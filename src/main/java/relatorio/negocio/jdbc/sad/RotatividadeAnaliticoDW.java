package relatorio.negocio.jdbc.sad;

import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.*;
import relatorio.negocio.comuns.sad.RotatividadeAnaliticoDWVO;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>RotatividadeAnaliticoDWVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>RotatividadeAnaliticoDWVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see RotatividadeAnaliticoDWVO
 * @see SuperEntidade
 */
public class RotatividadeAnaliticoDW extends SuperEntidade {

    

    public RotatividadeAnaliticoDW() throws Exception {
        super();
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>RotatividadeAnaliticoDWVO</code>.
     */
    public RotatividadeAnaliticoDWVO novo() throws Exception {
        // RotatividadeAnaliticoDW.incluir(getIdEntidade());
        RotatividadeAnaliticoDWVO obj = new RotatividadeAnaliticoDWVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>RotatividadeAnaliticoDWVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>RotatividadeAnaliticoDWVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(RotatividadeAnaliticoDWVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>RotatividadeAnaliticoDWVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>RotatividadeAnaliticoDWVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(RotatividadeAnaliticoDWVO obj) throws Exception {
        try {

            RotatividadeAnaliticoDWVO.validarDados(obj);
            // RotatividadeAnaliticoDW.incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO RotatividadeAnaliticoDW( dia, mes, ano, empresa, cliente, contrato, peso, situacao, foneCliente, dataAlteracaoRegistro ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDia()));
            sqlInserir.setInt(2, obj.getMes().intValue());
            sqlInserir.setInt(3, obj.getAno().intValue());
            if (obj.getEmpresa().getCodigo().intValue() != 0) {
                sqlInserir.setInt(4, obj.getEmpresa().getCodigo().intValue());
            } else {
                sqlInserir.setNull(4, 0);
            }
            if (obj.getCliente().getCodigo().intValue() != 0) {
                sqlInserir.setInt(5, obj.getCliente().getCodigo().intValue());
            } else {
                sqlInserir.setNull(5, 0);
            }
            if (obj.getContrato().getCodigo().intValue() != 0) {
                sqlInserir.setInt(6, obj.getContrato().getCodigo().intValue());
            } else {
                sqlInserir.setNull(6, 0);
            }
            sqlInserir.setInt(7, obj.getPeso().intValue());
            sqlInserir.setString(8, obj.getSituacao());
            sqlInserir.setString(9, obj.getFoneCliente());
            sqlInserir.setTimestamp(10, Uteis.getDataJDBCTimestamp(obj.getDataAlteracaoRegistro()));
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);

        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>RotatividadeAnaliticoDWVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>RotatividadeAnaliticoDWVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(RotatividadeAnaliticoDWVO obj) throws Exception {
        try {

            con.setAutoCommit(false);
            RotatividadeAnaliticoDWVO.validarDados(obj);
            // RotatividadeAnaliticoDW.alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE RotatividadeAnaliticoDW set dia=?, mes=?, ano=?, empresa=?, cliente=?, contrato=?, peso=?, situacao=?, foneCliente=?, dataalteracaoregistro=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDia()));
            sqlAlterar.setInt(2, obj.getMes().intValue());
            sqlAlterar.setInt(3, obj.getAno().intValue());
            if (obj.getEmpresa().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(4, obj.getEmpresa().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(4, 0);
            }
            if (obj.getCliente().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(5, obj.getCliente().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(5, 0);
            }
            if (obj.getContrato().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(6, obj.getContrato().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(6, 0);
            }
            sqlAlterar.setInt(7, obj.getPeso().intValue());
            sqlAlterar.setString(8, obj.getSituacao());
            sqlAlterar.setString(9, obj.getFoneCliente());
            sqlAlterar.setTimestamp(10, Uteis.getDataJDBCTimestamp(obj.getDataAlteracaoRegistro()));
            sqlAlterar.setInt(11, obj.getCodigo().intValue());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>RotatividadeAnaliticoDWVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>RotatividadeAnaliticoDWVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(RotatividadeAnaliticoDWVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>RotatividadeAnaliticoDWVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>RotatividadeAnaliticoDWVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluirSemCommit(RotatividadeAnaliticoDWVO obj) throws Exception {
        try {

            String sql = "DELETE FROM RotatividadeAnaliticoDW WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();

        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PeriodoAcessoClienteVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PeriodoAcessoClienteVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluirRotatividadeAnaliticoPorCodigoContrato(Integer codigoContrato) throws Exception {
        try {
            String sql = "DELETE FROM RotatividadeAnaliticoDW WHERE contrato = ?";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, codigoContrato.intValue());
            sqlExcluir.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Metodo que ira excluir dados da tabela rotatividadeAnalitco pelos parametros utilizados
     * @param mes que se quer excluir
     * @param ano que se quer exlcuir
     * @param empresa que se quer exlcuir
     * @param dia  so vai deletar os registro cujo o dia seja diferente do que esta sendo passado por paramentro
     * @throws Exception
     */
    public void excluirRotatividadeAnaliticoPorMesAnoDia(Integer mes, Integer ano, Integer empresa, Date dia) throws Exception {
        try {
            String sql = "DELETE FROM RotatividadeAnaliticoDW WHERE mes = " + mes + " and ano = " + ano + " and empresa =  " + empresa + " and "
                    + "Cast(dia as date) <> Cast('" + Uteis.getDataJDBC(dia) + "' as date)";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>RotatividadeAnaliticoDW</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>RotatividadeAnaliticoDWVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        //     SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM RotatividadeAnaliticoDW WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>RotatividadeAnaliticoDW</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>RotatividadeAnaliticoDWVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public RotatividadeAnaliticoDWVO consultarPorCodigoContratoEmpresa(Integer contrato, Integer empresa, Integer mes, Integer ano, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM RotatividadeAnaliticoDW WHERE contrato = " + contrato.intValue() + " and empresa = " + empresa.intValue()
                + " AND mes = " + mes + "AND ano = " + ano + "";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return new RotatividadeAnaliticoDWVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public ResultSet consultarRotatividadeAnaliticoPorMesAnoEmpresa(Integer mes,
            Integer ano,
            Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT peso, dia, contrato, situacao FROM rotatividadeAnaliticoDW WHERE mes = " + mes + " AND ano = " + ano + " AND empresa = " + codigoEmpresa;
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return tabelaResultado;
    }

    public Integer consultarRotatividadeAnaliticoFechamentoMes(Integer mes,
            Integer ano,
            Integer codigoEmpresa, Boolean virgenteMes, int nivelMontarDados) throws Exception {

        String sqlStr = "";
        if (virgenteMes) {
            sqlStr = "SELECT "
                    + "(SELECT COALESCE(SUM(peso), 0) as numeroPeso "
                    + "FROM RotatividadeAnaliticoDW "
                    + "WHERE mes = " + mes + "  AND ano = " + ano + " AND (situacao = 'MA' or situacao = 'RE' or situacao = 'RT') AND empresa = " + codigoEmpresa.intValue() + " "
                    + "AND cliente NOT IN (SELECT  ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " "
                    + "AND ra.ano = " + ano + "  AND ra.empresa = " + codigoEmpresa.intValue() + " and ra.dataAlteracaoRegistro >= RotatividadeAnaliticoDW.dataAlteracaoRegistro "
                    + "AND (ra.situacao = 'VE' or ra.situacao = 'CA' or ra.situacao = 'TR' or ra.situacao = 'DE'  or ra.situacao = 'RN'))) "
                    + " + "
                    + "(SELECT COALESCE(SUM(peso), 0) as numeroPeso "
                    + "FROM RotatividadeAnaliticoDW, contrato "
                    + "WHERE mes = " + mes + " AND ano = " + ano + " AND (RotatividadeAnaliticoDW.situacao = 'RN') AND RotatividadeAnaliticoDW.empresa = " + codigoEmpresa.intValue() + " "
                    + "AND cliente NOT IN (SELECT  ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " "
                    + "AND ra.ano = " + ano + " AND ra.empresa = " + codigoEmpresa.intValue() + " and ra.dataAlteracaoRegistro >= RotatividadeAnaliticoDW.dataAlteracaoRegistro "
                    + "AND (ra.situacao = 'VE' or ra.situacao = 'CA' or ra.situacao = 'TR' or ra.situacao = 'DE'))"
                    + "AND RotatividadeAnaliticoDW.contrato = contrato.codigo AND contrato.contratoresponsavelrenovacaomatricula = 0 ) as numeroPeso";

        } else {
            sqlStr = "SELECT COALESCE(SUM(peso), 0) as numeroPeso  "
                    + "FROM RotatividadeAnaliticoDW  rot "
                    + "WHERE rot.mes = " + mes + " AND rot.ano = " + ano + " "
                    + "AND rot.situacao = 'VE'  AND rot.empresa = " + codigoEmpresa.intValue() + " "
                    + "AND rot.cliente NOT IN (SELECT  sc.cliente FROM rotatividadeAnaliticoDW sc WHERE sc.mes = " + mes + " and sc.dataAlteracaoRegistro >= rot.dataAlteracaoRegistro "
                    + "AND sc.ano = " + ano + " AND sc.empresa = " + codigoEmpresa.intValue() + " and ( sc.situacao = 'CA' or sc.situacao = 'TR' or sc.situacao = 'DE' or  sc.situacao = 'RN' or  sc.situacao = 'RE' )) ";
        }
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return (new Integer(tabelaResultado.getInt(1)));

    }

    public Integer consultarRotatividadeAnaliticoFechamentoMesRetro(Integer mes,
            Integer ano,
            Integer codigoEmpresa, Boolean virgenteMes, int nivelMontarDados) throws Exception {

        String sqlStr = "";
        if (virgenteMes) {
            sqlStr = "SELECT "
                    + "(SELECT COALESCE(SUM(peso), 0) as numeroPeso "
                    + "FROM RotatividadeAnaliticoDW "
                    + "WHERE mes = " + mes + "  AND ano = " + ano + " AND (situacao = 'MA' or situacao = 'RE' or situacao = 'RT') AND empresa = " + codigoEmpresa.intValue() + " "
                    + "AND cliente NOT IN (SELECT  ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " "
                    + "AND ra.ano = " + ano + "  AND ra.empresa = " + codigoEmpresa.intValue() + " and ra.dia > RotatividadeAnaliticoDW.dia "
                    + "AND (ra.situacao = 'VE' or ra.situacao = 'CA' or ra.situacao = 'TR' or ra.situacao = 'DE'  or ra.situacao = 'RN'))) "
                    + " + "
                    + "(SELECT COALESCE(SUM(peso), 0) as numeroPeso "
                    + "FROM RotatividadeAnaliticoDW, contrato "
                    + "WHERE mes = " + mes + " AND ano = " + ano + " AND (RotatividadeAnaliticoDW.situacao = 'RN') AND RotatividadeAnaliticoDW.empresa = " + codigoEmpresa.intValue() + " "
                    + "AND cliente NOT IN (SELECT  ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " "
                    + "AND ra.ano = " + ano + " AND ra.empresa = " + codigoEmpresa.intValue() + " and ra.dia > RotatividadeAnaliticoDW.dia "
                    + "AND (ra.situacao = 'VE' or ra.situacao = 'CA' or ra.situacao = 'TR' or ra.situacao = 'DE'))"
                    + "AND RotatividadeAnaliticoDW.contrato = contrato.codigo) as numeroPeso";

        } else {
            sqlStr = "SELECT COALESCE(SUM(peso), 0) as numeroPeso  "
                    + "FROM RotatividadeAnaliticoDW  rot "
                    + "WHERE rot.mes = " + mes + " AND rot.ano = " + ano + " "
                    + "AND rot.situacao = 'VE'  AND rot.empresa = " + codigoEmpresa.intValue() + " "
                    + "AND rot.cliente NOT IN (SELECT  sc.cliente FROM rotatividadeAnaliticoDW sc WHERE sc.mes = " + mes + " and sc.dia >= rot.dia "
                    + "AND sc.ano = " + ano + " AND sc.empresa = " + codigoEmpresa.intValue() + " and ( sc.situacao = 'CA' or sc.situacao = 'TR' or sc.situacao = 'DE' or  sc.situacao = 'RN' or  sc.situacao = 'RE' )) ";
        }
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return (new Integer(tabelaResultado.getInt(1)));

    }

    public List obterTodosClienteVirgentesPorEmpresa(Date dataInicio, Date dataFim,
            Integer mes, Integer ano, Integer codigoEmpresa,
            int nivelMontarDados) throws Exception {

        String sqlStr = "(SELECT  RotatividadeAnaliticoDW.* "
                + "FROM RotatividadeAnaliticoDW "
                + "WHERE mes = " + mes + " "
                + "AND ano = " + ano + " "
                + "AND (situacao in ('MA','RE','RT')) "
                + "AND empresa = " + codigoEmpresa.intValue() + " "
                + "AND cliente NOT IN (SELECT ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " "
                + "AND ra.ano = " + ano + " "
                + "AND ra.empresa = " + codigoEmpresa.intValue() + " AND ra.dia >= RotatividadeAnaliticoDW.dia "
                + "AND (ra.situacao in ('VE','CA','TR','DE','RN'))) order by cliente) "
                + "UNION ALL "
                + "(SELECT   RotatividadeAnaliticoDW.* "
                + "FROM RotatividadeAnaliticoDW, contrato "
                + "WHERE mes = " + mes + " "
                + "AND ano = " + ano + " "
                + "AND (RotatividadeAnaliticoDW.situacao = 'RN') "
                + "AND RotatividadeAnaliticoDW.empresa = " + codigoEmpresa.intValue() + " "
                + "AND cliente NOT IN (SELECT ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " "
                + "AND ra.ano = " + ano + " "
                + "AND ra.empresa = " + codigoEmpresa.intValue() + " AND ra.dia >= RotatividadeAnaliticoDW.dia "
                + "AND (ra.situacao in ('VE','CA','TR','DE')) "
                + "AND RotatividadeAnaliticoDW.contrato = contrato.codigo order by cliente ) "
                + "AND RotatividadeAnaliticoDW.contrato = contrato.codigo /*AND contrato.contratoresponsavelrenovacaomatricula = 0*/  order by cliente ) "+
                "UNION ALL "
                + "(SELECT  rot.*  "
                + "FROM RotatividadeAnaliticoDW  rot "
                + "WHERE rot.mes = " + mes + " "
                + "AND rot.ano = " + ano + " "
                + "AND (rot.situacao = 'VE') "
                + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
                + "AND rot.cliente NOT IN (SELECT sc.cliente FROM rotatividadeAnaliticoDW sc WHERE "
                + "sc.mes = " + mes + " AND sc.ano = " + ano + " "
                + "AND sc.empresa = " + codigoEmpresa.intValue() + " AND sc.dia >= Rot.dia "
                + "AND ( sc.situacao in ('CA','TR','DE','RN','RE'))) order by cliente)";
        

        /*String sqlStr ="(SELECT  RotatividadeAnaliticoDW.* "+
                            "FROM RotatividadeAnaliticoDW "+
                            "WHERE mes = " + mes + " " +
                            "AND ano = " + ano + " " +
                            "AND cast(RotatividadeAnaliticoDW.dia as timestamp) <= '" + Uteis.getDataFormatoBD(dataFim) + " 23:59:59' " +
                            "AND (situacao = 'MA' or situacao = 'RE' or situacao = 'RT') "+
                            "AND empresa = " + codigoEmpresa.intValue() + " " +
                            "AND cliente NOT IN (SELECT ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " " +
                                                    "AND ra.ano = " + ano + " " +
                                                    "AND cast(ra.dia as timestamp) <= '" + Uteis.getDataFormatoBD(dataFim) + " 23:59:59' " +
                                                    "AND ra.empresa = " + codigoEmpresa.intValue() + " AND ra.dia > RotatividadeAnaliticoDW.dia "+
                                                    "and (ra.situacao = 'VE' or ra.situacao = 'CA' or ra.situacao = 'TR' or ra.situacao = 'DE'  or ra.situacao = 'RN')) "
                            + "order by cliente) "+
			"UNION ALL " +
                            "(SELECT   RotatividadeAnaliticoDW.* "+
                            "FROM RotatividadeAnaliticoDW, contrato "+
                            "WHERE mes = " + mes + " " +
                            "AND ano = " + ano + " " +
                            "AND cast(RotatividadeAnaliticoDW.dia as timestamp) <= '" + Uteis.getDataFormatoBD(dataFim) + " 23:59:59' " +
                            "AND (RotatividadeAnaliticoDW.situacao = 'RN') "+
                            "AND RotatividadeAnaliticoDW.empresa = " + codigoEmpresa.intValue() + " "+
                            "AND cliente NOT IN (SELECT ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " " +
                                                    "AND ra.ano = " + ano + " " +
                                                    "AND cast(ra.dia as timestamp) <= '" + Uteis.getDataFormatoBD(dataFim) + " 23:59:59' " +
                                                    "AND ra.empresa = " + codigoEmpresa.intValue() + " AND ra.dia > RotatividadeAnaliticoDW.dia "+
                                                    "AND (ra.situacao = 'VE' or ra.situacao = 'CA' or ra.situacao = 'TR' or ra.situacao = 'DE')) " +
                            "AND RotatividadeAnaliticoDW.contrato = contrato.codigo " +
                            //"AND contrato.contratoresponsavelrenovacaomatricula = 0  " +
                            "order by cliente ) "+
			"UNION ALL "+
                            "(SELECT  rot.*  "+
                            "FROM RotatividadeAnaliticoDW  rot "+
                            "WHERE rot.mes = " + mes + " " +
                            "AND rot.ano = " + ano + " " +
                            "AND cast(rot.dia as timestamp) <= '" + Uteis.getDataFormatoBD(dataFim) + " 23:59:59' " +
                            "AND (rot.situacao = 'VE') "+
                            "AND rot.empresa = " + codigoEmpresa.intValue() + " "+
                            "AND rot.cliente NOT IN (SELECT sc.cliente FROM rotatividadeAnaliticoDW sc WHERE "+
                                                        "sc.mes = " + mes + " AND sc.ano = " + ano + " " +
                                                        "AND cast(sc.dia as timestamp) <= '" + Uteis.getDataFormatoBD(dataFim) + " 23:59:59' " +
                                                        "AND sc.empresa = " + codigoEmpresa.intValue() + " AND sc.dia >= Rot.dia "+
                                                        "AND ( sc.situacao = 'CA' or sc.situacao = 'TR' or sc.situacao = 'DE' or  sc.situacao = 'RN' or  sc.situacao = 'RE')) "
                            + "order by cliente)";*/


        /*String sqlStr = "SELECT * "
                + "FROM RotatividadeAnaliticoDW rot "
                + "WHERE mes = " + mes + " AND ano = " + ano + " "
                + "AND cast(rot.dia as timestamp) <= '" + Uteis.getDataFormatoBD(dataFim) + " 23:59:59' "
                + "AND situacao in('MA','RE','RT') AND empresa = "
                + codigoEmpresa.intValue() + " "
                + "AND cliente NOT IN (SELECT cliente from RotatividadeAnaliticoDW "
                                        + "WHERE dia > rot.dia "
                                        + "AND cast(RotatividadeAnaliticoDW.dia as timestamp) <= '" + Uteis.getDataFormatoBD(dataFim) + " 23:59:59' "
                                        + "AND mes = " + mes + " "
                                        + "AND ano = " + ano + " "
                                        + "AND situacao in ('VE','DE','CA','TR','RN')) "
                
                + "UNION ALL ("
                + "SELECT * FROM RotatividadeAnaliticoDW  rot "
                + "WHERE rot.mes = " + mes + " AND rot.ano = " + ano + " "
                + "AND cast(rot.dia as timestamp) <= '" + Uteis.getDataFormatoBD(dataFim) + " 23:59:59' "
                + "AND rot.situacao = 'VE'  AND rot.empresa = " + codigoEmpresa.intValue() + " "
                + "AND cliente NOT IN (SELECT cliente FROM RotatividadeAnaliticoDW "
                                        + "WHERE dia >= RotatividadeAnaliticoDW.dia and mes = " + mes + " AND ano = " + ano + " "
                                        + "AND cast(RotatividadeAnaliticoDW.dia as timestamp) <= '" + Uteis.getDataFormatoBD(dataFim) + " 23:59:59' "
                                        + "AND situacao in ('RE','RT','RN', 'DE')))";

        
        */

        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List obterTodosClientesAtivosPorEmpresa(Date diaAte,
            Integer mes, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {

        /*String sqlStr = "SELECT  rot.*  "
        + "FROM RotatividadeAnaliticoDW  rot "
        + "WHERE rot.mes = " + mes + " "
        + "AND rot.ano = " + ano + " "
        + "AND cast (rot.dia as timestamp) <= '" + Uteis.getDataFormatoBD(diaAte) + " 23:59:59' "
        + "AND (rot.situacao = 'MA' or situacao = 'RE' or situacao = 'RN' or situacao = 'RT') "
        + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
        + "AND rot.cliente NOT IN (SELECT sc.cliente FROM rotatividadeAnaliticoDW sc WHERE "
        + "sc.mes = " + mes + " AND sc.ano = " + ano + " "
        + "AND sc.empresa = " + codigoEmpresa.intValue() + " AND (sc.dia > rot.dia) "
        + "AND cast (sc.dia as timestamp) <= '" + Uteis.getDataFormatoBD(diaAte) + " 23:59:59' "
        + "AND (sc.situacao = 'CA' OR sc.situacao = 'TR' OR sc.situacao = 'DE' OR  sc.situacao = 'VE' OR sc.situacao = 'RN')) "
        + "ORDER BY cliente";*/

        String sqlStr = "SELECT * "
                                + "FROM RotatividadeAnaliticoDW rot "
                                + "WHERE mes = " + mes + " AND ano = " + ano + " "
                                + "AND situacao in('MA','RE','RT','RN') AND empresa = "
                                + codigoEmpresa.intValue() + " "
                                + "AND cliente NOT IN (SELECT cliente from RotatividadeAnaliticoDW "
                                                        + "WHERE dia > rot.dia "
                                                        + "AND mes = " + mes + " "
                                                        + "AND ano = " + ano + " "
                                                        + "AND situacao in ('VE','DE','CA','TR')) "
                + "UNION ALL ("
                            + "SELECT * FROM RotatividadeAnaliticoDW  rot "
                            + "WHERE rot.mes = " + mes + " AND rot.ano = " + ano + " "
                            + "AND rot.situacao = 'VE'  AND rot.empresa = " + codigoEmpresa.intValue() + " "
                            + "AND cliente NOT IN (SELECT cliente FROM RotatividadeAnaliticoDW "
                            + "WHERE dia >= rot.dia and mes = " + mes + " AND ano = " + ano + " "
                            + "AND situacao in ('RE','RT','RN', 'DE'))) "
                + "EXCEPT (SELECT  rot.*  "
                            + "FROM RotatividadeAnaliticoDW  rot "
                            + "WHERE rot.mes = " + mes + " "
                            + "AND rot.ano = " + ano + " "
                            + "AND cast (rot.dia as timestamp) <= '" + Uteis.getDataFormatoBD(diaAte) + " 23:59:59' "
                            + "AND (rot.situacao = 'VE') "
                            + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
                            + "AND rot.cliente NOT IN (SELECT sc.cliente FROM rotatividadeAnaliticoDW sc WHERE "
                                                        + "sc.mes = " + mes + " AND sc.ano = " + ano + " "
                                                        + "AND sc.empresa = " + codigoEmpresa.intValue() + " AND sc.dia >= Rot.dia "
                                                        + "AND sc.situacao in ('CA','TR','DE','RN','RE')) "
                            + "order by cliente) ";
        

        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List obterTodosClienteVirgentesMesAnteriorPorEmpresa(Integer mes, Integer ano, Integer mesAtual, Integer anoAtual, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "(SELECT  RotatividadeAnaliticoDW.* "
        + "FROM RotatividadeAnaliticoDW "
        + "WHERE mes = " + mes + " "
        + "AND ano = " + ano + " "
        + "AND (situacao = 'MA' or situacao = 'RE' or situacao = 'RT') "
        + "AND empresa = " + codigoEmpresa.intValue() + " "
        + "AND cliente NOT IN (SELECT ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " "
        + "AND ra.ano = " + ano + " "
        + "AND ra.empresa = " + codigoEmpresa.intValue() + " AND ra.dia > RotatividadeAnaliticoDW.dia "
        + "and (ra.situacao = 'VE' or ra.situacao = 'CA' or ra.situacao = 'TR' or ra.situacao = 'DE'  or ra.situacao = 'RN')) order by cliente) "
        + "UNION ALL "
        + "(SELECT   RotatividadeAnaliticoDW.* "
        + "FROM RotatividadeAnaliticoDW, contrato "
        + "WHERE mes = " + mes + " "
        + "AND ano = " + ano + " "
        + "AND (RotatividadeAnaliticoDW.situacao = 'RN') "
        + "AND RotatividadeAnaliticoDW.empresa = " + codigoEmpresa.intValue() + " "
        + "AND cliente NOT IN (SELECT ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " "
        + "AND ra.ano = " + ano + " "
        + "AND ra.empresa = " + codigoEmpresa.intValue() + " AND ra.dia > RotatividadeAnaliticoDW.dia "
        + "AND (ra.situacao = 'VE' or ra.situacao = 'CA' or ra.situacao = 'TR' or ra.situacao = 'DE')) "
        + "AND RotatividadeAnaliticoDW.contrato = contrato.codigo /*AND contrato.contratoresponsavelrenovacaomatricula = 0*/  order by cliente ) "
        + "UNION ALL "
        + "(SELECT   RotatividadeAnaliticoDW.* "
        + "FROM RotatividadeAnaliticoDW "
        + "inner join contrato on RotatividadeAnaliticoDW.contrato = contrato.codigo and contrato.contratoresponsavelrenovacaomatricula <> 0		"
        + "and contrato.codigo in (SELECT ra.contrato from RotatividadeAnaliticoDW as ra where ra.mes  = " + mesAtual + " and ra.ano =" + anoAtual + " and ra.empresa = " + codigoEmpresa.intValue() + " ) "
        + "WHERE mes =  " + mes + " "
        + "AND ano =   " + ano + " "
        + "AND (RotatividadeAnaliticoDW.situacao = 'RN') "
        + "AND RotatividadeAnaliticoDW.empresa =  " + codigoEmpresa.intValue()
        + "AND cliente NOT IN (SELECT ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes =   " + mes + " "
        + "AND ra.ano =   " + ano + " "
        + "AND ra.empresa =   " + codigoEmpresa.intValue() + "   AND ra.dia > RotatividadeAnaliticoDW.dia "
        + "AND (ra.situacao = 'VE' or ra.situacao = 'CA' or ra.situacao = 'TR' or ra.situacao = 'DE')) "
        + "order by cliente ) "
        + "UNION ALL "
        + "(SELECT  rot.*  "
        + "FROM RotatividadeAnaliticoDW  rot "
        + "WHERE rot.mes = " + mes + " "
        + "AND rot.ano = " + ano + " "
        + "AND (rot.situacao = 'VE') "
        + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
        + "AND rot.cliente NOT IN (SELECT sc.cliente FROM rotatividadeAnaliticoDW sc WHERE "
        + "sc.mes = " + mes + " AND sc.ano = " + ano + " "
        + "AND sc.empresa = " + codigoEmpresa.intValue() + " AND sc.dia > Rot.dia "
        + "AND ( sc.situacao = 'CA' or sc.situacao = 'TR' or sc.situacao = 'DE' or  sc.situacao = 'RN' or  sc.situacao = 'RE')) order by cliente)";
         

        /*String sqlStr = "SELECT * "
                + "FROM RotatividadeAnaliticoDW rot "
                + "WHERE mes = " + mes + " AND ano = " + ano + " "
                + "AND situacao in('MA','RE','RT','RN') AND empresa = "
                + codigoEmpresa.intValue() + " "
                + "AND cliente NOT IN (SELECT cliente from RotatividadeAnaliticoDW "
                + "WHERE dia > rot.dia "
                + "AND mes = " + mes + " "
                + "AND ano = " + ano + " "
                + "AND situacao in ('VE','DE','CA','TR')) "
                + "UNION ALL ("
                + "SELECT * FROM RotatividadeAnaliticoDW  rot "
                + "WHERE rot.mes = " + mes + " AND rot.ano = " + ano + " "
                + "AND rot.situacao = 'VE'  AND rot.empresa = " + codigoEmpresa.intValue() + " "
                + "AND cliente NOT IN (SELECT cliente FROM RotatividadeAnaliticoDW "
                + "WHERE dia >= rot.dia and mes = " + mes + " AND ano = " + ano + " "
                + "AND situacao in ('RE','RT','RN', 'DE')))";*/

        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarPorCodigoEmpresaPorMesPorAno(Integer valorConsulta, Integer mes, Integer ano, int nivelMontarDados) throws Exception {
        //     SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT  RotatividadeAnaliticoDW.* FROM RotatividadeAnaliticoDW  WHERE mes = " + mes + " AND ano = " + ano + " AND empresa = " + valorConsulta
                + " and dataalteracaoregistro =  "
                + "(select MAX(ra.dataalteracaoregistro) from RotatividadeAnaliticoDW  as ra  "
                + "WHERE ra.mes = " + mes + " AND ra.ano = " + ano + "    AND ra.empresa = " + valorConsulta + " and ra.cliente = RotatividadeAnaliticoDW.cliente)";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>RotatividadeAnaliticoDW</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>RotatividadeAnaliticoDWVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public String consultarPorSituacaoCodigoContratoEmpresa(Integer cliente, Integer codigoEmpresa) throws Exception {
        String sqlStr = "SELECT  RotatividadeAnaliticoDW.* FROM RotatividadeAnaliticoDW  WHERE cliente =" + cliente + " AND empresa = " + codigoEmpresa
                + " and dataalteracaoregistro =  (select MAX(ra.dataalteracaoregistro) from RotatividadeAnaliticoDW  as ra WHERE ra.cliente = RotatividadeAnaliticoDW.cliente)";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return "";
        }
        return tabelaResultado.getString("situacao");
    }

    public boolean existeContratoNestaSituacaoEmpresa(Integer contrato, String situacao,
            Integer mes, Integer ano, Integer codigoEmpresa) throws Exception {
        String sqlStr = "select exists (select situacao from rotatividadeanaliticodw  "
                + "where contrato = ? and situacao = ? "
                + "and mes = ? and ano = ? "
                + "and empresa = ? ) as existe";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        stm.setInt(1, contrato);
        stm.setString(2, situacao);
        stm.setInt(3, mes);
        stm.setInt(4, ano);
        stm.setInt(5, codigoEmpresa);
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return tabelaResultado.getBoolean("existe");
    }

    public RotatividadeAnaliticoDWVO consultarPorUltimoRegistroPorCodigoContrato(Integer contrato, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM RotatividadeAnaliticoDW where contrato = " + contrato + " and dia  = (SELECT MAX(dia) FROM RotatividadeAnaliticoDW where contrato = " + contrato + ")";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return new RotatividadeAnaliticoDWVO();
        }
        return montarDados(tabelaResultado, nivelMontarDados);
    }

    public List consultarRotatividadePorSituacaoMesAnoEmpresa(Date dataInicio, Date dataFim, String situacao,
            Integer mes, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String condicaoConsiderarSituacoesPosteriores = "";
        if (situacao.equals("TR")){
            condicaoConsiderarSituacoesPosteriores = " AND cliente not in (SELECT rot.cliente  "
                                    + "FROM RotatividadeAnaliticoDW rot "
                                    + "WHERE rot.mes = " + mes + " "
                                    + "AND rot.ano = " + ano + " "
                                    + "AND rot.situacao = 'CA' "
                                    + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
                                    + "AND rot.dia > RotatividadeAnaliticoDW.dia) ";

        }

        String sqlStr = "SELECT RotatividadeAnaliticoDW.*  "
                + "FROM RotatividadeAnaliticoDW "
                + "WHERE mes = " + mes + " "
                + "AND ano = " + ano + " "
                + condicaoConsiderarSituacoesPosteriores
                + "AND situacao = '" + situacao.toUpperCase() + "' "
                + "AND empresa = " + codigoEmpresa.intValue() + " "
                + "AND dia >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' "
                + "AND dia <= '" + Uteis.getDataJDBC(dataFim) + " 23:59:59' "
                + " order by cliente ";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarRotatividadePorSituacaoMesAnoEmpresaQueFoiInativadaDepois(
            Date dataInicio, Date dataFim, String situacao,
            Integer mes, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT RotatividadeAnaliticoDW.*  "
                + "FROM RotatividadeAnaliticoDW "
                + "WHERE mes = " + mes + " "
                + "AND ano = " + ano + " "
                + "AND situacao in ('MA','RN','RE','VE', 'RT')"
                + "AND empresa = " + codigoEmpresa.intValue() + " "
                /*+ "AND dia >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' "
                + "AND dia <= '" + Uteis.getDataJDBC(dataFim) + " 23:59:59' "*/
                + "AND cliente not in (SELECT rot.cliente  "
                                    + "FROM RotatividadeAnaliticoDW rot "
                                    + "WHERE rot.mes = " + mes + " "
                                    + "AND rot.ano = " + ano + " "
                                    + "AND rot.situacao in ('CA', 'TR', 'DE', 'VE') "
                                    + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
                                    + "AND rot.dia >= RotatividadeAnaliticoDW.dia "
                                    /*+ "AND rot.dia >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' "
                                    + "AND rot.dia <= '" + Uteis.getDataJDBC(dataFim) + " 23:59:59' "*/
                                    + " order by rot.contrato)"
                + " order by contrato ";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarRotatividadeSaldoMes(Date dataInicio, Date dataFim,
            Integer mes, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {

        String sqlStr = "SELECT RotatividadeAnaliticoDW.*  "
                + "FROM RotatividadeAnaliticoDW "
                + "WHERE mes = " + mes + " "
                + "AND ano = " + ano + " "
                + "AND situacao in ('MA', 'RE', 'RT','CA', 'TR', 'DE') "
                + "AND contrato not in (SELECT rot.contrato  "
                + "FROM RotatividadeAnaliticoDW rot "
                + "WHERE rot.mes = " + mes + " "
                + "AND rot.ano = " + ano + " "
                + "AND rot.situacao in ('CA', 'TR', 'DE') "
                + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
                + "AND rot.dia >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' "
                + "AND rot.dia <= '" + Uteis.getDataJDBC(dataFim) + " 23:59:59' "
                + " order by rot.cliente) "
                + "AND empresa = " + codigoEmpresa.intValue() + " "
                + "AND dia >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' "
                + "AND dia <= '" + Uteis.getDataJDBC(dataFim) + " 23:59:59' "
                + " order by cliente";
        //matriculados, rematriculados, cancelados e retorno trancamento
        /*String sqlStr = "(SELECT RotatividadeAnaliticoDW.*  "
        + "FROM RotatividadeAnaliticoDW "
        + "WHERE mes = " + mes + " "
        + "AND ano = " + ano + " "
        + "AND situacao in ('MA', 'RE', 'RT','CA', 'TR', 'DE') "
        + "AND empresa = " + codigoEmpresa.intValue() + " "
        + "AND dia >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' "
        + "AND dia <= '" + Uteis.getDataJDBC(dataFim) + " 23:59:59' "
        + " order by cliente) "

        + "EXCEPT (SELECT rot.*  "
        + "FROM RotatividadeAnaliticoDW rot "
        + "WHERE rot.mes = " + mes + " "
        + "AND rot.ano = " + ano + " "
        + "AND rot.situacao in ('CA', 'TR', 'DE') "
        + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
        + "AND rot.dia >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' "
        + "AND rot.dia <= '" + Uteis.getDataJDBC(dataFim) + " 23:59:59' "
        + " order by rot.cliente)";*/
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));

    }

    public List consultarSituacoesRotatividadeHistoricoContrato(String situacao, Date dia,
            Integer mes, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT RotatividadeAnaliticoDW.*  "
                + "FROM RotatividadeAnaliticoDW, HistoricoContrato "
                + "WHERE RotatividadeAnaliticoDW.mes = " + mes + " "
                + "AND RotatividadeAnaliticoDW.ano = " + ano + " "
                + "AND RotatividadeAnaliticoDW.contrato =  HistoricoContrato.contrato "
                + "AND RotatividadeAnaliticoDW.situacao = '" + situacao.toUpperCase() + "' "
                + "AND RotatividadeAnaliticoDW.empresa = " + codigoEmpresa.intValue() + " "
                + "AND CAST (HistoricoContrato.datainiciosituacao as DATE)  = CAST ('" + Uteis.getDataJDBC(dia) + "' as DATE)"
                + "AND HistoricoContrato.tipoHistorico  = '" + situacao.toUpperCase() + "' "
                + " order by cliente ";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public Integer consultarQuantidadeRotatividadeParaMensalMatricuala(Date dataInicio, Date dataFim, String situacao, Integer mes, Integer ano, Integer codigoEmpresa) throws Exception {
        String sqlStr = "SELECT SUM(peso) as numeroPeso FROM RotatividadeAnaliticoDW as rot "
                + "inner join contrato on contrato.codigo = rot.contrato and  vigenciaDe >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and vigenciaDe <= '" + Uteis.getDataJDBC(dataFim) + " 23:59:59' "
                + "WHERE mes = " + mes + " AND ano = " + ano + " AND (rot.situacao = '" + situacao + "') AND rot.empresa = " + codigoEmpresa + "  ";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            new Integer(0);
        }
        return (new Integer(tabelaResultado.getInt(1)));
    }

    public Integer consultarQuantidadeRotatividadeParaMatriculaHoje(Date dataInicio, Integer mes, String situacao, Integer ano, Integer codigoEmpresa) throws Exception {
        String sqlStr = "SELECT SUM(peso) as numeroPeso FROM RotatividadeAnaliticoDW as rot "
                + "inner join contrato on contrato.codigo = rot.contrato and  CAST(vigenciaDe as DATE) = cast( '" + Uteis.getDataJDBC(dataInicio) + "' as DATE)  "
                + "WHERE mes = " + mes + " AND ano = " + ano + " AND (rot.situacao = '" + situacao + "') AND rot.empresa = " + codigoEmpresa + " ";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return 0;
        }
        return tabelaResultado.getInt(1);
    }

    public Integer consultarQuantidadeRotatividadePorSituacaoMesAnoEmpresa(Date dataInicio, Date dataFim, String situacao,
            Integer mes, Integer ano, Integer codigoEmpresa) throws Exception {
        String condicaoConsiderarSituacoesPosteriores = "";
        if (situacao.equals("TR")){
            condicaoConsiderarSituacoesPosteriores = " AND cliente not in (SELECT rot.cliente  "
                                    + "FROM RotatividadeAnaliticoDW rot "
                                    + "WHERE rot.mes = " + mes + " "
                                    + "AND rot.ano = " + ano + " "
                                    + "AND rot.situacao = 'CA' "
                                    + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
                                    + "AND rot.dia > RotatividadeAnaliticoDW.dia) ";

        }
        String sqlStr = "SELECT SUM(peso) as numeroPeso   "
                + "FROM RotatividadeAnaliticoDW "
                + "WHERE mes = " + mes + " "
                + "AND ano = " + ano + " "
                + condicaoConsiderarSituacoesPosteriores
                + "AND situacao = '" + situacao.toUpperCase() + "' "
                + "AND empresa = " + codigoEmpresa.intValue() + " "
                + "AND dia >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' "
                + "AND dia <= '" + Uteis.getDataJDBC(dataFim) + " 23:59:59' ";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return 0;
        }
        return tabelaResultado.getInt(1);
    }

    public Integer consultarQuantidadeRotatividadeHistoricoContrato(String situacao, Date dia,
            Integer mes, Integer ano, Integer codigoEmpresa) throws Exception {
        String condicaoConsiderarSituacoesPosteriores = "";
        if (situacao.equals("TR")){
            condicaoConsiderarSituacoesPosteriores = " AND cliente not in (SELECT rot.cliente  "
                                    + "FROM RotatividadeAnaliticoDW rot "
                                    + "WHERE rot.mes = " + mes + " "
                                    + "AND rot.ano = " + ano + " "
                                    + "AND rot.situacao = 'CA' "
                                    + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
                                    + "AND rot.dia > RotatividadeAnaliticoDW.dia) ";

        }
        String sqlStr = "SELECT SUM(peso) as numeroPeso  "
                + "FROM RotatividadeAnaliticoDW, HistoricoContrato "
                + "WHERE RotatividadeAnaliticoDW.mes = " + mes + " "
                + "AND RotatividadeAnaliticoDW.ano = " + ano + " "
                + condicaoConsiderarSituacoesPosteriores
                + "AND RotatividadeAnaliticoDW.contrato =  HistoricoContrato.contrato "
                + "AND RotatividadeAnaliticoDW.situacao = '" + situacao.toUpperCase() + "' "
                + "AND RotatividadeAnaliticoDW.empresa = " + codigoEmpresa.intValue() + " "
                + "AND CAST (HistoricoContrato.datainiciosituacao as DATE)  = CAST ('" + Uteis.getDataJDBC(dia) + "' as DATE)"
                + "AND HistoricoContrato.tipoHistorico  = '" + situacao.toUpperCase() + "' ";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return 0;
        }
        return tabelaResultado.getInt(1);
    }

    public Integer consultarQuantidadeRotatividadeTodosClienteVencidoMes(Date diaAte,
            Integer mes, Integer ano, Integer codigoEmpresa) throws Exception {
        String sqlStr = "(SELECT  SUM(peso) as numeroPeso  "
                + "FROM RotatividadeAnaliticoDW  rot "
                + "WHERE rot.mes = " + mes + " "
                + "AND rot.ano = " + ano + " "
                + "AND cast (rot.dia as timestamp) <= '" + Uteis.getDataFormatoBD(diaAte) + " 23:59:59' "
                + "AND (rot.situacao = 'VE') "
                + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
                + "AND rot.cliente NOT IN (SELECT sc.cliente FROM rotatividadeAnaliticoDW sc WHERE "
                + "sc.mes = " + mes + " AND sc.ano = " + ano + " "
                + "AND sc.empresa = " + codigoEmpresa.intValue() + " AND sc.dia >= Rot.dia "
                + "AND ( sc.situacao = 'CA' or sc.situacao = 'TR' or sc.situacao = 'DE' or  sc.situacao = 'RN' or  sc.situacao = 'RE')) )";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return 0;
        }
        return tabelaResultado.getInt(1);
    }

    public List consultarRotatividadeParaMensalMatricuala(Date dataInicio, Date dataFim, String situacao, Integer mes, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT rot.* FROM RotatividadeAnaliticoDW as rot "
                + "inner join contrato on contrato.codigo = rot.contrato and  vigenciaDe >= '" + Uteis.getDataJDBC(dataInicio) + " 00:00:00' and vigenciaDe <= '" + Uteis.getDataJDBC(dataFim) + " 23:59:59' "
                + "WHERE mes = " + mes + " AND ano = " + ano + " AND (rot.situacao = '" + situacao + "') AND rot.empresa = " + codigoEmpresa + " order by cliente ";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarRotatividadeParaMatriculaHoje(Date dataInicio, Integer mes, String situacao, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT rot.* FROM RotatividadeAnaliticoDW as rot "
                + "inner join contrato on contrato.codigo = rot.contrato and  CAST(vigenciaDe as DATE) = cast( '" + Uteis.getDataJDBC(dataInicio) + "' as DATE)  "
                + "WHERE mes = " + mes + " AND ano = " + ano + " AND (rot.situacao = '" + situacao + "') AND rot.empresa = " + codigoEmpresa + " order by cliente ";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarRotatividadeTodosClienteVencidoMes(Date diaAte, Integer mes,
            Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "(SELECT  rot.*  "
                + "FROM RotatividadeAnaliticoDW  rot "
                + "WHERE rot.mes = " + mes + " "
                + "AND rot.ano = " + ano + " "
                + "AND cast (rot.dia as timestamp) <= '" + Uteis.getDataFormatoBD(diaAte) + " 23:59:59' "
                + "AND (rot.situacao = 'VE') "
                + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
                + "AND rot.cliente NOT IN (SELECT sc.cliente FROM rotatividadeAnaliticoDW sc WHERE "
                + "sc.mes = " + mes + " AND sc.ano = " + ano + " "
                + "AND sc.empresa = " + codigoEmpresa.intValue() + " AND sc.dia >= Rot.dia "
                + "AND sc.situacao in ('CA','TR','DE','RN','RE')) "
                + "order by cliente)";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * consulta todos os clientes que permaneceram ou se tornaram ativos durante o mês
     * @param mes (mes de referencia a consultar)
     * @param ano (ano de referencia a consultar)
     * @param codigoEmpresa (empresa a consultar)
     * @param nivelMontarDados
     * @return Lista de rotatividade consultada
     * @throws Exception
     */
    public List consultarRotatividadeTodosClienteAtivoMes(Date diaAte,
            Integer mes, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT  rot.*  "
                + "FROM RotatividadeAnaliticoDW  rot "
                + "WHERE rot.mes = " + mes + " "
                + "AND rot.ano = " + ano + " "
                + "AND cast (rot.dia as timestamp) <= '" + Uteis.getDataFormatoBD(diaAte) + " 23:59:59' "
                + "AND (rot.situacao = 'MA' or situacao = 'RE' or situacao = 'RN' or situacao = 'RT') "
                + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
                + "AND rot.cliente NOT IN (SELECT sc.cliente FROM rotatividadeAnaliticoDW sc WHERE "
                + "sc.mes = " + mes + " AND sc.ano = " + ano + " "
                + "AND sc.empresa = " + codigoEmpresa.intValue() + " AND (sc.dia > rot.dia) "
                + "AND cast (sc.dia as timestamp) <= '" + Uteis.getDataFormatoBD(diaAte) + " 23:59:59' "
                + "AND (sc.situacao = 'CA' OR sc.situacao = 'TR' OR sc.situacao = 'DE' OR  sc.situacao = 'VE' OR sc.situacao = 'RN')) "
                + "ORDER BY cliente";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List obterRotatividadeClienteMatriculadoParaProximoMes(Integer mes, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "(SELECT  RotatividadeAnaliticoDW.* "
                + "FROM RotatividadeAnaliticoDW "
                + "WHERE mes = " + mes + " "
                + "AND ano = " + ano + " "
                + "AND (situacao = 'MA' or situacao = 'RE' or situacao = 'RT') "
                + "AND empresa = " + codigoEmpresa.intValue() + " "
                + "AND cliente NOT IN (SELECT ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " "
                                        + "AND ra.ano = " + ano + " "                                        
                                        + "AND ra.empresa = " + codigoEmpresa.intValue() + " AND ra.dia > RotatividadeAnaliticoDW.dia "
                                        + "and (ra.situacao = 'VE' or ra.situacao = 'CA' or ra.situacao = 'TR' or ra.situacao = 'DE'  or ra.situacao = 'RN'))) "
                + "UNION ALL "
                + "(SELECT  RotatividadeAnaliticoDW.* "
                + "FROM RotatividadeAnaliticoDW, contrato "
                + "WHERE mes = " + mes + " "
                + "AND ano = " + ano + " "
                + "AND (RotatividadeAnaliticoDW.situacao = 'RN') "
                + "AND RotatividadeAnaliticoDW.empresa = " + codigoEmpresa.intValue() + " "
                + "AND cliente NOT IN (SELECT ra.cliente FROM rotatividadeAnaliticoDW as ra WHERE ra.mes = " + mes + " "
                                        + "AND ra.ano = " + ano + " "                                        
                                        + "AND ra.empresa = " + codigoEmpresa.intValue() + " AND ra.dia > RotatividadeAnaliticoDW.dia "
                                        + "AND (ra.situacao = 'VE' or ra.situacao = 'CA' or ra.situacao = 'TR' or ra.situacao = 'DE')) "
                + "AND RotatividadeAnaliticoDW.contrato = contrato.codigo AND contrato.contratoresponsavelrenovacaomatricula = 0) "
                + "UNION ALL "
                + "(SELECT  rot.*  "
                + "FROM RotatividadeAnaliticoDW  rot "
                + "WHERE rot.mes = " + mes + " "
                + "AND rot.ano = " + ano + " "
                + "AND (rot.situacao = 'VE') "
                + "AND rot.empresa = " + codigoEmpresa.intValue() + " "
                + "AND rot.cliente NOT IN (SELECT sc.cliente FROM rotatividadeAnaliticoDW sc WHERE "
                                            + "sc.mes = " + mes + " AND sc.ano = " + ano + " "                                            
                                            + "AND sc.empresa = " + codigoEmpresa.intValue() + " AND sc.dia > Rot.dia "
                                            + "AND ( sc.situacao = 'CA' or sc.situacao = 'TR' or sc.situacao = 'DE' or  sc.situacao = 'RN' or  sc.situacao = 'RE')))";

        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>RotatividadeAnaliticoDWVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            RotatividadeAnaliticoDWVO obj = new RotatividadeAnaliticoDWVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>RotatividadeAnaliticoDWVO</code>.
     * @return  O objeto da classe <code>RotatividadeAnaliticoDWVO</code> com os dados devidamente montados.
     */
    public static RotatividadeAnaliticoDWVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        RotatividadeAnaliticoDWVO obj = new RotatividadeAnaliticoDWVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDia(dadosSQL.getTimestamp("dia"));
        obj.setDia(dadosSQL.getTimestamp("dataalteracaoregistro"));
        obj.setMes(new Integer(dadosSQL.getInt("mes")));
        obj.setAno(new Integer(dadosSQL.getInt("ano")));
        obj.getEmpresa().setCodigo(new Integer(dadosSQL.getInt("empresa")));
        obj.getCliente().setCodigo(new Integer(dadosSQL.getInt("cliente")));
        obj.getContrato().setCodigo(new Integer(dadosSQL.getInt("contrato")));
        obj.setPeso(new Integer(dadosSQL.getInt("peso")));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setFoneCliente(dadosSQL.getString("foneCliente"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_MINIMOS);
        montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        return obj;
    }

    public static void montarDadosCliente(RotatividadeAnaliticoDWVO obj, int nivelMontarDados) throws Exception {
        if (obj.getCliente().getCodigo().intValue() == 0) {
            obj.setCliente(new ClienteVO());
            return;
        }
        obj.setCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo().intValue(), nivelMontarDados));
    }

    public static void montarDadosContrato(RotatividadeAnaliticoDWVO obj, int nivelMontarDados) throws Exception {
        if (obj.getContrato().getCodigo().intValue() == 0) {
            obj.setContrato(new ContratoVO());
            return;
        }
        obj.setContrato(getFacade().getContrato().consultarPorChavePrimaria(obj.getContrato().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>RotatividadeAnaliticoDWVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public RotatividadeAnaliticoDWVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM RotatividadeAnaliticoDW WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( RotatividadeAnaliticoDW ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
}

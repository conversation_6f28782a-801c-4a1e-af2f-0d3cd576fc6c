package relatorio.negocio.jdbc.sad;

import negocio.facade.jdbc.arquitetura.*;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import negocio.comuns.utilitarias.*;
import relatorio.negocio.comuns.sad.RotatividadeSinteticoDWVO;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>RotatividadeSinteticoDWVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>RotatividadeSinteticoDWVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see RotatividadeSinteticoDWVO
 * @see SuperEntidade
 */
public class RotatividadeSinteticoDW extends SuperEntidade {    

    public RotatividadeSinteticoDW() throws Exception {
        super();        
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>RotatividadeSinteticoDWVO</code>.
     */
    public RotatividadeSinteticoDWVO novo() throws Exception {
        // RotatividadeSinteticoDW.incluir(getIdEntidade());
        RotatividadeSinteticoDWVO obj = new RotatividadeSinteticoDWVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>RotatividadeSinteticoDWVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>RotatividadeSinteticoDWVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(RotatividadeSinteticoDWVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>RotatividadeSinteticoDWVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>RotatividadeSinteticoDWVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(RotatividadeSinteticoDWVO obj) throws Exception {
        try {
            RotatividadeSinteticoDWVO.validarDados(obj);
            // RotatividadeSinteticoDW.incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO RotatividadeSinteticoDW( mes, ano, empresa,situacao, qtdVigentesMesAnterior, vencido,dia ) VALUES ( ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setInt(1, obj.getMes().intValue());
            sqlInserir.setInt(2, obj.getAno().intValue());
            if (obj.getEmpresa().getCodigo().intValue() != 0) {
                sqlInserir.setInt(3, obj.getEmpresa().getCodigo().intValue());
            } else {
                sqlInserir.setNull(3, 0);
            }
            sqlInserir.setString(4, obj.getSituacao());
            sqlInserir.setInt(5, obj.getQtdVigentesMesAnterior().intValue());
            sqlInserir.setInt(6, obj.getQtdVencido().intValue());
            sqlInserir.setTimestamp(7, Uteis.getDataJDBCTimestamp(obj.getDia()));
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));

        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>RotatividadeSinteticoDWVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>RotatividadeSinteticoDWVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(RotatividadeSinteticoDWVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            RotatividadeSinteticoDWVO.validarDados(obj);
            // RotatividadeSinteticoDW.alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE RotatividadeSinteticoDW set mes=?, ano=?, empresa=?, situacao=?, qtdVigentesMesAnterior=?, vencido=?, dia=? HERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setInt(1, obj.getMes().intValue());
            sqlAlterar.setInt(2, obj.getAno().intValue());
            if (obj.getEmpresa().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(3, obj.getEmpresa().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(3, 0);
            }
            sqlAlterar.setString(4, obj.getSituacao());
            sqlAlterar.setInt(5, obj.getQtdVigentesMesAnterior().intValue());
            sqlAlterar.setInt(6, obj.getQtdVencido().intValue());
            sqlAlterar.setTimestamp(7, Uteis.getDataJDBCTimestamp(obj.getDia()));
            sqlAlterar.setInt(8, obj.getCodigo().intValue());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>RotatividadeSinteticoDWVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>RotatividadeSinteticoDWVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(RotatividadeSinteticoDWVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            //     RotatividadeSinteticoDW.excluir(getIdEntidade());
            String sql = "DELETE FROM RotatividadeSinteticoDW WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    
    public void excluirRotatividadeSinteticoDWPorDia(Date dia, Integer empresa) throws Exception {
    		String sql = "DELETE FROM RotatividadeSinteticoDW WHERE Cast (dia as date) = Cast( '"+Uteis.getDataJDBC(dia)+"'as date) and empresa = "+empresa;
    		PreparedStatement sqlExcluir = con.prepareStatement(sql);    		
    		sqlExcluir.execute();    
    }

    /**
     * Responsável por realizar uma consulta de <code>RotatividadeSinteticoDW</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>RotatividadeSinteticoDWVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        //     SuperEntidade.consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM RotatividadeSinteticoDW WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public ResultSet consultarRotatividadePorSituacaoMesAnoEmpresaResultSet(
            Integer mes, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT RotatividadeSinteticoDW.*  " +
                "FROM RotatividadeSinteticoDW " +
                "WHERE mes = " + mes + " " +
                "AND ano = " + ano + " " +
                "AND empresa = " + codigoEmpresa.intValue();
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return tabelaResultado;
    }

    public RotatividadeSinteticoDWVO consultarRotatividadePorMesAnoEmpresa(
            Integer mes, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM RotatividadeSinteticoDW WHERE mes = ? AND ano = ? AND empresa = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sqlStr);
        sqlConsultar.setInt(1, mes);
        sqlConsultar.setInt(2, ano);
        sqlConsultar.setInt(3, codigoEmpresa);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if(tabelaResultado.next())
            return montarDados(tabelaResultado, nivelMontarDados);
        else
            return new RotatividadeSinteticoDWVO();
    }

    public Integer obterTodosClienteVirgentesPorEmpresa(Integer mes, Integer codigoEmpresa) throws Exception {
        String sqlStr = "SELECT SUM(qtdVigentesMesAnterior)as peso " +
                "FROM RotatividadeSinteticoDW " +
                "WHERE qtdVigentesMesAnterior <> 0 " +
                "AND mes = " + mes +
                " AND empresa = " + codigoEmpresa.intValue();
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return (new Integer(tabelaResultado.getInt(1)));
    }

    public List consultarRotatividadePorSituacaoMesAnoEmpresa(String situacao,
            Integer mes, Integer ano, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT RotatividadeSinteticoDW.*  " +
                "FROM RotatividadeSinteticoDW " +
                "WHERE mes = " + mes + " " +
                "AND ano = " + ano + " " +
                "AND situacao = '" + situacao.toUpperCase() + "' " +
                "AND empresa = " + codigoEmpresa.intValue();
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>RotatividadeSinteticoDWVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            RotatividadeSinteticoDWVO obj = new RotatividadeSinteticoDWVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>RotatividadeSinteticoDWVO</code>.
     * @return  O objeto da classe <code>RotatividadeSinteticoDWVO</code> com os dados devidamente montados.
     */
    public static RotatividadeSinteticoDWVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        RotatividadeSinteticoDWVO obj = new RotatividadeSinteticoDWVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDia(dadosSQL.getDate("dia"));
        obj.setMes(new Integer(dadosSQL.getInt("mes")));
        obj.setAno(new Integer(dadosSQL.getInt("ano")));
        obj.getEmpresa().setCodigo(new Integer(dadosSQL.getInt("empresa")));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setQtdVigentesMesAnterior(new Integer(dadosSQL.getInt("qtdVigentesMesAnterior")));
        obj.setQtdVencido(new Integer(dadosSQL.getInt("Vencido")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>RotatividadeSinteticoDWVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public RotatividadeSinteticoDWVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM RotatividadeSinteticoDW WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( RotatividadeSinteticoDW ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
}

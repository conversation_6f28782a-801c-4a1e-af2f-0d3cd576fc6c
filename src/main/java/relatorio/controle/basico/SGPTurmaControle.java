package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.JSONArray;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.SGPModalidadeComTurmaTO;
import relatorio.negocio.jdbc.basico.SGPTurmaRel;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.faces.model.SelectItem;
import java.io.*;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class SGPTurmaControle extends SuperControleRelatorio {
    private List<SGPModalidadeComTurmaTO> listaFrequencias = new ArrayList<>();
    private Date dataInicio;
    private Date dataFim;
    private List<ModalidadeVO> listaModalidades = new ArrayList<>();
    private ModalidadeVO modalidade = new ModalidadeVO();
    private TurmaVO turma = new TurmaVO();
    private boolean selecionarEmpresa;
    private Integer tipoRelatorioSelecionado = 0;
    private Integer modeloRelatorioSelecionado = 0;
    private Integer vezesSemanaRelatorioSelecionado = 0;
    private String filtros;
    private List<ClienteVO> listaClientes;
    private List<SelectItem> listaModalidade = new ArrayList<>();
    private List<SelectItem> listaTurma = new ArrayList<>();
    private List<SelectItem> tipoRel = new ArrayList<>();
    private List<SelectItem> modeloRel = new ArrayList<>();
    private List<SelectItem> vezesSemanaRel = new ArrayList<>();

    public SGPTurmaControle() {
        inicializarDados();
    }

    public void inicializarDados() {
        try {
            setListaFrequencias(new ArrayList<>());
            setDataInicio(new Date());
            setDataFim(new Date());
            setSelecionarEmpresa(false);
            setFiltros("");
            montarListaSelectItemEmpresa();
            montarListaModalidade();
            montarListaTurmas();
            montarListaTurma();

            getTipoRel().add(new SelectItem(0, "Inscrições"));
            getTipoRel().add(new SelectItem(1, "Presenças"));
            getTipoRel().add(new SelectItem(2, "Pessoas Atendidas"));

            getModeloRel().add(new SelectItem(0, "Turmas"));
            getModeloRel().add(new SelectItem(1, "Avaliação Física"));

            getVezesSemanaRel().add(new SelectItem(2, "2"));
            getVezesSemanaRel().add(new SelectItem(3, "3"));

            limparMsg();
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String consultar() {
        try {
            limparMsg();
            setarFiltros();
            SGPTurmaRel rel = getFacade().getSGPTurmaRel();
            rel.setListaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached(new JSONArray());
            rel.setarParametrosConsulta(getDataInicio(), getDataFim(), getEmpresaLogado(), getListaModalidades(), getModalidade(), getTurma(), getTipoRelatorioSelecionado(), getModeloRelatorioSelecionado(), getKey(), getVezesSemanaRelatorioSelecionado());
            rel.validarDados();
            setListaFrequencias(rel.consultar());
            setMensagem("Dados consultados");
            setModalidade(new ModalidadeVO());
            setTurma(new TurmaVO());
            return "relatorioTurma";
        } catch (ConsistirException e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultarTurma";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    public void consultarExcel() {
        try {
            limparMsg();
            setarFiltros();
            SGPTurmaRel rel = getFacade().getSGPTurmaRel();
            rel.setListaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached(new JSONArray());
            rel.setarParametrosConsulta(getDataInicio(), getDataFim(), getEmpresaLogado(), getListaModalidades(), getModalidade(), getTurma(), getTipoRelatorioSelecionado(), getModeloRelatorioSelecionado(), getKey(), getVezesSemanaRelatorioSelecionado());
            rel.validarDados();
            Object[][] dadosInscricoes = rel.obterDadosExcelEstatistico(0);
            Object[][] dadosPresencas = rel.obterDadosExcelEstatistico(1);
            Object[][] dadosPessoasAtendidas = rel.obterDadosExcelEstatistico(2);
            montarExcelSgpEstatisticoExportar(Calendario.getAno(dataInicio), Calendario.getMes(dataInicio), dadosInscricoes, dadosPresencas, dadosPessoasAtendidas);
            setMensagem("Dados consultados");
            setModalidade(new ModalidadeVO());
            setTurma(new TurmaVO());
            setMsgAlert("abrirPopup('../UpdateServlet?op=downloadfile&file=sgpEstatistico.xlsx&mimetype=application/vnd.ms-excel','Transacoes', 800,200);");
        } catch (ConsistirException e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void setarFiltros() {
        StringBuilder filtros = new StringBuilder();
        filtros.append("<b>Modelo Relatório: </b>");
        if (getModeloRelatorioSelecionado() == 0) {
            filtros.append("Turmas").append("</br>");
        } else {
            filtros.append("Avaliação Física").append("</br>");
        }
        filtros.append("<b>Tipo Relatório: </b>");
        if (getTipoRelatorioSelecionado() == 0) {
            filtros.append("Inscrições").append("</br>");
        } else if (getTipoRelatorioSelecionado() == 1) {
            filtros.append("Presenças").append("</br>");
        } else if (getTipoRelatorioSelecionado() == 2) {
            filtros.append("Pessoas Atendidas").append("</br>");
        }
        filtros.append("<b>Inicio: </b>").append(getDataInicio_Apresentar()).append("</br>");
        filtros.append("<b>Fim: </b>").append(getDataFim_Apresentar()).append("</br>");
        filtros.append("<b>Empresa: </b>").append(getEmpresa().getNome()).append("</br>");
        if (getModeloRelatorioSelecionado() == 0) {
            if (getModalidade() != null && !UteisValidacao.emptyNumber(getModalidade().getCodigo())) {
                try {
                    filtros.append("<b>Modalidade: </b>");
                    filtros.append(getFacade().getModalidade().consultarPorChavePrimaria(getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS).getNome()).append("</br>");
                    if (getTurma() != null && !UteisValidacao.emptyNumber(getTurma().getCodigo())) {
                        filtros.append("<b>Turma: </b>");
                        filtros.append(getFacade().getTurma().consultarPorChavePrimaria(getTurma().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS).getDescricao()).append("</br>");
                    }
                } catch (Exception ignore) {
                }
            } else {
                filtros.append("<b>Modalidades: </b> </br>");
                for (ModalidadeVO modalidade : listaModalidades) {
                    filtros.append(modalidade.getNome()).append("</br>");
                }
            }
        }
        setFiltros(filtros.toString());
    }

    public String voltar() {
        return "consultarTurma";
    }

    public void montarListaSelectItemEmpresa() {
        try {
            if (JSFUtilities.isJSFContext()) {
                setListaEmpresas(new ArrayList<>());
                setEmpresa(getEmpresaLogado());
            }
            if (getEmpresa() == null || getEmpresa().getCodigo() == 0) {
                setSelecionarEmpresa(true);
                montarListaEmpresas();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaModalidade() throws Exception {
        setModalidade(new ModalidadeVO());
        setTurma(new TurmaVO());
        setListaModalidade(new ArrayList<>());
        setListaTurma(new ArrayList<>());
        if (getEmpresa().getCodigo() > 0) {
            getListaModalidade().add(new SelectItem(0, ""));
            List resultadoConsulta = getFacade().getModalidade().consultarPorNomeUtilizaTurma("", getEmpresaLogado().getCodigo(), false, true, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (Object aResultadoConsulta : resultadoConsulta) {
                ModalidadeVO obj = (ModalidadeVO) aResultadoConsulta;
                getListaModalidade().add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        }
    }

    public void montarListaTurma() throws Exception {
        setTurma(new TurmaVO());
        setListaTurma(new ArrayList<>());

        if (getModalidade().getCodigo() > 0) {
            getListaTurma().add(new SelectItem(0, ""));
            List resultadoConsulta = getFacade().getTurma().consultarPorCodigoModalidadeVigenciaMaiorQueHoje(getModalidade().getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                TurmaVO obj = (TurmaVO) i.next();
                if( !UteisValidacao.dataMenorDataAtual( Uteis.getDataJDBCTimestamp(obj.getDataFinalVigencia()) )) {
                    getListaTurma().add(new SelectItem(obj.getCodigo(), obj.getIdentificador()));
                }
            }
        }
    }

    public String converterMinutosParaHoras(long min) {
        if(min == 0) {
            return "0,00";
        }
        double horasDecimais = (Uteis.arredondarForcando2CasasDecimais((double) min / 60));
        int horas = (int) horasDecimais;
        int minutos = (int) ((horasDecimais - horas) * 60);
        return String.format("%02d,%02d\n", horas, minutos);
    }

    private void montarListaTurmas() throws Exception {
        setListaModalidades(getFacade().getModalidade().consultarTodasModalidades(getEmpresaLogado().getCodigo(), true, true));
    }

    private void montarExcelSgpEstatisticoExportar(Integer ano, Integer mes, Object[][] inscricoesData, Object[][] presencasData, Object[][] pessoasAtendidasData) throws Exception {
        // Cria um novo workbook e uma planilha
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // Criar as linhas e mesclar células (A1 até D2)
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 3));

        // Cria as linhas e células conforme o exemplo
        Row row0 = sheet.createRow(0);

        // Ajustar largura das colunas mescladas
        for (int col = 0; col <= 3; col++) {
            sheet.setColumnWidth(col, 1); // Ajustar a largura da coluna
        }

        try {
            // Caminho da imagem local
            String imagePath = obterURLFotoEmpresa(); // Substitua pelo caminho correto

            // Ler a imagem
            InputStream inputStream = new URL(imagePath).openStream();
            byte[] imageBytes = IOUtils.toByteArray(inputStream);
            inputStream.close();

            // Adicionar a imagem ao workbook
            int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);
            Drawing<?> drawing = sheet.createDrawingPatriarch();
            ClientAnchor anchor = workbook.getCreationHelper().createClientAnchor();

            // Definir posição da imagem
            anchor.setCol1(0); // Coluna inicial
            anchor.setRow1(0); // Linha inicial
            anchor.setCol2(3); // Coluna final (tamanho da imagem)
            anchor.setRow2(1); // Linha final (tamanho da imagem)
            anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE); // Permite ajuste da imagem

            // Criar a imagem
            Picture picture = drawing.createPicture(anchor, pictureIdx);
            picture.resize(2.0); // Ajusta a imagem ao espaço definido
        } catch (Exception ex) {
            row0.createCell(0).setCellValue("LOGO DA EMPRESA");
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao montar foto logo empresa: " + ex.getMessage());
        }

        row0.createCell(4).setCellValue("01.04 LAZER");

        if (getModeloRelatorioSelecionado() == 0) {
            row0.createCell(16).setCellValue("ANO: "+ano);
        } else {
            row0.createCell(12).setCellValue("ANO: "+ano);
        }

        Row row1 = sheet.createRow(1);
        row1.createCell(4).setCellValue("01.04.21 DESENVOLVIMENTO FÍSICO-ESPORTIVO");

        if (getModeloRelatorioSelecionado() == 0) {
            row1.createCell(16).setCellValue("MÊS: "+mes);
        } else {
            row1.createCell(12).setCellValue("MÊS: "+mes);
        }

        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue("SUBATIVIDADE ESPECÍFICA");
        row2.createCell(11).setCellValue("PROJETO:");

        Row row3 = sheet.createRow(3);
        row3.createCell(0).setCellValue("TIPIFICAÇÃO : SEM");

        Row row4 = sheet.createRow(4);
        row4.createCell(0).setCellValue("SUBATIVIDADE");

        Row row5 = sheet.createRow(5);
        row5.createCell(0).setCellValue(getModeloRelatorioSelecionado() == 0 ? "SERVIÇO: "+
                "MODALIDADE: "+((getModalidade() != null && !UteisValidacao.emptyNumber(getModalidade().getCodigo()))? obterNomeModalidade(getModalidade().getCodigo()) : "TODAS") +
                ", TURMA: "+((getTurma() != null  && !UteisValidacao.emptyNumber(getTurma().getCodigo()))? obterNomeTurma(getTurma().getCodigo()) : "TODAS") +
                ", "+getVezesSemanaRelatorioSelecionado()+"X SEMANA" :
                "SERVIÇO: AVALIAÇÃO FÍSICA");

        Row row6 = sheet.createRow(6);
        row6.createCell(0).setCellValue("Inscrição");
        if (getModeloRelatorioSelecionado() == 0) {
            row6.createCell(10).setCellValue("Presença");
            row6.createCell(15).setCellValue("Pessoas Atendidas");
        } else {
            row6.createCell(6).setCellValue("Presença");
            row6.createCell(11).setCellValue("Pessoas Atendidas");
        }

        Row row7 = sheet.createRow(7);
        String[] headers = {"Dia", "T.C", "DEP", "P.G.", "P.N.I", "Total", "Evasões", "Turmas", "Turmas Ativas", "CH", "T.C", "DEP", "P.G.", "P.N.I", "Total", "T.C", "DEP", "P.G.", "P.N.I", "Total"};
        if(getModeloRelatorioSelecionado() == 1){
            headers = new String[]{"Dia", "T.C", "DEP", "P.G.", "P.N.I", "Total", "T.C", "DEP", "P.G.", "P.N.I", "Total", "T.C", "DEP", "P.G.", "P.N.I", "Total"};
        }
        for (int i = 0; i < headers.length; i++) {
            Cell cell = row7.createCell(i);
            cell.setCellValue(headers[i]);
        }

        if (getModeloRelatorioSelecionado() == 0) {
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 4, 15));
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 16, 19));
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 4, 15));
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 16, 19));

            sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 10));
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 11, 19));
            sheet.addMergedRegion(new CellRangeAddress(3, 3, 0, 19));
            sheet.addMergedRegion(new CellRangeAddress(4, 4, 0, 19));
            sheet.addMergedRegion(new CellRangeAddress(5, 5, 0, 19));
            sheet.addMergedRegion(new CellRangeAddress(6, 6, 0, 9));
            sheet.addMergedRegion(new CellRangeAddress(6, 6, 10, 14));
            sheet.addMergedRegion(new CellRangeAddress(6, 6, 15, 19));
        } else {
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 4, 11));
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 12, 15));
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 4, 11));
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 12, 15));

            sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 10));
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 11, 15));
            sheet.addMergedRegion(new CellRangeAddress(3, 3, 0, 15));
            sheet.addMergedRegion(new CellRangeAddress(4, 4, 0, 15));
            sheet.addMergedRegion(new CellRangeAddress(5, 5, 0, 15));
            sheet.addMergedRegion(new CellRangeAddress(6, 6, 0, 5));
            sheet.addMergedRegion(new CellRangeAddress(6, 6, 6, 10));
            sheet.addMergedRegion(new CellRangeAddress(6, 6, 11, 15));
        }

        int rowNum = 8;
        if (inscricoesData != null) {
            for (Object[] rowData : inscricoesData) {
                Row row = sheet.createRow(rowNum++);
                int colNum = 0;
                for (Object field : rowData) {
                    if ((getModeloRelatorioSelecionado() == 0 && colNum <= 9) ||
                            (getModeloRelatorioSelecionado() == 1 && colNum <= 5)) {
                        Cell cell = row.createCell(colNum++);
                        if (field instanceof String) {
                            cell.setCellValue((String) field);
                        } else if (field instanceof Integer) {
                            cell.setCellValue((Integer) field);
                        }
                    }
                }
            }
        }

        rowNum = 8;
        if (presencasData != null) {
            for (Object[] rowData : presencasData) {
                Row row = sheet.getRow(rowNum++);
                int colNum = getModeloRelatorioSelecionado() == 0 ? 10 : 6;
                int qtdColunaPular = 1;
                for (Object field : rowData) {
                    if (qtdColunaPular > 0) {
                        qtdColunaPular--;
                        continue;
                    }
                    if ((getModeloRelatorioSelecionado() == 0 && colNum <= 14) ||
                            (getModeloRelatorioSelecionado() == 1 && colNum <= 10)) {
                        Cell cell = row.createCell(colNum++);
                        if (field instanceof String) {
                            cell.setCellValue((String) field);
                        } else if (field instanceof Integer) {
                            cell.setCellValue((Integer) field);
                        }
                    }
                }
            }
        }

        rowNum = 8;
        if (pessoasAtendidasData != null) {
            for (Object[] rowData : pessoasAtendidasData) {
                Row row = sheet.getRow(rowNum++);
                int colNum = getModeloRelatorioSelecionado() == 0 ? 15 : 11;
                int qtdColunaPular = 1;
                for (Object field : rowData) {
                    if (qtdColunaPular > 0) {
                        qtdColunaPular--;
                        continue;
                    }
                    if ((getModeloRelatorioSelecionado() == 0 && colNum <= 19) ||
                            (getModeloRelatorioSelecionado() == 1 && colNum <= 15)) {
                        Cell cell = row.createCell(colNum++);
                        if (field instanceof String) {
                            cell.setCellValue((String) field);
                        } else if (field instanceof Integer) {
                            cell.setCellValue((Integer) field);
                        }
                    }
                }
            }
        }

        // Aplica o estilo de título às células
        Iterator<Row> rowIterator = sheet.iterator();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            for (int i = 0; i <= (getModeloRelatorioSelecionado() == 0 ? 19 : 15); i++) {
                Cell cell = row.getCell(i);
                if (cell == null) {
                    cell = row.createCell(i);
                }
                if (row.getRowNum() == 2 || row.getRowNum() == 3) {
                    // Cria um estilo para os títulos (negrito e alinhamento padrão)
                    CellStyle titleStyle = workbook.createCellStyle();
                    Font titleFont = workbook.createFont();
                    titleFont.setBold(true);
                    titleStyle.setFont(titleFont);
                    titleStyle.setFillForegroundColor(IndexedColors.GREY_40_PERCENT.getIndex()); // Cor de fundo cinza
                    titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    titleStyle.setAlignment(HorizontalAlignment.LEFT); // Alinhamento padrão
                    titleStyle.setBorderRight(BorderStyle.THIN);
                    titleStyle.setBorderBottom(BorderStyle.THIN);
                    cell.setCellStyle(titleStyle);
                } else if (row.getRowNum() == 6 || row.getRowNum() == 7) {
                    // Cria um estilo para as células de cabeçalho (fundo cinza e texto centralizado)
                    CellStyle headerStyle = workbook.createCellStyle();
                    Font headerFont = workbook.createFont();
                    headerFont.setBold(true);
                    headerStyle.setFont(headerFont);
                    headerStyle.setAlignment(HorizontalAlignment.CENTER);
                    headerStyle.setFillForegroundColor(IndexedColors.GREY_40_PERCENT.getIndex()); // Cor de fundo cinza
                    headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    headerStyle.setBorderRight(BorderStyle.THIN);
                    headerStyle.setBorderBottom(BorderStyle.THIN);
                    cell.setCellStyle(headerStyle);
                } else if (row.getRowNum() == 0 || row.getRowNum() == 1 || row.getRowNum() == 4 || row.getRowNum() == 5) {
                    // Cria um estilo para os títulos (negrito e centralizado)
                    CellStyle titleStyle = workbook.createCellStyle();
                    Font titleFont = workbook.createFont();
                    titleFont.setBold(true);
                    titleStyle.setFont(titleFont);
                    titleStyle.setAlignment(HorizontalAlignment.CENTER); // Centraliza o texto
                    titleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex()); // Cor de fundo cinza
                    titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    titleStyle.setBorderRight(BorderStyle.THIN);
                    titleStyle.setBorderBottom(BorderStyle.THIN);
                    cell.setCellStyle(titleStyle);
                } else {
                    // Cria um estilo para os títulos (negrito e centralizado)
                    CellStyle titleStyle = workbook.createCellStyle();
                    titleStyle.setAlignment(HorizontalAlignment.RIGHT); // Centraliza o texto
                    Font titleFont = workbook.createFont();
                    titleStyle.setFont(titleFont);
                    titleStyle.setBorderLeft(BorderStyle.THIN);
                    titleStyle.setBorderRight(BorderStyle.THIN);
                    titleStyle.setBorderBottom(BorderStyle.THIN);
                    cell.setCellStyle(titleStyle);
                }
            }
        }

        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        row0.getCell(0).setCellStyle(style);

        // Ajusta o tamanho das colunas
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // Escreve o arquivo
        try (FileOutputStream fileOut = new FileOutputStream(this.getServletContext().getRealPath("relatorio") + File.separator + "sgpEstatistico.xlsx")) {
            workbook.write(fileOut);
        }

        // Fecha o workbook
        workbook.close();
    }

    private String obterNomeModalidade(Integer codigoModalidade) {
        if (!UteisValidacao.emptyNumber(codigoModalidade)) {
            try {
                return getFacade().getModalidade().consultarPorChavePrimaria(codigoModalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS).getNome();
            } catch (Exception ex) {
                return "";
            }
        }
        return "";
    }

    private String obterNomeTurma(Integer codigoTurma) {
        if (!UteisValidacao.emptyNumber(codigoTurma)) {
            try {
                return getFacade().getTurma().consultarPorChavePrimaria(codigoTurma, Uteis.NIVELMONTARDADOS_DADOSBASICOS).getDescricao();
            } catch (Exception ex) {
                return "";
            }
        }
        return "";
    }

    private String obterURLFotoEmpresa() {
        try {
            if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                String fotoKey = MidiaService.getInstance().genKey(getKey(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, getEmpresaLogado().getCodigo().toString());
                return Uteis.getPaintFotoDaNuvem(fotoKey);

            } else {

                String path = "/imagensCRM/email/tmp/";
                File caminhoBase = new File(getServletContext().getRealPath("/"));

                String pathFull = caminhoBase.getAbsolutePath() + path;
                String nomeImagem = Uteis.retirarAcentuacaoRegex(getEmpresaLogado().getNome().replaceAll(" ", ""));

                getEmpresaLogado().setFotoRelatorio(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaLogado().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
                UteisEmail.criarImagem(pathFull, getEmpresaLogado().getFotoRelatorio(), nomeImagem + ".jpg");
                path = "." + path + nomeImagem + ".jpg";
                return path;
            }
        } catch (Exception ex) {
            return "";
        }
    }

    public boolean isSelecionarEmpresa() {
        return selecionarEmpresa;
    }

    public void setSelecionarEmpresa(Boolean selecionarEmpresa) {
        this.selecionarEmpresa = selecionarEmpresa;
    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public List<SGPModalidadeComTurmaTO> getListaFrequencias() {
        return listaFrequencias;
    }

    public void setListaFrequencias(List<SGPModalidadeComTurmaTO> listaFrequencias) {
        this.listaFrequencias = listaFrequencias;
    }

    public List<ModalidadeVO> getListaModalidades() {
        return listaModalidades;
    }

    public void setListaModalidades(List<ModalidadeVO> listaModalidades) {
        this.listaModalidades = listaModalidades;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataInicio_Apresentar() {
        return Uteis.getData(dataInicio);
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public String getDataFim_Apresentar() {
        return Uteis.getData(dataFim);
    }

    public List<ClienteVO> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClienteVO> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public void prepararListaSemCategoria() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaSemCategoria());
        }
    }

    public void prepararListaComerciario() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaComerciario());
        }
    }

    public void prepararListaDependente() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaDependentes());
        }
    }

    public void prepararListaUsuario() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaUsuarios());
        }
    }

    public void prepararListaCanceladosDesistentes() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaCanceladosDesistentes());
        }
    }

    public List<SelectItem> getListaModalidade() {
        return listaModalidade;
    }

    public void setListaModalidade(List<SelectItem> listaModalidade) {
        this.listaModalidade = listaModalidade;
    }

    public List<SelectItem> getListaTurma() {
        return listaTurma;
    }

    public void setListaTurma(List<SelectItem> listaTurma) {
        this.listaTurma = listaTurma;
    }

    public ModalidadeVO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeVO modalidade) {
        this.modalidade = modalidade;
    }

    public TurmaVO getTurma() {
        return turma;
    }

    public void setTurma(TurmaVO turma) {
        this.turma = turma;
    }

    public List<SelectItem> getTipoRel() {
        return tipoRel;
    }

    public void setTipoRel(List<SelectItem> tipoRel) {
        this.tipoRel = tipoRel;
    }

    public Integer getTipoRelatorioSelecionado() {
        return tipoRelatorioSelecionado;
    }

    public void setTipoRelatorioSelecionado(Integer tipoRelatorioSelecionado) {
        this.tipoRelatorioSelecionado = tipoRelatorioSelecionado;
    }

    public Integer getModeloRelatorioSelecionado() {
        return modeloRelatorioSelecionado;
    }

    public void setModeloRelatorioSelecionado(Integer modeloRelatorioSelecionado) {
        this.modeloRelatorioSelecionado = modeloRelatorioSelecionado;
    }

    public List<SelectItem> getModeloRel() {
        return modeloRel;
    }

    public void setModeloRel(List<SelectItem> modeloRel) {
        this.modeloRel = modeloRel;
    }

    public List<SelectItem> getVezesSemanaRel() {
        return vezesSemanaRel;
    }

    public void setVezesSemanaRel(List<SelectItem> vezesSemanaRel) {
        this.vezesSemanaRel = vezesSemanaRel;
    }

    public Integer getVezesSemanaRelatorioSelecionado() {
        return vezesSemanaRelatorioSelecionado;
    }

    public void setVezesSemanaRelatorioSelecionado(Integer vezesSemanaRelatorioSelecionado) {
        this.vezesSemanaRelatorioSelecionado = vezesSemanaRelatorioSelecionado;
    }
}

package relatorio.controle.basico;

import com.sun.xml.wss.impl.c14n.EXC14nStAXReaderBasedCanonicalizer;
import negocio.armario.ArmarioVO;
import negocio.armario.TamanhoArmarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.basico.Empresa;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 19/10/2015.
 */
public class ArmarioControleRel extends SuperControleRelatorio {
    private ArmarioVO armario;
    private List<SelectItem> listaDeEmpresa;
    private boolean temEmpresa;
    private List<SelectItem> tiposArmarios;
    private Boolean contratoAssinado=false;
    private List<SelectItem> tamanhosArmario;
    private List<SelectItem> planosLocacao;
    private List<ArmarioRelTO> resultado;
    private Integer totalResultado=0;
    private FiltroArmarioTO filtro;
    private Boolean habilitadoGestaoArmarios;
    private List<SelectItem> itemsContratoAssinado;

    public void montarListaSelectItemEmpresa() {
        try {
            getArmario().setEmpresa(getEmpresaLogado());
            if (  getArmario().getEmpresa() != null &&   getArmario().getEmpresa().getNome().equals("")) {
                setTemEmpresa(true);
                montarListaSelectItemEmpresa("");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public ArmarioControleRel(){
        try {
            montarCombosContratoAssinado();
            validarConfiguracoesSistema();
        }catch (Exception ex){
            setMensagemDetalhada(ex);
        }
    }
    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        for (Object aResultadoConsulta : resultadoConsulta) {
            EmpresaVO obj = (EmpresaVO) aResultadoConsulta;
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaDeEmpresa(objs);
    }
    public void validarConfiguracoesSistema() throws  Exception{
        ConfiguracaoSistemaVO aux = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        setHabilitadoGestaoArmarios(aux.getHabilitarGestaoArmarios());
    }
    public void validarCampos() throws Exception{

        if((filtro.getPeriodoLocacaoAte() == null && filtro.getPeriodoLocacaoDe()!=null) || (filtro.getPeriodoLocacaoAte() != null && filtro.getPeriodoLocacaoDe() ==null) ){
            throw new Exception("Informe o período de início correto!");
        }
        if(( filtro.getPeriodoLocacaoAte() != null && filtro.getPeriodoLocacaoDe() !=null ) &&   filtro.getPeriodoLocacaoAte().before(filtro.getPeriodoLocacaoDe()))
            throw new Exception("Informe o período de início correto!");

        if((filtro.getPeriodoRenovacaoAte() == null && filtro.getPeriodoRenovacaoDe()!=null) || (filtro.getPeriodoRenovacaoAte() != null && filtro.getPeriodoRenovacaoDe() ==null) ){
            throw new Exception("Informe o período de renovação correto!");
        }
        if(( filtro.getPeriodoRenovacaoAte() != null && filtro.getPeriodoRenovacaoDe() !=null ) &&   filtro.getPeriodoRenovacaoAte().before(filtro.getPeriodoRenovacaoDe()))
            throw new Exception("Informe o período de renovação correto!");

        if((filtro.getPeriodoVencimentoAte() == null && filtro.getPeriodoVencimentoDe()!=null) || (filtro.getPeriodoVencimentoAte() != null && filtro.getPeriodoVencimentoDe() ==null) ){
            throw new Exception("Informe o período de vencimento correto!");
        }
        if(( filtro.getPeriodoVencimentoAte() != null && filtro.getPeriodoVencimentoDe() !=null ) &&   filtro.getPeriodoVencimentoAte().before(filtro.getPeriodoVencimentoDe()))
            throw new Exception("Informe o período de vencimento correto!");
    }
    public String consultarArmarios(){

        try{
            limparMsg();
            validarCampos();
            filtro.setHabilitadoGestaoArmarios(getHabilitadoGestaoArmarios());
            resultado = getFacade().getArmario().consultarArmariosRel(filtro);
            return "resultado";
        }catch (Exception erro){
            setMsgAlert(erro.getMessage());
            setMensagemDetalhada(erro);
            return "erro";
        }
    }
    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return new Empresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
    }
    public ArmarioVO getArmario() {
        return armario;
    }

    public void setArmario(ArmarioVO armario) {
        this.armario = armario;
    }

    public List<SelectItem> getListaDeEmpresa() {
        return listaDeEmpresa;
    }

    public void setListaDeEmpresa(List<SelectItem> listaDeEmpresa) {
        this.listaDeEmpresa = listaDeEmpresa;
    }

    public boolean isTemEmpresa() {
        return temEmpresa;
    }

    public void setTemEmpresa(boolean temEmpresa) {
        this.temEmpresa = temEmpresa;
    }

    public void preencherTiposArmarios(){
        tiposArmarios = new ArrayList<SelectItem>();
        tiposArmarios.add(new SelectItem("A","Todos"));
        tiposArmarios.add(new SelectItem("F","Feminino"));
        tiposArmarios.add(new SelectItem("M","Masculino"));
        tiposArmarios.add(new SelectItem("U","Unisex"));
    }

    public List<SelectItem> getTiposArmarios() {
        if(tiposArmarios == null)
            preencherTiposArmarios();
        return tiposArmarios;
    }

    public String voltar(){
        return "consulta";
    }
    public void montarPlanosLocacao() throws Exception{
        planosLocacao.clear();
        planosLocacao.add(new SelectItem(0, "Todos"));
        for(ProdutoVO produto : getFacade().getProduto().consultarTodosArmario(getEmpresaLogado().getCodigo())){
            planosLocacao.add(new SelectItem(produto.getCodigo(),produto.getDescricao()));
        }
    }
    public void setTiposArmarios(List<SelectItem> tiposArmarios) {
        this.tiposArmarios = tiposArmarios;
    }

    public Boolean getContratoAssinado() {
        return contratoAssinado;
    }

    public void setContratoAssinado(Boolean contratoAssinado) {
        this.contratoAssinado = contratoAssinado;
    }

    public void montarTamanhoArmarios() throws Exception{
    tamanhosArmario.clear();
        tamanhosArmario.add(new SelectItem(0,"Todos"));
        for(TamanhoArmarioVO tamanhoArmarioVO : getFacade().getTamanhoArmario().consultarPorDescricao("")){
            tamanhosArmario.add(new SelectItem(tamanhoArmarioVO.getCodigo(),tamanhoArmarioVO.getDescricao()));
        }

    }
    public List<SelectItem> getTamanhosArmario() throws Exception{
        if(tamanhosArmario==null){
            tamanhosArmario = new ArrayList<SelectItem>();
            montarTamanhoArmarios();
        }
        return tamanhosArmario;
    }

    public List<SelectItem> getPlanosLocacao() throws Exception{
        if(planosLocacao == null) {
            planosLocacao = new ArrayList<SelectItem>();
            montarPlanosLocacao();
        }
        return planosLocacao;
    }

    public List<ArmarioRelTO> getResultado() {
        if(resultado==null)
            resultado = new ArrayList<ArmarioRelTO>();
        return resultado;
    }


    public Integer getTotalResultado() {
        totalResultado = resultado.size();
        return totalResultado;
    }
    public void montarCombosContratoAssinado(){
        getItemsContratoAssinado().clear();
        getItemsContratoAssinado().add(new SelectItem(""," - "));
        getItemsContratoAssinado().add(new SelectItem("true","Sim"));
        getItemsContratoAssinado().add(new SelectItem("false","Não"));
    }
    public void setTotalResultado(Integer totalResultado) {
        this.totalResultado = totalResultado;
    }

    public void setResultado(List<ArmarioRelTO> resultado) {
        this.resultado = resultado;
    }

    public void setPlanosLocacao(List<SelectItem> planosLocacao) {
        this.planosLocacao = planosLocacao;
    }

    public void setTamanhosArmario(List<SelectItem> tamanhosArmario) {
        this.tamanhosArmario = tamanhosArmario;
    }

    public FiltroArmarioTO getFiltro() {
        if(filtro==null)
         filtro = new FiltroArmarioTO();
        return filtro;
    }

    public List<SelectItem> getItemsContratoAssinado() {
        if(itemsContratoAssinado == null)
           itemsContratoAssinado = new ArrayList<SelectItem>();
        return itemsContratoAssinado;
    }

    public void setItemsContratoAssinado(List<SelectItem> itemsContratoAssinado) {
        this.itemsContratoAssinado = itemsContratoAssinado;
    }

    public Boolean getHabilitadoGestaoArmarios() {
        return habilitadoGestaoArmarios;
    }

    public void setHabilitadoGestaoArmarios(Boolean habilitadoGestaoArmarios) {
        this.habilitadoGestaoArmarios = habilitadoGestaoArmarios;
    }

    public void setFiltro(FiltroArmarioTO filtro) {
        this.filtro = filtro;
    }
}

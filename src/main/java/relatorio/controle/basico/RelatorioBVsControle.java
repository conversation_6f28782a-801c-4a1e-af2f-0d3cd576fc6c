/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.to.FiltrosConsultaRelatorioBVsTO;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import controle.arquitetura.SuperControle;

import java.awt.*;
import java.util.*;
import java.util.List;
import javax.faces.model.SelectItem;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PerguntaClienteVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.basico.enumerador.TipoServicoEnum;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.BVsRelatorioListasTO;
import relatorio.negocio.comuns.basico.BVsRelatorioTO;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;
import relatorio.negocio.comuns.basico.ResumoClientesBV;

/**
 * Controlador do relatório de BVs que faz interface entre as telas e a persistência
 * <AUTHOR>
 */
public class RelatorioBVsControle extends SuperControle {

    //TO que contém todos os filtros do relatório
    private FiltrosConsultaRelatorioBVsTO filtrosConsulta;
    //lista de empresas da tela principal
    private List<EmpresaVO> empresas;
    //flag usado para mostrar o contéudo da consulta
    private Boolean mostrarConteudo;
    //usado para apresentar a tabela com os dados principais do relatório
    private BVsRelatorioTO bVsRelatorioTO = new BVsRelatorioTO();
    //lista de clientes do link de Detalhes
    private List<ResumoClientesBV> resumoClientesBVs = new ArrayList<ResumoClientesBV>();
    private List<PerguntaClienteVO> listaPerguntasVO = new ArrayList<PerguntaClienteVO>();
    private boolean mostrarCampoRespostaTextual = false;
    private boolean relatorioPesquisa = false;
    private ArrayList<ItemRelatorioTO> quantidadeRespostasNPS;
    private String resultadoNPSPorcentagem;
    private String  valorTotalRespondidoApresentar;
    private String corResultadoNPS;
    private String mensagemNPS;
    private String resultadoNPSMostrar;
    private boolean mostrarNPS;
    int valorTotalRespondido;
    float resultadoNPS;
    int detratores;
    int qtdDetratores;
    int neutros;
    int qtdNeutros;
    int promotores;
    int qtdPromotores;
    private String respostaAbrir;
    private List<ColaboradorVO> consultoresAtivos;
    private List<ColaboradorVO> consultoresInativos;

    private List<SelectItem> listaPerguntas;

    public void setListaPerguntas(List<SelectItem> listaPerguntas) {
        this.listaPerguntas = listaPerguntas;
    }

    public RelatorioBVsControle() throws Exception {
        filtrosConsulta = new FiltrosConsultaRelatorioBVsTO();
        inicializarFiltros();
    }

    /**
     * Responsável por inicializar os filtros da consulta
     */
    public void inicializarFiltros() {
        try {
            this.setEmpresas(getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_TELACONSULTA));
            inicializarFiltroConsultor();
            filtrosConsulta.setDataInicial(null);
            filtrosConsulta.setDataFinal(null);
            filtrosConsulta.setCodigoMes(this.pegarMesAtual());
            filtrosConsulta.setAno(Calendario.hoje());
            filtrosConsulta.setCodigoEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Responsável por limpar os filtros do relatório
     * @throws Exception
     */
    public void limparCampos() throws Exception {
        filtrosConsulta.setDataInicial(null);
        filtrosConsulta.setDataFinal(null);
        filtrosConsulta.setCodigoMes(this.pegarMesAtual());
        filtrosConsulta.setDescricaoPergunta("");
        filtrosConsulta.setFiltrosApresentar("");
        mostrarConteudo = false;
        setbVsRelatorioTOs(new BVsRelatorioTO());
        inicializarFiltroConsultor();
        getListaQuestionarios();
        setListaPerguntas(new ArrayList<SelectItem>());
    }

    /**
     * Lista com o tipo de consulta de datas
     */
    public List<SelectItem> getListaTipoConsultas() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem(1, "Período"));
        itens.add(new SelectItem(2, "Mês/Ano"));
        return itens;
    }

    public void alterarTipoConsulta() {
        if (getFiltrosConsulta().getTipoConsulta().equals(1)) {
            getFiltrosConsulta().setTipoConsulta(2);
            // Alterando a data para nulo para que seja pesquisado pelo mês, se não o mesmo ficaria pesquisando com a data da consulta do tipo 1
            getFiltrosConsulta().setDataFinal(null);
            getFiltrosConsulta().setDataInicial(null);
        } else {
            getFiltrosConsulta().setTipoConsulta(1);
        }
    }

    /**
     * Responsável por retornar uma lista de empresas cadastradas
     *
     * <AUTHOR> 18/06/2012
     * @return
     * @throws Exception
     */
    public List<SelectItem> getListaEmpresa() throws Exception {
        // montar lista de select itens
        List<SelectItem> objs = new ArrayList<SelectItem>();
        for (EmpresaVO empresa : this.getEmpresas()) {
            objs.add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
        return objs;
    }

    /**
     * Retorna a lista de questionários
     * @throws Exception
     */
    public List<SelectItem> getListaQuestionarios() throws Exception {
        List<QuestionarioVO> lista = new ArrayList<QuestionarioVO>();
        if (isRelatorioPesquisa()) {
            lista = getFacade().getRelatorioBVs().consultarQuestionariosPorTipo(TipoServicoEnum.PESQUISA, false);
        } else {
            lista = getFacade().getRelatorioBVs().consultarQuestionarios(filtrosConsulta.getCodigoEmpresa(), isRelatorioPesquisa());
            Ordenacao.ordenarLista(lista, "tituloPesquisa");
        }
        // montar lista de select itens
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        for (QuestionarioVO questionario : lista) {
            if(isRelatorioPesquisa()){
                objs.add(new SelectItem(questionario.getCodigo(), questionario.getNomeInterno()));
            }else{
                objs.add(new SelectItem(questionario.getCodigo(), questionario.getTituloPesquisa()));
            }
        }
        return objs;

    }

    /**
     * Retorna a lista de perguntas que o cliente respondeu, se tiver escolhido algum questionario pesquisa as perguntas dele.
     * Se não pesquisa por todos.
     * @throws Exception
     */
    public List<SelectItem> getListaPerguntas() throws Exception {
        if(listaPerguntas == null){
            listaPerguntas  = new ArrayList<SelectItem>();
        }
        return listaPerguntas;
    }

    public void  montarListaPerguntas() throws Exception {
        listaPerguntasVO = (getFiltrosConsulta().isSomentePerguntasNoQuestionario() ? getFacade().getRelatorioBVs().consultarPerguntasNoQuestionario(getFiltrosConsulta().getCodigoQuestionario()) : getFacade().getRelatorioBVs().consultarPerguntaCliente(getFiltrosConsulta().getCodigoQuestionario()));
        // montar lista de select itens
        listaPerguntas = new ArrayList<SelectItem>();
        listaPerguntas.add(new SelectItem(0,""));
        int index = 1;
        for (PerguntaClienteVO perguntas : listaPerguntasVO) {
            listaPerguntas.add(new SelectItem(index++, perguntas.getDescricao()));
        }
    }

    /**
     * Retorna a lista de eventos
     * @throws Exception
     */
    public List<SelectItem> getListaEventos() throws Exception {
        List<EventoVO> listaEventos;
        listaEventos = getFacade().getEvento().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        // montar lista de select itens
        List<SelectItem> objs = new ArrayList<SelectItem>();
        for (EventoVO eventos : listaEventos) {
            objs.add(new SelectItem(eventos.getCodigo(), eventos.getDescricao()));
        }
        if (!UteisValidacao.emptyList(objs)) {
            objs.add(0, new SelectItem(0, ""));
        }
        return objs;

    }

    /**
     * Responsável por fazer a consulta setando filtros marcados em tela
     * <AUTHOR>
     * 19/06/2012
     */
    public void consultarComFiltros() {
        try {
            limparMsg();
            filtrosConsulta.setFiltrosApresentar("");
            mostrarConteudo = false;
            setbVsRelatorioTOs(new BVsRelatorioTO());
            FiltrosConsultaRelatorioBVsTO.validarDados(filtrosConsulta);
            montarConsultoresSelecionados();
            setbVsRelatorioTOs(getFacade().getRelatorioBVs().montarRelatorioBVs(this.getFiltrosConsulta()));
            montarFiltros();
            montarDataPizzaListaRespostasPergunta();
            mostrarConteudo = true;
            mostrarNPS = false;
            if(bVsRelatorioTO.getPerguntaVO().getTipoPergunta().equals("NS")){
                mostrarNPS = true;
                obterResultadoNPS();
            }
            if(UteisValidacao.emptyList(bVsRelatorioTO.getListaBVsRelatorioListasTO())){
                filtrosConsulta.setFiltrosApresentar("");
                mostrarConteudo = false;
                mostrarNPS = false;
                throw new Exception("Nenhuma resposta encontrada.");
            }
            montarJSONGrafico();
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void montarConsultoresSelecionados() {
        filtrosConsulta.setConsultores(new ArrayList<>());

        for (ColaboradorVO obj : getConsultoresAtivos()) {
            if (obj.getSelecionado()) {
                filtrosConsulta.getConsultores().add(obj);
            }
        }

        for (ColaboradorVO obj : getConsultoresInativos()) {
            if (obj.getSelecionado()) {
                filtrosConsulta.getConsultores().add(obj);
            }
        }
    }

    public void montarFiltros() throws Exception {
        String filtros = "";
        if (filtrosConsulta.getCodigoEmpresa() != null && filtrosConsulta.getCodigoEmpresa() != 0) {
            EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(
                    getFiltrosConsulta().getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            filtros += "Empresa: " + empresaVO.getNome();
        }
        if (filtrosConsulta.getTipoConsulta() == 1) {
            filtros += " | Período: " + Uteis.getDataAplicandoFormatacao(filtrosConsulta.getDataInicial(), "dd/MM/yyyy")+ " até "+ Uteis.getDataAplicandoFormatacao(filtrosConsulta.getDataFinal(), "dd/MM/yyyy");
        }
        if (filtrosConsulta.getTipoConsulta() == 2) {
            if (filtrosConsulta.getCodigoMes() != null && filtrosConsulta.getCodigoMes() != 0) {
                filtros += " | Mês: " + Mes.getMesPeloCodigo(filtrosConsulta.getCodigoMes());
            }
            if (filtrosConsulta.getAno() != null) {
                filtros += " | Ano: " + Uteis.getAnoData(filtrosConsulta.getAno());
            }
        }
        if (filtrosConsulta.getCodigoQuestionario() != 0) {
            QuestionarioVO questionarioVO = getFacade().getQuestionario().consultarPorCodigoExato(filtrosConsulta.getCodigoQuestionario(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            filtros += " | Questionário: " + (isRelatorioPesquisa() ? questionarioVO.getTituloPesquisa() : questionarioVO.getNomeInterno());
        }
        if (!filtrosConsulta.getDescricaoPergunta().isEmpty()) {
            filtros += " | Pergunta: " + filtrosConsulta.getDescricaoPergunta();
        }
        if (filtrosConsulta.getEventoVO().getCodigo() != 0) {
            EventoVO eventoVO = getFacade().getEvento().consultarPorCodigo(filtrosConsulta.getEventoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            filtros += " | Evento: " + eventoVO.getDescricao();
        }

        if (filtrosConsulta.getRespostaSubjetiva()!=null && !filtrosConsulta.getRespostaSubjetiva().isEmpty()) {
            filtros += " | Resposta Subjetiva: " + filtrosConsulta.getRespostaSubjetiva();
        }
        if (filtrosConsulta.isConsultoresAtivos()) {
            filtros += " | Consultores: Todos os ATIVOS";
        }
        if (filtrosConsulta.isConsultoresInativos()) {
            filtros += " | Consultores: Todos os INATIVOS";
        }
        if (!UteisValidacao.emptyList(filtrosConsulta.getConsultores())) {
            filtros += " | Colaboradores: ";
            String filtrosConsultor = "";
            for (ColaboradorVO obj : filtrosConsulta.getConsultores()) {
                filtrosConsultor += ", " + obj.getPessoa().getNome() + "";
            }
            filtrosConsultor = filtrosConsultor.replaceFirst(",", "");
            filtros += filtrosConsultor;
        }
        filtrosConsulta.setFiltrosApresentar(filtros);
    }

    /**
     * Seta a lista de clientes ao clicar em detalhes das respostas
     * @throws Exception
     */
    public void verDetalhes() throws Exception {
        //TODO criar visualização de detalhes para pesquisa NPS
        BVsRelatorioListasTO obj = (BVsRelatorioListasTO) context().getExternalContext().getRequestMap().get("respostas");
        if (obj != null) {
            setResumoClientesBVs(getFacade().getRelatorioBVs().consultarResumoClientes(
                    filtrosConsulta.getCodigoEmpresa(), bVsRelatorioTO.getPerguntaVO().getDescricao(),
                    obj.getRespostaVO().getDescricaoRespota(), filtrosConsulta));
        }
    }

    /**
     * Usado para permitir que posteriormente possa ser pesquisado por
     * mais de uma pergunta
     */
    public void preencherListaPerguntas() {
        List<String> listaPerguntas = new ArrayList<String>();
        if(UteisValidacao.emptyNumber(filtrosConsulta.getIndexPergunta())){
            filtrosConsulta.setDescricaoPergunta("");
        } else {
            filtrosConsulta.setDescricaoPergunta(listaPerguntasVO.get(filtrosConsulta.getIndexPergunta() - 1).getDescricao()); // index 0 é a string vazia, values começão no 1 para o selectItem, por isso o -1
            listaPerguntas.add(filtrosConsulta.getDescricaoPergunta());
            if (listaPerguntasVO.get(filtrosConsulta.getIndexPergunta() - 1).getTextual()) {
                setMostrarCampoRespostaTextual(true);
            } else {
                setMostrarCampoRespostaTextual(false);
            }
        }
        filtrosConsulta.setListaDescricoesPergunta(listaPerguntas);
    }

    public void inicializarFiltroConsultor() throws Exception {
        this.getFiltrosConsulta().setConsultores(new ArrayList<>());
        this.setConsultoresAtivos(new ArrayList<>());
        this.setConsultoresInativos(new ArrayList<>());

        List<ColaboradorVO> listaGeral = getFacade().getColaborador().consultarPorTipoColaborador(TipoColaboradorEnum.CONSULTOR,
                getFiltrosConsulta().getCodigoEmpresa(), false, Uteis.NIVELMONTARDADOS_MINIMOS);

        for (ColaboradorVO col : listaGeral) {
            if (col.getSituacao().equalsIgnoreCase("AT")) {
                this.getConsultoresAtivos().add(col);
            } else if (col.getSituacao().equalsIgnoreCase("NA")) {
                this.getConsultoresInativos().add(col);
            }
        }
    }

    /**
     * Retorna a lista de Meses
     *
     * @return List<SelectItem>
     * @throws Exception
     */
    public List<SelectItem> getMeses() throws Exception {
        // criar lista para retorno
        final List<SelectItem> itens = new ArrayList<SelectItem>();
        // percorrer os valores do enumerador TipoMesesEnum
        for (Mes tipoMeses : Mes.values()) {
            // adicionar os meses na lista de retorno
            itens.add(new SelectItem(tipoMeses.getCodigo(), tipoMeses.getDescricao()));
        }
        // retornar a lista
        return itens;
    }

    /**
     * Pegar mês atual
     *
     * @return data
     */
    public int pegarMesAtual() {
        // pegar data de hoje
        Date data = negocio.comuns.utilitarias.Calendario.hoje();
        return Uteis.getMesData(data);

    }

    /**
     * Pegar ano atual
     *
     * @return ano
     */
    public int pegarAnoAtual() {
        // pegar data de hoje
        Date data = negocio.comuns.utilitarias.Calendario.hoje();
        return Uteis.getAnoData(data);

    }

    /**
     * Apresenta os dados do cliente ao clicar no cliente escolhido
     */
    public void irParaTelaCliente() {
        ResumoClientesBV obj = (ResumoClientesBV) context().getExternalContext().getRequestMap().get("resumoCliente");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(obj.getQuestionarioClienteVO().getCliente());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Monta os dados do  gráfico do relatório do tipo pizza
     */
    public void montarDataPizzaListaRespostasPergunta() {
        for (BVsRelatorioListasTO bvR : getbVsRelatorioTO().getListaBVsRelatorioListasTO()) {
            getbVsRelatorioTO().getDataSetPizza().setValue(bvR.getRespostaVO().getDescricaoRespota(), bvR.getPercentualRespondidoPorResposta());
        }
        getbVsRelatorioTO().setPizza(true);
        getbVsRelatorioTO().setBarra(false);
    }

    /**
     * Monta os dados do gráfico do relatório do tipo barra
     */
    public void montarDataBarraListaRespostasPergunta() {
        getbVsRelatorioTO().setPizza(false);
        getbVsRelatorioTO().setBarra(true);
        for (BVsRelatorioListasTO bvR : getbVsRelatorioTO().getListaBVsRelatorioListasTO()) {
            getbVsRelatorioTO().getDataSetBarra().setValue(bvR.getPercentualRespondidoPorResposta(), bvR.getRespostaVO().getDescricaoRespostaResumo(), "");
        }
    }

    public FiltrosConsultaRelatorioBVsTO getFiltrosConsulta() {
        if (filtrosConsulta == null) {
            filtrosConsulta = new FiltrosConsultaRelatorioBVsTO();
        }
        return filtrosConsulta;
    }

    public List<EmpresaVO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<EmpresaVO> empresas) {
        this.empresas = empresas;
    }

    public Boolean getMostrarConteudo() {
        return mostrarConteudo;
    }

    public void setMostrarConteudo(Boolean mostrarConteudo) {
        this.mostrarConteudo = mostrarConteudo;
    }

    public BVsRelatorioTO getbVsRelatorioTO() {
        return bVsRelatorioTO;
    }

    public void setbVsRelatorioTOs(BVsRelatorioTO bVsRelatorioTO) {
        this.bVsRelatorioTO = bVsRelatorioTO;
    }

    public List<ResumoClientesBV> getResumoClientesBVs() {
        return resumoClientesBVs;
    }

    public void setResumoClientesBVs(List<ResumoClientesBV> resumoClientesBVs) {
        this.resumoClientesBVs = resumoClientesBVs;
    }

    public List<PerguntaClienteVO> getListaPerguntasVO() {
        return listaPerguntasVO;
    }

    public void setListaPerguntasVO(List<PerguntaClienteVO> listaPerguntasVO) {
        this.listaPerguntasVO = listaPerguntasVO;
    }

    public boolean isMostrarCampoRespostaTextual() {
        return mostrarCampoRespostaTextual;
    }

    public void setMostrarCampoRespostaTextual(boolean mostrarCampoRespostaTextual) {
        this.mostrarCampoRespostaTextual = mostrarCampoRespostaTextual;
    }

    public boolean isRelatorioPesquisa() {
        return relatorioPesquisa;
    }

    public void setRelatorioPesquisa(boolean relatorioPesquisa) {
        this.relatorioPesquisa = relatorioPesquisa;
    }

    public void obterResultadoNPS() {

        for (BVsRelatorioListasTO bVsRelatorioListasTO : bVsRelatorioTO.getListaBVsRelatorioListasTO()) {
            String descricao = String.valueOf(bVsRelatorioListasTO.getRespostaVO().getDescricaoRespota());
            int valorResposta = Integer.parseInt(descricao);
            if (valorResposta < 7) {
                detratores = valorResposta;
                qtdDetratores += bVsRelatorioListasTO.getTotalRespondidoPorResposta();
            } else if (valorResposta < 9) {
                neutros = valorResposta;
                qtdNeutros += bVsRelatorioListasTO.getTotalRespondidoPorResposta();
            } else {
                promotores = valorResposta;
                qtdPromotores += bVsRelatorioListasTO.getTotalRespondidoPorResposta();
            }
            valorTotalRespondido = bVsRelatorioTO.getTotalGeral();
        }
        quantidadeRespostasNPS = new ArrayList<>();
        resultadoNPS = (float) (qtdPromotores - qtdDetratores) / valorTotalRespondido;
        resultadoNPS = resultadoNPS * 100;
        addClientesNPPS();
        if(valorTotalRespondido == 0 ){
            mensagemNPS = "Ainda não há respostas para essa pergunta.";
            resultadoNPSMostrar = "";
            return;
        }
        if(resultadoNPS < 0){
            //RUIM
            resultadoNPSPorcentagem = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(resultadoNPS) + "%";
            corResultadoNPS = "#CF2A27";
            mensagemNPS = "NPS entre -100 e -1 - Ruim: ter um NPS entre -1 e -100 é um grande problema para a empresa, pois a insatisfação do " +
                    "cliente se dissemina entre amigos e familiares. Tomar uma ação emergencial é extremamente importante para reverter esse quadro.";
            resultadoNPSMostrar = "Ruim";
        }else if(resultadoNPS < 50){
            //Razoável
            resultadoNPSPorcentagem =Uteis.arrendondarForcando2CadasDecimaisComVirgula(resultadoNPS) + "%";
            corResultadoNPS = "#FFD966";
            mensagemNPS = "NPS entre 0 e 49 - Razoável: o nível razoável exige grandes cuidados da empresa. Aqui o trabalho de " +
                    "imagem deve ser prioridade, já que as chances de modificar o NPS são maiores.";
            resultadoNPSMostrar = "Razoável";
        }else if(resultadoNPS < 75){
            //Muito Bom
            resultadoNPSPorcentagem = Uteis.arrendondarForcando2CadasDecimaisComVirgula(resultadoNPS) + "%";
            corResultadoNPS = "#2B78E4";
            mensagemNPS = "NPS entre 50 e 74 - Muito bom: extremamente difícil de ser alcançado, estar neste nível faz das empresas referências em " +
                    "seus mercados. Além de ser para poucos, se manter aqui é um grande desafio.";
            resultadoNPSMostrar = "Muito Bom";
        }else{
            //Excelente
            resultadoNPSPorcentagem =Uteis.arrendondarForcando2CadasDecimaisComVirgula(resultadoNPS) + "%";
            corResultadoNPS = "#009E0F";
            mensagemNPS = "NPS entre 75 e 100 - Excelente: extremamente difícil de ser alcançado, estar neste nível faz das empresas referências em " +
                    "seus mercados. Além de ser para poucos, se manter aqui é um grande desafio.";
            resultadoNPSMostrar = "Excelente";
        }
        valorTotalRespondidoApresentar = String.valueOf(valorTotalRespondido);
        valorTotalRespondido = 0;
        detratores = 0;
        qtdDetratores = 0;
        neutros = 0;
        qtdNeutros = 0;
        promotores = 0;
        qtdPromotores = 0;
    }

    private void addClientesNPPS() {
        ItemRelatorioTO pesquisaNPSItem = new ItemRelatorioTO();
        pesquisaNPSItem.setNomeCliente("Detratores: ");
        pesquisaNPSItem.setQuantidade(String.valueOf(qtdDetratores));
        quantidadeRespostasNPS.add(pesquisaNPSItem);

        ItemRelatorioTO pesquisaNPSItem2 = new ItemRelatorioTO();
        pesquisaNPSItem2.setNomeCliente("Neutros: ");
        pesquisaNPSItem2.setQuantidade(String.valueOf(qtdNeutros));
        quantidadeRespostasNPS.add(pesquisaNPSItem2);

        ItemRelatorioTO pesquisaNPSItem3 = new ItemRelatorioTO();
        pesquisaNPSItem3.setNomeCliente("Promotores: ");
        pesquisaNPSItem3.setQuantidade(String.valueOf(qtdPromotores));
        quantidadeRespostasNPS.add(pesquisaNPSItem3);

        ItemRelatorioTO pesquisaNPSItem4 = new ItemRelatorioTO();
        pesquisaNPSItem4.setNomeCliente("Promotores: ");
        pesquisaNPSItem4.setQuantidade(String.valueOf(qtdPromotores));
        quantidadeRespostasNPS.add(pesquisaNPSItem4);

    }

    public String getResultadoNPSPorcentagem() {
        return resultadoNPSPorcentagem;
    }


    public String getCorResultadoNPS() {
        return corResultadoNPS;
    }


    public String getMensagemNPS() {
        return mensagemNPS;
    }


    public String getResultadoNPSMostrar() {
        return resultadoNPSMostrar;
    }


    public ArrayList<ItemRelatorioTO> getQuantidadeRespostasNPS() {
        return quantidadeRespostasNPS;
    }

    public void setQuantidadeRespostasNPS(ArrayList<ItemRelatorioTO> quantidadeRespostasNPS) {
        this.quantidadeRespostasNPS = quantidadeRespostasNPS;
    }

    public String getValorTotalRespondidoApresentar() {
        return valorTotalRespondidoApresentar;
    }

    public void setValorTotalRespondidoApresentar(String valorTotalRespondidoApresentar) {
        this.valorTotalRespondidoApresentar = valorTotalRespondidoApresentar;
    }

    public boolean isMostrarNPS() {
        return mostrarNPS;
    }

    public void montarJSONGrafico() {
        try {
            Set<String> mapaCor = new HashSet<>();


            JSONArray jsonArray = new JSONArray();
            for (BVsRelatorioListasTO bvR : getbVsRelatorioTO().getListaBVsRelatorioListasTO()) {
                JSONObject json = new JSONObject();
                json.put("resp", bvR.getRespostaVO().getDescricaoRespostaResumo());
                json.put("qtd", bvR.getTotalRespondidoPorResposta());
                json.put("convertido", bvR.getTotalConvertidos());

//                String color = obterCor(mapaCor);
//                json.put("color", color);
                jsonArray.put(json);
            }
            getbVsRelatorioTO().setJsonGrafico(jsonArray.toString());
        } catch (Exception ex) {

        }
    }

    private String obterCor(Set<String> mapaCor) {
        Random rand = new Random();
        float r = rand.nextFloat();
        float g = rand.nextFloat();
        float b = rand.nextFloat();
        Color color = new Color(r, g, b);
        String hex = convertColorToHexadeimal(color);
        if (mapaCor.contains(hex)) {
            return obterCor(mapaCor);
        } else {
            mapaCor.add(hex);
            return hex;
        }
    }

    private String convertColorToHexadeimal(Color color) {
        String hex = Integer.toHexString(color.getRGB() & 0xffffff);
        if (hex.length() < 6) {
            if (hex.length() == 5)
                hex = "0" + hex;
            if (hex.length() == 4)
                hex = "00" + hex;
            if (hex.length() == 3)
                hex = "000" + hex;
        }
        hex = "#" + hex;
        return hex;
    }

    public String getRespostaAbrir() {
        return respostaAbrir;
    }

    public void setRespostaAbrir(String respostaAbrir) {
        this.respostaAbrir = respostaAbrir;
    }

    public void verListaResposta() throws Exception {
        String descricao = getRespostaAbrir();
        for (BVsRelatorioListasTO bvR : getbVsRelatorioTO().getListaBVsRelatorioListasTO()) {
            if (bvR.getRespostaVO().getDescricaoRespostaResumo().equals(descricao)) {
                descricao = bvR.getRespostaVO().getDescricaoRespota();
                break;
            }
        }
        if (!UteisValidacao.emptyString(getRespostaAbrir())) {
            setResumoClientesBVs(getFacade().getRelatorioBVs().consultarResumoClientes(filtrosConsulta.getCodigoEmpresa(), bVsRelatorioTO.getPerguntaVO().getDescricao(), descricao, filtrosConsulta));
        }
    }

    public List<ColaboradorVO> getConsultoresAtivos() {
        if (consultoresAtivos == null) {
            consultoresAtivos = new ArrayList<>();
        }
        return consultoresAtivos;
    }

    public void setConsultoresAtivos(List<ColaboradorVO> consultoresAtivos) {
        this.consultoresAtivos = consultoresAtivos;
    }

    public List<ColaboradorVO> getConsultoresInativos() {
        if (consultoresInativos == null) {
            consultoresInativos = new ArrayList<>();
        }
        return consultoresInativos;
    }

    public void setConsultoresInativos(List<ColaboradorVO> consultoresInativos) {
        this.consultoresInativos = consultoresInativos;
    }

    public void marcarTodosAtivos() {
        for (ColaboradorVO obj : getConsultoresAtivos()) {
            obj.setSelecionado(filtrosConsulta.isConsultoresAtivos());
        }
    }

    public void marcarTodosInativos() {
        for (ColaboradorVO obj : getConsultoresInativos()) {
            obj.setSelecionado(filtrosConsulta.isConsultoresInativos());
        }
    }

    public void verificarCheckTodos() {
        boolean todosAtivos = true;
        for (ColaboradorVO obj : getConsultoresAtivos()) {
            if (!obj.getSelecionado()) {
                todosAtivos = false;
                break;
            }
        }

        boolean todosInativos = true;
        for (ColaboradorVO obj : getConsultoresInativos()) {
            if (!obj.getSelecionado()) {
                todosInativos = false;
                break;
            }
        }

        filtrosConsulta.setConsultoresAtivos(todosAtivos);
        filtrosConsulta.setConsultoresInativos(todosInativos);
    }

    public String getItemEportar(){
        if(isRelatorioPesquisa()){
            return ItemExportacaoEnum.REL_PESQUISA.getId();
        }
        return ItemExportacaoEnum.REL_BV.getId();
    }
}

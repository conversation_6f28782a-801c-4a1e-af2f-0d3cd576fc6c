package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.ClientePorDuracaoVO;
import relatorio.negocio.comuns.basico.ResumoClientePorDuracaoVO;
import relatorio.negocio.jdbc.basico.ClientePorDuracaoRel;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ClientePorDuracaoRelControle extends SuperControleRelatorio {

    protected List<ClientePorDuracaoVO> listaClientePorDuracao;
    private List<ResumoClientePorDuracaoVO> listaApresentar;
    protected Integer totalizadorPessoa;
    protected Boolean popUp;
    private String filtros;
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;
    private boolean apresentarSituacao = false;

    public ClientePorDuracaoRelControle() {
        inicializarDados();
    }

    public void inicializarDados() {
        try {
            setListaClientePorDuracao(new ArrayList<ClientePorDuracaoVO>());
            setFiltroEmpresa(0);
            setPopUp(false);
            setApresentarSituacao(false);
            setMensagemID("msg_entre_prmrelatorio");
            setListaApresentar(new ArrayList<ResumoClientePorDuracaoVO>());
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
            setTotalizadorPessoa(0);
            montarListaSelectItemEmpresa();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String imprimir() {
        try {
            setFiltros("");
            setTotalizadorPessoa(0);
            getFacade().getClientePorDuracaoRel().validarDados();
            setListaApresentar(new ArrayList<ResumoClientePorDuracaoVO>());

            boolean buscarTodas = (!getClientePorDuracaoRel().getSituacaoAtestado() && !getClientePorDuracaoRel().getSituacaoCarencia()
                    && !getClientePorDuracaoRel().getSituacaoNormal() && !getClientePorDuracaoRel().getSituacaoTrancado()
                    && !getClientePorDuracaoRel().getSituacaoAvencer() && !getClientePorDuracaoRel().getSituacaoVencido());
            setApresentarSituacao(!buscarTodas);

            if (getClientePorDuracaoRel().getSituacaoAtestado() || buscarTodas) {// situacao atestado usar sql de contratoOperacao
                getClientePorDuracaoRel().setTotalizadorPessoa(0);
                getListaApresentar().addAll(getClientePorDuracaoRel().consultarDuracaoPorPeriodoSituacaoContratoAnaliticoDW("AT", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa()));
                setTotalizadorPessoa(getTotalizadorPessoa() + getClientePorDuracaoRel().getTotalizadorPessoa());
            }
            if (getClientePorDuracaoRel().getSituacaoCarencia() || buscarTodas) {
                getClientePorDuracaoRel().setTotalizadorPessoa(0);
                getListaApresentar().addAll(getClientePorDuracaoRel().consultarDuracaoPorPeriodoSituacaoContratoAnaliticoDW("CR", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa()));
                setTotalizadorPessoa(getTotalizadorPessoa() + getClientePorDuracaoRel().getTotalizadorPessoa());
            }
            if (getClientePorDuracaoRel().getSituacaoNormal() || buscarTodas) {// situacao ativa usar sql de contrato
                getClientePorDuracaoRel().setTotalizadorPessoa(0);
                getListaApresentar().addAll(getClientePorDuracaoRel().consultarDuracaoPorPeriodoSituacaoContratoAnaliticoDW("NO", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa()));
                setTotalizadorPessoa(getTotalizadorPessoa() + getClientePorDuracaoRel().getTotalizadorPessoa());
            }
            if (getClientePorDuracaoRel().getSituacaoTrancado() || buscarTodas) {
                getClientePorDuracaoRel().setTotalizadorPessoa(0);
                getListaApresentar().addAll(getClientePorDuracaoRel().consultarDuracaoPorPeriodoSituacaoContratoAnaliticoDW("TR", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa()));
                setTotalizadorPessoa(getTotalizadorPessoa() + getClientePorDuracaoRel().getTotalizadorPessoa());
            }
            if (getClientePorDuracaoRel().getSituacaoAvencer() || buscarTodas) {// situacao a vencer usar sql de historicoContrato utilizando as datas e between
                getClientePorDuracaoRel().setTotalizadorPessoa(0);
                getListaApresentar().addAll(getClientePorDuracaoRel().consultarDuracaoPorPeriodoSituacaoContratoAnaliticoDW("AV", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa()));
                setTotalizadorPessoa(getTotalizadorPessoa() + getClientePorDuracaoRel().getTotalizadorPessoa());
            }
            if (getClientePorDuracaoRel().getSituacaoVencido() || buscarTodas) {
                getClientePorDuracaoRel().setTotalizadorPessoa(0);
                getListaApresentar().addAll(getClientePorDuracaoRel().consultarDuracaoPorPeriodoSituacaoContratoAnaliticoDW("VE", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa()));
                setTotalizadorPessoa(getTotalizadorPessoa() + getClientePorDuracaoRel().getTotalizadorPessoa());
            }


            if (!UteisValidacao.emptyNumber(getFiltroEmpresa())) {
                setFiltros("<b>Empresa: </b>" + getNomeEmpresaSelecionada(getFiltroEmpresa()) + " </br>");
            }
            if (getClientePorDuracaoRel().getSituacaoNormal()) {
                setFiltros(getFiltros() + "<b>Dia: </b> " + Uteis.getDataAplicandoFormatacao(getClientePorDuracaoRel().getDataPeriodo(), "dd/MM/yyyy") + "</br>");
            }
            if (getClientePorDuracaoRel().getSituacaoNormal()) {
                setFiltros(getFiltros() + "<b>Situação: </b>Normal </br>");
            }
            if (getClientePorDuracaoRel().getSituacaoAtestado()) {
                setFiltros(getFiltros() + "<b>Situação: </b>Atestado </br>");
            }
            if (getClientePorDuracaoRel().getSituacaoVencido()) {
                setFiltros(getFiltros() + "<b>Situação: </b>Vencido </br>");
            }
            if (getClientePorDuracaoRel().getSituacaoAvencer()) {
                setFiltros(getFiltros() + "<b>Situação: </b>A vencer </br>");
            }
            if (getClientePorDuracaoRel().getSituacaoCarencia()) {
                setFiltros(getFiltros() + "<b>Situação: </b> Férias </br>");
            }
            if (getClientePorDuracaoRel().getSituacaoTrancado()) {
                setFiltros(getFiltros() + "<b>Situação: </b> Trancado </br>");
            }
            if(buscarTodas) {
                setFiltros(getFiltros() + "<b>Situação: </b> Todas </br>");
            }
            Ordenacao.ordenarLista(getListaApresentar(), "numeroMeses");

            Map<Integer, EmpresaVO> mapaEmpresas = getFacade().getEmpresa().obterMapaEmpresas(Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            for(ResumoClientePorDuracaoVO obj: getListaApresentar()){
                if (UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
                    obj.getEmpresaVO().setNome("TODAS");
                } else {
                    obj.setEmpresaVO(mapaEmpresas.get(obj.getEmpresaVO().getCodigo()));
                }
                obj.setPercentual(Uteis.arrendondarForcando2CadasDecimaisComVirgula((obj.getQuantidade()*100.0)/totalizadorPessoa));
            }

            if(buscarTodas) {
                agruparDuracaoEmpresa();
            }

            setMensagem("Dados consultados");
            setPopUp(true);
            return "relatorio";
        } catch (ConsistirException ex) {
            setMensagemDetalhada("Uma empresa deve ser selecionada !");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setPopUp(false);
            return "";
        }
    }

    private void agruparDuracaoEmpresa() {
        List<ResumoClientePorDuracaoVO> listaFinal = new ArrayList<ResumoClientePorDuracaoVO>();


        for(ResumoClientePorDuracaoVO obj: getListaApresentar()){
            ResumoClientePorDuracaoVO novo = null;

            for (ResumoClientePorDuracaoVO lista : listaFinal) {
                if (lista.getEmpresaVO().getCodigo().equals(obj.getEmpresaVO().getCodigo()) &&
                        lista.getNumeroMeses().equals(obj.getNumeroMeses()) ) {
                    novo = lista;
                    novo.setQuantidade(novo.getQuantidade() + obj.getQuantidade());
                    novo.setPercentual(Uteis.arrendondarForcando2CadasDecimaisComVirgula((novo.getQuantidade()*100.0)/totalizadorPessoa));
                }
            }
            if (novo == null) {
                novo = obj;
                listaFinal.add(novo);
            }

        }

//        Map<Integer, List<ResumoClientePorDuracaoVO>> mapaEmpresa = new HashMap<Integer, List<ResumoClientePorDuracaoVO>>();
//        for(ResumoClientePorDuracaoVO obj: getListaApresentar()){
//            List<ResumoClientePorDuracaoVO> lista = mapaEmpresa.get(obj.getEmpresaVO().getCodigo());
//            if (lista == null) {
//                lista = new ArrayList<ResumoClientePorDuracaoVO>();
//            }
//            lista.add(obj);
//            mapaEmpresa.put(obj.getEmpresaVO().getCodigo(), lista);
//        }
//
//        List<Integer> listaEmpresas = (List<Integer>) mapaEmpresa.keySet();
//        for (Integer empresa : listaEmpresas) {
//            List<ResumoClientePorDuracaoVO> lista = mapaEmpresa.get(empresa);
//            if (UteisValidacao.emptyList(lista)){
//                continue;
//            }
//            ResumoClientePorDuracaoVO novo = new ResumoClientePorDuracaoVO();
//            novo.getEmpresaVO().setCodigo(empresa);
//            for (ResumoClientePorDuracaoVO obj : lista) {
//                if (obj.getNumeroMeses().intValue() == duracao.intValue()){
//                    novo.setQuantidade(novo.getQuantidade() + obj.getQuantidade());
//                }
//            }
//            novo.setPercentual(Uteis.arrendondarForcando2CadasDecimaisComVirgula((novo.getQuantidade()*100.0)/totalizadorPessoa));
//            listaFinal.add(novo);
//
//
//        }



        setListaApresentar(listaFinal);

    }

    public void montarDadosPessoas() {
        ResumoClientePorDuracaoVO obj = (ResumoClientePorDuracaoVO) context().getExternalContext().getRequestMap().get("lista");

        try {
            setListaClientePorDuracao(new ArrayList<ClientePorDuracaoVO>());
            getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("NO", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa(), obj));
            getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("AV", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa(), obj));
            getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("AT", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa(), obj));
            getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("CR", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa(), obj));
            getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("VE", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa(), obj));
            getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("TR", getFiltroEmpresa(), !getClientePorDuracaoRel().getSemBolsa(), obj));
        } catch (Exception ex) {
            setMensagemDetalhada(ex.toString());
        }
    }

    public void imprimirTudo() {
        setListaClientePorDuracao(new ArrayList<ClientePorDuracaoVO>());
        for (ResumoClientePorDuracaoVO obj : getListaApresentar()) {
            try {
                if (obj.getSituacao().equals("NO")) {
                    getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("NO", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
                }
                if (obj.getSituacao().equals("AV")) {
                    getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("AV", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
                }
                if (obj.getSituacao().equals("AT")) {
                    getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("AT", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
                }
                if (obj.getSituacao().equals("CR")) {
                    getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("CR", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
                }
                if (obj.getSituacao().equals("VE")) {
                    getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("VE", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
                }
                if (obj.getSituacao().equals("TR")) {
                    getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("TR", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
                }
            } catch (Exception ex) {
                setMensagemDetalhada(ex.toString());
            }
        }
    }

    public void montarDadosPessoasSituacao() {
        ResumoClientePorDuracaoVO obj = (ResumoClientePorDuracaoVO) context().getExternalContext().getRequestMap().get("lista");
        setListaClientePorDuracao(new ArrayList<ClientePorDuracaoVO>());
        try {

            if (obj.getSituacao().equals("AT")) {
                getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("AT", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
            }
            if (obj.getSituacao().equals("NO")) {
                getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("NO", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
            }
            if (obj.getSituacao().equals("AV")) {
                getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("AV", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
            }
            if (obj.getSituacao().equals("CR")) {
                getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("CR", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
            }
            if (obj.getSituacao().equals("TR")) {
                getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("TR", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
            }
            if (obj.getSituacao().equals("VE")) {
                getListaClientePorDuracao().addAll(getClientePorDuracaoRel().consultarPessoasParametrizado("VE", obj.getEmpresaVO().getCodigo(), !getClientePorDuracaoRel().getSemBolsa(), obj));
            }

        } catch (Exception ex) {
            setMensagemDetalhada(ex.toString());
        }
    }

    public String voltar() {
        setListaApresentar(new ArrayList<ResumoClientePorDuracaoVO>());
        setListaClientePorDuracao(new ArrayList<ClientePorDuracaoVO>());
        return "voltar";
    }

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<EmpresaVO> consultarEmpresaPorNome(String nomePrm) throws Exception {
        return getFacade().getEmpresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public ClientePorDuracaoRel getClientePorDuracaoRel() throws Exception {
        return getFacade().getClientePorDuracaoRel();
    }

    public List<ClientePorDuracaoVO> getListaClientePorDuracao() {
        return listaClientePorDuracao;
    }

    public void setListaClientePorDuracao(List<ClientePorDuracaoVO> listaClientePorDuracao) {
        this.listaClientePorDuracao = listaClientePorDuracao;
    }

    public Boolean getPopUp() {
        return popUp;
    }

    public void setPopUp(Boolean popUp) {
        this.popUp = popUp;
    }

    public Integer getTotalizadorPessoa() {
        return totalizadorPessoa;
    }

    public void setTotalizadorPessoa(Integer totalizadorPessoa) {
        this.totalizadorPessoa = totalizadorPessoa;
    }

    public void novo() {
        return;
    }

    public void irParaTelaCliente() {
        ClientePorDuracaoVO obj = (ClientePorDuracaoVO) context().getExternalContext().getRequestMap().get("lista");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public List<ResumoClientePorDuracaoVO> getListaApresentar() {
        if (listaApresentar == null) {
            listaApresentar = new ArrayList<ResumoClientePorDuracaoVO>();
        }
        return listaApresentar;
    }

    public void setListaApresentar(List<ResumoClientePorDuracaoVO> listaApresentar) {
        this.listaApresentar = listaApresentar;
    }

    public String getAtributosImpressao() {
        if (isApresentarSituacao()) {
            return "numero_Apresentar=Duração,situacao_Apresentar=Situação,quantidade=Quantidade,percentual_Apresentar=Percentual,empresaVO.nome=Empresa";
        } else {
            return "numero_Apresentar=Duração,quantidade=Quantidade,percentual_Apresentar=Percentual,empresaVO.nome=Empresa";
        }

    }

    public boolean isApresentarSituacao() {
        return apresentarSituacao;
    }

    public void setApresentarSituacao(boolean apresentarSituacao) {
        this.apresentarSituacao = apresentarSituacao;
    }
}

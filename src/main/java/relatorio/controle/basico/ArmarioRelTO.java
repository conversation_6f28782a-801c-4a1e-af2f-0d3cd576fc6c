package relatorio.controle.basico;

import com.exadel.fiji.io.converter.DateConverter;
import negocio.armario.GrupoArmarioEnum;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * Created by <PERSON> on 19/10/2015.
 */
public class ArmarioRelTO extends SuperTO{

    private String nome;
    private String matricula;
    private String telefone;
    private String email;
    private String numeroArmario;
    private String tamanho;
    private String tipo;
    private String planoLocacao;
    private Date dataInicio;
    private String vigencia;
    private String contratoAssinado;
    private Date dataFim;
    private Date dataLancamento;
    private String situacaoAluno;
    private Date dataInicioContrato;
    private Date dataFinalContrato;
    private String planoContrato;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNumeroArmario() {
        return numeroArmario;
    }

    public void setNumeroArmario(String numeroArmario) {
        this.numeroArmario = numeroArmario;
    }

    public String getTamanho() {
        return tamanho;
    }

    public void setTamanho(String tamanho) {
        this.tamanho = tamanho;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getPlanoLocacao() {
        return planoLocacao;
    }

    public void setPlanoLocacao(String planoLocacao) {
        this.planoLocacao = planoLocacao;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getVigencia() {
        return vigencia;
    }

    public void setVigencia(String vigencia) {
        this.vigencia = vigencia;
    }
    public String getTipoArmarioApresentar(){
        return GrupoArmarioEnum.obterPorCodigo(tipo).getLabel();
    }
    public String getDataInicioApresentar(){
        return Uteis.getData(dataInicio);
    }
    public String getDataFimApresentar(){
        return Uteis.getData(dataFim);
    }
    public String getIconContratoAssinado(){

        return getContratoAssinado().equals("true") ? "fa-icon-check" : "fa-icon-check-empty";
    }
    public String getContratoAssinado_Apresentar(){
        return getContratoAssinado().equals("true") ? "Sim" : "Não";
    }
    public String getLancamentoApresentar(){
        return Uteis.getData(dataLancamento);
    }
    public String getContratoAssinado() {
        return contratoAssinado;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public void setContratoAssinado(String contratoAssinado) {
        this.contratoAssinado = contratoAssinado;
    }

    public String getSituacaoAluno() {
        if (situacaoAluno == null) {
            situacaoAluno = "";
        }
        return situacaoAluno;
    }

    public void setSituacaoAluno(String situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public Date getDataInicioContrato() {
        return dataInicioContrato;
    }

    public void setDataInicioContrato(Date dataInicioContrato) {
        this.dataInicioContrato = dataInicioContrato;
    }

    public String getDataInicioContratoApresentar() {
        return Uteis.getData(getDataInicioContrato());
    }

    public Date getDataFinalContrato() {
        return dataFinalContrato;
    }

    public void setDataFinalContrato(Date dataFinalContrato) {
        this.dataFinalContrato = dataFinalContrato;
    }

    public String getDataFinalContratoApresentar() {
        return Uteis.getData(getDataFinalContrato());
    }

    public String getPlanoContrato() {
        if (planoContrato == null) {
            planoContrato = "";
        }
        return planoContrato;
    }

    public void setPlanoContrato(String planoContrato) {
        this.planoContrato = planoContrato;
    }
}

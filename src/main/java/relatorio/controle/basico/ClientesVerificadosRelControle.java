/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.basico;

import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import negocio.comuns.basico.ClienteSimplificadoTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class ClientesVerificadosRelControle extends BIControle {

    protected EmpresaVO empresaVO;
    protected Date dataInicio;
    protected Date dataFim;

    private int qtdVerificado = 0;
    private int qtdNaoVerificado = 0;
    private int totalVerificar = 0;

    private Date primeiraVerificacao;
    private Date ultimaVerificacao;

    private double indiceVerificacao = 0.0;
    private double mediaVerificao = 0.0;
    private boolean mostrarPaginacao = false;

    private List<ClienteSimplificadoTO> listaApresentar;
    private String tituloLista = "";
    private String itemExportacao = "";

    public ClientesVerificadosRelControle() throws Exception {
        novo();
    }

    public EmpresaVO getEmpresaFiltroBI() {
        return (EmpresaVO) JSFUtilities.getManagedBean("BIControle.empresaFiltro");
    }

    public List getListaSelectItemEmpresa() {
        return (List) JSFUtilities.getManagedBean("BIControle.listaSelectItemEmpresa");
    }

    public boolean isConsultarPorTodosColaboradores() {
        return (Boolean) JSFUtilities.getManagedBean("BIControle.consultarPorTodosColaboradores");
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public int getQtdVerificado() {
        return qtdVerificado;
    }

    public void setQtdVerificado(int qtdVerificado) {
        this.qtdVerificado = qtdVerificado;
    }

    public int getQtdNaoVerificado() {
        return qtdNaoVerificado;
    }

    public void setQtdNaoVerificado(int qtdNaoVerificado) {
        this.qtdNaoVerificado = qtdNaoVerificado;
    }

    public int getTotalVerificar() {
        return totalVerificar;
    }

    public void setTotalVerificar(int totalVerificar) {
        this.totalVerificar = totalVerificar;
    }

    public double getIndiceVerificacao() {
        return indiceVerificacao;
    }

    public void setIndiceVerificacao(double indiceVerificacao) {
        this.indiceVerificacao = indiceVerificacao;
    }

    public double getMediaVerificao() {
        return mediaVerificao;
    }

    public void setMediaVerificao(double mediaVerificao) {
        this.mediaVerificao = mediaVerificao;
    }

    public String getMediaVerificacao_HintApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getMediaVerificao()) + " verificações por dia";
    }

    public Date getPrimeiraVerificacao() {
        return primeiraVerificacao;
    }

    public void setPrimeiraVerificacao(Date primeiraVerificacao) {
        this.primeiraVerificacao = primeiraVerificacao;
    }

    public String getPrimeiraVerificacao_Apresentar() {
        if (getPrimeiraVerificacao() != null) {
            return Uteis.getData(getPrimeiraVerificacao());
        }
        return " - ";
    }

    public String getPrimeiraVerificacao_HintApresentar() {
        if (getPrimeiraVerificacao() != null) {
            return "Primeira verificação em: " + Uteis.getDataComHora(getPrimeiraVerificacao());
        }
        return " Não há verificações ";
    }

    public Date getUltimaVerificacao() {
        return ultimaVerificacao;
    }

    public void setUltimaVerificacao(Date ultimaVerificacao) {
        this.ultimaVerificacao = ultimaVerificacao;
    }

    public String getUltimaVerificacao_Apresentar() {
        if (getUltimaVerificacao() != null) {
            return Uteis.getData(getUltimaVerificacao());
        }
        return " - ";
    }

    public String getUltimaVerificacao_HintApresentar() {
        if (getUltimaVerificacao() != null) {
            return "Última verificação em: " + Uteis.getDataComHora(getUltimaVerificacao());
        }
        return " Não há verificações ";
    }

    public boolean isMostrarPaginacao() {
        return mostrarPaginacao;
    }

    public void setMostrarPaginacao(boolean mostrarPaginacao) {
        this.mostrarPaginacao = mostrarPaginacao;
    }

    public List<ClienteSimplificadoTO> getListaApresentar() {
        if (listaApresentar == null) {
            listaApresentar = new ArrayList<ClienteSimplificadoTO>();
        }
        return listaApresentar;
    }

    public void setListaApresentar(List<ClienteSimplificadoTO> listaApresentar) {
        this.listaApresentar = listaApresentar;
    }

    public String getDataBase_Apresentar() {
        DateFormat dfmt = new SimpleDateFormat("MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataFim());
        return dfmt.format(cal.getTime());
    }

    public String getTituloLista() {
        return tituloLista;
    }

    public void setTituloLista(String tituloLista) {
        this.tituloLista = tituloLista;
    }

    public String getNumeroPaginacao() {
        if (isMostrarPaginacao()) {
            return "10";
        } else {
            return "";
        }
    }

    public void novo() throws Exception {
        try {
            setDataFim(Calendario.hoje());
            setDataInicio(Uteis.obterPrimeiroDiaMes(Calendario.hoje()));
            limparMensagens();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void limparMensagens() {
        setMensagemDetalhada("", "");
        setMensagem("");
        setMensagemID("");
    }

    public void irParaTelaCliente() throws Exception {
        ClienteSimplificadoTO obj = ((ClienteSimplificadoTO) context().getExternalContext().getRequestMap().get("resumoPessoa"));
        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setCodigo(obj.getCodigo());
        irParaTelaCliente(clienteVO);
    }


    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        empresaVO = null;
        dataInicio = Calendario.hoje();
        dataFim = Calendario.hoje();
    }

    private void limpar() {
        empresaVO = null;
        dataInicio = Calendario.hoje();
        dataFim = Calendario.hoje();
        qtdVerificado = 0;
        qtdNaoVerificado = 0;
        totalVerificar = 0;
    }

    public void consultarIndiceVerificacaoTela() throws Exception {
        gravarHistoricoAcessoBI(BIEnum.CLIENTES_VERIFICADOS);
        consultarIndiceVerificacao();
    }

    public void consultarIndiceVerificacao() throws Exception {
        try {
            processarBIClientesVerificados(false);
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
            montarErro(ex.getMessage());
            throw ex;
        }
    }

    public void atualizarIndiceVerificacao() {
        try {
            gravarHistoricoAcessoBI(BIEnum.CLIENTES_VERIFICADOS);
            processarBIClientesVerificados(true);
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
            montarErro(ex.getMessage());
        }
    }

    private void validarEmpresa() throws Exception {
        if (getEmpresaFiltroBI().getCodigo() == 0 && !isPermiteConsultarTodasEmpresas()) {
            throw new Exception("O campo empresa deve ser informado");
        }
    }

    private void processarBIClientesVerificados(boolean atualizarAgora) throws Exception {
        try {
            validarEmpresa();

            FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(), BIEnum.CLIENTES_VERIFICADOS, getFiltroDTO(atualizarAgora));
            JSONObject dados = new JSONObject();
            if (filtroDTO.getJsonDados() != null) {
                dados = new JSONObject(filtroDTO.getJsonDados());
            }

            setDataInicio(Uteis.obterPrimeiroDiaMes(getDataBaseFiltro()));
            setDataFim(getDataBaseFiltro());

            limpar();
            setTotalVerificar(dados.optInt("totalVerificar"));
            setQtdVerificado(dados.optInt("qtdVerificado"));
            setQtdNaoVerificado(dados.optInt("qtdNaoVerificado"));
            if (dados.opt("indiceVerificacao") == null) {
                setIndiceVerificacao(0.0);
            } else {
                setIndiceVerificacao(dados.optDouble("indiceVerificacao"));
            }
            if (dados.optLong("dataPrimeiraVerificacao") > 0) {
                setPrimeiraVerificacao(new Date(dados.getLong("dataPrimeiraVerificacao")));
            }
            if (dados.optLong("dataUltimaVerificacao") > 0) {
                setUltimaVerificacao(new Date(dados.getLong("dataUltimaVerificacao")));
            }

            long nrDias = Uteis.nrDiasEntreDatas(getPrimeiraVerificacao(), getUltimaVerificacao()) + 1;
            setMediaVerificao(0.0);
            if (nrDias > 0 && getPrimeiraVerificacao() != null && getUltimaVerificacao() != null) {
                setMediaVerificao(Uteis.arredondarForcando2CasasDecimais(getQtdVerificado() / nrDias));
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void processarBIClientesVerificadosANTIGO() throws Exception {
        if (getEmpresaFiltroBI().getCodigo() == 0 && !isPermiteConsultarTodasEmpresas()) {
            throw new Exception("O campo empresa deve ser informado");
        }
        setDataInicio(Uteis.obterPrimeiroDiaMes(getDataBaseFiltro()));
        setDataFim(getDataBaseFiltro());

        limpar();
        //Verificar se irá filtrar por consultor;
        int clientesVerificar = getFacade().getCliente().contarClientesParaVerificar(getEmpresaFiltroBI().getCodigo(), null);
        setTotalVerificar(clientesVerificar);

        int clientesVerificados = getFacade().getCliente().contarClientesParaVerificar(getEmpresaFiltroBI().getCodigo(), true);
        setQtdVerificado(clientesVerificados);

        int clientesNaoVerificados = getFacade().getCliente().contarClientesParaVerificar(getEmpresaFiltroBI().getCodigo(), false);
        setQtdNaoVerificado(clientesNaoVerificados);

        if (getTotalVerificar() != 0) {
            setIndiceVerificacao(((double) getQtdVerificado() / getTotalVerificar()) * 100);
        }

        Date dataPrimeiraVerificacao = getFacade().getCliente().consultarPrimeiraVerificacao(getEmpresaFiltroBI().getCodigo());
        setPrimeiraVerificacao(dataPrimeiraVerificacao);

        Date dataUltimaVerificacao = getFacade().getCliente().consultarUltimaVerificacao(getEmpresaFiltroBI().getCodigo());
        setUltimaVerificacao(dataUltimaVerificacao);

        long nrDias = Uteis.nrDiasEntreDatas(dataPrimeiraVerificacao, dataUltimaVerificacao) + 1;
        if (nrDias > 0) {
            setMediaVerificao(Uteis.arredondarForcando2CasasDecimais(clientesVerificados / nrDias));
        }
    }

    public void mostrarTotalVerificar() {
        try {
            limparMensagens();
            setListaApresentar(new ArrayList<ClienteSimplificadoTO>());
            setListaApresentar(getFacade().getCliente().consultarClientesParaVerificar(getEmpresaFiltroBI().getCodigo(), null));
            setTituloLista("Clientes marcados para verificar");
            setItemExportacao(ItemExportacaoEnum.BI_VERIFICACAO_TOTAL.getId());
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    public void mostrarVerificados() {
        try {
            limparMensagens();
            setListaApresentar(new ArrayList<ClienteSimplificadoTO>());
            setListaApresentar(getFacade().getCliente().consultarClientesParaVerificar(getEmpresaFiltroBI().getCodigo(), true));
            setTituloLista("Clientes verificados");
            setItemExportacao(ItemExportacaoEnum.BI_VERIFICACAO_VERIFICADOS.getId());
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    public void mostrarNaoVerificados() {
        try {
            limparMensagens();
            setListaApresentar(new ArrayList<ClienteSimplificadoTO>());
            setListaApresentar(getFacade().getCliente().consultarClientesParaVerificar(getEmpresaFiltroBI().getCodigo(), false));
            setTituloLista("Clientes que não foram verificados");
            setItemExportacao(ItemExportacaoEnum.BI_VERIFICACAO_NAO_VERIFICADOS.getId());
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    private FiltroDTO getFiltroDTO(boolean atualizarAgora) {
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.CLIENTES_VERIFICADOS.name());
        JSONObject filtros = new JSONObject();
        filtros.put("atualizarAgora", atualizarAgora);
        filtros.put("data", Calendario.getDataComHoraZerada(Calendario.hoje()).getTime());

        if (UteisValidacao.notEmptyNumber(getEmpresaFiltroBI().getCodigo())) {
            filtros.put("empresa", getEmpresaFiltroBI().getCodigo());
        }

        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    public String getItemExportacao() {
        return itemExportacao;
    }

    public void setItemExportacao(String itemExportacao) {
        this.itemExportacao = itemExportacao;
    }
}

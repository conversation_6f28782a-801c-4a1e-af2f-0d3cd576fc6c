package relatorio.controle.basico;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;

import javax.faces.model.SelectItem;
import java.io.File;
import java.util.*;

/**
 * Created by fabio on 23/08/2016.
 */
public class SaldoCreditoRelControle extends SuperControleRelatorio{
    private boolean situacaoAtivo = Boolean.FALSE;
    private boolean situacaoInativo = Boolean.FALSE;
    private Integer creditosDe = 0;
    private Integer creditosAte = 999;
    private boolean situacaoVisitante = Boolean.FALSE;
    private List<SituacaoClienteSinteticoDWVO> listaClienteCredito;
    private boolean somenteSaldoCreditoMaiorQueZero = Boolean.FALSE;
    private int totalizador;
    private String filtros;
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;


    public SaldoCreditoRelControle() {
        inicializarDados();
    }

    public String getRelatorioExcelAtributos(){
        return "matricula=Matrícula,nomeCliente=Nome,saldoCreditoTreino=Saldo de Créditos,dataVigenciaAte_Apresentar=Vencimento Contrato,primeiroTelefone=Telefone,nomeEmpresa=Empresa";
    }

    public void inicializarDados() {
        try {
            setSituacaoAtivo(false);
            setSituacaoInativo(false);
            setSituacaoVisitante(false);
            setListaClienteCredito(new ArrayList<SituacaoClienteSinteticoDWVO>());
            setSomenteSaldoCreditoMaiorQueZero(false);
            setEmpresa(new EmpresaVO());
            setFiltroEmpresa(0);
            setMensagemID("msg_entre_prmrelatorio");
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
            setTotalizador(0);
            montarListaSelectItemEmpresa();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void validarDados() throws Exception {
        if (!permissaoConsultaTodasEmpresas && UteisValidacao.emptyNumber(getFiltroEmpresa())) {
            throw new Exception("Uma Empresa deve ser selecionada !");
        }
    }

    public String imprimir() {
        try {
            validarDados();
            setListaClienteCredito(new ArrayList<SituacaoClienteSinteticoDWVO>());
            setTotalizador(0);
            List<String> listaSituacao;
            // preenche as situações marcadas
            listaSituacao = preencheListaSituacaoCliente();
            setListaClienteCredito(getFacade().getSituacaoClienteSinteticoDW().consultarClienteCreditoPorSituacao(listaSituacao, somenteSaldoCreditoMaiorQueZero,getCreditosDe(),getCreditosAte(), getFiltroEmpresa()));
            setTotalizador(getListaClienteCredito().size());
            setFiltros(getDescricaoFiltros());
            setMensagem("Dados consultados");
            setMensagemDetalhada("");
            return "relatorio";

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e);
            return "";
        }
    }

    public String getNomeRelatorio(){
        return  "Saldo de Creditos";
    }

    public void imprimirRelatorio() {
        try {
            setRelatorio("sim");
            setListaRelatorio(getListaClienteCredito());
            apresentarRelatorioObjetos(prepareParams());
            setMensagemDetalhada("");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private Map<String, Object> prepareParams() throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("nomeRelatorio", getNomeRelatorio());
        params.put("tituloRelatorio", "Relatório de Créditos");
        params.put("nomeEmpresa", getNomeEmpresaSelecionada(getFiltroEmpresa()));
        params.put("tipoRelatorio", getTipoRelatorio());
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("usuario", getUsuarioLogado().getNome());
        params.put("listaObjetos", getListaRelatorio());
        params.put("filtros", getDescricaoFiltrosParaPDF());
        return params;
    }

    public List<String> preencheListaSituacaoCliente() {
        List listaSituacao = new ArrayList();
        if (isSituacaoAtivo()) {
            listaSituacao.add("AT");
        }
        if (isSituacaoInativo()) {
            listaSituacao.add("IN");
        }
        if (isSituacaoVisitante()) {
            listaSituacao.add("VI");
        }
        // se nenhum marcado considera todos marcados
        if (!isSituacaoAtivo() && !isSituacaoInativo() && !isSituacaoVisitante()) {
            listaSituacao.add("AT");
            listaSituacao.add("IN");
            listaSituacao.add("VI");
        }
        return listaSituacao;
    }

    public List<String> preencheListaSituacaoFiltros() {
        List listaSituacao = new ArrayList();
        if (isSituacaoAtivo()) {
            listaSituacao.add("Ativo");
        }
        if (isSituacaoInativo()) {
            listaSituacao.add("Inativo");
        }
        if (isSituacaoVisitante()) {
            listaSituacao.add("Visitante");
        }
        return listaSituacao;
    }

    public String voltar() {
        setListaClienteCredito(new ArrayList<SituacaoClienteSinteticoDWVO>());
        return "voltar";
    }

    private String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "SaldoCreditos.jrxml");
    }

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isSituacaoAtivo() {
        return situacaoAtivo;
    }

    public void setSituacaoAtivo(boolean situacaoAtivo) {
        this.situacaoAtivo = situacaoAtivo;
    }

    public boolean isSituacaoInativo() {
        return situacaoInativo;
    }

    public void setSituacaoInativo(boolean situacaoInativo) {
        this.situacaoInativo = situacaoInativo;
    }

    public boolean isSituacaoVisitante() {
        return situacaoVisitante;
    }

    public void setSituacaoVisitante(boolean situacaoVisitante) {
        this.situacaoVisitante = situacaoVisitante;
    }

    public List<SituacaoClienteSinteticoDWVO> getListaClienteCredito() {
        return listaClienteCredito;
    }

    public void setListaClienteCredito(List<SituacaoClienteSinteticoDWVO> listaClienteCredito) {
        this.listaClienteCredito = listaClienteCredito;
    }

    public boolean getSomenteSaldoCreditoMaiorQueZero() {
        return somenteSaldoCreditoMaiorQueZero;
    }

    public void setSomenteSaldoCreditoMaiorQueZero(boolean somenteSaldoCreditoMaiorQueZero) {
        this.somenteSaldoCreditoMaiorQueZero = somenteSaldoCreditoMaiorQueZero;
    }

    public int getTotalizador() {
        return totalizador;
    }

    public void setTotalizador(int totalizador) {
        this.totalizador = totalizador;
    }

    private String getDescricaoFiltros() throws Exception {
        String aux = "";
        aux += "<b>Empresa: </b> " + getNomeEmpresaSelecionada(getFiltroEmpresa()) + "</br>";

        List<String> lista = preencheListaSituacaoFiltros();
        if (lista.size() > 0) {
            for (String situacao : lista) {
                aux += "<b>Situação: </b> " + situacao + "</br>";
            }
        }
        return aux;
    }

    private String getDescricaoFiltrosParaPDF() throws Exception {
        String aux = "";
        aux += "Empresa: " + getNomeEmpresaSelecionada(getFiltroEmpresa()) + "\n";

        List<String> lista = preencheListaSituacaoFiltros();
        if (lista.size() > 0) {
            aux += " Situação: ";
            for (int i = 0; i <= lista.size() -1; i++) {
                aux += lista.get(i);
                if(lista.size() > 1){
                    if ( i == (lista.size() -1)) {
                        break;
                    }
                    aux += ", ";
                }
            }
            aux += "\n";
        }

        if(getSomenteSaldoCreditoMaiorQueZero()) {
            aux += "Somente crédito maior que zero: Sim";
        } else {
            aux += "Somente crédito maior que zero: Não";
        }

        return aux;
    }

    public Integer getCreditosDe() {
        return creditosDe;
    }

    public void setCreditosDe(Integer creditosDe) {
        this.creditosDe = creditosDe;
    }

    public Integer getCreditosAte() {
        return creditosAte;
    }

    public void setCreditosAte(Integer creditosAte) {
        this.creditosAte = creditosAte;
    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }
}

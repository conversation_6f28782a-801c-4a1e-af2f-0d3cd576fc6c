/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.basico;

import negocio.comuns.acesso.enumerador.TipoLiberacaoEnum;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

/**
 * Classe TO que contém os dados mostrados no relatorio de liberacao de acesso
 * <AUTHOR>
 */
public class FechamentoAcessoRelTO extends SuperRelatorio {

    private static final long serialVersionUID = 1406547653431582704L;

    public FechamentoAcessoRelTO() throws Exception {
    }
    private TipoLiberacaoEnum tipoLiberacaoEnum;
    private Double percentualAcessos;
    private Integer totalAcesso;
    private Integer jaJustificado;
    private Integer faltaJustificar;

    /**
     * @return the tipoLiberacaoEnum
     */
    public TipoLiberacaoEnum getTipoLiberacaoEnum() {
        return tipoLiberacaoEnum;
    }

    /**
     * @param tipoLiberacaoEnum the tipoLiberacaoEnum to set
     */
    public void setTipoLiberacaoEnum(TipoLiberacaoEnum tipoLiberacaoEnum) {
        this.tipoLiberacaoEnum = tipoLiberacaoEnum;
    }

    /**
     * @return the totalAcesso
     */
    public Integer getTotalAcesso() {
        if (totalAcesso == null) {
            return 0;
        }
        return totalAcesso;
    }

    /**
     * @param totalAcesso the totalAcesso to set
     */
    public void setTotalAcesso(Integer totalAcesso) {
        this.totalAcesso = totalAcesso;
    }

    /**
     * @return the jaJustificado
     */
    public Integer getJaJustificado() {
        if (jaJustificado == null) {
            return 0;
        }
        return jaJustificado;
    }

    /**
     * @param jaJustificado the jaJustificado to set
     */
    public void setJaJustificado(Integer jaJustificado) {
        this.jaJustificado = jaJustificado;
    }

    /**
     * @return the faltaJustificar
     */
    public Integer getFaltaJustificar() {
        if (faltaJustificar == null) {
            return 0;
        }
        return faltaJustificar;
    }

    /**
     * @param faltaJustificar the faltaJustificar to set
     */
    public void setFaltaJustificar(Integer faltaJustificar) {
        this.faltaJustificar = faltaJustificar;
    }

    /**
     * @return the percentualAcessos
     */
    public Double getPercentualAcessos() {
        if (percentualAcessos == null) {
            percentualAcessos = 0.0;
        }
        return percentualAcessos;
    }

    /**
     * @param percentualAcessos the percentualAcessos to set
     */
    public void setPercentualAcessos(Double percentualAcessos) {
        this.percentualAcessos = percentualAcessos;
    }
}

package relatorio.controle.basico;

/**
 *
 * <AUTHOR>
 */

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.acesso.AcessoClienteTO;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.acesso.AcessoColaboradorVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.GrupoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.jdbc.basico.ListaAcessoRel;
import servicos.bi.exportador.Exportador;
import servicos.bi.exportador.RelatorioBuilder;

import javax.faces.model.SelectItem;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class ListaAcessoControleRel extends SuperControleRelatorio {

    private static final String SUBDIRETORIO_ARQUIVOS = "relatoriogeralcliente";

    protected ListaAcessoRel listaAcessoRel;
    protected List<SelectItem> listaDeEmpresa;
    protected String campoConsultarCliente;
    protected String valorConsultarCliente;
    protected List listaConsultarCliente;
    protected String filtros;
    protected Boolean temEmpresa;
    protected Boolean popUp;
    private EmpresaInterfaceFacade empresaFacade = null;
    private List<AcessoClienteVO> listaAcessos;
    private List<SelectItem> grupos;
    private String campoOrdenarSelecionado = "NomeUsuarioApresentar" ;
    private List<SelectItem> listaSelectItemModalidade;
    private boolean campoSomentePrimeiroAcesso = false;
    private String urlUploadArquivo;


    public ListaAcessoControleRel() {
        abrirRelatorio();
    }

    public void abrirRelatorio(){
        inicializarFacades();
        inicializarDados();
        montarListaSelectItemEmpresa();
        montarListaSelectItemModalidade();
    }

    protected boolean inicializarFacades() {
        try {
            empresaFacade = getFacade().getEmpresa();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public void inicializarDados() {
        try {
            montarListaGrupos();
            setListaAcessoRel(new ListaAcessoRel());
            setListaDeEmpresa(new ArrayList<SelectItem>());
            setCampoConsultarCliente("");
            setValorConsultarCliente("");
            setListaConsultarCliente(new ArrayList());
            setUsuario(getUsuarioLogado());
            setPopUp(false);
            setTemEmpresa(false);
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    private void montarListaGrupos() throws Exception{
        List<GrupoVO> lista = getFacade().getGrupo().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        grupos = new ArrayList<SelectItem>();
        grupos.add(new SelectItem(0, ""));
        for(GrupoVO grupo : lista){
            grupos.add(new SelectItem(grupo.getCodigo(), grupo.getDescricao()));
        }
    }

    public void obterEmpresaEscolhida() throws Exception {
        if (getListaAcessoRel().getEmpresaVO().getCodigo().intValue() != 0) {
            getListaAcessoRel().setEmpresaVO(empresaFacade.consultarPorChavePrimaria(getListaAcessoRel().getEmpresaVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        } else {
            getListaAcessoRel().setEmpresaVO(new EmpresaVO());
        }
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Empresa</code>.
     */
    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaDeEmpresa(objs);
    }

    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return empresaFacade.consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    private void montarListaSelectItemModalidade() {
        try {
            List<ModalidadeVO> modalidades = getFacade().getModalidade().consultarTodasModalidadesComLimite(getListaAcessoRel().getEmpresaVO().getCodigo(), 50);
            List<SelectItem> listaModalidades = new ArrayList<SelectItem>();
            listaModalidades.add(new SelectItem(0, ""));
            for (ModalidadeVO modalidadeVO : modalidades) {
                listaModalidades.add(new SelectItem(modalidadeVO.getCodigo(), modalidadeVO.getNome()));
            }
            setListaSelectItemModalidade(listaModalidades);
            setErro(false);
            setSucesso(false);
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro","Não foi possível consultar as Modalidades desta empresa.");
            setErro(true);
            setSucesso(false);
        }
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Empresa</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Empresa</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemEmpresa() {
        try {
            getListaAcessoRel().setEmpresaVO(getEmpresaLogado());
            if (getListaAcessoRel().getEmpresaVO() != null && getListaAcessoRel().getEmpresaVO().getNome().trim().isEmpty()) {
                setTemEmpresa(true);
                montarListaSelectItemEmpresa("");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Integer obterEmpresaLogadoSistema() throws Exception {
        EmpresaVO emp = getEmpresaLogado();
        if (emp == null || emp.getCodigo().intValue() == 0) {
            return getListaAcessoRel().getEmpresaVO().getCodigo();
        } else {
            return emp.getCodigo();
        }
    }

    public void consultarCliente() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarCliente().equals("nome")) {
                objs = getFacade().getCliente().consultarPorNomePessoa(getValorConsultarCliente(), getListaAcessoRel().getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS, null);
            }
            if (getCampoConsultarCliente().equals("matricula")) {
                objs = getFacade().getCliente().consultarPorMatricula(getValorConsultarCliente(), getListaAcessoRel().getEmpresaVO().getCodigo(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            setListaConsultarCliente(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarCliente(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarCliente() {
        try {
            ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
            getListaAcessoRel().setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        } catch (Exception e) {
            getListaAcessoRel().setClienteVO(new ClienteVO());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void limparCampoCliente() {
        getListaAcessoRel().setClienteVO(new ClienteVO());
    }

    public List<SelectItem> getTipoConsultaComboCliente() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("nome", "Nome"));
        objs.add(new SelectItem("matricula", "Matrícula"));
        return objs;
    }

    public List<SelectItem> getListaSelectItemProfessor() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        List listaColaborador = getFacade().getColaborador().consultarPorNomeTipoColaborador("", getEmpresaLogado().getCodigo(), false, false, Uteis.NIVELMONTARDADOS_MINIMOS, TipoColaboradorEnum.PROFESSOR);
        Iterator i = listaColaborador.iterator();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ColaboradorVO colaborador = (ColaboradorVO) i.next();
            objs.add(new SelectItem(colaborador.getCodigo(), colaborador.getPessoa().getNome()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List<SelectItem> getListaSelectItemProfessorTreino() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        List listaColaboradorTreino = getFacade().getColaborador().consultarPorNomeTipoColaborador("", getEmpresaLogado().getCodigo(), false, false, Uteis.NIVELMONTARDADOS_MINIMOS, TipoColaboradorEnum.PROFESSOR_TREINO);

        Iterator i = listaColaboradorTreino.iterator();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ColaboradorVO colaborador = (ColaboradorVO) i.next();
            objs.add(new SelectItem(colaborador.getCodigo(), colaborador.getPessoa().getNome()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public void obterProfessorEscolhido() throws Exception {
        if (getListaAcessoRel().getProfessor().getCodigo() != 0) {
            getListaAcessoRel().setProfessor(getFacade().getColaborador().consultarPorChavePrimaria(getListaAcessoRel().getProfessor().getCodigo(), Uteis.NIVELMONTARDADOS_INDICERENOVACAO));
        } else {
            getListaAcessoRel().setProfessor(new ColaboradorVO());
        }
    }

    public void obterProfessorVinculoEscolhido() throws Exception {
        if (getListaAcessoRel().getProfessorVinculo().getCodigo().intValue() != 0) {
            getListaAcessoRel().setProfessorVinculo(getFacade().getColaborador().consultarPorChavePrimaria(getListaAcessoRel().getProfessorVinculo().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_INDICERENOVACAO));
        } else {
            getListaAcessoRel().setProfessorVinculo(new ColaboradorVO());
        }
    }

    public void obterProfessorTreinoEscolhido() throws Exception {
        if (getListaAcessoRel().getProfessorTreino().getCodigo().intValue() != 0) {
            getListaAcessoRel().setProfessorTreino(getFacade().getColaborador().consultarPorChavePrimaria(getListaAcessoRel().getProfessorTreino().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_INDICERENOVACAO));
        } else {
            getListaAcessoRel().setProfessorTreino(new ColaboradorVO());
        }
    }
    public void obterGrupo() throws Exception {
        if (getListaAcessoRel().getGrupo().getCodigo().intValue() != 0) {
            getListaAcessoRel().setGrupo(getFacade().getGrupo().consultarPorChavePrimaria(getListaAcessoRel().getGrupo().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        } else {
            getListaAcessoRel().setGrupo(new GrupoVO());
        }
    }

    public List<SelectItem> getListaSelectItemColaborador() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        List listaColaborador = getFacade().getColaborador().consultarPorNomePessoa("", getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        Iterator i = listaColaborador.iterator();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ColaboradorVO colaborador = (ColaboradorVO) i.next();
            objs.add(new SelectItem(colaborador.getCodigo(), colaborador.getPessoa().getNome()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public void obterColaboradorEscolhido() throws Exception {
        if (getListaAcessoRel().getColaborador().getCodigo().intValue() != 0) {
            getListaAcessoRel().setColaborador(getFacade().getColaborador().consultarPorChavePrimaria(getListaAcessoRel().getColaborador().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        } else {
            getListaAcessoRel().setColaborador(new ColaboradorVO());
        }
    }
    //  Método para vericar o mês, o 1.0 no return representa a quantidade de meses
    private boolean verificaPeriodoMeses( Integer mes) {
        Calendar iniCalendar = Calendar.getInstance();
        iniCalendar.setTime(getListaAcessoRel().getDataInicio());
        Calendar fimCalendar = Calendar.getInstance();
        fimCalendar.setTime(getListaAcessoRel().getDataTermino());

        BigDecimal anoMesIni = new BigDecimal(iniCalendar.get(Calendar.YEAR) * 12 + iniCalendar.get(Calendar.MONTH ));
        BigDecimal diaMesIni = new BigDecimal(iniCalendar.get(Calendar.DAY_OF_MONTH)).divide(new BigDecimal(iniCalendar.getActualMaximum(Calendar.DAY_OF_MONTH)),2, RoundingMode.HALF_UP);

        BigDecimal anoMesFim = new BigDecimal(fimCalendar.get(Calendar.YEAR) * 12 + fimCalendar.get(Calendar.MONTH ));
        BigDecimal diaMesFim = new BigDecimal(fimCalendar.get(Calendar.DAY_OF_MONTH)).divide(new BigDecimal(fimCalendar.getActualMaximum(Calendar.DAY_OF_MONTH)),2,RoundingMode.HALF_UP);

        BigDecimal meses = anoMesFim.add(diaMesFim).subtract(anoMesIni.add(diaMesIni));


        return meses.floatValue() > mes;
    }

    private boolean verificarSelecaoClienteOuColaborador(){
        if(getListaAcessoRel().getClienteVO().getPessoa().getNome() != "" || getListaAcessoRel().getColaborador().getCodigo().intValue() != 0){
            return false;
        }
        return true;
    }

    private void imprimirExcelCliente(List<AcessoClienteTO> resultado) throws Exception {
        File arquivo = criarArquivo("ListaAcessoRelCliente");
        RelatorioBuilder relatorio = new RelatorioBuilder();
        relatorio.dado(resultado);
        relatorio.addColuna("Mat. cliente", "cliente.matricula")
                .addColuna("Nome", "cliente.pessoa.nome")
                .addColuna("Data entrada", "dataHoraEntrada")
                .addColuna("Data saída", "dataHoraSaida")
                .addColuna("Tempo", "intervaloDataHoras")
                .addColuna("Sentido", "sentido")
                .addColuna("Meio identificação", "meioIdentificacaoEntrada.descricao")
                .addColuna("Coletor", "coletor.descricao")
                .addColuna("Bloqueio", "situacao.descricao")
                .addColuna("Empresa", "localAcesso.empresa.nome")
                .addColuna("Email", "cliente.pessoa.email")
                .addColuna("Usuário lib.", "usuario.primeiroNomeConcatenado");
        relatorio.titulo("Lista de Acessos");
        Exportador.exportarExcel(relatorio, arquivo);
    }

    private void imprimirExcelColaborador(List<AcessoColaboradorVO> resultado) throws Exception {
        File arquivo = criarArquivo("ListaAcessoRelColaborador");
        RelatorioBuilder relatorio = new RelatorioBuilder();
        relatorio.dado(resultado);
        relatorio.addColuna("Código", "colaborador.codigo")
                .addColuna("Nome", "colaborador.pessoa.nome")
                .addColuna("Data entrada", "dataHoraEntrada")
                .addColuna("Data saída", "dataHoraSaida")
                .addColuna("Tempo", "intervaloDataHoras")
                .addColuna("Sentido", "sentido")
                .addColuna("Meio identificação", "meioIdentificacaoEntrada.descricao")
                .addColuna("Coletor", "coletor.descricao")
                .addColuna("Email", "colaborador.pessoa.email")
                .addColuna("Empresa", "localAcesso.empresa.nome");
        relatorio.titulo("Lista de Acessos");
        Exportador.exportarExcel(relatorio, arquivo);
    }
    private void imprimirPDFCliente(){
        try {
            String nomeRelatorio = "ListaAcessoRelCliente";
            List listaRegistro = getListaRelatorio();
            if(getCampoOrdenarSelecionado().equals("dataHoraEntrada"))
                Ordenacao.ordenarLista(listaRegistro,getCampoOrdenarSelecionado());
            String titulo = "Lista de Acessos";
            String design = listaAcessoRel.getDesignIReportRelatorio();
            String filtros = getDescricaoFiltros();
            String barra = "/";
            if (listaAcessoRel.getEmpresaVO().getCidade().getNome().equals("")) {
                barra = "";
            }
            if (filtros.equals("")) {
                filtros = "nenhum";
            }
            Map<String, Object> params = new HashMap<String, Object>();
            String caminnhoXML = "/" + listaAcessoRel.getIdEntidade() + "/registros";

            params.put("tituloRelatorio", titulo);
            params.put("nomeRelatorio", nomeRelatorio);
            params.put("nomeEmpresa", listaAcessoRel.getEmpresaVO().getNome());
            params.put("imagemLogo", "");
            params.put("mensagemRel", "");
            params.put("tipoRelatorio", getTipoRelatorio());
            params.put("caminhoParserXML", caminnhoXML);
            params.put("nomeDesignIReport", design);
            params.put("nomeUsuario", "");
            params.put("listaObjetos", listaRegistro);
            params.put("tipoImplementacao", "OBJETO");
            params.put("filtros", filtros);
            params.put("dataIni","");
            params.put("dataFim", "");
            params.put("enderecoEmpresa", listaAcessoRel.getEmpresaVO().getEndereco());
            params.put("cidadeEmpresa", listaAcessoRel.getEmpresaVO().getCidade().getNome() +barra+ listaAcessoRel.getEmpresaVO().getCidade().getEstado().getSigla());

            apresentarRelatorioObjetos(params);
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void imprimirPDFColaborador(){
        try {
            String nomeRelatorio = "ListaAcessoRelCliente";
            List listaRegistro = getListaRelatorio();
            if(getCampoOrdenarSelecionado().equals("dataHoraEntrada"))
                Ordenacao.ordenarLista(listaRegistro,getCampoOrdenarSelecionado());
            String titulo = "Lista de Acessos";
            String design = listaAcessoRel.getDesignIReportRelatorio();
            String filtros = getDescricaoFiltros();
            String barra = "/";
            if (listaAcessoRel.getEmpresaVO().getCidade().getNome().equals("")) {
                barra = "";
            }
            if (filtros.equals("")) {
                filtros = "nenhum";
            }
            Map<String, Object> params = new HashMap<String, Object>();
            String caminnhoXML = "/" + listaAcessoRel.getIdEntidade() + "/registros";

            params.put("tituloRelatorio", titulo);
            params.put("nomeRelatorio", nomeRelatorio);
            params.put("nomeEmpresa", listaAcessoRel.getEmpresaVO().getNome());
            params.put("imagemLogo", "");
            params.put("mensagemRel", "");
            params.put("tipoRelatorio", getTipoRelatorio());
            params.put("caminhoParserXML", caminnhoXML);
            params.put("nomeDesignIReport", design);
            params.put("nomeUsuario", "");
            params.put("listaObjetos", listaRegistro);
            params.put("tipoImplementacao", "OBJETO");
            params.put("filtros", filtros);
            params.put("dataIni","");
            params.put("dataFim", "");
            params.put("enderecoEmpresa", listaAcessoRel.getEmpresaVO().getEndereco());
            params.put("cidadeEmpresa", listaAcessoRel.getEmpresaVO().getCidade().getNome() +barra+ listaAcessoRel.getEmpresaVO().getCidade().getEstado().getSigla());

            apresentarRelatorioObjetos(params);
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }


    private File criarArquivo(String s) throws Exception{

        String nome = s
                + "-" + getKey()
                + "-" + String.valueOf(negocio.comuns.utilitarias.Calendario.hoje().getTime())
                + ".xlsx";

        File arquivo = new File(Uteis.obterCaminhoWeb() + File.separator + "relatorio" + File.separator + nome);
        if (arquivo.exists()) {
            arquivo.delete();
        }else{
            new File(getDiretorioArquivos() + File.separator + SUBDIRETORIO_ARQUIVOS).mkdirs();
        }
        arquivo.createNewFile();

        this.urlUploadArquivo = JSFUtilities.getRequest().getContextPath() + "/DownloadSV?mimeType=application/vnd.ms-excel&relatorio=" + nome;

        return arquivo;
    }

    public void imprimirPDF() {
        imprimir("PDF");
    }
    public void imprimirExcel() {
        this.urlUploadArquivo = "";
        imprimir("Excel");
    }

    public void imprimir(String tipo) {
        try {
            if(verificarSelecaoClienteOuColaborador()){
                if(verificaPeriodoMeses((int) 1.0)){
                    throw new Exception("A data não pode ser maior que 1 mês!");
                }
            }else if(verificaPeriodoMeses((int) 12.0)){
                throw new Exception("A data não pode ser maior que 12 meses!");
            }

            setTipoRelatorio(tipo);
            if (listaAcessoRel.getTipoAcessoClienteOuColaborador().equals("CL")) {
                setListaAcessos(listaAcessoRel.consultarAcessosClientes(campoSomentePrimeiroAcesso));
            } else if (listaAcessoRel.getTipoAcessoClienteOuColaborador().equals("CO")) {
                setListaAcessos(listaAcessoRel.consultarAcessosColaboradores(campoSomentePrimeiroAcesso));
            }
            if (!getListaAcessos().isEmpty()) {
                if(getCampoOrdenarSelecionado().equals("dataHoraEntrada")) {
                    Ordenacao.ordenarLista(getListaAcessos(), getCampoOrdenarSelecionado());
                }

                setListaRelatorio(getListaAcessos());
                if(getTipoRelatorio().equalsIgnoreCase("PDF")){
                    if (listaAcessoRel.getTipoAcessoClienteOuColaborador().equals("CL")) {
                        imprimirPDFCliente();
                    } else if (listaAcessoRel.getTipoAcessoClienteOuColaborador().equals("CO")) {
                        imprimirPDFColaborador();
                    }
                }else{
                    if(getTipoRelatorio().equalsIgnoreCase("Excel")) {
                        if (listaAcessoRel.getTipoAcessoClienteOuColaborador().equals("CL")) {
                            imprimirExcelCliente(getListaRelatorio());
                        } else if (listaAcessoRel.getTipoAcessoClienteOuColaborador().equals("CO")) {
                            imprimirExcelColaborador(getListaRelatorio());
                        }
                    }

                }
            } else {
                throw new Exception("Dados não encontrados!");
            }

            setFiltros(getDescricaoFiltros());
            setRelatorio("sim");
            setMensagemDetalhada("");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setRelatorio("nao");
            setMensagemDetalhada("msg_erro", e.getMessage());
            if(verificaPeriodoMeses((int) 1.0)){
                setMensagemDetalhada("msg_entre_dicarelatorio", e.getMessage());
            }
        }
    }

    private String getDescricaoFiltros() throws Exception {
        String strFiltros = "";
        if (!getListaAcessoRel().getEmpresaVO().getNome().toString().equals("")) {
            strFiltros += "Empresa: " + getListaAcessoRel().getEmpresaVO().getNome();
        }
        strFiltros += "  Período de: " + Uteis.getData(getListaAcessoRel().getDataInicio()) + " até: " + Uteis.getData(getListaAcessoRel().getDataTermino());
        if (!getListaAcessoRel().getHorarioInicio().toString().equals("")
                && !getListaAcessoRel().getHorarioFim().toString().equals("")
                && getListaAcessoRel().getFaixaHoraInicial().toString().equals("")
                && getListaAcessoRel().getFaixaHoraFinal().toString().equals("")) {
            strFiltros += "  Horário Inicial: " + getListaAcessoRel().getHorarioInicio();
        }
        if (!getListaAcessoRel().getHorarioFim().toString().equals("")
                && !getListaAcessoRel().getHorarioFim().toString().equals("")
                && getListaAcessoRel().getFaixaHoraInicial().toString().equals("")
                && getListaAcessoRel().getFaixaHoraFinal().toString().equals("")) {
            strFiltros += "  Horário Final: " + getListaAcessoRel().getHorarioFim();
        }
        if (!getListaAcessoRel().getFaixaHoraInicial().toString().equals("")
                && !getListaAcessoRel().getFaixaHoraFinal().toString().equals("")) {
            strFiltros += "  Faixa Horária Inicial: " + getListaAcessoRel().getFaixaHoraInicial() + " Final: " + getListaAcessoRel().getFaixaHoraFinal();
        }
        strFiltros += " \nConsulta por: ";
        if (getListaAcessoRel().getTipoAcessoClienteOuColaborador().equals("CL")) {
            strFiltros += "Cliente";
            if (!getListaAcessoRel().getClienteVO().getPessoa().getNome().trim().isEmpty()) {
                strFiltros += "   Nome: " + getListaAcessoRel().getClienteVO().getPessoa().getNome();
            }
            if (!getListaAcessoRel().getProfessor().getPessoa().getNome().trim().isEmpty()) {
                strFiltros += "  Professor Turma: " + getListaAcessoRel().getProfessor().getPessoa().getNome();
            }
            if (!getListaAcessoRel().getProfessorVinculo().getPessoa().getNome().trim().isEmpty()) {
                strFiltros += "  Professor Vínculo: " + getListaAcessoRel().getProfessorVinculo().getPessoa().getNome();
            }
            if (!getListaAcessoRel().getProfessorTreino().getPessoa().getNome().trim().isEmpty()) {
                strFiltros += "  Professor Treino Web: " + getListaAcessoRel().getProfessorTreino().getPessoa().getNome();
            }
            if (!getListaAcessoRel().getGrupo().getDescricao().trim().isEmpty()) {
                strFiltros += "  Grupo: " + getListaAcessoRel().getGrupo().getDescricao();
            }
            if (!getListaAcessoRel().getPlano().getDescricao().trim().isEmpty()) {
                strFiltros += "  Plano: " + getListaAcessoRel().getPlano().getDescricao();
            }
        } else if (getListaAcessoRel().getTipoAcessoClienteOuColaborador().equals("CO")) {
            strFiltros += "Colaborador";
            if(!getListaAcessoRel().getColaborador().getPessoa().getNome().trim().isEmpty()){
                strFiltros +="  Nome: "+getListaAcessoRel().getColaborador().getPessoa().getNome();
            }
        }

        return strFiltros;
    }
    public List<SelectItem> getCamposOrdenacao(){
        List<SelectItem> campo = new ArrayList<SelectItem>();
        campo.add(new SelectItem("dataHoraEntrada","Data/Hora Entrada"));
        campo.add(new SelectItem("NomeUsuarioApresentar","Nome"));
        return campo;
    }
    public void novo() {
        return;
    }

    /**
     * @return the filtros
     */
    public String getFiltros() {
        return filtros;
    }

    /**
     * @param filtros the filtros to set
     */
    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    /**
     * @return the listaAcessoRel
     */
    public ListaAcessoRel getListaAcessoRel() {
        return listaAcessoRel;
    }

    /**
     * @param listaAcessoRel the listaAcessoRel to set
     */
    public void setListaAcessoRel(ListaAcessoRel listaAcessoRel) {
        this.listaAcessoRel = listaAcessoRel;
    }

    public List getListaSelectItemTipoAcessoClienteOuColaborador() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("CL", "Cliente"));
        objs.add(new SelectItem("CO", "Colaborador"));
        return objs;
    }

    /**
     * @return the campoConsultarCliente
     */
    public String getCampoConsultarCliente() {
        return campoConsultarCliente;
    }

    /**
     * @param campoConsultarCliente the campoConsultarCliente to set
     */
    public void setCampoConsultarCliente(String campoConsultarCliente) {
        this.campoConsultarCliente = campoConsultarCliente;
    }

    /**
     * @return the valorConsultarCliente
     */
    public String getValorConsultarCliente() {
        return valorConsultarCliente;
    }

    /**
     * @param valorConsultarCliente the valorConsultarCliente to set
     */
    public void setValorConsultarCliente(String valorConsultarCliente) {
        this.valorConsultarCliente = valorConsultarCliente;
    }

    /**
     * @return the listaConsultarCliente
     */
    public List getListaConsultarCliente() {
        return listaConsultarCliente;
    }

    /**
     * @param listaConsultarCliente the listaConsultarCliente to set
     */
    public void setListaConsultarCliente(List listaConsultarCliente) {
        this.listaConsultarCliente = listaConsultarCliente;
    }

    /**
     * @return the temEmpresa
     */
    public Boolean getTemEmpresa() {
        return temEmpresa;
    }

    /**
     * @param temEmpresa the temEmpresa to set
     */
    public void setTemEmpresa(Boolean temEmpresa) {
        this.temEmpresa = temEmpresa;
    }

    /**
     * @return the popUp
     */
    public Boolean getPopUp() {
        return popUp;
    }

    /**
     * @param popUp the popUp to set
     */
    public void setPopUp(Boolean popUp) {
        this.popUp = popUp;
    }

    /**
     * @return the listaDeEmpresa
     */
    public List<SelectItem> getListaDeEmpresa() {
        return listaDeEmpresa;
    }

    /**
     * @param listaDeEmpresa the listaDeEmpresa to set
     */
    public void setListaDeEmpresa(List<SelectItem> listaDeEmpresa) {
        this.listaDeEmpresa = listaDeEmpresa;
    }

    /**
     * @return the listaAcessos
     */
    public List getListaAcessos() {
        return listaAcessos;
    }

    /**
     * @param listaAcessos the listaAcessos to set
     */
    public void setListaAcessos(List<AcessoClienteVO> listaAcessos) {
        this.listaAcessos = listaAcessos;
    }

    public void setGrupos(List<SelectItem> grupos) {
        this.grupos = grupos;
    }

    public List<SelectItem> getGrupos() {
        return grupos;
    }

    public List<SelectItem> getListaSelectItemPlano() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        List listaPlano = getFacade().getPlano().consultarPorDescricao("", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
        Iterator i = listaPlano.iterator();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            PlanoVO plano = (PlanoVO) i.next();
            objs.add(new SelectItem(plano.getCodigo(), plano.getDescricao()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public void obterPlanoEscolhido() throws Exception {
        if (getListaAcessoRel().getPlano().getCodigo() != 0) {
            getListaAcessoRel().setPlano(getFacade().getPlano().consultarPorChavePrimaria(getListaAcessoRel().getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        } else {
            getListaAcessoRel().setPlano(new PlanoVO());
        }
    }

    public String getCampoOrdenarSelecionado() {
        return campoOrdenarSelecionado;
    }

    public void setCampoOrdenarSelecionado(String campoOrdenarSelecionado) {
        this.campoOrdenarSelecionado = campoOrdenarSelecionado;
    }

    public List<SelectItem> getListaSelectItemModalidade() {
        if (listaSelectItemModalidade == null) {
            listaSelectItemModalidade = new ArrayList<SelectItem>();
        }
        return listaSelectItemModalidade;
    }

    public void setListaSelectItemModalidade(List<SelectItem> listaSelectItemModalidade) {
        this.listaSelectItemModalidade = listaSelectItemModalidade;
    }

    public boolean isCampoSomentePrimeiroAcesso() {
        return campoSomentePrimeiroAcesso;
    }

    public void setCampoSomentePrimeiroAcesso(boolean campoSomentePrimeiroAcesso) {
        this.campoSomentePrimeiroAcesso = campoSomentePrimeiroAcesso;
    }

    public String getUrlUploadArquivo() {
        return urlUploadArquivo;
    }

    public void setUrlUploadArquivo(String urlUploadArquivo) {
        this.urlUploadArquivo = urlUploadArquivo;
    }

}

package relatorio.controle.basico;

import java.io.File;
import java.text.ParseException;
import java.util.*;
import javax.faces.model.SelectItem;

import br.com.pactosolucoes.comuns.to.AniversarioRelTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;

public class ClientePorAniversarioRelControle extends SuperControleRelatorio {

    private Date dataInicio;
    private Date dataTermino;
    private boolean situacaoAtivo;
    private boolean situacaoInativo;
    private boolean situacaoVisitante;
    private boolean situacaoTrancado;
    private List<AniversarioRelTO> listaClientePorAniversario;
    private int totalizador;
    private String filtros;
    private PlanoVO plano;
    private String tipoPessoaSelecionada;
    private List<SelectItem> listaPlanos;
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;

    public ClientePorAniversarioRelControle() throws Exception {
        inicializarDados();
        montarListaSelectItemEmpresa();
        montarDadosPlano();
    }

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Boolean getPessoaAluno() {

        if (TipoPessoaEnum.getTipo(tipoPessoaSelecionada) == TipoPessoaEnum.ALUNO) {
            return true;
        } else
            return false;

    }
    public String getRelatorioExcelAtributos() throws Exception {
        if (TipoPessoaEnum.getTipo(tipoPessoaSelecionada) == TipoPessoaEnum.ALUNO) {
            if (getUsuario().getLinguagem().equals("en")){
                return "matricula=Registration,nome=Name,dataNasc=Birthday,situacao=Status,plano=Membership,nomeEmpresa=Company";
            }
            return "matricula=Matrícula,nome=Nome,dataNasc=Data Aniversário,situacao=Situação,plano=Plano,nomeEmpresa=Empresa";
        } else if (TipoPessoaEnum.getTipo(tipoPessoaSelecionada) == TipoPessoaEnum.COLABORADOR) {
            return "nome=Nome,dataNasc=Data Aniversário,situacao=Situação,nomeEmpresa=Empresa";
        } else if (TipoPessoaEnum.getTipo(tipoPessoaSelecionada) == TipoPessoaEnum.AMBOS) {
            return "matricula=Matrícula,tipo=Tipo,nome=Nome,dataNasc=Data Aniversário,situacao=Situação,plano=Plano,nomeEmpresa=Empresa";
        }
        return null;
    }
    public Boolean getPessoaColaborador(){

        if(tipoPessoaSelecionada.equals(TipoPessoaEnum.COLABORADOR.getTipo())){
            return true;
        }else
            return false;

    }

    public Boolean getPessoaAmbos() {
        if (tipoPessoaSelecionada.equals(TipoPessoaEnum.AMBOS.getTipo())) {
            return true;
        } else {
            return false;
        }
    }

    public void inicializarDados() {
        try {
            setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
            setDataTermino(negocio.comuns.utilitarias.Calendario.hoje());
            setSituacaoAtivo(false);
            setSituacaoInativo(false);
            setSituacaoVisitante(false);
            setSituacaoTrancado(false);
            setEmpresa(new EmpresaVO());
            setListaClientePorAniversario(new ArrayList<>());
            setMensagemID("msg_entre_prmrelatorio");
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
            setTotalizador(0);
            setPlano(new PlanoVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    public List<SelectItem> getListaSelectItemTipoPessoa(){

        List<SelectItem> lista = new ArrayList<SelectItem>();
        for(TipoPessoaEnum tipo : TipoPessoaEnum.values()){
           lista.add(new SelectItem(tipo.getTipo(),tipo.getLabel()));
        }
        return lista;
    }

    public List<SelectItem> getListaPlanos() {
        if(listaPlanos== null) {
            listaPlanos = new ArrayList<SelectItem>();
        }
        return listaPlanos;
    }

    public void setListaPlanos(List<SelectItem> listaPlanos) {
        this.listaPlanos = listaPlanos;
    }

    public void validarDados() throws Exception {
        if (getDataInicio() == null) {
            throw new Exception("Não é possível emitir o relatório. Informe primeiro a Data De Início da Pesquisa.");
        }
        if (getDataTermino() == null) {
            throw new Exception("Não é possível emitir o relátorio. Informe primeiro a Data De Término da Pesquisa.");
        }
        if (getDataInicio().compareTo(getDataTermino()) > 0) {
            throw new Exception("A Data de Início deve ser menor que a Data De Término para Pesquisa.");
        }
    }

    public String imprimir() {
        try {
            validarDados();
            setListaClientePorAniversario(new ArrayList<>());
            setTotalizador(0);
            List<String> listaSituacao;
            List<AniversarioRelTO> listaAmbos;
            // preenche as situações marcadas
            if(getPessoaAluno()) {
                listaSituacao = preencheListaSituacaoCliente();
                List<AniversarioRelTO> listaClienteAniversario = getFacade().getCliente().consultarAniversarioClientes(getFiltroEmpresa(), dataInicio, dataTermino, listaSituacao, getPlano().getCodigo(), TipoPessoaEnum.getTipo(tipoPessoaSelecionada));
                if (getUsuario().getLinguagem().equals("en")) {
                    listaClienteAniversario = alteraIdiomaSituacao(listaClienteAniversario);
                    setListaClientePorAniversario(listaClienteAniversario);
                } else {
                    setListaClientePorAniversario(listaClienteAniversario);
                }
            } else if (getPessoaColaborador()) {
                listaSituacao = preencheListaSituacaoColaborador();
                setListaClientePorAniversario(getFacade().getCliente().consultarAniversarioClientes(getFiltroEmpresa(), dataInicio, dataTermino, listaSituacao, getPlano().getCodigo(), TipoPessoaEnum.getTipo(tipoPessoaSelecionada)));
            } else {
                listaSituacao = preencheListaSituacaoAmbos();
                listaAmbos = getFacade().getCliente().consultarAniversarioClientes(getFiltroEmpresa(), dataInicio, dataTermino, listaSituacao, getPlano().getCodigo(), TipoPessoaEnum.ALUNO);
                listaAmbos.addAll(getFacade().getCliente().consultarAniversarioClientes(getFiltroEmpresa(), dataInicio, dataTermino, listaSituacao, getPlano().getCodigo(), TipoPessoaEnum.COLABORADOR));
                listaAmbos.sort(Comparator.comparing(AniversarioRelTO::getAniversario));
                setListaClientePorAniversario(listaAmbos);
            }

            setTotalizador(getListaClientePorAniversario().size());
            setFiltros(getDescricaoFiltros());
            setMensagem("Dados consultados");
            setMensagemDetalhada("");
            return "relatorio";

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }
    public String getNomeRelatorio() throws Exception {
        String nomeRelatorio= "Aniversariantes";
        if(getPessoaAluno()) {
            if (getUsuario().getLinguagem().equals("en")){
                nomeRelatorio = "BirthdayStudents";
            }else {
                nomeRelatorio = "AlunosAniversariantes";
            }
        }else if (getPessoaColaborador()){
            nomeRelatorio = "ColaboradoresAniversariantes";
        } else {
            nomeRelatorio = "AmbosAniversariantes";
        }
        return  nomeRelatorio;
    }
    public void imprimirRelatorio() {
        try {

            String titulo = "Aniversariantes";
            String design = getDesignIReportRelatorio();
            setRelatorio("sim");
            setListaRelatorio(getListaClientePorAniversario());

            apresentarRelatorioObjetos(getNomeRelatorio(), titulo, getEmpresaLogado().getNome(), "", "", getTipoRelatorio(),
                    "/Aniversariantes/registros", design, getUsuarioLogado().getNome(),
                    getDescricaoFiltrosParaPDF(), getListaRelatorio());

            setMensagemDetalhada("");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    List<String> preencheListaSituacaoColaborador(){
        List listaSituacao = new ArrayList();
        if (isSituacaoAtivo()) {
            listaSituacao.add("AT");
        }
        if (isSituacaoInativo()) {
            listaSituacao.add("NA");
        }
        return listaSituacao;
    }
    public List<String> preencheListaSituacaoCliente() {
        List listaSituacao = new ArrayList();
        if (isSituacaoAtivo()) {
            listaSituacao.add("AT");
        }
        if (isSituacaoInativo()) {
            listaSituacao.add("IN");
        }
        if (isSituacaoVisitante()) {
            listaSituacao.add("VI");
        }
        if (isSituacaoTrancado()) {
            listaSituacao.add("TR");
        }
        return listaSituacao;
    }

    public List<String> preencheListaSituacaoAmbos() {
        List<String> listaSituacao = new ArrayList<>();
        if (isSituacaoAtivo()) {
            listaSituacao.add("AT");
        }
        if (isSituacaoInativo()) {
            listaSituacao.add("IN");
            listaSituacao.add("NA");
        }
        if (isSituacaoVisitante()) {
            listaSituacao.add("VI");
        }
        if (isSituacaoTrancado()) {
            listaSituacao.add("TR");
        }
        return listaSituacao;
    }

    public List<String> preencheListaSituacaoFiltros() {
        List listaSituacao = new ArrayList();
        if (isSituacaoAtivo()) {
            listaSituacao.add("Ativo");
        }
        if (isSituacaoInativo()) {
            listaSituacao.add("Inativo");
        }
        if (isSituacaoVisitante()) {
            listaSituacao.add("Visitante");
        }
        if (isSituacaoTrancado()) {
            listaSituacao.add("Trancado");
        }
        return listaSituacao;
    }

    public void selecionarMesAtual() throws Exception {
        setDataInicio(Uteis.obterPrimeiroDiaMes(negocio.comuns.utilitarias.Calendario.hoje()));
        setDataTermino(Uteis.obterUltimoDiaMesUltimaHora(negocio.comuns.utilitarias.Calendario.hoje()));
        setMensagemDetalhada("");
    }

    public String voltar() {
        setListaClientePorAniversario(new ArrayList<>());
        return "voltar";
    }

    public void irParaTelaCliente() throws  Exception{
        AniversarioRelTO obj = (AniversarioRelTO) context().getExternalContext().getRequestMap().get("pessoa");
        ClienteVO clienteVO = getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(),getEmpresa().getCodigo());
        try {
            if(obj == null)
                throw new Exception("Cliente Não Encontrado.");
            else
                irParaTelaCliente(clienteVO);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    public void irParaTelaColaborador() throws Exception{
        AniversarioRelTO obj = (AniversarioRelTO) context().getExternalContext().getRequestMap().get("pessoa");
        ColaboradorVO colaboradorVO  = getFacade().getColaborador().consultarPorChavePrimaria(obj.getCodigo(), getEmpresa().getCodigo());
        try {
            if(obj == null)
                throw new Exception("Cliente Não Encontrado.");
            else
                irParaTelaColaborador(colaboradorVO);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<AniversarioRelTO> getListaClientePorAniversario() {
        return listaClientePorAniversario;
    }

    public void setListaClientePorAniversario(List<AniversarioRelTO> listaClientePorAniversario) {
        this.listaClientePorAniversario = listaClientePorAniversario;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public boolean isSituacaoAtivo() {
        return situacaoAtivo;
    }

    public void setSituacaoAtivo(boolean situacaoAtivo) {
        this.situacaoAtivo = situacaoAtivo;
    }

    public boolean isSituacaoInativo() {
        return situacaoInativo;
    }

    public void setSituacaoInativo(boolean situacaoInativo) {
        this.situacaoInativo = situacaoInativo;
    }

    public int getTotalizador() {
        return totalizador;
    }

    public void setTotalizador(int totalizador) {
        this.totalizador = totalizador;
    }
    public String getTipoPessoaSelecionada() {
        if(tipoPessoaSelecionada== null){
            tipoPessoaSelecionada = TipoPessoaEnum.ALUNO.getTipo();

        }
        return tipoPessoaSelecionada;
    }

    public void setTipoPessoaSelecionada(String tipoPessoaSelecionada) {
        this.tipoPessoaSelecionada = tipoPessoaSelecionada;
    }

    private String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "Aniversariantes.jrxml");
    }

    private String getDescricaoFiltros() {
        String aux = "";
        aux += "<b>Empresa: </b> " + getNomeEmpresaSelecionada(getFiltroEmpresa()) + "</br>";
        aux += " <b>Período de: </b>" + Uteis.getData(dataInicio) + " <b>Até: </b>" + Uteis.getData(dataTermino) + "</br>";

        List<String> listaSituacao;
        if(getPessoaAluno()) {
            listaSituacao = preencheListaSituacaoCliente();
        } else {
            listaSituacao = preencheListaSituacaoColaborador();
        }

        if (!UteisValidacao.emptyList(listaSituacao)) {
            List<String> lista = preencheListaSituacaoFiltros();
            if (lista.size() > 0) {
                for (String situacao : lista) {
                    aux += "<b>Situação: </b> " + situacao + "</br>";
                }
            }
        }
        return aux;
    }

    private String getDescricaoFiltrosParaPDF() {
        String aux = "";
        aux += "Empresa " + getNomeEmpresaSelecionada(getFiltroEmpresa());
        aux += " Período de " + Uteis.getData(dataInicio) + " Até " + Uteis.getData(dataTermino);

        List<String> listaSituacao;
        if(getPessoaAluno()) {
            listaSituacao = preencheListaSituacaoCliente();
        } else {
            listaSituacao = preencheListaSituacaoColaborador();
        }

        if (!UteisValidacao.emptyList(listaSituacao)) {
            List<String> lista = preencheListaSituacaoFiltros();
            if (lista.size() > 0) {
                for (String situacao : lista) {
                    aux += " Situação "+ situacao;
                }
            }
        }
        return aux;
    }

    public boolean isSituacaoVisitante() {
        return situacaoVisitante;
    }

    public void setSituacaoVisitante(boolean situacaoVisitante) {
        this.situacaoVisitante = situacaoVisitante;
    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }


    public void montarDadosPlano() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        List listaPlano = getFacade().getPlano().consultarPorDescricao("", getFiltroEmpresa(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
        Iterator i = listaPlano.iterator();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            PlanoVO plano = (PlanoVO) i.next();
            objs.add(new SelectItem(plano.getCodigo(), plano.getDescricao()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        setListaPlanos(objs);

    }

    public PlanoVO getPlano() {
        return plano;
    }

    public void setPlano(PlanoVO plano){
        this.plano = plano;
    }

    public void obterPlanoEscolhido() throws Exception {
        if (getPlano().getCodigo() != 0) {
            setPlano(getFacade().getPlano().consultarPorChavePrimaria(getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        } else {
            setPlano(new PlanoVO());
        }
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public boolean isSituacaoTrancado() {
        return situacaoTrancado;
    }

    public void setSituacaoTrancado(boolean situacaoTrancado) {
        this.situacaoTrancado = situacaoTrancado;
    }

    public List<AniversarioRelTO> alteraIdiomaSituacao (List<AniversarioRelTO> list){
        for (int i=0; i < list.size(); i++){
            String situacao = list.get(i).getSituacao();
            if (situacao.equals("Ativo")) {
                situacao = "Active";
                list.get(i).setSituacao(situacao);
            } else if (situacao.equals("Inativo")) {
                situacao = "Inactive";
                list.get(i).setSituacao(situacao);
            } else if (situacao.equals("Visitante")) {
                situacao = "Visitor";
                list.get(i).setSituacao(situacao);
            }
        }
        return list;
    }
}

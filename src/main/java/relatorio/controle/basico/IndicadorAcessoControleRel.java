/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.basico;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.model.SelectItem;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ColecaoUtils;
import negocio.comuns.utilitarias.Uteis;
import org.apache.commons.collections.Predicate;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.jdbc.basico.IndicadorAcessoRel;
import relatorio.negocio.jdbc.basico.IndicadorAcessoTO;
import relatorio.negocio.jdbc.financeiro.TotalizadorFrequenciaRel;

/**
 *
 * <AUTHOR>
 */
public class IndicadorAcessoControleRel extends SuperControleRelatorio{
    
    private String tipoConsulta;
    private Date dataInicio = Calendario.getDataComHoraZerada(Calendario.hoje());
    private Date dataFinal = Calendario.getDataComHoraZerada(Calendario.hoje());
    private Integer quantidadeCliente;
    private List<SelectItem> listaPesquisa = new ArrayList<SelectItem>();
    private String listaAcessoGrafico;
    private List<IndicadorAcessoRel> listaAcessoDiaHora = new ArrayList<IndicadorAcessoRel>();
    private EmpresaVO empresaSelecionada = new EmpresaVO();
    private String quantidadeClienteMensagem;
    private String mensagemControle;
    private List<SelectItem> listaEmpresa = new ArrayList<SelectItem>();
    private Integer codigoEmpesaSelecinada = 0;
    private String onComplete = "";
    private List<IndicadorAcessoRel> listaAcessoDiaHoraAnoAtual = new ArrayList<IndicadorAcessoRel>();
    private List<IndicadorAcessoRel> listaAcessoDiaHoraAnoPassado = new ArrayList<IndicadorAcessoRel>();
    private List<IndicadorAcessoTO> listaAcessoProcessada = new ArrayList<IndicadorAcessoTO>();
    private String listaAcessoGraficoComparativo;
    private EmpresaVO empresa = new EmpresaVO();
    
    public IndicadorAcessoControleRel() {
        try {
            obterListaEmpresa();
            inicializarDados();
            montarListaPesquisa();
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public void inicializarDados(){
        
        try {
            setOnComplete("");
            validarDados();
            verificarQuantidadeCliente();
            montarListaAcessoGrafico();
            processarListaIndicadorAcesso();
            carregarGraficoComparativo();
            montarSucessoGrowl("Gráfico gerado com sucesso");
        } catch (Exception ex) {
            getListaAcessoDiaHora().clear();
            setListaAcessoGrafico("[]");
            setMensagemControle("Não Foi Possível Realizar esta Operação");
            montarErro(ex);
            setOnComplete(getMensagemNotificar());
            setListaAcessoGraficoComparativo("[]");
        }
        
    }
    
    private void validarDados() throws Exception{
        if (getTipoConsulta().equals("DIP")) {
            if (getDataInicio() == null) {
                throw new Exception("O campo DATA INÍCIO deve ser informado.");
            }
            
        }else if(getTipoConsulta().equals("PE")){
            if (getDataInicio() == null) {
                throw new Exception("O campo DATA INÍCIO deve ser informado.");
            }
            
            if (getDataFinal()== null) {
                throw new Exception("O campo DATA FINAL deve ser informado.");
            }
            
            if (Calendario.maior(dataInicio, dataFinal)) {
                throw new Exception("A data Incial não pode ser maior que a data final.");
            }
            
            if (getDataInicio() != null && getDataFinal() != null) {
                List<Date> meses = Uteis.getMesesEntreDatas(getDataInicio(), getDataFinal());
                
                if (meses.size() > 12) {
                   throw new Exception("Periodo não pode ser maior que 1 ano."); 
                }
            }
        }
    }
    
    private void verificarQuantidadeCliente() throws Exception{
        if (getUsuarioLogado().getAdministrador()) {
            if (getCodigoEmpesaSelecinada() != 0) {
                empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getCodigoEmpesaSelecinada(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }else{
                empresa = getFacade().getEmpresa().consultarPorChavePrimaria(1,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            
        } else {
            empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
         
        Date dataInicioHoraReduzida = Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, (empresa.getTempoSaidaAcademia() * -1));
        if (getTipoConsulta().equals("HO")) {
           quantidadeCliente = getFacade().getAcessoCliente().obterQuantidadeClientePelaHora(dataInicioHoraReduzida, Calendario.hoje(), empresa.getCodigo());
        }
            
        if (quantidadeCliente > 1) {
            quantidadeClienteMensagem = "Clientes";
        }else{
            quantidadeClienteMensagem = "Cliente";
        }
        TotalizadorFrequenciaRel indicadorAcessoRel = new TotalizadorFrequenciaRel();
        getListaAcessoDiaHora().clear();
        if (getTipoConsulta().equals("HO") || getTipoConsulta().equals("DIP")) {
            if (getTipoConsulta().equals("HO")) {
                setDataInicio(Calendario.hoje());
            }
            getListaAcessoDiaHora().addAll(indicadorAcessoRel.consultarAcessoDoDiaPorHoraData(Uteis.getDataComHoraZerada(getDataInicio()),null, empresa.getCodigo(),true));
        }else if(getTipoConsulta().equals("PE")){
            getListaAcessoDiaHora().addAll(indicadorAcessoRel.consultarAcessoDoDiaPorHoraData(getDataInicio(),getDataFinal(), empresa.getCodigo(),true));
        }else if(getTipoConsulta().equals("ANO")){
            
        }
        
    }
    
    private void montarListaPesquisa(){
        listaPesquisa.add(new SelectItem("HO","Hoje"));
        listaPesquisa.add(new SelectItem("DIP","Dia Personalizado"));
        //listaPesquisa.add(new SelectItem("PE","Período"));
        listaPesquisa.add(new SelectItem("ANO","Comparativo Anual"));
    }
    
    public void montarListaAcessoGrafico(){
        StringBuilder json = new StringBuilder();
        SimpleDateFormat dt = new SimpleDateFormat("dd/MM/yyyy");
        json.append("[");
        if (getTipoConsulta().endsWith("HO") || getTipoConsulta().equals("DIP")) {
            for (int i = 0; i < getListaAcessoDiaHora().size(); i++) {
                json.append("{").append("\"campo\":").append("\"").append(verificarHora(getListaAcessoDiaHora().get(i).getHora())).append("\",");
                json.append("\"valor\":").append("\"").append(getListaAcessoDiaHora().get(i).getQuantidade()).append("\"");
                json.append("},");
            }
        }else if(getTipoConsulta().equals("PE")){
            for (int i = 0; i < getListaAcessoDiaHora().size(); i++) {
                json.append("{").append("\"campo\":").append("\"").append(dt.format(getListaAcessoDiaHora().get(i).getData())).append("\",");
                json.append("\"valor\":").append("\"").append(getListaAcessoDiaHora().get(i).getQuantidade()).append("\"");
                json.append("},");
            }
        }
        
        json.append("]");
        setListaAcessoGrafico(json.toString());
    }
    
    private String verificarHora(Double horaParametro){
        String hora = "";
        Integer horaInteiro = horaParametro.intValue();
        if (horaInteiro <= 9) {
            hora = "0" + horaInteiro;
        }else{
            hora = horaInteiro.toString();
        }
        
        String horaFormatada = hora +":"+"00";
        
        return horaFormatada;
    }
    
    public void obterListaEmpresa() throws Exception{
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarEmpresas();
        for (EmpresaVO empresa : empresas) {
            getListaEmpresa().add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
    }
    
    private void processarListaIndicadorAcesso() throws Exception{
        setListaAcessoProcessada(new ArrayList<IndicadorAcessoTO>());
        TotalizadorFrequenciaRel indicadorAcessoRelControle = new TotalizadorFrequenciaRel();
        Calendar dataCorrente = Calendar.getInstance();
        dataCorrente.setTime(Calendario.hoje());
        setListaAcessoDiaHoraAnoAtual(indicadorAcessoRelControle.consultarAcessoDoDiaPorMes(null, dataCorrente.get(Calendar.YEAR), empresa.getCodigo(), true));
        setListaAcessoDiaHoraAnoPassado(indicadorAcessoRelControle.consultarAcessoDoDiaPorMes(null, dataCorrente.get(Calendar.YEAR)-1, empresa.getCodigo(), false));
        
        for (final IndicadorAcessoRel indicadorAcessoRel : getListaAcessoDiaHoraAnoPassado()) {
            IndicadorAcessoRel item = (IndicadorAcessoRel) ColecaoUtils.find(getListaAcessoDiaHoraAnoAtual(), new Predicate() {
                @Override
                public boolean evaluate(Object o) {
                    return ((IndicadorAcessoRel)o).getMes().equals(indicadorAcessoRel.getMes());
                }
            });
            IndicadorAcessoTO indicadorAcessoTO = new IndicadorAcessoTO();
            if (item != null) {
                indicadorAcessoTO.setMesPassado(indicadorAcessoRel.getMes());
                indicadorAcessoTO.setAnoPassado(indicadorAcessoRel.getAno());
                indicadorAcessoTO.setQuantidadePassado(indicadorAcessoRel.getQuantidade());
                 indicadorAcessoTO.setMesAtual(item.getMes());
                indicadorAcessoTO.setAnoAtual(item.getAno());
                indicadorAcessoTO.setQuantidadeAtual(item.getQuantidade());
            }else{
                indicadorAcessoTO.setMesPassado(indicadorAcessoRel.getMes());
                indicadorAcessoTO.setAnoPassado(indicadorAcessoRel.getAno());
                indicadorAcessoTO.setQuantidadePassado(indicadorAcessoRel.getQuantidade());
                indicadorAcessoTO.setMesAtual(indicadorAcessoRel.getMes());
                indicadorAcessoTO.setAnoAtual(indicadorAcessoRel.getAno()+1);
                indicadorAcessoTO.setQuantidadeAtual(0);
            }
            getListaAcessoProcessada().add(indicadorAcessoTO);
        }
    }
    
    private void carregarGraficoComparativo(){
        setListaAcessoGraficoComparativo("[]");
        
        if (!getListaAcessoProcessada().isEmpty()) {
            StringBuilder json = new StringBuilder();
            json.append("[");
            boolean dados = false;
            for (IndicadorAcessoTO indicadorAcessoTO : getListaAcessoProcessada()) {
                dados = true;
                String mes = Uteis.getMesNomeReferenciaAbreviadoPorMes(indicadorAcessoTO.getMesAtual());
                json.append("{").append("\"campo\":").append("\"").append(mes).append("\",");
                json.append("\"passado\":").append("").append(indicadorAcessoTO.getQuantidadePassado()).append(",");
                json.append("\"atual\":").append("").append(indicadorAcessoTO.getQuantidadeAtual()).append("},");
            }
            if (dados) {
                json.deleteCharAt(json.toString().length() - 1);
            }
            json.append("]");
            setListaAcessoGraficoComparativo(json.toString());
        }
    }
    
    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setQuantidadeCliente(Integer quantidadeCliente) {
        this.quantidadeCliente = quantidadeCliente;
    }

    public Integer getQuantidadeCliente() {
        if (quantidadeCliente == null) {
            quantidadeCliente = 0;
        }
        return quantidadeCliente;
    }

    public void setListaPesquisa(List<SelectItem> listaPesquisa) {
        this.listaPesquisa = listaPesquisa;
    }

    public List<SelectItem> getListaPesquisa() {
        return listaPesquisa;
    }

    public void setTipoConsulta(String tipoConsulta) {
        this.tipoConsulta = tipoConsulta;
    }

    public String getTipoConsulta() {
        if (tipoConsulta == null) {
            tipoConsulta = "HO";
        }
        return tipoConsulta;
    }

    public void setListaAcessoGrafico(String listaAcessoGrafico) {
        this.listaAcessoGrafico = listaAcessoGrafico;
    }

    public String getListaAcessoGrafico() {
        if (listaAcessoGrafico == null) {
            listaAcessoGrafico = "[]";
        }
        return listaAcessoGrafico;
    }

    public List<IndicadorAcessoRel> getListaAcessoDiaHora() {
        return listaAcessoDiaHora;
    }

    public void setListaAcessoDiaHora(List<IndicadorAcessoRel> listaAcessoDiaHora) {
        this.listaAcessoDiaHora = listaAcessoDiaHora;
    }

    public void setQuantidadeClienteMensagem(String quantidadeClienteMensagem) {
        this.quantidadeClienteMensagem = quantidadeClienteMensagem;
    }

    public String getQuantidadeClienteMensagem() {
        return quantidadeClienteMensagem;
    }

    public void setMensagemControle(String mensagemControle) {
        this.mensagemControle = mensagemControle;
    }

    public String getMensagemControle() {
        if (mensagemControle == null) {
            mensagemControle = "";
        }
        return mensagemControle;
    }
    
    public void zerarGraficoAcesso(){
        setListaAcessoGrafico("[]");
    }
    
    public void setListaEmpresa(List<SelectItem> listaEmpresa) {
        this.listaEmpresa = listaEmpresa;
    }

    public List<SelectItem> getListaEmpresa() {
        return listaEmpresa;
    }

    public void setEmpresaSelecionada(EmpresaVO empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public EmpresaVO getEmpresaSelecionada() {
        return empresaSelecionada;
    }

    public void setCodigoEmpesaSelecinada(Integer codigoEmpesaSelecinada) {
        this.codigoEmpesaSelecinada = codigoEmpesaSelecinada;
    }

    public Integer getCodigoEmpesaSelecinada() {
        return codigoEmpesaSelecinada;
    }
    
    public String getOnComplete() {
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public List<IndicadorAcessoRel> getListaAcessoDiaHoraAnoAtual() {
        return listaAcessoDiaHoraAnoAtual;
    }

    public void setListaAcessoDiaHoraAnoAtual(List<IndicadorAcessoRel> listaAcessoDiaHoraAnoAtual) {
        this.listaAcessoDiaHoraAnoAtual = listaAcessoDiaHoraAnoAtual;
    }

    public List<IndicadorAcessoRel> getListaAcessoDiaHoraAnoPassado() {
        return listaAcessoDiaHoraAnoPassado;
    }

    public void setListaAcessoDiaHoraAnoPassado(List<IndicadorAcessoRel> listaAcessoDiaHoraAnoPassado) {
        this.listaAcessoDiaHoraAnoPassado = listaAcessoDiaHoraAnoPassado;
    }

    public List<IndicadorAcessoTO> getListaAcessoProcessada() {
        return listaAcessoProcessada;
    }

    public void setListaAcessoProcessada(List<IndicadorAcessoTO> listaAcessoProcessada) {
        this.listaAcessoProcessada = listaAcessoProcessada;
    }

    public String getListaAcessoGraficoComparativo() {
        return listaAcessoGraficoComparativo;
    }

    public void setListaAcessoGraficoComparativo(String listaAcessoGraficoComparativo) {
        this.listaAcessoGraficoComparativo = listaAcessoGraficoComparativo;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }
 
}

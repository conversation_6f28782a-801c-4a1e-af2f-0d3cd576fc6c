package relatorio.controle.basico;

import java.util.Date;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.plano.ModalidadeVO;

public class MailingFiltrosTO extends SuperTO {

    private static final long serialVersionUID = 4832703472757001040L;
    private Integer malaDireta = null;
    private Integer categoria = null;
    private String situacao = "";
    private Integer duracao = null;
    private Integer evento = null;
    private VinculoVO filtroVinculoCarteira = new VinculoVO();
    private ModalidadeVO filtroModalidade = new ModalidadeVO();
    private int idadeMin = 0;
    private int idadeMax = 0;
    private Date dataCadastroMin = null;
    private Date dataCadastroMax = null;
    private boolean feminino = false;
    private boolean masculino = false;

    private boolean somenteRecorrencia;

    private String codigosCategoria = "";
    private String codigosModalidades = "";
    private String listaSituacoes = "";
    private String codigosConsultores = "";
    private String codigosProfessores = "";
    private String codigosPlanos = "";
    private String codigoContratoDuracao = "";

    private Date vencimentoContratoMin = null;
    private Date vencimentoContratoMax = null;

    private Integer diasSemComparecerMin;
    private Integer diasSemComparecerMax;

    public Date getVencimentoContratoMin() {
        return vencimentoContratoMin;
    }

    public void setVencimentoContratoMin(Date vencimentoContratoMin) {
        this.vencimentoContratoMin = vencimentoContratoMin;
    }

    public Date getVencimentoContratoMax() {
        return vencimentoContratoMax;
    }

    public void setVencimentoContratoMax(Date vencimentoContratoMax) {
        this.vencimentoContratoMax = vencimentoContratoMax;
    }

    public Integer getDiasSemComparecerMin() {
        return diasSemComparecerMin;
    }

    public void setDiasSemComparecerMin(Integer diasSemComparecerMin) {
        this.diasSemComparecerMin = diasSemComparecerMin;
    }

    public Integer getDiasSemComparecerMax() {
        return diasSemComparecerMax;
    }

    public void setDiasSemComparecerMax(Integer diasSemComparecerMax) {
        this.diasSemComparecerMax = diasSemComparecerMax;
    }

    public String getCodigoContratoDuracao() {
        return codigoContratoDuracao;
    }

    public void setCodigoContratoDuracao(String codigoContratoDuracao) {
        this.codigoContratoDuracao = codigoContratoDuracao;
    }

    public String getCodigosCategoria() {
        return codigosCategoria;
    }

    public void setCodigosCategoria(String codigosCategoria) {
        this.codigosCategoria = codigosCategoria;
    }

    public String getCodigosConsultores() {
        return codigosConsultores;
    }

    public void setCodigosConsultores(String codigosConsultores) {
        this.codigosConsultores = codigosConsultores;
    }

    public String getCodigosModalidades() {
        return codigosModalidades;
    }

    public void setCodigosModalidades(String codigosModalidades) {
        this.codigosModalidades = codigosModalidades;
    }

    public String getCodigosPlanos() {
        return codigosPlanos;
    }

    public void setCodigosPlanos(String codigosPlanos) {
        this.codigosPlanos = codigosPlanos;
    }

    public String getCodigosProfessores() {
        return codigosProfessores;
    }

    public void setCodigosProfessores(String codigosProfessores) {
        this.codigosProfessores = codigosProfessores;
    }

    public String getListaSituacoes() {
        return listaSituacoes;
    }

    public void setListaSituacoes(String listaSituacoes) {
        this.listaSituacoes = listaSituacoes;
    }

    

    public boolean getMostrarIdade(){
        return !(idadeMin == 0 && idadeMax == 0);
    }

    public boolean getMostrarDataCadastro(){
        return dataCadastroMin != null || dataCadastroMax != null;
    }

    public Integer getCategoria() {
        return categoria;
    }

    public void setCategoria(Integer categoria) {
        this.categoria = categoria;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getEvento() {
        return evento;
    }

    public void setEvento(Integer evento) {
        this.evento = evento;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setMalaDireta(Integer malaDireta) {
        this.malaDireta = malaDireta;
    }

    public Integer getMalaDireta() {
        return malaDireta;
    }

    public void setFiltroVinculoCarteira(VinculoVO filtroVinculoCarteira) {
        this.filtroVinculoCarteira = filtroVinculoCarteira;
    }

    public VinculoVO getFiltroVinculoCarteira() {
        return filtroVinculoCarteira;
    }

    public void setFiltroModalidade(ModalidadeVO filtroModalidade) {
        this.filtroModalidade = filtroModalidade;
    }

    public ModalidadeVO getFiltroModalidade() {
        return filtroModalidade;
    }
    public Date getDataCadastroMax() {
        return dataCadastroMax;
    }

    public void setDataCadastroMax(Date dataCadastroMax) {
        this.dataCadastroMax = dataCadastroMax;
    }

    public Date getDataCadastroMin() {
        return dataCadastroMin;
    }

    public void setDataCadastroMin(Date dataCadastroMin) {
        this.dataCadastroMin = dataCadastroMin;
    }

    public boolean isFeminino() {
        return feminino;
    }

    public void setFeminino(boolean feminino) {
        this.feminino = feminino;
    }

    public int getIdadeMax() {

        return idadeMax;
    }

    public void setIdadeMax(int idadeMax) {
        this.idadeMax = idadeMax;
    }

    public int getIdadeMin() {
        return idadeMin;
    }

    public String getIdadeMaxStr() {
        return String.valueOf(idadeMax);
    }

    public String getIdadeMinStr() {
        return String.valueOf(idadeMin);
    }

    public void setIdadeMinStr(String idadeMinStr){
        try {
            setIdadeMin(Integer.valueOf(idadeMinStr));
        }catch (Exception e){
            setIdadeMin(0);
        }
    }

    public void setIdadeMaxStr(String idadeMaxStr){
        try {
            setIdadeMax(Integer.valueOf(idadeMaxStr));
        }catch (Exception e){
            setIdadeMax(0);
        }
    }

    public void setIdadeMin(int idadeMin) {
        this.idadeMin = idadeMin;
    }

    public boolean isMasculino() {
        return masculino;
    }

    public void setMasculino(boolean masculino) {
        this.masculino = masculino;
    }

    public boolean getSomenteRecorrencia() {
        return somenteRecorrencia;
    }

    public void setSomenteRecorrencia(boolean somenteRecorrencia) {
        this.somenteRecorrencia = somenteRecorrencia;
    }

}

package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import controle.basico.RiscoControle;
import controle.crm.RelatorioAgendamentosControle;
import controle.financeiro.MetaFinanceiroBIControle;
import negocio.comuns.utilitarias.BICarregarTO;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.financeiro.IndiceConversaoVendaRelControle;
import relatorio.controle.financeiro.IndiceConversaoVendaSessaoRelControle;
import relatorio.controle.sad.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class StartBICallable implements Callable<String> {

    private HttpServletRequest request;
    private HttpServletResponse response;
    private BIControle biControle;

    public StartBICallable(HttpServletRequest request, HttpServletResponse response, BIControle biControle) {
        this.request = request;
        this.response = response;
        this.biControle = biControle;
    }

    @Override
    public String call() throws Exception {
        try {
            JSFUtilities.getFacesContext(request, response);
            carregarBIAsync();
        }catch (Exception e){
            e.printStackTrace();
        }
        return "carregado";
    }

    public void carregarBIAsync() throws Exception{

        long time1 = System.currentTimeMillis();
        biControle.setBiCarregado(true);
        biControle.setExibirBIs(true);
        List<BICallable> callableTasks = new ArrayList<>();
        Boolean carregarTodos = true;
        if(!biControle.getConfiguracaoBI().getClientesVerificados().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getClientesVerificados().isCarregar(),carregarTodos)){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.CLIENTES_VERIFICADOS));
        }

        if(!biControle.getConfiguracaoBI().getPendencia().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getPendencia().isCarregar(),carregarTodos)){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.PENDENCIA));
        }

        if(!biControle.getConfiguracaoBI().getConversaoVenda().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getConversaoVenda().isCarregar(),carregarTodos)){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.CONVERSAO_VENDAS));
        }

        if (biControle.isApresentarBI_STUDIO()) {
            if (!biControle.getConfiguracaoBI().getConversaoVendaSS().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getConversaoVendaSS().isCarregar(), carregarTodos)) {
                    callableTasks.add(new BICallable(request,
                            response, BIEnum.CONVERSAO_VENDAS_SS));
            }
        }

        if(!biControle.getConfiguracaoBI().getMovContrato().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getMovContrato().isCarregar(),carregarTodos)){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.ROTATIVIDADE_CONTRATO));
        }


        if(!biControle.getConfiguracaoBI().getgRisco().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getgRisco().isCarregar(),carregarTodos)){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.GRUPO_RISCO));
        }

        if(!biControle.getConfiguracaoBI().getContratoR().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getContratoR().isCarregar(),carregarTodos)){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.DCC));
        }

        if(!biControle.getConfiguracaoBI().getTicketMedio().isNaLixeira()){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.METAS_FINANCEIRAS));
        }

        if(!biControle.getConfiguracaoBI().getControleOp().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getControleOp().isCarregar(),carregarTodos)){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.CONTROLE_OPERACOES));
        }

        if (biControle.isApresentarBI_IA()) {
            if (!biControle.getConfiguracaoBI().getProbEvas().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getProbEvas().isCarregar(), carregarTodos)) {
                    callableTasks.add(new BICallable(request,
                            response, BIEnum.PROBABILIDADE_EVASAO));
            }
        }

        if(!biControle.getConfiguracaoBI().getIndiceRenovacao().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getIndiceRenovacao().isCarregar(),carregarTodos)){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.INDICE_RENOVACAO));
        }

        if(!biControle.getConfiguracaoBI().getAulaExperimental().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getAulaExperimental().isCarregar(),carregarTodos)){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.AULA_EXPERIMENTAL));
        }


//        boolean exibirBI = biControle.getFacade().getFinanceiro().getPlanoConta().exibirBI(BIEnum.LTV.getIndice());
//        if (exibirBI) {
//            if (!biControle.getConfiguracaoBI().getLtv().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getLtv().isCarregar(), carregarTodos)) {
//                bisCarregar.add(new BICarregarTO(BIEnum.LTV, "Carregando LTV...", carregarTodos));
//            }
//        }

        if(!biControle.getConfiguracaoBI().getGestaoAcesso().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getGestaoAcesso().isCarregar(),carregarTodos)){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.GESTAO_ACESSO));
        }

        if(!biControle.getConfiguracaoBI().getInadimplencia().isNaLixeira() && biControle.validarCarregar(biControle.getConfiguracaoBI().getInadimplencia().isCarregar(),carregarTodos)){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.INADIMPLENCIA));
        }

        if(!biControle.getConfiguracaoBI().getTicketMedio().isNaLixeira()){
                callableTasks.add(new BICallable(request,
                        response, BIEnum.TICKET_MEDIO));

        }
        final ExecutorService executorService = Executors.newFixedThreadPool(16);
        executorService.invokeAll(callableTasks);
        executorService.shutdown();
        long time2 = System.currentTimeMillis();
        System.out.println((time2 - time1) + "ms");
    }

    
}

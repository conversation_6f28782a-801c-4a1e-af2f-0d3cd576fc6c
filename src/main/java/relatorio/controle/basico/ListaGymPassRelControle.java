
package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.faces.event.ActionEvent;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.GympassVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.PendenciaResumoPessoaRelVO;

/**
 * <AUTHOR>
 */
public class ListaGymPassRelControle extends SuperControleRelatorio {
    private GympassVO gympassVO;
    private Date dataInicial;
    private Date dataFinal;
    private List<PendenciaResumoPessoaRelVO> listaGymPass;
    private EmpresaVO empresaSelecionada;
    private String oncomplete;
    private Double valorTotal;

    public ListaGymPassRelControle() throws Exception {
        inicializarDados();
    }

    private void inicializarDados() throws Exception {
        listaGymPass = new ArrayList<PendenciaResumoPessoaRelVO>();
        setDataInicial(Calendario.hoje());
        setDataFinal(Calendario.hoje());
        montarListaEmpresas();
        obterUsuarioLogado();
        empresaSelecionada = new EmpresaVO();
    }

    public void gerarRelatorio() {
        try {
            if (getUsuarioLogado().getAdministrador()) {
                if (empresaSelecionada.getCodigo() != 0) {
                    listaGymPass = getFacade().getContratoOperacao().obterClientesComGymPassPorPeriodo(getDataInicial(), getDataFinal(), empresaSelecionada);
                } else {
                    throw new Exception("Selecione uma Empresa.");
                }
            } else {
                listaGymPass = getFacade().getContratoOperacao().obterClientesComGymPassPorPeriodo(getDataInicial(), getDataFinal(), getEmpresaLogado());
            }
            somaValoresLista();
            setOncomplete("abrirPopup('../relatorio/listaAcessoGymPassForm.jsp', 'ListaGymPassForm', 1000, 600);");
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
            setOncomplete("");
        }
    }

    private void somaValoresLista() {
        Collections.sort(listaGymPass);
        double somatorio = 0.0;
        for (PendenciaResumoPessoaRelVO aux : listaGymPass) {
            somatorio += aux.getValorEmAberto();
        }
        setValorTotal(somatorio);
    }

    public void irParaTelaCliente() {
        try {
            PendenciaResumoPessoaRelVO obj = (PendenciaResumoPessoaRelVO) context().getExternalContext().getRequestMap().get("itemLista");
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getFiltros() {
        return "";
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());

        List listaParaImpressao = getListaGymPass();
        exportadorListaControle.exportar(evt, listaParaImpressao, "",null );
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public List<PendenciaResumoPessoaRelVO> getListaGymPass() {
        return listaGymPass;
    }

    public void setListaGymPass(List<PendenciaResumoPessoaRelVO> listaGymPass) {
        this.listaGymPass = listaGymPass;
    }

    public EmpresaVO getEmpresaSelecionada() {
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(EmpresaVO empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public String getOncomplete() {
        if (oncomplete == null) {
            oncomplete = "";
        }
        return oncomplete;
    }

    public void setOncomplete(String oncomplete) {
        this.oncomplete = oncomplete;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getValorTotal_Apresentar() {
        return Formatador.formatarValorMonetario(valorTotal);
    }
}

package relatorio.controle.basico;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.basico.enumerador.TipoPendenciaEnum;
import negocio.comuns.basico.fabricas.PendenciaRelAbstractFactory;
import negocio.comuns.basico.fabricas.PendenciaRelQtdClientesMesmoCartao;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.ajax4jsf.event.AjaxEvent;
import org.json.JSONObject;
import org.richfaces.component.html.HtmlDataTable;
import relatorio.controle.financeiro.ProdutoRelControle;
import relatorio.negocio.comuns.basico.PendenciaRelVO;
import relatorio.negocio.comuns.basico.PendenciaResumoPessoaRelVO;
import relatorio.negocio.comuns.basico.ResultadoBITO;

import javax.faces.event.ActionEvent;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class PendenciaControleRel extends BIControle {
    private PendenciaRelVO pendenciaRelVO = new PendenciaRelVO();
    private List<PendenciaRelVO> listaPendenciaRelVOs = new ArrayList<PendenciaRelVO>();
    private boolean mostrarGrupos = false;
    private Boolean marcarUsuario;
    private Boolean sitAvencer;
    private Boolean sitAtivo;
    private Boolean sitCancelado;
    private Boolean sitInativo;
    private Boolean sitTrancado;
    private Boolean sitVencido;
    private Boolean sitVisitante;
    private String nomeCliente;
    private String colaboradoresSelecionados = "";
    private List<ColaboradorVO> listaColaboradoresSelecionados;
    private List<Integer> listaIdsColaboradoresSelecionados;
    private Boolean somenteClientesAtivos = false;
    private Boolean assinaturaCancelamento = false;
    private String paramTipoConsulta;
    private HtmlDataTable dataTable;
    private TipoPendenciaEnum pendenciaSelecionada = null;
    private boolean exibirTodos = false;

    PendenciaRelVO parcelasEmAtraso = new PendenciaRelVO();
    PendenciaRelVO parcelasAPagar = new PendenciaRelVO();
    PendenciaRelVO colaboradoresParcelasAPagar = new PendenciaRelVO();
    PendenciaRelVO creditoEmContaCorrente = new PendenciaRelVO();
    PendenciaRelVO debitoEmContaCorrente = new PendenciaRelVO();
    PendenciaRelVO produtosVencidos = new PendenciaRelVO();

    public PendenciaControleRel() throws Exception {
        novo();
    }

    public void inicializarDadosUsuario() throws Exception {
        if (getUsuarioLogado() != null && getUsuarioLogado().getCodigo() != 0) {
            getPendenciaRelVO().setUsuarioVO(new UsuarioVO());
            getPendenciaRelVO().getUsuarioVO().setCodigo(getUsuarioLogado().getCodigo());
            getPendenciaRelVO().getUsuarioVO().setNome(getUsuarioLogado().getNome());
            getPendenciaRelVO().getUsuarioVO().setAdministrador(getUsuarioLogado().getAdministrador());

        }
    }
    public void obterColunaOrdenacao(){
        String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dataTable);
        if(colunaOrdenacao.equals(""))
            return;
        else  if (!colunaOrdenacao.isEmpty()) {
            String[] params = colunaOrdenacao.split(":");
            String coluna = params[0].replace("col_","");
           getConfPaginacao().setOrdernar(true);
           getConfPaginacao().setColunaOrdenacao(coluna);
           getConfPaginacao().setDirecaoOrdenacao(params[1]);
        }
    }
    public final void novo() throws Exception {
        try {
            inicializarDadosUsuario();
            getConfPaginacao().setExistePaginacao(true);
            getConfPaginacao().setPaginaAtual(0);
            getConfPaginacao().setItensPorPagina(15);
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
            setDataBaseInicialFiltro(getEmpresaLogado().getLimiteInicialItensBIPendencia());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarPendenciaClienteMensagemPorColaboradores(boolean atualizarCache, boolean atualizarDadosMS) {
        try {
            atualizarCache = true;//FIXME Desabilita temporariamente a consulta no memcache como solicitado no ticket #10156
            ResultadoBITO resultado = obterResultadoBIDiaCache(BIEnum.PENDENCIA);
            if(validarResultadoBIDia(resultado) || atualizarCache){
                consultarPendenciaClienteMensagemMS(atualizarDadosMS);
                resultado = new ResultadoBITO();
                for(PendenciaRelVO pendencia : getListaPendenciaRelVOs()){
                    resultado.getResultadosBI().put(pendencia.getTipo().name(),pendencia.getTipo().getDescricao()+"<>"+pendencia.getQtd()+"<>"+pendencia.getValor());
                }
                adicionarResultadoBI(BIEnum.PENDENCIA,resultado);
            } else {
                setListaPendenciaRelVOs(new ArrayList<PendenciaRelVO>());
                for(String key : resultado.getResultadosBI().keySet()){
                    TipoPendenciaEnum tipo  = TipoPendenciaEnum.valueOf(key);
                    String el = (String)resultado.getResultadosBI().get(key);
                    PendenciaRelVO pendenciaRelVO = new PendenciaRelVO();
                    pendenciaRelVO.setTipo(tipo);
                    pendenciaRelVO.setEmpresaVO(getEmpresaLogado());
                    pendenciaRelVO.setQtd(new Integer(el.split("<>")[1]));
                    try {
                        pendenciaRelVO.setValor(new Double(el.split("<>")[2]));
                    }catch (Exception ignored){

                    }
                    getListaPendenciaRelVOs().add(pendenciaRelVO);
                }
            }

            List<PendenciaRelVO> pendenciasFinanceiro = new ArrayList<>();
            for(PendenciaRelVO pendencia : getListaPendenciaRelVOs()) {
                switch (pendencia.getTipo()) {
                    case ParcelaPendAPagar:
                        parcelasAPagar = new PendenciaRelVO(pendencia);
                        pendenciasFinanceiro.add(pendencia);
                        break;
                    case ParcelaPendAtraso:
                        parcelasEmAtraso = new PendenciaRelVO(pendencia);
                        pendenciasFinanceiro.add(pendencia);
                        break;
                    case DebitoCCorrente:
                        debitoEmContaCorrente = new PendenciaRelVO(pendencia);
                        pendenciasFinanceiro.add(pendencia);
                        break;
                    case CreditoCCorrente:
                        creditoEmContaCorrente = new PendenciaRelVO(pendencia);
                        pendenciasFinanceiro.add(pendencia);
                        break;
                    case ParcelaEmAbertoColaborador:
                        colaboradoresParcelasAPagar = new PendenciaRelVO(pendencia);
                        pendenciasFinanceiro.add(pendencia);
                        break;
                    case ClientesProdutosVencidos:
                        produtosVencidos = new PendenciaRelVO(pendencia);
                        pendenciasFinanceiro.add(pendencia);
                        break;
                }
            }
            // Remover os indicadores do topo
            for(PendenciaRelVO pendencia : pendenciasFinanceiro) {
                getListaPendenciaRelVOs().remove(pendencia);
            }

            Ordenacao.ordenarLista(getListaPendenciaRelVOs(),"tipo");

        }catch (Exception ex){
            System.out.print(ex);
        }
    }

    private ResultSet resolveResultSetCount(TipoPendenciaEnum tipo) throws Exception {
        return PendenciaRelAbstractFactory.getFactory(tipo,
                getFacade(),getColaboradoresSelecionados(),
                getEmpresaFiltroBI().getCodigo(), getConfPaginacao(),
                somenteClientesAtivos, assinaturaCancelamento).getCount();
    }

    private String resolveCondicaoColaboradoresVinculo() {
        getListaColaboradoresSelecionados().clear();
        getListaIdsColaboradoresSelecionados().clear();

        StringBuilder sql =  new StringBuilder(" vinculo.colaborador in (");
        boolean algumSelecionado = false;
        for (ColaboradorVO co : getListaColaboradorFiltroBI(BIEnum.PENDENCIA.name())) {
                if (co.getColaboradorEscolhido() ) {
                    getListaColaboradoresSelecionados().add(co);
                    getListaIdsColaboradoresSelecionados().add(co.getCodigo());
                    sql.append(",").append(co.getCodigo());
                    algumSelecionado = true;
                }
        }
        sql = new StringBuilder(sql.toString().replaceFirst(",",""));
        sql.append(") ");
        return  algumSelecionado ? sql.toString() : "";
    }

    public List<ColaboradorVO> getListaColaboradoresSelecionados() {
        if (listaColaboradoresSelecionados == null) {
            listaColaboradoresSelecionados = new ArrayList<ColaboradorVO>();        }
        return listaColaboradoresSelecionados;
    }

    public List<Integer> getListaIdsColaboradoresSelecionados() {
        if (listaIdsColaboradoresSelecionados == null) {
            listaIdsColaboradoresSelecionados = new ArrayList<Integer>();        }
        return listaIdsColaboradoresSelecionados;
    }

    public void consultaPaginadoOrdenacao(AjaxEvent ajaxEvent) {
        try {
            obterColunaOrdenacao();
            add(pendenciaRelVO);
        } catch (Exception err) { }
    }

    private ResultSet resolveResultSetList(PendenciaRelVO obj) throws Exception {
        if(obj.getTipo().getDescricao().equals(TipoPendenciaEnum.ClientesSemGeoLocalizacao.getDescricao())){
            // Rastreamento de click - Equipe Controle
            notificarRecursoEmpresa(RecursoSistema.BI_DE_ALUNOS_SEM_GEO_LOCALIZACAO);
        }
        PendenciaRelAbstractFactory pendenciaRel = PendenciaRelAbstractFactory.getFactory(obj.getTipo(), getFacade(),getColaboradoresSelecionados(),
                getEmpresaFiltroBI().getCodigo(), getConfPaginacao(), somenteClientesAtivos, assinaturaCancelamento);
        popularParametros(pendenciaRel, obj.getTipo());
        return pendenciaRel.getList();
    }

    private void popularParametros(PendenciaRelAbstractFactory pendenciaRel, TipoPendenciaEnum tipo) {
        if(tipo.equals(TipoPendenciaEnum.ClientesMesmoCartao)){
            pendenciaRel.adicionarParametro(PendenciaRelQtdClientesMesmoCartao.NOME_CLIENTE, this.nomeCliente);
        }
    }

    public void exportarPendencia(ActionEvent evt) throws Exception {
        limparMsg();
        try {
            setMsgAlert("");
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getManagedBean(ExportadorListaControle.class.getSimpleName());
            getConfPaginacao().setExistePaginacao(false);
            add(pendenciaRelVO);
            List listaParaImpressao = pendenciaRelVO.getListaPendenciaResumoPessoaRelVOs();
            evt.getComponent().getAttributes().put("prefixo", pendenciaRelVO.getTipo().name());
            evt.getComponent().getAttributes().put("titulo", pendenciaRelVO.getTipo().getDescricao());
            exportadorListaControle.exportar(evt, listaParaImpressao, "", pendenciaRelVO.getTipo().getItemExportacao());
            if (exportadorListaControle.getErro()) {
                throw new Exception(exportadorListaControle.getMensagemDetalhada());
            }
            String tipo = (String) JSFUtilities.getFromActionEvent("tipo", evt);

            setMsgAlert("abrirPopup('UpdateServlet?op=downloadfile&file="+exportadorListaControle.getFileName()+"&mimetype="+(tipo.equals("pdf") ? "application/pdf": "application/vnd.ms-excel")+"','Transacoes', 800,200);");
            getConfPaginacao().setExistePaginacao(true);
        } catch (Exception e){
            montarErro(e);
        }
    }
    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        getConfPaginacao().setExistePaginacao(false);
        add(pendenciaRelVO);
        List listaParaImpressao = pendenciaRelVO.getListaPendenciaResumoPessoaRelVOs();
        switch (pendenciaSelecionada) {
            case CreditoCCorrente:

                if(campoOrdenacao.equals("Matrícula")) {
                    Ordenacao.ordenarLista(listaParaImpressao, "matricula_Apresentar");
                } else if (campoOrdenacao.equals("Nome")) {
                    Ordenacao.ordenarLista(listaParaImpressao, "nome_Apresentar");
                } else if (campoOrdenacao.equals("Situação")) {
                    Ordenacao.ordenarLista(listaParaImpressao, "situacao_Apresentar");
                } else if (campoOrdenacao.equals("Valor do Crédito")) {
                    Ordenacao.ordenarLista(listaParaImpressao, "valorCC");
                } else if (campoOrdenacao.equals("Data de Lançamento")) {
                    Ordenacao.ordenarLista(listaParaImpressao, "dataRegistro");
                }

                if(ordem.equals("desc")) {
                    Collections.reverse(listaParaImpressao);
                }
                break;
        }
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    private void add(TipoPendenciaEnum tipo) throws Exception {
        ResultSet rs = resolveResultSetCount(tipo);
        PendenciaRelVO obj = new PendenciaRelVO();
        obj.setUsuarioVO(getUsuarioLogado());
        if (rs.next()) {
            obj.setQtd(rs.getInt("qtd"));
            obj.setTipo(tipo);
            try {
                if (rs.findColumn("total") != 0) {
                    obj.setValor(rs.getDouble("total"));
                }
            } catch (Exception ignored) { }

            if (tipo == TipoPendenciaEnum.DebitoCCorrente && obj.getValor() != 0.0) {
                obj.setValor(obj.getValor() * (-1));
            }
        }
        Uteis.logar(null,tipo.name());
        if (obj.getQtd() > 0 || !PendenciaRelAbstractFactory.getFactory(tipo).ignorarSeForZero())
            getListaPendenciaRelVOs().add(obj);
    }

    private void addMS(TipoPendenciaEnum tipo, JSONObject dados) throws Exception {
        PendenciaRelVO obj = new PendenciaRelVO();
        obj.setUsuarioVO(getUsuarioLogado());
        obj.setTipo(tipo);
        switch (tipo) {
            case ParcelaPendAPagar:
                obj.setQtd(dados.getInt("qtdParcelasAPagar"));
                obj.setValor(dados.getDouble("valorParcelasAPagar"));
                break;
            case ParcelaPendAtraso:
                obj.setQtd(dados.getInt("qtdParcelasEmAtraso"));
                obj.setValor(dados.getDouble("valorParcelasEmAtraso"));
                break;
            case DebitoCCorrente:
                obj.setQtd(dados.getInt("qtdDebitoContaCorrente"));
                obj.setValor(dados.getDouble("valorDebitoContaCorrente"));
                if (obj.getValor() != 0.0) {
                    obj.setValor(obj.getValor() * (-1));
                }
                break;
            case CreditoCCorrente:
                obj.setQtd(dados.getInt("qtdCreditoContaCorrente"));
                obj.setValor(dados.getDouble("valorCreditoContaCorrente"));
                break;
            case ParcelaEmAbertoColaborador:
                obj.setQtd(dados.getInt("qtdParcelasColaborador"));
                obj.setValor(dados.getDouble("valorParcelasColaborador"));
                break;
            case ClientesProdutosVencidos:
                obj.setQtd(dados.getInt("qtdProdutosVencidos"));
                obj.setValor(dados.getDouble("valorProdutosVencidos"));
                break;
            case Aniversariantes:
                obj.setQtd(dados.getInt("qtdAniversariantesClientes"));
                break;
            case AniversariantesColaborador:
                obj.setQtd(dados.getInt("qtdAniversariantesColaboradores"));
                break;
            case BVPendente:
                obj.setQtd(dados.getInt("qtdBVPendente"));
                break;
            case CadastroIncompletoVisitante:
                obj.setQtd(dados.getInt("qtdCadastroIncompletoVisitantes"));
                break;
            case CadastroIncompletoCliente:
                obj.setQtd(dados.getInt("qtdCadastroIncompletoCliente"));
                break;
            case ClientesSemAssinaturaDigital:
                obj.setQtd(dados.getInt("qtdSemAssinaturaDigital"));
                break;
            case ClientesSemAssinaturaDigitalCancelamento:
                obj.setQtd(dados.getInt("qtdSemAssinaturaDigitalCancelamento"));
                break;
            case ClientesSemBiometriaFacial:
                obj.setQtd(dados.getInt("qtdSemReconhecimentoFacial"));
                break;
            case ClientesSemFoto:
                obj.setQtd(dados.getInt("qtdSemFoto"));
                break;
            case ClientesSemGeoLocalizacao:
                obj.setQtd(dados.getInt("qtdSemGeolocalizacao"));
                break;
            case ClientesSemProdutos:
                obj.setQtd(dados.getInt("qtdSemProdutos"));
                break;
            case ClientesTrancamentoVencidos:
                obj.setQtd(dados.getInt("qtdTrancamentoVencido"));
                break;
        }
        Uteis.logar(null,tipo.name());
        if (obj.getQtd() > 0 || !PendenciaRelAbstractFactory.getFactory(tipo).ignorarSeForZero())
            getListaPendenciaRelVOs().add(obj);
    }

    public void add(PendenciaRelVO obj) throws Exception {
        if (obj.getQtd() == 0 && PendenciaRelAbstractFactory.getFactory(obj.getTipo()).ignorarSeForZero())
            return;

        ResultSet rs = resolveResultSetList(obj);
        obj.getListaPendenciaResumoPessoaRelVOs().clear();
        while (rs.next()) {
            PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
            try {
                resumo.setMoeda(getEmpresaLogado().getMoeda() + " ");
                resumo.getClienteVO().setCodigo(rs.getInt("cli"));
                resumo.getClienteVO().setMatricula(rs.getString("matriculacli"));
                resumo.getClienteVO().getPessoa().setCodigo(rs.getInt("codPessoa"));
                resumo.getClienteVO().getPessoa().setNome(rs.getString("nome"));
                resumo.getClienteVO().setSituacao(rs.getString("situacaoCliente"));
                resumo.getClienteVO().getPessoa().setNumeroTelefonesApresentar(rs.getString("telefonescliente"));
                resumo.setDataNasc_Apresentar(rs.getString("datanascimento"));
                resumo.setDataBV(rs.getDate("dataBV"));
                resumo.setTipoBV(TipoBVEnum.getTipo(rs.getInt("tipoBV")).getDescricao());
            } catch (Exception ignored) {
                ignored.printStackTrace();
            }
            try{
                resumo.getClienteVO().getPessoa().setCfp(rs.getString("cpf"));
            }catch (Exception e){
                //Não e necessário tratar essa excessão.
            }
            if (obj.getTipo() == TipoPendenciaEnum.ParcelaPendAtraso || obj.getTipo() == TipoPendenciaEnum.ParcelaPendAPagar) {
                resumo.setValorEmAberto(rs.getDouble("valorEmAberto"));
                obj.setExibirInformacoesPessoa(true);
                obj.setExibirInformacoesFinanceiras(true);
            }
            if (obj.getTipo() == TipoPendenciaEnum.ParcelaPendAtraso){
                resumo.setQtdParcelaEmAtraso(rs.getInt("qtdParcelas"));
            }
            if (obj.getTipo() == TipoPendenciaEnum.ClientesMesmoCartao){
                resumo.setNomeEmpresa(rs.getString("nomeEmpresaCliente"));
            }
            if (obj.getTipo() == TipoPendenciaEnum.CartoesComProblema){
                obj.setExibirInformacoesFinanceiras(false);

                resumo.setProblemaCartao(rs.getString("situacaoCartao"));

                String cartao = rs.getString("cartaomascaradointerno");
                if (!UteisValidacao.emptyString(cartao)) {
                    resumo.setAutorizacaoCobrancaCliente("Cartão: " + cartao + " Validade: " + rs.getString("validadecartao"));
                } else {
                    resumo.setAutorizacaoCobrancaCliente("");
                }
            }
            if (obj.getTipo() == TipoPendenciaEnum.CreditoCCorrente) {
                resumo.setValorCC(rs.getDouble("saldoAtual"));
                resumo.setDataRegistro(rs.getDate("lancamento"));
            }
            if (obj.getTipo() == TipoPendenciaEnum.ParcelaEmAbertoColaborador ||
                    obj.getTipo() == TipoPendenciaEnum.AniversariantesColaborador) {
                ColaboradorVO col = new ColaboradorVO();
                col.setCodigo(rs.getInt("codigoColaborador"));
                col.setSituacao(rs.getString("situacao"));
                col.getPessoa().setNome(rs.getString("nome"));
                col.getPessoa().setCodigo(rs.getInt("pessoa"));
                try{
                    col.getPessoa().setCfp(rs.getString("cpf"));
                }catch (Exception e){
                    //Não e necessário tratar essa excessão.
                }
                resumo.setColaboradorVO(col);
            }
            if(obj.getTipo() == TipoPendenciaEnum.DebitoCCorrente){
                resumo.setValorCC(rs.getDouble("saldoAtual"));
                resumo.setDataRegistro(rs.getDate("dataRegistro"));
            }
            if (obj.getTipo() == TipoPendenciaEnum.Aniversariantes){
                resumo.getClienteVO().getPessoa().setDataNasc(rs.getDate("datanascimento"));
            }
            try {
                Integer contrato = rs.getInt("codContrato");
                resumo.getContratoVO().setCodigo(contrato);
                resumo.getContratoVO().setVigenciaDe(rs.getDate("dataInicio"));
                resumo.getContratoVO().setVigenciaAteAjustada(rs.getDate("dataFim"));
                resumo.getContratoVO().getContratoDuracao().setNumeroMeses(rs.getInt("duracaoContrato"));
                resumo.getContratoVO().setNomeModalidades(rs.getString("nomeModalidades"));
                resumo.getContratoVO().getPlano().setDescricao(rs.getString("nomePlano"));
            } catch (Exception ignored) {
            }
            try {
                resumo.setConteudoMensagem(PendenciaRelAbstractFactory.getFactory(obj.getTipo()).trataMensagem(rs.getString("mensagem")));
            } catch (Exception ignored){}
        // getFacade().getContrato().consultaPlanoDuracao(resumo.getClienteVO().getPessoa().getCodigo(), resumo.getContratoVO());
            if(obj.getTipo() == TipoPendenciaEnum.CartoesVencidos || obj.getTipo() == TipoPendenciaEnum.CartoesAVencer
                    || obj.getTipo() == TipoPendenciaEnum.ClientesMesmoCartao){
                obj.setExibirInformacoesFinanceiras(false);
                try {
                    resumo.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(rs.getInt("cli"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    String str = "";
                    if(!resumo.getClienteVO().getAutorizacoes().isEmpty()) {
                        for (AutorizacaoCobrancaClienteVO cartoes : resumo.getClienteVO().getAutorizacoes()) {
                            if(cartoes.getTipoAutorizacao() != null && cartoes.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)){
                                str += "Cartão: " + cartoes.getCartaoMascarado_Apresentar() + " Validade: " + cartoes.getValidadeCartao() + " ";
                            }
                        }
                        resumo.setAutorizacaoCobrancaCliente(str);
                    }
                } catch (Exception ignored) {  }
            }
            if(!obj.getListaPendenciaResumoPessoaRelVOs().contains(resumo))
                obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
        }
        obj.ajustaQuantidade();
        getConfPaginacao().setNumeroTotalItens(obj.getQtd());
        int nrTotalPag = obj.getQtd() / 15;
        if(obj.getQtd() % 15 > 0){
            nrTotalPag++;
        }
        getConfPaginacao().setPaginaAtualDeTodas((getConfPaginacao().getPaginaAtual()+1)+"/"+nrTotalPag);
        setPendenciaRelVO(obj);
    }

    public void consultarPendenciaClienteMensagemMS(boolean atualizarDadosMS) throws Exception {
        getListaPendenciaRelVOs().clear();
        setColaboradoresSelecionados(resolveCondicaoColaboradoresVinculo());

        FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(), BIEnum.PENDENCIA, getFiltroDTO(atualizarDadosMS), atualizarDadosMS);
        JSONObject dados = new JSONObject(filtroDTO.getJsonDados());
        TipoPendenciaEnum[] tipos = TipoPendenciaEnum.values();
        for (TipoPendenciaEnum tipo : tipos) {
            if (tipo != TipoPendenciaEnum.NENHUM && tipo !=
                    TipoPendenciaEnum.CartoesVencidos &&
                    tipo != TipoPendenciaEnum.CartoesAVencer &&
                    tipo != TipoPendenciaEnum.ClientesTransferidosContratoCancelado &&
                    tipo != TipoPendenciaEnum.ClientesMesmoCartao &&
                    tipo != TipoPendenciaEnum.CartoesComProblema) {
                addMS(tipo,dados);
            }
        }
    }

    public void consultarPendenciaClienteMensagem() throws Exception {

        getListaPendenciaRelVOs().clear();
        setColaboradoresSelecionados(resolveCondicaoColaboradoresVinculo());
        TipoPendenciaEnum[] tipos = TipoPendenciaEnum.values();
        for (TipoPendenciaEnum tipo : tipos) {
            if (tipo != TipoPendenciaEnum.NENHUM && tipo !=
                    TipoPendenciaEnum.CartoesVencidos &&
                    tipo != TipoPendenciaEnum.CartoesAVencer &&
                    tipo != TipoPendenciaEnum.ClientesTransferidosContratoCancelado &&
                    tipo != TipoPendenciaEnum.ClientesMesmoCartao &&
                    tipo != TipoPendenciaEnum.CartoesComProblema) {
                add(tipo);
            }
        }
    }

    public void selecionarPendenciaListener(ActionEvent evt) {
        try {
            limparMsg();
            setColaboradoresSelecionados(resolveCondicaoColaboradoresVinculo());

            PendenciaRelVO pendencia = (PendenciaRelVO) evt.getComponent().getAttributes().get("pendencia");
            pendencia.setEmpresaVO(getEmpresaLogado());
            getConfPaginacao().setExistePaginacao(true);
            getConfPaginacao().setPaginaAtual(0);
            getConfPaginacao().setItensPorPagina(15);
            getConfPaginacao().setColunaOrdenacao("");
            getConfPaginacao().setOrdernar(false);
            if (pendencia != null) {
                setPendenciaSelecionada(pendencia.getTipo());
                if (pendencia.getTipo() == TipoPendenciaEnum.ClientesSemAssinaturaDigitalCancelamento) {
                    setAssinaturaCancelamento(true);
                }
                try {
                    if (pendencia.getQtd() != 0) {
                        if (pendencia.getTipo() == TipoPendenciaEnum.ClientesSemProdutos) {
                            ProdutoRelControle produtoRelControle = (ProdutoRelControle) getControlador(ProdutoRelControle.class);
                            String situacoes[] = {SituacaoClienteEnum.ATIVO.getCodigo()};
                            produtoRelControle.setSituacoes(situacoes);
                            produtoRelControle.setColaboradoresPendencia(getColaboradoresSelecionados());
                            produtoRelControle.getProdutoRel().setSemProdutos(true);
                            produtoRelControle.getProdutoRel().setDataInicioCadastroCliente(getDataBaseInicialFiltro());
                            produtoRelControle.imprimirRelatorioPendencia();
                        } else if (pendencia.getTipo() == TipoPendenciaEnum.ClientesProdutosVencidos) {
                            ProdutoRelControle produtoRelControle = (ProdutoRelControle) getControlador(ProdutoRelControle.class);
                            produtoRelControle.setColaboradoresPendencia(getColaboradoresSelecionados());
                            String situacoes[] = {SituacaoClienteEnum.ATIVO.getCodigo()};
                            produtoRelControle.setSituacoes(situacoes);
                            produtoRelControle.setFiltroEmpresa(getEmpresaFiltroBI().getCodigo());
                            produtoRelControle.getProdutoRel().setDataFinalVenc(Uteis.somarDias(getDataBaseFiltroBI(), -1));
                            produtoRelControle.getProdutoRel().setTipoProduto("");
                            produtoRelControle.getProdutoRel().setSemProdutos(false);
                            produtoRelControle.getProdutoRel().setDataInicioVenc(getDataBaseInicialFiltro());
                            produtoRelControle.imprimirRelatorioPendencia();
                        } else {
                            if(pendencia.getTipo() != TipoPendenciaEnum.CreditoCCorrente){//montagem dessa pendencia é por servlet
                                add(pendencia);
                            } else {
                                setPendenciaRelVO(pendencia);
                            }
                        }
                    }
                    setMensagem("");
                    setMensagemID("");
                    setMensagemDetalhada("", "");
                } catch (Exception e) {
                    setMensagemDetalhada("msg_erro", e.getMessage());
                }
            } else {
                setPendenciaSelecionada(null);
            }
        }catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void filtrarPorPendenciaPorEmpresa() {
        try {
            consultarPendenciaClienteMensagemPorColaboradores(true, true);
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void irParaTelaCliente() {
        PendenciaResumoPessoaRelVO obj = (PendenciaResumoPessoaRelVO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(obj.getClienteVO());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void irParaTelaClienteDatatables() {
        try {
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
            if (codigoConsulta == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                ClienteVO obj = new ClienteVO();
                obj.setCodigo(codigoConsulta);

                irParaTelaCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    /**
     * Método que verifica qual colaborador deverá ser mostrado na tela
     */
    public void irParaTelaColaborador() {
        PendenciaResumoPessoaRelVO obj = (PendenciaResumoPessoaRelVO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        try {
            if (obj == null) {
                throw new Exception("Colaborador Não Encontrado.");
            } else {
                obj.setColaboradorVO(getFacade().getColaborador().consultarPorChavePrimaria(obj.getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                irParaTelaColaborador(obj.getColaboradorVO());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void toggleMostrarGrupos() {
        setMostrarGrupos(!isMostrarGrupos());
    }

    public boolean isMostrarGrupos() {
        return mostrarGrupos;
    }

    public void setMostrarGrupos(boolean mostrarGrupos) {
        this.mostrarGrupos = mostrarGrupos;
    }

    public PendenciaRelVO getPendenciaRelVO() {
        return pendenciaRelVO;
    }

    public String getAtributosExportar() {
        StringBuilder atributos = new StringBuilder();
        atributos.append("matricula_Apresentar=Matrícula,nome_Apresentar=Nome,cpf_Apresentar=CPF,situacao_Apresentar=Situação,valorCC_Apresentar=Valor Debito,dataNasc_Apresentar=Dt.Nascimento");
        if (pendenciaRelVO.getExibirDebitoCC())
            atributos.append(",dataRegistro_Apresentar=Data Lançamento");
        if (pendenciaRelVO.isExibirAutorizacaoCobrancaCliente())
            atributos.append(",autorizacaoCobrancaCliente=Autorização Cobrança");
        if (!pendenciaRelVO.isExibirAutorizacaoCobrancaCliente())
            atributos.append(",planoApresentar=Plano,duracaoApresentar=Duração");
        if (pendenciaRelVO.isExibirMensagem())
            atributos.append(",conteudoMensagem=Mensagem");
        if (pendenciaRelVO.isExibirQtdParcelasEmAtraso())
            atributos.append(",qtdParcelaEmAtraso=Qtd. Parc. Atrasadas");
        if (pendenciaRelVO.isExibirInformacoesFinanceiras())
            atributos.append(",valorEmAberto_Apresentar=Valor");
        if (pendenciaRelVO.isExibirInformacoesPessoa())
            atributos.append(",telefone_Apresentar=Telefone");
        if (pendenciaRelVO.isExibirProblemaCartao())
            atributos.append(",problemaCartao=Inf. Incompleta");
        return atributos.toString();
    }

    public String getItemExportar() {
        if(pendenciaRelVO.getTipo() != null && pendenciaRelVO.getTipo().equals(TipoPendenciaEnum.NENHUM)){
            return "";
        }
        return pendenciaRelVO.getTipo().getItemExportacao().getId();

    }

    public void proximaPagina(){
        try {
            int i = getConfPaginacao().getPaginaAtual();
            if (i < (getConfPaginacao().getNrTotalPaginas()-1)) {
                i++;
                getConfPaginacao().setPaginaAtual(i);
                add(pendenciaRelVO);
            }
        }catch (Exception ex){
            montarErro(ex);
        }
    }
    public void paginaAnterior(){
        try{
        int i = getConfPaginacao().getPaginaAtual();
        if(i > 0){
            i--;
            getConfPaginacao().setPaginaAtual(i);
            add(pendenciaRelVO);
        }

       }catch (Exception ex){
        montarErro(ex);
      }

    }
    public void primeiraPagina(){
        try {
            int i = getConfPaginacao().getPaginaAtual();
            if (i != 0) {
                getConfPaginacao().setPaginaAtual(0);
                add(pendenciaRelVO);
            }
        }catch (Exception ex){
            montarErro(ex);
        }
    }
    public void ultimaPagina(){
        try {
            int i = getConfPaginacao().getPaginaAtual();
            if (i != getConfPaginacao().getNrTotalPaginas()) {
                getConfPaginacao().setPaginaAtual(getConfPaginacao().getNrTotalPaginas()-1);
                add(pendenciaRelVO);
            }
        }catch (Exception ex){
            montarErro(ex);
        }
    }
    public Boolean getBIClientesMesmoCartao(){
        return this.pendenciaRelVO != null && this.pendenciaRelVO.getTipo() != null && this.pendenciaRelVO.getTipo().equals(TipoPendenciaEnum.ClientesMesmoCartao);
    }
    public void setPendenciaRelVO(PendenciaRelVO pendenciaRelVO) {
        this.pendenciaRelVO = pendenciaRelVO;
    }

    public List<PendenciaRelVO> getListaPendenciaRelVOs() {
        return listaPendenciaRelVOs;
    }

    public void setListaPendenciaRelVOs(List<PendenciaRelVO> listaPendenciaRelVOs) {
        this.listaPendenciaRelVOs = listaPendenciaRelVOs;
    }

    public String inicializarPendenciaControleRel() {
        return "";
    }

    public void setMarcarUsuario(Boolean marcarUsuario) {
        this.marcarUsuario = marcarUsuario;
    }

    public Boolean getSitAvencer() {
        return sitAvencer;
    }

    public void setSitAvencer(Boolean sitAvencer) {
        this.sitAvencer = sitAvencer;
    }

    public Boolean getSitAtivo() {
        return sitAtivo;
    }

    public void setSitAtivo(Boolean sitAtivo) {
        this.sitAtivo = sitAtivo;
    }

    public Boolean getSitCancelado() {
        return sitCancelado;
    }

    public void setSitCancelado(Boolean sitCancelado) {
        this.sitCancelado = sitCancelado;
    }

    public Boolean getSitInativo() {
        return sitInativo;
    }

    public void setSitInativo(Boolean sitInativo) {
        this.sitInativo = sitInativo;
    }

    public Boolean getSitTrancado() {
        return sitTrancado;
    }

    public void setSitTrancado(Boolean sitTrancado) {
        this.sitTrancado = sitTrancado;
    }

    public Boolean getSitVencido() {
        return sitVencido;
    }

    public void setSitVencido(Boolean sitVencido) {
        this.sitVencido = sitVencido;
    }

    public Boolean getSitVisitante() {
        return sitVisitante;
    }

    public void setSitVisitante(Boolean sitVisitante) {
        this.sitVisitante = sitVisitante;
    }

    public TipoPendenciaEnum getPendenciaSelecionada() {
        return pendenciaSelecionada;
    }

    public void setPendenciaSelecionada(TipoPendenciaEnum pendenciaSelecionada) {
        this.pendenciaSelecionada = pendenciaSelecionada;
    }

    public String getColaboradoresSelecionados() {
        return colaboradoresSelecionados;
    }

    public void setColaboradoresSelecionados(String colaboradoresSelecionados) {
        this.colaboradoresSelecionados = colaboradoresSelecionados;
    }

    public Boolean getSomenteClientesAtivos() {
        return somenteClientesAtivos;
    }

    public void setSomenteClientesAtivos(Boolean somenteClientesAtivos) {
        this.somenteClientesAtivos = somenteClientesAtivos;
    }

    public Boolean getAssinaturaCancelamento() {
        return assinaturaCancelamento;
    }

    public void setAssinaturaCancelamento(Boolean assinaturaCancelamento) {
        this.assinaturaCancelamento = assinaturaCancelamento;
    }

    public boolean isExibirTodos() {
        return exibirTodos;
    }

    public void setExibirTodos(boolean exibirTodos) {
        this.exibirTodos = exibirTodos;
    }

    public HtmlDataTable getDataTable() {
        return dataTable;
    }

    public void setDataTable(HtmlDataTable dataTable) {
        this.dataTable = dataTable;
    }

    public String getParamTipoConsulta() {
        return paramTipoConsulta;
    }

    public void setParamTipoConsulta(String paramTipoConsulta) {
        this.paramTipoConsulta = paramTipoConsulta;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public void atualizarConsulta() throws Exception {
        if (getEmpresaLogado() != null && getEmpresaLogado().getCodigo() > 0) {
            EmpresaVO empresaUsuarioLogado = getFacade().getEmpresa()//
                    .consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setDataBaseInicialFiltro(empresaUsuarioLogado.getLimiteInicialItensBIPendencia());
        }
        filtrarPorPendenciaPorEmpresa();
        gravarHistoricoAcessoBI(BIEnum.PENDENCIA);
    }

    public void atualizarDataBaseLimiteFiltroBI() throws Exception {
        final Date dataBaseInicialFiltroInformado = getDataBaseInicialFiltro();
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                getFacade().getEmpresa().atualizarLimiteInicialItensBIPendencia(getEmpresaLogado(), dataBaseInicialFiltroInformado);
                atualizarConsulta();
                montarSucessoGrowl("Data limite alterada com sucesso!");
                setExecutarAoCompletar(getMensagemNotificar());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                cancelarAlteracaoLimiteInicialItensBI();
            }

            @Override
            public void onFecharModalAutorizacao() {
                cancelarAlteracaoLimiteInicialItensBI();
            }

            private void cancelarAlteracaoLimiteInicialItensBI() {
                try {
                    EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    setDataBaseInicialFiltro(empresaVO.getLimiteInicialItensBIPendencia());
                } catch (Exception e) {
                    montarErro(e);
                }
            }
        };
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        auto.autorizar("Alterar data limite",
                "Empresa",
                "Você precisa da permissão \"2.10 - Empresa\" para realizar esta ação",
                "bi-container-pendencia",
                listener);
    }

    public void removerDataBaseLimiteFiltroBI() throws Exception {
        setDataBaseInicialFiltro(null);
        atualizarDataBaseLimiteFiltroBI();
    }

    public PendenciaRelVO getParcelasEmAtraso() {
        return parcelasEmAtraso;
    }

    public void setParcelasEmAtraso(PendenciaRelVO parcelasEmAtraso) {
        this.parcelasEmAtraso = parcelasEmAtraso;
    }

    public PendenciaRelVO getParcelasAPagar() {
        return parcelasAPagar;
    }

    public void setParcelasAPagar(PendenciaRelVO parcelasAPagar) {
        this.parcelasAPagar = parcelasAPagar;
    }

    public PendenciaRelVO getColaboradoresParcelasAPagar() {
        return colaboradoresParcelasAPagar;
    }

    public void setColaboradoresParcelasAPagar(PendenciaRelVO colaboradoresParcelasAPagar) {
        this.colaboradoresParcelasAPagar = colaboradoresParcelasAPagar;
    }

    public PendenciaRelVO getCreditoEmContaCorrente() {
        return creditoEmContaCorrente;
    }

    public void setCreditoEmContaCorrente(PendenciaRelVO creditoEmContaCorrente) {
        this.creditoEmContaCorrente = creditoEmContaCorrente;
    }

    public PendenciaRelVO getDebitoEmContaCorrente() {
        return debitoEmContaCorrente;
    }

    public void setDebitoEmContaCorrente(PendenciaRelVO debitoEmContaCorrente) {
        this.debitoEmContaCorrente = debitoEmContaCorrente;
    }

    public PendenciaRelVO getProdutosVencidos() {
        return produtosVencidos;
    }

    public void setProdutosVencidos(PendenciaRelVO produtosVencidos) {
        this.produtosVencidos = produtosVencidos;
    }

    private FiltroDTO getFiltroDTO(boolean atualizarAgora) {
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.PENDENCIA.name());
        JSONObject filtros = new JSONObject();
        filtros.put("atualizarAgora", atualizarAgora);
        filtros.put("dataBase", Calendario.getDataComHoraZerada(getDataBaseFiltroBI()).getTime());
        if (getDataBaseInicialFiltro() != null) {
            filtros.put("dataLimite", LocalDate.parse(getDataBaseInicialFiltro().toString())
                    .atStartOfDay().toEpochSecond(ZoneOffset.UTC));
        }
        if (UteisValidacao.notEmptyNumber(getEmpresaFiltroBI().getCodigo())) {
            filtros.put("empresa",getEmpresaFiltroBI().getCodigo());
        }

        List<Integer> colaboradores = getListaIdsColaboradoresSelecionados();
        filtros.put("colaboradores", colaboradores);

        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }
}

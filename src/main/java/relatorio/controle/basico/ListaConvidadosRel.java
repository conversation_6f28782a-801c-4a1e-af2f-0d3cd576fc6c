/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConviteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ListaConvidadosRel extends SuperControleRelatorio{

    private Date dataInicial;
    private Date dataFinal;
    private List<ConviteVO> listaConvidados;
    private EmpresaVO empresaSelecionada;

    public ListaConvidadosRel() throws Exception {
        inicializarDados();
    }

    private void inicializarDados() throws Exception{
        listaConvidados = new ArrayList<ConviteVO>();
        setDataInicial(Uteis.obterPrimeiroDiaMes(Calendario.hoje()));
        setDataFinal(Uteis.obterUltimoDiaMes(Calendario.hoje()));
        montarListaEmpresas();
        obterUsuarioLogado();
        empresaSelecionada = new EmpresaVO();
    }
    
    public void gerarRelatorio(){
        try {
            if (getUsuarioLogado().getAdministrador()) {
                if (empresaSelecionada.getCodigo() != 0) {
                    listaConvidados = getFacade().getConvite().relatorio(dataInicial, dataFinal, empresaSelecionada.getCodigo());
                }else{
                   throw new Exception("Selecione uma Empresa."); 
                }
            }else{
                listaConvidados = getFacade().getConvite().relatorio(dataInicial, dataFinal, 0);
            }
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        
        List listaParaImpressao = getListaConvidados();
        exportadorListaControle.exportar(evt, listaParaImpressao, "",null);
    }

    public void irParaTelaCliente() throws Exception {
        ConviteVO obj = (ConviteVO)  context().getExternalContext().getRequestMap().get("conv");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                ClienteVO clienteCons = new ClienteVO();
                clienteCons.setCodigo(obj.getConvidado().getCodigo());
                irParaTelaCliente(clienteCons);
            }
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }
    
    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public EmpresaVO getEmpresaSelecionada() {
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(EmpresaVO empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public List<ConviteVO> getListaConvidados() {
        return listaConvidados;
    }

    public void setListaConvidados(List<ConviteVO> listaConvidados) {
        this.listaConvidados = listaConvidados;
    }
}




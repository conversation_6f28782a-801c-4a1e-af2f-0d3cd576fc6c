
package relatorio.controle.basico;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.PessoaBloqueioCobrancaTO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 28/04/2020
 */
public class RelatorioPessoasBloqueioCobrancaControle extends SuperControleRelatorio {

    private Integer empresaFiltro;
    private String oncomplete;
    private List<PessoaBloqueioCobrancaTO> lista;
    private int scrollerPage;
    private List<SelectItem> listaSelectItemEmpresa;
    private Date dataInicio;
    private Date dataFinal;
    private boolean consultou = false;

    public RelatorioPessoasBloqueioCobrancaControle() throws Exception {
        inicializarDados();
    }

    public void abrir() throws Exception {
        inicializarDados();
    }

    private void inicializarDados() throws Exception {
        limparMsg();
        setOncomplete("");
        setConsultou(false);
        setLista(new ArrayList<>());
        obterUsuarioLogado();
        montarSelectItemEmpresa();
    }

    private void montarSelectItemEmpresa() throws Exception {
        setListaSelectItemEmpresa(new ArrayList<>());
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarTodas(null, Uteis.NIVELMONTARDADOS_MINIMOS);
        for (EmpresaVO empresaVO : empresas) {
            getListaSelectItemEmpresa().add(new SelectItem(empresaVO.getCodigo(), empresaVO.getNome()));
        }
        Ordenacao.ordenarLista(getListaSelectItemEmpresa(), "label");
        getListaSelectItemEmpresa().add(0, new SelectItem(0, ""));
    }

    public void irParaTelaCliente() {
        try {
            limparMsg();
            setOncomplete("");

            PessoaBloqueioCobrancaTO obj = (PessoaBloqueioCobrancaTO) context().getExternalContext().getRequestMap().get("cliente");
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente(), Uteis.NIVELMONTARDADOS_TODOS));
                setOncomplete("abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);");
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void consultarClientes() {
        try {
            limparMsg();
            setOncomplete("");
            setLista(new ArrayList<>());
            setConsultou(false);

            setLista(getFacade().getPessoa().consultarPessoasComCobrancaBloqueada(getEmpresaFiltro(), getDataInicio(), getDataFinal()));
            setConsultou(true);
            if (UteisValidacao.emptyList(getLista())) {
                throw new Exception("Nenhum cliente encontrado.");
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public String getOncomplete() {
        if (oncomplete == null) {
            oncomplete = "";
        }
        return oncomplete;
    }

    public void setOncomplete(String oncomplete) {
        this.oncomplete = oncomplete;
    }

    public int getScrollerPage() {
        return scrollerPage;
    }

    public void setScrollerPage(int scrollerPage) {
        this.scrollerPage = scrollerPage;
    }

    public Integer getListaTotal() {
        return getLista().size();
    }

    public List<PessoaBloqueioCobrancaTO> getLista() {
        if (lista == null) {
            lista = new ArrayList<PessoaBloqueioCobrancaTO>();
        }
        return lista;
    }

    public void setLista(List<PessoaBloqueioCobrancaTO> lista) {
        this.lista = lista;
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        if (listaSelectItemEmpresa == null) {
            listaSelectItemEmpresa = new ArrayList<>();
        }
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public Integer getEmpresaFiltro() {
        if (empresaFiltro == null) {
            empresaFiltro = 0;
        }
        return empresaFiltro;
    }

    public void setEmpresaFiltro(Integer empresaFiltro) {
        this.empresaFiltro = empresaFiltro;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public boolean isConsultou() {
        return consultou;
    }

    public void setConsultou(boolean consultou) {
        this.consultou = consultou;
    }
}

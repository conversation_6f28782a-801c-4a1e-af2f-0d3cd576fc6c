package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.SGPModalidadeSemTurmaTO;
import relatorio.negocio.jdbc.basico.SGPModalidadeSemTurmaRel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SGPModalidadeSemTurmaControle extends SuperControleRelatorio {
    private List<SGPModalidadeSemTurmaTO> listaFrequencias = new ArrayList<>();
    private List<ModalidadeVO> listaModalidades = new ArrayList<>();

    private Date dataInicio;
    private Date dataFim;

    private boolean selecionarEmpresa;
    private String filtros;
    private List<ClienteVO> listaClientes;

    public SGPModalidadeSemTurmaControle() {
        inicializarDados();
    }

    public void inicializarDados() {
        try {
            setListaFrequencias(new ArrayList<>());
            setDataInicio(new Date());
            setDataFim(new Date());
            setSelecionarEmpresa(false);
            setFiltros("");
            montarListaSelectItemEmpresa();
            montarListaTurmas();
            limparMsg();
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean selecionarTodasModalidades() {
        getListaModalidades().forEach(it -> it.setSelecionado(true));
        return true;
    }

    public String consultar() {
        try {
            limparMsg();
            setarFiltros();
            SGPModalidadeSemTurmaRel rel = getFacade().getsGPModalidadeSemTurmaRel();
            rel.setarParametrosConsulta(getDataInicio(), getDataFim(), getEmpresa(), getListaModalidades());
            rel.validarDados();
            setListaFrequencias(rel.consultar());
            setMensagem("Dados consultados");
            return "relatorio";
        } catch (ConsistirException e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    private void setarFiltros() {
        StringBuilder filtros = new StringBuilder();
        filtros.append("<b>Inicio: </b> ").append(getDataInicio_Apresentar()).append("</br>");
        filtros.append("<b>Fim: </b> ").append(getDataFim_Apresentar()).append("</br>");
        filtros.append("<b>Empresa: </b>").append(getEmpresa().getNome()).append("</br>");
        filtros.append("<b>Modalidades: </b>");
        for (ModalidadeVO modalidade : listaModalidades) {
            if (modalidade.getSelecionado()) {
                filtros.append(modalidade.getNome()).append("</br>");
            }
        }
        setFiltros(filtros.toString());
    }

    public String voltar() {
        return "consultar";
    }

    public void montarListaSelectItemEmpresa() {
        try {
            if (JSFUtilities.isJSFContext()) {
                setListaEmpresas(new ArrayList<>());
                setEmpresa(getEmpresaLogado());
            }
            if (getEmpresa() == null || getEmpresa().getCodigo() == 0) {
                setSelecionarEmpresa(true);
                montarListaEmpresas();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void montarListaTurmas() throws Exception {
        setListaModalidades(getFacade().getModalidade().consultarTodasModalidades(getEmpresa().getCodigo(), true, false));
    }

    public boolean isSelecionarEmpresa() {
        return selecionarEmpresa;
    }

    public void setSelecionarEmpresa(Boolean selecionarEmpresa) {
        this.selecionarEmpresa = selecionarEmpresa;
    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public List<SGPModalidadeSemTurmaTO> getListaFrequencias() {
        return listaFrequencias;
    }

    public void setListaFrequencias(List<SGPModalidadeSemTurmaTO> listaFrequencias) {
        this.listaFrequencias = listaFrequencias;
    }

    public List<ModalidadeVO> getListaModalidades() {
        return listaModalidades;
    }

    public void setListaModalidades(List<ModalidadeVO> listaModalidades) {
        this.listaModalidades = listaModalidades;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataInicio_Apresentar() {
        return Uteis.getData(dataInicio);
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    private String getDataFim_Apresentar() {
        return Uteis.getData(dataFim);
    }

    public List<ClienteVO> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClienteVO> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public void prepararListaNaoSocio() {
        SGPModalidadeSemTurmaTO item = (SGPModalidadeSemTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaNaoSocio());
        }
    }

    public void prepararListaAluno() {
        SGPModalidadeSemTurmaTO item = (SGPModalidadeSemTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaAlunos());
        }
    }

    public void prepararListaSocio() {
        SGPModalidadeSemTurmaTO item = (SGPModalidadeSemTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaSocios());
        }
    }
    
    public void prepararListaComerciario() {
        SGPModalidadeSemTurmaTO item = (SGPModalidadeSemTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaComerciario());
        }
    }

    public void prepararListaDependente() {
        SGPModalidadeSemTurmaTO item = (SGPModalidadeSemTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaDependentes());
        }
    }

    public void prepararListaUsuario() {
        SGPModalidadeSemTurmaTO item = (SGPModalidadeSemTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaUsuarios());
        }
    }

    public void prepararListaEvento() {
        SGPModalidadeSemTurmaTO item = (SGPModalidadeSemTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaEventos());
        }
    }

    public void prepararListaCanceladosDesistentes() {
        SGPModalidadeSemTurmaTO item = (SGPModalidadeSemTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaCanceladosDesistentes());
        }
    }
}

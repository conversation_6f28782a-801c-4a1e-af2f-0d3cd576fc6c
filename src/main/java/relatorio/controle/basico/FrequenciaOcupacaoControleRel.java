package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.ConsultarAlunosTurmaVO;
import negocio.comuns.plano.ConsultarTurmaTO;
import negocio.comuns.plano.HorarioTurmaTO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRParameter;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> Pimenta
 */
public class FrequenciaOcupacaoControleRel extends SuperControleRelatorio {

    private ConsultarTurmaTO consultarTurma = new ConsultarTurmaTO();
    private List<HorarioTurmaTO> listaHorarioTurma = new ArrayList<HorarioTurmaTO>();
    private HorarioTurmaTO horarioTurmaSelecionado = new HorarioTurmaTO();
    private UsuarioVO usuario = new UsuarioVO();
    private boolean mostrarEmpresa = false;
    private boolean emProcessamento = false;
    private boolean pollEnabled = true;
    private Date dataInicio;
    private Date dataTermino;
    private boolean relatorioDetalhado = false;
    private boolean exibirReposicoes = true;

    public void novo() {
        try {
            consultarTurma = new ConsultarTurmaTO();
            listaHorarioTurma = new ArrayList<HorarioTurmaTO>();
            obterUsuarioLogado();
            inicializarUsuarioLogado();
            inicializarEmpresaLogado();
            montarListaEmpresa();
            montarListas();
            setMensagemDetalhada("", "");
            dataInicio = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
            dataTermino = Uteis.obterUltimoDiaMes(Calendario.hoje());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void inicializarUsuarioLogado() throws Exception {
        usuario = (UsuarioVO) getUsuarioLogado().getClone(true);
        if (usuario == null) {
            throw new Exception("Usuário Não Encontrado. Entre Novamente no Sistema.");
        }
        mostrarEmpresa = usuario.getAdministrador();
    }

    private void inicializarEmpresaLogado() throws Exception {
        // se usuario administrador
        if (mostrarEmpresa) {
            consultarTurma.setEmpresa(new EmpresaVO());
        } else {
            consultarTurma.setEmpresa((EmpresaVO) getEmpresaLogado().getClone(false));
        }
        // se empresa logada esta null
        if (consultarTurma.getEmpresa() == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public void montarListas() {
        try {
            montarListaModalidade();
            montarListaTurma();
            montarListaProfessor();
            montarListaAmbiente();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void montarListaEmpresa() throws Exception {
        consultarTurma.setListaEmpresa(new ArrayList<SelectItem>());
        if (mostrarEmpresa) {
            consultarTurma.getListaEmpresa().add(new SelectItem(0, ""));
            List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (Object aResultadoConsulta : resultadoConsulta) {
                EmpresaVO obj = (EmpresaVO) aResultadoConsulta;
                consultarTurma.getListaEmpresa().add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        }
    }

    public void montarListaModalidade() throws Exception {
        consultarTurma.setModalidade(new ModalidadeVO());
        consultarTurma.setListaModalidade(new ArrayList<SelectItem>());
        if (consultarTurma.getEmpresa().getCodigo() > 0) {
            consultarTurma.getListaModalidade().add(new SelectItem(0, ""));
            List resultadoConsulta = getFacade().getModalidade().consultarPorNomeUtilizaTurma("", consultarTurma.getEmpresa().getCodigo(), false, true, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (Object aResultadoConsulta : resultadoConsulta) {
                ModalidadeVO obj = (ModalidadeVO) aResultadoConsulta;
                consultarTurma.getListaModalidade().add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
            Ordenacao.ordenarLista(consultarTurma.getListaModalidade(), "label");
        }
    }

    public void montarListaTurma() throws Exception {
        consultarTurma.setTurma(new TurmaVO());
        consultarTurma.setListaTurma(new ArrayList<SelectItem>());
        consultarTurma.getListaTurma().add(new SelectItem(0, ""));
        int modalidade = (consultarTurma.getModalidade() != null) ? consultarTurma.getModalidade().getCodigo() : 0;
        List resultadoConsulta = getFacade().getTurma().consultarPorCodigoModalidade(modalidade, consultarTurma.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (Object aResultadoConsulta : resultadoConsulta) {
            TurmaVO obj = (TurmaVO) aResultadoConsulta;
            consultarTurma.getListaTurma().add(new SelectItem(obj.getCodigo(), obj.getIdentificador()));
        }
        Ordenacao.ordenarLista(consultarTurma.getListaTurma(), "label");
    }

    public void montarListaProfessor() throws Exception {
        consultarTurma.setProfessor(new ColaboradorVO());
        consultarTurma.setListaProfessor(new ArrayList<SelectItem>());
        consultarTurma.getListaProfessor().add(new SelectItem(0, ""));
        List resultadoConsulta = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.PROFESSOR, "AT", consultarTurma.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (Object aResultadoConsulta : resultadoConsulta) {
            ColaboradorVO obj = (ColaboradorVO) aResultadoConsulta;
            consultarTurma.getListaProfessor().add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome()));
        }
        Ordenacao.ordenarLista(consultarTurma.getListaProfessor(), "label");
    }

    public void montarListaAmbiente() throws Exception {
        consultarTurma.setAmbiente(new AmbienteVO());
        consultarTurma.setListaAmbiente(new ArrayList<SelectItem>());
        consultarTurma.getListaAmbiente().add(new SelectItem(0, ""));
        List resultadoConsulta = getFacade().getAmbiente().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (Object aResultadoConsulta : resultadoConsulta) {
            AmbienteVO obj = (AmbienteVO) aResultadoConsulta;
            consultarTurma.getListaAmbiente().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        Ordenacao.ordenarLista(consultarTurma.getListaAmbiente(), "label");
    }

// * Consulta de turma da tela *************************************************frequenciaOcupacaoTurma
    private List<String> filtrodiasSemana() {
        List<String> diasSemana = new ArrayList<String>();
        if (consultarTurma.isDomingo()) {
            diasSemana.add("DM");
        }
        if (consultarTurma.isSegunda()) {
            diasSemana.add("SG");
        }
        if (consultarTurma.isTerca()) {
            diasSemana.add("TR");
        }
        if (consultarTurma.isQuarta()) {
            diasSemana.add("QA");
        }
        if (consultarTurma.isQuinta()) {
            diasSemana.add("QI");
        }
        if (consultarTurma.isSexta()) {
            diasSemana.add("SX");
        }
        if (consultarTurma.isSabado()) {
            diasSemana.add("SB");
        }
        if (diasSemana.isEmpty()) {
            consultarTurma.setDomingo(true);
            consultarTurma.setSegunda(true);
            consultarTurma.setTerca(true);
            consultarTurma.setQuarta(true);
            consultarTurma.setQuinta(true);
            consultarTurma.setSexta(true);
            consultarTurma.setSabado(true);
        }
        return diasSemana;
    }

    private List<String> filtroHorarios() {
        List<String> horarios = new ArrayList<String>();
        if (consultarTurma.isH0001as0200()) {
            horarios.add("00:01 - 02:00");
        }
        if (consultarTurma.isH0201as0400()) {
            horarios.add("02:01 - 04:00");
        }
        if (consultarTurma.isH0401as0600()) {
            horarios.add("04:01 - 06:00");
        }
        if (consultarTurma.isH0601as0800()) {
            horarios.add("06:01 - 08:00");
        }
        if (consultarTurma.isH0801as1000()) {
            horarios.add("08:01 - 10:00");
        }
        if (consultarTurma.isH1001as1200()) {
            horarios.add("10:01 - 12:00");
        }
        if (consultarTurma.isH1201as1400()) {
            horarios.add("12:01 - 14:00");
        }
        if (consultarTurma.isH1401as1600()) {
            horarios.add("14:01 - 16:00");
        }
        if (consultarTurma.isH1601as1800()) {
            horarios.add("16:01 - 18:00");
        }
        if (consultarTurma.isH1801as2000()) {
            horarios.add("18:01 - 20:00");
        }
        if (consultarTurma.isH2001as2200()) {
            horarios.add("20:01 - 22:00");
        }
        if (consultarTurma.isH2201as0000()) {
            horarios.add("22:01 - 00:00");
        }
        return horarios;
    }

    public void consultarTurmas() {
        setMensagemDetalhada("", "");
        setPollEnabled(true);
        if (!isEmProcessamento()) {
            try {
                setEmProcessamento(true);
                if (consultarTurma.getEmpresa() == null || consultarTurma.getEmpresa().getCodigo().intValue() == 0) {
                    throw new Exception("Empresa não encontrada.");
                }
                if ((consultarTurma.getModalidade() == null || UteisValidacao.emptyNumber(consultarTurma.getModalidade().getCodigo()))
                        && (consultarTurma.getProfessor() == null || UteisValidacao.emptyNumber(consultarTurma.getProfessor().getCodigo()))) {
                    throw new Exception("É necessário informar uma modalidade ou um professor.");
                }
                if (dataInicio == null) {
                    throw new Exception("É necessário informar o período inicial.");
                }
                if (dataTermino == null) {
                    throw new Exception("É necessário informar o período final.");
                }
                if (Calendario.diferencaEmDias(dataInicio, dataTermino) > 30) {
                    throw new Exception("Não é possível informar um período acima de 30 dias.");
                }
                // pega os filtros da tela
                List<String> diasSemana = filtrodiasSemana();
                List<String> horarios = filtroHorarios();
                // inicializa a lista resultado da consulta
                listaHorarioTurma = new ArrayList<HorarioTurmaTO>();
                // consulta todos os horarios de acordo com os filtros
                List<HorarioTurmaVO> listaAux = getFacade().getHorarioTurma().consultarPorEmpresaModalidadeTurmaProfessorIdade(
                        consultarTurma.getEmpresa().getCodigo(), consultarTurma.getModalidade().getCodigo(),
                        consultarTurma.getTurma().getCodigo(), consultarTurma.getProfessor().getCodigo(),
                        consultarTurma.getAmbiente().getCodigo(), -1, 0, diasSemana, horarios, dataInicio, dataTermino, Boolean.FALSE, Uteis.NIVELMONTARDADOS_TODOS, false, false, null);
                // processa horarios verificando frequencia do periodo
                processarHorarios(listaAux);
                setMensagemDetalhada("msg_dados_consultados", "");
                setEmProcessamento(false);
                setPollEnabled(false);
            } catch (Exception e) {
                listaHorarioTurma = new ArrayList<HorarioTurmaTO>();
                setEmProcessamento(false);
                setPollEnabled(false);
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
        }
    }

    public void processarHorarios(List<HorarioTurmaVO> listaHorarios) throws Exception {
        for (HorarioTurmaVO horario : listaHorarios) {
            // atualiza a taxa de ocupacao
            if (horario.getNrMaximoAluno() <= 0) {
                continue;
            }
            listaHorarioTurma.add(getFacade().getHorarioTurma().processaFrequenciaPorHorario(horario, dataInicio, dataTermino, isExibirReposicoes()));
        }
    }

// * lista de alunos por horario ***********************************************
    public void posicionaListaAlunos() {
        setMensagemDetalhada("", "");
        try {
            // pega o elemento escolhido na tela
            HorarioTurmaTO ht = (HorarioTurmaTO) context().getExternalContext().getRequestMap().get("horarioTurma");
            if (ht == null) {
                throw new Exception("Não foi possível encontrar o Horário correto. Contate suporte técnico.");
            } else {
                horarioTurmaSelecionado = ht;
                List<ReposicaoVO> reposicoes = ht.getListaReposicoes();
                for (ReposicaoVO repo : reposicoes) {
                    repo.definirTipo(horarioTurmaSelecionado.getHorario());
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

// * impressos em PDF **********************************************************
    public void escolheRelatorio(ActionEvent evt) {
        limparMsg();
        setMsgAlert("");
        try {
            relatorioDetalhado = false;
            String tipo = (String) JSFUtilities.getFromActionEvent("tipoRelatorio", evt);
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_FREQUENCIA_OCUPACAO, listaHorarioTurma.size(), "", tipo, "", "");
            imprimirRelatorioTurmas(evt);
            if(tipo.equals("PDF")) {
                setMsgAlert("imprimirRelatorio(this.form);");
            } else {
                setMsgAlert("abrirPopupPDFImpressao('"+getNomeArquivoRelatorioGeradoAgora()+"','', 480, 280);");
            }

        } catch (Exception e){
            montarErro(e);
        }
    }

    public void escolheRelatorioAluno() {
        relatorioDetalhado = true;
        imprimirRelatorioDetalhado();
    }

    public void imprimirRelatorio() {
        if (relatorioDetalhado) {
            imprimirRelatorioDetalhado();
        } else {
            imprimirRelatorioTurmas(JSFUtilities.createEventParameter("tipoRelatorio", "PDF"));
        }
    }

    public void imprimirRelatorioTurmas(ActionEvent evt) {
        try {
            String nomeRelatorio = "FrequenciaOcupacaoTurmas";
            String titulo = "";
            String design = "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator + "FrequenciaOcupacaoTurmas.jrxml";
            EmpresaVO empre = getFacade().getEmpresa().consultarPorChavePrimaria(consultarTurma.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setRelatorio("sim");
            setTipoRelatorio((String) evt.getComponent().getAttributes().get("tipoRelatorio"));
            String barra = empre.getCidade().getNome().trim().isEmpty() ? "" : "/";

            Map<String, Object> params = new HashMap();
            params.put("nomeRelatorio", nomeRelatorio);
            params.put("tituloRelatorio", titulo);
            params.put("nomeDesignIReport", design);
            params.put("nomeUsuario", getUsuarioLogado().getNome());
            params.put("caminhoParserXML", "/FrequenciaOcupacaoTurmas/registros");
            params.put("dataIni", Uteis.getData(dataInicio));
            params.put("dataFim", Uteis.getData(dataTermino));
            params.put("enderecoEmpresa", empre.getEndereco() + " " + empre.getNumero() + " " + empre.getSetor());
            params.put("cidadeEmpresa", empre.getCidade().getNome() + barra + empre.getCidade().getEstado().getSigla());
            params.put("listaObjetos", listaHorarioTurma);
            params.put("nomeEmpresa", empre.getNome());

            if (getTipoRelatorio().equals("PDF")) {
                apresentarRelatorioObjetos(params);
            } else if (getTipoRelatorio().equals("EXCEL")) {
                params.put("nomeEmpresa", "");
                params.put(JRParameter.IS_IGNORE_PAGINATION, true);
                apresentarRelatorioObjetos(params);
            }

            setMensagemDetalhada("");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator);
    }

    public void imprimirRelatorioDetalhado() {
        try {
            String nomeRelatorio = "FrequenciaAlunos";
            String titulo = "";
            String design = "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator + "FrequenciaAlunos.jrxml";
            EmpresaVO empre = getFacade().getEmpresa().consultarPorChavePrimaria(consultarTurma.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setRelatorio("sim");
            String barra = empre.getCidade().getNome().trim().isEmpty() ? "" : "/";


            Map<String, Object> params = new HashMap();
            params.put("nomeRelatorio", nomeRelatorio);
            params.put("nomeEmpresa", empre.getNome());

            params.put("tituloRelatorio", titulo);
            params.put("nomeDesignIReport", design);
            params.put("nomeUsuario", getUsuarioLogado().getNome());
            params.put("listaObjetos", horarioTurmaSelecionado.getListaAlunos());
            params.put("caminhoParserXML", "/FrequenciaAlunos/registros");
            params.put("dataIni", Uteis.getData(dataInicio));
            params.put("dataFim", Uteis.getData(dataTermino));
            params.put("enderecoEmpresa", empre.getEndereco() + " " + empre.getNumero() + " " + empre.getSetor());
            params.put("cidadeEmpresa", empre.getCidade().getNome() + barra + empre.getCidade().getEstado().getSigla());
            params.put("turma", horarioTurmaSelecionado.getHorario().getIdentificadorTurma());
            params.put("professor", horarioTurmaSelecionado.getHorario().getProfessor().getPessoa().getNome());
            params.put("ambiente", horarioTurmaSelecionado.getHorario().getAmbiente().getDescricao());
            params.put("diaSemana", horarioTurmaSelecionado.getHorario().getDiaSemana_Apresentar());
            params.put("horario", horarioTurmaSelecionado.getHorario().getHoraInicial() + " - " + horarioTurmaSelecionado.getHorario().getHoraFinal());
            JRDataSource jr1 = new JRBeanArrayDataSource(horarioTurmaSelecionado.getListaReposicoes().toArray(), false);

            params.put("dadosReposicoes", jr1);
            params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
            apresentarRelatorioObjetos(params);

//            apresentarRelatorioObjetos(
//                    nomeRelatorio,
//                    titulo,
//                    empre.getNome(),
//                    "",
//                    "",
//                    getTipoRelatorio(),
//                    "/FrequenciaAlunos/registros",
//                    design,
//                    usuario.getNome(),
//                    "",
//                    Uteis.getData(dataInicio),
//                    Uteis.getData(dataTermino),
//                    empre.getEndereco() + " " + empre.getNumero() + " " + empre.getSetor(),
//                    empre.getCidade().getNome() + barra + empre.getCidade().getEstado().getSigla(),
//                    horarioTurmaSelecionado.getHorario().getIdentificadorTurma(),
//                    horarioTurmaSelecionado.getHorario().getProfessor().getPessoa().getNome(),
//                    horarioTurmaSelecionado.getHorario().getAmbiente().getDescricao(),
//                    horarioTurmaSelecionado.getHorario().getDiaSemana_Apresentar(),
//                    horarioTurmaSelecionado.getHorario().getHoraInicial() + " - " + horarioTurmaSelecionado.getHorario().getHoraFinal(),
//                    0.0,
//                    0.0,
//                    0.0,
//                    0.0,
//                    0.0,
//                    0.0,
//                    0.0,
//                    getCaminhoSubRelatorio(),
//                    "",
//                    "",
//                    horarioTurmaSelecionado.getListaAlunos(),
//                    "",
//                    0.0,
//                    "",
//                    0.0,
//                    null,
//                    null);
            setMensagemDetalhada("");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

// * gets e sets ***************************************************************
    public void setConsultarTurma(ConsultarTurmaTO consultarTurma) {
        this.consultarTurma = consultarTurma;
    }

    public ConsultarTurmaTO getConsultarTurma() {
        return consultarTurma;
    }

    public boolean isMostrarEmpresa() {
        return mostrarEmpresa;
    }

    public void setMostrarEmpresa(boolean mostrarEmpresa) {
        this.mostrarEmpresa = mostrarEmpresa;
    }

    public List<HorarioTurmaTO> getListaHorarioTurma() {
        return listaHorarioTurma;
    }

    public void setListaHorarioTurma(List<HorarioTurmaTO> listaHorarioTurma) {
        this.listaHorarioTurma = listaHorarioTurma;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public HorarioTurmaTO getHorarioTurmaSelecionado() {
        return horarioTurmaSelecionado;
    }

    public void setHorarioTurmaSelecionado(HorarioTurmaTO horarioTurmaSelecionado) {
        this.horarioTurmaSelecionado = horarioTurmaSelecionado;
    }

    public boolean isExibirReposicoes() {
        return exibirReposicoes;
    }

    public void setExibirReposicoes(boolean exibirReposicoes) {
        this.exibirReposicoes = exibirReposicoes;
    }

    public boolean isEmProcessamento() {
        return emProcessamento;
    }

    public void setEmProcessamento(boolean emProcessamento) {
        this.emProcessamento = emProcessamento;
    }

    public boolean isPollEnabled() {
        return pollEnabled;
    }

    public void setPollEnabled(boolean pollEnabled) {
        this.pollEnabled = pollEnabled;
    }
}

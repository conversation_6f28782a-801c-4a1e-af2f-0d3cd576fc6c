package relatorio.controle.basico;

import negocio.comuns.basico.*;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.util.*;

public class HistoricoPontosParceiroFidelidadeControle extends SuperControleRelatorio {

    private Date dataInicio;
    private Date dataTermino;
    private List<SelectItem> listaDeEmpresa;
    private boolean temEmpresa;
    private String filtros;
    private List<ParceiroFidelidadePontosVO> listaHistorico;
    private Integer totalizador;
    private PessoaVO pessoa;
    private boolean apresentarVoltar = true;

    public HistoricoPontosParceiroFidelidadeControle() {
        inicializarDados();
    }

    public void inicializarDados() {
        try {
            setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
            setDataTermino(negocio.comuns.utilitarias.Calendario.hoje());
            setEmpresa(new EmpresaVO());
            setTemEmpresa(false);
            setListaDeEmpresa(new ArrayList<SelectItem>());
            setMensagemID("msg_entre_prmrelatorio");
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
            setTotalizador(0);
            setApresentarVoltar(true);
            montarListaSelectItemEmpresa();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void validarDados() throws Exception {
        if (getEmpresa().getCodigo() == 0) {
            throw new Exception("Uma Empresa deve ser selecionada !");
        }
        if (UteisValidacao.emptyNumber(getPessoa().getCodigo())) {
            if (getDataInicio() == null) {
                throw new Exception("Informe data de início da pesquisa.");
            }
            if (getDataTermino() == null) {
                throw new Exception("Informe data de término da pesquisa.");
            }
            if (getDataInicio().compareTo(getDataTermino()) > 0) {
                throw new Exception("A date de início deve ser menor que a data de término para pesquisa.");
            }
        }
    }

    public String consultar() {
        try {
            limparMsg();
            validarDados();
            setTotalizador(0);
            setListaHistorico(getFacade().getParceiroFidelidadePontos().consultarRelatorio(getDataInicio(), getDataTermino(), getPessoa().getCodigo()));
            setTotalizador(getListaHistorico().size());
            if (UteisValidacao.emptyNumber(getTotalizador()) && isApresentarVoltar()) {
                throw new Exception("Nenhum histórico encontrado.");
            }
            setFiltros(getDescricaoFiltros());
            return "relatorio";
        } catch (Exception e) {
            montarErro(e.getMessage());
            return "";
        }
    }

    private String getDescricaoFiltros() throws Exception {
        String aux = "";
        if (getEmpresa().getCodigo() != 0 && estahDeslogado()) {
            aux += "<b>Empresa: </b> " + getEmpresaLista() + "</br>";
        }

        if (getDataInicio() != null && getDataTermino() != null) {
            aux += " <b>Período de: </b>" + Uteis.getData(dataInicio) + "<b> até: </b>" + Uteis.getData(dataTermino) + "</br>";
        }

        if (!UteisValidacao.emptyNumber(getPessoa().getCodigo())) {
            aux += " <b>Cliente: </b>" + getPessoa().getNome();
        }
        return aux;
    }

    public void selecionarMesAtual() throws Exception {
        setDataInicio(Uteis.obterPrimeiroDiaMes(negocio.comuns.utilitarias.Calendario.hoje()));
        setDataTermino(Uteis.obterUltimoDiaMesUltimaHora(negocio.comuns.utilitarias.Calendario.hoje()));
        setMensagemDetalhada("");
    }

    public String voltar() {
        setListaHistorico(new ArrayList<ParceiroFidelidadePontosVO>());
        setPessoa(null);
        return "voltar";
    }

    public void montarListaSelectItemEmpresa() {
        try {
            setEmpresa(getEmpresaLogado());
            if (getEmpresa() != null && getEmpresa().getNome().equals("")) {
                setTemEmpresa(true);
                montarListaSelectItemEmpresa("");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        List resultadoConsulta = getFacade().getEmpresa().consultarPorNome(prm,true, false, Uteis.NIVELMONTARDADOS_TODOS);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaDeEmpresa(objs);
    }

    public void irParaTelaCliente() {
        ParceiroFidelidadePontosVO obj = (ParceiroFidelidadePontosVO) context().getExternalContext().getRequestMap().get("historico");
        try {
            if(obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(obj.getClienteVO());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<SelectItem> getListaDeEmpresa() {
        return listaDeEmpresa;
    }

    public void setListaDeEmpresa(List<SelectItem> listaDeEmpresa) {
        this.listaDeEmpresa = listaDeEmpresa;
    }

    public boolean getTemEmpresa() {
        return temEmpresa;
    }

    public void setTemEmpresa(boolean temEmpresa) {
        this.temEmpresa = temEmpresa;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public Integer getTotalizador() {
        if (totalizador == null) {
            totalizador = 0;
        }
        return totalizador;
    }

    public void setTotalizador(Integer totalizador) {
        this.totalizador = totalizador;
    }

    private String getEmpresaLista() throws Exception {
        setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        return getEmpresa().getNome();
    }

    public String getFiltrosImpressao() {
        return getFiltros().replaceAll("<b>", "").replaceAll("</b>","").replaceAll("</br>", "");
    }

    public String getFiltros() {
        if (filtros == null) {
            filtros = "";
        }
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public List<ParceiroFidelidadePontosVO> getListaHistorico() {
        if (listaHistorico == null) {
            listaHistorico = new ArrayList<ParceiroFidelidadePontosVO>();
        }
        return listaHistorico;
    }

    public void setListaHistorico(List<ParceiroFidelidadePontosVO> listaHistorico) {
        this.listaHistorico = listaHistorico;
    }

    public PessoaVO getPessoa() {
        if (pessoa == null) {
            pessoa = new PessoaVO();
        }
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public List<PessoaVO> executarAutocompleteConsultaPessoa(Object suggest) {
        String pref = (String) suggest;
        ArrayList<PessoaVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().consultarPessoaComLimite(getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().consultarPessoaPorNomeComLimite(getEmpresa().getCodigo(), pref, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        } catch (Exception ex) {
            result = (new ArrayList<PessoaVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarPessoaSuggestionBox() throws Exception {
        pessoa = (PessoaVO) request().getAttribute("result");
//        limparListas();
    }

    public void limparAluno() throws Exception {
//        limparListas();
        setPessoa(null);
    }

    public boolean isApresentarVoltar() {
        return apresentarVoltar;
    }

    public void setApresentarVoltar(boolean apresentarVoltar) {
        this.apresentarVoltar = apresentarVoltar;
    }
}

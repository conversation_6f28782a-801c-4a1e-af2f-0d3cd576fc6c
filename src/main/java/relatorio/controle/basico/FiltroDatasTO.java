package relatorio.controle.basico;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.faces.model.SelectItem;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoFiltroData;
import java.util.GregorianCalendar;
import negocio.comuns.arquitetura.SuperTO;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

public class FiltroDatasTO extends SuperTO {

    private static final String FORMATO_DIA_SEMANA = "EEEE (dd/MM/yy)";
    private static final long serialVersionUID = 3674061176012901691L;
    private Date inicio = Calendario.hoje();
    private Date fim = Calendario.hoje();
    private TipoFiltroData filtroVetado;
    private Integer tipoFiltroData;
    private Integer codigoMes;
    private Integer codigoSemana;
    private List<SelectItem> meses = new ArrayList<SelectItem>();

    public FiltroDatasTO(TipoFiltroData filtroVetado, TipoFiltroData inicial) {
        this.filtroVetado = filtroVetado;
        tipoFiltroData = inicial.getCodigo();
    }

    public void setTipoFiltroData(Integer tipoFiltroData) {
        this.tipoFiltroData = tipoFiltroData;
    }

    public Integer getTipoFiltroData() {
        return tipoFiltroData;
    }
    private List<SelectItem> semanas = new ArrayList<SelectItem>();

    /**
     * Retorna a lista de tipos de filtro de datas
     *
     * @return List<SelectItem>
     * @throws Exception
     * <AUTHOR>
     */
    public List<SelectItem> getTiposFiltroData() throws Exception {
        // criar lista para retorno
        final List<SelectItem> itens = new ArrayList<SelectItem>();
        for (TipoFiltroData tipo : TipoFiltroData.values()) {
            if (filtroVetado == null || !filtroVetado.equals(tipo)) {
                itens.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
            }
        }
        // retornar a lista
        return itens;
    }

    public List<SelectItem> getSemanas() throws Exception {
        if (semanas.isEmpty()) {
            atualizarSemanas();
        }
        return semanas;
    }

    public List<SelectItem> getMeses() throws Exception {
        if (meses == null || meses.isEmpty()) {
            // criar lista para retorno
            meses = new ArrayList<SelectItem>();
            // percorrer os valores do enumerador TipoMesesEnum
            for (Mes tipoMeses : Mes.values()) {
                // adicionar os meses na lista de retorno
                meses.add(new SelectItem(tipoMeses.getCodigo(), tipoMeses.getDescricao()));
            }
            // setar o mês como o atual
            this.setCodigoMes(this.pegarMesAtual());
            // retornar a lista	
        }

        return meses;
    }

    public boolean getIntervalo() {
        return tipoFiltroData.equals(TipoFiltroData.INTERVALO_DE_DATA.getCodigo());
    }

    public boolean getMes() {
        return tipoFiltroData.equals(TipoFiltroData.MES_SEMANA.getCodigo());
    }

    /**
     * Pegar mês atual
     *
     * @return data
     */
    public int pegarMesAtual() {
        // pegar data de hoje
        Date data = negocio.comuns.utilitarias.Calendario.hoje();
        return Uteis.getMesData(data);

    }

    public void atualizarSemanas() {
        Calendar calendar = Calendario.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.MONTH, this.getCodigoMes() - 1);
        List<SelectItem> itens = new ArrayList<SelectItem>();

        int aux = 1;
        do {
            String dataInicial = Formatador.formatarData(calendar.getTime(), FORMATO_DIA_SEMANA);
            while ((calendar.get(Calendar.DAY_OF_WEEK) > Calendar.SUNDAY)
                    && (calendar.get(Calendar.DAY_OF_MONTH) < calendar.getActualMaximum(Calendar.DAY_OF_MONTH))) {
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            String dataFinal = Formatador.formatarData(calendar.getTime(), FORMATO_DIA_SEMANA);
            itens.add(new SelectItem(aux++, dataInicial + " - " + dataFinal));
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        } while (calendar.get(Calendar.DAY_OF_MONTH) > 1);

        semanas = itens;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Date getFim() {
        return fim;
    }

    public void setCodigoMes(Integer codigoMes) {
        this.codigoMes = codigoMes;
    }

    public Integer getCodigoMes() {
        return codigoMes;
    }

    public void setCodigoSemana(Integer codigoSemana) {
        this.codigoSemana = codigoSemana;
    }

    public Integer getCodigoSemana() {
        return codigoSemana;
    }

    public void setarDatasFiltro() throws Exception {
        if (getMes()) {
            setarDatasFiltroSemana();
        }
    }

    public void setarDatasFiltroSemana() throws Exception {
        Integer codigoSemana = this.getCodigoSemana();
        // pega a instancia de calendar
        Calendar calendar = Calendario.getInstance();

        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.MONTH, this.getCodigoMes() - 1);

        if (codigoSemana == null) {
            setInicio(calendar.getTime());
            setFim(Uteis.obterUltimoDiaMesUltimaHora(calendar.getTime()));
            return;
        }

        while (codigoSemana >= 1) {
            if (codigoSemana == 1) {
                setInicio(calendar.getTime());
            }
            while ((calendar.get(Calendar.DAY_OF_WEEK) > Calendar.SUNDAY)
                    && (calendar.get(Calendar.DAY_OF_MONTH) < calendar.getActualMaximum(Calendar.DAY_OF_MONTH))) {
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            if (codigoSemana == 1) {
                setFim(calendar.getTime());
            }
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            codigoSemana--;
        }
    }
    
    public void preencherSemanaDataReferencia(Date dataReferencia){
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(dataReferencia);

        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        calendar.set(Calendar.AM_PM, Calendar.AM);
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        this.inicio = calendar.getTime();
        
        calendar.setTime(dataReferencia);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        calendar.set(Calendar.AM_PM, Calendar.PM);
        calendar.set(Calendar.HOUR, 11);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        this.fim = calendar.getTime();
    }
    
    public String getInicio_Apresentar(){
        if(inicio != null){
            return Uteis.getData(inicio);
        }
        return "";
    }
    
    public String getFim_Apresentar(){
        if(fim != null){
            return Uteis.getData(fim);
        }
        return "";
    } 
}

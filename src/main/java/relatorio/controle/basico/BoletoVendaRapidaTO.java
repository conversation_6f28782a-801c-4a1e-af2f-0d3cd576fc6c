package relatorio.controle.basico;

import negocio.comuns.arquitetura.SuperTO;

import java.util.ArrayList;
import java.util.List;

public class BoletoVendaRapidaTO extends SuperTO {

    private Integer empresa;
    private Integer contrato;
    private Integer cliente;
    private Integer convenio;
    private List<Integer> boletos;

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getConvenio() {
        return convenio;
    }

    public void setConvenio(Integer convenio) {
        this.convenio = convenio;
    }

    public List<Integer> getBoletos() {
        if (boletos == null) {
            boletos = new ArrayList<>();
        }
        return boletos;
    }

    public void setBoletos(List<Integer> boletos) {
        this.boletos = boletos;
    }
}

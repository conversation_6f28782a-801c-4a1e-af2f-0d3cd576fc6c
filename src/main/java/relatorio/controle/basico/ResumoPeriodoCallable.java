package relatorio.controle.basico;

import negocio.comuns.basico.enumerador.DadoResumoPeriodoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.webservice.IntegracaoImportacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;

import java.sql.Connection;
import java.util.concurrent.Callable;

public class ResumoPeriodoCallable implements Callable<String> {
    private final Connection connection;
    private final DadoResumoPeriodoEnum dadoResumoPeriodo;
    private final JSONObject retorno;
    private String dtInicio;
    private String dtFim;

    public ResumoPeriodoCallable(Connection connection, DadoResumoPeriodoEnum dadoResumoPeriodo, JSONObject retorno, String dtInicio, String dtFim) {
        this.dadoResumoPeriodo = dadoResumoPeriodo;
        this.retorno = retorno;
        this.dtInicio = dtInicio;
        this.dtFim = dtFim;
        this.connection = connection;
    }

    @Override
    public String call() throws Exception {
        try (Connection newConnection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(connection)) {
            IntegracaoImportacao integracaoImportacaoDao = new IntegracaoImportacao(newConnection);

            long millisInicioCall = System.currentTimeMillis();
            switch (dadoResumoPeriodo) {
                case CONTRATOS:
                    retorno.put("contratos", integracaoImportacaoDao.resumoPeriodoContratos(dtInicio, dtFim, false));
                    break;
                case ESTORNO_PAGAMENTOS:
                    retorno.put("estornos", integracaoImportacaoDao.resumoPeriodoEstornoPagamentos(dtInicio, dtFim));
                    break;
                case ESTORNO_CONTRATOS:
                    retorno.put("estornosContrato", integracaoImportacaoDao.resumoPeriodoEstornoContratos(dtInicio, dtFim));
                    break;
                case MANUTENCAO_MODALIDADE:
                    retorno.put("manutencaoModalidade", integracaoImportacaoDao.resumoPeriodoManutencaoModalidade(dtInicio, dtFim));
                    break;
                case OPERACOES:
                    retorno.put("operacoes", integracaoImportacaoDao.resumoPeriodoOperacoes(dtInicio, dtFim));
                    break;
                case PAGAMENTOS:
                    retorno.put("pagamentos", integracaoImportacaoDao.resumoPeriodoPagamentos(dtInicio, dtFim));
                    break;
                case PARCELAS_CANCELADAS:
                    retorno.put("parcelasCanceladas", integracaoImportacaoDao.resumoPeriodoParcelasCanceladas(dtInicio, dtFim));
                    break;
                case PARCELAS_PRO_RATA:
                    retorno.put("parcelasProRata", integracaoImportacaoDao.resumoPeriodoParcelasProRata(dtInicio, dtFim));
                case VENDAS_AVULSAS:
                    retorno.put("vendaAvulsa", integracaoImportacaoDao.resumoPeriodoVendasAvulsas(dtInicio, dtFim, false));
                    break;
                case PARCELAS_CONTRATO_VENCIMENTO:
                    retorno.put("contratos", integracaoImportacaoDao.resumoPeriodoContratos(dtInicio, dtFim, true));
                    break;
                case PARCELAS_VENDAAVULSA_VENCIMENTO:
                    retorno.put("vendaAvulsa", integracaoImportacaoDao.resumoPeriodoVendasAvulsas(dtInicio, dtFim, true));
                    break;
            }
            long millisCall = System.currentTimeMillis() - millisInicioCall;
            Uteis.logarDebug(String.format(
                    "-- ResumoPeriodo para " + dtInicio + " - " + dtFim + ", indicador " + dadoResumoPeriodo.name() + ".Tempo de execução: %d milissegundos.", millisCall));
            return null;
        }
    }

    public String getDtInicio() {
        return dtInicio;
    }

    public void setDtInicio(String dtInicio) {
        this.dtInicio = dtInicio;
    }

    public String getDtFim() {
        return dtFim;
    }

    public void setDtFim(String dtFim) {
        this.dtFim = dtFim;
    }
}

package relatorio.controle.basico;

import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.acesso.LiberacaoAcessoVO;
import negocio.comuns.acesso.enumerador.TipoLiberacaoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Controlador do relatório de Fechamento de Controle de Acessos da Catraca
 * <AUTHOR>
 */
public class FechamentoAcessosControleRel extends SuperControleRelatorio {

    //lista da combo
    private List<SelectItem> listaEmpresa;
    //empresa logada ou setada na combo
    private EmpresaVO empresa;
    //data inicial do periodo de acesso
    private Date dataPeriodoInicial;
    //data final do periodo de acesso
    private Date dataPeriodoFinal;
    private String horarioPeriodoInicial;
    private String horarioPeriodoFinal;
    private List<SelectItem> listaPeriodosPadrao;
    //percentual de acessos de liberação
    private Double percentualLiberacao;
    //percentual de acessos de clientes e colaboradores
    private Double percentualCliCol;
    private Integer totalAcessos;
    //lista usada para mostrar os tipos de liberação de acesso (ver enum TipoLiberacaoEnum)
    private List<TipoLiberacaoEnum> listaTipoLiberacaoEnumLiberacao;
    //usado para mostrar o resultado da pesquisa
    private boolean pesquisar = false;
    //atributo com o valor do periodo da combo caso seja escolhido
    private String tipoPadraoPeriodo = "";
    //total de acessos somente dos clientes
    private int totalAcessosClientes = 0;
    //total de acessos somente do colaborador
    private int totalAcessosColaboradores = 0;
    //lista de fechamento usada no resultado da pesquisa
    private List<FechamentoAcessoRelTO> listaFechamentoAcessoRel;
    //usado para abrir a tabela de mais detalhes
    private Boolean abrirGridMaisDetalhes = false;
    //objeto usado no grid que mostra mais detalhes da liberacao de acesso selecionado
    private List<LiberacaoAcessoVO> listaLiberacoes;
    //objeto usado para consultar dados a partir do numero indicador clicado
    private FechamentoAcessoRelTO fechamentoAcessoRelTO;
    private String tituloModalPanelJustificativa = "";
    private LiberacaoAcessoVO liberacaoAcessoVO;
    private String nomeLabelPessoa = "";
    private int posicaoObjetoListaLiberacoes = 0;
    private boolean abrirModalJustificativa = false;
    //por padrão verdadeiro
    private boolean mostrarModalPanelConfirmacaoGravado = true;
    private String abrirModalPanelConfirmacaoGravado = "";
    private UsuarioVO responsavelEnvioEmail = new UsuarioVO();
    private Integer qtdBVs = 0;
    private boolean mostrarModalPanelListaEmail = false;
    private ConfiguracaoSistemaVO configuracaoSistemaVO = new ConfiguracaoSistemaVO();
    private String msgAlert = "";
    private boolean clicouNoPrimeiroItem = false;
    private boolean clicouNoUltimoItem = false;
    //usado para verificar qual botão foi clicado,pois o botão Gravar deve ir para o proximo
    //ja o botão Sim pode ir para o ultimo ou o proximo ou o primeiro
    private boolean clicouNoBotaoGravar = false;
    private boolean permissaoConsultarInfoTodasEmpresas = false;

    public FechamentoAcessosControleRel() throws Exception {
        listaEmpresa = new ArrayList<>();
        empresa = new EmpresaVO();
        empresa.setCodigo(0);
        listaFechamentoAcessoRel = new ArrayList<>();
        listaLiberacoes = new ArrayList<>();
        liberacaoAcessoVO = new LiberacaoAcessoVO();
        montarListaEmpresa();
        montarListaPeriodosPadrao();
        inicializarEmpresaLogado();
    }

    /**
     * Usado para abrir o relatório verificando permissão
     */
    public void abrirRelatorio() {
        try {
            validarPermissaoJustificarAcessos();
            setPermissaoConsultarInfoTodasEmpresas(validarPermissaoConsultarInfoTodasEmpresas());
            dataPeriodoFinal = null;
            dataPeriodoInicial = null;
            setHorarioPeriodoInicial("00:00");
            setHorarioPeriodoFinal("23:59");
            setPesquisar(false);
            setAbrirGridMaisDetalhes(false);
            setAbrirModalJustificativa(false);
            setTipoPadraoPeriodo("HJ");
            setListaLiberacoes(new ArrayList<>());
            setLiberacaoAcessoVO(new LiberacaoAcessoVO());
            setMostrarModalPanelConfirmacaoGravado(true);
            setMostrarModalPanelListaEmail(false);
            setMsgAlert("abrirPopupMaximizada('relatorio/fechamentoAcessosRel.jsp','fechamentoAcesso');");
            setResponsavelEnvioEmail(getUsuarioLogado());
            getConfiguracaoSistemaVO().setEmailsFechamentoAcessos(getFacade().getConfiguracaoSistema().consultarEmailsFechamentoAcessos(configuracaoSistemaVO));
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void validarDados() throws Exception {
        if (empresa == null || empresa.getCodigo() == null || empresa.getCodigo() == 0) {
            throw new Exception("O campo Empresa deve ser informado");
        }
        if (dataPeriodoInicial == null) {
            throw new Exception("A Data Inicial do Período deve ser informado");
        }
        if (dataPeriodoFinal == null) {
            throw new Exception("A Data Final do Período deve ser informado");
        }
        if (horarioPeriodoInicial == null || horarioPeriodoInicial.isEmpty()) {
            throw new Exception("A Hora Inicial do Período deve ser informado");
        }
        if (horarioPeriodoFinal == null || horarioPeriodoFinal.isEmpty()) {
            throw new Exception("A Hora Final do Período deve ser informado");
        }
    }

    public void obterEmpresaEscolhida() throws Exception {
        if (empresa.getCodigo() != 0) {
            setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        } else {
            setEmpresa(new EmpresaVO());
        }
    }

    public void apresentarModalComJustificativa() {
        LiberacaoAcessoVO obj = (LiberacaoAcessoVO) context().getExternalContext().getRequestMap().get("liberacaoAcesso");
        if (obj != null) {
            setLiberacaoAcessoVO(obj);
        }
    }

    /**
     * Monta a combo de períodos padrões para facilitar a usabilidade
     */
    private void montarListaPeriodosPadrao() {
        setListaPeriodosPadrao(new ArrayList<>());
        getListaPeriodosPadrao().add(new SelectItem("", ""));
        getListaPeriodosPadrao().add(new SelectItem("HJ", "Hoje"));
        getListaPeriodosPadrao().add(new SelectItem("SE", "Semana"));
        getListaPeriodosPadrao().add(new SelectItem("MES", "Mês"));
    }

    public void mudarDatas() throws Exception {
        if (tipoPadraoPeriodo != null) {
            if (tipoPadraoPeriodo.equalsIgnoreCase("HJ")) {
                dataPeriodoInicial = Calendario.hoje();
                horarioPeriodoInicial = "00:00";
                dataPeriodoFinal = Calendario.hoje();
                horarioPeriodoFinal = "23:59";
            } else if (tipoPadraoPeriodo.equalsIgnoreCase("SE")) {
                dataPeriodoInicial = Uteis.obterPrimeiroEUltimoDiaSemana(true);
                horarioPeriodoInicial = "00:00";
                dataPeriodoFinal = Uteis.obterPrimeiroEUltimoDiaSemana(false);
                horarioPeriodoFinal = "23:59";
            } else if (tipoPadraoPeriodo.equalsIgnoreCase("MES")) {
                dataPeriodoInicial = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
                horarioPeriodoInicial = "00:00";
                dataPeriodoFinal = Uteis.obterUltimoDiaMes(Calendario.hoje());
                horarioPeriodoFinal = "23:59";
            }
        }
    }

    public Double getPercentualJustificados() {
        if (listaFechamentoAcessoRel != null && !getListaFechamentoAcessoRel().isEmpty()
                && getListaFechamentoAcessoRel().get(listaFechamentoAcessoRel.size() - 1).getTotalAcesso() > 0) {
            return Uteis.arredondarForcando2CasasDecimais((getListaFechamentoAcessoRel().get(listaFechamentoAcessoRel.size() - 1).getJaJustificado() * 100.0)
                    / getListaFechamentoAcessoRel().get(listaFechamentoAcessoRel.size() - 1).getTotalAcesso());
        }
        return 0.00;
    }

    /**
     * Monta a consulta do relatorio de fechamento de acessos
     */
    public void pesquisar() {
        try {
            validarDados();
            //consultar qtde de acessos de clientes e colaboradores
            totalAcessosClientes = getFacade().getAcessoCliente().
                    consultarTotalAcessosFiltros(empresa.getCodigo(), Uteis.getDataJDBC(dataPeriodoInicial),
                    Uteis.getDataJDBC(dataPeriodoFinal), horarioPeriodoInicial, horarioPeriodoFinal);
            totalAcessosColaboradores = getFacade().getAcessoColaborador().
                    consultarTotalAcessosFiltros(empresa.getCodigo(), Uteis.getDataJDBC(dataPeriodoInicial),
                    Uteis.getDataJDBC(dataPeriodoFinal), horarioPeriodoInicial, horarioPeriodoFinal);
            //consultar qtde total de liberacoes de acesso
            int totalAcessosLiberacao = getFacade().getLiberacaoAcesso().
                    contarTotalLiberacaoFiltros(getListaTipoLiberacaoEnumLiberacao(), empresa.getCodigo(),
                    Uteis.getDataJDBC(dataPeriodoInicial),
                    Uteis.getDataJDBC(dataPeriodoFinal), horarioPeriodoInicial, horarioPeriodoFinal);
            //calcular total de acessos e percentuais
            totalAcessos = totalAcessosClientes + totalAcessosColaboradores + totalAcessosLiberacao;

            if (totalAcessos > 0) {
                setPercentualCliCol(Uteis.arredondarForcando2CasasDecimais(
                        (100.0 * (totalAcessosClientes + totalAcessosColaboradores)) / totalAcessos));
                setPercentualLiberacao(Uteis.arredondarForcando2CasasDecimais(
                        (100.0 * totalAcessosLiberacao) / totalAcessos));
            } else {
                setPercentualCliCol(0.0);
                setPercentualLiberacao(0.0);
            }
            //consultar qtde de liberação de acesso pela catraca por tipo
            listaFechamentoAcessoRel = consultarLiberacoes(totalAcessosLiberacao);

            if (totalAcessos == 0) {
                throw new Exception("Não foi encontrado nenhum acesso no período informado.");
            }

            setQtdBVs(getFacade().getQuestionarioCliente().contarQtdQuestionarioVisitantesPorDataEmpresa(
                    dataPeriodoInicial, dataPeriodoFinal, horarioPeriodoInicial, horarioPeriodoFinal, empresa.getCodigo()));
            setPesquisar(true);
            setListaLiberacoes(new ArrayList<>());
            setAbrirGridMaisDetalhes(false);
            setMsgAlert("");
            setMensagemID("msg_dados_consultados");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void imprimirRelatorio() throws Exception {
        imprimirRelatorio(false);
    }

    public void imprimirRelatorio(boolean lancarExcecao) throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            List listaImpressao = getFacade().getLiberacaoAcesso().consultarTotalLiberacaoFiltros(null, empresa.getCodigo(),
                    Uteis.getDataJDBC(getDataPeriodoInicial()), Uteis.getDataJDBC(getDataPeriodoFinal()),
                    horarioPeriodoInicial, horarioPeriodoFinal, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, false, false);
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_FECHAMENTO_ACESSOS, listaImpressao.size(), "", "PDF", "", "");
            Map<String, Object> parametros = new HashMap<>();
            prepareParams(parametros, listaImpressao);
            apresentarRelatorioObjetos(parametros);
            setMsgAlert("abrirPopupPDFImpressao('"+getNomeArquivoRelatorioGeradoAgora()+"','', 780, 595);");
        } catch (Exception e) {
            if (lancarExcecao){
                throw e;
            }
            montarErro(e);
        }
    }

    public void consultarResponsavelEnvioEmail() {
        try {
            setResponsavelEnvioEmail(getFacade().getUsuario().consultarPorChavePrimaria(getResponsavelEnvioEmail().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    /**
     *
     * @return
     */
    public String getEstiloLegenda() {
        if (getPercentualJustificados() >= 0 && getPercentualJustificados() <= 40.99) {
            // cor vermelha
            return "padding: 5px 5px 5px 5px; background-color: red; color: white; font-size: 14pt;font-family: Arial,Verdana,sans-serif;";
        }
        if (getPercentualJustificados() > 41 && getPercentualJustificados() <= 60.99) {
            //cor laranja
            return "padding: 5px 5px 5px 5px; background-color: chocolate; color: white; font-size: 14pt;font-family: Arial,Verdana,sans-serif;";
        }
        if (getPercentualJustificados() > 61 && getPercentualJustificados() <= 85.99) {
            //cor verde
            return "padding: 5px 5px 5px 5px; background-color: green; color: white; font-size: 14pt;font-family: Arial,Verdana,sans-serif;";
        }
        if (getPercentualJustificados() > 86 && getPercentualJustificados() <= 100.99) {
            //cor azul
            return "padding: 5px 5px 5px 5px; background-color: blue; color: white; font-size: 14pt;font-family: Arial,Verdana,sans-serif;";
        }
        return "";
    }

    /**
     * Verifica se o usuário possui autorização de enviar email de fechamento
     * @throws Exception
     */
    public void autorizarEnvioEmail(UsuarioVO usuarioVO) throws Exception {
        //analisa autorização
        setErro(false);
        setMsgAlert("");
        setMensagemDetalhada("", "");
        setResponsavelEnvioEmail(usuarioVO);

        if (UteisValidacao.emptyString(getConfiguracaoSistemaVO().getEmailsFechamentoAcessos())) {
            throw new Exception("Informe pelo menos um email clicando no link \"Lista Email\"");
        }
        //validar emails informados
        validarEmails(getConfiguracaoSistemaVO().getEmailsFechamentoAcessos());
        //anexar o pdf
        imprimirRelatorio(true);
        //tenta enviar o email com o anexo em pdf
        enviarEmailFechamento();
    }

    /**
     * Responsável por obter o recibo, anexar no email e enviar
     * <AUTHOR>
     * 09/05/2011
     */
    private void enviarEmailFechamento() {
        try {
            //obter o recibo
            String nomePDF = Uteis.obterCaminhoWeb() + "/relatorio/" + getNomeArquivoRelatorioGeradoAgora();
            File arquivo = new File(nomePDF);
            //obter configurações do envio de email
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
            UteisEmail email = new UteisEmail();
            // assunto do email será "FECHAMENTO DE ACESSOS"
            email.novo("FECHAMENTO DE ACESSOS", configuracaoSistemaCRMVO);
            //remetente é o usuario logado
            email.setRemetente(getResponsavelEnvioEmail());
            email = email.addAnexo(getNomeArquivoRelatorioGeradoAgora(), arquivo);
            String[] listaEmails = configuracaoSistemaVO.getEmailsFechamentoAcessos().split(";");
            
            String nomeEmpresa = getUsuarioLogado().getAdministrador() ? "" : getEmpresaLogado().getNome();
            
            for (int i = 0; i < listaEmails.length; i++) {
                email.enviarEmail(listaEmails[i], getResponsavelEnvioEmail().getNome(), getTextoEmail(), nomeEmpresa);
            }
            //gerando log de envio de email
            gerarLogEnvioEmail(configuracaoSistemaVO.getEmailsFechamentoAcessos());
            setMensagemID("msg_enviar_email");
            montarMsgAlert("Email(s) enviados com sucesso!!");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setMsgAlert("msg_erro" + e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    /**
     * <AUTHOR>
     * 30/01/2012
     */
    private void gerarLogEnvioEmail(String listaEmails) throws Exception {
        //configurar o log
        LogVO log = new LogVO();
        log.setNomeEntidade("ENVIOEMAILFECHAMENTOACESSOS");
        log.setNomeEntidadeDescricao("Fechamento de Acessos - Envio de Email");
        log.setNomeCampo("emailFechamentoAcessos");
        log.setValorCampoAlterado(listaEmails);
        log.setValorCampoAnterior("");
        log.setDataAlteracao(Calendario.hoje());
        log.setOperacao("ENVIO DE EMAIL");
        log.setResponsavelAlteracao(getUsuarioLogado().getNome());
        log.setUserOAMD(getUsuarioLogado().getUserOamd());
        getFacade().getLog().incluir(log);
    }

    /**
     * @return the msgAlert
     */
    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void montarMsgAlert(String texto) {
        setMsgAlert("alert('" + texto + "');");
    }

    /**
     * @param msgAlert the msgAlert to set
     */
    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    /**
     * Método que retorna o texto que irá no conteúdo do email.
     * <AUTHOR>
     * 27/01/2011
     */
    private String getTextoEmail() {
        StringBuilder texto = new StringBuilder();
        texto.append("<center><b>Relatório de Fechamento de Acessos</b></center>");
        texto.append("<b>Responsável : </b>" + getResponsavelEnvioEmail().getNome() + "<br/>");
        texto.append("<b>Data Impressão : </b>" + Uteis.getData(Calendario.hoje()));
        return texto.toString();
    }

    public void gravarEmails() {
        try {
            if (getConfiguracaoSistemaVO().getEmailsFechamentoAcessos().isEmpty()) {
                throw new ConsistirException("Informe pelo menos um email ");
            }
            validarEmails(getConfiguracaoSistemaVO().getEmailsFechamentoAcessos());
            getFacade().getConfiguracaoSistema().gravarEmailsFechamentoAcessos(getConfiguracaoSistemaVO());
            //LOG - INICIO
            getConfiguracaoSistemaVO().setNovoObj(false);
            try {
                registrarLogObjetoVO(getConfiguracaoSistemaVO(), 0, "ALTERACAOEMAILSFECHAMENTOACESSO", 0);
            } catch (Exception e) {
                registrarLogErroObjetoVO("ALTERACAOEMAILSFECHAMENTOACESSO", 0, "ERRO AO GERAR LOG DE ALTERAÇÃO DE EMAILS DE FECHAMENTO DE ACESSOS", getUsuarioLogado().getNome(),getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
            //LOG - FiM
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void validarEmails(String emails) throws Exception {
        String[] listaEmails = emails.split(";");
        if (listaEmails.length == 0) {
            throw new Exception("Informe o email clicando no link Lista Email do relatório");
        }
        for (int i = 0; i < listaEmails.length; i++) {
            if (!UteisValidacao.validaEmail(listaEmails[i])) {
                String msg = "Email inválido (" + listaEmails[i] + ")";
                throw new Exception(msg);
            }
        }
    }

    /**
     * Abre o modal para ser informado o usuario e senha 
     */
    public void abrirModalPanelPermissaoEnvio() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                autorizarEnvioEmail(auto.getUsuario());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
                setSucesso(false);
                setAtencao(false);
                setErro(true);
            }
        };

        limparMsg();
        try {
            auto.autorizar("Confirmação de Liberação Alterar Horário", "GestaoControleAcessoEmail",
                    "Você precisa das permissões \"6.20 - Enviar Email de Fechamento de Acesso\" e \"9.98 - Permitir Exportar Dados\"",
                    "panelMensagemErro,panelAutorizarEnvioEmail,form:mensagem", listener);

        } catch (Exception e) {
            montarErro(e);
        }
    }

    /**
     * Usado para fechar o modal de permissão de envio de email
     */
    public void fecharPanelPermissaoEnvio() {
        setSucesso(false);
        setErro(false);
        setMensagemID("");
        setMensagemDetalhada("");
    }

    /**
     * Abre o modal para ser informado o usuario e senha
     */
    public void abrirModalListaEmail() throws Exception {
        setSucesso(false);
        setErro(false);
        setMensagemID("msg_entre_dados");
        setMostrarModalPanelListaEmail(true);
        getConfiguracaoSistemaVO().registrarObjetoVOAntesDaAlteracao();
    }

    /**
     * Usado para fechar o modal de permissão de envio de email
     */
    public void fecharModalListaEmail() {
        setSucesso(false);
        setErro(false);
        setMensagemID("");
        setMensagemDetalhada("");
        setMostrarModalPanelListaEmail(false);
    }

    private void prepareParams(Map<String, Object> params, List listaObjetos) throws Exception {
        Integer emp = this.getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        params.put("nomeRelatorio", "FechamentoControleAcessoDWRel");
        params.put("nomeEmpresa", empre.getNome());

        params.put("tituloRelatorio", "Fechamento de Acessos");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        params.put("listaObjetos", listaObjetos);

        params.put("totalAcessos", totalAcessos);
        params.put("percentualLiberacao", percentualLiberacao);
        params.put("percentualCliCol", percentualCliCol);
        params.put("totalCli", totalAcessosClientes);
        params.put("totalCol", totalAcessosColaboradores);
        params.put("percentualCli", getPercentualClientes());
        params.put("percentualCol", getPercentualColaboradores());
        params.put("totalAcessoLib", listaFechamentoAcessoRel.get(listaFechamentoAcessoRel.size() - 1).getTotalAcesso());
        params.put("totalJustificadoLib", listaFechamentoAcessoRel.get(listaFechamentoAcessoRel.size() - 1).getJaJustificado());
        params.put("totalFaltaJustificarLib", listaFechamentoAcessoRel.get(listaFechamentoAcessoRel.size() - 1).getFaltaJustificar());
        params.put("qtdBVs", getQtdBVs());

        params.put("filtros", getFiltros());
        params.put("dataIni", Uteis.getData(dataPeriodoInicial));
        params.put("dataFim", Uteis.getData(dataPeriodoFinal));
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
        JRDataSource jr1 = new JRBeanArrayDataSource(listaFechamentoAcessoRel.toArray(), false);

        params.put("listaFechamento", jr1);
        params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
    }

    public String getFiltros() {
        String filtros = "";
        if (empresa != null && empresa.getCodigo() != null && empresa.getCodigo() != 0) {
            filtros = "Empresa: " + empresa.getNome() + " ";
        }
        if (dataPeriodoInicial != null && dataPeriodoFinal != null
                && !horarioPeriodoInicial.isEmpty() && !horarioPeriodoFinal.isEmpty()) {
            filtros += "Período de Acesso: " + Uteis.getData(dataPeriodoInicial) + " " + horarioPeriodoInicial;
            filtros += " até " + Uteis.getData(dataPeriodoFinal) + " " + horarioPeriodoFinal + " ";
        }
        return filtros;
    }

    public static String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "FechamentoControleAcessoDW" + "Rel" + ".jrxml");
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator);
    }

    /**
     * Consulta de logs de emails de fechamento enviados
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogEnvioEmails() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        try {
            loginControle.setNomeClasse("Envio de Emails de Fechamento de Acessos");
            loginControle.setListaConsultaLog(getFacade().getLog().consultarPorNomeEntidadeOperacaoPorDataAlteracao("ENVIOEMAILFECHAMENTOACESSOS",
                    "Fechamento de Acessos - Envio de Email", "ENVIO DE EMAIL", Uteis.NIVELMONTARDADOS_DADOSBASICOS, false, "Destinatários",
                    dataPeriodoInicial, horarioPeriodoInicial, dataPeriodoFinal, horarioPeriodoFinal));
        } catch (Exception ex) {
            Logger.getLogger(FechamentoAcessosControleRel.class.getName()).log(Level.SEVERE, null, ex);
            loginControle.setListaConsultaLog(new ArrayList());
            loginControle.getListaConsultaLog().clear();
        }
    }

    /**
     * Consulta de logs de alteração de emails de Fechamento de Acessos
     */
    public void realizarConsultaLogAlteracaoEmails() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        try {
            loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("Alteração de Emails de Fechamento de Acessos"));
            loginControle.setListaConsultaLog(getFacade().getLog().consultarPorNomeEntidadeOperacaoPorDataAlteracao("ALTERACAOEMAILSFECHAMENTOACESSO",
                    "Configurações", "ALTERAÇÃO", Uteis.NIVELMONTARDADOS_DADOSBASICOS, true, "Emails",
                    dataPeriodoInicial, horarioPeriodoInicial, dataPeriodoFinal, horarioPeriodoFinal));
        } catch (Exception ex) {
            Logger.getLogger(FechamentoAcessosControleRel.class.getName()).log(Level.SEVERE, null, ex);
            loginControle.setListaConsultaLog(new ArrayList());
            loginControle.getListaConsultaLog().clear();
        }

    }

    /**
     * Consultar e montar os dados dos totalizadores de acessos
     * @param totalLiberacoes
     * @return
     * @throws SQLException
     * @throws Exception
     */
    public List<FechamentoAcessoRelTO> consultarLiberacoes(int totalLiberacoes) throws SQLException, Exception {
        List<FechamentoAcessoRelTO> listaFechLiberacoes = new ArrayList<FechamentoAcessoRelTO>();
        try {
            listaFechLiberacoes = getFacade().getLiberacaoAcesso().montarConsultaRelatorio(totalLiberacoes,
                    getPercentualLiberacao(), getListaTipoLiberacaoEnumLiberacao(), empresa.getCodigo(),
                    Uteis.getDataJDBC(dataPeriodoInicial), Uteis.getDataJDBC(dataPeriodoFinal),
                    horarioPeriodoInicial, horarioPeriodoFinal);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

        return listaFechLiberacoes;
    }

    /**
     * Consulta todos os acessos a partir do objeto escolhido
     */
    public void consultarTotalAcessos() throws Exception {
        try {
            FechamentoAcessoRelTO obj = prepararConsulta();
            listaLiberacoes = getFacade().getLiberacaoAcesso().consultarTotalLiberacaoFiltros(obj.getTipoLiberacaoEnum(),
                    empresa.getCodigo(), Uteis.getDataJDBC(dataPeriodoInicial), Uteis.getDataJDBC(dataPeriodoFinal),
                    horarioPeriodoInicial, horarioPeriodoFinal, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, false, false);
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Consulta acessos justificados
     */
    public void consultarAcessosJustificados() {
        try {
            FechamentoAcessoRelTO obj = prepararConsulta();
            listaLiberacoes = getFacade().getLiberacaoAcesso().consultarTotalLiberacaoFiltros(obj.getTipoLiberacaoEnum(),
                    empresa.getCodigo(), Uteis.getDataJDBC(dataPeriodoInicial), Uteis.getDataJDBC(dataPeriodoFinal),
                    horarioPeriodoInicial, horarioPeriodoFinal, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, true, false);
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Consulta acessos não justificados
     */
    public void consultarAcessosAJustificar() {
        try {
            FechamentoAcessoRelTO obj = prepararConsulta();
            listaLiberacoes = getFacade().getLiberacaoAcesso().consultarTotalLiberacaoFiltros(obj.getTipoLiberacaoEnum(),
                    empresa.getCodigo(), Uteis.getDataJDBC(dataPeriodoInicial), Uteis.getDataJDBC(dataPeriodoFinal),
                    horarioPeriodoInicial, horarioPeriodoFinal, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, false, true);
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public FechamentoAcessoRelTO prepararConsulta() {
        setAbrirGridMaisDetalhes((Boolean) true);
        FechamentoAcessoRelTO obj = (FechamentoAcessoRelTO) context().getExternalContext().getRequestMap().get("liberacao");
        if (obj != null) {
            setFechamentoAcessoRelTO(obj);
        }
        return getFechamentoAcessoRelTO();
    }

    public void gravarJustificativa() throws ConsistirException {
        try {

            if (getFacade().getLiberacaoAcesso().consultarUsuarioJustificou(liberacaoAcessoVO.getCodigo()) != 0
                    && this.getUsuarioLogado().getCodigo().intValue() != this.liberacaoAcessoVO.getUsuarioJustificou().getCodigo().intValue()) {
                throw new Exception("Somente o usuário que justificou poderá editar a justificativa");
            }
            liberacaoAcessoVO.setUsuarioJustificou(getUsuarioLogado());
            liberacaoAcessoVO.setDthrJustificativa(Calendario.hoje());
            getFacade().getLiberacaoAcesso().incluirJustificativa(liberacaoAcessoVO, nomeLabelPessoa);
            if (liberacaoAcessoVO.getPessoa().getCodigo() != 0) {
                getFacade().getSituacaoClienteSinteticoDW().registrarUltimoAcesso(liberacaoAcessoVO.getPessoa().getCodigo(), liberacaoAcessoVO.getDataHora());
            }
            if (posicaoObjetoListaLiberacoes <= listaLiberacoes.size()) {
                if (clicouNoPrimeiroItem && !clicouNoBotaoGravar) {
                    primeiroItem();
                } else if (clicouNoUltimoItem && !clicouNoBotaoGravar) {
                    ultimoItem();
                } else {
                    if(posicaoObjetoListaLiberacoes != listaLiberacoes.size())proximoItem();
                }
            }
            //necessário pois após clicar em gravar ele deve setar sempre como falso para processamentos posteriores
            setClicouNoBotaoGravar(false);
            setMensagemID("msg_dados_gravados");
            //analisar quando terminar o processamento incluir o objeto no indicador.
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirModalPanelConfirmacaoGravado("RichFaces.hideModalPanel('modalConfirmaGravacao');");
        }
    }

    /**
     * Seta qual botão foi clicado: botão Gravar do modal de justificativa
     * @param evt
     */
    public void setarBotaoGravar(ActionEvent evt) {
        setClicouNoBotaoGravar(true);
    }

    public void proximoItem() throws Exception {
        //setar falso pois clicou em proximo
        setClicouNoPrimeiroItem(false);
        setClicouNoUltimoItem(false);
        //se algum dado de liberação for vazio limpar dados de liberação para não incluir na lista em memória
        limparObjetoListaEmMemoriaQuandoNaoGravar();
        //setando o contador
        setPosicaoObjetoListaLiberacoes(posicaoObjetoListaLiberacoes + 1);
        //setando o proximo objeto da lista
        setLiberacaoAcessoVO(listaLiberacoes.get(posicaoObjetoListaLiberacoes - 1));
        setMostrarModalPanelConfirmacaoGravado(true);
        setarNomeLabelPessoa(liberacaoAcessoVO);
        setMensagemDetalhada("");
    }

    public void analisarProximoItem() throws Exception {
        setClicouNoPrimeiroItem(false);
        setClicouNoUltimoItem(false);
        if ((!liberacaoAcessoVO.getJustificativa().isEmpty() || liberacaoAcessoVO.getPessoa().getCodigo() != 0) && mostrarModalPanelConfirmacaoGravado) {
            setAbrirModalPanelConfirmacaoGravado("Richfaces.showModalPanel('modalConfirmaGravacao');");
            return;
        } else {
            setAbrirModalPanelConfirmacaoGravado("RichFaces.hideModalPanel('modalConfirmaGravacao');");
            proximoItem();
        }
    }

    public void analisarPrimeiroItem() throws Exception {
        //setar true pois clicou em primeiro item
        setClicouNoPrimeiroItem(true);
        setClicouNoUltimoItem(false);
        if ((!liberacaoAcessoVO.getJustificativa().isEmpty() || liberacaoAcessoVO.getPessoa().getCodigo() != 0) && mostrarModalPanelConfirmacaoGravado) {
            setAbrirModalPanelConfirmacaoGravado("Richfaces.showModalPanel('modalConfirmaGravacao');");
            return;
        } else {
            primeiroItem();
        }
    }

    public void primeiroItem() throws Exception {
        setClicouNoPrimeiroItem(true);
        setClicouNoUltimoItem(false);
        setAbrirModalPanelConfirmacaoGravado("RichFaces.hideModalPanel('modalConfirmaGravacao');");
        //se algum dado de liberação for vazio limpar dados de liberação para não incluir na lista em memória
        limparObjetoListaEmMemoriaQuandoNaoGravar();
        //setando o contador
        setPosicaoObjetoListaLiberacoes(1);
        //setando o proximo objeto da lista
        setLiberacaoAcessoVO(listaLiberacoes.get(0));
        setMostrarModalPanelConfirmacaoGravado(true);
        setarNomeLabelPessoa(liberacaoAcessoVO);
        setMensagemDetalhada("");
    }

    public void analisarUltimoItem() throws Exception {
        //setar falso pois clicou no ultimo item e nao no primeiro
        setClicouNoPrimeiroItem(false);
        setClicouNoUltimoItem(true);
        if ((!liberacaoAcessoVO.getJustificativa().isEmpty() || liberacaoAcessoVO.getPessoa().getCodigo() != 0) && mostrarModalPanelConfirmacaoGravado) {
            setAbrirModalPanelConfirmacaoGravado("Richfaces.showModalPanel('modalConfirmaGravacao');");
            return;
        } else {
            ultimoItem();
        }
    }

    public void ultimoItem() throws Exception {
        //setar falso pois clicou no ultimo item e nao no primeiro
        setClicouNoPrimeiroItem(false);
        setClicouNoUltimoItem(true);

        setAbrirModalPanelConfirmacaoGravado("RichFaces.hideModalPanel('modalConfirmaGravacao');");
        //se algum dado de liberação for vazio limpar dados de liberação para não incluir na lista em memória
        limparObjetoListaEmMemoriaQuandoNaoGravar();
        //setando o contador
        setPosicaoObjetoListaLiberacoes(listaLiberacoes.size());
        //setando o proximo objeto da lista
        setLiberacaoAcessoVO(listaLiberacoes.get(listaLiberacoes.size() - 1));
        setMostrarModalPanelConfirmacaoGravado(true);
        setarNomeLabelPessoa(liberacaoAcessoVO);
        setMensagemDetalhada("");
    }

    public void limparObjetoListaEmMemoriaQuandoNaoGravar() throws Exception {
        //se algum campo estiver vazio inclusive usuarioJustificou e dataJustificativa que
        //são preenchidas internamente limpar todos os campos
        if (liberacaoAcessoVO.getJustificativa().isEmpty()
                || liberacaoAcessoVO.getUsuarioJustificou() == null
                || liberacaoAcessoVO.getUsuarioJustificou().getCodigo() == 0
                || liberacaoAcessoVO.getDthrJustificativa() == null
                || liberacaoAcessoVO.getPessoa() == null
                || liberacaoAcessoVO.getPessoa().getCodigo() == 0) {
            //se não for do tipo TERCEIRIZADO e VISITANTE DIVERSO
            if (liberacaoAcessoVO.getTipoLiberacao() != TipoLiberacaoEnum.TERCEIRIZADO
                    && liberacaoAcessoVO.getTipoLiberacao() != TipoLiberacaoEnum.VISITANTE_DIVERSO) {
                liberacaoAcessoVO.setJustificativa("");
                liberacaoAcessoVO.setUsuarioJustificou(new UsuarioVO());
                liberacaoAcessoVO.setDthrJustificativa(null);
                liberacaoAcessoVO.setPessoa(new PessoaVO());
            }
        }
        //analisando se caso o usuário responsável seja diferente do que justificou e o mesmo editar
        //a justificativa ou a pessoa que foi liberada então o sistema consulta novamente para mostrar que não gravou
        if (getUsuarioLogado().getCodigo()
                != getFacade().getLiberacaoAcesso().consultarUsuarioJustificou(liberacaoAcessoVO.getCodigo())) {
            try {
                listaLiberacoes.set(posicaoObjetoListaLiberacoes - 1,
                        getFacade().getLiberacaoAcesso().consultarPorChavePrimaria(liberacaoAcessoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public Double getPercentualClientes() {
        if (totalAcessosClientes + totalAcessosColaboradores > 0) {
            return Uteis.arredondarForcando2CasasDecimais((percentualCliCol * totalAcessosClientes) / (totalAcessosClientes + totalAcessosColaboradores));
        }
        return 0.0;
    }

    public Double getPercentualColaboradores() {
        if (totalAcessosClientes + totalAcessosColaboradores > 0) {
            return Uteis.arredondarForcando2CasasDecimais((percentualCliCol * totalAcessosColaboradores) / (totalAcessosClientes + totalAcessosColaboradores));
        }
        return 0.0;
    }

    /**
     * Valida a permissão do usuário logado para a entidade RateioIntegracao
     * que usa a permissão "Plano de Contas"
     * @throws Exception
     */
    public void validarPermissaoJustificarAcessos() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "GestaoControleAcesso", "6.19 - Justificar Liberação de Acesso");
            }
        }
    }

    /**
     * Monta a combo de empresa
     * @throws Exception
     */
    private void montarListaEmpresa() throws Exception {
        setListaEmpresa(new ArrayList<SelectItem>());
        getListaEmpresa().add(new SelectItem(0, ""));

        List resultadoConsulta = getFacade().getUsuarioPerfilAcesso().obterListaEmpresasPermitidas(getUsuarioLogado());
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            SelectItem obj = (SelectItem) i.next();
            getListaEmpresa().add(new SelectItem(obj.getValue(), obj.getLabel()));
        }
    }

    /**
     * Inicializa a empresa logada
     * @throws Exception
     */
    private void inicializarEmpresaLogado() throws Exception {
        setEmpresa((EmpresaVO) getEmpresaLogado().getClone(false));
        // se empresa logada esta null
        if (getEmpresa() == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    /**
     * Usado para fechar o modal de justificativa
     */
    public void fecharPanelJustificativa() throws Exception {
        setAbrirModalJustificativa(false);
        limparObjetoListaEmMemoriaQuandoNaoGravar();
    }

    /**
     * Usado para fechar o modal de confirmação de gravação
     */
    public void fecharPanelConfirmacaoGravacao() {
        setMostrarModalPanelConfirmacaoGravado(false);
    }

    /**
     * Usado para abrir o modal de justificativa para criação
     */
    public void abrirModalJustificativa() {
        recuperarObjetoLiberacaoLista("Justificar Acesso");
        setMensagemDetalhada("");
        setAbrirModalJustificativa(true);
        liberacaoAcessoVO.setNovoObj(true);
    }

    /**
     * Usado para abrir o modal de justificativa para alteração
     * @throws Exception
     */
    public void abrirModalAlterarJustificativa() throws Exception {
        recuperarObjetoLiberacaoLista("Alterar Justificativa de Acesso");
        analisarSeUsuarioLogadoPodeEditar();
    }

    /**
     * Verifica se o usuário logado é o mesmo que cadastrou a justificativa
     * Se não for não permitir abrir o modal
     * @throws Exception
     */
    public void analisarSeUsuarioLogadoPodeEditar() throws Exception {
        if (getUsuarioLogado().getCodigo().intValue() == liberacaoAcessoVO.getUsuarioJustificou().getCodigo().intValue()) {
            setAbrirModalJustificativa(true);
            setMsgAlert("");
        } else {
            setAbrirModalJustificativa(false);
            setMensagemID("");
            setMensagemDetalhada("Somente o usuário que justificou poderá editar a justificativa");
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public LiberacaoAcessoVO recuperarObjetoLiberacaoLista(String tituloModalPanelJustificativa) {
        setTituloModalPanelJustificativa(tituloModalPanelJustificativa);
        setLiberacaoAcessoVO(new LiberacaoAcessoVO());
        setPosicaoObjetoListaLiberacoes(0);
        setNomeLabelPessoa("");
        LiberacaoAcessoVO obj = (LiberacaoAcessoVO) context().getExternalContext().getRequestMap().get("liberacaoAcesso");

        for (int i = 0; i < listaLiberacoes.size(); i++) {
            if (obj.getCodigo() == listaLiberacoes.get(i).getCodigo()) {
                setPosicaoObjetoListaLiberacoes(i + 1);
                break;
            }
        }
        if (obj != null) {
            setLiberacaoAcessoVO(obj);
        } else {
            setLiberacaoAcessoVO(new LiberacaoAcessoVO());
        }
        setarNomeLabelPessoa(obj);
        setMensagemID("msg_entre_dados");
        return getLiberacaoAcessoVO();
    }

    public void setarNomeLabelPessoa(LiberacaoAcessoVO obj) {
        if (obj.getTipoLiberacao() != null) {
            if (obj.getTipoLiberacao() == TipoLiberacaoEnum.CLIENTE) {
                setNomeLabelPessoa("Cliente");
            } else if (obj.getTipoLiberacao() == TipoLiberacaoEnum.CLIENTE_VISITANTE) {
                setNomeLabelPessoa("Cliente Visitante");
            } else if (obj.getTipoLiberacao() == TipoLiberacaoEnum.COLABORADOR) {
                setNomeLabelPessoa("Colaborador");
            } else if (obj.getTipoLiberacao() == TipoLiberacaoEnum.TERCEIRIZADO) {
                setNomeLabelPessoa("Colaborador");
            } else if (obj.getTipoLiberacao() == TipoLiberacaoEnum.VISITANTE_DIVERSO) {
                setNomeLabelPessoa("Responsável");
            } else {
                setNomeLabelPessoa("");
            }
        }
    }

    /**
     * Método responsável por processar a consulta na entidade <code>Pessoa</code> por meio dos parametros informados na tela de justificativa
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que realizam busca pelos parâmetros informados 
     * montando automaticamente o resultado da consulta para apresentação.
     */
    public List<PessoaVO> executarAutocompleteConsultaPessoa(Object suggest) {
        String pref = (String) suggest;
        liberacaoAcessoVO.setPessoa(new PessoaVO());
        ArrayList<PessoaVO> result = new ArrayList<PessoaVO>();
        try {
            if (liberacaoAcessoVO.getTipoLiberacao() == TipoLiberacaoEnum.COLABORADOR
                    || liberacaoAcessoVO.getTipoLiberacao() == TipoLiberacaoEnum.TERCEIRIZADO
                   ) {
                if (pref.equals("%")) {
                    result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                            consultarPessoaColaboradorComLimite(
                            empresa.getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_ROBO);

                } else {
                    result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                            consultarPessoaPorNomeColaboradorComLimite(
                            empresa.getCodigo(), pref, false, Uteis.NIVELMONTARDADOS_ROBO);
                }
            } else if (liberacaoAcessoVO.getTipoLiberacao() == TipoLiberacaoEnum.CLIENTE) {
                //deverá ser consultado clientes não visitantes
                if (pref.equals("%")) {
                    result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                            consultarPessoaClienteComLimite(0, false, false, Uteis.NIVELMONTARDADOS_ROBO);

                } else {
                    result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                            consultarPessoaPorNomeClienteComLimite(0, pref, false, false, Uteis.NIVELMONTARDADOS_ROBO);
                }

            } else if (liberacaoAcessoVO.getTipoLiberacao() == TipoLiberacaoEnum.CLIENTE_VISITANTE) {
                //deverá ser consultado somente os clientes-visitantes que realizaram BVs entre 5 dias antes e 5 dias depois do dia em que a pessoa realizou o acesso
                if (pref.equals("%")) {
                    result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                            consultarPessoaClienteVisitantePorDataBVComLimite(
                            empresa.getCodigo().intValue(), Uteis.obterDataAnterior(liberacaoAcessoVO.getDataHora(), 5),
                            Uteis.obterDataFutura2(liberacaoAcessoVO.getDataHora(),5), "00:00", "23:59", Uteis.NIVELMONTARDADOS_ROBO);

                } else {
                    result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                            consultarPessoaPorNomeClienteVisitantePorDataBVComLimite(
                            empresa.getCodigo().intValue(), pref, Uteis.obterDataAnterior(liberacaoAcessoVO.getDataHora(), 5),
                            Uteis.obterDataFutura2(liberacaoAcessoVO.getDataHora(),5), "00:00", "23:59", Uteis.NIVELMONTARDADOS_ROBO);
                }

            }else if(liberacaoAcessoVO.getTipoLiberacao()== TipoLiberacaoEnum.VISITANTE_DIVERSO){
                if (pref.equals("%")) {
                    result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                            consultarPessoaClienteComLimite(
                            empresa.getCodigo().intValue(), false, true, Uteis.NIVELMONTARDADOS_ROBO);

                } else {
                    result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                            consultarPessoaPorNomeClienteComLimite(
                            empresa.getCodigo(), pref, true, false, Uteis.NIVELMONTARDADOS_ROBO);
                }
            }

            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<PessoaVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarPessoaSuggestionBox() throws Exception {
        PessoaVO pessoaVO = (PessoaVO) request().getAttribute("result");
        if (pessoaVO != null) {
            getLiberacaoAcessoVO().setPessoa(pessoaVO);
        }
    }


    /**
     * Valida a permissão do usuário logado para a entidade ConsultarInfoTodasEmpresas
     * que usa a permissão "9.47 - Consultar relatórios de todas as empresas"
     * @throws Exception
     */
    public boolean validarPermissaoConsultarInfoTodasEmpresas() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return true;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_FECHAMENTO_ACESSO));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "ConsultarInfoTodasEmpresas", "9.47 - Consultar relatórios de todas as empresas");
                return true;
            }
        }
        return false;
    }

    /**
     * Retorna a data inicial do periodo para ser usada nesse controlador
     * @return
     */
    public Date getDataPeriodoInicial() {
        if (dataPeriodoInicial == null) {
            dataPeriodoInicial = Calendario.hoje();
        }
        return dataPeriodoInicial;
    }

    /**
     * Seta a data inicial do periodo para ser usada nesse controlador
     * @param dataPeriodoInicial
     */
    public void setDataPeriodoInicial(Date dataPeriodoInicial) {
        this.dataPeriodoInicial = dataPeriodoInicial;
    }

    /**
     * Retorna a data final do periodo de acesso para ser usada nesse controlador
     * @return
     */
    public Date getDataPeriodoFinal() {
        if (dataPeriodoFinal == null) {
            dataPeriodoFinal = Calendario.hoje();
        }
        return dataPeriodoFinal;
    }

    public void setDataPeriodoFinal(Date dataPeriodoFinal) {
        this.dataPeriodoFinal = dataPeriodoFinal;
    }

    public List<SelectItem> getListaEmpresa() {
        return listaEmpresa;
    }

    public void setListaEmpresa(List<SelectItem> listaEmpresa) {
        this.listaEmpresa = listaEmpresa;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public List<SelectItem> getListaPeriodosPadrao() {
        return listaPeriodosPadrao;
    }

    public void setListaPeriodosPadrao(List<SelectItem> listaPeriodosPadrao) {
        this.listaPeriodosPadrao = listaPeriodosPadrao;
    }

    /**
     * Usada para retornar o valor do percentual de liberação
     * @return the percentualLiberacao
     */
    public Double getPercentualLiberacao() {
        if (percentualLiberacao == null) {
            return 0.0;
        }
        return percentualLiberacao;
    }

    public void setPercentualLiberacao(Double percentualLiberacao) {
        this.percentualLiberacao = percentualLiberacao;
    }

    /**
     * Usada para retornar o valor do percentual de acessos de clientes e colaboradores
     * @return the percentualLiberacao
     */
    public Double getPercentualCliCol() {
        if (percentualCliCol == null) {
            return 0.0;
        }
        return percentualCliCol;
    }

    public void setPercentualCliCol(Double percentualCliCol) {
        this.percentualCliCol = percentualCliCol;
    }

    /**
     * @return the totalAcessos
     */
    public Integer getTotalAcessos() {
        if (totalAcessos == null) {
            return 0;
        }
        return totalAcessos;
    }

    /**
     * @param totalAcessos the totalAcessos to set
     */
    public void setTotalAcessos(Integer totalAcessos) {
        this.totalAcessos = totalAcessos;
    }

    /**
     * @return the listaTipoLiberacaoEnum
     */
    public List<TipoLiberacaoEnum> getListaTipoLiberacaoEnumLiberacao() {
        if (listaTipoLiberacaoEnumLiberacao == null) {
            listaTipoLiberacaoEnumLiberacao = new ArrayList<>();
            listaTipoLiberacaoEnumLiberacao.add(TipoLiberacaoEnum.CLIENTE);
            listaTipoLiberacaoEnumLiberacao.add(TipoLiberacaoEnum.CLIENTE_VISITANTE);
            listaTipoLiberacaoEnumLiberacao.add(TipoLiberacaoEnum.COLABORADOR);
            listaTipoLiberacaoEnumLiberacao.add(TipoLiberacaoEnum.TERCEIRIZADO);
            listaTipoLiberacaoEnumLiberacao.add(TipoLiberacaoEnum.VISITANTE_DIVERSO);
        }
        return listaTipoLiberacaoEnumLiberacao;
    }

    public void setListaTipoLiberacaoEnumLiberacao(List<TipoLiberacaoEnum> listaTipoLiberacaoEnumLiberacao) {
        this.listaTipoLiberacaoEnumLiberacao = listaTipoLiberacaoEnumLiberacao;
    }

    /**
     * @return the horarioPeriodoInicial
     */
    public String getHorarioPeriodoInicial() {
        return horarioPeriodoInicial;
    }

    /**
     * @param horarioPeriodoInicial the horarioPeriodoInicial to set
     */
    public void setHorarioPeriodoInicial(String horarioPeriodoInicial) {
        this.horarioPeriodoInicial = horarioPeriodoInicial;
    }

    /**
     * @return the horarioPeriodoFinal
     */
    public String getHorarioPeriodoFinal() {
        return horarioPeriodoFinal;
    }

    /**
     * @param horarioPeriodoFinal the horarioPeriodoFinal to set
     */
    public void setHorarioPeriodoFinal(String horarioPeriodoFinal) {
        this.horarioPeriodoFinal = horarioPeriodoFinal;
    }

    /**
     * @return the pesquisar
     */
    public boolean isPesquisar() {
        return pesquisar;
    }

    /**
     * @param pesquisar the pesquisar to set
     */
    public void setPesquisar(boolean pesquisar) {
        this.pesquisar = pesquisar;
    }

    /**
     * @return the tipoPadraoConsulta
     */
    public String getTipoPadraoPeriodo() {
        return tipoPadraoPeriodo;
    }

    public void setTipoPadraoPeriodo(String tipoPadraoPeriodo) {
        this.tipoPadraoPeriodo = tipoPadraoPeriodo;
    }

    /**
     * @return the totalAcessosClientes
     */
    public int getTotalAcessosClientes() {
        return totalAcessosClientes;
    }

    /**
     * @param totalAcessosClientes the totalAcessosClientes to set
     */
    public void setTotalAcessosClientes(int totalAcessosClientes) {
        this.totalAcessosClientes = totalAcessosClientes;
    }

    /**
     * @return the totalAcessosColaboradores
     */
    public int getTotalAcessosColaboradores() {
        return totalAcessosColaboradores;
    }

    /**
     * @param totalAcessosColaboradores the totalAcessosColaboradores to set
     */
    public void setTotalAcessosColaboradores(int totalAcessosColaboradores) {
        this.totalAcessosColaboradores = totalAcessosColaboradores;
    }

    /**
     * @return the listaFechamentoAcessoRel
     */
    public List<FechamentoAcessoRelTO> getListaFechamentoAcessoRel() {
        return listaFechamentoAcessoRel;
    }

    /**
     * @param listaFechamentoAcessoRel the listaFechamentoAcessoRel to set
     */
    public void setListaFechamentoAcessoRel(List<FechamentoAcessoRelTO> listaFechamentoAcessoRel) {
        this.listaFechamentoAcessoRel = listaFechamentoAcessoRel;
    }

    /**
     * @return the abrirGridMaisDetalhes
     */
    public Boolean getAbrirGridMaisDetalhes() {
        return abrirGridMaisDetalhes;
    }

    /**
     * @param abrirGridMaisDetalhes the abrirGridMaisDetalhes to set
     */
    public void setAbrirGridMaisDetalhes(Boolean abrirGridMaisDetalhes) {
        this.abrirGridMaisDetalhes = abrirGridMaisDetalhes;
    }

    /**
     * @return the listaLiberacoes
     */
    public List<LiberacaoAcessoVO> getListaLiberacoes() {
        return listaLiberacoes;
    }

    /**
     * @param listaLiberacoes the listaLiberacoes to set
     */
    public void setListaLiberacoes(List<LiberacaoAcessoVO> listaLiberacoes) {
        this.listaLiberacoes = listaLiberacoes;
    }

    /**
     * @return the fechamentoAcessoRelTO
     */
    public FechamentoAcessoRelTO getFechamentoAcessoRelTO() {
        return fechamentoAcessoRelTO;
    }

    /**
     * @param fechamentoAcessoRelTO the fechamentoAcessoRelTO to set
     */
    public void setFechamentoAcessoRelTO(FechamentoAcessoRelTO fechamentoAcessoRelTO) {
        this.fechamentoAcessoRelTO = fechamentoAcessoRelTO;
    }

    /**
     * @return the tituloModalPanelJustificativa
     */
    public String getTituloModalPanelJustificativa() {
        return tituloModalPanelJustificativa;
    }

    /**
     * @param tituloModalPanelJustificativa the tituloModalPanelJustificativa to set
     */
    public void setTituloModalPanelJustificativa(String tituloModalPanelJustificativa) {
        this.tituloModalPanelJustificativa = tituloModalPanelJustificativa;
    }

    /**
     * @return the liberacaoAcessoVO
     */
    public LiberacaoAcessoVO getLiberacaoAcessoVO() {
        if (liberacaoAcessoVO == null) {
            liberacaoAcessoVO = new LiberacaoAcessoVO();
        }
        return liberacaoAcessoVO;
    }

    /**
     * @param liberacaoAcessoVO the liberacaoAcessoVO to set
     */
    public void setLiberacaoAcessoVO(LiberacaoAcessoVO liberacaoAcessoVO) {
        this.liberacaoAcessoVO = liberacaoAcessoVO;
    }

    /**
     * @return the nomeLabelPessoa
     */
    public String getNomeLabelPessoa() {
        return nomeLabelPessoa;
    }

    /**
     * @param nomeLabelPessoa the nomeLabelPessoa to set
     */
    public void setNomeLabelPessoa(String nomeLabelPessoa) {
        this.nomeLabelPessoa = nomeLabelPessoa;
    }

    /**
     * @return the posicaoObjetoListaLiberacoes
     */
    public int getPosicaoObjetoListaLiberacoes() {
        return posicaoObjetoListaLiberacoes;
    }

    /**
     * @param posicaoObjetoListaLiberacoes the posicaoObjetoListaLiberacoes to set
     */
    public void setPosicaoObjetoListaLiberacoes(int posicaoObjetoListaLiberacoes) {
        this.posicaoObjetoListaLiberacoes = posicaoObjetoListaLiberacoes;
    }

    /**
     * @return the abrirModalJustificativa
     */
    public boolean isAbrirModalJustificativa() {
        return abrirModalJustificativa;
    }

    /**
     * @param abrirModalJustificativa the abrirModalJustificativa to set
     */
    public void setAbrirModalJustificativa(boolean abrirModalJustificativa) {
        this.abrirModalJustificativa = abrirModalJustificativa;
    }

    /**
     * @return the mostrarModalPanelConfirmacaoGravado
     */
    public boolean isMostrarModalPanelConfirmacaoGravado() {
        return mostrarModalPanelConfirmacaoGravado;
    }

    /**
     * @param mostrarModalPanelConfirmacaoGravado the mostrarModalPanelConfirmacaoGravado to set
     */
    public void setMostrarModalPanelConfirmacaoGravado(boolean mostrarModalPanelConfirmacaoGravado) {
        this.mostrarModalPanelConfirmacaoGravado = mostrarModalPanelConfirmacaoGravado;
    }

    /**
     * @return the abrirModalPanelConfirmacaoGravado
     */
    public String getAbrirModalPanelConfirmacaoGravado() {
        return abrirModalPanelConfirmacaoGravado;
    }

    /**
     * @param abrirModalPanelConfirmacaoGravado the abrirModalPanelConfirmacaoGravado to set
     */
    public void setAbrirModalPanelConfirmacaoGravado(String abrirModalPanelConfirmacaoGravado) {
        this.abrirModalPanelConfirmacaoGravado = abrirModalPanelConfirmacaoGravado;
    }

    /**
     * @return the responsavelEnvioEmail
     */
    public UsuarioVO getResponsavelEnvioEmail() {
        return responsavelEnvioEmail;
    }

    /**
     * @param responsavelEnvioEmail the responsavelEnvioEmail to set
     */
    public void setResponsavelEnvioEmail(UsuarioVO responsavelEnvioEmail) {
        this.responsavelEnvioEmail = responsavelEnvioEmail;
    }

    /**
     * @return the qtdBVs
     */
    public Integer getQtdBVs() {
        return qtdBVs;
    }

    /**
     * @param qtdBVs the qtdBVs to set
     */
    public void setQtdBVs(Integer qtdBVs) {
        this.qtdBVs = qtdBVs;
    }

    /**
     * @return the mostrarModalPanelListaEmail
     */
    public boolean isMostrarModalPanelListaEmail() {
        return mostrarModalPanelListaEmail;
    }

    /**
     * @param mostrarModalPanelListaEmail the mostrarModalPanelListaEmail to set
     */
    public void setMostrarModalPanelListaEmail(boolean mostrarModalPanelListaEmail) {
        this.mostrarModalPanelListaEmail = mostrarModalPanelListaEmail;
    }

    /**
     * @return the configuracaoSistemaVO
     */
    public ConfiguracaoSistemaVO getConfiguracaoSistemaVO() {
        return configuracaoSistemaVO;
    }

    /**
     * @param configuracaoSistemaVO the configuracaoSistemaVO to set
     */
    public void setConfiguracaoSistemaVO(ConfiguracaoSistemaVO configuracaoSistemaVO) {
        this.configuracaoSistemaVO = configuracaoSistemaVO;
    }

    /**
     * @return the clicouNoPrimeiroItem
     */
    public boolean isClicouNoPrimeiroItem() {
        return clicouNoPrimeiroItem;
    }

    /**
     * @param clicouNoPrimeiroItem the clicouNoPrimeiroItem to set
     */
    public void setClicouNoPrimeiroItem(boolean clicouNoPrimeiroItem) {
        this.clicouNoPrimeiroItem = clicouNoPrimeiroItem;
    }

    /**
     * @return the clicouNoUltimoItem
     */
    public boolean isClicouNoUltimoItem() {
        return clicouNoUltimoItem;
    }

    /**
     * @param clicouNoUltimoItem the clicouNoUltimoItem to set
     */
    public void setClicouNoUltimoItem(boolean clicouNoUltimoItem) {
        this.clicouNoUltimoItem = clicouNoUltimoItem;
    }

    /**
     * @return the clicouNoBotaoGravar
     */
    public boolean isClicouNoBotaoGravar() {
        return clicouNoBotaoGravar;
    }

    /**
     * @param clicouNoBotaoGravar the clicouNoBotaoGravar to set
     */
    public void setClicouNoBotaoGravar(boolean clicouNoBotaoGravar) {
        this.clicouNoBotaoGravar = clicouNoBotaoGravar;
    }

    public boolean isPermissaoConsultarInfoTodasEmpresas() { return permissaoConsultarInfoTodasEmpresas; }

    public void setPermissaoConsultarInfoTodasEmpresas(boolean permissaoConsultarInfoTodasEmpresas) {
        this.permissaoConsultarInfoTodasEmpresas = permissaoConsultarInfoTodasEmpresas;
    }
}

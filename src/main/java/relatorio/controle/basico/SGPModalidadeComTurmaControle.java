package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.SGPModalidadeComTurmaTO;
import relatorio.negocio.jdbc.basico.SGPModalidadeComTurmaRel;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class SGPModalidadeComTurmaControle extends SuperControleRelatorio {
    private List<SGPModalidadeComTurmaTO> listaFrequencias = new ArrayList<SGPModalidadeComTurmaTO>();
    private Date dataInicio;
    private Date dataFim;
    private List<ModalidadeVO> listaModalidades = new ArrayList<>();
    private boolean selecionarEmpresa;
    private String filtros;
    private List<ClienteVO> listaClientes;
    private List<Integer> turmasSelecionadas;
    private List<TurmaVO> listaTurmas = new ArrayList<>();
    private boolean visualizacaoDetalhadaPorTurma;

    public SGPModalidadeComTurmaControle() {
        inicializarDados();
    }

    public void inicializarDados() {
        try {
            setVisualizacaoDetalhadaPorTurma(false);
            setListaTurmas(new ArrayList<>());
            setListaFrequencias(new ArrayList<>());
            setDataInicio(new Date());
            setDataFim(new Date());
            setSelecionarEmpresa(false);
            setFiltros("");
            montarListaSelectItemEmpresa();
            montarListaModalidades();
            limparMsg();
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarTodasModalidades() {
        getListaModalidades().forEach(it -> it.setSelecionado(true));
    }

    public String consultar() {
        try {
            limparMsg();
            setarFiltros();
            SGPModalidadeComTurmaRel rel = getFacade().getSGPModalidadeComTurmaRel();
            rel.setarParametrosConsulta(getDataInicio(),
                    getDataFim(),
                    getEmpresa(),
                    getListaModalidades(),
                    isVisualizacaoDetalhadaPorTurma(),
                    getTurmasSelecionadas());
            rel.validarDados();
            setListaFrequencias(rel.consultar());
            setMensagem("Dados consultados");
            return "relatorio";
        } catch (ConsistirException e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }


    private void setarFiltros() {
        StringBuilder filtros = new StringBuilder();
        filtros.append("<b>Inicio: </b> ").append(getDataInicio_Apresentar()).append("</br>");
        filtros.append("<b>Fim: </b> ").append(getDataFim_Apresentar()).append("</br>");
        filtros.append("<b>Empresa: </b>").append(getEmpresa().getNome()).append("</br>");
        filtros.append("<b>Modalidades: </b>");
        for (ModalidadeVO modalidade : listaModalidades) {
            if (modalidade.getSelecionado()) {
                filtros.append(modalidade.getNome()).append("</br>");
            }
        }
        if(getTurmasSelecionadas()!=null && !getTurmasSelecionadas().isEmpty()) {
            try {
                filtros.append("<b>Turmas: </b>");
                for (Integer idTurma : getTurmasSelecionadas()) {
                    TurmaVO turmaVO = getFacade().getTurma().consultarPorChavePrimaria(idTurma,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    filtros.append(turmaVO.getDescricao()).append("</br>");
            }
            }catch(Exception e){
                e.printStackTrace();
            }
        }
        setFiltros(filtros.toString());
    }

    public String voltar() {
        return "consultar";
    }

    public void montarListaSelectItemEmpresa() {
        try {
            if (JSFUtilities.isJSFContext()) {
                setListaEmpresas(new ArrayList<SelectItem>());
                setEmpresa(getEmpresaLogado());
            }
            if (getEmpresa() == null || getEmpresa().getCodigo() == 0) {
                setSelecionarEmpresa(true);
                montarListaEmpresas();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void montarListaModalidades() throws Exception {
        setListaModalidades(getFacade().getModalidade().consultarTodasModalidades(getEmpresa().getCodigo(), true, true));
    }

    public List<SelectItem> getListaSelectItemTurmas() {
        List<SelectItem> turmas = new ArrayList<>();
        try {
            for (ModalidadeVO itemModalidade : getListaModalidades()) {
                if(!itemModalidade.getSelecionado()){
                    continue;
                }
                List<TurmaVO> listaTurmas = getFacade().getTurma().consultarPorCodigoModalidade(itemModalidade.getCodigo(), getEmpresa().getCodigo(),
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                turmas.addAll(listaTurmas.stream()
                        .map(turma -> new SelectItem(turma.getCodigo(), turma.getDescricao()) )
                        .sorted((o1, o2) -> o1.getLabel().compareToIgnoreCase(o2.getLabel()))
                        .collect(Collectors.toList()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return turmas;
    }
    public void atualizarVisualizacao() {
        // Apenas um método vazio para atualizar a view corretamente
    }

    public boolean isSelecionarEmpresa() {
        return selecionarEmpresa;
    }

    public void setSelecionarEmpresa(Boolean selecionarEmpresa) {
        this.selecionarEmpresa = selecionarEmpresa;
    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public List<SGPModalidadeComTurmaTO> getListaFrequencias() {
        return listaFrequencias;
    }

    public void setListaFrequencias(List<SGPModalidadeComTurmaTO> listaFrequencias) {
        this.listaFrequencias = listaFrequencias;
    }

    public List<ModalidadeVO> getListaModalidades() {
        return listaModalidades;
    }

    public void setListaModalidades(List<ModalidadeVO> listaModalidades) {
        this.listaModalidades = listaModalidades;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataInicio_Apresentar() {
        return Uteis.getData(dataInicio);
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public String getDataFim_Apresentar() {
        return Uteis.getData(dataFim);
    }

    public List<ClienteVO> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClienteVO> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public void prepararListaNaoSocio() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaNaoSocio());
        }
    }

    public void prepararListaAluno() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaAlunos());
        }
    }

    public void prepararListaSocio() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaSocios());
        }
    }

    public void prepararListaComerciario() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaComerciario());
        }
    }

    public void prepararListaDependente() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaDependentes());
        }
    }

    public void prepararListaUsuario() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaUsuarios());
        }
    }

    public void prepararListaEvento() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaEventos());
        }
    }

    public void prepararListaCanceladosDesistentes() {
        SGPModalidadeComTurmaTO item = (SGPModalidadeComTurmaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaCanceladosDesistentes());
        }
    }

    public boolean isVisualizacaoDetalhadaPorTurma() {
        return visualizacaoDetalhadaPorTurma;
    }

    public void setVisualizacaoDetalhadaPorTurma(boolean visualizacaoDetalhadaPorTurma) {
        this.visualizacaoDetalhadaPorTurma = visualizacaoDetalhadaPorTurma;
    }

    public void setSelecionarEmpresa(boolean selecionarEmpresa) {
        this.selecionarEmpresa = selecionarEmpresa;
    }

    public List<Integer> getTurmasSelecionadas() {
        return turmasSelecionadas;
    }

    public void setTurmasSelecionadas(List<Integer> turmasSelecionadas) {
        this.turmasSelecionadas = turmasSelecionadas;
    }

    public List<TurmaVO> getListaTurmas() {
        return listaTurmas;
    }

    public void setListaTurmas(List<TurmaVO> listaTurmas) {
        this.listaTurmas = listaTurmas;
    }
    public String getAtributosDinamicos() {
        String atributos = "nomeModalidade=Modalidade,qtdeNaoSocio=Não Sócios,qtdeAlunos=Alunos,qtdeSocios=Sócios,qtdeComerciario=Comerciários,qtdeDependente=Dependentes,qtdeUsuario=Usuários,qtdeEventos=Eventos,totalCategorias=Total,qtdTurmasCriadasPeriodo=Turmas,contratosCanceladosDesistentes=Evasões,qtdAulasPeriodo=Horas/Aulas,frequenciaPeriodo=Frequência";

        if (this.isVisualizacaoDetalhadaPorTurma()) {
            atributos = atributos.replace("nomeModalidade=Modalidade","nomeModalidade=Modalidade,nomeTurma=Turma");
        }

        return atributos;
    }

}

package relatorio.controle.basico;

import negocio.armario.GrupoArmarioEnum;
import negocio.armario.StatusArmarioEnum;
import negocio.armario.TamanhoArmarioVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by Rafael on 02/12/2015.
 */
public class FiltroArmarioTO {

    private Date periodoRenovacaoDe;
    private Date periodoRenovacaoAte;
    private Date periodoVencimentoDe;
    private Date periodoVencimentoAte;
    private Integer tamanhoArmarioSelecionado;
    private Integer planoLocacao;
    private Date periodoLocacaoDe;
    private Date periodoLocacaoAte;
    private String tipoArmarioSelecionado;
    private Boolean somenteParcelasAtrasadas = false;
    private String numeroArmario;
    private String contratoAssinado = null;
    //Filtros usados na Tela de Armários
    private String valorConsulta;
    private TamanhoArmarioVO tamanhoArmario;
    private GrupoArmarioEnum grupoSelecionado;
    private Integer empresa;
    private StatusArmarioEnum status;
    private int limit;
    private int offset;
    private List<FiltroArmarioTO> filtroConsulta;
    private Boolean habilitadoGestaoArmarios;
    private Boolean ordernarDataVigencia;

    public Date getPeriodoRenovacaoDe() {
        return periodoRenovacaoDe;
    }

    public void setPeriodoRenovacaoDe(Date periodoRenovacaoDe) {
        this.periodoRenovacaoDe = periodoRenovacaoDe;
    }

    public Date getPeriodoRenovacaoAte() {
        return periodoRenovacaoAte;
    }

    public void setPeriodoRenovacaoAte(Date periodoRenovacaoAte) {
        this.periodoRenovacaoAte = periodoRenovacaoAte;
    }

    public Date getPeriodoVencimentoDe() {
        return periodoVencimentoDe;
    }

    public void setPeriodoVencimentoDe(Date periodoVencimentoDe) {
        this.periodoVencimentoDe = periodoVencimentoDe;
    }

    public Date getPeriodoVencimentoAte() {
        return periodoVencimentoAte;
    }

    public void setPeriodoVencimentoAte(Date periodoVencimentoAte) {
        this.periodoVencimentoAte = periodoVencimentoAte;
    }

    public Integer getTamanhoArmarioSelecionado() {
        return tamanhoArmarioSelecionado;
    }

    public void setTamanhoArmarioSelecionado(Integer tamanhoArmarioSelecionado) {
        this.tamanhoArmarioSelecionado = tamanhoArmarioSelecionado;
    }

    public Integer getPlanoLocacao() {
        return planoLocacao;
    }

    public void setPlanoLocacao(Integer planoLocacao) {
        this.planoLocacao = planoLocacao;
    }

    public Date getPeriodoLocacaoDe() {
        return periodoLocacaoDe;
    }

    public void setPeriodoLocacaoDe(Date periodoLocacaoDe) {
        this.periodoLocacaoDe = periodoLocacaoDe;
    }

    public Date getPeriodoLocacaoAte() {
        return periodoLocacaoAte;
    }

    public void setPeriodoLocacaoAte(Date periodoLocacaoAte) {
        this.periodoLocacaoAte = periodoLocacaoAte;
    }

    public String getTipoArmarioSelecionado() {
        return tipoArmarioSelecionado;
    }

    public void setTipoArmarioSelecionado(String tipoArmarioSelecionado) {
        this.tipoArmarioSelecionado = tipoArmarioSelecionado;
    }

    public Boolean getSomenteParcelasAtrasadas() {
        return somenteParcelasAtrasadas;
    }

    public void setSomenteParcelasAtrasadas(Boolean somenteParcelasAtrasadas) {
        this.somenteParcelasAtrasadas = somenteParcelasAtrasadas;
    }

    public String getValorConsulta() {
        return valorConsulta;
    }

    public void setValorConsulta(String valorConsulta) {
        this.valorConsulta = valorConsulta;
    }


    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }


    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public Boolean getHabilitadoGestaoArmarios() {

        return habilitadoGestaoArmarios;
    }

    public void setHabilitadoGestaoArmarios(Boolean habilitadoGestaoArmarios) {
        this.habilitadoGestaoArmarios = habilitadoGestaoArmarios;
    }

    public TamanhoArmarioVO getTamanhoArmario() {
        return tamanhoArmario;
    }

    public void setTamanhoArmario(TamanhoArmarioVO tamanhoArmario) {
        this.tamanhoArmario = tamanhoArmario;
    }

    public GrupoArmarioEnum getGrupoSelecionado() {
        return grupoSelecionado;
    }

    public void setGrupoSelecionado(GrupoArmarioEnum grupoSelecionado) {
        this.grupoSelecionado = grupoSelecionado;
    }

    public StatusArmarioEnum getStatus() {
        return status;
    }

    public List<FiltroArmarioTO> getFiltroConsulta() {
        if(filtroConsulta==null)
            filtroConsulta = new ArrayList<FiltroArmarioTO>();
        return filtroConsulta;
    }

    public void setFiltroConsulta(List<FiltroArmarioTO> filtroConsulta) {
        this.filtroConsulta = filtroConsulta;
    }
    public Boolean getApresentarSit(){

        return getStatus()!= null ? true : false;
    }
    public Boolean getApresentarGrp(){

        return getGrupoSelecionado()!= null ? true : false;
    }
    public void setStatus(StatusArmarioEnum status) {
        this.status = status;
    }

    public Boolean getOrdernarDataVigencia() {
        return ordernarDataVigencia;
    }

    public void setOrdernarDataVigencia(Boolean ordernarDataVigencia) {
        this.ordernarDataVigencia = ordernarDataVigencia;
    }

    public String getNumeroArmario() {
        return numeroArmario;
    }

    public void setNumeroArmario(String numeroArmario) {
        this.numeroArmario = numeroArmario;
    }

    public String getContratoAssinado() {
        return contratoAssinado;
    }

    public void setContratoAssinado(String contratoAssinado) {
        this.contratoAssinado = contratoAssinado;
    }
}

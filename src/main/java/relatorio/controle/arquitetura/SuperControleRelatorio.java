package relatorio.controle.arquitetura;

import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.financeiro.ReciboDevolucaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRParameter;
import relatorio.arquitetura.VisualizadorRelatorio;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.Set;

public class SuperControleRelatorio extends SuperControle {

    protected Integer opcaoOrdenacao;
    protected List listaSelectItemOrdenacoesRelatorio;
    protected List listaRelatorio;
    protected String relatorio = "nao";
    protected String tipoRelatorio = "PDF";
    private boolean quebrarPagina = true;
    private Map<String, List<String>> mapaCaminhoRelatorio = new HashMap<String, List<String>>();

    public boolean isQuebrarPagina() {
        return quebrarPagina;
    }

    public void setQuebrarPagina(boolean quebrarPagina) {
        this.quebrarPagina = quebrarPagina;
    }

    public SuperControleRelatorio() {
    }

    /* Rotina responsável por acionar o Servlet de Apresentação de Relatório <code>VisualizadorRelatorio</code>,
     * fornecendo todos os parâmetros e dados necessários para a geração e visualização
     * do mesmo.
     * @param xml Dados a serem visualizados no relatório
     * @param tituloRelatorio Título do relatório
     * @param tipoRelatorio Pode assumir dois valores: HTML ou PDF
     * @param parserBuscaTag Padrão a ser utilizado pelo JasperReport
     *                       para filtrar quais dados do XML deverão ser processados
     * @param designIReport Nome do arquivo do IReport contendo o design gráfico do relatório
     * @param nomeUsuario Nome do usuário logado para apresentação no relatório
     */
    @Deprecated
    public void apresentarRelatorio(String nomeRelatorio,
                                    String xml,
                                    String tituloRelatorio,
                                    String nomeEmpresa,
                                    String mensagemRel,
                                    String tipoRelatorio,
                                    String parserBuscaTags,
                                    String designIReport,
                                    String nomeUsuario,
                                    String filtros/*,
            String ordenacao*/) throws Exception {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        request.setAttribute("xmlRelatorio", xml);
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("nomeRelatorio", nomeRelatorio);
        request.setAttribute("nomeEmpresa", nomeEmpresa);
        request.setAttribute("mensagemRel", mensagemRel);
        request.setAttribute("tituloRelatorio", tituloRelatorio);
        request.setAttribute("caminhoParserXML", parserBuscaTags);
        request.setAttribute("nomeDesignIReport", designIReport);
        request.setAttribute("nomeUsuario", nomeUsuario);
        //request.setAttribute("ordenacao", ordenacao);
        if (filtros.equals("")) {
            filtros = "nenhum";
        }
        request.setAttribute("filtros", filtros);
        process(request);
        /*context().getExternalContext().dispatch("/VisualizadorRelatorio");
        context().getCurrentInstance().responseComplete();*/
    }

    @Deprecated
    public void apresentarRelatorio(String nomeRelatorio,
                                    String xml,
                                    String tituloRelatorio,
                                    String nomeEmpresa,
                                    String mensagemRel,
                                    String tipoRelatorio,
                                    String parserBuscaTags,
                                    String designIReport,
                                    String nomeUsuario,
                                    String filtros,
                                    String parametro1,
                                    String parametro2,
                                    String parametro3,
                                    String parametro4,
                                    String parametro5,
                                    String parametro6,
                                    String parametro7,
                                    String totalJuroMulta,
                                    boolean mostrarCampo, boolean apresentarDadosSensiveis) throws Exception {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        request.setAttribute("xmlRelatorio", xml);
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("nomeRelatorio", nomeRelatorio);
        request.setAttribute("nomeEmpresa", nomeEmpresa);
        request.setAttribute("mensagemRel", mensagemRel);
        request.setAttribute("tituloRelatorio", tituloRelatorio);
        request.setAttribute("caminhoParserXML", parserBuscaTags);
        request.setAttribute("nomeDesignIReport", designIReport);
        request.setAttribute("nomeUsuario", nomeUsuario);
        request.setAttribute("parametro1", parametro1);
        request.setAttribute("parametro2", parametro2);
        request.setAttribute("parametro3", parametro3);
        request.setAttribute("parametro4", parametro4);
        request.setAttribute("parametro5", parametro5);
        request.setAttribute("parametro6", parametro6);
        request.setAttribute("parametro7", parametro7);
        request.setAttribute("totaljuromulta", totalJuroMulta);
        request.setAttribute("mostrarcampo", mostrarCampo);
        request.setAttribute("apresentarDadosSensiveis", apresentarDadosSensiveis);

        if (filtros.equals("")) {
            filtros = "nenhum";
        }
        request.setAttribute("filtros", filtros);
        process(request);
    }

    @Deprecated
    public void apresentarRelatorioObjetos(String nomeRelatorio,
                                           String tituloRelatorio,
                                           String nomeEmpresa,
                                           String mensagemRel,
                                           String imagemLogo,
                                           String tipoRelatorio,
                                           String parserBuscaTags,
                                           String designIReport,
                                           String nomeUsuario,
                                           String filtros,
                                           List listaObjetos) throws Exception {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("nomeRelatorio", nomeRelatorio);
        request.setAttribute("nomeEmpresa", nomeEmpresa);
        request.setAttribute("mensagemRel", mensagemRel);
        request.setAttribute("imagemLogo", imagemLogo);
        request.setAttribute("tituloRelatorio", tituloRelatorio);
        request.setAttribute("caminhoParserXML", parserBuscaTags);
        request.setAttribute("nomeDesignIReport", designIReport);
        request.setAttribute("nomeUsuario", nomeUsuario);
        request.setAttribute("listaObjetos", listaObjetos);
        request.setAttribute("tipoImplementacao", "OBJETO");
        if (filtros.equals("")) {
            filtros = "nenhum";
        }
        request.setAttribute("filtros", filtros);
        process(request);
        /*context().getExternalContext().dispatch("/VisualizadorRelatorio");
        context().getCurrentInstance().responseComplete();*/
    }

    @Deprecated
    public void apresentarRelatorioObjetos(String nomeRelatorio,
                                           String tituloRelatorio,
                                           String nomeEmpresa,
                                           String mensagemRel,
                                           String imagemLogo,
                                           String tipoRelatorio,
                                           String parserBuscaTags,
                                           String designIReport,
                                           String nomeUsuario,
                                           String filtros,
                                           Double totalPessoas,
                                           List listaObjetos) throws Exception {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("nomeRelatorio", nomeRelatorio);
        request.setAttribute("nomeEmpresa", nomeEmpresa);
        request.setAttribute("mensagemRel", mensagemRel);
        request.setAttribute("imagemLogo", imagemLogo);
        request.setAttribute("tituloRelatorio", tituloRelatorio);
        request.setAttribute("caminhoParserXML", parserBuscaTags);
        request.setAttribute("nomeDesignIReport", designIReport);
        request.setAttribute("nomeUsuario", nomeUsuario);
        request.setAttribute("totalPessoas", totalPessoas);
        request.setAttribute("listaObjetos", listaObjetos);
        request.setAttribute("tipoImplementacao", "OBJETO");
        if (filtros.equals("")) {
            filtros = "nenhum";
        }
        request.setAttribute("filtros", filtros);
        process(request);
    }

    @Deprecated
    public void apresentarRelatorioObjetos(String nomeRelatorio,
                                           String tituloRelatorio,
                                           String nomeEmpresa,
                                           String mensagemRel,
                                           String imagemLogo,
                                           String tipoRelatorio,
                                           String parserBuscaTags,
                                           String designIReport,
                                           String nomeUsuario,
                                           String filtros,
                                           String dataIni,
                                           String dataFim,
                                           String enderecoEmpresa,
                                           String cidadeEmpresa,
                                           String SUBREPORT_DIR,
                                           String SUBREPORT_DIR1,
                                           List listaObjetos) throws Exception {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("nomeRelatorio", nomeRelatorio);
        request.setAttribute("nomeEmpresa", nomeEmpresa);
        request.setAttribute("imagemLogo", imagemLogo);
        request.setAttribute("mensagemRel", mensagemRel);
        request.setAttribute("tituloRelatorio", tituloRelatorio);
        request.setAttribute("caminhoParserXML", parserBuscaTags);
        request.setAttribute("nomeDesignIReport", designIReport);
        request.setAttribute("nomeUsuario", nomeUsuario);
        request.setAttribute("listaObjetos", listaObjetos);
        request.setAttribute("tipoImplementacao", "OBJETO");
        if (filtros.equals("")) {
            filtros = "nenhum";
        }
        request.setAttribute("filtros", filtros);
        request.setAttribute("dataIni", dataIni);
        request.setAttribute("dataFim", dataFim);
        request.setAttribute("enderecoEmpresa", enderecoEmpresa);
        request.setAttribute("cidadeEmpresa", cidadeEmpresa);
        request.setAttribute("SUBREPORT_DIR", SUBREPORT_DIR);
        request.setAttribute("SUBREPORT_DIR1", SUBREPORT_DIR1);
        process(request);
    }

    @Deprecated
    public void apresentarRelatorioDevolucao(ReciboDevolucaoVO recibo, String design) throws Exception {
        apresentarRelatorioDevolucao(recibo, design, null);
    }

    @Deprecated
    public void apresentarRelatorioDevolucao(ReciboDevolucaoVO recibo, String design, HttpServletRequest request) throws Exception {
        if (request == null) {
            request = (HttpServletRequest) context().getExternalContext().getRequest();
        }
        request.setAttribute("tipoRelatorio", tipoRelatorio);


        request.setAttribute("nomeRelatorio", "reciboDevolucao");

        request.setAttribute("tituloRelatorio", "Recibo de Devolução");
        request.setAttribute("tipoImplementacao", "OBJETO");
        request.setAttribute("nomeDesignIReport", design);
        request.setAttribute("descricao", recibo.getDescricao());
        request.setAttribute("dataDevolucao", recibo.getDataDevolucao());
        request.setAttribute("valorDevolucao", recibo.getValorDevolucao());
        request.setAttribute("contrato", recibo.getContrato().getCodigo());
        request.setAttribute("produto", recibo.getProdutoVO().getCodigo());
        if (recibo.getContrato().getCodigo() > 0) {
            request.setAttribute("pessoa", recibo.getContrato().getPessoa());
            request.setAttribute("nomeEmpresa", recibo.getContrato().getEmpresa().getNome());
            request.setAttribute("enderecoEmpresa", recibo.getContrato().getEmpresa().getEndereco());
        } else {
            request.setAttribute("pessoa", recibo.getProdutoVO().getPessoa());
            if (recibo.getProdutoVO().getEmpresa() != null && recibo.getProdutoVO().getEmpresa().getCodigo() > 0) {
                request.setAttribute("nomeEmpresa", recibo.getProdutoVO().getEmpresa().getNome());
                request.setAttribute("enderecoEmpresa", recibo.getProdutoVO().getEmpresa().getEndereco());
            } else {
                request.setAttribute("nomeEmpresa", getEmpresaLogado().getNome());
                request.setAttribute("enderecoEmpresa", getEmpresaLogado().getEndereco());
            }
        }
        request.setAttribute("valorPorExtenso", recibo.getValorPorExtenso());
        request.setAttribute("responsavelDevolucao", recibo.getResponsavelDevolucao().getColaboradorVO().getPessoa().getNome());
        request.setAttribute("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator);
        List lista = new ArrayList();
        lista.add(recibo);
        request.setAttribute("listaObjetos", lista);


        process(request);
    }

    @Deprecated
    public void apresentarRelatorioObjetos(String nomeRelatorio,
                                           Locale locale,
                                           ResourceBundle bundle,
                                           String nomeEmpresa,
                                           String imagemLogo,
                                           String mensagemRel,
                                           String tipoRelatorio,
                                           String parserBuscaTags,
                                           String designIReport,
                                           String nomeUsuario,
                                           String filtros,
                                           String dataIni,
                                           String dataFim,
                                           String qtdAV,
                                           String qtdCA,
                                           String qtdCD,
                                           String qtdBB,
                                           String qtdChequeAV,
                                           String qtdChequePR,
                                           String qtdOutro,
                                           Double valorAV,
                                           Double valorCA,
                                           Double valorCD,
                                           Double valorBB,
                                           Double valorChequeAV,
                                           Double valorChequePR,
                                           Double valorOutro,
                                           String SUBREPORT_DIR,
                                           String SUBREPORT_DIR1,
                                           String SUBREPORT_DIR2,
                                           List listaObjetos,
                                           String qtdDV,
                                           Double valorDV,
                                           String qtdDR,
                                           Double valorDR,
                                           JRDataSource devolucoes,
                                           JRDataSource totalizadores,
                                           Boolean somenteSintetico,
                                           String moeda) throws Exception {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();

        request.setAttribute(JRParameter.REPORT_LOCALE, locale);
        request.setAttribute(JRParameter.REPORT_RESOURCE_BUNDLE, bundle);
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("nomeRelatorio", nomeRelatorio);
        request.setAttribute("nomeEmpresa", nomeEmpresa);
        request.setAttribute("imagemLogo", imagemLogo);
        request.setAttribute("mensagemRel", mensagemRel);
        request.setAttribute("caminhoParserXML", parserBuscaTags);
        request.setAttribute("nomeDesignIReport", designIReport);
        request.setAttribute("nomeUsuario", nomeUsuario);
        request.setAttribute("listaObjetos", listaObjetos);
        request.setAttribute("tipoImplementacao", "OBJETO");
        if (filtros.equals("")) {
            filtros = "nenhum";
        }
        request.setAttribute("qtdDR", qtdDR);
        request.setAttribute("valorDR", valorDR);
        request.setAttribute("qtdDV", qtdDV);
        request.setAttribute("valorDV", valorDV);
        request.setAttribute("filtros", filtros);
        request.setAttribute("dataIni", dataIni);
        request.setAttribute("dataFim", dataFim);
        request.setAttribute("qtdAV", qtdAV);
        request.setAttribute("qtdCA", qtdCA);
        request.setAttribute("qtdCD", qtdCD);
        request.setAttribute("qtdBB", qtdBB);
        request.setAttribute("qtdChequeAV", qtdChequeAV);
        request.setAttribute("qtdChequePR", qtdChequePR);
        request.setAttribute("qtdOutro", qtdOutro);
        request.setAttribute("valorAV", valorAV);
        request.setAttribute("valorCA", valorCA);
        request.setAttribute("valorCD", valorCD);
        request.setAttribute("valorBB", valorBB);
        request.setAttribute("valorChequeAV", valorChequeAV);
        request.setAttribute("valorChequePR", valorChequePR);
        request.setAttribute("valorOutro", valorOutro);
        request.setAttribute("SUBREPORT_DIR", SUBREPORT_DIR);
        request.setAttribute("SUBREPORT_DIR1", SUBREPORT_DIR1);
        request.setAttribute("SUBREPORT_DIR2", SUBREPORT_DIR2);
        request.setAttribute("devolucoes", devolucoes);
        request.setAttribute("totalizadores", totalizadores);
        request.setAttribute("somenteSintetico", somenteSintetico);

        if (listaObjetos == null) {
            request.setAttribute("apresentarDados", false);
        } else {
            request.setAttribute("apresentarDados", !listaObjetos.isEmpty());
        }
        request.setAttribute("moeda", moeda);

        process(request);
        //context().getExternalContext().dispatch("/VisualizadorRelatorio");
        //context().getCurrentInstance().responseComplete();
    }

    @Deprecated
    public void apresentarRelatorioObjetos(String nomeRelatorio,
                                           String tituloRelatorio,
                                           String nomeEmpresa,
                                           String imagemLogo,
                                           String mensagemRel,
                                           String tipoRelatorio,
                                           String parserBuscaTags,
                                           String designIReport,
                                           String nomeUsuario,
                                           String filtros,
                                           String dataIni,
                                           String dataFim,
                                           String qtdAV,
                                           String qtdCA,
                                           String qtdChequeAV,
                                           String qtdChequePR,
                                           String qtdOutro,
                                           Double valorAV,
                                           Double valorCA,
                                           Double valorChequeAV,
                                           Double valorChequePR,
                                           Double valorOutro,
                                           String SUBREPORT_DIR,
                                           String SUBREPORT_DIR1,
                                           List listaObjetos) throws Exception {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("nomeRelatorio", nomeRelatorio);
        request.setAttribute("nomeEmpresa", nomeEmpresa);
        request.setAttribute("imagemLogo", imagemLogo);
        request.setAttribute("mensagemRel", mensagemRel);
        request.setAttribute("tituloRelatorio", tituloRelatorio);
        request.setAttribute("caminhoParserXML", parserBuscaTags);
        request.setAttribute("nomeDesignIReport", designIReport);
        request.setAttribute("nomeUsuario", nomeUsuario);
        request.setAttribute("listaObjetos", listaObjetos);
        request.setAttribute("tipoImplementacao", "OBJETO");
        if (filtros.equals("")) {
            filtros = "nenhum";
        }
        request.setAttribute("filtros", filtros);
        request.setAttribute("dataIni", dataIni);
        request.setAttribute("dataFim", dataFim);
        request.setAttribute("qtdAV", qtdAV);
        request.setAttribute("qtdCA", qtdCA);
        request.setAttribute("qtdChequeAV", qtdChequeAV);
        request.setAttribute("qtdChequePR", qtdChequePR);
        request.setAttribute("qtdOutro", qtdOutro);
        request.setAttribute("valorAV", valorAV);
        request.setAttribute("valorCA", valorCA);
        request.setAttribute("valorChequeAV", valorChequeAV);
        request.setAttribute("valorChequePR", valorChequePR);
        request.setAttribute("valorOutro", valorOutro);
        request.setAttribute("SUBREPORT_DIR", SUBREPORT_DIR);
        request.setAttribute("SUBREPORT_DIR1", SUBREPORT_DIR1);
        context().getExternalContext().dispatch("/VisualizadorRelatorio");
        context().getCurrentInstance().responseComplete();
    }

    @Deprecated
    public void apresentarRelatorioObjetos(String nomeRelatorio,
                                           String tituloRelatorio,
                                           String nomeEmpresa,
                                           String mensagemRel,
                                           String imagemLogo,
                                           String tipoRelatorio,
                                           String parserBuscaTags,
                                           String designIReport,
                                           String nomeUsuario,
                                           String filtros,
                                           String dataIni,
                                           String dataFim,
                                           String enderecoEmpresa,
                                           String cidadeEmpresa,
                                           String SUBREPORT_DIR,
                                           String SUBREPORT_DIR1,
                                           List listaObjetos,
                                           String totalClientes,
                                           String totalContratos,
                                           String totalValor) throws Exception {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("nomeRelatorio", nomeRelatorio);
        request.setAttribute("nomeEmpresa", nomeEmpresa);
        request.setAttribute("imagemLogo", imagemLogo);
        request.setAttribute("mensagemRel", mensagemRel);
        request.setAttribute("tituloRelatorio", tituloRelatorio);
        request.setAttribute("caminhoParserXML", parserBuscaTags);
        request.setAttribute("nomeDesignIReport", designIReport);
        request.setAttribute("nomeUsuario", nomeUsuario);
        request.setAttribute("listaObjetos", listaObjetos);
        request.setAttribute("tipoImplementacao", "OBJETO");
        if (filtros.equals("")) {
            filtros = "nenhum";
        }
        request.setAttribute("filtros", filtros);
        request.setAttribute("dataIni", dataIni);
        request.setAttribute("dataFim", dataFim);
        request.setAttribute("enderecoEmpresa", enderecoEmpresa);
        request.setAttribute("cidadeEmpresa", cidadeEmpresa);
        request.setAttribute("SUBREPORT_DIR", SUBREPORT_DIR);
        request.setAttribute("SUBREPORT_DIR1", SUBREPORT_DIR1);

        request.setAttribute("totalClientes", totalClientes);
        request.setAttribute("totalContratos", totalContratos);
        request.setAttribute("totalValor", totalValor);

        process(request);
    }

    public void apresentarRelatorioObjetos(Map<String, Object> params) throws Exception {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        apresentarRelatorioObjetosComRequest(params, request);
    }

    public void apresentarRelatorioObjetosComRequest(Map<String, Object> params, HttpServletRequest request) throws Exception {
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("tipoImplementacao", "OBJETO");
        //parâmetros enviados pelo solicitante ao relatório desejado
        if (params != null) {
            Set<String> s = params.keySet();

            for (String paramName : s) {
                Object valor = params.get(paramName);
                request.setAttribute(paramName, valor);
            }
            request.setAttribute("parametrosRelatorio", params);
        }
        process(request);
    }

    private void process(HttpServletRequest request) throws ServletException,
            IOException, Exception {
        VisualizadorRelatorio visualizador = new VisualizadorRelatorio();
        visualizador.processRequest(request, null);
        visualizador = null;
    }

    public void apresentarRelatorioObjetosComRequest(Map<String, Object> params, HttpServletRequest request, Connection con) throws Exception {
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("tipoImplementacao", "OBJETO");
        //parâmetros enviados pelo solicitante ao relatório desejado
        if (params != null) {
            Set<String> s = params.keySet();

            for (String paramName : s) {
                Object valor = params.get(paramName);
                request.setAttribute(paramName, valor);
            }
            request.setAttribute("parametrosRelatorio", params);
        }
        process(request, con);
    }

    private void process(HttpServletRequest request, Connection con) throws ServletException,
            IOException, Exception {
        VisualizadorRelatorio visualizador = new VisualizadorRelatorio();
        visualizador.processRequest(request, null, con);
        visualizador = null;
    }

    public List getListaSelectItemOrdenacoesRelatorio() {
        return listaSelectItemOrdenacoesRelatorio;
    }

    public void setListaSelectItemOrdenacoesRelatorio(List listaSelectItemOrdenacoesRelatorio) {
        this.listaSelectItemOrdenacoesRelatorio = listaSelectItemOrdenacoesRelatorio;
    }

    public Integer getOpcaoOrdenacao() {
        return opcaoOrdenacao;
    }

    public void setOpcaoOrdenacao(Integer opcaoOrdenacao) {
        this.opcaoOrdenacao = opcaoOrdenacao;
    }

    public String getRelatorio() {
        return relatorio;
    }

    public void setRelatorio(String relatorio) {
        this.relatorio = relatorio;
    }

    public String getTipoRelatorio() {
        if (tipoRelatorio == null) {
            tipoRelatorio = "PDF";
        }
        return tipoRelatorio;
    }

    public void setTipoRelatorio(String tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    public List getListaRelatorio() {
        if (listaRelatorio == null) {
            listaRelatorio = new ArrayList();
        }
        return listaRelatorio;
    }

    public void setListaRelatorio(List listaRelatorio) {
        this.listaRelatorio = listaRelatorio;
    }

    public String getNomeArquivoRelatorioGeradoAgora() {
        if (request().getAttribute("nomeArquivoRelatorioGeradoAgora") != null) {
            return request().getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
        } else {
            return "";
        }
    }

    public String getNomeRefRelatorioGeradoAgora() {
        String hRef = getNomeArquivoRelatorioGeradoAgora();
        if (!hRef.isEmpty()) {
            return "location.href=\"" + hRef + "\"";
        } else {
            return "";
        }
    }

    public String getNomeRefPastaRelatorioRelatorioGeradoAgora() {
        String hRef = getNomeArquivoRelatorioGeradoAgora();
        if (!hRef.isEmpty()) {
            return "location.href=\"relatorio/" + hRef + "\"";
        } else {
            return "";
        }
    }

    public String imprimirReciboDevolucao(ReciboDevolucaoVO recibo) {
        return imprimirReciboDevolucao(recibo, null);
    }

    public String imprimirReciboDevolucao(ReciboDevolucaoVO recibo, HttpServletRequest request) {
        try {
            String design = "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro"
                    + File.separator + "ReciboDevolucao.jrxml";
            ReciboDevolucaoVO reciboDevolucaoVO = getFacade().getReciboDevolucao().consultarPorChavePrimaria(recibo.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            reciboDevolucaoVO.setValorDevolverSaldoTransferenciaCredito(recibo.getValorDevolverSaldoTransferenciaCredito());
            apresentarRelatorioDevolucao(reciboDevolucaoVO, design, request);
            setMensagem("");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemID("");
            setMensagemDetalhada(e);
        }

        if (request != null) {
            return request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
        } else {
            return getNomeArquivoRelatorioGeradoAgora();
        }
    }

    public String imprimirComprovanteOperacao(ContratoOperacaoVO operacaoVO, EmpresaVO empresaVO) {
        return imprimirComprovanteOperacao(operacaoVO, empresaVO, null);
    }

    public String imprimirComprovanteOperacao(ContratoOperacaoVO operacaoVO, EmpresaVO empresaVO, HttpServletRequest request) {
        try {
            String design;
            boolean cs = getFacade().getConfiguracaoSistema().usarDigitalComoAssinatura();
            if (cs) {
                design = "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato"
                        + File.separator + "ComprovanteOperacaoContratoNovo1.jrxml";

            } else {
                design = "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato"
                        + File.separator + "ComprovanteOperacaoContratoNovo.jrxml";
            }

            String assinaturaDigitalBiometria = operacaoVO.getAssinaturaDigitalBiometria();
            if (UteisValidacao.emptyString(assinaturaDigitalBiometria)) {
                assinaturaDigitalBiometria = getFacade().getContratoOperacao().consultarPorAssinaturaDigitalBiometria(operacaoVO.getContrato());
            }

            operacaoVO = getFacade().getContratoOperacao().consultarPorChavePrimaria(operacaoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoContrato(operacaoVO.getContrato(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);

            ReciboDevolucaoVO recibo = operacaoVO.getReciboDevolucao();
            ReciboDevolucaoVO reciboDevolucaoVO = getFacade().getReciboDevolucao().consultarPorChavePrimaria(recibo.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            reciboDevolucaoVO.setValorDevolverSaldoTransferenciaCredito(recibo.getValorDevolverSaldoTransferenciaCredito());

            operacaoVO.setNome(clienteVO.getNome_Apresentar());
            operacaoVO.setJustificativaApresentar(operacaoVO.getTipoJustificativa().getDescricao());
            operacaoVO.setReciboDevolucao(reciboDevolucaoVO);
            operacaoVO.setAssinaturaDigitalBiometria(assinaturaDigitalBiometria);

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("tipoRelatorio", tipoRelatorio);
            params.put("nomeRelatorio", "comprovanteOperacaoContrato");
            params.put("tituloRelatorio", "Comprovante de Operação");
            params.put("tipoImplementacao", "OBJETO");
            params.put("nomeDesignIReport", design);
            params.put("empresaVO", empresaVO);
            params.put("nomeEmpresa", empresaVO.getNome());
            params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator);
            params.put("SUBREPORT_DIR2", "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator);

            List lista = new ArrayList();
            lista.add(operacaoVO);
            lista.add(operacaoVO);
            params.put("listaObjetos", lista);

            if (request != null) {
                apresentarRelatorioObjetosComRequest(params, request);
            } else {
                apresentarRelatorioObjetos(params);
            }

            setMensagem("");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemID("");
            setMensagemDetalhada(e);
        }
        if (request != null) {
            return request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
        } else {
            return getNomeArquivoRelatorioGeradoAgora();
        }
    }

    public String imprimirComprovanteResgateBrinde(HistoricoPontosVO resgateBrindeVO, EmpresaVO empresaVO) {
        try {
            String design;
            boolean reciboTermico = empresaVO.isReciboParaImpressoraTermica();
            if (reciboTermico) {
                design = "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato"
                        + File.separator + "LayoutComprovanteResgateBrinde_Termico.jrxml";
            } else {
                design = "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato"
                        + File.separator + "LayoutComprovanteResgateBrinde.jrxml";
            }

            HistoricoPontosVO historicoResgate = getFacade().getHistoricoPontos().consultarPorChavePrimaria(resgateBrindeVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            Map<String, Object> params = new HashMap<>();
            params.put("tipoRelatorio", tipoRelatorio);
            params.put("nomeRelatorio", "resgateBrinde");
            params.put("tituloRelatorio", "Resgate de Brinde");
            params.put("tipoImplementacao", "OBJETO");
            params.put("nomeDesignIReport", design);
            params.put("empresaVO", empresaVO);
            params.put("nomeEmpresa", empresaVO.getNome());
            params.put("usuario", getUsuarioLogado().getNomeAbreviado());
            params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator);
            params.put("SUBREPORT_DIR2", "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator);

            List<HistoricoPontosVO> lista = new ArrayList<>();
            lista.add(historicoResgate);
            params.put("listaObjetos", lista);

            apresentarRelatorioObjetos(params);
            setMensagem("msg_operacao_sucesso");
            setMensagemDetalhada("msg_operacao_sucesso", "");
        } catch (Exception e) {
            setMensagemID("");
            setMensagemDetalhada(e);
        }
        return getNomeArquivoRelatorioGeradoAgora();
    }

    public Map<String, List<String>> getMapaCaminhoRelatorio() {
        return mapaCaminhoRelatorio;
    }

    public void setMapaCaminhoRelatorio(Map<String, List<String>> mapaCaminhoRelatorio) {
        this.mapaCaminhoRelatorio = mapaCaminhoRelatorio;
    }

    public String imprimirRelatorioNotasManuais(Map<String, Object> params) {
        try {
            apresentarRelatorioObjetos(params);
            setMensagem("");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemID("");
            setMensagemDetalhada(e);
        }
        return getNomeArquivoRelatorioGeradoAgora();
    }

    public boolean validarPermissaoEmpresas() {
        try {
            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    setMensagemDetalhada("");
                    setSucesso(true);
                    return true;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "LancarVisualizarLancamentosEmpresas", "9.23 - Lançar/Visualizar Lançamentos Financeiros para todas as Empresas");
                    return true;
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }


    public String imprimirNotaFiscal(HttpServletRequest request, Map<String, Object> params) {
        try {
            apresentarRelatorioObjetosComRequest(params, request);
            if (request.getAttribute("nomeArquivoRelatorioGeradoAgora") != null) {
                return request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
            } else {
                return "";
            }
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    public String imprimirRetornaNomeArquivo(HttpServletRequest request, Map<String, Object> params) {
        try {
            apresentarRelatorioObjetosComRequest(params, request);
            if (request.getAttribute("nomeArquivoRelatorioGeradoAgora") != null) {
                return request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
            } else {
                return "";
            }
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }
}

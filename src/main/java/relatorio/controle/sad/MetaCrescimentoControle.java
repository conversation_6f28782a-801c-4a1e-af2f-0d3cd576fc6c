package relatorio.controle.sad;

import controle.arquitetura.SuperControle;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import javax.faces.model.SelectItem;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.Contrato;
import relatorio.negocio.comuns.sad.MetaCrescimentoVO;
import relatorio.negocio.comuns.sad.RotatividadeSinteticoDWVO;

/**
 *
 * <AUTHOR>
 */
public class MetaCrescimentoControle extends SuperControle {

    private MetaCrescimentoVO meta;
    private Date dataMeta;
    private EmpresaVO empresaVO;
    private List<EmpresaVO> listaSelectItemEmpresa;

    public MetaCrescimentoControle() {
        novo();
    }

    public void novo() {
        meta = new MetaCrescimentoVO();
        dataMeta = negocio.comuns.utilitarias.Calendario.hoje();
        empresaVO = new EmpresaVO();
        listaSelectItemEmpresa = new ArrayList<EmpresaVO>();
        montarListaSelectItemEmpresa();
        inicializarEmpresaLogado();
        consultarMetaPanel();
        
    }

    public void inicializarEmpresaLogado() {
        try {
            if (getEmpresaLogado() != null && getEmpresaLogado().getCodigo().intValue() != 0) {
                setEmpresaVO(getEmpresaLogado());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarListaSelectItemEmpresa() {
        try {
            List objs = new ArrayList();
            objs.add(new SelectItem(0, ""));
            List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
            listaSelectItemEmpresa = objs;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarMetaPanel() {
        try {
            if (getEmpresaVO().getCodigo().intValue() == 0) {
                throw new Exception("O campo empresa deve ser informado");
            }
            // dados para as consultas
            int ano = Uteis.getAnoData(dataMeta);
            int mes = Uteis.getMesData(dataMeta);
            Date inicio = Uteis.obterPrimeiroDiaMes(dataMeta);
            Date fim = Uteis.obterUltimoDiaMesUltimaHora(dataMeta);
            Date dataFimMesPassado = Uteis.obterDataAnterior(inicio, 1);
            Date dataInicioMesPassado = Uteis.obterPrimeiroDiaMes(dataFimMesPassado);
            List<ColaboradorVO> lista = new ArrayList<ColaboradorVO>();
            lista.add(new ColaboradorVO());

            meta = getFacade().getMetaCrescimento().consultarPorAnoMes(getEmpresaVO(), ano, mes);

            int matriculados = getFacade().getContrato().contar(
                    String.format(Contrato.sqlMatriculadosContar,
                    new Object[]{
                            (" and c.empresa = " + getEmpresaVO().getCodigo()),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim)
                    }));

            int rematriculados = getFacade().getContrato().contar(
                    String.format(Contrato.sqlRematriculadosContar,
                    new Object[]{
                            (" and c.empresa = " + getEmpresaVO().getCodigo()),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim)
                    }));

            int retornotrancados = getFacade().getContrato().contar(
                    String.format(Contrato.sqlRetornoTrancamentosContar,
                    new Object[]{
                            (" and c.empresa = " + getEmpresaVO().getCodigo()),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim)
                    }));

            int desistentes = getFacade().getContrato().contar(
                    String.format(Contrato.sqlDesistentesContar,
                    new Object[]{
                            (" and c.empresa = " + getEmpresaVO().getCodigo()),
                        Uteis.getDataJDBC(fim),
                        Uteis.getDataJDBC(fim),
                        Uteis.getDataJDBC(fim),
                        Uteis.getDataJDBC(fim)
                    }));
            int vencidos = getFacade().getContrato().contar(
                    String.format(Contrato.sqlVencidosInicioMesContar,
                    new Object[]{
                            (" and c.empresa = " + getEmpresaVO().getCodigo()),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim)
                    }));

            int vendas = matriculados + rematriculados;

            meta.setTotalVisitas(getFacade().getQuestionarioCliente().consultaQuantidadeQuestionarioPorDataEmpresa(inicio, fim, getEmpresaVO().getCodigo(), false));
            meta.setTotalVendas(vendas);
            meta.setTotalRenovacoes(getFacade().getCliente().consultarClientePorContratoPrevistoRenovado(inicio, fim, getEmpresaVO().getCodigo(), lista).size());
            meta.setTotalRenovacoesAtrasadas(getFacade().getCliente().consultarClientePorContratoRenovadoAtrasado(getEmpresaVO().getCodigo(), inicio, fim, lista).size());
            //meta.setTotalCancelados(getFacade().getRotatividadeAnaliticoDW().consultarQuantidadeRotatividadePorSituacaoMesAnoEmpresa(inicio, fim, "CA", mes, ano, getEmpresaVO().getCodigo().intValue()));

            meta.setTotalCancelados(
                    getFacade().getContrato().contar(
                    String.format(Contrato.sqlCanceladosContar,
                    new Object[]{
                            (" and c.empresa = " + getEmpresaVO().getCodigo()),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim)
                    })));

            meta.setTotalTrancados(getFacade().getContrato().contar(
                    String.format(Contrato.sqlRetornoTrancamentosContar,
                    new Object[]{
                            (" and c.empresa = " + getEmpresaVO().getCodigo()),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim),
                        Uteis.getDataJDBC(inicio),
                        Uteis.getDataJDBC(fim)
                    })));

            // não podem ser abertas metas retroativas
            if (meta.getCodigo() == 0) {
                abrirMeta(ano, mes, inicio, fim, lista);
            }

            int saldo = matriculados + rematriculados + retornotrancados - desistentes - meta.getTotalCancelados() - meta.getTotalTrancados();
            int ativos = getFacade().getContrato().contar(
                    String.format(Contrato.sqlAtivosVencidosContar,
                    new Object[]{
                            (" and c.empresa = " + getEmpresaVO().getCodigo()),
                        Uteis.getDataJDBC(dataFimMesPassado),
                        Uteis.getDataJDBC(dataFimMesPassado),
                        Uteis.getDataJDBC(dataFimMesPassado),
                            Uteis.getDataJDBC(dataFimMesPassado),
                            Uteis.getDataJDBC(dataFimMesPassado),
                        Uteis.getDataJDBC(dataFimMesPassado),
                        Uteis.getDataJDBC(dataFimMesPassado)
                    }));
            meta.setTotalFinal(ativos + saldo - vencidos);
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private int pegarAtivosRotatividade() throws Exception {
        int mes = Uteis.getMesData(dataMeta) - 1;
        int ano = Uteis.getAnoData(dataMeta);
        if (mes == 0) {
            mes = 12;
            ano = ano - 1;
        }
        ResultSet lista = getFacade().getRotatividadeSinteticoDW().consultarRotatividadePorSituacaoMesAnoEmpresaResultSet(mes, ano, getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (lista.next()) {
            return (lista.getInt("qtdVigentesMesAnterior"));
        } else {
            return 0;
        }
    }

    /**
     * abre uma nova meta se necessário
     */
    private void abrirMeta(int ano, int mes, Date inicio, Date fim, List<ColaboradorVO> lista) throws Exception {
        // fecha a meta do mes passado
        fecharMeta();

        Date dataFimMesPassado = Uteis.obterDataAnterior(inicio, 1);
        Date dataInicioMesPassado = Uteis.obterPrimeiroDiaMes(dataFimMesPassado);

        // pega a meta do mesmo mês, mas do ano passado.
        MetaCrescimentoVO metaAnoPassado = getFacade().getMetaCrescimento().consultarPorAnoMes(getEmpresaVO(), ano - 1, mes);
        // se meta encontrada
        if (metaAnoPassado.getCodigo() != 0) {
            meta.setICVAnterior(metaAnoPassado.getICVRealizado());
            meta.setICanceladosAnterior(metaAnoPassado.getICanceladosRealizado());
            meta.setIRAnterior(metaAnoPassado.getIRRealizado());
            meta.setITrancadosAnterior(metaAnoPassado.getITrancadosRealizado());
        }
        // consulta rotatividade para pegar o total inicial do mes
        //RotatividadeSinteticoDWVO rotatividade = getFacade().getRotatividadeSinteticoDW().consultarRotatividadePorMesAnoEmpresa(mes - 1, ano, getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        RotatividadeSinteticoDWVO rotatividade = new RotatividadeSinteticoDWVO();


        rotatividade.setQtdVigentesMesAnterior(getFacade().getContrato().contar(
                String.format(Contrato.sqlAtivosVencidosContar,
                new Object[]{
                        (" and c.empresa = " + getEmpresaVO().getCodigo()),
                    Uteis.getDataJDBC(dataFimMesPassado),
                    Uteis.getDataJDBC(dataFimMesPassado),
                    Uteis.getDataJDBC(dataFimMesPassado),
                        Uteis.getDataJDBC(dataFimMesPassado),
                        Uteis.getDataJDBC(dataFimMesPassado),
                    Uteis.getDataJDBC(dataFimMesPassado),
                    Uteis.getDataJDBC(dataFimMesPassado)
                })));

        rotatividade.setQtdVencido(getFacade().getContrato().contar(
                String.format(Contrato.sqlVencidosInicioMesContar,
                new Object[]{
                        (" and c.empresa = " + getEmpresaVO().getCodigo()),
                    Uteis.getDataJDBC(dataInicioMesPassado),
                    Uteis.getDataJDBC(dataFimMesPassado),
                    Uteis.getDataJDBC(dataInicioMesPassado),
                    Uteis.getDataJDBC(dataFimMesPassado),
                    Uteis.getDataJDBC(dataInicioMesPassado),
                    Uteis.getDataJDBC(dataFimMesPassado)
                })));

        rotatividade.setQtdVigentesMesAnterior(rotatividade.getQtdVigentesMesAnterior()
                + rotatividade.getQtdVencido());

        rotatividade.setQtdTotal(rotatividade.getQtdVigentesMesAnterior());

        rotatividade.setQtdeVigenteMesAtual(
                rotatividade.getQtdVigentesMesAnterior()
                - rotatividade.getQtdVencido());


        // prepara os dados da meta
        meta.setEmpresa(empresaVO);
        meta.setAno(ano);
        meta.setMes(mes);
        meta.setTotalInicial(rotatividade.getQtdVigentesMesAnterior() - rotatividade.getQtdVencido());
        meta.setQtdePrevistosRenovar(getFacade().getContrato().contarQtdeContratoPrevistoPeriodo(getEmpresaVO().getCodigo(), inicio, fim, lista, false, null, null));
        /*if (rotatividade.getCodigo().intValue() == 0) {
            throw new Exception("Resumo Sintético do Mês Ainda Não Foi Realizado.");
        }*/
        salvar(meta);
    }

    private void fecharMeta() throws Exception {
        // pega a data do mes anterior
        Date aux = Uteis.obterDataAnterior(Uteis.obterPrimeiroDiaMes(dataMeta), 1);
        int anoMeta = Uteis.getAnoData(aux);
        int mesMeta = Uteis.getMesData(aux);
        MetaCrescimentoVO metaFechamento = getFacade().getMetaCrescimento().consultarPorAnoMes(getEmpresaVO(), anoMeta, mesMeta);
        // se nao existe meta no mes anterior ignora o fechamento.
        if (metaFechamento.getCodigo() == 0) {
            return;
        }

        // pega a meta do mesmo mês, mas do ano passado.
        MetaCrescimentoVO metaAnoPassado = getFacade().getMetaCrescimento().consultarPorAnoMes(getEmpresaVO(), anoMeta - 1, mesMeta);
        // se meta encontrada
        if (metaAnoPassado.getCodigo() != 0) {
            metaFechamento.setICVAnterior(metaAnoPassado.getICVRealizado());
            metaFechamento.setICanceladosAnterior(metaAnoPassado.getICanceladosRealizado());
            metaFechamento.setIRAnterior(metaAnoPassado.getIRRealizado());
            metaFechamento.setITrancadosAnterior(metaAnoPassado.getITrancadosRealizado());
        }

        // datas para consulta
        Date inicio = Uteis.obterPrimeiroDiaMes(aux);
        Date fim = Uteis.obterUltimoDiaMesUltimaHora(aux);
        Date dataFimMesPassado = Uteis.obterDataAnterior(inicio, 1);
        Date dataInicioMesPassado = Uteis.obterPrimeiroDiaMes(dataFimMesPassado);

        int vendas = getFacade().getContrato().consultaQuantidadeRematriculaPorDataEmpresa(inicio, fim, getEmpresaVO().getCodigo(), false);
        vendas += getFacade().getContrato().consultaQuantidadeMatriculaPorDataEmpresa(inicio, fim, getEmpresaVO().getCodigo(), false);

        // lista para consulta
        List<ColaboradorVO> lista = new ArrayList<ColaboradorVO>();
        lista.add(new ColaboradorVO());

        // consulta rotatividade para pegar o total inicial do mes
        RotatividadeSinteticoDWVO rotatividade = getFacade().getRotatividadeSinteticoDW().consultarRotatividadePorMesAnoEmpresa(mesMeta - 1, anoMeta, getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        // prepara os dados da meta
        metaFechamento.setEmpresa(empresaVO);
        metaFechamento.setAno(anoMeta);
        metaFechamento.setMes(mesMeta);
        metaFechamento.setTotalInicial(rotatividade.getQtdVigentesMesAnterior() - rotatividade.getQtdVencido());
        metaFechamento.setQtdePrevistosRenovar(getFacade().getContrato().contarQtdeContratoPrevistoPeriodo(getEmpresaVO().getCodigo(), inicio, fim, lista, false,null, null));
        metaFechamento.setTotalRenovacoesAtrasadas(getFacade().getCliente().consultarClientePorContratoRenovadoAtrasado(getEmpresaVO().getCodigo(), inicio, fim, lista).size());
        metaFechamento.setTotalVisitas(getFacade().getQuestionarioCliente().consultaQuantidadeQuestionarioPorDataEmpresa(inicio, fim, getEmpresaVO().getCodigo(), false));
        metaFechamento.setTotalVendas(vendas);
        metaFechamento.setTotalRenovacoes(getFacade().getCliente().consultarClientePorContratoPrevistoRenovado(inicio, fim, getEmpresaVO().getCodigo(), lista).size());
        metaFechamento.setTotalFinal(metaFechamento.getTotalInicial() - metaFechamento.getQtdePrevistosRenovar() + metaFechamento.getTotalRenovacoesAtrasadas()
                + metaFechamento.getTotalVendas() + metaFechamento.getTotalRenovacoes());
        metaFechamento.setTotalCancelados(getFacade().getRotatividadeAnaliticoDW().consultarQuantidadeRotatividadePorSituacaoMesAnoEmpresa(inicio, fim, "CA", mesMeta, anoMeta, getEmpresaVO().getCodigo().intValue()));
        metaFechamento.setTotalTrancados(getFacade().getRotatividadeAnaliticoDW().consultarQuantidadeRotatividadePorSituacaoMesAnoEmpresa(inicio, fim, "TR", mesMeta, anoMeta, getEmpresaVO().getCodigo().intValue()));
        salvar(metaFechamento);
    }

    public void salvar() throws Exception {
        salvar(meta);
    }

    public void salvar(MetaCrescimentoVO meta) throws Exception {
        if (meta.getCodigo() == 0) {
            getFacade().getMetaCrescimento().incluir(meta);
        } else {
            getFacade().getMetaCrescimento().alterar(meta);
        }
    }

    public boolean getCheckMFalta() {
        return meta.getTotalVendasFaltam() == 0;
    }

    public boolean getCheckRFalta() {
        return meta.getTotalRenovacoesFaltam() == 0;
    }

    public MetaCrescimentoVO getMeta() {
        return meta;
    }

    public void setMeta(MetaCrescimentoVO meta) {
        this.meta = meta;
    }

    public Date getDataMeta() {
        return dataMeta;
    }

    public void setDataMeta(Date dataMeta) {
        this.dataMeta = dataMeta;
    }

    public List<EmpresaVO> getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List<EmpresaVO> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }
}

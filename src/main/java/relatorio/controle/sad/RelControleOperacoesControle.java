package relatorio.controle.sad;

import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.security.LogControle;
import negocio.comuns.arquitetura.LogAgrupadoChavePrimaria;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoObservacaoOperacaoEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;
import relatorio.controle.basico.BIControle;
import relatorio.negocio.comuns.basico.ControleOperacoesRelVO;
import relatorio.negocio.comuns.basico.PendenciaResumoPessoaRelVO;

import javax.faces.event.ActionEvent;
import java.sql.ResultSet;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Classe de Controle do Relatório de Controle de Operações de Contrato
 *
 * <AUTHOR>
 */
public class RelControleOperacoesControle extends BIControle {

    private Date inicio;
    private ControleOperacoesRelVO controleOperacoesRelVO;
    private List<ControleOperacoesRelVO> listaControleOperacoesRelVOs;
    private List<DescontoTO> descontos;
    private Boolean consultarPorTodosColaboradores;
    private Boolean mostrarPaginacao;
    private boolean mostrarGrupos = false;
    private Boolean marcarUsuario;
    private boolean exibirTodos = false;
    private String nomeTela;

    public RelControleOperacoesControle() throws Exception {
        novo();
    }

    public void filtrarPorOperacaoPorEmpresaTela() {
        gravarHistoricoAcessoBI(BIEnum.CONTROLE_OPERACOES);
        filtrarPorOperacaoPorEmpresa(true);
    }

    /**
     * Método que atualiza o relatório usado pelo botão de atualizar
     * author carla
     * 05/07/2011
     */
    public void filtrarPorOperacaoPorEmpresa() {
        filtrarPorOperacaoPorEmpresa(false);
    }
    public void filtrarPorOperacaoPorEmpresa(boolean atualizar) {
        try {

            consultarItensControleOperacoesContrato(atualizar);

            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    public List getListaSelectItemEmpresa() {
        return (List)JSFUtilities.getManagedBean("BIControle.listaSelectItemEmpresa");
    }
    public EmpresaVO getEmpresaFiltroSessao(){
        return (EmpresaVO)JSFUtilities.getManagedBean("BIControle.empresaFiltro");
    }
    public String getAbriRichModalContratosAlterados() {
        if (!getListaControleOperacoesRelVOs().isEmpty()) {
            return "abrirPopup('faces/contratosAlteracaoDataManual.jsp', '', 980, 650);";
        }
        return "";
    }

    public void inicializarDados() throws Exception {
        setListaControleOperacoesRelVOs(new ArrayList<ControleOperacoesRelVO>());
        setControleOperacoesRelVO(new ControleOperacoesRelVO());
        setConsultarPorTodosColaboradores(false);
        this.setInicio(Uteis.obterPrimeiroDiaMes(this.getDataBaseFiltroBI()));
    }

    public void inicializarUsuarioLogado() throws Exception {
        if (getUsuarioLogado() != null && getUsuarioLogado().getCodigo() != 0) {
            getControleOperacoesRelVO().setUsuarioVO(new UsuarioVO());
            getControleOperacoesRelVO().getUsuarioVO().setCodigo(getUsuarioLogado().getCodigo());
            getControleOperacoesRelVO().getUsuarioVO().setNome(getUsuarioLogado().getNome());
            getControleOperacoesRelVO().getUsuarioVO().setAdministrador(getUsuarioLogado().getAdministrador());
        }
    }

    public final void novo() throws Exception {
        inicializarDados();
    }

    public void selecionarClientesInativosComPeriodoAcesso() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdClientesInativosComPeriodoAcesso() && obj.getQtdClientesInativosComPeriodoAcesso() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<ClienteVO> lista = getFacade().getCliente().consultarClientesInativosComPeriodoAcesso(
                            getEmpresaFiltroSessao().getCodigo(), getInicio(), getDataBaseFiltroBI(), getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()));
                    for (ClienteVO cli : lista) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setClienteVO(cli);
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Consulta clientes com operações de contrato retroativas
     */
    public void selecionarOperacoesContratoRetroativas() {
        try {

            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdOperacoesContratoRetroativas() && obj.getQtdOperacoesContratoRetroativas() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(getFacade().getContratoOperacao().consultarOperacoesContratoRetroativas(
                            getEmpresaFiltroSessao().getCodigo(), getInicio(), getDataBaseFiltroBI(), getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name())));

                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarLogsExclusaoVisitantes() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdExclusaoVisitantes() && obj.getQtdExclusaoVisitantes() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<LogVO> listaLogs = getFacade().getLog().consultarPorNomeEntidadePorDataAlteracaoPorOperacao(
                            "CLIENTE - VISITANTE", Uteis.getDataJDBC(getInicio()), Uteis.getDataJDBC(getDataBaseFiltroBI()),
                            "EXCLUSÃO", getEmpresaFiltroSessao().getNome(), true, getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (LogVO logs : listaLogs) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setLogVO(logs);
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarLogsExclusaoClientesVinculadoTreinoWeb() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdClientesExcluidosTreinoWeb() && obj.getQtdClientesExcluidosTreinoWeb() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<LogVO> listaLogs = getFacade().getLog().consultarPorNomeEntidadePorDataAlteracaoPorOperacao(
                            "CLIENTESINTETICO", Uteis.getDataJDBC(getInicio()), Uteis.getDataJDBC(getDataBaseFiltroBI()),
                            "EXCLUSÃO", "", true, Collections.<ColaboradorVO>emptyList(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (LogVO logs : listaLogs) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setLogVO(logs);
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }


    public void selecionarLogsExclusaoClientes() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdClientesExcluidos() && obj.getQtdClientesExcluidos() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<LogVO> listaLogs = getFacade().getLog().consultarPorNomeEntidadePorDataAlteracaoPorOperacao(
                            "CLIENTE", Uteis.getDataJDBC(getInicio()), Uteis.getDataJDBC(getDataBaseFiltroBI()),
                            "EXCLUSÃO", getNomeEmpresaLogada(), true, Collections.<ColaboradorVO>emptyList(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (LogVO logs : listaLogs) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setLogVO(logs);
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarLogsAlteracaoConsultorContrato() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdAlteracaoConsultorContrato() && obj.getQtdAlteracaoConsultorContrato() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<LogVO> listaLogs = getFacade().getLog().consultarPorNomeEntidadePorDataAlteracaoPorOperacao(
                            "CONTRATO", Uteis.getDataJDBC(getInicio()), Uteis.getDataJDBC(getDataBaseFiltroBI()),
                            "ALTERAÇÃO DO CONTRATO%", getEmpresaFiltroSessao().getNome(), true, getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (LogVO logs : listaLogs) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setLogVO(logs);
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarLogsEstornoContratoAdmin() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdEstornoContratoAdmin() && obj.getQtdEstornoContratoAdmin() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<EstornoObservacaoVO> listaEstornos = getFacade().getEstornoObservacao().consultarPorNomeEntidadePorDataAlteracaoPorOperacao(Uteis.getDataJDBC(getInicio()), Uteis.getDataJDBC(getDataBaseFiltroBI()),
                            getEmpresaFiltroSessao().getCodigo(), true, false, getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (EstornoObservacaoVO estornoObservacaoVO : listaEstornos) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setEstornoObservacaoVO(estornoObservacaoVO);
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarLogsEstornoContratoRecorrencia() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdEstornoContratoRecorrencia() && obj.getQtdEstornoContratoRecorrencia() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<EstornoObservacaoVO> listaEstornos = getFacade().getEstornoObservacao().consultarPorNomeEntidadePorDataAlteracaoPorOperacao(Uteis.getDataJDBC(getInicio()), Uteis.getDataJDBC(getDataBaseFiltroBI()),
                            getEmpresaFiltroSessao().getCodigo(), false, true, getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (EstornoObservacaoVO estornoObservacaoVO : listaEstornos) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setEstornoObservacaoVO(estornoObservacaoVO);
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarLogsEstornoContratoUsuarioComum() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdEstornoContratoUsuarioComum() && obj.getQtdEstornoContratoUsuarioComum() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<EstornoObservacaoVO> listaEstornos = getFacade().getEstornoObservacao().consultarPorNomeEntidadePorDataAlteracaoPorOperacao(Uteis.getDataJDBC(getInicio()), Uteis.getDataJDBC(getDataBaseFiltroBI()),
                            getEmpresaFiltroSessao().getCodigo(), false, false, getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (EstornoObservacaoVO estornoObservacaoVO : listaEstornos) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setEstornoObservacaoVO(estornoObservacaoVO);
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarLogsEstornoRecibo() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdEstornoRecibo() && obj.getQtdEstornoRecibo() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<LogVO> listaLogs = getFacade().getLog().consultarPorNomeEntidadeOperacaoPorDataAlteracao("RECIBOPAGAMENTO", "ESTORNO - RECIBO PAGAMENTO", getEmpresaFiltroSessao().getNome(),Uteis.getDataJDBC(getInicio()), Uteis.getDataJDBC(getDataBaseFiltroBI()),
                            getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (LogVO logs : listaLogs) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setLogVO(logs);
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarAlteracoesDataBaseContrato() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdAlteracoesDataBaseContrato() && obj.getQtdAlteracoesDataBaseContrato() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<ContratoVO> listaContratos = getFacade().getContrato().consultarPorIntervaloDatasEmpresaDataAlteracaoManual(
                            getInicio(), getDataBaseFiltroBI(), getEmpresaFiltroSessao().getCodigo(), getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), Uteis.NIVELMONTARDADOS_DADOSRESPONSAVELALTERACAO);
                    for (ContratoVO contrato : listaContratos) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setContratoVO(contrato);
                        resumo.setClienteVO(contrato.getCliente());
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarAlteracoesDataBasePagamento() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdAlteracoesDataBasePagamento() && obj.getQtdAlteracoesDataBasePagamento() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<MovPagamentoVO> listaMovPagamento = getFacade().getMovPagamento().consultarPorIntervaloDatasEmpresaDataAlteracaoManual(
                            getInicio(), getDataBaseFiltroBI(), getEmpresaFiltroSessao().getCodigo(), getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), Uteis.NIVELMONTARDADOS_DADOSRESPONSAVELALTERACAO);
                    for (MovPagamentoVO pag : listaMovPagamento) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setMovPagamentoVO(pag);
                        resumo.setClienteVO(getFacade().getCliente().consultarPorCodigoPessoa(pag.getPessoa().getCodigo(), getEmpresaFiltroSessao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarAlteracoesRecibo() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdAlteracoesRecibo() && obj.getQtdAlteracoesRecibo() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<LogAgrupadoChavePrimaria> listaLogAgrupado = getFacade().getLog().consultarPorNomeCodigoEntidadeAgrupadoPorChavePrimaria(
                            "MOVPAGAMENTO", 0, inicio, getDataBaseFiltroBI(), 0, getEmpresaFiltroSessao().getCodigo(), getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
                    for (LogAgrupadoChavePrimaria logAgrupado : listaLogAgrupado) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setLogAgrupadoChavePrimaria(logAgrupado);
                        resumo.setClienteVO(getFacade().getCliente().consultarPorCodigoPessoa(logAgrupado.getPessoa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public ControleOperacoesRelVO obterRenegociacaoParcelaRetroativa(){

        ControleOperacoesRelVO obj = new ControleOperacoesRelVO();
        try{
            String empresaNome = getEmpresaFiltroSessao().getCodigo() == 0 ? "" :
                    getEmpresaFiltroSessao().getNome();
        List<LogVO> listaLogs = getFacade().getLog()
                .consultarPorNomeEntidadeOperacaoPorDataAlteracao(
                        "PARCELA",
                        "RENEGOCIAÇÃO - PARCELA",
                        empresaNome,
                        Uteis.getDataJDBC(getInicio()),
                        Uteis.getDataJDBC(getDataBaseFiltroBI()),
                        getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        obj.setQtdRenegociacaoParcelaRetroativa(listaLogs.size());
        obj.setListaLogRenegociacaoParcelasRetroativas(listaLogs);

        }catch (Exception e){
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

        return obj;
    }

    public void selecionarRenegociacaoParcelaRetroativa(){
            setControleOperacoesRelVO(obterRenegociacaoParcelaRetroativa());
    }

    public void selecionarContratosTranferidosCancelados() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdContratosTransferidosCancelados() && obj.getQtdContratosTransferidosCancelados() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    ResultSet rs = getFacade().getJustificativaOperacao().consultarJustificativaContratoCanceladoDeOutraUnidade(getEmpresaFiltroSessao().getCodigo());
                    while (rs.next()) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setConteudoMensagem(rs.getString("mensagem"));
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarCancelamentoParcelas() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdParcelasCanceladas() && obj.getQtdParcelasCanceladas() != 0) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<ObservacaoOperacaoVO> lista = getFacade().getObservacaoOperacao().consultarPorNomeEntidadePorDataAlteracaoPorOperacao(Uteis.getDataJDBC(getInicio()), Uteis.getDataJDBC(getDataBaseFiltroBI()),
                            getEmpresaFiltroSessao().getCodigo(),  false, TipoObservacaoOperacaoEnum.PARCELA_CANCELADA, getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (ObservacaoOperacaoVO operacao : lista) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        MovParcelaVO movParcelaVO = getFacade().getMovParcela().consultarPorChavePrimaria(operacao.getMovParcela().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        resumo.setPessoaVO(movParcelaVO.getPessoa());
                        resumo.setObservacaoOperacaoVO(operacao);
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private FiltroDTO getFiltroDTO() throws Exception{
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.AULA_EXPERIMENTAL.name());
        JSONObject filtros = new JSONObject();
        filtros.put("empresaNome", getEmpresaFiltroSessao().getCodigo() == 0 ? "" :
                getEmpresaFiltroSessao().getNome());
        filtros.put("inicio", inicio.getTime());
        filtros.put("fim", Calendario.getDataComHoraZerada(getDataBaseFiltroBI()).getTime());
        List<Integer> colaboradores = new ArrayList<>();
        List<ColaboradorVO> lista = getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name());
        if (lista != null) {
            for (ColaboradorVO co : lista) {
                if(co.getColaboradorEscolhidoOperacoes()){
                    colaboradores.add(co.getCodigo());
                }
            }
        }
        filtros.put("colaboradores", colaboradores);
        if(!colaboradores.isEmpty()){
            filtros.put("colaboradoresNomes", getFacade().getPessoa().nomesColaboradores(colaboradores));
        }


        filtros.put("empresa", getEmpresaFiltroSessao().getCodigo());
        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    public void consultarItensControleOperacoesContrato() throws Exception {
        consultarItensControleOperacoesContrato(false);
    }

    public void consultarItensControleOperacoesContrato(boolean atualizar) throws Exception {
        this.setInicio(Uteis.obterPrimeiroDiaMesPrimeiraHora(this.getDataBaseFiltroBI()));
        setListaControleOperacoesRelVOs(new ArrayList<>());

        FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(), BIEnum.CONTROLE_OPERACOES, getFiltroDTO(), atualizar);
        ControleOperacoesRelVO dto = new ControleOperacoesRelVO(new JSONObject(filtroDTO.getJsonDados()));

        //qtde de clientes inativos com periodo de acesso
        ControleOperacoesRelVO obj = new ControleOperacoesRelVO();
        obj.setQtdClientesInativosComPeriodoAcesso(dto.getQtdClientesInativosComPeriodoAcesso());
        obj.setApresentarQtdClientesInativosComPeriodoAcesso(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de operações de contrato retroativas
        obj = new ControleOperacoesRelVO();
        obj.setQtdOperacoesContratoRetroativas(dto.getQtdOperacoesContratoRetroativas());
        obj.setApresentarQtdOperacoesContratoRetroativas(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de exclusao de visitantes
        obj = new ControleOperacoesRelVO();
        String empresaNome = getEmpresaFiltroSessao().getCodigo() == 0 ? "" :
            getEmpresaFiltroSessao().getNome();
        // 0 para consultar de todas as empresas
        int qtdExclusao = dto.getQtdExclusaoVisitantes();
        obj.setQtdExclusaoVisitantes(qtdExclusao);
        obj.setApresentarQtdExclusaoVisitantes(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de alterações de consultor de contratos
        obj = new ControleOperacoesRelVO();
        int qtdAlteracaoConsultorContratos = dto.getQtdAlteracaoConsultorContrato();
        obj.setQtdAlteracaoConsultorContrato(qtdAlteracaoConsultorContratos);
        obj.setApresentarQtdAlteracaoConsultorContrato(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de estorno de contrato de admin
        obj = new ControleOperacoesRelVO();

        //lista temporaria para buscar somente por administrador independente de colaborador escolhido
        int qtdEstornoAdmin = dto.getQtdEstornoContratoAdmin();
        obj.setQtdEstornoContratoAdmin(qtdEstornoAdmin);
        obj.setApresentarQtdEstornoContratoAdmin(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de estorno de contrato de usuário RECORRÊNCIA
        obj = new ControleOperacoesRelVO();

        int qtdEstornoRecorrencia = dto.getQtdEstornoContratoRecorrencia();
        obj.setQtdEstornoContratoRecorrencia(qtdEstornoRecorrencia);
        obj.setApresentarQtdEstornoContratoRecorrencia(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de estorno de contrato de usuário comum
        obj = new ControleOperacoesRelVO();

        int qtdEstornoUsuario = dto.getQtdEstornoContratoUsuarioComum();
        obj.setQtdEstornoContratoUsuarioComum(qtdEstornoUsuario);
        obj.setApresentarQtdEstornoContratoUsuarioComum(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de estorno de contrato de usuário comum
        obj = new ControleOperacoesRelVO();

        int qtdParcelasCanceladas = dto.getQtdParcelasCanceladas();
        obj.setQtdParcelasCanceladas(qtdParcelasCanceladas);
        obj.setApresentarQtdParcelasCanceladas(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de estorno de recibo
        obj = new ControleOperacoesRelVO();

        int qtdEstornoRecibo = dto.getQtdEstornoRecibo();
        obj.setQtdEstornoRecibo(qtdEstornoRecibo);
        obj.setApresentarQtdEstornoRecibo(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de alterações manuais de data base de contrato
        obj = new ControleOperacoesRelVO();

        int qtdAlteracoesDataBaseContrato = dto.getQtdAlteracoesDataBaseContrato();
        obj.setQtdAlteracoesDataBaseContrato(qtdAlteracoesDataBaseContrato);
        obj.setApresentarQtdAlteracoesDataBaseContrato(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de alterações manuais de data base de contrato
        obj = new ControleOperacoesRelVO();

        int qtdAlteracoesDataBasePagamento = dto.getQtdAlteracoesDataBasePagamento();
        obj.setQtdAlteracoesDataBasePagamento(qtdAlteracoesDataBasePagamento);
        obj.setApresentarQtdAlteracoesDataBasePagamento(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtd de parcelas renegociadas com data retroativas
        obj = new ControleOperacoesRelVO();
        obj.setQtdRenegociacaoParcelaRetroativa(dto.getQtdRenegociacaoParcelaRetroativa());
        obj.setApresentarQtdRenegociacaoParcelaRetroativa(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de alterações de recibo
        obj = new ControleOperacoesRelVO();

        int qtdAlteracoesRecibo = dto.getQtdAlteracoesRecibo();
        obj.setQtdAlteracoesRecibo(qtdAlteracoesRecibo);
        obj.setApresentarQtdAlteracoesRecibo(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de contratos cancelados
        obj = new ControleOperacoesRelVO();

        int qtdContratosCancelados = dto.getQtdContratosCancelamento();
        obj.setQtdContratosCancelamento(qtdContratosCancelados);
        obj.setApresentarQtdContratosCancelamento(true);
        getListaControleOperacoesRelVOs().add(obj);

        obj = new ControleOperacoesRelVO();
        //qtde de Transferidos com contratos cancelados
        int qtdContratosTransferidosCancelados = dto.getQtdContratosTransferidosCancelados();
        obj.setQtdContratosTransferidosCancelados(qtdContratosTransferidosCancelados);
        obj.setApresentarQtdContratosTransferidosCancelados(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de clientes com bônus
        obj = new ControleOperacoesRelVO();
        obj.setQtdClientesComBonus(dto.getQtdClientesComBonus());
        obj.setApresentarQtdClientesComBonus(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de clientes com freePass
        obj = new ControleOperacoesRelVO();
        obj.setQtdClientesComFreePass(dto.getQtdClientesComFreePass());
        obj.setApresentarQtdClientesComFreePass(true);
        getListaControleOperacoesRelVOs().add(obj);


        //qtde de clientes com GymPass
        obj = new ControleOperacoesRelVO();
        obj.setQtdClientesComGymPass(dto.getQtdClientesComGymPass());
        obj.setApresentarQtdClientesComGymPass(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtde de contratos do tipo bolsa
        obj = new ControleOperacoesRelVO();
        obj.setQtdContratosTipoBolsa(dto.getQtdContratosTipoBolsa());
        obj.setApresentarQtdContratosTipoBolsa(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtdClientesAutorizacaoNaoRenovavel
        obj = new ControleOperacoesRelVO();
        obj.setQtdClientesAutorizacaoNaoRenovavel(dto.getQtdClientesAutorizacaoNaoRenovavel());
        obj.setApresentarQtdClientesAutorizacaoNaoRenovavel(true);
        getListaControleOperacoesRelVOs().add(obj);

        //qtdClientesAutorizacaoRenovavel
        obj = new ControleOperacoesRelVO();
        obj.setQtdClientesAutorizacaoRenovavel(dto.getQtdClientesAutorizacaoRenovavel());
        obj.setApresentarQtdClientesAutorizacaoRenovavel(true);
        getListaControleOperacoesRelVOs().add(obj);


        //qtdClientesAutorizacaoRenovavel
        obj = new ControleOperacoesRelVO();
        obj.setApresentarValorDescontos(true);
        obj.setValorDescontos(dto.getValorDescontos());
        getListaControleOperacoesRelVOs().add(obj);

        //qtdAlunosExcluidosTreinoWeb
        obj = new ControleOperacoesRelVO();
        obj.setApresentarQtdClientesExcluidosTreinoWeb(true);
        obj.setQtdClientesExcluidosTreinoWeb(obj.getQtdClientesExcluidosTreinoWeb());
        getListaControleOperacoesRelVOs().add(obj);

        int qtdClientesExcluidos = dto.getQtdClientesExcluidos();
        obj = new ControleOperacoesRelVO();
        obj.setApresentarQtdClientesExcluidos(true);
        obj.setQtdClientesExcluidos(qtdClientesExcluidos);
        getListaControleOperacoesRelVOs().add(obj);

        //limpar mensagens de erro
        setMensagemDetalhada("", "");

    }

    public List<ColaboradorVO> listaTemporariaSetarSomenteAdiministrador() throws Exception {
        List<ColaboradorVO> listaColaborador = new ArrayList<ColaboradorVO>();
        List<UsuarioVO> listaUsuarios = getFacade().getUsuario().consultarPorAdministrador(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (UsuarioVO usu : listaUsuarios) {
            ColaboradorVO col = new ColaboradorVO();
            col.setColaboradorEscolhidoOperacoes(true);
            col.setPessoa(new PessoaVO());
            col.getPessoa().setNome(usu.getNome());
            listaColaborador.add(col);
        }
        return listaColaborador;
    }

    public void irParaTelaCliente() {
        PendenciaResumoPessoaRelVO obj = (PendenciaResumoPessoaRelVO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(obj.getClienteVO());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void abrirModalLog() {
        LogVO log = (LogVO) context().getExternalContext().getRequestMap().get("log");
        try {

            LogControle logControle = (LogControle) context().getExternalContext().getSessionMap().get("LogControle");

            logControle.setLogVO(log);
            List<LogVO> logs = new ArrayList<LogVO>();
            logs.add(log);
            logControle.setListaCampos(logs);
            context().getExternalContext().getSessionMap().put(LogControle.class.getSimpleName(), logControle);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void irParaModalLog() throws Exception {
        PendenciaResumoPessoaRelVO obj = (PendenciaResumoPessoaRelVO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        try {
            if (obj == null) {
                throw new Exception("Log Não Encontrado.");
            } else {
                LogControle logControle = (LogControle) context().getExternalContext().getSessionMap().get("LogControle");
                if (logControle == null) {
                    logControle = new LogControle();
                }

                logControle.setLogVO(obj.getLogVO());
                List<LogVO> logs = new ArrayList<LogVO>();
                logs.add(obj.getLogVO());
                logControle.setListaCampos(logs);
                context().getExternalContext().getSessionMap().put(LogControle.class.getSimpleName(), logControle);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarListaLog() {
        PendenciaResumoPessoaRelVO obj = (PendenciaResumoPessoaRelVO) context().getExternalContext().getRequestMap().get("resumoLog");
        try {
            if (obj == null) {
                throw new Exception("Log Não Encontrado.");
            } else {
                LogControle logControle = (LogControle) context().getExternalContext().getSessionMap().get("LogControle");
                if (logControle == null) {
                    logControle = new LogControle();
                }
                logControle.setListaConsulta(obj.getLogAgrupadoChavePrimaria().getListaLogsChavePrimaria());
                context().getExternalContext().getSessionMap().put(LogControle.class.getSimpleName(), logControle);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarClientesContratoCancelados() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdContratosCancelamento()) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<ClienteVO> lista = getFacade().getContratoOperacao().obterClientesCancelados(getInicio(), getDataBaseFiltroBI(),
                            getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), getEmpresaFiltroSessao().getCodigo());
                    for (ClienteVO cli : lista) {
                        PendenciaResumoPessoaRelVO resumo = new PendenciaResumoPessoaRelVO();
                        resumo.setClienteVO(cli);
                        obj.getListaPendenciaResumoPessoaRelVOs().add(resumo);
                    }
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarClientesComBonus() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdClientesComBonus()) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());

                    List<PendenciaResumoPessoaRelVO> lista = getFacade().getContratoOperacao().obterClientesComBonus(getDataBaseFiltroBI(),
                            getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), getEmpresaFiltroSessao().getCodigo());
                    obj.setListaPendenciaResumoPessoaRelVOs(lista);
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarClientesComFreePass() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdClientesComFreePass()) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());
                    List<PendenciaResumoPessoaRelVO> lista = getFacade().getContratoOperacao().obterClientesComFreePass(getDataBaseFiltroBI(),
                            getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), getEmpresaFiltroSessao().getCodigo());
                    for(PendenciaResumoPessoaRelVO resumo : lista) {
                        resumo.setApresentarFreePass(true);
                    }
                    obj.setListaPendenciaResumoPessoaRelVOs(lista);
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarClientesComGymPass() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdClientesComGymPass()) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());
                    List<PendenciaResumoPessoaRelVO> lista = getFacade().getContratoOperacao().obterClientesComGymPass(getDataBaseFiltroBI(), getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), getEmpresaFiltroSessao().getCodigo());
                    for(PendenciaResumoPessoaRelVO resumo : lista) {
                        resumo.setApresentarGymPass(true);
                    }
                    obj.setListaPendenciaResumoPessoaRelVOs(lista);
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public ControleOperacoesRelVO getControleOperacoesRelVO() {
        return controleOperacoesRelVO;
    }
    public int getQtdItensLista(){
        return controleOperacoesRelVO.getListaPendenciaResumoPessoaRelVOs().size();
    }
    public void setControleOperacoesRelVO(ControleOperacoesRelVO controleOperacoesRelVO) {
        this.controleOperacoesRelVO = controleOperacoesRelVO;
    }

    public List<ControleOperacoesRelVO> getListaControleOperacoesRelVOs() {
        return listaControleOperacoesRelVOs;
    }

    public void setListaControleOperacoesRelVOs(List<ControleOperacoesRelVO> listaControleOperacoesRelVOs) {
        this.listaControleOperacoesRelVOs = listaControleOperacoesRelVOs;
    }

    public Boolean getConsultarPorTodosColaboradores() {
        return consultarPorTodosColaboradores;
    }

    public void setConsultarPorTodosColaboradores(Boolean consultarPorTodosColaboradores) {
        this.consultarPorTodosColaboradores = consultarPorTodosColaboradores;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }


    public String getDataBase_Apresentar(){
        DateFormat dfmt = new SimpleDateFormat("MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataBaseFiltroBI());
        return dfmt.format(cal.getTime());
    }

    public Boolean getMostrarPaginacao() {
        if (mostrarPaginacao == null) {
            mostrarPaginacao = Boolean.FALSE;
        }
        return mostrarPaginacao;
    }

    public void setMostrarPaginacao(Boolean mostrarPaginacao) {
        this.mostrarPaginacao = mostrarPaginacao;
    }

    public String getNumeroPaginacao() {
        if (getMostrarPaginacao()) {
            return "10";
        } else {
            return "";
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

            String[] split = paramsTableFiltrada.split(",");
            String campoOrdenacao = split[0].replace("[", "");
            String ordem = split[1];
            String filtro = split[2].replace("''", "");
            filtro = filtro.replace("]", "");
            List listaParaImpressao = getFacade().getContratoOperacao().consultarParaImpressaoContratosCancelados(getEmpresaLogado().getCodigo(), getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), getInicio(), getDataBaseFiltroBI(), filtro, ordem, campoOrdenacao);
            exportadorListaControle.exportar(evt, listaParaImpressao, filtro, ItemExportacaoEnum.BI_EXCECOES_CONTRATOS_CANCELAMENTO);
            if(exportadorListaControle.getErro()){
                throw new Exception(exportadorListaControle.getMensagemDetalhada());
            }
            setMsgAlert(exportadorListaControle.getOperacaoOnComplete());
        } catch (Exception e){
            montarErro(e);
        }

    }

    public boolean isExibirTodos() {
        return exibirTodos;
    }

    public void setExibirTodos(boolean exibirTodos) {
        this.exibirTodos = exibirTodos;
    }

    public void selecionarAlunoBolsa() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdContratosTipoBolsa()) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());
                    List<PendenciaResumoPessoaRelVO> lista = getFacade().getContratoOperacao().obterAlunosContratoTipoBolsa(getInicio(),
                            getDataBaseFiltroBI(), getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), getEmpresaFiltroSessao().getCodigo());
                    obj.setListaPendenciaResumoPessoaRelVOs(lista);
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarClientesAutorizacaoNaoRenovavel() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdClientesAutorizacaoNaoRenovavel()) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());
                    List<PendenciaResumoPessoaRelVO> lista = getFacade().getContrato().obterAlunosAutorizacaoContratos(getEmpresaFiltroSessao().getCodigo(),
                            getDataBaseFiltroBI(), getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), null, false, true, false);
                    obj.setListaPendenciaResumoPessoaRelVOs(lista);
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarClientesAutorizacaoRenovavel() {
        try {
            setControleOperacoesRelVO(new ControleOperacoesRelVO());
            for (ControleOperacoesRelVO obj : getListaControleOperacoesRelVOs()) {
                if (obj.isApresentarQtdClientesAutorizacaoRenovavel()) {
                    obj.setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());
                    List<PendenciaResumoPessoaRelVO> lista = getFacade().getContrato().obterAlunosAutorizacaoContratos(getEmpresaFiltroSessao().getCodigo(),
                            getDataBaseFiltroBI(), getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name()), null, false, false, true);
                    obj.setListaPendenciaResumoPessoaRelVOs(lista);
                    setControleOperacoesRelVO(obj);
                }
            }
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarDescontos() {
        try {
            setDescontos(getFacade().getMovProduto().consultarDescontos(getInicio(),
                    getDataBaseFiltroBI(),
                    getEmpresaFiltroSessao().getCodigo(),
                    getListaColaboradorFiltroBI(BIEnum.CONTROLE_OPERACOES.name())));
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<DescontoTO> getDescontos() {
        if(descontos == null){
            descontos = new ArrayList<DescontoTO>();
        }
        return descontos;
    }

    public void setDescontos(List<DescontoTO> descontos) {
        this.descontos = descontos;
    }

    public int getQtdItensDescontos(){
        return getDescontos().size();
    }

    public String getNomeTela() {
        return nomeTela;
    }

    public void setNomeTela(String nomeTela) {
        this.nomeTela = nomeTela;
    }
}

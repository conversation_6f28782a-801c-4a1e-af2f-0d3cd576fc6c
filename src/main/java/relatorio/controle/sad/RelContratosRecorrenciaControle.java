package relatorio.controle.sad;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoConsultaParcelasEnum;
import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import controle.financeiro.GestaoRemessasControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoBIDCC;
import negocio.comuns.basico.enumerador.TipoPendenciaEnum;
import negocio.comuns.basico.fabricas.BiDCCRelAbstractFactory;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.financeiro.BIInadimplenciaTO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.TotalizadorRemessaTO;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.utilitarias.*;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.basico.BIControle;
import relatorio.controle.basico.PendenciaControleRel;
import relatorio.controle.basico.RecorrenciaClienteTO;
import relatorio.negocio.comuns.basico.BiDCCRelVO;
import relatorio.negocio.comuns.basico.PendenciaRelVO;
import relatorio.negocio.comuns.basico.ResultadoBITO;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

public class RelContratosRecorrenciaControle extends BIControle {

    private static final String KEY_TOTALIZADOR = "totalizador";
    private static final String KEY_DADOS_INADIMPLENCIA = "dadosInadimplencia";
    private static final String TITULO_ATIVOS = "BI - Contratos ativos em regime de recorrência";
    private static final String TITULO_CANCELADOS = "BI - Contratos cancelados automaticamente pela recorrência";
    private static final String TITULO_NAO_RENOVADOS = "BI - Contratos que não conseguiram renovação automática";
    private static final String TITULO_PARCELAS_SEM_AUTORIZACAO_DE_COBRANCA = "BI - Contratos de DCC sem Autorização de Cobrança";
    private static final String TITULO_PARCELAS_CANCELADAS = "BI - Alunos com Parcelas Canceladas";
    private static final String TITULO_PARCELAS_ABERTAS = "BI - Parcelas em aberto";
    private static final String TITULO_OPERACOES_SUSPEITAS = "BI - Operações Suspeitas";
    private Integer totalContratosAtivosRecorrencia;
    private Integer totalContratosCanceladosRecorrencia;
    private Integer totalNaoRenovadosRecorrencia;
    private Integer contratosRecorrenciaSemCartao;
    private Integer cartoesCreditosVencidos;
    private Integer cartoesCreditosAVencer;
    private Integer parcelasCanceladas;
    private Integer parcelasVencidaAbertas;
    private Integer operacoesSuspeitas;
    private List<RecorrenciaClienteTO> listaApresentarRecorrenciaCliente = new ArrayList<RecorrenciaClienteTO>();
    private Boolean mostrarPaginacao;
    private String tituloDetalhamento;
    private boolean apresentarVencimento = false;
    private List<TotalizadorRemessaTO> totalizador = new ArrayList<TotalizadorRemessaTO>();
    private List<TotalizadorRemessaTO> totalizadorAguardandoRetorno;
    private boolean somenteParcelasMes = true;
    private boolean incluirContratosCancelados = false;
    private boolean somenteParcelasForaMes = false;
    private double valorParcelasVencidaAbertas;
    private boolean camposEspeciais = false;
    private boolean totalizando = false;
    private boolean apresentarParcelas = false;
    private boolean apresentarSuspeita = false;
    private boolean apresentarValorParcela = false;
    private boolean apresentarFormaPagamento = false;
    private Integer clientesMesmoCartao;
    private Integer parcelasEmAberto;
    private Double valorParcelasAberto;
    private boolean exibirTodos = false;
    List<BiDCCRelVO> listaBIDCC = new ArrayList<BiDCCRelVO>();
    private List<SelectItem> conveniosCobranca = new ArrayList<SelectItem>();
    private List<String> listaConveniosSelecionados = new ArrayList<String>();
    private Set<TipoAutorizacaoCobrancaEnum> tiposAutorizacaoSelecionada = new HashSet<TipoAutorizacaoCobrancaEnum>();
    private Map<Integer, ConvenioCobrancaVO> conveniosCarregados = new HashMap<Integer, ConvenioCobrancaVO>();
    private List<BIInadimplenciaTO> dadosInadimplencia;

    private String itemExportacaoSelecionado;

    private boolean marcarTodos = true;

    public RelContratosRecorrenciaControle() throws Exception{
        carregarConvenios();
	}

	public void abrirFiltros(){
        marcarTodos = listaConveniosSelecionados.size() == conveniosCobranca.size();
    }

    /**
     * Realiza o carregamento da lista de convênios que serão populados no filtro
     */
    private void carregarConvenios() {
        try{
            List<ConvenioCobrancaVO> convenios = null;
            if (getEmpresaFiltroBI() != null && UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo())) {
                convenios = getFacade().getConvenioCobranca().consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                convenios = getFacade().getConvenioCobranca().consultarPorEmpresa(getEmpresaFiltroBI().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            conveniosCobranca.clear();
            for(ConvenioCobrancaVO convenio : convenios){
                conveniosCobranca.add(new SelectItem(convenio.getCodigo(), convenio.getDescricao()));
                conveniosCarregados.put(convenio.getCodigo(), convenio);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private List<ColaboradorVO> getListaColaboradoresFiltroBI(){
        List<ColaboradorVO> colaboradoresSelecionados = new ArrayList<ColaboradorVO>();

        for (Object obj: (List)JSFUtilities.getManagedBean("BIControle.listaColaboradorVOs")) {
            ColaboradorVO colaboradorVO = (ColaboradorVO) obj;
            if (colaboradorVO.getColaboradorEscolhido()) {
                colaboradoresSelecionados.add(colaboradorVO);
            }
        }

        return colaboradoresSelecionados;
    }

    private List<Integer> getListaIDsColaboradoresFiltroBI() {
        List<Integer> lista = new ArrayList<Integer>();
        for (ColaboradorVO colaboradorVO: getListaColaboradoresFiltroBI()) {
            lista.add(colaboradorVO.getCodigo());
        }
        return lista;
    }

    public void atualizar() {
        try {
//            if (getFacade().getMemCachedManager().getMemcachedOn())
//                carregar();
//            else
            carregarConvenios();
            consultarContratosRecorrencia(false);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void atualizarAgora() {
        try {
            gravarHistoricoAcessoBI(BIEnum.DCC);
            consultarContratosRecorrencia(true);
        }catch (Exception ex){
            montarErro(ex);
        }
    }

    private void consultarContratosRecorrencia(boolean atualizarAgora) {
        try {
            setListaBIDCC(new ArrayList<>());
            setTotalizador(new ArrayList<>());
            setDadosInadimplencia(new ArrayList<>());
            setTotalizadorAguardandoRetorno(new ArrayList<>());

            FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(), BIEnum.DCC, getFiltroDTO(atualizarAgora));
            JSONObject dados = new JSONObject(filtroDTO.getJsonDados());

            JSONArray itens = dados.getJSONArray("itens");
            for (int e = 0; e < itens.length(); e++) {
                getListaBIDCC().add(new BiDCCRelVO(itens.getJSONObject(e)));
            }

            JSONArray totalizador = dados.getJSONArray("totalizador");
            for (int e = 0; e < totalizador.length(); e++) {
                getTotalizador().add(new TotalizadorRemessaTO(totalizador.getJSONObject(e)));
            }

            JSONArray dadosInadimplencia = dados.getJSONArray("dadosInadimplencia");
            for (int e = 0; e < dadosInadimplencia.length(); e++) {
                getDadosInadimplencia().add(new BIInadimplenciaTO(dadosInadimplencia.getJSONObject(e)));
            }

            JSONArray totalizadorAguardandoRetorno = dados.getJSONArray("totalizadorAguardandoRetorno");
            for (int e = 0; e < totalizadorAguardandoRetorno.length(); e++) {
                getTotalizadorAguardandoRetorno().add(new TotalizadorRemessaTO(totalizadorAguardandoRetorno.getJSONObject(e)));
            }
            Ordenacao.ordenarLista(getListaBIDCC(), "ordem");
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            montarErro(ex);
        }
    }

	/**
	 * Responsável por
	 * author: alcides
	 * 21/10/2011
	 */
	private void consultarContratosRecorrencia_ANTIGO(boolean atualizarCache) throws Exception {
        try {
            ResultadoBITO resultado = obterResultadoBIDiaCache(BIEnum.DCC);
            verificarTiposAutorizacaoSelecionadas();
            if(validarResultadoBIDia(resultado) || atualizarCache){
                consultarRecorrencia();
                prepararTotalizador();
                resultado = new ResultadoBITO();
                resultado.getResultadosBI().clear();
                for(BiDCCRelVO obj : getListaBIDCC()){
                    resultado.getResultadosBI().put(obj.getTipo().name(),obj.getTipo().getDescricao()+"<>"+obj.getQtd()+"<>"+obj.getValor());
                }
                resultado.getResultadosBI().put(KEY_TOTALIZADOR,getTotalizador());
                resultado.getResultadosBI().put(KEY_DADOS_INADIMPLENCIA, getDadosInadimplencia());
                adicionarResultadoBI(BIEnum.DCC,resultado);
            } else {
                setListaBIDCC(new ArrayList<BiDCCRelVO>());
                for(String key : resultado.getResultadosBI().keySet()){
                    if(key.equals(KEY_TOTALIZADOR) || key.equals(KEY_DADOS_INADIMPLENCIA)){
                        continue;
                    }
                    TipoBIDCC tipo  = TipoBIDCC.valueOf(key);
                    String el = (String)resultado.getResultadosBI().get(key);
                    BiDCCRelVO obj = new BiDCCRelVO();
                    obj.setTipo(tipo);
                    obj.setQtd(new Integer(el.split("<>")[1]));
                    try {
                        obj.setValor(new Double(el.split("<>")[2]));
                    }catch (Exception ignored){
                        System.out.println(ignored);
                    }
                    if(!getListaBIDCC().contains(obj))
                        getListaBIDCC().add(obj);
                }
                setTotalizador((List<TotalizadorRemessaTO>) resultado.getResultadosBI().get(KEY_TOTALIZADOR));
                setDadosInadimplencia((List<BIInadimplenciaTO>) resultado.getResultadosBI().get(KEY_DADOS_INADIMPLENCIA));
                Ordenacao.ordenarLista(getListaBIDCC(),"ordem");                
            }
        }catch (Exception ex){
            Uteis.logar(ex, this.getClass());
         montarErro(ex);
        }
    }

    private void verificarTiposAutorizacaoSelecionadas() {
        this.tiposAutorizacaoSelecionada.clear();
        for(String codigo : this.listaConveniosSelecionados){
            ConvenioCobrancaVO convenio = conveniosCarregados.get(Integer.valueOf(codigo));
            this.tiposAutorizacaoSelecionada.add(convenio.getTipo().getTipoAutorizacao());
        }
    }

    public void consultarRecorrencia() throws Exception {
        getListaBIDCC().clear();
        TipoBIDCC[] tipos = TipoBIDCC.values();
        for (TipoBIDCC tipo : tipos) {
            if(tipo.getTipoAutorizacao() == null || this.tiposAutorizacaoSelecionada.isEmpty() || this.tiposAutorizacaoSelecionada.contains(tipo.getTipoAutorizacao())) {
                BiDCCRelVO obj = new BiDCCRelVO();
                obj.setTipo(tipo);
                if (tipo.equals(TipoBIDCC.ParcelasEmAberto)) {
                    ResultSet rs = obterResult(tipo);
                    while (rs.next()) {
                        obj.setValor(rs.getDouble("total"));
                        obj.setQtd(rs.getInt("qtd"));
                    }
                } else {
                    obj.setQtd(obterCount(tipo));
                    obj.setValor(obterSum(tipo));
                }
                if(!getListaBIDCC().contains(obj))
                    getListaBIDCC().add(obj);
            }
        }
    }
    public Integer obterCount(TipoBIDCC obj) throws Exception{
        return BiDCCRelAbstractFactory.getFactory(obj,getFacade(), getEmpresaFiltroBI().getCodigo(),getConfPaginacao(), getListaIDsColaboradoresFiltroBI(),getDataBaseFiltro(), ColecaoUtils.convertStringToInt(this.listaConveniosSelecionados)).getCount();
    }

    public Double obterSum(TipoBIDCC obj) throws Exception{
        return BiDCCRelAbstractFactory.getFactory(obj,getFacade(),
                getEmpresaFiltroBI().getCodigo(),getConfPaginacao(), getListaIDsColaboradoresFiltroBI(),getDataBaseFiltro(), ColecaoUtils.convertStringToInt(this.listaConveniosSelecionados)).getSum();
    }

    public ResultSet obterResult(TipoBIDCC obj) throws Exception{
        PendenciaControleRel pendenciaControle = (PendenciaControleRel) getControlador(PendenciaControleRel.class);
        return BiDCCRelAbstractFactory.getFactory(obj,getFacade(),
                getEmpresaFiltroBI().getCodigo(),getConfPaginacao(), getListaIDsColaboradoresFiltroBI(),getDataBaseFiltro(), ColecaoUtils.convertStringToInt(this.listaConveniosSelecionados)).getResult();
    }
    public List obterList(TipoBIDCC obj) throws Exception{
        return BiDCCRelAbstractFactory.getFactory(obj,getFacade(),
                getEmpresaFiltroBI().getCodigo(),getConfPaginacao(), getListaIDsColaboradoresFiltroBI(),getDataBaseFiltro(), ColecaoUtils.convertStringToInt(this.listaConveniosSelecionados)).getList();
    }
    public String obterCondicaoColaboradores(){
        String codigo = Uteis.retornarCodigos(getListaColaboradoresFiltroBI());
        return UteisValidacao.emptyString(codigo) ? " true " : " vinculo.colaborador in ("+ codigo+")";
    }
    public List<Integer> getlistaIDsColaboradoresFiltroBI(){
        List<Integer> codigos = new ArrayList<Integer>();
        for(ColaboradorVO co : getListaColaboradorVOs()){
            codigos.add(co.getCodigo());
        }
        return codigos;
    }

    public EmpresaVO getEmpresaFiltroBI(){
        return (EmpresaVO)JSFUtilities.getManagedBean("BIControle.empresaFiltro");
    }

    public void prepararListaTotalizador(ActionEvent evt) throws Exception{
        totalizando = true;
        TotalizadorRemessaTO obj = (TotalizadorRemessaTO) context().getExternalContext().getRequestMap().get("totalizador");
        this.setTituloDetalhamento(obj.getLabel());
        this.setMostrarPaginacao(true);
        this.setApresentarVencimento(true);
        this.setApresentarParcelas(true);
        this.setApresentarSuspeita(false);
        this.setApresentarValorParcela(true);
        this.setItemExportacaoSelecionado(obj.getLabel().equalsIgnoreCase("Enviadas com retorno") ? ItemExportacaoEnum.BI_DCC_ENVIADA_COM_RETORNO.getId() : (obj.getLabel().equalsIgnoreCase("Pagas pelo convênio") ? ItemExportacaoEnum.BI_DCC_PAGAS_CONVENIO.getId() : (obj.getLabel().equalsIgnoreCase("Pagas fora do convênio") ? ItemExportacaoEnum.BI_DCC_PAGAS_FORA_CONVENIO.getId() :(obj.getLabel().equalsIgnoreCase("Não pagas até hoje") ? ItemExportacaoEnum.BI_DCC_NAO_PAGAS_ATE_HOJE.getId() : ""))));
        this.setApresentarFormaPagamento(obj.isApresentarFormaPagamento());
        setMensagemDetalhada("", "");
        setMensagem("");
        setMensagemID("");
        setListaApresentarRecorrenciaCliente(new ArrayList<RecorrenciaClienteTO>());
        for(MovParcelaVO parcela : obj.getListaParcelas()){
            RecorrenciaClienteTO recorrenciaClienteTO = getFacade().getMovParcela().parcelaRecorrencia(parcela, obj.isApresentarFormaPagamento());
            getListaApresentarRecorrenciaCliente().add(recorrenciaClienteTO);
        }
        Ordenacao.ordenarLista(getListaApresentarRecorrenciaCliente(), "nomeCliente");
    }

    public void prepararLista(ActionEvent evt){
        try {
            totalizando = false;
            BiDCCRelVO obj = (BiDCCRelVO) context().getExternalContext().getRequestMap().get("obj");
            camposEspeciais = obj.getTipo().equals(TipoBIDCC.ParcelasEmAberto);
            if(obj.getTipo().equals(TipoBIDCC.CartoesCreditoVencidos)){
                consultarCartoesCreditosVencidos();
            } if(obj.getTipo().equals(TipoBIDCC.CartoesCreditoAVencer)){
                consultarCartoesCreditoAVencer();
            } if(obj.getTipo().equals(TipoBIDCC.ClientesMesmoCartao)){
                consultarClientesMesmoCartao();
            } if(obj.getTipo().equals(TipoBIDCC.CartoesComProblema)){
                consultarCartoesComProblema();
            } else {
                this.setTituloDetalhamento(obj.getTipo().getDescricao());
                this.setMostrarPaginacao(obj.getTipo().isPaginacao());
                this.setApresentarVencimento(obj.getTipo().isApresentarVencimento());
                this.setApresentarParcelas(obj.getTipo().isApresentarParcela());
                this.setApresentarSuspeita(obj.getTipo().isApresentarSuspeita());
                this.setApresentarValorParcela(obj.getTipo().isApresentarValorParcela());
                this.setListaApresentarRecorrenciaCliente(obterList(obj.getTipo()));
                setMensagemDetalhada("", "");
                setMensagem("");
                setMensagemID("");

                if(obj.getTipo().equals(TipoBIDCC.ParcelasVencidasEmAberto)){
                    consultarParcelasVencidasAbertas();
                }
            }
            setItemExportacaoSelecionado(obj.getTipo().getItemExportacao().getId());
        }catch (Exception ex){
            montarErro(ex);
        }
    }

//    public String abrirLista(){
//        BiDCCRelVO obj = (BiDCCRelVO) context().getExternalContext().getRequestMap().get("obj");
//        if(obj.getTipo().equals(TipoBIDCC.ParcelasVencidasEmAberto)){
//            return consultarParcelasVencidasAbertas();
//        }
//        return "";
//    }
    public void consultarParcelasVencidasAbertas() throws  Exception {
        try {
            GestaoRemessasControle gestaoControle = (GestaoRemessasControle) context().getExternalContext().getSessionMap().get("GestaoRemessasControle");
            if (gestaoControle == null) {
                gestaoControle = new GestaoRemessasControle();
                context().getExternalContext().getSessionMap().put("GestaoRemessasControle", gestaoControle);
            }
//            gestaoControle.fecharPanelDadosParametros();
//            gestaoControle.setAbaSelecionada("abaParcelas");

            String[] tipoConsulta = new String[2];
            tipoConsulta[0] = TipoConsultaParcelasEnum.PARCELAS_REPESCAGEM.toString();
            tipoConsulta[1] = TipoConsultaParcelasEnum.PARCELAS_EM_ABERTO_AUTORIZADAS.toString();
            gestaoControle.setTiposConsultaEscolhido(tipoConsulta);
            gestaoControle.setConvenio(new ConvenioCobrancaVO());
            gestaoControle.setDataInicio(null);
            gestaoControle.setDataFim(this.getDataBaseFiltroBI());
            gestaoControle.consultarParcelasBI(getEmpresaFiltroBI().getCodigo(),ColecaoUtils.convertStringToInt(this.listaConveniosSelecionados), new ArrayList<>(), getDataBaseFiltro());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarCartoesCreditosVencidos() {
        try {
            BiDCCRelVO obj = (BiDCCRelVO)context().getExternalContext().getRequestMap().get("obj");
            PendenciaControleRel pendenciaControle = getControlador(PendenciaControleRel.class);
            PendenciaRelVO pendenciaRelVO = new PendenciaRelVO();
            pendenciaRelVO.setTipo(TipoPendenciaEnum.CartoesVencidos);
            pendenciaRelVO.setQtd(obj != null ? obj.getQtd() : 0);
            pendenciaRelVO.getListaPendenciaResumoPessoaRelVOs().clear();
            pendenciaRelVO.setExibirAutorizacaoCobrancaCliente(true);
            getConfPaginacao().setExistePaginacao(true);
            getConfPaginacao().setPaginaAtual(0);
            getConfPaginacao().setItensPorPagina(15);
            getConfPaginacao().setColunaOrdenacao("");
            getConfPaginacao().setOrdernar(false);
            pendenciaControle.setConfPaginacao(getConfPaginacao());
            pendenciaControle.add(pendenciaRelVO);
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarCartoesCreditoAVencer() {
        try {
            BiDCCRelVO obj = (BiDCCRelVO)context().getExternalContext().getRequestMap().get("obj");
            PendenciaControleRel pendenciaControle = getControlador(PendenciaControleRel.class);
            PendenciaRelVO pendenciaRelVO = new PendenciaRelVO();
            pendenciaRelVO.setQtd(obj != null ? obj.getQtd() : 0);
            pendenciaRelVO.setTipo(TipoPendenciaEnum.CartoesAVencer);
            pendenciaRelVO.getListaPendenciaResumoPessoaRelVOs().clear();
            pendenciaRelVO.setExibirAutorizacaoCobrancaCliente(true);
            getConfPaginacao().setExistePaginacao(true);
            getConfPaginacao().setPaginaAtual(0);
            getConfPaginacao().setItensPorPagina(15);
            getConfPaginacao().setColunaOrdenacao("");
            getConfPaginacao().setOrdernar(false);
            pendenciaControle.setConfPaginacao(getConfPaginacao());
            pendenciaControle.setPendenciaRelVO(pendenciaRelVO);
            pendenciaControle.add(pendenciaRelVO);
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarCartoesComProblema() {
        try {
            BiDCCRelVO obj = (BiDCCRelVO)context().getExternalContext().getRequestMap().get("obj");
            PendenciaControleRel pendenciaControle = getControlador(PendenciaControleRel.class);
            PendenciaRelVO pendenciaRelVO = new PendenciaRelVO();
            pendenciaRelVO.setQtd(obj != null ? obj.getQtd() : 0);
            pendenciaRelVO.setTipo(TipoPendenciaEnum.CartoesComProblema);
            pendenciaRelVO.getListaPendenciaResumoPessoaRelVOs().clear();
            pendenciaRelVO.setExibirAutorizacaoCobrancaCliente(true);
            getConfPaginacao().setExistePaginacao(true);
            getConfPaginacao().setPaginaAtual(0);
            getConfPaginacao().setItensPorPagina(15);
            getConfPaginacao().setColunaOrdenacao("");
            getConfPaginacao().setOrdernar(false);
            pendenciaControle.setConfPaginacao(getConfPaginacao());
            pendenciaControle.setPendenciaRelVO(pendenciaRelVO);
            pendenciaControle.add(pendenciaRelVO);
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarClientesMesmoCartao() {
        try {
            PendenciaControleRel pendenciaControle = (PendenciaControleRel) JSFUtilities.getFromSession("PendenciaControleRel");
            pendenciaControle.setNomeCliente("");
            consultarClientesMesmoCartaoSemLimparNome();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarClientesMesmoCartaoSemLimparNome() throws Exception{
        PendenciaControleRel pendenciaControle = (PendenciaControleRel) JSFUtilities.getFromSession("PendenciaControleRel");
        pendenciaControle.getPendenciaRelVO().setQtd(getFacade().getContratoRecorrencia().contarPendenciaClientesMesmoCartao(getEmpresaFiltroBI().getCodigo(), getDataBaseInicialFiltroBI(),
                this.getDataBaseFiltroBI(), Collections.EMPTY_LIST,pendenciaControle.getSomenteClientesAtivos()));
        pendenciaControle.getPendenciaRelVO().setTipo(TipoPendenciaEnum.ClientesMesmoCartao);
        pendenciaControle.getPendenciaRelVO().getListaPendenciaResumoPessoaRelVOs().clear();
        pendenciaControle.getPendenciaRelVO().setExibirAutorizacaoCobrancaCliente(true);
        pendenciaControle.getPendenciaRelVO().setExibirInformacoesFinanceiras(false);
        getConfPaginacao().setExistePaginacao(true);
        getConfPaginacao().setPaginaAtual(0);
        getConfPaginacao().setItensPorPagina(15);
        getConfPaginacao().setColunaOrdenacao("");
        getConfPaginacao().setOrdernar(false);
        pendenciaControle.setConfPaginacao(getConfPaginacao());
        pendenciaControle.add(pendenciaControle.getPendenciaRelVO());
        setMensagemDetalhada("", "");
        setMensagem("");
        setMensagemID("");
    }

    /**
	 * author: alcides
	 * 16/09/2011
	 */
	public void irParaTelaCliente() {
        RecorrenciaClienteTO obj = (RecorrenciaClienteTO) context().getExternalContext().getRequestMap().get("item");
	        try {
	            if (obj == null) {
	                throw new Exception("Cliente Não Encontrado.");
	            } else {
                    ClienteVO clienteVO = new ClienteVO();
                    clienteVO.setCodigo(obj.getCodigoCliente());
	                irParaTelaCliente(clienteVO);
	            }
	        } catch (Exception e) {
	            setMensagemDetalhada("msg_erro", e.getMessage());
	        }
	    }

	public void selecionarGrupoColaboradorParticipante() throws Exception {
        try {
            GrupoColaboradorVO obj = (GrupoColaboradorVO) context().getExternalContext().getRequestMap().get("grupoColaborador");
            obj.setAbrirSimpleTooglePanelPassivo(!obj.getAbrirSimpleTooglePanelPassivo());
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }
    }
    public void toggleParcelasSomenteEsseMes(){
        try {
            setSomenteParcelasMes(!isSomenteParcelasMes());
            consultarContratosRecorrencia(true);
        }catch (Exception ex){
            montarErro(ex);
        }
    }

    public void toggleIncluirContratosCancelados(){
        try {
            setIncluirContratosCancelados(!isIncluirContratosCancelados());
            consultarContratosRecorrencia(true);
        }catch (Exception ex){
            montarErro(ex);
        }
    }

    public void toggleParcelasSomenteForaMes(){
        try{
            setSomenteParcelasForaMes(!isSomenteParcelasForaMes());
            consultarContratosRecorrencia(true);
        }catch (Exception ex){
            montarErro(ex);
        }
    }

    public void prepararTotalizador() throws Exception {
        setDadosInadimplencia(new ArrayList<BIInadimplenciaTO>());
        setTotalizador(new ArrayList<TotalizadorRemessaTO>());
        setTotalizadorAguardandoRetorno(new ArrayList<TotalizadorRemessaTO>());

        List<Date> mesesConsultar = Uteis.getMesesEntreDatas(Uteis.somarMeses(getDataBaseFiltro(), -5), getDataBaseFiltro());

        List<Integer> conveniosSelecionados = ColecaoUtils.convertStringToInt(this.listaConveniosSelecionados);

        List<Integer> tiposSelecionados = new ArrayList<Integer>();
        List<Integer> conveniosSelecionadosTodos = ColecaoUtils.convertStringToInt(this.listaConveniosSelecionados);
        List<ConvenioCobrancaVO> listaConveniosSel = new ArrayList<ConvenioCobrancaVO>();
        if(!UteisValidacao.emptyList(conveniosSelecionadosTodos)) {
            listaConveniosSel = getFacade().getConvenioCobranca().consultarPorCodigoIn(Uteis.montarListaIN(conveniosSelecionadosTodos), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        for (ConvenioCobrancaVO conv : listaConveniosSel) {
            if (conv.getTipo().isTransacaoOnline()) {
                if(!tiposSelecionados.contains(conv.getTipo().getTipoRemessa().getTipoTransacao().getId()))
                    tiposSelecionados.add(conv.getTipo().getTipoRemessa().getTipoTransacao().getId());
            }
        }

        if (!UteisValidacao.emptyList(mesesConsultar))
            for (Date mesPesquisar : mesesConsultar) {

                Integer empresa = getEmpresaFiltroBI().getCodigo();
                boolean somenteMes =  isSomenteParcelasMes();
                boolean somenteForaMes = isSomenteParcelasForaMes();

                String nrTentativas = "";
                String somenteConvenio = "";
                String situacaoParcela = "";
                String situacaoRemessa = "";

                Date dataInicioPesquisar = Uteis.obterPrimeiroDiaMes(mesPesquisar);
                Date dataFinalPesquisar = Uteis.obterUltimoDiaMes(mesPesquisar);

                //SE FOR O MES QUE ESTÁ SELECIONADO PELO USUARIO A DATA FINAL DA PESQUISA DEVE SER A DATA QUE FOI SELECIONADA NO CALENDÁRIO
                if (Uteis.getDataMesAnoConcatenado(mesPesquisar).equals(Uteis.getDataMesAnoConcatenado(getDataBaseFiltro()))) {
                    dataFinalPesquisar = getDataBaseFiltro();
                }

                //ENVIADAS
                TotalizadorRemessaTO enviadas = new TotalizadorRemessaTO();
                enviadas.setLabel("Enviadas com retorno");
                enviadas.setMesReferencia(mesPesquisar);
                nrTentativas = "";
                somenteConvenio = "";
                situacaoParcela = this.incluirContratosCancelados ? " not in ('RG') " : " not in ('CA','RG') ";
                situacaoRemessa = " AND re.situacaoremessa not IN  (" + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "," + SituacaoRemessaEnum.GERADA.getId() + ") ";
                getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(enviadas, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                        situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, "");
                enviadas.calcularPorcentagem(enviadas.getValor());

                //ACEITAS PELO CONVÊNIO
                TotalizadorRemessaTO aceitaConvenio = new TotalizadorRemessaTO();
                aceitaConvenio.setLabel("Pagas pelo convênio");
                aceitaConvenio.setMesReferencia(mesPesquisar);
                nrTentativas = "";
                somenteConvenio = " AND (fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) ";
                situacaoParcela = "= 'PG'";
                situacaoRemessa = "";
                getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(aceitaConvenio, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                        situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, "");
                aceitaConvenio.calcularPorcentagem(enviadas.getValor());

                //PAGAS NA PRIMEIRA TENTATIVA
                TotalizadorRemessaTO primeiraTentativa = new TotalizadorRemessaTO();
                primeiraTentativa.setLabel("Primeira Tentativa");
                primeiraTentativa.setMesReferencia(mesPesquisar);
                nrTentativas = " > 0 ";
                somenteConvenio = " AND (fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) ";
                situacaoParcela = "= 'PG'";
                situacaoRemessa = "";
                getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(primeiraTentativa, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                        situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, " = 1 ");
                primeiraTentativa.calcularPorcentagem(enviadas.getValor());

                //PAGAS NA SEGUNDA TENTATIVA
                TotalizadorRemessaTO segundaTentativa = new TotalizadorRemessaTO();
                segundaTentativa.setLabel("Segunda Tentativa");
                segundaTentativa.setMesReferencia(mesPesquisar);
                nrTentativas = " > 0 ";
                somenteConvenio = " AND (fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) ";
                situacaoParcela = "= 'PG'";
                situacaoRemessa = "";
                getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(segundaTentativa, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                        situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, " = 2 ");
                segundaTentativa.calcularPorcentagem(enviadas.getValor());

                //DEMAIS TENTATIVAS
                TotalizadorRemessaTO outrasTentativas = new TotalizadorRemessaTO();
                outrasTentativas.setLabel("Demais Tentativas");
                outrasTentativas.setMesReferencia(mesPesquisar);
                nrTentativas = " > 0 ";
                somenteConvenio = " AND (fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) ";
                situacaoParcela = "= 'PG'";
                situacaoRemessa = "";
                getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(outrasTentativas, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                        situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, " > 2 ");
                outrasTentativas.calcularPorcentagem(enviadas.getValor());

                //PAGAS FORA DO CONVÊNIO
                TotalizadorRemessaTO foraConvenio = new TotalizadorRemessaTO();
                foraConvenio.setLabel("Pagas fora do convênio");
                foraConvenio.setMesReferencia(mesPesquisar);
                nrTentativas = "";
                somenteConvenio = " AND fp.conveniocobranca is null AND fp.defaultrecorrencia = false ";
                situacaoParcela = "= 'PG'";
                situacaoRemessa = "";
                getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(foraConvenio, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                        situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, "");
                foraConvenio.calcularPorcentagem(enviadas.getValor());

                //NÃO PAGAS ATÉ HOJE
                TotalizadorRemessaTO naoPagas = new TotalizadorRemessaTO();
                naoPagas.setLabel("Não pagas até hoje");
                naoPagas.setMesReferencia(mesPesquisar);
                nrTentativas = "";
                somenteConvenio = "";
                situacaoParcela = this.incluirContratosCancelados ? " IN ('EA', 'CA') " : " =  'EA'";
                situacaoRemessa = "AND not exists (SELECT ri.movparcela FROM remessa r " +
                        "INNER JOIN remessaitem ri ON r.codigo = ri.remessa " +
                        "AND ri.movparcela = mpa.codigo " +
                        "AND r.situacaoremessa IN (" + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "," + SituacaoRemessaEnum.GERADA.getId() + "))";
                getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(naoPagas, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                        situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, "");
                naoPagas.calcularPorcentagem(enviadas.getValor());

                //AGUARDANDO RETORNO
                TotalizadorRemessaTO aguardandoRetorno = new TotalizadorRemessaTO();
                aguardandoRetorno.setLabel("Aguardando retorno");
                aguardandoRetorno.setMesReferencia(mesPesquisar);
                aguardandoRetorno.setTitle("Aqui é exibido a quantidade de parcelas em remessa que estão aguardando retorno.<br/>Este indicador não tem relação com os indicadores acima e nem com o gráfico abaixo.");
                nrTentativas = "";
                somenteConvenio = "";
                situacaoParcela = "= 'EA'";
                situacaoRemessa = "AND exists (SELECT ri.movparcela FROM remessa r " +
                        "INNER JOIN remessaitem ri ON r.codigo = ri.remessa left  join remessaitemmovparcela rim on rim.remessaitem = ri.codigo  " +
                        " where (ri.movparcela = mpa.codigo or rim.movparcela  = mpa.codigo) " +
                        "AND r.situacaoremessa IN (" + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "," + SituacaoRemessaEnum.GERADA.getId() + "))";
                getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(aguardandoRetorno, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                        situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, "");
                aguardandoRetorno.calcularPorcentagem(enviadas.getValor());

                //Title Pagas pelo convenio
                aceitaConvenio.setTitle("<div class='esquerda' style='width: 200px'>\n" +
                        "<div class='display:block;'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#008000;display:inline-block;'/>" +
                        "<span class='text'>1ª Tentativa - " +
                        "<span class='text' style='font-weight: bold;'> Qtd </span>\n" +
                        "<span class='text' style='font-weight: bold;'> "+ primeiraTentativa.getQuantidade() +"</span>\n" +
                        "<div><span class='text' style='font-weight: bold;'> " + getEmpresaLogado().getMoeda() + " </span>\n" +
                        "<span class='text' style='font-weight: bold;'> "+ primeiraTentativa.getValorApresentar() + "</span></div>\n" +
                        "<div><span class='text' style='font-weight: bold;'></span>\n" +
                        "<span class='text' style='font-weight: bold;'> "+ primeiraTentativa.getPorcentagem_Apresentar() +"% </span></div>\n" +
                        "<div class='display:block;'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#FF6E00;display:inline-block;'/>" +
                        "<span class='text'>2ª Tentativa - " +
                        "<span class='text' style='font-weight: bold;'> Qtd </span>\n" +
                        "<span class='text' style='font-weight: bold;'> "+ segundaTentativa.getQuantidade() +"</span>\n" +
                        "<div><span class='text' style='font-weight: bold;'>" + getEmpresaLogado().getMoeda() + "</span>\n" +
                        "<span class='text' style='font-weight: bold;'> "+ segundaTentativa.getValorApresentar() + "</span></div>\n" +
                        "<div><span class='text' style='font-weight: bold;'></span>\n" +
                        "<span class='text' style='font-weight: bold;'> "+ segundaTentativa.getPorcentagem_Apresentar() +"% </span></div>\n" +
                        "<div class='display:block;'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#F0D817;display:inline-block;'/>" +
                        "<span class='text'>Demais tentativas - " +
                        "<span class='text' style='font-weight: bold;'> Qtd </span>\n" +
                        "<span class='text' style='font-weight: bold;'> "+ outrasTentativas.getQuantidade() +"</span>\n" +
                        "<div><span class='text' style='font-weight: bold;'> " + getEmpresaLogado().getMoeda() + " </span>\n" +
                        "<span class='text' style='font-weight: bold;'> "+ outrasTentativas.getValorApresentar() + "</span></div>\n" +
                        "<div><span class='text' style='font-weight: bold;'></span>\n" +
                        "<span class='text' style='font-weight: bold;'> "+ outrasTentativas.getPorcentagem_Apresentar() +"% </span></div>\n" +
                        "</div>");


                //MONTAR GRAFICO DE INADIMPLENCIA
                //PARA O GRÁFICO SEMPRE DEVE REALIZAR A CONSULTA COM A OPÇÃO DE SOMENTE PARCELAS FORA DO MES FALSA!!
                montarBIInadimplencia(mesPesquisar, dataInicioPesquisar, dataFinalPesquisar, tiposSelecionados);

                //INFORMAÇÕES QUE SERÃO APRESENTADAS -- SOMENTE DO MÊS QUE ESTÁ SENDO FILTRADO
                if (Uteis.getDataMesAnoConcatenado(mesPesquisar).equals(Uteis.getDataMesAnoConcatenado(getDataBaseFiltro()))) {

                    //CONSULTAR INFORMAÇÕES REFERENTE A TRANSAÇÃO
                    totalizarTransacao(false, dataInicioPesquisar, dataFinalPesquisar, tiposSelecionados, enviadas, aceitaConvenio, primeiraTentativa, segundaTentativa, outrasTentativas, foraConvenio, naoPagas);

                    getTotalizador().add(enviadas);
                    getTotalizador().add(aceitaConvenio);
                    getTotalizador().add(foraConvenio);
                    getTotalizador().add(naoPagas);
                    getTotalizadorAguardandoRetorno().add(aguardandoRetorno);
                }
            }
    }

    private void montarBIInadimplencia(Date mesPesquisar, Date dataInicioPesquisar, Date dataFinalPesquisar, List<Integer> tiposSelecionados) throws Exception {
        //PARA O GRÁFICO DO BI DE INADIMPLENCIA
        //REFAZER A CONSULTA SEM A OPÇÃO DE SOMENTE PARCELAS FORA DO MES FALSA!!

        Integer empresa = getEmpresaFiltroBI().getCodigo();
        boolean somenteMes =  true;
        boolean somenteForaMes = false;
        List<Integer> conveniosSelecionados = ColecaoUtils.convertStringToInt(this.listaConveniosSelecionados);

        String nrTentativas = "";
        String somenteConvenio = "";
        String situacaoParcela = "";
        String situacaoRemessa = "";


        //ENVIADAS
        TotalizadorRemessaTO enviadas = new TotalizadorRemessaTO();
        enviadas.setLabel("Enviadas com retorno");
        enviadas.setMesReferencia(mesPesquisar);
        nrTentativas = "";
        somenteConvenio = "";
        situacaoParcela = this.incluirContratosCancelados ? " not in ('RG') " : " not in ('CA','RG') ";
        situacaoRemessa = "AND mpa.codigo not in (SELECT ri.movparcela FROM remessa r " +
                "INNER JOIN remessaitem ri ON r.codigo = ri.remessa " +
                "AND ri.movparcela = mpa.codigo " +
                "AND r.situacaoremessa IN (" + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "," + SituacaoRemessaEnum.GERADA.getId() + "))";
        getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(enviadas, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, "");
        enviadas.calcularPorcentagem(enviadas.getValor());

        //ACEITAS PELO CONVÊNIO
        TotalizadorRemessaTO aceitaConvenio = new TotalizadorRemessaTO();
        aceitaConvenio.setLabel("Pagas pelo convênio");
        aceitaConvenio.setMesReferencia(mesPesquisar);
        nrTentativas = "";
        somenteConvenio = " AND (fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) ";
        situacaoParcela = "= 'PG'";
        situacaoRemessa = "";
        getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(aceitaConvenio, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, "");
        aceitaConvenio.calcularPorcentagem(enviadas.getValor());

        //PAGAS FORA DO CONVÊNIO
        TotalizadorRemessaTO foraConvenio = new TotalizadorRemessaTO();
        foraConvenio.setLabel("Pagas fora do convênio");
        foraConvenio.setMesReferencia(mesPesquisar);
        nrTentativas = "";
        somenteConvenio = " AND fp.conveniocobranca is null AND fp.defaultrecorrencia = false ";
        situacaoParcela = "= 'PG'";
        situacaoRemessa = "";
        getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(foraConvenio, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, "");
        foraConvenio.calcularPorcentagem(enviadas.getValor());

        //NÃO PAGAS ATÉ HOJE
        TotalizadorRemessaTO naoPagas = new TotalizadorRemessaTO();
        naoPagas.setLabel("Não pagas até hoje");
        naoPagas.setMesReferencia(mesPesquisar);
        nrTentativas = "";
        somenteConvenio = "";
        situacaoParcela = this.incluirContratosCancelados ? " IN ('EA', 'CA') " : " =  'EA'";
        situacaoRemessa = "AND mpa.codigo not in (SELECT ri.movparcela FROM remessa r " +
                "INNER JOIN remessaitem ri ON r.codigo = ri.remessa " +
                "AND ri.movparcela = mpa.codigo " +
                "AND r.situacaoremessa IN (" + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "," + SituacaoRemessaEnum.GERADA.getId() + "))";
        getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(naoPagas, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, "");
        naoPagas.calcularPorcentagem(enviadas.getValor());

        //AGUARDANDO RETORNO -------   RETIRADA TICKET #6222
//        TotalizadorRemessaTO aguardandoRetorno = new TotalizadorRemessaTO();
//        aguardandoRetorno.setLabel("Aguardando retorno");
//        aguardandoRetorno.setMesReferencia(mesPesquisar);
//        nrTentativas = "";
//        somenteConvenio = "";
//        situacaoParcela = "= 'EA'";
//        situacaoRemessa = "AND re.situacaoremessa IN (" + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "," + SituacaoRemessaEnum.GERADA.getId() + ")";
//        getFacade().getZWFacade().getRemessaItem().totalizadorBIResultadoDCC(aguardandoRetorno, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
//                situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, conveniosSelecionados, "");
//        aguardandoRetorno.calcularPorcentagem(enviadas.getValor());


        //COLOCAR INFORMAÇÕES PARA O GRAFICO DE INADIMPLENCIA
        //---- Recebidas
        //Pagas pelo Convênio
        //Pagas fora do Convênio
        //
        //---- A receber
        //Aguardando Retorno ---- RETIRADA TICKET #6222
        //Não Pagas até hoje

        totalizarTransacao(true, dataInicioPesquisar, dataFinalPesquisar, tiposSelecionados, enviadas, aceitaConvenio, null, null, null, foraConvenio, naoPagas);

        Double valorPago = aceitaConvenio.getValor() + foraConvenio.getValor();
        Double valorEmAberto = naoPagas.getValor();
        getDadosInadimplencia().add(new BIInadimplenciaTO(Uteis.getDataMesAnoConcatenado(mesPesquisar), BigDecimal.valueOf(valorEmAberto), BigDecimal.valueOf(valorPago), BigDecimal.valueOf(enviadas.getValor())));

    }

    public Date getDataBaseFiltroBI(){
        return (Date)JSFUtilities.getManagedBean("BIControle.dataBaseFiltro");
    }
    public String getDataBase_Apresentar(){
        DateFormat dfmt = new SimpleDateFormat("MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataBaseFiltroBI());
        return dfmt.format(cal.getTime());
    }

    public Integer getTotalContratosAtivosRecorrencia() {
        if (totalContratosAtivosRecorrencia == null) {
            totalContratosAtivosRecorrencia = 0;
        }
        return totalContratosAtivosRecorrencia;
    }

    public void setTotalContratosAtivosRecorrencia(Integer totalContratosAtivosRecorrencia) {
        this.totalContratosAtivosRecorrencia = totalContratosAtivosRecorrencia;
    }

    public Integer getTotalContratosCanceladosRecorrencia() {
        if (totalContratosCanceladosRecorrencia == null) {
            totalContratosCanceladosRecorrencia = 0;
        }
        return totalContratosCanceladosRecorrencia;
    }

    public void setTotalContratosCanceladosRecorrencia(Integer totalContratosCanceladosRecorrencia) {
        this.totalContratosCanceladosRecorrencia = totalContratosCanceladosRecorrencia;
    }

    public Integer getTotalNaoRenovadosRecorrencia() {
        if (totalNaoRenovadosRecorrencia == null) {
            totalNaoRenovadosRecorrencia = 0;
        }
        return totalNaoRenovadosRecorrencia;
    }

    public void setTotalNaoRenovadosRecorrencia(Integer totalNaoRenovadosRecorrencia) {
        this.totalNaoRenovadosRecorrencia = totalNaoRenovadosRecorrencia;
    }

    public Boolean getMostrarPaginacao() {
        return mostrarPaginacao;
    }

    public void setMostrarPaginacao(Boolean mostrarPaginacao) {
        this.mostrarPaginacao = mostrarPaginacao;
    }

    public String getNumeroPaginacao() {
        if (getMostrarPaginacao()) {
            return "10";
        } else {
            return "";
        }
    }

    public String getTituloDetalhamento() {
        return tituloDetalhamento;
    }

    public void setTituloDetalhamento(String tituloDetalhamento) {
        this.tituloDetalhamento = tituloDetalhamento;
    }

    public Integer getContratosRecorrenciaSemCartao() {
        if (contratosRecorrenciaSemCartao == null) {
            contratosRecorrenciaSemCartao = 0;
        }
        return contratosRecorrenciaSemCartao;
    }

    public void setContratosRecorrenciaSemCartao(Integer contratosRecorrenciaSemCartao) {
        this.contratosRecorrenciaSemCartao = contratosRecorrenciaSemCartao;
    }

    public Integer getCartoesCreditosVencidos() {
        if (cartoesCreditosVencidos == null) {
            cartoesCreditosVencidos = 0;
        }
        return cartoesCreditosVencidos;
    }

    public void setCartoesCreditosVencidos(Integer cartoesCreditosVencidos) {
        this.cartoesCreditosVencidos = cartoesCreditosVencidos;
    }

    public List<RecorrenciaClienteTO> getListaApresentarRecorrenciaCliente() {
        return listaApresentarRecorrenciaCliente;
    }

    public void setListaApresentarRecorrenciaCliente(List<RecorrenciaClienteTO> listaApresentarRecorrenciaCliente) {
        this.listaApresentarRecorrenciaCliente = listaApresentarRecorrenciaCliente;
    }

    public boolean isApresentarVencimento() {
        return apresentarVencimento;
    }

    public void setApresentarVencimento(boolean apresentarVencimento) {
        this.apresentarVencimento = apresentarVencimento;
    }

    public List<TotalizadorRemessaTO> getTotalizador() {
        return totalizador;
    }

    public void setTotalizador(List<TotalizadorRemessaTO> totalizador) {
        this.totalizador = totalizador;
    }

    public boolean isSomenteParcelasMes() {
        return somenteParcelasMes;
    }

    public void setSomenteParcelasMes(boolean somenteParcelasMes) {
        this.somenteParcelasMes = somenteParcelasMes;
    }

    public boolean isSomenteParcelasForaMes() {
        return somenteParcelasForaMes;
    }

    public void setSomenteParcelasForaMes(boolean somenteParcelasForaMes) {
        this.somenteParcelasForaMes = somenteParcelasForaMes;
    }

    public Integer getParcelasCanceladas() {
        if (parcelasCanceladas == null) {
            parcelasCanceladas = 0;
        }
        return parcelasCanceladas;
    }

    public void setParcelasCanceladas(Integer parcelasCanceladas) {
        this.parcelasCanceladas = parcelasCanceladas;
    }

    public double getValorParcelasVencidaAbertas() {
        return valorParcelasVencidaAbertas;
    }

    public void setValorParcelasVencidaAbertas(double valorParcelasVencidaAbertas) {
        this.valorParcelasVencidaAbertas = valorParcelasVencidaAbertas;
    }

    public void setOperacoesSuspeitas(Integer operacoesSuspeitas) {
        this.operacoesSuspeitas = operacoesSuspeitas;
    }

    public Integer getOperacoesSuspeitas() {
        if (operacoesSuspeitas == null) {
            operacoesSuspeitas = 0;
        }
        return operacoesSuspeitas;
    }

    public boolean isApresentarParcelas() {
        return apresentarParcelas;
    }

    public void setApresentarParcelas(boolean apresentarParcelas) {
        this.apresentarParcelas = apresentarParcelas;
    }

    public boolean isApresentarSuspeita() {
        return apresentarSuspeita;
    }

    public void setApresentarSuspeita(boolean apresentarSuspeita) {
        this.apresentarSuspeita = apresentarSuspeita;
    }

    public Integer getCartoesCreditosAVencer() {
        if (cartoesCreditosAVencer == null) {
            cartoesCreditosAVencer = 0;
        }
        return cartoesCreditosAVencer;
    }

    public void setCartoesCreditosAVencer(Integer cartoesCreditosAVencer) {
        this.cartoesCreditosAVencer = cartoesCreditosAVencer;
    }

    public Integer getClientesMesmoCartao() {
        if (clientesMesmoCartao == null) {
            clientesMesmoCartao = 0;
        }
        return clientesMesmoCartao;
    }

    public Integer getParcelasVencidaAbertas() {
        return parcelasVencidaAbertas;
    }

    public void setParcelasVencidaAbertas(Integer parcelasVencidaAbertas) {
        this.parcelasVencidaAbertas = parcelasVencidaAbertas;
    }

    public Integer getParcelasEmAberto() {
        return parcelasEmAberto;
    }

    public void setParcelasEmAberto(Integer parcelasEmAberto) {
        this.parcelasEmAberto = parcelasEmAberto;
    }

    public Double getValorParcelasAberto() {
        return valorParcelasAberto;
    }

    public void setValorParcelasAberto(Double valorParcelasAberto) {
        this.valorParcelasAberto = valorParcelasAberto;
    }

    public void setClientesMesmoCartao(Integer clientesMesmoCartao) {
        this.clientesMesmoCartao = clientesMesmoCartao;
    }

    public boolean isApresentarValorParcela() {
        return apresentarValorParcela;
    }

    public void setApresentarValorParcela(boolean apresentarValorParcela) {
        this.apresentarValorParcela = apresentarValorParcela;
    }

    public String getAtributosExportacao() {
	    if(totalizando){
            String atributos = "empresa=Unidade,matricula=Matrícula,nomeCliente=Nome,cpf=CPF,codigoContrato=Contrato,descricaoPlano=Plano,tipo=Tipo,codigoParcela=Cód.Parcela," +
                    "nrParcela=Nr. da parcela,descricaoParcela=Parcela,dataLancamento_Apresentar=DT. Lançamento," +
                    "dataInicio_Apresentar=DT. Início,dataTermino_Apresentar=DT. Término,duracao=Duração," +
                    "dataVencimento_Apresentar=DT. Vencimento,valor_Apresentar=Valor";
            if (this.isApresentarFormaPagamento()) {
                atributos += ",descricaoFormaPagamento=Forma de Pagamento";
            }
            return atributos;
        }
        if (isApresentarParcelas()){
            String n = "";
            if(camposEspeciais){
                n+="empresa=Unidade,tipo=Tipo,nrParcela=Nr. da parcela,";
            }
            if(isApresentarVencimento()){
                n += "matricula=Matrícula,nomeCliente=Nome,codigoContrato=Contrato,codigoParcela=Cód.Parcela,descricaoParcela=Parcela,dataLancamento_Apresentar=DT. Lançamento,dataInicio_Apresentar=DT. Início,dataTermino_Apresentar=DT. Término,duracao=Duração,dataVencimento_Apresentar=DT. Vencimento";
            } else{
                n += "matricula=Matrícula,nomeCliente=Nome,codigoContrato=Contrato,nrParcelasEA=Nr Parc. EA,dataLancamento_Apresentar=DT. Lançamento,dataInicio_Apresentar=DT. Início,dataTermino_Apresentar=DT. Término,duracao=Duração";
            }
            if (isApresentarValorParcela()){
                n+=",valor_Apresentar=Valor";
            }
            return n;
        } else if(isApresentarSuspeita()){
            return "matricula=Matrícula,nomeCliente=Nome,codigoContrato=Contrato,suspeita=Suspeita,dataLancamento_Apresentar=DT. Lançamento,dataInicio_Apresentar=DT. Início,dataTermino_Apresentar=DT. Término,duracao=Duração";
        } 
        return "matricula=Matrícula,nomeCliente=Nome,cpf=CPF,codigoContrato=Contrato,descricaoPlano=Plano,dataLancamento_Apresentar=DT. Lançamento,dataInicio_Apresentar=DT. Início,dataTermino_Apresentar=DT. Término,duracao=Duração";
        
    }

    public List<BiDCCRelVO> getListaBIDCC() {
        return listaBIDCC;
    }

    public void setListaBIDCC(List<BiDCCRelVO> listaBIDCC) {
        this.listaBIDCC = listaBIDCC;
    }

    public boolean isExibirTodos() {
        return exibirTodos;
    }

    public void setExibirTodos(boolean exibirTodos) {
        this.exibirTodos = exibirTodos;
    }

    public List<SelectItem> getConveniosCobranca() {
        return conveniosCobranca;
    }

    public void setConveniosCobranca(List<SelectItem> conveniosCobranca) {
        this.conveniosCobranca = conveniosCobranca;
    }

    public List<String> getListaConveniosSelecionados() {
        return listaConveniosSelecionados;
    }

    public void setListaConveniosSelecionados(List<String> listaConveniosSelecionados) {
        this.listaConveniosSelecionados = listaConveniosSelecionados;
    }

    public boolean isIncluirContratosCancelados() {
        return incluirContratosCancelados;
    }

    public void setIncluirContratosCancelados(boolean incluirContratosCancelados) {
        this.incluirContratosCancelados = incluirContratosCancelados;
    }

    public List<BIInadimplenciaTO> getDadosInadimplencia() {
        return dadosInadimplencia;
    }

    public void setDadosInadimplencia(List<BIInadimplenciaTO> dadosInadimplencia) {
        this.dadosInadimplencia = dadosInadimplencia;
    }

    public String getDadosInadimplenciaJSON(){
        return this.dadosInadimplencia != null ? new JSONArray(this.dadosInadimplencia).toString() : new JSONArray().toString();
    }

    public List<TotalizadorRemessaTO> getTotalizadorAguardandoRetorno() {
        if (totalizadorAguardandoRetorno == null) {
            totalizadorAguardandoRetorno = new ArrayList<TotalizadorRemessaTO>();
        }
        return totalizadorAguardandoRetorno;
    }

    public void setTotalizadorAguardandoRetorno(List<TotalizadorRemessaTO> totalizadorAguardandoRetorno) {
        this.totalizadorAguardandoRetorno = totalizadorAguardandoRetorno;
    }

    private void totalizarTransacao(boolean grafico,
                                    Date dataInicioPesquisar,
                                    Date dataFinalPesquisar,
                                    List<Integer> tiposSelecionados,
                                    TotalizadorRemessaTO enviadas,
                                    TotalizadorRemessaTO aceitaConvenio,
                                    TotalizadorRemessaTO primeiraTentativa,
                                    TotalizadorRemessaTO segundaTentativa,
                                    TotalizadorRemessaTO outrasTentativas,
                                    TotalizadorRemessaTO foraConvenio,
                                    TotalizadorRemessaTO naoPagas) throws Exception {


            boolean somenteMes = isSomenteParcelasMes();
            boolean somenteForaMes = isSomenteParcelasForaMes();
            if (grafico) {
                somenteMes = true;
                somenteForaMes = false;
            }

            Integer empresa = getEmpresaFiltroBI().getCodigo();

            String nrTentativas = "";
            String somenteConvenio = "";
            String situacaoParcela = "";
            String situacaoRemessa = "";

            //ENVIADAS
            nrTentativas = "";
            somenteConvenio = "";
            situacaoParcela = this.incluirContratosCancelados ? " not in ('RG') " : " not in ('CA','RG') ";
            situacaoRemessa = "AND mpa.codigo not in (SELECT ri.movparcela FROM remessa r " +
                    "INNER JOIN remessaitem ri ON r.codigo = ri.remessa " +
                    "AND ri.movparcela = mpa.codigo " +
                    "AND r.situacaoremessa IN (" + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "," + SituacaoRemessaEnum.GERADA.getId() + "))";

            getFacade().getZWFacade().getTransacao().totalizadorBIResultadoDCC(enviadas, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                    situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, tiposSelecionados, "");
            enviadas.calcularPorcentagem(enviadas.getValor());

            //ACEITAS PELO CONVÊNIO
            nrTentativas = "";
            somenteConvenio = " AND (fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) ";
            situacaoParcela = "= 'PG'";
            situacaoRemessa = "AND mpa.codigo not in (SELECT ri.movparcela FROM remessa r " +
                    "INNER JOIN remessaitem ri ON r.codigo = ri.remessa " +
                    "AND ri.movparcela = mpa.codigo " +
                    "AND r.situacaoremessa IN (" + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "," + SituacaoRemessaEnum.GERADA.getId() + "))";
            getFacade().getZWFacade().getTransacao().totalizadorBIResultadoDCC(aceitaConvenio, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                    situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, tiposSelecionados, "");
            aceitaConvenio.calcularPorcentagem(enviadas.getValor());

            //PAGAS NA PRIMEIRA TENTATIVA
            if (primeiraTentativa != null) {
                nrTentativas = " > 0 ";
                somenteConvenio = " AND (fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) ";
                situacaoParcela = "= 'PG'";
                situacaoRemessa = "";
                getFacade().getZWFacade().getTransacao().totalizadorBIResultadoDCC(primeiraTentativa, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                        situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, tiposSelecionados, " = 1 ");
                primeiraTentativa.calcularPorcentagem(enviadas.getValor());
            }

            //PAGAS NA SEGUNDA TENTATIVA
            if (segundaTentativa != null) {
                nrTentativas = " > 0 ";
                somenteConvenio = " AND (fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) ";
                situacaoParcela = "= 'PG'";
                situacaoRemessa = "";
                getFacade().getZWFacade().getTransacao().totalizadorBIResultadoDCC(segundaTentativa, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                        situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, tiposSelecionados, " = 2 ");
                segundaTentativa.calcularPorcentagem(enviadas.getValor());
            }

            //DEMAIS TENTATIVAS
            if (outrasTentativas != null) {
                nrTentativas = " > 0 ";
                somenteConvenio = " AND (fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) ";
                situacaoParcela = "= 'PG'";
                situacaoRemessa = "";
                getFacade().getZWFacade().getTransacao().totalizadorBIResultadoDCC(outrasTentativas, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                        situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, tiposSelecionados, " > 2 ");
                outrasTentativas.calcularPorcentagem(enviadas.getValor());
            }

            //PAGAS FORA DO CONVÊNIO
            nrTentativas = "";
            somenteConvenio = " AND fp.conveniocobranca is null AND fp.defaultrecorrencia = false ";
            situacaoParcela = "= 'PG'";
            situacaoRemessa = "";
            getFacade().getZWFacade().getTransacao().totalizadorBIResultadoDCC(foraConvenio, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                    situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, tiposSelecionados, "");
            foraConvenio.calcularPorcentagem(enviadas.getValor());

            //NÃO PAGAS ATÉ HOJE
            nrTentativas = "";
            somenteConvenio = "";
            situacaoParcela = this.incluirContratosCancelados ? " IN ('EA', 'CA') " : " =  'EA'";
            situacaoRemessa = "AND mpa.codigo not in (SELECT ri.movparcela FROM remessa r " +
                    "INNER JOIN remessaitem ri ON r.codigo = ri.remessa " +
                    "AND ri.movparcela = mpa.codigo " +
                    "AND r.situacaoremessa IN (" + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + "," + SituacaoRemessaEnum.GERADA.getId() + "))";
            getFacade().getZWFacade().getTransacao().totalizadorBIResultadoDCC(naoPagas, empresa, dataInicioPesquisar, dataFinalPesquisar, nrTentativas,
                    situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes, tiposSelecionados, "");
            naoPagas.calcularPorcentagem(enviadas.getValor());

            //Title Pagas pelo convenio
            if (!grafico && primeiraTentativa != null && segundaTentativa != null && outrasTentativas != null) {
                aceitaConvenio.setTitle("<div class='esquerda' style='width: 200px'>\n" +
                        "<div class='display:block;'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#008000;display:inline-block;'/>" +
                        "<span class='text'>1ª Tentativa - " +
                        "<span class='text' style='font-weight: bold;'> Qtd </span>\n" +
                        "<span class='text' style='font-weight: bold;'> " + primeiraTentativa.getQuantidade() + "</span>\n" +
                        "<div><span class='text' style='font-weight: bold;'> " + getEmpresaLogado().getMoeda() + " </span>\n" +
                        "<span class='text' style='font-weight: bold;'> " + primeiraTentativa.getValorApresentar() + "</span></div>\n" +
                        "<div><span class='text' style='font-weight: bold;'></span>\n" +
                        "<span class='text' style='font-weight: bold;'> " + primeiraTentativa.getPorcentagem_Apresentar() + "% </span></div>\n" +
                        "<div class='display:block;'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#FF6E00;display:inline-block;'/>" +
                        "<span class='text'>2ª Tentativa - " +
                        "<span class='text' style='font-weight: bold;'> Qtd </span>\n" +
                        "<span class='text' style='font-weight: bold;'> " + segundaTentativa.getQuantidade() + "</span>\n" +
                        "<div><span class='text' style='font-weight: bold;'> " + getEmpresaLogado().getMoeda() + " </span>\n" +
                        "<span class='text' style='font-weight: bold;'> " + segundaTentativa.getValorApresentar() + "</span></div>\n" +
                        "<div><span class='text' style='font-weight: bold;'></span>\n" +
                        "<span class='text' style='font-weight: bold;'> " + segundaTentativa.getPorcentagem_Apresentar() + "% </span></div>\n" +
                        "<div class='display:block;'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#F0D817;display:inline-block;'/>" +
                        "<span class='text'>Demais tentativas - " +
                        "<span class='text' style='font-weight: bold;'> Qtd </span>\n" +
                        "<span class='text' style='font-weight: bold;'> " + outrasTentativas.getQuantidade() + "</span>\n" +
                        "<div><span class='text' style='font-weight: bold;'> " + getEmpresaLogado().getMoeda() + " </span>\n" +
                        "<span class='text' style='font-weight: bold;'> " + outrasTentativas.getValorApresentar() + "</span></div>\n" +
                        "<div><span class='text' style='font-weight: bold;'></span>\n" +
                        "<span class='text' style='font-weight: bold;'> " + outrasTentativas.getPorcentagem_Apresentar() + "% </span></div>\n" +
                        "</div>");
            }
    }

    public boolean isMarcarTodos() {
        return marcarTodos;
    }

    public void setMarcarTodos(boolean marcarTodos) {
        this.marcarTodos = marcarTodos;
    }

    public boolean isTotalizando() {
        return totalizando;
    }

    public void setTotalizando(boolean totalizando) {
        this.totalizando = totalizando;
    }

    public boolean isCamposEspeciais() {
        return camposEspeciais;
    }

    public void setCamposEspeciais(boolean camposEspeciais) {
        this.camposEspeciais = camposEspeciais;
    }

    private FiltroDTO getFiltroDTO(boolean atualizarAgora) {
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.DCC.name());
        JSONObject filtros = new JSONObject();
        filtros.put("atualizarAgora", atualizarAgora);
        if (getDataBaseFiltro() != null) {
            filtros.put("dataBase", Calendario.getDataComHoraZerada(getDataBaseFiltro()).getTime());
        } else {
            filtros.put("dataBase", Calendario.getDataComHoraZerada(getDataBaseFiltroBI()).getTime());
        }
        filtros.put("somenteParcelasMes", isSomenteParcelasMes());
        filtros.put("incluirContratosCancelados", isIncluirContratosCancelados());
        filtros.put("somenteParcelasForaMes", isSomenteParcelasForaMes());

        List<Integer> empresas = new ArrayList<>();
        if (!UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo())) {
            empresas.add(getEmpresaFiltroBI().getCodigo());
        }
        filtros.put("empresas", empresas);

        List<Integer> colaboradores = new ArrayList<>();
        filtros.put("colaboradores", colaboradores);

        List<Integer> convenios = new ArrayList<>();
        if (!UteisValidacao.emptyList(getListaConveniosSelecionados())) {
            for (String cod : getListaConveniosSelecionados()) {
                convenios.add(Integer.parseInt(cod));
            }
        }
        filtros.put("convenios", convenios);

        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    public boolean isApresentarFormaPagamento() {
        return apresentarFormaPagamento;
    }

    public void setApresentarFormaPagamento(boolean apresentarFormaPagamento) {
        this.apresentarFormaPagamento = apresentarFormaPagamento;
    }

    public String getItemExportacaoSelecionado() {
        return itemExportacaoSelecionado;
    }

    public void setItemExportacaoSelecionado(String itemExportacaoSelecionado) {
        this.itemExportacaoSelecionado = itemExportacaoSelecionado;
    }
}

package relatorio.controle.sad;

import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import relatorio.negocio.comuns.basico.ProbabilidadeEvasaoVO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ProbabilidadeEvasao extends SuperEntidade {

    protected static String idEntidade;

    public ProbabilidadeEvasao() throws Exception {
        super();
        setIdEntidade("ProbabilidadeEvasao");
    }

    public ProbabilidadeEvasao(Connection con) throws Exception {
        super(con);
        setIdEntidade("ProbabilidadeEvasao");
    }

    private String colaboradorFiltroBI(List<ColaboradorVO> listaColaboradorFiltroBI) {
        StringBuilder codigo = new StringBuilder();
        for (ColaboradorVO colaborador : listaColaboradorFiltroBI) {
            codigo.append(colaborador.getCodigo()).append(",");
        }

        String codigoIn = codigo.toString().substring(0, codigo.length() - 1);

        return codigoIn;

    }

    public int consultarQtdAlunos(int valorSlider, int empresa, List<ColaboradorVO> listaColaboradorFiltroBI) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT\n")
                .append("COUNT(rev.codigo) qtdAlunos\n")
                .append("FROM riscoevasao rev\n")
                .append("INNER JOIN cliente cli\n")
                .append("ON cli.codigo = rev.cliente\n");

        if (listaColaboradorFiltroBI.size() > 0) {
            String colaboradorIn = colaboradorFiltroBI(listaColaboradorFiltroBI);
            sql.append("INNER JOIN vinculo vin_cc3\n")
                    .append("ON vin_cc3.cliente = rev.cliente\n")
                    .append("AND vin_cc3.tipovinculo = 'CO'\n")
                    .append("AND vin_cc3.colaborador\n")
                    .append("IN (").append(colaboradorIn).append(")\n");
        }

        sql.append("WHERE rev.chancesair30dias >= ").append(valorSlider).append("\n")
                .append("AND cli.empresa = ").append(empresa).append("\n")
                .append("AND cli.situacao = 'AT'").append("\n");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        rs.next();

        return rs.getInt("qtdAlunos");
    }

    public List<ProbabilidadeEvasaoVO> consultarAlunos(int valorSlider, int empresa, List<ColaboradorVO> listaColaboradorFiltroBI) throws SQLException {
        return consultarAlunos(valorSlider, null, null, empresa, listaColaboradorFiltroBI);
    }

    public List<ProbabilidadeEvasaoVO> consultarAlunos(int valorSlider, String filtrarNome, int empresa, List<ColaboradorVO> listaColaboradorFiltroBI) throws SQLException {
        return consultarAlunos(valorSlider, filtrarNome, null, empresa, listaColaboradorFiltroBI);
    }

    public List<ProbabilidadeEvasaoVO> consultarAlunos(int valorSlider, String filtrarNome, Integer filtrarCodigo, int empresa, List<ColaboradorVO> listaColaboradorFiltroBI) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT\n")
                .append("rev.codigo, rev.cliente, rev.datapredicao, rev.datapredicao30dias, pes.nome, rev.chancesair30dias,\n")
                .append("mdi.dataenvio, mdi.datacriacao, mdi.vigenteate\n")
                .append("FROM riscoevasao rev\n")
                .append("INNER JOIN cliente cli\n")
                .append("ON cli.codigo = rev.cliente\n")
                .append("INNER JOIN pessoa pes\n")
                .append("ON cli.pessoa = pes.codigo\n")
                .append("LEFT JOIN maladireta mdi\n")
                .append("ON mdi.codigo = \n")
                .append("(SELECT mec.maladireta\n")
                .append("FROM maladiretacrmextracliente mec\n")
                .append("INNER JOIN maladireta mdi\n")
                .append("ON mec.maladireta = mdi.codigo\n")
                .append("WHERE mec.cliente = rev.cliente\n")
                .append("ORDER BY mdi.datacriacao DESC\n")
                .append("limit 1)\n");

        if (listaColaboradorFiltroBI.size() > 0) {
            String colaboradorIn = colaboradorFiltroBI(listaColaboradorFiltroBI);
            sql.append("INNER JOIN vinculo vin_cc1\n")
                    .append("ON vin_cc1.cliente = rev.cliente\n")
                    .append("AND vin_cc1.tipovinculo = 'CO'\n")
                    .append("AND vin_cc1.colaborador\n")
                    .append("IN (").append(colaboradorIn).append(")\n");
        }

        sql.append("INNER JOIN vinculo vin_cc2\n")
                .append("ON vin_cc2.cliente = rev.cliente\n")
                .append("AND vin_cc2.tipovinculo = 'CO'\n")
                .append("WHERE rev.chancesair30dias >= ").append(valorSlider).append("\n")
                .append("AND cli.empresa = ").append(empresa).append("\n")
                .append("AND cli.situacao = 'AT'").append("\n");

        if (!UteisValidacao.emptyString(filtrarNome)) {
            sql.append("AND pes.nome ilike '%").append(filtrarNome).append("%'\n");
        }

        if (!UteisValidacao.emptyNumber(filtrarCodigo)) {
            sql.append("AND cli.codigo=").append(filtrarCodigo).append("\n");
        }

        sql.append("ORDER BY rev.chancesair30dias, pes.nome");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();

        List<ProbabilidadeEvasaoVO> listProbabilidadeEvasaoVO = new ArrayList<>();
        ProbabilidadeEvasaoVO probabilidadeEvasaoVO;

        while (rs.next()) {
            probabilidadeEvasaoVO = new ProbabilidadeEvasaoVO();
            probabilidadeEvasaoVO.setCodigo(rs.getInt("codigo"));
            probabilidadeEvasaoVO.setCliente(rs.getInt("cliente"));
            probabilidadeEvasaoVO.setDataPredicao(rs.getDate("datapredicao"));
            probabilidadeEvasaoVO.setDataPredicao30Dias(rs.getDate("datapredicao30dias"));
            probabilidadeEvasaoVO.setNome(rs.getString("nome"));
            probabilidadeEvasaoVO.setChancesair30dias(rs.getFloat("chancesair30dias"));
            probabilidadeEvasaoVO.setDataEnvio(rs.getDate("dataenvio"));
            probabilidadeEvasaoVO.setDataCriacao(rs.getDate("datacriacao"));
            probabilidadeEvasaoVO.setVigenteAte(rs.getDate("vigenteate"));

            listProbabilidadeEvasaoVO.add(probabilidadeEvasaoVO);
        }

        return listProbabilidadeEvasaoVO;
    }
}
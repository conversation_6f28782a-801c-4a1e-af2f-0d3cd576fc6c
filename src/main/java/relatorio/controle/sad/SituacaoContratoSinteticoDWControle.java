/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.sad;

import controle.arquitetura.SuperControle;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import javax.faces.model.SelectItem;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.plano.Plano;
import negocio.interfaces.basico.ColaboradorInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.plano.PlanoInterfaceFacade;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;

import relatorio.negocio.comuns.sad.SinteticoColaboradorVO;
import relatorio.negocio.comuns.sad.SinteticoPlanoVO;
import relatorio.negocio.comuns.sad.SinteticoSituacaoVO;
import relatorio.negocio.comuns.sad.SituacaoContratoAnaliticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoContratoSinteticoDWVO;
import relatorio.negocio.jdbc.sad.SituacaoContratoAnaliticoDW;
import relatorio.negocio.jdbc.sad.SituacaoContratoSinteticoDW;

/**
 * 
 * <AUTHOR>
 */
public class SituacaoContratoSinteticoDWControle extends SuperControle {

    protected SituacaoContratoSinteticoDWVO situacaoContratoSinteticoDW;
    private DefaultPieDataset dataSetPizza;
    private DefaultCategoryDataset dataSetBarra;
    private Boolean pizza;
    private Boolean barra;
    private String filtros;
    protected List<SituacaoContratoSinteticoDWVO> selectListaClientes;
    protected List<SituacaoContratoSinteticoDWVO> selectListaClientesRelatorio;
    protected List<SituacaoContratoAnaliticoDWVO> selectListaClienteAnalitico;
    protected List<SinteticoSituacaoVO> listaSituacao;
    protected List<SinteticoPlanoVO> listaPlano;
    protected List<SinteticoColaboradorVO> listaVinculoCarteira;
    protected Date dataFinal;
    private List listaSelectItemEmpresa;
    protected Boolean apresentarRelatorio;
    protected EmpresaInterfaceFacade empresaFacade = null;
    protected PlanoInterfaceFacade planoFacade = null;
    protected ColaboradorInterfaceFacade colaboradorFacade = null;

    public SituacaoContratoSinteticoDWControle() {
        inicializarFacades();
        montarListaSelectItemEmpresa();
        setSituacaoContratoSinteticoDW(new SituacaoContratoSinteticoDWVO());
        setMensagemID("msg_entre_prmrelatorio");
        setSelectListaClientes(new ArrayList());
    }

    public void montarListaSelectItemEmpresa() {
        try {
            List objs = new ArrayList();
            objs.add(new SelectItem(new Integer(0), ""));
            List resultadoConsulta = consultarPorNomeEmpresa("");
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
            setListaSelectItemEmpresa(objs);
        } catch (Exception e) {
            setListaSelectItemEmpresa(new ArrayList());
            e.printStackTrace();
        }
    }

    public List consultarPorNomeEmpresa(String nomePrm) throws Exception {
        List lista = empresaFacade.consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public void inicializarUsuarioLogado() {
        try {
            getSituacaoContratoSinteticoDW().setUsuarioVO(new UsuarioVO());
            getSituacaoContratoSinteticoDW().getUsuarioVO().setCodigo(getUsuarioLogado().getCodigo().intValue());
            getSituacaoContratoSinteticoDW().getUsuarioVO().setUsername(getUsuarioLogado().getUsername());
            getSituacaoContratoSinteticoDW().getUsuarioVO().setAdministrador(getUsuarioLogado().getAdministrador());
        } catch (Exception exception) {
        }
    }

    public void inicializarEmpresaLogado() {
        try {
            if (getEmpresaLogado().getCodigo().intValue() != 0) {
                getSituacaoContratoSinteticoDW().getEmpresa().setCodigo(getEmpresaLogado().getCodigo().intValue());
                getSituacaoContratoSinteticoDW().getEmpresa().setNome(getEmpresaLogado().getNome());
            }
        } catch (Exception exception) {
        }
    }

    private String getEmpresaLista() throws Exception {
        getSituacaoContratoSinteticoDW().setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getSituacaoContratoSinteticoDW().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        return getSituacaoContratoSinteticoDW().getEmpresa().getNome();
    }

    public void obterEmpresaEscolhida() throws Exception {
        if (getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue() != 0) {
            getSituacaoContratoSinteticoDW().setEmpresa(empresaFacade.consultarPorChavePrimaria(getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        } else {
            getSituacaoContratoSinteticoDW().setEmpresa(new EmpresaVO());
        }
    }

    public String novo() {
        try {
            setSituacaoContratoSinteticoDW(new SituacaoContratoSinteticoDWVO());
            setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
            setSelectListaClientes(new ArrayList<SituacaoContratoSinteticoDWVO>());
            setSelectListaClientesRelatorio(new ArrayList<SituacaoContratoSinteticoDWVO>());
            setListaPlano(new ArrayList<SinteticoPlanoVO>());
            setListaSituacao(new ArrayList<SinteticoSituacaoVO>());
            setListaVinculoCarteira(new ArrayList<SinteticoColaboradorVO>());
            setBarra(false);
            setPizza(false);
            setDataSetBarra(new DefaultCategoryDataset());
            setDataSetPizza(new DefaultPieDataset());
            setApresentarRelatorio(new Boolean(false));
            limparListaClientes();
            inicializarUsuarioLogado();
            inicializarEmpresaLogado();
            setDataFinal(negocio.comuns.utilitarias.Calendario.hoje());
            if (getUsuarioLogado().getAdministrador() && getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().equals(0)) {
                return "clienteSintetico";
            }
            montarGraficoDefault();
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "clienteSintetico";

    }

    @Override
    protected boolean inicializarFacades() {
        try {
            empresaFacade = new Empresa();
            planoFacade = new Plano();
            colaboradorFacade = new Colaborador();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public void consultarClientesPorFiltros() throws Exception {
        try {
            if (getUsuarioLogado().getAdministrador() && getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().equals(0)) {
                setPizza(false);
                setBarra(false);
                throw new Exception("Informe a Empresa.");
            }
            if (getDataFinal().after(negocio.comuns.utilitarias.Calendario.hoje())) {
                setPizza(false);
                setBarra(false);
                throw new Exception("O Relatório só pode ser gerado com a data anterior ou igual ao dia atual.");
            }

            setPizza(true);
            setBarra(false);
            setDataSetBarra(new DefaultCategoryDataset());
            setDataSetPizza(new DefaultPieDataset());
            ResultSet lista;
            lista = new SituacaoContratoSinteticoDW().consultarQuantidadeContratosSinteticosDefault(getDataFinal(), getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            setListaPlano(new ArrayList<SinteticoPlanoVO>());
            setListaSituacao(new ArrayList<SinteticoSituacaoVO>());
            setListaVinculoCarteira(new ArrayList<SinteticoColaboradorVO>());
            inicializarListaCliente(lista);
            montarDatasetBarra();
            montarDatasetPizza();
            if (getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue() != 0
                    && (getEmpresaLogado().getCodigo().intValue()==0)) {
                setFiltros("<b>Empresa: </b>" + getEmpresaLista()+ " <b>Dia:</b> " + Uteis.getData(dataFinal) + "</br>");
            }
            else if (getEmpresaLogado().getCodigo().intValue() != 0) {
                setFiltros("<b>Dia: </b> " + Uteis.getData(dataFinal) + "</br>");
            }
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void montarGraficoDefault() throws Exception {
        try {
            setPizza(true);
            setBarra(false);
            setDataSetBarra(new DefaultCategoryDataset());
            setDataSetPizza(new DefaultPieDataset());
            setDataFinal(negocio.comuns.utilitarias.Calendario.hoje());
            montarDadosTableDefault(getDataFinal());
            if (getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue() != 0
                    && (getEmpresaLogado().getCodigo().intValue()==0)) {
                setFiltros("<b>Empresa: </b>" + getEmpresaLista()+ " <b>Dia:</b> " + Uteis.getData(dataFinal) + "</br>");
            }
            else if (getEmpresaLogado().getCodigo().intValue() != 0) {
                setFiltros("<b>Dia: </b> " + Uteis.getData(dataFinal) + "</br>");
            }
            
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarDadosTableDefault(Date dia) throws Exception {
        try {
            ResultSet lista;
            lista = new SituacaoContratoSinteticoDW().consultarQuantidadeContratosSinteticosDefault(dia, getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            inicializarListaCliente(lista);
            montarDatasetBarra();
            montarDatasetPizza();
        } catch (Exception e) {
            throw e;
        }
    }

    public Integer getNrColunaVinculoCarteira() {
        Integer x = getListaVinculoCarteira().size() / 5;
        if (x == 0) {
            return 1;
        }
        return x;

    }

    public Integer getTamanhoListaVinculoCarteira() {
        Integer x = getListaVinculoCarteira().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public Integer getNrColunaSituacao() {
        Integer x = getListaSituacao().size() / 3;
        if (x == 0) {
            return 1;
        }
        return x;

    }

    public Integer getTamanhoListaSituacao() {
        Integer x = getListaSituacao().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public Integer getNrColunaPlano() {
        Integer x = getListaPlano().size() / 5;
        if (x == 0) {
            return 1;
        }
        return x;

    }

    public Integer getTamanhoListaPlano() {
        Integer x = getListaPlano().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public void limparListaClientes() {
        getSituacaoContratoSinteticoDW().setQtdClientesAtivos(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosTrancados(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosTrancadosVencidos(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosAvencer(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosVencido(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosAtestado(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosCarencia(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantes(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantesAulaAvulsa(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantesDiaria(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantesFreePass(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesInativos(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesInativosCancelados(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesInativosDesistencia(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosNormal(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesTotal(new Integer(0));

    }

    public void inicializarListaCliente(ResultSet lista) throws SQLException, Exception {
        setSelectListaClientes(new ArrayList<SituacaoContratoSinteticoDWVO>());
        getSituacaoContratoSinteticoDW().setQtdClientesAtivos(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosTrancados(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosTrancadosVencidos(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosAvencer(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosVencido(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosAtestado(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosCarencia(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantes(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantesAulaAvulsa(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantesDiaria(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantesFreePass(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesInativos(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesInativosCancelados(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesInativosDesistencia(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosNormal(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesTotal(new Integer(0));

        while (lista.next()) {
            SituacaoContratoSinteticoDWVO obj = new SituacaoContratoSinteticoDWVO();
            obj.setSituacao(lista.getString("Situacao"));
            obj.setPeso(lista.getInt("peso"));
            obj.setVinculoCarteira(lista.getString("vinculoCarteira"));
            obj.setPlano(lista.getString("Plano"));
            if (obj.getPeso() > -1) {
                SinteticoSituacaoVO sin = new SinteticoSituacaoVO();
                sin.setNome(obj.getSituacao());
                sin.adicionarObjClienteSituacaoVOs(getListaSituacao(), sin);
                if (!obj.getVinculoCarteira().equals("")) {
                    inicializarFiltrosSinteticoVinculoCarteira(obj);
                }
                if (!obj.getPlano().equals("")) {
                    inicializarFiltrosSinteticoPlano(obj);
                }
            } else {
                SinteticoSituacaoVO.excluirObjClienteSituacaoVOs(getListaSituacao(), obj.getSituacao());
            }
            if (obj.getSituacao().equals("VI")) {
                getSituacaoContratoSinteticoDW().setQtdClientesVisitantes(getSituacaoContratoSinteticoDW().getQtdClientesVisitantes() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("AT")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivos(getSituacaoContratoSinteticoDW().getQtdClientesAtivos() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("NO")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosNormal(getSituacaoContratoSinteticoDW().getQtdClientesAtivosNormal() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("IN")) {
                getSituacaoContratoSinteticoDW().setQtdClientesInativos(getSituacaoContratoSinteticoDW().getQtdClientesInativos() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("PL")) {
                getSituacaoContratoSinteticoDW().setQtdClientesVisitantesFreePass(getSituacaoContratoSinteticoDW().getQtdClientesVisitantesFreePass() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("AA")) {
                getSituacaoContratoSinteticoDW().setQtdClientesVisitantesAulaAvulsa(getSituacaoContratoSinteticoDW().getQtdClientesVisitantesAulaAvulsa() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("DI")) {
                getSituacaoContratoSinteticoDW().setQtdClientesVisitantesDiaria(getSituacaoContratoSinteticoDW().getQtdClientesVisitantesDiaria() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("TR")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosTrancados(getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancados() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("CA")) {
                getSituacaoContratoSinteticoDW().setQtdClientesInativosCancelados(getSituacaoContratoSinteticoDW().getQtdClientesInativosCancelados() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("DE")) {
                getSituacaoContratoSinteticoDW().setQtdClientesInativosDesistencia(getSituacaoContratoSinteticoDW().getQtdClientesInativosDesistencia() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("TV")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosTrancadosVencidos(getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancadosVencidos() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("AV")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosAvencer(getSituacaoContratoSinteticoDW().getQtdClientesAtivosAvencer() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("VE")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosVencido(getSituacaoContratoSinteticoDW().getQtdClientesAtivosVencido() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("AE")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosAtestado(getSituacaoContratoSinteticoDW().getQtdClientesAtivosAtestado() + lista.getInt("peso"));
            } else if (obj.getSituacao().equals("CR")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosCarencia(getSituacaoContratoSinteticoDW().getQtdClientesAtivosCarencia() + lista.getInt("peso"));
            }

            getSituacaoContratoSinteticoDW().setQtdClientesTotal(getSituacaoContratoSinteticoDW().getQtdClientesTotal() + lista.getInt("peso"));
            getSelectListaClientes().add(obj);
            getSelectListaClientesRelatorio().add(obj);
        }
    }

    public void inicializarFiltrosSinteticoPlano(SituacaoContratoSinteticoDWVO obj) throws Exception {
        try {
            Integer plano = Integer.parseInt(obj.getPlano());
            PlanoVO p = new Plano().consultarPorChavePrimaria(plano, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            SinteticoPlanoVO sin = new SinteticoPlanoVO();
            sin.setNome(p.getDescricao());
            sin.setChavePrimaria(p.getCodigo());
            sin.adicionarObjClienteSituacaoVOs(getListaPlano(), sin);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarFiltrosSinteticoVinculoCarteira(SituacaoContratoSinteticoDWVO obj) throws Exception {
        try {
            String[] listaVinculo = obj.getVinculoCarteira().split(":");
            for (String vi : listaVinculo) {
                Integer codigoVI = Integer.parseInt(vi);
                SinteticoColaboradorVO sin = new SinteticoColaboradorVO();
                ColaboradorVO co = colaboradorFacade.consultarPorChavePrimaria(codigoVI, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                sin.setNome(co.getPessoa().getNome());
                sin.setChavePrimaria(codigoVI);
                sin.adicionarObjClienteSituacaoVOs(getListaVinculoCarteira(), sin);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void montarDatasetBarra() throws Exception {
        try {
            boolean visitante = true;
            boolean freepass = true;
            boolean trancado = true;
            boolean ativo = true;
            boolean normal = true;
            boolean atestado = true;
            boolean carencia = true;
            boolean inativo = true;
            boolean ativoVencido = true;
            boolean ativoAVencer = true;
            boolean cancelados = true;
            boolean desistentes = true;
            boolean diaria = true;
            boolean aulaAvulsa = true;
            boolean trancadoVencidos = true;

            getDataSetBarra().clear();

            for (SituacaoContratoSinteticoDWVO obj : getSelectListaClientesRelatorio()) {
                if (obj.getSituacao().equals("VI") && visitante) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesVisitantes().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesVisitantes(), obj.getSituacao_Apresentar(), "");
                    }
                    visitante = false;
                    continue;
                }

                if (obj.getSituacao().equals("AT") && ativo) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivos().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesAtivos(), obj.getSituacao_Apresentar(), "");
                    }
                    ativo = false;
                    continue;
                }

                if (obj.getSituacao().equals("NO") && normal) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosNormal().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesAtivosNormal(), obj.getSituacao_Apresentar(), "");
                    }
                    normal = false;
                    continue;
                }

                if (obj.getSituacao().equals("IN") && inativo) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesInativos().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesInativos(), obj.getSituacao_Apresentar(), "");
                    }
                    inativo = false;
                    continue;
                }

                if (obj.getSituacao().equals("PL") && freepass) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesVisitantesFreePass().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesVisitantesFreePass(), obj.getSituacao_Apresentar(), "");
                    }
                    freepass = false;
                    continue;
                }

                if (obj.getSituacao().equals("AV") && ativoAVencer) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosAvencer().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesAtivosAvencer(), obj.getSituacao_Apresentar(), "");

                    }
                    ativoAVencer = false;
                    continue;
                }

                if (obj.getSituacao().equals("VE") && ativoVencido) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosVencido().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesAtivosVencido(), obj.getSituacao_Apresentar(), "");
                    }
                    ativoVencido = false;
                    continue;
                }

                if (obj.getSituacao().equals("CA") && cancelados) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesInativosCancelados().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesInativosCancelados(), obj.getSituacao_Apresentar(), "");
                    }
                    cancelados = false;
                    continue;

                }

                if (obj.getSituacao().equals("DE") && desistentes) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesInativosDesistencia().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesInativosDesistencia(), obj.getSituacao_Apresentar(), "");
                    }
                    desistentes = false;
                    continue;
                }

                if (obj.getSituacao().equals("DI") && diaria) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesVisitantesDiaria().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesVisitantesDiaria(), obj.getSituacao_Apresentar(), "");

                    }
                    diaria = false;
                    continue;
                }

                if (obj.getSituacao().equals("AA") && aulaAvulsa) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesVisitantesAulaAvulsa().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesVisitantesAulaAvulsa(), obj.getSituacao_Apresentar(), "");
                    }
                    aulaAvulsa = false;
                    continue;
                }

                if (obj.getSituacao().equals("TR") && trancado) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancados().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancados(), obj.getSituacao_Apresentar(), "");
                    }
                    trancado = false;
                    continue;
                }

                if (obj.getSituacao().equals("TV") && trancadoVencidos) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancadosVencidos().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancadosVencidos(), obj.getSituacao_Apresentar(), "");
                    }
                    trancadoVencidos = false;
                    continue;
                }
                if (obj.getSituacao().equals("AE") && atestado) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosAtestado().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesAtivosAtestado(), obj.getSituacao_Apresentar(), "");
                    }
                    atestado = false;
                    continue;
                }
                if (obj.getSituacao().equals("CR") && carencia) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosCarencia().intValue() != 0) {
                        getDataSetBarra().addValue(getSituacaoContratoSinteticoDW().getQtdClientesAtivosCarencia(), obj.getSituacao_Apresentar(), "");
                    }
                    carencia = false;
                    continue;
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void montarDatasetPizza() throws Exception {
        try {
            boolean visitante = true;
            boolean freepass = true;
            boolean trancado = true;
            boolean ativo = true;
            boolean normal = true;
            boolean atestado = true;
            boolean carencia = true;
            boolean inativo = true;
            boolean ativoVencido = true;
            boolean ativoAVencer = true;
            boolean cancelados = true;
            boolean desistentes = true;
            boolean diaria = true;
            boolean aulaAvulsa = true;
            boolean trancadoVencidos = true;
            setDataSetPizza(new DefaultPieDataset());

            for (SituacaoContratoSinteticoDWVO situacao : getSelectListaClientesRelatorio()) {
                if (situacao.getSituacao().equals("VI") && visitante) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesVisitantes().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesVisitantes());

                    }
                    visitante = false;
                    continue;
                }

                if (situacao.getSituacao().equals("AT") && ativo) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivos().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesAtivos());
                    }
                    ativo = false;
                    continue;
                }

                if (situacao.getSituacao().equals("NO") && normal) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosNormal().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesAtivosNormal());
                    }
                    normal = false;
                    continue;
                }

                if (situacao.getSituacao().equals("IN") && inativo) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesInativos().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesInativos());
                    }
                    inativo = false;
                    continue;
                }

                if (situacao.getSituacao().equals("PL") && freepass) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesVisitantesFreePass().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesVisitantesFreePass());
                    }
                    freepass = false;
                    continue;
                }

                if (situacao.getSituacao().equals("AV") && ativoAVencer) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosAvencer().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesAtivosAvencer());
                    }
                    ativoAVencer = false;
                    continue;
                }

                if (situacao.getSituacao().equals("VE") && ativoVencido) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosVencido().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesAtivosVencido());
                    }
                    ativoVencido = false;
                    continue;
                }

                if (situacao.getSituacao().equals("CA") && cancelados) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesInativosCancelados().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesInativosCancelados());
                    }
                    cancelados = false;
                    continue;
                }

                if (situacao.getSituacao().equals("DE") && desistentes) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesInativosDesistencia().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesInativosDesistencia());
                    }
                    desistentes = false;
                    continue;
                }

                if (situacao.getSituacao().equals("DI") && diaria) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesVisitantesDiaria().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesVisitantesDiaria());
                    }
                    diaria = false;
                    continue;
                }

                if (situacao.getSituacao().equals("AA") && aulaAvulsa) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesVisitantesAulaAvulsa().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesVisitantesAulaAvulsa());
                    }
                    aulaAvulsa = false;
                    continue;
                }

                if (situacao.getSituacao().equals("TR") && trancado) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancados().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancados());
                    }
                    trancado = false;
                    continue;
                }

                if (situacao.getSituacao().equals("TV") && trancadoVencidos) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancadosVencidos().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancadosVencidos());
                    }
                    trancadoVencidos = false;
                    continue;
                }
                if (situacao.getSituacao().equals("AE") && atestado) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosAtestado().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesAtivosAtestado());
                    }
                    atestado = false;
                    continue;
                }
                if (situacao.getSituacao().equals("CR") && carencia) {
                    if (getSituacaoContratoSinteticoDW().getQtdClientesAtivosCarencia().intValue() != 0) {
                        getDataSetPizza().setValue(situacao.getSituacao_Apresentar(), getSituacaoContratoSinteticoDW().getQtdClientesAtivosCarencia());
                    }
                    carencia = false;
                    continue;
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterarParaPizza() throws Exception {
        try {
            setSelectListaClientesRelatorio(new ArrayList<SituacaoContratoSinteticoDWVO>());
            if (getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue() != 0
                   && (getEmpresaLogado().getCodigo().intValue()==0) ) {
                setFiltros("<b>Empresa: </b>" + getSituacaoContratoSinteticoDW().getEmpresa().getNome() + " <b>Dia: </b>" + Uteis.getData(dataFinal) + "</br>");
            } else {
                setFiltros("<b>Dia: </b>" + Uteis.getData(dataFinal) + "</br>");
            }
            obterClientesDeAcordoComFiltros();
            obterFiltros();
            obterQuantidadeClientes();
            montarDatasetPizza();
            setPizza(true);
            setBarra(false);
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void alterarParaBarra() throws Exception {
        try {
            setSelectListaClientesRelatorio(new ArrayList<SituacaoContratoSinteticoDWVO>());
            if (getEmpresaLogado().getCodigo().intValue() != 0
                     && (getEmpresaLogado().getCodigo().intValue()==0)) {
                setFiltros("<b>Dia: </b> " + Uteis.getData(dataFinal) + "</br>");
            }
            else if (getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue() != 0) {
                setFiltros("<b>Empresa: </b>" + getEmpresaLista()+ " <b>Dia:</b> " + Uteis.getData(dataFinal) + "</br>");
            }
            obterClientesDeAcordoComFiltros();
            obterFiltros();
            obterQuantidadeClientes();
            montarDatasetBarra();
            setBarra(true);
            setPizza(false);
            setMensagemDetalhada("");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void atualizarLista() {
        try {
            setSelectListaClientesRelatorio(new ArrayList<SituacaoContratoSinteticoDWVO>());
            setFiltros("");
            if (getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue() != 0
                      && (getEmpresaLogado().getCodigo().intValue()==0)) {
                setFiltros("<b>Empresa: </b>" + getEmpresaLista()+ " <b>Dia:</b> " + Uteis.getData(dataFinal) + "</br>");
            }
             else if (getEmpresaLogado().getCodigo().intValue() != 0) {
                setFiltros("<b>Dia: </b> " + Uteis.getData(dataFinal) + "</br>");
            }
            obterClientesDeAcordoComFiltros();
            obterFiltros();
            obterQuantidadeClientes();
            montarDatasetBarra();
            setBarra(true);
            setPizza(false);
            setMensagemDetalhada("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void obterFiltros() {
        obterFiltroPlano();
        obterFiltroSituacao();
        obterFiltroVinculoCarteira();
    }

    public void obterClientesDeAcordoComFiltros() {
        boolean adicionarObj = false;
        for (SituacaoContratoSinteticoDWVO obj : getSelectListaClientes()) {

            adicionarObj = validarFitroSituacao(obj);
            if (!adicionarObj) {
                continue;
            }
            adicionarObj = validarFitroPlano(obj);
            if (!adicionarObj) {
                continue;
            }
            adicionarObj = validarFitroVinculoCarteira(obj);
            if (!adicionarObj) {
                continue;
            }
            getSelectListaClientesRelatorio().add(obj);

        }
    }

    public Boolean validarFitroSituacao(SituacaoContratoSinteticoDWVO obj) {
        Boolean estaNaSituacao = false;
        Boolean entrouNaOpcaoMarcado = false;
        if (getListaSituacao().size() > 0) {
            Iterator i = getListaSituacao().iterator();
            while (i.hasNext()) {
                SinteticoSituacaoVO sin = (SinteticoSituacaoVO) i.next();
                if (sin.getMarcado()) {
                    if (obj.getSituacao().equals(sin.getNome())) {

                        return true;
                    } else {
                        estaNaSituacao = false;
                    }
                    entrouNaOpcaoMarcado = true;
                }
            }
        }
        if (entrouNaOpcaoMarcado) {
            return estaNaSituacao;
        } else {
            return true;
        }
    }

    public Boolean validarFitroPlano(SituacaoContratoSinteticoDWVO obj) {
        Boolean estaNaSituacao = false;
        Boolean entrouNaOpcaoMarcado = false;
        if (getListaPlano().size() > 0) {
            Iterator i = getListaPlano().iterator();
            while (i.hasNext()) {
                SinteticoPlanoVO sin = (SinteticoPlanoVO) i.next();
                if (sin.getMarcado()) {
                    if (obj.getPlano().equals(sin.getChavePrimaria().toString())) {
                        if (obj.getPeso() > 0) {
                            return true;
                        }
                    } else {
                        estaNaSituacao = false;
                    }
                    entrouNaOpcaoMarcado = true;
                }
            }
        }
        if (entrouNaOpcaoMarcado) {
            return estaNaSituacao;
        } else {
            return true;
        }
    }

    public Boolean validarFitroVinculoCarteira(SituacaoContratoSinteticoDWVO obj) {
        Boolean estaNaSituacao = false;
        Boolean entrouNaOpcaoMarcado = false;
        if (getListaVinculoCarteira().size() > 0) {
            Iterator j = getListaVinculoCarteira().iterator();
            while (j.hasNext()) {
                SinteticoColaboradorVO sin = (SinteticoColaboradorVO) j.next();
                if (sin.getMarcado()) {
                    String[] listaVinculo = obj.getVinculoCarteira().split(":");
                    for (String vi : listaVinculo) {
                        if (vi.equals(sin.getChavePrimaria().toString())) {
                            return true;
                        } else {
                            estaNaSituacao = false;
                        }
                    }
                    entrouNaOpcaoMarcado = true;
                }
            }
        }
        if (entrouNaOpcaoMarcado) {
            return estaNaSituacao;
        } else {
            return true;
        }
    }

    public void obterFiltroSituacao() {
        if (getListaSituacao().size() > 0) {
            Iterator i = getListaSituacao().iterator();
            while (i.hasNext()) {
                SinteticoSituacaoVO sin = (SinteticoSituacaoVO) i.next();
                if (sin.getMarcado()) {
                    setFiltros(getFiltros() + "<b>Situação: </b>" + sin.getNome_Apresentar() + "</br>");
                }
            }
        }
    }

    public void obterFiltroPlano() {
        if (getListaPlano().size() > 0) {
            Iterator i = getListaPlano().iterator();
            while (i.hasNext()) {
                SinteticoPlanoVO sin = (SinteticoPlanoVO) i.next();
                if (sin.getMarcado()) {
                    setFiltros(getFiltros() + "<b>Plano: </b>" + sin.getNome() + "</br>");
                }
            }
        }
    }

    public void obterFiltroVinculoCarteira() {
        if (getListaVinculoCarteira().size() > 0) {
            Iterator i = getListaVinculoCarteira().iterator();
            while (i.hasNext()) {
                SinteticoColaboradorVO sin = (SinteticoColaboradorVO) i.next();
                if (sin.getMarcado()) {
                    setFiltros(getFiltros() + "<b>Consultor: </b>" + sin.getNome() + "</br>");
                }
            }
        }
    }

    public void obterQuantidadeClientes() {
        getSituacaoContratoSinteticoDW().setQtdClientesAtivos(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosTrancados(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosTrancadosVencidos(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosAvencer(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosVencido(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosAtestado(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosCarencia(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantes(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantesAulaAvulsa(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantesDiaria(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesVisitantesFreePass(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesInativos(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesInativosCancelados(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesInativosDesistencia(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesAtivosNormal(new Integer(0));
        getSituacaoContratoSinteticoDW().setQtdClientesTotal(new Integer(0));

        for (SituacaoContratoSinteticoDWVO obj : getSelectListaClientesRelatorio()) {
            obj.getVinculoCarteira();

            if (obj.getSituacao().equals("VI")) {
                getSituacaoContratoSinteticoDW().setQtdClientesVisitantes(getSituacaoContratoSinteticoDW().getQtdClientesVisitantes() + obj.getPeso());
            } else if (obj.getSituacao().equals("AT")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivos(getSituacaoContratoSinteticoDW().getQtdClientesAtivos() + obj.getPeso());
            } else if (obj.getSituacao().equals("NO")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosNormal(getSituacaoContratoSinteticoDW().getQtdClientesAtivosNormal() + obj.getPeso());
            } else if (obj.getSituacao().equals("IN")) {
                getSituacaoContratoSinteticoDW().setQtdClientesInativos(getSituacaoContratoSinteticoDW().getQtdClientesInativos() + obj.getPeso());
            } else if (obj.getSituacao().equals("PL")) {
                getSituacaoContratoSinteticoDW().setQtdClientesVisitantesFreePass(getSituacaoContratoSinteticoDW().getQtdClientesVisitantesFreePass() + obj.getPeso());
            } else if (obj.getSituacao().equals("AA")) {
                getSituacaoContratoSinteticoDW().setQtdClientesVisitantesAulaAvulsa(getSituacaoContratoSinteticoDW().getQtdClientesVisitantesAulaAvulsa() + obj.getPeso());
            } else if (obj.getSituacao().equals("DI")) {
                getSituacaoContratoSinteticoDW().setQtdClientesVisitantesDiaria(getSituacaoContratoSinteticoDW().getQtdClientesVisitantesDiaria() + obj.getPeso());
            } else if (obj.getSituacao().equals("TR")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosTrancados(getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancados() + obj.getPeso());
            } else if (obj.getSituacao().equals("CA")) {
                getSituacaoContratoSinteticoDW().setQtdClientesInativosCancelados(getSituacaoContratoSinteticoDW().getQtdClientesInativosCancelados() + obj.getPeso());
            } else if (obj.getSituacao().equals("DE")) {
                getSituacaoContratoSinteticoDW().setQtdClientesInativosDesistencia(getSituacaoContratoSinteticoDW().getQtdClientesInativosDesistencia() + obj.getPeso());
            } else if (obj.getSituacao().equals("TV")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosTrancadosVencidos(getSituacaoContratoSinteticoDW().getQtdClientesAtivosTrancadosVencidos() + obj.getPeso());
            } else if (obj.getSituacao().equals("AV")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosAvencer(getSituacaoContratoSinteticoDW().getQtdClientesAtivosAvencer() + obj.getPeso());
            } else if (obj.getSituacao().equals("VE")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosVencido(getSituacaoContratoSinteticoDW().getQtdClientesAtivosVencido() + obj.getPeso());
            } else if (obj.getSituacao().equals("AE")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosAtestado(getSituacaoContratoSinteticoDW().getQtdClientesAtivosAtestado() + obj.getPeso());
            } else if (obj.getSituacao().equals("CR")) {
                getSituacaoContratoSinteticoDW().setQtdClientesAtivosCarencia(getSituacaoContratoSinteticoDW().getQtdClientesAtivosCarencia() + obj.getPeso());
            }
            getSituacaoContratoSinteticoDW().setQtdClientesTotal(getSituacaoContratoSinteticoDW().getQtdClientesTotal() + obj.getPeso());
        }

    }

    public String obterVinculoCarteiraMarcado() {
        StringBuilder sbVinculo = new StringBuilder();
        for (SinteticoColaboradorVO sin : listaVinculoCarteira) {
            if (sin.getMarcado()) {
                sbVinculo.append("vinculo.colaborador = " + sin.getChavePrimaria().toString() + " or ");
            }
        }
        int tamanho = sbVinculo.length();
        String vinculo = "";
        if (tamanho > 0) {
            vinculo = (sbVinculo.substring(0, (tamanho - 3)));
        }
        return vinculo;
    }

    public String obterFiltroPlanoParaConsulta() {
        StringBuilder sbPlano = new StringBuilder();
        if (getListaPlano().size() > 0) {
            Iterator i = getListaPlano().iterator();
            while (i.hasNext()) {
                SinteticoPlanoVO sin = (SinteticoPlanoVO) i.next();
                if (sin.getMarcado()) {
                    sbPlano.append("plano.descricao = '" + sin.getNome().toString().toUpperCase() + "'  or ");
                }
            }
        }
        String plano = "";
        int tamanho = sbPlano.length();
        if (tamanho > 0) {
            plano = (sbPlano.substring(0, (tamanho - 3)));
        }
        return plano;
    }

    public String montarListaClienteAnaliticoVisitante() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }
        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "VI", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, "", Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";

    }

    public String montarListaClienteAnaliticoVisitanteFreePass() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "PL", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, "", Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoVisitanteAulaAvulsa() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "AA", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, "", Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoVisitanteDiaria() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "DI", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, "", Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoInativo() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "IN", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoInativoCancelado() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "CA", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoInativoDesistente() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "DE", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoAtivo() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "AT", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoAtivoNormal() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "NO", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoAtivoAtestado() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "AE", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoAtivoCarencia() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "CR", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoAtivoTrancado() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "TR", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoAtivoTrancadoVencido() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "TV", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoTotalClientes() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoAtivoAVencer() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "AV", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    public String montarListaClienteAnaliticoAtivoVencido() throws Exception {
        SituacaoContratoAnaliticoDWControle situacaoContratoAnalitico = (SituacaoContratoAnaliticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoAnaliticoDWControle");
        if (situacaoContratoAnalitico != null) {
            situacaoContratoAnalitico.liberarBackingBeanMemoria("SituacaoContratoAnaliticoDWControle");
        }

        Date dia = negocio.comuns.utilitarias.Calendario.hoje();
        if (getDataFinal() != null) {
            dia = getDataFinal();
        }
        String vinculo = obterVinculoCarteiraMarcado();
        String plano = obterFiltroPlanoParaConsulta();
        setSelectListaClienteAnalitico(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClienteAnalitico(new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnalitico(dia, "VE", getSituacaoContratoSinteticoDW().getEmpresa().getCodigo().intValue(), vinculo, plano, Uteis.NIVELMONTARDADOS_TODOS));
        return "clienteAnalitico";
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        empresaFacade = null;
        planoFacade = null;
        colaboradorFacade = null;
        situacaoContratoSinteticoDW = null;
        dataSetPizza = null;
        dataSetBarra = null;
        pizza = null;
        barra = null;
        selectListaClientes.clear();
        selectListaClienteAnalitico.clear();
        selectListaClientesRelatorio.clear();
        dataFinal = null;
        apresentarRelatorio = null;
        filtros = null;
    }

    public SituacaoContratoSinteticoDWVO getSituacaoContratoSinteticoDW() {
        return situacaoContratoSinteticoDW;
    }

    public void setSituacaoContratoSinteticoDW(SituacaoContratoSinteticoDWVO situacaoContratoSinteticoDW) {
        this.situacaoContratoSinteticoDW = situacaoContratoSinteticoDW;
    }

    public List<SituacaoContratoSinteticoDWVO> getSelectListaClientes() {
        return selectListaClientes;
    }

    public void setSelectListaClientes(List<SituacaoContratoSinteticoDWVO> selectListaClientes) {
        this.selectListaClientes = selectListaClientes;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public DefaultCategoryDataset getDataSetBarra() {
        return dataSetBarra;
    }

    public void setDataSetBarra(DefaultCategoryDataset dataSetBarra) {
        this.dataSetBarra = dataSetBarra;
    }

    public Boolean getBarra() {
        return barra;
    }

    public void setBarra(Boolean barra) {
        this.barra = barra;
    }

    public DefaultPieDataset getDataSetPizza() {
        return dataSetPizza;
    }

    public void setDataSetPizza(DefaultPieDataset dataSetPizza) {
        this.dataSetPizza = dataSetPizza;
    }

    public Boolean getPizza() {
        return pizza;
    }

    public void setPizza(Boolean pizza) {
        this.pizza = pizza;
    }

    public Boolean getApresentarRelatorio() {
        return apresentarRelatorio;
    }

    public void setApresentarRelatorio(Boolean apresentarRelatorio) {
        this.apresentarRelatorio = apresentarRelatorio;
    }

    public List<SituacaoContratoAnaliticoDWVO> getSelectListaClienteAnalitico() {
        return selectListaClienteAnalitico;
    }

    public void setSelectListaClienteAnalitico(List<SituacaoContratoAnaliticoDWVO> selectListaClienteAnalitico) {
        this.selectListaClienteAnalitico = selectListaClienteAnalitico;
    }

    public List<SituacaoContratoSinteticoDWVO> getSelectListaClientesRelatorio() {
        return selectListaClientesRelatorio;
    }

    public void setSelectListaClientesRelatorio(List<SituacaoContratoSinteticoDWVO> selectListaClientesRelatorio) {
        this.selectListaClientesRelatorio = selectListaClientesRelatorio;
    }

    public String getFiltros() {
        if (filtros == null) {
            filtros = "";
        }
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    /**
     * @return the listaSelectItemEmpresa
     */
    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    /**
     * @param listaSelectItemEmpresa
     *            the listaSelectItemEmpresa to set
     */
    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public List<SinteticoPlanoVO> getListaPlano() {
        return listaPlano;
    }

    public void setListaPlano(List<SinteticoPlanoVO> listaPlano) {
        this.listaPlano = listaPlano;
    }

    public List<SinteticoSituacaoVO> getListaSituacao() {
        return listaSituacao;
    }

    public void setListaSituacao(List<SinteticoSituacaoVO> listaSituacao) {
        this.listaSituacao = listaSituacao;
    }

    public List<SinteticoColaboradorVO> getListaVinculoCarteira() {
        return listaVinculoCarteira;
    }

    public void setListaVinculoCarteira(List<SinteticoColaboradorVO> listaVinculoCarteira) {
        this.listaVinculoCarteira = listaVinculoCarteira;
    }
}

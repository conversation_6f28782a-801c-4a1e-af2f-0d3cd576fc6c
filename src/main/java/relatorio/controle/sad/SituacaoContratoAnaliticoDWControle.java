/*
, * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.sad;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import javax.faces.model.SelectItem;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Vinculo;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.plano.Plano;
import negocio.interfaces.basico.ColaboradorInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.basico.VinculoInterfaceFacade;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import negocio.interfaces.plano.PlanoInterfaceFacade;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.sad.SinteticoColaboradorVO;
import relatorio.negocio.comuns.sad.SinteticoPlanoVO;
import relatorio.negocio.comuns.sad.SinteticoSituacaoVO;
import relatorio.negocio.comuns.sad.SituacaoContratoAnaliticoDWVO;
import relatorio.negocio.jdbc.sad.SituacaoContratoAnaliticoDW;

/**
 *
 * <AUTHOR>
 */
public class SituacaoContratoAnaliticoDWControle extends SuperControleRelatorio {

    protected SituacaoContratoAnaliticoDWVO situacaoContratoAnaliticoDW;
    protected SituacaoContratoAnaliticoDW situacaoContratoAnaliticoDWRel;
    protected List<SituacaoContratoAnaliticoDWVO> selectListaClientes;
    protected List<SituacaoContratoAnaliticoDWVO> selectListaClientesRelatorio;
    protected List<SinteticoSituacaoVO> listaSituacao;
    protected List<SinteticoPlanoVO> listaPlano;
    protected List<SinteticoColaboradorVO> listaVinculoCarteira;
    protected Date dataInicial;
    protected Date dataFinal;
    protected String filtros;
    private List listaSelectItemEmpresa;
    protected Boolean apresentarRelatorio;
    protected Boolean apresentarFiltroSituacao;
    protected SinteticoSituacaoVO sinteticoSituacao;
    protected EmpresaInterfaceFacade empresaFacade = null;
    protected PlanoInterfaceFacade planoFacade = null;
    protected ColaboradorInterfaceFacade colaboradorFacade = null;
    protected ContratoInterfaceFacade contratoFacade = null;
    protected VinculoInterfaceFacade vinculoFacade = null;

    public SituacaoContratoAnaliticoDWControle() throws Exception {
        super();
        inicializarFacades();
        montarListaSelectItemEmpresa();
        validarClienteAnalitico();
        setFiltros("");
        setMensagemID("msg_entre_prmrelatorio");
    }

    public void montarListaSelectItemEmpresa() {
        try {
            List objs = new ArrayList();
            objs.add(new SelectItem(new Integer(0), ""));
            List resultadoConsulta = consultarPorNomeEmpresa("");
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
            setListaSelectItemEmpresa(objs);
        } catch (Exception e) {
            setListaSelectItemEmpresa(new ArrayList());
            e.printStackTrace();
        }
    }

    public List consultarPorNomeEmpresa(String nomePrm) throws Exception {
        List lista = empresaFacade.consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public void obterEmpresaEscolhida() throws Exception {
        if (getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo().intValue() != 0) {
            getSituacaoContratoAnaliticoDW().setEmpresa(empresaFacade.consultarPorChavePrimaria(getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        } else {
            getSituacaoContratoAnaliticoDW().setEmpresa(new EmpresaVO());
        }
    }

    private void validarClienteAnalitico() throws Exception {
        SituacaoContratoSinteticoDWControle situacaoContratoSinteticoDWControle = (SituacaoContratoSinteticoDWControle) context().getExternalContext().getSessionMap().get("SituacaoContratoSinteticoDWControle");
        if (situacaoContratoSinteticoDWControle != null && situacaoContratoSinteticoDWControle.getSelectListaClienteAnalitico().size() > 0) {
            setSituacaoContratoAnaliticoDW(new SituacaoContratoAnaliticoDWVO());
            setSelectListaClientes(new ArrayList<SituacaoContratoAnaliticoDWVO>());
            setListaPlano(new ArrayList<SinteticoPlanoVO>());
            setListaSituacao(new ArrayList<SinteticoSituacaoVO>());
            setListaVinculoCarteira(new ArrayList<SinteticoColaboradorVO>());
            setDataFinal(situacaoContratoSinteticoDWControle.getDataFinal());
            setApresentarRelatorio(false);
            inicializarUsuarioLogado();
            inicializarEmpresaLogado();
            setSelectListaClientes(situacaoContratoSinteticoDWControle.getSelectListaClienteAnalitico());
            setSelectListaClientesRelatorio(getSelectListaClientes());
            inicializarListaSituacao(getSelectListaClientesRelatorio());
            inicializarListaPlano(getSelectListaClientesRelatorio());
            inicializarListaVinculoCarteira(getSelectListaClientesRelatorio());
            if (getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo().intValue() != 0) {
                setFiltros("<b>Empresa: </b>" + getEmpresaLista() + " <b>Dia:</b> " + Uteis.getData(dataFinal) + "</br>");
            }else if (getEmpresaLogado().getCodigo().intValue() != 0) {
                setFiltros("<b>Dia:</b> " + Uteis.getData(dataFinal)+"</br>");
            } 
        }
    }

    public void inicializarListaSituacao(List lista) throws Exception {
        Iterator i = lista.iterator();
        SinteticoSituacaoVO sin = new SinteticoSituacaoVO();
        while (i.hasNext()) {
            SituacaoContratoAnaliticoDWVO obj = (SituacaoContratoAnaliticoDWVO) i.next();
            sin.setNome(obj.getSituacao());
            sin.adicionarObjClienteSituacaoVOs(getListaSituacao(), sin);
        }
    }

    public void inicializarListaPlano(List lista) throws Exception {
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            SituacaoContratoAnaliticoDWVO obj = (SituacaoContratoAnaliticoDWVO) i.next();
            if (obj.getPlano().getCodigo().intValue() != 0) {
                inicializarFiltrosAnaliticoPlano(obj);
            }
        }
    }

    public void inicializarListaVinculoCarteira(List lista) throws Exception {
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            SituacaoContratoAnaliticoDWVO obj = (SituacaoContratoAnaliticoDWVO) i.next();
            if (!obj.getCliente().getCodigo().equals(0)) {
                inicializarFiltrosAnaliticoVinculoCarteira(obj);
            }
        }
    }

    public void inicializarUsuarioLogado() {
        try {
            getSituacaoContratoAnaliticoDW().setUsuarioVO(new UsuarioVO());
            getSituacaoContratoAnaliticoDW().getUsuarioVO().setCodigo(getUsuarioLogado().getCodigo().intValue());
            getSituacaoContratoAnaliticoDW().getUsuarioVO().setUsername(getUsuarioLogado().getUsername());
            getSituacaoContratoAnaliticoDW().getUsuarioVO().setAdministrador(getUsuarioLogado().getAdministrador());
        } catch (Exception exception) {
        }
    }

    public void inicializarEmpresaLogado() throws Exception {
        try {
            if (getEmpresaLogado().getCodigo().intValue() != 0) {
                getSituacaoContratoAnaliticoDW().getEmpresa().setCodigo(getEmpresaLogado().getCodigo().intValue());
                getSituacaoContratoAnaliticoDW().getEmpresa().setNome(getEmpresaLogado().getNome());
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public String novo() throws Exception {
        setDataFinal(negocio.comuns.utilitarias.Calendario.hoje());
        setSituacaoContratoAnaliticoDW(new SituacaoContratoAnaliticoDWVO());
        setSelectListaClientes(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setSelectListaClientesRelatorio(new ArrayList<SituacaoContratoAnaliticoDWVO>());
        setListaPlano(new ArrayList<SinteticoPlanoVO>());
        setListaSituacao(new ArrayList<SinteticoSituacaoVO>());
        setListaVinculoCarteira(new ArrayList<SinteticoColaboradorVO>());
        setEmpresa(new EmpresaVO());
        setApresentarRelatorio(false);
        inicializarUsuarioLogado();
        inicializarEmpresaLogado();
        if (getUsuarioLogado().getAdministrador() && getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo().equals(0)) {
            return "clienteAnalitico";
        }
        return "clienteAnalitico";
    }

    public void consultarClientesPorData() throws Exception {
        try {
            System.out.println("Inicio " + this.getClass().getSimpleName() + " " + negocio.comuns.utilitarias.Calendario.hoje().toString());
            if (getUsuarioLogado().getAdministrador() && getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo().equals(0)) {
                throw new Exception("Informe a Empresa.");
            }
            getSelectListaClientes().clear();
            ResultSet lista;
            lista = new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnaliticoDefault(getDataFinal(),
                    getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo().intValue(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            setListaVinculoCarteira(new ArrayList<SinteticoColaboradorVO>());
            setListaPlano(new ArrayList<SinteticoPlanoVO>());
            setListaSituacao(new ArrayList<SinteticoSituacaoVO>());

            inicializarListaCliente(lista);
            if(getEmpresaLogado().getCodigo().intValue() !=0){
                setFiltros("Dia:" + Uteis.getData(dataFinal));
            }
            else if (getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo().intValue() != 0) {
                setFiltros("Empresa: " + getEmpresaLista()+"Dia: " + Uteis.getData(dataFinal));
            }
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
            System.out.println("Término: " + this.getClass().getSimpleName() + " " + negocio.comuns.utilitarias.Calendario.hoje().toString());
            //return "";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            //return "";
            //  return "clienteSintetico";
        }
        //return "clienteAnalitico";
    }

    public void montarListaDefault() throws Exception {
        try {
            if (getUsuarioLogado().getAdministrador() && getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo().equals(0)) {
                throw new Exception("Informe a Empresa.");
            }
            setDataFinal(negocio.comuns.utilitarias.Calendario.hoje());
            montarDadosTableDefault(getDataFinal(), getSituacaoContratoAnaliticoDW().getEmpresa());
            if (getEmpresaLogado().getCodigo().intValue() != 0) {
                setFiltros("Dia:" + Uteis.getData(dataFinal));
            } else if (getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo().intValue() != 0) {
                setFiltros("Empresa:" + getEmpresaLista() + " Dia: " + Uteis.getData(dataFinal) + "");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarDadosTableDefault(Date dia, EmpresaVO empresa) throws Exception {
        try {
            ResultSet lista;
            lista = new SituacaoContratoAnaliticoDW().consultarQuantidadeContratosAnaliticoDefault(dia,
                    getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo().intValue(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            inicializarListaCliente(lista);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarListaCliente(ResultSet lista) throws Exception {
        try {
            setSelectListaClientes(new ArrayList<SituacaoContratoAnaliticoDWVO>());
            setSelectListaClientesRelatorio(new ArrayList<SituacaoContratoAnaliticoDWVO>());
            while (lista.next()) {
                SituacaoContratoAnaliticoDWVO obj = new SituacaoContratoAnaliticoDWVO();
                obj.setSituacao(lista.getString("Situacao"));
                SinteticoSituacaoVO sin = new SinteticoSituacaoVO();
                sin.setNome(obj.getSituacao());
                sin.adicionarObjClienteSituacaoVOs(getListaSituacao(), sin);
                obj.setFoneCliente(lista.getString("FoneCliente"));
                obj.setEmailCliente(lista.getString("EmailCliente"));
                obj.getEmpresa().setCodigo(lista.getInt("Empresa"));
                obj.setModalidadeCliente(lista.getString("ModalidadeCliente"));
                obj.setEnderecoCliente(lista.getString("EnderecoCliente"));
                obj.getContrato().setCodigo(lista.getInt("Contrato"));
                if (obj.getContrato().getCodigo().intValue() != 0) {
                    obj.setContrato(getFacade().getContrato().consultarPorChavePrimaria(
                            obj.getContrato().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_ROBO));
                }
                obj.getCliente().setCodigo(lista.getInt("Cliente"));
                if (obj.getCliente().getCodigo().intValue() != 0) {
                    obj.setCliente(getFacade().getCliente().consultarPorChavePrimaria(
                            obj.getCliente().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_INDICERENOVACAO));
                    inicializarFiltrosAnaliticoVinculoCarteira(obj);
                }
                obj.getPlano().setCodigo(lista.getInt("Plano"));
                if (obj.getPlano().getCodigo().intValue() != 0) {
                    obj.setPlano(getFacade().getPlano().consultarPorChavePrimaria(
                            obj.getPlano().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_MINIMOS));
                    inicializarFiltrosAnaliticoPlano(obj);
                }
                getSelectListaClientes().add(obj);
                getSelectListaClientesRelatorio().add(obj);

            }
        } catch (Exception e) {
            throw e;
        }

    }

    public void obterFiltros() {
        obterFiltroVinculoCarteira();
        obterFiltroPlano();
        obterFiltroSituacao();
    }

    public void obterClientesDeAcordoComFiltros() {
        boolean adicionarObj = false;
        for (SituacaoContratoAnaliticoDWVO obj : getSelectListaClientes()) {
            adicionarObj = validarFitroSituacao(obj);
            if (!adicionarObj) {
                continue;
            }
            adicionarObj = validarFitroPlano(obj);
            if (!adicionarObj) {
                continue;
            }
            adicionarObj = validarFitroVinculoCarteira(obj);
            if (!adicionarObj) {
                continue;
            }
            getSelectListaClientesRelatorio().add(obj);
        }
    }

    public Boolean validarFitroSituacao(SituacaoContratoAnaliticoDWVO obj) {
        Boolean estaNaSituacao = false;
        Boolean entrouNaOpcaoMarcado = false;
        if (getListaSituacao().size() > 0) {
            Iterator i = getListaSituacao().iterator();
            while (i.hasNext()) {
                SinteticoSituacaoVO sin = (SinteticoSituacaoVO) i.next();
                if (sin.getMarcado()) {
                    if (obj.getSituacao().equals(sin.getNome())) {
                        return true;
                    } else {
                        estaNaSituacao = false;
                    }
                    entrouNaOpcaoMarcado = true;
                }
            }
        }
        if (entrouNaOpcaoMarcado) {
            return estaNaSituacao;
        } else {
            return true;
        }
    }

    public Boolean validarFitroPlano(SituacaoContratoAnaliticoDWVO obj) {
        Boolean estaNaSituacao = false;
        Boolean entrouNaOpcaoMarcado = false;
        if (getListaPlano().size() > 0) {
            Iterator i = getListaPlano().iterator();
            while (i.hasNext()) {
                SinteticoPlanoVO sin = (SinteticoPlanoVO) i.next();
                if (sin.getMarcado()) {
                    if (obj.getPlano().getCodigo().equals(sin.getChavePrimaria())) {
                        return true;
                    } else {
                        estaNaSituacao = false;
                    }
                    entrouNaOpcaoMarcado = true;
                }
            }
        }
        if (entrouNaOpcaoMarcado) {
            return estaNaSituacao;
        } else {
            return true;
        }
    }

    public Boolean validarFitroVinculoCarteira(SituacaoContratoAnaliticoDWVO obj) {
        Boolean estaNaSituacao = false;
        Boolean entrouNaOpcaoMarcado = false;
        if (getListaVinculoCarteira().size() > 0) {
            Iterator i = getListaVinculoCarteira().iterator();
            while (i.hasNext()) {
                SinteticoColaboradorVO sin = (SinteticoColaboradorVO) i.next();
                if (sin.getMarcado()) {
                    for (VinculoVO vi : obj.getCliente().getVinculoVOs()) {
                        if (vi.getColaborador().getCodigo().equals(sin.getChavePrimaria())) {
                            return true;
                        } else {
                            estaNaSituacao = false;
                        }
                    }
                    entrouNaOpcaoMarcado = true;
                }
            }
        }
        if (entrouNaOpcaoMarcado) {
            return estaNaSituacao;
        } else {
            return true;
        }
    }

    public void obterFiltroSituacao() {
        if (getListaSituacao().size() > 0) {
            Iterator i = getListaSituacao().iterator();
            while (i.hasNext()) {
                SinteticoSituacaoVO sin = (SinteticoSituacaoVO) i.next();
                if (sin.getMarcado()) {
                    setFiltros(getFiltros() + "Situação: " + sin.getNome_Apresentar() + "");
                }
            }
        }
    }

    public void obterFiltroPlano() {
        if (getListaPlano().size() > 0) {
            Iterator i = getListaPlano().iterator();

            while (i.hasNext()) {
                SinteticoPlanoVO sin = (SinteticoPlanoVO) i.next();
                if (sin.getMarcado()) {
                    setFiltros(getFiltros() + "Plano: " + sin.getNome());
                }
            }
        }
    }

    public void obterFiltroVinculoCarteira() {
        if (getListaVinculoCarteira().size() > 0) {
            Iterator i = getListaVinculoCarteira().iterator();
            SinteticoColaboradorVO sin;
            while (i.hasNext()) {
                sin = (SinteticoColaboradorVO) i.next();
                if (sin.getMarcado()) {
                    setFiltros(getFiltros() + "Colaborador: " + sin.getNome());
                }
            }
        }
    }

    public Integer getNrColunaVinculoCarteira() {
        Integer x = getListaVinculoCarteira().size() / 5;
        if (x == 0) {
            return 1;
        }
        return x;

    }

    public Integer getTamanhoListaVinculoCarteira() {
        Integer x = getListaVinculoCarteira().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public Integer getNrColunaSituacao() {
        Integer x = getListaSituacao().size() / 3;
        if (x == 0) {
            return 1;
        }
        return x;

    }

    public Integer getTamanhoListaSituacao() {
        Integer x = getListaSituacao().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public Integer getNrColunaPlano() {
        Integer x = getListaPlano().size() / 5;
        if (x == 0) {
            return 1;
        }
        return x;

    }

    public Integer getTamanhoListaPlano() {
        Integer x = getListaPlano().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public void imprimirRelatorio() {
        try {
            String nomeRelatorio = situacaoContratoAnaliticoDWRel.getIdEntidade();
            String titulo = "Relatório Analítico de Clientes";
            String barra = " - ";
            String design = situacaoContratoAnaliticoDWRel.getDesignIReportRelatorio();
            EmpresaVO empresa = new EmpresaVO();
            if (getEmpresaLogado().getCodigo().intValue() != 0) {
                empresa = new Empresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            }
            apresentarRelatorioObjetos(nomeRelatorio, titulo, empresa.getNome(), "", "",
                    "PDF", "/" + situacaoContratoAnaliticoDWRel.getIdEntidade() + "/registros",
                    design, getUsuarioLogado().getNome(), getFiltros(), "", "",
                    empresa.getEndereco() + " " + empresa.getNumero() + " " + empresa.getSetor(),
                    empresa.getCidade().getNome() + barra + empresa.getCidade().getEstado().getSigla(), "", "", getSelectListaClientesRelatorio());
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public Integer obterEmpresaLogadoSistema() throws Exception {
        EmpresaVO emp = getEmpresaLogado();
        if (emp == null || emp.getCodigo().intValue() == 0) {
            return (new Integer(0));
        } else {
            return emp.getCodigo();
        }
    }

    private String getEmpresaLista() throws Exception {
        getSituacaoContratoAnaliticoDW().setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        return getSituacaoContratoAnaliticoDW().getEmpresa().getNome();
    }

    public void inicializarFiltrosAnaliticoPlano(SituacaoContratoAnaliticoDWVO obj) throws Exception {
        try {
            //obj.setPlano(new Plano().consultarPorChavePrimaria(obj.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_INDICERENOVACAO));
            SinteticoPlanoVO sin = new SinteticoPlanoVO();
            sin.setNome(obj.getPlano().getDescricao());
            sin.setChavePrimaria(obj.getPlano().getCodigo());
            sin.adicionarObjClienteSituacaoVOs(getListaPlano(), sin);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarFiltrosAnaliticoVinculoCarteira(SituacaoContratoAnaliticoDWVO obj) throws Exception {
        try {
            /*obj.getCliente().setVinculoVOs(vinculoFacade.consultarPorCodigoCliente(obj.getCliente().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_INDICERENOVACAO));*/
            Iterator i = obj.getCliente().getVinculoVOs().iterator();
            while (i.hasNext()) {
                VinculoVO vi = (VinculoVO) i.next();
                SinteticoColaboradorVO sin = new SinteticoColaboradorVO();
                sin.setNome(vi.getColaborador().getPessoa().getNome());
                sin.setChavePrimaria(vi.getColaborador().getCodigo());
                sin.adicionarObjClienteSituacaoVOs(getListaVinculoCarteira(), sin);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void atualizarListaCliente() throws Exception {
        try {
            setSelectListaClientesRelatorio(new ArrayList<SituacaoContratoAnaliticoDWVO>());
            setFiltros("");
            if (getEmpresaLogado().getCodigo().intValue() != 0) {
                setFiltros("Dia:" + Uteis.getData(dataFinal));
            } else if (getSituacaoContratoAnaliticoDW().getEmpresa().getCodigo().intValue() != 0) {
                setFiltros("Empresa: " + getEmpresaLista() + " Dia: " + Uteis.getData(dataFinal) + "");
            }
            obterClientesDeAcordoComFiltros();
            obterFiltros();
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    @Override
    protected boolean inicializarFacades() {
        try {
            empresaFacade = new Empresa();
            planoFacade = new Plano();
            colaboradorFacade = new Colaborador();
            contratoFacade = new Contrato();
            vinculoFacade = new Vinculo();
            situacaoContratoAnaliticoDWRel = new SituacaoContratoAnaliticoDW();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public String obterParametroSituacao() {
        if (getSituacaoContratoAnaliticoDW().getSituacao() == null || getSituacaoContratoAnaliticoDW().getSituacao().equals("") || getSituacaoContratoAnaliticoDW().getSituacao().equals("TO")) {
            return "";
        }

        if (getSituacaoContratoAnaliticoDW().getSituacao().equals("VI")) {
            if (getSituacaoContratoAnaliticoDW().getOpcao() == null || getSituacaoContratoAnaliticoDW().getOpcao().equals("")) {
                return "VI";
            }

            if (getSituacaoContratoAnaliticoDW().getOpcao().equals("PL")) {
                return "PL";
            }

            if (getSituacaoContratoAnaliticoDW().getOpcao().equals("AA")) {
                return "AA";
            }

            if (getSituacaoContratoAnaliticoDW().getOpcao().equals("DI")) {
                return "DI";
            }

        }
        if (getSituacaoContratoAnaliticoDW().getSituacao().equals("AT")) {
            if (getSituacaoContratoAnaliticoDW().getOpcao() == null || getSituacaoContratoAnaliticoDW().getOpcao().equals("")) {
                return "NO";
            }

            if (getSituacaoContratoAnaliticoDW().getOpcao().equals("NO")) {
                return "NO";
            }

            if (getSituacaoContratoAnaliticoDW().getOpcao().equals("TR")) {
                return "TR";
            }

            if (getSituacaoContratoAnaliticoDW().getOpcao().equals("TV")) {
                return "TV";
            }

            if (getSituacaoContratoAnaliticoDW().getOpcao().equals("AV")) {
                return "AV";
            }

            if (getSituacaoContratoAnaliticoDW().getOpcao().equals("VE")) {
                return "VE";
            }
            if (getSituacaoContratoAnaliticoDW().getOpcao().equals("CR")) {
                return "CR";
            }
        }
        if (getSituacaoContratoAnaliticoDW().getSituacao().equals("IN")) {
            if (getSituacaoContratoAnaliticoDW().getOpcao() == null || getSituacaoContratoAnaliticoDW().getOpcao().equals("")) {
                return "IN";
            }

            if (getSituacaoContratoAnaliticoDW().getOpcao().equals("CA")) {
                return "CA";
            }

            if (getSituacaoContratoAnaliticoDW().getOpcao().equals("DE")) {
                return "DE";
            }

        }
        return "";
    }

    public void irParaTelaCliente() {
        SituacaoContratoAnaliticoDWVO obj = (SituacaoContratoAnaliticoDWVO) context().getExternalContext().getRequestMap().get("situacaoContratoAnaliticoDW");
        try {
            if(obj == null)
                throw new Exception("Cliente Não Encontrado.");
            else
                irParaTelaCliente(obj.getCliente());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        empresaFacade = null;
        colaboradorFacade = null;
        colaboradorFacade = null;
        planoFacade = null;
        apresentarRelatorio = null;
    }

    public SituacaoContratoAnaliticoDWVO getSituacaoContratoAnaliticoDW() {
        return situacaoContratoAnaliticoDW;
    }

    public void setSituacaoContratoAnaliticoDW(SituacaoContratoAnaliticoDWVO situacaoContratoAnaliticoDW) {
        this.situacaoContratoAnaliticoDW = situacaoContratoAnaliticoDW;
    }

    public List<SituacaoContratoAnaliticoDWVO> getSelectListaClientes() {
        return selectListaClientes;
    }

    public void setSelectListaClientes(List<SituacaoContratoAnaliticoDWVO> selectListaClientes) {
        this.selectListaClientes = selectListaClientes;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Boolean getApresentarRelatorio() {
        return apresentarRelatorio;
    }

    public void setApresentarRelatorio(Boolean apresentarRelatorio) {
        this.apresentarRelatorio = apresentarRelatorio;
    }

    public SituacaoContratoAnaliticoDW getSituacaoContratoAnaliticoDWRel() {
        return situacaoContratoAnaliticoDWRel;
    }

    public void setSituacaoContratoAnaliticoDWRel(SituacaoContratoAnaliticoDW situacaoContratoAnaliticoDWRel) {
        this.situacaoContratoAnaliticoDWRel = situacaoContratoAnaliticoDWRel;
    }

    public List<SituacaoContratoAnaliticoDWVO> getSelectListaClientesRelatorio() {
        return selectListaClientesRelatorio;
    }

    public void setSelectListaClientesRelatorio(List<SituacaoContratoAnaliticoDWVO> selectListaClientesRelatorio) {
        this.selectListaClientesRelatorio = selectListaClientesRelatorio;
    }

    public Boolean getApresentarFiltroSituacao() {
        return apresentarFiltroSituacao;
    }

    public void setApresentarFiltroSituacao(Boolean apresentarFiltroSituacao) {
        this.apresentarFiltroSituacao = apresentarFiltroSituacao;
    }

    public SinteticoSituacaoVO getSinteticoSituacao() {
        return sinteticoSituacao;
    }

    public void setSinteticoSituacao(SinteticoSituacaoVO sinteticoSituacao) {
        this.sinteticoSituacao = sinteticoSituacao;
    }

    public String getFiltros() {
        if (filtros == null) {
            filtros = "";
        }
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    /**
     * @return the listaSelectItemEmpresa
     */
    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    /**
     * @param listaSelectItemEmpresa the listaSelectItemEmpresa to set
     */
    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public List<SinteticoPlanoVO> getListaPlano() {
        return listaPlano;
    }

    public void setListaPlano(List<SinteticoPlanoVO> listaPlano) {
        this.listaPlano = listaPlano;
    }

    public List<SinteticoSituacaoVO> getListaSituacao() {
        return listaSituacao;
    }

    public void setListaSituacao(List<SinteticoSituacaoVO> listaSituacao) {
        this.listaSituacao = listaSituacao;
    }

    public List<SinteticoColaboradorVO> getListaVinculoCarteira() {
        return listaVinculoCarteira;
    }

    public void setListaVinculoCarteira(List<SinteticoColaboradorVO> listaVinculoCarteira) {
        this.listaVinculoCarteira = listaVinculoCarteira;
    }
}

       
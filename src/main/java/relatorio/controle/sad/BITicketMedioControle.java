/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.sad;

import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.json.TicketMedioJSON;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import org.json.JSONArray;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import negocio.comuns.basico.ConfiguracaoBIVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import org.json.JSONObject;
import relatorio.controle.basico.BIControle;
import relatorio.negocio.comuns.basico.TicketMedioVO;
import relatorio.negocio.enumeradores.TicketMedioEnum;

/**
 * <AUTHOR>
 */
public class BITicketMedioControle extends BIControle {

    private TicketMedioVO ticket = new TicketMedioVO();
    private Date dataBase = Calendario.hoje();
    private TicketMedioEnum tipo = TicketMedioEnum.COMPETENCIA;
    private String tituloTela;
    private List<TicketMedioJSON> listaGrafico;
    private List<ConfiguracaoBIVO> configuracoes;
    private String labelFonteDadosFinanceiros = "";

    public BITicketMedioControle() throws Exception {
        super();


    }

    public String getDadosGrafico() {
        try {
            return new JSONArray(listaGrafico == null || listaGrafico.isEmpty()
                    ? Arrays.asList(new TicketMedioJSON[]{new TicketMedioJSON()}) : listaGrafico).toString();
        } catch (Exception e) {
            return new JSONArray(Arrays.asList(new TicketMedioJSON[]{new TicketMedioJSON()})).toString();
        }
    }

    public TicketMedioVO getTicket() {
        return ticket;
    }

    public void setTicket(TicketMedioVO ticket) {
        this.ticket = ticket;
    }

    public void abrirLista(String tela, TicketMedioEnum tipo) {
        try {
            setMsgAlert("");
            tituloTela = tela;
            this.tipo = tipo;
            setMsgAlert("abrirPopup('./includes/bi/lista_ticket_medio.jsp', 'TicketMedio', 780, 595);");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirListaCompetencia() {
        abrirLista("Caixa por competência", TicketMedioEnum.COMPETENCIA);
    }

    public void abrirListaFaturamento() {
        abrirLista("Caixa por faturamento", TicketMedioEnum.FATURAMENTO_RECEBIDO);
    }

    public void abrirListaReceita() {
        abrirLista("Caixa por receita", TicketMedioEnum.RECEITA);
    }

    public void atualizar() {
        atualizar(false);
    }

    public void atualizarData() {
        gravarHistoricoAcessoBI(BIEnum.TICKET_MEDIO);
        atualizar(true);
    }

    public void atualizarData(EmpresaVO empresa) {
        gravarHistoricoAcessoBI(BIEnum.TICKET_MEDIO);
        atualizar(true, empresa);
    }
    public void     atualizarData(EmpresaVO empresa, String key) {
        gravarHistoricoAcessoBI(BIEnum.TICKET_MEDIO);
        atualizar(true, empresa, key);
    }

    private void validarEmpresa() throws Exception {
        if (getEmpresaFiltroBI().getCodigo() == 0 && !isPermiteConsultarTodasEmpresas()) {
            ticket = new TicketMedioVO();
            throw new Exception("O campo empresa deve ser informado");
        }
    }

    private FiltroDTO getFiltroDTO() {
        return getFiltroDTO(null);
    }

    private FiltroDTO getFiltroDTO(EmpresaVO empresa) {
        FiltroDTO filtroDTO = new FiltroDTO();
        int mesData = Uteis.getMesData(getDataBaseFiltro());
        int anoData = Uteis.getAnoData(getDataBaseFiltro());
        filtroDTO.setNome(BIEnum.AULA_EXPERIMENTAL.name());
        JSONObject filtros = new JSONObject();
        filtros.put("database", Uteis.getData(getDataBaseFiltro()));
        filtros.put("mes", mesData);
        filtros.put("ano", anoData);
        if(empresa == null) {
            filtros.put("empresa", getEmpresaFiltro().getCodigo());
        }else {
            filtros.put("empresa", empresa.getCodigo());
        }
        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    public void initConfig() throws Exception {
        configuracoes = getFacade().getConfiguracaoBI().consultarPorBI(BIEnum.TICKET_MEDIO, getEmpresaFiltro().getCodigo());

        configuracoes.removeIf(c -> c.getConfiguracao().equals(ConfiguracaoBIEnum.INCLUIR_PRODUTOS_RECEITA));

        ConfiguracaoSistemaVO configuracaoSistemaVO = (ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA);
        if (configuracaoSistemaVO != null && !configuracaoSistemaVO.isUsaPlanoRecorrenteCompartilhado()) {
            configuracoes.removeIf(c -> c.getConfiguracao().equals(ConfiguracaoBIEnum.CONTAR_DEPENDENTES_COMO_PAGANTES));
        }
    }

    public void atualizar(boolean gerarNovamente) {
        atualizar(gerarNovamente, null, null);
    }

    public void atualizar(boolean gerarNovamente, EmpresaVO empresa) {
        atualizar(gerarNovamente, null, null);
    }

    public void atualizar(boolean gerarNovamente, EmpresaVO empresa, String key) {
        try {
            if (empresa == null) {
                validarEmpresa();
            }
            initConfig();
            FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(key), BIEnum.TICKET_MEDIO, getFiltroDTO(empresa), gerarNovamente);
            JSONArray grafico = new JSONObject(filtroDTO.getJsonDados()).getJSONArray("grafico");
            listaGrafico = new ArrayList<>();
            configuracoes.forEach(c -> {
                if (c.getConfiguracao().getCodigo() == 9) {
                    c.getConfiguracao().getItens().forEach(i -> {
                        if ((Integer) i.getValue() == Integer.parseInt(c.getValor())) {
                            this.labelFonteDadosFinanceiros = i.getLabel();
                            setLabelFonteDadosFinanceiros(i.getLabel());
                        }
                    });
                }
            });
            for (int i = 0; i < grafico.length(); i++) {
                JSONObject graf = grafico.getJSONObject(i);
                String[] mesAno = graf.getString("mesAno").split("/");
                Integer mes = Integer.parseInt(mesAno[0]);
                Integer ano = Integer.parseInt(mesAno[1]);
                listaGrafico.add(new TicketMedioJSON(mes,
                        ano,
                        graf.getDouble("tmReceita"),
                        graf.getDouble("tmCompetencia"),
                        graf.getDouble("tmDespesa")));
            }
            ticket = new TicketMedioVO(new JSONObject(filtroDTO.getJsonDados()).getJSONObject("dados"));

        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    public EmpresaVO getEmpresaFiltro() {
        return (EmpresaVO) JSFUtilities.getManagedBean("BIControle.empresaFiltro");
    }

    public List getListaSelectItemEmpresa() {
        return (List) JSFUtilities.getManagedBean("BIControle.listaSelectItemEmpresa");
    }

    public void gravarConfiguracoes() {
        try {
            getFacade().getConfiguracaoBI().incluirPorBI(BIEnum.TICKET_MEDIO, getEmpresaFiltro().getCodigo(), configuracoes);
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    public boolean getIncluirBolsas() {
        try {
            return getConfiguracao(ConfiguracaoBIEnum.ALUNOS_BOLSA).getValorAsBoolean();
        } catch (Exception e) {
            return false;
        }
    }

    public boolean getConsiderarDependentesComoPagantes() {
        try {
            return getConfiguracao(ConfiguracaoBIEnum.CONTAR_DEPENDENTES_COMO_PAGANTES).getValorAsBoolean();
        } catch (Exception e) {
            return false;
        }
    }

    public boolean getMediaAtivos() {
        try {
            return getConfiguracao(ConfiguracaoBIEnum.ATIVOS).getValorAsInteger().equals(1);
        } catch (Exception e) {
            return false;
        }
    }

    private ConfiguracaoBIVO getConfiguracao(ConfiguracaoBIEnum cfg) {
        for (ConfiguracaoBIVO c : configuracoes) {
            if (c.getConfiguracao().equals(cfg)) {
                return c;
            }
        }
        return null;
    }

    public Date getDataBase() {
        return dataBase;
    }

    public String getDataBase_Apresentar() {
        DateFormat dfmt = new SimpleDateFormat("MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(dataBase);
        return dfmt.format(cal.getTime());
    }


    public void setDataBase(Date dataBase) {
        this.dataBase = dataBase;
    }

    public TicketMedioEnum getTipo() {
        return tipo;
    }

    public void setTipo(TicketMedioEnum tipo) {
        this.tipo = tipo;
    }

    public String getTituloTela() {
        return tituloTela;
    }

    public void setTituloTela(String tituloTela) {
        this.tituloTela = tituloTela;
    }

    public String getMesSelecionado() {
        return Uteis.getMesNomeReferencia(getDataBaseFiltroBI());
    }

    public List<TicketMedioJSON> getListaGrafico() {
        return listaGrafico;
    }

    public void setListaGrafico(List<TicketMedioJSON> listaGrafico) {
        this.listaGrafico = listaGrafico;
    }

    public List<ConfiguracaoBIVO> getConfiguracoes() {
        return configuracoes;
    }

    public void setConfiguracoes(List<ConfiguracaoBIVO> configuracoes) {
        this.configuracoes = configuracoes;
    }

    public String getLabelFonteDadosFinanceiros() {
        return this.labelFonteDadosFinanceiros;
    }

    public void setLabelFonteDadosFinanceiros(String labelFonteDadosFinanceiros) {
        this.labelFonteDadosFinanceiros = labelFonteDadosFinanceiros;
    }
}

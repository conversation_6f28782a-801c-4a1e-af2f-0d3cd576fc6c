package relatorio.controle.sad;

import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.comuns.basico.HistoricoParcelaOriginalTO;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ModalRenegociadasControle extends SuperControle {

    private List<HistoricoParcelaOriginalTO> listaLogRenegociacaoParcelasOriginais;
    private List<HistoricoParcelaOriginalTO> listaLogRenegociacaoParcelas;

    public ModalRenegociadasControle() {
    }

    public void abrirModalRenegociacoes() {
        boolean sucesso = true;
        setListaLogRenegociacaoParcelasOriginais(new ArrayList<>());
        setListaLogRenegociacaoParcelas(new ArrayList<>());
        limparMsg();
        setMsgAlert("");
        LogVO log = (LogVO) context().getExternalContext().getRequestMap().get("log");
        try {
            String valorAnterior = log != null ? log.getValorCampoAnterior() : "";
            Pattern pattern = Pattern.compile("Parcelas que foram renegociadas:\\s*Parcela=\\s*(\\d+);");
            Matcher matcher = pattern.matcher(valorAnterior);

            if (matcher.find()) {
                String codigoParcela = matcher.group(1);
                Set<HistoricoParcelaOriginalTO> historicoOriginais = getFacade().getLog().obterParcelasRenegociadasAPartirParcela(codigoParcela, true);
                if (historicoOriginais != null && !historicoOriginais.isEmpty()) {
                    historicoOriginais.forEach(historicoParcelaOriginalTO -> {
                        getListaLogRenegociacaoParcelasOriginais().add(historicoParcelaOriginalTO);
                    });
                    Ordenacao.ordenarLista(getListaLogRenegociacaoParcelasOriginais(), "id");
                }

                Set<HistoricoParcelaOriginalTO> historico = getFacade().getLog().obterParcelasRenegociadasAPartirParcela(codigoParcela, false);
                if (historico != null && !historico.isEmpty()) {
                    historico.forEach(historicoParcelaOriginalTO -> {
                        getListaLogRenegociacaoParcelas().add(historicoParcelaOriginalTO);
                    });
                    Ordenacao.ordenarLista(getListaLogRenegociacaoParcelas(), "id");
                }

                if((historico == null || historico.isEmpty()) && (historicoOriginais == null || historicoOriginais.isEmpty())) {
                    sucesso = false;
                    montarAviso("Não encontramos renegociações para parcela " + codigoParcela);
                }
            } else {
                sucesso = false;
                montarAviso("Não foi possível encontrar o histórico da parcela");
            }
        } catch (Exception e) {
            sucesso = false;
            montarErro("Não foi possível encontrar o histórico da parcela. " + e.getMessage());
        }

        if (sucesso) {
            setMsgAlert("Richfaces.showModalPanel('modalRenegociadas')");
        }
    }

    public void abrirModalRenegociacoesParcelasCanceladas() {
        boolean sucesso = true;
        setListaLogRenegociacaoParcelasOriginais(new ArrayList<>());
        setListaLogRenegociacaoParcelas(new ArrayList<>());
        limparMsg();
        setMsgAlert("");
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String codigoParcela = request.getParameter("movparcela");
        try {
            if (!UteisValidacao.emptyString(codigoParcela)) {
                Set<HistoricoParcelaOriginalTO> historicoOriginais = getFacade().getLog().obterParcelasRenegociadasAPartirParcela(codigoParcela, true);
                if (historicoOriginais != null && historicoOriginais.isEmpty()) {
                    String codigoAnterior = getFacade().getLog().obterMovParcelaAnteriorOrigemRenegociacaoPartirParcelaRenegociada(codigoParcela);
                    if (!UteisValidacao.emptyString(codigoAnterior)) {
                        codigoParcela = codigoAnterior;
                        historicoOriginais = getFacade().getLog().obterParcelasRenegociadasAPartirParcela(codigoParcela, true);
                    }
                }
                if (historicoOriginais != null && !historicoOriginais.isEmpty()) {
                    historicoOriginais.forEach(historicoParcelaOriginalTO -> {
                        getListaLogRenegociacaoParcelasOriginais().add(historicoParcelaOriginalTO);
                    });
                    Ordenacao.ordenarLista(getListaLogRenegociacaoParcelasOriginais(), "id");
                }

                Set<HistoricoParcelaOriginalTO> historico = getFacade().getLog().obterParcelasRenegociadasAPartirParcela(codigoParcela, false);
                if (historico != null && !historico.isEmpty()) {
                    historico.forEach(historicoParcelaOriginalTO -> {
                        getListaLogRenegociacaoParcelas().add(historicoParcelaOriginalTO);
                    });
                    Ordenacao.ordenarLista(getListaLogRenegociacaoParcelas(), "id");
                }

                if ((historico == null || historico.isEmpty()) && (historicoOriginais == null || historicoOriginais.isEmpty())) {
                    sucesso = false;
                    montarAviso("Não encontramos renegociações para parcela " + codigoParcela);
                }
            } else {
                sucesso = false;
                montarAviso("Não foi possível encontrar o histórico da parcela");
            }
        } catch (Exception e) {
            sucesso = false;
            montarErro("Não foi possível encontrar o histórico da parcela. " + e.getMessage());
        }

        if (sucesso) {
            setMsgAlert("Richfaces.showModalPanel('modalRenegociadas')");
        }
    }

    public void abrirModalRenegociacoesRelParcelas() {
        boolean sucesso = true;
        setListaLogRenegociacaoParcelasOriginais(new ArrayList<>());
        setListaLogRenegociacaoParcelas(new ArrayList<>());
        limparMsg();
        setMsgAlert("");
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String codigoParcela = request.getParameter("movparcela");
        try {

            if (!UteisValidacao.emptyString(codigoParcela)) {
                Set<HistoricoParcelaOriginalTO> historicoOriginais = getFacade().getLog().obterParcelasRenegociadasAPartirParcela(codigoParcela, true);
                if (historicoOriginais != null && historicoOriginais.isEmpty()) {
                    String codigoAnterior = getFacade().getLog().obterMovParcelaAnteriorOrigemRenegociacaoPartirParcelaRenegociada(codigoParcela);
                    if (!UteisValidacao.emptyString(codigoAnterior)) {
                        codigoParcela = codigoAnterior;
                        historicoOriginais = getFacade().getLog().obterParcelasRenegociadasAPartirParcela(codigoParcela, true);
                    }
                }
                if (historicoOriginais != null && !historicoOriginais.isEmpty()) {
                    historicoOriginais.forEach(historicoParcelaOriginalTO -> {
                        getListaLogRenegociacaoParcelasOriginais().add(historicoParcelaOriginalTO);
                    });
                    Ordenacao.ordenarLista(getListaLogRenegociacaoParcelasOriginais(), "id");
                }

                Set<HistoricoParcelaOriginalTO> historico = getFacade().getLog().obterParcelasRenegociadasAPartirParcela(codigoParcela, false);
                if (historico != null && !historico.isEmpty()) {
                    historico.forEach(historicoParcelaOriginalTO -> {
                        getListaLogRenegociacaoParcelas().add(historicoParcelaOriginalTO);
                    });
                    Ordenacao.ordenarLista(getListaLogRenegociacaoParcelas(), "id");
                }

                if ((historico == null || historico.isEmpty()) && (historicoOriginais == null || historicoOriginais.isEmpty())) {
                    sucesso = false;
                    montarAviso("Não encontramos renegociações para parcela " + codigoParcela);
                }
            } else {
                sucesso = false;
                montarAviso("Não foi possível encontrar o histórico da parcela");
            }
        } catch (Exception e) {
            sucesso = false;
            montarErro("Não foi possível encontrar o histórico da parcela. " + e.getMessage());
        }

        if (sucesso) {
            setMsgAlert("Richfaces.showModalPanel('modalRenegociadas')");
        }
    }

    public void atualizarModalRenegociacoesNext() {
        atualizarModalRenegociacoes(true);
    }

    public void atualizarModalRenegociacoesBack() {
        atualizarModalRenegociacoes(false);
    }

    public void atualizarModalRenegociacoes(boolean next) {
        String msgErro = "Não encontramos histórico de renegociação para parcela ";
        limparMsg();
        setMsgAlert("");
        try {
            HistoricoParcelaOriginalTO parcela = (HistoricoParcelaOriginalTO) context().getExternalContext().getRequestMap().get("parcela");
            if (parcela != null) {
                String idParcelaConsultar = next ? parcela.getId() : parcela.getIdRenegociada();
                if (!UteisValidacao.emptyString(idParcelaConsultar)) {
                    Set<HistoricoParcelaOriginalTO> historicoOriginais = getFacade().getLog().obterParcelasRenegociadasAPartirParcela(idParcelaConsultar, true);
                    if (historicoOriginais != null && !historicoOriginais.isEmpty()) {
                        setListaLogRenegociacaoParcelasOriginais(new ArrayList<>());
                        historicoOriginais.forEach(historicoParcelaOriginalTO -> {
                            getListaLogRenegociacaoParcelasOriginais().add(historicoParcelaOriginalTO);
                        });
                        Ordenacao.ordenarLista(getListaLogRenegociacaoParcelasOriginais(), "id");
                    }
                    Set<HistoricoParcelaOriginalTO> historico = getFacade().getLog().obterParcelasRenegociadasAPartirParcela(idParcelaConsultar, false);
                    if (historico != null && !historico.isEmpty()) {
                        setListaLogRenegociacaoParcelas(new ArrayList<>());
                        historico.forEach(historicoParcelaOriginalTO -> {
                            getListaLogRenegociacaoParcelas().add(historicoParcelaOriginalTO);
                        });
                        Ordenacao.ordenarLista(getListaLogRenegociacaoParcelas(), "id");
                    } else {
                        montarAviso(msgErro + idParcelaConsultar);
                    }
                } else {
                    montarErro(msgErro);
                }
            } else {
                montarErro(msgErro);
            }

        } catch (Exception e) {
            montarErro(msgErro + e.getMessage());
        }
    }

    public List<HistoricoParcelaOriginalTO> getListaLogRenegociacaoParcelas() {
        if(listaLogRenegociacaoParcelas == null) {
            listaLogRenegociacaoParcelas = new ArrayList<>();
        }
        return listaLogRenegociacaoParcelas;
    }

    public void setListaLogRenegociacaoParcelas(List<HistoricoParcelaOriginalTO> listaLogRenegociacaoParcelas) {
        this.listaLogRenegociacaoParcelas = listaLogRenegociacaoParcelas;
    }

    public List<HistoricoParcelaOriginalTO> getListaLogRenegociacaoParcelasOriginais() {
        if (listaLogRenegociacaoParcelasOriginais == null) {
            listaLogRenegociacaoParcelasOriginais = new ArrayList<>();
        }
        return listaLogRenegociacaoParcelasOriginais;
    }

    public void setListaLogRenegociacaoParcelasOriginais(List<HistoricoParcelaOriginalTO> listaLogRenegociacaoParcelasOriginais) {
        this.listaLogRenegociacaoParcelasOriginais = listaLogRenegociacaoParcelasOriginais;
    }

    public List<HistoricoParcelaOriginalTO> getListaLogRenegociacaoParcelasExportacao() {
        List<HistoricoParcelaOriginalTO> listaLogRenegociacaoParcelasExportacao = new ArrayList<>();
        if (getListaLogRenegociacaoParcelasOriginais() != null) {
            listaLogRenegociacaoParcelasExportacao.addAll(getListaLogRenegociacaoParcelasOriginais());
        }
        if (getListaLogRenegociacaoParcelas() != null) {
            listaLogRenegociacaoParcelasExportacao.addAll(getListaLogRenegociacaoParcelas());
        }
        return Ordenacao.ordenarLista(listaLogRenegociacaoParcelasExportacao, "id");
    }
}

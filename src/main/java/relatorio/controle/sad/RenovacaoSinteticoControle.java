/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.sad;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.GrupoTelaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteSimplificadoTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.HistoricoVinculoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.funcionais.Function;
import relatorio.controle.basico.BIControle;
import relatorio.negocio.comuns.basico.ResultadoBITO;
import relatorio.negocio.comuns.sad.GrupoRenovacaoConsultorTO;
import relatorio.negocio.comuns.sad.ListaPrevisaoRenovacaoTO;
import relatorio.negocio.comuns.sad.RenovacaoConsultorTO;
import relatorio.negocio.comuns.sad.RenovacaoContratoSinteticoTO;
import relatorio.negocio.comuns.sad.RenovacaoSinteticoVO;
import relatorio.negocio.comuns.sad.SinteticoColaboradorVO;
import relatorio.negocio.comuns.sad.SinteticoDuracaoTO;
import relatorio.negocio.comuns.sad.SinteticoEmpresaVO;
import relatorio.negocio.comuns.sad.SinteticoPlanoVO;
import relatorio.negocio.comuns.sad.SinteticoSituacaoRenovacaoVO;

import javax.faces.event.ActionEvent;
import java.io.File;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import static negocio.facade.jdbc.arquitetura.consulta.ConsultaUtil.concatenarValoresCollectionEmUmSet;

/**
 * <AUTHOR>
 */
public class RenovacaoSinteticoControle extends BIControle {

    private String l_PREVISTA = "R_PREVISTA";
    private String LISTA_PREVISTO_MES = "LISTA_PREVISTO_MES";
    private String R_ATIVOS = "R_ATIVOS";
    private String R_RENOVADA = "R_RENOVADA";
    private String R_DENTROMES = "R_DENTROMES";
    private String R_NAORENOVADA = "R_NAORENOVADA";
    private String R_TOTAL = "R_TOTAL";
    private String R_MESESPASSADOS = "R_MESESPASSADOS";
    private String R_MESESFUTUROS = "R_MESESFUTUROS";
    private String R_COMTOLERANCIA = "R_COMTOLERANCIA";
    private String R_NAORENOVADACOMTOLERANCIA = "R_NAORENOVADACOMTOLERANCIA";
    private String R_CLIENTESCOMMUITOSPROFESSORES = "R_CLIENTESCOMMUITOSPROFESSORES";

    private RenovacaoSinteticoVO renovacaoSinteticoVO;
    private Boolean bolsa;
    private Boolean cancelado;
    private Boolean trancado;
    private ConfiguracaoSistemaVO configuracaoSistemaVO;
    private List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacaoSintetico;
    private List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao1;
    private List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao2;
    private List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao3;
    private List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao4;
    private List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao5;
    private List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao6;
    private List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacaoSintetico;
    private List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao1;
    private List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao2;
    private List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao3;
    private List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao4;
    private List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao5;
    private List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao6;
    private List<SinteticoSituacaoRenovacaoVO> listaSituacaoRenovacao;
    private List<SinteticoColaboradorVO> listaConsultor;
    private List<ColaboradorVO> listaColaborador;
    private List<GrupoRenovacaoConsultorTO> listaResumoConsultor1;
    private List<GrupoRenovacaoConsultorTO> listaResumoConsultor2;
    private List<GrupoRenovacaoConsultorTO> listaResumoConsultor3;
    private List<GrupoRenovacaoConsultorTO> listaResumoConsultor4;
    private List<GrupoRenovacaoConsultorTO> listaResumoConsultor5;
    private List<GrupoRenovacaoConsultorTO> listaResumoConsultor6;
    private List<GrupoColaboradorVO> listaGrupos;
    private List<SinteticoPlanoVO> listaPlano;
    private List<SinteticoDuracaoTO> listaDuracoes;
    private EmpresaVO empresaVO;
    private Date dataInicio;
    private Date dataFim;
    private Date dataUltimaRenovacao;
    private String mes1;
    private String ano1;
    private String mes2;
    private String ano2;
    private String mes3;
    private String ano3;
    private String mes4;
    private String ano4;
    private String mes5;
    private String ano5;
    private String mes6;
    private String ano6;
    private String filtros;
    private int qtdePrevista;
    private int qtdeAtivos;
    private int qtdeRenovada;
    private int qtdeRenovadaDentroMes;
    private int qtdeNaoRenovada;
    private int qtdeRenovadaTotal;
    private int qtdeRenovadaMesesPassados;
    private int qtdeRenovadaMesesFuturos;
    private int qtdeRenovadaComTolerancia;
    private int qtdeNaoRenovadaComTolerancia;
    private int qtdeClientesComMuitosProfessores;
    private List<ClienteSimplificadoTO> listaClientes;
    private List<ClienteSimplificadoTO> listaRenovados;
    private List<ClienteSimplificadoTO> listaRenovadosTotal;
    private List<ClienteSimplificadoTO> listaRenovadosMesesPassados;
    private List<ClienteSimplificadoTO> listaRenovadosMesesFuturos;
    private List<ClienteSimplificadoTO> listaRenovadosDentroMes;
    private List<ClienteSimplificadoTO> listaPrevisaoMes;
    private List<ClienteSimplificadoTO> listaRenovadosPrevisaoMes;
    private List<ClienteSimplificadoTO> listaNaoRenovadosPrevisaoMes;
    private List<ClienteSimplificadoTO> listaRenovadosToleranciaPrevisaoMes;
    private List<ClienteSimplificadoTO> listaNaoRenovadosToleranciaPrevisaoMes;
    private List<ClienteSimplificadoTO> listaClientesComMuitosProfessores;
    private Date dataFimMesFiltro;
    private boolean mostrarGrupos = false;
    private Boolean marcarUsuario;
    private transient org.richfaces.component.html.HtmlDataTable dataTableRenovacao = null;
    private String relatorioAbrir = "";
    private List<SinteticoColaboradorVO> listaVinculosProfessor;
    private List<SinteticoColaboradorVO> listaVinculosConsultor;
    private List<SinteticoColaboradorVO> listaVinculosOutros;

    private boolean consultarTodasEmpresas = false;

    public void setMarcarUsuario(Boolean marcarUsuario) {
        this.marcarUsuario = marcarUsuario;
    }

    public Boolean getMarcarUsuario() throws Exception {
        if (getUsuarioLogado().getAdministrador()) {
            return false;
        }
        GrupoColaboradorParticipanteVO logado = obterColaboradorParticipanteVOLogado();
        if (logado == null) {
            return false;
        }
        return logado.getGrupoColaboradorParticipanteEscolhido();
    }

    private GrupoColaboradorParticipanteVO obterColaboradorParticipanteVOLogado() throws Exception {
        for (GrupoColaboradorVO grupoColaboradorVO : getListaGrupos()) {
            for (GrupoColaboradorParticipanteVO colaboradorParticipanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(colaboradorParticipanteVO.getColaboradorParticipante().getCodigo())) {
                    return colaboradorParticipanteVO;
                }
            }
        }
        return null;
    }

    public String getRelatorioAbrir() {
        return relatorioAbrir;
    }

    public void setRelatorioAbrir(String relatorioAbrir) {
        this.relatorioAbrir = relatorioAbrir;
    }

    public List<ClienteSimplificadoTO> getListaClientesComMuitosProfessores() {
        return listaClientesComMuitosProfessores;
    }

    public void setListaClientesComMuitosProfessores(List<ClienteSimplificadoTO> listaClientesComMuitosProfessores) {
        this.listaClientesComMuitosProfessores = listaClientesComMuitosProfessores;
    }

    public int getQtdeClientesComMuitosProfessores() {
        return qtdeClientesComMuitosProfessores;
    }

    public void setQtdeClientesComMuitosProfessores(int qtdeClientesComMuitosProfessores) {
        this.qtdeClientesComMuitosProfessores = qtdeClientesComMuitosProfessores;
    }

    public int getQtdeAtivos() {
        return qtdeAtivos;
    }

    public void setQtdeAtivos(int qtdeAtivos) {
        this.qtdeAtivos = qtdeAtivos;
    }

    private void adicionarListaOutroVinculos(SinteticoColaboradorVO sinteticoColaboradorVO) {
        for (SinteticoColaboradorVO objExistente : getListaVinculosOutros()) {
            if (objExistente.getChavePrimaria().equals(sinteticoColaboradorVO.getChavePrimaria())) {
                return;
            }
        }

        getListaVinculosOutros().add(sinteticoColaboradorVO);
    }

    public boolean isConsultarTodasEmpresas() {
        return consultarTodasEmpresas;
    }

    public void setConsultarTodasEmpresas(boolean consultarTodasEmpresas) {
        this.consultarTodasEmpresas = consultarTodasEmpresas;
    }

    enum TipoAuxiliar {
        RENOVADOS, NAO_RENOVADOS
    }

    public RenovacaoSinteticoControle() throws Exception {
        try {
            novo();
//            if (getEmpresaVO().getCodigo() != 0) {
//                geraDadosClientePorDataTelaInicial();
//            }
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public EmpresaVO getEmpresaFiltroBI(){
        return (EmpresaVO) JSFUtilities.getManagedBean("BIControle.empresaFiltro");
    }

    public List getListaSelectItemEmpresa() {
        return (List)JSFUtilities.getManagedBean("BIControle.listaSelectItemEmpresa");
    }
    @Override
    public boolean isConsultarPorTodosColaboradores(){
        return (Boolean)JSFUtilities.getManagedBean("BIControle.consultarPorTodosColaboradores");
    }

    public void atualizar(){
        carregarDados(false);
    }


    public void carregarTela(){
        gravarHistoricoAcessoBI(BIEnum.INDICE_RENOVACAO);
        carregar();
    }

    @Override
    public void carregar(){
        carregarDados(true);
    }

    public void carregarDados(boolean atualizarCache) {
        try {
            int atualizarBIs = (Integer) JSFUtilities.getManagedBean("BIControle.atualizarBIs");
            if (atualizarBIs != BIControle.NAO_ATUALIZAR) {
                atualizarCache = true;
            }

            ResultadoBITO resultado = obterResultadoBIDiaCache(BIEnum.INDICE_RENOVACAO);
            if (validarResultadoBIDia(resultado) || atualizarCache) {
                setDataInicio(getDataBaseFiltroBI());
                geraDadosClientePorDataTelaInicial();
                resultado = new ResultadoBITO();
                resultado.getResultadosBI().put(LISTA_PREVISTO_MES, getListaPrevisaoMes());
                resultado.getResultadosBI().put(R_ATIVOS, getQtdeAtivos());
                resultado.getResultadosBI().put(R_RENOVADA, getListaRenovadosPrevisaoMes());
                resultado.getResultadosBI().put(R_NAORENOVADA, getListaNaoRenovadosPrevisaoMes());
                resultado.getResultadosBI().put(R_DENTROMES, getListaRenovadosDentroMes());
                resultado.getResultadosBI().put(R_COMTOLERANCIA, getListaRenovadosToleranciaPrevisaoMes());
                resultado.getResultadosBI().put(R_NAORENOVADACOMTOLERANCIA, getListaNaoRenovadosToleranciaPrevisaoMes());
                resultado.getResultadosBI().put(R_MESESPASSADOS, getListaRenovadosMesesPassados());
                resultado.getResultadosBI().put(R_MESESFUTUROS, getListaRenovadosMesesFuturos());
                resultado.getResultadosBI().put(R_TOTAL, getQtdeRenovadaTotal());
                resultado.getResultadosBI().put(R_CLIENTESCOMMUITOSPROFESSORES, getQtdeClientesComMuitosProfessores());
                adicionarResultadoBI(BIEnum.INDICE_RENOVACAO, resultado);
            } else {
                montarRenovacaoCache(resultado);
            }
        } catch (Exception ex) {
            System.out.print(ex);
        }
    }
    public void montarRenovacaoCache(ResultadoBITO obj) {
        setDataUltimaRenovacao(processarRenovadasAtrasadas());
        setListaPrevisaoMes((List)obj.getResultadosBI().get(LISTA_PREVISTO_MES));
        setQtdePrevista(getListaPrevisaoMes().size());
        setQtdeAtivos((Integer)obj.getResultadosBI().get(R_ATIVOS));
        setListaRenovadosPrevisaoMes((List)obj.getResultadosBI().get(R_RENOVADA));
        setQtdeRenovada(getListaRenovadosPrevisaoMes().size());

        setListaNaoRenovadosPrevisaoMes(diferencaEntreListas(getListaPrevisaoMes(), getListaRenovadosPrevisaoMes()));
        setQtdeNaoRenovada(getListaNaoRenovadosPrevisaoMes().size());
        if (getQtdeNaoRenovada() < 0) {
            setQtdeNaoRenovada(0);
        }
        setListaRenovadosDentroMes((List)obj.getResultadosBI().get(R_DENTROMES));
        setQtdeRenovadaDentroMes(getListaRenovadosDentroMes().size());
        if (getQtdeRenovadaDentroMes() < 0) {
            setQtdeRenovadaDentroMes(0);
        }
        setListaRenovadosToleranciaPrevisaoMes((List)obj.getResultadosBI().get(R_COMTOLERANCIA));
        setQtdeRenovadaComTolerancia(getListaRenovadosToleranciaPrevisaoMes().size());

        setListaNaoRenovadosToleranciaPrevisaoMes(diferencaEntreListas(getListaPrevisaoMes(), getListaRenovadosToleranciaPrevisaoMes()));
        setQtdeNaoRenovadaComTolerancia(getListaNaoRenovadosToleranciaPrevisaoMes().size());
        if (getQtdeNaoRenovadaComTolerancia() < 0) {
            setQtdeNaoRenovadaComTolerancia(0);
        }


        setListaRenovadosMesesPassados((List)obj.getResultadosBI().get(R_MESESPASSADOS));
        setQtdeRenovadaMesesPassados(getListaRenovadosMesesPassados().size());
        setListaRenovadosMesesFuturos((List)obj.getResultadosBI().get(R_MESESFUTUROS));
        setQtdeRenovadaMesesFuturos(getListaRenovadosMesesFuturos().size());
        setListaRenovadosTotal(new ArrayList<ClienteSimplificadoTO>());
        getListaRenovadosTotal().addAll(getListaRenovadosMesesPassados());
        getListaRenovadosTotal().addAll(getListaRenovadosDentroMes());
        getListaRenovadosTotal().addAll(getListaRenovadosMesesFuturos());
        setQtdeRenovadaTotal(getListaRenovadosTotal().size());

        if(!getListaRenovadosPrevisaoMes().isEmpty()){
            setQtdeClientesComMuitosProfessores((Integer)obj.getResultadosBI().get(R_CLIENTESCOMMUITOSPROFESSORES));
        }

    }
    public void geraDadosClientePorDataTelaInicial() {
        try {
            if (getEmpresaFiltroBI().getCodigo() == 0 && !isPermiteConsultarTodasEmpresas()) {
                throw new Exception("O campo empresa deve ser informado");
            }
            // reinicia valores
            setDataInicio(Uteis.obterPrimeiroDiaMes(getDataInicio()));
            setDataFim(Uteis.obterUltimoDiaMesUltimaHora(getDataInicio()));
            setQtdePrevista(0);
            setQtdeAtivos(0);
            setQtdeRenovada(0);
            setQtdeNaoRenovada(0);
            setQtdeRenovadaTotal(0);
            setQtdeRenovadaMesesPassados(0);
            setQtdeRenovadaMesesFuturos(0);
            setQtdeRenovadaComTolerancia(0);
            setQtdeClientesComMuitosProfessores(0);
            setQtdeNaoRenovadaComTolerancia(0);
            setListaRenovados(new ArrayList());
            setListaRenovadosTotal(new ArrayList());
            setListaRenovadosMesesPassados(new ArrayList());
            setListaRenovadosMesesFuturos(new ArrayList());

            // datas usadas nas consultas
            setDataUltimaRenovacao(processarRenovadasAtrasadas());
            /*Date dataFimMesPassado = Uteis.obterDataAnterior(getDataInicio(), 1);
            Date dataInicioMesPassado = Uteis.obterPrimeiroDiaMes(dataFimMesPassado);*/

            // consulta colaboradores
//            if (getListaColaborador().isEmpty()) {
//                GrupoTelaControle grupoTelaControle = (GrupoTelaControle) context().getExternalContext().getSessionMap().get("GrupoTelaControle");
//                if (grupoTelaControle == null) {
//                    grupoTelaControle = new GrupoTelaControle();
//                }
//                setListaGrupos(grupoTelaControle.getListaGrupos());
//                setListaColaborador(grupoTelaControle.getListaColaboradores());
//                selecionarColaborador();
//            }

            final List<ColaboradorVO> colaboradoresEscolhidos = getColaboradoresEscolhidos();

            // consulta previstos 1
            setListaPrevisaoMes(getFacade().getContrato().clientesComContratosPrevistosPeriodo(getEmpresaFiltroBI().getCodigo(), getDataInicio(), getDataFim(), colaboradoresEscolhidos, false, null, null));
            setQtdePrevista(getListaPrevisaoMes().size());
            setQtdeAtivos(getFacade().getContrato().obterQuantidadeContratosAtivos(getEmpresaFiltroBI().getCodigo(), dataInicio));

            //lista renovados até este mês 2
            setListaRenovadosPrevisaoMes(getFacade().getContrato().clientesComContratosPrevistosPeriodo(getEmpresaFiltroBI().getCodigo(), getDataInicio(), getDataFim(), colaboradoresEscolhidos, true, null, null));
            setQtdeRenovada(getListaRenovadosPrevisaoMes().size());

            setListaNaoRenovadosPrevisaoMes(diferencaEntreListas(getListaPrevisaoMes(), getListaRenovadosPrevisaoMes()));
            setQtdeNaoRenovada(getListaNaoRenovadosPrevisaoMes().size());

            if (getQtdeNaoRenovada() < 0) {
                setQtdeNaoRenovada(0);
            }

            //lista renovados dentro do mês
            setListaRenovadosDentroMes(getFacade().getCliente().consultarClientePorContratoPrevistoRenovadoIndiferente(getDataInicio(), getDataFim(), getEmpresaFiltroBI().getCodigo(), getListaColaboradorFiltroBI(BIEnum.INDICE_RENOVACAO.name())));
            setQtdeRenovadaDentroMes(getListaRenovadosDentroMes().size());

            if (getQtdeRenovadaDentroMes() < 0) {
                setQtdeRenovadaDentroMes(0);
            }

            // consulta com tolerancia 3
            setListaRenovadosToleranciaPrevisaoMes(getFacade().getContrato().clientesComContratoPrevistoRenovadoPeriodo(getEmpresaFiltroBI().getCodigo(), getDataInicio(), getDataFim(), getDataUltimaRenovacao(), colaboradoresEscolhidos));
            setQtdeRenovadaComTolerancia(getListaRenovadosToleranciaPrevisaoMes().size());

            setListaNaoRenovadosToleranciaPrevisaoMes(diferencaEntreListas(getListaPrevisaoMes(), getListaRenovadosToleranciaPrevisaoMes()));
            setQtdeNaoRenovadaComTolerancia(getListaNaoRenovadosToleranciaPrevisaoMes().size());
            if (getQtdeNaoRenovadaComTolerancia() < 0) {
                setQtdeNaoRenovadaComTolerancia(0);
            }


            setListaRenovadosMesesPassados(getFacade().getCliente().consultarClientePorContratoRenovadoAtrasado(getEmpresaFiltroBI().getCodigo(), getDataInicio(), getDataFim(), getListaColaboradorFiltroBI(BIEnum.INDICE_RENOVACAO.name())));
            setQtdeRenovadaMesesPassados(getListaRenovadosMesesPassados().size());
            setListaRenovadosMesesFuturos(getFacade().getCliente().consultarClientePorContratoRenovadoAntecipado(getEmpresaFiltroBI().getCodigo(), getDataInicio(), getDataFim(), getListaColaboradorFiltroBI(BIEnum.INDICE_RENOVACAO.name())));
            setQtdeRenovadaMesesFuturos(getListaRenovadosMesesFuturos().size());

            // todas as renovações
            // de meses passados +
            // da previsao do mes +
            // de meses futuros
            getListaRenovadosTotal().addAll(getListaRenovadosMesesPassados());
            getListaRenovadosTotal().addAll(getListaRenovadosDentroMes());
            getListaRenovadosTotal().addAll(getListaRenovadosMesesFuturos());
            setQtdeRenovadaTotal(getListaRenovadosTotal().size());

            if(!getListaRenovadosPrevisaoMes().isEmpty()){
                setQtdeClientesComMuitosProfessores(getFacade().getCliente().contarQtdClienteVinculos(getListaRenovadosPrevisaoMes(), "PR", 1));
            }
            
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private List<ColaboradorVO> getColaboradoresEscolhidos() {
        final List<ColaboradorVO> colaboradoresEscolhidos = new ArrayList<ColaboradorVO>();
        for (final ColaboradorVO colaborador : getListaColaboradorFiltroBI(BIEnum.INDICE_RENOVACAO.name())) {
            if (colaborador.getColaboradorEscolhido()) {
                colaboradoresEscolhidos.add(colaborador);
            }
        }
        return colaboradoresEscolhidos;
    }

    private List<ClienteSimplificadoTO> diferencaEntreListas(List<ClienteSimplificadoTO> previsao, List<ClienteSimplificadoTO> renovados) {
        List<ClienteSimplificadoTO> diferenca = new ArrayList<ClienteSimplificadoTO>();
        for (ClienteSimplificadoTO cliente : previsao) {
            int vezesNaPrevisao = vezesClienteApareceNaLista(previsao, cliente);
            int vezesNaRenovacao = vezesClienteApareceNaLista(renovados, cliente);
            int vezesNaDiferenca = vezesClienteApareceNaLista(diferenca, cliente);
            int diferencaDeVezes = vezesNaPrevisao - vezesNaRenovacao;

            if (diferencaDeVezes > 0 && ((vezesNaDiferenca - diferencaDeVezes) != 0)) {
                diferenca.add(cliente);
            }
        }
        return diferenca;
    }

    private int vezesClienteApareceNaLista(List<ClienteSimplificadoTO> lista, ClienteSimplificadoTO cliente) {
        int i = 0;
        for (ClienteSimplificadoTO ClienteSimplificadoTO : lista) {
            if (ClienteSimplificadoTO.getCodigo() == cliente.getCodigo()) {
                i++;
            }
        }
        return i;
    }

    private boolean verificarSeListaContemCliente(List<ClienteSimplificadoTO> lista, ClienteSimplificadoTO cliente) {
        for (ClienteSimplificadoTO ClienteSimplificadoTO : lista) {
            if (ClienteSimplificadoTO.getCodigo() == cliente.getCodigo()) {
                return true;
            }
        }
        return false;
    }

    public void selecionarColaborador() throws Exception {
        // marca ou desmarca todos os iguais na lista de grupos
        for (GrupoColaboradorVO grupo : getListaGrupos()) {
            for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(participante.getColaboradorParticipante().getCodigo())) {
                    participante.setGrupoColaboradorParticipanteEscolhido(true);
                }
            }
        }
        for (ColaboradorVO co : getListaColaborador()) {
            if (co.equals(getUsuarioLogado().getColaboradorVO())) {
                co.setColaboradorEscolhidoRenovacao(true);
            }
        }

    }

    public void selecionarGrupoColaboradorParticipante() throws Exception {
        try {
            GrupoColaboradorVO obj = (GrupoColaboradorVO) context().getExternalContext().getRequestMap().get("grupoColaborador");
            obj.setAbrirSimpleTooglePanelPassivo(!obj.getAbrirSimpleTooglePanelPassivo());
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }
    }


    public void selecionarParticipante() throws Exception {
        GrupoColaboradorParticipanteVO obj = (GrupoColaboradorParticipanteVO) context().getExternalContext().getRequestMap().get("participante");
        if (obj == null) {
            obj = obterColaboradorParticipanteVOLogado();
            obj.setGrupoColaboradorParticipanteEscolhido(!obj.getGrupoColaboradorParticipanteEscolhido());
        }
        boolean selecionado = obj.getGrupoColaboradorParticipanteEscolhido();
        // marca ou desmarca todos os iguais na lista de grupos
        for (GrupoColaboradorVO grupo : getListaGrupos()) {
            for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                if (obj.getColaboradorParticipante().equals(participante.getColaboradorParticipante())) {
                    participante.setGrupoColaboradorParticipanteEscolhido(selecionado);
                }
            }
        }
        // marca ou desmarca todos os iguais na lista de nao repetidos
        for (ColaboradorVO colaborador : getListaColaborador()) {
            if (obj.getColaboradorParticipante().getCodigo().equals(colaborador.getCodigo())) {
                colaborador.setColaboradorEscolhidoRenovacao(selecionado);
            }
        }
        geraDadosClientePorDataTelaInicial();
    }

    public void selecionarConsultor() {
        try {
            if (getListaConsultor().size() > 0) {
                for (SinteticoColaboradorVO co : getListaConsultor()) {
                    if (co.getChavePrimaria().equals(getUsuarioLogado().getColaboradorVO().getCodigo())) {
                        co.setMarcado(true);
                    }
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarUsuarioLogado() throws Exception {
        if (getUsuarioLogado() != null && getUsuarioLogado().getCodigo() != 0) {
            getRenovacaoSinteticoVO().setUsuarioVO(new UsuarioVO());
            getRenovacaoSinteticoVO().getUsuarioVO().setCodigo(getUsuarioLogado().getCodigo());
            getRenovacaoSinteticoVO().getUsuarioVO().setNome(getUsuarioLogado().getNome());
            getRenovacaoSinteticoVO().getUsuarioVO().setAdministrador(getUsuarioLogado().getAdministrador());
        }
    }

    public void inicializarEmpresaLogado() throws Exception {
        if (getEmpresaLogado() != null && getEmpresaLogado().getCodigo() != 0) {
            getEmpresaVO().setCodigo(getEmpresaLogado().getCodigo());
            getEmpresaVO().setNome(getEmpresaLogado().getNome());
            getRenovacaoSinteticoVO().getEmpresa().setCodigo(getEmpresaLogado().getCodigo());
            getRenovacaoSinteticoVO().getEmpresa().setNome(getEmpresaLogado().getNome());
        }
    }

    public void inicializarEmpresaLogado(RenovacaoSinteticoVO rs) throws Exception {
        if (getEmpresaLogado() != null && getEmpresaLogado().getCodigo() != 0) {
            rs.getEmpresa().setCodigo(getEmpresaLogado().getCodigo());
            rs.getEmpresa().setNome(getEmpresaLogado().getNome());
        }
    }

    public void inicializarConfiguracaoSistema() throws Exception {
        setConfiguracaoSistemaVO(getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(
                1, Uteis.NIVELMONTARDADOS_ROBO));
    }

    public void novo() throws Exception {

        setRenovacaoSinteticoVO(new RenovacaoSinteticoVO());
        setEmpresaVO(new EmpresaVO());
        setListaVinculosProfessor(new ArrayList<SinteticoColaboradorVO>());
        setListaVinculosConsultor(new ArrayList<SinteticoColaboradorVO>());
        setListaVinculosOutros(new ArrayList<SinteticoColaboradorVO>());
        setConfiguracaoSistemaVO(new ConfiguracaoSistemaVO());
        setListaCompletaPrevisaoRenovacaoSintetico(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaPrevisaoRenovacao1(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaPrevisaoRenovacao2(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaPrevisaoRenovacao3(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaPrevisaoRenovacao4(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaPrevisaoRenovacao5(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaPrevisaoRenovacao6(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaCompletaPrevisaoRenovacaoSintetico(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaCompletaPrevisaoRenovacao1(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaCompletaPrevisaoRenovacao2(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaCompletaPrevisaoRenovacao3(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaCompletaPrevisaoRenovacao4(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaCompletaPrevisaoRenovacao5(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaCompletaPrevisaoRenovacao6(new ArrayList<ListaPrevisaoRenovacaoTO>());
        setListaConsultor(new ArrayList<SinteticoColaboradorVO>());
        setListaColaborador(new ArrayList<ColaboradorVO>());
        setListaResumoConsultor1(new ArrayList<GrupoRenovacaoConsultorTO>());
        setListaResumoConsultor2(new ArrayList<GrupoRenovacaoConsultorTO>());
        setListaResumoConsultor3(new ArrayList<GrupoRenovacaoConsultorTO>());
        setListaResumoConsultor4(new ArrayList<GrupoRenovacaoConsultorTO>());
        setListaResumoConsultor5(new ArrayList<GrupoRenovacaoConsultorTO>());
        setListaResumoConsultor6(new ArrayList<GrupoRenovacaoConsultorTO>());
        setListaSituacaoRenovacao(new ArrayList<SinteticoSituacaoRenovacaoVO>());
        setBolsa(false);
        setCancelado(false);
        setTrancado(false);
        setListaPlano(new ArrayList<>());
        setDataInicio(Uteis.obterPrimeiroDiaMes(negocio.comuns.utilitarias.Calendario.hoje()));
        setDataFim(Uteis.obterUltimoDiaMesUltimaHora(negocio.comuns.utilitarias.Calendario.hoje()));
        inicializarConfiguracaoSistema();
        inicializarEmpresaLogado();
        super.montarListaEmpresasComItemTodas();
        inicializarUsuarioLogado();
        setConsultarTodasEmpresas(permissao("ConsultarInfoTodasEmpresas"));

    }
    public void obterEmpresaEscolhida() throws Exception {
        if (getEmpresaVO().getCodigo() != 0) {
            setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        } else {
            setEmpresaVO(new EmpresaVO());
        }
    }

    /**
     * Joao Alcides
     * 12/04/2012
     */
    private void limparFiltros() {

        setBolsa(false);
        setTrancado(false);
        setCancelado(false);
        for (SinteticoColaboradorVO sin : getListaConsultor()) {
            sin.setMarcado(false);
        }
        for (SinteticoPlanoVO plano : getListaPlano()) {
            plano.setMarcado(false);
        }
        for (SinteticoDuracaoTO duracao : getListaDuracoes()) {
            duracao.setMarcado(false);
        }
        for (SinteticoSituacaoRenovacaoVO ssRen : getListaSituacaoRenovacao()) {
            ssRen.setMarcado(false);
        }

    }

    /**
     * Joao Alcides
     * 12/04/2012
     */
    public void verificarColaboradoresSelecionados() {
        for (ColaboradorVO co : getListaColaboradorFiltroBI(BIEnum.INDICE_RENOVACAO.name())) {
            if (co.getColaboradorEscolhidoRenovacao()) {
                for (SinteticoColaboradorVO sin : getListaConsultor()) {
                    if (sin.getChavePrimaria().equals(co.getCodigo())) {
                        sin.setMarcado(true);
                    }
                }
            }
        }
    }

    public void apresentarAlunosPrevisaoMes(ActionEvent evt)  {
        setMsgAlert("");
        try {

            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");

            if (loginControle.getPermissaoAcessoMenuVO().getRelatorioDePrevisaoRenovacao()){
                inicializarPrevisaoVindoDoBI(evt);
                setMsgAlert("abrirPopup('renovacaoAnaliticoForm.jsp', 'Renovação', 780, 595);");
            } else {
                mostrarListaPrevisaoMes();
                setMsgAlert("abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 780, 595);");
            }
        }catch (Exception e){
            montarErro(e);
        }

    }


    public void inicializarPrevisaoVindoDoBI(ActionEvent evt) throws Exception {
        final String tipo = evt.getComponent().getAttributes().get("tipo").toString();
        limparFiltros();
        //setar primeiro dia do mes
        setDataInicio(Uteis.obterPrimeiroDiaMes(getDataFim()));
        //consultar o periodo
        consultarClientesPorData();
        //verificar colaboradores marcados
        verificarColaboradoresSelecionados();

        if (tipo.equals("renovados")) {
            for (SinteticoSituacaoRenovacaoVO ssRen : getListaSituacaoRenovacao()) {
                if (ssRen.getNome().equals("AN") || ssRen.getNome().equals("RT") || ssRen.getNome().equals("ND")) {
                    ssRen.setMarcado(true);
                }
            }
        }
        if (tipo.equals("naorenovados")) {
            for (SinteticoSituacaoRenovacaoVO ssRen : getListaSituacaoRenovacao()) {
                if (ssRen.getNome().equals("RV") || ssRen.getNome().equals("RA") || ssRen.getNome().equals("RD")) {
                    ssRen.setMarcado(true);
                }
            }
        }
        //atualizar
        atualizarPrevisaoRenovacaoComFiltros();
    }

    public void consultarClientesPorData() {
        try {
            if (getUsuarioLogado().getAdministrador() && empresaVO.getCodigo().equals(0)) {
                limparListasPrevisao();
                setMensagemDetalhada("msg_erro", "Informe a Empresa.");
                return;
            }

            Integer codigoEmpresa = empresaVO.getCodigo();
            if (getEmpresaFiltroBI() != null) {
                codigoEmpresa = getEmpresaFiltroBI().getCodigo();
            }

            if (dataInicio.compareTo(dataFim) > 0) {
                limparListasPrevisao();
                setMensagemDetalhada("msg_erro", "A Data de Início deve ser menor que a Data De Término para Pesquisa.");
                return;
            }

            List<Date> listaDate = Uteis.getMesesEntreDatasAleatorias(dataInicio, dataFim);
            if (listaDate.size() > 6) {
                limparListasPrevisao();
                setMensagemDetalhada("msg_erro", "O campo Período informado não pode ser maior que 6 meses");
                return;
            }

            limparListasPrevisao();

            int i = 1;
            for (Date date : listaDate) {
                Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(date);
                dataFin = (dataFin.after(dataFim) ? dataFim : dataFin);
                dataFimMesFiltro = Calendario.getDataComHoraZerada(dataFin);
                Integer mes = Uteis.getMesData(dataFin);
                Integer ano = Uteis.getAnoData(dataFin);
                consultarContratoPrevisoRenovar(date, dataFin, i, mes.toString(), ano.toString(), codigoEmpresa);
                i++;
            }

            Ordenacao.ordenarLista(getListaConsultor(), "nome");
            gerarListaConsultorProfessor();

            montarDatasetBarra();
            montarDatasetPizza();
            montarDatasetBarraRenovados();
            montarDatasetPizzaRenovados();
            montarDatasetBarraNaoRenovados();
            montarDatasetPizzaNaoRenovados();

            obterEmpresaEscolhida();

            setFiltros("");
            if (getEmpresaLogado().getCodigo() != 0) {
                setFiltros("Período de " + Uteis.getData(dataInicio) + " Até " + Uteis.getData(dataFim));
            } else if (getEmpresaVO().getCodigo() != 0) {
                setFiltros("Empresa: " + getEmpresaLista() + " Período de " + Uteis.getData(dataInicio) + " Até " + Uteis.getData(dataFim));
            }

            mostrarFiltroPesquisarTambem();
            obterFiltros();
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
            setDataInicio(dataInicio);
        } catch (Exception e) {
            limparListasPrevisao();
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }
    }

    private void limparListasPrevisao() {
        setListaPrevisaoRenovacao1(new ArrayList<>());
        setListaPrevisaoRenovacao2(new ArrayList<>());
        setListaPrevisaoRenovacao3(new ArrayList<>());
        setListaPrevisaoRenovacao4(new ArrayList<>());
        setListaPrevisaoRenovacao5(new ArrayList<>());
        setListaPrevisaoRenovacao6(new ArrayList<>());
        setListaCompletaPrevisaoRenovacao1(new ArrayList<>());
        setListaCompletaPrevisaoRenovacao2(new ArrayList<>());
        setListaCompletaPrevisaoRenovacao3(new ArrayList<>());
        setListaCompletaPrevisaoRenovacao4(new ArrayList<>());
        setListaCompletaPrevisaoRenovacao5(new ArrayList<>());
        setListaCompletaPrevisaoRenovacao6(new ArrayList<>());
        setListaConsultor(new ArrayList<>());
        setListaSituacaoRenovacao(new ArrayList<>());
        setListaPlano(new ArrayList<>());
        setListaDuracoes(new ArrayList<>());
        setListaResumoConsultor1(new ArrayList<>());
        setListaResumoConsultor2(new ArrayList<>());
        setListaResumoConsultor3(new ArrayList<>());
        setListaResumoConsultor4(new ArrayList<>());
        setListaResumoConsultor5(new ArrayList<>());
        setListaResumoConsultor6(new ArrayList<>());
    }

    public void mostrarFiltroPesquisarTambem() {
        if (getBolsa()) {
            setFiltros(getFiltros() + "Pesquisar Também Contratos com Bolsa ");
        }
        if (getCancelado()) {
            setFiltros(getFiltros() + "Pesquisar Também Contratos Cancelados ");
        }
        if (getTrancado()) {
            setFiltros(getFiltros() + "Pesquisar Também Contratos Trancados ");
        }

    }

    private String getEmpresaLista() throws Exception {
        setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        return getEmpresaVO().getNome();
    }

    /**
     * a partir de uma lista de colaboradores monta os grupos aos quais eles pertencem
     *
     * @param lista
     */
    private void montarConsultoresEmGrupo(List<RenovacaoConsultorTO> lista, List<GrupoRenovacaoConsultorTO> grupos) throws Exception {
        List<ColaboradorVO> colaboradores = new ArrayList<ColaboradorVO>();
        // pega os colaboradores do resumo
        for (RenovacaoConsultorTO resumo : lista) {
            colaboradores.add(resumo.getColaborador());
        }
        // a partir dos colaboradores do resumo, monta os grupos
        List<GrupoColaboradorVO> grupoColaboradores = GrupoTelaControle.separarColaboradoresEmGrupos(colaboradores, getEmpresaLogado().getCodigo());
        // monta os dados do relatório usando os grupos recem montados
        for (GrupoColaboradorVO grupo : grupoColaboradores) {
            // monta os dados de um grupo
            GrupoRenovacaoConsultorTO aux = new GrupoRenovacaoConsultorTO();
            aux.setDescricao(grupo.getDescricao());
            // preenche o grupo com os participantes
            for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                // sabendo quem deve ser o participante do grupo pega o resumo equivalente
                RenovacaoConsultorTO aux2 = new RenovacaoConsultorTO();
                aux2.setColaborador(participante.getColaboradorParticipante());
                int indice = lista.indexOf(aux2);
                aux.getListaConsultores().add(lista.get(indice));
                aux.setQtdePrevista(aux.getQtdePrevista() + lista.get(indice).getQtdePrevista());
                aux.setQtdeRenovados(aux.getQtdeRenovados() + lista.get(indice).getQtdeRenovados());
            }
            // adiciona o grupo recem montado na lista final
            grupos.add(aux);
        }
    }

    public void consultarContratoPrevisoRenovar(Date dataIn, Date dataFi,
            Integer numeroLista, String mes, String ano, Integer codigoEmpresa) throws Exception {

        ListaPrevisaoRenovacaoTO lp = new ListaPrevisaoRenovacaoTO();
        List<RenovacaoConsultorTO> lista = new ArrayList<RenovacaoConsultorTO>();
        obterContratoPrevistoRenovacao(lp, dataIn, dataFi, lista, codigoEmpresa);
        if (lp.getListaRenovacaoSinteticoVOs().size() > 0 && numeroLista.equals(1)) {
            lp.setAno(ano);
            lp.setMes(mes);
            getListaPrevisaoRenovacao1().add(lp);
            getListaCompletaPrevisaoRenovacao1().add(lp);
            montarConsultoresEmGrupo(lista, getListaResumoConsultor1());
            setAno1(ano);
            setMes1(mes);

        } else if (lp.getListaRenovacaoSinteticoVOs().size() > 0 && numeroLista.equals(2)) {
            lp.setAno(ano);
            lp.setMes(mes);
            getListaPrevisaoRenovacao2().add(lp);
            getListaCompletaPrevisaoRenovacao2().add(lp);
            montarConsultoresEmGrupo(lista, getListaResumoConsultor2());
            setAno2(ano);
            setMes2(mes);
        } else if (lp.getListaRenovacaoSinteticoVOs().size() > 0 && numeroLista.equals(3)) {
            lp.setAno(ano);
            lp.setMes(mes);
            getListaPrevisaoRenovacao3().add(lp);
            getListaCompletaPrevisaoRenovacao3().add(lp);
            montarConsultoresEmGrupo(lista, getListaResumoConsultor3());
            setAno3(ano);
            setMes3(mes);
        } else if (lp.getListaRenovacaoSinteticoVOs().size() > 0 && numeroLista.equals(4)) {
            lp.setAno(ano);
            lp.setMes(mes);
            getListaPrevisaoRenovacao4().add(lp);
            getListaCompletaPrevisaoRenovacao4().add(lp);
            montarConsultoresEmGrupo(lista, getListaResumoConsultor4());
            setAno4(ano);
            setMes4(mes);
        } else if (lp.getListaRenovacaoSinteticoVOs().size() > 0 && numeroLista.equals(5)) {
            lp.setAno(ano);
            lp.setMes(mes);
            getListaPrevisaoRenovacao5().add(lp);
            getListaCompletaPrevisaoRenovacao5().add(lp);
            montarConsultoresEmGrupo(lista, getListaResumoConsultor5());
            setAno5(ano);
            setMes5(mes);
        } else if (lp.getListaRenovacaoSinteticoVOs().size() > 0 && numeroLista.equals(6)) {
            lp.setAno(ano);
            lp.setMes(mes);
            getListaPrevisaoRenovacao6().add(lp);
            getListaCompletaPrevisaoRenovacao6().add(lp);
            montarConsultoresEmGrupo(lista, getListaResumoConsultor6());
            setAno6(ano);
            setMes6(mes);
        }
    }

    public void gerarListaConsultorProfessor(){
        getListaVinculosConsultor().clear();
        getListaVinculosProfessor().clear();
        getListaVinculosOutros().clear();

        for(SinteticoColaboradorVO sinteticoColaboradorVO : getListaConsultor()){
            if(sinteticoColaboradorVO.getTipoColaborador().equals("CO")){
                getListaVinculosConsultor().add(sinteticoColaboradorVO);
            } else if(sinteticoColaboradorVO.getTipoColaborador().equals("PR")) {
                getListaVinculosProfessor().add(sinteticoColaboradorVO);
            } else {
                adicionarListaOutroVinculos(sinteticoColaboradorVO);
            }
        }
    }

    // TODO: estou deixando esse trecho de codigo que deve ser removido futuramente, pro caso de termos algum problema nessa consulta e ser necessario voltar a versao
    /*public void obterContratoPrevistoRenovacaoAntigo(ListaPrevisaoRenovacaoTO lp, Date dataIn, Date dataFi, List<RenovacaoConsultorTO> lista) throws Exception {
        List<ContratoVO> listaContrato = getFacade().getContrato().consultarContratoPrevistoRenovacao(
                dataIn, dataFi, getEmpresaVO().getCodigo(),
                getBolsa(), getTrancado(), getCancelado(), false,
                Uteis.NIVELMONTARDADOS_ROBO);

        for (ContratoVO contrato : listaContrato) {
            RenovacaoContratoSinteticoTO renovacaoContratoSinteticoTO = new RenovacaoContratoSinteticoTO();
            renovacaoContratoSinteticoTO.setContratoVO(contrato);
            carregarDadosRenovacaoContrato(dataIn, dataFi, lp, lista, renovacaoContratoSinteticoTO);
        }
    }*/

    public void obterContratoPrevistoRenovacao(ListaPrevisaoRenovacaoTO lp, Date dataIn, Date dataFi, List<RenovacaoConsultorTO> lista, Integer codigoEmpresa) throws Exception {
        List<RenovacaoContratoSinteticoTO> renovacaoContratoSinteticoTOs = getFacade().getRenovacaoSinteticoFacade().consultarRenovacaoContratosSinteticos(
                dataIn, dataFi, codigoEmpresa,
                getBolsa(), getTrancado(), getCancelado());

        Set<Integer> idsContratosResponsavelRenovacaoMatricula = concatenarValoresCollectionEmUmSet(renovacaoContratoSinteticoTOs, new Function<RenovacaoContratoSinteticoTO, Integer>() {
            @Override
            public Integer apply(RenovacaoContratoSinteticoTO renovacaoContratoSinteticoTO) {
                Integer contratoResponsavelRenovacaoMatricula = renovacaoContratoSinteticoTO.getContratoVO().getContratoResponsavelRenovacaoMatricula();
                return contratoResponsavelRenovacaoMatricula != 0 ? contratoResponsavelRenovacaoMatricula : null;
            }
        });

        Map<Integer, ContratoVO> contratoVOMappedByChave = getFacade().getContrato().mapearContratoPorCodigo(idsContratosResponsavelRenovacaoMatricula);
        for (RenovacaoContratoSinteticoTO renovacaoContratoSinteticoTO : renovacaoContratoSinteticoTOs) {
            carregarDadosRenovacaoContrato(dataIn, dataFi, lp, lista, renovacaoContratoSinteticoTO, contratoVOMappedByChave);
        }
    }

    private void carregarDadosRenovacaoContrato(Date dataIn, Date dataFi, ListaPrevisaoRenovacaoTO lp, List<RenovacaoConsultorTO> lista, RenovacaoContratoSinteticoTO renovacaoContratoSinteticoTO,
                                                Map<Integer, ContratoVO> contratoVOMappedByChave) throws Exception {
        ContratoVO contrato = renovacaoContratoSinteticoTO.getContratoVO();
        ClienteSimplificadoTO clienteSimplificadoTO =
                new ClienteSimplificadoTO(contrato.getCliente(), contrato.getPessoa());

        RenovacaoSinteticoVO renovacaoSinteticoVO = new RenovacaoSinteticoVO();

        renovacaoSinteticoVO.setContrato(contrato);
        renovacaoSinteticoVO.setCheckin4semanas(renovacaoContratoSinteticoTO.getCheckin4semanas());
        renovacaoSinteticoVO.setDiasSemAcesso(renovacaoContratoSinteticoTO.getDiasSemAcesso());
        renovacaoSinteticoVO.setUltimoContatoCRM(renovacaoContratoSinteticoTO.getUltimoContatoCRM());
        renovacaoSinteticoVO.setMediaAcessos4semanas(renovacaoContratoSinteticoTO.getMediaAcessos4semanas());
        renovacaoSinteticoVO.setProfessores(renovacaoContratoSinteticoTO.getProfessores());
        renovacaoSinteticoVO.setCliente(clienteSimplificadoTO);
        if (renovacaoContratoSinteticoTO.getHistoricosVinculoVO() != null) {
            renovacaoSinteticoVO.setVinculos(renovacaoContratoSinteticoTO.getHistoricosVinculoVO());
        } else {
            renovacaoSinteticoVO.setVinculos(getFacade().getHistoricoVinculo().consultarPorCodigoClientePeriodo(dataIn, dataFi, renovacaoSinteticoVO.getCliente().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL));
        }
        inicializarEmpresaLogado(renovacaoSinteticoVO);
        renovacaoSinteticoVO.obterSituacaoPrevisaRenovacao(getCancelado(), getTrancado());
        gerarListaSituacaoRenovacao(renovacaoSinteticoVO);
        gerarListaSinteticoColaborador(renovacaoSinteticoVO);
        gerarListaSinteticoDuracao(renovacaoSinteticoVO);
        gerarListaSinteticoPlano(renovacaoSinteticoVO);
        gerarListaSinteticoEmpresa(renovacaoSinteticoVO);

        if (contrato.getContratoResponsavelRenovacaoMatricula() != 0) {
            // posiciona o contrato de renovacao, se houver
            ContratoVO aux = contratoVOMappedByChave.get(contrato.getContratoResponsavelRenovacaoMatricula());

            renovacaoSinteticoVO.setPlanoRenovado(Uteis.getData(aux.getDataLancamento()));
            renovacaoSinteticoVO.setPlanoRenovadoDate(aux.getDataLancamento());
            renovacaoSinteticoVO.setPlanoRenovadoDuracao(aux.getContratoDuracao().getNumeroMeses().toString());
            renovacaoSinteticoVO.setPlanoRenovadoModalidades(aux.getNomeModalidadesConcatenado());
            renovacaoSinteticoVO.setPlanoRenovadoResponsavelCadastro(aux.getResponsavelContrato().getNomeAbreviado());
        }

        if (renovacaoSinteticoVO.getSituacaoRenovacao().equals("AN")) {
            lp.setQtdRenovacoesAntecipadas(lp.getQtdRenovacoesAntecipadas() + 1);
        } else if (renovacaoSinteticoVO.getSituacaoRenovacao().equals("RT")) {
            lp.setQtdRenovacoesAtrasadas(lp.getQtdRenovacoesAtrasadas() + 1);
        } else if (renovacaoSinteticoVO.getSituacaoRenovacao().equals("ND")) {
            lp.setQtdRenovacoesDia(lp.getQtdRenovacoesDia() + 1);
        } else if (renovacaoSinteticoVO.getSituacaoRenovacao().equals("RD")) {
            lp.setQtdNRenovadosDesistentes(lp.getQtdNRenovadosDesistentes() + 1);
        } else if (renovacaoSinteticoVO.getSituacaoRenovacao().equals("RA")) {
            lp.setQtdNRenovadosAVencer(lp.getQtdNRenovadosAVencer() + 1);
        } else if (renovacaoSinteticoVO.getSituacaoRenovacao().equals("RV")) {
            lp.setQtdNRenovadosVencidos(lp.getQtdNRenovadosVencidos() + 1);
        } else if (renovacaoSinteticoVO.getSituacaoRenovacao().equals("CA")) {
            lp.setQtdNRenovadosCancelados(lp.getQtdNRenovadosCancelados() + 1);
        } else if (renovacaoSinteticoVO.getSituacaoRenovacao().equals("TR")) {
            lp.setQtdNRenovadosTrancados(lp.getQtdNRenovadosTrancados() + 1);
        }

        if (renovacaoSinteticoVO.getSituacaoRenovacao().equals("AN")
                || renovacaoSinteticoVO.getSituacaoRenovacao().equals("RT")
                || renovacaoSinteticoVO.getSituacaoRenovacao().equals("ND")) {
            lp.setQtdTotalRenovacoes(lp.getQtdTotalRenovacoes() + 1);
            totalizaConsultoresVinculo(renovacaoSinteticoVO.getVinculos(), renovacaoSinteticoVO.getCliente(), TipoAuxiliar.RENOVADOS, lista);
        } else {
            lp.setQtdTotalNRenovados(lp.getQtdTotalNRenovados() + 1);
            totalizaConsultoresVinculo(renovacaoSinteticoVO.getVinculos(), renovacaoSinteticoVO.getCliente(), TipoAuxiliar.NAO_RENOVADOS, lista);
        }

        lp.setQtdTotalPrevisaoRenovacao(lp.getQtdTotalPrevisaoRenovacao() + 1);
        lp.getListaRenovacaoSinteticoVOs().add(renovacaoSinteticoVO);
    }

    /**
     * totaliza para cada consultor do vinculo a quantidade de contratos de sua responsabilidade
     *
     * @param lista
     * @param tipo
     */
    private void totalizaConsultoresVinculo(List<HistoricoVinculoVO> lista, ClienteSimplificadoTO cliente, TipoAuxiliar tipo, List<RenovacaoConsultorTO> listaRenovacao) throws Exception{
        if (listaRenovacao == null) {
            return;
        }
        if (lista == null) {
            return;
        }
        Ordenacao.ordenarLista(lista, "dataRegistro");
        Collections.reverse(lista);

        List<Integer> vinculosSaida = new ArrayList<Integer>();
        List<Integer> adicionados = new ArrayList<Integer>();
        // percorre a lista de vinculo do cliente do contrato
        for (HistoricoVinculoVO aux : lista) {
            if (!getFacade().getHistoricoVinculo().verificarVinculoNaData(dataFimMesFiltro,cliente.getCodigo(), aux.getColaborador().getCodigo())) {
                continue;
            }
            if (aux.getTipoHistoricoVinculo().equals("SD") && Calendario.menorOuIgual(Calendario.getDataComHoraZerada(aux.getDataRegistro()), dataFimMesFiltro)) {
                vinculosSaida.add(aux.getColaborador().getCodigo());
                continue;
            }
            if (aux.getTipoHistoricoVinculo().equals("EN") && Calendario.menorOuIgual(Calendario.getDataComHoraZerada(aux.getDataRegistro()), dataFimMesFiltro)
                    && !vinculosSaida.contains(aux.getColaborador().getCodigo())) {
                try {
//                    aux.setColaborador(getFacade().getColaborador().consultarPorChavePrimaria(aux.getColaborador().getCodigo(), Uteis.NIVELMONTARDADOS_INDICERENOVACAO));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                // monta o bean da lista
                RenovacaoConsultorTO consultor = new RenovacaoConsultorTO();
                consultor.setColaborador(aux.getColaborador());
                consultor.setTipoColaborador(aux.getTipoColaborador());
                // verifica se o consultor ja está na lista
                int indice = listaRenovacao.indexOf(consultor);
                // se nao está, adiciona
                if (indice < 0) {
                    listaRenovacao.add(consultor);
                } // se está apenas pega o consultor da lista
                else {
                    consultor = listaRenovacao.get(indice);
                }
                // totaliza os renovados
                if (tipo == TipoAuxiliar.RENOVADOS && !adicionados.contains(consultor.getColaborador().getCodigo())) {
                    consultor.setQtdeRenovados(consultor.getQtdeRenovados() + 1);
                }
                // totaliza os nao renovados
                if (tipo == TipoAuxiliar.NAO_RENOVADOS && !adicionados.contains(consultor.getColaborador().getCodigo())) {
                    consultor.setQtdeNaoRenovados(consultor.getQtdeNaoRenovados() + 1);
                }

                // totaliza todos os previstos
                if (!adicionados.contains(consultor.getColaborador().getCodigo())) {
                    consultor.setQtdePrevista(consultor.getQtdePrevista() + 1);
                    adicionados.add(consultor.getColaborador().getCodigo());
                }

            }

        }
    }

    public void gerarListaSituacaoRenovacao(RenovacaoSinteticoVO r) throws Exception {
        SinteticoSituacaoRenovacaoVO sin = new SinteticoSituacaoRenovacaoVO();
        sin.setNome(r.getSituacaoRenovacao());
        sin.adicionarObjClienteSituacaoVOs(getListaSituacaoRenovacao(), sin);
    }

    public void gerarListaSinteticoEmpresa(RenovacaoSinteticoVO r) throws Exception {
        SinteticoEmpresaVO sin = new SinteticoEmpresaVO();
        sin.setNome(r.getEmpresa().getNome());
        sin.setChavePrimaria(r.getEmpresa().getCodigo());
    }

    public void gerarListaSinteticoDuracao(RenovacaoSinteticoVO r) throws Exception {
        SinteticoDuracaoTO sinteticoDuracaoTO = new SinteticoDuracaoTO();
        int duracao = r.getContrato().getContratoDuracao().getNumeroMeses();
        sinteticoDuracaoTO.setNome(duracao == 1 ? "1 Mês" : duracao + " Meses");
        sinteticoDuracaoTO.setChavePrimaria(duracao);
        sinteticoDuracaoTO.adicionarObjSituacaoDuracaoTO(getListaDuracoes(), sinteticoDuracaoTO);
        duracao = 0;
        try {
            if (r.getContrato().getContratoResponsavelRenovacaoMatricula() != 0) {
                duracao = getFacade().getContratoDuracao().consultarContratoDuracoes(r.getContrato().getContratoResponsavelRenovacaoMatricula(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).getNumeroMeses();
            }
        } catch (Exception e) {
            // nao ha necessidade de tratar esta excecao !!
        }
        r.getContrato().setDuracaoRenovacao(duracao);
    }

    public void gerarListaSinteticoPlano(RenovacaoSinteticoVO r) throws Exception {
        SinteticoPlanoVO sin = new SinteticoPlanoVO();
        Integer chavePrimaria = r.getContrato().getPlano().getCodigo();
        sin.setNome(r.getPlanoApresentar());
        sin.setChavePrimaria(chavePrimaria);
        sin.adicionarObjClienteSituacaoVOs(getListaPlano(), sin);
    }

    public void sobreporColaboradorExistenteLista(SinteticoColaboradorVO obj, int lista){
        if(lista ==1) {
            for (SinteticoColaboradorVO sinteticoColaboradorVO : getListaVinculosConsultor()) {
                if (sinteticoColaboradorVO.getCodigo() == obj.getCodigo()) {
                    return;
                }
            }
            getListaVinculosConsultor().add(obj);
        }
        if(lista ==2) {
            for (SinteticoColaboradorVO sinteticoColaboradorVO : getListaVinculosProfessor()) {
                if (sinteticoColaboradorVO.getCodigo() == obj.getCodigo()) {
                    return;
                }
            }
            getListaVinculosProfessor().add(obj);
        }
    }
//   public void dividirProfessorColaborador(HistoricoVinculoVO hv,SinteticoColaboradorVO sin) throws Exception{
//     if(hv.getColaborador().getListaTipoColaboradorVOs().size() > 1){
//
//            for(TipoColaboradorVO tipoColaboradorVO : hv.getColaborador().getListaTipoColaboradorVOs()){
//                if(tipoColaboradorVO.getDescricao().equals("CO")){
//                    sobreporColaboradorExistenteLista(sin,1);
//
//                }
//                if(tipoColaboradorVO.getDescricao().equals("PR")){
//
//                        sobreporColaboradorExistenteLista(sin,2);
//
//
//                }
//            }
//     }else if(hv.getColaborador().getListaTipoColaboradorVOs().size() == 1){
//            if(hv.getColaborador().getListaTipoColaboradorVOs().get(0).getDescricao().equals("CO")){
//
//                    sobreporColaboradorExistenteLista( sin,1);
//
//            }else if(hv.getColaborador().getListaTipoColaboradorVOs().get(0).getDescricao().equals("PR")){
//
//                    sobreporColaboradorExistenteLista(sin,2);
//
//            }
//     }
//    }
    public void gerarListaSinteticoColaborador(RenovacaoSinteticoVO r) throws Exception {
        Iterator i = r.getVinculos().iterator();
        while (i.hasNext()) {
            HistoricoVinculoVO vi = (HistoricoVinculoVO) i.next();
            if(!(vi.getTipoHistoricoVinculo().equals("SD") && Calendario.menor(vi.getDataRegistro(), r.getContrato().getDataPrevistaRenovar()))){ // se for um historico de saída  anterior a data prevista para renovar, colaborador não entra na lista
                SinteticoColaboradorVO sin = new SinteticoColaboradorVO();
                sin.setNome(vi.getColaborador().getPessoa().getNome());
                sin.setChavePrimaria(vi.getColaborador().getCodigo());
                sin.setTipoColaborador(vi.getTipoColaborador());
                sin.adicionarObjClienteSituacaoVOs(getListaConsultor(), sin);

              //dividirProfessorColaborador(vi, sin);
               // adicionarObjClienteSituacaoVOsProfessor(sin);
              //  adicionarObjClienteSituacaoVOsColaborador(sin);
              //  getListaConsultorProfessor().add(sin);
                if (vi.getTipoColaborador().equals(TipoColaboradorEnum.CONSULTOR.getSigla()) && UteisValidacao.emptyNumber(r.getConsultor().getCodigo())) {
                    r.setConsultor(vi.getColaborador());
                }
            }
        }

    }
    public void adicionarObjClienteSituacaoVOsProfessor(SinteticoColaboradorVO obj) throws Exception {
             int index = 0;

                if(obj.getTipoColaborador().equals("PR")){

                    Iterator i = getListaVinculosProfessor().iterator();

                        if (!getListaVinculosProfessor().contains(obj.getChavePrimaria())) {

                            getListaVinculosProfessor().add(obj);
                        }


        }
    }
    public void adicionarObjClienteSituacaoVOsColaborador(SinteticoColaboradorVO obj) throws Exception {
            int index = 0;

                if(obj.getTipoColaborador().equals("CO")){

                    if (!getListaVinculosConsultor().contains(obj.getChavePrimaria())) {
                         getListaVinculosConsultor().add(obj);


                    }
                }


     }

    public void atualizarPrevisaoRenovacaoComFiltros() {
        try {
            Date dataInicioRel = new Date();
            dataInicioRel = getDataInicio();
            if (dataInicioRel.compareTo(getDataFim()) > 0) {
                throw new Exception("A Data de Início deve ser menor que a Data De Término para Pesquisa.");
            }
            List<RenovacaoConsultorTO> aux = new ArrayList<>();
            setListaResumoConsultor1(new ArrayList<>());
            setListaResumoConsultor2(new ArrayList<>());
            setListaResumoConsultor3(new ArrayList<>());
            setListaResumoConsultor4(new ArrayList<>());
            setListaResumoConsultor5(new ArrayList<>());
            setListaResumoConsultor6(new ArrayList<>());
            obterClientesDeAcordoComFiltros();
            List<Date> listaDate = Uteis.getMesesEntreDatasAleatorias(dataInicioRel, dataFim);

            if (!getListaPrevisaoRenovacao1().isEmpty() && listaDate.size() > 0) {
                Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(0));
                dataFimMesFiltro = Calendario.getDataComHoraZerada((dataFin.after(dataFim) ? dataFim : dataFin));
                obterQuantidadeClientes(getListaPrevisaoRenovacao1(), aux);
                montarConsultoresEmGrupo(aux, getListaResumoConsultor1());
            }
            aux = new ArrayList<RenovacaoConsultorTO>();
            if (!getListaPrevisaoRenovacao2().isEmpty() && listaDate.size() > 1) {
                Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(1));
                dataFimMesFiltro = Calendario.getDataComHoraZerada((dataFin.after(dataFim) ? dataFim : dataFin));
                obterQuantidadeClientes(getListaPrevisaoRenovacao2(), aux);
                montarConsultoresEmGrupo(aux, getListaResumoConsultor2());
            }
            aux = new ArrayList<RenovacaoConsultorTO>();
            if (!getListaPrevisaoRenovacao3().isEmpty() && listaDate.size() > 2) {
                Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(2));
                dataFimMesFiltro = Calendario.getDataComHoraZerada((dataFin.after(dataFim) ? dataFim : dataFin));
                obterQuantidadeClientes(getListaPrevisaoRenovacao3(), aux);
                montarConsultoresEmGrupo(aux, getListaResumoConsultor3());
            }
            aux = new ArrayList<RenovacaoConsultorTO>();
            if (!getListaPrevisaoRenovacao4().isEmpty() && listaDate.size() > 3) {
                Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(3));
                dataFimMesFiltro = Calendario.getDataComHoraZerada((dataFin.after(dataFim) ? dataFim : dataFin));
                obterQuantidadeClientes(getListaPrevisaoRenovacao4(), aux);
                montarConsultoresEmGrupo(aux, getListaResumoConsultor4());
            }
            aux = new ArrayList<RenovacaoConsultorTO>();
            if (!getListaPrevisaoRenovacao5().isEmpty() && listaDate.size() > 4) {
                Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(4));
                dataFimMesFiltro = Calendario.getDataComHoraZerada((dataFin.after(dataFim) ? dataFim : dataFin));
                obterQuantidadeClientes(getListaPrevisaoRenovacao5(), aux);
                montarConsultoresEmGrupo(aux, getListaResumoConsultor5());
            }
            aux = new ArrayList<RenovacaoConsultorTO>();
            if (!getListaPrevisaoRenovacao6().isEmpty() && listaDate.size() > 5) {
                Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(5));
                dataFimMesFiltro = Calendario.getDataComHoraZerada((dataFin.after(dataFim) ? dataFim : dataFin));
                obterQuantidadeClientes(getListaPrevisaoRenovacao6(), aux);
                montarConsultoresEmGrupo(aux, getListaResumoConsultor6());
            }

            setFiltros("");
            if (getEmpresaLogado().getCodigo() != 0) {
                setFiltros("Período de: " + Uteis.getData(dataInicioRel) + " Até: " + Uteis.getData(dataFim) + "");
            } else if (getEmpresaVO().getCodigo() != 0) {
                setFiltros("Empresa: " + getEmpresaLista() + " Período de: " + Uteis.getData(dataInicioRel) + " Até: " + Uteis.getData(dataFim) + "");
            }
            mostrarFiltroPesquisarTambem();
            obterFiltros();
            montarDatasetBarra();
            montarDatasetPizza();
            montarDatasetBarraRenovados();
            montarDatasetPizzaRenovados();
            montarDatasetBarraNaoRenovados();
            montarDatasetPizzaNaoRenovados();

            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
            setDataInicio(dataInicioRel);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void obterClientesDeAcordoComFiltrosTelaInicial() throws Exception {
        setListaPrevisaoRenovacaoSintetico(new ArrayList<ListaPrevisaoRenovacaoTO>());
        if (!getListaCompletaPrevisaoRenovacaoSintetico().isEmpty()) {
            obterListaPrevisaoRenovacao(getListaCompletaPrevisaoRenovacaoSintetico(), getListaPrevisaoRenovacaoSintetico());
        }
    }

    public void obterFiltros() {
        obterFiltroConsultor();
        obterFiltroPlano();
        obterFiltroDuracao();
        obterFiltroSituacaoSituacaoRenovacao();
    }

    public void obterClientesDeAcordoComFiltros() throws Exception {
        setListaPrevisaoRenovacao1(new ArrayList<>());
        setListaPrevisaoRenovacao2(new ArrayList<>());
        setListaPrevisaoRenovacao3(new ArrayList<>());
        setListaPrevisaoRenovacao4(new ArrayList<>());
        setListaPrevisaoRenovacao5(new ArrayList<>());
        setListaPrevisaoRenovacao6(new ArrayList<>());

        List<Date> listaDate = Uteis.getMesesEntreDatasAleatorias(dataInicio, dataFim);

        if (!getListaCompletaPrevisaoRenovacao1().isEmpty()) {
            Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(0));
            dataFimMesFiltro = Calendario.getDataComHoraZerada((dataFin.after(dataFim) ? dataFim : dataFin));
            obterListaPrevisaoRenovacao(getListaCompletaPrevisaoRenovacao1(), getListaPrevisaoRenovacao1());


        }
        if (!getListaCompletaPrevisaoRenovacao2().isEmpty()) {
            Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(1));
            dataFimMesFiltro = (dataFin.after(dataFim) ? dataFim : dataFin);
            obterListaPrevisaoRenovacao(getListaCompletaPrevisaoRenovacao2(), getListaPrevisaoRenovacao2());

        }
        if (!getListaCompletaPrevisaoRenovacao3().isEmpty()) {
            Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(2));
            dataFimMesFiltro = (dataFin.after(dataFim) ? dataFim : dataFin);
            obterListaPrevisaoRenovacao(getListaCompletaPrevisaoRenovacao3(), getListaPrevisaoRenovacao3());

        }
        if (!getListaCompletaPrevisaoRenovacao4().isEmpty()) {
            Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(3));
            dataFimMesFiltro = (dataFin.after(dataFim) ? dataFim : dataFin);
            obterListaPrevisaoRenovacao(getListaCompletaPrevisaoRenovacao4(), getListaPrevisaoRenovacao4());

        }
        if (!getListaCompletaPrevisaoRenovacao5().isEmpty()) {
            Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(4));
            dataFimMesFiltro = (dataFin.after(dataFim) ? dataFim : dataFin);
            obterListaPrevisaoRenovacao(getListaCompletaPrevisaoRenovacao5(), getListaPrevisaoRenovacao5());
        }
        if (!getListaCompletaPrevisaoRenovacao6().isEmpty()) {
            Date dataFin = Uteis.obterUltimoDiaMesUltimaHora(listaDate.get(5));
            dataFimMesFiltro = (dataFin.after(dataFim) ? dataFim : dataFin);
            obterListaPrevisaoRenovacao(getListaCompletaPrevisaoRenovacao6(), getListaPrevisaoRenovacao6());
        }
    }

    public void obterListaPrevisaoRenovacao(List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisao, List<ListaPrevisaoRenovacaoTO> listaPrevisao) throws Exception {
        boolean adicionarObj = false;
        for (ListaPrevisaoRenovacaoTO lp : listaCompletaPrevisao) {
            ListaPrevisaoRenovacaoTO lpNovo = new ListaPrevisaoRenovacaoTO();
            for (RenovacaoSinteticoVO obj : lp.getListaRenovacaoSinteticoVOs()) {
                adicionarObj = validarFiltroSituacao(obj);
                if (!adicionarObj) {
                    continue;
                }
                adicionarObj = validarFiltroPlano(obj);
                if (!adicionarObj) {
                    continue;
                }
                adicionarObj = validarFiltroDuracao(obj);
                if (!adicionarObj) {
                    continue;
                }
                adicionarObj = validarFiltroConsultor(obj);
                if (!adicionarObj) {
                    /*System.out.println(String.format("Não adicionou contrato %d por consultor",
                    new Object[]{
                    obj.getContrato().getCodigo()
                    }));*/
                    continue;
                }
                lpNovo.getListaRenovacaoSinteticoVOs().add(obj);
            }
            if (!lpNovo.getListaRenovacaoSinteticoVOs().isEmpty()) {
                listaPrevisao.add(lpNovo);
            }
        }
    }

    public Boolean validarFiltroSituacao(RenovacaoSinteticoVO obj) {
        Boolean estaNaSituacao = false;
        Boolean entrouNaOpcaoMarcado = false;
        if (getListaSituacaoRenovacao().size() > 0) {
            Iterator i = getListaSituacaoRenovacao().iterator();
            while (i.hasNext()) {
                SinteticoSituacaoRenovacaoVO sin = (SinteticoSituacaoRenovacaoVO) i.next();
                if (sin.getMarcado()) {
                    if (obj.getSituacaoRenovacao().equals(sin.getNome())) {
                        return true;
                    } else {
                        estaNaSituacao = false;
                    }
                    entrouNaOpcaoMarcado = true;
                }
            }
        }
        return !entrouNaOpcaoMarcado;
    }

    public Boolean validarFiltroPlano(RenovacaoSinteticoVO obj) {
        Boolean entrouNaOpcaoMarcado = false;
        if (getListaPlano().size() > 0) {
            Iterator i = getListaPlano().iterator();
            while (i.hasNext()) {
                SinteticoPlanoVO sin = (SinteticoPlanoVO) i.next();
                if (sin.getMarcado()) {
                    if (obj.getContrato().getPlano().getCodigo().intValue() == sin.getChavePrimaria().intValue()) {
                        return true;
                    }
                    entrouNaOpcaoMarcado = true;
                }
            }
        }
        return !entrouNaOpcaoMarcado;
    }

    public Boolean validarFiltroDuracao(RenovacaoSinteticoVO obj) {
        Boolean entrouNaOpcaoMarcado = false;
        if (getListaDuracoes().size() > 0) {
            Iterator i = getListaDuracoes().iterator();
            while (i.hasNext()) {
                SinteticoDuracaoTO sin = (SinteticoDuracaoTO) i.next();
                if (sin.getMarcado()) {
                    if (obj.getContrato().getContratoDuracao().getNumeroMeses().intValue() == sin.getChavePrimaria().intValue()) {
                        return true;
                    }
                    entrouNaOpcaoMarcado = true;
                }
            }
        }
        return !entrouNaOpcaoMarcado;
    }

    public Boolean validarFiltroConsultor(RenovacaoSinteticoVO obj) throws Exception {
        Boolean estaNaSituacao = false;
        Boolean entrouNaOpcaoMarcado = false;
        if (getListaConsultor().size() > 0) {
            Iterator i = getListaConsultor().iterator();
            while (i.hasNext()) {
                SinteticoColaboradorVO sin = (SinteticoColaboradorVO) i.next();
                if (sin.getMarcado()) {
                    if (getFacade().getHistoricoVinculo().verificarVinculoNaData(dataFimMesFiltro, obj.getCliente().getCodigo(), sin.getChavePrimaria(), sin.getTipoColaborador())) {
                        return true;
                    } else {
                        estaNaSituacao = false;
                    }
                    entrouNaOpcaoMarcado = true;
                }
            }
        }
        return !entrouNaOpcaoMarcado;
    }


    public void obterFiltroSituacaoSituacaoRenovacao() {
        List<SinteticoSituacaoRenovacaoVO> sinteticoSituacaoRenovacaoVOS = getListaSituacaoRenovacao();
        if (sinteticoSituacaoRenovacaoVOS.size() <= 0) {
            return;
        }

        for (SinteticoSituacaoRenovacaoVO sin : sinteticoSituacaoRenovacaoVOS) {
            if (sin.getMarcado()) {
                setFiltros(getFiltros() + "Situação: " + sin.getNome_Apresentar() + "");
            }
        }
    }

    public void obterFiltroPlano() {
        List<SinteticoPlanoVO> sinteticoPlanoVOS = getListaPlano();
        if (sinteticoPlanoVOS.size() <= 0) {
            return;
        }

        for (SinteticoPlanoVO sin : getListaPlano()) {
            if (sin.getMarcado()) {
                setFiltros(getFiltros() + "Plano: " + sin.getNome() + "");
            }
        }
    }
    public void obterFiltroDuracao() {
        List<SinteticoDuracaoTO> sinteticoDuracao = getListaDuracoes();
        if (sinteticoDuracao.size() <= 0) {
            return;
        }

        for (SinteticoDuracaoTO sin : getListaDuracoes()) {
            if (sin.getMarcado()) {
                setFiltros(getFiltros() + "Duração: " + sin.getNome() + "");
            }
        }
    }

    public void obterFiltroConsultor() {
        List<SinteticoColaboradorVO> sinteticoColaboradorVOS = getListaConsultor();
        if (sinteticoColaboradorVOS.size() <= 0) {
            return;
        }

        for (SinteticoColaboradorVO sin : sinteticoColaboradorVOS) {
            if (!sin.getMarcado()) {
                continue;
            }

            if (sin.getTipoColaborador().equals("CO")) {
                setFiltros(getFiltros() + " Consultor: " + sin.getNome() + "");
            } else if (sin.getTipoColaborador().equals("PR")) {
                setFiltros(getFiltros() + " Professor: " + sin.getNome() + "");
            }
        }
    }

    public Integer getNrColunaConsultor() {
        Integer x = getListaConsultor().size() / 5;
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public Integer getTamanhoListaConsultor() {
        Integer x = getListaConsultor().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public Integer getTamanhoListaColaborador() {
        Integer x = getListaColaborador().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public Integer getNrColunaSituacaoRenovacao() {
        Integer x = getListaSituacaoRenovacao().size() / 5;
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public Integer getTamanhoListaSituacaoRenovacao() {
        Integer x = getListaSituacaoRenovacao().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public Integer getNrColunaPlano() {
        Integer x = (getListaPlano().size()/4);

        if (x == 0) {
            return 1;
        }else
        if(getListaPlano().size()%4 > 0){
            x++;
        }
        return x;
    }

    public Integer getTamanhoListaPlano() {
        Integer x = getListaPlano().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public Integer getTamanhoListaDuracoes() {
        Integer x = getListaDuracoes().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public void montarDatasetBarra() throws Exception {
        montarDataBarraListaPrevista(getListaPrevisaoRenovacao1(), false, true);
        montarDataBarraListaPrevista(getListaPrevisaoRenovacao2(), false, true);
        montarDataBarraListaPrevista(getListaPrevisaoRenovacao3(), false, true);
        montarDataBarraListaPrevista(getListaPrevisaoRenovacao4(), false, true);
        montarDataBarraListaPrevista(getListaPrevisaoRenovacao5(), false, true);
        montarDataBarraListaPrevista(getListaPrevisaoRenovacao6(), false, true);
    }

    public void montarDatasetPizza() throws Exception {
        montarDataPizzaListaPrevista(getListaPrevisaoRenovacao1(), false, true);
        montarDataPizzaListaPrevista(getListaPrevisaoRenovacao2(), false, true);
        montarDataPizzaListaPrevista(getListaPrevisaoRenovacao3(), false, true);
        montarDataPizzaListaPrevista(getListaPrevisaoRenovacao4(), false, true);
        montarDataPizzaListaPrevista(getListaPrevisaoRenovacao5(), false, true);
        montarDataPizzaListaPrevista(getListaPrevisaoRenovacao6(), false, true);
    }

    public void montarDataBarraListaPrevista(List<ListaPrevisaoRenovacaoTO> listaPrevisao, Boolean barra, Boolean pizza) throws Exception {

        for (ListaPrevisaoRenovacaoTO lp : listaPrevisao) {
            lp.getDataSetBarra().addValue(lp.getQtdTotalNRenovados(), "Não Renovados", "");
            lp.getDataSetBarra().addValue(lp.getQtdTotalRenovacoes(), "Renovados", "");
            lp.setBarra(barra);
            lp.setPizza(pizza);
        }
    }

    public void montarDataPizzaListaPrevista(List<ListaPrevisaoRenovacaoTO> listaPrevisao, Boolean barra, Boolean pizza) throws Exception {

        for (ListaPrevisaoRenovacaoTO lp : listaPrevisao) {
            lp.getDataSetPizza().setValue("Não Renovados", lp.getQtdTotalNRenovados());
            lp.getDataSetPizza().setValue("Renovados", lp.getQtdTotalRenovacoes());
            lp.setBarra(barra);
            lp.setPizza(pizza);
        }
    }

    public void obterQuantidadeClientesTelaInicial(List<ListaPrevisaoRenovacaoTO> listaPrevisao) {
        for (ListaPrevisaoRenovacaoTO lp : listaPrevisao) {
            for (RenovacaoSinteticoVO r : lp.getListaRenovacaoSinteticoVOs()) {
                if (r.getContrato().getDataRenovarRealizada() != null) {
                    lp.setQtdTotalRenovacoes(lp.getQtdTotalRenovacoes() + 1);
                } else {
                    lp.setQtdTotalNRenovados(lp.getQtdTotalNRenovados() + 1);
                }
                lp.setQtdTotalPrevisaoRenovacao(lp.getQtdTotalPrevisaoRenovacao() + 1);
            }
        }
    }

    public void obterQuantidadeClientes(List<ListaPrevisaoRenovacaoTO> listaPrevisao, List<RenovacaoConsultorTO> listaResumo) throws Exception{
        for (ListaPrevisaoRenovacaoTO lp : listaPrevisao) {
            for (RenovacaoSinteticoVO r : lp.getListaRenovacaoSinteticoVOs()) {
                if (r.getSituacaoRenovacao().equals("AN")) {
                    lp.setQtdRenovacoesAntecipadas(lp.getQtdRenovacoesAntecipadas() + 1);
                } else if (r.getSituacaoRenovacao().equals("RT")) {
                    lp.setQtdRenovacoesAtrasadas(lp.getQtdRenovacoesAtrasadas() + 1);
                } else if (r.getSituacaoRenovacao().equals("RD")) {
                    lp.setQtdNRenovadosDesistentes(lp.getQtdNRenovadosDesistentes() + 1);
                } else if (r.getSituacaoRenovacao().equals("RA")) {
                    lp.setQtdNRenovadosAVencer(lp.getQtdNRenovadosAVencer() + 1);
                } else if (r.getSituacaoRenovacao().equals("ND")) {
                    lp.setQtdRenovacoesDia(lp.getQtdRenovacoesDia() + 1);
                } else if (r.getSituacaoRenovacao().equals("RV")) {
                    lp.setQtdNRenovadosVencidos(lp.getQtdNRenovadosVencidos() + 1);
                } else if (r.getSituacaoRenovacao().equals("CA")) {
                    lp.setQtdNRenovadosCancelados(lp.getQtdNRenovadosCancelados() + 1);
                } else if (r.getSituacaoRenovacao().equals("TR")) {
                    lp.setQtdNRenovadosTrancados(lp.getQtdNRenovadosTrancados() + 1);
                }
                if (r.getSituacaoRenovacao().equals("RT") || r.getSituacaoRenovacao().equals("ND") || r.getSituacaoRenovacao().equals("AN")) {
                    totalizaConsultoresVinculo(r.getVinculos(), r.getCliente(), TipoAuxiliar.RENOVADOS, listaResumo);
                    lp.setQtdTotalRenovacoes(lp.getQtdTotalRenovacoes() + 1);
                } else {
                    totalizaConsultoresVinculo(r.getVinculos(), r.getCliente(), TipoAuxiliar.NAO_RENOVADOS, listaResumo);
                    lp.setQtdTotalNRenovados(lp.getQtdTotalNRenovados() + 1);
                }
                lp.setQtdTotalPrevisaoRenovacao(lp.getQtdTotalPrevisaoRenovacao() + 1);
            }
        }
    }

    public void alterarParaBarra() throws Exception {
        setFiltros("");
        if (getEmpresaLogado().getCodigo() != 0) {
            setFiltros("Período de: " + Uteis.getData(dataInicio)
                    + " Até: " + Uteis.getData(dataFim) + "");
        } else if (getEmpresaVO().getCodigo() != 0) {
            setFiltros("Empresa: " + getEmpresaLista()
                    + " Período de: " + Uteis.getData(dataInicio)
                    + " Até: " + Uteis.getData(dataFim) + "");
        }
        obterClientesDeAcordoComFiltros();
        if (!getListaPrevisaoRenovacao1().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao1(), null);
            montarDataBarraListaPrevista(getListaPrevisaoRenovacao1(), true, false);
            montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao1(), true, false);
            montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao1(), true, false);
        }
        if (!getListaPrevisaoRenovacao2().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao2(), null);
            montarDataBarraListaPrevista(getListaPrevisaoRenovacao2(), true, false);
            montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao2(), true, false);
            montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao2(), true, false);
        }
        if (!getListaPrevisaoRenovacao3().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao3(), null);
            montarDataBarraListaPrevista(getListaPrevisaoRenovacao3(), true, false);
            montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao3(), true, false);
            montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao3(), true, false);
        }
        if (!getListaPrevisaoRenovacao4().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao4(), null);
            montarDataBarraListaPrevista(getListaPrevisaoRenovacao4(), true, false);
            montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao4(), true, false);
            montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao4(), true, false);
        }
        if (!getListaPrevisaoRenovacao5().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao5(), null);
            montarDataBarraListaPrevista(getListaPrevisaoRenovacao5(), true, false);
            montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao5(), true, false);
            montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao5(), true, false);
        }
        if (!getListaPrevisaoRenovacao6().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao6(), null);
            montarDataBarraListaPrevista(getListaPrevisaoRenovacao6(), true, false);
            montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao6(), true, false);
            montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao6(), true, false);
        }
        obterFiltros();
        setMensagemDetalhada("");
    }

    public void alterarParaPizza() throws Exception {
        setFiltros("");
        if (getEmpresaLogado().getCodigo() != 0) {
            setFiltros("Período de: " + Uteis.getData(dataInicio)
                    + " Até: " + Uteis.getData(dataFim) + "");
        } else if (getEmpresaVO().getCodigo() != 0) {
            setFiltros("Empresa: " + getEmpresaLista()
                    + " Período de: " + Uteis.getData(dataInicio)
                    + " Até: " + Uteis.getData(dataFim) + "");
        }
        obterClientesDeAcordoComFiltros();

        if (!getListaPrevisaoRenovacao1().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao1(), null);
            montarDataPizzaListaPrevista(getListaPrevisaoRenovacao1(), false, true);
            montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao1(), false, true);
            montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao1(), false, true);
        }
        if (!getListaPrevisaoRenovacao2().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao2(), null);
            montarDataPizzaListaPrevista(getListaPrevisaoRenovacao2(), false, true);
            montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao2(), false, true);
            montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao2(), false, true);
        }
        if (!getListaPrevisaoRenovacao3().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao3(), null);
            montarDataPizzaListaPrevista(getListaPrevisaoRenovacao3(), false, true);
            montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao3(), false, true);
            montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao3(), false, true);
        }
        if (!getListaPrevisaoRenovacao4().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao4(), null);
            montarDataPizzaListaPrevista(getListaPrevisaoRenovacao4(), false, true);
            montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao4(), false, true);
            montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao4(), false, true);
        }
        if (!getListaPrevisaoRenovacao5().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao5(), null);
            montarDataPizzaListaPrevista(getListaPrevisaoRenovacao5(), false, true);
            montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao5(), false, true);
            montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao5(), false, true);
        }
        if (!getListaPrevisaoRenovacao6().isEmpty()) {
            obterQuantidadeClientes(getListaPrevisaoRenovacao6(), null);
            montarDataPizzaListaPrevista(getListaPrevisaoRenovacao6(), false, true);
            montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao6(), false, true);
            montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao6(), false, true);
        }
        obterFiltros();
        setMensagemDetalhada("");
    }

    public void montarDatasetBarraRenovados() throws Exception {
        montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao1(), false, true);
        montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao2(), false, true);
        montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao3(), false, true);
        montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao4(), false, true);
        montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao5(), false, true);
        montarDataBarraListaPrevistaRenovados(getListaPrevisaoRenovacao6(), false, true);
    }

    public void montarDataBarraListaPrevistaRenovados(List<ListaPrevisaoRenovacaoTO> listaPrevisao, Boolean barra, Boolean pizza) throws Exception {
        boolean renovacaoAtrasada = true;
        boolean renovacaoDia = true;
        boolean renovacaoAntecipada = true;

        for (ListaPrevisaoRenovacaoTO lp : listaPrevisao) {
            for (RenovacaoSinteticoVO obj : lp.getListaRenovacaoSinteticoVOs()) {
                if (obj.getSituacaoRenovacao().equals("RT") && renovacaoAtrasada) {
                    if (lp.getQtdRenovacoesAtrasadas() != 0) {
                        lp.getDataSetBarraRenovados().addValue(lp.getQtdRenovacoesAtrasadas(), obj.getSituacaoRenovacao_Apresentar(), "");
                    }
                    renovacaoAtrasada = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("AN") && renovacaoAntecipada) {
                    if (lp.getQtdRenovacoesAntecipadas() != 0) {
                        lp.getDataSetBarraRenovados().addValue(lp.getQtdRenovacoesAntecipadas(), obj.getSituacaoRenovacao_Apresentar(), "");
                    }
                    renovacaoAntecipada = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("ND") && renovacaoDia) {
                    if (lp.getQtdRenovacoesDia() != 0) {
                        lp.getDataSetBarraRenovados().addValue(lp.getQtdRenovacoesDia(), obj.getSituacaoRenovacao_Apresentar(), "");
                    }
                    renovacaoDia = false;
                    continue;
                }
            }
            lp.setBarraRenovados(barra);
            lp.setPizzaRenovados(pizza);
        }
    }

    public void montarDatasetPizzaRenovados() throws Exception {
        montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao1(), false, true);
        montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao2(), false, true);
        montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao3(), false, true);
        montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao4(), false, true);
        montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao5(), false, true);
        montarDataPizzaListaPrevistaRenovados(getListaPrevisaoRenovacao6(), false, true);
    }

    public void montarDataPizzaListaPrevistaRenovados(List<ListaPrevisaoRenovacaoTO> listaPrevisao, Boolean barra, Boolean pizza) throws Exception {

        boolean renovacaoAtrasada = true;
        boolean renovacaoDia = true;
        boolean renovacaoAntecipada = true;

        for (ListaPrevisaoRenovacaoTO lp : listaPrevisao) {
            for (RenovacaoSinteticoVO obj : lp.getListaRenovacaoSinteticoVOs()) {
                if (obj.getSituacaoRenovacao().equals("RT") && renovacaoAtrasada) {
                    if (lp.getQtdRenovacoesAtrasadas() != 0) {
                        lp.getDataSetPizzaRenovados().setValue(obj.getSituacaoRenovacao_Apresentar(), lp.getQtdRenovacoesAtrasadas());
                    }
                    renovacaoAtrasada = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("AN") && renovacaoAntecipada) {
                    if (lp.getQtdRenovacoesAntecipadas() != 0) {
                        lp.getDataSetPizzaRenovados().setValue(obj.getSituacaoRenovacao_Apresentar(), lp.getQtdRenovacoesAntecipadas());
                    }
                    renovacaoAntecipada = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("ND") && renovacaoDia) {
                    if (lp.getQtdRenovacoesDia() != 0) {
                        lp.getDataSetPizzaRenovados().setValue(obj.getSituacaoRenovacao_Apresentar(), lp.getQtdRenovacoesDia());
                    }
                    renovacaoDia = false;
                    continue;
                }
            }
            lp.setBarra(barra);
            lp.setPizza(pizza);
            lp.setBarraRenovados(barra);
            lp.setPizzaRenovados(pizza);
            lp.setBarraNaoRenovados(barra);
            lp.setPizzaNaoRenovados(pizza);
        }
    }

    public void montarDatasetBarraNaoRenovados() throws Exception {
        montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao1(), false, true);
        montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao2(), false, true);
        montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao3(), false, true);
        montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao4(), false, true);
        montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao5(), false, true);
        montarDataBarraListaPrevistaNaoRenovados(getListaPrevisaoRenovacao6(), false, true);
    }

    public void montarDatasetPizzaNaoRenovados() throws Exception {
        montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao1(), false, true);
        montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao2(), false, true);
        montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao3(), false, true);
        montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao4(), false, true);
        montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao5(), false, true);
        montarDataPizzaListaPrevistaNaoRenovados(getListaPrevisaoRenovacao6(), false, true);
    }

    public void montarDataBarraListaPrevistaNaoRenovados(List<ListaPrevisaoRenovacaoTO> listaPrevisao, Boolean barra, Boolean pizza) throws Exception {
        boolean nRenovadosAVencer = true;
        boolean nRenovadosDesistentes = true;
        boolean nRenovadosVencidos = true;
        boolean nRenovadosCancelados = true;
        boolean nRenovadosTrancados = true;

        for (ListaPrevisaoRenovacaoTO lp : listaPrevisao) {
            for (RenovacaoSinteticoVO obj : lp.getListaRenovacaoSinteticoVOs()) {
                if (obj.getSituacaoRenovacao().equals("RA") && nRenovadosAVencer) {
                    if (lp.getQtdNRenovadosAVencer() != 0) {
                        lp.getDataSetBarraNaoRenovados().addValue(lp.getQtdNRenovadosAVencer(), obj.getSituacaoRenovacao_Apresentar(), "");
                    }
                    nRenovadosAVencer = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("RD") && nRenovadosDesistentes) {
                    if (lp.getQtdNRenovadosDesistentes() != 0) {
                        lp.getDataSetBarraNaoRenovados().addValue(lp.getQtdNRenovadosDesistentes(), obj.getSituacaoRenovacao_Apresentar(), "");
                    }
                    nRenovadosDesistentes = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("RV") && nRenovadosVencidos) {
                    if (lp.getQtdNRenovadosVencidos() != 0) {
                        lp.getDataSetBarraNaoRenovados().addValue(lp.getQtdNRenovadosVencidos(), obj.getSituacaoRenovacao_Apresentar(), "");
                    }
                    nRenovadosVencidos = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("CA") && nRenovadosCancelados) {
                    if (lp.getQtdNRenovadosCancelados() != 0) {
                        lp.getDataSetBarraNaoRenovados().addValue(lp.getQtdNRenovadosCancelados(), obj.getSituacaoRenovacao_Apresentar(), "");
                    }
                    nRenovadosCancelados = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("TR") && nRenovadosTrancados) {
                    if (lp.getQtdNRenovadosTrancados() != 0) {
                        lp.getDataSetBarraNaoRenovados().addValue(lp.getQtdNRenovadosTrancados(), obj.getSituacaoRenovacao_Apresentar(), "");
                    }
                    nRenovadosTrancados = false;
                    continue;
                }
            }
            lp.setBarraNaoRenovados(barra);
            lp.setPizzaNaoRenovados(pizza);
        }
    }

    public void montarDataPizzaListaPrevistaNaoRenovados(List<ListaPrevisaoRenovacaoTO> listaPrevisao, Boolean barra, Boolean pizza) throws Exception {

        boolean nRenovadosAVencer = true;
        boolean nRenovadosDesistentes = true;
        boolean nRenovadosVencidos = true;
        boolean nRenovadosCancelados = true;
        boolean nRenovadosTrancados = true;

        for (ListaPrevisaoRenovacaoTO lp : listaPrevisao) {
            for (RenovacaoSinteticoVO obj : lp.getListaRenovacaoSinteticoVOs()) {
                if (obj.getSituacaoRenovacao().equals("RA") && nRenovadosAVencer) {
                    if (lp.getQtdNRenovadosAVencer() != 0) {
                        lp.getDataSetPizzaNaoRenovados().setValue(obj.getSituacaoRenovacao_Apresentar(), lp.getQtdNRenovadosAVencer());
                    }
                    nRenovadosAVencer = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("RD") && nRenovadosDesistentes) {
                    if (lp.getQtdNRenovadosDesistentes() != 0) {
                        lp.getDataSetPizzaNaoRenovados().setValue(obj.getSituacaoRenovacao_Apresentar(), lp.getQtdNRenovadosDesistentes());
                    }
                    nRenovadosDesistentes = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("RV") && nRenovadosVencidos) {
                    if (lp.getQtdNRenovadosVencidos() != 0) {
                        lp.getDataSetPizzaNaoRenovados().setValue(obj.getSituacaoRenovacao_Apresentar(), lp.getQtdNRenovadosVencidos());
                    }
                    nRenovadosVencidos = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("CA") && nRenovadosCancelados) {
                    if (lp.getQtdNRenovadosCancelados() != 0) {
                        lp.getDataSetBarraNaoRenovados().addValue(lp.getQtdNRenovadosCancelados(), obj.getSituacaoRenovacao_Apresentar(), "");
                    }
                    nRenovadosCancelados = false;
                    continue;
                }
                if (obj.getSituacaoRenovacao().equals("TR") && nRenovadosTrancados) {
                    if (lp.getQtdNRenovadosTrancados() != 0) {
                        lp.getDataSetBarraNaoRenovados().addValue(lp.getQtdNRenovadosTrancados(), obj.getSituacaoRenovacao_Apresentar(), "");
                    }
                    nRenovadosTrancados = false;
                    continue;
                }
            }
            lp.setBarra(barra);
            lp.setPizza(pizza);
            lp.setBarraRenovados(barra);
            lp.setPizzaRenovados(pizza);
            lp.setBarraNaoRenovados(barra);
            lp.setPizzaNaoRenovados(pizza);
        }
    }

    public void irParaTelaCliente() {
        RenovacaoSinteticoVO obj = (RenovacaoSinteticoVO) context().getExternalContext().getRequestMap().get("renovacao");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                ClienteVO clienteCons = new ClienteVO();
                clienteCons.setCodigo(obj.getCliente().getCodigo());
                irParaTelaCliente(clienteCons);
            }
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public void irParaTelaClienteLista() {
        try {
            ClienteSimplificadoTO obj = (ClienteSimplificadoTO) context().getExternalContext().getRequestMap().get("cliente");
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public void irParaTelaClienteDatatables() {
        try {
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
            if (codigoConsulta == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                ClienteSimplificadoTO obj = new ClienteSimplificadoTO();
                obj.setCodigo(codigoConsulta);

                irParaTelaCliente(getFacade().getCliente().consultarPorCodigoMatricula(obj.getCodigo(),getEmpresaVO().getCodigo() ,Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public void imprimirRelatorio() {
        try {
            limparMsg();
            setMsgAlert("");
            List listaRenovacao = new ArrayList();
            obterListaImprimir(getListaPrevisaoRenovacao1(), listaRenovacao);
            obterListaImprimir(getListaPrevisaoRenovacao2(), listaRenovacao);
            obterListaImprimir(getListaPrevisaoRenovacao3(), listaRenovacao);
            obterListaImprimir(getListaPrevisaoRenovacao4(), listaRenovacao);
            obterListaImprimir(getListaPrevisaoRenovacao5(), listaRenovacao);
            obterListaImprimir(getListaPrevisaoRenovacao6(), listaRenovacao);
            ordenarLista(dataTableRenovacao,listaRenovacao);
            String nomeRelatorio = "SituacaoRenovacaoAnaliticoDW";
            String titulo = "Relatório Analítico de Renovações";
            String barra = " - ";
            String design = getDesignIReportRelatorio();
            EmpresaVO empresa = new EmpresaVO();
            if (getEmpresaLogado().getCodigo() != 0) {
                empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_RENOVACAO, listaRenovacao.size(), getFiltros(), "PDF", "", "");
            apresentarRelatorioObjetos(nomeRelatorio, titulo, empresa.getNome(), "", "", "PDF", "/" + "SituacaoRenovacaoAnaliticoDW"
                    + "/registros", design, getUsuarioLogado().getNome(), getFiltros(), "", "",
                    empresa.getEndereco() + " " + empresa.getNumero() + " " + empresa.getSetor(), empresa.getCidade().getNome() + barra + empresa.getCidade().getEstado().getSigla(),
                    "", "", listaRenovacao);
            setMsgAlert("abrirPopupPDFImpressao('relatorio/"+getNomeArquivoRelatorioGeradoAgora()+"','', 780, 595);");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public static String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "sad" + File.separator + "SituacaoRenovacaoAnaliticoDW" + "Rel" + ".jrxml");
    }

    public void obterListaImprimir(List<ListaPrevisaoRenovacaoTO> listaPrevisao, List listaImprimir) {
        ClienteVO cliente = new ClienteVO();
        for (ListaPrevisaoRenovacaoTO lp : listaPrevisao) {
            for (RenovacaoSinteticoVO obj : lp.getListaRenovacaoSinteticoVOs()) {
                cliente.getPessoa().setCodigo(obj.getCliente().getCodigoPessoa());
                obj.setFoneCliente(ClienteVO.obterTelefoneCliente(cliente));
                NumberFormat df = NumberFormat.getCurrencyInstance(new Locale("pt", "BR"));
                obj.setValorBaseCalculoApresentar(df.format(obj.getContrato().getValorBaseCalculo()));
                listaImprimir.add(obj);
            }
        }
    }

    public void mostrarListaMesesFuturos() {
        setRelatorioAbrir("MesesFuturos");
        setListaClientes(getListaRenovadosMesesFuturos());
    }

    public void mostrarListaMesPassado() {
        setRelatorioAbrir("MesPassado");
        setListaClientes(getListaRenovadosMesesPassados());
    }

    public void mostrarListaTotal() {
        setRelatorioAbrir("RenovadosTotal");
        setListaClientes(getListaRenovadosTotal());
    }

    public void mostrarLista() {
        setListaClientes(getListaRenovados());
    }

    public void mostrarListaDentroMes() {
        setRelatorioAbrir("RenovadosDentroMes");
        setListaClientes(getListaRenovadosDentroMes());
    }

    public void mostrarListaPrevisaoMes() {
        setRelatorioAbrir("PrevisaoMes");
        setListaClientes(getListaPrevisaoMes());
    }

    public void mostrarListaRenovadosPrevisaoMes() {
        setRelatorioAbrir("RenovadosPrevisaoMes");
        setListaClientes(getListaRenovadosPrevisaoMes());
    }

    public void mostrarListaNaoRenovadosPrevisaoMes() {
        setRelatorioAbrir("NaoRenovadosPrevisaoMes");
        setListaClientes(getListaNaoRenovadosPrevisaoMes());
    }

    public void mostrarListaClientesComMuitosProfessores() {
        try {
            setRelatorioAbrir("alunosComMuitosProfessores");
            setListaClientes(getFacade().getCliente().consultarClienteMaisProfessor(getListaRenovadosPrevisaoMes()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void mostrarListaRenovadosToleranciaPrevisaoMes() {
        setListaClientes(getListaRenovadosToleranciaPrevisaoMes());
    }

    public void mostrarListaNaoRenovadosToleranciaPrevisaoMes() {
        setListaClientes(getListaNaoRenovadosToleranciaPrevisaoMes());
    }

    public boolean isMostrarSegundoTitulo() {
        if(null != getDataBaseFiltro()) {
            if (Uteis.getAnoData(getDataBaseFiltro()) != Uteis.getAnoData(negocio.comuns.utilitarias.Calendario.hoje())) {
                return true;
            } else if (Uteis.getMesData(getDataBaseFiltro()) != Uteis.getMesData(negocio.comuns.utilitarias.Calendario.hoje())) {
                return true;
            }
        }
        return false;
    }

    public String getPrimeiroTitulo() throws Exception {
        return "dia 1 até " + Uteis.getData(Uteis.obterUltimoDiaMes(getDataBaseFiltro())).substring(0, 2);
    }

    public String getRenovadosAteUltimoDia() throws Exception {
        return "Renovados da Previsão ";
    }

    public String getSegundoTitulo() throws Exception {
        if (getDataUltimaRenovacao() != null) {
            return "2º - Última Renovação " + Uteis.getData(getDataUltimaRenovacao()).substring(0, 5);
        } else {
            return "";
        }
    }

    public String getRenovadosDoMes() throws Exception {
        return "Renovações Realizadas no Mês " + Uteis.getMesReferencia(getDataInicio());
    }

    public String getRenovadosDoMesPassado() throws Exception {
        //return "Do Mês Passado (" + Uteis.retornaDescricaoMes(Uteis.obterDataFutura3(getDataInicio(), -1)) + ")";
        return "De Meses Passados";
    }

    public RenovacaoSinteticoVO getRenovacaoSinteticoVO() {
        return renovacaoSinteticoVO;
    }

    public void setRenovacaoSinteticoVO(RenovacaoSinteticoVO renovacaoSinteticoVO) {
        this.renovacaoSinteticoVO = renovacaoSinteticoVO;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }
    public String getDataBase_Apresentar(){
        DateFormat dfmt = new SimpleDateFormat("MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataInicio());
        return dfmt.format(cal.getTime());
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistemaVO() {
        return configuracaoSistemaVO;
    }

    public void setConfiguracaoSistemaVO(ConfiguracaoSistemaVO configuracaoSistemaVO) {
        this.configuracaoSistemaVO = configuracaoSistemaVO;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaPrevisaoRenovacao1() {
        return listaPrevisaoRenovacao1;
    }

    public void setListaPrevisaoRenovacao1(List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao1) {
        this.listaPrevisaoRenovacao1 = listaPrevisaoRenovacao1;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaPrevisaoRenovacao2() {
        return listaPrevisaoRenovacao2;
    }

    public void setListaPrevisaoRenovacao2(List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao2) {
        this.listaPrevisaoRenovacao2 = listaPrevisaoRenovacao2;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaPrevisaoRenovacao3() {
        return listaPrevisaoRenovacao3;
    }

    public void setListaPrevisaoRenovacao3(List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao3) {
        this.listaPrevisaoRenovacao3 = listaPrevisaoRenovacao3;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaPrevisaoRenovacao4() {
        return listaPrevisaoRenovacao4;
    }

    public void setListaPrevisaoRenovacao4(List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao4) {
        this.listaPrevisaoRenovacao4 = listaPrevisaoRenovacao4;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaPrevisaoRenovacao5() {
        return listaPrevisaoRenovacao5;
    }

    public void setListaPrevisaoRenovacao5(List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao5) {
        this.listaPrevisaoRenovacao5 = listaPrevisaoRenovacao5;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaPrevisaoRenovacao6() {
        return listaPrevisaoRenovacao6;
    }

    public void setListaPrevisaoRenovacao6(List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacao6) {
        this.listaPrevisaoRenovacao6 = listaPrevisaoRenovacao6;
    }

    public String getAno1() {
        if (ano1 == null) {
            ano1 = "";
        }
        return ano1;
    }

    public void setAno1(String ano1) {
        this.ano1 = ano1;
    }

    public String getMes1() {
        if (mes1 == null) {
            mes1 = "";
        }
        return mes1;
    }

    public void setMes1(String mes1) {
        this.mes1 = mes1;
    }

    public String getAno2() {
        if (ano2 == null) {
            ano2 = "";
        }
        return ano2;
    }

    public void setAno2(String ano2) {
        this.ano2 = ano2;
    }

    public String getAno3() {
        if (ano3 == null) {
            ano3 = "";
        }
        return ano3;
    }

    public void setAno3(String ano3) {
        this.ano3 = ano3;
    }

    public String getAno4() {
        if (ano4 == null) {
            ano4 = "";
        }
        return ano4;
    }

    public void setAno4(String ano4) {
        this.ano4 = ano4;
    }

    public String getAno5() {
        if (ano5 == null) {
            ano5 = "";
        }
        return ano5;
    }

    public void setAno5(String ano5) {
        this.ano5 = ano5;
    }

    public String getAno6() {
        if (ano6 == null) {
            ano6 = "";
        }
        return ano6;
    }

    public void setAno6(String ano6) {
        this.ano6 = ano6;
    }

    public String getMes2() {
        if (mes2 == null) {
            mes2 = "";
        }
        return mes2;
    }

    public void setMes2(String mes2) {
        this.mes2 = mes2;
    }

    public String getMes3() {
        if (mes3 == null) {
            mes3 = "";
        }
        return mes3;
    }

    public void setMes3(String mes3) {
        this.mes3 = mes3;
    }

    public String getMes4() {
        if (mes4 == null) {
            mes4 = "";
        }
        return mes4;
    }

    public void setMes4(String mes4) {
        this.mes4 = mes4;
    }

    public String getMes5() {
        if (mes5 == null) {
            mes5 = "";
        }
        return mes5;
    }

    public void setMes5(String mes5) {
        this.mes5 = mes5;
    }

    public List<SinteticoColaboradorVO> getListaVinculosProfessor() {
        if(listaVinculosProfessor == null)
        listaVinculosProfessor= new ArrayList<SinteticoColaboradorVO>();
        return listaVinculosProfessor;
    }

    public void setListaVinculosProfessor(List<SinteticoColaboradorVO> listaVinculosProfessor) {
        this.listaVinculosProfessor = listaVinculosProfessor;
    }

    public List<SinteticoColaboradorVO> getListaVinculosConsultor() {
        if(listaVinculosConsultor == null)
            listaVinculosConsultor = new ArrayList<SinteticoColaboradorVO>();
        return listaVinculosConsultor;
    }

    public void setListaVinculosConsultor(List<SinteticoColaboradorVO> listaVinculosConsultor) {
        this.listaVinculosConsultor = listaVinculosConsultor;
    }

    public String getMes6() {
        if (mes6 == null) {
            mes6 = "";
        }
        return mes6;
    }

    public void setMes6(String mes6) {
        this.mes6 = mes6;
    }

    public boolean isApresentarExportacao() {
        return !getListaPrevisaoRenovacao1().isEmpty() ||
                !getListaPrevisaoRenovacao2().isEmpty() ||
                !getListaPrevisaoRenovacao3().isEmpty() ||
                !getListaPrevisaoRenovacao4().isEmpty() ||
                !getListaPrevisaoRenovacao5().isEmpty() ||
                !getListaPrevisaoRenovacao6().isEmpty();
    }

    public Boolean getAprensetarLista1() {
        return !getListaPrevisaoRenovacao1().isEmpty();
    }

    public Boolean getAprensetarLista2() {
        return !getListaPrevisaoRenovacao2().isEmpty();
    }

    public Boolean getAprensetarLista3() {
        return !getListaPrevisaoRenovacao3().isEmpty();
    }

    public Boolean getAprensetarLista4() {
        return !getListaPrevisaoRenovacao4().isEmpty();
    }

    public Boolean getAprensetarLista5() {
        return !getListaPrevisaoRenovacao5().isEmpty();
    }

    public Boolean getAprensetarLista6() {
        return !getListaPrevisaoRenovacao6().isEmpty();
    }

    public Boolean getApresentarListaResumo1() {
        return getListaResumoConsultor1().size() > 0;
    }

    public Boolean getApresentarListaResumo2() {
        return getListaResumoConsultor2().size() > 0;
    }

    public Boolean getApresentarListaResumo3() {
        return getListaResumoConsultor3().size() > 0;
    }

    public Boolean getApresentarListaResumo4() {
        return getListaResumoConsultor4().size() > 0;
    }

    public Boolean getApresentarListaResumo5() {
        return getListaResumoConsultor5().size() > 0;
    }

    public Boolean getApresentarListaResumo6() {
        return getListaResumoConsultor6().size() > 0;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getFiltros() {
        if (filtros == null) {
            filtros = "";
        }
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public List<SinteticoColaboradorVO> getListaConsultor() {
        return listaConsultor;
    }

    public void setListaConsultor(List<SinteticoColaboradorVO> listaConsultor) {
        this.listaConsultor = listaConsultor;
    }

    public List<ColaboradorVO> getListaColaborador() {
        return listaColaborador;
    }

    public void setListaColaborador(List<ColaboradorVO> listaColaborador) {
        this.listaColaborador = listaColaborador;
    }

    public List<SinteticoPlanoVO> getListaPlano() {
        return listaPlano;
    }

    public void setListaPlano(List<SinteticoPlanoVO> listaPlano) {
        this.listaPlano = listaPlano;
    }

    public List<SinteticoDuracaoTO> getListaDuracoes() {
        if (listaDuracoes == null) {
            listaDuracoes = new ArrayList<>();
        }
        return listaDuracoes;
    }

    public void setListaDuracoes(List<SinteticoDuracaoTO> listaDuracoes) {
        this.listaDuracoes = listaDuracoes;
    }

    public List<SinteticoSituacaoRenovacaoVO> getListaSituacaoRenovacao() {
        return listaSituacaoRenovacao;
    }

    public void setListaSituacaoRenovacao(List<SinteticoSituacaoRenovacaoVO> listaSituacaoRenovacao) {
        this.listaSituacaoRenovacao = listaSituacaoRenovacao;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaCompletaPrevisaoRenovacao1() {
        return listaCompletaPrevisaoRenovacao1;
    }

    public void setListaCompletaPrevisaoRenovacao1(List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao1) {
        this.listaCompletaPrevisaoRenovacao1 = listaCompletaPrevisaoRenovacao1;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaCompletaPrevisaoRenovacao2() {
        return listaCompletaPrevisaoRenovacao2;
    }

    public void setListaCompletaPrevisaoRenovacao2(List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao2) {
        this.listaCompletaPrevisaoRenovacao2 = listaCompletaPrevisaoRenovacao2;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaCompletaPrevisaoRenovacao3() {
        return listaCompletaPrevisaoRenovacao3;
    }

    public void setListaCompletaPrevisaoRenovacao3(List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao3) {
        this.listaCompletaPrevisaoRenovacao3 = listaCompletaPrevisaoRenovacao3;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaCompletaPrevisaoRenovacao4() {
        return listaCompletaPrevisaoRenovacao4;
    }

    public void setListaCompletaPrevisaoRenovacao4(List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao4) {
        this.listaCompletaPrevisaoRenovacao4 = listaCompletaPrevisaoRenovacao4;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaCompletaPrevisaoRenovacao5() {
        return listaCompletaPrevisaoRenovacao5;
    }

    public void setListaCompletaPrevisaoRenovacao5(List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao5) {
        this.listaCompletaPrevisaoRenovacao5 = listaCompletaPrevisaoRenovacao5;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaCompletaPrevisaoRenovacao6() {
        return listaCompletaPrevisaoRenovacao6;
    }

    public void setListaCompletaPrevisaoRenovacao6(List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacao6) {
        this.listaCompletaPrevisaoRenovacao6 = listaCompletaPrevisaoRenovacao6;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaPrevisaoRenovacaoSintetico() {
        return listaPrevisaoRenovacaoSintetico;
    }

    public void setListaPrevisaoRenovacaoSintetico(List<ListaPrevisaoRenovacaoTO> listaPrevisaoRenovacaoSintetico) {
        this.listaPrevisaoRenovacaoSintetico = listaPrevisaoRenovacaoSintetico;
    }

    public Boolean getBolsa() {
        return bolsa;
    }

    public void setBolsa(Boolean bolsa) {
        this.bolsa = bolsa;
    }

    public Boolean getCancelado() {
        return cancelado;
    }

    public void setCancelado(Boolean cancelado) {
        this.cancelado = cancelado;
    }

    public Boolean getTrancado() {
        return trancado;
    }

    public void setTrancado(Boolean trancado) {
        this.trancado = trancado;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        renovacaoSinteticoVO = null;
        configuracaoSistemaVO = null;
        listaPrevisaoRenovacao1.clear();
        listaPrevisaoRenovacao2.clear();
        listaPrevisaoRenovacao3.clear();
        listaPrevisaoRenovacao4.clear();
        listaPrevisaoRenovacao5.clear();
        listaPrevisaoRenovacao6.clear();
        listaCompletaPrevisaoRenovacao1.clear();
        listaCompletaPrevisaoRenovacao2.clear();
        listaCompletaPrevisaoRenovacao3.clear();
        listaCompletaPrevisaoRenovacao4.clear();
        listaCompletaPrevisaoRenovacao5.clear();
        listaCompletaPrevisaoRenovacao6.clear();
        listaSituacaoRenovacao.clear();
        listaConsultor.clear();
        listaPlano.clear();
        empresaVO = null;
        dataInicio = null;
        dataFim = null;
        mes1 = null;
        ano1 = null;
        mes2 = null;
        ano2 = null;
        mes3 = null;
        ano3 = null;
        mes4 = null;
        ano4 = null;
        mes5 = null;
        ano5 = null;
        mes6 = null;
        ano6 = null;
        filtros = null;
        bolsa = null;
        cancelado = null;
        trancado = null;
    }

    public List<ListaPrevisaoRenovacaoTO> getListaCompletaPrevisaoRenovacaoSintetico() {
        return listaCompletaPrevisaoRenovacaoSintetico;
    }

    public void setListaCompletaPrevisaoRenovacaoSintetico(List<ListaPrevisaoRenovacaoTO> listaCompletaPrevisaoRenovacaoSintetico) {
        this.listaCompletaPrevisaoRenovacaoSintetico = listaCompletaPrevisaoRenovacaoSintetico;
    }

    public String inicializarRenovacaoSinteticoControle() {
        return "";
    }

    public int getQtdePrevista() {
        return qtdePrevista;
    }

    public String getPrevistoRenovadoPercentual() {
        if (qtdePrevista != 0) {
            double total = (double) qtdePrevista;
            double renovadas = (double) qtdeRenovada;
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula((renovadas * 100) / total) + "%";
        } else {
            return "0,00%";
        }
    }

    public String getPrevistoVencidoAtivoPercentual() {
        if (qtdePrevista != 0 && qtdeAtivos != 0) {
            double previsto = (double) qtdePrevista;
            double total = (double) qtdeAtivos;
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula((previsto * 100) / total) + "% dos ativos";
        } else {
            return "0,00%";
        }
    }
    public boolean isAlertaRenovadoPercentual() {
        if (qtdePrevista != 0) {
            double total = (double) qtdePrevista;
            double renovadas = (double) qtdeRenovadaComTolerancia;
            return (((renovadas * 100) / total) > 50.0);
        }
        return true;
    }

    public boolean isAlertaRenovadoAtivoPercentual() {
        if (qtdePrevista != 0) {
            double previsto = (double) qtdePrevista;
            double total = (double) qtdeAtivos;
            return (((previsto * 100) / total) < 15.0);
        }
        return true;
    }

    public boolean isAlertaRenovado() {
        if (qtdePrevista != 0) {
            double total = (double) qtdePrevista;
            double renovadas = (double) qtdeRenovada;
            return (((renovadas * 100) / total) > 50.0);
        }
        return true;
    }

    public boolean isAlertaRenovacoesAtrasadas() {
        if (qtdePrevista != 0) {
            double total = (double) qtdePrevista;
            double renovadas = (double) qtdeRenovada;
            double renovadasComTolerancia = (double) qtdeRenovadaComTolerancia;
            double resultado = ((renovadasComTolerancia * 100) / total) - ((renovadas * 100) / total);
            return resultado < 10.00;
        }
        return true;
    }

    public String getPrevistoRenovadoPercentualComTolerancia() {
        if (qtdePrevista != 0) {
            double total = (double) qtdePrevista;
            double renovadas = (double) qtdeRenovadaComTolerancia;
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula((renovadas * 100) / total) + "%";
        } else {
            return "0,00%";
        }
    }

    public String getPrevistoNaoRenovadoPercentual() {
        if (qtdePrevista != 0) {
            double total = (double) qtdePrevista;
            double naoRenovadas = (double) qtdeNaoRenovada;
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula((naoRenovadas * 100) / total) + "%";
        } else {
            return "0,00%";
        }
    }

    public String getPrevistoNaoRenovadoPercentualComTolerancia() {
        if (qtdePrevista != 0) {
            double total = (double) qtdePrevista;
            double naoRenovadas = (double) qtdeNaoRenovadaComTolerancia;
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula((naoRenovadas * 100) / total) + "%";
        } else {
            return "0,00%";
        }
    }

    public String getDiferencaPrevisao() {
        if (qtdePrevista != 0) {
            double total = (double) qtdePrevista;
            double renovadas = (double) qtdeRenovada;
            double renovadasComTolerancia = (double) qtdeRenovadaComTolerancia;
            double resultado = ((renovadasComTolerancia * 100) / total) - ((renovadas * 100) / total);
            return (resultado < 0 ? "0%" : Uteis.arrendondarForcando2CadasDecimaisComVirgula(resultado) + "%");
        } else {
            return "0,00%";
        }
    }

    public String getRenovadoMesPassadoPercentual() {
        if (qtdeRenovadaTotal != 0) {
            double total = (double) qtdeRenovadaTotal;
            double renovadas = (double) qtdeRenovadaMesesPassados;
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula((renovadas * 100) / total) + "%";
        } else {
            return "0,00%";
        }
    }

    public String getRenovadoPercentual() {
        if (qtdeRenovadaTotal != 0) {
            double total = (double) qtdeRenovadaTotal;
            double renovadas = (double) qtdeRenovada;
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula((renovadas * 100) / total) + "%";
        } else {
            return "0,00%";
        }
    }

    public String getRenovadoDentroMesPercentual() {
        if (qtdeRenovadaTotal != 0) {
            double total = (double) qtdeRenovadaTotal;
            double renovadas = (double) qtdeRenovadaDentroMes;
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula((renovadas * 100) / total) + "%";
        } else {
            return "0,00%";
        }
    }

    public String getRenovadoMesesFuturosPercentual() {
        if (qtdeRenovadaTotal != 0) {
            double total = (double) qtdeRenovadaTotal;
            double renovadas = (double) qtdeRenovadaMesesFuturos;
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula((renovadas * 100) / total) + "%";
        } else {
            return "0,00%";
        }
    }

    public String getTitle() {
        StringBuilder str = new StringBuilder();
        str.append("  i.Não entram nesta previsão os planos cancelados e trancados.\n");
        str.append(" ii.Também não entram nesta previsão planos do tipo Bolsa.\n");
        str.append("iii.Obs: se não entra na previsão também não entra como não renovado.");
        return str.toString();
    }

    public void setQtdePrevista(int qtdePrevista) {
        this.qtdePrevista = qtdePrevista;
    }

    public int getQtdeRenovada() {
        return qtdeRenovada;
    }

    public void setQtdeRenovada(int qtdeRenovada) {
        this.qtdeRenovada = qtdeRenovada;
    }

    public int getQtdeNaoRenovada() {
        return qtdeNaoRenovada;
    }

    public void setQtdeNaoRenovada(int qtdeNaoRenovada) {
        this.qtdeNaoRenovada = qtdeNaoRenovada;
    }

    public int getQtdeRenovadaComTolerancia() {
        return qtdeRenovadaComTolerancia;
    }

    public void setQtdeRenovadaComTolerancia(int qtdeRenovadaComTolerancia) {
        this.qtdeRenovadaComTolerancia = qtdeRenovadaComTolerancia;
    }

    public int getQtdeNaoRenovadaComTolerancia() {
        return qtdeNaoRenovadaComTolerancia;
    }

    public void setQtdeNaoRenovadaComTolerancia(int qtdeNaoRenovadaComTolerancia) {
        this.qtdeNaoRenovadaComTolerancia = qtdeNaoRenovadaComTolerancia;
    }

    public int getQtdeRenovadaTotal() {
        return qtdeRenovadaTotal;
    }

    public void setQtdeRenovadaTotal(int qtdeRenovadaTotal) {
        this.qtdeRenovadaTotal = qtdeRenovadaTotal;
    }

    public int getQtdeRenovadaMesesPassados() {
        return qtdeRenovadaMesesPassados;
    }

    public void setQtdeRenovadaMesesPassados(int qtdeRenovadaMesesPassados) {
        this.qtdeRenovadaMesesPassados = qtdeRenovadaMesesPassados;
    }

    public int getQtdeRenovadaMesesFuturos() {
        return qtdeRenovadaMesesFuturos;
    }

    public void setQtdeRenovadaMesesFuturos(int qtdeRenovadaMesesFuturos) {
        this.qtdeRenovadaMesesFuturos = qtdeRenovadaMesesFuturos;
    }

    public List<ClienteSimplificadoTO> getListaRenovados() {
        return listaRenovados;
    }

    public void setListaRenovados(List<ClienteSimplificadoTO> listaRenovados) {
        this.listaRenovados = listaRenovados;
    }

    public List<ClienteSimplificadoTO> getListaRenovadosTotal() {
        return listaRenovadosTotal;
    }

    public void setListaRenovadosTotal(List<ClienteSimplificadoTO> listaRenovadosTotal) {
        this.listaRenovadosTotal = listaRenovadosTotal;
    }

    public List<ClienteSimplificadoTO> getListaRenovadosMesesPassados() {
        return listaRenovadosMesesPassados;
    }

    public void setListaRenovadosMesesPassados(List<ClienteSimplificadoTO> listaRenovadosMesesPassados) {
        this.listaRenovadosMesesPassados = listaRenovadosMesesPassados;
    }

    public List<ClienteSimplificadoTO> getListaRenovadosMesesFuturos() {
        return listaRenovadosMesesFuturos;
    }

    public void setListaRenovadosMesesFuturos(List<ClienteSimplificadoTO> listaRenovadosMesesFuturos) {
        this.listaRenovadosMesesFuturos = listaRenovadosMesesFuturos;
    }

    public List<ClienteSimplificadoTO> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClienteSimplificadoTO> listaClientes) {
        this.listaClientes = listaClientes;
    }

    private Date processarRenovadasAtrasadas() {
        Date ultimaRenovacao = getDataFim();
        try {
            Integer empresa = getEmpresaVO().getCodigo();
            if(getEmpresaFiltroBI() != null){
                empresa = getEmpresaFiltroBI().getCodigo();
            }
            List<Date> lista = getFacade().getContrato().consultarContratoDataPrevistoPeriodo(empresa, getDataInicio(), getDataFim(), getListaColaborador());
            Iterator i = lista.iterator();
            while (i.hasNext()) {
                Date aux = (Date) i.next();
                if (aux != null) {
                    ultimaRenovacao = (ultimaRenovacao.compareTo(aux) < 0 ? aux : ultimaRenovacao);
                }
            }
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return ultimaRenovacao;
    }

    public Date getDataUltimaRenovacao() {
        return dataUltimaRenovacao;
    }

    public void setDataUltimaRenovacao(Date dataUltimaRenovacao) {
        this.dataUltimaRenovacao = dataUltimaRenovacao;
    }

    public List<GrupoRenovacaoConsultorTO> getListaResumoConsultor1() {
        return listaResumoConsultor1;
    }

    public void setListaResumoConsultor1(List<GrupoRenovacaoConsultorTO> listaResumoConsultor) {
        this.listaResumoConsultor1 = listaResumoConsultor;
    }

    public List<GrupoRenovacaoConsultorTO> getListaResumoConsultor2() {
        return listaResumoConsultor2;
    }

    public void setListaResumoConsultor2(List<GrupoRenovacaoConsultorTO> listaResumoConsultor) {
        this.listaResumoConsultor2 = listaResumoConsultor;
    }

    public List<GrupoRenovacaoConsultorTO> getListaResumoConsultor3() {
        return listaResumoConsultor3;
    }

    public void setListaResumoConsultor3(List<GrupoRenovacaoConsultorTO> listaResumoConsultor) {
        this.listaResumoConsultor3 = listaResumoConsultor;
    }

    public List<GrupoRenovacaoConsultorTO> getListaResumoConsultor4() {
        return listaResumoConsultor4;
    }

    public void setListaResumoConsultor4(List<GrupoRenovacaoConsultorTO> listaResumoConsultor) {
        this.listaResumoConsultor4 = listaResumoConsultor;
    }

    public List<GrupoRenovacaoConsultorTO> getListaResumoConsultor5() {
        return listaResumoConsultor5;
    }

    public void setListaResumoConsultor5(List<GrupoRenovacaoConsultorTO> listaResumoConsultor) {
        this.listaResumoConsultor5 = listaResumoConsultor;
    }

    public List<GrupoRenovacaoConsultorTO> getListaResumoConsultor6() {
        return listaResumoConsultor6;
    }

    public void setListaResumoConsultor6(List<GrupoRenovacaoConsultorTO> listaResumoConsultor) {
        this.listaResumoConsultor6 = listaResumoConsultor;
    }

    public int getQtdeTotal() {
        int total = 0;
        if (listaPrevisaoRenovacao1.size() > 0) {
            total += listaPrevisaoRenovacao1.get(0).getQtdTotalPrevisaoRenovacao();
        }
        if (listaPrevisaoRenovacao2.size() > 0) {
            total += listaPrevisaoRenovacao2.get(0).getQtdTotalPrevisaoRenovacao();
        }
        if (listaPrevisaoRenovacao3.size() > 0) {
            total += listaPrevisaoRenovacao3.get(0).getQtdTotalPrevisaoRenovacao();
        }
        if (listaPrevisaoRenovacao4.size() > 0) {
            total += listaPrevisaoRenovacao4.get(0).getQtdTotalPrevisaoRenovacao();
        }
        if (listaPrevisaoRenovacao5.size() > 0) {
            total += listaPrevisaoRenovacao5.get(0).getQtdTotalPrevisaoRenovacao();
        }
        if (listaPrevisaoRenovacao6.size() > 0) {
            total += listaPrevisaoRenovacao6.get(0).getQtdTotalPrevisaoRenovacao();
        }
        return total;
    }

    public void toggleMostrarGrupos() {
        setMostrarGrupos(!isMostrarGrupos());
    }

    public boolean isMostrarGrupos() {
        return mostrarGrupos;
    }

    public void setMostrarGrupos(boolean mostrarGrupos) {
        this.mostrarGrupos = mostrarGrupos;
    }

    public List<GrupoColaboradorVO> getListaGrupos() {
        return listaGrupos;
    }

    public void setListaGrupos(List<GrupoColaboradorVO> listaGrupos) {
        this.listaGrupos = listaGrupos;
    }

    public void setQtdeRenovadaDentroMes(int qtdeRenovadaDentroMes) {
        this.qtdeRenovadaDentroMes = qtdeRenovadaDentroMes;
    }

    public int getQtdeRenovadaDentroMes() {
        return qtdeRenovadaDentroMes;
    }

    public void setListaRenovadosDentroMes(List<ClienteSimplificadoTO> listaRenovadosDentroMes) {
        this.listaRenovadosDentroMes = listaRenovadosDentroMes;
    }

    public List<ClienteSimplificadoTO> getListaRenovadosDentroMes() {
        return listaRenovadosDentroMes;
    }

    public List<ClienteSimplificadoTO> getListaPrevisaoMes() {
        return listaPrevisaoMes;
    }

    public void setListaPrevisaoMes(List<ClienteSimplificadoTO> listaPrevisaoMes) {
        this.listaPrevisaoMes = listaPrevisaoMes;
    }

    public List<ClienteSimplificadoTO> getListaRenovadosPrevisaoMes() {
        return listaRenovadosPrevisaoMes;
    }

    public void setListaRenovadosPrevisaoMes(List<ClienteSimplificadoTO> listaRenovadosPrevisaoMes) {
        this.listaRenovadosPrevisaoMes = listaRenovadosPrevisaoMes;
    }

    public List<ClienteSimplificadoTO> getListaNaoRenovadosPrevisaoMes() {
        return listaNaoRenovadosPrevisaoMes;
    }

    public void setListaNaoRenovadosPrevisaoMes(List<ClienteSimplificadoTO> listaNaoRenovadosPrevisaoMes) {
        this.listaNaoRenovadosPrevisaoMes = listaNaoRenovadosPrevisaoMes;
    }

    public List<ClienteSimplificadoTO> getListaRenovadosToleranciaPrevisaoMes() {
        return listaRenovadosToleranciaPrevisaoMes;
    }

    public void setListaRenovadosToleranciaPrevisaoMes(List<ClienteSimplificadoTO> listaRenovadosToleranciaPrevisaoMes) {
        this.listaRenovadosToleranciaPrevisaoMes = listaRenovadosToleranciaPrevisaoMes;
    }

    public List<ClienteSimplificadoTO> getListaNaoRenovadosToleranciaPrevisaoMes() {
        return listaNaoRenovadosToleranciaPrevisaoMes;
    }

    public void setListaNaoRenovadosToleranciaPrevisaoMes(List<ClienteSimplificadoTO> listaNaoRenovadosToleranciaPrevisaoMes) {
        this.listaNaoRenovadosToleranciaPrevisaoMes = listaNaoRenovadosToleranciaPrevisaoMes;
    }

    public void exportar(ActionEvent evt) {
        try {
            setMsgAlert("");
            limparFiltros();
            List listaRenovacao = new ArrayList();
            obterListaImprimir(getListaPrevisaoRenovacao1(), listaRenovacao);
            obterListaImprimir(getListaPrevisaoRenovacao2(), listaRenovacao);
            obterListaImprimir(getListaPrevisaoRenovacao3(), listaRenovacao);
            obterListaImprimir(getListaPrevisaoRenovacao4(), listaRenovacao);
            obterListaImprimir(getListaPrevisaoRenovacao5(), listaRenovacao);
            obterListaImprimir(getListaPrevisaoRenovacao6(), listaRenovacao);

            ordenarLista(dataTableRenovacao, listaRenovacao);

            evt.getComponent().getAttributes().put("lista", listaRenovacao);
            ExportadorListaControle exportador = (ExportadorListaControle) getControlador(ExportadorListaControle.class);
            exportador.exportar(evt);
            if(exportador.getErro()){
                throw new Exception (exportador.getMensagemDetalhada());
            }
            setMsgAlert("abrirPopup('UpdateServlet?op=downloadfile&file="+ exportador.getFileName()+"&mimetype=application/vnd.ms-excel','Transacoes', 800,200);");
        }catch (Exception e){
            montarErro(e);
        }
    }

    private List ordenarLista(org.richfaces.component.html.HtmlDataTable dataTable, List lista){
        if (!lista.isEmpty()) {
            String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dataTable);
            if (!colunaOrdenacao.isEmpty()){
                String[] params = colunaOrdenacao.split(":");
                Ordenacao.ordenarLista(lista, params[0]);
                if(params[1].equals("DESC")){
                    Collections.reverse(lista);
                }
            }
        }
        return lista;
    }


    public void exportarListas(ActionEvent evt){
        try {
            limparMsg();
            setMsgAlert("");
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

            String[] split = paramsTableFiltrada.split(",");
            String campoOrdenacao = split[0].replace("[", "");
            String ordem = split[1];
            String filtro = split[2].replace("''", "");
            filtro = filtro.replace("]", "");
            List listaParaImpressao = getFacade().getContrato().clientesComContratosPrevistosPeriodoExportar(getEmpresaFiltroBI().getCodigo(),
                    Uteis.obterPrimeiroDiaMes(getDataInicio()),
                    Uteis.obterUltimoDiaMesUltimaHora(getDataFim()),
                    getListaColaboradorFiltroBI(BIEnum.INDICE_RENOVACAO.name()), getRelatorioAbrir(), null, null, campoOrdenacao, ordem, filtro, getListaClientes());

            for (Object obj : listaParaImpressao) {
                RenovacaoSinteticoVO renovacaoSinteticoVO = (RenovacaoSinteticoVO) obj;
                renovacaoSinteticoVO.obterSituacaoPrevisaRenovacao(getCancelado(), getTrancado());
            }

            exportadorListaControle.exportar(evt, listaParaImpressao, filtro, getItemExportar());
            if(exportadorListaControle.getErro()){
                throw new Exception(exportadorListaControle.getMensagemDetalhada());
            }
            String tipo = (String) JSFUtilities.getFromActionEvent("tipo", evt);
            setMsgAlert("abrirPopup('../../UpdateServlet?op=downloadfile&file="+exportadorListaControle.getFileName()+"&mimetype="+(tipo.equals("pdf") ? "application/pdf": "application/vnd.ms-excel")+"','Transacoes', 800,200);");
        } catch (Exception e){
            montarErro(e);
        }
    }

    private ItemExportacaoEnum getItemExportar() {
        if ( getRelatorioAbrir().equals("MesesFuturos") ) {
            return ItemExportacaoEnum.BI_IR_MESES_FUTUROS;
        }
        if ( getRelatorioAbrir().equals("MesPassado") ) {
            return ItemExportacaoEnum.BI_IR_MES_PASSADO;
        }
        if ( getRelatorioAbrir().equals("RenovadosTotal") ) {
            return ItemExportacaoEnum.BI_IR_RENOVADOS_TOTAL;
        }
        if ( getRelatorioAbrir().equals("RenovadosDentroMes") ) {
            return ItemExportacaoEnum.BI_IR_DENTRO_MES;
        }
        if ( getRelatorioAbrir().equals("RenovadosPrevisaoMes") ) {
            return ItemExportacaoEnum.BI_IR_RENOVADOS_PREVISAO;
        }
        if ( getRelatorioAbrir().equals("NaoRenovadosPrevisaoMes") ) {
            return ItemExportacaoEnum.BI_IR_NAO_RENOVADOS_PREVISAO;
        }
        if ( getRelatorioAbrir().equals("NaoRenovadosPrevisaoMes") ) {
            return ItemExportacaoEnum.BI_IR_ALUNOS_MUITO_PROFESSORES;
        }
        if ( getRelatorioAbrir().equals("PrevisaoMes") ) {
            return ItemExportacaoEnum.BI_IR_PREVISAO_MES;
        }

        return null;
    }

    public boolean isMostrarCheckbox() throws Exception {
        return  !getUsuarioLogado().getAdministrador() && obterColaboradorParticipanteVOLogado() != null;
    }


    public void setDataTableRenovacao(org.richfaces.component.html.HtmlDataTable dataTableRenovacao) {
        this.dataTableRenovacao = dataTableRenovacao;
    }

    public org.richfaces.component.html.HtmlDataTable getDataTableRenovacao() {
        return dataTableRenovacao;
    }

    public List<SinteticoColaboradorVO> getListaVinculosOutros() {
        if(listaVinculosOutros == null){
            listaVinculosOutros= new ArrayList<SinteticoColaboradorVO>();
        }
        return listaVinculosOutros;
    }

    public void setListaVinculosOutros(List<SinteticoColaboradorVO> listaVinculosOutros) {
        this.listaVinculosOutros = listaVinculosOutros;
    }




}

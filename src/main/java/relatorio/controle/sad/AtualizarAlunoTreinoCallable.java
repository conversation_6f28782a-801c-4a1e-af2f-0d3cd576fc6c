/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.sad;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;

/**
 *
 * <AUTHOR>
 */
public class AtualizarAlunoTreinoCallable implements Runnable {

    private Connection con;

    public Connection getCon() {
        return con;
    }

    public AtualizarAlunoTreinoCallable(Connection c) {
        this.con = c;
    }

    @Override
    public void run() {
        SituacaoClienteSinteticoDW sitDao;
        try {
            sitDao = new SituacaoClienteSinteticoDW(con);
            sitDao.atualizarUsuariosTreino(con);
        } catch (Exception ex) {
            Logger.getLogger(SituacaoClienteSinteticoDWControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            try {
                if (con != null && !con.isClosed()) {
                    con.close();
                }
            } catch (SQLException ex) {
                Logger.getLogger(AtualizarAlunoTreinoCallable.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }
}
package relatorio.controle.sad;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoAgendamentoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.basico.BIControle;
import relatorio.negocio.comuns.basico.ProbabilidadeEvasaoVO;

import javax.faces.event.ActionEvent;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class RelControleProbEvas extends BIControle {

    private int qtdAlunos;
    private int valorSlider=70;
    private String mesAno;
    private Date inicio;
    private List<ProbabilidadeEvasaoVO> listProbabilidadeEvasaoVO;
    private String filtrarNome;
    private int rowsDataTable = 10;
    private boolean marcarTodosRegistros;
    private List<ProbabilidadeEvasaoVO> registrosSelecionados;
    private boolean registroSelecionado = false;
    private boolean mostraBtnEnviarCrm = false;
    private String msg;
    private boolean mostraMsg;
    private boolean mostraCardIA;
    private String qtdAlunosSelecionados = "nenhum aluno selecionado";

    public void filtrarPorOperacaoPorEmpresa() {
        try {
            consultarProbabilidadeEvasao();

            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarProbabilidadeEvasao() throws Exception {
        this.mostraCardIA = pesquisaModuloIA();
        if (!this.mostraCardIA)
            return;

        this.mesAno = Uteis.getDataAplicandoFormatacao(this.getDataBaseFiltroBI(), "MMMM") + " de " + Uteis.getDataAplicandoFormatacao(this.getDataBaseFiltroBI(), "yyyy");
        this.setInicio(Uteis.obterPrimeiroDiaMesPrimeiraHora(this.getDataBaseFiltroBI()));

        consultaQtdAlunos();

        setMensagemDetalhada("", "");
    }

    public void consultaQtdAlunos() throws Exception {
        ProbabilidadeEvasao probabilidadeEvasao = new ProbabilidadeEvasao();
        this.qtdAlunos = probabilidadeEvasao.consultarQtdAlunos(this.valorSlider, getEmpresaLogado().getCodigo(), getListaColaboradorFiltroBI());
    }

    public void consultarAlunos() throws Exception {
        this.qtdAlunosSelecionados = "nenhum aluno selecionado";
        this.setMostraBtnEnviarCrm(false);
        this.setMarcarTodosRegistros(false);
        this.setMostraMsg(false);
        ProbabilidadeEvasao probabilidadeEvasao = new ProbabilidadeEvasao();
        this.listProbabilidadeEvasaoVO = probabilidadeEvasao.consultarAlunos(this.valorSlider, getEmpresaLogado().getCodigo(), getListaColaboradorFiltroBI());
    }

    public void consultarAlunosEQtdAlunos() throws Exception {
        consultaQtdAlunos();
        consultarAlunos();
    }

    public void filtrarAlunos() throws Exception {
        ProbabilidadeEvasao probabilidadeEvasao = new ProbabilidadeEvasao();
        this.listProbabilidadeEvasaoVO = probabilidadeEvasao.consultarAlunos(this.valorSlider, this.filtrarNome, getEmpresaLogado().getCodigo(), getListaColaboradorFiltroBI());
    }

    public void exportar(ActionEvent evt) {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getManagedBean(ExportadorListaControle.class.getSimpleName());
        exportadorListaControle.exportar(evt, Ordenacao.ordenarLista(getListProbabilidadeEvasaoVO(), "nome"), "Probabilidade de Evasão", null);
    }

    public void marcarTodos() {
        this.setMostraMsg(false);
        try {

            int qtdAlunos = 0;

            for (ProbabilidadeEvasaoVO obj : listProbabilidadeEvasaoVO) {
                ProbabilidadeEvasaoVO probabilidadeEvasaoVO = obj;
                if (probabilidadeEvasaoVO.isDisabledCheckBox()) {
                    probabilidadeEvasaoVO.setRegistroSelecionado(false);
                } else {
                    qtdAlunos++;
                    probabilidadeEvasaoVO.setRegistroSelecionado(marcarTodosRegistros);
                }
                getRegistrosSelecionados().add(probabilidadeEvasaoVO);
            }

            this.qtdAlunosSelecionados = qtdAlunos + " alunos selecionados";

            this.mostraBtnEnviarCrm = true;

            if (marcarTodosRegistros == false) {
                setRegistrosSelecionados(new ArrayList<>());
                this.mostraBtnEnviarCrm = false;
                this.qtdAlunosSelecionados = "nenhum aluno selecionado";
            }

        } catch (Exception e) {
            setMsgAlert("Não existe registro para ser selecionado.");
        }
    }

    public void checkRegistro() {
        this.qtdAlunosSelecionados = "nenhum aluno selecionado";
        this.mostraBtnEnviarCrm = false;
        this.setMostraMsg(false);

        int qtdAlunosSelec = 0;

        for (ProbabilidadeEvasaoVO obj : listProbabilidadeEvasaoVO) {
            if (obj.isRegistroSelecionado()) {
                this.mostraBtnEnviarCrm = true;
                qtdAlunosSelec++;
            }
        }

        if (qtdAlunosSelec > 1) {
            this.qtdAlunosSelecionados = qtdAlunosSelec + " alunos selecionados";
        } else if (qtdAlunosSelec == 1) {
            this.qtdAlunosSelecionados = qtdAlunosSelec + " aluno selecionado";
        }
    }

    public void enviarCrm() throws Throwable {
        MalaDiretaVO malaDiretaVO = new MalaDiretaVO();
        malaDiretaVO.setMeioDeEnvio(MeioEnvio.CRM_EXTRA.getCodigo());
        malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.CRM_EXTRA);
        malaDiretaVO.setEmpresa(getEmpresaLogado());
        malaDiretaVO.setMetaExtraIndividual(true);
        malaDiretaVO.setTipoConsultorMetaExtraIndividual("CO");
        malaDiretaVO.setCrmExtra(true);
        malaDiretaVO.setTitulo("ChurnRate - Probabilidade de Evasão");
        malaDiretaVO.setDataEnvio(Calendario.hoje());
        malaDiretaVO.setVigenteAte(Calendario.somarDias(Calendario.hoje(), 30));
        malaDiretaVO.setRemetente(getUsuarioLogado());
        malaDiretaVO.setSql("");
        malaDiretaVO.setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);

        List<AmostraClienteTO> listAmostraClienteTO = new ArrayList<>();

        for (ProbabilidadeEvasaoVO obj : listProbabilidadeEvasaoVO) {
            AmostraClienteTO amostraClienteTO = new AmostraClienteTO();
            if (obj.isRegistroSelecionado()) {
                amostraClienteTO.setCodigoCliente(obj.getCliente());
                listAmostraClienteTO.add(amostraClienteTO);
            }
        }

        getFacade().getMalaDireta().gravarCRMExtra(malaDiretaVO);
        getFacade().getMalaDiretaCRMExtraCliente().incluirListaCliente(malaDiretaVO, listAmostraClienteTO);
        consultarAlunos();

        this.msg = "Enviado para o CRM com sucesso.";
        this.setMostraMsg(true);
        this.setMsgAlert(this.msg);
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public int getQtdAlunos() {
        return qtdAlunos;
    }

    public void setQtdAlunos(int qtdAlunos) {
        this.qtdAlunos = qtdAlunos;
    }

    public int getValorSlider() {
        return valorSlider;
    }

    public int getIntervaloSlider() {
        return valorSlider+10;
    }

    public void setValorSlider(int valorSlider) {
        this.valorSlider = valorSlider;
    }

    public String getMesAno() {
        return mesAno;
    }

    public void setMesAno(String mesAno) {
        this.mesAno = mesAno;
    }

    public String getDataBase_Apresentar() {
        DateFormat dfmt = new SimpleDateFormat("MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataBaseFiltroBI());
        return dfmt.format(cal.getTime());
    }

    public List<ProbabilidadeEvasaoVO> getListProbabilidadeEvasaoVO() {
        return listProbabilidadeEvasaoVO;
    }

    public void setListProbabilidadeEvasaoVO(List<ProbabilidadeEvasaoVO> listProbabilidadeEvasaoVO) {
        this.listProbabilidadeEvasaoVO = listProbabilidadeEvasaoVO;
    }

    public String getFiltrarNome() {
        return filtrarNome;
    }

    public void setFiltrarNome(String filtrarNome) {
        this.filtrarNome = filtrarNome;
    }

    public int getRowsDataTable() {
        return rowsDataTable;
    }

    public void setRowsDataTable(int rowsDataTable) {
        this.rowsDataTable = rowsDataTable;
    }

    public boolean getMarcarTodosRegistros() {
        return marcarTodosRegistros;
    }

    public void setMarcarTodosRegistros(boolean marcarTodosRegistros) {
        this.marcarTodosRegistros = marcarTodosRegistros;
    }

    public List<ProbabilidadeEvasaoVO> getRegistrosSelecionados() {
        if (registrosSelecionados == null) {
            registrosSelecionados = new ArrayList<>();
        }
        return registrosSelecionados;
    }

    public void setRegistrosSelecionados(List<ProbabilidadeEvasaoVO> registrosSelecionados) {
        this.registrosSelecionados = registrosSelecionados;
    }

    public boolean isRegistroSelecionado() {
        return registroSelecionado;
    }

    public void setRegistroSelecionado(boolean registroSelecionado) {
        this.registroSelecionado = registroSelecionado;
    }

    public boolean isMostraBtnEnviarCrm() {
        return mostraBtnEnviarCrm;
    }

    public void setMostraBtnEnviarCrm(boolean mostraBtnEnviarCrm) {
        this.mostraBtnEnviarCrm = mostraBtnEnviarCrm;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public boolean isMostraMsg() {
        return mostraMsg;
    }

    public void setMostraMsg(boolean mostraMsg) {
        this.mostraMsg = mostraMsg;
    }

    public boolean isMostraCardIA() {
        return mostraCardIA;
    }

    public void setMostraCardIA(boolean mostraCardIA) {
        this.mostraCardIA = mostraCardIA;
    }

    public String getQtdAlunosSelecionados() {
        return qtdAlunosSelecionados;
    }

    public void setQtdAlunosSelecionados(String qtdAlunosSelecionados) {
        this.qtdAlunosSelecionados = qtdAlunosSelecionados;
    }
}

package relatorio.controle.contrato;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.contrato.ItemMatriAlunoHorarioTurmaVO;
import relatorio.negocio.jdbc.contrato.MatriculaAlunoHorarioTurmaRel;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> Lhoji Shiozawa
 */
public class MatriculaAlunoHorarioTurmaRelControleRel extends SuperControleRelatorio {

    protected MatriculaAlunoHorarioTurmaRel matriculaAlunoHorarioTurmaRel = new MatriculaAlunoHorarioTurmaRel();
    private ItemMatriAlunoHorarioTurmaVO itemMatriAlunoHrVO;
    protected List listaSelectItemEmpresa = new ArrayList();
    protected List listaChamada = new ArrayList();
    private List listaDeChamada = new ArrayList();
    private String filtros;

    public MatriculaAlunoHorarioTurmaRelControleRel() throws Exception {
        setUsuario(getUsuarioLogado());
        montarListaSelectItemEmpresa();
        setMensagemID("msg_entre_prmrelatorio");
    }

    public void obterEmpresaEscolhida() throws Exception {
        if (getMatriculaAlunoHorarioTurmaRel().getEmpresa().getCodigo() != 0) {
            getMatriculaAlunoHorarioTurmaRel().setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getMatriculaAlunoHorarioTurmaRel().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        } else {
            getMatriculaAlunoHorarioTurmaRel().setEmpresa(new EmpresaVO());
        }
    }

    public void obterTurmaEscolhida() throws Exception {
        if (getMatriculaAlunoHorarioTurmaRel().getTurma().getCodigo() != 0) {
            getMatriculaAlunoHorarioTurmaRel().setTurma(getFacade().getTurma().consultarPorChavePrimaria(getMatriculaAlunoHorarioTurmaRel().getTurma().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
        } else {
            getMatriculaAlunoHorarioTurmaRel().setTurma(new TurmaVO());
        }
    }

    public void obterColaboradorEscolhido() throws Exception {
        if (getMatriculaAlunoHorarioTurmaRel().getProfessor().getCodigo() != 0) {
            getMatriculaAlunoHorarioTurmaRel().setProfessor(getFacade().getColaborador().consultarPorChavePrimaria(getMatriculaAlunoHorarioTurmaRel().getProfessor().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
        } else {
            getMatriculaAlunoHorarioTurmaRel().setProfessor(new ColaboradorVO());
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        try {
            permissaoFuncionalidade(getUsuarioLogado(),
                    "PermitirConsultarTurmasUsuarioNaoForResponsavel",
                    "9.53 - Permitir ao usuário consultar turmas até as que não for responsável");
        } catch (Exception ignored) {
            matriculaAlunoHorarioTurmaRel.getProfessor().setCodigo(getUsuarioLogado().getColaboradorVO().getCodigo());
        }

        setListaChamada(matriculaAlunoHorarioTurmaRel.consultarListaChamada());
        List listaRegistro = matriculaAlunoHorarioTurmaRel.consultarAlunoHorarioTurma(getListaChamada(), false);
        Ordenacao.ordenarLista(listaRegistro, "horaInicial");
        setListaDeChamada(listaRegistro);
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromSession("ExportadorListaControle");
        exportadorListaControle.exportar(evt);
    }

    public void imprimirPDF() {
        try {
            setTipoRelatorio("PDF");
            obterEmpresaEscolhida();
            try {
                permissaoFuncionalidade(getUsuarioLogado(),
                        "PermitirConsultarTurmasUsuarioNaoForResponsavel",
                        "9.53 - Permitir ao usuário consultar turmas até as que não for responsável");
            } catch (Exception ignored) {
                matriculaAlunoHorarioTurmaRel.getProfessor().setCodigo(getUsuarioLogado().getColaboradorVO().getCodigo());
            }

            setListaChamada(matriculaAlunoHorarioTurmaRel.consultarListaChamada());
            if (!getListaChamada().isEmpty()) {
                setListaRelatorio(getListaChamada());
            } else {
                throw new Exception("Dados não encontrados!");
            }
            setFiltros(getDescricaoFiltros());
            setRelatorio("sim");
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setRelatorio("nao");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private String getDescricaoFiltros() throws Exception {
        String strFiltros = "";
        if (getEmpresa() != null) {
            strFiltros += "<b>Empresa: </b> " + getEmpresaLista();
        }
        strFiltros += " <b>Período de: </b>" + Uteis.getData(getMatriculaAlunoHorarioTurmaRel().getDataInicio()) + " <b>Até: </b>" + Uteis.getData(getMatriculaAlunoHorarioTurmaRel().getDataTermino()) + "</br>";
        if (!getMatriculaAlunoHorarioTurmaRel().getTurma().toString().equals("")) {
            strFiltros += "<b>Turma: </b>" + getMatriculaAlunoHorarioTurmaRel().getTurma();
        }
        if (!getMatriculaAlunoHorarioTurmaRel().getProfessor().toString().equals("")) {
            strFiltros += "<b>Colaborador: </b>" + getMatriculaAlunoHorarioTurmaRel().getProfessor();
        }
        return strFiltros;
    }

    private String getEmpresaLista() throws Exception {
        setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        return getEmpresa().getNome();
    }

    public void imprimirHTML() {
        try {
            setTipoRelatorio("HTML");
            setListaChamada(matriculaAlunoHorarioTurmaRel.consultarListaChamada());

            if (!getListaChamada().isEmpty()) {
                setListaRelatorio(getListaChamada());
            } else {
                throw new Exception("Dados não encontrados!");
            }
            setRelatorio("sim");
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setRelatorio("nao");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void imprimirRelatorio() throws Exception {
        try {
            String nomeRelatorio = matriculaAlunoHorarioTurmaRel.getIdEntidade();
            List listaRegistro = matriculaAlunoHorarioTurmaRel.consultarAlunoHorarioTurma(getListaChamada(), false);
            Ordenacao.ordenarLista(listaRegistro, "horaInicial");
            EmpresaVO empre = matriculaAlunoHorarioTurmaRel.getEmpresa();
            String titulo = "Lista de Chamada";
            String barra = "/";
            if (empre.getCidade().getNome().equals("")) {
                barra = "";
            }

            setTipoRelatorio("PDF");

            String design = matriculaAlunoHorarioTurmaRel.getDesignIReportRelatorio();

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("nomeRelatorio", nomeRelatorio);
            params.put("nomeEmpresa", empre.getNome());

            params.put("tituloRelatorio", titulo);
            params.put("nomeDesignIReport", design);
            params.put("nomeUsuario", getUsuarioLogado().getNome());
            params.put("filtros", matriculaAlunoHorarioTurmaRel.getDescricaoFiltros());
            params.put("caminhoParserXML", "/" + matriculaAlunoHorarioTurmaRel.getIdEntidade() + "/registros");
            params.put("dataIni", Uteis.getData(matriculaAlunoHorarioTurmaRel.getDataInicio()));
            params.put("dataFim", Uteis.getData(matriculaAlunoHorarioTurmaRel.getDataTermino()));
            params.put("enderecoEmpresa", empre.getEndereco() + " " + empre.getNumero() + " " + empre.getSetor());
            params.put("cidadeEmpresa", empre.getCidade().getNome() + barra + empre.getCidade().getEstado().getSigla());
            params.put("listaObjetos", listaRegistro);
            params.put("SUBREPORT_DIR", MatriculaAlunoHorarioTurmaRel.getCaminhoSubRelatorio());
            params.put("group_startnewpage", isQuebrarPagina());

            apresentarRelatorioObjetos(params);

            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<SelectItem> getListaSelectItemColaborador() throws Exception {
        boolean apresentarSomenteTurmaQueForResponsavel = false;
        try {
            permissaoFuncionalidade(getUsuarioLogado(),
                    "PermitirConsultarTurmasUsuarioNaoForResponsavel",
                    "9.53 - Permitir ao usuário consultar turmas até as que não for responsável");
        } catch (Exception ignored) {
            apresentarSomenteTurmaQueForResponsavel = true;
        }

        List<SelectItem> objs = new ArrayList<SelectItem>();
        List listaColaborador = getFacade().getColaborador().consultarPorNomeTipoColaborador("", getEmpresaLogado().getCodigo(), true, false, Uteis.NIVELMONTARDADOS_MINIMOS, TipoColaboradorEnum.PROFESSOR);
        objs.add(new SelectItem(0, ""));
        Iterator i = listaColaborador.iterator();
        while (i.hasNext()) {
            ColaboradorVO colaborador = (ColaboradorVO) i.next();
            if (!apresentarSomenteTurmaQueForResponsavel || colaborador.getPessoa().getCodigo().equals(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo())) {
                objs.add(new SelectItem(colaborador.getCodigo(), colaborador.getPessoa().getNome()));
            }
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List<SelectItem> getListaSelectItemTurma() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        List listaTurma = getFacade().getTurma().consultarPorCodigoModalidadeVigenciaMaiorQueHoje(0, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        objs.add(new SelectItem(0, ""));
        Iterator i = listaTurma.iterator();
        while (i.hasNext()) {
            TurmaVO turma = (TurmaVO) i.next();
            objs.add(new SelectItem(turma.getCodigo(), turma.getDescricao() + " - " + turma.getIdentificador()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public final void montarListaSelectItemEmpresa() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaSelectItemEmpresa(objs);
        getMatriculaAlunoHorarioTurmaRel().setEmpresa(getEmpresaLogado());
    }

    public MatriculaAlunoHorarioTurmaRel getMatriculaAlunoHorarioTurmaRel() {
        return matriculaAlunoHorarioTurmaRel;
    }

    public void setMatriculaAlunoHorarioTurmaRel(MatriculaAlunoHorarioTurmaRel matriculaAlunoHorarioTurmaRel) {
        this.matriculaAlunoHorarioTurmaRel = matriculaAlunoHorarioTurmaRel;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public List getListaChamada() {
        return listaChamada;
    }

    public void setListaChamada(List listaChamada) {
        this.listaChamada = listaChamada;
    }

    public void novo() {
    }

    @Override
    protected void limparRecursosMemoria() {
        matriculaAlunoHorarioTurmaRel = null;
    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public ItemMatriAlunoHorarioTurmaVO getItemMatriAlunoHrVO() {
        return itemMatriAlunoHrVO;
    }

    public void setItemMatriAlunoHrVO(ItemMatriAlunoHorarioTurmaVO itemMatriAlunoHrVO) {
        this.itemMatriAlunoHrVO = itemMatriAlunoHrVO;
    }

    public List getListaDeChamada() {
        return listaDeChamada;
    }

    public void setListaDeChamada(List listaDeChamada) {
        this.listaDeChamada = listaDeChamada;
    }
}

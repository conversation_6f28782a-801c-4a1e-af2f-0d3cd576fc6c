package relatorio.controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.integracao.pactopay.dto.EnderecoDTO;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.jdbc.financeiro.ParcelaEmAbertoSPCRel;
import relatorio.negocio.jdbc.financeiro.ParcelaSPCTO;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.impl.microsservice.integracoes.AcaoSPCCallable;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

public class ParcelaEmAbertoSPCControleRel extends SuperControleRelatorio {

    private int RELATORIO_RETRATO = 0;
    private int RELATORIO_PAISAGEM = 1;
    private List<SelectItem> listaSelectItemTipoSituacao;
    private List<SelectItem> listaSelectItemTipoSituacaoSPC;
    private ParcelaEmAbertoSPCRel parcelaEmAbertoRel;
    private String campoConsultarCliente;
    private String valorConsultarCliente;
    private List<ClienteVO> listaConsultarCliente;
    private List<SelectItem> listaTipoRelatorio;
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;
    private boolean gerarMultasJuros = false;
    private List<ParcelaSPCTO> listaParcelas;
    private double valorTotalPago = 0.0;
    private double valorTotalEmAberto = 0.0;
    private double valorTotalCancelado = 0.0;
    private double valorTotalNegativadas = 0.0;
    private List<String> alunosEmAberto = new ArrayList<>();
    private List<String> alunosPago = new ArrayList<>();
    private List<String> alunosCancelado = new ArrayList<>();
    private List<String> alunosNegativados = new ArrayList<>();
    private int qtdeParcelasEmAberto = 0;
    private int qtdeParcelasPago = 0;
    private int qtdeParcelasCancelado = 0;
    private int qtdeParcelasNegativadas = 0;
    private int totalAlunos = 0;
    private int totalAlunosNegativados = 0;
    private boolean marcarTodasParcelas = false;
    private List<SelectItem> listaSelectPlanos;
    private static final Integer CODIGO_PLANO_TODOS = 9999;
    private List<SelectItem> planosSelecionados;
    private String abrirRelatorio;
    private SelectItem planoParaRemoverDaLista;
    private boolean mostrarNrTentativasMovitoRetorno = false;
    private boolean apresentarFormaPagamento = false;
    private Integer codigoPlanoSelecionadoModal = null;

    public ParcelaEmAbertoSPCControleRel() throws Exception {
        novo();
    }

    public void montarListaSelectItemSituacao() {
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem("", ""));
        objs.add(new SelectItem("PG", "Pago"));
        objs.add(new SelectItem("EA", "Em Aberto"));
        objs.add(new SelectItem("CA", "Cancelado"));
        objs.add(new SelectItem("RG", "Renegociado"));
        setListaSelectItemTipoSituacao(objs);
    }

    public void montarListaSelectItemSituacaoSPC() {
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem("", "Todas parcelas"));
        objs.add(new SelectItem("NG", "Negativadas"));
        objs.add(new SelectItem("PS", "Positivadas"));
        setListaSelectItemTipoSituacaoSPC(objs);
    }

    public void inicializarDados() throws Exception {
        setParcelaEmAbertoRel(new ParcelaEmAbertoSPCRel());
        setCampoConsultarCliente("");
        setValorConsultarCliente("");
        setListaConsultarCliente(new ArrayList<>());
        setListaParcelas(new ArrayList<>());
        valorTotalPago = 0.0;
        valorTotalEmAberto = 0.0;
        valorTotalCancelado = 0.0;
        alunosEmAberto = new ArrayList<>();
        alunosPago = new ArrayList<>();
        alunosCancelado = new ArrayList<>();
        alunosNegativados = new ArrayList<>();
        qtdeParcelasEmAberto = 0;
        qtdeParcelasPago = 0;
        qtdeParcelasCancelado = 0;
        totalAlunos = 0;
        gerarMultasJuros = false;
        montarListaPlanos();
    }

    private List<ParcelaSPCTO> consultarParcelasAberto() throws Exception{
        List<ParcelaSPCTO> parcelasAberto = parcelaEmAbertoRel.consultarTodosParcelasEmAberto(getFiltroEmpresa());
        parcelaEmAbertoRel.setTotalJuroeMulta(0.0d);
        if(isGerarMultasJuros()){
            parcelasAberto.forEach(e -> {
                if (e.getSituacao().equals("EA")) {
                    e.setMultas(parcelaEmAbertoRel.calcularMulta(e.getValor()));
                    e.setJuros(parcelaEmAbertoRel.calcularJuros(e.getValor(), e.getDateVencimento(), e.getDataPagamento()));
                    parcelaEmAbertoRel.setTotalJuroeMulta(parcelaEmAbertoRel.getTotalJuroeMulta() + e.getMultas() + e.getJuros());
                }
            });
        }
        return parcelasAberto;
    }

    public List<ParcelaSPCTO> parcelasEmAbertas() throws Exception{
        List<ParcelaSPCTO> parcelasAberto = consultarParcelasAberto();
        return parcelasAberto;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle)JSFUtilities.getManagedBean("ExportadorListaControle");
        List<ParcelaSPCTO> parcelasAberto = parcelasEmAbertas();
        if(!UteisValidacao.emptyList(parcelasAberto)){
            exportadorListaControle.exportar(evt, parcelasAberto, "", null);
        }
        prepararAbrirRelatorio(RELATORIO_RETRATO);
    }
    public void prepararAbrirRelatorio(int tipo){
        if(tipo == RELATORIO_RETRATO){
            ExportadorListaControle exp = (ExportadorListaControle)getControlador("ExportadorListaControle");
            if(UteisValidacao.emptyString(exp.getFileName())) {
                montarErro("Não foi encontrado resultados para o relatório!");
                abrirRelatorio = getMensagemNotificar();
            }else{
                abrirRelatorio = "location.href=\"../UpdateServlet?op=downloadfile&file="+exp.getFileName()+"&mimetype=application/vnd.ms-excel\"";
                abrirRelatorio += exp.getMsgAlert();
                limparMsg();
            }
        }else if(tipo == RELATORIO_PAISAGEM){
            if(UteisValidacao.emptyString(getNomeArquivoRelatorioGeradoAgora())) {
                montarErro("Não foi encontrado resultados para o relatório!");
                abrirRelatorio = getMensagemNotificar();
            }else{
                abrirRelatorio =   "window.open('" + getNomeArquivoRelatorioGeradoAgora() + "', '_blank', 'location=yes');";
                limparMsg();
            }
        }
    }

    public void imprimirPDF() {
        try {
            parcelaEmAbertoRel.setDescricaoFiltros("");
            String titulo = " Relatório de Gestão de Negativações ";
            parcelaEmAbertoRel.setTotalParcelaEmAberto(0);
            parcelaEmAbertoRel.setValorTotalParcelaEmAberto(0.0);
            parcelaEmAbertoRel.setTotalParcelaPaga(0);
            parcelaEmAbertoRel.setValorTotalParcelaPaga(0.0);
            parcelaEmAbertoRel.setTotalParcelacCancelada(0);
            parcelaEmAbertoRel.setValorTotalParcelaCancelada(0.0);
            String xml = parcelaEmAbertoRel.emitirRelatorio(getFiltroEmpresa(), consultarParcelasAberto());
            parcelaEmAbertoRel.setValorTotalParcelaEmAberto(Uteis.arredondarForcando2CasasDecimais(parcelaEmAbertoRel.getValorTotalParcelaEmAberto()));
            parcelaEmAbertoRel.setValorTotalParcelaPaga(Uteis.arredondarForcando2CasasDecimais(parcelaEmAbertoRel.getValorTotalParcelaPaga()));
            parcelaEmAbertoRel.setValorTotalParcelaCancelada(Uteis.arredondarForcando2CasasDecimais(parcelaEmAbertoRel.getValorTotalParcelaCancelada()));

            String design = parcelaEmAbertoRel.getDesignIReportRelatorio();
            apresentarRelatorio(
                    parcelaEmAbertoRel.getIdEntidade(),
                    xml,
                    titulo,
                    getNomeEmpresaSelecionada(getFiltroEmpresa()),
                    "",
                    "PDF", "/" + parcelaEmAbertoRel.getIdEntidade() + "/registros",
                    design,
                    getUsuarioLogado().getNome(),
                    parcelaEmAbertoRel.getDescricaoFiltros(),
                    parcelaEmAbertoRel.getTotalParcelaEmAberto().toString(), parcelaEmAbertoRel.getValorTotalParcelaEmAberto().toString(),
                    parcelaEmAbertoRel.getTotalParcelaPaga().toString(), parcelaEmAbertoRel.getValorTotalParcelaPaga().toString(),
                    parcelaEmAbertoRel.getTotalParcelacCancelada().toString(), parcelaEmAbertoRel.getValorTotalParcelaCancelada().toString(),
                    parcelaEmAbertoRel.getTotalMatriculas().toString(),
                    parcelaEmAbertoRel.getTotalJuroeMulta().toString(),
                    isGerarMultasJuros(),
                    true);
            setMensagemID("msg_relatorio_ok");
        } catch (ConsistirException e) {
            e.printStackTrace();
            setMensagemDetalhada("Nenhum Registro Encontrado!");
        } catch (Exception e) {
            e.printStackTrace();
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e.getMessage());
        }
    }

    public void imprimirHorizontal() {
        parcelaEmAbertoRel.setIdRelatorio("ParcelaEmAbertoRel-Paisagem");
        imprimirPDF();
        prepararAbrirRelatorio(RELATORIO_PAISAGEM);
    }

    public boolean isMostrarNrTentativasMovitoRetorno() {
        return mostrarNrTentativasMovitoRetorno;
    }

    public boolean isApresentarFormaPagamento() {
        return apresentarFormaPagamento;
    }

    public void setApresentarFormaPagamento(boolean apresentarFormaPagamento) {
        this.apresentarFormaPagamento = apresentarFormaPagamento;
    }

    public String getAtributos() {
        String colunasJurosMulta = isGerarMultasJuros() ? "juros=Juros,multas=Multa," : "";
        String colunasNrTentativasMotivoRetorno = isMostrarNrTentativasMovitoRetorno() ? "nrTentativas=Nr. Tentativas,motivoRetorno=Motivo Retorno," : "";
        String colunasInformacoesPagamento = isApresentarFormaPagamento() ? "dataPagamento=Dt.Pagamento,formasPagamento=Forma," : "";
        String colunasDadosSensiveis = parcelaEmAbertoRel.isApresentarDadosSensiveis() ? "cpf=CPF,endereco=Endereço," : "";
        return ("nomeempresa=Empresa,matricula=Matrícula,nome=Nome,telefone=Telefones,contrato=Nr.Contrato,descricaoParcela=Desc.Parcela,situacao_Apresentar=Situação,valor=Valor,dataCancelamento=Dt.Cancelamento,"
                + colunasJurosMulta + "recorrencia_Apresentar=Recorrência,"
                + colunasNrTentativasMotivoRetorno
                + "dataFatura=Dt.Faturamento,dateVencimento=Dt.Vencimento," + colunasInformacoesPagamento + "nome_plano=Plano,"
                + "convenioCobranca=Convênios,"
                + colunasDadosSensiveis
                + "modalidades=Modalidades"
        );
    }

    public void irParaTelaCliente() {
        ParcelaSPCTO obj = (ParcelaSPCTO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        setMsgAlert("");
        limparMsg();
        try {
            if (obj == null) {
                throw new Exception("Cliente não encontrado.");
            } else if (UteisValidacao.emptyNumber(obj.getCliente())) {
                throw new Exception("Cliente não encontrado.");
            } else {
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(obj.getCliente());
                irParaTelaCliente(clienteVO);
                setMsgAlert("abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700)");
            }
        } catch (Exception e) {
            e.printStackTrace();
            setMensagemDetalhada(MSG_ERRO, e.getMessage());
            montarErro(e.getMessage());
        }
    }

    public void negativarParcelas() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);

        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);

                limparMsg();
                List<ParcelaSPCTO> parcelasNegativar = listaParcelas.stream()
                        .filter(obj -> !obj.isIncluidaSpc() && obj.isSelecionada() && !obj.getSituacao().equals("RG"))
                        .collect(Collectors.toList());

                long qtdSelecionada = parcelasNegativar.size();

                if (qtdSelecionada <= 0) {
                    throw new ConsistirException("Não selecionou nenhuma parcela para negativar. Parcelas renegociadas não podem ser negativadas.");
                }

                EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (UteisValidacao.emptyString(empresaVO.getOperadorSpc())
                        || UteisValidacao.emptyString(empresaVO.getSenhaSpc())
                        || UteisValidacao.emptyNumber(empresaVO.getCodigoAssociadoSpc())) {
                    throw new ConsistirException("Código do Associado, Operador e senha SPC não estão configurados para esta empresa.");
                }

                ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave((String) JSFUtilities.getFromSession(JSFUtilities.KEY));

                //Hibrael - 20/03/2025 - Verifica se aluno e de menor e se tem pessoa responsavel valida vinculada, se tiver alterar na parcela a pessoa a ser negativada para o responsavel
                for(ParcelaSPCTO parcela : parcelasNegativar) {
                    if(parcela != null && !UteisValidacao.emptyNumber(parcela.getCodigoPessoa())) {
                        PessoaVO pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(parcela.getCodigoPessoa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if(pessoaVO != null && !UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                            if(pessoaVO.getDataNasc() != null && pessoaVO.getIdade() < 18) {
                                if(parcela.getPessoaResponsavel() != null && !UteisValidacao.emptyNumber(parcela.getPessoaResponsavel().getCodigo())) {
                                    if(!UteisValidacao.emptyString(parcela.getPessoaResponsavel().getCfp()) && !UteisValidacao.emptyString(parcela.getPessoaResponsavel().getNome())
                                            && !UteisValidacao.emptyString(parcela.getPessoaResponsavel().getEmail()) && parcela.getPessoaResponsavel().getDataNasc() != null
                                            && !UteisValidacao.emptyList(parcela.getPessoaResponsavel().getEnderecoVOs()) && parcela.getPessoaResponsavel().getIdade() > 18) {

                                        parcela.setCpf(parcela.getPessoaResponsavel().getCfp());
                                        parcela.setCpfApresentar(parcela.getPessoaResponsavel().getCfp());
                                        parcela.setNome(parcela.getPessoaResponsavel().getNome());
                                        parcela.setNomeApresentar(parcela.getPessoaResponsavel().getNome());
                                        parcela.setEmailValidado(parcela.getPessoaResponsavel().getEmail());
                                        parcela.setEmail(parcela.getPessoaResponsavel().getEmail());
                                        parcela.setDataNascimento(parcela.getPessoaResponsavel().getDataNasc());

                                        if(!UteisValidacao.emptyList(parcela.getPessoaResponsavel().getTelefoneVOs())) {
                                            TelefoneVO telefoneVO = Collections.max(parcela.getPessoaResponsavel().getTelefoneVOs(), Comparator.comparingInt(TelefoneVO::getCodigo));
                                            parcela.setTelefone(telefoneVO.getNumero());
                                        }

                                        EnderecoVO enderecoVO = Collections.max(parcela.getPessoaResponsavel().getEnderecoVOs(), Comparator.comparingInt(EnderecoVO::getCodigo));
                                        EnderecoDTO enderecoDTO = new EnderecoDTO();
                                        if(enderecoVO != null) {
                                            enderecoDTO.setCep(enderecoVO.getCep());
                                            enderecoDTO.setEndereco(enderecoVO.getEndereco());
                                            enderecoDTO.setNumero(enderecoVO.getNumero());
                                            enderecoDTO.setComplemento(enderecoVO.getComplemento());
                                            enderecoDTO.setBairro(enderecoVO.getBairro());
                                        }
                                        parcela.setEndereco(enderecoDTO);
                                    }
                                }
                            } else if (pessoaVO.getDataNasc() != null && pessoaVO.getIdade() >= 18) {
                                parcela.setCpfApresentar(parcela.getCpf());
                            }
                        }
                    }
                }

                List<AcaoSPCCallable> callableTasks = new ArrayList<>();
                for (ParcelaSPCTO parcelaEmAbertoSPCRelTO : parcelasNegativar) {
                    callableTasks.add(new AcaoSPCCallable()
                            .setIntegracoesMsUrl(clientDiscoveryDataDTO.getServiceUrls().getIntegracoesMsUrl())
                            .setParcela(parcelaEmAbertoSPCRelTO)
                            .setEmpresaVO(empresaVO)
                            .setResponsavel(auto.getUsuario())
                            .setMovparcelaDAO(getFacade().getMovParcela())
                            .setLogDAO(getFacade().getLog())
                            .setAcaoSPC(AcaoSPCCallable.INCLUIR)
                            .setChave((String) JSFUtilities.getFromSession(JSFUtilities.KEY)));
                }

                final ExecutorService executorService = Executors.newFixedThreadPool(5);
                executorService.invokeAll(callableTasks);
                executorService.shutdown();

                avaliarTotais();
                montarSucessoDadosGravados();
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }

            @Override
            public String getExecutarAoCompletar() {
                return getMensagemNotificar(false);
            }
        };

        limparMsg();
        auto.autorizar("Confirmação para Negativar parcelas", "NegativarLiberarParcelasGestaoNegativacoes",
                "Você precisa da permissão \"2.82 - Negativar/Liberar parcelas na Gestão de Negativações\"",
                "v20_form,v20_GridListagemParcelas", listener);
    }

    public void liberarParcelas() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);

        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);

                limparMsg();
                List<ParcelaSPCTO> parcelasRemoverNegativacao = listaParcelas.stream()
                        .filter(ParcelaSPCTO::isSelecionada)
                        .filter(ParcelaSPCTO::isIncluidaSpc)
                        .collect(Collectors.toList());
                long qtdSelecionada = parcelasRemoverNegativacao.size();

                if (qtdSelecionada <= 0) {
                    throw new ConsistirException("Não selecionou nenhuma parcela para remover negativação.");
                }

                EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (UteisValidacao.emptyString(empresaVO.getOperadorSpc())
                        || UteisValidacao.emptyString(empresaVO.getSenhaSpc())
                        || UteisValidacao.emptyNumber(empresaVO.getCodigoAssociadoSpc())) {
                    throw new ConsistirException("Código do Associado, Operador e senha SPC não estão configurados para esta empresa.");
                }

                ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave((String) JSFUtilities.getFromSession(JSFUtilities.KEY));

                List<AcaoSPCCallable> callableTasks = new ArrayList<>();
                for (ParcelaSPCTO parcelaEmAbertoSPCRelTO : parcelasRemoverNegativacao) {
                    callableTasks.add(new AcaoSPCCallable()
                            .setIntegracoesMsUrl(clientDiscoveryDataDTO.getServiceUrls().getIntegracoesMsUrl())
                            .setParcela(parcelaEmAbertoSPCRelTO)
                            .setEmpresaVO(empresaVO)
                            .setResponsavel(auto.getUsuario())
                            .setMovparcelaDAO(getFacade().getMovParcela())
                            .setLogDAO(getFacade().getLog())
                            .setAcaoSPC(AcaoSPCCallable.EXCLUIR)
                            .setEnderecoDAO(getFacade().getEndereco())
                            .setChave((String) JSFUtilities.getFromSession(JSFUtilities.KEY)));
                }

                final ExecutorService executorService = Executors.newFixedThreadPool(5);
                executorService.invokeAll(callableTasks);
                executorService.shutdown();

                avaliarTotais();
                montarSucessoDadosGravados();
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }

            @Override
            public String getExecutarAoCompletar() {
                return getMensagemNotificar(false);
            }
        };

        limparMsg();
        auto.autorizar("Confirmação para Negativar parcelas", "NegativarLiberarParcelasGestaoNegativacoes",
                "Você precisa da permissão \"2.82 - Negativar/Liberar parcelas na Gestão de Negativações\"",
                "v20_form,v20_GridListagemParcelas", listener);
    }

    public void consultarParcelas() {
        try {
            limparMsg();
            acaoConsultarParcelas();
        } catch (Exception e) {
            if(e.getMessage().equals("Nenhum Registro Encontrado!")){
                limparDados();
            }
            montarErro(e.getMessage());
            e.printStackTrace();
        }
    }

    private void limparDados(){
        valorTotalPago = 0;
        valorTotalEmAberto = 0;
        valorTotalCancelado = 0;
        valorTotalNegativadas = 0;
        qtdeParcelasEmAberto = 0;
        qtdeParcelasPago = 0;
        qtdeParcelasCancelado = 0;
        qtdeParcelasNegativadas = 0;
    }

    private void acaoConsultarParcelas() throws Exception {
        listaParcelas = new ArrayList<>();
        listaParcelas = parcelaEmAbertoRel.consultarTodosParcelasEmAberto(getFiltroEmpresa());
        avaliarTotais();
    }

    private void avaliarTotais() {
        valorTotalPago = 0;
        valorTotalEmAberto = 0;
        valorTotalCancelado = 0;
        valorTotalNegativadas = 0;
        qtdeParcelasEmAberto = 0;
        qtdeParcelasPago = 0;
        qtdeParcelasCancelado = 0;
        qtdeParcelasNegativadas = 0;

        alunosEmAberto = new ArrayList<>();
        alunosPago = new ArrayList<>();
        alunosCancelado = new ArrayList<>();
        alunosNegativados = new ArrayList<>();

        totalAlunos = 0;

        parcelaEmAbertoRel.setTotalJuroeMulta(0.0d);
        if (isGerarMultasJuros()) {
            listaParcelas.forEach(e -> {
                if (e.getSituacao().equals("EA")) {
                    e.setMultas(parcelaEmAbertoRel.calcularMulta(e.getValor()));
                    e.setJuros(parcelaEmAbertoRel.calcularJuros(e.getValor(), e.getDateVencimento(), e.getDataPagamento()));
                    parcelaEmAbertoRel.setTotalJuroeMulta(parcelaEmAbertoRel.getTotalJuroeMulta() + e.getMultas() + e.getJuros());
                }
            });
        }

        for (ParcelaSPCTO parcela : listaParcelas) {
            if (parcela.isIncluidaSpc()) {
                avaliarParcelaNegativada(parcela);
            }

            switch (parcela.getSituacao()) {
                case "EA":
                    avaliarParcelaEmAberto(parcela);
                    break;
                case "PG":
                    avaliarParcelaPaga(parcela);
                    break;
                case "CA":
                    avaliarParcelaCancelada(parcela);
                    break;
                default:
                    break;
            }
        }
        alunosEmAberto = alunosEmAberto.stream().distinct().collect(Collectors.toList());
        alunosPago = alunosPago.stream().distinct().collect(Collectors.toList());
        alunosCancelado = alunosCancelado.stream().distinct().collect(Collectors.toList());
        alunosNegativados = alunosNegativados.stream().distinct().collect(Collectors.toList());
        alunosEmAberto.addAll(alunosPago);
        alunosEmAberto.addAll(alunosCancelado);
        totalAlunos = (int) alunosEmAberto.stream().distinct().count();
    }

    private void avaliarParcelaCancelada(ParcelaSPCTO parcela) {
        valorTotalCancelado += Uteis.arredondar(parcela.getValorMultasJuros(), 2, 1);
        qtdeParcelasCancelado += 1;
        if (UteisValidacao.emptyNumber(parcela.getCodigoPessoa())) {
            alunosCancelado.add(parcela.getNome());
        } else {
            alunosCancelado.add(parcela.getCodigoPessoa().toString());
        }
    }


    private void avaliarParcelaNegativada(ParcelaSPCTO parcela) {
        valorTotalNegativadas += Uteis.arredondar(parcela.getValorMultasJuros(), 2, 1);
        qtdeParcelasNegativadas += 1;
        if (UteisValidacao.emptyNumber(parcela.getCodigoPessoa())) {
            alunosNegativados.add(parcela.getNome());
        } else {
            alunosNegativados.add(parcela.getCodigoPessoa().toString());
        }
    }

    private void avaliarParcelaPaga(ParcelaSPCTO parcela) {
        valorTotalPago += Uteis.arredondar(parcela.getValorMultasJuros(), 2, 1);
        qtdeParcelasPago += 1;
        if (UteisValidacao.emptyNumber(parcela.getCodigoPessoa())) {
            alunosPago.add(parcela.getNome());
        } else {
            alunosPago.add(parcela.getCodigoPessoa().toString());
        }
    }

    private void avaliarParcelaEmAberto(ParcelaSPCTO parcela) {
        valorTotalEmAberto += Uteis.arredondar(parcela.getValorMultasJuros(), 2, 1);
        qtdeParcelasEmAberto += 1;
        if (UteisValidacao.emptyNumber(parcela.getCodigoPessoa())) {
            alunosEmAberto.add(parcela.getNome()); // venda ao consumidor, onde não existe cadastro no sistema
        } else {
            alunosEmAberto.add(parcela.getCodigoPessoa().toString());
        }
    }

    public void consultarCliente() {
        try {
            List<ClienteVO> objs = new ArrayList<>();
            switch (getCampoConsultarCliente()) {
                case "nome":
                    objs = getFacade().getCliente().consultarPorNomePessoa(getValorConsultarCliente(), 0, Uteis.NIVELMONTARDADOS_MINIMOS, null);
                    break;
                case "matricula":
                    objs = getFacade().getCliente().consultarPorMatricula(getValorConsultarCliente(), 0, true, Uteis.NIVELMONTARDADOS_MINIMOS);
                    break;
                case "cpf":
                    objs = getFacade().getCliente().consultarPorNomeCPF("", getValorConsultarCliente(), 0, Uteis.NIVELMONTARDADOS_MINIMOS);
                    break;
                default:
                    break;
            }
            setListaConsultarCliente(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarCliente(new ArrayList<>());
            setMensagemDetalhada(MSG_ERRO, e.getMessage());
        }
    }

    public void selecionarCliente() {
        try {
            ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
            getParcelaEmAbertoRel().setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        } catch (Exception e) {
            getParcelaEmAbertoRel().setClienteVO(new ClienteVO());
            setMensagemDetalhada(MSG_ERRO, e.getMessage());
        }

    }

    public List<SelectItem> getTipoConsultaComboCliente() {
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem("nome", "Nome"));
        objs.add(new SelectItem("matricula", "Matrícula"));
        objs.add(new SelectItem("cpf", "CPF"));
        return objs;
    }

    public void montarListaSelectItemEmpresa() {
        try {
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void limparCampoCliente() {
        getParcelaEmAbertoRel().setClienteVO(new ClienteVO());
    }

    public List<SelectItem> getListaSelectItemTipoSituacao() {
        return listaSelectItemTipoSituacao;
    }

    public void setListaSelectItemTipoSituacao(List<SelectItem> listaSelectItemTipoSituacao) {
        this.listaSelectItemTipoSituacao = listaSelectItemTipoSituacao;
    }

    public List<SelectItem> getListaSelectItemTipoSituacaoSPC() {
        return listaSelectItemTipoSituacaoSPC;
    }

    public void setListaSelectItemTipoSituacaoSPC(List<SelectItem> listaSelectItemTipoSituacaoSPC) {
        this.listaSelectItemTipoSituacaoSPC = listaSelectItemTipoSituacaoSPC;
    }

    public ParcelaEmAbertoSPCRel getParcelaEmAbertoRel() {
        return parcelaEmAbertoRel;
    }

    public void setParcelaEmAbertoRel(ParcelaEmAbertoSPCRel parcelaEmAbertoRel) {
        this.parcelaEmAbertoRel = parcelaEmAbertoRel;
    }

    public String getCampoConsultarCliente() {
        return campoConsultarCliente;
    }

    public void setCampoConsultarCliente(String campoConsultarCliente) {
        this.campoConsultarCliente = campoConsultarCliente;
    }

    public List<ClienteVO> getListaConsultarCliente() {
        return listaConsultarCliente;
    }

    public void setListaConsultarCliente(List<ClienteVO> listaConsultarCliente) {
        this.listaConsultarCliente = listaConsultarCliente;
    }

    public String getValorConsultarCliente() {
        return valorConsultarCliente;
    }

    public void setValorConsultarCliente(String valorConsultarCliente) {
        this.valorConsultarCliente = valorConsultarCliente;
    }

    public void novo() throws Exception {
        inicializarDados();
        montarListaSelectItemSituacao();
        montarListaSelectItemSituacaoSPC();
        montarListaSelectItemEmpresa();
    }

    public void adicionarSituacaoNaLista() {
        SelectItem itemSelecionado = null;
        for (SelectItem itemSituacao : getListaSelectItemTipoSituacao()) {
            if (!parcelaEmAbertoRel.getSituacao().equals("") && itemSituacao.getValue().equals(parcelaEmAbertoRel.getSituacao())) {
                itemSelecionado = itemSituacao;
                parcelaEmAbertoRel.getSituacoesSelecionadas().add(itemSituacao);
                parcelaEmAbertoRel.setSituacao("");
                break;
            }
        }

        if (itemSelecionado != null) {
            getListaSelectItemTipoSituacao().remove(itemSelecionado);
        }
    }

    public void limparSituacoes() {
        montarListaSelectItemSituacao();
        montarListaSelectItemSituacaoSPC();
        parcelaEmAbertoRel.setSituacoesSelecionadas(new ArrayList<>());
    }

    public List<SelectItem> getListaTipoRelatorio() {
        if (listaTipoRelatorio == null) {
            listaTipoRelatorio = new ArrayList<>();
        }
        return listaTipoRelatorio;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public boolean isGerarMultasJuros() {
        return gerarMultasJuros;
    }

    public void setGerarMultasJuros(boolean gerarMultasJuros) {
        this.gerarMultasJuros = gerarMultasJuros;
    }

    public List<ParcelaSPCTO> getListaParcelas() {
        return listaParcelas;
    }

    public void setListaParcelas(List<ParcelaSPCTO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public double getValorTotalPago() {
        return valorTotalPago;
    }

    public void setValorTotalPago(double valorTotalPago) {
        this.valorTotalPago = valorTotalPago;
    }

    public double getValorTotalEmAberto() {
        return valorTotalEmAberto;
    }

    public void setValorTotalEmAberto(double valorTotalEmAberto) {
        this.valorTotalEmAberto = valorTotalEmAberto;
    }

    public double getValorTotalCancelado() {
        return valorTotalCancelado;
    }

    public void setValorTotalCancelado(double valorTotalCancelado) {
        this.valorTotalCancelado = valorTotalCancelado;
    }

    public double getValorTotalNegativadas() {
        return valorTotalNegativadas;
    }

    public void setValorTotalNegativadas(double valorTotalNegativadas) {
        this.valorTotalNegativadas = valorTotalNegativadas;
    }

    public String getValorTotalPago_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorTotalPago());
    }

    public String getValorTotalEmAberto_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorTotalEmAberto());
    }

    public String getValorTotalCancelado_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorTotalCancelado());
    }

    public String getValorTotalNegativadasApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorTotalNegativadas());
    }

    public Integer getQtdTotalParcelas() {
        return getListaParcelas().size();
    }

    public String getValorTotalParcelas_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorTotalEmAberto() + getValorTotalPago() + getValorTotalCancelado());
    }

    public List<String> getAlunosEmAberto() {
        return alunosEmAberto;
    }

    public void setAlunosEmAberto(List<String> alunosEmAberto) {
        this.alunosEmAberto = alunosEmAberto;
    }

    public Integer getTotalAlunosEmAberto() {
        return getAlunosEmAberto().size();
    }

    public List<String> getAlunosPago() {
        return alunosPago;
    }

    public void setAlunosPago(List<String> alunosPago) {
        this.alunosPago = alunosPago;
    }

    public Integer getTotalAlunosPago() {
        return getAlunosPago().size();
    }

    public List<String> getAlunosCancelado() {
        return alunosCancelado;
    }

    public void setAlunosCancelado(List<String> alunosCancelado) {
        this.alunosCancelado = alunosCancelado;
    }

    public Integer getTotalAlunosCancelado() {
        return getAlunosCancelado().size();
    }

    public List<String> getAlunosNegativados() {
        return alunosNegativados;
    }

    public void setAlunosNegativados(List<String> alunosNegativados) {
        this.alunosNegativados = alunosNegativados;
    }

    public Integer getTotalAlunosNegativados() {
        return getAlunosNegativados().size();
    }

    public int getTotalAlunos() {
        return totalAlunos;
    }

    public void setTotalAlunos(int totalAlunos) {
        this.totalAlunos = totalAlunos;
    }

    public int getQtdeParcelasEmAberto() {
        return qtdeParcelasEmAberto;
    }

    public void setQtdeParcelasEmAberto(int qtdeParcelasEmAberto) {
        this.qtdeParcelasEmAberto = qtdeParcelasEmAberto;
    }

    public int getQtdeParcelasPago() {
        return qtdeParcelasPago;
    }

    public void setQtdeParcelasPago(int qtdeParcelasPago) {
        this.qtdeParcelasPago = qtdeParcelasPago;
    }

    public int getQtdeParcelasCancelado() {
        return qtdeParcelasCancelado;
    }

    public void setQtdeParcelasCancelado(int qtdeParcelasCancelado) {
        this.qtdeParcelasCancelado = qtdeParcelasCancelado;
    }

    public int getQtdeParcelasNegativadas() {
        return qtdeParcelasNegativadas;
    }

    public void setQtdeParcelasNegativadas(int qtdeParcelasNegativadas) {
        this.qtdeParcelasNegativadas = qtdeParcelasNegativadas;
    }

    public boolean isMarcarTodasParcelas() {
        return marcarTodasParcelas;
    }

    public void setMarcarTodasParcelas(boolean marcarTodasParcelas) {
        this.marcarTodasParcelas = marcarTodasParcelas;
    }

    public void marcarTodasPacelasFinanceiro(){
        for (ParcelaSPCTO item : listaParcelas) {
            ParcelaSPCTO parcela = (ParcelaSPCTO) item;
            if(item.isPermiteSelecionar() && UteisValidacao.emptyString(item.getErro())){
                parcela.setSelecionada(marcarTodasParcelas);
            }
        }
    }

    public void montarListaPlanos() throws Exception {
        List<PlanoVO> planos = new ArrayList<PlanoVO>(){};
        if (UteisValidacao.emptyNumber(getFiltroEmpresa())) {
            planos = getFacade().getPlano().consultarTodos(null, Uteis.NIVELMONTARDADOS_MINIMOS);
        } else {
            if(permissaoConsultaTodasEmpresas){
                planos = getFacade().getPlano().consultarPorDescricaoDataIngresso(
                        "", Calendario.hoje(),
                        getFiltroEmpresa(), false, getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_MINIMOS).isControleAcessoMultiplasEmpresasPorPlano(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            } else {
                planos = getFacade().getPlano().consultarPorCodigoEmpresa(getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);
            }
        }

        List<SelectItem> itemPlanos = new ArrayList<SelectItem>();
        itemPlanos.add(new SelectItem("", ""));
        for (PlanoVO plano : planos) {
            itemPlanos.add(new SelectItem(plano.getCodigo(), plano.getDescricao()));
        }
        setListaSelectPlanos(itemPlanos);
    }

    public void adicionarPlanoNaLista() {
        SelectItem itemSelecionado = null;
        Integer codigoPlanoSelecionado = parcelaEmAbertoRel.getCodigoPlano();
        for (SelectItem itemPlano : getListaSelectPlanos()) {
            if (parcelaEmAbertoRel.getCodigoPlano() != 0 && itemPlano.getValue().equals(codigoPlanoSelecionado)) {
                itemSelecionado = itemPlano;
                parcelaEmAbertoRel.getPlanosSelecionados().add(itemPlano);
                parcelaEmAbertoRel.setCodigoPlano(0);
                break;
            }
        }

        if (itemSelecionado != null) {
            getListaSelectPlanos().remove(itemSelecionado);
            Ordenacao.ordenarLista(getListaSelectPlanos(), "label");
        }
    }

    public void adicionarPlanoNaListaModal() {
        SelectItem itemSelecionado = null;
        Integer codigoPlanoSelecionado = getCodigoPlanoSelecionadoModal();
        if (codigoPlanoSelecionado == null) {
            return;
        }
        for (SelectItem itemPlano : getListaSelectPlanos()) {
            if (codigoPlanoSelecionado != 0 && itemPlano.getValue().equals(codigoPlanoSelecionado)) {
                itemSelecionado = itemPlano;
                parcelaEmAbertoRel.getPlanosSelecionados().add(itemPlano);
                parcelaEmAbertoRel.setCodigoPlano(0);
                break;
            }
        }

        if (itemSelecionado != null) {
            getListaSelectPlanos().remove(itemSelecionado);
            Ordenacao.ordenarLista(getListaSelectPlanos(), "label");
        }
    }

    public void adicionarTodosPlanos() {
        if (getListaSelectPlanos() == null || getListaSelectPlanos().isEmpty()) {
            return;
        }

        List<SelectItem> planosParaAdicionar = new ArrayList<>();

        for (SelectItem item : getListaSelectPlanos()) {
            if (item.getLabel() != null && item.getLabel().length() >= 2 &&
                    item.getValue() != null && !item.getValue().equals(0)) {
                planosParaAdicionar.add(item);
            }
        }

        parcelaEmAbertoRel.getPlanosSelecionados().addAll(planosParaAdicionar);

        getListaSelectPlanos().removeAll(planosParaAdicionar);

        Ordenacao.ordenarLista(parcelaEmAbertoRel.getPlanosSelecionados(), "label");
    }

    public void removerPlanoDaListaTabela() {
        if (planoParaRemoverDaLista != null) {
            parcelaEmAbertoRel.getPlanosSelecionados().remove(planoParaRemoverDaLista);
        }
        getListaSelectPlanos().add(planoParaRemoverDaLista);
    }

    public void limparPlanos() throws Exception{
        getListaSelectPlanos().addAll(parcelaEmAbertoRel.getPlanosSelecionados());

        parcelaEmAbertoRel.setPlanosSelecionados(new ArrayList<SelectItem>());

        Ordenacao.ordenarLista(getListaSelectPlanos(), "label");
    }

    public List<SelectItem> getListaSelectPlanos() {
        return listaSelectPlanos;
    }

    public void setListaSelectPlanos(List<SelectItem> listaSelectPlanos) {
        this.listaSelectPlanos = listaSelectPlanos;
    }

    public List<SelectItem> getPlanosSelecionados() {
        return planosSelecionados;
    }

    public void setPlanosSelecionados(List<SelectItem> planosSelecionados) {
        this.planosSelecionados = planosSelecionados;
    }

    public String getAbrirRelatorio() {
        return abrirRelatorio;
    }

    public void setAbrirRelatorio(String abrirRelatorio) {
        this.abrirRelatorio = abrirRelatorio;
    }

    public SelectItem getPlanoParaRemoverDaLista() {
        return planoParaRemoverDaLista;
    }

    public void setPlanoParaRemoverDaLista(SelectItem planoParaRemoverDaLista) {
        this.planoParaRemoverDaLista = planoParaRemoverDaLista;
    }

    public Integer getCodigoPlanoSelecionadoModal() {
        return codigoPlanoSelecionadoModal;
    }

    public void setCodigoPlanoSelecionadoModal(Integer codigoPlanoSelecionadoModal) {
        this.codigoPlanoSelecionadoModal = codigoPlanoSelecionadoModal;
    }

    public boolean getRenderizarColunasPessoaResponsavel() {
        boolean renderizar = false;
        if(!UteisValidacao.emptyList(listaParcelas)){
            for(ParcelaSPCTO obj : listaParcelas) {
                if(obj.getPessoaResponsavel() != null && !UteisValidacao.emptyNumber(obj.getPessoaResponsavel().getCodigo())){
                    renderizar = true;
                    break;
                }
            }
        }

        return renderizar;
    }

    public boolean getRenderizarColunaErros() {
        boolean renderizar = false;
        if(!UteisValidacao.emptyList(listaParcelas)){
            for(ParcelaSPCTO obj : listaParcelas) {
                if(!UteisValidacao.emptyString(obj.getErro())){
                    renderizar = true;
                    break;
                }
            }
        }

        return renderizar;
    }

}

package relatorio.controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import org.jfree.data.category.DefaultCategoryDataset;
import relatorio.controle.basico.BIControle;
import relatorio.negocio.comuns.financeiro.ClientesICV;
import relatorio.negocio.comuns.financeiro.IndiceConversaoVendaVO;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

public class IndiceConversaoVendaSessaoRelControle extends BIControle {

    protected IndiceConversaoVendaVO indiceConversaoVendaVO;
    protected Date dataInicio;
    protected Date dataFim;
    private DefaultCategoryDataset dataSetBarra;
    private List<QuestionarioClienteVO> listaQuestionarios;
    private List<ClientesICV> listaClientes;
    private ClienteVO cliente;
    private boolean filtroSessaoPrimeiraCompra = true;
    private boolean filtroSessaoRetornoCompra = true;
    private boolean mostrarGrupos = false;

    public IndiceConversaoVendaSessaoRelControle() throws Exception {
        novo();
    }

    public void novo() throws Exception {
        try {
            setCliente(new ClienteVO());
            setDataFim(negocio.comuns.utilitarias.Calendario.hoje());
            setDataInicio(Uteis.obterPrimeiroDiaMes(negocio.comuns.utilitarias.Calendario.hoje()));
            setDataSetBarra(new DefaultCategoryDataset());
            setIndiceConversaoVendaVO(new IndiceConversaoVendaVO());
//            if (getEmpresaFiltroBI().getCodigo() != 0) {
//                consultarICVS();
//            }
            limparMensagens();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void limparMensagens() {
        setMensagemDetalhada("", "");
        setMensagem("");
        setMensagemID("");
    }

    private void validarEmpresa() throws Exception {
        if (getEmpresaFiltroBI().getCodigo() == 0 && !isPermiteConsultarTodasEmpresas()) {
            throw new Exception("O campo empresa deve ser informado");
        }
    }

    public List getListaSelectItemEmpresa() {
        return (List)JSFUtilities.getManagedBean("BIControle.listaSelectItemEmpresa");
    }

    @Override
    public boolean isConsultarPorTodosColaboradores(){
        return (Boolean)JSFUtilities.getManagedBean("BIControle.consultarPorTodosColaboradores");
    }

    public void consultarICVS() throws Exception {
        validarEmpresa();

        setDataFim(getDataBaseFiltro());
        setDataInicio(Uteis.obterPrimeiroDiaMes(getDataFim()));

        getIndiceConversaoVendaVO().limparCampos();

        marcarTodosFiltrosQuandoNaoMarcarNada();
        obterVinculosCliente();
        consultarIndiceConversaoVendaPorColaborador();
    }

    private void calcularInformacoes(String filtroColaboradores, List<ColaboradorVO> colaboradores) throws Exception {
        if (filtroSessaoPrimeiraCompra || filtroSessaoRetornoCompra) {
            getIndiceConversaoVendaVO().setQtdBVSessaoDia(getFacade().getQuestionarioCliente().consultaQuantidadeQuestionarioPorDataEmpresaPorColaborador(getDataFim(), getDataFim(), filtroColaboradores, getEmpresaFiltroBI().getCodigo(), getListaTiposBVs(), false));
            getIndiceConversaoVendaVO().setQtdBVSessaoMes(getFacade().getQuestionarioCliente().consultaQuantidadeQuestionarioPorDataEmpresaPorColaborador(getDataInicio(), getDataFim(), filtroColaboradores, getEmpresaFiltroBI().getCodigo(), getListaTiposBVs(), false));
        }

        List<TipoBVEnum> tiposBV = new ArrayList<TipoBVEnum>();
        if (filtroSessaoPrimeiraCompra) {
            tiposBV.add(TipoBVEnum.SS_PRIMEIRA_COMPRA);
            getIndiceConversaoVendaVO().setQtdBVSessaoPrimeiraCompraDia(getFacade().getVendaAvulsa().consultaQuantidadeSessaoICV(getDataFim(), getDataFim(), getEmpresaFiltroBI().getCodigo(), colaboradores, tiposBV));
            getIndiceConversaoVendaVO().setQtdBVSessaoPrimeiraCompraMes(getFacade().getVendaAvulsa().consultaQuantidadeSessaoICV(getDataInicio(), getDataFim(), getEmpresaFiltroBI().getCodigo(), colaboradores, tiposBV));
        }

        tiposBV = new ArrayList<TipoBVEnum>();
        if (filtroSessaoRetornoCompra) {
            tiposBV.add(TipoBVEnum.SS_RETORNO_COMPRA);
            getIndiceConversaoVendaVO().setQtdBVSessaoRetornoCompraDia(getFacade().getVendaAvulsa().consultaQuantidadeSessaoICV(getDataFim(), getDataFim(), getEmpresaFiltroBI().getCodigo(), colaboradores, tiposBV));
            getIndiceConversaoVendaVO().setQtdBVSessaoRetornoCompraMes(getFacade().getVendaAvulsa().consultaQuantidadeSessaoICV(getDataInicio(), getDataFim(), getEmpresaFiltroBI().getCodigo(), colaboradores, tiposBV));
        }
    }

    private void calcularTotalICVS() {
        if (!getIndiceConversaoVendaVO().getQtdBVSessaoMes().equals(0)) {
            getIndiceConversaoVendaVO().setTotalICVS(((getIndiceConversaoVendaVO().getQtdBVSessaoPrimeiraCompraMes() + getIndiceConversaoVendaVO().getQtdBVSessaoRetornoCompraMes()) / getIndiceConversaoVendaVO().getQtdBVSessaoMes().doubleValue()) * 100);
        }
    }

    public void marcarTodosFiltrosQuandoNaoMarcarNada() throws Exception {
        if (!filtroSessaoPrimeiraCompra && !filtroSessaoRetornoCompra) {
            filtroSessaoPrimeiraCompra = true;
            filtroSessaoRetornoCompra = true;
        }
    }

    /**
     * Adiciona filtro de colaborador para consultas de questionario.
     */
    public String getFiltroColaboradoresQuestionario() {
        // filtra a consulta pelos colaboradores
        int qtde = 0;
        StringBuilder sqlFiltro = new StringBuilder();
        for (ColaboradorVO co : getListaColaboradoresICV()) {
            if (co.getColaboradorEscolhido()) {
                if (qtde++ == 0) {
                    sqlFiltro.append(" AND QuestionarioCliente.consultor in (").append(co.getCodigo());
                } else {
                    sqlFiltro.append(", ").append(co.getCodigo());
                }
            }
        }
        if (qtde > 0) {
            sqlFiltro.append(")");
        }
        return sqlFiltro.toString();
    }

    public void consultarIndiceConversaoVendaPorColaborador() {
        try {
            ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("colaborador");
            if (obj == null) {
                obj = obterColaboradorParticipanteVOLogado();
                if (obj != null) {
                    obj.setColaboradorEscolhidoIndiceConversao(!obj.getColaboradorEscolhidoIndiceConversao());
                }
            }
            setDataFim(getDataBaseFiltro());
            setDataInicio(Uteis.obterPrimeiroDiaMes(getDataFim()));
            // caso nao exista nenhum colaborador marcado a consultar padrao sera por todos colaboradores.
            getIndiceConversaoVendaVO().limparCampos();
            Boolean consultarPorTodosColaboradores = true;
            String codigos = "";
            List<ColaboradorVO> colaboradoresEscolhidos = new ArrayList<ColaboradorVO>();
            for (ColaboradorVO co : getListaColaboradorFiltroBI(BIEnum.CONVERSAO_VENDAS_SS.name())) {
                if (co.getColaboradorEscolhidoIndiceConversao()) {
                    consultarPorTodosColaboradores = false;
                    codigos += co.getCodigo() + ",";
                    colaboradoresEscolhidos.add(co);
                }
            }

            String sql = "";
            if (!consultarPorTodosColaboradores) {
                sql = " and questionariocliente.consultor in (" + codigos.substring(0, codigos.length() - 1) + ")";
            } else {
                sql = getFiltroColaboradoresQuestionario();
                colaboradoresEscolhidos = getListaColaboradoresICV();
            }
            calcularInformacoes(sql, colaboradoresEscolhidos);

            calcularTotalICVS();

            montarDataBarra();

            limparMensagens();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getResumoFiltros() throws Exception {
        String resumoFiltros = "";
        if (isFiltroSessaoPrimeiraCompra()) {
            resumoFiltros = "SS-P";
        }
        if (isFiltroSessaoRetornoCompra()) {
            if (resumoFiltros.isEmpty()) {
                resumoFiltros += "SS-R";
            } else {
                resumoFiltros += ", SS-R";
            }
        }
        return resumoFiltros;
    }

    public List<TipoBVEnum> getListaTiposBVs() {
        List<TipoBVEnum> listaTiposBV = new ArrayList<TipoBVEnum>();
        if (filtroSessaoPrimeiraCompra) {
            listaTiposBV.add(TipoBVEnum.SS_PRIMEIRA_COMPRA);
        }
        if (filtroSessaoRetornoCompra) {
            listaTiposBV.add(TipoBVEnum.SS_RETORNO_COMPRA);
        }
        return listaTiposBV;
    }

    public void obterVinculosCliente() {
        try {
            if(getUsuarioLogado().getAdministrador() || getFacade().getAberturaMeta().verificarPermitirVisualizarTodasCarteiras()){
                getIndiceConversaoVendaVO().setListaColaboradoVOs(getFacade().getColaborador().consultarColaboradoresQuestionarios(
                        getDataInicio(), getDataFim(), getEmpresaFiltroBI().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS));
            }else{
                List<ColaboradorVO> colaborador = new ArrayList<ColaboradorVO>();
                colaborador.add((ColaboradorVO)getUsuarioLogado().getColaboradorVO().getClone(true));
                getIndiceConversaoVendaVO().setListaColaboradoVOs(colaborador);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaClientesBVMes() {
        try {
            setListaClientes(new ArrayList<ClientesICV>());
            setListaQuestionarios(new ArrayList<QuestionarioClienteVO>());
            if (indiceConversaoVendaVO.getQtdBVSessaoMes() != 0) {
                setListaQuestionarios(getFacade().getQuestionarioCliente().consultaQuestionarioPorDataEmpresaPorColaborador(
                        getDataInicio(),
                        getDataFim(),
                        getFiltroColaboradoresQuestionario(),
                        getListaTiposBVs(),
                        getEmpresaFiltroBI().getCodigo(),
                        false,
                        Uteis.NIVELMONTARDADOS_TODOS,
                        true,
                        true,null,null));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaClientesBVHoje() {
        try {
            setListaClientes(new ArrayList<ClientesICV>());
            setListaQuestionarios(new ArrayList<QuestionarioClienteVO>());
            if (indiceConversaoVendaVO.getQtdBVSessaoDia() != 0) {
                setListaQuestionarios(getFacade().getQuestionarioCliente().consultaQuestionarioPorDataEmpresaPorColaborador(
                        getDataFim(),
                        getDataFim(),
                        getFiltroColaboradoresQuestionario(),
                        getListaTiposBVs(),
                        getEmpresaFiltroBI().getCodigo(),
                        false,
                        Uteis.NIVELMONTARDADOS_TODOS,
                        true,
                        true,null, null));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaSessao(Date inicio, Date fim, List<TipoBVEnum> tiposBV) {
        try {
            setListaClientes(new ArrayList<ClientesICV>());
            setListaQuestionarios(new ArrayList<QuestionarioClienteVO>());
            setListaClientes(getFacade().getVendaAvulsa().consultaListaSessaoICV(inicio, fim, getEmpresaFiltroBI().getCodigo(), getListaColaboradoresICV(), tiposBV));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaSessaoMes() {
        List<TipoBVEnum> tiposBV = processarTiposBV();
        mostrarListaSessao(getDataInicio(), getDataFim(), tiposBV);
    }

    private List<TipoBVEnum> processarTiposBV() {
        String tipoSelecionado = context().getExternalContext().getRequestParameterMap().get("tipo");
        List<TipoBVEnum> tiposBV = new ArrayList<TipoBVEnum>();
        if (tipoSelecionado.equals("SS-R")) {
            tiposBV.add(TipoBVEnum.SS_RETORNO_COMPRA);
        }
        if (tipoSelecionado.equals("SS-P")) {
            tiposBV.add(TipoBVEnum.SS_PRIMEIRA_COMPRA);
        }
        return tiposBV;
    }

    public void mostrarListaSessaoHoje() {
        List<TipoBVEnum> tiposBV = processarTiposBV();
        mostrarListaSessao(getDataFim(), getDataFim(), tiposBV);
    }

    public void irParaTelaCliente() throws Exception {
        ClienteVO obj;
        if (getListaClientes().isEmpty()) {
            obj = ((QuestionarioClienteVO) context().getExternalContext().getRequestMap().get("resumoPessoa")).getCliente();
        } else {
            obj = ((ClientesICV) context().getExternalContext().getRequestMap().get("resumoPessoa")).getCliente();
        }
        irParaTelaCliente(obj);
    }

    public void montarDataBarra() throws Exception {
        // lista usada para mostrar a tabela do grafico de barras (nao contem nada)
        getDataSetBarra().addValue(getIndiceConversaoVendaVO().getQtdBVSessaoMes(), "BV-S", "");
        getDataSetBarra().addValue(getIndiceConversaoVendaVO().getQtdBVSessaoPrimeiraCompraMes(), "SS-P", "");
        getDataSetBarra().addValue(getIndiceConversaoVendaVO().getQtdBVSessaoRetornoCompraMes(), "SS-R", "");
    }

    public Integer getTamanhoListaColaborador() {
        Integer x = getIndiceConversaoVendaVO().getListaColaboradoVOs().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        indiceConversaoVendaVO = null;
        dataInicio = negocio.comuns.utilitarias.Calendario.hoje();
        dataFim = negocio.comuns.utilitarias.Calendario.hoje();
    }


    public void toggleMostrarGrupos() {
        setMostrarGrupos(!isMostrarGrupos());
    }

    public Boolean getMarcarUsuario() throws Exception {
        if (getUsuarioLogado().getAdministrador()) {
            return false;
        }
        ColaboradorVO logado = obterColaboradorParticipanteVOLogado();
        if (logado == null) {
            return false;
        }
        return logado.getColaboradorEscolhidoIndiceConversao();
    }
    private ColaboradorVO obterColaboradorParticipanteVOLogado() throws Exception {
        for (ColaboradorVO colaborador : getIndiceConversaoVendaVO().getListaColaboradoVOs()) {
            if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(colaborador.getCodigo())) {
                return colaborador;
            }
        }
        return null;
    }

    public boolean isMostrarGrupos() {
        return mostrarGrupos;
    }

    public void setMostrarGrupos(boolean mostrarGrupos) {
        this.mostrarGrupos = mostrarGrupos;
    }

    public Date getDataFim() {
        return dataFim;
    }
    public String getDataBase_Apresentar(){
        DateFormat dfmt = new SimpleDateFormat("MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataFim());
        return dfmt.format(cal.getTime());
    }


    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public DefaultCategoryDataset getDataSetBarra() {
        return dataSetBarra;
    }

    public void setDataSetBarra(DefaultCategoryDataset dataSetBarra) {
        this.dataSetBarra = dataSetBarra;
    }

    public IndiceConversaoVendaVO getIndiceConversaoVendaVO() {
        return indiceConversaoVendaVO;
    }

    public void setIndiceConversaoVendaVO(IndiceConversaoVendaVO indiceConversaoVendaVO) {
        this.indiceConversaoVendaVO = indiceConversaoVendaVO;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public List<QuestionarioClienteVO> getListaQuestionarios() {
        return listaQuestionarios;
    }

    public void setListaQuestionarios(List<QuestionarioClienteVO> lista) {
        this.listaQuestionarios = lista;
    }

    public List<ClientesICV> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClientesICV> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public boolean getMostraLista() {
        return listaClientes.isEmpty();
    }

    public int getQtdeLista() {
        return getMostraLista() ? listaQuestionarios.size() : listaClientes.size();
    }

    public boolean isMostrarCheckbox() throws Exception {
        return !getUsuarioLogado().getAdministrador() && obterColaboradorParticipanteVOLogado() != null;
    }

    public boolean isFiltroSessaoPrimeiraCompra() {
        return filtroSessaoPrimeiraCompra;
    }

    public void setFiltroSessaoPrimeiraCompra(boolean filtroSessaoPrimeiraCompra) {
        this.filtroSessaoPrimeiraCompra = filtroSessaoPrimeiraCompra;
    }

    public boolean isFiltroSessaoRetornoCompra() {
        return filtroSessaoRetornoCompra;
    }

    public void setFiltroSessaoRetornoCompra(boolean filtroSessaoRetornoCompra) {
        this.filtroSessaoRetornoCompra = filtroSessaoRetornoCompra;
    }

}

package relatorio.controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.financeiro.ClienteProdutoTO;
import relatorio.negocio.jdbc.financeiro.ProdutoRel;

import javax.faces.model.SelectItem;
import java.util.*;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created by glauco on 01/04/2014
 */
public class ProdutoRelControle extends SuperControleRelatorio {

    private String valorConsultaConsultor = "";
    private List<ColaboradorVO> listaConsultores = new ArrayList<ColaboradorVO>();
    private String valorConsultaResponsavelLancamento = "";
    private List<UsuarioVO> listaResponsavelLancamento = new ArrayList<UsuarioVO>();

    private List<SelectItem> listaTipoProduto = new ArrayList<SelectItem>();
    private List<SelectItem> listaProduto = new ArrayList<SelectItem>();
    private boolean mostrarListaProduto = false;

    private boolean mostrarRelatorio = false;
    private List<ClienteProdutoTO> clienteProdutoTOs = new ArrayList<ClienteProdutoTO>();

    private boolean semProdutos = false;

    private ProdutoRel produtoRel;

    private String[] situacoes;
    private int scrollerPage;
    private String colaboradoresPendencia = "";
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;

    public ProdutoRelControle() throws Exception {
        setProdutoRel(new ProdutoRel());
        montarListaSelectItemEmpresa();
        inicializarListaTipoProduto();
    }
    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void inicializarListaTipoProduto() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable desconto_tipoProdutos = (Hashtable) Dominios.getTipoProduto();
        Enumeration keys = desconto_tipoProdutos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) desconto_tipoProdutos.get(value);
            if (value.equals("SE") || value.equals("AT") || value.equals("DS") ) {
                objs.add(new SelectItem(value, label));
            }
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        setListaTipoProduto(objs);
    }

    public Object irParaTelaCliente() {
        ClienteProdutoTO obj = (ClienteProdutoTO) context().getExternalContext().getRequestMap().get("item");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(obj.getCodigoCliente());
                irParaTelaCliente(clienteVO);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public Object consultarResponsavel() throws Exception {
        setListaConsultores(getFacade().getColaborador().consultarPorNomeTipoVinculoPossivel(getValorConsultaConsultor(), TipoColaboradorEnum.CONSULTOR, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
        return true;
    }

    public Object selecionarResponsavel() {
        getProdutoRel().setConsultorSelecionado((ColaboradorVO) context().getExternalContext().getRequestMap().get("consultor"));
        return true;
    }

    public Object limparCampoOperador() {
        getProdutoRel().setConsultorSelecionado(new ColaboradorVO());
        return true;
    }

    public Object consultarResponsavelLancamento() throws Exception {
        setListaResponsavelLancamento(getFacade().getUsuario().consultarPorNomeUsuarioComLimite(valorConsultaResponsavelLancamento, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS));
        return true;
    }

    public Object selecionarResponsavelLancamento() {
        getProdutoRel().setResponsavelLancamento((UsuarioVO) context().getExternalContext().getRequestMap().get("usuario"));
        return true;
    }


    public Object limparCampoResponsavelLancamento() {
        getProdutoRel().setResponsavelLancamento(new UsuarioVO());
        return true;
    }

    public Object limparCampoDatasLanc() {
        getProdutoRel().setDataInicioLanc(null);
        getProdutoRel().setDataFinalLanc(null);
        return true;
    }

    public Object limparCampoDataCadastroCliente() {
        getProdutoRel().setDataInicioCadastroCliente(null);
        return true;
    }

    public Object limparCampoDatasVenc() {
        getProdutoRel().setDataInicioVenc(null);
        getProdutoRel().setDataFinalVenc(null);
        return true;
    }

    public Object acaoSelecionarTipoProduto() throws Exception {
        if (getProdutoRel() != null && !UteisValidacao.emptyString(getProdutoRel().getTipoProduto())) {
            preencherListaProdutos();
        } else {
            setMostrarListaProduto(false);
        }

        return true;
    }

    private void preencherListaProdutos() throws Exception {
        List<ProdutoVO> produtos = getFacade().getProduto().consultarProdutosPorTipoProdutoTipoVigencia(getProdutoRel().getTipoProduto(), "ID", false, Uteis.NIVELMONTARDADOS_MINIMOS);
        List<SelectItem> listaProdutos = new ArrayList<SelectItem>();
        for (ProdutoVO produtoVO : produtos) {
            if (listaProdutos.isEmpty()) {
                listaProdutos.add(new SelectItem("", ""));
            }
            listaProdutos.add(new SelectItem(produtoVO.getCodigo(), produtoVO.getDescricao()));
        }

        produtos = getFacade().getProduto().consultarProdutosPorTipoProdutoTipoVigencia(getProdutoRel().getTipoProduto(), "VV", false, Uteis.NIVELMONTARDADOS_MINIMOS);
        for (ProdutoVO produtoVO : produtos) {
            if (listaProdutos.isEmpty()) {
                listaProdutos.add(new SelectItem("", ""));
            }
            listaProdutos.add(new SelectItem(produtoVO.getCodigo(), produtoVO.getDescricao()));
        }
        setListaProduto(listaProdutos);
        setMostrarListaProduto(true);
    }

    public List<SelectItem> getSituacoesPossiveis() {
        List<SelectItem> situacoes = new ArrayList<SelectItem>();
        for (SituacaoClienteEnum situacao : SituacaoClienteEnum.values()) {
            if (situacao.getDescricao().equals(SituacaoClienteEnum.CONTRATOS_ATIVO.getDescricao())) continue;
            situacoes.add(new SelectItem(situacao.getCodigo(), situacao.getDescricao()));
        }
        return situacoes;
    }

    public Object imprimirRelatorio() throws Exception {
        setMostrarRelatorio(true);
        getProdutoRel().setSituacoes(new ArrayList<SituacaoClienteEnum>());
        for (String sit : getSituacoes()) {
            getProdutoRel().getSituacoes().add(SituacaoClienteEnum.getSituacaoCliente(sit));
        }
        setClienteProdutoTOs(getProdutoRel().consultarProdutos("", getFiltroEmpresa()));
        setSemProdutos(getProdutoRel().getSemProdutos());
        return true;
    }
    
    public Object imprimirRelatorioPendencia() throws Exception {
        setMostrarRelatorio(true);

        getProdutoRel().setSituacoes(new ArrayList<SituacaoClienteEnum>());
        for (String sit : getSituacoes()) {
            getProdutoRel().getSituacoes().add(SituacaoClienteEnum.getSituacaoCliente(sit));
        }
        setClienteProdutoTOs(getProdutoRel().consultarProdutos(getColaboradoresPendencia(), getFiltroEmpresa()));
        setSemProdutos(getProdutoRel().getSemProdutos());
        return true;
    }

    public String getValorTotalProduto_Apresentar() {
        Double valorTotal = 0.0;

        for (ClienteProdutoTO clienteProdutoTO : getClienteProdutoTOs()) {
            valorTotal += clienteProdutoTO.getValorProduto();
        }

        return Formatador.formatarValorMonetario(valorTotal);
    }

    public String getValorConsultaConsultor() {
        return valorConsultaConsultor;
    }

    public void setValorConsultaConsultor(String valorConsultaConsultor) {
        this.valorConsultaConsultor = valorConsultaConsultor;
    }

    public List<ColaboradorVO> getListaConsultores() {
        return listaConsultores;
    }

    public void setListaConsultores(List<ColaboradorVO> listaConsultores) {
        this.listaConsultores = listaConsultores;
    }

    public List<SelectItem> getListaTipoProduto() {
        return listaTipoProduto;
    }

    public void setListaTipoProduto(List<SelectItem> listaTipoProduto) {
        this.listaTipoProduto = listaTipoProduto;
    }

    public ProdutoRel getProdutoRel() {
        return produtoRel;
    }

    public void setProdutoRel(ProdutoRel produtoRel) {
        this.produtoRel = produtoRel;
    }

    public List<SelectItem> getListaProduto() {
        return listaProduto;
    }

    public void setListaProduto(List<SelectItem> listaProduto) {
        this.listaProduto = listaProduto;
    }

    public boolean isMostrarListaProduto() {
        return mostrarListaProduto;
    }

    public void setMostrarListaProduto(boolean mostrarListaProduto) {
        this.mostrarListaProduto = mostrarListaProduto;
    }

    public boolean isMostrarRelatorio() {
        return mostrarRelatorio;
    }

    public void setMostrarRelatorio(boolean mostrarRelatorio) {
        this.mostrarRelatorio = mostrarRelatorio;
    }

    public List<ClienteProdutoTO> getClienteProdutoTOs() {
        return clienteProdutoTOs;
    }

    public void setClienteProdutoTOs(List<ClienteProdutoTO> clienteProdutoTOs) {
        this.clienteProdutoTOs = clienteProdutoTOs;
    }

    public boolean isSemProdutos() {
        return semProdutos;
    }

    public void setSemProdutos(boolean semProdutos) {
        this.semProdutos = semProdutos;
    }

    public String[] getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(String[] situacoes) {
        this.situacoes = situacoes;
    }

    public String getValorConsultaResponsavelLancamento() {
        return valorConsultaResponsavelLancamento;
    }

    public void setValorConsultaResponsavelLancamento(String valorConsultaResponsavelLancamento) {
        this.valorConsultaResponsavelLancamento = valorConsultaResponsavelLancamento;
    }

    public List<UsuarioVO> getListaResponsavelLancamento() {
        return listaResponsavelLancamento;
    }

    public void setListaResponsavelLancamento(List<UsuarioVO> listaResponsavelLancamento) {
        this.listaResponsavelLancamento = listaResponsavelLancamento;
    }

    public int getScrollerPage() {
        return scrollerPage;
    }

    public void setScrollerPage(int scrollerPage) {
        this.scrollerPage = scrollerPage;
    }

    public String getColaboradoresPendencia() {
        return colaboradoresPendencia;
    }

    public void setColaboradoresPendencia(String colaboradoresPendencia) {
        this.colaboradoresPendencia = colaboradoresPendencia;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }
}

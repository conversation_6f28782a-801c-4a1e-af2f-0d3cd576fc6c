/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.financeiro;

import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.json.ConversaoVendaJSON;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoContratoEnum;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.financeiro.BIEventoDTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import negocio.comuns.utilitarias.UteisValidacao;
import org.jfree.data.general.DefaultPieDataset;
import org.jfree.data.category.DefaultCategoryDataset;
import org.json.JSONObject;
import relatorio.controle.basico.BIControle;
import relatorio.negocio.comuns.financeiro.ClientesICV;
import relatorio.negocio.comuns.financeiro.IndiceConversaoVendaVO;
import relatorio.negocio.comuns.financeiro.QuestionarioConsultorBVTO;

import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public class IndiceConversaoVendaRelControle extends BIControle {

    private DefaultPieDataset dataSetPizza;
    private DefaultCategoryDataset dataSetBarra;
    private Boolean mostarPizza;
    private Boolean mostarBarra;
    protected IndiceConversaoVendaVO indiceConversaoVendaVO;
    protected EmpresaVO empresaVO;
    protected List listaFalsa;
    protected Date dataInicio;
    protected Date dataFim;
    private List<QuestionarioClienteVO> listaQuestionarios;
    private List<ClientesICV> listaClientes;
    private ClienteVO cliente;
    private boolean filtroMatriculas = true;
    private boolean filtroRematriculas = true;
    private boolean filtroRetornos = true;
    private boolean filtroEspontaneo = true;
    private boolean filtroAgendado = true;
    private boolean filtroDesconsiderarGympass = true;
    private boolean incluirBolsistas = true;
    private boolean mostrarGrupos = false;
    private Boolean marcarUsuario;
    private String tituloResumoConversao = "";
    private List<QuestionarioConsultorBVTO> listaQuestionarioContsultoBV;
    private boolean apresentarBotaoDeAlteracaoConsulorBV = false;
    private ConversaoVendaJSON conversaoVendaJSON;
    private boolean filtroOrigemSistema = true;
    private boolean filtroOrigemSite = false;
    private List<EventoVO> listaEvento;
    private String itemExportacao = "";

    public IndiceConversaoVendaRelControle() throws Exception {
        novo();
    }
    public EmpresaVO getEmpresaFiltroBI(){
        return (EmpresaVO) JSFUtilities.getManagedBean("BIControle.empresaFiltro");
    }
    public List getListaSelectItemEmpresa() {
        return (List)JSFUtilities.getManagedBean("BIControle.listaSelectItemEmpresa");
    }
    public boolean isConsultarPorTodosColaboradores(){
        return (Boolean)JSFUtilities.getManagedBean("BIControle.consultarPorTodosColaboradores");
    }

    public void novo() throws Exception {
        try {
            setCliente(new ClienteVO());
            setDataFim(negocio.comuns.utilitarias.Calendario.hoje());
            setDataInicio(Uteis.obterPrimeiroDiaMes(negocio.comuns.utilitarias.Calendario.hoje()));
            setIndiceConversaoVendaVO(new IndiceConversaoVendaVO());
            setMostarPizza(false);
            setMostarBarra(false);
            limparMensagens();
            this.listaEvento = getFacade().getEvento().consultar(getDataBaseFiltro());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void limparMensagens() {
        setMensagemDetalhada("", "");
        setMensagem("");
        setMensagemID("");
    }

     public void consultarIndiceConversaoVenda() throws Exception {
        if (getEmpresaFiltroBI().getCodigo() == 0) {
            throw new Exception("O campo empresa deve ser informado");
        }
        setDataInicio(Uteis.obterPrimeiroDiaMes(getDataBaseFiltro()));
        setDataFim(getDataBaseFiltro());
        //limpar os campos
        getIndiceConversaoVendaVO().limparCampos();
        marcarTodasOpcoesFiltrosTipoQuandoNaoMarcarNada();
        String filtroColaborador = getFiltroColaboradoresQuestionario();
        if (!UteisValidacao.emptyString(filtroColaborador)) {
        	if (filtroMatriculas || filtroRematriculas || filtroRetornos) {
                atualizaQtdQuestionarios(filtroColaborador, getDataInicio(), getDataFim());
	        }
	        //deve consultar matricula caso o filtro for matricula
	        // ou então se estiver marcado pelo menos espontâneo ou agendado e não estiver marcado matrícula e rematrícula
	        // ou então se estiver marcado pelo menos espontâneo ou agendado e estiver marcado matrícula
	        if ((filtroMatriculas || filtroRetornos)
	                || ((!filtroMatriculas && !filtroRematriculas) && (filtroEspontaneo || filtroAgendado))
	                || ((filtroMatriculas && !filtroRematriculas) && (filtroEspontaneo || filtroAgendado))) {
                atualizaMapaMatriculas(filtroColaborador, getDataInicio(), getDataFim());
	        }
	        //deve consultar rematricula caso o filtro for rematricula
	        // ou então se estiver marcado pelo menos espontâneo ou agendado e não estiver marcado matrícula e rematrícula
	        // ou então se estiver marcado pelo menos espontâneo ou agendado e estiver marcado rematrícula
	        if ((filtroRematriculas || filtroRetornos)
	                || ((!filtroRematriculas && !filtroMatriculas) && (filtroEspontaneo || filtroAgendado))
	                || ((filtroRematriculas && !filtroMatriculas) && (filtroEspontaneo || filtroAgendado))) {
	            atualizaMapaRematriculas(filtroColaborador, getDataInicio(), getDataFim());
	        }
                indiceConversaoVendaVO.processarIndicadoresMapasMes();
            if (!getIndiceConversaoVendaVO().getQtdQuestionarioMes().equals(0)) {
                getIndiceConversaoVendaVO().setTotalICV(((getIndiceConversaoVendaVO().getQtdMatriculaMes() + getIndiceConversaoVendaVO().getQtdRematriculaMes()) / getIndiceConversaoVendaVO().getQtdQuestionarioMes().doubleValue()) * 100);
	            setMostarPizza(true);
	        }
        } else {
            consultarIndiceConversaoVendaPorColaborador();
        }
    }

    private void atualizaQtdQuestionarios(String filtro, Date dataInicio, Date dataFim)  throws  Exception {
        getIndiceConversaoVendaVO().setMapaQuestionariosDia(getMapaQuestionarioDia(filtro, dataFim, dataFim));
        getIndiceConversaoVendaVO().setMapaQuestionarios(getMapaQuestionarioDia(filtro, dataInicio, dataFim));
    }

    private void atualizaMapaMatriculas(String sql, Date dataInicio, Date dataFim) throws Exception {
        getIndiceConversaoVendaVO().setMapaGeralMatriculasDia(mapaClientes(getEmpresaFiltroBI().getCodigo(), "MA", true,getFiltroColaboradoresContrato()));
        getIndiceConversaoVendaVO().setMapaGeralMatriculas(mapaClientes(getEmpresaFiltroBI().getCodigo(), "MA", false,getFiltroColaboradoresContrato()));
    }

    private void atualizaMapaRematriculas(String sql, Date dataInicio, Date dataFim) throws  Exception {
        getIndiceConversaoVendaVO().setMapaGeralRematriculasDia(mapaClientes(getEmpresaFiltroBI().getCodigo(), "RE", true,getFiltroColaboradoresContrato()));
        getIndiceConversaoVendaVO().setMapaGeralRematriculas(mapaClientes(getEmpresaFiltroBI().getCodigo(), "RE", false,getFiltroColaboradoresContrato()));
    }

    private int getQtdMatriculaGenerico(String situacao, String sql, Date dataInicio, Date dataFim, boolean noDia) throws  Exception {
        return getFacade().getCliente().
                consultaQuantidadeSituacaoContratoPorDataLancamentoEmpresaColaborador(dataInicio, dataFim,
                        getEmpresaFiltroBI().getCodigo(), sql, situacao, getListaTiposContrato(),getListaTiposBVs(), false, noDia);
    }

     private Map<Integer,Integer> getMapaQuestionarioDia(String filtroColaborador, Date dataInicio,  Date dataFim) throws Exception {
        return getFacade().getQuestionarioCliente().
                consultaMapaQuestionarioPorDataEmpresaPorColaborador(dataInicio,
                        dataFim,
                        filtroColaborador,
                        getEmpresaFiltroBI().getCodigo(),
                        getListaTiposBVs(),
                        false,
                        incluirBolsistas,
                        filtroDesconsiderarGympass);
    }

    /**
     * Marca todas as opções de filtro por tipo quando não estiver nenhuma marcada.
     * @throws Exception
     */
    public void marcarTodasOpcoesFiltrosTipoQuandoNaoMarcarNada() throws Exception {
        if (!filtroMatriculas && !filtroRematriculas && !filtroRetornos && !filtroEspontaneo && !filtroAgendado) {
            filtroMatriculas = true;
            filtroRematriculas = true;
            filtroRetornos = true;
            filtroEspontaneo = true;
            filtroAgendado = true;
        }
    }

    /**
     * Adiciona filtro de colaborador para consultas de questionario.
     */
    public String getFiltroColaboradoresQuestionario() {
        // filtra a consulta pelos colaboradores
        int qtde = 0;
        StringBuilder sqlFiltro = new StringBuilder();
        for (ColaboradorVO co : getListaColaboradorFiltroBI(BIEnum.CONVERSAO_VENDAS.name())) {
            if (co.getColaboradorEscolhido()){
                if (qtde++ == 0) {
                    sqlFiltro.append(" AND QuestionarioCliente.consultor in (").append(co.getCodigo());
                } else {
                    sqlFiltro.append(", ").append(co.getCodigo());
                }
            }
        }
        if (qtde > 0) {
            sqlFiltro.append(")");
        }

        return sqlFiltro.toString();
    }

    /**
     * Adiciona filtro de colaborador para consultas de contrato.
     */
    public String getFiltroColaboradoresContrato() {
        // filtra a consulta pelos colaboradores
        int qtde = 0;
        StringBuilder sqlFiltro = new StringBuilder();
        for (ColaboradorVO co : getListaColaboradorFiltroBI(BIEnum.CONVERSAO_VENDAS.name())) {
            if (co.getColaboradorEscolhido()){
                if (qtde++ == 0) {
                    sqlFiltro.append(" AND contrato.consultor in (").append(co.getCodigo());
                } else {
                    sqlFiltro.append(", ").append(co.getCodigo());
                }
            }
        }
        if (qtde > 0) {
            sqlFiltro.append(")");
        }

        return sqlFiltro.toString();
    }

    List<Integer> getFiltroColaboradores(){
        ArrayList<Integer> codigos = new ArrayList<>();
        try{
            ColaboradorVO colaborador = (ColaboradorVO) context().getExternalContext().getRequestMap().get("colaborador");
            if (colaborador == null) {
                colaborador = obterColaboradorParticipanteVOLogado();
                if (colaborador != null) {
                    colaborador.setColaboradorEscolhidoIndiceConversao(!colaborador.getColaboradorEscolhidoIndiceConversao());
                }
            }
            for (ColaboradorVO co : getListaColaboradorFiltroBI(BIEnum.CONVERSAO_VENDAS.name())) {
                if (co.getColaboradorEscolhido()) {
                    codigos.add(co.getCodigo());
                }
            }
        }catch (Exception e){
            Uteis.logar("Erro ao obter filtro de colaboradores");
            e.printStackTrace();
        }

        return codigos;
    }

    private FiltroDTO getFiltroDTO(){
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.CONVERSAO_VENDAS.name());
        JSONObject filtros = new JSONObject();
        ArrayList<Integer> empresas = new ArrayList<>();
        if (getEmpresaFiltroBI().getCodigo() > 0) {
            empresas.add(this.getEmpresaFiltroBI().getCodigo());
        }
        filtros.put("empresas", empresas);
//        filtros.put("data", "2021-06-22"); // Debug
        filtros.put("data", Uteis.getData(getDataBaseFiltro(), "bd"));
        filtros.put("tipoBV", getListaTiposBVs().stream().map(TipoBVEnum::getCodigo).collect(Collectors.toList()));
        filtros.put("considerarPlanoBolsa", incluirBolsistas);
        filtros.put("desconsiderarGympass", filtroDesconsiderarGympass);
        filtros.put("tipoContrato", getListaTiposContrato().stream().map(TipoContratoEnum::getCodigo).collect(Collectors.toList()));
        filtros.put("matriculas", filtroMatriculas);
        filtros.put("rematriculas", filtroRematriculas);
        filtros.put("retorno", filtroRetornos);
        filtros.put("retorno", filtroRetornos);
        filtros.put("colaboradores", getFiltroColaboradores());
        filtros.put("listaOrigemSistema", getListaOrigemSistemaSelecionado());
        filtros.put("listaEvento", getListaEventoSelecionado());

        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    private String getListaEventoSelecionado(){
        StringBuilder codigos = new StringBuilder();
        for (EventoVO eventoVO : this.listaEvento){
            if (!eventoVO.isMarcado()){
                continue;
            }
            if (codigos.length() > 0){
                codigos.append(",");
            }
            codigos.append(eventoVO.getCodigo());
        }
        return codigos.toString();
    }

    public String getListaOrigemSistemaSelecionado() {
        String codigos = "";
        if (this.filtroOrigemSistema){
            codigos = OrigemSistemaEnum.ZW.getCodigo().toString() + "," + OrigemSistemaEnum.NOVA_TELA_NEGOCIACAO.getCodigo().toString();
        }
        if (this.filtroOrigemSite){
            if (!codigos.equals("")){
                codigos = codigos + ",";
            }
            codigos = codigos + OrigemSistemaEnum.VENDAS_ONLINE_2.getCodigo().toString();
        }
        return codigos;
    }


    public void atualizar() {
        atualizar(true);
    }

    public void atualizar(boolean gerarNovamente) {
        try {
            limparMsg();
            FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(), BIEnum.CONVERSAO_VENDAS, getFiltroDTO(), gerarNovamente);
            conversaoVendaJSON = JSONMapper.getObject(new JSONObject(filtroDTO.getJsonDados()), ConversaoVendaJSON.class);
            indiceConversaoVendaVO = new IndiceConversaoVendaVO();
            indiceConversaoVendaVO.setQtdQuestionarioDia(conversaoVendaJSON.getBoletinVisitaDia());
            indiceConversaoVendaVO.setQtdQuestionarioMes(conversaoVendaJSON.getBoletinVisitaMes());

            indiceConversaoVendaVO.setMapaMatriculasDia(conversaoVendaJSON.getMapaMatriculasDias());
            indiceConversaoVendaVO.setQtdMatriculaDia(conversaoVendaJSON.getMatriculasDia());

            indiceConversaoVendaVO.setMapaMatriculas(conversaoVendaJSON.getMapaMatriculasMes());
            indiceConversaoVendaVO.setQtdMatriculaMes(conversaoVendaJSON.getMatriculasMes());

            indiceConversaoVendaVO.setQtdRematriculaDia(conversaoVendaJSON.getReMatriculasDia());
            indiceConversaoVendaVO.setMapaRematriculasDia(conversaoVendaJSON.getMapaRematriculasDias());

            indiceConversaoVendaVO.setQtdRematriculaMes(conversaoVendaJSON.getReMatriculasMes());
            indiceConversaoVendaVO.setMapaRematriculas(conversaoVendaJSON.getMapaRematriculasMes());

            indiceConversaoVendaVO.setTotalICV(conversaoVendaJSON.getIndiceConversaoVendaMes());

            setDataInicio(Uteis.obterPrimeiroDiaMes(getDataBaseFiltro()));
            setDataFim(getDataBaseFiltro());
            this.listaEvento = getFacade().getEvento().consultar(getDataBaseFiltro());
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    public void consultarIndiceConversaoVendaPorColaboradorTela() {
        gravarHistoricoAcessoBI(BIEnum.CONVERSAO_VENDAS);
        consultarIndiceConversaoVendaPorColaborador();
    }

    public void consultarIndiceConversaoVendaPorColaborador() {
        try {
            ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("colaborador");
            if (obj == null) {
                obj = obterColaboradorParticipanteVOLogado();
                if (obj != null) {
                    obj.setColaboradorEscolhidoIndiceConversao(!obj.getColaboradorEscolhidoIndiceConversao());
                }
            }
            // caso nao exista nenhum colaborador marcado a consultar padrao sera por todos colaboradores.
            Boolean consultarPorTodosColaboradores = true;
            String sql = "";
            String paramContratoResponsavel;
            String codigos = "";
            setDataInicio(Uteis.obterPrimeiroDiaMes(getDataBaseFiltro()));
            setDataFim(getDataBaseFiltro());
            getIndiceConversaoVendaVO().limparCampos();
            for (ColaboradorVO co : getListaColaboradorFiltroBI(BIEnum.CONVERSAO_VENDAS.name())) {
                if (co.getColaboradorEscolhido()) {
                    consultarPorTodosColaboradores = false;
                    codigos += co.getCodigo() + ",";
                }
            }
            marcarTodasOpcoesFiltrosTipoQuandoNaoMarcarNada();
            if (consultarPorTodosColaboradores) {
                atualizaQtdQuestionarios("", getDataInicio(), getDataFim());
                atualizaMapaMatriculas("",getDataInicio(), getDataFim());
                atualizaMapaRematriculas("", getDataInicio(), getDataFim());
            } else {
                paramContratoResponsavel = " and contrato.consultor in (" + codigos.substring(0, codigos.length() - 1) + ")";
                sql = " and questionariocliente.consultor in (" + codigos.substring(0, codigos.length() - 1) + ")";
                atualizaQtdQuestionarios(sql,getDataInicio(), getDataFim());
                atualizaMapaMatriculas(paramContratoResponsavel,getDataInicio(), getDataFim());
                atualizaMapaRematriculas(paramContratoResponsavel,getDataInicio(), getDataFim());
            }
            indiceConversaoVendaVO.processarIndicadoresMapasMes();

            if (!getIndiceConversaoVendaVO().getQtdQuestionarioMes().equals(0)) {
                getIndiceConversaoVendaVO().setTotalICV(
                        (
                                (getIndiceConversaoVendaVO().getQtdMatriculaMes() + getIndiceConversaoVendaVO().getQtdRematriculaMes()) /
                                getIndiceConversaoVendaVO().getQtdQuestionarioMes().doubleValue()
                        ) * 100
                );
            }
//            setMostarPizza(true);
//            montarDataPizza();
//            montarDataBarra();
            limparMensagens();
            montarListaQuestionarioConsultorBV();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getResumoFiltros() throws Exception {
        String resumoFiltros = "";
        if (filtroMatriculas) {
            resumoFiltros = "MA";
        }
        if (filtroRematriculas) {
            if (resumoFiltros.isEmpty()) {
                resumoFiltros += "RE";
            } else {
                resumoFiltros += ", RE";
            }
        }
        if (filtroRetornos) {
            if (resumoFiltros.isEmpty()) {
                resumoFiltros += "RT";
            } else {
                resumoFiltros += ", RT";
            }
        }
        if (filtroEspontaneo) {
            if (resumoFiltros.isEmpty()) {
                resumoFiltros += "E";
            } else {
                resumoFiltros += ", E";
            }
        }
        if (filtroAgendado) {
            if (resumoFiltros.isEmpty()) {
                resumoFiltros += "A";
            } else {
                resumoFiltros += ", A";
            }
        }
        return resumoFiltros;
    }

    public List<TipoBVEnum> getListaTiposBVs() {
        List<TipoBVEnum> listaTiposBV = new ArrayList<TipoBVEnum>();
        if (filtroMatriculas) {
            listaTiposBV.add(TipoBVEnum.MA);
        }
        if (filtroRematriculas) {
            listaTiposBV.add(TipoBVEnum.RE);
        }
        if (filtroRetornos) {
            listaTiposBV.add(TipoBVEnum.RT);
        }
        return listaTiposBV;
    }

    public List<TipoContratoEnum> getListaTiposContrato() {
        List<TipoContratoEnum> listaTipoContratos = new ArrayList<TipoContratoEnum>();
        if (filtroAgendado) {
            listaTipoContratos.add(TipoContratoEnum.AGENDADO);
        }
        if (filtroEspontaneo) {
            listaTipoContratos.add(TipoContratoEnum.ESPONTANEO);
        }
        return listaTipoContratos;
    }

    public void mostrarListaClientesBVMes() {
        try {
            setListaClientes(new ArrayList<ClientesICV>());
            setListaQuestionarios(new ArrayList<QuestionarioClienteVO>());
            if (indiceConversaoVendaVO.getQtdQuestionarioMes() != 0) {
                setListaQuestionarios(getFacade().getQuestionarioCliente().consultaQuestionarioPorDataEmpresaPorColaborador(getDataInicio(),
                        getDataFim(), getFiltroColaboradoresQuestionario(), getListaTiposBVs(), getEmpresaFiltroBI().getCodigo(),
                        false, Uteis.NIVELMONTARDADOS_TODOS, incluirBolsistas, filtroDesconsiderarGympass,getListaOrigemSistemaSelecionado(),getListaEventoSelecionado()));
            }
            setTituloResumoConversao("Conversão de Vendas - BV");
            setItemExportacao(ItemExportacaoEnum.BI_ICV_BV.getId());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaClientesBVHoje() {
        try {
            setListaClientes(new ArrayList<ClientesICV>());
            setListaQuestionarios(new ArrayList<QuestionarioClienteVO>());
            if (indiceConversaoVendaVO.getQtdQuestionarioMes() != 0) {
                setListaQuestionarios(getFacade().getQuestionarioCliente().consultaQuestionarioPorDataEmpresaPorColaborador(getDataFim(),
                        getDataFim(), getFiltroColaboradoresQuestionario(), getListaTiposBVs(), getEmpresaFiltroBI().getCodigo(),
                        false, Uteis.NIVELMONTARDADOS_TODOS, incluirBolsistas, filtroDesconsiderarGympass,getListaOrigemSistemaSelecionado(),getListaEventoSelecionado()));
            }
            setTituloResumoConversao("Conversão de Vendas - BV");
            setItemExportacao(ItemExportacaoEnum.BI_ICV_BV.getId());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaClientesMatriculaMes() {
        try {
            setListaClientes(new ArrayList<ClientesICV>());
            setListaQuestionarios(new ArrayList<QuestionarioClienteVO>());
            if (indiceConversaoVendaVO.getQtdMatriculaMes() != 0) {
                setListaClientes(lerListaClientes(indiceConversaoVendaVO.getMapaMatriculas(), "MA"));
            }
            setTituloResumoConversao("Conversão de Vendas - Matrículas");
            setItemExportacao(ItemExportacaoEnum.BI_ICV_MATRICULAS.getId());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private List<ClientesICV> lerListaClientes(Map<Integer,Integer> mapaClienteContrato, String situacaoContrato) throws Exception {
        return getFacade().getCliente().consultarClientesListaICV(mapaClienteContrato, "RE".equals(situacaoContrato));
    }
    
    private Map<Integer,Integer> mapaClientes(int empresa, String situacao, boolean noDia, String colaboradores) throws Exception {
        return getFacade().getCliente().consultarClientesMapaICV(getDataInicio(),
                getDataFim(), empresa, situacao, colaboradores,
                getListaTiposContrato(), getListaTiposBVs(), noDia, incluirBolsistas);
    }

    public void mostrarListaClientesMatriculaHoje() {
        try {
            setListaClientes(new ArrayList<ClientesICV>());
            setListaQuestionarios(new ArrayList<QuestionarioClienteVO>());
            if (indiceConversaoVendaVO.getQtdMatriculaMes() != 0) {
                setListaClientes(lerListaClientes(indiceConversaoVendaVO.getMapaMatriculasDia(), "MA"));
            }
            setTituloResumoConversao("Conversão de Vendas - Matrículas");
            setItemExportacao(ItemExportacaoEnum.BI_ICV_MATRICULAS.getId());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaClientesRematriculaMes() {
        try {
            setListaClientes(new ArrayList<ClientesICV>());
            setListaQuestionarios(new ArrayList<QuestionarioClienteVO>());
            if (indiceConversaoVendaVO.getQtdRematriculaMes() != 0) {
                setListaClientes(lerListaClientes(indiceConversaoVendaVO.getMapaRematriculas(), "RE"));
            }
            setTituloResumoConversao("Conversão de Vendas - Rematrículas");
            setItemExportacao(ItemExportacaoEnum.BI_ICV_REMATRICULAS.getId());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mostrarListaClientesRematriculaHoje() {
        try {
            setListaClientes(new ArrayList<ClientesICV>());
            setListaQuestionarios(new ArrayList<QuestionarioClienteVO>());
            if (indiceConversaoVendaVO.getQtdRematriculaMes() != 0) {
                setListaClientes(lerListaClientes(indiceConversaoVendaVO.getMapaRematriculasDia(), "RE"));
            }
            setTituloResumoConversao("Conversão de Vendas - Rematrículas");
            setItemExportacao(ItemExportacaoEnum.BI_ICV_REMATRICULAS.getId());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void irParaTelaCliente() throws Exception {
        ClienteVO obj;
        if (getListaClientes().isEmpty()) {
            obj = ((QuestionarioClienteVO) context().getExternalContext().getRequestMap().get("resumoPessoa")).getCliente();
        } else {
            obj = ((ClientesICV) context().getExternalContext().getRequestMap().get("resumoPessoa")).getCliente();
        }
        irParaTelaCliente(obj);
    }

    public void montarDataPizza() throws Exception {
        getDataSetPizza().setValue("BV", getIndiceConversaoVendaVO().getQtdQuestionarioMes());
        getDataSetPizza().setValue("MA", getIndiceConversaoVendaVO().getQtdMatriculaMes());
        getDataSetPizza().setValue("RE", getIndiceConversaoVendaVO().getQtdRematriculaMes());
    }

    public void montarDataBarra() throws Exception {
        // lista usada para mostrar a tabela do grafico de barras (nao contem nada)
        setListaFalsa(new ArrayList());
        getListaFalsa().add("");
        getDataSetBarra().addValue(getIndiceConversaoVendaVO().getQtdQuestionarioMes(), "BV", "");
        getDataSetBarra().addValue(getIndiceConversaoVendaVO().getQtdMatriculaMes(), "MA", "");
        getDataSetBarra().addValue(getIndiceConversaoVendaVO().getQtdRematriculaMes(), "RE", "");
    }

    public Integer getTamanhoListaColaborador() {
        Integer x = getIndiceConversaoVendaVO().getListaColaboradoVOs().size();
        if (x == 0) {
            return 1;
        }
        return x;
    }

    public void irParaTelaClienteConsulorBV(){
        try {
            QuestionarioConsultorBVTO obj = (QuestionarioConsultorBVTO) context().getExternalContext().getRequestMap().get("lista");
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getQuestionarioClienteVO().getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    
    public void montarListaQuestionarioConsultorBV() throws Exception{
        List<LogVO> listaLog = getFacade().getLog().
                consultarPorNomeEntidadeOperacaoPorDataAlteracao("QuestionarioCliente", "Questionario Cliente", "ALTERAÇÃO DE CONSULTOR BV", Uteis.NIVELMONTARDADOS_TODOS, 
                false, "Consultor:", dataInicio, "00:00:00", dataFim, "23:59:59");
        listaQuestionarioContsultoBV = new ArrayList<QuestionarioConsultorBVTO>();

        for (LogVO logVO : listaLog) {
            QuestionarioClienteVO questionarioClienteVo = getFacade().getQuestionarioCliente().
                    consultarPorChavePrimaria(Integer.parseInt(logVO.getChavePrimaria()), Uteis.NIVELMONTARDADOS_TODOS);
            if(questionarioClienteVo.getConsultor().getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo())) {
                QuestionarioConsultorBVTO questionarioConsultorBVTO = new QuestionarioConsultorBVTO();
                questionarioConsultorBVTO.setQuestionarioClienteVO(questionarioClienteVo);
                questionarioConsultorBVTO.setLog(logVO);
                listaQuestionarioContsultoBV.add(questionarioConsultorBVTO);
            }
        }

        if (listaQuestionarioContsultoBV.isEmpty()) {
            setApresentarBotaoDeAlteracaoConsulorBV(false);
        }else{
            setApresentarBotaoDeAlteracaoConsulorBV(true);
        }
    }
    
    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        dataSetPizza = null;
        mostarPizza = false;
        indiceConversaoVendaVO = null;
        empresaVO = null;
        dataInicio = negocio.comuns.utilitarias.Calendario.hoje();
        dataFim = negocio.comuns.utilitarias.Calendario.hoje();
    }

    public void toggleMostrarGrupos() {
        setMostrarGrupos(!isMostrarGrupos());
    }

    public boolean isMostrarGrupos() {
        return mostrarGrupos;
    }

    public void setMostrarGrupos(boolean mostrarGrupos) {
        this.mostrarGrupos = mostrarGrupos;
    }

    public Date getDataFim() {
        return dataFim;
    }
    public String getDataBase_Apresentar(){
        DateFormat dfmt = new SimpleDateFormat("MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataFim());
        return dfmt.format(cal.getTime());
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public DefaultPieDataset getDataSetPizza() {
        return dataSetPizza;
    }

    public void setDataSetPizza(DefaultPieDataset dataSetPizza) {
        this.dataSetPizza = dataSetPizza;
    }

    public DefaultCategoryDataset getDataSetBarra() {
        return dataSetBarra;
    }

    public void setDataSetBarra(DefaultCategoryDataset dataSetBarra) {
        this.dataSetBarra = dataSetBarra;
    }

    public IndiceConversaoVendaVO getIndiceConversaoVendaVO() {
        return indiceConversaoVendaVO;
    }

    public void setIndiceConversaoVendaVO(IndiceConversaoVendaVO indiceConversaoVendaVO) {
        this.indiceConversaoVendaVO = indiceConversaoVendaVO;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public Boolean getMostarPizza() {
        return mostarPizza;
    }

    public void setMostarPizza(Boolean mostarPizza) {
        this.mostarPizza = mostarPizza;
    }

    public Boolean getMostarBarra() {
        return mostarBarra;
    }

    public void setMostarBarra(Boolean mostarBarra) {
        this.mostarBarra = mostarBarra;
    }

    public List getListaFalsa() {
        return listaFalsa;
    }

    public void setListaFalsa(List listaFalsa) {
        this.listaFalsa = listaFalsa;
    }

    public String inicializarIndiceConversaoVendaControle() {
        return "";
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setListaQuestionarios(List<QuestionarioClienteVO> lista) {
        this.listaQuestionarios = lista;
    }

    public List<QuestionarioClienteVO> getListaQuestionarios() {
        return listaQuestionarios;
    }

    public List<ClientesICV> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClientesICV> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public boolean getMostraLista() {
        return listaClientes.isEmpty();
    }

    public int getQtdeLista() {
        return (getMostraLista() ? listaQuestionarios.size() : listaClientes.size());
    }

    public boolean isFiltroMatriculas() {
        return filtroMatriculas;
    }

    public void setFiltroMatriculas(boolean filtroMatriculas) {
        this.filtroMatriculas = filtroMatriculas;
    }

    public boolean isFiltroRematriculas() {
        return filtroRematriculas;
    }

    public void setFiltroRematriculas(boolean filtroRematriculas) {
        this.filtroRematriculas = filtroRematriculas;
    }

    public boolean isFiltroRetornos() {
        return filtroRetornos;
    }

    public void setFiltroRetornos(boolean filtroRetornos) {
        this.filtroRetornos = filtroRetornos;
    }

    public boolean isFiltroEspontaneo() {
        return filtroEspontaneo;
    }

    public void setFiltroEspontaneo(boolean filtroEspontaneo) {
        this.filtroEspontaneo = filtroEspontaneo;
    }

    public boolean isFiltroAgendado() {
        return filtroAgendado;
    }

    public boolean isIncluirBolsistas(){
        return incluirBolsistas;
    }

    public void setIncluirBolsistas(boolean incluirBolsistas) {
        this.incluirBolsistas = incluirBolsistas;
    }

    public void setFiltroAgendado(boolean filtroAgendado) {
        this.filtroAgendado = filtroAgendado;
    }

    public boolean isFiltroDesconsiderarGympass() {
        return filtroDesconsiderarGympass;
    }

    public void setFiltroDesconsiderarGympass(boolean filtroDesconsiderarGympass) {
        this.filtroDesconsiderarGympass = filtroDesconsiderarGympass;
    }

    public Boolean getMarcarUsuario() throws Exception {
        if (getUsuarioLogado().getAdministrador()) {
            return false;
        }
        ColaboradorVO logado = obterColaboradorParticipanteVOLogado();
        if (logado == null) {
            return false;
        }
        return logado.getColaboradorEscolhidoIndiceConversao();
    }

    public void setMarcarUsuario(Boolean marcarUsuario) {
        this.marcarUsuario = marcarUsuario;
    }

    private ColaboradorVO obterColaboradorParticipanteVOLogado() throws Exception {
        for (ColaboradorVO colaborador : getIndiceConversaoVendaVO().getListaColaboradoVOs()) {
            if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(colaborador.getCodigo())) {
                return colaborador;
            }
        }
        return null;
    }

    public boolean isMostrarCheckbox() throws Exception {
        return  !getUsuarioLogado().getAdministrador() && obterColaboradorParticipanteVOLogado() != null;
    }

    public String getTituloResumoConversao() {
        return tituloResumoConversao;
    }

    public void setTituloResumoConversao(String tituloResumoConversao) {
        this.tituloResumoConversao = tituloResumoConversao;
    }

    public List<QuestionarioConsultorBVTO> getListaQuestionarioContsultoBV() {
        return listaQuestionarioContsultoBV;
    }

    public void setListaQuestionarioContsultoBV(List<QuestionarioConsultorBVTO> listaQuestionarioContsultoBV) {
        this.listaQuestionarioContsultoBV = listaQuestionarioContsultoBV;
    }

    public boolean isApresentarBotaoDeAlteracaoConsulorBV() {
        return apresentarBotaoDeAlteracaoConsulorBV;
    }

    public void setApresentarBotaoDeAlteracaoConsulorBV(boolean apresentarBotaoDeAlteracaoConsulorBV) {
        this.apresentarBotaoDeAlteracaoConsulorBV = apresentarBotaoDeAlteracaoConsulorBV;
    }


    public boolean isFiltroOrigemSistema() {
        return filtroOrigemSistema;
    }

    public void setFiltroOrigemSistema(boolean filtroOrigemSistema) {
        this.filtroOrigemSistema = filtroOrigemSistema;
    }

    public boolean isFiltroOrigemSite() {
        return filtroOrigemSite;
    }

    public void setFiltroOrigemSite(boolean filtroOrigemSite) {
        this.filtroOrigemSite = filtroOrigemSite;
    }

    public List<EventoVO> getListaEvento() {
        return listaEvento;
    }

    public void setListaEvento(List<EventoVO> listaEvento) {
        this.listaEvento = listaEvento;
    }

    public String getItemExportacao() {
        return itemExportacao;
    }

    public void setItemExportacao(String itemExportacao) {
        this.itemExportacao = itemExportacao;
    }
}

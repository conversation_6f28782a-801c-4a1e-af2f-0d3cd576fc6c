package relatorio.controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.jdbc.financeiro.ParcelaConsolidadaTO;

import javax.faces.event.ActionEvent;
import java.util.Date;
import java.util.List;

@Deprecated
public class ParcelaConsolidadoRelControle extends SuperControleRelatorio {

    private Date mesVencimento;
    private String abrirRelatorio;
    private Integer filtroEmpresa;
    private boolean isPossuiIdExterno;

    public ParcelaConsolidadoRelControle() throws Exception {
        novo();
        isPossuiIdExterno = getFacade().getMovParcela().existeColunasCampoExterno();
    }

    public String getAtributos() {
        return "nome_empresa=nome_empresa,movparcela_codigo=movparcela_codigo,cliente_matricula=cliente_matricula," +
                "pessoa_nome=pessoa_nome,email=email,parcela_descricao=parcela_descricao," +
                "parcela_dataregistro=parcela_dataregistro,parcela_datavencimento=parcela_datavencimento," +
                "parcela_situacao=parcela_situacao,regime_recorrencia=regime_recorrencia," +
                "parcela_contrato=parcela_contrato,parcela_valorparcela=parcela_valorparcela," +
                "total=total,datapagamento=datapagamento,tipo=tipo" +
                (isPossuiIdExterno ? ",id_externo_parcela=id_externo_parcela,id_externo_contrato=id_externo_contrato" : "");
    }

    public void exportar(ActionEvent evt) throws Exception {
        limparMsg();
        try {
            if (getMesVencimento() == null) {
                throw new ConsistirException("O mês de pesquisa deve ser informado");
            }

            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getManagedBean("ExportadorListaControle");
            List<ParcelaConsolidadaTO> parcelasConsolidadas = getFacade().getMovParcela().consultarConsolidadoParcelas(getMesVencimento(), isPossuiIdExterno);
            if (!UteisValidacao.emptyList(parcelasConsolidadas)) {
                exportadorListaControle.exportar(evt, parcelasConsolidadas, "", null);
            }
            prepararAbrirRelatorio();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void prepararAbrirRelatorio() {
        ExportadorListaControle exp = (ExportadorListaControle) getControlador("ExportadorListaControle");
        if (UteisValidacao.emptyString(exp.getFileName())) {
            montarErro("Não foi encontrado resultados para o relatório!");
            abrirRelatorio = getMensagemNotificar();
        } else {
            abrirRelatorio = "location.href=\"../UpdateServlet?op=downloadfile&file=" + exp.getFileName() + "&mimetype=application/vnd.ms-excel\"";
            abrirRelatorio += exp.getMsgAlert();
            limparMsg();
        }
    }

    public void consultarParcelas() {
        try {
            consultarParcelas_();
        } catch (Exception e) {
            montarErro(e.getMessage());
            e.printStackTrace();
        }
    }

    private void consultarParcelas_() throws Exception {
        limparMsg();
    }


    public void novo() throws Exception {
        setMesVencimento(Calendario.hoje());
    }

    public String getAbrirRelatorio() {
        return abrirRelatorio;
    }

    public void setAbrirRelatorio(String abrirRelatorio) {
        this.abrirRelatorio = abrirRelatorio;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public Date getMesVencimento() {
        if (mesVencimento == null) {
            mesVencimento = Calendario.hoje();
        }
        return mesVencimento;
    }

    public void setMesVencimento(Date mesVencimento) {
        this.mesVencimento = mesVencimento;
    }
}

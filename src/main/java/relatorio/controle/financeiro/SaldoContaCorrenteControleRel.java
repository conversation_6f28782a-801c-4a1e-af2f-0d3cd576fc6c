package relatorio.controle.financeiro;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import javax.faces.model.SelectItem;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.jdbc.financeiro.SaldoContaCorrenteRel;

public class SaldoContaCorrenteControleRel extends SuperControleRelatorio {

    private SaldoContaCorrenteRel saldoContaCorrenteRel;
    private List<SelectItem> listaDeEmpresa;
    private String campoConsultarCliente;
    private String valorConsultarCliente;
    private List listaConsultarCliente;
    private Boolean temEmpresa;
    private List listaSelectItemTipoPositivoNegativo;
    private boolean permiteConsultarInfoTodasEmpresas = false;

    public SaldoContaCorrenteControleRel() throws Exception {
        inicializarDados();
    }

    public void inicializarDados() throws Exception {
        permiteConsultarInfoTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
        setSaldoContaCorrenteRel(new SaldoContaCorrenteRel());
        setListaDeEmpresa(new ArrayList<SelectItem>());
        setCampoConsultarCliente("");
        setValorConsultarCliente("");
        setListaConsultarCliente(new ArrayList());
        montarListaSelectItemEmpresa();
    }

    public void imprimirPDF() {
        try {
            saldoContaCorrenteRel.validarDados(permiteConsultarInfoTodasEmpresas);
            saldoContaCorrenteRel.setDescricaoFiltros("");
            String titulo = " Relatório Saldo de Conta Corrente.";
            String xml = saldoContaCorrenteRel.emitirRelatorio();
            String design =  saldoContaCorrenteRel.getFiltroEmpresa() == 0 ?
                    SaldoContaCorrenteRel.getDesignIReportRelatorioTodasEmpresas() :
                    SaldoContaCorrenteRel.getDesignIReportRelatorio();

            String empresaNome;

            if(getSaldoContaCorrenteRel().getFiltroEmpresa() == null){
                empresaNome = getEmpresaLogado().getNome();
            }else if(getSaldoContaCorrenteRel().getFiltroEmpresa() == 0){
                empresaNome = LABEL_TODAS_EMPRESAS;
            }else{
                if(getSelectItemEmpresaSelecionada() == null){
                    empresaNome = getEmpresaLogado().getNome();
                }else{
                    empresaNome = getSelectItemEmpresaSelecionada().getLabel();
                }
            }

            apresentarRelatorio(
                    saldoContaCorrenteRel.getIdEntidade(),
                    xml,
                    titulo,
                    empresaNome,
                    "",
                    "PDF", "/" + saldoContaCorrenteRel.getIdEntidade() + "/registros",
                    design,
                    getUsuarioLogado().getNome(),
                    saldoContaCorrenteRel.getDescricaoFiltros());
            setMensagemID("msg_relatorio_ok");
        } catch (ConsistirException e) {
            setMensagemDetalhada("Nenhum Registro Encontrado!");
        } catch (Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public SelectItem getSelectItemEmpresaSelecionada(){
        for (SelectItem itemEmpresa : getListaDeEmpresa()) {
            if(itemEmpresa.getValue().equals(getSaldoContaCorrenteRel().getFiltroEmpresa())){
                return itemEmpresa;
            }
        }

        return null;
    }

    public void montarListaSelectItemEmpresa() {
        try {
            getSaldoContaCorrenteRel().setEmpresaVO(getEmpresaLogado());
            if ( (getSaldoContaCorrenteRel().getFiltroEmpresa() != null) ||
                permiteConsultarInfoTodasEmpresas) {
                setTemEmpresa(true);
                montarListaSelectItemEmpresa("");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List objs = new ArrayList();

        if(permiteConsultarInfoTodasEmpresas){
            objs.add(new SelectItem(new Integer(0), "TODAS"));
        }else{
            objs.add(new SelectItem(new Integer(0), ""));
        }
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaDeEmpresa(objs);
    }

    public void consultarCliente() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarCliente().equals("nome")) {
                objs = getFacade().getCliente().consultarPorNomePessoa(getValorConsultarCliente(), getSaldoContaCorrenteRel().getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS, null);
            }
            if (getCampoConsultarCliente().equals("matricula")) {
                objs = getFacade().getCliente().consultarPorMatricula(getValorConsultarCliente(), getSaldoContaCorrenteRel().getFiltroEmpresa(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            setListaConsultarCliente(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarCliente(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarCliente() {
    	try {
            ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
            getSaldoContaCorrenteRel().setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));	
        } catch (Exception e) {
            getSaldoContaCorrenteRel().setClienteVO(new ClienteVO());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void limparCampoCliente() {
        getSaldoContaCorrenteRel().setClienteVO(new ClienteVO());
    }

    public List getTipoConsultaComboCliente() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("nome", "Nome"));
        objs.add(new SelectItem("matricula", "Matrícula"));
        return objs;
    }

    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        List lista = new Empresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public String getCampoConsultarCliente() {
        return campoConsultarCliente;
    }

    public void setCampoConsultarCliente(String campoConsultarCliente) {
        this.campoConsultarCliente = campoConsultarCliente;
    }

    public List getListaConsultarCliente() {
        return listaConsultarCliente;
    }

    public void setListaConsultarCliente(List listaConsultarCliente) {
        this.listaConsultarCliente = listaConsultarCliente;
    }

    public List<SelectItem> getListaDeEmpresa() {
        return listaDeEmpresa;
    }

    public void setListaDeEmpresa(List<SelectItem> listaDeEmpresa) {
        this.listaDeEmpresa = listaDeEmpresa;
    }

    public String getValorConsultarCliente() {
        return valorConsultarCliente;
    }

    public void setValorConsultarCliente(String valorConsultarCliente) {
        this.valorConsultarCliente = valorConsultarCliente;
    }

    public Boolean getTemEmpresa() {
        return temEmpresa;
    }

    public void setTemEmpresa(Boolean temEmpresa) {
        this.temEmpresa = temEmpresa;
    }

    public SaldoContaCorrenteRel getSaldoContaCorrenteRel() {
        return saldoContaCorrenteRel;
    }

    public void setSaldoContaCorrenteRel(SaldoContaCorrenteRel saldoContaCorrenteRel) {
        this.saldoContaCorrenteRel = saldoContaCorrenteRel;
    }

    public List getListaSelectItemTipoPositivoNegativo() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(-10000, getEmpresaLogado().getMoeda() +" -10.000,00"));
        objs.add(new SelectItem(-9000, getEmpresaLogado().getMoeda() +" -9.000,00"));
        objs.add(new SelectItem(-8000, getEmpresaLogado().getMoeda() +" -8.000,00"));
        objs.add(new SelectItem(-7000, getEmpresaLogado().getMoeda() +" -7.000,00"));
        objs.add(new SelectItem(-6000, getEmpresaLogado().getMoeda() +" -6.000,00"));
        objs.add(new SelectItem(-5000, getEmpresaLogado().getMoeda() +" -5.000,00"));
        objs.add(new SelectItem(-4000, getEmpresaLogado().getMoeda() +" -4.000,00"));
        objs.add(new SelectItem(-3000, getEmpresaLogado().getMoeda() +" -3.000,00"));
        objs.add(new SelectItem(-2000, getEmpresaLogado().getMoeda() +" -2.000,00"));
        objs.add(new SelectItem(-1000, getEmpresaLogado().getMoeda() +" -1.000,00"));
        objs.add(new SelectItem(-900, getEmpresaLogado().getMoeda() +" -900,00"));
        objs.add(new SelectItem(-800, getEmpresaLogado().getMoeda() +" -800,00"));
        objs.add(new SelectItem(-700, getEmpresaLogado().getMoeda() +" -700,00"));
        objs.add(new SelectItem(-600, getEmpresaLogado().getMoeda() +" -600,00"));
        objs.add(new SelectItem(-500, getEmpresaLogado().getMoeda() +" -500,00"));
        objs.add(new SelectItem(-400, getEmpresaLogado().getMoeda() +" -400,00"));
        objs.add(new SelectItem(-300, getEmpresaLogado().getMoeda() +" -300,00"));
        objs.add(new SelectItem(-200, getEmpresaLogado().getMoeda() +" -200,00"));
        objs.add(new SelectItem(-100, getEmpresaLogado().getMoeda() +" -100,00"));
        objs.add(new SelectItem(-50, getEmpresaLogado().getMoeda() +" -50,00"));
        //objs.add(new SelectItem(0, "getEmpresaLogado().getMoeda() +" 0,00"));
        objs.add(new SelectItem(0, "Todos"));
        objs.add(new SelectItem(50, getEmpresaLogado().getMoeda() +"50,00"));
        objs.add(new SelectItem(100, getEmpresaLogado().getMoeda() +" 100,00"));
        objs.add(new SelectItem(200, getEmpresaLogado().getMoeda() +" 200,00"));
        objs.add(new SelectItem(300, getEmpresaLogado().getMoeda() +" 300,00"));
        objs.add(new SelectItem(400, getEmpresaLogado().getMoeda() +" 400,00"));
        objs.add(new SelectItem(500, getEmpresaLogado().getMoeda() +" 500,00"));
        objs.add(new SelectItem(600, getEmpresaLogado().getMoeda() +" 600,00"));
        objs.add(new SelectItem(700, getEmpresaLogado().getMoeda() +" 700,00"));
        objs.add(new SelectItem(800, getEmpresaLogado().getMoeda() +" 800,00"));
        objs.add(new SelectItem(900, getEmpresaLogado().getMoeda() +" 900,00"));
        objs.add(new SelectItem(1000, getEmpresaLogado().getMoeda() +" 1.000,00"));
        objs.add(new SelectItem(2000, getEmpresaLogado().getMoeda() +" 2.000,00"));
        objs.add(new SelectItem(3000, getEmpresaLogado().getMoeda() +" 3.000,00"));
        objs.add(new SelectItem(4000, getEmpresaLogado().getMoeda() +" 4.000,00"));
        objs.add(new SelectItem(5000, getEmpresaLogado().getMoeda() +" 5.000,00"));
        objs.add(new SelectItem(6000, getEmpresaLogado().getMoeda() +" 6.000,00"));
        objs.add(new SelectItem(7000, getEmpresaLogado().getMoeda() +" 7.000,00"));
        objs.add(new SelectItem(8000, getEmpresaLogado().getMoeda() +" 8.000,00"));
        objs.add(new SelectItem(9000, getEmpresaLogado().getMoeda() +" 9.000,00"));
        objs.add(new SelectItem(10000, getEmpresaLogado().getMoeda() +" 10.000,00"));
        setListaSelectItemTipoPositivoNegativo(objs);
        return listaSelectItemTipoPositivoNegativo;
    }

    public void setListaSelectItemTipoPositivoNegativo(List listaSelectItemTipoPositivoNegativo) {
        this.listaSelectItemTipoPositivoNegativo = listaSelectItemTipoPositivoNegativo;
    }

    public void novo() throws Exception {
        inicializarDados();
        return;
    }
}

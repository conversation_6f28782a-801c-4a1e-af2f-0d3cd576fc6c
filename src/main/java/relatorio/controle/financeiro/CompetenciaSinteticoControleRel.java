package relatorio.controle.financeiro;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoMesVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoRelTO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoResumoPessoaVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoTipoProdutoVO;
import relatorio.negocio.jdbc.financeiro.CaixaPorOperadorRel;
import relatorio.negocio.jdbc.financeiro.CompetenciaSinteticoRel;

/**
 *
 * <AUTHOR>
 */
public class CompetenciaSinteticoControleRel extends SuperControleRelatorio {

    private CompetenciaSinteticoRel competenciaSinteticoRel;
    private String campoConsultarOperador;
    private String valorConsultarOperador;
    private String dataInicioPrm;
    private String dataTerminoPrm;
    private boolean matrRenovRemat;
    private boolean produtoEstoque;
    private boolean mesReferenciaPlano;
    private boolean servico;
    private boolean trancamento;
    private boolean retornoTrancamento;
    private boolean aulaAvulsa;
    private boolean diaria;
    private boolean freePass;
    private boolean alterarHorario;
    private boolean manutencaoModalidade;
    private boolean taxaPersonal;
    private boolean sessao;
    private boolean pgtoSaldoDevedor;
    private boolean quitacaoCancelamento;
    private boolean acertoCCAluno;
    private List<SelectItem> listaDeEmpresa;
    private boolean temEmpresa;
    private CompetenciaSinteticoProdutoMesVO competenciaSinteticoProdutoMesVO;
    private List<CompetenciaSinteticoTipoProdutoVO> listaTipoProdutoVO;
    private List<PeriodoMensal> periodos;
    private String filtros;
    private CompetenciaSinteticoTipoProdutoVO resumo;
    private String agrupamento;
    private List<CompetenciaSinteticoRelTO> listaExportavel = new ArrayList<CompetenciaSinteticoRelTO>();
    private List<ContratoModalidadeVO> listaResumoModalidade = new ArrayList<ContratoModalidadeVO>();
    private int contratoSelecionado;

    public CompetenciaSinteticoControleRel() throws Exception {
        inicializarDados();
    }

    public List<CompetenciaSinteticoResumoPessoaVO> getListaResumo() {
        return getCompetenciaSinteticoProdutoMesVO().getListaResumoPessoa();
    }

    public void inicializarDados() throws Exception {
        setCompetenciaSinteticoRel(new CompetenciaSinteticoRel());
        setDataInicioPrm("");
        setDataTerminoPrm("");
        setCampoConsultarOperador("");
        setValorConsultarOperador("");
        setMesReferenciaPlano(true);
        setListaTipoProdutoVO(new ArrayList<CompetenciaSinteticoTipoProdutoVO>());
        setTemEmpresa(false);
        setListaDeEmpresa(new ArrayList<SelectItem>());
        montarListaSelectItemEmpresa();
        setResumo(new CompetenciaSinteticoTipoProdutoVO());
        setCompetenciaSinteticoProdutoMesVO(new CompetenciaSinteticoProdutoMesVO());
    }

    public Map<String, Boolean> getMapaTipoProduto() {
        Map<String, Boolean> mapaTipoProduto = new HashMap<String, Boolean>();
        mapaTipoProduto.put("", true);
        mapaTipoProduto.put("MARERN", matrRenovRemat);
        mapaTipoProduto.put("PE", produtoEstoque);
        mapaTipoProduto.put("PM", mesReferenciaPlano);
        mapaTipoProduto.put("SE", servico);
        mapaTipoProduto.put("TR", trancamento);
        mapaTipoProduto.put("AA", aulaAvulsa);
        mapaTipoProduto.put("DI", diaria);
        mapaTipoProduto.put("FR", freePass);
        mapaTipoProduto.put("AH", alterarHorario);
        mapaTipoProduto.put("MM", manutencaoModalidade);
        mapaTipoProduto.put("TP", taxaPersonal);
        mapaTipoProduto.put("SS", sessao);
        mapaTipoProduto.put("MC", pgtoSaldoDevedor);
        mapaTipoProduto.put("QU", quitacaoCancelamento);
        mapaTipoProduto.put("AC", acertoCCAluno);
        return mapaTipoProduto;
    }

    public Boolean getApresentarEmpresa() {
        try {
            if (getEmpresaLogado().getCodigo().equals(0)) {
                return true;
            }
        } catch (Exception ex) {
        }
        return false;
    }

    public void montarListaSelectItemEmpresa() {
        try {
            getCompetenciaSinteticoRel().setEmpresa(getEmpresaLogado());
            if (getCompetenciaSinteticoRel().getEmpresa() != null && getCompetenciaSinteticoRel().getEmpresa().getNome().equals("")) {
                setTemEmpresa(true);
                montarListaSelectItemEmpresa("");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        for (Object aResultadoConsulta : resultadoConsulta) {
            EmpresaVO obj = (EmpresaVO) aResultadoConsulta;
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaDeEmpresa(objs);
    }

    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return new Empresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public List getTipoAgrupamento() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("nomeDuracao", "Nome e Duração"));
        objs.add(new SelectItem("duracao", "Duração"));
        objs.add(new SelectItem("nome", "Nome"));
        return objs;
    }

    public boolean getTodos() {
        return (matrRenovRemat || produtoEstoque || mesReferenciaPlano
                || servico || trancamento || aulaAvulsa || retornoTrancamento
                || diaria || freePass || alterarHorario || manutencaoModalidade
                || taxaPersonal || sessao || pgtoSaldoDevedor || quitacaoCancelamento
                || acertoCCAluno);
    }

    public String imprimir() {
        try {
            ContadorTempo.limparCronometro();
            ContadorTempo.iniciarContagem();
            setListaExportavel(new ArrayList<CompetenciaSinteticoRelTO>());
            getCompetenciaSinteticoRel().validarDados();
            MovProduto movProduto = new MovProduto();// cria uma estancia de movProduto - onde fica o sql !
            periodos = Uteis.getPeriodosMensaisEntreDatas(competenciaSinteticoRel.getDataInicio(), competenciaSinteticoRel.getDataTermino());// pega data inicial e final para o relatorios
            listaTipoProdutoVO = new ArrayList<CompetenciaSinteticoTipoProdutoVO>();// cria uma lista da propria classe
            CompetenciaSinteticoProdutoVO totalGeral = new CompetenciaSinteticoProdutoVO(); // total geral de toda a consulta
            totalGeral.setDescricao("TOTAL GERAL");
            //TABLE - TIPOPRODUTO
            for (String tipoProduto : getMapaTipoProduto().keySet()) {
                if (tipoProduto.trim().isEmpty()) {
                    if (getTodos()) {
                        continue;
                    }
                } else if (!getMapaTipoProduto().get(tipoProduto)) { // trazer somente os tipos produtos informados
                    continue;
                }
                CompetenciaSinteticoTipoProdutoVO tipoProdutoVO = new CompetenciaSinteticoTipoProdutoVO(); // estancia do competencia, onde tem se lista de mês e lista de produtos
                tipoProdutoVO.setTipoProduto(getTipoProduto_Apresentar(tipoProduto)); // substitui as abreviacoes  do tipo de produto para o nome

                List<CompetenciaSinteticoProdutoVO> listaProduto = movProduto.consultarProdutosMovimentadoParaCompetenciaPeriodo(competenciaSinteticoRel.getDataInicio(),
                        competenciaSinteticoRel.getDataTermino(), preencherListaTipoProduto(tipoProduto),
                        getCompetenciaSinteticoRel().getEmpresa().getCodigo(), agrupamento, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null);
                // traz uma lista de CompetenciaSinteticoProdutoVO, filtrando por data e tipo do Produto: (matricula, produtoEstoque, mês de referencia, renovacao, devolucao e etc.. )
                //SUBTABLE - PRODUTO
                for (CompetenciaSinteticoProdutoVO fsProduto : listaProduto) { //laço que percorre uma lista de competenciaSinteticoProdutos              
                    //COLUNA - MES x PRODUTO
                    for (PeriodoMensal p : periodos) { // percorre um laço entre o periodo lançado na tela como filtro
                        String mesReferencia = Uteis.getMesReferenciaData(p.getDataInicio());
                        CompetenciaSinteticoProdutoMesVO produtoMesVO = movProduto.consultarCompetenciaSinteticoResumoPessoa(
                                mesReferencia, preencherListaTipoProduto(fsProduto.getProduto().getTipoProduto()),
                                getCompetenciaSinteticoRel().getEmpresa().getCodigo(), fsProduto.getDuracao(),
                                fsProduto.getPlano(), fsProduto.getProduto().getCodigo(), fsProduto.getNomePlano(),
                                fsProduto.getProduto().getTipoProduto().equals("PM") ? null : fsProduto.getDescricao());
                        fsProduto.getListaProdutoXMes().add(produtoMesVO);

                        for (CompetenciaSinteticoResumoPessoaVO listaPessoas : produtoMesVO.getListaResumoPessoa()) {
                            listaPessoas.setMesLancamentoMovProduto(p.getDataInicio());
                        }
                    }
                    if (!fsProduto.getListaProdutoXMes().isEmpty()) {
                        tipoProdutoVO.getListaProduto().add(fsProduto);
                    }
                }
                if (!tipoProdutoVO.getListaProduto().isEmpty()) {
                    CompetenciaSinteticoProdutoVO totalizador = new CompetenciaSinteticoProdutoVO();
                    totalizador.setDescricao("TOTALIZADOR");
                    int tamanho = 0;
                    for (CompetenciaSinteticoProdutoVO fsProduto : listaProduto) {
                        tamanho = fsProduto.getListaProdutoXMes().size();
                        break;
                    }
                    for (int i = 0; i < tamanho; i++) {
                        CompetenciaSinteticoProdutoMesVO subTotalGeral;
                        CompetenciaSinteticoProdutoMesVO totalizadorMes = new CompetenciaSinteticoProdutoMesVO();
                        if (totalGeral.getListaProdutoXMes().size() <= i) {
                            subTotalGeral = new CompetenciaSinteticoProdutoMesVO();
                            totalGeral.getListaProdutoXMes().add(subTotalGeral);
                        } else {
                            subTotalGeral = totalGeral.getListaProdutoXMes().get(i);
                        }
                        for (CompetenciaSinteticoProdutoVO fsProduto : listaProduto) {
                            CompetenciaSinteticoProdutoMesVO produtoMes = fsProduto.getListaProdutoXMes().get(i);
                            totalizadorMes.setQtd(totalizadorMes.getQtd().intValue() + produtoMes.getQtd().intValue());
                            totalizadorMes.setValor(totalizadorMes.getValor() + produtoMes.getValor());
                            totalizadorMes.getListaResumoPessoa().addAll(produtoMes.getListaResumoPessoa());
                            subTotalGeral.setQtd(subTotalGeral.getQtd().intValue() + produtoMes.getQtd().intValue());
                            subTotalGeral.setValor(subTotalGeral.getValor() + produtoMes.getValor());
                            subTotalGeral.getListaResumoPessoa().addAll(produtoMes.getListaResumoPessoa());
                        }
                        totalizador.getListaProdutoXMes().add(totalizadorMes);
                    }
                    tipoProdutoVO.getListaProduto().add(totalizador);
                    tipoProdutoVO.setApresentarResultado(true);
                    listaTipoProdutoVO.add(tipoProdutoVO);
                }
            }
            setResumo(new CompetenciaSinteticoTipoProdutoVO());
            getResumo().setTipoProduto("RESUMO GERAL");
            getResumo().getListaProduto().add(totalGeral);
            if (!getResumo().getListaProduto().isEmpty()
                    && !getResumo().getListaProduto().get(0).getListaProdutoXMes().isEmpty()) {
                getResumo().setApresentarResultado(true);
            }
            //listaTipoProdutoVO.add(resumo);
            setFiltros(getDescricaoFiltros());
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
            notificarRecursoEmpresa(RecursoSistema.RELATORIO_COMPETENCIA, ContadorTempo.encerraContagem(), Calendario.diferencaEmMeses(competenciaSinteticoRel.getDataInicio(), competenciaSinteticoRel.getDataTermino()) + 1);
            return "relatorio";
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
            return "consulta";
        }
    }

    public void exportar(ActionEvent actionEvent) throws Exception {
        montarListaExportavel();
        ((ExportadorListaControle)getControlador(ExportadorListaControle.class)).exportar(actionEvent);
    }

    public void montarListaExportavel() throws Exception {
        listaExportavel = new ArrayList<CompetenciaSinteticoRelTO>();

        ClienteInterfaceFacade cliente = getFacade().getCliente();
        ContratoInterfaceFacade contrato = getFacade().getContrato();
        EmpresaInterfaceFacade empresaDAO = getFacade().getEmpresa();
        Map<Integer, EmpresaVO> mapEmpresas = empresaDAO.obterMapaEmpresas(Uteis.NIVELMONTARDADOS_MINIMOS);

        for (CompetenciaSinteticoTipoProdutoVO cstpVO : listaTipoProdutoVO) {
            for (CompetenciaSinteticoProdutoVO cspVO : cstpVO.getListaProduto()) {
                if (!cspVO.getDescricao().equals("TOTALIZADOR")) {
                    for (CompetenciaSinteticoProdutoMesVO cspmVO : cspVO.getListaProdutoXMes()) {
                        for (CompetenciaSinteticoResumoPessoaVO csrpVO : cspmVO.getListaResumoPessoa()) {

                            if (csrpVO.getCliente().getPessoa().getCodigo() != 0) {
                                csrpVO.setCliente(cliente.consultarPorCodigoPessoa(csrpVO.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                                csrpVO.getCliente().setEmpresa(mapEmpresas.get(csrpVO.getCliente().getEmpresa().getCodigo()));
                                if (csrpVO.getCliente().getCodigo() == null || csrpVO.getCliente().getCodigo() == 0) {
                                    csrpVO.setColaboradorVO(getFacade().getColaborador().consultarPorCodigoPessoa(csrpVO.getCliente().getPessoa().getCodigo(), 0, Uteis.NIVELMONTARDADOS_MINIMOS));
                                    csrpVO.getColaboradorVO().setEmpresa(mapEmpresas.get(csrpVO.getColaboradorVO().getEmpresa().getCodigo()));
                                }
                            }
                            if (csrpVO.getContrato().getCodigo() != 0) {
                                csrpVO.setContrato(contrato.consultarPorChavePrimaria(csrpVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                            }


                            CompetenciaSinteticoRelTO csrTO = new CompetenciaSinteticoRelTO();
                            csrTO.setCodCliente(csrpVO.getCliente().getCodigo());
                            csrTO.setNomeCliente(csrpVO.getCliente().getPessoa().getNome());
//                            csrTO.setCPF(csrpVO.getCliente().getPessoa().getCfp());
                            csrTO.setCodContrato(csrpVO.getContrato_Apresentar());
                            csrTO.setValorCompetencia(csrpVO.getValor());
                            csrTO.setMesLancamentoProduto(csrpVO.getMesLancamentoMovProduto());
                            csrTO.setCodPlano(cspVO.getPlano());
                            csrTO.setNomePlano(cspVO.getNomePlano());
                            csrTO.setDuracaoPlano(cspVO.getDuracao());
                            if (!UteisValidacao.emptyNumber(csrpVO.getCliente().getCodigo())) {
                                csrTO.setNomeEmpresa(csrpVO.getCliente().getEmpresa().getNome());
                            } else {
                                csrTO.setNomeEmpresa(csrpVO.getColaboradorVO().getEmpresa().getNome());
                            }
                            listaExportavel.add(csrTO);
                        }
                    }
                }
            }
        }
    }

    public List<String> preencherListaTipoProduto(String tipoProduto) {
        //analisa o tipo de produto para consulta
        //por padrão para consultar por Matricula, Rematricula, Renovacao sera adicionado os três para a pesquisa
        List<String> listaTipoProdutos = new ArrayList<String>();
        if (tipoProduto.equalsIgnoreCase("MARERN")) {
            listaTipoProdutos.add("MA");
            listaTipoProdutos.add("RE");
            listaTipoProdutos.add("RN");
        } else {
            if (!tipoProduto.isEmpty()) {
                listaTipoProdutos.add(tipoProduto);
            }
        }
        return listaTipoProdutos;
    }

    private String getEmpresaLista() throws Exception {
        getCompetenciaSinteticoRel().setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getCompetenciaSinteticoRel().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        return getCompetenciaSinteticoRel().getEmpresa().getNome();
    }

    private String getDescricaoFiltros() throws Exception {
        String filtro = "";
        if (getCompetenciaSinteticoRel().getEmpresa().getCodigo() != 0
                && getCompetenciaSinteticoRel().getEmpresa().getCodigo() != null && estahDeslogado()) {
            filtro += "<b>Empresa: </b> " + getEmpresaLista() + "</br>";
            filtro += " <b>Período de: </b>" + Uteis.getData(competenciaSinteticoRel.getDataInicio()) + " <b>Até: </b>" + Uteis.getData(competenciaSinteticoRel.getDataTermino()) + "</br>";
        } else if (getEmpresaLogado().getCodigo().intValue() != 0) {
            filtro += " <b>Período de: </b>" + Uteis.getData(competenciaSinteticoRel.getDataInicio()) + " <b>Até: </b>" + Uteis.getData(competenciaSinteticoRel.getDataTermino()) + "</br>";
        }
        List<String> lista = preencheListaProdutosFiltros();
        if (lista.size() > 0) {
            for (String produtos : lista) {
                filtro += "<b>Produto: </b>";
                filtro += " " + produtos;
                if (produtos.equalsIgnoreCase("Mês de Referência Plano")) {
                    filtro += " <b>Agrupado por: </b>" + getNomeAgrupamento(agrupamento);
                }
                filtro += "</br>";
            }
        }
        return filtro;
    }

    public String getNomeAgrupamento(String agrupamento) {
        if (agrupamento.equalsIgnoreCase("duracao")) {
            return "Duração";
        } else if (agrupamento.equalsIgnoreCase("nomeDuracao")) {
            return "Nome e Duração";
        } else if (agrupamento.equalsIgnoreCase("nome")) {
            return "Nome";
        }
        return "";
    }

    public List<String> preencheListaProdutosFiltros() {
        List listaSituacao = new ArrayList();
        if (getMatriculaRenovacaoRematricula()) {
            listaSituacao.add("Matrícula, Rematrícula, Renovação");
        }
        if (getMesReferenciaPlano()) {
            listaSituacao.add("Mês de Referência Plano");
        }
        if (getRetornoTrancamento()) {
            listaSituacao.add("Retorno Trancamento");
        }
        if (getDiaria()) {
            listaSituacao.add("Diária");
        }
        if (getAlterarHorario()) {
            listaSituacao.add("Alterar - Horário ");
        }
        if (getProdutoEstoque()) {
            listaSituacao.add("Produto Estoque");
        }
        if (getServico()) {
            listaSituacao.add("Serviço");
        }
        if (getTrancamento()) {
            listaSituacao.add("Trancamento");
        }
        if (getAulaAvulsa()) {
            listaSituacao.add("Aula Avulsa");
        }
        if (getFreePass()) {
            listaSituacao.add("FreePass");
        }
        if (getManutencaoModalidade()) {
            listaSituacao.add("Manutenção Modalidade");
        }
        if (isTaxaPersonal()) {
            listaSituacao.add("Taxa de Personal");
        }
        if (isSessao()) {
            listaSituacao.add("Sessão");
        }
        if (isPgtoSaldoDevedor()) {
            listaSituacao.add("PAGTO. SALDO DEVEDOR C/C");
        }
        if (isQuitacaoCancelamento()) {
            listaSituacao.add("Quitação de Dinheiro - Cancelamento");
        }
        if (isAcertoCCAluno()) {
            listaSituacao.add("Acerto C/C Aluno");
        }

        return listaSituacao;
    }

    public void visualizarPessoas() throws Exception {
        ClienteInterfaceFacade cliente = getFacade().getCliente();
        ContratoInterfaceFacade contrato = getFacade().getContrato();
        for (CompetenciaSinteticoResumoPessoaVO resumoTotal : getCompetenciaSinteticoProdutoMesVO().getListaResumoPessoa()) {
            if (resumoTotal.getCliente().getPessoa().getCodigo() != 0) {
                resumoTotal.setCliente(cliente.consultarPorCodigoPessoa(resumoTotal.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                if (resumoTotal.getCliente().getCodigo() == null || resumoTotal.getCliente().getCodigo() == 0) {
                    resumoTotal.setColaboradorVO(getFacade().getColaborador().consultarPorCodigoPessoa(
                            resumoTotal.getCliente().getPessoa().getCodigo(), 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
            }
            if (resumoTotal.getContrato().getCodigo() != 0) {
                resumoTotal.setContrato(contrato.consultarPorChavePrimariaComModalidade(resumoTotal.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
            }
        }
    }
    public void visualizarModalidades() throws Exception {
      listaResumoModalidade =  getFacade().getContratoModalidade().consultarContratoModalidades(contratoSelecionado,false,Uteis.NIVELMONTARDADOS_TELACONSULTA);
    }
    public void imprimirPDF() {
        try {
            validarDados();
            setTipoRelatorio("PDF");
            setListaRelatorio(new ArrayList());
            setDataInicioPrm(Uteis.getData(getCompetenciaSinteticoRel().getDataInicio()));
            setDataTerminoPrm(Uteis.getData(getCompetenciaSinteticoRel().getDataTermino()));
            competenciaSinteticoRel.setListaReciboPagamentoRelVOs(new ArrayList());
            List listaRegistro = competenciaSinteticoRel.montarDadosReciboPagamentoRelVO(getUsuarioLogado());
            if (!listaRegistro.isEmpty()) {
                setListaRelatorio(listaRegistro);
            } else {
                throw new Exception("Não foi encontrado nenhum recibo no período informado.");
            }
            setRelatorio("sim");
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
            imprimirRelatorio();
        } catch (Exception e) {
            setRelatorio("nao");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void getImprimirHTML() {
        try {
            setTipoRelatorio("HTML");
            setListaRelatorio(new ArrayList());
            setDataInicioPrm(Uteis.getData(getCompetenciaSinteticoRel().getDataInicio()));
            setDataTerminoPrm(Uteis.getData(getCompetenciaSinteticoRel().getDataTermino()));
            competenciaSinteticoRel.setListaReciboPagamentoRelVOs(new ArrayList());
            List listaRegistro = competenciaSinteticoRel.montarDadosReciboPagamentoRelVO(getUsuarioLogado());
            if (!listaRegistro.isEmpty()) {
                setListaRelatorio(listaRegistro);
            } else {
                throw new Exception("Não foi encontrado nenhum recibo no período informado.");
            }
            setRelatorio("sim");
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
            imprimirRelatorio();
        } catch (Exception e) {
            setRelatorio("nao");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void validarDados() throws Exception {
        if (getCompetenciaSinteticoRel().getDataInicio() == null) {
            throw new Exception("O campo DATA INÍCIO deve ser informado.");
        }

        if (getCompetenciaSinteticoRel().getDataTermino() == null) {
            throw new Exception("O campo DATA TÉRMINO deve ser informado.");
        }

        if (getCompetenciaSinteticoRel().getOrdenacao().equals("")) {
            throw new Exception("O campo ORDENAÇÃO deve ser informado.");
        }
        if (getCompetenciaSinteticoRel().getTipoVisualizacao().equals("")) {
            throw new Exception("O campo TIPO VISUALIZAÇÃO deve ser informado.");
        }

    }

    public void imprimirRelatorio() {
        try {
            String nomeRelatorio = competenciaSinteticoRel.getIdEntidade();
            String titulo = "Fechamento de Caixa Por Operador";
            String design = competenciaSinteticoRel.getDesignIReportRelatorio();
            String imagemLogo = "logoPadraoRelatorio.png";
            if (getEmpresaLogado().getCodigo().intValue() != 0 && !getEmpresaLogado().getNomeFotoRelatorio().equals("")) {
                imagemLogo = getEmpresaLogado().getNomeFotoRelatorio().substring(8, getEmpresaLogado().getNomeFotoRelatorio().length());
            }

            apresentarRelatorioObjetos(nomeRelatorio, titulo, "", imagemLogo, "", getTipoRelatorio(),
                    "/" + competenciaSinteticoRel.getIdEntidade() + "/registros", design,
                    getUsuarioLogado().getNome(), competenciaSinteticoRel.getDescricaoFiltros(),
                    getDataInicioPrm(), getDataTerminoPrm(),
                    String.valueOf(competenciaSinteticoRel.getQtdPagamentoAV()),
                    String.valueOf(competenciaSinteticoRel.getQtdPagamentoCA()),
                    String.valueOf(competenciaSinteticoRel.getQtdPagamentoChAvista()),
                    String.valueOf(competenciaSinteticoRel.getQtdPagamentoChPrazo()),
                    String.valueOf(competenciaSinteticoRel.getQtdPagamentoOutros()),
                    competenciaSinteticoRel.getValorPagamentoAV(),
                    competenciaSinteticoRel.getValorPagamentoCA(),
                    competenciaSinteticoRel.getValorPagamentoChAvista(),
                    competenciaSinteticoRel.getValorPagamentoChPrazo(),
                    competenciaSinteticoRel.getValorPagamentoOutros(),
                    CaixaPorOperadorRel.getCaminhoSubRelatorio(), CaixaPorOperadorRel.getCaminhoSubRelatorio(),
                    getListaRelatorio());
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String voltar() {
        return "voltar";
    }

    public List getListaSelectItemOrdenacao() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("MA", "Matrícula"));
        objs.add(new SelectItem("NO", "Nome do Aluno"));
        objs.add(new SelectItem("DU", "Duração"));
        return objs;
    }

    public List getListaSelectItemTipoVisualilzacao() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("SI", "Sintético"));
        objs.add(new SelectItem("AN", "Analítico"));
        return objs;
    }

    public List getListaSelectItemTipoProduto() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("MA", "Matrícula"));
        objs.add(new SelectItem("PE", "Produto Estoque"));
        objs.add(new SelectItem("PM", "Mês de Referência Plano"));
        objs.add(new SelectItem("RE", "Rematrícula"));
        objs.add(new SelectItem("RN", "Renovação"));
        objs.add(new SelectItem("SE", "Serviço"));
        objs.add(new SelectItem("TR", "Trancamento"));
        objs.add(new SelectItem("RT", "Retorno Trancamento"));
        objs.add(new SelectItem("AA", "Aula Avulsa"));
        objs.add(new SelectItem("DI", "Diária"));
        objs.add(new SelectItem("FR", "FreePass"));
        objs.add(new SelectItem("AH", "Alterar - Horário"));
        objs.add(new SelectItem("MM", "Manutenção Modalidade"));
        objs.add(new SelectItem("TP", "Taxa de Personal"));
        objs.add(new SelectItem("SS", "Sessão"));
        return objs;
    }

    public List getTipoConsultaComboOperador() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("nome", "Nome"));
        return objs;
    }

    public void marcarTodosTipoProduto() {
        setMatriculaRenovacaoRematricula(true);
        setProdutoEstoque(true);
        setMesReferenciaPlano(true);
        setServico(true);
        setTrancamento(true);
        setRetornoTrancamento(true);
        setAulaAvulsa(true);
        setDiaria(true);
        setFreePass(true);
        setAlterarHorario(true);
        setManutencaoModalidade(true);
    }

    public void DESmarcarTodosTipoProduto() {
        setMatriculaRenovacaoRematricula(false);
        setProdutoEstoque(false);
        setMesReferenciaPlano(false);
        setServico(false);
        setTrancamento(false);
        setRetornoTrancamento(false);
        setAulaAvulsa(false);
        setDiaria(false);
        setFreePass(false);
        setAlterarHorario(false);
        setManutencaoModalidade(false);
    }

    public List<ContratoModalidadeVO> getListaResumoModalidade() {
        return listaResumoModalidade;
    }

    public void setListaResumoModalidade(List<ContratoModalidadeVO> listaResumoModalidade) {
        this.listaResumoModalidade = listaResumoModalidade;
    }

    public CompetenciaSinteticoRel getCompetenciaSinteticoRel() {
        return competenciaSinteticoRel;
    }

    public void setCompetenciaSinteticoRel(CompetenciaSinteticoRel competenciaSinteticoRel) {
        this.competenciaSinteticoRel = competenciaSinteticoRel;
    }

    public String getCampoConsultarOperador() {
        return campoConsultarOperador;
    }

    public void setCampoConsultarOperador(String campoConsultarOperador) {
        this.campoConsultarOperador = campoConsultarOperador;
    }

    public String getValorConsultarOperador() {
        return valorConsultarOperador;
    }

    public void setValorConsultarOperador(String valorConsultarOperador) {
        this.valorConsultarOperador = valorConsultarOperador;
    }

    public int getContratoSelecionado() {
        return contratoSelecionado;
    }

    public void setContratoSelecionado(int contratoSelecionado) {
        this.contratoSelecionado = contratoSelecionado;
    }

    public String getDataInicioPrm() {
        return dataInicioPrm;
    }

    public void setDataInicioPrm(String dataInicioPrm) {
        this.dataInicioPrm = dataInicioPrm;
    }

    public String getDataTerminoPrm() {
        return dataTerminoPrm;
    }

    public void setDataTerminoPrm(String dataTerminoPrm) {
        this.dataTerminoPrm = dataTerminoPrm;
    }

    public Boolean getAlterarHorario() {
        return alterarHorario;
    }

    public void setAlterarHorario(Boolean alterarHorario) {
        this.alterarHorario = alterarHorario;
    }

    public Boolean getAulaAvulsa() {
        return aulaAvulsa;
    }

    public void setAulaAvulsa(Boolean aulaAvulsa) {
        this.aulaAvulsa = aulaAvulsa;
    }

    public Boolean getDiaria() {
        return diaria;
    }

    public void setDiaria(Boolean diaria) {
        this.diaria = diaria;
    }

    public Boolean getFreePass() {
        return freePass;
    }

    public void setFreePass(Boolean freePass) {
        this.freePass = freePass;
    }

    public Boolean getManutencaoModalidade() {
        return manutencaoModalidade;
    }

    public void setManutencaoModalidade(Boolean manutencaoModalidade) {
        this.manutencaoModalidade = manutencaoModalidade;
    }

    public Boolean getMatriculaRenovacaoRematricula() {
        return matrRenovRemat;
    }

    public void setMatriculaRenovacaoRematricula(Boolean matrRenovRemat) {
        this.matrRenovRemat = matrRenovRemat;
    }

    public Boolean getMesReferenciaPlano() {
        return mesReferenciaPlano;
    }

    public void setMesReferenciaPlano(Boolean mesReferenciaPlano) {
        this.mesReferenciaPlano = mesReferenciaPlano;
    }

    public Boolean getProdutoEstoque() {
        return produtoEstoque;
    }

    public void setProdutoEstoque(Boolean produtoEstoque) {
        this.produtoEstoque = produtoEstoque;
    }

    public Boolean getRetornoTrancamento() {
        return retornoTrancamento;
    }

    public void setRetornoTrancamento(Boolean retornoTrancamento) {
        this.retornoTrancamento = retornoTrancamento;
    }

    public Boolean getServico() {
        return servico;
    }

    public void setServico(Boolean servico) {
        this.servico = servico;
    }

    public Boolean getTrancamento() {
        return trancamento;
    }

    public void setTrancamento(Boolean trancamento) {
        this.trancamento = trancamento;
    }

    public List<CompetenciaSinteticoTipoProdutoVO> getListaTipoProdutoVO() {
        return listaTipoProdutoVO;
    }

    public void setListaTipoProdutoVO(List<CompetenciaSinteticoTipoProdutoVO> listaTipoProdutoVO) {
        this.listaTipoProdutoVO = listaTipoProdutoVO;
    }

    public List<SelectItem> getListaDeEmpresa() {
        return listaDeEmpresa;
    }

    public void setListaDeEmpresa(List<SelectItem> listaDeEmpresa) {
        this.listaDeEmpresa = listaDeEmpresa;
    }

    public List<PeriodoMensal> getPeriodos() {
        return periodos;
    }

    public void setPeriodos(List<PeriodoMensal> periodos) {
        this.periodos = periodos;
    }

    public CompetenciaSinteticoProdutoMesVO getCompetenciaSinteticoProdutoMesVO() {
        return competenciaSinteticoProdutoMesVO;
    }

    public void setCompetenciaSinteticoProdutoMesVO(CompetenciaSinteticoProdutoMesVO competenciaSinteticoProdutoMesVO) {
        this.competenciaSinteticoProdutoMesVO = competenciaSinteticoProdutoMesVO;
    }

    public Boolean getTemEmpresa() {
        return temEmpresa;
    }

    public void setTemEmpresa(Boolean temEmpresa) {
        this.temEmpresa = temEmpresa;
    }

    public void novo() throws Exception {
        inicializarDados();
        return;
    }

    public void irParaTelaCliente() {
        CompetenciaSinteticoResumoPessoaVO obj = (CompetenciaSinteticoResumoPessoaVO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(obj.getCliente());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void irParaTelaColaborador() {
        CompetenciaSinteticoResumoPessoaVO obj = (CompetenciaSinteticoResumoPessoaVO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        try {
            if (obj == null) {
                throw new Exception("Colaborador Não Encontrado.");
            } else {
                irParaTelaColaborador(obj.getColaboradorVO());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    /**
     * @return the resumo
     */
    public CompetenciaSinteticoTipoProdutoVO getResumo() {
        return resumo;
    }

    /**
     * @param resumo the resumo to set
     */
    public void setResumo(CompetenciaSinteticoTipoProdutoVO resumo) {
        this.resumo = resumo;
    }

    /**
     * @return the agrupamento
     */
    public String getAgrupamento() {
        return agrupamento;
    }

    /**
     * @param agrupamento the agrupamento to set
     */
    public void setAgrupamento(String agrupamento) {
        this.agrupamento = agrupamento;
    }

    public String getTipoProduto_Apresentar(String tipoProduto) {
        if (tipoProduto == null) {
            tipoProduto = "";
        }
        if (tipoProduto.equals("MARERN")) {
            return "Matrícula, Rematrícula, Renovação";
        }
        if (tipoProduto.equals("PE")) {
            return "Produto Estoque";
        }
        if (tipoProduto.equals("PM")) {
            return "Mês de Referência Plano";
        }
        if (tipoProduto.equals("SE")) {
            return "Serviço";
        }
        if (tipoProduto.equals("CD")) {
            return "Convênio de Desconto";
        }
        if (tipoProduto.equals("DE")) {
            return "Desconto";
        }
        if (tipoProduto.equals("DV")) {
            return "Devolução";
        }
        if (tipoProduto.equals("TR")) {
            return "Trancamento";
        }
        if (tipoProduto.equals("RT")) {
            return "Retorno Trancamento";
        }
        if (tipoProduto.equals("AA")) {
            return "Aula Avulsa";
        }
        if (tipoProduto.equals("DI")) {
            return "Diária";
        }
        if (tipoProduto.equals("FR")) {
            return "FreePass";
        }
        if (tipoProduto.equals("AH")) {
            return "Alterar - Horário";
        }
        if (tipoProduto.equals("MM")) {
            return "Manutenção Modalidade";
        }
        if (tipoProduto.equals("MC")) {
            return "Pagamento de Saldo Devedor C/C";
        }
        if (tipoProduto.equals("DR")) {
            return "Desconto em Renovação Antecipada";
        }
        if (tipoProduto.equals("TP")) {
            return "Taxa de Personal";
        }
        if (tipoProduto.equals("SS")) {
            return "Sessão";
        }
        if (tipoProduto.equals("QU")) {
            return "Quitação de dinheiro - Cancelamento";
        }
        if (tipoProduto.equals("AC")) {
            return "Acerto Conta Corrente Aluno";
        }
        return (tipoProduto);
    }

    public boolean isTaxaPersonal() {
        return taxaPersonal;
    }

    public void setTaxaPersonal(boolean taxaPersonal) {
        this.taxaPersonal = taxaPersonal;
    }

    public boolean isSessao() {
        return sessao;
    }

    public void setSessao(boolean sessao) {
        this.sessao = sessao;
    }

    public boolean isPgtoSaldoDevedor() {
        return pgtoSaldoDevedor;
    }

    public void setPgtoSaldoDevedor(boolean pgtoSaldoDevedor) {
        this.pgtoSaldoDevedor = pgtoSaldoDevedor;
    }

    public boolean isQuitacaoCancelamento() {
        return quitacaoCancelamento;
    }

    public void setQuitacaoCancelamento(boolean quitacaoCancelamento) {
        this.quitacaoCancelamento = quitacaoCancelamento;
    }

    public boolean isAcertoCCAluno() {
        return acertoCCAluno;
    }

    public void setAcertoCCAluno(boolean acertoCCAluno) {
        this.acertoCCAluno = acertoCCAluno;
    }

    public List<CompetenciaSinteticoRelTO> getListaExportavel() {
        return listaExportavel;
    }

    public void setListaExportavel(List<CompetenciaSinteticoRelTO> listaExportavel) {
        this.listaExportavel = listaExportavel;
    }
}

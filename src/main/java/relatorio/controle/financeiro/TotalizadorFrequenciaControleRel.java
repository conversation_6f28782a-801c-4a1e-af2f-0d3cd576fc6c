package relatorio.controle.financeiro;

import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.imageio.ImageIO;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.basico.Empresa;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.data.category.CategoryDataset;
import org.jfree.data.category.DefaultCategoryDataset;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.jdbc.financeiro.TotalizadorFrequenciaRel;

public class TotalizadorFrequenciaControleRel extends SuperControleRelatorio {

    protected TotalizadorFrequenciaRel totalizadorFrequenciaRel;
    protected List<TotalizadorFrequenciaRel> listaDeTotalizadores;
    protected Boolean popUp;
    protected Integer grafico;
    private String filtros;
    private JFreeChart chart;
    private CategoryDataset dataset;
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;
    private Integer agrupar;
    private ClienteVO clienteVO;
    private ColaboradorVO colaboradorVO;
    private PlanoVO planoVO;
    private List<SelectItem> selectItemPlano;
    private List<PlanoVO> listaPlanos;
    private List<ModalidadeVO> listaModalidades;
    private List<SelectItem> selectItemModalidade;
    private ModalidadeVO modalidadeVO;
    private boolean agruparPorPessoa = false;
    private Integer codigoPlanoSelecionado;
    private Integer codigoModalidadeSelecionada;

    public TotalizadorFrequenciaControleRel() {
        inicializarDados();
    }

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            montarListaEmpresasComItemTodas();
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void inicializarDados() {
        try {
            setTotalizadorFrequenciaRel(new TotalizadorFrequenciaRel());
            setListaDeTotalizadores(new ArrayList<TotalizadorFrequenciaRel>());
            setPopUp(Boolean.FALSE);
            setGrafico(1);
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
            montarListaSelectItemEmpresa();

            setAgruparPorPessoa(false);
            setClienteVO(new ClienteVO());
            setColaboradorVO(new ColaboradorVO());

            setPlanoVO(new PlanoVO());
            setSelectItemPlano(new ArrayList<SelectItem>());
            setListaPlanos(new ArrayList<PlanoVO>());

            setModalidadeVO(new ModalidadeVO());
            setSelectItemModalidade(new ArrayList<SelectItem>());
            setListaModalidades(new ArrayList<ModalidadeVO>());

            montarListaSelectItemPlano();
            montarListaSelectItemModalidade();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List getListaSelectItemFrequencia() {
        List objs = new ArrayList();
        objs.add(new SelectItem(1, "Geral"));
        objs.add(new SelectItem(2, "Geral Por Dia da Semana"));
        objs.add(new SelectItem(3, "Por Horário"));
        return objs;
    }

    public List getListaSelectItemGrafico() {
        List objs = new ArrayList();
        objs.add(new SelectItem(1, "Não Mostrar Gráfico"));
        objs.add(new SelectItem(2, "Mostrar Gráfico"));
        return objs;
    }

    public void imprimir() {
        try {
            setListaDeTotalizadores(new ArrayList<TotalizadorFrequenciaRel>());

            if(getTotalizadorFrequenciaRel().getIdadeInicio() > getTotalizadorFrequenciaRel().getIdadeFim()){
                throw new ConsistirException("Faixa etária invalida, o valor de início não pode ser maior que o valor final.");
            }
            totalizadorFrequenciaRel.validarDados();
            getListaDeTotalizadores().addAll(getTotalizadorFrequenciaRel().consultaParametrizada(getFiltroEmpresa(), getClienteVO().getCodigo(), getPlanoVO().getCodigo(), getModalidadeVO().getCodigo(), getColaboradorVO().getCodigo(), isAgruparPorPessoa()));
//            getListaDeTotalizadores().addAll(getTotalizadorFrequenciaRel().consultaParametrizada(getFiltroEmpresa(), 9384, getPlanoVO().getCodigo(), getColaboradorVO().getCodigo(), isAgruparPorPessoa()));
            if (getGrafico() == 2) {
                montarDataBarraListaPrevista(getListaDeTotalizadores());
            }

            //Monta String que será apresentada na tela do relatório totalizador de acessos.
            montarDescricaoFiltros();

            setMensagemID("msg_relatorio_ok");
            setPopUp(true);
        } catch (ConsistirException e) {
            setPopUp(false);
            setMensagemID("msg_erro_relatorio");
            setMensagemDetalhada(e.getMessage());
        } catch (Exception e) {
            setPopUp(false);
            setMensagemID("");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    private void montarDescricaoFiltros() throws Exception {
        StringBuilder filtros = new StringBuilder();
        filtros.append("<b>Empresa: </b>").append(getNomeEmpresaSelecionada(getFiltroEmpresa())).append(", </br>");

        if(getTotalizadorFrequenciaRel().getDataInicio() != null && getTotalizadorFrequenciaRel().getDataTermino() != null) {
            filtros.append("<b> Período de: </b>").append(Uteis.getDataAplicandoFormatacao(getTotalizadorFrequenciaRel().getDataInicio(), "dd/MM/yyyy"));
            filtros.append(" <b>até: </b>").append(Uteis.getData(getTotalizadorFrequenciaRel().getDataTermino())).append(", </br>");
        }

        if (getTotalizadorFrequenciaRel().getFrequencia() == 1) {
            filtros.append("<b>Frequência: </b>Geral");
        }
        if (getTotalizadorFrequenciaRel().getFrequencia() == 2) {
            filtros.append("<b>Frequência: </b>Geral por Dia da Semana");
        }
        if (getTotalizadorFrequenciaRel().getFrequencia() == 3) {
            filtros.append("<b>Frequência: </b>Por Horário");
        }


        if(getTotalizadorFrequenciaRel().getGeneroSelecionado() != null){
            if(getTotalizadorFrequenciaRel().getGeneroSelecionado().equals("M")) {
                filtros.append(", </br>");
                filtros.append("<b> Sexo: </b>Masculino");
            } else if(getTotalizadorFrequenciaRel().getGeneroSelecionado().equals("F")) {
                filtros.append(", </br>");
                filtros.append("<b> Sexo: </b>Feminino");
            }
        }

        if(getTotalizadorFrequenciaRel().getIdadeFim() > getTotalizadorFrequenciaRel().getIdadeInicio()) {
            filtros.append(", </br>");
            filtros.append("<b> Faixa Etária de: </b>").append(getTotalizadorFrequenciaRel().getIdadeInicio());
            filtros.append(" <b>até: </b>").append(getTotalizadorFrequenciaRel().getIdadeFim());
        }

        if(!UteisValidacao.emptyNumber(getClienteVO().getCodigo())) {
            filtros.append(", </br>");
            filtros.append("<b> Cliente: </b>").append(getClienteVO().getNome_Apresentar());
        }

        if(!UteisValidacao.emptyNumber(getPlanoVO().getCodigo())) {
            filtros.append(", </br>");
            filtros.append("<b> Plano: </b>").append(getPlanoVO().getDescricao());
        }

        if(!UteisValidacao.emptyNumber(getModalidadeVO().getCodigo())) {
            filtros.append(", </br>");
            filtros.append("<b> Modalidade: </b>").append(getModalidadeVO().getNome());
        }

        if(!UteisValidacao.emptyNumber(getColaboradorVO().getCodigo())) {
            filtros.append(", </br>");
            filtros.append("<b> Colaborador: </b>").append(getColaboradorVO().getPessoa_Apresentar());
        }

        setFiltros(filtros.toString());
    }

    public String getAbrirPopUp() {
        if (getPopUp()) {
            return "abrirPopup('../relatorio/totalizadorFrequenciaForm.jsp', 'Totalizadorfrequencia', 780, 595);";
        } else {
            return "";
        }
    }

    public List consultarEmpresaPorNome(
            String nomePrm) throws Exception {
        List lista = new Empresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public void montarDataBarraListaPrevista(List<TotalizadorFrequenciaRel> lista) throws Exception {
        try {
            getTotalizadorFrequenciaRel().setDataSetBarra(new DefaultCategoryDataset());
            for (TotalizadorFrequenciaRel obj : lista) {
                if (getTotalizadorFrequenciaRel().getFrequencia() == 1) {
                    getTotalizadorFrequenciaRel().getDataSetBarra().addValue(obj.getQuantidade(), Uteis.getData(obj.getDataInicio()), "");
                }
                if (getTotalizadorFrequenciaRel().getFrequencia() == 2) {
                    getTotalizadorFrequenciaRel().getDataSetBarra().addValue(obj.getQuantidade(), obj.getDiaDaSemana(), "");
                }
                if (getTotalizadorFrequenciaRel().getFrequencia() == 3) {
                    getTotalizadorFrequenciaRel().getDataSetBarra().addValue(obj.getQuantidade(), obj.getHora().toString(), "");
                }

            }
        } catch (Exception e) {
            throw e;
        }

    }

    public TotalizadorFrequenciaRel getTotalizadorFrequenciaRel() {
        return totalizadorFrequenciaRel;
    }

    public void setTotalizadorFrequenciaRel(TotalizadorFrequenciaRel totalizadorFrequenciaRel) {
        this.totalizadorFrequenciaRel = totalizadorFrequenciaRel;
    }

    public Integer getGrafico() {
        return grafico;
    }

    public void setGrafico(Integer grafico) {
        this.grafico = grafico;
    }

    public List<TotalizadorFrequenciaRel> getListaDeTotalizadores() {
        return listaDeTotalizadores;
    }

    public void setListaDeTotalizadores(List<TotalizadorFrequenciaRel> listaDeTotalizadores) {
        this.listaDeTotalizadores = listaDeTotalizadores;
    }

    public Boolean getPopUp() {
        return popUp;
    }

    public void setPopUp(Boolean popUp) {
        this.popUp = popUp;
    }

    public void novo() {
        return;
    }

    /**
     * @return the filtros
     */
    public String getFiltros() {
        return filtros;
    }

    /**
     * @param filtros the filtros to set
     */
    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public void exportar(ActionEvent eve) throws Exception {
        try {
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) getControlador(ExportadorListaControle.class.getSimpleName());
            if (getGrafico().intValue() == 2) {
                BufferedImage image = getChart().createBufferedImage(500, 300);
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                ImageIO.write(image, "gif", os);
                InputStream is = new ByteArrayInputStream(os.toByteArray());
                exportadorListaControle.setImagemGrafico(is);
            }
            exportadorListaControle.exportar(eve);
        } catch (Exception e) {
            setMensagemID("");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarGrafico(OutputStream out, Object data) throws IOException {
        BufferedImage bufferedImage = getChart().createBufferedImage(500, 300);
        ImageIO.write(bufferedImage, "gif", out);
    }

    private CategoryDataset criarDados() {
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();
        for (TotalizadorFrequenciaRel obj : getListaDeTotalizadores()) {
            if (getTotalizadorFrequenciaRel().getFrequencia() == 1) {
                dataset.addValue(obj.getQuantidade(), Uteis.getData(obj.getDataInicio()), "");
            }
            if (getTotalizadorFrequenciaRel().getFrequencia() == 2) {
                dataset.addValue(obj.getQuantidade(), obj.getDiaDaSemana(), "");
            }
            if (getTotalizadorFrequenciaRel().getFrequencia() == 3) {
                dataset.addValue(obj.getQuantidade(), obj.getHora().toString(), "");
            }
        }
        return dataset;
    }

    public JFreeChart getChart() {
        if (getTotalizadorFrequenciaRel().getFrequencia() == 1) {
            chart = ChartFactory.createBarChart("Quantidade Por Data", "", "Quantidade", getDataset(), PlotOrientation.VERTICAL, true, true, false);
        }
        if (getTotalizadorFrequenciaRel().getFrequencia() == 2) {
            chart = ChartFactory.createBarChart("Quantidade Por Dia", "", "Quantidade", getDataset(), PlotOrientation.VERTICAL, true, true, false);
        }
        if (getTotalizadorFrequenciaRel().getFrequencia() == 3) {
            chart = ChartFactory.createBarChart("Quantidade Por Hora", "", "Quantidade", getDataset(), PlotOrientation.VERTICAL, true, true, false);
        }
        return chart;
    }

    public void setChart(JFreeChart chart) {
        this.chart = chart;
    }

    public CategoryDataset getDataset() {
        dataset = criarDados();
        return dataset;
    }

    public void setDataset(CategoryDataset dataset) {
        this.dataset = dataset;
    }
    
    public String getFiltrosSemHtml(){
        return Uteis.retiraTags(this.getFiltros(), false);
    }
    
    public String getAtributos(){
        String atributos = "";
        if (getTotalizadorFrequenciaRel().getFrequencia() == 1) {
            if (isAgruparPorPessoa()) {
                atributos = "matricula=Matricula,nome=Nome,plano=Plano,modalidade=Modalidade,quantidade=Quantidade,porcentagem=Porcentagem (%)";
            } else {
                atributos = "dataInicio=Data,quantidade=Quantidade,porcentagem=Porcentagem (%),porcentagemAtivos=Porcentagem Ativos(%)";
            }
        } else if (getTotalizadorFrequenciaRel().getFrequencia() == 2) {
            if (getTotalizadorFrequenciaRel().getAgrupamento().equals("NENHUM")) {
                atributos = "diaDaSemana=Dia da Semana,quantidade=Quantidade,porcentagem=Porcentagem (%),porcentagemAtivos=Porcentagem Ativos(%)";
            } else {
                atributos = "diaDaSemana=Dia da Semana,quantidade=Quantidade,porcentagemRelacionada=Porcent. maior dia (%),porcentagem=Porcentagem (%),porcentagemAtivos=Porcentagem Ativos(%)";
            }
        } else if (getTotalizadorFrequenciaRel().getFrequencia() == 3) {
            atributos = "hora=Hora,quantidade=Quantidade,porcentagem=Porcentagem (%),porcentagemAtivos=Porcentagem Ativos(%)";
        }

        if (isPermissaoConsultaTodasEmpresas()) {
            atributos = atributos + ",nomeEmpresa=Empresa";
        }
        return atributos;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public ColaboradorVO getColaboradorVO() {
        if (colaboradorVO == null) {
            colaboradorVO = new ColaboradorVO();
        }
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public List<SelectItem> getSelectItemPlano() {
        if (selectItemPlano == null) {
            selectItemPlano = new ArrayList<SelectItem>();
        }
        return selectItemPlano;
    }

    public void setSelectItemPlano(List<SelectItem> selectItemPlano) {
        this.selectItemPlano = selectItemPlano;
    }

    public List<PlanoVO> getListaPlanos() {
        if (listaPlanos == null) {
            listaPlanos = new ArrayList<PlanoVO>();
        }
        return listaPlanos;
    }

    public void setListaPlanos(List<PlanoVO> listaPlanos) {
        this.listaPlanos = listaPlanos;
    }

    public PlanoVO getPlanoVO() {
        if (planoVO == null) {
            planoVO = new PlanoVO();
        }
        return planoVO;
    }

    public void setPlanoVO(PlanoVO planoVO) {
        this.planoVO = planoVO;
    }

    public List<ClienteVO> executarAutocompleteConsultaCliente(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ClienteVO> result;
        try {

            if (pref.equals("%")) {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarPorNomeClienteComLimite(getFiltroEmpresa(), "", false, Uteis.NIVELMONTARDADOS_MINIMOS);
            } else {
                if (UteisValidacao.somenteNumeros(pref)) {
                    result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarPorMatricula(pref, getFiltroEmpresa(),false, Uteis.NIVELMONTARDADOS_MINIMOS);
                } else {
                    result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarPorNomeClienteComLimite(getFiltroEmpresa(), pref, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                }
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<ClienteVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarClienteSuggestionBox() {
        ClienteVO clienteVO = (ClienteVO) request().getAttribute("result");
        setClienteVO(clienteVO);
    }

    public List<ColaboradorVO> executarAutocompleteConsultaColaborador(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ColaboradorVO> resultCol;
        try {
            if (pref.equals("%")) {
                resultCol = (ArrayList<ColaboradorVO>) getFacade().getColaborador().consultarPorNomeColaboradorComLimite("", getFiltroEmpresa(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                resultCol = (ArrayList<ColaboradorVO>) getFacade().getColaborador().consultarPorNomeColaboradorComLimite(pref, getFiltroEmpresa(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            resultCol = (new ArrayList<ColaboradorVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return resultCol;
    }

    public void selecionarColaboradorSuggestionBox() {
        ColaboradorVO colaboradorVO = (ColaboradorVO) request().getAttribute("resultCol");
        setColaboradorVO(colaboradorVO);
    }

    public void montarListaSelectItemPlano() {
        try {
            setPlanoVO(new PlanoVO());
            setSelectItemPlano(new ArrayList<SelectItem>());
            setListaPlanos(new ArrayList<PlanoVO>());

            List<SelectItem> objs = new ArrayList<SelectItem>();
            objs.add(new SelectItem(0, ""));

            List<PlanoVO> listaPlanos = getFacade().getPlano().consultarPorDescricao("", getFiltroEmpresa(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            Ordenacao.ordenarLista(listaPlanos, "descricao");
            for (PlanoVO planoVO : listaPlanos) {
                objs.add(new SelectItem(planoVO.getCodigo(), planoVO.getDescricao()));
            }
            setSelectItemPlano(objs);
            setListaPlanos(listaPlanos);
        } catch (Exception ex) {
            setSelectItemPlano(new ArrayList<SelectItem>());
            setListaPlanos(new ArrayList<PlanoVO>());
        }
    }

    public void montarListaSelectItemModalidade() {
        try {
            setModalidadeVO(new ModalidadeVO());
            setSelectItemModalidade(new ArrayList<>());
            setListaModalidades(new ArrayList<>());

            List<SelectItem> objs = new ArrayList<>();
            objs.add(new SelectItem(0, ""));

            List<ModalidadeVO> listaModalidades = getFacade().getModalidade().consultarPorNome("", getFiltroEmpresa(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            Ordenacao.ordenarLista(listaModalidades, "nome");
            for (ModalidadeVO modalidadeVO : listaModalidades) {
                objs.add(new SelectItem(modalidadeVO.getCodigo(), modalidadeVO.getNome()));
            }
            setSelectItemModalidade(objs);
            setListaModalidades(listaModalidades);
        } catch (Exception ex) {
            setSelectItemModalidade(new ArrayList<>());
            setListaModalidades(new ArrayList<>());
        }
    }

    public void limparCliente() {
        setClienteVO(new ClienteVO());
    }
    public void limparColaborador() {
        setColaboradorVO(new ColaboradorVO());
    }

    public void obterPlanoEscolhido() {
        if (UteisValidacao.emptyNumber(codigoPlanoSelecionado)) {
            setPlanoVO(new PlanoVO());
        } else {
            for (PlanoVO planoVO : getListaPlanos()) {
                if (planoVO.getCodigo().equals(codigoPlanoSelecionado)) {
                    setPlanoVO(planoVO);
                    break;
                }
            }
        }
    }

    public void obterModalidadeEscolhido() {
        if (UteisValidacao.emptyNumber(codigoModalidadeSelecionada)) {
            setModalidadeVO(new ModalidadeVO());
        } else {
            for (ModalidadeVO modalidadeVO : getListaModalidades()) {
                if (modalidadeVO.getCodigo().equals(codigoModalidadeSelecionada)) {
                    setModalidadeVO(modalidadeVO);
                    break;
                }
            }
        }
    }

    public boolean isAgruparPorPessoa() {
        return agruparPorPessoa;
    }

    public void setAgruparPorPessoa(boolean agruparPorPessoa) {
        this.agruparPorPessoa = agruparPorPessoa;
    }

    public Integer getAgrupar() {
        if (agrupar == null) {
            agrupar = 0;
        }
        return agrupar;
    }

    public void setAgrupar(Integer agrupar) {
        this.agrupar = agrupar;
    }

    public void marcarAgrupar() {
        if (isAgruparPorPessoa()) {
            getTotalizadorFrequenciaRel().setSomenteAcessoDia(false);
            setGrafico(1);
        }
    }

    public void alterarFrequencia() {
        if (!getTotalizadorFrequenciaRel().getFrequencia().equals(1)) {
            setAgruparPorPessoa(false);
        }
    }

    public List<ModalidadeVO> getListaModalidades() {
        return listaModalidades;
    }

    public void setListaModalidades(List<ModalidadeVO> listaModalidades) {
        this.listaModalidades = listaModalidades;
    }

    public List<SelectItem> getSelectItemModalidade() {
        return selectItemModalidade;
    }

    public void setSelectItemModalidade(List<SelectItem> selectItemModalidade) {
        this.selectItemModalidade = selectItemModalidade;
    }

    public ModalidadeVO getModalidadeVO() {
        return modalidadeVO;
    }

    public void setModalidadeVO(ModalidadeVO modalidadeVO) {
        this.modalidadeVO = modalidadeVO;
    }

    public Integer getCodigoPlanoSelecionado() {
        return codigoPlanoSelecionado;
    }

    public void setCodigoPlanoSelecionado(Integer codigoPlanoSelecionado) {
        this.codigoPlanoSelecionado = codigoPlanoSelecionado;
    }

    public Integer getCodigoModalidadeSelecionada() {
        return codigoModalidadeSelecionada;
    }

    public void setCodigoModalidadeSelecionada(Integer codigoModalidadeSelecionada) {
        this.codigoModalidadeSelecionada = codigoModalidadeSelecionada;
    }
}

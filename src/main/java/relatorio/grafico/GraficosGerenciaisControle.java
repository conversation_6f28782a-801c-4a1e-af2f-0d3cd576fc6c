/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.grafico;

import java.util.Date;
import net.sf.jasperreports.charts.util.DefaultXYZDataset;
import org.jfree.data.time.Day;
import org.jfree.data.time.Month;
import org.jfree.data.time.TimeSeries;
import org.jfree.data.time.TimeSeriesCollection;
import org.jfree.data.xy.XYDataset;

/**
 *
 * <AUTHOR>
 */
public class GraficosGerenciaisControle {

    private XYDataset dataset;

    public GraficosGerenciaisControle() {
        try {
            dadosXYDataset();
        } catch (Exception e) {
        }
    }

//    private void createDataset() {
//        TimeSeries series1 = new TimeSeries("First", Day.class);
//        series1.add(new Day(negocio.comuns.utilitarias.Calendario.hoje()), 1);
//
//        TimeSeries series2 = new TimeSeries("Second", Day.class);
//        series1.add(new Day(negocio.comuns.utilitarias.Calendario.hoje()), 2);
//
//        TimeSeriesCollection datasetCollection = new TimeSeriesCollection();
//        datasetCollection.addSeries(series1);
//        datasetCollection.addSeries(series2);
//
//        datasetCollection.setDomainIsPointsInTime(true);
//
//        dataset = datasetCollection;
//    }
    public void dadosXYDataset() {
        setDataset(new DefaultXYZDataset());
        TimeSeriesCollection timeSeriesDataSet = new TimeSeriesCollection();
        TimeSeries s1 = new TimeSeries("Ativo", Day.class);
        s1.add(new Day(1, 1, 2009), 1.5);
        s1.add(new Day(30, 7, 2009), 1.5);

        TimeSeries s2 = new TimeSeries("Inativo", Day.class);
        s2.add(new Day(1, 8, 2009), 1.5);
        s2.add(new Day(30, 10, 2009), 1.5);

        TimeSeries s3 = new TimeSeries("");
        s3.add(new Day(negocio.comuns.utilitarias.Calendario.hoje()), 2);

        TimeSeries s4 = new TimeSeries("");
        s4.add(new Day(negocio.comuns.utilitarias.Calendario.hoje()), 1);

        timeSeriesDataSet.addSeries(s1);
        timeSeriesDataSet.addSeries(s2);
        timeSeriesDataSet.addSeries(s3);
        timeSeriesDataSet.addSeries(s4);

        dataset = timeSeriesDataSet;
    }

    /**
     * @return the dataset
     */
    public XYDataset getDataset() {
        return dataset;
    }

    /**
     * @param dataset the dataset to set
     */
    public void setDataset(XYDataset dataset) {
        this.dataset = dataset;
    }
}

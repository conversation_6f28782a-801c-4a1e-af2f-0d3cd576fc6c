//relatorio
package relatorio.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import it.businesslogic.ireport.connection.JRXMLDataSource;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.text.DateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import net.sf.jasperreports.engine.export.JRHtmlExporter;
import net.sf.jasperreports.engine.export.JRHtmlExporterParameter;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import net.sf.jasperreports.engine.util.JRLoader;
import controle.arquitetura.SuperControle;
import javax.faces.context.FacesContext;
import javax.servlet.ServletContext;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import net.sf.jasperreports.engine.JRGroup;
import net.sf.jasperreports.engine.JRParameter;
import net.sf.jasperreports.engine.export.JRXlsExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import net.sf.jasperreports.engine.fill.JRFileVirtualizer;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import static controle.arquitetura.SuperControle.LABEL_TODAS_EMPRESAS;

/**
 * Servlet responsável por gerar a visualização do relatório e apresentá-lo ao
 * usuário.
 * É utilizado por todos os relatórios do sistema com esta finalidade. É capaz
 * de receber os
 * dados do relatório e compilar o relatório final utilizando o JasperReport.
 */
public class VisualizadorRelatorio extends HttpServlet {

    public String obterCaminhoBaseAplicacao(HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (JSFUtilities.isJSFContext()) {
            return ((ServletContext) FacesContext.getCurrentInstance().getExternalContext().getContext()).getRealPath("WEB-INF" + File.separator + "classes");
        } else {
            return new File(request.getSession().getServletContext().getRealPath("WEB-INF" + File.separator + "classes")).getAbsolutePath();
        }
    }

    /**
     * Rotina responsável por obter o diretório real da aplicação Web em
     * execução.
     * É importante acessar este diretório para que seja possível utilizar
     * recursos
     * existentes nos pacotes da aplicação.
     */
    public String obterCaminhoWebAplicacao(HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (JSFUtilities.isJSFContext()) {
            return ((ServletContext) FacesContext.getCurrentInstance().getExternalContext().getContext()).getRealPath("");
        } else {
            return new File(request.getSession().getServletContext().getRealPath("")).getAbsolutePath();
        }
    }

    public JasperPrint gerarRelatorioJasperPrintXML(HttpServletRequest request, HttpServletResponse response, String xmlDados, String caminhoParserXML, String nomeDesignIReport) throws Exception {
        JRXMLDataSource jrxmlds = new JRXMLDataSource(new ByteArrayInputStream(xmlDados.getBytes()), caminhoParserXML);
        String nomeJasperReportDesignIReport = nomeDesignIReport.substring(0, nomeDesignIReport.lastIndexOf(".")) + ".jasper";
        File arquivoIReport = new File(obterCaminhoBaseAplicacao(request, response) + File.separator + nomeJasperReportDesignIReport);
        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);
        jasperReport.setProperty("net.sf.jasperreports.awt.ignore.missing.font", "true");
        jasperReport.setProperty("net.sf.jasperreports.default.pdf.font.name", "SansSerif");
        return JasperFillManager.fillReport(jasperReport, (Map) request.getAttribute("parametrosRelatorio"), jrxmlds);

    }

    public JasperPrint gerarRelatorioJasperPrintXML(HttpServletRequest request, HttpServletResponse response, String nomeDesignIReport, Connection con) throws Exception {
        String nomeJasperReportDesignIReport = nomeDesignIReport.substring(0, nomeDesignIReport.lastIndexOf(".")) + ".jasper";
        File arquivoIReport = new File(obterCaminhoBaseAplicacao(request, response) + File.separator + nomeJasperReportDesignIReport);
        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);
        jasperReport.setProperty("net.sf.jasperreports.awt.ignore.missing.font", "true");
        return JasperFillManager.fillReport(jasperReport, (Map) request.getAttribute("parametrosRelatorio"), con);

    }

    public JasperPrint gerarRelatorioJasperPrintObjeto(HttpServletRequest request, HttpServletResponse response, String nomeDesignIReport) throws Exception {
        JRDataSource jr = new JRBeanArrayDataSource(((List) request.getAttribute("listaObjetos")).toArray(), false);
        String nomeJasperReportDesignIReport = nomeDesignIReport.substring(0, nomeDesignIReport.lastIndexOf(".")) + ".jasper";
        File arquivoIReport = new File(obterCaminhoBaseAplicacao(request, response) + File.separator + nomeJasperReportDesignIReport);
        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);
        jasperReport.setProperty("net.sf.jasperreports.awt.ignore.missing.font", "true");
        jasperReport.setProperty("net.sf.jasperreports.default.pdf.font.name", "SansSerif");

        if (request.getAttribute("group_startnewpage") != null) {
            JRGroup[] groups = jasperReport.getGroups();
            if (groups != null) {
                for (JRGroup jRGroup : groups) {
                    Boolean b = (Boolean) request.getAttribute("group_startnewpage");
                    jRGroup.setStartNewPage(b);
                }
            }
        }

        return JasperFillManager.fillReport(jasperReport, (Map) request.getAttribute("parametrosRelatorio"), jr);
    }

    protected void visualizarRelatorioHTML(HttpServletRequest request, HttpServletResponse response, JasperPrint print) throws ServletException, IOException, Exception {
        response.setContentType("text/html;charset=ISO-8859-1");

        // Gerar relatório no padrão HTML
        JRHtmlExporter jrhtmlexporter = new JRHtmlExporter();

        Map imagesMap = new HashMap();
        request.getSession().setAttribute("IMAGES_MAP", imagesMap);
        jrhtmlexporter.setParameter(JRHtmlExporterParameter.IMAGES_MAP, imagesMap);
        jrhtmlexporter.setParameter(JRHtmlExporterParameter.IMAGES_URI, "image?image=");
        jrhtmlexporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
        jrhtmlexporter.setParameter(JRExporterParameter.OUTPUT_WRITER, response.getWriter());
        jrhtmlexporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "ISO-8859-1");
        jrhtmlexporter.exportReport();
    }

    protected void visualizarRelatorioPDF(HttpServletRequest request, HttpServletResponse response, JasperPrint print, Connection con) throws ServletException, IOException, Exception {
        //response.setContentType("application/pdf");

        String chave = (con != null ? DAO.resolveKeyFromConnection(con) : (String) request.getSession().getAttribute("key"));
        if (UteisValidacao.emptyString(chave) &&
                (request.getAttribute("servlet-chave") != null && !UteisValidacao.emptyString(request.getAttribute("servlet-chave").toString()))) {
            chave = request.getAttribute("servlet-chave").toString();
        }

        String nomePDF = request.getAttribute("nomeRelatorio").toString()
                + "-" + chave
                + "-" + String.valueOf(negocio.comuns.utilitarias.Calendario.hoje().getTime())
                + ".pdf";

        boolean servletRelatorio =  (request.getAttribute("servlet-relatorio") != null && request.getAttribute("servlet-relatorio").toString().equalsIgnoreCase("true"));
        String nomeRelPDF = (servletRelatorio ? "servlet-relatorio" : "relatorio") + File.separator + nomePDF;
        File pdfFile = new File(obterCaminhoWebAplicacao(request, response) + File.separator + nomeRelPDF);
        if (pdfFile.exists()) {
            try {
                pdfFile.delete();
            } catch (Exception e) {
                DateFormat formatador = DateFormat.getDateInstance(DateFormat.MEDIUM);
                String dataStr = formatador.format(negocio.comuns.utilitarias.Calendario.hoje());
                nomePDF = nomePDF + dataStr + ".pdf";
                nomeRelPDF = (servletRelatorio ? "servlet-relatorio" : "relatorio") + File.separator + nomePDF;
                pdfFile = new File(obterCaminhoWebAplicacao(request, response) + File.separator + nomeRelPDF);
            }

        }

        JRPdfExporter jrpdfexporter = new JRPdfExporter();
        jrpdfexporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
        jrpdfexporter.setParameter(JRExporterParameter.OUTPUT_FILE, pdfFile);
        //debug qual empresa gerando o relatório
        System.out.println("EXPORTANDO PDF: " + pdfFile.getAbsolutePath());
        jrpdfexporter.exportReport();
        request.setAttribute("nomeArquivoRelatorioGeradoAgora", nomePDF);

    }

    protected void visualizarRelatorioEXCEL(HttpServletRequest request,
            HttpServletResponse response, JasperPrint print, Connection con) throws ServletException,
            IOException, Exception {

        String nomeXLS = request.getAttribute("nomeRelatorio").toString()
                + "-" + (con != null ? DAO.resolveKeyFromConnection(con) : (String) request.getSession().getAttribute("key"))
                + "-" + String.valueOf(negocio.comuns.utilitarias.Calendario.hoje().getTime())
                + ".xls";
        String nomeRelPDF = "relatorio" + File.separator + nomeXLS;
        File xlsFile = new File(obterCaminhoWebAplicacao(request, response) + File.separator + nomeRelPDF);
        if (xlsFile.exists()) {
            try {
                xlsFile.delete();
            } catch (Exception e) {
                DateFormat formatador = DateFormat.getDateInstance(DateFormat.MEDIUM);
                String dataStr = formatador.format(negocio.comuns.utilitarias.Calendario.hoje());
                nomeXLS = nomeXLS + dataStr + ".xls";
                nomeRelPDF = "relatorios" + File.separator + nomeXLS;
                xlsFile = new File(obterCaminhoWebAplicacao(request, response) + File.separator + nomeRelPDF);
            }

        }

        JRXlsxExporter exporter = new JRXlsxExporter();
        exporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, xlsFile.toString());
        exporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET, Boolean.FALSE);
        exporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.TRUE);
        exporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.TRUE);
        exporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
        exporter.setParameter(JRXlsExporterParameter.IS_COLLAPSE_ROW_SPAN, Boolean.TRUE);

        exporter.exportReport();

        request.setAttribute("nomeArquivoRelatorioGeradoAgora", nomeXLS);

    }

    private void inicializarParametrosBasicos(HttpServletRequest request, HttpServletResponse response, Connection con) throws Exception {
        EmpresaVO empresa = new EmpresaVO();
        String nomeEmpresa = (String) request.getAttribute("nomeEmpresa");
        InputStream logo = null;
        if (nomeEmpresa.equals("") || nomeEmpresa.equals(LABEL_TODAS_EMPRESAS)) {
            logo = getImagem(request, response);
        } else {
            if (con != null) {
                Empresa empresaDAO = new Empresa(con);
                empresa = empresaDAO.consultarPorNomeEmpresa(nomeEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                empresa.setFotoRelatorio(empresaDAO.obterFotoPadrao(DAO.resolveKeyFromConnection(con), empresa.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
                empresaDAO = null;
            } else {
                empresa = FacadeManager.getFacade().getEmpresa().consultarPorNomeEmpresa(nomeEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                empresa.setFotoRelatorio(FacadeManager.getFacade().getEmpresa().obterFotoPadrao(
                        DAO.resolveKeyFromConnection(FacadeManager.getFacade().getEmpresa().getCon()),
                        empresa.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
            }
            if (empresa.getFotoRelatorio() == null || empresa.getFotoRelatorio().length == 0) {
                logo = getImagem(request, response);
            } else {
                InputStream fs = new ByteArrayInputStream(empresa.getFotoRelatorio());
                logo = fs;
            }
        }
        ((Map) request.getAttribute("parametrosRelatorio")).put("logoPadraoRelatorio", logo);
        ((Map) request.getAttribute("parametrosRelatorio")).put("empresaVO.cnpj", empresa.getCNPJ());
        ((Map) request.getAttribute("parametrosRelatorio")).put("empresaVO.endereco", empresa.getEndereco() + " - " + empresa.getCidade().getNome() + " - " + empresa.getEstado().getDescricao());
        ((Map) request.getAttribute("parametrosRelatorio")).put("empresaVO.fone", empresa.getTelComercial1());
        ((Map) request.getAttribute("parametrosRelatorio")).put("empresaVO.site", empresa.getSite());
    }

    protected void inicializarParametrosRelatorio(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String tipo = request.getAttribute("tipoRelatorio").toString();
        if ((tipo == null) || tipo.equals("")) {
            request.setAttribute("tipoRelatorio", "HTML");
        }
        //preservar parametros já inicializados
        HashMap parameters = (HashMap) request.getAttribute("parametrosRelatorio");
        if (parameters == null) {
            parameters = new HashMap();
        }
        EmpresaVO empresa = new EmpresaVO();
        String nomeEmpresa = (String) request.getAttribute("nomeEmpresa");
        if (nomeEmpresa.equals("") || nomeEmpresa.equals(LABEL_TODAS_EMPRESAS)) {
            parameters.put("logoPadraoRelatorio", getImagem(request, response));
        } else {
            empresa = FacadeManager.getFacade().getEmpresa().consultarPorNomeEmpresa(nomeEmpresa, false,
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (empresa != null) {
                empresa.setFotoRelatorio(FacadeManager.getFacade().getEmpresa().obterFoto(
                        DAO.resolveKeyFromConnection(FacadeManager.getFacade().getEmpresa().getCon()),
                        empresa.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));

                if (empresa.getFotoRelatorio() == null || empresa.getFotoRelatorio().length == 0) {
                    parameters.put("logoPadraoRelatorio", getImagem(request, response));
                } else {
                    InputStream fs = new ByteArrayInputStream(empresa.getFotoRelatorio());
                    parameters.put("logoPadraoRelatorio", fs);
                }
            }
        }

        parameters.put("pastaPadraoImagens", obterCaminhoWebAplicacao(request, response) + File.separator + "imagens" + File.separator);
        parameters.put("tituloRelatorio", request.getAttribute("tituloRelatorio"));
        parameters.put("usuario", request.getAttribute("nomeUsuario"));
        parameters.put("filtros", "Filtros: " + request.getAttribute("filtros"));
        Double totalPessoas = (Double) request.getAttribute("totalPessoas");
        if (totalPessoas == null) {
            totalPessoas = 0.0;
        }
        parameters.put("totalPessoas", totalPessoas);

        String totalContratos = (String) request.getAttribute("totalContratos");
        if (totalContratos == null) {
            totalContratos = "";
        }
        parameters.put("totalContratos", totalContratos);

        String totalClientes = (String) request.getAttribute("totalClientes");
        if (totalClientes == null) {
            totalClientes = "";
        }
        parameters.put("totalClientes", totalClientes);

        String totalValor = (String) request.getAttribute("totalValor");
        if (totalValor == null) {
            totalValor = "";
        }
        parameters.put("totalValor", totalValor);

        String totalCompetencia = (String) request.getAttribute("totalCompetencia");
        if (totalCompetencia == null) {
            totalCompetencia = "";
        }
        parameters.put("totalCompetencia", totalCompetencia);

        String dataIni = (String) request.getAttribute("dataIni");
        if (dataIni == null) {
            dataIni = "";
        }
        parameters.put("dataIni", dataIni);
        String dataFim = (String) request.getAttribute("dataFim");
        if (dataFim == null) {
            dataFim = "";
        }
        parameters.put("dataFim", dataFim);

        String qtdAV = (String) request.getAttribute("qtdAV");
        if (qtdAV == null) {
            qtdAV = "";
        }
        parameters.put("qtdAV", qtdAV);

        String qtdBB = (String) request.getAttribute("qtdBB");
        if (qtdBB == null) {
            qtdBB = "";
        }
        parameters.put("qtdBB", qtdBB);

        String qtdCA = (String) request.getAttribute("qtdCA");
        if (qtdCA == null) {
            qtdCA = "";
        }
        parameters.put("qtdCA", qtdCA);

        String qtdCD = (String) request.getAttribute("qtdCD");
        if (qtdCD == null) {
            qtdCD = "";
        }
        parameters.put("qtdCD", qtdCD);

        String qtdChequeAV = (String) request.getAttribute("qtdChequeAV");
        if (qtdChequeAV == null) {
            qtdChequeAV = "";
        }
        parameters.put("qtdChequeAV", qtdChequeAV);

        String qtdChequePR = (String) request.getAttribute("qtdChequePR");
        if (qtdChequePR == null) {
            qtdChequePR = "";
        }
        parameters.put("qtdChequePR", qtdChequePR);

        String qtdOutro = (String) request.getAttribute("qtdOutro");
        if (qtdOutro == null) {
            qtdOutro = "";
        }
        parameters.put("qtdOutro", qtdOutro);

        String qtdDV = (String) request.getAttribute("qtdDV");
        if (qtdDV == null) {
            qtdDV = "";
        }
        parameters.put("qtdDV", qtdDV);

        String qtdDR = (String) request.getAttribute("qtdDR");
        if (qtdDR == null) {
            qtdDR = "";
        }
        parameters.put("qtdDR", qtdDR);

        Double valorAV = (Double) request.getAttribute("valorAV");
        if (valorAV == null) {
            valorAV = 0.0;
        }
        parameters.put("valorAV", valorAV);

        Double valorBB = (Double) request.getAttribute("valorBB");
        if (valorBB == null) {
            valorBB = 0.0;
        }
        parameters.put("valorBB", valorBB);

        Double valorCA = (Double) request.getAttribute("valorCA");
        if (valorCA == null) {
            valorCA = 0.0;
        }
        parameters.put("valorCA", valorCA);

        Double valorCD = (Double) request.getAttribute("valorCD");
        if (valorCD == null) {
            valorCD = 0.0;
        }
        parameters.put("valorCD", valorCD);

        Double valorChequeAV = (Double) request.getAttribute("valorChequeAV");
        if (valorChequeAV == null) {
            valorChequeAV = 0.0;
        }
        parameters.put("valorChequeAV", valorChequeAV);

        Double valorDV = (Double) request.getAttribute("valorDV");
        if (valorDV == null) {
            valorDV = 0.0;
        }
        parameters.put("valorDV", valorDV);

        Double valorDR = (Double) request.getAttribute("valorDR");
        if (valorDR == null) {
            valorDR = 0.0;
        }
        parameters.put("valorDR", valorDR);


        Double valorChequePR = (Double) request.getAttribute("valorChequePR");
        if (valorChequePR == null) {
            valorChequePR = 0.0;
        }
        parameters.put("valorChequePR", valorChequePR);

        Double valorOutro = (Double) request.getAttribute("valorOutro");
        if (valorOutro == null) {
            valorOutro = 0.0;
        }
        parameters.put("valorOutro", valorOutro);

        String enderecoEmpresa = (String) request.getAttribute("enderecoEmpresa");
        if (enderecoEmpresa == null) {
            enderecoEmpresa = "";
        }
        parameters.put("enderecoEmpresa", enderecoEmpresa);
        String cidadeEmpresa = (String) request.getAttribute("cidadeEmpresa");
        if (cidadeEmpresa == null) {
            cidadeEmpresa = "";
        }
        parameters.put("cidadeEmpresa", cidadeEmpresa);
        String SUBREPORT_DIR = (String) request.getAttribute("SUBREPORT_DIR");
        if (SUBREPORT_DIR == null) {
            SUBREPORT_DIR = "";
        }
        parameters.put("SUBREPORT_DIR", obterCaminhoBaseAplicacao(request, response) + File.separator + SUBREPORT_DIR);

        String SUBREPORT_DIR1 = (String) request.getAttribute("SUBREPORT_DIR1");
        if (SUBREPORT_DIR1 == null) {
            SUBREPORT_DIR1 = "";
        }
        parameters.put("SUBREPORT_DIR1", obterCaminhoBaseAplicacao(request, response) + File.separator + SUBREPORT_DIR1);

        String SUBREPORT_DIR2 = (String) request.getAttribute("SUBREPORT_DIR2");
        if (SUBREPORT_DIR2 == null) {
            SUBREPORT_DIR2 = "";
        }
        parameters.put("SUBREPORT_DIR2", obterCaminhoBaseAplicacao(request, response) + File.separator + SUBREPORT_DIR2);

        parameters.put("nomeEmpresa", (String) request.getAttribute("nomeEmpresa"));
        parameters.put("mensagemRel", (String) request.getAttribute("mensagemRel"));
        parameters.put("versaoSoftware", PropsService.getPropertyValue(PropsService.VERSAO_SISTEMA));

        String parametro1 = (String) request.getAttribute("parametro1");
        if (parametro1 == null) {
            parametro1 = "";
        }
        parameters.put("parametro1", parametro1);

        String parametro2 = (String) request.getAttribute("parametro2");
        if (parametro2 == null) {
            parametro2 = "";
        }
        parameters.put("parametro2", parametro2);

        String parametro3 = (String) request.getAttribute("parametro3");
        if (parametro3 == null) {
            parametro3 = "";
        }
        parameters.put("parametro3", parametro3);

        String parametro4 = (String) request.getAttribute("parametro4");
        if (parametro4 == null) {
            parametro4 = "";
        }
        parameters.put("parametro4", parametro4);

        String parametro5 = (String) request.getAttribute("parametro5");
        if (parametro5 == null) {
            parametro5 = "";
        }
        parameters.put("parametro5", parametro5);

        String parametro6 = (String) request.getAttribute("parametro6");
        if (parametro6 == null) {
            parametro6 = "";
        }

        parameters.put("parametro6", parametro6);


        String parametro7 = (String) request.getAttribute("parametro7");
        if (parametro7 == null) {
            parametro7 = "";
        }

        parameters.put("parametro7", parametro7);

        Integer totalAcessos = (Integer) request.getAttribute("totalAcessos");
        if (totalAcessos == null) {
            totalAcessos = 0;
        }
        parameters.put("totalAcessos", totalAcessos);
        Double percentualLiberacao = (Double) request.getAttribute("percentualLiberacao");
        if (percentualLiberacao == null) {
            percentualLiberacao = 0.0;
        }
        parameters.put("percentualLiberacao", percentualLiberacao);
        Double percentualCliCol = (Double) request.getAttribute("percentualCliCol");
        if (percentualCliCol == null) {
            percentualCliCol = 0.0;
        }
        parameters.put("percentualCliCol", percentualCliCol);

        Integer totalCli = (Integer) request.getAttribute("totalCli");
        if (totalCli == null) {
            totalCli = 0;
        }
        parameters.put("totalCli", totalCli);

        Integer totalCol = (Integer) request.getAttribute("totalCol");
        if (totalCol == null) {
            totalCol = 0;
        }
        parameters.put("totalCol", totalCol);

        Double percentualCli = (Double) request.getAttribute("percentualCli");
        if (percentualCli == null) {
            percentualCli = 0.0;
        }
        parameters.put("percentualCli", percentualCli);
        Double percentualCol = (Double) request.getAttribute("percentualCol");
        if (percentualCol == null) {
            percentualCol = 0.0;
        }
        parameters.put("percentualCol", percentualCol);

        parameters.put("empresaVO.cnpj", empresa.getCNPJ());
        parameters.put("empresaVO.endereco", empresa.getEndereco() + " - " + empresa.getCidade().getNome() + " - " + empresa.getEstado().getDescricao());
        parameters.put("empresaVO.fone", empresa.getTelComercial1());
        parameters.put("empresaVO.site", empresa.getSite());
        parameters.put("dadosTotalPorSituacao", request.getAttribute("dadosTotalPorSituacao"));
        parameters.put("dadosValoresPorSituacao", request.getAttribute("dadosValoresPorSituacao"));
        parameters.put("dadosValoresParcelas", request.getAttribute("dadosValoresParcelas"));
        parameters.put("totalTransacoesCobradas", request.getAttribute("totalTransacoesCobradas"));
        parameters.put("listaFechamento", request.getAttribute("listaFechamento"));
        parameters.put("totalAcessoLib", request.getAttribute("totalAcessoLib"));
        parameters.put("totalJustificadoLib", request.getAttribute("totalJustificadoLib"));
        parameters.put("totalFaltaJustificarLib", request.getAttribute("totalFaltaJustificarLib"));
        parameters.put("qtdBVs", request.getAttribute("qtdBVs"));

        parameters.put("qtdMatriculado", request.getAttribute("qtdMatriculado"));
        parameters.put("qtdRematriculado", request.getAttribute("qtdRematriculado"));
        parameters.put("qtdTrancamento", request.getAttribute("qtdTrancamento"));
        parameters.put("qtdCancelado", request.getAttribute("qtdCancelado"));
        parameters.put("qtdDesistente", request.getAttribute("qtdDesistente"));
        parameters.put("qtdRetornoTrancamento", request.getAttribute("qtdRetornoTrancamento"));
        parameters.put("qtdVencidoMes", request.getAttribute("qtdVencidoMes"));
        parameters.put("qtdTotal", request.getAttribute("qtdTotal"));
        parameters.put("listaTotais", request.getAttribute("listaTotais"));

        parameters.put("tipoRelatorioDF", request.getAttribute("tipoRelatorioDF"));
        parameters.put("totalEntrada", request.getAttribute("totalEntrada"));
        parameters.put("totalSaida", request.getAttribute("totalSaida"));
        parameters.put("resultado", request.getAttribute("resultado"));
        parameters.put("devolucoes", request.getAttribute("devolucoes"));
        parameters.put("apresentarDados", request.getAttribute("apresentarDados"));

        parameters.put("listaCheques", request.getAttribute("listaCheques"));
        parameters.put("listaCartoes", request.getAttribute("listaCartoes"));
        parameters.put("listaCheques2", request.getAttribute("listaCheques"));
        parameters.put("listaCartoes2", request.getAttribute("listaCartoes"));

        parameters.put("tipoOperacao", request.getAttribute("tipoOperacao"));
        parameters.put("exibirCheques", request.getAttribute("exibirCheques"));
        parameters.put("exibirCartoes", request.getAttribute("exibirCartoes"));

        parameters.put("totalizadores", request.getAttribute("totalizadores"));
        parameters.put("somenteSintetico", request.getAttribute("somenteSintetico"));

        parameters.put("totalComissao", request.getAttribute("totalComissao"));
        parameters.put("totalUnitario", request.getAttribute("totalUnitario"));
        parameters.put("totalCliente", request.getAttribute("totalCliente"));

        parameters.put("totalValorPago", request.getAttribute("totalValorPago"));
        parameters.put("totalDescontado", request.getAttribute("totalDescontado"));
        parameters.put("totalCompensado", request.getAttribute("totalCompensado"));
        parameters.put("totalRepasse", request.getAttribute("totalRepasse"));

        parameters.put("diario", request.getAttribute("diario"));
        parameters.put("formas", request.getAttribute("formas"));
        parameters.put("faturamento", request.getAttribute("faturamento"));
        parameters.put("compensacao", request.getAttribute("compensacao"));

        parameters.put("dataAberturaApresentar", request.getAttribute("dataAberturaApresentar"));
        parameters.put("dataFechamentoApresentar", request.getAttribute("dataFechamentoApresentar"));
        parameters.put("operadorAbertura", request.getAttribute("operadorAbertura"));
        parameters.put("codigoCaixa", request.getAttribute("codigoCaixa"));
        parameters.put("operadorFechamento", request.getAttribute("operadorFechamento"));
        parameters.put("totalRecebidos", request.getAttribute("totalRecebidos"));
        parameters.put("totalRecebiveis", request.getAttribute("totalRecebiveis"));
        parameters.put("inicioFat", request.getAttribute("inicioFat"));
        parameters.put("fimFat", request.getAttribute("fimFat"));
        parameters.put("inicioFaturamento", request.getAttribute("inicioFaturamento"));
        parameters.put("fimFaturamento", request.getAttribute("fimFaturamento"));
        parameters.put("inicioCompensacao", request.getAttribute("inicioCompensacao"));
        parameters.put("fimCompensacao", request.getAttribute("fimCompensacao"));
        parameters.put("valorTotalGR", request.getAttribute("valorTotalGR"));

        parameters.put("colNome", request.getAttribute("colNome"));
        parameters.put("colContrato", request.getAttribute("colContrato"));
        parameters.put("colMatricula", request.getAttribute("colMatricula"));
        parameters.put("colSituacao", request.getAttribute("colSituacao"));
        parameters.put("colVinculo", request.getAttribute("colVinculo"));
        parameters.put("colModalidade", request.getAttribute("colModalidade"));
        parameters.put("colDuracao", request.getAttribute("colDuracao"));
        parameters.put("colDataLancamento", request.getAttribute("colDataLancamento"));
        parameters.put("colInicio", request.getAttribute("colInicio"));
        parameters.put("colVence", request.getAttribute("colVence"));
        parameters.put("colHorario", request.getAttribute("colPlano"));
        parameters.put("colPlano", request.getAttribute("colPlano"));
        parameters.put("colHorario", request.getAttribute("colHorario"));
        parameters.put("colValorModalidade", request.getAttribute("colValorModalidade"));
        parameters.put("colFaturamento", request.getAttribute("colFaturamento"));
        parameters.put("dataImpressao", request.getAttribute("dataImpressao"));
        parameters.put("usuarioImpressao", request.getAttribute("usuarioImpressao"));
        
        parameters.put("periodo", request.getAttribute("periodo"));
        parameters.put("valorInicial", request.getAttribute("valorInicial"));
        parameters.put("valorFinal", request.getAttribute("valorFinal"));
        parameters.put("totalEntrada", request.getAttribute("totalEntrada"));
        parameters.put("totalSaida", request.getAttribute("totalSaida"));

        parameters.put("contasSelecionadas", request.getAttribute("contasSelecionadas"));

        parameters.put("apresentarDadosSensiveis", request.getAttribute("apresentarDadosSensiveis"));

        String moeda = (String) request.getAttribute("moeda");
        if (moeda == null) {
            moeda = "";
        }
        parameters.put("moeda", moeda);

        parameters.put(JRParameter.REPORT_LOCALE, request.getAttribute("REPORT_LOCALE"));
        parameters.put(JRParameter.REPORT_RESOURCE_BUNDLE, request.getAttribute("REPORT_RESOURCE_BUNDLE"));

        request.setAttribute("parametrosRelatorio", parameters);
    }

    public void processRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        processRequest(request, response, null);
    }

    public void processRequest(HttpServletRequest request, HttpServletResponse response, Connection con) throws Exception {
        if (request.getAttribute("parametrosRelatorio") == null) {
            //26/01/13 Ulisses.. Inicializar os parâmetros do relatório somente se os mesmos não estiverem definidos.
            inicializarParametrosRelatorio(request, response);
        } else {
            inicializarParametrosBasicos(request, response, con);
        }
        JasperPrint print = null;
        String tipo = (String) request.getAttribute("tipoImplementacao");
        if (tipo == null) {
            tipo = "";
        }

        Map params = (Map) request.getAttribute("parametrosRelatorio");

        if (request.getAttribute("totaljuromulta") != null) {
            params.put("totaljuromulta", request.getAttribute("totaljuromulta"));
        }
        if (request.getAttribute("mostrarcampo") != null) {
            params.put("mostrarcampo", request.getAttribute("mostrarcampo"));
        }
        if (request.getAttribute("tituloRelatorio") != null) {
            params.put("tituloRelatorio", request.getAttribute("tituloRelatorio"));
        }


        JRFileVirtualizer virtualizer = new JRFileVirtualizer(2, this.getClass().getResource("").getPath());
        params.put(JRParameter.REPORT_VIRTUALIZER, virtualizer);
        params.put("considerarCompensacaoOriginal", request.getAttribute("considerarCompensacaoOriginal"));
        long start = System.currentTimeMillis();
        try {
            if (con != null) {
                if (tipo.equals("OBJETO")) {
                    print = gerarRelatorioJasperPrintObjeto(request, response,
                            request.getAttribute("nomeDesignIReport").toString());
                } else {
                    print = gerarRelatorioJasperPrintXML(request, response, request.getAttribute("nomeDesignIReport").toString(), con);
                }
            } else {
                if (tipo.equals("")) {
                    print = gerarRelatorioJasperPrintXML(request, response, request.getAttribute("xmlRelatorio").toString(),
                            request.getAttribute("caminhoParserXML").toString(),
                            request.getAttribute("nomeDesignIReport").toString());
                } else {
                    print = gerarRelatorioJasperPrintObjeto(request, response,
                            request.getAttribute("nomeDesignIReport").toString());
                }
            }

            if (request.getAttribute("tipoRelatorio").equals("PDF")) {
                visualizarRelatorioPDF(request, response, print, con);
            } else if (request.getAttribute("tipoRelatorio").equals("EXCEL")) {
                visualizarRelatorioEXCEL(request, response, print, con);
            } else {
                visualizarRelatorioHTML(request, response, print);
            }
        } finally {
            virtualizer.cleanup();
        }
        System.err.println("Filling time : " + (System.currentTimeMillis() - start));

    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request
     *            servlet request
     * @param response
     *            servlet response
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            processRequest(request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request
     *            servlet request
     * @param response
     *            servlet response
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            processRequest(request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Returns a short description of the servlet.
     */
    @Override
    public String getServletInfo() {
        return "Servlet Genérico que encapsula controle de Visualização de Relatórios";
    }

    public InputStream getImagem(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String caminhoApp = obterCaminhoWebAplicacao(request, response);
        String caminho = caminhoApp + File.separator + "fotos" + File.separator + "logoPadraoRelatorio.jpg";
        File imagem = new File(caminho);
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        byte buffer[] = new byte[4096];
        int bytesRead = 0;
        FileInputStream fi = new FileInputStream(imagem.getAbsolutePath());
        while ((bytesRead = fi.read(buffer)) != -1) {
            arrayOutputStream.write(buffer, 0, bytesRead);
        }
        byte[] a = (arrayOutputStream.toByteArray());
        InputStream fs = new ByteArrayInputStream(a);
        arrayOutputStream.close();
        fi.close();
        return fs;

    }
}

/**
 * 
 */
package relatorio.arquitetura;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;

import negocio.comuns.utilitarias.Uteis;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;

/**
 * <AUTHOR>
 * 
 */
public class GeradorRelatorio  {	

	public JasperPrint gerarRelatorioJasperPrintObjeto(List listaObjetos, String caminhoDesign,Map parametros) throws Exception {
		JRDataSource jr = new JRBeanCollectionDataSource(listaObjetos,false);
		JasperPrint print = JasperFillManager.fillReport(getCaminhoRelatorio() + File.separator + caminhoDesign + ".jasper", parametros, jr);
		return print;
	}

	protected void exportarRelatorioExcel(JasperPrint print, String nomeEntidade) throws ServletException, IOException, Exception {

		File arquivo = new File(Uteis.obterCaminhoWeb() + File.separator + "relatorio" + File.separator + nomeEntidade + ".xlsx");
		if (arquivo.exists()) {
			arquivo.delete();
		}
		JRXlsxExporter jrpdfexporter = new JRXlsxExporter();
		jrpdfexporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, Uteis.obterCaminhoWeb() + File.separator + "relatorio" + File.separator + nomeEntidade + ".xlsx");
		jrpdfexporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
		jrpdfexporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET, Boolean.FALSE);
		jrpdfexporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.TRUE);
		jrpdfexporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.TRUE);
		jrpdfexporter.setParameter(JRXlsExporterParameter.IS_WHITE_PAGE_BACKGROUND, Boolean.FALSE);
		jrpdfexporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
		jrpdfexporter.setParameter(JRXlsExporterParameter.IS_FONT_SIZE_FIX_ENABLED, Boolean.FALSE);
		jrpdfexporter.setParameter(JRXlsExporterParameter.IS_IMAGE_BORDER_FIX_ENABLED, Boolean.FALSE);
		jrpdfexporter.setParameter(JRXlsExporterParameter.MAXIMUM_ROWS_PER_SHEET, 0);
		jrpdfexporter.setParameter(JRXlsExporterParameter.IS_IGNORE_GRAPHICS, Boolean.FALSE);
		jrpdfexporter.setParameter(JRXlsExporterParameter.IS_COLLAPSE_ROW_SPAN, Boolean.FALSE);
		jrpdfexporter.setParameter(JRXlsExporterParameter.IS_IGNORE_CELL_BORDER, Boolean.FALSE);
		jrpdfexporter.setParameter(JRXlsExporterParameter.IS_IGNORE_CELL_BACKGROUND, Boolean.FALSE);
		jrpdfexporter.exportReport();

	}
	

	public void montarRelatorio(String tipoImplementacao, String tipoRelatorio, String nomeEntidadeRelatorio, String caminhoDesign, List listaObjetos, Map parametros) throws ServletException, IOException, Exception {
		JasperPrint print = null;
		if (tipoImplementacao == null) {
			tipoImplementacao = "";
		}
		if (tipoImplementacao.equals("")) {
			//print = gerarRelatorioJasperPrintXML();
		} else {
			print = gerarRelatorioJasperPrintObjeto(listaObjetos, caminhoDesign, parametros);
		}
		if (tipoRelatorio.equals("EXCEL")) {
			exportarRelatorioExcel(print, nomeEntidadeRelatorio);
		}

	}

	public static String getCaminhoRelatorio() {
		return Uteis.obterCaminhoWeb() + File.separator + "WEB-INF" + File.separator + "classes" + File.separator + "relatorio";
	}

	

}

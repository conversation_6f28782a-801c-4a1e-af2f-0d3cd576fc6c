/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.controlecredito.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class ControleCreditoTreinoJSON extends SuperJSON{
    
    private Integer codigo;
    private String data;
    private String operacao;
    private Integer quantidade;
    private Integer saldo;

    public ControleCreditoTreinoJSON() {
    }

    public ControleCreditoTreinoJSON(ControleCreditoTreinoVO obj) {
        this.codigo = obj.getCodigo();
        this.data = Uteis.getDataAplicandoFormatacao(obj.getDataOperacao(), "dd/MM/yyyy HH:mm");
        this.operacao = obj.getTipoOperacaoCreditoTreinoEnum().getDescricaoCurta();
        this.quantidade = obj.getQuantidade();
        this.saldo = obj.getSaldo();
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Integer getSaldo() {
        return saldo;
    }

    public void setSaldo(Integer saldo) {
        this.saldo = saldo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
    
    
    
}

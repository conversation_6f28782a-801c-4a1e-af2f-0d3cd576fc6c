/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum TipoOperacaoContratoEnum {
    
    TRANSFERENCIA_SAIDA("TS", false,"fa-icon-backward"),
    ALTERACAO_DURACAO("AD", false,"fa-icon-dashboard"),
    BONUS_ACRESCIMO("BA", false,"fa-icon-certificate"),
    BONUS_REDUCAO("BR", false,"fa-icon-certificate"),
    CARENCIA("CR", true,"fa-icon-ship"),
    TRANSFERENCIA_ENTRADA("TE", false,"fa-icon-forward"),
    CANCELAMENTO("CA", false,"fa-icon-ban-circle"),
    ALTERACAO_HORARIO("AH", false,"fa-icon-clock-o"),
    TRANCAMENTO("TR", false,"fa-icon-lock"),
    TRANCAMENTO_VENCIDO("TV", false,"fa-icon-unlock-alt"),
    RET<PERSON><PERSON><PERSON>_TRANCAMENTO("RT", false,"fa-icon-unlock"),
    INCLUIR_MODALIDADE("IM", false,"fa-icon-plus"),
    ALTERAR_MODALIDADE("AM", false,"fa-icon-pencil"),
    EXCLUIR_MODALIDADE("EM", false,"fa-icon-minus"),
    ALTERACAO_CONTRATO("AC", false,"fa-icon-cogs"),
    ATESTADO("AT", true,"fa-icon-ambulance"),
    RETORNO_ATESTADO("RA", false,"fa-icon-ambulance"),
    LIBERAR_VAGA("LV", false,"fa-icon-signout"),
    BONUS_COLETIVO("BC", false,"fa-icon-certificate"),
    TRANSFERENCIA_DIREITO_USO("TD", false,"fa-icon-signout"),
    RECUPERACAO_DIREITO_USO("RD", false,"fa-icon-signin"),
    ;
    
    public static TipoOperacaoContratoEnum obterPorSigla(String sigla){
        for(TipoOperacaoContratoEnum to : values()){
            if(to.getSigla().equals(sigla)){
                return to;
            }
        }
        return null;
    }

    private TipoOperacaoContratoEnum(String sigla, boolean periodo, String icone) {
        this.sigla = sigla;
        this.periodo = periodo;
        this.icone = icone;
    }

    private String sigla;
    private String icone;
    private boolean periodo;

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public boolean isPeriodo() {
        return periodo;
    }

    public void setPeriodo(boolean periodo) {
        this.periodo = periodo;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }
    
}

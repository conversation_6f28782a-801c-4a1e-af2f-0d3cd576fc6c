/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum TipoVigenciaConsultaEnum {

    TODOS(1, "Todos"),
    SOMENTE_VIGENTES(2, "Somente vigentes"),
    VENCIDOS(3, "Vencidos");
    private String descricao;
    private Integer codigo;

    private TipoVigenciaConsultaEnum(Integer codigo, String descricao) {
        this.descricao = descricao;
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}

package br.com.pactosolucoes.enumeradores;

/**
 * Created by ulisses on 12/01/2016.
 */
public enum TipoAntecedenciaMarcarAulaEnum {

    NAO_VALIDAR(1, "NÃO VALIDAR", "", "", ""),
    TEMPO_MINIMO_ANTECEDENCIA(2, "QUALQUER DIA", "com até ", "Data base:12/01/16. Se estiver configurado o tempo de 30 minutos e a próxima aula for 18/01/16 10:00, o aluno tem o período de 12/01/16 até 18/01/16 09:30 para marcar a aula.","minutos de antecedência."),
    TEMPO_MAXIMO_ANTECEDENCIA(3, "SOMENTE NO DIA","a partir de ", "Data base:12/01/16. Se estiver configurado o tempo de 30 minutos e a próxima aula for 18/01/16 10:00, o aluno só poderá marcar a aula das 09:30 até as 10:00 do dia 18/01/16", "minutos antes do início da aula.");

    TipoAntecedenciaMarcarAulaEnum(Integer codigo, String descricao, String descricaoLonga, String exemplo,String descFinal){
        this.codigo = codigo;
        this.descricao = descricao;
        this.descricaoLonga = descricaoLonga;
        this.exemplo = exemplo;
        this.descFinal = descFinal;
    }
    private Integer codigo;
    private String descricao;
    private String descricaoLonga;
    private String exemplo;
    private String descFinal;

    public static TipoAntecedenciaMarcarAulaEnum getTipo(Integer codigo){
        if (codigo == null)
            return TipoAntecedenciaMarcarAulaEnum.NAO_VALIDAR;
        for (TipoAntecedenciaMarcarAulaEnum obj: TipoAntecedenciaMarcarAulaEnum.values()){
            if(obj.getCodigo().equals(codigo)){
                return obj;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricaoLonga() {
        return descricaoLonga;
    }

    public void setDescricaoLonga(String descricaoLonga) {
        this.descricaoLonga = descricaoLonga;
    }

    public String getExemplo() {
        return exemplo;
    }

    public void setExemplo(String exemplo) {
        this.exemplo = exemplo;
    }

    public String getDescFinal() {
        return descFinal;
    }

    public void setDescFinal(String descFinal) {
        this.descFinal = descFinal;
    }
}

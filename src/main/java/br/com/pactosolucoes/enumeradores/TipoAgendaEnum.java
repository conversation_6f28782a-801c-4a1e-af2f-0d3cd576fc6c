/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum TipoAgendaEnum {

    LIGACAO("LI", "Ligação"),
    VISITA("VI", "Visita"),
    AULA_EXPERIMENTAL("AE", "Aula Experimental"),
    AULA_DIARIA("DI", "Diária");
    private String id;
    private String descricao;

    private TipoAgendaEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoAgendaEnum getFromId(String id){
        for(TipoAgendaEnum tipo : values()){
            if(tipo.getId().equals(id)){
                return tipo;
            }
        }
        return null;
    }
}

package br.com.pactosolucoes.enumeradores;

/**
 * Enumerador que substitui o uso do pattern de classe estática 'Domínios' para obtenção de tipos de colaboradores.
 * <AUTHOR>
 *
 */
public enum TipoEnderecoEnum {
	
	RESIDENCIAL ("RE", "Residencial"),
	COMERCIAL ("CO", "Comercial");
	
	private String codigo;
	private String descricao;
	
	
	private TipoEnderecoEnum(String codigo, String descricao){
		this.codigo = codigo;
		this.descricao = descricao;
	}
	
	/**
	 * @param descricao the descricao to set
	 */
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}
	/**
	 * @return the descricao
	 */
	public String getDescricao() {
		return descricao;
	}
	/**
	 * @param codigo the codigo to set
	 */
	public void setCodigo(String codigo) {
		this.codigo = codigo;
	}
	/**
	 * @return the codigo
	 */
	public String getCodigo() {
		return codigo;
	}
	
	
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum TipoContatoCRM {

    CONTATO_TELEFONE("TE", "fa-icon-phone"),
    CONTATO_EMAIL("EM", "fa-icon-paper-plane"),
    CONTATO_PESSOAL("PE", "fa-icon-comments"),
    LIGACAO_SEM_CONTATO("LC", "fa-icon-minus-sign"),
    CONTATO_SMS("CS", "fa-icon-mobile-phone"),
    CONTATO_APP("AP", "fa-icon-mobile-phone"),
    CONTATO_WHATSAPP("WA", "fa-icon-mobile-phone"),
    CONTATO_GYMBOT("BT", "fa-icon-mobile-phone"),
    CONTATO_GYMBOT_PRO("GP", "fa-icon-mobile-phone"),
    ;

    private String sigla;
    private String icone;

    private TipoContatoCRM(String sigla, String icone) {
        this.sigla = sigla;
        this.icone = icone;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public static TipoContatoCRM obterPorSigla(String sigla) {
        for (TipoContatoCRM to : values()) {
            if (to.getSigla().equals(sigla)) {
                return to;
            }
        }
        return null;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }
}

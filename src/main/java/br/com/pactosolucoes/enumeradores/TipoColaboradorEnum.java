package br.com.pactosolucoes.enumeradores;

/**
 * Enumerador que substitui o uso do pattern de classe estática 'Domínios' para obtenção de tipos de colaboradores.
 *
 * <AUTHOR>
 */
public enum TipoColaboradorEnum {

    PROFESSOR("<PERSON>", "Professor"),
    PROFESSOR_TREINO("TW", "Professor (TreinoWeb)"),
    PERSONAL_TRAINER("PT", "Personal Trainer"),
    ORIENTADOR("OR", "Orientador"),
    CONSULTOR("CO", "Consultor"),
    PERSONAL_INTERNO("PI", "Personal Interno"),
    PERSONAL_EXTERNO("PE", "Personal Externo"),
    TERCEIRIZADO("TE", "Terceirizado"),
    ESTUDIO("ES", "Estúdio"),
    FORNECEDOR("FO", "Fornecedor"),
    COORDENADOR("CR", "Coordenador"),
    MEDICO("MD", "Médico"),
    FUNCIONARIO("FC", "Funcionário"),
    ADMINISTRADOR("AD", "Administrador");
    
    private String sigla;
    private String descricao;


    private TipoColaboradorEnum(String sigla, String descricao) {
        this.sigla = sigla;
        this.descricao = descricao;
    }

    /**
     * <AUTHOR>
     * 08/11/2011
     */
    public static TipoColaboradorEnum getTipo(String sigla) {
        TipoColaboradorEnum tipo = null;
        for (TipoColaboradorEnum tipoColaborador : TipoColaboradorEnum.values()) {
            if (tipoColaborador.getSigla().equals(sigla)) {
                tipo = tipoColaborador;
                break;
            }
        }
        return tipo;
    }

    public static String getTiposSQL(boolean comAspas, TipoColaboradorEnum... tipoColaboradorEnums) {
        StringBuilder sb = new StringBuilder();
        for (TipoColaboradorEnum tipo : tipoColaboradorEnums) {
            if (comAspas) {
                sb.append("'");
            }
            sb.append(tipo.getSigla());
            if (comAspas) {
                sb.append("'");
            }
            sb.append(",");
        }
        if (sb.length() > 2) {
            sb.deleteCharAt(0);
            sb.deleteCharAt(sb.length() - 1);
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }
}
package br.com.pactosolucoes.enumeradores;

public enum TipoContratoEnum {
	ESPONTANEO(1, "Espontâneo"), AGENDADO(2,"Agendado");
	
	private int codigo;
	private String descricao;
	
	private TipoContratoEnum(int codigo, String descricao){
		this.setCodigo(codigo);
		this.setDescricao(descricao);
	}

	public static TipoContratoEnum getTipo(int consulta){
		for(TipoContratoEnum origem : TipoContratoEnum.values()){
			if(origem.getCodigo() == consulta){
				return origem;
			}
		}
		return null;
	}
	
	public void setCodigo(int codigo) {
		this.codigo = codigo;
	}

	public int getCodigo() {
		return codigo;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public String getDescricao() {
		return descricao;
	}
	
	
}

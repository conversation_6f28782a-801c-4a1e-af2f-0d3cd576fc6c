/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 * <AUTHOR>
 */
public enum CampoBIEnum {

    ATIVOS_VENCIDOS_INICIO_MES(BIEnum.TICKET_MEDIO, 1),
    ATIVOS_VENCIDOS_FIM_MES(BIEnum.TICKET_MEDIO, 2),
    VENCIDOS_FIM_MES(BIEnum.TICKET_MEDIO, 3),
    CAIXA_POR_RECEITA(BIEnum.TICKET_MEDIO, 4),
    BOLSAS_MES(BIEnum.TICKET_MEDIO, 5),
    CAIXA_POR_COMPETENCIA(BIEnum.TICKET_MEDIO, 6),
    CAIXA_POR_FATURAMENTO(BIEnum.TICKET_MEDIO, 7),
    ATIVOS(BIEnum.TICKET_MEDIO, 8),
    DEPENDENTES_INICIO_MES(BIEnum.TICKET_MEDIO, 9),
    DEPENDENTES_FINAL_MES(BIEnum.TICKET_MEDIO, 10),
    ;

    private final BIEnum bi;
    private final Integer codigo;

    CampoBIEnum(BIEnum bi, Integer codigo) {
        this.bi = bi;
        this.codigo = codigo;
    }

    public static CampoBIEnum getFromCodigo(Integer cod) {
        for (CampoBIEnum campo : CampoBIEnum.values()) {
            if (cod != null && campo.getCodigo().equals(cod)) {
                return campo;
            }
        }
        return null;

    }

    public BIEnum getBi() {
        return bi;
    }

    public Integer getCodigo() {
        return codigo;
    }

}

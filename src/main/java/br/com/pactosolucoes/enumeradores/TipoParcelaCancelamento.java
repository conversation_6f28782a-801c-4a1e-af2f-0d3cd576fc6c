package br.com.pactosolucoes.enumeradores;

import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created by <PERSON> on 08/09/2016.
 */
public enum TipoParcelaCancelamento {
    TODAS_PARCELAS("TP","Todas parcelas"),
    MAIOR_IGUAL_MES_ATUAL("MI","Parcelas maior igual mês atual"),
    MAIOR_IGUAL("MA","Parcelas maior que a data cancelamento"),
    CANCELAMENTO_POR_TRANSFERENCIA("TR", "Cancelamento por transferência do aluno entre empresas"), // Veja https://app.assembla.com/spaces/plataforma-zw/tickets/realtime_cardwall?ticket=12368
    CANCELAMENTO_POR_MUDANCA_PLANO("TR", "Cancelamento por mudança de plano"); // Veja: https://app.assembla.com/spaces/plataforma-zw/tickets/realtime_cardwall?ticket=12756&tab=activity

    private String sigla;
    private String descricao;

    TipoParcelaCancelamento(String sigla, String descricao) {
        this.sigla = sigla;
        this.descricao = descricao;
    }

    TipoParcelaCancelamento(String sigla) {
        this.sigla = sigla;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoParcelaCancelamento obterEnumPorSigla(String sigla){
        if(UteisValidacao.emptyString(sigla)){
            return null;
        }
        for(TipoParcelaCancelamento obj : values()){
            if(obj.getSigla().equals(sigla)){
                return obj;
            }
        }
        return null;
    }
}

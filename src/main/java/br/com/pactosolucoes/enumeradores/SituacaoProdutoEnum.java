package br.com.pactosolucoes.enumeradores;

public enum SituacaoProdutoEnum {

    PAGO("PG", "Pago"),
    EM_ABERTO("EA", "Em aberto"),
    CANCELADO("CA", "Cancelado");

    private String codigo;
    private String descricao;

    SituacaoProdutoEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }


    public static SituacaoProdutoEnum getSituacaoParcela(final String codigo) {
        SituacaoProdutoEnum situacao = null;
        for (SituacaoProdutoEnum sit : SituacaoProdutoEnum.values()) {
            if (sit.getCodigo().equals(codigo)) {
                situacao = sit;
            }
        }
        return situacao;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }


    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

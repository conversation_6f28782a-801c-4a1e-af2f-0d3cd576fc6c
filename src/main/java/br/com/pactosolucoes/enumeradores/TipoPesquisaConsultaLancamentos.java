/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;
/**
 * Enumerador usado na tela de consulta de lançamentos da combo de tipo de pesquisa
 * <AUTHOR>
 */
public enum TipoPesquisaConsultaLancamentos {

    PERIODO(1,"Período"),

    PESQUISAPADRAO(2,"Pesquisa Padrão");

    private Integer codigo;
    private String descricao;

    private TipoPesquisaConsultaLancamentos(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return descricao;
    }

    /**
     * @param descricao the descricao to set
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoPesquisaConsultaLancamentos getTipoPesquisaConsultaLancamentos(final Integer codigo) {
		TipoPesquisaConsultaLancamentos obj = null;
		for (TipoPesquisaConsultaLancamentos status : TipoPesquisaConsultaLancamentos.values()) {
			if (status.getCodigo()== codigo) {
				obj = status;
			}
		}
		return obj;
	}
}

package br.com.pactosolucoes.enumeradores;

/**
 * Created by glauco on 19/05/2014.
 */
public enum SituacaoRenovacaoEnum {


    NaoRenovadosAVencer("RA", "Não Renovados a Vencer"),
    NaoRenovadosDesistentes("RD", "Não Renovados Desistentes"),
    RenovacaoAtrasada("RT", "Renovação Atrasada"),
    RenovacaoNoDia("ND", "Renovação do Dia"),
    NaoRenovadosVencidos("RV", "Não Renovados Vencidos"),
    NaoRenovadosCancelados("CA", "Não Renovados Cancelado"),
    NaoRenovadosTrancados("TR", "Não Renovados Trancado"),
    RenovacaoAntecipada("AN", "Renovação Antecipada");

    private String descricao;
    private String sigla;

    private SituacaoRenovacaoEnum(String sigla, String descricao) {
        this.sigla = sigla;
        this.descricao = descricao;
    }

    public static SituacaoRenovacaoEnum getSituacaoPorSigla(String sigla) {
        for (SituacaoRenovacaoEnum situacaoRenovacaoEnum : SituacaoRenovacaoEnum.values()) {
            if (situacaoRenovacaoEnum.getSigla().equals(sigla)) {
                return situacaoRenovacaoEnum;
            }
        }
        return null;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }
}

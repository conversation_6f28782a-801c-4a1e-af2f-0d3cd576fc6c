package br.com.pactosolucoes.enumeradores;

public enum GrupoTipoEnum {

    FAMILIA("FA", "Em Familia"),
    GRUPO("GR", "Em grupo");

    private final String codigo;
    private final String descricao;

    GrupoTipoEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}

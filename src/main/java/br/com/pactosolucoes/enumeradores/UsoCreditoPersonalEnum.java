/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum UsoCreditoPersonalEnum {
    SOMENTE_PRE_PAGO(1,"Somente pré-pago"),
    PERMITIR_POS_PAGO(2,"Permitir pós-pago");
    
    public Integer codigo;
    public String nome;

    private UsoCreditoPersonalEnum(Integer codigo, String nome) {
        this.codigo = codigo;
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
    
    
}

package br.com.pactosolucoes.enumeradores;

/**
 * Created by ulisses on 26/05/2016.
 */
public enum TipoPremioCupomDescontoEnum {

    MENSALIDADE(1, "MENSALIDADE");

    TipoPremioCupomDescontoEnum(int codigo, String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }


    private int codigo;
    private String descricao;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoPremioCupomDescontoEnum getTipo(int codigo) {
        for (TipoPremioCupomDescontoEnum tipo : values()) {
            if (tipo.getCodigo() == codigo){
                return tipo;
            }
        }
        return null;
    }

}

 /*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 * Enumerador que representa o filtro do Relatório de Comissão para Professor do Estúdio
 * <AUTHOR>
 */
public enum SituacaoParcelaEnum {
    //apresenta todos os agendamentos que foram pagos
    PAGO("PG", "Pago"),
    //todas os agendamentos nao pagos a receber e nao pagos a faturar
    NAOPAGO("NP", "Não Pago"),//todas os agendamentos que não possuem venda e que não foram pagos
    NAOPAGO_AFATURAR("NPF", "Não Pago a Faturar"),
    //todos os agendamentos que possuem venda mas não pagou ainda.
    NAOPAGO_ARECEBER("NPR", "Não Pago a Receber"),
    //todos os agendamentos que possuem status Falta Sem Débito.
    NAOPAGO_SEMFATURAMENTO("SF", "Sem Faturamento");
    private String codigo;
    private String descricao;

    private SituacaoParcelaEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    /**
     * Busca o código do enumerador e retorna o enumerador
     *
     * @param codigo
     * @return situacao
     */
    public static SituacaoParcelaEnum getSituacaoParcela(final String codigo) {
        SituacaoParcelaEnum situacao = null;
        for (SituacaoParcelaEnum sit : SituacaoParcelaEnum.values()) {
            if (sit.getCodigo().equals(codigo)) {
                situacao = sit;
            }
        }
        return situacao;
    }

    /**
     * @return the codigo
     */
    public String getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return descricao;
    }

    /**
     * @param descricao the descricao to set
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

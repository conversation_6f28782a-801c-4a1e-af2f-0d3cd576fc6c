/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;
/**
 * Enumerador usado na tela de consulta de lançamentos da combo de tipo de pesquisa
 * <AUTHOR>
 */
public enum TipoDataConsultaLancamentos {

    DATA_LANCAMENTO(1,"Data de Lançamento"),
    DATA_QUITACAO(2,"Data de Quitação"),
    DATA_VENCIMENTO(3,"Data de Vencimento"),
    DATA_COMPETENCIA(4,"Data de Competência");

    private Integer codigo;
    private String descricao;

    private TipoDataConsultaLancamentos(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return descricao;
    }

    /**
     * @param descricao the descricao to set
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoDataConsultaLancamentos getTipoDataConsultaLancamentos(final Integer codigo) {
		TipoDataConsultaLancamentos obj = null;
		for (TipoDataConsultaLancamentos status : TipoDataConsultaLancamentos.values()) {
			if (status.getCodigo()== codigo) {
				obj = status;
			}
		}
		return obj;
	}
}

package br.com.pactosolucoes.enumeradores;

import java.util.ArrayList;
import java.util.List;

public enum FiltroMailingEnum {
	CATEGORIA("Categoria"),
	SITUACAO("Situação", TipoEventoEnum.EVENTO_ANIVERSARIANTES),
	VINCULO("Vínculo de colaborador"), 
	MODALIDADE("Modalidade"),
	DURACAO("Duração"),
	EVENTO("Evento");
	
	public boolean getContem(TipoEventoEnum evento){
		return eventos.contains(evento);
	}
	private FiltroMailingEnum(String descricao, TipoEventoEnum ... tipoEvento ){
		for(TipoEventoEnum t : tipoEvento){
			this.eventos.add(t);
		}
		this.descricao = descricao;
	}
	
	private List<TipoEventoEnum> eventos = new ArrayList<TipoEventoEnum>();
	private String descricao;
	
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}
	public String getDescricao() {
		return descricao;
	}
	public void setEventos(List<TipoEventoEnum> eventos) {
		this.eventos = eventos;
	}
	public List<TipoEventoEnum> getEventos() {
		return eventos;
	}
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum SituacaoItemExtratoEnum {
    OK("#077113"), //Verde
    PENDENCIAS("#FFFF00"), //Amarelo
    AUTORIZACAO_NAO_EXISTE("#A90102"), //Vermelho
    ESTORNADO_SISTEMA("#0f0000"), //Preto
    ESTORNADO_OPERADORA("#ff6e1e"); //Laranja

    private String corLinha;

    private SituacaoItemExtratoEnum(String corLinha) {
        this.corLinha = corLinha;
    }
    
    public String getCorLinha() {
        return corLinha;
    }

    public void setCorLinha(String corLinha) {
        this.corLinha = corLinha;
    }
    
    public static SituacaoItemExtratoEnum getFromOrdinal(int i){
        for(SituacaoItemExtratoEnum s : values()){
            if(s.ordinal() == i){
                return s;
            }
        }
        return null;
    }
}

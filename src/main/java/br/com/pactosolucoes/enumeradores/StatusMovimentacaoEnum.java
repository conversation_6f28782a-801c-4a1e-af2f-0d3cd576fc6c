/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum StatusMovimentacaoEnum {
    
    TODOS(1, "Todos os recebíveis"),
    MOVIMENTADOS(2, "Apenas movimentados"),
    NAO_MOVIMENTADOS(3,"Não movimentados");
    
    private int codigo;
    private String label;
    
    private StatusMovimentacaoEnum(int i, String s){
        codigo = i;
        label = s;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
    
    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }
    
    
}

package br.com.pactosolucoes.enumeradores;

/**
 * Created by luiz on 14/04/2016.
 */
public enum ProcessoAjusteGeralEnum {

    EXCLUIR_NOTA_FISCAL(0, "Exclusão de NFSe"),
    REMESSA_CANCELAMENTO(1, "Remessa de Cancelamento"),
    REIMPRESSAO_CUPOM_FISCAL(7, "Reimpressão de cupom fiscal"),
    EXCLUIR_NFCe(8, "Exclusão de NFCe"),
    EXCLUIR_ACESSO_FUTURO(9,"Exclusão de Acesso Futuro"),
    EXCLUIR_NOTA_FISCAL_ENOTAS(10,"Exclusão de Nota Fiscal eNotas"),
    CANCELAR_TRANSACAO(11,"Cancelar transação"),
    EXCLUIR_REMESSA(12,"Excluir remessa"),
    COBRANCA_AUTOMATICA_BLOQUEAR(13,"Bloquear cobrança automática cliente"),
    COBRANCA_AUTOMATICA_DESBLOQUEAR(14,"Desbloquear cobrança automática cliente"),
    ALTERAR_TIPO_A_COBRAR(15,"Tipo a cobrar autorização cobrança cliente"),
    EXCLUIR_NOTA_FAMILIA(16, "Exclusão de Nota em Grupo"),
    ESTORNAR_RECIBO(17, "Estorno de recibos em massa")
    ;

    private Integer codigo;
    private String descricao;

    ProcessoAjusteGeralEnum(final Integer codigo, final String descricao) {
        this.setCodigo(codigo);
        this.setDescricao(descricao);
    }

    public static ProcessoAjusteGeralEnum getConsultarPorCodigo(Integer codigo) {
        for (ProcessoAjusteGeralEnum ajuste : values()) {
            if (ajuste.getCodigo().equals(codigo)) {
                return ajuste;
            }
        }
        return null;
    }

    public static String getConsultarDescricaoPorCodigo(Integer codigo) {
        for (ProcessoAjusteGeralEnum ajuste : values()) {
            if (ajuste.getCodigo().equals(codigo)) {
                return ajuste.getDescricao();
            }
        }
        return "";
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

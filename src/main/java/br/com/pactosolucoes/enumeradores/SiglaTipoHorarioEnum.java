/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.enumeradores;

import java.util.ArrayList;
import java.util.List;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public enum SiglaTipoHorarioEnum {

    AULA("A", "Aula"),
    AVALIACAO("AV", "Avaliação"),
    EXPERIMENTAL("EX", "Experimental"),
    SESSAO("SS","Sessão");
    private String sigla;
    private String descricao;

    private SiglaTipoHorarioEnum(String sigla, String descricao) {
        this.sigla = sigla;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public static String getDescricao(String sigla) {
        for (SiglaTipoHorarioEnum enums : SiglaTipoHorarioEnum.values()) {
            if (enums.getSigla().equals(sigla)) {
                return enums.getDescricao();
            }
        }
        return null;
    }

    public static SiglaTipoHorarioEnum get(String sigla) {
        for (SiglaTipoHorarioEnum enums : SiglaTipoHorarioEnum.values()) {
            if (enums.getSigla().equals(sigla)) {
                return enums;
            }
        }
        return null;
    }
    
    public static List getSelectListSiglaTipoHorario() {
        List temp = new ArrayList<SiglaTipoHorarioEnum>();
        for (int i = 0; i < SiglaTipoHorarioEnum.values().length; i++) {
            SiglaTipoHorarioEnum obj = SiglaTipoHorarioEnum.values()[i];
            temp.add(new SelectItem(obj, obj.getSigla() + " - " + obj.getDescricao()));
        }
        return temp;
    }

    @Override
    public String toString(){
        return descricao;
    }
}

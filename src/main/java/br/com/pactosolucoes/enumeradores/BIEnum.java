/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public enum BIEnum {
    PENDENCIA(2, 0, true, 0, "/v2-pendencias"),
    CONVERSAO_VENDAS(5, 1, true, 2, "/v2-conversao-venda"),
    CONVERSAO_VENDAS_SS(6, 2, true, 3, null),
    METAS_FINANCEIRAS(8, 3, true, 5, "/v2-meta-financeira"),
    TICKET_MEDIO(1, 4, true, 7, "/v2-ticket-medio"),
    GRUPO_RISCO(3, 5, true, 1, "/v2-grupo-risco"),
    INDICE_RENOVACAO(4, 6, true, 4, null),
    ROTATIVIDADE_CONTRATO(7, 7, true, 6, "/v2-rotatividade-contrato"),
    <PERSON><PERSON>(9, 8, false, 8, "/v2-recorrencia"),
    CONTROLE_OPERACOES(10, 9, true, 9, "/v2-operacao-excecao"),
    CONVITE_BI(11, 10, true, 10, null),
    CLIENTES_VERIFICADOS(12, 11, true, 11, "/v2-clientes-verificados"),
    AULA_EXPERIMENTAL(13, 12, true, 12, "/v2-aula-experimental"),
    GESTAO_ACESSO(14, 13, true, 13, "/v2-gestao-acesso"),
    INADIMPLENCIA(15, 14, false, 14, "/v2-inadimplencia"),
    PROBABILIDADE_EVASAO(16, 15, true, 15, null),
    LTV(17, 16, false, 16, "/v2-ltv"),
    GYM_PASS(18, 17, false, 17, "/v2-gym-pass"),
    ;


    Integer codigo;
    Integer indice;
    Integer ordemOriginal;
    String endpointMS;
    boolean cachePorColaborador;


    BIEnum(Integer codigo, Integer indice, boolean cachePorColaborador, Integer ordemOriginal, String endpointMS) {
        this.codigo = codigo;
        this.endpointMS = endpointMS;
        this.indice = indice;
        this.ordemOriginal = ordemOriginal;
        this.cachePorColaborador = cachePorColaborador;
    }

    public static BIEnum getFromCodigo(Integer cod) {
        for (BIEnum cfg : BIEnum.values()) {
            if (cod != null && cfg.getCodigo().equals(cod)) {
                return cfg;
            }
        }
        return null;

    }

    public static BIEnum getFromIndice(Integer idx) {
        for (BIEnum cfg : BIEnum.values()) {
            if (idx != null && cfg.getIndice().equals(idx)) {
                return cfg;
            }
        }
        return null;

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getIndice() {
        return indice;
    }

    public void setIndice(Integer indice) {
        this.indice = indice;
    }

    public boolean isCachePorColaborador() {
        return cachePorColaborador;
    }

    public void setCachePorColaborador(boolean cachePorColaborador) {
        this.cachePorColaborador = cachePorColaborador;
}

    public Integer getOrdemOriginal() {
        return ordemOriginal;
    }

    public static List<Integer> obterTodosIndices(){
        List<Integer> indices = new ArrayList<Integer>();
        for(BIEnum biEnum: BIEnum.values()){
            indices.add(biEnum.getIndice());
        }
        return indices;
    }

    public String getEndpointMS() {
        return endpointMS;
    }
}

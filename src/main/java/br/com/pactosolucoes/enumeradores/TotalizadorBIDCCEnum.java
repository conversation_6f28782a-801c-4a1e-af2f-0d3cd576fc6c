/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/*
 * <AUTHOR>
 */
public enum TotalizadorBIDCCEnum {

    ENVIADAS(1, "Enviadas"),
    PAGAS_PELO_CONVENIO(2, "Pagas pelo convênio"),
    PAGAS_FORA_CONVENIO(3, "Pagas fora do convênio"),
    NAO_PAGAS(4, "Não pagas até hoje"),
    AGUARDANDO_RETORNO(5, "Aguardando retorno"),
    PAGAS_PRIMEIRA_TENTATIVA(6, "Pagas na primeira tentativa"),
    PAGAS_SEGUNDA_TENTATIVA(7, "Pagas na segunda tentativa"),
    PAGAS_DEMAIS_TENTATIVAS(8, "Pagas nas demais tentativas");

    Integer codigo;
    String descricao;

    private TotalizadorBIDCCEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TotalizadorBIDCCEnum getFromCodigo(Integer cod) {
        for (TotalizadorBIDCCEnum cfg : TotalizadorBIDCCEnum.values()) {
            if (cfg.getCodigo().equals(cod)) {
                return cfg;
            }
        }
        return null;
    }


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }




}

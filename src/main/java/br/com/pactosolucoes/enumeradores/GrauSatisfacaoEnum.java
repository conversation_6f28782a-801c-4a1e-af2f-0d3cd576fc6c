package br.com.pactosolucoes.enumeradores;

/**
 * <AUTHOR>
 *
 */
public enum GrauSatisfacaoEnum {
	
	MUITO_SATISFEITO("MF","Muito Satisfeito"), 
	SATISFEITO("SA","Satisfeito"), 
	INSATISFEITO("IN","Insatisfeito");
	
	private String codigo;
	private String descricao;
	
	private GrauSatisfacaoEnum(String codigo, String descricao){
		this.setCodigo(codigo);
		this.setDescricao(descricao);
	}
	
	public static GrauSatisfacaoEnum obterGrauSatisfacao(String codigo){
		GrauSatisfacaoEnum grau = null;
		for(GrauSatisfacaoEnum grauSatisfacao : GrauSatisfacaoEnum.values()){
			if(codigo.equals(grauSatisfacao.getCodigo())){
				grau = grauSatisfacao;
			}
		}
		return grau;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public String getDescricao() {
		return descricao;
	}

	public void setCodigo(String codigo) {
		this.codigo = codigo;
	}

	public String getCodigo() {
		return codigo;
	}
}

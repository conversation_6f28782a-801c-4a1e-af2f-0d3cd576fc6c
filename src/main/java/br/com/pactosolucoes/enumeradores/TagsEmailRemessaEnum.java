package br.com.pactosolucoes.enumeradores;

import negocio.comuns.plano.MarcadorVO;

import java.util.ArrayList;
import java.util.List;

public enum TagsEmailRemessaEnum {

    TAG_PARCELAS_COBRANCA(1,"Descrição das Parcelas em Aberto", "TAG_PARCELAS_COBRANCA"),
    TAG_PARCELA_VALOR(2, "Valor Parcela", "TAG_PARCELA_VALOR"),
    TAG_CARTAO_MASCARADO(3, "Cartão Mascarado", "TAG_CARTAO_MASCARADO"),
    TAG_CODIGO_RETORNO(4, "Cod. Retorno", "TAG_CODIGO_RETORNO"),
    TAG_DESCRICAO_RETORNO(5, "Descrição Retorno", "TAG_DESCRICAO_RETORNO"),
    TAG_ACAO_REALIZAR_RETORNO(6, "Ação para o Retorno", "TAG_ACAO_REALIZAR_RETORNO"),
    TAG_DATA_RETORNO(7, "Dt. <PERSON>", "TAG_DATA_RETORNO");

    private Integer codigo;
    private String descricao;
    private String tag;

    private TagsEmailRemessaEnum(Integer codigo, String descricao, String tag) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.tag = tag;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public static List<MarcadorVO> getListaTagsEmailRemessa() {
        List<MarcadorVO> objs = new ArrayList<MarcadorVO>();
        MarcadorVO marcador = new MarcadorVO();
        for (TagsEmailRemessaEnum tag : TagsEmailRemessaEnum.values()) {
            marcador.setTag(tag.getTag());
            marcador.setNome(tag.getDescricao());
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        return objs;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 * Enumerador usado em Lançamentos de Recebimento e Pagamento - FINANCEIRO
 * Campo de MovContaVO - TipoOperacaoLancamento
 *
 * <AUTHOR>
 */
public enum TipoOperacaoLancamento {

    PAGAMENTO(1, "Pagamento", "Pagto.", "Conta a Pagar", true, true),
    RECEBIMENTO(2, "Recebimento", "Receb.", "Conta a Receber", true, true),
    DEPOSITO(3, "Depósito", "Dep.", "Depósito na Conta", true, true),
    TRANSFERENCIA(4, "Transferência", "Transf.", "Transferência entre Contas", true, false),
    ESTORNO(5, "Estorno", "Estorno", "Estorno de uma movimentação da Conta", true, false),
    TROCAFORMAPAGTO(6, "Troca de Forma de Pagamento", "<PERSON>. <PERSON>.", "Troca de Forma de Pagamento", true, true),
    AJUSTESALDO(7, "Ajuste de Saldo", "Aj. <PERSON>", "Ajuste de Saldo da Conta", true, false),
    CUSTODIA(8, "Custódia", "Cust.", "Custódia", true, false),
    RECEBIVEL_AVULSO(9, "Recebível Avulso", "Recebível Avulso", "Recebível Avulso", false, true),
    RETIRADA_RECEBIVEL_LOTE(10, "Retirada de recebível de lote", "Retirada de recebível de lote", "Retirada de recebível", false, false),
    FLUXO_CAIXA(11, "Fluxo de Caixa", "Fluxo de Caixa", "Fluxo de Caixa", false, false),
    DEVOLUCAO_CHEQUE(12, "Devolução de cheques", "Devolução de cheques", "Devolução de cheques", false, false);

    private final Integer codigo;
    private final String descricao;
    private final String label;
    private final String descricaoCurta;
    private final Boolean usar;
    private final Boolean usarSempre;

    TipoOperacaoLancamento(Integer codigo, String descricao, String descCurta, String label, boolean usar,
                           boolean usarSempre) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.label = label;
        this.descricaoCurta = descCurta;
        this.usar = usar;
        this.usarSempre = usarSempre;
    }

    public static TipoOperacaoLancamento getTipoOperacaoLancamento(final Integer codigo) {
        TipoOperacaoLancamento obj = null;
        for (TipoOperacaoLancamento status : TipoOperacaoLancamento.values()) {
            if (status.getCodigo() == codigo.intValue()) {
                obj = status;
            }
        }
        return obj;
    }

    public static TipoOperacaoLancamento getTipoOperacaoLancamento(final String descricao) {
        TipoOperacaoLancamento obj = null;
        for (TipoOperacaoLancamento status : TipoOperacaoLancamento.values()) {
            if (status.getDescricao().toUpperCase().equals(descricao.toUpperCase())) {
                obj = status;
            }
        }
        return obj;
    }


    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getLabel() {
        return label;
    }

    public String getDescricaoCurta() {
        return descricaoCurta;
    }

    public Boolean getUsar() {
        return usar;
    }

    public Boolean getUsarSempre() {
        return usarSempre;
    }

    /**
     * Quando uma operação é estornável, significa que ela pode vir a ser excluída para que o valor volte ao cliente
     * ou à conta de origem. Por exemplo, numa movimentação de conta onde temos um valor transferido, iremos excluir a
     * última movimentação, fazendo com que o valor passe para a última conta associada.
     *
     * @return <b>true</b> se o tipo de operação for estornável ou <b>false</b> em caso contrário
     */
    public Boolean isEstornavel() {
        switch (this) {
            case DEVOLUCAO_CHEQUE:
                return false;
            default:
                return true;
        }
    }

    public Boolean isAjusteSaldo() {
        switch (this) {
            case AJUSTESALDO:
                return true;
            default:
                return false;
        }
    }
}
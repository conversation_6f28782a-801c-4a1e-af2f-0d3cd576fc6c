package br.com.pactosolucoes.enumeradores;

/**
 * Enumerador de tipo de filtro de data.
 */
public enum TipoFiltroData {

	DATA(1, "Data"), INTERVALO_DE_DATA(2, "Intervalo de Data"), MES_SEMANA(3, "Mês / Semana");

	private Integer codigo;
	private String descricao;

	/**
	 * Método que seta o código e descrição
	 * 
	 * @param codigo
	 * @param descricao
	 */
	private TipoFiltroData(final Integer codigo, final String descricao) {
		this.setCodigo(codigo);
		this.setDescricao(descricao);
	}

	/**
	 * Busca o código do enumerador e retorna o enumerador
	 * 
	 * @param codigo
	 * @return tipoFiltroData
	 */
	public static TipoFiltroData getTipoFiltroData(final Integer codigo) {
		TipoFiltroData tipoFiltroData = null;
		for (TipoFiltroData tipo : TipoFiltroData.values()) {
			if (tipo.getCodigo().equals(codigo)) {
				tipoFiltroData = tipo;
				break;
			}
		}
		return tipoFiltroData;
	}

	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo descricao.
	 */
	public String getDescricao() {
		return this.descricao;
	}

	/**
	 * @param descricao
	 *            O novo valor de descricao.
	 */
	public void setDescricao(final String descricao) {
		this.descricao = descricao;
	}

}
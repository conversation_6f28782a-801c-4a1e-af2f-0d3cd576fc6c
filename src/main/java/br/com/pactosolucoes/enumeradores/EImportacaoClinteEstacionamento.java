package br.com.pactosolucoes.enumeradores;

public enum EImportacaoClinteEstacionamento {
    SEM_VALOR_COM_HORA,
    COM_VALOR_SEM_HORA,
    SEM_VALOR_SEM_HORA,
    COM_VALOR_COM_HORA;

    public static EImportacaoClinteEstacionamento getConf(boolean valor, boolean hora){
        if(valor && hora) return COM_VALOR_COM_HORA;
        if(valor && !hora) return COM_VALOR_SEM_HORA;
        if(!valor && hora) return SEM_VALOR_COM_HORA;
        return SEM_VALOR_SEM_HORA;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum UsoArredondamentoEnum {
    NAO_USA(0,"Não usar"),
    ARRENDONDAR_CENTAVOS(1,"Arredondar centavos"),
    REMOVER_CENTAVOS(2,"Remover centavos");
    
    String descricao;
    int id;

    private UsoArredondamentoEnum(int id, String descricao) {
        this.descricao = descricao;
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
    
    public static UsoArredondamentoEnum getFromId(int id){
        for(UsoArredondamentoEnum uso : values()){
            if(uso.getId() == id){
                return uso;
            }
        }
        return null;
    }
    
}

package br.com.pactosolucoes.enumeradores;

/**
 * Created by <PERSON><PERSON> on 20/11/2016.
 */
public enum TipoConciliacaoEnum {

    PAGAMENTOS(4,"Por pagamentos"),
    VENDAS(3,"Por vendas"),
    CANCELAMENTO(5, "Cancelamento de pagamento"),
    CHARGEBACK(6, "Chargeback"),
    TAXA_CANCELAMENTO(7, "Taxas de Cancelamento"),
    ESTORNO_CHARGEBACK(8, "Estorno de Chargeback");

    public static TipoConciliacaoEnum getTipo(Integer codigo){
        for(TipoConciliacaoEnum t : values()){
            if(t.getCodigo().equals(codigo)){
                return t;
            }
        }
        return null;
    }

    TipoConciliacaoEnum(Integer codigo, String descricao) {
        this.descricao = descricao;
        this.codigo = codigo;
    }

    private String descricao;
    private Integer codigo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

package br.com.pactosolucoes.enumeradores;


public enum StatusEnvioMailingEnum {
	ERRO(1, "Erro"),
	AGUARDANDO(2, "Aguardando"),
	EXECUTANDO(3, "Executando"),
	CONCLUIDO(4,"<PERSON>cluí<PERSON>"),
	CONCLUIDO_ERRO(5,"Concluído com erro");
	
	private Integer codigo;
	private String descricao;
	
	private StatusEnvioMailingEnum(Integer codigo, String e){
		this.codigo = codigo;
		this.descricao = e;
	}

	public Integer getCodigo() {
		return codigo;
	}
	
	public static StatusEnvioMailingEnum getStatus(final Integer numeral) {
		for (StatusEnvioMailingEnum status : StatusEnvioMailingEnum.values()) {
			if (status.getCodigo().equals(numeral)) {
				return status;
			}
		}
		return null;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public String getDescricao() {
		return descricao;
	}
}

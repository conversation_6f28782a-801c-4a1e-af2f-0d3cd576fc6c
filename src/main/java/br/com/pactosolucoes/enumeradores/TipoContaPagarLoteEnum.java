/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

public enum TipoContaPagarLoteEnum {

    NENHUM(1, "Nenhum", "Nenhum"),
    BOLETO(2, "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"),
    BOLETO_CONTA_CONSUMO(3, "Boleto Conta Consumo", "Boleto"),
    PAYLOAD_PIX(4, "Payload Pix", "Pix"),
    TRANSFERENCIA(5, "Transferência Bancária", "Transferência"),
    OUTROS(6, "Outros", "Outros");

    private final Integer codigo;
    private final String descricao;
    private final String descricaoExibir;

    TipoContaPagarLoteEnum(Integer codigo, String descricao, String descricaoExibir) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.descricaoExibir = descricaoExibir;
    }

    public static TipoContaPagarLoteEnum obterPorCodigo(final Integer codigo) {
        TipoContaPagarLoteEnum obj = null;
        for (TipoContaPagarLoteEnum tipo : TipoContaPagarLoteEnum.values()) {
            if (tipo.getCodigo() == codigo.intValue()) {
                obj = tipo;
            }
        }
        return obj;
    }

    public static TipoContaPagarLoteEnum obterPorDescricao(final String descricao) {
        TipoContaPagarLoteEnum obj = null;
        for (TipoContaPagarLoteEnum status : TipoContaPagarLoteEnum.values()) {
            if (status.getDescricao().toUpperCase().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getDescricaoExibir() {
        return descricaoExibir;
    }

}

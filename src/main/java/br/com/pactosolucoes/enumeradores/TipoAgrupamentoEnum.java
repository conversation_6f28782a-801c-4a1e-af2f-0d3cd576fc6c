/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum TipoAgrupamentoEnum {

    TIPO_AGRUPAMENTO_1(1, "Agrupamento 1"),
    TIPO_AGRUPAMENTO_2(2, "Agrupamento 2"),
    TIPO_AGRUPAMENTO_3(3, "Agrupamento 3"),
    TIPO_AGRUPAMENTO_4(4, "Agrupamento 4"),
    TIPO_AGRUPAMENTO_5(5, "Agrupamento 5");

    private Integer id;
    private String descricao;

    private TipoAgrupamentoEnum(Integer id, String desc) {
        this.id = id;
        this.descricao = desc;
    }

    public Integer getId() {
        return id;
    }
    public String getDescricao() {
        return descricao;
    }

    public static TipoAgrupamentoEnum getFromId(Integer id){
        for(TipoAgrupamentoEnum tipo : values()){
            if(tipo.getId().equals(id)){
                return tipo;
            }
        }
        return null;
    }
}

package br.com.pactosolucoes.enumeradores;

public enum ZonaChurnEnum {
    SEGURANCA(0, 59, "Zona de segurança"),
    ATENCAO(60, 69, "Zona de atenção"),
    RISCO(70, 84, "Zona de risco"),
    CRITICA(85, 94, "Zona crítica"),
    DESPEDIDA(95, 100, "Zona de despedida");

    private final int min;
    private final int max;
    private final String descricao;

    ZonaChurnEnum(int min, int max, String descricao) {
        this.min = min;
        this.max = max;
        this.descricao = descricao;
    }

    public int getMin() {
        return min;
    }

    public int getMax() {
        return max;
    }

    public String getDescricao() {
        return descricao;
    }

    public static ZonaChurnEnum fromRiscoChurn(int score) {
        for (ZonaChurnEnum categoria : values()) {
            if (score >= categoria.min && score <= categoria.max) {
                return categoria;
            }
        }
        return null;
    }
}

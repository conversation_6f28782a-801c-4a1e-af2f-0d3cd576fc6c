package br.com.pactosolucoes.enumeradores;

public enum TipoDadoConciliacaoEnum {

    Movpagamento(1, "Movpagamento"),
    <PERSON><PERSON><PERSON><PERSON>(2, "Estornado"),
    MovPagamentoContaCorrente(3, "MovPagamentoContaCorrente"),
    CCDataAlterada(4, "CCDataAlterada"),
    MovConta(5, "MovConta"),
    NaoConciliado(6, "NaoConciliado");

    public static TipoDadoConciliacaoEnum getTipo(Integer codigo) {
        for (TipoDadoConciliacaoEnum t : values()) {
            if (t.getCodigo().equals(codigo)) {
                return t;
            }
        }
        return null;
    }

    TipoDadoConciliacaoEnum(Integer codigo, String descricao) {
        this.descricao = descricao;
        this.codigo = codigo;
    }

    private String descricao;
    private Integer codigo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

package br.com.pactosolucoes.enumeradores;

import negocio.comuns.utilitarias.ModuloNaoEncontradoException;
import org.apache.commons.lang.StringUtils;

import java.util.HashSet;
import java.util.Set;

public enum Modulo {

    AULA_CHEIA("SLC", "Aula cheia", "LoginControle.apresentarLinkAulaCheia", "imagens_flat/pct-icone-fundo-administrativo.svg", false, false, ""),
    CENTRAL_DE_EVENTOS("CE", "Central de eventos", "LoginControle.apresentarLinkCE", "imagens_flat/genteCE.svg", false, false, ""),
    CUSTOMER_RELATIONSHIP_MANAGEMENT("CRM", "CRM", "LoginControle.apresentarLinkParaModuloCRM", "imagens_flat/pct-icone-fundo-crm.svg", false, true, "LoginControle.permissaoAcessoMenuVO.configuracaoSistemaCRM"),
    FINANCEIRO("FIN", "Financeiro",  "LoginControle.apresentarLinkFinanceiro", "imagens_flat/pct-icone-fundo-financeiro.svg", false, true, "LoginControle.permissaoAcessoMenuVO.configuracaoFinanceiro"),
    GAME_OF_RESULTS("GOR", "Game", "LoginControle.apresentarLinkGame", "imagens_flat/pct-icone-fundo-game-of-results.svg", false, false, ""),
    STUDIO("EST", "Estúdio", "LoginControle.apresentarLinkEstudio", "imagens_flat/pct-icone-fundo-studio.svg", false, false, ""),
    TREINO("TR", "Treino", "false", "imagens_flat/pct-icone-fundo-administrativo.svg", false, false, ""),
    ZILLYON_WEB("ZW", "Administrativo", "Adm", "LoginControle.apresentarLinkZW", "imagens_flat/pct-icone-fundo-administrativo.svg", false, true, "LoginControle.permissaoAcessoMenuVO.configuracaoSistema"),
    UNIVERSIDADE_COORPORATIVA_PACTO("UCP", "UCP", "false", "imagens_flat/pct-icone-fundo-administrativo.svg", false, false, ""),
    GESTAO_DE_PERSONAL("GP", "Personal", "false", "imagens_flat/pct-icone-fundo-administrativo.svg", false, false, ""),
    SMART_BOX("SBX", "Smart Box", "false", "imagens_flat/pct-icone-fundo-administrativo.svg", false, false, ""),
    ZILLYON_AUTO_ATENDIMENTO("ZAA", "Auto atentimento", "false", "imagens_flat/pct-icone-fundo-administrativo.svg", false, false, ""),
    NOVO_TREINO("NTR", "Treino", "LoginControle.apresentarModuloNovoTreino", "imagens_flat/pct-icone-fundo-novo-treino.svg", true, true, "LoginControle.apresentarModuloNovoTreino"),
    NOVO_ZW("NZW", "Administrativo", "false", "imagens_flat/pct-icone-fundo-administrativo.svg", true, false, ""),
    NOVO_CRM("NCRM", "Relacionamento", "true", "/imagens_flat/pct-icone-fundo-crm.svg", true, false, ""),
    PACTO_PAY("PAY", "PactoPay", "LoginControle.apresentarModuloPactoPay", "imagens_flat/pct-icone-fundo-pactopay.svg", true, false, ""),
    CROSS("NCR", "Cross", "LoginControle.apresentarModuloCross", "imagens_flat/pct-icone-fundo-cross.svg", true, false, ""),
    GRADUACAO("GRD", "Graduação", "LoginControle.apresentarModuloGraduacao", "imagens_flat/pct-icone-fundo-graduacao.svg", true, false, ""),
    AVALIACAO_FISICA("NAV", "Avaliação Física", "LoginControle.apresentarModuloAvaliacaoFisica", "imagens_flat/pct-icone-fundo-avaliacao.svg", true, false, ""),
    CANAL_CLIENTE("CANAL", "Canal do Cliente", "true", "imagens_flat/pct-icone-fundo-canal-do-cliente.svg", false, false, ""),
    NOTAS("NOTAS", "Notas", "LoginControle.apresentarNotaFiscal", "imagens_flat/pct-icone-fundo-nota-fiscal.svg", false, false, ""),
    AGENDA("AGE", "Agenda", "LoginControle.apresentarModuloAgenda", "imagens_flat/pct-icone-fundo-novo-agenda-2.svg", true, false, ""),
    INTEGRACOES("INTE", "Integrações", "false", "null", false, false, ""),
    PESSOAS("PES", "Pessoas", "true", "null", true, false, ""),
    FACILITE_PAY("FAC", "Fypay", "true", "null", false, false, ""),
    ;

    private static final String SEPARADOR_SIGLA_MODULOS = ",";
    private final String siglaModulo;
    private final String descricao;
    private final String titulo;
    private final String expresaoRenderizar;
    private final String iconeRelaiveUrl;
    private final boolean zwUi;
    private boolean possuiConfiguracao;
    private final String expresaoRenderizarConfig;

    Modulo(String siglaModulo, String descricao, String expresaoRenderizar, String iconeRelaiveUrl, boolean zwUi, boolean possuiConfiguracao, String expresaoRenderizarConfig) {
        this.siglaModulo = siglaModulo;
        this.descricao = descricao;
        this.titulo = descricao;
        this.expresaoRenderizar = expresaoRenderizar;
        this.iconeRelaiveUrl = iconeRelaiveUrl;
        this.zwUi = zwUi;
        this.possuiConfiguracao = possuiConfiguracao;
        this.expresaoRenderizarConfig = expresaoRenderizarConfig;
    }

    Modulo(String siglaModulo, String descricao, String titulo, String expresaoRenderizar, String iconeRelaiveUrl, boolean zwUi, boolean possuiConfiguracao, String expresaoRenderizarConfig) {
        this.siglaModulo = siglaModulo;
        this.descricao = descricao;
        this.titulo = titulo;
        this.expresaoRenderizar = expresaoRenderizar;
        this.iconeRelaiveUrl = iconeRelaiveUrl;
        this.zwUi = zwUi;
        this.possuiConfiguracao = possuiConfiguracao;
        this.expresaoRenderizarConfig = expresaoRenderizarConfig;
    }

    public static Modulo fromSigla(String siglaModulo) {
        for (Modulo value : values()) {
            String siglaModuloComTrim = siglaModulo.trim();
            if (value.siglaModulo.equalsIgnoreCase(siglaModuloComTrim)) {
                return value;
            }
        }

        throw new ModuloNaoEncontradoException(siglaModulo);
    }

    public static Set<Modulo> fromSiglas(String siglasModulos) {
        String[] siglas = siglasModulos.split(SEPARADOR_SIGLA_MODULOS);

        Set<Modulo> modulos = new HashSet<Modulo>();
        for (String sigla : siglas) {
            if (StringUtils.isNotBlank(sigla)) {
                modulos.add(Modulo.fromSigla(sigla));
            }
        }

        return modulos;
    }

    public static boolean isModuloHabilitado(Set<Modulo> modulosHabilitados, Modulo modulo) {
        return modulosHabilitados.contains(modulo);
    }

    public static boolean isModuloNaoHabilitado(Set<Modulo> modulosHabilitados, Modulo modulo) {
        return !modulosHabilitados.contains(modulo);
    }

    public String getSiglaModulo() {
        return siglaModulo;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getExpresaoRenderizar() {
        return expresaoRenderizar;
    }

    public String getIconeRelaiveUrl() {
        return iconeRelaiveUrl;
    }

    public boolean isZwUi() {
        return zwUi;
    }
    public boolean isPossuiConfiguracao() { return possuiConfiguracao; }

    public String getTitulo() {
        return titulo;
    }

    public String getExpresaoRenderizarConfig() {
        return expresaoRenderizarConfig;
    }
}

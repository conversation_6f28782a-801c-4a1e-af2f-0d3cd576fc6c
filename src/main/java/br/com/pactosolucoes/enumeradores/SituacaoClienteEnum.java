package br.com.pactosolucoes.enumeradores;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 */
public enum SituacaoClienteEnum {

    A_VENCER("AV", "A Vencer", false,"AT"),
    ATIVO("AT", "Ativo", true, ""),
    CANCELADO("CA", "Cancelado", false,"CA"),
    DESISTENTE("DE", "Desistente", false,"IN"),
    INATIVO("IN", "Inativo", true, ""),
    TRANCADO("TR", "Trancado", false,"TR"),
    VENCIDO("VE", "Vencido", false,"IN"),
    VISITANTE("VI", "Visitante", true, ""),
    CONTRATOS_ATIVO("AT", "Ativo, Contrato ativo", true, "AT")
    ;

    private String codigo;
    private String descricao;
    private boolean situacaoCliente;
    private String situacaoContrato;

    private SituacaoClienteEnum(String codigo, String descricao, boolean situacaoCli, String situacaoContrato) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.situacaoCliente = situacaoCli;
        this.situacaoContrato = situacaoContrato;
    }

    public static SituacaoClienteEnum getSituacaoCliente(final String codigo) {
        SituacaoClienteEnum situacao = null;
        for (SituacaoClienteEnum sit : SituacaoClienteEnum.values()) {
            if (sit.getCodigo().equals(codigo)) {
                situacao = sit;
                break;
            }
        }
        return situacao;
    }

    public static JSONArray toJSON() throws JSONException {
        JSONArray situacoes = new JSONArray();

        for (SituacaoClienteEnum situacao :SituacaoClienteEnum.values()) {
            JSONObject situacaoJSON = new JSONObject();
            situacaoJSON.put("codigo", situacao.getCodigo());
            situacaoJSON.put("descricao", situacao.getDescricao());

            situacoes.put(situacaoJSON);
        }

        return situacoes;
    }

    public boolean isSituacaoCliente() {
        return situacaoCliente;
    }

    public boolean isSituacaoContratoTambem(){
        return isSituacaoCliente() && !situacaoContrato.isEmpty();
    }

    public void setSituacaoCliente(boolean situacaoCliente) {
        this.situacaoCliente = situacaoCliente;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }
}

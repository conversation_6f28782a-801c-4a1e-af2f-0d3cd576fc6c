package br.com.pactosolucoes.enumeradores;

public enum OcorrenciaEnum {
	INSTANTANEO(1, "Instantâneo", true),
	DIARIAMENTE(2, "Diariamente", true),
	SEMANALMENTE(3, "Semanalmente", true),
	MENSALMENTE(4, "Mensalmente", true),
	INCLUSAO_VISITANTE(5, "Após o cadastro do visitante", false),
	VENDA_CONTRATO(6, "Após a venda", false),
	APOS_MATRICULA(7, "Após a matrícula", false),
	APOS_RENOVACAO(8, "Após a renovação", false),
	APOS_REMATRICULA(9, "Após a rematrícula", false),
	APOS_CANCELAMENTO(10, "Após o cancelamento", false);

	private Integer codigo;
	private String descricao;
	private boolean apresentar;
	
	private OcorrenciaEnum(Integer codigo, String descricao, boolean apresentar){
		this.codigo = codigo;
		this.descricao = descricao;
		this.apresentar = apresentar;

	}
	
	public Integer getCodigo() {
		return codigo;
	}
	public String getDescricao() {
		return descricao;
	}

	public static OcorrenciaEnum getOcorrencia(final Integer numeral) {
		OcorrenciaEnum ocorrencia = null;
		for (OcorrenciaEnum oc : OcorrenciaEnum.values()) {
			if (oc.getCodigo().equals(numeral)) {
				ocorrencia = oc;
			}
		}
		return ocorrencia;
	}

	public boolean isApresentar() {
		return apresentar;
	}

	public void setApresentar(boolean apresentar) {
		this.apresentar = apresentar;
	}
}

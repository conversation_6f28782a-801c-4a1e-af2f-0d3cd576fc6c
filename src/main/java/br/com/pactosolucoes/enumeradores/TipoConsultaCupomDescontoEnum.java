package br.com.pactosolucoes.enumeradores;

/**
 * Created by ulisses on 30/06/2016.
 */
public enum TipoConsultaCupomDescontoEnum {

    TODOS(1,"TODOS"),
    CUPOM_ISENCAO_ADESAO(2,"CUPONS UTILIZADOS PARA ISENÇÃO DE ADESÃO"),
    CUPOM_ISENCAO_MENSALIDADE(3, "CUPONS UTILIZADOS PARA ISENÇÃO MENSALIDADE");

    TipoConsultaCupomDescontoEnum(int codigo, String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }

    private int codigo;
    private String descricao;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    public static TipoConsultaCupomDescontoEnum getTipo(int codigo){
        for(TipoConsultaCupomDescontoEnum tipo : values()){
            if(tipo.getCodigo() == codigo){
                return tipo;
            }
        }
        return null;
    }
}

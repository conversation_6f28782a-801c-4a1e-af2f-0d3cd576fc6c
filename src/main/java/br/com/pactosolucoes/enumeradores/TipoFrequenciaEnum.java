package br.com.pactosolucoes.enumeradores;

/**
 * Created by ulisses on 20/08/2015.
 */
public enum TipoFrequenciaEnum {

    SEMANAL(1);

    TipoFrequenciaEnum(Integer codigo){
        setCodigo(codigo);
    };
    private Integer codigo;

    public Integer getCodigo() {
        return codigo;
    }

    public static TipoFrequenciaEnum getTipoFrequenciaEnum(Integer codigo){
        for (TipoFrequenciaEnum tipoFrequenciaEnum: TipoFrequenciaEnum.values()){
            if (tipoFrequenciaEnum.getCodigo().equals(codigo)){
                return tipoFrequenciaEnum;
            }
        }
        return null;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}

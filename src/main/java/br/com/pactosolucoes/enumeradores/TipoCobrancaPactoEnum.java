package br.com.pactosolucoes.enumeradores;

/**
 * Created by luiz on 07/04/2016.
 */
public enum TipoCobrancaPactoEnum {

    /**
     * Essa classe é compartilhada com o OAMD
     * Em caso de alteração deve ser realizado a mesma no OAMD
     */

    PRE_PAGO(0, "Pré-Pago Tentativa"),
    POS_PAGO_EFETIVADO(1, "Pós-Pago Efetivado"),
    POS_PAGO_TENTATIVA(2, "Pós-Pago Tentativa"),
    REDE_EMPRESAS_PRE_PAGO(3, "Pré-Pago Efetivado - Rede de empresas"),
    PRE_PAGO_EFETIVADO(4, "Pré-Pago Efetivado"),
    POS_PAGO_EFETIVADO_MENSAL(5, "Pós-Pago Efetivado - Mensal"),
    POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS(6, "Pós-Pago Efetivado - Mensal - Rede de Empresas"),
    ;

    private Integer codigo;
    private String descricao;

    TipoCobrancaPactoEnum(final Integer codigo, final String descricao) {
        this.setCodigo(codigo);
        this.setDescricao(descricao);
    }

    public static TipoCobrancaPactoEnum getConsultarPorCodigo(Integer codigo) {
        for (TipoCobrancaPactoEnum tipoCobranca : values()) {
            if (tipoCobranca.getCodigo().equals(codigo)) {
                return tipoCobranca;
            }
        }
        return null;
    }

    public static String getConsultarDescricaoPorCodigo(Integer codigo) {
        for (TipoCobrancaPactoEnum tipoCobranca : values()) {
            if (tipoCobranca.getCodigo().equals(codigo)) {
                return tipoCobranca.getDescricao();
            }
        }
        return "";
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isPrePago() {
        return this.equals(TipoCobrancaPactoEnum.PRE_PAGO) ||
                this.equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO) ||
                this.equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO);
    }

    public boolean isPosPago() {
        return this.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO) ||
                this.equals(TipoCobrancaPactoEnum.POS_PAGO_TENTATIVA) ||
                this.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL) ||
                this.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS);
    }
}

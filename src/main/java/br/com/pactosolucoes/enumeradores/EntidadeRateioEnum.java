package br.com.pactosolucoes.enumeradores;

/**
 * <AUTHOR>
 *
 */
public enum EntidadeRateioEnum {

	CATEGORIA_PRODUTO(1, "Categoria de Produto"), PRODUTO(2,"Produto"), MODALIDADE(3, "Modalidade"),
	AMBIENTES(4,"Ambientes"), AMBIENTE(5,"Ambiente"), PRODUTO_CE(6, "Produto CE"),
	SERVICOS(7, "Serviços"), SERVICO(8, "Serviço"), BENS_CONSUMO(9, "Bens de consumo"), UTENSILIOS(10, "Utensílios"),
	BRINQUEDOS(11, "Brinquedos"), CREDITO(12, "Crédito"), DEVOLUCAO_CREDITO(13,"Devolução crédito"),
        GERAL_MODALIDADES(14,"Rateio geral das modalidades")
        ;
	
	private int tipo;
	private String descricao;
	
	private EntidadeRateioEnum(int tipo, String descricao){
		this.setTipo(tipo);
		this.setDescricao(descricao);
	}
	
	public static EntidadeRateioEnum getEntidadeRateioEnum(int tipo){
		EntidadeRateioEnum tRateio = null;
		for(EntidadeRateioEnum tipoRateio : EntidadeRateioEnum.values()){
			if(tipoRateio.getTipo() == tipo){
				tRateio = tipoRateio;
				break;
			}
		}
		return tRateio;
	}

	/**
	 * @param tipo the tipo to set
	 */
	public void setTipo(int tipo) {
		this.tipo = tipo;
	}

	/**
	 * @return the tipo
	 */
	public int getTipo() {
		return tipo;
	}

	/**
	 * @param descricao the descricao to set
	 */
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	/**
	 * @return the descricao
	 */
	public String getDescricao() {
		return descricao;
	}
}

package br.com.pactosolucoes.enumeradores;

/**
 * Created by ulisses on 26/05/2016.
 */
public enum TipoDistribuicaoCupomDescontoEnum {

    AGENCIA_MARKETING(1, "AGÊNCIA DE MARKETING"),
    ZW(2, "<PERSON><PERSON><PERSON>YONWEB");

    TipoDistribuicaoCupomDescontoEnum(int codigo, String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }


    private int codigo;
    private String descricao;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    public static TipoDistribuicaoCupomDescontoEnum getTipo(int codigo) {
        for (TipoDistribuicaoCupomDescontoEnum tipo : values()) {
            if (tipo.getCodigo() == codigo){
                return tipo;
            }
        }
        return null;
    }

}

package br.com.pactosolucoes.enumeradores;

/**
 * Created by <PERSON><PERSON> on 28/10/2021.
 */
public enum ConfiguracaoEmailEnum {

    GMAIL("smtp.gmail.com", new String[]{"587"}),
    HOTMAIL("smtp.office365.com", new String[]{"587"}),
    OUTLOOK("smtp-mail.outlook.com", new String[]{"587"}),
    YAHOO("smtp.mail.yahoo.com", new String[]{"465"}),
    SENDGRID("smtp.sendgrid.net", new String[]{"465"}),
    TERRA("smtp.vix.terra.com.br", new String[]{"587", "465"}),
    UOL("smtp.uol.com.br", new String[]{"587"}),
    KINGHOST("smtp.kinghost.net", new String[]{"465"}),
    KINGHOST_INTER("smtpi.kinghost.net", new String[]{"465"}),
    UHSERVER("smtp.uhserver.com", new String[]{"465"}),
    SMTPLW("smtplw.com.br", new String[]{"587"}),
    HOSTINGER("smtp.hostinger.com", new String[]{"465"}),
    AMAZON_AWS("email-smtp.us-west-2.amazonaws.com", new String[]{"587", "465"}),
    GENERICO("smtp.EMAIL_REMETENTE", new String[]{"587", "465", "26"}),
    GENERICO2("mail.EMAIL_REMETENTE", new String[]{"587", "465", "26"}),
    CLUBJOY("mail.clubjoyacademia.com.br", new String[]{"465"}),
    EMAILSSL("email-ssl.com.br", new String[]{"465"}),
    MAILGUN("smtp.mailgun.org", new String[]{"587"}),
    ZOHO("smtp.zoho.com", new String[]{"587", "465"})
    ;

    private String smtpPadrao;
    private String[] portaPadrao;


    ConfiguracaoEmailEnum(String smtpPadrao, String[] portaPadrao) {
        this.smtpPadrao = smtpPadrao;
        this.portaPadrao = portaPadrao;
    }

    public String getSmtpPadrao() {
        return smtpPadrao;
    }

    public String[] getPortaPadrao() {
        return portaPadrao;
    }
}

package br.com.pactosolucoes.enumeradores;

public enum TipoPlanoEnum {

    PLANO_NORMAL(0, "Plano Normal"),
    PLANO_RECORRENCIA(1, "Plano Recorrência");

    private Integer codigo;
    private String descricao;

    TipoPlanoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoPlanoEnum getTipoPlanoEnum(Integer codigo){
        for (TipoPlanoEnum obj: TipoPlanoEnum.values()){
            if (obj.getCodigo().equals(codigo))
                return obj;
        }
        return null;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum TipoConsultaVelocimetro {
    RECEITA("<PERSON>ce<PERSON>", 1),
    FATURAMENTO("Faturamento",2),
    DESPESA("Despesa", 3);
    
    private String label;
    private int codigo;

    private TipoConsultaVelocimetro(String label, int codigo){
        this.codigo = codigo;
        this.label = label;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

 public static TipoConsultaVelocimetro getPeloCodigo(int codigo) {
        for (TipoConsultaVelocimetro mes : TipoConsultaVelocimetro.values()) {
            if (mes.getCodigo() == codigo)
                return mes;
        }
        return null;
    }


}

package br.com.pactosolucoes.enumeradores;

/**
 * Enumerador que substitui o uso do pattern de classe estática 'Domínios' para obtenção de tipos de colaboradores.
 * <AUTHOR>
 *
 */
public enum TipoTelefoneEnum {
	
	RESIDENCIAL ("RE", "Residencial"),
	RECADO ("RC", "Recado"),
	CELULAR ("CE", "Celular"),
	COMERCIAL ("CO", "Comercial"),
	FAX ("FA", "Fax"),
	EMERGENCIA("EM","Emergência");
	
	private String codigo;
	private String descricao;
	
	
	private TipoTelefoneEnum(String codigo, String descricao){
		this.codigo = codigo;
		this.descricao = descricao;
	}
	
	/**
	 * @param descricao the descricao to set
	 */
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}
	/**
	 * @return the descricao
	 */
	public String getDescricao() {
		return descricao;
	}
	/**
	 * @param codigo the codigo to set
	 */
	public void setCodigo(String codigo) {
		this.codigo = codigo;
	}
	/**
	 * @return the codigo
	 */
	public String getCodigo() {
		return codigo;
	}

	public static TipoTelefoneEnum obterPorSigla(String sigla) {
		TipoTelefoneEnum[] values = TipoTelefoneEnum.values();
		for (TipoTelefoneEnum eDIStatusEnum : values) {
			if (eDIStatusEnum.getCodigo().equals(sigla)) {
				return eDIStatusEnum;
			}
		}
		return TipoTelefoneEnum.RESIDENCIAL;
	}
}

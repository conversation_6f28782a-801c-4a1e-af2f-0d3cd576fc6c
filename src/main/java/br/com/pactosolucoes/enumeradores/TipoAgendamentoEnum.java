package br.com.pactosolucoes.enumeradores;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

public enum TipoAgendamentoEnum {
	
	AGENDAMENTO_PREVISTO(2,"Agendamento"),
	AGENDAMENTO_INSTANTANEO(1, "Instantâneo"),
	TODOS(3,"Todos");
	
	private String descricao;
	private Integer codigo;
	
	private TipoAgendamentoEnum(Integer codigo, String descricao){
		this.descricao = descricao;
		this.codigo = codigo;
	}
	
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}
	public String getDescricao() {
		return descricao;
	}
	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}
	public Integer getCodigo() {
		return codigo;
	}

	public static TipoAgendamentoEnum getTipo(Integer codigo){
		for(TipoAgendamentoEnum t : values()){
			if(t.getCodigo().equals(codigo)){
				return t;
			}
		}
		return null;
	}
	
	public static List<SelectItem> getListaCombo(){
		List<SelectItem> lista = new ArrayList<SelectItem>();
		for(TipoAgendamentoEnum tipo : values()){
			lista.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
		}
		return lista;
	}
	
}

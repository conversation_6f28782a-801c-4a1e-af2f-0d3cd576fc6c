/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum RegraCancelamentoAutomaticoEnum {

    PARCELA_VENCIDA_TOLERANCIA_PLANO_TODAS_PARCELAS(0, "\nMOTIVO: CA<PERSON><PERSON>AMENTO POR PARCELA VENCIDA HÁ XXX DIAS. \nREGRA: CANCELAR TODAS AS PARCELAS"),
    PARCELA_VENCIDA_TOLERANCIA_PLANO_MAIOR_IGUAL_MES_ATUAL(1, "\nMOTIVO: CANCELAMENTO POR PARCELA VENCIDA HÁ XXX DIAS. \nREGRA: <PERSON><PERSON>ELAR PARCELAS MAIOR IGUAL AO MÊS ATUAL"),
    PARCELAS_VENCIDAS_SEGUIDAS(2, "\nMOTIVO: CANCELAMENTO POR MAIS DE XXX PARCELAS VENCIDAS SEGUIDAS"),
    TOLERANCIA_NAO_ASSINOU_CONTRATO(3, "\nMOTIVO: CANCELAMENTO POR MAIS DE XXX DIAS SEM ASSINAR O CONTRATO"),
    PARCELA_VENCIDA_APOS_QTD_DIAS_UTEIS_TOLERADOS(4, "\nMOTIVO: CANCELAMENTO POR PARCELA VENCIDA HÁ MAIS DE XXX DIAS ÚTEIS");

    private Integer codigo;
    private String descricao;

    private RegraCancelamentoAutomaticoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}

package br.com.pactosolucoes.enumeradores;

import java.util.ArrayList;
import java.util.List;


public enum SituacaoEnum {
	//É UM VISITANTE QUE NÃO SE ENQUADRA EM FREE PASS, AULA AVULSA E DIÁRIA
	VISITANTE_NORMAL("VI", "Visitante Normal", GrupoSituacaoEnum.VISITANTE), 
	//indica que o cliente é um visitante e possui um periodo de acesso do tipo free pass. 
	//Identificada na tabela periodoacessocliente, quando o tipo de acesso  é PL
	FREE_PASS("PL", "Free Pass", GrupoSituacaoEnum.VISITANTE),
	//também é um estado de visitante e também é identificado por um registro na tabela periodoacessocliente
	AULA_AVULSA("AA", "Aula Avulsa", GrupoSituacaoEnum.VISITANTE),
	//também é um estado de visitante e também é identificado por um registro na tabela periodoacessocliente
	DIARIA("DI", "Diária", GrupoSituacaoEnum.VISITANTE),
	//um cliente ativo tem situação normal quando possui Histórico Contrato de um dos tipos: MA, RE, RN
	NORMAL("NO", "Normal", GrupoSituacaoEnum.ATIVO),
	//um cliente ativo está de atestado quando ele possui registro de contrato operação do tipo AT, valendo
	//pelo período indicado nesse mesmo registro
	ATESTADO("AT", "Atestado", GrupoSituacaoEnum.ATIVO),
	// indicado pela tabela Histórico contrato, TIPO: CR
	CARENCIA("CR", "Férias", GrupoSituacaoEnum.ATIVO),
	//também indicado pela tabela Histórico contrato, TIPO: AV
	A_VENCER("AV", "A vencer", GrupoSituacaoEnum.ATIVO),
	//também indicado pela tabela Histórico contrato, TIPO: TR
	TRANCADO("TR", "Trancado", GrupoSituacaoEnum.TRANCADO),
	//indicado pela tabela contrato operação, TIPO: TV
	TRANCADO_VENCIDO("TV", "Trancado Vencido", GrupoSituacaoEnum.TRANCADO),
	// indicado pela tabela Histórico contrato, TIPO: VE
	VENCIDO("VE", "Vencido", GrupoSituacaoEnum.INATIVO),
	// indicado pela tabela Histórico contrato, TIPO: DE
	DESISTENCIA("DE", "Desistência", GrupoSituacaoEnum.INATIVO),
	// indicado pela tabela Histórico contrato, TIPO: CA
	CANCELADO("CA", "Cancelado", GrupoSituacaoEnum.INATIVO),
	//um cliente INativo está de atestado quando ele possui registro de contrato operação do tipo AT
	// e esse atestado foi lançado depois do término do contrato
	// valendo pelo período indicado nesse mesmo registro.
	ATESTADO_INATIVO("AI", "Atestado(IN)", GrupoSituacaoEnum.INATIVO),
	RENOVADO("RN", "Renovado", null);
	
	
	private String codigo;
	private String descricao;
	private GrupoSituacaoEnum grupo;
	
	
	private SituacaoEnum(String codigo, String descricao, GrupoSituacaoEnum grupo){
		this.codigo = codigo;
		this.descricao = descricao;
		this.grupo = grupo;
	}
	
	/**
	 * Responsável por obter uma lista com as situações de um grupo específico. Ex.: IN, AT...
	 * <AUTHOR>
	 * 05/04/2011
	 * @param grupo
	 * @return
	 */
	public static List<SituacaoEnum> getSituacoesPorGrupo(GrupoSituacaoEnum grupo){
		List<SituacaoEnum> lista = new ArrayList<SituacaoEnum>();
		for(SituacaoEnum sit : SituacaoEnum.values()){
			if(sit.getGrupo() != null && sit.getGrupo().equals(grupo))
				lista.add(sit);
		}
		return lista;
	}

	/**
	 * Busca o código do enumerador e retorna o enumerador
	 * 
	 * @param codigo
	 * @return situacao
	 */
	public static SituacaoEnum getSituacao(final String codigo) {
		SituacaoEnum situacao = null;
		for (SituacaoEnum sit : SituacaoEnum.values()) {
			if (sit.getCodigo().equals(codigo)) {
				situacao = sit;
			}
		}
		if (codigo.equals("MA") || codigo.equals("RE") || codigo.equals("RT") || codigo.equals("RC") || codigo.equals("RA") 
				|| codigo.equals("BA") || codigo.equals("BR")){
			situacao = SituacaoEnum.NORMAL;
		}
		return situacao;
	}

	/**
	 * @return the codigo
	 */
	public String getCodigo() {
		return codigo;
	}

	/**
	 * @param codigo the codigo to set
	 */
	public void setCodigo(String codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return the descricao
	 */
	public String getDescricao() {
		return descricao;
	}

	/**
	 * @param descricao the descricao to set
	 */
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	/**
	 * @return the grupo
	 */
	public GrupoSituacaoEnum getGrupo() {
		return grupo;
	}

	/**
	 * @param grupo the grupo to set
	 */
	public void setGrupo(GrupoSituacaoEnum grupo) {
		this.grupo = grupo;
	}
}

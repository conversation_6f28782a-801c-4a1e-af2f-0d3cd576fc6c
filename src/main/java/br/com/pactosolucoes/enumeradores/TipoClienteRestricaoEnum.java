package br.com.pactosolucoes.enumeradores;

public enum TipoClienteRestricaoEnum {

    INADIMPLENCIA("IN", "Inadimplência"),
    RESGRISTRO_MANUAL_TELA_CLIENTE("RM", "Registro manual tela do cliente");

    private String sigla;
    private String descricao;

    TipoClienteRestricaoEnum(String sigla, String descricao) {
        this.sigla = sigla;
        this.descricao = descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

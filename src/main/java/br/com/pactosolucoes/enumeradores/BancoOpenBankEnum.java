package br.com.pactosolucoes.enumeradores;

/**
 * <AUTHOR>
 */
public enum BancoOpenBankEnum {
    STONE_OPENBANK(1, "STONE OPENBANK"),
    ;


    Integer codigo;
    String descricao;

    private BancoOpenBankEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static BancoOpenBankEnum getFromCodigo(Integer cod) {
        for (BancoOpenBankEnum cfg : BancoOpenBankEnum.values()) {
            if (cod != null && cfg.getCodigo().equals(cod)) {
                return cfg;
            }
        }
        return null;

    }

    public static BancoOpenBankEnum getFromDescricao(String descricao) {
        for (BancoOpenBankEnum cfg : BancoOpenBankEnum.values()) {
            if (descricao != null && cfg.getDescricao().equals(descricao)) {
                return cfg;
            }
        }
        return null;

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

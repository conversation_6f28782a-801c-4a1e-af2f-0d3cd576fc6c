package br.com.pactosolucoes.enumeradores;

public enum MarcadoresEmailEnum {
    ENDERECO("ENDERECO_TAG", "Endereço", true),
    CIDADE("CIDADE_ESTADO_TAG", "Cidade/Estado", true),
    TELEFONE("TELEFONE_TAG", "Telefone", true),
    SITE("WEB_SITE_TAG", "Site", true),
    LOGO_EMPRESA_TAG("LOGO_EMPRESA_TAG", "Logomarca", true),
    NOME_EMPRESA("NOME_EMPRESA", "Nome", true);

    private String descricao;
    private String tag;
    private boolean modeloMensagem;

    MarcadoresEmailEnum(String tag, String descricao, boolean modeloMensagem) {
        this.descricao = descricao;
        this.tag = tag;
        this.modeloMensagem = modeloMensagem;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public boolean isModeloMensagem() {
        return modeloMensagem;
    }

    public void setModeloMensagem(boolean modeloMensagem) {
        this.modeloMensagem = modeloMensagem;
    }
}

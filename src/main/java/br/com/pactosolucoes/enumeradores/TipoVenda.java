package br.com.pactosolucoes.enumeradores;

import negocio.comuns.utilitarias.UteisValidacao;

public enum TipoVenda {
    NORMAL("NO","Normal"),
    DEGUSTACAO("DE","Degustacao"),
    EVENTO("EV","Evento");



    private String sigla;
    private String descricao;

    TipoVenda(String sigla, String descricao) {
        this.sigla = sigla;
        this.descricao = descricao;
    }

    TipoVenda(String sigla) {
        this.sigla = sigla;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoVenda obterEnumPorSigla(String sigla){
        if(UteisValidacao.emptyString(sigla)){
            return null;
        }
        for(TipoVenda obj : values()){
            if(obj.getSigla().equals(sigla)){
                return obj;
            }
        }
        return null;
    }
}


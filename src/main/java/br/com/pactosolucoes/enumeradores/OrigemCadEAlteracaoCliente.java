package br.com.pactosolucoes.enumeradores;

import negocio.comuns.utilitarias.UteisValidacao;

public enum OrigemCadEAlteracaoCliente {
    ZW(0,"ZW"),
    VENDAS_ONLINE(1,"Vendas Online"),
    ZW_NOVO_FRONT(2, "Novo Front ZW");

    int id;
    private String descricao;

    OrigemCadEAlteracaoCliente(int id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static OrigemCadEAlteracaoCliente obterEnumPorId(int id){
        if(UteisValidacao.emptyNumber(id)){
            return null;
        }
        for(OrigemCadEAlteracaoCliente obj : values()){
            if(obj.getId()== id){
                return obj;
            }
        }
        return null;
    }
}


/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 * <AUTHOR>
 */
public enum TipoCancelamentoEnum {
    CANCELAMENTO_PADRAO(0, "Cancelamento padrão"),
    CANCELAMENTO_ANTECEDENCIA(1, "Cancelamento com cálculo de antecedência para vencimento da próxima parcela"),
    CANCELAMENTO_OBRIGATORIEDADE_PAGAMETO(2, "Cancelamento com obrigatoriedade de pagamento da próxima parcela e 30 dias de acesso"),
    CANCELAMENTO_AVALIANDO_PARCELAS(3, "Cancelamento avaliando parcelas"),
    ;

    String descricao;
    int id;

    TipoCancelamentoEnum(int id, String descricao) {
        this.descricao = descricao;
        this.id = id;
    }

    public static TipoCancelamentoEnum getFromId(int id) {
        for (TipoCancelamentoEnum uso : values()) {
            if (uso.getId() == id) {
                return uso;
            }
        }
        return null;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

}

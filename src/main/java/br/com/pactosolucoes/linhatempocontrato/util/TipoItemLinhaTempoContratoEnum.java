/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.linhatempocontrato.util;

/**
 *
 * <AUTHOR>
 */
public enum TipoItemLinhaTempoContratoEnum {
    
    PARCELA("parcela", "fa-icon-credit-card"),
    PAGAMENTO("pagamento", "fa-icon-money"),
    OPERACAO_CONTRATO("", ""),
    RELACIONAMENTO("", "")
    ;

    private TipoItemLinhaTempoContratoEnum(String css, String icone) {
        this.css = css;
        this.icone = icone;
    }
    private String css;
    private String icone;

    public String getCss() {
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }
    
    
    
}

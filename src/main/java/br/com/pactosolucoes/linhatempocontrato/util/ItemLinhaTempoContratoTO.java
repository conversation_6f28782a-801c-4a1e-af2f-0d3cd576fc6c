/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.linhatempocontrato.util;

import br.com.pactosolucoes.enumeradores.TipoOperacaoContratoEnum;
import java.util.Date;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class ItemLinhaTempoContratoTO extends SuperTO{
    
    private TipoItemLinhaTempoContratoEnum tipo;
    private TipoOperacaoContratoEnum tipoOperacao;
    private Date inicio;
    private Date fim;
    private String titulo;
    private String complemento;
    private Integer complementoPosicao = 0;
    private String texto;
    private String nomeResponsavel;
    private Double percentual;
    private Double percentualFim;
    private Double tamanhoLinha;
    private String css = "";
    private String icone = "";
    private String name = "";
    private boolean mostrarNome = true;
    private boolean periodo = false;

    public String getInicioApresentar() {
        return (Uteis.getDataAplicandoFormatacao(inicio, "dd.MM.yyyy"));
    }

    public String getFimApresentar() {
        return (Uteis.getDataAplicandoFormatacao(fim, "dd.MM.yyyy"));
    }
    
    public TipoItemLinhaTempoContratoEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoItemLinhaTempoContratoEnum tipo) {
        this.tipo = tipo;
    }
    
    public String getTipoApresentar(){
        return getName();
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date dia) {
        this.inicio = dia;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public String getNomeResponsavel() {
        return nomeResponsavel;
    }

    public void setNomeResponsavel(String nomeResponsavel) {
        this.nomeResponsavel = nomeResponsavel;
    }

    public Double getPercentual() {
        return percentual;
    }

    public void setPercentual(Double percentual) {
        this.percentual = percentual;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public Integer getComplementoPosicao() {
        return complementoPosicao;
    }

    public void setComplementoPosicao(Integer complementoPosicao) {
        this.complementoPosicao = complementoPosicao;
    }

    public String getCss() {
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }

    public TipoOperacaoContratoEnum getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(TipoOperacaoContratoEnum tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isMostrarNome() {
        return mostrarNome;
    }

    public void setMostrarNome(boolean mostrarNome) {
        this.mostrarNome = mostrarNome;
    }

    public Double getPercentualFim() {
        return percentualFim;
    }

    public void setPercentualFim(Double percentualFim) {
        this.percentualFim = percentualFim;
    }

    public Double getTamanhoLinha() {
        return tamanhoLinha;
    }

    public void setTamanhoLinha(Double tamanhoLinha) {
        this.tamanhoLinha = tamanhoLinha;
    }

    public boolean isPeriodo() {
        return periodo;
    }

    public void setPeriodo(boolean periodo) {
        this.periodo = periodo;
    }
    
    
    
}

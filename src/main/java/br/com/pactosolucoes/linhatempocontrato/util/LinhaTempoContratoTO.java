/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.linhatempocontrato.util;

import br.com.pactosolucoes.enumeradores.SituacaoContratoEnum;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class LinhaTempoContratoTO implements Serializable{

    private List<ItemLinhaTempoContratoTO> itens = new ArrayList<ItemLinhaTempoContratoTO>();
    private boolean recorrencia = false;
    private Date inicio;
    private Date inicioLinha;
    private Date fimLinha;
    private Date fim;
    private Date fimCarenciaRenovacao;
    private Date lancamento;
    private SituacaoContratoEnum situacaoContrato;
    private Integer codigo;
    private Integer duracao;
    private Integer diasCarencia;
    private String nomePlano;
    private String nomeConsultor;
    private String nomeResponsavel;
    private List<MesLinhaTempoTO> meses;
    
    public Double getPercentualFimContrato(){
        return Uteis.obterPosicaoData(fim, inicioLinha, fimLinha);
    }
    
    public Double getPercentualInicioContrato(){
        return Uteis.obterPosicaoData(inicio, inicioLinha, fimLinha);
    }
    
    public Double getPercentualLancamentoContrato(){
        return Uteis.obterPosicaoData(lancamento, inicioLinha, fimLinha);
    }

    public String getInicioApresentar() {
        return (Uteis.getDataAplicandoFormatacao(inicio, "dd.MM.yyyy"));
    }
    
    public String getFimApresentar() {
        return (Uteis.getDataAplicandoFormatacao(fim, "dd.MM.yyyy"));
    }
    
    public String getLancamentoApresentar() {
        return (Uteis.getDataAplicandoFormatacao(lancamento, "dd.MM.yyyy"));
    }
    
    public String getFimCarenciaRenovacaoApresentar() {
        return (Uteis.getDataAplicandoFormatacao(fimCarenciaRenovacao, "dd.MM.yyyy"));
    }

    public Date getLancamento() {
        return lancamento;
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public String getNomeConsultor() {
        return nomeConsultor;
    }

    public void setNomeConsultor(String nomeConsultor) {
        this.nomeConsultor = nomeConsultor;
    }

    public String getNomeResponsavel() {
        return nomeResponsavel;
    }

    public void setNomeResponsavel(String nomeResponsavel) {
        this.nomeResponsavel = nomeResponsavel;
    }

    public List<ItemLinhaTempoContratoTO> getItens() {
        return itens;
    }

    public void setItens(List<ItemLinhaTempoContratoTO> itens) {
        this.itens = itens;
    }

    public boolean isRecorrencia() {
        return recorrencia;
    }

    public void setRecorrencia(boolean recorrencia) {
        this.recorrencia = recorrencia;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public SituacaoContratoEnum getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(SituacaoContratoEnum situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public List<MesLinhaTempoTO> getMeses() {
        return meses;
    }

    public void setMeses(List<MesLinhaTempoTO> meses) {
        this.meses = meses;
    }

    public void povoarMeses() throws Exception{
        List<Date> mesesEntreDatas = Uteis.getMesesEntreDatas(inicioLinha, fimLinha);
        meses = new ArrayList<MesLinhaTempoTO>();
        if(mesesEntreDatas.isEmpty() || mesesEntreDatas.size() == 1){
            return;
        }
        Collections.sort(mesesEntreDatas);
        for(Date dia : mesesEntreDatas){
            meses.add(new MesLinhaTempoTO(Uteis.obterPosicaoData(dia, inicioLinha, fimLinha), Uteis.getAnoData(dia), Uteis.getMesNomeReferencia(dia).substring(0, 3)));
        }
    }
    
    public Date getFimCarenciaRenovacao() {
        return fimCarenciaRenovacao;
    }

    public void setFimCarenciaRenovacao(Date fimCarenciaRenovacao) {
        this.fimCarenciaRenovacao = fimCarenciaRenovacao;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDiasCarencia() {
        return diasCarencia;
    }

    public void setDiasCarencia(Integer diasCarencia) {
        this.diasCarencia = diasCarencia;
    }

    public Date getInicioLinha() {
        return inicioLinha;
    }

    public void setInicioLinha(Date inicioLinha) {
        this.inicioLinha = inicioLinha;
    }

    public Date getFimLinha() {
        return fimLinha;
    }

    public void setFimLinha(Date fimLinha) {
        this.fimLinha = fimLinha;
    }
    
    
    
}

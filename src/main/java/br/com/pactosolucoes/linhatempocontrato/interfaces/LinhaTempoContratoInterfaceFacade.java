/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.linhatempocontrato.interfaces;

import br.com.pactosolucoes.linhatempocontrato.util.LinhaTempoContratoTO;
import java.util.Date;
import java.util.List;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface LinhaTempoContratoInterfaceFacade extends SuperInterface {
    
    public LinhaTempoContratoTO gerarLinhaTempoContrato(Integer codigoContrato, EmpresaVO empreesa, ConfiguracaoSistemaVO configuracao, Integer matricula, Date inicioLinha, Date fimLinha) throws Exception;
    
    public List<LinhaTempoContratoTO> obterLinhasTempoCliente(Integer codigoMatricula, EmpresaVO empreesa, ConfiguracaoSistemaVO configuracao) throws Exception;
}

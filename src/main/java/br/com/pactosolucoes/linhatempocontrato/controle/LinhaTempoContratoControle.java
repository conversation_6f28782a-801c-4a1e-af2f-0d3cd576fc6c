/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.linhatempocontrato.controle;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.linhatempocontrato.util.LinhaTempoContratoTO;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.faces.context.FacesContext;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 *
 * <AUTHOR>
 */
public class LinhaTempoContratoControle extends SuperControle {

    private List<LinhaTempoContratoTO> linhas = new ArrayList<LinhaTempoContratoTO>();
    private Date inicioLinha;
    private Date fimLinha;
    
    public LinhaTempoContratoControle() {
        try {
            ConfiguracaoSistemaVO configuracaoSistema = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            String matricula = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("matricula");
            if(matricula == null || matricula.isEmpty()){
                ClienteControle telaControle = (ClienteControle) JSFUtilities.getManagedBean(ClienteControle.class.getSimpleName());
                if(telaControle != null && telaControle.getClienteVO() != null && !UteisValidacao.emptyNumber(telaControle.getClienteVO().getCodigo())){
                    matricula = telaControle.getClienteVO().getMatricula();
                }
            }
            linhas = getFacade().getLinhaTempoContrato().obterLinhasTempoCliente(Integer.valueOf(matricula), getEmpresaLogado(), configuracaoSistema);
            if(linhas == null || linhas.isEmpty()){
                return;
            }
            inicioLinha = linhas.get(0).getInicioLinha();
            fimLinha = linhas.get(0).getFimCarenciaRenovacao();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    
    public void iniciarLinhaContrato(Integer codigoContrato, Integer matricula){
        try {
            ConfiguracaoSistemaVO configuracaoSistema = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);
            LinhaTempoContratoTO linhaTempoContrato = getFacade().getLinhaTempoContrato().gerarLinhaTempoContrato(codigoContrato, getEmpresaLogado(), configuracaoSistema, matricula, null, null);
            linhas = new ArrayList<LinhaTempoContratoTO>();
            linhas.add(linhaTempoContrato);
            inicioLinha = linhas.get(0).getInicioLinha();
            fimLinha = linhas.get(0).getFimCarenciaRenovacao();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    
    public boolean getMostrarHoje(){
        try {
            return Calendario.maior(fimLinha, Calendario.hoje());
        } catch (Exception e) {
            return false;
        }
        
    }
    
    public Double getPercentualHoje(){
        return Uteis.obterPosicaoData(Calendario.hoje(), inicioLinha, fimLinha);
    } 

    public List<LinhaTempoContratoTO> getLinhas() {
        return linhas;
    }

    public void setLinhas(List<LinhaTempoContratoTO> linhas) {
        this.linhas = linhas;
    }
    
    public String getHojeApresentar(){
        return (Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd.MM.yyyy"));
    }

    public Date getInicioLinha() {
        return inicioLinha;
    }

    public void setInicioLinha(Date inicioLinha) {
        this.inicioLinha = inicioLinha;
    }

    public Date getFimLinha() {
        return fimLinha;
    }

    public void setFimLinha(Date fimLinha) {
        this.fimLinha = fimLinha;
    }
    
    
}

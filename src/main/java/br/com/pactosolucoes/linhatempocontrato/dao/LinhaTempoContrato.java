/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.linhatempocontrato.dao;

import br.com.pactosolucoes.enumeradores.SituacaoContratoEnum;
import br.com.pactosolucoes.enumeradores.TipoContatoCRM;
import br.com.pactosolucoes.enumeradores.TipoOperacaoContratoEnum;
import br.com.pactosolucoes.linhatempocontrato.interfaces.LinhaTempoContratoInterfaceFacade;
import br.com.pactosolucoes.linhatempocontrato.util.ItemLinhaTempoContratoTO;
import br.com.pactosolucoes.linhatempocontrato.util.LinhaTempoContratoTO;
import br.com.pactosolucoes.linhatempocontrato.util.TipoItemLinhaTempoContratoEnum;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;

/**
 *
 * <AUTHOR>
 */
public class LinhaTempoContrato extends SuperEntidade implements LinhaTempoContratoInterfaceFacade {

    public LinhaTempoContrato() throws Exception {
        super();
    }

    public LinhaTempoContrato(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public List<LinhaTempoContratoTO> obterLinhasTempoCliente(Integer codigoMatricula, EmpresaVO empresa, ConfiguracaoSistemaVO configuracao) throws Exception {
        Date inicioLinha = null;
        Date fimLinha = null;
        List<LinhaTempoContratoTO> linhas = new ArrayList<LinhaTempoContratoTO>();
        ResultSet rs = criarConsulta("select con.codigo from contrato con\n"
                + " INNER JOIN situacaoclientesinteticodw sw ON sw.codigopessoa = con.pessoa\n"
                + " WHERE con.situacao = 'AT' AND sw.matricula = " + codigoMatricula
                + " AND con.vigenciade <= '"+Uteis.getDataJDBC(Calendario.hoje())
                + "' ORDER BY con.vigenciade, con.codigo", con);
                
        while (rs.next()) {
            LinhaTempoContratoTO linhaTempoContrato = gerarLinhaTempoContrato(rs.getInt("codigo"), empresa, configuracao, codigoMatricula, inicioLinha, fimLinha);
            linhas.add(linhaTempoContrato);
            if(inicioLinha == null){
                inicioLinha = linhaTempoContrato.getInicioLinha();
                fimLinha = linhaTempoContrato.getFimLinha();
            }
        }
        if (linhas.isEmpty()) {
            ResultSet rsUltimo = criarConsulta("select con.codigo from contrato con\n"
                    + "INNER JOIN situacaoclientesinteticodw sw ON sw.codigopessoa = con.pessoa\n"
                    + "WHERE sw.matricula = " + codigoMatricula + " ORDER BY con.codigo DESC LIMIT 1", con);
            if (rsUltimo.next()) {
                linhas.add(gerarLinhaTempoContrato(rsUltimo.getInt("codigo"), empresa, configuracao, codigoMatricula, inicioLinha, fimLinha));
            }
        }

        return linhas;
    }

    @Override
    public LinhaTempoContratoTO gerarLinhaTempoContrato(Integer codigoContrato, EmpresaVO empresa, ConfiguracaoSistemaVO configuracao, Integer matricula, Date inicioLinha, Date fimLinha) throws Exception {
        Integer carenciaRenovacao;
        if (empresa.getCarenciaRenovacao() == null || empresa.getCarencia() == 0) {
            carenciaRenovacao = configuracao.getCarenciaRenovacao();
        } else {
            carenciaRenovacao = empresa.getCarenciaRenovacao();
        }
        LinhaTempoContratoTO linha = new LinhaTempoContratoTO();
        linha.setCodigo(codigoContrato);
        StringBuilder sqlContrato = new StringBuilder();
        sqlContrato.append(" SELECT cd.numeromeses, pla.descricao as plano, situacaocontrato, con.datalancamento, ");
        sqlContrato.append(" con.vigenciaateajustada, con.vigenciade, u.nome as nomeresponsavel, ");
        sqlContrato.append(" pes.nome as nomeconsultor, regimerecorrencia FROM contrato con \n");
        sqlContrato.append(" INNER JOIN contratoduracao cd ON cd.contrato = con.codigo \n");
        sqlContrato.append(" INNER JOIN usuario u ON con.responsavelcontrato = u.codigo \n");
        sqlContrato.append(" INNER JOIN colaborador col ON col.codigo = con.consultor \n");
        sqlContrato.append(" INNER JOIN pessoa pes ON pes.codigo = col.pessoa  \n");
        sqlContrato.append(" INNER JOIN plano pla ON pla.codigo = con.plano  \n");
        sqlContrato.append(" WHERE con.codigo = ").append(codigoContrato);
        ResultSet rsContrato = criarConsulta(sqlContrato.toString(), con);
        if (rsContrato.next()) {
            linha.setCodigo(codigoContrato);
            linha.setInicio(rsContrato.getDate("vigenciade"));
            linha.setLancamento(rsContrato.getDate("datalancamento"));
            linha.setInicioLinha(inicioLinha == null ? 
                    (Calendario.maior(linha.getInicio(), linha.getLancamento()) ? linha.getLancamento() : linha.getInicio()) :
                    inicioLinha);
            linha.setFim(rsContrato.getDate("vigenciaateajustada"));
            linha.setFimCarenciaRenovacao(Uteis.somarCampoData(rsContrato.getDate("vigenciaateajustada"), Calendar.DAY_OF_MONTH, carenciaRenovacao));
            linha.setFimLinha(fimLinha == null ? linha.getFimCarenciaRenovacao() : fimLinha);
            linha.setRecorrencia(rsContrato.getBoolean("regimerecorrencia"));
            linha.setSituacaoContrato(SituacaoContratoEnum.obterPorCodigo(rsContrato.getString("situacaocontrato")));
            linha.setNomeConsultor(rsContrato.getString("nomeconsultor"));
            linha.setNomePlano(rsContrato.getString("plano"));
            linha.setNomeResponsavel(rsContrato.getString("nomeresponsavel"));
            linha.setDuracao(rsContrato.getInt("numeromeses"));
            linha.setDiasCarencia(carenciaRenovacao);
            linha.povoarMeses();
        }
        
        StringBuilder sqlOperacoes = new StringBuilder();
        sqlOperacoes.append(" select datainicioefetivacaooperacao, datafimefetivacaooperacao, descricaocalculo, observacao, responsavel, u.nome as nomeresponsavel, co.tipooperacao, ");
        sqlOperacoes.append(" valor, j.descricao from contratooperacao co \n");
        sqlOperacoes.append(" INNER JOIN usuario u ON co.responsavel = u.codigo \n");
        sqlOperacoes.append(" LEFT JOIN justificativaoperacao j ON j.codigo = co.tipojustificativa \n");
        sqlOperacoes.append(" where contrato = ").append(codigoContrato);
        ResultSet rsOperacoes = criarConsulta(sqlOperacoes.toString(), con);
        while(rsOperacoes.next()){
            ItemLinhaTempoContratoTO item = montarItem(TipoItemLinhaTempoContratoEnum.OPERACAO_CONTRATO, 
                    rsOperacoes.getDate("datainicioefetivacaooperacao"), 
                    rsOperacoes.getDate("datafimefetivacaooperacao"), linha, "operacao");
            TipoOperacaoContratoEnum tipoOperacao = TipoOperacaoContratoEnum.obterPorSigla(rsOperacoes.getString("tipooperacao"));
            if(tipoOperacao != null){
                item.setCss(tipoOperacao.name() + " operacao");
                item.setIcone(tipoOperacao.getIcone());
                item.setName(tipoOperacao.name());
                if(tipoOperacao.isPeriodo()){
                    Double percentualFim = Uteis.obterPosicaoData(item.getFim(), linha.getInicioLinha(), linha.getFimCarenciaRenovacao());
                    item.setPercentualFim(percentualFim);
                    item.setTamanhoLinha(percentualFim-item.getPercentual());
                    item.setPeriodo(true);
                }
            }
            linha.getItens().add(item);
        }

        ResultSet rsParcelas = criarConsulta(" select descricao, datavencimento, situacao from movparcela where contrato = " + codigoContrato + " ORDER BY datavencimento", con);
        while (rsParcelas.next()) {
            ItemLinhaTempoContratoTO item = montarItem(TipoItemLinhaTempoContratoEnum.PARCELA, rsParcelas.getDate("dataVencimento"),
                    null, linha, "financeiro");
            item.setComplemento(rsParcelas.getString("descricao"));
            item.setCss(item.getCss() + (rsParcelas.getString("situacao").equals("PG") ? " paga" : " aberta") + " financeiro");
            item.setMostrarNome(false);
            linha.getItens().add(item);
        }

        // Montando por último os pagamentos, para que os recibos fiquem visíveis no lugar das parcelas, caso o pagamento seja feito no dia do vencimento da parcela.
        ResultSet rsPagamento = criarConsulta(" select distinct rp.data, mp.recibopagamento from movpagamento mp\n" +
                                            " INNER JOIN recibopagamento rp on rp.codigo = mp.recibopagamento\n" +
                                            " INNER JOIN pagamentomovparcela pmp on pmp.movpagamento = mp.codigo\n" +
                                            " INNER JOIN movparcela par ON par.codigo = pmp.movparcela AND par.contrato = " 
                                                            + codigoContrato + " ORDER BY data", con);
        
        while (rsPagamento.next()) {
            ItemLinhaTempoContratoTO item = montarItem(TipoItemLinhaTempoContratoEnum.PAGAMENTO, 
                    rsPagamento.getDate("data"), null, linha, "financeiro");
            item.setComplemento("<br/>Recibo "+rsPagamento.getInt("recibopagamento"));
            linha.getItens().add(item);
        }
        
        ResultSet rsContatos = criarConsulta(" SELECT hc.dia, tipocontato FROM historicocontato hc\n" +
                "INNER JOIN situacaoclientesinteticodw sw ON hc.cliente = sw.codigocliente \n" +
                "WHERE sw.matricula = " + matricula + " AND hc.dia BETWEEN  '"
                +Uteis.getDataJDBC(linha.getInicioLinha())+"' AND '"
                +Uteis.getDataJDBC(linha.getFim())+"' ORDER BY hc.dia", con);
        while (rsContatos.next()) {
            ItemLinhaTempoContratoTO item = montarItem(TipoItemLinhaTempoContratoEnum.RELACIONAMENTO, rsContatos.getDate("dia"), 
                    null, linha, "relacionamento");
            TipoContatoCRM tipo = TipoContatoCRM.obterPorSigla(rsContatos.getString("tipocontato"));
            tipo = tipo == null ? TipoContatoCRM.CONTATO_PESSOAL : tipo;
            item.setCss(tipo.name() + " relacionamento");
            item.setIcone(tipo.getIcone());
            item.setName(tipo.name());
            linha.getItens().add(item);
        }
        
        linha.setItens(Ordenacao.ordenarLista(linha.getItens(), "percentual"));
        //evitar itens sobrepostos
        Map<Integer, Integer> mapaOcupados = new HashMap<Integer, Integer>();
        mapaOcupados.put(linha.getPercentualFimContrato().intValue(), 1);
        mapaOcupados.put(linha.getPercentualInicioContrato().intValue(), 1);
        mapaOcupados.put(linha.getPercentualLancamentoContrato().intValue(), 1);
        return linha;
    }

    public ItemLinhaTempoContratoTO montarItem(TipoItemLinhaTempoContratoEnum tipo, 
            Date inicio, Date fim, LinhaTempoContratoTO linha, String categoria) {
        ItemLinhaTempoContratoTO item = new ItemLinhaTempoContratoTO();
        item.setInicio(inicio);
        item.setFim(fim);
        Double percentual = Uteis.obterPosicaoData(inicio, linha.getInicioLinha(), linha.getFimCarenciaRenovacao());
        item.setPercentual(percentual);
        item.setTipo(tipo);
        item.setCss(tipo.getCss() + " " + categoria);
        item.setIcone(tipo.getIcone());
        item.setName(tipo.name());
        return item;
    }
}

package br.com.pactosolucoes.conviteaulaexperimental.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalVO;

import java.util.Date;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

/**
 * Created by ulisses on 25/01/2016.
 */
public class TipoConviteAulaExperimentalJSON extends SuperJSON {

    private Integer codigo = 0;
    private String descricao;
    private String vigenciaInicial;
    private String vigenciaFinal;
    private boolean aulasAgendadasEmDiasSeguido = true;
    private Integer quantidadeAulaExperimental;
    private Integer quantidadeConviteAlunoPodeEnviar = 0;
    private Integer empresa;
    private String dataLancamento;
    private Integer totalConvitesJaEnviado;


    public TipoConviteAulaExperimentalJSON(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO){
        this.codigo = tipoConviteAulaExperimentalVO.getCodigo();
        this.descricao = tipoConviteAulaExperimentalVO.getDescricao();
        this.vigenciaInicial = Uteis.getData(tipoConviteAulaExperimentalVO.getVigenciaInicial());
        this.vigenciaFinal  = Uteis.getData(tipoConviteAulaExperimentalVO.getVigenciaFinal());
        this.aulasAgendadasEmDiasSeguido = tipoConviteAulaExperimentalVO.isAulasAgendadasEmDiasSeguido();
        this.quantidadeAulaExperimental = tipoConviteAulaExperimentalVO.getQuantidadeAulaExperimental();
        this.quantidadeConviteAlunoPodeEnviar =  tipoConviteAulaExperimentalVO.getQuantidadeConviteAlunoPodeEnviar();
        this.empresa = tipoConviteAulaExperimentalVO.getEmpresaVO().getCodigo();
        this.dataLancamento = Uteis.getData(tipoConviteAulaExperimentalVO.getDataLancamento());
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getVigenciaInicial() {
        return vigenciaInicial;
    }

    public void setVigenciaInicial(String vigenciaInicial) {
        this.vigenciaInicial = vigenciaInicial;
    }

    public String getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(String vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }

    public boolean isAulasAgendadasEmDiasSeguido() {
        return aulasAgendadasEmDiasSeguido;
    }

    public void setAulasAgendadasEmDiasSeguido(boolean aulasAgendadasEmDiasSeguido) {
        this.aulasAgendadasEmDiasSeguido = aulasAgendadasEmDiasSeguido;
    }

    public Integer getQuantidadeAulaExperimental() {
        return quantidadeAulaExperimental;
    }

    public void setQuantidadeAulaExperimental(Integer quantidadeAulaExperimental) {
        this.quantidadeAulaExperimental = quantidadeAulaExperimental;
    }

    public Integer getQuantidadeConviteAlunoPodeEnviar() {
        return quantidadeConviteAlunoPodeEnviar;
    }

    public void setQuantidadeConviteAlunoPodeEnviar(Integer quantidadeConviteAlunoPodeEnviar) {
        this.quantidadeConviteAlunoPodeEnviar = quantidadeConviteAlunoPodeEnviar;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getTotalConvitesJaEnviado() {
        return totalConvitesJaEnviado;
    }

    public void setTotalConvitesJaEnviado(Integer totalConvitesJaEnviado) {
        this.totalConvitesJaEnviado = totalConvitesJaEnviado;
    }
}

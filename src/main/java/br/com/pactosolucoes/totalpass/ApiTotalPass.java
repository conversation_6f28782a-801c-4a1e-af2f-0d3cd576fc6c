package br.com.pactosolucoes.totalpass;

import acesso.webservice.AcessoControle;
import acesso.webservice.Validador;
import negocio.facade.jdbc.arquitetura.FacadeFactory;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import com.google.gson.JsonObject;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.LogTotalPassVO;
import negocio.interfaces.arquitetura.LogTotalPassInterfaceFacade;
import servicos.propriedades.PropsService;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.logging.Logger;

public class ApiTotalPass {
    private static final String CONTENT_TYPE_JSON = "application/json";
    private static final String ACCEPT_JSON = "application/json";
    private static final String URL_API_STAGING = "https://staging.totalpass.com/api/v1/track_usages";
    private static final String URL_VALIDATE_STAGING = "https://staging.totalpass.com/api/v1/track_usages/validate";

    private static final Logger LOGGER = Logger.getLogger(ApiTotalPass.class.getName());

    public LogTotalPassVO consumirAPI(String origem, Integer codigopessoa, Integer empresa, String apikey, String cpf, String codigototalpass, boolean usarValidad) throws Exception {

        LogTotalPassVO registroEntrada = new LogTotalPassVO();
        InetAddress enderecoIP = InetAddress.getLocalHost();
        registroEntrada.setPessoa(codigopessoa);
        registroEntrada.setDataRegistroInstant(Instant.now());
        registroEntrada.setTempoResposta(new Long(0));
        registroEntrada.setEmpresa(empresa);
        registroEntrada.setOrigem(origem);
        registroEntrada.setJson(null);
        registroEntrada.setIp(enderecoIP.getHostAddress());
        if(usarValidad && apikey != null && apikey.startsWith("TESTES_")) {
            registroEntrada.setTipo("VALIDATE EM TESTE");
        }
        if(usarValidad && apikey != null && !apikey.startsWith("TESTES_")){
            registroEntrada.setTipo("VALIDATE EM PRODUÇÃO");
        }
        if(!usarValidad && apikey != null && !apikey.startsWith("TESTES_")){
            registroEntrada.setTipo("QUEIMA EM PRODUÇÃO");
        }
        if (!usarValidad && apikey != null && apikey.startsWith("TESTES_")){
            registroEntrada.setTipo("QUEIMA EM TESTE");
        }


        try {

            String uri = usarValidad ? PropsService.getPropertyValue(PropsService.totalPassApiValidate) :
                    PropsService.getPropertyValue(PropsService.totalPassApi);
            if(apikey != null && apikey.startsWith("TESTES_")){
                apikey = apikey.replace("TESTES_", "");
                uri = usarValidad ? URL_VALIDATE_STAGING : URL_API_STAGING;
            }
            System.out.println(uri);
            URL url = new URL(uri);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", CONTENT_TYPE_JSON);
            connection.setRequestProperty("Accept", ACCEPT_JSON);
            connection.setRequestProperty("x-api-key", apikey);
            connection.setDoOutput(true);

            JsonObject requestBody = new JsonObject();
            JsonObject data = new JsonObject();
            JsonObject attributes = new JsonObject();

            attributes.addProperty("type", "cpf");
            attributes.addProperty("identifier", cpf);
            attributes.addProperty("service_provider_code", codigototalpass);

            data.addProperty("type", "string");
            data.add("attributes", attributes);

            requestBody.add("data", data);

            Instant startTime = Instant.now();

            try (OutputStream outputStream = connection.getOutputStream()) {
                outputStream.write(requestBody.toString().getBytes());
                outputStream.flush();
            }

            int responseCode = connection.getResponseCode();
            Instant endTime = Instant.now();
            Duration elapsedTime = Duration.between(startTime, endTime);
            LOGGER.info("Tempo de resposta da requisição: " + elapsedTime.toMillis() + " milissegundos");


            registroEntrada.setUri(uri);
            registroEntrada.setApikey(apikey);
            String jsonEntrada = (requestBody.toString());
            registroEntrada.setJson(jsonEntrada);
            registroEntrada.setTempoResposta(elapsedTime.toMillis());


            if (responseCode >= 200 && responseCode < 205) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }

                    registroEntrada.setRespostaApi(response.toString());
                    registroEntrada.setResposta(String.valueOf(responseCode));
                    return registroEntrada;
                }
            } else {
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    StringBuilder errorResponse = new StringBuilder();
                    String errorLine;
                    while ((errorLine = errorReader.readLine()) != null) {
                        errorResponse.append(errorLine);
                    }
                    registroEntrada.setRespostaApi(errorResponse.toString());
                } catch (IOException e) {
                    e.printStackTrace();
                }
                registroEntrada.setResposta(String.valueOf(responseCode));
                return registroEntrada;
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    public static FacadeFactory getFacade() throws Exception {
        return FacadeManager.getFacade();
    }

}

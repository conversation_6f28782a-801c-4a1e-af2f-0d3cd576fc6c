/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.autorizacaocobranca.controle;

import br.com.pactosolucoes.autorizacaocobranca.controle.util.ValidaBandeira;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.contrato.ContratoControle;
import controle.crm.HistoricoContatoControle;
import edu.emory.mathcs.backport.java.util.Arrays;
import negocio.comuns.arquitetura.PermissaoAcessoMenuVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.maxiPago.AdquirenteMaxiPagoEnum;
import servicos.impl.vindi.VindiService;
import servicos.propriedades.PropsService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class AutorizacaoCobrancaControle extends SuperControle {

    private ClienteVO cliente;
    private ColaboradorVO colaborador;

    private List<AutorizacaoCobrancaVO> listaAutorizacoes;
    private AutorizacaoCobrancaVO autorizacao = new AutorizacaoCobrancaClienteVO();
    private List<ConvenioCobrancaVO> convenioVOs = new ArrayList<ConvenioCobrancaVO>();
    private List<SelectItem> convenios = new ArrayList<SelectItem>();
    private List<OperadorasExternasAprovaFacilEnum> bandeirasCartao = new ArrayList<OperadorasExternasAprovaFacilEnum>();
    private List<SelectItem> tiposACobrar = new ArrayList<SelectItem>();
    private boolean negociacao = false;
    private String bandeiraCartao;
    private boolean contaValida = true;
    private boolean permiteEditarProdutos = true;
    private PermissaoAcessoMenuVO permissaoAcessoMenuVO;
    private String imagemCartao = "semCartao";
    private boolean cartaoSelecionado = false;
    private boolean debitoSelecionado = false;
    private boolean boletoSelecionado = false;
    private boolean apresentarEmpresa = true;

    private List<String> tiposProdutosSelecionados;
    private AutorizacaoCobrancaVO autorizacaoEditarTipoProduto;
    private AutorizacaoCobrancaVO autorizacaoRemover;
    private String onComplete;
    private String gatewayTokenVindi;
    private boolean cadastrarUsandoVindiPublica = false;
    private String formaPagamentoSelect;
    private String styleClassCartao = "white";
    private String styleClassCartaoRodape = "white";
    private String styleClassCartaoTitles = "titleGrey";
    private AutorizacaoCobrancaClienteVO autoCliente = new AutorizacaoCobrancaClienteVO();
    private AutorizacaoCobrancaColaboradorVO autoColaborador = new AutorizacaoCobrancaColaboradorVO();
    private boolean verificacaoCartaoSucesso = false;
    private String verificacaoCartaoMsg;
    private boolean origemVendaDePlano;
    private boolean permiteCadastrarCartaoMesmoAssim = true;
    private String reRender;

    public AutorizacaoCobrancaControle() {
        setApresentarEmpresa(true);
    }

    public AutorizacaoCobrancaVO getAutorizacao() {
        return autorizacao;
    }

    public void init() throws Exception {
        try {
            setApresentarEmpresa(getFacade().getEmpresa().consultarTodosCodigos().size() > 1);
        } catch (Exception ex) {
            setApresentarEmpresa(true);
        }

        try {
            this.setMensagemDetalhada("", "");
            this.setListaAutorizacoes(this.carregarAutorizacoesCliente());
            if (cliente != null) {
                this.setAutorizacao(new AutorizacaoCobrancaClienteVO(true));

                if (!UteisValidacao.emptyString(cliente.getEmpresa().getTiposProduto())) {
                    this.getAutorizacao().setListaObjetosACobrar(cliente.getEmpresa().getTiposProduto());
                }

            } else if (colaborador != null) {
                this.setAutorizacao(new AutorizacaoCobrancaColaboradorVO(true));
            }
        } catch (Exception e) {
            this.setListaAutorizacoes(new ArrayList<AutorizacaoCobrancaVO>());
        }
        carregarTiposACobrar();
        selecionarAutorizacao();
    }

    private void selecionarAutorizacao() {
        this.autorizacao.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
    }

    public void abrirContatoCliente() {
        try {
            HistoricoContatoControle histContatoControle = (HistoricoContatoControle) JSFUtilities.getManagedBean(HistoricoContatoControle.class.getSimpleName());
            histContatoControle.novo();
            ClienteVO cli = getFacade().getCliente().consultarPorChavePrimaria(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            histContatoControle.inicializarCliente(Calendario.hoje(), cli);
            setMsgAlert("abrirPopup('realizarContatoForm.jsp', 'RealizarContatoform', 664, 635);");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
            setMensagemDetalhada("", "");
        }
    }

    public void carregarConveniosTela() {
        try {
            if (autorizacao.getTipoAutorizacao() != null &&
                    autorizacao.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                if (cliente != null) {
                    getFacade().getAutorizacaoCobrancaCliente().validarQuantidadeCartoesCadastrados(cliente.getCodigo(), autorizacao);
                } else if (colaborador != null) {
                    getFacade().getAutorizacaoCobrancaColaborador().validarQuantidadeCartoesCadastrados(colaborador.getCodigo(), autorizacao);
                }
            }
            carregarConvenios();
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private void carregarConvenios() throws Exception {
        limparMsg();
        setMensagemID("");
        convenios.clear();
        TipoConvenioCobrancaEnum[] tipos = autorizacao.getTipoAutorizacao() != null ? autorizacao.getTipoAutorizacao().getTiposConvenio() : null;
        if (tipos != null) {
            Integer codEmpresa = 0;
            if (cliente != null) {
                codEmpresa = cliente.getEmpresa().getCodigo();
            } else if (colaborador != null) {
                codEmpresa = colaborador.getEmpresa().getCodigo();
            } else {
                if (autorizacao != null && autorizacao.getConvenio() != null && autorizacao.getConvenio().getCodigo() > 0) {
                    codEmpresa = autorizacao.getConvenio().getEmpresa().getCodigo();
                }
            }
            convenioVOs = getFacade().getConvenioCobranca().consultarPorTiposESituacao(tipos, codEmpresa, SituacaoConvenioCobranca.ATIVO, false, Uteis.NIVELMONTARDADOS_TODOS, false);
            if (autorizacao.getConvenio().getSituacao().equals(SituacaoConvenioCobranca.INATIVO)) {
                convenioVOs.add(autorizacao.getConvenio());
            }
            convenioVOs = Ordenacao.ordenarLista(convenioVOs, "descricao");
            convenios = JSFUtilities.getSelectItemListFrom(convenioVOs, "descricao", "codigo", true, false);
            convenios = prepararListaConveniosTela(convenios);

            if (cliente != null) {
                if (cliente.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA)) {
                    if (UteisValidacao.emptyString(autorizacao.getCpfTitular()) && !UteisValidacao.emptyString(cliente.getPessoa().getCfp())) {
                        autorizacao.setCpfTitular(cliente.getPessoa().getCfp());
                    }
                } else {
                    if (UteisValidacao.emptyString(autorizacao.getCpfTitular()) && !UteisValidacao.emptyString(cliente.getPessoa().getCnpj())) {
                        autorizacao.setCpfTitular(Uteis.formatarCpfCnpj(cliente.getPessoa().getCnpj(), true));
                    }
                }
            } else if (colaborador != null) {
                if (colaborador.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA)) {
                    if (UteisValidacao.emptyString(autorizacao.getCpfTitular()) && !UteisValidacao.emptyString(colaborador.getPessoa().getCfp())) {
                        autorizacao.setCpfTitular(colaborador.getPessoa().getCfp());
                    }
                } else {
                    if (autorizacao.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
                        if (UteisValidacao.emptyString(autorizacao.getCpfTitular()) && !UteisValidacao.emptyString(colaborador.getPessoa().getCnpj())) {
                            autorizacao.setCpfTitular(colaborador.getPessoa().getCnpj().replaceAll("[/.-]", ""));
                        }
                    } else {
                        autorizacao.setCpfTitular("");
                    }
                }
            }

            if (autorizacao.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)) {
                autorizacao.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
            }
        }
        setarConvenio();
    }

    public void setarConvenio() throws Exception {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        Boolean formapagamento = false;
        formapagamento = Boolean.parseBoolean(request.getParameter("negociacao"));

        ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
        if (formapagamento && contratoControle != null) {
            List<ConvenioCobrancaVO> convenioCobrancaVOS = getFacade().getConvenioCobranca().consultarPorTipoConvenio(contratoControle.getContratoVO().getPlanoCondicaoPagamento().getCondicaoPagamento().getTipoConvenioCobranca().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            autorizacao.setConvenio(convenioCobrancaVOS.get(0));
        }
        setMensagemID("");

        Integer empresa = 0;
        if (cliente != null) {
            empresa = cliente.getEmpresa().getCodigo();
        } else if (colaborador != null) {
            empresa = colaborador.getEmpresa().getCodigo();
        }

        if (!UteisValidacao.emptyNumber(autorizacao.getConvenio().getCodigo())) {
            ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(autorizacao.getConvenio().getCodigo(), empresa, Uteis.NIVELMONTARDADOS_TODOS);
            autorizacao.setConvenio(convenioCobrancaVO);
            if (autorizacao.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
                autorizacao.setBanco(convenioCobrancaVO.getBanco());
            }
        }

        switch (autorizacao.getTipoAutorizacao().getId()) {
            case 1:
                setCartaoSelecionado(true);
                setDebitoSelecionado(false);
                setBoletoSelecionado(false);
                break;
            case 2:
                setCartaoSelecionado(false);
                setDebitoSelecionado(true);
                setBoletoSelecionado(false);
                break;
            case 3:
                setCartaoSelecionado(false);
                setDebitoSelecionado(false);
                setBoletoSelecionado(true);
        }

        if (autorizacao.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                !UteisValidacao.emptyNumber(autorizacao.getConvenio().getCodigo())) {
            carregarBandeirasCartao();
        }
    }

    public List<TipoObjetosCobrarEnum> getTiposObjetosCobrar() {
        TipoObjetosCobrarEnum[] tipos = (TipoObjetosCobrarEnum[]) Arrays.copyOfRange(TipoObjetosCobrarEnum.values(), 1, TipoObjetosCobrarEnum.values().length);
        List<TipoObjetosCobrarEnum> list = new ArrayList<TipoObjetosCobrarEnum>();
        list.addAll(Arrays.asList(tipos));
        if (colaborador != null) {
            for (TipoObjetosCobrarEnum tipo : tipos) {
                if (tipo.equals(TipoObjetosCobrarEnum.APENAS_PLANOS) ||
                        tipo.equals(TipoObjetosCobrarEnum.CONTRATOS_RENOVAVEIS_AUTO)) {
                    list.remove(tipo);
                }
            }
        }
        return list;
    }

    private void carregarTiposACobrar() throws Exception {
        tiposACobrar = JSFUtilities.getSelectItemListFromEnum(TipoObjetosCobrarEnum.class, "descricao", false);
        tiposACobrar.remove(0);

        if (colaborador != null) {
            List<SelectItem> temp = new ArrayList<SelectItem>(tiposACobrar);
            for (SelectItem item : temp) {
                if (item.getValue().equals(TipoObjetosCobrarEnum.APENAS_PLANOS) ||
                        item.getValue().equals(TipoObjetosCobrarEnum.CONTRATOS_RENOVAVEIS_AUTO)) {
                    tiposACobrar.remove(item);
                }
            }
        }
    }

    private void carregarBandeirasCartao() throws Exception {
        bandeirasCartao.clear();
        int position = 0;
        int i = 0;
        for (ConvenioCobrancaVO co : convenioVOs) {
            if (co.getCodigo() == autorizacao.getConvenio().getCodigo()) {
                i = position;
            }
            position += 1;
        }
        if (i != -1) {
            List<OperadorasExternasAprovaFacilEnum> operadorasDoConvenio = OperadorasExternasAprovaFacilEnum.operadorasConvenio(convenioVOs.get(i).getTipo());
            List<OperadoraCartaoVO> todasOperadorasCredito = getFacade().getOperadoraCartao().consultarPorTipo(true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for (OperadoraCartaoVO operadoraCredito : todasOperadorasCredito) {
                for (OperadorasExternasAprovaFacilEnum operadoraConvenio : operadorasDoConvenio) {
                    if (
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoAPF() && (convenioVOs.get(i).getTipo() == null || !convenioVOs.get(i).getTipo().isTransacaoOnline()))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoVindi()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoCielo()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoMaxiPago()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoERede()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoFitnessCard()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_FITNESS_CARD))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoGetNet()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoStoneOnline()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoStoneOnlineV5()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoMundiPagg()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoPagarMe()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoPagBank()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGBANK))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoStripe()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE)
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoPagoLivre()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoFacilitePay()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoPinBank()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoCeopag()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_CEOPAG))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoOnePayment()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT))
                            ||
                            (operadoraConvenio == operadoraCredito.getCodigoIntegracaoDCCCaixaOnline()
                                    && convenioVOs.get(i).getTipo() != null
                                    && convenioVOs.get(i).getTipo().equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE))
                            )) {
                        bandeirasCartao.add(operadoraConvenio);
                        break;
                    }

                }

            }
        }
    }

    public void setAutorizacao(AutorizacaoCobrancaVO autorizacao) {
        this.autorizacao = autorizacao;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
        this.colaborador = null;
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
        this.cliente = null;
    }

    public List<AutorizacaoCobrancaVO> getListaAutorizacoes() {
        if (listaAutorizacoes == null) {
            listaAutorizacoes = new ArrayList<AutorizacaoCobrancaVO>();
        }
        return listaAutorizacoes;
    }

    public void setListaAutorizacoes(List<AutorizacaoCobrancaVO> listaAutorizacoes) {
        this.listaAutorizacoes = listaAutorizacoes;
    }

    public List<OperadorasExternasAprovaFacilEnum> getBandeirasCartao() {
        return bandeirasCartao;
    }

    public void setBandeirasCartao(List<OperadorasExternasAprovaFacilEnum> bandeirasCartao) {
        this.bandeirasCartao = bandeirasCartao;
    }

    public List<SelectItem> getTiposAutorizacao() {
        List<SelectItem> lista = new ArrayList<>();
        for (TipoAutorizacaoCobrancaEnum tipo : TipoAutorizacaoCobrancaEnum.values()) {
            if (tipo.equals(TipoAutorizacaoCobrancaEnum.PIX) || tipo.equals(TipoAutorizacaoCobrancaEnum.NENHUM)) {
                continue;
            }
            lista.add(new SelectItem(tipo, tipo.getDescricao()));
        }
        lista.add(0,new SelectItem(TipoAutorizacaoCobrancaEnum.NENHUM, "(Selecionar cobrança)"));
        return lista;
    }

    public List<SelectItem> getTiposACobrar() {
        return tiposACobrar;
    }

    public void setTiposACobrar(List<SelectItem> tiposACobrar) {
        this.tiposACobrar = tiposACobrar;
    }

    public List<SelectItem> getConvenios() {
        return convenios;
    }

    public void setConvenios(List<SelectItem> convenios) {
        this.convenios = convenios;
    }

    public boolean isNegociacao() {
        return negociacao;
    }

    public void setNegociacao(boolean negociacao) {
        this.negociacao = negociacao;
    }

    public void selecionaOperadora() {
        String tmp = request().getParameter("operadora");
        if (tmp != null) {
            int codBandeira = Integer.valueOf(tmp);
            autorizacao.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(codBandeira));
        }
    }

    public void selecionarBandeiraNumeroCartao() {
        try {
            if (bandeiraCartao != null) {
                autorizacao.setOperadoraCartao(Enum.valueOf(OperadorasExternasAprovaFacilEnum.class, bandeiraCartao.toUpperCase()));
            }
        } catch (Exception ignored) {
        }
    }

    public void buscaBandeiraCartaoOperadora() {
        try {
            inicializarCard();
            String numeroCartao = autorizacao.getNumeroCartao().replaceAll(" ", "");
            if (ValidaBandeira.numeroCartaoValido(numeroCartao)) {
                ValidaBandeira.Bandeira bandeiraCard = ValidaBandeira.buscarBandeira(numeroCartao);
                bandeiraCartao = String.valueOf(bandeiraCard);
                autorizacao.setOperadoraCartao(Enum.valueOf(OperadorasExternasAprovaFacilEnum.class, bandeiraCartao.toUpperCase()));
                imagemCartao = bandeiraCard.getImagem();
            } else {
                imagemCartao = "semCartao";
            }

        } catch (Exception ignored) {
        }
    }

    public void setBandeiraCartaoOperadora() {
        if (!UteisValidacao.emptyString(bandeiraCartao)) {
            autorizacao.setOperadoraCartao(Enum.valueOf(OperadorasExternasAprovaFacilEnum.class, bandeiraCartao.toUpperCase()));
            imagemCartao = autorizacao.getOperadoraCartao().getImagem();
        }
    }

    public List<SelectItem>  getBandeirasAprovaFacil(){
        return OperadorasExternasAprovaFacilEnum.getSelectListTipo();
    }

    public List<AutorizacaoCobrancaVO> carregarAutorizacoesCliente() throws Exception {
        List<AutorizacaoCobrancaVO> lista = new ArrayList<AutorizacaoCobrancaVO>();

        if (cliente != null) {
            lista.addAll(getFacade().getAutorizacaoCobrancaCliente().consultarPorCliente(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        } else if (colaborador != null) {
            lista.addAll(getFacade().getAutorizacaoCobrancaColaborador().consultarPorColaborador(colaborador.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        }
        for (AutorizacaoCobrancaVO auto : lista) {
            String numeroMasc = (!UteisValidacao.emptyString(auto.getNumeroCartao())) ? APF.getCartaoMascarado(auto.getNumeroCartao()) : "";
            auto.setCartaoMascarado(auto.getTipoAutorizacao() == TipoAutorizacaoCobrancaEnum.CARTAOCREDITO ? numeroMasc : "");
        }
        return lista;
    }

    public void novo() throws Exception {
        limparMsg();
        autorizacao.setEdit(false);
        autorizacao.setNovoObj(true);
        clear();
        init();
        limparDadosDoCartao();
        autorizacao.setOrigemCobrancaEnum(OrigemCobrancaEnum.ZW_MANUAL_AUTORIZACAO_COBRANCA);

        if (cliente != null && !UteisValidacao.emptyString((cliente.getEmpresa().getTiposProduto()))) {
            permiteEditarProdutos = false;
        }
        TipoConvenioCobrancaEnum tipo = apresentarConvenioPreenchimento();
        if (tipo != null) {
            List<ConvenioCobrancaVO> convenios = new ArrayList<ConvenioCobrancaVO>();
            if (cliente != null) {
                convenios = getFacade().getConvenioCobranca().consultarPorEmpresaSituacaoTipoAutorizacao(cliente.getEmpresa().getCodigo(), SituacaoConvenioCobranca.ATIVO, tipo.getTipoAutorizacao(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else if (colaborador != null) {
                convenios = getFacade().getConvenioCobranca().consultarPorEmpresaSituacaoTipoAutorizacao(colaborador.getEmpresa().getCodigo(), SituacaoConvenioCobranca.ATIVO, tipo.getTipoAutorizacao(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (convenios.size() > 0) {
                autorizacao.setConvenio(convenios.get(0));
                autorizacao.setTipoAutorizacao(tipo.getTipoAutorizacao());
                carregarConvenios();
                setarConvenio();
            }
        }
    }

    public void editarTipoACobrar() {
        try {
            setMensagemDetalhada("", "");
            limparMsg();
            setMsgAlert("");
            String msg = "";

            if (colaborador != null) {
                AutorizacaoCobrancaColaboradorVO autoColaborador = (AutorizacaoCobrancaColaboradorVO) JSFUtilities.getFromRequest("autorizacao");

                AutorizacaoCobrancaVO autorizacaoCobrancaVO = getFacade().getAutorizacaoCobrancaColaborador().consultarPorChavePrimaria(autoColaborador.getCodigo());
                autoColaborador.setObjetoVOAntesAlteracao(autorizacaoCobrancaVO);
                autorizacaoCobrancaVO.setCartaoMascarado(autoColaborador.getCartaoMascarado());
                if (!autoColaborador.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                        getFacade().getAutorizacaoCobrancaColaborador().existeOutraAutorizacaoParecidaParaOMesmoTipo(autoColaborador)) {
                    throw new ConsistirException("Já existe uma Autorização com o mesmo 'tipo de parcelas a cobrar'");
                }
                if (autoColaborador.getTipoACobrar().equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS) && UteisValidacao.emptyString(autoColaborador.getListaObjetosACobrar())) {
                    throw new ConsistirException("Não foi informado nenhum tipo de produto a ser cobrado. Por favor adicione algum produto e tente novamente");
                }
                getFacade().getAutorizacaoCobrancaColaborador().alterarTipoACobrar(autoColaborador);
                msg = "Tipo a cobrar alterado com sucesso!";
                montarSucessoGrowl(msg);
                setSucesso(true);
                setAtencao(true);


                try {
                    registrarLogAutorizacaoCobrancaColaborador(autoColaborador);
                } catch (Exception e) {
                    montarErro(e);
                    setMsgAlert(getMensagemNotificar());
                }
            } else {
                AutorizacaoCobrancaClienteVO autoCliente = (AutorizacaoCobrancaClienteVO) JSFUtilities.getFromRequest("autorizacao");

                AutorizacaoCobrancaVO autorizacaoCobrancaVO = getFacade().getAutorizacaoCobrancaCliente().consultarPorChavePrimaria(autoCliente.getCodigo());
                autoCliente.setObjetoVOAntesAlteracao(autorizacaoCobrancaVO);
                autorizacaoCobrancaVO.setCartaoMascarado(autoCliente.getCartaoMascarado());
                if (!autoCliente.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                        getFacade().getAutorizacaoCobrancaCliente().existeOutraAutorizacaoParecidaParaOMesmoTipo(autoCliente)) {
                    throw new ConsistirException("Já existe uma Autorização com o mesmo 'tipo de parcelas a cobrar'");
                }
                if (autoCliente.getTipoACobrar().equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS) && UteisValidacao.emptyString(autoCliente.getListaObjetosACobrar())) {
                    throw new ConsistirException("Não foi informado nenhum tipo de produto a ser cobrado. Por favor adicione algum produto e tente novamente");
                }
                getFacade().getAutorizacaoCobrancaCliente().alterarTipoACobrar(autoCliente);
                msg = "Tipo a cobrar alterado com sucesso!";
                montarSucessoGrowl(msg);
                setSucesso(true);
                setAtencao(true);


                try {
                    registrarLogAutorizacaoCobranca(autoCliente);
                } catch (Exception e) {
                    montarErro(e);
                    setMsgAlert(getMensagemNotificar());
                }
            }

        } catch (Exception ex) {
            montarErro(ex);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void registrarLogAutorizacaoCobranca(AutorizacaoCobrancaClienteVO auto) throws Exception {
        try {
            registrarLogObjetoVO(auto, auto.getCodigo(), "AUTORIZACAOCOBRANCACLIENTE", auto.getCliente().getPessoa().getCodigo());
            auto.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("AUTORIZACAOCOBRANCACLIENTE", auto.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE  ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        auto.registrarObjetoVOAntesDaAlteracao();
    }

    public void registrarLogAutorizacaoCobrancaColaborador(AutorizacaoCobrancaColaboradorVO auto) throws Exception {
        try {
            registrarLogObjetoVO(auto, auto.getCodigo(), "AUTORIZACAOCOBRANCACOLABORADOR", auto.getColaborador().getPessoa().getCodigo());
            auto.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("AUTORIZACAOCOBRANCACOLABORADOR", auto.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE  ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        auto.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirAutorizacaoIdVindi() {
        this.getAutorizacao().setUsarIdVindiPessoa(true);
        this.getAutorizacao().setValidadeCartao("");
        this.getAutorizacao().setNumeroCartao("");
        this.getAutorizacao().setCartaoMascarado("");
        this.getAutorizacao().setOperadoraCartao(null);
        this.getAutorizacao().setNomeTitularCartao("");
        confirmar();
    }

    public void confirmarOrigemVendaDePlano(){
        origemVendaDePlano = true;
        confirmar();
    }

    public void confirmarOrigemAluno(){
        origemVendaDePlano = false;
        confirmar();
    }

    public void confirmar() {
        try {
            limparMsg();
            setMsgAlert("");
            setReRender("dados, panelModalVerificacaoCartao,modalVerificacaoCartao");
            getFacade().getAutorizacaoCobrancaCliente().possuiPermissao("INCLUIR");
            AutorizacaoCobrancaClienteVO autoCliente = new AutorizacaoCobrancaClienteVO();
            AutorizacaoCobrancaColaboradorVO autoColaborador = new AutorizacaoCobrancaColaboradorVO();
            if (cliente != null) {
                autoCliente = (AutorizacaoCobrancaClienteVO) autorizacao;
                autoCliente.setCliente(cliente);

                //Novo obj incrementa a ordem, edição não
                if (UteisValidacao.emptyNumber(autoCliente.getCodigo())) {
                    autoCliente.setOrdem(getListaAutorizacoes().size() + 1);
                }
                setAutoCliente(autoCliente);
                setAutoColaborador(autoColaborador);
                if (autoCliente.isTipoBoleto() && autoCliente.getTipoACobrar().equals(TipoObjetosCobrarEnum.NENHUM)){
                    autoCliente.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
                }
                if (autorizacao.getConvenio() != null && autorizacao.getConvenio().getCodigo() != null && !autorizacao.getConvenio().getCodigo().equals(0)) {
                    autorizacao.setConvenio(getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(autorizacao.getConvenio().getCodigo(), cliente.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
                autorizacao.setDesabilitarValidacaoValidade(!cliente.getEmpresa().isValidarVencimentoCartaoAutorizacao());
                setPermiteCadastrarCartaoMesmoAssim(cliente.getEmpresa().isPermiteCadastrarCartaoMesmoAssim());
                AutorizacaoCobrancaClienteVO.validarDados(autorizacao);
                if (!autoCliente.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                        getFacade().getAutorizacaoCobrancaCliente().existeOutraAutorizacaoParecidaParaOMesmoTipo(autoCliente)) {
                    throw new ConsistirException("Já existe uma Autorização com o mesmo 'tipo de parcelas a cobrar'");
                }
                getFacade().getAutorizacaoCobrancaCliente().validarClienteTitularCartao(autorizacao);
                getFacade().getAutorizacaoCobrancaCliente().validarMesmoCartao(autorizacao);
                if (!contaValida) {
                    throw new ConsistirException("A conta informada é inválida");
                }
            } else if (colaborador != null) {
                autoColaborador = (AutorizacaoCobrancaColaboradorVO) autorizacao;
                autoColaborador.setOrdem(getListaAutorizacoes().size() + 1);
                if (!UteisValidacao.emptyNumber(colaborador.getEmpresa().getCodigo())) {
                    colaborador.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(colaborador.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
                }
                if (autoColaborador.isTipoBoleto() && autoCliente.getTipoACobrar().equals(TipoObjetosCobrarEnum.NENHUM)){
                    autoColaborador.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
                }
                autoColaborador.setColaborador(colaborador);
                setAutoColaborador(autoColaborador);
                if (autorizacao.getConvenio() != null && autorizacao.getConvenio().getCodigo() != null && !autorizacao.getConvenio().getCodigo().equals(0)) {
                    autorizacao.setConvenio(getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(autorizacao.getConvenio().getCodigo(), colaborador.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
                autorizacao.setDesabilitarValidacaoValidade(!colaborador.getEmpresa().isValidarVencimentoCartaoAutorizacao());
                setPermiteCadastrarCartaoMesmoAssim(colaborador.getEmpresa().isPermiteCadastrarCartaoMesmoAssim());
                AutorizacaoCobrancaColaboradorVO.validarDados(autorizacao);
                if (!autoColaborador.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                        getFacade().getAutorizacaoCobrancaColaborador().existeOutraAutorizacaoParecidaParaOMesmoTipo(autoColaborador)) {
                    throw new ConsistirException("Já existe uma Autorização com o mesmo 'tipo de parcelas a cobrar'");
                }
                getFacade().getAutorizacaoCobrancaColaborador().validarClienteTitularCartao((AutorizacaoCobrancaColaboradorVO) autorizacao);
                getFacade().getAutorizacaoCobrancaColaborador().validarMesmoCartao(autorizacao);
                if (!contaValida) {
                    throw new ConsistirException("A conta informada é inválida");
                }
            }

            if (autorizacao.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                autorizacao.setCartaoMascarado(APF.getCartaoMascarado(autorizacao.getNumeroCartao()));
            }

            Optional<ConvenioCobrancaVO> convenioSelecionado = convenioVOs.stream().filter(conv -> conv.getCodigo().equals(autorizacao.getConvenio().getCodigo())).findFirst();
            convenioSelecionado.ifPresent(convenioCobrancaVO -> autorizacao.setConvenio(convenioCobrancaVO));

            if (cliente != null) {
                getFacade().getClienteMensagem().processarMensagensCartaoVencidoCliente(cliente.getCodigo(), getUsuario());
            }

         //ORIGEM VENDA DE PLANO
            if (origemVendaDePlano) {
                //Cartão de crédito realiza cobrança de verificação
                if (autorizacao.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                    JSONObject jsonRetorno = getFacade().getTransacao().realizaCobrancaVerificarCartao(this.cliente, this.colaborador, autorizacao, getUsuarioLogado(), this.getIpCliente());
                    boolean erro = jsonRetorno.getBoolean("erro");
                    if (erro) {
                        setVerificacaoCartaoSucesso(false);
                        setVerificacaoCartaoMsg(jsonRetorno.optString("message"));
                        setMsgAlert("Richfaces.hideModalPanel('panelAutorizacaoCobranca');Richfaces.showModalPanel('modalVerificacaoCartao');");

                    } else {
                        setVerificacaoCartaoSucesso(true);
                        setVerificacaoCartaoMsg(jsonRetorno.optString("message"));
                        setMsgAlert("Richfaces.hideModalPanel('panelAutorizacaoCobranca');Richfaces.showModalPanel('modalVerificacaoCartao');");
                    }
                } else {
                    //Se não for cartão de crédito segue o fluxo
                    inserirAutorizacaoCobranca();
                    setReRender("panelAutorizacaoCobrancaCliente, dados, panelModalVerificacaoCartao,modalVerificacaoCartao");
                }

        //ORIGEM CADASTRO ALUNO|COLABORADOR
            } else {
                //Cartão de crédito realiza cobrança de verificação (cobrança teste)
                if (autorizacao.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                    JSONObject jsonRetorno = getFacade().getTransacao().realizaCobrancaVerificarCartao(this.cliente, this.colaborador, autorizacao, getUsuarioLogado(), this.getIpCliente());
                    setVerificacaoCartaoSucesso(!jsonRetorno.getBoolean("erro"));
                    setVerificacaoCartaoMsg(jsonRetorno.optString("message"));
                    if(jsonRetorno.getString("modal").equals("show")) {
                        setMsgAlert("Richfaces.showModalPanel('modalVerificacaoCartao');");
                    }
                    else{
                        inserirAutorizacaoCobranca();
                        setReRender("panelAutorizacaoCobrancaCliente, dados, panelModalVerificacaoCartao,modalVerificacaoCartao");
                    }

                } else {
                    //Se não for cartão de crédito segue o fluxo
                    inserirAutorizacaoCobranca();
                    setReRender("panelAutorizacaoCobrancaCliente, dados, panelModalVerificacaoCartao,modalVerificacaoCartao");
                }
            }
        } catch (Exception ex) {
            montarErro(ex);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void inserirAutorizacaoCobranca() {
        try {
            String msg = "";
            MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
            if (autorizacao.isEdit()) {
                int index = 0;
                for (AutorizacaoCobrancaVO autoLista : listaAutorizacoes) {
                    if (autoLista.getCodigo().equals(autorizacao.getCodigo())) {
                        listaAutorizacoes.set(index, autorizacao);
                        break;
                    }
                    index++;
                }
                if (index >= listaAutorizacoes.size()) {
                    listaAutorizacoes.add(autorizacao);
                }
            } else {
                int j = listaAutorizacoes.indexOf(autorizacao);
                if (j != -1) {
                    throw new Exception("Já existe uma autorização idêntica a esta na Lista.");
                }
                listaAutorizacoes.add(autorizacao);
            }

            if (autorizacao.getCodigo() != 0) {
                if (!autorizacao.isGravarCodigoExterno()) {
                    autorizacao.setCodigoExterno("");
                }
                autorizacao.setIdCardMundiPagg("");
                autorizacao.setIdCardPagarMe("");
                if (cliente != null) {
                    getFacade().getAutorizacaoCobrancaCliente().alterar(autoCliente);
                    msg = "Autorização alterada com sucesso!";
                } else if (colaborador != null) {
                    getFacade().getAutorizacaoCobrancaColaborador().alterar(autoColaborador);
                    msg = "Autorização alterada com sucesso!";
                }

            } else {
                if (cliente != null) {
                    getFacade().getAutorizacaoCobrancaCliente().incluir(autoCliente);
                    msg = "Autorização adicionada com sucesso!";
                } else if (colaborador != null) {
                    getFacade().getAutorizacaoCobrancaColaborador().incluir(autoColaborador);
                    msg = "Autorização adicionada com sucesso!";
                }
            }

            if (cliente != null) {
                getFacade().getClienteMensagem().processarMensagensCartaoVencidoCliente(cliente.getCodigo(), getUsuario());
            }

            novo();
            control.montarSucessoGrowl(msg);
            montarSucessoGrowl(msg);
            setMsgAlert(getMensagemNotificar());
            setReRender("panelAutorizacaoCobrancaCliente, dados, panelModalVerificacaoCartao,modalVerificacaoCartao");
        }catch (Exception ex){
            montarErro(ex);
            setMsgAlert(getMensagemNotificar());
        }
    }

    private void clear() {
        imagemCartao = "semCartao";
        limparMsg();
        if (cliente != null) {
            AutorizacaoCobrancaClienteVO autoCliente = new AutorizacaoCobrancaClienteVO(true);
            autoCliente.setCliente(cliente);
            autorizacao = autoCliente;
        } else if (colaborador != null) {
            AutorizacaoCobrancaColaboradorVO autoColaborador = new AutorizacaoCobrancaColaboradorVO(true);
            autoColaborador.setColaborador(colaborador);

            autorizacao = autoColaborador;
        }
    }

    public void confirmarRemoverAutorizacaoCobranca() {
        setAutorizacaoRemover(null);
        AutorizacaoCobrancaVO aut = (AutorizacaoCobrancaVO) JSFUtilities.getRequestAttribute("autorizacao");
        setAutorizacaoRemover(aut);
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Excluir Autorização de Cobrança",
                "Tem certeza que deseja excluir a autorização de cobrança?",
                this, "remover", "", "", "", "panelAutorizacaoCobrancaCliente,dados");
    }

    public void remover() {
        try {
            limparMsg();

            getFacade().getAutorizacaoCobrancaCliente().possuiPermissao("EXCLUIR");
            AutorizacaoCobrancaVO aut = getAutorizacaoRemover();
            if (getCliente() != null &&
                    !getCliente().getEmpresa().getCodigo().equals(aut.getConvenio().getEmpresa().getCodigo()) &&
                    getFacade().getMovParcela().existeParcelaEmSituacao(getCliente().getCodigo(), "EA", aut.getConvenio().getEmpresa().getCodigo())) {
                throw new ConsistirException("Não e possível remover a autorização de outra empresa, pois o cliente possui cobranças pendentes nela!");
            }

            listaAutorizacoes.remove(aut);
            if (cliente != null) {
                AutorizacaoCobrancaClienteVO autoCliente = (AutorizacaoCobrancaClienteVO) aut;
                getFacade().getAutorizacaoCobrancaCliente().alterarSituacaoAutorizacaoCobranca(false, autoCliente, "", getUsuarioLogado());
            } else {
                AutorizacaoCobrancaColaboradorVO autoColaborador = (AutorizacaoCobrancaColaboradorVO) aut;
                getFacade().getAutorizacaoCobrancaColaborador().desativar(autoColaborador);
            }

            if (cliente != null) {
                getFacade().getClienteMensagem().excluirClienteMensagemCartaoVencido(aut.getCartaoMascarado(), cliente.getCodigo());
                getFacade().getClienteMensagem().processarMensagensCartaoVencidoCliente(cliente.getCodigo(), getUsuario());
            }

            novo();
            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.montarSucessoGrowl("Autorização removida com sucesso!");
        } catch (Exception ex) {
            montarErro(ex);
            setMsgAlert(getMensagemNotificar());
        } finally {
            setAutorizacaoRemover(null);
            limparDadosDoCartao();
        }
    }

    private void obterNazgDTO(AutorizacaoCobrancaVO obj) throws Exception {
        AragornService aragornService = new AragornService();
        obj.setNazgDTO(aragornService.obterNazg(obj.getTokenAragorn()));
        aragornService = null;
    }

    public void limparDadosDoCartao() {
        autorizacao.setNumeroCartao("");
        autorizacao.setCartaoMascarado("");
        autorizacao.setValidadeCartao("");
        autorizacao.setNomeTitularCartao("");
        autorizacao.setCpfTitular("");
        autorizacao.setTokenAragorn("");
        autorizacao.setCvv("");
        autorizacao.setTokenAragorn("");
        autorizacao.setIdCardMundiPagg("");
        autorizacao.setIdCardPagarMe("");
        autorizacao.setMesValidade(0);
        autorizacao.setAnoValidade(0);
        setImagemCartao("semCartao");
        setBandeiraCartao(null);
        setStyleClassCartao("white");
        setStyleClassCartaoRodape("white");
        setStyleClassCartaoTitles("titleGrey");
    }

    public void selecionaAutorizacao() {
        try {
            inicializarCard();
            limparMsg();

            getFacade().getAutorizacaoCobrancaCliente().possuiPermissao("ALTERAR");

            AutorizacaoCobrancaVO selecionada;
            if (cliente != null) {
                selecionada = (AutorizacaoCobrancaClienteVO) JSFUtilities.getRequestAttribute("autorizacao");
                autorizacao = getFacade().getAutorizacaoCobrancaCliente().consultarPorChavePrimaria(selecionada.getCodigo());
            } else {
                selecionada = (AutorizacaoCobrancaColaboradorVO) JSFUtilities.getRequestAttribute("autorizacao");
                autorizacao = getFacade().getAutorizacaoCobrancaColaborador().consultarPorChavePrimaria(selecionada.getCodigo());
            }

            if (autorizacao.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                obterNazgDTO(autorizacao);
                autorizacao.setNumeroCartao(autorizacao.getNazgDTO().getCardMask());
                autorizacao.setCartaoMascarado(autorizacao.getNazgDTO().getCardMask());
                if (!UteisValidacao.emptyString(autorizacao.getNazgDTO().getValidadeMMYYYY(true)) && autorizacao.getNazgDTO().getValidadeMMYYYY(true).length() == 7) {
                    //vem do banco no formato mm/yyyy mas na tela mostra mm/yy
                    autorizacao.setValidadeCartao(autorizacao.getNazgDTO().getValidadeMMYYYY(true).replace("/20", "/"));
                }else {
                    autorizacao.setValidadeCartao(autorizacao.getNazgDTO().getValidadeMMYYYY(true));
                }
                autorizacao.setMesValidade(autorizacao.getNazgDTO().getMonth());
                autorizacao.setAnoValidade(autorizacao.getNazgDTO().getYear());
                autorizacao.setNomeTitularCartao(autorizacao.getNazgDTO().getName());
                autorizacao.setCpfTitular(autorizacao.getNazgDTO().getCpf());
            }

            carregarConvenios();

            if (autorizacao.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                    !UteisValidacao.emptyNumber(autorizacao.getConvenio().getCodigo())) {
                carregarBandeirasCartao();
                obterImagemBandeira();
            }

            autorizacao.setEdit(true);
            autorizacao.setObjetoVOAntesAlteracao(autorizacao.getClone(true));

            if (!UteisValidacao.emptyString(autorizacao.getNumeroCartao())) {
                ((AutorizacaoCobrancaVO) autorizacao.getObjetoVOAntesAlteracao()).setCartaoMascarado(autorizacao.getNumeroCartao());
            }

            if (cliente != null && !UteisValidacao.emptyString((cliente.getEmpresa().getTiposProduto()))) {
                this.getAutorizacao().setListaObjetosACobrar(cliente.getEmpresa().getTiposProduto());
            }
        } catch (Exception e) {
            montarErro(e);
            Logger.getLogger(AutorizacaoCobrancaControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    private void obterImagemBandeira() {
        if (autorizacao.getOperadoraCartao() != null) {
            ValidaBandeira.Bandeira bandeiraCard = ValidaBandeira.buscarBandeira(autorizacao.getOperadoraCartao());
            bandeiraCartao = String.valueOf(bandeiraCard);
            autorizacao.setOperadoraCartao(Enum.valueOf(OperadorasExternasAprovaFacilEnum.class, bandeiraCartao.toUpperCase()));
            imagemCartao = bandeiraCard.getImagem();
        } else {
            imagemCartao = "semCartao";
        }
    }

    public void gravar() throws Exception {
        try {
            limparMsg();
            List<AutorizacaoCobrancaVO> lista = listaAutorizacoes;
            List<AutorizacaoCobrancaVO> listaAntiga = carregarAutorizacoesCliente();
            List<AutorizacaoCobrancaVO> listaExcluir = new ArrayList<AutorizacaoCobrancaVO>();
            for (AutorizacaoCobrancaVO aAntiga : listaAntiga) {
                boolean excluiu = true;
                for (AutorizacaoCobrancaVO a : lista) {
                    if (a.getCodigo().intValue() == aAntiga.getCodigo().intValue()) {
                        excluiu = false;
                    }
                }
                if (excluiu) {
                    listaExcluir.add(aAntiga);
                }
            }

            for (AutorizacaoCobrancaVO aExcluir : listaExcluir) {
                AutorizacaoCobrancaClienteVO autoExcluir = (AutorizacaoCobrancaClienteVO) aExcluir;
                getFacade().getAutorizacaoCobrancaCliente().alterarSituacaoAutorizacaoCobranca(false, autoExcluir, "gravar | AutorizacaoCobrancaControle", getUsuarioLogado());
            }

            for (AutorizacaoCobrancaVO a : lista) {
                AutorizacaoCobrancaClienteVO auto = (AutorizacaoCobrancaClienteVO) a;
                if (a.getCodigo() != 0) {
                    getFacade().getAutorizacaoCobrancaCliente().alterar(auto, getUsuarioLogado());
                } else {
                    getFacade().getAutorizacaoCobrancaCliente().incluir(auto, getUsuarioLogado());
                }
            }

            if (cliente != null) {
                getFacade().getClienteMensagem().processarMensagensCartaoVencidoCliente(cliente.getCodigo(), getUsuario());
            }

            setMensagemID("msg_dados_gravados");
        } catch (Exception e) {
            setMensagem(e.getMessage());
            throw e;
        }
    }

    public void abrirAutorizacoes(ActionEvent evt) {
        PessoaVO pessoa = (PessoaVO) evt.getComponent().getAttributes().get(
                "pessoa");
        if (pessoa != null) {
            ClienteVO c;
            try {
                c = getFacade().getCliente().consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (c.getCodigo() != 0) {
                    setCliente(c);
                    setNegociacao(false);
                    init();
                    novo();
                }
            } catch (Exception ex) {
                setMensagemDetalhada(ex);
                Logger.getLogger(AutorizacaoCobrancaControle.class.getName()).log(Level.SEVERE, null, ex);
            }

        }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = autorizacao.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));

        if (cliente != null) {
            loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), 0, cliente.getPessoa().getCodigo());
        } else {
            loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), 0, colaborador.getPessoa().getCodigo());
        }
    }

    public boolean isPermiteAlterarConvenio() {
        return getCliente() == null || getCliente().getEmpresa().getCodigo().equals(getAutorizacao().getConvenio().getEmpresa().getCodigo()) || getAutorizacao().isNovoObj();
    }

    public String getBandeiraCartao() {
        return bandeiraCartao;
    }

    public void setBandeiraCartao(String bandeiraCartao) {
        this.bandeiraCartao = bandeiraCartao;
    }

    public List getListaAdquirenteMaxiPago() {
        return AdquirenteMaxiPagoEnum.getSelectListAdquirenteMaxiPago();
    }

    public boolean getContaValida() {
        return contaValida;
    }

    public void setContaValida(boolean contaValida) {
        this.contaValida = contaValida;
    }

    public PermissaoAcessoMenuVO getPermissaoAcessoMenuVO() {
        return permissaoAcessoMenuVO;
    }

    public void setPermissaoAcessoMenuVO(PermissaoAcessoMenuVO permissaoAcessoMenuVO) {
        this.permissaoAcessoMenuVO = permissaoAcessoMenuVO;
    }

    public boolean isPermiteEditarProdutos() {
        return permiteEditarProdutos;
    }

    public void setPermiteEditarProdutos(boolean permiteEditarProdutos) {
        this.permiteEditarProdutos = permiteEditarProdutos;
    }

    public void tipoACobrarListener(ValueChangeEvent event) {
        TipoObjetosCobrarEnum tipoNovo = (TipoObjetosCobrarEnum) event.getNewValue();
        if (TipoObjetosCobrarEnum.TIPOS_PRODUTOS.equals(tipoNovo)) {
            if (!UteisValidacao.emptyString((cliente.getEmpresa().getTiposProduto()))) {
                AutorizacaoCobrancaClienteVO autorizacao = (AutorizacaoCobrancaClienteVO) request().getAttribute("autorizacao");
                autorizacao.setListaObjetosACobrar(cliente.getEmpresa().getTiposProduto());
            }
        }

    }

    public String getImagemCartao() {
        return imagemCartao;
    }

    public void setImagemCartao(String imagemCartao) {
        this.imagemCartao = imagemCartao;
    }

    public TipoAutorizacaoCobrancaEnum getTipoAutorizacaoCondicaoPagamento() {
        ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
        return contratoControle.getContratoVO().getPlanoCondicaoPagamento().getCondicaoPagamento().getTipoConvenioCobranca().getTipoAutorizacao();
    }

    public TipoConvenioCobrancaEnum apresentarConvenioPreenchimento() {
        ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
        if (contratoControle != null) {
            return contratoControle.getContratoVO().getPlanoCondicaoPagamento().getCondicaoPagamento().getTipoConvenioCobranca();
        }
        return null;
    }

    public boolean isCartaoSelecionado() {
        return cartaoSelecionado;
    }

    public void setCartaoSelecionado(boolean cartaoSelecionado) {
        this.cartaoSelecionado = cartaoSelecionado;
    }

    public boolean isDebitoSelecionado() {
        return debitoSelecionado;
    }

    public void setDebitoSelecionado(boolean debitoSelecionado) {
        this.debitoSelecionado = debitoSelecionado;
    }

    public boolean isBoletoSelecionado() {
        return boletoSelecionado;
    }

    public void setBoletoSelecionado(boolean boletoSelecionado) {
        this.boletoSelecionado = boletoSelecionado;
    }

    public boolean isApresentarAutorizarDebito() {
        for (AutorizacaoCobrancaVO auto : getListaAutorizacoes()) {
            if (auto.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
                return true;
            }
        }
        return false;
    }

    public boolean isApresentarEmpresa() {
        return apresentarEmpresa;
    }

    public void setApresentarEmpresa(boolean apresentarEmpresa) {
        this.apresentarEmpresa = apresentarEmpresa;
    }

    public List<String> getTiposProdutosSelecionados() {
        if (tiposProdutosSelecionados == null) {
            tiposProdutosSelecionados = new ArrayList<>();
        }
        return tiposProdutosSelecionados;
    }

    public void setTiposProdutosSelecionados(List<String> tiposProdutosSelecionados) {
        this.tiposProdutosSelecionados = tiposProdutosSelecionados;
    }

    public List<SelectItem> getTiposProdutosDisponiveis() {
        return TipoProduto.getTiposProdutosParaAutorizacaoCobranca(false);
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public void abrirModalTiposProdutosNovo() {
        abrirModalTiposProdutosGeral(getAutorizacao());
    }

    public void abrirModalTiposProdutosLista() {
        if (colaborador != null) {
            AutorizacaoCobrancaColaboradorVO autoColaborador = (AutorizacaoCobrancaColaboradorVO) JSFUtilities.getFromRequest("autorizacao");
            abrirModalTiposProdutosGeral(autoColaborador);
        }else {
            AutorizacaoCobrancaClienteVO autoCliente = (AutorizacaoCobrancaClienteVO) JSFUtilities.getFromRequest("autorizacao");
            abrirModalTiposProdutosGeral(autoCliente);
        }
    }

    private void abrirModalTiposProdutosGeral(AutorizacaoCobrancaVO autorizacao) {
        try {
            limparMsg();
            setOnComplete("");
            setTiposProdutosSelecionados(new ArrayList<>());

            setAutorizacaoEditarTipoProduto(autorizacao);

            if (!UteisValidacao.emptyString(getAutorizacaoEditarTipoProduto().getListaObjetosACobrar())) {
                String[] tipos = getAutorizacaoEditarTipoProduto().getListaObjetosACobrar().split(",");
                for (String tipo : tipos) {
                    if (!UteisValidacao.emptyString(tipo)) {
                        getTiposProdutosSelecionados().add(tipo);
                    }
                }
            }
            setOnComplete("Richfaces.showModalPanel('modalTipoProdutoAuto')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void gravarTiposProdutos() {
        try {
            limparMsg();
            setOnComplete("");

            if (getAutorizacaoEditarTipoProduto() == null) {
                throw new Exception("");
            }

            if (UteisValidacao.emptyList(getTiposProdutosSelecionados())){
                throw new Exception("Não foi informado nenhum tipo de produto a ser cobrado. Por favor adicione algum produto e tente novamente");
            }


            //autorização nova..
            if (UteisValidacao.emptyNumber(getAutorizacaoEditarTipoProduto().getCodigo())) {

                getAutorizacao().setListaObjetosACobrar("");
                for (String tipo : getTiposProdutosSelecionados()) {
                    if (!UteisValidacao.emptyString(tipo)) {
                        getAutorizacao().setListaObjetosACobrar(getAutorizacao().getListaObjetosACobrar() + "," + tipo);
                    }
                }
                getAutorizacao().setListaObjetosACobrar(getAutorizacao().getListaObjetosACobrar().replaceFirst(",", ""));


            } else {

                //alteração de uma de cliente
                if (cliente != null) {
                AutorizacaoCobrancaClienteVO autorizacaoCobrancaVO = getFacade().getAutorizacaoCobrancaCliente().consultarPorChavePrimaria(getAutorizacaoEditarTipoProduto().getCodigo());
                getAutorizacaoEditarTipoProduto().setObjetoVOAntesAlteracao(autorizacaoCobrancaVO);
                autorizacaoCobrancaVO.setObjetoVOAntesAlteracao(getAutorizacaoEditarTipoProduto());
                autorizacaoCobrancaVO.setCartaoMascarado(getAutorizacaoEditarTipoProduto().getCartaoMascarado());
                autorizacaoCobrancaVO.setListaObjetosACobrar("");

                for (String tipo : getTiposProdutosSelecionados()) {
                    if (!UteisValidacao.emptyString(tipo)) {
                        autorizacaoCobrancaVO.setListaObjetosACobrar(autorizacaoCobrancaVO.getListaObjetosACobrar() + "," + tipo);
                    }
                }
                autorizacaoCobrancaVO.setListaObjetosACobrar(autorizacaoCobrancaVO.getListaObjetosACobrar().replaceFirst(",", ""));
                getFacade().getAutorizacaoCobrancaCliente().alterarTipoACobrar(autorizacaoCobrancaVO);
                getAutorizacao().setListaObjetosACobrar(autorizacaoCobrancaVO.getListaObjetosACobrar());

                try {
                    registrarLogAutorizacaoCobranca(autorizacaoCobrancaVO);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                for (AutorizacaoCobrancaVO autoVO : this.getListaAutorizacoes()) {
                    if (autoVO.getCodigo().equals(autorizacaoCobrancaVO.getCodigo())) {
                        autoVO.setListaObjetosACobrar(autorizacaoCobrancaVO.getListaObjetosACobrar());
                        break;
                    }
                }
                montarSucessoGrowl("Produtos alterados com sucesso.");
            }
                //alteração de uma de colaborador
                if (colaborador != null) {
                    AutorizacaoCobrancaColaboradorVO autorizacaoCobrancaColaboradorVO = getFacade().getAutorizacaoCobrancaColaborador().consultarPorChavePrimaria(getAutorizacaoEditarTipoProduto().getCodigo());
                    getAutorizacaoEditarTipoProdutoColaborador().setObjetoVOAntesAlteracao(autorizacaoCobrancaColaboradorVO);
                    autorizacaoCobrancaColaboradorVO.setObjetoVOAntesAlteracao(getAutorizacaoEditarTipoProdutoColaborador());
                    autorizacaoCobrancaColaboradorVO.setCartaoMascarado(getAutorizacaoEditarTipoProdutoColaborador().getCartaoMascarado());
                    autorizacaoCobrancaColaboradorVO.setListaObjetosACobrar("");

                    for (String tipo : getTiposProdutosSelecionados()) {
                        if (!UteisValidacao.emptyString(tipo)) {
                            autorizacaoCobrancaColaboradorVO.setListaObjetosACobrar(autorizacaoCobrancaColaboradorVO.getListaObjetosACobrar() + "," + tipo);
                        }
                    }
                    autorizacaoCobrancaColaboradorVO.setListaObjetosACobrar(autorizacaoCobrancaColaboradorVO.getListaObjetosACobrar().replaceFirst(",", ""));
                    getFacade().getAutorizacaoCobrancaColaborador().alterarTipoACobrar(autorizacaoCobrancaColaboradorVO);
                    getAutorizacao().setListaObjetosACobrar(autorizacaoCobrancaColaboradorVO.getListaObjetosACobrar());

                    try {
                        registrarLogAutorizacaoCobrancaColaborador(autorizacaoCobrancaColaboradorVO);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    for (AutorizacaoCobrancaVO autoVO : this.getListaAutorizacoes()) {
                        if (autoVO.getCodigo().equals(autorizacaoCobrancaColaboradorVO.getCodigo())) {
                            autoVO.setListaObjetosACobrar(autorizacaoCobrancaColaboradorVO.getListaObjetosACobrar());
                            break;
                        }
                    }
                    montarSucessoGrowl("Produtos alterados com sucesso.");
                }

        }
            montarSucessoGrowl("Produtos Adicionados com sucesso.");
            setOnComplete("Richfaces.hideModalPanel('modalTipoProdutoAuto')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public AutorizacaoCobrancaVO getAutorizacaoEditarTipoProduto() {
        if (autorizacaoEditarTipoProduto == null) {
            autorizacaoEditarTipoProduto = new AutorizacaoCobrancaClienteVO();
        }
        return autorizacaoEditarTipoProduto;
    }

    public AutorizacaoCobrancaVO getAutorizacaoEditarTipoProdutoColaborador() {
        if (autorizacaoEditarTipoProduto == null) {
            autorizacaoEditarTipoProduto = new AutorizacaoCobrancaColaboradorVO();
        }
        return autorizacaoEditarTipoProduto;
    }

    public void setAutorizacaoEditarTipoProduto(AutorizacaoCobrancaVO autorizacaoEditarTipoProduto) {
        this.autorizacaoEditarTipoProduto = autorizacaoEditarTipoProduto;
    }

    public boolean isPermiteUsarIdVindiCliente() {
        try {
            return getCliente() != null &&
                    !UteisValidacao.emptyNumber(getCliente().getPessoa().getIdVindi()) &&
                    getAutorizacao().getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI);
        } catch (Exception ex) {
            return false;
        }
    }

    public AutorizacaoCobrancaVO getAutorizacaoRemover() {
        return autorizacaoRemover;
    }

    public void setAutorizacaoRemover(AutorizacaoCobrancaVO autorizacaoRemover) {
        this.autorizacaoRemover = autorizacaoRemover;
    }

    public boolean isApresentarClienteTitularCartao() {
        if (this.getCliente() != null) {
            return this.getCliente().getEmpresa().isAgruparParcelasPorCartao();
        } else if (this.getColaborador() != null) {
            return this.getColaborador().getEmpresa().isAgruparParcelasPorCartao();
        } else {
            return false;
        }
    }

    public String getGatewayTokenVindi() {
        if (gatewayTokenVindi == null) {
            gatewayTokenVindi = "";
        }
        return gatewayTokenVindi;
    }

    public void setGatewayTokenVindi(String gatewayTokenVindi) {
        this.gatewayTokenVindi = gatewayTokenVindi;
    }

    public String getUrlGatewayTokenVindi() {
        try {
            if (UteisValidacao.emptyNumber(getAutorizacao().getConvenio().getCodigo())) {
                return "";
            }

            if (getAutorizacao().getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
                if (getAutorizacao().getConvenio().getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                    return PropsService.getPropertyValue(PropsService.urlApiVindiProducao) + "/public/payment_profiles";
                } else {
                    return PropsService.getPropertyValue(PropsService.urlApiVindiSandbox) + "/public/payment_profiles";
                }
            } else {
                return "";
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public String getChavePublicaGatewayTokenVindi() {
        try {
            if (UteisValidacao.emptyNumber(getAutorizacao().getConvenio().getCodigo())) {
                return "";
            }

            String chave = "";
            if (this.getAutorizacao().getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
                chave = this.getAutorizacao().getConvenio().getCodigoAutenticacao02();
            }
            return new String(new Base64().encode((chave + ":").getBytes()));
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public void verificarCadastrarVindiPublica() {
        try {
            AutorizacaoCobrancaClienteVO.validarDados(autorizacao);
            limparMsg();
            setOnComplete("");

            if (this.cliente != null &&
                    UteisValidacao.emptyNumber(this.getCliente().getPessoa().getIdVindi())) {
                //cadastrar cliente na vindi primeiro

                PessoaVO pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(this.cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_PESSOA_PACTO_PAY);
                VindiService vindiService = new VindiService(getFacade().getContrato().getCon(), this.cliente.getEmpresa().getCodigo(), this.autorizacao.getConvenio().getCodigo());
                vindiService.incluirPessoa(pessoaVO, null);
                if (UteisValidacao.emptyNumber(pessoaVO.getIdVindi())) {
                    throw new Exception("Não foi possível cadastrar cliente na Vindi.");
                }
                this.getCliente().getPessoa().setIdVindi(pessoaVO.getIdVindi());
            }

            setOnComplete("Notifier.cleanAll();Richfaces.showModalPanel('panelStatus1');cadastrarCardVindiPublicaAuto();");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            setOnComplete("Richfaces.hideModalPanel('panelStatus1');");
        }
    }

    public void confirmarCadastrarVindiPublica() {
        try {
            limparMsg();

            if (!UteisValidacao.emptyString(this.getGatewayTokenVindi())) {
                VindiService vindiService = new VindiService(getFacade().getContrato().getCon(), this.cliente.getEmpresa().getCodigo(), this.autorizacao.getConvenio().getCodigo());
                Integer idVindiPaymentProfile = vindiService.vincularTokenClienteVindi(this.getCliente().getPessoa(), this.getGatewayTokenVindi());
                this.getAutorizacao().setCodigoExterno(idVindiPaymentProfile.toString());
                this.getAutorizacao().setGravarCodigoExterno(true);
            } else {
                this.getAutorizacao().setCodigoExterno("");
            }

            if (!UteisValidacao.emptyString(this.getGatewayTokenVindi()) &&
                    getFacade().getConvenioCobranca().somenteConvenioVindi()) {
                incluirAutorizacaoIdVindi();
            } else {
                confirmar();
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void inicializarCard() {
        setStyleClassCartao("gradient");
        setStyleClassCartaoRodape("cartaoRodaPe");
        setStyleClassCartaoTitles("titleWhite");
    }

    public boolean renderizarColunaTipoCobranca() {
        //coluna só deve ser renderizada caso tenha alguma autorização dentro da lista diferente de boleto
        //se tiver somente boleto ela não renderiza
        boolean renderizar = false;

        for (AutorizacaoCobrancaVO auto : listaAutorizacoes) {
            if (auto.getTipoAutorizacao().getId() != 3) {
                renderizar = true;
            }
        }
        return renderizar;
    }

    public boolean isCadastrarUsandoVindiPublica() {
        return cadastrarUsandoVindiPublica;
    }

    public void setCadastrarUsandoVindiPublica(boolean cadastrarUsandoVindiPublica) {
        this.cadastrarUsandoVindiPublica = cadastrarUsandoVindiPublica;
    }

    public String getFormaPagamentoSelect() {
        return formaPagamentoSelect;
    }

    public void setFormaPagamentoSelect(String formaPagamentoSelect) {
        this.formaPagamentoSelect = formaPagamentoSelect;
    }

    public String getStyleClassCartao() {
        return styleClassCartao;
    }

    public void setStyleClassCartao(String styleClassCartao) {
        this.styleClassCartao = styleClassCartao;
    }

    public String getStyleClassCartaoRodape() {
        return styleClassCartaoRodape;
    }

    public void setStyleClassCartaoRodape(String styleClassCartaoRodape) {
        this.styleClassCartaoRodape = styleClassCartaoRodape;
    }

    public String getStyleClassCartaoTitles() {
        return styleClassCartaoTitles;
    }

    public void setStyleClassCartaoTitles(String styleClassCartaoTitles) {
        this.styleClassCartaoTitles = styleClassCartaoTitles;
    }

    public AutorizacaoCobrancaClienteVO getAutoCliente() {
        return autoCliente;
    }

    public void setAutoCliente(AutorizacaoCobrancaClienteVO autoCliente) {
        this.autoCliente = autoCliente;
    }

    public AutorizacaoCobrancaColaboradorVO getAutoColaborador() {
        return autoColaborador;
    }

    public void setAutoColaborador(AutorizacaoCobrancaColaboradorVO autoColaborador) {
        this.autoColaborador = autoColaborador;
    }

    public boolean isVerificacaoCartaoSucesso() {
        return verificacaoCartaoSucesso;
    }

    public void setVerificacaoCartaoSucesso(boolean verificacaoCartaoSucesso) {
        this.verificacaoCartaoSucesso = verificacaoCartaoSucesso;
    }

    public String getVerificacaoCartaoMsg() {
        if (verificacaoCartaoMsg == null) {
            verificacaoCartaoMsg = "";
        }
        return verificacaoCartaoMsg;
    }

    public void setVerificacaoCartaoMsg(String verificacaoCartaoMsg) {
        this.verificacaoCartaoMsg = verificacaoCartaoMsg;
    }
    public List<SelectItem> prepararListaConveniosTela(List<SelectItem> convenios){
        List<SelectItem> temp = new ArrayList<SelectItem>(convenios);
        for (SelectItem item : temp) {
            if (item.getLabel().equals("(Nenhum)")) {
                convenios.remove(item);
            }
        }
        convenios.add(0,new SelectItem(0,"(Selecionar convênio)"));
        return convenios;
    }

    public boolean isOrigemVendaDePlano() {
        return origemVendaDePlano;
    }

    public void setOrigemVendaDePlano(boolean origemVendaDePlano) {
        this.origemVendaDePlano = origemVendaDePlano;
    }

    public boolean isPermiteCadastrarCartaoMesmoAssim() {
        return permiteCadastrarCartaoMesmoAssim;
    }

    public void setPermiteCadastrarCartaoMesmoAssim(boolean permiteCadastrarCartaoMesmoAssim) {
        this.permiteCadastrarCartaoMesmoAssim = permiteCadastrarCartaoMesmoAssim;
    }

    public boolean isApresentarDadosCobranca() {
        try {
            for (AutorizacaoCobrancaVO obj : getListaAutorizacoes()) {
                if (!obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)) {
                    return true;
                }
            }
            return false;
        } catch (Exception ex) {
            return true;
        }
    }

    public String getReRender() {
        if (reRender == null) {
            reRender = "dados, panelModalVerificacaoCartao,modalVerificacaoCartao";
        }
        return reRender;
    }

    public void setReRender(String reRender) {
        this.reRender = reRender;
    }

    public void subirAutorizacao() {
        try {
            limparMsg();
            AutorizacaoCobrancaVO selecionada;
            if (cliente != null) {
                selecionada = (AutorizacaoCobrancaClienteVO) JSFUtilities.getRequestAttribute("autorizacao");
            } else {
                selecionada = (AutorizacaoCobrancaColaboradorVO) JSFUtilities.getRequestAttribute("autorizacao");
            }

            for (AutorizacaoCobrancaVO auto : getListaAutorizacoes()) {
                if (selecionada.getOrdem() == auto.getOrdem() + 1) {
                    auto.setOrdem(auto.getOrdem() + 1);
                    selecionada.setOrdem(selecionada.getOrdem() - 1);
                    if (cliente != null) {
                        getFacade().getAutorizacaoCobrancaCliente().alterarOrdem(auto.getOrdem() - 1, auto.getOrdem(), auto.getCodigo(), ((AutorizacaoCobrancaClienteVO) auto).getCliente(), getUsuarioLogado());
                        getFacade().getAutorizacaoCobrancaCliente().alterarOrdem(selecionada.getOrdem() + 1, selecionada.getOrdem(), selecionada.getCodigo(), ((AutorizacaoCobrancaClienteVO) selecionada).getCliente(), getUsuarioLogado());
                    } else {
                        getFacade().getAutorizacaoCobrancaColaborador().alterarOrdem(auto.getOrdem() - 1, auto.getOrdem(), auto.getCodigo(), ((AutorizacaoCobrancaColaboradorVO) auto).getColaborador(), getUsuarioLogado());
                        getFacade().getAutorizacaoCobrancaColaborador().alterarOrdem(selecionada.getOrdem() + 1, selecionada.getOrdem(), selecionada.getCodigo(), ((AutorizacaoCobrancaColaboradorVO) selecionada).getColaborador(), getUsuarioLogado());
                    }
                    break;
                }
            }
            this.setListaAutorizacoes(Ordenacao.ordenarLista(getListaAutorizacoes(), "ordem"));
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void descerAutorizacao() {
        try {
            limparMsg();
            AutorizacaoCobrancaVO selecionada;
            if (cliente != null) {
                selecionada = (AutorizacaoCobrancaClienteVO) JSFUtilities.getRequestAttribute("autorizacao");
            } else {
                selecionada = (AutorizacaoCobrancaColaboradorVO) JSFUtilities.getRequestAttribute("autorizacao");
            }

            for (AutorizacaoCobrancaVO auto : getListaAutorizacoes()) {
                if (auto.getOrdem() == selecionada.getOrdem() + 1) {
                    auto.setOrdem(auto.getOrdem() - 1);
                    selecionada.setOrdem(selecionada.getOrdem() + 1);

                    if (cliente != null) {
                        getFacade().getAutorizacaoCobrancaCliente().alterarOrdem(auto.getOrdem() + 1, auto.getOrdem(), auto.getCodigo(), ((AutorizacaoCobrancaClienteVO) auto).getCliente(), getUsuarioLogado());
                        getFacade().getAutorizacaoCobrancaCliente().alterarOrdem(selecionada.getOrdem() - 1, selecionada.getOrdem(), selecionada.getCodigo(), ((AutorizacaoCobrancaClienteVO) selecionada).getCliente(), getUsuarioLogado());
                    } else {
                        getFacade().getAutorizacaoCobrancaColaborador().alterarOrdem(auto.getOrdem() + 1, auto.getOrdem(), auto.getCodigo(), ((AutorizacaoCobrancaColaboradorVO) auto).getColaborador(), getUsuarioLogado());
                        getFacade().getAutorizacaoCobrancaColaborador().alterarOrdem(selecionada.getOrdem() - 1, selecionada.getOrdem(), selecionada.getCodigo(), ((AutorizacaoCobrancaColaboradorVO) selecionada).getColaborador(), getUsuarioLogado());
                    }
                    break;
                }
            }
            this.setListaAutorizacoes(Ordenacao.ordenarLista(getListaAutorizacoes(), "ordem"));
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }
}

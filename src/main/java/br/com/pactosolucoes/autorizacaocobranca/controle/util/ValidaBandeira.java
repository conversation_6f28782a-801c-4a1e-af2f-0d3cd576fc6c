package br.com.pactosolucoes.autorizacaocobranca.controle.util;

import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ValidaBandeira {

    public static void main(String[] args) {
//        System.getProperty("user.dir");
        System.out.println(buscarBandeira("****************").getCodigo());
    }

    public enum Bandeira {
        VISA(1, "^4[0-9]{12}(?:[0-9]{3})?$", "visa"),
        MASTERCARD(2, "^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$", "mastercard"),
        DINERS(3, "^3(?:0[0-5]|[68][0-9])[0-9]{11}$", "diners"),
        AMEX(4, "^3[47][0-9]{13}$", "amex"),
        HIPERCARD(5, "^(606282\\d{10}(\\d{3})?)|(3841\\d{15})$", "hipercard"),
        JCB(6, "^(?:2131|1800|35\\d{3})\\d{11}$", "jcb"),
        AURA(8, "^(5078\\d{2})(\\d{2})(\\d{11})$", "aura"), //
        ELO(9, "^(40117[8-9]|431274|438935|451416|457393|45763[1-2]|506(699|7[0-6][0-9]|77[0-8])|509\\d{3}|504175|627780|636297|636368|65003[1-3]|6500(3[5-9]|4[0-9]|5[0-1])|6504(0[5-9]|[1-3][0-9])|650(4[8-9][0-9]|5[0-2][0-9]|53[0-8])|6505(4[1-9]|[5-8][0-9]|9[0-8])|6507(0[0-9]|1[0-8])|65072[0-7]|6509(0[1-9]|1[0-9]|20)|6516(5[2-9]|[6-7][0-9])|6550([0-1][0-9]|2[1-9]|[3-4][0-9]|5[0-8]))", "elo"),
        DISCOVER(13, "^6(?:011|5[0-9]{2})[0-9]{12}$", "discover");
//        MAESTRO(18, "^(5018|5020|5038|6304|6759|6761|6763)[0-9]{8,15}$", "maestro");

//        ELO(9, "^((((636368)|(438935)|(504175)|(451416)|(636297))\\d{0,10})|((5067)|(4576)|(4011))\\d{0,12})$", "elo"),
//        DISCOVER(13, "^65[4-9][0-9]{13}|64[4-9][0-9]{13}|6011[0-9]{12}|(622(?:12[6-9]|1[3-9][0-9]|[2-8][0-9][0-9]|9[01][0-9]|92[0-5])[0-9]{10})$", "elo");

        private Integer codigo;
        private String descricao;
        private String imagem;

        Bandeira(Integer codigo, String descricao, String imagem) {
            this.codigo = codigo;
            this.descricao = descricao;
            this.imagem = imagem;
        }

        public Integer getCodigo() {
            return codigo;
        }
        public String getDescricao() {
            return descricao;
        }
        public String getImagem() { return imagem;
        }
    }

    /**
     * Busca bandeira do cartao
     *
     * @param numeroCartao Numero do cartao de credito
     * @return Bandeira conforme o numero do cartao de credito informado no parametro
     */
    public static Bandeira buscarBandeira(String numeroCartao) {
        if (UteisValidacao.emptyString(numeroCartao))
            return null;

        Boolean encontrou;

        for (Bandeira bandeira : Bandeira.values()) {
            Pattern pattern = Pattern.compile(bandeira.getDescricao());
            Matcher matcher = pattern.matcher(numeroCartao);

            encontrou = matcher.find();
            if (encontrou)
                return bandeira;
//                return bandeira.name();
        }

        return null;
    }

    public static Bandeira buscarBandeira(OperadorasExternasAprovaFacilEnum operadora) {
        if (operadora == null) {
            return null;
        }

        Boolean encontrou;

        for (Bandeira bandeira : Bandeira.values()) {
            if (bandeira.name().equalsIgnoreCase(operadora.name())) {
                return bandeira;
            }
        }
        return null;
    }

    /**
     * Valida bandeira do cartao de credito
     *
     * @param numeroCartao Numero do cartao de credito
     * @return Se o numero do cartao de credito informado no parametro é valido
     */
    public static boolean numeroCartaoValido(String numeroCartao) {
        if (UteisValidacao.emptyString(numeroCartao))
            return false;

        Boolean encontrou = null;

        for (Bandeira bandeira : Bandeira.values()) {
            Pattern pattern = Pattern.compile(bandeira.getDescricao());
            Matcher matcher = pattern.matcher(numeroCartao);

            encontrou = matcher.find();
            if (encontrou)
                return encontrou;
        }

        return encontrou;
    }
}

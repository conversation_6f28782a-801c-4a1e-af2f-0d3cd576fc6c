/*
 *
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.autorizacaocobranca.interfaces;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AutorizacaoCobrancaColaboradorInterfaceFacade extends SuperInterface {

    void alterar(AutorizacaoCobrancaColaboradorVO obj) throws Exception;

    AutorizacaoCobrancaColaboradorVO consultarPorChavePrimaria(final int codigo) throws Exception;

    List<AutorizacaoCobrancaColaboradorVO> consultarPorColaborador(final int codigoColaborador, int nivelMontarDados) throws Exception;

    void desativar(AutorizacaoCobrancaColaboradorVO obj) throws Exception;

    void incluir(AutorizacaoCobrancaColaboradorVO obj) throws Exception;

    boolean existeOutraAutorizacaoParecidaParaOMesmoTipo(final AutorizacaoCobrancaColaboradorVO obj) throws Exception;

    List<AutorizacaoCobrancaColaboradorVO> obterOutrasAutorizacoesParecidasParaOMesmoTipo(final AutorizacaoCobrancaColaboradorVO obj) throws Exception;

    List<AutorizacaoCobrancaColaboradorVO> consulta(final String condicao, int nivelMontarDados) throws Exception;

    List<AutorizacaoCobrancaColaboradorVO> consultarPorPessoa(final int codigoCliente) throws Exception;

    void trocarCartao(final int colaborador, final CartaoCreditoTO cartao, final String cartaoMascaradoAnterior) throws Exception;

    void atribuirParcelasDCC(Integer codigoAutorizacao, int pessoaAutorizacao, int codigoColaborador, boolean alterarParcelasDCC) throws Exception;

    void alterarTipoACobrar(AutorizacaoCobrancaColaboradorVO obj) throws Exception;

    public List<AutorizacaoCobrancaColaboradorVO> consultarPorPessoaTipoAutorizacao(final int codigoPessoa, TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum, int nivelMontarDados) throws Exception;

    void alterarSomenteCartaoVerificado(AutorizacaoCobrancaColaboradorVO autoVO) throws Exception;

    void validarClienteTitularCartao(AutorizacaoCobrancaVO obj) throws Exception;

    void validarMesmoCartao(AutorizacaoCobrancaVO obj) throws Exception;

    void validarQuantidadeCartoesCadastrados(Integer colaborador, AutorizacaoCobrancaVO autorizacaoCobrancaVO) throws Exception;

    void alterarOrdem(Integer ordemAnterior, Integer ordemNova, Integer codigo,
                      ColaboradorVO colaboradorVO, UsuarioVO usuarioVO) throws Exception;
}

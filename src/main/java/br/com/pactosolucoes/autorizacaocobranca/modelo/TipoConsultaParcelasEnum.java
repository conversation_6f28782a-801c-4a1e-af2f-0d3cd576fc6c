/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.autorizacaocobranca.modelo;

/**
 *
 * <AUTHOR>
 */
public enum TipoConsultaParcelasEnum {

    PARCELAS_REPESCAGEM(1, "Parcelas Repescagem", "São as parcelas que já foram enviadas numa remessa e não foram autorizadas (pagas)"),
    PARCELAS_EM_ABERTO_AUTORIZADAS(2, "Parcelas Em Aberto", "São as parcelas que estão em aberto e ainda não foram cobradas e possuem autorização de cobrança");
    private int id;
    private String descricao;
    private String detalhe;

    private TipoConsultaParcelasEnum(int id, String descricao, String detalhe) {
        this.id = id;
        this.descricao = descricao;
        this.detalhe = detalhe;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDetalhe() {
        return detalhe;
    }

    public void setDetalhe(String detalhe) {
        this.detalhe = detalhe;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public static TipoConsultaParcelasEnum obterID(int id) {
        for (TipoConsultaParcelasEnum tipo : TipoConsultaParcelasEnum.values()) {
            if (tipo.getId() == id) {
                return tipo;
            }
        }
        return null;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.autorizacaocobranca.modelo;

import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;

/**
 * <AUTHOR>
 */
public enum TipoAutorizacaoCobrancaEnum {

    NENHUM(0, "(Nenhum)", new TipoConvenioCobrancaEnum[]{
            TipoConvenioCobrancaEnum.NENHUM
    }),
    CARTAOCREDITO(1, "Cartão de Crédito", new TipoConvenioCobrancaEnum[]{
            TipoConvenioCobrancaEnum.DCC,
            TipoConvenioCobrancaEnum.DCC_GETNET,
            TipoConvenioCobrancaEnum.DCC_BIN,
            TipoConvenioCobrancaEnum.DCC_VINDI,
            TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE,
            TipoConvenioCobrancaEnum.DCC_E_REDE,
            TipoConvenioCobrancaEnum.DCC_MAXIPAGO,
            TipoConvenioCobrancaEnum.DCC_FITNESS_CARD,
            TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE,
            TipoConvenioCobrancaEnum.DCC_STONE_ONLINE,
            TipoConvenioCobrancaEnum.DCC_MUNDIPAGG,
            TipoConvenioCobrancaEnum.DCC_PAGAR_ME,
            TipoConvenioCobrancaEnum.DCC_STRIPE,
            TipoConvenioCobrancaEnum.DCC_PAGOLIVRE,
            TipoConvenioCobrancaEnum.DCC_PINBANK,
            TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT,
            TipoConvenioCobrancaEnum.DCC_FACILITEPAY,
            TipoConvenioCobrancaEnum.DCC_CEOPAG,
            TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE,
            TipoConvenioCobrancaEnum.DCC_PAGBANK,
            TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5
    }),
    DEBITOCONTA(2, "Débito em Conta Corrente", new TipoConvenioCobrancaEnum[]{
            TipoConvenioCobrancaEnum.DCO_BB,
            TipoConvenioCobrancaEnum.DCO_HSBC,
            TipoConvenioCobrancaEnum.DCO_BRADESCO,
            TipoConvenioCobrancaEnum.DCO_ITAU,
            TipoConvenioCobrancaEnum.DCO_CAIXA,
            TipoConvenioCobrancaEnum.BOLETO_ITAU,
            TipoConvenioCobrancaEnum.DCO_SANTANDER,
            TipoConvenioCobrancaEnum.DCO_SANTANDER_150,
            TipoConvenioCobrancaEnum.DCO_BRADESCO_240,
            TipoConvenioCobrancaEnum.DCO_CAIXA_SICOV
    }),
    BOLETO_BANCARIO(3, "Boleto Bancário", new TipoConvenioCobrancaEnum[]{
            TipoConvenioCobrancaEnum.BOLETO,
            TipoConvenioCobrancaEnum.BOLETO_ITAU,
            TipoConvenioCobrancaEnum.ITAU,
            TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL,
            TipoConvenioCobrancaEnum.BOLETO_PJBANK,
            TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE,
            TipoConvenioCobrancaEnum.BOLETO_ASAAS,
            TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE,
            TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE
    }),
    PIX(4, "Pix", new TipoConvenioCobrancaEnum[]{
            TipoConvenioCobrancaEnum.PIX_BB
    });

    private int id;
    private String descricao;
    private TipoConvenioCobrancaEnum[] tiposConvenio;

    private TipoAutorizacaoCobrancaEnum(int id, String descricao, TipoConvenioCobrancaEnum[] tiposConvenio) {
        this.id = id;
        this.descricao = descricao;
        this.tiposConvenio = tiposConvenio;
    }

    public static TipoAutorizacaoCobrancaEnum valueOf(final int id) {
        TipoAutorizacaoCobrancaEnum[] lista = TipoAutorizacaoCobrancaEnum.values();
        for (TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum : lista) {
            if (id == tipoAutorizacaoCobrancaEnum.id) {
                return tipoAutorizacaoCobrancaEnum;
            }

        }
        return TipoAutorizacaoCobrancaEnum.NENHUM;
    }

    public static TipoAutorizacaoCobrancaEnum getPorTipoConvenioCobraca(TipoConvenioCobrancaEnum tipoConvenio){
        TipoAutorizacaoCobrancaEnum tipo = null;
        for(TipoAutorizacaoCobrancaEnum tipoAutorizacao : TipoAutorizacaoCobrancaEnum.values()){
            for(TipoConvenioCobrancaEnum tipoConvenioPesquisa : tipoAutorizacao.getTiposConvenio()){
                if(tipoConvenioPesquisa.equals(tipoConvenio)){
                    tipo = tipoAutorizacao;
                    break;
                }
            }
            if(tipo != null){
                break;
            }
        }
        return tipo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public TipoConvenioCobrancaEnum[] getTiposConvenio() {
        return tiposConvenio;
    }

    public void setTiposConvenio(TipoConvenioCobrancaEnum[] tiposConvenio) {
        this.tiposConvenio = tiposConvenio;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.estrutura.exportador.controle;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.bi.JasperGenerics;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.faces.event.ActionEvent;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.text.Normalizer;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ExportadorListaControle extends SuperControleRelatorio {

    private String fileName;
    private String fileAbsolutePath;
    private InputStream imagemGrafico;

    private String operacaoOnComplete;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileAbsolutePath() {
        return fileAbsolutePath;
    }

    public void setFileAbsolutePath(String fileAbsolutePath) {
        this.fileAbsolutePath = fileAbsolutePath;
    }

    public void exportar(ActionEvent evt) {

        limparMsg();
        setOperacaoOnComplete("");

        List l = (List) JSFUtilities.getFromActionEvent("lista", evt);
        String filtro = (String) JSFUtilities.getFromActionEvent("filtro", evt);//facultativo

        exportar(evt, l, filtro, ItemExportacaoEnum.obterPorId((String) JSFUtilities.getFromActionEvent("itemExportacao", evt)));

    }



    public void exportar(ActionEvent evt, List l, String filtro, ItemExportacaoEnum itemExportar) {
        limparMsg();
        String tipo = (String) JSFUtilities.getFromActionEvent("tipo", evt);
        String prefixoArquivo = (String) JSFUtilities.getFromActionEvent("prefixo", evt);
        String labels = (String) JSFUtilities.getFromActionEvent("atributos", evt);//facultativo
        String funcoes = (String) JSFUtilities.getFromActionEvent("funcoes", evt);//facultativo
        String titulo = (String) JSFUtilities.getFromActionEvent("titulo", evt);//facultativo
        String subTitulo = (String) JSFUtilities.getFromActionEvent("subTitulo", evt);//facultativo


        exportar(l, filtro, tipo, prefixoArquivo, labels,
                funcoes, titulo, subTitulo, true, itemExportar);
    }

    public void exportar(List l, String filtro, String tipo, String prefixoArquivo, String labels,
                         String funcoes, String titulo, String subTitulo, boolean isDadosLogin, ItemExportacaoEnum itemExportar) {
        setMsgAlert("");
        fileName = "";
        fileAbsolutePath = "";
        try {
            validarPermissaoEIncluirLogExportacao(itemExportar, l.size(), filtro, tipo, labels, prefixoArquivo);
            //Normalizando nome para o arquvio
            prefixoArquivo = Normalizer.normalize(prefixoArquivo, Normalizer.Form.NFD).replaceAll("[^a-zA-Z0-9]", "_");

            if (labels != null) {
                labels = labels.replaceAll("\\r\\n","");
                labels = labels.replaceAll("\\n","");
                labels =labels.replaceAll("\\s{2,}","");
            }


            if (l != null && tipo != null && prefixoArquivo != null) {
                JasperGenerics obj = new JasperGenerics(l);
                obj.setTitulo(UteisValidacao.emptyString(titulo) ? prefixoArquivo : titulo);
                obj.setSubTitulo(subTitulo == null ? "" : subTitulo);

                String pathRelatorio;

                if(isDadosLogin) {
                    pathRelatorio = this.getServletContext().getRealPath("relatorio");
                    obj.setUsuario(getUsuarioLogado().getNomeAbreviado());
                    obj.setLogo(obterLogo(getUsuarioLogado().getAdministrador() ? "" : getEmpresaLogado().getNome()));
                    if(!getUsuarioLogado().getAdministrador())
                        obj.setEmpresa(getEmpresaLogado());
                } else {
                    try {
                        pathRelatorio = this.getServletContext().getRealPath("relatorio");
                    } catch (Exception e) {
                        File f = new File(PropsService.getPropertyValue(PropsService.diretorioArquivos));
                        if(!f.exists()) {
                            f.getParentFile().mkdirs();
                        }
                        pathRelatorio = f.getAbsolutePath();
                    }
                }
                obj.setFiltro(filtro);
                obj.setFileNameDefault(false);
                prefixoArquivo = String.format("%s-%s-%s", new Object[]{
                        prefixoArquivo,
                        getKey(),
                        new Date().getTime()
                });

                obj.setPath(pathRelatorio + File.separator);
                obj.setPrefixoArquivo(prefixoArquivo);

                if (labels != null) {
                    String[] arrLabels = labels.split(",");
                    for (String arrLabel : arrLabels) {
                        String[] pares = arrLabel.split("=");
                        obj.putLabel(pares[0], pares[1]);
                    }
                }

                if (funcoes != null) {
                    String[] arrFunc = funcoes.split(",");
                    for (String s : arrFunc) {
                        String[] pares = s.split("=");
                        obj.putFunction(pares[1], pares[0]);
                    }
                }

                if (getImagemGrafico() != null){
                    obj.setImagemGrafico(getImagemGrafico());
                }

                if (tipo.equalsIgnoreCase("xls") || tipo.equalsIgnoreCase("xlsx")) {
                    obj.prepare(false, false);
                    obj.xlsx();
                } else if (tipo.equalsIgnoreCase("pdf")) {
                    obj.prepare(true, true);
                    obj.pdf();
                }
                if (obj.getDestFile() != null && obj.getDestFile().exists()
                        && !obj.getDestFile().isDirectory()) {
                    fileName = obj.getDestFile().getName();
                    fileAbsolutePath = obj.getDestFile().getAbsolutePath();
                    if (tipo.equalsIgnoreCase("xls") || tipo.equalsIgnoreCase("xlsx")) {
                        setOperacaoOnComplete("abrirPopup('../UpdateServlet?op=downloadfile&file="+fileName+"&mimetype=application/vnd.ms-excel','Transacoes', 800,200);");
                    } else if (tipo.equalsIgnoreCase("pdf")) {
                        setOperacaoOnComplete("abrirPopup('../UpdateServlet?op=downloadfile&file="+fileName+"&mimetype=application/pdf','Transacoes', 800,200);");
                    }
                } else {
                    montarMsgAlert("Arquivo não foi gerado!");
                }
            } else {
                montarMsgAlert("Parâmetros para exportação incompletos.");
            }
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
            Uteis.logar(e, ExportadorListaControle.class);
        }
    }

    private InputStream obterLogo(String nomeEmpresa) throws Exception {
        EmpresaVO empresa;
        InputStream logo;
        if (nomeEmpresa.equals("")) {
            logo = getImagem();
        } else {
            empresa = getFacade().getEmpresa().consultarPorNomeEmpresa(nomeEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            empresa.setFotoRelatorio(FacadeManager.getFacade().getEmpresa().obterFoto(getKey(), empresa.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
            if (empresa.getFotoRelatorio() == null || empresa.getFotoRelatorio().length == 0) {
                logo = getImagem();
            } else {
                InputStream fs = new ByteArrayInputStream(empresa.getFotoRelatorio());
                logo = fs;
            }
        }
        return logo;
//        ((Map) request.getAttribute("parametrosRelatorio")).put("logoPadraoRelatorio", logo);
//        ((Map) request.getAttribute("parametrosRelatorio")).put("empresaVO.cnpj", empresa.getCNPJ());
//        ((Map) request.getAttribute("parametrosRelatorio")).put("empresaVO.endereco", empresa.getEndereco() + " - " + empresa.getCidade().getNome() + " - " + empresa.getEstado().getDescricao());
//        ((Map) request.getAttribute("parametrosRelatorio")).put("empresaVO.fone", empresa.getTelComercial1());
//        ((Map) request.getAttribute("parametrosRelatorio")).put("empresaVO.site", empresa.getSite());
    }

    public InputStream getImagem() throws Exception {
        String caminhoApp = obterCaminhoWebAplicacaoFoto();
        String caminho = caminhoApp + File.separator + "fotos"  + File.separator + "logoPadraoRelatorio.jpg";
        File imagem = new File(caminho);
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        byte buffer[] = new byte[4096];
        int bytesRead = 0;
        FileInputStream fi = new FileInputStream(imagem.getAbsolutePath());
        while ((bytesRead = fi.read(buffer)) != -1) {
            arrayOutputStream.write(buffer, 0, bytesRead);
        }
        byte[] a = (arrayOutputStream.toByteArray());
        InputStream fs = new ByteArrayInputStream(a);
        arrayOutputStream.close();
        fi.close();
        return fs;

    }

    public InputStream getImagemGrafico() {
        return imagemGrafico;
    }

    public void setImagemGrafico(InputStream imagemGrafico) {
        this.imagemGrafico = imagemGrafico;
    }

    public String getOperacaoOnComplete() {
        return operacaoOnComplete;
    }

    public void setOperacaoOnComplete(String operacaoOnComplete) {
        this.operacaoOnComplete = operacaoOnComplete;
    }
}

package br.com.pactosolucoes.estrutura.paginacao;

import java.io.Serializable;
import java.sql.ResultSet;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

/**
 * Classe que ficara responsavel controlar a paginacao
 * 
 * <AUTHOR>
 */
public class ConfPaginacao implements Serializable{
	//SQL SELECT para totalização (contagem) de registros
	private static final String SQL_COUNT = "SELECT COUNT(1) as cont ";
	//pagina inicial
	private int pagIni;
	//decorator de PreparedStatement 
	PreparedStatementPersonalizado stm;

	//variáveis da paginação
	private boolean existePaginacao;
	private int paginaAtual;
	private int itensPorPagina;
	private int numeroTotalItens;
	private String paginaAtualDeTodas = "0/0";
	private String pagNavegacao;
	
	//variáveis de exibição
	private boolean apresentarPrimeiro;
	private boolean apresentarUltimo;
	private boolean apresentarAnterior;
	private boolean apresentarPosterior;

	// auxiliar (do from pra frente)
	private StringBuffer sql = new StringBuffer();
	// sql para totalização
	private StringBuffer sqlCount = new StringBuffer(SQL_COUNT);
	// filtros
	private StringBuffer sqlFiltro = new StringBuffer();
	//objeto de persistência (DAO)
	private SuperEntidade superEntidade;
	//Variavel que defini se a consulta será paginada em banco ou nao
	private boolean paginarBanco = true;

	private Boolean ordernar = false;
	private String colunaOrdenacao;
	private String direcaoOrdenacao;

	/**
	 * Construtor
	 */
	public ConfPaginacao() {
		inicializarPaginacao(Uteis.TAMANHOLISTA);
	}

        public ConfPaginacao(Integer nrItens) {
		inicializarPaginacao(nrItens);
	}

	private void inicializarPaginacao(int nrItens){
		this.setExistePaginacao(false);
		this.setPaginaAtual(1);
		this.setItensPorPagina(nrItens);
		this.setPagNavegacao("pagInicial");
	}
	
	/**
	 * METODO RESPONSAVEL POR DEFINIR QUAL É A PAGINA ATUAL
	 * QUE DEPENDERA DO VALOR QUE O ATRIBUTO "pagNavegacao"
	 * DO OBJETO "ConfPaginacao" QUE ESTA CONTIDO NO OBJETO
	 * "SuperControle"
	 * 
	 * Obs.: Este é o primeiro metodo que devera ser chamado na
	 * classe DAO
	 *
	 * Autor: Pedro Y. Saito
	 * Criado em 06/01/2011
	 */
	public void configurarNavegacao() throws Exception {
		if (isPaginarBanco()){
			if ("pagInicial".equals(pagNavegacao)) {
				irPaginaInicial();
			} else if ("pagAnterior".equals(pagNavegacao)) {
				irPaginaAnterior();
			} else if ("pagPosterior".equals(pagNavegacao)) {
				if (paginaAtual < getNrTotalPaginas()) {
					irPaginaPosterior();
				}
			} else if ("pagFinal".equals(pagNavegacao)) {
				irPaginaFinal();
			}
			setPagNavegacao("");
		}
	}

	public void definirVisibilidadeLinksNavegacao() {
		this.setPaginaAtualDeTodas(paginaAtual + "/" + this.getNrTotalPaginas());

		this.setExistePaginacao(true);
		
		if (paginaAtual == 1 && getNrTotalPaginas() > 1) {
			this.setApresentarPrimeiro(false);
			this.setApresentarAnterior(false);
			this.setApresentarPosterior(true);
			this.setApresentarUltimo(true);
		} else if (paginaAtual == 1 && getNrTotalPaginas() == 1) {
			this.setApresentarPrimeiro(false);
			this.setApresentarAnterior(false);
			this.setApresentarPosterior(false);
			this.setApresentarUltimo(false);
		} else if (paginaAtual > 1 && paginaAtual != getNrTotalPaginas()) {
			this.setApresentarPrimeiro(true);
			this.setApresentarAnterior(true);
			this.setApresentarPosterior(true);
			this.setApresentarUltimo(true);
		} else if (paginaAtual == getNrTotalPaginas() && getNrTotalPaginas() != 0) {
			this.setApresentarPrimeiro(true);
			this.setApresentarAnterior(true);
			this.setApresentarPosterior(false);
			this.setApresentarUltimo(false);
		} else if (numeroTotalItens == 0) {
			this.setPaginaAtualDeTodas("0/0");
			this.setApresentarPrimeiro(false);
			this.setApresentarAnterior(false);
			this.setApresentarPosterior(false);
			this.setApresentarUltimo(false);			
		}
	}

	/**
	 * Definindo pagina inicial
	 * 
	 * Autor: Pedro Y. Saito Criado em 27/12/2010
	 */
	private void irPaginaInicial() throws Exception {
		this.setPaginaAtual(1);
	}

	/**
	 * Definindo pagina anterior
	 * 
	 * Autor: Pedro Y. Saito Criado em 27/12/2010
	 */
	private void irPaginaAnterior() throws Exception {
		this.setPaginaAtual(this.getPaginaAtual() - 1);
	}

	/**
	 * Definindo pagina posterior
	 * 
	 * Autor: Pedro Y. Saito Criado em 27/12/2010
	 */
	private void irPaginaPosterior() throws Exception {
		this.setPaginaAtual(this.getPaginaAtual() + 1);
	}

	/**
	 * Definindo pagina final
	 * 
	 * Autor: Pedro Y. Saito Criado em 27/12/2010
	 */
	private void irPaginaFinal() throws Exception {
		this.setPaginaAtual(this.getNrTotalPaginas());
	}

	/**
	 * Calcula o numero total de paginas, mas ja devera estar definido o numero total de itens
	 * 
	 * Autor: Pedro Y. Saito Criado em 27/12/2010
	 */
	public int getNrTotalPaginas() {
		double tamanhoPagina = this.getItensPorPagina();
		double nrPaginasDouble = Math.ceil(numeroTotalItens / tamanhoPagina);
		String nrTotalPaginas = String.valueOf(nrPaginasDouble);
		nrTotalPaginas = nrTotalPaginas.substring(0, nrTotalPaginas.indexOf("."));
		return (Integer.parseInt(nrTotalPaginas));
	}

	/**
	 * @return O campo existePaginacao.
	 */
	public boolean isExistePaginacao() {
		return this.existePaginacao;
	}

	/**
	 * @param existePaginacao
	 *            O novo valor de existePaginacao.
	 */
	public void setExistePaginacao(boolean existePaginacao) {
		this.existePaginacao = existePaginacao;
	}

	/**
	 * @return O campo paginaAtual.
	 */
	public int getPaginaAtual() {
		return this.paginaAtual;
	}

	/**
	 * @param paginaAtual
	 *            O novo valor de paginaAtual.
	 */
	public void setPaginaAtual(int paginaAtual) {
		this.paginaAtual = paginaAtual;
	}

	/**
	 * @return O campo itensPorPagina.
	 */
	public int getItensPorPagina() {
		return this.itensPorPagina;
	}

	/**
	 * @param itensPorPagina
	 *            O novo valor de itensPorPagina.
	 */
	public void setItensPorPagina(int itensPorPagina) {
		this.itensPorPagina = itensPorPagina;
	}

	/**
	 * @return O campo numeroTotalItens.
	 */
	public int getNumeroTotalItens() {
		return this.numeroTotalItens;
	}

	/**
	 * @param numeroTotalItens
	 *            O novo valor de numeroTotalItens.
	 */
	public void setNumeroTotalItens(int numeroTotalItens) {
		this.numeroTotalItens = numeroTotalItens;
		if (this.numeroTotalItens > this.itensPorPagina) {
			this.existePaginacao = true;
		}
	}

	/**
	 * @return O campo paginaAtualDeTodas.
	 */
	public String getPaginaAtualDeTodas() {
		return this.paginaAtualDeTodas;
	}

	/**
	 * @param paginaAtualDeTodas
	 *            O novo valor de paginaAtualDeTodas.
	 */
	public void setPaginaAtualDeTodas(String paginaAtualDeTodas) {
		this.paginaAtualDeTodas = paginaAtualDeTodas;
	}

	/**
	 * @return O campo pagNavegacao.
	 */
	public String getPagNavegacao() {
		return this.pagNavegacao;
	}

	/**
	 * @param pagNavegacao
	 *            O novo valor de pagNavegacao.
	 */
	public void setPagNavegacao(String pagNavegacao) {
		this.pagNavegacao = pagNavegacao;
	}

	/**
	 * @return O campo apresentarPrimeiro.
	 */
	public boolean isApresentarPrimeiro() {
		return this.apresentarPrimeiro;
	}

	/**
	 * @param apresentarPrimeiro
	 *            O novo valor de apresentarPrimeiro.
	 */
	public void setApresentarPrimeiro(boolean apresentarPrimeiro) {
		this.apresentarPrimeiro = apresentarPrimeiro;
	}

	/**
	 * @return O campo apresentarUltimo.
	 */
	public boolean isApresentarUltimo() {
		return this.apresentarUltimo;
	}

	/**
	 * @param apresentarUltimo
	 *            O novo valor de apresentarUltimo.
	 */
	public void setApresentarUltimo(boolean apresentarUltimo) {
		this.apresentarUltimo = apresentarUltimo;
	}

	/**
	 * @return O campo apresentarAnterior.
	 */
	public boolean isApresentarAnterior() {
		return this.apresentarAnterior;
	}

	/**
	 * @param apresentarAnterior
	 *            O novo valor de apresentarAnterior.
	 */
	public void setApresentarAnterior(boolean apresentarAnterior) {
		this.apresentarAnterior = apresentarAnterior;
	}

	/**
	 * @return O campo apresentarPosterior.
	 */
	public boolean isApresentarPosterior() {
		return this.apresentarPosterior;
	}

	/**
	 * @param apresentarPosterior
	 *            O novo valor de apresentarPosterior.
	 */
	public void setApresentarPosterior(boolean apresentarPosterior) {
		this.apresentarPosterior = apresentarPosterior;
	}

	/**
	 * @return O campo sql.
	 */
	public StringBuffer getSql() {
		return this.sql;
	}

	/**
	 * @param sql
	 *            O novo valor de sql.
	 */
	public void setSql(StringBuffer sql) {
		this.sql = sql;
	}

	/**
	 * @return O campo sqlCount.
	 */
	public StringBuffer getSqlCount() {
		return this.sqlCount;
	}

	/**
	 * @param sqlCount
	 *            O novo valor de sqlCount.
	 */
	public void setSqlCount(StringBuffer sqlCount) {
		this.sqlCount = sqlCount;
	}

	/**
	 * @return O campo sqlFiltro.
	 */
	public StringBuffer getSqlFiltro() {
		return this.sqlFiltro;
	}

	/**
	 * @param sqlFiltro
	 *            O novo valor de sqlFiltro.
	 */
	public void setSqlFiltro(StringBuffer sqlFiltro) {
		this.sqlFiltro = sqlFiltro;
	}

	/**
	 * Associa a uma DAO este objeto de paginação
	 * 
	 * @param superEntidade SuperEntidade
	 */
	public void iniciarPaginacao(SuperEntidade superEntidade) {
		this.superEntidade = superEntidade;
		
		if (isPaginarBanco()){
			pagIni = (this.getItensPorPagina() * (this.getPaginaAtual() - 1));
		}
		stm = new PreparedStatementPersonalizado();

		this.sql = new StringBuffer();
		this.sqlCount = new StringBuffer(SQL_COUNT);
		this.sqlFiltro = new StringBuffer();
	}

	/**
	 * @return the stm
	 */
	public PreparedStatementPersonalizado getStm() {
		return stm;
	}

	/**
	 * Chamado para adicionar a consulta as propriedades de paginação em banco
	 * Também é responsável por preencher Strings de consultas auxiliares desta classe
	 * 
	 * @param sqlSelect StringBuffer
	 * @throws Exception
	 */
    public void addPaginacao(StringBuffer sqlSelect) throws Exception {
		addPaginacao(sqlSelect, true);
	}
    public void addPaginacao(StringBuffer sqlSelect, boolean usarOffset) throws Exception {
        //string temporário para manipular a consulta
        String a = sqlSelect.toString();
        //captura somente a cláusula FROM + WHERE do select
        //remove *select e *order by
        int parametro = a.lastIndexOf("ORDER BY");
        if (parametro > 0) {
            sqlCount.append(a, a.indexOf("FROM"), a.indexOf("ORDER BY"));
        } else {
            sqlCount.append(a.substring(a.indexOf("FROM")));
        }

        if (this.isPaginarBanco()) {
            //adiciona a paginação
            sqlSelect.append(" limit ").append(usarOffset ? getItensPorPagina() : getItensPorPagina() + pagIni);
			if (usarOffset) {
				sqlSelect.append(" offset ").append(pagIni);
			}
        }

        //seta o prepared statement
        this.setPreparedStatement(sqlSelect.toString());
    }

	/**
	 * Esta classe encapsula um statement
	 * 
	 * @param pSql pSql
	 * @throws Exception
	 */
	public void setPreparedStatement(String pSql) throws Exception {
		this.getStm().setPreparedStatement(superEntidade.getCon(), pSql);
	}
	
	/**
	 * Executa a query do statement.
	 * Seta o número total de itens da paginação
	 * Realiza a consulta.
	 *  
	 * @return ResultSet
	 * @throws Exception
	 */
	public ResultSet consultaPaginada() throws Exception {
		ResultSet tabelaResultado = getStm().executeQuery();
		
		getStm().setPreparedStatement(superEntidade.getCon(), getSqlCount().toString());
		setNumeroTotalItens(superEntidade.numeroRegistrosTabela(getStm()));
//		System.out.println("Numero total de registros: " + getNumeroTotalItens());
		
		definirVisibilidadeLinksNavegacao();
		
		return tabelaResultado;
	}
	
	public ResultSet consultaPaginada(Integer total) throws Exception {
		ResultSet tabelaResultado = getStm().executeQuery();
		setNumeroTotalItens(total);
		definirVisibilidadeLinksNavegacao();
		
		return tabelaResultado;
	}

	/**
	 * @return O campo paginarBanco.
	 */
	public boolean isPaginarBanco() {
		return this.paginarBanco;
	}

	/**
	 * @param paginarBanco O novo valor de paginarBanco.
	 */
	public void setPaginarBanco(boolean paginarBanco) {
		this.paginarBanco = paginarBanco;
	}

	public Boolean getOrdernar() {
		return ordernar;
	}

	public void setOrdernar(Boolean ordernar) {
		this.ordernar = ordernar;
	}

	public String getColunaOrdenacao() {
		return colunaOrdenacao;
	}

	public void setColunaOrdenacao(String colunaOrdenacao) {
		this.colunaOrdenacao = colunaOrdenacao;
	}

	public String getDirecaoOrdenacao() {
		return direcaoOrdenacao;
	}

	public void setDirecaoOrdenacao(String direcaoOrdenacao) {
		this.direcaoOrdenacao = direcaoOrdenacao;
	}

	public void setPagIni(int pagIni) {
		this.pagIni = pagIni;
	}
}

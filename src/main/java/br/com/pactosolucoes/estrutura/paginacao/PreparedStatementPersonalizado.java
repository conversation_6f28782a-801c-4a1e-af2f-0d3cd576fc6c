/**
 * 
 */
package br.com.pactosolucoes.estrutura.paginacao;

import java.io.Serializable;
import java.sql.Array;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.ArrayList;

/**
 * <AUTHOR>
 *
 */
public class PreparedStatementPersonalizado implements Serializable{

    private transient PreparedStatement preparedStatement;
    private ArrayList params;
    private String sql;

    /**
     * Construtor
     */
    public PreparedStatementPersonalizado() {
        params = new ArrayList();
    }

    /* (non-Javadoc)
     * @see java.sql.PreparedStatement#setString(int, java.lang.String)
     */
    @SuppressWarnings("unchecked")
    public void setString(int arg0, String arg1) throws SQLException {
        params.add(arg0, arg1);
    }

    /**
     * @return O campo preparedStatement.
     */
    public PreparedStatement getStm() {
        return this.preparedStatement;
    }

    /**
     * @param preparedStatement O novo valor de preparedStatement.
     */
    public void setPreparedStatement(PreparedStatement preparedStatement) {
        this.preparedStatement = preparedStatement;
    }

    /**
     * Atribui o statement específico no PreparedStatementPersonalizado.
     * @throws SQLException
     */
    public void setPreparedStatement(Connection con, String pSql) throws SQLException {
        this.sql = pSql;
        this.preparedStatement = con.prepareStatement(pSql);
    }

    /**
     * @return O campo params.
     */
    public ArrayList getParams() {
        return this.params;
    }

    /**
     * @param params O novo valor de params.
     */
    public void setParams(ArrayList params) {
        this.params = params;
    }

    /**
     * @return O campo sql.
     */
    public String getSql() {
        return this.sql;
    }

    /**
     * @param sql O novo valor de sql.
     */
    public void setSql(String sql) {
        this.sql = sql;
    }

    /**
     * Atribui os valores passados para o Resultset ao
     * resultset especifico.
     *
     * Autor: Pedro Y. Saito
     * Criado em 27/12/2010
     */
    private void prepararParametros() throws SQLException {
        int size = params.size();
        Object aux = null;

        for (int x = 1; x <= size; x++) {
            aux = params.get(x - 1);

            if (aux instanceof Integer) {
                this.preparedStatement.setInt(x, ((Integer) aux).intValue());
            } else if (aux instanceof String) {
                this.preparedStatement.setString(x, (String) aux);
            } else if (aux instanceof Date) {
                this.preparedStatement.setDate(x, (Date) aux);
            } else if (aux instanceof java.util.Date) {
                this.preparedStatement.setTimestamp(x, new Timestamp(((java.util.Date) aux).getTime()));
            } else if (aux instanceof Timestamp) {
                this.preparedStatement.setTimestamp(x, (Timestamp) aux);
            } else if (aux instanceof Double) {
                this.preparedStatement.setDouble(x, ((Double) aux).doubleValue());
            } else if (aux instanceof Float) {
                this.preparedStatement.setFloat(x, ((Float) aux).floatValue());
            } else if (aux instanceof Long) {
                this.preparedStatement.setLong(x, ((Long) aux).longValue());
            } else if (aux instanceof Byte) {
                this.preparedStatement.setByte(x, ((Byte) aux).byteValue());
            } else if (aux instanceof byte[]) {
                this.preparedStatement.setBytes(x, (byte[]) aux);
            } else if (aux instanceof Array) {
                this.preparedStatement.setArray(x, (Array) aux);
            } else if (aux instanceof Boolean) {
                this.preparedStatement.setBoolean(x, ((Boolean) aux).booleanValue());
            } else if (aux instanceof Short) {
                this.preparedStatement.setShort(x, ((Short) aux).shortValue());
            } else if (aux instanceof Time) {
                this.preparedStatement.setTime(x, (Time) aux);
            }
        }
    }

    /**
     * TODO Descricao do metodo
     *
     * Autor: Pedro Y. Saito
     * Criado em 30/12/2010
     * @throws SQLException
     *
     *
     */
    public ResultSet executeQuery() throws SQLException {
        this.prepararParametros();
        return this.preparedStatement.executeQuery();
    }

    /**
     * TODO Descricao do metodo
     *
     * Autor: Pedro Y. Saito
     * Criado em 30/12/2010
     *
     *
     */
    public void setDate(int i, Date dataJDBC) {
        params.add(i, dataJDBC);

    }

    public void setTimestamp(int i, Timestamp dataJDBC) {
        params.add(i, dataJDBC);
        
    }

    /**
     * TODO Descricao do metodo
     *
     * Autor: Pedro Y. Saito
     * Criado em 30/12/2010
     *
     *
     */
    public void setInt(int i, Integer valor) {
        params.add(i, valor);

    }
}

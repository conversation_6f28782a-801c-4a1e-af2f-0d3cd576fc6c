package br.com.pactosolucoes.estrutura;

import negocio.comuns.utilitarias.UteisValidacao;

public enum ItemExportacaoEnum {
    ANIVERSARIANTES("aniversariantesRel", "Relatório Aniversariantes", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CLIENTE("cliente", "Cadastro Clientes" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_CLIENTES("relClientes", "Relatório de Clientes" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_CANCELADOS("relCancelados", "Relatório de Clientes Cancelados" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_ATESTADOS("relAtestados", "Relatório de Clientes com Atestado" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_BONUS("relBonus", "Relatório de Clientes com Bônus" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_TRANCADOS("relTrancados", "Relatório de Clientes Trancados" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_CLIENTES_SIMPLIFICADO("relClientesSimplificado", "Lista de Clientes Simplificada" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    RECIBOS("recibos", "Consulta de Recibos" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_COMPETENCIA("competenciaRel", "Relatório Competência Mensal" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CONTROLE_LOG("controleLog", "Controle de Log" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_FATURAMENTO("faturamentoRel", "Relatório Faturamento por Período" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_FATURAMENTO_RECEBIDO("faturamentoRecebidoRel", "Relatório Faturamento Recebido por Período" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    GERAL_CLIENTES("geralClientes", "Relatório Geral de Clientes" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    MOV_CC_CLIENTE("movCCCliente", "Movimento de Conta Corrente do Cliente" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_ORCAMENTOS("relOrcamentos", "Relatório de Orçamentos" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_PARCELAS("relParcelas", "Relatório de Parcelas" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_RENOVACAO("relRenovacao", "Relatório de Previsão de Renovação" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    VENDAS_CONSUMIDOR("vendasConsumidor", "Venda Consumidor" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_CLIENTE_DEBITO("penClienteDebito", "BI PENDÊNCIA - Débito em Conta Corrente" , "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_BV_PENDENTE("penBVPendente", "BI PENDÊNCIA - BV Pendente",  "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_CADASTROS_INCOMPLETOS_CLIENTE("penCadastroImcompletoCliente", "BI PENDÊNCIA - Cadastro Incompleto (Cliente)","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_CADASTROS_INCOMPLETOS_VISITANTE("penCadastroImcompletoVisitante", "BI PENDÊNCIA - Cadastro Incompleto (Visitante)", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_PARCELAS_A_PAGAR("penParcelasAPagar", "BI PENDÊNCIA - Parcelas a Pagar", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_PARCELAS_ATRASO("penParcelasAtraso", "BI PENDÊNCIA - Parcelas em Atraso", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_CREDITO_CC("penCreditoCC", "BI PENDÊNCIA - Crédito em Conta Corrente", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),

    PENDENCIA_ANIVERSARIANTES("penAniversariantes", "BI PENDÊNCIA - Aniversariantes do dia", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_ANIVERSARIANTES_COL("penAniversariantesCol", "BI PENDÊNCIA - Aniversariantes Colaboradores", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_PARCELA_ABERTO_COLABORADOR("penParcelaCol", "BI PENDÊNCIA - Colab. c/ Parcelas a Pagar", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_PRODUTO_VENCIDO("penProdutoVencido", "BI PENDÊNCIA - Produtos Vencidos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_SEM_PRODUTOS("penSemProduto", "BI PENDÊNCIA - Sem Produtos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_CARTOES_VENCIDOS("penCartoesVencidos", "BI DCC - Cartões de Crédito Vencidos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_CARTOES_AVENCER("penCartoesAVencer", "BI DCC - Cartões de Crédito A Vencer Próximo Mês", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_MESMO_CARTOES("penMesmoCartao", "BI DCC - Clientes com mesmo Cartão de Crédito", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_SEM_FOTO("penSemFoto","BI PENDÊNCIA - Sem Foto", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_TRANCADOS_VENCIDOS("penTrancadosVencidos","BI PENDÊNCIA - Trancamento Vencido",  "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_TRANSFERIDOS_CANCELADO("penTransferidosCancelados", "BI PENDÊNCIA - Transferidos com Contrato Cancelado", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_SEM_ASSINATURA("penSemAssinatura","BI PENDÊNCIA - Sem assinatura de contrato", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA__CARTOES_PROBLEMA("penCartoesComProblema", "BI PENDÊNCIA - Cartões de Credito Incompletos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_SEM_GEOLOCALIZACAO("penSemGeolocalizacao", "BI PENDÊNCIA - Sem Geolocalização", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_SEM_BIOMETRIA_FACIAl("penSemBiometriaFacial","BI PENDÊNCIA - Sem Reconhecimento Facial", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PENDENCIA_SEM_ASSINATURA_CANCELAMENTO("penSemAssinaturaCancelados","BI PENDÊNCIA - Sem assinatura de cancelamento de contrato", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_RISCO("biRisco","Grupo de Risco", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_CANCELADO("rotCancelados","BI MOVIMENTAÇÃO CONTRATO - Cancelados", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_DESISTENTE("rotDesistentes","BI MOVIMENTAÇÃO CONTRATO - Desistentes", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_MATRICULADO("rotMatriculados","BI MOVIMENTAÇÃO CONTRATO - Matriculados", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_REMATRICULADO("rotRematriculados","BI MOVIMENTAÇÃO CONTRATO - Rematriculados", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_RETORNO_TRANCADO("rotRetornoTrancado","BI MOVIMENTAÇÃO CONTRATO - Retorno Trancado", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_TOTAL_VIGENTES("rotAtivosVencidosMes","BI MOVIMENTAÇÃO CONTRATO - Total de Ativos + Vencidos do mês", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_TRANCADO("rotTrancados","BI MOVIMENTAÇÃO CONTRATO - Trancados", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_CONTRATO_TRANSFERIDO("rotTransferidos","BI MOVIMENTAÇÃO CONTRATO - Contratos Transferidos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_VENCIDOS_MES("rotVencidosMes","BI MOVIMENTAÇÃO CONTRATO - Vencidos do mês", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_VIGENTES_MES_ANTERIOR("rotAtivosVencidosInicio","BI MOVIMENTAÇÃO CONTRATO - Total de Ativos + Vencidos Início Mês", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_ATIVOS_MES_ANTERIOR("rotAtivosInicio","BI MOVIMENTAÇÃO CONTRATO - Total de Ativos Início do Mês", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_VENCIDOS_MES_ANTERIOR("rotVencidosInicio","BI MOVIMENTAÇÃO CONTRATO - Vencidos Início do Mês", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_ATIVOS_MES("rotAtivosMes","BI MOVIMENTAÇÃO CONTRATO - Total de Ativos do mês", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_SALDO_MES("rotSaldoMês","BI MOVIMENTAÇÃO CONTRATO - Saldo do mês", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_DEPENDENTES_MES_ANTERIOR("rotDependentesInicio","BI MOVIMENTAÇÃO CONTRATO - Dependentes Início Mês", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_DEPENDENTES_MES("rotDependentesMes","BI MOVIMENTAÇÃO CONTRATO - Dependentes do mês", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_DEPENDENTES_VINCULADO("rotDependentesVinculados","BI MOVIMENTAÇÃO CONTRATO - Dependentes Vinculados", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_DEPENDENTES_DESVINCULADO("rotDependentesDesvinculados","BI MOVIMENTAÇÃO CONTRATO - Dependentes Desvinculados", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ROTATIVIDADE_AGREGADORESS_VINCULADO("rotAgregadoresVinculados","BI MOVIMENTAÇÃO CONTRATO - Clientes do contrato com agregadores", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_IR_MESES_FUTUROS("irMesesFuturos" ,"BI ÍNDICE RENOVAÇÃO - Renovados de Meses Futuros","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_IR_PREVISAO_MES("irPrevisaoMes" ,"BI ÍNDICE RENOVAÇÃO - Previsão","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_IR_MES_PASSADO("irMesPassado" ,"BI ÍNDICE RENOVAÇÃO - Renovados do Mes Passado","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_IR_RENOVADOS_TOTAL("irRenovadosTotal" ,"BI ÍNDICE RENOVAÇÃO - Renovados do Mês","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_IR_DENTRO_MES("irDoMes" ,"BI ÍNDICE RENOVAÇÃO - Da Previsão do Mês","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_IR_RENOVADOS_PREVISAO("irDaPrevisao" ,"BI ÍNDICE RENOVAÇÃO - Renovados da Previcao","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_IR_NAO_RENOVADOS_PREVISAO("irNaoRenovadosDaPrevisao" ,"BI ÍNDICE RENOVAÇÃO - Não Renovados da Previsão","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_IR_ALUNOS_MUITO_PROFESSORES("irMuitoProfessores" ,"BI ÍNDICE RENOVAÇÃO - Alunos com mais de um professor","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_ICV_BV("icvBV" ,"BI ICV - BV","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_ICV_MATRICULAS("icvMatriculas" ,"BI ICV - Matrículas","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_ICV_REMATRICULAS("icvRematriculas" ,"BI ICV - Rematrículas","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_CONTRATOS_ATIVOS("dccContratosAtivos","BI DCC - Contratos ativos por regime de Recorrência","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_CONTRATOS_CANCELADOS("dccContratosCancelados","BI DCC - Contratos cancelados automaticamente pela Recorrência","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_CONTRATOS_NAO_RENOVADOS("dccContratosNaoRenovados","BI DCC - Contratos que não conseguiram renovação automaticamente","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_CONTRATOS_SEM_AUTORIZACAOCOBRANCA("dccContratosSemAutorizacaoCobranca","BI DCC - Contratos ativos por regime de Recorrência sem Autorização de Cobrança","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_PARCELASCANCELADAS("dccParcelasCanceladas","BI DCC - Alunos com Parcelas Canceladas","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_PARCELASVENCIDAS_EMABERTO("dccParcelasVencidasEmAberto","BI DCC - Parcelas Vencidas em aberto","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_PARCELAS_EMABERTO("dccParcelasEmAberto","BI DCC - Parcelas em aberto","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_OPERACOESSUSPEITAS("dccOperacoesSuspeitas","BI DCC - Operações Suspeitas","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_ALUNOSADIMPLENTES("dccAdimplentes","BI DCC - Alunos Adimplentes","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_ENVIADA_COM_RETORNO("dccEnviadaComRetorno","BI DCC - Enviadas com retorno","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),

    BI_DCC_PAGAS_CONVENIO("dccPagasPeloConvenio","BI DCC - Pagas pelo convênio","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_PAGAS_FORA_CONVENIO("dccPagasForaConvenio","BI DCC - Pagas fora do convênio","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_DCC_NAO_PAGAS_ATE_HOJE("dccNaoPagaAteHoje","BI DCC - Não pagas até hoje","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_BV("relBV","Relatório de BVs","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_COBRANCA_BLOQUEADA("relCobrancaAutomaticaBloqueada","Relatório de Clientes com cobrança Automática bloqueada","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CONSULTA_TURMA("consultaTurma","Relatório de Consulta de Turma","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_DESCONTO_POR_OCUPACAO("relDescontoPorOcupacao","Relatório de Desconto Por Ocupação na Turma","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_FECHAMENTO_ACESSOS("relFechamentoAcessos","Relatório de Fechamento de Acessos","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_COMISSAO_PROFESSOR_SINTETICO("relComissaoProfessorSintetico","Relatório de Comissão por Professor Sintético","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_COMISSAO_PROFESSOR_ANALITICO("relComissaoProfessorAnalitico","Relatório de Comissão por Professor Analítico","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_FREQUENCIA_OCUPACAO("relFrequenciaOcupacao","Relatório de Frenquência e Ocupação de Turma","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    MAPA_TURMA("mapaTurma","Mapa de Turmas","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_REPASSE("relRepasse","Relatório de Frenquência e Ocupação de Turma","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_CONVITE_EXPERIMENTAL("relConviteExperimental","Relatório de Convite Aula Experimental","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_HISTORICO_PONTOS("relPontuacaoAlunos","Relatório de Pontuação de Alunos","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_PESQUISA("relPesquisa","Relatório de Pesquisa","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_HISTORICO_PESQUISA("relRepasse","Relatório de Frenquência e Ocupação de Turma","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_VERIFICACAO_TOTAL("biVerificaoTotal","BI VERIFICAÇÃO - Clientes marcados para verificar","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_VERIFICACAO_NAO_VERIFICADOS("biVerificaoNaoVerificados","BI VERIFICAÇÃO - Clientes que não foram verificados","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_VERIFICACAO_VERIFICADOS("biVerificaoVerificados","BI VERIFICAÇÃO - Clientes verificados","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_META_FINANCEIRA("biMetaFinanceira","BI META FINANCEIRA - Detalhes do Cumprimento da Meta Financeira","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REL_AULA_EXPERIMENTAL("relAulaExperimental","Relatório de Agendamentos","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_INADIMPLENCIA_PREVISAO("biInadimplenciaPrevisao","BI Inadimplência - Previsão","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_INADIMPLENCIA_RECEBIDO("biInadimplenciaRecebido","BI Inadimplência - Recebidos","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_INADIMPLENCIA_INADIMPLENCIA("biInadimplenciaInadimplencia","BI Inadimplência - Inadimplentes","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_LTV_LT("biLtvLT","BI LTV - Lista Alunos Life Time","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_INATIVOS_COM_PERIODOACESSO("biExcecoesInativosComPeriodoAcesso","BI EXCEÇÕES - Clientes com Contrato Inativo com Período de Acesso","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_OPERACOES_RETROATIVAS("biExcecoesOpercoesRetroativas","BI EXCEÇÕES - Operações de Contrato Retroativas","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_EXCLUSAO_VISITANTES("biExcecoesExclusaoVisitantes","BI EXCEÇÕES - Exclusão de Visitantes","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_ALTERACAO_CONSULTOR("biExcecoesConsultoresAlterados","BI EXCEÇÕES - Consultores de Contrato Alterados","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_ESTORNOCONTRATO_ADMIN("biExcecoesEstornoAdmin","BI EXCEÇÕES - Estornos de Contrato - Administrador","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_ESTORNOCONTRATO_USUARIOCOMUM("biExcecoesEstornoComum","BI EXCEÇÕES - Estornos de Contrato - Usuário Comum","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_ESTORNOCONTRATO_RECORRENCIA("biExcecoesEstornoRecor","BI EXCEÇÕES - Estornos de Contrato - Recorrência","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_ESTORNO_RECIBO("biExcecoesEstornoRecibo","BI EXCEÇÕES - Estornos de Recibo","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_DATABASE_CONTRATO("biExcecoesContratosDataBase","BI EXCEÇÕES - Contratos com DataBase Alterada","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_DATABASE_PAGAMENTO("biExcecoesPagamentoDataBase","BI EXCEÇÕES - Pagamentos com DataBase Alterada","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_RENEGOCIACAO_PARCELA_RETROATIVA("biExcecoesRenegociacaoParcelas","BI EXCEÇÕES - Renegociações de parcelas","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_ALTERACOES_RECIBO("biExcecoesEdicaoPagamento","BI EXCEÇÕES - Edições de Pagamento","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_CONTRATOS_CANCELAMENTO("biExcecoesContratosCancelados","BI EXCEÇÕES - Contratos Cancelados","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_CLIENTES_BONUS("biExcecoesClientesBonus","BI EXCEÇÕES - Clientes com Bônus","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_CLIENTES_FREEPASS("biExcecoesClientesFreePass","BI EXCEÇÕES - Clientes com FreePass","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_CLIENTES_GYMPASS("biExcecoesClientesGymPass","BI EXCEÇÕES - Clientes com GymPass","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_PARCELAS_CANCELADAS("biExcecoesParcelasCanceladas","BI EXCEÇÕES - Parcelas Canceladas","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_CONTRATOS_BOLSA("biExcecoesAlunosBolsa","BI EXCEÇÕES - Alunos Bolsa","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_CLIENTES_AUTORIZACAO_NAORENOVAVEL("biExcecoesAutorizacaoSemRenovacao","BI EXCEÇÕES - Clientes com autorização sem renovação automática","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_VALORDESCONTOS("biExcecoesValorDescontos","BI EXCEÇÕES - Valor em descontos","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_CLIENTES_AUTORIZACAO_RENOVAVEL("biExcecoesAutorizacaoComRenovacao","BI EXCEÇÕES - Clientes com autorização e renovação automática","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_EXCLUIDOS_TREINOWEB("biExcecoesExcluidosTreinoweb","BI EXCEÇÕES - Alunos excluídos do treinoweb que possuíam vínculos","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_CONTRATOS_TRANSFERIDOSCANCELADOS("biExcecoesTransferidosCancelados","BI EXCEÇÕES - Transferidos com Contrato Cancelados","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_EXCECOES_CLIENTES_EXCLUIDOS("biExcecoesClientesExcluidos","BI EXCEÇÕES - Alunos excluídos da base de dados","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_ACESSO_TEMPO_REAL("biAcessoTempoReal","BI ACESSOS - Alunos em tempo real","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_ACESSO_ALUNOS_AULAAGENDADA("biAcessoAlunosAulasAgendadas","BI ACESSOS - Alunos em aulas agendadas","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_ACESSO_BLOQUEADOS("biAcessobloqueados","BI ACESSOS - Acessos bloqueados hoje","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_ACESSO_REALIZADOS_HOJE("biAcessoRealizadosHoje","BI ACESSOS - Acessos realizados hoje","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_ACESSO_OUTRA_UNIDADE("biAcessoOutraunidade","BI ACESSOS - Alunos de outra unidade","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_ACESSO_LIBERADOS("biAcessoLiberados","BI ACESSOS - Acessos liberados","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_ACESSO_INATIVOS_COM_PERMISSAO("biAcessoInativosComPermissao","BI ACESSOS - Inativos com permissão de acesso","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_ACESSO_PENDENCIA_FINANCEIRA("biAcessoPendenciasFinanceira","BI ACESSOS - Alunos com pendências financeiras","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_GYMPASS_ACESSOS("biGymPassAcessos","BI GYMPASS - Acesso GymPass","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_GYMPASS_ALUNOS("biGymPassAlunos","BI GYMPASS - Alunos GymPass","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_AULA_EXPERIMENTAL_ICV("biAulaExperimentalICV","BI AULA EXPERIMENTAL - ICV Professores Aula Experimental","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_AULA_EXPERIMENTAL_AGENDADOS("biAulaExperimentalAgendados","BI AULA EXPERIMENTAL - Alunos Agendados","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_AULA_EXPERIMENTAL_EXECUTADOS("biAulaExperimentalExecutaram","BI AULA EXPERIMENTAL - Executaram","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BI_AULA_EXPERIMENTAL_CONVERTIDOS("biAulaExperimentalConvertidos","BI AULA EXPERIMENTAL - Convertidos","PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TELA_CLIENTES("telaClientes","Tela Clientes","ExportarRelatorioCliente", "9.66 - Exportar relatório de clientes"),
    ;


    private String id;
    private String descricaoLog;
    private String permissao;
    private String descricaoPermissao;

    ItemExportacaoEnum(String id, String descricaoLog, String permissao, String descricaoPermissao) {
        this.id = id;
        this.descricaoLog = descricaoLog;
        this.permissao = permissao;
        this.descricaoPermissao =descricaoPermissao;
    }

    public String getId() {
        return id;
    }

    public String getDescricaoLog() {
        return descricaoLog;
    }

    public String getPermissao() {
        return permissao;
    }

    public String getDescricaoPermissao() {
        return descricaoPermissao;
    }

    public static ItemExportacaoEnum obterPorId(final String id) {
        if(!UteisValidacao.emptyString(id)) {
            for (ItemExportacaoEnum i : ItemExportacaoEnum.values()) {
                if (i.getId().equals(id)) {
                    return i;
                }
            }
        }
        return null;
    }
}



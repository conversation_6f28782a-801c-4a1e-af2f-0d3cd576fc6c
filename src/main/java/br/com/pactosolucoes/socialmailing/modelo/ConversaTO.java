package br.com.pactosolucoes.socialmailing.modelo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class ConversaTO extends SuperTO{
    private static final long serialVersionUID = 3067077931630215524L;
	
	private List<SocialMailPartenerVO> parteners = new ArrayList<SocialMailPartenerVO>();
	private List<MensagemTO> mensagens = new ArrayList<MensagemTO>();
	private transient byte foto[];
	private String grupo = "";
	private String nomeConversa = "";
	private String nomeConversaResumida = "";
	private Integer nrMensagensNaoLidas = 0;
	private Date dataUltimaMSG;
	private boolean fotoIndividual = true;
	private boolean conversaCarregada = false;
	private Integer nrMensagens = 0;
	private String selected  = "";
	private Map<Integer, byte[]> fotos = new HashMap<Integer, byte[]>();
	
	public ConversaTO getClone() throws Exception{
		ConversaTO clone = new ConversaTO();
		clone.setFoto(this.foto);
		clone.setGrupo(this.grupo);
		clone.setNomeConversa(this.nomeConversa);
		clone.setNomeConversaResumida(this.nomeConversaResumida);
		clone.setNrMensagensNaoLidas(this.nrMensagensNaoLidas);
		clone.setDataUltimaMSG(this.dataUltimaMSG);
		clone.setNrMensagens(this.nrMensagens);
		clone.setFotos(this.fotos);
		for(SocialMailPartenerVO smp : this.parteners){
			clone.getParteners().add((SocialMailPartenerVO)smp.getClone(true));
		}
		for(MensagemTO msg : this.getMensagens()){
			clone.getMensagens().add(msg);
		}
		return clone;
	}
	
	public List<SocialMailPartenerVO> getParteners() {
		return parteners;
	}
	public void setParteners(List<SocialMailPartenerVO> parteners) {
		this.parteners = parteners;
	}
	public byte[] getFoto() {
		return foto;
	}
	public boolean getTemFoto(){
		return foto != null && foto.length > 0;
	}
	public void setFoto(byte[] foto) {
		this.foto = foto;
	}
	public String getGrupo() {
		return grupo;
	}
	public void setGrupo(String grupo) {
		this.grupo = grupo;
	}
	public String getNomeConversa() {
		return nomeConversa;
	}
	public void setNomeConversa(String nomeConversa) {
		this.nomeConversa = nomeConversa;
	}
	public Integer getNrMensagensNaoLidas() {
		return nrMensagensNaoLidas;
	}
	public void setNrMensagensNaoLidas(Integer nrMensagensNaoLidas) {
		this.nrMensagensNaoLidas = nrMensagensNaoLidas;
	}
	public boolean equals(Object o) {
		if (o instanceof ConversaTO) {
			ConversaTO ct = (ConversaTO) o;
			return ct.grupo.equals(this.grupo);
		} else {
			return false;
		}
	}
	public void setDataUltimaMSG(Date dataUltimaMSG) {
		this.dataUltimaMSG = dataUltimaMSG;
	}
	public Date getDataUltimaMSG() {
		return dataUltimaMSG;
	}
	public String getDataUltimaConversa(){
		return Uteis.getDataComHHMM(dataUltimaMSG);
	}

	public static byte[] obterFoto(String grupo, List<ConversaTO> conversas){
		for(ConversaTO conversa : conversas){
			if(conversa.getGrupo().equals(grupo))
				return conversa.getFoto();
		}
		return null;
	}

	public void setFotoIndividual(boolean fotoIndividual) {
		this.fotoIndividual = fotoIndividual;
	}

	public boolean getFotoIndividual() {
		return fotoIndividual;
	}

	public void setNomeConversaResumida(String nomeConversaResumida) {
		this.nomeConversaResumida = nomeConversaResumida;
	}

	public String getNomeConversaResumida() {
		return nomeConversaResumida;
	}
	
	public String getNomeConversaResumidaSemHtml() {
		return nomeConversaResumida.replace("<br/>", "");
	}

	public void setMensagens(List<MensagemTO> mensagens) {
		this.mensagens = mensagens;
	}

	public List<MensagemTO> getMensagens() {
		return mensagens;
	}

	public void setConversaCarregada(boolean conversaCarregada) {
		this.conversaCarregada = conversaCarregada;
	}

	public boolean getConversaCarregada() {
		return conversaCarregada;
	}

	public void setNrMensagens(Integer nrMensagens) {
		this.nrMensagens = nrMensagens;
	}

	public Integer getNrMensagens() {
		return nrMensagens;
	}
	
	public boolean getDisabledMais(){
		return getMensagens().size() >= nrMensagens;
	}
	
	public void agruparMsgsPorDia(){
		Ordenacao.ordenarLista(mensagens, "enviada");
		String diaAgrupamento = "";
		for(MensagemTO mensagem : mensagens){
			mensagem.setSeparator(!diaAgrupamento.equals(Uteis.getDataAplicandoFormatacao(mensagem.getEnviada(), "dd/MM")));
			diaAgrupamento = Uteis.getDataAplicandoFormatacao(mensagem.getEnviada(), "dd/MM");
		}
		Collections.reverse(mensagens);
		
	}

	public void setSelected(String selected) {
		this.selected = selected;
	}

	public String getSelected() {
		return UteisValidacao.emptyString(selected) ? "notselected" : selected;
	}

	public void setFotos(Map<Integer, byte[]> fotos) {
		this.fotos = fotos;
	}

	public Map<Integer, byte[]> getFotos() {
		return fotos;
	}
}


package br.com.pactosolucoes.socialmailing.modelo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.UteisValidacao;

public class SocialMailVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private Date dataEnvio = Calendario.hoje();
    @ChaveEstrangeira
    private PessoaVO pessoaOrigem = new PessoaVO();
    private String texto = "";
    @NaoControlarLogAlteracao
    private List<SocialMailPartenerVO> destinatarios = new ArrayList<SocialMailPartenerVO>();
    private String grupo = "";
    private SocialMailGrupoVO socialMailGrupo = new SocialMailGrupoVO();
    private boolean enviarSMS = false;
    private Integer pergunta_id = 0;

    public static void validarDados(SocialMailVO mensagem) throws ConsistirException {
        if (mensagem.getDataEnvio() == null) {
            throw new ConsistirException("O campo DATA ENVIO deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(mensagem.getPessoaOrigem().getCodigo())) {
            throw new ConsistirException("O campo PESSOA ORIGEM deve ser informado.");
        }
        if (UteisValidacao.emptyString(mensagem.getTexto())) {
            throw new ConsistirException("O campo TEXTO deve ser informado.");
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public Date getDataEnvio() {
        return dataEnvio;
    }

    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;
    }

    public PessoaVO getPessoaOrigem() {
        return pessoaOrigem;
    }

    public void setPessoaOrigem(PessoaVO pessoaOrigem) {
        this.pessoaOrigem = pessoaOrigem;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public List<SocialMailPartenerVO> getDestinatarios() {
        return destinatarios;
    }

    public void setDestinatarios(List<SocialMailPartenerVO> destinatarios) {
        this.destinatarios = destinatarios;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }

    public String getGrupo() {
        return grupo;
    }

    public String obterGrupo() {
        SocialMailPartenerVO origem = new SocialMailPartenerVO();
        origem.setPessoaDestino(pessoaOrigem);
        List<SocialMailPartenerVO> codigos = new ArrayList<SocialMailPartenerVO>();
        codigos.add(origem);
        codigos.addAll(getDestinatarios());
        Ordenacao.ordenarLista(codigos, "codigoPessoa");
        grupo = "";
        for (SocialMailPartenerVO smp : codigos) {
            if (smp.getCodigoPessoa() > 0) {
                grupo += "," + smp.getCodigoPessoa();
            }

        }
        grupo = grupo.replaceFirst(",", "");
        return grupo;
    }

    public SocialMailGrupoVO getSocialMailGrupo() {
        return socialMailGrupo;
    }

    public void setSocialMailGrupo(SocialMailGrupoVO socialMailGrupo) {
        this.socialMailGrupo = socialMailGrupo;
    }

    public boolean isEnviarSMS() {
        return enviarSMS;
    }

    public void setEnviarSMS(boolean enviarSMS) {
        this.enviarSMS = enviarSMS;
    }

    public Integer getPergunta_id() {
        return pergunta_id;
    }

    public void setPergunta_id(Integer pergunta_id) {
        this.pergunta_id = pergunta_id;
    }
}

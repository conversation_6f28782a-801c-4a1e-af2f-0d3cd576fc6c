/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.socialmailing.modelo;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.PessoaVO;

/**
 *
 * <AUTHOR>
 */
public class SocialMailGrupoParticipanteVO extends SuperVO{

    private boolean ativo = true;
    private SocialMailGrupoVO socialMailGrupo = new SocialMailGrupoVO();
    private PessoaVO participante = new PessoaVO();
    private boolean usuarioPactoBR = false;

    public String getNome(){
        try {
            return participante.getNome();
        } catch (Exception e) {
            return "";
        }
    }
    public int getCodigoPessoa(){
        try {
            return participante.getCodigo();
        } catch (Exception e) {
            return 0;
        }
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public SocialMailGrupoVO getSocialMailGrupo() {
        return socialMailGrupo;
    }

    public void setSocialMailGrupo(SocialMailGrupoVO socialMailGrupo) {
        this.socialMailGrupo = socialMailGrupo;
    }

    public PessoaVO getParticipante() {
        return participante;
    }

    public void setParticipante(PessoaVO participante) {
        this.participante = participante;
    }

    public boolean isUsuarioPactoBR() {
        return usuarioPactoBR;
    }

    public void setUsuarioPactoBR(boolean usuarioPactoBR) {
        this.usuarioPactoBR = usuarioPactoBR;
    }
}

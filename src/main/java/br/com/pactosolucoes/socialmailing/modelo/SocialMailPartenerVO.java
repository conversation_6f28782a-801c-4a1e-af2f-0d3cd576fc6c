package br.com.pactosolucoes.socialmailing.modelo;

import java.util.Date;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;

public class SocialMailPartenerVO extends SuperVO{
	@ChavePrimaria
	private Integer codigo = 0;
	private Date dataLida = Calendario.hoje();
	@ChaveEstrangeira
	private PessoaVO pessoaDestino = new PessoaVO();
	@ChaveEstrangeira
	private SocialMailVO mensagem = new SocialMailVO();

	public static void validarDados(SocialMailPartenerVO mensagem) throws ConsistirException{
		if(mensagem.getDataLida() == null){
			throw new ConsistirException("O campo DATA ENVIO deve ser informado.");
		}
		if(UteisValidacao.emptyNumber(mensagem.getPessoaDestino().getCodigo())){
			throw new ConsistirException("O campo PESSOA DESTINO deve ser informado.");
		}
		
	}
	public Integer getCodigo() {
		return codigo;
	}
	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}
	public Date getDataLida() {
		return dataLida;
	}
	public void setDataLida(Date dataLida) {
		this.dataLida = dataLida;
	}
	public PessoaVO getPessoaDestino() {
		return pessoaDestino;
	}
	public void setPessoaDestino(PessoaVO pessoaDestino) {
		this.pessoaDestino = pessoaDestino;
	}
	
	public SocialMailVO getMensagem() {
		return mensagem;
	}
	public void setMensagem(SocialMailVO mensagem) {
		this.mensagem = mensagem;
	}
	public int getCodigoPessoa(){
		return getPessoaDestino().getCodigo();
	}
	
	public boolean equals(Object o) {
		if (o instanceof SocialMailPartenerVO) {
			SocialMailPartenerVO ct = (SocialMailPartenerVO) o;
			return ct.getPessoaDestino().getCodigo().equals(this.getPessoaDestino().getCodigo());
		} else {
			return false;
		}
	}
}

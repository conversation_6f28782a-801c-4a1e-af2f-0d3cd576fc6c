package br.com.pactosolucoes.socialmailing.modelo;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.arquitetura.SuperTO;

import negocio.comuns.utilitarias.Uteis;

public class MensagemTO extends SuperTO {
    private static final long serialVersionUID = 3652346146860659153L;

    private String mensagem = "";
    private String pessoa = "";
    private Date enviada;
    private Integer codPessoa = 0;
    private Integer codPergunta = 0;
    private boolean separator = false;
    private boolean enviadoSMS = false;
    private String fotoKey;
    private boolean usuarioPactoBR = false;
    private boolean respostaCompreendida = false;
    private Integer socialMail;

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getDataEnviada() {
        return Uteis.getDataAplicandoFormatacao(enviada, "dd MMM, HH:mm");
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getMensagem() {
        if(mensagem == null){
            mensagem = "";
        }

        if (mensagem.contains("@UCP=")) {

            Integer qtdConhecimentos = Uteis.contarSequenciaCaracterString(mensagem , "@UCP=");

            int i = 0;
            while (i < qtdConhecimentos) {
                String[] men = mensagem.split("@UCP=");
                String[] codPerg = men[1].split("@");
                String pergunta = codPerg[0];

                String mensagemSubstituir = "@UCP="+pergunta+"@";
                String mensagemColocar = "<a style=\"font-size: 14px;\" href=\""+getLinkUcp()+pergunta+"\" target=\"ucp\">Clique Aqui</a>";

                mensagem = mensagem.replaceAll(mensagemSubstituir, mensagemColocar);
                i++;
            }
        }

        if(codPergunta != null && codPergunta > 0){
            mensagem = replaceWidth(mensagem);
            return mensagem + "<br/><br/>" +
                    "<a style=\"font-size: 15px;\" href=\""+getLinkPergunta()+"\" target=\"ucp\">Ver mais</a>";
        }else{
            return mensagem.replaceAll("@RFIM", "").replaceAll("@rfim", "");
        }
    }

    private String replaceWidth(String mensagem) {
        Pattern imagem = Pattern.compile("\\s*[w][i][d][t][h]\\s*=\\s*[\\\"']{0,1}([^\\\"'\\s]*)");
        Matcher matcher = imagem.matcher(mensagem);

        while (matcher.find()) {
            String img = matcher.group();
            mensagem = mensagem.replaceFirst(img, " width=\"100%\"");
        }
        return mensagem;
    }

    public String getLinkPergunta(){
        try {
            String request = (String) JSFUtilities.getManagedBean("SuperControle.contextPath");
            String urlMontada = request + "/redir?up&codPerguntaUCP=" + getCodPergunta();
            return urlMontada;
        }catch (Exception ex){
            return "";
        }
    }

    public String getLinkUcp(){
        try {
            String request = (String) JSFUtilities.getManagedBean("SuperControle.contextPath");
            return request + "/redir?up&codPerguntaUCP=";
        }catch (Exception ex){
            return "";
        }
    }


    public void setPessoa(String pessoa) {
        this.pessoa = pessoa;
    }

    public String getPessoa() {
        return pessoa;
    }

    public void setEnviada(Date enviada) {
        this.enviada = enviada;
    }

    public Date getEnviada() {
        return enviada;
    }

    public void setCodPessoa(Integer codPessoa) {
        this.codPessoa = codPessoa;
    }

    public Integer getCodPessoa() {
        return codPessoa;
    }

    public void setSeparator(boolean separator) {
        this.separator = separator;
    }

    public boolean getSeparator() {
        return separator;
    }

    public boolean isEnviadoSMS() {
        return enviadoSMS;
    }

    public void setEnviadoSMS(boolean enviadoSMS) {
        this.enviadoSMS = enviadoSMS;
    }

    public Integer getCodPergunta() {
        return codPergunta;
    }

    public void setCodPergunta(Integer codPergunta) {
        this.codPergunta = codPergunta;
    }

    public boolean isUsuarioPactoBR() {
        return usuarioPactoBR;
    }

    public void setUsuarioPactoBR(boolean usuarioPactoBR) {
        this.usuarioPactoBR = usuarioPactoBR;
    }

    public boolean isRespostaCompreendida() {
        return respostaCompreendida;
    }

    public void setRespostaCompreendida(boolean respostaCompreendida) {
        this.respostaCompreendida = respostaCompreendida;
    }

    public Integer getSocialMail() {
        if (socialMail == null) {
            socialMail = 0;
        }
        return socialMail;
    }

    public void setSocialMail(Integer socialMail) {
        this.socialMail = socialMail;
    }
}

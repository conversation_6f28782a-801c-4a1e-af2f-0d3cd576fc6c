package br.com.pactosolucoes.socialmailing.modelo;

/**
 * Created by luiz on 08/01/2018.
 */
public enum TipoSocialMailEnum {

    CONVERSAS    (0, "Conversas", "Grupo de Conversa"),
    SOLICITACAO  (1, "Solicitações", "Solicitações"),
    CONTATO_APP  (2, "Contato APP", "Conversas");

    private Integer codigo;
    private String descricao;
    private String titulo;

    TipoSocialMailEnum(final Integer codigo, final String descricao, final String titulo) {
        this.setCodigo(codigo);
        this.setDescricao(descricao);
        this.setTitulo(titulo);
    }

    public static TipoSocialMailEnum getConsultarPorCodigo(Integer codigo) {
        for (TipoSocialMailEnum tipoCobranca : values()) {
            if (tipoCobranca.getCodigo().equals(codigo)) {
                return tipoCobranca;
            }
        }
        return CONVERSAS;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }
}
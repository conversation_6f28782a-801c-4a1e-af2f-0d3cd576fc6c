/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.socialmailing.modelo;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class SocialMailGrupoVO extends SuperVO {

    private PessoaVO dono = new PessoaVO();
    private String nome = "";
    private boolean coletivo = false;
    private boolean ativo = true;
    private Date dataCriacao;
    private List<SocialMailGrupoParticipanteVO> participantes = new ArrayList<SocialMailGrupoParticipanteVO>();
    private String selected = "notselected";
    private Integer nrMensagens = 0;
    private transient byte foto[];
    private Integer nrMensagensNaoLidas = 0;
    private String nomeResumido = "";
    private String nomeTodos = "";
    private List<MensagemTO> mensagens = new ArrayList<MensagemTO>();
    private Map<Integer, byte[]> fotos = new HashMap<Integer, byte[]>();
    private Date dataUltimaMsg;
    private boolean souDono = false;
    public String fotoKey;
    private Integer solicitacao_id;
    private String ultimaMensagem;
    private TipoSocialMailEnum tipoSocialMail;

    public void montarNomeConversa(int pessoaDesconsiderar) {
        try {
            Ordenacao.ordenarLista(participantes, "nome");
            nomeResumido = "";
            int size = participantes.size();
            int cont = 0;
            souDono = getDono() != null && getDono().getCodigo() != null && getDono().getCodigo().intValue() == pessoaDesconsiderar;
            for (int i = 0; i < size; i++) {
                nomeTodos += Uteis.getPrimeiroNome(participantes.get(i).getParticipante().getNome()) + " | ";
                if (cont == 2) {
                    nomeResumido += "...";
                    cont++;
                }
                if (participantes.get(i).getParticipante().getCodigo() != pessoaDesconsiderar
                        && participantes.get(i).isAtivo()
                        && cont < 2) {
                    nomeResumido += Uteis.getPrimeiroNome(participantes.get(i).getParticipante().getNome()) + "<br/>";
                    if (!isColetivo()) {
                        foto = participantes.get(i).getParticipante().getFoto();
                        fotoKey = participantes.get(i).getParticipante().getUrlFoto();
                    }
                    cont++;
                }

            }
            if (!UteisValidacao.emptyString(nome)) {
                nomeResumido = nome;
            }
        } catch (Exception e) {
            nomeResumido = "GRUPO";
        }
    }

    public Integer getNrMensagens() {
        return nrMensagens;
    }

    public void setNrMensagens(Integer nrMensagens) {
        this.nrMensagens = nrMensagens;
    }

    public String getSelected() {
        return UteisValidacao.emptyString(selected) ? "notselected" : selected;
    }

    public void setSelected(String selected) {
        this.selected = selected;
    }

    public PessoaVO getDono() {
        return dono;
    }

    public void setDono(PessoaVO dono) {
        this.dono = dono;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public List<SocialMailGrupoParticipanteVO> getParticipantes() {
        return participantes;
    }

    public void setParticipantes(List<SocialMailGrupoParticipanteVO> participantes) {
        this.participantes = participantes;
    }

    public String getNome() {
        return nome;
    }

    public String getNomeApresentarSolicitacao() {
        if (!UteisValidacao.emptyNumber(getSolicitacao_id())) {
            return nome.replaceAll(getSolicitacao_id().toString(), "").toUpperCase();
        } else {
            return nome;
        }
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isColetivo() {
        return coletivo;
    }

    public void setColetivo(boolean coletivo) {
        this.coletivo = coletivo;
    }

    public byte[] getFoto() {
        return foto;
    }

    public void setFoto(byte[] foto) {
        this.foto = foto;
    }

    public boolean getTemFoto() {
        return foto != null && foto.length > 0;
    }

    public boolean getTemFotoNuvem() {
        return fotoKey != null && !fotoKey.isEmpty();
    }

    public Integer getNrMensagensNaoLidas() {
        return nrMensagensNaoLidas;
    }

    public void setNrMensagensNaoLidas(Integer nrMensagensNaoLidas) {
        this.nrMensagensNaoLidas = nrMensagensNaoLidas;
    }

    public String getNomeResumido() {
        return nomeResumido;
    }

    public void setNomeResumido(String nomeResumido) {
        this.nomeResumido = nomeResumido;
    }

    public String getNomeTodos() {
        return nomeTodos;
    }

    public void setNomeTodos(String nomeTodos) {
        this.nomeTodos = nomeTodos;
    }

    public List<MensagemTO> getMensagens() {
        return mensagens;
    }

    public void setMensagens(List<MensagemTO> mensagens) {
        this.mensagens = mensagens;
    }

    public boolean getDisabledMais() {
        return getMensagens().size() >= nrMensagens;
    }

    public static byte[] obterFoto(Integer grupo, List<SocialMailGrupoVO> conversas) {
        for (SocialMailGrupoVO conversa : conversas) {
            if (conversa.getCodigo().equals(grupo)) {
                return conversa.getFoto();
            }
        }
        return null;
    }

    public String getUrlFoto(){
        return fotoKey;
    }

    public Map<Integer, byte[]> getFotos() {
        return fotos;
    }

    public void setFotos(Map<Integer, byte[]> fotos) {
        this.fotos = fotos;
    }

    public Date getDataUltimaMsg() {
        if (dataUltimaMsg == null) {
            dataUltimaMsg = Calendario.hoje();
        }
        return dataUltimaMsg;
    }

    public void setDataUltimaMsg(Date dataUltimaMsg) {
        this.dataUltimaMsg = dataUltimaMsg;
    }

    @Override
    public boolean equals(Object o) {
        if (o instanceof SocialMailGrupoVO) {
            SocialMailGrupoVO ct = (SocialMailGrupoVO) o;
            return ct.getCodigo().equals(this.getCodigo());
        } else {
            return false;
        }
    }

    @Override
    public int hashCode() {
        return this.getCodigo().hashCode();
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public boolean isSouDono() {
        return souDono;
    }

    public void setSouDono(boolean souDono) {
        this.souDono = souDono;
    }

    public Integer getSolicitacao_id() {
        if (solicitacao_id == null) {
            solicitacao_id = 0;
        }
        return solicitacao_id;
    }

    public void setSolicitacao_id(Integer solicitacao_id) {
        this.solicitacao_id = solicitacao_id;
    }

    public String getUltimaMensagem() {
        if (ultimaMensagem == null) {
            ultimaMensagem = "";
        }
        return ultimaMensagem;
    }

    public void setUltimaMensagem(String ultimaMensagem) {
        this.ultimaMensagem = ultimaMensagem;
    }

    public TipoSocialMailEnum getTipoSocialMail() {
        if (tipoSocialMail == null) {
            tipoSocialMail = TipoSocialMailEnum.CONVERSAS;
        }
        return tipoSocialMail;
    }

    public void setTipoSocialMail(TipoSocialMailEnum tipoSocialMail) {
        this.tipoSocialMail = tipoSocialMail;
    }
}

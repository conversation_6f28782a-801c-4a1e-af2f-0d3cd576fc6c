package br.com.pactosolucoes.socialmailing.dao;

import br.com.pactosolucoes.ce.comuns.ex.NegocioException;
import br.com.pactosolucoes.socialmailing.interfaces.SocialMailingInterfaceFacade;
import br.com.pactosolucoes.socialmailing.modelo.ConversaTO;
import br.com.pactosolucoes.socialmailing.modelo.MensagemTO;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoParticipanteVO;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoVO;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailPartenerVO;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailVO;
import br.com.pactosolucoes.socialmailing.modelo.TipoSocialMailEnum;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Arrays.asList;

public class SocialMail extends SuperEntidade implements SocialMailingInterfaceFacade {

	public SocialMail(Connection conexao) throws Exception {
		super(conexao);
	}
	public SocialMail() throws Exception {
		super();
	}

	public void incluir(SocialMailVO mensagem) throws Exception {
		verificarTagTexto(mensagem);
		String sql = "INSERT INTO socialmail(dataenvio, pessoaorigem, texto, grupo, socialmailgrupo, enviarsms, pergunta_id) " +
				"VALUES ( ?, ?, ?, ?, ?, ?, ?);";
		try (PreparedStatement stm = con.prepareStatement(sql)) {
			int i = 1;
			stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(mensagem.getDataEnvio()));
			stm.setInt(i++, mensagem.getPessoaOrigem().getCodigo());
			stm.setString(i++, mensagem.getTexto());
			stm.setString(i++, mensagem.obterGrupo());
			stm.setInt(i++, mensagem.getSocialMailGrupo().getCodigo());
			stm.setBoolean(i++, mensagem.isEnviarSMS());
			stm.setInt(i++, mensagem.getPergunta_id());
			stm.execute();
		}
		mensagem.setCodigo(obterValorChavePrimariaCodigo());
		incluirPartner(mensagem);
	}

	public SocialMailGrupoVO descobrirCodigoGrupo(Integer pessoaPactoBR, Integer pessoaUsuario, Integer solicitacao_id, String titulo) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT \n");
		sql.append("sg.codigo \n");
		sql.append("FROM socialmailgrupo sg \n");
		sql.append("INNER JOIN socialmailgrupoparticipante sgp on sgp.socialmailgrupo = sg.codigo \n");
		sql.append("WHERE 1 = 1 \n");
		if (solicitacao_id != null && solicitacao_id > 0) {
			sql.append("AND solicitacao_id = ").append(solicitacao_id).append(" \n");
		}
		sql.append("AND sgp.participante = ").append(pessoaUsuario).append(" \n");
		SocialMailGrupoVO gr = null;
		SocialMailGrupo smgDao = new SocialMailGrupo(con);
		try (ResultSet rs = criarConsulta(sql.toString(), con)) {
			if (rs.next()) {
				int socialmailgrupo = rs.getInt("codigo");
				try (ResultSet rsGrupo = SuperFacadeJDBC.criarConsulta("SELECT * FROM socialmailgrupo where codigo = " + socialmailgrupo, con)) {
					if (rsGrupo.next()) {
						gr = smgDao.montarDados(rsGrupo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
					}
				}
			}
		}

		if(gr == null) {
			gr = new SocialMailGrupoVO();
			gr.setTipoSocialMail(TipoSocialMailEnum.SOLICITACAO);
			gr.setDataCriacao(Calendario.hoje());
			gr.setSolicitacao_id(solicitacao_id);
			gr.setColetivo(true);

			String tituloGravar = "";
			if (solicitacao_id != null && solicitacao_id != 0) {
			   tituloGravar = solicitacao_id.toString() + " ";
			}
			if (!UteisValidacao.emptyString(titulo)) {
				tituloGravar += titulo;
			}
            gr.setNome(tituloGravar.trim());

			PessoaVO dono = new PessoaVO();
			dono.setCodigo(pessoaPactoBR);
			gr.setDono(dono);

			SocialMailGrupoParticipanteVO part1 = new SocialMailGrupoParticipanteVO();
			part1.setParticipante(dono);
			gr.setParticipantes(new ArrayList<SocialMailGrupoParticipanteVO>());
			gr.getParticipantes().add(part1);

			SocialMailGrupoParticipanteVO part2 = new SocialMailGrupoParticipanteVO();
			PessoaVO pesusuario = new PessoaVO();
			pesusuario.setCodigo(pessoaUsuario);
			part2.setParticipante(pesusuario);
			gr.getParticipantes().add(part2);

			smgDao.incluir(gr);
		}
		return gr;
	}

	public boolean usuarioPacto(Integer codigoPessoa) throws Exception {
		try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT u.username FROM colaborador col\n" +
				"INNER JOIN usuario u ON u.colaborador = col.codigo\n" +
				"WHERE col.pessoa = " + codigoPessoa, con)) {
			if (rs.next()) {
				return rs.getString("username").equals("PACTOBR");
			}
		}
		return false;
	}

	public Integer inserirAvisoAoUsuario(Integer codigoUsuario,String username, String texto, String titulo,
										 Integer codigoExterno, String origem) throws Exception {
		String sistema = "";
		String msg = "";

		String[] splitOrigem = origem.split("_");
		String[] splitConteudo = texto.split("#MSG#");

		Integer idSolicitacao = 0;
		Integer idPergunta = 0;

		if(splitOrigem.length > 1){
			idSolicitacao = Integer.valueOf(splitOrigem[0]);
			sistema = splitOrigem[1];
		}else{
			sistema = splitOrigem[0];
		}

		if(splitConteudo.length > 1){
			idPergunta = Integer.valueOf(splitConteudo[0].trim());
			msg = splitConteudo[1];
		}else{
			msg = splitConteudo[0];
		}

		String id = "#"+sistema+":"+codigoExterno+".";
		if(codigoExterno != null && codigoExterno > 0){
			try (ResultSet rsCodExt = SuperFacadeJDBC.criarConsulta("SELECT * FROM socialmail where texto like '%" +
					id + "%'", con)) {
				if (rsCodExt.next()) {
					Integer idSocialMailing = rsCodExt.getInt("codigo");
					throw new Exception("Este comentário já foi gravado!" + idSocialMailing);
				}
			}
		}

		msg += "<div style=\"display:none;\">"+id+"</div>";
		try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT col.pessoa FROM colaborador col\n" +
				"INNER JOIN usuario u ON u.colaborador = col.codigo\n" +
				"WHERE u.username ilike 'PACTOBR'", con)) {
			if (rs.next()) {
				Integer pessoaPacto = rs.getInt("pessoa");
				try (ResultSet rsCodUsuario = SuperFacadeJDBC.criarConsulta("SELECT col.pessoa FROM colaborador col\n" +
						"INNER JOIN usuario u ON u.colaborador = col.codigo\n" +
						(codigoUsuario == null ? (" WHERE u.username = '" + username + "'") :
								" WHERE u.codigo = " + codigoUsuario), con)) {
					if (rsCodUsuario.next()) {
						Integer pessoaDestino = rsCodUsuario.getInt("pessoa");
						SocialMailVO mensagem = new SocialMailVO();
						SocialMailGrupoVO grupoVO = descobrirCodigoGrupo(pessoaPacto, pessoaDestino, idSolicitacao, titulo);
						mensagem.setTexto(msg);
						//setar pessoa origem e data de envio
						PessoaVO pessoaPactoVO = new PessoaVO();
						pessoaPactoVO.setCodigo(pessoaPacto);

						mensagem.setPessoaOrigem(pessoaPactoVO);
						mensagem.setDataEnvio(Calendario.hoje());
						mensagem.setSocialMailGrupo(grupoVO);
						mensagem.setPergunta_id(idPergunta);
						incluir(mensagem);

						if (msg.toUpperCase().startsWith("@RFIM")) {
							SuperFacadeJDBC.executarConsultaUpdate("UPDATE socialmailgrupo SET ativo = false WHERE codigo = " + mensagem.getSocialMailGrupo().getCodigo(), con);
						}
						return mensagem.getCodigo();
					} else {
						throw new Exception("Não foi possível encontrar o usuário de código " + codigoUsuario);
					}
				}
			} else {
				throw new Exception("Não foi possível encontrar o usuário PACTOBR");
			}
		}
	}

	private void verificarTagTexto(SocialMailVO mensagem){
		String[] split = mensagem.getTexto().split("@");
		for(String str : split){
				String matricula = "";
				for (int i = 0; i < str.length(); i++) {
		            if (Character.isDigit(str.charAt(i))) {
		            	matricula = matricula + str.substring(i, i + 1);
		            }else
		            	break;
		        }
				if(!matricula.isEmpty())
					mensagem.setTexto(mensagem.getTexto().replaceAll("@"+matricula, "<a href=\"#\" onclick=\"mostrarCliente('"+matricula+"');\">@"+matricula+"</a>")) ;
			}
	}

	@Override
	public void consultarMensagensPorGrupo(SocialMailGrupoVO grupo, Integer pessoaLogada) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT \n");
		sql.append("sm.texto, \n");
		sql.append("sm.pergunta_id, \n");
		sql.append("p.codigo as codpessoa, \n");
		sql.append("p.nome, \n");
		sql.append("p.fotokey, \n");
		sql.append("sm.dataenvio, \n");
		sql.append("sm.enviarSMS, \n");
		sql.append("sm.codigo, \n");
		sql.append("(select distinct respostaCompreendida from socialmailpartener  where socialmail = sm.codigo and pessoadestino = ").append(pessoaLogada).append(") as respostaCompreendida \n");
		sql.append("FROM socialmail sm \n");
		sql.append("INNER JOIN pessoa p ON sm.pessoaorigem = p.codigo \n");
		sql.append("WHERE socialmailgrupo = ").append(grupo.getCodigo()).append(" \n");
		sql.append("ORDER BY dataenvio DESC limit 10 offset " + grupo.getMensagens().size());

		try (ResultSet dados = criarConsulta(sql.toString(), con)) {
			while (dados.next()) {
				MensagemTO mensagem = new MensagemTO();
				mensagem.setPessoa(Uteis.getPrimeiroNome(dados.getString("nome")));
				mensagem.setFotoKey(Uteis.getPaintFotoDaNuvem(dados.getString("fotokey")));
				mensagem.setMensagem(dados.getString("texto"));
				mensagem.setEnviada(dados.getTimestamp("dataenvio"));
				mensagem.setCodPessoa(dados.getInt("codpessoa"));
				mensagem.setCodPergunta(dados.getInt("pergunta_id"));
				mensagem.setEnviadoSMS(dados.getBoolean("enviarSMS"));
				mensagem.setSocialMail(dados.getInt("codigo"));
				mensagem.setRespostaCompreendida(dados.getBoolean("respostaCompreendida"));
				grupo.getMensagens().add(mensagem);
			}
		}
		Ordenacao.ordenarLista(grupo.getMensagens(), "enviada");
	}


	public void incluirPartner(SocialMailVO mensagem) throws Exception{
		String insert = "insert into socialmailpartener (pessoadestino, socialmail) values (%s, %s);";
		String sql = "";
                for(SocialMailGrupoParticipanteVO smgp : mensagem.getSocialMailGrupo().getParticipantes()){
                    if(!mensagem.getPessoaOrigem().getCodigo().equals(smgp.getParticipante().getCodigo())){
                        sql +=  String.format(insert, new Object[]{smgp.getParticipante().getCodigo(), mensagem.getCodigo() });
                    }

                }
		executarConsulta(sql, con);
	}

	public void incluirPartnerChatExistente(SocialMailVO mensagem, ConversaTO conversa) throws Exception{
        String grupoFinal = mensagem.getGrupo();
        SocialMailVO novoMembroGrupo = (SocialMailVO)mensagem.getClone(true);
        String update = "UPDATE socialmail SET grupo = '%s' WHERE grupo = '%s'";
        String sql =  String.format(update, mensagem.getGrupo(), conversa.getGrupo());
		executarConsulta(sql, con);
        String[] a = mensagem.getGrupo().split(",");
        String[] b = conversa.getGrupo().split(",");
        for (String ab: a){
            boolean existe = false;
            for (String ba: b){
                if (ba.equals(ab)){
                    existe = true;
                }
            }
            if (!existe) {
                novoMembroGrupo.setGrupo(ab);
                break;
            }
        }
        String sqlPartner = "";
        if (novoMembroGrupo.getCodigo() != 0) {
            String insert = "insert into socialmailpartener (pessoadestino, socialmail) values (%s, %s);";
            sqlPartner = String.format(insert, novoMembroGrupo.getGrupo(), novoMembroGrupo.getCodigo());
            executarConsulta(sqlPartner, con);
        }else{
            String select = "SELECT codigo FROM socialmail where grupo = '%s'";
            sqlPartner = String.format(select, grupoFinal);
			try (ResultSet dados = criarConsulta(sqlPartner, con)) {
				while (dados.next()) {
					String insert = "insert into socialmailpartener (pessoadestino, socialmail) values (%s, %s)";
					sqlPartner = String.format(insert, novoMembroGrupo.getGrupo(), dados.getString("codigo"));
					executarConsulta(sqlPartner, con);
				}
			}
		}

    }

    public void excluirPartnerChatExistente(SocialMailVO mensagem, String usuario) throws Exception {
        String[] a = mensagem.getGrupo().split(",");
        String novoGrupo = "";
        for (String ab : a) {
            if (!ab.equals(usuario)) {
                novoGrupo += ","+ab;
            }
        }
        novoGrupo = novoGrupo.replaceFirst(",", "");
        String sqlPartner = "";
        String delete = "DELETE FROM socialmailpartener where pessoadestino = %s and (socialmail IN (SELECT codigo FROM socialmail where grupo = '%s'))";
        sqlPartner = String.format(delete, usuario, mensagem.getGrupo());
        executarConsulta(sqlPartner, con);

        String update = "UPDATE socialmail SET grupo = '%s' WHERE grupo = '%s'";
        String sql = String.format(update, novoGrupo, mensagem.getGrupo());
        executarConsulta(sql, con);
        if (mensagem.getGrupo().indexOf(","+usuario) > -1){
            mensagem.setGrupo(mensagem.getGrupo().replace(","+usuario, ""));
        }else{
            mensagem.setGrupo(mensagem.getGrupo().replace(usuario+",", ""));
        }
    }


    public List<ConversaTO> getConversas(Integer pessoaLogada) throws Exception{

		List<ConversaTO> conversas = new ArrayList<ConversaTO>();
		StringBuilder sql = new StringBuilder();

		sql.append(" SELECT sm.grupo, p.nome, max(sm.dataenvio) as dataultimamsg, p.codigo as pessoa, p.foto,\n");
		sql.append(" (SELECT COUNT(smpc.codigo) FROM socialmailpartener smpc, socialmail smc WHERE datalida IS NULL AND pessoadestino = ").append(pessoaLogada);
		sql.append(" AND smc.codigo = smpc.socialmail AND smc.grupo = sm.grupo) as naolidas,  (SELECT COUNT(codigo) FROM socialmail WHERE grupo LIKE sm.grupo) as nrmsgs ");
		sql.append(" FROM socialmail sm \n");
		sql.append(" INNER JOIN socialmailpartener smp ON smp.socialmail = sm.codigo \n");
		sql.append(" INNER JOIN pessoa p ON p.codigo = sm.pessoaorigem OR p.codigo = smp.pessoadestino  \n");
		sql.append(" WHERE sm.pessoaorigem = ").append(pessoaLogada).append(" OR smp.pessoadestino = ").append(pessoaLogada);
		sql.append(" OR sm.grupo LIKE '%,").append(pessoaLogada).append("' OR sm.grupo LIKE '").append(pessoaLogada);
		sql.append(",%' OR sm.grupo LIKE '%,").append(pessoaLogada).append(",%'");
		sql.append(" GROUP BY grupo, nome, p.codigo, p.foto order by dataultimamsg DESC ");

		try (ResultSet dados = criarConsulta(sql.toString(), con)) {
			int cont = 0;
			while (dados.next()) {
				if (dados.getInt("pessoa") != pessoaLogada) {
					ConversaTO conversa = new ConversaTO();
					conversa.setGrupo(dados.getString("grupo"));
					if (conversas.contains(conversa)) {
						conversa = conversas.get(conversas.indexOf(conversa));
						conversa.setNomeConversaResumida(cont < 2 ? conversa.getNomeConversaResumida() + "<br/> " + Uteis.getPrimeiroNome(dados.getString("nome"))
								: cont == 2 ? conversa.getNomeConversaResumida() + "<br/>..." : conversa.getNomeConversaResumida() + "");
						conversa.setFotoIndividual(false);
						try {
							conversa.getFotos().put(dados.getInt("pessoa"), dados.getBytes("foto"));
						} catch (Exception e) {
						}
						conversa.setNomeConversa(conversa.getNomeConversa() + ", " + Uteis.getPrimeiroNome(dados.getString("nome")));
						cont++;
					} else {
						cont = 1;
						conversas.add(conversa);
						conversa.setNomeConversa(Uteis.getPrimeiroNome(dados.getString("nome")));
						conversa.setNomeConversaResumida(Uteis.getPrimeiroNome(dados.getString("nome")));
						try {
							conversa.setFoto(dados.getBytes("foto"));
							conversa.getFotos().put(dados.getInt("pessoa"), dados.getBytes("foto"));
						} catch (Exception e) {
						}
						conversa.setNrMensagensNaoLidas(dados.getInt("naolidas"));
						conversa.setNrMensagens(dados.getInt("nrmsgs"));
					}
					conversa.setDataUltimaMSG(dados.getTimestamp("dataultimamsg"));
					SocialMailPartenerVO socialMP = new SocialMailPartenerVO();
					socialMP.getPessoaDestino().setCodigo(dados.getInt("pessoa"));
					socialMP.getPessoaDestino().setNome(dados.getString("nome"));
					conversa.getParteners().add(socialMP);
				}
			}
		}

		return conversas;
	}

	public void marcarConversasLidas(Date data, Integer grupo, Integer pessoa) throws Exception{
		String sql = "UPDATE socialmailpartener SET datalida = ? WHERE pessoadestino = ? AND socialmail IN "+
					 "(select codigo from socialmail where socialmailgrupo = ?)";
		try (PreparedStatement stm = con.prepareStatement(sql)) {
			stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(data));
			stm.setInt(2, pessoa);
			stm.setInt(3, grupo);
			stm.execute();
		}

	}

	public Integer conversasNaoLidas(Integer pessoa, Integer grupo) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT COUNT(par.codigo) as naolidas from socialmailpartener par \n");
		sql.append("INNER JOIN socialmail sm on par.socialmail = sm.codigo \n");
		sql.append("INNER JOIN socialmailgrupo gru on sm.socialmailgrupo = gru.codigo \n");
		sql.append("WHERE 1 = 1 \n");
		if (UteisValidacao.emptyNumber(grupo)) {
			sql.append("AND (gru.solicitacao_id is null or gru.solicitacao_id = 0) \n");
			sql.append("AND gru.ativo = 't' \n");
		} else {
			sql.append("AND gru.codigo = ").append(grupo).append(" \n");
		}
		sql.append("AND par.pessoadestino =  " + pessoa + " \n");
		sql.append("AND par.datalida is null \n");
		try (ResultSet dados = criarConsulta(sql.toString(), con)) {
			dados.next();
			return dados.getInt("naolidas");
		}

	}

	public void montarConversa(final String chave, List<SocialMailPartenerVO> destinatarios, ConversaTO conversa) throws Exception{
		if(destinatarios.size() == 1){
			SocialMailPartenerVO partner = destinatarios.get(0);
			conversa.setNomeConversaResumida(Uteis.getPrimeiroNome(partner.getPessoaDestino().getNome()));
			conversa.setNomeConversa(Uteis.getPrimeiroNome(partner.getPessoaDestino().getNome()));
			conversa.setFotoIndividual(true);
			conversa.setFoto(getFacade().getPessoa().obterFoto(chave, partner.getPessoaDestino().getCodigo()));

		}else{
			int cont = 0;
			boolean ja = false;
			for(SocialMailPartenerVO partner : destinatarios){
				if(ja){
					conversa.setNomeConversaResumida(cont < 2 ? conversa.getNomeConversaResumida() + ",<br/> "
							+Uteis.getPrimeiroNome(partner.getPessoaDestino().getNome())
							: cont == 2 ? conversa.getNomeConversaResumida()
							                		 + "<br/>..." : conversa.getNomeConversaResumida() + "" );

					conversa.setNomeConversa(conversa.getNomeConversa() + ", "+Uteis.getPrimeiroNome(partner.getPessoaDestino().getNome()));
				}else{
					conversa.setNomeConversaResumida(Uteis.getPrimeiroNome(partner.getPessoaDestino().getNome()));
					conversa.setNomeConversa(Uteis.getPrimeiroNome(partner.getPessoaDestino().getNome()));
					ja = true;
				}
				conversa.setFotoIndividual(false);
				cont++;
			}
		}
		conversa.setParteners(destinatarios);
	}

	public Integer solicitacaoUltimaConversa(Integer grupo) throws Exception {
		try (ResultSet rs = criarConsulta("select solicitacao_id, texto from socialmail  \n" +
				" WHERE socialmailgrupo = " + grupo +
				"  AND solicitacao_id > 0 order by dataenvio desc LIMIT 1", con)) {
			if (rs.next()) {
				String texto = rs.getString("texto");
				if (texto.contains("@rfim") || texto.contains("@RFIM")) {
					return null;
				} else {
					return rs.getInt("solicitacao_id");
				}
			} else {
				return null;
			}
		}
	}

	public String enviarMensagemService(String nomeUsuario, Integer codigoSolicitacao, String texto) throws IOException {
		texto = texto + "<br/>Usuário: "+nomeUsuario;
		String urlDash = "http://intranet.pactosolucoes.com.br:81/dash2/prest/salpa/incluirRespostaCliente";
//		String urlDash = "http://192.168.156.144:8080/dashboard2/prest/salpa/incluirRespostaCliente";
		Map<String, String> params = new HashMap<String, String>();
		params.put("solicitacao_id", codigoSolicitacao.toString());
		params.put("mensagem", texto);
		return ExecuteRequestHttpService.executeRequest(urlDash, params);
	}

	public Integer respostasNaoLidasService(Integer pessoa) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("select \n");
		sql.append("count(smp.codigo) as naolidas \n");
		sql.append("from socialmailpartener smp \n");
		sql.append("inner join socialmail sm on smp.socialmail = sm.codigo \n");
		sql.append("inner join socialmailgrupo sg on sg.codigo = sm.socialmailgrupo \n");
		sql.append("where 1 = 1 \n");
		sql.append("and (sg.solicitacao_id is not null and sg.solicitacao_id <> 0) \n");
		sql.append("AND smp.pessoadestino = ").append(pessoa).append(" \n");
		sql.append("AND smp.datalida is null \n");
		sql.append("AND sg.ativo = true \n");
		try (ResultSet dados = criarConsulta(sql.toString(), con)) {
			dados.next();
			return dados.getInt("naolidas");
		}
	}

	public List<SocialMailGrupoVO> consultarGruposConversaPactoBRSolicitacoesEmAberto(Integer pessoaUsuarioLogado) throws Exception {
		List<SocialMailGrupoVO> retorno = new ArrayList<SocialMailGrupoVO>();
		StringBuilder sql = new StringBuilder();
		sql.append("select \n");
		sql.append("distinct(sg.codigo) \n");
		sql.append("from socialmailgrupo sg \n");
		sql.append("inner join socialmailgrupoparticipante sgp on sgp.socialmailgrupo = sg.codigo \n");
		sql.append("where 1 = 1 \n");
		sql.append("and (sg.solicitacao_id is not null and sg.solicitacao_id <> 0) \n");
		sql.append("AND sg.ativo = true \n");
		sql.append("AND sgp.participante = ").append(pessoaUsuarioLogado);

		try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
			SocialMailGrupo smgDao = new SocialMailGrupo(con);
			while (rs.next()) {
				int socialmailgrupo = rs.getInt("codigo");
				ResultSet rsGrupo = SuperFacadeJDBC.criarConsulta("SELECT * FROM socialmailgrupo where codigo = " + socialmailgrupo, con);
				if (rsGrupo.next()) {
					SocialMailGrupoVO grupo = smgDao.montarDados(rsGrupo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
	//				grupo.setUltimaMensagem(consultarUltimaMensagemGrupo(grupo.getCodigo()));
					retorno.add(grupo);
				}
			}
		}
		return retorno;
	}

	public List<SocialMailGrupoVO> consultarSolicitacoesRespostasNaoCompreeendidas(Integer pessoaUsuarioLogado) throws Exception {
		List<SocialMailGrupoVO> retorno = new ArrayList<SocialMailGrupoVO>();
		StringBuilder sql = new StringBuilder();

		sql.append("select \n");
		sql.append("distinct(sg.codigo) \n");
		sql.append("from socialmailpartener sp \n");
		sql.append("inner join socialmail sm on sm.codigo = sp.socialmail \n");
		sql.append("inner join socialmailgrupo sg on sg.codigo = sm.socialmailgrupo \n");
		sql.append("where 1 = 1 \n");
		sql.append("and (sg.solicitacao_id is not null and sg.solicitacao_id <> 0) \n");
		sql.append("and sp.respostacompreendida = false \n");
		sql.append("and sp.pessoadestino = ").append(pessoaUsuarioLogado);

		try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
			SocialMailGrupo smgDao = new SocialMailGrupo(con);
			while (rs.next()) {
				int socialmailgrupo = rs.getInt("codigo");
				ResultSet rsGrupo = SuperFacadeJDBC.criarConsulta("SELECT * FROM socialmailgrupo where codigo = " + socialmailgrupo, con);
				if (rsGrupo.next()) {
					SocialMailGrupoVO grupo = smgDao.montarDados(rsGrupo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
	//				grupo.setUltimaMensagem(consultarUltimaMensagemGrupo(grupo.getCodigo()));
					retorno.add(grupo);
				}
			}
		}
		return retorno;
	}

	private String consultarUltimaMensagemGrupo(Integer codGrupo) throws Exception {
		String sql = "select texto from socialmail where socialmailgrupo  = "+codGrupo+" order by dataenvio desc limit 1";
		try (PreparedStatement pst = con.prepareStatement(sql)) {
			try (ResultSet dadosSQL = pst.executeQuery()) {
				if (dadosSQL.next()) {
					return dadosSQL.getString("texto");
				}
			}
		}
		return "";
	}

	public List<SocialMailGrupoVO> consultarGruposConversaPactoBR(Integer pessoaUsuarioLogado, boolean buscarTodas) throws Exception {
		List<SocialMailGrupoVO> retorno = new ArrayList<SocialMailGrupoVO>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT \n");
		sql.append("distinct(sg.codigo), \n");
		sql.append("sg.solicitacao_id, \n");
		sql.append("sg.ativo \n");
		sql.append("FROM socialmailgrupo sg \n");
		sql.append("INNER JOIN socialmailgrupoparticipante sgp on sgp.socialmailgrupo = sg.codigo \n");
		sql.append("WHERE 1 = 1 \n");
		sql.append("AND (sg.solicitacao_id is not null and sg.solicitacao_id <> 0) \n");
		if (!buscarTodas) {
		sql.append("AND sgp.participante = ").append(pessoaUsuarioLogado).append(" \n");
		}
		sql.append("ORDER BY sg.ativo desc,sg.solicitacao_id desc \n");

		try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
			SocialMailGrupo smgDao = new SocialMailGrupo(con);
			while (rs.next()) {
				int socialmailgrupo = rs.getInt("codigo");

				try (ResultSet rsGrupo = SuperFacadeJDBC.criarConsulta("SELECT * FROM socialmailgrupo where codigo = " + socialmailgrupo, con)) {
					if (rsGrupo.next()) {
						retorno.add(smgDao.montarDados(rsGrupo, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
					}
				}
			}
		}

		return retorno;
	}

	public SocialMailVO ultimaMensagemNaoLidaPactoBR(Integer pessoa) throws Exception {
		String sql = "SELECT par.socialmail from socialmailpartener par "
				+ " inner join socialmail sm on par.socialmail = sm.codigo "
				+ " inner join socialmailgrupo gru on sm.socialmailgrupo = gru.codigo "
				+ " WHERE par.pessoadestino =  "+pessoa+" AND par.datalida is null AND gru.ativo = 't'"
				+ " AND sm.pessoaorigem  in (SELECT col.pessoa FROM colaborador col "
				+ " INNER JOIN usuario u ON u.colaborador = col.codigo "
				+ " WHERE u.username ilike 'PACTOBR') order by par.socialmail desc limit 1";

		try (PreparedStatement pst = con.prepareStatement(sql)) {
			try (ResultSet dadosSQL = pst.executeQuery()) {
				if (dadosSQL.next()) {
					Integer codigoSocialMail = dadosSQL.getInt("socialmail");
					return consultarPorChavePrimaria(codigoSocialMail);
				}
			}
		}
		return new SocialMailVO();
	}

	public SocialMailVO consultarPorChavePrimaria(Integer codigo) throws Exception {
		if (UteisValidacao.emptyNumber(codigo)) {
			return new SocialMailVO();
		}
		try (PreparedStatement sqlConsulta = con.prepareStatement("SELECT * FROM socialmail WHERE codigo = " + codigo)) {
			try (ResultSet resultado = sqlConsulta.executeQuery()) {
				if (resultado.next()) {
					SocialMailVO obj = new SocialMailVO();
					obj.setNovoObj(false);
					obj.setCodigo(resultado.getInt("codigo"));
					obj.setDataEnvio(resultado.getDate("dataenvio"));
					obj.getPessoaOrigem().setCodigo(resultado.getInt("pessoaorigem"));
					obj.setTexto(resultado.getString("texto"));
					obj.setGrupo(resultado.getString("grupo"));
					obj.getSocialMailGrupo().setCodigo(resultado.getInt("socialmailgrupo"));
					obj.setEnviarSMS(resultado.getBoolean("enviarsms"));
					obj.setPergunta_id(resultado.getInt("pergunta_id"));
					return obj;
				}
			}
		}
		return new SocialMailVO();
	}

	public boolean existeGruposConversaPactoBRSolicitacoesMesmoFinalizada(Integer pessoaUsuarioLogado) throws Exception {

		StringBuilder sql = new StringBuilder();
		sql.append("select exists( \n");
		sql.append("select \n");
		sql.append("distinct(sg.codigo) \n");
		sql.append("from socialmailgrupo sg \n");
		sql.append("inner join socialmailgrupoparticipante sgp on sgp.socialmailgrupo = sg.codigo \n");
		sql.append("where 1 = 1 \n");
		sql.append("and (sg.solicitacao_id is not null and sg.solicitacao_id <> 0) \n");
		sql.append("AND sgp.participante = ").append(pessoaUsuarioLogado).append(" )");

		try (Statement stm = con.createStatement()) {
			try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
				tabelaResultado.next();
				return tabelaResultado.getBoolean(1);
			}
		}
	}

	public void marcarRespostaCompreendida(Integer socialmail, Integer pessoadestino) throws Exception{
		String sql = "update socialmailpartener set respostaCompreendida = true where socialmail = " + socialmail + " and  pessoadestino = "+ pessoadestino;
		executarConsulta(sql, con);
	}

	public void enviarMensagemComoRemetentePactoBR(String mensagemTexto, PessoaVO pessoaDestino, boolean enviarSMS) throws Exception {
		enviarMensagem(mensagemTexto, getFacade().getPessoa().getPessoaPactoBR(), pessoaDestino, enviarSMS);
	}

	public void enviarMensagem(String mensagemTexto, PessoaVO pessoaRemetente, PessoaVO pessoaDestino, boolean enviarSMS) throws Exception {
		SocialMailVO socialMail = new SocialMailVO();

		socialMail.setTexto(mensagemTexto);
		socialMail.setPessoaOrigem(pessoaRemetente);
		socialMail.setDataEnvio(Calendario.hoje());
		socialMail.setEnviarSMS(enviarSMS);
		socialMail.setGrupo(pessoaRemetente.getCodigo().toString());

		SocialMailGrupoVO socialGrupo = getFacade().getSocialMailing().consultarGrupoEntreDuasPessoas(pessoaRemetente, pessoaDestino);

		if (UteisValidacao.emptyNumber(socialGrupo.getCodigo())) {
			socialGrupo.setDono(pessoaRemetente);
			socialGrupo.setDataCriacao(Calendario.hoje());

			SocialMailGrupoParticipanteVO participanteRemetente = new SocialMailGrupoParticipanteVO();
			participanteRemetente.setParticipante(pessoaRemetente);
			participanteRemetente.setAtivo(true);
			participanteRemetente.setSocialMailGrupo(socialGrupo);

			if (participanteRemetente.getNome().equals("PACTO - MÉTODO DE GESTÃO")) {
				participanteRemetente.setUsuarioPactoBR(true);
			}

			SocialMailGrupoParticipanteVO participanteDestinatario = new SocialMailGrupoParticipanteVO();
			participanteDestinatario.setParticipante(pessoaDestino);
			participanteDestinatario.setAtivo(true);
			participanteDestinatario.setSocialMailGrupo(socialGrupo);
			participanteDestinatario.setUsuarioPactoBR(false);

			socialGrupo.setParticipantes(asList(participanteDestinatario, participanteRemetente));

			getFacade().getSocialMailGrupo().incluir(socialGrupo);
		}

		socialMail.setSocialMailGrupo(socialGrupo);
		getFacade().getSocialMailing().incluir(socialMail);
	}

	public SocialMailGrupoVO consultarGrupoEntreDuasPessoas(PessoaVO pessoaDonaConversa, PessoaVO pessoaDestino) throws SQLException, NegocioException {
    	return consultarGrupoEntreDuasPessoas(pessoaDonaConversa.getCodigo(), pessoaDestino.getCodigo());
	}

	public SocialMailGrupoVO consultarGrupoEntreDuasPessoas(Integer codigoPessoaDonaConversa, Integer codigoPessoaDestino) throws SQLException, NegocioException {
		if (UteisValidacao.emptyNumber(codigoPessoaDonaConversa)) {
			throw new NegocioException("É necessário informar o 'codigoPessoaDonaConversa'");
		}

		if (UteisValidacao.emptyNumber(codigoPessoaDestino)) {
			throw new NegocioException("É necessário informar o 'codigoPessoaDestino'");
		}

		SocialMailGrupoVO socialMailGrupoVO;
		List<SocialMailGrupoParticipanteVO> participantes;
		try (PreparedStatement ps = con.prepareStatement(new StringBuilder()
				.append("     SELECT                                                                                                                     ")
				.append("            grupo.codigo                      as \"grupo.codigo\",                                                              ")
				.append("            grupoparticipante.codigo          as \"grupoparticipante.codigo\",                                                  ")
				.append("            grupoparticipante.ativo           as \"grupoparticipante.ativo\",                                                   ")
				.append("            grupoparticipante.socialmailgrupo as \"grupoparticipante.socialmailgrupo\",                                         ")
				.append("            grupoparticipante.participante    as \"grupoparticipante.participante\",                                            ")
				.append("            pessoa.nome                       as \"pessoa.nome\"                                                                ")

				.append("       FROM             socialmailgrupo grupo                                                                                   ")
				.append(" INNER JOIN socialmailgrupoparticipante grupoparticipante ON grupoparticipante.socialmailgrupo = grupo.codigo                   ")
				.append(" INNER JOIN                      pessoa pessoa            ON                     pessoa.codigo = grupoparticipante.participante ")

				.append("      WHERE grupo.dono = ?                                                                                                      ")

				// O 'grupo.codigo' em questão deve conter as duas pessoas (remetente e destinatário).
				.append("        AND EXISTS (                                                                                                            ")
				.append("                     SELECT 1                                                                                                   ")
				.append("                       FROM socialmailgrupoparticipante grupoparticipante1                                                      ")
				.append("                 INNER JOIN socialmailgrupoparticipante grupoparticipante2                                                      ")
				.append("                                                     ON grupoparticipante2.socialmailgrupo = grupoparticipante1.socialmailgrupo ")

				.append("              WHERE grupoparticipante1.socialmailgrupo = grupo.codigo                                                           ")
				.append("                AND grupoparticipante2.socialmailgrupo = grupo.codigo                                                           ")
				.append("                AND    grupoparticipante1.participante = ?                                                                      ")
				.append("                AND    grupoparticipante2.participante = ?                                                                      ")
				.append("                AND           grupoparticipante1.ativo = true                                                                   ")
				.append("                AND           grupoparticipante2.ativo = true                                                                   ")
				.append("           )                                                                                                                    ")
				.toString()
		)) {

			ps.setInt(1, codigoPessoaDonaConversa);
			ps.setInt(2, codigoPessoaDonaConversa);
			ps.setInt(3, codigoPessoaDestino);

			try (ResultSet rs = ps.executeQuery()) {
				socialMailGrupoVO = new SocialMailGrupoVO();
				SocialMailGrupoParticipanteVO participanteVO;
				PessoaVO pessoaVO;
				participantes = new ArrayList<SocialMailGrupoParticipanteVO>();
				while (rs.next()) {
					socialMailGrupoVO.setCodigo(rs.getInt("grupo.codigo"));

					pessoaVO = new PessoaVO();
					pessoaVO.setCodigo(rs.getInt("grupoparticipante.participante"));
					pessoaVO.setNome(rs.getString("pessoa.nome"));

					participanteVO = new SocialMailGrupoParticipanteVO();
					participanteVO.setParticipante(pessoaVO);
					participanteVO.setCodigo(rs.getInt("grupoparticipante.codigo"));
					participanteVO.setAtivo(rs.getBoolean("grupoparticipante.ativo"));

					participantes.add(participanteVO);
				}
			}
		}

		socialMailGrupoVO.setParticipantes(participantes);

		for (SocialMailGrupoParticipanteVO participante : socialMailGrupoVO.getParticipantes()) {
			if (participante.getParticipante().getCodigo().equals(codigoPessoaDonaConversa)) {
				socialMailGrupoVO.setDono(participante.getParticipante());
			}

			if (participante.getNome().equals("PACTO - MÉTODO DE GESTÃO")) {
				participante.setUsuarioPactoBR(true);
			}
		}

		return socialMailGrupoVO;
	}

}

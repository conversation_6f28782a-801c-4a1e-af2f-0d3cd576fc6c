/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.socialmailing.dao;

import br.com.pactosolucoes.socialmailing.interfaces.SocialMailGrupoInterfaceFacade;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoParticipanteVO;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoVO;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.executarConsulta;

/**
 *
 * <AUTHOR>
 */
public class SocialMailGrupo extends SuperEntidade implements SocialMailGrupoInterfaceFacade {

    public SocialMailGrupo(Connection conexao) throws Exception {
        super(conexao);
    }

    public SocialMailGrupo() throws Exception {
        super();
    }

    @Override
    public void incluir(SocialMailGrupoVO grupo) throws Exception {
        String sql = "INSERT INTO socialmailgrupo(datacriacao, dono, nome, coletivo, solicitacao_id, tipoSocialMail) VALUES (?, ?, ?, ?, ?, ?);";
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 1;
        stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(grupo.getDataCriacao()));
        stm.setInt(i++, grupo.getDono().getCodigo());
        stm.setString(i++, grupo.getNome());
        stm.setBoolean(i++, grupo.isColetivo());
        stm.setInt(i++, grupo.getSolicitacao_id());
        stm.setInt(i++, grupo.getTipoSocialMail().getCodigo());
        stm.execute();
        grupo.setCodigo(obterValorChavePrimariaCodigo());
        for (SocialMailGrupoParticipanteVO smgp : grupo.getParticipantes()) {
            smgp.setSocialMailGrupo(grupo);
            incluirParticipante(smgp);
        }
    }

    @Override
    public List<SocialMailGrupoVO> consultarGruposComLimite(Integer pessoa, Integer limite, Boolean coletivos, SocialMailGrupoVO grupoComparar,Boolean somenteAtivos) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT smg.*,p.nome as nomedono, (SELECT COUNT(codigo) FROM socialmail WHERE socialmailgrupo = smgp.socialmailgrupo) as nrmsgs, \n");
        sql.append(" (SELECT COUNT(smpc.codigo) FROM socialmailpartener smpc, socialmail smc WHERE datalida IS NULL AND pessoadestino = ").append(pessoa);
        sql.append(" AND smc.codigo = smpc.socialmail AND smc.socialmailgrupo = smg.codigo) as naolidas,");
        sql.append("(SELECT MAX(dataenvio) FROM socialmail WHERE socialmailgrupo = smgp.socialmailgrupo) as maxdata \n");
        sql.append("FROM socialmailgrupo smg \n");
        sql.append("INNER JOIN socialmailgrupoparticipante smgp ON smgp.socialmailgrupo = smg.codigo AND smgp.ativo AND smgp.participante = ").append(pessoa);
        if (coletivos != null) {
            sql.append(coletivos ? " AND smg.coletivo \n" : " AND NOT smg.coletivo\n");
        }
        sql.append("LEFT JOIN pessoa p ON p.codigo = smg.dono ");
        if(somenteAtivos!=null && somenteAtivos ) {
            sql.append("INNER JOIN Colaborador co ON  co.pessoa = p.codigo\n ");
        }
        sql.append("WHERE smg.ativo \n");
        sql.append("AND (smg.solicitacao_id is null or smg.solicitacao_id = 0) \n");
        if(somenteAtivos!=null && somenteAtivos) {
            sql.append(" AND (co.situacao = 'AT' OR smg.coletivo) ");
        }
        if (grupoComparar != null) {
            Ordenacao.ordenarLista(grupoComparar.getParticipantes(), "codigoPessoa");
            String cods = "";
            for (SocialMailGrupoParticipanteVO smgp : grupoComparar.getParticipantes()) {
                cods += ";" + smgp.getParticipante().getCodigo();
            }
            sql.append("and ARRAY_TO_STRING(\n");
            sql.append("ARRAY(select participante from socialmailgrupoparticipante where ativo ");
            sql.append("and socialmailgrupo = smg.codigo order by participante), ';') like '").append(cods.toString().replaceFirst(";", ""));
            sql.append("'");
        }

        return montarDadosConsulta(criarConsulta(sql.toString(), con), Uteis.NIVELMONTARDADOS_TODOS);

    }

    public List<SocialMailGrupoVO> montarDadosConsulta(ResultSet rs, Integer nivel) throws Exception {
        List<SocialMailGrupoVO> lista = new ArrayList<SocialMailGrupoVO>();
        while (rs.next()) {
            lista.add(montarDados(rs, nivel));
        }
        return lista;
    }

    public SocialMailGrupoVO montarDados(ResultSet rs, Integer nivel) throws Exception {
        SocialMailGrupoVO obj = new SocialMailGrupoVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.getDono().setCodigo(rs.getInt("dono"));
        obj.setDataCriacao(rs.getDate("datacriacao"));
        obj.setColetivo(rs.getBoolean("coletivo"));
        obj.setNome(rs.getString("nome"));
        obj.setSolicitacao_id(rs.getInt("solicitacao_id"));
        obj.setAtivo(rs.getBoolean("ativo"));
        if (nivel == Uteis.NIVELMONTARDADOS_TODOS) {
            obj.setNrMensagens(rs.getInt("nrmsgs"));
            obj.setNrMensagensNaoLidas(rs.getInt("naolidas"));
            obj.getDono().setNome(rs.getString("nomedono"));
            obj.setDataUltimaMsg(rs.getTimestamp("maxdata"));
            obj.setParticipantes(consultarParticipantes(obj.getCodigo()));
            obj.setDono(getFacade().getPessoa().consultarPorCodigo(obj.getDono().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            for (SocialMailGrupoParticipanteVO smgp : obj.getParticipantes()) {
                obj.getFotos().put(smgp.getParticipante().getCodigo(), smgp.getParticipante().getFoto());
            }
            addFotosContatosInativos(obj);
        }
        if (nivel == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            obj.setParticipantes(consultarParticipantes(obj.getCodigo()));
        }
        if (nivel == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            obj.setParticipantes(consultarParticipantes(obj.getCodigo()));
            obj.setDono(getFacade().getPessoa().consultarPorCodigo(obj.getDono().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS));
            for (SocialMailGrupoParticipanteVO smgp : obj.getParticipantes()) {
                obj.getFotos().put(smgp.getParticipante().getCodigo(), smgp.getParticipante().getFoto());
            }
        }
        return obj;
    }

    public void addFotosContatosInativos(SocialMailGrupoVO smg) throws Exception {
        String sql = "SELECT p.codigo, p.foto FROM socialmailgrupoparticipante smgp \n"
                + " INNER JOIN pessoa p ON p.codigo = smgp.participante \n"
                + " WHERE not ativo AND socialmailgrupo = " + smg.getCodigo();
        ResultSet rs = criarConsulta(sql, con);
        while(rs.next()){
            smg.getFotos().put(rs.getInt("codigo"), rs.getBytes("foto"));
        }
    }

    public List<SocialMailGrupoParticipanteVO> consultarParticipantes(Integer codigoGrupo) throws Exception {
        String sql = "SELECT smgp.*, p.nome, p.foto, p.fotokey FROM socialmailgrupoparticipante smgp \n"
                + " INNER JOIN pessoa p ON p.codigo = smgp.participante \n"
                + " WHERE ativo AND socialmailgrupo = " + codigoGrupo;
        return montarParticipantes(criarConsulta(sql, con));

    }

    public List<SocialMailGrupoParticipanteVO> montarParticipantes(ResultSet rs) throws Exception {
        List<SocialMailGrupoParticipanteVO> lista = new ArrayList<SocialMailGrupoParticipanteVO>();
        while (rs.next()) {
            SocialMailGrupoParticipanteVO smgp = new SocialMailGrupoParticipanteVO();
            smgp.setCodigo(rs.getInt("codigo"));
            smgp.setAtivo(rs.getBoolean("ativo"));
            smgp.getParticipante().setCodigo(rs.getInt("participante"));
            smgp.getSocialMailGrupo().setCodigo(rs.getInt("socialmailgrupo"));
            try {
                smgp.getParticipante().setNome(rs.getString("nome"));
                smgp.getParticipante().setFoto(rs.getBytes("foto"));
                smgp.getParticipante().setFotoKey(rs.getString("fotokey"));
            } catch (Exception e) {
            }
            lista.add(smgp);
        }
        return lista;
    }

    @Override
    public void incluirParticipante(SocialMailGrupoParticipanteVO participante) throws Exception {
        String sql = "INSERT INTO socialmailgrupoparticipante(participante, socialmailgrupo, ativo) VALUES (?,?,?);";
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 1;
        if(UteisValidacao.emptyNumber( participante.getParticipante().getCodigo())){
            stm.setNull(i++,0);
        }else {
            stm.setInt(i++, participante.getParticipante().getCodigo());
        }
        stm.setInt(i++, participante.getSocialMailGrupo().getCodigo());
        stm.setBoolean(i++, participante.isAtivo());
        stm.execute();
        participante.setCodigo(obterValorChavePrimariaCodigo());
    }

    @Override
    public void alterarNomeGrupo(SocialMailGrupoVO grupo) throws Exception {
        if (UteisValidacao.emptyNumber(grupo.getCodigo())) {
            incluir(grupo);
        } else {
            executarConsulta("UPDATE socialmailgrupo SET nome ='" + grupo.getNome()
                    + "' WHERE codigo = " + grupo.getCodigo(), con);
        }
        grupo.setNomeResumido(grupo.getNome());
    }

    @Override
    public void desativarGrupo(SocialMailGrupoVO grupo) throws Exception {
        executarConsulta("UPDATE socialmailgrupo SET ativo = false WHERE codigo = " + grupo.getCodigo(), con);

    }

    @Override
    public void desativarParticipante(SocialMailGrupoParticipanteVO participante) throws Exception {
        ResultSet rsDonoAtual = criarConsulta("SELECT dono FROM socialmailgrupo WHERE codigo = "+participante.getSocialMailGrupo().getCodigo(), con);
        Integer dono = 0;
        if(rsDonoAtual.next()){
            dono = rsDonoAtual.getInt("dono");
        }
        executarConsulta("UPDATE socialmailgrupoparticipante SET ativo = false "
                + "WHERE participante = " + participante.getCodigoPessoa()
                + " AND socialmailgrupo = "+ participante.getSocialMailGrupo().getCodigo(), con);
        if (participante.getParticipante().getCodigo().equals(dono)) {
            ResultSet rsDono = criarConsulta("SELECT pessoaorigem FROM socialmail "
                    + " where codigo = (select min(codigo) from  socialmail sc where sc.pessoaorigem <>"
                    + participante.getParticipante().getCodigo() + " AND sc.socialmailgrupo = "
                    + participante.getSocialMailGrupo().getCodigo() + " ) ", con);
            if (rsDono.next()) {
                executarConsulta("UPDATE socialmailgrupo SET dono = " + rsDono.getInt("pessoaorigem") + " WHERE codigo = "
                        + participante.getSocialMailGrupo().getCodigo(), con);
            }
        }

    }

    public SocialMailGrupoVO consultarPorIdSolicitacao(Integer idSolicitacao) throws Exception {
        SocialMailGrupoVO obj = new SocialMailGrupoVO();
        String sql = "select * from socialmailgrupo  where solicitacao_id = " + idSolicitacao;
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        ResultSet resultado = sqlConsulta.executeQuery();
        if (resultado.next()) {
            return  montarDados(resultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        }
        return obj;
    }
}

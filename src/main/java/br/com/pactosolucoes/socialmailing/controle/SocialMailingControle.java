package br.com.pactosolucoes.socialmailing.controle;

import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;

import br.com.pactosolucoes.socialmailing.modelo.*;
import org.json.JSONObject;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.UsuarioControle;
import controle.basico.ClienteControle;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;


public class SocialMailingControle extends SuperControle {

    private List<ConversaTO> conversas = new ArrayList<ConversaTO>();
    private List<ConversaTO> conversasGrupo = new ArrayList<ConversaTO>();
    private List<ConversaTO> conversasContato = new ArrayList<ConversaTO>();
    private ConversaTO conversaSelecionada = new ConversaTO();
    private SocialMailVO socialMail = new SocialMailVO();
    private SocialMailVO antigoSocialMail = new SocialMailVO();
    private String nomeDestino = "";
    private List<MensagemTO> mensagens = new ArrayList<MensagemTO>();
    private String grupoSelecionado = "";
    private String oncompleteCarrossel = "";
    private String idSelecionado = "";
    private String rerenderNLidas = "";
    private String consultaUsuario = "Buscar contato";
    private String consultaAdicionarUsuario = "";
    private String matriculaSelecionada = "";
    private String onCompleteTag = "";
    private List<byte[]> fotosParticipantes = new ArrayList<byte[]>();
    private boolean exibirResultados = false;
    private List<UsuarioVO> listaUsuarios = new ArrayList<UsuarioVO>();
    private List<UsuarioVO> listaUsuariosAdicionarConversa = new ArrayList<UsuarioVO>();
    //----------------------------REFACTOR-------------------------------------------//
    private SocialMailGrupoVO grupoSelected = new SocialMailGrupoVO();
    private List<SocialMailGrupoVO> grupos = new ArrayList<SocialMailGrupoVO>();
    private List<SocialMailGrupoVO> gruposColetivos = new ArrayList<SocialMailGrupoVO>();
    private List<SocialMailGrupoVO> gruposSimples = new ArrayList<SocialMailGrupoVO>();
    private String mensagemEnvioSMS = "";
    private Boolean apresentarSomenteAtivos = true;
    private TipoSocialMailEnum tipoSocialMail;

    public String getAbrirPopUp() {
        return "abrirPopup('" + JSFUtilities.getRequest().getContextPath() + "/faces/socialMailing.jsp', 'SocialMailing', 725, 620)";

    }

    public String getOnClickTirar() {
        try {
            return getGrupoSelected().isSouDono() ? "if(!confirm('Excluir a pessoa da conversa?')){return false;};"
                    : "alert('Somente o administrador do grupo poderá remover pessoas. Peça a "
                    + grupoSelected.getDono().getPrimeiroNomeConcatenado() + " para fazer isso.');return false;";
        } catch (Exception e) {
            return "";
        }
    }

    public void sairGrupo() throws Exception {
        for (SocialMailGrupoParticipanteVO smgp : grupoSelected.getParticipantes()) {
            if (smgp.getParticipante().getCodigo().equals(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo())) {
                getFacade().getSocialMailGrupo().desativarParticipante(smgp);
                msgPadrao(smgp.getParticipante(), false);
                getFacade().getSocialMailing().marcarConversasLidas(Calendario.hoje(), grupoSelected.getCodigo(), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
            }
        }
        atualizarConversas();

    }

    public void tirarParticipantedoGrupo() throws Exception {
        setMsgAlert("");
        SocialMailGrupoParticipanteVO smgp = (SocialMailGrupoParticipanteVO) request().getAttribute("partner");
        if (smgp.getParticipante().getCodigo().equals(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo())) {
            sairGrupo();
        } else {
            if (grupoSelected.isSouDono()) {
                getFacade().getSocialMailGrupo().desativarParticipante(smgp);
                grupoSelected.setParticipantes(getFacade().getSocialMailGrupo().consultarParticipantes(grupoSelected.getCodigo()));
                msgPadrao(smgp.getParticipante(), false);
                getFacade().getSocialMailing().marcarConversasLidas(Calendario.hoje(), grupoSelected.getCodigo(), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
            } else {
                setMsgAlert("alert('Somente o administrador do grupo poderá remover pessoas. Peça a "
                        + grupoSelected.getDono().getPrimeiroNomeConcatenado() + " para fazer isso.');");
            }

        }
    }

    public void desativarGrupo() throws Exception {
        getFacade().getSocialMailGrupo().desativarGrupo(grupoSelected);
        atualizarConversas();
    }

    public void novoGrupo() throws Exception {
        grupoSelected = new SocialMailGrupoVO();
        grupoSelected.setDataCriacao(Calendario.hoje());
        grupoSelected.setColetivo(true);
        grupoSelected.setDono(getUsuarioLogado().getColaboradorVO().getPessoa());
        grupoSelected.setSouDono(true);
        SocialMailGrupoParticipanteVO participante2 = new SocialMailGrupoParticipanteVO();
        participante2.setParticipante(getUsuarioLogado().getColaboradorVO().getPessoa());
        grupoSelected.getParticipantes().add(participante2);
        grupoSelected.setTipoSocialMail(TipoSocialMailEnum.CONVERSAS);
        getFacade().getSocialMailGrupo().incluir(grupoSelected);
        grupos.add(grupoSelected);
        gruposColetivos.add(grupoSelected);
        grupoSelected.getFotos().put(participante2.getParticipante().getCodigo(),
                getFacade().getPessoa().obterFoto(getKey(),
                        participante2.getParticipante().getCodigo()));
    }

    public void selecionarContato() throws Exception {
        UsuarioVO user = (UsuarioVO) request().getAttribute("result");
        if (user != null) {
            SocialMailGrupoVO smg = new SocialMailGrupoVO();
            SocialMailGrupoParticipanteVO participante1 = new SocialMailGrupoParticipanteVO();
            participante1.setParticipante(user.getColaboradorVO().getPessoa());
            SocialMailGrupoParticipanteVO participante2 = new SocialMailGrupoParticipanteVO();
            participante2.setParticipante(getUsuarioLogado().getColaboradorVO().getPessoa());
            smg.getParticipantes().add(participante1);
            smg.getParticipantes().add(participante2);
            List<SocialMailGrupoVO> lista = getFacade().getSocialMailGrupo().consultarGruposComLimite(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo(), 1, null, smg,true);
            if (lista.isEmpty()) {
                smg.montarNomeConversa(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                grupos.add(smg);
                gruposSimples.add(smg);
                escolherGrupo(smg);
            } else {
                int indexOf = gruposSimples.indexOf(lista.get(0));
                if (indexOf >= 0) {
                    escolherGrupo(gruposSimples.get(indexOf));
                } else {
                    lista.get(0).montarNomeConversa(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                    grupos.add(lista.get(0));
                    gruposSimples.add(lista.get(0));
                    escolherGrupo(lista.get(0));
                }
            }
            fecharConsulta();
        }
    }

    public void gravarNomeGrupo() {
        try {
            if (!UteisValidacao.emptyString(grupoSelected.getNome())) {
                getFacade().getSocialMailGrupo().alterarNomeGrupo(grupoSelected);
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void limparEscolhas() {
        for (SocialMailGrupoVO sc : gruposColetivos) {
            sc.setSelected("");
        }
        for (SocialMailGrupoVO sc : gruposSimples) {
            sc.setSelected("");
        }
    }

    public void escolherGrupo() {
        escolherGrupo((SocialMailGrupoVO) context().getExternalContext().getRequestMap().get("grupo"));
    }

    public void escolherGrupo(SocialMailGrupoVO socialMailGrupo) {
        try {
            limparEscolhas();
            grupoSelected = socialMailGrupo;
            grupoSelected.setSelected("selected");
            if (UteisValidacao.emptyList(grupoSelected.getMensagens())) {
                getFacade().getSocialMailing().consultarMensagensPorGrupo(grupoSelected, getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
            }

            if (getGrupoSolicitacao()) {
                UsuarioVO usuarioPactoBR = getFacade().getUsuario().consultarPorUsername("PACTOBR", Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                for (MensagemTO mensagemTO : grupoSelected.getMensagens()) {
                    mensagemTO.setUsuarioPactoBR(mensagemTO.getCodPessoa().equals(usuarioPactoBR.getColaboradorVO().getPessoa().getCodigo()));
                }

                for (SocialMailGrupoParticipanteVO part : grupoSelected.getParticipantes()) {
                    part.setUsuarioPactoBR(part.getParticipante().getCodigo().equals(usuarioPactoBR.getColaboradorVO().getPessoa().getCodigo()));
                }

                for (SocialMailGrupoVO grupoVO : gruposColetivos) {
                    if (socialMailGrupo.getCodigo().equals(grupoVO.getCodigo())) {
                        grupoVO.setSelected("selected");
                        grupoVO.setNrMensagensNaoLidas(0);
                    } else {
                        grupoVO.setNrMensagensNaoLidas(getFacade().getSocialMailing().conversasNaoLidas(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo(), grupoVO.getCodigo()));
                    }
                }
            }

            getFacade().getSocialMailing().consultarMensagensPorGrupo(grupoSelected, getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
            getFacade().getSocialMailing().marcarConversasLidas(Calendario.hoje(), grupoSelected.getCodigo(), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
            grupoSelected.setNrMensagensNaoLidas(0);
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    public void selecionarResultadoAdicionar() throws Exception {
        UsuarioVO user = (UsuarioVO) request().getAttribute("result");
        consultaAdicionarUsuario = "";
        listaUsuariosAdicionarConversa = new ArrayList<UsuarioVO>();
        if (user != null) {
            for (SocialMailGrupoParticipanteVO smgp : grupoSelected.getParticipantes()) {
                if (smgp.getParticipante().getCodigo().equals(user.getColaboradorVO().getPessoa().getCodigo())) {
                    return;
                }
            }
            SocialMailGrupoParticipanteVO participante2 = new SocialMailGrupoParticipanteVO();
            participante2.setParticipante(user.getColaboradorVO().getPessoa());
            participante2.setSocialMailGrupo(grupoSelected);
            getFacade().getSocialMailGrupo().incluirParticipante(participante2);
            grupoSelected.getParticipantes().add(participante2);
            grupoSelected.montarNomeConversa(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
            grupoSelected.getFotos().put(participante2.getParticipante().getCodigo(), participante2.getParticipante().getFoto());
            msgPadrao(user.getColaboradorVO().getPessoa(), true);
            getFacade().getSocialMailing().marcarConversasLidas(Calendario.hoje(), grupoSelected.getCodigo(), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());

        }
    }

    public void msgPadrao(PessoaVO pessoa, boolean entrou) throws Exception {
        SocialMailVO sm = new SocialMailVO();
        sm.setSocialMailGrupo(grupoSelected);
        sm.setDataEnvio(Calendario.hoje());
        sm.setPessoaOrigem(pessoa);
        sm.setTexto(pessoa.getPrimeiroNomeConcatenado() + (entrou ? " entrou no " : " saiu do ") + "grupo.");
        getFacade().getSocialMailing().incluir(sm);

        MensagemTO msg = new MensagemTO();
        msg.setMensagem(sm.getTexto());
        msg.setCodPessoa(sm.getPessoaOrigem().getCodigo());
        msg.setPessoa(Uteis.getPrimeiroNome(sm.getPessoaOrigem().getNome()));
        msg.setFotoKey(Uteis.getPaintFotoDaNuvem(sm.getPessoaOrigem().getFotoKey()));
        msg.setEnviada(sm.getDataEnvio());
        grupoSelected.getMensagens().add(msg);

    }

    /**
     * Responsável por
     *
     * <AUTHOR> Alcides 05/02/2013
     */
    private void montarGrupo(boolean limpar, SocialMailPartenerVO destinario, boolean adicionar, boolean removerDaConversa) throws Exception {
        socialMail.obterGrupo();
        ConversaTO conversa = new ConversaTO();
        conversa.setGrupo(socialMail.getGrupo());
        setGrupoSelecionado(socialMail.getGrupo());

        //adicionar mensagem as conversas
        if (!removerDaConversa && getConversas().contains(conversa)) {
            conversa = getConversas().get(getConversas().indexOf(conversa));
            getConversas().remove(conversa);
            getConversas().add(0, conversa);

            if (conversa.getParteners().size() > 1) {
                getConversasGrupo().remove(conversa);
                getConversasGrupo().add(0, conversa);

            } else {
                getConversasContato().remove(conversa);
                getConversasContato().add(0, conversa);

            }
            selecionarGrupo();
        } else {
            getFacade().getSocialMailing().montarConversa(getKey(),
                    socialMail.getDestinatarios(), conversa);
            if (!limpar) {
                conversa.setFotos(getConversaSelecionada().getFotos());
            }
            if (adicionar) {
                conversa.getFotos().put(destinario.getPessoaDestino().getCodigo(), destinario.getPessoaDestino().getFoto());
            }
            getConversas().add(0, conversa);
            if (conversa.getParteners().size() >= 1 && conversa.getFotos().size() > 1) {
                if (getConversasGrupo().indexOf(getConversaSelecionada()) < 0) {//cria o grupo
                    getConversasGrupo().add(0, conversa);
                    getFacade().getSocialMailing().incluir(socialMail);
                } else if (removerDaConversa) {//adiciona o usuário ao grupo
                    SocialMailPartenerVO obj = (SocialMailPartenerVO) context().getExternalContext().getRequestMap().get("partner");
                    String usuario = obj.getPessoaDestino().getCodigo().toString();
                    getFacade().getSocialMailing().excluirPartnerChatExistente(antigoSocialMail, usuario);
                    atualizarConversas();
                } else if (adicionar) {
                    getConversasGrupo().get(getConversasGrupo().indexOf(getConversaSelecionada())).setParteners(conversa.getParteners());
                    getFacade().getSocialMailing().incluirPartnerChatExistente(getSocialMail(), getConversasGrupo().get(getConversasGrupo().indexOf(getConversaSelecionada())));
                    atualizarConversas();
                }
            } else {
                getConversasContato().add(0, conversa);
            }

            mensagens = new ArrayList<MensagemTO>();
            for (ConversaTO con : getConversas()) {
                con.setSelected("");
            }
            conversa.setSelected("selected");
            selecionarGrupo();
        }
    }

    public void consultarUsuario() throws Exception {
        if (consultaUsuario.equals("Buscar usuário")) {
            exibirResultados = false;
            return;
        }
        exibirResultados = true;
        if (UteisValidacao.emptyString(consultaUsuario)) {
            listaUsuarios = new ArrayList<UsuarioVO>();
        } else {
            UsuarioVO usuarioPactoBR = getFacade().getUsuario().consultarPorUsername("PACTOBR", Uteis.NIVELMONTARDADOS_MINIMOS);
            List<UsuarioVO> usuariosNaoPesquisar = new ArrayList<UsuarioVO>();
            usuariosNaoPesquisar.add(getUsuarioLogado());
            usuariosNaoPesquisar.add(usuarioPactoBR);
            listaUsuarios = getFacade().getUsuario().consultarNomeComFoto(consultaUsuario, usuariosNaoPesquisar);
        }
    }

    public void consultarUsuarioAdicionarConversa() throws Exception {
        if (UteisValidacao.emptyString(consultaAdicionarUsuario)) {
            listaUsuariosAdicionarConversa = new ArrayList<UsuarioVO>();
        } else {
            UsuarioVO usuarioPactoBR = getFacade().getUsuario().consultarPorUsername("PACTOBR", Uteis.NIVELMONTARDADOS_MINIMOS);
            List<UsuarioVO> usuariosNaoPesquisar = new ArrayList<UsuarioVO>();
            usuariosNaoPesquisar.add(getUsuarioLogado());
            usuariosNaoPesquisar.add(usuarioPactoBR);
            listaUsuariosAdicionarConversa = getFacade().getUsuario().consultarNomeComFoto(consultaAdicionarUsuario, usuariosNaoPesquisar);
        }
    }

    public void marcarRespostaCompreendida() {
        try {
            MensagemTO mensagem = (MensagemTO) request().getAttribute("mensagem");
            getFacade().getSocialMailing().marcarRespostaCompreendida(mensagem.getSocialMail(), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
            UsuarioControle usuarioControle = (UsuarioControle) getControlador(UsuarioControle.class.getSimpleName());
            usuarioControle.atualizarNrMsgNaoLidas(false);
        } catch (Exception ignored) {
        }
    }

    public void fecharConsulta() {
        exibirResultados = false;
        consultaUsuario = "Buscar usuário";
        listaUsuarios = new ArrayList<UsuarioVO>();

    }

    public void verificarConsulta() {
        if (UteisValidacao.emptyString(consultaUsuario) || consultaUsuario.equals("Buscar usuário")) {
            listaUsuarios = new ArrayList<UsuarioVO>();

        }
    }
    private String valorConsulta = "";
    private List<UsuarioVO> listaAdicionar = new ArrayList<UsuarioVO>();

    public void consultarUsuarios() {
        try {
            listaAdicionar = getFacade().getUsuario().consultarPorNomeEDiferenteDoUsuarioSomenteColaborador(getValorConsulta(), getUsuarioLogado().getCodigo(),
                    false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            setMsgAlert("");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void cancelar() throws Exception {
        if (getConversas().isEmpty()) {
            setSocialMail(new SocialMailVO());
        } else {
            selecionarGrupo();
        }

    }

    /**
     * <AUTHOR> Alcides 04/09/2012
     */
    public void pesquisarMensagens() {
        //limpar mensagens
        mensagens = new ArrayList<MensagemTO>();
        //se o filtro é limpo adicionar todas da conversa
        if (UteisValidacao.emptyString(getConsultaUsuario()) || getConsultaUsuario().equals("Buscar usuário")) {
            mensagens.addAll(conversaSelecionada.getMensagens());
        } else {
            //filtrar
            for (MensagemTO mensagem : conversaSelecionada.getMensagens()) {
                if (mensagem.getMensagem().toLowerCase().contains(getConsultaUsuario().toLowerCase())) {
                    mensagens.add(mensagem);
                }
            }
        }
    }

    public String atualizarConversas() throws Exception {
        if (this.getTipoSocialMail().equals(TipoSocialMailEnum.SOLICITACAO)) {
            grupos = getFacade().getSocialMailing().consultarGruposConversaPactoBR(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo(), verificaUsuarioPermiteBuscarTodas());
        } else if (this.getTipoSocialMail().equals(TipoSocialMailEnum.CONVERSAS)) {
            grupos = getFacade().getSocialMailGrupo().consultarGruposComLimite(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo(), 30, null, null,null);
        } else if (this.getTipoSocialMail().equals(TipoSocialMailEnum.CONTATO_APP)) {
//            grupos = getFacade().getSocialMailGrupo().consultarGruposComLimite(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo(), 30, null, null,null);
        }
        Ordenacao.ordenarLista(grupos, "dataUltimaMsg");
        montarConversas();

        if (!grupos.isEmpty()) {
            if (this.getTipoSocialMail().equals(TipoSocialMailEnum.CONVERSAS)) {
                Collections.reverse(grupos);
            }
            boolean grupoSelecionado = false;
            for (SocialMailGrupoVO grupoSelecionar : grupos) {
                if (grupoSelecionar.isAtivo()) {
                    escolherGrupo(grupoSelecionar);
                    grupoSelecionado = true;
                    break;
                }
            }
            if (!grupoSelecionado) {
                escolherGrupo(grupos.get(0));
            }
        }
        return recarregar();
    }

    public String preparaAbrirSolicitacoes() throws Exception {
        grupos = getFacade().getSocialMailing().consultarGruposConversaPactoBR(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo(), verificaUsuarioPermiteBuscarTodas());
        montarConversas();
        return recarregar();
    }

    private String recarregar() {
            return "socialMailing";
        }

    /**
     * <AUTHOR> Alcides 04/09/2012
     */
    public void abrirSocialMail() throws Exception {
        UsuarioControle usuarioControle = (UsuarioControle) getControlador(UsuarioControle.class.getSimpleName());
        usuarioControle.getUsuario().setNrMensagensNaoLidas(0);
        atualizarConversas();
    }

    /**
     * <AUTHOR> Alcides 04/09/2012
     */
    public void selecionarGrupo(MensagemTO mensagem) {
        try {
            //limpar campo de pesquisa
            setConsultaUsuario("Buscar usuário");
            mensagens = new ArrayList<MensagemTO>();
            //obter grupo selecionado
            conversaSelecionada.setGrupo(getGrupoSelecionado());
            //obter conversa do grupo
            ConversaTO conversaTO = getConversas().get(getConversas().indexOf(conversaSelecionada));
            for (ConversaTO con : getConversas()) {
                con.setSelected("");
            }
            conversaTO.setSelected("selected");
            //se a conversa ainda não foi carregada, buscar no banco
            if (conversaTO.getConversaCarregada()) {
                if (mensagem != null) {
                    conversaTO.getMensagens().add(mensagem);
                    Ordenacao.ordenarLista(conversaTO.getMensagens(), "enviada");
                }
            } else {
                getFacade().getSocialMailing().consultarMensagensPorGrupo(grupoSelected, getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                conversaTO.setConversaCarregada(true);
            }
            //clonar a conversa
            conversaSelecionada = conversaTO.getClone();
            //listar as mensagens
            mensagens = conversaTO.getMensagens();
            //listar destinatarios
            socialMail.setDestinatarios(conversaSelecionada.getParteners());
            //limpar mensagens não lidas
            getConversas().get(getConversas().indexOf(conversaSelecionada)).setNrMensagensNaoLidas(0);

            //marcar mensagens lidas
            getFacade().getSocialMailing().marcarConversasLidas(Calendario.hoje(), grupoSelected.getCodigo(), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());

            fotosParticipantes = new ArrayList<byte[]>();
            Set<Integer> keySet = getConversaSelecionada().getFotos().keySet();
            for (Integer key : keySet) {
                if (!key.equals(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo())) {
                    fotosParticipantes.add(getConversaSelecionada().getFotos().get(key));
                }
            }

        } catch (Exception e) {
            setMensagem(e.getMessage());
        }
    }

    public void mais() {
        try {
            getFacade().getSocialMailing().consultarMensagensPorGrupo(grupoSelected, getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
        } catch (Exception e) {
            setMensagem(e.getMessage());
        }

    }

    public void selecionarGrupo() {
        selecionarGrupo(null);
    }

    public void mostrarAluno() {
        try {
            setOnCompleteTag("");
            ClienteVO cliente = getFacade().getCliente().consultarPorCodigoMatricula(
                    Integer.valueOf(getMatriculaSelecionada()), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyNumber(cliente.getCodigo())) {
                setOnCompleteTag("alert('Aluno não existe!');");

            } else {
                ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
                clienteControle.prepararTelaCliente(cliente, false);
                setOnCompleteTag("abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente',  780, 595);");
            }

        } catch (Exception e) {
            setMensagem(e.getMessage());
        }
    }

    /**
     * <AUTHOR> Alcides 04/09/2012
     */
    public void enviarMensagem() {
        try {
            setOncompleteCarrossel("abaixar();");
            if (UteisValidacao.emptyNumber(getUsuarioLogado().getColaboradorVO().getCodigo())) {
                setOncompleteCarrossel("alert('Este usuário não possui cadastro de colaborador e não pode enviar mensagens!');");
                return;
            }
            if (grupoSelected.getParticipantes().isEmpty()) {
                setOncompleteCarrossel("alert('A conversa precisa ter pelo menos um destinatário!');");
                return;
            }
            if (socialMail.getTexto().isEmpty()) {
                setOncompleteCarrossel("alert('Mensagem vazia!');");
                return;
            }
            String textoExibirImediado = socialMail.getTexto();
            socialMail.setTexto(socialMail.getTexto().replaceAll("<", "&lt").replaceAll(">","&gt"));
            socialMail.setTexto(socialMail.getTexto().replaceAll("\\n", "<br/>"));
            textoExibirImediado = textoExibirImediado.replaceAll("\\n", "<br/>");
            //setar pessoa origem e data de envio
            socialMail.setPessoaOrigem(getUsuarioLogado().getColaboradorVO().getPessoa());
            socialMail.setDataEnvio(Calendario.hoje());
            if (UteisValidacao.emptyNumber(grupoSelected.getCodigo())) {
                grupoSelected.setDono(getUsuarioLogado().getColaboradorVO().getPessoa());
                grupoSelected.setDataCriacao(Calendario.hoje());
                getFacade().getSocialMailGrupo().incluir(grupoSelected);
            }
            socialMail.setSocialMailGrupo(grupoSelected);
            if (socialMail.isEnviarSMS()) {
                if (!enviarSMS()) {
                    setOncompleteCarrossel(getOncompleteCarrossel() + "alert('SMS não foi enviado. "
                            + mensagemEnvioSMS + "');");
                    socialMail.setEnviarSMS(false);
                }
            }
            if (!UteisValidacao.emptyNumber(socialMail.getSocialMailGrupo().getSolicitacao_id())) {
                String resposta = getFacade().getSocialMailing().enviarMensagemService(getUsuarioLogado().getNome(), socialMail.getSocialMailGrupo().getSolicitacao_id(), socialMail.getTexto());
                JSONObject retorno = new JSONObject(resposta);
                if (retorno.optString("return") == null || !retorno.optString("return").toLowerCase().equals("ok")) {
                    throw new Exception("Não foi possível enviar sua mensagem. Erro: " + retorno.optString("erro"));
                }
            }
            getFacade().getSocialMailing().incluir(socialMail);
            MensagemTO msg = new MensagemTO();
            msg.setMensagem(textoExibirImediado);
            msg.setCodPessoa(socialMail.getPessoaOrigem().getCodigo());
            msg.setPessoa(socialMail.getPessoaOrigem().getNome());
            msg.setFotoKey(Uteis.getPaintFotoDaNuvem(socialMail.getPessoaOrigem().getFotoKey()));
            msg.setEnviada(socialMail.getDataEnvio());
            msg.setEnviadoSMS(socialMail.isEnviarSMS());
            grupoSelected.getMensagens().add(msg);
            socialMail.setTexto("");
            socialMail.setEnviarSMS(false);

        } catch (Exception e) {
            socialMail.setTexto(socialMail.getTexto().replaceAll("&lt", "<").replaceAll("&gt",">"));
            socialMail.setTexto(socialMail.getTexto().replaceAll("<br/>", "\\n"));
            setOncompleteCarrossel("alert('" + e.getMessage() + "');");
        }
    }

    public boolean enviarSMS() throws Exception {
        mensagemEnvioSMS = "";
        String texto = socialMail.getTexto().length() > 140 ? socialMail.getTexto().substring(0, 140)
                : socialMail.getTexto();
        boolean enviado = false;
        if (!getUsuarioLogado().getAdministrador()) {
            String tokenSMS = getFacade().getEmpresa().obterTokenSMS(getEmpresaLogado().getCodigo());
            SmsController smsController = new SmsController(tokenSMS, getKey(), TimeZone.getTimeZone(getEmpresaLogado().getTimeZoneDefault()));


            for (SocialMailGrupoParticipanteVO smgp : grupoSelected.getParticipantes()) {
                List<TelefoneVO> tels = getFacade().getTelefone().consultarTelefones(smgp.getParticipante().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (TelefoneVO tel : tels) {
                    if (tel.getTipoTelefone().equals("CE")) {
                        String retorno = smsController.sendMessage(null,  new Message().setNumero(tel.getNumero()).setMsg(texto));
                        enviado = "".equals(retorno) || retorno.contains("ok");
                        mensagemEnvioSMS = enviado ? mensagemEnvioSMS : retorno;
                    }
                }
            }
        }
        return enviado;
    }

    /**
     * <AUTHOR> Alcides 04/09/2012
     */
    public void selecionarColaboradorSuggestionBox() throws Exception {
        setOncompleteCarrossel("");
        UsuarioVO user = (UsuarioVO) request().getAttribute("result");
        if (user != null) {
            SocialMailPartenerVO partner = new SocialMailPartenerVO();
            partner.setPessoaDestino(user.getColaboradorVO().getPessoa());
            if (!socialMail.getDestinatarios().contains(partner)) {
                socialMail.getDestinatarios().add(partner);
            }
        }
        nomeDestino = "";
    }

    /**
     * <AUTHOR> Alcides 04/09/2012
     * @throws Exception
     */
    public void removerDestinatario() throws Exception {
        setMsgAlert("");
        if (socialMail.getDestinatarios().size() == 1) {
            setMsgAlert("alert('Conversa com uma pessoa apenas, você não pode excluí-la.');");
            return;
        }
        SocialMailPartenerVO obj = (SocialMailPartenerVO) context().getExternalContext().getRequestMap().get("partner");
        int index = socialMail.getDestinatarios().indexOf(obj);
        if (index > -1) {
            antigoSocialMail = (SocialMailVO) socialMail.getClone(true);
            antigoSocialMail.obterGrupo();
            socialMail.getDestinatarios().remove(index);
            socialMail.setPessoaOrigem(getUsuarioLogado().getColaboradorVO().getPessoa());
            montarGrupo(false, new SocialMailPartenerVO(), false, true);
        }
    }

    /**
     * <AUTHOR> Alcides 04/09/2012
     */
    public List<ConversaTO> executarAutocompletePesquisa(Object suggest) throws Exception {
        String pref = (String) suggest;
        ArrayList<ConversaTO> result = new ArrayList<ConversaTO>();

        for (ConversaTO conversa : getConversas()) {
            if (conversa.getNomeConversa().toLowerCase().contains(pref.toLowerCase())) {
                result.add(conversa.getClone());
            }
        }

        return result;
    }

    /**
     * <AUTHOR> Alcides 04/09/2012
     */
    public List<UsuarioVO> executarAutocompleteConsultaColaborador(Object suggest) {
        String pref = (String) suggest;
        ArrayList<UsuarioVO> result = new ArrayList<UsuarioVO>();
        try {
            if (pref.equals("%")) {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNomeEDiferenteDoUsuarioSomenteColaborador("", getUsuarioLogado().getCodigo(), false,
                        Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            } else {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNomeEDiferenteDoUsuarioSomenteColaborador(pref, getUsuarioLogado().getCodigo(), false,
                        Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<UsuarioVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    /**
     * <AUTHOR> Alcides 04/09/2012
     */
    public String getNomeDestinatarios() {
        String nomes = "";
        for (SocialMailPartenerVO socialP : socialMail.getDestinatarios()) {
            nomes += ", " + Uteis.getPrimeiroNome(socialP.getPessoaDestino().getNome());
        }
        return nomes.replaceFirst(", ", "");
    }

    public void setNomeDestinatarios(String e) {
    }

    public List<ConversaTO> getConversas() {
        return conversas;
    }

    public void setConversas(List<ConversaTO> conversas) {
        this.conversas = conversas;
    }

    public ConversaTO getConversaSelecionada() {
        return conversaSelecionada;
    }

    public void setConversaSelecionada(ConversaTO conversaSelecionada) {
        this.conversaSelecionada = conversaSelecionada;
    }

    public SocialMailVO getSocialMail() {
        return socialMail;
    }

    public void setSocialMail(SocialMailVO socialMail) {
        this.socialMail = socialMail;
    }

    public String getNomeDestino() {
        return nomeDestino;
    }

    public void setNomeDestino(String nomeDestino) {
        this.nomeDestino = nomeDestino;
    }

    public void setMensagens(List<MensagemTO> mensagens) {
        this.mensagens = mensagens;
    }

    public List<MensagemTO> getMensagens() {
        return mensagens;
    }

    public void setGrupoSelecionado(String grupoSelecionado) {
        this.grupoSelecionado = grupoSelecionado;
    }

    public String getGrupoSelecionado() {
        return grupoSelecionado;
    }

    public Integer getCodPessoaLogada() throws Exception {
        return getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo();
    }

    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        Integer grupo = Integer.valueOf(request.getParameter("grupo"));
        SuperControle.paintFoto(out, SocialMailGrupoVO.obterFoto(grupo, gruposSimples));
    }



    public void paintFotoConsulta(OutputStream out, Object data) throws IOException, Exception {

        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();

        UsuarioVO usuario = new UsuarioVO();
        usuario.setCodigo(Integer.valueOf(request.getParameter("usuario")));
        if (listaUsuarios.contains(usuario)) {
            usuario = listaUsuarios.get(listaUsuarios.indexOf(usuario));
        } else if (listaUsuariosAdicionarConversa.contains(usuario)) {
            usuario = listaUsuariosAdicionarConversa.get(listaUsuariosAdicionarConversa.indexOf(usuario));
        }
        SuperControle.paintFoto(out, usuario.getColaboradorVO().getPessoa().getFoto());
    }

    public void paintFotoMensagens(OutputStream out, Object data) throws IOException, Exception {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String pessoa = request.getParameter("pessoa");
        SuperControle.paintFoto(out, getGrupoSelected().getFotos().get(Integer.valueOf(pessoa)));
    }

    public void paintFotoUsuarioLogado(OutputStream out, Object data) throws IOException, Exception {
        SuperControle.paintFoto(out, getFacade().getPessoa().obterFoto(getKey(),
                getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo()));
    }

    public void paintFotoTopo(OutputStream out, Object data) throws IOException, Exception {

        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String grupo = request.getParameter("grupo");
        SuperControle.paintFoto(out, ConversaTO.obterFoto(grupo, conversas));
    }

    public void setIdSelecionado(String idSelecionado) {
        this.idSelecionado = idSelecionado;
    }

    public String getIdSelecionado() {
        return idSelecionado;
    }

    public void setRerenderNLidas(String rerenderNLidas) {
        this.rerenderNLidas = rerenderNLidas;
    }

    public String getRerenderNLidas() {
        return rerenderNLidas;
    }

    public void setConsultaUsuario(String consultaMensagens) {
        this.consultaUsuario = consultaMensagens;
    }

    public String getConsultaUsuario() {
        return consultaUsuario;
    }

    public void setMatriculaSelecionada(String matriculaSelecionada) {
        this.matriculaSelecionada = matriculaSelecionada;
    }

    public String getMatriculaSelecionada() {
        return matriculaSelecionada;
    }

    public void setOnCompleteTag(String onCompleteTag) {
        this.onCompleteTag = onCompleteTag;
    }

    public String getOnCompleteTag() {
        return onCompleteTag;
    }

    public void setOncompleteCarrossel(String oncompleteCarrossel) {
        this.oncompleteCarrossel = oncompleteCarrossel;
    }

    public String getOncompleteCarrossel() {
        return oncompleteCarrossel;
    }

    public void setConversasGrupo(List<ConversaTO> conversasGrupo) {
        this.conversasGrupo = conversasGrupo;
    }

    public List<ConversaTO> getConversasGrupo() {
        return conversasGrupo;
    }

    public void setConversasContato(List<ConversaTO> conversasContato) {
        this.conversasContato = conversasContato;
    }

    public List<ConversaTO> getConversasContato() {
        return conversasContato;
    }

    public void montarConversas() throws Exception {
        grupoSelected = new SocialMailGrupoVO();
        gruposColetivos = new ArrayList<SocialMailGrupoVO>();
        gruposSimples = new ArrayList<SocialMailGrupoVO>();
        for (SocialMailGrupoVO grupoSm : grupos) {
            grupoSm.montarNomeConversa(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
            if (grupoSm.isColetivo()) {
                gruposColetivos.add(grupoSm);
            } else {
                if(apresentarSomenteAtivos){
                    for(SocialMailGrupoParticipanteVO participanteVO : grupoSm.getParticipantes()){
                        if(participanteVO.getCodigoPessoa() != getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo()){
                            if(getFacade().getColaborador().existeColaboradorEmpresaPorCodigoPessoaSituacao(participanteVO.getCodigoPessoa(), 0, true)){
                                gruposSimples.add(grupoSm);
                                continue;
                            }else{

                            }
                        }
                    }
                }else{
                    gruposSimples.add(grupoSm);
                }

            }
        }
    }

    public void setListaAdicionar(List<UsuarioVO> listaAdicionar) {
        this.listaAdicionar = listaAdicionar;
    }

    public List<UsuarioVO> getListaAdicionar() {
        return listaAdicionar;
    }

    public void setValorConsulta(String valorConsulta) {
        this.valorConsulta = valorConsulta;
    }

    public String getValorConsulta() {
        return valorConsulta;
    }

    public void setFotosParticipantes(List<byte[]> fotosParticipantes) {
        this.fotosParticipantes = fotosParticipantes;
    }

    public List<byte[]> getFotosParticipantes() {
        return fotosParticipantes;
    }

    public boolean getCarrossel() {
        return getConversaSelecionada().getParteners().size() > 6;
    }

    public void setListaUsuarios(List<UsuarioVO> listaUsuarios) {
        this.listaUsuarios = listaUsuarios;
    }

    public List<UsuarioVO> getListaUsuarios() {
        return listaUsuarios;
    }

    public boolean getExibirResultados() {
        return exibirResultados;
    }

    public void setExibirResultados(boolean er) {
        exibirResultados = er;
    }

    public boolean getListaVazia() {
        return listaUsuarios.isEmpty();
    }

    public void setListaUsuariosAdicionarConversa(List<UsuarioVO> listaUsuariosAdicionarConversa) {
        this.listaUsuariosAdicionarConversa = listaUsuariosAdicionarConversa;
    }

    public List<UsuarioVO> getListaUsuariosAdicionarConversa() {
        return listaUsuariosAdicionarConversa;
    }

    public void setConsultaAdicionarUsuario(String consultaAdicionarUsuario) {
        this.consultaAdicionarUsuario = consultaAdicionarUsuario;
    }

    public String getConsultaAdicionarUsuario() {
        return consultaAdicionarUsuario;
    }

    public SocialMailVO getAntigoSocialMail() {
        return antigoSocialMail;
    }

    public void setAntigoSocialMail(SocialMailVO antigoSocialMail) {
        this.antigoSocialMail = antigoSocialMail;
    }

    public List<SocialMailGrupoVO> getGruposColetivos() {
        return gruposColetivos;
    }

    public void setGruposColetivos(List<SocialMailGrupoVO> gruposColetivos) {
        this.gruposColetivos = gruposColetivos;
    }

    public List<SocialMailGrupoVO> getGruposSimples() {
        return gruposSimples;
    }

    public void setGruposSimples(List<SocialMailGrupoVO> gruposSimples) {
        this.gruposSimples = gruposSimples;
    }

    public List<SocialMailGrupoVO> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<SocialMailGrupoVO> grupos) {
        this.grupos = grupos;
    }

    public SocialMailGrupoVO getGrupoSelected() {
        return grupoSelected;
    }

    public void setGrupoSelected(SocialMailGrupoVO grupoSelected) {
        this.grupoSelected = grupoSelected;
    }
    public Boolean getVerificarPermissaoSomenteAtivos(){
        try{
            return getFacade().getControleAcesso().verificarPermissaoFuncionalidade("ColaboradorInativoSocialMailing");
        }catch (Exception err){
            return false;
        }
    }
    public Boolean getApresentarSomenteAtivos() throws Exception{
        return apresentarSomenteAtivos;
    }

    public void setApresentarSomenteAtivos(Boolean apresentarSomenteAtivos) {
        this.apresentarSomenteAtivos = apresentarSomenteAtivos;
    }

    public Boolean getGrupoSolicitacao() {
        return !UteisValidacao.emptyNumber(grupoSelected.getSolicitacao_id());
    }

    public Boolean getPermiteEnviarMensagem() {
        try {
            if (getUsuarioLogado().getUsuarioPACTOBR() || getUsuarioLogado().getUsuarioAdminPACTO()) {
                return false;
            }
        } catch (Exception ignored) {
        }

        if (grupoSelected.getParticipantes().size() <= 2) {
            for (SocialMailGrupoParticipanteVO participanteVO : grupoSelected.getParticipantes()) {
                if (participanteVO.getNome().contains("PACTO")) {
                    return false;
                }
            }
        }

        if (UteisValidacao.emptyNumber(grupoSelected.getSolicitacao_id())) {
            return true;
        } else if (grupoSelected.isAtivo()) {
            return true;
        } else {
            return false;
        }
    }

    private boolean verificaUsuarioPermiteBuscarTodas() {
        try {
            UsuarioVO usuarioVO = getUsuarioLogado();
            usuarioVO.setUsuarioPerfilAcessoVOs(getFacade().getUsuarioPerfilAcesso().consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));

            if (usuarioVO.getAdministrador()) {
                return true;
            } else {
                for (Object o : usuarioVO.getUsuarioPerfilAcessoVOs()) {
                    UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
                    if (usuarioPerfilAcesso.getPerfilAcesso().getTipo().equals(PerfilUsuarioEnum.ADMINISTRADOR)
                            && usuarioPerfilAcesso.getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo())) {
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public TipoSocialMailEnum getTipoSocialMail() {
        if (tipoSocialMail == null) {
            tipoSocialMail = TipoSocialMailEnum.CONVERSAS;
        }
        return tipoSocialMail;
    }

    public void setTipoSocialMail(TipoSocialMailEnum tipoSocialMail) {
        this.tipoSocialMail = tipoSocialMail;
    }
}

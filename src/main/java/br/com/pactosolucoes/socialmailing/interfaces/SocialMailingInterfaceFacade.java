package br.com.pactosolucoes.socialmailing.interfaces;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.ce.comuns.ex.NegocioException;
import br.com.pactosolucoes.socialmailing.modelo.ConversaTO;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoVO;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailPartenerVO;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.interfaces.basico.SuperInterface;

public interface SocialMailingInterfaceFacade extends SuperInterface {

    public List<ConversaTO> getConversas(Integer pessoaDestino) throws Exception;

    public void incluir(SocialMailVO mensagem) throws Exception;

    public void incluirPartner(SocialMailVO mensagem) throws Exception;

    public void incluirPartnerChatExistente(SocialMailVO mensagem, ConversaTO conversa) throws Exception;

    public void excluirPartnerChatExistente(SocialMailVO mensagem, String usuario) throws Exception;

    public void consultarMensagensPorGrupo(SocialMailGrupoVO grupo, Integer pessoaLogada) throws Exception;

    public void marcarConversasLidas(Date data, Integer grupo, Integer pessoa) throws Exception;

    public Integer conversasNaoLidas(Integer pessoa, Integer grupo) throws Exception;

    public void montarConversa(final String chave, List<SocialMailPartenerVO> destinatarios, ConversaTO conversa) throws Exception;

    public Integer inserirAvisoAoUsuario(Integer codigoUsuario, String username, String texto, String titulo, Integer codigoExterno, String origem) throws Exception;

    public boolean usuarioPacto(Integer codigoPessoa) throws Exception;

    public Integer solicitacaoUltimaConversa(Integer grupo) throws Exception;

    public String enviarMensagemService(String nomeUsuario, Integer codigoSolicitacao, String texto) throws IOException;

    Integer respostasNaoLidasService(Integer pessoa) throws Exception;

    List<SocialMailGrupoVO> consultarGruposConversaPactoBR(Integer pessoaUsuarioLogado, boolean buscarTodas) throws Exception;

    SocialMailVO ultimaMensagemNaoLidaPactoBR(Integer pessoa) throws Exception;

    List<SocialMailGrupoVO> consultarGruposConversaPactoBRSolicitacoesEmAberto(Integer pessoaUsuarioLogado) throws Exception;

    public boolean existeGruposConversaPactoBRSolicitacoesMesmoFinalizada(Integer pessoaUsuarioLogado) throws Exception;

    public void marcarRespostaCompreendida(Integer socialmail, Integer pessoadestino) throws Exception;

    List<SocialMailGrupoVO> consultarSolicitacoesRespostasNaoCompreeendidas(Integer pessoaUsuarioLogado) throws Exception;

    void enviarMensagemComoRemetentePactoBR(String mensagemTexto,PessoaVO pessoaDestino, boolean enviarSMS) throws Exception;

    void enviarMensagem(String mensagemTexto, PessoaVO pessoaRemetente, PessoaVO pessoaDestino, boolean enviarSMS) throws Exception;

    SocialMailGrupoVO consultarGrupoEntreDuasPessoas(PessoaVO pessoaDonaConversa, PessoaVO pessoaDestino) throws SQLException, NegocioException;

    SocialMailGrupoVO consultarGrupoEntreDuasPessoas(Integer codigoPessoaDonaConversa, Integer codigoPessoaDestino) throws SQLException, NegocioException;
}

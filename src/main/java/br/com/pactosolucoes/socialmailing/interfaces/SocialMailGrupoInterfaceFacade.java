/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.socialmailing.interfaces;

import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoParticipanteVO;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoVO;
import java.util.List;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface SocialMailGrupoInterfaceFacade extends SuperInterface {
    
    public void incluir(SocialMailGrupoVO grupo) throws Exception;
    
    public List<SocialMailGrupoVO> consultarGruposComLimite(Integer pessoa, Integer limite, Boolean coletivos, SocialMailGrupoVO grupoComparar,Boolean somenteAtivos) throws Exception;
    
    public void incluirParticipante(SocialMailGrupoParticipanteVO participante) throws Exception ;
    
    public void alterarNomeGrupo(SocialMailGrupoVO grupo) throws Exception;
    
    public void desativarGrupo(SocialMailGrupoVO grupo) throws Exception;
    
    public void desativarParticipante(SocialMailGrupoParticipanteVO participante) throws Exception;
    
    public List<SocialMailGrupoParticipanteVO> consultarParticipantes(Integer codigoGrupo) throws Exception;

    public SocialMailGrupoVO consultarPorIdSolicitacao(Integer idSolicitacao) throws Exception;
    
}

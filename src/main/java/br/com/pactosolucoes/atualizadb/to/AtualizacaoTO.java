/**
 * 
 */
package br.com.pactosolucoes.atualizadb.to;

import java.io.Serializable;
import java.util.Date;

import br.com.pactosolucoes.atualizadb.enumerador.Resultado;
import br.com.pactosolucoes.comuns.util.Formatador;

/**
 * <AUTHOR>
 */
public class AtualizacaoTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4044997718768113649L;

	private Integer codigo;
	private Integer versao;
	private String script;
	private Resultado resultado;
	private String mensagem;
	private String stackTrace;
	private Date data;
	private String descricao;
	private Integer codigoUsuario;
	private String nomeUsuario;

	private Date dataInicial;
	private Date dataFinal;

	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return codigo;
	}
        public String getResultado_Apresentar(){
            return getResultado().getDescricao();
        }

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo versao.
	 */
	public Integer getVersao() {
		return versao;
	}

	/**
	 * @param versao
	 *            O novo valor de versao.
	 */
	public void setVersao(Integer versao) {
		this.versao = versao;
	}

	/**
	 * @return O campo script.
	 */
	public String getScript() {
		return script;
	}

	/**
	 * @param script
	 *            O novo valor de script.
	 */
	public void setScript(String script) {
		this.script = script;
	}

	/**
	 * @return O campo resultado.
	 */
	public Resultado getResultado() {
		return resultado;
	}

	/**
	 * @param resultado
	 *            O novo valor de resultado.
	 */
	public void setResultado(Resultado resultado) {
		this.resultado = resultado;
	}

	/**
	 * @return O campo mensagem.
	 */
	public String getMensagem() {
		return mensagem;
	}

	/**
	 * @param mensagem
	 *            O novo valor de mensagem.
	 */
	public void setMensagem(String mensagem) {
		this.mensagem = mensagem;
	}

	/**
	 * @return O campo stackTrace.
	 */
	public String getStackTrace() {
		return stackTrace;
	}

	/**
	 * @param stackTrace
	 *            O novo valor de stackTrace.
	 */
	public void setStackTrace(String stackTrace) {
		this.stackTrace = stackTrace;
	}

	/**
	 * @return O campo data.
	 */
	public Date getData() {
		return data;
	}

	/**
	 * @return O campo data formatado.
	 */
	public String getDataFormatada() {
		return Formatador.formatarData(this.getData(), "dd/MM/yyyy - HH:mm:ss");
	}

	/**
	 * @param data
	 *            O novo valor de data.
	 */
	public void setData(Date data) {
		this.data = data;
	}

	/**
	 * @return O campo descricao.
	 */
	public String getDescricao() {
		return descricao;
	}

	/**
	 * @param descricao
	 *            O novo valor de descricao.
	 */
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	/**
	 * @return O campo codigoUsuario.
	 */
	public Integer getCodigoUsuario() {
		return codigoUsuario;
	}

	/**
	 * @param codigoUsuario
	 *            O novo valor de codigoUsuario.
	 */
	public void setCodigoUsuario(Integer codigoUsuario) {
		this.codigoUsuario = codigoUsuario;
	}

	/**
	 * @return O campo nomeUsuario.
	 */
	public String getNomeUsuario() {
		return nomeUsuario;
	}

	/**
	 * @param nomeUsuario
	 *            O novo valor de nomeUsuario.
	 */
	public void setNomeUsuario(String nomeUsuario) {
		this.nomeUsuario = nomeUsuario;
	}

	/**
	 * @return O campo dataInicial.
	 */
	public Date getDataInicial() {
		return dataInicial;
	}

	/**
	 * @param dataInicial
	 *            O novo valor de dataInicial.
	 */
	public void setDataInicial(Date dataInicial) {
		this.dataInicial = dataInicial;
	}

	/**
	 * @return O campo dataFinal.
	 */
	public Date getDataFinal() {
		return dataFinal;
	}

	/**
	 * @param dataFinal
	 *            O novo valor de dataFinal.
	 */
	public void setDataFinal(Date dataFinal) {
		this.dataFinal = dataFinal;
	}

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

interface SqlProcesso {
    String getSqlUpdate();
}

/**
 * <AUTHOR>
 */
public class ReduzirValoresUltimaParcelaContratos {

    private static final double QUANTIDADE_DIAS_REDUZIR = 6;
    private static final double QUANTIDADE_DIAS_MES = 30;
    private Connection connection;

    private ReduzirValoresUltimaParcelaContratos(Connection con) {
        this.connection = con;
    }

    public static void main(String... args) {
        try {
            Connection con = DriverManager.getConnection("************************************************************************", "zillyonweb", "pactodb");

            ReduzirValoresUltimaParcelaContratos processo = new ReduzirValoresUltimaParcelaContratos(con);
            processo.processar();
        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void processar() throws Exception {
        Date dataLimiteContrato = Uteis.getDate("19/01/2020");
        List<ContratoProcessoReduzirValor> contratosAjustar = consultarContratosParaAjuste(dataLimiteContrato);

//        connection.setAutoCommit(false);
        int i = 0;
        for (ContratoProcessoReduzirValor contrato : contratosAjustar) {
            try {
                MovProdutoProcesso movProdutoProcesso = obterMovProduto(contrato);
                Double valorReduzir;
                if (movProdutoProcesso != null && movProdutoProcesso.isValido()) {
                    valorReduzir = Uteis.arredondarForcando2CasasDecimais(((QUANTIDADE_DIAS_REDUZIR / QUANTIDADE_DIAS_MES) * movProdutoProcesso.totalFinal));
                    movProdutoProcesso.valorDesconto = Uteis.arredondarForcando2CasasDecimais(movProdutoProcesso.valorDesconto + valorReduzir);
                    movProdutoProcesso.totalFinal = Uteis.arredondarForcando2CasasDecimais(movProdutoProcesso.totalFinal - valorReduzir);
                    movProdutoProcesso.atualizarMovProdutoModalidadeDescricao();

                    MovProdutoParcelaProcesso movProdutoParcelaProcesso = obterMovProdutoParcela(movProdutoProcesso);
                    if (movProdutoParcelaProcesso != null) {
                        movProdutoParcelaProcesso.valorPago = Uteis.arredondarForcando2CasasDecimais(movProdutoParcelaProcesso.valorPago - valorReduzir);
                        if (movProdutoParcelaProcesso.valorPago < 0) {
                            throw new Exception("DEU RUIM!");
                        }

                    }

                    MovParcelaProcesso movParcelaProcesso = obterMovParcela(movProdutoParcelaProcesso);
                    movParcelaProcesso.valorParcela = Uteis.arredondarForcando2CasasDecimais(movParcelaProcesso.valorParcela - valorReduzir);


                    try (PreparedStatement ps = connection.prepareStatement(movProdutoProcesso.getSqlUpdate())) {
                        ps.setDouble(1, movProdutoProcesso.precoUnitario);
                        ps.setDouble(2, movProdutoProcesso.valorDesconto);
                        ps.setDouble(3, movProdutoProcesso.totalFinal);
                        ps.setString(4, movProdutoProcesso.descricaoMovProdutoModalidade);
                        ps.setInt(5, movProdutoProcesso.codigo);
                        ps.execute();
                    }

                    for (MovProdutoModalidadeProcesso movProdutoModalidadeProcesso : movProdutoProcesso.movProdutosModalidades) {
                        try (PreparedStatement ps = connection.prepareStatement(movProdutoModalidadeProcesso.getSqlUpdate())) {
                            ps.setDouble(1, movProdutoModalidadeProcesso.valor);
                            ps.setDouble(2, movProdutoModalidadeProcesso.movproduto);
                            ps.setDouble(3, movProdutoModalidadeProcesso.modalidade);
                            ps.execute();
                        }
                    }

                    try (PreparedStatement ps = connection.prepareStatement(movParcelaProcesso.getSqlUpdate())) {
                        ps.setDouble(1, movParcelaProcesso.valorParcela);
                        ps.setDouble(2, movParcelaProcesso.codigo);
                        ps.execute();
                    }

                    try (PreparedStatement ps = connection.prepareStatement(movProdutoParcelaProcesso.getSqlUpdate())) {
                        ps.setDouble(1, movProdutoParcelaProcesso.valorPago);
                        ps.setDouble(2, movProdutoParcelaProcesso.codigo);
                        ps.execute();
                    }

                }
                System.out.println(++i + " de " + contratosAjustar.size() + ": Contrato: " + contrato.codigo + " - " + contrato.dataLancamento);
//                connection.commit();
            } catch (Exception ex) {
                ex.printStackTrace();
//                connection.rollback();
            }
        }
    }

    private MovParcelaProcesso obterMovParcela(MovProdutoParcelaProcesso movProdutoParcelaProcesso) throws Exception {
        String sql = "SELECT codigo, valorparcela FROM movparcela WHERE codigo = ?";
        if (movProdutoParcelaProcesso == null) {
            throw new Exception("DEU RUIM NA MOVPARCELA!");
        }
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setInt(1, movProdutoParcelaProcesso.movParcela);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    MovParcelaProcesso mpp = new MovParcelaProcesso();

                    mpp.codigo = rs.getInt("codigo");
                    mpp.valorParcela = rs.getDouble("valorparcela");
                    return mpp;
                }
            }
        }
        return null;
    }

    private MovProdutoParcelaProcesso obterMovProdutoParcela(MovProdutoProcesso movProdutoProcesso) throws Exception {
        String sql = "SELECT codigo, movparcela, valorpago FROM movprodutoparcela WHERE movproduto = ?";
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setInt(1, movProdutoProcesso.codigo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    MovProdutoParcelaProcesso mppp = new MovProdutoParcelaProcesso();

                    mppp.codigo = rs.getInt("codigo");
                    mppp.movParcela = rs.getInt("movparcela");
                    mppp.valorPago = rs.getDouble("valorpago");
                    return mppp;
                }
            }
        }
        return null;
    }

    private MovProdutoProcesso obterMovProduto(ContratoProcessoReduzirValor contrato) throws SQLException {
        String sql = "select mprod.codigo, situacao, precounitario, valordesconto, totalfinal, descricaomovprodutomodalidade\n" +
                "from movproduto mprod\n" +
                "         inner join produto p on mprod.produto = p.codigo and p.tipoproduto = 'PM'\n" +
                "WHERE mprod.contrato = ?\n" +
                "AND mprod.mesreferencia = ?\n" +
                "order by anoreferencia desc, mesreferencia desc\n" +
                "limit 1;\n";


        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setInt(1, contrato.codigo);
            ps.setString(2, contrato.getMesReferenciaReduzir());
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    MovProdutoProcesso mpp = new MovProdutoProcesso();

                    mpp.codigo = rs.getInt("codigo");
                    mpp.situacao = rs.getString("situacao");
                    mpp.precoUnitario = rs.getDouble("precoUnitario");
                    mpp.valorDesconto = rs.getDouble("valorDesconto");
                    mpp.totalFinal = rs.getDouble("totalFinal");
                    mpp.descricaoMovProdutoModalidade = rs.getString("descricaomovprodutomodalidade");
                    return mpp;
                }
            }
        }
        return null;
    }

    private List<ContratoProcessoReduzirValor> consultarContratosParaAjuste(Date dataConsultar) throws Exception {
        List<ContratoProcessoReduzirValor> contratos = new ArrayList<>();
        String sql = "SELECT codigo, datalancamento\n" +
                "FROM contrato\n" +
                "WHERE ? BETWEEN vigenciade AND vigenciaateajustada;";
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setDate(1, Uteis.getDataJDBC(dataConsultar));
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ContratoProcessoReduzirValor contrato = new ContratoProcessoReduzirValor();
                    contrato.codigo = rs.getInt("codigo");
                    contrato.dataLancamento = rs.getDate("datalancamento");
                    contratos.add(contrato);
                }
            }
        }
        return contratos;
    }
}

class ContratoProcessoReduzirValor {
    public Integer codigo;
    public Date dataLancamento;

    public String getMesReferenciaReduzir() {
        Date dataReferencia = Uteis.somarCampoData(dataLancamento, Calendar.MONTH, 5);
        return Uteis.getMesReferenciaData(dataReferencia);
    }
}


class MovParcelaProcesso implements SqlProcesso {
    public Integer codigo;
    public Double valorParcela;

    public String getSqlUpdate() {
        return "UPDATE movparcela\n" +
                "SET valorparcela = ?\n" +
                "WHERE codigo = ?";
    }
}

class MovProdutoParcelaProcesso implements SqlProcesso {
    public Integer codigo;
    public Integer movParcela;
    public Double valorPago;

    public String getSqlUpdate() {
        return "UPDATE movprodutomodalidade\n" +
                "SET valor = ?\n" +
                "WHERE codigo = ?";
    }
}

class MovProdutoModalidadeProcesso implements SqlProcesso {
    public Double valor;
    public Integer movproduto;
    public Integer modalidade;

    public String getSqlUpdate() {
        return "UPDATE movprodutomodalidade\n" +
                "SET valor = ?\n" +
                "WHERE movproduto = ? AND modalidade = ?";
    }
}

class MovProdutoProcesso implements SqlProcesso {

    public Integer codigo;
    public String situacao;
    public Double precoUnitario;
    public Double valorDesconto;
    public Double totalFinal;
    List<MovProdutoModalidadeProcesso> movProdutosModalidades;
    String descricaoMovProdutoModalidade;

    public String getSqlUpdate() {
        return "UPDATE movproduto\n" +
                "SET precounitario = ?, valordesconto = ?, totalFinal = ?, descricaomovprodutomodalidade = ?\n" +
                "WHERE codigo = ?";
    }

    public boolean isValido() {
        return "EA".equals(situacao);
    }

    void atualizarMovProdutoModalidadeDescricao() {
        movProdutosModalidades = new ArrayList<>();

        String[] itemMovProdutoModalidade = descricaoMovProdutoModalidade.split("\\^");
        StringBuilder novaDescricao = new StringBuilder();
        for (String item : itemMovProdutoModalidade) {
            String[] partesItem = item.split(";");

            Integer modalidade = Integer.parseInt(partesItem[0]);
            Double valor = Uteis.arredondarForcando2CasasDecimais(totalFinal / itemMovProdutoModalidade.length);

            novaDescricao.append(modalidade).append(";")
                    .append(partesItem[1]).append(";")
                    .append(valor).append(";")
                    .append(partesItem[3]).append(";")
                    .append(partesItem[4]).append("^");

            MovProdutoModalidadeProcesso movProdutoModalidadeProcesso = new MovProdutoModalidadeProcesso();
            movProdutoModalidadeProcesso.modalidade = modalidade;
            movProdutoModalidadeProcesso.movproduto = this.codigo;
            movProdutoModalidadeProcesso.valor = valor;

            movProdutosModalidades.add(movProdutoModalidadeProcesso);
        }
        descricaoMovProdutoModalidade = novaDescricao.toString();
    }
}


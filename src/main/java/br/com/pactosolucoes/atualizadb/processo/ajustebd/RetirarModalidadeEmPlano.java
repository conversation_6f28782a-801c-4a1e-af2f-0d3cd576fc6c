package br.com.pactosolucoes.atualizadb.processo.ajustebd;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoModalidade;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 21/03/2019
 */
public class RetirarModalidadeEmPlano extends SuperEntidade {

    private ContratoModalidade contratoModalidadeDAO;
    private Log logDAO;


    public RetirarModalidadeEmPlano() throws Exception {
        super();

        try {
            this.contratoModalidadeDAO = new ContratoModalidade(con);
            this.logDAO = new Log(con);
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi possivel inicializar DAOs");
        }
    }

    public List<ContratoVO> consultarContratosRetirarModalidade(int codEmpresa, PlanoVO planoVO, ModalidadeVO modalidadeVO) throws Exception {
        String sql = "SELECT con.*\n" +
                "FROM contrato con\n" +
                "  LEFT JOIN contratomodalidade cm ON con.codigo = cm.contrato AND modalidade = ?\n" +
                "WHERE 1 = 1\n" +
                "      AND con.plano = ?\n" +
                "      AND con.empresa = ?\n" +
                "      AND cm.codigo IS NOT NULL\n" +
                "      AND con.situacao = 'AT';";

        PreparedStatement preparedStatement = con.prepareStatement(sql);
        preparedStatement.setInt(1, modalidadeVO.getCodigo());
        preparedStatement.setInt(2, planoVO.getCodigo());
        preparedStatement.setInt(3, codEmpresa);
        ResultSet rs = preparedStatement.executeQuery();

        return Contrato.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_ROBO, con);
    }

    public void retirarModalidadeContrato(ContratoVO contratoVO, ModalidadeVO modalidadeVO, UsuarioVO usuarioVO) throws Exception {
        ContratoModalidadeVO contratoModalidadeVO = new ContratoModalidadeVO();
        contratoModalidadeVO.setModalidade(modalidadeVO);
        contratoModalidadeVO.setContrato(contratoVO.getCodigo());

        contratoModalidadeDAO.excluirContratoModalidade(contratoVO.getCodigo(), modalidadeVO.getCodigo());
        incluirLog(contratoVO, modalidadeVO, usuarioVO);
    }

    private void incluirLog(ContratoVO contratoVO, ModalidadeVO modalidadeVO, UsuarioVO usuarioVO) throws  Exception {
        LogVO logVO = new LogVO();
        logVO.setChavePrimaria(contratoVO.getCodigo().toString());
        logVO.setPessoa(contratoVO.getPessoa().getCodigo());
        logVO.setNomeEntidade("CONTRATO");
        logVO.setNomeEntidadeDescricao("Configurações");
        logVO.setOperacao("Retirar Modalidade (Processo)");
        logVO.setResponsavelAlteracao(usuarioVO.getNome());
        logVO.setUserOAMD(usuarioVO.getUserOamd());
        logVO.setNomeCampo("Modalidades");
        logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        logVO.setValorCampoAnterior("");
        logVO.setValorCampoAlterado(modalidadeVO.getNome());


        logDAO.incluir(logVO);
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luís Antônio",
        data = "19/08/2024",
        descricao = "Cria Tabela CRM Fase IA",
        motivacao = "PRPI-42")
public class CriarTabelaCrmFaseIAPRPI42 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.configuracaofaseia (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    descricao text NULL,\n" +
                    "    habilitar boolean NOT NULL,\n" +
                    "    fase integer null );", c);

            SuperFacadeJDBC
                    .executarUpdateExecutarProcessos("" +
                            "        CREATE TABLE public.configuracaocrmia (\n" +
                            "                codigo SERIAL PRIMARY KEY,\n" +
                            "                habilitarconfigia BOOLEAN,\n" +
                            "                personalidade text NULL,\n" +
                            "                informacoesAdicionaisAcademia text NULL ); ", c);

            SuperFacadeJDBC
                    .executarUpdate("" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (1, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (14, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (2, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (3, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (11, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (19, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (17, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (7, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (6, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (13, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (18, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (5, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (21, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (20, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (8, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (12, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (4, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (10, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (9, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (15, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (16, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (23, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (22, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (24, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (26, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (25, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (27, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (28, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (29, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (30, '', false);\n" +
                            "INSERT INTO configuracaofaseia (fase, descricao, habilitar) VALUES (31, '', false);\n" +
                            "", c);
        }
    }


}

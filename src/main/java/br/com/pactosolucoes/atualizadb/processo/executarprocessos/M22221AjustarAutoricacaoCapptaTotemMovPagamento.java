package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Maurin <PERSON>",
        data = "19/08/2024",
        descricao = "Ajustar Autorização e NSU de MovPagamento para Totem Cappta",
        motivacao = "M2-2221")
public class M22221AjustarAutoricacaoCapptaTotemMovPagamento implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo, observacao FROM movpagamento \n" +
                    "WHERE (observacao ILIKE '%AUT=%' OR observacao ILIKE '%AUT:%')\n" +
                    "AND LENGTH(autorizacaocartao) > 6\n" +
                    "ORDER BY codigo desc;", c);
            while (rs.next()) {
                String observacao = rs.getString("observacao");
                String autorizacao = "";
                String nsu = "";
                int index = -1;

                if (observacao != null) {
                    if (observacao.contains("AUT=")) {
                        index = observacao.indexOf("AUT=") + 4; // 4 porque AUT= tem 4 caracteres
                    } else if (observacao.contains("AUT:")) {
                        index = observacao.indexOf("AUT:") + 4; // 4 porque AUT: tem 4 caracteres
                    }

                    if (index != -1 && index + 6 <= observacao.length()) {
                        autorizacao = observacao.substring(index, index + 6);
                    }
                }

                if (observacao != null) {
                    if (observacao.contains("(NSU D-TEF   : ")) {
                        index = observacao.indexOf("(NSU D-TEF   : ") + 15; // 15 porque (NSU D-TEF   :  tem 15 caracteres
                    }

                    if (index != -1 && index + 6 <= observacao.length()) {
                        nsu = observacao.substring(index, index + 6);
                    }
                }

                String sql = "UPDATE movpagamento SET autorizacaocartao = '" + autorizacao + "', nsu = '" + nsu + "' WHERE codigo = " + rs.getInt("codigo") + ";";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        }
    }
}

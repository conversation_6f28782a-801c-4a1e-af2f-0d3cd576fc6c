package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "10/09/2024",
        descricao = "Criar indices tabela integração Sesi",
        motivacao = "GC-950")
public class AtualizacaoTicketGC950 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_integracaosesi_codigopessoa ON public.integracaosesi USING btree (codigopessoa);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_integracaosesi_datarequisicao ON public.integracaosesi USING btree (datarequisicao);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_integracaosesi_dataretorno ON public.integracaosesi USING btree (dataretorno);", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Luiz Felipe",
        data = "15/07/2025",
        descricao = "Configuração para definir idade minima.",
        motivacao = "MJ-1240"
)
public class AtualizacaoTicket_MJ_1240 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE ConfiguracaoSistema ADD COLUMN idademinima INT;", c);
        }
    }

}

package br.com.pactosolucoes.atualizadb.processo;

import com.itextpdf.html2pdf.HtmlConverter;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import javax.xml.transform.Result;
import java.io.*;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

public class ImprimirTodosContratos {
    public static void main(String[] args) throws Exception {
        Connection con = DriverManager.getConnection("**************************************************", "postgres", "pactodb");
        String chave = "6db9e9c3446bdd35fa3d47c98c8dd7ad";
        Integer empresa = 1;
        File dirPagosSomenteCartao = new File("C:\\pacto\\contratos\\contornoDoCorpoBetim");
        if (!dirPagosSomenteCartao.exists()){
            dirPagosSomenteCartao.mkdirs();
        }
        String sqlcontratos = "select c2.contrato, c2.contratohtml, cl.matricula, p.nome from contrato c inner join contratotextopadrao c2 on c.codigo = c2.contrato inner join cliente cl on cl.pessoa  = c.pessoa inner join pessoa p on p.codigo = cl.pessoa  where c.codigo in (8338,8352,8274) \n" +
                "order by 1 ";
        imprimircontratos(con, chave, empresa, dirPagosSomenteCartao, sqlcontratos, false);
//        File dirPagosCartaoEOutraForma = new File("C:\\pacto\\contratos\\pagosCartaoEOutraForma");
//        if (!dirPagosCartaoEOutraForma.exists()){
//            dirPagosCartaoEOutraForma.mkdirs();
//        }
//        String contratosPagosCartaoEOutraForma = "select codigo as contrato from contrato c where datalancamento::date  between '2016-01-01' and '2021-01-31' and exists(\n" +
//                "select pag.codigo from movparcela par inner  join pagamentomovparcela pmm on pmm.movparcela = par.codigo inner join movpagamento pag on pag.codigo = pmm.movpagamento  inner join formapagamento fp on fp.codigo = pag.formapagamento where par.contrato = c.codigo and tipoformapagamento in ('CA', 'CD'))\n" +
//                "and  exists(\n" +
//                "select pag.codigo from movparcela par inner  join pagamentomovparcela pmm on pmm.movparcela = par.codigo inner join movpagamento pag on pag.codigo = pmm.movpagamento  inner join formapagamento fp on fp.codigo = pag.formapagamento where par.contrato = c.codigo and tipoformapagamento not in ('CA', 'CD')) \n" +
//                " order by codigo ";
//        imprimircontratos(con, chave, empresa, dirPagosCartaoEOutraForma, contratosPagosCartaoEOutraForma);
//
//        File dirOutraForma = new File("C:\\pacto\\contratos\\pagosOutraForma");
//        if (!dirOutraForma.exists()){
//            dirOutraForma.mkdirs();
//        }
//        String contratosOutraForma = "select codigo as contrato from contrato c where datalancamento::date  between '2016-01-01' and '2021-01-31' and not importacao and not exists(\n" +
//                "select pag.codigo from movparcela par inner  join pagamentomovparcela pmm on pmm.movparcela = par.codigo inner join movpagamento pag on pag.codigo = pmm.movpagamento  inner join formapagamento fp on fp.codigo = pag.formapagamento where par.contrato = c.codigo and tipoformapagamento in ('CA', 'CD'))\n" +
//                " order by codigo ";
//        imprimircontratos(con, chave, empresa, dirOutraForma, contratosOutraForma);
//
//        File dirOutraFormaImportacao = new File("C:\\pacto\\contratos\\pagosOutraFormaImportacao");
//        if (!dirOutraFormaImportacao.exists()){
//            dirOutraFormaImportacao.mkdirs();
//        }
//        String contratosOutraFormaImportacao = "select codigo as contrato from contrato c where datalancamento::date  between '2016-01-01' and '2021-01-31' and importacao and not exists(\n" +
//                "select pag.codigo from movparcela par inner  join pagamentomovparcela pmm on pmm.movparcela = par.codigo inner join movpagamento pag on pag.codigo = pmm.movpagamento  inner join formapagamento fp on fp.codigo = pag.formapagamento where par.contrato = c.codigo and tipoformapagamento in ('CA', 'CD'))\n" +
//                " order by codigo ";
//        imprimircontratos(con, chave, empresa, dirOutraFormaImportacao, contratosOutraFormaImportacao, false);



    }

    private static void imprimircontratos(Connection con, String chave, Integer empresa, File diretorio, String sqlContratos, boolean gerarHmtl) throws Exception {
        ZillyonWebFacade zwDAO = new ZillyonWebFacade(con);
        EmpresaVO empresaVO = zwDAO.getEmpresa().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_TODOS);
        ResultSet rsContratos = SuperFacadeJDBC.criarConsulta(sqlContratos, con);
        File allContratos = new File(diretorio, "AllContratos.html");
        allContratos.createNewFile();
        FileWriter fileWriterAllContratos = new FileWriter(allContratos, false);
        PrintWriter printAllContratos = new PrintWriter(fileWriterAllContratos);
        while(rsContratos.next()){
            try {
                ContratoVO contratoVo;
                ClienteVO cliente;
                String texto ="";
                if(gerarHmtl) {
                    contratoVo = zwDAO.getContrato().consultarPorChavePrimaria(rsContratos.getInt("contrato"), Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);
                    // setar atributos do contrato
                    contratoVo.setMovParcelaVOs(zwDAO.getMovParcela().consultarPorContratoNaoRenegociadaNegociada(contratoVo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    contratoVo.setContratoTextoPadrao(zwDAO.getContratoTextoPadrao().consultarPorCodigoContrato(contratoVo.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                    contratoVo.setPessoa(zwDAO.getPessoa().consultarPorChavePrimaria(contratoVo.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                    contratoVo.setEmpresa(empresaVO);
                    contratoVo.setContratoDuracao(zwDAO.getContratoDuracao().consultarContratoDuracoes(contratoVo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    if (contratoVo.isVendaCreditoTreino()) {
                        contratoVo.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(zwDAO.getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(contratoVo.getContratoDuracao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    }
                    contratoVo.setContratoHorario(zwDAO.getContratoHorario().consultarContratoHorarios(contratoVo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    contratoVo.setContratoModalidadeVOs(zwDAO.getContratoModalidade().consultarContratoModalidades(contratoVo.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
                    contratoVo.setResponsavelContrato(zwDAO.getUsuario().consultarPorChavePrimaria(contratoVo.getResponsavelContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    contratoVo.getPlano().setPlanoTextoPadrao(contratoVo.getContratoTextoPadrao().getPlanoTextoPadrao());
                    // pesquisar pagamentos já efetuados para informar no contrato.
                    List<MovPagamentoVO> pagamentos = zwDAO.getMovPagamento().consultarPagamentoDeUmContrato(contratoVo.getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_TODOS);
                    cliente = zwDAO.getCliente().consultarPorCodigoPessoa(contratoVo.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                    texto = zwDAO.getContratoTextoPadrao().consultarHtmlContrato(contratoVo.getCodigo(), false);
                    //            texto = arranjarImagens(texto, contratoVo, zwDAO, chave);

                    gerarArquivos(contratoVo, texto, diretorio, printAllContratos, cliente);
                } else {
                    contratoVo = new ContratoVO();
                    contratoVo.setCodigo(rsContratos.getInt("contrato"));
                    cliente = new ClienteVO(rsContratos.getString("nome"));
                    cliente.setMatricula(rsContratos.getString("matricula"));
                    texto = rsContratos.getString("contratohtml");
                }
                gerarArquivos(contratoVo, texto, diretorio, printAllContratos, cliente);
            }catch (Exception e){
                Uteis.logar("Erro ao imprimir contrato " +rsContratos.getInt("contrato")+": "+e.getMessage());
            }
        }

        printAllContratos.close();

    }

    private static void gerarArquivos(ContratoVO contratoVo, String texto, File dir, PrintWriter printAllContratos, ClienteVO cliente) {
        try {
            printAllContratos.println(texto);
            printAllContratos.println("<br><br><br><br><hr size=\"10\" color=\"black\"><br><br><br><br>");
            printAllContratos.flush();
            String nomeArquivo = "Matricula-" + cliente.getMatricula() + "-cliente-" +cliente.getPessoa().getNome().replaceAll(" ", "_")+"-contrato-" + contratoVo.getCodigo();

//            File contrato = new File(dir, nomeArquivo+".html");
//            contrato.createNewFile();
//            FileWriter fileWritercontrato = new FileWriter(contrato, false);
//            PrintWriter printcontrato = new PrintWriter(fileWritercontrato);
//            printcontrato.println(texto);
//            printcontrato.flush();
//            printcontrato.close();
//            String url = contrato.toURI().toURL().toString();
//            System.out.println("URL: " + url);

            OutputStream out = new FileOutputStream(dir.getAbsolutePath()+"\\"+nomeArquivo+".pdf");
            HtmlConverter.convertToPdf(texto, out);
            out.close();
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public static String arranjarImagens(String texto, ContratoVO contrato, ZillyonWebFacade zwDAO, String key) throws Exception {
        //criar a imagem temporaria
        String path = "/imagensCRM/email/tmp/";
        String pathFull = Uteis.obterCaminhoWeb() + path;
        String nomeImagem = Uteis.retirarAcentuacaoRegex(contrato.getEmpresa().getNome().replaceAll(" ", ""));
        contrato.getEmpresa().setFotoRelatorio(zwDAO.getEmpresa().obterFoto(key,
                contrato.getEmpresa().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
        UteisEmail.criarImagem(pathFull, contrato.getEmpresa().getFotoRelatorio(), nomeImagem + ".jpg");
//            String path = getUrl() + path + nomeImagem + ".jpg";
        texto = texto.replaceAll("<img[^>]+src\\s*=\\s*['\"]acesso\\?emp*([^'\"]+)['\"][^>]*>", "<img src=\"" + path + "\" />");
        return texto;
    }
}

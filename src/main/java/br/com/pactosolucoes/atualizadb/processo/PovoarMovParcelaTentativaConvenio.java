/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;


public class PovoarMovParcelaTentativaConvenio {

    public static void main(String[] args) {
        try {
            Connection con1 = DriverManager.getConnection("****************************************", "zillyonweb", "pactodb");
//            Conexao.guardarConexaoForJ2SE(con1);
//            preencherMovParcelaTentativaConvenio(con1);
//            preencherCodigoRetornoDescricaoTransacao(Calendario.somarAnos(Calendario.hoje(), -2), Calendario.hoje(), 200, con1);
            preencherCodigoRetornoDescricaoTransacao(null, null, null, null, null, con1);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public static void preencherMovParcelaTentativaConvenio(Connection con) throws Exception {

        Date d1 = Calendario.hoje();
        System.out.println("PovoarMovParcelaTentativaConvenio - Início em : " + new Date());

        ConvenioCobranca convenioCobrancaDao = new ConvenioCobranca(con);
        List<ConvenioCobrancaVO> listaConvenios = convenioCobrancaDao.consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS);


        String sql = "select * from (\n" +
                "select\n" +
                "false as transacao,\n" +
                "0 as codigoTransacaoMovParcela,\n" +
                "0 as tipoTransacao,\n" +
                "r.conveniocobranca,\n" +
                "r.dataregistro,\n" +
                "ri.movparcela,\n" +
                "ri.nrtentativaparcela,\n" +
                "r.empresa\n" +
                "from remessaitem ri\n" +
                "inner join remessa r on r.codigo = ri.remessa\n" +
                "where 1 = 1 \n" +
                "and r.tipo in (2,8,12)\n" +
                "UNION\n" +
                "select \n" +
                "true as transacao,\n" +
                "tm.codigo as codigoTransacaoMovParcela,\n" +
                "t.tipo as tipoTransacao,\n" +
                "0 as conveniocobranca,\n" +
                "t.dataprocessamento as dataregistro,\n" +
                "tm.movparcela,\n" +
                "0 as nrtentativaparcela,\n" +
                "t.empresa\n" +
                "from transacaomovparcela tm\n" +
                "inner join transacao t on t.codigo = tm.transacao\n" +
                "where 1 =1 \n" +
                ") as sql\n" +
                "where movparcela is not null\n" +
                "and dataregistro::date >= '01/12/2017' \n" +
                "order by movparcela, dataregistro";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        Integer nrTentativa = 1;
        Integer ultimaMovparcela = 0;

        while (rs.next()) {
            try {

                Integer movParcela = rs.getInt("movparcela");

                if (movParcela.equals(ultimaMovparcela)) {
                    nrTentativa++;
                } else {
                    nrTentativa = 1;
                    ultimaMovparcela = movParcela;
                }

                boolean transacao = rs.getBoolean("transacao");

                Integer convenioCobranca = null;

                if (transacao) {
                    boolean naoExisteConv = true;
                    Integer tipoTransacao = rs.getInt("tipoTransacao");
                    Integer empresa = rs.getInt("empresa");
                    for (ConvenioCobrancaVO obj : listaConvenios) {
                        if ((obj.getTipo().getTipoRemessa().getTipoTransacao().getId() == tipoTransacao) && obj.getEmpresa().getCodigo().equals(empresa)) {
                            convenioCobranca = obj.getCodigo();
                            naoExisteConv = false;
                            break;
                        }
                    }
                    if (naoExisteConv) {
                        throw new Exception("Nenhum convenio encontrado");
                    }
                } else {
                    convenioCobranca = rs.getInt("conveniocobranca");
                }

                String sqlInsert = "insert into movparcelatentativaconvenio(movparcela,conveniocobranca,nrTentativaParcela) values (" + movParcela + "," + convenioCobranca + "," + nrTentativa + ");";
                SuperFacadeJDBC.executarConsultaUpdate(sqlInsert, con);

                if (transacao) {
                    Integer codigoTransacaoMovParcela = rs.getInt("codigoTransacaoMovParcela");
                    String updateTransacao = "update transacaomovparcela set nrtentativaparcela = " + nrTentativa + " where codigo = " + codigoTransacaoMovParcela + ";";
                    SuperFacadeJDBC.executarConsultaUpdate(updateTransacao, con);
                }

            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }

        Date d2 = Calendario.hoje();
        System.out.println("PovoarMovParcelaTentativaConvenio - Fim em : " + new Date());
        System.out.println("Tempo Total PovoarMovParcelaTentativaConvenio: " + (d2.getTime() - d1.getTime()));
    }

    public static void preencherCodigoRetornoTransacao(Connection con) throws Exception {

        Date d1 = Calendario.hoje();
        System.out.println("PreencherCodigoRetornoTransacao - Início em : " + new Date());

        Transacao transacaoDao = new Transacao(con);
        List<TransacaoVO> transacoes = transacaoDao.consultar("select * from transacao", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        Integer sucesso = 0;
        Integer erro = 0;

        for (TransacaoVO transacaoVO: transacoes) {
            try {
                transacaoDao.atualizarCodigoRetorno(transacaoVO);
                sucesso++;
            }catch (Exception e) {
                erro++;
            }
        }

        System.out.println("Sucesso: " + sucesso);
        System.out.println("Erro: " + erro);

        Date d2 = Calendario.hoje();
        System.out.println("PreencherCodigoRetornoTransacao - Fim em : " + new Date());
        System.out.println("Tempo Total PreencherCodigoRetornoTransacao: " + (d2.getTime() - d1.getTime()));
    }

    public static Integer preencherCodigoRetornoDescricaoTransacao(String codigoRetorno, TipoTransacaoEnum tipoTransacaoEnum,
                                                                   Date inicio, Date fim, Integer limite, Connection con) throws Exception {
        Transacao transacaoDAO;
        try {
            transacaoDAO = new Transacao(con);
            Date d1 = Calendario.hoje();
            Uteis.logarDebug("PreencherCodigoRetornoTransacao - Início em : " + new Date());

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("t.* \n");
            sql.append("from transacao t \n");
            sql.append("where coalesce(t.codigoretornodescricao, '') = '' \n");
            if (!UteisValidacao.emptyString(codigoRetorno)) {
                sql.append("and t.codigoRetorno = '").append(codigoRetorno).append("' \n");
            }
            if (tipoTransacaoEnum != null) {
                sql.append("and t.tipo = ").append(tipoTransacaoEnum.getId()).append(" \n");
            }
            if (inicio != null) {
                sql.append("and t.dataprocessamento::date >= '").append(Uteis.getDataFormatoBD(inicio)).append("' \n");
            }
            if (fim != null) {
                sql.append("and t.dataprocessamento::date <= '").append(Uteis.getDataFormatoBD(fim)).append("' \n");
            }
            sql.append("order by t.codigo desc \n");
            if (!UteisValidacao.emptyNumber(limite)) {
                sql.append("limit ").append(limite).append(" \n");
            }

            List<TransacaoVO> transacoes = transacaoDAO.consultar(sql.toString(), Uteis.NIVELMONTARDADOS_BASICO_TRANSACAO);
            if (UteisValidacao.emptyList(transacoes)) {
                throw new Exception("Nenhuma transação encontrada");
            }

            Integer sucesso = 0;
            Integer erro = 0;
            for (TransacaoVO transacaoVO : transacoes) {
                try {
                    transacaoDAO.atualizarCodigoRetorno(transacaoVO);
                    sucesso++;
                } catch (Exception e) {
                    erro++;
                }
            }

            Uteis.logarDebug("Sucesso: " + sucesso);
            Uteis.logarDebug("Erro: " + erro);

            Date d2 = Calendario.hoje();
            Uteis.logarDebug("PreencherCodigoRetornoTransacao - Fim em : " + new Date());
            Uteis.logarDebug("Tempo Total PreencherCodigoRetornoTransacao: " + (d2.getTime() - d1.getTime()));
            return sucesso;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            transacaoDAO = null;
        }
    }
}

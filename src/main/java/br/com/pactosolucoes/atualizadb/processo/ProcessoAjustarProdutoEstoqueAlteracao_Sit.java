package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Date;

public class ProcessoAjustarProdutoEstoqueAlteracao_Sit {

    public static void main(String... args) {
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"orsi"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            Uteis.debug = true;

            Connection con = new DAO().obterConexaoEspecifica(chave);
            ProcessoAjustarProdutoEstoqueAlteracao_Sit.corrigirProdutosEstoque(con);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void corrigirProdutosEstoque(Connection con) throws Exception {
        try {

            Date dataOperacao;

            String sqlGetProdutosEstoqueSemAlteracao_Sit = "SELECT peq.produto as codProduto, peq.codigo AS codProdutoEstoque FROM produtoestoque peq\n" +
                    "LEFT JOIN produtoestoque_alteracaosit pea ON pea.produtoestoque = peq.codigo\n" +
                    "WHERE pea.codigo IS NULL\n" +
                    "ORDER BY peq.produto;";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlGetProdutosEstoqueSemAlteracao_Sit)) {
                    Uteis.logar("INÍCIO | ProcessoAjustarProdutoEstoqueAlteracao_Sit");
                    while (rs.next()) {
                        int codigoProduto = rs.getInt("codProduto");
                        int codigoProdutoEstoque = rs.getInt("codProdutoEstoque");
                        String sql = "SELECT MIN(foo.dataMenor) AS dataMenor FROM (\n" +
                                "-- BUSCA PRIMEIRO MOVPRODUTO\n" +
                                "SELECT mpd.produto AS produto, MIN(mpd.datalancamento) AS dataMenor, 'MOVPRODUTO' AS origem FROM movproduto mpd\n" +
                                "WHERE mpd.produto IN (" + codigoProduto +")\n" +
                                "GROUP BY mpd.produto\n" +
                                "\n" +
                                "UNION ALL\n" +
                                "\n" +
                                "-- BUSCA PRIMEIRO COMPRA\n" +
                                "SELECT  cit.produto AS produto, MIN(cmp.datacadastro) AS dataMenor, 'COMPRAITEM' AS origem  FROM compraitens cit\n" +
                                "INNER JOIN compra cmp ON cmp.codigo = cit.compra \n" +
                                "WHERE cit.produto IN (" + codigoProduto + ")\n" +
                                "GROUP BY cit.produto\n" +
                                "\n" +
                                "UNION ALL\n" +
                                "\n" +
                                "-- BUSCA PRIMEIRO BALANCO\n" +
                                "SELECT bli.produto AS produto, MIN(blc.datacadastro) AS dataMenor, 'BALANCO' AS origem FROM balanco blc\n" +
                                "INNER JOIN balancoitens bli ON blc.codigo = bli.balanco  \n" +
                                "WHERE bli.produto IN (" + codigoProduto + ")\n" +
                                "GROUP BY bli.produto ) AS foo;";

                        try (Statement stmAux = con.createStatement()) {
                            try (ResultSet rsAux = stmAux.executeQuery(sql)) {
                                while (rsAux.next()) {
                                    dataOperacao = rsAux.getDate("dataMenor");

                                    if (dataOperacao != null) {

                                        dataOperacao = Calendario.subtrairDias(dataOperacao, 1);

                                        Uteis.logar("Criando ProdutoEstoque_AlteracaoSit produto " + codigoProduto + " produtoEstoque " + codigoProdutoEstoque + " na data " + dataOperacao);

                                        String sqlAlt = "INSERT INTO ProdutoEstoque_AlteracaoSit (produtoEstoque, datacadastro, usuario, situacao) VALUES (?, ?, ?, ? )";
                                        PreparedStatement sqlInserirAlt = con.prepareStatement(sqlAlt);
                                        // Incluir a primeira alteração de situaçao do ProdutoEstoque
                                        sqlInserirAlt.setInt(1, codigoProdutoEstoque);
                                        sqlInserirAlt.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataOperacao));
                                        sqlInserirAlt.setInt(3, 2);
                                        sqlInserirAlt.setString(4, "A");
                                        sqlInserirAlt.execute();
                                    }
                                }
                            }
                        }

                    }
                    Uteis.logar("FIM | ProcessoAjustarProdutoEstoqueAlteracao_Sit");
                }
            }

        } catch (Exception ignore) {
            ignore.printStackTrace();
        }

    }

}

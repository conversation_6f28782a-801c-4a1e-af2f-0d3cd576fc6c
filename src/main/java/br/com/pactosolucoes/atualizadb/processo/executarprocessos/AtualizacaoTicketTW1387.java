package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "12/01/2025",
        descricao = "Criar tabela turmamapaequipamentoaparelho e alterar coluna de turma",
        motivacao = "TW-1387 evolução reserva de equipamento")
public class AtualizacaoTicketTW1387  implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarConsulta(
                    "CREATE TABLE turmamapaequipamentoaparelho (" +
                    "   codigo serial PRIMARY KEY, " +
                    "   codigo_aparelhotreino INT NOT NULL, " +
                    "   turma INT, " +
                    "   mapaequipamento TEXT, " +
                    "   CONSTRAINT fk_turma FOREIGN KEY (turma) REFERENCES turma(codigo) " +
                    ");",
                    c
            );
            SuperFacadeJDBC.executarConsulta("ALTER TABLE turma ALTER COLUMN mapaequipamentos TYPE text;", c);
        }
    }

}

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;

import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

public class AtualizacoesCentralEventos  extends SuperEntidade {

	public AtualizacoesCentralEventos() throws Exception {
		super();
	}
	
	public AtualizacoesCentralEventos(Connection con) throws Exception {
		super(con);
	}
	
	public static void atualizaCentralEventos(Connection con) throws Exception{
		SuperFacadeJDBC.executarConsultaUpdate(" ALTER TABLE perfileventoprodutolocacao DROP COLUMN quantidade,"+
						"ADD COLUMN minimo integer NOT NULL default 0, ADD COLUMN maximo integer NOT NULL default 0;", con);
		
		SuperFacadeJDBC.executarConsultaUpdate("ALTER TABLE perfileventoservico DROP COLUMN quantidade,"+
												"ADD COLUMN minimo integer NOT NULL default 0, ADD COLUMN maximo integer NOT NULL default 0;", con);
		
		StringBuilder sql = new StringBuilder();
		sql.append("    ALTER TABLE tipoambiente DROP COLUMN tempoadicionalanteriormin, ");
		sql.append("                ADD COLUMN duracaovisitamin INTEGER NOT NULL DEFAULT 30; ");
		sql.append("    ALTER TABLE eventointeresse DROP COLUMN tipoevento; ");
		sql.append("    DROP TABLE tipoevento; ");
		sql.append("    ALTER TABLE produtolocacaopatrimonio ALTER COLUMN codigo TYPE CHARACTER VARYING(30), ");
		sql.append("                ALTER COLUMN descricao TYPE text; ");
		sql.append("    ALTER TABLE conversa ALTER COLUMN descricao TYPE text; ");
		sql.append("    ALTER TABLE perfileventomodelocontrato ALTER COLUMN descricao TYPE text; ");
		sql.append("    ALTER TABLE perfileventomodeloorcamento ALTER COLUMN descricao TYPE text; ");
		sql.append("    ALTER TABLE perfileventoambientelayout ALTER COLUMN descricao TYPE text;");
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
		sql.append("    CREATE TABLE perfileventomodeloorcamentoimagem ");
		sql.append("                 ( 			  codigo serial NOT NULL                                                , ");
		sql.append("                              perfileventomodeloorcamento INTEGER NOT NULL                          , ");
		sql.append("                              arquivo bytea NOT NULL                                                , ");
		sql.append("                              nomearquivo CHARACTER VARYING(50) NOT NULL                            , ");
		sql.append("                              CONSTRAINT perfileventomodeloorcamentoimagem_pkey PRIMARY KEY (codigo), ");
		sql.append("                              CONSTRAINT fk_perfileventomodeloorcamentoimagem_perfileventomodeloorcamento FOREIGN KEY (perfileventomodeloorcamento) REFERENCES perfileventomodeloorcamento (codigo) MATCH SIMPLE ON ");
		sql.append("                 UPDATE RESTRICT ON DELETE RESTRICT ); ");
		sql.append("          CREATE INDEX ch_perfileventomodeloorcamentoimagem_perfileventomodeloorcamento ");
		sql.append("          ON perfileventomodeloorcamentoimagem ");
		sql.append("          USING        btree ( perfileventomodeloorcamento );");
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		
		sql = new StringBuilder();
		sql.append("    ALTER TABLE negociacaoevento ADD COLUMN horariofinalexibicao TIME without TIME zone; ");
		sql.append("    ALTER TABLE negociacaoeventoperfileventoprodutolocacao ADD COLUMN textolivre text; ");
		sql.append("    ALTER TABLE negociacaoeventoperfileventoservico ADD COLUMN textolivre text; ");
		sql.append("    ALTER TABLE perfileventoprodutolocacao ALTER COLUMN textolivre TYPE text; ");
		sql.append("    ALTER TABLE perfileventoservico ALTER COLUMN textolivre TYPE text; ");
		sql.append("    ALTER TABLE negociacaoeventoperfileventoprodutolocacao ADD COLUMN extra BOOLEAN NOT NULL DEFAULT FALSE;");
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
		sql.append("    CREATE TABLE tipovisita ");
		sql.append("                 ( 			  codigo serial NOT NULL              , ");
		sql.append("                              nome CHARACTER VARYING(100) NOT NULL, ");
		sql.append("                              duracaomin INTEGER NOT NULL         , ");
		sql.append("                              CONSTRAINT tipovisita_pkey PRIMARY KEY (codigo) ); ");
		sql.append("     ");
		sql.append("    INSERT INTO   tipovisita ( ");
		sql.append("                  codigo, ");
		sql.append("                  nome  , ");
		sql.append("                  duracaomin ) VALUES ( ");
		sql.append("                  1              , ");
		sql.append("                  'Visita Rápida', ");
		sql.append("                  15 ) , ( ");
		sql.append("                  2                 , ");
		sql.append("                  'Visita Detalhada', ");
		sql.append("                  60 ) , ( ");
		sql.append("                  3                 , ");
		sql.append("                  'Visita em Aberto', ");
		sql.append("                  0 ); ");
		sql.append("    SELECT SETVAL('tipovisita_codigo_seq', 3); ");
		sql.append("    ALTER TABLE tipoambiente DROP COLUMN duracaovisitamin; ");
		sql.append("    ALTER TABLE agendavisita DROP COLUMN tipovisita   , ");
		sql.append("                ADD COLUMN tipovisita INTEGER NOT NULL, ");
		sql.append("                ADD COLUMN duracaomin INTEGER         , ");
		sql.append("                ADD COLUMN observacao text            , ");
		sql.append("                ADD CONSTRAINT fk_agendavisita_tipovisita FOREIGN KEY (tipovisita) REFERENCES tipovisita (codigo) MATCH SIMPLE ");
		sql.append("    ON ");
		sql.append("    UPDATE RESTRICT ");
		sql.append("    ON ");
		sql.append("    DELETE RESTRICT; ");
		sql.append("    CREATE INDEX ch_agendavisita_tipovisita ");
		sql.append("    ON agendavisita ");
		sql.append("    USING        btree ( tipovisita );");
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
		sql.append(" CREATE TABLE negociacaoeventocontratosituacao ");
		sql.append(" ( codigo serial NOT NULL, ");
		sql.append(" 	abreviacao character(2), ");
		sql.append(" 	descricao character varying(20), ");
		sql.append(" 	CONSTRAINT negociacaoeventocontratosituacao_pkey PRIMARY KEY (codigo)); ");
		sql.append("INSERT INTO negociacaoeventocontratosituacao (codigo, abreviacao, descricao) ");
		sql.append("	VALUES	(1, 'AC', 'Ativo'), ");
		sql.append("			(2, 'IN', 'Inativo'); ");
		sql.append(" SELECT SETVAL('negociacaoeventocontratosituacao_codigo_seq', 2); ");
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
		sql.append(" CREATE TABLE negociacaoeventocontrato ");
		sql.append(" ( ");
		sql.append(" codigo serial NOT NULL, ");
		sql.append(" valordesconto real, ");
		sql.append("  valordescontoespecifico real, ");
		sql.append("  valordescontopercentual real, ");
		sql.append("  nomeevento character varying(50), ");
		sql.append("  somaproduto real, ");
		sql.append("  datapagamento timestamp without time zone, ");
		sql.append("  dataevento timestamp without time zone, ");
		sql.append("  pagarcomboleto boolean, ");
		sql.append("  responsavelcontrato integer NOT NULL, ");
		sql.append("  observacao text, ");
		sql.append("  valorfinal real, ");
		sql.append("  valorbasecalculo real, ");
		sql.append("  vigenciade timestamp without time zone, ");
		sql.append("  vigenciaate timestamp without time zone, ");
		sql.append(" vigenciaateajustada timestamp without time zone, ");
		sql.append(" situacao integer NOT NULL, ");
		sql.append(" perfilevento integer NOT NULL, ");
		sql.append(" pessoa integer NOT NULL, ");
		sql.append(" empresa integer NOT NULL, ");
		sql.append(" dividirprodutosnasparcelas boolean, ");
		sql.append(" CONSTRAINT negociacaoeventocontrato_pkey PRIMARY KEY (codigo), ");
		sql.append(" CONSTRAINT fk_negociacaoeventocontrato_responsavelcontrato FOREIGN KEY (responsavelcontrato) ");
		sql.append(" REFERENCES usuario (codigo) MATCH SIMPLE ");
		sql.append(" ON UPDATE RESTRICT ON DELETE RESTRICT, ");
		sql.append(" CONSTRAINT fk_negociacaoeventocontrato_situacao FOREIGN KEY (situacao) ");
		sql.append(" REFERENCES negociacaoeventocontratosituacao (codigo) MATCH SIMPLE ");
		sql.append("     ON UPDATE RESTRICT ON DELETE RESTRICT, ");
		sql.append("  CONSTRAINT fk_negociacaoeventocontrato_perfilevento FOREIGN KEY (perfilevento) ");
		sql.append("      REFERENCES perfilevento (codigo) MATCH SIMPLE ");
		sql.append("      ON UPDATE RESTRICT ON DELETE RESTRICT, ");
		sql.append("  CONSTRAINT fk_negociacaoeventocontrato_pessoa FOREIGN KEY (pessoa) ");
		sql.append("      REFERENCES pessoa (codigo) MATCH SIMPLE ");
		sql.append("      ON UPDATE RESTRICT ON DELETE RESTRICT, ");
		sql.append("  CONSTRAINT fk_negociacaoeventocontrato_empresa FOREIGN KEY (empresa) ");
		sql.append(" REFERENCES empresa (codigo) MATCH SIMPLE ");
		sql.append("      ON UPDATE RESTRICT ON DELETE RESTRICT ");
		sql.append("); ");
		sql.append("CREATE INDEX ch_negociacaoeventocontrato_responsavelcontrato ");
		sql.append("  ON negociacaoeventocontrato ");
		sql.append("  USING btree ");
		sql.append("  (responsavelcontrato); ");
		sql.append("CREATE INDEX ch_negociacaoeventocontrato_situacao ");
		sql.append("  ON negociacaoeventocontrato ");
		sql.append("  USING btree ");
		sql.append("  (situacao); ");
		sql.append("CREATE INDEX ch_negociacaoeventocontrato_perfilevento ");
		sql.append("  ON negociacaoeventocontrato ");
		sql.append("  USING btree ");
		sql.append(" (perfilevento); ");
		sql.append(" CREATE INDEX ch_negociacaoeventocontrato_pessoa ");
		sql.append(" ON negociacaoeventocontrato ");
		sql.append(" USING btree ");
		sql.append(" (pessoa); ");
		sql.append("CREATE INDEX ch_negociacaoeventocontrato_empresa ");
		sql.append("  ON negociacaoeventocontrato ");
		sql.append("  USING btree ");
		sql.append("  (empresa); ");
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
		sql.append("  ALTER TABLE perfileventoproduto ");
		sql.append("  DROP COLUMN perfilevento; ");
		sql.append("  ALTER TABLE perfilevento ");
		sql.append("  ADD COLUMN produto integer, ");
		sql.append("  ADD CONSTRAINT fk_perfilevento_produto FOREIGN KEY (produto) ");
		sql.append("    REFERENCES perfileventoproduto (codigo) MATCH SIMPLE ");
		sql.append("    ON UPDATE RESTRICT ON DELETE RESTRICT; ");
		sql.append("  CREATE INDEX ch_perfilevento_produto ");
		sql.append("   ON perfilevento ");
		sql.append("  USING btree ");
		sql.append("  (produto); ");
		sql.append("  CREATE TABLE negociacaoeventoimpressaocontrato ");
		sql.append("  (	codigo serial NOT NULL, ");
		sql.append("  	negociacaoevento integer NOT NULL, ");
		sql.append("  	contrato integer NOT NULL, ");
		sql.append("  data timestamp without time zone NOT NULL,  ");
		sql.append("  CONSTRAINT negociacaoeventoimpressaocontrato_pkey PRIMARY KEY (codigo), ");
		sql.append("  CONSTRAINT fk_negociacaoeventoimpressaocontrato_negociacaoevento FOREIGN KEY (negociacaoevento) ");
		sql.append("  REFERENCES negociacaoevento (codigo) MATCH SIMPLE ");
		sql.append("  ON UPDATE RESTRICT ON DELETE RESTRICT, ");
		sql.append("  CONSTRAINT fk_negociacaoeventoimpressaocontrato_contrato FOREIGN KEY (contrato) ");
		sql.append("  REFERENCES perfileventomodelocontrato (codigo) MATCH SIMPLE ");
		sql.append("  ON UPDATE RESTRICT ON DELETE RESTRICT ); ");
		sql.append("  CREATE INDEX ch_negociacaoeventoimpressaocontrato_negociacaoevento ");
		sql.append("    ON negociacaoeventoimpressaocontrato ");
		sql.append("    USING btree ");
		sql.append("    (negociacaoevento); ");
		sql.append("    CREATE INDEX ch_negociacaoeventoimpressaocontrato_contrato  ");
		sql.append("     ON negociacaoeventoimpressaocontrato ");
		sql.append("     USING btree (contrato); ");
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
		sql.append("  CREATE TABLE negociacaoeventoimpressaoorcamento ");
		sql.append("  ( codigo serial NOT NULL,  ");
		sql.append("  	negociacaoevento integer NOT NULL, ");
		sql.append("  orcamento integer NOT NULL, ");
		sql.append("  data timestamp without time zone NOT NULL, ");
		sql.append("  CONSTRAINT negociacaoeventoimpressaoorcamento_pkey PRIMARY KEY (codigo), ");
		sql.append("  CONSTRAINT fk_negociacaoeventoimpressaoorcamento_negociacaoevento FOREIGN KEY (negociacaoevento) ");
		sql.append("  REFERENCES negociacaoevento (codigo) MATCH SIMPLE ");
		sql.append("  ON UPDATE RESTRICT ON DELETE RESTRICT, ");
		sql.append("  CONSTRAINT fk_negociacaoeventoimpressaoorcamento_orcamento FOREIGN KEY (orcamento) ");
		sql.append("  REFERENCES perfileventomodeloorcamento (codigo) MATCH SIMPLE ");
		sql.append("  ON UPDATE RESTRICT ON DELETE RESTRICT ); ");
		sql.append("  CREATE INDEX ch_negociacaoeventoimpressaoorcamento_negociacaoevento ");
		sql.append("  ON negociacaoeventoimpressaoorcamento ");
		sql.append("  USING btree     (negociacaoevento); ");
		sql.append("  CREATE INDEX ch_negociacaoeventoimpressaoorcamento_orcamento ");
		sql.append("  ON negociacaoeventoimpressaoorcamento ");
		sql.append("  USING btree   (orcamento); ");
		sql.append("  ALTER TABLE produtolocacao ");
		sql.append("  ADD COLUMN valor real NOT NULL DEFAULT 0.0; ");
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
		sql.append(" CREATE TABLE negociacaoeventocontratoparcelas ");
		sql.append(" ( contrato integer NOT NULL, ");
		sql.append("   parcela integer NOT NULL, ");
		sql.append(" CONSTRAINT negociacaoeventocontratoparcelas_contrato_fkey FOREIGN KEY (contrato) ");
		sql.append(" REFERENCES negociacaoeventocontrato (codigo) MATCH SIMPLE ");
		sql.append(" ON UPDATE NO ACTION ON DELETE NO ACTION, ");
		sql.append(" CONSTRAINT negociacaoeventocontratoparcelas_parcela_fkey FOREIGN KEY (parcela) ");
		sql.append(" REFERENCES movparcela (codigo) MATCH SIMPLE ");
		sql.append(" ON UPDATE NO ACTION ON DELETE NO ACTION ); ");
		sql.append(" CREATE INDEX ch_negociacaoeventocontratoparcelas_contrato ");
		sql.append(" ON negociacaoeventocontratoparcelas ");
		sql.append(" USING btree  (contrato); ");
		sql.append(" CREATE INDEX ch_negociacaoeventocontratoparcelas_parcela ");
		sql.append(" ON negociacaoeventocontratoparcelas ");
		sql.append(" USING btree  (parcela); ");
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
	    sql.append(" ALTER TABLE negociacaoeventoperfileventoambiente ");
		sql.append(" DROP CONSTRAINT fk_negevtperfevtamb_tipolayout, ");
		sql.append(" ADD CONSTRAINT fk_negevtperfevtamb_tipolayout FOREIGN KEY (tipolayout) ");
		sql.append(" 	REFERENCES perfileventoambientelayout (codigo) MATCH SIMPLE ");
		sql.append(" 	ON UPDATE RESTRICT ON DELETE RESTRICT; ");
	    sql.append(" ALTER TABLE negociacaoeventocontrato ADD COLUMN eventointeresse integer; ");
	    sql.append(" ALTER TABLE negociacaoeventocontrato ADD CONSTRAINT fk_negociacaoeventocontrato_evento FOREIGN KEY (eventointeresse) ");
	    sql.append("       REFERENCES eventointeresse (codigo) MATCH SIMPLE ");
	    sql.append("       ON UPDATE RESTRICT ON DELETE RESTRICT; ");
	    SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
	    sql.append(" ALTER TABLE perfilevento ADD COLUMN permiteoutrosservicos boolean NOT NULL DEFAULT false; ");
	    sql.append(" ALTER TABLE perfileventomodelocontrato DROP COLUMN descricao; ");
	    sql.append(" ALTER TABLE perfileventomodeloorcamento DROP COLUMN descricao; ");
	    sql.append(" ALTER TABLE perfileventomodelocontrato ADD COLUMN descricao TEXT NOT NULL DEFAULT 'Descrição do Contrato'; ");
	    sql.append(" ALTER TABLE perfileventomodeloorcamento ADD COLUMN descricao TEXT NOT NULL DEFAULT 'Descrição do Orçamento'; ");
	    sql.append(" ALTER TABLE perfileventomodelocontrato ALTER descricao DROP DEFAULT; ");
	    sql.append(" ALTER TABLE perfileventomodeloorcamento ALTER descricao DROP DEFAULT; ");
	    sql.append("ALTER TABLE negociacaoeventocontrato DROP COLUMN situacao");
	    SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
		sql.append(" CREATE TABLE negociacaoeventocontratopagamento ");
		sql.append("     ( contrato integer NOT NULL, ");
		sql.append("       movpagamento integer NOT NULL, ");
		sql.append("       CONSTRAINT negociacaoeventocontratopagamento_contrato_fkey FOREIGN KEY (contrato) ");
		sql.append("           REFERENCES negociacaoeventocontrato (codigo) MATCH SIMPLE ");
		sql.append("          ON UPDATE NO ACTION ON DELETE NO ACTION, ");
		sql.append("      CONSTRAINT negociacaoeventocontratopagamento_movpagamento_fkey FOREIGN KEY (movpagamento) ");
		sql.append("         REFERENCES movpagamento (codigo) MATCH SIMPLE ");
		sql.append("         ON UPDATE NO ACTION ON DELETE NO ACTION ); ");
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
		sql.append(" INSERT INTO formacontato(descricao) VALUES ('Pessoal'), ('Telefônico'),('E-mail'); ");
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
		
		sql = new StringBuilder();
		sql.append("ALTER TABLE negociacaoeventoperfileventoambiente ADD COLUMN desconto real DEFAULT 0; ");
		sql.append("ALTER TABLE negociacaoeventoperfileventoambiente ADD COLUMN  tipodesconto integer;");
		
		SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
	}
}

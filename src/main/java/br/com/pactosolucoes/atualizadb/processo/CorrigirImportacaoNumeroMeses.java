package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;

/**
 * Created by Joao Alcides on 23/09/2016.
 */
public class CorrigirImportacaoNumeroMeses {
    static MovProduto movProdutoDao;
    static MovParcela movParcelaDao;
    static MovProdutoParcela movProdutoParcelaDao;
    static ReciboPagamento reciboDao;
    static MovPagamento pagamentoDao;
    static PagamentoMovParcela pagamentoMovparcelaDao;
    static Contrato contratoDao;

    private static Integer CODIGO_FORMA_PAGAMENTO = 3;

    public static void main(String ... args) throws Exception{
        Connection con = DriverManager.getConnection("****************************************", "postgres", "pactodb");
        corrigir(con);
    }

    public static void corrigir(Connection con) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select c.codigo from contratoduracao cd ");
        sql.append("inner join contrato c ON c.codigo = cd.contrato ");
        sql.append("where numeromeses = 11  ");

        movProdutoDao = new MovProduto(con);
        movParcelaDao = new MovParcela(con);
        movProdutoParcelaDao = new MovProdutoParcela(con);
        reciboDao = new ReciboPagamento(con);
        contratoDao = new Contrato(con);
        pagamentoDao = new MovPagamento(con);
        pagamentoMovparcelaDao = new PagamentoMovParcela(con);

        ResultSet rs = con.prepareStatement(sql.toString()).executeQuery();
        Conexao.guardarConexaoForJ2SE(con);

        while(rs.next()){
            ContratoVO contratoVO = contratoDao.consultarPorChavePrimaria(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_ROBO);

            ResultSet rsProduto = SuperFacadeJDBC.criarConsulta("SELECT * FROM movproduto WHERE contrato = " +
                    rs.getInt("codigo")+
                    " and descricao like 'PLANO IMP%'\n" +
                    " ORDER BY anoreferencia DESC, mesreferencia DESC LIMIT 1", con);
            if(rsProduto.next()){
                MovProdutoVO prod = MovProduto.montarDados(rsProduto, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                Date referencia = Uteis.somarMeses(Uteis.getDate("01/" + prod.getMesReferencia()), 1);
                prod.setAnoReferencia(Uteis.getAnoData(referencia));
                prod.setMesReferencia(Uteis.getDataAplicandoFormatacao(referencia, "MM/yyyy"));
                prod.setDescricao("PLANO IMPORTACAO - "+Uteis.getDataAplicandoFormatacao(referencia, "MM/yyyy"));
                movProdutoDao.incluirSemValidar(prod);

                StringBuilder sqlParcela = new StringBuilder();
                sqlParcela.append(" SELECT * FROM movparcela ");
                sqlParcela.append(" WHERE contrato = ").append(rs.getInt("codigo"));
                sqlParcela.append(" AND descricao = 'PARCELA 11'");
                ResultSet rsParcela = con.prepareStatement(sqlParcela.toString()).executeQuery();
                Double valorParcela = 0.0;
                if(rsParcela.next()){
                    System.out.println("Gerando parcela 12 para contrato "+rs.getInt("codigo"));
                    MovParcelaVO movParcelaVO = MovParcela.montarDadosBasico(rsParcela);
                    movParcelaVO.setDataVencimento(Uteis.somarMeses(movParcelaVO.getDataVencimento(), 1));
                    movParcelaVO.setDataCobranca(movParcelaVO.getDataVencimento());
                    movParcelaVO.setDescricao("PARCELA 12");
                    movParcelaVO.setSituacao("PG");
                    valorParcela = movParcelaVO.getValorParcela();
                    movParcelaDao.incluir(movParcelaVO);

                    ReciboPagamentoVO recibo = new ReciboPagamentoVO();
                    recibo.setContrato(contratoVO);
                    recibo.setData(contratoVO.getDataLancamento());
                    recibo.setNomePessoaPagador(contratoVO.getPessoa().getNome());
                    recibo.setPessoaPagador(contratoVO.getPessoa());
                    recibo.setResponsavelLancamento(contratoVO.getResponsavelContrato());
                    recibo.setValorTotal(prod.getTotalFinal());
                    recibo.setEmpresa(contratoVO.getEmpresa());
                    reciboDao.incluir(recibo);

                    MovProdutoParcelaVO prodParcela = new MovProdutoParcelaVO();
                    prodParcela.setMovParcela(movParcelaVO.getCodigo());
                    prodParcela.setMovProduto(prod.getCodigo());
                    prodParcela.setValorPago(prod.getTotalFinal());
                    prodParcela.setReciboPagamento(recibo);
                    movProdutoParcelaDao.incluir(prodParcela);

                    MovPagamentoVO movPagamento = new MovPagamentoVO();
                    movPagamento.setPessoa(contratoVO.getPessoa());
                    movPagamento.setDataLancamento(contratoVO.getDataLancamento());
                    movPagamento.setDataPagamento(movParcelaVO.getDataVencimento());
                    movPagamento.setDataQuitacao(movParcelaVO.getDataVencimento());
                    movPagamento.setResponsavelPagamento(contratoVO.getResponsavelContrato());
                    movPagamento.setValor(prod.getTotalFinal());
                    movPagamento.setValorTotal(prod.getTotalFinal());
                    movPagamento.setEmpresa(contratoVO.getEmpresa());
                    movPagamento.getFormaPagamento().setCodigo(CODIGO_FORMA_PAGAMENTO);
                    movPagamento.setNomePagador(contratoVO.getPessoa().getNome());
                    movPagamento.setMovPagamentoEscolhida(true);
                    movPagamento.setReciboPagamento(recibo);
                    pagamentoDao.incluir(movPagamento);

                    PagamentoMovParcelaVO ppmp = new PagamentoMovParcelaVO();
                    ppmp.setMovPagamento(movPagamento.getCodigo());
                    ppmp.setMovParcela(movParcelaVO);
                    ppmp.setValorPago(prod.getTotalFinal());
                    ppmp.setReciboPagamento(recibo);
                    pagamentoMovparcelaDao.incluir(ppmp);

                }
                Double valorfinal = contratoVO.getValorFinal() + valorParcela;
                Double valorbasecalculo = contratoVO.getValorBaseCalculo() + valorParcela;

                SuperFacadeJDBC.executarConsulta("UPDATE contratoduracao set numeromeses = 12 where contrato = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("UPDATE contrato set valorfinal = "+valorfinal+
                                ", valorbasecalculo  = " +valorbasecalculo+
                                " where codigo = "+rs.getInt("codigo"), con);
            }
        }
        GerarMovProdutoModalidade.gravarDados(con);
    }

}

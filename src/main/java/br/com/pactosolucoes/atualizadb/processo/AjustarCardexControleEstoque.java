/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;


import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Calendar;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class AjustarCardexControleEstoque {
    
    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("*******************************************************", "postgres", "pactodb");
            corrigirDatasCompras(con1);

        } catch (Exception ex) {
            Logger.getLogger(CorrigirDataCompensacaoMovPagamento.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void corrigirDatasCompras(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select c.codigo as codigocompra, c.datacadastro as datacompra , ps.datacadastro as dataentrada from compra c inner join compraitens item on item.compra = c.codigo \n" +
    "inner join produtoestoque pe on pe.produto = item.produto and pe.situacao = 'A'\n" +
    "inner join produtoestoque_alteracaosit ps on ps.produtoestoque = pe.codigo\n" +
    "where c.datacadastro < ps.datacadastro", con);
        
        int cont = 0;
        boolean primeiraExecucao = true;
        try {
            con.setAutoCommit(false);
            while (consulta.next()) {
                if(primeiraExecucao){
                     SuperFacadeJDBC.executarConsultaUpdate(getStringBufferDropTriggers().toString(), con);
                     primeiraExecucao= false;
                }
                Date novadatacompra = Uteis.somarCampoData(consulta.getTimestamp("dataentrada"), Calendar.MINUTE,1);
                SuperFacadeJDBC.executarConsultaUpdate("update compra set datacadastro  ='" + novadatacompra + "' where codigo ="+ consulta.getInt("codigocompra"), con);
                System.out.println(++cont + " - compra " + consulta.getInt("codigocompra") + " foi ajustada");
            }
            if(!primeiraExecucao){
                SuperFacadeJDBC.executarConsultaUpdate(getStringBufferCreateTriggers().toString(), con);
            }
            con.commit();
        }catch(Exception e){
            con.rollback();
            throw e;
        } finally{
            con.setAutoCommit(true);
        }
    }
    
    public static StringBuffer getStringBufferDropTriggers(){
        StringBuffer strDrop = new StringBuffer();
        strDrop.append("DROP TRIGGER tg_ad_alterarestoque_movproduto ON movproduto;\n");
        strDrop.append("DROP TRIGGER tg_ai_alterarestoque_movproduto ON movproduto;\n");
        strDrop.append("DROP TRIGGER tg_bi_alterarestoque_movproduto ON movproduto;\n");
        strDrop.append("DROP TRIGGER tg_bu_alterarestoque_movproduto ON movproduto;\n");
        strDrop.append("DROP TRIGGER tg_ai_alterarestoque_balancoitens ON balancoitens;\n");
        strDrop.append("DROP TRIGGER tg_bd_alterarestoque_balancoitens ON balancoitens;\n");
        strDrop.append("DROP TRIGGER tg_bi_alterarestoque_balancoitens ON balancoitens;\n");
        strDrop.append("DROP TRIGGER tg_bu_alterarestoque_balancoitens ON balancoitens;\n");
        strDrop.append("DROP TRIGGER tg_ai_alterarestoque_compraitens ON compraitens;\n");
        strDrop.append("DROP TRIGGER tg_bd_alterarestoque_compraitens ON compraitens;\n");
        strDrop.append("DROP TRIGGER tg_bi_alterarestoque_compraitens ON compraitens;\n");
        strDrop.append("DROP TRIGGER tg_bu_alterarestoque_compraitens ON compraitens;\n");
        strDrop.append("DROP TRIGGER tg_au_alterarestoque_balanco ON balanco;\n");
        strDrop.append("DROP TRIGGER tg_bd_alterarestoque_balanco ON balanco;\n");
        strDrop.append("DROP TRIGGER tg_bu_alterarestoque_balanco ON balanco;\n");
        strDrop.append("DROP TRIGGER tg_au_alterarestoque_compra ON compra;\n");
        strDrop.append("DROP TRIGGER tg_bd_alterarestoque_compra ON compra;\n");
        strDrop.append("DROP TRIGGER tg_bu_alterarestoque_compra ON compra;\n");
        strDrop.append("DROP TRIGGER tg_bd_alterarestoque_produtoestoque ON produtoestoque;\n");
        strDrop.append("DROP TRIGGER tg_bu_alterarestoque_produtoestoque ON produtoestoque;\n");
        strDrop.append("DROP FUNCTION fn_tg_after_alterarestoque();\n");
        strDrop.append("DROP FUNCTION fn_tg_before_alterarestoque();\n");

        
        
        return strDrop;
        
    }
    
    
    public static StringBuffer getStringBufferCreateTriggers(){
        StringBuffer strCreate = new StringBuffer();
        strCreate.append("CREATE OR REPLACE FUNCTION fn_tg_after_alterarestoque()\n");
        strCreate.append("RETURNS trigger AS\n");
        strCreate.append("$BODY$  \n");
        strCreate.append("  declare   \n");
        strCreate.append("     reg_prod record;  \n");
        strCreate.append("  	reg_itens record;  \n");
        strCreate.append("diferenca integer;  \n");
        strCreate.append("codAux integer;  \n");
        strCreate.append(" 	codEmpresa integer;  \n");
        strCreate.append(" 	dataAd Date;  \n");
        strCreate.append("  BEGIN  \n");
        strCreate.append("      IF (upper(TG_RELNAME) = 'COMPRAITENS') THEN  \n");
        strCreate.append("        IF upper(TG_OP) = 'INSERT' THEN   \n");
        strCreate.append(" 	     select into codEmpresa empresa from compra where codigo = NEW.compra; \n");
        strCreate.append("          update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + new.quantidade where produto = new.produto and empresa = codEmpresa and situacao = 'A';	     \n");
        strCreate.append(" 		 update produtoEstoque set ape = null where produto = new.produto and empresa = codEmpresa;	     \n");
        strCreate.append("        END IF;  \n");
        strCreate.append("      END IF;  \n");
        strCreate.append("     IF (upper(TG_RELNAME) = 'COMPRA') THEN  \n");
        strCreate.append("       IF upper(TG_OP) = 'UPDATE' THEN  \n");
        strCreate.append("         -- remover do estoque os produtos da compra cancelada.  \n");
        strCreate.append("  	     IF (new.cancelada <> old.cancelada) and (new.cancelada = true) THEN  \n");
        strCreate.append("           FOR reg_itens in select * from compraItens where compra = old.codigo  LOOP  \n");
        strCreate.append("                update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) - reg_itens.quantidade where produto = reg_itens.produto and empresa = new.empresa and situacao = 'A' ;	     \n");
        strCreate.append(" 			   update produtoEstoque set ape = null where produto = reg_itens.produto and empresa = new.empresa;	     \n");
        strCreate.append("            END LOOP;		   \n");
        strCreate.append("          END IF; 		   \n");
        strCreate.append("       END IF;  \n");
        strCreate.append("     END IF;  \n");
  
        strCreate.append("     IF (upper(TG_RELNAME) = 'BALANCOITENS') THEN  \n");
        strCreate.append(" 	  select into codEmpresa empresa from balanco where codigo = NEW.balanco; \n");
        strCreate.append("       IF upper(TG_OP) = 'INSERT' THEN   \n");
        strCreate.append("         update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + (new.qtdeBalanco - coalesce(new.qtdeEstoqueAnterior,0)) where produto = new.produto and empresa = codEmpresa and situacao = 'A';	     \n");
        strCreate.append(" 		update produtoEstoque set ape = null where produto = new.produto and empresa = codEmpresa;	     \n");
        strCreate.append("       END IF;  \n");
        strCreate.append("     END IF;  \n");
  
        strCreate.append("     IF (upper(TG_RELNAME) = 'BALANCO') THEN  \n");
        strCreate.append("        IF upper(TG_OP) = 'UPDATE' THEN  \n");
        strCreate.append("          -- Desfazer as alterações de estoque para o balanço cancelado.  \n");
        strCreate.append("  	     IF (new.cancelado <> old.cancelado) and (new.cancelado = true) THEN  \n");
        strCreate.append("             FOR reg_itens in select * from balancoItens where balanco = old.codigo  LOOP  \n");
        strCreate.append("                update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + ((reg_itens.qtdeBalanco - coalesce(reg_itens.qtdeEstoqueAnterior,0)) * -1)  where produto = reg_itens.produto and empresa = new.empresa and situacao = 'A';	     \n");
        strCreate.append("                update produtoEstoque set ape = null  where produto = reg_itens.produto and empresa = new.empresa;	    			    \n");
        strCreate.append("             END LOOP;		   \n");
        strCreate.append("          END IF; 		   \n");
        strCreate.append("        END IF;  \n");
        strCreate.append("     END IF;  \n");
  
  
        strCreate.append("     IF (upper(TG_RELNAME) = 'MOVPRODUTO') THEN  \n");
        strCreate.append("       IF upper(TG_OP) = 'INSERT' THEN  \n");
        strCreate.append("         select into codAux codigo from produtoEstoque where produto = NEW.produto;     \n");
        strCreate.append("         IF (codAux is not null) then -- verifica se o produto tem controle de estoque   \n");
        strCreate.append("            update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) - new.quantidade where produto = new.produto and empresa = new.empresa and situacao = 'A';	     \n");
        strCreate.append(" 		update produtoEstoque set ape = null where produto = new.produto and empresa = new.empresa;	     \n");
        strCreate.append("         END IF;  \n");
        strCreate.append("       END IF;  \n");
        strCreate.append("  \n");
        strCreate.append("       IF upper(TG_OP) = 'DELETE' THEN  \n");
        strCreate.append("         select into codAux codigo from produtoEstoque where produto = old.produto and empresa = old.empresa;    \n");
        strCreate.append("         IF (codAux is not null) then -- verifica se o produto tem controle de estoque   \n");
        strCreate.append("            update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + old.quantidade where produto = old.produto and empresa = old.empresa and situacao = 'A';	     \n");
        strCreate.append(" 		update produtoEstoque set ape = null where produto = old.produto and empresa = old.empresa; \n");
        strCreate.append(" 		-- pesquisar a data em que o produto foi adicionado ao controle de estoque. \n");
        strCreate.append(" 		select into dataAd cast (min(sit.dataCadastro) as date) \n");
        strCreate.append(" 		from produtoEstoque pe \n");
        strCreate.append(" 		inner join ProdutoEstoque_alteracaoSit sit on sit.produtoEstoque = pe.codigo \n");
        strCreate.append(" 		where pe.empresa = old.empresa and pe.produto = old.produto and sit.situacao = 'A'; \n");
        strCreate.append(" 		-- Gravar o cancelamento da venda para ser visualizado no Cardex, somente das vendas que foram realizadas após a data em que o produto foi adicionado ao controle de estoque. \n");
        strCreate.append(" 		IF (old.dataLancamento >= dataAd) THEN \n");
        strCreate.append(" 		    -- Registrar o cancelamento, somente para as vendas que tiveram balanço realizado após a data da venda.  \n");
        strCreate.append(" 		    select into codAux b.codigo   \n");
        strCreate.append(" 		    from balanco b  \n");
        strCreate.append(" 		    inner join balancoItens bi on bi.balanco = b.codigo  \n");
        strCreate.append(" 		    where b.dataCadastro > old.dataLancamento and b.empresa = old.empresa and bi.produto = old.produto;  \n");
        strCreate.append(" 		    IF (codAux is not null) THEN  \n");
        strCreate.append(" 		      insert into exclusaoMovProdutoEstoque(produto, empresa, pessoa, quantidade, dataExclusao, datavenda) values(old.produto, old.empresa, old.pessoa, old.quantidade, now(), old.dataLancamento); \n");
        strCreate.append(" 		    END IF;  \n");
        strCreate.append(" 		END IF;   \n");
        strCreate.append("         END IF;  \n");
        strCreate.append("       END IF;  \n");
        strCreate.append("  	END IF;  \n");
  
  
        strCreate.append("  RETURN NEW;  \n");
        strCreate.append("  END;  \n");
        strCreate.append("  $BODY$\n");
        strCreate.append("  LANGUAGE plpgsql VOLATILE\n");
        strCreate.append("  COST 100;\n");
        strCreate.append("ALTER FUNCTION fn_tg_after_alterarestoque()\n");
        strCreate.append("  OWNER TO zillyonweb;\n");
        strCreate.append("  CREATE OR REPLACE FUNCTION fn_tg_before_alterarestoque()\n");
        strCreate.append("  RETURNS trigger AS\n");
        strCreate.append("$BODY$\n");
        strCreate.append("  DECLARE\n");
        strCreate.append("    reg_prod RECORD;\n");
        strCreate.append("  BEGIN\n");

        strCreate.append("--RAISE EXCEPTION 'TABELA: %  OPERACAO % ',TG_RELNAME, TG_OP ;  \n");

        strCreate.append("    IF (upper(TG_RELNAME) = 'PRODUTOESTOQUE')\n");
        strCreate.append("    THEN\n");

        strCreate.append("      IF upper(TG_OP) = 'DELETE'\n");
        strCreate.append("      THEN\n");
        strCreate.append("        RAISE EXCEPTION 'Não é permitido excluir o produto estoque. Só é permitido alterar a situaçao através da tela de cadastro de produto estoque.!';\n");
        strCreate.append("      END IF;\n");

        strCreate.append("      IF upper(TG_OP) = 'UPDATE'\n");
        strCreate.append("      THEN\n");
        strCreate.append("        IF (new.estoque <> old.estoque)\n");
        strCreate.append("        THEN\n");
        strCreate.append("          IF ((new.ape IS null) OR (new.ape <> 'trigger'))\n");
        strCreate.append("          THEN\n");
        strCreate.append("            RAISE EXCEPTION 'Não é permitido alterar o estoque do produto via sql.!';\n");
        strCreate.append("          END IF;\n");
        strCreate.append("        END IF;\n");
        strCreate.append("      END IF;\n");
        strCreate.append("      IF (new.situacao <> old.situacao)\n");
        strCreate.append("      THEN\n");
        strCreate.append("        IF (new.ape IS null) OR (new.ape <> 'trigger')\n");
        strCreate.append("        THEN\n");
        strCreate.append("          RAISE EXCEPTION 'Não é permitido alterar a situação do produto estoque via sql.!';\n");
        strCreate.append("        END IF;\n");
        strCreate.append("      END IF;\n");
        strCreate.append("    END IF;\n");

        strCreate.append("    IF (upper(TG_RELNAME) = 'COMPRA')\n");
        strCreate.append("    THEN\n");
        strCreate.append("      IF upper(TG_OP) = 'UPDATE'\n");
        strCreate.append("      THEN\n");
        strCreate.append("        IF (old.cancelada = TRUE) AND (new.cancelada = FALSE)\n");
        strCreate.append("        THEN\n");
        strCreate.append("          RAISE EXCEPTION 'Não é possível alterar a situação da compra de cancelada para ativa!';\n");
        strCreate.append("        END IF;\n");
        strCreate.append("      END IF;\n");
        strCreate.append("      IF upper(TG_OP) = 'DELETE'\n");
        strCreate.append("      THEN\n");
        strCreate.append("        RAISE EXCEPTION 'Não é permitido excluir a compra. Só é permitido cancelar a compra.!';\n");
        strCreate.append("      END IF;\n");
        strCreate.append("    END IF;\n");

        strCreate.append("    IF (upper(TG_RELNAME) = 'COMPRAITENS')\n");
        strCreate.append("    THEN\n");
        strCreate.append("      IF upper(TG_OP) = 'UPDATE'\n");
        strCreate.append("      THEN\n");
        strCreate.append("        RAISE EXCEPTION 'Não é permitido alterar os itens da compra. !';\n");
        strCreate.append("      END IF;\n");
        strCreate.append("      IF upper(TG_OP) = 'DELETE'\n");
        strCreate.append("      THEN\n");
        strCreate.append("        RAISE EXCEPTION 'Não é permitido excluir os itens da compra. !';\n");
        strCreate.append("      END IF;\n");
        strCreate.append("    END IF;\n");
        strCreate.append("    IF (upper(TG_RELNAME) = 'BALANCO')\n");
        strCreate.append("THEN\n");
        strCreate.append("      IF upper(TG_OP) = 'UPDATE'\n");
        strCreate.append("      THEN\n");
        strCreate.append("        IF (old.cancelado = TRUE) AND (new.cancelado = FALSE)\n");
        strCreate.append("        THEN\n");
        strCreate.append("          RAISE EXCEPTION 'Não é possível alterar a situação do balanço de cancelado para ativo!';\n");
        strCreate.append("        END IF;\n");
        strCreate.append("      END IF;\n");
        strCreate.append("      IF upper(TG_OP) = 'DELETE'\n");
        strCreate.append("      THEN\n");
        strCreate.append("        RAISE EXCEPTION 'Não é permitido excluir o balanço. Só é permitido cancelar o balanço.!';\n");
        strCreate.append("      END IF;\n");
        strCreate.append("    END IF;\n");

        strCreate.append("    IF (upper(TG_RELNAME) = 'BALANCOITENS')\n");
        strCreate.append("    THEN\n");
        strCreate.append("      IF upper(TG_OP) = 'UPDATE'\n");
        strCreate.append("      THEN\n");
        strCreate.append("        RAISE EXCEPTION 'Não é permitido alterar os itens do balanço. !';\n");
        strCreate.append("      END IF;\n");
        strCreate.append("      IF upper(TG_OP) = 'DELETE'\n");
        strCreate.append("      THEN\n");
        strCreate.append("        RAISE EXCEPTION 'Não é permitido excluir os itens do balanço. !';\n");
        strCreate.append("      END IF;\n");
        strCreate.append("    END IF;\n");

        strCreate.append("    RETURN NEW;\n");
        strCreate.append("  END;\n");
        strCreate.append("  $BODY$\n");
        strCreate.append("  LANGUAGE plpgsql VOLATILE\n");
        strCreate.append("  COST 100;\n");
        strCreate.append("ALTER FUNCTION fn_tg_before_alterarestoque()\n");
        strCreate.append("  OWNER TO zillyonweb;\n");

        strCreate.append("CREATE TRIGGER tg_ad_alterarestoque_movproduto\n");
        strCreate.append("AFTER DELETE\n");
        strCreate.append("  ON movproduto\n");
          strCreate.append("FOR EACH ROW\n");
          strCreate.append("EXECUTE PROCEDURE fn_tg_after_alterarestoque();  \n");

          strCreate.append("CREATE TRIGGER tg_ai_alterarestoque_movproduto\n");
          strCreate.append("AFTER INSERT\n");
        strCreate.append("  ON movproduto\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_after_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_bi_alterarestoque_movproduto\n");
        strCreate.append("  BEFORE INSERT\n");
        strCreate.append("  ON movproduto\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_bu_alterarestoque_movproduto\n");
        strCreate.append("  BEFORE UPDATE\n");
        strCreate.append("  ON movproduto\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_ai_alterarestoque_balancoitens\n");
        strCreate.append("  AFTER INSERT\n");
        strCreate.append("  ON balancoitens\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_after_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_bd_alterarestoque_balancoitens\n");
        strCreate.append("  BEFORE DELETE\n");
        strCreate.append("  ON balancoitens\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_bi_alterarestoque_balancoitens\n");
        strCreate.append("  BEFORE INSERT\n");
        strCreate.append("  ON balancoitens\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_bu_alterarestoque_balancoitens\n");
        strCreate.append("  BEFORE UPDATE\n");
        strCreate.append("  ON balancoitens\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_ai_alterarestoque_compraitens\n");
        strCreate.append("  AFTER INSERT\n");
        strCreate.append("  ON compraitens\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_after_alterarestoque();\n");
  
        strCreate.append("CREATE TRIGGER tg_bd_alterarestoque_compraitens\n");
        strCreate.append("  BEFORE DELETE\n");
        strCreate.append("  ON compraitens\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("CREATE TRIGGER tg_bi_alterarestoque_compraitens\n");
        strCreate.append("BEFORE INSERT\n");
        strCreate.append("  ON compraitens\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_bu_alterarestoque_compraitens\n");
        strCreate.append("  BEFORE UPDATE\n");
        strCreate.append("  ON compraitens\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_au_alterarestoque_balanco\n");
        strCreate.append("  AFTER UPDATE\n");
        strCreate.append("  ON balanco\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_after_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_bd_alterarestoque_balanco\n");
        strCreate.append("  BEFORE DELETE\n");
        strCreate.append("  ON balanco\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");
  
        strCreate.append("CREATE TRIGGER tg_bu_alterarestoque_balanco\n");
        strCreate.append("  BEFORE UPDATE\n");
        strCreate.append("  ON balanco\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_au_alterarestoque_compra\n");
        strCreate.append("  AFTER UPDATE\n");
        strCreate.append("  ON compra\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_after_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_bd_alterarestoque_compra\n");
        strCreate.append("  BEFORE DELETE\n");
        strCreate.append("  ON compra\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_bu_alterarestoque_compra\n");
        strCreate.append("  BEFORE UPDATE\n");
        strCreate.append("  ON compra\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("CREATE TRIGGER tg_bd_alterarestoque_produtoestoque\n");
        strCreate.append("  BEFORE DELETE\n");
        strCreate.append("  ON produtoestoque\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        strCreate.append("  CREATE TRIGGER tg_bu_alterarestoque_produtoestoque\n");
        strCreate.append("  BEFORE UPDATE\n");
        strCreate.append("  ON produtoestoque\n");
        strCreate.append("  FOR EACH ROW\n");
        strCreate.append("  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n");

        return strCreate; 
        
    }
    
    
    
}

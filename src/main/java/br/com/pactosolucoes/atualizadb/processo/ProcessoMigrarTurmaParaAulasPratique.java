package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import edu.emory.mathcs.backport.java.util.Arrays;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.basico.enumerador.TipoToleranciaAulaEnum;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.plano.AlunoHorarioTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Reposicao;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.Turma;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.codehaus.jettison.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ProcessoMigrarTurmaParaAulasPratique {

    private Connection con;
    private UsuarioVO usuarioVO;
    private Usuario usuarioDAO;
    private Turma turmaDAO;
    private HorarioTurma horarioTurmaDAO;
    private Reposicao reposicaoDAO;
    private TurmasServiceImpl turmasService;
    private ControleCreditoTreino controleCreditoTreinoDAO;

    public ProcessoMigrarTurmaParaAulasPratique(Connection con, UsuarioVO usuarioVO) throws Exception {
        this.con = con;
        this.usuarioDAO = new Usuario(con);
        this.turmaDAO = new Turma(con);
        this.horarioTurmaDAO = new HorarioTurma(con);
        this.reposicaoDAO = new Reposicao(con);
        this.turmasService = new TurmasServiceImpl(con);
        this.controleCreditoTreinoDAO = new ControleCreditoTreino(con);
        this.usuarioVO = usuarioVO;
        if (this.usuarioVO == null) {
            this.usuarioVO = usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
    }
    public static void main(String[] args) {
        try {
            String key = "pratiqueonline";
            String codigosTurmas = "26,48,42,18,46,34,43,23,49,45,37,44,16,22,20,17,30,52,56,19,13,21,36"; // 1,2,3

            if (args.length > 0) {
                key = args[0];
                codigosTurmas = args[1];
            }

            DAO dao = new DAO();
            Connection con = dao.obterConexaoEspecifica(key);
            Conexao.guardarConexaoForJ2SE(con);

            ProcessoMigrarTurmaParaAulasPratique processoTransformarTurmaEmAulasTreino = new ProcessoMigrarTurmaParaAulasPratique(con, null);
            processoTransformarTurmaEmAulasTreino.processarTurmas(codigosTurmas);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public List<String> processarTurmas(String codigosTurmas) throws Exception {
        if (UteisValidacao.emptyString(codigosTurmas)) {
            throw new Exception("Codigo de turmas nao informados.");
        }

        List<String> resultados = new ArrayList<>();

        int total = Arrays.asList(codigosTurmas.split(",")).size();
        int atual = 0;

        for (String codigoTurma : codigosTurmas.split(",")) {

            System.out.printf("%d\\%d - Processando turma %s \n", ++atual, total, codigoTurma);
            try {
                con.setAutoCommit(false);

                if (!codigoTurma.matches("\\d+")) {
                    throw new Exception("Codigo de turma invalido: " + codigoTurma);
                }
                TurmaVO turmaVO = turmaDAO.consultarPorChavePrimaria(Integer.parseInt(codigoTurma), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (turmaVO == null || turmaVO.getCodigo().intValue() == 0) {
                    throw new Exception("Turma nao encontrada: cod: " + codigoTurma);
                }
                if (Calendario.menor(turmaVO.getDataFinalVigencia(), Calendario.hoje())) {
                    throw new Exception("Turma nao esta vigente: cod: " + codigoTurma);
                }

                turmaVO.setHorarioTurmaVOs(horarioTurmaDAO.consultarPorTurma(turmaVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                migrarTurmasParaAulas(turmaVO, false);
                migrarReposicoesFuturasTurmaParaAulas(turmaVO);
                inativarTurma(turmaVO);

                con.commit();

                resultados.add(String.format("Cod. Turma: %s - OK", codigoTurma));
            } catch (Exception ex) {
                ex.printStackTrace();
                String msg = String.format("Cod. Turma: %s - ERRO: %s", codigoTurma, ex.getMessage());
                System.out.println(msg);
                resultados.add(msg);
                con.rollback();
            } finally {
                con.setAutoCommit(true);
            }
        }
        return resultados;
    }

    private void inativarTurma(TurmaVO turmaVO) throws Exception {
        Date dataFinalVigencia = turmaVO.getDataFinalVigencia();
        turmaVO.setDataFinalVigencia(Calendario.somarDias(new Date(), -1));
        turmaDAO.alterarSemCommit(turmaVO);
        turmasService.incluirLog(turmaVO.getCodigo().toString(), "TURMA", "Turma", usuarioVO, "ALTERAÇÃO - INATIVAR TURMA - MIGRACAO PARA AULA", "Data Final Vigencia", Calendario.getDataAplicandoFormatacao(turmaVO.getDataFinalVigencia(), "dd/MM/yyyy"), Calendario.getDataAplicandoFormatacao(dataFinalVigencia, "dd/MM/yyyy"), null, null);
    }

    private void migrarReposicoesFuturasTurmaParaAulas(TurmaVO turmaVO) throws Exception {
        String sql = "SELECT r.codigo as reposicao, ht_aula.codigo as horarioturma_aula, ht_aula.turma as turma_aula FROM reposicao r \n" +
                "INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma \n" +
                "INNER JOIN horarioturma ht_aula ON ht_aula.codigohorarioturmaorigem = r.horarioturma \n" +
                "WHERE r.datareposicao::date >= current_date \n" +
                " AND ht.turma = " + turmaVO.getCodigo();

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {
            Integer codReposicao = rs.getInt("reposicao");
            Integer codHorarioTurma = rs.getInt("horarioturma_aula");
            ReposicaoVO reposicaoVO = reposicaoDAO.consultarPorChavePrimaria(codReposicao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            AlunoHorarioTurmaVO alunoHorarioTurma = new AlunoHorarioTurmaVO();
            alunoHorarioTurma.setCliente(reposicaoVO.getCliente().getCodigo());
            alunoHorarioTurma.getHorarioTurma().setCodigo(codHorarioTurma);
            alunoHorarioTurma.setExperimental(false);
            alunoHorarioTurma.setData(reposicaoVO.getDataReposicao());
            alunoHorarioTurma.setUsuario(usuarioVO.getCodigo());
            alunoHorarioTurma.setOrigemSistema(OrigemSistemaEnum.AULA_CHEIA);

            horarioTurmaDAO.incluirAlunoAulaCheia(alunoHorarioTurma, null, null);

            ControleCreditoTreinoVO controleCreditoTreinoVO = controleCreditoTreinoDAO.consultarPorParametros(null, reposicaoVO.getCodigo(), null);
            if(!UteisValidacao.emptyNumber(controleCreditoTreinoVO.getCodigo())){
                controleCreditoTreinoVO.setAulaDesmarcadaVO(new AulaDesmarcadaVO());
                controleCreditoTreinoVO.setReposicaoVO(new ReposicaoVO());
                controleCreditoTreinoVO.setDescricaoAulaMarcada(String.format("%s - %s - %s - %s às %s ",
                        turmaVO.getModalidade().getNome(),
                        Calendario.getDataAplicandoFormatacao(alunoHorarioTurma.getData(), "dd/MM/yyyy"),
                        alunoHorarioTurma.getHorarioTurma().getDiaSemana_Apresentar   (),
                        alunoHorarioTurma.getHorarioTurma().getHoraInicial(),
                        alunoHorarioTurma.getHorarioTurma().getHoraFinal()));
                controleCreditoTreinoDAO.alterarSemCommit(controleCreditoTreinoVO, null,null);
            }
            reposicaoDAO.excluirSemValidarPermissao(reposicaoVO);

            gerarLogs(alunoHorarioTurma, reposicaoVO);
        }
    }

    private void gerarLogs(AlunoHorarioTurmaVO alunoHorarioTurmaVO, ReposicaoVO reposicaoVO) {
        JSONObject json = new JSONObject();
        json.put("reposicaoorigem", reposicaoVO.getCodigo());
        json.put("alunohorarioturma", alunoHorarioTurmaVO.getCodigo());

        String codigo = alunoHorarioTurmaVO.getHorarioTurma().getCodigo() + "_" + Calendario.getDataAplicandoFormatacao(alunoHorarioTurmaVO.getData(), "dd/MM/yyyy");
        turmasService.incluirLog(codigo,
                "ALUNO_AULA_COLETIVA_MIGRAR_TURMAS_PARA_AULA",
                "Aluno marcado",
                usuarioVO,
                "INCLUSÃO",
                "Marcação de aula",
                json.toString(),
                "",
                0,
                alunoHorarioTurmaVO.getCliente());
    }

    private void migrarTurmasParaAulas(TurmaVO turmaVO, boolean comNivel) throws Exception {
        if (turmaVO.getAulaColetiva()) {
            throw new Exception("Turma já é do tipo aula coletiva: cod: " + turmaVO.getCodigo());
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT * FROM turma WHERE codigoTurmaOrigem = " + turmaVO.getCodigo(), con);
        if (rs.next()) {
            throw new Exception(String.format("Turma já foi migrada para aula: cod turma: %d -> cod. aula: %d", turmaVO.getCodigo(), rs.getInt("codigo")));
        }

        Map<String, List<HorarioTurmaVO>> mapaHorariosTurmaAgrupado = new HashMap<>();
        Map<Integer, HorarioTurmaVO> mapaHorarioTurma = new HashMap<>();
        turmaVO.getHorarioTurmaVOs().forEach(ht -> mapaHorarioTurma.put(ht.getCodigo(), ht));

        String sql = "SELECT \n" +
                " coalesce(ht.professor,0) as professor,\n" +
                " coalesce(ht.ambiente,0) as ambiente,\n" +
                " coalesce(ht.nrmaximoaluno,0) as nrmaximoaluno,\n" +
                " ht.horainicial,\n" +
                " ht.horafinal,\n" +
                " array_to_string(array_agg(DISTINCT (ht.nivelturma)),',') AS niveis,\n" +
                " array_to_string(array_agg(DISTINCT (ht.diasemana)),';') AS dias,\n" +
                " array_to_string(array_agg(ht.codigo),';') AS horarios\n" +
                "FROM horarioturma ht\n" +
                " WHERE ht.situacao = 'AT'\n" +
                " AND ht.turma = " + turmaVO.getCodigo() + "\n" +
                " GROUP BY 1,2,3,4,5";

        ResultSet rsTurmasHorarios = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rsTurmasHorarios.next()) {
            String codigosHorarios = rsTurmasHorarios.getString("horarios");
            String dias = rsTurmasHorarios.getString("dias");
            String niveis = rsTurmasHorarios.getString("niveis");
            String key = String.format("%d-%d-%d-%s-%s", rsTurmasHorarios.getInt("professor"),
                    rsTurmasHorarios.getInt("ambiente"), rsTurmasHorarios.getInt("nrmaximoaluno"), niveis, dias);
            for (String codHorarioTurma: codigosHorarios.split(";")) {
                HorarioTurmaVO ht = mapaHorarioTurma.get(Integer.parseInt(codHorarioTurma));

                if (mapaHorariosTurmaAgrupado.get(key) == null) {
                    mapaHorariosTurmaAgrupado.put(key, new ArrayList<>());
                }

                mapaHorariosTurmaAgrupado.get(key).add(ht);
            }
        }

        for (String key : mapaHorariosTurmaAgrupado.keySet()) {
            Integer professor = Integer.parseInt(key.split("-")[0]);
            Integer ambiente = Integer.parseInt(key.split("-")[1]);
            Integer capacidade = Integer.parseInt(key.split("-")[2]);
            String niveis = key.split("-")[3];
            String dias = key.split("-")[4];

            List<DiaSemana> listDias = new ArrayList<>();
            for (String dia : dias.split(";")) {
                listDias.add(DiaSemana.getDiaSemana(dia));
            }
            Ordenacao.ordenarLista(listDias, "numeral");
            dias = listDias.stream().map(DiaSemana::getCodigo).collect(Collectors.joining(";"));

            List<HorarioTurmaVO> horarioTurmaVOS = mapaHorariosTurmaAgrupado.get(key);

            TurmaVO aulaTreino = turmaVO.clone();
            aulaTreino.setHorarioTurmaVOs(new ArrayList<>());
            aulaTreino.setAulaColetiva(true);
            aulaTreino.setUsuario(usuarioVO.getCodigo());
            aulaTreino.setProfessor(professor);
            aulaTreino.setAmbiente(ambiente);
            aulaTreino.setCapacidade(capacidade);
            aulaTreino.setTolerancia(turmaVO.getMinutosAposInicioApp());
            aulaTreino.setTipoTolerancia(TipoToleranciaAulaEnum.APOS_INICIO.getCodigo());
            if (comNivel) {
                aulaTreino.setNiveis(niveis);
            }
            aulaTreino.setDias(dias);

            // remover configs turma que não existem na aula
            aulaTreino.setBloquearMatriculasAcimaLimite(false);
            aulaTreino.setBloquearReposicaoAcimaLimite(false);
            aulaTreino.setMonitorada(false);
            aulaTreino.setMinutosAntecedenciaDesmarcarAula(0);
            aulaTreino.setMinutosAntecedenciaMarcarAula(0);
            aulaTreino.setPermitirAulaExperimental(false);
            aulaTreino.setValidarRestricoesMarcacao(false);
            aulaTreino.setPermiteAlunoOutraEmpresa(false);

            List<String> horariosDescricoes = new ArrayList<>();
            horarioTurmaVOS.forEach(ht -> {
                String h = ht.getHoraInicial() + " - " + ht.getHoraFinal();
                if (!horariosDescricoes.contains(h)) {
                    horariosDescricoes.add(h);
                }
            });

            Collections.sort(horariosDescricoes);
            aulaTreino.setHorarios(horariosDescricoes.stream().collect(Collectors.joining(";")));

            turmaDAO.incluir(aulaTreino);
            SuperFacadeJDBC.executarUpdate("UPDATE turma SET codigoTurmaOrigem = " + turmaVO.getCodigo()
                    + " WHERE codigo = " + aulaTreino.getCodigo(), con);

            for (HorarioTurmaVO ht : horarioTurmaVOS) {
                HorarioTurmaVO novoHorario = ht.clone();
                novoHorario.setTurma(aulaTreino.getCodigo());
                horarioTurmaDAO.incluir(novoHorario);
                SuperFacadeJDBC.executarUpdate("UPDATE horarioturma SET codigoHorarioTurmaOrigem = " + ht.getCodigo()
                        + " WHERE codigo = " + novoHorario.getCodigo(), con);
            }
            turmasService.alterarPontuacaoItenCampanhaClubeVantagens(aulaTreino, true);
            turmasService.incluirLogAulaColetivaGravar(aulaTreino);

        }
    }
}

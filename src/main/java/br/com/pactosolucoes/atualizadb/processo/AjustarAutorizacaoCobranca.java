package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * <AUTHOR>
 */
public class AjustarAutorizacaoCobranca {
    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");
            Integer codigoEmpresa = 1;

            Integer codigoConvenioCobrancaAnterior = 1;
            TipoAutorizacaoCobrancaEnum tipoAutorizacaoConvenioCobrancaAnterior = TipoAutorizacaoCobrancaEnum.CARTAOCREDITO;

            Integer codigoConvenioCobrancaNovo = 2;
            boolean apenasComCartaoCompleto = false;

            List<OperadorasExternasAprovaFacilEnum> operadoras = new ArrayList<OperadorasExternasAprovaFacilEnum>();
            operadoras.add(OperadorasExternasAprovaFacilEnum.MASTERCARD);
            operadoras.add(OperadorasExternasAprovaFacilEnum.VISA);

            ajustarAutorizacaoCobrancaCliente(con1, codigoEmpresa, codigoConvenioCobrancaAnterior, tipoAutorizacaoConvenioCobrancaAnterior, codigoConvenioCobrancaNovo, operadoras, apenasComCartaoCompleto);
            ajustarAutorizacaoCobrancaColaborador(con1, codigoEmpresa, codigoConvenioCobrancaAnterior, tipoAutorizacaoConvenioCobrancaAnterior, codigoConvenioCobrancaNovo, operadoras);

        } catch (Exception ex) {
            Logger.getLogger(AjustarAutorizacaoCobranca.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarAutorizacaoCobrancaCliente(Connection con, Integer codigoEmpresa, Integer codigoConvenioCobrancaAnterior,
                                                         TipoAutorizacaoCobrancaEnum tipoAutorizacaoConvenioCobrancaAnterior,
                                                         Integer codigoConvenioCobrancaNovo, List<OperadorasExternasAprovaFacilEnum> operadoras, boolean apenasComCVV) throws Exception {

        if (tipoAutorizacaoConvenioCobrancaAnterior == null) {
            throw new Exception("Necessário informar o TipoAutorizacaoCobrancaAnterior");
        }

        //limpar caso seja dessas autorizações de cobrança já que não utilizam
        if (tipoAutorizacaoConvenioCobrancaAnterior.equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO) ||
                        tipoAutorizacaoConvenioCobrancaAnterior.equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
            operadoras = new ArrayList<>();
        }

        StringBuilder sbOperadoras = new StringBuilder();
        for (OperadorasExternasAprovaFacilEnum operadora : operadoras) {
            sbOperadoras.append(operadora.getId()).append(",");
        }
        if (sbOperadoras.length() > 0) {
            sbOperadoras.deleteCharAt(sbOperadoras.length() - 1);
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("acc.codigo as codigo_autorizacao \n");
        sql.append("FROM autorizacaocobrancacliente acc \n");
        sql.append("LEFT JOIN conveniocobranca cc ON acc.conveniocobranca = cc.codigo \n");
        sql.append("INNER JOIN conveniocobrancaempresa cce ON acc.conveniocobranca = cce.conveniocobranca \n");
        sql.append("WHERE acc.ativa \n");
        sql.append("AND acc.conveniocobranca = ").append(codigoConvenioCobrancaAnterior).append(" \n");
        sql.append("AND cce.empresa = ").append(codigoEmpresa).append(" \n");
        sql.append("AND acc.tipoautorizacao  = ").append(tipoAutorizacaoConvenioCobrancaAnterior.getId()).append(" \n");

        if (tipoAutorizacaoConvenioCobrancaAnterior.equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            sql.append(" AND coalesce(acc.tokenaragorn,'') <> '' \n");
        }

        if (sbOperadoras.length() > 0) {
            sql.append("AND acc.operadoracartao IN (").append(sbOperadoras.toString()).append(") \n");
        }
        if (apenasComCVV) {
            sql.append("AND coalesce(acc.cvv,'') <> '' \n");
            sql.append("AND coalesce(acc.nometitularcartao,'') <> '' \n");
        }

        ResultSet consulta = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (consulta.next()) {
            String sqlUpdate = "UPDATE autorizacaocobrancacliente SET conveniocobranca = ? WHERE codigo = ?;";
            PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate);

            sqlAlterar.setInt(1, codigoConvenioCobrancaNovo);
            sqlAlterar.setInt(2, consulta.getInt("codigo_autorizacao"));
            sqlAlterar.execute();
        }
    }

    public static void ajustarAutorizacaoCobrancaColaborador(Connection con, Integer codigoEmpresa, Integer codigoConvenioCobrancaAnterior,
                                                             TipoAutorizacaoCobrancaEnum tipoAutorizacaoConvenioCobrancaAnterior,
                                                             Integer codigoConvenioCobrancaNovo, List<OperadorasExternasAprovaFacilEnum> operadoras) throws Exception {

        if (tipoAutorizacaoConvenioCobrancaAnterior == null) {
            throw new Exception("Necessário informar o TipoAutorizacaoCobrancaAnterior");
        }

        //limpar caso seja dessas autorizações de cobrança já que não utilizam
        if (tipoAutorizacaoConvenioCobrancaAnterior.equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO) ||
                        tipoAutorizacaoConvenioCobrancaAnterior.equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
            operadoras = new ArrayList<>();
        }

        StringBuilder sbOperadoras = new StringBuilder();
        for (OperadorasExternasAprovaFacilEnum operadora : operadoras) {
            sbOperadoras.append(operadora.getId()).append(",");
        }
        if (sbOperadoras.length() > 0) {
            sbOperadoras.deleteCharAt(sbOperadoras.length() - 1);
        }

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("acc.codigo as codigo_autorizacao \n");
        sql.append("FROM autorizacaocobrancacolaborador acc \n");
        sql.append("LEFT JOIN conveniocobranca cc ON acc.conveniocobranca = cc.codigo \n");
        sql.append("INNER JOIN conveniocobrancaempresa cce ON acc.conveniocobranca = cce.conveniocobranca \n");
        sql.append("WHERE acc.ativa \n");
        sql.append("AND acc.conveniocobranca = ").append(codigoConvenioCobrancaAnterior).append(" \n");
        sql.append("AND cce.empresa = ").append(codigoEmpresa).append(" \n");
        sql.append("AND acc.tipoautorizacao  = ").append(tipoAutorizacaoConvenioCobrancaAnterior.getId()).append(" \n");

        if (tipoAutorizacaoConvenioCobrancaAnterior.equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            sql.append(" AND coalesce(acc.tokenaragorn,'') <> '' \n");
        }

        if (sbOperadoras.length() > 0) {
            sql.append("AND acc.operadoracartao IN (").append(sbOperadoras.toString()).append(") \n");
        }

        ResultSet consulta = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (consulta.next()) {
            String sqlUpdate = "UPDATE autorizacaocobrancacolaborador SET conveniocobranca = ? WHERE codigo = ?;";
            PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdate);

            sqlAlterar.setInt(1, codigoConvenioCobrancaNovo);
            sqlAlterar.setInt(2, consulta.getInt("codigo_autorizacao"));
            sqlAlterar.execute();
        }
    }
}

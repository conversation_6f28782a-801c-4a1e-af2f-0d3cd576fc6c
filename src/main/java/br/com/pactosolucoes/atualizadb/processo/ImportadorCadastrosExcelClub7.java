package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoTelefone;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import importador.LeitorExcel;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.hssf.usermodel.HSSFRow;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;


/*
 * Created by Luiz Felipe on 12/08/2018.
 */
public class ImportadorCadastrosExcelClub7 {

    private static Integer naoImportado = 0;

    public static void main(String[] args) throws SQLException {
        System.out.println("Início em : " + new Date());

        Connection con = DriverManager.getConnection("***********************************************", "zillyonweb", "pactodb");
        con.setAutoCommit(true);
        try {

            Conexao.guardarConexaoForJ2SE(con);
            SuperFacadeJDBC.executarConsultaUpdate("alter table endereco alter column  endereco TYPE character varying(255);", con);
            importarClientes("C:\\PactoJ\\club7_importacao.xls", 1, con);

        } catch (Exception e) {
            con.setAutoCommit(true);
            e.printStackTrace();
        } finally {
            con.setAutoCommit(true);
        }
        System.out.println("Total Alunos não importados: " + naoImportado);
        System.out.println("Fim em : " + new Date());
    }


    private static void importarClientes(String caminhoArquivo, Integer empresaImportar, Connection con) throws Exception {
        List<HSSFRow> linhas = LeitorExcel.lerLinhas(caminhoArquivo);

        int i = 1;
        int qtdTotal = linhas.size();
        for (HSSFRow linha : linhas) {
            System.out.println("Importando...  Atual: " + i + " Total: " + qtdTotal);
            montarExcel(linha, empresaImportar, con);
            i++;
        }
    }

    private static void montarExcel(HSSFRow linha, Integer empresaImportar, Connection con) {
        String idExternoTeste = "";
        try {

            ZillyonWebFacade facade = new ZillyonWebFacade(con);
            Cliente clienteDao = new Cliente(con);
            Cidade cidadeDao = new Cidade(con);
            Empresa empresaDao = new Empresa(con);
            ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
            Colaborador colaboradorDao = new Colaborador(con);
//            Usuario usuarioDao = new Usuario(con);

            Number idExterno = LeitorExcel.obterNumero(linha, 0);
            idExternoTeste = idExterno.toString();
            String nome = LeitorExcel.obterString(linha, 1);
            String sexo = LeitorExcel.obterString(linha, 2);
            Date data_nascimento = LeitorExcel.obterDataEspecifico(linha, 3);
            String endereco = LeitorExcel.obterString(linha, 4);
            String telefones = LeitorExcel.obterString(linha, 5);
//            String celular = LeitorExcel.obterString(linha, 6);
            String email = LeitorExcel.obterString(linha, 7);
            String cep = LeitorExcel.obterString(linha, 8);
            String cpf = LeitorExcel.obterString(linha, 9);
            String rg = LeitorExcel.obterString(linha, 10);

            //CIDADE FIXO
            CidadeVO cidadeClienteVO = cidadeDao.consultarPorNomeCidadeSiglaEstado("BRASILIA", "DF");

            //EMPRESA
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresaImportar, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            //CONFIGURACAO SISTEMA
            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            //CONSULTOR
            ColaboradorVO consultor = colaboradorDao.consultarPorNomeColaborador("PACTO - M", empresaImportar, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            //PESSOA
            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
            pessoaVO.setCategoriaPessoa(TipoPessoa.FISICA);
            pessoaVO.setNome(nome);
            pessoaVO.setSexo(sexo);
            pessoaVO.setCfp(cpf);
            pessoaVO.setRg(rg);
            pessoaVO.setDataCadastro(Calendario.hoje());
            pessoaVO.setDataNasc(data_nascimento);
            pessoaVO.setCidade(cidadeClienteVO);
            pessoaVO.setEstadoVO(cidadeClienteVO.getEstado());
            pessoaVO.setPais(cidadeClienteVO.getPais());

            //ENDERECO
            if (!UteisValidacao.emptyString(endereco)) {
                EnderecoVO enderecoVO = new EnderecoVO();
                enderecoVO.setEndereco(endereco);
                enderecoVO.setNumero("");
                enderecoVO.setCep(cep);
                enderecoVO.setComplemento("");
                enderecoVO.setBairro("");
                pessoaVO.adicionarObjEnderecoVOs(enderecoVO);
            }

            //EMAIL
            try {
                if (!UteisValidacao.emptyString(email)) {
                    EmailVO emailVO = new EmailVO();
                    emailVO.setEmail(email);
                    pessoaVO.adicionarObjEmailVOs(emailVO);
                }
            } catch (Exception e) {

            }

            if (!UteisValidacao.emptyString(telefones)) {
                String residencial = "";
                String celular = "";

                for (String tel : telefones.split(",")) {
                    if (tel.toUpperCase().contains("CELUL")) {
                        celular = tel.toUpperCase().replace("CELULAR", "");
                    } else if (tel.toUpperCase().contains("RESIDEN")) {
                        residencial = tel.toUpperCase().replace("RESIDENCIAL", "");
                    }
                }

                //TELEFONE RESIDENCIAL
                if (!UteisValidacao.emptyString(residencial)) {
                    TelefoneVO telefoneResiVO = new TelefoneVO();
                    telefoneResiVO.setTipoTelefone(TipoTelefone.RESIDENCIAL.getCodigo());
                    telefoneResiVO.setNumero(Formatador.removerMascara(residencial));
                    pessoaVO.adicionarObjTelefoneVOs(telefoneResiVO);
                }

                //TELEFONE CELULAR
                if (!UteisValidacao.emptyString(celular)) {
                    TelefoneVO telefoneCelularVO = new TelefoneVO();
                    telefoneCelularVO.setTipoTelefone(TipoTelefone.CELULAR.getCodigo());
                    telefoneCelularVO.setNumero(Formatador.removerMascara(celular));
                    pessoaVO.adicionarObjTelefoneVOs(telefoneCelularVO);
                }
            }

            //CLIENTE
            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setMatriculaExterna(idExterno.longValue());
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setEmpresa(empresaVO);
            clienteVO.setSituacao(SituacaoClienteEnum.VISITANTE.getCodigo());
            clienteVO.setMatricula(""); //APAGAR A MATRICULA PARA O SISTEMA GERAR UMA NOVA

            //CLIENTE - VINCULO
            VinculoVO vinculo = new VinculoVO();
            vinculo.setColaborador(consultor);
            vinculo.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
            vinculo.setCliente(clienteVO);
            clienteVO.getVinculoVOs().add(vinculo);

            clienteDao.gerarNumeroMatricula(clienteVO, empresaVO, configuracaoSistemaVO); //gerar a matricula do aluno
            clienteDao.incluirClienteSimplificadoImportacao(clienteVO); //incluir aluno.. pessoa... etc...
            clienteDao.atualizarMatriculaAluno(clienteVO.getCodigoMatricula()); //atualizar tabela que registra o ultimo numero de matricula
            facade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false); //criar o sintético

        } catch (Exception e) {
            naoImportado++;
            System.out.println("Aluno não importado " + idExternoTeste + " --- ERRO: " + e.getMessage());
        }
    }

}

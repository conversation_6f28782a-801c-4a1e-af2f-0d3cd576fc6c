package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class ProcessoCorrecaoGetnet {

    public static void corrigir(Connection c) throws Exception {
        try {
            Uteis.logarDebug("ProcessoCorrecaoGetnet | INICIO!");

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("t.codigo,\n");
            sql.append("t.codigoexterno,\n");
            sql.append("t.dataprocessamento,\n");
            sql.append("t.tipo,\n");
            sql.append("t.paramsresposta,\n");
            sql.append("(select paramsresposta from historicoretornotransacao where transacao = t.codigo and metodo = 'processarRetorno' order by codigo limit 1) as resp2\n");
            sql.append("from transacao t \n");
            sql.append("where coalesce(t.situacao,0) = 0\n");
            sql.append("and t.tipo = ").append(TipoTransacaoEnum.GETNET_ONLINE.getId()).append(" \n");
            sql.append("and (coalesce (t.paramsresposta,'') <> '' and coalesce (t.codigoexterno,'') = '' and paramsresposta ilike '%payment_id%')\n");
            sql.append("or (coalesce (t.paramsresposta,'') = '' and exists(select codigo from historicoretornotransacao where transacao = t.codigo and metodo = 'processarRetorno'))\n");

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", c);
            Integer atual = 0;
            try (Statement stm = c.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        String msg = "";
                        Integer transacao = 0;
                        try {

                            transacao = rs.getInt("codigo");

                            String codigoexterno = rs.getString("codigoexterno");
                            String paramsresposta = rs.getString("paramsresposta");
                            String resp2 = rs.getString("resp2");

                            JSONObject jsonResp = null;
                            try {
                                if (!UteisValidacao.emptyString(paramsresposta)) {
                                    jsonResp = new JSONObject(paramsresposta);
                                } else if (!UteisValidacao.emptyString(resp2)) {
                                    jsonResp = new JSONObject(resp2);
                                }
                            } catch (Exception ignored) {
                            }


                            if (UteisValidacao.emptyString(codigoexterno) && jsonResp != null) {
                                codigoexterno = jsonResp.optString("payment_id");
                                if (!UteisValidacao.emptyString(codigoexterno)) {
                                    SuperFacadeJDBC.executarUpdate("update transacao set codigoexterno = '" + codigoexterno + "' where codigo = " + transacao, c);
                                    msg += "Ajustado codigo externo | ";
                                }
                            }

                            if (UteisValidacao.emptyString(paramsresposta) && !UteisValidacao.emptyString(resp2)) {
                                paramsresposta = resp2;
                                if (!UteisValidacao.emptyString(paramsresposta)) {
                                    SuperFacadeJDBC.executarUpdate("update transacao set paramsresposta = '" + paramsresposta + "' where codigo = " + transacao, c);
                                    msg += "Ajustado paramsresposta | ";
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            msg = ex.getMessage();
                        } finally {
                            Uteis.logarDebug("ProcessoCorrecaoGetnet | " + ++atual + "/" + total + " - Transacao " + transacao + " | Resultado: " + msg);
                        }
                    }
                }
            }
        } finally {
            Uteis.logarDebug("ProcessoCorrecaoGetnet | FIM!");
        }
    }

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("*****************************************************", "zillyonweb", "pactodb");
            ProcessoCorrecaoGetnet.corrigir(con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Maurin <PERSON>",
        data = "16/10/2024",
        descricao = "Add coluna gerandoBoletoPeloGestaoBoletosOnline na tabela movparcela",
        motivacao = "M2-2298")
public class M22485AddColumGerandoBoletoGestaoBoletosOnline implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE movparcela ADD COLUMN gerandoBoletoPeloGestaoBoletosOnline Boolean DEFAULT false;",
                    c
            );
        }
    }
}

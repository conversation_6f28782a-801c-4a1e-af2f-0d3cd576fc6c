package br.com.pactosolucoes.atualizadb.processo.itau;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.text.ParseException;
import java.util.Date;

public class WebhookItauJSON {

    public static final String LIQUIDACAO_NORMAL = "06";
    public static final String BAIXA_OPERACIONAL = "95";

    private String dataNotificacao;
    private String horaNotificacao;
    private String tipoLiquidacao;
    private String tipoCobranca;
    private String idBeneficiario;
    private String codigoCarteira;
    private String numeroNossoNumero;
    private String dacTitulo;
    private String idBoleto;
    private String codigoEspecie;
    private String descricaoEspecie;
    private String codigoBarras;
    private String numeroLinhaDigitavel;
    private String dataVencimento;
    private String valorTitulo;
    private String dataInclusaoPagamento;
    private String valorPagoTotalCobranca;
    private String valorCreditado;
    private String dataCredito;
    private String codigoInstituicaoFinanceiraPagamento;
    private String numeroAgenciaRecebedora;
    private String codigoTipoPessoa;
    private String numeroCadastroPessoaFisica;
    private String numeroCadastroNacionalPessoaJuridica;
    private String nomePagador;
    private String chavePix;
    private String pixCopiaECola;
    private String txid;
    private String endToEndId;

    public WebhookItauJSON(JSONObject json) {
        this.dataNotificacao = json.optString("dataNotificacao");
        this.horaNotificacao = json.optString("horaNotificacao");
        this.tipoLiquidacao = json.optString("tipoLiquidacao");
        this.tipoCobranca = json.optString("tipoCobranca");
        this.idBeneficiario = json.optString("idBeneficiario");
        this.codigoCarteira = json.optString("codigoCarteira");
        this.numeroNossoNumero = json.optString("numeroNossoNumero");
        this.dacTitulo = json.optString("dacTitulo");
        this.idBoleto = json.optString("idBoleto");
        this.codigoEspecie = json.optString("codigoEspecie");
        this.descricaoEspecie = json.optString("descricaoEspecie");
        this.codigoBarras = json.optString("codigoBarras");
        this.numeroLinhaDigitavel = json.optString("numeroLinhaDigitavel");
        this.dataVencimento = json.optString("dataVencimento");
        this.valorTitulo = json.optString("valorTitulo");
        this.dataInclusaoPagamento = json.optString("dataInclusaoPagamento");
        this.valorPagoTotalCobranca = json.optString("valorPagoTotalCobranca");
        this.valorCreditado = json.optString("valorCreditado");
        this.dataCredito = json.optString("dataCredito");
        this.codigoInstituicaoFinanceiraPagamento = json.optString("codigoInstituicaoFinanceiraPagamento");
        this.numeroAgenciaRecebedora = json.optString("numeroAgenciaRecebedora");
        this.codigoTipoPessoa = json.optString("codigoTipoPessoa");
        this.numeroCadastroPessoaFisica = json.optString("numeroCadastroPessoaFisica");
        this.numeroCadastroNacionalPessoaJuridica = json.optString("numeroCadastroNacionalPessoaJuridica");
        this.nomePagador = json.optString("nomePagador");
        this.chavePix = json.optString("chavePix");
        this.pixCopiaECola = json.optString("pixCopiaECola");
        this.txid = json.optString("txid");
        this.endToEndId = json.optString("endToEndId");
    }

    public String getDataNotificacao() {
        return dataNotificacao;
    }

    public void setDataNotificacao(String dataNotificacao) {
        this.dataNotificacao = dataNotificacao;
    }

    public String getHoraNotificacao() {
        return horaNotificacao;
    }

    public void setHoraNotificacao(String horaNotificacao) {
        this.horaNotificacao = horaNotificacao;
    }

    public String getTipoLiquidacao() {
        return tipoLiquidacao;
    }

    public void setTipoLiquidacao(String tipoLiquidacao) {
        this.tipoLiquidacao = tipoLiquidacao;
    }

    public String getTipoCobranca() {
        return tipoCobranca;
    }

    public void setTipoCobranca(String tipoCobranca) {
        this.tipoCobranca = tipoCobranca;
    }

    public String getIdBeneficiario() {
        return idBeneficiario;
    }

    public void setIdBeneficiario(String idBeneficiario) {
        this.idBeneficiario = idBeneficiario;
    }

    public String getCodigoCarteira() {
        return codigoCarteira;
    }

    public void setCodigoCarteira(String codigoCarteira) {
        this.codigoCarteira = codigoCarteira;
    }

    public String getNumeroNossoNumero() {
        return numeroNossoNumero;
    }

    public void setNumeroNossoNumero(String numeroNossoNumero) {
        this.numeroNossoNumero = numeroNossoNumero;
    }

    public String getDacTitulo() {
        return dacTitulo;
    }

    public void setDacTitulo(String dacTitulo) {
        this.dacTitulo = dacTitulo;
    }

    public String getIdBoleto() {
        return idBoleto;
    }

    public void setIdBoleto(String idBoleto) {
        this.idBoleto = idBoleto;
    }

    public String getCodigoEspecie() {
        return codigoEspecie;
    }

    public void setCodigoEspecie(String codigoEspecie) {
        this.codigoEspecie = codigoEspecie;
    }

    public String getDescricaoEspecie() {
        return descricaoEspecie;
    }

    public void setDescricaoEspecie(String descricaoEspecie) {
        this.descricaoEspecie = descricaoEspecie;
    }

    public String getCodigoBarras() {
        return codigoBarras;
    }

    public void setCodigoBarras(String codigoBarras) {
        this.codigoBarras = codigoBarras;
    }

    public String getNumeroLinhaDigitavel() {
        return numeroLinhaDigitavel;
    }

    public void setNumeroLinhaDigitavel(String numeroLinhaDigitavel) {
        this.numeroLinhaDigitavel = numeroLinhaDigitavel;
    }

    public String getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public String getValorTitulo() {
        return valorTitulo;
    }

    public void setValorTitulo(String valorTitulo) {
        this.valorTitulo = valorTitulo;
    }

    public String getDataInclusaoPagamento() {
        return dataInclusaoPagamento;
    }

    public void setDataInclusaoPagamento(String dataInclusaoPagamento) {
        this.dataInclusaoPagamento = dataInclusaoPagamento;
    }

    public String getValorPagoTotalCobranca() {
        return valorPagoTotalCobranca;
    }

    public void setValorPagoTotalCobranca(String valorPagoTotalCobranca) {
        this.valorPagoTotalCobranca = valorPagoTotalCobranca;
    }

    public String getValorCreditado() {
        return valorCreditado;
    }

    public void setValorCreditado(String valorCreditado) {
        this.valorCreditado = valorCreditado;
    }

    public String getDataCredito() {
        return dataCredito;
    }

    public void setDataCredito(String dataCredito) {
        this.dataCredito = dataCredito;
    }

    public String getCodigoInstituicaoFinanceiraPagamento() {
        return codigoInstituicaoFinanceiraPagamento;
    }

    public void setCodigoInstituicaoFinanceiraPagamento(String codigoInstituicaoFinanceiraPagamento) {
        this.codigoInstituicaoFinanceiraPagamento = codigoInstituicaoFinanceiraPagamento;
    }

    public String getNumeroAgenciaRecebedora() {
        return numeroAgenciaRecebedora;
    }

    public void setNumeroAgenciaRecebedora(String numeroAgenciaRecebedora) {
        this.numeroAgenciaRecebedora = numeroAgenciaRecebedora;
    }

    public String getCodigoTipoPessoa() {
        return codigoTipoPessoa;
    }

    public void setCodigoTipoPessoa(String codigoTipoPessoa) {
        this.codigoTipoPessoa = codigoTipoPessoa;
    }

    public String getNumeroCadastroPessoaFisica() {
        return numeroCadastroPessoaFisica;
    }

    public void setNumeroCadastroPessoaFisica(String numeroCadastroPessoaFisica) {
        this.numeroCadastroPessoaFisica = numeroCadastroPessoaFisica;
    }

    public String getNumeroCadastroNacionalPessoaJuridica() {
        return numeroCadastroNacionalPessoaJuridica;
    }

    public void setNumeroCadastroNacionalPessoaJuridica(String numeroCadastroNacionalPessoaJuridica) {
        this.numeroCadastroNacionalPessoaJuridica = numeroCadastroNacionalPessoaJuridica;
    }

    public String getNomePagador() {
        return nomePagador;
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public String getChavePix() {
        return chavePix;
    }

    public void setChavePix(String chavePix) {
        this.chavePix = chavePix;
    }

    public String getPixCopiaECola() {
        return pixCopiaECola;
    }

    public void setPixCopiaECola(String pixCopiaECola) {
        this.pixCopiaECola = pixCopiaECola;
    }

    public String getTxid() {
        return txid;
    }

    public void setTxid(String txid) {
        this.txid = txid;
    }

    public String getEndToEndId() {
        return endToEndId;
    }

    public void setEndToEndId(String endToEndId) {
        this.endToEndId = endToEndId;
    }

    public Float getValor_Float() {
        if (UteisValidacao.emptyString(getValorTitulo())) {
            return (float) 0.0;
        } else {
            return Float.valueOf(getValorTitulo());
        }
    }

    public Float getValor_pago_Float() {
        if (UteisValidacao.emptyString(getValorPagoTotalCobranca())) {
            return (float) 0.0;
        } else {
            return Float.valueOf(getValorPagoTotalCobranca());
        }
    }

    public Float getValor_liquido_Float() {
        if (UteisValidacao.emptyString(getValorCreditado())) {
            return (float) 0.0;
        } else {
            return Float.valueOf(getValorCreditado());
        }
    }

    public Date getData_pagamento_Date() throws ParseException {
        if (UteisValidacao.emptyString(getDataInclusaoPagamento())) {
            return null;
        } else {
            return Calendario.getDate("yyyy-MM-dd", getDataInclusaoPagamento());
        }
    }

    public Date getData_credito_Date() throws ParseException {
        if (UteisValidacao.emptyString(getDataCredito())) {
            return null;
        } else {
            return Calendario.getDate("yyyy-MM-dd", getDataCredito());
        }
    }

    public Date getData_vencimento_Date() throws ParseException {
        if (UteisValidacao.emptyString(getDataVencimento())) {
            return null;
        } else {
            return Calendario.getDate("yyyy-MM-dd", getDataVencimento());
        }
    }

    public Date getData_notificacao_Date() throws ParseException {
        if (UteisValidacao.emptyString(getDataNotificacao()) && UteisValidacao.emptyString(getHoraNotificacao())) {
            return null;
        } else {
            return Calendario.getDate("yyyy-MM-dd hh:mm", getDataNotificacao() + " " + getHoraNotificacao());
        }
    }
}

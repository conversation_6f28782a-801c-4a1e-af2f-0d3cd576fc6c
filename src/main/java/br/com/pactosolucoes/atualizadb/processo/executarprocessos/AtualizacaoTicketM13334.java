package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "14/11/2024",
        descricao = "Atualizando a autorização de acesso do grupo empresarial com a empresa local correta",
        motivacao = "M1-3334")
public class AtualizacaoTicketM13334 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE autorizacaoacessogrupoempresarial agel\n" +
                    "SET integracaoacessogrupoempresarial = (SELECT ige2.codigo FROM integracaoacessogrupoempresarial ige2 WHERE ige2.descricao ILIKE ige.descricao AND ige2.empresalocal = agel.empresalocal LIMIT 1)\n" +
                    "FROM integracaoacessogrupoempresarial ige\n" +
                    "WHERE agel.integracaoacessogrupoempresarial = ige.codigo\n" +
                    "  AND agel.empresalocal <> ige.empresalocal\n" +
                    "  AND EXISTS (SELECT 1 FROM integracaoacessogrupoempresarial ige2 WHERE ige2.descricao ILIKE ige.descricao AND ige2.empresalocal = agel.empresalocal);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE autorizacaoacessogrupoempresarial age SET empresalocal = COALESCE(ige.empresalocal, age.empresalocal)\n" +
                    "FROM integracaoacessogrupoempresarial ige\n" +
                    "WHERE age.integracaoacessogrupoempresarial = ige.codigo\n" +
                    "\tAND age.empresalocal <> ige.empresalocal;", c);
        }
    }

}

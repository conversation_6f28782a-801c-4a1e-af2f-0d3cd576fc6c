package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Luiz Felipe",
        data = "16/07/2025",
        descricao = "Log envio email",
        motivacao = "PAY-1180"
)
public class AtualizacaoTicket_PAY_1180 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.pactopayenvioemaillog (\n" +
                    "\tcodigo serial primary key,\n" +
                    "\tdataregistro TIMESTAMP WITHOUT TIME zone,\n" +
                    "\tpactopayenvioemail integer,\n" +
                    "\toperacao text,\n" +
                    "\tdados text\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopayenvioemaillog_pactopayenvioemail ON public.pactopayenvioemaillog USING btree (pactopayenvioemail);", c);
        }
    }

}

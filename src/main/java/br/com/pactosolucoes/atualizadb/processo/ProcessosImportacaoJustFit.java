package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioEmail;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.TipoColaborador;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import relatorio.controle.sad.SituacaoClienteSinteticoDWControle;
import servicos.integracao.TreinoWSConsumer;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProcessosImportacaoJustFit {

    public static void main(String[] args) {
        try {

            System.out.println("Iniciando.... ProcessosImportacaoJustFit...");
            Uteis.debug = true;

            if (args.length == 0) {
                args = new String[]{"teste"};
            }

            String chave = args[0];
            Connection con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(con);

            long a = System.currentTimeMillis();

            //CONRRIGIR HISTORICO DE CONTRATOS
            Contrato contratoDAO = new Contrato(con);
            Integer codigoEmpresa = 0; // para atualizar apenas de determinada empresa
            contratoDAO.gerarSituacoesTemporaisContratos(0, codigoEmpresa);
            CorrigirHistoricoContratos.corrigirEncadeamentoContratosOrfaos(con);

            //CLIENTE SINTETICO
            SituacaoClienteSinteticoDWControle control = new SituacaoClienteSinteticoDWControle();
            control.setDebug(true);
            control.processarDia(Calendario.hoje(), false);

            //MENSAGEM DE PARCELA EM ATRASO
            atualizarMensagens(con);

            //SINCRONIZAR COM O TREINO
            sincronizarTodosUsuarios(chave, con);

            long b = System.currentTimeMillis();
            System.out.println("ProcessosImportacaoJustFit realizada em: " + ((b - a) / (1000 * 60)) + " minutos");


        } catch (Exception e) {
            System.out.println("ERRO ProcessosImportacaoJustFit: " + e.getMessage());
        }
    }

    private static void atualizarMensagens(Connection con) {
        try {
            List<EmpresaVO> empresas = new Empresa(con).consultarEmpresas();
            RoboVO robo = new RoboVO();
            robo.setDia(Calendario.hoje());
            robo.setListaEmpresa(empresas);
            robo.processarClientesComParcelaVencida();
        } catch (Exception ex) {
            System.out.println("ERRO atualizarMensagens | ERRO: " + ex.getMessage());
        }
    }

    private static void sincronizarTodosUsuarios(String key, Connection con) {
        try {
            Usuario usuarioDAO = new Usuario(con);
            List<UsuarioVO> usuarios = usuarioDAO.consultarTodosAtivosSemAdministrador(Uteis.NIVELMONTARDADOS_TODOS);
            for (UsuarioVO usuarioVO : usuarios) {
                sincronizarUsuarioMovel(usuarioVO, key, con);
            }
        } catch (Exception ex) {
            System.out.println("ERRO sincronizarTodosUsuarios | ERRO: " + ex.getMessage());
        }
    }

    private static void sincronizarUsuarioMovel(UsuarioVO usuarioVO, String key, Connection con) {
        try {
            UsuarioEmail usuarioEmailDAO = new UsuarioEmail(con);
            TipoColaborador tipoColaboradorDAO = new TipoColaborador(con);
            UsuarioMovel usuarioMovelDAO = new UsuarioMovel(con);

            UsuarioEmailVO usuarioEmailVO = usuarioEmailDAO.consultarPorUsuario(usuarioVO.getCodigo());
            if (UteisValidacao.emptyNumber(usuarioEmailVO.getCodigo())) {
                System.out.println("Usuário " + usuarioVO.getUsername() + " não tem email!");
            }
            UsuarioMovelVO userMovel = usuarioMovelDAO.consultarPorUsuario(usuarioVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            userMovel.setUsuarioEmailVO(usuarioEmailVO);
            userMovel.getUsuarioEmailVO().setUsuario(usuarioVO.getCodigo());
            userMovel.setColaborador(usuarioVO.getColaboradorVO());
            userMovel.setUsuarioZW(usuarioVO.getCodigo());
            userMovel.getColaborador().setListaTipoColaboradorVOs(tipoColaboradorDAO.consultarPorCodigoColaborador(userMovel.getColaborador().getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            userMovel.setNome(usuarioVO.getUsername());
            userMovel.setSenha(usuarioVO.getSenha());
            userMovel.setAtivo(true);
            userMovel.setEmpresa(usuarioVO.getColaboradorVO().getEmpresa().getCodigo());
            userMovel.setSenhaEncriptada(true);
            userMovel.setPropagarExcessao(true);

            if (UteisValidacao.emptyNumber(userMovel.getCodigo())) {
                usuarioMovelDAO.incluir(userMovel);
            } else {
                usuarioMovelDAO.alterar(userMovel);
            }
            sincronizaTreino(key, userMovel);

            usuarioEmailDAO = null;
            tipoColaboradorDAO = null;
            usuarioMovelDAO = null;
        } catch (Exception e) {
            System.out.println("ERRO Sincronizar Usuario " + usuarioVO.getUsername() + " | ERRO: " + e.getMessage());

        }
    }

    private static void sincronizaTreino(String key, UsuarioMovelVO userMovel) {
        try {
            TreinoWSConsumer.sincronizarUsuario(key, userMovel);
            TreinoWSConsumer.sincronizarProfessor(key, userMovel.getColaborador().toProfessorSintetico(), userMovel.getColaborador().getEmpresa().getCodigo());
        } catch (Exception e) {
            System.out.println("ERRO SincronizarTreino Usuário " + userMovel.getNome() + " | ERRO: " + e.getMessage());
        }
    }

}

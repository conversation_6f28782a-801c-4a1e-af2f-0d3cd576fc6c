package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "23/08/2024",
        descricao = "Novos campos - Câmera - Servidor Facial",
        motivacao = "M5-531")
public class AtualizacaoTicketM5531 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE camera ADD COLUMN urlRtsp varchar(80) NULL", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE coletor ADD COLUMN usaRtsp boolean DEFAULT FALSE;", c);
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.HistoricoContrato;

/**
 *
 * <AUTHOR>
 */
public class VerificaProblemasPeriodoAcesso {

    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("*****************************************************", "zillyonweb", "pactodb");
            verificarContratoSemPeriodoAcesso(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void verificarContratoSemPeriodoAcesso(Connection con) throws Exception {
        System.out.println("\n########## verificarPeriodoAcessoErrado  - início em : " + new Date()+" ####"+con.getCatalog()+" ###########");
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato, c.vigenciaateajustada from contrato c "
                + "where c.situacao = 'AT' ", con);
        int cont = 1;

        while (consulta.next()) {
            ResultSet consultaRenovacao = SuperFacadeJDBC.criarConsulta("select * from periodoacessocliente p "
                    + "where p.contrato = " + consulta.getInt("contrato")
                    + " order by datafinalacesso desc limit 1", con);
           if(!consultaRenovacao.next()) {
                System.out.println(++cont + " - Contrato " + consulta.getInt("contrato") + " está com problemas no periodo de acesso ");
                
            } else {
                if(consultaRenovacao.getDate("datafinalacesso").compareTo(consulta.getDate("vigenciaateajustada")) != 0){
                     System.out.println(++cont + " - Contrato " + consulta.getInt("contrato") + " está com datafinal =  " + consulta.getDate("vigenciaateajustada")
                             +" e seu periodo de acesso do tipo = " + consultaRenovacao.getString("tipoacesso")+" esta com data final = " +consultaRenovacao.getDate("datafinalacesso") );
                }
            }
//
        }
         System.out.println( "verificarPeriodoAcessoErrado - fim em : "+ new Date()+" ####"+con.getCatalog());
    }
}

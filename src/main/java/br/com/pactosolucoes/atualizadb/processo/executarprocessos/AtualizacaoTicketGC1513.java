package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "03/02/2025",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-1513")
public class AtualizacaoTicketGC1513 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produtotextopadrao ADD COLUMN dataassinaturacontrato timestamp default null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produtotextopadrao ADD COLUMN ipassinaturacontrato text default null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produtotextopadrao ADD COLUMN emailrecebimento text default null;", c);
            StringBuilder sql = new StringBuilder();
            sql.append("    CREATE TABLE produtoassinaturadigital ");
            sql.append("                 ( 			  codigo serial NOT NULL                                                , ");
            sql.append("                              usuarioresponsavel INTEGER NOT NULL                                                , ");
            sql.append("                              produtotextopadrao INTEGER NOT NULL                                                , ");
            sql.append("                              lancamento timestamp NOT NULL                            , ");
            sql.append("                              documentos text                            , ");
            sql.append("                              endereco text                            , ");
            sql.append("                              assinatura text                            , ");
            sql.append("                              atestado text                            , ");
            sql.append("                              anexo1 text                            , ");
            sql.append("                              anexo2 text                            , ");
            sql.append("                              anexocancelamento text                            , ");
            sql.append("                              CONSTRAINT produtoassinaturadigital_pkey PRIMARY KEY (codigo), ");
            sql.append("                              CONSTRAINT fk_produtoassinaturadigital_produtotextopadrao FOREIGN KEY (produtotextopadrao) REFERENCES produtotextopadrao (codigo) MATCH SIMPLE ON ");
            sql.append("                 UPDATE RESTRICT ON DELETE RESTRICT, ");
            sql.append("                              CONSTRAINT fk_produtoassinaturadigital_usuarioresponsavel FOREIGN KEY (usuarioresponsavel) REFERENCES usuario (codigo) MATCH SIMPLE ON ");
            sql.append("                 UPDATE RESTRICT ON DELETE RESTRICT ); ");
            sql.append("          CREATE INDEX ch_produtoassinaturadigital_produtotextopadrao ");
            sql.append("          ON produtoassinaturadigital ");
            sql.append("          USING        btree ( produtotextopadrao );");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), c);
        }
    }
}

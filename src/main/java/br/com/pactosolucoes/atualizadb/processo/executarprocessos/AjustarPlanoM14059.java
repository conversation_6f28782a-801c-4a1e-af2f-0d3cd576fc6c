package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Marcos Andre",
        data = "14/01/2025",
        descricao = "problema na descrição dos planos criados na tela nova",
        motivacao = "M1-4059")
public class AjustarPlanoM14059 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update plano set descricao  = TRIM(BOTH ' ' FROM descricao) where descricao ~ '^[ ]+|[ ]+$';", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Ivan <PERSON>",
        data = "16/10/2024",
        descricao = "Ajustar contrato que estão sem condição de pagamento",
        motivacao = "M1-3023")
public class AtualizacaoTicketM13023 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO contratocondicaopagamento(condicaopagamento, contrato, percentualdesconto, tipooperacao, tipovalor, valorespecifico)\n" +
                    "SELECT (SELECT plp.condicaopagamento FROM planocondicaopagamento plp INNER JOIN condicaopagamento cp ON cp.codigo = plp.condicaopagamento WHERE plp.planoduracao IN (SELECT codigo from planoduracao pd WHERE pd.plano = con.plano) ORDER BY (cp.descricao ILIKE '%VISTA%') DESC, cp.codigo limit 1), con.codigo, 0, '', '', 0\n" +
                    "FROM contrato con WHERE (con.situacao = 'AT' OR (con.contratoresponsavelrenovacaomatricula  = 0 AND contratoresponsavelrematriculamatricula = 0 AND vigenciaateajustada> '2023-01-01') ) \n" +
                    "AND NOT EXISTS (SELECT * FROM contratocondicaopagamento cc WHERE cc.contrato = con.codigo);",c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO contratohorario(horario, contrato, tipooperacao, tipovalor, valorespecifico, percentualdesconto)\n" +
                    "SELECT (SELECT ph.horario FROM planohorario ph INNER JOIN horario hr ON hr.codigo = ph.horario WHERE ph.plano = con.plano ORDER BY ph.codigo limit 1), con.codigo, '', '', 0, 0\n" +
                    "FROM contrato con WHERE (con.situacao = 'AT' OR (con.contratoresponsavelrenovacaomatricula  = 0 AND con.contratoresponsavelrematriculamatricula = 0 AND con.vigenciaateajustada> '2023-01-01') )\n" +
                    "AND NOT EXISTS (SELECT * FROM contratohorario ch WHERE ch.contrato = con.codigo);",c);
        }
    }

}

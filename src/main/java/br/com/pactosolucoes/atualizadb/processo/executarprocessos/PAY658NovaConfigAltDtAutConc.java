package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "16/04/2025",
        descricao = "Nova config financeiro conc.",
        motivacao = "PAY-658")
public class PAY658NovaConfigAltDtAutConc implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro add column alterarDtPgtoZWAutomaticamenteConc boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE extratodiarioitem add column alterouDataRecebimentoZWAutomaticamente boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE extratodiarioitem add column dataPgtoOriginalZWAntesDaAlteracaoAutomatica timestamp without time zone;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cartaoCredito add column alterouDataRecebimentoZWAutomaticamente boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cartaoCredito add column dataPgtoOriginalZWAntesDaAlteracaoAutomatica timestamp without time zone;", c);
        }
    }
}

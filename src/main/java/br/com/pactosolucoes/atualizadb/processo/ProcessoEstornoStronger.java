/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.apf.APF;
import servicos.impl.cieloecommerce.CieloeCommerceService;
import servicos.impl.redepay.ERedeService;
import servicos.interfaces.AprovacaoServiceInterface;

import java.sql.Connection;
import java.util.List;


public class ProcessoEstornoStronger {

    public ProcessoEstornoStronger() {
    }

    public static void main(String[] args) {
        try {
            DAO dao = new DAO();
            if (args.length < 3) {
                System.out.println("São necessários 3 parâmetros: <CHAVE_EMPRESA> <COD_EMPRESA> <DATA_BD_AUTORIZADAS_PARA_CANCELAR>");
            }
            String chave = args[0];
            Integer empresa = Integer.parseInt(args[1]);
            Connection con = dao.obterConexaoEspecifica(chave);
//            Connection con = DriverManager.getConnection("**************************************************", "zillyonweb", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);
            cancelarTransacoes(con, empresa, args[2]);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    private static void cancelarTransacoes(Connection con, Integer empresa, String data) {
        try {

            Integer sucesso = 0;
            Integer falha = 0;

            Transacao transacaoDao = new Transacao(con);
//            List<TransacaoVO> listaTranscoes = transacaoDao.consultar("select * from transacao where situacao = 4 and empresa <>2 and  dataprocessamento::date > '01/01/2018' ", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<TransacaoVO> listaTranscoes = transacaoDao.consultar("select * from transacao where situacao = 4 AND empresa = " + empresa + " AND dataprocessamento::date = '" + data + "' ", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            System.out.println("Total: " + listaTranscoes.size());

            for (TransacaoVO transacaoVO : listaTranscoes) {
                try {
                    SituacaoTransacaoEnum sitAnterior = ((TransacaoVO) transacaoVO.getClone(true)).getSituacao();
                    getServiceParaTransacao(transacaoVO).cancelarTransacao(transacaoVO, true);
                    SituacaoTransacaoEnum sitNova = transacaoVO.getSituacao();
                    if ((sitAnterior != sitNova) && (sitNova == SituacaoTransacaoEnum.CANCELADA)) {
                        sucesso++;
                        System.out.println("Cancelada");
                    } else {
                        System.out.println("Não cancelada: " + transacaoVO.getValorAtributoCancelamento(APF.ResultSolicCancel));
                    }
                } catch (Exception e) {
                    System.out.println("ERRO: " + e.getMessage());
                    falha++;
                }
            }

            System.out.println("Total: " + listaTranscoes.size());
            System.out.println("Sucesso: " + sucesso);
            System.out.println("Falha: " + falha);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static AprovacaoServiceInterface getServiceParaTransacao(TransacaoVO transacao) throws Exception {
        if (transacao.getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE)) {
            return new CieloeCommerceService(Conexao.getFromSession(), transacao.getEmpresa(), transacao.getConvenioCobrancaVO().getCodigo());
        } else if (transacao.getTipo().equals(TipoTransacaoEnum.E_REDE)) {
            return new ERedeService(Conexao.getFromSession(), transacao.getEmpresa(), transacao.getConvenioCobrancaVO().getCodigo());
        }
        return null;
    }
}

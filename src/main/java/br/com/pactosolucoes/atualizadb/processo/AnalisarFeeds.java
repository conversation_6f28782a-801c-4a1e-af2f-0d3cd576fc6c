/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class AnalisarFeeds {
    
    static Map<String, Integer> nrDicasEnviadasPorDia = new HashMap<String, Integer>();
    static Map<String, Integer> nrDicasEnviadasPorEmpresa = new HashMap<String, Integer>();
    static Map<String, Integer> nrDicasLidasPorDia = new HashMap<String, Integer>();
    static Map<String, Integer> nrDicasLidasPorEmpresa = new HashMap<String, Integer>();
    
    public static void main(String ... args){
        consultaDadosEmTodosOsBancos();
    }
    
    public static void analise(String chave, JSONObject objDados) throws JSONException{
        Integer nrDicasPorDia = nrDicasEnviadasPorDia.get(objDados.getString("dataapresentar"));
        nrDicasPorDia = nrDicasPorDia == null ? 1 : nrDicasPorDia + 1;
        nrDicasEnviadasPorDia.put(objDados.getString("dataapresentar"), nrDicasPorDia);
        
        Integer nrDicasPorEmpresa = nrDicasEnviadasPorEmpresa.get(objDados.getString("nome") + " - " + chave);
        nrDicasPorEmpresa = nrDicasPorEmpresa == null ? 1 : nrDicasPorEmpresa + 1;
        nrDicasEnviadasPorEmpresa.put(objDados.getString("nome") + " - " + chave, nrDicasPorEmpresa);
        try {
            int lidas = Integer.valueOf(objDados.getString("lidas"));
            if(lidas > 0){
                Integer nrLidasPorDia = nrDicasLidasPorDia.get(objDados.getString("dataapresentar"));
                nrLidasPorDia = nrLidasPorDia == null ? lidas : nrLidasPorDia + lidas;
                nrDicasLidasPorDia.put(objDados.getString("dataapresentar"), nrLidasPorDia);

                Integer nrLidasPorEmpresa = nrDicasLidasPorEmpresa.get(objDados.getString("nome") + " - " + chave);
                nrLidasPorEmpresa = nrLidasPorEmpresa == null ? lidas : nrLidasPorEmpresa + lidas;
                nrDicasLidasPorEmpresa.put(objDados.getString("nome") + " - " + chave, nrLidasPorEmpresa);
            }
        } catch (Exception e) {
        }
        
    }
    
    public static void consultaDadosEmTodosOsBancos() {
        try {
            Map<String, String> p = new HashMap<String, String>();
            p.put("op", "selectFULL");
            p.put("hostPG", "********");
            p.put("portaPG", "5432");
            p.put("userPG", "postgres");
            p.put("pwdPG", "pactodb");
            p.put("sql", "select empresa.nome,fgh.dataapresentar,codigofeedoamd, \n" +
                        "(select count(codigo) from feedgestaolida where codigohistorico = fgh.codigo) as lidas\n" +
                        "from feedgestaohistorico fgh\n" +
                        "inner join empresa on fgh.empresa = empresa.codigo\n" +
                        "where dataapresentar > '2015-10-01'");
            p.put("format", "json");
            System.out.println("inicio consulta em " + Calendario.hoje());
            String result = executeRequestInner("http://app.pactosolucoes.com.br/UpdateServlet", p, 0);
            montarDadosString(result);
            for(String key : nrDicasEnviadasPorDia.keySet()){
                int enviadas = nrDicasEnviadasPorDia.get(key);
                int lidas = 0;
                try{
                    lidas = nrDicasLidasPorDia.get(key);
                }catch(Exception e){
                    
                }
                System.out.println(Uteis.getData(Uteis.getDate(key, "yyyy-MM-dd")) + ";" + enviadas+";"+lidas+";");
            }
            System.out.println("\n\n\n");
            System.out.println("################# -   -    EMPRESAS - - - - ##############################");
            System.out.println("\n");
//            for(String key : nrDicasEnviadasPorEmpresa.keySet()){
//                int enviadas = nrDicasEnviadasPorEmpresa.get(key);
//                int lidas = 0;
//                try{
//                    lidas = nrDicasLidasPorEmpresa.get(key);
//                }catch(Exception e){
//                    
//                }
//                System.out.println(key + ";" + enviadas+";"+lidas+";");
//            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        
    }
    
    
    public static void montarDadosString(String result) throws JSONException {
        JSONArray jsonArray = new JSONArray(result);
        for (int t = 0; t < jsonArray.length(); t++) {
            try {
                JSONObject objServidor = (JSONObject) jsonArray.get(t);
                Uteis.logar(null, objServidor.getString("nomeservidor"));
                JSONArray arrayResult = objServidor.getJSONArray("servidor");
                for (int i = 0; i < arrayResult.length(); i++) {
                    try {
                        JSONObject obj = (JSONObject) arrayResult.get(i);
                        String dadosStr = obj.getString("result");
                        JSONArray jsonDados = new JSONArray(dadosStr);
                        for (int j = 0; j < jsonDados.length(); j++) {
                            try {
                                JSONObject objDados = (JSONObject) jsonDados.get(j);
                                analise(obj.getString("chave"), objDados);
                                
                            } catch (Exception e) {
                                Uteis.logar(null, e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        Uteis.logar(null, e.getMessage());
                    }
                }
            } catch (Exception e) {
                Uteis.logar(null, e.getMessage());
            }
        }
    }
    
    public static String executeRequestInner(String urlRequest,
            Map<String, String> params, final int timeout) throws IOException {
        String parametrosCodificados = "";
        if (params != null) {
            Uteis.logar(null, params.toString());
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                            + paramName + "="
                            + URLEncoder.encode(valor, "iso-8859-1");
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        if (timeout != 0) {
            conn.setReadTimeout(timeout);
            conn.setConnectTimeout(timeout);
        }
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        String resposta = "";
        try {
            wr.write(parametrosCodificados);
            wr.flush();
            // Pega a Resposta
            BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            try {
                String line;
                while ((line = rd.readLine()) != null) {
                    // Processa linha a linha
                    resposta += line + "\n";
                }
            } catch (IOException io) {
                int respCode = ((HttpURLConnection) conn).getResponseCode();
                InputStreamReader es = new InputStreamReader(((HttpURLConnection) conn).getErrorStream());
                int ret = 0;
                // read the response body
                char[] buf = null;
                while ((ret = es.read(buf)) > 0) {
                }
                // close the errorstream
                es.close();
            } finally {
                rd.close();
            }
        } finally {
            wr.close();
        }
        Uteis.logar(null, resposta);
        return resposta;
    }
    
    class FeedOamdAnalise{
        
    }
    
}

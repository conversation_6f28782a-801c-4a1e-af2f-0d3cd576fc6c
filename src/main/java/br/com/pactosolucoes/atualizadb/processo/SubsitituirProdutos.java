package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

public class SubsitituirProdutos {

	public static void main(String[] args) {
		try {
			Connection con1 = DriverManager.getConnection("*********************************************************", "postgres", "pactodb");
//			substituir(966, con1);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public static void substituir(int produtoSubstituir, int produtoSubstituido, boolean deletar, Connection con) throws Exception{
		String sqlError = "";
		try {
            con.setAutoCommit(false);
            String[] sqls = new String[]{
    				"UPDATE sch_estudio.agenda_agendar SET id_produto = ? WHERE id_produto = ?;",
    				"UPDATE aulaavulsadiaria SET produto = ? WHERE produto = ?;",
    				"UPDATE checklistproduto SET produto = ? WHERE produto = ?;",
    				"UPDATE cliente SET freepass = ? WHERE freepass = ?;",
    				"UPDATE clientemensagem SET produto = ? WHERE produto = ?;",
    				"UPDATE cupomfiscalitens SET produto = ? WHERE produto = ?;",
    				"UPDATE movproduto SET produto = ? WHERE produto = ?;",
    				"UPDATE negociacaoeventoperfilambienteprodutoincluso SET produto = ? WHERE produto = ?;",
    				"UPDATE itemvendaavulsa SET produto = ? WHERE produto = ?;",
    				"UPDATE negociacaoeventoperfileventoproduto SET produto = ? WHERE produto = ?;",
    				"UPDATE perfilevento SET produto = ? WHERE produto = ?;",
    				"UPDATE perfileventoproduto SET produto = ? WHERE produto = ?;",
    				"UPDATE planoprodutosugerido SET produto = ? WHERE produto = ?;",
    				"UPDATE produtoinclusao SET produto = ? WHERE produto = ?;",
    				"UPDATE produtosugerido SET produto = ? WHERE produto = ?;",
    				"UPDATE rateiointegracao SET produto = ? WHERE produto = ?;",
    				"UPDATE trancamentocontrato SET produtotrancamento = ? WHERE produtotrancamento = ?;",
    				"UPDATE sch_estudio.agenda SET id_produto = ? WHERE id_produto = ?;",
    				"UPDATE sch_estudio.agenda_agendar SET id_produto = ? WHERE id_produto = ?;",
    				"UPDATE sch_estudio.agenda_faturar SET id_produto = ? WHERE id_produto = ?;",
    				"UPDATE sch_estudio.produto_ambiente SET id_produto = ? WHERE id_produto = ?;",
    				"UPDATE sch_estudio.produto_colaborador SET id_produto = ? WHERE id_produto = ?;"};
    		for(String sql : sqls){
    			sqlError = sql;
    			PreparedStatement stm = con.prepareStatement(sql);
    			stm.setInt(1, produtoSubstituir);
    			stm.setInt(2, produtoSubstituido);
    			stm.execute();
    		}
    		if(deletar){
    			SuperFacadeJDBC.executarConsulta("DELETE FROM produto WHERE codigo = "+produtoSubstituido, con);
    		}
            con.commit();
        } catch (Exception e) {
        	System.out.println(sqlError);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
		
		
		
	}
}

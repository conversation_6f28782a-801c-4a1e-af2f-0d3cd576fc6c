package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "02/12/2024",
        descricao = "Adicionar coluna codigoEmpresaDestino na tabela PlanoRedeEmpresa",
        motivacao = "GC-1212")
public class AtualizacaoTicketGC1212 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planoredeempresa ADD COLUMN codigoempresadestino int4;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE planoredeempresa SET codigoempresadestino = 1 WHERE codigoempresadestino IS NULL;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.HistoricoCartaoVO;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 27/05/2015.
 */
public class ExecutarProcessoHistoricoCartao {


     public static void executarProcesso(Connection con){

         try {

             Connection  c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
             StringBuilder sqlCreate = new StringBuilder();
             sqlCreate.append("CREATE TABLE historicocartao\n");
             sqlCreate.append("(\n       codigo serial NOT NULL,\n");
             sqlCreate.append("          cartao integer NOT NULL,\n");
             sqlCreate.append("          lote integer,\n");
             sqlCreate.append("          credito boolean default true,\n");
             sqlCreate.append("          datainicio timestamp without time zone NOT NULL,\n");
             sqlCreate.append("          datafim timestamp without time zone,\n");
             sqlCreate.append("          movconta integer,\n");
             sqlCreate.append("          dataoriginal timestamp without time zone,\n");
             sqlCreate.append("  CONSTRAINT historicocartaocredito_pkey PRIMARY KEY (codigo),\n");
             sqlCreate.append("  CONSTRAINT fk_historicocartao_cartaocredito FOREIGN KEY (cartao)\n");
             sqlCreate.append("          REFERENCES cartaocredito (codigo) MATCH SIMPLE\n");
             sqlCreate.append("      ON UPDATE NO ACTION ON DELETE CASCADE\n");
             sqlCreate.append(")\n");
             sqlCreate.append("WITH (\n");
             sqlCreate.append("  OIDS=FALSE\n");
             sqlCreate.append(");\n");
             sqlCreate.append("ALTER TABLE historicocartao OWNER TO zillyonweb;\n");
             sqlCreate.append("\n");
             sqlCreate.append("-- Index: historicocartao_cartao_idx\n");
             sqlCreate.append("\n");
             sqlCreate.append("-- DROP INDEX historicocartao_cartao_idx;\n");
             sqlCreate.append("\n");
             sqlCreate.append("CREATE INDEX historicocartao_cartao_idx\n");
             sqlCreate.append("  ON historicocartao\n");
             sqlCreate.append(" USING btree (cartao);");
             c.prepareStatement(sqlCreate.toString()).executeUpdate();
             //Responsavel por povoar  HistoricoCartao
             StringBuilder sqlInsert = new StringBuilder();
             sqlInsert.append("INSERT INTO HISTORICOCARTAO(cartao,lote,movconta,datainicio) SELECT\n");
             sqlInsert.append(" ccl.cartao,l.codigo as LOTE,mc.codigo as MOVCONTA,mc.datalancamento  FROM lote l\n");
             sqlInsert.append("INNER JOIN chequecartaolote Ccl ON ccl.lote = l.codigo\n");
             sqlInsert.append("INNER JOIN movconta mc ON mc.lote = l.codigo\n");
             sqlInsert.append("INNER JOIN movcontarateio mcr ON mcr.movconta = mc.codigo\n");
             sqlInsert.append("where mcr.tipoes = 1 and cartao is not null\n");
             sqlInsert.append("order BY mc.datalancamento");
             c.prepareStatement(sqlInsert.toString()).executeUpdate();

             //Seleciona Cartões com mais de 2 Movimentações
             StringBuilder sql = new StringBuilder();
             sql.append("Select cartao,datainicio datainicio,codigo,datafim from historicocartao where \n");
             sql.append("cartao IN \n");
             sql.append("(select cartao from historicocartao\n");
             sql.append("group by cartao\n" );
             sql.append("HAVING Count(*)>1)\n");
             sql.append("order by cartao,datainicio DESC");

             ResultSet dados = c.prepareStatement(sql.toString()).executeQuery();
             List<HistoricoCartaoVO> lista =new ArrayList<HistoricoCartaoVO>();
             while(dados.next()){
                 HistoricoCartaoVO cartao = new HistoricoCartaoVO();
                 cartao.getCartao().setCodigo(dados.getInt("cartao"));
                 cartao.setCodigo(dados.getInt("codigo"));
                 cartao.setDataInicio(dados.getTimestamp("datainicio"));
                 lista.add(cartao);
             }
             //Seta a data fim do Historico Cartao de acordo com Codigo e numero Cartao
             HistoricoCartaoVO cartao,cartaoAnterior;
             for(int e=0;e < (lista.size()-1);e++){
                 cartao = lista.get(e);
                 cartaoAnterior = lista.get(e+1);
                 if((int)cartao.getCartao().getCodigo() == (int)cartaoAnterior.getCartao().getCodigo()
                         && cartaoAnterior.getDataFim()==null)
                 {
                     c.prepareStatement("UPDATE HISTORICOCARTAO SET\n" +
                             "DATAFIM = \n'" + cartao.getDataInicio()+
                             "' WHERE CODIGO = "+cartaoAnterior.getCodigo()).executeUpdate();
                 }
             }
         } catch (Exception e) {
             e.printStackTrace();
         }
     }
}

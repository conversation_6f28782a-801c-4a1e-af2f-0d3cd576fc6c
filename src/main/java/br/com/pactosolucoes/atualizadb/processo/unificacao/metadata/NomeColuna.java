package br.com.pactosolucoes.atualizadb.processo.unificacao.metadata;

import br.com.pactosolucoes.atualizadb.processo.unificacao.AbstractUnificadorCodigoOrigemDestinoMapeavel;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 09/04/2019
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface NomeColuna {

    /**
     * @return Deve informar o nome da coluna que será lido e usado nas operações dentro de {@link AbstractUnificadorCodigoOrigemDestinoMapeavel}.
     */
    String value();
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "31/07/2024",
        descricao = "M2-2125 - Novas colunas para gravar informações de requisição das transações",
        motivacao = "M2-2125")
public class AtualizacaoTicketM22125 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE historicoRetornoTransacao ADD COLUMN statusServer INT DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE historicoRetornoTransacao ADD COLUMN tempoRequisicao BIGINT DEFAULT NULL;", c);
        }
    }
}

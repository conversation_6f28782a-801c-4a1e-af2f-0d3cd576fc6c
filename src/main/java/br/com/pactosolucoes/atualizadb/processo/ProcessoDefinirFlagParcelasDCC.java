package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Rafael on 08/01/2016.
 */
public class ProcessoDefinirFlagParcelasDCC {

    public static void main(String... args) {
        try {
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "lifegyn");
            Conexao.guardarConexaoForJ2SE(c);
            definirParcelasComoDCC(c);
        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    public static void definirParcelasComoDCC(Connection con) {
        try {

            try {
                con.setAutoCommit(false);
                int cont = 0;
                AutorizacaoCobrancaCliente autorizacaoDao = new AutorizacaoCobrancaCliente(con);
                MovParcela movParcelaDao = new MovParcela(con);
                StringBuilder sql = new StringBuilder();
                sql.append(" SELECT acc.codigo as autorizacao,c.codigo as cliente,p.codigo as pessoa FROM AutorizacaoCobrancaCliente acc\n");
                sql.append(" INNER JOIN Cliente c ON  c.codigo = acc.cliente\n");
                sql.append(" INNER JOIN Pessoa p ON  p.codigo = c.pessoa\n");
                Statement stm = con.createStatement();
                ResultSet rs = stm.executeQuery(sql.toString());
                //Flag como parcelaDCC de acordo com a autorização Existente
                while(rs.next()){
                    int codigoAutorizacao = rs.getInt("autorizacao");
                    int cliente = rs.getInt("cliente");
                    int pessoa = rs.getInt("pessoa");
                    autorizacaoDao.atribuirParcelasDCC(codigoAutorizacao, pessoa, cliente, false);
                    System.out.println("["+cont+"]Autorização Cod :"+codigoAutorizacao);
                    cont++;
                }
                //Flag como parcelaDCC de acordo com as parcelas que já tiveram Remessas Geradas
                sql = new StringBuilder();
                sql.append(" SELECT parcela.* FROM MovParcela parcela\n");
                sql.append(" INNER JOIN RemessaItem item ON  item.movparcela = parcela.codigo\n");
                sql.append(" WHERE NOT parcela.parcelaDCC AND parcela.situacao = 'PG'\n");
                stm = con.createStatement();
                rs = stm.executeQuery(sql.toString());
                while(rs.next()){
                    movParcelaDao.alterarRegimeDCCParcela(rs.getInt("codigo"), true);
                }
            } catch (Exception e) {
                con.rollback();
                System.out.println("error: "+e.getMessage());
            } finally {
                con.setAutoCommit(true);
            }

        }catch (Exception erro){}

    }
    
        public static void definirParcelasComoDCCParcelasAgendadas(Connection con) {
        try {

            try {
                con.setAutoCommit(false);
                int cont = 0;
                AutorizacaoCobrancaCliente autorizacaoDao = new AutorizacaoCobrancaCliente(con);
                MovParcela movParcelaDao = new MovParcela(con);
                StringBuilder sql = new StringBuilder();
                sql.append(" SELECT acc.codigo as autorizacao,c.codigo as cliente,p.codigo as pessoa FROM AutorizacaoCobrancaCliente acc\n");
                sql.append(" INNER JOIN Cliente c ON  c.codigo = acc.cliente\n");
                sql.append(" INNER JOIN Pessoa p ON  p.codigo = c.pessoa\n ");
                sql.append(" and exists(select codigo from movparcela parc where parc.pessoa = p.codigo and parc.reagendada)");
                Statement stm = con.createStatement();
                ResultSet rs = stm.executeQuery(sql.toString());
                //Flag como parcelaDCC de acordo com a autorização Existente
                while(rs.next()){
                    int codigoAutorizacao = rs.getInt("autorizacao");
                    int cliente = rs.getInt("cliente");
                    int pessoa = rs.getInt("pessoa");
                    autorizacaoDao.atribuirParcelasDCC(codigoAutorizacao, pessoa, cliente, false);
                    System.out.println("["+cont+"]Autorização Cod :"+codigoAutorizacao);
                    cont++;
                }
                //Flag como parcelaDCC de acordo com as parcelas que já tiveram Remessas Geradas
                sql = new StringBuilder();
                sql.append(" SELECT parcela.* FROM MovParcela parcela\n");
                sql.append(" INNER JOIN RemessaItem item ON  item.movparcela = parcela.codigo\n");
                sql.append(" WHERE NOT parcela.parcelaDCC AND parcela.situacao = 'PG' AND parcela.reagendada \n");
                stm = con.createStatement();
                rs = stm.executeQuery(sql.toString());
                while(rs.next()){
                    movParcelaDao.alterarRegimeDCCParcela(rs.getInt("codigo"), true);
                }
            } catch (Exception e) {
                con.rollback();
                System.out.println("error: "+e.getMessage());
            } finally {
                con.setAutoCommit(true);
            }

        }catch (Exception erro){}

    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luiz Felipe",
        data = "11/06/2024",
        descricao = "FP-33 - Dashboard Régua de Cobrança",
        motivacao = "FP-33 - Dashboard Régua de Cobrança")
public class AtualizacaoTicketFP33 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN facilitePayReguaCobrancaEmail BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN facilitePayReguaCobrancaSms BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN facilitePayReguaCobrancaApp BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN facilitePayReguaCobrancaWhatsApp BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa SET facilitePayReguaCobrancaEmail = true WHERE facilitePayReguaCobrancaEmail is null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa SET facilitePayReguaCobrancaSms = true WHERE facilitePayReguaCobrancaSms is null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa SET facilitePayReguaCobrancaApp = true WHERE facilitePayReguaCobrancaApp is null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa SET facilitePayReguaCobrancaWhatsApp = true WHERE facilitePayReguaCobrancaWhatsApp is null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN cobrancaAntecipadaSMS BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN cobrancaAntecipadaEmail BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN cobrancaAntecipadaWhatsApp BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN cobrancaAntecipadaApp BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE pactopayconfig SET cobrancaAntecipadaSMS = false WHERE cobrancaAntecipadaSMS is null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE pactopayconfig SET cobrancaAntecipadaEmail = true WHERE cobrancaAntecipadaEmail is null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE pactopayconfig SET cobrancaAntecipadaWhatsApp = false WHERE cobrancaAntecipadaWhatsApp is null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE pactopayconfig SET cobrancaAntecipadaApp = false WHERE cobrancaAntecipadaApp is null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN configuracaogymbot text;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE transacao ADD COLUMN desconto double precision;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN desconto double precision;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE PactoPayCobrancaAntecipada ADD COLUMN pactopaycomunicacao integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaycobrancaantecipada_pactopaycomunicacao ON public.pactopaycobrancaantecipada USING btree (pactopaycomunicacao);", c);
        }
    }
}

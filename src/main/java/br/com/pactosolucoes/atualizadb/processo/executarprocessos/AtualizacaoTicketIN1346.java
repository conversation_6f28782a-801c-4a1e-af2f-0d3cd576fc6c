package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "17/06/2025",
        descricao = "IN-1346 - Melhoria performance BI treino",
        motivacao = "IN-1346 - Melhoria performance BI treino")
public class AtualizacaoTicketIN1346 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_acessocliente_dthrsaida ON public.acessocliente USING btree (dthrsaida ASC NULLS FIRST);", c);
        }
    }
}

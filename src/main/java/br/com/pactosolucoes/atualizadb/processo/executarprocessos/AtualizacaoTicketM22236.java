package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import br.com.pactosolucoes.atualizadb.processo.annotations.Processo;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Vinicius de Moraes",
        data = "12/09/2024",
        descricao = "M2-2236 - Integração novo Gymbot Pro",
        motivacao = "M2-2236")
public class AtualizacaoTicketM22236 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE public.configuracaointegracaogymbotpro (\n" +
                            "\tcodigo serial4 NOT NULL,\n" +
                            "\ttoken varchar(200) NULL,\n" +
                            "\tidfluxo varchar(200) NULL,\n" +
                            "\tdescricao varchar(300) NULL,\n" +
                            "\tempresa int4 NULL,\n" +
                            "\ttipofluxo varchar(1) NULL,\n" +
                            "\tfase varchar(2) NULL,\n" +
                            "\tCONSTRAINT configuracaointegracaogymbotpro_pkey PRIMARY KEY (codigo)\n" +
                            ");", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE public.maladireta \n" +
                            "ADD COLUMN configuracaointegracaogymbotpro INT NULL;", c);

            // Adicionando a constraint de chave estrangeira
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE public.maladireta \n" +
                            "ADD CONSTRAINT fk_configuracaointegracaogymbotpro \n" +
                            "FOREIGN KEY (configuracaointegracaogymbotpro) \n" +
                            "REFERENCES public.configuracaointegracaogymbotpro(codigo);", c);
        }
    }
}



package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "29/08/2024",
        descricao = "Adicionar config empresa permiteCompartilhamentoPlanoClienteAtivoPlanoCredito",
        motivacao = "GC-907")
public class AtualizacaoTicketGC907 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN permiteCompartilhamentoPlanoClienteAtivoPlanoCredito BOOLEAN DEFAULT FALSE;", c);
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.ConfiguracaoSistemaControle;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.openxmlformats.schemas.drawingml.x2006.spreadsheetDrawing.CTOneCellAnchor;

/**
 *
 * <AUTHOR>
 */
public class CorrigirHistoricoContratos {

    public static void main(String... args) {
        try {
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "bdzillyonmovimento-2023-08-15");
            Conexao.guardarConexaoForJ2SE(c);
            Contrato contratoDao = new Contrato(c);
            Integer codigoPrimeiroContrato = 0;
            Integer codigoEmpresa = 0; // para atualizar apenas de determinada empresa
            contratoDao.gerarSituacoesTemporaisContratos(codigoPrimeiroContrato, codigoEmpresa);
            contratoDao = null;
//            converterRematriculasEmRenovacoes();
            corrigirEncadeamentoContratosOrfaos(c);
            //Contrato.descobrirContratosRenovacaoQueSeriaRematricula();
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void converterRematriculasEmRenovacoes(Connection con) {
        StringBuilder sql = new StringBuilder();
        sql.append("select codigo,contratoresponsavelrenovacaomatricula,").
                append("contratoresponsavelrematriculamatricula,").
                append("contratobaseadorematricula,contratobaseadorenovacao ").
                append("from contrato ").
                append("where situacaocontrato = 'RE' and contratobaseadorematricula not in ").
                append("(select contrato from historicocontrato where tipohistorico in('DE','CA')) ").
                append("and contratobaseadorematricula <> 0 ").
                append("order by codigo");
        ResultSet rs;
        try {
            rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                int contrato = rs.getInt("codigo");
                Contrato.converterRematriculaEmRenovacao(contrato, con);
            }

        } catch (SQLException ex) {
            Logger.getLogger(CorrigirHistoricoContratos.class.getName()).log(Level.SEVERE, null, ex);
        } catch (Exception ex) {
            Logger.getLogger(CorrigirHistoricoContratos.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    public static void corrigirEncadeamentoContratosOrfaos(Connection con) {
        StringBuilder sql = new StringBuilder();
        sql.append("select pessoa, situacaocontrato,situacao,codigo, contratoresponsavelrenovacaomatricula,contratoresponsavelrematriculamatricula ").
                append("from contrato ").
                append("where (contratoresponsavelrenovacaomatricula <> 0 and contratoresponsavelrenovacaomatricula not in (select codigo from contrato)) ").
                append("or (contratoresponsavelrematriculamatricula <> 0 and contratoresponsavelrematriculamatricula not in(select codigo from contrato)) ").
                append("order by codigo");
        ResultSet rs;
        try {
            rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                int contrato = rs.getInt("codigo");
                if (rs.getString("situacaocontrato").equals("MA")) {
                    SuperFacadeJDBC.executarConsulta(
                            "update contrato set contratoresponsavelrenovacaomatricula = 0, "
                            + "contratoresponsavelrematriculamatricula = 0 "
                            + "where codigo = " + contrato, con);
                } else if (rs.getString("situacaocontrato").equals("RE")) {

                    ResultSet rsContratoAnterior = SuperFacadeJDBC.criarConsulta("select max(codigo) from contrato where pessoa = " + rs.getInt("pessoa") + " and codigo <> " + contrato, con);
                    int codigoAnterior = 0;
                    if (rsContratoAnterior.next()) {
                        codigoAnterior = rsContratoAnterior.getInt(1);
                    }
                    SuperFacadeJDBC.executarConsulta(
                            "update contrato set contratoresponsavelrenovacaomatricula = 0, "
                            + "contratoresponsavelrematriculamatricula = " + codigoAnterior
                            + "where codigo = " + contrato, con);

                } else if (rs.getString("situacaocontrato").equals("RN")) {

                    ResultSet rsContratoAnterior = SuperFacadeJDBC.criarConsulta("select max(codigo) from contrato where pessoa = " + rs.getInt("pessoa") + " and codigo <> " + contrato, con);
                    int codigoAnterior = 0;
                    if (rsContratoAnterior.next()) {
                        codigoAnterior = rsContratoAnterior.getInt(1);
                    }
                    SuperFacadeJDBC.executarConsulta(
                            "update contrato set contratoresponsavelrenovacaomatricula = " + codigoAnterior
                            + ", contratoresponsavelrematriculamatricula = 0"
                            + "where codigo = " + contrato, con);

                }
            }

        } catch (Exception ex) {
            Logger.getLogger(CorrigirHistoricoContratos.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void corrigirContratosSemHistorico(Connection con) throws Exception {
        SuperFacadeJDBC.executarConsulta("INSERT INTO historicocontrato(retornomanual,contrato,situacaorelativahistorico,tipohistorico,dataregistro,datainiciotemporal,datainiciosituacao,datafinalsituacao,responsavelregistro,descricao)\n" +
                "SELECT \n" +
                "\tFALSE AS retornomanual,\n" +
                "\tcon.codigo AS contrato,\n" +
                "\t'' AS situacaorelativahistorico,\n" +
                "\t(CASE WHEN coalesce(con.contratobaseadorenovacao,0) > 0 THEN 'RN'\n" +
                "\t \t  WHEN coalesce(con.contratobaseadorematricula,0) > 0 THEN 'RE'\n" +
                "\t \t  ELSE 'MA' END) AS tipohistorico,\n" +
                "\tcon.datalancamento AS dataregistro,\n" +
                "\tcon.vigenciade AS datainiciotemporal,\n" +
                "\tcon.vigenciade AS datainiciosituacao,\n" +
                "\tcon.vigenciade AS datafinalsituacao,\n" +
                "\tu.codigo AS responsavelregistro,\n" +
                "\t(CASE WHEN con.situacaocontrato = 'MA' THEN 'MATRICULADO' \n" +
                "\t\t\t\t  WHEN con.situacaocontrato = 'RE' THEN 'REMATRICULADO'\n" +
                "\t\t\t\t  WHEN con.situacaocontrato = 'RN' THEN 'RENOVADO' ELSE '' END) AS descricao\n" +
                "FROM pessoa pes\n" +
                "\tINNER JOIN cliente cli ON cli.pessoa = pes.codigo \n" +
                "\tINNER JOIN contrato con ON con.pessoa = cli.pessoa \n" +
                "\tLEFT JOIN usuario u ON u.codigo = con.responsavelcontrato \n" +
                "WHERE 1 = 1\n" +
                "AND con.situacao <> 'CA'\n" +
                "AND NOT EXISTS (SELECT hc.codigo FROM historicocontrato hc WHERE hc.contrato = con.codigo)\n", con);

    }
}

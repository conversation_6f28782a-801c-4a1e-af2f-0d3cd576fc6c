/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoModalidadeVezesSemanaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.AulaDesmarcada;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.facade.jdbc.contrato.ContratoModalidadeVezesSemana;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class AdicionarModalidadeContratoAluno {
    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("**********************************************************", "postgres", "pactodb");
            addmodalidade(con1);

        } catch (Exception ex) {
            Logger.getLogger(CorrigirDataCompensacaoMovPagamento.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void addmodalidade(Connection con) throws Exception {
        int[] mats = new int[] {78539, 79804,
        60325,
        78058,
        53537,
        78381,
        37257,
        77932,
        4151,
        575,
        78774,
        51115,
        59408,
        2543,
        78765,
        54974,
        59388,
        80024,
        59546,
        78550,
        80742,
        80384,
        78611,
        4623,
        79276,
        49826,
        80746,
        80246,
        55586,
        58474,
        57478,
        38021,
        61425,
        54598,
        59209,
        59505,
        37869,
        41268};
        ContratoModalidade contratoModalidadeDAO = new ContratoModalidade(con);
        ContratoModalidadeVezesSemana contratoModalidadeVezesSemanaDAO = new ContratoModalidadeVezesSemana(con);
        Modalidade modDao = new Modalidade(con);
        ModalidadeVO modalidadeVO = modDao.consultarPorChavePrimaria(20, Uteis.NIVELMONTARDADOS_TODOS);
        for(int mat : mats){
            System.out.println("insert modalidade 20 na matricula " + mat);
            ResultSet consulta = SuperFacadeJDBC.criarConsulta("select codigocontrato " +
                    "from situacaoclientesinteticodw s where matricula = " + mat, con);
            if(consulta.next()){
                ContratoModalidadeVO contratoModalidadeVO = new ContratoModalidadeVO();
                contratoModalidadeVO.setNrVezesSemana(modalidadeVO.getNrVezes());
                contratoModalidadeVO.setValorModalidade(0.0);
                contratoModalidadeVO.setValorFinalModalidade(0.0);
                contratoModalidadeVO.setModalidade(modalidadeVO);
                contratoModalidadeVO.setContrato(consulta.getInt("codigocontrato"));

                contratoModalidadeDAO.incluirContratoModalidade(contratoModalidadeVO);
                ContratoModalidadeVezesSemanaVO contratoModalidadeVezesSemanaVO = new ContratoModalidadeVezesSemanaVO();
                contratoModalidadeVezesSemanaVO.setNrVezes(modalidadeVO.getNrVezes());
                contratoModalidadeVO.setContratoModalidadeVezesSemanaVO(contratoModalidadeVezesSemanaVO);

                contratoModalidadeVezesSemanaDAO.incluirContratoModalidadeVezesSemana(contratoModalidadeVO.getCodigo(), contratoModalidadeVezesSemanaVO);
            }

        }
    }


}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "He<PERSON>am <PERSON>",
        data = "25/02/2025",
        descricao = "gravar numero do documento do rateio",
        motivacao = "GC-1497")
public class AtualizacaoTicketGC1497 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movcontarateio ADD COLUMN numerodocumento text NULL;", c);
        }
    }
}

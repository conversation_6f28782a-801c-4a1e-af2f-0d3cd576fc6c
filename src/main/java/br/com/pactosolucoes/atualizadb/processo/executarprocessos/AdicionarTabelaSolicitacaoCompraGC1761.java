package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>lisam <PERSON>",
        data = "21/03/2025",
        descricao = "Adio da tabela SolicitacaoCompra",
        motivacao = "GC-1761")
public class AdicionarTabelaSolicitacaoCompraGC1761 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            String sql = "CREATE TABLE public.solicitacaocompra (" +
                    " codigo serial4 NOT NULL, " +
                    " titulo varchar(100) NOT NULL, " +
                    " data_solicitacao timestamp DEFAULT now() NOT NULL, " +
                    " situacao varchar(25) NOT NULL, " +
                    " descricao varchar(255) NULL, " +
                    " motivo_negacao varchar(255) NULL, " +
                    " CONSTRAINT solicitacaocompra_pkey PRIMARY KEY (codigo) " +
                    ");";
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);

            SuperFacadeJDBC.executarConsulta("ALTER TABLE compra ADD COLUMN solicitacaoCompra integer default null;", c);
            SuperFacadeJDBC.executarConsulta("ALTER TABLE public.compra ADD CONSTRAINT comprasolicitacaocompra_fkey FOREIGN KEY (solicitacaocompra) REFERENCES public.solicitacaocompra(codigo);", c);
            SuperFacadeJDBC.executarConsulta("ALTER TABLE arquivo ADD COLUMN solicitacaoCompra integer default null;", c);
            SuperFacadeJDBC.executarConsulta("ALTER TABLE arquivo ADD COLUMN observacao text;", c);
            SuperFacadeJDBC.executarConsulta("ALTER TABLE arquivo ADD COLUMN fotokey text;", c);
            SuperFacadeJDBC.executarConsulta("CREATE INDEX arquivo_solicitacaocompra_idx ON public.arquivo USING btree (solicitacaocompra);", c);
            SuperFacadeJDBC.executarConsulta("ALTER TABLE public.arquivo ADD CONSTRAINT arquivosolicitacaocompra_fkey FOREIGN KEY (solicitacaocompra) REFERENCES public.solicitacaocompra(codigo);", c);
        }
    }
}
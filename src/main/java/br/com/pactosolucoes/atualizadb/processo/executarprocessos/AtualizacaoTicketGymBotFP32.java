package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luiz Felipe",
        data = "17/07/2024",
        descricao = "FP-32 - Régua de Cobrança",
        motivacao = "FP-32 - Régua de Cobrança")
public class AtualizacaoTicketGymBotFP32 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN tags text;", c);
        }
    }
}

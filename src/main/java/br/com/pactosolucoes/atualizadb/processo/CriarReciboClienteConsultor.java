package br.com.pactosolucoes.atualizadb.processo;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 27/05/13
 * Time: 10:08
 */
public class CriarReciboClienteConsultor {


    public static String funcaoPovoarReciboClienteConsultor() {
        return "CREATE OR REPLACE FUNCTION migrador_povoarreciboclienteconsultor()\n"
                + "  RETURNS TEXT AS $$\n"
                + "DECLARE registro RECORD;\n"
                + "DECLARE cons integer;\n"
                + "BEGIN\n"
                + "  FOR registro IN (SELECT\n"
                + "                     DISTINCT\n"
                + "                     rp.codigo          AS recibo,\n"
                + "                     rp.data            AS dataRec,\n"
                + "                     cli.codigo         AS cliente,\n"
                + "                     fp.codigo          AS formaPagamento,\n"
                + "                     sum(pmp.valorpago) AS valor\n"
                + "                   FROM public.recibopagamento rp\n"
                + "                     INNER JOIN public.movpagamento mpag\n"
                + "                       ON (mpag.recibopagamento = rp.codigo AND mpag.credito <> true)\n"
                + "                     LEFT JOIN public.formapagamento fp\n"
                + "                       ON mpag.formapagamento = fp.codigo\n"
                + "                     LEFT JOIN public.pagamentomovparcela pmp\n"
                + "                       ON pmp.movpagamento = mpag.codigo\n"
                + "                     LEFT JOIN public.movparcela mpar\n"
                + "                       ON mpar.codigo = pmp.movparcela\n"
                + "                     LEFT JOIN public.cliente cli\n"
                + "                       ON mpar.pessoa = cli.pessoa\n"
                + "                   GROUP BY 1, 2, 3, 4\n"
                + "                   ORDER BY 1)\n"
                + "  LOOP\n"
                + "    cons = (SELECT\n"
                + "              hv.colaborador\n"
                + "            FROM public.historicovinculo hv\n"
                + "            WHERE tipocolaborador LIKE 'CO' AND cliente = registro.cliente\n"
                + "                  AND dataregistro < registro.dataRec AND tipohistoricovinculo LIKE 'EN'\n"
                + "            ORDER BY dataregistro DESC\n"
                + "            LIMIT 1);\n"
                + "    \n"
                + "  INSERT INTO public.reciboclienteconsultor (recibo, cliente, formapagamento, valor, consultor)\n"
                + "    VALUES (registro.recibo, registro.cliente, registro.formaPagamento, registro.valor, cons);\n"
                + "  END LOOP;\n"
                + "\n"
                + "  RETURN 1;\n"
                + "END;\n"
                + "$$\n"
                + "LANGUAGE plpgsql";
    }
}




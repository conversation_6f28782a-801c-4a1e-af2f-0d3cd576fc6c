package br.com.pactosolucoes.atualizadb.processo.ajustebd;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoHorarioVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoHorario;
import negocio.facade.jdbc.contrato.ContratoModalidade;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

public class AlterarHorarioPlano extends SuperEntidade {
    private ContratoHorario contratoHorarioDAO;
    private Log logDAO;

    public AlterarHorarioPlano() throws Exception {
        super();

        try {
            this.contratoHorarioDAO = new ContratoHorario(con);
            this.logDAO = new Log(con);
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi possível inicializar DAOs");
        }
    }


    public List<ContratoVO> consultarContratosAlterarHorarios(int codEmpresa, PlanoVO planoVO, HorarioVO horarioVO, HorarioVO horarioVOAtual) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT con.* \n");
        sql.append("FROM contratohorario ch \n");
        sql.append("INNER JOIN contrato con ON con.codigo = ch.contrato AND  ch.horario <> ? \n");
        sql.append("WHERE 1 = 1 \n");
        sql.append(" AND con.plano = ? \n");
        sql.append(" AND con.empresa = ? \n");
        sql.append(" AND con.situacao = 'AT' \n");
        if(!UteisValidacao.emptyNumber(horarioVOAtual.getCodigo())) {
            sql.append(" AND ch.horario = ? \n");
        }
        sql.append("ORDER BY con.codigo; ");

        PreparedStatement ps = con.prepareStatement(sql.toString());
        ps.setInt(1, horarioVO.getCodigo());
        ps.setInt(2, planoVO.getCodigo());
        ps.setInt(3, codEmpresa);
        if(!UteisValidacao.emptyNumber(horarioVOAtual.getCodigo())) {
            ps.setInt(4, horarioVOAtual.getCodigo());
        }
        ResultSet rs = ps.executeQuery();

        return Contrato.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_ROBO, con);
    }


    public void alterarHorarioContrato(ContratoVO contratoVO, HorarioVO horarioVO, UsuarioVO usuarioVO) throws Exception {
        contratoHorarioDAO.alterarHorariosConfiguracoes(contratoVO,horarioVO);
        incluirLog(contratoVO, horarioVO, usuarioVO);
    }

       private void incluirLog(ContratoVO contratoVO, HorarioVO horarioVO, UsuarioVO usuarioVO) throws Exception {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(contratoVO.getCodigo().toString());
        obj.setPessoa(contratoVO.getPessoa().getCodigo());
        obj.setNomeEntidade("CONTRATOHORARIO");
        obj.setNomeEntidadeDescricao("Configurações");
        obj.setOperacao("Alterar Horario (Processo)");
        obj.setResponsavelAlteracao(usuarioVO.getNome());
        obj.setUserOAMD(usuarioVO.getUserOamd());
        obj.setNomeCampo("Horario");
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado(horarioVO.getDescricao());


        logDAO.incluir(obj);
    }
}

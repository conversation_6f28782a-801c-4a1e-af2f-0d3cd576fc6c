package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "11/11/2024",
        descricao = "PlanoAcessoEmpresaRemota",
        motivacao = "GC-477")
public class AtualizacaoTicketGC477 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.plano ADD COLUMN acessoRedeEmpresasEspecificas BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.planoempresaredeacesso (\n" +
                    "\tcodigo serial4 NOT NULL,\n" +
                    "\tplano int4 NOT NULL,\n" +
                    "\tchave varchar(50) NOT NULL,\n" +
                    "\tcodigoEmpresa int4 NOT NULL,\n" +
                    "\tnomeempresa varchar(50) NOT NULL,\n" +
                    "\tCONSTRAINT planoempresaredeacesso_pkey PRIMARY KEY (codigo),\n" +
                    "\tCONSTRAINT fk_planoempresaredeacesso_plano FOREIGN KEY (plano) REFERENCES public.plano(codigo)\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_planoempresaredeacesso_plano ON public.planoempresaredeacesso USING btree (plano);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_planoempresaredeacesso_chave ON public.planoempresaredeacesso USING btree (chave)", c);
        }
    }
}

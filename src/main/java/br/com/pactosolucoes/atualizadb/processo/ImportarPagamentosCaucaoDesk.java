/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.UsuarioNFeVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.CartaoCredito;
import negocio.facade.jdbc.financeiro.Cheque;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.NFSeEmitida;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.jdom.Element;
import test.simulacao.LeitorXML;

/**
 *
 * <AUTHOR>
 */
public class ImportarPagamentosCaucaoDesk {

    public static void main(String... args) {
        try {
            String nomeThread = args.length > 0 ? args[0] : "ath";
            String pathXmlPagamentos = args.length > 1 ? args[1] : "C:\\pacto\\pagamentos.xml";
            String pathXmlNotas = args.length > 2 ? args[2] : "C:\\pacto\\notas.xml";
            String pathXmlCheques = args.length > 3 ? args[3] : "C:\\pacto\\xml\\cheques.xml";
            String pathLog = args.length > 3 ? args[3] : "C:\\pacto\\log";
            DAO dao = new DAO();
            Connection con = dao.obterConexaoEspecifica(nomeThread);
            importarChequeParaVendaAvulsa(con, pathXmlCheques, pathLog);
        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void importarPagamentos(Connection con, String pathXmlPagamentos) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        String consultaContratosProblematicos = "select id_externo, c.valorfinal, sum(valorparcela) from contrato c inner join movparcela m on m.contrato = c.codigo  where m.descricao like 'PARCELA %' group by 1,2 having c.valorfinal - sum(valorparcela) > 10 order by 1";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(consultaContratosProblematicos, con);
        List<Integer> contratosProblematicos = new ArrayList<Integer>();
        while (consulta.next()) {
            contratosProblematicos.add(consulta.getInt("id_externo"));
        }
        LeitorXML leitorXML = new LeitorXML();
        Map<String, Integer> formasPagamentos = getFormas(con);
        Map<Integer, Integer> bancos = getBancos(con);
        Map<Integer, Integer> operadoras = getOperadoras(con);
        Map<Integer, String> contratosProcessados = new HashMap<Integer, String>();
        List<Element> listaPagamentos = leitorXML.lerXML(pathXmlPagamentos);
        MovPagamento pagDAO = new MovPagamento(con);
        CartaoCredito cartaoDAO = new CartaoCredito(con);
        Cheque chequeDAO = new Cheque(con);
        Integer idExterno = 0;
        int count = 0;
        int perc = 1;
        int porcao = listaPagamentos.size()/10; 
        System.out.println("\n#### iniciando processo de importar pagamentos ####");
        for (Element pag : listaPagamentos) {
            try {
                String idRecebe = retirarNulo(pag, "Id_Recebe");
                idExterno = UteisValidacao.emptyString(idRecebe) ? 0 : Integer.parseInt(idRecebe);
                if (UteisValidacao.emptyNumber(idExterno) || !contratosProblematicos.contains(idExterno)) {
                    continue;
                }
                con.setAutoCommit(false);
                String consultaContrato = "select c.codigo as contrato,c.pessoa,c.empresa, c.datalancamento,r.nomepessoapagador, r.codigo as recibo, m.codigo as parcela, r.valortotal as valorrecibo  from contrato c inner join recibopagamento r on r.contrato = c.codigo inner join movparcela m on m.contrato = c.codigo  where c.id_externo  = " + idExterno + " and m.descricao like 'PARCELA %'";
                ResultSet contratoSet = SuperFacadeJDBC.criarConsulta(consultaContrato, con);
                if (contratoSet.next()) {
                    Integer codContrato = contratoSet.getInt("contrato");
                    Integer codRecibo = contratoSet.getInt("recibo");
                    Integer codParcela = contratoSet.getInt("parcela");
                    Double valorApurado = contratoSet.getDouble("valorrecibo");
                    if (!contratosProcessados.containsKey(codContrato)) {
                        valorApurado = 0.0;
                        SuperFacadeJDBC.executarConsultaUpdate("delete from movpagamento where recibopagamento =" + codRecibo, con);
                        SuperFacadeJDBC.executarConsultaUpdate("delete from pagamentomovparcela where recibopagamento =" + codRecibo, con);
                    }
                    MovPagamentoVO novo = new MovPagamentoVO();
                    novo.setDataLancamento(contratoSet.getTimestamp("datalancamento"));
                    novo.setDataPagamento(contratoSet.getTimestamp("datalancamento"));
                    novo.setDataQuitacao(contratoSet.getTimestamp("datalancamento"));
                    novo.getReciboPagamento().setCodigo(codRecibo);
                    novo.getPessoa().setCodigo(contratoSet.getInt("pessoa"));
                    novo.getPessoa().setNome(contratoSet.getString("nomepessoapagador"));
                    novo.setNomePagador(contratoSet.getString("nomepessoapagador"));
                    novo.getEmpresa().setCodigo(contratoSet.getInt("empresa"));
                    novo.setCredito(Boolean.FALSE);
                    novo.setDepositoCC(Boolean.FALSE);
                    novo.setId_recebe(idExterno);
                    preencherPagamento(pag, novo, operadoras, bancos, formasPagamentos);
                    
                    boolean inserir = true;
                    if(novo.getFormaPagamento().getTipoFormaPagamento().equals("CH") || novo.getFormaPagamento().getTipoFormaPagamento().equals("CA") ){
                          List<MovPagamentoVO> pagamentoVOs = pagDAO.consultarPorCodigoRecibo(codRecibo, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                          for(MovPagamentoVO pagamentoExistente : pagamentoVOs){
                              if(pagamentoExistente.getFormaPagamento().getCodigo().equals(novo.getFormaPagamento().getCodigo())){
                                  Double valorNovo = Uteis.arredondarForcando2CasasDecimais(pagamentoExistente.getValor() + novo.getValor());
                                  if(novo.getFormaPagamento().getTipoFormaPagamento().equals("CH")){
                                      novo.getChequeVOs().get(0).setMovPagamento(pagamentoExistente.getCodigo());
                                      chequeDAO.incluir(novo.getChequeVOs().get(0));
                                      SuperFacadeJDBC.executarConsultaUpdate("UPDATE movpagamento set valor ="+valorNovo+", valortotal = "+ valorNovo +" where codigo = "+ pagamentoExistente.getCodigo(), con);
                                  }
                                  if(novo.getFormaPagamento().getTipoFormaPagamento().equals("CA")){
                                      novo.getCartaoCreditoVOs().get(0).getMovpagamento().setCodigo(pagamentoExistente.getCodigo());
                                      cartaoDAO.incluir(novo.getCartaoCreditoVOs().get(0));
                                      Integer numeroParcela = pagamentoExistente.getNrParcelaCartaoCredito() + 1;
                                      SuperFacadeJDBC.executarConsultaUpdate("UPDATE movpagamento set nrparcelacartaocredito = "+numeroParcela+", valor ="+valorNovo+", valortotal = "+ valorNovo +" where codigo = "+ pagamentoExistente.getCodigo(), con);
                                  }
                                  SuperFacadeJDBC.executarConsultaUpdate("UPDATE pagamentomovparcela set valorpago = "+valorNovo+" where movpagamento = "+ pagamentoExistente.getCodigo(), con);
                                  inserir = false;
                                  break;
                              }
                          }
                    }
                    if(inserir){
                        PagamentoMovParcelaVO novoMPP = new PagamentoMovParcelaVO();
                        novoMPP.setValorPago(novo.getValor());
                        novoMPP.getReciboPagamento().setCodigo(codRecibo);
                        novoMPP.getMovParcela().setCodigo(codParcela);
                        novo.getPagamentoMovParcelaVOs().add(novoMPP);
                        pagDAO.incluirSemCommit(novo);
                        SuperFacadeJDBC.executarConsultaUpdate("UPDATE movpagamento set id_recebe = "+idRecebe+" where codigo = "+ novo.getCodigo(), con);
                    }
                    valorApurado = Uteis.arredondarForcando2CasasDecimais(valorApurado + novo.getValor());
                    SuperFacadeJDBC.executarConsultaUpdate("UPDATE recibopagamento set valortotal = "+valorApurado+" where codigo = "+ codRecibo, con);
                    SuperFacadeJDBC.executarConsultaUpdate("UPDATE movparcela set valorparcela = "+valorApurado+" where codigo = "+ codParcela, con);
                    contratosProcessados.put(codContrato, codRecibo+","+codParcela);
                } else {
                    throw new Exception("Não foram encontrados dados para id_Externo " + idExterno);
                }
                con.commit();
            } catch (Exception e) {
                con.rollback();
                System.out.println("\n#### " + e.getMessage() + " : idExterno ="+idExterno+"  ####");
            } finally {
                con.setAutoCommit(true);
                count++;
                if(count == (porcao*perc)){
                    System.out.println("\n####"+perc*10+ "% do xml foi processado  ####");
                    perc++;
                }
            }
        }
         MovProduto produtoDAO = new MovProduto(con);
         MovParcela parcelaDAO = new MovParcela(con);
         ProdutosPagosServico produtosPagos = new ProdutosPagosServico();
         MovProdutoParcela mppDAO = new MovProdutoParcela(con);
         System.out.println("\n#### iniciando processo de vincular produtos e parcelas ####");
         count = 0;
         perc = 1;
         porcao = contratosProcessados.size() / 10;
        for (Map.Entry<Integer,String> pair : contratosProcessados.entrySet()) {
            try {
                String[] split = pair.getValue().split(",");
                Integer recibo = Integer.parseInt(split[0]);
                Integer codParcela = Integer.parseInt(split[0]);
                SuperFacadeJDBC.executarConsultaUpdate("delete from movprodutoparcela where movparcela = "+codParcela, con);
                MovParcelaVO parcela = parcelaDAO.consultarPorChavePrimaria(codParcela, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Double valorParcela = parcela.getValorParcela();
                List<MovProdutoVO> produtos = produtoDAO.consultarPorCodigoContrato(pair.getKey(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                Ordenacao.ordenarLista(produtos, "codigo");
                for (MovProdutoVO produto : produtos){
                    Double valorPago = 0.0;
                    if(produto.getProduto().getCodigo().equals(9) || produto.getProduto().getCodigo().equals(11) || produto.getProduto().getCodigo().equals(12) || produto.getProduto().getCodigo().equals(10)){
                        if(Uteis.arredondarForcando2CasasDecimais(valorParcela) > Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal())){
                            valorParcela = Uteis.arredondarForcando2CasasDecimais(valorParcela - produto.getTotalFinal());
                            valorPago = Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal());
                        } else {
                            valorPago = Uteis.arredondarForcando2CasasDecimais(valorParcela);
                            valorParcela = 0.0;
                        }
                        MovProdutoParcelaVO novoMPP = new MovProdutoParcelaVO();
                        novoMPP.setValorPago(valorPago);
                        novoMPP.setMovParcela(codParcela);
                        novoMPP.getReciboPagamento().setCodigo(recibo);
                        novoMPP.setMovProduto(produto.getCodigo());
                        mppDAO.incluir(novoMPP);
                        if(valorParcela == 0.0){
                            break;
                        }
                    }
                }
                produtosPagos.setarProdutosPagos(con, recibo);
                System.out.println("\n$$$ contrato ajustado :" + pair.getKey() + " $$$");
            } catch (Exception e) {
                System.out.println("\n#### " + e.getMessage() + " : contrato ="+pair.getKey()+"  ####");
            } finally {
                count++;
                if(count == (porcao*perc)){
                    System.out.println("\n####"+perc*10+ "% dos contratos ajustados foram corrigidos  ####");
                    perc++;
                }
            }
        }
        System.out.println("\n\n\n#### fim processo de importar pagamentos ####");
        parcelaDAO = null;
        mppDAO = null;
        produtosPagos = null;
        pagDAO = null;
        cartaoDAO = null;
        chequeDAO = null;
        produtoDAO = null;

    }

    public static String retirarNulo(Element e, String campo) {
        try {
            return retirarNull(e.getAttributeValue(campo));
        } catch (Exception ie) {
            return null;
        }
    }

    public static String retirarNull(String valor) {
        if (valor.equals("null") || valor.equals("")) {
            return null;
        } else {
            return valor;
        }
    }

    public static Map<Integer, Integer> getBancos(Connection con) throws SQLException, Exception {
        Map<Integer,Integer> bancos = new HashMap<Integer, Integer>();
        String sqlBanco = "SELECT codigo, codigobanco FROM banco";
        ResultSet query = SuperFacadeJDBC.criarConsulta(sqlBanco, con);
        while (query.next()) {
            bancos.put(query.getInt("codigobanco"), query.getInt("codigo"));
        }
        return bancos;
    }
    
    public static Map<Integer, Integer> getOperadoras(Connection con) throws SQLException, Exception {
        Map<Integer, Integer> operadoras = new HashMap<Integer, Integer>();
        String sqlOperadoras = "SELECT codigo, codigooperadora  FROM operadoracartao";
        ResultSet queryOp = con.prepareStatement(sqlOperadoras).executeQuery();
        while(queryOp.next()){
             operadoras.put(queryOp.getInt("codigooperadora"), queryOp.getInt("codigo"));
        }
        return operadoras;
    }
    
    public static Map<String, Integer> getFormas(Connection con) throws SQLException, Exception {
        Map<String, Integer> formas = new HashMap<String, Integer>();
        String sqlOperadoras = "SELECT codigo, tipoformapagamento FROM formapagamento where codigo in (6,5,4,3)";
        ResultSet queryOp = con.prepareStatement(sqlOperadoras).executeQuery();
        while(queryOp.next()){
             formas.put(queryOp.getString("tipoformapagamento"), queryOp.getInt("codigo"));
        }
        return formas;
    }

    private static void preencherPagamento(Element e,MovPagamentoVO novo, Map<Integer, Integer> operadoras ,  Map<Integer, Integer> bancos ,  Map<String, Integer> formas) throws Exception {
                novo.setId_recebe(Integer.parseInt(retirarNulo(e, "Id_Recebe")));
                novo.getResponsavelPagamento().setCodigo(2);
                novo.setMovPagamentoEscolhida(Boolean.TRUE);
		novo.setValor(Double.valueOf(retirarNulo(e, "Valor")));
                novo.setValorTotal(novo.getValor());
                novo.setObservacao(retirarNulo(e, "Observacao"));
                String formaPgExt = retirarNulo(e, "FormaPgExt");
                if(formaPgExt.equals("Cheque") || formaPgExt.equals("Outros")){
                    novo.getFormaPagamento().setCodigo(formas.get("CH"));
                    novo.getFormaPagamento().setTipoFormaPagamento("CH");
		} else if(formaPgExt.equals("Dinheiro") || formaPgExt.equals("Banco")){
                    novo.getFormaPagamento().setCodigo(formas.get("AV"));
                    novo.getFormaPagamento().setTipoFormaPagamento("AV");
		}else if(formaPgExt.equals("Cartao Credito") || formaPgExt.equals("Cartão")){
                    novo.getFormaPagamento().setTipoFormaPagamento("CA");
                    novo.getFormaPagamento().setCodigo(formas.get("CA"));
		} else 	if(formaPgExt.equals("Cartao Debito")){
                    novo.getFormaPagamento().setTipoFormaPagamento("CD");
                    novo.getFormaPagamento().setCodigo(formas.get("CD"));
		} 
		
                if(novo.getFormaPagamento().getTipoFormaPagamento().equals("CH")){
                    ChequeVO cheque = new ChequeVO();
                    cheque.setValor(novo.getValor());
                    cheque.setValorTotal(novo.getValor());
                    cheque.setNumero(UteisValidacao.emptyString(retirarNulo(e, "Nr_Doc")) ? "impor" : retirarNulo(e, "Nr_Doc"));
                    Integer banco = UteisValidacao.emptyString(retirarNulo(e, "Banco")) ? bancos.get(0) : bancos.get(Integer.parseInt(retirarNulo(e, "Banco")));
                    cheque.getBanco().setCodigo(UteisValidacao.emptyNumber(banco) ? bancos.get(0) : banco);
                    cheque.setDataCompensacao(Formatador.formataData(retirarNulo(e,"Dt_Vencimento")));
                    String vistaPrazo = retirarNulo(e, "AVistaPrazo");
                    cheque.setVistaOuPrazo(UteisValidacao.emptyString(vistaPrazo) || vistaPrazo.equals("P") ? "PR" : "AV");
                    cheque.setNomeNoCheque(retirarNulo(e, "Nome_NoChequeCartao"));
                    cheque.setConta(UteisValidacao.emptyString(retirarNulo(e, "ContaCorrente")) ? "impor" : retirarNulo(e, "ContaCorrente"));
                    cheque.setAgencia(UteisValidacao.emptyString(retirarNulo(e, "Agencia")) ? "impor" : retirarNulo(e, "Agencia"));
                    cheque.setSituacao("EA");
                    novo.getChequeVOs().add(cheque);
                }
                if(novo.getFormaPagamento().getTipoFormaPagamento().equals("CA") || novo.getFormaPagamento().getTipoFormaPagamento().equals("CD")){
                    String operadora = retirarNulo(e, "Nome_Banco") == null ? "" : retirarNulo(e, "Nome_Banco");
                     if(operadora.equals("MASTER CARD CRÉDITO")){
                         novo.getOperadoraCartaoVO().setCodigo(operadoras.get(904));
                     } else if(operadora.equals("MASTER CARD DÉBITO")){
                         novo.getOperadoraCartaoVO().setCodigo(operadoras.get(907));
                     } else if(operadora.equals("VISA DÉBITO")){
                         novo.getOperadoraCartaoVO().setCodigo(operadoras.get(901));
                     } else if(operadora.equals("VISA CRÉDITO")){
                         novo.getOperadoraCartaoVO().setCodigo(operadoras.get(902));
                     } else {
                         novo.getOperadoraCartaoVO().setCodigo(operadoras.get(99999));
                     }
                     novo.setAutorizacaoCartao(retirarNulo(e, "NomeOuAuto"));
                }
                if(novo.getFormaPagamento().getTipoFormaPagamento().equals("CA")){
                     CartaoCreditoVO cartaoCredito = new CartaoCreditoVO();
                     cartaoCredito.setValor(novo.getValor());
                     cartaoCredito.setValorTotal(novo.getValor());
                     cartaoCredito.setOperadora(novo.getOperadoraCartaoVO());
                     cartaoCredito.setDataCompensacao(Formatador.formataData(retirarNulo(e,"Dt_Vencimento")));
                     cartaoCredito.setSituacao("EA");
                     novo.getCartaoCreditoVOs().add(cartaoCredito);
                     novo.setNrParcelaCartaoCredito(1);
                }
                if(!novo.getFormaPagamento().getTipoFormaPagamento().equals("CH") && !novo.getFormaPagamento().getTipoFormaPagamento().equals("CA")){
                    novo.setDataLancamento(Formatador.formataData(retirarNulo(e,"Dt_Vencimento")));
                    novo.setDataPagamento(Formatador.formataData(retirarNulo(e,"Dt_Vencimento")));
                    novo.setDataQuitacao(Formatador.formataData(retirarNulo(e,"Dt_Vencimento")));
                }
		
		
		
    }
    
    public static void ajustarRelacionamentoProdutoParcela(Connection con) throws Exception{
        Conexao.guardarConexaoForJ2SE(con);
        String consultaContratosProblematicos = "select m.contrato,m.codigo as movparcela,max(pm.recibopagamento)as recibopagamento, max(valorparcela), sum(mpp.valorpago)  from movparcela m inner join contrato c on c.codigo = m.contrato inner join movprodutoparcela mpp on mpp.movparcela  = m.codigo\n" +
        "inner join pagamentomovparcela pm on pm.movparcela = m.codigo where c.importacao and m.descricao like 'PARCELA %' group by 1,2 having max(valorparcela) - sum(mpp.valorpago) > 10 order by 1 desc";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(consultaContratosProblematicos, con);
        MovProduto produtoDAO = new MovProduto(con);
         MovParcela parcelaDAO = new MovParcela(con);
         ProdutosPagosServico produtosPagos = new ProdutosPagosServico();
         MovProdutoParcela mppDAO = new MovProdutoParcela(con);
         System.out.println("\n#### iniciando processo de vincular produtos e parcelas ####");
         Integer contrato = 0;
         while (consulta.next()) {
            try {
                Integer recibo = consulta.getInt("recibopagamento");
                Integer codParcela = consulta.getInt("movparcela");
                contrato = consulta.getInt("contrato");
                SuperFacadeJDBC.executarConsultaUpdate("delete from movprodutoparcela where movparcela = "+codParcela, con);
                MovParcelaVO parcela = parcelaDAO.consultarPorChavePrimaria(codParcela, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Double valorParcela = parcela.getValorParcela();
                List<MovProdutoVO> produtos = produtoDAO.consultarPorCodigoContrato(contrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                Ordenacao.ordenarLista(produtos, "codigo");
                for (MovProdutoVO produto : produtos){
                    Double valorPago = 0.0;
                    if(produto.getProduto().getCodigo().equals(9) || produto.getProduto().getCodigo().equals(11) || produto.getProduto().getCodigo().equals(12) || produto.getProduto().getCodigo().equals(10)){
                        if(Uteis.arredondarForcando2CasasDecimais(valorParcela) > Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal())){
                            valorParcela = Uteis.arredondarForcando2CasasDecimais(valorParcela - produto.getTotalFinal());
                            valorPago = Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal());
                        } else {
                            valorPago = Uteis.arredondarForcando2CasasDecimais(valorParcela);
                            valorParcela = 0.0;
                        }
                        MovProdutoParcelaVO novoMPP = new MovProdutoParcelaVO();
                        novoMPP.setValorPago(valorPago);
                        novoMPP.setMovParcela(codParcela);
                        novoMPP.getReciboPagamento().setCodigo(recibo);
                        novoMPP.setMovProduto(produto.getCodigo());
                        mppDAO.incluir(novoMPP);
                        if(valorParcela == 0.0){
                            break;
                        }
                    }
                }
                produtosPagos.setarProdutosPagos(con, recibo);
                System.out.println("\n$$$ contrato ajustado :" + contrato + " $$$");
            } catch (Exception e) {
                System.out.println("\n#### " + e.getMessage() + " : contrato ="+contrato+"  ####");
            } finally {
            }
        }
        
        parcelaDAO = null;
        mppDAO = null;
        produtosPagos = null;
        produtoDAO = null;

    }
    
    
    public static void importarPagamentosOutros(Connection con, String pathXmlPagamentos) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        LeitorXML leitorXML = new LeitorXML();
        Map<String, Integer> formasPagamentos = getFormas(con);
        Map<Integer, Integer> bancos = getBancos(con);
        Map<Integer, Integer> operadoras = getOperadoras(con);
        Map<Integer, String> contratosProcessados = new HashMap<Integer, String>();
        List<Element> listaPagamentos = leitorXML.lerXML(pathXmlPagamentos);
        MovPagamento pagDAO = new MovPagamento(con);
        CartaoCredito cartaoDAO = new CartaoCredito(con);
        Cheque chequeDAO = new Cheque(con);
        Integer idExterno = 0;
        int count = 0;
        int perc = 1;
        int porcao = listaPagamentos.size()/10; 
        System.out.println("\n\n\n#### iniciando processo de reimportar pagamentos. "+listaPagamentos.size()+" pagamentos serão verificados ####");
        for (Element pag : listaPagamentos) {
            try {
                String idRecebe = retirarNulo(pag, "Id_Recebe");
                idExterno = UteisValidacao.emptyString(idRecebe) ? 0 : Integer.parseInt(idRecebe);
                if (UteisValidacao.emptyNumber(idExterno) || !"Outros".equals(retirarNulo(pag, "FormaPgExt"))) {
                    continue;
                }
                con.setAutoCommit(false);
                String consultaContrato = "select c.codigo as contrato,c.pessoa,c.empresa, c.datalancamento,r.nomepessoapagador, r.codigo as recibo, m.codigo as parcela, r.valortotal as valorrecibo  from contrato c inner join recibopagamento r on r.contrato = c.codigo inner join movparcela m on m.contrato = c.codigo  where c.id_externo  = " + idExterno + " and m.descricao like 'PARCELA 1'";
                ResultSet contratoSet = SuperFacadeJDBC.criarConsulta(consultaContrato, con);
                if (contratoSet.next()) {
                    Integer codContrato = contratoSet.getInt("contrato");
                    Integer codRecibo = contratoSet.getInt("recibo");
                    Integer codParcela = contratoSet.getInt("parcela");
                    String existeEdicao = "select codigo from movparcela where contrato = "+codContrato+"  and descricao  like 'PARCELA EDIÇÃO DE%'";
                    String existeAlteracao = "select codigo from movpagamento where recibopagamento = "+codRecibo+" and  dataalteracaomanual  is not null";
                    if(SuperFacadeJDBC.existe(existeEdicao, con) || SuperFacadeJDBC.existe(existeAlteracao, con)){
                         throw new Exception(" Houveram edições no recibo ou o recibo foi lançado novamente usando alteração da data base. Por isso pagamentos não foram ajustados. Contrato " + codContrato);
                    }
                    MovPagamentoVO novo = new MovPagamentoVO();
                    novo.setDataLancamento(contratoSet.getTimestamp("datalancamento"));
                    novo.setDataPagamento(contratoSet.getTimestamp("datalancamento"));
                    novo.setDataQuitacao(contratoSet.getTimestamp("datalancamento"));
                    novo.getReciboPagamento().setCodigo(codRecibo);
                    novo.getPessoa().setCodigo(contratoSet.getInt("pessoa"));
                    novo.getPessoa().setNome(contratoSet.getString("nomepessoapagador"));
                    novo.setNomePagador(contratoSet.getString("nomepessoapagador"));
                    novo.getEmpresa().setCodigo(contratoSet.getInt("empresa"));
                    novo.setCredito(Boolean.FALSE);
                    novo.setDepositoCC(Boolean.FALSE);
                    novo.setId_recebe(idExterno);
                    preencherPagamento(pag, novo, operadoras, bancos, formasPagamentos);
                    boolean inserir = true;
                    List<MovPagamentoVO> pagamentoVOs = pagDAO.consultarPorCodigoRecibo(codRecibo, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    MovPagamentoVO existente = null;
                    MovPagamentoVO outro = null;
                    for(MovPagamentoVO pagamentoExistente : pagamentoVOs){
                        if(pagamentoExistente.getFormaPagamento().getTipoFormaPagamento().equals("AV") && Uteis.arredondarForcando2CasasDecimais(pagamentoExistente.getValor()) >= Uteis.arredondarForcando2CasasDecimais(novo.getValor())){
                            if(Calendario.igual(pagamentoExistente.getDataPagamento(),novo.getDataPagamento())){
                                existente = pagamentoExistente;
                            } else {
                                outro = pagamentoExistente;
                            }
                        }
                    }
                    if(existente == null){ //feito para tentar encontrar um pagamento igual
                        if(outro == null){
                            throw new Exception(" Não foram encontratos pagamento para substituição dos dados " + codContrato);
                        }else {
                           existente = outro;
                        }
                    }
                    if(novo.getFormaPagamento().getTipoFormaPagamento().equals("CH") || novo.getFormaPagamento().getTipoFormaPagamento().equals("CA") ){
                          pagamentoVOs = pagDAO.consultarPorCodigoRecibo(codRecibo, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                          for(MovPagamentoVO pagamentoExistente : pagamentoVOs){
                              if(pagamentoExistente.getFormaPagamento().getCodigo().equals(novo.getFormaPagamento().getCodigo())){
                                  Double valorNovo = Uteis.arredondarForcando2CasasDecimais(pagamentoExistente.getValor() + novo.getValor());
                                  if(novo.getFormaPagamento().getTipoFormaPagamento().equals("CH")){
                                      novo.getChequeVOs().get(0).setMovPagamento(pagamentoExistente.getCodigo());
                                      chequeDAO.incluir(novo.getChequeVOs().get(0));
                                      SuperFacadeJDBC.executarConsultaUpdate("UPDATE movpagamento set valor ="+valorNovo+", valortotal = "+ valorNovo +" where codigo = "+ pagamentoExistente.getCodigo(), con);
                                  }
                                  if(novo.getFormaPagamento().getTipoFormaPagamento().equals("CA")){
                                      novo.getCartaoCreditoVOs().get(0).getMovpagamento().setCodigo(pagamentoExistente.getCodigo());
                                      cartaoDAO.incluir(novo.getCartaoCreditoVOs().get(0));
                                      Integer numeroParcela = pagamentoExistente.getNrParcelaCartaoCredito() + 1;
                                      SuperFacadeJDBC.executarConsultaUpdate("UPDATE movpagamento set nrparcelacartaocredito = "+numeroParcela+", valor ="+valorNovo+", valortotal = "+ valorNovo +" where codigo = "+ pagamentoExistente.getCodigo(), con);
                                  }
                                  SuperFacadeJDBC.executarConsultaUpdate("UPDATE pagamentomovparcela set valorpago = "+valorNovo+" where movpagamento = "+ pagamentoExistente.getCodigo(), con);
                                  inserir = false;
                                  break;
                              }
                          }
                    }
                    if(inserir){
                        PagamentoMovParcelaVO novoMPP = new PagamentoMovParcelaVO();
                        novoMPP.setValorPago(novo.getValor());
                        novoMPP.getReciboPagamento().setCodigo(codRecibo);
                        novoMPP.getMovParcela().setCodigo(codParcela);
                        novo.getPagamentoMovParcelaVOs().add(novoMPP);
                        pagDAO.incluirSemCommit(novo);
                        SuperFacadeJDBC.executarConsultaUpdate("UPDATE movpagamento set id_recebe = "+idRecebe+" where codigo = "+ novo.getCodigo(), con);
                    }
                    if(Uteis.arredondarForcando2CasasDecimais(existente.getValor()) > Uteis.arredondarForcando2CasasDecimais(novo.getValor())){
                        Double valorNovo = Uteis.arredondarForcando2CasasDecimais(existente.getValor() - novo.getValor());
                        SuperFacadeJDBC.executarConsultaUpdate("UPDATE movpagamento set valor ="+valorNovo+", valortotal = "+ valorNovo +" where codigo = "+ existente.getCodigo(), con);
                        SuperFacadeJDBC.executarConsultaUpdate("UPDATE pagamentomovparcela set valorpago = "+valorNovo+" where movpagamento = "+ existente.getCodigo(), con);
                    } else {
                        SuperFacadeJDBC.executarConsultaUpdate("delete from pagamentomovparcela where movpagamento = "+ existente.getCodigo(), con);
                        SuperFacadeJDBC.executarConsultaUpdate("delete from movpagamento where codigo = "+ existente.getCodigo(), con);
                    }
                    contratosProcessados.put(codContrato, codRecibo+","+codParcela);
                } else {
                    throw new Exception("Não foram encontrados dados para id_Externo " + idExterno);
                }
                con.commit();
            } catch (Exception e) {
                con.rollback();
                System.out.println("\n#### " + e.getMessage() + " : idExterno ="+idExterno+"  ####");
            } finally {
                con.setAutoCommit(true);
                count++;
                if(count == (porcao*perc)){
                    System.out.println("\n####"+perc*10+ "% do xml foi processado  ####");
                    perc++;
                }
            }
        }
        SuperFacadeJDBC.executarConsultaUpdate("UPDATE cheque set numero = codigo where  numero = 'impor'", con);
        SuperFacadeJDBC.executarConsultaUpdate("UPDATE cheque set agencia = codigo where  agencia = 'impor'", con);
        SuperFacadeJDBC.executarConsultaUpdate("UPDATE cheque set conta = codigo where  conta = 'impor'", con);
         ProdutosPagosServico produtosPagos = new ProdutosPagosServico();
         MovProdutoParcela mppDAO = new MovProdutoParcela(con);
         System.out.println("\n#### iniciando processo de vincular produtos e parcelas ####");
         count = 0;
         perc = 1;
         porcao = contratosProcessados.size() / 10;
        for (Map.Entry<Integer,String> pair : contratosProcessados.entrySet()) {
            try {
                String[] split = pair.getValue().split(",");
                Integer recibo = Integer.parseInt(split[0]);
                Integer codParcela = Integer.parseInt(split[0]);
                produtosPagos.setarProdutosPagos(con, recibo);
                System.out.println("\n$$$ contrato ajustado :" + pair.getKey() + " $$$");
            } catch (Exception e) {
                System.out.println("\n#### " + e.getMessage() + " : contrato ="+pair.getKey()+"  ####");
            } finally {
                count++;
                if(count == (porcao*perc)){
                    System.out.println("\n####"+perc*10+ "% dos contratos ajustados foram corrigidos  ####");
                    perc++;
                }
            }
        }
        System.out.println("\n\n\n#### finalizado processo de reimportar pagamentos. "+listaPagamentos.size()+" pagamentos forão verificados ####");
        mppDAO = null;
        produtosPagos = null;
        pagDAO = null;
        cartaoDAO = null;
        chequeDAO = null;
    }
    
     public static void importarDadosNotasEmitadas(Connection con, String pathXmlNotas) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        LeitorXML leitorXML = new LeitorXML();
        Map<String, Integer> formasPagamentos = getFormas(con);
        Map<Integer, Integer> bancos = getBancos(con);
        Map<Integer, Integer> operadoras = getOperadoras(con);
        Map<Integer, String> contratosProcessados = new HashMap<Integer, String>();
        List<Element> listaNotas = leitorXML.lerXML(pathXmlNotas);
        NFSeEmitida nfseDao = new NFSeEmitida(con);
        ProdutosPagosServico produtosPagos = new ProdutosPagosServico();
        Integer idExterno = 0;
        int count = 0;
        int perc = 1;
        int porcao = listaNotas.size()/10;
        Date dataEmissao;
        String nrNota = "";
        Double valorPagamento;
        Double valorNota;
        MovPagamento pagamentoDAO = new MovPagamento(con);
        PagamentoMovParcela pmpDAO = new PagamentoMovParcela(con);
        System.out.println("\n#### iniciando processo de importar notas. "+listaNotas.size()+" notas serão verificados ####");
        for (Element nota : listaNotas) {
            try {
                String idRecebe = retirarNulo(nota, "Id_Recebe");
                idExterno = UteisValidacao.emptyString(idRecebe) ? 0 : Integer.parseInt(idRecebe);
                if (UteisValidacao.emptyNumber(idExterno)) {
                    continue;
                }
                dataEmissao =  Formatador.formataData(retirarNulo(nota,"Dt_EmissaoNF"));
                nrNota = retirarNulo(nota, "Nr_NotaFiscal");
                valorPagamento = Double.valueOf(retirarNulo(nota, "ValorFormaPg"));
                try{
                    valorNota = Double.valueOf(retirarNulo(nota, "ValorNotaFiscal"));
                }catch(Exception e){
                    valorNota = valorPagamento;
                }
                con.setAutoCommit(false);
                String consultaPagamentos = "select * from (select c.codigo as contrato, mp.recibopagamento,mp.codigo as pagamento,0 as cartao,0 as cheque, fp.tipoformapagamento,mp.datapagamento as compensacao, mp.valortotal as valorPagamento from contrato c inner join movparcela p on p.contrato = c.codigo \n" +
                        "inner join pagamentomovparcela pmp on pmp.movparcela = p.codigo \n" +
                        "inner join movpagamento mp on mp.codigo = pmp.movpagamento \n" +
                        "inner join formapagamento fp on fp.codigo = mp.formapagamento \n" +
                        " where c.id_externo = "+idExterno+" and  fp.tipoformapagamento not in ('CH', 'CA') and not exists (select * from nfseemitida  where movpagamento = mp.codigo)\n" +
                        " union all\n" +
                        "select c.codigo as contrato,mp.recibopagamento,mp.codigo as pagamento,0 as cartao,ch.codigo as cheque, fp.tipoformapagamento,ch.datacompesancao as compensacao, ch.valortotal as valorPagamento  from contrato c inner join movparcela p on p.contrato = c.codigo \n" +
                        "inner join pagamentomovparcela pmp on pmp.movparcela = p.codigo \n" +
                        "inner join movpagamento mp on mp.codigo = pmp.movpagamento \n" +
                        "inner join formapagamento fp on fp.codigo = mp.formapagamento \n" +
                        "inner join cheque ch on ch.movpagamento = mp.codigo \n" +
                        " where c.id_externo = "+idExterno+" and  fp.tipoformapagamento = 'CH' and not exists (select * from nfseemitida  where cheque = ch.codigo)\n" +
                        "  union all\n" +
                        "select c.codigo as contrato,mp.recibopagamento,mp.codigo as pagamento,ca.codigo as cartao,0 as cheque, fp.tipoformapagamento,ca.datacompesancao as compensacao, ca.valortotal as valorPagamento  from contrato c inner join movparcela p on p.contrato = c.codigo \n" +
                        "inner join pagamentomovparcela pmp on pmp.movparcela = p.codigo \n" +
                        "inner join movpagamento mp on mp.codigo = pmp.movpagamento \n" +
                        "inner join formapagamento fp on fp.codigo = mp.formapagamento \n" +
                        "inner join cartaocredito ca on ca.movpagamento = mp.codigo \n" +
                        " where c.id_externo = "+idExterno+" and  fp.tipoformapagamento = 'CA' and not exists (select * from nfseemitida  where cartaocredito = ca.codigo)) as foo where valorPagamento::numeric = "+valorNota+" order by compensacao";
                ResultSet pagamentoSet = SuperFacadeJDBC.criarConsulta(consultaPagamentos, con);
                List<NFSeEmitidaVO> listaPagamentos = new ArrayList<NFSeEmitidaVO>();
                while (pagamentoSet.next()) {
                    NFSeEmitidaVO novo = new NFSeEmitidaVO();
                    novo.getContrato().setCodigo(pagamentoSet.getInt("contrato"));
                    novo.getRecibo().setCodigo(pagamentoSet.getInt("recibopagamento"));
                    novo.getMovPagamento().setCodigo(pagamentoSet.getInt("pagamento"));
                    novo.getCartaoCredito().setCodigo(pagamentoSet.getInt("cartao"));
                    novo.getCheque().setCodigo(pagamentoSet.getInt("cheque"));
                    novo.setDataLancamento(pagamentoSet.getDate("compensacao")); // uso essa data apenas para a comparação
                    listaPagamentos.add(novo);
                }
                if(!listaPagamentos.isEmpty()){
                    boolean encontrado = false;
                    for(NFSeEmitidaVO pagamento : listaPagamentos){ //tenta achar a data de emissão no mesmo dia da compensação
                        if(Calendario.igual(dataEmissao, pagamento.getDataLancamento())){
                            pagamento.setNrNotaManual(nrNota);
                            nfseDao.incluir(pagamento);
                            encontrado = true;
                            break;
                        }
                    }
                    if(!encontrado){
                        for(NFSeEmitidaVO pagamento : listaPagamentos){ //tenta achar a data de emissão no mesmo mês da compensação
                            if(dataEmissao.getYear() == pagamento.getDataLancamento().getYear() && dataEmissao.getMonth() ==  pagamento.getDataLancamento().getMonth()){
                                pagamento.setNrNotaManual(nrNota);
                                nfseDao.incluir(pagamento);
                                encontrado = true;
                                break;
                            }
                        }
                        if(!encontrado){
                            for(NFSeEmitidaVO pagamento : listaPagamentos){ //adiciona o primeiro da lista caso não seja encontrado relação entre as datas
                                pagamento.setNrNotaManual(nrNota);
                                nfseDao.incluir(pagamento);
                                encontrado = true;
                                break;
                            }
                        }
                    }
                } else {
                    consultaPagamentos = "select * from (select c.codigo as contrato, mp.recibopagamento,mp.codigo as pagamento,0 as cartao,0 as cheque, fp.tipoformapagamento,mp.datapagamento as compensacao, mp.valortotal as valorPagamento from contrato c inner join movparcela p on p.contrato = c.codigo \n" +
                        "inner join pagamentomovparcela pmp on pmp.movparcela = p.codigo \n" +
                        "inner join movpagamento mp on mp.codigo = pmp.movpagamento \n" +
                        "inner join formapagamento fp on fp.codigo = mp.formapagamento \n" +
                        " where c.id_externo = "+idExterno+" and  fp.tipoformapagamento not in ('CH', 'CA') and not exists (select * from nfseemitida  where movpagamento = mp.codigo)\n" +
                        ") as foo where valorPagamento::numeric > "+valorNota+" order by compensacao";
                    pagamentoSet = SuperFacadeJDBC.criarConsulta(consultaPagamentos, con);
                    listaPagamentos = new ArrayList<NFSeEmitidaVO>();
                    if (pagamentoSet.next()) {
                        MovPagamentoVO pagamento = pagamentoDAO.consultarPorChavePrimaria(pagamentoSet.getInt("pagamento"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        PagamentoMovParcelaVO pmpVO = (PagamentoMovParcelaVO) pmpDAO.consultarPagamentoMovParcelas(pagamento.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).get(0);
                        pagamento.setValor(Uteis.arredondarForcando2CasasDecimais(pagamento.getValor() - valorNota));
                        pagamento.setValorTotal(pagamento.getValor());
                        SuperFacadeJDBC.executarConsultaUpdate("UPDATE movpagamento set valor = "+pagamento.getValor()+", valortotal = "+pagamento.getValor()+" where  codigo = "+pagamento.getCodigo(), con);
                        SuperFacadeJDBC.executarConsultaUpdate("UPDATE pagamentomovparcela set valorpago = "+pagamento.getValor()+" where  movpagamento = "+pagamento.getCodigo(), con);
                        
                        pagamento.setValor(valorNota);
                        pagamento.setValorTotal(valorNota);
                        PagamentoMovParcelaVO novoMPP = new PagamentoMovParcelaVO();
                        novoMPP.setValorPago(pagamento.getValor());
                        novoMPP.getReciboPagamento().setCodigo(pagamento.getReciboPagamento().getCodigo());
                        novoMPP.getMovParcela().setCodigo(pmpVO.getMovParcela().getCodigo());
                        pagamento.getPagamentoMovParcelaVOs().add(novoMPP);
                        pagamentoDAO.incluirSemCommit(pagamento);
                        NFSeEmitidaVO novo = new NFSeEmitidaVO();
                        novo.getContrato().setCodigo(pagamentoSet.getInt("contrato"));
                        novo.getRecibo().setCodigo(pagamentoSet.getInt("recibopagamento"));
                        novo.getMovPagamento().setCodigo(pagamento.getCodigo());
                        novo.getCartaoCredito().setCodigo(pagamentoSet.getInt("cartao"));
                        novo.getCheque().setCodigo(pagamentoSet.getInt("cheque"));
                        novo.setNrNotaManual(nrNota);
                        nfseDao.incluir(novo);
                        produtosPagos.setarProdutosPagos(con, pagamento.getReciboPagamento().getCodigo());
                    } else {
                        if(dataEmissao.getYear() >= (Calendario.hoje().getYear() - 1)){
                            throw new Exception("Não foram encontrados dados para id_Externo " + idExterno + " da nota de número: "+nrNota+" no valor de "+valorNota);
                        }
                    }
                }
                con.commit();
            } catch (Exception e) {
                con.rollback();
                System.out.println("\n#### ERRO: " + e.getMessage() + " : idExterno ="+idExterno+" nota: "+nrNota+"  ####");
            } finally {
                con.setAutoCommit(true);
                count++;
                if(count == (porcao*perc)){
                    System.out.println("\n####"+perc*10+ "% do xml foi processado  ####");
                    perc++;
                }
            }
        }
    }
     
    public static void importarChequeParaVendaAvulsa(Connection con, String pathXmlCheques, String pathDiretorioLog) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        FileWriter writerCSVIncluidos = new FileWriter(pathDiretorioLog+"\\chequesImportados.csv");
        FileWriter writerCSVAlterados= new FileWriter(pathDiretorioLog+"\\chequesAlterados.csv");
        FileWriter writerCSVVerificar= new FileWriter(pathDiretorioLog+"\\chequesVerificar.csv");
        FileWriter writerErros = new FileWriter(pathDiretorioLog+"\\errosImportacao.txt");
        LeitorXML leitorXML = new LeitorXML();
        Map<String, Integer> formasPagamentos = getFormas(con);
        Map<Integer, Integer> bancos = getBancos(con);
        List<Element> listaCheques = leitorXML.lerXML(pathXmlCheques);
        MovPagamento pagDAO = new MovPagamento(con);
        MovParcela parcelaDAO = new MovParcela(con);
        Cliente clienteDAO = new Cliente(con);
        System.out.println("\n#### iniciando processo de importar pagamentos ####");
        String strChequeExiste = "select %s from cheque where movpagamento in (select codigo from movpagamento  where pessoa in (select pessoa from cliente where codigomatricula = %s )) and ltrim(trim(agencia),'0') = '%s'  and ltrim(trim(conta),'0') ='%s' and ltrim(trim(numero),'0') = '%s'";
        Integer codigoContratoZW = 0;
        writerCSVIncluidos.write("matricula;aluno;recebimentoZD;contratoZW;datalancamento;agencia;conta;numero;datacompensacao;valor;operacao;observacao\n");
        writerCSVAlterados.write("matricula;aluno;recebimentoZD;contratoZW;datalancamento;agencia;conta;numero;datacompensacao;valor;operacao;observacao\n");
        writerCSVVerificar.write("matricula;aluno;recebimentoZD;contratoZW;datalancamento;agencia;conta;numero;datacompensacao;valor;operacao;observacao\n");
        ClienteVO cliente = new ClienteVO();
        String idRecebe = "";
        String matricula = "";
        for (Element pag : listaCheques ) {
            try {
                idRecebe = retirarNulo(pag, "Id_Recebe");
                matricula = retirarNulo(pag, "Cd_Aluno");
                boolean devolvido = Boolean.valueOf(retirarNulo(pag, "Devolvido"));
                Date dataLancamento = Formatador.formataData(retirarNulo(pag,"dt_GravacaoRecebimento"));;
                Calendario.dia = dataLancamento;
                con.setAutoCommit(false);
                ChequeVO cheque = preencherCheque(pag, bancos);
                if(cheque.getDataCompensacao().getYear() > 122){
                    continue;
                }
                ResultSet rsChequesExistentes = SuperFacadeJDBC.criarConsulta(String.format(strChequeExiste, "*",matricula,cheque.getAgencia().trim().replaceFirst("0*", ""),cheque.getConta().trim().replaceFirst("0*", ""), cheque.getNumero().trim().replaceFirst("0*", "")), con);
                boolean inserirCheque = true;
                boolean cancelado = false;
                double valorTotal = 0.0;
                while(rsChequesExistentes.next()){
//                    if (Math.abs((int) Uteis.nrDiasEntreDatas(cheque.getDataCompensacao(), rsChequesExistentes.getDate("dataoriginal")  == null ? rsChequesExistentes.getDate("datacompesancao"): rsChequesExistentes.getDate("dataoriginal") )) > 30) {
//                        continue;
//                    }
                    inserirCheque = false;
                    if(devolvido && !rsChequesExistentes.getString("situacao").equals("CA")){
                        cliente = clienteDAO.consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                        SuperFacadeJDBC.executarConsultaUpdate("UPDATE cheque set situacao = 'CA' where codigo in ("+ String.format(strChequeExiste, "codigo",matricula,cheque.getAgencia().trim().replaceFirst("0*", ""),cheque.getConta().trim().replaceFirst("0*", ""), cheque.getNumero().trim().replaceFirst("0*", ""))+")", con);
                        writerCSVAlterados.append(cliente.getMatricula()+";"+cliente.getPessoa().getNome()+";"+idRecebe+";"+0+";"+Uteis.getData(dataLancamento)+";"+cheque.getAgencia()+";"+cheque.getConta()+";"+cheque.getNumero()+";"+cheque.getDataCompensacao_Apresentar()+";"+cheque.getValor()+";");
                        writerCSVAlterados.append("ALTERAÇÃO;Foi devolvido no desk e teve sua situacão alterada no ZW \n");
                        cancelado = true;
                        break;
                    } else if(!rsChequesExistentes.getString("situacao").equals("CA")){
                        valorTotal = Uteis.arredondarForcando2CasasDecimais(valorTotal + rsChequesExistentes.getDouble("valor"));
                    } 
                }
                if(!cancelado && !inserirCheque && !devolvido && valorTotal > 0 && Uteis.arredondarForcando2CasasDecimais(valorTotal - Uteis.arredondarForcando2CasasDecimais(cheque.getValor())) > 1.0){
                    cliente = clienteDAO.consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    writerCSVVerificar.append(cliente.getMatricula()+";"+cliente.getPessoa().getNome()+";"+idRecebe+";"+0+";"+Uteis.getData(dataLancamento)+";"+cheque.getAgencia()+";"+cheque.getConta()+";"+cheque.getNumero()+";"+cheque.getDataCompensacao_Apresentar()+";"+cheque.getValor()+";");
                    writerCSVVerificar.append("VERIFICAR;cheque no Desk é no valor de R$ "+cheque.getValor()+" e no ZW R$ "+valorTotal+" \n");
                }
                if(inserirCheque && !devolvido){
                    cliente = clienteDAO.consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    String consultaContrato = "select codigo as contrato from contrato where  id_externo = " + idRecebe + ";";
                    ResultSet contratoSet = SuperFacadeJDBC.criarConsulta(consultaContrato, con);
                    codigoContratoZW = 0;
                    if (contratoSet.next()) {      
                        codigoContratoZW = contratoSet.getInt("contrato");
                    } 
                    MovPagamentoVO novo = new MovPagamentoVO();
                    novo.setDataLancamento(dataLancamento);
                    novo.setDataPagamento(dataLancamento);
                    novo.setDataQuitacao(dataLancamento);
                    novo.getPessoa().setCodigo(cliente.getPessoa().getCodigo());
                    novo.getPessoa().setNome(cliente.getPessoa().getNome());
                    novo.setNomePagador(cliente.getPessoa().getNome());
                    novo.setEmpresa(cliente.getEmpresa());
                    novo.setCredito(Boolean.FALSE);
                    novo.setDepositoCC(Boolean.FALSE);
                    novo.setId_recebe(Integer.parseInt(idRecebe));
                    novo.getResponsavelPagamento().setCodigo(2);
                    novo.setMovPagamentoEscolhida(Boolean.TRUE);
                    novo.setObservacao(retirarNulo(pag, "Observacao"));
                    novo.getFormaPagamento().setCodigo(formasPagamentos.get("CH"));
                    novo.getFormaPagamento().setTipoFormaPagamento("CH");
                    novo.setValor(cheque.getValor());
                    novo.setValorTotal(cheque.getValor());
                    novo.getChequeVOs().add(cheque);
                    VendaAvulsaVO venda = gerarVendaAvulsa(con,cliente, novo.getValor(),dataLancamento, 277, idRecebe);
                    List<MovPagamentoVO> pagamentos = new ArrayList<MovPagamentoVO>();
                    List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();
                    pagamentos.add(novo);
                    MovParcelaVO movParcela = parcelaDAO.consultarPorCodigoVendaAvulsa(venda.getCodigo(),"EA",
                    false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    movParcela.setSituacao("EA");

                    parcelas.add(movParcela);
                    pagDAO.incluirListaPagamento(pagamentos, parcelas, null, null, false, 0.0, false, null);
                    writerCSVIncluidos.append(cliente.getMatricula()+";"+cliente.getPessoa().getNome()+";"+idRecebe+";"+codigoContratoZW+";"+Uteis.getData(dataLancamento)+";"+cheque.getAgencia()+";"+cheque.getConta()+";"+cheque.getNumero()+";"+cheque.getDataCompensacao_Apresentar()+";"+cheque.getValor()+";");
                    writerCSVIncluidos.append("INCLUSÃO;Não foi encontrato no ZW e então foi inserido\n");
                }
                con.commit();
            } catch (Exception e) {
                con.rollback();
                writerErros.append("\n#### idRecebe ="+idRecebe+" Matricula = "+matricula+" Erro:" + e.getMessage() + "  ####");
            } finally {
                con.setAutoCommit(true);
            }
        }
        System.out.println("\n\n\n#### fim processo de importar pagamentos ####");
        parcelaDAO = null;
        pagDAO = null;
        writerCSVIncluidos.flush();
        writerCSVIncluidos.close();
        writerCSVAlterados.flush();
        writerCSVAlterados.close();
        writerCSVVerificar.flush();
        writerCSVVerificar.close();
        writerErros.flush();
        writerErros.close();
    } 
    
    
    private static ChequeVO preencherCheque(Element e,  Map<Integer, Integer> bancos) throws Exception {
        ChequeVO cheque = new ChequeVO();
        cheque.setValor(Double.valueOf(retirarNulo(e, "Valor")));
        cheque.setValorTotal(cheque.getValor());
        cheque.setNumero(UteisValidacao.emptyString(retirarNulo(e, "Nr_Doc")) ? "impor" : retirarNulo(e, "Nr_Doc"));
        Integer banco = UteisValidacao.emptyString(retirarNulo(e, "Banco")) ? bancos.get(0) : bancos.get(Integer.parseInt(retirarNulo(e, "Banco")));
        cheque.getBanco().setCodigo(UteisValidacao.emptyNumber(banco) ? bancos.get(0) : banco);
        cheque.setDataCompensacao(Uteis.getDate(retirarNulo(e,"Dt_Vencimento"),"yyyyMMdd"));
        String vistaPrazo = retirarNulo(e, "AVistaPrazo");
        cheque.setVistaOuPrazo(UteisValidacao.emptyString(vistaPrazo) || vistaPrazo.equals("P") ? "PR" : "AV");
        cheque.setNomeNoCheque(retirarNulo(e, "Nome_NoChequeCartao"));
        cheque.setConta(UteisValidacao.emptyString(retirarNulo(e, "ContaCorrente")) ? "impor" : retirarNulo(e, "ContaCorrente"));
        cheque.setAgencia(UteisValidacao.emptyString(retirarNulo(e, "Agencia")) ? "impor" : retirarNulo(e, "Agencia"));
        cheque.setSituacao("EA");
	return cheque;	
    }

    private static VendaAvulsaVO gerarVendaAvulsa(Connection con,ClienteVO cliente, Double valor, Date data, Integer codigoProduto, String idRecebe) throws Exception {
        //é setado com o valor zero, mesmo tendo recebido o debito da conta
        VendaAvulsa vendaDAO = new VendaAvulsa(con);
        VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
        vendaAvulsaVO.setTipoComprador("CI");
        vendaAvulsaVO.setCliente(cliente);
        vendaAvulsaVO.setDataRegistro(data);
        vendaAvulsaVO.setEmpresa(vendaAvulsaVO.getCliente().getEmpresa());
        ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
        item.setDataVenda(data);
        item.setQuantidade(1);
        item.setUsuarioVO(new UsuarioVO());
        item.getUsuarioVO().setCodigo(2);
        String descricaoProduto = "CHEQUE IMPORTADO recebimento ZD: "+idRecebe;
        
        
        item.getProduto().setCodigo(codigoProduto);
        item.getProduto().setValorFinal(valor);
            
        vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
        vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
        vendaAvulsaVO.setResponsavel(item.getUsuarioVO());
        vendaAvulsaVO.setDescricaoAdicional(
                descricaoProduto
                + " - R$ "
                + valor);
        
        vendaDAO.incluirSemCommit(vendaAvulsaVO, false, Calendario.hoje());
        vendaDAO = null;
        return vendaAvulsaVO;
    }
    
    
     
}

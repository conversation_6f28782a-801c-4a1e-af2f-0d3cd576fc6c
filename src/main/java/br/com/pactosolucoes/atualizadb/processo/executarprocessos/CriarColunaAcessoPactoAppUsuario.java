package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Kai<PERSON> Sanchez",
        data = "22/08/2024",
        descricao = "Cria coluna booleana de acesso ao pacto app",
        motivacao = "APPS-1339")
public class CriarColunaAcessoPactoAppUsuario implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE usuario ADD COLUMN acessoPactoApp BOOLEAN NOT NULL DEFAULT true;",
                    c
            );
        }
    }
}


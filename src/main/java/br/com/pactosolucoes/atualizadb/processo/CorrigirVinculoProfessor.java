package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Vinculo;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 18/07/13
 * Time: 09:56
 */
public class CorrigirVinculoProfessor extends SuperEntidade {

    private Vinculo vinculoDAO;
    private MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurmaDAO;
    private ConfiguracaoSistemaControle config;


    public CorrigirVinculoProfessor() throws Exception {
        vinculoDAO = new Vinculo();
        matriculaAlunoHorarioTurmaDAO = new MatriculaAlunoHorarioTurma();
    }

    public void executarProcesso() throws Exception {
        processarCorrecao();
    }

    public void processarCorrecao() throws Exception {
        getCon().setAutoCommit(false);
        config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");

        //Consultar vínculos de Professor;
        List<VinculoVO> vinculoVOs = vinculoDAO.consultarTodosVinculosPorTipoVinculo(TipoColaboradorEnum.PROFESSOR.getSigla(), Uteis.NIVELMONTARDADOS_MINIMOS);
        List<VinculoVO> vinculoOrientadorVOs = vinculoDAO.consultarTodosVinculosPorTipoVinculo(TipoColaboradorEnum.ORIENTADOR.getSigla(), Uteis.NIVELMONTARDADOS_MINIMOS);

        //Excluir os vínculos, mas manter os objetos em memória;
        for (VinculoVO vinculoVO : vinculoVOs) {
            vinculoDAO.excluirVinculoPorCodigoColaboradorClienteTipoVinculo(vinculoVO.getCodigo(), vinculoVO.getColaborador().getCodigo(), vinculoVO.getCliente().getCodigo(), vinculoVO.getTipoVinculo(), new Date(), "PROC. CORR. VÍN. PROF", null);
        }

        for (VinculoVO vinculoVO : vinculoOrientadorVOs) {
            vinculoDAO.excluirVinculoPorCodigoColaboradorClienteTipoVinculo(vinculoVO.getCodigo(), vinculoVO.getColaborador().getCodigo(), vinculoVO.getCliente().getCodigo(), vinculoVO.getTipoVinculo(), new Date(), "PROC. CORR. VÍN. PROF", null);
        }

        //Obter os contratos ativos, pelo Sintético;
        Map<Integer, ItemCorrecao> mapaDeContratos = obterMapaContratosClientes();
        List<MatriculaAlunoHorarioTurmaVO> mahtVOs = matriculaAlunoHorarioTurmaDAO.consultarPorContratos(mapaDeContratos.keySet());
        Map<Integer, List<VinculoVO>> vinculosCliente = processarDados(mapaDeContratos, mahtVOs);

        //Verificar os contratos que estão sendo incluídos os vínculos;
        Set<Integer> clientes = vinculosCliente.keySet();
        setTotalParaProcessar(clientes.size());
        for (Integer cliente : clientes) {
            List<VinculoVO> vinculosDoContrato = vinculosCliente.get(cliente);
            for (VinculoVO vinculo : vinculosDoContrato) {
                vinculoDAO.incluir(vinculo, new Date(), "PROC. CORR. VÍN. PROF", false, null, null);
            }
            incrementarProcessados();
        }

        if (config != null) {
            config.setTotalProcessadosVinculoProfessor(config.getTotalAProcessarVinculoProfessor());
        }

        getCon().commit();
        getCon().setAutoCommit(true);
    }

    /**
     * Retornar o mapa de clientes e vínculos;
     */
    private Map<Integer, List<VinculoVO>> processarDados(Map<Integer, ItemCorrecao> mapaDeContratos, List<MatriculaAlunoHorarioTurmaVO> mahtVOs) {
        Map<Integer, List<VinculoVO>> mapaClienteVinculos = new HashMap<Integer, List<VinculoVO>>();

        for (MatriculaAlunoHorarioTurmaVO maht : mahtVOs) {
            ItemCorrecao itemCorrecao = mapaDeContratos.get(maht.getContrato().getCodigo());
            ContratoVO contrato = itemCorrecao.getContrato();

            if (maht.getDataFim().equals(contrato.getVigenciaAteAjustada())
                    || maht.getDataFim().after(contrato.getVigenciaAteAjustada())) {

                List<VinculoVO> vinculosDoCliente = mapaClienteVinculos.get(itemCorrecao.getCliente().getCodigo());
                if (vinculosDoCliente == null) {
                    vinculosDoCliente = new ArrayList<VinculoVO>();
                }

                boolean existe = false;

                for (VinculoVO vinculoVO : vinculosDoCliente) {
                    if (vinculoVO.getColaborador().getCodigo().equals(maht.getHorarioTurma().getProfessor().getCodigo())) {
                        existe = true;
                    }
                }

                if (!existe) {
                    VinculoVO vinculoVO = new VinculoVO();
                    vinculoVO.setColaborador(maht.getHorarioTurma().getProfessor());
                    vinculoVO.setCliente(itemCorrecao.getCliente());
                    vinculoVO.setTipoVinculo(TipoColaboradorEnum.PROFESSOR.getSigla());
                    vinculosDoCliente.add(vinculoVO);
                }
                mapaClienteVinculos.put(itemCorrecao.getCliente().getCodigo(), vinculosDoCliente);
            }
        }
        return mapaClienteVinculos;
    }

    private void setTotalParaProcessar(double totalParaProcessar) {
        if (config != null) {
            config.setTotalAProcessarVinculoProfessor(totalParaProcessar);
        }
    }

    private void incrementarProcessados() {
        if (config != null) {
            config.setTotalProcessadosVinculoProfessor(config.getTotalProcessadosVinculoProfessor() + 1);
        }
    }

    private Map<Integer, ItemCorrecao> obterMapaContratosClientes() throws Exception {
        String sqlContratoCliente = "SELECT\n" +
                "  codigocliente,\n" +
                "  codigocontrato,\n" +
                "  c.vigenciaateajustada        AS contratoVigencia,\n" +
                "  c.contratoresponsavelrenovacaomatricula,\n" +
                "  renovado.vigenciaateajustada AS renovadoVigencia\n" +
                "FROM situacaoclientesinteticodw sinteticodw\n" +
                "  LEFT JOIN contrato c\n" +
                "    ON sinteticodw.codigocontrato = c.codigo\n" +
                "  LEFT JOIN contrato renovado\n" +
                "    ON c.contratoresponsavelrenovacaomatricula = renovado.codigo\n" +
                "WHERE sinteticodw.situacao = 'AT' OR sinteticodw.situacao = 'TR'";

        ResultSet resultSet = criarConsulta(sqlContratoCliente, this.con);
        Map<Integer, ItemCorrecao> mapaDeContratos = new HashMap<Integer, ItemCorrecao>();

        while (resultSet.next()) {
            ItemCorrecao itemCorrecao = new ItemCorrecao();
            itemCorrecao.getCliente().setCodigo(resultSet.getInt("codigocliente"));
            itemCorrecao.getContrato().setCodigo(resultSet.getInt("codigocontrato"));
            itemCorrecao.getContrato().setVigenciaAteAjustada(resultSet.getDate("contratoVigencia"));
            mapaDeContratos.put(itemCorrecao.getContrato().getCodigo(), itemCorrecao);
            if (resultSet.getInt("contratoresponsavelrenovacaomatricula") != 0) {
                ItemCorrecao itemCorrecao1 = new ItemCorrecao();
                itemCorrecao1.getCliente().setCodigo(resultSet.getInt("codigocliente"));
                itemCorrecao1.getContrato().setCodigo(resultSet.getInt("contratoresponsavelrenovacaomatricula"));
                itemCorrecao1.getContrato().setVigenciaAteAjustada(resultSet.getDate("renovadoVigencia"));
                mapaDeContratos.put(itemCorrecao1.getContrato().getCodigo(), itemCorrecao1);
            }
        }
        return mapaDeContratos;
    }
}

class ItemCorrecao {
    private ContratoVO contrato = new ContratoVO();
    private ClienteVO cliente = new ClienteVO();

    ContratoVO getContrato() {
        return contrato;
    }

    ClienteVO getCliente() {
        return cliente;
    }
}
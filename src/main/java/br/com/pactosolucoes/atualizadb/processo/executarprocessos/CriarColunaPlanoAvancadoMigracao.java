package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Kvio Castro",
        data = "26/06/2024",
        descricao = "Cria columas para definio de planos avanados",
        motivacao = "Diferenciar planos normais e avanados")
public class CriarColunaPlanoAvancadoMigracao implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table public.plano add planoavancado boolean;", c);
        }
    }
}



package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luiz Felipe",
        data = "25/02/2025",
        descricao = "GCM-1 - Importação de compra via XML NFe",
        motivacao = "GCM-1 - Importação de compra via XML NFe")
public class AtualizacaoTicketGCM1 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE compra ADD COLUMN importacaoobs CHARACTER VARYING (120);", c);
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import static br.com.pactosolucoes.atualizadb.processo.AjustarDatasRenovacaoImportacao.ajustarDatasRenovacao;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class BaterMetaRepescagem {

    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("**********************************************", "postgres", "pactodb");
            atualizarMetasObjecao(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void atualizarMetasObjecao(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);

        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select fmd.*,fm.identificadormeta,fm.dataregistro from fecharmetadetalhado fmd\n"
                + "inner join historicocontato hc on hc.codigo = fmd.historicocontato\n"
                + "inner join fecharmeta fm on fm.codigo = fmd.fecharmeta	\n"
                + "  where fmd.historicocontato is not null and fmd.obtevesucesso = 'f' and hc.objecao  is not null and fm.identificadormeta not in ('AG','RE','VE') order by fm.dataregistro desc", con);

        int cont = 0;
        FecharMetaVO meta;
        Integer repescagem = 0;

        while (consulta.next()) {
            cont++;
            meta = getFacade().getFecharMeta().consultarPorChavePrimaria(consulta.getInt("fecharmeta"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if ((meta.getMetaAtingida() + meta.getRepescagem()) < meta.getMeta()) {
                repescagem = meta.getRepescagem().intValue() + 1;
                SuperFacadeJDBC.executarConsulta("update fecharmeta set repescagem = " + repescagem.toString() + " where codigo = " + consulta.getInt("fecharmeta") + ";", con);
            }

            SuperFacadeJDBC.executarConsulta("update fecharmetadetalhado  set obtevesucesso  = 't' where codigo = " + consulta.getInt("codigo") + ";", con);
            System.out.println(cont + ". Meta " + consulta.getString("identificadormeta") + " de codigo = " + consulta.getInt("fecharmeta") + " foi atualizada para " + repescagem.toString() + ".");
//
        }
    }

    public static void atualizarMetasSimplesRegistro(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);

        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select fmd.*,fm.identificadormeta,fm.dataregistro from fecharmetadetalhado fmd\n"
                + "inner join historicocontato hc on hc.codigo = fmd.historicocontato\n"
                + "inner join fecharmeta fm on fm.codigo = fmd.fecharmeta	\n"
                + "  where fmd.historicocontato is not null and fmd.obtevesucesso = 'f' and hc.resultado = 'Simples Registro' and fm.identificadormeta not in ('AG','RE','VE','EX','HO','IN','CV','PE','VE') order by fm.dataregistro desc", con);

        int cont = 0;
        FecharMetaVO meta;
        Integer repescagem = 0;

        while (consulta.next()) {
            cont++;
            meta = getFacade().getFecharMeta().consultarPorChavePrimaria(consulta.getInt("fecharmeta"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if ((meta.getMetaAtingida() + meta.getRepescagem()) < meta.getMeta()) {
                repescagem = meta.getRepescagem().intValue() + 1;
                SuperFacadeJDBC.executarConsulta("update fecharmeta set repescagem = " + repescagem.toString() + " where codigo = " + consulta.getInt("fecharmeta") + ";", con);
            }

            SuperFacadeJDBC.executarConsulta("update fecharmetadetalhado  set obtevesucesso  = 't' where codigo = " + consulta.getInt("codigo") + ";", con);
            System.out.println(cont + ". Meta " + consulta.getString("identificadormeta") + " de codigo = " + consulta.getInt("fecharmeta") + " foi atualizada para " + repescagem.toString() + ".");
//
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Wenderson Reis",
        data = "11/04/2025",
        descricao = "Criar Coluna para salvar valor máximo de desconto por duração no plano",
        motivacao = "M1-5089")
public class M15098CreateColumnValorMaximoDescontoPorPlano implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planoduracao \n" +
                    "ADD COLUMN valormaximodescontoplano DOUBLE PRECISION;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "14/01/2025",
        descricao = "Criação coluna retornoPagamentoStoneConnect na tabela PinPadPedido",
        motivacao = "Ticket PAY-83, para armazenar o retorno do pagamento da StoneConnect e auxiliar em analise futuras")
public class PAY83CriarColunaRetornoPagamentoStoneConnect implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE PinPadPedido ADD COLUMN retornoPagamentoStoneConnect TEXT," +
                    "ADD COLUMN dataUltimaAlteracaoRetornoStoneConnect timestamp without time zone;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Marcos Andre",
        data = "02/05/2025",
        descricao = "Colocando planos antigos como avançados, para poderem ser utilizados na tela nova",
        motivacao = "M1-5432")
public class AjustarPlanosAntigosParaAvancadoM15432 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update plano set planoavancado = true where codigo in (select distinct p.codigo from plano p  inner join planomodalidade pm on pm.plano = p.codigo where p.recorrencia = false and p.vendacreditotreino = false and p.planopersonal = false and not exists(select p2.codigo from planoexcecao p2 where plano = p.codigo and p2.modalidade = pm.modalidade) and not exists (select p2.codigo from planoexcecao p2 inner join composicaomodalidade c2 on c2.composicao = p2.pacote where plano = p.codigo and c2.modalidade = pm.modalidade))", c);
        }
    }
}

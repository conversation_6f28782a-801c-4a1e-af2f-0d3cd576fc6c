package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Cesar Henrique",
        data = "13/01/2025",
        descricao = "INCLUIR NOVO CAMPO DESCRICAOSERVICOMUNICIPIO VARCHAR NA TABELA PRODUTO.",
        motivacao = "M2-2845")
public class M22845PdfNotaFiscal implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE PRODUTO ADD COLUMN DESCRICAOSERVICOMUNICIPIO VARCHAR;", c);
        }
    }
}

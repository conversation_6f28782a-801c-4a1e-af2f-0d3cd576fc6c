package br.com.pactosolucoes.atualizadb.processo.itau;

import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

import java.util.Date;

public class WebhookItauJSONV2 {
    private String id_boleto;
    private String id_beneficiario;
    private String nome_cobranca;
    private String nome_pessoa;
    private String numero_cadastro_pessoa_fisica;
    private String codigo_carteira;
    private String situacao_geral_boleto;
    private String status_vencimento;
    private String numero_nosso_numero;
    private Date data_vencimento;
    private double valor_titulo;
    private double valor_pago_total_cobranca;
    private String texto_seu_numero;
    private Integer dac_titulo;
    private String codigo_barras;
    private String numero_linha_digitavel;
    private Date data_limite_pagamento;
    private Date data_emissao;
    private Date data_inclusao_pagamento;


    public WebhookItauJSONV2(JSONObject json) throws Exception {
        this.id_boleto = json.optString("id_boleto");
        JSONObject beneficiario = json.optJSONObject("beneficiario");
        if (beneficiario != null) {
            this.id_beneficiario = beneficiario.optString("id_beneficiario");
            this.nome_cobranca = beneficiario.optString("nome_cobranca");
        }
        JSONObject dado_boleto = new JSONObject(json.optString("dado_boleto"));
        this.codigo_carteira = dado_boleto.optString("codigo_carteira");
        this.data_emissao = Uteis.getDate(dado_boleto.optString("data_emissao"), "yyyy-MM-dd");
        JSONObject dados_individuais_boleto = dado_boleto.getJSONArray("dados_individuais_boleto").getJSONObject(0);
        this.situacao_geral_boleto = dados_individuais_boleto.optString("situacao_geral_boleto");
        this.status_vencimento = dados_individuais_boleto.optString("status_vencimento");
        this.numero_nosso_numero = dados_individuais_boleto.optString("numero_nosso_numero");
        this.data_vencimento = Uteis.getDate(dados_individuais_boleto.optString("data_vencimento"), "yyyy-MM-dd");
        this.valor_titulo = dados_individuais_boleto.optDouble("valor_titulo");
        this.texto_seu_numero = dados_individuais_boleto.optString("texto_seu_numero");
        this.dac_titulo = dados_individuais_boleto.optInt("dac_titulo");
        this.codigo_barras = dados_individuais_boleto.optString("codigo_barras");
        this.numero_linha_digitavel = dados_individuais_boleto.optString("numero_linha_digitavel");
        this.data_limite_pagamento = Uteis.getDate(dados_individuais_boleto.optString("data_limite_pagamento"), "yyyy-MM-dd");

        if (dado_boleto.has("pagamentos_cobranca")) {
            JSONObject pagamentos_cobranca = dado_boleto.optJSONArray("pagamentos_cobranca").getJSONObject(0);
            this.data_inclusao_pagamento = Uteis.getDate(pagamentos_cobranca.optString("data_inclusao_pagamento"), "yyyy-MM-dd");
            this.valor_pago_total_cobranca = pagamentos_cobranca.optDouble("valor_pago_total_cobranca");
        }
    }

    public String getId_boleto() {
        return id_boleto;
    }

    public void setId_boleto(String id_boleto) {
        this.id_boleto = id_boleto;
    }

    public String getId_beneficiario() {
        return id_beneficiario;
    }

    public void setId_beneficiario(String id_beneficiario) {
        this.id_beneficiario = id_beneficiario;
    }

    public String getNome_cobranca() {
        return nome_cobranca;
    }

    public void setNome_cobranca(String nome_cobranca) {
        this.nome_cobranca = nome_cobranca;
    }

    public String getNome_pessoa() {
        return nome_pessoa;
    }

    public void setNome_pessoa(String nome_pessoa) {
        this.nome_pessoa = nome_pessoa;
    }

    public String getNumero_cadastro_pessoa_fisica() {
        return numero_cadastro_pessoa_fisica;
    }

    public void setNumero_cadastro_pessoa_fisica(String numero_cadastro_pessoa_fisica) {
        this.numero_cadastro_pessoa_fisica = numero_cadastro_pessoa_fisica;
    }

    public String getCodigo_carteira() {
        return codigo_carteira;
    }

    public void setCodigo_carteira(String codigo_carteira) {
        this.codigo_carteira = codigo_carteira;
    }

    public String getSituacao_geral_boleto() {
        return situacao_geral_boleto;
    }

    public void setSituacao_geral_boleto(String situacao_geral_boleto) {
        this.situacao_geral_boleto = situacao_geral_boleto;
    }

    public String getStatus_vencimento() {
        return status_vencimento;
    }

    public void setStatus_vencimento(String status_vencimento) {
        this.status_vencimento = status_vencimento;
    }

    public String getNumero_nosso_numero() {
        return numero_nosso_numero;
    }

    public void setNumero_nosso_numero(String numero_nosso_numero) {
        this.numero_nosso_numero = numero_nosso_numero;
    }

    public Date getData_vencimento() {
        return data_vencimento;
    }

    public void setData_vencimento(Date data_vencimento) {
        this.data_vencimento = data_vencimento;
    }

    public double getValor_titulo() {
        return valor_titulo;
    }

    public void setValor_titulo(double valor_titulo) {
        this.valor_titulo = valor_titulo;
    }

    public String getTexto_seu_numero() {
        return texto_seu_numero;
    }

    public void setTexto_seu_numero(String texto_seu_numero) {
        this.texto_seu_numero = texto_seu_numero;
    }

    public Integer getDac_titulo() {
        return dac_titulo;
    }

    public void setDac_titulo(Integer dac_titulo) {
        this.dac_titulo = dac_titulo;
    }

    public String getCodigo_barras() {
        return codigo_barras;
    }

    public void setCodigo_barras(String codigo_barras) {
        this.codigo_barras = codigo_barras;
    }

    public String getNumero_linha_digitavel() {
        return numero_linha_digitavel;
    }

    public void setNumero_linha_digitavel(String numero_linha_digitavel) {
        this.numero_linha_digitavel = numero_linha_digitavel;
    }

    public Date getData_limite_pagamento() {
        return data_limite_pagamento;
    }

    public void setData_limite_pagamento(Date data_limite_pagamento) {
        this.data_limite_pagamento = data_limite_pagamento;
    }

    public Date getData_emissao() {
        return data_emissao;
    }

    public void setData_emissao(Date data_emissao) {
        this.data_emissao = data_emissao;
    }

    public double getValor_pago_total_cobranca() {
        return valor_pago_total_cobranca;
    }

    public void setValor_pago_total_cobranca(double valor_pago_total_cobranca) {
        this.valor_pago_total_cobranca = valor_pago_total_cobranca;
    }

    public Date getData_inclusao_pagamento() {
        return data_inclusao_pagamento;
    }

    public void setData_inclusao_pagamento(Date data_inclusao_pagamento) {
        this.data_inclusao_pagamento = data_inclusao_pagamento;
    }
}

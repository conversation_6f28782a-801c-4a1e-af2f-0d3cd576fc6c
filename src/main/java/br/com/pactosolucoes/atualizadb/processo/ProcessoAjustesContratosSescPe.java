package br.com.pactosolucoes.atualizadb.processo;

import importador.LeitorExcel2010;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.BonusContratoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ProcessoAjustesContratosSescPe {

    private static StringBuilder logGravar;

    public static void main(String[] args) throws Exception {

        try {
            Connection con = DriverManager.getConnection("**********************************************************************", "postgres", "pactodb");
            String codigoMatriculas = "";
            String codigosContratos = "";
            String caminhoPlanilhaDePara = "C:\\pacto\\backups\\sesipe_ajustes\\sescpe-ajuste-contratos-renovados-2025-01-13-gerar-parcelas.xlsx";

//            ajustarVigenciaContratos(codigosContratos, "2025-01-31", con);
//            ajustarVigenciaContratosTodasEmpresas(codigoMatriculas,"2024-12-19", "2024-12-23", "2024-12-31", con);
//            ajustarParcelas(codigoMatriculas, "2025-01-01", "2025-01-31", con);
//            ajustarValorParcelas("C:\\pacto\\backups\\sesipe_ajustes\\Renegociação_unificada.xlsx", 0,4, 5, 6, con);
//            corrigirContratosRenovadosAutomaticamente(con, codigosContratos);
//            estornarContratos(con, codigosContratos);
            corrigirContratosRenovadosSescPe(con, caminhoPlanilhaDePara, codigosContratos);
//            corrigirContratosRenovadosParcelasZeradas(con, caminhoPlanilhaDePara, codigosContratos);
        } catch (Exception ex) {
            adicionarLog(ex.getMessage());
        } finally {
            Uteis.salvarArquivo("processo-ajustar-parcelas-sescpe-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\PactoJ\\log\\" + File.separator);
        }
    }

    private static void corrigirContratosRenovadosSescPe(Connection con, String caminhoPlanilhaDePara, String codigosContratos) throws Exception {
        Contrato contratoDAO = new Contrato(con);

        List<Integer> codigosContratosList = new ArrayList<>();
        if (!UteisValidacao.emptyString(codigosContratos)) {
            for (String codigo: codigosContratos.split(",")) {
                codigosContratosList.add(Integer.parseInt(codigo));
            }
        }


        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(caminhoPlanilhaDePara), 0);

        int atual = 0;
        int total = hssfRows.size();


        for (XSSFRow linha : hssfRows) {
            try {
                con.setAutoCommit(false);
                Integer codigoContratoRenovacao = Integer.parseInt(LeitorExcel2010.obterString(linha, 3));

                if (codigosContratosList.size() > 0 && !codigosContratosList.contains(codigoContratoRenovacao)) {
                    continue;
                }
                String dataFimContrato = LeitorExcel2010.obterString(linha, 14);
                Integer durcaoContrato = Integer.parseInt(LeitorExcel2010.obterString(linha, 15));
                Integer quantidadeParcelas = Integer.parseInt(LeitorExcel2010.obterString(linha, 16));
                Double valorCorretoMensalidade = LeitorExcel2010.obterDouble(linha, 17);

                System.out.printf(" %d\\%d - %s\n", ++atual, total, "Corrigindo contrato renovação cod: " + codigoContratoRenovacao);

                ContratoVO contratoVO = contratoDAO.consultarPorCodigo(codigoContratoRenovacao, Uteis.NIVELMONTARDADOS_TODOS);
                if (UteisValidacao.emptyNumber(contratoVO.getContratoBaseadoRenovacao())) {
                    throw new ConsistirException("Contrato não é renovação");
                }

                ajustarVigenciaContrato(contratoVO, dataFimContrato, durcaoContrato, con);
                ajustarProdutoParcelasContrato(contratoVO, valorCorretoMensalidade, quantidadeParcelas, con);
            } catch (Exception e) {
                e.printStackTrace();
                con.rollback();
                throw e;
            } finally {
                con.commit();
            }
        }
    }

    private static void corrigirContratosRenovadosParcelasZeradas(Connection con, String caminhoPlanilhaDePara, String codigosContratos) throws Exception {
        Contrato contratoDAO = new Contrato(con);
        MovParcela movParcelaDAO = new MovParcela(con);
        MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(con);
        MovProduto movProdutoDAO=  new MovProduto(con);

        List<Integer> codigosContratosList = new ArrayList<>();
        if (!UteisValidacao.emptyString(codigosContratos)) {
            for (String codigo: codigosContratos.split(",")) {
                codigosContratosList.add(Integer.parseInt(codigo));
            }
        }

        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(caminhoPlanilhaDePara), 0);

        int atual = 0;
        int total = hssfRows.size();


        for (XSSFRow linha : hssfRows) {
            Integer codigoContratoRenovacao = Integer.parseInt(LeitorExcel2010.obterString(linha, 3));

            try {
                con.setAutoCommit(false);

                if (codigosContratosList.size() > 0 && !codigosContratosList.contains(codigoContratoRenovacao)) {
                    continue;
                }
                Double valorCorretoMensalidade = LeitorExcel2010.obterDouble(linha, 17);

                adicionarLog(String.format(" %d\\%d - %s", ++atual, total, "Corrigindo parcelas do contrato renovação cod: " + codigoContratoRenovacao));

                ContratoVO contratoVO = contratoDAO.consultarPorCodigo(codigoContratoRenovacao, Uteis.NIVELMONTARDADOS_TODOS);
                if (contratoVO == null) {
                    continue;
                }
                if (UteisValidacao.emptyNumber(contratoVO.getContratoBaseadoRenovacao())) {
                    throw new ConsistirException("Contrato não é renovação");
                }

                List<MovParcelaVO> movParcelaVOS = movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                List<MovProdutoVO> movProdutoVOS = movProdutoDAO.consultarPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Map<Integer, MovProdutoVO> mapaMovProdutos = movProdutoVOS.stream()
                        .collect(Collectors.toMap(MovProdutoVO::getCodigo, movProdutoVO -> movProdutoVO));
                Map<String, List<MovProdutoVO>> mapaMovProdutosMesRef = movProdutoVOS.stream()
                        .filter(m -> m.getProduto().getTipoProduto().equals("PM"))
                        .collect(Collectors.groupingBy(MovProdutoVO::getMesReferencia));

                Date mesAnoRefPrimeiroMovProduto = null;
                for (String key: mapaMovProdutosMesRef.keySet()) {
                    Date mesReferencia = Calendario.getDate("dd/MM/yyyy", "01/" + key);
                    if (mesAnoRefPrimeiroMovProduto == null || Calendario.menor(mesReferencia, mesAnoRefPrimeiroMovProduto)) {
                        mesAnoRefPrimeiroMovProduto = mesReferencia;
                    }
                }

                for (MovParcelaVO mpar : movParcelaVOS) {
                    if (mpar.getValorParcela().doubleValue() == 0.0
                            && mpar.getSituacao().equals("PG")
                            && mpar.getDescricao().toUpperCase().contains("PARCELA")) {
                        adicionarLog(String.format("\tAjustando Parcela %d %s %.2f %s para situacao %s valor %.2f", mpar.getCodigo(), mpar.getDescricao(), mpar.getValorParcela(), mpar.getDescricao(),
                                "EA", valorCorretoMensalidade));

                        List<MovProdutoParcelaVO> mppVOSParcela = movProdutoParcelaDAO.consultarPorCodigoMovParcela(mpar.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        for (MovProdutoParcelaVO mppVO: mppVOSParcela) {
                            MovProdutoVO mpro = mapaMovProdutos.get(mppVO.getMovProduto());
                            if (mpro.getPrecoUnitario().doubleValue() > 0.0) {
                                throw new ConsistirException(String.format("MovProduto %d %s está vinculado com parcela que possui valor maior que 0.0", mpro.getCodigo(), mpro.getDescricao()));
                            }
                            if (mapaMovProdutos.get(mppVO.getMovProduto()).getTipoProduto().equals("PM")) {
                                movProdutoParcelaDAO.excluir(mppVO);
                            }
                        }

                        String mesAnoParcela = Calendario.getDataAplicandoFormatacao(mesAnoRefPrimeiroMovProduto, "MM/yyyy");

                        List<MovProdutoVO> mproVOS = mapaMovProdutosMesRef.get(mesAnoParcela);
                        if (mproVOS == null) {
                            throw new ConsistirException("MovProduto não encontrado para parcela: " + mpar.getCodigo() + " - " + mpar.getDescricao());
                        }
                        if (mproVOS.size() > 1) {
                            throw new ConsistirException("Mais de um MovProduto encontrado para parcela: " + mpar.getCodigo() + " - " + mpar.getDescricao());
                        }

                        MovProdutoVO mpro = mproVOS.get(0);

                        List<MovProdutoParcelaVO> mppVOS = movProdutoParcelaDAO.consultarPorCodigoMovProdutos(mpro.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (mppVOS.size() > 1) {
                            throw new ConsistirException("MovProduto descrição " + mpro.getDescricao() + " já está vinculado a outras movParcelas");
                        }

                        if (mpro.getProduto().getTipoProduto().equals("PM")
                                && mpro.getSituacao().equals("PG")
                                && mpro.getTotalFinal().doubleValue() == 0.0) {
                            // vincular movProduto a movParcela
                            MovProdutoParcelaVO mppVO = new MovProdutoParcelaVO();
                            mppVO.setMovParcela(mpar.getCodigo());
                            mppVO.setMovProduto(mpro.getCodigo());
                            movProdutoParcelaDAO.incluir(mppVO);
                            // deixar movproduto em aberto
                            mpro.setSituacao("EA");
                            mpro.setValorFaturado(valorCorretoMensalidade);
                            mpro.setTotalFinal(valorCorretoMensalidade);
                            mpro.setPrecoUnitario(valorCorretoMensalidade);
                            movProdutoDAO.alterarSemCommit(mpro);
                        } else {
                            throw new Exception("MovProduto descrição " + mpro.getDescricao() + " não está em situação PG ou valor total final é diferente de 0.0");
                        }
                        // deixar movparcela em aberto
                        mpar.setValorParcela(valorCorretoMensalidade);
                        mpar.setSituacao("EA");
                        movParcelaDAO.alterarSemCommit(mpar);

                        // deletar recibos zerados
                        ResultSet rsPagamentoParcela = SuperFacadeJDBC.criarConsulta("select * from pagamentomovparcela \n" +
                                " where movparcela = " + mpar.getCodigo(), con);
                        while (rsPagamentoParcela.next()) {
                            SuperFacadeJDBC.executarConsulta("delete from movpagamento where codigo = " + rsPagamentoParcela.getInt("movpagamento"), con);
                            adicionarLog(String.format("\t\tMovPagamento %d excluído", rsPagamentoParcela.getInt("movpagamento")));
                            SuperFacadeJDBC.executarConsulta("delete from recibopagamento where codigo = " + rsPagamentoParcela.getInt("recibopagamento"), con);
                            adicionarLog(String.format("\t\tRecibo %d excluído", rsPagamentoParcela.getInt("recibopagamento")));
                            SuperFacadeJDBC.executarConsulta("delete from pagamentomovparcela where codigo = " + rsPagamentoParcela.getInt("codigo"), con);
                            adicionarLog(String.format("\t\tPagamentoMovParcela %d excluído", rsPagamentoParcela.getInt("codigo")));
                        }

                        mesAnoRefPrimeiroMovProduto = Calendario.somarMeses(mesAnoRefPrimeiroMovProduto, 1);
                    }
                }

                SuperFacadeJDBC.executarConsulta("update contrato set valorfinal =  (select sum(mpar.valorparcela) from movparcela mpar where mpar.contrato = contrato.codigo)," +
                        " valorbasecalculo = (select sum(mpar.valorparcela) from movparcela mpar where mpar.contrato = contrato.codigo) \n" +
                        " where codigo = " + contratoVO.getCodigo(), con);

                con.commit();
            } catch (Exception e) {
                e.printStackTrace();
                con.rollback();
                throw e;
            }
        }
    }


    private static void ajustarProdutoParcelasContrato(ContratoVO contratoVO, Double valorCorretoMensalidade, Integer quantidadeParcelas, Connection con) throws Exception {
        Integer qtdParcelaAtual = SuperFacadeJDBC.contar("select count(*) from movparcela where contrato = " + contratoVO.getCodigo(), con);
        Integer qtdParcelaGerar = quantidadeParcelas - qtdParcelaAtual;

        if (qtdParcelaGerar <= 0) {
            return;
        }

        ResultSet rsParcela = SuperFacadeJDBC.criarConsulta("select codigo from movparcela where contrato = " + contratoVO.getCodigo() + " order by codigo desc limit 1", con);
        int parcelaRef = rsParcela.next() ? rsParcela.getInt("codigo") : 0;
        if (parcelaRef == 0) {
            throw new ConsistirException("Parcela de referência não encontrada");
        }

        ResultSet rsProduto = SuperFacadeJDBC.criarConsulta("select mpro.codigo from movproduto mpro\n" +
                " inner join produto pro on pro.codigo = mpro.produto and pro.tipoproduto = 'PM'\n" +
                " where mpro.contrato = " + contratoVO.getCodigo() + " order by codigo desc limit 1", con);
        int produtoRef =  rsProduto.next() ? rsProduto.getInt("codigo") : 0;
        if (produtoRef == 0) {
            throw new ConsistirException("Produto de referência não encontrada");
        }

        MovProduto movProdutoDAO = new MovProduto(con);
        MovParcela movParcelaDAO = new MovParcela(con);
        MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(con);

        MovParcelaVO parcelaVORef = movParcelaDAO.consultarPorCodigo(parcelaRef, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        MovProdutoVO produtoVORef = movProdutoDAO.consultarPorChavePrimaria(produtoRef, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        Date mesReferencia = Calendario.getDate("MM/yyyy", produtoVORef.getMesReferencia());
        Date dataVencimento = parcelaVORef.getDataVencimento();

        String sql = "select mpar.* from movparcela mpar \n" +
                "where mpar.contrato = " + contratoVO.getCodigo() + " \n" +
                "and (mpar.descricao like 'PARCELA _' or mpar.descricao like 'PARCELA __') \n" +
                "and (situacao = 'EA' or (situacao = 'PG' AND valorparcela = 0.0)) \n" +
                "and not exists (select mpar2.codigo from movparcela mpar2 where mpar2.contrato = " + contratoVO.getCodigo() + " and mpar2.descricao ilike '%renegoci%') \n" +
                "order by codigo";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {
            SuperFacadeJDBC.executarConsulta("update movparcela set situacao = 'EA', valorparcela = " + valorCorretoMensalidade + " \n" +
                    "where codigo = " + rs.getInt("codigo"), con);
            SuperFacadeJDBC.executarConsulta("update movproduto mpro set situacao = 'EA', totalfinal = " + valorCorretoMensalidade + ", \n" +
                    " valorfaturado = " + valorCorretoMensalidade + ", \n" +
                    " precounitario = " + valorCorretoMensalidade + " \n" +
                    "from movprodutoparcela mpp, produto pro \n" +
                    "where mpp.movproduto = mpro.codigo \n" +
                    "and pro.codigo = mpro.produto \n" +
                    "and pro.tipoproduto = 'PM' \n" +
                    "and mpp.movparcela = " + rs.getInt("codigo"), con);

            if (rs.getString("situacao").equals("PG")) {
                ResultSet rsPagamentoParcela = SuperFacadeJDBC.criarConsulta("select * from pagamentomovparcela \n" +
                        " where movparcela = " + rs.getInt("codigo"), con);
                while (rsPagamentoParcela.next()) {
                    SuperFacadeJDBC.executarConsulta("delete from movpagamento where codigo = " + rsPagamentoParcela.getInt("movpagamento"), con);
                    SuperFacadeJDBC.executarConsulta("delete from recibopagamento where codigo = " + rsPagamentoParcela.getInt("recibopagamento"), con);
                    SuperFacadeJDBC.executarConsulta("delete from pagamentomovparcela where codigo = " + rsPagamentoParcela.getInt("codigo"), con);
                }
            }
        }

        for (int i = (qtdParcelaAtual  + 1); i <= quantidadeParcelas; i++) {
            mesReferencia = Calendario.somarMeses(mesReferencia, 1);
            dataVencimento = Calendario.somarMeses(dataVencimento,1);

            MovParcelaVO novaParcela = (MovParcelaVO) parcelaVORef.getClone(true);
            novaParcela.setDescricao("PARCELA " + i);
            novaParcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorCorretoMensalidade));
            novaParcela.setDataVencimento(dataVencimento);
            novaParcela.setDataCobranca(dataVencimento);
            movParcelaDAO.incluirParcelaSemCommit(novaParcela);

            MovProdutoVO novoProduto = (MovProdutoVO) produtoVORef.getClone(true);
            novoProduto.setMesReferencia(Calendario.getData(mesReferencia, "MM/yyyy"));
            novoProduto.setDescricao(novoProduto.getDescricao().replace(produtoVORef.getMesReferencia(), novoProduto.getMesReferencia()));
            movProdutoDAO.incluirSemCommit(novoProduto);

            MovProdutoParcelaVO mppVO = new MovProdutoParcelaVO();
            mppVO.setMovParcela(novaParcela.getCodigo());
            mppVO.setMovProduto(novoProduto.getCodigo());
            movProdutoParcelaDAO.incluir(mppVO);
        }

        SuperFacadeJDBC.executarConsulta("update contrato set valorfinal =  (select sum(mpar.valorparcela) from movparcela mpar where mpar.contrato = contrato.codigo)," +
                " valorbasecalculo = (select sum(mpar.valorparcela) from movparcela mpar where mpar.contrato = contrato.codigo) \n" +
                " where codigo = " + contratoVO.getCodigo(), con);
    }

    private static void ajustarVigenciaContrato(ContratoVO contratoVO, String dataFimContrato, Integer durcaoContrato, Connection con) throws Exception {
        String updateContrato = "UPDATE contrato SET vigenciaateajustada = ?,\n" +
                "\tdataprevistarenovar = ?,\n" +
                "\tdataprevistarematricula = ?\n" +
                "\tWHERE codigo = ?;";
        PreparedStatement pstm = con.prepareStatement(updateContrato);
        pstm.setDate(1, new java.sql.Date(Calendario.getDate("dd/MM/yyyy", dataFimContrato).getTime()));
        pstm.setDate(2, new java.sql.Date(Calendario.getDate("dd/MM/yyyy", dataFimContrato).getTime()));
        pstm.setDate(3, new java.sql.Date(Calendario.getDate("dd/MM/yyyy", dataFimContrato).getTime()));
        pstm.setInt(4, contratoVO.getCodigo());
        pstm.execute();

        SuperFacadeJDBC.executarConsulta("update contratoduracao set numeromeses = " + durcaoContrato + " \n" +
                " where contrato = " + contratoVO.getCodigo(), con);

        SuperFacadeJDBC.executarConsulta("UPDATE periodoacessocliente pac SET datafinalacesso = con.vigenciaateajustada FROM contrato con\n" +
                "\tWHERE con.codigo = pac.contrato\n" +
                "\tAND pac.tipoacesso = 'CA'\n" +
                "\tAND con.codigo = " + contratoVO.getCodigo(), con);

        SuperFacadeJDBC.executarConsulta("UPDATE historicocontrato hc SET datafinalsituacao = con.vigenciaateajustada FROM contrato con\n" +
                "\tWHERE con.codigo = hc.contrato\n" +
                "\tAND hc.tipohistorico = 'RN'\n" +
                "\tAND con.codigo = " + contratoVO.getCodigo(), con);
    }

    private static void estornarContratos(Connection con, String codigosContratos) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);

        Contrato contratoDAO = new Contrato(con);
        ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
        MovPagamento movPagamentoDAO = new MovPagamento(con);
        MovParcela movParcelaDAO = new MovParcela(con);
        Cliente clienteDAO = new Cliente(con);
        ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(con);

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        usuarioDAO = null;

        int atual = 0;
        for (String codigo: codigosContratos.split(",")) {
            System.out.printf(" %d\\%d Estornando contrato: %s\n", atual++, codigosContratos.split(",").length, codigo);

            ContratoVO contratoVO = contratoDAO.consultarPorCodigo(Integer.parseInt(codigo), Uteis.NIVELMONTARDADOS_TODOS);
            contratoVO.setUsuarioVO(usuarioVO);
            List<ReciboPagamentoVO> listaReciboPagamento = reciboPagamentoDAO.consularReciboPagamParcelaPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!listaReciboPagamento.isEmpty()) {
                contratoVO.setMovParcelaVOs(new ArrayList<>());
                contratoVO.setListaEstornoRecibo(new ArrayList<>());
                for (ReciboPagamentoVO recibo : listaReciboPagamento) {
                    EstornoReciboVO estornoRecibo = new EstornoReciboVO();
                    estornoRecibo.setReciboPagamentoVO(recibo);
                    estornoRecibo.setListaMovPagamento(movPagamentoDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR));
                    estornoRecibo.setListaMovParcela(movParcelaDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS));

                    //transações de cartão de crédito
                    contratoVO.montarListaTransacoes(estornoRecibo.getListaMovParcela(), con);
                    contratoVO.getListaEstornoRecibo().add(estornoRecibo);
                }

            } else {
                contratoVO.setMovParcelaVOs(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

                //transações de cartão de crédito
                contratoVO.montarListaTransacoes(contratoVO.getMovParcelaVOs(), con);
            }

            contratoVO.setPrecisaEstornarTransacoes(false);

            contratoVO.montarListaItensRemessa(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA), con);

            try {
                ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                contratoDAO.estornoContrato(contratoVO, clienteVO, null, null);
                zillyonWebFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static void ajustarValorParcelas(String caminhoPlanilha, int indexAba, int indiceCodigoParcela, int indiceValorOriginal, int indiceNovoValor, Connection con) throws Exception {
        MovParcela movParcelaDAO = new MovParcela(con);
        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(caminhoPlanilha), indexAba);
        int total = hssfRows.size();
        int atual = 0;

        for (XSSFRow linha : hssfRows) {
            Integer codigoParcela = Integer.parseInt(LeitorExcel2010.obterString(linha, indiceCodigoParcela));
            Double valorOriginal = LeitorExcel2010.obterDouble(linha, indiceValorOriginal);
            Double novoValor = LeitorExcel2010.obterDouble(linha, indiceNovoValor);

            MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorCodigo(codigoParcela, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (movParcelaVO == null) {
                adicionarLog("Parcela cod: " + codigoParcela + " não encontrada");
                continue;
            }

            adicionarLog(String.format("%d\\%d - Parcela: %d %s %s Valor antigo: %.2f -> Novo Valor: %.2f\n", ++atual, total, movParcelaVO.getCodigo(), movParcelaVO.getDescricao(), Calendario.getDataAplicandoFormatacao(movParcelaVO.getDataVencimento(), "dd/MM/yyyy"), valorOriginal, novoValor));

            if (!movParcelaVO.getSituacao().equals("EA")) {
                adicionarLog(String.format("Parcela: %d - NÃO AJUSTADA - Situação: %s\n", movParcelaVO.getCodigo(), movParcelaVO.getSituacao()));
                continue;
            }
            if (Uteis.arredondarForcando2CasasDecimais(valorOriginal) != Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela())) {
                adicionarLog(String.format("Parcela: %d - NÃO AJUSTADA - Valor original: %.2f - Valor parcela: %.2f\n", movParcelaVO.getCodigo(), valorOriginal, movParcelaVO.getValorParcela()));
                continue;
            }

            renegociarParcela(movParcelaVO, novoValor, usuarioVO, con);
        }
    }

    private static void ajustarVigenciaContratosTodasEmpresas(String codigosMatriculas, String dtInicialFiltro, String dtFinalFiltro, String novaDataFimContratos, Connection con) throws Exception {
        Empresa empresaDAO = new Empresa(con);
        List<EmpresaVO> empresaVOS = empresaDAO.consultarTodas(true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (EmpresaVO empresaVO : empresaVOS) {
            ajustarVigenciaContratos(codigosMatriculas, dtInicialFiltro, dtFinalFiltro, novaDataFimContratos, empresaVO, con);
        }
    }

    private static void ajustarVigenciaContratos(String codigosMatriculas, String dtInicialFiltro, String dtFinalFiltro, String novaDataFimContratos, EmpresaVO empresaVO, Connection con) throws Exception {
        if (UteisValidacao.emptyString(codigosMatriculas)) {
            throw new Exception("Código matrículas não informado");
        }

        if (UteisValidacao.emptyString(dtInicialFiltro)) {
            throw new Exception("Data inicial não informada");
        }

        if (UteisValidacao.emptyString(dtFinalFiltro)) {
            throw new Exception("Data final não informada");
        }

        if (Calendario.maior(Calendario.getDate("yyyy-MM-dd", dtInicialFiltro), Calendario.getDate("yyyy-MM-dd", dtFinalFiltro))) {
            throw new Exception("Data inicial maior que data final");
        }

        if (UteisValidacao.emptyString(novaDataFimContratos)) {
            throw new Exception("Nova data final não informada");
        }

        Date novaDtFim = Calendario.getDate("yyyy-MM-dd", novaDataFimContratos);

        if (Calendario.maior(Calendario.getDate("yyyy-MM-dd", dtInicialFiltro), novaDtFim)) {
            throw new Exception("Data inicial maior que nova data final");
        }

        Conexao.guardarConexaoForJ2SE(con);

        String sql = "SELECT con.* FROM contrato con\n" +
                "\tINNER JOIN empresa e ON e.codigo = con.empresa\n" +
                "\tINNER JOIN cliente cli ON cli.pessoa = con.pessoa\n" +
                "WHERE con.empresa = " + empresaVO.getCodigo() + "\n" +
                "AND con.situacao = 'AT'\n" +
                "AND con.vigenciaateajustada BETWEEN '" + dtInicialFiltro + "' AND '" + dtFinalFiltro + "'\n" +
                "AND cli.codigomatricula IN (" + codigosMatriculas + ") \n" +
                "AND NOT EXISTS(SELECT cp.codigo FROM contratooperacao cp WHERE cp.contrato = con.codigo AND cp.tipooperacao = 'CA')\n";

        Contrato contratoDAO = new Contrato(con);
        List<ContratoVO> contratoVOs = contratoDAO.consultar(sql.toString(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        int atual = 0;
        int total = contratoVOs.size();

        for (ContratoVO contratoVO: contratoVOs) {
            adicionarLog(String.format("%d\\%d [%s] - Lançando bonus contrato: %d - Dt Fim: %s qtd dias adicionar: %d",
                    ++atual, total, empresaVO.getNome(), contratoVO.getCodigo(), Calendario.getDataAplicandoFormatacao(contratoVO.getVigenciaAteAjustada(), "dd/MM/yyyy")));
            incluirBonusDiferenca(contratoVO, novaDtFim, empresaVO, con);
        }
    }

    private static void ajustarVigenciaContratos(String codigoContratos, String novaDataFimContratos, Connection con) throws Exception {
        if (UteisValidacao.emptyString(codigoContratos)) {
            throw new Exception("Código contratos não informado");
        }
        if (UteisValidacao.emptyString(novaDataFimContratos)) {
            throw new Exception("Nova data final não informada");
        }
        Date novaDtFim = Calendario.getDate("yyyy-MM-dd", novaDataFimContratos);

        Conexao.guardarConexaoForJ2SE(con);

        String sql = "SELECT con.* FROM contrato con\n" +
                "\tINNER JOIN empresa e ON e.codigo = con.empresa\n" +
                "\tINNER JOIN cliente cli ON cli.pessoa = con.pessoa\n" +
                "WHERE con.codigo IN (" + codigoContratos + ") \n";

        Contrato contratoDAO = new Contrato(con);
        List<ContratoVO> contratoVOs = contratoDAO.consultar(sql.toString(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        contratoDAO = null;
        Empresa empresaDAO = new Empresa(con);

        int atual = 0;
        int total = contratoVOs.size();

        for (ContratoVO contratoVO: contratoVOs) {
            contratoVO.setEmpresa(empresaDAO.consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            adicionarLog(String.format("%d\\%d [%s] - Lançando bonus contrato: %d - Dt Fim: %s Nova Dt Fim: %s",
                    ++atual, total, contratoVO.getEmpresa().getNome(), contratoVO.getCodigo(),
                    Calendario.getDataAplicandoFormatacao(contratoVO.getVigenciaAteAjustada(), "dd/MM/yyyy"),
                    Calendario.getDataAplicandoFormatacao(novaDtFim, "dd/MM/yyyy")));
            incluirBonusDiferenca(contratoVO, novaDtFim, contratoVO.getEmpresa(), con);
        }
    }



    private static void incluirBonusDiferenca(ContratoVO contratoVO, Date novaDtFim, EmpresaVO empresaVO, Connection con) throws Exception {
        BonusContratoVO bonusContratoVO = new BonusContratoVO();
        bonusContratoVO.setEmpresa(empresaVO.getCodigo());
        bonusContratoVO.getResponsavelOperacao().setCodigo(1);
        bonusContratoVO.getResponsavelOperacao().setUsername("ADMIN");

        ZillyonWebFacade zwDAO = new ZillyonWebFacade(con);

        JustificativaOperacaoVO justificativaOperacaoVO = new JustificativaOperacaoVO();
        justificativaOperacaoVO.setEmpresa(empresaVO);
        justificativaOperacaoVO.setDescricao("AJUSTAR DATA FIM CONTRATO - RECESSO ACADEMIA");
        justificativaOperacaoVO.setTipoOperacao("BO");
        justificativaOperacaoVO = zwDAO.getJustificativaOperacao().criarOuConsultarSeExistePorNome(justificativaOperacaoVO);


        try {
            int nrDias = (int) Uteis.nrDiasEntreDatas(contratoVO.getVigenciaAteAjustada(), novaDtFim);
            con.setAutoCommit(false);
            contratoVO.setEmpresa(empresaVO);
            bonusContratoVO.getResponsavelOperacao().setCodigo(1);
            bonusContratoVO.getResponsavelOperacao().setUsername("ADMIN");

            bonusContratoVO.setContratoVO(contratoVO);
            bonusContratoVO.setEmpresa(contratoVO.getEmpresa().getCodigo());
            bonusContratoVO.setAcrescentarDiaContrato("AC"); // AC = acrescentar
            bonusContratoVO.setNrDias(nrDias);
            bonusContratoVO.setDataInicio(Uteis.obterDataFutura2(contratoVO.getVigenciaAteAjustada(), 1));
            bonusContratoVO.setDataTermino(Uteis.obterDataFutura2(bonusContratoVO.getDataInicio(), (bonusContratoVO.getNrDias() - 1)));
            bonusContratoVO.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            bonusContratoVO.setTipoJustificativa(justificativaOperacaoVO.getCodigo());
            bonusContratoVO.setObservacao("Bônus - recesso academia");
            zwDAO.getContratoOperacao().incluirOperacaoBonus(bonusContratoVO, false, null, false, null);

            con.commit();
        } catch (Exception e) {
            adicionarLog(e.getMessage());
            con.rollback();
            throw e;
        }
    }

    private static void ajustarParcelas(String codigosMatriculas, String dataInicioVencimento, String dataFimVencimento, Connection con) throws Exception {
        MovParcela movParcelaDAO = new MovParcela(con);

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        usuarioDAO = null;

        String sql = "SELECT \n" +
                "\tcli.codigomatricula,\n" +
                "\tpes.nome,\n" +
                "\tcon.codigo AS contrato,\n" +
                "\tcon.vigenciade::date AS inicio,\n" +
                "\tcon.vigenciaateajustada::date AS fim,\n" +
                "\tpla.descricao AS plano,\n" +
                "\tmpar.codigo AS codigoMovParcela,\n" +
                "\tmpar.descricao,\n" +
                "\tmpar.datavencimento::date AS datavencimento,\n" +
                "\tmpar.situacao,\n" +
                "\tmpar.valorparcela,\n" +
                "\ttrunc(((mpar.valorparcela/30) * 15)::NUMERIC,2) AS novo_valorparcela\n" +
                "FROM cliente cli \n" +
                "\tINNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n" +
                "\tINNER JOIN contrato con ON con.pessoa = pes.codigo \n" +
                "\tLEFT JOIN plano pla ON pla.codigo = con.plano\n" +
                "\tLEFT JOIN movparcela mpar ON mpar.contrato = con.codigo \n" +
                "WHERE 1 = 1\n" +
                "AND con.situacao = 'AT'\n" +
                "AND mpar.datavencimento BETWEEN '" + dataInicioVencimento + "' AND '" + dataFimVencimento + "'\n" +
                "AND mpar.situacao = 'EA'\n" +
                "AND cli.codigomatricula IN (" + codigosMatriculas + ")";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", con);
        int atual = 0;

        while (rs.next()) {
            Integer codigoMatricula = rs.getInt("codigomatricula");
            String nome = rs.getString("nome");
            Integer codigoContrato = rs.getInt("contrato");
            Integer codigoParcela = rs.getInt("codigoMovParcela");
            Double novoValorParcela = rs.getDouble("novo_valorparcela");


            MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorCodigo(codigoParcela, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            adicionarLog(String.format("%d\\%d - %d - %s - Contrato: %d - Parcela: %d %s %s Valor antigo: %.2f -> Novo Valor: %.2f\n", ++atual, total, codigoMatricula, nome, codigoContrato,
                    movParcelaVO.getCodigo(), movParcelaVO.getDescricao(), Calendario.getDataAplicandoFormatacao(movParcelaVO.getDataVencimento(), "dd/MM/yyyy"), movParcelaVO.getValorParcela(), novoValorParcela));

            renegociarParcela(movParcelaVO, novoValorParcela, usuarioVO, con);
        }
    }

    public static void renegociarParcela(MovParcelaVO movParcelaVO, Double novoValorParcela, UsuarioVO usuarioVO, Connection con) throws Exception {
        MovParcela movParcelaDAO = new MovParcela(con);

        Double valorDiferencaRenegociar = novoValorParcela - movParcelaVO.getValorParcela();
        MovParcelaVO parcelaTaxa = new MovParcelaVO();
        parcelaTaxa.setDescricao("");
        parcelaTaxa.setDataVencimento(Calendario.hoje());

        MovParcelaVO parcelaDesconto = new MovParcelaVO();
        parcelaDesconto.setDescricao("");
        parcelaDesconto.setDataVencimento(Calendario.hoje());

        String tipoProdutoExtra;

        String descricaoParcela = "";

        Double valorFinalNovaParcela = movParcelaVO.getValorParcela();
        if (valorDiferencaRenegociar < 0.0) {
            tipoProdutoExtra = "DE";
            descricaoParcela = "DESCONTO";
            parcelaDesconto.setValorParcela(valorDiferencaRenegociar * -1);
            valorFinalNovaParcela -= parcelaDesconto.getValorParcela();
        } else {
            tipoProdutoExtra = "TX";
            descricaoParcela = "ACRESCIMO";
            parcelaTaxa.setValorParcela(valorDiferencaRenegociar);
            valorFinalNovaParcela += parcelaTaxa.getValorParcela();
        }

        List<MovParcelaVO> parcelasRenegociar = new ArrayList<>();
        parcelasRenegociar.add(movParcelaVO);

        // parcela desconto\acrescimo
        MovParcelaVO parcelaRenegociar = new MovParcelaVO();
        parcelaRenegociar.setDescricao(descricaoParcela);
        parcelaRenegociar.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorDiferencaRenegociar));
        parcelaRenegociar.setDataVencimento(Calendario.hoje());
        parcelasRenegociar.add(parcelaRenegociar);

        // Parcelas Renegociadas
        List<MovParcelaVO> parcelasRenegociadas = new ArrayList<>();
        MovParcelaVO novaParcela = (MovParcelaVO) movParcelaVO.getClone(true);
        novaParcela.setDescricao("PARCELA RENEGOCIADA");
        novaParcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorFinalNovaParcela));
        novaParcela.setDataRegistro(Calendario.hoje());
        parcelasRenegociadas.add(novaParcela);

        movParcelaDAO.renegociarParcelas(parcelasRenegociar, parcelasRenegociadas, parcelaDesconto, parcelaTaxa, tipoProdutoExtra, false, null, null, 0.0, false, usuarioVO, true, false, true, null, null);
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }


}

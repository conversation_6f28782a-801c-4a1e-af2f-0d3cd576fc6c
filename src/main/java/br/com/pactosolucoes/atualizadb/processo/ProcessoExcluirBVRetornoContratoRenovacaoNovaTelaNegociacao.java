package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoExcluirBVRetornoContratoRenovacaoNovaTelaNegociacao {

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "greenlife";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            removeBvRetornoContratoRenovacaoNovaTela(c);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoExcluirBVRetornoContratoRenovacaoNovaTelaNegociacao.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void removeBvRetornoContratoRenovacaoNovaTela(Connection con) {
        try {
            Uteis.logarDebug("INÍCIO | ProcessoExcluirBVRetornoContratoRenovacaoNovaTelaNegociacao");

            StringBuilder sqlDeleteClienteMensagem = new StringBuilder();
            sqlDeleteClienteMensagem.append("delete from clientemensagem\n")
                    .append("where questionariocliente in (\n")
                    .append("select distinct quest.codigo from questionariocliente quest\n")
                    .append("inner join cliente cli on quest.cliente = cli.codigo\n")
                    .append("inner join contrato co on co.pessoa = cli.pessoa\n")
                    .append("where co.situacaocontrato = 'RN'\n")
                    .append("and co.origemsistema = 17\n")
                    .append("and quest.tipobv = 2\n")
                    .append("and quest.data::date between '2024-12-19'::date AND '2025-01-08'::date);");
            try (Statement stm = con.createStatement()) {
                stm.execute(sqlDeleteClienteMensagem.toString());
            }

            StringBuilder sqlDeleteQuestionarioCliente = new StringBuilder();
            sqlDeleteQuestionarioCliente.append("delete from questionariocliente\n")
                    .append("where codigo in (\n")
                    .append("select distinct quest.codigo from questionariocliente quest\n")
                    .append("inner join cliente cli on quest.cliente = cli.codigo\n")
                    .append("inner join contrato co on co.pessoa = cli.pessoa\n")
                    .append("where co.situacaocontrato = 'RN'\n")
                    .append("and quest.tipobv = 2\n")
                    .append("and co.origemsistema = 17\n")
                    .append("and quest.data::date between '2024-12-19'::date AND '2025-01-08'::date);");
            try (Statement stm = con.createStatement()) {
                stm.execute(sqlDeleteQuestionarioCliente.toString());
            }
            Uteis.logarDebug("FIM | ProcessoExcluirBVRetornoContratoRenovacaoNovaTelaNegociacao");
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoExcluirBVRetornoContratoRenovacaoNovaTelaNegociacao - " + ex.getMessage());
        }
    }
}

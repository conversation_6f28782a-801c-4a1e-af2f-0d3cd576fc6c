package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by luiz on 27/04/2016.
 */
public class ProdutosColetivoSelfit extends SuperEntidade {

    public ProdutosColetivoSelfit() throws Exception {
    }

    private static void gerarProdutoColetivoSelfit(final Connection con) {
//        Uteis.logar(null, "Iniciando Lançamento Coletivo Selfit...");
        try {

            //CONSULTAR OS PLANOS
            List<PlanoVO> listaPlanos = new ArrayList<PlanoVO>();
            PreparedStatement pst = con.prepareStatement("select codigo,descricao from plano where descricao ilike '%blue%';");
            ResultSet dadosSQL = pst.executeQuery();

            while (dadosSQL.next()) {
                PlanoVO planoVO = new PlanoVO();
                planoVO.setCodigo(dadosSQL.getInt("codigo"));
                planoVO.setDescricao(dadosSQL.getString("descricao"));
                listaPlanos.add(planoVO);
            }

            if (UteisValidacao.emptyList(listaPlanos)) {
                throw new Exception("Nenhum plano com a descricao BLUE encontrado");
            }
            String codigosPlanos = Uteis.retornarCodigos(listaPlanos);


            //CONSULTAR O PRODUTO
            List<ProdutoVO> listaProduto = new ArrayList<ProdutoVO>();
            PreparedStatement prt = con.prepareStatement("select codigo,descricao from produto where descricao  ilike '%acesso rede%';");
            ResultSet dadosProdutoSQL = prt.executeQuery();

            while (dadosProdutoSQL.next()) {
                ProdutoVO produtoVO = new ProdutoVO();
                produtoVO.setCodigo(dadosProdutoSQL.getInt("codigo"));
                produtoVO.setDescricao(dadosProdutoSQL.getString("descricao"));
                listaProduto.add(produtoVO);
            }

            if (UteisValidacao.emptyList(listaProduto)) {
                throw new Exception("Nenhum produto ACESSO REDE encontrado");
            } else if (listaProduto.size() > 1) {
                throw new Exception("Mais de um produto ACESSO REDE encontrado");
            }

            ProdutoVO produtoAcesso = listaProduto.get(0);

            //USUÁRIO RESPONSAVEL PELO LANÇAMENTO
            UsuarioVO usuarioVOResponsavel = getFacade().getUsuario().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);


            //LISTA DE CONTRATOS
            List<ContratoVO> listaContratos = getFacade().getContrato().consultar("select * from contrato where plano in (" + codigosPlanos + ")" +
                    " and codigo not in (select contrato from movproduto where contrato is not null and produto = " + produtoAcesso.getCodigo() + ") " +
                    " order by codigo desc;", Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            if (UteisValidacao.emptyList(listaContratos)) {
                throw new Exception("Nenhum contrato do plano BLUE encontrado sem o produto ACESSO REDE lançado");
            }

            Integer totalContratos = listaContratos.size();
            Integer atual = 0;

            Uteis.logar(null, "Total de Contratos " + totalContratos);

            for (ContratoVO contratoVO : listaContratos) {
                atual++;
                Uteis.logar(null, "Andamento: " + atual + " / " + totalContratos);
                try {
                    con.setAutoCommit(false);

                    //GERANDO O PRODUTO
                    MovProdutoVO produto = new MovProdutoVO();

                    //DATA DE LANÇAMENTO A DATA DE LANÇAMENTO DO CONTRATO
                    Date dataLancamento = contratoVO.getDataLancamento();

                    Integer ano = Uteis.getAnoData(dataLancamento);
                    String mes = Uteis.getMesReferencia(dataLancamento);

                    produto.setSituacao("PG");
                    produto.setDataInicioVigencia(contratoVO.getVigenciaDe());
                    produto.setDataFinalVigencia(contratoVO.getVigenciaAteAjustada());
                    produto.setAnoReferencia(ano);
                    produto.setMesReferencia(mes + "/" + ano);
                    produto.setResponsavelLancamento(usuarioVOResponsavel);
                    produto.setDataLancamento(dataLancamento);
                    produto.setTotalFinal(0.0);
                    produto.setPrecoUnitario(0.0);
                    produto.setQuantidade(1);
                    produto.setDescricao(produtoAcesso.getDescricao());
                    produto.setEmpresa(contratoVO.getEmpresa());
                    produto.setPessoa(contratoVO.getPessoa());
                    produto.setContrato(contratoVO);
                    produto.setProduto(produtoAcesso);

                    getFacade().getMovProduto().incluir(produto);


                    //GERANDO A PARCELA
                    MovParcelaVO parcela = new MovParcelaVO();

                    parcela.setDescricao("PARCELA 1");
                    parcela.setResponsavel(usuarioVOResponsavel);
                    parcela.setValorParcela(0.0);
                    parcela.setContrato(contratoVO);
                    parcela.setSituacao("PG");
                    parcela.setDataVencimento(dataLancamento);
                    parcela.setDataRegistro(dataLancamento);
                    parcela.setEmpresa(contratoVO.getEmpresa());
                    parcela.setPessoa(contratoVO.getPessoa());
                    parcela.setDataCobranca(dataLancamento);
                    parcela.setReagendada(false);
                    parcela.setParcelaDCC(false);


                    //GERANDO MOVPRODUTOPARCELA
                    MovProdutoParcelaVO produtoParcela = new MovProdutoParcelaVO();
                    produtoParcela.setMovParcela(parcela.getCodigo());
                    produtoParcela.setMovProduto(produto.getCodigo());
                    produtoParcela.setValorPago(parcela.getValorParcela());
                    produtoParcela.setMovProdutoVO(produto);

                    parcela.getMovProdutoParcelaVOs().add(produtoParcela);

                    getFacade().getMovParcela().incluir(parcela);

                    con.commit();

                    Uteis.logar(null, "Produto " + produto.getCodigo() + " lançado para o contrato " + contratoVO.getCodigo());
                } catch (Exception e) {
                    con.rollback();
                    con.setAutoCommit(true);
                    Uteis.logar(null, "ERRO ao lançar produto para o contrato " + contratoVO.getCodigo());
                } finally {
                    con.setAutoCommit(true);
                }
            }

        } catch (Exception e) {
            Uteis.logar(null, "ERRO Lançar Produto Selfit... " + e.getMessage());
        }
    }

    private static void corrigirVigenciaProdutosSelfit(final Connection con) {
        Uteis.logar(null, "Iniciando a correção dos produtos Selfit...");
        try {

            //CONSULTAR O PRODUTO
            List<ProdutoVO> listaProduto = new ArrayList<ProdutoVO>();
            PreparedStatement prt = con.prepareStatement("select codigo,descricao from produto where descricao  ilike '%acesso rede%';");
            ResultSet dadosProdutoSQL = prt.executeQuery();

            while (dadosProdutoSQL.next()) {
                ProdutoVO produtoVO = new ProdutoVO();
                produtoVO.setCodigo(dadosProdutoSQL.getInt("codigo"));
                produtoVO.setDescricao(dadosProdutoSQL.getString("descricao"));
                listaProduto.add(produtoVO);
            }

            if (UteisValidacao.emptyList(listaProduto)) {
                throw new Exception("Nenhum produto ACESSO REDE encontrado");
            } else if (listaProduto.size() > 1) {
                throw new Exception("Mais de um produto ACESSO REDE encontrado");
            }

            ProdutoVO produtoAcesso = listaProduto.get(0);

            List<MovProdutoVO> listaProdutos = new ArrayList<MovProdutoVO>();
            PreparedStatement pst = con.prepareStatement("select\n" +
                    "m.codigo,\n" +
                    "c.codigo as contrato,\n" +
                    "c.vigenciade,\n" +
                    "c.vigenciaateajustada,\n" +
                    "m.datainiciovigencia,\n" +
                    "m.datafinalvigencia\n" +
                    "from movproduto m\n" +
                    "inner join contrato c on c.codigo = m.contrato\n" +
                    "where m.produto is not null \n" +
                    "and m.produto = " + produtoAcesso.getCodigo() + " ;");
            ResultSet dadosSQL = pst.executeQuery();

            while (dadosSQL.next()) {
                MovProdutoVO movProdutoVO = new MovProdutoVO();
                movProdutoVO.setCodigo(dadosSQL.getInt("codigo"));
                movProdutoVO.getContrato().setCodigo(dadosSQL.getInt("contrato"));
                movProdutoVO.getContrato().setVigenciaDe(dadosSQL.getDate("vigenciade"));
                movProdutoVO.getContrato().setVigenciaAteAjustada(dadosSQL.getDate("vigenciaateajustada"));
                listaProdutos.add(movProdutoVO);
            }

            Integer totalProdutos = listaProdutos.size();
            Integer atual = 0;

            Uteis.logar(null, "Total de Produtos " + totalProdutos);

            for (MovProdutoVO movProdutoVO : listaProdutos) {
                atual++;
                Uteis.logar(null, "Andamento: " + atual + " / " + totalProdutos);
                try {
                    con.setAutoCommit(false);

                    String sql = "update movproduto set  datainiciovigencia  = '" + Uteis.getData(movProdutoVO.getContrato().getVigenciaDe())
                            + "', datafinalvigencia  = '" + Uteis.getData(movProdutoVO.getContrato().getVigenciaAteAjustada())
                            + "' where codigo = " + movProdutoVO.getCodigo();
                    PreparedStatement teste = con.prepareStatement(sql);
                    teste.execute();

                    con.commit();
                } catch (Exception e) {
                    con.rollback();
                    con.setAutoCommit(true);
                    Uteis.logar(null, "ERRO ao alterar vigencia produto " + movProdutoVO.getCodigo());
                } finally {
                    con.setAutoCommit(true);
                }
            }

        } catch (Exception e) {
            Uteis.logar(null, "ERRO Alterar vigencia produtos Selfit... " + e.getMessage());
        }
    }

    public static void main(String... args) {
        try {
            String chave = "boaviagem02";
            if (args.length > 0) {
                chave = args[0];
            }
//            Uteis.logar(null, "################  Iniciando Lançamento Coletivo Produto Selfit CHAVE: " + chave + " ################");

            Connection c = DriverManager.getConnection("*********************************************************", "postgres", "pactodb");

//            Connection c = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(c);
            corrigirVigenciaProdutosSelfit(c);
//            Uteis.logar(null, "################  Finalizado Lançamento Coletivo Produto CHAVE: " + chave + " ################");
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}
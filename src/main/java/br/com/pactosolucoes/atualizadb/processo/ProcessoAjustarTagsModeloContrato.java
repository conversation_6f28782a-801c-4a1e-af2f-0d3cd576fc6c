package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Questionario;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAjustarTagsModeloContrato {

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "engenhariadocorpofozdoiguacu";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            ajustarTagsModeloContrato(c);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAjustarTagsModeloContrato.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarTagsModeloContrato(Connection con) {
        try {
            Uteis.logarDebug("INÍCIO | ProcessoAjustarTagsModeloContrato");

            StringBuilder sqlSelectModelosContratoSemTags = new StringBuilder();
            sqlSelectModelosContratoSemTags.append("SELECT\n");
            sqlSelectModelosContratoSemTags.append("\tptp.texto as texto,\n");
            sqlSelectModelosContratoSemTags.append("\tptp.codigo as codigo\n");
            sqlSelectModelosContratoSemTags.append("FROM planotextopadrao ptp\n");
            sqlSelectModelosContratoSemTags.append("WHERE NOT EXISTS (SELECT codigo\n");
            sqlSelectModelosContratoSemTags.append("\tFROM planotextopadraotag\n");
            sqlSelectModelosContratoSemTags.append("\tWHERE planotextopadrao = ptp.codigo)\n");
            sqlSelectModelosContratoSemTags.append("AND ptp.texto IS NOT NULL\n");
            sqlSelectModelosContratoSemTags.append("AND ptp.texto <> ''\n");

            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlSelectModelosContratoSemTags.toString())) {
                    while (rs.next()) {
                        String texto = rs.getString("texto");
                        Integer codigo = rs.getInt("codigo");
                        String tag = "";
                        while ((texto.indexOf("[") != -1) && (texto.indexOf("]") != -1)) {
                            int posicaoIni = texto.indexOf("[");
                            int posicaoFim = texto.indexOf("]");
                            tag = texto.substring(posicaoIni, posicaoFim + 1);
                            String sqlInserirTagModeloContrato = "INSERT INTO planotextopadraotag (tag, planotextopadrao) VALUES(?, ?)";
                            try (PreparedStatement psSqlInserirTag = con.prepareStatement(sqlInserirTagModeloContrato)) {
                                psSqlInserirTag.setString(1, tag);
                                psSqlInserirTag.setInt(2, codigo);
                                psSqlInserirTag.execute();
                            }
                            texto = texto.substring(0, posicaoIni) + texto.substring(posicaoFim + 1);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoAjustarTagsModeloContrato - " + ex.getMessage());
        } finally {
            Uteis.logarDebug("FIM | ProcessoAjustarTagsModeloContrato");
        }
    }

}

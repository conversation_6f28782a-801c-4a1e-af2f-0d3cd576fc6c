package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "19/06/2025",
        descricao = "Atualizar descrição das permissões do gympass para wellhub",
        motivacao = "MJ-1130")
public class AtualizacaoTicketMJ1130 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update permissao set tituloapresentacao = '9.37 - Permite visualizar relatório Wellhub por periodo' " +
                    "WHERE tituloapresentacao ILIKE '9.37%';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update permissao set tituloapresentacao = '9.63 - Excluir Token Wellhub' " +
                    "WHERE tituloapresentacao ILIKE '9.63%';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update permissao set tituloapresentacao = '9.86 - Visualizar BI - Wellhub' " +
                    "WHERE tituloapresentacao ILIKE '9.86%';", c);
        }
    }
}

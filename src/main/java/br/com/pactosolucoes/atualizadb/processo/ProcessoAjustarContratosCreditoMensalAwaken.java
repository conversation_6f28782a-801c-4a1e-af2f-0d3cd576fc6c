/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;


public class ProcessoAjustarContratosCreditoMensalAwaken {

    public ProcessoAjustarContratosCreditoMensalAwaken() {
    }

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("***************************************", "zillyonweb", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);

            String planos = "5,6,7";

            ajustarContratos(planos, con);


        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    private static void ajustarContratos(String planos, Connection con) throws Exception {
        Date d1 = Calendario.hoje();
        System.out.println("AjustarContratos - Início em : " + new Date());

        ResultSet rs = SuperFacadeJDBC.criarConsulta("select \n" +
                "distinct(c.codigo) as contrato,\n" +
                "c.vigenciade as vigenciadecontrato,\n" +
                "pdc.quantidadeCreditoMensal as quantidadeCreditoMensal_PLANO,\n" +
                "cdc.creditoTreinoNaoCumulativo,\n" +
                "cdc.quantidadeCreditoMensal,\n" +
                "cdc.dataUltimoCreditoMensal,\n" +
                "cdc.codigo as codigocontratoduracaocreditotreino\n" +
                "from contratoduracaocreditotreino  cdc\n" +
                "inner join contratoduracao cd on cd.codigo = cdc.contratoduracao\n" +
                "inner join contrato c on c.codigo = cd.contrato\n" +
                "inner join plano p on p.codigo = c.plano\n" +
                "inner join planoduracao pd on pd.plano = p.codigo\n" +
                "inner join planoduracaocreditotreino pdc on pdc.planoduracao = pd.codigo\n" +
                "where c.plano in (" + planos + ")\n" +
                "and c.vigenciaateajustada::date > CURRENT_DATE\n" +
                "and cdc.quantidadecreditomensal is null", con);

        while (rs.next()) {

            Integer contrato = rs.getInt("contrato");
            Integer codigocontratoduracaocreditotreino = rs.getInt("codigocontratoduracaocreditotreino");
            Integer quantidadeCreditoMensal_PLANO = rs.getInt("quantidadeCreditoMensal_PLANO");
            Date vigenciadecontrato = rs.getDate("vigenciadecontrato");

            System.out.println("Ajustando Contrato: " + contrato + " -- Data Início Contrato: " + Uteis.getData(vigenciadecontrato));

            Date dataReferencia = vigenciadecontrato;
            Date dataUltimoCreditoMensal = Calendario.hoje();
            while (true) {

                Date dataReferenciaSomada = Uteis.somarDias(dataReferencia, 30);
                if (Calendario.maior(dataReferencia, Calendario.hoje())) {
                    dataUltimoCreditoMensal = Uteis.somarDias(dataReferencia, -30);
                    break;
                } else {
                    dataReferencia = dataReferenciaSomada;
                }
            }

            String update = "update contratoDuracaoCreditoTreino set dataUltimoCreditoMensal = '" + Uteis.getData(dataUltimoCreditoMensal) + "', creditoTreinoNaoCumulativo = true, quantidadeCreditoMensal = " + quantidadeCreditoMensal_PLANO + " where codigo = " + codigocontratoduracaocreditotreino;
            SuperFacadeJDBC.executarConsultaUpdate(update, con);
//            System.out.println(update);
        }

        Date d2 = Calendario.hoje();
        System.out.println("Tempo Total AjustarContratos: " + (d2.getTime() - d1.getTime()));
    }


}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Waller Maciel",
        data = "07/10/2024",
        descricao = "TW-885 - Indice para performar consulta de cliente por nomeConsulta utilizando operador LIKE",
        motivacao = "TW-885 ")
public class AtualizacaoTicketTW885 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_nomeconsulta_trgm ON situacaoclientesinteticodw USING gin (nomeconsulta gin_trgm_ops);", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Glauco Troncha Camargo",
        data = "17/10/2024",
        descricao = "IN-1034 - Tabela para armazenar os dados dos processos rodados na engrenagem",
        motivacao = "IN-1034 - Necessidade de mapear os eventos lentos do sistema ")
public class AtualizacaoTicketIN1034 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE logprocessosistema (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    processo text NULL,\n" +
                    "    inicio timestamp NOT NULL,\n" +
                    "    final timestamp NULL,\n" +
                    "    usuario integer NULL,\n" +
                    "    usuariooamd text NULL);", c);

        }
    }
}

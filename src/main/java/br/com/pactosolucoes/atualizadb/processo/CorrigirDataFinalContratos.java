package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Date;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;

public class CorrigirDataFinalContratos {

	public static void processarDatasContratos(Connection con){
		try{
			
			
			String sql = "SELECT c.codigo, c.vigenciade,c.pessoa, c.vigenciaateajustada, c.contratobaseadorenovacao,  (select codigo from historicocontrato where contrato = " +
			"c.codigo order by codigo desc limit 1 ) as ultimohistorico, EXISTS (SELECT codigo from periodoacessocliente  where contrato = c.codigo) as temperiodo " +
			" FROM contrato c WHERE regimerecorrencia "+
			"and  extract(day from vigenciaateajustada) = extract(day from vigenciade)";

			ResultSet dados = con.prepareStatement(sql).executeQuery();
			int i = 0;
			while(dados.next()){
				System.out.println(i++);
				StringBuilder sqls = new StringBuilder();
				Date vigenciaAteAjustada = dados.getDate("vigenciaateajustada");
				int codigo = dados.getInt("codigo");
				int codigoUltimoHistorico = dados.getInt("ultimohistorico");
				java.sql.Date dataFim = Uteis.getDataJDBC(Uteis.obterDataAnterior(vigenciaAteAjustada, 1));
				sqls.append("UPDATE contrato SET vigenciaate = '"+dataFim+"', ");
				sqls.append(" vigenciaateajustada = '"+dataFim+"',");
				sqls.append(" dataprevistarenovar = '"+dataFim+"',");
				sqls.append(" dataprevistarematricula = '"+dataFim);
				sqls.append("' WHERE codigo = "+codigo+"; \n");
				sqls.append(" UPDATE historicocontrato SET datafinalsituacao = '"+dataFim+"' WHERE codigo = "+codigoUltimoHistorico+";\n");
				
				if(!dados.getBoolean("temperiodo")){
					java.sql.Date dataInicio = Uteis.getDataJDBC(dados.getDate("vigenciade"));
					sqls.append(" INSERT into periodoacessocliente (pessoa, contrato, datainicioacesso, datafinalacesso, contratobaseadorenovacao, tipoacesso) ");
					sqls.append(" values ("+dados.getInt("pessoa")+", "+codigo+", '"+dataInicio+"', '"+dataFim+"', "+dados.getInt("contratobaseadorenovacao")+", 'CA')"); 
					
				}
				
				con.prepareStatement(sqls.toString()).execute();
			}
//			System.out.println(sqls.toString());
			
			System.out.println("FIM - "+new Date());
		}catch (Exception e) {
			e.printStackTrace();
		}
		
		
	}
	
	public static void main(String[] args) {
		try {
			Conexao con = Conexao.getInstance();
			processarDatasContratos(con.getConexao());
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
}

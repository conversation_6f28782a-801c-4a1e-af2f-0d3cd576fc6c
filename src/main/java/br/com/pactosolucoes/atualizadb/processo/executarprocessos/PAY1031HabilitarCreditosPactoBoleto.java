package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "23/06/2025",
        descricao = "Habilitar cobrança de créditos pacto de boletos",
        motivacao = "PAY-1031")
public class PAY1031HabilitarCreditosPactoBoleto implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa SET cobrarCreditoPactoBoleto = TRUE;", c);
        }
    }
}



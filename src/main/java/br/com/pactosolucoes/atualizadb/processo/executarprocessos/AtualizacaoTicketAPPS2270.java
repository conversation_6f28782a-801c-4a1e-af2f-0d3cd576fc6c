package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Kai<PERSON> Sanchez",
        data = "08/01/2025",
        descricao = "Atualização do ticket APPS-2270",
        motivacao = "Adicionar coluna app na tabela horarioequipamentoaluno")
public class AtualizacaoTicketAPPS2270 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE horarioequipamentoaluno\n" +
                    "ADD COLUMN app BOOLEAN DEFAULT FALSE NOT NULL;", c);
            SuperFacadeJDBC.executarConsulta("CREATE UNIQUE INDEX unico_horarioturma_diaaula_equipamento ON horarioequipamentoaluno (horarioturma, diaaula, equipamento) " +
                    "WHERE equipamento IS NOT NULL AND equipamento <> '';", c);
        }
    }
}

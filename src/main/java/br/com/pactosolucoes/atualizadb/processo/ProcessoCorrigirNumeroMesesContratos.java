package br.com.pactosolucoes.atualizadb.processo;

import importador.LeitorExcel2010;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

public class ProcessoCorrigirNumeroMesesContratos {

    public static void main(String[] args) throws SQLException {
        try {
            Connection con = DriverManager.getConnection("************************************************", "postgres", "pactodb");
            String codigoMatriculas = "";

            corrigirNumeroMesesContratos(con, codigoMatriculas);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void corrigirNumeroMesesContratos(Connection con, String codigosMatriculas) throws Exception {
        Contrato contratoDAO = new Contrato(con);
        MovProduto movProdutoDAO = new MovProduto(con);
        MovParcela movParcelaDAO = new MovParcela(con);

        List<Integer> codigosContratos = new ArrayList<>();

        String sql = "SELECT con.codigo, cli.codigomatricula FROM contrato con \n" +
                "INNER JOIN contratoduracao cd ON cd.contrato = con.codigo \n" +
                "INNER JOIN plano pla ON pla.codigo = con.plano \n" +
                "INNER JOIN cliente cli ON cli.pessoa =  con.pessoa \n" +
                "INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n" +
                "WHERE cli.codigomatricula IN (" + codigosMatriculas + ") \n " +
                "AND cd.numeromeses = 1 \n " +
                "AND cli.situacao = 'AT'\n";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);

        int total = SuperFacadeJDBC.contar("select count(s.*) from (" + sql + ") s", con);
        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        while (rs.next()) {
            try {
                con.setAutoCommit(false);

                System.out.printf("%d\\%d - Processando contrato: %s matricula: %s \n",
                        ++atual, total, rs.getInt("codigo"), rs.getInt("codigomatricula"));

                ContratoVO contratoVO = contratoDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                contratoVO.setMovProdutoVOs(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                Long dias = Uteis.nrDiasEntreDatas(contratoVO.getVigenciaDe(), contratoVO.getVigenciaAteAjustada());
                Integer numeroMeses = dias < 30 ? 1 : (new Long(dias / 30).intValue());

                if (contratoVO.getContratoDuracao().getNumeroMeses().equals(numeroMeses)) {
                    System.out.println("\tContrato já está com a duração correta.");
                    continue;
                }

                SuperFacadeJDBC.executarUpdate("update contratoduracao set numeromeses = " + numeroMeses + " where contrato = " + contratoVO.getCodigo(), con);

                List<MovProdutoVO> movProdutoVOS = movProdutoDAO.consultarPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyList(movProdutoVOS)) {
                    System.out.println("\tContrato sem movimentos de produtos.");
                    continue;
                }

                Iterator it = movProdutoVOS.iterator();
                while (it.hasNext()) {
                    MovProdutoVO movProdutoVO = (MovProdutoVO) it.next();
                    if (!movProdutoVO.getProduto().getTipoProduto().equals("PM")) {
                        it.remove();
                    }
                }
                if (UteisValidacao.emptyList(movProdutoVOS)) {
                    System.out.println("\tContrato sem movimentos de produtos.");
                    continue;
                }

                MovProdutoVO movProdutoRef = movProdutoVOS.get(0);
                String codigosMovProdutosDeletar = movProdutoVOS.stream().map(movProdutoVO -> movProdutoVO.getCodigo().toString()).collect(Collectors.joining(","));
                Double valorTotal = movProdutoVOS.stream().mapToDouble(movProdutoVO -> movProdutoVO.getTotalFinal()).sum();
                Double valorMovProduto = Uteis.arredondarForcando2CasasDecimais(valorTotal / numeroMeses);
                Double residuo = valorTotal - (valorMovProduto * numeroMeses);

                SuperFacadeJDBC.executarUpdate("delete from movproduto where codigo in (" + codigosMovProdutosDeletar + ")", con);

                for (int i = 1; i <= numeroMeses; i++) {
                    Date referencia = Uteis.somarMeses(Uteis.getDate("01/" + movProdutoRef.getMesReferencia()), 1);

                    movProdutoRef.setTotalFinal(valorMovProduto);
                    if (residuo > 0) {
                        movProdutoRef.setTotalFinal(valorMovProduto + residuo);
                        residuo = 0.0;
                    }
                    movProdutoRef.setValorFaturado(movProdutoRef.getTotalFinal());
                    movProdutoRef.setPrecoUnitario(movProdutoRef.getTotalFinal());
                    movProdutoRef.setAnoReferencia(Uteis.getAnoData(referencia));
                    movProdutoRef.setMesReferencia(Uteis.getDataAplicandoFormatacao(referencia, "MM/yyyy"));
                    movProdutoRef.setDescricao(String.format("%s - %s", contratoVO.getPlano().getDescricao().toUpperCase(),
                            Uteis.getDataAplicandoFormatacao(referencia, "MM/yyyy")));
                    movProdutoDAO.incluirSemValidar(movProdutoRef);
                }

                codigosContratos.add(contratoVO.getCodigo());

                con.commit();
                sucesso++;
            } catch (Exception e) {
                falha++;
                e.printStackTrace();
                con.rollback();
            } finally {
                con.setAutoCommit(true);
            }
        }

        System.out.println("=============> Refazendo vinculos movproduto e movparcela...");
        codigosContratos.forEach(codigoContrato -> {
            try {
                RefazerVinculoMovProdutoParcelaContratos.refazerMovProdutoParcela(con, codigoContrato);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        System.out.printf("\n\nSucesso: %d - Falha: %d\n", sucesso, falha);

    }
}

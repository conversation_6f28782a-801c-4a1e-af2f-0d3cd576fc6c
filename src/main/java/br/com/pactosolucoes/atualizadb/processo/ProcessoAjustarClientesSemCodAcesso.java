package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Cliente;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAjustarClientesSemCodAcesso {

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "abfitnesssaogoncalo";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            ajustarClientesSemCodAcesso(c);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAjustarClientesSemCodAcesso.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarClientesSemCodAcesso(Connection con) throws Exception {
        try {
            Cliente clienteDao = new Cliente(con);
            Uteis.logarDebug("INÍCIO | ProcessoAjustarClientesSemCodAcesso");
            StringBuilder sqlSelectClientesSemCodAcesso = new StringBuilder();
            sqlSelectClientesSemCodAcesso.append("SELECT codigo as codCliente\n");
            sqlSelectClientesSemCodAcesso.append("FROM cliente\n");
            sqlSelectClientesSemCodAcesso.append("WHERE codacesso IS NULL\n");
            sqlSelectClientesSemCodAcesso.append("OR codacesso ILIKE ''");
            try (java.sql.Statement stm = con.createStatement()) {
                try (java.sql.ResultSet rs = stm.executeQuery(sqlSelectClientesSemCodAcesso.toString())) {
                    while (rs.next()) {
                        Integer codCliente = rs.getInt("codCliente");
                        ClienteVO clienteVO = clienteDao.consultarPorCodigo(codCliente, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                        String codAcesso = clienteDao.gerarCodigoAcesso(clienteVO, 0);
                        StringBuilder updateCodAcessoCliente = new StringBuilder();
                        updateCodAcessoCliente.append("UPDATE cliente\n");
                        updateCodAcessoCliente.append("SET codacesso = ?\n");
                        updateCodAcessoCliente.append("WHERE codigo = ?\n");
                        try (PreparedStatement psSqlUpdateCodAcessoCliente = con.prepareStatement(updateCodAcessoCliente.toString())) {
                            psSqlUpdateCodAcessoCliente.setString(1, codAcesso);
                            psSqlUpdateCodAcessoCliente.setInt(2, codCliente);
                            psSqlUpdateCodAcessoCliente.execute();
                        }
                    }
                }

            }

        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoAjustarClientesSemCodAcesso - " + ex.getMessage());
        } finally {
            Uteis.logarDebug("FIM | ProcessoAjustarClientesSemCodAcesso");
        }

    }

}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luis Antônio de Melo Gomes",
        data = "04/12/2024",
        descricao = "Atualizar tabela de configuração de IA do modulo ADM",
        motivacao = "PRPI-309")
public class AtualizacaoTicket309 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
        SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaocrmia\n" +
                " ADD COLUMN whatsappBusiness boolean default false NOT NULL;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Gabriel <PERSON>",
        data = "19/08/2024",
        descricao = "Adicionar a nova permissão ao perfil corretos",
        motivacao = "MJ-112")
public class AdicionarNovaPermissaoMJ112 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo as codperfilacesso from perfilacesso where codigo = 1", c);
            if (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '2.86 - Permitir criar/editar e excluir avisos internos','(0)(1)(2)(3)(9)(12)', "
                        + " 'PermitirAvisosInternos', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);

            }
        }
    }

}


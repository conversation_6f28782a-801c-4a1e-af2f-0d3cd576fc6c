/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.ResultSet;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;

/**
 *
 * <AUTHOR>
 */
public class CorrigirSinteticoProcessoDeRemocaoDeVinculos {
    
        public static void atualizarSinteticoTreino(Connection con) throws Exception{
            ResultSet consulta = SuperFacadeJDBC.criarConsulta("select distinct his.cliente, usu.codigo cliente from historic<PERSON>in<PERSON>lo his left join usuariomovel usu on usu.cliente = his.cliente where his.origem  = 'PROCESSAMENTO DIÁRIO' order by usu.codigo;", con);
            ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
            ClienteVO cliente = new ClienteVO();
           
            while (consulta.next()) {
                cliente.setCodigo(consulta.getInt(("cliente")));
                cliente.setDadosSinteticoPreparados(false);
                zwFacade.atualizarSintetico(cliente,
                Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_VINCULO, true);
            }

        }
    
}

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;

public class AjustarEmailsDuplicadosPorPessoa {

    public static void main(String... args) {
        try {
            String chave =args.length > 0 ? args[0] : "bodyhiiit";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            corrigirEmailsDuplicadosPorPessoa(c);
        } catch (Exception ex) {
            Logger.getLogger(RefazerVinculoMovProdutoParcelaContratos.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void corrigirEmailsDuplicadosPorPessoa(Connection con) throws Exception {
        String sqlSelectDelete = "SELECT 'DELETE FROM email WHERE email ILIKE ''' || REPLACE(e.email, '''', '''''') || ''' AND pessoa = ' || pessoa || ' AND codigo <> ' || MIN (e.codigo) || ';' AS deleteEmailDuplicado\n" +
                "FROM email e\n" +
                "GROUP BY email, pessoa HAVING count(e.codigo) > 1;";
        try (java.sql.Statement stm = con.createStatement()) {
            try (java.sql.ResultSet rs = stm.executeQuery(sqlSelectDelete)) {
                Uteis.logarDebug("INÍCIO | AjustarEmailsDuplicadosPorPessoa");
                while (rs.next()) {
                    String sqlUpdate = rs.getString("deleteEmailDuplicado");
                    stm.execute(sqlUpdate);
                }
                Uteis.logarDebug("FIM | AjustarEmailsDuplicadosPorPessoa");
            }
        }
    }

}

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.modulos.integracao.UsuarioMovelControle;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioEmail;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.TipoColaborador;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import servicos.integracao.TreinoWSConsumer;

import java.sql.Connection;
import java.util.List;

public class SincronizarUsuariosEClientesTreinoOAMD {

    public static void main(String[] args) {
        long a = System.currentTimeMillis();
        Uteis.logarDebug("SincronizarUsuariosEClientesTreino | Iniciando...");
        try {
            Uteis.debug = true;

            String chave = "teste";
            Connection con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(con);
            executar(chave, con);
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("SincronizarUsuariosEClientesTreino | ERRO: " + e.getMessage());
        } finally {
            long b = System.currentTimeMillis();
            Uteis.logarDebug("SincronizarUsuariosEClientesTreino | FIM -- Realizada em: " + ((b - a) / (1000 * 60)) + " minutos");
        }
    }

    public static void executar(String chave, Connection con) {
        //MENSAGEM DE PARCELA EM ATRASO
        atualizarMensagens(con);

        //SINCRONIZAR COM O TREINO
        sincronizarTodosUsuarios(chave, con);

        //SINCRONIZAR ALUNOS
        sincronizarCliente(chave, con);
    }

    private static void atualizarMensagens(Connection con) {
        try {
            Uteis.logarDebug("atualizarMensagens | Iniciando...");
            List<EmpresaVO> empresas = new Empresa(con).consultarEmpresas();
            RoboVO robo = new RoboVO();
            robo.setDia(Calendario.hoje());
            robo.setListaEmpresa(empresas);
            robo.processarClientesComParcelaVencida();
        } catch (Exception ex) {
            Uteis.logarDebug("atualizarMensagens | ERRO: " + ex.getMessage());
        } finally {
            Uteis.logarDebug("atualizarMensagens | FIM...");
        }
    }

    private static void sincronizarTodosUsuarios(String key, Connection con) {
        Usuario usuarioDAO;
        try {
            Uteis.logarDebug("sincronizarTodosUsuarios | Iniciando...");
            usuarioDAO = new Usuario(con);
            List<UsuarioVO> usuarios = usuarioDAO.consultarTodosAtivosSemAdministrador(Uteis.NIVELMONTARDADOS_TODOS);
            for (UsuarioVO usuarioVO : usuarios) {
                sincronizarUsuarioMovel(usuarioVO, key, con);
            }
        } catch (Exception ex) {
            Uteis.logarDebug("sincronizarTodosUsuarios | ERRO: " + ex.getMessage());
        } finally {
            usuarioDAO = null;
            Uteis.logarDebug("sincronizarTodosUsuarios | Fim...");
        }
    }

    private static void sincronizarCliente(String key, Connection con) {
        UsuarioMovel usuarioMovelDAO;
        try {
            Uteis.logarDebug("sincronizarCliente | Iniciando...");
            usuarioMovelDAO = new UsuarioMovel(con);
            List<UsuarioMovelVO> lista = usuarioMovelDAO.consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (UsuarioMovelVO usuarioMovelVO : lista) {
                usuarioMovelVO.setPropagarExcessao(true);
                try {
                    TreinoWSConsumer.sincronizarUsuario(key, usuarioMovelVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                try {
                    UsuarioMovelControle.adicionarUsuarioServicoDescobrir(key, usuarioMovelVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug("sincronizarCliente | ERRO: " + ex.getMessage());
        } finally {
            usuarioMovelDAO = null;
            Uteis.logarDebug("sincronizarCliente | Fim...");
        }
    }

    private static void sincronizarUsuarioMovel(UsuarioVO usuarioVO, String key, Connection con) {
        UsuarioEmail usuarioEmailDAO;
        TipoColaborador tipoColaboradorDAO;
        UsuarioMovel usuarioMovelDAO;
        try {
            usuarioEmailDAO = new UsuarioEmail(con);
            tipoColaboradorDAO = new TipoColaborador(con);
            usuarioMovelDAO = new UsuarioMovel(con);

            UsuarioEmailVO usuarioEmailVO = usuarioEmailDAO.consultarPorUsuario(usuarioVO.getCodigo());
            if (UteisValidacao.emptyNumber(usuarioEmailVO.getCodigo())) {
                Uteis.logarDebug("Usuário " + usuarioVO.getUsername() + " não tem email!");
            }
            UsuarioMovelVO userMovel = usuarioMovelDAO.consultarPorUsuario(usuarioVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            userMovel.setUsuarioEmailVO(usuarioEmailVO);
            userMovel.getUsuarioEmailVO().setUsuario(usuarioVO.getCodigo());
            userMovel.setColaborador(usuarioVO.getColaboradorVO());
            userMovel.setUsuarioZW(usuarioVO.getCodigo());
            userMovel.getColaborador().setListaTipoColaboradorVOs(tipoColaboradorDAO.consultarPorCodigoColaborador(userMovel.getColaborador().getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            userMovel.setNome(usuarioVO.getUsername());
            userMovel.setSenha(usuarioVO.getSenha());
            userMovel.setAtivo(true);
            userMovel.setEmpresa(usuarioVO.getColaboradorVO().getEmpresa().getCodigo());
            userMovel.setSenhaEncriptada(true);
            userMovel.setPropagarExcessao(true);

            if (UteisValidacao.emptyNumber(userMovel.getCodigo())) {
                usuarioMovelDAO.incluir(userMovel);
            } else {
                usuarioMovelDAO.alterar(userMovel);
            }
            sincronizaTreino(key, userMovel);
        } catch (Exception e) {
            Uteis.logarDebug("ERRO Sincronizar Usuario " + usuarioVO.getUsername() + " | ERRO: " + e.getMessage());
        } finally {
            usuarioEmailDAO = null;
            tipoColaboradorDAO = null;
            usuarioMovelDAO = null;
        }
    }

    private static void sincronizaTreino(String key, UsuarioMovelVO userMovel) {
        try {
            TreinoWSConsumer.sincronizarUsuario(key, userMovel);
            TreinoWSConsumer.sincronizarProfessor(key, userMovel.getColaborador().toProfessorSintetico(), userMovel.getColaborador().getEmpresa().getCodigo());
        } catch (Exception e) {
            Uteis.logarDebug("ERRO SincronizarTreino Usuário " + userMovel.getNome() + " | ERRO: " + e.getMessage());
        }
    }

}

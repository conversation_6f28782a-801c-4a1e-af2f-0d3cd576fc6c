package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;

/**
 * Created by Joao Alcides on 11/10/2016.
 */
public class TrocarPlanosFitnessSportCenter {

    public static void trocarPlanosContratoAluno(Connection con, Integer matricula, Integer planoNovo, Integer codigoProduto){
        try {
            System.out.println("Atualizando dados do aluno de matricula "+ matricula);
            String nomeNovoPlano = "";
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT descricao FROM plano WHERE codigo = " + planoNovo, con);
            if(rs.next()){
                nomeNovoPlano = rs.getString("descricao");
            }
            ResultSet rsContratos = SuperFacadeJDBC.criarConsulta("SELECT plano, codigo FROM contrato WHERE pessoa " +
                    "IN (SELECT pessoa FROM cliente WHERE codigomatricula = " + matricula+")", con);
            while(rsContratos.next()){
                SuperFacadeJDBC.executarConsulta("UPDATE contrato SET plano = "+planoNovo + " where codigo = "+rsContratos.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("UPDATE movproduto SET descricao = '"+nomeNovoPlano +
                        "' || ' - ' || mesreferencia  where contrato  = "+rsContratos.getInt("codigo")+
                        " and produto = "+codigoProduto, con);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String [] args) throws Exception{
        Connection con = DriverManager.getConnection("************************************************************", "postgres", "pactodb");
        trocarPlanosContratoAluno(con, 3756,3,5);
        GerarContratoDePlano.gerarContrato(con, 3, Uteis.getDate("14/12/2014"), Uteis.getDate("13/12/2015"), 3756, 12, 1, 600.0, 1);
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.HistoricoVinculoVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.HistoricoVinculo;
import negocio.facade.jdbc.basico.Vinculo;

import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;

public class ExcluirVinculosDuplicados extends SuperEntidade {
	
	public ExcluirVinculosDuplicados() throws Exception {
		super();
	}
	public ExcluirVinculosDuplicados(Connection con) throws Exception {
		super(con);
	}
	
	public void ajustar(){
		try {
			excluirVinculos(vinculosAExcluir());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public List<VinculoVO> vinculosAExcluir() throws Exception{
		List<VinculoVO> vinculos = new ArrayList<VinculoVO>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * FROM vinculo \n");
		sql.append("WHERE tipovinculo LIKE 'CO' \n");
		sql.append("AND codigo NOT IN (SELECT MAX(codigo) FROM vinculo WHERE tipovinculo LIKE 'CO' GROUP BY cliente) \n");
		
		ResultSet resultSet = ExcluirVinculosDuplicados.criarConsulta(sql.toString(), con);
		while(resultSet.next()){
			vinculos.add(Vinculo.montarDados(resultSet, Uteis.NIVELMONTARDADOS_MINIMOS, con));
		}
		return vinculos;
	}
	
	public void excluirVinculos(List<VinculoVO> vinculos) throws Exception{
		Vinculo vinculo = new Vinculo(con);
		HistoricoVinculo hvinculo = new HistoricoVinculo(con);
		for(VinculoVO obj : vinculos){
			HistoricoVinculoVO hist = new HistoricoVinculoVO();
			ColaboradorVO col = new ColaboradorVO();
			col.setCodigo(obj.getColaborador().getCodigo());
			hist.setColaborador(col);
			ClienteVO cli = new ClienteVO();
			cli.setCodigo(obj.getCliente().getCodigo());
			hist.setCliente(cli);
			hist.setTipoColaborador(TipoColaboradorEnum.CONSULTOR.getSigla());
			hist.setDataRegistro(Calendario.hoje());
			hist.setTipoHistoricoVinculo("SD");
			hist.setOrigem("EXCLUIR VINCULOS");
			hvinculo.incluir(hist);
			vinculo.excluir(obj);
			System.out.println("Excluído vínculo:"+obj.getCodigo());
		}
		
	}
	
	public static void main(String[] args)  {
		try {
			ExcluirVinculosDuplicados ajustar = new ExcluirVinculosDuplicados();
			ajustar.ajustar();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
	}



}

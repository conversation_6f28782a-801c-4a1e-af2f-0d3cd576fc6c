package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "05/11/2024",
        descricao = "Cria a 'mensagemCatraca' e 'bloquearAcessoAniversario' na tabela Operacao Coletiva",
        motivacao = "GC-1116: Bloquear alunos na catraca - Operação coletiva")
public class CriarColMsgCatracaBloqueioAniversarioTabelaOpColetiva implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        String sql = "ALTER TABLE OperacaoColetiva " +
                "ADD COLUMN mensagemCatraca VARCHAR(255), " +
                "ADD COLUMN bloquearAcessoAniversario BOOLEAN DEFAULT false;";
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoIncluirReciboParcelasAbertoMachida {

    private Connection connection;

    public ProcessoIncluirReciboParcelasAbertoMachida(Connection connection) {
        this.connection = connection;
    }

    public static void main(String[] args) {
        try {
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "bdzillyonmachidapedreira-2018-10-04");
            Conexao.guardarConexaoForJ2SE(c);

            final Integer contrato = null;
            final TipoFormaPagto tipoFormaPagto = TipoFormaPagto.AVISTA;

            ProcessoIncluirReciboParcelasAbertoMachida processo = new ProcessoIncluirReciboParcelasAbertoMachida(c);
            processo.incluirRecibo(contrato, tipoFormaPagto);
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void incluirRecibo(final Integer contrato, TipoFormaPagto tipoFormaPagto) throws SQLException {
        try {
            connection.setAutoCommit(false);
            Map<Integer, List<MovParcelaVO>> mapParcelasContratos = obterParcelasEmAberto(contrato);

            MovPagamento pagDAO = new MovPagamento(connection);

            FormaPagamento formaPagamentoDAO = new FormaPagamento(connection);
            FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarPorTipoFormaPagamentoAtiva(tipoFormaPagto.getSigla(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for (Integer codContrato : mapParcelasContratos.keySet()) {
                Double valorPagar = 0.0;
                Date dataVencimentoPrimeiraParcela = null;
                PessoaVO pessoaVO = null;
                EmpresaVO empresaVO = null;
                List<MovParcelaVO> parcelasContrato = mapParcelasContratos.get(codContrato);
                for (MovParcelaVO mp : parcelasContrato) {
                    valorPagar += mp.getValorParcela();
                    if (dataVencimentoPrimeiraParcela == null) {
                        dataVencimentoPrimeiraParcela = mp.getDataVencimento();
                    }
                    if (pessoaVO == null) {
                        pessoaVO = mp.getPessoa();
                    }
                    if (empresaVO == null) {
                        empresaVO = mp.getEmpresa();
                    }
                }

                MovPagamentoVO novo = new MovPagamentoVO();
                novo.setDataLancamento(dataVencimentoPrimeiraParcela);
                novo.setDataPagamento(dataVencimentoPrimeiraParcela);
                novo.setDataQuitacao(dataVencimentoPrimeiraParcela);
                novo.getPessoa().setCodigo(pessoaVO.getCodigo());
                novo.getPessoa().setNome(pessoaVO.getNome());
                novo.setNomePagador(pessoaVO.getNome());
                novo.setEmpresa(empresaVO);
                novo.setCredito(Boolean.FALSE);
                novo.setDepositoCC(Boolean.FALSE);
                novo.getResponsavelPagamento().setCodigo(2);
                novo.setMovPagamentoEscolhida(Boolean.TRUE);
                novo.setFormaPagamento(formaPagamentoVO);
                novo.setValor(valorPagar);
                novo.setValorTotal(valorPagar);
                List<MovPagamentoVO> pagamentos = new ArrayList<MovPagamentoVO>();
                pagamentos.add(novo);

                pagDAO.incluirListaPagamento(pagamentos, parcelasContrato, null, null, false, 0.0, false, null);

                System.out.println("Incluído recibo para: " + pessoaVO.getNome() + " - Contrato: " + contrato);
            }


            connection.commit();
        } catch (Exception ex) {
            Uteis.logar(ex, ProcessoIncluirReciboParcelasAbertoMachida.class);
            connection.rollback();
        } finally {
            connection.setAutoCommit(true);
        }
    }

    private Map<Integer, List<MovParcelaVO>> obterParcelasEmAberto(Integer contrato) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT mp.* FROM movparcela mp\n" +
                "INNER JOIN contrato c ON mp.contrato = c.codigo\n" +
                "WHERE c.importacao = TRUE\n" +
                "AND mp.situacao = 'EA'\n");
        if (contrato != null) {
            sql.append(" AND c.codigo = ").append(contrato);
        }
        sql.append("ORDER BY mp.datavencimento;");
        PreparedStatement ps = connection.prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        List<MovParcelaVO> movParcelas = MovParcela.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, connection);
        Map<Integer, List<MovParcelaVO>> parcelasPorContrato = new HashMap<Integer, List<MovParcelaVO>>();
        for (MovParcelaVO mp : movParcelas) {
            List<MovParcelaVO> parcelaContrato = parcelasPorContrato.get(mp.getContrato().getCodigo());
            if (parcelaContrato == null) {
                parcelaContrato = new ArrayList<MovParcelaVO>();
                parcelasPorContrato.put(mp.getContrato().getCodigo(), parcelaContrato);
            }
            parcelaContrato.add(mp);
        }
        return parcelasPorContrato;
    }

    public Connection getConnection() {
        return connection;
    }

    public void setConnection(Connection connection) {
        this.connection = connection;
    }
}

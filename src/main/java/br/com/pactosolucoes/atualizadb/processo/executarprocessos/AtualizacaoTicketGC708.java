package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "26/08/2024",
        descricao = "GC-708 - Customização Carteirinha - pacto print",
        motivacao = "GC-708 - Customização Carteirinha - pacto print")
public class AtualizacaoTicketGC708 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN utilizarPactoPrint boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN presidente varchar(80);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN superintendente varchar(80);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE carteirinhacliente ADD COLUMN numeroCarteirinha varchar(20);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE carteirinhacliente ADD COLUMN dataValidadeCarteirinha date;", c);
        }
    }
}

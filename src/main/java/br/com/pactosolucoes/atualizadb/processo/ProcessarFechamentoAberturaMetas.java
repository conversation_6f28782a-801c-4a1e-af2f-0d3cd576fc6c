/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.threads.ThreadRobo;
import controle.crm.AberturaMetaControle;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.RobotRunner;

/**
 *
 * <AUTHOR>
 */
public class ProcessarFechamentoAberturaMetas {
    
    public static void main(String[] args) {
        System.out.println("Entrou no Runner");
        if (args.length == 0) {
            args = new String[]{"pacto8"};
        }
        if (args.length >= 1) {
            String nomeThread = args[0];
            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica(nomeThread);
                Conexao.guardarConexaoForJ2SE(con);
                //atualizar banco de dados, se defasado
                if (args.length >= 2) {
                    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                    Date hoje = Calendario.getInstance(sdf.parse(args[1])).getTime();
                    Calendario.dia = hoje;
                }
                new AberturaMetaControle().fecharMetas(Uteis.somarDias(Calendario.hoje(), -1));
                new AberturaMetaControle().abrirMetas(Calendario.hoje());

            } catch (Exception ex) {
                Logger.getLogger(RobotRunner.class.getName()).log(Level.SEVERE,
                        "Erro ao obter conexao especifica com a chave " + nomeThread, ex);
            }

        }
    }
    
}

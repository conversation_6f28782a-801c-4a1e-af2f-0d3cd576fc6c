package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Hibrael Araújo Alves",
        data = "20/05/2025",
        descricao = "PAY-718 - Conciliação multiplicando o valor com o parcelamento",
        motivacao = "PAY-718 - Conciliação multiplicando o valor com o parcelamento")
public class AtualizacaoTicketPAY718 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try(Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)){
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE extratodiarioitem ADD COLUMN tipoParcelamento integer default 0;", c);
        }
    }
}

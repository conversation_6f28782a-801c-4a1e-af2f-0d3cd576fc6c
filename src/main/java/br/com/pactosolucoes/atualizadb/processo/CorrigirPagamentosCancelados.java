package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.CartaoCredito;
import negocio.facade.jdbc.financeiro.MovParcela;

/**
 * <AUTHOR>
 *
 */
public class CorrigirPagamentosCancelados {

	Connection con;
	
	public CorrigirPagamentosCancelados(Connection con) throws Exception {
		this.con = con;
	}

	public List<Map<String, Object>> dados;
	
	private void montarDadosProcesso() throws SQLException, Exception{
		dados = new ArrayList<Map<String, Object>>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT mp.codigo, mp.nomepagador FROM movpagamento mp \n");
		sql.append("INNER JOIN formapagamento fp ON mp.formapagamento = fp.codigo  \n");
		sql.append("INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = mp.codigo \n");
		sql.append("INNER JOIN movparcela mpr ON mpr.codigo = pmp.movparcela \n");
		sql.append("WHERE fp.tipoformapagamento LIKE 'CA' \n");
		sql.append("AND mpr.situacao LIKE 'CA' \n");
		sql.append("GROUP by mp.codigo, mp.nomepagador \n");
		
		Statement stm = con.createStatement();
        ResultSet resultSet = stm.executeQuery(sql.toString());
		
		while(resultSet.next()){
			StringBuilder sqlParcelas = new StringBuilder();
			sqlParcelas.append("SELECT mpr.* FROM movparcela mpr  \n");
			sqlParcelas.append("INNER JOIN pagamentomovparcela pmp ON pmp.movparcela = mpr.codigo  \n");
			sqlParcelas.append("WHERE pmp.movpagamento = "+resultSet.getInt("codigo"));
			
			stm = con.createStatement();
			ResultSet resultParcelas = stm.executeQuery(sqlParcelas.toString());
			List<MovParcelaVO> parcelas = MovParcela.montarDadosConsulta(resultParcelas, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
			
			stm = con.createStatement();
			String sqlCartoes = "SELECT * FROM cartaocredito WHERE movpagamento = "+resultSet.getInt("codigo");
			ResultSet resultCartoes = stm.executeQuery(sqlCartoes);
			List<CartaoCreditoVO> cartoes = CartaoCredito.montarDadosConsulta(resultCartoes, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
			
			Map<String, Object> mapa = new HashMap<String, Object>();
			mapa.put("pagamento", resultSet.getInt("codigo"));
			mapa.put("nomepessoa", resultSet.getString("nomepagador"));
			mapa.put("parcelas", parcelas);
			mapa.put("cartoes", cartoes);
			dados.add(mapa);
		}
	}
	
	public void processarDados() throws Exception{
		montarDadosProcesso();
		CartaoCredito ccDAO = new CartaoCredito();
		for(Map<String,Object> mapa : dados){
			List<MovParcelaVO> parcelas = (List<MovParcelaVO>) mapa.get("parcelas");
			List<CartaoCreditoVO> cartoes = (List<CartaoCreditoVO>) mapa.get("cartoes");
			
			Double valorNaoCancelado = 0.0;
			for(MovParcelaVO parcela : parcelas){
				if(!parcela.getSituacao().equals("CA")){
					valorNaoCancelado += parcela.getValorParcela();
				}
			}
			
			Ordenacao.ordenarLista(cartoes, "dataCompensacao");
			for(CartaoCreditoVO cartaoCredito : cartoes){
				if(cartaoCredito.getValor() > valorNaoCancelado){
					cartaoCredito.setSituacao("CA");
					System.out.println("CARTÃO DA PESSOA "+mapa.get("nomepessoa")+" CORRIGIDO.");
					ccDAO.alterar(cartaoCredito);
					valorNaoCancelado = 0.0;
				}else{
					valorNaoCancelado = valorNaoCancelado - cartaoCredito.getValor();
				}
			}
		}
		
	}

	
	
	
}

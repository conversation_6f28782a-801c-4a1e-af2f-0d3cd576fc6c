package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "25/03/2025",
        descricao = "criar tabela para guardar dados da integracao",
        motivacao = "GC-1787")
public class AtualizacaoTicketGC1787 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE configuracaointegracaoacessopratique (\n" +
                    "\tcodigo serial,\n" +
                    "\thabilitada boolean,\n" +
                    "\tempresa int4 NOT NULL,\n" +
                    "\tCONSTRAINT configuracaointegracaoacessopratique_pkey PRIMARY KEY (codigo),\n" +
                    "\tCONSTRAINT configuracaointegracaoacessopratique_empresa_fkey FOREIGN KEY (empresa) REFERENCES public.empresa(codigo)\n" +
                    ");", c);
        }
    }
}

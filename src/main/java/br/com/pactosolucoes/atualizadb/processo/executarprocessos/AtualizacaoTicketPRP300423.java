package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Victor Augusto Machado",
        data = "07/03/2025",
        descricao = "IA-300 Adicionar suporte para envio de contextos de todas as empresas da rede nos endpoints da API de integração com a IA Pacto Conversas",
        motivacao = "IA-300")
public class AtualizacaoTicketPRP300423 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        String key1 = Conexao.getPseudoChave_apresentar();
        String key2 = (String) JSFUtilities.getFromSession("key");
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdate("UPDATE configuracaocrmia c\n" +
                    "SET matriz = true,\n" +
                    "    codigoempresa = (\n" +
                    "        SELECT e.codigo\n" +
                    "        FROM empresa e\n" +
                    "        ORDER BY e.codigo ASC\n" +
                    "        LIMIT 1\n" +
                    "    )\n" +
                    "WHERE c.habilitarconfigia = true;", c);

            if (!key1.isEmpty() || key2 != null) {
                String key = !key1.isEmpty() ? key1 : key2;
                SuperFacadeJDBC.executarUpdate("INSERT INTO configuracaoredeia (chavebancomatriz, tipoconfigrede, codigounidadematriz)\n" +
                        "SELECT '" + key + "' AS chavebancomatriz,\n" +
                        "    'individual' AS tipoconfigrede,\n" +
                        "    (SELECT e.codigo\n" +
                        "      FROM empresa e\n" +
                        "      ORDER BY e.codigo ASC\n" +
                        "      LIMIT 1) AS codigounidadematriz\n" +
                        "FROM configuracaocrmia c\n" +
                        "WHERE c.habilitarconfigia = true\n" +
                        "  AND NOT EXISTS (SELECT 1 FROM configuracaoredeia);", c);
            }

        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ReciboClienteConsultor;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.controle.sad.SituacaoClienteSinteticoDWControle;
import servicos.impl.dcc.base.ProcessarBoletosPJBankEngenharia;
import test.simulacao.AjustarMovProdutosContratosCanceladosImportacao;

import java.sql.Connection;
import java.sql.DriverManager;

public class ProcessosPosImportacao {
    public static void main(String[] args) throws Exception {

        String chave = ""; //informar a chave de produção nas configurações do cfgdb deve estar apontando para o oamd de produção
        Integer codigoEmpresa = 0; // deixar zero pra rodar em todas
        String urlConnection = "******************************************";

        Connection con;
        if (args.length > 0) {
            chave = args[0];
            codigoEmpresa = Integer.parseInt(args[1]);
            con = new DAO().obterConexaoEspecifica(chave);
        } else {
            con = DriverManager.getConnection(urlConnection,"postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);
        }
        AjustarVezesPorSemanaContratosImportados(con);
        AjustarLinhaDoTempoDeContratos.ajustarContratos(con, codigoEmpresa);
        corrigirHistoricoContratos(con, codigoEmpresa);
        alterarClienteSituacao(con);
        AjustarDatasRenovacaoImportacao.ajustarDatasRenovacao(con);
        ProdutosPagosServico.atualizarTodosProdutosPagos(con);
        CriarPendenciaBVClientesImportacao.receberConexao(con);
        GerarMovProdutoModalidade.gravarDados(con);
        GerarMovProdutoModalidade.gerarMovProdutoModalidadeProdutoCancelado(con);
        migradorMovProdutoModalidade(con);
        GerarMovPagamentosMovimento.receberConexao(con);
        ajustarRecibosClientes(con);
        alterarClienteSituacao(con);
        SituacaoClienteSinteticoDWControle.executar();
        usuariosEClientesTreinoOAMD(chave, con);
        ProcessarBoletosPJBankEngenharia.buscarInformacoesBoletosPJBank(con);
        AjustarMovProdutosContratosCanceladosImportacao.ajustarMovProdutosContratosCanceladosImportacao(codigoEmpresa, "", con);
    }

    private static void ajustarRecibosClientes(Connection con) throws Exception {
        Uteis.logarDebug("Executando updates recibos clientes...");
        ReciboClienteConsultor reciboClienteConsultorDAO = new ReciboClienteConsultor(con);
        reciboClienteConsultorDAO.excluirDados();
        reciboClienteConsultorDAO.selectMigradorPovoarReciboClienteConsultor();
        reciboClienteConsultorDAO = null;
        MovProduto movProdutoDAO = new MovProduto(con);
        movProdutoDAO.alterarValorFaturado();
        Uteis.logarDebug("Updates recibos clientes executado!");
    }

    private static void migradorMovProdutoModalidade(Connection con) throws Exception {
        Uteis.logarDebug("Executando migrador MovProdutoModalidade...");
        MovProdutoModalidade movProdutoModalidadeDAO = new MovProdutoModalidade(con);
        movProdutoModalidadeDAO.selectMigradorMOVProdutoModalidade();
        movProdutoModalidadeDAO = null;
        Uteis.logarDebug("Migrador MovProdutoModalidadeExecutado!");
    }

    private static void ajustarLinhaDoTempoContrato(Connection con) throws Exception {
        AjustarLinhaDoTempoDeContratos.ajustarContratos(con, 0);
    }

    private static void corrigirHistoricoContratos(Connection con, Integer codigoEmpresa) throws Exception {
        Uteis.logarDebug("Iniciando processo: corrigir historico contratos...");
        Contrato contratoDao = new Contrato(con);
        contratoDao.gerarSituacoesTemporaisContratos(0, codigoEmpresa);
        contratoDao = null;
        CorrigirHistoricoContratos.corrigirEncadeamentoContratosOrfaos(con);
        CorrigirHistoricoContratos.corrigirContratosSemHistorico(con);
    }

    private static void alterarClienteSituacao(Connection con) throws Exception {
        Uteis.logarDebug("Executando update alterar cliente situação...");
        Cliente clienteDAO = new Cliente(con);
        clienteDAO.alterarClienteSituacao();
        clienteDAO = null;
        Uteis.logarDebug("Update cliente situação executado! ");
    }

    private static void usuariosEClientesTreinoOAMD(String chave, Connection con) throws Exception {
        Uteis.logarDebug("Executando usuariosEClientesTreinoOAMD...");
        SincronizarUsuariosEClientesTreinoOAMD.executar(chave, con);
        Uteis.logarDebug("usuariosEClientesTreinoOAMD! ");
    }

    private static void AjustarVezesPorSemanaContratosImportados(Connection con) {
        String updateContratoModalidade = "update contratomodalidadevezessemana cmv set nrvezes  = (select vezessemana from contratomodalidade where codigo = cmv.contratomodalidade ) where cmv.contratomodalidade  in (select codigo from contratomodalidade where contrato in (select codigo from contrato where importacao))";

        try {
            SuperFacadeJDBC.executarConsulta(updateContratoModalidade, con);
        } catch (Exception e) {
            Uteis.logarDebug("Erro ao ajustar vezes por semana dos contratos");
        }
    }

}

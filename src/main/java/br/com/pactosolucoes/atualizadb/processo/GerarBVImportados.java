package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;

import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.utilitarias.Uteis;

public class GerarBVImportados {

	
	public static void gerarBvsImportados(Connection c) throws Exception{
		ResultSet resultSet = c.prepareStatement("select cliente.codigo, min(contrato.datalancamento) as vigenciade from cliente inner join contrato on cliente.pessoa = contrato.pessoa " +
												 " where contrato.situacao like 'AT' AND (contrato.situacaocontrato like 'MA' OR contrato.situacaocontrato like 'RE') and cliente.codigo not in " +
												 "(select cliente from questionariocliente) group by cliente.codigo").executeQuery();
		while(resultSet.next()){
				ResultSet resultVinc = c.prepareStatement("select colaborador from vinculo where tipovinculo like 'CO' and cliente = "+resultSet.getInt("codigo")).executeQuery();
				if(resultVinc.next()){
					c.prepareStatement("INSERT INTO questionariocliente(data, consultor, cliente, questionario, tipobv)" +
							" values ('"+Uteis.getDataJDBC(resultSet.getDate("vigenciade"))+"', "+resultVinc.getInt("colaborador")+
									", "+resultSet.getInt("codigo")+", 1, "+TipoBVEnum.MA.getCodigo()+")").execute();
				}
		}
		
		
	}
	
	public static void main(String[] args) {
		try {
			Connection con1 = DriverManager.getConnection("**************************************************************", "zillyonweb", "pactodb");
			gerarBvsImportados(con1);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}

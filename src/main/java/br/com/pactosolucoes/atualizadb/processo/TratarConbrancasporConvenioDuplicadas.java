package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.AdministrativoRunner;
import servicos.adm.CreditoDCCService;
import servicos.impl.dcc.base.ExtratoDiarioService;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.dcc.base.VerificadorCobrancasService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;
import java.util.logging.Level;
import java.util.logging.Logger;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

public class TratarConbrancasporConvenioDuplicadas {

    public static void main(String... args) {
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"teste"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            Uteis.debug = true;
            Connection con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, con);
            corrigirTransacoesDuplicadas(con,chave);
        } catch (Exception ex) {
            Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static String corrigirTransacoesDuplicadas(Connection con, String chave) throws Exception {
        StringBuilder retorno = new StringBuilder();
        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
        usuarioDAO = null;
        Transacao transacaoDao = new Transacao(con);

        StringBuilder sqlTransacoes = new StringBuilder("select m.codigo as parcela , count(t.codigo)as nrtransacoes, min(dataprocessamento) as primeiratentativa,");
        sqlTransacoes.append(" max(dataprocessamento) as ultimatentativa from transacao t inner join transacaomovparcela tm on tm.transacao = t.codigo ");
        sqlTransacoes.append(" inner  join movparcela m on m.codigo = tm.movparcela  where t.situacao in (2,4) group by 1 having count(t.codigo) > 1");
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(sqlTransacoes.toString(),con);
        while (consulta.next()) {
            List<TransacaoVO> transacoes = transacaoDao.consultarPorParcela(consulta.getInt("parcela"));
            TransacaoVO transacaoAprovada = null;
            TransacaoVO transacaoAguardando = null;
            TransacaoVO transacaoCerta = null;
            for(TransacaoVO transacaoVo : transacoes){
                if(transacaoVo.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) &&
                        (UteisValidacao.notEmptyNumber(transacaoVo.getReciboPagamento()) ||
                    transacaoAprovada == null  )){
                        transacaoAprovada = transacaoVo;
                }if(transacaoVo.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)){
                    transacaoAguardando = transacaoVo;
                }
            }
            transacaoCerta = transacaoAprovada == null ? transacaoAguardando : transacaoAprovada;
            for(TransacaoVO transacaoVo : transacoes){
                try {
                    if ((transacaoCerta != null && transacaoVo.getCodigo().equals(transacaoCerta.getCodigo()))
                            || (!transacaoVo.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)
                            && !transacaoVo.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)
                            && !transacaoVo.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA))) {
                        continue;
                    }
                    if (transacaoVo.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) &&
                            UteisValidacao.notEmptyNumber(transacaoVo.getReciboPagamento())) {
                        SuperFacadeJDBC.executarUpdate("delete from pagamentomovparcela where recibopagamento = " + transacaoVo.getReciboPagamento(), con);
                        SuperFacadeJDBC.executarUpdate("update transacao set recibopagamento = null where codigo = " + transacaoVo.getCodigo(), con);
                        SuperFacadeJDBC.executarUpdate("delete from recibopagamento  where codigo = " + transacaoVo.getReciboPagamento(), con);
                        transacaoVo.setReciboPagamento(null);
                    }
                    transacaoDao.cancelarTransacao(transacaoVo, false, usuarioVO, chave);
                }catch (Exception e){
                    retorno.append("transacao ").append(transacaoVo.getCodigo()).append(" - ").append(e.getMessage()).append("<br/>");
                    Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, e);
                }
            }

        }
        return retorno.toString();
    }
}

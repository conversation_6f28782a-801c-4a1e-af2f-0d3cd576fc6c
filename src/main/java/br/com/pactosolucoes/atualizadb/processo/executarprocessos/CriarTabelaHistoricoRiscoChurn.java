package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Joao Alcides",
        data = "07/08/2024",
        descricao = "Cria tabela HistoricoRiscoChurn",
        motivacao = "Novo BI de Churn")
public class CriarTabelaHistoricoRiscoChurn implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE situacaoclientesinteticodw ADD COLUMN sugestaogpt text;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE situacaoclientesinteticodw ADD COLUMN riscochurn float;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table situacaoclientesinteticodw add column riscochurnlancamento timestamp without time zone;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE HistoricoRiscoChurn (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    cliente int constraint HistoricoRiscoChurn_cliente_fk references public.cliente ON DELETE CASCADE,\n" +
                    "    riscochurn float,\n" +
                    "    datarisco TIMESTAMP NOT NULL\n" +
                    ");", c);
        }
    }
}

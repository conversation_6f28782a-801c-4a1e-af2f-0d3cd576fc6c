package br.com.pactosolucoes.atualizadb.processo;

import static java.util.Objects.nonNull;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Calendar;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.utilitarias.Conexao;

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 *
 * <AUTHOR>
 */
public class CorrigirDataCompensacaoMovPagamento {
        public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("**************************************************************************", "zillyonweb", "pactodb");
//            corrigirDatasCartaoCreditoAlterandoDataOriginal(con1);
            corrigirDatasCartaoCreditoAlterandoDataCompensacao(con1, new Date("2021/12/01"), new Date("2021/12/31"), 1);

        } catch (Exception ex) {
            Logger.getLogger(CorrigirDataCompensacaoMovPagamento.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    /*
    * Método para corrigir na tabela cartaocredito onde somará os dias
    * compensação cartão crédito que estão na tabela e setar na data original.
    * Para isso acontecer devemos informar o codigo da forma de pagamento.
    * */
    public static void corrigirDatasCartaoCreditoAlterandoDataOriginal(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select m.codigo as movpagamento, m.datalancamento, f.diascompensacaocartaocredito\n" +
                "from movpagamento m inner join formapagamento f on m.formapagamento = f.codigo \n" +
                "where f.tipoformapagamento = 'CA' and f.diascompensacaocartaocredito = 30 and m.formapagamento = 14 \n" +
                "group by m.codigo , m.datalancamento, f.diascompensacaocartaocredito", con);
        int cont = 0;

        while (consulta.next()) {
            Date datalancamento = consulta.getDate("datalancamento");
            ResultSet consultaParcelasCartao = SuperFacadeJDBC.criarConsulta("select * from cartaocredito where movpagamento = "+consulta.getInt("movpagamento")+" order by codigo",con);

            Date datacompensacao = Uteis.somarDias(datalancamento, consulta.getInt("diascompensacaocartaocredito"));
            while (consultaParcelasCartao.next()) {
                if(consultaParcelasCartao.getDate("dataoriginal") != null ){
                    SuperFacadeJDBC.executarConsultaUpdate("update cartaocredito set dataoriginal = '" + Uteis.getDataJDBC(datacompensacao) + "' where codigo ="+ consultaParcelasCartao.getInt("codigo"), con);
                }
                int diasparcelas = 30;
                datacompensacao = Uteis.somarDias(datacompensacao, diasparcelas);
            }

            System.out.println(++cont + " - movpagamento " + consulta.getInt("movpagamento") + " foi ajustado");
        }
    }

    public static void corrigirDatasCartaoCreditoAlterandoDataCompensacao(Connection con, Date inicio, Date fim, int formaPagamento) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(
                "select m.codigo as movpagamento, m.datalancamento, f.diascompensacaocartaocredito\n"
                        + "from movpagamento m inner join formapagamento f on m.formapagamento = f.codigo \n"
                        + "where f.tipoformapagamento = 'CA' \n"
                        + "and f.diascompensacaocartaocredito = 30 and m.formapagamento = " + formaPagamento + " \n"
                        + ((nonNull(inicio) && nonNull(fim)) ? "and m.datalancamento between '" + Uteis.getDataJDBC(inicio) + " 00:00:00' and '" + Uteis.getDataJDBC(fim) + " 23:59:59' \n" : "")
                        + "group by m.codigo , m.datalancamento, f.diascompensacaocartaocredito", con);
        int cont = 0;

        while (consulta.next()) {
            Date datalancamento = consulta.getDate("datalancamento");
            ResultSet consultaParcelasCartao = SuperFacadeJDBC.criarConsulta(
                    "select * from cartaocredito where movpagamento = " + consulta.getInt("movpagamento")
                            + " order by codigo", con);

            Date datacompensacao = Uteis.somarDias(datalancamento, consulta.getInt("diascompensacaocartaocredito"));
            while (consultaParcelasCartao.next()) {
                SuperFacadeJDBC.executarConsultaUpdate(
                        "update cartaocredito set datacompesancao = '" + Uteis.getDataJDBC(datacompensacao)
                                + "' where codigo =" + consultaParcelasCartao.getInt("codigo"), con);
                int diasparcelas = consulta.getInt("diascompensacaocartaocredito");
                datacompensacao = Uteis.somarDias(datacompensacao, diasparcelas);
            }

            System.out.println(++cont + " - movpagamento " + consulta.getInt("movpagamento") + " foi ajustado");
        }
    }

    public static void corrigirDatasCartaoCredito(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select m.codigo as movpagamento, m.datalancamento, f.diascompensacaocartaocredito\n" +
"from movpagamento m inner join formapagamento f on m.formapagamento = f.codigo \n" +
"left join remessaitem ri on ri.movpagamento = m.codigo left join remessa  re on re.codigo = ri.remessa left join conveniocobranca cc on cc.codigo = re.conveniocobranca  \n" +
"where f.tipoformapagamento = 'CA' and not compensacaodiasuteis and (m.datalancamento > '2015-11-05 23:59:59' or dataalteracaomanual > '2015-11-05 23:59:59') and (cc.codigo is null or cc.tipoconvenio = 8) \n" +
"group by m.codigo , m.datalancamento, f.diascompensacaocartaocredito", con);
        int cont = 0;

        while (consulta.next()) {
            Date datacompensacao = consulta.getDate("datalancamento");
             ResultSet consultaParcelasCartao = SuperFacadeJDBC.criarConsulta("select * from cartaocredito where movpagamento = "+consulta.getInt("movpagamento")+" and datacompesancao::date = '" + Uteis.getDataJDBC(datacompensacao) + "' order by codigo",con);
            while (consultaParcelasCartao.next()) {
                datacompensacao = Uteis.somarDias(datacompensacao, consulta.getInt("diascompensacaocartaocredito"));
                if(consultaParcelasCartao.getDate("dataoriginal") == null ){
                    SuperFacadeJDBC.executarConsultaUpdate("update cartaocredito set datacompesancao = '" + Uteis.getDataJDBC(datacompensacao) + "' where codigo ="+ consultaParcelasCartao.getInt("codigo"), con);
                } else { 
                    SuperFacadeJDBC.executarConsultaUpdate("update cartaocredito set dataoriginal = '" + Uteis.getDataJDBC(datacompensacao) + "' where codigo ="+ consultaParcelasCartao.getInt("codigo"), con);
                }
            }
                
            System.out.println(++cont + " - movpagamento " + consulta.getInt("movpagamento") + " foi ajustado");
        }
    }
    
    public static void preencherDataPagamentoOriginal(Connection con) throws Exception {
        SuperFacadeJDBC.executarConsultaUpdate("update movpagamento  set datapagamentooriginal  = datalancamento;", con);
        ResultSet resultDados = SuperFacadeJDBC.criarConsulta("select codigo,nrdiascompensacao  from empresa", con);
        MovPagamento movDAO = new MovPagamento(con);
        while (resultDados.next()) {
            EmpresaVO empresa = new EmpresaVO();
            empresa.setCodigo(resultDados.getInt("codigo"));
            movDAO.preencherDataPagamentoOriginalCartaoDebito(resultDados.getInt("nrdiascompensacao"), empresa);
        }
        movDAO = null;
    }
    
    
    public static void corrigirDatasCartaoCreditoFormaFitness(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select autorizacaocartao,datalancamento, (select max(datacompesancao) from cartaocredito c inner join movpagamento m on m.codigo = c.movpagamento where m.autorizacaocartao = foo.autorizacaocartao and datacompesancao < '2018-05-15') as compensacaoanterior from (\n" +
"        select movpagamento,m.valor, date_part('year',datacompesancao), date_part('month',datacompesancao),m.datalancamento,m.autorizacaocartao, count(c.codigo), max(datacompesancao) as datacompesancao from cartaocredito c left join movpagamento m on c.movpagamento = m.codigo where situacao = 'EA' group by 1,2,3,4,5,6 having count(c.codigo)  > 1 )\n" +
"        as foo where datacompesancao = '2018-05-15' group by 1,2 order by 2", con);
        int cont = 0;

        while (consulta.next()) {
            Date datacompensacao = consulta.getDate("compensacaoanterior") == null ? consulta.getDate("datalancamento") : consulta.getDate("compensacaoanterior");
             ResultSet consultaParcelasCartao = SuperFacadeJDBC.criarConsulta("select c.* from cartaocredito c inner join movpagamento m on m.codigo = c.movpagamento where m.autorizacaocartao = '"+consulta.getString("autorizacaocartao")+"' and datacompesancao::date > '2018-05-14' order by nrparcela",con);
            int parcelaAtual = 0;
            while (consultaParcelasCartao.next()) {
                if(parcelaAtual == 0 || parcelaAtual != consultaParcelasCartao.getInt("nrparcela")){
                    datacompensacao = Uteis.somarCampoData(datacompensacao, Calendar.MONTH, 1);
                }
                parcelaAtual =  consultaParcelasCartao.getInt("nrparcela");
                if(consultaParcelasCartao.getDate("dataoriginal") == null ){
                    SuperFacadeJDBC.executarConsultaUpdate("update cartaocredito set datacompesancao = '" + Uteis.getDataJDBC(datacompensacao) + "' where codigo ="+ consultaParcelasCartao.getInt("codigo"), con);
                } else { 
                    SuperFacadeJDBC.executarConsultaUpdate("update cartaocredito set dataoriginal = '" + Uteis.getDataJDBC(datacompensacao) + "' where codigo ="+ consultaParcelasCartao.getInt("codigo"), con);
                }
            }
                
            System.out.println(++cont + " - movpagamento " + consulta.getString("autorizacaocartao") + " foi ajustado");
        }
    }
    
}

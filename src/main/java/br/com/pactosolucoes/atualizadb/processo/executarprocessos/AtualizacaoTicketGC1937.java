package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Franco Alvarez",
        data = "18/04/2025",
        descricao = "Criação da tabela de Aditivo + Alterações na tabela de Contrato Assinatura Digital",
        motivacao = "GC-1937")
public class AtualizacaoTicketGC1937 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE aditivo ( \n" +
                    "codigo serial primary key,\n" +
                    "nome varchar(200),\n" +
                    "descricao text,\n" +
                    "dataprocessamento timestamp,\n" +
                    "datacriacao timestamp,\n" +
                    "plano integer,\n" +
                    "responsavel integer,\n" +
                    "CONSTRAINT aditivo_planotextopadrao_fkey FOREIGN KEY (plano) REFERENCES planotextopadrao (codigo),\n" +
                    "CONSTRAINT aditivo_usuario_fkey FOREIGN KEY (responsavel) REFERENCES usuario (codigo));", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratoassinaturadigital ADD COLUMN ehAditivo boolean DEFAULT false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratoassinaturadigital ADD COLUMN aditivo integer,\n" +
                    "ADD CONSTRAINT fk_contratoassinaturadigital_aditivo FOREIGN KEY (aditivo) REFERENCES aditivo(codigo);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratoassinaturadigital ADD COLUMN contratoaditivo integer,\n" +
                    "ADD CONSTRAINT fk_contratoassinaturadigital_contratoaux_aditivo FOREIGN KEY (contratoaditivo) REFERENCES contrato(codigo);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratotextopadrao ADD COLUMN aditivo integer,\n" +
                    "ADD CONSTRAINT fk_contratotextopadrao_aditivo FOREIGN KEY (aditivo) REFERENCES aditivo(codigo);", c);
        }
    }
}

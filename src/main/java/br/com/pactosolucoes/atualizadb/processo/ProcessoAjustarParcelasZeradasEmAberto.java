package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAjustarParcelasZeradasEmAberto {

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "universofitnessrj";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            ajustarParcelasZeradasEmAberto(c);
            ajustarParcelasZeradasEmAbertoProdutoMARERN(c);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAjustarParcelasZeradasEmAberto.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarParcelasZeradasEmAbertoProdutoMARERN(Connection con) throws Exception {
        try {
            Uteis.logarDebug("INÍCIO | ProcessoAjustarParcelasZeradasEmAberto");

            //Primeiro coloca as parcelas pagas como em Aberto
            StringBuilder sqlGetParcelasPagasSemPagamentoMovParcela = new StringBuilder();
            sqlGetParcelasPagasSemPagamentoMovParcela.append("SELECT\n");
            sqlGetParcelasPagasSemPagamentoMovParcela.append("'UPDATE movparcela SET situacao = ''EA'' WHERE codigo = ' || mpv.codigo || '; UPDATE movproduto SET situacao = ''EA'' WHERE codigo = ' || mpr.codigo || ';' as sqlUpdate\n");
            sqlGetParcelasPagasSemPagamentoMovParcela.append("FROM movparcela mpv\n");
            sqlGetParcelasPagasSemPagamentoMovParcela.append("LEFT JOIN pagamentomovparcela pmp ON pmp.movparcela = mpv.codigo\n");
            sqlGetParcelasPagasSemPagamentoMovParcela.append("INNER JOIN movprodutoparcela mpp ON mpp.movparcela = mpv.codigo\n");
            sqlGetParcelasPagasSemPagamentoMovParcela.append("INNER JOIN movproduto mpr ON mpr.codigo = mpp.movproduto\n");
            sqlGetParcelasPagasSemPagamentoMovParcela.append("INNER JOIN produto prd ON prd.codigo = mpr.produto\n");
            sqlGetParcelasPagasSemPagamentoMovParcela.append("WHERE pmp.codigo IS NULL\n");
            sqlGetParcelasPagasSemPagamentoMovParcela.append("AND mpv.situacao ILIKE 'PG'\n");
            sqlGetParcelasPagasSemPagamentoMovParcela.append("AND valorparcela = 0\n");
            sqlGetParcelasPagasSemPagamentoMovParcela.append("AND prd.tipoproduto IN ('MA', 'RE', 'RN')");
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlGetParcelasPagasSemPagamentoMovParcela.toString())) {
                    while (rs.next()) {
                        String sqlUpdateMovparcelaMovproduto = rs.getString("sqlUpdate");
                        try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdateMovparcelaMovproduto)) {
                            sqlAlterar.execute();
                        }
                    }
                }
            }

            // Agora consulta demais parcelas que estão em aberto que estão com valor zerado e faz o pagamento das mesmas
            StringBuilder sqlSelectParcelasAbertoProdutosMARERN = new StringBuilder();
            sqlSelectParcelasAbertoProdutosMARERN.append("SELECT\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("\tmpv.codigo AS codParcela,\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("\tmpv.responsavel AS responsavelParcela,\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("\tmpv.dataregistro AS dataLancamentoParcela,\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("\tmpv.empresa AS codEmpresa,\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("\tmpv.pessoa AS codPessoa,\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("\tpes.nome AS nomePessoa\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("FROM movparcela mpv\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("INNER JOIN movprodutoparcela mpp ON mpp.movparcela = mpv.codigo\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("INNER JOIN movproduto mpd ON mpd.codigo = mpp.movproduto\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("INNER JOIN pessoa pes ON pes.codigo = mpv.pessoa\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("INNER JOIN produto prd ON prd.codigo = mpd.produto\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("WHERE 1=1\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("AND mpv.valorparcela = 0\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("AND mpv.situacao = 'EA'\n");
            sqlSelectParcelasAbertoProdutosMARERN.append("AND prd.tipoproduto IN ('MA', 'RE', 'RN')");
            try (java.sql.Statement stm = con.createStatement()) {
                try (java.sql.ResultSet rs = stm.executeQuery(sqlSelectParcelasAbertoProdutosMARERN.toString())) {
                    while (rs.next()) {
                        int codParcela = rs.getInt("codParcela");
                        int responsavelParcela = rs.getInt("responsavelParcela");
                        Date dataLancamentoParcela = rs.getDate("dataLancamentoParcela");
                        int codEmpresa = rs.getInt("codEmpresa");
                        int codPessoa = rs.getInt("codPessoa");
                        String nomePessoa = rs.getString("nomePessoa");
                        if (UteisValidacao.emptyNumber(codParcela)) {
                            continue;
                        }
                        StringBuilder sqlInsertReciboPagamento = new StringBuilder();
                        sqlInsertReciboPagamento.append("INSERT INTO recibopagamento (valortotal,pessoapagador,nomepessoapagador,responsavellancamento,contrato,data,empresa,integrado) ");
                        sqlInsertReciboPagamento.append("VALUES (0.0");
                        sqlInsertReciboPagamento.append(",").append(codPessoa);
                        sqlInsertReciboPagamento.append(",'").append(nomePessoa).append("'");
                        sqlInsertReciboPagamento.append(",").append(responsavelParcela);
                        sqlInsertReciboPagamento.append(",NULL");
                        sqlInsertReciboPagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoParcela)).append("'");
                        sqlInsertReciboPagamento.append(",").append(codEmpresa);
                        sqlInsertReciboPagamento.append(",false)");
                        try (PreparedStatement sqlInserirReciboPagamento = con.prepareStatement(sqlInsertReciboPagamento.toString())) {
                            sqlInserirReciboPagamento.execute();
                        }
                        try (java.sql.ResultSet rsReciboGerado = stm.executeQuery("SELECT MAX(codigo) AS codigo FROM recibopagamento")) {
                            int codigoReciboGerado = rsReciboGerado.next() ? rsReciboGerado.getInt("codigo") : 0;
                            if (!UteisValidacao.emptyNumber(codigoReciboGerado)) {
                                try (java.sql.ResultSet rsFormaPagamentoAVista = stm.executeQuery("SELECT codigo as codigo FROM FormaPagamento WHERE upper(tipoFormaPagamento) LIKE ('AV') AND ativo AND NOT somentefinanceiro ORDER BY codigo LIMIT 1")) {
                                    int codigoFormaPagamento = rsFormaPagamentoAVista.next() ? rsFormaPagamentoAVista.getInt("codigo") : 0;

                                    StringBuilder sqlInsertMovpagamento = new StringBuilder();
                                    sqlInsertMovpagamento.append("INSERT INTO movpagamento (responsavelpagamento");
                                    sqlInsertMovpagamento.append(",nrparcelacartaocredito");
                                    sqlInsertMovpagamento.append(",movpagamentoescolhida");
                                    sqlInsertMovpagamento.append(",nomepagador");
                                    sqlInsertMovpagamento.append(",formapagamento");
                                    sqlInsertMovpagamento.append(",valor");
                                    sqlInsertMovpagamento.append(",datalancamento");
                                    sqlInsertMovpagamento.append(",datapagamento");
                                    sqlInsertMovpagamento.append(",pessoa");
                                    sqlInsertMovpagamento.append(",dataquitacao");
                                    sqlInsertMovpagamento.append(",recibopagamento");
                                    sqlInsertMovpagamento.append(",empresa");
                                    sqlInsertMovpagamento.append(",observacao");
                                    sqlInsertMovpagamento.append(",autorizacaocartao");
                                    sqlInsertMovpagamento.append(",credito");
                                    sqlInsertMovpagamento.append(",valortotal");
                                    sqlInsertMovpagamento.append(",nsu");
                                    sqlInsertMovpagamento.append(",depositocc");
                                    sqlInsertMovpagamento.append(",datapagamentooriginal");
                                    sqlInsertMovpagamento.append(",usarparceirofidelidade");
                                    sqlInsertMovpagamento.append(",tabelaparceirofidelidade");
                                    sqlInsertMovpagamento.append(",multiplicadorparceirofidelidade");
                                    sqlInsertMovpagamento.append(",tipopontoparceirofidelidade");
                                    sqlInsertMovpagamento.append(",pontosparceirofidelidade");
                                    sqlInsertMovpagamento.append(",cpfparceirofidelidade");
                                    sqlInsertMovpagamento.append(",parceirofidelidadeprocessado");
                                    sqlInsertMovpagamento.append(",codigoexternoprodutoparceirofidelidade");
                                    sqlInsertMovpagamento.append(",senhaparceirofidelidade");
                                    sqlInsertMovpagamento.append(",enviadoconciliadora");
                                    sqlInsertMovpagamento.append(",respostarequisicaopinpad");
                                    sqlInsertMovpagamento.append(",numerounicotransacao)");
                                    sqlInsertMovpagamento.append("VALUES (").append(responsavelParcela);
                                    sqlInsertMovpagamento.append(",0");
                                    sqlInsertMovpagamento.append(",true");
                                    sqlInsertMovpagamento.append(",'").append(nomePessoa).append("'");
                                    sqlInsertMovpagamento.append(",").append(codigoFormaPagamento);
                                    sqlInsertMovpagamento.append(",0.0");
                                    sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoParcela)).append("'");
                                    sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoParcela)).append("'");
                                    sqlInsertMovpagamento.append(",").append(codPessoa);
                                    sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoParcela)).append("'");
                                    ;
                                    sqlInsertMovpagamento.append(",").append(codigoReciboGerado);
                                    sqlInsertMovpagamento.append(",").append(codEmpresa);
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",false");
                                    sqlInsertMovpagamento.append(",0.0");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",false");
                                    sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoParcela)).append("'");
                                    ;
                                    sqlInsertMovpagamento.append(",false");
                                    sqlInsertMovpagamento.append(",0");
                                    sqlInsertMovpagamento.append(",0.0");
                                    sqlInsertMovpagamento.append(",0");
                                    sqlInsertMovpagamento.append(",0");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",false");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",false");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",'')");
                                    try (PreparedStatement sqlInserirMovpagamento = con.prepareStatement(sqlInsertMovpagamento.toString())) {
                                        sqlInserirMovpagamento.execute();
                                    }
                                    try (java.sql.ResultSet rsMovpagamentoGerado = stm.executeQuery("SELECT MAX(codigo) AS codigo FROM movpagamento")) {
                                        int codigoMovpagamentoGerado = rsMovpagamentoGerado.next() ? rsMovpagamentoGerado.getInt("codigo") : 0;
                                        if (!UteisValidacao.emptyNumber(codigoMovpagamentoGerado)) {
                                            StringBuilder sqlInsertPagamentoMovParcela = new StringBuilder();
                                            sqlInsertPagamentoMovParcela.append("INSERT INTO pagamentomovparcela (valorpago");
                                            sqlInsertPagamentoMovParcela.append(",recibopagamento");
                                            sqlInsertPagamentoMovParcela.append(",movparcela");
                                            sqlInsertPagamentoMovParcela.append(",movpagamento)");
                                            sqlInsertPagamentoMovParcela.append("VALUES (0.0");
                                            sqlInsertPagamentoMovParcela.append(",").append(codigoReciboGerado);
                                            sqlInsertPagamentoMovParcela.append(",").append(codParcela);
                                            sqlInsertPagamentoMovParcela.append(",").append(codigoMovpagamentoGerado).append(")");
                                            try (PreparedStatement sqlInserirPagamentoMovparcela = con.prepareStatement(sqlInsertPagamentoMovParcela.toString())) {
                                                sqlInserirPagamentoMovparcela.execute();
                                            }

                                            try (java.sql.ResultSet rsMovprodutoContrato = stm.executeQuery("SELECT codigo as codigo, movproduto as codMovproduto FROM movprodutoparcela WHERE movparcela = " + codParcela)) {
                                                while(rsMovprodutoContrato.next()){
                                                    int codigoMovprodutoParcela = rsMovprodutoContrato.getInt("codigo");
                                                    int codigoMovproduto = rsMovprodutoContrato.getInt("codMovproduto");
                                                    StringBuilder sqlUpdateMovProdutoParcela = new StringBuilder();
                                                    sqlUpdateMovProdutoParcela.append("UPDATE movprodutoparcela SET recibopagamento = ").append(codigoReciboGerado);
                                                    sqlUpdateMovProdutoParcela.append("WHERE codigo = ").append(codigoMovprodutoParcela);
                                                    try (PreparedStatement sqlInserirMovprodutoParcela = con.prepareStatement(sqlUpdateMovProdutoParcela.toString())) {
                                                        sqlInserirMovprodutoParcela.execute();
                                                    }

                                                    StringBuilder sqlUpdateMovparcela = new StringBuilder();
                                                    sqlUpdateMovparcela.append("UPDATE movparcela SET situacao = 'PG' WHERE codigo = ").append(codParcela);
                                                    try (PreparedStatement psSqlUpdateMovparcela = con.prepareStatement(sqlUpdateMovparcela.toString())) {
                                                        psSqlUpdateMovparcela.execute();
                                                    }

                                                    StringBuilder sqlUpdateMovproduto = new StringBuilder();
                                                    sqlUpdateMovproduto.append("UPDATE movproduto SET situacao = 'PG' WHERE codigo = ").append(codigoMovproduto);
                                                    try (PreparedStatement psSqlUpdateMovproduto = con.prepareStatement(sqlUpdateMovproduto.toString())) {
                                                        psSqlUpdateMovproduto.execute();
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }  catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoAjustarParcelasZeradasEmAberto - " + ex.getMessage());
        } finally {
            Uteis.logarDebug("FIM | ProcessoAjustarParcelasZeradasEmAberto");
        }
    }

    public static void ajustarParcelasZeradasEmAberto(Connection con) throws Exception {
        try {
            Uteis.logarDebug("INÍCIO | ProcessoAjustarParcelasZeradasEmAberto");
            StringBuilder sqlSelectParcelasAbertoVendaAvulsa = new StringBuilder();
            sqlSelectParcelasAbertoVendaAvulsa.append("SELECT\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("\tmpv.codigo AS codParcela,\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("\tmpv.responsavel AS responsavelParcela,\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("\tvav.dataregistro AS dataLancamentoVendaAvulsa,\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("\tmpv.empresa AS codEmpresa,\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("\tmpv.pessoa AS codPessoa,\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("\tpes.nome AS nomePessoa\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("FROM movparcela mpv\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("INNER JOIN movprodutoparcela mpp ON mpp.movparcela = mpv.codigo\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("INNER JOIN movproduto mpd ON mpd.codigo = mpp.movproduto\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("INNER JOIN vendaavulsa vav ON vav.codigo = mpv.vendaavulsa\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("INNER JOIN pessoa pes ON pes.codigo = mpv.pessoa\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("WHERE mpv.descricao LIKE 'Venda Avulsa'\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("AND mpv.valorparcela = 0\n");
            sqlSelectParcelasAbertoVendaAvulsa.append("AND mpv.situacao = 'EA'");
            try (java.sql.Statement stm = con.createStatement()) {
                try (java.sql.ResultSet rs = stm.executeQuery(sqlSelectParcelasAbertoVendaAvulsa.toString())) {
                    while (rs.next()) {
                        int codParcela = rs.getInt("codParcela");
                        int responsavelParcela = rs.getInt("responsavelParcela");
                        Date dataLancamentoVendaAvulsa = rs.getDate("dataLancamentoVendaAvulsa");
                        int codEmpresa = rs.getInt("codEmpresa");
                        int codPessoa = rs.getInt("codPessoa");
                        String nomePessoa = rs.getString("nomePessoa");
                        if (UteisValidacao.emptyNumber(codParcela)) {
                            continue;
                        }
                        StringBuilder sqlInsertReciboPagamento = new StringBuilder();
                        sqlInsertReciboPagamento.append("INSERT INTO recibopagamento (valortotal,pessoapagador,nomepessoapagador,responsavellancamento,contrato,data,empresa,integrado) ");
                        sqlInsertReciboPagamento.append("VALUES (0.0");
                        sqlInsertReciboPagamento.append(",").append(codPessoa);
                        sqlInsertReciboPagamento.append(",'").append(nomePessoa).append("'");
                        sqlInsertReciboPagamento.append(",").append(responsavelParcela);
                        sqlInsertReciboPagamento.append(",NULL");
                        sqlInsertReciboPagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoVendaAvulsa)).append("'");
                        sqlInsertReciboPagamento.append(",").append(codEmpresa);
                        sqlInsertReciboPagamento.append(",false)");
                        try (PreparedStatement sqlInserirReciboPagamento = con.prepareStatement(sqlInsertReciboPagamento.toString())) {
                            sqlInserirReciboPagamento.execute();
                        }
                        try (java.sql.ResultSet rsReciboGerado = stm.executeQuery("SELECT MAX(codigo) AS codigo FROM recibopagamento")) {
                            int codigoReciboGerado = rsReciboGerado.next() ? rsReciboGerado.getInt("codigo") : 0;
                            if (!UteisValidacao.emptyNumber(codigoReciboGerado)) {
                                try (java.sql.ResultSet rsFormaPagamentoAVista = stm.executeQuery("SELECT codigo as codigo FROM FormaPagamento WHERE upper(tipoFormaPagamento) LIKE ('AV') AND ativo AND NOT somentefinanceiro ORDER BY codigo LIMIT 1")) {
                                    int codigoFormaPagamento = rsFormaPagamentoAVista.next() ? rsFormaPagamentoAVista.getInt("codigo") : 0;

                                    StringBuilder sqlInsertMovpagamento = new StringBuilder();
                                    sqlInsertMovpagamento.append("INSERT INTO movpagamento (responsavelpagamento");
                                    sqlInsertMovpagamento.append(",nrparcelacartaocredito");
                                    sqlInsertMovpagamento.append(",movpagamentoescolhida");
                                    sqlInsertMovpagamento.append(",nomepagador");
                                    sqlInsertMovpagamento.append(",formapagamento");
                                    sqlInsertMovpagamento.append(",valor");
                                    sqlInsertMovpagamento.append(",datalancamento");
                                    sqlInsertMovpagamento.append(",datapagamento");
                                    sqlInsertMovpagamento.append(",pessoa");
                                    sqlInsertMovpagamento.append(",dataquitacao");
                                    sqlInsertMovpagamento.append(",recibopagamento");
                                    sqlInsertMovpagamento.append(",empresa");
                                    sqlInsertMovpagamento.append(",observacao");
                                    sqlInsertMovpagamento.append(",autorizacaocartao");
                                    sqlInsertMovpagamento.append(",credito");
                                    sqlInsertMovpagamento.append(",valortotal");
                                    sqlInsertMovpagamento.append(",nsu");
                                    sqlInsertMovpagamento.append(",depositocc");
                                    sqlInsertMovpagamento.append(",datapagamentooriginal");
                                    sqlInsertMovpagamento.append(",usarparceirofidelidade");
                                    sqlInsertMovpagamento.append(",tabelaparceirofidelidade");
                                    sqlInsertMovpagamento.append(",multiplicadorparceirofidelidade");
                                    sqlInsertMovpagamento.append(",tipopontoparceirofidelidade");
                                    sqlInsertMovpagamento.append(",pontosparceirofidelidade");
                                    sqlInsertMovpagamento.append(",cpfparceirofidelidade");
                                    sqlInsertMovpagamento.append(",parceirofidelidadeprocessado");
                                    sqlInsertMovpagamento.append(",codigoexternoprodutoparceirofidelidade");
                                    sqlInsertMovpagamento.append(",senhaparceirofidelidade");
                                    sqlInsertMovpagamento.append(",enviadoconciliadora");
                                    sqlInsertMovpagamento.append(",respostarequisicaopinpad");
                                    sqlInsertMovpagamento.append(",numerounicotransacao)");
                                    sqlInsertMovpagamento.append("VALUES (").append(responsavelParcela);
                                    sqlInsertMovpagamento.append(",0");
                                    sqlInsertMovpagamento.append(",true");
                                    sqlInsertMovpagamento.append(",'").append(nomePessoa).append("'");
                                    sqlInsertMovpagamento.append(",").append(codigoFormaPagamento);
                                    sqlInsertMovpagamento.append(",0.0");
                                    sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoVendaAvulsa)).append("'");
                                    sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoVendaAvulsa)).append("'");
                                    sqlInsertMovpagamento.append(",").append(codPessoa);
                                    sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoVendaAvulsa)).append("'");
                                    ;
                                    sqlInsertMovpagamento.append(",").append(codigoReciboGerado);
                                    sqlInsertMovpagamento.append(",").append(codEmpresa);
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",false");
                                    sqlInsertMovpagamento.append(",0.0");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",false");
                                    sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoVendaAvulsa)).append("'");
                                    ;
                                    sqlInsertMovpagamento.append(",false");
                                    sqlInsertMovpagamento.append(",0");
                                    sqlInsertMovpagamento.append(",0.0");
                                    sqlInsertMovpagamento.append(",0");
                                    sqlInsertMovpagamento.append(",0");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",false");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",false");
                                    sqlInsertMovpagamento.append(",''");
                                    sqlInsertMovpagamento.append(",'')");
                                    try (PreparedStatement sqlInserirMovpagamento = con.prepareStatement(sqlInsertMovpagamento.toString())) {
                                        sqlInserirMovpagamento.execute();
                                    }
                                    try (java.sql.ResultSet rsMovpagamentoGerado = stm.executeQuery("SELECT MAX(codigo) AS codigo FROM movpagamento")) {
                                        int codigoMovpagamentoGerado = rsMovpagamentoGerado.next() ? rsMovpagamentoGerado.getInt("codigo") : 0;
                                        if (!UteisValidacao.emptyNumber(codigoMovpagamentoGerado)) {
                                            StringBuilder sqlInsertPagamentoMovParcela = new StringBuilder();
                                            sqlInsertPagamentoMovParcela.append("INSERT INTO pagamentomovparcela (valorpago");
                                            sqlInsertPagamentoMovParcela.append(",recibopagamento");
                                            sqlInsertPagamentoMovParcela.append(",movparcela");
                                            sqlInsertPagamentoMovParcela.append(",movpagamento)");
                                            sqlInsertPagamentoMovParcela.append("VALUES (0.0");
                                            sqlInsertPagamentoMovParcela.append(",").append(codigoReciboGerado);
                                            sqlInsertPagamentoMovParcela.append(",").append(codParcela);
                                            sqlInsertPagamentoMovParcela.append(",").append(codigoMovpagamentoGerado).append(")");
                                            try (PreparedStatement sqlInserirPagamentoMovparcela = con.prepareStatement(sqlInsertPagamentoMovParcela.toString())) {
                                                sqlInserirPagamentoMovparcela.execute();
                                            }

                                            try (java.sql.ResultSet rsMovprodutoContrato = stm.executeQuery("SELECT codigo as codigo, movproduto as codMovproduto FROM movprodutoparcela WHERE movparcela = " + codParcela)) {
                                                while(rsMovprodutoContrato.next()){
                                                    int codigoMovprodutoParcela = rsMovprodutoContrato.getInt("codigo");
                                                    int codigoMovproduto = rsMovprodutoContrato.getInt("codMovproduto");
                                                    StringBuilder sqlUpdateMovProdutoParcela = new StringBuilder();
                                                    sqlUpdateMovProdutoParcela.append("UPDATE movprodutoparcela SET recibopagamento = ").append(codigoReciboGerado);
                                                    sqlUpdateMovProdutoParcela.append("WHERE codigo = ").append(codigoMovprodutoParcela);
                                                    try (PreparedStatement sqlInserirMovprodutoParcela = con.prepareStatement(sqlUpdateMovProdutoParcela.toString())) {
                                                        sqlInserirMovprodutoParcela.execute();
                                                    }

                                                    StringBuilder sqlUpdateMovparcela = new StringBuilder();
                                                    sqlUpdateMovparcela.append("UPDATE movparcela SET situacao = 'PG' WHERE codigo = ").append(codParcela);
                                                    try (PreparedStatement psSqlUpdateMovparcela = con.prepareStatement(sqlUpdateMovparcela.toString())) {
                                                        psSqlUpdateMovparcela.execute();
                                                    }

                                                    StringBuilder sqlUpdateMovproduto = new StringBuilder();
                                                    sqlUpdateMovproduto.append("UPDATE movproduto SET situacao = 'PG' WHERE codigo = ").append(codigoMovproduto);
                                                    try (PreparedStatement psSqlUpdateMovproduto = con.prepareStatement(sqlUpdateMovproduto.toString())) {
                                                        psSqlUpdateMovproduto.execute();
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }  catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoAjustarParcelasZeradasEmAberto - " + ex.getMessage());
        } finally {
            Uteis.logarDebug("FIM | ProcessoAjustarParcelasZeradasEmAberto");
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "21/10/2024",
        descricao = "Add coluna exibirBotaoAgendaSobreBanner na tabela vendasonlineconfig",
        motivacao = "M2-2076")
public class M22076AddColumExibirBotaoAgendaSobreBanner implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE vendasonlineconfig ADD COLUMN exibirBotaoAgendaSobreBanner Boolean DEFAULT true;",
                    c
            );
        }
    }
}

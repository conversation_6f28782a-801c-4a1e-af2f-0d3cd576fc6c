package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Victor <PERSON>",
        data = "27/05/2025",
        descricao = "Criar configuração para que possam ser configurados agendamentos específicos para fases.",
        motivacao = "IA-1122")
public class AtualizacaoTicketIA1122 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofaseia ADD COLUMN mensagensextras text NULL", c);
        }
    }
}
package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Marcos Andre",
        data = "28/04/2025",
        descricao = "Ajustando Parcelas canceladas indevidamente",
        motivacao = "M1-5320")
public class CorrigirParcelasM15320 implements MigracaoVersaoInterface{
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update movproduto set situacao = 'EA' where codigo in (select movproduto from movprodutoparcela where movparcela in (select par.codigo  from movparcela par inner join contrato c on c.codigo = par.contrato inner join contratorecorrencia cr on cr.contrato =c.codigo  inner join contratooperacao co on co.contrato = c.codigo inner join empresa e on e.codigo = c.empresa where co.dataoperacao > '2025-04-16' and co.dataoperacao < '2025-04-29' and co.tipooperacao  = 'CA'  and cr.cancelamentoproporcional  and e.tipoparcelacancelamento  = 'MA' and par.datavencimento <= co.dataoperacao and par.situacao  = 'CA' and par.descricao not like '%ANUIDADE%' and par.descricao not like '%ALTERA%' and not exists (select codigo from log where operacao = 'CANCELAMENTO - PARCELA' and chaveprimaria::int = par.codigo)));", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update movparcela set situacao = 'EA' where codigo in (select par.codigo  from movparcela par inner join contrato c on c.codigo = par.contrato inner join contratorecorrencia cr on cr.contrato =c.codigo  inner join contratooperacao co on co.contrato = c.codigo inner join empresa e on e.codigo = c.empresa where co.dataoperacao > '2025-04-16' and co.dataoperacao < '2025-04-29' and co.tipooperacao  = 'CA'  and cr.cancelamentoproporcional  and e.tipoparcelacancelamento  = 'MA' and par.datavencimento <= co.dataoperacao and par.situacao  = 'CA' and par.descricao not like '%ANUIDADE%' and par.descricao not like '%ALTERA%' and not exists (select codigo from log where operacao = 'CANCELAMENTO - PARCELA' and chaveprimaria::int = par.codigo));", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by alcides on 24/11/2017.
 */
public class AjustarDataLancamentoNfseEmitida {

    public static void main(String... args) {
        try {
            String nomeThread = args.length > 0 ? args[0] : "axis";
            DAO dao = new DAO();
            Connection con = dao.obterConexaoEspecifica(nomeThread);
            ajustarData(con);

        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarData(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);

    }

}

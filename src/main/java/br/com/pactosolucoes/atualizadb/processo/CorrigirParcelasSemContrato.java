/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;

/**
 *
 * <AUTHOR>
 */
public class CorrigirParcelasSemContrato {
     public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("***************************************************", "zillyonweb", "pactodb");
            corrigirParcelasSemVinculosComContrato(con1);

        } catch (Exception ex) {
            Logger.getLogger(CorrigirProdutosDeMultaEJuros.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void corrigirParcelasSemVinculosComContrato(Connection con) throws Exception {
        String sqlUpdateParcela = "update movparcela SET contrato =  ? where codigo = ?;";
        String sqlExcluirParcela = "DELETE FROM movparcela  where codigo  = ?;";

        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select par.codigo as parcela, par.situacao, par.dataregistro,pro.contrato, pag.recibopagamento as recibopag, mpp.recibopagamento as recibomov, replace(replace(array_agg(pro.codigo)::text,'{','('), '}',')') as codigosprodutos \n" +
                "from movparcela par left join movprodutoparcela mpp on mpp.movparcela = par.codigo\n" +
                "left join movproduto pro on pro.codigo = mpp.movproduto \n" +
                "left  join pagamentomovparcela pag on pag.movparcela = par.codigo\n" +
                "where  par.contrato is null and par.vendaavulsa is null and par.personal is null and par.aulaavulsadiaria is null and par.codigo not in (select parcela from negociacaoeventocontratoparcelas )\n" +
                "group by  par.codigo, pro.contrato, pag.recibopagamento,mpp.recibopagamento,par.situacao,par.dataregistro", con);
        int cont = 0;
        
        while (consulta.next()) {
            if(UteisValidacao.emptyNumber(consulta.getInt("contrato"))){
                try {
                    PreparedStatement sqlExcluir = con.prepareStatement(sqlExcluirParcela);
                    sqlExcluir.setInt(1, consulta.getInt("parcela"));
                    sqlExcluir.execute();
                    Uteis.logar(null,++cont + " - movparcela " + consulta.getInt("parcela") + "  foi excluída");
                }catch(Exception e){
                    Uteis.logar(null,++cont + " - movparcela " + consulta.getInt("parcela") + " teve problemas na exclusao: "+e.getMessage());
                }
            } else {
                PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdateParcela);
                 sqlAlterar.setInt(1, consulta.getInt("contrato"));
                 sqlAlterar.setInt(2, consulta.getInt("parcela"));
                 sqlAlterar.execute();
                 Uteis.logar(null,++cont + " - movparcela " + consulta.getInt("parcela") + " do contrato "+consulta.getInt("contrato")+" foi ajustada");
            }

            
        }

        PagamentoMovParcela.corrigirPagamentosSemMovParcelas(con);
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Vinicius Franca",
        data = "18/03/2025",
        descricao = "Atualização para mesclagem de produtos",
        motivacao = "GCm-64")
public class AtualizacaoTicketProdutoMesclado implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto ADD COLUMN mesclado BOOLEAN DEFAULT false NULL;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("DROP TABLE IF EXISTS PRODUTOSMESCLADOS;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.PRODUTOMESCLADO (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    produtodestino INT NOT NULL, -- pai\n" +
                    "    produtoorigem INT NOT NULL, -- filho\n" +
                    "    dataexecucao TIMESTAMP NULL,\n" +
                    "    responsavel VARCHAR(150) DEFAULT '' NULL\n" +
                    ");", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX mesclado_index_produto_destino ON public.PRODUTOMESCLADO USING BTREE (produtodestino);", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.PRODUTOMESCLADO \n" +
                    "ADD CONSTRAINT fk_PRODUTOSMESCLADOS_produto_destino \n" +
                    "FOREIGN KEY (produtodestino) \n" +
                    "REFERENCES public.produto(codigo);", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.PRODUTOMESCLADO \n" +
                    "ADD CONSTRAINT fk_PRODUTOSMESCLADOS_produto_origem \n" +
                    "FOREIGN KEY (produtoorigem) \n" +
                    "REFERENCES public.produto(codigo);", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "31/10/2024",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-1100")
public class AtualizacaoTicketGC1100 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contratoassinaturadigital ADD COLUMN assinatura2 varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN exigirAssinaturaDigitalResponsavelFinanceiro boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN nomeRespFinanceiro varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN emailRespFinanceiro varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN cpfRespFinanceiro varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN rgRespFinanceiro varchar;", c);
        }
    }
}

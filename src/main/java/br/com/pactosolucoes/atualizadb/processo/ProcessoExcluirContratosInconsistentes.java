package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoExcluirContratosInconsistentes {

    private static ZillyonWebFacade zwFacadeDAO;
    private static Cliente clienteDAO;

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "physicaltrainingqueimadaspb";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            excluirContratosInconsistentes(c);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoExcluirContratosInconsistentes.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void inicializarDao(Connection con) throws Exception {
        zwFacadeDAO = new ZillyonWebFacade(con);
        clienteDAO = new Cliente(con);
    }

    public static void excluirContratosInconsistentes(Connection con) {
        try {
            inicializarDao(con);
            Uteis.logarDebug("INÍCIO | ProcessoExcluirContratosInconsistentes");
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT\n");
            sql.append("\tc.codigo AS codigoContrato,\n");
            sql.append("\tp.codigo AS codigoPessoa,\n");
            sql.append("\tEXISTS (SELECT codigo FROM recibopagamento WHERE contrato =  c.codigo) as existerecibopagamento\n");
            sql.append("FROM contrato c\n");
            sql.append("INNER JOIN pessoa p ON p.codigo = c.pessoa\n");
            sql.append("INNER JOIN empresa e ON e.codigo = c.empresa\n");
            sql.append("WHERE (NOT EXISTS (SELECT codigo FROM contratoduracao WHERE contrato =  c.codigo)\n");
            sql.append("\tOR NOT EXISTS (SELECT codigo FROM contratocondicaopagamento WHERE contrato =  c.codigo)\n");
            sql.append("\tOR NOT EXISTS (SELECT codigo FROM contratohorario WHERE contrato =  c.codigo)\n");
            sql.append("\tOR NOT EXISTS (SELECT codigo FROM contratomodalidade WHERE contrato =  c.codigo))\n");
            sql.append("AND NOT EXISTS (SELECT * FROM contrato c WHERE contratobaseadorenovacao = c.codigo OR contratobaseadorematricula = c.codigo)\n");
            sql.append("AND c.datalancamento > NOW() - INTERVAL '3 months'\n");
            sql.append("ORDER BY c.datalancamento;\n");
            try (java.sql.Statement stm = con.createStatement()) {
                try (java.sql.ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        boolean existerecibopagamento = rs.getBoolean("existerecibopagamento");
                        if (!existerecibopagamento){
                            int codigoContrato = rs.getInt("codigoContrato");
                            StringBuilder sqlExcluir = new StringBuilder();
                            sqlExcluir.append("DELETE FROM cancelamentoassinaturadigital WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contratoassinaturadigital WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM auladesmarcada WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contratocomposicao WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM fecharmetadetalhado WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contratocondicaopagamento WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contratoduracao WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contratohorario WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contratomodalidade WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contratooperacao WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contratoplanoprodutosugerido WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contratotextopadrao WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM controlecreditotreino WHERE contratoorigem = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contratodependente WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM historicocontrato WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM matriculaalunohorarioturma WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM movparcela WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM movproduto WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM periodoacessocliente WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM periodoacessocliente WHERE contratobaseadorenovacao = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM recibopagamento WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM reposicao WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM trancamentocontrato WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contratorecorrencia WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM movprodutomodalidade WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM nfseemitida WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM reajustecontrato WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM recibodevolucao WHERE contrato = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("UPDATE contrato SET contratoresponsavelrenovacaomatricula = 0, datarenovarrealizada = NULL, situacaorenovacao = '' WHERE contratoresponsavelrenovacaomatricula = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("UPDATE contrato SET contratoresponsavelrematriculamatricula = 0, datarematricularealizada = NULL, situacaorematricula = '' WHERE contratoresponsavelrematriculamatricula = ").append(codigoContrato).append(";\n");
                            sqlExcluir.append("DELETE FROM contrato WHERE codigo = ").append(codigoContrato).append(";");
                            try (PreparedStatement statementExcluirContrato = con.prepareStatement(sqlExcluir.toString())) {
                                statementExcluirContrato.execute();
                                ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(rs.getInt("codigoPessoa"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                zwFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                            }
                        }
                    }
                }
            }
            Uteis.logarDebug("FIM | ProcessoExcluirContratosInconsistentes");
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoExcluirContratosInconsistentes - " + ex.getMessage());
        }
    }
}

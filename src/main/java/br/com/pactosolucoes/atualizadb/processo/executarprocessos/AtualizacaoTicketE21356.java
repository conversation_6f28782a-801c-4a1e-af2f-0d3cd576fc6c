package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.dcc.base.ProcessoCorrecaoVinculosE21356;

import java.sql.Connection;

@ClasseProcesso(autor = "Luiz Felipe",
        data = "21/11/2024",
        descricao = "E2-1356 - Vinculo",
        motivacao = "E2-1356 - Vinculo")
public class AtualizacaoTicketE21356 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ProcessoCorrecaoVinculosE21356.processarExecutarProcessos(c);
        }
    }
}

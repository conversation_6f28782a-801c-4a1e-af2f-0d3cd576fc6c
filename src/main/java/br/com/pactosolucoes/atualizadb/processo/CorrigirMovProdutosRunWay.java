/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovParcela;

/**
 *
 * <AUTHOR>
 */
public class CorrigirMovProdutosRunWay {

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("***************************************", "postgres", "pactodb");
            corrigirMovProdutosContratos(con);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void corrigirMovProdutosContratos(Connection con) {
        try {
            String sql = "select cd.numeromeses, pl.descricao as nomeplano, con.codigo, con.* from contrato con\n"
                    + "inner join contratoduracao cd on cd.contrato = con.codigo\n"
                    + "inner join plano pl on con.plano = pl.codigo\n"
                    + "where con.vigenciaate  < con.vigenciaateajustada  and valorfinal > 0 "
                    + "and con.vigenciaateajustada > '2014-10-01' and importacao "
                    + "and (select count(*) from movparcela where contrato = con.codigo) = 1 "
                    + "and (select count(*) from movproduto inner join nfseemitida nfe "
                    + "on nfe.movproduto = movproduto.codigo and movproduto.contrato = con.codigo) = 0";
            ResultSet rscount = SuperFacadeJDBC.criarConsulta("SELECT COUNT(*) as t FROM ("+sql+") as a", con);
            int total = rscount.next() ? rscount.getInt("t") : 0;
            int at = 0;
            MovProduto daoMovProduto = new MovProduto(con);
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            
            while (rs.next()) {
                int meses = (int) (Uteis.nrDiasEntreDatas(rs.getDate("vigenciade"), rs.getDate("vigenciaateajustada")) / 30);
                ContratoVO contratoVO = Contrato.montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                if (rs.getInt("numeromeses") != meses) {
                    Double valorMovProdutos = 0.0;
                    ResultSet rsMovProdutos = SuperFacadeJDBC.criarConsulta("select * from movproduto mp\n"
                            + "where produto = 9 and contrato = " + rs.getInt("codigo"), con);
                    List<MovProdutoVO> listaMovProdutos = new ArrayList<MovProdutoVO>();
                    List<MovProdutoVO> novaListaMovProdutos = new ArrayList<MovProdutoVO>();
                    while (rsMovProdutos.next()) {
                        MovProdutoVO movproduto = MovProduto.montarDados(rsMovProdutos, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                        valorMovProdutos += movproduto.getTotalFinal();
                        listaMovProdutos.add(movproduto);
                    }
                    contratoVO.getPlano().setDescricao(rs.getString("nomeplano"));
                    Double valorMensalidade = Uteis.arredondarForcando2CasasDecimais(valorMovProdutos / meses);
                    Date dataInicio;
                    Date dataFim = new Date(contratoVO.getVigenciaDe().getTime());
                    for (int i = 1; i <= meses; i++) {
                        dataInicio = dataFim;
                        dataFim = Uteis.somarCampoData(dataInicio, Calendar.MONTH, 1);
                        novaListaMovProdutos.add(montarMensalidade(contratoVO, dataInicio, dataFim, 9, valorMensalidade));
                    }
                    
//                    for (MovProdutoVO movp : novaListaMovProdutos) {
//                        System.out.println(movp.getMesReferencia() + " - " + movp.getTotalFinal());
//                    }
                    ResultSet rsParcela = SuperFacadeJDBC.criarConsulta("SELECT * FROM movparcela WHERE contrato = " + contratoVO.getCodigo(), con);
                    if (rsParcela.next()) {
                        MovParcelaVO movParcela = MovParcela.montarDados(rsParcela, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                        ResultSet rsRecibo = SuperFacadeJDBC.criarConsulta("SELECT recibopagamento FROM pagamentomovparcela WHERE movparcela = " + movParcela.getCodigo(), con);
                        if (rsRecibo.next()) {
                            Integer recibo = rsRecibo.getInt("recibopagamento");
                            for (MovProdutoVO mpd : listaMovProdutos) {
                                SuperFacadeJDBC.executarConsulta("DELETE FROM movprodutoparcela where movproduto = " + mpd.getCodigo(), con);
                                SuperFacadeJDBC.executarConsulta("DELETE FROM movproduto where codigo = " + mpd.getCodigo(), con);
                            }
                            for (MovProdutoVO mpd : novaListaMovProdutos) {
                                daoMovProduto.incluir(mpd);
                                SuperFacadeJDBC.executarConsulta("INSERT INTO movprodutoparcela (valorpago, movparcela, recibopagamento,"
                                        + "movproduto) values (" + mpd.getTotalFinal() + ","
                                        + movParcela.getCodigo() + ","
                                        + recibo + ","
                                        + mpd.getCodigo()+")", con);
                            }
                        }

                    }
                    at++;
                    System.out.println(at + "/"+total+" - Contrato: " + rs.getInt("codigo") + " - " + rs.getInt("numeromeses") + " - " + meses);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static MovProdutoVO montarMensalidade(ContratoVO contratoVO, Date inicio, Date fim,
            Integer codProduto, Double valor) {

        Integer ano = Uteis.getAnoData(inicio);
        String mes = Uteis.getMesReferencia(inicio);

        MovProdutoVO movProduto = new MovProdutoVO();
        movProduto.setSituacao("PG");
        movProduto.setDataFinalVigencia(fim);
        movProduto.setDataInicioVigencia(inicio);
        movProduto.setAnoReferencia(ano);
        movProduto.setMesReferencia(mes + "/" + ano);
        movProduto.setResponsavelLancamento(contratoVO.getResponsavelContrato());
        movProduto.setDataLancamento(contratoVO.getDataLancamento());
        movProduto.setTotalFinal(valor);
        movProduto.setValorDesconto(0.0);
        movProduto.setPrecoUnitario(valor);
        movProduto.setQuantidade(1);
        movProduto.setDescricao(contratoVO.getPlano().getDescricao() + " - " + mes + "/" + ano);
        movProduto.setEmpresa(contratoVO.getEmpresa());
        movProduto.setPessoa(contratoVO.getPessoa());
        movProduto.setContrato(contratoVO);
        ProdutoVO produto = new ProdutoVO();
        produto.setCodigo(codProduto);
        movProduto.setProduto(produto);
        movProduto.setApresentarMovProduto(true);
        movProduto.setQuitado(true);
        return movProduto;
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "07/02/2025",
        descricao = "Adicionar coluna chavePrimaria logintegracoes",
        motivacao = "GC-1559")
public class AtualizacaoTicketGC1559 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE logintegracoes ADD COLUMN chaveprimaria varchar(30);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_logintegracoes_chaveprimaria ON public.logintegracoes USING btree (chaveprimaria);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE logintegracoes SET chaveprimaria = (dadosrecebidos::json->>'jsonBodyEnviado')::json->>'identifier'\n" +
                    "\tWHERE chaveprimaria IS NULL\n" +
                    "\tAND servico = 'INTEGRACAO_FOGUETE'\n" +
                    "\tAND dadosrecebidos LIKE '{%}';", c);
        }
    }
}

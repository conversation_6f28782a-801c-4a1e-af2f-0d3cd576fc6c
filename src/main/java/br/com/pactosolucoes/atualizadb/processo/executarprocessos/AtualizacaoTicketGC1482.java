package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "30/01/2025",
        descricao = "GC-1482",
        motivacao = "GC-1482")
public class AtualizacaoTicketGC1482 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE controlecreditotreino ADD COLUMN horarioturma int4", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE controlecreditotreino ADD COLUMN diaaula date", c);
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.CaixaContaVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeTO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 *
 * <AUTHOR>
 */
public class ProcessoStrongerAprovaFacil {
    
    public static final String sqlParcelas = "select pmp.recibopagamento, pmp.movpagamento,  par.codigo as codigoparcela, par.valorparcela, datavencimento,emp.nome as empresa,par.contrato,con.situacao as situacaocontrato, con.vigenciaateajustada, \n" +
            "(select max(a.dthrentrada) from acessocliente a where a.cliente = cli.codigo and a.dthrentrada between con.vigenciade and (con.vigenciaateajustada + '1 day')) as ultimoacesso,\n" +
            " p.nome as aluno, cli.situacao as situacaoaluno, (select exists (select * from autorizacaocobrancacliente  where cliente  = cli.codigo)) as temAutorizacao    \n" +
            "from movparcela par \n" +
            "inner join contrato con on con.codigo = par.contrato \n" +
            "inner join empresa emp on emp.codigo = par.empresa\n" +
            "inner join pessoa p on par.pessoa = p.codigo\n" +
            "inner join cliente cli on cli.pessoa = p.codigo\n" +
            "inner join pagamentomovparcela pmp on pmp.movparcela = par.codigo\n" +
            " %s order by pmp.recibopagamento";
    
    public static void main(String... args) {
        try {
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "stronger");
            Conexao.guardarConexaoForJ2SE(c);
            ajustarParcelas(c);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public static void processoAjustarParcelas(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        ajustarParcelas(con);
    }

    public static void ajustarParcelas(Connection con) throws Exception {
        File recibosIndevidosLog = new File("C:/parcelasStrongerLog.txt");
        FileWriter writerLog = new FileWriter(recibosIndevidosLog, true);
        BufferedWriter saida = new BufferedWriter(writerLog);
        File recibosIndevidos = new File("C:/parcelasStronger.csv");
        FileWriter writer = new FileWriter(recibosIndevidos, true);
        BufferedWriter saidaCSV = new BufferedWriter(writer);
        saida.write("******* Iniciando Processamento ProcessoStrongerAprovaFacil  ******\n");
        saidaCSV.write("empresa;aluno;contrato;parcela;vencimentoParcela;vencimentoContrato;ultimoAcessoAluno;valorParcela;TemAutorizacao\n");
        boolean somenteAtivos = true;
        boolean vencidasAntesDoUltimoAcesso = true;
        Integer codigoProduto = incluirProdutoParaNaoGerarNota();
        Date dataCobranca = Uteis.getDate("30/09/2015");
        
        UsuarioVO usuario = getFacade().getUsuario().consultarPorChavePrimaria(6, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarPorCodigo(0,true, false, Uteis.NIVELMONTARDADOS_ROBO);
        List<Integer> recibosProcessados = new ArrayList<Integer>();
        List<MovParcelaVO> parcelas = null;
        List<CartaoCreditoVO> cartoes = null;
        List<LoteVO> lotes = null;
        Double valorTotal = 0.0;
        for (EmpresaVO empresa : empresas){
            saida.write("###### Iniciando Processamento da Empresa: " + empresa.getNome()+" ########\n");
            StringBuffer where = new StringBuffer(" where  pmp.recibopagamento in" );
            where.append(" (select recibopagamento  from transacao ts where ts.paramsenvio ilike '%UrlRequest=https://teste.ap%' and paramsresposta ilike '%<TransacaoAprovada>True</TransacaoAprovada>%' and recibopagamento is not null  order by codigo) ");
            where.append(" and par.empresa = ").append(empresa.getCodigo());
            if (somenteAtivos){
                where.append(" and cli.situacao = 'AT' ");
            }
            if(vencidasAntesDoUltimoAcesso){
                where.append(" and par.datavencimento < (select max(a.dthrentrada) from acessocliente a where a.cliente = cli.codigo and a.dthrentrada between con.vigenciade and (con.vigenciaateajustada + '1 day')) ");
            }
            Double valorTotalEmpresa = 0.0;
            CaixaVO caixaVoEmAberto = new CaixaVO();
            caixaVoEmAberto.setListaCaixaConta(preencherListaCaixaConta(empresa.getCodigo()));
            caixaVoEmAberto.setDataAbertura(Calendario.hoje());
            caixaVoEmAberto.setDataTrabalho(Calendario.hoje());
            caixaVoEmAberto.setUsuarioVo(usuario);
            caixaVoEmAberto.setEmpresaVo(empresa);
            getFacade().getFinanceiro().getCaixa().incluir(caixaVoEmAberto);
            ResultSet consulta= SuperFacadeJDBC.criarConsulta(String.format(sqlParcelas, new Object[]{             where.toString()                }), con);
            List<CartaoCreditoTO> cartoesDevolvidos =  null;
            while (consulta.next()){
                try{
                    if(!recibosProcessados.contains(consulta.getInt("recibopagamento"))){
                       cartoes  = getFacade().getCartaoCredito().consultarCartaoCreditos(consulta.getInt("movpagamento"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                       for (CartaoCreditoVO cartao: cartoes){
                           CartaoCreditoTO cartaoTO = cartao.toTO();
                           cartoesDevolvidos = new ArrayList<CartaoCreditoTO>();
                           cartoesDevolvidos.add(cartaoTO);
                           lotes = getFacade().getLote().consultarPorCartaoLista(cartao.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                            for (LoteVO lote: lotes){
                                getFacade().getFinanceiro().getMovConta().gravarRetiradaRecebivelLote(Calendario.hoje(),
                                new ArrayList<ChequeTO>(), cartoesDevolvidos, lote, "processo de correção dados Aprova fácil",
                                usuario, caixaVoEmAberto.getCodigo());
                            }
                       }
                       parcelas = getFacade().getMovParcela().consultarPorCodigoRecibo(consulta.getInt("recibopagamento"), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                       estornarRecibo(consulta.getInt("recibopagamento"), caixaVoEmAberto);
                       valorTotalEmpresa = valorTotalEmpresa + processarParcelas(parcelas, consulta, dataCobranca,codigoProduto, saidaCSV, con);
                    }
                }catch(Exception e){
                    saida.write("Recibo: "+consulta.getInt("recibopagamento")+" teve problemas: " +e.getMessage()+"\n");
                }
                recibosProcessados.add(consulta.getInt("recibopagamento"));
                saidaCSV.flush();
            }
             
            valorTotal += valorTotalEmpresa;
            caixaVoEmAberto.setDataFechamento(Calendario.hoje());
            caixaVoEmAberto.setResponsavelFechamento(usuario);
            getFacade().getFinanceiro().getCaixa().alterar(caixaVoEmAberto);
            saida.write("$$$$ Valor total de parcelas alteradas " + valorTotalEmpresa.toString()+"\n");
            saida.write("###### Finalizando Processamento da Empresa: " + empresa.getNome()+" ########\n");
            
            
        }
        
        saida.write("******* finalizando Processamento ProcessoStrongerAprovaFacil  ******\n");
        saida.write("$$$$ Valor total de parcelas alteradas da Todas Empresas " + valorTotal.toString()+"\n");
        saida.close();
        saidaCSV.write("fim;");
        saidaCSV.close();
    }
    
     public static List<CaixaContaVO> preencherListaCaixaConta(Integer empresa) throws Exception {
       List<CaixaContaVO> listaContasAbrirCaixa = new ArrayList<CaixaContaVO>();
        List<ContaVO> lista = getFacade().getFinanceiro().getConta().consultarContasParaCaixa(empresa, Uteis.NIVELMONTARDADOS_TODOS);
        for (ContaVO obj : lista) {
            CaixaContaVO caixaContaVo = new CaixaContaVO();
            obj.setContaEscolhida(Boolean.TRUE);
            caixaContaVo.setContaVo(obj);
            caixaContaVo.setSaldoInicial(obj.getSaldoAtual());
            caixaContaVo.setSaldoFinal(obj.getSaldoAtual());
            listaContasAbrirCaixa.add(caixaContaVo);
        }
        return listaContasAbrirCaixa;
    }
     
     
      public static void estornarRecibo(Integer codigoRecibo, CaixaVO caixa) throws ConsistirException {

        try {
            ReciboPagamentoVO recibo = getFacade().getReciboPagamento().consultarPorChavePrimaria(codigoRecibo, Uteis.NIVELMONTARDADOS_TODOS);
            EstornoReciboVO estornoReciboVO = new EstornoReciboVO();
            estornoReciboVO.setReciboPagamentoVO(recibo);
            estornoReciboVO.setListaMovPagamento(getFacade().getMovPagamento().consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            estornoReciboVO.setListaMovParcela(getFacade().getMovParcela().consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
            getFacade().getCupomFiscal().setCon(getFacade().getMovPagamento().getCon());
            //consulta as transações de cartão de crédito relacionadas aos contratos do recibo
            estornoReciboVO.montarListaTransacoes(estornoReciboVO.getListaMovParcela(), getFacade().getMovPagamento().getCon());
            estornoReciboVO.montarListaItensRemessa(estornoReciboVO.getListaMovParcela(), estornoReciboVO.getListaMovPagamento(), getFacade().getMovPagamento().getCon());

            if (recibo.getEmpresa().getUsarNFSe()) {

                NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultarPorRecibo(recibo.getCodigo());
                if(nfSeEmitidaVO!=null){
                    getFacade().getNFSeEmitida().excluir(nfSeEmitidaVO);
                }
                
            }
            
            getFacade().getReciboPagamento().estornarReciboPagamento(
                    estornoReciboVO,
                    getFacade().getMovPagamento(),
                    getFacade().getMovProdutoParcela(),
                    caixa, "");
            

        } catch (Exception e) {
            throw new ConsistirException("Durante o estorno: " +e.getMessage());
        }
    }

    private static Double processarParcelas(List<MovParcelaVO> parcelas, ResultSet  rs, Date dataCobranca, Integer codigoProduto, BufferedWriter saidaCSV,Connection con) throws Exception {
        Double valorParcelas = 0.0;
        for(MovParcelaVO parcela: parcelas){
            if(Calendario.menorOuIgual(parcela.getDataVencimento(), Calendario.hoje())){
                ResultSet existeItem = SuperFacadeJDBC.criarConsulta("select exists (select codigo from remessaitem  where movparcela = "+parcela.getCodigo()+")", con);
                existeItem.next();
                SuperFacadeJDBC.executarConsultaUpdate("update movparcela set "+(existeItem.getBoolean(1) ? "datacobranca ='"+Uteis.getDataJDBC(dataCobranca)+ "' " : " datavencimento = '"+Uteis.getDataJDBC(dataCobranca)+"' " ) +" where codigo = "+parcela.getCodigo(), con);
            }
            valorParcelas += Uteis.arredondarForcando2CasasDecimais(parcela.getValorParcela());
            SuperFacadeJDBC.executarConsultaUpdate("update movproduto set produto = "+codigoProduto+" where codigo in ( select movproduto from movprodutoparcela where movparcela = "+parcela.getCodigo()+") ", con);
            saidaCSV.write(rs.getString("empresa")+";"+rs.getString("aluno")+";"+parcela.getContrato().getCodigo()+";"+parcela.getCodigo()+";"+Uteis.getData(parcela.getDataVencimento(),"dd/MM/yyyy")+";"+Uteis.getData(rs.getDate("vigenciaateajustada"),"dd/MM/yyyy")+";"+Uteis.getData(rs.getDate("ultimoacesso"), "dd/MM/yyyy")+";"+parcela.getValorParcela()+";"+(rs.getBoolean("temAutorizacao") ? "sim" : "não")+"\n");
        }
        return valorParcelas;
    }

    private static Integer incluirProdutoParaNaoGerarNota() throws Exception {
        ProdutoVO produto = getFacade().getProduto().consultarPorNomeProduto("PLANO PROVISORIO", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if(UteisValidacao.emptyNumber(produto.getCodigo())){
           produto.setDescricao("PLANO PROVISORIO");
           produto.setTipoProduto(TipoProduto.PRODUTO_ESTOQUE.getCodigo());
           produto.setDesativado(Boolean.TRUE);
           produto.setValorBaseCalculo(0.0);
           produto.setValorFinal(0.0);
           produto.setNrDiasVigencia(0);
           produto.getCategoriaProduto().setCodigo(5);
           produto.setBloqueiaPelaVigencia(Boolean.FALSE);
           produto.setPrevalecerVigenciaContrato(false);
           getFacade().getProduto().incluir(produto);
           
        }
        return produto.getCodigo();
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by glauco on 10/12/2014.
 */
public class AjustarHistoricoCreditoDCC {

    public static void main(String... args) {
        try {
//            Connection c = DriverManager.getConnection("************************************************", "postgres", "pactodb");
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "elite");
            Conexao.guardarConexaoForJ2SE(c);
            Integer empresa = (args.length > 1 ? Integer.parseInt(args[1]) : 1);
            ajustarSaldoDCC(empresa, c);
        } catch (Exception ex) {
            Logger.getLogger(AjustarHistoricoCreditoDCC.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarSaldoDCC(Integer empresa, Connection con) throws Exception {
        try {
            con.setAutoCommit(false);

            String apagarLogDcc = "DELETE FROM logdcc\n" +
                    "WHERE 1 = 1\n" +
                    "AND (codigo > (select min(codigo) from logdcc where empresa = " + empresa + "))\n" +
                    "AND empresa = " + empresa + ";";
            SuperFacadeJDBC.executarConsultaUpdate(apagarLogDcc, con);

            int codigoSeqLogDCC = 2;
            String obterCodigo = "Select max(codigo) as codigo from logdcc";
            ResultSet consultaCodigo = SuperFacadeJDBC.criarConsulta(obterCodigo, con);
            if (consultaCodigo.next()) {
                codigoSeqLogDCC = consultaCodigo.getInt(1);
            }

            String sqlSaldoInicial = "SELECT saldo FROM logdcc where codigo = (SELECT min(codigo) FROM logdcc WHERE empresa = " + empresa + ");";
            int saldoInicial = 0;
            ResultSet consultaSaldoInicial = SuperFacadeJDBC.criarConsulta(sqlSaldoInicial, con);
            if (consultaSaldoInicial.next()) {
                saldoInicial = consultaSaldoInicial.getInt(1);
            }

            String consultaHistoricos = "select re.codigo, re.dataregistro, count(rei.codigo) as conta from remessa re\n" +
                    "inner join remessaitem rei on re.codigo = rei.remessa\n" +
                    "where dataregistro > '2014-11-25'\n" +
                    "   AND re.empresa = " + empresa + "\n" +
                    "group by re.codigo, re.dataregistro\n" +
                    "order by re.codigo asc;\n";
            ResultSet consulta = SuperFacadeJDBC.criarConsulta(consultaHistoricos, con);

            while (consulta.next()) {
                int qtdUsada = consulta.getInt("conta");
                saldoInicial -= qtdUsada;
                String sqlNovoLog = "INSERT INTO logdcc(codigo, data, quantidade, saldo, empresa)\n" +
                        "VALUES (" + (++codigoSeqLogDCC) + ",'" + Uteis.getDataJDBCTimestamp(consulta.getTimestamp("dataregistro")) + "'," +
                        "" + (-1 * qtdUsada) + "," + saldoInicial + "," + empresa + ");";
                SuperFacadeJDBC.executarConsultaUpdate(sqlNovoLog, con);
            }

            System.out.println("SaldoFinal: " + saldoInicial);
            String sqlEmpresa = "UPDATE empresa SET creditodcc = " + saldoInicial + " WHERE codigo = " + empresa + ";";
            SuperFacadeJDBC.executarConsultaUpdate(sqlEmpresa, con);

            System.out.println("Concluído para a empresa: " + empresa + " que agora tem saldo de: " + saldoInicial + ".");
        } catch (Exception ex) {
            con.rollback();
            Logger.getLogger(AjustarHistoricoCreditoDCC.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            con.setAutoCommit(true);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import java.sql.Connection;

@ClasseProcesso(
        autor = "Douglas Soares",
        data = "03/02/2025",
        descricao = "Adicionando os campos 'exibirModalPlanosInativos' na tabela 'ConfiguracaoSistema' e 'showModalPlanos' + 'dataexibirmodalplanos' na tabela 'Usuario'.",
        motivacao = "Implementação de nova funcionalidade para exibir aviso de planos inativos ao logar - Ticket M1-XXXX"
)
public class AtualizacaoTicketM14324 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE ConfiguracaoSistema " +
                            "ADD COLUMN exibirModalPlanosInativos BOOLEAN DEFAULT FALSE;",
                    c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE Usuario " +
                            "ADD COLUMN showModalPlanos BOOLEAN DEFAULT FALSE, " +
                            "ADD COLUMN dataexibirmodalplanos DATE;",
                    c);
        }
    }
}

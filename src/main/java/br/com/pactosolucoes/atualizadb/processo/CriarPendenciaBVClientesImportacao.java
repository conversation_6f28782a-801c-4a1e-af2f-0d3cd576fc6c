package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.PerguntaClienteVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaVO;
import negocio.comuns.basico.RespostaPergClienteVO;
import negocio.comuns.basico.RespostaPerguntaVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.basico.PerguntaCliente;
import negocio.facade.jdbc.basico.QuestionarioCliente;
import negocio.facade.jdbc.basico.QuestionarioPergunta;
import negocio.facade.jdbc.basico.QuestionarioPerguntaCliente;
import negocio.facade.jdbc.basico.RespostaPergCliente;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PessoaVO;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 30/04/13
 * Time: 09:21
 */
public class CriarPendenciaBVClientesImportacao extends SuperEntidade {

    ClienteMensagem clienteMensagem;
    QuestionarioCliente questionarioCliente;
    QuestionarioPerguntaCliente questionarioPerguntaCliente;
    QuestionarioPergunta questionarioPergunta;
    PerguntaCliente perguntaCliente;
    RespostaPergCliente respostaPergCliente;
    List questionarioPerguntaVOs;
    List questionarioPerguntaVOsRematricula;
    List questionarioPerguntaVOsRetorno;
    double quantidadeDeClientes = 0;
    double quantidadeRealizada = 0;
    String textoMostrado = "";

    public CriarPendenciaBVClientesImportacao() throws Exception {
    }

    public CriarPendenciaBVClientesImportacao(Connection connection) throws Exception {
        super(connection);
    }

    public static void main(String[] args) {
        try {
            Connection con1;
            if (args.length == 0) {
                con1 = DriverManager.getConnection("*********************************************************************","postgres", "pactodb");
            } else {
                con1 = new DAO().obterConexaoEspecifica(args[0]);
            }
            Conexao.guardarConexaoForJ2SE(con1);
            new CriarPendenciaBVClientesImportacao(con1).executarProcesso();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Metodo criando para receber a conexão que vem do Controle para executar o processo
     * @param connection
     */
    public static void receberConexao(Connection connection) {
        try {
            new CriarPendenciaBVClientesImportacao(connection).executarProcesso();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void executarProcesso() throws SQLException {
        try {
            Uteis.logarDebug("Iniciou em : " + Calendario.hoje());
            con.setAutoCommit(false);
            clienteMensagem = new ClienteMensagem(con);
            questionarioCliente = new QuestionarioCliente(con);
            questionarioPergunta = new QuestionarioPergunta(con);
            questionarioPerguntaCliente = new QuestionarioPerguntaCliente(con);
            perguntaCliente = new PerguntaCliente(con);
            respostaPergCliente = new RespostaPergCliente(con);
            processar(con);
            con.commit();
            Uteis.logarDebug("Terminou em : " + Calendario.hoje());
        } catch (Exception e) {
            if (con != null) {
                con.rollback();
            }
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
            }
        }
    }

    private void processar(Connection con) throws Exception {
        int matricula = 1; //BV Matrícula
        int rematricula = 2; //BV Matrícula
        int retorno = 3;
        int usuario = consultarCodigoUsuarioPactoBR(con);
        int colaboradorPactoBR = consultarCodigoConsultorPactoBR(con);

        ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");

        questionarioPerguntaVOs = questionarioPergunta.consultarPorDescricaoQuestionario("BV MATRICULA", Uteis.NIVELMONTARDADOS_TODOS);
        questionarioPerguntaVOsRematricula = questionarioPergunta.consultarPorDescricaoQuestionario("BV REMATRICULA", Uteis.NIVELMONTARDADOS_TODOS);
        questionarioPerguntaVOsRetorno = questionarioPergunta.consultarPorDescricaoQuestionario("BV RETORNO", Uteis.NIVELMONTARDADOS_TODOS);
        
        Uteis.logarDebug("##Iniciando Processamento de Clientes Sem BV : " + Calendario.hoje());
        List<ItemProcesso> clientesSemBV = consultarClientesSemBV(con);
        quantidadeDeClientes = clientesSemBV.size();
        processarListaDePendencias(clientesSemBV,colaboradorPactoBR, matricula, usuario, TipoBVEnum.MA,questionarioPerguntaVOs  );
        
        Uteis.logarDebug("##Iniciando Processamento de Matriculas Sem BV : " + Calendario.hoje());
        List<ItemProcesso> matriculasSemBV = consultarMatriculasSemBV(con);
        quantidadeDeClientes =  matriculasSemBV.size();
        processarListaDePendencias(matriculasSemBV,colaboradorPactoBR, retorno, usuario, TipoBVEnum.RT,questionarioPerguntaVOsRetorno);
        
        Uteis.logarDebug("##Iniciando Processamento de Rematriculas Sem BV : " + Calendario.hoje());
       List<ItemProcesso> rematriculasSemBV = consultarRematriculasSemBV(con);
        quantidadeDeClientes =  rematriculasSemBV.size();
        processarListaDePendencias(rematriculasSemBV,colaboradorPactoBR, rematricula, usuario, TipoBVEnum.RE,questionarioPerguntaVOsRematricula );

        config.setInformacaoCriarPendenciaBVClientesImportacao("Ajustes concluídos");
        config.setRodandoProdutosPagosServico(false);

    }

    private Integer consultarCodigoUsuarioPactoBR(Connection con) throws Exception {
        String sql = "SELECT\n"
                + "  codigo\n"
                + "FROM usuario\n"
                + "WHERE username = 'PACTOBR'";

        ResultSet resultSet = criarConsulta(sql, con);

        if (resultSet.next()) {
            return resultSet.getInt("codigo");
        }

        return 0;
    }

    private Integer consultarConsultor(Connection con, Integer cliente) throws Exception {
        String sql = "SELECT\n"
                + "  colaborador\n"
                + "FROM vinculo\n"
                + "WHERE cliente = " + cliente + " AND tipovinculo = 'CO'";

        ResultSet resultSet = criarConsulta(sql, con);

        if (resultSet.next()) {
            return resultSet.getInt("colaborador");
        }

        return 0;
    }

    private Integer consultarCodigoConsultorPactoBR(Connection con) throws Exception {
        String sql = "SELECT\n"
                + "  colaborador\n"
                + "FROM usuario\n"
                + "WHERE username = 'PACTOBR'";

        ResultSet resultSet = criarConsulta(sql, con);

        if (resultSet.next()) {
            return resultSet.getInt("colaborador");
        }

        return 0;
    }

    private List<ItemProcesso> consultarClientesSemBV(Connection con) throws Exception {
        List<ItemProcesso> clientesGerarPendenciaBV = new ArrayList<>();
        String sql = "SELECT\n"
                + " cliente.codigo as cliente, pessoa.datacadastro,pessoa.codigo as codigopessoa, vinculo.colaborador\n"
                + "FROM cliente\n"
                + "  INNER JOIN pessoa ON cliente.pessoa = pessoa.codigo\n"
                + "  LEFT JOIN vinculo ON vinculo.cliente = cliente.codigo AND tipovinculo = 'CO'\n"
                + " Left join  questionariocliente ques on ques.cliente = cliente.codigo "
                    
                + " WHERE  ques.codigo is null "
//                + " AND cliente.codigomatricula in (220,228,231,237,254,292) "
                + " ORDER BY 1;";

        ResultSet resultSet = criarConsulta(sql, con);
        while (resultSet.next()) {
            clientesGerarPendenciaBV.add(new ItemProcesso(resultSet.getInt("cliente"), null,
                   null, resultSet.getDate("datacadastro"), resultSet.getInt("colaborador"), resultSet.getInt("codigopessoa")));
        }

        return clientesGerarPendenciaBV;
    }

    private void processarListaDePendencias(List<ItemProcesso> clientesSemBV, int colaboradorPactoBR, int codigoQuestionario, int usuario, TipoBVEnum tipo, List perguntas) throws Exception {
        quantidadeRealizada = 0;
        boolean executarProcesso = true;
        ConfiguracaoSistemaControle config = null;
        if (JSFUtilities.isJSFContext()) {
            config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
            if (config != null) {
                executarProcesso = config.isRodandoCriarPendenciaBVClientesImportacao() && config.isRodandoExecutarProcessos();
            }
        }

        for (ItemProcesso item : clientesSemBV) {
            if (!executarProcesso) {
                return;
            }

           int consultor = item.colaborador;
           if (consultor == 0) {
               consultor = colaboradorPactoBR;
           }
           if (consultor != 0) {
               Date data = item.cadastroCliente;
               if (item.situacaoContrato != null && (item.situacaoContrato.equals("MA") || item.situacaoContrato.equals("RE"))) {
                   data = item.lancamentoContrato == null ? item.cadastroCliente : item.lancamentoContrato;
               }

               QuestionarioClienteVO questionarioClienteVO = criarRegistroQuestionarioCliente(consultor, item.codigoCliente,item.codigoPessoa, codigoQuestionario, tipo, data);
               questionarioClienteVO.setQuestionarioPerguntaClienteVOs(perguntas);

               for (Object questionarioPerguntaVO : perguntas) {
                   QuestionarioPerguntaVO questionarioPerguntaVO1 = (QuestionarioPerguntaVO) questionarioPerguntaVO;

                   PerguntaClienteVO perguntaClienteVO = new PerguntaClienteVO();
                   perguntaClienteVO.setDescricao(questionarioPerguntaVO1.getPergunta().getDescricao());
                   perguntaClienteVO.setTipoPergunta(questionarioPerguntaVO1.getPergunta().getTipoPergunta());
                   if (perguntaClienteVO.getTipoPergunta().equals("ME")) {
                       perguntaClienteVO.setMultipla(true);
                   } else if (perguntaClienteVO.getTipoPergunta().equals("SE")) {
                       perguntaClienteVO.setSimples(true);
                   } else if (perguntaClienteVO.getTipoPergunta().equals("TE")) {
                       perguntaClienteVO.setTextual(true);
                   }
                   perguntaCliente.incluirPerguntaCliente(perguntaClienteVO, false, true);

                   for (RespostaPerguntaVO respostaPerguntaVO : questionarioPerguntaVO1.getPergunta().getRespostaPerguntaVOs()) {
                       RespostaPergClienteVO respostaPergClienteVO = new RespostaPergClienteVO();
                       respostaPergClienteVO.setDescricaoRespota(respostaPerguntaVO.getDescricaoRespota());
                       respostaPergClienteVO.setPerguntaCliente(perguntaClienteVO.getCodigo());
                       respostaPergCliente.incluir(respostaPergClienteVO, false);
                   }


                   QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO = new QuestionarioPerguntaClienteVO();
                   questionarioPerguntaClienteVO.setQuestionarioCliente(questionarioClienteVO.getCodigo());
                   questionarioPerguntaClienteVO.setPerguntaCliente(perguntaClienteVO);
                   questionarioPerguntaCliente.incluir(questionarioPerguntaClienteVO, false, true);
               }

               //Gera a pendência
               quantidadeRealizada++;
               montarMensagensParaUSuario(config);
           } else {
               String log = String.format("Não foi possível gerar Pendência para o cliente: %d", item.codigoCliente);
               Uteis.logarDebug(log);
               if (config != null) {
                   config.setInformacaoCriarPendenciaBVClientesImportacao(log);
                   //EXECUÇÃO DE TODOS PROCESSOS
                   config.setInformacaoExecutarProcessos(log);
               }
           }

        }
    }

    private void montarMensagensParaUSuario(ConfiguracaoSistemaControle config) {
        String textoParaMostrar = Math.rint((quantidadeRealizada / quantidadeDeClientes) * 100) + "% Concluído";
        if (!textoParaMostrar.equals(textoMostrado) && config != null) {
            Uteis.logarDebug(textoParaMostrar);
            config.setInformacaoCriarPendenciaBVClientesImportacao(textoParaMostrar);
            config.setRodandoCriarPendenciaBVClientesImportacao(false);

            //EXECUÇÃO DE TODOS OS PROCESSOS
            config.setInformacaoExecutarProcessos(textoParaMostrar);

            textoMostrado = textoParaMostrar;
        }
    }

    private class ItemProcesso {

        private ItemProcesso(int codigoCliente, Date lancamentoContrato, String situacaoContrato, Date cadastroCliente, int colaborador, int codigoPessoa) {
            this.codigoCliente = codigoCliente;
            this.lancamentoContrato = lancamentoContrato;
            this.situacaoContrato = situacaoContrato;
            this.cadastroCliente = cadastroCliente;
            this.colaborador = colaborador;
            this.codigoPessoa = codigoPessoa;
        }
        private int codigoCliente;
        private int colaborador;
        private Date lancamentoContrato;
        private String situacaoContrato;
        private Date cadastroCliente;
        private int codigoPessoa;
    }

    private ClienteMensagemVO criarRegistroClienteMensagem(QuestionarioClienteVO questionarioCliente, Integer usuario, Integer cliente) throws Exception {
        ClienteMensagemVO clienteMensagemVO = new ClienteMensagemVO();
        clienteMensagemVO.getCliente().setCodigo(cliente);
        clienteMensagemVO.getUsuario().setCodigo(usuario);
        clienteMensagemVO.setQuestionarioCliente(questionarioCliente);
        clienteMensagemVO.setTipomensagem(TiposMensagensEnum.BOLETIM);
        clienteMensagemVO.setMensagem(TiposMensagensEnum.BOLETIM.getMensagem());

        clienteMensagem.incluirSemCommit(clienteMensagemVO);
        return clienteMensagemVO;
    }

    private QuestionarioClienteVO criarRegistroQuestionarioCliente(Integer consultor, Integer cliente,Integer pessoa, Integer questionario, TipoBVEnum tipoBV, Date data) throws Exception {
        QuestionarioClienteVO questionarioClienteVO = new QuestionarioClienteVO();
        questionarioClienteVO.setData(data);
        questionarioClienteVO.getQuestionario().setCodigo(questionario);
        questionarioClienteVO.getCliente().setCodigo(cliente);
        questionarioClienteVO.getCliente().setPessoa(new PessoaVO());
        questionarioClienteVO.getCliente().getPessoa().setCodigo(pessoa);
        questionarioClienteVO.getConsultor().setCodigo(consultor);
        questionarioClienteVO.setTipoBV(tipoBV);
        questionarioClienteVO.setResponsavel(new UsuarioVO());
        questionarioClienteVO.getResponsavel().setNome("admin");
        

        questionarioCliente.incluirSemComit(questionarioClienteVO, false, true);
        return questionarioClienteVO;
    }
    
    private List<ItemProcesso> consultarMatriculasSemBV(Connection con) throws Exception {
       List<ItemProcesso> clientesGerarPendenciaBV = new ArrayList<>();
            String sql = "SELECT\n" +
"                 cliente.codigo as cliente, contrato.datalancamento,pessoa.codigo as codigopessoa, contrato.situacaocontrato, contrato.codigo as contrato, pessoa.datacadastro, contrato.consultor as colaborador\n" +
"                FROM cliente\n" +
"                  INNER JOIN pessoa ON cliente.pessoa = pessoa.codigo\n" +
"                  inner JOIN contrato ON contrato.pessoa = pessoa.codigo  and situacaocontrato = 'MA'\n" +
"                  left join questionariocliente ques on ques.cliente = cliente.codigo and ques.tipobv in (1,2) and date_part('month',ques.data) = date_part('month',contrato.datalancamento) and date_part('year',ques.data) = date_part('year',contrato.datalancamento)\n" +
"                  \n" +
"                WHERE ques.codigo is null\n" +
"                ORDER BY 1;";

        ResultSet resultSet = criarConsulta(sql, con);
        while (resultSet.next()) {
            clientesGerarPendenciaBV.add(new ItemProcesso(resultSet.getInt("cliente"), resultSet.getDate("datalancamento"),
                    resultSet.getString("situacaocontrato"), resultSet.getDate("datacadastro"), resultSet.getInt("colaborador"), resultSet.getInt("codigopessoa")));
        }

        return clientesGerarPendenciaBV;
    }
    
    
     private List<ItemProcesso> consultarRematriculasSemBV(Connection con) throws Exception {
       List<ItemProcesso> clientesGerarPendenciaBV = new ArrayList<>();
        String sql = "SELECT\n" +
"                 cliente.codigo as cliente, contrato.datalancamento,pessoa.codigo as codigopessoa, contrato.situacaocontrato, contrato.codigo as contrato, pessoa.datacadastro, contrato.consultor as colaborador\n" +
"                FROM cliente\n" +
"                  INNER JOIN pessoa ON cliente.pessoa = pessoa.codigo\n" +
"                  inner JOIN contrato ON contrato.pessoa = pessoa.codigo  and situacaocontrato = 'RE'\n" +
"                  left join questionariocliente ques on ques.cliente = cliente.codigo and ques.tipobv in (3,2) and date_part('month',ques.data) = date_part('month',contrato.datalancamento) and date_part('year',ques.data) = date_part('year',contrato.datalancamento)\n" +
"                  \n" +
"                WHERE ques.codigo is null\n" +
"                ORDER BY 1;";

        ResultSet resultSet = criarConsulta(sql, con);
        while (resultSet.next()) {
            clientesGerarPendenciaBV.add(new ItemProcesso(resultSet.getInt("cliente"), resultSet.getDate("datalancamento"),
                    resultSet.getString("situacaocontrato"), resultSet.getDate("datacadastro"), resultSet.getInt("colaborador"), resultSet.getInt("codigopessoa")));
        }

        return clientesGerarPendenciaBV;
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import importador.LeitorExcel;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.FamiliarVO;
import negocio.comuns.basico.ParentescoVO;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Familiar;
import negocio.facade.jdbc.basico.Parentesco;
import org.apache.poi.hssf.usermodel.HSSFRow;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.*;

public class ImportarFamilia {



    public static void main(String[] args) throws Exception{
        String csvFile = "/home/<USER>/pacto-dev/importacao/GrupoDesc.csv";
        String graucsvFile = "/home/<USER>/pacto-dev/importacao/GrauParen.csv";
        String Ca_DescontoFile = "/home/<USER>/pacto-dev/importacao/Ca_Desconto.csv";

        boolean limparFamiliasAntes = false;

        Connection con = DriverManager.getConnection("******************************************************", "postgres", "pactodb");
        if(limparFamiliasAntes){
            SuperFacadeJDBC.executarConsulta("delete from familiar;", con);
        }

        Map<Integer, List<Map<String, Integer>>> dados = new HashMap<>();
        Map<Integer, ParentescoVO> grau = new HashMap<>();
        Map<Integer, Integer> chefes = new HashMap<>();


        List<String[]> listaFamilias = lerArquivo(csvFile);
        List<String[]> listaGraus = lerArquivo(graucsvFile);
        List<String[]> listaChefes = lerArquivo(Ca_DescontoFile);

        for(String[] linha : listaChefes){
            try {
                chefes.put(Integer.valueOf(linha[0]), Integer.valueOf(linha[2]));
            }catch (Exception e){

            }
        }

        for(String[] linha : listaGraus){
            try {
                Parentesco dao = new Parentesco(con);
                grau.put(Integer.valueOf(linha[0]), dao.obterParentescoCriandoSeNaoExiste(linha[1]));
            }catch (Exception e){

            }
        }

        for(String[] linha : listaFamilias){
            try {
                Integer number = Integer.valueOf(linha[0]);
                List<Map<String, Integer>> cods = dados.get(number);
                if(cods == null){
                    cods = new ArrayList<>();
                    dados.put(number, cods);
                }
                Map<String, Integer> map = new HashMap<>();
                map.put("codaluno", Integer.valueOf(linha[2]));
                map.put("grau", Integer.valueOf(linha[3]));
                cods.add(map);
            }catch (Exception e){

            }
        }
        Familiar dao = new Familiar(con);
        for(Integer key: dados.keySet()){
            Integer chefe = chefes.get(key);
            Integer codclientechefe = codcliente(con, chefe);
            if(chefe != null){
                for(Map<String, Integer> familiar : dados.get(key)){
                    FamiliarVO familiarVO = initfamilia(con, codclientechefe, codcliente(con, familiar.get("codaluno")));
                    familiarVO.setParentesco(grau.get(familiar.get("grau")));
                    System.out.println("gravando " + familiarVO.getParentesco().getDescricao() + " do " + codclientechefe );
                    try {
                        dao.incluir(familiarVO, false);
                    }catch (Exception e){

                    }

                }
            }

        }
        ResultSet empresas = SuperFacadeJDBC.criarConsulta("select codigo from empresa", con);
        while (empresas.next()){
            dao.gerarSintetico(empresas.getInt("codigo"));
        }

    }

    public static FamiliarVO initfamilia(Connection con, Integer codclientechefe, Integer codfamiliar) throws Exception{
        FamiliarVO familiarVO = new FamiliarVO();
        familiarVO.setCliente(codclientechefe);
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select cli.situacao, p.nome, cli.codacesso " +
                "from cliente cli inner join pessoa p on cli.pessoa = p.codigo  where cli.matriculaexterna = " + codfamiliar, con);
        if(rs.next()){
            familiarVO.setNome(rs.getString("nome"));
            familiarVO.setCodAcesso(rs.getString("codacesso"));
            familiarVO.setFamiliar(codfamiliar);
            familiarVO.setSituacao(rs.getString("situacao"));
        }
        return familiarVO;
    }

    public static Integer codcliente(Connection con, Integer idexterno) throws Exception{
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select cli.codigo from cliente cli inner join pessoa p on cli.pessoa = p.codigo  where cli.matriculaexterna = " + idexterno, con);
        return rs.next() ? rs.getInt("codigo") : null;
    }

    public static List<String[]> lerArquivo(String csvFile){
        List<String[]> linhas = new ArrayList<>();
        BufferedReader br = null;
        String line = "";
        String cvsSplitBy = ";";
        try {
            br = new BufferedReader(new FileReader(csvFile));

            while ((line = br.readLine()) != null) {
                linhas.add(line.split(cvsSplitBy));
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return linhas;
    }



}

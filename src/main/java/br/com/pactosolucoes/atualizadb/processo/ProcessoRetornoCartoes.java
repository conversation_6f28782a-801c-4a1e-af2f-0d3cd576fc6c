package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.impl.apf.APF;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 06/03/2020
 */
public class ProcessoRetornoCartoes {

    private Connection con;

    public ProcessoRetornoCartoes(Connection con) {
        this.con = con;
    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "INICIO | ProcessoRetornoCartoes...");

            String chave = "teste";
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }

            Connection conOAMD = Conexao.obterConexaoBancoEmpresas();
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT chave from empresa \n");
            sql.append("WHERE 1 = 1 \n");
            if (!UteisValidacao.emptyString(chave)) {
                sql.append(" AND chave = '").append(chave).append("'");
            }

            PreparedStatement st = conOAMD.prepareStatement(sql.toString());
            ResultSet rs = st.executeQuery();
            while (rs.next()) {
                chave = rs.getString("chave");
                Connection con = null;
                try {
                    Uteis.logar(null, "Obter conexão para chave: " + chave);
                    con = new DAO().obterConexaoEspecifica(chave);
                    ProcessoRetornoCartoes processo = new ProcessoRetornoCartoes(con);
                    processo.atualizarTabelas();
                    processo.processar(0, Uteis.obterPrimeiroDiaMes(Calendario.somarMeses(Calendario.hoje(), -3)), Calendario.hoje());
                    processo = null;
                } catch (Exception ex) {
                    Uteis.logar(null, "Erro chave: " + chave);
                    ex.printStackTrace();
                } finally {
                    if (con != null) {
                        con.close();
                    }
                }
            }

            Uteis.logar(true, null, "FIM | ProcessoRetornoCartoes...");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public JSONObject processar(Integer empresa, Date dtInicio, Date dtFim) {
        Integer total = 0;
        Integer sucesso = 0;
        Integer falha = 0;
        try {
            Uteis.logar(true, null, "INICIO | ProcessoRetornoCartoes.processar...");
            Uteis.logar(true, null, "DT Inicio | " + Uteis.getDataComHHMM(dtInicio));
            Uteis.logar(true, null, "DT Fim | " + Uteis.getDataComHHMM(dtFim));

            String sql = getSQLCartaoTransacoesRemessas(empresa, dtInicio, dtFim);
            total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            Uteis.logar(true, null, "ProcessoRetornoCartoes... Total " + total);

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            int ind = 0;
            while (rs.next()) {
                try {
                    con.setAutoCommit(false);

                    Uteis.logar(true, null, "ProcessoRetornoCartoes... " + ++ind + "/" + total);


                    String id = rs.getString("id");
                    if (existeResultado(id)) {
                        Uteis.logar(true, null, "ID ja existe: " + id);
                        continue;
                    }

                    String insert = "INSERT INTO retornocartao(id, matricula, pessoa, datacobranca, codretorno, cartao, tipo, empresa)\n" +
                            "    VALUES (?, ?, ?, ?, ?, ?, ?, ?);";

                    String numeroCartaoMask = rs.getString("cartao");
                    if (!UteisValidacao.emptyString(numeroCartaoMask) && !numeroCartaoMask.contains("***")) {
                        numeroCartaoMask = APF.getCartaoMascarado(numeroCartaoMask);
                    }

                    int i = 0;
                    try (PreparedStatement ps = con.prepareStatement(insert)) {
                        ps.setString(++i, id);
                        ps.setString(++i, rs.getString("matricula"));
                        ps.setInt(++i, rs.getInt("pessoa"));
                        ps.setDate(++i, rs.getDate("datacobranca"));
                        ps.setString(++i, rs.getString("codretorno"));
                        ps.setString(++i, numeroCartaoMask);
                        ps.setString(++i, rs.getString("tipo"));
                        ps.setInt(++i, rs.getInt("empresa"));
                        ps.execute();
                    }

                    ++sucesso;
                    con.commit();
                } catch (Exception ex) {
                    ++falha;
                    con.rollback();
                    ex.printStackTrace();
                    Uteis.logar(true, null, "ProcessoRetornoCartoes : " + ex.getMessage());
                } finally {
                    con.setAutoCommit(true);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            Uteis.logar(true, null, "FIM | ProcessoRetornoCartoes.processar...");
        }

        JSONObject json = new JSONObject();
        json.put("Total", total);
        json.put("Sucesso", sucesso);
        json.put("Falha", falha);
        return json;
    }

    public JSONObject atualizarDataCadastroCartao() {
        String msg = "";
        try {
            Uteis.logar(true, null, "INICIO | ProcessoRetornoCartoes.atualizarDataCadastroCartao...");
            try {
                con.setAutoCommit(false);

                String update = "update retornocartao set datacadastrocartao = (select dataregistro from autorizacaocobrancacliente where cartaomascaradointerno = retornocartao.cartao order by codigo limit 1) where datacadastrocartao is null";
                SuperFacadeJDBC.executarConsulta(update, con);
                msg = "Atualizado com sucesso dataCadastroCartao em retornocartao";
                con.commit();
            } catch (Exception ex) {
                con.rollback();
                ex.printStackTrace();
                throw ex;
            } finally {
                con.setAutoCommit(true);
            }
        } catch (Exception ex) {
            msg = ex.getMessage();
            ex.printStackTrace();
        } finally {
            Uteis.logar(true, null, "FIM | ProcessoRetornoCartoes.atualizarDataCadastroCartao...");
        }

        JSONObject json = new JSONObject();
        json.put("msg", msg);
        return json;
    }

    public boolean existeResultado(String id) throws Exception {
        ResultSet rs = con.prepareStatement("select exists(select codigo from retornocartao where id = '" + id + "') as existe").executeQuery();
        if (rs.next()) {
            return rs.getBoolean(1);
        }
        return false;
    }

    public void atualizarTabelas() {
        try {
            Uteis.logar(true, null, "Inicio | ProcessoRetornoCartoes - AtualizarTabelas...");
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE retornocartao\n" +
                    "(\n" +
                    "  codigo serial primary key,\n" +
                    "  id character varying(50),\n" +
                    "  matricula character varying(20),\n" +
                    "  pessoa integer,\n" +
                    "  datacobranca date,\n" +
                    "  codretorno character varying(20),\n" +
                    "  cartao character varying(225),\n" +
                    "  tipo character varying(20),\n" +
                    "  empresa integer,\n" +
                    "  dataCadastroCartao date  \n" +
                    ")\n" +
                    "WITH (\n" +
                    "  OIDS=FALSE\n" +
                    ");", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE retornocartao OWNER TO zillyonweb;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE EXTENSION pg_trgm;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX retornocartao_pessoa_idx ON retornocartao USING btree(pessoa);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX retornocartao_cartao_trgm_idx ON retornocartao USING gin(cartao gin_trgm_ops);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX retornocartao_id_trgm_idx ON retornocartao USING gin(id gin_trgm_ops);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX retornocartao_codretorno_trgm_idx ON retornocartao USING gin(codretorno gin_trgm_ops);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX autorizacaocobrancacliente_cartaomascaradointerno_trgm_idx ON autorizacaocobrancacliente USING gin(cartaomascaradointerno gin_trgm_ops);", con);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            Uteis.logar(true, null, "FIM | ProcessoRetornoCartoes - AtualizarTabelas...");
        }
    }

    private String getSQLCartaoTransacoesRemessas(Integer empresa, Date dtInicio, Date dtFim) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from ( \n");
        sql.append("select   \n");
        sql.append("('TRANSACAO-' || t.codigo) as id,\n");
        sql.append("sw.matricula,  \n");
        sql.append("t.pessoapagador as pessoa,  \n");
        sql.append("t.dataprocessamento::date as dataCobranca,  \n");
        sql.append("t.codigoretorno as codretorno,    \n");
        sql.append("CASE   \n");
        sql.append("WHEN t.tipo = 2 THEN split_part(split_part(t.paramsenvio, 'card_number\":\"', 2), '\"', 1)  \n");
        sql.append("WHEN t.tipo = 3 THEN split_part(split_part(t.paramsenvio, 'CardNumber\":\"', 2), '\"', 1)  \n");
        sql.append("WHEN t.tipo = 7 THEN split_part(split_part(t.paramsenvio, 'cardNumber\":\"', 2), '\"', 1)  \n");
        sql.append("WHEN t.tipo = 10 THEN split_part(split_part(t.outrasinformacoes, 'card\":\"', 2), '\"', 1)  \n");
        sql.append("WHEN t.tipo = 11 THEN split_part(split_part(t.paramsenvio, '<PAN>', 2), '</', 1)  \n");
        sql.append("ELSE '' END as cartao, \n");
        sql.append("CASE   \n");
        sql.append("WHEN t.tipo = 2 THEN 'VINDI ONLINE'  \n");
        sql.append("WHEN t.tipo = 3 THEN 'CIELO ONLINE'  \n");
        sql.append("WHEN t.tipo = 7 THEN 'REDE ONLINE'   \n");
        sql.append("WHEN t.tipo = 10 THEN 'GETNET ONLINE'   \n");
        sql.append("WHEN t.tipo = 11 THEN 'STONE ONLINE'   \n");
        sql.append("ELSE 'OUTRA' END as tipo, \n");
        sql.append("e.codigo as empresa \n");
        sql.append("from transacao t  \n");
        sql.append("left join situacaoclientesinteticodw sw on sw.codigopessoa = t.pessoapagador\n");
        sql.append("inner join empresa e on e.codigo = t.empresa  \n");
        sql.append("where t.dataprocessamento::date between 'DATA_INICIO' and 'DATA_FIM' \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and t.empresa = ").append(empresa).append(" \n");
        }
        sql.append("union   \n");
        sql.append("select   \n");
        sql.append("('REMESSAITEM-' || ri.codigo) as id,\n");
        sql.append("sw.matricula,  \n");
        sql.append("ri.pessoa,  \n");
        sql.append("r.dataregistro::date as dataCobranca,  \n");
        sql.append("split_part(split_part(split_part(ri.props, 'StatusVenda=', 2), ',', 1), '}', 1) as codretorno,  \n");
        sql.append("split_part(split_part(split_part(ri.props, 'CartaoMascarado=', 2), ',', 1), '}', 1) as cartao,  \n");
        sql.append("CASE   \n");
        sql.append("WHEN r.tipo = 2 THEN 'CIELO EDI'  \n");
        sql.append("WHEN r.tipo = 8 THEN 'GETNET EDI'  \n");
        sql.append("WHEN r.tipo = 12 THEN 'BIN EDI'   \n");
        sql.append("ELSE 'OUTRA REMESSA' END as tipo,  \n");
        sql.append("e.codigo as empresa \n");
        sql.append("from remessaitem ri  \n");
        sql.append("left join situacaoclientesinteticodw sw on sw.codigopessoa = ri.pessoa \n");
        sql.append("inner join remessa r on r.codigo = ri.remessa  \n");
        sql.append("inner join empresa e on e.codigo = r.empresa  \n");
        sql.append("WHERE r.dataregistro::date between 'DATA_INICIO' and 'DATA_FIM'  \n");
        sql.append("and r.tipo in (2,8,12)  \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and r.empresa = ").append(empresa).append(" \n");
        }
        sql.append("and r.situacaoremessa = 2  \n");
        sql.append("and ri.props ilike '%StatusVenda=%') as sql \n");
        sql.append("order by dataCobranca");

        String retorno = sql.toString();
        retorno = retorno.replaceAll("DATA_INICIO", Calendario.getDataAplicandoFormatacao(dtInicio, "yyyy-MM-dd"));
        retorno = retorno.replaceAll("DATA_FIM", Calendario.getDataAplicandoFormatacao(dtFim, "yyyy-MM-dd"));
        return retorno;
    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.MovConta;
import negocio.facade.jdbc.financeiro.NFSeEmitida;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 *
 * <AUTHOR>
 */
public class RollbackContasPagarReceberDeletadas {

    public static void main(String... args) {
        try {
            Connection con = DriverManager.getConnection("*****************************************************-24-03","postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);
            List<MovContaVO> contasInserir = consultarMovConta(con, 1);
            List<NFSeEmitidaVO> notasAtualizar = consultarNfEmitidasMovConta(con, 1);
            con = null;
            Connection con1 = DriverManager.getConnection("*****************************************************","postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con1);
            con1 = null;
            for(MovContaVO mvConta : contasInserir) {
                int codigoMovContaBancoAntigo = mvConta.getCodigo();
                getFacade().getMovConta().incluir(mvConta, mvConta.getCaixa(), false, null);
                int codigoMovContaBancoAtual = mvConta.getCodigo();

                for (NFSeEmitidaVO nota : notasAtualizar) {
                    if (nota.getMovConta().equals(codigoMovContaBancoAntigo)) {
                        getFacade().getNFSeEmitida().atualizarMovConta(codigoMovContaBancoAtual, nota.getCodigo());
                    }
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static List<NFSeEmitidaVO> consultarNfEmitidasMovConta(Connection con, Integer tipoOperacao) {
        try {
            StringBuilder consulta = new StringBuilder();
            consulta.append(" select * from nfseemitida where movconta in (select codigo from movconta where ");
            if (!UteisValidacao.emptyNumber(tipoOperacao)) {
                consulta.append(" tipooperacao = ").append(tipoOperacao);
            } else {
                consulta.append(" tipooperacao in (1,2)"); // Contas a pagar e A Receber.
            }
            consulta.append(" ) ");
            ResultSet result = SuperFacadeJDBC.criarConsulta(consulta.toString(), con);
            List<NFSeEmitidaVO> nfSeEmitidas = new ArrayList<>();
            NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
            while (result.next()) {
                nfSeEmitidas.add(nfSeEmitidaDAO.montarDados(result, Uteis.NIVELMONTARDADOS_TODOS, con));
            }
            nfSeEmitidaDAO = null;
            return nfSeEmitidas;

        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public static List<MovContaVO> consultarMovConta(Connection con, Integer tipoOperacao) {
        try {
            StringBuilder consulta = new StringBuilder();
            consulta.append("select * from movconta ");
            consulta.append(" where ");
            if (!UteisValidacao.emptyNumber(tipoOperacao)) {
                consulta.append(" tipooperacao = ").append(tipoOperacao);
            } else {
                consulta.append(" tipooperacao in (1,2)"); // Contas a pagar e A Receber.
            }
            ResultSet result = SuperFacadeJDBC.criarConsulta(consulta.toString(), con);
            List<MovContaVO> movContas = new ArrayList<>();
            MovConta movContaDAO = new MovConta(con);
            while (result.next()) {
                movContas.add(movContaDAO.montarDados(result, Uteis.NIVELMONTARDADOS_TODOS,false, con));
            }
            movContaDAO = null;
            return movContas;

        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }
}

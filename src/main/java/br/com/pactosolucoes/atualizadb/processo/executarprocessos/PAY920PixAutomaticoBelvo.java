package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "28/07/2025",
        descricao = "Pix Automático Belvo",
        motivacao = "PAY-920")
public class PAY920PixAutomaticoBelvo implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE pixAutomatico (\n" +
                    "    codigo serial not null PRIMARY KEY,\n" +
                    "    idRec VARCHAR(255),\n" +
                    "    dataRegistro TIMESTAMP WITHOUT TIME ZONE,\n" +
                    "    ultimaAtualizacao TIMESTAMP WITHOUT TIME ZONE,\n" +
                    "    objeto TEXT,\n" +
                    "    pessoa INT,\n" +
                    "    statusBelvo INT,\n" +
                    "    devedorCPF VARCHAR(14),\n" +
                    "    devedorNome VARCHAR(255),\n" +
                    "    contrato VARCHAR(255),\n" +
                    "    return_url VARCHAR (255),\n" +
                    "    external_Id VARCHAR (255),\n" +
                    "    possuiCobrancaImediata BOOLEAN DEFAULT FALSE,\n" +
                    "    valorCobrancaImediata DOUBLE PRECISION,\n" +
                    "    parcelasCobrancaImediata TEXT,\n" +
                    "    valorMinimoRecebedor DOUBLE PRECISION,\n" +
                    "    valorMaximoRecebedor DOUBLE PRECISION,\n" +
                    "    dataInicial TIMESTAMP WITHOUT TIME ZONE,\n" +
                    "    dataFinal TIMESTAMP WITHOUT TIME ZONE,\n" +
                    "    periodicidade INT,\n" +
                    "    politicaRetentativa INT,\n" +
                    "    paramsEnvio TEXT,\n" +
                    "    paramsResposta TEXT,\n" +
                    "    authorization_url TEXT,\n" +
                    "    status_updated_at TIMESTAMP WITHOUT TIME ZONE,\n" +
                    "    status_reason_code VARCHAR(255),\n" +
                    "    status_reason_message TEXT,\n" +
                    "    empresa INT,\n" +
                    "    convenioCobranca INT,\n" +
                    "    linkJaUtilizado BOOLEAN DEFAULT FALSE,\n" +
                    "    pixAutomaticoOAMD INT,\n" +
                    "    autorizacaoCobranca INT,\n" +
                    "    usuarioResponsavel INT,\n" +
                    "    icon_logo text,\n" +
                    "    legal_entity_name VARCHAR(255),\n" +
                    "    idInstituicaoBancaria VARCHAR(255),\n" +
                    "    nomeInstituicaoBancaria VARCHAR(255)\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE cobrancapixautomatico (\n" +
                            "    codigo SERIAL PRIMARY KEY,\n" +
                            "    convenioCobranca INT,\n" +
                            "    autorizacaoCobranca INT,\n" +
                            "    idCob VARCHAR(255),\n" +
                            "    idRec VARCHAR(255),\n" +
                            "    dataCobranca DATE,\n" +
                            "    dataPagamento DATE,\n" +
                            "    movPagamento INT,\n" +
                            "    criacao TIMESTAMP WITHOUT TIME ZONE,\n" +
                            "    ajusteDiaUtil BOOLEAN DEFAULT FALSE,\n" +
                            "    valor DOUBLE PRECISION,\n" +
                            "    reciboPagamento INT,\n" +
                            "    pixAutomatico INT NOT NULL,\n" +
                            "    statusBelvo INT,\n" +
                            "    status_updated_at TIMESTAMP WITHOUT TIME ZONE,\n" +
                            "    end_to_end_id VARCHAR(255),\n" +
                            "    empresa INT NOT NULL,\n" +
                            "    infoAdicional TEXT,\n" +
                            "    paramsEnvio TEXT,\n" +
                            "    paramsResposta TEXT,\n" +
                            "    status_reason_code VARCHAR(255),\n" +
                            "    status_reason_message TEXT,\n" +
                            "    previous VARCHAR(255),\n" +
                            "    next VARCHAR(255),\n" +
                            "    cobrancaImediata BOOLEAN DEFAULT FALSE,\n" +
                            "    cobrancapixautomaticooamd INT,\n" +
                            "    contabilizadaPacto BOOLEAN DEFAULT FALSE,\n" +
                            "    CONSTRAINT pix_automatico_empresa_codigo_fk FOREIGN KEY (empresa) REFERENCES empresa(codigo),\n" +
                            "    CONSTRAINT pix_automatico_conveniocobranca_codigo_fk FOREIGN KEY (convenioCobranca) REFERENCES conveniocobranca(codigo)\n" +
                            ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE cobrancapixautomaticoItem (\n" +
                            "    codigo SERIAL PRIMARY KEY,\n" +
                            "    cobrancapixautomatico INT NOT NULL,\n" +
                            "    movparcela INT,\n" +
                            "    dataRegistro TIMESTAMP WITHOUT TIME ZONE,\n" +
                            "    FOREIGN KEY (cobrancapixautomatico) REFERENCES cobrancapixautomatico(codigo),\n" +
                            "    CONSTRAINT fk_movparcelacpixautomaticoitem_movparcela \n" +
                            "    FOREIGN KEY (movparcela) REFERENCES movparcela(codigo) ON DELETE SET NULL\n" +
                            ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE contaBancariaBelvo (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    dataRegistro TIMESTAMP WITHOUT TIME ZONE,\n" +
                    "    institution VARCHAR(255),\n" +
                    "    institution_name VARCHAR(255),\n" +
                    "    id VARCHAR(255),\n" +
                    "    external_id VARCHAR(255),\n" +
                    "    holder_name VARCHAR(255),\n" +
                    "    holder_identifier VARCHAR(255),\n" +
                    "    agency VARCHAR(10),\n" +
                    "    number VARCHAR(15),\n" +
                    "    convenioCobranca INT,\n" +
                    "    paramsEnvio TEXT,\n" +
                    "    paramsResposta TEXT,\n" +
                    "    ativa BOOLEAN DEFAULT FALSE\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniocobranca ADD COLUMN idContaBancariaBeneficiarioBelvo VARCHAR (255)", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacliente ADD COLUMN pixAutomatico INT", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN agruparParcelasPixAutomatico BOOLEAN DEFAULT TRUE", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN pixAutomaticoGerarCobrancaDiasUteis BOOLEAN DEFAULT FALSE", c);
        }
    }
}

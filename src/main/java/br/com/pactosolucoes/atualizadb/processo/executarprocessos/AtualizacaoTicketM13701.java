package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "11/12/2024",
        descricao = "Remover históricos errados",
        motivacao = "M1-3701")
public class AtualizacaoTicketM13701 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("DELETE FROM historicocontrato hc\n" +
                    "WHERE hc.tipohistorico in ('MA', 'RN', 'RE')\n" +
                    "AND hc.contrato IN (SELECT hc2.contrato FROM historicocontrato hc2 WHERE hc2.contrato = hc.contrato AND hc2.tipohistorico = 'TE' AND hc2.datainiciosituacao = hc.datainiciosituacao);", c);
        }
    }

}

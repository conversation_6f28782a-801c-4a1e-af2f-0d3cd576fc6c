package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "16/10/2024",
        descricao = "Ajustar aulas de link de visitante do vendas online duplicadas",
        motivacao = "M2-2358")
public class M22358AjustarAulasDoVendasOnlineLinkVisitanteDuplicadas implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            // primeiro deletar as que estão 'excluídas'
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "DELETE from aulavendasonlinelinkvisitante where dataexclusao is not null;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("DELETE FROM aulavendasonlinelinkvisitante \n" +
                    "WHERE codigo NOT IN ( \n" +
                    "  SELECT MAX(codigo) \n" +
                    "  FROM aulavendasonlinelinkvisitante \n" +
                    "  GROUP BY turma, modalidade, vendasonlineconfig \n" +
                    ");", c);
        }
    }
}

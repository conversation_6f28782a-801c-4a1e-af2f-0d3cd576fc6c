package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

public class ProcessoPovoarConfigsIntegracoes {

    public static void povoarConfiguracaoIntegracaoBuzzLead(Connection con) throws Exception {
        Empresa empresaDAO = new Empresa(con);

        List<EmpresaVO> empresaVOS = empresaDAO.consultarEmpresas();

        for (EmpresaVO empresaVO : empresaVOS) {
            if (empresaVO.getConfiguracaoRDStation() != null) {
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from configuracaointegracaobuzzlead where empresa = " + empresaVO.getCodigo(), con);
                if (rs.next()) {
                    continue;
                }

                try (PreparedStatement stm = con.prepareStatement("INSERT INTO configuracaointegracaobuzzlead(tokenbuzzlead, empresa, habilitada, responsavelpadrao, horalimite, acaoobjecao)\n" +
                        "    VALUES (?, ?, ?, ?, ?, ?);")) {
                    int i = 0;
                    stm.setString(++i, empresaVO.getTokenBuzzLead());
                    stm.setInt(++i, empresaVO.getCodigo());
                    stm.setBoolean(++i, empresaVO.isUsaIntegracoesCrm());
                    if (!UteisValidacao.emptyNumber(empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo())) {
                        stm.setInt(++i, empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo());
                    } else {
                        stm.setNull(++i, 0);
                    }
                    stm.setString(++i, empresaVO.getConfiguracaoRDStation().getHoraLimite());
                    stm.setInt(++i, empresaVO.getConfiguracaoRDStation().getAcaoObjecao());
                    stm.execute();
                }
            }
        }
    }

    public static void povoarConfiguracaoIntegracaoWordPress(Connection con) throws Exception {
        Empresa empresaDAO = new Empresa(con);

        List<EmpresaVO> empresaVOS = empresaDAO.consultarEmpresas();

        for (EmpresaVO empresaVO : empresaVOS) {
            if (empresaVO.getConfiguracaoRDStation() != null) {
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from configuracaointegracaowordpress where empresa = " + empresaVO.getCodigo(), con);
                if (rs.next()) {
                    continue;
                }

                try (PreparedStatement stm = con.prepareStatement("INSERT INTO configuracaointegracaowordpress(empresa, habilitada, responsavelpadrao, horalimite, acaoobjecao)\n" +
                        "    VALUES (?, ?, ?, ?, ?);")) {
                    int i = 0;
                    stm.setInt(++i, empresaVO.getCodigo());
                    stm.setBoolean(++i, empresaVO.isUsaIntegracoesCrm());
                    if (!UteisValidacao.emptyNumber(empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo())) {
                        stm.setInt(++i, empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo());
                    } else {
                        stm.setNull(++i, 0);
                    }
                    stm.setString(++i, empresaVO.getConfiguracaoRDStation().getHoraLimite());
                    stm.setInt(++i, empresaVO.getConfiguracaoRDStation().getAcaoObjecao());
                    stm.execute();
                }
            }
        }
    }

    public static void povoarConfiguracaoIntegracaoJoin(Connection con) throws Exception {
        Empresa empresaDAO = new Empresa(con);

        List<EmpresaVO> empresaVOS = empresaDAO.consultarEmpresas();

        for (EmpresaVO empresaVO : empresaVOS) {
            if (empresaVO.getConfiguracaoRDStation() != null) {
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from configuracaointegracaojoin where empresa = " + empresaVO.getCodigo(), con);
                if (rs.next()) {
                    continue;
                }

                try (PreparedStatement stm = con.prepareStatement("INSERT INTO configuracaointegracaojoin(empresa, habilitada, responsavelpadrao, horalimite, acaoobjecao)\n" +
                        "    VALUES (?, ?, ?, ?, ?);")) {
                    int i = 0;
                    stm.setInt(++i, empresaVO.getCodigo());
                    stm.setBoolean(++i, empresaVO.isUsaIntegracoesCrm());
                    if (!UteisValidacao.emptyNumber(empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo())) {
                        stm.setInt(++i, empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo());
                    } else {
                        stm.setNull(++i, 0);
                    }
                    stm.setString(++i, empresaVO.getConfiguracaoRDStation().getHoraLimite());
                    stm.setInt(++i, empresaVO.getConfiguracaoRDStation().getAcaoObjecao());
                    stm.execute();
                }
            }
        }
    }

    public static void povoarConfiguracaoIntegracaoGenericaLeads(Connection con) throws Exception {
        Empresa empresaDAO = new Empresa(con);

        List<EmpresaVO> empresaVOS = empresaDAO.consultarEmpresas();

        for (EmpresaVO empresaVO : empresaVOS) {
            if (empresaVO.getConfiguracaoRDStation() != null) {
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from configuracaointegracaogenericaleads where empresa = " + empresaVO.getCodigo(), con);
                if (rs.next()) {
                    continue;
                }

                try (PreparedStatement stm = con.prepareStatement("INSERT INTO configuracaointegracaogenericaleads(empresa, habilitada, responsavelpadrao, horalimite, acaoobjecao)\n" +
                        "    VALUES (?, ?, ?, ?, ?);")) {
                    int i = 0;
                    stm.setInt(++i, empresaVO.getCodigo());
                    stm.setBoolean(++i, true);
                    if (!UteisValidacao.emptyNumber(empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo())) {
                        stm.setInt(++i, empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo());
                    } else {
                        stm.setNull(++i, 0);
                    }
                    stm.setString(++i, empresaVO.getConfiguracaoRDStation().getHoraLimite());
                    stm.setInt(++i, empresaVO.getConfiguracaoRDStation().getAcaoObjecao());
                    stm.execute();
                }
            }
        }
    }

    public static void povoarConfiguracaoIntegracaoGenericaLeadsGymbot(Connection con) throws Exception {
        Empresa empresaDAO = new Empresa(con);

        List<EmpresaVO> empresaVOS = empresaDAO.consultarEmpresas();

        for (EmpresaVO empresaVO : empresaVOS) {
            if (empresaVO.getConfiguracaoRDStation() != null) {
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from configuracaointegracaogenericaleadsgymbot where empresa = " + empresaVO.getCodigo(), con);
                if (rs.next()) {
                    continue;
                }

                try (PreparedStatement stm = con.prepareStatement("INSERT INTO configuracaointegracaogenericaleadsgymbot(empresa, habilitada, responsavelpadrao, horalimite, acaoobjecao)\n" +
                        "    VALUES (?, ?, ?, ?, ?);")) {
                    int i = 0;
                    stm.setInt(++i, empresaVO.getCodigo());
                    stm.setBoolean(++i, true);
                    if (!UteisValidacao.emptyNumber(empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo())) {
                        stm.setInt(++i, empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo());
                    } else {
                        stm.setNull(++i, 0);
                    }
                    stm.setString(++i, empresaVO.getConfiguracaoRDStation().getHoraLimite());
                    stm.setInt(++i, empresaVO.getConfiguracaoRDStation().getAcaoObjecao());
                    stm.execute();
                }
            }
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.Transacao;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class ProcessosTransacao {

    public static void main(String[] args) {
        try {
//            Uteis.debug = true;
            Connection con1 = DriverManager.getConnection("*************************************************************", "zillyonweb", "pactodb");
//            Conexao.guardarConexaoForJ2SE(con1);

            Date inicio = Uteis.getDate("01/01/2022", "dd/MM/yyyy");
//            Date fim = Uteis.getDate("01/02/2021", "dd/MM/yyyy");
            Date fim = null;
            boolean somenteAprovadas = true;
            boolean atualizarExistentes = false;
            Integer limite = 100000;
            String tipo = "nrvezes-bandeira-adquirente";
            atualizarOutrasInformacoes(somenteAprovadas, atualizarExistentes, tipo, inicio, fim, limite, con1);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static JSONObject atualizarOutrasInformacoes(boolean somenteAprovadas, boolean atualizarExistentes, String tipo,
                                                        Date inicio, Date fim, Integer limite, Connection con) throws Exception {
        Transacao transacaoDAO;
        JSONObject json = new JSONObject();
        try {
            transacaoDAO = new Transacao(con);
            Date d1 = Calendario.hoje();
            Uteis.logarDebug("AtualizarOutrasInformacoes | Início: " + new Date());

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("t.* \n");
            sql.append("from transacao t \n");
            sql.append("where coalesce(t.paramsenvio, '') <> '' \n");
            sql.append("and coalesce(t.paramsresposta, '') <> '' \n");

            if (somenteAprovadas) {
                sql.append("and t.movpagamento is not null \n");
            }
            if (inicio != null) {
                sql.append("and t.dataprocessamento::date >= '").append(Uteis.getDataFormatoBD(inicio)).append("' \n");
            }
            if (fim != null) {
                sql.append("and t.dataprocessamento::date <= '").append(Uteis.getDataFormatoBD(fim)).append("' \n");
            }


            List<String> listaCondicao = new ArrayList<>();

            boolean nrvezes = tipo.toLowerCase().contains("nrvezes");
            boolean bandeira = tipo.toLowerCase().contains("bandeira");
            boolean adquirente = tipo.toLowerCase().contains("adquirente");

            if (!atualizarExistentes && nrvezes) {
                StringBuilder condicao = new StringBuilder();
                condicao.append("t.outrasinformacoes not ilike '%").append(AtributoTransacaoEnum.numeroParcelas.name()).append("%' \n");
                listaCondicao.add(condicao.toString());
            }

            if (!atualizarExistentes && bandeira) {
                StringBuilder condicao = new StringBuilder();
                condicao.append("t.outrasinformacoes not ilike '%").append(AtributoTransacaoEnum.cartaoBandeira.name()).append("%' \n");
                listaCondicao.add(condicao.toString());
            }

            if (!atualizarExistentes && adquirente) {
                StringBuilder condicao = new StringBuilder();
                condicao.append("t.outrasinformacoes not ilike '%").append(AtributoTransacaoEnum.adquirente.name()).append("%' \n");
                listaCondicao.add(condicao.toString());
            }

            if (listaCondicao.isEmpty()) {
                throw new Exception("Nenhuma condição informada");
            }

            sql.append("and ( \n");
            boolean primeiro = true;
            for (String condicao : listaCondicao) {
                if (primeiro) {
                    primeiro = false;
                } else {
                    sql.append(" or \n");
                }
                sql.append(condicao);
            }
            sql.append(") \n");
            sql.append("order by t.codigo \n");
            if (!UteisValidacao.emptyNumber(limite)) {
                sql.append("limit ").append(limite).append(" \n");
            }

            List<TransacaoVO> transacoes = transacaoDAO.consultarComNivelMontarDados(sql.toString(), Uteis.NIVELMONTARDADOS_BASICO_TRANSACAO);
            if (UteisValidacao.emptyList(transacoes)) {
                throw new Exception("Nenhuma transação encontrada");
            }

            Integer total = transacoes.size();
            Integer qtd_nrVezes = 0;
            Integer qtd_bandeira = 0;
            Integer qtd_adquirente = 0;
            for (TransacaoVO transacaoVO : transacoes) {
                try {
                    transacaoDAO.getCon().setAutoCommit(false);

                    boolean alterou = false;
                    if (nrvezes && !UteisValidacao.emptyNumber(transacaoVO.getNrVezesCobranca())) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.numeroParcelas, transacaoVO.getNrVezesCobranca().toString());
                        qtd_nrVezes++;
                        alterou = true;
                    }

                    if (bandeira && !UteisValidacao.emptyString(transacaoVO.getBandeira())) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira, transacaoVO.getBandeira());
                        qtd_bandeira++;
                        alterou = true;
                    }

                    if (adquirente && !UteisValidacao.emptyString(transacaoVO.getAdquirente())) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.adquirente, transacaoVO.getAdquirente());
                        qtd_adquirente++;
                        alterou = true;
                    }

                    if (alterou) {
                        transacaoDAO.alterarOutrasInformacoes(transacaoVO);
                    }
                    transacaoDAO.getCon().commit();
                } catch (Exception e) {
                    transacaoDAO.getCon().rollback();
                } finally {
                    transacaoDAO.getCon().setAutoCommit(true);
                }
            }

            Uteis.logarDebug("Total: " + total);
            Uteis.logarDebug("nrVezes: " + qtd_nrVezes);
            Uteis.logarDebug("bandeira: " + qtd_bandeira);
            Uteis.logarDebug("adquirente: " + qtd_adquirente);

            json.put("Total", total);
            json.put("NrVezes", qtd_nrVezes);
            json.put("Bandeira", qtd_bandeira);
            json.put("Adquirente", qtd_adquirente);

            Date d2 = Calendario.hoje();
            Uteis.logarDebug("AtualizarOutrasInformacoes | " + json);
            Uteis.logarDebug("AtualizarOutrasInformacoes | Fim: " + new Date());
            Uteis.logarDebug("AtualizarOutrasInformacoes | Tempo Total: " + (d2.getTime() - d1.getTime()));
        } catch (Exception ex) {
            ex.printStackTrace();
            json.put("erro", ex.getMessage());
        } finally {
            transacaoDAO = null;
        }
        return json;
    }
}

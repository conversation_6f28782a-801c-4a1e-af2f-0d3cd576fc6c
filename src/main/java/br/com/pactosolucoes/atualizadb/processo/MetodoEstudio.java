package br.com.pactosolucoes.atualizadb.processo;

/**
 *
 * <AUTHOR> GeoInova <PERSON>çõ<PERSON>
 */
public class MetodoEstudio {

    public static String criarSchema() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE SCHEMA sch_estudio");
        return sql.toString();
    }

    public static String tabelaAgenda() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.agenda");
        sql.append("(");
        sql.append("  id_agenda serial NOT NULL,");
        sql.append("  id_empresa integer NOT NULL,");
        sql.append("  id_produto integer NOT NULL,");
        sql.append("  id_cliente integer NOT NULL,");
        sql.append("  id_colaborador integer NOT NULL,");
        sql.append("  id_ambiente integer NOT NULL,");
        sql.append("  data_aula date NOT NULL,");
        sql.append("  hora_inicio time(6) without time zone NOT NULL,");
        sql.append("  data_lancamento timestamp without time zone NOT NULL,");
        sql.append("  id_usuario_lancamento integer NOT NULL,");
        sql.append("  status character(1),");
        sql.append("  observacao character varying(100),");
        sql.append("  id_tipo_horario integer,");
        sql.append("  status_finan character(1),");
        sql.append("  valor numeric(14,4),");
        sql.append("  CONSTRAINT agenda_pkey PRIMARY KEY (id_agenda),");
        sql.append("  CONSTRAINT fkagenda_ambiente FOREIGN KEY (id_ambiente)     REFERENCES ambiente (codigo) MATCH SIMPLE       ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkagenda_cliente FOREIGN KEY (id_cliente)      REFERENCES cliente (codigo) MATCH SIMPLE       ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkagenda_colaborador FOREIGN KEY (id_colaborador)      REFERENCES colaborador (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkagenda_empresa FOREIGN KEY (id_empresa)       REFERENCES empresa (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkagenda_produto FOREIGN KEY (id_produto)      REFERENCES produto (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkagenda_tipo_horario FOREIGN KEY (id_tipo_horario)      REFERENCES sch_estudio.tipo_horario (id_tipo_horario) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkagenda_usuaro_la FOREIGN KEY (id_usuario_lancamento)      REFERENCES usuario (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaAgendaAgendar() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.agenda_agendar");
        sql.append("(");
        sql.append("  id_agenda_agendar serial NOT NULL,");
        sql.append("  id_vendaavulsa integer,");
        sql.append("  id_produto integer,");
        sql.append("  id_cliente integer,");
        sql.append("  id_empresa integer,");
        sql.append("  CONSTRAINT \"Pk_agenda_agendar\" PRIMARY KEY (id_agenda_agendar),");
        sql.append("  CONSTRAINT \"FK_cliente_agenda\" FOREIGN KEY (id_cliente)      REFERENCES cliente (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT \"FK_empresa_agendar\" FOREIGN KEY (id_empresa)      REFERENCES empresa (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT \"FK_produto_agendar\" FOREIGN KEY (id_produto)      REFERENCES produto (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT \"FK_venda_avulsa_agenda\" FOREIGN KEY (id_vendaavulsa)      REFERENCES vendaavulsa (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaAgendaFaturar() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.agenda_faturar");
        sql.append("(");
        sql.append("  id_agenda_faturar serial NOT NULL,");
        sql.append("  id_agenda integer,");
        sql.append("  id_produto integer,");
        sql.append("  id_cliente integer,");
        sql.append("  id_empresa integer,");
        sql.append("  CONSTRAINT \"PK_agenda_faturar\" PRIMARY KEY (id_agenda_faturar),");
        sql.append("  CONSTRAINT \"FK_agenda_faturar\" FOREIGN KEY (id_agenda)      REFERENCES sch_estudio.agenda (id_agenda) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT \"FK_cliente_agenda_faturar\" FOREIGN KEY (id_cliente)      REFERENCES cliente (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT \"FK_empresa_faturar\" FOREIGN KEY (id_empresa)      REFERENCES empresa (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT \"FK_produto_agenda_faturar\" FOREIGN KEY (id_produto)      REFERENCES produto (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaAgendaVenda() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.agenda_venda");
        sql.append("(");
        sql.append("  id_agenda_venda serial NOT NULL,");
        sql.append("  id_agenda integer,");
        sql.append("  id_vendaavulsa integer,");
        sql.append("  comissao boolean,");
        sql.append("  CONSTRAINT \"PK_agenda_venda\" PRIMARY KEY (id_agenda_venda),");
        sql.append("  CONSTRAINT \"FK_agenda_agenda\" FOREIGN KEY (id_agenda)      REFERENCES sch_estudio.agenda (id_agenda) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaAmbienteIndisponivel() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.ambiente_agenda_indisp");
        sql.append("(");
        sql.append("  id_ambiente_agenda serial NOT NULL,");
        sql.append("  id_ambiente integer NOT NULL,");
        sql.append("  dia_semana integer,");
        sql.append("  dia_mes date,");
        sql.append("  hora_inicial time(6) without time zone,");
        sql.append("  hora_final time(6) without time zone,");
        sql.append("  motivo character varying(60),");
        sql.append("  CONSTRAINT ambiente_agenda_indisp_pkey PRIMARY KEY (id_ambiente_agenda),");
        sql.append("  CONSTRAINT fkambiente_agenda_ambiente FOREIGN KEY (id_ambiente)      REFERENCES ambiente (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaColaboradorIndisponivel() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.colaborador_agenda_indisp");
        sql.append("(");
        sql.append("  id_colaborador_agenda serial NOT NULL,");
        sql.append("  id_colaborador integer NOT NULL,");
        sql.append("  dia_semana integer,");
        sql.append("  dia_mes date,");
        sql.append("  hora_inicial time(6) without time zone,");
        sql.append("  hora_final time(6) without time zone,");
        sql.append("  motivo character varying(60),");
        sql.append("  CONSTRAINT colaborador_agenda_indisp_pkey PRIMARY KEY (id_colaborador_agenda),");
        sql.append("  CONSTRAINT fkcolaborador_agenda_colaborador FOREIGN KEY (id_colaborador)      REFERENCES colaborador (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaConfiguracao() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.configuracao");
        sql.append("(");
        sql.append("  id_empresa integer NOT NULL,");
        sql.append("  hora_inicial time without time zone,");
        sql.append("  hora_final time without time zone,");
        sql.append("  CONSTRAINT configuracao_pkey PRIMARY KEY (id_empresa),");
        sql.append("  CONSTRAINT fkconfiguracao_empresa FOREIGN KEY (id_empresa)      REFERENCES empresa (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaConfiguracaoPreferencia() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.configuracao_preferencia");
        sql.append("(");
        sql.append("  id_configuracao_preferencia serial NOT NULL,");
        sql.append("  id_empresa integer NOT NULL,");
        sql.append("  id_ambiente integer,");
        sql.append("  id_colaborador integer,");
        sql.append("  CONSTRAINT configuracao_preferencia_ambiente_pkey PRIMARY KEY (id_configuracao_preferencia),");
        sql.append("  CONSTRAINT fkconfiguracao_ambiente FOREIGN KEY (id_ambiente)      REFERENCES ambiente (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkconfiguracao_colaborador FOREIGN KEY (id_colaborador)      REFERENCES colaborador (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkconfiguracao_empresa FOREIGN KEY (id_empresa)      REFERENCES empresa (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaEmpresaFechada() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.empresa_fechada");
        sql.append("(");
        sql.append("  id_empresa_fechada serial NOT NULL,");
        sql.append("  id_empresa integer NOT NULL,");
        sql.append("  dia_semana integer,");
        sql.append("  dia_mes date,");
        sql.append("  hora_inicial time(6) without time zone,");
        sql.append("  hora_final time(6) without time zone,");
        sql.append("  motivo character varying(60),");
        sql.append("  CONSTRAINT empresa_fechada_pkey PRIMARY KEY (id_empresa_fechada),");
        sql.append("  CONSTRAINT fkempresa_fechada_empresa FOREIGN KEY (id_empresa)      REFERENCES empresa (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaHistoricoStatusAgenda() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.historico_status_agenda");
        sql.append("(");
        sql.append("  id_historico_status_agenda serial NOT NULL,");
        sql.append("  id_agenda integer NOT NULL,");
        sql.append("  status_anterior character(1),");
        sql.append("  data_lancamento timestamp without time zone NOT NULL,");
        sql.append("  id_usuario_lancamento integer NOT NULL,");
        sql.append("  CONSTRAINT historico_status_agenda_pkey PRIMARY KEY (id_historico_status_agenda),");
        sql.append("  CONSTRAINT fkhistorico_agenda_usuario FOREIGN KEY (id_usuario_lancamento)      REFERENCES usuario (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaPacote() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.pacote");
        sql.append("(");
        sql.append("  id_pacote serial NOT NULL,");
        sql.append("  id_empresa integer,");
        sql.append("  titulo character varying(30),");
        sql.append("  descricao text,");
        sql.append("  validade_inicial date,");
        sql.append("  validade_final date,");
        sql.append("  desativado boolean,");
        sql.append("  valor_total numeric(14,4),");
        sql.append("  imagem bytea,");
        sql.append("  CONSTRAINT \"PK_pacote_codigo\" PRIMARY KEY (id_pacote),");
        sql.append("  CONSTRAINT \"FK_empresa_pacote\" FOREIGN KEY (id_empresa)      REFERENCES empresa (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaPacoteProduto() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.pacote_produto");
        sql.append("(");
        sql.append("  id_pacote integer NOT NULL,");
        sql.append("  id_produto integer NOT NULL,");
        sql.append("  quantidade integer,");
        sql.append("  valor_unitario numeric(14,4),");
        sql.append("  CONSTRAINT \"PK_pacote_produto\" PRIMARY KEY (id_pacote, id_produto)");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaProdutoAmbiente() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.produto_ambiente");
        sql.append("(");
        sql.append("  id_produto_ambiente serial NOT NULL,");
        sql.append("  id_produto integer,");
        sql.append("  id_ambiente integer NOT NULL,");
        sql.append("  id_categoria_produto integer,");
        sql.append("  id_empresa integer NOT NULL DEFAULT 1,");
        sql.append("  CONSTRAINT produto_ambiente_pkey PRIMARY KEY (id_produto_ambiente),");
        sql.append("  CONSTRAINT fkprodutoambiente_ambiente FOREIGN KEY (id_ambiente)      REFERENCES ambiente (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkprodutoambiente_categoria FOREIGN KEY (id_categoria_produto)      REFERENCES categoriaproduto (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkprodutoambiente_produto FOREIGN KEY (id_produto)      REFERENCES produto (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaProdutoColaborador() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.produto_colaborador");
        sql.append("(");
        sql.append("  id_produto_colaborador serial NOT NULL,");
        sql.append("  id_produto integer,");
        sql.append("  id_colaborador integer NOT NULL,");
        sql.append("  id_categoria_produto integer,");
        sql.append("  CONSTRAINT produto_colaborador_pkey PRIMARY KEY (id_produto_colaborador),");
        sql.append("  CONSTRAINT fkproduto_categoria FOREIGN KEY (id_categoria_produto)      REFERENCES categoriaproduto (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkprodutocolaborador_colaborador FOREIGN KEY (id_colaborador)      REFERENCES colaborador (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION,");
        sql.append("  CONSTRAINT fkprodutocolaborador_produto FOREIGN KEY (id_produto)      REFERENCES produto (codigo) MATCH SIMPLE      ON UPDATE NO ACTION ON DELETE NO ACTION");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String tabelaTipoHorario() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.tipo_horario");
        sql.append("(");
        sql.append("  id_tipo_horario serial NOT NULL,");
        sql.append("  descricao character varying(30) NOT NULL,");
        sql.append("  cor character(20),");
        sql.append("  sigla character(2),");
        sql.append("  desativado character(1) NOT NULL,");
        sql.append("  id_empresa integer,");
        sql.append("  CONSTRAINT tipo_horario_pkey PRIMARY KEY (id_tipo_horario),");
        sql.append("  CONSTRAINT sigla_unique UNIQUE (sigla)");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        return sql.toString();
    }

    public static String funcaoDisponibilidade() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE OR REPLACE FUNCTION sch_estudio.fc_disponibilidade(IN arrayprodutos character varying, IN arraycolaborador character varying, IN arrayambiente character varying, IN periodoinicial timestamp without time zone, IN periodofinal timestamp without time zone, IN emp integer)");
        sql.append("  RETURNS TABLE(produto integer, desc_produto character varying, valor real, capacidade_produto integer, colaborador integer, desc_colab character varying, horainicol character varying, horaficol character varying, ambiente integer, desc_ambiente character varying, capacidade_ambiente integer, horainiamb character varying, horafiamb character varying, dia_mes_table date, horainiemp character varying, horafiemp character varying) AS");
        sql.append(" $BODY$ ");
        sql.append(" ");
        sql.append("DECLARE");
        sql.append(" ");
        sql.append("toprec RECORD;");
        sql.append("colabrec RECORD;");
        sql.append("ambrec RECORD;");
        sql.append("emprec RECORD;");
        sql.append("cont integer default 0;");
        sql.append(" ");
        sql.append("BEGIN");
        sql.append(" ");
        sql.append(" ");
        sql.append("FOR toprec IN ");
        sql.append(" ");
        sql.append("	SELECT cp.*, tt.*, pa.id_ambiente as id_ambiente, amb.descricao as desc_amb, amb.capacidade as capcdambiente,");
        sql.append("		prod.descricao, pessoa.nome, prod.valorfinal, prod.capacidade as capcdproduto");
        sql.append("		FROM sch_estudio.vw_colaborador_produto AS cp");
        sql.append("		INNER JOIN public.produto prod ON prod.codigo = cp.id_produto AND desativado = false AND prod.tipoproduto = 'SS'");
        sql.append("		INNER JOIN public.colaborador colab ON colab.codigo = cp.id_colaborador AND situacao = 'AT'");
        sql.append("		INNER JOIN public.tipocolaborador tipoColab ON tipoColab.colaborador = colab.codigo AND tipoColab.descricao = 'ES'");
        sql.append("		INNER JOIN sch_estudio.produto_ambiente pa ON pa.id_produto = prod.codigo");
        sql.append("		INNER JOIN public.ambiente amb ON amb.codigo = pa.id_ambiente");
        sql.append("		INNER JOIN public.pessoa pessoa ON pessoa.codigo = colab.pessoa,");
        sql.append("		(SELECT to_char(t, 'yyyy-MM-dd') as dia_mes_serie ");
        sql.append("			FROM generate_series(periodoinicial, ");
        sql.append("			periodofinal, '1 day') as t ) as tt");
        sql.append("			WHERE ");
        sql.append("				colab.empresa = emp AND ");
        sql.append("				(CASE WHEN LENGTH(arrayprodutos) > 0 THEN (cp.id_produto = ANY (STRING_TO_ARRAY(arrayprodutos, ';')::int[])) ELSE true END)");
        sql.append("				AND (CASE WHEN LENGTH(arraycolaborador) > 0 THEN (id_colaborador = ANY (STRING_TO_ARRAY(arraycolaborador, ';')::int[])) ELSE true END)");
        sql.append("				AND (CASE WHEN LENGTH(arrayambiente) > 0 THEN (id_ambiente = ANY (STRING_TO_ARRAY(arrayambiente, ';')::int[])) ELSE true END)");
        sql.append("				AND dia_mes_serie::date NOT IN ");
        sql.append("				(SELECT dia_mes FROM sch_estudio.ambiente_agenda_indisp ");
        sql.append("					WHERE (dia_mes = dia_mes_serie::date) AND (pa.id_ambiente = id_ambiente) AND (hora_inicial IS NULL AND hora_final IS NULL))");
        sql.append("				AND (extract(dow from dia_mes_serie::date) + 1) NOT IN ");
        sql.append("				(SELECT dia_semana FROM sch_estudio.ambiente_agenda_indisp ");
        sql.append("					WHERE (dia_semana = (extract(dow from dia_mes_serie::date) + 1)) ");
        sql.append("					AND (pa.id_ambiente = id_ambiente) AND (hora_inicial IS NULL AND hora_final IS NULL))");
        sql.append("				AND (dia_mes_serie::date NOT IN ");
        sql.append("				(SELECT dia_mes FROM sch_estudio.colaborador_agenda_indisp ");
        sql.append("					WHERE (dia_mes = dia_mes_serie::date) AND (cp.id_colaborador = id_colaborador) AND (hora_inicial IS NULL AND hora_final IS NULL)))");
        sql.append("				AND (extract(dow from dia_mes_serie::date) + 1) NOT IN ");
        sql.append("				(SELECT dia_semana FROM sch_estudio.colaborador_agenda_indisp ");
        sql.append("					WHERE (dia_semana = (extract(dow from dia_mes_serie::date) + 1)) ");
        sql.append("					AND (cp.id_colaborador = id_colaborador) AND (hora_inicial IS NULL AND hora_final IS NULL))");
        sql.append("				AND (dia_mes_serie::date NOT IN (SELECT dia::date FROM public.feriado))");
        sql.append("				AND (dia_mes_serie::date NOT IN (SELECT dia_mes FROM sch_estudio.empresa_fechada WHERE dia_mes NOTNULL AND (id_empresa = emp) AND (hora_inicial IS NULL AND hora_final IS NULL)))");
        sql.append("				AND (extract(dow from dia_mes_serie::date) + 1) NOT IN ");
        sql.append("				(SELECT dia_semana FROM sch_estudio.empresa_fechada ");
        sql.append("					WHERE dia_semana NOTNULL AND (id_empresa = emp) AND (hora_inicial IS NULL AND hora_final IS NULL))");
        sql.append("			ORDER BY id_colaborador, id_produto, dia_mes_serie LOOP");
        sql.append(" ");
        sql.append("	produto 		:= toprec.id_produto;");
        sql.append("	desc_produto		:= toprec.descricao;");
        sql.append("	capacidade_produto 	:= toprec.capcdproduto;");
        sql.append("	valor			:= toprec.valorfinal; ");
        sql.append("	colaborador  		:= toprec.id_colaborador;");
        sql.append("	desc_colab		:= toprec.nome;");
        sql.append(" ");
        sql.append("	cont = 0;");
        sql.append("	FOR colabrec IN ");
        sql.append("		(SELECT hora_inicial FROM sch_estudio.colaborador_agenda_indisp AS ca ");
        sql.append("			WHERE ca.id_colaborador = colaborador AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1))) ");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horainicol := colabrec.hora_inicial::varchar;");
        sql.append("			ELSE");
        sql.append("				horainicol := coalesce((horainicol),'') || ';' || colabrec.hora_inicial::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append("	");
        sql.append(" ");
        sql.append("	cont = 0;");
        sql.append("	FOR colabrec IN ");
        sql.append("		(SELECT hora_final FROM sch_estudio.colaborador_agenda_indisp AS ca ");
        sql.append("			WHERE ca.id_colaborador = colaborador AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1))) ");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horaficol := colabrec.hora_final::varchar;");
        sql.append("			ELSE");
        sql.append("				horaficol := coalesce((horaficol),'') || ';' || colabrec.hora_final::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append(" ");
        sql.append("	");
        sql.append("	ambiente  		:= toprec.id_ambiente;");
        sql.append("	desc_ambiente		:= toprec.desc_amb;");
        sql.append("	capacidade_ambiente 	:= toprec.capcdambiente;");
        sql.append(" ");
        sql.append("	cont = 0;");
        sql.append("	FOR ambrec IN ");
        sql.append("		(SELECT hora_inicial FROM sch_estudio.ambiente_agenda_indisp ");
        sql.append("			WHERE id_ambiente = ambiente AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1)))");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horainiamb := ambrec.hora_inicial::varchar;");
        sql.append("			ELSE");
        sql.append("				horainiamb := coalesce((horainiamb),'') || ';' || ambrec.hora_inicial::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append("	");
        sql.append(" ");
        sql.append("	cont = 0;");
        sql.append("	FOR ambrec IN ");
        sql.append("		(SELECT hora_final FROM sch_estudio.ambiente_agenda_indisp ");
        sql.append("			WHERE id_ambiente = ambiente AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1)))");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horafiamb := ambrec.hora_final::varchar;");
        sql.append("			ELSE");
        sql.append("				horafiamb := coalesce((horafiamb),'') || ';' || ambrec.hora_final::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append(" ");
        sql.append("	");
        sql.append("	dia_mes_table		:= toprec.dia_mes_serie::date;");
        sql.append("	cont = 0;");
        sql.append("	FOR emprec IN ");
        sql.append("		(SELECT hora_inicial FROM sch_estudio.empresa_fechada ");
        sql.append("		WHERE id_empresa = emp AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1)))");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horainiemp := emprec.hora_inicial::varchar;");
        sql.append("			ELSE");
        sql.append("				horainiemp := coalesce((horainiemp),'') || ';' || emprec.hora_inicial::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append("	");
        sql.append("	");
        sql.append("	cont = 0;");
        sql.append("	FOR emprec IN ");
        sql.append("		(SELECT hora_final FROM sch_estudio.empresa_fechada ");
        sql.append("		WHERE id_empresa = emp AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1)))");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horafiemp := emprec.hora_final::varchar;");
        sql.append("			ELSE");
        sql.append("				horafiemp := coalesce((horafiemp),'') || ';' || emprec.hora_final::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append("	");
        sql.append(" ");
        sql.append("return next;");
        sql.append(" ");
        sql.append("END LOOP;");
        sql.append("");
        sql.append("END;");
        sql.append(" $BODY$ ");
        sql.append("  LANGUAGE plpgsql VOLATILE");
        sql.append("  COST 100");
        sql.append("  ROWS 1000;");
        return sql.toString();
    }

    public static String visaoColaboradorProduto() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE OR REPLACE VIEW sch_estudio.vw_colaborador_produto AS ");
        sql.append("         SELECT a.colaborador AS id_colaborador, b.codigo AS id_produto");
        sql.append("           FROM (         SELECT col.codigo AS colaborador");
        sql.append("                           FROM colaborador col");
        sql.append("                EXCEPT ");
        sql.append("                         SELECT produto_colaborador.id_colaborador AS col");
        sql.append("                           FROM sch_estudio.produto_colaborador");
        sql.append("          ORDER BY 1) a, produto b ");
        sql.append(" UNION ALL ");
        sql.append("         SELECT produto_colaborador.id_colaborador, produto_colaborador.id_produto");
        sql.append("           FROM sch_estudio.produto_colaborador;");
        return sql.toString();
    }

    public static String visaoContadorAmbiente() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE OR REPLACE VIEW sch_estudio.vw_contador_ambiente AS ");
        sql.append(" SELECT count(*) AS matricula, agenda.data_aula AS dia_mes_table, agenda.hora_inicio, agenda.id_ambiente AS ambiente");
        sql.append("   FROM sch_estudio.agenda");
        sql.append("  GROUP BY agenda.data_aula, agenda.hora_inicio, agenda.id_ambiente;");
        return sql.toString();
    }

    public static String visaoContadorColaborador() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE OR REPLACE VIEW sch_estudio.vw_contador_colaborador AS ");
        sql.append(" SELECT count(*) AS matricula, agenda.data_aula AS dia_mes_table, agenda.hora_inicio, agenda.id_colaborador AS colaborador, agenda.id_produto AS produto, agenda.id_ambiente AS ambiente");
        sql.append("   FROM sch_estudio.agenda");
        sql.append("  GROUP BY agenda.data_aula, agenda.hora_inicio, agenda.id_colaborador, agenda.id_produto, agenda.id_ambiente");
        sql.append("  ORDER BY agenda.data_aula;");
        return sql.toString();
    }

    public static String visaoContadorAmbienteFaltas() {
        StringBuilder sql = new StringBuilder();
        sql.append("DROP VIEW sch_estudio.vw_contador_ambiente;");
        sql.append("CREATE OR REPLACE VIEW sch_estudio.vw_contador_ambiente AS ");
        sql.append(" SELECT count(*) AS matricula, agenda.data_aula AS dia_mes_table, agenda.hora_inicio, agenda.id_ambiente AS ambiente");
        sql.append("   FROM sch_estudio.agenda");
        sql.append("   WHERE agenda.status <> 'F'::bpchar AND agenda.status <> 'X'::bpchar");
        sql.append("  GROUP BY agenda.data_aula, agenda.hora_inicio, agenda.id_ambiente;");
        return sql.toString();
    }

    public static String visaoContadorColaboradorFaltas() {
        StringBuilder sql = new StringBuilder();
        sql.append("DROP VIEW sch_estudio.vw_contador_colaborador;");
        sql.append("CREATE OR REPLACE VIEW sch_estudio.vw_contador_colaborador AS ");
        sql.append(" SELECT count(*) AS matricula, agenda.data_aula AS dia_mes_table, agenda.hora_inicio, agenda.id_colaborador AS colaborador, agenda.id_produto AS produto, agenda.id_ambiente AS ambiente");
        sql.append("   FROM sch_estudio.agenda");
        sql.append("   WHERE agenda.status <> 'F'::bpchar AND agenda.status <> 'X'::bpchar");
        sql.append("  GROUP BY agenda.data_aula, agenda.hora_inicio, agenda.id_colaborador, agenda.id_produto, agenda.id_ambiente");
        sql.append("  ORDER BY agenda.data_aula;");
        return sql.toString();
    }

    public static String visaoContador() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE OR REPLACE VIEW sch_estudio.vw_contador AS ");
        sql.append(" SELECT count(*) AS matricula, agenda.data_aula AS dia_mes_table, agenda.hora_inicio, agenda.id_colaborador AS colaborador, agenda.id_ambiente AS ambiente, agenda.id_produto AS produto");
        sql.append("   FROM sch_estudio.agenda");
        sql.append("  GROUP BY agenda.data_aula, agenda.hora_inicio, agenda.id_colaborador, agenda.id_ambiente, agenda.id_produto;");
        return sql.toString();
    }

    public static String visaoContadorFaltas() {
        StringBuilder sql = new StringBuilder();
        sql.append("DROP VIEW sch_estudio.vw_contador;");
        sql.append("CREATE OR REPLACE VIEW sch_estudio.vw_contador AS ");
        sql.append(" SELECT count(*) AS matricula, agenda.data_aula AS dia_mes_table, agenda.hora_inicio, agenda.id_colaborador AS colaborador, agenda.id_ambiente AS ambiente, agenda.id_produto AS produto");
        sql.append("   FROM sch_estudio.agenda");
        sql.append("   WHERE agenda.status <> 'F'::bpchar AND agenda.status <> 'X'::bpchar");
        sql.append("  GROUP BY agenda.data_aula, agenda.hora_inicio, agenda.id_colaborador, agenda.id_ambiente, agenda.id_produto;");
        return sql.toString();
    }

    public static String campoTabelaProduto() {
        StringBuilder sql = new StringBuilder();
        sql.append("ALTER TABLE produto add column capacidade integer;");
        return sql.toString();
    }

    public static String campoTabelaAmbiente() {
        StringBuilder sql = new StringBuilder();
        sql.append("ALTER TABLE ambiente add column capacidade integer;");
        return sql.toString();
    }

    public static String insertTipoHorario() {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO sch_estudio.tipo_horario (descricao, cor, sigla, desativado, id_empresa) VALUES ('Aula Normal', '#54e031', 'A', '0', 1);");
        return sql.toString();
    }

    public static String tabelaAgendaExcecao() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE sch_estudio.agenda_excecao");
        sql.append("(");
        sql.append("  id_agenda_excecao serial NOT NULL,");
        sql.append("  resolvido_como character(1),");
        sql.append("  id_agenda_remarcada integer,");
        sql.append("  id_vendaavulsa integer,");
        sql.append("  id_empresa integer NOT NULL,");
        sql.append("  id_produto integer NOT NULL,");
        sql.append("  id_cliente integer NOT NULL,");
        sql.append("  id_colaborador integer NOT NULL,");
        sql.append("  id_ambiente integer NOT NULL,");
        sql.append("  data_aula date NOT NULL,");
        sql.append("  hora_inicio time(6) without time zone NOT NULL,");
        sql.append("  data_lancamento timestamp without time zone NOT NULL,");
        sql.append("  id_usuario_lancamento integer NOT NULL,");
        sql.append("  status character(1),");
        sql.append("  observacao character varying(100),");
        sql.append("  id_tipo_horario integer,");
        sql.append("  CONSTRAINT PK_agenda_excecao_id_agenda PRIMARY KEY (id_agenda_excecao)");
        sql.append(")");
        sql.append("WITH (");
        sql.append("  OIDS=FALSE");
        sql.append(");");
        sql.append("COMMENT ON COLUMN sch_estudio.agenda_excecao.resolvido_como IS '");
        sql.append("0 - Pendente");
        sql.append("1 - Remarcação");
        sql.append("2 - Prescreveu Remarcação';");
        return sql.toString();
    }

    public static String funcaoDisponibilidadeNull() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE OR REPLACE FUNCTION sch_estudio.fc_disponibilidade(IN arrayprodutos character varying, IN arraycolaborador character varying, IN arrayambiente character varying, IN periodoinicial timestamp without time zone, IN periodofinal timestamp without time zone, IN emp integer)");
        sql.append("  RETURNS TABLE(produto integer, desc_produto character varying, valor real, capacidade_produto integer, colaborador integer, desc_colab character varying, horainicol character varying, horaficol character varying, ambiente integer, desc_ambiente character varying, capacidade_ambiente integer, horainiamb character varying, horafiamb character varying, dia_mes_table date, horainiemp character varying, horafiemp character varying) AS");
        sql.append(" $BODY$ ");
        sql.append(" ");
        sql.append("DECLARE");
        sql.append(" ");
        sql.append("toprec RECORD;");
        sql.append("colabrec RECORD;");
        sql.append("ambrec RECORD;");
        sql.append("emprec RECORD;");
        sql.append("cont integer default 0;");
        sql.append(" ");
        sql.append("BEGIN");
        sql.append(" ");
        sql.append(" ");
        sql.append("FOR toprec IN ");
        sql.append(" ");
        sql.append("	SELECT cp.*, tt.*, pa.id_ambiente as id_ambiente, amb.descricao as desc_amb, amb.capacidade as capcdambiente,");
        sql.append("		prod.descricao, pessoa.nome, prod.valorfinal, prod.capacidade as capcdproduto");
        sql.append("		FROM sch_estudio.vw_colaborador_produto AS cp");
        sql.append("		INNER JOIN public.produto prod ON prod.codigo = cp.id_produto AND desativado = false AND prod.tipoproduto = 'SS'");
        sql.append("		INNER JOIN public.colaborador colab ON colab.codigo = cp.id_colaborador AND situacao = 'AT'");
        sql.append("		INNER JOIN public.tipocolaborador tipoColab ON tipoColab.colaborador = colab.codigo AND tipoColab.descricao = 'ES'");
        sql.append("		INNER JOIN sch_estudio.produto_ambiente pa ON pa.id_produto = prod.codigo");
        sql.append("		INNER JOIN public.ambiente amb ON amb.codigo = pa.id_ambiente");
        sql.append("		INNER JOIN public.pessoa pessoa ON pessoa.codigo = colab.pessoa,");
        sql.append("		(SELECT to_char(t, 'yyyy-MM-dd') as dia_mes_serie ");
        sql.append("			FROM generate_series(periodoinicial, ");
        sql.append("			periodofinal, '1 day') as t ) as tt");
        sql.append("			WHERE ");
        sql.append("				colab.empresa = emp AND ");
        sql.append("				(CASE WHEN LENGTH(arrayprodutos) > 0 THEN (cp.id_produto = ANY (STRING_TO_ARRAY(arrayprodutos, ';')::int[])) ELSE true END)");
        sql.append("				AND (CASE WHEN LENGTH(arraycolaborador) > 0 THEN (id_colaborador = ANY (STRING_TO_ARRAY(arraycolaborador, ';')::int[])) ELSE true END)");
        sql.append("				AND (CASE WHEN LENGTH(arrayambiente) > 0 THEN (id_ambiente = ANY (STRING_TO_ARRAY(arrayambiente, ';')::int[])) ELSE true END)");
        sql.append("				AND dia_mes_serie::date NOT IN ");
        sql.append("				(SELECT dia_mes FROM sch_estudio.ambiente_agenda_indisp ");
        sql.append("					WHERE (dia_mes = dia_mes_serie::date) AND (pa.id_ambiente = id_ambiente) AND (hora_inicial IS NULL AND hora_final IS NULL))");
        sql.append("				AND (extract(dow from dia_mes_serie::date) + 1) NOT IN ");
        sql.append("				(SELECT dia_semana FROM sch_estudio.ambiente_agenda_indisp ");
        sql.append("					WHERE (dia_semana = (extract(dow from dia_mes_serie::date) + 1)) ");
        sql.append("					AND (pa.id_ambiente = id_ambiente) AND (hora_inicial IS NULL AND hora_final IS NULL))");
        sql.append("				AND (dia_mes_serie::date NOT IN ");
        sql.append("				(SELECT dia_mes FROM sch_estudio.colaborador_agenda_indisp ");
        sql.append("					WHERE (dia_mes = dia_mes_serie::date) AND (cp.id_colaborador = id_colaborador) AND (hora_inicial IS NULL AND hora_final IS NULL)))");
        sql.append("				AND (extract(dow from dia_mes_serie::date) + 1) NOT IN ");
        sql.append("				(SELECT dia_semana FROM sch_estudio.colaborador_agenda_indisp ");
        sql.append("					WHERE (dia_semana = (extract(dow from dia_mes_serie::date) + 1)) ");
        sql.append("					AND (cp.id_colaborador = id_colaborador) AND (hora_inicial IS NULL AND hora_final IS NULL))");
        sql.append("				AND (dia_mes_serie::date NOT IN (SELECT dia::date FROM public.feriado))");
        sql.append("				AND (dia_mes_serie::date NOT IN (SELECT dia_mes FROM sch_estudio.empresa_fechada WHERE dia_mes NOTNULL AND (id_empresa = emp) AND (hora_inicial IS NULL AND hora_final IS NULL)))");
        sql.append("				AND (extract(dow from dia_mes_serie::date) + 1) NOT IN ");
        sql.append("				(SELECT dia_semana FROM sch_estudio.empresa_fechada ");
        sql.append("					WHERE dia_semana NOTNULL AND (id_empresa = emp) AND (hora_inicial IS NULL AND hora_final IS NULL))");
        sql.append("			ORDER BY id_colaborador, id_produto, dia_mes_serie LOOP");
        sql.append(" ");
        sql.append("	produto 		:= toprec.id_produto;");
        sql.append("	desc_produto		:= toprec.descricao;");
        sql.append("	capacidade_produto 	:= toprec.capcdproduto;");
        sql.append("	valor			:= toprec.valorfinal; ");
        sql.append("	colaborador  		:= toprec.id_colaborador;");
        sql.append("	desc_colab		:= toprec.nome;");
        sql.append(" ");
        sql.append("	cont = 0;");
        sql.append("	horainicol := null;");
        sql.append("	FOR colabrec IN ");
        sql.append("		(SELECT hora_inicial FROM sch_estudio.colaborador_agenda_indisp AS ca ");
        sql.append("			WHERE ca.id_colaborador = colaborador AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1))) ");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horainicol := colabrec.hora_inicial::varchar;");
        sql.append("			ELSE");
        sql.append("				horainicol := coalesce((horainicol),'') || ';' || colabrec.hora_inicial::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append("	");
        sql.append(" ");
        sql.append("	cont = 0;");
        sql.append("	horaficol := null;");
        sql.append("	FOR colabrec IN ");
        sql.append("		(SELECT hora_final FROM sch_estudio.colaborador_agenda_indisp AS ca ");
        sql.append("			WHERE ca.id_colaborador = colaborador AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1))) ");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horaficol := colabrec.hora_final::varchar;");
        sql.append("			ELSE");
        sql.append("				horaficol := coalesce((horaficol),'') || ';' || colabrec.hora_final::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append(" ");
        sql.append("	");
        sql.append("	ambiente  		:= toprec.id_ambiente;");
        sql.append("	desc_ambiente		:= toprec.desc_amb;");
        sql.append("	capacidade_ambiente 	:= toprec.capcdambiente;");
        sql.append(" ");
        sql.append("	cont = 0;");
        sql.append("	horainiamb := null;");
        sql.append("	FOR ambrec IN ");
        sql.append("		(SELECT hora_inicial FROM sch_estudio.ambiente_agenda_indisp ");
        sql.append("			WHERE id_ambiente = ambiente AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1)))");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horainiamb := ambrec.hora_inicial::varchar;");
        sql.append("			ELSE");
        sql.append("				horainiamb := coalesce((horainiamb),'') || ';' || ambrec.hora_inicial::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append("	");
        sql.append(" ");
        sql.append("	cont = 0;");
        sql.append("	horafiamb := null;");
        sql.append("	FOR ambrec IN ");
        sql.append("		(SELECT hora_final FROM sch_estudio.ambiente_agenda_indisp ");
        sql.append("			WHERE id_ambiente = ambiente AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1)))");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horafiamb := ambrec.hora_final::varchar;");
        sql.append("			ELSE");
        sql.append("				horafiamb := coalesce((horafiamb),'') || ';' || ambrec.hora_final::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append(" ");
        sql.append("	");
        sql.append("	dia_mes_table		:= toprec.dia_mes_serie::date;");
        sql.append("	cont = 0;");
        sql.append("	horainiemp := null;");
        sql.append("	FOR emprec IN ");
        sql.append("		(SELECT hora_inicial FROM sch_estudio.empresa_fechada ");
        sql.append("		WHERE id_empresa = emp AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1)))");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horainiemp := emprec.hora_inicial::varchar;");
        sql.append("			ELSE");
        sql.append("				horainiemp := coalesce((horainiemp),'') || ';' || emprec.hora_inicial::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append("	");
        sql.append("	");
        sql.append("	cont = 0;");
        sql.append("	horafiemp := null;");
        sql.append("	FOR emprec IN ");
        sql.append("		(SELECT hora_final FROM sch_estudio.empresa_fechada ");
        sql.append("		WHERE id_empresa = emp AND ((dia_mes = toprec.dia_mes_serie::date) OR dia_semana = (extract(dow from toprec.dia_mes_serie::date) + 1)))");
        sql.append("		LOOP");
        sql.append("			IF (cont = 0) THEN");
        sql.append("				horafiemp := emprec.hora_final::varchar;");
        sql.append("			ELSE");
        sql.append("				horafiemp := coalesce((horafiemp),'') || ';' || emprec.hora_final::varchar;");
        sql.append("			END IF;");
        sql.append("			cont = cont + 1;");
        sql.append("		END LOOP;");
        sql.append("	");
        sql.append(" ");
        sql.append("return next;");
        sql.append(" ");
        sql.append("END LOOP;");
        sql.append("");
        sql.append("END;");
        sql.append(" $BODY$ ");
        sql.append("  LANGUAGE plpgsql VOLATILE");
        sql.append("  COST 100");
        sql.append("  ROWS 1000;");
        return sql.toString();
    }

    public static String campoPorcTabelaProdutoColaborador() {
        StringBuilder sql = new StringBuilder();
        sql.append("ALTER TABLE sch_estudio.produto_colaborador add column porccomissao real;");
        return sql.toString();
    }

    public static String funcaoHorarioDisponibilidade() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE OR REPLACE FUNCTION sch_estudio.fc_horarios_possiveis(IN periodoinicial date, IN periodofinal date, IN horainicial time without time zone, IN horafinal time without time zone, IN emp integer) ");
        sql.append("  RETURNS TABLE(id_ambiente integer, desc_ambiente character varying, horarios integer, totalhorarios integer) AS ");
        sql.append("$BODY$ ");
        sql.append("DECLARE ");
        sql.append(" ");
        sql.append("toprec RECORD; ");
        sql.append("horarec RECORD; ");
        sql.append("empsemanarec RECORD; ");
        sql.append("empmesrec RECORD; ");
        sql.append("horainic TIME; ");
        sql.append("horafi TIME; ");
        sql.append("cont INTEGER DEFAULT 0; ");
        sql.append("totalcont INTEGER DEFAULT 0; ");
        sql.append(" ");
        sql.append("BEGIN ");
        sql.append(" ");
        sql.append("IF (horainicial IS NULL) THEN ");
        sql.append("	horainic := (SELECT hora_inicial FROM sch_estudio.configuracao WHERE id_empresa = emp); ");
        sql.append("ELSE ");
        sql.append("	horainic := horainicial; ");
        sql.append("END IF; ");
        sql.append(" ");
        sql.append("IF (horafinal IS NULL) THEN ");
        sql.append("	horafi := (SELECT hora_final FROM sch_estudio.configuracao WHERE id_empresa = emp); ");
        sql.append("ELSE ");
        sql.append("	horafi := horafinal; ");
        sql.append("END IF; ");
        sql.append(" ");
        sql.append(" ");
        sql.append("FOR toprec IN ");
        sql.append("	SELECT codigo,descricao	 ");
        sql.append("		FROM ambiente  ");
        sql.append("		ORDER BY codigo LOOP ");
        sql.append(" ");
        sql.append("	FOR horarec IN ");
        sql.append("			SELECT hora.hora, hora.dia FROM ( ");
        sql.append("				SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia ");
        sql.append("					FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp,  ");
        sql.append("					(to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') as g) hora ");
        sql.append("					INNER JOIN (SELECT cast(generate_series(to_timestamp(to_char(hora_inicial, 'HH24:MI'),'HH24:MI'),to_timestamp(to_char(hora_final, 'HH24:MI'),'HH24:MI'), '1 hour') as time) AS hora_config ");
        sql.append("							FROM sch_estudio.configuracao) AS c ON c.hora_config = hora.hora  ");
        sql.append("				WHERE ((hora.hora >= horainic) AND (hora.hora < horafi)) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia  ");
        sql.append("						FROM sch_estudio.empresa_fechada indemp, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia  ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE ");
        sql.append("						indemp.id_empresa = emp AND ");
        sql.append("						indemp.dia_mes NOTNULL AND ");
        sql.append("						(CASE WHEN (indemp.dia_mes BETWEEN periodoinicial AND periodofinal) THEN ");
        sql.append("							(CASE WHEN ((indemp.hora_inicial IS NULL) AND (to_char(indemp.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indemp.hora_inicial AND gg.hora < indemp.hora_final) AND (to_char(indemp.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia  ");
        sql.append("						FROM sch_estudio.empresa_fechada indemp, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia  ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE  ");
        sql.append("						indemp.id_empresa = emp AND ");
        sql.append("						indemp.dia_semana NOTNULL AND ");
        sql.append("						(CASE WHEN (indemp.dia_semana = (extract(dow from gg.dia::date) + 1)) THEN ");
        sql.append("							(CASE WHEN ((indemp.hora_inicial IS NULL) AND (indemp.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indemp.hora_inicial AND gg.hora < indemp.hora_final) AND (indemp.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia ");
        sql.append("						FROM sch_estudio.ambiente_agenda_indisp indamb, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia  ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE ");
        sql.append("						indamb.id_ambiente = toprec.codigo AND ");
        sql.append("						indamb.dia_mes NOTNULL AND ");
        sql.append("						(CASE WHEN (indamb.dia_mes BETWEEN periodoinicial AND periodofinal) THEN ");
        sql.append("							(CASE WHEN ((indamb.hora_inicial IS NULL) AND (to_char(indamb.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indamb.hora_inicial AND gg.hora < indamb.hora_final) AND (to_char(indamb.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia ");
        sql.append("						FROM sch_estudio.ambiente_agenda_indisp indamb, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE ");
        sql.append("						indamb.id_ambiente = toprec.codigo AND ");
        sql.append("						indamb.dia_semana NOTNULL AND ");
        sql.append("						(CASE WHEN (indamb.dia_semana = (extract(dow from gg.dia::date) + 1)) THEN ");
        sql.append("							(CASE WHEN ((indamb.hora_inicial IS NULL) AND (indamb.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indamb.hora_inicial AND gg.hora < indamb.hora_final) AND (indamb.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("			ORDER BY hora.dia, hora.hora LOOP ");
        sql.append(" ");
        sql.append("	cont 		:= cont + 1; ");
        sql.append("	 ");
        sql.append("	END LOOP; ");
        sql.append(" ");
        sql.append("	totalcont := totalcont + cont; ");
        sql.append("	 ");
        sql.append("	id_ambiente	:= toprec.codigo; ");
        sql.append(" 	desc_ambiente	:= toprec.descricao; ");
        sql.append(" 	horarios	:= cont; ");
        sql.append(" 	totalhorarios 	:= totalcont;  ");
        sql.append(" ");
        sql.append("	cont		:= 0; ");
        sql.append(" ");
        sql.append("RETURN NEXT; ");
        sql.append(" ");
        sql.append("END LOOP; ");
        sql.append(" ");
        sql.append("END; ");
        sql.append("$BODY$ ");
        sql.append("  LANGUAGE plpgsql VOLATILE ");
        return sql.toString();
    }

    public static String funcaoHorarioDisponibilidadeDescST() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE OR REPLACE FUNCTION sch_estudio.fc_horarios_possiveis(IN periodoinicial date, IN periodofinal date, IN horainicial time without time zone, IN horafinal time without time zone, IN emp integer) ");
        sql.append("  RETURNS TABLE(id_ambiente integer, desc_ambiente character varying, horarios integer, totalhorarios integer) AS ");
        sql.append("$BODY$ ");
        sql.append("DECLARE ");
        sql.append(" ");
        sql.append("toprec RECORD; ");
        sql.append("horarec RECORD; ");
        sql.append("empsemanarec RECORD; ");
        sql.append("empmesrec RECORD; ");
        sql.append("horainic TIME; ");
        sql.append("horafi TIME; ");
        sql.append("cont INTEGER DEFAULT 0; ");
        sql.append("totalcont INTEGER DEFAULT 0; ");
        sql.append(" ");
        sql.append("BEGIN ");
        sql.append(" ");
        sql.append("IF (horainicial IS NULL) THEN ");
        sql.append("	horainic := (SELECT hora_inicial FROM sch_estudio.configuracao WHERE id_empresa = emp); ");
        sql.append("ELSE ");
        sql.append("	horainic := horainicial; ");
        sql.append("END IF; ");
        sql.append(" ");
        sql.append("IF (horafinal IS NULL) THEN ");
        sql.append("	horafi := (SELECT hora_final FROM sch_estudio.configuracao WHERE id_empresa = emp); ");
        sql.append("ELSE ");
        sql.append("	horafi := horafinal; ");
        sql.append("END IF; ");
        sql.append(" ");
        sql.append(" ");
        sql.append("FOR toprec IN ");
        sql.append("	SELECT codigo,descricao	 ");
        sql.append("		FROM ambiente  ");
        sql.append("		WHERE descricao LIKE '%-ST'  ");
        sql.append("		ORDER BY codigo LOOP ");
        sql.append(" ");
        sql.append("	FOR horarec IN ");
        sql.append("			SELECT hora.hora, hora.dia FROM ( ");
        sql.append("				SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia ");
        sql.append("					FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp,  ");
        sql.append("					(to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') as g) hora ");
        sql.append("					INNER JOIN (SELECT cast(generate_series(to_timestamp(to_char(hora_inicial, 'HH24:MI'),'HH24:MI'),to_timestamp(to_char(hora_final, 'HH24:MI'),'HH24:MI'), '1 hour') as time) AS hora_config ");
        sql.append("							FROM sch_estudio.configuracao) AS c ON c.hora_config = hora.hora  ");
        sql.append("				WHERE ((hora.hora >= horainic) AND (hora.hora < horafi)) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia  ");
        sql.append("						FROM sch_estudio.empresa_fechada indemp, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia  ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE ");
        sql.append("						indemp.id_empresa = emp AND ");
        sql.append("						indemp.dia_mes NOTNULL AND ");
        sql.append("						(CASE WHEN (indemp.dia_mes BETWEEN periodoinicial AND periodofinal) THEN ");
        sql.append("							(CASE WHEN ((indemp.hora_inicial IS NULL) AND (to_char(indemp.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indemp.hora_inicial AND gg.hora < indemp.hora_final) AND (to_char(indemp.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia  ");
        sql.append("						FROM sch_estudio.empresa_fechada indemp, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia  ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE  ");
        sql.append("						indemp.id_empresa = emp AND ");
        sql.append("						indemp.dia_semana NOTNULL AND ");
        sql.append("						(CASE WHEN (indemp.dia_semana = (extract(dow from gg.dia::date) + 1)) THEN ");
        sql.append("							(CASE WHEN ((indemp.hora_inicial IS NULL) AND (indemp.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indemp.hora_inicial AND gg.hora < indemp.hora_final) AND (indemp.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia ");
        sql.append("						FROM sch_estudio.ambiente_agenda_indisp indamb, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia  ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE ");
        sql.append("						indamb.id_ambiente = toprec.codigo AND ");
        sql.append("						indamb.dia_mes NOTNULL AND ");
        sql.append("						(CASE WHEN (indamb.dia_mes BETWEEN periodoinicial AND periodofinal) THEN ");
        sql.append("							(CASE WHEN ((indamb.hora_inicial IS NULL) AND (to_char(indamb.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indamb.hora_inicial AND gg.hora < indamb.hora_final) AND (to_char(indamb.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia ");
        sql.append("						FROM sch_estudio.ambiente_agenda_indisp indamb, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE ");
        sql.append("						indamb.id_ambiente = toprec.codigo AND ");
        sql.append("						indamb.dia_semana NOTNULL AND ");
        sql.append("						(CASE WHEN (indamb.dia_semana = (extract(dow from gg.dia::date) + 1)) THEN ");
        sql.append("							(CASE WHEN ((indamb.hora_inicial IS NULL) AND (indamb.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indamb.hora_inicial AND gg.hora < indamb.hora_final) AND (indamb.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("			ORDER BY hora.dia, hora.hora LOOP ");
        sql.append(" ");
        sql.append("	cont 		:= cont + 1; ");
        sql.append("	 ");
        sql.append("	END LOOP; ");
        sql.append(" ");
        sql.append("	totalcont := totalcont + cont; ");
        sql.append("	 ");
        sql.append("	id_ambiente	:= toprec.codigo; ");
        sql.append(" 	desc_ambiente	:= toprec.descricao; ");
        sql.append(" 	horarios	:= cont; ");
        sql.append(" 	totalhorarios 	:= totalcont;  ");
        sql.append(" ");
        sql.append("	cont		:= 0; ");
        sql.append(" ");
        sql.append("RETURN NEXT; ");
        sql.append(" ");
        sql.append("END LOOP; ");
        sql.append(" ");
        sql.append("END; ");
        sql.append("$BODY$ ");
        sql.append("  LANGUAGE plpgsql VOLATILE ");
        return sql.toString();
    }

     public static String funcaoHorarioDisponibilidadeDescTipoModulo() {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE OR REPLACE FUNCTION sch_estudio.fc_horarios_possiveis(IN periodoinicial date, IN periodofinal date, IN horainicial time without time zone, IN horafinal time without time zone, IN emp integer) ");
        sql.append("  RETURNS TABLE(id_ambiente integer, desc_ambiente character varying, horarios integer, totalhorarios integer) AS ");
        sql.append("$BODY$ ");
        sql.append("DECLARE ");
        sql.append(" ");
        sql.append("toprec RECORD; ");
        sql.append("horarec RECORD; ");
        sql.append("empsemanarec RECORD; ");
        sql.append("empmesrec RECORD; ");
        sql.append("horainic TIME; ");
        sql.append("horafi TIME; ");
        sql.append("cont INTEGER DEFAULT 0; ");
        sql.append("totalcont INTEGER DEFAULT 0; ");
        sql.append(" ");
        sql.append("BEGIN ");
        sql.append(" ");
        sql.append("IF (horainicial IS NULL) THEN ");
        sql.append("	horainic := (SELECT hora_inicial FROM sch_estudio.configuracao WHERE id_empresa = emp); ");
        sql.append("ELSE ");
        sql.append("	horainic := horainicial; ");
        sql.append("END IF; ");
        sql.append(" ");
        sql.append("IF (horafinal IS NULL) THEN ");
        sql.append("	horafi := (SELECT hora_final FROM sch_estudio.configuracao WHERE id_empresa = emp); ");
        sql.append("ELSE ");
        sql.append("	horafi := horafinal; ");
        sql.append("END IF; ");
        sql.append(" ");
        sql.append(" ");
        sql.append("FOR toprec IN ");
        sql.append("	SELECT codigo,descricao	 ");
        sql.append("		FROM ambiente  ");
        sql.append("		WHERE tipoModulo = 'SS' and situacao = 1  ");
        sql.append("		ORDER BY codigo LOOP ");
        sql.append(" ");
        sql.append("	FOR horarec IN ");
        sql.append("			SELECT hora.hora, hora.dia FROM ( ");
        sql.append("				SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia ");
        sql.append("					FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp,  ");
        sql.append("					(to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') as g) hora ");
        sql.append("					INNER JOIN (SELECT cast(generate_series(to_timestamp(to_char(hora_inicial, 'HH24:MI'),'HH24:MI'),to_timestamp(to_char(hora_final, 'HH24:MI'),'HH24:MI'), '1 hour') as time) AS hora_config ");
        sql.append("							FROM sch_estudio.configuracao) AS c ON c.hora_config = hora.hora  ");
        sql.append("				WHERE ((hora.hora >= horainic) AND (hora.hora < horafi)) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia  ");
        sql.append("						FROM sch_estudio.empresa_fechada indemp, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia  ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE ");
        sql.append("						indemp.id_empresa = emp AND ");
        sql.append("						indemp.dia_mes NOTNULL AND ");
        sql.append("						(CASE WHEN (indemp.dia_mes BETWEEN periodoinicial AND periodofinal) THEN ");
        sql.append("							(CASE WHEN ((indemp.hora_inicial IS NULL) AND (to_char(indemp.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indemp.hora_inicial AND gg.hora < indemp.hora_final) AND (to_char(indemp.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia  ");
        sql.append("						FROM sch_estudio.empresa_fechada indemp, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia  ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE  ");
        sql.append("						indemp.id_empresa = emp AND ");
        sql.append("						indemp.dia_semana NOTNULL AND ");
        sql.append("						(CASE WHEN (indemp.dia_semana = (extract(dow from gg.dia::date) + 1)) THEN ");
        sql.append("							(CASE WHEN ((indemp.hora_inicial IS NULL) AND (indemp.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indemp.hora_inicial AND gg.hora < indemp.hora_final) AND (indemp.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia ");
        sql.append("						FROM sch_estudio.ambiente_agenda_indisp indamb, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia  ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE ");
        sql.append("						indamb.id_ambiente = toprec.codigo AND ");
        sql.append("						indamb.dia_mes NOTNULL AND ");
        sql.append("						(CASE WHEN (indamb.dia_mes BETWEEN periodoinicial AND periodofinal) THEN ");
        sql.append("							(CASE WHEN ((indamb.hora_inicial IS NULL) AND (to_char(indamb.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indamb.hora_inicial AND gg.hora < indamb.hora_final) AND (to_char(indamb.dia_mes, 'yyyy-MM-dd') = gg.dia)) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("				AND ((hora.hora, hora.dia) NOT IN ( ");
        sql.append("					SELECT gg.hora, gg.dia ");
        sql.append("						FROM sch_estudio.ambiente_agenda_indisp indamb, ");
        sql.append("						(SELECT cast(to_char(g, 'HH24:MI') as time) AS hora, to_char(g, 'yyyy-MM-dd') AS dia ");
        sql.append("							FROM generate_series((to_char(periodoinicial, 'yyyy-MM-dd') || ' 00:00')::timestamp, (to_char(periodofinal, 'yyyy-MM-dd') || ' 23:59')::timestamp, '1 hour') AS g) AS gg  ");
        sql.append("					WHERE ");
        sql.append("						indamb.id_ambiente = toprec.codigo AND ");
        sql.append("						indamb.dia_semana NOTNULL AND ");
        sql.append("						(CASE WHEN (indamb.dia_semana = (extract(dow from gg.dia::date) + 1)) THEN ");
        sql.append("							(CASE WHEN ((indamb.hora_inicial IS NULL) AND (indamb.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE  ");
        sql.append("								(CASE WHEN ((gg.hora >= indamb.hora_inicial AND gg.hora < indamb.hora_final) AND (indamb.dia_semana = (extract(dow from gg.dia::date) + 1))) THEN TRUE ELSE FALSE END) END) END) )) ");
        sql.append("			ORDER BY hora.dia, hora.hora LOOP ");
        sql.append(" ");
        sql.append("	cont 		:= cont + 1; ");
        sql.append("	 ");
        sql.append("	END LOOP; ");
        sql.append(" ");
        sql.append("	totalcont := totalcont + cont; ");
        sql.append("	 ");
        sql.append("	id_ambiente	:= toprec.codigo; ");
        sql.append(" 	desc_ambiente	:= toprec.descricao; ");
        sql.append(" 	horarios	:= cont; ");
        sql.append(" 	totalhorarios 	:= totalcont;  ");
        sql.append(" ");
        sql.append("	cont		:= 0; ");
        sql.append(" ");
        sql.append("RETURN NEXT; ");
        sql.append(" ");
        sql.append("END LOOP; ");
        sql.append(" ");
        sql.append("END; ");
        sql.append("$BODY$ ");
        sql.append("  LANGUAGE plpgsql VOLATILE ");
        return sql.toString();
    }


}
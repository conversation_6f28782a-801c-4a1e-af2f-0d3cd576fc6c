package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Franco Alvarez",
        data = "08/03/2025",
        descricao = "Criação de campo para cancelamento automático de contrato após quantidade X de dias úteis",
        motivacao = "GC-1457")
public class AtualizacaoTicketGC1457 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN quantidadeDiasUteisAposVencimentoParaCancelarContrato INTEGER DEFAULT 0 NOT NULL;", c);
        }
    }
}

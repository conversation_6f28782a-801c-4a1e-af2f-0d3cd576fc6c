/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class CorrigirDecricaoMovContaRateio {

    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("**********************************************", "postgres", "pactodb");
            ajustarDecricaoMovContaRateio(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarDecricaoMovContaRateio(Connection con) throws Exception {
         System.out.println("\n########## ajustarDecricaoMovContaRateio  - início em : " + new Date()+" ####"+con.getCatalog()+" ###########");
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select codigo as movconta,  descricao from movconta where agendamentofinanceiro  is not null ", con);
        int cont = 1;

        while (consulta.next()) {

            String sql = "update movcontarateio  set descricao  = ? where movconta = ?;";

            PreparedStatement sqlAlterarNovo = con.prepareStatement(sql);

            sqlAlterarNovo.setString(1, consulta.getString("descricao"));
            sqlAlterarNovo.setInt(2, consulta.getInt("movconta"));
            sqlAlterarNovo.execute();
        }
         System.out.println( "ajustarDecricaoMovContaRateio - fim em : "+ new Date()+" ####"+con.getCatalog());
    }
}

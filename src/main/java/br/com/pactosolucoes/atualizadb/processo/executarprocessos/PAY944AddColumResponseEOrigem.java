package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "06/06/2025",
        descricao = "Add coluna response e origem na tabela detalhesrequestenviada",
        motivacao = "PAY-944")
public class PAY944AddColumResponseEOrigem implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE detalhesrequestenviada ADD COLUMN response TEXT, ADD COLUMN origem VARCHAR(50);",
                    c
            );
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luiz Felipe",
        data = "01/10/2024",
        descricao = "Informação de info migração empresa",
        motivacao = "Informação de info migração empresa")
public class AtualizacaoTicketInfoMigracao implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table infomigracaohistoricoempresa( \n" +
                    "codigo serial primary key, \n" +
                    "dataregistro TIMESTAMP WITHOUT TIME zone, \n" +
                    "tipoinfo int not null, \n" +
                    "tiposinfomigracaopadrao text, \n" +
                    "empresa int, \n" +
                    "padrao boolean DEFAULT FALSE);", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import controle.arquitetura.threads.ThreadMyWellnes;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;

public class sincronizarMyWellness {

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("********************************************", "postgres", "pactodb");
            String chave = "";
//            deletarVisitantes(con, chave);
            sincronizarDados(con, chave);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void  sincronizarDados(Connection con, String chave) throws SQLException {
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select c.codigo from colaborador c inner join empresa e on e.codigo = c.empresa where integracaoMyWellnessEnviarVinculos and integracaomywellnehabilitada and c.situacao  = 'AT' order by c.codigo" , con);
            while (rs.next()) {
                ThreadMyWellnes threadMyWellnes = new ThreadMyWellnes(con, chave, "CO", rs.getInt("codigo"), false);
                threadMyWellnes.run();
            }
            rs = SuperFacadeJDBC.criarConsulta("select c.codigo from cliente c inner join empresa e on c.empresa = e.codigo where e.integracaomywellnehabilitada and  c.situacao = 'AT' and not exists (select codigo from integracaomywellness  where codigoentidade = c.codigo and origem ='CL' )", con);
            while (rs.next()) {
                ThreadMyWellnes threadMyWellnes = new ThreadMyWellnes(con, chave, "CL", rs.getInt("codigo"), false, "ADD", true, true);
                threadMyWellnes.run();
                Integer limiteRequisicoesPorMinuto = 30;
                Thread.sleep(1900);
            }
            rs = SuperFacadeJDBC.criarConsulta("select codigoentidade as codigo from integracaomywellness i inner join cliente c on  c.codigo = i.codigoentidade and i.origem = 'CL' where i.codigo = (select codigo from integracaomywellness  where codigoentidade = c.codigo and origem = 'CL' order by codigo desc limit 1) and  dtsincronizacaoapi is null", con);
            while (rs.next()) {
                ThreadMyWellnes threadMyWellnes = new ThreadMyWellnes(con, chave, "CL", rs.getInt("codigo"), false, "ADD", true,true);
                threadMyWellnes.run();
                Integer limiteRequisicoesPorMinuto = 30;
                Thread.sleep(1900);
            }
            rs = SuperFacadeJDBC.criarConsulta("select codigo from cliente where pessoa in (select codigoentidade from integracaomywellness i inner join cliente c on  c.codigo = i.codigoentidade and i.origem = 'CL' where i.codigo = (select codigo from integracaomywellness  where codigoentidade = c.codigo and origem = 'CL' order by codigo desc limit 1) and  errosincronizacaoapi like '%ExternalIdAlreadyUsed%')", con);
            while (rs.next()) {
                ThreadMyWellnes threadMyWellnes = new ThreadMyWellnes(con, chave, "CL", rs.getInt("codigo"), false, "ADD", true,true);
                threadMyWellnes.run();
                Integer limiteRequisicoesPorMinuto = 30;
                Thread.sleep(1900);
            }
            rs = SuperFacadeJDBC.criarConsulta("select codigoentidade as codigo from integracaomywellness i inner join cliente c on  c.codigo = i.codigoentidade and i.origem = 'CL' where i.codigo = (select codigo from integracaomywellness  where codigoentidade = c.codigo and origem = 'CL' order by codigo desc limit 1)  and  errosincronizacaoapi like '%ExternalIdAlreadyUsed%'", con);
            while (rs.next()) {
                ThreadMyWellnes threadMyWellnes = new ThreadMyWellnes(con, chave, "CL", rs.getInt("codigo"), false, "ADD", true, true);
                threadMyWellnes.run();
                Integer limiteRequisicoesPorMinuto = 30;
                Thread.sleep(1900);
            }



        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void  deletarVisitantes(Connection con, String chave) throws SQLException {
        try {
            StringBuilder visitantes = new StringBuilder();
            visitantes .append("select distinct c.codigo from cliente c inner join  integracaomywellnessusertoken itk on itk.pessoa = c.pessoa  where situacao  = 'VI'");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(visitantes.toString(), con);
            while (rs.next()) {
                ThreadMyWellnes threadMyWellnes = new ThreadMyWellnes(con, chave, "CL", rs.getInt("codigo"), false, "DELETE");
                threadMyWellnes.run();
                Integer limiteRequisicoesPorMinuto = 30;
                Thread.sleep((limiteRequisicoesPorMinuto / 60) * 1000);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.ajustebd;

import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoModalidadeVezesSemanaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.facade.jdbc.contrato.ContratoModalidadeVezesSemana;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

/**
 * Created by GlaucoT on 06/03/2017
 */
public class AdicionarModalidadeEmPlano extends SuperEntidade {

    private ContratoModalidade contratoModalidadeDAO;
    private ContratoModalidadeVezesSemana contratoModalidadeVezesSemanaDAO;
    private Log logDAO;

    public AdicionarModalidadeEmPlano() throws Exception {
        super();

        try {
            this.contratoModalidadeDAO = new ContratoModalidade(con);
            this.contratoModalidadeVezesSemanaDAO = new ContratoModalidadeVezesSemana(con);
            this.logDAO = new Log(con);
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi possível inicializar DAOs");
        }
    }

    public AdicionarModalidadeEmPlano(Connection con) throws Exception {
        super(con);
        try {
            this.contratoModalidadeDAO = new ContratoModalidade(con);
            this.contratoModalidadeVezesSemanaDAO = new ContratoModalidadeVezesSemana(con);
            this.logDAO = new Log(con);
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi possível inicializar DAOs");
        }
    }

    public List<ContratoVO> consultarContratosAdicionarModalidade(int codEmpresa, PlanoVO planoVO, ModalidadeVO modalidadeVO, String horarioContrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct con.* \n");
        sql.append("FROM contrato con \n");
        sql.append("LEFT JOIN contratomodalidade cm ON con.codigo = cm.contrato AND modalidade = ? \n");
        if (!UteisValidacao.emptyString(horarioContrato)) {
            sql.append("LEFT JOIN contratoduracao cd ON con.codigo = cd.contrato \n");
            sql.append("LEFT JOIN contratoduracaocreditotreino cdt ON cd.codigo = cdt.contratoduracao \n");
            sql.append("LEFT JOIN contratohorario ch ON ch.contrato = con.codigo \n");
            sql.append("LEFT JOIN horario hr ON hr.codigo = ch.horario \n");
        }
        sql.append("WHERE con.plano = ? \n");
        sql.append("AND con.empresa = ? \n");
        sql.append("AND cm.codigo IS NULL \n");
        sql.append("AND con.situacao = 'AT' ");

        if (!UteisValidacao.emptyString(horarioContrato)) {
            sql.append("AND (case when con.vendacreditotreino and cdt.tipohorario = 2 THEN '"+ TipoHorarioCreditoTreinoEnum.LIVRE_OBRIGATORIO_MARCAR_AULA.getDescricao() +
                    "' else hr.descricao end) = ? \n");
        }

        PreparedStatement ps = con.prepareStatement(sql.toString());
        ps.setInt(1, modalidadeVO.getCodigo());
        ps.setInt(2, planoVO.getCodigo());
        ps.setInt(3, codEmpresa);
        if (!UteisValidacao.emptyString(horarioContrato)) {
            ps.setString(4, horarioContrato);
        }
        ResultSet rs = ps.executeQuery();

        return Contrato.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_ROBO, con);
    }


    public void adicionarModalidadeContrato(ContratoVO contratoVO, ModalidadeVO modalidadeVO, UsuarioVO usuarioVO) throws Exception {
        ContratoModalidadeVO contratoModalidadeVO = new ContratoModalidadeVO();
        contratoModalidadeVO.setNrVezesSemana(modalidadeVO.getNrVezes());
        contratoModalidadeVO.setValorModalidade(0.0);
        contratoModalidadeVO.setValorFinalModalidade(0.0);
        contratoModalidadeVO.setModalidade(modalidadeVO);
        contratoModalidadeVO.setContrato(contratoVO.getCodigo());

        contratoModalidadeDAO.incluirContratoModalidade(contratoModalidadeVO);
        adicionarContratoModalidadeVezesSemana(contratoModalidadeVO, modalidadeVO);
        incluirLog(contratoVO, modalidadeVO, usuarioVO);
    }

    public void adicionarContratoModalidadeVezesSemana(ContratoModalidadeVO contratoModalidade, ModalidadeVO modalidadeVO) throws Exception{
        ContratoModalidadeVezesSemanaVO contratoModalidadeVezesSemanaVO = new ContratoModalidadeVezesSemanaVO();
        contratoModalidadeVezesSemanaVO.setNrVezes(modalidadeVO.getNrVezes());
        contratoModalidade.setContratoModalidadeVezesSemanaVO(contratoModalidadeVezesSemanaVO);

        contratoModalidadeVezesSemanaDAO.incluirContratoModalidadeVezesSemana(contratoModalidade.getCodigo(), contratoModalidadeVezesSemanaVO);

    }

    private void incluirLog(ContratoVO contratoVO, ModalidadeVO modalidadeVO, UsuarioVO usuarioVO) throws Exception {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(contratoVO.getCodigo().toString());
        obj.setPessoa(contratoVO.getPessoa().getCodigo());
        obj.setNomeEntidade("CONTRATO");
        obj.setNomeEntidadeDescricao("Configurações");
        obj.setOperacao("Adicionar Modalidade (Processo)");
        obj.setResponsavelAlteracao(usuarioVO.getNome());
        obj.setUserOAMD(usuarioVO.getUserOamd());
        obj.setNomeCampo("Modalidades");
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado(modalidadeVO.getNome());


        logDAO.incluir(obj);
    }
}

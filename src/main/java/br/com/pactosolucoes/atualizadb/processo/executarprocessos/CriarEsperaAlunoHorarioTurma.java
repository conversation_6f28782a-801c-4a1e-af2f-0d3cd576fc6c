package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anna Carolina",
        data = "08/07/2024",
        descricao = "Cria coluna booleana de espera em AlunoHorarioTurma",
        motivacao = "Utilizar a coluna para exibir o status fila de espera")
public class CriarEsperaAlunoHorarioTurma implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE alunohorarioturma ADD COLUMN espera BOOLEAN;",
                    c
            );

            // Define o valor padrão como 'false' para todos os registros existentes
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "UPDATE alunohorarioturma SET espera = false;",
                    c
            );
        }
    }
}


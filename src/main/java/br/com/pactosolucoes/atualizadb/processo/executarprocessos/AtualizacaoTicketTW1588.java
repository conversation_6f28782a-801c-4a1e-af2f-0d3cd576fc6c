package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "14/02/2025",
        descricao = "Atualizar campo publicidmgb que foi preenchido indevidamente devido processo antigo",
        motivacao = "TW-1588 - Normalizar dados")
public class AtualizacaoTicketTW1588 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        // este processo é para corrigir o publicidmgb do aluno que foi preenchido pelo serviço antigo "updatePublicIdMgb" (não será mais utilizado)
        // mas agora tem validações para verificar se o aluno pode ou não ser sincronizado, e o serviço antigo estava inserindo o publicidmgb no aluno
        // e ao atualizar o aluno era identificado que ele não possuia um horário com nivel mgb e em seguida inativado no mgb
        // ao retirar o publicidmgb do aluno será garantido que ele não será mais inativado no mgb, em situações onde ele foi cadastrado manualmente lá

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            // consulta para obter todos alunos que não atendem a condição de sincronizar com mgb
            ResultSet rs = SuperFacadeJDBC.criarConsulta(
                    "select c.codigo \n" +
                            "from cliente c \n" +
                            "inner join pessoa p on p.codigo = c.pessoa \n" +
                            "where c.publicidmgb is not null\n" +
                            "and c.codigo not in ( \n" +
                            "   select cli.codigo \n" +
                            "   from cliente as cli \n" +
                            "   inner join  pessoa pes ON cli.pessoa = pes.codigo \n" +
                            "   inner join matriculaalunohorarioturma m on pes.codigo = m.pessoa \n" +
                            "   inner join horarioturma h2  ON h2.codigo = m.horarioturma \n" +
                            "   inner join turma t ON t.codigo = h2.turma \n" +
                            "   inner join nivelturma n on h2.nivelturma = n.codigo \n" +
                            "   where cli.codigomatricula  = c.codigomatricula \n" +
                            "   and current_date between m.datainicio and m.datafim \n" +
                            "   and n.codigo is not null and n.codigomgb <> '' and n.codigomgb <> '0' \n" +
                            ")",
                    c
            );

            String codigos = "";
            while (rs.next()) {
                codigos += "," + rs.getInt("codigo");
            }

            // Remover o publicidmgb do registro do aluno que não atende a condição de sincronização
            if (!codigos.equals("")) {
                codigos = codigos.substring(1);
                String sql = "update cliente set publicidmgb = null where codigo in (" + codigos + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        }
    }

}

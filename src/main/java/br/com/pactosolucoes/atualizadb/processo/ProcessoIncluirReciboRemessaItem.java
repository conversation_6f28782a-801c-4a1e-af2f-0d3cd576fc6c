package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.RemessaItem;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.dcc.base.RemessaService;

import java.sql.Connection;
import java.sql.DriverManager;

public class ProcessoIncluirReciboRemessaItem {

    public static void main(String[] args) {
        RemessaItem remessaItemDAO = null;
        RemessaService remessaService  = null;
        try {
            Uteis.debug = true;
            Connection con = DriverManager.getConnection("******************************************", "zillyonweb", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);
            remessaItemDAO = new RemessaItem(con);
            RemessaItemVO remessaItemVO = remessaItemDAO.consultarPorChavePrimaria(2736, Uteis.NIVELMONTARDADOS_TODOS);
            remessaService = new RemessaService(con);
            remessaService.incluirPagamento(remessaItemVO);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            remessaItemDAO = null;
            remessaService = null;
        }
    }
}


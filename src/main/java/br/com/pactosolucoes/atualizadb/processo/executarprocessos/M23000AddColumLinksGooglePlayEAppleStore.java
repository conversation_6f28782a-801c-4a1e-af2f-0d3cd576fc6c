package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "02/01/2025",
        descricao = "Add colunas dos Links Google Play e Apple Store na tabela vendasonlineconfig",
        motivacao = "M2-3000")
public class M23000AddColumLinksGooglePlayEAppleStore implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN ativarLinksGooglePlayEAppleStore Boolean DEFAULT false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN urlLinkGooglePlay VARCHAR(256);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN urlLinkAppleStore VARCHAR(256);", c);
        }
    }
}

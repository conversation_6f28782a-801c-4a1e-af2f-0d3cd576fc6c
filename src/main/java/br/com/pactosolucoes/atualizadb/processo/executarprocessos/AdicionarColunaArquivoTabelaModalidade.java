package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;


@ClasseProcesso(autor = "<PERSON> Rodrigues",
        data = "02/09/2024",
        descricao = "Adiciona a coluna 'arquivo' e a chave estrangeira 'modalidade_arquivo_fkey' na tabela 'modalidade'",
        motivacao = "Atualizar a estrutura do banco de dados para suportar a nova relacao com a tabela 'arquivo'")
public class AdicionarColunaArquivoTabelaModalidade implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE modalidade ADD COLUMN arquivo int4 NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE modalidade ADD CONSTRAINT modalidade_arquivo_fkey FOREIGN KEY (arquivo) REFERENCES arquivo(codigo);", c);
        }
    }
}

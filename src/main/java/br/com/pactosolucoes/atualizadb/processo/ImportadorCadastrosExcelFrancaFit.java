package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoTelefone;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import importador.LeitorExcel;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteObservacaoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteObservacao;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.hssf.usermodel.HSSFRow;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;


/*
 * Created by Luiz Felipe on 12/04/2017.
 */
public class ImportadorCadastrosExcelFrancaFit {

    private static Integer naoImportado = 0;

    public static void main(String[] args) throws SQLException {
        System.out.println("Início em : " + new Date());

        Connection con = DriverManager.getConnection("***********************************************************", "postgres", "pactodb");
        con.setAutoCommit(true);
        try {

            Conexao.guardarConexaoForJ2SE(con);
            SuperFacadeJDBC.executarConsultaUpdate("alter table endereco  alter column  endereco TYPE character varying(255);", con);
            importarClientes("C:\\Users\\<USER>\\Documents\\Importação\\ExcelImportacao.xls", 1, con);

        } catch (Exception e) {
            con.setAutoCommit(true);
            e.printStackTrace();
        } finally {
            con.setAutoCommit(true);
        }
        System.out.println("Total Alunos não importados: " + naoImportado);
        System.out.println("Fim em : " + new Date());
    }


    private static void importarClientes(String caminhoArquivo, Integer empresaImportar, Connection con) throws Exception {
        List<HSSFRow> linhas = LeitorExcel.lerLinhas(caminhoArquivo);

        int i = 1;
        int qtdTotal = linhas.size();
        for (HSSFRow linha : linhas) {
            System.out.println("Importando...  Atual: " + i + " Total: " + qtdTotal);
            montarExcel(linha, empresaImportar, con);
            i++;
        }
    }

    private static void montarExcel(HSSFRow linha, Integer empresaImportar, Connection con) {
        try {

            ZillyonWebFacade facade = new ZillyonWebFacade(con);
            Cliente clienteDao = new Cliente(con);
            Cidade cidadeDao = new Cidade(con);
            Empresa empresaDao = new Empresa(con);
            ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
            Colaborador colaboradorDao = new Colaborador(con);
            ClienteObservacao clienteObservacaoDao = new ClienteObservacao(con);
            Usuario usuarioDao = new Usuario(con);

            Number idExterno = LeitorExcel.obterNumero(linha, 0);
            String nome = LeitorExcel.obterString(linha, 1);
            String sexo = LeitorExcel.obterString(linha, 2);
            String cpf = LeitorExcel.obterString(linha, 3);
            Date data_nascimento = LeitorExcel.obterDataEspecifico(linha, 4);
            String endereco = LeitorExcel.obterString(linha, 5);
            String telefone = LeitorExcel.obterString(linha, 6);
            String celular = LeitorExcel.obterString(linha, 7);
            String email = LeitorExcel.obterString(linha, 8).trim();
            String fax = LeitorExcel.obterString(linha, 18);
            String whatsapp = LeitorExcel.obterString(linha, 19);
            String rg = LeitorExcel.obterString(linha, 20);

            String observacao_debido = LeitorExcel.obterString(linha, 9);
            String observacao_contratoAtivo = LeitorExcel.obterString(linha, 10);
            String observacao_vencimento = LeitorExcel.obterString(linha, 11);
            String observacao_valor = LeitorExcel.obterString(linha, 12);
            String observacao_ultimoPlano = LeitorExcel.obterString(linha, 13);

            String numero = LeitorExcel.obterString(linha, 14);
            String cep = LeitorExcel.obterString(linha, 15);
            String complemento = LeitorExcel.obterString(linha, 16);
            String bairro = LeitorExcel.obterString(linha, 17);


            StringBuilder observacao = new StringBuilder();
            if (!UteisValidacao.emptyString(observacao_debido)) {
                observacao.append("DÉBITO: ").append(observacao_debido).append(" \n");
            }
            if (!UteisValidacao.emptyString(observacao_contratoAtivo)) {
                observacao.append("CONTRATO ATIVO: ").append(observacao_contratoAtivo).append(" \n");
            }
            if (!UteisValidacao.emptyString(observacao_vencimento)) {
                observacao.append("VENCIMENTO: ").append(observacao_vencimento).append(" \n");
            }
            if (!UteisValidacao.emptyString(observacao_valor)) {
                observacao.append("VALOR: ").append(observacao_valor).append(" \n");
            }
            if (!UteisValidacao.emptyString(observacao_ultimoPlano)) {
                observacao.append("ÚLTIMO PLANO: ").append(observacao_ultimoPlano);
            }

            //CIDADE FIXO
            CidadeVO cidadeClienteVO = cidadeDao.consultarPorNomeCidadeSiglaEstado("ANAPOLIS", "GO");

            //EMPRESA
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(empresaImportar, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            //CONFIGURACAO SISTEMA
            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            //CONSULTOR
            ColaboradorVO consultor = colaboradorDao.consultarPorNomeColaborador("PACTO - M", empresaImportar, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            //USUARIO ADMIN
            UsuarioVO usuarioAdminVO = usuarioDao.consultarPorNomeUsuario("admin", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);


            //PESSOA
            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
            pessoaVO.setCategoriaPessoa(TipoPessoa.FISICA);
            pessoaVO.setNome(nome);
            pessoaVO.setSexo(sexo);
            pessoaVO.setDataCadastro(Calendario.hoje());
            pessoaVO.setDataNasc(data_nascimento);
            pessoaVO.setCidade(cidadeClienteVO);
            pessoaVO.setEstadoVO(cidadeClienteVO.getEstado());
            pessoaVO.setPais(cidadeClienteVO.getPais());
            pessoaVO.setCfp(cpf);
            pessoaVO.setRg(rg);

            //ENDERECO
            EnderecoVO enderecoVO = new EnderecoVO();
            enderecoVO.setEndereco(endereco);
            enderecoVO.setNumero(numero);
            enderecoVO.setCep(cep);
            enderecoVO.setComplemento(complemento);
            enderecoVO.setBairro(bairro);
            if (!UteisValidacao.emptyString(endereco)) {
                pessoaVO.adicionarObjEnderecoVOs(enderecoVO);
            }


            //EMAIL
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(email);
            if (!UteisValidacao.emptyString(email)) {
                pessoaVO.adicionarObjEmailVOs(emailVO);
            }


            //TELEFONE RESIDENCIAL
            TelefoneVO telefoneResiVO = new TelefoneVO();
            telefoneResiVO.setTipoTelefone(TipoTelefone.RESIDENCIAL.getCodigo());

            telefoneResiVO.setNumero(Formatador.removerMascara(telefone));
            if (!UteisValidacao.emptyString(telefone)) {
                pessoaVO.adicionarObjTelefoneVOs(telefoneResiVO);
            }

            //TELEFONE CELULAR
            TelefoneVO telefoneCelularVO = new TelefoneVO();
            telefoneCelularVO.setTipoTelefone(TipoTelefone.CELULAR.getCodigo());
            telefoneCelularVO.setNumero(Formatador.removerMascara(celular));
            if (!UteisValidacao.emptyString(celular)) {
                pessoaVO.adicionarObjTelefoneVOs(telefoneCelularVO);
            }

            TelefoneVO telefoneWhastapp = new TelefoneVO();
            telefoneWhastapp.setTipoTelefone(TipoTelefone.CELULAR.getCodigo());
            telefoneWhastapp.setNumero(Formatador.removerMascara(whatsapp));
            telefoneWhastapp.setDescricao("Whatsapp");
            if (!UteisValidacao.emptyString(whatsapp)) {
                pessoaVO.adicionarObjTelefoneVOs(telefoneWhastapp);
            }

            TelefoneVO telefoneFax = new TelefoneVO();
            telefoneFax.setTipoTelefone(TipoTelefone.COMERCIAL.getCodigo());
            telefoneFax.setNumero(Formatador.removerMascara(fax));
            telefoneFax.setDescricao("Fax");
            if (!UteisValidacao.emptyString(fax)) {
                pessoaVO.adicionarObjTelefoneVOs(telefoneFax);
            }


            //CLIENTE
            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setMatriculaExterna(idExterno.longValue());
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setEmpresa(empresaVO);
            clienteVO.setSituacao(SituacaoClienteEnum.VISITANTE.getCodigo());
            clienteVO.setMatricula(""); //APAGAR A MATRICULA PARA O SISTEMA GERAR UMA NOVA

            //CLIENTE - VINCULO
            VinculoVO vinculo = new VinculoVO();
            vinculo.setColaborador(consultor);
            vinculo.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
            vinculo.setCliente(clienteVO);
            clienteVO.getVinculoVOs().add(vinculo);

            clienteDao.gerarNumeroMatricula(clienteVO, empresaVO, configuracaoSistemaVO); //gerar a matricula do aluno
            clienteDao.incluirClienteSimplificadoImportacao(clienteVO); //incluir aluno.. pessoa... etc...
            clienteDao.atualizarMatriculaAluno(clienteVO.getCodigoMatricula()); //atualizar tabela que registra o ultimo numero de matricula
            facade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false); //criar o sintético

            //CLIENTE - OBSERVAÇÃO
            ClienteObservacaoVO clienteObservacaoVO = new ClienteObservacaoVO();
            clienteObservacaoVO.setClienteVO(clienteVO);
            clienteObservacaoVO.setDataCadastro(Calendario.hoje());
            clienteObservacaoVO.setUsuarioVO(usuarioAdminVO);
            clienteObservacaoVO.setObservacao(observacao.toString());
            clienteObservacaoDao.incluir(clienteObservacaoVO);

        } catch (Exception e) {
            naoImportado++;
            Uteis.logar(null, "Aluno não importado --- ERRO: " + e.getMessage());
        }
    }

}

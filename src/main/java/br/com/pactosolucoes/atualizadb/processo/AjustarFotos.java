/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class AjustarFotos {

    public static void main(String... args) {
        try {
            Connection conAntigo = DriverManager.getConnection("*******************************************************", "postgres", "pactodb");
            Connection conAtual = DriverManager.getConnection("****************************************************************", "postgres", "pactodb");

            transferirFotos(conAntigo, conAtual);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void transferirFotos(Connection conAntigo, Connection conAtual) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select p.codigo,p.nome from pessoa p where p.foto is not null", conAntigo);
        int cont = 0;

        while (consulta.next()) {
            cont++;
            ResultSet consultaFoto = SuperFacadeJDBC.criarConsulta("select p.foto from pessoa p where p.codigo ="+consulta.getInt("codigo")+"", conAntigo);
            consultaFoto.next();
            
            String sql = "update pessoa set foto  = ? where codigo = ?;";

            PreparedStatement sqlAlterarNovo = conAtual.prepareStatement(sql);

            sqlAlterarNovo.setBytes(1, consultaFoto.getBytes("foto"));
            sqlAlterarNovo.setInt(2, consulta.getInt("codigo"));
            sqlAlterarNovo.execute();
            System.out.println(cont+"- Pessoa teve a foto restaurada "+consulta.getInt("codigo")+" - "+consulta.getString("nome")+".");

        }
    }
}

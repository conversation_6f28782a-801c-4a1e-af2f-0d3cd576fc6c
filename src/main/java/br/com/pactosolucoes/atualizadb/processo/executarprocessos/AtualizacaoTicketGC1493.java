package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Franco Alvarez",
        data = "25/03/2025",
        descricao = "Criação de tabela de fornecedor rede empresa + Criar campo para controle de replicação + ",
        motivacao = "GC-1493")
public class AtualizacaoTicketGC1493 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        final String sqlCriarTabela = "CREATE TABLE FornecedorRedeEmpresa (\n" +
                "    codigo SERIAL PRIMARY KEY,\n" +
                "    fornecedor INTEGER NOT NULL,\n" +
                "    chave VARCHAR(255) NOT NULL,\n" +
                "    dataCadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n" +
                "    fornecedorReplicado INTEGER,\n" +
                "    nomeUnidade VARCHAR(255),\n" +
                "    mensagemSituacao VARCHAR(500),\n" +
                "    codigoEmpresaDestino INTEGER NOT NULL,\n" +
                "    dataAtualizacao TIMESTAMP\n" +
                ");";

        final String sqlCriarIndex = "CREATE INDEX idx_fornecedor_chave_empresa " +
                "ON FornecedorRedeEmpresa (fornecedor, chave, codigoEmpresaDestino);";

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaosistema ADD COLUMN permitirreplicarfornecedorredeempresa boolean NOT NULL DEFAULT false;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlCriarTabela, c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlCriarIndex, c);
        }
    }
}

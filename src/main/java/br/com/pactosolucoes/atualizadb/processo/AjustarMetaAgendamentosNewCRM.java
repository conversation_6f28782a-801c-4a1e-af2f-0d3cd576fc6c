/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.crm.AberturaMetaControle;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.crm.AberturaMetaVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.utilitarias.ThreadEnviarEmail;
import negocio.comuns.utilitarias.Uteis;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class AjustarMetaAgendamentosNewCRM {
    public static void main(String... args) {
        try {
            String nomeThread = args.length > 0 ? args[0] : "alta";
            DAO dao = new DAO();
            Connection con = dao.obterConexaoEspecifica(nomeThread);
            ajustarMetasAgendamento(con,true);
            con.close();
        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public static void ajustarMetasAgendamentoServlet(Connection con) throws Exception {
        ajustarMetasAgendamento(con, true);
    }

    public static void ajustarMetasAgendamento(Connection con,boolean guardarConexao) throws Exception {
        if(guardarConexao){
            Conexao.guardarConexaoForJ2SE(con);
        }
        String consultaMetas = "select fm.codigo from fecharmeta fm inner join aberturameta am on am.codigo = fm.aberturameta where am.dia >= '2016-03-03' and identificadormeta = 'AG'";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(consultaMetas,con);
        while (consulta.next()) {
            try{
                FecharMetaVO fm = getFacade().getFecharMeta().consultarPorChavePrimaria(consulta.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ResultSet consultaAtingida = SuperFacadeJDBC.criarConsulta("select count(fmd.codigo)::float as atingida from fecharmetadetalhado fmd inner join fecharmeta fm on fm.codigo = fmd.fecharmeta inner join agenda ag on ag.codigo = fmd.codigoorigem and fmd.origem = 'AGENDA' where fmd.fecharmeta in ("+fm.getCodigo()+") and fmd.obtevesucesso and  not fmd.repescagem and ag.tipoagendamento <> 'LI'",con );
                consultaAtingida.next();
                fm.setMetaAtingida(consultaAtingida.getDouble("atingida"));
                ResultSet consultaRepescagem = SuperFacadeJDBC.criarConsulta("select count(fmd.codigo)::float as repescagem from fecharmetadetalhado fmd inner join fecharmeta fm on fm.codigo = fmd.fecharmeta inner join agenda ag on ag.codigo = fmd.codigoorigem and fmd.origem = 'AGENDA' where fmd.fecharmeta in ("+fm.getCodigo()+") and fmd.obtevesucesso and fmd.repescagem and ag.tipoagendamento <> 'LI'",con );
                consultaRepescagem.next();
                fm.setRepescagem(consultaRepescagem.getDouble("repescagem"));
                fm.calcularPorcentagem();

                SuperFacadeJDBC.executarConsultaUpdate("update fecharmeta set porcentagem = " + fm.getPorcentagem() + " , metaatingida =" + fm.getMetaAtingida() + " , repescagem = 0   where codigo = " + fm.getCodigo(), con);
            }catch (Exception e){
                Uteis.logar(null, e.getMessage());
            }
        }
    }
}
package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Marcos Andre",
        data = "26/07/2024",
        descricao = "Adicionar as novas permissões aos perfis corretos",
        motivacao = "M1-2318")
public class AjustarNovasPermissoesM12318 {
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select distinct codperfilacesso  from permissao where nomeentidade = 'Cliente'", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.88 - Relatório de Cliente','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeCliente', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.91 - Relatório de Previsão de Renovação','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDePrevisaoRenovacao', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }

            rs = SuperFacadeJDBC.criarConsulta("select distinct codperfilacesso  from permissao where nomeentidade = 'EstornoRecibo'", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.89 - Relatório de Consulta de Recibos','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeConsultaRecibo', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }

            rs = SuperFacadeJDBC.criarConsulta("select distinct codperfilacesso  from permissao where nomeentidade = 'GestaoArmario'", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.90 - Relatório de Armários','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeArmarios', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }

            rs = SuperFacadeJDBC.criarConsulta("select distinct codperfilacesso  from permissao where nomeentidade = 'FaturamentoSinteticoRel'", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.92 - Relatório de Faturamento Recebido Por Período','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeFaturamentoRecebidoPeríodo', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }

            rs = SuperFacadeJDBC.criarConsulta("select distinct codperfilacesso  from permissao where nomeentidade = 'MovimentoContaCorrenteCliente'", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.93 - Relatório de Movimentação de Conta Corrente do Cliente','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeMovimentacaoContaCorrenteCliente', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }

            rs = SuperFacadeJDBC.criarConsulta("select distinct codperfilacesso  from permissao where nomeentidade = 'ParcelaEmAbertoRel'", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.94 - Relatório de Produtos com Vigência','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeProdutosComVigencia', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }

            rs = SuperFacadeJDBC.criarConsulta("select distinct codperfilacesso  from permissao where nomeentidade = 'RelatorioClientes'", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.95 - Relatório de Clientes Cancelados','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeClientesCancelados', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }

            rs = SuperFacadeJDBC.criarConsulta("select distinct codperfilacesso  from permissao where nomeentidade = 'TotalizadorFrequenciaRel'", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.96 - Relatório de Totalizador de Acessos','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeTotalizadorDeAcessos', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }

            rs = SuperFacadeJDBC.criarConsulta("select codigo as  codperfilacesso  from perfilacesso where tipo = 1", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.97 - Permitir Imprimir Recibo em Branco','(0)(1)(2)(3)(9)(12)', "
                        + " 'PermitirImprimirReciboEmBranco', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.98 - Permitir Exportar Dados','(0)(1)(2)(3)(9)(12)', "
                        + " 'PermitirExportarDados', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);


            }
        }

    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON> da <PERSON>",
        data = "30/07/2025",
        descricao = "MJ-1401 - Permissão exclusão de clientes",
        motivacao = "MJ-1401 - Permissão exclusão de clientes com tipo errado")
public class AtualizacaoTicketMJ1401 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE permissao SET tipopermissao = 2 WHERE tituloapresentacao = '9.69 - Permitir excluir cliente da base de dados';", c);
        }
    }
}

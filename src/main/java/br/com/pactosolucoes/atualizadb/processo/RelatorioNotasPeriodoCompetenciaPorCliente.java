package br.com.pactosolucoes.atualizadb.processo;

import edu.emory.mathcs.backport.java.util.Collections;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;

import java.io.File;
import java.io.FileOutputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RelatorioNotasPeriodoCompetenciaPorCliente {

    private List<String> colunasCriar;
    private HSSFSheet sheet;

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("****************************************************************", "postgres", "pactodb");

            RelatorioNotasPeriodoCompetenciaPorCliente relatorioNotasPeriodoCompetenciaPorCliente = new RelatorioNotasPeriodoCompetenciaPorCliente();
            relatorioNotasPeriodoCompetenciaPorCliente.gerarRelatorio(con, 1);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void criarCelula(int numeroCelula, Row row, Object valor) {
        Cell cell = row.createCell(numeroCelula);
        setValorCelula(cell, valor);
    }

    private void criarCelula(int numeroCelula, Row row, Object valor, CellStyle cellStyle) {
        Cell cell = row.createCell(numeroCelula);
        setValorCelula(cell, valor);
        cell.setCellStyle(cellStyle);
    }

    private void setValorCelula(Cell cell, Object valor) {
        if (valor instanceof String) {
            cell.setCellType(CellType.STRING);
            cell.setCellValue((String) valor);
        } else if (valor instanceof Integer) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Integer) valor);
        } else if (valor instanceof Double) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue(Uteis.arredondarForcando2CasasDecimais((Double) valor));
        } else if (valor instanceof Date) {
            cell.setCellValue((Date) valor);
        } else if (valor instanceof Boolean) {
            cell.setCellType(CellType.BOOLEAN);
            cell.setCellValue((Boolean) valor);
        } else {
            cell.setCellValue(valor == null ? "" : valor.toString());
        }
    }

    private void cabecalho(HSSFWorkbook hssfWorkbook, Row row, int cellnum, String textoCelula) {
        CellStyle cellStyle = hssfWorkbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        Cell cell = row.createCell(cellnum);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(textoCelula);
    }

    private HSSFWorkbook criarTabela(List<String> colunas) {
        List<String> colunasAdicionar = new ArrayList<>();
        for (String coluna : colunas) {
            colunasAdicionar.add(coluna.split("#")[1]);
        }

        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        sheet = hssfWorkbook.createSheet("EmissaoNotas");

        int cellnum = 0;
        Row row;

        // Definindo alguns padroes de layout
        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeight((short) 300);

        row = sheet.createRow(0);
        colunasCriar = new ArrayList<>();
        colunasCriar.add("Matricula");
        colunasCriar.add("Nome");
        colunasCriar.add("CPF");
        colunasCriar.add("Início Plano");
        colunasCriar.add("Final Plano");
        colunasCriar.addAll(colunasAdicionar);
        colunasCriar.add("Valor emitido");
        colunasCriar.add("Qd emitir");
        colunasCriar.add("Valor emitir");
        colunasCriar.add("Valor total(plano)");

        for (String coluna : colunasCriar) {
            cabecalho(hssfWorkbook, row, cellnum++, coluna);
        }

        return hssfWorkbook;
    }

    public void gerarRelatorio(Connection con, Integer empresa) {
        String sql = "SELECT cli.matricula,\n" +
                "   pes.nome as nomepessoa,\n" +
                "   pes.cfp as cpf,\n" +
                "   n.codigo as codigoNota,\n" +
                "   mprod.anoreferencia,\n" +
                "   mprod.mesreferencia,\n" +
                "   mprod.totalfinal,\n" +
                "   c.valorfinal,\n" +
                "   c.vigenciade,\n" +
                "   c.vigenciaateajustada,\n" +
                "   exists(SELECT mpp.movproduto FROM movprodutoparcela mpp \n" +
                "       INNER JOIN pagamentomovparcela pmp ON mpp.recibopagamento = pmp.recibopagamento\n" +
                "       INNER JOIN nfseemitida n2 ON n2.movpagamento = pmp.movpagamento\n" +
                "       WHERE mpp.movproduto = mprod.codigo AND n2.nrnotamanual <> '') as notamanual\n" +
                "FROM movproduto mprod\n" +
                "         INNER JOIN produto prod ON mprod.produto = prod.codigo\n" +
                "         INNER JOIN contrato c ON mprod.contrato = c.codigo\n" +
                "         INNER JOIN pessoa pes ON mprod.pessoa = pes.codigo\n" +
                "         INNER JOIN cliente cli ON cli.pessoa = pes.codigo\n" +
                "         LEFT JOIN nfseemitida n on mprod.codigo = n.movproduto\n" +
                "WHERE anoreferencia >= 2019\n" +
                "  AND c.vigenciade >= '2019-10-17'\n" +
                "  AND prod.tipoproduto = 'PM'\n" +
                "  AND mprod.contrato > 0\n" +
                "  AND mprod.situacao IN ('EA', 'PG')\n" +
                "  AND mprod.totalfinal > 0\n" +
                "order by matricula, mesreferencia";

        Map<String, ObjetoNota> notas = new HashMap<>();
        List<String> meses = new ArrayList<>();
        try (ResultSet rsPeriodo = SuperFacadeJDBC.criarConsulta(sql, con)) {
            while (rsPeriodo.next()) {
                String matricula = rsPeriodo.getString("matricula");
                String nomepessoa = rsPeriodo.getString("nomepessoa");
                Date vigenciaDe = rsPeriodo.getDate("vigenciade");
                Date vigenciaAte = rsPeriodo.getDate("vigenciaateajustada");
                ObjetoNota objetoNota = notas.get(matricula);
                if (objetoNota == null) {
                    String cpf = rsPeriodo.getString("cpf");
                    Double valorContrato = rsPeriodo.getDouble("valorfinal");

                    objetoNota = new ObjetoNota();
                    objetoNota.matricula = matricula;
                    objetoNota.nome = nomepessoa;
                    objetoNota.cpf = cpf;
                    objetoNota.vigenciaDe = Calendario.getDataAplicandoFormatacao(vigenciaDe, "dd/MM/yyyy");
                    objetoNota.vigenciaAte = Calendario.getDataAplicandoFormatacao(vigenciaAte, "dd/MM/yyyy");
                    objetoNota.valorContrato = valorContrato;
                    objetoNota.itens = new HashMap<>();
                    notas.put(matricula, objetoNota);
                }
                Integer codigoNota = rsPeriodo.getInt("codigoNota");
                boolean notaManual = rsPeriodo.getBoolean("notamanual");
                String mesReferencia = rsPeriodo.getString("mesreferencia");
                String anoReferencia = rsPeriodo.getString("anoreferencia");
                String referencia = anoReferencia + "#" + mesReferencia;
                if (!meses.contains(referencia)) {
                    meses.add(referencia);
                }
                Double valorMesReferencia = rsPeriodo.getDouble("totalfinal");

                ItemObjetoNota itemObjetoNota = new ItemObjetoNota();
                itemObjetoNota.valorProduto = valorMesReferencia;
                if (UteisValidacao.emptyNumber(codigoNota) && !notaManual) {
                    itemObjetoNota.notaEmitida = false;
                    objetoNota.valorEmitir += valorMesReferencia;
                    objetoNota.qtdEmitir++;
                } else {
                    itemObjetoNota.notaEmitida = true;
                    objetoNota.valorEmitido += valorMesReferencia;
                }
                objetoNota.itens.put(mesReferencia, itemObjetoNota);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        Collections.sort(meses);
        HSSFWorkbook hssfWorkbook = criarTabela(meses);

        List<String> matriculas = new ArrayList<>(notas.keySet());
        Collections.sort(matriculas);

        CellStyle cellStyleEmitida = hssfWorkbook.createCellStyle();
        HSSFFont fontEmitida = hssfWorkbook.createFont();
        fontEmitida.setColor(HSSFColor.HSSFColorPredefined.GREEN.getIndex());
        fontEmitida.setBold(true);
        cellStyleEmitida.setFont(fontEmitida);

        CellStyle cellStyleEmitir = hssfWorkbook.createCellStyle();
        HSSFFont fontEmitir = hssfWorkbook.createFont();
        fontEmitir.setColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
        fontEmitir.setBold(true);
        cellStyleEmitir.setFont(fontEmitir);


        int rowNumber = 0;
        for (String matricula : matriculas) {
            ObjetoNota objetoNota = notas.get(matricula);

            int cellNumber = -1;
            Map<String, ItemObjetoNota> itensNotas = objetoNota.itens;
            Row row = hssfWorkbook.getSheetAt(0).createRow(++rowNumber);
            criarCelula(++cellNumber, row, objetoNota.matricula);
            criarCelula(++cellNumber, row, objetoNota.nome);
            criarCelula(++cellNumber, row, objetoNota.cpf);
            criarCelula(++cellNumber, row, objetoNota.vigenciaDe);
            criarCelula(++cellNumber, row, objetoNota.vigenciaAte);
            for (String mesReferencia : meses) {
                String mesRef = mesReferencia.split("#")[1];
                ItemObjetoNota itemObjetoNota = itensNotas.get(mesRef);
                if (itemObjetoNota != null) {
                    if (itemObjetoNota.notaEmitida) {
                        criarCelula(++cellNumber, row, itemObjetoNota.valorProduto, cellStyleEmitida);
                    } else {
                        criarCelula(++cellNumber, row, itemObjetoNota.valorProduto, cellStyleEmitir);
                    }
                } else {
                    criarCelula(++cellNumber, row, "");
                }
            }
            criarCelula(++cellNumber, row, objetoNota.valorEmitido, cellStyleEmitida);
            criarCelula(++cellNumber, row, objetoNota.qtdEmitir, cellStyleEmitir);
            criarCelula(++cellNumber, row, objetoNota.valorEmitir, cellStyleEmitir);
            criarCelula(++cellNumber, row, objetoNota.valorContrato);
        }


        for (int i = 0; i < colunasCriar.size(); i++) {
            sheet.autoSizeColumn(i);
        }

        try {
            File file = new File("/opt/NotasEmitidas-" + Calendario.hoje().getTime() + ".xls");
            FileOutputStream out = new FileOutputStream(file);
            hssfWorkbook.write(out);
            out.close();
            hssfWorkbook.close();
        } catch (Exception ignored) {

        }
    }

    class ObjetoNota {
        String matricula;
        String nome;
        String cpf;
        Map<String, ItemObjetoNota> itens;
        int qtdEmitir = 0;
        double valorEmitir = 0.0;
        double valorEmitido = 0.0;
        double valorContrato = 0.0;
        String vigenciaDe;
        String vigenciaAte;
    }

    class ItemObjetoNota {
        boolean notaEmitida = false;
        double valorProduto = 0.0;
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "01/04/2025",
        descricao = "Adicionar coluna produto na tabela PeriodoAcessoCliente",
        motivacao = "M1-4259")
public class AtualizacaoTicketM14259 implements MigracaoVersaoInterface {
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE periodoacessocliente ADD COLUMN produto INT DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE periodoacessocliente ADD CONSTRAINT fk_periodoacessocliente_produto FOREIGN KEY (produto) REFERENCES produto(codigo);", c);
        }
    }
}

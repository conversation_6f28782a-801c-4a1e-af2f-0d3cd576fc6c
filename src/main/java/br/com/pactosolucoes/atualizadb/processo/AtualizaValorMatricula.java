/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.HistoricoContrato;

/**
 *
 * <AUTHOR>
 */
public class AtualizaValorMatricula {

    private static  Double guara = 0.0;
    private Double sul = 0.0;
    private Double norte = 0.0;
    private Double qnl = 0.0;
    private Double ceilandia = 0.0;
    private Double totalGeral = 0.0;

    public Double getTotalGeral() {
        return totalGeral;
    }

    public void setTotalGeral(Double totalGeral) {
        this.totalGeral = totalGeral;
    }



    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("****************************************************", "postgres", "pactodb");
           AtualizaValorMatricula atualiza = new AtualizaValorMatricula();
           atualiza.atualizaValorMatriculaContrato(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void atualizaValorMatriculaContrato(Connection con) throws Exception {
        File matriculaSemOperacao = new File("D:/matriculaSemOperacao.txt");
        FileWriter writer = new FileWriter(matriculaSemOperacao, true);
        PrintWriter saida = new PrintWriter(writer);

        saida.println("atualizar Matriculas- início em : " + new Date());
        ResultSet consultaProduto = SuperFacadeJDBC.criarConsulta("select p.codigo as produto, p.tipoproduto  from produto  p "
                + "where  p.tipoproduto = 'MA' OR p.tipoproduto = 'RE'", con);
        int prodMatricula = 0;
        int prodRematricula = 0;
        while (consultaProduto.next()) {
            if (consultaProduto.getString("tipoproduto").equals("MA")) {
                prodMatricula = consultaProduto.getInt("produto");
            }
            if (consultaProduto.getString("tipoproduto").equals("RE")) {
                prodRematricula = consultaProduto.getInt("produto");
            }
        }

        int geral = 0;

        saida.println("################### CONTRATOS DE MATRICULA SEM OPERAÇÕES ####################");
        ResultSet consultaContrato = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato,c.empresa from contrato  c "
                + "where  c.situacaocontrato = 'MA' and codigo not in (select contrato from contratooperacao  where tipooperacao in('TR', 'AH','MM','CA'))", con);
        int count = 0;
        while (consultaContrato.next()) {
            ResultSet consultaParcela = SuperFacadeJDBC.criarConsulta("select sum(valorparcela) as valorParcela from movparcela where contrato = " + consultaContrato.getInt("contrato"), con);
            consultaParcela.next();
            Double valorParcela = consultaParcela.getDouble("valorParcela");
            ResultSet consultaProdutos = SuperFacadeJDBC.criarConsulta("select sum(totalfinal) as valorProduto from movproduto where contrato = " + consultaContrato.getInt("contrato") + " and produto <> 9", con);
            consultaProdutos.next();
            Double valorProdutos = consultaProdutos.getDouble("valorProduto");
            if (Uteis.arredondarForcando2CasasDecimais(valorParcela) > Uteis.arredondarForcando2CasasDecimais(valorProdutos)) {
                Double diferenca = Uteis.arredondarForcando2CasasDecimais(valorParcela - valorProdutos);
                if (diferenca.doubleValue() > 1.0) {
                    count++;
                    geral++;
                    saida.println(count + "# Contrato = " + consultaContrato.getInt("contrato") + ", parcelas = " + valorParcela.toString() + ", produtos = "
                            + valorProdutos + ", valor sugerido matricula = " + diferenca);

//                    Double desconto = 200 - diferenca;
//                    String sql = "UPDATE movproduto set "
//                    + " totalFinal=?, precounitario=200, valordesconto = ?"
//                    + " WHERE contrato = ? and produto = ?";
//                    PreparedStatement sqlAlterar = con.prepareStatement(sql);
//
//                    sqlAlterar.setDouble(1, diferenca);
//                    sqlAlterar.setDouble(2, desconto);
//                    sqlAlterar.setInt(3, consultaContrato.getInt("contrato"));
//                    sqlAlterar.setInt(4, prodMatricula);
//                    sqlAlterar.execute();
//
//                     String sql1 = "UPDATE movprodutoparcela set "
//                    + " valorpago=?"
//                    + " WHERE  movproduto in (select codigo from movproduto where  contrato = ? and produto = ?)";
//                    PreparedStatement sqlAlterar1 = con.prepareStatement(sql1);
//
//                    sqlAlterar1.setDouble(1, diferenca);
//                    sqlAlterar1.setInt(2, consultaContrato.getInt("contrato"));
//                    sqlAlterar1.setInt(3, prodMatricula);
//                    sqlAlterar1.execute();
                    somarDiferencas(diferenca, consultaContrato.getInt("empresa"));
                }

            }
        }
        count = 0;
        saida.println("\n\n################### CONTRATOS DE MATRICULA COM OPERAÇÕES ####################");
        consultaContrato = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato,c.empresa from contrato  c "
                + "where  c.situacaocontrato = 'MA' and codigo in (select contrato from contratooperacao  where tipooperacao in('TR', 'AH','MM','CA'))", con);
        while (consultaContrato.next()) {
            ResultSet consultaParcela = SuperFacadeJDBC.criarConsulta("select sum(valorparcela) as valorParcela from movparcela where contrato = " + consultaContrato.getInt("contrato"), con);
            consultaParcela.next();
            Double valorParcela = consultaParcela.getDouble("valorParcela");
            ResultSet consultaProdutos = SuperFacadeJDBC.criarConsulta("select sum(totalfinal) as valorProduto from movproduto where contrato = " + consultaContrato.getInt("contrato") + " and produto <> 9", con);
            consultaProdutos.next();
            Double valorProdutos = consultaProdutos.getDouble("valorProduto");
            if (Uteis.arredondarForcando2CasasDecimais(valorParcela) > Uteis.arredondarForcando2CasasDecimais(valorProdutos)) {
                Double diferenca = Uteis.arredondarForcando2CasasDecimais(valorParcela - valorProdutos);
                if (diferenca.doubleValue() > 1.0) {
                    count++;
                    geral++;
                    saida.println(count + "# Contrato = " + consultaContrato.getInt("contrato") + ", parcelas = " + valorParcela.toString() + ", produtos = "
                            + valorProdutos + ", valor sugerido matricula = " + diferenca);
                     Double desconto = 200 - diferenca;
//                    String sql = "UPDATE movproduto set "
//                    + " totalFinal=?, precounitario=200, valordesconto = ?"
//                    + " WHERE contrato = ? and produto = ?";
//                    PreparedStatement sqlAlterar = con.prepareStatement(sql);
//
//                    sqlAlterar.setDouble(1, diferenca);
//                    sqlAlterar.setDouble(2, desconto);
//                    sqlAlterar.setInt(3, consultaContrato.getInt("contrato"));
//                    sqlAlterar.setInt(4, prodMatricula);
//                    sqlAlterar.execute();
//
//                     String sql1 = "UPDATE movprodutoparcela set "
//                    + " valorpago=?"
//                    + " WHERE  movproduto in (select codigo from movproduto where  contrato = ? and produto = ?)";
//                    PreparedStatement sqlAlterar1 = con.prepareStatement(sql1);
//
//                    sqlAlterar1.setDouble(1, diferenca);
//                    sqlAlterar1.setInt(2, consultaContrato.getInt("contrato"));
//                    sqlAlterar1.setInt(3, prodMatricula);
//                    sqlAlterar1.execute();
                    somarDiferencas(diferenca, consultaContrato.getInt("empresa"));
                }

            }
        }
        count = 0;
        saida.println("\n\n################### CONTRATOS DE REMATRICULA SEM OPERAÇÕES ####################");
        consultaContrato = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato,c.empresa from contrato  c "
                + "where  c.situacaocontrato = 'RE' and codigo not in (select contrato from contratooperacao  where tipooperacao in('TR', 'AH','MM','CA'))", con);
        while (consultaContrato.next()) {
            ResultSet consultaParcela = SuperFacadeJDBC.criarConsulta("select sum(valorparcela) as valorParcela from movparcela where contrato = " + consultaContrato.getInt("contrato"), con);
            consultaParcela.next();
            Double valorParcela = consultaParcela.getDouble("valorParcela");
            ResultSet consultaProdutos = SuperFacadeJDBC.criarConsulta("select sum(totalfinal) as valorProduto from movproduto where contrato = " + consultaContrato.getInt("contrato") + " and produto <> 9", con);
            consultaProdutos.next();
            Double valorProdutos = consultaProdutos.getDouble("valorProduto");
            if (Uteis.arredondarForcando2CasasDecimais(valorParcela) > Uteis.arredondarForcando2CasasDecimais(valorProdutos)) {
                Double diferenca = Uteis.arredondarForcando2CasasDecimais(valorParcela - valorProdutos);
                if (diferenca.doubleValue() > 1.0) {
                    count++;
                    geral++;
                    saida.println(count + "# Contrato = " + consultaContrato.getInt("contrato") + ", parcelas = " + valorParcela.toString() + ", produtos = "
                            + valorProdutos + ", valor sugerido matricula = " + diferenca);
                    Double desconto = 200 - diferenca;
//                    String sql = "UPDATE movproduto set "
//                    + " totalFinal=?, precounitario=200, valordesconto = ?"
//                    + " WHERE contrato = ? and produto = ?";
//                    PreparedStatement sqlAlterar = con.prepareStatement(sql);
//
//                    sqlAlterar.setDouble(1, diferenca);
//                    sqlAlterar.setDouble(2, desconto);
//                    sqlAlterar.setInt(3, consultaContrato.getInt("contrato"));
//                    sqlAlterar.setInt(4, prodMatricula);
//                    sqlAlterar.execute();
//
//                     String sql1 = "UPDATE movprodutoparcela set "
//                    + " valorpago=?"
//                    + " WHERE  movproduto in (select codigo from movproduto where  contrato = ? and produto = ?)";
//                    PreparedStatement sqlAlterar1 = con.prepareStatement(sql1);
//
//                    sqlAlterar1.setDouble(1, diferenca);
//                    sqlAlterar1.setInt(2, consultaContrato.getInt("contrato"));
//                    sqlAlterar1.setInt(3, prodMatricula);
//                    sqlAlterar1.execute();
                    somarDiferencas(diferenca, consultaContrato.getInt("empresa"));
                }

            }
        }
        count = 0;
        saida.println("\n\n################### CONTRATOS DE REMATRICULA COM OPERAÇÕES ####################");
        consultaContrato = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato,c.empresa from contrato  c "
                + "where  c.situacaocontrato = 'RE' and codigo in (select contrato from contratooperacao  where tipooperacao in('TR', 'AH','MM','CA'))", con);
        while (consultaContrato.next()) {
            ResultSet consultaParcela = SuperFacadeJDBC.criarConsulta("select sum(valorparcela) as valorParcela from movparcela where contrato = " + consultaContrato.getInt("contrato"), con);
            consultaParcela.next();
            Double valorParcela = consultaParcela.getDouble("valorParcela");
            ResultSet consultaProdutos = SuperFacadeJDBC.criarConsulta("select sum(totalfinal) as valorProduto from movproduto where contrato = " + consultaContrato.getInt("contrato") + " and produto <> 9", con);
            consultaProdutos.next();
            Double valorProdutos = consultaProdutos.getDouble("valorProduto");
            if (Uteis.arredondarForcando2CasasDecimais(valorParcela) > Uteis.arredondarForcando2CasasDecimais(valorProdutos)) {
                Double diferenca = Uteis.arredondarForcando2CasasDecimais(valorParcela - valorProdutos);
                if (diferenca.doubleValue() > 1.0) {
                    count++;
                    geral++;
                    saida.println(count + "# Contrato = " + consultaContrato.getInt("contrato") + ", parcelas = " + valorParcela.toString() + ", produtos = "
                            + valorProdutos + ", valor sugerido matricula = " + diferenca);

                     Double desconto = 200 - diferenca;
//                    String sql = "UPDATE movproduto set "
//                    + " totalFinal=?, precounitario=200, valordesconto = ?"
//                    + " WHERE contrato = ? and produto = ?";
//                    PreparedStatement sqlAlterar = con.prepareStatement(sql);
//
//                    sqlAlterar.setDouble(1, diferenca);
//                    sqlAlterar.setDouble(2, desconto);
//                    sqlAlterar.setInt(3, consultaContrato.getInt("contrato"));
//                    sqlAlterar.setInt(4, prodMatricula);
//                    sqlAlterar.execute();
//
//                     String sql1 = "UPDATE movprodutoparcela set "
//                    + " valorpago=?"
//                    + " WHERE  movproduto in (select codigo from movproduto where  contrato = ? and produto = ?)";
//                    PreparedStatement sqlAlterar1 = con.prepareStatement(sql1);
//
//                    sqlAlterar1.setDouble(1, diferenca);
//                    sqlAlterar1.setInt(2, consultaContrato.getInt("contrato"));
//                    sqlAlterar1.setInt(3, prodMatricula);
//                    sqlAlterar1.execute();
                    somarDiferencas(diferenca, consultaContrato.getInt("empresa"));
                }

            }
        }

        saida.println("\n\n################### Total de contrato = " + geral + " ####################");
        saida.println("\n\n################### Valores ####################");
         saida.println("Total Geral: R$ "+getTotalGeral());
         saida.println("Guara: R$ "+getGuara());
         saida.println("norte: R$ "+getNorte());
         saida.println("sul: R$ "+getSul());
         saida.println("qnl: R$ "+getQnl());
         saida.println("ceilandia: R$"+getCeilandia());
        saida.println("atualizar Matriculas- fim em : " + new Date());
        saida.close();
        writer.close();
    }

    private  void somarDiferencas(Double diferenca, int empresa) {
        setTotalGeral(getTotalGeral()+diferenca);
        switch(empresa){
            case 1:
                setGuara(getGuara() + diferenca);
                break;
            case 3:
                setNorte(getNorte() + diferenca);
                break;
            case 4:
                setQnl(getQnl() + diferenca);
                break;
            case 5:
                setSul(getSul() + diferenca);
                break;
            case 6:
                setCeilandia(getCeilandia() + diferenca);
                break;
        }
    }

    public Double getCeilandia() {
        return ceilandia;
    }

    public void setCeilandia(Double ceilandia) {
        this.ceilandia = ceilandia;
    }

    public Double getGuara() {
        return guara;
    }

    public void setGuara(Double guara) {
        this.guara = guara;
    }

    public Double getNorte() {
        return norte;
    }

    public void setNorte(Double norte) {
        this.norte = norte;
    }

    public Double getQnl() {
        return qnl;
    }

    public void setQnl(Double qnl) {
        this.qnl = qnl;
    }

    public Double getSul() {
        return sul;
    }

    public void setSul(Double sul) {
        this.sul = sul;
    }
}

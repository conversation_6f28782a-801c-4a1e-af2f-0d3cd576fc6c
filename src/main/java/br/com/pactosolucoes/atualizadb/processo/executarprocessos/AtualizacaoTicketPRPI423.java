package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luis Antônio de Melo Gomes",
        data = "16/01/2025",
        descricao = "Atualizar tabela de configuração de IA do modulo ADM",
        motivacao = "PRPI-423")
public class AtualizacaoTicketPRPI423 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaocrmia ADD COLUMN codigoempresa integer NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofaseia ADD COLUMN codigoempresa integer NULL;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "29/07/2024",
        descricao = "coluna fornecedor na pessoa",
        motivacao = "GC-746")
public class AtualizacaoTicketGC746 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE CATEGORIA ADD COLUMN validarSituacaoEmpresaSesi boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN nomeFantasia varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN razaoSocial varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN dataCadastro DATE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN dataValidade DATE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN cnae varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN codigoFpas varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN grauRiscoNr4 integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN grauRiscoInss integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN sindicato varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN nrTotalFuncionarios integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN porteEmpresa integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN documento varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE FORNECEDOR ADD COLUMN documentoExtensao varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE EMPRESA ADD COLUMN habilitarCadastroEmpresaSesi boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE CLIENTE ADD COLUMN empresaFornecedor integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE CLIENTE ADD CONSTRAINT fk_cliente_empresaForncedor " +
                    "FOREIGN KEY (empresaForncedor) REFERENCES fornecedor (codigo);", c);
        }
    }
}

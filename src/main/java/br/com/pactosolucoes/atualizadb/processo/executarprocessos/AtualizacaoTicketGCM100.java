package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Vitor Junio",
        data = "19/03/2025",
        descricao = "Adicionar coluna na tabela plano para nova feature de transferência de crédito.",
        motivacao = "GCM-100")
public class AtualizacaoTicketGCM100 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE plano " +
                            "ADD COLUMN permitirtransferenciadecredito BOOLEAN DEFAULT FALSE, " +
                            "ADD COLUMN quantidadeatransferirpermitidaporaluno INTEGER;",
                    c
            );
        }
    }
}

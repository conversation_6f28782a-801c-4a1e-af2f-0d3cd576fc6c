package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Joao Alcides",
        data = "26/06/2024",
        descricao = "Cria tabela processos migracao",
        motivacao = "Mudar a forma com que o sistema atualiza os bancos de dados")
public class CriarTabelaProcessosMigracao implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE processosmigracao (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    script TEXT NOT NULL,\n" +
                    "    versaoaplicacao varchar(50),\n" +
                    "    dataprocesso TIMESTAMP NOT NULL\n" +
                    ");", c);
        }
    }
}

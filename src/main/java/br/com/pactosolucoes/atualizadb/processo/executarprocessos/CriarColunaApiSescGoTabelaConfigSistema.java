package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "18/09/2024",
        descricao = "Cria coluna 'apiSescGo', 'usuarioApiSescGo' e 'senhaApiSescGo' na tabela ConfiguracaoSistema",
        motivacao = "GC-897 [Customização] Nova API de Integração do SCA com o SESC/GO")
public class CriarColunaApiSescGoTabelaConfigSistema implements MigracaoVersaoInterface{
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE ConfiguracaoSistema " +
                            "ADD COLUMN apiSescGo BOOLEAN DEFAULT FALSE, " +
                            "ADD COLUMN usuarioApiSescGo VARCHAR(255), " +
                            "ADD COLUMN senhaApiSescGo VARCHAR(255)",
                    c
            );
        }
    }
}

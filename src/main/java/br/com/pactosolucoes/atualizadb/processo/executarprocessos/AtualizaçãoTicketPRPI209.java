package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luís Antônio de Melo Gomes",
        data = "08/10/2024",
        descricao = "PRPI-209 Atualizar configuracao CRM IA",
        motivacao = "PRPI-209")
public class AtualizaçãoTicketPRPI209 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaocrmia ADD COLUMN horarioPadrao TIME ;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;

/**
 * Created by Rafael on 08/10/2015.
 */
public class PreeencheVinculoClienteSintetico {


    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("****************************************************", "zillyonweb", "pactodb");
            corrigirVinculos(con);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    private static void corrigirVinculos(Connection con) throws Exception {
        String sql = "Select codigoCliente from situacaoclientesinteticodw ";
        ResultSet query = con.prepareStatement(sql).executeQuery();
        ZillyonWebFacade zwFac = new ZillyonWebFacade(con);
        Cliente cliFac = new Cliente(con);
        while (query.next()){
            int codigoCliente = query.getInt("codigoCliente");
            if(codigoCliente > 0) {
                ClienteVO cli = cliFac.consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
                zwFac.atualizarSintetico(cli,
                        Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_VINCULO, false);
            }
        }
    }
}

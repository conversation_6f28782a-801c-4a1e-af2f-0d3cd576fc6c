package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "19/09/2024",
        descricao = "Criar coluna para permitir renovar com ou sem desconto o contrato pelo totem",
        motivacao = "M1-2793")
public class AtualizacaoTicketM12793 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN renovarComDescontoTotem BOOLEAN DEFAULT FALSE", c);
        }
    }

}

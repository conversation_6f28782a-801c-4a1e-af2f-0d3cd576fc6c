package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

public class CorrigirSaidasValores {
	
	public static void main(String[] args) {
		try {
			Connection con = DriverManager.getConnection("***************************************", "zillyonweb", "pactodb");
//			consultar(con);
//			corrigirMovimentacoes(con);
			verificarComposicaoReferenciaErrada("cartaocredito",con);
			verificarComposicaoReferenciaErrada("cheque",con);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	public static void verificarComposicaoReferenciaErrada(String enti, Connection c) throws Exception{
		ResultSet s = c.prepareStatement(" SELECT "+enti+".codigo as "+enti+","+enti+".valor, "+enti+".valortotal, composicao, " +
				" movpagamento.codigo as movpagamento, movpagamento.nomepagador, movpagamento.movpagamentoorigemcredito as  compmov FROM "+enti+" " +
				" INNER JOIN movpagamento on movpagamento.codigo = "+enti+".movpagamento "+
				" WHERE composicao not like '' and composicao is not null ").executeQuery();
		while(s.next()){
			if(!SuperFacadeJDBC.existe(" SELECT * FROM "+enti+" where codigo in ("+s.getString("composicao")+")", c)){
				System.out.println(""+enti+" COD.:"+s.getInt(enti) +", no valor de "+s.getDouble("valor")+" e valortotal de "+s.getDouble("valortotal")+", do pagamento "+s.getInt("movpagamento")
						           +" da pessoa "+s.getString("nomepagador") +" tem um código de composição que não corresponde com algum "+enti+" no banco de dados.");
				
				System.out.println("---------------------------------");
				System.out.println("");
			}
		}
	}
	
	
	public static void consultar(Connection c) throws Exception{
		ResultSet s= c.prepareStatement(" SELECT mc.* FROM movconta mc inner join movcontarateio mcr on mcr.movconta = mc.codigo "+
										" where mc.lote is not null and mcr.tipoes = 1").executeQuery();
		while(s.next()){
			StringBuilder sb = new StringBuilder();
			ResultSet s1 = c.prepareStatement(" SELECT mc.* FROM movconta mc inner join movcontarateio mcr on mcr.movconta = mc.codigo "+
			" where mc.lote is not null and mcr.tipoes = 2 and mc.lote = "+s.getInt("lote")).executeQuery();
			Double v = 0.0;
			while(s1.next()){
				v += s1.getDouble("valor");
				sb.append("\n--> "+s1.getString("descricao")+" --- "
										 +s1.getInt("codigo")+" --- "
										 +s1.getDouble("valor"));
			}
			sb.append("\n--> "+s.getString("descricao")+" --- "
					 +s.getInt("codigo")+" --- "
					 +s.getDouble("valor"));
			if(v > s.getDouble("valor")){
				sb.append("\n--- INCONSISTENCIA -------------------------");
				System.out.println(sb);
			}
		}
	}
	
	public static void corrigirMovimentacoes(Connection con) throws Exception{
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT * FROM lote \n");
		sb.append(" WHERE ( SELECT mc.valor FROM movconta mc inner join movcontarateio mcr on mcr.movconta = mc.codigo where mcr.tipoes = 1 and mc.lote = lote.codigo) \n"); 
		sb.append(" <>  ( SELECT sum(mc.valor) FROM movconta mc inner join movcontarateio mcr on mcr.movconta = mc.codigo where mcr.tipoes = 2 and mc.lote = lote.codigo)  \n");
		sb.append(" and ( SELECT count(mc.codigo) FROM movconta mc inner join movcontarateio mcr on mcr.movconta = mc.codigo where mcr.tipoes = 2 and mc.lote = lote.codigo) = 1 \n");
		sb.append(" order by datadeposito desc \n");
		ResultSet resultSet = SuperFacadeJDBC.criarConsulta(sb.toString(), con);
		while(resultSet.next()){
			Double valor = resultSet.getDouble("valor");
			if(valor != null){
				SuperFacadeJDBC.executarConsulta("UPDATE movconta SET valor = "+valor+" WHERE lote = "+resultSet.getInt("codigo"), con);
				SuperFacadeJDBC.executarConsulta("UPDATE movcontarateio SET valor = "+valor+" WHERE movconta in (select codigo from movconta where lote = "+resultSet.getInt("codigo")+")", con);	
			}
		}
	}
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Marcos Andre",
        data = "24/07/2024",
        descricao = "Adicionar as novas permissões aos perfis corretos",
        motivacao = "M1-2255")
public class AjustarNovasPermissoesM12255 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select distinct codperfilacesso  from permissao where nomeentidade = 'VisualizarBI'", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.74 - Relatório de BVs','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioBVs', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.75 - Relatório de Clientes com Atestado','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioClientesComAtestado', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.76 - Relatório de Clientes com Bônus','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioClientesComBonus', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.77 - Relatório de Clientes Trancados','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioClientesTrancados', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.78 - Relatório de Desconto por Ocupação na Turma','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDescontoPorOcupacao', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.79 - Relatório de Fechamento de Acessos','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioFechamentoDeAcessos', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.80 - Relatório de Indicador de Acessos','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioIndicadorDeAcessos', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.81 - Relatório de Repasse','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeRepasse', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.82 - Relatório de Visitantes','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeVisitantes', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);


            }
            rs = SuperFacadeJDBC.criarConsulta("select codigo as  codperfilacesso  from perfilacesso", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.83 - Relatório de Pedidos Pinpad','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDePedidosPinpad', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.84 - Relatório de Totalizador de Tickets','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeTotalizadorDeTickets', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.85 - Relatório de Transações Pix','(0)(1)(2)(3)(9)(12)', "
                        + " 'RelatorioDeTransacoesPix', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.86 - Visualizar BI - GymPass','(0)(1)(2)(3)(9)(12)', "
                        + " 'BiGymPass', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '9.87 - Visualizar BI - Ciclo de Vida do seu Cliente','(0)(1)(2)(3)(9)(12)', "
                        + " 'BiCicloDeVida', " + rs.getInt("codperfilacesso") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);

            }
        }

    }
}

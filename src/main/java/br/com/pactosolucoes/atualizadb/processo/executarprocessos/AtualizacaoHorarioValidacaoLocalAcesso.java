package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Franco de Sá Feitosa",
        data = "19/03/2025",
        descricao = "Atualizar valores nulos de horario para 1 (horario livre) na tabela validacaolocalacesso",
        motivacao = "GCM-166 - setar horário livre nas validações gympass pré-existentes sem horário informado")
public class AtualizacaoHorarioValidacaoLocalAcesso implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            String sql = "UPDATE validacaolocalacesso SET horario = 1 WHERE horario IS NULL AND tipovalidacao = 4;";
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}

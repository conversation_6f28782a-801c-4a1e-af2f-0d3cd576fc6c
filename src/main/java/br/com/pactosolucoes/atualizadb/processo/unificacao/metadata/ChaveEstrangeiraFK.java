package br.com.pactosolucoes.atualizadb.processo.unificacao.metadata;

import br.com.pactosolucoes.atualizadb.processo.unificacao.TabelaReferenciavel;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 10/04/2019
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ChaveEstrangeiraFK {

    /**
     * @return Deve informar o nome da classe de uma {@link TabelaReferenciavel}.
     */
    Class<? extends TabelaReferenciavel> tabelaReferenciavelName();
}

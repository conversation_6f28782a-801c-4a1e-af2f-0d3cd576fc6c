package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "24/02/2025",
        descricao = "Atualiza data de acesso do usuário de acordo com acesso do colaborador",
        motivacao = "M1-4613")
public class AtualizacaoTicketM14613 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE usuario usu SET ultimoacesso = ac.dthrentrada::DATE\n" +
                    "FROM (\n" +
                    "    SELECT DISTINCT ON (colaborador) *\n" +
                    "    FROM acessocolaborador\n" +
                    "    ORDER BY colaborador, dthrentrada DESC\n" +
                    ") ac\n" +
                    "where ac.colaborador = usu.colaborador\n" +
                    "and not exists (select codigo from tipocolaborador tc where tc.descricao in ('PE', 'PI') and tc.colaborador = ac.colaborador)\n" +
                    "and (usu.ultimoacesso = null or usu.ultimoacesso < dthrentrada::DATE);", c);
        }
    }

}

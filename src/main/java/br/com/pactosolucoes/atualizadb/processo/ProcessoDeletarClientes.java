package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.NotaFiscalConsumidorEletronica;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONObject;
import servicos.integracao.TreinoWSConsumer;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.List;

public class ProcessoDeletarClientes {

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("********************************************************", "zillyonweb", "pactodb");
            String chave = "teste";
            String matriculas = "TODOS"; //para excluir todos colocar "TODOS"
            boolean usaTreino = true;
            boolean excluirMesmoComRemessa = true;
            ProcessoDeletarClientes.deletar(chave, matriculas, excluirMesmoComRemessa, usaTreino, con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void deletar(String chave, String matriculas,
                               boolean excluirMesmoComRemessa,
                               boolean usaTreino, Connection con) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        try {
            Uteis.logarDebug("ProcessoDeletarClientes | INICIO!");

            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);

            UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();

            List<ClienteVO> lista;
            if (matriculas.equalsIgnoreCase("todos")) {
                lista = clienteDAO.consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                lista = clienteDAO.consultarPorCodigosMatricula(matriculas, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            Integer atual = 0;
            Integer falha = 0;
            Integer sucesso = 0;
            for (ClienteVO clienteVO : lista) {
                try {
                    JSONObject json = excluirCliente(chave, clienteVO, usaTreino, excluirMesmoComRemessa, usuarioVO, con);
                    String msg = "Atual: " + atual++ + "/" + lista.size() + " | Mat.: " + json.optString("matricula") + " | " + json.optString("nome");
                    if (json.optBoolean("sucesso")) {
                        sucesso++;
                        Uteis.logarDebug(msg + " | SUCESSO");
                    } else {
                        falha++;
                        Uteis.logarDebug(msg + " | FALHA");
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            Uteis.logarDebug("############ RESUMO ############");
            Uteis.logarDebug("##### Sucesso: " + sucesso + " #####");
            Uteis.logarDebug("##### Falha: " + falha + " #####");
            Uteis.logarDebug("##### TOTAL: " + lista.size() + " #####");
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            Uteis.logarDebug("ProcessoDeletarClientes | FIM!");
        }
    }

    public static JSONObject excluirCliente(String chave, ClienteVO clienteVO,
                                            boolean existeTreino, boolean excluirMesmoComRemessa,
                                            UsuarioVO usuarioVO, Connection con) {
        JSONObject json = new JSONObject();
        StringBuilder msg = new StringBuilder();
        UsuarioMovel usuarioMovelDAO;
        NotaFiscalConsumidorEletronica notaDAO;
        Cliente clienteDAO;
        try {
            usuarioMovelDAO = new UsuarioMovel(con);
            notaDAO = new NotaFiscalConsumidorEletronica(con);
            clienteDAO = new Cliente(con);

            json.put("cliente", clienteVO.getCodigo());
            json.put("matricula", clienteVO.getMatricula());
            json.put("nome", clienteVO.getPessoa().getNome());

            if (!excluirMesmoComRemessa && clienteDAO.clientePossuiRemessa(clienteVO)) {
                throw new Exception("Cliente está em remessa");
            }

            UsuarioMovelVO usuarioMovelVO = usuarioMovelDAO.consultarPorCliente(clienteVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            boolean possuiUsuarioMovel = usuarioMovelVO != null && usuarioMovelVO.getCodigo() != null && usuarioMovelVO.getCodigo() > 0;
            boolean excluiuNoTreino = false;

            if ((existeTreino && possuiUsuarioMovel) || (existeTreino && !possuiUsuarioMovel)) {
                excluiuNoTreino = TreinoWSConsumer.excluirCliente(chave, clienteVO.getCodigo()).equals("ok");

                if (!excluiuNoTreino) {
                    msg.append(clienteVO.getMatricula()).append(" Treino está fora - O cliente não pode ser excluído, pois pode ficar com o registro órfão no treino. \n\n");
                    throw new Exception("");
                }
            }
            if (excluiuNoTreino) {
                clienteDAO.excluirClienteETodosSeusRelacionamentos(clienteVO, usuarioVO);
                msg.append(clienteVO.getMatricula()).append(" - Cliente possui registro no treino e ambos foram excluídos com sucesso. \n\n");
            }
            if ((existeTreino && !possuiUsuarioMovel)) {
                clienteDAO.excluirClienteETodosSeusRelacionamentos(clienteVO, usuarioVO);
                msg.append(clienteVO.getMatricula()).append(" - Cliente não possui registro no treino e foi excluído com sucesso. \n\n");
            }
            if (!existeTreino) {
                clienteDAO.excluirClienteETodosSeusRelacionamentos(clienteVO, usuarioVO);
                msg.append(clienteVO.getMatricula()).append(" - Cliente excluído com sucesso.");
            }
            json.put("sucesso", true);
        } catch (Exception ex) {
            json.put("sucesso", false);
            msg.append(clienteVO.getMatricula()).append(" - Falha ao remover cliente.").append(ex.getMessage()).append(" \n\n");
        } finally {
            usuarioMovelDAO = null;
            notaDAO = null;
            clienteDAO = null;
        }
        json.put("msg", msg);
        return json;
    }
}

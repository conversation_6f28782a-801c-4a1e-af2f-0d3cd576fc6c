package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "21/10/2024",
        descricao = "Ajustes hotsite gestão de vendas online.",
        motivacao = "M2-1995")
public class M22588AjustesCamposHotsite implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planositevendasonline ALTER COLUMN descricaoPlano TYPE VARCHAR(256);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planositevendasonline ALTER COLUMN beneficio01 TYPE VARCHAR(256);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planositevendasonline ALTER COLUMN beneficio02 TYPE VARCHAR(256);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planositevendasonline ALTER COLUMN beneficio03 TYPE VARCHAR(256);", c);
        }
    }
}

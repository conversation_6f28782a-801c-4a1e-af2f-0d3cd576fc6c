package br.com.pactosolucoes.atualizadb.processo;

import importador.outros.ReajusteDeParcelasSesc;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.plano.Produto;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CopiarProdutosEntreBancos {

    private static Map<String, StringBuilder> mapaLogGravar;
    private static String nomeBanco = "";
    private static String keyLogGeral = "GERAL";

    public static void main(String... args) throws Exception {
        Date d1 = Calendario.hoje();
        try {
            Connection conOrigem = DriverManager.getConnection("*************************************************************", "postgres", "pactodb");
            Connection conDestino = DriverManager.getConnection("*********************************************************", "postgres", "pactodb");
            copiar(conOrigem, conDestino);

            conDestino = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");
            copiar(conOrigem, conDestino);
        } finally {
            Date d2 = Calendario.hoje();
            adicionarLog(keyLogGeral, "=============================================");
            adicionarLog(keyLogGeral, "Banco: " + nomeBanco);
            adicionarLog(keyLogGeral, "Tempo Total: " + Calendario.diferencaEmSegundos(d1, d2) + " segundos");
            adicionarLog(keyLogGeral, "=============================================");
            Uteis.salvarArquivo(CopiarProdutosEntreBancos.class.getSimpleName() + "_" + nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt",
                    getLogGravar(keyLogGeral).toString(), "C:\\Processos\\" + File.separator);
        }
    }


    private static void copiar(Connection conOrigem, Connection conDestino) throws Exception {
        Produto produtoDAOOrigem;
        Produto produtoDAODestino;
        try {
            produtoDAOOrigem = new Produto(conOrigem);
            produtoDAODestino = new Produto(conDestino);

            String nomeBancoOrigem = conOrigem.getCatalog();
            String nomeBancoDestino = conDestino.getCatalog();
            adicionarLog(keyLogGeral, "=============================================");
            adicionarLog(keyLogGeral, "Origem: " + nomeBancoOrigem);
            adicionarLog(keyLogGeral, "Destino: " + nomeBancoDestino);
            adicionarLog(keyLogGeral, "=============================================");

            List<ProdutoVO> produtosOrigem = produtoDAOOrigem.consultarProdutosPorTipoProduto(TipoProduto.PRODUTO_ESTOQUE.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            List<ProdutoVO> produtosDestino = produtoDAODestino.consultarProdutosPorTipoProduto(TipoProduto.PRODUTO_ESTOQUE.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);

            int atual = 0;
            int sucesso = 0;
            int erro = 0;
            for (ProdutoVO produtoOrigem : produtosOrigem) {
                for (ProdutoVO produtoDestino : produtosDestino) {
                    if (produtoDestino.getDescricao().equals(produtoOrigem.getDescricao())) {
                        String msg = "";
                        try {
                            produtoDestino.setCfop(produtoOrigem.getCfop());
                            produtoDestino.setAliquotaPIS(produtoOrigem.getAliquotaPIS());
                            produtoDestino.setSituacaoTributariaPIS(produtoOrigem.getSituacaoTributariaPIS());
                            produtoDestino.setAliquotaICMS(produtoOrigem.getAliquotaICMS());
                            produtoDestino.setSituacaoTributariaICMS(produtoOrigem.getSituacaoTributariaICMS());
                            produtoDestino.setAliquotaCOFINS(produtoOrigem.getAliquotaCOFINS());
                            produtoDestino.setSituacaoTributariaCOFINS(produtoOrigem.getSituacaoTributariaCOFINS());
                            produtoDestino.setNcm(produtoOrigem.getNcm());
                            produtoDestino.setNcmNFCe(produtoOrigem.getNcmNFCe());
                            produtoDestino.setPercentualMunicipal(produtoOrigem.getPercentualMunicipal());
                            produtoDestino.setPercentualEstadual(produtoOrigem.getPercentualEstadual());
                            produtoDestino.setPercentualFederal(produtoOrigem.getPercentualFederal());
                            produtoDestino.setCest(produtoOrigem.getCest());
                            produtoDestino.setIsentoPIS(produtoOrigem.isIsentoPIS());
                            produtoDestino.setIsentoCOFINS(produtoOrigem.isIsentoCOFINS());
                            produtoDestino.setIsentoICMS(produtoOrigem.isIsentoICMS());
                            produtoDestino.setAliquotaISSQN(produtoOrigem.getAliquotaISSQN());
                            produtoDestino.setCodigoListaServico(produtoOrigem.getCodigoListaServico());
                            produtoDestino.setCodigoTributacaoMunicipio(produtoOrigem.getCodigoTributacaoMunicipio());
                            produtoDestino.setCodigoBeneficioFiscal(produtoOrigem.getCodigoBeneficioFiscal());
                            produtoDestino.setEnviarPercentualImposto(produtoOrigem.isEnviarPercentualImposto());
                            produtoDestino.setEnviaAliquotaNFeCOFINS(produtoOrigem.isEnviaAliquotaNFeCOFINS());
                            produtoDestino.setEnviaAliquotaNFeICMS(produtoOrigem.isEnviaAliquotaNFeICMS());
                            produtoDestino.setEnviaAliquotaNFePIS(produtoOrigem.isEnviaAliquotaNFePIS());
                            produtoDAODestino.alterar(produtoDestino);
                            msg = "SUCESSO!";
                            ++sucesso;
                        } catch (Exception ex) {
                            ++erro;
                            msg = ("ERRO: " + ex.getMessage());
                            ex.printStackTrace();
                        } finally {
                            adicionarLog(keyLogGeral, "Produto " + ++atual + " - Copiando... | " + produtoOrigem.getDescricao() + " | Resultado: " + msg);
                        }
                    }
                }
            }

            adicionarLog(keyLogGeral, "=============================================");
            adicionarLog(keyLogGeral, "Sucesso: " + sucesso);
            adicionarLog(keyLogGeral, "Erro: " + erro);
            adicionarLog(keyLogGeral, "=============================================");
        } finally {
            produtoDAOOrigem = null;
            produtoDAODestino = null;
        }
    }

    private static void adicionarLog(String keyMapa, String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        StringBuilder logGravar = getLogGravar(keyMapa).append(s).append("\n");
        mapaLogGravar.put(keyMapa, logGravar);
    }

    private static StringBuilder getLogGravar(String keyMapa) {
        if (mapaLogGravar == null) {
            mapaLogGravar = new HashMap<>();
        }
        StringBuilder logGravar = mapaLogGravar.get(keyMapa);
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

}

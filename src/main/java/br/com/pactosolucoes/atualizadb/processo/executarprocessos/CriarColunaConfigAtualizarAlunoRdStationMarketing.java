package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "28/09/2024",
        descricao = "Cria colunas configAtualizarAlunoRdStationMarketing, accessTokenRdStationMarketing, refreshTokenRdStationMarketing na tabela configuracaoempresardstation",
        motivacao = "GC-1006: Integraçao Pacto para RD Station - Pratique")
public class CriarColunaConfigAtualizarAlunoRdStationMarketing implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        String sql = "ALTER TABLE configuracaoempresardstation " +
                "ADD COLUMN configAtualizarAlunoRdStationMarketing BOOLEAN DEFAULT false, " +
                "ADD COLUMN accessTokenRdStationMarketing TEXT, " +
                "ADD COLUMN refreshTokenRdStationMarketing VARCHAR(255)";

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}

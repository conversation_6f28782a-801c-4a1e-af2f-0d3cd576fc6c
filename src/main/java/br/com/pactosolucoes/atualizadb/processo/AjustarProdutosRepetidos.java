/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import static br.com.pactosolucoes.atualizadb.processo.AjustarAcessosRepetidos.apagarAcessosRepetidos;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class AjustarProdutosRepetidos {
    
    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("************************************************************", "postgres", "pactodb");
             apagarprodutosRepetidos(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void apagarprodutosRepetidos(Connection con) throws Exception {
        ResultSet consultaCredito = SuperFacadeJDBC.criarConsulta("select min(codigo) as codProduto from produto where desativado  = 'f' and descricao = 'DEPÓSITO CONTA CORRENTE ALUNO' and tipoProduto = 'CC'", con);

        while (consultaCredito .next()) {
            SuperFacadeJDBC.executarConsultaUpdate("update movproduto set produto = " +consultaCredito.getInt("codProduto") + " where produto in (select codigo from produto where descricao = 'DEPÓSITO CONTA CORRENTE ALUNO' and tipoProduto = 'CC') ", con);
            SuperFacadeJDBC.executarConsultaUpdate("update itemvendaavulsa set produto = " + consultaCredito.getInt("codProduto") + " where produto in (select codigo from produto where descricao = 'DEPÓSITO CONTA CORRENTE ALUNO' and tipoProduto = 'CC') ", con);
            SuperFacadeJDBC.executarConsultaUpdate("update colaborador SET produtodefault = " + consultaCredito.getInt("codProduto") + " WHERE produtodefault  <> " + consultaCredito.getInt("codProduto") + " and produtodefault in( select codigo from produto where descricao = 'DEPÓSITO CONTA CORRENTE ALUNO' and tipoProduto = 'CC')", con);
            SuperFacadeJDBC.executarConsultaUpdate("delete from rateiointegracao where produto  <> " + consultaCredito.getInt("codProduto") + " and produto in( select codigo from produto where descricao = 'DEPÓSITO CONTA CORRENTE ALUNO' and tipoProduto = 'CC')", con);
            SuperFacadeJDBC.executarConsultaUpdate("delete from produto where codigo  <> " + consultaCredito.getInt("codProduto") + " and codigo in( select codigo from produto where descricao = 'DEPÓSITO CONTA CORRENTE ALUNO' and tipoProduto = 'CC')", con);
            
        }
        
        ResultSet consultaAcerto = SuperFacadeJDBC.criarConsulta("select min(codigo) as codProduto from produto where desativado  = 'f' and descricao = 'ACERTO CONTA CORRENTE ALUNO' and tipoProduto = 'AC'", con);

        while (consultaAcerto .next()) {
            SuperFacadeJDBC.executarConsultaUpdate("update movproduto set produto = " +consultaAcerto.getInt("codProduto") + " where produto in (select codigo from produto where descricao = 'ACERTO CONTA CORRENTE ALUNO' and tipoProduto = 'AC')", con);
            SuperFacadeJDBC.executarConsultaUpdate("update itemvendaavulsa set produto = " + consultaAcerto.getInt("codProduto") + " where produto in (select codigo from produto where descricao = 'ACERTO CONTA CORRENTE ALUNO' and tipoProduto = 'AC')", con);
            SuperFacadeJDBC.executarConsultaUpdate("update colaborador SET produtodefault = " + consultaCredito.getInt("codProduto") + " WHERE produtodefault  <> " + consultaAcerto.getInt("codProduto") + " and produtodefault in( select codigo from produto where descricao = 'ACERTO CONTA CORRENTE ALUNO' and tipoProduto = 'AC')", con);
            SuperFacadeJDBC.executarConsultaUpdate("delete from rateiointegracao where produto  <> " + consultaAcerto.getInt("codProduto") + " and produto in( select codigo from produto where descricao = 'ACERTO CONTA CORRENTE ALUNO' and tipoProduto = 'AC')", con);
            SuperFacadeJDBC.executarConsultaUpdate("delete from produto where codigo  <> " + consultaAcerto.getInt("codProduto") + " and codigo in( select codigo from produto where descricao = 'ACERTO CONTA CORRENTE ALUNO' and tipoProduto = 'AC')", con);
        }
        
    }
    
}

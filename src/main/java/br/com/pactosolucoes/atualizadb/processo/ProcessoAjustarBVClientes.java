package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Questionario;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAjustarBVClientes {

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "openfitness";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            ajustarBVClientes(c);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAjustarBVClientes.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarBVClientes(Connection con) {
        try {
            Uteis.logarDebug("INÍCIO | ProcessoAjustarContratosBolsaSemParcela");
            Questionario questionarioDao = new Questionario(con);
            Empresa empresaDao = new Empresa(con);
            List<EmpresaVO> listaEmpresas = empresaDao.consultarTodas(true, Uteis.NIVELMONTARDADOS_MINIMOS);
            for (EmpresaVO empresa : listaEmpresas) {
                QuestionarioVO questionarioMatricula = questionarioDao.obterQuestionario(empresa.getCodigo(), "MA");
                QuestionarioVO questionarioRematricula = questionarioDao.obterQuestionario(empresa.getCodigo(), "RE");
                QuestionarioVO questionarioRetorno = questionarioDao.obterQuestionario(empresa.getCodigo(), "RT");

                StringBuilder sqlInsertContratosSemBVRetorno = new StringBuilder();
                sqlInsertContratosSemBVRetorno.append("SELECT\n");
                sqlInsertContratosSemBVRetorno.append("'INSERT INTO questionariocliente (data, consultor, cliente, questionario, observacao, tipobv, ultimaatualizacao, origemsistema) VALUES (''' || con.datalancamento || ''', ' || con.consultor  || ', ' || cli.codigo || ', ").append(questionarioRetorno.getCodigo()).append(", '''', 2, ''' || con.datalancamento || ''', 1);' AS sqlInsertBVRetorno\n");
                sqlInsertContratosSemBVRetorno.append("FROM contrato con\n");
                sqlInsertContratosSemBVRetorno.append("INNER JOIN cliente cli ON cli.pessoa = con.pessoa\n");
                sqlInsertContratosSemBVRetorno.append("WHERE con.situacaocontrato IN ('MA')\n");
                sqlInsertContratosSemBVRetorno.append("AND EXISTS (SELECT codigo FROM questionariocliente WHERE cliente = cli.codigo AND tipobv IN (1,2) AND EXTRACT(MONTH FROM data) <> EXTRACT(MONTH FROM con.datalancamento))\n");
                sqlInsertContratosSemBVRetorno.append("AND NOT EXISTS (SELECT codigo FROM questionariocliente WHERE cliente = cli.codigo AND tipobv IN (1,2) AND EXTRACT(MONTH FROM data) = EXTRACT(MONTH FROM con.datalancamento))\n");
                sqlInsertContratosSemBVRetorno.append("AND con.datalancamento > '2024-01-01'\n");
                sqlInsertContratosSemBVRetorno.append("AND con.datalancamento < '2024-12-19'\n");
                sqlInsertContratosSemBVRetorno.append("ORDER BY cli.codigo;\n");
                try (Statement stm = con.createStatement()) {
                    try (ResultSet rs = stm.executeQuery(sqlInsertContratosSemBVRetorno.toString())) {
                        while (rs.next()) {
                            String sqlUpdateCliente = rs.getString("sqlInsertBVRetorno");
                            try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdateCliente)) {
                                sqlAlterar.execute();
                            }
                        }
                    }
                }

                StringBuilder sqlInsertContratosSemBVRematricula = new StringBuilder();
                sqlInsertContratosSemBVRematricula.append("SELECT\n");
                sqlInsertContratosSemBVRematricula.append("'INSERT INTO questionariocliente (data, consultor, cliente, questionario, observacao, tipobv, ultimaatualizacao, origemsistema) VALUES (''' || con.datalancamento || ''', ' || con.consultor  || ', ' || cli.codigo || ', ").append(questionarioRematricula.getCodigo()).append(", '''', 3, ''' || con.datalancamento || ''', 1);' AS sqlInsertBVRematricula\n");
                sqlInsertContratosSemBVRematricula.append("FROM contrato con\n");
                sqlInsertContratosSemBVRematricula.append("INNER JOIN cliente cli ON cli.pessoa = con.pessoa\n");
                sqlInsertContratosSemBVRematricula.append("WHERE con.situacaocontrato IN ('RE')\n");
                sqlInsertContratosSemBVRematricula.append("AND NOT EXISTS (SELECT codigo FROM questionariocliente WHERE cliente = cli.codigo AND tipobv IN (1,2,3) AND EXTRACT(MONTH FROM data) = EXTRACT(MONTH FROM con.datalancamento))\n");
                sqlInsertContratosSemBVRematricula.append("AND con.datalancamento > '2024-01-01'\n");
                sqlInsertContratosSemBVRematricula.append("AND con.datalancamento < '2024-12-19'\n");
                sqlInsertContratosSemBVRematricula.append("ORDER BY cli.codigo;\n");
                try (Statement stm = con.createStatement()) {
                    try (ResultSet rs = stm.executeQuery(sqlInsertContratosSemBVRematricula.toString())) {
                        while (rs.next()) {
                            String sqlUpdateCliente = rs.getString("sqlInsertBVRematricula");
                            try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdateCliente)) {
                                sqlAlterar.execute();
                            }
                        }
                    }
                }

                StringBuilder sqlInsertContratosSemBVMatricula = new StringBuilder();
                sqlInsertContratosSemBVMatricula.append("SELECT\n");
                sqlInsertContratosSemBVMatricula.append("'INSERT INTO questionariocliente (data, consultor, cliente, questionario, observacao, tipobv, ultimaatualizacao, origemsistema) VALUES (''' || con.datalancamento || ''', ' || con.consultor  || ', ' || cli.codigo || ', ").append(questionarioMatricula.getCodigo()).append(", '''', 1, ''' || con.datalancamento || ''', 1);' AS sqlInsertBVMatricula\n");
                sqlInsertContratosSemBVMatricula.append("FROM contrato con\n");
                sqlInsertContratosSemBVMatricula.append("INNER JOIN cliente cli ON cli.pessoa = con.pessoa\n");
                sqlInsertContratosSemBVMatricula.append("WHERE con.situacaocontrato IN ('MA')\n");
                sqlInsertContratosSemBVMatricula.append("AND NOT EXISTS (SELECT codigo FROM questionariocliente WHERE cliente = cli.codigo AND tipobv IN (1,2) AND EXTRACT(MONTH FROM data) = EXTRACT(MONTH FROM con.datalancamento))\n");
                sqlInsertContratosSemBVMatricula.append("AND con.datalancamento > '2024-01-01'\n");
                sqlInsertContratosSemBVMatricula.append("AND con.datalancamento < '2024-12-19'\n");
                sqlInsertContratosSemBVMatricula.append("ORDER BY cli.codigo;\n");
                try (Statement stm = con.createStatement()) {
                    try (ResultSet rs = stm.executeQuery(sqlInsertContratosSemBVMatricula.toString())) {
                        while (rs.next()) {
                            String sqlUpdateCliente = rs.getString("sqlInsertBVMatricula");
                            try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdateCliente)) {
                                sqlAlterar.execute();
                            }
                        }
                    }
                }
            }
            Uteis.logarDebug("FIM | ProcessoAjustarBVClientes");
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoAjustarBVClientes - " + ex.getMessage());
        }
    }
}

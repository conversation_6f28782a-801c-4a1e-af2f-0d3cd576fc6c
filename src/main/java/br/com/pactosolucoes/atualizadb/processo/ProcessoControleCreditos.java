/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;


public class ProcessoControleCreditos {

    public static void inserirTabelaReprocessados(Connection con, Integer contrato, Integer reposicao, Integer acesso, Integer tipo) throws Exception{
        con.prepareStatement("insert into reprocessamentocreditos (contrato, reposicao, acesso, tipo) values " +
                "("+contrato+", "+reposicao+", "+acesso+", "+tipo+")").execute();
    }
    public static void criarTabelaReprocessados(Connection con){
        try {
            con.prepareStatement("create table reprocessamentocreditos(\n" +
                    "codigo serial primary key,\n" +
                    "contrato int,\n" +
                    "acesso int,\n" +
                    "tipo int,\n" +
                    "reposicao int\n" +
                    ")").execute();
        }catch (Exception e){
            System.out.println("ja existe a tabela reprocessamentocreditos");
//            e.printStackTrace();
        }
    }
    public static void criarTabelaSaldos(Connection con){
        try {
            con.prepareStatement("create table reprocessamentosaldos(\n" +
                    "codigo serial primary key,\n" +
                    "contrato int,\n" +
                    "saldo int\n" +
                    ")").execute();
        }catch (Exception e){
            System.out.println("ja existe a tabela reprocessamentosaldos");
//            e.printStackTrace();
        }
    }

    public static void inserirTabelaSaldos(Connection con, Integer contrato, Integer saldo) throws Exception{
        con.prepareStatement("insert into reprocessamentosaldos (contrato, saldo) values " +
                "("+contrato+", "+saldo+")").execute();
    }

    public static void inserirTabelaContratoDeleteTransferencia(Connection con, Integer contrato, Integer quantidade) throws Exception{
        con.prepareStatement("insert into ContratoDeleteTransferencia (contrato, creditos) values " +
                "("+contrato+", "+quantidade+")").execute();
    }

    public static void criarTabelaContratoDeleteTransferencia(Connection con){
        try {
            con.prepareStatement("create table ContratoDeleteTransferencia(\n" +
                    "codigo serial primary key,\n" +
                    "contrato int,\n" +
                    "creditos int)").execute();
        }catch (Exception e){
            System.out.println("ja existe a tabela ContratoDeleteTransferencia");
//            e.printStackTrace();
        }
    }

    public static void main(String... args) {
        String urlBDHoje = "********************************************************";
        Connection con = conexao(urlBDHoje);
//        processar(con);
//        processarTransferidos(con);
        checarNegativos(con);
    }



    public static void updateSintetico(Connection con, Integer contrato, Integer saldo) throws Exception{
        ResultSet consulta = consulta(con, "select situacao, pessoa from contrato where codigo = " + contrato);
        if(consulta.next()){
            String situacao = consulta.getString("situacao");
            if("AT".equals(situacao)){
                con.prepareStatement("update situacaoclientesinteticodw set saldocreditotreino = " + saldo
                    + " where codigopessoa = " + consulta.getInt("pessoa")
                );
            }
        }
    }
    public static void checarNegativos(Connection con) {
        try {
            criarTabelaSaldos(con);
            ResultSet rs = consulta(con, "select distinct contrato from reprocessamentocreditos");
            int totalSaldoNegativo = 0;
            while (rs.next()) {
                Integer saldoCredito = consultarSaldoCredito(con, rs.getInt("contrato"));
                inserirTabelaSaldos(con, rs.getInt("contrato"), saldoCredito);
                updateSintetico(con, rs.getInt("contrato"), saldoCredito);
                System.out.println("contrato: " + rs.getInt("contrato") + " - saldo: " + saldoCredito);
                if(saldoCredito < 0){
                    totalSaldoNegativo += (saldoCredito * -1);
                }
            }
             rs = consulta(con, "select distinct contrato from ContratoDeleteTransferencia");
            while (rs.next()) {
                Integer saldoCredito = consultarSaldoCredito(con, rs.getInt("contrato"));
                updateSintetico(con, rs.getInt("contrato"), saldoCredito);
                System.out.println("contrato: " + rs.getInt("contrato") + " - saldo: " + saldoCredito);
            }
            System.out.println("----------------------");
            System.out.println(totalSaldoNegativo);
        }catch (Exception e){
            e.printStackTrace();
        }

    }



    public static void processarTransferidos(Connection con) {
        try {
            criarTabelaContratoDeleteTransferencia(con);
            ResultSet rs = consulta(con, "select distinct contrato from reprocessamentocreditos");
            int totalSaldoNegativo = 0;
            while (rs.next()) {
                Integer saldoCredito = consultarSaldoCredito(con, rs.getInt("contrato"));
                int contrato = rs.getInt("contrato");
                System.out.println("contrato: " + rs.getInt("contrato") + " - saldo: " + saldoCredito);
                if(saldoCredito < 0){
                    String sql = "select codigo, quantidade from controlecreditotreino c \n" +
                            "where tipooperacaocreditotreino = 8 \n" +
                            "and contrato = " + contrato;
                    PreparedStatement pst = con.prepareStatement(sql);
                    ResultSet rsTransferido = pst.executeQuery();
                    if (rsTransferido.next()) {
                        Integer quantidade = rsTransferido.getInt("quantidade") * -1;
                        Integer contratoTransferido = consultarContratoTransferido(con, contrato);

                        if(quantidade > (saldoCredito * -1)){
                            Integer quantidadePosProcessamento = quantidade - (saldoCredito * -1);
                            PreparedStatement statement = con.prepareStatement("update controlecreditotreino set quantidade = -" +
                                    (quantidadePosProcessamento) + " where codigo = " + rsTransferido.getInt("codigo"));
                            statement.execute();

                            statement = con.prepareStatement("update controlecreditotreino set quantidade = " +
                                    (quantidadePosProcessamento) + " where contratoorigem = " + contrato);
                            statement.execute();
                        } else {
                            PreparedStatement statement = con.prepareStatement("delete from controlecreditotreino " +
                                    " where codigo = " + rsTransferido.getInt("codigo"));
                            statement.execute();

                            statement = con.prepareStatement("delete from controlecreditotreino " +
                                    " where contratoorigem = " + contrato);
                            statement.execute();
                        }
                        inserirTabelaContratoDeleteTransferencia(con, contratoTransferido, quantidade);

                    }

                }
            }
            System.out.println("----------------------");
            System.out.println(totalSaldoNegativo);
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public static void processar(Connection con) {
        try {
            criarTabelaReprocessados(con);
            Conexao.guardarConexaoForJ2SE(con);
            int cont = 1;
            int creditosReprocessar = 0;
            int saldoExcedente = 0;
            int finalizados = 0;
            Map<Integer, Integer> saldonovo = new HashMap<>();

            //consultar todos contratos de credito com vigencia > 02/10/2022
            ResultSet rs = consulta(con, "select c.codigo, p.nome, cli.codigo as cliente, cli.matricula, c.vigenciaateajustada, c.vigenciade from contrato c\n" +
                    "inner join pessoa p on p.codigo = c.pessoa \n" +
                    "inner join cliente cli on p.codigo = cli.pessoa \n" +
                    "where c.situacao <> 'CA' and c.vendacreditotreino and vigenciaateajustada > '2022-10-12' order by codigo desc");
            while (rs.next()) {
                String nome = rs.getString("nome");
                String matricula = rs.getString("matricula");
                Integer contrato = rs.getInt("codigo");
                Integer cliente = rs.getInt("cliente");
                Date vigenciade = rs.getDate("vigenciade");
                Date vigenciaateajustada = rs.getDate("vigenciaateajustada");
                //obter modalidades do contrato
                String modalidades = listStr(lista(con, "select modalidade as codigo from contratomodalidade c where contrato = " + contrato));
                ResultSet rsRepo = consulta(con, "select r.horarioturma, r.usuario, r.codigo, r.datareposicao  from reposicao r \n" +
                        " inner join turma t on t.codigo = r.turmadestino\n" +
                        " inner join contrato con on r.contrato = con.codigo\n" +
                        " left join controlecreditotreino c on c.reposicao = r.codigo \n" +
                        " where datareposicao >= '2022-10-12' and datareposicao < '2022-12-21'" +
                        " and datareposicao >= '" + Uteis.getDataAplicandoFormatacao(vigenciade, "yyyy-MM-dd") + "'" +
                        " and datareposicao <= '" + Uteis.getDataAplicandoFormatacao(vigenciaateajustada, "yyyy-MM-dd") + "'" +
                        " and c.codigo is null\n" +
                        " and not con.vendacreditotreino\n" +
                        " and r.contrato <> " + contrato +
                        " and r.cliente = " + cliente +
                        " and t.modalidade in (44,104)");
                List<Integer> reposicoes = new ArrayList<>();
                while (rsRepo.next()) {
                    cont++;
                    reposicoes.add(rsRepo.getInt("codigo"));
                    processar(con, contrato, rsRepo.getInt("codigo"), cliente,
                            rsRepo.getInt("horarioturma"), rsRepo.getTimestamp("datareposicao"));
                }

                if(reposicoes.isEmpty()){
                    continue;
                }
                int debitar = reposicoes.size();
                int saldo = consultarSaldoCredito(con, contrato);
                int quantidadeUltimoAjusteManual = consultarUltimoAjusteManual(con, contrato);
                int quantidadeTransferido = consultarTransferencia(con, contrato);
                int contratoTransferido = consultarContratoTransferido(con, contrato);
                int debitarOutroContrato = 0;
                if(contratoTransferido > 0){
                    Integer saldoDoTransferido = saldonovo.get(contratoTransferido);
                    if(saldoDoTransferido == null){
                        saldoDoTransferido = consultarSaldoCredito(con, contratoTransferido);
                    }
                    debitarOutroContrato = saldoDoTransferido >= quantidadeTransferido ? quantidadeTransferido : saldoDoTransferido;
                }


                creditosReprocessar += debitar;
                int saldoexc = (saldo) - (debitar > quantidadeUltimoAjusteManual ? (debitar - quantidadeUltimoAjusteManual) : (0));
                saldonovo.put(contrato, saldoexc);

                if (saldoexc < 0 && (saldoexc * -1 > debitarOutroContrato)) {
                    saldoExcedente += (saldoexc * -1) - debitarOutroContrato;
                    System.out.println(contrato + " - MAT: " + matricula + " - " + cliente + " - " + nome + " - creditos não lançados: " + debitar + " - saldo do cliente: " + saldo
                            + " - ultimo ajuste manual: " + quantidadeUltimoAjusteManual);
                    System.out.println("finaliza em: " + Uteis.getData(vigenciaateajustada));
                }

            }

        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void processar(Connection con,
                          Integer contrato, Integer reposicao,
                          Integer cliente,
                          Integer horarioturma,
                          Date operacao) throws Exception{
        ControleCreditoTreino controleCreditoTreino = new ControleCreditoTreino(con);
        ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
        controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.MARCOU_AULA);
        controleCreditoTreinoVO.setQuantidade(0);
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(contrato);
        controleCreditoTreinoVO.setContratoVO(contratoVO);
        ReposicaoVO reposicaoVO = new ReposicaoVO();
        reposicaoVO.setCodigo(reposicao);
        controleCreditoTreinoVO.setReposicaoVO(reposicaoVO);
        UsuarioVO usuarioVO = new UsuarioVO();
        usuarioVO.setCodigo(3);
        controleCreditoTreinoVO.setUsuarioVO(usuarioVO);
        controleCreditoTreinoVO.setDataOperacao(operacao);
        controleCreditoTreinoVO.setDatalancamento(operacao);
        controleCreditoTreino.incluirSemCommit(controleCreditoTreinoVO, null, null, null);
        ResultSet rs = consulta(con, "select * from acessocliente a where dthrentrada::date = '" + Uteis.getDataAplicandoFormatacao(operacao, "yyyy-MM-dd")
                + "' and cliente = " + cliente);
        if(rs.next()){
            debitarCreditoPresenca(con, rs.getInt("codigo"), contrato, rs.getTimestamp("dthrentrada"), cliente);
            inserirTabelaReprocessados(con, contrato, reposicao, rs.getInt("codigo"), TipoOperacaoCreditoTreinoEnum.UTILIZACAO.getCodigo());
        } else{
            HorarioTurmaVO horarioTurmaFalta = new HorarioTurmaVO();
            horarioTurmaFalta.setCodigo(horarioturma);
            diminuirCreditoTreinoNaoComparecimento(con, contratoVO, operacao, "", horarioTurmaFalta);
            inserirTabelaReprocessados(con, contrato, reposicao, 0, TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO.getCodigo());
        }


    }

    private static void diminuirCreditoTreinoNaoComparecimento(Connection con, ContratoVO contratoVO, Date dataOperacao, String observacao,
                                                        HorarioTurmaVO horarioTurmaFalta) throws Exception {
        ControleCreditoTreino controleCreditoTreino = new ControleCreditoTreino(con);
        ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
        controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO);
        controleCreditoTreinoVO.setObservacao(observacao);
        controleCreditoTreinoVO.setHorarioTurmaFalta(horarioTurmaFalta);
        controleCreditoTreinoVO.setQuantidade(-1);
        controleCreditoTreinoVO.setContratoVO(contratoVO);
        controleCreditoTreinoVO.setDataOperacao(dataOperacao);
        UsuarioVO usuarioVO = new UsuarioVO();
        usuarioVO.setCodigo(3);
        controleCreditoTreinoVO.setUsuarioVO(usuarioVO);
        controleCreditoTreino.incluirSemCommit(controleCreditoTreinoVO,
                null, null, null);
    }

    public static void debitarCreditoPresenca(Connection con, Integer acesso, Integer contrato,
                                              Date entrada,
                                              Integer cliente) throws Exception{
            ControleCreditoTreino controleCreditoTreino = new ControleCreditoTreino(con);
            ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
            controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.UTILIZACAO);
            AcessoClienteVO acessoVO = new AcessoClienteVO();
            acessoVO.setCodigo(acesso);
            controleCreditoTreinoVO.setAcessoClienteVO(acessoVO);
            controleCreditoTreinoVO.setQuantidade(-1);
            ContratoVO contratoVO = new ContratoVO();
            contratoVO.setCodigo(contrato);
            controleCreditoTreinoVO.setContratoVO(contratoVO);
            controleCreditoTreinoVO.setDataOperacao(entrada);
            UsuarioVO usuarioVO = new UsuarioVO();
            usuarioVO.setCodigo(3);
            controleCreditoTreinoVO.setUsuarioVO(usuarioVO);
            controleCreditoTreino.incluirSemCommit(controleCreditoTreinoVO,
                    cliente, null, null);
    }

    public static Integer consultarContratoTransferido(Connection connection, Integer codigoContrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select contrato  \n");
        sql.append("from controleCreditoTreino \n");
        sql.append("where tipooperacaocreditotreino = 8 and contratoorigem = ").append(codigoContrato);
        PreparedStatement pst = connection.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()) {
            return rs.getInt("contrato");
        }
        return 0;
    }

    public static Integer consultarSaldoCredito(Connection connection, Integer codigoContrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select sum(quantidade) as total \n");
        sql.append("from controleCreditoTreino \n");
        sql.append("where contrato = ").append(codigoContrato);
        PreparedStatement pst = connection.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()) {
            return rs.getInt("total");
        }
        return 0;
    }

    public static Integer consultarUltimoAjusteManual(Connection connection, Integer codigoContrato) throws Exception {
        String sql = "select quantidade from controlecreditotreino c \n" +
                "where tipooperacaocreditotreino = 7 \n" +
                " and quantidade < 0" +
                "and not exists (select codigo from controlecreditotreino c2 where c2.contrato = c.contrato and c.codigo < c2.codigo)\n" +
                "and contrato = " + codigoContrato;
        PreparedStatement pst = connection.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()) {
            return rs.getInt("quantidade") * -1;
        }
        return 0;
    }

    public static Integer consultarTransferencia(Connection connection, Integer codigoContrato) throws Exception {
        String sql = "select quantidade from controlecreditotreino c \n" +
                "where tipooperacaocreditotreino = 8 \n" +
                "and contrato = " + codigoContrato;
        PreparedStatement pst = connection.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()) {
            return rs.getInt("quantidade") * -1;
        }
        return 0;
    }

    public static String listStr(List<Integer> lista) {
        String s = "";
        for (Integer i : lista) {
            s += "," + i;
        }
        return s.replaceFirst(",", "");
    }

    public static List<Integer> lista(Connection con, String sql) throws Exception {
        List<Integer> lista = new ArrayList<>();
        ResultSet consulta = consulta(con, sql);
        while (consulta.next()) {
            lista.add(consulta.getInt("codigo"));
        }
        return lista;
    }

    public static ResultSet consulta(Connection con, String sql) {
        try {
            PreparedStatement pst = con.prepareStatement(sql);
            return pst.executeQuery();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Connection conexao(String urlBD) {
        try {
            return DriverManager.getConnection(urlBD, "postgres", "pactodb");
        } catch (Exception e) {
            return null;
        }
    }
}

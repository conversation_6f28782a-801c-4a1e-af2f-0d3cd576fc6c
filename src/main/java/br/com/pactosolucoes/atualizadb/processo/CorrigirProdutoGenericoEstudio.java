package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.estudio.dao.Pacote;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ItemVendaAvulsa;

import java.sql.*;

/**
 * Created by Rafael on 15/09/2015.
 */
public class CorrigirProdutoGenericoEstudio extends SuperControle {

    public static void main(String[] args) throws SQLException {
        Connection con;
        try {
            con = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "yoga");
            alterarAgendasProdutoGenerico(con);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }
    static void alterarAgendasProdutoGenerico(Connection con) throws Exception{

        StringBuilder sql = new StringBuilder();

        //<PERSON><PERSON>ciona as Vendas Avulsas onde so foi adicionado um pacote
        sql.append(" SELECT venda.codigo as codigo FROM  vendaavulsa venda\n");
        sql.append(" INNER JOIN  itemvendaavulsa item ON item.vendaavulsa = venda.codigo\n");
        sql.append(" INNER JOIN produto prod ON prod.codigo = item.produto\n");
        sql.append(" WHERE venda.codigo NOT IN (Select vendaavulsa from itemvendaavulsa where pacote is null)");
        sql.append(" AND prod.descricao = 'PRODUTO GENÉRICO'");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(),con);
        while (rs.next()){
            StringBuilder updateAgendaSomentePacote = new StringBuilder();
            updateAgendaSomentePacote.append("UPDATE sch_estudio.agenda  Set produto_Generico = true ");
            updateAgendaSomentePacote.append(" where id_agenda in (Select id_agenda from sch_estudio.agenda_venda where id_vendaavulsa = ").append(rs.getInt("codigo")).append(")");
            SuperFacadeJDBC.executarConsultaUpdate(updateAgendaSomentePacote.toString(),con);
             try{
               SuperFacadeJDBC.executarConsultaUpdate("update sch_estudio.agenda_excecao  set produto_generico = true where id_vendaavulsa = "+rs.getInt("codigo"),con);
           }catch(Exception e){
               
           }
        }

        //Selecionas as Vendas Avulsa onde foi adicionar produto do tipo Pacote
        //e tambem um outro produto
        sql = new StringBuilder();
        sql.append(" SELECT venda.* FROM vendaavulsa venda\n");
        sql.append(" WHERE venda.codigo in (Select vendaavulsa from itemvendaavulsa where pacote is NOT NULL)");
        rs = SuperFacadeJDBC.criarConsulta(sql.toString(),con);
        while(rs.next()){
           int codigoVendaAvulsa =  rs.getInt("codigo");
           int totalProdutosAAgenda = 0;
           sql = new StringBuilder();
           sql.append(" SELECT * FROM ItemVendaAvulsa item\n");
           sql.append(" INNER JOIN sch_estudio.Pacote pac ON pac.id_pacote = item.pacote ");
           sql.append(" WHERE item.vendaAvulsa = ").append(codigoVendaAvulsa);
           sql.append(" AND pac.generico = true ");
           ResultSet resultado = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (resultado.next()) {

                ItemVendaAvulsaVO novoObj;
                ItemVendaAvulsa itemVendaAvulsaDAO = new ItemVendaAvulsa(con);
                novoObj = itemVendaAvulsaDAO.montarDados(resultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                itemVendaAvulsaDAO = null;
                Pacote pacote = new Pacote(con);
                novoObj.setPacoteVO(pacote.consultarPorCodigo(novoObj.getPacoteVO().getCodigo()));
                if(novoObj.getPacoteVO().getPacoteGenerico()){
                    sql = new StringBuilder();
                    sql.append(" SELECT COUNT(*) as total FROM sch_estudio.agenda_agendar ");
                    sql.append(" WHERE agenda_agendar.id_vendaavulsa = ").append(codigoVendaAvulsa);
                    sql.append(" AND id_produto =  \n");
                    sql.append(" (SELECT id_produto FROM sch_estudio.pacote_produto WHERE id_pacote = ").append(novoObj.getPacoteVO().getCodigo()).append(")");
                    ResultSet resultadoTab = SuperFacadeJDBC.criarConsulta(sql.toString(),con);
                    while (resultadoTab.next())
                    totalProdutosAAgenda = resultadoTab.getInt("total");

                    sql = new StringBuilder();
                    sql.append(" UPDATE sch_estudio.agenda SET produto_generico = true\n");
                    sql.append(" WHERE id_agenda in\n");
                    sql.append(" (SELECT id_agenda FROM sch_estudio.agenda_venda WHERE id_vendaavulsa = ").append(codigoVendaAvulsa);
                    sql.append(" limit ").append(novoObj.getPacoteVO().getQtdProdutosGenericos() - totalProdutosAAgenda).append(" )");
                    sql.append(" AND id_produto in\n");
                    sql.append(" (SELECT id_produto FROM sch_estudio.pacote_produto_generico WHERE id_pacote = ").append(novoObj.getPacoteVO().getCodigo()).append(")");

                    SuperFacadeJDBC.executarConsultaUpdate(sql.toString(),con);
                    
                    sql = new StringBuilder();
                    sql.append("update sch_estudio.agenda_excecao  set produto_generico = true ");
                    sql.append("where id_vendaavulsa = ").append(codigoVendaAvulsa);
                    sql.append(" and (id_produto in ");
                    sql.append(" (SELECT id_produto FROM sch_estudio.pacote_produto_generico WHERE id_pacote = ").append(novoObj.getPacoteVO().getCodigo()).append(")");
                    sql.append(" or id_produto in (SELECT codigo FROM produto WHERE descricao = 'PRODUTO GENÉRICO'))");
                    try{
                        SuperFacadeJDBC.executarConsultaUpdate(sql.toString(),con);
                    }catch(Exception e){
                    }

                }
            }
        }
    }
}

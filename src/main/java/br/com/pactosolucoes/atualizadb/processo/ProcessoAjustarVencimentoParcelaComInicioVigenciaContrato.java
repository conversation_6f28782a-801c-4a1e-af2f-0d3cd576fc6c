package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Calendar;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAjustarVencimentoParcelaComInicioVigenciaContrato {

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "acadironbergbarrafundasp";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            ajustarVencimentoContratosPorDataInicioContrato(c);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAjustarVencimentoParcelaComInicioVigenciaContrato.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarVencimentoContratosPorDataInicioContrato(Connection con) {
        Log logDAO = null;
        try {
            con.setAutoCommit(true);
            logDAO = new Log(con);

            String parcelas = "'PARCELA 1'";
            for (int x=2;x <= 36; x++) {
                parcelas += ",'PARCELA " + x + "'";
            }

            Uteis.logarDebug("INÍCIO | ProcessoAjustarVencimentoParcelaComInicioVigenciaContrato");
            StringBuilder sql = new StringBuilder();
            sql.append("select distinct cli.matricula, pes.nome, pes.codigo as pessoa, con.codigo as contrato, con.vigenciade, con.vigenciaateajustada, \n");
            sql.append("rec.diavencimentocartao, EXTRACT(DAY FROM con.vigenciade::DATE) as diavencimento\n");
            sql.append("from contrato con\n");
            sql.append("inner join pessoa pes on pes.codigo = con.pessoa\n");
            sql.append("inner join cliente cli on cli.pessoa = pes.codigo\n");
            sql.append("inner join contratorecorrencia rec on rec.contrato = con.codigo\n");
            sql.append("where con.vigenciaateajustada > CURRENT_DATE\n");
            sql.append("AND AGE(con.vigenciaateajustada, con.vigenciade) > INTERVAL '31 days'\n");
            sql.append("and exists (select 1 from movparcela mpar where mpar.descricao ilike 'PARCELA 2' and mpar.contrato = con.codigo and EXTRACT(DAY FROM con.vigenciade::DATE) <> EXTRACT(DAY FROM mpar.datavencimento::DATE))\n");
            sql.append("order by con.codigo");
            try (java.sql.Statement stm = con.createStatement()) {
                try (java.sql.ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        int diaVencimento = rs.getInt("diavencimento");
                        int diaVencimentoUsar = diaVencimento > 30 ? 30 : diaVencimento;
                        int pessoa = rs.getInt("pessoa");
                        int contrato = rs.getInt("contrato");
                        Date dataBase = Uteis.obterPrimeiroDiaMes(rs.getDate("vigenciade"));
                        String nomePessoa = rs.getString("nome");
                        String matricula = rs.getString("matricula");

                        boolean alterouParcela = false;
                        StringBuilder mensagem = new StringBuilder();

                        StringBuilder sqlParcelas = new StringBuilder();
                        sqlParcelas.append("select * from movparcela where contrato = ").append(contrato).append("\n");
                        sqlParcelas.append("and descricao in (").append(parcelas).append(")\n");
                        try (java.sql.Statement stmPar = con.createStatement()) {
                            try (java.sql.ResultSet rsPar = stmPar.executeQuery(sqlParcelas.toString())) {
                                while (rsPar.next()) {
                                    MovParcelaVO obj = new MovParcelaVO();
                                    obj.setCodigo(rsPar.getInt("codigo"));
                                    obj.setDescricao(rsPar.getString("descricao"));
                                    obj.setDataVencimento(rsPar.getDate("datavencimento"));
                                    obj.setDataCobranca(rsPar.getDate("datacobranca"));

                                    int numParcela = Integer.parseInt(obj.getDescricao().replace("PARCELA ", ""));
                                    Date novaData = Uteis.somarMeses(dataBase, numParcela - 1);
                                    novaData = ajustarDataVencimento(novaData, diaVencimento);

                                    if (!Uteis.datasMesmoDiaMesAno(novaData, obj.getDataVencimento()) || !Uteis.datasMesmoDiaMesAno(novaData, obj.getDataCobranca())) {
                                        StringBuilder sqlUpdate1 = new StringBuilder();
                                        sqlUpdate1.append("update movparcela set \n");
                                        sqlUpdate1.append("datavencimento = '" + Uteis.getDataJDBC(novaData) + " 00:00:00', datacobranca = '" + Uteis.getDataJDBC(novaData) + " 00:00:00'\n");
                                        sqlUpdate1.append("where codigo = " + obj.getCodigo());
                                        try (PreparedStatement psUpdate1 = con.prepareStatement(sqlUpdate1.toString())) {
                                            psUpdate1.execute();
                                        }
                                        mensagem.append(obj.getDescricao()).append(" - Data vencimento = ").append(Calendario.getDataAplicandoFormatacao(obj.getDataVencimento(), "dd/MM/yyyy")).append(" >>>> ").append(Calendario.getDataAplicandoFormatacao(novaData, "dd/MM/yyyy")).append("\n");

                                        String mensagemLog = obj.getDescricao() + ";" + Calendario.getDataAplicandoFormatacao(obj.getDataVencimento(), "dd/MM/yyyy") + ";" + Calendario.getDataAplicandoFormatacao(novaData, "dd/MM/yyyy");
                                        Uteis.logarDebug("CONTRATO " + contrato + ";" + matricula + " - " + nomePessoa + ";" + mensagemLog);

                                        alterouParcela = true;
                                    }
                                }
                            }
                        }

                        StringBuilder sqlUpdate2 = new StringBuilder();
                        sqlUpdate2.append("update contratorecorrencia set diavencimentocartao = " + diaVencimentoUsar + " \n");
                        sqlUpdate2.append("where contrato = " + contrato);

                        try (PreparedStatement psUpdate2 = con.prepareStatement(sqlUpdate2.toString())) {
                            psUpdate2.execute();
                        }

                        if (alterouParcela) {
                            LogVO logVO = new LogVO();
                            logVO.setNomeEntidade("CONTRATO");
                            logVO.setNomeEntidadeDescricao("Contrato");
                            logVO.setChavePrimaria(contrato + "");
                            logVO.setChavePrimariaEntidadeSubordinada("");
                            logVO.setNomeCampo("TODOS");
                            logVO.setValorCampoAnterior("");
                            logVO.setValorCampoAlterado("------------------------------------\n" +
                                    "ATUALIZAÇÃO DA DATA DE VENCIMENTO DAS PARCELAS SEGUINDO A DATA DE INÍCIO DO CONTRATO\n" +
                                    mensagem +
                                    "\nTICKET M1-4413\n" +
                                    "------------------------------------");
                            logVO.setDataAlteracao(Calendario.hoje());
                            logVO.setResponsavelAlteracao("PACTO");
                            logVO.setOperacao("ALTERAÇÃO VENCIMENTO PARCELAS");
                            logVO.setPessoa(pessoa);
                            logDAO.incluir(logVO);
                        }
                    }
                }
            }
            Uteis.logarDebug("FIM | ProcessoAjustarVencimentoParcelaComInicioVigenciaContrato");
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoAjustarVencimentoParcelaComInicioVigenciaContrato - " + ex.getMessage());
        } finally {
            logDAO = null;
        }
    }

    public static Date ajustarDataVencimento(Date data, int diaMes) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);

        // Obtém ano e mês da data
        int ano = calendar.get(Calendar.YEAR);
        int mes = calendar.get(Calendar.MONTH);

        // Define o último dia do mês
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.MONTH, mes);
        calendar.set(Calendar.YEAR, ano);
        int ultimoDia = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

        // Ajusta o dia conforme a regra
        calendar.set(Calendar.DAY_OF_MONTH, Math.min(diaMes, ultimoDia));

        return calendar.getTime();
    }

}
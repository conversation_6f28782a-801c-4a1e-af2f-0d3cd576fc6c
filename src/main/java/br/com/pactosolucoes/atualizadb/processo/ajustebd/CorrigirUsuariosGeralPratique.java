package br.com.pactosolucoes.atualizadb.processo.ajustebd;

import br.com.pactosolucoes.atualizadb.processo.SincronizarUsuarioNovoLogin;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class CorrigirUsuariosGeralPratique {


    private static void corrigirUsuariosGeral(String key, String urlConexao) {
        try {
            Uteis.debug = true;

            try (Connection con = DriverManager.getConnection(urlConexao, "postgres", "pactodb")) {

                // LIMPA TODOS QUE ESTÃO COM ALGUM E-MAIL DE APOIOADM OU SOCIO
                StringBuilder sbSelectUsuariosLimpos = new StringBuilder();
                sbSelectUsuariosLimpos.append("SELECT").append("\n");
                sbSelectUsuariosLimpos.append("usu.username as username,").append("\n");
                sbSelectUsuariosLimpos.append("usu.codigo as codUsuario,").append("\n");
                sbSelectUsuariosLimpos.append("usu.usuariogeral as usuariogeral,").append("\n");
                sbSelectUsuariosLimpos.append("uem.email as email").append("\n");
                sbSelectUsuariosLimpos.append("FROM usuario usu").append("\n");
                sbSelectUsuariosLimpos.append("INNER JOIN usuarioemail uem ON uem.usuario = usu.codigo").append("\n");
                sbSelectUsuariosLimpos.append("WHERE (uem.email ILIKE '<EMAIL>' OR uem.email ILIKE '<EMAIL>');").append("\n");

                List<Integer> codigosUsuariosLimpos = new ArrayList<>();
                ResultSet rs = SuperEntidade.criarConsulta(sbSelectUsuariosLimpos.toString(), con);
                while (rs.next()) {
                    Integer codUsuario = rs.getInt("codUsuario");
                    codigosUsuariosLimpos.add(codUsuario);

                    String updateUsuarioEmail = "UPDATE usuarioemail SET email = NULL, verificado = FALSE WHERE usuario = " + codUsuario;
                    String updateUsuario = "UPDATE usuario SET usuariogeral = '' WHERE codigo = " + codUsuario;
                    Statement sqlUpdateUsuario = con.createStatement();
                    sqlUpdateUsuario.execute(updateUsuarioEmail);
                    sqlUpdateUsuario.execute(updateUsuario);
                }


                StringBuilder sbSelectUsuariosSocioAndFinanceiro = new StringBuilder();
                sbSelectUsuariosSocioAndFinanceiro.append("SELECT").append("\n");
                sbSelectUsuariosSocioAndFinanceiro.append("usu.username as username,").append("\n");
                sbSelectUsuariosSocioAndFinanceiro.append("usu.codigo as codUsuario,").append("\n");
                sbSelectUsuariosSocioAndFinanceiro.append("usu.usuariogeral as usuariogeral,").append("\n");
                sbSelectUsuariosSocioAndFinanceiro.append("uem.email as email").append("\n");
                sbSelectUsuariosSocioAndFinanceiro.append("FROM usuario usu").append("\n");
                sbSelectUsuariosSocioAndFinanceiro.append("INNER JOIN usuarioemail uem ON uem.usuario = usu.codigo").append("\n");
                sbSelectUsuariosSocioAndFinanceiro.append("WHERE (usu.username ILIKE 'socio' OR usu.username ILIKE 'financeiro');").append("\n");
                rs = SuperEntidade.criarConsulta(sbSelectUsuariosSocioAndFinanceiro.toString(), con);

                while (rs.next()) {
                    if (rs.getString("username").equalsIgnoreCase("socio")) {
                        // Se é sócio, vincula ao e-mail SOCIOSGRUPOPRATIQUE
                        Integer codUsuario = rs.getInt("codUsuario");
                        String updateUsuarioEmail = "UPDATE usuarioemail SET email = '<EMAIL>', verificado = TRUE WHERE usuario = " + codUsuario;
                        String updateUsuario = "UPDATE usuario SET usuariogeral = '0becd58f-476b-4032-8030-bcb4ab2b63a6' WHERE codigo = " + codUsuario;
                        Statement sqlUpdateUsuario = con.createStatement();
                        sqlUpdateUsuario.execute(updateUsuarioEmail);
                        sqlUpdateUsuario.execute(updateUsuario);

                    } else if (rs.getString("username").equalsIgnoreCase("financeiro")) {
                        // Se é financeiro, vincula ao e-mail APOIOADMPRATIQUE
                        Integer codUsuario = rs.getInt("codUsuario");
                        String updateUsuarioEmail = "UPDATE usuarioemail SET email = '<EMAIL>', verificado = TRUE WHERE usuario = " + codUsuario;
                        String updateUsuario = "UPDATE usuario SET usuariogeral = 'eb3b5709-c9f0-40ed-ac33-d98dc60aa08b' WHERE codigo = " + codUsuario;
                        Statement sqlUpdateUsuario = con.createStatement();
                        sqlUpdateUsuario.execute(updateUsuarioEmail);
                        sqlUpdateUsuario.execute(updateUsuario);
                    }
                }

                Usuario usuarioDao = new Usuario(con);
                UsuarioVO usuarioResponsavelVO = usuarioDao.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_MINIMOS);
                for (Integer codUsuario : codigosUsuariosLimpos) {
                    SincronizarUsuarioNovoLogin.atualizarUsuarioGeral(codUsuario, con, 1, usuarioResponsavelVO, "", true, key);
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(CorrigirUsuariosGeralPratique.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void main(String... args) {
        HashMap<String, String> mapChaveUrl = new HashMap<>();
//        mapChaveUrl.put("ff9bdc1899b2097474823395db2841c4", "*******************************************************************************");
//        mapChaveUrl.put("77ff8085434367107c64d7f9da792dc0", "*****************************************************************************)");
        mapChaveUrl.put("5c8b2e9a77b7e0f10f8b8612db7359b9", "jdbc:postgresql://************:5492/bdzillyonpratiquegloria");
        mapChaveUrl.put("750241fde1cd5bd7b0032541b191be49", "jdbc:postgresql://************:5492/bdzillyonpratiquebetim");
        mapChaveUrl.put("90feb6cf79c043140bd85d18f7a420ac", "jdbc:postgresql://************:5492/bdzillyonpratiqueunidcardoso");
        mapChaveUrl.put("bb4018128272283142e869f5d34cf084", "jdbc:postgresql://************:5492/bdzillyonpratiquealipiodemelo");
        mapChaveUrl.put("767f1ae6e9272de3e6ce8c42be3ff4ea", "jdbc:postgresql://*************:5432/bdzillyonpratiqueveneza");
        mapChaveUrl.put("e9248c86e86a42cd891b584f786ff3ab", "jdbc:postgresql://************:5492/bdzillyonpratiqueunidadenazare");
        mapChaveUrl.put("a7d256f32ecb1afa0c660048846123ca", "jdbc:postgresql://*************:5432/bdzillyonpratiquemeiapraia");
        mapChaveUrl.put("a61c98c71894331206618d095aadc6a", "jdbc:postgresql://************:5492/bdzillyonpratiquepatobranco");
        mapChaveUrl.put("d76a94be5d04c831f49231c5dab065d5", "jdbc:postgresql://*************:5432/bdzillyonpratiquesaojosecampinas");
        mapChaveUrl.put("d48b4fa775ecc46413730e800ab59c80", "jdbc:postgresql://************:5492/bdzillyonpratiquecastelo");
        mapChaveUrl.put("7d291e4b18a0a0a0898249988c9fc624", "jdbc:postgresql://************:5492/bdzillyonpratiquelagoa");
        mapChaveUrl.put("a48910f14af8f4c8c450a2007f119244", "jdbc:postgresql://************:5492/bdzillyonpratiquexangrilafit");
        mapChaveUrl.put("176d4211bde27432add1be2e768581a0", "************************************************************");
        mapChaveUrl.put("4b36b9842885ebf326ff3a1c182c4092", "jdbc:postgresql://************:5492/bdzillyonpratiquesetelagoas");
        mapChaveUrl.put("cd8c63c16eeef458361510fadfc5b319", "jdbc:postgresql://************:5492/bdzillyonpratiquefernaodias");
        mapChaveUrl.put("87c3b562574301ec96ff898add1a01a9", "jdbc:postgresql://*************:5432/bdzillyonpratiqueaririu");
        mapChaveUrl.put("67da95acc77dda4365adffb6560a924d", "jdbc:postgresql://************:5492/bdzillyonpratiquefitnesssantaines");
        mapChaveUrl.put("28e5dd449b0dc90671f1dc938a05a18d", "jdbc:postgresql://************:5492/bdzillyonpratiquefitnessibirite");
        mapChaveUrl.put("5c27f435bed41b3933a9ef698481f700", "jdbc:postgresql://************:5492/bdzillyonpratiquesaobentomg");
        mapChaveUrl.put("a93b7fb8bc2999f9e2f3c90801809497", "jdbc:postgresql://************:5492/bdzillyonpratiquepradomg");
        mapChaveUrl.put("a360c98fecff5b19e8c3e0d5eb2dcfe3", "jdbc:postgresql://*************:5432/bdzillyonpratiquesaojose");
        mapChaveUrl.put("4761b428f38980a5a3a9c5fada15fa84", "*************************************************************");
        mapChaveUrl.put("442c3f0cf4adcba347aa73d42785bcc8", "jdbc:postgresql://*************:5432/bdzillyonpratiquecachoeirinha");
        mapChaveUrl.put("d8d7b998f15f59f27617b0790702c63e", "jdbc:postgresql://*************:5432/bdzillyonpratiquefitnesssantaegigenia");
        mapChaveUrl.put("970871f7b472090a2fe80eaa9e08711e", "jdbc:postgresql://************:5492/bdzillyonpratiquesaogabriel");
        mapChaveUrl.put("3fcd1b74da3b2c6f680e82b72db39c61", "jdbc:postgresql://*************:5432/bdzillyonaltaenergiacidadenova");
        mapChaveUrl.put("8ac568d74191771ac51536eec0a914c0", "jdbc:postgresql://*************:5432/bdzillyonpratiquejardimatlantico");
        mapChaveUrl.put("19830abc7c9c57d7147b05025f68c5f3", "jdbc:postgresql://*************:5432/bdzillyonpratiquefitnessitajai");
        mapChaveUrl.put("250bb4ef7f65adec2c927caca85a3f9a", "jdbc:postgresql://************:5492/bdzillyonpratiqueindependenciaareias");
        mapChaveUrl.put("6f655e458a8caf8dfec249b995cf0d0f", "jdbc:postgresql://************:5492/bdzillyonpratiquesagradafamilia");
        mapChaveUrl.put("ed5ab38f77ec8b6cb74ada821559487e", "jdbc:postgresql://************:5492/bdzillyonpratiqueceuazul");
        mapChaveUrl.put("55eae2b88e623beb1419e02588c8560b", "jdbc:postgresql://************:5492/bdzillyonpratiqueunidsantahelena");
        mapChaveUrl.put("bcf34b330655edd31d812bd0653dc862", "*********************************************************************");
        mapChaveUrl.put("6718ee34a62862aba2beeb1d9acfceaf", "**********************************************************************");
        mapChaveUrl.put("f7ede23039c6b36f3426a1717e7022ec", "****************************************************************");
        mapChaveUrl.put("c66fa993c772af1fa766826fc9da9669", "*************************************************************");
        mapChaveUrl.put("7c24bb9deb941e9545f1fb18596e060d", "jdbc:postgresql://*************:5432/bdzillyonpratiquefitnessfernaodias");
        mapChaveUrl.put("5cb1f1bc55b1878a48dc01e10e658290", "**********************************************************************");
        mapChaveUrl.put("a5f31ffd648bd313ee8212b87464f6e3", "jdbc:postgresql://*************:5432/bdzillyonpratiqueflorianopolis");
        mapChaveUrl.put("bdfd0b64da6255bdb1658ba11e770fac", "jdbc:postgresql://*************:5432/bdzillyonpratiquepalhocapedrabranca");
        mapChaveUrl.put("56bd14b9d48583896a9622e42158137", "*****************************************************************");
        mapChaveUrl.put("b34b808abb4713d666122b0a8283f15e", "*****************************************************************");
        mapChaveUrl.put("a7773d241986e326045e3d3124d2dc92", "*********************************************************************");
        mapChaveUrl.put("c8d80d04b5a0566016113b15aefe5d5b", "***********************************************************************");
        mapChaveUrl.put("491897ad0047892a0417f289495f01b5", "jdbc:postgresql://*************:5432/bdzillyonpratiquebiguacucentro");
        mapChaveUrl.put("c3f142aff19990abc1b5b9212447303a", "******************************************************************");
        mapChaveUrl.put("76763bdf7e1c3f5b518dd62d8b5766a1", "**************************************************************");
        mapChaveUrl.put("bc90a77b5b1b42a8d392b0be68303e55", "jdbc:postgresql://*************:5432/bdzillyonpratiquesaoleopoldo");
        mapChaveUrl.put("5a8cc4832efe42145cc18ddb1e5ad6f5", "*************************************************************");
        mapChaveUrl.put("ade1581674222d645c6220f3f2ed4d35", "***************************************************************");
        mapChaveUrl.put("8ad6a93a48c86b00edfba545e86d39b1", "***********************************************************");
        mapChaveUrl.put("fd73282d5ca5865952a7b952228d659a", "****************************************************************");
        mapChaveUrl.put("69452aa8b15686834704921d31540db4", "*****************************************************************");
        mapChaveUrl.put("ae66ec91296114207b102e5452f32f6e", "jdbc:postgresql://*************:5432/bdzillyonpratiquemartminas");
        mapChaveUrl.put("61b885f1c340bbb538131fe88caa0a68", "jdbc:postgresql://*************:5432/bdzillyonpratiquevistaalegre");
        mapChaveUrl.put("d3d0b77ac3dea5c6f3fa6ab575524486", "jdbc:postgresql://*************:5432/bdzillyonpowergympacheco");
        mapChaveUrl.put("bf507defb24f6021f1ee0f43cc3722c4", "***********************************************************");
        mapChaveUrl.put("189ae066f0951a18334ca0d147d5927d", "********************************************************************");
        mapChaveUrl.put("cac3c71719378b2377068ccf2303f948", "jdbc:postgresql://*************:5432/bdzillyonpratiqueunidgoiania");
        mapChaveUrl.put("589b54c2e94d0ad20091c3876bd34ddb", "****************************************************************");
        mapChaveUrl.put("703d1805e9b3a0b18a81eb822f66655c", "*****************************************************************");
        mapChaveUrl.put("21e39cadedda34c6e26d4f1d6c897b24", "jdbc:postgresql://*************:5432/bdzillyonpratiqueunidfloramar");
        mapChaveUrl.put("3a19c1e4117c971cea310a84d143dee5", "jdbc:postgresql://*************:5432/bdzillyonpratiqueipiranga");
        mapChaveUrl.put("510a1fde98c85c4ae278696c2ecba4d9", "jdbc:postgresql://************:5492/bdzillyonpratiquesantamonica");
        mapChaveUrl.put("b9055aace082097a99ba272a5613ab5f", "jdbc:postgresql://*************:5432/bdzillyonpratiqueguarani");
        mapChaveUrl.put("d84017d5be86c834cb2d0cd48253785", "jdbc:postgresql://************:5492/bdzillyonpratiqueforquilhasfit");
        mapChaveUrl.put("d826fbbdd2c37d1342b8d16dfa5c75fd", "***********************************************************");
        mapChaveUrl.put("34281f9115b3184e312ca3bb6496f3ad", "jdbc:postgresql://*************:5432/bdzillyonpratiquefitnesssaosebastiao");
        mapChaveUrl.put("8553af5a471fde839264af3fe6d2d81a", "jdbc:postgresql://*************:5432/bdzillyonpratiquefitnesstirol");
        mapChaveUrl.put("ca32ba4f275d27c304da2852b238a023", "jdbc:postgresql://************:5492/bdzillyonpratiqueitabira");
        mapChaveUrl.put("e31e70c08cce88d85a3ec59e29e031a6", "jdbc:postgresql://************:5492/bdzillyonpratiquetrindadesc");
        mapChaveUrl.put("adbd8170be062d57d67b515f59f8a934", "jdbc:postgresql://*************:5432/bdzillyonpowergymfitnessclub");
        mapChaveUrl.put("7bb2d34d3172169c43f083b8c0574a7f", "jdbc:postgresql://*************:5432/bdzillyonpratiquefitnessloanda");
        mapChaveUrl.put("35e6999659075160839ae0171e2e405c", "jdbc:postgresql://************:5492/bdzillyonpratiqueunidpaqueta");
        mapChaveUrl.put("eee722990a3717f4f657059df7e388f3", "*************************************************************************");
        mapChaveUrl.put("76f01cabb189e8d48bc4a382ced0a1dc", "jdbc:postgresql://*************:5432/bdzillyonpratiquevaledojatoba");
        mapChaveUrl.put("950e70287c5020596e0d4be18cf549db", "jdbc:postgresql://*************:5432/bdzillyonpratiquemangabeiras");
        mapChaveUrl.put("be5a2bf9bdaed7df7abd8c9854fca4", "jdbc:postgresql://************:5492/bdzillyonpratiquevendanova");
        mapChaveUrl.put("a86d3f9e83200630a4975aecad8d445e", "jdbc:postgresql://*************:5432/bdzillyonpratiquesomarcas");
        mapChaveUrl.put("fc9f8597610376a7aef4dca025ae9ac7", "jdbc:postgresql://************:5492/bdzillyonpratiquesantamaria");
        mapChaveUrl.put("fa88db166d31f80532374868a46fad", "************************************************************************");
        mapChaveUrl.put("d33e2dc6e025d05489e261b28ca5c6af", "jdbc:postgresql://*************:5432/bdzillyonpratiqueparademinas");
        mapChaveUrl.put("5548559f158335936fdadf3496dc58f9", "jdbc:postgresql://*************:5432/bdzillyonpratiqueitabiragabiroba");
        mapChaveUrl.put("8f9da71d879dc26c071ea956b0d1b9f9", "jdbc:postgresql://************:5492/bdzillyonpratiquetreinamento");
        mapChaveUrl.put("e8249b227b1c3cfb732b8180aa484a38", "jdbc:postgresql://************:5492/bdzillyonpratiqueunidgaveaiimg");
        mapChaveUrl.put("7abe760654464774bb383d3b2153ceb", "jdbc:postgresql://*************:5432/bdzillyonpratiquecamelao");
        mapChaveUrl.put("953fd09bb12467409b834cd00146bf22", "***************************************************************");

        for (Map.Entry<String, String> entry : mapChaveUrl.entrySet()) {
            Uteis.logarDebug("Iniciando " + entry.getValue());
            corrigirUsuariosGeral(entry.getKey(), entry.getValue());
        }
    }

}

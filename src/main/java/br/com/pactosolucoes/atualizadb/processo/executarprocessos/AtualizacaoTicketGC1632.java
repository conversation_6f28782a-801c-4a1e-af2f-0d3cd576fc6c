package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "15/04/2025",
        descricao = "gravar categoria excecao plano",
        motivacao = "GC-1632")
public class AtualizacaoTicketGC1632 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planoexcecao \n" +
                    "ADD COLUMN categoria INTEGER default null,\n" +
                    "ADD CONSTRAINT fk_planoexcecao_categoria \n" +
                    "FOREIGN KEY (categoria) REFERENCES public.categoria(codigo);", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "10/03/2025",
        descricao = "Liberar Parcelas Incorretas Estabelecimento Invalido",
        motivacao = "PAY-396")
public class PAY396LiberarParcelasIncorretasEstabelecimentoInvalido implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("delete from cartaotentativa \n" +
                    "where tipoconveniocobranca = 21 \n" +
                    "and codigoretorno = '1009' \n" +
                    "and data >= '2025-02-27 00:00:00.000' and data <= '2025-02-27 23:59:59.999'", c);
        }
    }
}



package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Hibrael Araujo",
        data = "10/12/2024",
        descricao = "M2-2903 - Mudança de conceito de como apresenta os numeros nos b.is da régua",
        motivacao = "M2-2903 - Mudança de conceito de como apresenta os numeros nos b.is da régua")
public class AtualizacaoTicketM22903 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN qtdDiasEficienciaComunicacao INTEGER;", c);
        }
    }
}

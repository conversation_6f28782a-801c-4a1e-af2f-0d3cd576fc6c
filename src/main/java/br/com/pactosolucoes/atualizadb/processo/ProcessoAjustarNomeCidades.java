package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Cep;
import negocio.facade.jdbc.utilitarias.CidadeCepVO;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

public class ProcessoAjustarNomeCidades {

    public static void main(String... args) {
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"acadironbergbarrafundasp"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            Uteis.debug = true;

            Connection con = new DAO().obterConexaoEspecifica(chave);
            ProcessoAjustarNomeCidades.corrigirCidadesComNomeErrado(con);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void corrigirCidadesComNomeErrado(Connection con) throws Exception {
        try {
            StringBuilder sbCidadesNomeErrados = new StringBuilder();
            sbCidadesNomeErrados.append("SELECT\n");
            sbCidadesNomeErrados.append("\tcid.codigo as codCidade,\n");
            sbCidadesNomeErrados.append("\tcid.nome as nomeCidade,\n");
            sbCidadesNomeErrados.append("\test.sigla as ufEstado,\n");
            sbCidadesNomeErrados.append("\test.descricao as nomeEstado,\n");
            sbCidadesNomeErrados.append("\test.codigo as codigoEstado,\n");
            sbCidadesNomeErrados.append("\test.pais as codigoPais\n");
            sbCidadesNomeErrados.append("FROM cidade cid\n");
            sbCidadesNomeErrados.append("INNER JOIN estado est ON est.codigo = cid.estado\n");
            sbCidadesNomeErrados.append("WHERE cid.nome ILIKE '%?%'\n");
            sbCidadesNomeErrados.append("OR cid.nome ILIKE '%¿%'\n");
            sbCidadesNomeErrados.append("OR cid.nome ILIKE '%½%'\n");
            sbCidadesNomeErrados.append("OR cid.nome ILIKE '%Ï%'\n");
            sbCidadesNomeErrados.append("ORDER BY est.sigla;\n");
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sbCidadesNomeErrados.toString())) {
                    Uteis.logar("INÍCIO | ProcessoAjustarNomeCidades");
                    while (rs.next()) {
                        int codCidadeNomeErrado = rs.getInt("codCidade");
                        String nomeCidadePreparado = rs.getString("nomeCidade").replace("?", "%").replace("¿", "%").replace("½", "%").replace("Ï", "%");
                        String ufEstado = rs.getString("ufEstado");
                        int codEstado = rs.getInt("codigoEstado");
                        int codPais = rs.getInt("codigoPais");
                        // VERIFICA SE JA EXISTE UMA CIDADE COM ESSE NOME NA BASE BDZILLYON DO CLIENTE
                        // SE EXISTIR, EXCLUI A CIDADE COM ESSE NOME ERRADO E MUDA O REGISTRO PARA ESSA CIDADE COM NOME CERTO
                        StringBuilder sbCidadeExistente = new StringBuilder();
                        sbCidadeExistente.append("SELECT\n");
                        sbCidadeExistente.append("\tcid.codigo as codCidade,\n");
                        sbCidadeExistente.append("\tcid.nome as nomeCidade,\n");
                        sbCidadeExistente.append("\test.sigla as ufEstado,\n");
                        sbCidadeExistente.append("\test.descricao as nomeEstado,\n");
                        sbCidadeExistente.append("\test.codigo as codigoEstado\n");
                        sbCidadeExistente.append("FROM cidade cid\n");
                        sbCidadeExistente.append("INNER JOIN estado est ON est.codigo = cid.estado\n");
                        sbCidadeExistente.append("WHERE cid.nome ILIKE ?\n");
                        sbCidadeExistente.append("AND cid.codigo <> ?\n");
                        sbCidadeExistente.append("AND est.sigla ILIKE ?\n");
                        sbCidadeExistente.append("AND (cid.nome NOT ILIKE '%?%' OR cid.nome NOT ILIKE '%¿%' OR cid.nome NOT ILIKE '%½%' OR cid.nome NOT ILIKE '%Ï%')\n");
                        sbCidadeExistente.append("ORDER BY est.sigla LIMIT 1;\n");
                        try (PreparedStatement pstm = con.prepareStatement(sbCidadeExistente.toString())) {
                            pstm.setString(1, nomeCidadePreparado);
                            pstm.setInt(2, codCidadeNomeErrado);
                            pstm.setString(3, ufEstado);
                            try (ResultSet rsCidadeExistente = pstm.executeQuery()) {
                                if (rsCidadeExistente.next()) {
                                    int codCidadeNomeCerto = rsCidadeExistente.getInt("codCidade");
                                    StringBuilder updateCidadePessoa = new StringBuilder();
                                    updateCidadePessoa.append("UPDATE pessoa\n");
                                    updateCidadePessoa.append("SET cidade = ").append(codCidadeNomeCerto).append("\n");
                                    updateCidadePessoa.append("WHERE cidade = ").append(codCidadeNomeErrado).append("\n");
                                    stm.execute(updateCidadePessoa.toString());

                                    StringBuilder deleteCidadeNomeErrado = new StringBuilder();
                                    deleteCidadeNomeErrado.append("DELETE FROM cidade WHERE codigo = ").append(codCidadeNomeErrado);
                                    stm.execute(deleteCidadeNomeErrado.toString());
                                }
                                // COMO NÃO EXISTE CIDADE NA BASE BDZILLYON, O SISTEMA BUSCA NO BDCEP E INCLUI NO BDZILLYON
                                else {
                                    Cep cepDao = new Cep();
                                    CidadeCepVO cidadeCepVO = cepDao.consultarPorCidadeUF(nomeCidadePreparado, ufEstado);
                                    if (cidadeCepVO != null && !UteisValidacao.emptyString(cidadeCepVO.getDescricao())) {
                                        StringBuilder sbInsertCidade = new StringBuilder();
                                        sbInsertCidade.append("INSERT INTO cidade (pais,estado,nome,nomesemacento,homologada)\n");
                                        sbInsertCidade.append("VALUES (?,?,?,?,false);\n");
                                        try (PreparedStatement pstmInsertCidade = con.prepareStatement(sbInsertCidade.toString())) {
                                            pstmInsertCidade.setInt(1, codPais);
                                            pstmInsertCidade.setInt(2, codEstado);
                                            pstmInsertCidade.setString(3, cidadeCepVO.getDescricao());
                                            pstmInsertCidade.setString(4, Uteis.retirarAcentuacao(cidadeCepVO.getDescricao()));
                                            pstmInsertCidade.execute();
                                        }

                                        StringBuilder updateCidadePessoa = new StringBuilder();
                                        updateCidadePessoa.append("UPDATE pessoa\n");
                                        updateCidadePessoa.append("SET cidade = (SELECT MAX(codigo) FROM cidade)\n");
                                        updateCidadePessoa.append("WHERE cidade = ").append(codCidadeNomeErrado).append("\n");
                                        stm.execute(updateCidadePessoa.toString());

                                        StringBuilder deleteCidadeNomeErrado = new StringBuilder();
                                        deleteCidadeNomeErrado.append("DELETE FROM cidade WHERE codigo = ").append(codCidadeNomeErrado);
                                        stm.execute(deleteCidadeNomeErrado.toString());
                                    }
                                }
                            }
                        }
                    }
                    Uteis.logar("FIM | ProcessoAjustarNomeCidades");
                }
            }

        } catch (Exception ignored) {
            ignored.printStackTrace();
        }

    }


    public static void mesclarCidades(Connection con, CidadeVO cidadeVO, String cidadesRemovidas) throws Exception {
        try {
            Uteis.logar("INÍCIO | ProcessoMesclarCidades");
            String nomesCidadesRemovidas = buscarCidadesExcluidas(Conexao.getFromSession(), cidadeVO, cidadesRemovidas);
            Uteis.logar("Mesclando cidades: Mantendo a cidade " + cidadeVO.getCodigo() + " - " + cidadeVO.getNome() + " e excluindo a(s) " + nomesCidadesRemovidas);
            SuperFacadeJDBC.executarUpdate("UPDATE pessoa SET cidade = "+cidadeVO.getCodigo()+" WHERE cidade in ("+cidadesRemovidas+")", con);
            SuperFacadeJDBC.executarUpdate("DELETE FROM cidade WHERE codigo in ("+cidadesRemovidas+") and codigo <> "+cidadeVO.getCodigo(), con);
        } catch (Exception ex){
            Uteis.logar("ERRO | ProcessoMesclarCidades - "+ex.getMessage());
            throw ex;
        }
        Uteis.logar("FIM | ProcessoMesclarCidades");
    }


    public static String buscarCidadesExcluidas(Connection con, CidadeVO cidadeVO, String cidadesRemovidas) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select STRING_AGG(nome, ',' ORDER BY nome) as cidades \n");
            sql.append("from cidade \n");
            sql.append("where codigo in (").append(cidadesRemovidas).append(") \n");
            if (cidadeVO != null &&  !UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
                sql.append("and codigo <> ").append(cidadeVO.getCodigo());
            }
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    if (rs.next()) {
                        return rs.getString("cidades");
                    }
                }
            }
        } catch (Exception ex){
            throw ex;
        }
        return "";
    }

    public static CidadeVO buscarCidadeMantida(Connection con, Integer codigo) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select codigo, nome \n");
            sql.append("from cidade \n");
            sql.append("where codigo = ").append(codigo);
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    if (rs.next()) {
                        CidadeVO cidadeVO = new CidadeVO();
                        cidadeVO.setCodigo(rs.getInt("codigo"));
                        cidadeVO.setNome(rs.getString("nome"));
                        return cidadeVO;
                    }
                }
            }
        } catch (Exception ex){
            throw ex;
        }
        return null;
    }

}

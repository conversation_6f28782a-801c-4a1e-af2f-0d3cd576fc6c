package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.*;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by glauco on 06/03/14
 */
public class CorrigirVencimentoParcelasRecorrencia extends SuperEntidade {

    public CorrigirVencimentoParcelasRecorrencia(Connection conexao) throws Exception {
        super(conexao);
    }

    public static void main(String[] args) {
        try {
            Connection con1;
            if (args.length == 0) {
                con1 = DriverManager.getConnection("*************************************************", "postgres", "pactodb");
            } else {
                con1 = new DAO().obterConexaoEspecifica(args[0]);
            }
            Conexao.guardarConexaoForJ2SE(con1);
            new CorrigirVencimentoParcelasRecorrencia(con1).executarProcesso();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void executarProcesso() throws SQLException {
        try {
            System.out.println("Iniciou em : " + Calendario.hoje());
            MovParcela movParcela = new MovParcela(con);
            con.setAutoCommit(false);
            List<MovParcelaVO> listaParcelasErradas = consultarParcelasErradas();
            for (MovParcelaVO parcelaErrada : listaParcelasErradas) {
                Date dataVencimento = parcelaErrada.getDataVencimento();
                Date possivelData = Uteis.somarCampoData(dataVencimento, Calendar.MONTH, -1);
                if (Calendario.menorOuIgual(possivelData, Calendario.hoje())) {
                    possivelData = Calendario.proximo(Calendar.DATE, Calendario.hoje());
                }
                parcelaErrada.setDataVencimento(possivelData);
                movParcela.alterar(parcelaErrada);
                System.out.println("Contrato " + parcelaErrada.getContrato().getCodigo() + " ajustado para " + possivelData);
            }
            con.commit();
            System.out.println("Terminou em : " + Calendario.hoje());
        } catch (Exception e) {
            if (con != null) {
                con.rollback();
            }
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
            }
        }
    }

    private List<MovParcelaVO> consultarParcelasErradas() throws Exception {
        String sql = "SELECT\n" +
                "  mp.*\n" +
                "FROM movparcela mp\n" +
                "  INNER JOIN contrato c\n" +
                "    ON mp.contrato = c.codigo\n" +
                "WHERE mp.datavencimento > c.vigenciaateajustada\n" +
                "      AND mp.dataregistro :: DATE > '21/02/2014'\n" +
                "      AND mp.situacao = 'EA';";

        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();

        return MovParcela.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }
}
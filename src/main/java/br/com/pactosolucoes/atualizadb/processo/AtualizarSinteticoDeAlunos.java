/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

/**
 *
 * <AUTHOR>
 */
public class AtualizarSinteticoDeAlunos {
    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("*******************************************************", "postgres", "pactodb");
            String script = "";
            String select = "";
            corrigirSituacaoClienteScripSelect(con1,script,select, SituacaoClienteSinteticoEnum.GRUPO_TODOS);

        } catch (Exception ex) {
            Logger.getLogger(CorrigirProdutosDeMultaEJuros.class.getName()).log(Level.SEVERE, null, ex);
        }
    }


    public static void corrigirSituacaoClienteScripSelect(Connection con, final String script, final String select,SituacaoClienteSinteticoEnum dadosAtualizar) throws  Exception{
        if(UteisValidacao.emptyString(select)){
            return;
        }
        ZillyonWebFacade ZWFacade = new ZillyonWebFacade(con);
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(select, con);
        
        if(!UteisValidacao.emptyString(script)){
            SuperFacadeJDBC.executarConsultaUpdate(script, con);
        }
        int cont = 0;
        while (consulta.next()) {
            ClienteVO cli = ZWFacade.getSituacaoClienteSinteticoDW().consultarClientePreparado(consulta.getInt("pessoa"));
            if (cli.getCodigo() != 0) {
                ZWFacade.atualizarSintetico(cli, Calendario.hoje(), dadosAtualizar, true);
            }
        }
    }
    
}

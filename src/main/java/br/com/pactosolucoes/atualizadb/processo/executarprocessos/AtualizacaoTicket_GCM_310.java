package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Matheus Cassimiro",
        data = "23/04/2025",
        descricao = "Configuração para permitir que usuários estornem contratos lançados nos últimos 30 minutos, mesmo sem permissão 3.19.",
        motivacao = "GCM-310"
)
public class AtualizacaoTicket_GCM_310 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE ConfiguracaoSistema ADD COLUMN permiteEstornarContrato30MinAposLancamento BOOLEAN DEFAULT FALSE;", c);
        }
    }

}

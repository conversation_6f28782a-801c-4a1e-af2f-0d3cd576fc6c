package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Marcos Andre",
        data = "24/07/2024",
        descricao = "Adicionar as novas permissões aos perfis corretos",
        motivacao = "M1-2255")
public class AdicionaColunaBloqueioTotem implements MigracaoVersaoInterface{
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN bloquearAcessoMatriculaRematriculaTotemSemPagamento boolean NOT NULL DEFAULT false;", c);
        }
    }
}

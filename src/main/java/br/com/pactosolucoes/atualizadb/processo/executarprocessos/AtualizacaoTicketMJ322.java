package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.ProcessoAjustarParcelasZeradasEmAberto;
import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "14/11/2024",
        descricao = "Gerar pagamento para parcelas com valor zerado e sem pagamento, que são da nova tela de venda avulsa",
        motivacao = "MJ-322")
public class AtualizacaoTicketMJ322 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ProcessoAjustarParcelasZeradasEmAberto.ajustarParcelasZeradasEmAberto(c);
        }
    }
}

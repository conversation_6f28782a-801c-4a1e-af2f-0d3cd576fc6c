package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "04/08/2025",
        descricao = "Add column integracaofoguetehabilitada empresa",
        motivacao = "GC-2694")
public class AtualizacaoTicketGC2694 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN integracaofoguetehabilitada boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa e SET integracaofoguetehabilitada = cfg.habilitada FROM configuracaointegracaofoguete cfg\n" +
                    "\tWHERE cfg.empresa = e.codigo;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.basico.ClienteVO;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Colaborador;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

public class TransferirCarteirasConsultores {
	// FABIANE SA CALIXTO - COD 6 ----> MURILO DA SILVA CÂNEPA - COD 103
	// JOELMA REGINA - COD 5 --------> GEISE KELLY PINHEIRO DE SOUZA - COD 108
	
	
	public static void main(String[] args) {
		try {
			System.out.println("Iniciou em : "+Calendario.hoje());
			Connection con1 = DriverManager.getConnection("*****************************************************", "zillyonweb", "pactodb");
			transferir(con1, 6, 103, TipoColaboradorEnum.CONSULTOR.getSigla());
			transferir(con1, 5, 108, TipoColaboradorEnum.CONSULTOR.getSigla());
			System.out.println("Terminou em : "+Calendario.hoje());
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	
	public static void transferir(Connection con, Integer codigoOrigem,
                Integer codigoDestino, String tipoVinculo){
		try{
			PreparedStatement stm = con.prepareStatement("SELECT * FROM vinculo WHERE tipovinculo LIKE '"+tipoVinculo+"' AND colaborador = "+codigoOrigem);
                        Colaborador colaborador = new Colaborador(con);
                        String nomeOrigem = colaborador.obterNomePessoa(codigoOrigem);
                        String nomeDestino = colaborador.obterNomePessoa(codigoDestino);
			ResultSet resultSet = stm.executeQuery();
                        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
			while(resultSet.next()){
				Date data = Calendario.hoje();
                                int codCliente = resultSet.getInt("cliente");
				inserirHistorico(con, codigoOrigem, codCliente, data, "SD", tipoVinculo);
				inserirHistorico(con, codigoDestino, codCliente, Uteis.somarCampoData(data, Calendar.MILLISECOND, 1), "EN", tipoVinculo);
                                
                                

			}
			con.prepareStatement("UPDATE vinculo SET colaborador = "+codigoDestino+" WHERE colaborador = "+codigoOrigem+" AND tipovinculo LIKE '"+tipoVinculo+"'").execute();
                        zwFacade.getSituacaoClienteSinteticoDW().atualizarVinculos(
                                tipoVinculo, nomeOrigem, nomeDestino);
                        zwFacade = null;
		}catch (Exception e) {
			System.out.println(e.getMessage());
		}
		
		
	}

	/**
	 * <AUTHOR> Alcides
	 * 21/03/2013
	 */
	private static void inserirHistorico(Connection con, Integer codigo, Integer cliente, Date data, String tipo, String tipoVinc) throws SQLException {
		PreparedStatement stmHistVinculo = con.prepareStatement("INSERT INTO historicovinculo(dataregistro, tipohistoricovinculo, " +
				"tipocolaborador, cliente, colaborador, origem) " +
				" VALUES (?,?,?,?,?,?)");
		stmHistVinculo.setTimestamp(1, Uteis.getDataJDBCTimestamp(data));
		stmHistVinculo.setString(2, tipo);
		stmHistVinculo.setString(3, tipoVinc);
		stmHistVinculo.setInt(4, cliente);
		stmHistVinculo.setInt(5, codigo);
		stmHistVinculo.setString(6, "PROCESSO DE TRANSFERENCIA");
		stmHistVinculo.execute();
	}
}

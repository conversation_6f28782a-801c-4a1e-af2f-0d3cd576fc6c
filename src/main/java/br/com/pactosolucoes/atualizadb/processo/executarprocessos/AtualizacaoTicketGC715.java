package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "22/07/2024",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-715")
public class AtualizacaoTicketGC715 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE categoria ADD COLUMN obrigatorioCnpjClienteSesi BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO configuracaosistemacadastrocliente (nome,obrigatorio,mostrar,pendente,visitante,validarcatraca) VALUES ('CNPJ Sesi',false,false,false,true,false);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO configuracaosistemacadastrocliente (nome,obrigatorio,mostrar,pendente,visitante,validarcatraca) VALUES ('CNPJ Sesi',false,false,false,false,false);", c);
        }
    }
}

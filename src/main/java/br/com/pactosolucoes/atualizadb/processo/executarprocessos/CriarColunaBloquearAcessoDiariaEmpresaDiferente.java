package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "19/07/2024",
        descricao = "Bloquear alunos com diária de outra unidade",
        motivacao = "M1-2302")
public class CriarColunaBloquearAcessoDiariaEmpresaDiferente implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN bloquearAcessoDiariaEmpresaDiferente BOOLEAN DEFAULT FALSE", c);
        }
    }
}

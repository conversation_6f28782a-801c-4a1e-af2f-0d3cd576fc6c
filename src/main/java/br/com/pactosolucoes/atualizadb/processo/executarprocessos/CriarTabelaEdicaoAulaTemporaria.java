package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Joao Alcides",
        data = "09/10/2024",
        descricao = "Cria tabela edicaoaulatemporaria",
        motivacao = "Criar tabela para armazenar edições temporárias de aulas")
public class CriarTabelaEdicaoAulaTemporaria implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE edicaoaulatemporaria (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    professor int4,\n" +
                    "    ambiente int4,\n" +
                    "    nivel varchar(255),\n" +
                    "    horarioinicial varchar(255),\n" +
                    "    horariofinal varchar(255),\n" +
                    "    diaaula timestamp,\n" +
                    "    capacidade int4,\n" +
                    "    idademinimaanos int4,\n" +
                    "    idademinimameses int4,\n" +
                    "    idademaximaanos int4,\n" +
                    "    idademaximameses int4,\n" +
                    "    usuarioalterou int4,\n" +
                    "    horarioturma int4,\n" +
                    "    tolerancia int4,\n" +
                    "    tipotolerancia varchar(255),\n" +
                    "    diasemana varchar(255),\n" +
                    "    mensagem text ,\n" +
                    "    nsu text,\n" +
                    "    aulagympass boolean,\n" +
                    "    aulatotalpass boolean,\n" +
                    "    validarmodalidadecontratocheckin boolean,\n" +
                    "    controlarcheckin boolean,\n" +
                    "    tipoalteracao text ,\n" +
                    "    limite timestamp );", c);
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.impl.dcc.base.DCCAttEnum;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;


public class ProcessoReabrirRemessa {

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("*********************************************************", "postgres", "pactodb");

            String remessas = "9310";
            reabrirRemessas(remessas, con);
        } catch (Exception ex) {
            // TODO Auto-generated catch block
            ex.printStackTrace();
        }
    }

    private static void reabrirRemessas(String remessas, Connection con) throws Exception {
        try {
            System.out.println("Reabrir Remessas - Início em : " + new Date() + " --> Remessas " + remessas);
            con.setAutoCommit(false);

            ajustarMovParcelas(remessas, con);
            ajustarRemessaItem(remessas, con);
            ajustarRemessa(remessas, con);

            con.commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
        } finally {
            con.setAutoCommit(true);
        }
    }

    private static void ajustarMovParcelas(String remessas, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from ( \n");
        sql.append("select  \n");
        sql.append("ri.movparcela \n");
        sql.append("from remessaitem ri \n");
        sql.append("where ri.remessa in (").append(remessas).append(") \n");
        sql.append("union \n");
        sql.append("select  \n");
        sql.append("rim.movparcela \n");
        sql.append("from remessaitem ri \n");
        sql.append("inner join remessaitemmovparcela rim on ri.codigo = rim.remessaitem \n");
        sql.append("where ri.remessa in (").append(remessas).append(")) as sql \n");
        sql.append("where movparcela is not null ");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {

            Integer movParcela = rs.getInt("movparcela");
            SuperFacadeJDBC.executarUpdate("update movparcela set situacao = 'EA' where codigo = " + movParcela, con);
            SuperFacadeJDBC.executarUpdate("update movprodutoparcela set recibopagamento = null where movparcela = " + movParcela, con);
            SuperFacadeJDBC.executarUpdate("update movproduto set situacao = 'EA' where codigo in (select movproduto from movprodutoparcela where movparcela = " + movParcela + " )", con);
            SuperFacadeJDBC.executarUpdate("delete from pagamentomovparcela where movparcela = " + movParcela, con);
        }
    }

    private static void ajustarRemessaItem(String remessas, Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo,props from remessaitem where remessa in (" + remessas + ")", con);
        while (rs.next()) {

            RemessaItemVO remessaItemVO = new RemessaItemVO();
            remessaItemVO.setCodigo(rs.getInt("codigo"));
            remessaItemVO.setProps(Uteis.obterMapFromString(rs.getString("props")));

            remessaItemVO.getProps().remove(DCCAttEnum.StatusVenda.name());
            remessaItemVO.getProps().remove(DCCAttEnum.CodigoAutorizacao.name());

            SuperFacadeJDBC.executarUpdate("update remessaitem set props = '" + remessaItemVO.getProps().toString() + "', movpagamento = null where codigo = " + remessaItemVO.getCodigo(), con);
        }
    }

    private static void ajustarRemessa(String remessas, Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo,props from remessa where codigo in (" + remessas + ")", con);
        while (rs.next()) {

            RemessaVO remessaVO = new RemessaVO();
            remessaVO.setCodigo(rs.getInt("codigo"));
            remessaVO.setProps(Uteis.obterMapFromString(rs.getString("props")));

            remessaVO.getProps().remove(DCCAttEnum.DataHoraRetorno.name());
            remessaVO.getProps().remove(DCCAttEnum.CodUsuarioRetorno.name());
            remessaVO.getProps().remove(DCCAttEnum.NomeUsuarioRetorno.name());

            SuperFacadeJDBC.executarUpdate("update remessa set situacaoremessa = " + SituacaoRemessaEnum.REMESSA_ENVIADA.getId() + ", props = '" + remessaVO.getProps().toString() + "' where codigo = " + remessaVO.getCodigo(), con);
        }
    }
}

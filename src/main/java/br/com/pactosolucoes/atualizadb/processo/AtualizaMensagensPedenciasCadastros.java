/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import controle.basico.ConfiguracaoSistemaControle;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.ColunaVO;
import negocio.comuns.arquitetura.RoboTransientObjectsVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import java.sql.DriverManager;

/**
 *
 * <AUTHOR>
 */
public class AtualizaMensagensPedenciasCadastros {


    public static void main(String... args) {

        try {
             Conexao.guardarConexaoForJ2SE(new DAO().obterConexaoEspecifica(args.length > 0
                  ? args[0] : "sereia"));
           // Conexao.guardarConexaoForJ2SE(DriverManager.getConnection("**********************************************", "postgres", "pactodb"));
            ClienteControle controle = new  ClienteControle();
            controle.atualizarMensagensCadastroClientes();
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(
                    Level.SEVERE, null, ex);
        }

    }

   
}


package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "19/11/2024",
        descricao = "Add coluna modalidadesIniciarSelecionadasContratoTurma na tabela vendasonlineconfig",
        motivacao = "M2-2735")
public class M22735AddColumModalidadesIniciarSelecionadasContratoTurma implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE vendasonlineconfig ADD COLUMN modalidadesIniciarSelecionadasContratoTurma Boolean DEFAULT true;",
                    c
            );
        }
    }
}

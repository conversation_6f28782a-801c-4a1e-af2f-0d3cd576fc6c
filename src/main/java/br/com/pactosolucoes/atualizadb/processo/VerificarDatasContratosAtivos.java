package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class VerificarDatasContratosAtivos {

    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("*******************************************", "zillyonweb", "pactodb");
            validarVigenciaContrato(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void validarVigenciaContrato(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select codigo as contrato, vigenciade, vigenciaate, vigenciaateajustada,vendaCreditoTreino ,(select max(dthrentrada) from acessocliente  where cliente in (select codigo from cliente where pessoa = c.pessoa)  ) as ultimoacesso from contrato c where codigo = '42129' and vigenciaateajustada > '2021-08-15' and situacao ='AT' and exists (select codigo from contratooperacao   where contrato = c.codigo and tipooperacao not in ('CA','AD','AH','IM','EM','AM','AC')) order by 1 ", con);
        int count = 0;
        while (consulta.next()) {
            try {
                ResultSet consultaoperacoes = SuperFacadeJDBC.criarConsulta("select  tipooperacao, datafimefetivacaooperacao,nrdiasoperacao,  datainicioefetivacaooperacao  from contratooperacao  where   contrato = " + consulta.getInt("contrato")+" and tipooperacao not in ('CA','AD','AH','IM','EM','AM','AC') order by codigo ", con);
                List<Date> diasUteis = Uteis.getDiasEntreDatas(consulta.getDate("vigenciade"), consulta.getDate("vigenciaate"));
                List<Date> diasBonus = new ArrayList<Date>();
                while (consultaoperacoes.next()) {
                    String tipoOperacao = consultaoperacoes.getString("tipooperacao");
                    if (tipoOperacao.equals("BA")) {
                        Date dataAdicionar = diasBonus.isEmpty() ? diasUteis.get(diasUteis.size() - 1) : diasBonus.get(diasBonus.size() - 1);
                        for (int i = 1; i <= consultaoperacoes.getInt("nrdiasoperacao"); i++) {
                            diasBonus.add(Uteis.somarDias(dataAdicionar, i));
                        }
                    } else if (tipoOperacao.equals("BR")) {
                        for (int i = 1; i <= consultaoperacoes.getInt("nrdiasoperacao"); i++) {
                            if (!diasBonus.isEmpty()) {
                                diasBonus.remove(diasBonus.size() - 1);
                            } else {
                                diasUteis.remove(diasUteis.size() - 1);
                            }
                        }
                    } else if (tipoOperacao.equals("TE")) {
                        for (int day = 1; day <= consultaoperacoes.getInt("nrdiasoperacao"); day++) {
                            diasUteis.add(Uteis.somarDias(diasUteis.get(diasUteis.size() - 1), 1));
                        }
                    } else if (tipoOperacao.equals("CR") || tipoOperacao.equals("AT") || tipoOperacao.equals("TR") || tipoOperacao.equals("BC")) {
                        List<Date> datasOperacao = Uteis.getDiasEntreDatas(consultaoperacoes.getDate("datainicioefetivacaooperacao"), consultaoperacoes.getDate("datafimefetivacaooperacao"));
                        for (Date dataOperacao : datasOperacao) {
                            if (diasUteis.contains(dataOperacao)) {
                                diasUteis.add(Uteis.somarDias(diasUteis.get(diasUteis.size() - 1), 1));
                                diasUteis.remove(dataOperacao);
                                if (diasBonus.contains(diasUteis.get(diasUteis.size() - 1))) {
                                    diasBonus.add(Uteis.somarDias(diasBonus.get(diasBonus.size() - 1), 1));
                                    diasBonus.remove(diasUteis.get(diasUteis.size() - 1));
                                }
                            } else if (diasBonus.contains(dataOperacao)) {
                                diasBonus.remove(dataOperacao);
                                if (consulta.getBoolean("vendaCreditoTreino") || tipoOperacao.equals("BC") || tipoOperacao.equals("TR")) { // operações que abonam dias de bonus
                                    diasBonus.add(Uteis.somarDias(diasBonus.get(diasBonus.size() - 1), 1));
                                }
                            }
                        }
                    }
                }
                Date datafinal =  diasBonus.isEmpty() ? diasUteis.get(diasUteis.size() - 1) : diasBonus.get(diasBonus.size() - 1);
                if (!Calendario.igual(datafinal, consulta.getDate("vigenciaateajustada"))){
                    String descricao =  Calendario.maior(datafinal,consulta.getDate("vigenciaateajustada")) ? " está vigencia menor." :  " está vigencia maior.";
                    System.out.println(++count + " - contrato " + consulta.getInt("contrato") + descricao + " termina dia   " + Uteis.getData(consulta.getDate("vigenciaateajustada")) + " mas deveria terminar  " + Uteis.getData(datafinal)+". Data ultimo acesso = " +  Uteis.getData(consulta.getDate("ultimoacesso")));
                }
            } catch (Exception e) {
                System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " teve erro na alteração:  " + e.getMessage());
            }
        }
    }
}

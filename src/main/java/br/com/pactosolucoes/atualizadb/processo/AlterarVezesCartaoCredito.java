/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.CartaoCredito;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class AlterarVezesCartaoCredito {

    public static void main(String... args) {
        try {
            DAO dao = new DAO();

            String nomeThread = args.length > 0 ? args[0] : "bdzillyonsandiego-2016-01-19";
            Integer nrVezes = 12;
            if (args.length > 1) {
                nrVezes = Integer.parseInt(args[1]);
            }

            Connection con = dao.obterConexaoEspecifica(nomeThread);
            Conexao.guardarConexaoForJ2SE(con);

            processar(nrVezes, con);
        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void processar(Integer qtdVezes, Connection con) throws Exception {
        StringBuilder sqlMovPagamentos = new StringBuilder();
        sqlMovPagamentos.append("SELECT mp.*\n");
        sqlMovPagamentos.append("FROM movpagamento mp\n");
        sqlMovPagamentos.append("  INNER JOIN formapagamento fp ON fp.codigo = mp.formapagamento\n");
        sqlMovPagamentos.append("  LEFT JOIN operadoracartao oc ON oc.codigo = mp.operadoracartao\n");
        sqlMovPagamentos.append("WHERE 1 = 1\n");
        sqlMovPagamentos.append("      AND tipoformapagamento = 'CA'\n");
        sqlMovPagamentos.append("      AND nrparcelacartaocredito <> ").append(qtdVezes).append("\n");
        sqlMovPagamentos.append("      AND valor > 0\n");
        sqlMovPagamentos.append("      AND mp.conveniocobranca IS NULL\n");
        sqlMovPagamentos.append("      AND (oc.codigo IS NULL OR oc.descricao NOT LIKE '%DCC%')\n");
        sqlMovPagamentos.append("      AND mp.codigo NOT IN (SELECT DISTINCT cc.movpagamento\n");
        sqlMovPagamentos.append("                                   FROM nfseemitida\n");
        sqlMovPagamentos.append("                                     LEFT JOIN cartaocredito cc ON cc.codigo = nfseemitida.cartaocredito\n");
        sqlMovPagamentos.append("                                   WHERE cartaocredito IS NOT NULL)");
        sqlMovPagamentos.append("ORDER BY valor ASC");

        PreparedStatement sql = con.prepareStatement(sqlMovPagamentos.toString());
        ResultSet rs = sql.executeQuery();

        CartaoCredito cartaoCredito = new CartaoCredito(con);
        ProdutosPagosServico produtosPagosServico = new ProdutosPagosServico();

        while (rs.next()) {
            List<CartaoCreditoVO> novosCartoesCredito = new ArrayList<CartaoCreditoVO>();
            MovPagamentoVO movPagamentoVO = MovPagamento.montarDados(rs, Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO, con);

            cartaoCredito.excluirCartaoCreditos(movPagamentoVO.getCodigo());

            for (int i = 0; i < 12; i++) {
                CartaoCreditoVO cartaoCreditoVO = new CartaoCreditoVO();

                cartaoCreditoVO.setDataCompensacao(Uteis.somarDias(movPagamentoVO.getDataLancamento(), 30 * (1 + i)));
                cartaoCreditoVO.setMovpagamento(movPagamentoVO);
                cartaoCreditoVO.setValor(Uteis.arredondarForcando2CasasDecimais(movPagamentoVO.getValor() / 12));
                cartaoCreditoVO.setOperadora(movPagamentoVO.getOperadoraCartaoVO());
                cartaoCreditoVO.setSituacao("EA");
                cartaoCreditoVO.setValorTotal(Uteis.arredondarForcando2CasasDecimais(movPagamentoVO.getValor() / 12));

                novosCartoesCredito.add(cartaoCreditoVO);
            }

            StringBuilder sqlAtualizarMovPagamento = new StringBuilder();
            sqlAtualizarMovPagamento.append("UPDATE movpagamento SET nrparcelacartaocredito = ").append(qtdVezes).append(" WHERE codigo = ").append(movPagamentoVO.getCodigo());
            PreparedStatement psAtualizar = con.prepareStatement(sqlAtualizarMovPagamento.toString());
            psAtualizar.execute();

            for (CartaoCreditoVO cartaoCreditoVO : novosCartoesCredito) {
                cartaoCredito.incluir(cartaoCreditoVO);
            }

            produtosPagosServico.setarProdutosPagos(con, movPagamentoVO.getReciboPagamento().getCodigo());
        }
    }
}

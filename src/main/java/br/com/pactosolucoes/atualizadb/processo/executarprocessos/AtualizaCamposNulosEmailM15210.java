package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Paulo Henrique Batista de Jesus",
        data = "15/04/2025",
        descricao = "Atualizar campos nulos de bloqueadobounce e receberemailnovidades para false",
        motivacao = "Correção de dados para campos que ficam nulos ao adicionar emails")
public class AtualizaCamposNulosEmailM15210 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "UPDATE email SET bloqueadobounce = false WHERE bloqueadobounce IS NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "UPDATE email SET receberemailnovidades = false WHERE receberemailnovidades IS NULL;", c);
        }
    }


}

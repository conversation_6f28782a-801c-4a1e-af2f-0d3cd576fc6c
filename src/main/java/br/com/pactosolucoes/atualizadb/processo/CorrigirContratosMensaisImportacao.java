/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class CorrigirContratosMensaisImportacao {

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("*********************************************************", "postgres", "pactodb");
            excluirContratosImportadosErrados(con);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void excluirContratosImportadosErrados(Connection con) throws SQLException {
        try {
            con.setAutoCommit(false);
            StringBuilder sql = new StringBuilder();

            String dataSql = "'2022-11-01'";

            sql.append("SELECT DISTINCT pes.codigo as pessoa FROM contrato con \n");
            sql.append("INNER JOIN pessoa pes on pes.codigo = con.pessoa \n");
            sql.append("WHERE vigenciade >= "+dataSql+" \n");
            sql.append("AND situacaocontrato in ('RN', 'RE') \n");
            sql.append("AND con.plano = 1 \n"); //plano importação
            sql.append("ORDER BY pes.codigo desc \n");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                Integer pessoa = rs.getInt("pessoa");

                Cliente clienteDAO = new Cliente(con);
                ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoa, Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);

                StringBuilder sql2 = new StringBuilder();
                sql2.append("SELECT  string_agg(con.codigo::varchar, ',') as contratos FROM contrato con \n");
                sql2.append("INNER JOIN pessoa pes ON pes.codigo = con.pessoa \n");
                sql2.append("WHERE vigenciade >= "+dataSql+" \n");
                sql2.append("AND situacaocontrato in ('RN', 'RE') \n");
                sql2.append("AND con.plano = 1 \n"); //plano importação
                sql2.append("AND pes.codigo = "+pessoa+" \n");
                sql2.append("GROUP BY pes.codigo \n");


                ResultSet rsCodigos = SuperFacadeJDBC.criarConsulta(sql2.toString(), con);
                if (!rsCodigos.next()) {
                    continue;
                }

                String codigosContratos = rsCodigos.getString("contratos");
                
                SuperFacadeJDBC.executarConsulta("DELETE FROM movpagamento WHERE codigo in"
                        + "(SELECT movpagamento FROM pagamentomovparcela WHERE movparcela IN "
                        + "(SELECT codigo FROM movparcela WHERE contrato IN ("+codigosContratos+")));", con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM periodoacessocliente where contratobaseadorenovacao in("+codigosContratos+")", con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM contrato WHERE codigo in("+codigosContratos+")", con);

                ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            }
             con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            e.printStackTrace();
        } finally {
            con.setAutoCommit(true);
        }

    }
}

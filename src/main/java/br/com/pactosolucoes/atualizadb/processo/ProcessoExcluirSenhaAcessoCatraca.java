package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

public class ProcessoExcluirSenhaAcessoCatraca {

    public static void main(String... args) {
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"acadironbergbarrafundasp"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            Uteis.debug = true;

            Connection con = new DAO().obterConexaoEspecifica(chave);
            ProcessoExcluirSenhaAcessoCatraca.excluirSenhaAcessoCatraca(con, true, true);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static Integer contarRegistrosAfetados(Connection con, boolean excluirSenhaColaborador, boolean excluirSenhaAluno) throws Exception {
        Integer quantidadeRegistros = 0;
        try {
            StringBuilder sbSelectSenhas = new StringBuilder();

            if (excluirSenhaColaborador) {
                sbSelectSenhas.append("SELECT COUNT(*) AS quantidadeColaborador\n");
                sbSelectSenhas.append("FROM pessoa pes\n");
                sbSelectSenhas.append("WHERE senhaacesso IS NOT NULL\n");
                sbSelectSenhas.append("AND senhaacesso <> ''\n");
                sbSelectSenhas.append("AND EXISTS (SELECT codigo\n");
                sbSelectSenhas.append("\tFROM colaborador\n");
                sbSelectSenhas.append("\tWHERE pessoa = pes.codigo)\n");

                try (Statement stm = con.createStatement()) {
                    try (ResultSet rs = stm.executeQuery(sbSelectSenhas.toString())) {
                        Uteis.logar("INÍCIO | ProcessoAjustarNomeCidades");
                        if (rs.next()) {
                            quantidadeRegistros += rs.getInt("quantidadeColaborador");
                        }
                    }
                }
            }

            sbSelectSenhas = new StringBuilder();

            if (excluirSenhaAluno) {
                sbSelectSenhas.append("SELECT COUNT(*) AS quantidadeAluno\n");
                sbSelectSenhas.append("FROM pessoa pes\n");
                sbSelectSenhas.append("WHERE senhaacesso IS NOT NULL\n");
                sbSelectSenhas.append("AND senhaacesso <> ''\n");
                sbSelectSenhas.append("AND EXISTS (SELECT codigo\n");
                sbSelectSenhas.append("\tFROM cliente\n");
                sbSelectSenhas.append("\tWHERE pessoa = pes.codigo)\n");

                try (Statement stm = con.createStatement()) {
                    try (ResultSet rs = stm.executeQuery(sbSelectSenhas.toString())) {
                        Uteis.logar("INÍCIO | ProcessoAjustarNomeCidades");
                        if (rs.next()) {
                            quantidadeRegistros += rs.getInt("quantidadeAluno");
                        }
                    }
                }
            }

        } catch (Exception ignored) {
            quantidadeRegistros = 0;
        }

        return quantidadeRegistros;
    }

    public static void excluirSenhaAcessoCatraca(Connection con, boolean excluirSenhaColaborador, boolean excluirSenhaAluno) throws Exception {
        try {
            StringBuilder sbUpdateSenhaAcesso = new StringBuilder();

            Uteis.logar("INÍCIO | ProcessoExcluirSenhaAcessoCatraca");

            if (excluirSenhaColaborador) {
                sbUpdateSenhaAcesso.append("UPDATE pessoa\n");
                sbUpdateSenhaAcesso.append("SET senhaacesso = NULL, liberasenhaacesso = FALSE\n");
                sbUpdateSenhaAcesso.append("WHERE EXISTS (SELECT codigo\n");
                sbUpdateSenhaAcesso.append("\tFROM colaborador\n");
                sbUpdateSenhaAcesso.append("\tWHERE pessoa = pessoa.codigo)\n");
                sbUpdateSenhaAcesso.append("AND senhaacesso IS NOT NULL\n");
                sbUpdateSenhaAcesso.append("AND senhaacesso <> '';\n");
            }

            if (excluirSenhaAluno) {
                sbUpdateSenhaAcesso.append("UPDATE pessoa\n");
                sbUpdateSenhaAcesso.append("SET senhaacesso = NULL, liberasenhaacesso = FALSE\n");
                sbUpdateSenhaAcesso.append("WHERE EXISTS (SELECT codigo\n");
                sbUpdateSenhaAcesso.append("\tFROM cliente\n");
                sbUpdateSenhaAcesso.append("\tWHERE pessoa = pessoa.codigo)");
                sbUpdateSenhaAcesso.append("AND senhaacesso IS NOT NULL\n");
                sbUpdateSenhaAcesso.append("AND senhaacesso <> '';\n");
            }

            try (PreparedStatement preparedStatement = con.prepareStatement(sbUpdateSenhaAcesso.toString())) {
                preparedStatement.execute();
            }

            Uteis.logar("FIM | ProcessoExcluirSenhaAcessoCatraca");

        } catch (Exception ignored) {
            ignored.printStackTrace();
        }

    }

}

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Pessoa;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

public class ProcessoCorrigirPessoaResponsavelCliente {

    public static void corrigirVinculosErrados(Connection con) throws Exception {
        try {
            // Primeiro passo ele remove clientes responsáveis por si mesmo
            String sqlUpdateCliente = "UPDATE cliente SET pessoaresponsavel = NULL";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdateCliente)) {
                sqlAlterar.execute();
            }

            // No segundo passo ele identifica alunos com o mesmo CPF de sua mãe/pai e coloca seu pai como responsável
            corrigirCPFDuplicadosEntreAlunoEResponsavel(con);

            // Agora ele remove o responsável do cliente caso o responsável seja menor de 18y
            String sqlConsultaRespMenor = "SELECT cli.codigo as codigoCliente FROM cliente cli " +
                    "INNER JOIN pessoa pesresp ON cli.pessoaresponsavel = pesresp.codigo " +
                    "WHERE situacao = 'AT' AND pesresp.datanasc > '2005-01-01'";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlConsultaRespMenor)) {
                    while (rs.next()) {
                        int codigoCliente = rs.getInt("codigoCliente");
                        sqlUpdateCliente = "UPDATE cliente SET pessoaresponsavel = NULL WHERE codigo = " + codigoCliente;
                        try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdateCliente)) {
                            sqlAlterar.execute();
                        }
                    }
                }
            }

            zerarCPFMenorIdade(con);

            ProcessoCorrigirPessoaResponsavelCliente.processarCorrecaoPessoaResponsavelCliente(con);

        } catch (Exception ignore) {
        }

    }

    public static void zerarCPFMenorIdade(Connection con) throws Exception {
        String sqlPessoaMenorIdade = "SELECT cli.codigo as codigoCliente, pes.emitirNotaNomeMae AS emitirNotaNomeMae, pes.cfp AS cpf, pes.cpfpai AS cpfpai, pes.cpfmae AS cpfmae, pes.codigo as codigoPessoa FROM cliente cli " +
                "INNER JOIN pessoa pes ON cli.pessoa = pes.codigo " +
                "WHERE pes.datanasc > '2005-01-01' " +
                "AND pes.cfp <> '' " +
                "AND pes.cfp IS NOT NULL ";

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlPessoaMenorIdade)) {
                while (rs.next()) {
                    boolean emitirNotaNomeMae = rs.getBoolean("emitirNotaNomeMae");
                    String cpfpai = rs.getString("cpfpai");
                    String cpfMae = rs.getString("cpfmae");
                    String cpf = rs.getString("cpf");
                    int codigoPessoa = rs.getInt("codigoPessoa");
                    String sqlUpdatePessoa = "";
                    if (UteisValidacao.emptyString(cpfMae) && (emitirNotaNomeMae || (Uteis.formatarCpfCnpj(cpf, true).equals(Uteis.formatarCpfCnpj(cpfMae, true))))) {
                        sqlUpdatePessoa = "UPDATE pessoa SET cpfmae = '" + cpf + "' WHERE codigo = " + codigoPessoa + "; UPDATE pessoa SET cfp = '' WHERE codigo = " + codigoPessoa;
                        try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdatePessoa)) {
                            sqlAlterar.execute();
                        }
                    } else {
                        if (UteisValidacao.emptyString(cpfpai)) {
                            sqlUpdatePessoa = "UPDATE pessoa SET cpfpai = '" + cpf + "' WHERE codigo = " + codigoPessoa + "; UPDATE pessoa SET cfp = '' WHERE codigo = " + codigoPessoa;
                            try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdatePessoa)) {
                                sqlAlterar.execute();
                            }
                        }
                    }
                }
            }
        }

    }

    public static void corrigirCPFDuplicadosEntreAlunoEResponsavel(Connection con) throws Exception {
        String sqlUpdatePessoaCPFRepetido = "UPDATE pessoa SET cfp = '' WHERE cfp = cpfpai AND cfp <> ''; UPDATE pessoa SET cfp = '' WHERE cfp = cpfmae AND cfp <> '';";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdatePessoaCPFRepetido)) {
            sqlAlterar.execute();
        }
        String sqlConsulta = "SELECT cfp AS cpf, cpfpai AS cpfpai, cpfmae AS cpfmae, codigo FROM pessoa p \n" +
                "WHERE ((cpfpai <> '' AND cpfpai IS NOT NULL) OR (cpfmae <> '' AND cpfmae IS NOT NULL)) \n" +
                "AND (cfp <> '' AND cfp IS NOT NULL) ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlConsulta)) {
                while (rs.next()) {
                    String cpf = rs.getString("cpf");
                    String cpfPai = rs.getString("cpfpai");
                    String cpfMae = rs.getString("cpfmae");
                    int codigoPessoa = rs.getInt("codigo");
                    if (Uteis.formatarCpfCnpj(cpf, true).equals(Uteis.formatarCpfCnpj(cpfMae, true)) || Uteis.formatarCpfCnpj(cpf, true).equals(Uteis.formatarCpfCnpj(cpfPai, true))) {
                        String sqlUpdatePessoa = "UPDATE pessoa SET cfp = '' WHERE codigo = " + codigoPessoa + "; UPDATE cliente SET pessoaresponsavel = NULL WHERE pessoa = " + codigoPessoa + ";";
                        try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdatePessoa)) {
                            sqlAlterar.execute();
                        }
                    }
                }
            }
        }
    }

    public static void processarCorrecaoPessoaResponsavelCliente(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cli.codigo AS codigoCliente, pes.cpfpai as cpfPai, pes.cpfmae as cpfMae, pes.* FROM cliente cli\n");
        sql.append("INNER JOIN pessoa pes ON pes.codigo = cli.pessoa\n");
        sql.append("WHERE ((pes.cpfpai <> '' AND pes.cpfpai IS NOT NULL) OR (pes.cpfmae <> '' AND pes.cpfmae IS NOT NULL))\n");
        sql.append("AND (cli.pessoaresponsavel = 0 OR cli.pessoaresponsavel IS NULL) AND cli.situacao = 'AT';\n");

        ConfiguracaoSistema configuracaoSistemaDAO = new ConfiguracaoSistema(con);
        ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    String cpfPai = rs.getString("cpfPai");
                    String cpfMae = rs.getString("cpfMae");
                    Integer codigoCliente = rs.getInt("codigoCliente");
                    if (configuracaoSistemaVO.isValidarCPFResponsaveis() || configuracaoSistemaVO.isUsarNomeResponsavelNota()) {
                        Cliente clienteDAO = new Cliente(con);
                        ClienteVO clienteVO = clienteDAO.consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                        if (clienteVO != null && !UteisValidacao.emptyNumber(clienteVO.getCodigo()) && clienteVO.getDataNasc() != null && Integer.parseInt(clienteVO.getPessoa().getIdadePessoa()) < 18) {
                            if (!UteisValidacao.emptyString(cpfMae)) {
                                Pessoa pessoaDAO = new Pessoa(con);
                                PessoaVO pessoaResponsavelVO = pessoaDAO.consultarPorCPF(cpfMae, Uteis.NIVELMONTARDADOS_MINIMOS);
                                if (pessoaResponsavelVO != null && !UteisValidacao.emptyNumber(pessoaResponsavelVO.getCodigo())) {
                                    clienteVO.setPessoaResponsavel(pessoaResponsavelVO);
                                } else {
                                    pessoaResponsavelVO = incluirPessoaResponsavel(false, clienteVO, con);
                                    clienteVO.setPessoaResponsavel(pessoaResponsavelVO);
                                }
                                atualizarPessoaResponsavelCliente(clienteVO, con);
                            } else if (!UteisValidacao.emptyString(cpfPai)) {
                                Pessoa pessoaDAO = new Pessoa(con);
                                PessoaVO pessoaResponsavelVO = pessoaDAO.consultarPorCPF(cpfPai, Uteis.NIVELMONTARDADOS_MINIMOS);
                                if (pessoaResponsavelVO != null && !UteisValidacao.emptyNumber(pessoaResponsavelVO.getCodigo())) {
                                    clienteVO.setPessoaResponsavel(pessoaResponsavelVO);
                                } else {
                                    pessoaResponsavelVO = incluirPessoaResponsavel(true, clienteVO, con);
                                    clienteVO.setPessoaResponsavel(pessoaResponsavelVO);
                                }
                                atualizarPessoaResponsavelCliente(clienteVO, con);
                            }
                        }
                    }
                }
            }
        }
    }

    public static PessoaVO incluirPessoaResponsavel(boolean utilizaCPFPai, ClienteVO clienteVO, Connection con) throws
            Exception {
        PessoaVO pessoaResponsavel = new PessoaVO();
        Pessoa pessoaDAO = new Pessoa(con);
        if (utilizaCPFPai) {
            pessoaResponsavel.setNome(UteisValidacao.emptyString(clienteVO.getPessoa().getNomePai()) ? "" : clienteVO.getPessoa().getNomePai());
            pessoaResponsavel.setCfp(clienteVO.getPessoa().getCpfPai());
            pessoaResponsavel.setRg(UteisValidacao.emptyString(clienteVO.getPessoa().getRgPai()) ? "" : clienteVO.getPessoa().getRgPai());
            if (!UteisValidacao.emptyList(clienteVO.getPessoa().getEnderecoVOs())) {
                pessoaResponsavel.setEnderecoVOs(clienteVO.getPessoa().getEnderecoVOs());
            }
            pessoaResponsavel = pessoaDAO.incluirPessoaResponsavelAluno(pessoaResponsavel);
        } else {
            pessoaResponsavel.setNome(UteisValidacao.emptyString(clienteVO.getPessoa().getNomeMae()) ? "" : clienteVO.getPessoa().getNomeMae());
            pessoaResponsavel.setCfp(clienteVO.getPessoa().getCpfMae());
            pessoaResponsavel.setRg(UteisValidacao.emptyString(clienteVO.getPessoa().getRgMae()) ? "" : clienteVO.getPessoa().getRgMae());
            if (!UteisValidacao.emptyList(clienteVO.getPessoa().getEnderecoVOs())) {
                pessoaResponsavel.setEnderecoVOs(clienteVO.getPessoa().getEnderecoVOs());
            }
            pessoaResponsavel = pessoaDAO.incluirPessoaResponsavelAluno(pessoaResponsavel);
        }
        return pessoaResponsavel;
    }

    public static void atualizarPessoaResponsavelCliente(ClienteVO obj, Connection con) throws Exception {
        String sql = "UPDATE cliente SET pessoaresponsavel = ? WHERE codigo = ? ";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            int i = 0;
            sqlAlterar.setInt(++i, obj.getPessoaResponsavel().getCodigo());
            sqlAlterar.setInt(++i, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

}

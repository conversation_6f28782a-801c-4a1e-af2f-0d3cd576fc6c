package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.ProcessoAjustarPlanoCondicaoPagamento;
import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

@ClasseProcesso(autor = "Joao Alcides",
        data = "14/04/2025",
        descricao = "Verificar se trigger que é disparada antes da inserção de contrato foi criada",
        motivacao = "MJ-745")
public class VerificarTriggerCriada implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            String sql = "SELECT tgname, tgtype, tgenabled \n" +
                    "FROM pg_trigger \n" +
                    "WHERE tgrelid = 'public.contrato'::regclass\n" +
                    "AND tgname like '%contrato';";
            try (Statement stmt = c.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                if (!rs.next()) {
                    SuperFacadeJDBC.executarConsulta("create trigger tg_ad_inserir_contrato before " +
                    " insert on public.contrato for each row execute" +
                    " procedure fn_tg_before_inserircontrato()", c);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

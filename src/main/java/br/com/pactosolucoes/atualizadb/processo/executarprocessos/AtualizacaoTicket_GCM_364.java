package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Matheus Cassimiro",
        data = "17/06/2025",
        descricao = "Configuração para permitir que seja possível vender crédito para contratos.",
        motivacao = "GCM-364"
)
public class AtualizacaoTicket_GCM_364 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            // Campo para definir se o produto será utilizado como credito Extra
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto ADD COLUMN creditoExtra BOOLEAN DEFAULT FALSE;", c);

            //Campo configurações do plano
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN gerarValorCreditoExtra BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN produtoCreditoExtra INTEGER DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN valorCreditoExtra float4 DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD CONSTRAINT fk_planocreditoextra_produto FOREIGN KEY (produtoCreditoExtra) REFERENCES produto(codigo) ON DELETE RESTRICT;", c);

            // Campo vincular extrato de credito ao movproduto gerado
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE controlecreditotreino ADD COLUMN movproduto INTEGER DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE controlecreditotreino ADD CONSTRAINT fk_controlecreditotreino_movproduto FOREIGN KEY (movproduto) REFERENCES movproduto(codigo) ON DELETE CASCADE;", c);
        }
    }

}

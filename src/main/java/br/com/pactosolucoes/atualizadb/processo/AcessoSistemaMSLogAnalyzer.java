package br.com.pactosolucoes.atualizadb.processo;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Deque;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.Set;

public class AcessoSistemaMSLogAnalyzer {

    public static void main(String[] args) {
        String logPath = "/opt/acesso-sistema-ms.log.1"; // Caminho do arquivo de log
        String successPath = "/opt/sucesso.txt"; // Caminho do arquivo de saída

        // Carregar códigos AUT já processados
        Set<String> autCodesProcessed = loadProcessedAutCodes(successPath);

        // Manter as últimas 10 linhas em uma fila
        Deque<String> lastLines = new LinkedList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(logPath))) {
            String currentLine;

            while ((currentLine = reader.readLine()) != null) {
                // Verifica se a linha contém o erro 504
                if (currentLine.contains("504 Gateway Time-out")) {
                    // Verifica até 10 linhas para trás em busca do código AUT
                    String autCode = findAutCodeInLastLines(lastLines);
                    if (autCode != null && !autCodesProcessed.contains(autCode)) {
                        System.out.println("Código AUT encontrado: " + autCode);
                        boolean success = dispararRequest(autCode);

                        if (success) {
                            // Salvar código AUT no arquivo de saída
                            saveAutCode(successPath, autCode);
                            autCodesProcessed.add(autCode);
                        }
                    }
                }

                // Atualiza a fila com a linha atual, mantendo no máximo 10 linhas
                lastLines.addLast(currentLine);
                if (lastLines.size() > 10) {
                    lastLines.removeFirst();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String findAutCodeInLastLines(Deque<String> lastLines) {
        for (String line : lastLines) {
            String autCode = extractAutCode(line);
            if (autCode != null) {
                return autCode;
            }
        }
        return null;
    }

    private static String extractAutCode(String line) {
        String autPrefix = "CodAutorizacao AUT";
        int start = line.indexOf(autPrefix);

        if (start != -1) {
            int end = line.indexOf(' ', start + autPrefix.length());
            return "AUT" + line.substring(start + autPrefix.length(), end != -1 ? end : line.length());
        }
        return null;
    }

    private static boolean dispararRequest(String autCode) {
        String url = "https://zw301.pactosolucoes.com.br/acesso-sistema-ms/autorizacoesAcesso/"
                + autCode + "/force-sync?url=https://bio.pactosolucoes.com.br/e82cdafdf149e399e18ae3eb096bd873&tipo=biometria";
        try {
            URL requestUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Authorization", "Bearer e82cdafdf149e399e18ae3eb096bd873");

            int responseCode = connection.getResponseCode();
            System.out.println("Requisição para " + autCode + " retornou código: " + responseCode);
            connection.disconnect();

            return responseCode == 200;
        } catch (IOException e) {
            System.err.println("Erro ao disparar requisição para AUT " + autCode + ": " + e.getMessage());
            return false;
        }
    }

    private static Set<String> loadProcessedAutCodes(String successPath) {
        Set<String> autCodes = new HashSet<>();
        if (Files.exists(Paths.get(successPath))) {
            try (BufferedReader reader = new BufferedReader(new FileReader(successPath))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    autCodes.add(line.trim());
                }
            } catch (IOException e) {
                System.err.println("Erro ao ler o arquivo de sucesso: " + e.getMessage());
            }
        }
        return autCodes;
    }

    private static void saveAutCode(String successPath, String autCode) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(successPath, true))) {
            writer.write(autCode);
            writer.newLine();
        } catch (IOException e) {
            System.err.println("Erro ao salvar código AUT: " + e.getMessage());
        }
    }
}

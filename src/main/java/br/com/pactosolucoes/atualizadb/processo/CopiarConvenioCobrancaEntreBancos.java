package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class CopiarConvenioCobrancaEntreBancos {

    public static void main(String... args) throws Exception {
        Connection conOrigem = DriverManager.getConnection("********************************************************************************___", "postgres", "pactodb");
        Connection conDestino = DriverManager.getConnection("***************************************************************************___", "postgres", "pactodb");
        Integer[] tipoCopiar = new Integer[]{TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo(), TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT.getCodigo()};
        copiar(conOrigem, conDestino, tipoCopiar);
    }

    private static void copiar(Connection conOrigem, Connection conDestino, Integer[] tipoCopiar) throws Exception {
        ConvenioCobranca convenioCobrancaDAOOrigem;
        ConvenioCobranca convenioCobrancaDAODestino;
        Usuario usuarioDAODestino;
        try {
            conDestino.setAutoCommit(false);

            convenioCobrancaDAOOrigem = new ConvenioCobranca(conOrigem);
            convenioCobrancaDAODestino = new ConvenioCobranca(conDestino);
            usuarioDAODestino = new Usuario(conDestino);

            UsuarioVO usuarioVO = usuarioDAODestino.consultarPorUsername("admin", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<ConvenioCobrancaVO> listaGeral = convenioCobrancaDAOOrigem.consultarTodos(Uteis.NIVELMONTARDADOS_TODOS);
            List<ConvenioCobrancaVO> listaCopiar = new ArrayList<>();

            Set<Integer> add = new HashSet<>();
            for (ConvenioCobrancaVO obj : listaGeral) {
                if (Arrays.stream(tipoCopiar).noneMatch(cod -> cod.equals(obj.getTipo().getCodigo()))) {
                    continue;
                }
                if (!add.contains(obj.getCodigo())) {
                    add.add(obj.getCodigo());
                    listaCopiar.add(obj);
                }
            }

            int atual = 0;
            for (ConvenioCobrancaVO obj : listaCopiar) {
                Uteis.logarDebug(++atual + "/" + listaCopiar.size() + " - Importando... " + obj.getDescricao());
                obj.setUsuarioVO(usuarioVO);
                convenioCobrancaDAODestino.incluir(obj);
            }

            conDestino.commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            conDestino.rollback();
        } finally {
            conDestino.setAutoCommit(true);
            convenioCobrancaDAOOrigem = null;
            convenioCobrancaDAODestino = null;
        }
    }
}
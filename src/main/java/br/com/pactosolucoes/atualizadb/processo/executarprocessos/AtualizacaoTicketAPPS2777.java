package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Kaio Sanhcez",
        data = "18/02/2025",
        descricao = "Atualização de campos adicionais para vendas online",
        motivacao = "APPS-2777")
public class AtualizacaoTicketAPPS2777 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig \n" +
                    "ADD COLUMN camposadicionaisplanoflow TEXT,\n" +
                    "ADD COLUMN camposadicionaisprodutoflow TEXT;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "03/12/2024",
        descricao = "Controlar preenchimento de campo",
        motivacao = "TW-1217")
public class AtualizacaoTicketTW1217 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarConsulta("CREATE TABLE horarioequipamentoaluno ( codigo serial primary key, " +
                    " horarioturma INTEGER REFERENCES horarioturma (codigo), " +
                    " equipamento text, " +
                    " cliente INTEGER REFERENCES cliente (codigo), " +
                    " empresa INTEGER REFERENCES empresa (codigo), " +
                    " diaAula TIMESTAMP," +
                    " dataLancamento TIMESTAMP NOT NULL DEFAULT NOW()," +
                    " usuarioLancou  INTEGER REFERENCES usuario (codigo) " +
                    ");", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.threads.ThreadRoboPontuacao;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;
import java.util.Date;

public abstract class CorrigirPontuacaoRetroativa  {

    private static ThreadRoboPontuacao threadRobo;
    public static void main(String[] args) {
        System.out.println("Entrou no Runner Pontos");
        if (args.length == 0) {
            Uteis.debug = true;
            args = new String[]{"bdteste", "01/11/2019"};
        }
        if (args.length >= 1) {
            String chaveEmpresa = args[0];
            try {
                Date diaInciarProcessamento = Calendario.getDate("dd/MM/yyyy", (args.length > 1 ? args[1] : Calendario.hoje().toString()));
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica(chaveEmpresa);
                executar(chaveEmpresa, diaInciarProcessamento, con);

            } catch (Exception ex) {
                ex.printStackTrace();
            }

        }
    }

    public static void executar(String nomeThread, Date diaInciarProcessamento, Connection con) throws Exception {
        if(threadRobo == null || threadRobo.isFinalizado()) {
            //Conexao.guardarConexaoForJ2SE(con);
            threadRobo = new ThreadRoboPontuacao();
            threadRobo.setChave(nomeThread);
            threadRobo.setName("Robo_" + nomeThread);
            threadRobo.setDiaProcessar(diaInciarProcessamento);
            threadRobo.run();
        }
    }

    public static ThreadRoboPontuacao getThreadRobo() {
        return threadRobo;
    }
}

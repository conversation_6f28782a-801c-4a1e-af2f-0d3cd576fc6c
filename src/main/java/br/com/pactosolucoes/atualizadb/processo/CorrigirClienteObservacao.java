package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteObservacaoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.ClienteObservacao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class CorrigirClienteObservacao {

    public static void main(String[] args) throws SQLException {
        Connection con1;
        try {
            con1 = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "companhiadocorpo");
            ajustarMensagens(con1);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void ajustarMensagens(Connection con) throws Exception {
        try {
            con.setAutoCommit(false);
            ClienteObservacao clienteObservacao = new ClienteObservacao(con);
            Usuario usuario = new Usuario(con);

            String sql = "SELECT * FROM clientemensagem WHERE tipomensagem = 'OB'";
            Statement stm = con.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);
            ResultSet query = stm.executeQuery(sql);

            UsuarioVO usuarioVO = usuario.consultarPorNomeUsuario("admin", false, Uteis.NIVELMONTARDADOS_MINIMOS);

            int i = 1;
            int total = 0;
            if (query.last()) {
                total = query.getRow();
                query.beforeFirst();
            }

            while (query.next()) {
                System.out.println("Processando " + i + " de " + total);
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(query.getInt("cliente"));

                String observacaoCompleta = query.getString("mensagem");
                observacaoCompleta = observacaoCompleta.replaceAll("\\s*<br/> OBSERVAÇÃO:", "");
                String[] observacoes = observacaoCompleta.split("\r\n");

                List<StringBuilder> observacoesProcessadas = new ArrayList<StringBuilder>();

                for (String obs : observacoes) {
                    if (!obs.trim().equals("")) {
                        if (ehNovaObs(obs)) {
                            StringBuilder sb = new StringBuilder(obs);
                            observacoesProcessadas.add(sb);
                        } else {
                            if (observacoesProcessadas.size() == 0) {
                                StringBuilder sb = new StringBuilder(obs);
                                observacoesProcessadas.add(sb);
                            } else {
                                observacoesProcessadas.get(observacoesProcessadas.size() - 1).append(obs);
                            }
                        }
                    }
                }

                int j = 1;
                for (StringBuilder obsProcessada : observacoesProcessadas) {
                    System.out.println("Processando observacao " + j + " de " + observacoesProcessadas.size());
                    String obs = obsProcessada.toString().trim();
                    String[] obsPartes = obs.split(" - ");
                    boolean contemUsuario = obs.contains("->");
                    boolean mensagemHTML = obs.contains("DOCTYPE html");
                    if (!contemUsuario && !obsPartes[0].trim().equals("") && !mensagemHTML) {
                        ClienteObservacaoVO clienteObservacaoVO = new ClienteObservacaoVO();

                        Date dataCadastroAnterior = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").parse("01/01/2000 00:00:00");

                        StringBuilder observacaoAnterior = observacoesProcessadas.get(j - 1);
                        String[] obsAnteriorPartes = observacaoAnterior.toString().split(" - ");
                        boolean anteriorContemUsuario = observacaoAnterior.toString().contains("->");
                        if (obsAnteriorPartes.length > 1 && anteriorContemUsuario) {
                            String dataCadastroObservacao[] = obsAnteriorPartes[1].split("->");
                            String dataCadastro = dataCadastroObservacao[0].trim();
                            dataCadastroAnterior = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").parse(dataCadastro);
                            dataCadastroAnterior = Uteis.somarCampoData(dataCadastroAnterior, Calendar.SECOND, 1);
                        }

                        clienteObservacaoVO.setDataCadastro(dataCadastroAnterior);

                        clienteObservacaoVO.setUsuarioVO(usuarioVO);
                        clienteObservacaoVO.setObservacao(obs);

                        clienteObservacaoVO.setClienteVO(clienteVO);
                        clienteObservacao.incluir(clienteObservacaoVO);
                    } else if (!mensagemHTML) {
                        if (obsPartes.length > 1) {
                            String nomeUsuario = obsPartes[0];
                            String dataCadastroObservacao[] = obsPartes[1].split("->");
                            String dataCadastro = dataCadastroObservacao[0].trim();
                            String obsFinal = "";
                            if (dataCadastroObservacao.length > 1) {
                                obsFinal = dataCadastroObservacao[1].trim();
                            }

                            UsuarioVO usuarioObs = usuario.consultarPorNomeUsuario(nomeUsuario, false, Uteis.NIVELMONTARDADOS_MINIMOS);

                            if (usuarioObs.getCodigo() == null || usuarioObs.getCodigo() == 0) {
                                usuarioObs = usuarioVO;
                            }

                            Date dtCadastro;
                            if (dataCadastro.length() != 10) {
                                dataCadastro = dataCadastro.replaceAll("\\(", "");
                                dataCadastro = dataCadastro.replaceAll("\\)", "");
                                dtCadastro = new SimpleDateFormat("dd/MM/yy").parse(dataCadastro);
                            } else {
                                dtCadastro = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").parse(dataCadastro);
                            }

                            ClienteObservacaoVO clienteObservacaoVO = new ClienteObservacaoVO();
                            clienteObservacaoVO.setDataCadastro(dtCadastro);
                            clienteObservacaoVO.setUsuarioVO(usuarioObs);
                            clienteObservacaoVO.setObservacao(obsFinal);

                            clienteObservacaoVO.setClienteVO(clienteVO);
                            clienteObservacao.incluir(clienteObservacaoVO);
                        }
                    } else {
                        ClienteObservacaoVO clienteObservacaoVO = new ClienteObservacaoVO();
                        Date dataCadastroAnterior = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").parse("01/01/2000 00:00:00");
                        clienteObservacaoVO.setDataCadastro(dataCadastroAnterior);

                        clienteObservacaoVO.setUsuarioVO(usuarioVO);
                        if (obs.length() > 2500) {
                            clienteObservacaoVO.setObservacao(obs.substring(0, 2500));
                        } else {
                            clienteObservacaoVO.setObservacao(obs);
                        }

                        clienteObservacaoVO.setClienteVO(clienteVO);
                        clienteObservacao.incluir(clienteObservacaoVO);
                    }
                    j++;
                }
                i++;
            }
            con.commit();
            con.setAutoCommit(true);
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
            }
        }
    }

    private static boolean ehNovaObs(String obs) {
        String obsTemp = obs.trim();
        String[] obsPartes = obsTemp.split(" - ");
        boolean contemUsuario = obsTemp.contains("->");

        return (obsPartes.length > 1) && contemUsuario;
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ProcessoReplicarCadastroClienteEngenharia {

    private static final Integer codigoEmpresaDestino = 1;

    public static void main(String[] args) throws Exception {

        Connection conOAMD = new Conexao("*************************************", "postgres", "pactodb").getConexao();
        Integer redeEmpresaId = 0; // 467 - Engenharia do Corpo

        String chaveEmpresaOrigem = "";
        String chavesIgnorar = "";
        String matriculas = ""; // matriculas dos cadastros que serão replicados nas demais unidades da rede
        boolean lancarBloqueio = true;
        String msgBloqueio = "Acesso não permitido na rede.";
        boolean lancarAviso = true;
        String msgAvisoConsultor = "Aluno com acesso não permitido na rede. O aluno já foi notificado extrajudicialmente";

        processarClientesReplicar(conOAMD, redeEmpresaId, chaveEmpresaOrigem, chavesIgnorar, matriculas, lancarBloqueio, msgBloqueio, lancarAviso, msgAvisoConsultor);

    }

    private static void processarClientesReplicar(Connection conOAMD, Integer redeEmpresaId, String chaveOrigem, String chavesIgnorar, String matriculas, boolean lancarBloqueio, String msgBloqueio, boolean lancarAviso, String msgAvisoConsultor) throws Exception {

        // Consultar empresa origem e criar conexão
        ResultSet rsEmpresaOrigem = SuperFacadeJDBC.criarConsulta("select 'jdbc:postgresql://' || e.\"hostBD\" || ':' || e.porta || '/' || e.\"nomeBD\" as urlcon from empresa e \n" +
                "where e.chave = '" + chaveOrigem + "' \n", conOAMD);
        Connection conOrigem = null;
        if (rsEmpresaOrigem.next()) {
            conOrigem = new Conexao(rsEmpresaOrigem.getString("urlcon"), "postgres", "pactodb").getConexao();
        } else {
            throw new Exception("Nenhuma empresa origem encontrada para chave: " + chaveOrigem);
        }
        // Consultar clientes por cpf na empresa origem
        Cliente clienteDaoOrigem = new Cliente(conOrigem);
        Pessoa pessoaDaoOrigem = new Pessoa(conOrigem);
        List<ClienteVO> clientesOrigem = new ArrayList<>();
        for (String matricula: matriculas.split(",")) {
            ClienteVO clienteVO = clienteDaoOrigem.consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_TODOS);
            clienteVO.setPessoa(pessoaDaoOrigem.consultarPorChavePrimaria(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            clientesOrigem.add(clienteVO);
        }
        if (UteisValidacao.emptyList(clientesOrigem)) {
            throw new Exception("Nenhum cliente encontrado para replicar!");
        }

        String sqlUnidades = "select \n" +
                " e.chave,\n" +
                " e.identificadorempresa,\n" +
                " 'jdbc:postgresql://' || e.\"hostBD\" || ':' || e.porta || '/' || e.\"nomeBD\" as urlcon,\n" +
                " ef.redeempresa_id,\n" +
                " r.nome as nome_redeempresa\n" +
                "from empresa e\n" +
                " inner join empresafinanceiro ef on ef.chavezw = e.chave\n" +
                " inner join redeempresa r on r.id = ef.redeempresa_id \n" +
                "where ef.redeempresa_id = " + redeEmpresaId + "\n" +
                "and coalesce(ef.chavezw,'') <> '' \n" +
                "and e.chave <> r.chavefranqueadora \n" +
                "and e.chave <> '" + chaveOrigem + "' \n" +
                "and e.ativa is true \n" +
                "and e.usoteste is false \n";

        if (!UteisValidacao.emptyString(chavesIgnorar)) {
            String chaves = "";
            for (String c: chavesIgnorar.split(",")) {
                chaves = "," + (c.startsWith("'") && c.endsWith("'") ? c : "'" + c + "'");
            }
            sqlUnidades += "and e.chave not in (" + chaves.replaceFirst(",", "") + ") \n";
        }

        ResultSet rsEmpresas = SuperFacadeJDBC.criarConsulta(sqlUnidades, conOAMD);

        int count = 0;
        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sqlUnidades + ") as sql", conOAMD);
        while (rsEmpresas.next()) {
            Connection conDestino = new Conexao(rsEmpresas.getString("urlcon"), "postgres", "pactodb").getConexao();
            Uteis.logarDebug(++count + "\\" + total + " - Replicando clientes na unidade: " + rsEmpresas.getString("chave") + " - " + rsEmpresas.getString("identificadorempresa"));
            replicarClientes(conDestino, clientesOrigem, lancarBloqueio, msgBloqueio, lancarAviso, msgAvisoConsultor);
        }
    }

    private static void replicarClientes(Connection con, List<ClienteVO> clientes, boolean lancarBloqueio, String msgBloqueio, boolean lancarAviso, String msgAvisoConsultor) throws Exception {
        ConfiguracaoSistema configuracaoSistemaDAO = new ConfiguracaoSistema(con);
        ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaDestinoVO = empresaDAO.consultarPorChavePrimaria(codigoEmpresaDestino, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        Colaborador colaboradorDAO = new Colaborador(con);
        ColaboradorVO consultor = colaboradorDAO.consultarPorNomeColaborador("PACTO - M", codigoEmpresaDestino, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("PACTOBR", Uteis.NIVELMONTARDADOS_MINIMOS);

        VinculoVO vinculo = new VinculoVO();
        vinculo.setColaborador(consultor);
        vinculo.setNovoObj(true);
        vinculo.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());

        Cliente clienteDAO = new Cliente(con);
        Pessoa pessoaDAO = new Pessoa(con);
        ClienteMensagem clienteMensagemDAO = new ClienteMensagem(con);
        ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(con);

        Cidade cidadeDAO = new Cidade(con);
        int falha = 0;
        int sucesso = 0;
        int contador = 0;
        for (ClienteVO clienteVO : clientes) {
            try {
                Uteis.logarDebug("\tAtual " + (++contador) + "/" + clientes.size() + " CPF: " + clienteVO.getPessoa().getCfp()  + " - "+ clienteVO.getPessoa().getNome());

                ResultSet rsCliente = SuperFacadeJDBC.criarConsulta("select cli.codigo from cliente cli \n" +
                        " inner join pessoa pes on pes.codigo = cli.pessoa \n" +
                        " where pes.cfp = '" + clienteVO.getPessoa().getCfp() + "' limit 1", con);
                if (rsCliente.next()) {
                    Uteis.logarDebug("\t\tJá existe um cliente com esse CPF");
                    clienteVO = clienteDAO.consultarPorChavePrimaria(rsCliente.getInt("codigo"), Uteis.NIVELMONTARDADOS_TODOS);
                    clienteVO.setPessoa(pessoaDAO.consultarPorChavePrimaria(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
                } else {
                    clienteVO.getCategoria().setCodigo(0);
                    clienteVO.setCodigo(0);

                    clienteVO.setEmpresa(empresaDestinoVO);

                    CidadeVO novaCidadeVO = cidadeDAO.consultarPorNomeCidadeSiglaEstado(clienteVO.getPessoa().getCidade().getNomeSemAcento(), clienteVO.getPessoa().getCidade().getEstado().getSigla());
                    clienteVO.getPessoa().setCidade(novaCidadeVO);
                    clienteVO.getPessoa().setEstadoVO(novaCidadeVO.getEstado());
                    clienteVO.getPessoa().setPais(novaCidadeVO.getPais());

                    vinculo.setCliente(clienteVO);

                    clienteVO.getPessoa().setTipoPessoa("CLI");
                    clienteVO.getVinculoVOs().add(vinculo);
                    clienteVO.setMatricula("");

                    clienteDAO.gerarNumeroMatricula(clienteVO, empresaDestinoVO, configuracaoSistemaVO);
                    clienteDAO.incluirClienteSimplificadoImportacao(clienteVO);
                    if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        throw new Exception("Falha ao incluir cliente!");
                    }
                    clienteDAO.atualizarMatriculaAluno(clienteVO.getCodigoMatricula());


                    vinculo.setNovoObj(true);
                    vinculo.setCodigo(0);
                }

                if (lancarBloqueio) {
                    Uteis.logarDebug("\t\tLançando mensagem de bloqueio na catraca...");
                    ClienteMensagemVO clienteMensagemVO = clienteMensagemDAO.consultarPorCodigoTipoMensagemECliente(
                            clienteVO.getCodigo(),
                            TiposMensagensEnum.CATRACA.getSigla(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    clienteMensagemVO.setMensagem(msgBloqueio);
                    if (UteisValidacao.emptyNumber(clienteMensagemVO.getCodigo())) {
                        clienteMensagemVO.setBloqueio(true);
                        clienteMensagemVO.setDataBloqueio(Calendario.hoje());
                        clienteMensagemVO.setUsuario(usuarioVO);
                        clienteMensagemVO.setTipomensagem(TiposMensagensEnum.CATRACA);
                        clienteMensagemVO.setCliente(clienteVO);
                        clienteMensagemDAO.incluirSemCommit(clienteMensagemVO);
                    } else {
                        clienteMensagemDAO.alterarSemCommit(clienteMensagemVO);
                    }
                }
                if (lancarAviso) {
                    Uteis.logarDebug("\t\tLançando mensagem de aviso ao consultor...");
                    ClienteMensagemVO clienteMensagemVO = clienteMensagemDAO.consultarPorCodigoTipoMensagemECliente(
                            clienteVO.getCodigo(),
                            TiposMensagensEnum.AVISO_CONSULTOR.getSigla(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    clienteMensagemVO.setMensagem(msgAvisoConsultor);
                    if (UteisValidacao.emptyNumber(clienteMensagemVO.getCodigo())) {
                        clienteMensagemVO.setTipomensagem(TiposMensagensEnum.AVISO_CONSULTOR);
                        clienteMensagemVO.setCliente(clienteVO);
                        clienteMensagemDAO.incluirSemCommit(clienteMensagemVO);
                    } else {
                        clienteMensagemDAO.alterarSemCommit(clienteMensagemVO);
                    }
                }
                zillyonWebFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                sucesso++;
            } catch (Exception e) {
                falha++;
                Uteis.logarDebug("Aluno: " + clienteVO.getPessoa().getNome() + " --- ERRO: " + e.getMessage());
            }
        }
        configuracaoSistemaDAO = null;
        clienteMensagemDAO = null;
        clienteDAO = null;
        pessoaDAO = null;
        zillyonWebFacadeDAO = null;
        usuarioDAO = null;
        cidadeDAO = null;

        Uteis.logarDebug("TOTAL SUCESSO: " + sucesso);
        Uteis.logarDebug("TOTAL FALHA: " + falha);
    }
}


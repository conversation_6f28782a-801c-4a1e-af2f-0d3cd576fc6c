/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.Transacao;
import org.json.JSONObject;
import servicos.impl.cieloecommerce.CieloECommerceStatusEnum;
import servicos.util.ExecuteRequestHttpService;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class ProcessoTransacaoCielo {


    public static void main(String[] args) {
        try {
            Connection con1 = DriverManager.getConnection("*****************************************", "zillyonweb", "pactodb");
            processoCielo(con1);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    private static String executarRequestCieloConsulta(String parametros, String metodo, String metodoHTTP) throws IOException {
        return executarRequestCieloECommerce(true, parametros, metodo, metodoHTTP);
    }

    private static String executarRequestCieloECommerce(boolean consulta, String parametros, String metodo, String metodoHTTP) throws IOException {
        String URL = "https://api.cieloecommerce.cielo.com.br";
        if (consulta) {
            URL = "https://apiquery.cieloecommerce.cielo.com.br";
        }
        String path = URL + metodo;
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("MerchantId", "07e82ebb-7405-43ae-b55c-858a967c83e6");
        headers.put("MerchantKey", "Xw8cBSJ4QAhu8oJmYQlvxcy5xHjbuHfSf8xMIPNi");
        return executeHttpRequest(path, parametros, headers, metodoHTTP, "UTF-8");
    }

    public static String executeHttpRequest(String urlRequest, String corpo, Map<String, String> headers, String metodo, String encode) throws IOException{
        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for(String keyHeader : headers.keySet()){
            conn.setRequestProperty(keyHeader, headers.get(keyHeader));
        }
        conn.setRequestMethod(metodo);
        if(corpo != null){
            conn.setDoOutput(true);
            OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
            wr.write(corpo);
            wr.flush();
            wr.close();
        }
        // Pega a Resposta
        InputStream in = null;
        try{
            in = conn.getInputStream();
        }catch (IOException  e){
            if(conn.getResponseCode() != 200){
                in = conn.getErrorStream();
            }
        }
        BufferedReader rd = new BufferedReader(new InputStreamReader(in, Charset.forName(encode)));
        String line;
        StringBuilder resposta = new StringBuilder();
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta.append(line);
        }
        rd.close();
        return resposta.toString();
    }



    public static void processoCielo(Connection con) throws Exception {
        Date d1 = Calendario.hoje();

        Transacao transacaoDAO = new Transacao(con);
        String[] listaCodigos = "2,170".split(",");
        for (String codigo : listaCodigos) {
            if (!UteisValidacao.emptyString(codigo)) {
                TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(Integer.parseInt(codigo));
                consultarSituacaoTransacao(transacaoVO, con);
            }
        }

        Date d2 = Calendario.hoje();
        System.out.println("Tempo Total processoCielo: " + (d2.getTime() - d1.getTime()));
    }


    public static void consultarSituacaoTransacao(TransacaoVO transacao, Connection con) throws Exception {
        String retorno = executarRequestCieloConsulta(null, "/1/sales/" + transacao.getCodigoExterno(), ExecuteRequestHttpService.METODO_GET);
        processarRetornoConsultaTransacao(transacao, retorno);
        new Transacao(con).alterar(transacao);
    }

    private static void processarRetornoConsultaTransacao(TransacaoVO transacao, String retorno) throws Exception {
        try {
            JSONObject retornoTransacao = new JSONObject(retorno);
            JSONObject payment = retornoTransacao.getJSONObject("Payment");
            Integer status = payment.getInt("Status");

            if (status.equals(CieloECommerceStatusEnum.ANULADO.getId())) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (status.equals(CieloECommerceStatusEnum.AUTORIZADO.getId())) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            } else if (status.equals(CieloECommerceStatusEnum.PAGAMENTO_CONFIRMADO.getId())) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            }
        } catch (Exception e) {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        }
    }
}

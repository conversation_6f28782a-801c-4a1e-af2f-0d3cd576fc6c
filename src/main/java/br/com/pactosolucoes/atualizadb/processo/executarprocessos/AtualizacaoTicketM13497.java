package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON> Alves",
        data = "25/11/2024",
        descricao = "Remove e cria novamente as mensagens de cartão vencidos mantendo apenas as mensagens corretas de acordo com o cartão ativo",
        motivacao = "M1-3497")
public class AtualizacaoTicketM13497 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("DELETE FROM clientemensagem\n" +
                    "WHERE codigo in (\n" +
                    "SELECT cm.codigo FROM clientemensagem cm\n" +
                    "INNER JOIN cliente cli on cli.codigo = cm.cliente\n" +
                    "INNER JOIN pessoa pes on pes.codigo = cli.pessoa\n" +
                    "WHERE cm.tipomensagem = 'CV'\n" +
                    "AND NOT EXISTS (SELECT 1 FROM AutorizacaoCobrancaCliente acc WHERE acc.cliente = cli.codigo AND cm.mensagem LIKE ('%' || acc.cartaomascaradointerno || '%') AND acc.ativa = true)\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO clientemensagem(usuario, tipomensagem, cliente, mensagem, bloqueio, desabilitado, bloqueiocheque, dataregistro)\n" +
                    "SELECT (SELECT codigo FROM USUARIO ORDER BY username ilike 'PACTOBR' DESC, username ilike 'RECOR' DESC, username ilike 'ADMIN' DESC, administrador = true DESC LIMIT 1),\n" +
                    "'CV', cli.codigo, 'Cartão de Crédito ' || acc.cartaomascaradointerno || ' com validade vencida em ' || acc.validadecartao || '. Ligue para o cliente.',\n" +
                    "'f', 'f', 'f', '2024-11-26 10:10:10'::TIMESTAMP\n" +
                    "FROM AutorizacaoCobrancaCliente acc\n" +
                    "INNER JOIN cliente cli on cli.codigo = acc.cliente\n" +
                    "WHERE ativa = true AND length(acc.cartaomascaradointerno) > 3\n" +
                    "AND TO_DATE(acc.validadecartao, 'MM/YYYY') < DATE_TRUNC('month', CURRENT_DATE)\n" +
                    "AND NOT EXISTS (SELECT 1 FROM clientemensagem cm WHERE cm.cliente = cli.codigo AND tipomensagem = 'CV' AND cm.mensagem ILIKE ('%' || acc.cartaomascaradointerno || '%'));", c);
        }
    }

}

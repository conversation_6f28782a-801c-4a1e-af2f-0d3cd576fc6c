package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Wenderson",
        data = "23/12/2024",
        descricao = "Criação de Coluna para registrar Origem de alteração ou Inclusão de novo Cliente/Aluno",
        motivacao = "Ticket M2-2935, necessário para obter mais detalhes sobre o fluxo de alteração e cadastro de Clientes/Alunos")
public class M22935CriarColunaOrigemTabelaDeLog implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE log ADD COLUMN origem TEXT;", c);
        }
    }
}

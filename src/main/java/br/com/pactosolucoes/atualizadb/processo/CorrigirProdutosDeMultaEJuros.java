/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class CorrigirProdutosDeMultaEJuros {

    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("*******************************************************", "postgres", "pactodb");
            corrigirValorProdutos(con1);

        } catch (Exception ex) {
            Logger.getLogger(CorrigirProdutosDeMultaEJuros.class.getName()).log(Level.SEVERE, null, ex);
        }
    }


    public static void corrigirSituacaoMultaEJurosPagos(Connection con) throws  Exception{
        try {
            con.setAutoCommit(false);
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT pag.*\n" +
                    "FROM PagamentoMovParcela pag\n" +
                    "INNER JOIN movparcela mov ON mov.codigo = pag.movparcela\n" +
                    "WHERE mov.situacao = 'EA'\n" +
                    "AND mov.descricao ILIKE '%MULTA E JUROS%'", con);
            while (rs.next()) {
               PreparedStatement ps = con.prepareStatement("UPDATE movparcela\n" +
                       "SET situacao = 'PG'\n" +
                       ",valorparcela = ?\n" +
                       "WHERE codigo = ?;");
                ps.setDouble(1, rs.getDouble("valorpago"));
                ps.setInt(2, rs.getInt("movparcela"));
                ps.executeUpdate();


                ps = con.prepareStatement("UPDATE movprodutoparcela\n" +
                        "SET recibopagamento = ?\n" +
                        "WHERE movparcela = ?;");
                ps.setDouble(1, rs.getInt("recibopagamento"));
                ps.setInt(2, rs.getInt("movparcela"));
                ps.executeUpdate();

                ps = con.prepareStatement("UPDATE movproduto\n" +
                        "SET situacao = 'PG'\n" +
                        ", totalfinal = ?\n" +
                        ", valorfaturado = ?\n" +
                        "WHERE codigo IN (\n" +
                        "SELECT movproduto\n" +
                        "FROM movprodutoparcela\n" +
                        "WHERE movparcela = ?\n" +
                        ");");
                ps.setDouble(1, rs.getDouble("valorpago"));
                ps.setDouble(2, rs.getDouble("valorpago"));
                ps.setInt(3, rs.getInt("movparcela"));
                ps.executeUpdate();

            }
            con.commit();
        }catch (Exception e){
            con.rollback();
            e.printStackTrace();
        }
    }

    public static void corrigirValorProdutos(Connection con) throws Exception {
        String sqlProduto = "update movproduto SET totalfinal =  ?,  juros = ? where codigo = ?;";
        String sqlProdutoParcela = "update movprodutoparcela  set valorpago  = ? where movproduto = ?;";
        
        SuperFacadeJDBC.executarConsultaUpdate("update movproduto set situacao = 'CA' where codigo in (\n"
                + "select mpp.movproduto from movparcela par inner join movprodutoparcela mpp on mpp.movparcela = par.codigo\n"
                + "where par.descricao like '%MULTA E JUROS%' and par.situacao = 'CA'\n"
                + ") and situacao <> 'CA';", con);

        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select par.codigo as parcela, par.valorparcela, sum(mpp.valorpago) as valorprodutos,\n"
                + " count(mpp.movproduto) as qtdProdutos,\n"
                + " min(mpp.movproduto) as codigoproduto \n"
                + " from movparcela par inner join movprodutoparcela mpp on mpp.movparcela = par.codigo \n"
                + "where par.descricao like '%MULTA E JUROS%'\n"
                + "group by par.codigo,par.valorparcela having par.valorparcela::numeric <> sum(mpp.valorpago)::numeric\n"
                + "order by qtdProdutos", con);
        int cont = 0;
        
        while (consulta.next()) {
            Double diferenca = Uteis.arredondarForcando2CasasDecimais(consulta.getDouble("valorparcela") - consulta.getDouble("valorprodutos"));
            ResultSet consultaProduto = SuperFacadeJDBC.criarConsulta("select juros, totalfinal  from movproduto  where codigo = " + consulta.getInt("codigoproduto"), con);
            while (consultaProduto.next()) {
                Double novoTotalFinal = Uteis.arredondarForcando2CasasDecimais(diferenca + consultaProduto.getDouble("totalfinal"));
                Double novoJuros = Uteis.arredondarForcando2CasasDecimais(diferenca + consultaProduto.getDouble("juros"));

                PreparedStatement sqlAlterarProduto = con.prepareStatement(sqlProduto);

                sqlAlterarProduto.setDouble(1, novoTotalFinal);
                sqlAlterarProduto.setDouble(2, novoJuros);
                sqlAlterarProduto.setInt(3, consulta.getInt("codigoproduto"));

                sqlAlterarProduto.execute();
                
                PreparedStatement sqlAlterarProdutoParcela = con.prepareStatement(sqlProdutoParcela);

                sqlAlterarProdutoParcela.setDouble(1, novoTotalFinal);
                sqlAlterarProdutoParcela.setInt(2, consulta.getInt("codigoproduto"));

                sqlAlterarProdutoParcela.execute();
                
            }

            System.out.println(++cont + " - movproduto " + consulta.getInt("codigoproduto") + " foi ajustado");
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.HistoricoContrato;

public class VerificaCarenciaUtilizada {
	 public static void main(String ... args){
	        try {
	            Connection con1 = DriverManager.getConnection("**************************************************", "zillyonweb", "pactodb");
	            verificarContratoCarenciaExcedida(con1);
	            
	        } catch (Exception ex) {
	            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
	        }
	    }

	    public static void verificarContratoCarenciaExcedida(Connection con) throws Exception{
	        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato,cd.carencia  from contrato  c " +
	        		"inner join contratoduracao cd on c.codigo = cd.contrato " +
	        		"where  c.situacao =  'AT' and c.codigo in(select contrato from contratooperacao  where tipooperacao  = 'CR') ", con);
	        int i = 1;
	        Contrato co = new Contrato(con);
	        ContratoOperacao op = new ContratoOperacao(con);
	        HistoricoContrato hc = new HistoricoContrato(con);
	        ZillyonWebFacade zw = new ZillyonWebFacade(con);
	        int cont = 0;
	        while(consulta.next()){
	        	boolean alterar = false;
        		int diasUtilizados = 0;
        		int diasPermitidos = consulta.getInt("carencia");
                 List<ContratoOperacaoVO> operacoes = op.consultarPorContrato(consulta.getInt("contrato"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                 for (ContratoOperacaoVO operacao : operacoes) {
                     if (operacao.getCarenciaFerias()) {
                        diasUtilizados += operacao.obterNrDiasContratoOperacao();
                     }
                 }
                 if(diasUtilizados > diasPermitidos){
                	 ContratoVO contrato = co.consultarPorChavePrimaria(consulta.getInt("contrato"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                	 List<HistoricoContratoVO> historicos = hc.consultarHistoricoContratos(consulta.getInt("contrato"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                	  for (ContratoOperacaoVO operacao : operacoes) {
                		  if(operacao.getDataOperacao().compareTo(operacao.getDataFimEfetivacaoOperacao()) < 0){
	                          if (operacao.getCarenciaFerias()) {
	                        	  for (HistoricoContratoVO historico : historicos) {
	                        		  if(historico.getTipoHistorico().equals("CR")){
	                        			  if(historico.getDataRegistro().compareTo(operacao.getDataOperacao()) == 0){
	                        				 if((operacao.getDataOperacao().compareTo(operacao.getDataInicioEfetivacaoOperacao()) >= 0 && (historico.getDataInicioSituacao().compareTo(historico.getDataRegistro()) == 0 
	                        						 || historico.getDataInicioSituacao().compareTo(operacao.getDataInicioEfetivacaoOperacao()) == 0)) 
	                        						 || (operacao.getDataOperacao().compareTo(operacao.getDataInicioEfetivacaoOperacao()) < 0 && operacao.getDataInicioEfetivacaoOperacao().compareTo(historico.getDataInicioSituacao()) == 0)){
	                        					 if (operacao.getDataFimEfetivacaoOperacao().compareTo(historico.getDataFinalSituacao()) != 0){
	                        						 operacao.setDataFimEfetivacaoOperacao(historico.getDataFinalSituacao());
	                        						 op.alterar(operacao);
	                        						 alterar = true;
	                        					 }
	                        				 }
	                        			  }
	                        				  
	                        		  }
	                        	  }
	                          }
                		  }
                      }
                	 if(alterar){
                		 System.out.println(++cont+" - Contrato "+consulta.getInt("contrato") +" tem direito de " + diasPermitidos + " dias  de férias e já utilizou "+ diasUtilizados);
	                	 Integer diasOperacoes =  new Long(zw.obterNrDiasOperacoesCarenciaFeriasAtestadoBonusTrancamentoNoContratoParaDinheiro(contrato, contrato.getVigenciaAteAjustada())).intValue();;
	                	 contrato.setVigenciaAteAjustada(Uteis.somarDias(contrato.getVigenciaAte(), diasOperacoes));
	                	 co.alterarDatasVigenciaContrato(contrato);
	                	 String sql = "UPDATE HistoricoContrato set "
	                         + " dataFinalSituacao=? "
	                         + " WHERE codigo in (select codigo from historicocontrato where contrato=? order by codigo desc limit 1) ";
		                 PreparedStatement sqlAlterar = con.prepareStatement(sql);
		        
		                 sqlAlterar.setDate(1, Uteis.getDataJDBC(contrato.getVigenciaAteAjustada()));
		                 sqlAlterar.setInt(2, contrato.getCodigo().intValue());
		                 sqlAlterar.execute();
		                 
		                 sql = "UPDATE periodoAcessoCliente set dataFinalAcesso=?" +
		                 		" WHERE codigo in (select codigo from periodoAcessoCliente where contrato=? order by codigo desc limit 1)";
		                 sqlAlterar = con.prepareStatement(sql);
		        
		                 sqlAlterar.setDate(1, Uteis.getDataJDBC(contrato.getVigenciaAteAjustada()));
		                 sqlAlterar.setInt(2, contrato.getCodigo().intValue());
		                 sqlAlterar.execute();
                	 }
//	                 
                 }
	        }

	    }
}

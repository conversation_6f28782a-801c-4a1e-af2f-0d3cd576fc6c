package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luis Antonio de Melo Gomes",
        data = "13/01/2025",
        descricao = "Adicionar novos campos para tabela de configuracao",
        motivacao = "PRPI-431")
public class AtualizacaoTicketPRPI431 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaocrmia\n" +
                            "    ADD COLUMN emailResponsavelConversasAI varchar(250) NULL," +
                            "    ADD COLUMN telefoneResponsavelConversasAI varchar(250) NULL;",
                    c);
        }
    }
}
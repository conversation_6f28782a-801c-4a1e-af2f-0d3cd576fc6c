package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;

public class ProcessoAjustarContratosAssinaturaDigitalDuplicados {

    public static void corrigirContratosAssinaturaDigitalDuplicados(Connection con) throws Exception {
        try {
            Uteis.logar("INÍCIO | ProcessoAjustarContratosAssinaturaDigitalDuplicados");
            String sqlContratoAlterar = "DELETE FROM contratoassinaturadigital c\n"
                    + "WHERE (assinatura = '' OR assinatura IS NULL)\n"
                    + "AND contrato  IN (SELECT contrato FROM contratoassinaturadigital GROUP BY contrato HAVING count(codigo) > 1)\n";
            SuperFacadeJDBC.executarUpdate(sqlContratoAlterar, con);
        } catch (Exception ex){
            throw ex;
        } finally {
            Uteis.logar("FIM | ProcessoAjustarContratosAssinaturaDigitalDuplicados");
        }
    }


}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "21/05/2025",
        descricao = "Incluir autorizado via Gestão de Redes",
        motivacao = "GCM-428")
public class AtualizacaoTicketGCM428 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE AlunoHorarioTurma ADD COLUMN autorizadoGestaoRede BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE AlunoHorarioTurma ADD COLUMN codAcessoAutorizado varchar(20);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE AlunoHorarioTurma ADD COLUMN matriculaAutorizado INT DEFAULT NULL;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE aulaconfirmada ADD COLUMN autorizadoGestaoRede BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE aulaconfirmada ADD COLUMN codAcessoAutorizado varchar(20);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE aulaconfirmada ADD COLUMN matriculaAutorizado INT DEFAULT NULL;", c);
        }
    }

}

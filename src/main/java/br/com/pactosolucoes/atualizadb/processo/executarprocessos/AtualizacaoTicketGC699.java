package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "28/10/2024",
        descricao = "Integração GoGood",
        motivacao = "GC-699")
public class AtualizacaoTicketGC699 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN tokenAcademyGoGood varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cliente ADD COLUMN gogoodtoken varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE periodoacessocliente ADD COLUMN tokenGoGood varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table infocheckingogood(\n" +
                    "\tcodigo serial primary key,\n" +
                    "\tcliente int not null,\n" +
                    "\tempresa int not null,\n" +
                    "\tintegracao int not null,\n" +
                    "\tcancelado boolean default false,\n" +
                    "\tperiodoacesso int not null,\n" +
                    "\ttoken text,\t\n" +
                    "\tretorno text,\t\n" +
                    "\tCONSTRAINT fk_infocheckingogood_cliente FOREIGN KEY (cliente) REFERENCES cliente (codigo),\t\n" +
                    "\tCONSTRAINT fk_infocheckingogood_empresa FOREIGN KEY (empresa) REFERENCES empresa (codigo),\t\n" +
                    "\tCONSTRAINT fk_infocheckingogood_periodoacesso FOREIGN KEY (periodoacesso) REFERENCES periodoacessocliente (codigo)\t\n" +
                    ");", c);
        }
    }
}

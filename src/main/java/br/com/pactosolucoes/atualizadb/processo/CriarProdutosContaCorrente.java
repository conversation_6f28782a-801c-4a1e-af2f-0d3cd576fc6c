package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 18/02/13
 * Time: 15:29
 */
public class CriarProdutosContaCorrente extends SuperEntidade {

    private ConfiguracaoSistemaControle config;

    public CriarProdutosContaCorrente() throws Exception {
        super();
    }

    public void executarProcesso() throws Exception {
        processarCorrecao();
    }

    public void executarProcessoRegerarReciboClienteConsultor() throws Exception{
        executarProcesso();
        executarConsultaUpdate("DELETE FROM public.reciboclienteconsultor;", con);
        executarConsultaUpdate("SELECT public.migrador_povoarreciboclienteconsultor();", con);
    }

    private void processarCorrecao() throws Exception {
        config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");

        String encontrarRecibosComDiferencas = "SELECT\n" +
                "  *,\n" +
                "  valortotalpagamento - valorpg AS dif\n" +
                "FROM\n" +
                "  (SELECT\n" +
                "     m.recibopagamento      AS recibo,\n" +
                "     m.pessoa               AS pessoapagador,\n" +
                "     m.responsavelpagamento AS responsavellancamento,\n" +
                "     m.codigo               AS movpagamento,\n" +
                "     m.valortotal                AS valortotalpagamento,\n" +
                "     m.datalancamento       AS datalancamento,\n" +
                "     sum(pmp.valorpago)     AS valorpg\n" +
                "   FROM public.pagamentomovparcela pmp\n" +
                "     INNER JOIN public.movpagamento m\n" +
                "       ON m.codigo = pmp.movpagamento\n" +
                "   GROUP BY 1, 2, 3, 4, 5, 6\n" +
                "   ORDER BY m.recibopagamento) AS t\n" +
                "WHERE abs(t.valortotalpagamento - t.valorpg) >= 0.01";

        PreparedStatement stm = con.prepareStatement(encontrarRecibosComDiferencas,  ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultDados = stm.executeQuery();
        String descricao = MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO;
        long rowCount = getRowCount(resultDados);
        if(config != null){
            config.setTotalAProcessarParcelasCC(rowCount);
        }

        while (resultDados.next()) {
            if(config != null){
                config.setTotalProcessadosParcelasCC(config.getTotalProcessadosParcelasCC() + 1);
            }

            try {
                ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(resultDados.getInt("pessoapagador"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                double valor = resultDados.getDouble("dif");
                if (valor < 0) {
                    List pagamentoMovParcelas = getFacade().getPagamentoMovParcela().consultarPorReciboPagamneto(resultDados.getInt("recibo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    List<PagamentoMovParcelaVO> pagamentosMovParcelasDoPagamento = montarListaPMP(pagamentoMovParcelas, resultDados.getInt("movpagamento"));

                    double somatorio = 0;
                    for (PagamentoMovParcelaVO pmp : pagamentosMovParcelasDoPagamento) {
                        somatorio += pmp.getValorPago();
                    }

                    MovPagamentoVO movPagamentoVO = getFacade().getMovPagamento().consultarPorChavePrimaria(resultDados.getInt("movpagamento"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    double diferenca = somatorio - movPagamentoVO.getValor();

                    double diferencaPorParcela = diferenca / pagamentosMovParcelasDoPagamento.size();

                    for (PagamentoMovParcelaVO pmp : pagamentosMovParcelasDoPagamento) {
                        pmp.setValorPago(pmp.getValorPago() - diferencaPorParcela);
                        getFacade().getPagamentoMovParcela().alterar(pmp);
                    }

                } else {
                    Date datalancamento = resultDados.getDate("datalancamento");

                    UsuarioVO responsavelRecebimento = new UsuarioVO();
                    responsavelRecebimento.setCodigo(resultDados.getInt("responsavellancamento"));

                    ReciboPagamentoVO reciboPagamentoVO = getFacade().getReciboPagamento().consultarPorChavePrimaria(resultDados.getInt("recibo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    //Alterar a data para data do recibo;
                    VendaAvulsaVO vendaAvulsaVO = getFacade().getMovimentoContaCorrenteCliente().gerarProdutoPagamentoCredito(valor, cliente, null, responsavelRecebimento, descricao, datalancamento, reciboPagamentoVO.getEmpresa());

                    //Operações com o MovParcela
                    MovParcelaVO parcelaVO = getFacade().getMovParcela().consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(), "EA", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    parcelaVO.setSituacao("PG");
                    getFacade().getMovParcela().alterar(parcelaVO);

//            List movProdParc = getFacade().getMovProdutoParcela().consultarMovProdutoParcelasPorParcelas(parcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    List movProdParc = parcelaVO.getMovProdutoParcelaVOs();
                    for (Object mpp : movProdParc) {
                        MovProdutoParcelaVO movProdutoParcelaVO = (MovProdutoParcelaVO) mpp;
                        MovProdutoVO movProdutoVO = getFacade().getMovProduto().consultarPorChavePrimaria(movProdutoParcelaVO.getMovProduto(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        movProdutoVO.setQuitado(true);
                        movProdutoVO.setSituacao("PG");
                        movProdutoVO.setMesReferencia(Uteis.getMesReferencia(datalancamento));
                        getFacade().getMovProduto().alterar(movProdutoVO);

                        movProdutoParcelaVO.setReciboPagamento(reciboPagamentoVO);
                        getFacade().getMovProdutoParcela().alterar(movProdutoParcelaVO);
                    }

                    PagamentoMovParcelaVO pagamentoMovParcelaVO = new PagamentoMovParcelaVO();
                    pagamentoMovParcelaVO.setMovParcela(parcelaVO);
                    pagamentoMovParcelaVO.setMovPagamento(resultDados.getInt("movpagamento"));
                    pagamentoMovParcelaVO.setReciboPagamento(reciboPagamentoVO);
                    pagamentoMovParcelaVO.setValorPago(valor);
                    getFacade().getPagamentoMovParcela().incluir(pagamentoMovParcelaVO);
                }
            } catch (Exception e) {
                System.out.println("Pessoapagador: " + resultDados.getInt("pessoapagador"));
                System.out.println("Recibo: " + resultDados.getInt("recibo"));
                throw new Exception(e);
            }
        }
    }

    private long getRowCount(ResultSet resultDados) throws SQLException {
        int rowCount = 0;
        if (resultDados.last()) {
            rowCount = resultDados.getRow();
            resultDados.beforeFirst();
        }
        return rowCount;
    }

    private List<PagamentoMovParcelaVO> montarListaPMP(List pagamentoMovParcelas, Integer movpagamento) {
        List<PagamentoMovParcelaVO> listaPorMovPagamento = new ArrayList<PagamentoMovParcelaVO>();
        for (Object obj : pagamentoMovParcelas) {
            PagamentoMovParcelaVO pagamentoMovParcelaVO = (PagamentoMovParcelaVO) obj;
            if (pagamentoMovParcelaVO.getMovPagamento().equals(movpagamento)) {
                listaPorMovPagamento.add(pagamentoMovParcelaVO);
            }
        }

        return listaPorMovPagamento;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class AjustarImportacaoContratosRematricula {

    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("****************************************************************", "postgres", "pactodb");
            ajustarContratosRematricula(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarContratosRematricula(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select c.codigo as antigo, c.contratoresponsavelrematriculamatricula,c.vigenciaateajustada,c.datarematricularealizada from contrato c "
                + "where c.contratoresponsavelrematriculamatricula  > 0 ", con);
        int cont = 1;

        while (consulta.next()) {
            ResultSet consultaRematricula = SuperFacadeJDBC.criarConsulta("select c.codigo as novo from contrato c "
                    + "where c.situacaocontrato = 'RE' and c.codigo =  " + consulta.getInt("contratoresponsavelrematriculamatricula")
                    + " and c.vigenciade - '" + Uteis.getDataJDBCTimestamp(consulta.getDate("vigenciaateajustada"))+"' < '2 day' ;", con);
            while (consultaRematricula.next()) {

                System.out.println(++cont + " - Contrato " + consultaRematricula.getInt("novo") + " foi alterado para Renovação ");

                String sql = "update contrato set situacaocontrato  = ?, contratobaseadorenovacao = ?, contratobaseadorematricula = ?, situacaorematricula = ?, situacaorenovacao=? where codigo = ?;";

                PreparedStatement sqlAlterarNovo = con.prepareStatement(sql);

                sqlAlterarNovo.setString(1, "RN");
                sqlAlterarNovo.setInt(2, consulta.getInt("antigo"));
                sqlAlterarNovo.setInt(3, 0);
                 sqlAlterarNovo.setString(4, "");
                 sqlAlterarNovo.setString(5, "AN");
                sqlAlterarNovo.setInt(6, consultaRematricula.getInt("novo"));
                sqlAlterarNovo.execute();


                sql = "update historicocontrato set tipohistorico = ?, descricao = ? where contrato = ? and tipohistorico = ?;";
                PreparedStatement sqlAlterarHis = con.prepareStatement(sql);

                sqlAlterarHis.setString(1, "RN");
                sqlAlterarHis.setString(2, "RENOVADO");
                sqlAlterarHis.setInt(3, consultaRematricula.getInt("novo"));
                sqlAlterarHis.setString(4, "RE");
                sqlAlterarHis.execute();

                sql = "update contrato set contratoresponsavelrematriculamatricula= ?, contratoresponsavelrenovacaomatricula = ?, datarenovarrealizada = ?, datarematricularealizada = ?  where codigo = ?;";
                PreparedStatement sqlAlterarAntigo = con.prepareStatement(sql);

                sqlAlterarAntigo.setInt(1, 0);
                sqlAlterarAntigo.setInt(2, consultaRematricula.getInt("novo"));
                sqlAlterarAntigo.setDate(3, Uteis.getDataJDBC(consulta.getDate("datarematricularealizada")));
                sqlAlterarAntigo.setNull(4, 0);
                sqlAlterarAntigo.setInt(5, consulta.getInt("antigo"));
                sqlAlterarAntigo.execute();
            }
//
        }
    }
}

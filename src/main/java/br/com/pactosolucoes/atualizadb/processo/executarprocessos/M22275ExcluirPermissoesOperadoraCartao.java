package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "02/09/2024",
        descricao = "Excluir permissao de operadora de cartao",
        motivacao = "M2-2275")
public class M22275ExcluirPermissoesOperadoraCartao {
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE permissao SET permissoes = '(0)(9)(1)' WHERE tituloapresentacao ILIKE '4.15 - Operadora de Cartão';", c);
        }

    }
}

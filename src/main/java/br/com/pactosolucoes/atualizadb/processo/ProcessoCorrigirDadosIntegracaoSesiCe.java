package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.webservice.IntegracaoImportacao;
import negocio.facade.jdbc.basico.webservice.sesice.AlunoSesiCeJSON;
import negocio.facade.jdbc.basico.webservice.sesice.TiposStatusClienteSesiCeEnum;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.io.Charsets;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProcessoCorrigirDadosIntegracaoSesiCe {

    private static Map<String, JSONObject> mapaContratosCanceladosSesiCe = new HashMap<>();
    private static Map<String, JSONObject> mapaTurmas = new HashMap<>();
    private static final List<Integer> codigosTurmasParaNaoMigrar = Arrays.asList(1714,1719,2103,2104,2105,1713,2071,2072,2073,1712,2074,2075,2076,1711,2106,2107,2108);


    public static void main(String[] args) throws Exception {
        Connection con = DriverManager.getConnection("************************************************", "postgres", "pactodb");
        Conexao.guardarConexaoForJ2SE(con);

        removerTurmasDuplicadas(con);
        ajustarHorariosTurmasInativos(con);
        corrigirNumeroMesesContratoDuracao(con);
        refazerMovProdutosParcelasPagamentos(con);
        corrigirContratosSemMatriculaAlunoTurma(con);
        corrigirContratosSemPeriodoAcesso(con);
        corrigirContratosCancelados(con);
        obterListaAlunosSesi(con);
//        verificarAlunosNaoSincronizados(con, "d3118cc09620ebdb95e191d344b809f0", "2023-01-01");
    }

    private static void corrigirNumeroMesesContratoDuracao(Connection con) throws Exception {
        String sql = "select con.codigo, con.vigenciade, con.vigenciaate, cd.codigo as contratoduracao, cd.numeromeses from contrato con\n" +
                " inner join contratoduracao cd on cd.contrato = con.codigo \n" +
                "where coalesce(con.xnumpro,'') <> ''\n" +
                "order by con.codigo";

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") sql", con);
        int atual = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {
            System.out.printf("\n %d\\%d Corrigindo numero de meses para contrato codigo  %d", ++atual, total, rs.getInt("codigo"));

            Integer numeroMeses;
            Long dias = Uteis.nrDiasEntreDatas(rs.getDate("vigenciade"), rs.getDate("vigenciaate"));
            if (dias < 30) {
                numeroMeses = 1;
            } else {
                numeroMeses = (new Long(dias / 30).intValue());
            }

            if (!numeroMeses.equals(rs.getInt("numeromeses"))) {
                SuperFacadeJDBC.executarConsulta("update contratoduracao set numeromeses = " + numeroMeses + " where codigo = " + rs.getInt("contratoduracao"), con);
            }
        }
    }

    private static void refazerMovProdutosParcelasPagamentos(Connection con) throws Exception {
        Contrato contratoDAO = new Contrato(con);
        Pessoa pessoaDAO = new Pessoa(con);

        String sql = "select con.codigo from contrato con\n" +
                "where coalesce(con.xnumpro,'') <> ''\n" +
                "order by con.codigo";

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") sql", con);
        int atual = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while(rs.next()) {
            System.out.printf("\n %d\\%d Refazendo produtos, parcelas e pagamentos para contrato codigo  %d", ++atual, total, rs.getInt("codigo"));

            try {
                con.setAutoCommit(false);

                SuperFacadeJDBC.executarConsulta("delete from movproduto where contrato = " + rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("delete from movparcela where contrato = " + rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("delete from movpagamento where recibopagamento in (select codigo from recibopagamento where contrato = " + rs.getInt("codigo") + ")", con);
                SuperFacadeJDBC.executarConsulta("delete from recibopagamento where contrato = " + rs.getInt("codigo"), con);

                ContratoVO contratoVO = contratoDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                contratoVO.setPessoa(pessoaVO);
                MovParcelaVO movParcelaVO = gerarMovParcelaZerada(contratoVO, con);
                contratoVO.getMovParcelaVOs().add(movParcelaVO);
                List<MovProdutoVO> movProdutoVOS = gerarMovProdutos(contratoVO, con);
                gerarMovProdutoParcela(movProdutoVOS, movParcelaVO, con);
                gerarMovPagamento(contratoVO, con);

                con.commit();
            } catch (Exception e) {
                e.printStackTrace();
                con.rollback();
            } finally {
                con.setAutoCommit(true);
            }
        }
    }

    private static void gerarMovPagamento(ContratoVO contratoVO, Connection con) throws Exception {
        FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
        MovPagamento movPagamentoDAO = new MovPagamento(con);
        ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);

        FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarAVista();

        MovPagamentoVO movPagamento = new MovPagamentoVO();
        movPagamento.setFormaPagamento(formaPagamentoVO);
        movPagamento.setPessoa(contratoVO.getPessoa());
        movPagamento.setDataLancamento(contratoVO.getDataLancamento());
        movPagamento.setDataPagamento(movPagamento.getDataLancamento());
        movPagamento.setDataQuitacao(movPagamento.getDataLancamento());
        movPagamento.setResponsavelPagamento(contratoVO.getResponsavelContrato());
        movPagamento.setEmpresa(contratoVO.getEmpresa());
        movPagamento.setMovPagamentoEscolhida(true);
        movPagamento.setNomePagador(contratoVO.getPessoa().getNome());
        movPagamento.setValor(0.0);
        movPagamento.setValorTotal(0.0);

        List<MovPagamentoVO> pagamentoVOS = new ArrayList<>();
        pagamentoVOS.add(movPagamento);

        ReciboPagamentoVO reciboPagamentoVO = new ReciboPagamentoVO();
        movPagamentoDAO.inicializarDadosReciboPagamento(pagamentoVOS, reciboPagamentoVO, contratoVO);
        reciboPagamentoDAO.incluir(reciboPagamentoVO);

        movPagamento.setReciboPagamento(reciboPagamentoVO);

        PagamentoMovParcelaVO pagamentoMovParcelaVO = new PagamentoMovParcelaVO();
        pagamentoMovParcelaVO.setMovParcela(contratoVO.getMovParcelaVOs().get(0));
        pagamentoMovParcelaVO.setValorPago(0.0);
        pagamentoMovParcelaVO.setReciboPagamento(reciboPagamentoVO);
        movPagamento.getPagamentoMovParcelaVOs().add(pagamentoMovParcelaVO);

        movPagamentoDAO.incluirSemCommit(movPagamento);

        SuperFacadeJDBC.executarConsulta("update movprodutoparcela set recibopagamento = " + reciboPagamentoVO.getCodigo() + " \n" +
                " where movparcela = " + pagamentoMovParcelaVO.getMovParcela().getCodigo(), con);

        ProdutosPagosServico.setarProdutosPagos(con, reciboPagamentoVO.getCodigo());

        reciboPagamentoDAO = null;
        movPagamentoDAO = null;
        formaPagamentoDAO = null;

    }

    private static void gerarMovProdutoParcela(List<MovProdutoVO> movProdutoVOS, MovParcelaVO movParcelaVO, Connection con) throws Exception {
        MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(con);

        for (MovProdutoVO movProdutoVO: movProdutoVOS) {
            MovProdutoParcelaVO mpp = new MovProdutoParcelaVO();
            mpp.setReciboPagamento(null);
            mpp.setMovParcela(movParcelaVO.getCodigo());
            mpp.setMovProduto(movProdutoVO.getCodigo());
            mpp.setValorPago(0.0);
            movProdutoParcelaDAO.incluir(mpp);
        }
    }

    private void gerarMovprodutoParcela(List<MovParcelaVO> movParcelaVOS, List<MovProdutoVO> movProdutoVOS, Connection con) throws Exception {
        MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(con);

        for (MovParcelaVO movParcelaVO: movParcelaVOS) {
            if (UteisValidacao.emptyNumber(movParcelaVO.getValorParcela())) {
                continue;
            }

            movParcelaVO.setValorBaseCalculo(movParcelaVO.getValorParcela());
            for (MovProdutoVO movProdutoVO: movProdutoVOS) {
                MovProdutoParcelaVO mpp = new MovProdutoParcelaVO();
                mpp.setReciboPagamento(null);
                mpp.setMovParcela(movParcelaVO.getCodigo());

                if(movProdutoVO.getQuitado()){
                    continue;
                }
                mpp.setMovProduto(movProdutoVO.getCodigo());
                if(Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorBaseCalculo()) >= Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getValorPagoMovProdutoParcela())){
                    mpp.setValorPago(movProdutoVO.getValorPagoMovProdutoParcela());
                    movParcelaVO.setValorBaseCalculo(Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorBaseCalculo() - movProdutoVO.getValorPagoMovProdutoParcela()));
                    movProdutoVO.setQuitado(true);
                    movProdutoVO.setValorPagoMovProdutoParcela(0.0);
                } else {
                    mpp.setValorPago(movParcelaVO.getValorBaseCalculo());
                    movProdutoVO.setValorPagoMovProdutoParcela(Uteis.arredondarForcando2CasasDecimais( movProdutoVO.getValorPagoMovProdutoParcela() - movParcelaVO.getValorBaseCalculo()) );
                    movParcelaVO.setValorBaseCalculo(0.0);
                }
                movProdutoVO.setQuitado(Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getValorPagoMovProdutoParcela()) == 0.00);
                movProdutoParcelaDAO.incluir(mpp);
                if(movParcelaVO.getValorBaseCalculo() == 0.00){
                    break;
                }
            }
        }
    }

    private static MovParcelaVO gerarMovParcelaZerada(ContratoVO contratoVO, Connection con) throws Exception {
        MovParcela movParcelaDAO = new MovParcela(con);

        MovParcelaVO movParcelaVO = new MovParcelaVO();
        movParcelaVO.setDataRegistro(contratoVO.getDataLancamento());
        movParcelaVO.setDataVencimento(contratoVO.getVigenciaDe());
        movParcelaVO.setResponsavel(contratoVO.getResponsavelContrato());
        movParcelaVO.setSituacao("PG");
        movParcelaVO.setValorParcela(0.0);
        movParcelaVO.setPercentualJuro(0.0);
        movParcelaVO.setPercentualMulta(0.0);
        movParcelaVO.setPessoa(contratoVO.getPessoa());
        movParcelaVO.setEmpresa(contratoVO.getEmpresa());
        movParcelaVO.setContrato(contratoVO);

        movParcelaDAO.incluirParcelaSemCommit(movParcelaVO);
        movParcelaDAO = null;
        return movParcelaVO;
    }

    private static List<MovProdutoVO> gerarMovProdutos(ContratoVO contratoVO, Connection con) throws Exception {
        MovProduto movProdutoDAO = new MovProduto(con);
        Produto produtoDAO = new Produto(con);

        ProdutoVO produtoPlanoMensal = produtoDAO.consultarPorTipoProduto("PM", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        List<MovProdutoVO> movProdutoVOS = new ArrayList<>();
        for (int i = 0; i < contratoVO.getContratoDuracao().getNumeroMeses(); i++) {
            Date mesReferencia = Calendario.somarMeses(contratoVO.getVigenciaDe(), i);
            Integer ano = Uteis.getAnoData(mesReferencia);
            String mes = Uteis.getMesReferencia(mesReferencia);

            MovProdutoVO movProdutoVO = new MovProdutoVO();
            movProdutoVO.setSituacao("PG");
            movProdutoVO.setContrato(contratoVO);
            movProdutoVO.setPessoa(new PessoaVO());
            movProdutoVO.getPessoa().setCodigo(contratoVO.getPessoa().getCodigo());
            movProdutoVO.setEmpresa(contratoVO.getEmpresa());
            movProdutoVO.setDescricao(String.format("%s - %s", contratoVO.getPlano().getDescricao(), mes + "/" + ano));
            movProdutoVO.setQuantidade(1);
            movProdutoVO.setDataLancamento(contratoVO.getDataLancamento());
            movProdutoVO.setDataInicioVigencia(contratoVO.getVigenciaDe());
            movProdutoVO.setDataFinalVigencia(contratoVO.getVigenciaAte());
            movProdutoVO.setResponsavelLancamento(contratoVO.getResponsavelContrato());
            movProdutoVO.setMesReferencia(mes + "/" + ano);
            movProdutoVO.setAnoReferencia(ano);
            movProdutoVO.setQuitado(false);
            movProdutoVO.setProduto(produtoPlanoMensal);
            movProdutoVO.setApresentarMovProduto(true);
            movProdutoVO.setTotalFinal(0.0);
            movProdutoVO.setValorDesconto(0.0);
            movProdutoVO.setPrecoUnitario(0.0);
            movProdutoVO.setJuros(0.0);
            movProdutoVO.setMulta(0.0);
            movProdutoVO.setValorFaturado(0.0);
            movProdutoVO.setJurosNaoRecebidos(0.0);
            movProdutoVO.setMultaNaoRecebida(0.0);

            for (ContratoModalidadeVO cm: contratoVO.getContratoModalidadeVOs()) {
                MovProdutoModalidadeVO mpm = new MovProdutoModalidadeVO();
                mpm.getMovProdutoVO().setCodigo(movProdutoVO.getCodigo());
                mpm.getModalidadeVO().setCodigo(cm.getModalidade().getCodigo());
                mpm.setDataInicio(contratoVO.getVigenciaDe());
                mpm.setDataFim(contratoVO.getVigenciaAte());
                mpm.setValor(0.0);
                mpm.getContrato().setCodigo(contratoVO.getCodigo());
                movProdutoVO.setMovProdutoModalidades(new ArrayList<>());
                movProdutoVO.getMovProdutoModalidades().add(mpm);
            }

            movProdutoDAO.incluirSemCommit(movProdutoVO);
            movProdutoVOS.add(movProdutoVO);
        }
        movProdutoDAO = null;
        produtoDAO = null;
        return movProdutoVOS;
    }

    private static void corrigirContratosSemMatriculaAlunoTurma(Connection con) throws Exception {
        MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurmaDAO = new MatriculaAlunoHorarioTurma(con);

        String sql = "select \n" +
                " con.codigo, \n" +
                " con.empresa, \n" +
                " con.pessoa, \n" +
                " con.vigenciade, \n" +
                " con.vigenciaateajustada, \n" +
                " cmht.horarioturma, \n" +
                " e.toleranciaocupacaoturma \n" +
                "from contrato con\n" +
                " inner join empresa e on e.codigo = con.empresa \n" +
                " inner join contratomodalidade cm on cm.contrato = con.codigo \n" +
                " inner join contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo \n" +
                " inner join contratomodalidadehorarioturma cmht on cmht.contratomodalidadeturma = cmt.codigo \n" +
                "where coalesce(con.xnumpro,'') <> ''\n" +
                "and not exists (select mat.codigo from matriculaalunohorarioturma mat where mat.contrato = con.codigo and mat.horarioturma = cmht.horarioturma) \n" +
                "order by con.codigo";

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") sql", con);
        int atual = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {
            System.out.printf("\n %d\\%d - Incluindo matriculaalunohorarioturma para contrato codigo  %d", ++atual, total, rs.getInt("codigo"));

            MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO = new MatriculaAlunoHorarioTurmaVO();
            matriculaAlunoHorarioTurmaVO.setEmpresa(rs.getInt("empresa"));
            matriculaAlunoHorarioTurmaVO.setPessoa(new PessoaVO());
            matriculaAlunoHorarioTurmaVO.getPessoa().setCodigo(rs.getInt("pessoa"));
            matriculaAlunoHorarioTurmaVO.setContrato(new ContratoVO());
            matriculaAlunoHorarioTurmaVO.getContrato().setCodigo(rs.getInt("codigo"));
            matriculaAlunoHorarioTurmaVO.setHorarioTurma(new HorarioTurmaVO());
            matriculaAlunoHorarioTurmaVO.getHorarioTurma().setCodigo(rs.getInt("horarioturma"));
            matriculaAlunoHorarioTurmaVO.setDataInicio(rs.getDate("vigenciade"));
            matriculaAlunoHorarioTurmaVO.setDataFim(rs.getDate("vigenciaateajustada"));
            matriculaAlunoHorarioTurmaDAO.incluir(matriculaAlunoHorarioTurmaVO);
        }
    }

    private static void ajustarHorariosTurmasInativos(Connection con) throws Exception {
        SuperFacadeJDBC.executarConsulta("update horarioturma ht set ativo = true from turma t \n" +
                " where t.codigo = ht.turma \n" +
                " and ht.ativo is null " +
                " and coalesce(t.idexterno,'') <> ''", con);
    }

    private static void removerTurmasDuplicadas(Connection con) throws Exception {
        String sql = "select\n" +
                "\tt.idexterno,\n" +
                "\tcount(t.codigo) as qtd,\n" +
                "\tmin(t.codigo) as turma\n" +
                "from turma t\n" +
                "where coalesce(t.idexterno,'') <> ''\n" +
                "group by t.idexterno\n" +
                "having count(t.codigo) > 1";

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") sql", con);
        int atual = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {
            String idExternoTurma = rs.getString("idexterno");
            Integer codigoTurmaCorreta = rs.getInt("turma");
            System.out.printf("\n %d\\%d Removendo turmas duplicadas para idexterno %s - quantidade duplicadas: %d", ++atual, total, idExternoTurma, rs.getInt("qtd") - 1);

            try {
                con.setAutoCommit(false);

                SuperFacadeJDBC.executarConsulta("update contratomodalidadeturma set turma = " + codigoTurmaCorreta + "\n" +
                        " where turma in (select t.codigo from turma t where t.idexterno = '" + idExternoTurma + "')", con);

                SuperFacadeJDBC.executarConsulta("update contratomodalidadehorarioturma cmht set horarioturma = ht_new.codigo from horarioturma ht, turma t, horarioturma ht_new\n" +
                        "\twhere ht.codigo = cmht.horarioturma \n" +
                        "\tand t.codigo = ht.turma \n" +
                        "\tand t.idexterno = '" + idExternoTurma + "'\n" +
                        "\tand t.codigo <> " + codigoTurmaCorreta + "\n" +
                        "\tand ht_new.codigo = (\n" +
                        "\t\tselect min(ht_sub.codigo) from horarioturma ht_sub\n" +
                        "\t\twhere ht_sub.turma = " + codigoTurmaCorreta + "\n" +
                        "\t\tand ht_sub.diasemana = ht.diasemana \n" +
                        "\t\tand ht_sub.horainicial = ht.horainicial \n" +
                        "\t\tand ht_sub.horafinal = ht.horafinal)", con);

                SuperFacadeJDBC.executarConsulta("update alunohorarioturma aht set horarioturma = ht_new.codigo from horarioturma ht, turma t, horarioturma ht_new\n" +
                        "\twhere ht.codigo = aht.horarioturma\n" +
                        "\tand t.codigo = ht.turma \n" +
                        "\tand t.idexterno = '" + idExternoTurma + "'\n" +
                        "\tand t.codigo <> " + codigoTurmaCorreta + "\n" +
                        "\tand ht_new.codigo = (\n" +
                        "\t\tselect min(ht_sub.codigo) from horarioturma ht_sub\n" +
                        "\t\twhere ht_sub.turma = " + codigoTurmaCorreta + "\n" +
                        "\t\tand ht_sub.diasemana = ht.diasemana \n" +
                        "\t\tand ht_sub.horainicial = ht.horainicial \n" +
                        "\t\tand ht_sub.horafinal = ht.horafinal \n" +
                        "\t);", con);

                SuperFacadeJDBC.executarConsulta("delete from turma where codigo <> " + codigoTurmaCorreta+ "\n" +
                        " and idexterno = '" + idExternoTurma + "'", con);

                con.commit();
            } catch (Exception e) {
                e.printStackTrace();
                con.rollback();
            } finally {
                con.setAutoCommit(true);
            }
        }

    }

    private static void corrigirContratosCancelados(Connection con) throws Exception {
        String sqlEmpresa = "select idexterno from empresa \n" +
                "where coalesce(idexterno,'') <> '' \n" +
                "and ativa is true \n";
        ResultSet rsEmpresa = SuperFacadeJDBC.criarConsulta(sqlEmpresa, con);
        while (rsEmpresa.next()) {
            carregarContratosCanceladosSesiCe(rsEmpresa.getInt("idexterno"));
        }

        Date hoje = new Date();
        IntegracaoImportacao integracaoImportacao = new IntegracaoImportacao(con);

        int total = mapaContratosCanceladosSesiCe.size();
        int atual = 0;

        for (String xnumpro: mapaContratosCanceladosSesiCe.keySet()) {

            JSONObject alunoContratoCancelado = mapaContratosCanceladosSesiCe.get(xnumpro);

            System.out.printf("%d\\%d Verificando cancelamento do contrato xnumpro: %s - %s \n", ++atual, total, xnumpro, alunoContratoCancelado.getString("nome"));

            String dtCancel = "";
            if (alunoContratoCancelado.getString("status_matricula").toLowerCase().contains("cancelado")) {
                dtCancel = alunoContratoCancelado.getString("data_cancelamento");
            } else if (alunoContratoCancelado.getString("status_matricula").toLowerCase().contains("evadido")) {
                dtCancel = alunoContratoCancelado.getString("data_evasao");
            }

            if (UteisValidacao.emptyString(dtCancel)) {
                throw new Exception("Data de cancelamento não informada!");
            }

            // expressao regular para validar data no formato yyyy-MM-dd hh:mm:ss
            if (!dtCancel.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                throw new Exception("Data de cancelamento inválida.");
            }

            Date dataCancelamento = Calendario.getDate("yyyy-MM-dd HH:mm:ss", dtCancel);
            if (Calendario.maior(dataCancelamento, hoje)) {
                throw new Exception("A data de cancelamento não pode ser maior que hoje");
            }

            Calendario.setDateThread(dataCancelamento);

            String sql = "select con.codigo from contrato con\n" +
                    "where con.xnumpro = '" + xnumpro + "' \n";

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            if (rs.next()) {
                try {
                    con.setAutoCommit(false);

                    Integer codigoContrato = rs.getInt("codigo");

                    if (refazerCancelamento(codigoContrato, con)) {
                        desfazerAlteracoesCancelamentoContrato(codigoContrato, con);
                        integracaoImportacao.cancelarContratoSesiCe(codigoContrato, dataCancelamento, false);
                    }

                    con.commit();
                } catch (Exception e) {
                    e.printStackTrace();
                    con.rollback();
                    throw e;
                } finally {
                    con.setAutoCommit(true);
                }
            } else {
                System.out.println("Contrato não encontrado no sistema: " + xnumpro);
            }
        }
    }

    private static boolean refazerCancelamento(int codigo, Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from historicocontrato where contrato = " + codigo + " and descricao = 'Ca'", con);
        if (rs.next()) {
            return true;
        }
        rs = SuperFacadeJDBC.criarConsulta("select codigo from historicocontrato where contrato = " + codigo + " and tipohistorico = 'CA'", con);
        if (!rs.next()) {
            return true;
        }
        rs = SuperFacadeJDBC.criarConsulta("select codigo from contratooperacao where contrato = " + codigo + " and tipooperacao = 'CA'", con);
        if (!rs.next()) {
            return true;
        }
        rs = SuperFacadeJDBC.criarConsulta("select codigo from periodoacessocliente where contrato = " + codigo + " and tipoacesso = 'CN'", con);
        if (!rs.next()) {
            return true;
        }
        return false;
    }

    private static void desfazerAlteracoesCancelamentoContrato(int codigoContrato, Connection con) throws Exception {
        SuperFacadeJDBC.executarConsulta("update contrato set situacao = 'AT', \n" +
                "vigenciaateajustada = vigenciaate, \n" +
                "dataprevistarenovar = vigenciaate, \n" +
                "dataprevistarematricula = vigenciaate \n" +
                " where codigo = " + codigoContrato, con);
        SuperFacadeJDBC.executarConsulta("delete from historicocontrato where upper(trim(descricao)) = 'CA' and contrato = " + codigoContrato, con);
        SuperFacadeJDBC.executarConsulta("delete from historicocontrato where tipohistorico = 'CA' and contrato = " + codigoContrato, con);
        SuperFacadeJDBC.executarConsulta("delete from historicocontrato where contrato = " + codigoContrato + " \n" +
                "and tipohistorico not in ('MA', 'RE', 'RN', 'AT', 'RA', 'CR', 'RC','RT','TR', 'CT')", con);
        SuperFacadeJDBC.executarConsulta("update historicocontrato hc set datafinalsituacao = con.vigenciaateajustada from contrato con \n" +
                "where con.codigo = hc.contrato \n" +
                "and hc.codigo = (select max(hc_sub.codigo) from historicocontrato hc_sub where hc_sub.contrato = " + codigoContrato + ") \n", con);

        SuperFacadeJDBC.executarConsulta("delete from contratooperacao where tipooperacao = 'CA' and contrato = " + codigoContrato, con);

        SuperFacadeJDBC.executarConsulta("delete from periodoacessocliente where tipoacesso = 'CN' and contrato = " + codigoContrato, con);
        SuperFacadeJDBC.executarConsulta("update periodoacessocliente pa set datafinalacesso = con.vigenciaateajustada from contrato con \n" +
                "where con.codigo = pa.contrato \n" +
                "and pa.codigo = (select max(pa_sub.codigo) from periodoacessocliente pa_sub where pa_sub.tipoacesso = 'CA' and pa_sub.contrato = " +  codigoContrato + ")", con);
    }

    private static void carregarContratosCanceladosSesiCe(Integer idExternoEmpresa) throws Exception {
        String urlApiSesiCe = "https://sistemas.sfiec.org.br/app-sesi-v0.0.5/public/api/app-treino/consulta/alunos-matricula";
        urlApiSesiCe += "?data_inicio=2023-09-01";
        urlApiSesiCe += "&status_matricula=%d";
        urlApiSesiCe += "&id_unidade=%d";

        List<Integer> statusMatriculas = Arrays.asList(2, 3);

        for (int status : statusMatriculas) {
            String url = String.format(urlApiSesiCe, status, idExternoEmpresa);
            String retorno = ExecuteRequestHttpService.executeHttpRequest(url, null, new HashMap<>(), ExecuteRequestHttpService.METODO_GET, Charsets.UTF_8.name());
            JSONObject jsonRetorno = new JSONObject(retorno);
            for (int i = 0; i < jsonRetorno.getJSONArray("matriculas").length(); i++) {
                JSONObject alunoContrato = jsonRetorno.getJSONArray("matriculas").getJSONObject(i);
                mapaContratosCanceladosSesiCe.put(alunoContrato.getString("xnumpro"), alunoContrato);
            }
        }
    }

    private static void corrigirContratosSemPeriodoAcesso(Connection con) throws Exception {
        Contrato contratoDAO = new Contrato(con);
        PeriodoAcessoCliente periodoAcessoClienteDAO = new PeriodoAcessoCliente(con);

        String sql = "select con.codigo from contrato con\n" +
                "where coalesce(con.xnumpro,'') <> ''\n" +
                "and not exists (select p.codigo from periodoacessocliente p where p.contrato = con.codigo) \n" +
                "order by con.codigo";

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") sql", con);
        int atual = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {

            System.out.printf("\n %d\\%d Incluindo periodo de acesso para contrato codigo  %d", ++atual, total, rs.getInt("codigo"));

            ContratoVO contratoVO = contratoDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            PeriodoAcessoClienteVO periodoAcessoClienteVO = new PeriodoAcessoClienteVO();
            periodoAcessoClienteVO.setContrato(contratoVO.getCodigo());
            periodoAcessoClienteVO.setPessoa(contratoVO.getPessoa().getCodigo());
            periodoAcessoClienteVO.setDataLancamento(contratoVO.getDataLancamento());
            periodoAcessoClienteVO.setDataInicioAcesso(contratoVO.getVigenciaDe());
            periodoAcessoClienteVO.setDataFinalAcesso(contratoVO.getSituacao().equals("CA") ? Calendario.somarDias(contratoVO.getVigenciaAteAjustada(), -1) : contratoVO.getVigenciaAteAjustada());
            periodoAcessoClienteVO.setTipoAcesso("CA");

            periodoAcessoClienteDAO.incluir(periodoAcessoClienteVO);


            if (contratoVO.getSituacao().equals("CA")) {
                PeriodoAcessoClienteVO periodoAcessoClinteCancelamento = new PeriodoAcessoClienteVO();
                periodoAcessoClinteCancelamento.setContrato(contratoVO.getCodigo());
                periodoAcessoClinteCancelamento.setPessoa(contratoVO.getPessoa().getCodigo());
                periodoAcessoClinteCancelamento.setDataLancamento(contratoVO.getVigenciaAteAjustada());
                periodoAcessoClinteCancelamento.setDataInicioAcesso(contratoVO.getVigenciaAteAjustada());
                periodoAcessoClinteCancelamento.setDataFinalAcesso(contratoVO.getVigenciaAteAjustada());
                periodoAcessoClinteCancelamento.setTipoAcesso("CN");
                periodoAcessoClienteDAO.incluir(periodoAcessoClinteCancelamento);
            }
        }
    }

    private static void obterListaAlunosSesi(Connection con) throws Exception {

        criarTabelaAlunosSesi(con);

        String urlApiSesiCe = "https://sistemas.sfiec.org.br/app-sesi-v0.0.5/public/api/app-treino/consulta/alunos-matricula";
        urlApiSesiCe += "?data_inicio=2023-10-01";
        urlApiSesiCe += "&status_matricula=%d";
        urlApiSesiCe += "&id_unidade=%s";

        String sqlEmpresa = "select codexternounidadesesi from empresa \n" +
                "where coalesce(idexterno,'') <> '' \n" +
                "and ativa is true \n";
        ResultSet rsEmpresa = SuperFacadeJDBC.criarConsulta(sqlEmpresa, con);

        List<JSONObject> alunos = new ArrayList<>();

        while (rsEmpresa.next()) {
            for (TiposStatusClienteSesiCeEnum tipoStatus : TiposStatusClienteSesiCeEnum.values()) {
                List<Integer> matriculas = obterMatriculasAlunoSesi(con, tipoStatus);
                System.out.printf("Consultando alunos Sesi empresa id %d status: %s...\n", rsEmpresa.getInt("codexternounidadesesi"), tipoStatus.getDescricao());
                String url = String.format(urlApiSesiCe, tipoStatus.getId(), rsEmpresa.getString("codexternounidadesesi"));
                String retorno = ExecuteRequestHttpService.executeHttpRequest(url, null, new HashMap<>(), ExecuteRequestHttpService.METODO_GET, Charsets.UTF_8.name());
                JSONObject jsonRetorno = new JSONObject(retorno);
                for (int i = 0; i < jsonRetorno.getJSONArray("matriculas").length(); i++) {
                    JSONObject dados = jsonRetorno.getJSONArray("matriculas").getJSONObject(i);
                    if (matriculas.contains(dados.optInt("id_matricula"))) {
                        continue;
                    }
                    dados.put("id_unidade", rsEmpresa.getInt("codexternounidadesesi"));
                    alunos.add(dados);
                }
            }
        }

        int total = alunos.size();
        int atual = 0;

        for (JSONObject aluno: alunos) {
            System.out.printf("%d\\%d Inserindo aluno %s \n", ++atual, total, aluno.getString("nome"));

            String sql = "insert into alunosesi(matricula, cpf, nome, id_unidade, dados) values (?,?,?,?,?::json)";
            PreparedStatement ps = con.prepareStatement(sql);
            ps.setInt(1, aluno.getInt("id_matricula"));
            ps.setString(2, aluno.optString("cpf"));
            ps.setString(3, aluno.optString("nome"));
            ps.setInt(4, aluno.optInt("id_unidade"));
            ps.setString(5, aluno.toString());
            ps.execute();
        }
    }

    private static List<Integer> obterMatriculasAlunoSesi(Connection con, TiposStatusClienteSesiCeEnum tipoStatus) throws Exception {
        List<Integer> matriculas = new ArrayList<>();
        ResultSet rs = SuperFacadeJDBC.criarConsulta( "select matricula from alunosesi where upper(trim(dados->>'status_matricula')) = upper(trim('" + tipoStatus.getDescricao() + "'))", con);
        while(rs.next()) {
            matriculas.add(rs.getInt("matricula"));
        }
        return matriculas;
    }

    private static void criarTabelaAlunosSesi(Connection con) throws Exception {
        try {
            SuperFacadeJDBC.executarConsulta("create table alunosesi (" +
                    "codigo serial," +
                    "matricula integer," +
                    "cpf char(11)," +
                    "nome varchar(255)," +
                    "id_unidade integer," +
                    "dados json);", con);
//            SuperFacadeJDBC.criarConsulta("CREATE INDEX alunosesi_matricula_idx ON public.alunosesi USING btree (matricula);", con);
//            SuperFacadeJDBC.criarConsulta("CREATE INDEX alunosesi_cpf_idx ON public.alunosesi USING btree (cpf);", con);
        } catch (Exception e) {
            System.out.println("Tabela alunosesi já existe.");
        }
    }


    private static void verificarAlunosNaoSincronizados(Connection con, String chave, String dataInicial) throws Exception {
        carregarTurmasSesi();

        String sql = "select \n" +
                "\ta.codigo,\n" +
                "\ta.matricula,\n" +
                "\ta.cpf,\n" +
                "\ta.matricula,\n" +
                "\ta.dados->>'id_unidade' as id_unidade,\n" +
                "\te.codigo as codigoempresa,\n" +
                "\ta.dados->>'xnumpro' as xnumpro,\n" +
                "\ta.dados->>'data_inicio_contrato' as data_inicio_contrato,\n" +
                "\ta.nome as nome_sesi,\n" +
                "\ta.dados->>'status_matricula' as status_matricula,\n" +
                "\tpes.nome as nomepacto,\n" +
                "\tpes.codigo as cod_pessoa,\n" +
                "\tcli.codigomatricula as matricula,\n" +
                "\tcon.codigo as contrato,\n" +
                "\ta.dados \n" +
                "from alunosesi a \n" +
                "\tinner join empresa e on e.codexternounidadesesi = (a.dados->>'id_unidade')::integer \n" +
                "\tleft join pessoa pes on coalesce(pes.cfp,'') <> '' and a.cpf = replace(replace(pes.cfp,'.',''),'-','')\n" +
                "\tleft join cliente cli on cli.pessoa = pes.codigo\n" +
                "\tleft join cliente cli_mat on cli_mat.matriculaexterna = (a.dados->>'id_matricula')::integer\n" +
                "\tleft join contrato con on con.xnumpro = a.dados->>'xnumpro'\n" +
                "\tleft join contratooperacao cp on cp.contrato = con.codigo and cp.tipooperacao = 'CA'\n" +
                "where 1 = 1\n" +
//                "and a.dados->>'xnumpro' = 'CL266424'\n" +
                "and ((pes.codigo is null and cli_mat.codigo is null) \n" +
                "\t  or con.codigo is null\n" +
                "\t  or (con.situacao = 'AT' and cp.codigo is null and upper(trim(a.dados->>'status_matricula')) in ('CANCELADO', 'EVADIDO'))\n" +
                "\t)\n" +
                "and (a.dados->>'data_inicio_contrato')::date > '" + dataInicial + "'\n" +
                "order by (a.dados->>'data_inicio_contrato')::date desc";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") sql", con);
        int atual = 0;

        while (rs.next()) {
            try {
                System.out.printf("\n %d\\%d - Processando aluno %s - %s", ++atual, total, rs.getString("nome_sesi"), rs.getString("xnumpro"));

                Integer codigoEmpresa = rs.getInt("codigoempresa");
                JSONObject jsonAluno = new JSONObject(rs.getString("dados"));

                AlunoSesiCeJSON alunoSesiCeJSON = new AlunoSesiCeJSON(jsonAluno);
                alunoSesiCeJSON.validarDados();

                JSONObject jsonTurma = mapaTurmas.get(alunoSesiCeJSON.getIdTurma());
                if (jsonTurma != null) {
                    alunoSesiCeJSON.setIdModalidade(jsonTurma.getInt("id_modalidade"));
                }

                if (codigosTurmasParaNaoMigrar.contains(alunoSesiCeJSON.getIdTurma())) {
                    alunoSesiCeJSON.setIdTurma(null);
                }

                IntegracaoImportacao integracaoImportacao = new IntegracaoImportacao(con);
                System.out.println("\tIncluindo cadastro...");
                ClienteVO clienteVO = integracaoImportacao.processarCadastroAlunoSesiCe(alunoSesiCeJSON, codigoEmpresa);
                System.out.println("\tIncluindo contrato...");
                integracaoImportacao.processarContratoAlunoSesiCe(clienteVO, alunoSesiCeJSON, codigoEmpresa);

                Integer codigoContrato = 0;
                ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("select con.codigo, con.situacao, con.vigenciaateajustada, \n" +
                        " exists(select cp.codigo from contratooperacao cp where cp.contrato = con.codigo and cp.tipooperacao = 'CA') as existe_cancelamento \n" +
                        "from contrato con where con.xnumpro = '" + alunoSesiCeJSON.getXnumpro() + "'", con);
                if (rsContrato.next()) {
                    codigoContrato = rsContrato.getInt("codigo");
                } else {
                    throw new Exception(String.format("Contrato xnumpro %s não encontrado! ", alunoSesiCeJSON.getXnumpro()));
                }

                if ("AT".equals(rsContrato.getString("situacao"))
                        && !rsContrato.getBoolean("existe_cancelamento")
                        && (alunoSesiCeJSON.obterStatusCliente().equals(TiposStatusClienteSesiCeEnum.STATUS_SESI_CANCELADO)
                        || alunoSesiCeJSON.obterStatusCliente().equals(TiposStatusClienteSesiCeEnum.STATUS_SESI_EVADIDO))) {

                    System.out.printf("\tCancelamento contrato...");
                    Date dataCancelamento = alunoSesiCeJSON.obterDataCancelamento();
                    Calendario.setDateThread(dataCancelamento);
                    integracaoImportacao.cancelarContratoSesiCe(codigoContrato, dataCancelamento, true);
                }

                System.out.println("\tAtualizando sintetico...");
                integracaoImportacao.atualizarSinteticoCliente(clienteVO);
            } catch (Exception e) {
                System.out.printf("\nErro ao processar aluno %s %s - %s - %s", rs.getInt("matricula"), rs.getString("nome_sesi"), rs.getString("xnumpro"), e.getMessage());
            }
        }
    }

    private static void carregarTurmasSesi() throws Exception {
        String url = "https://sistemas.sfiec.org.br/app-sesi-v0.0.5/public/api/app-treino/consulta/turmas";

        String retorno = ExecuteRequestHttpService.executeHttpRequest(url, null, new HashMap<>(), ExecuteRequestHttpService.METODO_GET, Charsets.UTF_8.name());

        JSONObject jsonRetorno = new JSONObject(retorno);
        for (int i = 0; i < jsonRetorno.getJSONArray("turmas").length(); i++) {
            JSONObject turma = jsonRetorno.getJSONArray("turmas").getJSONObject(i);
            mapaTurmas.put(turma.getString("id_turma"), turma);
        }
    }

}

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.PerguntaClienteVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaVO;
import negocio.comuns.basico.RespostaPergClienteVO;
import negocio.comuns.basico.RespostaPerguntaVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.PerguntaCliente;
import negocio.facade.jdbc.basico.QuestionarioCliente;
import negocio.facade.jdbc.basico.QuestionarioPergunta;
import negocio.facade.jdbc.basico.QuestionarioPerguntaCliente;
import negocio.facade.jdbc.basico.RespostaPergCliente;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 07/02/14
 * Time: 08:23
 */
public class CriarBVPrimeiraCompra extends SuperEntidade {

    QuestionarioCliente questionarioCliente;
    QuestionarioPerguntaCliente questionarioPerguntaCliente;
    QuestionarioPergunta questionarioPergunta;
    PerguntaCliente perguntaCliente;
    RespostaPergCliente respostaPergCliente;
    List questionarioPerguntaVOs;
    double quantidadeDeClientes = 0;
    double quantidadeRealizada = 0;
    String textoMostrado = "";

    public CriarBVPrimeiraCompra(Connection connection) throws Exception {
        super(connection);
    }

    public static void main(String[] args) {
        try {
            Connection con1;
            if (args.length == 0) {
                con1 = DriverManager.getConnection("************************************************", "postgres", "pactodb");
            } else {
                con1 = new DAO().obterConexaoEspecifica(args[0]);
            }
            Conexao.guardarConexaoForJ2SE(con1);
            new CriarBVPrimeiraCompra(con1).executarProcesso();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void executarProcesso() throws SQLException {
        try {
            System.out.println("Iniciou em : " + Calendario.hoje());
            con.setAutoCommit(false);
            questionarioCliente = new QuestionarioCliente(con);
            questionarioPergunta = new QuestionarioPergunta(con);
            questionarioPerguntaCliente = new QuestionarioPerguntaCliente(con);
            perguntaCliente = new PerguntaCliente(con);
            respostaPergCliente = new RespostaPergCliente(con);
            processar(con);
            con.commit();
            System.out.println("Terminou em : " + Calendario.hoje());
        } catch (Exception e) {
            if (con != null) {
                con.rollback();
            }
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
            }
        }
    }

    private void processar(Connection con) throws Exception {
        int primeiraCompra = 4; //BV Primeira Compra
        int colaboradorPactoBR = consultarCodigoConsultorPactoBR(con);

        questionarioPerguntaVOs = questionarioPergunta.consultarPorDescricaoQuestionario("BV PRIMEIRA COMPRA", Uteis.NIVELMONTARDADOS_TODOS);

        List<ItemProcesso> clientesSemBV = consultarClientesGerarPendenciaBV(con);
        quantidadeDeClientes = clientesSemBV.size();
        for (ItemProcesso item : clientesSemBV) {
            Integer consultor = item.colaborador;
            if (consultor == 0) {
                consultor = colaboradorPactoBR;
            }
            if (consultor != 0) {
                TipoBVEnum tipo = TipoBVEnum.SS_PRIMEIRA_COMPRA;
                Date data = item.cadastroCliente;

                QuestionarioClienteVO questionarioClienteVO = criarRegistroQuestionarioCliente(consultor, item.codigoCliente, primeiraCompra, tipo, data);
                questionarioClienteVO.setQuestionarioPerguntaClienteVOs(questionarioPerguntaVOs);

                for (Object questionarioPerguntaVO : questionarioPerguntaVOs) {
                    QuestionarioPerguntaVO questionarioPerguntaVO1 = (QuestionarioPerguntaVO) questionarioPerguntaVO;

                    PerguntaClienteVO perguntaClienteVO = new PerguntaClienteVO();
                    perguntaClienteVO.setDescricao(questionarioPerguntaVO1.getPergunta().getDescricao());
                    perguntaClienteVO.setTipoPergunta(questionarioPerguntaVO1.getPergunta().getTipoPergunta());
                    if (perguntaClienteVO.getTipoPergunta().equals("ME")) {
                        perguntaClienteVO.setMultipla(true);
                    } else if (perguntaClienteVO.getTipoPergunta().equals("SE")) {
                        perguntaClienteVO.setSimples(true);
                    } else if (perguntaClienteVO.getTipoPergunta().equals("TE")) {
                        perguntaClienteVO.setTextual(true);
                    }
                    perguntaCliente.incluirPerguntaCliente(perguntaClienteVO, false, true);

                    for (RespostaPerguntaVO respostaPerguntaVO : questionarioPerguntaVO1.getPergunta().getRespostaPerguntaVOs()) {
                        RespostaPergClienteVO respostaPergClienteVO = new RespostaPergClienteVO();
                        respostaPergClienteVO.setDescricaoRespota(respostaPerguntaVO.getDescricaoRespota());
                        respostaPergClienteVO.setPerguntaCliente(perguntaClienteVO.getCodigo());
                        respostaPergCliente.incluir(respostaPergClienteVO, false);
                    }


                    QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO = new QuestionarioPerguntaClienteVO();
                    questionarioPerguntaClienteVO.setQuestionarioCliente(questionarioClienteVO.getCodigo());
                    questionarioPerguntaClienteVO.setPerguntaCliente(perguntaClienteVO);
                    questionarioPerguntaCliente.incluir(questionarioPerguntaClienteVO, false, true);
                }

                //Gera a pendência
                quantidadeRealizada++;
                String textoParaMostrar = Math.rint((quantidadeRealizada / quantidadeDeClientes) * 100) + "% Concluído";
                if (!textoParaMostrar.equals(textoMostrado)) {
                    System.out.println(textoParaMostrar);
                    textoMostrado = textoParaMostrar;
                }
            } else {
                System.out.println("Não foi possível gerar Pendência para o cliente: " + item.codigoCliente);
            }
        }
    }

    private Integer consultarCodigoConsultorPactoBR(Connection con) throws Exception {
        String sql = "SELECT\n"
                + "  colaborador\n"
                + "FROM usuario\n"
                + "WHERE username = 'PACTOBR'";

        ResultSet resultSet = criarConsulta(sql, con);

        if (resultSet.next()) {
            return resultSet.getInt("colaborador");
        }

        return 0;
    }

    private List<ItemProcesso> consultarClientesGerarPendenciaBV(Connection con) throws Exception {
        List<ItemProcesso> clientesGerarPendenciaBV = new ArrayList<ItemProcesso>();
        String sql = "SELECT\n" +
                "  va.cliente,\n" +
                "  min(dataregistro) AS datalancamento,\n" +
                "  min(p.datacadastro) as datacadastro,\n" +
                "  vinculo.colaborador\n" +
                "FROM vendaavulsa va\n" +
                "  INNER JOIN cliente ON va.cliente = cliente.codigo\n" +
                "  INNER JOIN pessoa p ON cliente.pessoa = p.codigo\n" +
                "  LEFT JOIN vinculo ON vinculo.cliente = cliente.codigo AND tipovinculo = 'CO'\n" +
                "WHERE va.codigo IN (SELECT\n" +
                "                      vendaavulsa\n" +
                "                    FROM itemvendaavulsa iva\n" +
                "                      INNER JOIN produto prod\n" +
                "                        ON iva.produto = prod.codigo AND prod.tipoproduto = 'SS')\n" +
                "GROUP BY 1, 4\n" +
                "ORDER BY cliente;";

        ResultSet resultSet = criarConsulta(sql, con);
        while (resultSet.next()) {
            ItemProcesso itemProcesso = new ItemProcesso(resultSet.getInt("cliente"), resultSet.getDate("datacadastro"), resultSet.getInt("colaborador"));
            clientesGerarPendenciaBV.add(itemProcesso);
        }

        return clientesGerarPendenciaBV;
    }

    private QuestionarioClienteVO criarRegistroQuestionarioCliente(Integer consultor, Integer cliente, Integer questionario, TipoBVEnum tipoBV, Date data) throws Exception {
        QuestionarioClienteVO questionarioClienteVO = new QuestionarioClienteVO();
        questionarioClienteVO.setData(data);
        questionarioClienteVO.getQuestionario().setCodigo(questionario);
        questionarioClienteVO.getCliente().setCodigo(cliente);
        questionarioClienteVO.getConsultor().setCodigo(consultor);
        questionarioClienteVO.setTipoBV(tipoBV);

        questionarioCliente.incluirSemComit(questionarioClienteVO, false, true);
        return questionarioClienteVO;
    }

    private class ItemProcesso {

        private int codigoCliente;
        private int colaborador;
        private Date cadastroCliente;

        private ItemProcesso(int codigoCliente, Date cadastroCliente, int colaborador) {
            this.codigoCliente = codigoCliente;
            this.cadastroCliente = cadastroCliente;
            this.colaborador = colaborador;
        }
    }
}

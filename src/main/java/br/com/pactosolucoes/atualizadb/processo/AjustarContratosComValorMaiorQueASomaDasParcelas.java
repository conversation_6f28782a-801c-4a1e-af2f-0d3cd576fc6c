package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.SituacaoParcelaEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.lang.Exception;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

public class AjustarContratosComValorMaiorQueASomaDasParcelas {
    private static UsuarioVO admin;
    private static final String mensagem = "Ajuste de Importação";

    public static void main(String... args) {
        try {
            String nomeThread = args.length > 0 ? args[0] : "bdzillyonestacaosaudeceilandia-2016-05-11";
            Connection con = new DAO().obterConexaoEspecifica(nomeThread);
            Conexao.guardarConexaoForJ2SE(con);
            ajustarParcelas();
        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void ajustarParcelas() throws Exception {
        try {
            Statement stm = Conexao.getConexaoForJ2SE().createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);
            ResultSet query = stm.executeQuery(getSql());
            ContratoVO contratoVO;
            EmpresaVO empresaVO;
            while (query.next()) {
                int empresa = query.getInt("empresa");
                int codigoContrato = query.getInt("codigoContrato");
                double valorNovaParcela = query.getDouble("valorNovaParcela");
                gravarLogNovoContratoDesajustado(empresa, codigoContrato, valorNovaParcela);
                contratoVO = getFacade().getContrato().consultarPorChavePrimaria(codigoContrato,
                                        Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                empresaVO = getFacade().getEmpresa().consultarPorCodigo(empresa,
                                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                ReciboPagamentoVO reciboPagamentoVO = insiraReciboPagamentoVO(contratoVO, empresaVO, valorNovaParcela);
                MovPagamentoVO movPagamentoVO = insiraMovPagamento(contratoVO, reciboPagamentoVO, empresaVO, valorNovaParcela);
                MovParcelaVO movParcelaVO = insiraMovParcela(contratoVO, empresaVO, valorNovaParcela);
                insiraPagamentoMovParcela(movParcelaVO, movPagamentoVO, reciboPagamentoVO, valorNovaParcela);
                insiraMovProduto(contratoVO, empresaVO, movParcelaVO, reciboPagamentoVO, valorNovaParcela);
                System.out.println("Finalizando inserções para Contrato = " + codigoContrato);
            }
        } catch (Exception e) {
            Conexao.getConexaoForJ2SE().rollback();
            Conexao.getConexaoForJ2SE().setAutoCommit(true);
            e.printStackTrace();
        } finally {
            if (Conexao.getConexaoForJ2SE() != null) {
                Conexao.getConexaoForJ2SE().setAutoCommit(true);
            }
        }
    }

    private static void insiraPagamentoMovParcela(MovParcelaVO movParcelaVO, MovPagamentoVO movPagamentoVO,
                                                  ReciboPagamentoVO reciboPagamentoVO, double valorNovaParcela) throws Exception{
        System.out.println("Inserindo novo PagamentoMovParcela...");
        PagamentoMovParcelaVO pagamentoMovParcelaVO = getFacade().getPagamentoMovParcela().novo();
        pagamentoMovParcelaVO.setReciboPagamento(reciboPagamentoVO);
        pagamentoMovParcelaVO.setMovPagamento(movPagamentoVO.getCodigo());
        pagamentoMovParcelaVO.setMovParcela(movParcelaVO);
        pagamentoMovParcelaVO.setValorPago(valorNovaParcela);
        getFacade().getPagamentoMovParcela().incluir(pagamentoMovParcelaVO);
    }

    private static MovPagamentoVO insiraMovPagamento(ContratoVO contratoVO, ReciboPagamentoVO reciboPagamentoVO, EmpresaVO empresaVO,
                                                     double valorNovaParcela) throws Exception {
        System.out.println("Inserindo novo MovPagamento...");
        MovPagamentoVO movPagamentoVO = getFacade().getMovPagamento().novo();
        movPagamentoVO.setResponsavelPagamento(getAdmin());
        movPagamentoVO.setValor(valorNovaParcela);
        movPagamentoVO.setDataLancamento(contratoVO.getDataLancamento());
        movPagamentoVO.setDataPagamento(contratoVO.getDataLancamento());
        movPagamentoVO.setPessoa(contratoVO.getPessoa());
        movPagamentoVO.setDataQuitacao(contratoVO.getDataLancamento());
        movPagamentoVO.setEmpresa(empresaVO);
        movPagamentoVO.setObservacao(mensagem);
        movPagamentoVO.setValorTotal(valorNovaParcela);
        movPagamentoVO.setReciboPagamento(reciboPagamentoVO);
        movPagamentoVO.setFormaPagamento(getFormaPagamentoOutros());
        movPagamentoVO.setNomePagador(getAdmin().getNome());
        getFacade().getMovPagamento().incluir(movPagamentoVO);
        return movPagamentoVO;
    }

    private static ReciboPagamentoVO insiraReciboPagamentoVO(ContratoVO contratoVO, EmpresaVO empresaVO,
                                                             double valorNovaParcela) throws Exception {
        System.out.println("Inserindo novo ReciboPagamento...");
        ReciboPagamentoVO reciboPagamentoVO = getFacade().getReciboPagamento().novo();
        reciboPagamentoVO.setResponsavelLancamento(getAdmin());
        reciboPagamentoVO.setContrato(contratoVO);
        reciboPagamentoVO.setValorTotal(valorNovaParcela);
        reciboPagamentoVO.setData(contratoVO.getDataLancamento());
        reciboPagamentoVO.setEmpresa(empresaVO);
        PessoaVO pessoaVO = getFacade().getPessoa()
                .consultarPorCodigo(contratoVO.getPessoa().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        reciboPagamentoVO.setPessoaPagador(pessoaVO);
        reciboPagamentoVO.setNomePessoaPagador(pessoaVO.getNome());
        getFacade().getReciboPagamento().incluir(reciboPagamentoVO);
        return  reciboPagamentoVO;
    }

    private static void insiraMovProduto(ContratoVO contratoVO, EmpresaVO empresaVO,
                                         MovParcelaVO movParcelaVO, ReciboPagamentoVO reciboPagamentoVO,
                                         double valorNovaParcela) throws Exception {
        System.out.println("Inserindo novo MovProduto...");
        MovProdutoVO movProdutoVO = getFacade().getMovProduto().novo();
        movProdutoVO.setSituacao(SituacaoParcelaEnum.PAGO.getCodigo());
        movProdutoVO.setQuitado(true);
        movProdutoVO.setDataFinalVigencia(contratoVO.getVigenciaDe());
        movProdutoVO.setResponsavelLancamento(getAdmin());
        movProdutoVO.setDataLancamento(contratoVO.getDataLancamento());
        movProdutoVO.setTotalFinal(valorNovaParcela);
        movProdutoVO.setPrecoUnitario(valorNovaParcela);
        movProdutoVO.setQuantidade(1);
        movProdutoVO.setDescricao(mensagem);
        movProdutoVO.setEmpresa(empresaVO);
        movProdutoVO.setPessoa(contratoVO.getPessoa());
        movProdutoVO.setContrato(contratoVO);
        movProdutoVO.setProduto(getPlanoMensalidade());
        movProdutoVO.setValorPagoMovProdutoParcela(valorNovaParcela);
        getFacade().getMovProduto().incluir(movProdutoVO);

        System.out.println("Inserindo novo MovProdutoParcela...");
        MovProdutoParcelaVO movProdutoParcelaVO = getFacade().getMovProdutoParcela().novo();
        movProdutoParcelaVO.setMovParcela(movParcelaVO.getCodigo());
        movProdutoParcelaVO.setReciboPagamento(reciboPagamentoVO);
        movProdutoParcelaVO.setValorPago(valorNovaParcela);
        movProdutoParcelaVO.setMovProduto(movProdutoVO.getCodigo());
        getFacade().getMovProdutoParcela().incluir(movProdutoParcelaVO);
    }

    private static MovParcelaVO insiraMovParcela(ContratoVO contratoVO,EmpresaVO empresaVO, double valorNovaParcela) throws Exception {
        System.out.println("Inserindo novo MovParcela...");
        MovParcelaVO novaParcela = getFacade().getMovParcela().novo();
        novaParcela.setDescricao(mensagem);
        novaParcela.setResponsavel(getAdmin());
        novaParcela.setValorParcela(valorNovaParcela);
        novaParcela.setContrato(contratoVO);
        novaParcela.setSituacao(SituacaoParcelaEnum.PAGO.getCodigo());
        novaParcela.setDataVencimento(contratoVO.getDataLancamento());
        novaParcela.setDataRegistro(contratoVO.getDataLancamento());
        novaParcela.setEmpresa(empresaVO);
        novaParcela.setPessoa(contratoVO.getPessoa());
        novaParcela.setDataCobranca(contratoVO.getDataLancamento());
        getFacade().getMovParcela().incluir(novaParcela);
        return novaParcela;
    }

    private static UsuarioVO getAdmin() throws  Exception {
        if (admin == null) {
            System.out.println("Lendo Usuário Admin do Banco de Dados...");
            admin = getFacade().getUsuario().consultarPorNomeUsuario("admin", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        return admin;
    }

    private static String getSql() {
        return "select c.codigo as codigoContrato, valorfinal, c.empresa as empresa, sum(mp.valorparcela) as somatorioParcelas, " +
                "(valorfinal::numeric(10,2) - sum(mp.valorparcela)::numeric(10,2)) as valorNovaParcela\n" +
                "from contrato c\n" +
                "left join movparcela mp ON mp.contrato = c.codigo\n" +
                "where c.situacao = 'AT' and now() between c.vigenciaDe and vigenciaAteAjustada\n"+
                "group by 1,2\n" +
                "having valorfinal::numeric(10,2) > sum(mp.valorparcela::numeric(10,2))\n" +
                "order by 1";
    }

    private static ProdutoVO getPlanoMensalidade() throws Exception {
        System.out.println("Lendo Produto Plano Mensalidade do Banco de Dados...");
        return getFacade().getProduto().consultarPorTipoProduto("PM", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    private static void gravarLogNovoContratoDesajustado(int empresa, int codigoContrato, double valorNovaParcela) {
        System.out.println("Inserindo parcela complmentar para contrato: " + codigoContrato);
        System.out.println("Empresa: " + empresa);
        System.out.println("Valor da Nova Parcela:" + valorNovaParcela);
    }

    private static FormaPagamentoVO getFormaPagamentoOutros() throws Exception {
        List listaFormasPagamento = getFacade().getFormaPagamento().consultarPorDescricao("DINHEIRO", false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (listaFormasPagamento.isEmpty())
            throw new Exception("Banco de Dados Inconsistente. Deveria existir a forma de pagamento 'DINHEIRO'");

        return (FormaPagamentoVO) listaFormasPagamento.get(0);
    }
}
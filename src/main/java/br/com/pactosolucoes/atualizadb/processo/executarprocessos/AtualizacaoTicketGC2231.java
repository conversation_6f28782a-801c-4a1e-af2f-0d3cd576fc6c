package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "11/06/2025",
        descricao = "Atualizar codigo da permissão fila de espera",
        motivacao = "GC-2231 - Permissão duplicada")
public class AtualizacaoTicketGC2231 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            String sql = "UPDATE permissao SET tituloapresentacao = '10.11 - Fila de espera turma' \n" +
                    " WHERE nomeentidade = 'PermiteOperarFilaEsperaTurma';";
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}

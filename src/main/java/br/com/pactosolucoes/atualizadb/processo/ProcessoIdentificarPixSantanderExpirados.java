package br.com.pactosolucoes.atualizadb.processo;

import controle.financeiro.EstornoReciboControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.pix.PixRequisicaoDto;
import servicos.pix.PixService;
import servicos.pix.PixStatusEnum;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ProcessoIdentificarPixSantanderExpirados {

    private static String nomeProcesso = "PROCESSO_SANTANDER";

    public static void main(String[] args) {
        Connection conOAMD = null;
        try {
            boolean somenteConsulta = true;
            //todas empresas ativas do oamd
            conOAMD = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");
            String chavesFiltrar = "";

            //chaves separadas por vírgula
            //String chavesFiltrar = "";

            ProcessoIdentificarPixSantanderExpirados.consultar(chavesFiltrar, conOAMD, somenteConsulta);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (conOAMD != null) {
                    conOAMD.close();
                }
            } catch (Exception ignored) {
            }
        }
    }

    public static void consultar(String chavesFiltrar, Connection conOAMD, boolean somenteConsulta) throws Exception {
        try {
            Uteis.logarDebug("##### ProcessoIdentificarPixSantanderExpirados | INICIO! #####");

            StringBuilder sqlOAMD = new StringBuilder();
            sqlOAMD.append("select \n");
            sqlOAMD.append("distinct \n");
            sqlOAMD.append("e.chave, \n");
            sqlOAMD.append("e.\"hostBD\" as host, \n");
            sqlOAMD.append("e.porta, \n");
            sqlOAMD.append("e.\"userBD\" as user, \n");
            sqlOAMD.append("e.\"passwordBD\" as senha, \n");
            sqlOAMD.append("e.\"nomeBD\" as banco \n");
            sqlOAMD.append("from empresa e \n");
            sqlOAMD.append("inner join empresafinanceiro ef on ef.chavezw = e.chave \n");
            sqlOAMD.append("where e.ativa \n");
            if (!UteisValidacao.emptyString(chavesFiltrar)) {
                sqlOAMD.append("and e.chave in ('").append(chavesFiltrar).append("') \n");
            } else {
                sqlOAMD.append("and exists(select codigo from z_oamd_pactopay_convenio_cobranca where empresafinanceiro_codigo = ef.codigo and tipo_convenio = ").append(TipoConvenioCobrancaEnum.PIX_SANTANDER.getCodigo()).append(") \n");
            }
            ResultSet rsEmpresa = SuperFacadeJDBC.criarConsulta(sqlOAMD.toString(), conOAMD);

            while (rsEmpresa.next()) {
                Connection conEmp = null;
                String host = "";
                try {
                    host = rsEmpresa.getString("host");
                    if (host.contains("zw63")) {
                        host = "*************";
                    } else if (host.contains("zw35")) {
                        host = "************";
                    } else if (host.contains("zw53")) {
                        host = "************";
                    }
                    conEmp = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + rsEmpresa.getInt("porta") + "/" + rsEmpresa.getString("banco"),
                            rsEmpresa.getString("user"), rsEmpresa.getString("senha"));
                    ProcessoIdentificarPixSantanderExpirados.contarPixStatusIncorretos(rsEmpresa.getString("chave"), conEmp, somenteConsulta);
                } catch (Exception ex) {
//                    ex.printStackTrace();
                    System.out.println("Erro host: " + host);
                } finally {
                    try {
                        if (conEmp != null) {
                            conEmp.close();
                        }
                    } catch (Exception ignored) {
                    }
                }
            }
        } finally {
            Uteis.logarDebug("ProcessoIdentificarPixSantanderExpirados | FIM!");
        }
    }

    private static void criarTabela(Connection con) {
        StringBuilder tabela3 = new StringBuilder();
        tabela3.append("CREATE TABLE processopix ( \n");
        tabela3.append("codigo serial PRIMARY KEY,  \n");
        tabela3.append("dataregistro TIMESTAMP WITHOUT TIME ZONE,  \n");
        tabela3.append("pix integer, \n");
        tabela3.append("processo CHARACTER VARYING, \n");
        tabela3.append("resultado CHARACTER VARYING) \n");
        SuperFacadeJDBC.executarUpdateExecutarProcessos(tabela3.toString(), con);
    }

    private static void inserirResultado(Integer pix, String resultado, Connection con) {
        SuperFacadeJDBC.executarUpdateExecutarProcessos("insert into processopix(dataregistro,pix,processo,resultado) values(now()," + pix + ",'" + nomeProcesso + "','" + resultado + "')", con);
    }

    private static void apagarResultados(Connection con) {
        SuperFacadeJDBC.executarUpdateExecutarProcessos("delete from processopix where processo = '" + nomeProcesso + "'", con);
    }

    private static void contarPixStatusIncorretos(String chave, Connection con, boolean somenteConsulta) {

        try {
            criarTabela(con);
            Conexao.guardarConexaoForJ2SE(chave, con);

            StringBuilder sql = new StringBuilder();
            sql.append("select p.codigo, txid from pix p \n");
            sql.append("inner join conveniocobranca c on p.conveniocobranca = c.codigo  \n");
            sql.append("where p.status = 'CONCLUIDA' and p.recibopagamento is not null \n");
            sql.append("and c.tipoconvenio = ").append(TipoConvenioCobrancaEnum.PIX_SANTANDER.getCodigo()).append(" \n");
            sql.append("and not exists (select codigo from processopix where pix = p.codigo and processo = '").append(nomeProcesso).append("') \n");

            ResultSet rsPix = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);

            int statusErrado = 0;
            int statusCorreto = 0;
            while (rsPix.next()) {

                Pix pixDAO;
                PixService pixService;
                try {
                    pixDAO = new Pix(con);
                    PixVO pixVO = pixDAO.consultarPorCodigo(rsPix.getInt("codigo"));

                    pixService = new PixService(con);
                    PixRequisicaoDto pixRequisicaoDto = pixService.consultarCobranca(pixVO);

                    if (pixVO.getConveniocobranca().isPixSantander()) {
                        JSONObject retornoJSON = new JSONObject(pixRequisicaoDto.getResposta());

                        String resultado = "";
                        try {
                            //verifica se a array está preenchida, se não tiver vai cair no catch
                            retornoJSON.getJSONArray("pix").getJSONObject(0);
                            if (pixRequisicaoDto.getPixDto().getStatus().equals(PixStatusEnum.CONCLUIDA.toString())) {
                                statusCorreto++;
                                resultado = "CORRETO";
                            }

                        } catch (Exception e) {
                            //se caiu no catch é porque a lista está vazia e o pix com a situação CONCLUIDA está EXPIRADO na verdade
                            if (pixRequisicaoDto.getPixDto().getStatus().equals(PixStatusEnum.CONCLUIDA.toString())) {
                                statusErrado++;
                                resultado = "ERRADO";
                                Uteis.logarDebug("Chave: " + chave + " | Cód. PIX Incorreto: " + pixVO.getCodigo());
                            }
                        } finally {
                            inserirResultado(pixVO.getCodigo(), resultado, con);
                        }
                    }

                } catch (Exception ex) {
//                    ex.printStackTrace();
                } finally {
                    pixDAO = null;
                    pixService = null;
                }
            }
            if (total > 0) {
                Uteis.logarDebug("Chave: " + chave + " | Total: " + total + " | Corretos: " + statusCorreto + " | Errados: " + statusErrado);
            }
            if (!somenteConsulta) {
                corrigirPixIncorretos(con, chave);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void corrigirPixIncorretos(Connection con, String chave) throws Exception {

        Pix pixDAO;
        Uteis.logarDebug("####Vou começar a corrigir os pix incorretos na chave..." + chave + "####");
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select pp.pix, pp.resultado, p.recibopagamento  \n");
            sql.append("from processopix pp  \n");
            sql.append("inner join pix p on p.codigo = pp.pix  \n");
            sql.append("where pp.resultado = 'ERRADO' \n");
            sql.append("and p.recibopagamento is not null \n");
            sql.append("and processo = '" + nomeProcesso + "'");

            ResultSet rs1 = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            pixDAO = new Pix(con);

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);

            if (total > 0) {
                Uteis.logarDebug("Encontrei " + total + " recibos para estornar, vou iniciar os estornos...");
            } else {
                Uteis.logarDebug("Não foi encontrado nenhum pix com recibo para ser estornado");
            }

            while (rs1.next()) {
                PixVO pixVO = pixDAO.consultarPorCodigo(rs1.getInt("pix"));
                Pessoa pessoa = new Pessoa(con);
                PessoaVO aluno = pessoa.consultarPorChavePrimaria(pixVO.getPessoa(), Uteis.NIVELMONTARDADOS_MINIMOS);
                String nomeAluno = aluno.getNome();

                Uteis.logarDebug("Estornando recibo " + rs1.getInt("recibopagamento") + " do aluno: " + nomeAluno + " | REF. PIX: " + rs1.getInt("pix"));
                boolean sucesso = estornarRecibo(rs1.getInt("recibopagamento"), pixVO, con);

                if (sucesso) {
                    Uteis.logarDebug("Recibo " + rs1.getInt("recibopagamento") + " estornado com sucesso!");
                    Uteis.logarDebug("Alterando status do pix " + rs1.getInt("pix") + " para EXPIRADO...");

                    try {
                        Pix pix = new Pix(con);
                        pix.alterarStatusAjusteManual(pixVO, PixStatusEnum.EXPIRADA);
                        Uteis.logarDebug("Status alterado com sucesso!");
                    } catch (Exception e) {
                        Uteis.logarDebug("Não consegui aterar o status do pix");
                    }

                }
            }
        } catch (Exception e) {
        } finally {
            pixDAO = null;
        }
    }

    public static boolean estornarRecibo(int codRecibo, PixVO pixVO, Connection con) throws Exception {

        try {
            ReciboPagamento reciboFacade = new ReciboPagamento(con);
            MovPagamento movPagamentoFacade = new MovPagamento(con);
            MovProdutoParcela movProdutoParcelaFacade = new MovProdutoParcela(con);

            EstornoReciboControle estorno = new EstornoReciboControle();
            EstornoReciboVO estornoVO = new EstornoReciboVO();
            estornoVO.setExcluirNFSe(true);

            Usuario usuarioDAO = new Usuario(con);
            UsuarioVO usuarioRecorrencia = usuarioDAO.getUsuarioRecorrencia();
            estornoVO.setResponsavelEstornoRecivo(usuarioRecorrencia);


            List<MovPagamentoVO> listaMovPagamento = new ArrayList();
            listaMovPagamento.addAll(movPagamentoFacade.consultarPorCodigoRecibo(codRecibo, false, Uteis.NIVELMONTARDADOS_TODOS));

            MovParcela movParcela = new MovParcela(con);
            estornoVO.setListaMovPagamento(listaMovPagamento);
            estornoVO.setListaMovParcela(movParcela.consultarPorPix(pixVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            if(UteisValidacao.emptyList(estornoVO.getListaMovParcela())){
                estornoVO.setListaMovParcela(movParcela.consultarPorCodigoRecibo(codRecibo, false, Uteis.NIVELMONTARDADOS_TODOS));
            }
            estornoVO.setReciboPagamentoVO(reciboFacade.consultarPorChavePrimaria(codRecibo, Uteis.NIVELMONTARDADOS_TODOS));

            estorno.setEstornoReciboVO(estornoVO);

            reciboFacade.estornarReciboPagamento(estornoVO, movPagamentoFacade, movProdutoParcelaFacade, null, null, Calendario.hoje(), con.getAutoCommit());

            try {
                Uteis.logarDebug("Registrando Log da operação...");
                registrarLogOperacao(con, codRecibo, usuarioRecorrencia, pixVO);
                Uteis.logarDebug("Log registrado com sucesso");
            } catch (Exception ignore) {
                Uteis.logarDebug("Não consegui registrar o log");
            }


            return true;
        } catch (Exception e) {
            Uteis.logarDebug("Não foi possível estornar o recibo: " + e);
            return false;
        }
    }

    private static void registrarLogOperacao(Connection con, int codRecibo, UsuarioVO usuarioRecorrencia, PixVO pixVO) {
        Log log = null;
        try {
            log = new Log(con);

            //criar Log
            LogVO obj = new LogVO();
            obj.setChavePrimaria(Integer.toString(codRecibo));
            obj.setNomeEntidade("RECIBOPAGAMENTO");
            obj.setNomeEntidadeDescricao("Recibo Pagamento");
            obj.setOperacao("ESTORNO - RECIBO PAGAMENTO");
            obj.setResponsavelAlteracao(usuarioRecorrencia.getNome());
            obj.setNomeCampo("TODOS");
            obj.setPessoa(pixVO.getPessoa());
            obj.setValorCampoAlterado("O Recibo de cód. " + codRecibo + " foi estornado automaticamente pois esse pix não foi pago de fato pelo aluno! Caso o aluno questione, exija comprovante de transferência.");
            obj.setValorCampoAnterior("");
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());

            log.incluirSemCommit(obj);
        } catch (Exception e) {

        }

    }
}

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import importador.ExportadorExcel;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.BoletoMovParcelaVO;
import negocio.comuns.financeiro.BoletoPJBankVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.BoletoMovParcela;
import negocio.facade.jdbc.financeiro.BoletoPJBank;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Date;

public class ProcessosPJBank {

    private static String nomeArquivo = "";

    public static File cancelarBoletos(Integer empresa,
                                       Integer convenioCobranca,
                                       String codigosBoleto,
                                       UsuarioVO usuarioVO,
                                       Connection con) throws Exception {

        nomeArquivo = "PJBankCancelamento";
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cl.matricula, \n");
        sql.append("p.nome, \n");
        sql.append("b.* \n");
        sql.append("from boleto b \n");
        sql.append("left join pessoa p on p.codigo = b.pessoa \n");
        sql.append("left join cliente cl on cl.pessoa = b.pessoa \n");
        sql.append("inner join conveniocobranca c on c.codigo = b.conveniocobranca   \n");
        sql.append("where b.situacao in (").append(SituacaoBoletoEnum.GERADO.getCodigo()).append(",")
                .append(SituacaoBoletoEnum.AGUARDANDO_REGISTRO.getCodigo()).append(",")
                .append(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO.getCodigo()).append(") \n");
        sql.append("and b.recibopagamento is null \n");
        sql.append("and c.tipoconvenio = ").append(TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigo()).append( "\n");
        if (!UteisValidacao.emptyNumber(convenioCobranca)) {
            sql.append("and b.conveniocobranca = ").append(convenioCobranca).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and b.empresa = ").append(empresa).append(" \n");
        }
        if (!UteisValidacao.emptyString(codigosBoleto)) {
            sql.append("and b.codigo in (").append(codigosBoleto).append(") \n");
        }
        sql.append("order by b.codigo");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
        Integer atual = 0;

        if (UteisValidacao.emptyNumber(total)) {
            throw new Exception("Nenhum boleto apto para cancelamento encontrado!");
        }

        Boleto boletoDAO;
        Usuario usuarioDAO;
        try {
            Uteis.logarDebug("INICIO | ProcessosPJBank | cancelarBoletos | " + total + " Itens");

            boletoDAO = new Boleto(con);
            usuarioDAO = new Usuario(con);
            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                usuarioVO = usuarioDAO.getUsuarioRecorrencia();
            }

            JSONArray jsonArray = new JSONArray();
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        Uteis.logarDebug("Atual " + ++atual + "/" + total + " | ProcessosPJBank | cancelarBoletos");

                        JSONObject json = new JSONObject();

                        Integer boleto = 0;
                        String resultado = "";
                        try {
                            boleto = rs.getInt("codigo");
                            json.put("codigo_boleto", boleto);
                            json.put("matricula", rs.getString("matricula"));
                            json.put("nome", rs.getString("nome"));

                            BoletoVO boletoVO = boletoDAO.consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                            BoletoMovParcela boletoMovParcela = new BoletoMovParcela(con);
                            boletoVO.setListaBoletoMovParcela(boletoMovParcela.consultarPorCodigoBoleto(boletoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));

                            StringBuilder codParcelas = new StringBuilder();
                            StringBuilder descParcelas = new StringBuilder();
                            for (BoletoMovParcelaVO parcela :  boletoVO.getListaBoletoMovParcela()){
                                codParcelas.append(" | ").append(parcela.getMovParcelaVO().getCodigo());
                                descParcelas.append(" | ").append(parcela.getMovParcelaVO().getDescricao());
                             }

                            json.put("parcela_codigo", codParcelas.toString().replaceFirst(" \\| ", ""));
                            json.put("parcela_descricao", descParcelas.toString().replaceFirst(" \\| ", ""));
                            json.put("vencimento_boleto", Calendario.getDataAplicandoFormatacao(boletoVO.getDataVencimento(), "dd/MM/yyyy"));
                            json.put("valor_boleto", Uteis.arredondarForcando2CasasDecimais(boletoVO.getValor()));
                            json.put("link_boleto", boletoVO.getLinkBoleto());
                            if (!boletoVO.isPodeCancelar()) {
                                throw new Exception("Boleto não pode ser cancelado");
                            }
                            boletoDAO.cancelarBoleto(boletoVO, usuarioVO, "Processo Cancelamento - Configuração Sistema");
                            resultado = "Boleto Cancelado";
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            resultado = ex.getMessage();
                            Uteis.logarDebug("Boleto " + boleto + " | ERRO | " + ex.getMessage());
                        } finally {
                            json.put("resultado", resultado);
                            jsonArray.put(json);
                        }
                    }
                }
            }

            File file = new ExportadorExcel().gerarArquivoExcel(nomeArquivo, jsonArray, DAO.resolveKeyFromConnection(con));
            System.out.println(file.getAbsolutePath());
            return file;
        } finally {
            boletoDAO = null;
            usuarioDAO = null;
            Uteis.logarDebug("FIM | ProcessosPJBank | cancelarBoletos");
        }
    }


    public static void cancelarBoletos58DiasVencidos(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT b.codigo, b.datavencimento \n");
        sql.append("FROM boleto b \n");
        sql.append("INNER JOIN conveniocobranca c on c.codigo = b.conveniocobranca \n");
        sql.append("WHERE b.datavencimento <= CURRENT_DATE - INTERVAL '58 days' \n");
        sql.append("AND c.tipoconvenio = ").append(TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigo()).append(" \n");
        sql.append("AND b.situacao in (").append(SituacaoBoletoEnum.GERADO.getCodigo()).append(",")
                .append(SituacaoBoletoEnum.AGUARDANDO_REGISTRO.getCodigo()).append(",")
                .append(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO.getCodigo()).append(") \n");
        sql.append("ORDER BY b.codigo");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
        Integer atual = 0;

        if (UteisValidacao.emptyNumber(total)) {
            Uteis.logarDebug("Nenhum boleto vencido há mais de 58 dias encontrado!");
            return;
        }

        Boleto boletoDAO = null;
        Usuario usuarioDAO = null;
        try {
            Uteis.logarDebug("INICIO | ProcessosPJBank | cancelarBoletos58DiasVencidos | " + total + " Itens");

            boletoDAO = new Boleto(con);
            usuarioDAO = new Usuario(con);
            UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();

            try (Statement stm = con.createStatement(); ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    Uteis.logarDebug("Atual " + ++atual + "/" + total + " | ProcessosPJBank | cancelarBoletos58DiasVencidos");

                    Integer codigoBoleto = rs.getInt("codigo");
                    Date dataVencimento = rs.getDate("datavencimento");

                    int diferencaDias = Calendario.diferencaEmDias(dataVencimento, Calendario.hoje());

                    if(diferencaDias <= 90) {
                        BoletoVO boletoVO = boletoDAO.consultarPorChavePrimaria(codigoBoleto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if(boletoVO.isPodeCancelar()) {
                            try {
                                Uteis.logarDebug("Boleto " + codigoBoleto + " Vencido há " + diferencaDias + " dias. Vou tentar cancelar na API.");
                                boletoDAO.cancelarBoleto(boletoVO, usuarioVO, "Processo Cancelamento Automático - Boleto vencido há mais de 58 dias");
                                Uteis.logarDebug("Boleto " + codigoBoleto + " cancelado com sucesso");
                            } catch(Exception ex) {
                                ex.printStackTrace();
                                boletoDAO.alterarSituacaoGeral(boletoVO, SituacaoBoletoEnum.CANCELADO, "Processo Cancelamento Automático - Boleto vencido há mais de 58 dias");
                            }
                        }
                    } else {
                        Uteis.logarDebug("Boleto " + codigoBoleto + " Vencido há mais de 90 dias, Vou alterar situação no banco de dados.");
                        BoletoVO boletoVO = new BoletoVO();
                        boletoVO.setCodigo(codigoBoleto);
                        boletoVO.setSituacao(SituacaoBoletoEnum.CANCELADO);
                        boletoDAO.alterarSituacaoGeral(boletoVO, SituacaoBoletoEnum.CANCELADO, "Cancelamento direto - vencido há mais de 90 dias");
                    }
                }
            }
        } finally {
            boletoDAO = null;
            usuarioDAO = null;
            Uteis.logarDebug("FIM | ProcessosPJBank | cancelarBoletos58DiasVencidos");
        }
    }


    public static void main(String[] args) {
        try {
            Uteis.debug = true;

//            String chave = null;
//            if (args.length > 0) {
//                chave = args[0];
//            }

//            if (UteisValidacao.emptyString(chave)) {
//                throw new Exception("Chave não informada");
//            }

//            Uteis.logarDebug("Obter conexão para chave: " + chave);
//                Connection con = DriverManager.getConnection("************************************************", "zillyonweb", "pactodb");
//            Connection con = new DAO().obterConexaoEspecifica(chave);
//                ProcessosPJBank.cancelarBoletos(0, null, null, con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}


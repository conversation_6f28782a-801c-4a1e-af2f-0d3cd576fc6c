package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Marcos Andre",
        data = "19/03/2025",
        descricao = "Corrigi dados de contrato com vigencia nula e sem historico",
        motivacao = "IN-1260")
public class CorrecaoDadosIN1260 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update contrato con set vigenciade = (select c.vigenciaate - interval '1 day' * 30 * c2.numeromeses from contrato c inner join contratoduracao c2 on c2.contrato = c.codigo where c.codigo = con.codigo )where vigenciade is null;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO public.historicocontrato (datafinalsituacao, tipohistorico, datainiciosituacao, situacaorelativahistorico, dataregistro, responsavelregistro, descricao, contrato,  retornomanual) select coalesce ((select min(datafinalsituacao) from historicocontrato h2 where contrato = c.codigo) - interval '1 day', c.vigenciaateajustada) as datafinal ,'TE', c.vigenciade,'', c.datalancamento, c.responsavelcontrato, 'TRANSFERIDO DA EMPRESA '||e.nome||', CONTRATO '||c.contratoorigemtransferencia as descricao,c.codigo, false  from contrato c left join historicocontrato h on c.codigo = h.contrato and h.tipohistorico in ('MA', 'RE', 'RN', 'TE', 'CT') inner join contrato o on o.codigo = c.contratoorigemtransferencia inner join empresa e on e.codigo = o.empresa where h.codigo is null  and c.situacaocontrato = 'TF';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO public.historicocontrato (datafinalsituacao, tipohistorico, datainiciosituacao, situacaorelativahistorico, dataregistro, responsavelregistro, descricao, contrato,  retornomanual) select coalesce ((select min(datafinalsituacao) from historicocontrato h2 where contrato = c.codigo) - interval '1 day', c.vigenciaateajustada) as datafinal ,'MA', c.vigenciade,'', c.datalancamento, c.responsavelcontrato, 'MATRICULADO' as descricao,c.codigo, false  from contrato c left join historicocontrato h on c.codigo = h.contrato and h.tipohistorico in ('MA', 'RE', 'RN', 'TE', 'CT') where h.codigo is null  and c.situacaocontrato = 'MA';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO public.historicocontrato (datafinalsituacao, tipohistorico, datainiciosituacao, situacaorelativahistorico, dataregistro, responsavelregistro, descricao, contrato,  retornomanual) select coalesce ((select min(datafinalsituacao) from historicocontrato h2 where contrato = c.codigo) - interval '1 day', c.vigenciaateajustada) as datafinal ,'RE', c.vigenciade,'', c.datalancamento, c.responsavelcontrato, 'REMATRICULADO' as descricao,c.codigo, false  from contrato c left join historicocontrato h on c.codigo = h.contrato and h.tipohistorico in ('MA', 'RE', 'RN', 'TE', 'CT')  where h.codigo is null  and c.situacaocontrato = 'RE';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO public.historicocontrato (datafinalsituacao, tipohistorico, datainiciosituacao, situacaorelativahistorico, dataregistro, responsavelregistro, descricao, contrato,  retornomanual) select coalesce ((select min(datafinalsituacao) from historicocontrato h2 where contrato = c.codigo) - interval '1 day', c.vigenciaateajustada) as datafinal ,'RN', c.vigenciade,'', c.datalancamento, c.responsavelcontrato, 'RENOVADO' as descricao,c.codigo, false  from contrato c left join historicocontrato h on c.codigo = h.contrato and h.tipohistorico in ('MA', 'RE', 'RN', 'TE', 'CT')  where h.codigo is null  and c.situacaocontrato = 'RN';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update log lcon set pessoa = (select pessoa from contrato where codigo = lcon.chaveprimaria::int ) where operacao = 'INCLUSÃO DE CONTRATO IMPORTAÇÃO';", c);

        }
    }
}

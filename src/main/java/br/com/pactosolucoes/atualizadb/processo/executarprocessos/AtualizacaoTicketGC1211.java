package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rone Soares",
        data = "28/07/2025",
        descricao = "Criado os campos código da unidade organizacional Sesi na tabela de empresa, e o código conta contábil Sesi no cadastro de produto",
        motivacao = "GC-1211")
public class AtualizacaoTicketGC1211 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto ADD COLUMN codigocontacontabilsesi VARCHAR(50);", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN codunidadeorganizacionalsesi INTEGER;", c);
        }
    }
}

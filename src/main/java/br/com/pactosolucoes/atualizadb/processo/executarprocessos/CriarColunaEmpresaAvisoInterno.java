package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Joao Alcides",
        data = "11/09/2024",
        descricao = "add coluna empresa na tabela avisointerno",
        motivacao = "add coluna empresa na tabela avisointerno")
public class CriarColunaEmpresaAvisoInterno implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table avisointerno " +
                    "add column empresa int;", c);
        }
    }
}

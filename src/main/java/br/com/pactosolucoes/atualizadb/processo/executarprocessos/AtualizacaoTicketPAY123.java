package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Hibrael A Alves",
        data = "21/01/2025",
        descricao = "PAY-123 - Criação de configuração para habilitar atualização do valor previsto na tela de contas a pagar/receber",
        motivacao = "PAY-123 - Erro ao editar o valor de uma conta a receber/apagar na tela de pesquisa")
public class AtualizacaoTicketPAY123 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofinanceiro ADD COLUMN alterarValorPrevisto boolean DEFAULT FALSE;", c);
        }
    }

}

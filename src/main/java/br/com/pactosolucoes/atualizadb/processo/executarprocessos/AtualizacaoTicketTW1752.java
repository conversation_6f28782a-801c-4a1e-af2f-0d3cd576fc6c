package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "13/03/2025",
        descricao = "Aumentar limite de caracteres coluna apikey da configtotalpass",
        motivacao = "TW-1752 erro ao salvar apikey totalpass devido tamanho")
public class AtualizacaoTicketTW1752 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarConsulta("ALTER TABLE configtotalpass ALTER COLUMN apikey TYPE VARCHAR(255);", c);
        }
    }

}
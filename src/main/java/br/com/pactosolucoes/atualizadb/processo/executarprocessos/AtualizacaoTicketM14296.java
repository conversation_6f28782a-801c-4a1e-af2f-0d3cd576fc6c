package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Douglas Soares",
        data = "03/02/2025",
        descricao = "Adicionando o campo 'somenterecorrencia' na tabela 'mailingfiltros' para controle de filtros de mala direta.",
        motivacao = "Implementação de nova funcionalidade para filtro de recorrência no sistema - Ticket M1-4296"
)
public class AtualizacaoTicketM14296 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE mailingfiltros ADD COLUMN somenterecorrencia BOOLEAN DEFAULT FALSE;", c);
        }
    }
}

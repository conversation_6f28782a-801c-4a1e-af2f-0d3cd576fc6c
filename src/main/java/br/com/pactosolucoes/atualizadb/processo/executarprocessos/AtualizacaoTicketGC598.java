package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "17/09/2024",
        descricao = "GC-598",
        motivacao = "GC-598")
public class AtualizacaoTicketGC598 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE controlecreditotreino ADD COLUMN descricaoAulaMarcada VARCHAR(255);", c);
            SuperFacadeJDBC.executarUpdate("ALTER TABLE turma ADD COLUMN codigoTurmaOrigem integer;", con);
            SuperFacadeJDBC.executarUpdate("ALTER TABLE horarioturma ADD COLUMN codigoHorarioTurmaOrigem integer;", con);
        }
    }
}

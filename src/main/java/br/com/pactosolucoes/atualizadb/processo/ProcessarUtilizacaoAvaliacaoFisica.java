package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.UtilizacaoAvaliacaoFisicaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.UtilizacaoAvaliacaoFisica;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;

public class ProcessarUtilizacaoAvaliacaoFisica {
    public static void main(String[] args) throws Exception {
        Connection conTreino = DriverManager.getConnection("***********************************************", "postgres", "pactodb");
        Connection conZW = DriverManager.getConnection("**************************************************", "postgres", "pactodb");

        processarAvaliacoes(conTreino, conZW);
    }

    private static void processarAvaliacoes(Connection conTreino, Connection conZW) throws Exception {
        String sqlAvaliacoes= "select a.codigo as avaliacao,c.codigocliente, a.dataavaliacao from avaliacaofisica a inner join  clientesintetico c on a.cliente_codigo  = c.codigo  where a.dataavaliacao is not null  order by 1";
        ResultSet rsAvaliacoes = SuperFacadeJDBC.criarConsulta(sqlAvaliacoes, conTreino);
        MovProduto movProdutoDao = new MovProduto(conZW);
        UtilizacaoAvaliacaoFisica utilizacaoAvaliacaoFisicaDao = new UtilizacaoAvaliacaoFisica(conZW);
        while(rsAvaliacoes.next()){
            if(!SuperFacadeJDBC.existe("select avaliacaofisica  from utilizacaoavaliacaofisica u where avaliacaofisica = "+ rsAvaliacoes.getInt("avaliacao"),conZW )){
                MovProdutoVO movProdutoAvalicao = movProdutoDao.consultarAvaliacaoVigente(rsAvaliacoes.getInt("codigocliente"), rsAvaliacoes.getDate("dataavaliacao"), Uteis.NIVELMONTARDADOS_MINIMOS);
                if (movProdutoAvalicao != null) {
                    UtilizacaoAvaliacaoFisicaVO utilizacao = new UtilizacaoAvaliacaoFisicaVO();
                    utilizacao.setMovProdutoVO(movProdutoAvalicao);
                    utilizacao.setCodAvaliacaoFisica(rsAvaliacoes.getInt("avaliacao"));
                    utilizacao.setDataAvaliacaoFisica(rsAvaliacoes.getDate("dataavaliacao"));
                    utilizacao.setPrimeiraavaliacao(utilizacaoAvaliacaoFisicaDao.findByMovproduto(movProdutoAvalicao.getCodigo()).isEmpty());
                    utilizacaoAvaliacaoFisicaDao.gravar(utilizacao);
                }
            }
        }

    }
}

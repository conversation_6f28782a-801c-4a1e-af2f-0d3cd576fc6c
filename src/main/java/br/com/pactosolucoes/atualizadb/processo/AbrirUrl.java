package br.com.pactosolucoes.atualizadb.processo;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.sql.Connection;
import java.sql.DriverManager;

public class AbrirUrl {

    public static void main(String[] args) {
        String caminhoArq = "C:\\opt\\selfit\\ThreadExecucaoFila-190114-190120.log";
        File fileLeitura = new File(caminhoArq);

        try {
            Connection con = DriverManager.getConnection("*********************************************************************************", "zillyonweb", "pactodb2020");

            FileReader fileReader = new FileReader(fileLeitura);
            BufferedReader reader = new BufferedReader(fileReader);
            String data;
            int j = 0;
            while ((data = reader.readLine()) != null) {
                if (data.contains("@URL_APLICACAO@")) {
                    j++;
                    String[] partes = data.split("@URL_APLICACAO@");
                    if (j == 300) {
                        System.out.println(partes[1]);
                    }
                    URL url = new URL("http://lb-zw-web-939419841.sa-east-1.elb.amazonaws.com/app" + partes[1]);
                    InputStream is = url.openConnection().getInputStream();
                    BufferedReader readerUrl = new BufferedReader(new InputStreamReader(is));
                    String line;
                    while ((line = readerUrl.readLine()) != null) {
                        System.out.println(line);
                    }
                    readerUrl.close();

                }
                if (j == 400) {
                    int quantidadeConexoes = SuperFacadeJDBC.contar("select count(*) from pg_stat_activity ", con);
                    System.out.println("Quantidade de conexões: " + quantidadeConexoes);
                    while (quantidadeConexoes > 400) {
                        System.out.println("Aguardando conexões no banco de dados liberarem");
                        Thread.sleep(30 * 1000);
                        quantidadeConexoes = SuperFacadeJDBC.contar("select count(*) from pg_stat_activity ", con);
                        System.out.println("Quantidade de conexões: " + quantidadeConexoes);
                    }
                    j = 0;
                }
            }
            fileReader.close();
            reader.close();
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
    }


//    public static void main(String[] args) {
//        String caminhoArq = "C:\\Users\\<USER>\\Downloads\\LinksSalvos.txt";
//        String caminhoSalvar = "C:\\Users\\<USER>\\Downloads\\XMLs\\";
//        File fileLeitura = new File(caminhoArq);
//
//        try {
//            File destino = new File(caminhoSalvar);
//            if (!destino.exists()) {
//                destino.mkdir();
//            }
//
//            FileReader fileReader = new FileReader(fileLeitura);
//            BufferedReader reader = new BufferedReader(fileReader);
//            String data;
//            while ((data = reader.readLine()) != null) {
//                URL url = new URL("http://" + data);
//                InputStream is = url.openConnection().getInputStream();
//                BufferedReader readerUrl = new BufferedReader(new InputStreamReader(is));
//                String line;
//                while ((line = readerUrl.readLine()) != null) {
//                    String[] partes = data.split("/");
//                    String nomeArquivo = partes[partes.length - 1];
//                    File xml = new File(caminhoSalvar + nomeArquivo);
//                    FileUtils.writeStringToFile(xml, line);
//                }
//                readerUrl.close();
//            }
//            fileReader.close();
//            reader.close();
//        } catch (Exception ex) {
//            System.out.println(ex.getMessage());
//        }
//    }
}

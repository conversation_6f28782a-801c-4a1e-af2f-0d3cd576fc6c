package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Marcos Andre",
        data = "08/04/2025",
        descricao = "Adicionar os produtos cancelados para manter rateio no financeiro",
        motivacao = "M1-4211")
public class AdicionarProdutosCancelados implements MigracaoVersaoInterface{
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movpagamento ADD COLUMN produtospagoscancelados text default '';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cartaocredito ADD COLUMN produtospagoscancelados text default '';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cheque ADD COLUMN produtospagoscancelados text default '';", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "10/07/2024",
        descricao = "Atualização da tabela Empresa com os campos da configuraçao da integração Nuvemshop",
        motivacao = "GC-745")
public class AtualizacaoTabelaEmpresaGC745 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            String sql = "ALTER TABLE empresa " +
                    "ADD COLUMN integracaoNuvemshopNomeApp VARCHAR(255) DEFAULT '', " +
                    "ADD COLUMN integracaoNuvemshopEmail VARCHAR(255) DEFAULT '', " +
                    "ADD COLUMN integracaoNuvemshopTokenAcesso VARCHAR(255) DEFAULT '', " +
                    "ADD COLUMN integracaoNuvemshopHabilitada BOOLEAN DEFAULT FALSE, " +
                    "ADD COLUMN integracaoNuvemshopStoreId VARCHAR(255) DEFAULT ''; ";
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}

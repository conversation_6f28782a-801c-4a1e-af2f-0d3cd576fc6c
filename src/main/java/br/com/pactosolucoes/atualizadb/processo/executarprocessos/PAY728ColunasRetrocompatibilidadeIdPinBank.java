package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "30/04/2025",
        descricao = "Criar colunas para garantir retrocompatibilidade com o schema do banco de dados.",
        motivacao = "PAY-728")
public class PAY728ColunasRetrocompatibilidadeIdPinBank implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE operadoracartao ADD COLUMN codigointegracaopinbank integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaoCobrancaCliente ADD COLUMN idPinBank VARCHAR(100);", c);
        }
    }
}

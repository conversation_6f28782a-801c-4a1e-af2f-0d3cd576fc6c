package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProcessoCorrecaoRecibosPix {

    public static void processar(Connection c) throws SQLException {
        processarValorPagoIncorreto(c);
    }

    private static void processarValorPagoIncorreto(Connection c) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("('update pagamentomovparcela set valorpago = '||valorparcela||' where codigo = '||pagamentomovparcela||';') as sql \n");
        sql.append("from ( \n");
        sql.append("select  \n");
        sql.append("pmp.codigo as pagamentomovparcela, \n");
        sql.append("mp.codigo as parcela, \n");
        sql.append("mp.valorparcela, \n");
        sql.append("pmp.valorpago \n");
        sql.append("from movparcela mp  \n");
        sql.append("inner join empresa e on e.codigo = mp.empresa \n");
        sql.append("inner join pessoa p on p.codigo = mp.pessoa \n");
        sql.append("inner join pagamentomovparcela pmp on pmp.movparcela = mp.codigo \n");
        sql.append("inner join movpagamento mov on mov.codigo = pmp.movpagamento \n");
        sql.append("inner join formapagamento fp on fp.codigo = mov.formapagamento \n");
        sql.append("where fp.tipoformapagamento = 'PX') as sql \n");
        sql.append("where valorparcela <> valorpago \n");
        try (Statement stm = c.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    SuperFacadeJDBC.executarUpdateExecutarProcessos(rs.getString("sql"), c);
                }
            }
        }
    }

    public static void ajustarRecibos(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("*  \n");
        sql.append("from ( \n");
        sql.append("select  \n");
        sql.append("r.pessoapagador, \n");
        sql.append("p.nome as pessoa, \n");
        sql.append("r.codigo as recibo, \n");
        sql.append("r.data::date as data, \n");
        sql.append("r.valortotal as valorrecibo, \n");
        sql.append("(select sum(valorpago) from pagamentomovparcela where movpagamento = mp.codigo) as soma, \n");
        sql.append("e.nome as empresa, \n");
        sql.append("exists(select codigo from contrato where pessoa = p.codigo and codigo > (\n" +
                "select \n" +
                "distinct(mp1.contrato) \n" +
                "from movparcela mp1 \n" +
                "inner join pagamentomovparcela pmp1 on pmp1.movparcela = mp1.codigo\n" +
                "where pmp1.recibopagamento = r.codigo\n" +
                ")) as existeoutrocontrato \n");
        sql.append("from recibopagamento r \n");
        sql.append("inner join empresa e on e.codigo = r.empresa \n");
        sql.append("inner join pessoa p on p.codigo = r.pessoapagador \n");
        sql.append("inner join movpagamento mp on mp.recibopagamento = r.codigo \n");
        sql.append("inner join formapagamento fp on fp.codigo = mp.formapagamento \n");
        sql.append("where fp.tipoformapagamento = 'PX') as sql \n");
        sql.append("where valorrecibo::numeric < soma::numeric \n");
        sql.append("order by data desc  \n");

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql.toString() + ") as qtd", con);
        int i = 0;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    Integer recibo = 0;
                    try {
                        con.setAutoCommit(false);

                        recibo = rs.getInt("recibo");
                        Uteis.logarDebug(++i + "/" + total + " - Ajustar recibo -> " + recibo);

                        Date dataRecibo = rs.getDate("data");
                        Double valorRecibo = rs.getDouble("valorrecibo");
                        Double soma = rs.getDouble("soma");
                        boolean existeoutrocontrato = rs.getBoolean("existeoutrocontrato");
                        if (existeoutrocontrato) {
                            throw new Exception("Existe contrato mais recente");
                        }

                        Double valorPago = obterValorPago(recibo, con);

                        if (Uteis.arredondarForcando2CasasDecimais(valorRecibo) != valorPago) {
                            throw new Exception("Valor pago não bate");
                        }

                        List<MovParcelaVO> listaParcelasReabrir = obterParcelasReabrir(recibo, con);
                        for (MovParcelaVO movParcelaVO : listaParcelasReabrir) {

                            Uteis.logarDebug("Reabrir parcela: " + movParcelaVO.getCodigo() + " | " + movParcelaVO.getDescricao());

                            //remover relacionamento do recibo com movprodutoparcela
                            SuperFacadeJDBC.executarUpdate("update movprodutoparcela set recibopagamento = null where movparcela = " + movParcelaVO.getCodigo(), con);
                            //excluir pagamentomovparcela
                            SuperFacadeJDBC.executarUpdate("delete from pagamentomovparcela where movparcela  = " + movParcelaVO.getCodigo(), con);
                            //reabrir produtos
                            SuperFacadeJDBC.executarUpdate("update movproduto set situacao = 'EA' where codigo in (select movproduto from movprodutoparcela where movparcela = " + movParcelaVO.getCodigo() + ")", con);
                            //reabrir parcelas
                            SuperFacadeJDBC.executarUpdate("update movparcela set situacao = 'EA' where codigo = " + movParcelaVO.getCodigo(), con);
                        }

                        con.commit();
                    } catch (Exception ex) {
                        con.rollback();
                        ex.printStackTrace();
                        Uteis.logarDebug("### Erro recibo -> " + recibo + " | Erro: " + ex.getMessage());
                    } finally {
                        con.setAutoCommit(true);
                    }
                }
            }
        }
    }

    private static Double obterValorPago(Integer recibo, Connection con) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("sum(mp.valorparcela) as total \n");
        sql.append("from movparcela mp \n");
        sql.append("inner join pagamentomovparcela pm on pm.movparcela = mp.codigo \n");
        sql.append("inner join recibopagamento rp on rp.codigo = pm.recibopagamento \n");
        sql.append("where rp.data::date = mp.datavencimento::date  \n");
        sql.append("and pm.recibopagamento = ").append(recibo);
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return Uteis.arredondarForcando2CasasDecimais(rs.getDouble("total"));
                }
            }
        }
        return 0.0;
    }

    private static List<MovParcelaVO> obterParcelasReabrir(Integer recibo, Connection con) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("mp.codigo, \n");
        sql.append("mp.valorparcela, \n");
        sql.append("mp.descricao, \n");
        sql.append("mp.situacao, \n");
        sql.append("mp.datavencimento \n");
        sql.append("from movparcela mp \n");
        sql.append("inner join pagamentomovparcela pm on pm.movparcela = mp.codigo \n");
        sql.append("inner join recibopagamento rp on rp.codigo = pm.recibopagamento \n");
        sql.append("where rp.data::date <> mp.datavencimento::date \n");
        sql.append("and pm.recibopagamento = ").append(recibo);

        List<MovParcelaVO> lista = new ArrayList<>();
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    MovParcelaVO obj = new MovParcelaVO();
                    obj.setCodigo(rs.getInt("codigo"));
                    obj.setValorParcela(rs.getDouble("valorparcela"));
                    obj.setSituacao(rs.getString("situacao"));
                    obj.setDescricao(rs.getString("descricao"));
                    obj.setDataVencimento(rs.getDate("datavencimento"));
                    lista.add(obj);
                }
            }
        }
        return lista;
    }

//    public static void main(String[] args) {
//        try {
//            Connection con = DriverManager.getConnection("*************************************************************", "zillyonweb", "pactodb");
//            ProcessoCorrecaoRecibosPix.ajustarRecibos(con);
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//    }
}

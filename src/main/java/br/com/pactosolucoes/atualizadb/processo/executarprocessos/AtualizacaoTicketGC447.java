package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "18/06/2024",
        descricao = "Criar Tabela clienterestricao",
        motivacao = "GC-447")
public class AtualizacaoTicketGC447 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table clienterestricao (\n" +
                    "\tcodigo serial,\n" +
                    "\tnome varchar(80) NOT NULL, \n" +
                    "\tcodigoMatricula integer NOT NULL,\n" +
                    "\tcpf char(11) NOT NULL,\n" +
                    "\tobservacao text,\n" +
                    "\tcodigoempresa INTEGER NOT NULL,\n" +
                    "\tnomeempresa varchar(50) NOT NULL,\n" +
                    "\tchaveempresa varchar(50) NOT NULL,\n" +
                    "\ttipo char(2) NOT NULL,\n" +
                    "\tCONSTRAINT clienterestricao_pkey PRIMARY KEY (codigo) \n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_clienterestricao_cpf ON public.clienterestricao USING btree (cpf);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_clienterestricao_chaveempresa ON public.clienterestricao USING btree (chaveempresa);", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN utilizaGestaoClientesComRestricoes BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cliente ADD COLUMN dataInclusaoClienteRestricaoRedeEmpresa TIMESTAMP WITHOUT TIME ZONE;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * Created by Glauco on 15/09/2015
 */
public class CorrigirPosicaoEstoqueMultaJuros {

    public static void main(String[] args) throws SQLException {
        Connection con;
        try {
            con = DriverManager.getConnection("***********************************************************", "zillyonweb", "pactodb");
            corrigirPosicaoEstoque(con);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    private static void corrigirPosicaoEstoque(Connection con) throws Exception {
        try {
            con.setAutoCommit(false);

            Statement stmDesativarTrigger = con.createStatement();
            stmDesativarTrigger.execute("DROP TRIGGER IF EXISTS tg_bu_alterarestoque_produtoestoque ON produtoestoque;\n");

            String sqlAtualizarProdutoEstoque = "UPDATE produtoestoque SET estoque = 4 WHERE produto = 1034;";
            Statement stmAtualizar = con.createStatement();
            stmAtualizar.execute(sqlAtualizarProdutoEstoque);


            String sqlAtivarTrigger = "CREATE TRIGGER tg_bu_alterarestoque_produtoestoque\n" +
                    "  BEFORE UPDATE\n" +
                    "  ON produtoestoque\n" +
                    "  FOR EACH ROW\n" +
                    "  EXECUTE PROCEDURE fn_tg_before_alterarestoque();\n";
            Statement stmAtivarTrigger = con.createStatement();
            stmAtivarTrigger.execute(sqlAtivarTrigger);


            con.commit();
        } catch (Exception ex) {
            Uteis.logar(ex, CorrigirPosicaoEstoqueMultaJuros.class);
            con.rollback();
        } finally {
            con.setAutoCommit(true);
        }
    }
}

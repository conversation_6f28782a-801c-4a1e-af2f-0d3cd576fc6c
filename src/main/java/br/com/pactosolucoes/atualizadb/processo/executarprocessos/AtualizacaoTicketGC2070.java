package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rone Soares",
        data = "06/05/2025",
        descricao = "Aumentado o tamanho do campo nomeempresa na tabela planoempresaredeacesso, para resolver o erro ao gravar empresas no plano vip",
        motivacao = "GC-2070")

public class AtualizacaoTicketGC2070 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planoempresaredeacesso ALTER COLUMN nomeempresa TYPE VARCHAR(255);", c);
        }
    }
}

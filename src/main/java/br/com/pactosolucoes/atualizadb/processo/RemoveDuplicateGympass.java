package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import servicos.integracao.TreinoWSConsumer;

import java.sql.*;
import java.util.List;

public class RemoveDuplicateGympass {

    public static void main(String[] args) throws Exception{
        String url = "**************************************************";
        String user = "postgres";
        String password = "pactodb";
        String chave = "tayobasp";

        try (Connection conn = DriverManager.getConnection(url, user, password)) {
            Cliente clienteDao = new Cliente(conn);
            Usuario usuarioDao = new Usuario(conn);
            DAO dao = new DAO();
            dao.reloadProps(chave);

            UsuarioVO usuarioRecorrencia = usuarioDao.getUsuarioRecorrencia();

            String query = "SELECT gympassuniquetoken , COUNT(*) FROM cliente GROUP BY gympassuniquetoken HAVING COUNT(*) > 1";
            try (Statement stmt = conn.createStatement();
                 ResultSet resultSet = stmt.executeQuery(query)) {

                while (resultSet.next()) {
                    String token = resultSet.getString("gympassuniquetoken");

                    ResultSet rsDuplicado = stmt.executeQuery("select nome, matricula, c.codigo from cliente c \n" +
                            "inner join pessoa p on p.codigo = c.pessoa \n" +
                            "where gympassuniquetoken = '" + token + "'\n" +
                            "order by c.codigo DESC limit 1");

                    if(rsDuplicado.next()){
                        String matricula = rsDuplicado.getString("matricula");
                        System.out.println("EXCLUIR: " + rsDuplicado.getString("nome")
                                + " - " + rsDuplicado.getString("matricula"));
                        List<ClienteVO> lista = clienteDao.consultarPorCodigosMatricula(matricula,
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                        StringBuilder msg = new StringBuilder();
                        for (ClienteVO clienteVO : lista) {
                            boolean excluiuNoTreino;
                            try {
                                excluiuNoTreino = TreinoWSConsumer.excluirCliente(chave, clienteVO.getCodigo()).equals("ok");

                                if (excluiuNoTreino) {
                                    clienteDao.excluirClienteETodosSeusRelacionamentos(clienteVO, usuarioRecorrencia);
                                    msg.append(clienteVO.getMatricula()).append(" - Cliente possui registro no treino e ambos foram excluídos com sucesso. \n\n");
                                } else {
                                    msg.append(clienteVO.getMatricula()).append(" Treino está fora - O cliente não pode ser excluído, pois pode ficar com o registro órfão no treino. \n\n");
                                    throw new Exception("");
                                }

                                clienteDao.excluirClienteETodosSeusRelacionamentos(clienteVO, usuarioRecorrencia);
                                msg.append(clienteVO.getMatricula()).append(" - Cliente excluído com sucesso.");
//
                            } catch (Exception ex) {
                                msg.append(clienteVO.getMatricula()).append(" - Falha ao remover cliente." + ex.getMessage()).append(" \n\n");
                                throw new  Exception(msg.toString());
                            }
                        }
                    }

                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.ProcessoAjustarContratosBolsaSemParcela;
import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "14/10/2024",
        descricao = "Remover vínculos de clientes que deveriam estar sem vínculo com treino web",
        motivacao = "M1-3012")
public class AtualizacaoTicketM13039 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ProcessoAjustarContratosBolsaSemParcela.ajustarContratosBolsaSemParcela(c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.ProcessoAjustarPlanoCondicaoPagamento;
import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "02/04/2025",
        descricao = "Ajustar planos sem condição de pagamento",
        motivacao = "M1-5037")
public class AtualizacaoTicket_M1_5037 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ProcessoAjustarPlanoCondicaoPagamento.ajustarPlanoCondicaoPagamento(c);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "27/09/2024",
        descricao = "Ajustar retirar configuração ir tela Cobrar Transção da Forma Pagamento Cartão Crédito",
        motivacao = "M2-2221")
public class M22473AjustarIrTelaPagamentoCartaoCredito implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update empresa set irTelaPagamentoCartaoCreditoFormaPagamento = false;", c);
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;


public class ProcessoNovoCRM {

    public ProcessoNovoCRM() {
    }

    public static void preencherTeveContatoFecharMetaDetalhado(Connection con) throws SQLException, Exception {
        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("PreencherTeveContatoFecharMetaDetalhado - Início em : " + new Date());

        Date dia = Uteis.obterDataAnterior(Calendario.hoje(), 30);
        String dataPesquisa = Uteis.getData(dia);

        ResultSet rsTotalDetalhado = SuperFacadeJDBC.criarConsulta("select count(fmd.*) as total from fecharmetadetalhado fmd\n" +
                "inner join fecharmeta fm on fm.codigo = fmd.fecharmeta\n" +
                "where fmd.obtevesucesso = false\n" +
                "and fm.dataregistro >= '"+dataPesquisa+"'", con);
        int totalDetalhado = 0;
        if (rsTotalDetalhado.next()) {
            totalDetalhado = rsTotalDetalhado.getInt(1);
        }
        System.out.println("Total de Registros: " + totalDetalhado);

        StringBuilder sql = new StringBuilder();
        sql.append("select \n" +
                "fmd.codigo,\n" +
                "fm.dataregistro as datameta,\n" +
                "fmd.cliente,\n" +
                "fmd.passivo,\n" +
                "fmd.indicado\n" +
                "from fecharmetadetalhado fmd\n" +
                "inner join fecharmeta fm on fm.codigo = fmd.fecharmeta\n" +
                "where fmd.obtevesucesso = false\n" +
                "and fm.dataregistro >= '"+dataPesquisa+"'");
        ResultSet dados = con.prepareStatement(sql.toString()).executeQuery();

        int totalModificado = 0;
        int processado = 0;
        while (dados.next()) {
            ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
            FecharMetaDetalhadoVO fecharMetaDetalhadoVO = new FecharMetaDetalhadoVO();
            fecharMetaDetalhadoVO.setCodigo(dados.getInt("codigo"));
            fecharMetaDetalhadoVO.getFecharMeta().setDataRegistro(dados.getTimestamp("datameta"));
            fecharMetaDetalhadoVO.getCliente().setCodigo(dados.getInt("cliente"));
            fecharMetaDetalhadoVO.getPassivo().setCodigo(dados.getInt("passivo"));
            fecharMetaDetalhadoVO.getIndicado().setCodigo(dados.getInt("indicado"));
            fecharMetaDetalhadoVO.setTeveContato(zillyonWebFacade.getHistoricoContato().existeHistoricoDataExpecifica(fecharMetaDetalhadoVO));

            if (fecharMetaDetalhadoVO.isTeveContato()) {
                String updateFecharMetaDetalhado = "update fecharmetadetalhado set tevecontato = true where codigo = " + fecharMetaDetalhadoVO.getCodigo() + " ;";
                SuperFacadeJDBC.executarConsultaUpdate(updateFecharMetaDetalhado, con);
                totalModificado++;
            }

            processado++;
//            System.out.println("Atual: "  + processado + " de " + totalDetalhado);
        }

//
        String updateFecharMetaDetalhadoTodosComSucesso = "update fecharmetadetalhado set teveContato = true where codigo in (select fmd.codigo \n" +
                "from fecharmetadetalhado fmd\n" +
                "inner join fecharmeta fm on fm.codigo = fmd.fecharmeta\n" +
                "where fmd.obtevesucesso = true\n" +
                "and fm.dataregistro >= '"+dataPesquisa+"');";
        SuperFacadeJDBC.executarConsultaUpdate(updateFecharMetaDetalhadoTodosComSucesso, con);

//        System.out.println("Total de Registros: " + totalDetalhado);
//        System.out.println("Total Modificado: " + totalModificado);
//        System.out.println("PreencherTeveContatoFecharMetaDetalhado - Fim em : " + new Date());

        Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("Tempo Total PreencherTeveContatoFecharMetaDetalhado: " + (d2.getTime() - d1.getTime()));

    }

    public static void main(String[] args) {
        try {
            Connection con1 = DriverManager.getConnection("*******************************************", "zillyonweb", "pactodb");
            preencherTeveContatoFecharMetaDetalhado(con1);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public static void ajustarMetaAgendamentosPresenciais(Connection con) throws Exception {
        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();

        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from fecharmeta  where (metaatingida + repescagem) > meta and identificadormeta = 'AG'", con);
        while (rs.next()) {
            Integer fecharMeta = rs.getInt("codigo");

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("'meta' as ident, \n");
            sql.append("count(f.codigo) as qtd \n");
            sql.append("from fecharmetadetalhado f \n");
            sql.append("inner join agenda a on a.codigo = f.codigoorigem \n");
            sql.append("where 1 = 1 \n");
            sql.append("and tipoagendamento <> 'LI' \n");
            sql.append("and f.fecharmeta = ").append(fecharMeta).append(" \n");
            sql.append("UNION \n");
            sql.append("        select \n");
            sql.append("'metaatingida' as ident, \n");
            sql.append("count(f.codigo) as qtd \n");
            sql.append("from fecharmetadetalhado f \n");
            sql.append("inner join agenda a on a.codigo = f.codigoorigem \n");
            sql.append("where 1 = 1 \n");
            sql.append("and f.obtevesucesso = true \n");
            sql.append("and tipoagendamento <> 'LI' \n");
            sql.append("and f.fecharmeta = ").append(fecharMeta).append(" \n");
            sql.append("UNION \n");
            sql.append("        select \n");
            sql.append("'repescagem' as ident, \n");
            sql.append("count(f.codigo) as qtd \n");
            sql.append("from fecharmetadetalhado f \n");
            sql.append("inner join agenda a on a.codigo = f.codigoorigem \n");
            sql.append("where 1 = 1 \n");
            sql.append("and f.obtevesucesso = true \n");
            sql.append("and f.repescagem = true \n");
            sql.append("and tipoagendamento <> 'LI' \n");
            sql.append("and f.fecharmeta = ").append(fecharMeta).append(" \n");

            ResultSet rsValores = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            Double meta = 0.0;
            Double metaAtingida = 0.0;
            Double repescagem = 0.0;
            Double porcentagem = 0.0;
            while (rsValores.next()) {

                String ident = rsValores.getString("ident");
                Double qtd = rsValores.getDouble("qtd");

                if (ident.equals("meta")) {
                    meta = qtd;
                } else if (ident.equals("metaatingida")) {
                    metaAtingida = qtd;
                } else if (ident.equals("repescagem")) {
                    repescagem = qtd;
                }
            }

            try {
                if (meta == 0.0) {
                    porcentagem = 0.0;
                } else {
                    porcentagem = (((metaAtingida + repescagem) / meta) * 100.0);
                }
            } catch (Exception ignored) {
            }

            String updateCorrecao = "update fecharmeta set repescagem = "+repescagem+" , metaatingida = "+metaAtingida+", meta = "+meta+", porcentagem = "+porcentagem+" where codigo = " + fecharMeta + ";";
            SuperFacadeJDBC.executarConsultaUpdate(updateCorrecao, con);
        }

        Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("Tempo Total ajustarMetaAgendamentosPresenciais: " + (d2.getTime() - d1.getTime()));
    }
}

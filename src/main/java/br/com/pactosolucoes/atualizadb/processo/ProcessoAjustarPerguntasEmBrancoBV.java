package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAjustarPerguntasEmBrancoBV {

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "centrodetreinamentosynergy";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            ajustarVencimentoContratosPorDataInicioContrato(c);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAjustarPerguntasEmBrancoBV.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarVencimentoContratosPorDataInicioContrato(Connection con) {
        try {
            con.setAutoCommit(true);

            Uteis.logarDebug("INÍCIO | ProcessoAjustarPerguntasEmBrancoBV");
            StringBuilder sql = new StringBuilder();
            sql.append("select pc.codigo as perguntacliente, per.codigo as pergunta, per.descricao as perguntadescricao, \n");
            sql.append("	(select count(*) from respostapergunta where pergunta = per.codigo) as totalrespostas, \n");
            sql.append("	(select count(*) from respostapergcliente where perguntacliente = pc.codigo) as totalrespostascliente \n");
            sql.append("from respostapergcliente rpc \n");
            sql.append("inner join perguntacliente pc on pc.codigo = rpc.perguntacliente \n");
            sql.append("inner join pergunta per on per.descricao = pc.descricao \n");
            sql.append("where true \n");
            sql.append("and per.tipopergunta in ('ME', 'SE', 'SN') and pc.tipopergunta in ('ME', 'SE', 'SN') \n");
            sql.append("and length(trim(rpc.descricaorespota)) = 0 \n");
            sql.append("GROUP BY 1,2,3 \n");
            sql.append("ORDER BY pc.codigo \n");

            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        int totalrespostas = rs.getInt("totalrespostas");
                        int totalrespostascliente = rs.getInt("totalrespostascliente");
                        int pergunta = rs.getInt("pergunta");
                        int perguntacliente = rs.getInt("perguntacliente");


                        if (totalrespostas != totalrespostascliente) {
                            continue;
                        }

                        int contador_offset = 0;

                        StringBuilder sqlPerg = new StringBuilder();
                        sqlPerg.append("select codigo, descricaorespota as reposta from respostapergunta where pergunta = " + pergunta + " order by nrquestao");

                        try (Statement stmPerg = con.createStatement()) {
                            try (ResultSet rsPerg = stmPerg.executeQuery(sqlPerg.toString())) {
                                while (rsPerg.next()) {
                                    String descricao = "(select descricaorespota from respostapergunta where codigo = " + rsPerg.getInt("codigo") + ")";
                                    SuperFacadeJDBC.executarConsultaUpdate("UPDATE respostapergcliente SET descricaorespota = " + descricao + " \n" +
                                            "WHERE codigo = (select codigo from respostapergcliente where perguntacliente = " + perguntacliente + " \n" +
                                            "ORDER BY codigo OFFSET " + contador_offset + " LIMIT 1)", con);
                                    contador_offset += 1;
                                }
                            }
                        }
                    }
                }
            }
            Uteis.logarDebug("FIM | ProcessoAjustarPerguntasEmBrancoBV");
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoAjustarPerguntasEmBrancoBV - " + ex.getMessage());
        }
    }

}
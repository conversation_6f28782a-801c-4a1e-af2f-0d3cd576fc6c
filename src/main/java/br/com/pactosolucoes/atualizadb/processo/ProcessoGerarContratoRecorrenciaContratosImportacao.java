package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoDuracao;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.plano.Plano;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

public class ProcessoGerarContratoRecorrenciaContratosImportacao {

    public static void main(String[] args) throws Exception {

        String chave = "wpfitness";
        Integer codigoEmpresa = 1;
        String codigosContratos = "";

        DAO dao = new DAO();
        Connection con = dao.obterConexaoEspecifica(chave);

        gerarContratoRecorrenciaContratosImportacao(con, codigoEmpresa, codigosContratos);

    }

    private static void gerarContratoRecorrenciaContratosImportacao(Connection con, Integer codigoEmpresa, String codigosContratos) throws Exception {
        ContratoRecorrencia contratoRecorrenciaDAO = new ContratoRecorrencia(con);
        Contrato contratoDAO = new Contrato(con);
        MovParcela movparcelaDAO = new MovParcela(con);
        Plano planoDAO = new Plano(con);
        ContratoDuracao contratoDuracaoDAO = new ContratoDuracao(con);

        String sql = "SELECT con.codigo, con.situacao, con.vigenciade as inicio, con.vigenciaateajustada as fim FROM contrato con\n" +
                " INNER JOIN empresa e ON e.codigo = con.empresa \n" +
                " LEFT JOIN contratorecorrencia cr ON cr.contrato = con.codigo \n" +
                " INNER JOIN plano pla ON pla.codigo = con.plano\n" +
                "WHERE (con.situacao IN ('AT','TR') \n" +
                "       OR (con.situacao = 'IN' \n" +
                "           AND current_date > con.vigenciaateajustada" +
                "           AND current_date <=  con.vigenciaateajustada + (interval '1 day' * e.carenciarenovacao))) \n" + // contratos vencidos, aptos para renovação
                "AND pla.recorrencia IS TRUE \n" +
                "AND cr.codigo IS NULL \n";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql += " AND e.codigo = " + codigoEmpresa + "\n";
        }
        if(!UteisValidacao.emptyString(codigosContratos)){
            sql += " AND con.codigo IN (" + codigosContratos + ")\n";
        }
        sql += " ORDER BY con.codigo";

        int total = SuperFacadeJDBC.contar("select count(s.*) from (" + sql  + ") as s", con);
        int atual = 0;
        int sucesso = 0;
        int falha = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {
            try {
                con.setAutoCommit(false);

                System.out.printf("%d\\%d Processando contrato codigo: %d %s - %s até %s\n", ++atual, total, rs.getInt("codigo"),
                        rs.getString("situacao"),
                        Calendario.getDataAplicandoFormatacao(rs.getDate("inicio"), "dd/MM/yyyy"),
                        Calendario.getDataAplicandoFormatacao(rs.getDate("fim"), "dd/MM/yyyy"));

                ContratoVO contratoVO = contratoDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                contratoVO.setContratoDuracao(contratoDuracaoDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                PlanoVO planoVO = planoDAO.consultarPorChavePrimaria(contratoVO.getPlano().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);

                List<MovParcelaVO> parcelas = movparcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Ordenacao.ordenarListaReverse(parcelas, "codigo");

                if (parcelas.isEmpty()) {
                    Uteis.logarDebug("\tContrato sem parcelas. Contrato: " + contratoVO.getCodigo());
                    continue;
                }
                MovParcelaVO ultimaParcela = parcelas.get(0);
                if (UteisValidacao.emptyNumber(ultimaParcela.getValorParcela())) {
                    Uteis.logarDebug("\tUltima parcela sem valor. Contrato: " + contratoVO.getCodigo() + ", Parcela: " + ultimaParcela.getCodigo());
                    continue;
                }


                ContratoRecorrenciaVO contratoRecorrenciaVO = new ContratoRecorrenciaVO();
                contratoRecorrenciaVO.setContrato(contratoVO);
                contratoRecorrenciaVO.setDiaVencimentoAnuidade(planoVO.getPlanoRecorrencia().getDiaAnuidade());
                contratoRecorrenciaVO.setMesVencimentoAnuidade(planoVO.getPlanoRecorrencia().getMesAnuidade());
                contratoRecorrenciaVO.setDiaVencimentoCartao(Uteis.getDiaMesData(contratoVO.getVigenciaDe()));
                contratoRecorrenciaVO.setDiasCancelamentoAutomatico(planoVO.getPlanoRecorrencia().getQtdDiasAposVencimentoCancelamentoAutomatico());
                contratoRecorrenciaVO.setFidelidade(contratoVO.getContratoDuracao().getNumeroMeses());
                contratoRecorrenciaVO.setPessoa(contratoVO.getPessoa());
                contratoRecorrenciaVO.setRenovavelAutomaticamente(planoVO.getPlanoRecorrencia().getRenovavelAutomaticamente());
                contratoRecorrenciaVO.setValorMensal(ultimaParcela.getValorParcela());
                contratoRecorrenciaVO.setValorMensalNegociado(ultimaParcela.getValorParcela());
                contratoRecorrenciaVO.setValorAnuidade(0.0);
                contratoRecorrenciaVO.setAnuidadeNaParcela(false);
                contratoRecorrenciaVO.setCancelamentoProporcional(planoVO.getPlanoRecorrencia().isCancelamentoProporcional());
                contratoRecorrenciaDAO.incluir(contratoRecorrenciaVO);

                String updateContrato = "update contrato set regimerecorrencia = true, renovavelautomaticamente = ? where codigo = ? ";
                PreparedStatement pstm = con.prepareStatement(updateContrato);
                pstm.setBoolean(1, planoVO.getPlanoRecorrencia().getRenovavelAutomaticamente());
                pstm.setInt(2, contratoVO.getCodigo());
                pstm.execute();

                String updateParcelas = "UPDATE movparcela SET regimerecorrencia = TRUE WHERE contrato = ?";
                PreparedStatement pstmParcela = con.prepareStatement(updateParcelas);
                pstmParcela.setInt(1, contratoVO.getCodigo());
                pstmParcela.execute();

                con.commit();
                sucesso++;
            } catch (Exception ex) {
                falha++;
                ex.printStackTrace();
                con.rollback();
            } finally {
                con.setAutoCommit(true);
            }
        }

        contratoRecorrenciaDAO = null;
        contratoDAO = null;
        contratoDuracaoDAO = null;

        System.out.println("\nProcessamento finalizado");
        System.out.printf("Sucesso: %d\n", sucesso);
        System.out.printf("Falha: %d\n", falha);
    }

}

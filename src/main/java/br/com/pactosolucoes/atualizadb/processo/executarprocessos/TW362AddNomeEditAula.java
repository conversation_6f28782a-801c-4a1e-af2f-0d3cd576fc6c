package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anna Carolina",
        data = "30/04/2025",
        descricao = "Nova coluna na tabela de edição de aula temporária",
        motivacao = "TW-362")
public class TW362AddNomeEditAula implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE edicaoaulatemporaria ADD COLUMN Nome VARCHAR(255);", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "16/08/2024",
        descricao = "Alterando para permitir que o sistema cobre a multa de cancelamento mesmo quando contrato possui menos que 7 dias de utilização, por padrão o sistema vai isentar os contratos com menos de 7 dias de utilização",
        motivacao = "M1-2543")
public class AtualizacaoTicketM12543 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa SET isentarcancelamento7dias = TRUE WHERE (NOT cancelamentoantecipado AND NOT cancelamentoAvaliandoParcelas)", c);
        }
    }

}

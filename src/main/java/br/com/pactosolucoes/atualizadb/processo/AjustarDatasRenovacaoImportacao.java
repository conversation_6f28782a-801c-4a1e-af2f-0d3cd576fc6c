/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class AjustarDatasRenovacaoImportacao {

    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("*********************************************************************", "postgres", "pactodb");
            ajustarDatasRenovacao(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarDatasRenovacao(Connection con) throws Exception {
        Uteis.logarDebug("Iniciando processo: ajustar datas de renovação");
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select c.codigo as antigo, c.vigenciaateajustada,c.contratoresponsavelrenovacaomatricula  from contrato c "
                + "where c.contratoresponsavelrenovacaomatricula    > 0 ", con);
        int cont = 1;

        ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");

        if (!JSFUtilities.isJSFContext()) {
            config = new ConfiguracaoSistemaControle();
            config.setRodandoAjustarDatasRenovacaoImportacao(true);
        }

        while (consulta.next()) {
            ResultSet consultaRenovacao = SuperFacadeJDBC.criarConsulta("select c.codigo as novo from contrato c "
                    + "where c.situacaocontrato = 'RN' and c.codigo =  " + consulta.getInt("contratoresponsavelrenovacaomatricula")
                    + " and c.vigenciade - '" + Uteis.getDataJDBCTimestamp(consulta.getDate("vigenciaateajustada")) + "' < '1 day' ;", con);
            while (consultaRenovacao.next()) {

                if (config.isRodandoAjustarDatasRenovacaoImportacao() || config.isRodandoExecutarProcessos()) {
                    Date novaData = Uteis.obterDataFutura2(consulta.getDate("vigenciaateajustada"), 1);
                    String log = String.format("%d - Contrato %d foi alterado para iniciar no dia %s", ++cont, consultaRenovacao.getInt("novo"), Uteis.getDataJDBC(novaData));
                    Uteis.logarDebug(log);
                    config.setInformacaoAjustarDatasRenovacaoImportacao(log);
                    //EXECUÇÃO DE TODOS OS PROCESSOS
                    config.setInformacaoExecutarProcessos(log);

                    String sql = "update contrato set vigenciade  = ? where codigo = ?;";
                    try (PreparedStatement sqlAlterarNovo = con.prepareStatement(sql)) {
                        sqlAlterarNovo.setDate(1, Uteis.getDataJDBC(novaData));
                        sqlAlterarNovo.setInt(2, consultaRenovacao.getInt("novo"));
                        sqlAlterarNovo.execute();
                    }


                    sql = "update historicocontrato set datainiciosituacao = ? where contrato = ? and tipohistorico = ?;";
                    try (PreparedStatement sqlAlterarHis = con.prepareStatement(sql)) {
                        sqlAlterarHis.setDate(1, Uteis.getDataJDBC(novaData));
                        sqlAlterarHis.setInt(2, consultaRenovacao.getInt("novo"));
                        sqlAlterarHis.setString(3, "RN");
                        sqlAlterarHis.execute();
                    }

                    sql = "update periodoacessocliente set datainicioacesso= ? where codigo in (select codigo from periodoacessocliente  where contrato = ? order by codigo limit 1);";
                    try (PreparedStatement sqlAlterarPeriodo = con.prepareStatement(sql)) {
                        sqlAlterarPeriodo.setDate(1, Uteis.getDataJDBC(novaData));
                        sqlAlterarPeriodo.setInt(2, consultaRenovacao.getInt("novo"));
                        sqlAlterarPeriodo.execute();
                    }
                }
            }
        }
        config.setInformacaoAjustarDatasRenovacaoImportacao("Ajustes concluídos");
        config.setRodandoAjustarDatasRenovacaoImportacao(false);
    }
}

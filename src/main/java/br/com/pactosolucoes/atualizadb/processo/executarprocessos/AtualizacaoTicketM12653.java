package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "20/06/2024",
        descricao = "Apagar registro de grupos inexistentes de contratos",
        motivacao = "M1-2156")
public class AtualizacaoTicketM12653 implements MigracaoVersaoInterface{


    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)){
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT\n" +
                    "'UPDATE cliente SET titularplanocompartilhado = ' || cdp.titular || ' WHERE codigo = ' || cli.codigo || ';' AS sqlUpdate\n" +
                    "FROM contratodependente cdp\n" +
                    "INNER JOIN cliente cli ON cli.codigo = cdp.cliente\n" +
                    "WHERE NOW() BETWEEN cdp.datainicio AND cdp.datafinalajustada\n" +
                    "AND (cli.titularplanocompartilhado IS NULL OR titularplanocompartilhado = 0)", c);
            while (rs.next()) {
                String sqlUpdate = rs.getString("sqlUpdate");
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlUpdate, c);
            }
        }
    }

}

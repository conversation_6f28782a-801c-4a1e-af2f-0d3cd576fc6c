/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class ExcluirHistoricoContatoDuplicados {
     public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("*******************************************************", "postgres", "pactodb");
            apagarHistoricosRepetidos(con1);

        } catch (Exception ex) {
            Logger.getLogger(ExcluirHistoricoContatoDuplicados.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void apagarHistoricosRepetidos(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select count(cliente) as repeticoes ,maladireta, dia::date as data, min(codigo) as primeiro, max(codigo) as ultimo, cliente from historicocontato   group by cliente,maladireta,data having count(cliente) > 1", con);
        int cont = 0;

        while (consulta.next()) {
             ResultSet consultaExisteFMDprimeiro = SuperFacadeJDBC.criarConsulta("select exists(select codigo from fecharmetadetalhado  where historicocontato = "+consulta.getInt("primeiro")+") as temFMD",con);
             consultaExisteFMDprimeiro.next();
            if(!consultaExisteFMDprimeiro.getBoolean("temFMD")){
                SuperFacadeJDBC.executarConsultaUpdate("delete from historicocontato  where  codigo = " + consulta.getInt("primeiro"), con);
            } else { 
                ResultSet consultaExisteFMDultimo= SuperFacadeJDBC.criarConsulta("select exists(select codigo from fecharmetadetalhado  where historicocontato = "+consulta.getInt("ultimo")+") as temFMD",con);
                consultaExisteFMDultimo.next();
                if(!consultaExisteFMDultimo.getBoolean("temFMD")){
                    SuperFacadeJDBC.executarConsultaUpdate("delete from historicocontato  where  codigo = " + consulta.getInt("ultimo"), con);
                }
            }
                
            System.out.println(++cont + " - historico do cliente " + consulta.getInt("cliente") + " foi ajustado");
        }
    }
    
}

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.basico.MovimentoContaCorrenteClienteComposicaoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteClienteComposicao;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;

public class GerarMovPagamentosMovimento {
	
	public static void gravarDados(Connection con, FormaPagamentoVO formaPagamento) throws Exception{
		Uteis.logarDebug("Iniciando processo: GerarMovPagamentosMovimento");

		MovPagamento movPag = new MovPagamento(con);
		MovimentoContaCorrenteClienteComposicao movimentoComposicao = new MovimentoContaCorrenteClienteComposicao(con);

		ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
		if (!JSFUtilities.isJSFContext()) {
			config = new ConfiguracaoSistemaControle();
			config.setRodandoGerarMOVPagamentosMovimento(true);
		}

		String logInicio = String.format("GerarMovPagamentosMovimento  - início em : %s", new Date());
		Uteis.logarDebug(logInicio);
		config.setInformacaoGerarMOVPagamentosMovimento(logInicio);
		//EXECUÇÃO DE TODOS OS PROCESSOS
		config.setInformacaoExecutarProcessos(logInicio);

		StringBuilder sql = new StringBuilder();
		sql.append("SELECT mov.codigo, mov.saldoatual, mov.responsavelautorizacao, mov.dataregistro,mov.pessoa, p.nome, mov.pessoa, c.empresa  FROM movimentocontacorrentecliente AS mov ");
		sql.append(" INNER JOIN pessoa p ON p.codigo = mov.pessoa");
		sql.append(" INNER JOIN cliente c ON c.pessoa = mov.pessoa ");
		sql.append(" WHERE mov.codigo = (SELECT codigo FROM movimentocontacorrentecliente ");
		sql.append(" WHERE movimentocontacorrentecliente.pessoa = mov.pessoa  ");
		sql.append("  ORDER BY movimentocontacorrentecliente.codigo DESC  ");
		sql.append(" LIMIT 1) AND  ");
		sql.append(" mov.saldoatual > 0  ");
		sql.append(" AND  mov.codigo not in (select movimentocontacorrentecliente from movimentocontacorrenteclientecomposicao) ");
		try (ResultSet dados = con.prepareStatement(sql.toString()).executeQuery()) {
			MovPagamentoVO mov;
			MovimentoContaCorrenteClienteComposicaoVO composicao;
			while (dados.next()) {
				if (config.isRodandoGerarMOVPagamentosMovimento() && config.isRodandoExecutarProcessos()) {
					mov = new MovPagamentoVO();
					mov.setValorTotal(dados.getDouble("saldoatual"));
					mov.setValor(dados.getDouble("saldoatual"));
					mov.setFormaPagamento(formaPagamento);
					mov.getResponsavelPagamento().setCodigo(dados.getInt("responsavelautorizacao"));
					mov.setMovPagamentoEscolhida(true);
					mov.setNrParcelaCartaoCredito(0);
					mov.setCredito(Boolean.TRUE);
					mov.setNomePagador(dados.getString("nome"));
					mov.setDataLancamento(dados.getTimestamp("dataregistro"));
					mov.setDataPagamento(dados.getTimestamp("dataregistro"));
					mov.getPessoa().setCodigo(dados.getInt("pessoa"));
					mov.setDataQuitacao(dados.getDate("dataregistro"));
					mov.getEmpresa().setCodigo(dados.getInt("empresa"));
					mov.setCredito(true);

					movPag.incluir(mov);

					composicao = new MovimentoContaCorrenteClienteComposicaoVO();
					composicao.setMovimento(dados.getInt("codigo"));
					composicao.setMovpagamento(mov.getCodigo());

					movimentoComposicao.incluirSemCommit(composicao);
				}
			}
		}
		String logFinal = String.format("GerarMovPagamentosMovimento - fim em : %s", new Date());
		Uteis.logarDebug(logFinal);
		config.setInformacaoGerarMOVPagamentosMovimento(logFinal);
		//EXECUÇÃO DE TODOS OS PROCESSOS
		config.setInformacaoExecutarProcessos(logFinal);
		config.setRodandoGerarMOVPagamentosMovimento(false);
	}
	
	public static void atualizarMovPagamentosCredito(Connection con) throws Exception {
		Uteis.logarDebug("atualizar movpagamento  - início em : " + new Date());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT mov.codigo, mov.pessoa, mov.formapagamento FROM movpagamento AS mov ");
		sql.append(" WHERE mov.valor = 0 ");
		try (ResultSet dados = con.prepareStatement(sql.toString()).executeQuery()) {
			while (dados.next()) {
				Integer codigo = dados.getInt("codigo");
				Integer pessoa = dados.getInt("pessoa");
				Integer formaPagamento = dados.getInt("formapagamento");
				sql = new StringBuilder();
				sql.append("UPDATE movpagamento set movpagamentoorigemcredito = ").append(codigo);
				sql.append(" where credito = 't'  and ");
				sql.append(" pessoa  = ").append(pessoa);
				sql.append(" and formapagamento = ").append(formaPagamento).append("; ");
				SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
			}
			Uteis.logarDebug("atualizar movpagamento - fim em : " + new Date());
		}
	}

        public static void gerarMovPagamentosCredito(Connection con) throws Exception{
                FormaPagamento fp = new FormaPagamento(con);
                FormaPagamentoVO formaPagamento = fp.consultarPorTipoFormaPagamentoAtiva("CC", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
		MovPagamento movPag = new MovPagamento(con);
		MovimentoContaCorrenteClienteComposicao movimentoComposicao = new MovimentoContaCorrenteClienteComposicao(con);
		Uteis.logarDebug( "GerarMovPagamentosMovimento  - início em : "+ new Date());
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT mov.codigo, mov.saldoatual, mov.responsavelautorizacao, mov.dataregistro,mov.pessoa, p.nome, mov.pessoa, c.empresa  FROM movimentocontacorrentecliente AS mov ");
		sql.append(" INNER JOIN pessoa p ON p.codigo = mov.pessoa");
		sql.append(" INNER JOIN cliente c ON c.pessoa = mov.pessoa ");
		sql.append(" WHERE mov.codigo = (SELECT codigo FROM movimentocontacorrentecliente ");
		sql.append(" WHERE movimentocontacorrentecliente.pessoa = mov.pessoa  ");
		sql.append("  ORDER BY movimentocontacorrentecliente.codigo DESC  ");
		sql.append(" LIMIT 1) AND  ");
		sql.append(" mov.saldoatual > 0  ");
		sql.append(" AND  mov.codigo not in (select movimentocontacorrentecliente from movimentocontacorrenteclientecomposicao) ");
		try (ResultSet dados = con.prepareStatement(sql.toString()).executeQuery()) {
			MovPagamentoVO mov;
			MovimentoContaCorrenteClienteComposicaoVO composicao;
			while (dados.next()) {
				mov = new MovPagamentoVO();
				mov.setValorTotal(dados.getDouble("saldoatual"));
				mov.setValor(dados.getDouble("saldoatual"));
				mov.setFormaPagamento(formaPagamento);
				mov.getResponsavelPagamento().setCodigo(dados.getInt("responsavelautorizacao"));
				mov.setMovPagamentoEscolhida(true);
				mov.setNrParcelaCartaoCredito(0);
				mov.setCredito(true);
				mov.setNomePagador(dados.getString("nome"));
				mov.setDataLancamento(dados.getDate("dataregistro"));
				mov.setDataPagamento(dados.getDate("dataregistro"));
				mov.getPessoa().setCodigo(dados.getInt("pessoa"));
				mov.setDataQuitacao(dados.getDate("dataregistro"));
				mov.getEmpresa().setCodigo(dados.getInt("empresa"));

				movPag.incluir(mov);

				composicao = new MovimentoContaCorrenteClienteComposicaoVO();

				composicao.setMovimento(dados.getInt("codigo"));
				composicao.setMovpagamento(mov.getCodigo());

				movimentoComposicao.incluirSemCommit(composicao);
			}
		}
		Uteis.logarDebug( "GerarMovPagamentosMovimento - fim em : "+ new Date());
	}

	public static void main(String... args) {
		try {
			Connection con1 = DriverManager.getConnection("*********************************************************************","postgres", "pactodb");

			FormaPagamento fp = new FormaPagamento(con1);
			FormaPagamentoVO contaCorrente = fp.consultarPorTipoFormaPagamentoAtiva("CC", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
			gravarDados(con1, contaCorrente);

		} catch (Exception ex) {
			Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
		}
	}

	public static void receberConexao(Connection connection) throws Exception {
		FormaPagamento formaPagamento = new FormaPagamento(connection);
		FormaPagamentoVO contaCorrente = formaPagamento.consultarPorTipoFormaPagamentoAtiva("CC", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

		gravarDados(connection, contaCorrente);

	}


}

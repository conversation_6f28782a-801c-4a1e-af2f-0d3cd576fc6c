package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lu<PERSON> Felipe",
        data = "17/11/2024",
        descricao = "M2-2434 - Pix Itaú",
        motivacao = "M2-2434 - Pix Itaú")
public class AtualizacaoTicketM22434 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            StringBuilder tabela3 = new StringBuilder();
            tabela3.append("CREATE TABLE pixhistorico ( \n");
            tabela3.append("codigo serial PRIMARY KEY,  \n");
            tabela3.append("dataregistro TIMESTAMP WITHOUT TIME ZONE,  \n");
            tabela3.append("pix integer,  \n");
            tabela3.append("operacao CHARACTER VARYING, \n");
            tabela3.append("dados text \n");
            tabela3.append(") \n");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(tabela3.toString(), con);
        }
    }
}

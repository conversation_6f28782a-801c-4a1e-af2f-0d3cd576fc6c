/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
/**
 *
 * <AUTHOR>
 */
public class AjustarAcessosRepetidos {
 public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("**********************************************************************", "postgres", "pactodb");
            apagarAcessosRepetidos(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void apagarAcessosRepetidos(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select cliente,dthrentrada, count(cliente) as repeticoes, max(codigo) as ultimo from acessocliente GROUP BY cliente, dthrentrada having count(cliente) > 1 ", con);
        int cont = 0;

        while (consulta.next()) {
            System.out.println(++cont + " - cliente " + consulta.getInt("cliente") + " teve  " + consulta.getInt("repeticoes") +" no dia "+ consulta.getTimestamp("dthrentrada"));

            String sql = "delete from acessocliente  where dthrentrada =? and cliente = ? and codigo <> ?;";

            PreparedStatement sqlDeletar = con.prepareStatement(sql);

            sqlDeletar.setTimestamp(1, consulta.getTimestamp("dthrentrada"));
            sqlDeletar.setInt(2, consulta.getInt("cliente"));
            sqlDeletar.setInt(3, consulta.getInt("ultimo"));
            sqlDeletar.execute();
        }
    }
}

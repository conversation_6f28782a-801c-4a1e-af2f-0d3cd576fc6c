/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;


import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import importador.LeitorExcel;
import negocio.comuns.basico.*;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.hssf.usermodel.HSSFRow;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;


public class ImportarCadastrosExcel {

    public Cidade cidadeDAO;
    public CidadeVO cidadeVO;
    public Map<String, ClienteVO> alunosInseridos;
    public Cliente clienteDAO;

    public Colaborador colaboradorDAO;
    public ColaboradorVO colaboradorVO;

    public ConfiguracaoSistema configuracaoSistemaDAO;
    public ConfiguracaoSistemaVO configuracaoSistemaVO;

    public ImportarCadastrosExcel() {
    }

    private static void iniciarVariaveisFixas(ImportarCadastrosExcel importador) throws Exception {
        importador.cidadeVO = importador.cidadeDAO.consultarPorNome("BELO HORIZONTE", Uteis.NIVELMONTARDADOS_MINIMOS);
        importador.configuracaoSistemaVO = importador.configuracaoSistemaDAO.buscarPorCodigo(1, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        importador.colaboradorVO = importador.colaboradorDAO.consultarPorNomeColaborador("PACTO", 1, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public static void importarClientes(String caminhoArquivo, ImportarCadastrosExcel importador) throws Exception {
        List<HSSFRow> linhas = LeitorExcel.lerLinhas(caminhoArquivo);

        for (HSSFRow linha : linhas) {
            montarExcel(linha, importador);
        }

        Set<String> nomes = importador.alunosInseridos.keySet();
        int i = 0;
        int qtdTotal = nomes.size();
        for (String nome : nomes) {
            ClienteVO cliente = importador.alunosInseridos.get(nome);
            System.out.println(++i + "/" + qtdTotal + " - " + cliente.getNome_Apresentar());
            importador.clienteDAO.incluir(cliente, importador.configuracaoSistemaVO);
        }

    }

    private static void montarExcel(HSSFRow linha, ImportarCadastrosExcel importador) throws Exception {
        String nomeCliente = LeitorExcel.obterString(linha, 3);
        if (importador.alunosInseridos.get(nomeCliente) == null) {

            String cpf = LeitorExcel.obterStringDoNumero(linha, 0);
            while (cpf.length() < 11) {
                cpf = "0" + cpf;
            }
            cpf = (Formatador.removerMascara(cpf).length() > 11 ? Formatador.formatarString("##.###.###/####-##", Formatador.removerMascara(cpf))
                    : Formatador.formatarString("###.###.###-##", Formatador.removerMascara(cpf)));

            TelefoneVO telefoneVO = new TelefoneVO();
            telefoneVO.setTipoTelefone(TipoTelefoneEnum.CELULAR.getCodigo());
            String ddd = UteisValidacao.emptyString(LeitorExcel.obterStringDoNumero(linha, 1)) ? LeitorExcel.obterString(linha, 1) : LeitorExcel.obterStringDoNumero(linha, 1);
            String telefone = UteisValidacao.emptyString(LeitorExcel.obterStringDoNumero(linha, 2)) ? LeitorExcel.obterString(linha, 2) : LeitorExcel.obterStringDoNumero(linha, 2);
            telefoneVO.setNumero("(" + ddd + ")" + telefone);

            EnderecoVO enderecoVO = new EnderecoVO();
            enderecoVO.setTipoEndereco(TipoEnderecoEnum.RESIDENCIAL.getCodigo());
            enderecoVO.setEndereco(LeitorExcel.obterString(linha, 5));
            enderecoVO.setNumero(LeitorExcel.obterString(linha, 6));
            enderecoVO.setComplemento(LeitorExcel.obterString(linha, 7));
            enderecoVO.setBairro(LeitorExcel.obterString(linha, 8));
            enderecoVO.setCep(Formatador.formatarString("#####-###", LeitorExcel.obterString(linha, 11)));
            if (enderecoVO.getEndereco().length() == 0 && enderecoVO.getNumero().length() == 0) {
                enderecoVO.setEndereco("SEM RUA");
            }

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setNome(nomeCliente);
            pessoaVO.setEstadoVO(importador.cidadeVO.getEstado());
            pessoaVO.setCidade(importador.cidadeVO);
            pessoaVO.setCfp(cpf);
            pessoaVO.getTelefoneVOs().add(telefoneVO);
            pessoaVO.getEnderecoVOs().add(enderecoVO);

            VinculoVO vinculoVO = new VinculoVO();
            vinculoVO.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
            vinculoVO.setColaborador(importador.colaboradorVO);

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setSituacao(SituacaoClienteEnum.VISITANTE.getCodigo());
            clienteVO.setPessoa(pessoaVO);
            clienteVO.getEmpresa().setCodigo(1);
            clienteVO.adicionarObjVinculoVOs(vinculoVO);
            clienteVO.setValidarDados(false);

            importador.alunosInseridos.put(nomeCliente, clienteVO);
        } else {
            TelefoneVO telefoneVO = new TelefoneVO();
            telefoneVO.setTipoTelefone(TipoTelefoneEnum.CELULAR.getCodigo());
            String ddd = UteisValidacao.emptyString(LeitorExcel.obterStringDoNumero(linha, 1)) ? LeitorExcel.obterString(linha, 1) : LeitorExcel.obterStringDoNumero(linha, 1);
            String telefone = UteisValidacao.emptyString(LeitorExcel.obterStringDoNumero(linha, 2)) ? LeitorExcel.obterString(linha, 2) : LeitorExcel.obterStringDoNumero(linha, 2);
            telefoneVO.setNumero("(" + ddd + ")" + telefone);

            boolean contem = false;
            for (TelefoneVO telefoneSalvo : importador.alunosInseridos.get(nomeCliente).getPessoa().getTelefoneVOs()) {
                if (telefoneSalvo.getNumero().equals(telefoneVO.getNumero())) {
                    contem = true;
                }
            }

            if (!contem) {
                importador.alunosInseridos.get(nomeCliente).getPessoa().getTelefoneVOs().add(telefoneVO);
            }
        }
    }

    public static void importarClientesTreinoWeb(String caminhoArquivo, Connection con) throws Exception {
        List<HSSFRow> linhas = LeitorExcel.lerLinhas(caminhoArquivo);

        int i = 1;
        int qtdTotal = linhas.size();
        for (HSSFRow linha : linhas) {
            System.out.println("Importando...  Atual: " + i + " Total: " + qtdTotal);
            montarExcelTreino(linha, con);
            i++;
        }
    }

    private static void montarExcelTreino(HSSFRow linha, Connection con) throws Exception {

        String nomeCliente = LeitorExcel.obterString(linha, 0);
        Integer codigoPessoa = 0;
        Integer codigoEmail = 0;
        Integer codigoTelefone1 = 0;
        Integer codigoTelefone2 = 0;
        Integer codigoEmpresa = 1;
        Integer codigoCliente = 0;

        //INSERIR A PESSOA
        String cpf = LeitorExcel.obterString(linha, 1);

        while (cpf.length() < 11) {
            cpf = "0" + cpf;
        }

        cpf = (Formatador.removerMascara(cpf).length() > 11 ? Formatador.formatarString("##.###.###/####-##", Formatador.removerMascara(cpf))
                : Formatador.formatarString("###.###.###-##", Formatador.removerMascara(cpf)));


        String insertPessoa = "INSERT INTO pessoa(nome, cpf) VALUES ('" + nomeCliente.toUpperCase() + "', '" + cpf + "');";
        SuperFacadeJDBC.executarConsultaUpdate(insertPessoa, con);

        ResultSet consultaPessoa = SuperFacadeJDBC.criarConsulta("select max(codigo) as codigoPessoa from pessoa", con);
        while (consultaPessoa.next()) {
            codigoPessoa = consultaPessoa.getInt("codigoPessoa");
        }

        //INSERIR O EMAIL
        String email = LeitorExcel.obterString(linha, 2);

        if (!email.isEmpty()) {
            String insertEmail = "INSERT INTO email(email, pessoa_codigo) VALUES ('" + email + "'," + codigoPessoa + ");";
            SuperFacadeJDBC.executarConsultaUpdate(insertEmail, con);

            ResultSet consultaEmail = SuperFacadeJDBC.criarConsulta("select max(codigo) as codigoEmail from email", con);
            while (consultaEmail.next()) {
                codigoEmail = consultaEmail.getInt("codigoEmail");
            }

//            String insertEmailPessoa = "INSERT INTO pessoa_email(pessoa_codigo, emails_codigo) VALUES ('" + codigoPessoa + "'," + codigoEmail + ");";
//            SuperFacadeJDBC.executarConsultaUpdate(insertEmailPessoa, con);
        }


        //INSERIR O TELEFONE 1
        String telefone1 = LeitorExcel.obterString(linha, 3);
        if (!telefone1.isEmpty()) {
            String ddd = "(11)";
            String numeroTelefone = ddd + telefone1;

            String insertTelefone1 = "INSERT INTO telefone(telefone, pessoa_codigo) VALUES ('" + numeroTelefone + "', " + codigoPessoa + ");";
            SuperFacadeJDBC.executarConsultaUpdate(insertTelefone1, con);

            ResultSet consultaTelefone1 = SuperFacadeJDBC.criarConsulta("select max(codigo) as codigoTelefone1 from telefone", con);
            while (consultaTelefone1.next()) {
                codigoTelefone1 = consultaTelefone1.getInt("codigoTelefone1");
            }

//            String insertTelefone1Pessoa = "INSERT INTO pessoa_telefone(pessoa_codigo, telefones_codigo) VALUES (" + codigoPessoa + " , " + codigoTelefone1 + ");";
//            SuperFacadeJDBC.executarConsultaUpdate(insertTelefone1Pessoa, con);
        }

        //INSERIR O TELEFONE 2
        String telefone2 = LeitorExcel.obterString(linha, 4);
        if (!telefone2.isEmpty()) {
            String ddd = "(11)";
            String numeroTelefone = ddd + telefone2;

            String insertTelefone2 = "INSERT INTO telefone(telefone, pessoa_codigo) VALUES ('" + numeroTelefone + "'," + codigoPessoa + ");";
            SuperFacadeJDBC.executarConsultaUpdate(insertTelefone2, con);

            ResultSet consultaTelefone2 = SuperFacadeJDBC.criarConsulta("select max(codigo) as codigoTelefone2 from telefone", con);
            while (consultaTelefone2.next()) {
                codigoTelefone2 = consultaTelefone2.getInt("codigoTelefone2");
            }

//            String insertTelefone2Pessoa = "INSERT INTO pessoa_telefone(pessoa_codigo, telefones_codigo) VALUES (" + codigoPessoa + "," + codigoTelefone2 + ");";
//            SuperFacadeJDBC.executarConsultaUpdate(insertTelefone2Pessoa, con);
        }

        //INSERIR O CLIENTE SINTÉTICO
        String insertCliente = "INSERT INTO clientesintetico(codigopessoa, empresa, nome, pessoa_codigo) VALUES (" + codigoPessoa + "," + codigoEmpresa + ",'" + nomeCliente + "'," + codigoPessoa + ");";
        SuperFacadeJDBC.executarConsultaUpdate(insertCliente, con);

        ResultSet consultaCliente = SuperFacadeJDBC.criarConsulta("select max(codigo) as codigoCliente from clientesintetico", con);
        while (consultaCliente.next()) {
            codigoCliente = consultaCliente.getInt("codigoCliente");
        }

        String updateCliente = "update clientesintetico set codigocliente = " + codigoCliente + " , matricula = " + codigoCliente + " where codigo = " + codigoCliente + " ;";
        SuperFacadeJDBC.executarConsultaUpdate(updateCliente, con);


        //INSERIR O USUARIO
        String insertUsuario = "INSERT INTO usuario(empresazw, nome, status, tipo, username, cliente_codigo, perfil_codigo) VALUES (" + codigoEmpresa + " , '" + nomeCliente + "' , 2, 0, '" + codigoCliente + "' , " + codigoCliente + " , 1);";
        SuperFacadeJDBC.executarConsultaUpdate(insertUsuario, con);


    }

    public static void main(String[] args) throws SQLException {
        Connection con = DriverManager.getConnection("****************************************", "zillyonweb", "pactodb");
        con.setAutoCommit(true);
        try {
            Conexao.guardarConexaoForJ2SE(con);

            System.out.println("Início em : " + new Date());

            SuperFacadeJDBC.executarConsultaUpdate("ALTER TABLE pessoa ADD COLUMN cpf character varying(255);", con);

            importarClientesTreinoWeb("C:\\Pacto\\novoImport.xls", con);

            SuperFacadeJDBC.executarConsultaUpdate("update clientesintetico    set sexo = 'M';", con);
            SuperFacadeJDBC.executarConsultaUpdate("update clientesintetico cli  set telefones = ARRAY_TO_STRING(ARRAY(select telefone from telefone where pessoa_codigo = cli.pessoa_codigo), ';');", con);
            SuperFacadeJDBC.executarConsultaUpdate("update clientesintetico cli  set email = ARRAY_TO_STRING(ARRAY(select email from email where pessoa_codigo = cli.pessoa_codigo), ';');", con);

            System.out.println("Fim em : " + new Date());

        } catch (Exception e) {
            con.setAutoCommit(true);
            e.printStackTrace();
        } finally {
            con.setAutoCommit(true);
        }
    }



//    public static void main(String[] args) throws SQLException {
//        Connection con = DriverManager.getConnection("*****************************************************", "zillyonweb", "pactodb");
//        con.setAutoCommit(false);
//        try {
//            Conexao.guardarConexaoForJ2SE(con);
//
//            ImportarCadastrosExcel importador = new ImportarCadastrosExcel();
//            importador.alunosInseridos = new HashMap<String, ClienteVO>();
//
//            importador.configuracaoSistemaDAO = new ConfiguracaoSistema(con);
//            importador.cidadeDAO = new Cidade(con);
//
//            importador.clienteDAO = new Cliente(con);
//
//            importador.colaboradorDAO = new Colaborador(con);
//
//            iniciarVariaveisFixas(importador);
//
//            importarClientes("C:\\Users\\<USER>\\Downloads\\Flavio-29145\\Mangabeiras-4680.xls", importador);
////            importarClientes("C:\\Users\\<USER>\\Downloads\\Flavio-29145\\Anchieta -5835.xls", importador);
////            importarClientes("C:\\Users\\<USER>\\Downloads\\Flavio-29145\\Cruzeiro 5000.xls", importador);
////            importarClientes("C:\\Users\\<USER>\\Downloads\\Flavio-29145\\Sion-13630.xls", importador);
//
//            con.commit();
//        } catch (Exception e) {
//            con.setAutoCommit(true);
//            e.printStackTrace();
//        } finally {
//            con.setAutoCommit(true);
//        }
//    }
}

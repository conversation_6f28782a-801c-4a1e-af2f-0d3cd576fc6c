package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "26/11/2024",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-1056")
public class AtualizacaoTicketGC1056 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update configuracaosistemacadastrocliente set nome = 'CNPJ Sesi Indústria' where nome = 'CNPJ Sesi'", c);
        }
    }
}

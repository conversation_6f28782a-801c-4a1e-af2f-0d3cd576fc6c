/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class AplicarMatriculaImportacao {
    
    public static void main(String ... args) throws SQLException {
            Connection con = DriverManager.getConnection("***********************************************************", "postgres", "pactodb");
            executar(con, null);
    }

    public static void processar(Connection con, Integer codigoCliente) {
        List<Integer> codigosClientes = new ArrayList<>();
        codigosClientes.add(codigoCliente);
        executar(con, codigosClientes);
    }

    public static void executar(Connection con, List<Integer> codigosClientes) {
        try {
            String sql = "select codigo,codigomatricula, matricula, matriculaexterna from cliente \n";
            if (!UteisValidacao.emptyList(codigosClientes)) {
                sql += " where codigo in (" + codigosClientes.stream().map(Objects::toString).collect(Collectors.joining(",")) + ")\n";
            }
            sql += " order by matriculaexterna";
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            Map<Integer, Integer> mapaMatriculas = new HashMap<Integer, Integer>();
            List<Integer> matriculasExistentes = new ArrayList<Integer>();
            while (rs.next()) {
                if(rs.getInt("matriculaexterna") != 0){
                    mapaMatriculas.put(rs.getInt("codigo"), rs.getInt("matriculaexterna"));
                }
                matriculasExistentes.add(rs.getInt("codigomatricula"));
            }
            for(Integer codigo : mapaMatriculas.keySet()){
                try {

                    Integer novaMatricula = mapaMatriculas.get(codigo);
                    if(matriculasExistentes.contains(novaMatricula)){
                        ResultSet rsMat = SuperFacadeJDBC.criarConsulta("select coalesce(matricula) as m from numeromatricula", con);
                        if (rsMat.next()) {
                            Integer mat = rsMat.getInt("m")+1;
                            String novaMat = mat.toString();
                            if (novaMat.length() < 6) {
                                for (int i = novaMat.length(); i < 6; i++) {
                                    novaMat = "0" + novaMat;
                                }
                            }
                            SuperFacadeJDBC.executarConsulta("UPDATE cliente SET codigomatricula = "
                                    +mat + ", matricula = '"+novaMat
                                    +"' where codigomatricula = "
                                    +novaMatricula, con);

                            SuperFacadeJDBC.executarConsulta("UPDATE numeromatricula SET matricula = "
                                    +(mat+1), con);


                        }
                    }

                    String novaMat = novaMatricula.toString();
                    if (novaMat.length() < 6) {
                        for (int i = novaMat.length(); i < 6; i++) {
                            novaMat = "0" + novaMat;
                        }
                    }
                    SuperFacadeJDBC.executarConsulta("UPDATE cliente SET codigomatricula = "
                            + novaMatricula + ", matricula = '" + novaMat
                            + "' where codigo = "
                            + codigo, con);

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void executar(Connection con) {
        try {
            ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");

            ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo,codigomatricula, matricula, matriculaexterna from cliente order by matriculaexterna", con);
            Map<Integer, Integer> mapaMatriculas = new HashMap<Integer, Integer>();
            List<Integer> matriculasExistentes = new ArrayList<Integer>();
            while (rs.next()) {
                if(rs.getInt("matriculaexterna") != 0){
                    mapaMatriculas.put(rs.getInt("codigo"), rs.getInt("matriculaexterna"));
                }
                matriculasExistentes.add(rs.getInt("codigomatricula"));
            }
            for(Integer codigo : mapaMatriculas.keySet()){
                if (config.isRodandoAplicarMatriculaImportacao() && config.isRodandoExecutarProcessos()){
                    try {

                        Integer novaMatricula = mapaMatriculas.get(codigo);
                        config.setInformacaoAplicarMatriculaImportacao(codigo+ " == "+novaMatricula);

                        //EXECUÇÃO DE TODOS OS PROCESSOS
                        config.setInformacaoExecutarProcessos(codigo+ " == "+novaMatricula);

                        if(matriculasExistentes.contains(novaMatricula)){
                            ResultSet rsMat = SuperFacadeJDBC.criarConsulta("select coalesce(matricula) as m from numeromatricula", con);
                            if (rsMat.next()) {
                                Integer mat = rsMat.getInt("m")+1;
                                String novaMat = mat.toString();
                                if (novaMat.length() < 6) {
                                    for (int i = novaMat.length(); i < 6; i++) {
                                        novaMat = "0" + novaMat;
                                    }
                                }
                                SuperFacadeJDBC.executarConsulta("UPDATE cliente SET codigomatricula = "
                                        +mat + ", matricula = '"+novaMat
                                        +"' where codigomatricula = "
                                        +novaMatricula, con);

                                SuperFacadeJDBC.executarConsulta("UPDATE numeromatricula SET matricula = "
                                        +(mat+1), con);


                            }
                        }

                        String novaMat = novaMatricula.toString();
                        if (novaMat.length() < 6) {
                            for (int i = novaMat.length(); i < 6; i++) {
                                novaMat = "0" + novaMat;
                            }
                        }
                        SuperFacadeJDBC.executarConsulta("UPDATE cliente SET codigomatricula = "
                                + novaMatricula + ", matricula = '" + novaMat
                                + "' where codigo = "
                                + codigo, con);

                    } catch (Exception e) {
//                    System.out.println("erro codigo = "+codigo);
                        config.setInformacaoAplicarMatriculaImportacao("Falha na execução");

                        //EXECUÇÃO DE TODOS OS PROCESSOS
                        config.setInformacaoExecutarProcessos("Falha na execução");

                        config.setRodandoAplicarMatriculaImportacao(false);
                        e.printStackTrace();
                    }
                }
            }
            config.setInformacaoAplicarMatriculaImportacao("Execução concluída");

            //EXECUÇÃO DE TODOS OS PROCESSOS
            config.setInformacaoExecutarProcessos("Execução concluída");

            config.setRodandoAplicarMatriculaImportacao(false);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "19/07/2024",
        descricao = "Criar config plano: Obrigatoriedade de cadastro de cartão de crédito para venda",
        motivacao = "GC-846")
public class AtualizacaoTicketGC846 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN obrigatorioinformarcartaocreditovenda BOOLEAN DEFAULT FALSE;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

@ClasseProcesso(autor = "Jose Lu<PERSON>",
        data = "25/05/2025",
        descricao = "Aumentando o tamanho da coluna descricao da tabela de produtos",
        motivacao = "Solicitacao GC-2141")
public class AtualizacaoTicketGC2141 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            String sql = " SELECT definition FROM pg_views WHERE viewname = 'dadoscampanhaview';";
            try (Statement stmt = c.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                if (!rs.next()) {
                    SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table produto alter column descricao type varchar(100) using descricao::varchar(100);", c);
                }else{
                    SuperFacadeJDBC.executarUpdateExecutarProcessos("DROP VIEW dadoscampanhaview;", c);
                    SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table produto alter column descricao type varchar(100) using descricao::varchar(100);", c);
                    SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE VIEW dadoscampanhaview as "+rs.getString("definition"), c);
                }
            }
        }
    }
}

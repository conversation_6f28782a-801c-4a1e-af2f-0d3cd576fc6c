package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "20/12/2024",
        descricao = "Processo alterar nome da permissao de 10.05 para 10.07",
        motivacao = "GC-1285 - ajustar permissoes duplicadas")
public class AtualizacaoTicketGC1285 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM perfilacesso WHERE tipo IN (0,1,2,3,4)", c);

            while (rs.next()) {
                int codigoPerfil = rs.getInt("codigo");

                ResultSet rs2 = SuperFacadeJDBC.criarConsulta(
                        "SELECT EXISTS(SELECT 1 FROM permissao " +
                                "WHERE tituloapresentacao = '10.05 - Consultar parcelas de todas as empresas' " +
                                "AND codperfilacesso = " + codigoPerfil + ")", c);

                if (rs2.next() && rs2.getBoolean(1)) {
                    String sql = "UPDATE permissao " +
                            "SET tituloapresentacao = '10.07 - Consultar parcelas de todas as empresas' " +
                            "WHERE tituloapresentacao = '10.05 - Consultar parcelas de todas as empresas' " +
                            "AND codperfilacesso = " + codigoPerfil;
                    SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                }
            }
        }
    }
}

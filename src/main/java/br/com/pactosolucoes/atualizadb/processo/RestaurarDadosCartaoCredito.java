/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import java.util.List;
/**
 *
 * <AUTHOR>
 */
public class RestaurarDadosCartaoCredito {
    
         public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("***********************************************", "postgres", "pactodb");
            corrigirDatasCartao(con1);

        } catch (Exception ex) {
            Logger.getLogger(RestaurarDadosCartaoCredito.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void corrigirDatasCartao(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarEmpresas();
        
        for(EmpresaVO empresa: empresas){
            
            SuperFacadeJDBC.executarConsultaUpdate("delete from caixamovconta where  movconta  in (select codigo from movconta where  lote in (select distinct lt.codigo from lote lt inner join chequecartaolote ccl on ccl.lote = lt.codigo inner join cartaocredito c on ccl.cartao=c.codigo where lt.empresa = "+empresa.getCodigo()+"))", con);
            SuperFacadeJDBC.executarConsultaUpdate("delete from historicocartao  where cartao in(select cc.codigo from cartaocredito cc inner join movpagamento mov on mov.codigo = cc.movpagamento where mov.empresa = "+empresa.getCodigo()+")", con);
            SuperFacadeJDBC.executarConsultaUpdate("delete from lote where  codigo in (select distinct lt.codigo from lote lt inner join chequecartaolote ccl on ccl.lote = lt.codigo inner join cartaocredito c on ccl.cartao=c.codigo where lt.empresa = "+empresa.getCodigo()+")", con);
            ResultSet consulta = SuperFacadeJDBC.criarConsulta("select m.codigo as movpagamento, m.datalancamento, f.diascompensacaocartaocredito, f.compensacaodiasuteis\n" +
"            from movpagamento m inner join formapagamento f on m.formapagamento = f.codigo \n" +
"            where f.tipoformapagamento = 'CA' and m.empresa = "+empresa.getCodigo(), con);
            int cont = 0;

            while (consulta.next()) {
                Date datacompensacao = consulta.getDate("datalancamento");
                 ResultSet consultaParcelasCartao = SuperFacadeJDBC.criarConsulta("select * from cartaocredito where movpagamento = "+consulta.getInt("movpagamento")+"  order by codigo", con);
                while (consultaParcelasCartao.next()) {
                    datacompensacao = Uteis.somarDias(datacompensacao, consulta.getInt("diascompensacaocartaocredito"));
                    Date dataUtil = datacompensacao;
                    if(consulta.getBoolean("compensacaodiasuteis") ){
                          dataUtil = getFacade().getMovPagamento().obterProximoDiaUtil(dataUtil, empresa);
                    } 
                    SuperFacadeJDBC.executarConsultaUpdate("update cartaocredito set dataoriginal = null, datacompesancao = '" + Uteis.getDataJDBC(dataUtil) + "' where codigo ="+ consultaParcelasCartao.getInt("codigo"), con);

                }

                System.out.println(++cont + " - movpagamento " + consulta.getInt("movpagamento") + " foi ajustado");
            }
        }
    }
    
}

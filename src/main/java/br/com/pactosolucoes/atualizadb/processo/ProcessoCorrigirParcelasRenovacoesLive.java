package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ProcessoCorrigirParcelasRenovacoesLive {

    private final Connection conOAMD;
    private final boolean simular;

    public ProcessoCorrigirParcelasRenovacoesLive(boolean simular, String password) throws Exception {
        this.simular = simular;
        this.conOAMD = DriverManager.getConnection(Conexao.getInstance().getUrlOAMD(), Conexao.getInstance().getUsernameBD(), password);
    }

    public static void main(String[] args) throws Exception {

        Integer idRedeEmpresa = 0;
        boolean simular = true;
        String passwordBD = "";
        String[] chaves = new String[]{}; // OPCIONAL
        String justificativa = "Correção valor parcela - renovação automática" + idRedeEmpresa;

        ProcessoCorrigirParcelasRenovacoesLive processo = new ProcessoCorrigirParcelasRenovacoesLive(simular, passwordBD);
        processo.corrigirParcelasRenovacoesTodasUnidadesDaRede(idRedeEmpresa, justificativa, chaves);

    }

    private void corrigirParcelasRenovacoesTodasUnidadesDaRede(Integer idRedeEmpresa, String justificativa, String[] chaves) throws Exception {
        if (UteisValidacao.emptyNumber(idRedeEmpresa)) {
            throw new Exception("O id rede empresa não foi informado!");
        }
        String sqlEmpresas = "select e.* from empresa e \n" +
                " inner join empresafinanceiro ef on ef.chavezw = e.chave \n" +
                "where ef.redeempresa_id = " + idRedeEmpresa + " \n";
        if (!UteisValidacao.emptyArray(chaves)) {
            sqlEmpresas += " and e.chave in ('" +  String.join("','", chaves) + "') \n";
        }

        int count = 0;
        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sqlEmpresas + ") as sql", conOAMD);

        if (total == 0) {
            throw new Exception("Nenhuma empresa encontrada para o redeEmpresaId" + idRedeEmpresa);
        }

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmpresas, conOAMD)) {
            while (rs.next()) {
                String host = rs.getString("hostBD");
                String porta = rs.getString("porta");
                String nomeBD = rs.getString("nomeBD");
                String user = rs.getString("userBD");
                String passWord = rs.getString("passwordBD");

                try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + nomeBD, user, passWord)) {
                    Conexao.guardarConexaoForJ2SE(con);
                    System.out.println((++count) + "\\" + total + " " + con.getCatalog() + " - Executando processo corrigir parcelas renovacoes!");
                    try (ResultSet rsEmpresa = SuperFacadeJDBC.criarConsulta("select codigo from empresa where ativa is true", con)) {
                        while (rsEmpresa.next()) {
                            corrigirParcelasRenovacoes(rsEmpresa.getInt("codigo"), justificativa, con);
                        }
                    }
                }
            }
        }
    }

    private void corrigirParcelasRenovacoes(Integer codigoEmpresa, String justificativa, Connection con) throws Exception {
        MovParcela mparDAO = new MovParcela(con);
        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_EMPRESA_BASICO);

        String sql = getSqlConsultarParcelasAjustar(codigoEmpresa);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", con);
        int atual = 0;

        while(rs.next()) {
            try {
                if (simular) {
                    con.setAutoCommit(false);
                }
                double valorCorreto = rs.getDouble("valor_segunda_parcela_contrato_base_rn");
                MovParcelaVO parcelaVO = mparDAO.consultarPorChavePrimaria(rs.getInt("parcela_codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                Uteis.logarDebug(String.format("%s[%s] %d\\%d - Processando parcela cod: %d %s %s R$ %.2f ===> R$ %.2f ",
                        simular ? "[MODO_SIMULACAO]" : "",
                        con.getCatalog(), ++atual, total, parcelaVO.getCodigo(), parcelaVO.getDescricao(),
                        parcelaVO.getDataVencimento_Apresentar(), parcelaVO.getValorParcela(), valorCorreto));

                renegociarParcela(parcelaVO, valorCorreto, justificativa, usuarioVO, con);

                String updateCR = "UPDATE contratorecorrencia SET valormensal = ?, valormensalnegociado = ? WHERE contrato = ?";
                PreparedStatement psCR = con.prepareStatement(updateCR);
                psCR.setDouble(1, valorCorreto);
                psCR.setDouble(2, valorCorreto);
                psCR.setInt(3, parcelaVO.getContrato().getCodigo());
                psCR.execute();

                if (simular) {
                    con.rollback();
                }
            } catch (Exception e) {
                if (simular && !con.getAutoCommit()) {
                    try {
                        con.rollback();
                    } catch (Exception rollbackEx) {
                        Uteis.logarDebug("Falha ao fazer rollback: " + rollbackEx.getMessage());
                    }
                }
                e.printStackTrace();
                Uteis.logarDebug("Falha ao processar parcela! " + e.getMessage());
            } finally {
                if (simular) {
                    con.setAutoCommit(true);
                }
            }
        }
    }

    private String getSqlConsultarParcelasAjustar(Integer codigoEmpresa) {
        String sql = "SELECT \n" +
                "\tcli.codigomatricula AS matricula,\n" +
                "\tpes.nome,\n" +
                "\tcon.codigo AS contrato_codigo,\n" +
                "\tcon.situacao AS contrato_situacao,\n" +
                "\tpla.descricao AS contrato_plano_descricao,\n" +
                "\tcon.regimerecorrencia contrato_regimerecorrencia,\n" +
                "\ttrunc(mpar1_con_base.valorparcela::NUMERIC,2) AS valor_primeira_parcela_contrato_base_rn,\n" +
                "\ttrunc(mpar2_con_base.valorparcela::NUMERIC,2) AS valor_segunda_parcela_contrato_base_rn,\n" +
                "\tmpar.codigo AS parcela_codigo,\n" +
                "\tmpar.descricao AS parcela_descricao,\n" +
                "\ttrunc(mpar.valorparcela::NUMERIC,2) AS parcela_valor,\n" +
                "\tmpar.situacao AS parcela_situacao,\n" +
                "\tmpar.datavencimento AS parcela_datavencimento\n" +
                "FROM contrato con\n" +
                "\tINNER JOIN plano pla ON pla.codigo = con.plano\n" +
                "\tINNER JOIN movparcela mpar ON mpar.contrato = con.codigo\n" +
                "\tINNER JOIN pessoa pes ON pes.codigo = con.pessoa\n" +
                "\tINNER JOIN cliente cli ON cli.pessoa = con.pessoa\n" +
                "\tINNER JOIN contrato con_base ON con_base.codigo = con.contratobaseadorenovacao\n" +
                "\tINNER JOIN movparcela mpar_1 ON mpar_1.codigo = (SELECT mpar_sub.codigo FROM movparcela mpar_sub \n" +
                "\t\t\t\t\t\t\t\t\t\t\t\t\tWHERE mpar_sub.contrato = con.codigo \n" +
                "\t\t\t\t\t\t\t\t\t\t\t\t\tORDER BY mpar_sub.codigo LIMIT 1)\n" +
                "\tINNER JOIN movparcela mpar1_con_base ON mpar1_con_base.codigo = (SELECT mpar_sub.codigo FROM movparcela mpar_sub \n" +
                "\t\t\t\t\t\t\t\t\t\t\t\t\tWHERE mpar_sub.contrato = con_base.codigo \n" +
                "\t\t\t\t\t\t\t\t\t\t\t\t\tAND mpar_sub.descricao = 'PARCELA 1'\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\t\tORDER BY mpar_sub.codigo LIMIT 1)\n" +
                "\tINNER JOIN movparcela mpar2_con_base ON mpar2_con_base.codigo = (SELECT mpar_sub.codigo FROM movparcela mpar_sub \n" +
                "\t\t\t\t\t\t\t\t\t\t\t\t\tWHERE mpar_sub.contrato = con_base.codigo\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\t\tAND mpar_sub.descricao = 'PARCELA 2'\n" +
                "\t\t\t\t\t\t\t\t\t\t\t\t\tORDER BY mpar_sub.codigo LIMIT 1)\n" +
                "WHERE con.responsavelcontrato = 3\n" +
                "AND mpar.situacao = 'EA'\n" +
                "AND mpar.descricao NOT ILIKE '%RENEGOCIADA%'\n" +
                "AND coalesce(con_base.id_externo, con_base.idexterno) > 0 \n" +
                "AND trunc(mpar_1.valorparcela::NUMERIC, 2) > trunc(mpar1_con_base.valorparcela::NUMERIC, 2)\n" +
                "AND trunc(mpar.valorparcela::NUMERIC, 2) <> trunc(mpar2_con_base.valorparcela::NUMERIC, 2)\n";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql += "AND con.empresa = " + codigoEmpresa + " \n";
        }
        sql += "ORDER BY con.codigo, mpar.codigo \n";
        return sql;
    }

    public void renegociarParcela(MovParcelaVO movParcelaVO, Double novoValorParcela, String justificativa, UsuarioVO usuarioVO, Connection con) throws Exception {
        MovParcela movParcelaDAO = new MovParcela(con);

        Double valorDiferencaRenegociar = novoValorParcela - movParcelaVO.getValorParcela();
        MovParcelaVO parcelaTaxa = new MovParcelaVO();
        parcelaTaxa.setDescricao("");
        parcelaTaxa.setDataVencimento(Calendario.hoje());

        MovParcelaVO parcelaDesconto = new MovParcelaVO();
        parcelaDesconto.setDescricao("");
        parcelaDesconto.setDataVencimento(Calendario.hoje());

        String tipoProdutoExtra;

        String descricaoParcela = "";

        Double valorFinalNovaParcela = movParcelaVO.getValorParcela();
        if (valorDiferencaRenegociar < 0.0) {
            tipoProdutoExtra = "DE";
            descricaoParcela = "DESCONTO";
            parcelaDesconto.setValorParcela(valorDiferencaRenegociar * -1);
            valorFinalNovaParcela -= parcelaDesconto.getValorParcela();
        } else {
            tipoProdutoExtra = "TX";
            descricaoParcela = "ACRESCIMO";
            parcelaTaxa.setValorParcela(valorDiferencaRenegociar);
            valorFinalNovaParcela += parcelaTaxa.getValorParcela();
        }

        List<MovParcelaVO> parcelasRenegociar = new ArrayList<>();
        parcelasRenegociar.add(movParcelaVO);

        // parcela desconto\acrescimo
        MovParcelaVO parcelaRenegociar = new MovParcelaVO();
        parcelaRenegociar.setDescricao(descricaoParcela);
        parcelaRenegociar.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorDiferencaRenegociar));
        parcelaRenegociar.setDataVencimento(Calendario.hoje());
        parcelasRenegociar.add(parcelaRenegociar);

        // Parcelas Renegociadas
        List<MovParcelaVO> parcelasRenegociadas = new ArrayList<>();
        MovParcelaVO novaParcela = (MovParcelaVO) movParcelaVO.getClone(true);
        novaParcela.setDescricao("PARCELA RENEGOCIADA");
        novaParcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorFinalNovaParcela));
        novaParcela.setDataRegistro(Calendario.hoje());
        parcelasRenegociadas.add(novaParcela);

        movParcelaDAO.renegociarParcelas(parcelasRenegociar, parcelasRenegociadas, parcelaDesconto, parcelaTaxa, tipoProdutoExtra, false, null, null, 0.0, false, usuarioVO, true, false, true, justificativa, null);
    }


}

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

public class AjustarConfiguracaoCobranca {

    private Connection con;

    public AjustarConfiguracaoCobranca(Connection con) {
        this.con = con;
    }

    public void ajustarConfiguracaoEmpresa() throws Exception {
        try {
            this.con.setAutoCommit(false);

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("e.codigo, \n");
            sql.append("e.nome, \n");
            sql.append("e.QtdDiasLimiteCobrancaParcelasRecorrencia, \n");
            sql.append("e.QtdDiasRepetirCobrancaParcelasRecorrencia \n");
            sql.append("from empresa e \n");
            sql.append("where e.habilitarreenvioautomaticoremessa \n");
            sql.append("and (e.QtdDiasLimiteCobrancaParcelasRecorrencia > 95 or e.QtdDiasRepetirCobrancaParcelasRecorrencia < 3) \n");

            Statement st = this.con.createStatement();
            ResultSet rs = st.executeQuery(sql.toString());
            while (rs.next()) {

                Integer empresa = rs.getInt("codigo");


                Integer qtdDiasLimiteCobrancaParcelasRecorrencia_ANTIGO = rs.getInt("qtdDiasLimiteCobrancaParcelasRecorrencia");
                if (qtdDiasLimiteCobrancaParcelasRecorrencia_ANTIGO > 95) {
                    Integer qtdDiasLimiteCobrancaParcelasRecorrencia_NOVO = 95;
                    SuperFacadeJDBC.executarUpdate("update empresa set qtdDiasLimiteCobrancaParcelasRecorrencia = " + qtdDiasLimiteCobrancaParcelasRecorrencia_NOVO + " where codigo = " + empresa, this.con);
                    registrarLog("Definir limite de dias para parcela entrar na repescagem", qtdDiasLimiteCobrancaParcelasRecorrencia_ANTIGO.toString(), qtdDiasLimiteCobrancaParcelasRecorrencia_NOVO.toString(), empresa);
                }

                Integer qtdDiasRepetirCobrancaParcelasRecorrencia_ANTIGO = rs.getInt("qtdDiasRepetirCobrancaParcelasRecorrencia");
                if (qtdDiasRepetirCobrancaParcelasRecorrencia_ANTIGO < 3) {
                    Integer qtdDiasRepetirCobrancaParcelasRecorrencia_NOVO = 3;
                    SuperFacadeJDBC.executarUpdate("update empresa set qtdDiasRepetirCobrancaParcelasRecorrencia = " + qtdDiasRepetirCobrancaParcelasRecorrencia_NOVO + " where codigo = " + empresa, this.con);
                    registrarLog("Definir intervalo de dias para repetir parcela não aprovada", qtdDiasRepetirCobrancaParcelasRecorrencia_ANTIGO.toString(), qtdDiasRepetirCobrancaParcelasRecorrencia_NOVO.toString(), empresa);
                }
            }

            SuperFacadeJDBC.executarUpdate("ALTER TABLE empresa DROP COLUMN IF EXISTS usardiasrepetircobrancaparcelasrecorrencia;", this.con);
            SuperFacadeJDBC.executarUpdate("ALTER TABLE empresa DROP COLUMN IF EXISTS usarlimitecobrancaparcelasrecorrencia;", this.con);

            this.con.commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            this.con.rollback();
            throw ex;
        } finally {
            this.con.setAutoCommit(true);
        }
    }

    private void registrarLog(String nomeCampo, String valorCampoAnterior, String valorCampoAlterado, Integer empresa) throws Exception {
        Log logDAO = null;
        try {
            logDAO = new Log(this.con);
            //criar log
            LogVO obj = new LogVO();
            obj.setChavePrimaria(empresa.toString());
            obj.setPessoa(0);
            obj.setNomeEntidade("EMPRESA");
            obj.setNomeEntidadeDescricao("EMPRESA");
            obj.setOperacao("ALTERACAO");
            obj.setResponsavelAlteracao("ADMIN");
            obj.setUserOAMD("AUTOMATICO");
            obj.setDataAlteracao(Calendario.hoje());
            obj.setNomeCampo(nomeCampo);
            obj.setValorCampoAnterior(valorCampoAnterior);
            obj.setValorCampoAlterado(valorCampoAlterado);
            logDAO.incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            logDAO = null;
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Date;

public class ProcessoCorrigirMetasCRMNaoAtingidas {

    public static void main(String... args) {
        try {
            String chave = null;
            Integer codigoEmpresa = 0;
            Date dataInicialProcessarMetas = Calendario.hoje();
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            if (args.length > 1) {
                codigoEmpresa = Integer.parseInt(args[1]);
            }
            if (args.length > 2) {
                dataInicialProcessarMetas = Calendario.getDate("dd/MM/yyyy", args[2]);
            }

            Uteis.debug = true;

            Connection con = new DAO().obterConexaoEspecifica(chave);
            corrigirMetasCRMNaoAtingidas(con, codigoEmpresa, dataInicialProcessarMetas);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static String corrigirMetasCRMNaoAtingidas(Connection con, Integer codigoEmpresa, Date dataInicialProcessarMetas) throws Exception {
        validarDataInicial(dataInicialProcessarMetas);

        int totalSucesso = 0;
        int totalFalha = 0;

        String sql = "select\n" +
                "\ta.codigo as aberturameta,\n" +
                "\ta.dia,\n" +
                "\tu.codigo as usuario,\n" +
                "\tu.colaborador,\n" +
                "\tu.nome as nome_colaborador,\n" +
                "\tf.codigo as fecharmeta,\n" +
                "\tf.identificadormeta,\n" +
                "\tfd.codigo as fecharmetadetalhado,\n" +
                "\tfd.cliente,\n" +
                "\tcli.pessoa,\n" +
                "\tcli.codigomatricula,\n" +
                "\tpes_cli.nome as nome_cliente,\n" +
                "\thc.codigo as historicocontato\n" +
                "from aberturameta a\n" +
                "\t         inner join fecharmeta f on f.aberturameta = a.codigo\n" +
                "\t         inner join fecharmetadetalhado fd on fd.fecharmeta = f.codigo\n" +
                "\t         inner join empresa e on e.codigo = a.empresa\n" +
                "\t         inner join usuario u on u.codigo = a.colaboradorresponsavel \n" +
                "\t         left join cliente cli on cli.codigo = fd.cliente \n" +
                "\t         left join pessoa pes_cli on pes_cli.codigo = cli.pessoa \n" +
                "\t         inner join historicocontato hc on hc.codigo = (select min(hc.codigo) from historicocontato hc where hc.cliente = fd.cliente and hc.contatoavulso is false and hc.dia::date = a.dia::date and hc.fase = f.identificadormeta)\n" +
                "where 1 = 1\n" +
                "and fd.obtevesucesso is false\n" +
                "and coalesce(fd.historicocontato, 0) = 0\n" +
                "and f.identificadormeta in ('" + FasesCRMEnum.ALUNO_GYMPASS.getSigla() + "', '" + FasesCRMEnum.GRUPO_RISCO.getSigla() + "',\n" +
                " '" + FasesCRMEnum.POS_VENDA.getSigla() + "', '" + FasesCRMEnum.FALTOSOS.getSigla() +"', '" + FasesCRMEnum.ANIVERSARIANTES.getSigla() + "')\n" +
                "and hc.codigo is not null\n" +
                "and fd.obtevesucesso is false\n" +
                "and a.dia >= '" + Calendario.getData(dataInicialProcessarMetas, "yyyy-MM-dd") + "' \n";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql += "and a.empresa = " + codigoEmpresa + "\n";
        }
        sql += "order by a.dia, f.identificadormeta, hc.codigo";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", con);
        int count = 0;

        while (rs.next()) {
            try {
                con.setAutoCommit(false);
                Uteis.logar((++count) + "\\" + total + " Processando meta codigo: " + rs.getInt("fecharmetadetalhado") + " dia: " + Calendario.getData(rs.getDate("dia"), "dd/MM/yyyy")
                        + " fase: " + FasesCRMEnum.getFasePorSigla(rs.getString("identificadormeta")).getDescricao()
                        + " responsavel: " + rs.getInt("usuario") + " - " + rs.getString("nome_colaborador")
                        + " cliente MAT: " + rs.getInt("codigomatricula"));

                FecharMetaDetalhado fecharMetaDetalhado = new FecharMetaDetalhado(con);
                fecharMetaDetalhado.baterMetaPorFase(rs.getInt("cliente"), rs.getInt("pessoa"), rs.getDate("dia"), rs.getString("identificadormeta"), rs.getInt("historicocontato"), 0, 0);

                ResultSet rsMetaAtingida = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM fecharmetadetalhado WHERE codigo = " + rs.getInt("fecharmetadetalhado") + " and obtevesucesso is true", con);
                if (!rsMetaAtingida.next()) {
                    throw new Exception("Falha ao ajustar meta!");
                }
                totalSucesso++;
                con.commit();
            } catch (Exception e) {
                totalFalha++;
                con.rollback();
                Uteis.logar(e.getMessage());
            } finally {
                con.setAutoCommit(true);
            }
        }

        Uteis.logar("TOTAL SUCESSO: " + totalSucesso);
        Uteis.logar("TOTAL FALHA: " + totalFalha);
        return "TOTAL PROCESSADO: " + total + " | SUCESSO: " + totalSucesso + " | FALHA: " + totalFalha;
    }

    private static void validarDataInicial(Date dataInicialProcessarMetas) throws Exception {
        if (dataInicialProcessarMetas == null) {
            throw new Exception("Data inicial não informada!");
        }
        if (Calendario.maior(dataInicialProcessarMetas, Calendario.hoje())) {
            throw new Exception("A data não pode ser maior que hoje! (" + Calendario.getData("dd/MM/yyyy") + ")");
        }
    }

}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "20/08/2024",
        descricao = "Cria coluna 'capacidade limite' na tabela 'localacesso'",
        motivacao = "GC-905 Criar limitador para quantidade de alunos por local de acesso - Pratique")
public class CriarColunaCapacidadeLimiteTabelaLocalAcesso implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE localacesso ADD COLUMN capacidadelimite INT DEFAULT 0;", c);
        }
    }
}

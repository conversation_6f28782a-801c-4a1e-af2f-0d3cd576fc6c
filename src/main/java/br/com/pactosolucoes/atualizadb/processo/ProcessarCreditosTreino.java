/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;


public class ProcessarCreditosTreino {



    public static void main(String... args) throws Exception {
        String urlBDHoje = "********************************************************";
        Connection con = conexao(urlBDHoje);
        JSONArray validated = validate(con, false);
        System.out.println(validated);
    }

    public static JSONArray validate(Connection con, boolean debitar) throws Exception {
        JSONArray invalidos = new JSONArray();
        ResultSet rs = obterContratosAtivosCredito(con);
        while(rs.next()){
            String nomeCliente = "";
            int cliente = 0;
            ResultSet rsSintetico = con.prepareStatement("select nomecliente, codigocontrato, saldocreditotreino, codigocliente " +
                    " from situacaoclientesinteticodw s where codigopessoa = " + rs.getString("pessoa")).executeQuery();
            int saldoContrato = rs.getInt("quantidadecreditodisponivel");
            while(rsSintetico.next()){
                nomeCliente = rsSintetico.getString("nomecliente");
                cliente = rsSintetico.getInt("codigocliente");

            }
            int contrato = rs.getInt("codigo");
            ResultSet saldoCompra = con.prepareStatement("select sum(quantidade) as saldo from controlecreditotreino c\n" +
                    " where contrato = " + contrato +
                    " and quantidade > 0").executeQuery();
            int saldoCompradosAjustados = 0;
            if(saldoCompra.next()){
                saldoCompradosAjustados = saldoCompra.getInt("saldo");
            }

            ResultSet saldoRemovidoRs = con.prepareStatement("select sum(quantidade) as saldo from controlecreditotreino c\n" +
                    " where contrato = " + contrato +
                    " and quantidade < 0").executeQuery();
            int saldoRemovido = 0;
            if(saldoRemovidoRs.next()){
                saldoRemovido = saldoRemovidoRs.getInt("saldo");
            }

            ResultSet aulasMarcadasRs = con.prepareStatement("select count(c.codigo) as saldo from controlecreditotreino c\n" +
                    " inner join reposicao r on r.codigo = c.reposicao  " +
                    " where c.contrato = " + contrato +
                    " and r.datareposicao < current_date ").executeQuery();
            int reposicoes = 0;
            if(aulasMarcadasRs.next()){
                reposicoes = aulasMarcadasRs.getInt("saldo");
            }
            int saldoReal = (saldoCompradosAjustados - saldoRemovido - (reposicoes  - saldoRemovido));
            if(saldoReal < saldoContrato ){
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("cliente",  nomeCliente);
                jsonObject.put("contrato",  contrato);
                jsonObject.put("saldo contrato", saldoContrato);
                jsonObject.put("saldo real", saldoReal);
                if(debitar){
                    debitarCreditoPresenca(con, saldoContrato - saldoReal, contrato, cliente);
                }
                invalidos.put(jsonObject);
            }

        }
        return invalidos;

    }

    public static Connection conexao(String urlBD) {
        try {
            return DriverManager.getConnection(urlBD, "postgres", "pactodb");
        } catch (Exception e) {
            return null;
        }
    }


    private static ResultSet obterContratosAtivosCredito(Connection con) throws Exception {
        String sql = "select cdt.quantidadecreditodisponivel, c.codigo, c.pessoa from contrato c " +
                " inner join contratoduracao cd on cd.contrato = c.codigo" +
                " inner join contratoduracaocreditotreino cdt on cd.codigo = cdt.contratoduracao" +
                " where c.situacao = 'AT' and c.vendaCreditoTreino = true and cdt.tipohorario = 2 ";
        return con.prepareStatement(sql).executeQuery();
    }

    public static void debitarCreditoPresenca(Connection con, Integer quantidade, Integer contrato,
                                              Integer cliente) throws Exception{
        ControleCreditoTreino controleCreditoTreino = new ControleCreditoTreino(con);
        ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
        controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO);
        controleCreditoTreinoVO.setQuantidade(-1 * quantidade);
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(contrato);
        controleCreditoTreinoVO.setContratoVO(contratoVO);
        UsuarioVO usuarioVO = new UsuarioVO();
        usuarioVO.setCodigo(3);
        controleCreditoTreinoVO.setUsuarioVO(usuarioVO);
        controleCreditoTreino.incluirSemCommit(controleCreditoTreinoVO,
                cliente, null, null);
    }

}

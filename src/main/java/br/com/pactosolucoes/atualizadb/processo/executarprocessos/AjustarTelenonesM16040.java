package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Sistema",
        data = "08/07/2025",
        descricao = "Ajustar números de telefone removendo hífens e espaços",
        motivacao = "M1-6040")
public class AjustarTelenonesM16040 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "UPDATE telefone SET numero = replace(replace(numero, '-', ''), ' ', '') WHERE numero LIKE '%-%' OR numero LIKE '% %'",
                    c
            );
        }
    }
}

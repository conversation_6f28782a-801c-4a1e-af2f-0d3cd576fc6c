package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "20/06/2024",
        descricao = "Apagar registro de mensagem de atestado de aptidão física, mesmo quando existe um atestado vigente",
        motivacao = "M1-3310")
public class AtualizacaoTicketM13310 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT 'DELETE FROM clientemensagem WHERE codigo = ' || cms.codigo || ';' as sqlDelete FROM clientemensagem cms\n");
            sql.append("INNER JOIN cliente cli ON cli.codigo = cms.cliente\n");
            sql.append("INNER JOIN pessoa pes ON pes.codigo = cli.pessoa\n");
            sql.append("WHERE cms.produto IN (SELECT codigo FROM produto WHERE tipoproduto = 'AT')\n");
            sql.append("AND cms.tipomensagem = 'PV'\n");
            sql.append("AND EXISTS (SELECT * FROM atestado atd\n");
            sql.append("\tINNER JOIN movproduto mpd ON mpd.codigo = atd.movproduto\n");
            sql.append("\tWHERE NOW() BETWEEN mpd.datainiciovigencia AND mpd.datafinalvigencia\n");
            sql.append("\tAND mpd.pessoa = pes.codigo)\n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), c);
            while (rs.next()) {
                String sqlUpdate = rs.getString("sqlDelete");
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlUpdate, c);
            }
        }
    }

}

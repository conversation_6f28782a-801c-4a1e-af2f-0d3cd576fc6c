package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "22/11/2024",
        descricao = "Alterando situação de planoProdutoSugerido para desativado, quando o produto é desativado",
        motivacao = "M1-3474")
public class AtualizacaoTicketM13474 implements MigracaoVersaoInterface{
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            StringBuilder sql = new StringBuilder();
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE planoprodutosugerido SET ativoplano = FALSE, obrigatorio = FALSE WHERE produto IN (SELECT prd.codigo FROM produto prd WHERE prd.desativado)", c);
        }
    }
}

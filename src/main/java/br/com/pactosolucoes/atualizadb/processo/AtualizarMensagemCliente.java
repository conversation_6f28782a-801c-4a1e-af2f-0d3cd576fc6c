package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.List;

/**
 * Created by <PERSON> on 07/06/2016.
 */
public class AtualizarMensagemCliente {
    public static void main(String[] args) {
        try {
            Connection con = new DAO().obterConexaoEspecifica("corposaude");
            Conexao.guardarConexaoForJ2SE(con);
            atualizarMensagens(con);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public static void atualizarMensagens(Connection con){
        try {
            List<EmpresaVO> empresas = new Empresa(con).consultarEmpresas();
            RoboVO robo = new RoboVO();
            robo.setDia(Calendario.hoje());
            robo.setListaEmpresa(empresas);
            robo.processarClientesComParcelaVencida();
        }catch (Exception ex){

        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "25/09/2024",
        descricao = "Tabela para gravar fila de espera de turmas nao coletivas",
        motivacao = "GC-735")
public class AtualizacaoTicketGC735 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "create table filaesperaturmacrm (codigo serial not null constraint filaesperaturmacrm_pk primary key,\n" +
                    "cliente integer constraint filaesperaturmacrm_cliente_codigo_fk references cliente,\n" +
                    "passivo integer constraint filaesperaturmacrm_passivo_codigo_fk references passivo,\n" +
                    "horarioturma integer constraint filaesperaturmacrm_horarioturma_codigo_fk references horarioturma,\n" +
                    "dataregistro timestamp,\n" +
                    "dataentrada timestamp,\n" +
                    "ordem integer);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table fecharmetadetalhado add column observacaoFilaEsperaTurmaCrm varchar", c);
        }
    }
}

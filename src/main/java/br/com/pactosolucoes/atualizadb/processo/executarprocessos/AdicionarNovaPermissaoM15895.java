package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "<PERSON>",
        data = "10/06/2025",
        descricao = "Adicionar a nova permissão em todos os perfils",
        motivacao = "M1-5895")
public class AdicionarNovaPermissaoM15895 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM perfilacesso", c);

            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (2, '3.43 - NFSe Recibo - Autorizar','(0)(1)(2)(3)(9)(12)', 'ApresentarNfseRecibo', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        }
    }

}


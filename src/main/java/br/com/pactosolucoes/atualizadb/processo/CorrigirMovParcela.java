package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.ConfiguracaoSistemaControle;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

public class CorrigirMovParcela extends SuperEntidade {

    public CorrigirMovParcela() throws Exception {
        super();
    }

    public static void main(String[] args) {
        try {
            Connection con = new DAO().obterConexaoEspecifica(args[0]);
            Conexao.guardarConexaoForJ2SE(con);
            CorrigirMovParcela p = new CorrigirMovParcela();
            p.setCon(con);
            CorrigirMovParcela.corrigirParcelasContrato(con, true);
            Uteis.logar(null, "Corrigir parcelas em aberto...");
            p.corrigirParcelasEA();
            Uteis.logar(null, "Concluido!");
        } catch (Exception ex) {
            Logger.getLogger(CorrigirMovParcela.class.getName()).log(
                    Level.SEVERE, null, ex);
        }


    }

    public String executarProcesso(boolean controleTransacao ) throws Exception {
        String erros = corrigirParcelasContrato(con, controleTransacao);
        corrigirParcelasEA();
        ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
        if(config != null){
            config.setTotalProcessadosParcelas(100);
            config.setTotalAProcessarParcelas(100);
        }
        return erros;

    }

    private void corrigirParcelasEA() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" UPDATE movproduto SET situacao = 'PG' where codigo in ( ");
        sql.append(" SELECT mpd.codigo FROM movproduto mpd ");
        sql.append(" INNER JOIN movprodutoparcela mpp ON mpp.movproduto = mpd.codigo ");
        sql.append(" INNER JOIN movparcela mp ON mp.codigo = mpp.movparcela ");
        sql.append(" WHERE mp.situacao like 'EA' ");
        sql.append(" and (SELECT sum(valorpago) FROM pagamentomovparcela where movparcela = mp.codigo and recibopagamento is not null ) = mp.valorparcela )");

        executarConsulta(sql.toString(), con);

        sql = new StringBuilder();
        sql.append(" UPDATE movparcela mp SET situacao = 'PG' ");
        sql.append(" WHERE mp.situacao like 'EA' ");
        sql.append(" and (SELECT sum(valorpago) FROM pagamentomovparcela where movparcela = mp.codigo and recibopagamento is not null ) = mp.valorparcela");
        executarConsulta(sql.toString(), con);
    }

    /**
     * Responsável por selecionar os contratos que possuem movprodutos sem relacionamento com movparcelas
     * <AUTHOR>
     * 23/07/2011
     * @throws Exception
     */
    public static String corrigirParcelasContrato(Connection c, boolean controleTransacao) throws Exception {
        StringBuilder msgErros = new StringBuilder();
        try {
            StringBuilder sql = new StringBuilder();
            //obter todos os codigos de contratos que possuem movparcelas sem relacionamento com movprodutos
            sql.append(" SELECT DISTINCT(contrato) FROM movparcela mp left join movprodutoparcela mpp on mpp.movparcela = mp.codigo \n");
            sql.append(" WHERE mpp.codigo is null and mp.situacao <> 'RG' and mp.valorparcela > 0 and mp.contrato is not null ");
            sql.append(" AND descricao LIKE 'PARCELA%' order by contrato");
            setarTotalAProcessar(sql.toString(), c);

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), c);
            ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
            while (rs.next()) {
                try{
                    int contrato = rs.getInt("contrato");
                    arranjarParcelas(rs.getInt("contrato"), c, controleTransacao);
                    if (config != null) {
                        config.setTotalProcessadosParcelas(config.getTotalProcessadosParcelas() + 1);
                    }
                    Uteis.logar(null, "Corrigido parcelas do contrato -> " + contrato);
                } catch (Exception e){
                    msgErros.append(e.getMessage()).append("\n");
                }
            }
            Uteis.logar(null, "Setando recibos... ");
            sql = new StringBuilder();
            //setar recibo onde não foi setado
            sql.append("UPDATE movprodutoparcela mpp SET recibopagamento = "
                    + "(SELECT MAX(recibopagamento) FROM pagamentomovparcela pmp WHERE pmp.movparcela = mpp.movparcela) "
                    + "WHERE mpp.recibopagamento IS NULL AND (SELECT COUNT(recibopagamento) FROM pagamentomovparcela pmp WHERE pmp.movparcela = mpp.movparcela)> 0");
            executarConsulta(sql.toString(), c);
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return msgErros.toString();
    }

    public static void arranjarParcelas(Integer codigoContrato, Connection c, boolean controleTransacao) throws Exception {
        try {
            ZillyonWebFacade zwFacade = new ZillyonWebFacade(c);
            ResultSet set = SuperFacadeJDBC.criarConsulta("SELECT * FROM movproduto movpro inner join produto pro on pro.codigo =movpro.produto WHERE movpro.contrato = " + codigoContrato + " AND pro.tipoproduto = 'RD'", c);
            if(set.next()){
                throw new Exception("Contrato "+codigoContrato +"  teve devolução de cheques. O processo automático não consegue resolver essa situação");
            }
            if(controleTransacao)
                c.setAutoCommit(false);
            ContratoVO contrato = zwFacade.getContrato().consultarPorChavePrimaria(codigoContrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            //obter parcelas originais do contrato
            set = SuperFacadeJDBC.criarConsulta("SELECT * FROM movparcela WHERE contrato = " + codigoContrato + " AND descricao LIKE 'PARCELA%' and situacao <> 'RG' ORDER BY MovParcela.datavencimento", c);
            contrato.setMovParcelaVOs(MovParcela.montarDadosConsulta(set, Uteis.NIVELMONTARDADOS_DADOSBASICOS, c));
            set = SuperFacadeJDBC.criarConsulta("SELECT * FROM movparcela WHERE contrato = " + codigoContrato + " AND descricao LIKE 'ADESÃO PARCELA%' ORDER BY MovParcela.datavencimento", c);
            //parcelas que cobram a adesão separada
            List<MovParcelaVO> parcelasAdesao = MovParcela.montarDadosConsulta(set, Uteis.NIVELMONTARDADOS_DADOSBASICOS, c);
            set = SuperFacadeJDBC.criarConsulta("SELECT * FROM movparcela WHERE contrato = " + codigoContrato + " AND descricao LIKE 'ANUIDADE PLANO%' and dataregistro ='"+Uteis.getDataJDBC(contrato.getDataLancamento())+"' ORDER BY MovParcela.datavencimento", c);
            MovParcelaVO parcelaAnuidade = null;
            if(set.next()){
                parcelaAnuidade = MovParcela.montarDados(set, Uteis.NIVELMONTARDADOS_DADOSBASICOS, c);
            }
            contrato.getMovParcelaVOs().addAll(parcelasAdesao);
            MovProdutoVO produtoAdesao = new MovProdutoVO();
            //obter produtos originais do contrato
            set = SuperFacadeJDBC.criarConsulta("SELECT mp.* FROM movproduto mp, produto p WHERE p.codigo = mp.produto AND mp.contrato =  " + codigoContrato + " AND p.tipoproduto IN ('PM', 'MA','RE', 'RN', 'PE', 'SE','TD'"+(parcelaAnuidade == null ? ",'TA'" : "") +") ORDER BY codigo", c);
            List<MovProdutoVO> listMovProdutos = MovProduto.montarDadosConsulta(set, Uteis.NIVELMONTARDADOS_VENDA, c);
            for (MovProdutoVO prod : listMovProdutos) {
                prod.setMovProdutoParcelaVOs(new ArrayList<MovProdutoVO>());
                prod.setQuitado(false);
                if(prod.getProduto().getTipoProduto().equals("TD")){
                    produtoAdesao = prod;
                }
            }
            contrato.setMovProdutoVOs(listMovProdutos);


            ResultSet resultSet = criarConsulta("SELECT cp.nrparcelas FROM condicaopagamento cp, contratocondicaopagamento ccp "
                    + "WHERE cp.codigo = ccp.condicaopagamento AND ccp.contrato = " + contrato.getCodigo(), c);
            resultSet.next();
            boolean produto = true;
            for (Object obj : contrato.getMovParcelaVOs()) {
                 MovParcelaVO parcela = (MovParcelaVO) obj;
                //é necessário deletar os relacionamentos que existem, visto que usarei o mesmo processo que é usado na criação
                //do contrato, portanto pode haver duplicidade
                SuperFacadeJDBC.executarConsulta("DELETE FROM movprodutoparcela WHERE movparcela = "+parcela.getCodigo(), c);


                ResultSet resultRecibo = criarConsulta("select recibopagamento from pagamentomovparcela where movparcela = "
                        + parcela.getCodigo(), c);


                if (resultRecibo.next()) {
                    parcela.getReciboPagamento().setCodigo(resultRecibo.getInt("recibopagamento"));
                }
                parcela.setMovProdutoParcelaVOs(new ArrayList());
                if(parcela.getDescricao().startsWith("PARCELA")){
                    parcela.setValorBaseCalculo(parcela.getValorParcela());
        //            Ordenacao.ordenarLista(produtos, "mesReferencia");
                    zwFacade.dividirProdutosNasParcelas(parcela, listMovProdutos, parcela.getReciboPagamento(), resultSet.getInt("nrparcelas"), produto, contrato.getDividirProdutosNasParcelas(), parcela.getDescricao().equals("PARCELA 1"), !parcelasAdesao.isEmpty());
                    zwFacade.getMovProdutoParcela().incluirMovProdutoParcelas(parcela.getCodigo(), parcela.getMovProdutoParcelaVOs());

                    if (!contrato.getDividirProdutosNasParcelas()) {
                        produto = false;
                    }
                } else {//parcelas  de adesao
                    MovProdutoParcelaVO  novo = new MovProdutoParcelaVO();
                    novo.setValorPago(parcela.getValorParcela());
                    novo.setMovParcela(parcela.getCodigo());
                    novo.setMovProduto(produtoAdesao.getCodigo());
                    novo.setReciboPagamento(parcela.getReciboPagamento());
                    zwFacade.getMovProdutoParcela().incluir(novo);
                }
            }
            if(controleTransacao) {
                c.commit();
            }
        }catch(Exception e){
            if(controleTransacao && !c.getAutoCommit()) {
                c.rollback();
            }
            throw e;
        } finally{
            if(controleTransacao) {
                c.setAutoCommit(true);
            }
        }
                
    }

    /**
     * <AUTHOR>
     */
    public static void setarTotalAProcessar(String sql,Connection c) throws Exception {
        ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
        if (config != null) {
            ResultSet rs = SuperFacadeJDBC.criarConsultaRolavel(sql, c);
            if (rs.next()) {
                rs.last();
                config.setTotalAProcessarParcelas(config.getTotalAProcessarParcelas() + rs.getRow());
            } else {
                config.setTotalAProcessarParcelas(config.getTotalAProcessarParcelas() + 0);
            }
        }
    }
}

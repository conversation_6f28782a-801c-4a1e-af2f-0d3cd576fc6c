package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "28/07/2025",
        descricao = "Cria colunas para enviar eventos - RD Station - Marketing",
        motivacao = "Cria colunas para enviar eventos - RD Station - Marketing")
public class CriarColunasNovosEventosRDStation implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        String sql = "ALTER TABLE configuracaoempresardstation \n" +
                "ADD COLUMN nomeEventoNovoCliente VARCHAR(80),\n" +
                "ADD COLUMN nomeEventoNovoContrato VARCHAR(80),\n" +
                "ADD COLUMN nomeEventoUpgradePlano VARCHAR(80),\n" +
                "ADD COLUMN nomeEventoDowngradePlano VARCHAR(80);";

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}

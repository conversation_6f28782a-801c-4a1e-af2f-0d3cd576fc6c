package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Joao Alcides",
        data = "26/06/2024",
        descricao = "Cria coluna classe na tabela bdatualizacao",
        motivacao = "Mudar a forma com que o sistema atualiza os bancos de dados")
public class CriarColunaClasseBDAtualizacao implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table bdatualizacao add column classe boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX bdatualizacao_script_idx ON bdatualizacao USING btree (script);", c);
        }
    }
}

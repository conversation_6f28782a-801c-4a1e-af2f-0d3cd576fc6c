package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ExcluirClientesDuplicados extends SuperEntidade {

    public ExcluirClientesDuplicados() throws Exception {
        super();
    }

    public ExcluirClientesDuplicados(Connection con1) throws Exception {
        con = con1;
    }

    public static void main(String[] args) {
        try {
            String key = "bdzillyonupper-2017-01-17";
            Connection con = new DAO().obterConexaoEspecifica(key);
            Conexao.guardarConexaoForJ2SE(key, con);
            ExcluirClientesDuplicados ajustar = new ExcluirClientesDuplicados(con);
            ajustar.processar();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<ObjetoGenerico> obterClientesDuplicados() throws Exception {
        List<ObjetoGenerico> clientesDuplicados = new ArrayList<ObjetoGenerico>();
        StringBuilder sqlDuplicados = new StringBuilder();
        sqlDuplicados.append("SELECT\n");
        sqlDuplicados.append("  pes.nome,\n");
        sqlDuplicados.append("  MAX(pes.codigo) AS cod_pessoa,\n");
        sqlDuplicados.append("  count(*) AS count\n");
        sqlDuplicados.append("FROM pessoa pes \n");
        sqlDuplicados.append("INNER JOIN cliente cli ON pes.codigo = cli.pessoa\n");
        sqlDuplicados.append("GROUP BY 1\n");
        sqlDuplicados.append("HAVING count(*) > 1");
        Statement stm = con.createStatement();
        ResultSet resultSet = stm.executeQuery(sqlDuplicados.toString());
        while (resultSet.next()) {
            String nome = resultSet.getString("nome");
            String duplicacoes = resultSet.getString("count");
            ObjetoGenerico objetoGenerico = new ObjetoGenerico(nome, duplicacoes);
            clientesDuplicados.add(objetoGenerico);
        }
        return clientesDuplicados;
    }

    public String processar() throws Exception {
        StringBuilder retorno = new StringBuilder();

        StringBuilder sqlDuplicados = new StringBuilder();
        sqlDuplicados.append("SELECT\n");
        sqlDuplicados.append("  pes.nome,\n");
        sqlDuplicados.append("  MAX(pes.codigo) AS cod_pessoa,\n");
        sqlDuplicados.append("  count(*) AS count\n");
        sqlDuplicados.append("FROM pessoa pes \n");
        sqlDuplicados.append("INNER JOIN cliente cli ON pes.codigo = cli.pessoa\n");
        sqlDuplicados.append("GROUP BY 1\n");
        sqlDuplicados.append("HAVING count(*) > 1");

        Statement stm = con.createStatement();
        ResultSet resultSet = stm.executeQuery(sqlDuplicados.toString());
        while (resultSet.next()) {
            String nomeDuplicado = resultSet.getString("nome");
            int qtdDuplicacoes = resultSet.getInt("count");

            List<Integer> codPessoasDeletar = new ArrayList<Integer>();
            List<Integer> codClienteDeletar = new ArrayList<Integer>();

            StringBuilder sqlCodigosDeletar = new StringBuilder();
            sqlCodigosDeletar.append("SELECT \n");
            sqlCodigosDeletar.append("   pes.codigo as pessoa, \n");
            sqlCodigosDeletar.append("   cli.codigo as cliente, \n");
            sqlCodigosDeletar.append("   cli.situacao,\n");
            sqlCodigosDeletar.append("   cli.matricula,\n");
            sqlCodigosDeletar.append("   pes.datacadastro, \n");
            sqlCodigosDeletar.append("   (SELECT count(codigo) FROM recibopagamento rp WHERE rp.pessoapagador = pes.codigo) as recibos,\n");
            sqlCodigosDeletar.append("   (SELECT count(codigo) FROM contrato con WHERE con.pessoa = pes.codigo) as contratos\n");
            sqlCodigosDeletar.append("FROM pessoa pes \n");
            sqlCodigosDeletar.append("LEFT JOIN cliente cli ON pes.codigo = cli.pessoa\n");
            sqlCodigosDeletar.append("WHERE pes.nome = '").append(nomeDuplicado.replace("'", "''")).append("'\n");
            sqlCodigosDeletar.append("ORDER BY datacadastro DESC, cli.codigo DESC;");

            System.out.println("Ajustando: " + nomeDuplicado + " (Qtd: " + qtdDuplicacoes + ")");
            retorno.append("Ajustando: <b>").append(nomeDuplicado).append("</b> (Qtd: ").append(qtdDuplicacoes).append(")<br/>");

            stm = con.createStatement();
            ResultSet resultSetCodigosDeletar = stm.executeQuery(sqlCodigosDeletar.toString());
            Integer codPessoaNaoExcluir = null;
            while (resultSetCodigosDeletar.next()) {
                Integer codPessoa = resultSetCodigosDeletar.getInt("pessoa");
                Integer codCliente = resultSetCodigosDeletar.getInt("cliente");
                String situacao = resultSetCodigosDeletar.getString("situacao");
                String matricula = resultSetCodigosDeletar.getString("matricula");
                Long qtdRecibos = resultSetCodigosDeletar.getLong("recibos");
                Long qtdContratos = resultSetCodigosDeletar.getLong("contratos");

                if ("VI".equals(situacao) && (codPessoasDeletar.size() < qtdDuplicacoes - 1) && (qtdRecibos == 0)) {
                    codPessoasDeletar.add(codPessoa);
                    codClienteDeletar.add(codCliente);
                } else {
                    if (codPessoasDeletar.size() == (qtdDuplicacoes - 1)) {
                        System.out.println("[Mais antigo possível] Cód Cliente: " + codCliente + "(" + matricula + ") - Situação: " + situacao);
                        retorno.append("[Mais antigo possível] Cód Cliente: ").append(codCliente).append("(").append(matricula).append(") - Situação: ").append(situacao).append("<br/>");
                    } else {
                        System.out.println("[Não Excluído] Cód Cliente: " + codCliente + "(" + matricula + ") - Situação: " + situacao);
                        retorno.append("[Não Excluído] Cód Cliente: ").append(codCliente).append("(").append(matricula).append(") - Situação: ").append(situacao).append("<br/>");
                    }
                    if (codPessoaNaoExcluir == null || "AT".equals(situacao)) {
                        codPessoaNaoExcluir = codPessoa;
                    }
                }
            }

            if (codClienteDeletar.size() > 0 || codPessoasDeletar.size() > 0) {
                try {
                    Statement stmExclusao = con.createStatement();
                    con.setAutoCommit(false);
                    stmExclusao.execute("DELETE FROM situacaoclientesinteticodw WHERE codigopessoa IN (" + obterCodigos(codPessoasDeletar) + ");");
                    if (codClienteDeletar.size() > 0) {
                        stmExclusao.execute("DELETE FROM fecharmetadetalhado WHERE cliente IN (" + obterCodigos(codClienteDeletar) + ");");
                        stmExclusao.execute("DELETE FROM optin WHERE cliente IN (" + obterCodigos(codClienteDeletar) + ");");
                        stmExclusao.execute("DELETE FROM clientemensagem WHERE cliente IN (" + obterCodigos(codClienteDeletar) + ");");
                        stmExclusao.execute("DELETE FROM questionariocliente WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM maladiretaenviada WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM maladiretacrmextracliente WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM fecharmetadetalhado WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM historicoContato WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM agenda WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM reposicao WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM situacaocontratoanaliticodw WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM risco WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM aulaavulsadiaria WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM itemtaxapersonal WHERE aluno IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM vinculo WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM alunohorarioturma WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM historicopontos WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM aulaconfirmada WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM demandahorarioturma WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM orcamento WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM malingenviados WHERE cliente IN ( " + obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM lead WHERE cliente  IN ( "+ obterCodigos(codClienteDeletar)+ ");");
                        stmExclusao.execute("DELETE FROM cliente WHERE codigo IN (" + obterCodigos(codClienteDeletar) + ");");
                    }
                    if (codPessoaNaoExcluir != null) {
                        stmExclusao.execute("UPDATE cliente SET pessoaresponsavel = " + codPessoaNaoExcluir + " WHERE pessoaresponsavel IN (" + obterCodigos(codPessoasDeletar) + ");");
                    }
                    stmExclusao.execute("DELETE FROM pessoa WHERE codigo IN (" + obterCodigos(codPessoasDeletar) + ");");
                    con.commit();
                } catch (Exception ex) {
                    System.out.println(ex.getMessage());
                    retorno.append(ex.getMessage()).append("<br/>");
                    con.rollback();
                } finally {
                    con.setAutoCommit(true);
                }
            }
        }
        return retorno.toString();
    }

    public String obterCodigos(List obj) {
        return Arrays.toString(obj.toArray()).replace("[", "").replace("]", "");
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

/**
 *
 * <AUTHOR>
 */
public class FuncoesControleEstoque {

    public static String retornarSQLTabelas() {
        StringBuilder sql = new StringBuilder();
        // Criar tabela de Compra.
        sql.append("CREATE TABLE Compra( \n");
        sql.append("codigo serial not null, \n");
        sql.append("empresa integer not null,  \n");
        sql.append("fornecedor integer not null, \n");
        sql.append("cancelada boolean default false, \n");
        sql.append("numeroNF character varying(50), \n");
        sql.append("contato character varying (100), \n");
        sql.append("telefoneContato character varying(15), \n");
        sql.append("dataEmissao timestamp without time zone, \n");
        sql.append("dataCadastro timestamp without time zone, \n");
        sql.append("usuarioCadastro integer not null, \n");
        sql.append("dataCancelamento timestamp without time zone, \n");
        sql.append("usuarioCancelamento integer, \n");
        sql.append("valorTotal real, \n");
        sql.append("observacoes character varying(2000), \n");
        sql.append("  CONSTRAINT compra_pkey PRIMARY KEY (codigo), \n");
        sql.append("  CONSTRAINT fk_Compra_empresa FOREIGN KEY (empresa) \n");
        sql.append("REFERENCES empresa (codigo) MATCH SIMPLE \n");
        sql.append("ON UPDATE RESTRICT ON DELETE RESTRICT, \n");
        sql.append("CONSTRAINT fk_CompraUsuCad_usuario FOREIGN KEY (usuarioCadastro) \n");
        sql.append("REFERENCES usuario (codigo) MATCH SIMPLE \n");
        sql.append("ON UPDATE RESTRICT ON DELETE RESTRICT,   \n");
        sql.append("CONSTRAINT fk_CompraUsuAlt_usuario FOREIGN KEY (usuarioCancelamento) \n");
        sql.append("REFERENCES usuario (codigo) MATCH SIMPLE \n");
        sql.append("ON UPDATE RESTRICT ON DELETE RESTRICT,   \n");

        sql.append("   CONSTRAINT fk_Compra_fornecedor FOREIGN KEY (fornecedor) \n");
        sql.append("REFERENCES fornecedor (codigo) MATCH SIMPLE \n");
        sql.append("ON UPDATE RESTRICT ON DELETE RESTRICT   );  \n");
        // Criar tabela dos intens da Compra
        sql.append(" CREATE TABLE CompraItens( \n");
        sql.append(" codigo serial not null, \n");
        sql.append(" compra integer not null, \n");
        sql.append(" produto integer not null, \n");
        sql.append(" quantidade integer not null, \n");
        sql.append(" valorUnitario real not null, \n");
        sql.append(" desconto real, \n");
        sql.append(" total real, \n");
        sql.append("   CONSTRAINT compraItens_pkey PRIMARY KEY (codigo), \n");
        sql.append("   CONSTRAINT fk_CompraItens_compra FOREIGN KEY (compra) \n");
        sql.append(" REFERENCES compra (codigo) MATCH SIMPLE \n");
        sql.append("       ON UPDATE RESTRICT ON DELETE RESTRICT,     \n");
        sql.append(" CONSTRAINT fk_compraItens_produto FOREIGN KEY (produto) \n");
        sql.append(" REFERENCES produto (codigo) MATCH SIMPLE \n");
        sql.append(" ON UPDATE RESTRICT ON DELETE RESTRICT   ); \n");
        // criar tabela de balanço
        sql.append(" CREATE TABLE Balanco( \n");
        sql.append(" codigo serial not null,  \n");
        sql.append(" empresa integer not null, \n");
        sql.append(" cancelado boolean default false, \n");
        sql.append(" dataCadastro timestamp without time zone, \n");
        sql.append(" usuarioCadastro integer not null, \n");
        sql.append(" dataCancelamento timestamp without time zone, \n");
        sql.append(" usuarioCancelamento integer,  \n");
        sql.append(" observacoes character varying(2000), \n");
        sql.append("  CONSTRAINT balanco_pkey PRIMARY KEY (codigo), \n");
        sql.append("   CONSTRAINT fk_Balanco_empresa FOREIGN KEY (empresa) \n");
        sql.append(" REFERENCES empresa (codigo) MATCH SIMPLE \n");
        sql.append(" ON UPDATE RESTRICT ON DELETE RESTRICT,   \n");
        sql.append("     CONSTRAINT fk_BalancoUsuCad_usuario FOREIGN KEY (usuarioCadastro) \n");
        sql.append(" REFERENCES usuario (codigo) MATCH SIMPLE \n");
        sql.append(" ON UPDATE RESTRICT ON DELETE RESTRICT,   \n");
        sql.append("     CONSTRAINT fk_BalancoUsuAlt_usuario FOREIGN KEY (usuarioCancelamento) \n");
        sql.append(" REFERENCES usuario (codigo) MATCH SIMPLE \n");
        sql.append(" ON UPDATE RESTRICT ON DELETE RESTRICT   \n");
        sql.append(" ); \n");
        // criar tabela de balancoItens
        sql.append(" CREATE TABLE BalancoItens( \n");
        sql.append(" codigo serial not null,  \n");
        sql.append(" balanco integer not null, \n");
        sql.append(" produto integer not null, \n");
        sql.append(" qtdeEstoqueAnterior integer not null, \n");
        sql.append(" qtdeBalanco integer not null, \n");
        sql.append("   CONSTRAINT balancoItens_pkey PRIMARY KEY (codigo), \n");
        sql.append("   CONSTRAINT fk_BalancoItens_balanco FOREIGN KEY (balanco) \n");
        sql.append("       REFERENCES balanco (codigo) MATCH SIMPLE \n");
        sql.append("       ON UPDATE RESTRICT ON DELETE RESTRICT,    \n");
        sql.append("    CONSTRAINT fk_balancoItens_produto FOREIGN KEY (produto) \n");
        sql.append(" REFERENCES produto (codigo) MATCH SIMPLE \n");
        sql.append(" ON UPDATE RESTRICT ON DELETE RESTRICT \n");
        sql.append(" ); \n");
        // Criar tabela para controlar o estoque do produto por empresa.
        sql.append(" CREATE TABLE produtoestoque( \n");
        sql.append("   codigo serial NOT NULL, \n");
        sql.append("   empresa integer NOT NULL, \n");
        sql.append("   produto integer NOT NULL, \n");
        sql.append("   estoque integer, \n");
        sql.append("   estoqueminimo integer, \n");
        sql.append("   situacao character(1) NOT NULL, \n");
        sql.append("   ape character varying(10), \n");
        sql.append("   CONSTRAINT produtoestoque_pkey PRIMARY KEY (codigo), \n");
        sql.append("   CONSTRAINT fk_produtoestoque_empresa FOREIGN KEY (empresa) \n");
        sql.append("       REFERENCES empresa (codigo) MATCH SIMPLE \n");
        sql.append("       ON UPDATE RESTRICT ON DELETE RESTRICT, \n");
        sql.append("   CONSTRAINT fk_produtoestoque_produto FOREIGN KEY (produto) \n");
        sql.append("       REFERENCES produto (codigo) MATCH SIMPLE \n");
        sql.append("       ON UPDATE RESTRICT ON DELETE RESTRICT, \n");
        sql.append("   CONSTRAINT unique_produtoempresa UNIQUE (produto, empresa) ); \n");
        // criar tabela para armazenar as alterações da situaçao do produtoEstoque
        sql.append(" CREATE TABLE produtoestoque_alteracaosit( \n");
        sql.append("   codigo serial NOT NULL, \n");
        sql.append("   produtoestoque integer NOT NULL, \n");
        sql.append("   datacadastro timestamp without time zone NOT NULL, \n");
        sql.append("   usuario integer NOT NULL, \n");
        sql.append("   situacao character(1) NOT NULL, \n");
        sql.append("   CONSTRAINT prodestaltsit_pkey PRIMARY KEY (codigo), \n");
        sql.append("   CONSTRAINT fk_prodestalt_produtoest FOREIGN KEY (produtoestoque) \n");
        sql.append("       REFERENCES produtoestoque (codigo) MATCH SIMPLE \n");
        sql.append("       ON UPDATE RESTRICT ON DELETE RESTRICT, \n");
        sql.append("   CONSTRAINT fk_produtoestusualt_usuario FOREIGN KEY (usuario) \n");
        sql.append("       REFERENCES usuario (codigo) MATCH SIMPLE \n");
        sql.append("       ON UPDATE RESTRICT ON DELETE RESTRICT); \n");
        // criar tabela para gravar os cancelamentos de vendas no ZillyonWeb, para os produtos do controle de estoque.
        sql.append(" create table exclusaoMovProdutoEstoque( \n");
        sql.append(" codigo serial NOT NULL, \n");
        sql.append(" produto integer NOT NULL,   \n");
        sql.append("   empresa integer NOT NULL, \n");
        sql.append("   pessoa integer, \n");
        sql.append("   quantidade integer, \n");
        sql.append("   datavenda timestamp without time zone, \n");
        sql.append("   dataexclusao timestamp without time zone, \n");
        sql.append(" CONSTRAINT exclusaoMovprodutoEst_pkey PRIMARY KEY (codigo),   \n");
        sql.append(" CONSTRAINT fk_Exclmovproduto_empresa FOREIGN KEY (empresa) \n");
        sql.append("       REFERENCES empresa (codigo) MATCH SIMPLE \n");
        sql.append("       ON UPDATE RESTRICT ON DELETE RESTRICT, \n");
        sql.append("   CONSTRAINT fk_Exclmovproduto_pessoa FOREIGN KEY (pessoa) \n");
        sql.append("       REFERENCES pessoa (codigo) MATCH SIMPLE \n");
        sql.append("       ON UPDATE RESTRICT ON DELETE RESTRICT, \n");
        sql.append("   CONSTRAINT fk_Exclmovproduto_produto FOREIGN KEY (produto) \n");
        sql.append("       REFERENCES produto (codigo) MATCH SIMPLE \n");
        sql.append("       ON UPDATE RESTRICT ON DELETE RESTRICT );  \n");
        return sql.toString();
    }

    public static String retornarSQLTriggersProdutoEstoque() {
        StringBuilder sql = new StringBuilder();
        sql.append(" CREATE TRIGGER tg_bd_alterarestoque_produtoestoque \n");
        sql.append("   BEFORE DELETE \n");
        sql.append("   ON produtoestoque \n");
        sql.append("   FOR EACH ROW \n");
        sql.append("   EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");

        sql.append(" CREATE TRIGGER tg_bu_alterarestoque_produtoestoque \n");
        sql.append("   BEFORE UPDATE \n");
        sql.append("   ON produtoestoque \n");
        sql.append("   FOR EACH ROW \n");
        sql.append("   EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");
        return sql.toString();
    }

    public static String retornarSQLTriggersBalanco() {
        StringBuilder sql = new StringBuilder();
        sql.append(" CREATE TRIGGER tg_au_AlterarEstoque_Balanco \n");
        sql.append(" AFTER UPDATE \n");
        sql.append(" ON balanco \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_after_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_bd_AlterarEstoque_Balanco \n");
        sql.append(" BEFORE DELETE \n");
        sql.append(" ON balanco \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_bu_AlterarEstoque_balanco \n");
        sql.append(" BEFORE UPDATE \n");
        sql.append(" ON balanco \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_ai_AlterarEstoque_BalancoItens \n");
        sql.append(" AFTER INSERT \n");
        sql.append(" ON balancoitens \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_after_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_bd_AlterarEstoque_BalancoItens \n");
        sql.append(" BEFORE DELETE \n");
        sql.append(" ON balancoitens \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_bi_AlterarEstoque_BalancoItens \n");
        sql.append(" BEFORE INSERT \n");
        sql.append(" ON balancoitens \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_bu_AlterarEstoque_BalancoItens \n");
        sql.append(" BEFORE UPDATE \n");
        sql.append(" ON balancoitens \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");

        return sql.toString();
    }

    public static String retornarSQLTriggersCompra() {
        StringBuilder sql = new StringBuilder();

        sql.append(" CREATE TRIGGER tg_au_AlterarEstoque_Compra \n");
        sql.append(" AFTER UPDATE \n");
        sql.append(" ON compra \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_after_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_bd_AlterarEstoque_Compra \n");
        sql.append(" BEFORE DELETE \n");
        sql.append(" ON compra \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_bu_AlterarEstoque_Compra \n");
        sql.append(" BEFORE UPDATE \n");
        sql.append(" ON compra \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_ai_AlterarEstoque_CompraItens \n");
        sql.append(" AFTER INSERT \n");
        sql.append(" ON compraitens \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_after_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_bd_AlterarEstoque_CompraItens \n");
        sql.append(" BEFORE DELETE \n");
        sql.append(" ON compraitens \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_bi_AlterarEstoque_CompraItens \n");
        sql.append(" BEFORE INSERT \n");
        sql.append(" ON compraitens \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");
        sql.append("  \n");
        sql.append(" CREATE TRIGGER tg_bu_AlterarEstoque_CompraItens \n");
        sql.append(" BEFORE UPDATE \n");
        sql.append(" ON compraitens \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");
        return sql.toString();
    }

    public static String retornarSQLTriggersMovProduto() {
        StringBuilder sql = new StringBuilder();
        sql.append(" CREATE TRIGGER tg_ad_AlterarEstoque_MovProduto \n");
        sql.append(" AFTER DELETE \n");
        sql.append(" ON movproduto \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_after_alterarestoque(); \n");
        sql.append("    \n");
        sql.append(" CREATE TRIGGER tg_ai_AlterarEstoque_MovProduto \n");
        sql.append(" AFTER INSERT \n");
        sql.append(" ON movproduto \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_after_alterarestoque(); \n");
        sql.append("    \n");
        sql.append("    \n");
        sql.append(" CREATE TRIGGER tg_bi_AlterarEstoque_MovProduto \n");
        sql.append(" BEFORE INSERT \n");
        sql.append(" ON movproduto \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");
        sql.append("  \n");
        sql.append("    \n");
        sql.append(" CREATE TRIGGER tg_bu_AlterarEstoque_MovProduto \n");
        sql.append(" BEFORE UPDATE \n");
        sql.append(" ON movproduto \n");
        sql.append(" FOR EACH ROW \n");
        sql.append(" EXECUTE PROCEDURE fn_tg_before_alterarestoque(); \n");

        return sql.toString();

    }

    public static String retornarSQLTriggerBeforeEstoque() {
        StringBuilder sql = new StringBuilder();
        sql.append(" CREATE OR REPLACE FUNCTION fn_tg_before_alterarestoque() \n");
        sql.append(" RETURNS trigger AS \n");
        sql.append(" $BODY$  \n");
        sql.append("  declare   \n");
        sql.append("      reg_prod record;  \n");
        sql.append("  BEGIN  \n");
        sql.append("    \n");
        sql.append(" --RAISE EXCEPTION 'TABELA: %  OPERACAO % ',TG_RELNAME, TG_OP ;  \n");
        sql.append("   \n");
        sql.append("    IF (upper(TG_RELNAME) = 'PRODUTOESTOQUE') THEN  \n");
        sql.append("         \n");
        sql.append(" 	     IF upper(TG_OP) = 'DELETE' THEN  \n");
        sql.append(" 		 RAISE EXCEPTION 'Não é permitido excluir o produto estoque. Só é permitido alterar a situaçao através da tela de cadastro de produto estoque.!';  \n");
        sql.append(" 	     END IF;  \n");
        sql.append(" 	    \n");
        sql.append(" 	     IF  upper(TG_OP) = 'UPDATE' THEN \n");
        sql.append(" 	       IF (new.estoque <> old.estoque) THEN  \n");
        sql.append(" 		   IF ((new.ape is null) or (new.ape <> 'trigger' )) THEN \n");
        sql.append(" RAISE EXCEPTION 'Não é permitido alterar o estoque do produto via sql.!';  \n");
        sql.append(" 		   END IF;  \n");
        sql.append(" 	        END IF; 	  \n");
        sql.append(" 	     END IF;    \n");
        sql.append(" 	      IF (new.situacao <> old.situacao) THEN    \n");
        sql.append(" 		 IF (new.ape is null) or (new.ape <> 'trigger' ) THEN \n");
        sql.append(" 		   RAISE EXCEPTION 'Não é permitido alterar a situação do produto estoque via sql.!';  \n");
        sql.append(" 		 END IF;  \n");
        sql.append(" 	      END IF; \n");
        sql.append("     END IF; \n");
        sql.append("         \n");
        sql.append("    IF (upper(TG_RELNAME) = 'COMPRA') THEN  \n");
        sql.append("  	   IF upper(TG_OP) = 'UPDATE' THEN      \n");
        sql.append("  		 IF (old.cancelada = true) and (new.cancelada = false) THEN  \n");
        sql.append("  		   RAISE EXCEPTION 'Não é possível alterar a situação da compra de cancelada para ativa!';  \n");
        sql.append("  		 END IF;  \n");
        sql.append("  	   END IF;  \n");
        sql.append("  	   IF upper(TG_OP) = 'DELETE' THEN  \n");
        sql.append("  		 RAISE EXCEPTION 'Não é permitido excluir a compra. Só é permitido cancelar a compra.!';  \n");
        sql.append("  	   END IF;  \n");
        sql.append("  	END IF;  \n");
        sql.append("    \n");
        sql.append("  	IF (upper(TG_RELNAME) = 'COMPRAITENS') THEN  \n");
        sql.append(" 	   IF upper(TG_OP) = 'UPDATE' THEN      \n");
        sql.append(" 		   RAISE EXCEPTION 'Não é permitido alterar os itens da compra. !';  \n");
        sql.append(" 	   END IF;  \n");
        sql.append(" 	   IF upper(TG_OP) = 'DELETE' THEN  \n");
        sql.append(" 		 RAISE EXCEPTION 'Não é permitido excluir os itens da compra. !';  \n");
        sql.append(" 	   END IF;  \n");
        sql.append("  	END IF;  \n");
        sql.append("    \n");
        sql.append("  	IF (upper(TG_RELNAME) = 'BALANCO') THEN  \n");
        sql.append("  	   IF upper(TG_OP) = 'UPDATE' THEN      \n");
        sql.append("  		 IF (old.cancelado = true) and (new.cancelado = false) THEN  \n");
        sql.append("  		   RAISE EXCEPTION 'Não é possível alterar a situação do balanço de cancelado para ativo!';  \n");
        sql.append("  		 END IF;  \n");
        sql.append("  	   END IF;  \n");
        sql.append("  	   IF upper(TG_OP) = 'DELETE' THEN  \n");
        sql.append("  		 RAISE EXCEPTION 'Não é permitido excluir o balanço. Só é permitido cancelar o balanço.!';  \n");
        sql.append("  	   END IF;  \n");
        sql.append("  	END IF;  \n");
        sql.append("    \n");
        sql.append("  	IF (upper(TG_RELNAME) = 'BALANCOITENS') THEN  \n");
        sql.append(" 	   IF upper(TG_OP) = 'UPDATE' THEN      \n");
        sql.append(" 		   RAISE EXCEPTION 'Não é permitido alterar os itens do balanço. !';  \n");
        sql.append(" 	   END IF;  \n");
        sql.append(" 	   IF upper(TG_OP) = 'DELETE' THEN  \n");
        sql.append(" 		 RAISE EXCEPTION 'Não é permitido excluir os itens do balanço. !';  \n");
        sql.append(" 	   END IF;  \n");
        sql.append("     END IF;  \n");
        sql.append("    \n");
        sql.append("    \n");
        sql.append("      RETURN NEW;  \n");
        sql.append("  END;  \n");
        sql.append("  $BODY$ \n");
        sql.append("   LANGUAGE 'plpgsql' VOLATILE; \n");

        return sql.toString();

    }

    public static String retornarSQLTriggerAfterEstoque() {
        StringBuilder sql = new StringBuilder();
        sql.append(" CREATE OR REPLACE FUNCTION fn_tg_after_alterarestoque() \n");
        sql.append(" RETURNS trigger AS \n");
        sql.append(" $BODY$  \n");
        sql.append("  declare   \n");
        sql.append("     reg_prod record;  \n");
        sql.append("  	reg_itens record;  \n");
        sql.append(" diferenca integer;  \n");
        sql.append(" codAux integer;  \n");
        sql.append(" 	codEmpresa integer;  \n");
        sql.append(" 	dataAd Date;  \n");
        sql.append("  BEGIN  \n");
        sql.append("      IF (upper(TG_RELNAME) = 'COMPRAITENS') THEN  \n");
        sql.append("        IF upper(TG_OP) = 'INSERT' THEN   \n");
        sql.append(" 	     select into codEmpresa empresa from compra where codigo = NEW.compra; \n");
        sql.append("          update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + new.quantidade where produto = new.produto and empresa = codEmpresa and situacao = 'A';	     \n");
        sql.append(" 		 update produtoEstoque set ape = null where produto = new.produto and empresa = codEmpresa;	     \n");
        sql.append("        END IF;  \n");
        sql.append("      END IF;  \n");
        sql.append("  \n");
        sql.append("     IF (upper(TG_RELNAME) = 'COMPRA') THEN  \n");
        sql.append("       IF upper(TG_OP) = 'UPDATE' THEN  \n");
        sql.append("         -- remover do estoque os produtos da compra cancelada.  \n");
        sql.append("  	     IF (new.cancelada <> old.cancelada) and (new.cancelada = true) THEN  \n");
        sql.append("           FOR reg_itens in select * from compraItens where compra = old.codigo  LOOP  \n");
        sql.append("                update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) - reg_itens.quantidade where produto = reg_itens.produto and empresa = new.empresa and situacao = 'A' ;	     \n");
        sql.append(" 			   update produtoEstoque set ape = null where produto = reg_itens.produto and empresa = new.empresa;	     \n");
        sql.append("            END LOOP;		   \n");
        sql.append("          END IF; 		   \n");
        sql.append("       END IF;  \n");
        sql.append("     END IF;  \n");
        sql.append("  \n");
        sql.append("     IF (upper(TG_RELNAME) = 'BALANCOITENS') THEN  \n");
        sql.append(" 	  select into codEmpresa empresa from balanco where codigo = NEW.balanco; \n");
        sql.append("       IF upper(TG_OP) = 'INSERT' THEN   \n");
        sql.append("         update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + (new.qtdeBalanco - coalesce(new.qtdeEstoqueAnterior,0)) where produto = new.produto and empresa = codEmpresa and situacao = 'A';	     \n");
        sql.append(" 		update produtoEstoque set ape = null where produto = new.produto and empresa = codEmpresa;	     \n");
        sql.append("       END IF;  \n");
        sql.append("     END IF;  \n");
        sql.append("  \n");
        sql.append("     IF (upper(TG_RELNAME) = 'BALANCO') THEN  \n");
        sql.append("        IF upper(TG_OP) = 'UPDATE' THEN  \n");
        sql.append("          -- Desfazer as alterações de estoque para o balanço cancelado.  \n");
        sql.append("  	     IF (new.cancelado <> old.cancelado) and (new.cancelado = true) THEN  \n");
        sql.append("             FOR reg_itens in select * from balancoItens where balanco = old.codigo  LOOP  \n");
        sql.append("                update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + ((reg_itens.qtdeBalanco - coalesce(reg_itens.qtdeEstoqueAnterior,0)) * -1)  where produto = reg_itens.produto and empresa = new.empresa and situacao = 'A';	     \n");
        sql.append("                update produtoEstoque set ape = null  where produto = reg_itens.produto and empresa = new.empresa;	    			    \n");
        sql.append("             END LOOP;		   \n");
        sql.append("          END IF; 		   \n");
        sql.append("        END IF;  \n");
        sql.append("     END IF;  \n");
        sql.append("  \n");
        sql.append("  \n");
        sql.append("     IF (upper(TG_RELNAME) = 'MOVPRODUTO') THEN  \n");
        sql.append("       IF upper(TG_OP) = 'INSERT' THEN  \n");
        sql.append("         select into codAux codigo from produtoEstoque where produto = NEW.produto;     \n");
        sql.append("         IF (codAux is not null) then -- verifica se o produto tem controle de estoque   \n");
        sql.append("            update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) - new.quantidade where produto = new.produto and empresa = new.empresa and situacao = 'A';	     \n");
        sql.append(" 		update produtoEstoque set ape = null where produto = new.produto and empresa = new.empresa;	     \n");
        sql.append("         END IF;  \n");
        sql.append("       END IF;  \n");
        sql.append("  \n");
        sql.append("       IF upper(TG_OP) = 'DELETE' THEN  \n");
        sql.append("         select into codAux codigo from produtoEstoque where produto = old.produto and empresa = old.empresa;    \n");
        sql.append("         IF (codAux is not null) then -- verifica se o produto tem controle de estoque   \n");
        sql.append("            update produtoEstoque set ape = 'trigger', estoque = coalesce(estoque, 0) + old.quantidade where produto = old.produto and empresa = old.empresa and situacao = 'A';	     \n");
        sql.append(" 		update produtoEstoque set ape = null where produto = old.produto and empresa = old.empresa; \n");
        sql.append(" 		-- pesquisar a data em que o produto foi adicionado ao controle de estoque. \n");
        sql.append(" 		select into dataAd cast (min(sit.dataCadastro) as date) \n");
        sql.append(" 		from produtoEstoque pe \n");
        sql.append(" 		inner join ProdutoEstoque_alteracaoSit sit on sit.produtoEstoque = pe.codigo \n");
        sql.append(" 		where pe.empresa = old.empresa and pe.produto = old.produto and sit.situacao = 'A'; \n");
        sql.append(" 		-- Gravar o cancelamento da venda para ser visualizado no Cardex, somente das vendas que foram realizadas após a data em que o produto foi adicionado ao controle de estoque. \n");
        sql.append(" 		IF (old.dataLancamento >= dataAd) THEN \n");
        sql.append(" 		    -- Registrar o cancelamento, somente para as vendas que tiveram balanço realizado após a data da venda.  \n");
        sql.append(" 		    select into codAux b.codigo   \n");
        sql.append(" 		    from balanco b  \n");
        sql.append(" 		    inner join balancoItens bi on bi.balanco = b.codigo  \n");
        sql.append(" 		    where b.dataCadastro > old.dataLancamento and b.empresa = old.empresa and bi.produto = old.produto;  \n");
        sql.append(" 		    IF (codAux is not null) THEN  \n");
        sql.append(" 		      insert into exclusaoMovProdutoEstoque(produto, empresa, pessoa, quantidade, dataExclusao, datavenda) values(old.produto, old.empresa, old.pessoa, old.quantidade, now(), old.dataLancamento); \n");
        sql.append(" 		    END IF;  \n");
        sql.append(" 		END IF;   \n");
        sql.append("         END IF;  \n");
        sql.append("       END IF;  \n");
        sql.append("  	END IF;  \n");
        sql.append("  \n");
        sql.append("  \n");
        sql.append("  \n");
        sql.append("  RETURN NEW;  \n");
        sql.append("  END;  \n");
        sql.append("  $BODY$ \n");
        sql.append("   LANGUAGE 'plpgsql' VOLATILE; \n");
        return sql.toString();

    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

@ClasseProcesso(autor = "Joao Alcides",
        data = "21/02/2025",
        descricao = "corrigir dados de contratos que estão como renovação e não são",
        motivacao = "erro na tela de negociação")
public class AtualizacaoTicketMJ629 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        String consultaResponsavelRenovacao = "select codigo from contrato\n" +
                "where contrato.origemsistema = 17\n" +
                "and contrato.contratoresponsavelrenovacaomatricula > 0 \n" +
                "and not exists (select codigo from contrato c \n" +
                "where codigo = contrato.contratoresponsavelrenovacaomatricula and c.pessoa = contrato.pessoa)";

        String consultaBaseadoRenovacao = "select contratobaseadorenovacao, codigo, " +
                "pessoa from contrato \n" +
                "where contrato.origemsistema = 17\n" +
                "and contrato.situacaocontrato = 'RN'\n" +
                "and contrato.contratobaseadorenovacao > 0 \n" +
                "and not exists (select codigo from contrato c " +
                "where codigo = contrato.contratobaseadorenovacao and c.pessoa = contrato.pessoa)";

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
            ResultSet rsBaseado = SuperFacadeJDBC.criarConsulta(consultaBaseadoRenovacao, c);
            ResultSet rsResponsavel = SuperFacadeJDBC.criarConsulta(consultaResponsavelRenovacao, c)) {

            while (rsBaseado.next()) {
                try {
                    String codigo = rsBaseado.getString("codigo");
                    SuperFacadeJDBC.executarUpdateExecutarProcessos("update contrato " +
                            "set situacaocontrato = 'MA', " +
                            " contratobaseadorenovacao = 0 " +
                            "where codigo = " + codigo, c);

                }catch (Exception e){
                    e.printStackTrace();
                }
            }

            while (rsResponsavel.next()) {
                try {
                    Integer contratobaseadorenovacao = rsResponsavel.getInt("codigo");
                        try (ResultSet rsContratoOrigem = SuperFacadeJDBC.criarConsulta("select codigo from contrato " +
                                " where contratobaseadorenovacao = " + contratobaseadorenovacao, c)) {
                            int codigoOrigem = 0;
                            if (rsContratoOrigem.next()) {
                                codigoOrigem = rsContratoOrigem.getInt("codigo");
                            }
                            SuperFacadeJDBC.executarUpdateExecutarProcessos("update contrato " +
                                    "set contratoresponsavelrenovacaomatricula = " + codigoOrigem +
                                    " where codigo = " + contratobaseadorenovacao, c);
                        }

                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Denis Silva",
        data = "22/07/2024",
        descricao = "Migrando videos já salvos para a tabela turmavideo",
        motivacao = "Ticket APPS-1495 Fazer um migrador para que os vídeos já salvos não sejam perdidos ao atualizar.")
public class MigrarVideoParaTabelaTurmaVideo implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("insert into turmavideo (turma_codigo, linkvide<PERSON>, professor) \n" +
                    "select a.codigo, a.urlvideoyoutube, false from turma a \n" +
                    "left join turmavideo tv on a.codigo = tv.turma_codigo \n" +
                    "where coalesce(a.urlvideoyoutube, '') <> '' \n" +
                    "and tv.codigo is null ", c);

        }
    }
}

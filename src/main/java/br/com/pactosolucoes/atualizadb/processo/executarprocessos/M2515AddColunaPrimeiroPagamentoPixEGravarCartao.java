package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "07/10/2024",
        descricao = "Adicionar coluna primeiraCobrancaPixEGuardarCartao na tabela vendasonlineconfig.",
        motivacao = "M2-2515")
public class M2515AddColunaPrimeiroPagamentoPixEGravarCartao implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN primeiraCobrancaPixEGuardarCartao Boolean DEFAULT false;", c);
        }
    }
}

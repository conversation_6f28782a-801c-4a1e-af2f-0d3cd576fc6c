/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.awss3.AmazonS3Client;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

/**
 *
 * <AUTHOR>
 */
public class AplicarFotoAlunoImportacao {

    public static void main(String... args) {
        try {
            Connection con = DriverManager.getConnection("**********************************************", "postgres", "pactodb");
            String chave = "aca438e8c9e947e64db2236bb2f1f7a9";
            try {
                SuperFacadeJDBC.executarConsulta("ALTER TABLE cliente ADD COLUMN urlfotoimp text;", con);
            } catch (Exception e) {
//                e.printStackTrace();
            }
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select p.nome, cliente.codigo, codigomatricula, urlfotoimp, pessoa from cliente "
                    + " INNER JOIN pessoa p ON cliente.pessoa = p.codigo "
                    + " WHERE urlfotoimp is not null and urlfotoimp not like ''", con);
            while (rs.next()) {
                String urlFoto = rs.getString("urlfotoimp");
                String nome = rs.getString("nome");
                Integer pessoa = rs.getInt("pessoa");
                byte[] b = getImage(urlFoto);
                System.out.println(nome +" - "+urlFoto);
                MidiaService.getInstance().uploadObjectFromByteArray(chave,
                                MidiaEntidadeEnum.FOTO_PESSOA, pessoa.toString(), b);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static byte[] getImage(String imageUrl) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        InputStream is = null;
        URL u = new URL(imageUrl);
        byte[] byteChunk = new byte[4096];
        try {
            is = u.openStream();
             // Or whatever size you want to read in at a time.
            int n;
            while ((n = is.read(byteChunk)) > 0) {
                baos.write(byteChunk, 0, n);
            }
        } catch (IOException e) {
            System.err.printf("Failed while reading bytes from %s: %s", u.toExternalForm(), e.getMessage());
            e.printStackTrace();
        } finally {
            if (is != null) {
                is.close();
            }
        }
        return byteChunk;
    }
}

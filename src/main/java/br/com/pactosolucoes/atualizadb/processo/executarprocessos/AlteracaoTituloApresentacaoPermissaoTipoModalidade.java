package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Gabriel Rodrigues",
        data = "01/10/2024",
        descricao = "Alterar tipo apresentação da entidade TipoModalidade",
        motivacao = "Corrigir duplicação do tituloapresentação")
public class AlteracaoTituloApresentacaoPermissaoTipoModalidade implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "UPDATE permissao set tituloapresentacao = '5.17 - Tipo de Modalidade' where nomeentidade = 'TipoModalidade';",
                    c
            );
        }
    }
}


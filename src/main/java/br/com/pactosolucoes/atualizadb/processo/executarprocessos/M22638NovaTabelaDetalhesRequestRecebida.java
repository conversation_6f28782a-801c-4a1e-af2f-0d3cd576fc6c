package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 30/10/2024
 */

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "30/10/2024",
        descricao = "Nova tabela para gaurdar informações relevantes das requests recebidas no zw para futuras análises",
        motivacao = "M2-2638")
public class M22638NovaTabelaDetalhesRequestRecebida implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.detalhesRequestRecebida (codigo SERIAL NOT NULL, \n" +
                    "dataRegistro TIMESTAMP WITHOUT TIME ZONE, \n" +
                    "url VARCHAR, \n" +
                    "body TEXT, \n" +
                    "userAgent TEXT, \n" +
                    "ip VARCHAR);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.detalhesRequest  RENAME TO detalhesRequestEnviada", c);
        }
    }
}


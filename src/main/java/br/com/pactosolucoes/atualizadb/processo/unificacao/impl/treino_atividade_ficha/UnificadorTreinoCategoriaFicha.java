package br.com.pactosolucoes.atualizadb.processo.unificacao.impl.treino_atividade_ficha;

import br.com.pactosolucoes.atualizadb.processo.unificacao.AbstractUnificadorCodigoOrigemDestinoMapeavel;
import br.com.pactosolucoes.atualizadb.processo.unificacao.CodigoOrigemRetornavel;
import br.com.pactosolucoes.atualizadb.processo.unificacao.UnificadorFilho;
import br.com.pactosolucoes.atualizadb.processo.unificacao.Unificavel;
import br.com.pactosolucoes.atualizadb.processo.unificacao.metadata.NomeColuna;
import br.com.pactosolucoes.atualizadb.processo.unificacao.wrapper.ConnectionUnificacao;

/**
 * Deve realizar a unificação da tabela 'categoriaficha' do módulo do TreinoWeb. <br>
 * O que impede a duplicação de registros é a <b>categoriaficha_nome_key</b>, atribuído a coluna <b>nome</b>.
 *
 * <AUTHOR>
 * @since 05/04/2019
 */
class UnificadorTreinoCategoriaFicha extends AbstractUnificadorCodigoOrigemDestinoMapeavel implements UnificadorFilho<UnificadorTreinoAtividadesImpl> {

    @Override
    public String getNomeTabelaAlvo() {
        return "categoriaficha";
    }

    @Override
    public void executar(UnificadorTreinoAtividadesImpl unificadorOrquestrador,
                         ConnectionUnificacao conexaoOrigem,
                         ConnectionUnificacao conexaoDestino) throws Exception {
        unificadorOrquestrador.realizarUnificacaoViaReflection(conexaoOrigem, conexaoDestino, CategoriaFicha.class);
    }

    public static class CategoriaFicha implements Unificavel, CodigoOrigemRetornavel {

        @NomeColuna("codigo")
        private Integer codigo;

        @NomeColuna("nome")
        private String nome;

        @Override
        public Integer getCodigo() {
            return codigo;
        }

        public String getNome() {
            return nome;
        }

        public void setCodigo(Integer codigo) {
            this.codigo = codigo;
        }

        public void setNome(String nome) {
            this.nome = nome;
        }
    }

}

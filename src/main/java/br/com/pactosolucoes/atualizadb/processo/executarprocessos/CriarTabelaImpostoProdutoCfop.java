package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Vinicius Franca",
        data = "10/03/2025",
        descricao = "Criar tabela impostoprodutocfop no banco de dados",
        motivacao = "GCM-49")
public class CriarTabelaImpostoProdutoCfop implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {

        String sqlCriarTabela = "CREATE TABLE impostoprodutocfop (\n" +
                "    codigo SERIAL PRIMARY KEY,\n" +
                "    cfop VARCHAR(50) DEFAULT '' NOT NULL,\n" +
                "    ncm VARCHAR(10),\n" +
                "    empresa INTEGER NOT NULL,\n" +
                "    configuracaonotafiscalnfse INTEGER,\n" +
                "    configuracaonotafiscalnfce INTEGER,\n" +
                "    codigolistaservico VARCHAR(50) DEFAULT '',\n" +
                "    codigotributacaomunicipio VARCHAR(50) DEFAULT '',\n" +
                "    descricaoservicomunicipio VARCHAR,\n" +
                "    enviarpercentualimposto BOOLEAN DEFAULT TRUE,\n" +
                "    percentualfederal FLOAT DEFAULT 5.0,\n" +
                "    percentualestadual FLOAT DEFAULT 5.0,\n" +
                "    percentualmunicipal FLOAT DEFAULT 5.0,\n" +
                "    situacaotributariaissqn VARCHAR(5) DEFAULT '',\n" +
                "    aliquotaissqn NUMERIC DEFAULT 0.0,\n" +
                "    situacaotributariapis VARCHAR(5) DEFAULT '',\n" +
                "    aliquotapis NUMERIC DEFAULT 0.0,\n" +
                "    isentopis BOOLEAN DEFAULT FALSE,\n" +
                "    enviaaliquotanfepis BOOLEAN DEFAULT FALSE,\n" +
                "    situacaotributariacofins VARCHAR(5) DEFAULT '',\n" +
                "    aliquotacofins NUMERIC DEFAULT 0.0,\n" +
                "    isentocofins BOOLEAN DEFAULT FALSE,\n" +
                "    enviaaliquotanfecofins BOOLEAN DEFAULT FALSE,\n" +
                "    isentoicms BOOLEAN DEFAULT FALSE,\n" +
                "    situacaotributariaicms VARCHAR(5) DEFAULT '',\n" +
                "    aliquotaicms NUMERIC DEFAULT 0.0,\n" +
                "    enviaaliquotanfeicms BOOLEAN DEFAULT FALSE,\n" +
                "    codigobeneficiofiscal VARCHAR(20) DEFAULT '',\n" +
                "    desativado BOOLEAN DEFAULT FALSE\n" +
                ");";

        String sqlCriarIndices = "CREATE INDEX index_cfop ON impostoprodutocfop (cfop);" +
                "CREATE INDEX index_ncm ON impostoprodutocfop (ncm);";

        String sqlChaveEstrangeira = "ALTER TABLE impostoprodutocfop \n" +
                "ADD CONSTRAINT fk_impostoprodutocfop_empresa \n" +
                "FOREIGN KEY (empresa) REFERENCES empresa(codigo);";

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlCriarTabela, c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlCriarIndices, c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlChaveEstrangeira, c);
        }
    }
}

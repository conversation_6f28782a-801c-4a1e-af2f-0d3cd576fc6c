/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.CepVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Cep;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 09/04/2020
 */
public class ProcessoPovoarCidade {

    private Map<Integer, EmpresaVO> mapaEmpresas;
    private UsuarioVO usuarioRecorrencia;
    private Connection con;

    public ProcessoPovoarCidade(Connection con) throws Exception {
        Usuario usuarioDAO = null;
        Empresa empresaDAO = null;
        try {
            this.con = con;

            usuarioDAO = new Usuario(this.con);
            empresaDAO = new Empresa(this.con);

            usuarioRecorrencia = usuarioDAO.getUsuarioRecorrencia();

            mapaEmpresas = new HashMap<>();
            List<EmpresaVO> empresas = empresaDAO.consultarTodas(null, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            for (EmpresaVO empresaVO : empresas) {
                mapaEmpresas.put(empresaVO.getCodigo(), empresaVO);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
            empresaDAO = null;
        }
    }

    public static void main(String... args) {
        Connection con = null;
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"teste"};
            }
            if (args.length > 0) {
                chave = args[0];
                System.out.println( "Obter conexão para chave: " + chave);
            }

            con = new DAO().obterConexaoEspecifica(chave);

            ProcessoPovoarCidade service = new ProcessoPovoarCidade(con);
            service.processar(0, true, null);
            service = null;
        } catch (Exception ex) {
            Logger.getLogger(ProcessoPovoarCidade.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ignored) {
                }
            }
        }
    }


    public void processar(Integer empresaFiltrar, boolean seNaoEncontrarUsarCidadeEmpresa, UsuarioVO usuarioVO) throws Exception {

        if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
            usuarioVO = this.usuarioRecorrencia;
        }

        Cep cepDAO;
        Cidade cidadeDAO;
        try {
            cepDAO = new Cep();
            cidadeDAO = new Cidade(con);

            System.out.println( "### Iniciando ProcessoPovoarCidade");

            StringBuilder sql = new StringBuilder();
            sql.append("select * from ( \n");
            sql.append("select  \n");
            sql.append("p.codigo as pessoa, \n");
            sql.append("p.pais, \n");
            sql.append("p.estado, \n");
            sql.append("p.cidade, \n");
            sql.append("(select cep from endereco where pessoa = p.codigo and coalesce(cep, '') <> '' and enderecocorrespondencia limit 1) as cepCorrespondencia, \n");
            sql.append("(select cep from endereco where pessoa = p.codigo and coalesce(cep, '') <> '' limit 1) as cep, \n");
            sql.append("cl.empresa as empresacliente, \n");
            sql.append("co.empresa as empresacolaborador \n");
            sql.append("from pessoa p  \n");
            sql.append("left join cliente cl on cl.pessoa = p.codigo \n");
            sql.append("left join colaborador co on co.pessoa = p.codigo \n");
            sql.append("where p.cidade is null \n");
            if (!UteisValidacao.emptyNumber(empresaFiltrar)) {
                sql.append("and (cl.empresa = ").append(empresaFiltrar).append(" OR co.empresa = ").append(empresaFiltrar).append(") \n");
            }
            sql.append("order by p.codigo ) as sql \n");
            sql.append("where  \n");
            sql.append("(sql.empresacliente is not null or sql.empresacolaborador is not null or coalesce(cepCorrespondencia, '') <> '' or  coalesce(cep, '') <> '') \n");

            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql.toString() + ") as qtd", con);
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            int i = 0;
            Set<Integer> pessoasProcessas = new HashSet<>();
            while (rs.next()) {
                try {
                    con.setAutoCommit(false);

                    Integer pessoa = rs.getInt("pessoa");

                    System.out.println("Processando " + ++i + "/" + total + " | " + pessoa);

                    JSONObject jsonAlteracoes = new JSONObject();


                    Integer pais = rs.getInt("pais");
                    Integer estado = rs.getInt("estado");
                    Integer cidade = rs.getInt("cidade");
                    Integer empresacliente = rs.getInt("empresacliente");
                    Integer empresacolaborador = rs.getInt("empresacolaborador");
                    String cepCorrespondencia = Uteis.removerMascara(rs.getString("cepCorrespondencia"));
                    String cep = Uteis.removerMascara(rs.getString("cep"));

                    if (cep != null && cep.length() < 8) {
                        cep = "";
                    }
                    if (cepCorrespondencia != null && cepCorrespondencia.length() < 8) {
                        cepCorrespondencia = "";
                    }

                    jsonAlteracoes.put("paisAnterior", pais);
                    jsonAlteracoes.put("estadoAnterior", estado);
                    jsonAlteracoes.put("cidadeAnterior", cidade);


                    if (pessoasProcessas.contains(pessoa)) {
                        continue;
                    }
                    pessoasProcessas.add(pessoa);



                    PaisVO objPais = null;
                    EstadoVO objEstado = null;
                    CidadeVO objCidade = null;

                    if (!seNaoEncontrarUsarCidadeEmpresa && UteisValidacao.emptyString(cep) && UteisValidacao.emptyString(cepCorrespondencia)) {
                        //aluno não tem cep para ser utilziado na busca.. e não é para utilizar o da empresa.. .
                        //então vamos pular ele...
                        continue;
                    }


                    if (!UteisValidacao.emptyString(cep) || !UteisValidacao.emptyString(cepCorrespondencia)) {
                        //buscar pelo cep que o aluno tem no endereço

                        CepVO cepVO = null;

                        //buscar pelo endereço q tem correspondencia marcado
                        if (!UteisValidacao.emptyString(cepCorrespondencia)) {
                            try {
                                cepVO = cepDAO.consultarPorNumeroCep(cepCorrespondencia, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            } catch (Exception ex) {
                                cepVO = null;
                            }
                        }

                        //se não encontrou acima busca pelo outro cep
                        if (cepVO == null && !UteisValidacao.emptyString(cep)) {
                            try {
                                cepVO = cepDAO.consultarPorNumeroCep(cep, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            } catch (Exception ex) {
                                cepVO = null;
                            }
                        }

                        if (cepVO == null || UteisValidacao.emptyNumber(cepVO.getCodigo())){
                            if (!UteisValidacao.emptyString(cepCorrespondencia)) {
                                cepVO = cepDAO.consultarApiViaCEP(cepCorrespondencia);
                            } else if (!UteisValidacao.emptyString(cep)) {
                                cepVO = cepDAO.consultarApiViaCEP(cep);
                            }
                        }

                        if (cepVO != null && !UteisValidacao.emptyString(cepVO.getCidadeDescricao().trim()) && !UteisValidacao.emptyString(cepVO.getUfSigla())) {
                            objCidade = cidadeDAO.consultarPorNomeCidadeSiglaEstado(Uteis.retirarAcentuacao(cepVO.getCidadeDescricao().trim()), cepVO.getUfSigla());

                            if (objCidade == null || UteisValidacao.emptyNumber(objCidade.getCodigo())) {
                                //não encontrou cidade
                                continue;
                            }

                            objPais = objCidade.getPais();
                            objEstado = objCidade.getEstado();
                        }
                    }


                    //não tem cep e é para utilizar o da empresa
                    if (seNaoEncontrarUsarCidadeEmpresa && (objPais == null || UteisValidacao.emptyNumber(objPais.getCodigo()) ||
                            objEstado == null || UteisValidacao.emptyNumber(objEstado.getCodigo()) ||
                            objCidade == null || UteisValidacao.emptyNumber(objCidade.getCodigo()))) {

                        EmpresaVO empresaVO = null;
                        Integer empresaUtilizar = empresacliente;
                        if (UteisValidacao.emptyNumber(empresaUtilizar)) {
                            empresaUtilizar = empresacolaborador;
                        }

                        //não encontrou empresa vinculado a essa pessoa
                        if (UteisValidacao.emptyNumber(empresaUtilizar)) {
                            continue;
                        }

                        empresaVO = mapaEmpresas.get(empresaUtilizar);
                        if (empresaVO == null) {
                            continue;
                        }

                        objPais = empresaVO.getPais();
                        objEstado = empresaVO.getEstado();
                        objCidade = empresaVO.getCidade();
                    }


                    if (objPais == null || UteisValidacao.emptyNumber(objPais.getCodigo()) ||
                            objEstado == null || UteisValidacao.emptyNumber(objEstado.getCodigo()) ||
                            objCidade == null || UteisValidacao.emptyNumber(objCidade.getCodigo())) {
                        continue;
                    }


                    jsonAlteracoes.put("paisNovo", objPais.getCodigo());
                    jsonAlteracoes.put("estadoNovo", objEstado.getCodigo());
                    jsonAlteracoes.put("cidadeNovo", objCidade.getCodigo());

                    try (PreparedStatement stm = con.prepareStatement("update pessoa set pais = ?, estado = ?, cidade = ? where codigo = ?")) {
                        stm.setInt(1, objPais.getCodigo());
                        stm.setInt(2, objEstado.getCodigo());
                        stm.setInt(3, objCidade.getCodigo());
                        stm.setInt(4, pessoa);
                        stm.execute();
                    }

                    gerarLogAlteracaoCidade(jsonAlteracoes, pessoa, usuarioVO);

                    con.commit();
                } catch (Exception ex) {
                    ex.printStackTrace();
                    con.rollback();
                    con.setAutoCommit(true);
                } finally {
                    con.setAutoCommit(true);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            cepDAO = null;
            cidadeDAO = null;
            System.out.println( "### Fim ProcessoPovoarCidade");
        }
    }

    private void gerarLogAlteracaoCidade(JSONObject json, Integer pessoa, UsuarioVO usuarioVO) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("PESSOA");
            log.setNomeEntidadeDescricao("PESSOA");
            log.setDescricao("PROCESSO-ALTERAR-CIDADE");
            log.setChavePrimaria(pessoa.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("PROCESSO-ALTERAR-CIDADE");
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setPessoa(pessoa);
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(json.toString());
            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println( "Erro ao gerarLogAlteracaoCidade " + ex.getMessage());
            throw ex;
        }
    }
}

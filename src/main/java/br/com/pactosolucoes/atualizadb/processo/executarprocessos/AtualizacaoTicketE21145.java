package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luiz Felipe",
        data = "26/06/2024",
        descricao = "Integração Hubspot",
        motivacao = "Integração Hubspot")
public class AtualizacaoTicketE21145 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE lead ADD COLUMN dados text;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE lead ADD COLUMN dataregistro TIMESTAMP WITHOUT TIME ZONE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaoempresahubspot ADD COLUMN appid character varying (30);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaoempresahubspot ADD COLUMN token text;", c);
        }
    }
}

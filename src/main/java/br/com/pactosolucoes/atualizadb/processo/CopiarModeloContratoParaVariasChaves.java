package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.plano.PlanoTextoPadrao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class CopiarModeloContratoParaVariasChaves {


    public static void main(String... args) throws Exception {

        String urlsBancoOrigem = "*****************************************************************************";

        String[] urlsBancos = new String[]{
                "*********************************************************************",
                "*****************************************************************************",
//                "*****************************************************************************",
//                "***************************************************************************",
//                "*********************************************************************",
//                "*********************************************************************shopping",
//                "************************************************************************",
                "************************************************************************",
                "**************************************************************************",
                "*************************************************************************",
                "**************************************************************************",
                "***********************************************************************",
                "*********************************************************************",
                "**********************************************************************",
                "*********************************************************************",
                "***************************************************************************",
                "*************************************************************************",
                "**********************************************************************",
                "*******************************************************************************",
                "*****************************************************************************",
                "****************************************************************************",
                "*************************************************************************",
                "***********************************************************************",
                "********************************************************************",
                "************************************************************************",
                "*************************************************************************",
                "**************************************************************************",
                "**********************************************************************",
                "*******************************************************************************",
                "**************************************************************************",
                "***********************************************************************"
        };

        incluirPlanoTextoPadrao(urlsBancoOrigem, 30, urlsBancos);
        incluirPlanoTextoPadrao(urlsBancoOrigem, 29, urlsBancos);
        incluirPlanoTextoPadrao(urlsBancoOrigem, 28, urlsBancos);
        incluirPlanoTextoPadrao(urlsBancoOrigem, 27, urlsBancos);
        incluirPlanoTextoPadrao(urlsBancoOrigem, 26, urlsBancos);
//        atualizarPlanosDuracaoPorDescricaoPlano(urlsBancoOrigem, descricaoPlanoOrigem, urlsBancos);
//        atualizarValorModalidadePorNome(urlsBancoOrigem, nomeModalidade, urlsBancos);
//        incluirModeloMensagemCRM(urlsBancoOrigem, codigoModeloMensagem, urlsBancos);
//        incluirPlano(urlsBancoOrigem, codigoPlano, urlsBancos);
//        inativarPlano(urlsBancoOrigem, codigoPlano, urlsBancos);
    }

    private static void incluirPlanoTextoPadrao(String urlBancoOrigem, Integer codigoPlanoTextoPadrao, String[] bancosDestino) throws Exception {
        Connection conOrigem = DriverManager.getConnection(urlBancoOrigem, "postgres", "pactodb");
        PlanoTextoPadrao planoTextoPadraoDAO = new PlanoTextoPadrao(conOrigem);
        PlanoTextoPadraoVO planoTextoPadraoVO = planoTextoPadraoDAO.consultarPorChavePrimaria(codigoPlanoTextoPadrao, Uteis.NIVELMONTARDADOS_TODOS);
        planoTextoPadraoDAO = null;

        for (String urlBanco : bancosDestino) {
            try (Connection con = DriverManager.getConnection(urlBanco, "postgres", "pactodb")) {
                PlanoTextoPadrao planoTextoPadraoDAODestino = new PlanoTextoPadrao(con);
                planoTextoPadraoDAODestino.incluir(planoTextoPadraoVO);
                planoTextoPadraoDAODestino = null;
                System.out.println("PlanoTextoPadrao inserido na url " + urlBanco);
            }
        }
    }

    private static void ajustarSequenciasPostgres(String[] bancosDestino) throws Exception {
        String obterComandos = "SELECT 'SELECT setval(''' || n.nspname || '.' ||\n" +
                "replace(replace(replace(replace(replace(replace(replace(replace(a.adsrc,\n" +
                "'(',''),')',''),'::',''),'textregclass',''),'nextval',''),'regclass',''),'''',''),\n" +
                "n.nspname||'.','')||''',(SELECT coalesce(MAX('||ab.attname||'),1) FROM '||\n" +
                "n.nspname|| '.'||c.relname||'),true);' as seqname\n" +
                "FROM pg_class c\n" +
                "JOIN pg_attrdef a ON c.oid=a.adrelid\n" +
                "JOIN pg_namespace n ON c.relnamespace = n.oid AND n.nspname NOT LIKE 'pg_%'\n" +
                "JOIN pg_index i ON i.indrelid=c.oid AND i.indisprimary='t'\n" +
                "JOIN pg_attribute ab ON ab.attrelid=c.oid AND ab.attisdropped='f' AND ab.atthasdef='t' AND i.indkey[0]=ab.attnum AND i.indkey[1] IS NULL\n" +
                "Where a.adsrc like 'nextval%';";
        for (String urlBanco : bancosDestino) {
            try (Connection con = DriverManager.getConnection(urlBanco, "postgres", "pactodb")) {
                Statement stm = con.createStatement();
                ResultSet rs = stm.executeQuery(obterComandos);
                List<String> updateNextVal = new ArrayList<>();
                while (rs.next()) {
                    updateNextVal.add(rs.getString("seqname"));
                }

                for (String sql : updateNextVal) {
                    stm = con.createStatement();
                    stm.execute(sql);
                }
            }
        }

    }
}

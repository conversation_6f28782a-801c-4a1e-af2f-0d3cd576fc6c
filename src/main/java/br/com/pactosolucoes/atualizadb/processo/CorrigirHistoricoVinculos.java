package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.HistoricoVinculoVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.HistoricoVinculo;
import negocio.facade.jdbc.basico.Vinculo;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.basico.ConfiguracaoSistemaControle;
import java.util.Date;


public class CorrigirHistoricoVinculos {
	
	private Connection con;
	public CorrigirHistoricoVinculos() throws Exception {
	}
	
	public CorrigirHistoricoVinculos(Connection con1) throws Exception {
		con = con1;
	}

	
	public Map<Integer, List<String>> montarClientesColaboradores(boolean duplicacao, boolean ticketE21356) throws SQLException, Exception{
		Map<Integer, List<String>> clientesColaboradores = new HashMap<Integer, List<String>>();
		ResultSet consulta;
		String sql = "SELECT colaborador,tipocolaborador FROM historicovinculo WHERE cliente = XX GROUP BY colaborador,tipocolaborador;";
		List<Integer> clientes = new ArrayList<>();
		if (ticketE21356) {
			clientes = getCodigosClientesCorrecaoTicketE21356();
		} else {
			clientes = getCodigosClientes(duplicacao);
		}
		//aqui, para cada cliente eu vou montar uma lista com colaboradores que tem ou já tiveram algum vínculo com o cliente e colacar no mapa
		for(Integer cliente : clientes){
			consulta = SuperFacadeJDBC.criarConsulta(sql.replaceAll("XX", cliente.toString()), con);
			List<String> colaboradores = new ArrayList<String>();
			while(consulta.next()){
				colaboradores.add(consulta.getInt("colaborador")+"-"+consulta.getString("tipocolaborador"));
			}
			//se o cliente possui um vínculo ao menos, incluí-lo no mapa
			if(!colaboradores.isEmpty())
				clientesColaboradores.put(cliente, colaboradores);
		}
		return clientesColaboradores;
	}

	private List<Integer> getCodigosClientesCorrecaoTicketE21356() throws Exception {
		String sql = "select distinct cliente from backhistoricovinculo";
		ResultSet set = SuperFacadeJDBC.criarConsulta(sql, con);
		List<Integer> codigos = new ArrayList<Integer>();
		while(set.next()){
			codigos.add(set.getInt("cliente"));
		}
		return codigos;
	}
	
	/** 
	 * <AUTHOR>
	 * 04/11/2011
	 */
	private List<Integer> getCodigosClientes(boolean duplicacao) throws Exception{
            String sql = "";
            if(duplicacao){
                 sql = "select cliente as codigo from vinculo where tipovinculo = 'CO' or tipovinculo = 'TW' GROUP by cliente,tipovinculo having  count(cliente) > 1";
            } else {
                 sql = "SELECT codigo FROM cliente";
            }
		ResultSet set = SuperFacadeJDBC.criarConsulta(sql, con);
		List<Integer> codigos = new ArrayList<Integer>();
		while(set.next()){
			codigos.add(set.getInt("codigo"));
		}
		return codigos;
	}
	
	public List<String> getColaboradoresVinculosAtuais(Integer cliente) throws SQLException, Exception{
		ResultSet consulta = SuperFacadeJDBC.criarConsulta("SELECT colaborador,tipovinculo FROM vinculo WHERE cliente = "+cliente,con);
		List<String> vinculosAtuais = new ArrayList<String>();
		while(consulta.next()){
			vinculosAtuais.add(consulta.getInt("colaborador")+"-"+consulta.getString("tipovinculo"));
		}
		return vinculosAtuais;
		
	}
	
    public void processarCorrecao(boolean duplicacao, boolean ticketE21356) throws Exception {

        ConfiguracaoSistemaControle config = null;
		if (!ticketE21356) {
			config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
		}
        Map<Integer, List<String>> mapaClientesColaboradores = montarClientesColaboradores(duplicacao, ticketE21356);
        Set<Integer> keySet = mapaClientesColaboradores.keySet();
        String sql = "SELECT * FROM historicovinculo WHERE cliente = %s AND colaborador = %s and  tipocolaborador = '%s'  ORDER BY dataregistro;";
        HistoricoVinculo hvDao = new HistoricoVinculo(con);
        if (config != null) {
            config.setTotalAProcessarHistoricoVinculo(keySet.size());
        } else {
            System.out.println("\n####Quantidade de alunos a  processar " + keySet.size()+" ####");
        }
        String sqlDeletarVinculosDuplicados = "DELETE FROM vinculo\n"
                + "WHERE codigo IN (SELECT\n"
                + "                  min(codigo) AS codigo\n"
                + "                 FROM vinculo\n"
                + "                 WHERE tipovinculo = 'CO' or tipovinculo = 'TW'\n"
                + "                 GROUP BY cliente,tipovinculo\n"
                + "                 HAVING count(cliente) > 1);";
        SuperFacadeJDBC.executarConsulta(sqlDeletarVinculosDuplicados, con);
        int count = 0;
        int perc = 1;
        int porcao = keySet.size()/10; 
        for (Integer cliente : keySet) {

            List<String> vinculosAtuais = getColaboradoresVinculosAtuais(cliente);

            //verificar se o vinculo atual tem historico
            for (String colaborador : vinculosAtuais) {
                if (!mapaClientesColaboradores.get(cliente).contains(colaborador)) {
                    criarHistorico(cliente, colaborador, hvDao);
                }

            }

            for (String colaborador : mapaClientesColaboradores.get(cliente)) {
                String[] split = colaborador.split("-");
                Integer codigoColaborador = Integer.parseInt(split[0]);
                String tipoVinculo = split[1];
                ResultSet resultSet = SuperFacadeJDBC.criarConsulta(String.format(sql,
                        new Object[]{cliente.toString(), codigoColaborador, tipoVinculo}), con);
                List<HistoricoVinculoVO> historicos = HistoricoVinculo.montarDadosConsulta(
                        resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                boolean temVinculoAtual = vinculosAtuais.contains(colaborador);

                            // aqui devo aplicar a inteligencia da correção
                // primeira regra: se esse colaborador tem apenas um historico,
                // e não estiver na lista de vinculos atuais, excluir. se
                // estiver, deve ser de entrada
                if (historicos.size() == 1) {
                    HistoricoVinculoVO historico = historicos.get(0);
                    // tem vinculo atual?
                    if (temVinculoAtual) {
                        // se sim, único histórico é de saída?
                        if (historico.getTipoHistoricoVinculo().equals("SD")) {
                            // então alterar para entrada
                            historico.setTipoHistoricoVinculo("EN");
                            historico.setOrigem("PROCESSO");
                            hvDao.alterar(historico);
                        }
                    } else {
                        // não tem vinculo atual, excluir
                        hvDao.excluir(historico);
                    }
                    continue;
                }

                if (historicos.size() >= 1) {
                    boolean par = historicos.size() % 2 == 0;
                    Ordenacao.ordenarLista(historicos, "dataRegistro");
                    Collections.reverse(historicos);
                    HistoricoVinculoVO historico = historicos.get(0);
                    boolean ultimoVinculoEntrada = historico.getTipoHistoricoVinculo().equals("EN");
                                    // segunda regra: se tem mais de um histórico e possui
                    // vínculo atual com o cliente,
                    // o número de históricos deve ser ímpar e o último deve ser
                    // de entrada
                    if (temVinculoAtual) {
                        if (par) {
                            novoHistorico(historico, "EN", ultimoVinculoEntrada, historicos, hvDao);
                        }
                        atualizarTipoHistorico(true, historicos, hvDao);
                    } // terceira regra: se tem mais de um histórico e não possui
                    // vínculo atual com o cliente, o número de históricos deve
                    // ser par
                    // e o último deve ser de saída
                    else {
                        if (!par) {
                            novoHistorico(historico, ultimoVinculoEntrada ? "SD" : "EN",
                                    !ultimoVinculoEntrada, historicos, hvDao);
                        }
                        atualizarTipoHistorico(false, historicos, hvDao);
                    }

                }
                if (config != null) {
                    config.setTotalProcessadosHistoricoVinculo(config.getTotalProcessadosHistoricoVinculo() + 1);
                }

            }
            count++;
            if(count == (porcao*perc)){
                System.out.println("\n####"+perc*10+ "% dos alunos foram processados  ####");
                perc++;
            }
            
        }
    }
	
	/**
	 * Joao Alcides
	 * 26/06/2012
	 * aqui eu crio um novo histórico de vinculo para preencher uma falta que foi gerada por erro do sistema. ele pode
	 * ser inserido como o primeiro ou último histórico desse cliente para o colaborador.
	 */
	private void novoHistorico(HistoricoVinculoVO historico, String tipo, 
				boolean adicionarNoFim, List<HistoricoVinculoVO> historicos, HistoricoVinculo hvDao) throws Exception{
		HistoricoVinculoVO novoHistorico = new HistoricoVinculoVO();
		novoHistorico.setColaborador(historico.getColaborador());
		novoHistorico.setCliente(historico.getCliente());
		novoHistorico.setTipoColaborador(historico.getTipoColaborador());
		novoHistorico.setTipoHistoricoVinculo(tipo);
		novoHistorico.setOrigem("PROCESSO");
		//quando for pra adicionar no fim da lista, significa que a data de registro deste novo historico será um minuto antes do primeiro histórico em banco
		if(adicionarNoFim){
			novoHistorico.setDataRegistro(Uteis.somarCampoData(historicos.get(historicos.size()-1).getDataRegistro(), Calendar.MINUTE, -1));
		}else{
			//neste caso, a data de registro deste novo historico será um minuto depois do último histórico em banco	
			novoHistorico.setDataRegistro(Uteis.somarCampoData(historico.getDataRegistro(), Calendar.MINUTE, 1));
		}
		//adicionar esse novo histórico
		hvDao.incluir(novoHistorico);
		historicos.add(novoHistorico);
		//reordenar a lista
		Ordenacao.ordenarLista(historicos, "dataRegistro");
		Collections.reverse(historicos);
	}
	
	private void atualizarTipoHistorico(boolean entrada, List<HistoricoVinculoVO> historicos, HistoricoVinculo hvDao) throws Exception{
		for(HistoricoVinculoVO historico : historicos){
			if(entrada && historico.getTipoHistoricoVinculo().equals("SD")){
				historico.setTipoHistoricoVinculo("EN");
				historico.setOrigem("PROCESSO");
				hvDao.alterar(historico);
			}
			if(!entrada && historico.getTipoHistoricoVinculo().equals("EN")){
				historico.setTipoHistoricoVinculo("SD");
				historico.setOrigem("PROCESSO");
				hvDao.alterar(historico);
			}
			entrada = !entrada;
		}
	}
	
	/**
	 * <AUTHOR>
	 * 04/11/2011
	 * 
	 */
	public void executarProcesso(boolean duplicacao){
		try {
                    System.out.println("\n########## corrigir Vinculo  - início em : " + new Date()+" ####"+con.getCatalog()+" ###########");
                    processarCorrecao(duplicacao, false);
                    System.out.println("\n########## corrigir Vinculo  - fim em : " + new Date()+" ####"+con.getCatalog()+" ###########");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	

	public void criarHistorico(Integer cliente, String colaborador, HistoricoVinculo hvDao) throws SQLException, Exception{
                String[] split = colaborador.split("-");
                Integer codigoColaborador = Integer.parseInt(split[0]);
                String tipoVinculo = split[1];
		ResultSet criarConsulta = SuperFacadeJDBC.criarConsulta("SELECT * FROM vinculo WHERE cliente = "+cliente
				+" AND colaborador = "+codigoColaborador+" and tipovinculo = '"+tipoVinculo+"'", con);
		criarConsulta.next();
		VinculoVO vinculoVO = Vinculo.montarDados(criarConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
		HistoricoVinculoVO novoHistorico = new HistoricoVinculoVO();
		novoHistorico.setCliente(vinculoVO.getCliente());
		novoHistorico.setColaborador(vinculoVO.getColaborador());
		novoHistorico.setTipoColaborador(vinculoVO.getTipoVinculo());
		novoHistorico.setTipoHistoricoVinculo("EN");
		novoHistorico.setOrigem("PROCESSO");
		if(vinculoVO.getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla()) || vinculoVO.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla())){
			ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT dataregistro FROM historicovinculo WHERE cliente = "+cliente
					+" AND tipocolaborador LIKE '"+vinculoVO.getTipoVinculo()+"' ORDER BY codigo DESC ", con);
			if(resultSet.next()){
				novoHistorico.setDataRegistro(Uteis.somarCampoData(resultSet.getTimestamp("dataregistro"), Calendar.MINUTE, 1));
			}else{
				novoHistorico.setDataRegistro(Calendario.hoje());
			}
		}else{
			novoHistorico.setDataRegistro(Calendario.hoje());
		}
		hvDao.incluir(novoHistorico);
	}
	
	public static void main(String[] args) {
		try {
//			Connection con1 = DriverManager.getConnection("***********************************************", "postgres", "pactodb");
			Connection con1 = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "");
//			corrigirDatasIguais(con1);
			 CorrigirHistoricoVinculos corrigirHistoricoVinculos = new CorrigirHistoricoVinculos(con1);
		     corrigirHistoricoVinculos.executarProcesso(false);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

         public static void ajustarVinculosComDuplicacao(Connection con1) throws Exception {
             CorrigirHistoricoVinculos corrigirHistoricoVinculos = new CorrigirHistoricoVinculos(con1);
             corrigirHistoricoVinculos.executarProcesso(true);
         }
         public static void ajustarVinculos(Connection con1) throws Exception {
             CorrigirHistoricoVinculos corrigirHistoricoVinculos = new CorrigirHistoricoVinculos(con1);
             corrigirHistoricoVinculos.executarProcesso(false);
         }
	
	public static void corrigirDatasIguais(Connection con) throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append(" select ZZZ from historicovinculo hv ");
		sql.append(" where exists(select * from historicovinculo his where his.dataregistro = hv.dataregistro "); 
		sql.append(" and his.tipocolaborador = hv.tipocolaborador and hv.cliente = his.cliente  ");
		sql.append(" and his.tipohistoricovinculo = 'SD') AND hv.tipohistoricovinculo = 'EN' ");
		
		Integer totalRegs = 0;
		ResultSet total = SuperFacadeJDBC.criarConsulta(sql.toString().replaceFirst("ZZZ", "count(*) as total "), con);
		if(total.next()){
			totalRegs = total.getInt("total");
		}
		int nr = 1;
		
		ResultSet resultSet = SuperFacadeJDBC.criarConsulta(sql.toString().replaceFirst("ZZZ", "*"), con);
		
		while(resultSet.next()){
			System.out.println(nr+++"/"+totalRegs);
			HistoricoVinculoVO historicoVinculoVO = HistoricoVinculo.montarDados(resultSet, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
			PreparedStatement stm = con.prepareStatement("SELECT * FROM historicovinculo where dataregistro = ? and tipohistoricovinculo = 'SD' AND cliente = ? ");
			stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(historicoVinculoVO.getDataRegistro()));
			stm.setInt(2, historicoVinculoVO.getCliente().getCodigo());
			ResultSet queryVinculoIgual = stm.executeQuery();
			
			if(queryVinculoIgual.next()){
				HistoricoVinculoVO historicoVinculoIgualVO = HistoricoVinculo.montarDados(queryVinculoIgual, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
				StringBuilder verificacao = new StringBuilder();
				verificacao.append("SELECT (select count(*) from historicovinculo  where tipohistoricovinculo = 'EN' "); 
				verificacao.append("	    and cliente = "+historicoVinculoIgualVO.getCliente().getCodigo()+" and tipocolaborador = 'CO' and dataregistro < '"+Uteis.getDataJDBCTimestamp(historicoVinculoIgualVO.getDataRegistro())+"') as nrentradas, ");
				verificacao.append(" (select count(*) from historicovinculo  where tipohistoricovinculo = 'SD'  ");
				verificacao.append("        and cliente = "+historicoVinculoIgualVO.getCliente().getCodigo()+" and tipocolaborador = 'CO' and dataregistro < '"+Uteis.getDataJDBCTimestamp(historicoVinculoIgualVO.getDataRegistro())+"') as nrsaidas ");
				ResultSet verificacaoConsulta = SuperFacadeJDBC.criarConsulta(verificacao.toString(), con);
				if(verificacaoConsulta.next()){
					if(verificacaoConsulta.getInt("nrentradas") > verificacaoConsulta.getInt("nrsaidas")){
						historicoVinculoIgualVO.setDataRegistro(Uteis.somarCampoData(historicoVinculoIgualVO.getDataRegistro(), Calendar.SECOND, -1));
                                                historicoVinculoVO.setDataRegistro(Uteis.somarCampoData(historicoVinculoVO.getDataRegistro(), Calendar.SECOND, 1));
					}else{
						historicoVinculoIgualVO.setDataRegistro(Uteis.somarCampoData(historicoVinculoIgualVO.getDataRegistro(), Calendar.SECOND, 1));
                                                historicoVinculoVO.setDataRegistro(Uteis.somarCampoData(historicoVinculoVO.getDataRegistro(), Calendar.SECOND, -1));
					}
					SuperFacadeJDBC.executarConsulta("UPDATE historicovinculo SET dataregistro = '"
							+Uteis.getDataJDBCTimestamp(historicoVinculoIgualVO.getDataRegistro())+
							"' WHERE codigo = "+historicoVinculoIgualVO.getCodigo(), con);

                                        SuperFacadeJDBC.executarConsulta("UPDATE historicovinculo SET dataregistro = '"
							+Uteis.getDataJDBCTimestamp(historicoVinculoVO.getDataRegistro())+
							"' WHERE codigo = "+historicoVinculoVO.getCodigo(), con);
				}
			}
		}
		
	}

    private List<String> getTiposDeVinculos(Integer cliente, Integer colaborador) throws Exception {
        List<String> tipos = new ArrayList<String>();
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select distinct(tipocolaborador) as tipo from historicovinculo  where cliente = "+ cliente +" and colaborador = " + colaborador , con);
        while(resultSet.next()){
            tipos.add(resultSet.getString("tipo"));
        }
        return tipos;
    }
	
}

package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.Banco;
import negocio.facade.jdbc.financeiro.CartaoCredito;
import negocio.facade.jdbc.financeiro.Cheque;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class CorrigirFormaPagamentoKorpus {

    private Connection connection;

    public CorrigirFormaPagamentoKorpus(Connection connection) {
        this.connection = connection;
    }

    public static void main(String[] args) {
        try {
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "bdzillyonkorpusunidbessa-2018-09-17");
            Conexao.guardarConexaoForJ2SE(c);

            final int codRecibo = 39848;
            final TipoFormaPagto tipoFormaPagto = TipoFormaPagto.CARTAOCREDITO;
            final int nrVezes = 12;
            final Date dataInicial = Calendario.getDate("dd/MM/yyyy", "15/03/2018");

            CorrigirFormaPagamentoKorpus corrigirFormaPagamentoKorpus = new CorrigirFormaPagamentoKorpus(c);
            corrigirFormaPagamentoKorpus.corrigirRecibo(codRecibo, tipoFormaPagto, nrVezes, dataInicial);
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void corrigirRecibo(int codRecibo, TipoFormaPagto tipoFormaPagto, int nrVezes, Date dataInicial) throws SQLException {
        try {
            connection.setAutoCommit(false);
            if (!tipoFormaPagto.equals(TipoFormaPagto.CARTAOCREDITO)
                    && !tipoFormaPagto.equals(TipoFormaPagto.CHEQUE)) {
                throw new ConsistirException("Processo permitido apenas para as formas de pagamento: 'Cheque' ou 'Cartão de Crédito'");
            }

            ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(connection);
            ReciboPagamentoVO reciboPagamentoVO = reciboPagamentoDAO.consultarPorCodigo(codRecibo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            atualizarMovPagamento(reciboPagamentoVO, tipoFormaPagto, dataInicial);
            incluirChequeCartaoCredito(tipoFormaPagto, reciboPagamentoVO, nrVezes, dataInicial);

            connection.commit();
        } catch (Exception ex) {
            Uteis.logar(ex, CorrigirFormaPagamentoKorpus.class);
            connection.rollback();
        } finally {
            connection.setAutoCommit(true);
        }
    }

    private void atualizarMovPagamento(ReciboPagamentoVO reciboPagamentoVO, TipoFormaPagto tipoFormaPagto, Date dataInicial) throws Exception {
        FormaPagamento formaPagamento = new FormaPagamento(connection);
        FormaPagamentoVO formaPagamentoVO = formaPagamento.consultarPorTipoFormaPagamentoAtiva(tipoFormaPagto.getSigla(), false, Uteis.NIVELMONTARDADOS_TODOS);

        String sql = "UPDATE movpagamento SET formapagamento = ?, datalancamento = ?, datapagamento = ?, dataquitacao = ? WHERE recibopagamento = ?";
        PreparedStatement ps = connection.prepareStatement(sql);
        ps.setInt(1, formaPagamentoVO.getCodigo());
        ps.setDate(2, Uteis.getDataJDBC(dataInicial));
        ps.setDate(3, Uteis.getDataJDBC(dataInicial));
        ps.setDate(4, Uteis.getDataJDBC(dataInicial));
        ps.setInt(5, reciboPagamentoVO.getCodigo());
        ps.execute();
        if (!ps.isClosed()) {
            ps.close();
        }
    }


    private void incluirChequeCartaoCredito(TipoFormaPagto tipoFormaPagto, ReciboPagamentoVO reciboPagamentoVO, int nrVezes, Date dataInicial) throws Exception {
        if (nrVezes < 1) {
            throw new ConsistirException("Processo exige que o pagamento seja em pelo menos 1x");
        }

        MovPagamento movPagamentoDAO = new MovPagamento(connection);
        List<MovPagamentoVO> listaMovPagamentos = movPagamentoDAO.consultarPorCodigoRecibo(reciboPagamentoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaMovPagamentos.size() != 1) {
            throw new ConsistirException("Processo não suporta mais de um pagamento por recibo");
        }

        MovPagamentoVO pagamentoVO = listaMovPagamentos.get(0);

        Cheque chequeDAO = new Cheque(connection);
        CartaoCredito cartaoCreditoDAO = new CartaoCredito(connection);
        OperadoraCartao operadoraCartaoDAO = new OperadoraCartao(connection);
        Banco bancoDAO = new Banco(connection);

        List<OperadoraCartaoVO> listaOperadoraImportacao = operadoraCartaoDAO.consultarPorCodigoOperadora(99999, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaOperadoraImportacao.size() != 1) {
            throw new ConsistirException("Processo não suporta mais de uma operadora de código 99999");
        }

        BancoVO bancoVO = bancoDAO.consultarCodigoBanco(0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        for (int i = 0; i < nrVezes; i++) {
            Date dataCompensacao = Uteis.somarDias(dataInicial, 30 * i);
            if (tipoFormaPagto.equals(TipoFormaPagto.CHEQUE)) {
                ChequeVO chequeVO = new ChequeVO();
                String dataInicialTexto = Uteis.getData(dataInicial, "ddMMyyyy");
                String dataCompensacaoTexto = Uteis.getData(dataCompensacao, "ddMMyyyy");
                chequeVO.setVistaOuPrazo(dataInicialTexto.equals(dataCompensacaoTexto) ? "AV" : "PR");
                chequeVO.setDataCompensacao(Uteis.obterProximoDiaUtil(dataCompensacao, new ArrayList<Date>()));
                chequeVO.setValor(pagamentoVO.getValorTotal() / nrVezes);
                chequeVO.setAgencia(pagamentoVO.getCodigo().toString());
                chequeVO.setConta(pagamentoVO.getCodigo().toString());
                chequeVO.setNumero("0");
                chequeVO.setMovPagamento(pagamentoVO.getCodigo());
                chequeVO.setSituacao("EA");
                chequeVO.setBanco(bancoVO);
                chequeVO.setValorTotal(pagamentoVO.getValorTotal() / nrVezes);

                chequeDAO.incluir(chequeVO);
                atualizarSomenteNumeroCheque(chequeVO);
            } else if (tipoFormaPagto.equals(TipoFormaPagto.CARTAOCREDITO)) {
                CartaoCreditoVO cartaoCreditoVO = new CartaoCreditoVO();
                cartaoCreditoVO.setDataCompensacao(Uteis.obterProximoDiaUtil(dataCompensacao, new ArrayList<Date>()));
                cartaoCreditoVO.setMovpagamento(pagamentoVO);
                cartaoCreditoVO.setValor(pagamentoVO.getValorTotal() / nrVezes);
                cartaoCreditoVO.setOperadora(listaOperadoraImportacao.get(0));
                cartaoCreditoVO.setSituacao("EA");
                cartaoCreditoVO.setValorTotal(pagamentoVO.getValorTotal() / nrVezes);

                cartaoCreditoDAO.incluir(cartaoCreditoVO);
            }
        }

        ProdutosPagosServico produtosPagosServico = new ProdutosPagosServico();
        produtosPagosServico.setarProdutosPagos(connection, reciboPagamentoVO.getCodigo());

        chequeDAO = null;
        cartaoCreditoDAO = null;
    }

    private void atualizarSomenteNumeroCheque(ChequeVO chequeVO) throws SQLException {
        String sql = "UPDATE cheque SET numero = ? WHERE codigo = ?";
        PreparedStatement ps = connection.prepareStatement(sql);
        ps.setString(1, chequeVO.getCodigo().toString());
        ps.setInt(2, chequeVO.getCodigo());
        ps.execute();
        if (!ps.isClosed()) {
            ps.close();
        }
    }


    public Connection getConnection() {
        return connection;
    }

    public void setConnection(Connection connection) {
        this.connection = connection;
    }
}

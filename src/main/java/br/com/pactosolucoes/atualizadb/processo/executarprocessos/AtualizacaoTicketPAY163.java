package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Hibrael A Alves",
        data = "20/01/2025",
        descricao = "PAY-163 - ACAD LIVE - HOTSITE BENEFICIOS",
        motivacao = "PAY-163 - ACAD LIVE - HOTSITE BENEFICIOS")
public class AtualizacaoTicketPAY163 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planositevendasonline ADD COLUMN lstBeneficios text;", c);

            StringBuilder sqlMigracaoBeneficios = new StringBuilder();
            sqlMigracaoBeneficios.append("UPDATE planositevendasonline \n");
            sqlMigracaoBeneficios.append("SET lstbeneficios = \n");
            sqlMigracaoBeneficios.append("CASE \n");
            sqlMigracaoBeneficios.append("WHEN COALESCE(beneficio01, '') = '' AND COALESCE(beneficio02, '') = '' AND COALESCE(beneficio03, '') = '' \n");
            sqlMigracaoBeneficios.append("THEN NULL \n");
            sqlMigracaoBeneficios.append("ELSE CONCAT_WS('#', NULLIF(beneficio01, ''), NULLIF(beneficio02, ''), NULLIF(beneficio03, ''))\n");
            sqlMigracaoBeneficios.append("END;\n");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlMigracaoBeneficios.toString(), c);

        }
    }

}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "15/08/2024",
        descricao = "Criar novas colunas publicId MGB para tabelas ambiente, colaborador e horarioTurma ",
        motivacao = "Novas integrações MGB TW-652"
)
public class CriarNovasColunasPublicIdMgb implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table ambiente add column codigoPiscinaMgb varchar;", c);
        }

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table horarioTurma add column publicIdTurmaMgb varchar;", c);
        }

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table colaborador add column publicIdProfessorMgb varchar;", c);
        }
    }

}

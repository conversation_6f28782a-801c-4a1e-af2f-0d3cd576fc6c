/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
/**
 *
 * <AUTHOR>
 */
public class AjustarMatriculasDuplicadas {
public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("*************************************************", "postgres", "pactodb");
            ajustarMatriculas(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarMatriculas(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select codigomatricula, count(codigomatricula),max(codigo) as cliente from cliente group by codigomatricula having count(codigomatricula) > 1 ", con);
         ResultSet matriculaAtual = null;
        int cont = 0;
        String matricula= "";
        String mascaraMatricula = "XXXXXX";
        while (consulta.next()) {
            matriculaAtual = SuperFacadeJDBC.criarConsulta("select * from numeromatricula  ", con);
            matriculaAtual.next();
            int matriculaUsada =  matriculaAtual.getInt("matricula") + 1;
            SuperFacadeJDBC.executarConsultaUpdate("UPDATE numeromatricula  SET matricula = "+ matriculaUsada , con);
            System.out.println(++cont + " - cliente " + consulta.getInt("cliente") + " teve a matricula alterada de  " + consulta.getInt("codigomatricula") +" para "+matriculaUsada);
            matricula = Uteis.getMontarMatricula(String.valueOf(matriculaUsada), mascaraMatricula.length());

            String sql = "update cliente  set codigomatricula  = ?, matricula  = ? where codigo = ?;";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);

            sqlAlterar.setInt(1, matriculaUsada);
            sqlAlterar.setString(2, matricula);
            sqlAlterar.setInt(3, consulta.getInt("cliente"));
            sqlAlterar.execute();

            String sqlSintetico = "update situacaoclientesinteticodw  set matricula  =? where  codigocliente  = ?;";

            sqlAlterar = con.prepareStatement(sqlSintetico);

            sqlAlterar.setInt(1, matriculaUsada);
            sqlAlterar.setInt(2, consulta.getInt("cliente"));
            sqlAlterar.execute();
        }
    }
    
    public static void reiniciarMatriculas(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select codigomatricula,codigo as cliente from cliente order by codigo", con);
         ResultSet matriculaAtual = null;
        int cont = 0;
        String matricula= "";
        String mascaraMatricula = "XXXXXX";
        while (consulta.next()) {
            matriculaAtual = SuperFacadeJDBC.criarConsulta("select * from numeromatricula  ", con);
            matriculaAtual.next();
            int matriculaUsada =  matriculaAtual.getInt("matricula") + 1;
            SuperFacadeJDBC.executarConsultaUpdate("UPDATE numeromatricula  SET matricula = "+ matriculaUsada , con);
            System.out.println(++cont + " - cliente " + consulta.getInt("cliente") + " teve a matricula alterada de  " + consulta.getInt("codigomatricula") +" para "+matriculaUsada);
            matricula = Uteis.getMontarMatricula(String.valueOf(matriculaUsada), mascaraMatricula.length());

            String sql = "update cliente  set codigomatricula  = ?, matricula  = ? where codigo = ?;";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);

            sqlAlterar.setInt(1, matriculaUsada);
            sqlAlterar.setString(2, matricula);
            sqlAlterar.setInt(3, consulta.getInt("cliente"));
            sqlAlterar.execute();

            String sqlSintetico = "update situacaoclientesinteticodw  set matricula  =? where  codigocliente  = ?;";

            sqlAlterar = con.prepareStatement(sqlSintetico);

            sqlAlterar.setInt(1, matriculaUsada);
            sqlAlterar.setInt(2, consulta.getInt("cliente"));
            sqlAlterar.execute();
        }
    }
}

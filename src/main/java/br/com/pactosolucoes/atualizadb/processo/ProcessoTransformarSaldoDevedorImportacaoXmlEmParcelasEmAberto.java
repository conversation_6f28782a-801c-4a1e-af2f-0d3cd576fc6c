package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProcessoTransformarSaldoDevedorImportacaoXmlEmParcelasEmAberto {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";

    public static void main(String... args) throws IOException {
        try {
            /*
            * Esse processo transforma o saldo devedor importado do zillyon desk  em parcelas
            * As parcelas são adicionadas no contrato importado do aluno, e o saldo devedor importado é excluido.
            * Serão ignorados os clientes que tiveram algum lançamento\alteração no saldo da conta corrente ou que tiveram alguma renegociação de parcelas em seus contratos
             */
            Connection con = DriverManager.getConnection("******************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);
            nomeBanco = con.getCatalog();

            Integer codigoEmpresa = 1;
            Boolean somenteContratosAtivos = false;
            String matriculasSeparadasPorVirgula = "";

            transformarSaldoDevedorImportacaoXmlEmParcelasEmAberto(codigoEmpresa, somenteContratosAtivos, matriculasSeparadasPorVirgula, con);
        } catch (Exception ex) {
            adicionarLog(ex.getMessage());
        } finally {
            Uteis.salvarArquivo(nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\PactoJ\\log\\" + File.separator);
        }
    }

    private static void transformarSaldoDevedorImportacaoXmlEmParcelasEmAberto(Integer codigoEmpresa, Boolean somenteContratosAtivos, String matriculasSeparadasPorVirgula, Connection con) throws Exception {
        try {
            MovProdutoParcela movProdutoParcela = new MovProdutoParcela(con);
            MovParcela movParcelaDAO = new MovParcela(con);
            MovProduto movProdutoDAO = new MovProduto(con);

            StringBuilder sql = new StringBuilder();
            sql.append(" select \n");
            sql.append("  cli.codigo as cliente, cli.codigomatricula, cli.pessoa, pes.nome, \n");
            sql.append("  mcc.codigo as mcc, mcc.descricao, mcc.saldoatual,\n");
            sql.append("  con.codigo as contrato, con.id_externo, con.datalancamento, \n");
            sql.append("  mpar.codigo as movparcela, mpar.descricao, round(mpar.valorparcela::numeric) as valorparcela\n");
            sql.append(" from pessoa pes\n");
            sql.append("\tinner join cliente cli on cli.pessoa = pes.codigo\n");
            sql.append("\tinner join movimentocontacorrentecliente mcc on mcc.codigo = (select max(mcc2.codigo) from movimentocontacorrentecliente mcc2 where mcc2.pessoa = pes.codigo)\n");
            sql.append("\tinner join contrato con on con.pessoa = pes.codigo\n");
            sql.append("\tinner join contratoduracao cd on cd.contrato = con.codigo\n");
            sql.append("\tinner join movparcela mpar on mpar.contrato = con.codigo\n");
            sql.append("where 1 = 1\n");
            sql.append("and cli.empresa = " + codigoEmpresa + "\n");
            if (somenteContratosAtivos) {
                sql.append(" and con.situacao = 'AT' \n");
            }
            if (!UteisValidacao.emptyString(matriculasSeparadasPorVirgula) && matriculasSeparadasPorVirgula.split(",").length > 0) {
                sql.append(" and cli.codigomatricula in (").append(matriculasSeparadasPorVirgula).append(") \n");
            }
            sql.append("and mcc.saldoatual < 0\n");
            sql.append("and mcc.descricao ilike '%ORIGINADO DA IMPORTA__O%'\n");
            sql.append("and round((mcc.saldoatual * -1)::numeric) = round(mpar.valorparcela::numeric) \n");
            sql.append("and con.id_externo is not null\n");
            sql.append("and con.empresa = " + codigoEmpresa + "\n");
            sql.append("and mpar.pessoa is null \n");
                sql.append("and mpar.descricao = 'PARCELA 2'\n");
            sql.append("and mpar.situacao = 'PG'\n");
            sql.append("and not exists (select mpar.codigo from movparcela mpar where mpar.contrato = con.codigo and mpar.descricao ilike '%PARCELA RENEGOCIADA%')\n");
            sql.append("and cd.numeromeses = (select count(mpro.codigo) from movproduto mpro inner join produto pro on pro.codigo = mpro.produto where mpro.contrato = con.codigo and pro.tipoproduto = 'PM')\n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            StringBuilder sqlCount = new StringBuilder();
            sqlCount.append("SELECT count(*) FROM (");
            sqlCount.append(sql);
            sqlCount.append(") as sql");
            int total = SuperFacadeJDBC.contar(sqlCount.toString(), con);
            int count = 1;
            while (rs.next()) {
                int codigoPessoa = rs.getInt("pessoa");
                int codigoContrato = rs.getInt("contrato");
                Date dataLancamento = rs.getDate("datalancamento");
                Date dataVencimento = null;

                String msg = count + "\\" + total + " Processando cliente: " + rs.getInt("codigomatricula") + " " + rs.getString("nome");
                adicionarLog(msg);

                Statement stmExcluirMovparcelas = con.createStatement();
                stmExcluirMovparcelas.execute("DELETE FROM movparcela WHERE codigo = " + rs.getInt("movparcela"));

                PagamentoMovParcela.corrigirPagamentosSemMovParcelas(con);

                Statement stmMovprodutos = con.createStatement();
                ResultSet rsMovprodutos = stmMovprodutos.executeQuery("SELECT mpro.* FROM movproduto mpro INNER JOIN produto pro ON pro.codigo = mpro.produto WHERE contrato = " + codigoContrato + " AND pro.tipoproduto = 'PM' ORDER BY codigo");

                Double valorListaProdutoEmAberto = 0.0;
                int qtdParcelas = 0;
                while (rsMovprodutos.next()) {
                    MovProdutoVO movProdutoVO = MovProduto.montarDados(rsMovprodutos, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                    Statement stmExisteMovProdutoParcela = con.createStatement();
                    ResultSet rsExisteMovprodutoParcela = stmExisteMovProdutoParcela.executeQuery("SELECT mpp.codigo FROM movprodutoparcela mpp inner join movproduto mpro on mpro.codigo = mpp.movproduto WHERE round(mpp.valorpago::numeric) = round(mpro.totalfinal::numeric) AND mpro.codigo = " + movProdutoVO.getCodigo());
                    boolean existeMovProdutoParcela = rsExisteMovprodutoParcela.next();
                    if (!existeMovProdutoParcela) {
                        Double valorTotalMovProduto = movProdutoVO.getTotalFinal();
                        for (MovProdutoParcelaVO mpp : movProdutoVO.getMovProdutoParcelaVOs()) {
                            valorTotalMovProduto -= mpp.getValorPago();
                        }
                        valorListaProdutoEmAberto += valorTotalMovProduto;
                        qtdParcelas++;

                        // Data de vencimento da primeira parcela a ser gerada
                        if (dataVencimento == null) {
                            String dataVencimentoStr = (dataLancamento.getDate() < 10 ? "0" + dataLancamento.getDate() : dataLancamento.getDate()) + "/" + movProdutoVO.getMesReferencia();
                            dataVencimento = Uteis.getDate(dataVencimentoStr, "dd/MM/yyyy");
                        }
                    }
                }

                int numeroParcela = 1;
                if (valorListaProdutoEmAberto > 0.0 && qtdParcelas > 0) {
                    Double valorParcela = Uteis.arredondarForcando2CasasDecimais(valorListaProdutoEmAberto / qtdParcelas);
                    Double valorDiferencaoArredondamento = 0.0;
                    valorDiferencaoArredondamento = valorListaProdutoEmAberto - (valorParcela * qtdParcelas);

                    // Descobrir numero da parcela a partir da qual as demais serão geradas
                    boolean existeMovParcela = true;
                    while (existeMovParcela) {
                        Statement stmExisteMovParcela = con.createStatement();
                        ResultSet rsExisteMovParcela = stmExisteMovParcela.executeQuery("SELECT codigo FROM movparcela WHERE descricao = 'PARCELA " + numeroParcela + "' AND contrato = " + codigoContrato);
                        existeMovParcela = rsExisteMovParcela.next();

                        if (existeMovParcela) {
                            numeroParcela++;
                        }
                    }

                    for (int i = 1; i <= qtdParcelas; i++) {
                        Double valorFinalParcela = 0.0;
                        if (i == 1 && Uteis.arredondarForcando2CasasDecimais(valorDiferencaoArredondamento) > 0.0) {
                            // adicionar ou subtrair o valor da diferenca do arredondamento na primeira parcela
                            valorFinalParcela = valorParcela + valorDiferencaoArredondamento;
                        } else {
                            valorFinalParcela = valorParcela;
                        }
                        insiraMovParcela("PARCELA " + numeroParcela, codigoContrato, codigoPessoa, dataLancamento, dataVencimento, codigoEmpresa, valorFinalParcela, movParcelaDAO);
                        dataVencimento = Uteis.somarMeses(dataVencimento, 1);
                        numeroParcela++;
                    }
                }

                Statement stmDeletarCC = con.createStatement();
                stmDeletarCC.execute("DELETE FROM movimentocontacorrentecliente WHERE pessoa = " + codigoPessoa);
                count++;

                RefazerVinculoMovProdutoParcelaContratos.refazerMovProdutoParcela(con, codigoContrato);
            }

        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
            }
        }
    }

    private static MovParcelaVO insiraMovParcela(String descricao, Integer contrato, Integer pessoa, Date datalancamento, Date dataVencimento, Integer empresa, double valorNovaParcela, MovParcela movParcelaDAO) throws Exception {
        adicionarLog("\tInserindo parcela: " + descricao + " " +valorNovaParcela + " " + Uteis.getData(dataVencimento, "dd/MM/yyyy"));
        MovParcelaVO novaParcela = new MovParcelaVO();
        novaParcela.setDescricao(descricao);
        novaParcela.setResponsavel(new UsuarioVO());
        novaParcela.getResponsavel().setCodigo(1);
        novaParcela.setValorParcela(valorNovaParcela);
        novaParcela.setContrato(new ContratoVO());
        novaParcela.getContrato().setCodigo(contrato);
        novaParcela.setSituacao("EA");
        novaParcela.setDataRegistro(datalancamento);
        novaParcela.setEmpresa(new EmpresaVO());
        novaParcela.getEmpresa().setCodigo(empresa);
        novaParcela.setPessoa(new PessoaVO());
        novaParcela.getPessoa().setCodigo(pessoa);
        novaParcela.setDataVencimento(dataVencimento);
        novaParcela.setDataCobranca(dataVencimento);
        movParcelaDAO.incluir(novaParcela);
        return novaParcela;
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    public void setLogGravar(StringBuilder logGravar) {
        this.logGravar = logGravar;
    }
}

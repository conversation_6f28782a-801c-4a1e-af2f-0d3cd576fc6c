package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luiz Felipe",
        data = "14/12/2024",
        descricao = "E2-1466 - Comissão valor fixo",
        motivacao = "E2-1466 - Comissão valor fixo")
public class AtualizacaoTicketE21466 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE modalidadecomissaocolaborador ADD COLUMN valorComissao double precision;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE alunocomissaocolaborador ADD COLUMN valorComissao double precision;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE turmacomissaocolaborador ADD COLUMN valorComissao double precision;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE colaborador ADD COLUMN valorComissao double precision;", c);
            boolean existe = SuperFacadeJDBC.existe("select codigo from cadastrodinamicoitem where nomecampo = 'VALORFIXOCOMISSAO'", c);
            if (!existe) {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO cadastrodinamicoitem(cadastrodinamico,nomecampo,labelcampo,mostrarcampo,campoobrigatorio) " +
                        "VALUES((select codigo from cadastrodinamico where nometabela ilike 'colaborador'), '" +
                        "', 'Valor fixo Comissão', false, false);", c);
            }
        }
    }
}

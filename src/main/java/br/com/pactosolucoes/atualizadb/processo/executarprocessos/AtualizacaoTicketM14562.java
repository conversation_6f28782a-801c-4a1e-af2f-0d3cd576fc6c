package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Douglas Soares",
        data = "20/02/2025",
        descricao = "Aumentar o tamanho do campo 'nome' na tabela 'brinde' para varchar(200)",
        motivacao = "Ticket M1-4562 - Para Evitar erro ao inserir nomes grandes em brindes"
)
public class AtualizacaoTicketM14562 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            StringBuilder sql = new StringBuilder();
            sql.append("ALTER TABLE brinde ")
                    .append("ALTER COLUMN nome TYPE varchar(200);");

            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), c);
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import edu.emory.mathcs.backport.java.util.Arrays;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.List;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class CorrigirNomenclaturaBancos {

    public static void main(String... args) throws Exception {
        Uteis.debug = true;
        String[] hosts = new String[]{
            "********************************/",
            "********************************/",
            "********************************/",
            "********************************/",
            "********************************/",
            "*********************************/",
            "********************************/",
            "********************************/",
            "********************************/",
            "********************************/",
            "********************************/",
            "********************************/",
            "********************************/",
            "********************************/",
            "********************************/",
            "********************************/",
            "********************************/"            
        };        
        
//        final String[] terms = new String[]{"%_old%", "%_final"};
//        final String[] terms = new String[]{"\\_%"};
        final String[] terms = new String[]{"%pratique"};
        
        final String[] bancosRenomear = new String[]{/*
            "bdzillyontubaraogo_old03042019", 
            "bdzillyonluizaduarte_old020419", 
            "bdmuscsesicatalaoteste_oldzerado", 
            "bdzillyoncolunavertebral_old", 
            "bdzillyonaepfesportes_old010219", 
            "bdzillyonlimitsnovaiguacu_OLD03042019", 
            "bdzillyonisacrochadf_old01042019", 
            "bdzillyonisacrochadf_old0104_2", 
            "bdzillyoncrossfitteresopolis_old2603", 
            "bdzillyonenvolveipatinga_old2803", 
            "bdzillyonniteroiswinrj_old2803", 
            "bdzillyonclubecolombosp_old_2803", 
            "bdzillyonnadarfitnessaquaticopr_old2503", 
            "bdzillyonclubecolombosp_old2703", 
            "bdzillyonpactojuliano_final", 
            "bdzillyonextremefit_OLD01042019", 
            "bdzillyonclipamericana_old02042019"
            */
        };
        
        List<String> a = Arrays.asList(bancosRenomear);
        
        for (String host : hosts) {
//            Uteis.logar("######################### " + host);
            Connection dbPgsql = DriverManager.getConnection(host + "postgres", "postgres", "pactodb");
            StringBuilder s = new StringBuilder("select datname from pg_database where ((lower(datname) like '%bdzillyon%') or (lower(datname) like '%bdmusc%')) ");
            if (terms.length > 0) {
                s.append(" and (");
                for (int i = 0; i < terms.length; i++) {
                    s.append(" (lower(datname) like '").append(terms[i]).append("')").append((i + 1) < terms.length ? " or " : "");                    
                }                
                s.append(" )");
            }
            s.append(" order by datname");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(s.toString(), dbPgsql);
            while (rs.next()) {
                try {
                    final String nomeBD = rs.getString("datname");
                    Uteis.logar(String.format("%s em %s", nomeBD, host));
                    if (a.contains(nomeBD)){
                        SuperFacadeJDBC.executarConsulta(String.format(
                                "ALTER DATABASE \"%s\" RENAME TO _%s;", nomeBD, nomeBD), dbPgsql);
                        Uteis.logar("################## Renomeado banco " + nomeBD + " ##################");
                    }
                } catch (Exception e) {
                    Uteis.logar(e, CorrigirNomenclaturaBancos.class);
                }
            }
            dbPgsql.close();
            dbPgsql = null;
        }

    }
}

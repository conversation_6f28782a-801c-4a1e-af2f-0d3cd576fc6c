package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.BoletoPJBankVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.BoletoPJBank;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 08/12/2021
 */
public class MigradorBoletoBoletoPjBank {

    private Connection con;

    public MigradorBoletoBoletoPjBank(Connection con) {
        this.con = con;
    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "INICIO | MigradorBoletoBoletoPjBank...");

            String chave = null;
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }

            Connection conOAMD = Conexao.obterConexaoBancoEmpresas();
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT chave from empresa \n");
            sql.append("WHERE 1 = 1 \n");
            if (!UteisValidacao.emptyString(chave)) {
                sql.append(" AND chave = '").append(chave).append("'");
            }

            PreparedStatement st = conOAMD.prepareStatement(sql.toString());
            ResultSet rs = st.executeQuery();
            while (rs.next()) {
                chave = rs.getString("chave");
                Connection con = null;
                try {
                    Uteis.logar(null, "Obter conexão para chave: " + chave);
                    con = new DAO().obterConexaoEspecifica(chave);
                    MigradorBoletoBoletoPjBank migrador = new MigradorBoletoBoletoPjBank(con);
                    migrador.atualizarTabelas();
                    migrador.migrarPjBank();
                    migrador = null;
                } catch (Exception ex) {
                    Uteis.logar(null, "Erro chave: " + chave);
                    ex.printStackTrace();
                } finally {
                    if (con != null) {
                        con.close();
                    }
                }
            }

            Uteis.logar(true, null, "FIM | MigradorBoletoBoletoPjBank...");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public JSONObject migrarPjBank() {
        Integer sucesso = 0;
        Integer falha = 0;
        Integer total = 0;
        Empresa empresaDAO;
        try {
            Uteis.logar(true, null, "INICIO | Migrar PJBank...");
            empresaDAO = new Empresa(con);

            String sql = "select \n" +
                    "b.*, \n" +
                    "mp.empresa as empresa_parcela, \n" +
                    "mp.valorparcela as parcela_valorparcela, \n" +
                    "exists(select codigo from movpagamento where codigo = b.movpagamento) as existeMovPagamento, \n" +
                    "exists(select codigo from recibopagamento where codigo = b.recibopagamento) as existeReciboPagamento, \n" +
                    "exists(select codigo from pessoa where codigo = b.pessoa) as existePessoa \n" +
                    "from boletospjbank b \n" +
                    "left join movparcela mp on mp.codigo = b.movparcela \n" +
                    "where b.migrado = false \n" +
                    "order by b.codigo";

            total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            int i = 0;

            Map<Integer, EmpresaVO> mapaEmpresa = empresaDAO.obterMapaEmpresas(Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                BoletoPJBankVO boletoPJBankVO = null;
                BoletoPJBank boletoPJBankDAO;
                Boleto boletoDAO;
                try {
                    con.setAutoCommit(false);
                    boletoPJBankDAO = new BoletoPJBank(con);
                    boletoDAO = new Boleto(con);

                    Uteis.logar(true, null, "Boleto PjBank... " + ++i + "/" + total);

                    boletoPJBankVO = boletoPJBankDAO.montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    EmpresaVO empresaVO = null;
                    Integer empresa_parcela = rs.getInt("empresa_parcela");
                    if (!UteisValidacao.emptyNumber(empresa_parcela)) {
                        empresaVO = mapaEmpresa.get(empresa_parcela);
                    }
                    boolean existeReciboPagamento = rs.getBoolean("existeReciboPagamento");
                    boolean existeMovPagamento = rs.getBoolean("existeMovPagamento");
                    boolean existePessoa = rs.getBoolean("existePessoa");

                    boletoPJBankVO.getMovParcelaVO().setValorParcela(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("parcela_valorparcela")));

                    BoletoVO boletoVO = new BoletoVO();
                    boletoVO.setTipo(TipoBoletoEnum.PJ_BANK);
                    boletoVO.setOrigem(OrigemCobrancaEnum.NENHUM);
                    boletoVO.setIdExterno(boletoPJBankVO.getIdUnico());
                    boletoVO.setEmpresaVO(empresaVO);
                    boletoVO.setConvenioCobrancaVO(boletoPJBankVO.getConvenioCobrancaVO());
                    boletoVO.setUsuarioVO(boletoPJBankVO.getUsuarioVO());
                    if (existeMovPagamento) {
                        boletoVO.setMovPagamentoVO(boletoPJBankVO.getMovPagamentoVO());
                    }
                    if (existeReciboPagamento) {
                        boletoVO.setReciboPagamentoVO(boletoPJBankVO.getReciboPagamentoVO());
                    }
                    if (existePessoa) {
                        boletoVO.setPessoaVO(boletoPJBankVO.getPessoaVO());
                    }
                    boletoVO.setValor(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(boletoPJBankVO.getValor())));
                    boletoVO.setValorPago(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(boletoPJBankVO.getValorPago())));
                    boletoVO.setValorLiquido(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(boletoPJBankVO.getValorLiquido())));
                    boletoVO.setValorTarifa(Uteis.arredondarForcando2CasasDecimais(Double.valueOf(boletoPJBankVO.getValorTarifa())));
                    boletoVO.setLinhaDigitavel(boletoPJBankVO.getLinhaDigitavel());
                    boletoVO.setDataVencimento(boletoPJBankVO.getDataVencimento());
                    boletoVO.setDataPagamento(boletoPJBankVO.getDatapagamento());
                    boletoVO.setDataCredito(boletoPJBankVO.getDataCredito());
                    boletoVO.setDataRegistro(boletoPJBankVO.getDataRegistro());
                    boletoVO.setLinkBoleto(boletoPJBankVO.getLinkBoleto());
                    boletoVO.setNossoNumero(boletoPJBankVO.getNossoNumero());
                    boletoVO.setNumeroInterno(boletoPJBankVO.getPedidoNumero().toString());
                    boletoVO.setNumeroExterno(boletoPJBankVO.getPedidoNumeroPJBank());
                    boletoVO.setJsonEstorno(boletoPJBankVO.getJsonEstorno());
                    boletoVO.setDiaMesDescontoPagAntecipado(boletoPJBankVO.getDiaDoMesDescontoBoletoPagAntecipado());
                    boletoVO.setPorcentagemDescontoPagAntecipado(boletoPJBankVO.getPorcentagemDescontoBoletoPagAntecipado());

                    SituacaoBoletoEnum situacaoBoletoEnum = null;
                    if (boletoPJBankVO.getRegistroSistemaBancario().equalsIgnoreCase("enviado") ||
                            boletoPJBankVO.getRegistroSistemaBancario().equalsIgnoreCase("pendente")) {
                        situacaoBoletoEnum = SituacaoBoletoEnum.AGUARDANDO_REGISTRO;
                    } else if (boletoPJBankVO.getRegistroSistemaBancario().equalsIgnoreCase("Estornado") ||
                            (boletoPJBankVO.getRegistroSistemaBancario().trim().isEmpty() && !boletoPJBankVO.getJsonEstorno().trim().isEmpty())) {
                        situacaoBoletoEnum = SituacaoBoletoEnum.ESTORNADO;
                    } else if (boletoPJBankVO.getRegistroSistemaBancario().equalsIgnoreCase("Cancelado") ||
                            boletoPJBankVO.getRegistroSistemaBancario().equalsIgnoreCase("baixado")) {
                        situacaoBoletoEnum = SituacaoBoletoEnum.CANCELADO;
                    } else if ((boletoPJBankVO.getRegistroSistemaBancario().equalsIgnoreCase("confirmado") && boletoPJBankVO.getValorPago() > 0) ||
                            !UteisValidacao.emptyNumber(boletoPJBankVO.getMovPagamentoVO().getCodigo()) ||
                                    !UteisValidacao.emptyNumber(boletoPJBankVO.getReciboPagamentoVO().getCodigo())) {
                        situacaoBoletoEnum = SituacaoBoletoEnum.PAGO;
                    } else if (boletoPJBankVO.getRegistroSistemaBancario().equalsIgnoreCase("confirmado")) {
                        situacaoBoletoEnum = SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO;
                    } else if (boletoPJBankVO.getRegistroSistemaBancario().equalsIgnoreCase("rejeitado")) {
                        situacaoBoletoEnum = SituacaoBoletoEnum.REJEITADO;
                    }

                    if (situacaoBoletoEnum == null) {
                        throw new Exception("Situacao não encontrada | RegistroSistemaBancario: " + boletoPJBankVO.getRegistroSistemaBancario());
                    }

                    boletoVO.setSituacao(situacaoBoletoEnum);

                    if (!UteisValidacao.emptyNumber(boletoPJBankVO.getMovParcelaVO().getCodigo())) {
                        boletoPJBankVO.getMovParcelaVO().setValorMulta(boletoPJBankVO.getMultaValorFixo());
                        boletoPJBankVO.getMovParcelaVO().setValorJuros(boletoPJBankVO.getJurosValorFixo());
                        boletoVO.getListaParcelas().add(boletoPJBankVO.getMovParcelaVO());
                    }

                    boletoVO.setValidarDados(false);
                    boletoDAO.incluir(boletoVO);

                    SuperFacadeJDBC.executarUpdate("update boletospjbank set migrado = true where codigo = " + boletoPJBankVO.getCodigo(), con);
                    ++sucesso;
                    con.commit();
                } catch (Exception ex) {
                    ++falha;
                    con.rollback();
                    ex.printStackTrace();
                    Uteis.logar(true, null, "BoletoPjBank :" + boletoPJBankVO.getCodigo() + " | " + ex.getMessage());
                } finally {
                    con.setAutoCommit(true);
                    boletoPJBankDAO = null;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            empresaDAO = null;
            Uteis.logar(true, null, "FIM | Migrar PJBank...");
            Uteis.logar(true, null, "Total | " + total);
            Uteis.logar(true, null, "Sucesso | " + sucesso);
            Uteis.logar(true, null, "Falha | " + falha);
        }

        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("falha", falha);
        return json;
    }

    public void atualizarTabelas() {
        try {
            Uteis.logar(true, null, "INICIO | AtualizarTabelas...");

            StringBuilder tabela1 = new StringBuilder();
            tabela1.append("CREATE TABLE boleto ( \n");
            tabela1.append("codigo serial PRIMARY KEY,  \n");
            tabela1.append("dataRegistro TIMESTAMP WITHOUT TIME ZONE,  \n");
            tabela1.append("idexterno CHARACTER VARYING, \n");
            tabela1.append("tipo integer, \n");
            tabela1.append("usuario integer, \n");
            tabela1.append("pessoa integer, \n");
            tabela1.append("empresa integer, \n");
            tabela1.append("situacao integer, \n");
            tabela1.append("valor double precision, \n");
            tabela1.append("datavencimento DATE, \n");
            tabela1.append("conveniocobranca integer, \n");
            tabela1.append("movpagamento integer, \n");
            tabela1.append("recibopagamento integer, \n");
            tabela1.append("origem integer, \n");
            tabela1.append("outrasInformacoes text, \n");
            tabela1.append("paramsenvio text, \n");
            tabela1.append("paramsresposta text, \n");
            tabela1.append("valorPago double precision, \n");
            tabela1.append("valorLiquido double precision, \n");
            tabela1.append("valorTarifa double precision, \n");
            tabela1.append("dataCredito DATE, \n");
            tabela1.append("dataPagamento TIMESTAMP WITHOUT TIME ZONE, \n");
            tabela1.append("linkBoleto CHARACTER VARYING, \n");
            tabela1.append("nossoNumero CHARACTER VARYING, \n");
            tabela1.append("numeroInterno CHARACTER VARYING, \n");
            tabela1.append("numeroExterno CHARACTER VARYING, \n");
            tabela1.append("linhaDigitavel CHARACTER VARYING, \n");
            tabela1.append("jsonEstorno text, \n");
            tabela1.append("diaMesDescontoPagAntecipado integer, \n");
            tabela1.append("porcentagemDescontoPagAntecipado double precision, \n");
            tabela1.append("CONSTRAINT fk_boleto_empresa FOREIGN KEY (empresa) \n");
            tabela1.append("REFERENCES empresa (codigo) \n");
            tabela1.append("MATCH simple ON UPDATE NO ACTION ON DELETE NO ACTION, \n");
            tabela1.append("CONSTRAINT fk_boleto_usuario FOREIGN KEY (usuario) \n");
            tabela1.append("REFERENCES usuario (codigo) \n");
            tabela1.append("MATCH simple ON UPDATE NO ACTION ON DELETE NO ACTION, \n");
            tabela1.append("CONSTRAINT fk_boleto_pessoa FOREIGN KEY (pessoa) \n");
            tabela1.append("REFERENCES pessoa (codigo) \n");
            tabela1.append("MATCH simple ON UPDATE NO ACTION ON DELETE NO ACTION, \n");
            tabela1.append("CONSTRAINT fk_boleto_conveniocobranca FOREIGN KEY (conveniocobranca) \n");
            tabela1.append("REFERENCES conveniocobranca (codigo) \n");
            tabela1.append("MATCH simple ON UPDATE NO ACTION ON DELETE NO ACTION, \n");
            tabela1.append("CONSTRAINT fk_boleto_movpagamento FOREIGN KEY (movpagamento) \n");
            tabela1.append("REFERENCES movpagamento (codigo) \n");
            tabela1.append("MATCH simple ON UPDATE NO ACTION ON DELETE NO ACTION, \n");
            tabela1.append("CONSTRAINT fk_boleto_recibopagamento FOREIGN KEY (recibopagamento) \n");
            tabela1.append("REFERENCES recibopagamento (codigo) \n");
            tabela1.append("MATCH simple ON UPDATE NO ACTION ON DELETE NO ACTION \n");
            tabela1.append(") \n");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(tabela1.toString(), con);

            StringBuilder tabela2 = new StringBuilder();
            tabela2.append("CREATE TABLE boletomovparcela ( \n");
            tabela2.append("codigo serial PRIMARY KEY,  \n");
            tabela2.append("boleto integer, \n");
            tabela2.append("movparcela integer, \n");
            tabela2.append("nrTentativaParcela integer, \n");
            tabela2.append("valorparcela double precision, \n");
            tabela2.append("valormulta double precision, \n");
            tabela2.append("valorjuros double precision, \n");
            tabela2.append("jsonEstorno text, \n");
            tabela2.append("CONSTRAINT fk_boletomovparcela_movparcela FOREIGN KEY (movparcela) \n");
            tabela2.append("REFERENCES movparcela (codigo) \n");
            tabela2.append("MATCH simple ON UPDATE NO ACTION ON DELETE NO ACTION, \n");
            tabela2.append("CONSTRAINT fk_boletomovparcela_boleto FOREIGN KEY (boleto) \n");
            tabela2.append("REFERENCES boleto (codigo) \n");
            tabela2.append("MATCH simple ON UPDATE NO ACTION ON DELETE NO ACTION \n");
            tabela2.append(") \n");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(tabela2.toString(), con);

            StringBuilder tabela3 = new StringBuilder();
            tabela3.append("CREATE TABLE boletohistorico ( \n");
            tabela3.append("codigo serial PRIMARY KEY,  \n");
            tabela3.append("dataregistro TIMESTAMP WITHOUT TIME ZONE,  \n");
            tabela3.append("boleto integer,  \n");
            tabela3.append("operacao CHARACTER VARYING, \n");
            tabela3.append("dados text \n");
            tabela3.append(") \n");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(tabela3.toString(), con);

            //alterar todos os pjbank para produção
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE conveniocobranca SET ambiente = " + AmbienteEnum.PRODUCAO.getCodigo() + " WHERE tipoconvenio = " + TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigo() + ";", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN gerarBoletoCaixaAberto boolean default true;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa SET gerarBoletoCaixaAberto = false;", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE boletospjbank ADD COLUMN migrado boolean default false;", con);

            //remover constraint da tabela antiga
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE boletospjbank DROP CONSTRAINT fk_boletospjbank_movparcela;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE boletospjbank DROP CONSTRAINT fk_boletospjbank_conveniocobranca;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE logspjbank DROP CONSTRAINT fk_boletopjbank_boletospjbank;", con);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            Uteis.logar(true, null, "FIM | AtualizarTabelas...");
        }
    }

}

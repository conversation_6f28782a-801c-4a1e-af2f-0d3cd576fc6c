package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "29/08/2024",
        descricao = "Cria a coluna idPedidoNuvemshop na tabela Compra",
        motivacao = "GC-921: Ajustes na Integração Nuvemshop e Sistema Pacto")
public class CriarColunaIdPedidoNuvemshopTbCompra implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        String sql = "ALTER TABLE Compra ADD COLUMN idPedidoNuvemshop VARCHAR(255);";
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.sincronizador;

import br.com.pactosolucoes.enumeradores.TipoFilaEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.modulos.integracao.UsuarioMovelControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.TreinoWSConsumer;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;

public class Sincronizador {

    private String key;
    private Connection con;
    private UsuarioMovel usuarioMovelDao;
    private Cliente clienteDao;

    public Sincronizador(String key, Connection con) throws Exception{
        this.key = key;
        this.con = con;
        this.usuarioMovelDao = new UsuarioMovel(con);
        this.clienteDao = new Cliente(con);
    }

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "vithal";
            Uteis.logar(null, "Obter conexão para chave: " + chave);
            Conexao.guardarConexaoForJ2SE(new DAO().obterConexaoEspecifica(chave));
            Sincronizador sincronizador = new Sincronizador(chave, new DAO().obterConexaoEspecifica(chave));
            sincronizador.sincronizar();

        } catch (Exception ex) {
            Logger.getLogger(Sincronizador.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void sincronizar(){
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select identificador, tipo from filasincronia order by codigo", con);
            while(rs.next()){
                TipoFilaEnum tipo = TipoFilaEnum.getInstance(rs.getInt("tipo"));
                switch (tipo){
                    case ALUNO_TREINO:
                        ClienteVO cliente = clienteDao.consultarPorChavePrimaria(rs.getInt("identificador"), Uteis.NIVELMONTARDADOS_TODOS);
                        if(sincronizarAlunoTreino(cliente)){
                            removerDaFila(tipo, cliente.getCodigo());
                        }
                        break;
                    case ALUNO_PESQUISA_TREINO:
                        JSONArray clientesJson = clienteDao.consultarDadosClienteConsultaTW(rs.getInt("identificador"));
                        if (clientesJson.length() == 0 || sincronizarAlunoPesquisaTreino((JSONObject) clientesJson.get(0))) {
                            removerDaFila(tipo, rs.getInt("identificador"));
                        }
                        break;
                }
            }
        }catch (Exception ex){
            Logger.getLogger(Sincronizador.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public boolean sincronizarAlunoTreino(ClienteVO cliente){
        try {

            UsuarioMovelVO uMovel = usuarioMovelDao.consultarPorCliente(cliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            uMovel.setPropagarExcessao(true);
            uMovel.setCliente(cliente);
            TreinoWSConsumer.sincronizarUsuario(key, uMovel);
            UsuarioMovelControle.adicionarUsuarioServicoDescobrir(key, uMovel);
            return true;
        }catch (Exception e){
            Logger.getLogger(Sincronizador.class.getName()).log(Level.SEVERE, null, e);
            return false;
        }
    }

    public void removerDaFila(TipoFilaEnum tipo, Integer identificador) throws Exception{
        SuperFacadeJDBC.executarConsulta("DELETE FROM filasincronia where tipo = " + tipo.getId()
                        + " and identificador = " + identificador, con);
    }

    public static void inserirNaFila(TipoFilaEnum tipo, Integer identificador, String chave){
        try {
            SuperFacadeJDBC.executarConsulta("insert into filasincronia (tipo, identificador) values (" + tipo.getId()+ ", " + identificador+")", new DAO().obterConexaoEspecifica(chave));
        }catch (Exception e){
            Logger.getLogger(Sincronizador.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public boolean sincronizarAlunoPesquisaTreino(JSONObject clienteJson){
        try {
            TreinoWSConsumer.sincronizarAlunoPesquisa(key,  clienteJson);
            return true;
        }catch (Exception e){
            Logger.getLogger(Sincronizador.class.getName()).log(Level.SEVERE, null, e);
            return false;
        }
    }

}

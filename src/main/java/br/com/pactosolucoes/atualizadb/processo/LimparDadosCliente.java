/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import java.util.List;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.facade.jdbc.financeiro.PlanoConta;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class LimparDadosCliente {
        Connection c = null;
       public static void main(String[] args) {
        try {
            Connection conn = DriverManager.getConnection("***********************************************************************", "postgres", "pactodb");
            Statement stmt =  conn.createStatement();
            
            stmt.addBatch("UPDATE periodoacessocliente set contratobaseadorenovacao = null where codigo in (SELECT pac.codigo from periodoacessocliente pac left join contrato c on pac.contratobaseadorenovacao = c.codigo where c.vigenciaateajustada <= '2014-12-31')");
            stmt.addBatch("DELETE FROM periodoacessocliente where contrato in (SELECT codigo from contrato where vigenciaateajustada <= '2014-12-31');");
            stmt.addBatch("DELETE FROM contrato where vigenciaateajustada <= '2014-12-31';");
            stmt.addBatch("DELETE FROM caixamovconta where movconta in (SELECT codigo from movconta where dataquitacao <= '2014-12-31');");
            stmt.addBatch("UPDATE lote set pagamovconta = null where codigo in (SELECT lote.codigo from lote inner join movconta on movconta.codigo = lote.pagamovconta where movconta.dataquitacao <= '2014-12-31')");
            stmt.addBatch("DELETE FROM movconta where dataquitacao <= '2014-12-31';");
            stmt.addBatch("DELETE FROM movproduto where vendaavulsa in (select codigo from vendaavulsa where dataregistro <= '2014-12-31');");
            stmt.addBatch("update lote l set valor = (select coalesce(sum ( valor ), 0) from cheque inner join chequecartaolote on chequecartaolote.cheque = cheque.codigo and chequecartaolote.lote = l.codigo)+(select coalesce(sum ( valor ), 0) from cartaocredito inner join chequecartaolote on chequecartaolote.cartao = cartaocredito.codigo and chequecartaolote.lote = l.codigo)");
            stmt.addBatch("DELETE FROM movparcela where vendaavulsa in (select codigo from vendaavulsa where dataregistro <= '2014-12-31');");
            stmt.addBatch("DELETE FROM vendaavulsa where dataregistro <= '2014-12-31';");
            stmt.addBatch("update movconta mc set valor = (select valor from lote where codigo = mc.lote) where mc.lote is not null and mc.lote > 0;");
            stmt.addBatch("update movcontarateio mcr set valor = (select valor from movconta where codigo = mcr.movconta) where movconta in (select codigo from movconta where lote is not null and lote > 0);");
            stmt.addBatch("DELETE FROM acessocliente where dthrentrada  <= '2014-12-31'");
            
            int[] results = stmt.executeBatch();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
       
}

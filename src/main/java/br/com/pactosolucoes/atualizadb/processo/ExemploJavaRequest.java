package br.com.pactosolucoes.atualizadb.processo;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;

public class ExemploJavaRequest {

    public static void main(String ... args) throws Exception{
        HttpURLConnection urlConn;
        URL mUrl = new URL("http://app.pactosolucoes.com.br/api/prest/importacao/9fe5efa91ec0625b18d8f4cf8eddf95f/" +
                "atualizarFotoCliente?cpf=409.074.616-70");
        urlConn = (HttpURLConnection) mUrl.openConnection();
        urlConn.addRequestProperty("Content-Type", "application/" + "POST");
        String foto = getFoto();
        urlConn.setDoOutput(true);
        urlConn.setRequestProperty("Content-Length", Integer.toString(foto.length()));
        urlConn.getOutputStream().write(foto.getBytes("UTF8"));


        OutputStreamWriter wr = new OutputStreamWriter(urlConn.getOutputStream());
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(urlConn.getInputStream()));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        System.out.println(resposta);
    }

    public static String getFoto() throws Exception {
        BufferedReader br = new BufferedReader(new FileReader("C:\\PactoJ\\foto.txt"));
        StringBuilder sb = new StringBuilder();
        String line = br.readLine();
        while (line != null) {
            sb.append(line);
            line = br.readLine();
        }
        return sb.toString();
    }

}

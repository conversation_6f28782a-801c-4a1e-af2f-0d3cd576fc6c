package br.com.pactosolucoes.atualizadb.processo;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import negocio.facade.jdbc.utilitarias.Conexao;



/**
 * A funcao "limparLogComReferenciasAObjetos" que realiza uma limpeza 
 * na tabela de log que retira os valores gravados que sao referencia a objetos, classe ira:
 * 
 * - chamar a funcao limparLog();
 * - Buscar na tabela log nos campos valorcampoalterado e valorcampoanterior que contem o valor "%negocio.comuns%";
 * - Depois realizar um laco no resultado desta busca e verifica se os campos valorcampoalterado ou valorcampoanterior
 *   contem o valor 'negocio.comuns', se existir é chamado a funcao limparCampo;
 * - A funcao limparCampo() recebe o valor a ser limpo
 * - Apos realiza a limpeza dos campos se realiza as operacoes de atualizacao ou delecao
 *  
 * <AUTHOR>
 */
public class LimparLog  {
	//{Host, DBName, Usuario, Senha... }
	private String[][] bancos = {
									{"localhost","biovida", "postgres", "pactodb", "5432"}
								};
	
	/**
	 * Metodo construtor
	 * 
	 * @throws SQLException
	 */
	public LimparLog() throws SQLException {
		//Descomentar essa linha quando for executar o limpador de log
		//em modo stand alone
//		limparLogComReferenciasAObjetos();
	}
	
	/**
	 * A funcao faz um split com o caracter '[', se o resultado for um array maior que 1
	 * o resultado sera tratado para retirar do campo as referencias ao(s) objeto(s), senao
	 * o campo será deletado, conforme combina com o Joao Felipe. Porque nos teste que realizamos
	 * nos detectamos somente as seguintes situacoes abaixo, onde nas situacoes 'a' e 'b' o campo
	 * contem mais valores, na situacao 'c' so existia a referencia ao objeto e 'd' que contem valor
	 * antes da referencia 
	 * 
	 * Exemplos:
	 * 
	 * a) [negocio.comuns.plano.PlanoCondicaoPagamentoVO@86ef16, negocio.comuns.plano.PlanoCondicaoPagamentoVO@ba8d9f]
	 * b) [negocio.comuns.plano.PlanoCondicaoPagamentoVO@86ef16]
	 * c) negocio.comuns.plano.PlanoCondicaoPagamentoVO@86ef16
	 * d) Campo(s) Código 7 Modalidade 7 Empresa negocio.comuns.basico.EmpresaVO@1da886f
	 * 
	 * Autor: Pedro Y. Saito
	 * Criado em 24/02/2011
	 */
	private String limparCampo(String campo){
		String campoNovo = "";
				
		String[] aux = campo.split("\\[");
		int pos;
		for (int i=0;i<aux.length;i++){
			if (aux.length > 1){
				pos = aux[i].indexOf(']');
				if (aux[i].trim().indexOf("negocio.comuns") >= 0){
					campoNovo += " " + aux[i].substring((pos+1), aux[i].length());					
				} else {
					if (pos == -1){
						campoNovo += aux[i];
					} else {
						campoNovo += "[" + aux[i];
					}
				}
			} else {				
				if (aux[0].trim().indexOf("negocio.comuns") > 0){
					int posA = aux[0].trim().indexOf("negocio.comuns");
					String b = aux[0].trim().substring(0, posA);
					campoNovo += b;				
				}
			}
		}
		
		return campoNovo;
	}
	
	/**
	 * Metodo que realiza a conexao com o banco de dados dependendo 
	 * do valor passado, onde o valor passado é uma posicao no array
	 * de bancos (variavel global de formato {Host, DBName, Usuario, Senha, porta... })
	 *
	 * Autor: Pedro Y. Saito
	 * Criado em 24/02/2011
	 */
	private Connection getConexao(int posicaoBancos){
		//{Host, DBName, Usuario, Senha, porta... }
		
		Connection con = null;	
		
		if (posicaoBancos <= (bancos.length - 1)){
			String driver = "org.postgresql.Driver";  
	        String user   = bancos[posicaoBancos][2];  
	        String senha = bancos[posicaoBancos][3];  
	        String url      = "jdbc:postgresql://" + bancos[posicaoBancos][0] + ":" + bancos[posicaoBancos][4] + "/" + bancos[posicaoBancos][1];  
	          
	        try  
	        {  
	        	System.out.println("Criando conexao para " + bancos[posicaoBancos][0] + " no banco " + bancos[posicaoBancos][1]);
	            Class.forName(driver);    
	  
	            con = (Connection) DriverManager.getConnection(url, user, senha);  
	  
	            System.out.println("Conexão realizada com sucesso.");  
	  
	        }  
	        catch (ClassNotFoundException ex)  
	        {  
	            System.err.print(ex.getMessage());  
	        }   
	        catch (SQLException e)  
	        {  
	            System.err.print(e.getMessage());  
	        }
		}
        return con;
	}
	
	/**
	 * Metodo que realiza o gerencia a limpeza do log do array de bancos
	 *
	 * - Buscar na tabela log nos campos valorcampoalterado e valorcampoanterior que contem o valor "%negocio.comuns%";
	 * - Depois realizar um laco no resultado desta busca e verifica se os campos valorcampoalterado ou valorcampoanterior
 	 *   contem o valor 'negocio.comuns', se existir é chamado a funcao limparCampo;
 	 * - A funcao limparCampo() recebe o valor a ser limpo
 	 * - Apos realiza a limpeza dos campos se realiza as operacoes de atualizacao ou delecao
	 *
	 * Autor: Pedro Y. Saito
	 * Criado em 24/02/2011
	 */
	public void limparLogComReferenciasAObjetos() throws SQLException {
		Connection con = null; 
		
		//Laco que no array de bancos
		for (int j=0; j < bancos.length; j++){
			//Chamada ao metodo que realiza a conexao com o banco na posicao passada
			con = getConexao(j);
			
			if (con != null){
				try {
					con.setAutoCommit(false);
					
					limparLog (con);
					
					con.commit();
				} catch (SQLException e) {
					con.rollback();
					e.printStackTrace();
				} finally {  
			         try {  
			            con.close();  
			         } catch(SQLException onConClose) {  
			             System.out.println("Erro no fechamento da conexão");  
			             onConClose.printStackTrace();  
			         }  
				} 
			}
		}
	}
		
	/**
	 * Metodo que realiza o gerencia a limpeza do log da conexao que estiver na sessao
	 *
	 * - Buscar na tabela log nos campos valorcampoalterado e valorcampoanterior que contem o valor "%negocio.comuns%";
	 * - Depois realizar um laco no resultado desta busca e verifica se os campos valorcampoalterado ou valorcampoanterior
 	 *   contem o valor 'negocio.comuns', se existir é chamado a funcao limparCampo;
 	 * - A funcao limparCampo() recebe o valor a ser limpo
 	 * - Apos realiza a limpeza dos campos se realiza as operacoes de atualizacao ou delecao
	 *
	 * Autor: Pedro Y. Saito
	 * Criado em 28/02/2011
	 */
	public void limparLogComReferenciasAObjetosReflection () throws Exception {
		//Obtendo a conexao da sessao
		Connection con = Conexao.getFromSession();
		
		//Verifica se a conexao e nula
		if (con != null){
			//Chamada ao metodo que ira realizar a limpeza no log
			limparLog (con);
		}
	}
	
	/**
	 * Metodo que realiza o gerencia a limpeza do log da conexao passada
	 * como parametro
	 *
	 * - Buscar na tabela log nos campos valorcampoalterado e valorcampoanterior que contem o valor "%negocio.comuns%";
	 * - Depois realizar um laco no resultado desta busca e verifica se os campos valorcampoalterado ou valorcampoanterior
 	 *   contem o valor 'negocio.comuns', se existir é chamado a funcao limparCampo;
 	 * - A funcao limparCampo() recebe o valor a ser limpo
 	 * - Apos realiza a limpeza dos campos se realiza as operacoes de atualizacao ou delecao
	 *
	 * Autor: Pedro Y. Saito
	 * Criado em 24/02/2011
	 */
	public void limparLog (Connection con) throws SQLException {
		PreparedStatement stm;
		String valorCampoAnteriorNovo = "";
		String valorCampoAlteradoNovo = "";
		StringBuilder sql = new StringBuilder();
		StringBuilder sqlUpDate = new StringBuilder();
		ResultSet rs = null;
					
		//Select de consulta os logs
		sql.append("select codigo, dataalteracao, valorcampoalterado, valorcampoanterior from log ");
		sql.append(" where valorcampoalterado ilike '%negocio.comuns%' or valorcampoanterior ilike '%negocio.comuns%' ");
		
		stm = con.prepareStatement(sql.toString());
		
		rs = stm.executeQuery();
		
		while (rs.next()) {
			
			//Setando os valores obtidos na pesquisa as variavel auxiliar
			valorCampoAnteriorNovo = rs.getString("valorcampoanterior").trim();
			//Setando os valores obtidos na pesquisa as variavel auxiliar
			valorCampoAlteradoNovo = rs.getString("valorcampoalterado").trim();
			
			//Inicio - Limpando os campos
				//Verificando se o campo contem a string "negocio.comuns"
				if (rs.getString("valorcampoanterior").indexOf("negocio.comuns") >= 0){
					//Chamada a funcao limpa campo que ira retornar o valor tratado ou vazio
					valorCampoAnteriorNovo = limparCampo(rs.getString("valorcampoanterior"));
				}
				//Verificando se o campo contem a string "negocio.comuns"
				if (rs.getString("valorcampoalterado").indexOf("negocio.comuns") >= 0){
					//Chamada a funcao limpa campo que ira retornar o valor tratado ou vazio
					valorCampoAlteradoNovo = limparCampo(rs.getString("valorcampoalterado"));
				}
			//Fim - Limpando os campos
			
			//Inicio - Verificando se é para excluir ou atualizar
			if ("".equals(valorCampoAnteriorNovo) && "".equals(valorCampoAlteradoNovo)){
				//Deletando a informacao quando nao existir dados
				System.out.println("Deletando: " + rs.getInt("codigo"));
				stm = con.prepareStatement("DELETE FROM log WHERE codigo = ?");
				int i = 1;						
				stm.setInt(i++, rs.getInt("codigo"));								
				stm.execute();
								
			} else if (!valorCampoAnteriorNovo.equals(rs.getString("valorcampoanterior").trim()) 
					|| !valorCampoAlteradoNovo.equals(rs.getString("valorcampoalterado").trim())){
											
				//Inicio - Atualizando os campos com os dados novos
					//Montando os campos que serao atualizados
					if (valorCampoAnteriorNovo != null && !"".equals(valorCampoAnteriorNovo)){
						sqlUpDate.append(" valorcampoanterior=? ");
					}
					if ( valorCampoAlteradoNovo != null && !"".equals( valorCampoAlteradoNovo)){
						if (valorCampoAnteriorNovo != null && !"".equals(valorCampoAnteriorNovo)){
							sqlUpDate.append(", valorcampoalterado=?");
						} else {
							sqlUpDate.append(" valorcampoalterado=?");
						}
					}
					
					//Verificando se existe campos a serem atualizados
					if (!"".equals(sqlUpDate.toString())){
						
						//Atualizando os campos
						System.out.println("Atualizando: " + rs.getInt("codigo"));
						stm = con.prepareStatement(("UPDATE log SET " + sqlUpDate.toString() + " WHERE codigo=?"));
						int i = 1;
						//Atribuindo os valores ao PreparedStatement
						if (valorCampoAnteriorNovo != null && !"".equals(valorCampoAnteriorNovo)){
							stm.setString(i++, valorCampoAnteriorNovo);
						}
						if (valorCampoAlteradoNovo != null && !"".equals(valorCampoAlteradoNovo)){
							stm.setString(i++, valorCampoAlteradoNovo);
						}						
						stm.setInt(i++, rs.getInt("codigo"));
						
						stm.execute();						
						
					}
				
				//Fim - Atualizando os campos com os dados novos
			}
			//Fim - Verificando se é para excluir ou atualizar
			
			
			//Inicializando a variaveis auxiliares
			valorCampoAnteriorNovo = "";
			valorCampoAlteradoNovo = "";
			sqlUpDate = new StringBuilder();
			
		}
	}
	
	/**
	 * Autor: Pedro Y. Saito
	 * Criado em 24/02/2011
	 */
	public static void main(String[] args) {
		try {
			new LimparLog();
		} catch (SQLException e) {
			System.out.println("Erro ao limpar log: " + e.getMessage());
		}
	}

}

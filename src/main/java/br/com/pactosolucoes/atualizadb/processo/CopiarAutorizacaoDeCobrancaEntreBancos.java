package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class CopiarAutorizacaoDeCobrancaEntreBancos {

    public static void main(String... args) throws Exception {
        //Informações bando de origem
        Connection conOrigem = DriverManager.getConnection("**********************************************unid2", "postgres", "pactodb");
        Integer convenioOrigem = 2;

        //Informações bando de destino
        Connection conDestino = DriverManager.getConnection("**********************************************", "postgres", "pactodb");
        Integer convenioDestino = 2;

        copiar(conOrigem, conDestino, convenioOrigem, convenioDestino);
    }

    private static void copiar(Connection conOrigem, Connection conDestino,
                               Integer convenioOrigem, Integer convenioDestino) {

        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteOrigem;
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDestino;
        ConvenioCobranca convenioCobrancaDAODestino;
        Usuario usuarioDAODestino;
        try {
            atualizarBanco(conDestino);

            convenioCobrancaDAODestino = new ConvenioCobranca(conDestino);
            autorizacaoCobrancaClienteOrigem = new AutorizacaoCobrancaCliente(conOrigem);
            autorizacaoCobrancaClienteDestino = new AutorizacaoCobrancaCliente(conDestino);
            usuarioDAODestino = new Usuario(conDestino);

            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAODestino.consultarPorChavePrimaria(convenioDestino, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("p.nome, \n");
            sql.append("p.cfp as cpf, \n");
            sql.append("cl.matricula, \n");
            sql.append("au.origem, \n");
            sql.append("au.codigo \n");
            sql.append("from autorizacaocobrancacliente au \n");
            sql.append("inner join cliente cl on cl.codigo = au.cliente \n");
            sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
            sql.append("where au.ativa \n");
            sql.append("and length(coalesce(au.tokenaragorn,'')) > 0 \n");
            sql.append("and length(coalesce(p.cfp,'')) > 0 \n");
            sql.append("and au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
            sql.append("and au.conveniocobranca = ").append(convenioOrigem).append(" \n");
            sql.append("order by cl.codigo \n");

            Integer total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conOrigem);
            Integer sucesso = 0;
            Integer erro = 0;
            Integer atual = 0;

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conOrigem);
            Map<String, Integer> mapErros = new HashMap<>();
            List<String> listaOrigemNaoImportado = new ArrayList<>();
            while (rs.next()) {
                String nome = "";
                String cpf = "";
                String msgFinal = "";
                boolean deuErro = false;
                try {
                    conDestino.setAutoCommit(false);

                    nome = rs.getString("nome");
                    cpf = rs.getString("cpf");
                    String matricula = rs.getString("matricula");
                    Integer autorizacao = rs.getInt("codigo");
                    Integer origem = rs.getInt("origem");

                    ClienteVO clienteVO = obterCliente(cpf, conDestino);

                    AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO = autorizacaoCobrancaClienteOrigem.consultarPorChavePrimaria(autorizacao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    autorizacaoCobrancaClienteVO.setCodigo(0);
                    autorizacaoCobrancaClienteVO.setNovoObj(true);
                    autorizacaoCobrancaClienteVO.setCliente(clienteVO);
                    autorizacaoCobrancaClienteVO.setConvenio(convenioCobrancaVO);
                    autorizacaoCobrancaClienteVO.setValidarBinCartao(false);
                    autorizacaoCobrancaClienteVO.setProcessoImportacao(true);

                    autorizacaoCobrancaClienteDestino.incluir(autorizacaoCobrancaClienteVO);

                    JSONObject json = new JSONObject();
                    json.put("matricula", matricula);
                    json.put("nome", nome);
                    json.put("cpf", cpf);
                    json.put("origemAutorizacao", origem);
                    json.put("bancoOrigem", conOrigem.getCatalog());
                    json.put("autorizacaoCobrancaClienteOrigem", autorizacao);
                    json.put("dataImportacao", Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMddHHmmss"));
                    SuperFacadeJDBC.executarUpdate("UPDATE autorizacaocobrancacliente SET importacaoobs = '" + json.toString() + "' where codigo = " + autorizacaoCobrancaClienteVO.getCodigo(), conDestino);

                    conDestino.commit();
                    sucesso++;
                    msgFinal = "SUCESSO";
                } catch (Exception ex) {
//                    ex.printStackTrace();
                    conDestino.rollback();
                    erro++;
                    msgFinal = ex.getMessage();
                    deuErro = true;

                    listaOrigemNaoImportado.add(cpf + " | " + nome + " | ERRO | " + ex.getMessage());

                    Integer totalErros = mapErros.get(ex.getMessage());
                    if (totalErros == null) {
                        totalErros = 0;
                    }
                    mapErros.put(ex.getMessage(), (totalErros + 1));

                } finally {
                    conDestino.setAutoCommit(true);
                    Uteis.logarDebug(++atual + "/" + total + " -->> " + (deuErro ? "ERRO | " : "") + "CPF | " + cpf + " | " + nome + " | " + msgFinal);
                }
            }

            Uteis.logarDebug("Total: " + total);
            Uteis.logarDebug("Sucesso: " + sucesso);
            Uteis.logarDebug("Erro: " + erro);
            Uteis.logarDebug("########################");
            Uteis.logarDebug("######### ERROS ########");
            Uteis.logarDebug("########################");
            for (String key : mapErros.keySet()) {
                Uteis.logarDebug(key + " --> Total de erros: " + mapErros.get(key));
            }
            Uteis.logarDebug("########################");
            Uteis.logarDebug("##### ALUNOS ERRO ######");
            Uteis.logarDebug("########################");
            for (String key : listaOrigemNaoImportado) {
                Uteis.logarDebug(key);
            }
            Uteis.logarDebug("########################");
            Uteis.logarDebug("######### FIM ##########");
            Uteis.logarDebug("########################");
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            autorizacaoCobrancaClienteOrigem= null;
            autorizacaoCobrancaClienteDestino = null;
            convenioCobrancaDAODestino = null;
            usuarioDAODestino = null;
        }
    }

    private static void atualizarBanco(Connection conDestino) {
        Set<String> atualiza = new HashSet<>();
        atualiza.add("DO $$ \n" +
                "    BEGIN\n" +
                "        BEGIN\n" +
                "            ALTER TABLE autorizacaocobrancacliente ADD COLUMN importacaoobs text;\n" +
                "        EXCEPTION\n" +
                "            WHEN duplicate_column THEN RAISE NOTICE 'Coluna importacaoobs já existe';\n" +
                "        END;\n" +
                "    END;\n" +
                "$$");
        for (String update : atualiza) {
            try {
                SuperFacadeJDBC.executarConsulta(update, conDestino);
            } catch (Exception ignored) {
            }
        }
    }

    private static ClienteVO obterCliente(String cpf, Connection conDestino) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("p.nome, \n");
        sql.append("p.codigo as pessoa, \n");
        sql.append("cl.codigo as cliente, \n");
        sql.append("cl.empresa, \n");
        sql.append("exists (select codigo from autorizacaocobrancacliente where cliente = cl.codigo and length(coalesce(importacaoobs,'')) > 0) as existeAuto \n");
        sql.append("from cliente cl \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
        sql.append("where 1 = 1 \n");
        sql.append("and (p.cfp = '").append(Uteis.formatarCpfCnpj(cpf, false)).append("' or p.cfp = '").append(Uteis.formatarCpfCnpj(cpf, true)).append("') \n");

        Integer total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conDestino);
        if (UteisValidacao.emptyNumber(total)) {
            throw new Exception("Nenhum aluno encontrado com o CPF");
        }
        if (total > 1) {
            throw new Exception("Mais de 1 aluno encontrado com o CPF");
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conDestino);
        if (rs.next()) {
            String nome = rs.getString("nome");
            if (rs.getBoolean("existeAuto")) {
                throw new Exception("Cliente de destino " + nome + " | já tem uma autorizacao importada");
            }

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setCodigo(rs.getInt("cliente"));
            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setCodigo(rs.getInt("pessoa"));
            clienteVO.setPessoa(pessoaVO);

            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(rs.getInt("empresa"));
            clienteVO.setEmpresa(empresaVO);
            return clienteVO;
        }
        throw new Exception("Erro consultar cliente");
    }
}
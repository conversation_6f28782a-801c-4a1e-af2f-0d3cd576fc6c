package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.PlanoProdutoSugeridoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.plano.PlanoProdutoSugerido;

public class GerarProdutoRenovacaoRecorrencia {

	
	public static void main(String[] args) {
		try {
			Connection con1 = DriverManager.getConnection("*****************************************", "postgres", "pactodb");
			corrigirContratosRenovadosSemProduto(con1);			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	public static void corrigirContratosRenovadosSemProduto(Connection con){
		try{
			System.out.println( "Início em : "+Calendario.hoje());
			StringBuilder sql = new StringBuilder();
			sql.append("select ZZZ from contrato \n");
			sql.append("where situacaocontrato  like 'RN' \n"); 
			sql.append("and contrato.codigo not in  \n");
			sql.append("(select distinct contrato from movproduto inner join produto on produto.codigo = movproduto.produto where produto.tipoproduto like 'RN') \n");
			sql.append("and regimerecorrencia ");
			
			Integer totalRegs = 0;
			ResultSet total = SuperFacadeJDBC.criarConsulta(sql.toString().replaceFirst("ZZZ", "count(*) as total "), con);
			if(total.next()){
				totalRegs = total.getInt("total");
			}
			
			ResultSet resultSet = SuperFacadeJDBC.criarConsulta(sql.toString().replaceFirst("ZZZ", "*"), con);
			int nr = 0;
			while(resultSet.next()){
				try{
					System.out.println(++nr+"/"+totalRegs);
					ResultSet parcela = SuperFacadeJDBC.criarConsulta(" SELECT * FROM movparcela WHERE descricao " +
		                      " LIKE 'PARCELA 1' AND contrato = "+resultSet.getInt("codigo"), con);
					if(parcela.next()){
						MovParcelaVO movParcelaVO = MovParcela.montarDados(parcela, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
						ContratoVO contrato = new Contrato(con).consultarPorChavePrimaria(resultSet.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
						MovProdutoVO movProdutoVO = criarProdutoRenovacao(contrato, movParcelaVO, con);
						new MovProduto(con).incluir(movProdutoVO);
						
						PreparedStatement stm = con.prepareStatement("INSERT INTO movprodutoparcela(movparcela, movproduto, valorpago) VALUES (?,?,?) ");
						stm.setInt(1, movParcelaVO.getCodigo());
						stm.setInt(2, movProdutoVO.getCodigo());
						stm.setDouble(3, movProdutoVO.getTotalFinal());
						
						stm.execute();
						
						if(movProdutoVO.getTotalFinal() > 0.0){
							SuperFacadeJDBC.executarConsulta("UPDATE movparcela SET valorparcela = "+(movParcelaVO.getValorParcela()+movProdutoVO.getTotalFinal()), con);
						}
					}else{
						continue;
					}
				}catch (Exception e) {
					System.out.println("Problema com o contrato "+resultSet.getInt("codigo")+": "+e.getMessage());
				}
			}
			System.out.println("Fim em : "+Calendario.hoje());
		}catch (Exception e) {
			e.printStackTrace();
		}
	}
	
    private static MovProdutoVO criarProdutoRenovacao(ContratoVO contrato, MovParcelaVO parcela, Connection con) throws Exception{
    	List<PlanoProdutoSugeridoVO> produtoSugeridos = new PlanoProdutoSugerido(con).consultarPlanoProdutoSugeridos(
    			contrato.getPlano().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
    	
    	for(PlanoProdutoSugeridoVO planoProdutoSugerido : produtoSugeridos){
    		if(planoProdutoSugerido.getProduto().getTipoProduto().equals("RN")){
    			MovProdutoVO movP = new MovProdutoVO();
    			movP.setProduto(planoProdutoSugerido.getProduto());
    			movP.setApresentarMovProduto(false);
    			movP.setContrato(contrato);
                movP.setDescricao(planoProdutoSugerido.getProduto().getDescricao());
                movP.setSituacao(parcela.getSituacao());
                movP.setEmpresa(contrato.getEmpresa());
                movP.setPessoa(contrato.getPessoa());
                movP.setMesReferencia(Uteis.getMesReferenciaData(contrato.getVigenciaDe()));
                movP.setQuantidade(1);
                movP.setAnoReferencia(Uteis.getAnoData(contrato.getVigenciaDe()));
                movP.setDataLancamento(contrato.getDataLancamento());
                movP.setDataInicioVigencia(null);
                movP.setDataFinalVigencia(contrato.getVigenciaAteAjustada());
                movP.setResponsavelLancamento(contrato.getResponsavelContrato());
                movP.setPrecoUnitario(planoProdutoSugerido.getValorProduto());
                movP.setTotalFinal(parcela.getSituacao().equals("EA") ?
                		           Uteis.arredondarForcando2CasasDecimaisMantendoSinal(planoProdutoSugerido.getValorProduto()) :
                		           0.0);
    			return movP;
    		}
    	}
    	return null;
    }
}

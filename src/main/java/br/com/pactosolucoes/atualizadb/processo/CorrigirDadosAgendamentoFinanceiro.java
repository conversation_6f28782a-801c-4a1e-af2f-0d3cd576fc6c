package br.com.pactosolucoes.atualizadb.processo;

import controle.arquitetura.threads.ThreadAgendamentoFinanceiro;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Joao Alcides on 12/04/2017.
 */
public class CorrigirDadosAgendamentoFinanceiro {

    public static void main(String... args) throws Exception {
        Connection con = DriverManager.getConnection("************************************************", "postgres", "pactodb");
        corrigir(con);
//        ThreadAgendamentoFinanceiro.gerarParcelasParaAgendamento(con, Calendario.hoje());
    }


    public static void corrigir(Connection con) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  distinct agendamentofinanceiro, a.descricao\r\n");
        sql.append("FROM movconta m\r\n");
        sql.append("inner join agendamentofinanceiro a on a.codigo = m.agendamentofinanceiro\r\n");
        sql.append("WHERE m.datalancamento > '2017-04-01' and m.datavencimento < '2017-04-01'\r\n");
        Map<String, List<Integer>> mapaAgendamentos = new HashMap<String, List<Integer>>();
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while(rs.next()){
            Integer agendamentofinanceiro = rs.getInt("agendamentofinanceiro");
            System.out.println("Modificando agendamento "+rs.getString("descricao"));
            StringBuilder agendadoslanc = new StringBuilder();
            agendadoslanc.append(" SELECT codigo, nrparcela FROM movconta where datalancamento > '2017-04-01' ");
            agendadoslanc.append(" AND dataquitacao is null ");
            agendadoslanc.append(" AND agendamentofinanceiro = ").append(agendamentofinanceiro);
            agendadoslanc.append(" order by datavencimento");

            ResultSet rsLanc = SuperFacadeJDBC.criarConsulta(agendadoslanc.toString(), con);
            List<Integer> parcelas = new ArrayList<Integer>();
            while(rsLanc.next()){
                SuperFacadeJDBC.executarConsulta("DELETE FROM movconta WHERE codigo = "+rsLanc.getInt("codigo"),
                        con);
                parcelas.add(rsLanc.getInt("nrparcela"));
            }

            PreparedStatement stm = con.prepareStatement("UPDATE agendamentofinanceiro SET vencimentoultimaparcela = ?, " +
                    "proximovencimento = ? WHERE codigo = ?");
            stm.setDate(1, Uteis.getDataJDBC(Calendario.hoje()));
            stm.setDate(2, Uteis.getDataJDBC(Uteis.somarDias(Calendario.hoje(), 1)));
            stm.setInt(3,agendamentofinanceiro);
            stm.execute();

            mapaAgendamentos.put("Agendamento: "+rs.getString("descricao")+"; Código: "+agendamentofinanceiro, parcelas);
        }
        String insert = "INSERT INTO log(nomeentidade, nomeentidadedescricao, nomecampo, " +
                "valorcampoalterado, dataalteracao, responsavelalteracao, operacao) \n"
                + "VALUES ('PROCESSO_AUTOMATICO', 'Processo', 'Agendamento Financeiro', ?, ?, " +
                "'PROCESSO', 'CORREÇÃO');";
        for(String agen : mapaAgendamentos.keySet()){
            List<Integer> lancamentos = mapaAgendamentos.get(agen);
            if(lancamentos.isEmpty()){
                continue;
            }
            Collections.sort(lancamentos);
            String parc = agen+" - As parcelas de "+lancamentos.get(0)+" até "+lancamentos.get(lancamentos.size() - 1)+" foram deletadas. O agendamento recorrente foi encerrado.";
            PreparedStatement stm = con.prepareStatement(insert);
            stm.setString(1, parc);
            stm.setDate(2, Uteis.getDataJDBC(Calendario.hoje()));
            stm.execute();
        }
    }

}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "03/10/2024",
        descricao = "Evitar Null Pointer Exception ao cancelar um contrato por conta da configuração de cancelamento na empresa",
        motivacao = "M1-2947")
public class AtualizacaoTicketM12937 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa SET tipoparcelacancelamento='TP' WHERE tipoparcelacancelamento IS NULL OR tipoparcelacancelamento = '';",c);
        }
    }
}

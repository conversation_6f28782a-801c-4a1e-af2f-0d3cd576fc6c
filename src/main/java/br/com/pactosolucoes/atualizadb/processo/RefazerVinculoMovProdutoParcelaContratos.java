/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class RefazerVinculoMovProdutoParcelaContratos {
  public static void main(String... args) {
        try {
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "pacto");
//            Connection c = DriverManager.getConnection("*********************************************", "zillyonweb", "pactodb");
            refazerMovProdutoParcelaContratos(c);

        } catch (Exception ex) {
            Logger.getLogger(RefazerVinculoMovProdutoParcelaContratos.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public static void refazerMovProdutoParcelaContratos(Connection con) throws Exception {
        refazerMovProdutoParcela(con, null);
    }

    public static void refazerMovProdutoParcela(Connection con, Integer codigoContrato) throws Exception {
        String sqlContratos = "  select distinct contrato, dividirprodutosnasparcelas, c.datalancamento  from ( \n" +
                "select *,abs(valorRelacioanento - valorparcela)::numeric as diferenca, (select sum(valorparcela::numeric) from movparcela where contrato = foo.contrato and situacao <> 'RG' ) as valorparcelas, " +
                "(select sum(m.totalfinal::numeric) from movproduto m inner join produto p on p.codigo = m.produto where contrato = foo.contrato and tipoproduto not in ('CD','DC', 'DE','DR','DV','FR')) as valorprodutos  from (select par.codigo as movparcela,dataregistro, valorparcela,pessoa, contrato,vendaavulsa, descricao,sum (coalesce(valorpago,0))as valorRelacioanento from movparcela par left join movprodutoparcela mpp on mpp.movparcela = par.codigo where par.situacao <> 'RG' \n" +
                "and par.contrato is not null and par.descricao not like 'PARCELA TRANSF.%' \n" +
                "  group by 1,2,3,4,5,6) as foo where dataregistro > '2019-01-01' and abs(valorRelacioanento - valorparcela)::numeric > 0.1 order by dataregistro desc,contrato \n" +
                "  ) as foo1 inner join contrato c on c.codigo = foo1.contrato where abs(valorparcelas - valorprodutos) < 0.02 ";
        if(UteisValidacao.notEmptyNumber(codigoContrato)){
            sqlContratos = "select codigo as contrato, dividirprodutosnasparcelas, datalancamento  from contrato where codigo = " + codigoContrato;
        }
        ResultSet rsContratos = SuperFacadeJDBC.criarConsultaRolavel(sqlContratos, con);
        StringBuffer sbErros = new StringBuffer("Resumo de Erros: \n");
        List<MovParcelaVO> listaParcelasTodas;
        List<MovParcelaVO> listaParcelasNormais;
        List<MovParcelaVO> listaParcelasRenegociadas;
        List<MovParcelaVO> listaParcelasAnuidade;
        List<MovParcelaVO> listaParcelasProdutos;
        List<MovParcelaVO> listaParcelasMatricula;
        List<MovParcelaVO> listaParcelasTraferenciaDias;
        List<MovParcelaVO> listaParcelasTrancamento;
        List<MovParcelaVO> listaParcelasEdicao;
        List<MovParcelaVO> listaParcelasHorario;
        List<MovParcelaVO> listaParcelasLancamentoColetivo;
        MovParcelaVO parcelaQuitacao;
        MovParcelaVO parcelaAlteracao;
        List<MovParcelaVO> listaParcelasOutros;
        List<MovParcelaVO> listaParcelasManutencao;
        List<MovParcelaVO> listaParcelasAdesao;
        List<MovParcelaVO> listaParcelasAlteracaoVencimento;

        List<MovProdutoVO> listaProdutosTodos;
        List<MovProdutoVO> listaProdutosPlano;
        List<MovProdutoVO> listaProdutosOutros;
        List<MovProdutoVO> listaProdutosManutencao;
        List<MovProdutoVO> listaProdutosTranferidos;
        List<MovProdutoVO> listaProdutosTrancamento;
        List<MovProdutoVO> listaProdutosHorario;
        List<MovProdutoVO> listaProdutosDevolucao;
        List<MovProdutoVO> listaProdutoAnuidade;
        List<MovProdutoVO> listaProdutoAdesao;
        List<MovProdutoVO> listaProdutoMatricula;
        List<MovProdutoVO> listaProdutosProdutos;
        List<MovProdutoVO> listaProdutosAlteracaoVencimento;
        MovProdutoVO produtoQuitacao;

        ZillyonWebFacade zwDAO = new ZillyonWebFacade(con);

        rsContratos.last();
        int total = rsContratos.getRow();
        rsContratos.beforeFirst();
        int i = 1;
        List<Integer> recibos;

        while (rsContratos.next()) {
            try {
                con.setAutoCommit(false);
                recibos = new ArrayList<Integer>();
                listaParcelasNormais = new ArrayList<MovParcelaVO>();
                listaParcelasRenegociadas = new ArrayList<MovParcelaVO>();
                listaParcelasAnuidade = new ArrayList<MovParcelaVO>();
                listaParcelasProdutos = new ArrayList<MovParcelaVO>();
                listaParcelasMatricula = new ArrayList<MovParcelaVO>();
                listaParcelasTraferenciaDias = new ArrayList<MovParcelaVO>();
                listaParcelasTrancamento = new ArrayList<MovParcelaVO>();
                listaParcelasEdicao = new ArrayList<MovParcelaVO>();
                listaParcelasHorario = new ArrayList<MovParcelaVO>();
                listaParcelasLancamentoColetivo = new ArrayList<MovParcelaVO>();
                parcelaQuitacao = null;
                parcelaAlteracao = null;
                listaParcelasOutros = new ArrayList<MovParcelaVO>();
                listaParcelasManutencao = new ArrayList<MovParcelaVO>();
                listaParcelasAdesao = new ArrayList<MovParcelaVO>();
                listaParcelasAlteracaoVencimento = new ArrayList<MovParcelaVO>();

                listaProdutosTodos = new ArrayList<MovProdutoVO>();
                listaProdutosPlano = new ArrayList<MovProdutoVO>();
                listaProdutosOutros = new ArrayList<MovProdutoVO>();
                listaProdutosManutencao = new ArrayList<MovProdutoVO>();
                listaProdutosTranferidos = new ArrayList<MovProdutoVO>();
                listaProdutosTrancamento = new ArrayList<MovProdutoVO>();
                listaProdutosHorario = new ArrayList<MovProdutoVO>();
                listaProdutosDevolucao = new ArrayList<MovProdutoVO>();
                listaProdutoAnuidade  = new ArrayList<MovProdutoVO>();
                listaProdutoAdesao  = new ArrayList<MovProdutoVO>();
                listaProdutoMatricula  = new ArrayList<MovProdutoVO>();
                listaProdutosProdutos  = new ArrayList<MovProdutoVO>();
                listaProdutosAlteracaoVencimento = new ArrayList<MovProdutoVO>();
                produtoQuitacao = null;

                boolean processar = true;
                System.out.println(i++ + " de " + total +" - contrato " + rsContratos.getInt("contrato"));
                listaParcelasTodas = zwDAO.getMovParcela().consultar("select * from movparcela where contrato = " + rsContratos.getInt("contrato") + " and situacao <> 'RG' order by codigo", Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                for (MovParcelaVO parcela : listaParcelasTodas) {
                    if (parcela.getDescricao().startsWith("PARCELA 0") || parcela.getDescricao().startsWith("PARCELA CANCELAMENTO")
                            || parcela.getDescricao().startsWith("PARCELA MULTA CANCELAMENTO")
                            || parcela.getDescricao().startsWith("PARCELA TRANSF.")
                            || parcela.getDescricao().startsWith("TAXA DE MANUTENÇÃO")) {
                        processar = false;
                        break;
                    } else if (parcela.getDescricao().contains(" L.C.")) {
                        listaParcelasLancamentoColetivo.add(parcela);
                    } else if (parcela.getDescricao().startsWith("PARCELA PRO RATA - ALTERAÇÃO VENCIMENTO")) {
                        listaParcelasAlteracaoVencimento.add(parcela);
                    } else if (parcela.getDescricao().startsWith("ADESÃO")) {
                        listaParcelasAdesao.add(parcela);
                    } else if (parcela.getDescricao().startsWith("ANUIDADE")) {
                        listaParcelasAnuidade.add(parcela);
                    } else if (parcela.getDescricao().startsWith("ALTERA -")) {
                        listaParcelasHorario.add(parcela);
                    } else if (parcela.getDescricao().startsWith("ALTERACÕES -")) {
                        parcelaAlteracao = parcela;
                    } else if (parcela.getDescricao().startsWith("MANUTEN")) {
                        listaParcelasManutencao.add(parcela);
                    } else if (parcela.getDescricao().startsWith("MATRÍCULA")) {
                        listaParcelasMatricula.add(parcela);
                    } else if (parcela.getDescricao().startsWith("PARCELA EDI")) {
                        listaParcelasEdicao.add(parcela);
                    } else if (parcela.getDescricao().startsWith("PARCELA RENEGOCIADA")) {
                        listaParcelasRenegociadas.add(parcela);
                    } else if (parcela.getDescricao().startsWith("PARCELA TRANSFER")) {
                        listaParcelasTraferenciaDias.add(parcela);
                    } else if (parcela.getDescricao().startsWith("PRODUTOS")) {
                        listaParcelasProdutos.add(parcela);
                    } else if (parcela.getDescricao().startsWith("QUITA")) {
                        parcelaQuitacao = parcela;
                    } else if (parcela.getDescricao().startsWith("TRANCAMENTO")) {
                        listaParcelasTrancamento.add(parcela);
                    } else {
                        listaParcelasNormais.add(parcela);
                    }
                    if(parcela.getSituacao().equals("PG")){
                        parcela.setReciboPagamento(zwDAO.getReciboPagamento().consultarPorParcela(parcela.getCodigo(),Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                        if(UteisValidacao.notEmptyNumber(parcela.getReciboPagamento().getCodigo()) && !recibos.contains(parcela.getReciboPagamento().getCodigo())){
                            recibos.add(parcela.getReciboPagamento().getCodigo());
                        }
                    }
                    SuperFacadeJDBC.executarUpdate("delete from movprodutoparcela where movparcela = "+parcela.getCodigo(),con);
                    parcela.setValorBaseCalculo(parcela.getValorParcela());

                }

                if (!processar || rsContratos.getBoolean("dividirprodutosnasparcelas")) { // depois IMPLEMENTAREI CASOS COM DIVISÃO DE PRODUTOS NAS PARCELAS
                        continue;
                }

                listaProdutosTodos = zwDAO.getMovProduto().consultar("select m.* from movproduto m inner join produto p on m.produto = p.codigo where contrato = " + rsContratos.getInt("contrato") + " and tipoproduto not in ('CD','DC', 'DE','DR','DV','FR') order by m.codigo ", Uteis.NIVELMONTARDADOS_VENDA);
                for (MovProdutoVO movProdutoVO : listaProdutosTodos) {
                    if(movProdutoVO.getDescricao().startsWith("PRO RATA - ALTERAÇÃO VENCIMENTO DE")){
                        listaProdutosAlteracaoVencimento.add(movProdutoVO);
                    } else if (movProdutoVO.getProduto().getTipoProduto().equals("AA")
                            || movProdutoVO.getProduto().getTipoProduto().equals("CP")
                            || movProdutoVO.getProduto().getTipoProduto().equals("AT")
                            || movProdutoVO.getProduto().getTipoProduto().equals("DI")
                            || movProdutoVO.getProduto().getTipoProduto().equals("MJ")
                            || movProdutoVO.getProduto().getTipoProduto().equals("PE")
                            || movProdutoVO.getProduto().getTipoProduto().equals("SE")
                            || movProdutoVO.getProduto().getTipoProduto().equals("SS")
                            || movProdutoVO.getProduto().getTipoProduto().equals("TN")
                            || movProdutoVO.getProduto().getTipoProduto().equals("TP")
                            || movProdutoVO.getProduto().getTipoProduto().equals("AC")) {
                        listaProdutosProdutos.add(movProdutoVO);
                    } else if (movProdutoVO.getProduto().getTipoProduto().equals("AH")) {
                        listaProdutosHorario.add(movProdutoVO);
                    } else if (movProdutoVO.getProduto().getTipoProduto().equals("RD")) {
                        listaProdutosDevolucao.add(movProdutoVO);
                    } else if (movProdutoVO.getProduto().getTipoProduto().equals("MA")
                            || movProdutoVO.getProduto().getTipoProduto().equals("RE")
                            || movProdutoVO.getProduto().getTipoProduto().equals("RN")) {
                        listaProdutoMatricula.add(movProdutoVO);
                    } else if (movProdutoVO.getProduto().getTipoProduto().equals("QU")) {
                        produtoQuitacao = movProdutoVO;
                    } else if (movProdutoVO.getProduto().getTipoProduto().equals("TA")) {
                        listaProdutoAnuidade.add(movProdutoVO);
                    } else if (movProdutoVO.getProduto().getTipoProduto().equals("TD")) {
                        listaProdutoAdesao.add(movProdutoVO);
                    } else if (movProdutoVO.getProduto().getTipoProduto().equals("MM")) {
                        listaProdutosManutencao.add(movProdutoVO);
                    } else if (movProdutoVO.getProduto().getTipoProduto().equals("TR")) {
                        listaProdutosTrancamento.add(movProdutoVO);
                    } else if (movProdutoVO.getProduto().getTipoProduto().equals("PM")) {
                        if (movProdutoVO.getDescricao().startsWith("PLANO TRANSFERIDO")) {
                            listaProdutosTranferidos.add(movProdutoVO);
                        } else {
                            listaProdutosPlano.add(movProdutoVO);
                        }
                    } else {
                        listaProdutosOutros.add(movProdutoVO);
                    }
                    movProdutoVO.setValorParcialmentePago(movProdutoVO.getTotalFinal());
                    movProdutoVO.setQuitado(false);

                }

                if (produtoQuitacao != null) {
                    if (parcelaQuitacao == null) {
                        listaProdutosOutros.add(produtoQuitacao);
                    } else {
                        incluirMPP(zwDAO,parcelaQuitacao.getCodigo(), produtoQuitacao.getCodigo(), parcelaQuitacao.getReciboPagamento().getCodigo(), produtoQuitacao.getTotalFinal());

                    }
                }

                if (!listaProdutosAlteracaoVencimento.isEmpty()){
                    if(listaParcelasAlteracaoVencimento.isEmpty()){
                        listaProdutosOutros.addAll(listaProdutosAlteracaoVencimento);
                    }else{
                        listaProdutosOutros.addAll(processarListaProdutosParcelas(zwDAO, listaProdutosAlteracaoVencimento,listaParcelasAlteracaoVencimento));
                    }
                }

                if (!listaProdutoAdesao.isEmpty()){
                    if(listaParcelasAdesao.isEmpty()){
                        listaProdutosOutros.addAll(listaProdutoAdesao);
                    }else{
                        listaProdutosOutros.addAll(processarListaProdutosParcelas(zwDAO, listaProdutoAdesao,listaParcelasAdesao));
                    }
                }
                if (!listaProdutoAnuidade.isEmpty()){
                    if(listaParcelasAnuidade.isEmpty()){
                        listaProdutosOutros.addAll(listaProdutoAnuidade);
                    }else{
                        listaProdutosOutros.addAll(processarListaProdutosParcelas(zwDAO, listaProdutoAnuidade,listaParcelasAnuidade));
                    }
                }
                if (!listaProdutoMatricula.isEmpty()){
                    if(listaParcelasMatricula.isEmpty()){
                        listaProdutosOutros.addAll(listaProdutoMatricula);
                    }else{
                        listaProdutosOutros.addAll(processarListaProdutosParcelas(zwDAO, listaProdutoMatricula,listaParcelasMatricula));
                    }
                }

                if (!listaProdutosHorario.isEmpty()){
                    if(listaParcelasHorario.isEmpty()){
                        listaProdutosOutros.addAll(listaProdutosHorario);
                    }else{
                        listaProdutosOutros.addAll(processarListaProdutosParcelas(zwDAO, listaProdutosHorario,listaParcelasHorario));
                    }
                }

                if (!listaProdutosTrancamento.isEmpty()){
                    if(listaParcelasTrancamento.isEmpty()){
                        listaProdutosOutros.addAll(listaProdutosTrancamento);
                    }else{
                        listaProdutosOutros.addAll(processarListaProdutosParcelas(zwDAO, listaProdutosTrancamento,listaParcelasTrancamento));
                    }
                }

                if (!listaProdutosProdutos.isEmpty()){
                    if(listaParcelasProdutos.isEmpty()){
                        listaProdutosOutros.addAll(listaProdutosProdutos);
                    }else{
                        listaProdutosOutros.addAll(processarListaProdutosParcelas(zwDAO, listaProdutosProdutos,listaParcelasProdutos));
                    }
                }

                if (!listaProdutosManutencao.isEmpty()){
                    if(listaParcelasManutencao.isEmpty()){
                        listaProdutosOutros.addAll(listaProdutosManutencao);
                    }else{
                        listaProdutosOutros.addAll(processarListaProdutosParcelas(zwDAO, listaProdutosManutencao,listaParcelasManutencao));
                    }
                }

                if (!listaProdutosTranferidos.isEmpty()){
                    if(listaParcelasTraferenciaDias.isEmpty()){
                        listaProdutosOutros.addAll(listaProdutosTranferidos);
                    }else{
                        listaProdutosOutros.addAll(processarListaProdutosParcelas(zwDAO, listaProdutosTranferidos,listaParcelasTraferenciaDias));
                    }
                }
                Ordenacao.ordenarLista(listaProdutosOutros, "dataLancamento");
                List<MovProdutoVO> produtosProcessar = new ArrayList<MovProdutoVO>();
                List<MovProdutoVO> produtosProcessarOutros = new ArrayList<MovProdutoVO>();
                for(MovProdutoVO produto : listaProdutosOutros){
                    if(Calendario.igual(produto.getDataLancamento(), rsContratos.getDate("datalancamento"))){
                        produtosProcessar.add(produto);
                    } else {
                        produtosProcessarOutros.add(produto);
                    }
                }
//                if(rsContratos.getBoolean("dividirprodutosnasparcelas")){

//                } else {
                    produtosProcessar.addAll(listaProdutosPlano);
//                }
                produtosProcessar.addAll(produtosProcessarOutros);
                listaParcelasNormais.addAll(listaParcelasRenegociadas);
                Ordenacao.ordenarLista(listaParcelasNormais,"dataVencimento");
                processarListaProdutosParcelas(zwDAO, produtosProcessar,listaParcelasNormais);
                processarSituacaoProdutosPagos(con,listaProdutosTodos);
                processarRecibos(con, recibos);
                con.commit();
            }catch (Exception e) {
                con.rollback();
                Uteis.logar(e, RefazerVinculoMovProdutoParcelaContratos.class);
            }finally {
                con.setAutoCommit(true);
            }
        }
    }

    private static void processarRecibos(Connection con, List<Integer> recibos) {
        for (Integer recibo: recibos) {
            ProdutosPagosServico.setarProdutosPagos(con, recibo);
        }
    }

    private static void processarSituacaoProdutosPagos(Connection con, List<MovProdutoVO> listaProdutosTodos) throws Exception {
      String situacao = "";
      for(MovProdutoVO produtoVO: listaProdutosTodos){
          if(produtoVO.getSituacao().equals("CA")){
              continue;
          }
          if(SuperFacadeJDBC.existe("select codigo from movprodutoparcela where movproduto = "+produtoVO.getCodigo()+" and recibopagamento is null", con)){
              situacao ="EA";
          } else {
              situacao = "PG";
          }
          SuperFacadeJDBC.executarUpdate("update movproduto set situacao = '"+situacao+"' where codigo = "+produtoVO.getCodigo(), con);
      }
    }

    private static List<MovProdutoVO> processarListaProdutosParcelas(ZillyonWebFacade zwDAO, List<MovProdutoVO> listaProdutos, List<MovParcelaVO> listaParcelas) throws Exception {
      for(MovProdutoVO produto : listaProdutos){
          if(produto.getSituacao().equals("CA")){
              for(MovParcelaVO parcelaVO : listaParcelas){
                  if(parcelaVO.getSituacao().equals("CA") && (Uteis.arredondarForcando2CasasDecimais(parcelaVO.getValorBaseCalculo()) > 0.0 ||
                          (Uteis.arredondarForcando2CasasDecimais(produto.getValorParcialmentePago()) == 0.00 && !produto.getQuitado()))){
                      processarProdutoParcela(zwDAO,produto,parcelaVO);
                      if(Uteis.arredondarForcando2CasasDecimais(produto.getValorParcialmentePago()) == 0.00 ){
                          break;
                      }
                  }
              }
          }
      }
        for(MovProdutoVO produto : listaProdutos){
            if(Uteis.arredondarForcando2CasasDecimais(produto.getValorParcialmentePago()) > 0.00 || !produto.getQuitado()){
                for(MovParcelaVO parcelaVO : listaParcelas){
                    if(Uteis.arredondarForcando2CasasDecimais(parcelaVO.getValorBaseCalculo()) > 0.0
                    || (Uteis.arredondarForcando2CasasDecimais(produto.getValorParcialmentePago()) == 0.00 && !produto.getQuitado())){
                        processarProdutoParcela(zwDAO,produto,parcelaVO);
                        if(Uteis.arredondarForcando2CasasDecimais(produto.getValorParcialmentePago()) == 0.00 ){
                            break;
                        }
                    }
                }
            }
        }
        List<MovProdutoVO> naoProcessado = new ArrayList<MovProdutoVO>();
        for(MovProdutoVO produto : listaProdutos){
            if(Uteis.arredondarForcando2CasasDecimais(produto.getValorParcialmentePago()) > 0.00){
                naoProcessado.add(produto);
            }
        }
        return naoProcessado;

    }

    private static void processarProdutoParcela(ZillyonWebFacade zwDAO, MovProdutoVO produto, MovParcelaVO parcelaVO) throws Exception {
        if(Uteis.arredondarForcando2CasasDecimais(parcelaVO.getValorBaseCalculo()) >= Uteis.arredondarForcando2CasasDecimais(produto.getValorParcialmentePago())){
            incluirMPP(zwDAO, parcelaVO.getCodigo(), produto.getCodigo(), parcelaVO.getReciboPagamento().getCodigo(), produto.getValorParcialmentePago());
            parcelaVO.setValorBaseCalculo(Uteis.arredondarForcando2CasasDecimais(parcelaVO.getValorBaseCalculo() - produto.getValorParcialmentePago()));
            produto.setValorParcialmentePago(0.0);
            produto.setQuitado(true);
        } else {
            incluirMPP(zwDAO, parcelaVO.getCodigo(), produto.getCodigo(), parcelaVO.getReciboPagamento().getCodigo(), parcelaVO.getValorBaseCalculo());
            produto.setValorParcialmentePago(Uteis.arredondarForcando2CasasDecimais(produto.getValorParcialmentePago() - parcelaVO.getValorBaseCalculo()));
            parcelaVO.setValorBaseCalculo(0.0);
        }
    }

    private static void incluirMPP(ZillyonWebFacade zwDAO, Integer parcela, Integer produto, Integer recibo, Double valorPago) throws Exception {
      MovProdutoParcelaVO mpp = new MovProdutoParcelaVO();
      mpp.setValorPago(valorPago);
      mpp.setMovProduto(produto);
      mpp.setMovParcela(parcela);
      mpp.getReciboPagamento().setCodigo(recibo);
      zwDAO.getMovProdutoParcela().incluir(mpp);
    }
}


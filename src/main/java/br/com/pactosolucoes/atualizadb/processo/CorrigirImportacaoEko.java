/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.SimpleTimeZone;
import java.util.TimeZone;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.jdom.Element;
import test.simulacao.LeitorXML;

/**
 *
 * <AUTHOR>
 */
public class CorrigirImportacaoEko {

    public static void main(String... args) throws Exception {
        LeitorXML leitor = new LeitorXML();
        Connection con = DriverManager.getConnection("************************************", "zillyonweb", "pactodb");
        //montar registros em lista
        List<Element> registros = leitor.lerXML("D:\\Pagamentos.xml");
        int andamento = 0;
        for (Element e : registros) {
            Integer duracao = Integer.valueOf(retirarNulo(e, "duracao"));
            Integer idRecebe = Integer.valueOf(retirarNulo(e, "id_recebe"));
            Date dataFinal = formataData(retirarNulo(e, "finalcontratoajustado"));
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT * FROM contrato WHERE id_externo = " + idRecebe, con);

            SuperFacadeJDBC.executarConsulta("DELETE FROM contratooperacao WHERE descricaocalculo LIKE 'OPERAÇÃO DE BÔNUS DE REDUÇÃO IMPORTAÇÃO';"
            +"DELETE FROM contratooperacao WHERE descricaocalculo LIKE 'OPERAÇÃO DE BÔNUS DE ACRÉSCIMO IMPORTAÇÃO';", con);

            StringBuilder sql = new StringBuilder();
            sql.append(" UPDATE contrato SET dataprevistarenovar = ?, ");
            sql.append(" dataprevistarematricula = ?, ");
            sql.append(" vigenciaate = ?, ");
            sql.append(" vigenciaateajustada = ? ");
            sql.append(" where codigo = ? ");
            
            if(rs.next()){
                int codigoContrato = rs.getInt("codigo");
                SuperFacadeJDBC.executarConsulta("UPDATE contratoduracao SET numeromeses = "+duracao+" WHERE contrato = "+codigoContrato, con);
                PreparedStatement stm = con.prepareStatement(sql.toString());
                for(int i = 1; i<5; i++){
                    stm.setDate(i, Uteis.getDataJDBC(dataFinal));
                }
                stm.setInt(5, codigoContrato);
                stm.execute();

                SuperFacadeJDBC.executarConsulta("UPDATE periodoacessocliente SET datafinalacesso = '"+Uteis.getDataJDBC(dataFinal)
                        +"' WHERE contrato = "+codigoContrato, con);


                arrumarMovProdutos(con, duracao, codigoContrato, rs.getDouble("valorfinal"));
                System.out.println(++andamento+" - contrato "+codigoContrato+" atualizado.");
            }


        }

    }

    private static void arrumarMovProdutos(Connection con, Integer duracao, Integer codigoContrato, Double valor) throws Exception{
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT * from movproduto WHERE contrato = " + codigoContrato + " AND descricao like '%PLANO IMPOR%' order by codigo", con);
        List<MovProdutoVO> movprodutos = MovProduto.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
        int nr = 1;
        Double novoValorPorMes = valor/duracao;
        for(MovProdutoVO vo : movprodutos ){
            if(nr <= duracao){
                SuperFacadeJDBC.executarConsulta("UPDATE movproduto SET totalfinal = "+novoValorPorMes+", precounitario = "+novoValorPorMes
                        +" WHERE codigo = "+vo.getCodigo()+";"
                        + " UPDATE movprodutoparcela SET valorpago = "+novoValorPorMes+" where movproduto = "+vo.getCodigo(), con);
            }else{
                SuperFacadeJDBC.executarConsulta("DELETE FROM movprodutoparcela WHERE movproduto = "+vo.getCodigo()+";"
                        + " DELETE FROM movproduto where codigo = "+vo.getCodigo(), con);
            }
            nr++;
        }
    }


    public static String retirarNulo(Element e, String campo) {
        try {
            return retirarNull(e.getAttributeValue(campo));
        } catch (Exception ie) {
            return null;
        }


    }

    public static String retirarNull(String valor) {
        if (valor.equals("null") || valor.equals("")) {
            return null;
        } else {
            return valor;
        }
    }

    public static Date formataData(String data) throws Exception {
        TimeZone tzone = new SimpleTimeZone(-3 * 60 * 60 * 1000, "Brazil");
        TimeZone.setDefault(tzone);
        Date datamenor = new Date();
        Date datamaior = new Date();
        String dataSistema = "01/01/1900";
        String dataMaiorSistema = "01/01/2100";
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        try {
            datamenor = df.parse(dataSistema);
            datamaior = df.parse(dataMaiorSistema);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        // verificar se data é nula
        if (data == null) {
            return null;
        } else {
            try {
                // converter data
                DateFormat formatter = new SimpleDateFormat("yyyyMMdd HH:mm");
                data = data.replaceAll("T", " ");
                if (data.length() > 14) {
                    data = data.substring(0, 14);
                }
                formatter.setLenient(false);
                Date date = (Date) formatter.parse(data);
                //verificar se a data é válida
                if (date.before(datamenor) || date.after(datamaior)) {
                    return null;
                }
                // retornar data
                if (data.length() > 8) {
                    date = Uteis.getDateTime(date, Integer.parseInt(data.substring(9, 11)), Integer.parseInt(data.substring(12, 14)), 0);
                }
                return date;
            } catch (ParseException e) {
                try {
                    // converter data
                    DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
                    formatter.setLenient(false);
                    Date date = (Date) formatter.parse(data);
                    //verificar se a data é válida
                    if (date.before(datamenor) || date.after(datamaior)) {
                        return null;
                    }
                    // retornar data
                    return date;
                    // caso existam erros na formatação, retornar null
                } catch (ParseException ex) {
                    try {
                        // converter data
                        DateFormat formatter = new SimpleDateFormat("yyyyMMdd");
                        formatter.setLenient(false);
                        Date date = (Date) formatter.parse(data);
                        //verificar se a data é válida
                        if (date.before(datamenor) || date.after(datamaior)) {
                            return null;
                        }
                        // retornar data
                        return date;
                    } catch (ParseException exc) {
                        return null;
                    }
                }
            }

        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Franco de Sá Feitosa",
        data = "20/03/2025",
        descricao = "Criação de novos campos na configuração de cadastro de cliente simplificado, o que permitirá cadastrar o responsável financeiro junto ao aluno",
        motivacao = "GCM-25: Campo de responsável financeiro ao incluir um novo aluno no BD")
public class AtualizacaoTicketGCM25 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente, visitante, validarcatraca) " +
                            "VALUES ('Nome Resp. Financeiro', false, false, false, false, false);", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente, visitante, validarcatraca) " +
                            "VALUES ('CPF Resp. Financeiro', false, false, false, false, false);", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente, visitante, validarcatraca) " +
                            "VALUES ('RG Resp. Financeiro', false, false, false, false, false);", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "INSERT INTO configuracaosistemacadastrocliente (nome, obrigatorio, mostrar, pendente, visitante, validarcatraca) " +
                            "VALUES ('E-mail Resp. Financeiro', false, false, false, false, false);", c);
        }
    }
}

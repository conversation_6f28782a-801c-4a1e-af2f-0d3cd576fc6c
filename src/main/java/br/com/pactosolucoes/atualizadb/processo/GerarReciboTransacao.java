package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.apf.APF;
import servicos.impl.apf.AprovaFacilService;
import servicos.impl.apf.RecorrenciaService;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class GerarReciboTransacao {

    public static void main(String ... args) throws Exception{
        Connection con = DriverManager.getConnection("*************************************************************", "postgres", "pactodb");
        Conexao.guardarConexaoForJ2SE(con);
//        gerarReciboDaTransacao(con, 3505, true);
//        for (Integer idEmpresa: obterIdEmpresasPorConvenio(con, 6)){
//            atualizarPeloArquivoRetorno(con, idEmpresa);
//        }
    }

    public static void processarTodasAbertas(Connection con){
        try {
            ResultSet rs = SuperEntidade.criarConsulta("select * from CartaoCredito where movpagamento = '33219'", con);
            //String transacoes = "";
            while(rs.next()){
                int codigo = rs.getInt("codigo");
                String data = rs.getString("datacompesancao");
                System.out.println("codigo " + codigo + " data " + data);
            }
            //incluirLog(transacoes, con);
        }catch (Exception e){
            Uteis.logar(e, GerarReciboTransacao.class);
        }
    }

    public static List<Integer> obterIdEmpresasPorConvenio(Connection con, int codigoConvenioCobranca){
        List<Integer> empresas = new ArrayList<>();
        try {
            ResultSet rs = SuperEntidade.criarConsulta("select empresa from conveniocobrancaempresa cce left join conveniocobranca cc on cce.conveniocobranca=cc.codigo where cce.conveniocobranca="+codigoConvenioCobranca, con);
            while (rs.next()) {
                int empresaId = rs.getInt("empresa");
                empresas.add(empresaId);
            }
        }catch (Exception e){
            Uteis.logar(e, GerarReciboTransacao.class);
        }
        return empresas;
    }

    public static void atualizarPeloArquivoRetorno(Connection con, int empresa){
        try {
            ResultSet rs = SuperEntidade.criarConsulta("select codigo, situacao, paramsresposta from transacao where dataprocessamento between '2019-09-01' and '2019-10-01' and empresa = "+empresa+" order by dataprocessamento desc", con);
            String transacoes = "";
            while (rs.next()) {
                TransacaoVO transacaoVO = new TransacaoVO();
                transacaoVO.setCodigo(rs.getInt("codigo"));
                transacaoVO.setSituacao(SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rs.getInt("situacao")));
                transacaoVO.setParamsResposta(rs.getString("paramsresposta"));

                List<ObjetoGenerico> listaParametros = Uteis.obterListaParametrosValores(transacaoVO.getParamsResposta());
                String valueRspnRsn = getValueObjetoGenerico(listaParametros, "RspnRsn");

                if(valueRspnRsn!=null){
                    Integer codigoSituacao = Integer.valueOf(valueRspnRsn);
                    if(valueRspnRsn!=null){
                        if(problemaCodigoSitucao(codigoSituacao, transacaoVO.getSituacao().getId())) {
                            transacaoVO.setSituacao(SituacaoTransacaoEnum.getSituacaoTransacaoEnum(getSituacaoTransacao(codigoSituacao)));
                            SuperEntidade.executarConsulta("update transacao set situacao = "+transacaoVO.getSituacao().getId()+" where empresa = "+empresa+" and codigo = "+transacaoVO.getCodigo(), con);
                            transacoes += transacaoVO.getCodigo() + "|";
                        }
                    }
                }
            }
            incluirLog(transacoes, con);
        }catch (Exception e){
            Uteis.logar(e, GerarReciboTransacao.class);
        }
    }

    private static boolean problemaCodigoSitucao(int codigo, int situacao){
        if(situacao==4 || situacao==6){
            if(codigo!=0000)
                return true;
        }else if(situacao==0){
            if(codigo!=0)
                return true;
        }
        return false;
    }

    private static int getSituacaoTransacao(int codigo){
        if (codigo != 0){
//            case 1000:
                return 3;
//            case 0:
//                return 0;
        }
        return codigo;
    }

    private static String getValueObjetoGenerico(List<ObjetoGenerico> listaParametros, String attr){
        for (ObjetoGenerico obj: listaParametros){
            if(obj.getAtributo().equalsIgnoreCase(attr)){
                return obj.getValor();
            }
        }
        return null;
    }

    public static void incluirLog(String transacoes, Connection c) {
        LogVO obj = new LogVO();
        obj.setChavePrimaria("0");
        obj.setNomeEntidade("");
        obj.setNomeEntidadeDescricao("PROCESSO DE TRANSAÇÕES APROVADAS");
        obj.setOperacao("EXECUCAO_PROCESSO_TRANSACOES");
        obj.setResponsavelAlteracao("EXECUTAR PROCESSOS");
        obj.setNomeCampo("TRANSACOES");
        obj.setDataAlteracao(Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado(transacoes);

        try {
            Log dao = new Log(c);
            dao.incluirSemCommit(obj);
        } catch (Exception ignored) {

        }
    }

    public static Integer gerarReciboDaTransacao(Connection con, Integer codigoTransacao, boolean utilizarDataProcessamento) throws Exception {
        Transacao transacaoDAO;
        ConvenioCobranca convenioCobrancaDAO;
        Empresa empresaDAO;
        try {
            transacaoDAO = new Transacao(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            empresaDAO = new Empresa(con);

            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(codigoTransacao);
            transacaoVO.setConvenioCobrancaVO(convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            transacaoVO.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
            ReciboPagamentoVO reciboPagamentoVO = transacaoDAO.gerarReciboTransacao(transacaoVO, null, utilizarDataProcessamento);
            return reciboPagamentoVO.getCodigo();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            transacaoDAO = null;
            convenioCobrancaDAO = null;
            empresaDAO = null;
        }
    }

    public static Integer gerarReciboDaTransacao_ANTIGO(Connection con, Integer codigoTransacao){
        try {
            Transacao transacaoDAO = new Transacao(con);
            Cliente clienteDAO = new Cliente(con);
            Contrato contratoDAO = new Contrato(con);
            OperadoraCartao opDao = new OperadoraCartao(con);

            AutorizacaoCobrancaCliente autoDao = new AutorizacaoCobrancaCliente(con);
            TransacaoMovParcela transacaoMovParcelaVO = new TransacaoMovParcela(con);
            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(codigoTransacao);
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(transacaoVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<TransacaoMovParcelaVO> transacaoMovParcelaVOS = transacaoMovParcelaVO.cosultarPorCodigoTransacao(codigoTransacao);
            List<MovParcelaVO> parcelas = new ArrayList<>();
            Integer codigoContrato = null;
            OperadorasExternasAprovaFacilEnum operadorasExternasAprovaFacilEnum = null;
            for(TransacaoMovParcelaVO t : transacaoMovParcelaVOS ){
                codigoContrato = t.getMovParcela().getCodigoContrato();
                parcelas.add(t.getMovParcela());
            }
            ContratoVO contratoVO = new ContratoVO();
            if (codigoContrato != null && codigoContrato > 0) {
                contratoVO = contratoDAO.consultarPorChavePrimaria(codigoContrato, Uteis.NIVELMONTARDADOS_TODOS);
            }else if(parcelas != null && !parcelas.isEmpty()){
                contratoVO.setPessoa(parcelas.get(0).getPessoa());
                contratoVO.setEmpresa(parcelas.get(0).getEmpresa());
            }

            OperadoraCartaoVO operadoraCartaoVO = null;
            List<AutorizacaoCobrancaClienteVO> autorizacaoCobrancaClienteVOS = autoDao.consultarPorPessoaTipoAutorizacao(contratoVO.getPessoa().getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_TODOS);
            if(autorizacaoCobrancaClienteVOS != null && !autorizacaoCobrancaClienteVOS.isEmpty()){
                operadorasExternasAprovaFacilEnum = autorizacaoCobrancaClienteVOS.get(0).getOperadoraCartao();

                operadoraCartaoVO = opDao.consultarPorCodigoIntegracaoStoneOnline(autorizacaoCobrancaClienteVOS.get(0).getOperadoraCartao(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);

                if(operadoraCartaoVO == null){
                    operadoraCartaoVO = opDao.consultarPorCodigoIntegracaoVindi(autorizacaoCobrancaClienteVOS.get(0).getOperadoraCartao(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
                }

                if(operadoraCartaoVO == null){
                    operadoraCartaoVO = opDao.consultarPorCodigoIntegracaoCielo(autorizacaoCobrancaClienteVOS.get(0).getOperadoraCartao(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
                }

                if(operadoraCartaoVO == null){
                    operadoraCartaoVO = opDao.consultarPorCodigoIntegracaoERede(autorizacaoCobrancaClienteVOS.get(0).getOperadoraCartao(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
                }

                if(operadoraCartaoVO == null){
                    operadoraCartaoVO = opDao.consultarPorCodigoIntegracaoGetNet(autorizacaoCobrancaClienteVOS.get(0).getOperadoraCartao(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
                }

                if(operadoraCartaoVO == null){
                    operadoraCartaoVO = opDao.consultarPorCodigoIntegracaoMundiPagg(autorizacaoCobrancaClienteVOS.get(0).getOperadoraCartao(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
                }

                if(operadoraCartaoVO == null){
                    operadoraCartaoVO = opDao.consultarPorCodigoIntegracaoPagarMe(autorizacaoCobrancaClienteVOS.get(0).getOperadoraCartao(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
                }
            }

            RecorrenciaService recorrenciaService = new RecorrenciaService(con);
            List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setFormaPagamento(recorrenciaService.getFormaPagamento());
            movPagamentoVO.setMovPagamentoEscolhida(true);
            movPagamentoVO.setValor(transacaoVO.getValor());
            movPagamentoVO.setValorTotal(transacaoVO.getValor());
            movPagamentoVO.setPessoa(transacaoVO.getPessoaPagador());
            movPagamentoVO.setNomePagador(transacaoVO.getPessoaPagador().getNome());
            movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
            movPagamentoVO.setNrParcelaCartaoCredito(1);
            movPagamentoVO.setOperadoraCartaoVO( operadoraCartaoVO);
            movPagamentoVO.setResponsavelPagamento(recorrenciaService.getUsuario());
            movPagamentoVO.setNsu(transacaoVO.getNSU());
            movPagamentoVO.setDataCobrancaTransacao(transacaoVO.getDataProcessamento());
            movPagamentoVO.setDataLancamento(transacaoVO.getDataProcessamento());
            movPagamentoVO.setDataPagamento(transacaoVO.getDataProcessamento());
            movPagamentoVO.setDataQuitacao(transacaoVO.getDataProcessamento());
            movPagamentoVO.setEmpresa(clienteVO.getEmpresa());
            Adquirente adquirenteDAO = null;
            try {
                adquirenteDAO = new Adquirente(recorrenciaService.getCon());
                movPagamentoVO.setAdquirenteVO(adquirenteDAO.obterAdquirenteTransacao(transacaoVO));
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                adquirenteDAO = null;
            }

            listaPagamento.add(movPagamentoVO);
            ReciboPagamentoVO reciboObj = recorrenciaService.getMovPagamentoDAO().incluirListaPagamento(
                    listaPagamento,
                    parcelas,
                    null,
                    contratoVO,
                    false, 0.0);
            transacaoVO.setReciboPagamento(reciboObj.getCodigo());
            transacaoVO.setMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo());
            transacaoVO.setCodigoExterno(transacaoVO.getValorAtributoResposta(APF.Transacao));
            transacaoVO.setBandeiraPagamento(operadorasExternasAprovaFacilEnum);
            AprovaFacilService aprovacaoService = new AprovaFacilService(con);
            aprovacaoService.getTransacaoFacade().alterar(transacaoVO);

            return reciboObj.getCodigo();
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public static Integer povoarPagamentoSemOperadora(Connection con, Integer reciboPagamento) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("tr.codigo as transacao, \n");
            sql.append("mp.* \n");
            sql.append("from movpagamento mp \n");
            sql.append("inner join formapagamento fp on fp.codigo = mp.formapagamento \n");
            sql.append("inner join transacao tr on tr.movpagamento = mp.codigo \n");
            sql.append("where fp.tipoformapagamento = 'CA' \n");
            sql.append("and mp.operadoracartao is null \n");
            sql.append("and tr.conveniocobranca is not null \n");
            if (!UteisValidacao.emptyNumber(reciboPagamento)) {
                sql.append("and mp.reciboPagamento = ").append(reciboPagamento).append(" \n");
            }

            int ajustado = 0;
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {

                Transacao transacaoDAO = null;
                ConvenioCobranca convenioCobrancaDAO = null;
                OperadoraCartao operadoraDAO = null;
                try {
                    transacaoDAO = new Transacao(con);
                    convenioCobrancaDAO = new ConvenioCobranca(con);
                    operadoraDAO = new OperadoraCartao(con);

                    Integer transacao = rs.getInt("transacao");
                    Integer movpagamento = rs.getInt("codigo");

                    TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);
                    if (!UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                        transacaoVO.setConvenioCobrancaVO(convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    } else {
                        Uteis.logarDebug("Transação não tem convenio... | Transação " + transacaoVO.getCodigo());
                        continue;
                    }

                    OperadorasExternasAprovaFacilEnum operadorasExternasAprovaFacilEnum = OperadorasExternasAprovaFacilEnum.valueOf(transacaoVO.getBandeira());
                    if (operadorasExternasAprovaFacilEnum == null) {
                        Uteis.logarDebug("OperadorasExternasAprovaFacilEnum não encontrada... | Transação " + transacaoVO.getCodigo());
                        continue;
                    }

                    OperadoraCartaoVO operadoraCartaoVO = operadoraDAO.consultarOuCriaPorCodigoIntegracao(transacaoVO.getConvenioCobrancaVO().getTipo(), 1, operadorasExternasAprovaFacilEnum);
                    if (operadoraCartaoVO != null && !UteisValidacao.emptyNumber(operadoraCartaoVO.getCodigo())) {
                        String update1 = "update movpagamento set operadoracartao = " + operadoraCartaoVO.getCodigo() + " where codigo = " + movpagamento;
                        SuperFacadeJDBC.executarUpdate(update1, con);
                        String update2 = "update cartaocredito set operadoracartao = (select mp.operadoracartao from movpagamento mp where mp.codigo = movpagamento) where operadoracartao is null and movpagamento = " + movpagamento;
                        SuperFacadeJDBC.executarUpdate(update2, con);
                        ++ajustado;
                    } else {
                        Uteis.logarDebug("OperadoraCartaoVO não encontrada... | Transação " + transacaoVO.getCodigo());
                        continue;
                    }

                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    operadoraDAO = null;
                    convenioCobrancaDAO = null;
                    transacaoDAO = null;
                }
            }

            return ajustado;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import importador.LeitorExcel2010;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Date;
import java.util.List;

public class AjustarImportacaoAcquaforme {

    public static void main(String[] args) throws SQLException {
        System.out.println("Início em : " + new Date());

        Connection con = DriverManager.getConnection("*******************************************************", "postgres", "pactodb");
        con.setAutoCommit(true);
        try {
            corrigirEndereco("/home/<USER>/Downloads/Cadastro_Alunos_08082018.xlsx", con);

        } catch (Exception e) {
            con.setAutoCommit(true);
            e.printStackTrace();
        } finally {
            con.setAutoCommit(true);
        }
        System.out.println("Fim em : " + new Date());
    }

    private static void corrigirEndereco(final String caminhoArquivo, Connection con) throws Exception {
        List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(caminhoArquivo);

        String updateEndereco = "UPDATE endereco SET numero = '%s' WHERE pessoa IN (SELECT codigo FROM pessoa WHERE nome = '%s');";
        for (XSSFRow linha : linhas) {
            String endereco = LeitorExcel2010.obterString(linha, 12);
            String[] partesEndereco = endereco.split(" ");
            String numeroEndereco = "";
            for (String parte : partesEndereco) {
                if (Uteis.isNumeroValido(parte)) {
                    numeroEndereco = parte;
                    break;
                }
            }
            if (!UteisValidacao.emptyString(numeroEndereco)) {
                String nomeCliente = LeitorExcel2010.obterString(linha, 2);

                Statement stm = con.createStatement();
                stm.execute(String.format(updateEndereco, numeroEndereco, nomeCliente.toUpperCase().replace("'", "''")));
            }
        }
    }
}

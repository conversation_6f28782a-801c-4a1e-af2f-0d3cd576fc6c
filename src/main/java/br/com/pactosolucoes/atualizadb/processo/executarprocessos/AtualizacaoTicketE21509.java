package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luiz Felipe",
        data = "20/01/2025",
        descricao = "E2-1509 - Arquivo data registro",
        motivacao = "E2-1509 - Arquivo data registro")
public class AtualizacaoTicketE21509 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE arquivo ADD COLUMN dataRegistro TIMESTAMP WITHOUT TIME ZONE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE atestado ADD COLUMN dataRegistro TIMESTAMP WITHOUT TIME ZONE;", c);
        }
    }
}

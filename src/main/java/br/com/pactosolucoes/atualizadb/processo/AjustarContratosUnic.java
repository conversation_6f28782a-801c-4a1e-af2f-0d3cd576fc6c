package br.com.pactosolucoes.atualizadb.processo;

import importador.LeitorExcel2010;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 19/12/2017.
 */
public class AjustarContratosUnic {


    private static final int COLUNA_MATRICULA_ALUNO = 0;
    private static final int COLUNA_VENCIMENTO_CONTRATO = 2;
    private static final int COLUNA_OBSERVACAO = 5;

    public static void main(String[] args) {
        try {
            String pathExcel = "/opt/ANÁLISE BANCO - 2.xlsx";
            Connection connection = new Conexao("***********************************************************", "postgres", "pactodb").getConexao();
            Conexao.guardarConexaoForJ2SE("bdzillyonunicespacodemeta", connection);

            ajustarContratos(pathExcel, connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void ajustarContratos(String pathExcel_plano, Connection connection) throws Exception {
        List<XSSFRow> linhasPlano = LeitorExcel2010.lerLinhas(pathExcel_plano, 1);
        int i = 0;

        String sqlConsultarPessoa = "SELECT codigo, pessoa FROM cliente WHERE codigomatricula = ?";
        String sqlConsultarContrato = "SELECT codigo FROM contrato WHERE pessoa = ? ORDER BY codigo DESC limit 1";
        String sqlAtualizarContrato = "UPDATE contrato SET vigenciaAteAjustada = ? WHERE codigo = ?";
        String sqlAtualizarPeriodoAcessoCliente = "UPDATE periodoacessocliente SET datafinalacesso = ? WHERE contrato = ?";

        for (XSSFRow linha : linhasPlano) {
            try {
                int codigoMatricula = LeitorExcel2010.obterNumero(linha, COLUNA_MATRICULA_ALUNO).intValue();
                Date vencimentoContrato = LeitorExcel2010.obterDataNoFormatoData(linha, COLUNA_VENCIMENTO_CONTRATO);
                String obs = LeitorExcel2010.obterString(linha, COLUNA_OBSERVACAO);

                if (obs.contains("VENCIMENTO")) {
                    PreparedStatement ps = connection.prepareStatement(sqlConsultarPessoa);
                    ps.setInt(1, codigoMatricula);
                    ResultSet rs = ps.executeQuery();
                    int codPessoa = 0;
                    int codCliente = 0;
                    if (rs.next()) {
                        codCliente = rs.getInt("codigo");
                        codPessoa = rs.getInt("pessoa");
                    }
                    finalizar(ps, rs);


                    ps = connection.prepareStatement(sqlConsultarContrato);
                    ps.setInt(1, codPessoa);
                    rs = ps.executeQuery();
                    int codContrato = 0;
                    if (rs.next()) {
                        codContrato = rs.getInt("codigo");
                    }
                    finalizar(ps, rs);

                    ps = connection.prepareStatement(sqlAtualizarContrato);
                    ps.setDate(1, Uteis.getDataJDBC(vencimentoContrato));
                    ps.setInt(2, codContrato);
                    ps.execute();
                    finalizar(ps, null);

                    ps = connection.prepareStatement(sqlAtualizarPeriodoAcessoCliente);
                    ps.setDate(1, Uteis.getDataJDBC(vencimentoContrato));
                    ps.setInt(2, codContrato);
                    ps.execute();
                    finalizar(ps, null);
                }
            } catch (Exception ex) {
                Uteis.logar(ex, AjustarContratosUnic.class);
            }
            System.out.println(i++);
        }
    }

    private static void finalizar(PreparedStatement ps, ResultSet rs) throws SQLException {
        if (ps != null && !ps.isClosed()) {
            ps.close();
        }

        if (rs != null && !rs.isClosed()) {
            rs.close();
        }

    }

    public static Connection obterConexao(String hostBD, String porta, String nomeBD) throws Exception {
        String userBD = "postgres";
        String passwordBD = "pactodb";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }


}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class ValidarTodosCPF {
     public static void main(String... args) {
        try {
            String nomeThread = args.length > 0 ? args[0] : "habitus";
            DAO dao = new DAO();
            Connection con = dao.obterConexaoEspecifica(nomeThread);
            validarTodosCpfs(con);
        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    
    public static void validarTodosCpfs(Connection con) throws Exception{
        Conexao.guardarConexaoForJ2SE(con);
        String consultarTodosAlunos = "select c.matricula, p.nome, p.cfp, c.situacao from cliente c inner join pessoa p on p.codigo = c.pessoa where cfp  is not null and cfp <> '' order by 4,2;";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(consultarTodosAlunos, con);
        System.out.println("\n#### iniciando validacao CPF ####");
        int contador = 1;
        while (consulta.next()) {
            try {
                if(!SuperVO.verificaCPF(consulta.getString("cfp"))){
                    System.out.println(contador+++". Aluno: " +consulta.getString("nome")+ " de Matricula: "+consulta.getString("matricula")+" e situacao: "+consulta.getString("situacao")+", tem o CPF "+consulta.getString("cfp")+" inválido");
                }
            } catch (Exception e) {
                System.out.println("#### " + e.getMessage() + " : matricula ="+consulta.getString("matricula")+"  ####");
            } finally {
            }
        }
         System.out.println("\n#### fim da validacao CPF ####");
    }
    
    
    

    
}

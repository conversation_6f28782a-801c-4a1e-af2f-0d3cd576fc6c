package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.HistoricoContrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

public class ProcessoAlterarContratosImportadosParaRenovacao {

    private static StringBuilder logGravar;
    private static String caminhoLog = "C:\\pacto\\logs";
    private static String nomeBanco = "";


    public static void main(String[] args) throws Exception {

        Connection con = new DAO().obterConexaoEspecifica("acadtoponeunidpassodareiars");
        Conexao.guardarConexaoForJ2SE(con);

        Integer codigoEmpresa = 1;
        String matriculas = "";

        try {
            alterarContratosImportadosParaRenovacao(con, codigoEmpresa, matriculas);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            String nomeArqLog =  con.getCatalog() + "-" + Calendario.getData("yyyyMMddHHmmss") + ".csv";
            Uteis.salvarArquivo(nomeArqLog, getLogGravar().toString(), caminhoLog + File.separator);
        }

    }

    public static void alterarContratosImportadosParaRenovacao(Connection con, Integer codigoEmpresa, String matriculas) throws Exception {
        Contrato contratoDAO = new Contrato(con);
        Cliente clienteDAO = new Cliente(con);
        ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(con);

        String sql = "SELECT \n" +
                " cli.codigomatricula,\n" +
                " pes.nome,\n" +
                " con.codigo AS contrato,\n" +
                " con.id_externo,\n" +
                " con.situacao,\n" +
                " con.situacaocontrato,\n" +
                " con.valorfinal,\n" +
                " con.contratoresponsavelrenovacaomatricula,\n" +
                " con.contratoresponsavelrematriculamatricula,\n" +
                " con.contratobaseadorenovacao,\n" +
                " con.contratobaseadorematricula, \n" +
                " con2.codigo AS contrato_base_rn,\n" +
                " con2.vigenciaateajustada AS contrato_base_vigenciaate\n" +
                "FROM contrato con\n" +
                " INNER JOIN pessoa pes ON pes.codigo = con.pessoa\n" +
                " INNER JOIN cliente cli ON cli.pessoa = con.pessoa \n" +
                " INNER JOIN contrato con2 ON con2.codigo = (SELECT con_sub.codigo FROM contrato con_sub\n" +
                "   WHERE con_sub.pessoa = con.pessoa\n" +
                "   AND con_sub.id_externo > 0\n" +
                "   AND con_sub.id_externo < con.id_externo \n" +
                "   AND con_sub.codigo <> con.codigo\n" +
                "   AND con_sub.datalancamento::date <> con.datalancamento::date\n" +
                "   AND NOT EXISTS (SELECT cp.codigo FROM contratooperacao cp WHERE cp.contrato = con_sub.codigo AND cp.tipooperacao = 'CA')\n" +
                "   AND con_sub.vigenciaateajustada < con.vigenciaateajustada\n" +
                "   AND (con.vigenciaateajustada - con_sub.vigenciaateajustada) > INTERVAL '26 days' \n" +
                "   AND con.vigenciade BETWEEN con_sub.vigenciade AND con_sub.vigenciaateajustada\n" +
                "   AND con_sub.contratoresponsavelrenovacaomatricula = 0\n" +
                "   ORDER BY con.id_externo DESC LIMIT 1)\n" +
                "WHERE 1 = 1\n" +
                "AND COALESCE(con.id_externo,0) > 0 \n" +
                "AND con.situacaocontrato <> 'RN'\n";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql += "AND cli.empresa = " + codigoEmpresa + "\n";
        }
        if (!UteisValidacao.emptyString(matriculas)) {
            sql += "AND cli.codigomatricula IN (" + matriculas + ")\n";
        }

        sql += "ORDER BY cli.codigomatricula, con.id_externo";

        int total = SuperFacadeJDBC.contar("select count(s.*) from (" + sql + ") as s", con);
        int atual = 0;

        ResultSet rs = con.createStatement().executeQuery(sql);

        con.setAutoCommit(false);
        List<Integer> codigosPessoas = new ArrayList<>();
        List<Integer> contratos = new ArrayList<>();

        getLogGravar().append("matricula;nome;contrato;situacao;dt_inicio;dt_fim;dt_inicio_alterada;dt_contrato_base_renovacao\n");

        try {
            while (rs.next()) {
                Integer codigoMatricula = rs.getInt("codigomatricula");
                String nome = rs.getString("nome");
                Integer codigoContrato = rs.getInt("contrato");
                Integer codigoContratoBaseRN = rs.getInt("contrato_base_rn");
                Date novaVigenciaDe = Calendario.somarDias(rs.getDate("contrato_base_vigenciaate"), 1);

                ContratoVO contratoVO = contratoDAO.consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                Date vigenciaDeOriginal = contratoVO.getVigenciaDe();

                System.out.printf("Processando %d\\%d - MAT: %d %s Contrato: %d | Contrato Base RN: %d\n", ++atual, total, rs.getInt("codigomatricula"), rs.getString("nome"), codigoContrato, codigoContratoBaseRN);

                contratoVO.setContratoBaseadoRenovacao(codigoContratoBaseRN);
                contratoVO.setVigenciaDe(novaVigenciaDe);
                contratoVO.getContratoDuracao().setNumeroMeses(Calendario.diferencaEmMeses(contratoVO.getVigenciaDe(), contratoVO.getVigenciaAteAjustada()));

                ajustarContratoRenovacao(con, contratoVO);
                ajustarHistoricoContratoRenovacao(con, contratoVO);
                ajustarPeriodoAcessoContratoRenovacao(con, contratoVO);

                if (!codigosPessoas.contains(contratoVO.getPessoa().getCodigo())) {
                    codigosPessoas.add(contratoVO.getPessoa().getCodigo());
                }
                String linha = String.format("%d;%s;%d;%s;%s;%s;%s;%d", codigoMatricula, nome.replace("\n", "").trim(), codigoContrato, contratoVO.getSituacao(),
                        Calendario.getData(vigenciaDeOriginal, "dd/MM/yyyy"),
                        Calendario.getData(contratoVO.getVigenciaAteAjustada(), "dd/MM/yyyy"),
                        Calendario.getData(novaVigenciaDe, "dd/MM/yyyy"), codigoContratoBaseRN);
                getLogGravar().append(linha).append("\n");
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

        atual = 0;
        for (Integer codigoContrato: contratos) {
            System.out.printf("Gerando historico temporal do Contrato %d\\%d\n", ++atual, contratos.size());
            contratoDAO.gerarHistoricoTemporalUmContrato(codigoContrato);
        }


        atual = 0;
        for (Integer codigoPessoa: codigosPessoas) {
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(codigoPessoa, Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
            System.out.printf("Atualizando Sintético %d\\%d - Cliente: %d\n", ++atual, codigosPessoas.size(), clienteVO.getCodigo());
            zillyonWebFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        }
    }

    private static boolean ajustarMovProdutos(Connection con, ContratoVO contratoVO) throws Exception {
        if (existeAlgumaAlteracaoNoPagamentoLancadaPeloSistema(con, contratoVO.getCodigo())) {
            System.out.printf("Movprodutos não alterados no contrato [%d] - Existe movimento de pagamento lançado pelo sistema\n", contratoVO.getCodigo());
            return false;
        }

        MovProduto movProdutoDAO = new MovProduto(con);
        List<MovProdutoVO> movProdutos = movProdutoDAO.consultarPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        List<MovProdutoVO> movProdutoVOSDelete = new ArrayList<>();
        Iterator i = movProdutos.iterator();
        while(i.hasNext()) {
            MovProdutoVO movProdutoVO = (MovProdutoVO) i.next();
            if (!movProdutoVO.getTipoProduto().equals("PM")) {
                i.remove();
                continue;
            }
            Date dataReferencia = Calendario.getDate("MM/yyyy", movProdutoVO.getMesReferencia());
            Date dataReferenciaUltimoDiaMes = obterUtimoDiaMes(dataReferencia);

            if (Calendario.menor(dataReferenciaUltimoDiaMes, contratoVO.getVigenciaDe())) {
                movProdutoVOSDelete.add(movProdutoVO);
                i.remove();
            }
        }
        if (movProdutos.size() != contratoVO.getContratoDuracao().getNumeroMeses().intValue()) {
            System.out.printf("Contrato [%d] - Quantidade de movimentos [%d] diferente da duração do contrato [%d]\n", contratoVO.getCodigo(), movProdutos.size(), contratoVO.getContratoDuracao().getNumeroMeses());
        } else {
            Double valorRestante = movProdutoVOSDelete.stream().mapToDouble(MovProdutoVO::getTotalFinal).sum();
            Double valorAdicionarMovProdutos = Uteis.arredondarForcando2CasasDecimais(valorRestante / movProdutos.size());
            Double residuo = (valorAdicionarMovProdutos * movProdutos.size()) - valorRestante;

            SuperFacadeJDBC.executarUpdate("delete from movproduto" +
                    " where codigo in (" + movProdutoVOSDelete.stream().map(MovProdutoVO::getCodigo).map(String::valueOf).collect(Collectors.joining(","))
                    + ")", con);

            for (MovProdutoVO movProdutoVO: movProdutos) {
                movProdutoVO.setTotalFinal(movProdutoVO.getTotalFinal() + valorAdicionarMovProdutos);
                movProdutoVO.setPrecoUnitario(movProdutoVO.getTotalFinal());
                movProdutoVO.setValorFaturado(movProdutoVO.getTotalFinal());
                if (residuo > 0) {
                    movProdutoVO.setTotalFinal(movProdutoVO.getTotalFinal() + residuo);
                    residuo = 0.0;
                }
                movProdutoDAO.alterarSemCommit(movProdutoVO);
            }
            return true;
        }
        return false;
    }


    private static boolean existeAlgumaAlteracaoNoPagamentoLancadaPeloSistema(Connection con, Integer codigoContrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT con.codigo FROM contrato con \n");
        sql.append("WHERE con.codigo = " + codigoContrato + " \n");
        sql.append("AND (EXISTS (SELECT mpar.codigo FROM movparcela mpar INNER JOIN transacaomovparcela tmpar ON tmpar.movparcela = mpar.codigo WHERE mpar.contrato = con.codigo) \n");
        sql.append("OR EXISTS (SELECT mpar.codigo FROM movparcela mpar INNER JOIN remessaitem ri ON ri.movparcela = mpar.codigo WHERE mpar.contrato = con.codigo) \n");
        sql.append("OR EXISTS (SELECT rpg.codigo FROM recibopagamento rpg INNER JOIN usuario u ON u.codigo = rpg.responsavellancamento WHERE rpg.contrato = con.codigo AND TRIM(UPPER(u.username)) NOT IN ('PACTOBR','MASTER','ADMIN')) \n");
        sql.append("OR EXISTS (SELECT mpar.codigo FROM movparcela mpar WHERE mpar.contrato = con.codigo AND mpar.descricao ILIKE '%RENEGOCIADA%'))\n");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

        return rs.next();
    }

    private static Date obterUtimoDiaMes(Date data) {
        Calendar c = Calendar.getInstance();
        c.setTime(data);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        return c.getTime();
    }

    private static void ajustarPeriodoAcessoContratoRenovacao(Connection con, ContratoVO contratoVO) throws SQLException {
        String sql = "update periodoacessocliente set datainicioacesso = ?, contratobaseadorenovacao = ? \n" +
                "where tipoacesso = 'CA' \n" +
                "and contrato = ? ;";
        try (PreparedStatement pstm = con.prepareStatement(sql)) {
            pstm.setDate(1, Uteis.getDataJDBC(contratoVO.getVigenciaDe()));
            pstm.setInt(2,contratoVO.getContratoBaseadoRenovacao());
            pstm.setInt(3, contratoVO.getCodigo());
            pstm.execute();
        }
    }

    private static void ajustarHistoricoContratoRenovacao(Connection con, ContratoVO contratoVO) throws Exception {
        String sqlDel = "delete from historicocontrato where contrato = ? and tipohistorico in ('MA','RE','RN');";
        try (PreparedStatement pstm = con.prepareStatement(sqlDel)) {
            pstm.setInt(1, contratoVO.getCodigo());
            pstm.execute();
        }

        HistoricoContrato historicoContratoDAO = new HistoricoContrato(con);
        HistoricoContratoVO historicoContratoOperacao = null;
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select hc.codigo from historicocontrato hc \n" +
                "where hc.tipohistorico in ('AT', 'RA', 'CA', 'CR', 'RC','RT','TR') \n" +
                "and hc.contrato = " + contratoVO.getCodigo() + " \n" +
                "order by hc.codigo limit 1", con);
        if (rs.next()) {
            historicoContratoOperacao = historicoContratoDAO.consultarPorChavePrimaria(rs.getInt(1), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        Date dataFinalSituacaoRenovado = contratoVO.getVigenciaAteAjustada();
        if (historicoContratoOperacao != null && !UteisValidacao.emptyNumber(historicoContratoOperacao.getCodigo())) {
            dataFinalSituacaoRenovado = Calendario.somarDias(historicoContratoOperacao.getDataInicioSituacao(), -1);
        }

        HistoricoContratoVO historicoContratoVO = new HistoricoContratoVO();
        historicoContratoVO.setContrato(contratoVO.getCodigo());
        historicoContratoVO.setDescricao("Renovado");
        historicoContratoVO.setTipoHistorico("RN");
        historicoContratoVO.setDataInicioTemporal(contratoVO.getVigenciaDe());
        historicoContratoVO.setResponsavelRegistro(contratoVO.getResponsavelContrato());
        historicoContratoVO.setDataRegistro(contratoVO.getDataLancamento());
        historicoContratoVO.setDataInicioSituacao(contratoVO.getVigenciaDe());
        historicoContratoVO.setDataFinalSituacao(dataFinalSituacaoRenovado);

        historicoContratoDAO.incluirSemCommit(historicoContratoVO, false);

    }

    private static void ajustarContratoRenovacao(Connection con, ContratoVO contratoVO) throws SQLException {
        String updateContrato = "update contrato set situacaocontrato = 'RN', contratobaseadorematricula = 0, " +
                "vigenciade  = ?, contratobaseadorenovacao = ? where codigo = ?";
        try (PreparedStatement pstm = con.prepareStatement(updateContrato)) {
            pstm.setDate(1, Uteis.getDataJDBC(contratoVO.getVigenciaDe()));
            pstm.setInt(2, contratoVO.getContratoBaseadoRenovacao());
            pstm.setInt(3, contratoVO.getCodigo());
            pstm.execute();
        }

        String updateContratoDuracao = "update contratoduracao set numeromeses = ? where contrato = ?";
        try (PreparedStatement pstm = con.prepareStatement(updateContratoDuracao)) {
            pstm.setInt(1, contratoVO.getContratoDuracao().getNumeroMeses());
            pstm.setInt(2, contratoVO.getCodigo());
            pstm.execute();
        }

        String updateContratoBaseRN = "update contrato set contratoresponsavelrenovacaomatricula = ?, contratoresponsavelrematriculamatricula = 0 where codigo = ?";
        try (PreparedStatement pstm = con.prepareStatement(updateContratoBaseRN)) {
            pstm.setInt(1, contratoVO.getCodigo());
            pstm.setInt(2, contratoVO.getContratoBaseadoRenovacao());
            pstm.execute();
        }
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}

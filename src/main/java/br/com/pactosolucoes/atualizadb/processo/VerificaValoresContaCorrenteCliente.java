/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class VerificaValoresContaCorrenteCliente {

    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("***********************************************", "zillyonweb", "pactodb");
           verificarCreditoDebitoAlunos(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void verificarCreditoDebitoAlunos(Connection con1) throws Exception{
        verificarAlunoCreditoErrado(con1);
        verificarAlunoDebitoErrado(con1);
    }
    
    public static void verificarAlunoCreditoErrado(Connection con) throws Exception {
        System.out.println("\n########## verificarAlunoCreditoErrado  - início em : " + new Date()+" ####"+con.getCatalog()+" ###########");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT mov.codigo, mov.saldoatual, mov.responsavelautorizacao, mov.dataregistro,mov.pessoa, p.nome, mov.pessoa, c.empresa  FROM movimentocontacorrentecliente AS mov ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = mov.pessoa");
        sql.append(" INNER JOIN cliente c ON c.pessoa = mov.pessoa ");
        sql.append(" WHERE mov.codigo = (SELECT codigo FROM movimentocontacorrentecliente ");
        sql.append(" WHERE movimentocontacorrentecliente.pessoa = mov.pessoa  ");
        sql.append("  ORDER BY movimentocontacorrentecliente.codigo DESC  ");
        sql.append(" LIMIT 1) AND  ");
        sql.append(" mov.saldoatual > 0  ");
        ResultSet dados = con.prepareStatement(sql.toString()).executeQuery();
        while (dados.next()) {
            sql = new StringBuilder();
            sql.append("SELECT sum(valor) as soma FROM movpagamento  where credito = 't' and codigo in (select movpagamento ");
            sql.append("from movimentocontacorrenteclientecomposicao  where movimentocontacorrentecliente  = "+dados.getInt("codigo")+")   ");
            ResultSet soma = con.prepareStatement(sql.toString()).executeQuery();
            if(soma.next()){
                if(Uteis.arredondarForcando2CasasDecimais(soma.getDouble("soma")) != Uteis.arredondarForcando2CasasDecimais(dados.getDouble("saldoatual"))){
                    System.err.println("O aluno "+dados.getString("nome")+" tem "+dados.getDouble("saldoatual")+" na conta, mas apenas "+soma.getDouble("soma")+" está vinculado ");
                }
            } else{
                System.err.println("O aluno "+dados.getString("nome")+" tem "+dados.getDouble("saldoatual")+" na conta, mas nada está vinculado ");
            }
        }
        System.out.println( "verificarAlunoCreditoErrado - fim em : "+ new Date()+" ####"+con.getCatalog());
    }

    public static void verificarAlunoDebitoErrado(Connection con) throws Exception {
        System.out.println("\n verificarAlunoDebitoErrado  - início em : " + new Date()+" ####"+con.getCatalog());
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT mov.codigo, mov.saldoatual, mov.responsavelautorizacao, mov.dataregistro,mov.pessoa, p.nome, mov.pessoa, c.empresa  FROM movimentocontacorrentecliente AS mov ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = mov.pessoa");
        sql.append(" INNER JOIN cliente c ON c.pessoa = mov.pessoa ");
        sql.append(" WHERE mov.codigo = (SELECT codigo FROM movimentocontacorrentecliente ");
        sql.append(" WHERE movimentocontacorrentecliente.pessoa = mov.pessoa  ");
        sql.append("  ORDER BY movimentocontacorrentecliente.codigo DESC  ");
        sql.append(" LIMIT 1) AND  ");
        sql.append(" mov.saldoatual < 0  ");
        ResultSet dados = con.prepareStatement(sql.toString()).executeQuery();
        while (dados.next()) {
            sql = new StringBuilder();
            sql.append("SELECT sum(valor) as soma FROM movpagamento  where codigo in (select movpagamento ");
            sql.append("from movimentocontacorrenteclientecomposicao  where movimentocontacorrentecliente  = "+dados.getInt("codigo")+")   ");
            ResultSet soma = con.prepareStatement(sql.toString()).executeQuery();
            if(soma.next()){
                if(Uteis.arredondarForcando2CasasDecimais(soma.getDouble("soma")) != Uteis.arredondarForcando2CasasDecimais(dados.getDouble("saldoatual")) && (soma.getDouble("soma") != 0.0)){
                    System.err.println("O aluno "+dados.getString("nome")+" tem "+dados.getDouble("saldoatual")+" na conta, mas apenas "+soma.getDouble("soma")+" está vinculado");
                }
            }
        }
        System.out.println( "verificarAlunoDebitoErrado - fim em : "+ new Date()+" ####"+con.getCatalog() +" ###########\n");
    }


}

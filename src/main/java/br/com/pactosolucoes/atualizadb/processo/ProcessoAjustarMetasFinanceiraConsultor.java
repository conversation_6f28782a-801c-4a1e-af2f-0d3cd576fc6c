package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

public class ProcessoAjustarMetasFinanceiraConsultor {

    public static void main(String... args) {
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"studio"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            Uteis.debug = true;

            Connection con = new DAO().obterConexaoEspecifica(chave);
            ProcessoAjustarMetasFinanceiraConsultor.corrigirMetasFinanceiraConsultor(con);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void corrigirMetasFinanceiraConsultor(Connection con) throws Exception {
        try {
            String sqlConsultaMetasInconsistentes = "SELECT \n" +
                    "CASE \n" +
                    " WHEN (SELECT codigo FROM colaborador WHERE mfe.empresa = empresa AND col.pessoa = pessoa) ISNULL THEN 'DELETE FROM metafinanceiraconsultor WHERE codigo = ' || mfc.codigo || ';' \n" +
                    " WHEN (SELECT codigo FROM colaborador WHERE mfe.empresa = empresa AND col.pessoa = pessoa) > 0 THEN 'UPDATE metafinanceiraconsultor SET colaborador = ' || (SELECT codigo FROM colaborador WHERE mfe.empresa = empresa AND col.pessoa = pessoa) || ' WHERE codigo = ' || mfc.codigo || ';' \n" +
                    "END AS sqlScr\n" +
                    "FROM metafinanceiraconsultor mfc \n" +
                    "INNER JOIN metafinanceiraempresa mfe ON mfe.codigo = mfc.metafinanceiraempresa \n" +
                    "INNER JOIN colaborador col ON col.codigo = mfc.colaborador \n" +
                    "WHERE col.empresa <> mfe.empresa;";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlConsultaMetasInconsistentes)) {
                    Uteis.logar("INÍCIO | ProcessoAjustarMetasFinanceiraConsultor");
                    while (rs.next()) {
                        String sqlCorrigirMetas = rs.getString("sqlScr");
                        stm.execute(sqlCorrigirMetas);
                    }
                    Uteis.logar("FIM | ProcessoAjustarMetasFinanceiraConsultor");
                }
            }

        } catch (Exception ignored) {
            ignored.printStackTrace();
        }

    }

}

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;


public class MovProdutoModalidadeSemContrato {
	public static void main(String[] args) {
		try {
			Connection con = DriverManager.getConnection("**********************************************************************", "zillyonweb", "pactodb");
			corrigirRegistros(con);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static void corrigirRegistros(Connection con) throws Exception{
		String sql = "select movproduto from movprodutomodalidade where contrato is null";
		ResultSet queryProduto = con.prepareStatement(sql).executeQuery();
		int i = 1;
		while(queryProduto.next()){
			String sqlContrato = "select contrato from movproduto where codigo ="+queryProduto.getInt("movproduto");
			ResultSet querycontrato = con.prepareStatement(sqlContrato).executeQuery();
			querycontrato.next();
			String sqlUpdate = "Update movprodutomodalidade set contrato ="+querycontrato.getInt("contrato");
			sqlUpdate +=" where movproduto = "+queryProduto.getInt("movproduto");
			con.prepareStatement(sqlUpdate).execute();
			System.out.println("Atualizados dados do movproduto = "+queryProduto.getInt("movproduto"));
			
		}
		System.out.println("Pronto!!");
	}
}

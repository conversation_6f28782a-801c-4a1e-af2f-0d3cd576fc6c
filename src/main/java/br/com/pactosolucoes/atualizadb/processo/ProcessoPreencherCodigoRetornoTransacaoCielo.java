package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.Transacao;

import java.sql.Connection;
import java.sql.ResultSet;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 21/09/2021
 */
public class ProcessoPreencherCodigoRetornoTransacaoCielo {

    private Connection con;

    public ProcessoPreencherCodigoRetornoTransacaoCielo(Connection con) {
        this.con = con;
    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "INICIO | ProcessoPreencherCodigoRetornoTransacaoCielo...");

            String chave = "teste";
            if (args.length > 0) {
                chave = args[0];
            }

            Connection con = null;
            try {
                Uteis.logar(null, "Obter conexão para chave: " + chave);
                con = new DAO().obterConexaoEspecifica(chave);
                ProcessoPreencherCodigoRetornoTransacaoCielo processo = new ProcessoPreencherCodigoRetornoTransacaoCielo(con);
                processo.processar();
                processo = null;
            } catch (Exception ex) {
                Uteis.logar(null, "Erro chave: " + chave);
                ex.printStackTrace();
            } finally {
                if (con != null) {
                    con.close();
                }
            }

            Uteis.logar(true, null, "FIM | ProcessoPreencherCodigoRetornoTransacaoCielo...");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private StringBuilder getSQLBase() {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("distinct(t.codigo) \n");
        sql.append("from transacao t  \n");
        sql.append("inner join transacaomovparcela tm on tm.transacao = t.codigo  \n");
        sql.append("inner join movparcela mp on mp.codigo = tm.movparcela \n");
        sql.append("where (t.situacao = 3 and t.tipo = 3 and t.codigoretorno in('0','00')) \n");
        sql.append("and mp.situacao = 'EA' \n");
        sql.append("order by 1 \n");
        return sql;
    }

    public void processar() {
        try {
            Uteis.logar(true, null, "INICIO ...");

            StringBuilder sql = getSQLBase();
            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);

            Uteis.logar(true, null, "Processar... Total " + total);

            if (total <= 0) {
                return;
            }

            Transacao transacaoDAO;
            try {
                transacaoDAO = new Transacao(con);

                int atual = 0;
                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                while (rs.next()) {
                    Integer codigo = 0;
                    try {
                        codigo = rs.getInt("codigo");

                        Uteis.logar(true, null, "Transacao... " + ++atual + "/" + total + " - Cod " + codigo);

                        String sqlTransacao = "select tipo, paramsresposta, outrasinformacoes, codigoretorno, situacao from transacao where codigo = " + codigo;
                        ResultSet rsTransacao = SuperFacadeJDBC.criarConsulta(sqlTransacao, con);
                        if (rsTransacao.next()) {

                            TipoTransacaoEnum tipoTransacaoEnum = TipoTransacaoEnum.getTipoTransacaoEnum(rsTransacao.getInt("tipo"));
                            String paramsresposta = rsTransacao.getString("paramsresposta");
                            String outrasinformacoes = rsTransacao.getString("outrasinformacoes");
                            String codigoretorno = rsTransacao.getString("codigoretorno");

                            TransacaoVO transacaoVO = transacaoDAO.obterObjetoTransacaoPorTipo(tipoTransacaoEnum);
                            transacaoVO.setCodigo(codigo);
                            transacaoVO.setParamsResposta(paramsresposta);
                            transacaoVO.setOutrasInformacoes(outrasinformacoes);
                            transacaoVO.setCodigoRetorno(codigoretorno);
                            transacaoVO.setSituacao(SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rsTransacao.getInt("situacao")));

                            String codigoRetornoNovo = transacaoVO.getCodigoRetornoGestaoTransacao();
                            String motivoGestaoTransacao = transacaoVO.getCodigoRetornoGestaoTransacaoMotivo();
                            if ((UteisValidacao.emptyString(codigoRetornoNovo) || codigoRetornoNovo.equals("?")) && !UteisValidacao.emptyString(motivoGestaoTransacao)) {
                                if (motivoGestaoTransacao.toLowerCase().contains("vencido segundo a validade informada")) {
                                    codigoRetornoNovo = CodigoRetornoPactoEnum.CARTAO_VENCIDO.getCodigo();
                                }
                            }

                            if (UteisValidacao.emptyString(codigoRetornoNovo)) {
                                throw new Exception("Sem código de erro.");
                            }

                            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA) &&
                                    codigoretorno.equals(codigoRetornoNovo) && (codigoRetornoNovo.equals("0") || codigoRetornoNovo.equals("00"))) {
                                codigoRetornoNovo = "?";
                            } else if (codigoretorno.equals(codigoRetornoNovo)) {
                                throw new Exception("Mesmo código que anteriormente. Antes " + codigoretorno + " | Novo " + codigoRetornoNovo);
                            }

                            Uteis.logar(true, null, "Transacao " + codigo + " - Cod Retorno anterior " + codigoretorno + " | Atual " + codigoRetornoNovo);
                            transacaoDAO.atualizarCodigoRetorno(codigoRetornoNovo, motivoGestaoTransacao, transacaoVO.getCodigo());

                        }
                    } catch (Exception ex) {
                        Uteis.logar(true, null, "Transacao: " + codigo + " | " + ex.getMessage());
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                transacaoDAO = null;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            Uteis.logar(true, null, "FIM...");
        }
    }
}

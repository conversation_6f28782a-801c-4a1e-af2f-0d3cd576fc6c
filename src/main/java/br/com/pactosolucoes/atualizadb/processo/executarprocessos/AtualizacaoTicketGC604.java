package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "22/08/2024",
        descricao = "Processo incluir permissao de acordo com permissao marcada",
        motivacao = "GC-604")
public class AtualizacaoTicketGC604 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT\n"
                    + "codigo\n"
                    + "FROM perfilacesso where tipo in (0,1,2,3,4)", c);
            while (rs.next()) {
                ResultSet rs2 = SuperFacadeJDBC.criarConsulta("SELECT EXISTS(SELECT nomeentidade FROM permissao where permissoes = '(0)(1)(2)(3)(9)(12)'" +
                        " and nomeentidade = 'ConsultarAlunosCaixaAbertoTodasEmpresas' and codperfilacesso = " + rs.getInt("codigo") + ")", c);
                if (rs2.next() && rs2.getBoolean(1)) {
                    String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                            + " VALUES (2, '10.05 - Consultar parcelas de todas as empresas','(0)(1)(2)(3)(9)(12)', "
                            + " 'ConsultarParcelasTodasEmpresas', " + rs.getInt("codigo") + ")";
                    SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
                }
            }
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Franco Alvarez",
        data = "26/02/2025",
        descricao = "Criacao colunas integração SESC (tokensescdf, usarsescdf)",
        motivacao = "GC-1606")
public class AtualizacaoTicketGC1606 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD tokensescdf text, ADD usarsescdf boolean default false;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Athos Feitosa",
        data = "24/04/2025",
        descricao = "Criar tabelas para configuração de valores de produtos por plano",
        motivacao = "GCM-287"
)
public class AtualizacaoTicketGCM287 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE IF NOT EXISTS configuracaoprodutoempresaplano (" +
                            "codigo SERIAL PRIMARY KEY, " +
                            "produto INTEGER NOT NULL, " +
                            "empresa INTEGER NOT NULL, " +
                            "plano INTEGER NOT NULL, " +
                            "valor DOUBLE PRECISION" +
                            ");",
                    c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE configuracaoprodutoempresa " +
                            "ADD COLUMN valorAlunoGympass DOUBLE PRECISION, " +
                            "ADD COLUMN valorAlunoGogood DOUBLE PRECISION, " +
                            "ADD COLUMN valorAlunoTotalpass DOUBLE PRECISION;",
                    c
            );
        }
    }
}

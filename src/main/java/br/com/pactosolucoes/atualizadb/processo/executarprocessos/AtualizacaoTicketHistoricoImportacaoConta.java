package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Vinicius  Franca",
        data = "05/05/2025",
        descricao = "Criação da tabela historicoimportacaoconta",
        motivacao = "Necessário armazenar logs de importação de contas")
public class AtualizacaoTicketHistoricoImportacaoConta implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE public.historicoimportacaoconta (" +
                            "codigo SERIAL PRIMARY KEY, " +
                            "dataimportacao TIMESTAMP NOT NULL, " +
                            "usuario VARCHAR(100) NOT NULL, " +
                            "origemarquivo VARCHAR(255), " +
                            "totalimportados INTEGER NOT NULL DEFAULT 0, " +
                            "totalerros INTEGER NOT NULL DEFAULT 0, " +
                            "mensagemresultado TEXT" +
                            ");", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE INDEX idx_historicoimportacaoconta_codigo ON public.historicoimportacaoconta USING btree (codigo);", c);
        }
    }
}

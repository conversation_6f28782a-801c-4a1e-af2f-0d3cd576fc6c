package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "10/01/2025",
        descricao = "Alterar situacao na tabela transacoes.",
        motivacao = "PAY-120")
public class PAY120AlterarTransacoesSituacaoErrada implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE transacao SET situacao = 3 WHERE situacao = 2 AND codigoretorno <> '?' AND dataprocessamento::date >= '2025-01-01';", c);
        }
    }
}

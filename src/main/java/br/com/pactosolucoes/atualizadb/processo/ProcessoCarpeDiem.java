package br.com.pactosolucoes.atualizadb.processo;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by GlaucoT on 12/11/2015
 */
public class ProcessoCarpeDiem {


    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("***************************************************", "postgres", "pactodb");

            boolean continuar = true;
            while (continuar) {
                ResultSet consultarContratoQueTemDuplicados = SuperFacadeJDBC.criarConsulta("select distinct contrato1 from (\n" +
                        "select con1.pessoa, con1.codigo as contrato1, con2.codigo as contrato2\n" +
                        "from contrato con1, contrato con2\n" +
                        "where 1 = 1 \n" +
                        "and con1.pessoa = con2.pessoa \n" +
                        "and con1.vigenciaDe = con2.vigenciaDe \n" +
                        "and con1.vigenciaAteAjustada = con2.vigenciaAteAjustada \n" +
                        "and con1.plano = con2.plano\n" +
                        "and con1.codigo <> con2.codigo\n" +
                        "order by pessoa, contrato1, contrato2\n" +
                        ") as foo\n" +
                        "order by 1\n" +
                        "limit 1", con1);
                continuar = consultarContratoQueTemDuplicados.next();
                if (continuar) {
                    ResultSet contratosDuplicados = SuperFacadeJDBC.criarConsulta("select con1.pessoa, con1.codigo as contrato1, con2.codigo as contrato2\n" +
                            "from contrato con1, contrato con2\n" +
                            "where 1 = 1 \n" +
                            "and con1.pessoa = con2.pessoa \n" +
                            "and con1.vigenciaDe = con2.vigenciaDe \n" +
                            "and con1.vigenciaAteAjustada = con2.vigenciaAteAjustada \n" +
                            "and con1.plano = con2.plano\n" +
                            "and con1.codigo <> con2.codigo\n" +
                            "and con1.codigo = " + consultarContratoQueTemDuplicados.getInt("contrato1") + "\n" +
                            "order by pessoa, contrato1, contrato2", con1);
                    while (contratosDuplicados.next()) {
                        Integer codContrato = contratosDuplicados.getInt("contrato2");
                        System.out.println("Estornando contrato: " + codContrato);
                        SuperFacadeJDBC.executarConsultaUpdate("DELETE FROM contrato WHERE codigo = " + codContrato, con1);
                    }
                }
            }

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "02/05/2025",
        descricao = "Criar permissao de operadora de cartao para usuários sem permissão, pois não tem como incluir manualmente e vai precisar no cadastro do Convênio de Cobrança",
        motivacao = "PAY-749")
public class PAY749IncluirPermissoesOperadoraCartaoUsuariosSemPermissao {
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM perfilacesso WHERE codigo NOT IN (" +
                    "SELECT codperfilacesso FROM permissao WHERE tituloapresentacao = '4.15 - Operadora de Cartão'" +
                    ");", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (1, '4.15 - Operadora de Cartão','(0)(9)(1)', "
                        + " 'OperadoraCartao', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        }

    }
}

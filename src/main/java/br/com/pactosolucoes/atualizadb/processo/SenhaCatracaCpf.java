package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Pessoa;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SenhaCatracaCpf {

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("***********************************************************************", "postgres", "pactodb");
            SenhaCatracaCpf.criarSenhaParaTodosAlunos(con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void criarSenhaParaTodosAlunos(Connection connection) throws Exception {
        Map<String, PessoaProcesso> mapaSenhasClientes = new HashMap<>();
        List<PessoaProcesso> clientesSemCPFeSemSenha = new ArrayList<>();
        List<PessoaProcesso> clientesAptosAoProcesso = new ArrayList<>();

        String sqlSenhasCadastradas = "select\n" +
                "  senhaacesso\n" +
                "  , cli.matricula\n" +
                "  , p.codigo as codigoPessoa\n" +
                "  , p.nome\n" +
                "  , p.cfp as cpf\n" +
                "  , cli.situacao as situacao\n" +
                "  , array_to_string(array(select email from email where email.pessoa = p.codigo), ',', '') as email\n" +
                "from\n" +
                "  pessoa p\n" +
                "inner join cliente cli on\n" +
                "  p.codigo = cli.pessoa\n" +
                "order by cli.codigomatricula ";

        String sqlColaboradores = "select\n" +
                "  senhaacesso\n" +
                "  , col.codigo\n" +
                "  , p.nome\n" +
                "  , p.cfp as cpf\n" +
                "  , '' as email\n" +
                "  , col.situacao\n" +
                "from\n" +
                "  pessoa p\n" +
                "inner join colaborador col on p.codigo = col.pessoa";


        List<PessoaProcesso> pessoas = new ArrayList<>();
        try (PreparedStatement sqlConsultar = connection.prepareStatement(sqlSenhasCadastradas);
             ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
            while (tabelaResultado.next()) {
                PessoaProcesso pessoaProcesso = new PessoaProcesso();
                pessoaProcesso.setCodigoPessoa(tabelaResultado.getInt("codigoPessoa"));
                pessoaProcesso.setMatricula(tabelaResultado.getString("matricula"));
                pessoaProcesso.setNome(tabelaResultado.getString("nome"));
                pessoaProcesso.setCpf(tabelaResultado.getString("cpf"));
                pessoaProcesso.setEmail(tabelaResultado.getString("email"));
                pessoaProcesso.setSenhaAcesso(tabelaResultado.getString("senhaacesso"));
                pessoaProcesso.setTipoPessoa("CL");
                pessoaProcesso.setSituacao(tabelaResultado.getString("situacao"));
                pessoas.add(pessoaProcesso);
            }
        }

        try (PreparedStatement sqlConsultar = connection.prepareStatement(sqlColaboradores);
             ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
            while (tabelaResultado.next()) {
                PessoaProcesso pessoaProcesso = new PessoaProcesso();
                pessoaProcesso.setCodigoPessoa(tabelaResultado.getInt("codigo"));
                pessoaProcesso.setNome(tabelaResultado.getString("nome"));
                pessoaProcesso.setCpf(tabelaResultado.getString("cpf"));
                pessoaProcesso.setEmail(tabelaResultado.getString("email"));
                pessoaProcesso.setSenhaAcesso(tabelaResultado.getString("senhaacesso"));
                pessoaProcesso.setTipoPessoa("CO");
                pessoaProcesso.setSituacao(tabelaResultado.getString("situacao"));
                pessoas.add(pessoaProcesso);
            }
        }

        for (PessoaProcesso cp : pessoas) {
            if (cp.getTipoPessoa().equals("CL")
                    && cp.getSituacao().equals("AT")
                    && UteisValidacao.emptyString(cp.getSenhaAcesso())
                    && UteisValidacao.emptyString(cp.getCpf())) {

                clientesSemCPFeSemSenha.add(cp);
            }

            if (!UteisValidacao.emptyString(cp.getSenhaAcesso())) {
                mapaSenhasClientes.put(cp.getSenhaAcesso(), cp);
            }

            if (cp.getTipoPessoa().equals("CL")
                    && cp.getSituacao().equals("AT")
                    && UteisValidacao.emptyString(cp.getSenhaAcesso())
                    && !UteisValidacao.emptyString(cp.getCpf())) {
                clientesAptosAoProcesso.add(cp);
            }
        }
        System.out.println("Quantidade de clientes que estão sem CPF para o processo: " + clientesSemCPFeSemSenha.size());
        System.out.println("Quantidade de clientes que estão com senha cadastrada: " + mapaSenhasClientes.size());
        System.out.println("Quantidade de clientes que estão aptos ao processo: " + clientesAptosAoProcesso.size());

        System.out.println("============= SEM CPF =============");
        System.out.println("Matricula, Nome, Email");
        for (PessoaProcesso cp : clientesSemCPFeSemSenha) {
            System.out.println(cp.getMatricula() + "," + cp.getNome() + "," + cp.getEmail());
        }

        System.out.println("============= SENHA CADASTRADA =============");
        System.out.println("Matricula, Nome, Email, Cpf");


        Pessoa pessoa = new Pessoa(connection);
        for (PessoaProcesso cp : clientesAptosAoProcesso) {
            String senhaEncriptada = Uteis.encriptar(cp.getCpfParaSenha());
            PessoaProcesso senhaCadastrada = mapaSenhasClientes.get(senhaEncriptada);
            if (senhaCadastrada == null) {
                pessoa.alterarSenhaAcesso(cp.getCodigoPessoa(), cp.getCpfParaSenha(), false);
                cp.setSenhaCadastradaComSucesso(true);
                mapaSenhasClientes.put(senhaEncriptada, cp);

                System.out.println(cp.getMatricula() + "," + cp.getNome() + "," + cp.getEmail() + "," + cp.getCpf());
            }
        }

        System.out.println("Quantidade final de clientes que estão com senha cadastrada: " + mapaSenhasClientes.size());
    }
}

class PessoaProcesso {
    private Integer codigoPessoa;
    private String matricula;
    private String nome;
    private String cpf;
    private String email;
    private String senhaAcesso;
    private boolean senhaCadastradaComSucesso = false;
    private String tipoPessoa;
    private String situacao;

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getCpfParaSenha() {
        String cpfSenha = Uteis.removerMascara(getCpf());
        cpfSenha = cpfSenha.substring(0, 5);
        return cpfSenha;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSenhaAcesso() {
        return senhaAcesso;
    }

    public void setSenhaAcesso(String senhaAcesso) {
        this.senhaAcesso = senhaAcesso;
    }

    public boolean isSenhaCadastradaComSucesso() {
        return senhaCadastradaComSucesso;
    }

    public void setSenhaCadastradaComSucesso(boolean senhaCadastradaComSucesso) {
        this.senhaCadastradaComSucesso = senhaCadastradaComSucesso;
    }

    public String getTipoPessoa() {
        return tipoPessoa;
    }

    public void setTipoPessoa(String tipoPessoa) {
        this.tipoPessoa = tipoPessoa;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }
}

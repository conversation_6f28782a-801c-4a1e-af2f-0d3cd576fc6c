package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

public class ProcessoCorrigirClientesSemNome {

    public static void corrigirClientesSemNome(Connection con) throws Exception {
        try {
            String sqlGetClienteSemNome = "SELECT 'UPDATE pessoa SET nome = ''' || scs.nomecliente || ''' WHERE codigo = ' || pes.codigo || '; UPDATE pessoa SET nomeconsulta = remove_acento_upper(''' || scs.nomecliente || ''') WHERE codigo = ' || pes.codigo || ';' AS sqlUpdate \n" +
                    "FROM cliente cli \n" +
                    "INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n" +
                    "INNER JOIN situacaoclientesinteticodw scs ON scs.codigocliente = cli.codigo \n" +
                    "WHERE (pes.nome IS NULL OR pes.nome ILIKE '')\n" +
                    "AND (scs.nomecliente IS NOT NULL AND scs.nomecliente <> '')";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlGetClienteSemNome)) {
                    while (rs.next()) {
                        String sqlUpdateCliente = rs.getString("sqlUpdate");
                        try (PreparedStatement sqlAlterar = con.prepareStatement(sqlUpdateCliente)) {
                            sqlAlterar.execute();
                        }
                    }
                }
            }

        } catch (Exception ignore) {
        }

    }

}

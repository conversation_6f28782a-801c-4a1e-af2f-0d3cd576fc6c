package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "12/03/2025",
        descricao = "Cria a coluna 'permiteAcionarEmergencia' na tabela do Usuario",
        motivacao = "GCM-39")
public class CriarColunaPermiteAcionarEmergenciaTabUsuario implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE usuario ADD COLUMN permiteAcionarEmergencia BOOLEAN default false;", c);
        }
    }
}

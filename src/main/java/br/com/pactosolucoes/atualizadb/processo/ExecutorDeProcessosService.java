package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD.executarUmProcessoQualquer;

public class ExecutorDeProcessosService {

    public static void main(String... args) {
        try {
            DAO oamd = new DAO();
            Uteis.debug = true;
            java.lang.String metodo = "";
            List<String> argsMetodo  = new ArrayList<String>();
            argsMetodo.add("chave");
            List<Map> lista =  oamd.buscarListaEmpresas();
            if (args.length > 0) {
                metodo = args[0];
                for (int i = 1; i < args.length ; i++) {
                    argsMetodo.add(args[i]);
                }
            } else {
                metodo = "servicos.operacoes.AtualizarUsuarioTreinoService.main";
                argsMetodo.add("true");
            }
            String[] argumentos = new String[argsMetodo.size()];
            for (int i = 0; i < argsMetodo.size() ; i++) {
                argumentos[i] = argsMetodo.get(i);
            }

            Uteis.logar(null, "#### iniciando processamento do metodo "+metodo+" para " +lista.size()+" chaves em: "+Uteis.getDataComHora(Calendario.hoje()));
            for (Map map : lista) {

                Uteis.logar(null, map.toString());
                final String chave = (String) map.get("chave");
                Uteis.logar(null, "#### iniciando processamento do metodo "+metodo+" para a chave "+chave+" em : "+Uteis.getDataComHora(Calendario.hoje()));
                argumentos[0] = chave;

                executarUmProcessoQualquer(metodo, argumentos);
                Uteis.logar(null, "#### terminando processamento do metodo "+metodo+" para a chave "+chave+" em : "+Uteis.getDataComHora(Calendario.hoje()));
            }
            Uteis.logar(null, "#### terminando processamento do metodo "+metodo+" para " +lista.size()+" chaves em: "+Uteis.getDataComHora(Calendario.hoje()));
        } catch (Exception ex) {
            Logger.getLogger(ExecutorDeProcessosService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}

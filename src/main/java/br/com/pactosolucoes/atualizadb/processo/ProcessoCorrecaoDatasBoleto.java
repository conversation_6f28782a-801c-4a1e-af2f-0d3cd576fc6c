package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Date;

public class ProcessoCorrecaoDatasBoleto {

    public static JSONObject corrigir(Connection con, String reciboBuscar) {
        JSONObject json = new JSONObject();
        json.put("sucesso", true);
        Integer ajustado = 0;
        Integer erro = 0;
        Integer total = 0;
        try {
            Uteis.logarDebug("ProcessoCorrecaoDatasBoleto | INICIO!");

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("rp.codigo as recibo, \n");
            sql.append("mp.codigo as movpagamento, \n");
            sql.append("rp.data as recibo_data, \n");
            sql.append("mp.datalancamento  as movpagamento_lancamento, \n");
            sql.append("mp.dataquitacao as movpagamento_quitacao, \n");
            sql.append("b.datacredito as boleto_credito, \n");
            sql.append("b.datapagamento as boleto_pagamento,\n");
            sql.append("rp2.codigo as recibo2, \n");
            sql.append("mp2.codigo as movpagamento2\n");
            sql.append("from boleto b \n");
            sql.append("inner join recibopagamento rp on rp.codigo = b.recibopagamento \n");
            sql.append("inner join movpagamento mp on mp.codigo = b.movpagamento \n");
            sql.append("left join movpagamento mp2 on mp2.movpagamentoorigemcredito = mp.codigo\n");
            sql.append("left join recibopagamento rp2 on rp2.codigo = mp2.recibopagamento \n");
            sql.append("where b.datapagamento is not null \n");
            sql.append("and b.movpagamento is not null \n");
            sql.append("and (rp.data::date <> b.datapagamento::date or rp2.data::date <> b.datapagamento::date)\n");
            if (!UteisValidacao.emptyString(reciboBuscar)) {
                sql.append("and (rp.codigo in (").append(reciboBuscar).append(") or rp2.codigo in (").append(reciboBuscar).append("))\n");
            }
            sql.append("order by b.recibopagamento \n");

            total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            Integer atual = 0;
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        String msg = "";
                        Integer movPagamento = 0;
                        Integer recibo = 0;
                        try {
                            con.setAutoCommit(false);

                            movPagamento = rs.getInt("movpagamento");
                            recibo = rs.getInt("recibo");
                            Date dataPagamentoBoleto = rs.getTimestamp("boleto_pagamento");

                            if (UteisValidacao.emptyNumber(movPagamento)) {
                                throw new Exception("MovPagamento não encontrado");
                            }
                            if (UteisValidacao.emptyNumber(recibo)) {
                                throw new Exception("recibo não encontrado");
                            }
                            if (dataPagamentoBoleto == null) {
                                throw new Exception("dataPagamentoBoleto não encontrado");
                            }

                            atualizarMovPagamento(dataPagamentoBoleto, movPagamento, con);
                            atualizarRecibo(dataPagamentoBoleto, recibo, con);

                            Integer movPagamento2 = rs.getInt("movpagamento2");
                            if (!UteisValidacao.emptyNumber(movPagamento2)) {
                                atualizarMovPagamento(dataPagamentoBoleto, movPagamento2, con);
                            }
                            Integer recibo2 = rs.getInt("recibo2");
                            if (!UteisValidacao.emptyNumber(recibo2)) {
                                atualizarRecibo(dataPagamentoBoleto, recibo2, con);
                            }

                            ajustado++;
                            msg += "Ajustado";
                            con.commit();
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            msg = ex.getMessage();
                            con.rollback();
                            erro++;
                        } finally {
                            Uteis.logarDebug("ProcessoCorrecaoDatasBoleto | " + ++atual + "/" + total + " - MovPagamento " + movPagamento + " | Resultado: " + msg);
                            con.setAutoCommit(true);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            json.put("sucesso", false);
            json.put("msg", ex.getMessage());
        } finally {
            Uteis.logarDebug("ProcessoCorrecaoDatasBoleto | FIM!");
        }
        json.put("ajustado", ajustado);
        json.put("erro", erro);
        json.put("total", total);
        return json;
    }

    private static void atualizarMovPagamento(Date data, Integer movPagamento, Connection con) throws SQLException {
        String sqlUpdate = "update movpagamento set datalancamento = ? where codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(sqlUpdate)) {
            pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(data));
            pst.setInt(2, movPagamento);
            pst.execute();
        }
    }

    private static void atualizarRecibo(Date data, Integer recibo, Connection con) throws SQLException {
        String sqlUpdate1 = "update recibopagamento set data = ? where codigo = ?";
        try (PreparedStatement pst = con.prepareStatement(sqlUpdate1)) {
            pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(data));
            pst.setInt(2, recibo);
            pst.execute();
        }
    }

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("*************************************************", "zillyonweb", "pactodb");
            ProcessoCorrecaoDatasBoleto.corrigir(con, "64278");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luís Antônio",
        data = "19/08/2024",
        descricao = "Cria tabela configuracao pos venda por IA",
        motivacao = "PRPI-42")
public class CriarTabelaCrmFaseIAPRPI160 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    " CREATE TABLE public.configuracaocrmiaposvenda (\n" +
                    "          codigo SERIAL PRIMARY KEY,\n" +
                    "          codigoposvenda INTEGER NOT NULL,\n" +
                    "          instrucao TEXT,\n" +
                    "          habilitar BOOLEAN\n" +
                    ");", c);
        }
    }


}

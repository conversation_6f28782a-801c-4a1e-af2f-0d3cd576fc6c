package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "12/05/2025",
        descricao = "Criando tabela de Desconto Convenio por Plano",
        motivacao = "GC-2109")
public class AtualizacaoTicketGC2109 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.conveniodescontoplanoconfiguracao (\n" +
                    "\tcodigo serial4 NOT NULL,\n" +
                    "\tplano int4 NOT NULL,\n" +
                    "\tconveniodesconto int4 NOT NULL,\n" +
                    "\tporcentagemdesconto real NOT NULL,\n" +
                    "\tvalordesconto real NOT NULL,\n" +
                    "\tduracao int4 NOT NULL,\n" +
                    "\ttipodesconto varchar(2) NOT NULL,\n" +
                    "\tCONSTRAINT conveniodescontoplanoconfiguracao_pkey PRIMARY KEY (codigo),\n" +
                    "\tCONSTRAINT fk_conveniodescontoplanoconfiguracao_plano FOREIGN KEY (plano) REFERENCES public.plano(codigo),\n" +
                    "\tCONSTRAINT fk_conveniodescontoplanoconfiguracao_conveniodesconto FOREIGN KEY (conveniodesconto) REFERENCES public.conveniodesconto(codigo)\n" +
                    ");", c);
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.financeiro.ExtratoDiarioItemVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.ExtratoDiarioItem;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class ReprocessaritensExtrato {
    public static void main(String... args) {
        try {
            String nomeThread = args.length > 0 ? args[0] : "cia";
            String sqlItens = args.length > 1 ? args[1] : "select * from extratodiarioitem  order by codigo";
            DAO dao = new DAO();
            Connection con = dao.obterConexaoEspecifica(nomeThread);
            Conexao.guardarConexaoForJ2SE(nomeThread, con);
            executar(con, sqlItens);

            } catch (Exception ex) {
                Logger.getLogger(ReprocessaritensExtrato.class.getName()).log(Level.SEVERE, null, ex);
            }
    }

    public static void executar(Connection con, String sqlItens) throws Exception {
        ExtratoDiarioItem extratoDAO = new ExtratoDiarioItem(con);
        try {
            List<ExtratoDiarioItemVO> extratoDiarioItemVOs = extratoDAO.consultarItensSql(sqlItens);
            extratoDAO.processarListaExtratoDiario(extratoDiarioItemVOs, true);
        } catch (Exception e) {
            Uteis.logar(null, "# ERRO ao processar Extrato: " + e.getMessage());
       }
    }
    
}

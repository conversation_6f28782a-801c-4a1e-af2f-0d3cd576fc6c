package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;

import java.sql.Connection;
import java.util.List;

public class ProcessoAjustaAulaDesmarcadaContratoAnterior {

    public static void main(String... args) {
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"studiovowisp"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            Uteis.debug = true;

            Connection con = new DAO().obterConexaoEspecifica(chave);
            ProcessoAjustaAulaDesmarcadaContratoAnterior.corrigirAulasDesmarcadasDeContratosAnteriores(con);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void corrigirAulasDesmarcadasDeContratosAnteriores(Connection con) throws Exception{
        Empresa empresaDao = new Empresa(con);
        List<EmpresaVO> empresas = empresaDao.consultarEmpresas();
        for (EmpresaVO empresaVO : empresas){
            if (empresaVO.isAdicionarAulasDesmarcadasContratoAnterior()){
                String sqlSelectUpdate = "SELECT \n" +
                        "CASE \n" +
                        "\tWHEN con.contratoresponsavelrenovacaomatricula <> 0 THEN 'UPDATE auladesmarcada SET contrato = '|| con.contratoresponsavelrenovacaomatricula ||', contratoanterior = ' || con.codigo || ' WHERE codigo = ' || adm.codigo || ';'\n" +
                        "\tWHEN con.contratoresponsavelrematriculamatricula <> 0 THEN 'UPDATE auladesmarcada SET contrato = '|| con.contratoresponsavelrematriculamatricula ||', contratoanterior = ' || con.codigo || ' WHERE codigo = ' || adm.codigo || ';'\n" +
                        "END AS sqlUpdate\n" +
                        "FROM auladesmarcada adm\n" +
                        "INNER JOIN contrato con ON con.codigo = adm.contrato\n" +
                        "WHERE adm.datareposicao IS NULL\n" +
                        "AND (con.contratoresponsavelrenovacaomatricula <> 0 OR con.contratoresponsavelrematriculamatricula <> 0)\n" +
                        "AND adm.datalancamento > '2024-03-01'\n" +
                        "AND adm.permiteReporAulaDesmarcada = TRUE\n" +
                        "AND con.empresa = " + empresaVO.getCodigo() + ";";
                try (java.sql.Statement stm = con.createStatement()) {
                    try (java.sql.ResultSet rs = stm.executeQuery(sqlSelectUpdate)) {
                        Uteis.logarDebug("INÍCIO | ProcessoAjustaAulaDesmarcadaContratoAnterior");
                        while (rs.next()) {
                            String sqlUpdate = rs.getString("sqlUpdate");
                            stm.execute(sqlUpdate);
                        }
                        Uteis.logarDebug("FIM | ProcessoAjustaAulaDesmarcadaContratoAnterior");
                    }
                }
            }
        }
    }

}

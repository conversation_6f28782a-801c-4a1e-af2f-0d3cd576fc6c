package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "21/01/2025",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-1438")
public class AtualizacaoTicketGC1438 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaoacessogrupoempresarial ADD COLUMN statusSincronizacaoFotoPessoa integer default 0;", c);
        }
    }
}

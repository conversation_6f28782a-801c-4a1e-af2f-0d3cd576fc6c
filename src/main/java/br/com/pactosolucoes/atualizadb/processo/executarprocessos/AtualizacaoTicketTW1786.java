package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "17/03/2025",
        descricao = "TW-1786 - Criar nova coluna para limitação de vagas em aula coletiva para agregados",
        motivacao = "TW-1786")
public class AtualizacaoTicketTW1786 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE turma ADD COLUMN limiteVagasAgregados INTEGER DEFAULT 0;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE horarioturma ADD COLUMN limiteVagasAgregados INTEGER DEFAULT 0;", c);
        }
    }
}

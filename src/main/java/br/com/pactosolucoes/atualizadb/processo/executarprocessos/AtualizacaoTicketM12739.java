package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "02/10/2024",
        descricao = "Criar colunas para armazenar informações de erro em processamento de rotinas do robo",
        motivacao = "M1-2739")
public class AtualizacaoTicketM12739 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE robo ADD COLUMN rotinaProcessadaParcialmente BOOLEAN DEFAULT FALSE ;",c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE robo ADD COLUMN textoErroProcessamento TEXT DEFAULT NULL;",c);
        }
    }
}

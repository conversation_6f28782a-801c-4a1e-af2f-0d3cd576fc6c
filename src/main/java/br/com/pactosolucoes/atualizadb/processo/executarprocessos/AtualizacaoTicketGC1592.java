package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "03/03/2026",
        descricao = "Atualizar chaveprimaria logintegracoes integração foguete",
        motivacao = "Atualizar chaveprimaria logintegracoes integração foguete")
public class AtualizacaoTicketGC1592 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE logintegracoes l SET chaveprimaria = (substring(chaveprimaria, 0, POSITION('_' IN chaveprimaria)) || '_' || cli.codigo) FROM pessoa pes, cliente cli\n" +
                    "\tWHERE l.servico = 'INTEGRACAO_FOGUETE'\n" +
                    "\tAND coalesce(l.chaveprimaria,'') LIKE '%_%'\n" +
                    "\tAND pes.cfp = substring(l.chaveprimaria, POSITION('_' IN l.chaveprimaria) + 1) \n" +
                    "\tAND cli.pessoa = pes.codigo", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luiz Felipe",
        data = "28/04/2025",
        descricao = "MJ-939 - Nova home",
        motivacao = "MJ-939 - Nova home")
public class AtualizacaoTicketMJ939 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE notificacaoUsuario ADD COLUMN dados text;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE notificacaoUsuario ADD COLUMN dataNaoApresentar TIMESTAMP WITHOUT TIME ZONE;", c);
        }
    }
}

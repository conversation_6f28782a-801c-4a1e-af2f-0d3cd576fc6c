package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Hibrael A Alves",
        data = "17/01/2025",
        descricao = "PAY-153 Erro ao Receber Pagamentos com Cartão de Crédito na Bandeira ELO",
        motivacao = "PAY-153")
public class AtualizacaoTicketPAY153 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdate("UPDATE transacao\n" +
                            "    SET outrasinformacoes = regexp_replace(\n" +
                            "        outrasinformacoes,\n" +
                            "        '\"cartaoMascarado\":\"(\\d{6})\\d{6}(\\d{4})\"',\n" +
                            "        '\"cartaoMascarado\":\"\\1******\\2\"',\n" +
                            "        'g'\n" +
                            "   )\n" +
                            "WHERE outrasinformacoes ~ '\"cartaoMascarado\":\"\\d{16}\"'\n" +
                            "AND codigo IN (SELECT t.codigo FROM transacao t \n" +
                            "INNER JOIN conveniocobranca c ON (c.codigo = t.conveniocobranca AND c.tipoconvenio =" + 46 + "));",
                    c);

        }
    }

}

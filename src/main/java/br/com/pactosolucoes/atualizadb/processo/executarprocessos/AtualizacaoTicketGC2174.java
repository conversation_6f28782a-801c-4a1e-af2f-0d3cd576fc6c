package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Jose Luiz",
        data = "29/05/2025",
        descricao = "Adicionando dados de Duracao do PLano na Configuracao do Foguete",
        motivacao = "GC-2174")
public class AtualizacaoTicketGC2174 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            StringBuilder sql = new StringBuilder();
            sql.append("    CREATE TABLE configuracaointegracaofogueteDuracaoPlano ");
            sql.append("                 (codigo serial NOT NULL, ");
            sql.append("                  configuracaointegracaofoguete INTEGER NOT NULL, ");
            sql.append("                  duracaoPlano INTEGER, ");
            sql.append("                  CONSTRAINT configuracaointegracaofogueteDuracaoPlano_pkey PRIMARY KEY (codigo), ");
            sql.append("                  CONSTRAINT fk_configuracaointegracaofogueteDuracaoPlano_configuracaointegracaofoguete FOREIGN KEY (configuracaointegracaofoguete) REFERENCES configuracaointegracaofoguete (codigo) MATCH SIMPLE ON ");
            sql.append("                 UPDATE RESTRICT ON DELETE RESTRICT ); ");
            SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);

        }
    }
}

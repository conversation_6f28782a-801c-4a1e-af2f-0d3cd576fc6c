package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Pessoa;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.vindi.ImportacaoVindiDTO;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProcessoEnviarCartaoVindi {

    public static String urlAPI = PropsService.getPropertyValue(PropsService.urlApiVindiProducao);

    public static void main(String[] args) {
        Connection con = null;
        try {
            Uteis.debug = true;

            if (args.length < 3) {
                throw new Exception("Args incorreto!");
            }

            String chave = args[0];
            Integer empresa = Integer.valueOf(args[1]);
            String chavePrivadaVindi = args[2];

            Uteis.logarDebug("ProcessoEnviarCartaoVindi | Chave: " + chave);
            Uteis.logarDebug("ProcessoEnviarCartaoVindi | Empresa: " + empresa);
            Uteis.logarDebug("ProcessoEnviarCartaoVindi | ChavePrivadaVindi: " + chavePrivadaVindi);

            Uteis.logarDebug("Obter conexão para chave: " + chave);
            con = new DAO().obterConexaoEspecifica(chave);
//                con = DriverManager.getConnection("***************************************************", "postgres", "pactodb");

            processar(chavePrivadaVindi, empresa, con);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private static void processar(String chaveVindi, Integer empresa, Connection con) throws Exception {
        List<AutorizacaoCobrancaClienteVO> listaAutorizacao = consultarAutorizacoesEnviar(empresa, con);

        AragornService aragornService = new AragornService();
        for (AutorizacaoCobrancaClienteVO autoVO : listaAutorizacao) {
            String msg = "";
            String retornoEnvio = "";
            try {

                //consultar o cliente na vindi
                ImportacaoVindiDTO importacaoVindiDTO = obterClienteVindi(chaveVindi, autoVO.getCliente().getPessoa());

                if (importacaoVindiDTO == null) {
                    //cadastrar o cliente na vindi caso não tenha encontrado
                    importacaoVindiDTO = enviarClienteVindi(chaveVindi, autoVO.getCliente().getPessoa());
                }

                if (importacaoVindiDTO == null) {
                    throw new Exception("Cliente VINDI não encontrado");
                }

                //obter dados do cartão no aragorn
                autoVO.setNazgDTO(aragornService.obterNazg(autoVO.getTokenAragorn()));

                JSONObject payments = new JSONObject();
                payments.put("card_number", autoVO.getNazgDTO().getCard());
                payments.put("holder_name", Uteis.retirarAcentuacao(autoVO.getNazgDTO().getName()));
                payments.put("card_expiration", autoVO.getNazgDTO().getValidadeMMYYYY(true));
                payments.put("payment_company_code", obterPayment_company_code(autoVO.getNazgDTO()));
                payments.put("payment_method_code", "credit_card");
                payments.put("customer_id", importacaoVindiDTO.getIdVindi());

                retornoEnvio = executarRequest(chaveVindi, payments.toString(), "payment_profiles", null, MetodoHttpEnum.POST);
                JSONObject jsonResp = new JSONObject(retornoEnvio);
                String idVindiPagamento = jsonResp.getJSONObject("payment_profile").optString("id");
                if (!UteisValidacao.emptyString(idVindiPagamento)) {
                    AutorizacaoCobrancaCliente autoDAO = new AutorizacaoCobrancaCliente(con);
                    autoVO.setCodigoExterno(idVindiPagamento);
                    autoDAO.alterarCodigoExterno(autoVO);
                    autoDAO = null;
                } else {
                    throw new Exception("payment_profile id não encontrado");
                }
                msg = "Enviado com sucesso";
            } catch (Exception ex) {
//                ex.printStackTrace();
                msg = ("ERRO: " + ex.getMessage() + " | Resposta: " + retornoEnvio);
            } finally {
                Uteis.logarDebug("Mat. " + autoVO.getCliente().getMatricula() + " - " + autoVO.getCliente().getPessoa().getNome() + " | Resultado: " + msg);
            }
        }
    }

    private static ImportacaoVindiDTO obterClienteVindi(String chaveVindi, PessoaVO pessoaVO) throws Exception {
        Map<String, String> params = new HashMap<String, String>();
        params.put("perPage", "25");
        params.put("page", "1");
        params.put("query", "registry_code=" + Uteis.formatarCpfCnpj(pessoaVO.getCfp(), true));

        JSONObject obj = new JSONObject(executarRequest(chaveVindi, null, "customers", params, MetodoHttpEnum.GET));
        JSONArray customers = obj.getJSONArray("customers");
        if (customers.length() > 1) {
            throw new Exception("Mais de um cliente encontrado com o CPF: " + pessoaVO.getCfp());
        }
        for (int i = 0; i < customers.length(); i++) {
            JSONObject json = customers.getJSONObject(i);

            ImportacaoVindiDTO itemDTO = new ImportacaoVindiDTO();
            itemDTO.setIdVindi(json.opt("id").toString());
            itemDTO.setCpfVindi(json.opt("registry_code").toString());
            itemDTO.setNomeVindi(json.optString("name"));
            itemDTO.setStatus(json.optString("status"));
            return itemDTO;
        }
        return null;
    }

    private static ImportacaoVindiDTO enviarClienteVindi(String chaveVindi, PessoaVO pessoaVO) throws Exception {
        JSONObject pessoaJSON = criarPessoaJSON(pessoaVO);
        JSONObject resposta = new JSONObject(executarRequest(chaveVindi, pessoaJSON.toString(), "customers", null, MetodoHttpEnum.POST));

        if (resposta.has("customer")) {
            JSONObject customer = resposta.getJSONObject("customer");
            ImportacaoVindiDTO itemDTO = new ImportacaoVindiDTO();
            itemDTO.setChaveAPI(chaveVindi);
            itemDTO.setIdVindi(customer.opt("id").toString());
            itemDTO.setCpfVindi(customer.getString("registry_code"));
            itemDTO.setNomeVindi(customer.getString("name"));
            itemDTO.setStatus(customer.optString("status"));
            return itemDTO;
        }
        return null;
    }

    private static JSONObject criarPessoaJSON(PessoaVO pessoa) {
        JSONObject pes = new JSONObject();
        pes.put("name", Uteis.retirarAcentuacao(pessoa.getNome()));
        pes.put("code", "P" + pessoa.getCodigo());
        if (!UteisValidacao.emptyString(pessoa.getEmail())) {
            pes.put("email", pessoa.getEmail());
        }
        if (!UteisValidacao.emptyString(pessoa.getCfp())) {
            pes.put("registry_code", pessoa.getCfp());
        }
        JSONArray phones = getTelefonesCelularesPessoa(pessoa);
        if (phones.length() > 0) {
            pes.put("phones", phones);
        }
        if (!UteisValidacao.emptyNumber(pessoa.getIdVindi())) {
            pes.put("id", pessoa.getIdVindi());
        }
        return pes;
    }

    private static JSONArray getTelefonesCelularesPessoa(PessoaVO pessoa) {
        JSONArray telefones = new JSONArray();
        if (pessoa.getTelefoneVOs() != null && !pessoa.getTelefoneVOs().isEmpty()) {
            for (TelefoneVO telefone : pessoa.getTelefoneVOs()) {
                if (!UteisValidacao.emptyString(telefone.getTipoTelefone()) && telefone.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo())) {
                    telefones.put(criarTelefoneJSON(telefone));
                }
            }
        }
        return telefones;
    }

    private static JSONObject criarTelefoneJSON(TelefoneVO telefone) {
        JSONObject tel = new JSONObject();
        tel.put("phone_type", telefone.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo()) ? "mobile" : "landline");
        String numero = telefone.getNumero();
        numero = numero.replaceAll("[()]", "");
        tel.put("number", "55" + numero);
        return tel;
    }

    private static List<AutorizacaoCobrancaClienteVO> consultarAutorizacoesEnviar(Integer empresa, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("p.codigo as pessoa, \n");
        sql.append("p.idvindi, \n");
        sql.append("cl.codigo as cliente, \n");
        sql.append("cl.matricula, \n");
        sql.append("p.nome, \n");
        sql.append("au.codigoexterno, \n");
        sql.append("au.codigo as autorizacao, \n");
        sql.append("au.validadecartao, \n");
        sql.append("au.cartaomascaradointerno, \n");
        sql.append("au.tokenaragorn \n");
        sql.append("from cliente cl  \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
        sql.append("inner join autorizacaocobrancacliente au on au.cliente = cl.codigo \n");
        sql.append("where cl.empresa = ").append(empresa).append(" \n");
        sql.append("and au.ativa \n");
        sql.append("and coalesce(au.codigoexterno,'') = '' \n");
        sql.append("and au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
        sql.append("order by p.nome \n");
//        sql.append("limit 1 \n");
        List<AutorizacaoCobrancaClienteVO> lista = new ArrayList<>();
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {

            Pessoa pessoaDAO = new Pessoa(con);
            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(rs.getInt("pessoa"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pessoaDAO = null;

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setCodigo(rs.getInt("cliente"));
            clienteVO.setMatricula(rs.getString("matricula"));
            clienteVO.setPessoa(pessoaVO);

            AutorizacaoCobrancaClienteVO autoVO = new AutorizacaoCobrancaClienteVO();
            autoVO.setCodigo(rs.getInt("autorizacao"));
            autoVO.setTokenAragorn(rs.getString("tokenaragorn"));
            autoVO.setCliente(clienteVO);
            lista.add(autoVO);
        }
        return lista;
    }

    private static String obterPayment_company_code(NazgDTO nazgDTO) {
        OperadorasExternasAprovaFacilEnum operadorasExternasAprovaFacilEnum = OperadorasExternasAprovaFacilEnum.valueOf(nazgDTO.getFlag());
        if (operadorasExternasAprovaFacilEnum.equals(OperadorasExternasAprovaFacilEnum.AMEX)) {
            return "american_express";
        } else if (operadorasExternasAprovaFacilEnum.equals(OperadorasExternasAprovaFacilEnum.DISCOVER)) {
            return OperadorasExternasAprovaFacilEnum.ELO.name().toLowerCase();
        } else {
            return operadorasExternasAprovaFacilEnum.name().toLowerCase();
        }
    }

    private static List<ImportacaoVindiDTO> consultarServico(String chaveVindi) throws Exception {
        Integer perPage = 50;
        int page = 0;
        JSONArray customers;
        List<ImportacaoVindiDTO> listaClientesVindi = new ArrayList<>();
        boolean buscar = true;
        while (buscar) {
            String retorno = executarRequest(chaveVindi, null, "customers?per_page=" + perPage + "&page=" + page++, null, MetodoHttpEnum.GET);
            JSONObject obj = new JSONObject(retorno);
            customers = obj.getJSONArray("customers");

            if (customers.length() > 0) {
                for (int i = 0; i < customers.length(); i++) {
                    JSONObject json = customers.getJSONObject(i);

                    ImportacaoVindiDTO itemDTO = new ImportacaoVindiDTO();
                    itemDTO.setChaveAPI(chaveVindi);
                    itemDTO.setIdVindi(json.opt("id").toString());
                    itemDTO.setCpfVindi(json.opt("registry_code").toString());
                    itemDTO.setNomeVindi(json.optString("name"));

//                if (vemDaEVO) {
//                    adicionarLog(cont++ + " - " + json.getString("name") + " - " + json.opt("code").toString() + " - " + json.opt("id").toString());
//                    updatePessoa(Integer.valueOf(json.opt("id").toString()), Integer.valueOf(json.opt("code").toString()), con,
//                            json.getString("name"), convenio, itemDTO);
//                } else {
//                    if (UteisValidacao.emptyList(alunosFiltrar) || alunosFiltrar.contains(json.getString("name").toLowerCase())) {
//                        updatePessoaSemCode(Integer.valueOf(json.opt("id").toString()), (json.opt("registry_code") == null ? "" : json.opt("registry_code").toString()),
//                                con, json.getString("name"), convenio, itemDTO);
//                    }
//                }
                    listaClientesVindi.add(itemDTO);
                }
            } else {
                buscar = false;
            }
        }
        return listaClientesVindi;
    }

    private static String executarRequest(String chave, String body, String metodo, Map<String, String> params, MetodoHttpEnum metodoHttpEnum) throws Exception {

        Integer limiteRequisicoesPorMinuto = 120;
        Thread.sleep((limiteRequisicoesPorMinuto / 60) * 1000);

        String path = urlAPI + "/" + metodo;
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Basic " + new String(new Base64().encode(chave.getBytes())));
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, params, body, metodoHttpEnum);
        service = null;
        return respostaHttpDTO.getResponse();
    }
}

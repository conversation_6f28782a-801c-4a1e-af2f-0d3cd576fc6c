package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;

/**
 * No ticket MJ-742 foi realizado uma correção no AtualizadorBD para que seja feito a atualização através da ordem de data
 * Como os scripts AtualizacaoTicketPRPI407 e AtualizacaoTicket309 não foram executados 154 chaves
 * antes dessa alteração, foi criado esse processo para executar novamente as atualizações.
 */
@ClasseProcesso(autor = "<PERSON>",
        data = "22/05/2025",
        descricao = "Execução dos scripts AtualizacaoTicketPRPI407 e AtualizacaoTicket309",
        motivacao = "MJ-1024")
public class AtualizacaoTicketMJ1024 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try {
            new AtualizacaoTicket309().executar(con);
        } catch (Exception ex) {
            Uteis.logar("Falha ao executar o processo AtualizacaoTicket309");
            Uteis.logar(ex, AtualizacaoTicketMJ1024.class);
        }
        try {
            new AtualizacaoTicketPRPI407().executar(con);
        } catch (Exception ex) {
            Uteis.logar("Falha ao executar o processo AtualizacaoTicketPRPI407");
            Uteis.logar(ex, AtualizacaoTicketMJ1024.class);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "25/11/2024",
        descricao = "Adicionar CUPOM_DESCONTO aos campos camposAdicionais e camposAdicionaisProduto",
        motivacao = "Habilitar o campo CUPOM_DESCONTO como padrão. Ticket: GC-1089")
public class AjustarColunaCamposAdicionaisTabVendasOnlineConfig implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        String sql = "UPDATE vendasonlineconfig " +
                "SET camposAdicionais = " +
                "    CASE " +
                "        WHEN camposAdicionais LIKE '%CUPOM_DESCONTO%' THEN camposAdicionais " +
                "        WHEN camposAdicionais IS NOT NULL AND camposAdicionais != '' THEN CONCAT(camposAdicionais, ';CUPOM_DESCONTO') " +
                "        ELSE 'CUPOM_DESCONTO' " +
                "    END, " +
                "    camposAdicionaisProduto = " +
                "    CASE " +
                "        WHEN camposAdicionaisProduto LIKE '%CUPOM_DESCONTO%' THEN camposAdicionaisProduto " +
                "        WHEN camposAdicionaisProduto IS NOT NULL AND camposAdicionaisProduto != '' THEN CONCAT(camposAdicionaisProduto, ';CUPOM_DESCONTO') " +
                "        ELSE 'CUPOM_DESCONTO' " +
                "    END;";

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}

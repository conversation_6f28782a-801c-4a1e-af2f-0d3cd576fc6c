package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON>am Soares Bentes",
        data = "07/04/2025",
        descricao = "Atualizar tabela de configurao financeira, adicionando campo obrigarPreenchimentoManualDtCompetencia",
        motivacao = "GC-1720")
public class AtualizacaoTicketGC1720 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaofinanceiro\n" +
                    " ADD COLUMN obrigarPreenchimentoManualDtCompetencia BOOLEAN DEFAULT FALSE;", c);
        }
    }
}
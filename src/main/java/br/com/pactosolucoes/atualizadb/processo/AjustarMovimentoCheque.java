/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ChequeTO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ComportamentoConta;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.Conta;
import negocio.facade.jdbc.financeiro.Lote;
import negocio.facade.jdbc.financeiro.MovConta;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class AjustarMovimentoCheque {

    public static void main(String args[]) throws SQLException {
        Connection con = null;

        try {
            
            con = DriverManager.getConnection("***********************************************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);
            Statement stm = con.createStatement();
            List<Integer> listaCodigosCheques = new ArrayList<Integer>();
            StringBuilder consulta = new StringBuilder("select ch.codigo as codigocheque, ch.valortotal from movpagamento \n");
            consulta.append("inner join cheque ch on ch.movpagamento = movpagamento.codigo \n");
            consulta.append("where ch.datacompesancao \n");
            consulta.append("between '2012-01-01' and '2014-12-31' \n");

            ResultSet rs = stm.executeQuery(consulta.toString());
            Double valorCheques = 0.0;
            while (rs.next()) {
                
                Statement stm2 = con.createStatement();
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT exists( \n");
                sql.append("SELECT c.codigo as codigoconta, c.descricao as contac, lote.codigo as nrlote, lote.pagamovconta, hc.datafim FROM historicocheque hc \n");
                sql.append(" INNER JOIN movconta mc ON mc.codigo = hc.movconta \n");
                sql.append(" INNER JOIN lote ON hc.lote = lote.codigo \n");
                sql.append(" INNER JOIN conta c ON c.codigo = mc.conta \n");
                sql.append(" WHERE hc.cheque = ").append(rs.getInt("codigocheque"));
                sql.append(" AND not lote.avulso \n");
                sql.append(" AND hc.datafim is null \n");
                sql.append("ORDER BY hc.datainicio DESC limit 1) as foo \n");
                ResultSet rsMovconta = stm2.executeQuery(sql.toString());
       
                if(rsMovconta.next()) {
                    
                   if (rsMovconta.getBoolean("foo") == false) {
                        listaCodigosCheques.add(rs.getInt("codigocheque"));
                        valorCheques += rs.getDouble("valortotal");
                    }
                }
            }

            Lote dao = new Lote(con);
            MovConta daoMovConta = new MovConta(con);
            Conta contaDAO = new Conta();
            
            LoteVO novo = new LoteVO();
            UsuarioVO usuario = new UsuarioVO();
            MovContaVO movConta = new MovContaVO();
            PessoaVO pessoa = new PessoaVO();
            
            pessoa.setCodigo(1);
            
            usuario.setCodigo(1);
            
            ContaVO contaVO = contaDAO.consultarPorChavePrimaria(5, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            
            novo.getEmpresa().setCodigo(1);
            novo.setDescricao("Ajuste Cheque");
            novo.setUsuarioResponsavel(usuario);
            novo.setDataLancamento(Calendario.hoje());         
            novo.getEmpresa().setCodigo(1);
            novo.setCheques(new ArrayList<ChequeVO>());
            novo.setValor(valorCheques);
            novo.setConta(contaVO.getAgencia());
            novo.setConta(contaVO.getDescricao());
            for (Integer ch : listaCodigosCheques) {
                ChequeTO to = new ChequeTO();
                to.setCodigo(ch);
                novo.getChequesTO().add(to);
                ChequeVO vo = new ChequeVO();
                vo.setCodigo(ch);
                novo.getCheques().add(vo);
            }
            
            dao.incluir(novo);
            System.out.println(novo.getCodigo());         
            
            movConta.setLote(novo);
            movConta.getEmpresaVO().setCodigo(1);
            movConta.setTipoOperacaoLancamento(TipoOperacaoLancamento.CUSTODIA);            
            movConta.setPessoaVO(pessoa);
            movConta.setDescricao("Ajuste Pacto");
            movConta.setValor(valorCheques);
            movConta.setDataVencimento(Calendario.hoje());
            movConta.setDataCompetencia(Calendario.hoje());
            movConta.setUsuarioVO(usuario);
            movConta.setContaVO(contaVO);
            
            daoMovConta.incluir(movConta, 0, false, ComportamentoConta.COFRE);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            con.close();
        }
    }
}

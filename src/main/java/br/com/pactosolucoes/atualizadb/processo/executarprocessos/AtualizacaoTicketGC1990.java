
package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "30/04/2025",
        descricao = "Adicionando dados de Observacoes na tabela plano",
        motivacao = "Adicionando dados de Observacoes na tabela plano")
public class AtualizacaoTicketGC1990 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN observacao1 text;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ADD COLUMN observacao2 text;", c);
        }
    }
}
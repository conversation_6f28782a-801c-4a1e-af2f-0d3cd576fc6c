/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import static br.com.pactosolucoes.atualizadb.processo.AjustandoHistoricoAVencer.ajustarHistoricoAVencer;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.crm.AberturaMetaControle;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ThreadEnviarEmail;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class ProcessarMetasDoDia {
    
       public static void main(String... args) {
        try {
            Uteis.debug = true;
            String nomeThread = "pacto7";
            DAO dao = new DAO();
            if (nomeThread.equals("todas")) {
                List<String> l = dao.buscarListaChaves();
                for (String chave : l) {
                    try (Connection con = dao.obterConexaoEspecifica(chave)) {
                        abrirFecharMetas(con);
                    } catch (Exception e) {
                        Uteis.logar(e, ProcessarMetasDoDia.class);
                    }
                }
            }else {
                Connection con = dao.obterConexaoEspecifica(nomeThread);
                abrirFecharMetas(con);
            }
        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
     public static void abrirFecharMetas(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        try {
            new AberturaMetaControle().fecharMetas(Uteis.somarDias(Calendario.hoje(), -1));
            new AberturaMetaControle().abrirMetas(Calendario.hoje());
         } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
         } finally{
            ThreadEnviarEmail.finalmente();
        }
    }
    
}

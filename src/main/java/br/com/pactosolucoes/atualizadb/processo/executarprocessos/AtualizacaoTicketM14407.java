package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Douglas Soares",
        data = "13/03/2025",
        descricao = "Criação do campo 'bloquearrecompra' na tabela 'plano'",
        motivacao = "Ticket M1-4407 - Necessidade de restringir recompra de plano ativo"
)
public class AtualizacaoTicketM14407 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            StringBuilder sql = new StringBuilder();
            sql.append("ALTER TABLE plano ")
                    .append("ADD COLUMN bloquearrecompra BOOLEAN DEFAULT false NOT NULL;");

            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), c);
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class MigrarPlanoParaAnual {
    public static void main(String... args) {
        try {
             /*
             * arg0 - chave da empresa
             * arg1 - plano atual
             * arg2 - plano Novo
             * arg3 - duracao atual
             * arg4 - codigo produto Plano Mensal
             * arg5 - condicao Pagamento Nova
             * arg6 - data Matricula (apenas contratos com data de matricula anterior a essa data serão ajustados)
             */
            Connection c = new DAO().obterConexaoEspecifica(args[0]);
            
            int planoAtual = Integer.parseInt(args[1]);
            int planoNovo = Integer.parseInt(args[2]);
            int duracaoatual = Integer.parseInt(args[3]);
            int produtoPlanoMensal = Integer.parseInt(args[4]);
            int condicaoPagamentoNova = Integer.parseInt(args[5]);
            Date dataMatricula = Uteis.getDate(args[6]);
       
            
            new MigrarPlanoParaAnual().passarParaAnual(c, planoAtual,planoNovo, duracaoatual, produtoPlanoMensal,condicaoPagamentoNova,dataMatricula);

        } catch (Exception ex) {
            Logger.getLogger(MigrarPlanoParaAnual.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void passarParaAnual(Connection con,int planoAtual,int planoNovo,int duracaoatual, int produtoPlanoMensal,int condicaoPagamentoNova,Date dataMatricula ) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato,* from contrato c  inner join contratoduracao cd on c.codigo = cd.contrato where c.plano = "+planoAtual+" and c.situacao = 'AT' and c.contratoresponsavelrenovacaomatricula  = 0  and cd.numeromeses = "+duracaoatual+" and datamatricula < '"+Uteis.getDataJDBC(dataMatricula)+"' and not exists(select codigo from contratooperacao where contrato = c.codigo and tipooperacao = 'CA')", con);
        MovProduto movProdutoDAO = new MovProduto(con); 
        MovParcela movParcelaDAO = new MovParcela(con);
        MovProdutoParcela mppDAO = new MovProdutoParcela(con);
        MovProdutoModalidade mpmDAO = new MovProdutoModalidade(con);
        List<MovProdutoVO> produtoContrato = new ArrayList<MovProdutoVO>();
        List<MovProdutoModalidadeVO> movProdModalidade = new ArrayList<MovProdutoModalidadeVO>();
        MovParcelaVO parcelaContrato;
        MovProdutoParcelaVO movPPVO;
        String descricaoPlanoAtual = "";
        
        boolean planoAtualRecorrencia = false; 
        boolean planoNovoRecorrencia = false; 
        ResultSet consultaPlanoAtual = SuperFacadeJDBC.criarConsulta("select descricao,recorrencia from plano where codigo = "+planoAtual, con);
        if(consultaPlanoAtual.next()){
            descricaoPlanoAtual = consultaPlanoAtual.getString("descricao");
            planoAtualRecorrencia = consultaPlanoAtual.getBoolean("recorrencia");
        }
        String descricaoPlanoNovo = "";
        ResultSet consultaPlanoNovo = SuperFacadeJDBC.criarConsulta("select descricao,recorrencia from plano where codigo = "+planoNovo, con);
        if(consultaPlanoNovo.next()){
            descricaoPlanoNovo = consultaPlanoNovo.getString("descricao");
            planoNovoRecorrencia = consultaPlanoNovo.getBoolean("recorrencia");
        }
        if(planoAtualRecorrencia ^ planoNovoRecorrencia){
            throw new Exception("Um plano é de recorrência e o outro não. Tanto o novo quanto o atual devem ser do mesmo tipo");
        }
        int count = 0;
        while (consulta.next()) {
            con.setAutoCommit(false);
            Double valorMensal=0.0;
            try {
                produtoContrato = movProdutoDAO.consultar("select * from movproduto where contrato = "+consulta.getInt("contrato")+" and produto = "+produtoPlanoMensal+" and descricao not like 'REAJUSTE MONETARIO%' and  descricao not like 'PLANO TRANSFERIDO%' order by codigo desc limit 1 ", Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                ResultSet consultaValorMensal = SuperFacadeJDBC.criarConsulta("select sum(totalfinal) as valormensal from movproduto  where contrato = "+consulta.getInt("contrato")+" and produto = "+produtoPlanoMensal+" and mesreferencia ='"+produtoContrato.get(0).getMesReferencia()+"'", con);
                 if(consultaValorMensal.next()){
                     valorMensal = consultaValorMensal.getDouble("valormensal");
                 } else {
                     System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " não teve alteração, pois não foi possivel consultar valor mensal");
                     continue;
                 }
                parcelaContrato = movParcelaDAO.consultarPorMovProduto(produtoContrato.get(0).getCodigo().intValue(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                movProdModalidade = mpmDAO.consultarPorMovProduto(produtoContrato.get(0).getCodigo(),  Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if(planoAtual != planoNovo){
                    SuperFacadeJDBC.executarConsultaUpdate("update contrato set plano = '"+planoNovo+"' where codigo  = "+consulta.getInt("contrato") , con);
                    SuperFacadeJDBC.executarConsultaUpdate("update movproduto set descricao  = replace(descricao, '"+descricaoPlanoAtual+"','"+descricaoPlanoNovo+"') where contrato  = "+consulta.getInt("contrato") , con);
                    produtoContrato.get(0).setDescricao(produtoContrato.get(0).getDescricao().replace(descricaoPlanoAtual, descricaoPlanoNovo));
                }
                int indice = 1;
                int mesesAdicionar = 12 - duracaoatual;
                Date novaVigencia = Uteis.somarCampoData(consulta.getDate("vigenciaate"), Calendar.MONTH,  mesesAdicionar);            
                Date novaVigenciaAjustada = novaVigencia;
                if(!Calendario.igual(consulta.getDate("vigenciaate"),consulta.getDate("vigenciaateajustada")) ){
                    int diferenca = (int) (Uteis.nrDiasEntreDatas(consulta.getDate("vigenciaate"), consulta.getDate("vigenciaateajustada")) + 1);
                    novaVigenciaAjustada = Uteis.somarDias(novaVigencia, diferenca);
                }
                Date competencia = produtoContrato.get(0).getDataInicioVigencia();
                Date vencimento = parcelaContrato.getDataVencimento();
                while(indice <= mesesAdicionar ){
                    competencia = Uteis.obterDataFuturaParcela(competencia, 1);
                    vencimento =  Uteis.obterDataFuturaParcela(vencimento, 1);
                    parcelaContrato.setDataVencimento(vencimento);
                    parcelaContrato.setValorParcela(valorMensal);
                    parcelaContrato.setDescricao("PARCELA "+(indice+ 1));
                    parcelaContrato.setSituacao("EA");
                    movParcelaDAO.incluirSemCommit(parcelaContrato);
                    produtoContrato.get(0).setAnoReferencia(Uteis.getAnoData(competencia));
                    produtoContrato.get(0).setMesReferencia(Uteis.getMesReferenciaData(competencia));
                    produtoContrato.get(0).setDescricao(produtoContrato.get(0).getDescricao().substring(0, (produtoContrato.get(0).getDescricao().length() - 7))+ Uteis.getMesReferenciaData(competencia)) ;
                    produtoContrato.get(0).setPrecoUnitario(valorMensal);
                    produtoContrato.get(0).setTotalFinal(valorMensal);
                    produtoContrato.get(0).setValorDesconto(0.0);
                    produtoContrato.get(0).setSituacao("EA");
                    produtoContrato.get(0).setMovProdutoModalidades(movProdModalidade);
                    movProdutoDAO.incluirSemCommit(produtoContrato.get(0));
                    movPPVO = new MovProdutoParcelaVO();
                    movPPVO.setMovParcela(parcelaContrato.getCodigo());
                    movPPVO.setMovProdutoVO(produtoContrato.get(0));
                    movPPVO.setMovProduto(produtoContrato.get(0).getCodigo());
                    movPPVO.setValorPago(valorMensal);
                    mppDAO.incluir(movPPVO);
                    indice++;
                }
                SuperFacadeJDBC.executarConsultaUpdate("update movproduto set datafinalvigencia = '"+Uteis.getDataFormatoBD(novaVigencia)+"' where contrato = "+consulta.getInt("contrato")+" and datafinalvigencia is not null;" , con);
                SuperFacadeJDBC.executarConsultaUpdate("update contratocondicaopagamento  set condicaopagamento  = "+condicaoPagamentoNova+" where contrato = "+consulta.getInt("contrato") , con);
                SuperFacadeJDBC.executarConsultaUpdate("update movprodutomodalidade  set datafim  = '"+Uteis.getDataFormatoBD(novaVigencia)+"' where contrato = "+ consulta.getInt("contrato") , con);
                SuperFacadeJDBC.executarConsultaUpdate("update contrato c set valorbasecalculo  = (select sum(totalfinal) from movproduto  where contrato = c.codigo  and  produto = "+produtoPlanoMensal+") where codigo =  "+ consulta.getInt("contrato") , con);
                SuperFacadeJDBC.executarConsultaUpdate("update contrato  set valorfinal  = (valorbasecalculo+ somaproduto), dataprevistarenovar  = '"+Uteis.getDataFormatoBD(novaVigenciaAjustada)+"', vigenciaateajustada  = '"+Uteis.getDataFormatoBD(novaVigenciaAjustada)+"', vigenciaate = '"+Uteis.getDataFormatoBD(novaVigencia)+"' where codigo = "+ consulta.getInt("contrato") , con);
                SuperFacadeJDBC.executarConsultaUpdate("update periodoacessocliente set datafinalacesso  = '"+Uteis.getDataFormatoBD(novaVigenciaAjustada)+"' where codigo in(select codigo from periodoacessocliente  where   contrato  = "+ consulta.getInt("contrato")+" order by datafinalacesso desc limit 1)" , con);
                SuperFacadeJDBC.executarConsultaUpdate("delete from historicocontrato  where contrato = "+ consulta.getInt("contrato")+" and tipohistorico = 'AV';" , con);
                SuperFacadeJDBC.executarConsultaUpdate("update historicocontrato   set datafinalsituacao = '"+Uteis.getDataFormatoBD(novaVigenciaAjustada)+"' where codigo in(select codigo from historicocontrato  where    contrato  = "+ consulta.getInt("contrato")+" order by datafinalsituacao desc limit 1);" , con);
                SuperFacadeJDBC.executarConsultaUpdate("update contratoduracao c set numeromeses  = (select count(codigo) from movproduto  where contrato = c.contrato  and  produto ="+produtoPlanoMensal+") where contrato = "+ consulta.getInt("contrato"),con);
                System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " teve sua vigencia  alterada de   " + Uteis.getData(consulta.getDate("vigenciaateajustada")) +" para "+ Uteis.getData(novaVigenciaAjustada));
                con.commit();
            } catch (Exception e){
                con.rollback();
                System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " teve erro na alteração:  " + e.getMessage());
            } finally{
                con.setAutoCommit(true);
            }
        }
    }
    
}

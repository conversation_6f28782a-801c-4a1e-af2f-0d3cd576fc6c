package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Victor August<PERSON>",
        data = "26/12/2024",
        descricao = "Criar tabela de registro de mensagens do whatsapp já enviadas",
        motivacao = "PRPI-388")
public class AtualizacaoTicketPRPI388 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE mensagemenviada (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    codigoempresa INTEGER,\n" +
                    "    dataenivo DATE NOT NULL,\n" +
                    "    nomefase varchar NOT NULL,\n" +
                    "    codigoscliente TEXT NOT NULL\n" +
                    ");", c);
        }
    }
}
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class ProcessoAjustarContratosSemConsultor {

    public static void main(String... args) {
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"mybox"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            Uteis.debug = true;

            Connection con = new DAO().obterConexaoEspecifica(chave);
            ProcessoAjustarContratosSemConsultor.corrigirContratos(con);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void corrigirContratos(Connection con) throws Exception {
        String sqlUpdateContrato = "";
        StringBuilder sqlContratoAlterar = new StringBuilder();
        sqlContratoAlterar.append("SELECT \n");
        sqlContratoAlterar.append("con.codigo AS codigocontrato, cli.codigomatricula AS matricula, vin.colaborador AS colaborador \n");
        sqlContratoAlterar.append("FROM contrato con \n");
        sqlContratoAlterar.append("INNER JOIN cliente cli ON cli.pessoa = con.pessoa \n");
        sqlContratoAlterar.append("INNER JOIN vinculo vin ON vin.cliente = cli.codigo \n");
        sqlContratoAlterar.append("WHERE con.consultor IS NULL \n");
        sqlContratoAlterar.append("AND vin.tipovinculo = 'CO';");

        PreparedStatement sqlConsultaContratosAlterar = con.prepareStatement(sqlContratoAlterar.toString());
        ResultSet rs = sqlConsultaContratosAlterar.executeQuery();
        try {
            Uteis.logar("INÍCIO | ProcessoAjustarContratosSemConsultor");
            while (rs.next()) {
                Uteis.logar("Vinculando contrato " + rs.getInt("codigocontrato") + " ao colaborador " + rs.getInt("colaborador"));
                sqlUpdateContrato = "UPDATE contrato \n" +
                                    "SET consultor = " +  rs.getInt("colaborador") + " \n" +
                                    "WHERE codigo = " + rs.getInt("codigocontrato");
                SuperFacadeJDBC.executarUpdate(sqlUpdateContrato, con);
            }
        } catch (Exception ex){
            throw ex;
        } finally {
            Uteis.logar("FIM | ProcessoAjustarContratosSemConsultor");
        }
    }


}

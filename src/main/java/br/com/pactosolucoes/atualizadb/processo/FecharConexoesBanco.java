/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
/**

/**
 *
 * <AUTHOR>
 */
public class FecharConexoesBanco {
     public static void main(String... args) {
        try {
            String host = args[0];
            Connection con1 = DriverManager.getConnection("jdbc:postgresql://"+host+":5432/postgres", "postgres", "pactodb");
            fecharConexoes(con1, args[1]);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void  fecharConexoes(Connection con, String nomeBancos) throws Exception {
        String[] bancos =nomeBancos.split(",");
        for (int i= 0; i < bancos.length; i++){
            ResultSet consulta = SuperFacadeJDBC.criarConsulta("select procpid from pg_stat_activity where datname = '"+bancos[i]+"'", con);

            while (consulta.next()) {

                String sql = "select pg_terminate_backend (?);";

                PreparedStatement sqlDeletar = con.prepareStatement(sql);

                sqlDeletar.setInt(1, consulta.getInt("procpid"));
                sqlDeletar.execute();
            }
        }
    }

}

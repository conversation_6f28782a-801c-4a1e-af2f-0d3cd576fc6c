package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class RecuperarEmailDeletadoBounce {

    private Map<String, List<JSONObject>> emailsRecuperados = new HashMap<String, List<JSONObject>>();

    public static void main(String[] args) {
        try {
            String chave = "bdzillyonrunway-2017-09-30";
            if (args.length > 0) {
                chave = args[0];
            }
            Connection c = new DAO().obterConexaoEspecifica(chave);

            RecuperarEmailDeletadoBounce rec = new RecuperarEmailDeletadoBounce();
            rec.recuperarEmails(c);
        } catch (Exception ex) {
            Logger.getLogger(RecuperarEmailDeletadoBounce.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private Map<String, List<JSONObject>> getEmailsRecuperados() {
        return emailsRecuperados;
    }

    public void recuperarEmails(Connection c) throws Exception {
        List<LogVO> logs = obterLogs(c);
        for (LogVO log : logs) {
            String[] partes = log.getValorCampoAlterado().split("Locaweb: ");
            if (partes.length > 1) {
                try {
                    JSONObject object = new JSONObject(partes[1]);
                    String chave = null;
                    try {
                        chave = object.getString("recipient");
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    String chaveMapa = chave + ";" + log.getPessoa();
                    if (chave != null) {
                        List<JSONObject> objects = getEmailsRecuperados().get(chaveMapa);
                        if (objects == null) {
                            objects = new ArrayList<JSONObject>();
                        }
                        getEmailsRecuperados().put(chaveMapa, objects);
                    } else {
                        System.out.println(partes[1]);
                    }
                } catch (Exception ex) {
                    Uteis.logar(null, partes[1]);
                }
            }
        }

        for (String email : getEmailsRecuperados().keySet()) {
            String[] tmp = email.split(";");
            String emailCad = tmp[0];
            Integer codPessoa = Integer.parseInt(tmp[1]);
            try {
                incluirEmail(emailCad, codPessoa, c);
            } catch (Exception ex) {
                Uteis.logar(ex, RecuperarEmailDeletadoBounce.class);
            }
        }
    }

    private List<LogVO> obterLogs(Connection con) throws SQLException {
        String sqlStr = "SELECT * FROM log WHERE valorcampoalterado like '%Locaweb%' ORDER BY dataAlteracao desc";
        Statement stm = con.createStatement();
        ResultSet dadosSQL = stm.executeQuery(sqlStr);
        List<LogVO> vetResultado = new ArrayList<LogVO>();
        while (dadosSQL.next()) {
            LogVO obj = new LogVO();
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setNomeEntidade(dadosSQL.getString("nomeEntidade"));
            obj.setNomeEntidadeDescricao(dadosSQL.getString("nomeEntidadeDescricao"));
            obj.setChavePrimaria(dadosSQL.getString("chavePrimaria"));
            obj.setChavePrimariaEntidadeSubordinada(dadosSQL.getString("chavePrimariaEntidadeSubordinada"));
            obj.setNomeCampo(dadosSQL.getString("nomeCampo"));
            obj.setValorCampoAnterior(dadosSQL.getString("valorCampoAnterior"));
            obj.setValorCampoAlterado(dadosSQL.getString("valorCampoAlterado"));
            obj.setDataAlteracao(dadosSQL.getTimestamp("dataAlteracao"));
            obj.setResponsavelAlteracao(dadosSQL.getString("responsavelAlteracao"));
            obj.setOperacao(dadosSQL.getString("operacao"));
            obj.setPessoa(dadosSQL.getInt("pessoa"));
            obj.setNovoObj(false);

            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private void incluirEmail(String email, Integer codPessoa, Connection con) throws Exception {
        EmailVO emailVO = new EmailVO();
        emailVO.setBloqueadoBounce(true);
        emailVO.setEmailCorrespondencia(true);
        emailVO.setEmail(email);
        emailVO.setPessoa(codPessoa);

        if (!existeEmail(emailVO, con)) {
            incluir(emailVO, con);
        } else {
            alterarBounce(emailVO, con);
        }
    }

    private void alterarBounce(EmailVO obj, Connection con) throws SQLException {
        obj.realizarUpperCaseDados();
        String sql = "UPDATE email SET bloqueadoBounce = TRUE WHERE email = ? AND pessoa = ?";
        PreparedStatement sqlUpdate = con.prepareStatement(sql);
        sqlUpdate.setString(1, obj.getEmail());
        sqlUpdate.setInt(2, obj.getPessoa());
        sqlUpdate.execute();
    }

    private void incluir(EmailVO obj, Connection con) throws Exception {
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Email(email, pessoa, emailCorrespondencia, bloqueadoBounce) VALUES (?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getEmail());
        sqlInserir.setInt(2, obj.getPessoa());
        sqlInserir.setBoolean(3, obj.getEmailCorrespondencia());
        sqlInserir.setBoolean(4, obj.getBloqueadoBounce());
        sqlInserir.execute();
    }

    private boolean existeEmail(EmailVO obj, Connection con) throws Exception {
        return SuperFacadeJDBC.existe("SELECT codigo FROM email WHERE email like '" + obj.getEmail() + "' AND pessoa = " + obj.getPessoa(), con);
    }

}

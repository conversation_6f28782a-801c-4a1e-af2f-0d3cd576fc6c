package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.enumerador.ISPBContaBancaria;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.Banco;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "19/06/2024",
        descricao = "Integração Kobana",
        motivacao = "M2-1785")
public class CriarTabelasIntegracaoKobana implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.integracaoKobana (\n" +
                    "codigo SERIAL PRIMARY KEY, empresa INTEGER, email VARCHAR, business_cnpj VARCHAR,\n" +
                    "nickname VARCHAR, business_legal_name VARCHAR, api_access_token TEXT, id BIGINT,\n" +
                    "created_At TIMESTAMP WITHOUT TIME ZONE, ativo BOOLEAN, ambiente int\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.financialAccountKobana (\n" +
                    "codigo SERIAL PRIMARY KEY, empresa INTEGER, uid TEXT, created_At TIMESTAMP WITHOUT TIME ZONE,\n" +
                    "financial_provider_slug VARCHAR, codIntegracaoKobana INTEGER, ativo BOOLEAN\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.historicoIntegracaoKobana (\n" +
                    "codigo SERIAL PRIMARY KEY, empresa INTEGER, metodo VARCHAR, lote INT, paramsEnvio TEXT, paramsRetorno TEXT,\n" +
                    "dataRegistro TIMESTAMP WITHOUT TIME ZONE, sucesso BOOLEAN);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.loteKobana (\n" +
                    "codigo SERIAL PRIMARY KEY, empresa INTEGER, valor DOUBLE PRECISION, tipoContaPagarLote INT, uid VARCHAR, status INT, registration_status INT,\n" +
                    " financial_account_uid VARCHAR, created_at TIMESTAMP WITHOUT TIME ZONE, updated_at TIMESTAMP WITHOUT TIME ZONE,\n" +
                    " paramsEnvio TEXT, paramsRetorno TEXT);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.loteKobanaItem (\n" +
                    "codigo SERIAL PRIMARY KEY, valor DOUBLE PRECISION, lotekobana INT, movconta INT, uid VARCHAR, status INT,\n" +
                    " registration_status int, financial_account_uid VARCHAR, created_at TIMESTAMP WITHOUT TIME ZONE,\n" +
                    " updated_at TIMESTAMP WITHOUT TIME ZONE, codigobarras TEXT,\n" +
                    " qrcode TEXT, contaBancariaFornecedor INT, rejected_error TEXT, rejected_at TIMESTAMP WITHOUT TIME ZONE);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.contaBancariaFornecedor (\n" +
                    "codigo SERIAL PRIMARY KEY, agency_number VARCHAR(20), agency_digit VARCHAR(1), account_number VARCHAR(20), account_digit VARCHAR(1), \n" +
                    " pessoa INT, banco INT, cpfoucnpj varchar(14));\n", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.kobanaWebhook (\n" +
                    " codigo SERIAL PRIMARY KEY, dataRegistro TIMESTAMP WITHOUT TIME ZONE, dados TEXT, event_code VARCHAR, \n" +
                    " processado boolean default false, dataProcessamento TIMESTAMP WITHOUT TIME ZONE, uidLoteKobanaItem VARCHAR, \n" +
                    " codLoteKobanaItem INT, uidLoteKobana VARCHAR, codLoteKobana INT, movConta INTEGER);\n", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.movconta ADD COLUMN contadeconsumo BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.movconta ADD COLUMN presaemlotedepagamento BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.movconta ADD COLUMN lotedepagamento BIGINT DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.movconta ADD COLUMN pagoOrigemWebhook BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.movconta ADD COLUMN cpfoucnpjbeneficiario varchar(18);", c);

            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT\n"
                    + "codigo\n"
                    + "FROM perfilacesso where tipo = " + PerfilUsuarioEnum.ADMINISTRADOR.getId(), c);
            while (rs.next()) {
                String sql1 = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (3, '9.102 - Permite consultar Lotes de Pagamento','(0)(1)(2)(3)(9)(12)', "
                        + " 'ConsultarLotesPagamento', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql1, c);
                String sql2 = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (3, '9.103 - Permite criar Lotes de Pagamento','(0)(1)(2)(3)(9)(12)', "
                        + " 'CriarLotesPagamento', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql2, c);
            }

            //Preencher ISPB Bancos
            Banco bancoDAO;
            try {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.banco ADD COLUMN ispb VARCHAR;", c);
                bancoDAO = new Banco(c);
                List<BancoVO> bancos = bancoDAO.consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyList(bancos)) {
                    for (BancoVO banco : bancos) {
                        ISPBContaBancaria info = ISPBContaBancaria.obterPorCodigoBanco(banco.getCodigoBanco());
                        if (!UteisValidacao.emptyString(info.getIspb())) {
                            bancoDAO.incluirISPB(info.getIspb(), banco.getCodigoBanco());
                        }
                    }
                }
            } finally {
                bancoDAO = null;
            }

            try {
                //verificar se já possui o PagBank cadastrado
                ResultSet rs2 = SuperFacadeJDBC.criarConsulta("select * from banco where codigobanco = 290", c);
                if (rs2 != null && !rs2.next()) {
                    String sql3 = "INSERT INTO banco(nome, codigobanco, ispb) VALUES ('PagSeguro Internet', 290, '********')";
                    SuperFacadeJDBC.executarUpdateExecutarProcessos(sql3, c);
                }
            } catch (Exception ex) {
                Uteis.logarDebug("Não foi possível fazer o select do executarprocessos CriarTabelasIntegracaoKobana: " + ex.getMessage());
            }
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;

import java.io.File;
import java.io.FileOutputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.*;

public class GeralRelatorioReceitaGeralPorContrato {



    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("*******************************************", "postgres", "pactodb");
            String tipoformaPagamento = "AV";
            String descricaoFormaPagamento = "DINHEIRO";
            gerarRelatorio(con, 1 ,tipoformaPagamento , descricaoFormaPagamento);
            gerarRelatorio(con, 2,tipoformaPagamento , descricaoFormaPagamento);
            tipoformaPagamento = "CH";
            descricaoFormaPagamento = "CHEQUE";
            gerarRelatorio(con, 1 ,tipoformaPagamento , descricaoFormaPagamento);
            gerarRelatorio(con, 2,tipoformaPagamento , descricaoFormaPagamento);
            tipoformaPagamento = "CD";
            descricaoFormaPagamento = "CARTAO-DEBITO";
            gerarRelatorio(con, 1 ,tipoformaPagamento , descricaoFormaPagamento);
            gerarRelatorio(con, 2,tipoformaPagamento , descricaoFormaPagamento);
            tipoformaPagamento = "CA";
            descricaoFormaPagamento = "CARTAO-CREDITO";
            gerarRelatorio(con, 1 ,tipoformaPagamento , descricaoFormaPagamento);
            gerarRelatorio(con, 2,tipoformaPagamento , descricaoFormaPagamento);
            tipoformaPagamento = "";
            descricaoFormaPagamento = "GERAL";
            gerarRelatorio(con, 1 ,tipoformaPagamento , descricaoFormaPagamento);
            gerarRelatorio(con, 2,tipoformaPagamento , descricaoFormaPagamento);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void  gerarRelatorio(Connection con, Integer empresa, String tipoformaPagamento, String descricaoFormaPagamento) {
        try {
            ResultSet rsPeriodo = SuperFacadeJDBC.criarConsulta("select min(dataminima) as inicio, max(datamaxima) as fim from (select min(datacompesancao) as dataminima, max(datacompesancao) as datamaxima from cheque union all  select min(datacompesancao) as dataminima,max(datacompesancao) as datamaxima from cartaocredito  union all select min(datapagamento) as dataminima, max(datapagamento)  as datamaxima from movpagamento) as foo" , con);
            rsPeriodo.next();
            Date dataInicio = rsPeriodo.getDate("inicio");
            Date dataFim = rsPeriodo.getDate("fim");
            Map<String, Integer> mapaColunas = new HashMap<String, Integer>();
            HSSFWorkbook hssfWorkbook = criarTabela(dataInicio, dataFim, mapaColunas);
            StringBuilder sqlContratos = new StringBuilder("select cl.matricula, p.nome as NomeCliente, con.codigo as contrato, con.situacao,con.situacaocontrato, \n");
            sqlContratos.append("Case when p.cfp is not null and p.cfp <> ''  then p.cfp When  p.cpfmae is not null and p.cpfmae <> ''  then p.cpfmae When  p.cpfpai is not null and p.cpfpai <> ''  then p.cpfpai \n");
            sqlContratos.append("when  cl.pessoaresponsavel  is not null then (select cfp from pessoa where codigo = cl.pessoaresponsavel) else 'não informado' end as cpf,\n");
            sqlContratos.append("cp.descricao as CondicaoPagamento, cd.numeromeses as duracao, co.dataoperacao as datacancelamento\n");
            sqlContratos.append(", con.vigenciade, con.vigenciaate, con.vigenciaateajustada, con.valorfinal,  con.valorbasecalculo,\n");
            sqlContratos.append("(select sum(totalfinal) from movproduto mp inner join produto p on p.codigo = mp.produto where mp.codigo in (select movproduto from movprodutoparcela mpp inner join movparcela par on par.codigo = mpp.movparcela where par.contrato = con.codigo) and tipoproduto not in ('PM', 'MM', 'DV','RD')) as valorprodutos\n");
            sqlContratos.append("from  pessoa p inner join cliente cl on cl.pessoa = p.codigo\n");
            sqlContratos.append("inner join contrato con on con.pessoa = cl.pessoa\n");
            sqlContratos.append("inner join contratocondicaopagamento  ccp on ccp.contrato = con.codigo\n");
            sqlContratos.append("inner join condicaopagamento cp on cp.codigo = ccp.condicaopagamento\n");
            sqlContratos.append(" inner join contratoduracao cd on cd.contrato = con.codigo\n");
            sqlContratos.append(" left join contratooperacao co on co.contrato = con.codigo and co.tipooperacao = 'CA'  where con.empresa = ").append(empresa);
            if(!UteisValidacao.emptyString(tipoformaPagamento)){
                sqlContratos.append(" and exists(");
                sqlContratos.append("select mp.codigo from movparcela mp inner join pagamentomovparcela pmp on mp.codigo = pmp.movparcela inner join movpagamento pag on pag.codigo = pmp.movpagamento\n");
                sqlContratos.append("inner join formapagamento fp on fp.codigo = pag.formapagamento left join cheque ch on ch.movpagamento = pag.codigo and ch.situacao = 'DV'\n");
                sqlContratos.append("left join movproduto pro on pro.chequedevolucao = ch.codigo left join movprodutoparcela mpp on mpp.movproduto = pro.codigo \n");
                sqlContratos.append(" left join pagamentomovparcela pmpch on pmpch.movparcela = mpp.movparcela  left join movpagamento pagch on pagch.codigo = pmpch.movpagamento \n");
                sqlContratos.append("left join formapagamento fpch on fpch.codigo =pagch.formapagamento\n");
                sqlContratos.append(" where mp.contrato = con.codigo and (fp.tipoformapagamento = 'CA' or fpch.tipoformapagamento = 'CA')  limit 1 \n");
                sqlContratos.append(")");
            }
            sqlContratos.append(" order by p.nome,con.codigo\n");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlContratos.toString() , con);
            int rownum = 1;
            int cellnum = 0;
            Row row;
            while (rs.next()) {
                row = hssfWorkbook.getSheetAt(0).createRow(rownum++);
                cellnum = 0;
                criarCelula(hssfWorkbook, cellnum++, row, rs.getString("matricula"));
                criarCelula(hssfWorkbook, cellnum++, row, rs.getString("NomeCliente"));
                criarCelula(hssfWorkbook, cellnum++, row, rs.getString("cpf"));
                criarCelula(hssfWorkbook, cellnum++, row, new Integer(rs.getInt("contrato")));
                criarCelula(hssfWorkbook, cellnum++, row, rs.getString("CondicaoPagamento"));
                criarCelula(hssfWorkbook, cellnum++, row, getTipoContrato(rs.getString("situacaocontrato")));
                criarCelula(hssfWorkbook, cellnum++, row, getSituacao_Descricao(rs.getString("situacao")));
                criarCelula(hssfWorkbook, cellnum++, row, Uteis.getData(rs.getDate("datacancelamento"), "dd/MM/yyyy"));
                criarCelula(hssfWorkbook, cellnum++, row, new Integer(rs.getInt("duracao")));
                criarCelula(hssfWorkbook, cellnum++, row, Uteis.getData(rs.getDate("vigenciade"), "dd/MM/yyyy"));
                criarCelula(hssfWorkbook, cellnum++, row, Uteis.getData(rs.getDate("vigenciaate"), "dd/MM/yyyy"));
                criarCelula(hssfWorkbook, cellnum++, row, Uteis.getData(rs.getDate("vigenciaateajustada"), "dd/MM/yyyy"));
                criarCelula(hssfWorkbook, cellnum++, row, new Double(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorfinal"))));
                criarCelula(hssfWorkbook, cellnum++, row, new Double(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorbasecalculo"))));
                criarCelula(hssfWorkbook, cellnum++, row, new Double(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorprodutos"))));
                processarReceitaContrato(hssfWorkbook,row, rs.getInt("contrato"), mapaColunas, con, tipoformaPagamento);
            }

        File file = new File("C:" + File.separator + "opt" + File.separator +"ReceitaGeralContrato-Empresa-"+empresa+"-"+descricaoFormaPagamento+"-"+ Calendario.hoje().getTime() + ".xls");
            FileOutputStream out = new FileOutputStream(file);
            hssfWorkbook.write(out);
            out.close();
            hssfWorkbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void processarReceitaContrato(HSSFWorkbook hssfWorkbook, Row row, int contrato, Map<String, Integer> mapaColunas, Connection con, String tipoformaPagamento) throws Exception {
        Map<String, Double> mapaReceitaContrato = new HashMap<String,Double>();
        StringBuilder stringReceita = new StringBuilder("select pag.codigo, datacompesancao as compensacao, produtospagos, valor, situacao, (select exists(select pmp.codigo from pagamentomovparcela pmp inner join movparcela par\n");
        stringReceita.append(" on par.codigo = pmp.movparcela where movpagamento = pag.movpagamento and (par.contrato <> ").append(contrato).append(" or par.contrato is null) )) as  pagaoutracoisa, 'CA' as tipopagamento from cartaocredito pag  where movpagamento in \n");
        stringReceita.append("(select movpagamento from pagamentomovparcela pmp inner join movparcela par on par.codigo = pmp.movparcela where contrato = ").append(contrato).append(") and situacao = 'EA'\n");
        stringReceita.append("union all \n");
        stringReceita.append("select pag.codigo, datacompesancao as compensacao, produtospagos, valor,situacao, (select exists(select pmp.codigo from pagamentomovparcela pmp inner join movparcela par on par.codigo = pmp.movparcela \n");
        stringReceita.append("where movpagamento = pag.movpagamento and (par.contrato <> ").append(contrato).append(" or par.contrato is null))) as  pagaoutracoisa , 'CH' as tipopagamento  from cheque pag  where movpagamento in  \n");
        stringReceita.append("(select movpagamento from pagamentomovparcela pmp inner join movparcela par on par.codigo = pmp.movparcela where contrato = ").append(contrato).append(") and situacao <> 'CA'  \n");
        stringReceita.append("union all \n");
        stringReceita.append("select pag.codigo, datapagamento as compensacao, produtospagos, valor,  'EA' as situacao, (select exists(select pmp.codigo from pagamentomovparcela pmp inner join movparcela par on par.codigo = pmp.movparcela \n");
        stringReceita.append("where movpagamento = pag.codigo and (par.contrato <> ").append(contrato).append(" or par.contrato is null))) as  pagaoutracoisa , fp.tipoformapagamento as tipopagamento from movpagamento pag \n");
        stringReceita.append("inner join formapagamento fp on fp.codigo = pag.formapagamento where pag.codigo in   \n");
        stringReceita.append(" (select movpagamento from pagamentomovparcela pmp inner join movparcela par on par.codigo = pmp.movparcela where contrato = ").append(contrato).append(") and valor > 0.0 and tipoformapagamento not in ('CH', 'CA', 'CC');\n");
        ResultSet rsPagamentos = SuperFacadeJDBC.criarConsulta( stringReceita.toString(), con);
        while (rsPagamentos.next()){
            String chave = Uteis.getAnoData(rsPagamentos.getDate("compensacao")) + "-"+Uteis.getMesData(rsPagamentos.getDate("compensacao"));
            Double valor = 0.0;
            if(rsPagamentos.getString("tipopagamento").equals("CH") && rsPagamentos.getString("situacao").equals("DV")){
                 processarChequeDevolvido(rsPagamentos.getInt("codigo"), rsPagamentos.getString("produtospagos"), contrato, mapaReceitaContrato, con, tipoformaPagamento);
            } else {
                if(UteisValidacao.emptyString(tipoformaPagamento) || tipoformaPagamento.equals(rsPagamentos.getString("tipopagamento"))) {
                    if (rsPagamentos.getBoolean("pagaoutracoisa")) {
                        valor = processarProdutosPagos(rsPagamentos.getString("produtospagos"), contrato);
                    } else {
                        valor = Uteis.arredondarForcando2CasasDecimais(rsPagamentos.getDouble("valor"));
                    }
                    if (mapaReceitaContrato.containsKey(chave)) {
                        mapaReceitaContrato.put(chave, Uteis.arredondarForcando2CasasDecimais(valor) + mapaReceitaContrato.get(chave));
                    } else {
                        mapaReceitaContrato.put(chave, Uteis.arredondarForcando2CasasDecimais(Uteis.arredondarForcando2CasasDecimais(valor)));
                    }
                }
            }
        }

        preencherReceitalinhaPlanilha(hssfWorkbook, row,  mapaColunas, mapaReceitaContrato);

    }

    private static void preencherReceitalinhaPlanilha(HSSFWorkbook hssfWorkbook, Row row, Map<String, Integer> mapaColunas, Map<String, Double> mapaReceitaContrato) {
        for (Map.Entry<String, Double> entry : mapaReceitaContrato.entrySet()) {
            criarCelula(hssfWorkbook, mapaColunas.get(entry.getKey()), row, entry.getValue());
        }

    }

    private static void processarChequeDevolvido(int codigoCheque, String produtosPagos, int contrato, Map<String, Double> mapaReceitaContrato, Connection con, String tipoformaPagamento) throws Exception {
        StringBuilder stringReceita = new StringBuilder("select pag.codigo, datacompesancao as compensacao,  valor, situacao, 'CA' as tipopagamento from cartaocredito pag  where movpagamento in (select distinct movpagamento from movproduto pro inner join movprodutoparcela mpp on mpp.movproduto = pro.codigo inner join pagamentomovparcela pmp on pmp.movparcela = mpp.movparcela  where chequedevolucao  = ").append(codigoCheque).append(")\n");
        stringReceita.append("    and situacao = 'EA'\n");
        stringReceita.append("                union all\n");
        stringReceita.append("select pag.codigo, datacompesancao as compensacao,  valor,  situacao, 'CH' as tipopagamento  from cheque pag  where movpagamento in (select distinct movpagamento from movproduto pro inner join movprodutoparcela mpp on mpp.movproduto = pro.codigo inner join pagamentomovparcela pmp on pmp.movparcela = mpp.movparcela  where chequedevolucao  = ").append(codigoCheque).append(")\n");
        stringReceita.append(" and situacao <> 'CA'\n");
        stringReceita.append("                   union all \n");
        stringReceita.append("  select pag.codigo, datapagamento as compensacao,valor, 'EA' as situacao, tipoformapagamento as tipopagamento from movpagamento pag \n");
        stringReceita.append("            inner join formapagamento fp on fp.codigo = pag.formapagamento \n");
        stringReceita.append("             where pag.codigo in \n");
        stringReceita.append("                (select distinct movpagamento from movproduto pro inner join movprodutoparcela mpp on mpp.movproduto = pro.codigo inner join pagamentomovparcela pmp on pmp.movparcela = mpp.movparcela  where chequedevolucao  = ").append(codigoCheque).append(") and valor > 0.0 and tipoformapagamento not in ('CH', 'CA', 'CC') order by 2;\n");
        ResultSet rsPagamentos = SuperFacadeJDBC.criarConsulta( stringReceita.toString(), con);
        List<MovProdutoVO> listaOrdemProdutos = new ArrayList<MovProdutoVO>();
        Double valorPagoContrato = 0.0;
        if (!produtosPagos.trim().equals("")) {
            String[] infoProdutosPagos = produtosPagos.split("\\|");
            for (String produto : infoProdutosPagos) {
                if (!produto.trim().equals("")) {
                    MovProdutoVO novo = new MovProdutoVO();
                    String[] infoProduto = produto.split(",");
                    novo.getContrato().setCodigo(Integer.parseInt(infoProduto[2]));
                    novo.setValorParcialmentePago(Double.parseDouble(infoProduto[3]));
                    if (novo.getContrato().getCodigo().equals(contrato)) {
                        valorPagoContrato = Uteis.arredondarForcando2CasasDecimais(valorPagoContrato + novo.getValorParcialmentePago());
                    }
                    listaOrdemProdutos.add(novo);
                }
            }
        }
        while (rsPagamentos.next()) {
            String chave = Uteis.getAnoData(rsPagamentos.getDate("compensacao")) + "-" + Uteis.getMesData(rsPagamentos.getDate("compensacao"));
            Double valorPagamento = Uteis.arredondarForcando2CasasDecimais(rsPagamentos.getDouble("valor"));
            Double valor = 0.0;
            for (MovProdutoVO produtoVo : listaOrdemProdutos) {
                if (produtoVo.getValorParcialmentePago() == 0.0) {
                    continue;
                }

                if (valorPagamento >= Uteis.arredondarForcando2CasasDecimais(produtoVo.getValorParcialmentePago())) {
                    if (produtoVo.getContrato().getCodigo().equals(contrato)) {
                        valor = Uteis.arredondarForcando2CasasDecimais(valor + produtoVo.getValorParcialmentePago());
                    }
                    valorPagamento = Uteis.arredondarForcando2CasasDecimais(valorPagamento - produtoVo.getValorParcialmentePago());
                    produtoVo.setValorParcialmentePago(0.0);
                } else {
                    if (produtoVo.getContrato().getCodigo().equals(contrato)) {
                        valor = Uteis.arredondarForcando2CasasDecimais(valor + valorPagamento);
                    }
                    produtoVo.setValorParcialmentePago(Uteis.arredondarForcando2CasasDecimais(produtoVo.getValorParcialmentePago() - valorPagamento));
                    valorPagamento = 0.0;
                }
                if (valorPagamento == 0.0) {
                    break;
                }
            }
            if (valor > 0.0  && (UteisValidacao.emptyString(tipoformaPagamento) || tipoformaPagamento.equals(rsPagamentos.getString("tipopagamento")))) {
                if (mapaReceitaContrato.containsKey(chave)) {
                    mapaReceitaContrato.put(chave, Uteis.arredondarForcando2CasasDecimais(valor) + mapaReceitaContrato.get(chave));
                } else {
                    mapaReceitaContrato.put(chave, Uteis.arredondarForcando2CasasDecimais(valor));
                }
            }
        }
    }

    private static Double processarProdutosPagos(String produtosPagos, Integer contrato) {
        Double valorPagoContrato = 0.0;
        if (!produtosPagos.trim().equals("")) {
            String[] infoProdutosPagos = produtosPagos.split("\\|");
            for (String produto : infoProdutosPagos) {
                if (!produto.trim().equals("")) {
                    String[] infoProduto = produto.split(",");
                    Integer codigoContrato = Integer.parseInt(infoProduto[2]);
                    double valorPago = Double.parseDouble(infoProduto[3]);
                    if (codigoContrato.equals(contrato)) {
                        valorPagoContrato = Uteis.arredondarForcando2CasasDecimais(valorPagoContrato + valorPago);
                    }
                }
            }
        }
        return valorPagoContrato;
    }

    private static Object getSituacao_Descricao(String situacao) {
        if(situacao.equals("AT")){
            return "Ativo";
        } else if(situacao.equals("IN")){
            return "Inativo";
        } else if(situacao.equals("TR")){
            return "Trancado";
        } else if(situacao.equals("CA")) {
            return "Cancelado";
        }
        return situacao;
    }

    private static Object getTipoContrato(String situacaocontrato) {
        if(situacaocontrato.equals("MA")){
            return "Matricula";
        } else if (situacaocontrato.equals("RE")) {
            return "Rematricula";
        } else if (situacaocontrato.equals("RN")) {
            return "Renovação";
        }
        return situacaocontrato;
    }

    private static void criarCelula(HSSFWorkbook hssfWorkbook, int numeroCelula, Row row, Object valor) {
        Cell cell = row.createCell(numeroCelula);
        setValorCelula(cell, valor);
    }

    private static HSSFWorkbook criarTabela(Date dataInicio, Date dataFim, Map<String, Integer> mapaColunas) {
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        HSSFSheet sheet = hssfWorkbook.createSheet("ReceitaGeralContratos");

        int cellnum = 0;
        Row row;

        // Definindo alguns padroes de layout
        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeight((short) 300);

        row = sheet.createRow(0);
        String[] colunas = new String[]{
                "Matrícula", "Nome Cliente", "CPF", "Contrato", "Condição de Pgto","Tipo Contrato", "Situação (Ativo/ Inativo/ Trancado)", "Data do Cancelamento/Desistencia", "Meses",
                "Dt Inicio Plano", "Dt Termino Original", "Dt Termino Ajustada","Valor Contrato", "Valor Base Calculo", "Valor Produtos"};
        for (String coluna : colunas) {
            cabecalho(hssfWorkbook, row, cellnum++, coluna);
        }

        Integer anoInicio = Uteis.getAnoData(dataInicio);
        Integer anoFim = Uteis.getAnoData(dataFim);
        Integer mesInicio = Uteis.getMesData(dataInicio);
        Integer mesFim = Uteis.getMesData(dataFim);

        for (int ano = anoInicio; ano <= anoFim; ano++) {
            Integer mesFimAtual = 12;
            if(ano == anoFim){
                mesFimAtual = mesFim;
            }
            for (int mes = mesInicio; mes <= mesFimAtual; mes++) {
                String chave = ano+"-"+mes;
                String coluna = getMesDescricao(mes) +"/"+ ano;
                mapaColunas.put(chave, cellnum);
                cabecalho(hssfWorkbook, row, cellnum++, coluna);
            }
            mesInicio = 1;
        }
        return hssfWorkbook;
    }

    private static String getMesDescricao(int mes) {
        if(mes == 1)
            return "jan";
        if(mes == 2)
            return "fev";
        if(mes == 3)
            return "mar";
        if(mes == 4)
            return "abr";
        if(mes == 5)
            return "mai";
        if(mes == 6)
            return "jun";
        if(mes == 7)
            return "jul";
        if(mes == 8)
            return "ago";
        if(mes == 9)
            return "set";
        if(mes == 10)
            return "out";
        if(mes == 11)
            return "nov";
        if(mes == 12)
            return "dec";
        return "";
    }

    private static Cell cabecalho(HSSFWorkbook hssfWorkbook, Row row, int cellnum, String textoCelula) {
        CellStyle cellStyle = hssfWorkbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Cell cell = row.createCell(cellnum++);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(textoCelula);
        return cell;
    }

    private static void setValorCelula(Cell cell, Object valor) {
        if (valor instanceof String) {
            cell.setCellType(CellType.STRING);
            cell.setCellValue((String) valor);
        } else if (valor instanceof Integer) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Integer) valor);
        } else if (valor instanceof Double) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Double) valor);
        } else if (valor instanceof Date) {
            cell.setCellValue((Date) valor);
        } else if (valor instanceof Boolean) {
            cell.setCellType(CellType.BOOLEAN);
            cell.setCellValue((Boolean) valor);
        } else {
            cell.setCellValue(valor == null ? "" : valor.toString());
        }
    }


}

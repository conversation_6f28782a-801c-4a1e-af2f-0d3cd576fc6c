package br.com.pactosolucoes.atualizadb.processo.geolocalizacao;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisServletExportData;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Endereco;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.HashMap;

public class GoogleApiGeocodeService {

    private Endereco enderecoDao;
    private String apiKey = "AIzaSyA-zZBUQGmHLNRE6M1L6051uKLnS1ac8V0";

    public static void main(String[] args) throws Exception {
        Connection con = DriverManager.getConnection("*******************************************", "postgres", "pactodb");
        GoogleApiGeocodeService servico = new GoogleApiGeocodeService(con);
        servico.processarTodos();
    }

    public GoogleApiGeocodeService(Connection con) throws Exception {
        enderecoDao = new Endereco(con);
    }

    public void processarTodos() throws Exception {
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select codigo from endereco where ltdlng is null and cep is not null and char_length (cep) > 7", enderecoDao.getCon());
        while (resultSet.next()) {
            String geolocalizarUm = "";
            try {
                int codigo = resultSet.getInt("codigo");
                JSONObject jsonObject = enderecoDao.obterEndereco(codigo);
                geolocalizarUm = geolocalizarUm(codigo, jsonObject);

            }catch (Exception e){
                e.printStackTrace();
            }
            if(geolocalizarUm.equals("exceeded")){
                throw new Exception("estourou o limite diário.");
            }
        }
    }

    public String geolocalizarUm(Integer codigo, JSONObject endereco) throws Exception {
        String cidade = getString(endereco, "cidade");
        String estado = getString(endereco, "estado");
        if (getString(endereco, "endereco").isEmpty()
                || getString(endereco, "cidade").isEmpty()
                || getString(endereco, "cep").isEmpty()) {
            throw new Exception("todos os campos são obrigatorios");
        }
        String params = getString(endereco, "numero") + "+" +
                getString(endereco, "endereco") + "+" +
                getString(endereco, "bairro") + "+" +
                ",+" +
                getString(endereco, "cep")
                + "+" +
                estado + "+" + cidade;
        params = Uteis.retirarAcentuacao(params).replaceAll(" ", "+");
        System.out.println("Geolocalizando para " + params);
        String urlQuery = "https://maps.googleapis.com/maps/api/geocode/json" +
                "?address=" + params +
                "&key=" + apiKey;
        JSONObject jsonRetorno = get(urlQuery);
        JSONObject location = jsonRetorno.getJSONArray("results").getJSONObject(0).getJSONObject("geometry").getJSONObject("location");

        if(jsonRetorno.toString().contains("exceeded")){
            return "exceeded";
        }

        System.out.println("Resultado:" + location.get("lat") + "," + location.get("lng"));
        String latitude = "";
        String longitude = "";
        try {
            latitude = location.opt("lat") instanceof String ? location.optString("lat") : Double.valueOf(location.optString("lat")).toString();
            longitude = location.opt("lng") instanceof String ? location.optString("lng") : Double.valueOf(location.optString("lng")).toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        enderecoDao.atualizarGeolocalizacao(latitude, longitude, codigo);
        return "ok";
    }

    public JSONObject get(String url) throws Exception {
        String requestGET = ExecuteRequestHttpService.executeRequestGET(url, new HashMap<>());
        return new JSONObject(requestGET);
    }

    public static String getString(JSONObject json, String campo) {
        return json.optString(campo) == null ? "" : json.optString(campo);
    }

    public JSONArray arrayProcessar(Integer empresa) throws Exception {
        JSONArray array = new JSONArray();
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT e.codigo " +
                "\nFROM endereco e " +
                "\nINNER JOIN cliente c ON c.pessoa = e.pessoa " +
                "\nINNER JOIN pessoa p ON p.codigo = e.pessoa " +
                "\nWHERE (ltdlng IS NULL or ltdlng = '') " +
                "\nAND cep IS NOT NULL " +
                "\nAND char_length (cep) > 7 " +
                "\nAND (p.cidade IS NOT NULL AND p.cidade <> 0) " +
                "\nAND c.empresa = " + empresa +
                "\nORDER BY c.situacao " +
                "\nLIMIT 3000", enderecoDao.getCon());
        while (resultSet.next()) {
            int codigo = resultSet.getInt("codigo");
            array.put(codigo);
        }
        return array;
    }


    public Boolean processarComLimite(Integer empresa, Integer limite) throws Exception {
        boolean atualizouUm = false;
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT e.codigo " +
                "\nFROM endereco e " +
                "\nINNER JOIN cliente c ON c.pessoa = e.pessoa " +
                "\nINNER JOIN pessoa p ON p.codigo = e.pessoa " +
                "\nWHERE (ltdlng IS NULL or ltdlng = '') " +
                "\nAND cep IS NOT NULL " +
                "\nAND char_length (cep) > 7 " +
                "\nAND (p.cidade IS NOT NULL AND p.cidade <> 0) " +
                "\nAND c.empresa = " + empresa +
                "\nORDER BY c.situacao " +
                "\nLIMIT " + limite, enderecoDao.getCon());
        while (resultSet.next()) {
            try {
                int codigo = resultSet.getInt("codigo");
                JSONObject jsonObject = enderecoDao.obterEndereco(codigo);
                String geolocalizarUm = geolocalizarUm(codigo, jsonObject);
                if(geolocalizarUm.equals("exceeded")){
                    break;
                }
                atualizouUm = true;
            }catch (Exception e){
                Uteis.logar(e, GoogleApiGeocodeService.class);
            }

        }
        return atualizouUm;
    }

    public Boolean temEnderecoParaGeolocalizar(Integer empresa) throws Exception {
        return SuperFacadeJDBC.existe("SELECT e.codigo " +
                "\nFROM endereco e " +
                "\nINNER JOIN cliente c ON c.pessoa = e.pessoa " +
                "\nINNER JOIN pessoa p ON p.codigo = e.pessoa " +
                "\nWHERE (ltdlng IS NULL or ltdlng = '') " +
                "\nAND cep IS NOT NULL " +
                "\nAND char_length (cep) > 7 " +
                "\nAND (p.cidade IS NOT NULL AND p.cidade <> 0) " +
                "\nAND c.empresa = " + empresa +
                "\nORDER BY c.situacao", enderecoDao.getCon());

    }
}

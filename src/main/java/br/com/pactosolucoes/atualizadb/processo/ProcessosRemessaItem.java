package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.RemessaItem;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.dcc.base.RemessaService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

public class ProcessosRemessaItem {

    public static JSONObject ajustarMovPagamentoSemOperadoraCartao(Integer empresa, Integer formaPagamento,
                                                                   String codigosRecibo, Connection con) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("mp.codigo as movpagamento, \n");
        sql.append("mp.operadoracartao, \n");
        sql.append("mp.datalancamento, \n");
        sql.append("ri.codigo as remessaitem, \n");
        sql.append("re.codigo as remessa, \n");
        sql.append("re.conveniocobranca \n");
        sql.append("from movpagamento mp \n");
        sql.append("inner join remessaitem ri on ri.movpagamento = mp.codigo \n");
        sql.append("inner join remessa re on re.codigo = ri.remessa \n");
        sql.append("inner join formapagamento fp on fp.codigo = mp.formapagamento \n");
        sql.append("where mp.operadoracartao is null \n");
        sql.append("and fp.tipoformapagamento = 'CA' \n");
        sql.append("and ri.props ilike '%Bandeira=%' \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and mp.empresa = ").append(empresa).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(formaPagamento)) {
            sql.append("and mp.formaPagamento = ").append(formaPagamento).append(" \n");
        }
        if (!UteisValidacao.emptyString(codigosRecibo)) {
            sql.append("and mp.recibopagamento in (").append(codigosRecibo).append(") \n");
        }
        sql.append("order by mp.codigo \n");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);

        if (UteisValidacao.emptyNumber(total)) {
            throw new Exception("Nenhum MovPagamento encontrado");
        }

        Integer sucesso = 0;
        Integer erro = 0;
        RemessaItem remessaItemDAO;
        ConvenioCobranca convenioCobrancaDAO;
        RemessaService remessaService;
        JSONArray erros = new JSONArray();
        JSONObject json = new JSONObject();
        json.put("qtd_total", total);

        try {
            Uteis.logarDebug("INICIO | ProcessosRemessaItem | ajustarPagamentosSemOperadoraCartao | " + total + " Itens");

            remessaItemDAO = new RemessaItem(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            remessaService = new RemessaService(con);
            Map<Integer, ConvenioCobrancaVO> mapConve = new HashMap<>();

            Integer atual = 0;
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        Uteis.logarDebug("Atual " + ++atual + "/" + total + " | ProcessosRemessaItem | ajustarPagamentosSemOperadoraCartao");
                        Integer movPagamento = rs.getInt("movpagamento");
                        Integer remessaItem = rs.getInt("remessaItem");
                        Integer conveniocobranca = rs.getInt("conveniocobranca");
                        try {
                            RemessaItemVO remessaItemVO = remessaItemDAO.consultarPorChavePrimaria(remessaItem, Uteis.NIVELMONTARDADOS_GESTAOREMESSABOLETO);

                            if (!UteisValidacao.emptyNumber(conveniocobranca)) {
                                ConvenioCobrancaVO convenioVO = mapConve.get(conveniocobranca);
                                if (convenioVO == null) {
                                    convenioVO = convenioCobrancaDAO.consultarPorChavePrimaria(conveniocobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    mapConve.put(convenioVO.getCodigo(), convenioVO);
                                }
                                remessaItemVO.getRemessa().setConvenioCobranca(convenioVO);
                            }

                            OperadoraCartaoVO operadoraCartaoVO = remessaService.obterOperadora(remessaItemVO);
                            if (operadoraCartaoVO != null && !UteisValidacao.emptyNumber(operadoraCartaoVO.getCodigo())) {
                                SuperFacadeJDBC.executarUpdate("update movpagamento set operadoracartao = " + operadoraCartaoVO.getCodigo() + " where codigo = " + movPagamento, con);
                                ++sucesso;
                            } else {
                                throw new Exception("Operadora não identificada");
                            }
                        } catch (Exception ex) {
                            ++erro;
                            ex.printStackTrace();
                            Uteis.logarDebug("MovPagamento " + movPagamento + " | RemessaItem " + remessaItem + " | ERRO | " + ex.getMessage());
                            JSONObject jsonerro = new JSONObject();
                            jsonerro.put("movpagamento", movPagamento);
                            jsonerro.put("remessaitem", remessaItem);
                            jsonerro.put("erro", ex.getMessage());
                            erros.put(jsonerro);
                        }
                    }
                }
            }
        } finally {
            remessaItemDAO = null;
            convenioCobrancaDAO = null;
            remessaService = null;
            Uteis.logarDebug("FIM | ProcessosRemessaItem | ajustarPagamentosSemOperadoraCartao");
        }
        json.put("qtd_sucesso", sucesso);
        json.put("qtd_erro", erro);
        if (erros.length() > 0) {
            json.put("erros", erros);
        }
        return json;
    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;

            String chave = "teste";
            if (args.length > 0) {
                chave = args[0];
            }

            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }

            Uteis.logarDebug("Obter conexão para chave: " + chave);
//            Connection con = DriverManager.getConnection("************************************************", "zillyonweb", "pactodb");
            Connection con = new DAO().obterConexaoEspecifica(chave);
            JSONObject json = ProcessosRemessaItem.ajustarMovPagamentoSemOperadoraCartao(0, 0, null, con);
            System.out.println(json.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}


/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.*;

/**
 * <AUTHOR>
 */
public class CorrigirFotoKeyTreino {

    public static void main(String... args) throws Exception {
        Uteis.debug = true;
        String[] hosts = args.length == 0 ? new String[]{
                "********************************/"
        }: args;
        for (String host : hosts) {
            Uteis.logar("######################### " + host);
            try (Connection oamd = DriverManager.getConnection(host + "OAMD", "postgres", "pactodb")) {
                try {
                    try (ResultSet rsOAMD = SuperFacadeJDBC.criarConsulta(" SELECT * FROM empresa where ativa", oamd)) {
                        while (rsOAMD.next()) {
                            try {
                                String nomeBD = rsOAMD.getString("nomeBD");
                                try (Connection conBancoTR = DriverManager.getConnection(host + rsOAMD.getString("nomeBD").replace("bdzillyon", "bdmusc"), "zillyonweb", "pactodb")) {
                                    try (Connection conBancoZW = DriverManager.getConnection(host + rsOAMD.getString("nomeBD"), "zillyonweb", "pactodb")) {
                                        processarFotoKeysAlteradosRecentementeZW(nomeBD, conBancoZW, conBancoTR);
                                    }
                                }
                            } catch (Exception e) {
                                Uteis.logarPrintStackTrace(e);
                            }
                        }
                    }
                } catch (Exception e) {
                    Uteis.logarPrintStackTrace(e);
                }
            }

        }

    }

    public static void processarFotoKeysAlteradosRecentementeZW(String nomeBD, Connection conZW, Connection conTR) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("")
                .append("SELECT ")
                .append("\n")
                .append("    codigo as pessoa_zw,")
                .append("\n")
                .append("    fotokey as fotokey_zw")
                .append("\n")
                .append("FROM ")
                .append("\n")
                .append("    pessoa")
                .append("\n")
                .append("WHERE ")
                .append("\n (fotokey is not null and fotokey <> '') ")
                .append("ORDER BY codigo");
        PreparedStatement stm = conZW.prepareStatement(sql.toString());
        int rows = 1;
        try (ResultSet rs = stm.executeQuery()) {
            boolean first = true;
            while (rs.next()) {
                if (first) {
                    Uteis.logar("---------------------- " + nomeBD + " ---------------------- ");
                    first = false;
                }
                Uteis.logar(String.format("Banco: %s Pessoa: %s Fotokey: %s ",
                        nomeBD,
                        rs.getString("pessoa_zw"),
                        rs.getString("fotokey_zw")));

                String updateFotoKey = String.format("update\n" +
                                "\tpessoa p\n" +
                                "set\n" +
                                "\tfotokey = '%s'\n" +
                                "from\n" +
                                "\tclientesintetico cs\n" +
                                "where\n" +
                                "\tcs.pessoa_codigo = p.codigo\n" +
                                "\tand cs.codigopessoa = %s\n" +
                                "\t\tand (fotokey is null or fotokey = '')",
                        rs.getString("fotokey_zw"), rs.getInt("pessoa_zw"));

                try {
                    Uteis.logarDebug(updateFotoKey);
                    SuperFacadeJDBC.executarConsultaUpdate(updateFotoKey, conTR);
                    Uteis.logarDebug(String.format("Atualizado row %s de pessoa do ZW %s no TR com fotokey do ZW %s",
                            rows, rs.getInt("pessoa_zw"), rs.getString("fotokey_zw")));
                    rows++;
                } catch (Exception e) {
                    Uteis.logarPrintStackTrace(e);
                }
            }
        } finally {
            Uteis.logarDebug(String.format("Alterações concluídas do banco %s com %s rows de pessoa do ZW sem fotokey no TR", nomeBD, rows));
        }
    }
}

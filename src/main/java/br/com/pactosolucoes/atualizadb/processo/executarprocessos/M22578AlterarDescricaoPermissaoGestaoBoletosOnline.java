package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "18/10/2024",
        descricao = "Alterando descricao da permissão para acessar o Gestão de Boletos Online",
        motivacao = "M2-2578")
public class M22578AlterarDescricaoPermissaoGestaoBoletosOnline implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update permissao set tituloapresentacao = '4.48 - Permitir acesso ao Gestão de Boletos Online', " +
                    "nomeentidade = 'GestaoBoletosOnline' WHERE tituloapresentacao ILIKE '4.48%';", c);
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import static br.com.pactosolucoes.atualizadb.processo.AjustarAcessosRepetidos.apagarAcessosRepetidos;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class AjustarMovimentacaoMovPagamentoCC {
    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("**********************************************************************", "postgres", "pactodb");
            ajustarMovContas(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarMovContas(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select movpagamentoorigemcredito,movconta from movpagamento  where movconta  is not null and movpagamentoorigemcredito  is not null", con);

        while (consulta.next()) {
           
            String sql = "update movpagamento set movconta = ? where movpagamentoorigemcredito = ?;";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);

            sqlAlterar.setInt(1, consulta.getInt("movconta"));
            sqlAlterar.setInt(2, consulta.getInt("movpagamentoorigemcredito"));
            sqlAlterar.execute();
        }
    }
    
}


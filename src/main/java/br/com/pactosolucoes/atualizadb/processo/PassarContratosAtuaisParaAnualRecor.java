/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

/**
 *
 * <AUTHOR>
 */
public class PassarContratosAtuaisParaAnualRecor {
    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("******************************************************************", "postgres", "pactodb");
            passarParaAnual(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void passarParaAnual(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        String datavigencia = "2024-12-19";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato,cli.codigomatricula,c.situacaocontrato,c.pessoa, c.plano,pr.valormensal, p.produtopadraogerarparcelascontrato,  cd.numeromeses,c.renovavelautomaticamente,p.renovavelautomaticamente , " +
                " c.vigenciaateajustada, c.vigenciaate  from contrato c  inner join cliente cli on cli.pessoa = c.pessoa inner join plano p on p.codigo = c.plano inner join planorecorrencia pr on pr.plano = p.codigo  inner join contratoduracao cd on cd.contrato = c.codigo  " +
                "where vigenciaateajustada >= '"+datavigencia+"' and (c.contratoresponsavelrenovacaomatricula is null or c.contratoresponsavelrenovacaomatricula = 0) and c.situacao= 'AT' and p.recorrencia  = true and pr.renovavelautomaticamente and  cd.numeromeses = 1 and not exists (select codigo from contratooperacao co where co.contrato = c.codigo and co.tipooperacao = 'CA')   order by 1 ", con);
        MovProduto movProdutoDAO = new MovProduto(con);
        MovParcela movParcelaDAO = new MovParcela(con);
        MovProdutoParcela mppDAO = new MovProdutoParcela(con);
        MovProdutoModalidade mpmDAO = new MovProdutoModalidade(con);
        ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
        List<MovProdutoVO> produtoContrato = new ArrayList<MovProdutoVO>();
        List<MovProdutoModalidadeVO> movProdModalidade = new ArrayList<MovProdutoModalidadeVO>();
        MovParcelaVO parcelaContrato;
        MovProdutoParcelaVO movPPVO;
        int count = 0;
        while (consulta.next()) {
            con.setAutoCommit(false);
            try {
                int numeroMeses = 1;
                String situacaoContrato = consulta.getString("situacaocontrato");
                int contratoAvaliar = consulta.getInt("contrato");
                while (situacaoContrato.equals("RN")){
                    ResultSet consultaContratoAnterior = SuperFacadeJDBC.criarConsulta("select c.codigo, c.situacaocontrato, cd.numeromeses from contrato c inner join contratoduracao cd on c.codigo = cd.contrato where c.contratoresponsavelrenovacaomatricula = "+contratoAvaliar, con);
                    if(consultaContratoAnterior.next()){
                        situacaoContrato = consultaContratoAnterior.getString("situacaocontrato");
                        numeroMeses += consultaContratoAnterior.getInt("numeromeses");
                        contratoAvaliar = consultaContratoAnterior.getInt("codigo");
                    } else {
                        throw new Exception("Contrato anterior não encontrado do contrato"+consulta.getInt("contrato"));
                    }
                }
                if(numeroMeses > 11){
                    System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " não foi alterado pois tem "+numeroMeses+" meses de vigência");
                    con.commit();
                    continue;
                }
                Double valorMensal = consulta.getDouble("valormensal");
                produtoContrato = movProdutoDAO.consultar("select * from movproduto where contrato = "+consulta.getInt("contrato")+" and produto = "+consulta.getInt("produtopadraogerarparcelascontrato")+" order by codigo desc limit 1 ", Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                parcelaContrato = movParcelaDAO.consultarPorMovProduto(produtoContrato.get(0).getCodigo().intValue(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                movProdModalidade = mpmDAO.consultarPorMovProduto(produtoContrato.get(0).getCodigo(),  Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                int indice = 1;
                int mesesAdicionar = 12 - numeroMeses;
                Date novaVigencia = Uteis.somarCampoData(consulta.getDate("vigenciaate"), Calendar.MONTH,  mesesAdicionar);            
                Date novaVigenciaAjustada = novaVigencia;
                if(!Calendario.igual(consulta.getDate("vigenciaate"),consulta.getDate("vigenciaateajustada")) ){
                    int diferenca = (int) (Uteis.nrDiasEntreDatas(consulta.getDate("vigenciaate"), consulta.getDate("vigenciaateajustada")) );
                    novaVigenciaAjustada = Uteis.somarDias(novaVigencia, diferenca);
                }
                Date competencia = produtoContrato.get(0).getDataInicioVigencia();
                if(Calendario.menor(Uteis.obterDataFuturaParcela(competencia, 1), Calendario.hoje())){
                    competencia =  Uteis.somarMeses(Calendario.hoje(), -1);
                }
                Date vencimentoBase = parcelaContrato.getDataVencimento();
                while (Calendario.menor(Uteis.obterDataFuturaParcela(vencimentoBase, 1), Calendario.hoje())){
                    vencimentoBase = Uteis.obterDataFuturaParcela(vencimentoBase, 1);
                }
               int mesAtual = 1;
                while(indice <= mesesAdicionar ){
                    competencia = Uteis.obterDataFuturaParcela(competencia, 1);
                    Date vencimento =  Uteis.obterDataFuturaParcela(vencimentoBase, mesAtual++);
                    parcelaContrato.setDataVencimento(vencimento);
                    parcelaContrato.setValorParcela(valorMensal);
                    parcelaContrato.setDescricao("PARCELA "+(indice+ 1));
                    parcelaContrato.setSituacao("EA");
                    movParcelaDAO.incluirSemCommit(parcelaContrato);
                    produtoContrato.get(0).setAnoReferencia(Uteis.getAnoData(competencia));
                    produtoContrato.get(0).setMesReferencia(Uteis.getMesReferenciaData(competencia));
                    produtoContrato.get(0).setDescricao(produtoContrato.get(0).getDescricao().substring(0, (produtoContrato.get(0).getDescricao().length() - 7))+ Uteis.getMesReferenciaData(competencia)) ;
                    produtoContrato.get(0).setPrecoUnitario(valorMensal);
                    produtoContrato.get(0).setTotalFinal(valorMensal);
                    produtoContrato.get(0).setValorDesconto(0.0);
                    produtoContrato.get(0).setSituacao("EA");
                    produtoContrato.get(0).setMovProdutoModalidades(movProdModalidade);
                    movProdutoDAO.incluirSemCommit(produtoContrato.get(0));
                    movPPVO = new MovProdutoParcelaVO();
                    movPPVO.setMovParcela(parcelaContrato.getCodigo());
                    movPPVO.setMovProdutoVO(produtoContrato.get(0));
                    movPPVO.setMovProduto(produtoContrato.get(0).getCodigo());
                    movPPVO.setValorPago(valorMensal);
                    mppDAO.incluir(movPPVO);
                    indice++;
                }
                SuperFacadeJDBC.executarConsultaUpdate("update movproduto set datafinalvigencia = '"+Uteis.getDataFormatoBD(novaVigencia)+"' where contrato = "+consulta.getInt("contrato")+" and datafinalvigencia is not null;" , con);
                SuperFacadeJDBC.executarConsultaUpdate("update movprodutomodalidade  set datafim  = '"+Uteis.getDataFormatoBD(novaVigencia)+"' where contrato = "+ consulta.getInt("contrato") , con);
                SuperFacadeJDBC.executarConsultaUpdate("update contrato c set valorbasecalculo  = (select sum(totalfinal) from movproduto  where contrato = c.codigo  and  produto = "+consulta.getInt("produtopadraogerarparcelascontrato")+") where codigo =  "+ consulta.getInt("contrato") , con);
                SuperFacadeJDBC.executarConsultaUpdate("update contrato  set observacao = observacao||' | mensal >> Anual M1-3727', valorfinal  = (valorbasecalculo+ somaproduto), dataprevistarenovar  = '"+Uteis.getDataFormatoBD(novaVigenciaAjustada)+"', vigenciaateajustada  = '"+Uteis.getDataFormatoBD(novaVigenciaAjustada)+"', vigenciaate = '"+Uteis.getDataFormatoBD(novaVigencia)+"' where codigo = "+ consulta.getInt("contrato") , con);
                SuperFacadeJDBC.executarConsultaUpdate("update periodoacessocliente set datafinalacesso  = '"+Uteis.getDataFormatoBD(novaVigenciaAjustada)+"' where codigo in(select codigo from periodoacessocliente  where   contrato  = "+ consulta.getInt("contrato")+" order by datafinalacesso desc limit 1)" , con);
                SuperFacadeJDBC.executarConsultaUpdate("delete from historicocontrato  where contrato = "+ consulta.getInt("contrato")+" and tipohistorico = 'AV';" , con);
                SuperFacadeJDBC.executarConsultaUpdate("update historicocontrato   set datafinalsituacao = '"+Uteis.getDataFormatoBD(novaVigenciaAjustada)+"' where codigo in(select codigo from historicocontrato  where    contrato  = "+ consulta.getInt("contrato")+" order by datafinalsituacao desc limit 1);" , con);
                SuperFacadeJDBC.executarConsultaUpdate("update contratoduracao c set numeromeses  = (select count(codigo) from movproduto  where contrato = c.contrato  and  produto ="+consulta.getInt("produtopadraogerarparcelascontrato")+") where contrato = "+ consulta.getInt("contrato"),con);
                System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " teve sua vigencia  alterada de   " + Uteis.getData(consulta.getDate("vigenciaateajustada")) +" para "+ Uteis.getData(novaVigenciaAjustada));
                con.commit();
                ClienteVO cliente = zillyonWebFacade.getCliente().consultarPorCodigoPessoa(consulta.getInt("pessoa"), Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
                zillyonWebFacade.atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
            } catch (Exception e){
                con.rollback();
                System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " teve erro na alteração:  " + e.getMessage());
            } finally{
                con.setAutoCommit(true);
            }
        }
    }
}

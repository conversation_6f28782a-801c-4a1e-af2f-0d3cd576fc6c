/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class CorrigirParcelasMesmoMes {

    public static void main(String[] args) throws Exception {

        Connection conOamd2 = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");
        Map<String, String> bancos = bancos();
        for (String bd : bancos.keySet()) {
            File dir = new File("\\logs\\");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            System.out.println(bd);
            dir = new File("\\logs\\" + bd + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd_MM_yyyy_HH_mm")+".txt");
            FileWriter arq = new FileWriter("\\logs\\" + bd + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd_MM_yyyy_HH_mm")+ ".txt");
            PrintWriter gravarArq = new PrintWriter(arq);
            Connection con = DriverManager.getConnection(bancos.get(bd), "zillyonweb", bd.contains("selfit") ? "pactodb2020" : "pactodb");
            corrigir(gravarArq, con);
            gravarArq.printf("]");
            arq.close();
        }
    }

    public static void corrigir(PrintWriter gravarArq, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        Map<Integer, Integer> contratoParcelaAlterar = new HashMap<Integer, Integer>();

        sql.append(" select r.codigo as remessa, ir.codigo as remessaitem, mp1.dataregistro, mp1.datacobranca, mp1.codigo, mp1.contrato, mp1.situacao, mp1.descricao from movparcela mp1");
        sql.append(" inner join movparcela mp2 on to_char(mp2.datavencimento, 'MM/YYYY') = to_char(mp1.datavencimento, 'MM/YYYY') and mp1.contrato = mp2.contrato and mp1.codigo <> mp2.codigo");
        sql.append(" and mp2.descricao ilike 'PARCELA%'");
        sql.append(" and mp2.situacao IN ('EA', 'PG')");
        sql.append(" and mp1.descricao ilike 'PARCELA%'");
        sql.append(" and mp1.descricao <> 'PARCELA RENEGOCIADA'");
        sql.append(" and mp2.descricao <> 'PARCELA RENEGOCIADA'");
        sql.append(" and mp1.situacao IN ('EA', 'PG')");
        sql.append(" and mp2.situacao IN ('EA', 'PG')");
        sql.append(" left join remessaitem ir on ir.movparcela = mp1.codigo");
        sql.append(" left join remessa r on ir.remessa = r.codigo and r.situacaoremessa <> 2");
        sql.append("  inner join contrato c on c.codigo = mp1.contrato ");
        sql.append("  inner join contratorecorrencia cr on cr.contrato = c.codigo ");
        sql.append(" where mp1.dataregistro::date >= '01/06/2017'");
        sql.append(" and mp1.situacao = 'EA' ");
        sql.append(" and mp1.descricao in ('PARCELA 1', 'PARCELA 2') ");
        sql.append(" and r.codigo is null ");
        sql.append(" order by mp1.contrato, mp1.datacobranca, mp1.descricao");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        int i = 1;
        while (rs.next()) {
            contratoParcelaAlterar.put(rs.getInt("contrato"), rs.getInt("codigo"));
        }
        List<Integer> contratos = new ArrayList<Integer>(contratoParcelaAlterar.keySet());
        Collections.sort(contratos);
        for (Integer contrato : contratos) {
            incrementarDataParcela(gravarArq, contratoParcelaAlterar.get(contrato), contrato, con);
            ResultSet rsParcelas = SuperFacadeJDBC.criarConsulta("select codigo from movparcela where codigo > "
                    + contratoParcelaAlterar.get(contrato)
                    + " and situacao = 'EA' and contrato = " + contrato, con);
            while (rsParcelas.next()) {
                incrementarDataParcela(gravarArq, rsParcelas.getInt("codigo"), contrato, con);
            }
           System.out.println("¬¬¬¬¬¬¬¬"+i++);
        }


    }

    private static void incrementarDataParcela(PrintWriter gravarArq, Integer codigo, Integer contrato, Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select datacobranca, datavencimento from movparcela where codigo = " + codigo, con);
        if (rs.next()) {
            Date cobranca = rs.getDate("datacobranca");
            Date vencimento = rs.getDate("datavencimento");
            Date incrementada = Uteis.somarCampoData(vencimento, Calendar.MONTH, 1);
            System.out.println(codigo);
            gravarArq.printf("\nParcela " + codigo + " do contrato " + contrato
                    + "\n Data de vencimento atual: " + Uteis.getData(vencimento)
                    + "\n Nova data de vencimento: " + Uteis.getData(incrementada)
                    + "\n -------------------------------------------");

            PreparedStatement stm = con.prepareStatement("UPDATE movparcela SET datavencimento = ?, datacobranca = ? where codigo = ?");
            stm.setDate(1, Uteis.getDataJDBC(incrementada));
            stm.setDate(2, Uteis.getDataJDBC(incrementada));
            stm.setInt(3, codigo);
            stm.execute();
        }
    }

    private static Map<String, String> bancos() {
        Map<String, String> chaves = new HashMap<String, String>();
        chaves.put("bdzillyonselfitbarra","*********************************************************************************************");
        chaves.put("bdzillyonselfitepitaciopessoa","******************************************************************************************************");
        chaves.put("bdzillyonselfitiguatemi","************************************************************************************************");
        chaves.put("bdzillyonselfitmangabeira","**************************************************************************************************");
        chaves.put("bdzillyonselfitparalela","************************************************************************************************");
        chaves.put("bdzillyonselfitpaulovi","***********************************************************************************************");
        chaves.put("bdzillyonselfitsaoraphael","**************************************************************************************************");
        chaves.put("bdzillyonwellnessclubvitoria","*************************************************************");
        chaves.put("bdzillyonmetabolismo","*****************************************************");
        chaves.put("bdzillyonorsigaspar","****************************************************");
        chaves.put("bw","****************************************************");
        chaves.put("MEGAFITNESS","*****************************************************");
        chaves.put("MEGAFITNESSunidade2","*****************************************************unid2");
        chaves.put("theplace","**************************************************");
        chaves.put("planethealthlifegym","*************************************************************");
        chaves.put("stronger","**************************************************");
        chaves.put("batalhafit","****************************************************");
        return chaves;
    }
}

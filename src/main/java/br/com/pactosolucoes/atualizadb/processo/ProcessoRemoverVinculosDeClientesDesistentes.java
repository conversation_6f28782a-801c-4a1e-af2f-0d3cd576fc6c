package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.HistoricoVinculoVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.HistoricoVinculo;
import negocio.facade.jdbc.basico.Vinculo;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.HistoricoContrato;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.integracao.treino.dto.SinteticoMsDTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoRemoverVinculosDeClientesDesistentes {

    public static void main(String... args) {
        try {
            String chave =args.length > 0 ? args[0] : "corporesjrpsp";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            removerVinculosDeClientesDesistentes(c);
        } catch (Exception ex) {
            Logger.getLogger(RefazerVinculoMovProdutoParcelaContratos.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void removerVinculosDeClientesDesistentes(Connection con) {
        try {
            Empresa empresaDao = new Empresa(con);
            Contrato contratoDao = new Contrato(con);
            HistoricoVinculo historicoVinculoDao = new HistoricoVinculo(con);
            Usuario usuarioDao = new Usuario(con);
            Vinculo vinculoDao = new Vinculo(con);
            ZillyonWebFacade zillyonWebFacadeDao = new ZillyonWebFacade(con);

            UsuarioVO usuarioVOAdmin = usuarioDao.consultarPorCodigo(1, Uteis.NIVELMONTARDADOS_MINIMOS);
            List<EmpresaVO> listaEmpresas = empresaDao.consultarTodas(true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Uteis.logarDebug("INÍCIO | ProcessoRemoverVinculosDeClientesDesistentes");
            for (EmpresaVO empresa : listaEmpresas) {
                if (empresa.isRemoverVinculosAposDesistencia()) {
                    Date data = Uteis.obterDataAnterior(Calendario.hoje(), empresa.getNrDiasDesistenteRemoverVinculoTreino());
                    StringBuilder sqlStringBuilder = new StringBuilder();
                    sqlStringBuilder.append("SELECT con.pessoa as pessoa, con.vigenciaAteAjustada as vigenciaAteAjustada, cli.codigo as codigoCliente, *\n");
                    sqlStringBuilder.append("FROM historicocontrato histc\n");
                    sqlStringBuilder.append("INNER JOIN contrato con ON histc.contrato = con.codigo\n");
                    sqlStringBuilder.append("INNER JOIN cliente cli ON con.pessoa = cli.pessoa\n");
                    sqlStringBuilder.append("WHERE 1 = 1\n");
                    sqlStringBuilder.append("AND tipohistorico IN ('DE')\n");
                    sqlStringBuilder.append("AND datainiciosituacao <= '").append(Uteis.getDataJDBC(data)).append("'\n");
                    sqlStringBuilder.append("AND con.empresa = ").append(empresa.getCodigo()).append("\n");
                    sqlStringBuilder.append("AND EXISTS (SELECT vnc.codigo FROM Vinculo vnc\n");
                    sqlStringBuilder.append("\tINNER JOIN cliente cli ON vnc.cliente = cli.codigo\n");
                    sqlStringBuilder.append("\tWHERE cli.pessoa = con.pessoa\n");
                    sqlStringBuilder.append("\tAND vnc.tipoVinculo <> 'CO' ORDER BY vnc.codigo);\n");

                    try (java.sql.Statement stm = con.createStatement()) {
                        try (java.sql.ResultSet rs = stm.executeQuery(sqlStringBuilder.toString())) {
                            while (rs.next()) {
                                Date ultimoVencimento = contratoDao.consultarUltimoVencimento(rs.getInt("pessoa"));
                                if (Calendario.maiorOuIgual(rs.getDate("vigenciaAteAjustada"), ultimoVencimento)) {

                                    String statusCliente = rs.getString("situacao");

                                    if (!"AT".equalsIgnoreCase(statusCliente)) {
                                        continue;
                                    }

                                    List<VinculoVO> vinculos = vinculoDao.consultarVinculosDiferentesDeConsultorPorCliente(rs.getInt("codigoCliente"), Uteis.NIVELMONTARDADOS_MINIMOS);

                                    List<VinculoVO> vinculosValidos = new ArrayList<>();
                                    for (VinculoVO vinculo : vinculos) {
                                        StringBuilder sql = new StringBuilder("SELECT codigo FROM Vinculo WHERE codigo = " + vinculo.getCodigo());
                                        ResultSet resultSet = stm.executeQuery(sql.toString());
                                        if (resultSet.next()) {
                                            vinculosValidos.add(vinculo);
                                        }
                                    }

                                    StringBuilder sqlDeleteVinculos = new StringBuilder("DELETE FROM Vinculo WHERE codigo in (");
                                    StringBuilder codigos = new StringBuilder();
                                    for (VinculoVO vinculoVO : vinculosValidos) {
                                        codigos.append(",").append(vinculoVO.getCodigo());
                                    }
                                    codigos.deleteCharAt(0);
                                    sqlDeleteVinculos.append(codigos.toString()).append(");");
                                    try (PreparedStatement sqlExcluir = con.prepareStatement(sqlDeleteVinculos.toString())) {
                                        sqlExcluir.execute();
                                    }

                                    vinculoDao.removerVinculos(vinculos, "PROCESSAMENTO DIÁRIO", usuarioVOAdmin, true, Calendario.hoje());

                                    List<Integer> clientesAtualizar = new ArrayList<Integer>();
                                    for (VinculoVO vinculoVO : vinculosValidos) {
                                        HistoricoVinculoVO hist = new HistoricoVinculoVO(vinculoVO.getCliente().getCodigo(),
                                                vinculoVO.getColaborador().getCodigo(),
                                                "SD",
                                                vinculoVO.getTipoVinculo(),
                                                 Calendario.hoje(),
                                                "PROCESSAMENTO DIÁRIO",
                                                usuarioVOAdmin);
                                        historicoVinculoDao.incluirSemCommit(hist, false);
                                        if (!clientesAtualizar.contains(vinculoVO.getCliente().getCodigo())) {
                                            clientesAtualizar.add(vinculoVO.getCliente().getCodigo());
                                        }
                                    }
                                    if (!clientesAtualizar.isEmpty()) {
                                        ClienteVO cliente = new ClienteVO();
                                        for(Integer codcliente: clientesAtualizar){
                                            cliente.setCodigo(codcliente);
                                            cliente.setDadosSinteticoPreparados(false);
                                            zillyonWebFacadeDao.atualizarSintetico(cliente,
                                                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_VINCULO, true, null);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            empresaDao = null;
            contratoDao = null;
            historicoVinculoDao = null;
            usuarioDao = null;
            vinculoDao = null;
            zillyonWebFacadeDao = null;
            Uteis.logarDebug("FIM | ProcessoRemoverVinculosDeClientesDesistentes");
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoRemoverVinculosDeClientesDesistentes - " + ex.getMessage());
        }
    }

}

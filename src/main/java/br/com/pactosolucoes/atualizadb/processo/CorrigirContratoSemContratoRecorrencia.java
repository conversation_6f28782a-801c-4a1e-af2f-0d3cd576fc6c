package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoDuracao;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.plano.Plano;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by glauco on 06/03/14
 */
public class CorrigirContratoSemContratoRecorrencia extends SuperEntidade {

    private CorrigirContratoSemContratoRecorrencia(Connection conexao) throws Exception {
        super(conexao);
    }


    public static Connection obterConexao(String nomeBD) throws Exception {
        String hostBD = "zw-rds-9.cmgq5kfs7ytf.sa-east-1.rds.amazonaws.com";
        String porta = "5432";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        String userBD = "zillyonweb";
        String passwordBD = "pactodb2020";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }

    private static List<Connection> retornarListaConexoes() {
        List<Connection> lista = new ArrayList<Connection>();
        try {
            lista.add(obterConexao("bdzillyonselfitboaviagem"));
            lista.add(obterConexao("bdzillyonselfitgraca"));
            lista.add(obterConexao("bdzillyonselfitbarra"));
            lista.add(obterConexao("bdzillyonselfitmagshopping"));
            lista.add(obterConexao("bdzillyonselfitparalela"));
            lista.add(obterConexao("bdzillyonselfitpituba2"));
            lista.add(obterConexao("bdzillyonselfitpontaverde"));
            lista.add(obterConexao("bdzillyonselfitcaruaru"));
            lista.add(obterConexao("bdzillyonselfitriverside"));
            lista.add(obterConexao("bdzillyonselfitpaulovi"));
            lista.add(obterConexao("bdzillyonselfitmangabeira"));
            lista.add(obterConexao("bdzillyonselfitcabula"));
            lista.add(obterConexao("bdzillyonselfitiguatemi"));
            lista.add(obterConexao("bdzillyonselfitepitaciopessoa"));
            lista.add(obterConexao("bdzillyonselfitsaoraphael"));
            lista.add(obterConexao("bdzillyonusinadocorpo"));
        } catch (Exception e) {
            System.out.println("ERRO AO CRIAR CONEXÃO COM O BD. ERRO: " + e.getMessage());
        }
        return lista;
    }

    public static void main(String[] args) {
        try {
            List<Connection> listaCon = new ArrayList<Connection>();

            Connection con1;
            if (args.length == 0) {
                listaCon = retornarListaConexoes();
            } else {
                con1 = new DAO().obterConexaoEspecifica(args[0]);
                listaCon.add(con1);
            }

            for (Connection conZW : listaCon) {
                new CorrigirContratoSemContratoRecorrencia(conZW).executarProcesso();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void executarProcesso() throws SQLException {
        try {
            System.out.println("Iniciou em : " + Calendario.hoje());
            ContratoRecorrencia contratoRecorrenciaDAO = new ContratoRecorrencia(con);
            Contrato contratoDAO = new Contrato(con);
            Plano planoDAO = new Plano(con);
            ContratoDuracao contratoDuracaoDAO = new ContratoDuracao(con);
            String encontrarContratos = "SELECT\n" +
                    "  c.codigo AS contrato,\n" +
                    "  c.pessoa AS pessoa,\n" +
                    "  c.plano AS plano,\n" +
                    "  c.datalancamento,\n" +
                    "  p.descricao,\n" +
                    "  (SELECT count(codigo)\n" +
                    "   FROM movparcela\n" +
                    "   WHERE movparcela.contrato = c.codigo) as qtdParcelas,\n" +
                    "  emp.nome as empresa\n" +
                    "FROM contrato c\n" +
                    "  LEFT JOIN contratorecorrencia cr ON cr.contrato = c.codigo\n" +
                    "  LEFT JOIN plano p ON c.plano = p.codigo\n" +
                    "  LEFT JOIN planorecorrencia pr ON pr.plano = p.codigo\n" +
                    "  LEFT JOIN empresa emp ON c.empresa = emp.codigo\n" +
                    "WHERE (cr.codigo IS NULL AND c.regimerecorrencia IS TRUE)\n" +
                    "ORDER BY 1";


            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(encontrarContratos);
            String empresa = "";
            while (rs.next()) {
                String emp = rs.getString("empresa");
                if (!empresa.equals(emp)) {
                    empresa = emp;
                    System.out.println(empresa);
                }
                int contrato = rs.getInt("contrato");
                int plano = rs.getInt("plano");
                int qtdParcelas = rs.getInt("qtdParcelas");
                if (qtdParcelas == 0) {
                    System.out.println("Contrato " + contrato + " não tem parcelas...");
                    continue;
                }

                ContratoVO contratoVO = contratoDAO.consultarPorCodigo(contrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Double valorMensal = Uteis.arredondarForcando2CasasDecimais(contratoVO.getValorBaseCalculo() / contratoVO.getContratoDuracao().getNumeroMeses());

                contratoVO.setContratoDuracao(contratoDuracaoDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                PlanoVO planoVO = planoDAO.consultarPorChavePrimaria(contratoVO.getPlano().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);

                ContratoRecorrenciaVO contratoRecorrenciaVO = new ContratoRecorrenciaVO();
                contratoRecorrenciaVO.setContrato(contratoVO);
                contratoRecorrenciaVO.setDiaVencimentoAnuidade(planoVO.getPlanoRecorrencia().getDiaAnuidade());
                contratoRecorrenciaVO.setMesVencimentoAnuidade(planoVO.getPlanoRecorrencia().getMesAnuidade());
                contratoRecorrenciaVO.setDiaVencimentoCartao(Uteis.getDiaMesData(contratoVO.getVigenciaDe()));
                contratoRecorrenciaVO.setDiasCancelamentoAutomatico(planoVO.getPlanoRecorrencia().getQtdDiasAposVencimentoCancelamentoAutomatico());
                contratoRecorrenciaVO.setFidelidade(contratoVO.getContratoDuracao().getNumeroMeses());
                contratoRecorrenciaVO.setPessoa(contratoVO.getPessoa());
                contratoRecorrenciaVO.setRenovavelAutomaticamente(planoVO.getPlanoRecorrencia().getRenovavelAutomaticamente());
                contratoRecorrenciaVO.setValorMensal(valorMensal);
                contratoRecorrenciaVO.setValorAnuidade(0.0);
                contratoRecorrenciaVO.setAnuidadeNaParcela(false);
                contratoRecorrenciaVO.setCancelamentoProporcional(planoVO.getPlanoRecorrencia().isCancelamentoProporcional());
                contratoRecorrenciaDAO.incluir(contratoRecorrenciaVO);
            }
            System.out.println("Terminou em : " + Calendario.hoje());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private int obterDiaVencimentoCartao(int contrato, Connection con) throws SQLException {
        String obterDataContrato = "SELECT\n" +
                "  extract(DAY FROM datavencimento) AS diavencimento,\n" +
                "  count(*)\n" +
                "FROM movparcela\n" +
                "WHERE contrato = " + contrato + "\n" +
                "GROUP BY 1\n" +
                "ORDER BY 2 DESC\n" +
                "LIMIT 1";

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(obterDataContrato);
        if (rs.next()) {
            return rs.getInt("diavencimento");
        }
        return 0;
    }

    private ContratoRecorrenciaVO obterPossivelContratoRecorrencia(int contrato, int plano, Connection con) throws Exception {
        ContratoRecorrenciaVO contratoRecorrenciaVO = new ContratoRecorrenciaVO();
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT DISTINCT fidelidade, valormensal, diasbloqueioacesso, diascancelamentoautomatico, renovavelautomaticamente, valoranuidade, diavencimentoanuidade, mesvencimentoanuidade\n");
        sb.append("FROM contratorecorrencia\n");
        sb.append("WHERE contrato IN (\n");
        sb.append("  (SELECT codigo FROM contrato WHERE contrato.codigo < ").append(contrato).append(" AND plano = ").append(plano).append(" ORDER BY codigo DESC LIMIT 1)\n");
        sb.append("   UNION ALL\n");
        sb.append("  (SELECT codigo FROM contrato WHERE contrato.codigo > ").append(contrato).append(" AND plano = ").append(plano).append(" ORDER BY codigo ASC LIMIT 1)\n");
        sb.append(")");


        String count = "select count(*) from (" + sb.toString() + ") as count;";
        int qtdConRecor = SuperFacadeJDBC.contar(count, con);
        if (qtdConRecor == 1) {
            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sb.toString());
            if (rs.next()) {
                contratoRecorrenciaVO.setFidelidade(rs.getInt("fidelidade"));
                contratoRecorrenciaVO.setValorMensal(rs.getDouble("valormensal"));
                contratoRecorrenciaVO.setDiasBloqueioAcesso(rs.getInt("diasbloqueioacesso"));
                contratoRecorrenciaVO.setDiasCancelamentoAutomatico(rs.getInt("diascancelamentoautomatico"));
                contratoRecorrenciaVO.setRenovavelAutomaticamente(rs.getBoolean("renovavelautomaticamente"));
                contratoRecorrenciaVO.setValorAnuidade(rs.getDouble("valoranuidade"));
                contratoRecorrenciaVO.setDiaVencimentoAnuidade(rs.getInt("diavencimentoanuidade"));
                contratoRecorrenciaVO.setMesVencimentoAnuidade(rs.getInt("mesvencimentoanuidade"));
            }
        } else {
            System.out.println("Contrato " + contrato + " não é possível calcular Contrato Recorrência...");
        }
        return contratoRecorrenciaVO;
    }
}

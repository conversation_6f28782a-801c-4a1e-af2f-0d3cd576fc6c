package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "25/07/2024",
        descricao = "Cria índices",
        motivacao = "M2-2081")
public class AtualizacaoTicketM22081 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_transacao_dataprocessamento_situacao ON transacao (dataprocessamento, situacao);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_transacao_codigo ON transacao (codigo);", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "06/03/2025",
        descricao = "Add novas configs de convênios a serem utilizados nos links de agamentos de origem da régua de cobrança",
        motivacao = "PAY-178")
public class PAY178NovasConfigsConvenioCobrancaRegua implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN conveniocobrancacartaoregua INT DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN conveniocobrancapixregua INT DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN conveniocobrancaboletoregua INT DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN tipoparcelascobrarvendasiteregua INT DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN enviarEmailPagamentoRegua boolean DEFAULT false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN gerarAutCobrancaComCobAutBloqueadaRegua boolean DEFAULT false;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("update empresa set conveniocobrancacartaoregua = conveniocobrancacartao, conveniocobrancapixregua = conveniocobrancapix," +
                    " conveniocobrancaboletoregua = conveniocobrancaboleto, tipoparcelascobrarvendasiteregua = tipoparcelascobrarvendasite, enviarEmailPagamentoRegua = enviarEmailPagamento, " +
                    "gerarAutCobrancaComCobAutBloqueadaRegua = gerarAutCobrancaComCobAutBloqueada", c);
        }
    }
}

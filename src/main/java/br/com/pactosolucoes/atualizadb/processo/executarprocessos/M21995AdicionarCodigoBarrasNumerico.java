package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "22/08/2024",
        descricao = "Adicionar a coluna na tabela Boleto para armazenar o código de barras numérico.",
        motivacao = "M2-1995")
public class M21995AdicionarCodigoBarrasNumerico implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE boleto ADD COLUMN codigobarrasnumerico VARCHAR(80) NULL;", c);
        }
    }

}

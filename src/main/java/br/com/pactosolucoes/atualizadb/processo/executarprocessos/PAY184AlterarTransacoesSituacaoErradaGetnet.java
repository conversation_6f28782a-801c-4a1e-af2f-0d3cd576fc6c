package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "22/01/2025",
        descricao = "Alterar situacao na tabela transacoes.",
        motivacao = "PAY-184")
public class PAY184AlterarTransacoesSituacaoErradaGetnet implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE transacao SET codigoretorno = 481 WHERE codigoretorno ILIKE '?'" +
                    "AND codigoretornodescricao ILIKE 'Transacao negada.getnet!//deny%Transação negada por regra de segurança do sistema de Antifraude'" +
                    "AND dataprocessamento::date > '2024-10-01'" +
                    "AND conveniocobranca IN (" +
                    "      SELECT c.codigo" +
                    "      FROM conveniocobranca c" +
                    "      WHERE c.tipoconvenio = 20" +
                    "  );"
                    , c);
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import static br.com.pactosolucoes.atualizadb.processo.CorrigirDataCompensacaoMovPagamento.corrigirDatasCartaoCredito;
import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.AulaDesmarcada;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Reposicao;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
/**
 *
 * <AUTHOR>
 */
public class AjustarMatriculasContratoCredito {
    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("***********************************************", "postgres", "pactodb");
            validarExcessoDeAulasDesmarcadas(con1);

        } catch (Exception ex) {
            Logger.getLogger(CorrigirDataCompensacaoMovPagamento.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void corrigirDatasMatriculasContratosCredito(Connection con) throws Exception {
        SuperFacadeJDBC.executarConsultaUpdate("delete from controlecreditotreino  where auladesmarcada in (select a.codigo from auladesmarcada a inner join contrato c on c.codigo = a.contrato  \n" +
            "inner join contratoduracao cd on cd.contrato = c.codigo inner join contratoduracaocreditotreino cdt on cdt.contratoduracao = cd.codigo\n" +
            "left join matriculaalunohorarioturma m on m.contrato = a.contrato and a.horarioturma = m.horarioturma where cdt.tipohorario = 3 and a.reposicao is null and permitereporauladesmarcada  and a.dataorigem::date > now()::date and m.datafim < a.dataorigem::date or  m.codigo is null);", con);   
        SuperFacadeJDBC.executarConsultaUpdate("delete from auladesmarcada  where codigo in (select a.codigo from auladesmarcada a inner join contrato c on c.codigo = a.contrato  \n" +
            "inner join contratoduracao cd on cd.contrato = c.codigo inner join contratoduracaocreditotreino cdt on cdt.contratoduracao = cd.codigo\n" +
            "left join matriculaalunohorarioturma m on m.contrato = a.contrato and a.horarioturma = m.horarioturma where cdt.tipohorario = 3 and a.reposicao is null and permitereporauladesmarcada  and a.dataorigem::date > now()::date and m.datafim < a.dataorigem::date or  m.codigo is null);", con);   
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato,cdt.quantidadecreditocompra, quantidadecreditodisponivel,c.vigenciade,c.vigenciaateajustada from contrato c inner join contratoduracao cd on c.codigo = cd.contrato inner join contratoduracaocreditotreino cdt on cd.codigo = cdt.contratoduracao where vendacreditotreino and cdt.tipohorario = 3 and c.situacao = 'AT' ", con);
        Contrato conDao = new Contrato(con);
        AulaDesmarcada aulaDesmarcadaDAO = new AulaDesmarcada(con);
        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
        while (consulta.next()) {
            ContratoVO contrato = conDao.consultarPorCodigo(consulta.getInt("contrato"), Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL);
            Integer saldo = ControleCreditoTreino.consultarSaldoCredito(con, contrato.getCodigo());
            Integer aulasDesmarcasPassadasARepor = aulaDesmarcadaDAO.contarAulasDesmarcadasPorPeriodo(contrato.getCodigo(), 0,null,Calendario.hoje()); //aulas que já passaram e não foram repostas e não consumiram crédito
            contrato.gerarVigenciaMatriculaPlanoCreditoTreino(Calendario.maior(Calendario.hoje(), contrato.getVigenciaDe()) ? Calendario.hoje() : contrato.getVigenciaDe() ,(saldo - aulasDesmarcasPassadasARepor));
            if(Calendario.maior(contrato.getVigenciaTurmaCreditoTreinoAte(), contrato.getVigenciaAteAjustada())){
                contrato.setVigenciaTurmaCreditoTreinoAte(contrato.getVigenciaAteAjustada());
            }
            SuperFacadeJDBC.executarConsultaUpdate("update matriculaalunohorarioturma set datafim  = '" + Uteis.getDataJDBC(contrato.getVigenciaTurmaCreditoTreinoAte()) + "' where contrato = "+contrato.getCodigo()+ " and datafim > '"+ Uteis.getDataJDBC(Calendario.hoje())+"';", con);   
            aulaDesmarcadaDAO.excluirAulasDesmarcadasFuturasSemReposicaoProcesso(contrato, null, contrato.getVigenciaTurmaCreditoTreinoAte(),usuarioVO);
            System.out.println("Contrato " + consulta.getInt("contrato") + " foi ajustado");
        }
    }
    
    public static void excluirFaltasErradas(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select cc.contrato,c.pessoa,replace(replace(array_agg(cc.codigo)::text, '{',''),'}','') as codigos, count(cc.codigo) from controlecreditotreino cc \n" +
            "inner join contrato  c on c.codigo = cc.contrato and c.situacao = 'AT' \n" +
            "inner join  matriculaalunohorarioturma mht on cc.horarioturmafalta = mht.horarioturma and mht.contrato = cc.contrato \n" +
            "where  tipooperacaocreditotreino = 3 and dataoperacao > datafim and not exists (select * from matriculaalunohorarioturma "
                + " where contrato = c.codigo and horarioturma = mht.horarioturma and datainicio > mht.datafim) group by 1,2 having count(cc.codigo) > 0  ", con);
        SituacaoClienteSinteticoDW sinteticoDW = new SituacaoClienteSinteticoDW(con);
        while (consulta.next()) {
            SuperFacadeJDBC.executarConsultaUpdate("delete from  controlecreditotreino where codigo in (" + consulta.getString("codigos")+ ");", con);   
            sinteticoDW.atualizarInformacoesCreditoTreino(consulta.getInt("pessoa"), Calendario.hoje());
        }
    }
    public static void corrigirExcessoDeAulasDesmarcadas(Connection con) throws Exception {
        ajustarAulasDesmarcadasAlunos(con, true);
    }
    
    public static void validarExcessoDeAulasDesmarcadas(Connection con) throws Exception {
        ajustarAulasDesmarcadasAlunos(con, false);
    }
    
    public static void ajustarAulasDesmarcadasAlunos(Connection con, boolean resolver) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        TurmasServiceImpl turmaservice = new TurmasServiceImpl(con);
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select c.codigo as contrato, dw.matricula from contrato c inner join situacaoclientesinteticodw dw on c.codigo = dw.codigocontrato inner join contratoduracao cd on c.codigo = cd.contrato inner join contratoduracaocreditotreino cdt on cd.codigo = cdt.contratoduracao where vendacreditotreino and cdt.tipohorario = 3 and c.situacao = 'AT' ", con);
        Map<Integer, Integer> contratosExcesso = new HashMap<Integer, Integer>();
        Map<Integer, Integer>  contratosNormais = new HashMap<Integer, Integer>();
       Map<Integer, Integer>  contratosFaltaAulas = new HashMap<Integer, Integer>();
        Integer count = 0;
        while (consulta.next()) {
            count++;
            Integer diferencaCreditosAulas = turmaservice.nrAulasExtras(consulta.getInt("matricula"), consulta.getInt("contrato"));
            if(diferencaCreditosAulas > 0){
                contratosFaltaAulas.put(consulta.getInt("contrato"), diferencaCreditosAulas);
            }else if(diferencaCreditosAulas < 0){
                contratosExcesso.put(consulta.getInt("contrato"), diferencaCreditosAulas * -1);
            } else {
                 contratosNormais.put(consulta.getInt("contrato"), 0);
            }
        }
        if(resolver){
            ControleCreditoTreino creditoDAO = new ControleCreditoTreino(con);
            Usuario usuarioDao = new Usuario(con);
            Cliente clienteDao = new Cliente(con);
            UsuarioVO usuario = usuarioDao.getUsuarioRecorrencia();
            Set<Map.Entry<Integer, Integer>> setExcesso = contratosExcesso.entrySet();
            for (Map.Entry<Integer, Integer> ent : setExcesso) {
                try{
                    con.setAutoCommit(false);
                    Uteis.logar(null, "*contrato "+ent.getKey()+" tem "+ent.getValue()+" aulas a mais. Corrigindo...");
                    Integer quantidadeRetirar = ent.getValue();
                    int indexReposicao = 0;
                    List<ReposicaoVO> reposicoesSemDesmarcacao = turmaservice.getReposicaoDao().consultarAulaMarcadaComCreditoExtra(ent.getKey(), Uteis.getDate("01/01/2011"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    List<AulaDesmarcadaVO> aulasdermarcadasSemReposicao = turmaservice.getAulaDesmarcadaDao().consultarListaAulasDesmarcadas(ent.getKey(), 0, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    Ordenacao.ordenarLista(reposicoesSemDesmarcacao, "dataLancamento");
                    Ordenacao.ordenarLista(aulasdermarcadasSemReposicao, "dataLancamento");
                    for(AulaDesmarcadaVO desmarcacao: aulasdermarcadasSemReposicao){
                           if(quantidadeRetirar == 0 || reposicoesSemDesmarcacao.size() <= indexReposicao){
                               break;
                           }
                           ReposicaoVO reposicao = reposicoesSemDesmarcacao.get(indexReposicao);
                           SuperFacadeJDBC.executarConsultaUpdate("update reposicao set marcacaoaula='f', dataorigem ='"+Uteis.getDataFormatoBD(desmarcacao.getDataOrigem())+"' , horarioturmaorigem = "+desmarcacao.getHorarioTurmaVO().getCodigo() +" where codigo ="+reposicao.getCodigo(), con);   
                           SuperFacadeJDBC.executarConsultaUpdate("update auladesmarcada set datareposicao ='"+Uteis.getDataFormatoBD(reposicao.getDataReposicao())+"' , reposicao = "+reposicao.getCodigo() +" where codigo ="+desmarcacao.getCodigo(), con);   
                           SuperFacadeJDBC.executarConsultaUpdate("update controlecreditotreino set tipooperacaocreditotreino ='"+ TipoOperacaoCreditoTreinoEnum.REPOSICAO.getCodigo()+"' where reposicao = "+reposicao.getCodigo() +" and tipooperacaocreditotreino ="+ TipoOperacaoCreditoTreinoEnum.MARCOU_AULA.getCodigo(), con);   
                           indexReposicao++;
                           quantidadeRetirar--;
                    }
                    if(quantidadeRetirar > 0){
                        ClienteVO clienteVO = clienteDao.consultarPorCodigoContrato(ent.getKey(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        Integer saldo = ControleCreditoTreino.consultarSaldoCredito(con, ent.getKey());
                        creditoDAO.liberarVagaNoHorarioTurma(ent.getKey(), clienteVO.getCodigo(), quantidadeRetirar, saldo, usuario);
                    }
                    con.commit();
                } catch (Exception e) {
                    con.rollback();
                    Uteis.logar(null, "*****contrato "+ent.getKey()+" tem teve problemas para corrigir :"+ e.getMessage());
                } finally{
                    con.setAutoCommit(true);
                }
            }
        } else {
            Uteis.logar(null, "Qte contratos avaliados: "+count );
            Uteis.logar(null, "Qte contratos corretos : "+contratosNormais.size() );
            Uteis.logar(null, "################################################################");
            Uteis.logar(null, "Qte contratos com falta de aulas : "+contratosFaltaAulas.size() );
            Set<Map.Entry<Integer, Integer>> setFalta = contratosFaltaAulas.entrySet();
            for (Map.Entry<Integer, Integer> ent : setFalta) {
                 Uteis.logar(null, "*contrato "+ent.getKey()+" tem "+ent.getValue()+" aulas a menos");
            }
            Uteis.logar(null, "################################################################");
            Uteis.logar(null, "Qte contratos com falta de aulas : "+contratosExcesso.size() );
            Set<Map.Entry<Integer, Integer>> setExcesso = contratosExcesso.entrySet();
            for (Map.Entry<Integer, Integer> ent : setExcesso) {
                 Uteis.logar(null, "*contrato "+ent.getKey()+" tem "+ent.getValue()+" aulas a mais");
            }
        }
    }
}
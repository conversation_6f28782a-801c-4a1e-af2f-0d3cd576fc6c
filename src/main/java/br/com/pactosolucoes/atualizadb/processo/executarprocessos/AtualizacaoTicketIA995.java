package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;



@ClasseProcesso(autor = "Luis Antônio de Melo Gomes",
        data = "05/05/2025",
        descricao = "Implementar campanhas para configuracao crm",
        motivacao = "IA-995")
public class AtualizacaoTicketIA995 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE public.campanha (" +
                            " codigo SERIAL PRIMARY KEY, " +
                            " configuracaoia INT NOT NULL, " +
                            " empresa INT NOT NULL, "+
                            " idCampanha VARCHAR(36) NOT NULL," +
                            " titulo TEXT NULL, " +
                            " tag TEXT NULL, " +
                            " linkExterno TEXT NULL, " +
                            " descricao TEXT NULL, " +
                            " imagemPath TEXT NULL, " +
                            " periodoInicial timestamp NOT NULL, " +
                            " periodoFinal timestamp NOT NULL, " +
                            " data_criacao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),"+
                            " CONSTRAINT fk_campanha_config "+
                            " FOREIGN KEY (configuracaoia) "+
                            " REFERENCES public.configuracaocrmia(codigo) "+
                            " ON DELETE CASCADE ); " +
                        " CREATE INDEX idx_campanha_configuracao "+
                        " ON public.campanha(configuracaoia); "
                    , c);
        }
    }
}


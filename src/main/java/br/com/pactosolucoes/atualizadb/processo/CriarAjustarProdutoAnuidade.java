package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: GlaucoT
 * Date: 06/12/13
 * Time: 08:47
 * To change this template use File | Settings | File Templates.
 */
//TODO O PROCESSO DEVERÁ SER AJUSTADO, CASO SEJA RODADO EM OUTRA ACADEMIA
public class CriarAjustarProdutoAnuidade extends SuperEntidade {

    private ProdutoVO produtoAnuidade = null;

    public CriarAjustarProdutoAnuidade() throws Exception {
        super();
    }

    public CriarAjustarProdutoAnuidade(Connection connection) throws Exception {
        super(connection);
    }

    public static void main(String[] args) {
        try {
            Connection con1 = null;
            if (args.length == 0) {
                con1 = DriverManager.getConnection("****************************************************************", "postgres", "pactodb");
            } else {
                con1 = new DAO().obterConexaoEspecifica(args[0]);
            }
            Conexao.guardarConexaoForJ2SE(con1);
            new CriarAjustarProdutoAnuidade(con1).executarProcesso();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void executarProcesso() throws SQLException {
        try {
            System.out.println("Iniciou em : " + Calendario.hoje());
            con.setAutoCommit(false);
            processar(con);
            con.commit();
            System.out.println("Terminou em : " + Calendario.hoje());
        } catch (Exception e) {
            if (con != null) {
                con.rollback();
            }
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
            }
        }
    }

    private void processar(Connection con) throws Exception {
        List<ContratoAnuidadeTO> listaContratos = obterListaContratosEmRecorrencia(con);

        UsuarioVO usuarioVO = getFacade().getUsuario().consultarPorNomeUsuario("admin", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        int i = 0;
        for (ContratoAnuidadeTO contratoAnuidadeTO : listaContratos) {
            if (contratoAnuidadeTO.getValorAnuidade() == null || contratoAnuidadeTO.getValorAnuidade() == 0) {
                ++i;
                ContratoVO contratoVO = getFacade().getContrato().consultarPorChavePrimaria(contratoAnuidadeTO.getCodContrato(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Date dataVencimento = new SimpleDateFormat("dd/MM/yyyy").parse("15/03/2016");
                gerarParcela(contratoVO, usuarioVO, 99.90, contratoAnuidadeTO.getCodEmpresa(), Calendario.hoje(), dataVencimento);
                System.out.println(i + " - Contrato " + contratoAnuidadeTO.getCodContrato() + " gerado!");
            }
        }
    }

    private List<ContratoAnuidadeTO> obterListaContratosEmRecorrencia(Connection con) throws Exception {
        List<ContratoAnuidadeTO> listaContratos = new ArrayList<ContratoAnuidadeTO>();

        String sql = "select c.codigo as codContrato,\n" +
                "c.empresa as codEmpresa,\n" +
                "null as valorAnuidade from contrato c\n" +
                "where plano in (4) AND c.situacao = 'AT'\n" +
                "and c.codigo not in (\n" +
                "select c.codigo from contrato c\n" +
                "left join movparcela mpar ON mpar.contrato = c.codigo\n" +
                "where plano in (4) \n" +
                "AND c.situacao = 'AT'\n" +
                "AND mpar.descricao like 'ANUI%'\n" +
                ")order by 1;";

        ResultSet resultSet = criarConsulta(sql, con);

        while (resultSet.next()) {
            ContratoAnuidadeTO contratoAnuidadeTO = new ContratoAnuidadeTO();
            contratoAnuidadeTO.setCodContrato(resultSet.getInt("codContrato"));
            contratoAnuidadeTO.setCodEmpresa(resultSet.getInt("codEmpresa"));
            if (resultSet.getString("valorAnuidade") != null) {
                contratoAnuidadeTO.setValorAnuidade(resultSet.getDouble("valorAnuidade"));
            }
            listaContratos.add(contratoAnuidadeTO);
        }

        return listaContratos;
    }

    public void gerarParcela(ContratoVO contratoVO, UsuarioVO usuarioVO, Double valorAnuidade, Integer codEmpresa, Date dataRegistro, Date dataVencimento) throws Exception {
        MovParcelaVO parcela = new MovParcelaVO();
        parcela.setContrato(contratoVO);
        parcela.setDataRegistro(dataRegistro);
        parcela.setDataVencimento(dataVencimento);
        parcela.setDescricao("ANUIDADE PLANO RECORRENTE");
        parcela.setResponsavel(usuarioVO);
        if (valorAnuidade == 0) {
            parcela.setSituacao("PG");
        } else {
            parcela.setSituacao("EA");
        }
        parcela.setValorBaseCalculo(valorAnuidade);
        parcela.setValorParcela(valorAnuidade);
        parcela.setEmpresa(contratoVO.getEmpresa());
        parcela.setPessoa(contratoVO.getPessoa());
        gerarMovProduto(parcela, contratoVO, usuarioVO, valorAnuidade, dataRegistro, codEmpresa);
        getFacade().getZWFacade().incluirMovParcelaSemCommit(parcela);
    }

    public void gerarMovProduto(MovParcelaVO parcelaVO, ContratoVO contratoVO, UsuarioVO usuarioVO,
                                Double valorAnuidade, Date dataRegistro, Integer codEmpresa) throws Exception {
        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(parcelaVO.getDataVencimento()));
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(parcelaVO.getDataVencimento()));
        movProdutoVO.setDataLancamento(dataRegistro);
        movProdutoVO.setContrato(contratoVO);
        movProdutoVO.setProduto(getProdutoAnuidade());
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao("ANUIDADE PLANO RECORRENTE");
        movProdutoVO.setQuantidade(1);
        movProdutoVO.setResponsavelLancamento(usuarioVO);
        movProdutoVO.setPrecoUnitario(valorAnuidade);
        movProdutoVO.setTotalFinal(movProdutoVO.getPrecoUnitario());
        movProdutoVO.getEmpresa().setCodigo(codEmpresa);
        movProdutoVO.setPessoa(contratoVO.getPessoa());
        if (valorAnuidade == 0) {
            movProdutoVO.setQuitado(true);
            movProdutoVO.setSituacao("PG");
        } else {
            movProdutoVO.setQuitado(false);
            movProdutoVO.setSituacao("EA");
        }
        new MovProduto().incluirSemCommit(movProdutoVO);
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProdutoVO(movProdutoVO);
        movProdutoParcela.setMovProduto(movProdutoVO.getCodigo());
        movProdutoParcela.setValorPago(movProdutoVO.getTotalFinal());
        parcelaVO.getMovProdutoParcelaVOs().add(movProdutoParcela);
    }

    private ProdutoVO getProdutoAnuidade() throws Exception {
        if (produtoAnuidade == null) {
            produtoAnuidade = getFacade().getProduto().consultarPorTipoProduto("TA", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        return produtoAnuidade;
    }
}

class ContratoAnuidadeTO extends SuperTO {

    private Integer codContrato = 0;
    private Integer codEmpresa = 0;
    private Double valorAnuidade;

    Integer getCodContrato() {
        return codContrato;
    }

    void setCodContrato(Integer codContrato) {
        this.codContrato = codContrato;
    }

    Integer getCodEmpresa() {
        return codEmpresa;
    }

    void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    Double getValorAnuidade() {
        return valorAnuidade;
    }

    void setValorAnuidade(Double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }
}

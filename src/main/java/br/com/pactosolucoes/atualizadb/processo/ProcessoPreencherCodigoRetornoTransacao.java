package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.Transacao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 25/04/2020
 */
public class ProcessoPreencherCodigoRetornoTransacao {

    private Connection con;

    public ProcessoPreencherCodigoRetornoTransacao(Connection con) {
        this.con = con;
    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "INICIO | ProcessoPreencherCodigoRetornoTransacao...");

            String chave = "teste";
            if (args.length > 0) {
                chave = args[0];
            }

            Connection con = null;
            try {
                Uteis.logar(null, "Obter conexão para chave: " + chave);
                con = new DAO().obterConexaoEspecifica(chave);
                ProcessoPreencherCodigoRetornoTransacao processo = new ProcessoPreencherCodigoRetornoTransacao(con);
                processo.processar();
                processo = null;
            } catch (Exception ex) {
                Uteis.logar(null, "Erro chave: " + chave);
                ex.printStackTrace();
            } finally {
                if (con != null) {
                    con.close();
                }
            }

            Uteis.logar(true, null, "FIM | ProcessoPreencherCodigoRetornoTransacao...");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void processar() {
        try {
            Uteis.logar(true, null, "INICIO ...");

            List<Date> mesesAjustar = Uteis.getMesesEntreDatas(Uteis.somarMeses(Calendario.hoje(), -12), Calendario.hoje());

            for (Date mes : mesesAjustar) {
                Transacao transacaoDAO;
                try {
                    transacaoDAO = new Transacao(con);

                    Uteis.logar(true, null, "Processar... Mês " + Uteis.getMesReferenciaData(mes));

                    StringBuilder sql = new StringBuilder();
                    sql.append("select  \n");
                    sql.append("t.codigo  \n");
                    sql.append("from transacao t \n");
                    sql.append("where (coalesce(t.paramsresposta, '') <> '' or coalesce(t.outrasinformacoes, '') <> '')  \n");
                    sql.append("and (coalesce(t.codigoretorno, '') = '' or (t.situacao <> 4 and (coalesce(t.codigoretorno, '') = '0' or coalesce(t.codigoretorno, '') = '?'))) \n");
                    sql.append("and t.dataprocessamento::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mes))).append("' \n");
                    sql.append("and t.dataprocessamento::date <= '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mes))).append("' \n");
                    sql.append("order by t.codigo \n");

                    Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);

                    if (total <= 0) {
                        continue;
                    }

                    int atual = 0;

                    ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                    while (rs.next()) {
                        Integer codigo = 0;
                        try {
                            codigo = rs.getInt("codigo");

                            Uteis.logar(true, null, "Transacao... " + ++atual + "/" + total + " - Cod " + codigo);

                            String sqlTransacao = "select tipo, paramsresposta, outrasinformacoes, codigoretorno, situacao from transacao where codigo = " + codigo;
                            ResultSet rsTransacao = SuperFacadeJDBC.criarConsulta(sqlTransacao, con);
                            if (rsTransacao.next()) {

                                TipoTransacaoEnum tipoTransacaoEnum = TipoTransacaoEnum.getTipoTransacaoEnum(rsTransacao.getInt("tipo"));
                                String paramsresposta = rsTransacao.getString("paramsresposta");
                                String outrasinformacoes = rsTransacao.getString("outrasinformacoes");
                                String codigoretorno = rsTransacao.getString("codigoretorno");

                                TransacaoVO transacaoVO = transacaoDAO.obterObjetoTransacaoPorTipo(tipoTransacaoEnum);
                                transacaoVO.setCodigo(codigo);
                                transacaoVO.setParamsResposta(paramsresposta);
                                transacaoVO.setOutrasInformacoes(outrasinformacoes);
                                transacaoVO.setCodigoRetorno(codigoretorno);
                                transacaoVO.setSituacao(SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rsTransacao.getInt("situacao")));

                                String codigoRetornoNovo = transacaoVO.getCodigoRetornoGestaoTransacao();
                                String motivoGestaoTransacao = transacaoVO.getCodigoRetornoGestaoTransacaoMotivo();
                                if ((UteisValidacao.emptyString(codigoRetornoNovo) || codigoRetornoNovo.equals("?")) && !UteisValidacao.emptyString(motivoGestaoTransacao)) {
                                    if (motivoGestaoTransacao.toLowerCase().contains("vencido segundo a validade informada")) {
                                        codigoRetornoNovo = CodigoRetornoPactoEnum.CARTAO_VENCIDO.getCodigo();
                                    }
                                }

                                if (UteisValidacao.emptyString(codigoRetornoNovo)) {
                                    throw new Exception("Sem código de erro.");
                                }

                                Uteis.logar(true, null, "Transacao " + codigo + " - Cod Retorno anterior " + codigoretorno + " | Atual " + codigoRetornoNovo);
                                transacaoDAO.atualizarCodigoRetorno(codigoRetornoNovo, motivoGestaoTransacao, transacaoVO.getCodigo());

                            }
                        } catch (Exception ex) {
                            Uteis.logar(true, null, "Transacao: " + codigo + " | " + ex.getMessage());
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    transacaoDAO = null;
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            Uteis.logar(true, null, "FIM...");
        }
    }
}

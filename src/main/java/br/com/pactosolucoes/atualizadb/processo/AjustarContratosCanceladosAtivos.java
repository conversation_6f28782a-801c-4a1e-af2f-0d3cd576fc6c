package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.*;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ContratoDependente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.HistoricoContrato;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.financeiro.MovParcela;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class AjustarContratosCanceladosAtivos {
    public static void main(String[] args) throws Exception {
        Connection c = DriverManager.getConnection("*********************************************************************", "postgres", "pactodb");

        ajustarContratosCanceladosAtivos(c);
    }
    @SuppressWarnings("unchecked")
    public static void ajustarContratosCanceladosAtivos(Connection con) throws Exception{
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT c.codigo AS contrato, conop.codigo AS operacao, h.codigo AS historico, pa.codigo as periodo, pes.codigo AS pessoa, cli.codigo AS cliente ");
        sql.append("FROM contrato c ");
        sql.append("INNER JOIN pessoa pes ON pes.codigo = c.pessoa ");
        sql.append("INNER JOIN cliente cli ON cli.pessoa = pes.codigo ");
        sql.append("INNER JOIN contratooperacao conop ON conop.contrato = c.codigo ");
        sql.append("INNER JOIN historicocontrato h ON h.contrato = c.codigo ");
        sql.append("INNER JOIN periodoacessocliente pa on pa.contrato = c.codigo ");
        sql.append("INNER JOIN plano p ON p.codigo = c.plano ");
        sql.append("INNER JOIN planorecorrencia pr ON pr.plano = p.codigo ");
        sql.append("INNER JOIN planorecorrenciaparcela prp ON prp.planorecorrencia = pr.codigo ");
        sql.append("WHERE c.situacao = 'AT' ");
        sql.append("AND prp.valor = 0 ");
        sql.append("AND (pr.gerarparcelasvalordiferenterenovacao OR pr.gerarparcelasvalordiferenterenovacao) ");
        sql.append("AND conop.tipooperacao = 'CA' ");
        sql.append("AND h.tipohistorico = 'CA' ");
        sql.append("AND pa.tipoacesso = 'CN'");
        sql.append("AND conop.tipojustificativa = 33 ");
        sql.append("AND EXISTS ( ");
        sql.append("  SELECT 1 FROM movparcela m ");
        sql.append("  WHERE m.contrato = c.codigo ");
        sql.append("  AND m.situacao = 'PG' ");
        sql.append("  AND m.valorparcela = 0 ");
        sql.append(");");

        ResultSet consulta = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        MovParcela movParcelaDAO = new MovParcela(con);
        Contrato contratoDAO = new Contrato(con);
        ContratoOperacao contratoOperacaoDAO = new ContratoOperacao(con);
        HistoricoContrato historicoContratoDAO = new HistoricoContrato(con);
        PeriodoAcessoCliente periodoAcessoClienteDAO = new PeriodoAcessoCliente(con);
        ContratoDependente contratoDependenteDAO = new ContratoDependente(con);
        Cliente clienteDAO = new Cliente(con);
        ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(con);
        int codigoContrato = 0;

        while(consulta.next()){
            try{
                con.setAutoCommit(false);
                codigoContrato = consulta.getInt("contrato");

                //Contrato
                ContratoVO contratoVO = contratoDAO.consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                //Contrato Operação
                ContratoOperacaoVO contratoOperacaoVO = (ContratoOperacaoVO) contratoOperacaoDAO.consultarPorCodigo(consulta.getInt("operacao"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS).get(0);
                //Histórico Contrato
                List<HistoricoContratoVO> historicoContratoList = historicoContratoDAO.consultarPorContrato(codigoContrato, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                //Periodo Acesso
                List<PeriodoAcessoClienteVO> periodoAcessoClienteList = periodoAcessoClienteDAO.consultarPorContrato(codigoContrato, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                //Parcela
                List<MovParcelaVO> parcelasContrato =  movParcelaDAO.consultarPorCodigoPessoaContrato(consulta.getInt("pessoa"), contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                MovParcelaVO ultimaParcelaPaga = obtenhaUltimaParcelaPaga(parcelasContrato);
                //Cliente
                ClienteVO clienteAtualizarSintetico = clienteDAO
                        .consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(),
                                Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
                clienteAtualizarSintetico.setDeveAtualizarDependentesSintetico(true);

                Date dataUltimaParcelaPaga = Calendario.getDataComHoraZerada(Calendario.somarDias(ultimaParcelaPaga.getDataVencimento(), Calendar.MONTH)) ;


                if (Calendario.menor(dataUltimaParcelaPaga, Calendario.hoje())) {
                    dataUltimaParcelaPaga = contratoOperacaoVO.getDataOperacao();
                }

                //Contrato
                contratoVO.setSituacao("CA");
                contratoVO.setVigenciaAteAjustada(dataUltimaParcelaPaga);
                contratoVO.setDataPrevistaRenovar(dataUltimaParcelaPaga);

                //Contrato Operação
                contratoOperacaoVO.setDataInicioEfetivacaoOperacao(dataUltimaParcelaPaga);
                contratoOperacaoVO.setDataFimEfetivacaoOperacao(dataUltimaParcelaPaga);


                //Histórico Contrato
                for (HistoricoContratoVO historicoAntigo: historicoContratoList) {
                    HistoricoContratoVO historicoContratoVO = new HistoricoContratoVO();
                    historicoContratoVO = historicoAntigo;
                    if (historicoContratoVO.getTipoHistorico().equals("CA")) {
                        historicoContratoVO.setDataInicioSituacao(dataUltimaParcelaPaga);
                        historicoContratoVO.setDataFinalSituacao(dataUltimaParcelaPaga);
                    } else if (historicoContratoVO.getTipoHistorico().equals("MA") || historicoContratoVO.getTipoHistorico().equals("RE") || historicoContratoVO.getTipoHistorico().equals("RN")) {
                        Date dataHistoricoAnteriorAoCancelamento = Calendario.subtrairDias(dataUltimaParcelaPaga, 1);
                        historicoContratoVO.setDataInicioSituacao(dataHistoricoAnteriorAoCancelamento);
                        historicoContratoVO.setDataFinalSituacao(dataHistoricoAnteriorAoCancelamento);
                    }
                    historicoContratoDAO.alterarSemCommit(historicoContratoVO, false);
                }

                //Periodo Acesso
                for (PeriodoAcessoClienteVO periodoAcessoAntigo: periodoAcessoClienteList) {
                    PeriodoAcessoClienteVO periodoAcessoClienteVO = new PeriodoAcessoClienteVO();
                    periodoAcessoClienteVO = periodoAcessoAntigo;
                    if (periodoAcessoClienteVO.getTipoAcesso().equals("CA")) {
                        periodoAcessoClienteVO.setDataInicioAcesso(dataUltimaParcelaPaga);
                        periodoAcessoClienteVO.setDataFinalAcesso(dataUltimaParcelaPaga);
                    } else {
                        Date dataPeriodoAcessoAnteriorAoCancelamento = Calendario.subtrairDias(dataUltimaParcelaPaga, 1);
                        periodoAcessoClienteVO.setDataInicioAcesso(dataPeriodoAcessoAnteriorAoCancelamento);
                        periodoAcessoClienteVO.setDataFinalAcesso(dataPeriodoAcessoAnteriorAoCancelamento);
                    }
                    periodoAcessoClienteDAO.alterarSemCommit(periodoAcessoClienteVO);
                }


                //Update
                contratoDAO.alterarDatasVigenciaContrato(contratoVO);
                contratoDAO.alterarSituacaoContrato(contratoVO);
                contratoOperacaoDAO.alterar(contratoOperacaoVO);
                contratoDependenteDAO.alterarVigenciaFinalContratoDependente(contratoVO.getCodigo(), contratoVO.getVigenciaAteAjustada());
                contratoOperacaoDAO.atualizaDataFimDeProdutoComVigenciaDeContrato(contratoVO.getCodigo(), contratoVO.getVigenciaDe(), contratoVO.getVigenciaAteAjustada());
                zillyonWebFacadeDAO.atualizarSintetico(clienteAtualizarSintetico, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);

                Uteis.logar("Contrato " + codigoContrato + " teve a operação de cancelamento ajustada.");
                con.commit();
            } catch (Exception e){
                e.printStackTrace();
                Uteis.logar("Erro ao alterar datas da operações de cancelamento do contrato " + codigoContrato + ": "+ e.getMessage());
            } finally {
                con.setAutoCommit(true);
            }

        }
        consulta = null;
        movParcelaDAO = null;
        contratoDAO = null;
        contratoOperacaoDAO = null;
        periodoAcessoClienteDAO = null;
        contratoDependenteDAO = null;
        clienteDAO = null;
        zillyonWebFacadeDAO = null;

    }

    private static MovParcelaVO obtenhaUltimaParcelaPaga(List<MovParcelaVO> todasParcelas) throws Exception {
        List<MovParcelaVO> parcelasOrdenadas = Ordenacao.ordenarListaReverse(todasParcelas, "dataVencimento");
        MovParcelaVO ultimaParcelaPaga = new MovParcelaVO();
        for (MovParcelaVO movParcelaVO : parcelasOrdenadas) {
            if (movParcelaVO.getSituacao().equals("PG") && movParcelaVO.getDescricao().contains("PARCELA") && movParcelaVO.getValorParcela() > 0.00) {
                ultimaParcelaPaga = (MovParcelaVO) movParcelaVO.getClone(false);
                break;
            }
        }
        return ultimaParcelaPaga;
    }

}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Hibrael A Alves",
        data = "02/10/2024",
        descricao = "FP-8 - Trava de Contas FacilitePay",
        motivacao = "FP-8 - Trava de Contas FacilitePay")
public class AtualizacaoTicketFP8 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN qtdlmtcontasconcfacilitepay integer default 0;", c);
        }
    }
}

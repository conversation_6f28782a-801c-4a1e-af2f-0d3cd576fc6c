package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class ProcessoAjustarCancelamentosAutomaticosEngenharia {

    private static ZillyonWebFacade zwFacadeDAO;
    private static Cliente clienteDAO;


    public static void main(String... args) {
        try {
            List<String> chaves = new ArrayList<>();
            chaves.add("engenhariadocorpobrusquesc");
            chaves.add("engenhariadocorpocuritibafazendariogrande");
            if (!UteisValidacao.emptyList(chaves)) {
                for (String chave : chaves) {
                    Uteis.logar(null, "Obter conexão para chave: " + chave);

                    if (UteisValidacao.emptyString(chave)) {
                        throw new Exception("Chave não informada");
                    }
                    Uteis.debug = true;

                    Connection con = new DAO().obterConexaoEspecifica(chave);
                    inicializar(con);
                    ProcessoAjustarCancelamentosAutomaticosEngenharia.corrigirCancelamentosAutomaticosEngenharia(con);
                    con.close();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void inicializar(Connection con) throws Exception {
        zwFacadeDAO = new ZillyonWebFacade(con);
        clienteDAO = new Cliente(con);
    }


    public static void corrigirCancelamentosAutomaticosEngenharia(Connection con) throws Exception {
        try {
            String sqlGerarUpdate = "SELECT \n" +
                    "'UPDATE contrato SET vigenciaateajustada = ''2024-06-11 00:00:00'', dataprevistarenovar = ''2024-06-11 00:00:00'', dataprevistarematricula = ''2024-06-11 00:00:00'' WHERE codigo = ' || con.codigo || ';\n" +
                    "UPDATE contratooperacao SET datafimefetivacaooperacao = ''2024-06-11 00:00:00'', datainicioefetivacaooperacao = ''2024-06-11 00:00:00'' WHERE tipooperacao = ''CA'' AND contrato = ' || con.codigo || ';\n" +
                    "UPDATE historicocontrato SET datafinalsituacao = ''2024-06-10 00:00:00'' WHERE tipohistorico = ''' || con.situacaocontrato || ''' AND contrato = ' || con.codigo || ';\n" +
                    "UPDATE historicocontrato SET datainiciosituacao = ''2024-06-11 00:00:00'', datafinalsituacao = ''2024-06-11 00:00:00'' WHERE tipohistorico = ''CA'' AND contrato = ' || con.codigo || ';\n" +
                    "UPDATE periodoacessocliente SET datafinalacesso = ''2024-06-11 00:00:00'', datainicioacesso = ''2024-06-11 00:00:00'' WHERE tipoacesso = ''CN'' AND contrato = ' || con.codigo || ';\n" +
                    "UPDATE periodoacessocliente SET datafinalacesso = ''2024-06-10 00:00:00'' WHERE tipoacesso = ''CA'' AND contrato = ' || con.codigo || ';\n" +
                    "UPDATE movparcela SET situacao = ''CA'' WHERE datavencimento > ''2024-06-11 00:00:00'' AND descricao NOT ILIKE ''%CANCELAMENTO%'' AND contrato = ' || con.codigo || ';\n" +
                    "UPDATE movproduto SET situacao = ''CA'' WHERE codigo IN (SELECT movproduto FROM movprodutoparcela WHERE movparcela IN (SELECT codigo FROM movparcela WHERE datavencimento > ''2024-06-11 00:00:00'' AND descricao NOT ILIKE ''%CANCELAMENTO%'' AND contrato = ' || con.codigo || '));' AS sqlUpdate,\n" +
                    "\tcli.codigo AS codCliente\n" +
                    "FROM contrato con\n" +
                    "INNER JOIN contratooperacao cop ON cop.contrato = con.codigo \n" +
                    "INNER JOIN cliente cli ON cli.pessoa = con.pessoa \n" +
                    "WHERE cop.dataoperacao BETWEEN '2024-06-11 00:00:00' AND '2024-06-11 23:59:59'\n" +
                    "AND cop.tipooperacao = 'CA'\n" +
                    "AND cop.datafimefetivacaooperacao::date > cop.dataoperacao::date\n" +
                    "AND cop.responsavel = 3\n" +
                    "AND cop.descricaocalculo ILIKE '%AVISOU COM MENOS DE%'\n" +
                    "ORDER BY con.codigo;";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlGerarUpdate)) {
                    Uteis.logar("INÍCIO | ProcessoAjustarCancelamentosAutomaticosEngenharia");
                    while (rs.next()) {
                        String sqlCorrigirContratosParcelas = rs.getString("sqlUpdate");
                        stm.execute(sqlCorrigirContratosParcelas);
                        ClienteVO clienteVO = clienteDAO.consultarPorCodigo(rs.getInt("codCliente"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        zwFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                    }
                    Uteis.logar("FIM | ProcessoAjustarCancelamentosAutomaticosEngenharia");
                }
            }


        } catch (Exception ignored) {
            ignored.printStackTrace();
        }

    }

}

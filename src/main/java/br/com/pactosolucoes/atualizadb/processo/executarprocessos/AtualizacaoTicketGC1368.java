
package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "06/01/2025",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-1368")
public class AtualizacaoTicketGC1368 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE boletowebhook\n" +
                    "(\n" +
                    "codigo serial PRIMARY KEY,\n" +
                    "conveniocobranca INTEGER,\n" +
                    "dataregistro date default now(),\n" +
                    "empresa INTEGER,\n" +
                    "retorno text,\n" +
                    "CONSTRAINT fk_BoletoWebhook_ConvenioCobranca FOREIGN KEY (conveniocobranca) \n" +
                    "REFERENCES ConvenioCobranca (codigo) MATCH SIMPLE \n" +
                    "ON UPDATE NO ACTION ON DELETE CASCADE,\n" +
                    "CONSTRAINT fk_BoletoWebhook_Empresa FOREIGN KEY (empresa) \n" +
                    "REFERENCES Empresa (codigo) MATCH SIMPLE \n" +
                    "ON UPDATE NO ACTION ON DELETE CASCADE);", c);
        }
    }
}

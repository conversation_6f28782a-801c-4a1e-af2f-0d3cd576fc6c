package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "12/03/2025",
        descricao = "Melhoria performance consulta parcelas",
        motivacao = "PAY-405")
public class PAY405MelhoriaPerformanceConsultaParcelas implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_movparcela_datavencimento ON movparcela(datavencimento);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_movparcela_datavencimento_nrtentativas ON movparcela(datavencimento, nrtentativas);", c);
        }
    }
}



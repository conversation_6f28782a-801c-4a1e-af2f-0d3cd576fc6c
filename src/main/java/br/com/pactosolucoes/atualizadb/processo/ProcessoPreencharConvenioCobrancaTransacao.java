package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoERedeVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import org.json.JSONObject;
import servicos.impl.stone.TransacaoStoneOnlineVO;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

public class ProcessoPreencharConvenioCobrancaTransacao {

    public static JSONObject corrigir(Connection con, Integer transacaoCorrigir) {
        JSONObject json = new JSONObject();
        json.put("sucesso", true);
        Integer ajustado = 0;
        Integer erro = 0;
        Integer total = 0;
        ConvenioCobranca convenioCobrancaDAO;
        Transacao transacaoDAO;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            transacaoDAO = new Transacao(con);

            List<ConvenioCobrancaVO> convenios = convenioCobrancaDAO.consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("t.codigo \n");
            sql.append("from transacao t \n");
            sql.append("where t.recibopagamento is null \n");
            sql.append("and t.conveniocobranca is null \n");
            sql.append("and t.transacaoverificarcartao = false \n");
            sql.append("and t.situacao = ").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(" \n");
            if (!UteisValidacao.emptyNumber(transacaoCorrigir)) {
                sql.append("and t.codigo = ").append(transacaoCorrigir).append(" \n");
            }
            sql.append("order by 1 ");

            total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            Integer atual = 0;
            try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = stm.executeQuery()) {
                    Integer transacao = 0;
                    String msg = "";
                    while (rs.next()) {
                        try {
                            transacao = rs.getInt("codigo");
                            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);
                            Integer convenio = 0;

                            if (transacaoVO.getTipo().equals(TipoTransacaoEnum.STONE_ONLINE)) {
                                String stoneCode = ((TransacaoStoneOnlineVO) transacaoVO).getStoneCode();
                                String sak = ((TransacaoStoneOnlineVO) transacaoVO).getSAK();

                                if (UteisValidacao.emptyString(stoneCode) ||
                                        UteisValidacao.emptyString(sak)) {
                                    throw new Exception("StoneCode ou SAK não identificado");
                                }

                                for (ConvenioCobrancaVO conv : convenios) {
                                    if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) &&
                                            conv.getCodigoAutenticacao01().equalsIgnoreCase(stoneCode) &&
                                            conv.getCodigoAutenticacao02().equalsIgnoreCase(sak)) {
                                        convenio = conv.getCodigo();
                                        break;
                                    }
                                }

                                if (UteisValidacao.emptyNumber(convenio)) {
                                    Integer logCod1 = consultarLog("codigoAutenticacao01", stoneCode, con);
                                    Integer logCod2 = consultarLog("codigoAutenticacao02", sak, con);
                                    if (!UteisValidacao.emptyNumber(logCod1) && logCod1.equals(logCod2)) {
                                        convenio = logCod1;
                                    }
                                }

                            } else if (transacaoVO.getTipo().equals(TipoTransacaoEnum.E_REDE)) {
                                String numeroFiliacao = ((TransacaoERedeVO) transacaoVO).getNumeroFiliacao();

                                if (UteisValidacao.emptyString(numeroFiliacao)) {
                                    throw new Exception("Número Filiação não identificado");
                                }

                                for (ConvenioCobrancaVO conv : convenios) {
                                    if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE) &&
                                            conv.getCodigoAutenticacao01().equalsIgnoreCase(numeroFiliacao)) {
                                        convenio = conv.getCodigo();
                                        break;
                                    }
                                }

                                if (UteisValidacao.emptyNumber(convenio)) {
                                    Integer logCod1 = consultarLog("codigoAutenticacao01", numeroFiliacao, con);
                                    if (!UteisValidacao.emptyNumber(logCod1)) {
                                        convenio = logCod1;
                                    }
                                }

                            }

                            if (UteisValidacao.emptyNumber(convenio)) {
                                throw new Exception("Convênio não identificado");
                            }

                            String sqlUpdate1 = "update transacao set conveniocobranca = ? where codigo = ?";
                            try (PreparedStatement pst = con.prepareStatement(sqlUpdate1)) {
                                pst.setInt(1, convenio);
                                pst.setInt(2, transacaoVO.getCodigo());
                                pst.execute();
                            }
                            msg = "Ajustada";
                            ajustado++;
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            msg = ("ERRO: " + ex.getMessage());
                            erro++;
                        } finally {
                            Uteis.logarDebug("GerarReciboTransacao | " + ++atual + "/" + total + " - Transacao " + transacao + " | Resultado: " + msg);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            json.put("sucesso", false);
            json.put("msg", ex.getMessage());
        } finally {
            convenioCobrancaDAO = null;
            transacaoDAO = null;
        }
        json.put("ajustado", ajustado);
        json.put("erro", erro);
        json.put("total", total);
        return json;
    }

    private static Integer consultarLog(String campo, String valor, Connection con) throws SQLException {
        String sql = "select chaveprimaria from log where nomeentidade  = 'CONVENIOCOBRANCA' and nomecampo = '" + campo + "' and valorcampoalterado = '" + valor + "'";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    String chaveprimaria = rs.getString("chaveprimaria");
                    if (!UteisValidacao.emptyString(chaveprimaria)) {
                        return Integer.parseInt(chaveprimaria);
                    }
                }
            }
        }
        return null;
    }

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("*************************************************", "zillyonweb", "pactodb");
            JSONObject json = ProcessoPreencharConvenioCobrancaTransacao.corrigir(con, null);
            System.out.println(json);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}

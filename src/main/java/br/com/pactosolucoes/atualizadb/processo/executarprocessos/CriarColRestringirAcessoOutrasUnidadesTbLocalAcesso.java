package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "22/08/2024",
        descricao = "Cria coluna 'restringirAcessoOutrasUnidades' na tabela 'localAcesso'",
        motivacao = "GC-908")
public class CriarColRestringirAcessoOutrasUnidadesTbLocalAcesso implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            String sql = "ALTER TABLE localAcesso ADD COLUMN restringirAcessoOutrasUnidades BOOLEAN DEFAULT FALSE";
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "26/03/2025",
        descricao = "Integracao PínBank",
        motivacao = "PAY-384")
public class PAY384IntegracaoPinBank implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE operadoracartao RENAME COLUMN codigointegracaovaloribank TO codigointegracaopinbank;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaoCobrancaCliente RENAME COLUMN idValoriBank TO idPinBank;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;

import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.JustificativaOperacao;

public class CorrigirJustificativaImportacao {
	
	public static void corrigirJustificativa(Connection con) throws Exception{
		ResultSet consulta = SuperFacadeJDBC.criarConsulta("SELECT * FROM justificativaoperacao where descricao like 'FÉRIAS IMPORTAÇÃO%'", con);
		List<JustificativaOperacaoVO> lista = JustificativaOperacao.montarDadosConsulta(consulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
		if(lista.size() > 1){
			JustificativaOperacaoVO justificativa = lista.get(0);
			SuperFacadeJDBC.executarConsulta("UPDATE contratooperacao SET tipojustificativa = "+
											 justificativa.getCodigo() + " WHERE tipojustificativa IN " +
											 "(SELECT codigo FROM justificativaoperacao where descricao like 'FÉRIAS IMPORTAÇÃO%')", con);
			SuperFacadeJDBC.executarConsulta("DELETE FROM justificativaoperacao where descricao like 'FÉRIAS IMPORTAÇÃO%' AND codigo <> "+
					 						 justificativa.getCodigo() , con);
		}
	}
}

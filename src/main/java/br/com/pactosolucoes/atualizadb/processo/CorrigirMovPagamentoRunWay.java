/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import static br.com.pactosolucoes.atualizadb.processo.CorrigirImportacao.formataData;
import static br.com.pactosolucoes.atualizadb.processo.CorrigirImportacao.retirarNulo;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.CartaoCredito;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.jdom.Element;
import test.simulacao.LeitorXML;

/**
 *
 * <AUTHOR>
 */
public class CorrigirMovPagamentoRunWay {

    public static class Pagamento {

        MovPagamentoVO pagVO = new MovPagamentoVO();
        Map<String, List<CartaoCreditoVO>> cartoes = new HashMap<String, List<CartaoCreditoVO>>();

        Pagamento(MovPagamentoVO p) {
            pagVO = p;
        }

        void addCartao(Element forma) throws Exception {
            List<CartaoCreditoVO> cartss = cartoes.get(retirarNulo(forma, "NomeOuAuto"));
            if (cartss == null) {
                cartss = new ArrayList<CartaoCreditoVO>();
                cartoes.put(retirarNulo(forma, "NomeOuAuto"), cartss);
            }
            CartaoCreditoVO cartao = new CartaoCreditoVO();
            cartao.setValor(Double.valueOf(retirarNulo(forma, "Vl_NoCheque")));
            cartao.setValorTotal(Double.valueOf(retirarNulo(forma, "Vl_NoCheque")));
            cartao.setOperadora(new OperadoraCartaoVO());
            cartao.getOperadora().setCodigo(9);
            cartao.setDataCompensacao(formataData(retirarNulo(forma, "Dt_Vencimento")));
            cartao.setSituacao("PG");
            cartss.add(cartao);
        }
    }

    public static void remontarPagamentos(Map<Integer, Pagamento> mapa, Connection con) throws Exception {
        MovPagamento dao = new MovPagamento(con);
        PagamentoMovParcela daoPMP = new PagamentoMovParcela(con);
        CartaoCredito cartaoDao = new CartaoCredito(con);
        int inc = 0;
        for (Integer key : mapa.keySet()) {
            System.out.println("Atualizando pagamentos: " + (++inc) + "/" + mapa.keySet().size());
            try {
                Pagamento pagamento = mapa.get(key);
                boolean alterar = true;
                    SuperFacadeJDBC.executarConsulta("DELETE FROM cartaocredito WHERE movpagamento = " + pagamento.pagVO.getCodigo(), con);
                for (String auto : pagamento.cartoes.keySet()) {
                    List<CartaoCreditoVO> cartoes = pagamento.cartoes.get(auto);
                    Double valor = 0.0;
                    ResultSet rsPagamento = SuperFacadeJDBC.criarConsulta(
                            "select * from pagamentomovparcela where movpagamento = " + pagamento.pagVO.getCodigo() , con);
                    if (rsPagamento.next()) {
                        MovPagamentoVO movPag = (MovPagamentoVO) pagamento.pagVO.getClone(true);
                        PagamentoMovParcelaVO pagamentoMovParcela = PagamentoMovParcela.montarDados(rsPagamento, Uteis.NIVELMONTARDADOS_TODOS, con);
                        for (CartaoCreditoVO cartao : cartoes) {
                            valor += cartao.getValor();
                            if (alterar) {
                                cartao.setMovpagamento(movPag);
                                cartaoDao.incluir(cartao);
                            }
                        }
                        movPag.setValor(valor);
                        movPag.setValorTotal(valor);
                        pagamentoMovParcela.setValorPago(valor);
                        if (alterar) {
                            dao.alterar(movPag);
                            daoPMP.alterar(pagamentoMovParcela);
                            alterar = false;
                        } else {
                            dao.incluir(movPag);
                            pagamentoMovParcela.setMovPagamento(movPag.getCodigo());
                            daoPMP.incluir(pagamentoMovParcela);
                            for (CartaoCreditoVO cartao : cartoes) {
                                cartao.setMovpagamento(movPag);
                                cartaoDao.incluir(cartao);
                            }
                        }
                        SuperFacadeJDBC.executarConsulta("UPDATE movpagamento set id_recebe = " + auto + " where codigo = " + movPag.getCodigo(), con);
                    }
                }

            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
    }

    public static void main(String... args) {
        try {
            Connection con = DriverManager.getConnection("***************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);
            Integer empresa = 1;
//            addAutorizacaoCartao(con);
            //    5;"ACADEMIA RUNWAY - ESCRITÓRIO"
            //4;"ACADEMIA RUNWAY - ÁGUAS CLARAS"
            //2;"ACADEMIA RUNWAY - ASA NORTE"
            //3;"ACADEMIA RUNWAY - LAGO NORTE"
            //1;"ACADEMIA RUNWAY - SUDOESTE"
            LeitorXML leitorXML = new LeitorXML();
            List<Element> listaPagamentos = leitorXML.lerXML("D:\\Teste.xml");
            Map<Integer, Pagamento> mapa = new HashMap<Integer, Pagamento>();
            Map<Integer, List<Element>> elements = new HashMap<Integer, List<Element>>();

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT mp.*, EXISTS (select codigo from chequecartaolote \n");
            sql.append("where cartao in (select codigo from cartaocredito where movpagamento = mp.codigo)) as temmovimento\n");
            sql.append("FROM movpagamento mp WHERE id_recebe is not null and formapagamento in (1,9,4) and empresa = ");
            sql.append(empresa).append(" order by temmovimento\n");

            Integer contar = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql.toString() + ") as a", con);
            List<MovPagamentoVO> naotemMovimento = new ArrayList<MovPagamentoVO>();
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            System.out.println(" ----------- Montar listas de pagamentos --------------");
            int inc = 0;

            for (Element forma : listaPagamentos) {
                List<Element> list = elements.get(Integer.parseInt(retirarNulo(forma, "Id_Recebe")));
                if (list == null) {
                    list = new ArrayList<Element>();
                    elements.put(Integer.parseInt(retirarNulo(forma, "Id_Recebe")), list);
                }
                list.add(forma);
            }

            while (rs.next()) {
                System.out.println("Montando lista: " + (++inc) + "/" + contar);
                if (!rs.getBoolean("temmovimento") && elements.keySet().contains(rs.getString("id_recebe"))) {
                    MovPagamentoVO pagamento = MovPagamento.montarDados(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
                    pagamento.setId_recebe(rs.getInt("id_recebe"));
                    naotemMovimento.add(pagamento);
                }
            }


            for (MovPagamentoVO pagamento : naotemMovimento) {
                if (elements.get(pagamento.getId_recebe()) == null) {
                    continue;
                }
                for (Element forma : elements.get(pagamento.getId_recebe())) {
                    Pagamento pag = mapa.get(pagamento.getId_recebe());
                    if (pag == null) {
                        pag = new Pagamento(pagamento);
                        mapa.put(pagamento.getId_recebe(), pag);
                    }
                    pag.addCartao(forma);
                }
            }

            remontarPagamentos(mapa, con);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Maurin <PERSON>",
        data = "05/03/2025",
        descricao = "Add coluna registrarBoletoOnlineImpressao na tabela conveniocobranca",
        motivacao = "PAY-334")
public class PAY334AddColumRegistrarBoletoOnlineImpressao implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE conveniocobranca ADD COLUMN registrarBoletoOnlineImpressao Boolean DEFAULT false;",
                    c
            );
        }
    }
}

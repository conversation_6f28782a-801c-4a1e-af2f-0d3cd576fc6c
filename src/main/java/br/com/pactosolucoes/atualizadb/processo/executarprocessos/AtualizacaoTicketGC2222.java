package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "09/07/2025",
        descricao = "gravar assinatura contrato produto diaria",
        motivacao = "GC-2222")
public class AtualizacaoTicketGC2222 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produtotextopadrao ADD COLUMN aulaavulsadiaria integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produtotextopadrao ALTER COLUMN vendaavulsa DROP NOT NULL;", c);
        }
    }
}

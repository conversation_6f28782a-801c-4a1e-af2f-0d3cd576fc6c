package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "30/12/2024",
        descricao = "GC-1327 Configuração Integração Foguete",
        motivacao = "GC-1327 Configuração Integração Foguete")
public class AtualizacaoTicketGC1327 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE configuracaointegracaofoguete (\n" +
                    "\tcodigo serial,\n" +
                    "\thabilitada boolean,\n" +
                    "\ttokenapi varchar(255),\n" +
                    "\tempresa int4 NOT NULL,\n" +
                    "\tproduto int4 NOT NULL,\n" +
                    "\tCONSTRAINT configuracaointegracaofoguete_pkey PRIMARY KEY (codigo),\n" +
                    "\tCONSTRAINT configuracaointegracaofoguete_empresa_fkey FOREIGN KEY (empresa) REFERENCES public.empresa(codigo)\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE contrato ADD COLUMN dataSincronizacaoIntegracaoFoguete timestamp;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luis Antônio de Melo Gomes",
        data = "20/05/2025",
        descricao = "atualizar tabela de telefone para aceitar quantidade maior de caracteres",
        motivacao = "IA-1294")
public class AtualizacaoTicketIA1294 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE telefone ALTER COLUMN numero TYPE varchar(18);", c);
        }
    }
}

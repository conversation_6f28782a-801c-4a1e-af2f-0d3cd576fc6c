/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;

/**
 *
 * <AUTHOR>
 */
public class CorrigirManutencaoModalidade {

    public static void main(String... args) {
        try {
            Connection con = DriverManager.getConnection("**************************************", "postgres", "pactodb");
            MovProduto movDAO = new MovProduto(con);
            
            String sqlDif = "select (select sum(totalfinal) from movproduto where contrato = c.codigo and produto = 9) as totalmp,\n"
                    + "(select sum(valor) from movprodutomodalidade where contrato = c.codigo) as totalmpm,\n"
                    + " valorbasecalculo, codigo from contrato c\n"
                    + " where (select sum(totalfinal) from movproduto where contrato = c.codigo "
                    + "and produto = 9) > (select sum(valor) from movprodutomodalidade where contrato = c.codigo)";
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlDif, con);
            int i = 0;
            while (rs.next()) {
                
                if (Uteis.arredondarForcando2CasasDecimais(rs.getDouble("totalmp"))
                        > Uteis.arredondarForcando2CasasDecimais(rs.getDouble("totalmpm"))) {
                    Integer codigoContrato = rs.getInt("codigo");
                    SuperFacadeJDBC.executarConsulta("DELETE FROM movprodutomodalidade WHERE contrato = "+codigoContrato,con);
                    
                    MovProdutoModalidade movProdMod = new MovProdutoModalidade(con);
                    StringBuilder sql = new StringBuilder();
                    sql.append("select MP.codigo as movproduto, MP.contrato, mp.totalfinal, c.vigenciade, ");
                    sql.append("c.vigenciaateajustada, cm.modalidade, cm.valormodalidade, (select sum(valormodalidade) from contratomodalidade where contrato = c.codigo) as valorModalidades");
                    sql.append(" from movproduto mp ");
                    sql.append(" inner join produto p on mp.produto = p.codigo and p.tipoproduto like 'PM' ");
                    sql.append(" inner join contrato c on mp.contrato = c.codigo ");
                    sql.append(" inner join contratomodalidade cm on cm.contrato = c.codigo ");
                    sql.append(" left join movprodutomodalidade mpm on mpm.movproduto = mp.codigo   ");
                    sql.append(" where mp.contrato =  ").append(codigoContrato);
                    sql.append(" order by mp.codigo ");
                    ResultSet dados = con.prepareStatement(sql.toString()).executeQuery();
                    while (dados.next()) {
                        Double valorProduto = dados.getDouble("totalfinal");
                        Double valorTotalModalidade = dados.getDouble("valorModalidades");
                        Double valorModalidade = dados.getDouble("valormodalidade");

                        Double valorMovProdModalidade = valorTotalModalidade == 0.0 ? 0.0 : (valorProduto * (valorModalidade / valorTotalModalidade));

                        MovProdutoModalidadeVO mpm = new MovProdutoModalidadeVO();
                        mpm.getMovProdutoVO().setCodigo(dados.getInt("movproduto"));
                        mpm.getModalidadeVO().setCodigo(dados.getInt("modalidade"));
                        mpm.setDataInicio(dados.getDate("vigenciade"));
                        mpm.setDataFim(dados.getDate("vigenciaateajustada"));
                        mpm.setValor(valorMovProdModalidade);
                        mpm.getContrato().setCodigo(dados.getInt("contrato"));
                        movProdMod.incluir(mpm);
                    }
                    movDAO.atualizarDescricaoMovProdutoModalidade(codigoContrato);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


    }
}

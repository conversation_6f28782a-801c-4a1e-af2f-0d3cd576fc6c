package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luis Antônio de Melo Gomes",
        data = "14/03/2025",
        descricao = "Implementar configuracao de gymbot para distribuicao para conversas IA",
        motivacao = "IA-707")
public class AtualizacaoTicketIA707 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE configuracaocrmia" +
                            "    ADD COLUMN tokenGymbot VARCHAR(255)," +
                            "    ADD COLUMN habilitarGymbot BOOLEAN DEFAULT false," +
                            "    ADD COLUMN idDepartamento VARCHAR(255)," +
                            "    ADD COLUMN descricaoDepartamento Character Varying;"
                    , c);
        }
    }
}



package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoMovParcelaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.financeiro.TransacaoMovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.gatewaypagamento.CobrancaOnlineService;
import servicos.impl.gatewaypagamento.VerificadorTransacaoService;
import servicos.impl.stone.StoneOnlineService;
import servicos.interfaces.AprovacaoServiceInterface;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.List;

public class ProcessoCorrecaoTransacaoDuplicada {

    public static void corrigirTransacoes(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("t.codigo \n");
        sql.append("from transacao t \n");
        sql.append("where coalesce(t.situacao,0) = ").append(SituacaoTransacaoEnum.NENHUMA.getId()).append(" \n");
        sql.append("and t.codigoretorno  = '?' \n");
        sql.append("and dataprocessamento::date >= '01/09/2021' \n");
        sql.append("order by t.codigo  \n");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
        Integer atual = 0;

        try {
            Uteis.logarDebug("INICIO | ProcessoCorrecaoTransacaoDuplicada | AJUSTAR " + total + " Itens");

            Transacao transacaoDAO = new Transacao(con);
            TransacaoMovParcela transacaoMovParcelaDAO = new TransacaoMovParcela(con);
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {

                        Uteis.logarDebug("Atual " + ++atual + "/" + total + " | ProcessoCorrecaoTransacaoDuplicada");

                        Integer transacao = 0;
                        AprovacaoServiceInterface service = null;
                        StoneOnlineService serviceStone = null;
                        try {
                            transacao = rs.getInt("codigo");
                            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);
                            TransacaoVO transacaoAnterior = (TransacaoVO) transacaoVO.getClone(true);
                            if (UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                                throw new Exception("CONVENIO DE COBRANCA NÃO IDENTIFICADO");
                            }
                            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            transacaoVO.setConvenioCobrancaVO(convenioCobrancaVO);

                            service = CobrancaOnlineService.getImplementacaoAprovacaoService(transacaoVO.getTipo(), transacaoVO.getEmpresa(),
                                    transacaoVO.getConvenioCobrancaVO().getCodigo(), false, con);
                            if (service == null) {
                                throw new Exception("SERVICE NÂO IDENTIFICADO");
                            }

                            service.consultarSituacaoCobrancaTransacao(transacaoVO);


                            if (!transacaoAnterior.getSituacao().equals(transacaoVO.getSituacao())) {

                                boolean gerarPagamento = false;
                                boolean cancelarTransacao = false;

                                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                                    //verificar se as parcelas estão em aberto

                                    List<TransacaoMovParcelaVO> lista = transacaoMovParcelaDAO.cosultarPorCodigoTransacao(transacaoVO.getCodigo());

                                    if (!UteisValidacao.emptyList(lista)) {
                                        gerarPagamento = true;
                                        for (TransacaoMovParcelaVO transacaoMovParcelaVO : lista) {
                                            if (transacaoMovParcelaVO.getMovParcela() == null ||
                                                    !transacaoMovParcelaVO.getMovParcela().getSituacao().equalsIgnoreCase("EA")) {
                                                cancelarTransacao = true;
                                                gerarPagamento = false;
                                                break;
                                            }
                                        }
                                    } else {
                                        Uteis.logarDebug("Transação " + transacaoVO.getCodigo() + " | SEM PARCELAS PARA BAIXAR");
                                        cancelarTransacao = true;
                                    }

                                    if (gerarPagamento) {
                                        Uteis.logarDebug("Transação " + transacaoVO.getCodigo() + " | GERAR RECIBO");
                                        transacaoVO.setUtilizarUsuarioGerarRecibo(true);
                                        transacaoVO.setDataCobranca(transacaoVO.getDataProcessamento());
                                        VerificadorTransacaoService verificadorTransacaoService = new VerificadorTransacaoService(con);
                                        verificadorTransacaoService.processarPagamentoTransacao(transacaoVO, transacaoVO.getTipo(), con);
                                        verificadorTransacaoService = null;
                                        transacaoDAO.alterar(transacaoVO);
                                    } else if (cancelarTransacao) {
                                        Uteis.logarDebug("Transação " + transacaoVO.getCodigo() + " | VOU CANCELAR");
                                        if (transacaoVO.getTipo().equals(TipoTransacaoEnum.STONE_ONLINE)) {
                                            serviceStone = new StoneOnlineService(con, transacaoVO.getEmpresaVO().getCodigo(), convenioCobrancaVO.getCodigo());
                                            serviceStone.cancelarTransacaoComdentificadorStone(transacaoVO, true, transacaoVO.getCodigoExterno());
                                        } else {
                                            service.cancelarTransacao(transacaoVO, true);
                                        }

                                        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) ||
                                                transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                                            Uteis.logarDebug("Transação " + transacaoVO.getCodigo() + " | TRANSAÇÃO FOI CANCELADA");
                                            transacaoDAO.alterar(transacaoVO);
                                        }
                                    }

                                } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
                                    Uteis.logarDebug("Transação " + transacaoVO.getCodigo() + " | JÁ ESTÁ CANCELADA");
                                    transacaoDAO.alterar(transacaoVO);
                                } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                                    Uteis.logarDebug("Transação " + transacaoVO.getCodigo() + " | NAO APROVADA");
                                    transacaoDAO.alterar(transacaoVO);
                                } else {
                                    Uteis.logarDebug("Transação " + transacaoVO.getCodigo() + " | NÃO FEZ NADA");
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            Uteis.logarDebug("Transação " + transacao + " | ERRO | " + ex.getMessage());
                        } finally {
                            service = null;
                            serviceStone = null;
                        }
                    }
                }
            }
        } finally {
            Uteis.logarDebug("FIM | ProcessoCorrecaoTransacaoDuplicada");
        }
    }

    private static void todosBancos() {
        try (Connection conOAMD = Conexao.obterConexaoBancoEmpresas()) {
            try (Statement stm = conOAMD.createStatement()) {
                try (ResultSet rs = stm.executeQuery("select chave,\"hostBD\",porta,\"nomeBD\",\"userBD\",\"passwordBD\" from empresa")) {
                    while (rs.next()) {
                        Uteis.logarDebug("Processando Banco: " + rs.getString("chave") + " | " + rs.getString("nomeBD"));
                        try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + rs.getString("hostBD") + ":" + rs.getInt("porta") + "/" + rs.getString("nomeBD"), rs.getString("userBD"), rs.getString("passwordBD"))) {
                            ProcessoCorrecaoTransacaoDuplicada.corrigirTransacoes(con);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;

            String chave = null;
            if (args.length > 0) {
                chave = args[0];
            }

            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }

            boolean todosBancos = chave.equalsIgnoreCase("TODASEMPRESA");

            if (todosBancos) {
                todosBancos();
            } else {
                Uteis.logarDebug("Obter conexão para chave: " + chave);
//                Connection con = DriverManager.getConnection("********************************************************************", "zillyonweb", "pactodb");
                Connection con = new DAO().obterConexaoEspecifica(chave);
                ProcessoCorrecaoTransacaoDuplicada.corrigirTransacoes(con);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}


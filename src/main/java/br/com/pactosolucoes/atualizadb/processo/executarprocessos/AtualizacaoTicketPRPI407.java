package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Victor Augusto <PERSON>",
        data = "17/01/2025",
        descricao = "Criar interface de configuração de meta extra no CRM.",
        motivacao = "PRPI-407")
public class AtualizacaoTicketPRPI407 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofaseia ADD COLUMN codigometaextra INTEGER;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofaseia ADD COLUMN nomemetaextra VARCHAR(255);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE configuracaofaseia ADD COLUMN codigoempresa INTEGER DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE mensagemenviada ADD COLUMN codigometaextra INTEGER DEFAULT NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE mensagemenviada ADD COLUMN nomemetaextra  VARCHAR(255);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE mensagemenviada ALTER COLUMN nomefase DROP NOT NULL;", c);
        }
    }
}
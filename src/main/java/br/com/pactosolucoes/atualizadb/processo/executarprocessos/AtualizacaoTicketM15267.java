package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "16/04/2025",
        descricao = "Alterar ligação das tabelas para não ocorrer erro ao excluir um modelo de contrato, produto ou venda avulsa que tenha ligação com a produtotextopadrao",
        motivacao = "M1-5267")
public class AtualizacaoTicketM15267 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.produtotextopadrao DROP CONSTRAINT fk_produtotextopadrao_planotextopadrao;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.produtotextopadrao ADD CONSTRAINT fk_produtotextopadrao_planotextopadrao\n" +
                    "FOREIGN KEY (planotextopadrao) REFERENCES public.planotextopadrao (codigo) ON DELETE CASCADE ON UPDATE RESTRICT;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.produtotextopadrao DROP CONSTRAINT fk_produtotextopadrao_produto;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.produtotextopadrao ADD CONSTRAINT fk_produtotextopadrao_produto\n" +
                    "FOREIGN KEY (produto) REFERENCES public.produto (codigo) ON DELETE CASCADE ON UPDATE RESTRICT;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.produtotextopadrao DROP CONSTRAINT fk_produtotextopadrao_vendaavulsa;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.produtotextopadrao ADD CONSTRAINT fk_produtotextopadrao_vendaavulsa\n" +
                    "FOREIGN KEY (vendaavulsa) REFERENCES public.vendaavulsa (codigo) ON DELETE CASCADE ON UPDATE RESTRICT;", c);
        }
    }
}

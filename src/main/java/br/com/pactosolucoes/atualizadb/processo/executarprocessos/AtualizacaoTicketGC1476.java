package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "30/01/2025",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-1476")
public class AtualizacaoTicketGC1476 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE produto ADD COLUMN contratoTextoPadrao integer default null;", c);
            StringBuilder sql = new StringBuilder();
            sql.append("    CREATE TABLE produtoTextoPadrao ");
            sql.append("                 ( 			  codigo serial NOT NULL                                                , ");
            sql.append("                              vendaavulsa INTEGER NOT NULL                          , ");
            sql.append("                              produto INTEGER NOT NULL                          , ");
            sql.append("                              planotextopadrao INTEGER NOT NULL                          , ");
            sql.append("                              texto text NOT NULL                          , ");
            sql.append("                              datalancamento timestamp NOT NULL                                                , ");
            sql.append("                              CONSTRAINT produtoTextoPadrao_pkey PRIMARY KEY (codigo), ");
            sql.append("                              CONSTRAINT fk_produtoTextoPadrao_produto FOREIGN KEY (produto) REFERENCES produto (codigo) MATCH SIMPLE ON ");
            sql.append("                 UPDATE RESTRICT ON DELETE RESTRICT, ");
            sql.append("                              CONSTRAINT fk_produtoTextoPadrao_planotextopadrao FOREIGN KEY (planotextopadrao) REFERENCES planotextopadrao (codigo) MATCH SIMPLE ON ");
            sql.append("                 UPDATE RESTRICT ON DELETE RESTRICT, ");
            sql.append("                              CONSTRAINT fk_produtoTextoPadrao_vendaavulsa FOREIGN KEY (vendaavulsa) REFERENCES vendaavulsa (codigo) MATCH SIMPLE ON ");
            sql.append("                 UPDATE RESTRICT ON DELETE RESTRICT ); ");
            sql.append("          CREATE INDEX ch_produtoTextoPadrao_vendaavulsa ");
            sql.append("          ON produtoTextoPadrao ");
            sql.append("          USING        btree ( vendaavulsa );");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), c);
        }
    }
}

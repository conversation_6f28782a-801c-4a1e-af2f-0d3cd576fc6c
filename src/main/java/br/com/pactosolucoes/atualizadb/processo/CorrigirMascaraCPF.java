package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;

/**
 * Created by glauco on 31/03/2014.
 */
public class CorrigirMascaraCPF {


    public static void main(String... args) {
        try {
            Connection con = DriverManager.getConnection("**********************************************", "zillyonweb", "pactodb");
            CorrigirMascaraCPF processo = new CorrigirMascaraCPF();
            processo.corrigirTodos(con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void corrigirTodos(Connection con) throws Exception {
        con.prepareStatement("UPDATE pessoa\n" +
                "SET cfp = (substring(cfp FROM 1 FOR 3) || '.' || substring(cfp FROM 4 FOR 3) || '.' ||\n" +
                "           substring(cfp FROM 7 FOR 3) || '-' || substring(cfp FROM 10 FOR 2))\n" +
                "WHERE length(cfp) < 14 AND length(cfp) > 0").execute();

        con.prepareStatement("UPDATE pessoa SET cfp = '' WHERE length(cfp) < 14 AND length(cfp) > 0").execute();
    }

}

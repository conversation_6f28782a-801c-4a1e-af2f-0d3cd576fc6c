package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "19/02/2025",
        descricao = "Add coluna geradoAutomaticoPlanoRecorrente na tabela condicaopagamento",
        motivacao = "PAY-233")
public class PAY233AddColumGeradoAutomaticoPlanoRecorrente implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE condicaopagamento ADD COLUMN geradoAutomaticoPlanoRecorrente Boolean DEFAULT false;",
                    c
            );
        }
    }
}

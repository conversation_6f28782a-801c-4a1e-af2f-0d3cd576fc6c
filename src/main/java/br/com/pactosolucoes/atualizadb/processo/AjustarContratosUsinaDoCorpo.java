package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class AjustarContratosUsinaDoCorpo {

    public static void main(String... args) {
        try {
            Connection con = DriverManager.getConnection("***************************************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);
            ajustarParcelas(con);
        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void ajustarParcelas(Connection con) throws Exception {
        try {
            MovProdutoParcela movProdutoParcela = new MovProdutoParcela(con);
            MovParcela movParcelaDAO = new MovParcela(con);
            MovProduto movProdutoDAO = new MovProduto(con);

            Statement stm = con.createStatement();
            ResultSet query = stm.executeQuery("select *, exists(select codigo from contrato where contrato.pessoa = movimentocontacorrentecliente.pessoa\n" +
                    "AND contrato.empresa = 4) as contratoVigente from \n" +
                    "movimentocontacorrentecliente where saldoatual < 0");
            while (query.next()) {
                boolean contratoVigente = query.getBoolean("contratoVigente");
                if (contratoVigente) {
                    int codPessoa = query.getInt("pessoa");
                    Statement stmContratosManter = con.createStatement();
                    ResultSet rsContratoManter = stmContratosManter.executeQuery("select max(codigo), vigenciaateajustada \n" +
                            "from contrato \n" +
                            "where pessoa = " + codPessoa + " \n" +
                            "group by vigenciaateajustada ");
                    StringBuilder sbCodigosManter = new StringBuilder();
                    while (rsContratoManter.next()) {
                        if (sbCodigosManter.length() > 0) {
                            sbCodigosManter.append(",");
                        }

                        int codigoContratoManter = rsContratoManter.getInt("max");
                        sbCodigosManter.append(codigoContratoManter);
                    }

                    Statement stmDeletarContratoDuplicado = con.createStatement();
                    stmDeletarContratoDuplicado.execute("DELETE FROM contrato WHERE pessoa = " + codPessoa + " AND codigo NOT IN (" + sbCodigosManter.toString() + ");");

                    Statement stmContratos = con.createStatement();
                    ResultSet rsContratos = stmContratos.executeQuery("SELECT codigo, datalancamento, empresa FROM contrato WHERE pessoa = " + codPessoa);
                    while (rsContratos.next()) {
                        int codContrato = rsContratos.getInt("codigo");

                        boolean avaliarContrato = SuperFacadeJDBC.existe("select codigo from movparcela where contrato = " + codContrato + " and (coalesce(pessoa,0) =0 or valorparcela = 0)", con);
                        if (!avaliarContrato) {
                            continue;
                        }

                        Date datalancamento = rsContratos.getDate("datalancamento");
                        Date dataVencimento = rsContratos.getDate("datalancamento");
                        int empresa = rsContratos.getInt("empresa");

                        Statement stmExcluirMovparcelas = con.createStatement();
                        stmExcluirMovparcelas.execute("DELETE FROM movparcela WHERE contrato = " + codContrato + " AND (pessoa is null  or valorparcela = 0)");

                        PagamentoMovParcela.corrigirPagamentosSemMovParcelas(con);

                        Statement stmMovprodutos = con.createStatement();
                        ResultSet rsMovprodutos = stmMovprodutos.executeQuery("SELECT * FROM movproduto WHERE contrato = " + codContrato + " ORDER BY codigo");

                        int numeroParcela = 1;
                        double valorListaprodutos = 0.0;
                        List<MovProdutoVO> movprodutosParcela = new ArrayList<MovProdutoVO>();
                        while (rsMovprodutos.next()) {
                            MovProdutoVO movProdutoVO = MovProduto.montarDados(rsMovprodutos, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);

                            Statement stmExisteMovProdutoParcela = con.createStatement();
                            ResultSet rsExisteMovprodutoParcela = stmExisteMovProdutoParcela.executeQuery("SELECT codigo FROM movprodutoparcela WHERE movproduto = " + movProdutoVO.getCodigo());
                            boolean existeMovProdutoParcela = rsExisteMovprodutoParcela.next();

                            if (!existeMovProdutoParcela) {
                                movprodutosParcela.add(movProdutoVO);
                                valorListaprodutos += movProdutoVO.getTotalFinal();

                                if (valorListaprodutos > 0.0) {

                                    boolean existeMovParcela = true;
                                    while (existeMovParcela) {
                                        Statement stmExisteMovParcela = con.createStatement();
                                        ResultSet rsExisteMovParcela = stmExisteMovParcela.executeQuery("SELECT codigo FROM movparcela WHERE descricao = 'PARCELA " + numeroParcela + "' AND contrato = " + codContrato);
                                        existeMovParcela = rsExisteMovParcela.next();

                                        if (existeMovParcela) {
                                            numeroParcela++;
                                            dataVencimento = Uteis.somarMeses(dataVencimento, 1);
                                        }
                                    }


                                    MovParcelaVO movParcelaVO = insiraMovParcela("PARCELA " + numeroParcela, codContrato, codPessoa, datalancamento, dataVencimento, empresa, valorListaprodutos, movParcelaDAO);
                                    for (MovProdutoVO mprod : movprodutosParcela) {
                                        MovProdutoParcelaVO movProdutoParcelaVO = new MovProdutoParcelaVO();
                                        movProdutoParcelaVO.setValorPago(mprod.getTotalFinal());
                                        movProdutoParcelaVO.setMovProduto(mprod.getCodigo());
                                        movProdutoParcelaVO.setMovParcela(movParcelaVO.getCodigo());
                                        movProdutoParcela.incluir(movProdutoParcelaVO);

                                        movProdutoDAO.alterarSomenteSituacaoSemCommit(mprod.getCodigo(), "EA");
                                    }

                                    numeroParcela++;
                                    dataVencimento = Uteis.somarMeses(dataVencimento, 1);
                                    valorListaprodutos = 0.0;
                                    movprodutosParcela = new ArrayList<MovProdutoVO>();
                                }
                            }
                        }
                    }

                    Statement stmDeletarCC = con.createStatement();
                    stmDeletarCC.execute("DELETE FROM movimentocontacorrentecliente WHERE pessoa = " + codPessoa);
                }
            }
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
            }
        }
    }

    private static MovParcelaVO insiraMovParcela(String descricao, Integer contrato, Integer pessoa, Date datalancamento, Date dataVencimento, Integer empresa, double valorNovaParcela, MovParcela movParcelaDAO) throws Exception {
        System.out.println("Inserindo novo MovParcela...");
        MovParcelaVO novaParcela = new MovParcelaVO();
        novaParcela.setDescricao(descricao);
        novaParcela.setResponsavel(new UsuarioVO());
        novaParcela.getResponsavel().setCodigo(1);
        novaParcela.setValorParcela(valorNovaParcela);
        novaParcela.setContrato(new ContratoVO());
        novaParcela.getContrato().setCodigo(contrato);
        novaParcela.setSituacao("EA");
        novaParcela.setDataRegistro(datalancamento);
        novaParcela.setEmpresa(new EmpresaVO());
        novaParcela.getEmpresa().setCodigo(empresa);
        novaParcela.setPessoa(new PessoaVO());
        novaParcela.getPessoa().setCodigo(pessoa);
        novaParcela.setDataVencimento(dataVencimento);
        novaParcela.setDataCobranca(dataVencimento);
        movParcelaDAO.incluir(novaParcela);
        return novaParcela;
    }
}
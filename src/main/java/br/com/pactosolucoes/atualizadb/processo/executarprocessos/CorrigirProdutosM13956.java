package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Marcos Andre",
        data = "07/01/2025",
        descricao = "Produtos renovaveis sem renovar",
        motivacao = "M1-3956 ")
public class CorrigirProdutosM13956 implements MigracaoVersaoInterface{
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update log set valorcampoanterior = 'SIM', valorcampoalterado = 'NÃO' where codigo in (select codigolog from (select chaveprimaria, count(l.codigo), min(l.codigo) as codigolog   from movproduto mp inner join  log l on  l.chaveprimaria = mp.codigo::text and l.nomeentidade = 'MOVPRODUTO' and nomecampo ilike 'RENOVAR PRODUTO AUTOMATICAMENTE%' where mp.renovavelautomaticamente = false group by 1 having count(l.codigo) = 1)  as foo) ;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update movproduto set renovavelautomaticamente = true where codigo in (select movproduto  from (select mp.pessoa,mp.codigo as movproduto , p.desativado,mp.datafinalvigencia, coalesce((select dataalteracao from log where chaveprimaria = p.codigo::text and nomeentidade = 'PRODUTO' and nomecampo = 'Renovável Automaticamente:' and valorcampoalterado = 'Sim' order by codigo desc limit 1 ), '2023-07-06') as dataalteracao ,mp.datalancamento from movproduto mp inner join produto p on p.codigo = mp.produto inner join vendaavulsa v on v.codigo = mp.vendaavulsa where p.renovavelautomaticamente and mp.renovavelautomaticamente = false and situacao <> 'CA' and (v.origemsistema = 17 or v.origemsistema is null)and not exists(select codigo  from log where chaveprimaria = mp.codigo::text and nomeentidade = 'MOVPRODUTO' and nomecampo ilike 'RENOVAR PRODUTO AUTOMATICAMENTE%' and valorcampoalterado = 'NÃO' order by codigo desc limit 1 )) as foo where foo.dataalteracao <= foo.datalancamento and datafinalvigencia > current_date  - interval  '15 days')", c);
        }
    }
}

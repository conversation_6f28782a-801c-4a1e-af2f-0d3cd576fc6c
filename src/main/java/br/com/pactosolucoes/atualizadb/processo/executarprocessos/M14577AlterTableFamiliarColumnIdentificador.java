package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Wenderson Reis",
        data = "07/03/2025",
        descricao = "Aumentar o tamanho da coluna identificador da tabela familiar para VARCHAR(20)",
        motivacao = "M1-4577")
public class M14577AlterTableFamiliarColumnIdentificador implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE familiar ALTER COLUMN identificador TYPE VARCHAR(20);",
                        c
            );
        }
    }
}

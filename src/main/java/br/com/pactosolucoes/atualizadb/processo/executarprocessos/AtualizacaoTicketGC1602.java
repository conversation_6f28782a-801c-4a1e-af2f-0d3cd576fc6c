
package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "27/02/2025",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-1602")
public class AtualizacaoTicketGC1602 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE acessocliente ADD COLUMN nomeCodEmpresaOrigem varchar(255);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE acessocliente ADD COLUMN nomeCpfEmailClienteOrigem varchar(255);", c);
        }
    }
}

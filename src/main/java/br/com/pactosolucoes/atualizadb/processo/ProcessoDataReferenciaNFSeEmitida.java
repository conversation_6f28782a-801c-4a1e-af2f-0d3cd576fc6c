/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


public class ProcessoDataReferenciaNFSeEmitida {

    public ProcessoDataReferenciaNFSeEmitida() {
    }

    public static void preencherDataReferenciaNFSeEmitida(Connection con) throws Exception {

        Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("preencherDataReferenciaNFSeEmitida - Início em : " + new Date());

        ResultSet rsEmpresa = SuperFacadeJDBC.criarConsulta("select codigo,nome,tipogestaonfse,mostrarnotapordiacompetencia,usardataoriginalcompensacaonfse from empresa", con);

        while (rsEmpresa.next()) {

            Integer codEmpresa = rsEmpresa.getInt("codigo");
            String nome = rsEmpresa.getString("nome");
            Integer tipogestaonfse = rsEmpresa.getInt("tipogestaonfse");
            boolean usardataoriginalcompensacaonfse = rsEmpresa.getBoolean("usardataoriginalcompensacaonfse");

            TipoRelatorioDF tipoRelatorioDF = TipoRelatorioDF.getTipoRelatorioDF(tipogestaonfse);

            System.out.println("PROCESSANDO EMPRESA: " + codEmpresa + " - " + nome + " TIPO EMISSÃO: " + tipoRelatorioDF);

            String sqlCompetencia = "update nfseemitida set datareferencia  = (\n" +
                    "select\n" +
                    "to_date('01/' || m.mesreferencia, 'DD/MM/YYYY')\n" +
                    "from nfseemitida n\n" +
                    "inner join movproduto m on m.codigo = n.movproduto\n" +
                    "and nfseemitida.codigo = n.codigo\n" +
                    "and nfseemitida.movproduto is not null\n" +
                    "and nfseemitida.datareferencia is null) where datareferencia is null";

            SuperFacadeJDBC.executarConsultaUpdate(sqlCompetencia, con);

            String sqlFaturamento = "update nfseemitida set datareferencia  = (\n" +
                    "select\n" +
                    "m.datalancamento\n" +
                    "from nfseemitida n\n" +
                    "inner join movproduto m on m.codigo = n.movproduto\n" +
                    "and nfseemitida.codigo = n.codigo\n" +
                    "and nfseemitida.movproduto is not null\n" +
                    "and nfseemitida.datareferencia is null) where datareferencia is null";

            SuperFacadeJDBC.executarConsultaUpdate(sqlFaturamento, con);

            StringBuilder sqlCartao = new StringBuilder();
            sqlCartao.append("update nfseemitida set datareferencia  = ( \n");
            sqlCartao.append("select \n");
            if (usardataoriginalcompensacaonfse) {
                sqlCartao.append("c.dataoriginal \n");
            } else {
                sqlCartao.append("c.datacompesancao \n");
            }
            sqlCartao.append("from nfseemitida n \n");
            sqlCartao.append("inner join cartaocredito c on c.codigo = n.cartaocredito \n");
            sqlCartao.append("and nfseemitida.codigo = n.codigo \n");
            sqlCartao.append("and nfseemitida.empresa = ").append(codEmpresa).append(" \n");
            sqlCartao.append("and nfseemitida.cartaocredito is not null \n");
            sqlCartao.append("and nfseemitida.datareferencia is null) where datareferencia is null");
            SuperFacadeJDBC.executarConsultaUpdate(sqlCartao.toString(), con);

            StringBuilder sqlCheque = new StringBuilder();
            sqlCheque.append("update nfseemitida set datareferencia  = ( \n");
            sqlCheque.append("select \n");
            if (usardataoriginalcompensacaonfse) {
                sqlCheque.append("c.dataoriginal \n");
            } else {
                sqlCheque.append("c.datacompesancao \n");
            }
            sqlCheque.append("from nfseemitida n \n");
            sqlCheque.append("inner join cheque c on c.codigo = n.cheque \n");
            sqlCheque.append("and nfseemitida.codigo = n.codigo \n");
            sqlCheque.append("and nfseemitida.empresa = ").append(codEmpresa).append(" \n");
            sqlCheque.append("and nfseemitida.cheque is not null \n");
            sqlCheque.append("and nfseemitida.datareferencia is null) where datareferencia is null");
            SuperFacadeJDBC.executarConsultaUpdate(sqlCheque.toString(), con);

            String sqlDinheiro = "update nfseemitida set datareferencia  = ( \n" +
                    "select \n" +
                    "m.datapagamento \n" +
                    "from nfseemitida n \n" +
                    "inner join movpagamento m on m.codigo = n.movpagamento \n" +
                    "and nfseemitida.codigo = n.codigo \n" +
                    "and nfseemitida.empresa = " + codEmpresa + " \n" +
                    "and nfseemitida.movpagamento is not null \n" +
                    "and nfseemitida.datareferencia is null) where datareferencia is null";
            SuperFacadeJDBC.executarConsultaUpdate(sqlDinheiro, con);


            String sqlFatuCaixa = "update nfseemitida set datareferencia  = (\n" +
                    "select\n" +
                    "r.data\n" +
                    "from nfseemitida n\n" +
                    "inner join recibopagamento r on r.codigo = n.recibopagamento\n" +
                    "and nfseemitida.codigo = n.codigo\n" +
                    "and nfseemitida.empresa = " + codEmpresa + " \n" +
                    "and nfseemitida.recibopagamento is not null\n" +
                    "and nfseemitida.datareferencia is null) where datareferencia is null";
            SuperFacadeJDBC.executarConsultaUpdate(sqlFatuCaixa, con);

        }

        String updateHistorico = "update nfseemitidahistorico set datareferencia  = (\n" +
                "select\n" +
                "n.datareferencia\n" +
                "from nfseemitida n\n" +
                "where n.codigo = nfseemitidahistorico.nfseemitida\n" +
                "and n.datareferencia is not null\n" +
                "and nfseemitidahistorico.datareferencia is null) where datareferencia is null";
        SuperFacadeJDBC.executarConsultaUpdate(updateHistorico, con);

        Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("Tempo Total preencherDataReferenciaNFSeEmitida: " + (d2.getTime() - d1.getTime()));

    }

    public static void main(String[] args) {
        try {

            Connection con = DriverManager.getConnection("*****************************************", "zillyonweb", "pactodb");
            preencherDataReferenciaNFSeEmitida(con);

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Marcos Andre",
        data = "18/03/2025",
        descricao = "Corrigi dados de trancamentos vencidos que ficaram errados",
        motivacao = "IN-1259")
public class CorrecaoDadosIN1259 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update contratooperacao set datafimefetivacaooperacao = datafimefetivacaooperacao - interval '1 day' where codigo in (select tv.codigo from contratooperacao rt inner join contratooperacao  tv on rt.contrato = tv.contrato and rt.tipooperacao = 'RT' and tv.tipooperacao = 'TV' and tv.datafimefetivacaooperacao = rt.datainicioefetivacaooperacao );", c);
        }
    }
}


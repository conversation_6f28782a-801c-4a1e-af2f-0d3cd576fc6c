/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.RoboControle;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.basico.MovimentoContaCorrenteClienteComposicaoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteClienteComposicao;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import java.util.List;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.utilitarias.Conexao;
/**
 *
 * <AUTHOR>
 */
public class CorrigirContratosSemClientes {
    public static void gravarDados(Connection con) throws SQLException, Exception{
                Conexao.guardarConexaoForJ2SE(con);
		Cliente clienteDao = new Cliente(con);
                ConfiguracaoSistema confDAO = new ConfiguracaoSistema(con);
                Empresa empresaDao = new Empresa(con);
                Pessoa pessoaDAO = new Pessoa(con);
                UsuarioVO usuario = new UsuarioVO();
                usuario.setCodigo(1);
                ConfiguracaoSistemaVO configuracaoSistemaVO = confDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
		Uteis.logar(null, "CorrigirContratosSemClientes  - início em : "+ new Date());
		StringBuilder sql = new StringBuilder();
		sql.append("select con.codigo as contrato,con.consultor as consultorcontrato,con.empresa,con.datalancamento, p.codigo as pessoaContrato, p.nome as nomeContrato,  cliatual.codigo as codigoCliente, ");
                sql.append("cliatual.codigomatricula, atual.codigo as pessoaCliente, atual.nome as nomeCliente, vi.colaborador as consultorcliente from contrato con inner join pessoa p on p.codigo = con.pessoa ");
                sql.append("left join cliente cli on cli.pessoa = con.pessoa  left join pessoa atual  on atual.nome = p.nome and atual.codigo <> p.codigo ");
                sql.append("left join cliente cliatual on cliatual.pessoa = atual.codigo left join vinculo vi on vi.cliente = cliatual.codigo and vi.tipovinculo = 'CO' where cli.codigo is null ");
		ResultSet dados = con.prepareStatement(sql.toString()).executeQuery();
		List<Integer> pessoasAjustas = new ArrayList<Integer>();
                String clienteAjustados = "";
                ClienteVO novoCliente = null;
                EmpresaVO emp = null;
                VinculoVO vinculoVO = null;
                EmpresaVO empresaVO = null;
		while(dados.next()){
                    if(!pessoasAjustas.contains(dados.getInt("pessoacontrato"))){
                        if(!UteisValidacao.emptyNumber(dados.getInt("codigocliente"))){

                            SuperFacadeJDBC.executarConsultaUpdate("update contrato set pessoa ="+dados.getInt("pessoacliente")+" where pessoa = "+dados.getInt("pessoacontrato")+";",con);
                            SuperFacadeJDBC.executarConsultaUpdate("update movproduto   set pessoa  = "+dados.getInt("pessoacliente")+" where pessoa = "+dados.getInt("pessoacontrato")+";",con);
                            SuperFacadeJDBC.executarConsultaUpdate("update movparcela   set pessoa  = "+dados.getInt("pessoacliente")+" where pessoa = "+dados.getInt("pessoacontrato")+";",con);
                            SuperFacadeJDBC.executarConsultaUpdate("update movpagamento   set pessoa   = "+dados.getInt("pessoacliente")+" where pessoa = "+dados.getInt("pessoacontrato")+";",con);
                            SuperFacadeJDBC.executarConsultaUpdate("update recibopagamento   set pessoapagador   = "+dados.getInt("pessoacliente")+" where pessoapagador = "+dados.getInt("pessoacontrato")+";",con);
                            SuperFacadeJDBC.executarConsultaUpdate("update log set pessoa  = "+dados.getInt("pessoacliente")+" where pessoa = "+dados.getInt("pessoacontrato")+";",con);
                            SuperFacadeJDBC.executarConsultaUpdate("update periodoacessocliente  set pessoa   = "+dados.getInt("pessoacliente")+" where pessoa = "+dados.getInt("pessoacontrato")+";",con);
                            SuperFacadeJDBC.executarConsultaUpdate("update telefone  set pessoa   = "+dados.getInt("pessoacliente")+" where pessoa = "+dados.getInt("pessoacontrato")+";",con);
                            SuperFacadeJDBC.executarConsultaUpdate("update email  set pessoa   = "+dados.getInt("pessoacliente")+" where pessoa = "+dados.getInt("pessoacontrato")+";",con);
                            SuperFacadeJDBC.executarConsultaUpdate("update endereco  set pessoa   = "+dados.getInt("pessoacliente")+" where pessoa = "+dados.getInt("pessoacontrato")+";",con);
                            SuperFacadeJDBC.executarConsultaUpdate("delete from pessoa  where codigo = "+dados.getInt("pessoacontrato")+";",con);
                            if(UteisValidacao.emptyNumber(dados.getInt("consultorcontrato")) && !UteisValidacao.emptyNumber(dados.getInt("consultorcliente"))){
                                SuperFacadeJDBC.executarConsultaUpdate("update contrato  set consultor   = "+dados.getInt("consultorcliente")+" where codigo = "+dados.getInt("contrato")+";",con);
                            }
                            clienteAjustados += ","+dados.getInt("codigomatricula");
                        } else {
                            PessoaVO pessoa = pessoaDAO.consultarPorChavePrimaria(dados.getInt("pessoacontrato"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            novoCliente = new ClienteVO();
                            novoCliente.setValidarDados(false);
                            novoCliente.setPessoa(pessoa);
                            if(!UteisValidacao.emptyNumber(dados.getInt("consultorcontrato"))){
                                vinculoVO = new VinculoVO();
                                vinculoVO.setTipoVinculo("CO");
                                vinculoVO.getColaborador().setCodigo(dados.getInt("consultorcontrato"));
                                novoCliente.getVinculoVOs().add(vinculoVO);
                            }
                            novoCliente.setEmpresa(empresaDao.consultarPorChavePrimaria(dados.getInt("empresa"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                            novoCliente.setSituacao("IN");
                            novoCliente.setUsuarioVO(usuario);
                            clienteDao.gerarNumeroMatricula(novoCliente, novoCliente.getEmpresa(), configuracaoSistemaVO);
                            clienteDao.incluirSemPessoa(novoCliente, configuracaoSistemaVO);
                            clienteAjustados += ","+novoCliente.getCodigoMatricula();
                        }
                        pessoasAjustas.add(dados.getInt("pessoacontrato"));
                    }
                   
		}
                RoboControle robo = new RoboControle();
                robo.setContratos("");
                robo.setApagarHistorioContratoAntes(true);
                robo.setMatriculas(clienteAjustados.replaceFirst(",", ""));
                robo.getRobo().setProcessarValidador(true); // isso define que o processamento do robo é soliciatado pelo verificador de inconsistencias
                robo.processarLote();
                SuperFacadeJDBC.executarConsultaUpdate("delete from situacaoclientesinteticodw  where codigo in (select sw.codigo from situacaoclientesinteticodw sw  left join pessoa p on p.codigo = sw.codigopessoa left join cliente c on c.codigo =sw.codigocliente where  p.codigo is null or c.codigo is null);",con);
                Uteis.logar(null, "Cliente ajustados: "+clienteAjustados);
                
		Uteis.logar(null, "CorrigirContratosSemClientes - fim em : "+ new Date());
	}
	
	public static void main(String... args) {
		try {
//            Connection con1 = DriverManager.getConnection("********************************************************************", "zillyonweb", "pactodb");
			Connection con1 = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "teste");
			gravarDados(con1);

		} catch (Exception ex) {
			Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
		}
	}
}

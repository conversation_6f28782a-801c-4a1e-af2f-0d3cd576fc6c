/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
/**
 *
 * <AUTHOR>
 */
public class AjustarResponsavelFreePass {

public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("**********************************************************************", "postgres", "pactodb");
           ajustarResponsavelFreePass(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarResponsavelFreePass(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select cli.responsavelfreepass, pa.codigo from cliente cli  inner join periodoacessocliente pa on pa.pessoa = cli.pessoa where cli.freepass IS NOT null and pa.codigo in (select max(codigo) from periodoacessocliente  where tipoacesso  = 'PL' and pessoa = cli.pessoa) and pa.responsavel is null;", con);

        while (consulta.next()) {
            
            String sql = "update periodoacessocliente set responsavel = ? where codigo = ?;";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);


            sqlAlterar.setInt(1, consulta.getInt("responsavelfreepass"));
            sqlAlterar.setInt(2, consulta.getInt("codigo"));
            sqlAlterar.execute();
        }
    }

}

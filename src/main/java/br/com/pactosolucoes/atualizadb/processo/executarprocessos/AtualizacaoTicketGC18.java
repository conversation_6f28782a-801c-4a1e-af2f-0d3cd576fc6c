package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "29/05/2025",
        descricao = "GC-18 Tabela para controle de replicação de plano de contas",
        motivacao = "GC-18 Tabela para controle de replicação de plano de contas")
public class AtualizacaoTicketGC18 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table configuracaofinanceiro add column bloquearCriacaoPlanoConta boolean default false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table public.planocontaredeempresa (codigo serial not null constraint planocontaredeempresa_pk primary key,\n" +
                    "planoconta integer,\n" +
                    "chaveOrigem varchar,\n" +
                    "chaveDestino varchar,\n" +
                    "empresaDestino integer,\n" +
                    "nomeunidade text,\n" +
                    "datacadastro timestamp,\n" +
                    "mensagemSituacao text,\n" +
                    "planocontareplicado integer,\n" +
                    "dataatualizacao timestamp);", c);
        }
    }
}

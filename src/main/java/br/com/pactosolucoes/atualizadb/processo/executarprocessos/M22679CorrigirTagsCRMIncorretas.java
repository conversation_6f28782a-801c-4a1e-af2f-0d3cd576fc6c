package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Wenderson Reis",
        data = "18/11/2024",
        descricao = "Correção de tags incorretas",
        motivacao = "M2-2679")
public class M22679CorrigirTagsCRMIncorretas implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE maladireta SET mensagem = REPLACE(mensagem, 'TAG_ENDERECO', 'ENDERECO_TAG') WHERE mensagem LIKE '%TAG_ENDERECO%';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE maladireta SET mensagem = REPLACE(mensagem, 'TAG_TELEFONE', 'TELEFONE_TAG') WHERE mensagem LIKE '%TAG_TELEFONE%';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE maladireta SET mensagem = REPLACE(mensagem, 'TAG_NOME_EMPRESA', 'NOME_EMPRESA') WHERE mensagem LIKE '%TAG_NOME_EMPRESA%';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE maladireta SET mensagem = REPLACE(mensagem, 'TAG_CIDADE_ESTADO', 'CIDADE_ESTADO_TAG') WHERE mensagem LIKE '%TAG_CIDADE_ESTADO%';", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE maladireta SET mensagem = REPLACE(mensagem, 'TAG_WEB_SITE', 'WEB_SITE_TAG') WHERE mensagem LIKE '%TAG_WEB_SITE%';", c);
        }
    }

}

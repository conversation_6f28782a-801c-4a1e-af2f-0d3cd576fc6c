package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.plano.Modalidade;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.integracao.TreinoWSConsumer;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Altera os contratos que foram vendidos quando a modalidade não havia crossfit e que foi habilitado pela tela nova.
 */
public class ProcessoAjustarCrossfitContrato {

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "espacotonmg";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            ajustarCrossfitContratos(c, chave);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAjustarCrossfitContrato.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarCrossfitContratos(Connection con, String chave) {
        try {
            Uteis.logarDebug("INÍCIO | ProcessoAjustarCrossfitContrato");
            StringBuilder sql = new StringBuilder("select\n" +
                    " con.codigo as contrato,\n" +
                    " cli.codigomatricula,\n" +
                    " cli.codigo,\n" +
                    " cli.pessoa,\n" +
                    " mod.crossfit\n" +
                    "from\n" +
                    " contrato con\n" +
                    "inner join cliente cli on\n" +
                    " con.pessoa = cli.pessoa\n" +
                    "inner join contratomodalidade conmod on\n" +
                    " conmod.contrato = con.codigo\n" +
                    "inner join modalidade mod on\n" +
                    " mod.codigo = conmod.modalidade\n" +
                    "where\n" +
                    " con.situacao = 'AT'\n" +
                    " and mod.codigo::text in (\n" +
                    "  select\n" +
                    "   chaveprimaria\n" +
                    "  from\n" +
                    "   log\n" +
                    "  where\n" +
                    "   nomeentidade = 'MODALIDADE'\n" +
                    "   and operacao = 'INCLUSÃO'\n" +
                    "   and dataalteracao >= '2024-11-01'\n" +
                    "   and chaveprimaria not in (\n" +
                    "   select\n" +
                    "    chaveprimaria\n" +
                    "   from\n" +
                    "    log\n" +
                    "   where\n" +
                    "    nomeentidade = 'MODALIDADE'\n" +
                    "    and operacao = 'EXCLUSÃO'\n" +
                    "    and nomeentidadedescricao = 'Modalidade'" +
                    "    and dataalteracao >= '2024-11-01')\n" +
                    "  order by\n" +
                    "   codigo desc) and mod.crossfit;");
            SituacaoClienteSinteticoDW sinteticoDW = new SituacaoClienteSinteticoDW(con);

            List<Integer> codContratosAlterados = new ArrayList<>();

            try (ResultSet rs = con.prepareStatement(sql.toString()).executeQuery()) {
                while (rs.next()) {
                    try {
                        codContratosAlterados.add(rs.getInt("contrato"));
                        sinteticoDW.atualizarInformacoesCrossfit(null, false, rs.getInt("pessoa"));
                        TreinoWSConsumer.atualizarCrossfit(chave, rs.getInt("codigomatricula"), rs.getBoolean("crossfit"));
                    } catch (Exception e) {
                        Uteis.logar(e, Modalidade.class);
                    }
                }
            }
            Uteis.logarDebug("Contratos afetados: " + codContratosAlterados);
            Uteis.logarDebug("FIM | ProcessoAjustarCrossfitContrato");
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoAjustarCrossfitContrato - " + ex.getMessage());
        }
    }
}

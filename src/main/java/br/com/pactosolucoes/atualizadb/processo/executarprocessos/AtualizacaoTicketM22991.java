package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "03/01/2025",
        descricao = "Nova coluna nova config do vendas",
        motivacao = "M2-2991")
public class AtualizacaoTicketM22991 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN permiteProsseguirMesmoCpfOuEmailCadastroVisitante BOOLEAN DEFAULT TRUE;", c);
        }
    }
}

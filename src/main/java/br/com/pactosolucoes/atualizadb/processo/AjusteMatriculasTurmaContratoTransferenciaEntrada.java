/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;

/**
 *
 * <AUTHOR>
 */
public class AjusteMatriculasTurmaContratoTransferenciaEntrada {

    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("***********************************************", "postgres", "pactodb");
            ajustarMatriculasTurmaContratoTransferenciaEntrada(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarMatriculasTurmaContratoTransferenciaEntrada(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select  c.contratoresponsavelrenovacaomatricula, c.codigo , c.vigenciaateajustada, e.toleranciaocupacaoturma "
                + " from contrato c inner join empresa e on c.empresa = e.codigo "
                + " where c.codigo in (select contrato from contratooperacao where tipooperacao = 'TE') and situacao = 'AT'", con);
        int cont = 1;
        MatriculaAlunoHorarioTurma mt = new MatriculaAlunoHorarioTurma(con);
        ContratoVO contrato = new ContratoVO();

        while (consulta.next()) {

            contrato.setCodigo(consulta.getInt("codigo"));
            contrato.setVigenciaAteAjustada(consulta.getDate("vigenciaateajustada"));
            List<MatriculaAlunoHorarioTurmaVO> lista = mt.consultarMatriculaAtiva(contrato.getCodigo(), negocio.comuns.utilitarias.Calendario.hoje());
            for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
                if (consulta.getInt("contratoresponsavelrenovacaomatricula") == 0) {
                    matricula.setDataFim(Uteis.somarDias(contrato.getVigenciaAteAjustada(), consulta.getInt("toleranciaocupacaoturma")));
                } else {
                    matricula.setDataFim(contrato.getVigenciaAteAjustada());
                }
                mt.alterarSemCommit(matricula);
            }
        }
    }
}

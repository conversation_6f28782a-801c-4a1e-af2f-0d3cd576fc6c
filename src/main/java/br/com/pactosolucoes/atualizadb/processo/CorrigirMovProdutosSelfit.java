package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by luiz on 01/03/2016.
 */
public class CorrigirMovProdutosSelfit {

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("******************************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);
            corrigirMovProdutosContratos(con);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void corrigirMovProdutosContratos(Connection con) {
        try {
            String sql = "select * from contrato where vigenciade = '2016-02-29 00:00:00'";
            ResultSet rscount = SuperFacadeJDBC.criarConsulta("SELECT COUNT(*) as t FROM (" + sql + ") as a", con);
            int total = rscount.next() ? rscount.getInt("t") : 0;
            int at = 0;
            MovProduto daoMovProduto = new MovProduto(con);
            MovParcela daoMovParcela = new MovParcela(con);
            Contrato daoContrato = new Contrato(con);
            Plano daoPlano = new Plano(con);
            PeriodoAcessoCliente daoAcesso = new PeriodoAcessoCliente(con);
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);

            while (rs.next()) {
                Date dataInicialNova = new SimpleDateFormat("dd/MM/yyyy").parse("10/03/2016");
                int diasDiferenca = (int) Uteis.nrDiasEntreDatas(rs.getDate("vigenciade"), dataInicialNova);
                ContratoVO contratoVO = Contrato.montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                PlanoVO planoVO = daoPlano.consultarPorChavePrimaria(contratoVO.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                contratoVO.setVigenciaDe(Uteis.somarDias(contratoVO.getVigenciaDe(), diasDiferenca));
                contratoVO.setVigenciaAte(Uteis.somarDias(contratoVO.getVigenciaAte(), diasDiferenca));
                contratoVO.setVigenciaAteAjustada(contratoVO.getVigenciaAte());
                daoContrato.alterarApenasDadosBasicos(contratoVO);

                //ALTERAR AS PARCELAS DO CONTRATO
                List<MovParcelaVO> parcelasContrato = daoMovParcela.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                int adesao = 0;
                int parcela = 0;
                for (MovParcelaVO movParcelaVO : parcelasContrato) {
                    if (movParcelaVO.getDescricao().contains("ANUIDADE")) {
                        Date dataAnuidade = new SimpleDateFormat("dd/MM/yyyy").parse("15/03/2016");
                        movParcelaVO.setDataVencimento(dataAnuidade);
                        movParcelaVO.setDataCobranca(dataAnuidade);
                    } else if (movParcelaVO.getDescricao().contains("ADESÃO")) {
                        Date dataAdesao = Uteis.somarCampoData(dataInicialNova, Calendar.MONTH, adesao);
                        movParcelaVO.setDataVencimento(dataAdesao);
                        movParcelaVO.setDataCobranca(dataAdesao);
                        adesao++;
                    } else {
                        Date dataParcela = Uteis.somarCampoData(dataInicialNova, Calendar.MONTH, parcela);
                        movParcelaVO.setDataVencimento(dataParcela);
                        movParcelaVO.setDataCobranca(dataParcela);
                        parcela++;
                    }
                    daoMovParcela.alterar(movParcelaVO);
                }


                //ALTERAR OS PRODUTOS DO CONTRATO
                List<MovProdutoVO> produtosContrato = daoMovProduto.consultarPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                for (MovProdutoVO movProdutoVO : produtosContrato) {
                    if (movProdutoVO.getDataInicioVigencia() != null) {
                        movProdutoVO.setDataInicioVigencia(Uteis.somarDias(movProdutoVO.getDataInicioVigencia(), diasDiferenca));
                        movProdutoVO.setDataFinalVigencia(Uteis.somarDias(movProdutoVO.getDataFinalVigencia(), diasDiferenca));
                    }

                    if (movProdutoVO.getProduto().getCodigo().equals(9)) {
                        movProdutoVO.setDescricao(planoVO.getDescricao() + " - " + movProdutoVO.getMesReferencia());
                    }

                    if (movProdutoVO.getMesReferencia().equals("02/2016")) {
                        if (movProdutoVO.getProduto().getCodigo().equals(9)) {
                            Integer ano = 2017;
                            String mes = "02";
                            movProdutoVO.setMesReferencia(mes + "/" + ano);
                            movProdutoVO.setAnoReferencia(ano);
                            movProdutoVO.setDescricao(planoVO.getDescricao() + " - " + mes + "/" + ano);
                        } else {
                            Integer ano = 2016;
                            String mes = "03";
                            movProdutoVO.setMesReferencia(mes + "/" + ano);
                            movProdutoVO.setAnoReferencia(ano);
                        }
                    }
                    daoMovProduto.alterar(movProdutoVO);
                }

                //ALTERAR O PERIODO DE ACESSO
                List<PeriodoAcessoClienteVO> acessoContrato = daoAcesso.consultarPorContrato(contratoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                for (PeriodoAcessoClienteVO acessoClienteVO : acessoContrato) {
                    acessoClienteVO.setDataInicioAcesso(Uteis.somarDias(acessoClienteVO.getDataInicioAcesso(), diasDiferenca));
                    acessoClienteVO.setDataFinalAcesso(Uteis.somarDias(acessoClienteVO.getDataFinalAcesso(), diasDiferenca));
                    daoAcesso.alterar(acessoClienteVO);
                }

                at++;
                System.out.println(at + "/" + total + " - Contrato: " + contratoVO.getCodigo());

            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
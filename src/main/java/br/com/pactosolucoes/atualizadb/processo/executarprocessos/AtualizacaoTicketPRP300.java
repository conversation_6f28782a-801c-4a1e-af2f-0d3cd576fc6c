package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Victor Augusto Machado",
        data = "23/01/2025",
        descricao = "PRPI-300 Adicionar suporte para envio de contextos de todas as empresas da rede nos endpoints da API de integração com a IA Pacto Conversas",
        motivacao = "PRPI-300")
public class AtualizacaoTicketPRP300 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdate("ALTER TABLE public.configuracaocrmia\n" +
                    "    ADD COLUMN matriz boolean NULL,\n" +
                    "    ADD COLUMN chavematriz varchar(255),\n" +
                    "    ADD COLUMN nomematriz varchar(255),\n" +
                    "    ADD COLUMN habilitarConfigParaRedeAcademias boolean DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdate("CREATE TABLE public.configuracaoredeia (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    chavebancomatriz VARCHAR(255),\n" +
                    "    tipoconfigrede VARCHAR(255),\n" +
                    "    codigounidadematriz INTEGER);", c);
        }
    }
}

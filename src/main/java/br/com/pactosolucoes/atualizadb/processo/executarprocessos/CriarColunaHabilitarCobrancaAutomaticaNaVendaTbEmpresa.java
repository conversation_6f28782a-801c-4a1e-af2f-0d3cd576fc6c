package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "23/09/2024",
        descricao = "Cria a coluna habilitarCobrancaAutomaticaNaVenda na tabela Empresa",
        motivacao = "GC-461: Cobrança da parcela do aluno na conclusão do contrato - Venda rápida")
public class CriarColunaHabilitarCobrancaAutomaticaNaVendaTbEmpresa implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        String sql = "ALTER TABLE Empresa ADD COLUMN habilitarCobrancaAutomaticaNaVenda BOOLEAN DEFAULT FALSE;";
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 09/10/2024
 */

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "09/10/2024",
        descricao = "Nova tabela para gaurdar informações relevantes das requests para futuras análises",
        motivacao = "M2-2358")
public class M22358NovaTabelaDetalhesRequest implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.detalhesRequest (codigo SERIAL NOT NULL,\n" +
                    "dataRegistro TIMESTAMP WITHOUT TIME ZONE,\n" +
                    "url VARCHAR,\n" +
                    "statusResponse INT,\n" +
                    "tempoRequisicaoMs BIGINT,\n" +
                    "sucesso BOOLEAN DEFAULT FALSE,\n" +
                    "nomeTabelaForeignKey VARCHAR,\n" +
                    "codigoTabelaForeignKey INT);", c);
        }
    }
}

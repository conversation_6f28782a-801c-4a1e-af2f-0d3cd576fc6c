package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class ProcessoCorrecaoProdutosDuplicadosPlano {

    public static void corrigirProdutosDuplicadosDuplicadosPlano(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT prod.tipoproduto AS tipoProd, pps.plano AS codPlano, prod.codigo\n");
        sql.append("FROM planoprodutosugerido pps\n");
        sql.append("INNER JOIN produto prod ON pps.produto = prod.codigo\n");
        sql.append("INNER JOIN plano pln ON pln.codigo = pps.plano\n");
        sql.append("WHERE (pln.ingressoate > NOW () or pln.vigenciaate > NOW())\n");
        sql.append("AND prod.desativado IS FALSE\n");
        sql.append("AND prod.tipoproduto IN ('MA', 'RE', 'RN','TA', 'TD')\n");
        sql.append("GROUP BY prod.tipoproduto, pps.plano, prod.codigo HAVING count(prod.tipoproduto) > 1;");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();

        Uteis.debug = true;
        try {
            Uteis.logar("INÍCIO | ProcessoCorrecaoProdutosDuplicadosPlano");
            while (rs.next()) {
                try {
                    String tipoProduto = rs.getString("tipoProd");
                    int codigoPlano = rs.getInt("codPlano");
                    corrigirPlanoProdutoSugerido(tipoProduto, codigoPlano, con);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex){
            ex.printStackTrace();
        } finally {
            Uteis.logar("FIM | ProcessoCorrecaoProdutosDuplicadosPlano");
        }
    }

    public static void corrigirPlanoProdutoSugerido(String tipoProduto, int codigoPlano, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();

        // planoprodutosugerido que será excluído
        sql.append("SELECT pps.codigo AS codigopps FROM planoprodutosugerido pps\n");
        sql.append("INNER JOIN produto prod ON pps.produto = prod.codigo\n");
        sql.append("WHERE pps.plano = ").append(codigoPlano).append("\n");
        sql.append("AND prod.tipoproduto = '").append(tipoProduto).append("'\n");
        sql.append("ORDER BY pps.valorproduto\n");
        sql.append("LIMIT 1;\n");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        if(rs.next()) {
            int codigoPlanoProdutoSugerido = rs.getInt("codigopps");
            boolean existeContratoComProdutoSugerido = SuperFacadeJDBC.existe(" SELECT codigo FROM contratoplanoprodutosugerido WHERE planoprodutosugerido = " + codigoPlanoProdutoSugerido, con);
            if (existeContratoComProdutoSugerido){
                // Obtem o planoprodutosugerido que irá substituir os demais
                String sqlAux = "SELECT pps.codigo AS codigopps FROM planoprodutosugerido pps INNER JOIN produto prod ON pps.produto = prod.codigo WHERE pps.plano = " + codigoPlano + " AND prod.tipoproduto = '" + tipoProduto + "' AND pps.codigo <> " + codigoPlanoProdutoSugerido + " ORDER BY pps.valorproduto DESC LIMIT 1;";
                PreparedStatement stmAux = con.prepareStatement(sqlAux);
                ResultSet rsAux = stmAux.executeQuery();
                if(rsAux.next()) {
                    int codigoPlanoProdutoSugeridoSubstituto = rsAux.getInt("codigopps");
                    corrigirContratoComProdutoSugerido(codigoPlanoProdutoSugerido, codigoPlanoProdutoSugeridoSubstituto, con);
                }
            }
            String sqlDelete = "DELETE FROM planoprodutosugerido WHERE codigo = " + codigoPlanoProdutoSugerido;
            SuperFacadeJDBC.executarConsulta(sqlDelete, con);
        } else {
            Uteis.logar("Não foi possivel excluir os produto duplicados do plano " + codigoPlano);
        }
    }

    public static void corrigirContratoComProdutoSugerido(int codigoPlanoProdutoSugerido, int codigoPlanoProdutoSugeridoSubstituto, Connection con) throws Exception {
        String sql = "UPDATE contratoplanoprodutosugerido SET planoprodutosugerido =" + codigoPlanoProdutoSugeridoSubstituto + " WHERE planoprodutosugerido = " + codigoPlanoProdutoSugerido;
        SuperFacadeJDBC.executarUpdate(sql, con);
    }

}

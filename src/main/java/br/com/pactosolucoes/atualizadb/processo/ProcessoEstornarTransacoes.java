/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.apf.AprovaFacilService;

/**
 *
 * <AUTHOR>
 */
public class ProcessoEstornarTransacoes {

    Connection con = null;
    String inicio = null;
    String fim = null;
    String url = null;

    public ProcessoEstornarTransacoes(Connection con, String inicio, String fim, String url){
        this.con = con;
        this.inicio = inicio;
        this.fim = fim;
        this.url = url;
        try {
            Conexao.guardarConexaoForJ2SE(con);
        } catch (SQLException ex) {
            ex.printStackTrace();
        }
    }

    public void limparCupomFiscal() throws SQLException{
           con.prepareStatement("update cupomfiscal set horaemissao = null, statusimpressao = 0 where recibo in("+
                        "select recibopagamento from transacao  where paramsenvio like "+
                     "'%UrlRequest="+url+"' "+
                     "AND dataprocessamento BETWEEN '"+inicio+"' AND '"+fim+"' "+
                     "AND recibopagamento IS NOT NULL "+
                     "ORDER BY dataprocessamento)").execute();
    }

    public List<TransacaoVO> consultarTransacoes() throws Exception{
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(con);
            ResultSet rs = con.prepareStatement("select * from transacao  where paramsenvio like "
                    + " '%UrlRequest=" + url + "' "
                    + " AND dataprocessamento BETWEEN '" + inicio + "' AND '" + fim + "' "
                    + " AND recibopagamento IS NOT NULL "
                    + " ORDER BY dataprocessamento").executeQuery();
            return transacaoDAO.montarDadosConsulta(rs, con);
        } finally {
            transacaoDAO = null;
        }
    }

    public String consultarParcelas(Integer recibo) throws Exception{
        ResultSet rs = con.prepareStatement("SELECT ARRAY_TO_STRING(ARRAY(select distinct(movparcela) "
                + "from movprodutoparcela  where recibopagamento  =  "+recibo+"), ',') as parcelas ").executeQuery();
        return rs.next() ? rs.getString("parcelas") : "";
    }

    public void processarEstornoTransacoes(List<TransacaoVO> lista) throws Exception{
        AprovaFacilService aprovaFacilService = new AprovaFacilService(con);
        limparCupomFiscal();
        for(TransacaoVO transacao : lista){
            String parcelas = consultarParcelas(transacao.getReciboPagamento());
            aprovaFacilService.estornarRecibo(transacao, false);
            if(!UteisValidacao.emptyString(parcelas)){
                con.prepareStatement("UPDATE movparcela SET nrtentativas = 0, situacao = 'EA' WHERE codigo IN ("+parcelas+") ").execute();
                con.prepareStatement("UPDATE movproduto SET situacao = 'EA' WHERE codigo IN ("
                        + " SELECT movproduto FROM movprodutoparcela WHERE movparcela IN ("+parcelas+")) ").execute();
            }
        }
    }

    public void processar(){
        try{
            processarEstornoTransacoes(consultarTransacoes());
        }catch(Exception e){
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        try {
            Connection con1 = DriverManager.getConnection("****************************************", "zillyonweb", "pactodb");
            ProcessoEstornarTransacoes processo = new ProcessoEstornarTransacoes(con1, "2013-11-01 11:00:00", "2013-11-04 12:00:00",
                    "https://teste.aprovafacil.com/cgi-bin/APFW/pactosolucoes/APC%");
            processo.processar();
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

//        public static void main(String... args) {
//        try {
//            Connection c = new DAO().obterConexaoEspecifica(args[0]);
//            Conexao.guardarConexaoForJ2SE(c);
//            ProcessoEstornarTransacoes processo = new ProcessoEstornarTransacoes(c, "2013-11-01 11:00:00", "2013-11-04 12:00:00",
//                    "https://teste.aprovafacil.com/cgi-bin/APFW/pactosolucoes/APC%");
//            processo.processar();
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//    }

}

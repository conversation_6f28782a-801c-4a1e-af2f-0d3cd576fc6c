package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "02/10/2024",
        descricao = "Integração E-commerce PagBank",
        motivacao = "M2-2506")
public class M22506IntegracaoPagBank implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE operadoracartao ADD COLUMN codigoIntegracaoPagBank Integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE transacao ADD COLUMN codigoexterno2 Text;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE convenioCobranca ADD COLUMN refreshToken Text;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE convenioCobranca ADD COLUMN dataGeracaoAccessToken TIMESTAMP WITHOUT TIME ZONE;", c);
        }
    }
}

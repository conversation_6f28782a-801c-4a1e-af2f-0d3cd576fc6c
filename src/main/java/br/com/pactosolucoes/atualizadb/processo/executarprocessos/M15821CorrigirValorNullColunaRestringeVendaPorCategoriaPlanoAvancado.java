package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Wenderson",
        data = "03/06/2025",
        descricao = "Processo para salvar false onde está [NULL] na coluna restringevendaporcategoria do plano avançado. ",
        motivacao = "Ticket M1-5821, Corrigir dados com valor incorreto.")
public class M15821CorrigirValorNullColunaRestringeVendaPorCategoriaPlanoAvancado implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update plano set restringevendaporcategoria = false where restringevendaporcategoria is null;", c);
        }
    }
}

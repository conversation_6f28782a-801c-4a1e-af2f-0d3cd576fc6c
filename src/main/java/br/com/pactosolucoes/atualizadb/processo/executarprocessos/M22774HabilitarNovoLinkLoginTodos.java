package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "21/11/2024",
        descricao = "Habilitar coluna que envia novo link de login pra todos usuários.",
        motivacao = "M2-2774")
public class M22774HabilitarNovoLinkLoginTodos implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE ConfiguracaoSistema set forcarNovoLogin = true", c);
        }
    }
}

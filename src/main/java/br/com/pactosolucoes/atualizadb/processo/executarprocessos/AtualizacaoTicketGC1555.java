package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Franco Alvarez",
        data = "10/06/2025",
        descricao = "Adicionar coluna qtddiascontinuaremabertocancelauto na tabela PlanoRecorrencia",
        motivacao = "GC-1555")
public class AtualizacaoTicketGC1555 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE planorecorrencia ADD COLUMN qtddiascontinuaremabertocancelauto int4;", c);
        }
    }
}
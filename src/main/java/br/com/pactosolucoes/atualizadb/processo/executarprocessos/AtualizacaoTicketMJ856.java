package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luiz Felipe",
        data = "30/04/2025",
        descricao = "MJ-856 - Novo caixa em aberto",
        motivacao = "MJ-856 - Novo caixa em aberto")
public class AtualizacaoTicketMJ856 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movparcela ADD COLUMN datacriacao TIMESTAMP WITHOUT TIME ZONE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE movparcela ALTER COLUMN datacriacao SET DEFAULT now();", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX ch_movparcela_datacriacao ON public.movparcela USING btree (datacriacao);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE movparcela SET datacriacao = dataregistro WHERE datacriacao is null;", c);
        }
    }
}

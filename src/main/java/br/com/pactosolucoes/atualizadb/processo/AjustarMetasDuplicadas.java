/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.crm.FecharMeta;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;

/**
 *
 * <AUTHOR>
 */
public class AjustarMetasDuplicadas {
     public static void main(String... args) {
        try {
                Connection con1 = DriverManager.getConnection("*******************************************************", "postgres", "pactodb");
                apagarAlunosDuplicados(con1);

            } catch (Exception ex) {
                Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
            }
    }

    public static void apagarAlunosDuplicados(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select cliente,fecharmeta, count(cliente) as repeticoes, max(codigo) as ultimo, min(codigo) as primeiro from fecharmetadetalhado   group by cliente, fecharmeta having count(cliente) > 1", con);
        int cont = 0;
        FecharMeta fmDao = new FecharMeta(con);
        FecharMetaDetalhado detalhadoDao = new FecharMetaDetalhado(con);
        FecharMetaDetalhadoVO detalhado = null;
        FecharMetaVO fecharMeta= null;
        
        while (consulta.next()) {
            detalhado = detalhadoDao.consultarPorChavePrimaria(consulta.getInt("ultimo"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if(!UteisValidacao.emptyNumber(detalhado.getHistoricoContatoVO().getCodigo())){
                detalhado = detalhadoDao.consultarPorChavePrimaria(consulta.getInt("primeiro"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }
            fecharMeta = fmDao.consultarPorChavePrimaria(consulta.getInt("fecharMeta"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if(!fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla()) && !fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())){
                System.out.println(++cont + " - cliente " + consulta.getInt("cliente") + " ficou duplicado na meta " + fecharMeta.getIdentificadorMeta_Apresentar() +" no dia "+ fecharMeta.getDataRegistro_Apresentar());
                fecharMeta.setMeta(fecharMeta.getMeta() - 1);

                if(!UteisValidacao.emptyNumber(detalhado.getHistoricoContatoVO().getCodigo())){
                    if(fecharMeta.getMetaAtingida() > 0.0){
                        fecharMeta.setMetaAtingida(fecharMeta.getMetaAtingida() - 1);
                    } else if(fecharMeta.getRepescagem() > 0.0){
                        fecharMeta.setRepescagem(fecharMeta.getRepescagem() - 1);
                    } 
                }
                fecharMeta.calcularPorcentagem();
                fmDao.alteraSemSubordinada(fecharMeta);
                // exclui o registro detalhado
               detalhadoDao.excluir(detalhado);
            }
        }
    }
}

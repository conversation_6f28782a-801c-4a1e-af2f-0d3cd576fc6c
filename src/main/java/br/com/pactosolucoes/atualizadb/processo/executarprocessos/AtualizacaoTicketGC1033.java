package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "09/10/2024",
        descricao = "Adicionar config empresa utilizaConfigCancelamentoSesc e habilitar nas empresa que possuem configuracao sesc",
        motivacao = "GC1033")
public class AtualizacaoTicketGC1033 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN utilizaConfigCancelamentoSesc BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa SET utilizaConfigCancelamentoSesc = TRUE FROM configuracaosistema cfg \n" +
                    " WHERE cfg.sesc IS TRUE;", c);
        }
    }
}

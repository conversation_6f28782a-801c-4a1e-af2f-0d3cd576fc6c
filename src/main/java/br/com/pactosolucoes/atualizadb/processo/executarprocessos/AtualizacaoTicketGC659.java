package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "27/06/2024",
        descricao = "Incluir tabela para manter info de qual produto pode ser devolvido no cancelamento por empresa",
        motivacao = "GC-659")
public class AtualizacaoTicketGC659 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("create table public.ProdutoDevolverCancelamentoEmpresa (" +
                    "codigo serial not null constraint produtodevolvercancelamentoempresa_pk primary key,\n" +
                    "produto integer constraint produtodevolvercancelamentoempresa_produto_codigo_fk references public.produto,\n" +
                    "empresa integer constraint produtodevolvercancelamentoempresa_empresa_codigo_fk references public.empresa);", c);
        }
    }
}

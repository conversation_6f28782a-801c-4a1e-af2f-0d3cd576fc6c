package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Vinicius Franca",
        data = "31/07/2025",
        descricao = "Controle de prdutos por credito",
        motivacao = "GCM-312"
)
public class AtualizacaoTicket_GCM_312 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE produto ADD COLUMN habilitacontrolecreditos BOOLEAN DEFAULT FALSE;",
                    c
            );
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE produto ADD COLUMN toleranciaacesso INT DEFAULT 0;",
                    c
            );            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "COMMENT ON COLUMN produto.toleranciaacesso IS 'Tolerância de acesso em minutos';",
                    c
            );
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE TABLE public.controlecreditoproduto (" +
                            "codigo serial4 NOT NULL," +
                            "pessoa int4 NOT NULL," +
                            "produto int4 NOT NULL," +
                            "tipo_operacao int2 NOT NULL," +
                            "quantidade int4 NOT NULL," +
                            "descricao varchar(255) NULL," +
                            "datalancamento timestamp DEFAULT now() NOT NULL," +
                            "CONSTRAINT controlecreditoproduto_pkey PRIMARY KEY (codigo)" +
                            ");",
                    c
            );
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE INDEX controlecreditoproduto_pessoa_idx ON public.controlecreditoproduto USING btree (pessoa);",
                    c
            );
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE INDEX controlecreditoproduto_produto_idx ON public.controlecreditoproduto USING btree (produto);",
                    c
            );
        }
    }
}

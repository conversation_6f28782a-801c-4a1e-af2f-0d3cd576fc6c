/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import controle.arquitetura.RoboControle;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

/**
 *
 * <AUTHOR>
 */
public class CorrigirSituacaoTrancadoVencido {

    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("**********************************************", "postgres", "pactodb");
            corrigirSituacaoContratoTrancadoVencido(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void corrigirSituacaoContratoTrancadoVencido(Connection con) throws Exception {
        ZillyonWebFacade zw = new ZillyonWebFacade(con);
        Cliente cli = new Cliente(con);

        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select h.codigo as his,h.contrato,cl.codigo as cliente, p.nome, cl.situacao,cl.codigomatricula from historicocontrato h inner join contrato c on c.codigo = h.contrato "
                + " inner join cliente cl on c.pessoa = cl.pessoa "
                + " inner join pessoa p on p.codigo = c.pessoa where h.codigo in "
                + " (select codigo from historicocontrato where historicocontrato.contrato = c.codigo order by codigo desc limit 1) and h.tipohistorico = 'TR'  and cl.situacao = 'AT' ", con);

        String sql = "update contratooperacao set datafimefetivacaooperacao  = ? where codigo in "
                + " (select co.codigo from contratooperacao co inner join contrato con on co.contrato = con.codigo where  contrato in  "
                + " (select h.contrato from historicocontrato h inner join contrato c on c.codigo = h.contrato "
                + " inner join cliente cl on c.pessoa = cl.pessoa "
                + " inner join pessoa p on p.codigo = c.pessoa where h.codigo in "
                + " (select codigo from historicocontrato where historicocontrato.contrato = c.codigo order by codigo desc limit 1) and h.tipohistorico = 'TR'  and cl.situacao = 'AT') "
                + " and co.codigo in (select codigo from contratooperacao  where contratooperacao.contrato = con.codigo and tipooperacao = 'TV' order by codigo desc limit 1));";

        PreparedStatement sqlAlterar = con.prepareStatement(sql);

        sqlAlterar.setDate(1, Uteis.getDataJDBC(Calendario.hoje()));
        sqlAlterar.execute();

        int cont = 1;

        while (consulta.next()) {

            System.out.println(++cont + " - Aluno " + consulta.getString("nome") + " com o contrato  " + consulta.getInt("contrato") + " foi ajustado para trancado vencido.");

            sql = "update contrato set situacao = ? where codigo = ? ;";
            PreparedStatement sqlAlterarCont = con.prepareStatement(sql);

            sqlAlterarCont.setString(1, "TR");
            sqlAlterarCont.setInt(2, consulta.getInt("contrato"));
            sqlAlterarCont.execute();

            ClienteVO cliente = cli.consultarPorChavePrimaria(consulta.getInt("cliente"),Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            zw.atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, true,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        }
//
    }
}

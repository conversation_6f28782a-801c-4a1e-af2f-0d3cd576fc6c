package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "17/10/2024",
        descricao = "coluna documento e documento extensao na compra",
        motivacao = "GC-1040")
public class AtualizacaoTicketGC1040 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE COMPRA ADD COLUMN documento varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE COMPRA ADD COLUMN documentoExtensao varchar;", c);
        }
    }
}
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.impl.apf.APF;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 06/03/2020
 */
public class PovoadorDataInclusaoAutorizacaoCobranca {

    private Connection con;

    public PovoadorDataInclusaoAutorizacaoCobranca(Connection con) {
        this.con = con;
    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "INICIO | PovoadorDataInclusaoAutorizacaoCobranca...");

            String chave = "teste";
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }

            Connection conOAMD = Conexao.obterConexaoBancoEmpresas();
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT chave from empresa \n");
            sql.append("WHERE 1 = 1 \n");
            if (!UteisValidacao.emptyString(chave)) {
                sql.append(" AND chave = '").append(chave).append("'");
            }

            PreparedStatement st = conOAMD.prepareStatement(sql.toString());
            ResultSet rs = st.executeQuery();
            while (rs.next()) {
                chave = rs.getString("chave");
                Connection con = null;
                try {
                    Uteis.logar(null, "Obter conexão para chave: " + chave);
                    con = new DAO().obterConexaoEspecifica(chave);
                    PovoadorDataInclusaoAutorizacaoCobranca processo = new PovoadorDataInclusaoAutorizacaoCobranca(con);
                    processo.atualizarTabelas();
                    processo.atualizarAutorizacoesClientes();
                    processo.atualizarAutorizacoesColaborador();
                    processo = null;
                } catch (Exception ex) {
                    Uteis.logar(null, "Erro chave: " + chave);
                    ex.printStackTrace();
                } finally {
                    if (con != null) {
                        con.close();
                    }
                }
            }

            Uteis.logar(true, null, "FIM | PovoadorDataInclusaoAutorizacaoCobranca...");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void atualizarAutorizacoesClientes() {
        Integer sucesso = 0;
        Integer falha = 0;
        Integer total = 0;
        try {
            Uteis.logar(true, null, "INICIO | AtualizarAutorizacao de Clientes...");

            String sql = "select codigo, numerocartao, tokenaragorn, cartaomascaradointerno from autorizacaocobrancacliente  where coalesce(cartaomascaradointerno, '') = '' and tipoautorizacao  = " + TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId();

            total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            int i = 0;

            if (total <= 0) {
                return;
            }

            AragornService service = new AragornService();

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                Integer autorizacao = 0;
                try {
                    con.setAutoCommit(false);

                    Uteis.logar(true, null, "Auto Cliente... " + ++i + "/" + total);

                    autorizacao = rs.getInt("codigo");
                    String numerocartao = rs.getString("numerocartao");
                    String tokenaragorn = rs.getString("tokenaragorn");

                    if (!UteisValidacao.emptyString(numerocartao)) {
                        numerocartao = APF.getCartaoMascarado(numerocartao);
                    } else if (!UteisValidacao.emptyString(tokenaragorn)) {
                        NazgDTO dto = service.obterNazg(tokenaragorn);
                        numerocartao = APF.getCartaoMascarado(dto.getCard());
                    }


                    if (!UteisValidacao.emptyString(numerocartao) && numerocartao.contains("***")) {
                        SuperFacadeJDBC.executarUpdate("update autorizacaocobrancacliente set cartaomascaradointerno = '" + numerocartao + "' where codigo = " + autorizacao, con);
                    } else {
                        throw new Exception("Cartão mascarado não identificado.");
                    }

                    ++sucesso;
                    con.commit();
                } catch (Exception ex) {
                    ++falha;
                    con.rollback();
                    ex.printStackTrace();
                    Uteis.logar(true, null, "AutorizacaoCliente :" + autorizacao + " | " + ex.getMessage());
                } finally {
                    con.setAutoCommit(true);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            Uteis.logar(true, null, "FIM | AtualizarAutorizacao de Clientes...");
        }

        Uteis.logar(true, null,"Total " + total);
        Uteis.logar(true, null,"Sucesso " + sucesso);
        Uteis.logar(true, null,"Falha " + falha);
    }

    public void atualizarAutorizacoesColaborador() {
        Integer sucesso = 0;
        Integer falha = 0;
        Integer total = 0;
        try {
            Uteis.logar(true, null, "INICIO | AtualizarAutorizacao de Colaborador...");

            String sql = "select codigo, numerocartao, tokenaragorn, cartaomascaradointerno from autorizacaocobrancacolaborador  where coalesce(cartaomascaradointerno, '') = '' and tipoautorizacao  = " + TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId();

            total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            int i = 0;

            if (total <= 0) {
                return;
            }

            AragornService service = new AragornService();

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                Integer autorizacao = 0;
                try {
                    con.setAutoCommit(false);

                    Uteis.logar(true, null, "Auto Colaborador... " + ++i + "/" + total);

                    autorizacao = rs.getInt("codigo");
                    String numerocartao = rs.getString("numerocartao");
                    String tokenaragorn = rs.getString("tokenaragorn");

                    if (!UteisValidacao.emptyString(numerocartao)) {
                        numerocartao = APF.getCartaoMascarado(numerocartao);
                    } else if (!UteisValidacao.emptyString(tokenaragorn)) {
                        NazgDTO dto = service.obterNazg(tokenaragorn);
                        numerocartao = APF.getCartaoMascarado(dto.getCard());
                    }

                    if (!UteisValidacao.emptyString(numerocartao) && numerocartao.contains("***")) {
                        SuperFacadeJDBC.executarUpdate("update autorizacaocobrancacolaborador set cartaomascaradointerno = '" + numerocartao + "' where codigo = " + autorizacao, con);
                    } else {
                        throw new Exception("Cartão mascarado não identificado.");
                    }

                    ++sucesso;
                    con.commit();
                } catch (Exception ex) {
                    ++falha;
                    con.rollback();
                    ex.printStackTrace();
                    Uteis.logar(true, null, "AutorizacaoColaborador :" + autorizacao + " | " + ex.getMessage());
                } finally {
                    con.setAutoCommit(true);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            Uteis.logar(true, null, "FIM | AtualizarAutorizacao de Colaborador...");
        }

        Uteis.logar(true, null,"Total " + total);
        Uteis.logar(true, null,"Sucesso " + sucesso);
        Uteis.logar(true, null,"Falha " + falha);
    }

    public void atualizarTabelas() {
        try {
            Uteis.logar(true, null, "Inicio | PovoadorDataInclusaoAutorizacaoCobranca - AtualizarTabelas...");
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacliente ADD COLUMN dataRegistro TIMESTAMP DEFAULT NOW();", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacolaborador ADD COLUMN dataRegistro TIMESTAMP DEFAULT NOW();", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE autorizacaocobrancacliente SET dataRegistro = NULL;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE autorizacaocobrancacolaborador SET dataRegistro = NULL;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_log_pessoa ON log USING btree (pessoa);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX autorizacaocobrancacliente_cartaomascaradointerno_trgm_idx ON autorizacaocobrancacliente USING gin(cartaomascaradointerno gin_trgm_ops);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX autorizacaocobrancacolaborador_cartaomascaradointerno_trgm_idx ON autorizacaocobrancacolaborador USING gin(cartaomascaradointerno gin_trgm_ops);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE autorizacaocobrancacliente SET dataregistro = (select \n" +
                    "(select dataalteracao from log where pessoa = cl.pessoa and chaveprimaria::integer = au.codigo and operacao ilike 'INCLUS%' and nomeentidade  = 'AUTORIZACAOCOBRANCACLIENTE' order by codigo limit 1) as datainclusao\n" +
                    "from autorizacaocobrancacliente au \n" +
                    "inner join cliente cl on cl.codigo = au.cliente\n" +
                    "where autorizacaocobrancacliente.codigo = au.codigo) WHERE dataregistro is null", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE autorizacaocobrancacolaborador set dataregistro = (select \n" +
                    "(select dataalteracao from log where pessoa = co.pessoa and chaveprimaria::integer = au.codigo and operacao ilike 'INCLUS%' and nomeentidade  = 'AUTORIZACAOCOBRANCACOLABORADOR' order by codigo limit 1) as datainclusao\n" +
                    "from autorizacaocobrancacolaborador au \n" +
                    "inner join colaborador co on co.codigo = au.colaborador\n" +
                    "where autorizacaocobrancacolaborador.codigo = au.codigo) where dataregistro is null;", con);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            Uteis.logar(true, null, "FIM | PovoadorDataInclusaoAutorizacaoCobranca - AtualizarTabelas...");
        }
    }
}

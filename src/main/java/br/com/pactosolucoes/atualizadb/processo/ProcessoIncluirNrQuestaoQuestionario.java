package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.basico.QuestionarioPerguntaVO;
import negocio.comuns.basico.RespostaPerguntaVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.QuestionarioPergunta;
import negocio.facade.jdbc.basico.RespostaPergunta;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ProcessoIncluirNrQuestaoQuestionario {

    public static void ordernarQuestionarioPergunta(Connection con) throws Exception {
        try {
            String sqlConsultaCodigoQuestionario = "SELECT codigo as codigo FROM questionario ORDER BY codigo";
            List<Integer> codigosQuestionario = new ArrayList<>();
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlConsultaCodigoQuestionario)) {
                    while (rs.next()) {
                        int codigoQuestionario = rs.getInt("codigo");
                        codigosQuestionario.add(codigoQuestionario);
                    }
                }
            }

            for (Integer codigoQuestionario : codigosQuestionario){
                QuestionarioPergunta questionarioPerguntaDAO = new QuestionarioPergunta(con);
                List questionarioPerguntasVOs = questionarioPerguntaDAO.consultarQuestionarioPerguntas(codigoQuestionario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Ordenacao.ordenarLista(questionarioPerguntasVOs, "codigo");
                int cont = 0;
                Iterator i =  questionarioPerguntasVOs.iterator();
                while (i.hasNext()){
                    QuestionarioPerguntaVO obj = (QuestionarioPerguntaVO) i.next();
                    obj.setNrQuestao(cont);
                    questionarioPerguntaDAO.alterar(obj);
                    cont++;
                }
            }

        } catch (Exception ignore) {
        }

    }

    public static void ordernarRespostaPergunta(Connection con) throws Exception {
        try {
            String sqlConsultaCodigoQuestionario = "SELECT codigo as codigo FROM pergunta ORDER BY codigo";
            List<Integer> codigosPergunta = new ArrayList<>();
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlConsultaCodigoQuestionario)) {
                    while (rs.next()) {
                        int codigoQuestionario = rs.getInt("codigo");
                        codigosPergunta.add(codigoQuestionario);
                    }
                }
            }

            for (Integer codigoPergunta : codigosPergunta){
                RespostaPergunta respostaPerguntaDAO = new RespostaPergunta(con);
                List respostaPerguntaVOs = respostaPerguntaDAO.consultarRespostaPerguntas(codigoPergunta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Ordenacao.ordenarLista(respostaPerguntaVOs, "codigo");
                int cont = 0;
                Iterator i =  respostaPerguntaVOs.iterator();
                while (i.hasNext()){
                    RespostaPerguntaVO obj = (RespostaPerguntaVO) i.next();
                    obj.setNrQuestao(cont);
                    respostaPerguntaDAO.alterar(obj);
                    cont++;
                }
            }

        } catch (Exception ignore) {
        }

    }

}

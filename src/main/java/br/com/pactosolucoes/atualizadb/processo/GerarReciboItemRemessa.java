package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RemessaService;

import java.sql.Connection;
import java.sql.DriverManager;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static controle.arquitetura.SuperControle.registrarLogErroObjetoVO;
import static controle.arquitetura.SuperControle.registrarLogObjetoVO;

public class GerarReciboItemRemessa {

    List<FormaPagamentoVO> listaFormasPagamentoComConvenio;
    FormaPagamento fpDao;
    Pessoa pDao;
    MovPagamento mDao;
    ReciboPagamento rDao;
    OperadoraCartao oDao;
    FormaPagamentoVO formaPagamentoCartaoCredito;
    Connection con;
    UsuarioVO usuarioVO;
    ZillyonWebFacade zwFacade;
    RemessaItem remDao;

    public static void main(String ... args ) throws Exception{
        Connection con = DriverManager.getConnection("*****************************************************************", "postgres", "pactodb");
        Conexao.guardarConexaoForJ2SE(con);
        GerarReciboItemRemessa gerar = new GerarReciboItemRemessa();

        //Integer [] remessasItemId = new Integer[] {20662,20661,20660,20659,20658,20656,20654,20652,20651,20650,20648,20647,20663,20657,20676,20675,20674,20673,20672,20671,20670,20669,20668,20667,20666,20665,20664,20646,20653,20655,20649};
        Integer [] remessasItemId = new Integer[] {2465};
        for (int i=0; i<remessasItemId.length; i++){
            gerar.inicializar(remessasItemId[i], con);
        }
    }


    public void inicializar(Integer codigoremessaitem, Connection con){
        try {
            this.con = con;
            fpDao = new FormaPagamento(con);
            pDao = new Pessoa(con);
            remDao = new RemessaItem(con);
            rDao = new ReciboPagamento(con);
            mDao = new MovPagamento(con);
            oDao = new OperadoraCartao(con);
            zwFacade = new ZillyonWebFacade(con);
            formaPagamentoCartaoCredito = fpDao.obterFormaPagamentoCartaoRecorrente();
            listaFormasPagamentoComConvenio = fpDao.consultarFormaPagamentoComConvenioCobranca(true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            usuarioVO = zwFacade.getUsuarioRecorrencia();
            RemessaItemVO remessaItemVO = remDao.consultarPorChavePrimaria(codigoremessaitem, Uteis.NIVELMONTARDADOS_TODOS);
            incluirPagamento(remessaItemVO, remessaItemVO.getRemessa().getDataRetorno(), true);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void gerarItemRemessa(Integer codigoRemessaItem, String codAutorizacao, Date dataCompensacao, Connection con, UsuarioVO usuarioLogado) throws Exception {
        this.con = con;
        fpDao = new FormaPagamento(con);
        pDao = new Pessoa(con);
        remDao = new RemessaItem(con);
        rDao = new ReciboPagamento(con);
        mDao = new MovPagamento(con);
        oDao = new OperadoraCartao(con);
        zwFacade = new ZillyonWebFacade(con);
        formaPagamentoCartaoCredito = fpDao.obterFormaPagamentoCartaoRecorrente();
        listaFormasPagamentoComConvenio = fpDao.consultarFormaPagamentoComConvenioCobranca(true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        usuarioVO = zwFacade.getUsuarioRecorrencia();
        RemessaItemVO remessaItemVO = remDao.consultarPorChavePrimaria(codigoRemessaItem, Uteis.NIVELMONTARDADOS_TODOS);
        remessaItemVO.put(DCCAttEnum.CodigoAutorizacao.name(), codAutorizacao);

        if (!UteisValidacao.emptyNumber(remessaItemVO.getMovPagamento().getCodigo())) {
            mDao.incluirAutorizacao(remessaItemVO.getMovPagamento().getCodigo(), codAutorizacao, "");
        } else {
            RemessaService remessaService = new RemessaService();
            remessaService.incluirPagamentoAgrupado(remessaItemVO, remessaItemVO.getRemessa().getDataRetorno(), true, true);
            remessaService = null;
        }
        remDao.alterar(remessaItemVO);

//            try (Statement stm = con.createStatement()) {
//                try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
//                    return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
//                }
//            }

        StringBuilder sql = new StringBuilder();
        sql.append("update cartaocredito")
                .append(" set datacompesancao = '" + dataCompensacao + "'")
                .append(" where codigo = ")
                .append(" (select ccr.codigo")
                .append(" from cartaocredito ccr")
                .append(" inner join movpagamento mpa on ccr.movpagamento = mpa.codigo")
                .append(" inner join remessaitem rem on rem.movpagamento = mpa.codigo")
                .append(" where rem.codigo = " + codigoRemessaItem + ")");
        con.prepareStatement(sql.toString()).execute();

        incluirLog(remessaItemVO, usuarioLogado, codAutorizacao, dataCompensacao);
    }

    private void incluirLog(RemessaItemVO remessaItemVO, UsuarioVO usuarioLogado, String codigoAutorizacao, Date dataCompensacao) throws Exception {
        try {
            List<LogVO> logs = new ArrayList<>();

            LogVO log = montaLog(remessaItemVO, usuarioLogado);
            log.setNomeCampo("Autorização");
            log.setValorCampoAnterior(remessaItemVO.getMovPagamento().getAutorizacaoCartao());
            log.setValorCampoAlterado(codigoAutorizacao);
            logs.add(log);

            log = montaLog(remessaItemVO, usuarioLogado);
            log.setNomeCampo("Data compensação");
            log.setValorCampoAnterior("Sem valor");
            log.setValorCampoAlterado(Calendario.getData(dataCompensacao, "dd-MM-yyyy"));
            logs.add(log);

            log = montaLog(remessaItemVO, usuarioLogado);
            log.setNomeCampo("Parcela");
            log.setValorCampoAnterior(remessaItemVO.getMovParcela().getDescricao());
            log.setValorCampoAlterado("Sem alteração");
            logs.add(log);

            log = montaLog(remessaItemVO, usuarioLogado);
            log.setNomeCampo("Cod. Remessa");
            log.setValorCampoAnterior(remessaItemVO.getCodigo().toString());
            log.setValorCampoAlterado("Sem alteração");
            logs.add(log);

            registrarLogObjetoVO(logs, remessaItemVO.getPessoa().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("Cliente", remessaItemVO.getPessoa().getCodigo(), "ERRO AO GERAR LOG DO CLIENTE", usuarioLogado.getUsuarioLogado().getNome(), usuarioLogado.getUsuarioLogado().getUserOamd());
        }
    }

    private LogVO montaLog(RemessaItemVO remessaItemVO, UsuarioVO usuarioLogado) {
        LogVO log = new LogVO();
        log.setChavePrimaria(remessaItemVO.getClienteVO().getCodigo().toString());
        log.setNomeEntidade("Cliente");
        log.setNomeEntidadeDescricao("Cliente");
        log.setOperacao("INCLUSÃO");
        log.setResponsavelAlteracao(usuarioLogado.getNome());
        log.setUserOAMD(usuarioLogado.getUserOamd());
        log.setDataAlteracao(Calendario.hoje());

        return log;
    }

    private void incluirPagamento(RemessaItemVO item, Date dataOcorrencia, boolean alterarDataRecibo) throws Exception {
        if (item.getMovParcela().getSituacao().equals("EA")) {
            List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();

            if (item.getMovParcela().getPessoa().getCodigo() != 0) {
                item.getMovParcela().setPessoa(pDao.
                        consultarPorChavePrimaria(item.getMovParcela().
                                getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setDataPrevistaDeposito(dataOcorrencia);

            boolean temFormaPagamento = false;
            for (FormaPagamentoVO form : listaFormasPagamentoComConvenio) {
                if (form.getConvenioCobrancaVO() != null && form.getConvenioCobrancaVO().getCodigo().equals(item.getRemessa().getConvenioCobranca().getCodigo())) {
                    movPagamentoVO.setFormaPagamento(form);
                    temFormaPagamento = true;
                    break;
                }
            }

            if (!temFormaPagamento) {
                movPagamentoVO.setFormaPagamento(formaPagamentoCartaoCredito);
            }
            movPagamentoVO.setMovPagamentoEscolhida(true);
            movPagamentoVO.setValor(item.getValorItemRemessa());
            movPagamentoVO.setValorTotal(item.getValorItemRemessa());
            movPagamentoVO.setPessoa(item.getMovParcela().getContrato().getPessoa());
            movPagamentoVO.setNomePagador(item.getMovParcela().getPessoa().getNome());
            if (item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC)
                    || item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                    || item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                movPagamentoVO.setConvenio(item.getRemessa().getConvenioCobranca());
                movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
                if(item.getMovParcela().getNumeroParcelasOperadora() == null
                        || item.getMovParcela().getNumeroParcelasOperadora() == 0){
                    movPagamentoVO.setNrParcelaCartaoCredito(1);
                }else{
                    movPagamentoVO.setNrParcelaCartaoCredito(item.getMovParcela().getNumeroParcelasOperadora());
                }
                OperadoraCartaoVO oper = obterOperadora(item);
                if (oper != null) {
                    movPagamentoVO.setOperadoraCartaoVO(oper);
                }
                movPagamentoVO.setAutorizacaoCartao(item.get(DCCAttEnum.CodigoAutorizacao.name()));
                movPagamentoVO.setAdquirenteVO(incluirAdquirenteMovPagamento(item));
            } else {
                movPagamentoVO.setOpcaoPagamentoDinheiro(true);
            }

            movPagamentoVO.setResponsavelPagamento(usuarioVO);
            movPagamentoVO.setPessoa(item.getMovParcela().getPessoa());
            movPagamentoVO.setEmpresa(item.getMovParcela().getEmpresa());
            if (alterarDataRecibo) {
                movPagamentoVO.setDataLancamento(dataOcorrencia);
                movPagamentoVO.setDataQuitacao(dataOcorrencia);
                movPagamentoVO.setDataPagamento(dataOcorrencia);
            }
            listaPagamento.add(movPagamentoVO);

            List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();
            parcelas.add(item.getMovParcela());


            MovPagamentoVO.validarDados(movPagamentoVO);

            ReciboPagamentoVO reciboObj = mDao.incluirListaPagamento(
                    listaPagamento,
                    parcelas,
                    null,
                    item.getMovParcela().getContrato(),
                    false, 0.0);
            if (alterarDataRecibo) {
                reciboObj.setData(dataOcorrencia);
                rDao.alterar(reciboObj);
            }


            item.setMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0));
        }else{
            if(item.getMovParcela().getSituacao().equals("CA")){
                remDao.alterarProps(item.getCodigo(), "remessaCancelada=true");
                item.getProps().put("remessaCancelada", "true");
            }
        }
    }

    private OperadoraCartaoVO obterOperadora(RemessaItemVO item) throws Exception {
        String nomeBandeira = item.get(APF.Bandeira);
        if (!nomeBandeira.isEmpty()) {
            OperadorasExternasAprovaFacilEnum oper = OperadorasExternasAprovaFacilEnum.valueOf(nomeBandeira);
            return obterOperadoraPorEnum(oper);
        } else {
            return null;
        }
    }

    private OperadoraCartaoVO obterOperadoraPorEnum(OperadorasExternasAprovaFacilEnum oper) throws Exception {
        List<OperadoraCartaoVO> lista = oDao.consultarPorCodigoIntegracaoAPF(
                oper.getId(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (!lista.isEmpty()) {
            return lista.get(0);
        } else {
            return null;
        }
    }

    private AdquirenteVO incluirAdquirenteMovPagamento(RemessaItemVO obj) throws Exception {
        if (obj != null) {
            Adquirente adquirenteDAO;
            try {
                adquirenteDAO = new Adquirente(con);
                return adquirenteDAO.obterAdquirenteRemessaItem(obj.getRemessa());
            } catch (Exception e) {
                e.getStackTrace();
            } finally {
                adquirenteDAO = null;
            }
        }
        return new AdquirenteVO();
    }
}

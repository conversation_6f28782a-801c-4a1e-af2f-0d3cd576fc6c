package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Wenders<PERSON>",
        data = "21/11/2024",
        descricao = "Criação de duas colunas para salvar o tipo e código do fluxo do GymBot na tabela historicocontato",
        motivacao = "Ticket M2-2751, necessário ter referência do fluxo Fase CRM e Tela de cliente para validar se está em uso antes de exluir")
public class M22751CriarColunasFluxoGymBotHistoricoContato implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE historicocontato ADD COLUMN fluxoGymBot Integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE historicocontato ADD COLUMN tipoGymBotEnum Integer;", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luis Antônio de Melo Gomes",
        data = "02/05/2025",
        descricao = "Implementar campo de descricao contato proativo",
        motivacao = "IA-916")
public class AtualizacaoTicketIA916 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE configuracaocrmia ADD COLUMN descricaoNotificacaoProativo TEXT;"
                    , c);
        }
    }
}

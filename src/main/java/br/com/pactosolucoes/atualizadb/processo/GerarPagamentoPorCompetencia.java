/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class GerarPagamentoPorCompetencia {

    static MovProduto movProdutoDao;
    static MovParcela movParcelaDao;
    static MovProdutoParcela movProdutoParcelaDao;
    static ReciboPagamento reciboDao;
    static MovPagamento pagamentoDao;
    static PagamentoMovParcela pagamentoMovparcelaDao;
    static Contrato contratoDao;

    public static void main(String ... args) throws Exception{
        Connection con = DriverManager.getConnection("**************************************", "postgres", "pactodb");
        refazerPagamentosParcelas(con, true);
    }

    public static void pagarTodasParcelasImportacao(Connection con) throws Exception {
        movProdutoDao = new MovProduto(con);
        movParcelaDao = new MovParcela(con);
        movProdutoParcelaDao = new MovProdutoParcela(con);
        reciboDao = new ReciboPagamento(con);
        contratoDao = new Contrato(con);
        pagamentoDao = new MovPagamento(con);
        pagamentoMovparcelaDao = new PagamentoMovParcela(con);
        Conexao.guardarConexaoForJ2SE(con);
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from contrato where id_externo is not null", con);
        while (rs.next()) {
            ContratoVO contratoVO = Contrato.montarDados(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
            ResultSet rsParcela = SuperFacadeJDBC.criarConsulta("select * from movparcela where situacao = 'EA' and contrato = "+rs.getInt("codigo"), con);
            while(rsParcela.next()){
                MovParcelaVO movParcelaVO = MovParcela.montarDados(rsParcela, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);

                ReciboPagamentoVO recibo = new ReciboPagamentoVO();
                recibo.setContrato(contratoVO);
                recibo.setData(contratoVO.getDataLancamento());
                recibo.setNomePessoaPagador(contratoVO.getPessoa().getNome());
                recibo.setPessoaPagador(contratoVO.getPessoa());
                recibo.setResponsavelLancamento(contratoVO.getResponsavelContrato());
                recibo.setValorTotal(movParcelaVO.getValorParcela());
                recibo.setEmpresa(contratoVO.getEmpresa());
                reciboDao.incluir(recibo);

                MovPagamentoVO movPagamento = new MovPagamentoVO();
                movPagamento.setPessoa(contratoVO.getPessoa());
                movPagamento.setDataLancamento(contratoVO.getDataLancamento());
                movPagamento.setDataPagamento(contratoVO.getDataLancamento());
                movPagamento.setDataQuitacao(contratoVO.getDataLancamento());
                movPagamento.setResponsavelPagamento(contratoVO.getResponsavelContrato());
                movPagamento.setValor(movParcelaVO.getValorParcela());
                movPagamento.setValorTotal(movParcelaVO.getValorParcela());
                movPagamento.setEmpresa(contratoVO.getEmpresa());
                movPagamento.getFormaPagamento().setCodigo(3);
                movPagamento.setNomePagador(contratoVO.getPessoa().getNome());
                movPagamento.setMovPagamentoEscolhida(true);
                movPagamento.setReciboPagamento(recibo);
                pagamentoDao.incluir(movPagamento);

                PagamentoMovParcelaVO ppmp = new PagamentoMovParcelaVO();
                ppmp.setMovPagamento(movPagamento.getCodigo());
                ppmp.setMovParcela(movParcelaVO);
                ppmp.setValorPago(movParcelaVO.getValorParcela());
                ppmp.setReciboPagamento(recibo);
                pagamentoMovparcelaDao.incluir(ppmp);
                SuperFacadeJDBC.executarConsulta("update movparcela set situacao = 'PG' WHERE codigo = "+movParcelaVO.getCodigo(), con);
                SuperFacadeJDBC.executarConsulta("update movprodutoparcela set recibopagamento = " +recibo.getCodigo()+
                        "  WHERE recibopagamento is null and movparcela = "+movParcelaVO.getCodigo(), con);
                SuperFacadeJDBC.executarConsulta("update movproduto set situacao = 'PG' WHERE codigo IN" +
                        " (select movproduto from movprodutoparcela where movparcela = "+movParcelaVO.getCodigo()+") ", con);
                System.out.println("Paguei parcela "+movParcelaVO.getCodigo());
            }
        }
    }


    public static void refazerPagamentosParcelas(Connection con, boolean pagarApenasPassado) throws Exception{
        movProdutoDao = new MovProduto(con);
        movParcelaDao = new MovParcela(con);
        movProdutoParcelaDao = new MovProdutoParcela(con);
        reciboDao = new ReciboPagamento(con);
        contratoDao = new Contrato(con);
        pagamentoDao = new MovPagamento(con);
        pagamentoMovparcelaDao = new PagamentoMovParcela(con);
        Conexao.guardarConexaoForJ2SE(con);
        String wherecontratos = "";

        StringBuilder where = new StringBuilder();

        if(wherecontratos.isEmpty()){
            where.append(" where id_externo is not null ");
        }else{
            where.append(" where codigo in (");
            where.append(wherecontratos.replaceFirst(",", ""));
            where.append(") ");
        }

        Integer total = SuperFacadeJDBC.contar("select count(codigo) from contrato "+where.toString(), con);
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from contrato "+where.toString(), con);
        int cont = 0;
        while (rs.next()) {
            System.out.println("------------------------------------------"+(++cont)+"/"+total);
            Integer codigoContrato = rs.getInt("codigo");
            //deletar historico
            SuperFacadeJDBC.executarConsulta("DELETE FROM recibopagamento where codigo in (select "
                    + "recibopagamento from movpagamento where codigo in "
                    + "(select movpagamento from pagamentomovparcela where movparcela in "
                    + "(select codigo from movparcela where contrato = "+codigoContrato+")))",con);
            SuperFacadeJDBC.executarConsulta("DELETE FROM movpagamento where codigo in "
                    + "(select movpagamento from pagamentomovparcela where movparcela in "
                    + "(select codigo from movparcela where contrato = "+codigoContrato+"))",con);
            SuperFacadeJDBC.executarConsulta("DELETE FROM movprodutoparcela where movparcela in"
                    + "(select codigo from movparcela where contrato = "+codigoContrato+")", con);

            SuperFacadeJDBC.executarConsulta("DELETE from movparcela where contrato = "+codigoContrato, con);

            PagamentoMovParcela.corrigirPagamentosSemMovParcelas(con);

            con.commit();
            ContratoVO contratoVO = contratoDao.consultarPorChavePrimaria(codigoContrato, Uteis.NIVELMONTARDADOS_ROBO);
            montarParcelasPagamentos(contratoVO, con, pagarApenasPassado);
        }
    }

    public static void  montarParcelasPagamentos(ContratoVO contratoVO, Connection con, boolean pagarApenasPassado) throws SQLException, Exception{

        List<MovProdutoVO> produtos = movProdutoDao.consultarPorCodigoContrato(contratoVO.getCodigo(),
                Uteis.NIVELMONTARDADOS_PAGAMENTOS_TELA_CLIENTE);
        produtos = Ordenacao.ordenarLista(produtos, "codigo");
        Integer diaBase = Uteis.obterDiaData(contratoVO.getVigenciaDe());
        int nrParcela = 1;
        for(MovProdutoVO prod : produtos){
            if(!prod.getProduto().getDescricao().startsWith("PLANO")){
                continue;
            }
            Date dataBaseMes = Uteis.getDate("01/"+prod.getMesReferencia());
            Date dataVencimentoParcela;
            int mesData = Uteis.getMesData(dataBaseMes);
            MesesEnum mes = MesesEnum.getFromId(mesData);
            if(mes.getNrDias() < diaBase){
                dataVencimentoParcela = Uteis.getDate(mes.getNrDias()+"/"+prod.getMesReferencia());
            }else{
                dataVencimentoParcela = Uteis.getDate((diaBase < 10 ? "0" : "" )+diaBase+"/"+prod.getMesReferencia());
            }
            MovParcelaVO movParcelaVO = new MovParcelaVO();
            movParcelaVO.setContrato(contratoVO);
            movParcelaVO.setDataRegistro(contratoVO.getDataLancamento());
            movParcelaVO.setDataVencimento(dataVencimentoParcela);
            movParcelaVO.setDescricao("PARCELA "+(nrParcela));
            System.out.println("Gerando parcela "+nrParcela+" para contrato "+contratoVO.getCodigo());
            movParcelaVO.setResponsavel(contratoVO.getResponsavelContrato());
            if(!pagarApenasPassado || Calendario.menorOuIgual(dataVencimentoParcela, Calendario.hoje())){
                movParcelaVO.setSituacao("PG");
            }else{
                movParcelaVO.setSituacao("EA");
            }

            movParcelaVO.setValorParcela(prod.getTotalFinal());
            movParcelaVO.setPercentualJuro(0.0);
            movParcelaVO.setPercentualMulta(0.0);
            movParcelaVO.setPessoa(contratoVO.getPessoa());
            movParcelaVO.setEmpresa(contratoVO.getEmpresa());
            movParcelaDao.incluir(movParcelaVO);



            MovProdutoParcelaVO prodParcela = new MovProdutoParcelaVO();
            prodParcela.setMovParcela(movParcelaVO.getCodigo());
            prodParcela.setMovProduto(prod.getCodigo());
            prodParcela.setValorPago(prod.getTotalFinal());

            ReciboPagamentoVO recibo = null;
            if(!pagarApenasPassado || Calendario.menorOuIgual(dataVencimentoParcela, Calendario.hoje())){
                recibo = new ReciboPagamentoVO();
                recibo.setContrato(contratoVO);
                recibo.setData(contratoVO.getDataLancamento());
                recibo.setNomePessoaPagador(contratoVO.getPessoa().getNome());
                recibo.setPessoaPagador(contratoVO.getPessoa());
                recibo.setResponsavelLancamento(contratoVO.getResponsavelContrato());
                recibo.setValorTotal(prod.getTotalFinal());
                recibo.setEmpresa(contratoVO.getEmpresa());
                prodParcela.setReciboPagamento(recibo);
                reciboDao.incluir(recibo);
            }



            movProdutoParcelaDao.incluir(prodParcela);


            if(!pagarApenasPassado || Calendario.menorOuIgual(dataVencimentoParcela, Calendario.hoje())){
                MovPagamentoVO movPagamento = new MovPagamentoVO();
                movPagamento.setPessoa(contratoVO.getPessoa());
                movPagamento.setDataLancamento(contratoVO.getDataLancamento());
                movPagamento.setDataPagamento(dataVencimentoParcela);
                movPagamento.setDataQuitacao(dataVencimentoParcela);
                movPagamento.setResponsavelPagamento(contratoVO.getResponsavelContrato());
                movPagamento.setValor(prod.getTotalFinal());
                movPagamento.setValorTotal(prod.getTotalFinal());
                movPagamento.setEmpresa(contratoVO.getEmpresa());
                movPagamento.getFormaPagamento().setCodigo(3);
                movPagamento.setNomePagador(contratoVO.getPessoa().getNome());
                movPagamento.setMovPagamentoEscolhida(true);
                movPagamento.setReciboPagamento(recibo);
                pagamentoDao.incluir(movPagamento, false);

                PagamentoMovParcelaVO ppmp = new PagamentoMovParcelaVO();
                ppmp.setMovPagamento(movPagamento.getCodigo());
                ppmp.setMovParcela(movParcelaVO);
                ppmp.setValorPago(prod.getTotalFinal());
                ppmp.setReciboPagamento(recibo);
                pagamentoMovparcelaDao.incluir(ppmp);
            }


            nrParcela++;
        }

    }

    public enum MesesEnum {

        JANEIRO(1, 31),
        FEVEREIRO(2, 29),
        MARCO(3, 31),
        ABRIL(4, 30),
        MAIO(5, 31),
        JUNHO(6, 30),
        JULHO(7, 31),
        AGOSTO(8, 31),
        SETEMBRO(9, 30),
        OUTUBRO(10, 31),
        NOVEMBRO(11, 30),
        DEZEMBRO(12, 31);
        private Integer numero;
        private Integer nrDias;

        private MesesEnum(Integer numero, Integer nrDias) {
            this.numero = numero;
            this.nrDias = nrDias;

        }

        public Integer getNumero() {
            return numero;
        }

        public void setNumero(Integer numero) {
            this.numero = numero;
        }

        public Integer getNrDias() {
            return nrDias;
        }

        public void setNrDias(Integer nrDias) {
            this.nrDias = nrDias;
        }

        public static MesesEnum getFromId(int i){
            for(MesesEnum m : MesesEnum.values()){
                if(m.getNumero() == i){
                    return m;
                }
            }
            return null;
        }

    }

}

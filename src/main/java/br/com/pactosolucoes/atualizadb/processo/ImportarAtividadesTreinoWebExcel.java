/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;


import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;

import java.io.*;
import java.sql.*;
import java.util.*;
import java.util.Date;


public class ImportarAtividadesTreinoWebExcel {

    public static void main(String[] args) throws SQLException {

        /* INFORMAÇÕES:
        Formato do excel (xls)
        A primeira linha de cada coluna deve ser o GRUPO MUSCULAR.
        As linhas abaixo devem ser as ATIVIDADES daquele GRUPO MUSCULAR.
        */

        String arquivoExcel = "C:\\PactoJ\\justAtividades.xls";
        Connection con = DriverManager.getConnection("*****************************************", "postgres", "pactodb");
        try {
            con.setAutoCommit(true);

            System.out.println("Início em : " + new Date());

            Map<String, List<String>> mapa = lerExcel(arquivoExcel);
            importarAtividadesTreinoWeb(mapa, con);

            System.out.println("Fim em : " + new Date());


        } catch (Exception e) {
            con.setAutoCommit(true);
            e.printStackTrace();
        } finally {
            con.setAutoCommit(true);
        }
    }

    private static Map<String, List<String>> lerExcel(String caminhoArquivo) throws IOException {
        Map<String, List<String>> mapa = new HashMap<String, List<String>>();
        try {
            Map<Integer, String> mapaHeader = new HashMap<Integer, String>();
            FileInputStream file = new FileInputStream(new File(caminhoArquivo));
            HSSFWorkbook workbook = new HSSFWorkbook(file);
            HSSFSheet sheet = workbook.getSheetAt(0);

            Iterator rowIterator = sheet.rowIterator();
            while (rowIterator.hasNext()) {
                Row row = (Row) rowIterator.next();
                Iterator cellIterator = row.cellIterator();
                while (cellIterator.hasNext()) {
                    Cell cell = (Cell) cellIterator.next();


                    if (row.getRowNum() == 0) {
                        mapaHeader.put(cell.getColumnIndex(), cell.getStringCellValue().trim());
                    } else {

                        String header = mapaHeader.get(cell.getColumnIndex());

                        List<String> lista = mapa.get(header);
                        if (lista == null) {
                            lista = new ArrayList<String>();
                        }

                        String valorColuna = cell.getStringCellValue();
                        if (!UteisValidacao.emptyString(valorColuna)) {
                            lista.add(valorColuna.trim());
                        }
                        mapa.put(header, lista);
                    }
                }
            }
            file.close();
            workbook.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        return mapa;
    }

    private static void importarAtividadesTreinoWeb(Map<String, List<String>> mapa, Connection con) throws Exception {

        for (String key : mapa.keySet()) {

            Integer codigoGrupo = 0;

            ResultSet consultaGrupo = SuperFacadeJDBC.criarConsulta("select codigo from grupomuscular where nome = '" + key + "'", con);
            while (consultaGrupo.next()) {
                codigoGrupo = consultaGrupo.getInt("codigo");
            }

            if (UteisValidacao.emptyNumber(codigoGrupo)) {
                PreparedStatement insert = con.prepareStatement("insert into grupomuscular(nome) values ('" + key + "') RETURNING codigo;");
                ResultSet rs = insert.executeQuery();
                if (rs.next()) {
                    codigoGrupo = rs.getInt("codigo");
                }
            }

            System.out.println("### Grupo: " + key + " || Código do Grupo: " + codigoGrupo);


            List<String> atividades = mapa.get(key);

            for (String ativ : atividades) {

                Integer codigoAtividade = 0;

                ResultSet consultaAtivi = SuperFacadeJDBC.criarConsulta("select codigo from atividade where nome = '" + ativ + "'", con);
                while (consultaAtivi.next()) {
                    codigoAtividade = consultaAtivi.getInt("codigo");
                }

                if (UteisValidacao.emptyNumber(codigoAtividade)) {
                    PreparedStatement insert = con.prepareStatement("insert into atividade(todasempresas,versao,unidademedida,descricao,categoriaatividadewod,seriesapenasduracao,tipo,crossfit,ativo,nome) " +
                            "values (true,0,0,'',0,false,0,false,true,'" + ativ + "') RETURNING codigo;");
                    ResultSet rs = insert.executeQuery();
                    if (rs.next()) {
                        codigoAtividade = rs.getInt("codigo");
                    }
                }

                System.out.println("### Atividade: " + ativ + " || Código da Atividade: " + codigoAtividade);

                if (!UteisValidacao.emptyNumber(codigoAtividade) && !UteisValidacao.emptyNumber(codigoGrupo)) {
                    Integer codRelacionamento = 0;
                    PreparedStatement insertRelacio = con.prepareStatement("insert into atividadegrupomuscular(grupomuscular_codigo,atividade_codigo) values (" + codigoGrupo + "," + codigoAtividade + ") RETURNING codigo;");
                    ResultSet rs = insertRelacio.executeQuery();
                    if (rs.next()) {
                        codRelacionamento = rs.getInt("codigo");
                    }
                } else {
                    System.out.println("### ERRO: Código Atividade: " + codigoAtividade + " || Código do Grupo: " + codigoGrupo);
                }
            }
        }
    }
}

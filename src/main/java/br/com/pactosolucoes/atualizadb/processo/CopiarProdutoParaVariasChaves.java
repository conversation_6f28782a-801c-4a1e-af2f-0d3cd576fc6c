package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.plano.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.crm.ModeloMensagem;
import negocio.facade.jdbc.plano.*;
import org.apache.commons.lang.SerializationUtils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CopiarProdutoParaVariasChaves {


    public static void main(String... args) throws Exception {

        String urlsBancoOrigem = "*********************************************************************";
        Integer codigoProdutoOrigem = 148;
        String descricaoPlanoOrigem = "PLANOS 3X";
        String nomeModalidade = "CROSS TRAINING";
        Integer codigoModeloMensagem = 1;
        Integer codigoPlano = 26;

        String[] urlsBancos = new String[]{


                "*****************************************************************************"
                ,"********************************************************************"
                ,"**********************************************************************"
                ,"********************************************************************"
                ,"***********************************************************************"
                ,"*************************************************************************"
                ,"***********************************************************************"
                ,"************************************************************************"
                ,"**********************************************************************"
                ,"*********************************************************************"
                ,"*************************************************************************"
                ,"jdbc:postgresql://************:5432/bdzillyoncrossexperienceunidourosmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidmorumbisaojosedoscampossp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceuberabamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencesaoluizrj"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidcancellipr"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceitabaianasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidguaxupe"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencecacondesp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencesjnepomucenomg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceruaesmaeluniditajubamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceportovelhoro"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencetaubatesp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidcanarinhoboavistarr"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencenovomundomg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencemontemorsp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencepontealtarj"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidguaranesia"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencesaojosedospinhaispr"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidjardimesperancamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceextremamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidbelavistamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencejequieba"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencefranciscobeltraopr"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencesilvanopolismg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceinterlagos"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencemauritipa"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceuniddeltasinopmt"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceavanhandavasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencealtoumuaramamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencetubalinauberlandiamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencebarrape"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceourinhossp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidstoafonsotrescoracoesmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidsaofelixdoxingupa"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidsagradocoracaovarginhamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidcentroporteirinhamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceliquinhamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidluiseduardomagalhaesba"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidsaogotardomg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceitapemasc"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencematiasbarbosamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidsaosebastdoparaiso"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidhortolandiasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceitapevamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencejaguarees"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencejacutingamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencejardimpatriciamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencealtosaojoaomontesclarosmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceparacatumg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencealfenasmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidalvorada"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencecanasvieirassc"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidguararemasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidiguape"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencejdvalesp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencesaoborjars"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidmariadafemg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidcarmodorioclaro"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencealpinopolismg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidfontesvillejuizdeforamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidvilafurlansp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceceramicajuizdeforamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencemarabapatosdeminasmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencelimeirosp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidnovohorizontesp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceuniditapirasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidsaojosesc"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencediamantinosantarempa"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencecongonhalmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidjdcuritibamt"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceserrinhaba"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencejaguaquaraba"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidsantacruzmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencebelempa"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencelondrinepr"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencejacupirangasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencetresriosrj"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceparquebrasilbpaulistasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidcentrotrespontasmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencevilasaocristovaovalinhossp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencecrateusce"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencemucajairr"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceramonmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencepampulhabh"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencegovernadorvaladaresmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencedrjoaoribeirolavrasmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencecurvelomg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncexperienceparque10am"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencealtomaronvitoriadaconquistaba"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencebrisamarjoaopessopb"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceguarulhossp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencedocapa"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceipatingamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceparquevenezarj"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceuberabamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceconceicaodaaparecidamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunaimg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencerpacademiasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceboxcrosstrainingbelavistaba"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencemartins"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencesaogeraldorj"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencealemparaibamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencelinhareses"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencejacarandasinopmt"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencerecantbarreipousoalegremg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencearacatubasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencenoronhapousoalegremg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceteofilootonimg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceriobonitosp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencetaiuvasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceeloimendesmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencegurupito"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidsantaluziauba"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencevitoriaregialondrinapr"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidcastanheirasgovernador"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidraiardosolboavistarr"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencemiguelsutilmt"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidguaratinguetasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidriosul"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencealenquerpa"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidparaisopolismg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceboulevardto"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidsantamonicacatalaogo"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidandremaggimt"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidburitismg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidsantanadolivramentors"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencelorenasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidbarbacenamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencesjrpsp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidcaboverdemg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencesaoluizma"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidjardimdasnacoesmt"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencebarradaestivaba"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidriachodesantanaba"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencemanoelhonoriojuizdeforamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencetrespontasmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidmiracatusp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencejardimcarliforniasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidsorocabasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidouvidorgo"
                ,"**********************************************************************************"
                ,"*************************************************************************"
                ,"***********************************************************************"
                ,"****************************************************************************"
                ,"***********************************************************************************"
                ,"******************************************************************************"
                ,"***********************************************************************************"
                ,"********************************************************************************"
                ,"*****************************************************************************************"
                ,"********************************************************************************"
                ,"*****************************************************************************"
                ,"********************************************************************************"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencecajatisp"
                ,"************************************************************************"
                ,"*********************************************************************************"
                ,"***********************************************************************"
                ,"****************************************************************************"
                ,"***********************************************************************"
                ,"**********************************************************************"
                ,"**********************************************************************************"
                ,"************************************************************************"
                ,"****************************************************************************"
                ,"********************************************************************"
                ,"******************************************************************************"
                ,"***********************************************************************"
                ,"************************************************************************************"
                ,"**************************************************************************"
                ,"******************************************************************************"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencebelvederemanausam"
                ,"*****************************************************************************"
                ,"*******************************************************************************"
                ,"*****************************************************************************"
                ,"***********************************************************************"
                ,"*********************************************************************"
                ,"********************************************************************"
                ,"*******************************************************************************"
                ,"*************************************************************************"
                ,"***********************************************************************"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencedunasrs"
                ,"***********************************************************************"
                ,"**********************************************************************************"
                ,"************************************************************************************"
                ,"*******************************:5432/bdzillyoncrossexperienceunidpizalondrinapr"
                ,"*******************************:5432/bdzillyoncrossexperienceunidestrelamg"
                ,"*******************************:5432/bdzillyoncrossexperiencepontenovamg"
                ,"*******************************:5432/bdzillyoncrossexperiencearaxamg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidpqindustrialsp"
                ,"*******************************:5432/bdzillyoncrossexperiencecentrojanuariamg"
                ,"*******************************:5432/bdzillyoncrossexperiencerezendevarginhamg"
                ,"*******************************:5432/bdzillyoncrossexperiencemagabeirajoaopessoapb"
                ,"*******************************:5432/bdzillyoncrossexperiencegramajuizdeforamg"
                ,"*******************************:5432/bdzillyoncrossexperiencemanoasetelagoasmg"
                ,"*******************************:5432/bdzillyoncrossexperiencecentrocambuquiramg"
                ,"*******************************:5432/bdzillyoncrossexperiencelagodoscisnessaomateuses"
                ,"*******************************:5432/bdzillyoncrossexperiencejdmaringaitapevasp"
                ,"*******************************:5432/bdzillyoncrossexperienceunidsaobeneditocampobelomg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidsantarita"
                ,"*******************************:5432/bdzillyoncrossexperienceunidcapitaogomes"
                ,"*******************************:5432/bdzillyoncrossexperiencecentroturvolandiamg"
                ,"*******************************:5432/bdzillyoncrossexperiencealvoradamanausam"
                ,"*******************************:5432/bdzillyoncrossexperiencecentroparaguacumg"
                ,"*******************************:5432/bdzillyoncrossexperienceuniversitarioituveravasp"
                ,"*******************************:5432/bdzillyoncrossexperiencecentroalterosamg"
                ,"*******************************:5432/bdzillyoncrossexperiencefeitosaal"
                ,"*******************************:5432/bdzillyoncrossexperiencealtodospassosjuizdeforamg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidpelotasmg"
                ,"*******************************:5432/bdzillyoncrossexperiencensdefatimaparacatumg"
                ,"*******************************:5432/bdzillyoncrossexperienceipanemapatosdeminasmg"
                ,"*******************************:5432/bdzillyoncrossexperiencesantaesmeraldasp"
                ,"*******************************:5432/bdzillyoncrossexperienceunidnossotetosp"
                ,"*******************************:5432/bdzillyoncrossexperienceunidgraduquesagovernadorvaladaresmg"
                ,"*******************************:5432/bdzillyoncrossexperiencecentrodivinopolismg"
                ,"*******************************:5432/bdzillyoncrossexperiencevilaisagovernadorvaladaresmg"
                ,"*******************************:5432/bdzillyoncrossexperiencecentromuzambinhomg"
                ,"*******************************:5432/bdzillyoncrossexperienceboavistatancredonevesrr"
                ,"*******************************:5432/bdzillyoncrossexperiencesantaluziajuizdeforamg"
                ,"*******************************:5432/bdzillyoncrossexperienceplanaltoestivamg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidsaocarlossp"
                ,"*******************************:5432/bdzillyoncrossexperienceibirapueravitoriadaconquistaba"
                ,"*******************************:5432/bdzillyoncrossexperiencesaopedrojuizdeforamg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidmaracasba"
                ,"*******************************:5432/bdzillyoncrossexperiencecentenarioboavistarr"
                ,"*******************************:5432/bdzillyoncrossexperienceunidpiraubamg"
                ,"*******************************:5432/bdzillyoncrossexperiencevalentinapb"
                ,"*******************************:5432/bdzillyoncrossexperiencemvfdearaujopi"
                ,"*******************************:5432/bdzillyoncrossexperiencesaojudas"
                ,"*******************************:5432/bdzillyoncrossexperiencecentroaiuruocamg"
                ,"*******************************:5432/bdzillyoncrossexperiencecentromatoverdemg"
                ,"*******************************:5432/bdzillyoncrossexperienceavenida7juizdeforamg"
                ,"*******************************:5432/bdzillyoncrossexperiencemarialuizacascavelpr"
                ,"*******************************:5432/bdzillyoncrossexperiencesaocristovaopousoalegremg"
                ,"*******************************:5432/bdzillyoncrossexperiencecentroipuiunamg"
                ,"*******************************:5432/bdzillyoncrossexperiencevlsaocarlosmogiguacusp"
                ,"*******************************:5432/bdzillyoncrossexperiencecumbuquiracarlospratesbhmg"
                ,"*******************************:5432/bdzillyoncrossexperienceparqueprimaveracachoeirapaulistasp"
                ,"*******************************:5432/bdzillyoncrossexperiencemaringapr"
                ,"*******************************:5432/bdzillyoncrossexperiencematozinhossjdrmg"
                ,"*******************************:5432/bdzillyoncrossexperienceliberdademarabapa"
                ,"*******************************:5432/bdzillyoncrossexperiencemajorpratesmontesclarosmg"
                ,"*******************************:5432/bdzillyoncrossexperienceparacatuzinhoparacatumg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidjdrosangelo"
                ,"*******************************:5432/bdzillyoncrossexperiencematupamt"
                ,"*******************************:5432/bdzillyoncrossexperiencecaruarupe"
                ,"*******************************:5432/bdzillyoncrossexperiencelondrinamaritacaspr"
                ,"*******************************:5432/bdzillyoncrossexperienceamparocentrosp"
                ,"*******************************:5432/bdzillyoncrossexperiencecaicarasmg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidinesgroppomg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidipojucape"
                ,"*******************************:5432/bdzillyoncrossexperienceriogranders"
                ,"*******************************:5432/bdzillyoncrossexperienceuniditumbiarago"
                ,"*******************************:5432/bdzillyoncrossexperienceguapemg"
                ,"*******************************:5432/bdzillyoncrossexperienceilhacompridasp"
                ,"*******************************:5432/bdzillyoncrossexperienceunidnovaperuibesp"
                ,"*******************************:5432/bdzillyoncrossexperienceunidjdcaliforniasp"
                ,"*******************************:5432/bdzillyoncrossexperiencecatalaogo"
                ,"*******************************:5432/bdzillyoncrossexperienceouroverdeba"
                ,"*******************************:5432/bdzillyoncrossexperiencebatistaetaombamg"
                ,"*******************************:5432/bdzillyoncrossexperiencecentropatosdeminasmg"
                ,"*******************************:5432/bdzillyoncrossexperiencesantosdumontjuizdeforamg"
                ,"*******************************:5432/bdzillyoncrossexperiencejardimeuropamg"
                ,"*******************************:5432/bdzillyoncrossexperiencesantacruzcatalaogo"
                ,"*******************************:5432/bdzillyoncrossexperiencepanoramasalinasmg"
                ,"*******************************:5432/bdzillyoncrossexperiencevilajosecarlosmg"
                ,"*******************************:5432/bdzillyoncrossexperienceblumenausc"
                ,"*******************************:5432/bdzillyoncrossexperiencedompedritors"
                ,"*******************************:5432/bdzillyoncrossexperienceunidiisantaritadosapucaimg"
                ,"*******************************:5432/bdzillyoncrossexperiencevilaidealmg"
                ,"*******************************:5432/bdzillyoncrossexperiencepariqueraacusp"
                ,"*******************************:5432/bdzillyoncrossexperiencesantoamaropr"
                ,"*******************************:5432/bdzillyoncrossexperiencedomalmirmg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidpresidenteolegariomg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidosascosp"
                ,"*******************************:5432/bdzillyoncrossexperienceunidcaxambumg"
                ,"*******************************:5432/bdzillyoncrossexperiencepiresdoriogo"
                ,"*******************************:5432/bdzillyoncrossexperiencesantateclabage"
                ,"*******************************:5432/bdzillyoncrossexperienceunidcentropinheiros"
                ,"*******************************:5432/bdzillyoncrossexperienceunidsetebarrassp"
                ,"*******************************:5432/bdzillyoncrossexperienceunidcidadesatelite"
                ,"*******************************:5432/bdzillyoncrossexperiencemogidascruzessp"
                ,"*******************************:5432/bdzillyoncrossexperienceunidbairrogrande"
                ,"*******************************:5432/bdzillyoncrossexperienceuniduniversitarioituveravasp"
                ,"*******************************:5432/bdzillyoncrossexperienceunidjddashortenciaspocosdecaldasmg"
                ,"*******************************:5432/bdzillyoncrossexperiencebairrotocantinsmg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidjardimaeroporto"
                ,"*******************************:5432/bdzillyoncrossexperiencefortalezace"
                ,"*******************************:5432/bdzillyoncrossexperiencegranadauberlandiamg"
                ,"*******************************:5432/bdzillyoncrossexperiencealeixoam"
                ,"*******************************:5432/bdzillyoncrossexperienceunidjardimholandamg"
                ,"*******************************:5432/bdzillyoncrossexperienceleopoldinamg"
                ,"*******************************:5432/bdzillyoncrossexperiencejardimguanabaramt"
                ,"*******************************:5432/bdzillyoncrossexperiencevoltagranderj"
                ,"*******************************:5432/bdzillyoncrossexperienceunidrioclarosp"
                ,"*******************************:5432/bdzillyoncrossexperienceunidcapaobonito"
                ,"*******************************:5432/bdzillyoncrossexperienceunidteixeiradefreitas"
                ,"*******************************:5432/bdzillyoncrossexperienceunidsantoantoniodejesusba"
                ,"*******************************:5432/bdzillyoncrossexperienceunidcruziliamg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidbomjesusdosperdoessp"
                ,"*******************************:5432/bdzillyoncrossexperienceunidgloriamg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidborboletajuizdeforamg"
                ,"*******************************:5432/bdzillyoncrossexperiencemancapuruam"
                ,"*******************************:5432/bdzillyoncrossexperienceaterradorj"
                ,"*******************************:5432/bdzillyoncrossexperiencebarbosalagemg"
                ,"*******************************:5432/bdzillyoncrossexperiencecoqueiralmg"
                ,"*******************************:5432/bdzillyoncrossexperiencecastelomg"
                ,"*******************************:5432/bdzillyoncrossexperiencerecifepe"
                ,"*******************************:5432/bdzillyoncrossexperienceurbisba"
                ,"*******************************:5432/bdzillyoncrossexperiencesooretamaes"
                ,"*******************************:5432/bdzillyoncrossexperienceourobrancopr"
                ,"*******************************:5432/bdzillyoncrossexperiencetrindadeltdago"
                ,"*******************************:5432/bdzillyoncrossexperiencecruzdasalmasba"
                ,"*******************************:5432/bdzillyoncrossexperiencebethaniamg"
                ,"*******************************:5432/bdzillyoncrossexperiencecxshoppingparkmg"
                ,"*******************************:5432/bdzillyoncrossexperiencecurraisnovosrn"
                ,"*******************************:5432/bdzillyoncrossexperiencejdsaocarloscapelavinhedosp"
                ,"*******************************:5432/bdzillyoncrossexperiencesantoantonioarcossp"
                ,"*******************************:5432/bdzillyoncrossexperiencejardimalvoradapenapolissp"
                ,"*******************************:5432/bdzillyoncrossexperiencebordadamatamg"
                ,"*******************************:5432/bdzillyoncrossexperiencevilapintovarginhamg"
                ,"*******************************:5432/bdzillyoncrossexperiencealdeiasantarempa"
                ,"*******************************:5432/bdzillyoncrossexperiencedonatunicaparademinasmg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidbocainamaua"
                ,"*******************************:5432/bdzillyoncrossexperienceunidcentrocampestremg"
                ,"*******************************:5432/bdzillyoncrossexperiencejardimmotorama"
                ,"*******************************:5432/bdzillyoncrossexperienceunidedensorocaba"
                ,"*******************************:5432/bdzillyoncrossexperienceunidriopombamg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidsaomateusmontebelo"
                ,"*******************************:5432/bdzillyoncrossexperiencevoltaredondarj"
                ,"*******************************:5432/bdzillyoncrossexperienceunidsaojoaodaboavista"
                ,"*******************************:5432/bdzillyoncrossexperiencebomjardim"
                ,"*******************************:5432/bdzillyoncrossexperienceunidvicentepiresdf"
                ,"*******************************:5432/bdzillyoncrossexperienceunidtresmariasmg"
                ,"*******************************:5432/bdzillyoncrossexperienceunidibiocoraba"
                ,"*******************************:5432/bdzillyoncrossexperienceipiauba"
                ,"*******************************:5432/bdzillyoncrossexperiencetaboaosp"
                ,"*******************************:5432/bdzillyoncrossexperienceparelheiros"
                ,"*******************************:5432/bdzillyoncrossexperienceagualimparj"
                ,"*******************************:5432/bdzillyoncrossexperienceparkruralrs"
                ,"*******************************:5432/bdzillyoncrossexperiencearealrs"
                ,"*******************************:5432/bdzillyoncrossexperiencearthurthomaspr"
                ,"*******************************:5432/bdzillyoncrossexperienceresenderj"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencepatrociniomg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencepampulhaudimg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceaparecidasaopaulo"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencebarrierasba"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencecamboriusc"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencecontagemmg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencealtosdesantanasaojosedoscampossp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidibipora"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceunidsaulelkedin"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencesionvarginhamg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencepenharj"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceloteamentonanuqueba"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencecanaadoscarajas"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencejardimeldoradomg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperienceuberlandia"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencepequismg"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencejardimdagranjasp"
                ,"jdbc:postgresql://*************:5432/bdzillyoncrossexperiencesorrisomt"

        };

//        incluirProduto(urlsBancoOrigem, codigoProdutoOrigem, urlsBancos);
//        atualizarPlanosDuracaoPorDescricaoPlano(urlsBancoOrigem, descricaoPlanoOrigem, urlsBancos);
//        atualizarValorModalidadePorNome(urlsBancoOrigem, nomeModalidade, urlsBancos);
//        incluirModeloMensagemCRM(urlsBancoOrigem, codigoModeloMensagem, urlsBancos);
//        incluirPlano(urlsBancoOrigem, codigoPlano, urlsBancos);
//        inativarPlano(urlsBancoOrigem, codigoPlano, urlsBancos);
    }

    private static void incluirProduto(String urlBancoOrigem, Integer codigoProduto, String[] bancosDestino) throws Exception {
        Connection conOrigem = DriverManager.getConnection(urlBancoOrigem, "postgres", "pactodb");
        Produto produtoDAO = new Produto(conOrigem);
        ProdutoVO produtoOrigem = produtoDAO.consultarPorChavePrimaria(codigoProduto, Uteis.NIVELMONTARDADOS_TODOS);
        produtoDAO = null;

        for (String urlBanco : bancosDestino) {
            try (Connection con = DriverManager.getConnection(urlBanco, "postgres", "pactodb")) {
                Produto produtoDAODestino = new Produto(con);
                produtoDAODestino.incluir(produtoOrigem);
                produtoDAODestino = null;
                System.out.println("Produto inserido na url " + urlBanco);
            }
        }
    }

    private static void incluirPlano(String urlBancoOrigem, Integer codigoPlanoOrigem, String[] bancosDestino) throws Exception {
        Connection conOrigem = DriverManager.getConnection(urlBancoOrigem, "postgres", "pactodb");
        Plano planoDAO = new Plano(conOrigem);
        PlanoRedeEmpresa planoRedeEmpresaDAO = new PlanoRedeEmpresa(conOrigem);

        PlanoVO planoOrigem = planoDAO.consultarPorChavePrimaria(codigoPlanoOrigem, Uteis.NIVELMONTARDADOS_TODOS);
        for (String urlBancoDestino : bancosDestino) {
            PlanoRedeEmpresaVO planoRedeEmpresaVO = new PlanoRedeEmpresaVO();
            planoRedeEmpresaVO.setPlano(codigoPlanoOrigem);
            planoRedeEmpresaVO.setDatacadastro(Calendario.hoje());
            planoRedeEmpresaVO.setDataatualizacao(Calendario.hoje());

            try (Connection con = DriverManager.getConnection(urlBancoDestino, "postgres", "pactodb")) {
                String chaveBancoDestino = findKey(con);
                PlanoRedeEmpresaVO planoRedeEmpresaReplicado = planoRedeEmpresaDAO.consultarPorChavePlano(chaveBancoDestino, codigoPlanoOrigem, 1);
                if (planoRedeEmpresaReplicado != null) {
                    continue;
                }


                Plano planoDAODestino = new Plano(con);
                Modalidade modalidadeDAODestino = new Modalidade(con);
                Produto produtoDAODestino = new Produto(con);
                PlanoTextoPadrao planoTextoPadraoDAODestino = new PlanoTextoPadrao(con);
                Empresa empresaDAODestino = new Empresa(con);

                EmpresaVO empresaVO = empresaDAODestino.consultarPorCodigo(1, Uteis.NIVELMONTARDADOS_MINIMOS);

                PlanoVO planoDestinoVO = (PlanoVO) SerializationUtils.clone(planoOrigem);
                for (PlanoModalidadeVO planoModalidadeVO : planoDestinoVO.getPlanoModalidadeVOs()) {
                    ModalidadeVO modalidadeVO = modalidadeDAODestino.consultarPorNomeModalidadeAtivaComValor(planoModalidadeVO.getModalidade().getNome(), false, Uteis.NIVELMONTARDADOS_TODOS);
                    planoModalidadeVO.setModalidade(modalidadeVO);
                }
                try {
                    PlanoTextoPadraoVO planoTextoPadraoVO = planoTextoPadraoDAODestino.consultarPorChavePrimaria(planoDestinoVO.getPlanoTextoPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } catch (Exception e) {
                    List<PlanoTextoPadraoVO> vetResultado = planoTextoPadraoDAODestino.consultarPorCodigoSituacaoTipo(0, planoDestinoVO.getPlanoTextoPadrao().getSituacao(), planoDestinoVO.getPlanoTextoPadrao().getTipoContrato(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    planoDestinoVO.setPlanoTextoPadrao(vetResultado.get(vetResultado.size() - 1));
                }


                List novoPlanoProdutoSugeridoVOs = new ArrayList();
                for (Object object : planoDestinoVO.getPlanoProdutoSugeridoVOs()) {
                    PlanoProdutoSugeridoVO planoProdutoSugeridoVO = (PlanoProdutoSugeridoVO) object;
                    if (!planoProdutoSugeridoVO.getProduto().getTipoProduto().equals("TA") && !planoProdutoSugeridoVO.getProduto().getTipoProduto().equals("TD")) {
                        ProdutoVO produtoVO = produtoDAODestino.criarOuConsultarProdutoPorTipo(planoProdutoSugeridoVO.getProduto().getTipoProduto(), planoProdutoSugeridoVO.getProduto().getDescricao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        planoProdutoSugeridoVO.setProduto(produtoVO);
                        novoPlanoProdutoSugeridoVOs.add(planoProdutoSugeridoVO);
                    }
                }
                planoDestinoVO.setPlanoProdutoSugeridoVOs(novoPlanoProdutoSugeridoVOs);


                planoDAODestino.incluir(planoDestinoVO);

                planoRedeEmpresaVO.setPlanoReplicado(planoDestinoVO.getCodigo());
                planoRedeEmpresaVO.setChave(chaveBancoDestino);
                planoRedeEmpresaVO.setNomeUnidade(empresaVO.getNome());
                planoRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(Calendario.hoje()) + " (PROCESSO PACTO)");

                planoDAODestino = null;
                modalidadeDAODestino = null;
                produtoDAODestino = null;

                System.out.println("Plano inserido na url " + urlBancoDestino);
            } catch (Exception e) {
                System.out.println("Erro ao criar plano no banco " + urlBancoDestino + ". Erro: " + e.getMessage());
            }

            planoRedeEmpresaDAO.inserir(planoRedeEmpresaVO);
        }
        planoDAO = null;
    }

    private static String findKey(Connection con) throws SQLException {
        String obterComandos = "select chave from dadosgerencialpmg d order by codigo limit 1";
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(obterComandos);
        String key = "";
        if (rs.next()) {
            key = rs.getString("chave");
        }
        return key;
    }

    private static void atualizarPlanosDuracaoPorDescricaoPlano(String urlBancoOrigem, String descricaoPlano, String[] bancosDestino) throws Exception {
        Connection conOrigem = DriverManager.getConnection(urlBancoOrigem, "postgres", "pactodb");
        Plano planoDAO = new Plano(conOrigem);
        PlanoVO planoOrigem = planoDAO.consultarPorDescricao(descricaoPlano, Uteis.NIVELMONTARDADOS_TODOS);
        planoDAO = null;

        for (String urlBanco : bancosDestino) {
            try (Connection con = DriverManager.getConnection(urlBanco, "postgres", "pactodb")) {
                Plano planoDAODestino = new Plano(con);
                PlanoDuracao planoDuracaoDAODestino = new PlanoDuracao(con);
                PlanoModalidade planoModalidadeDAODestino = new PlanoModalidade(con);

                PlanoVO planoUnidade = planoDAODestino.consultarPorDescricao(descricaoPlano, Uteis.NIVELMONTARDADOS_TODOS);
                if (planoUnidade != null && planoUnidade.getCodigo() > 0) {
                    for (PlanoDuracaoVO planoDuracaoOrigemVO : planoOrigem.getPlanoDuracaoVOs()) {
                        boolean incluirDuracao = true;
                        for (PlanoDuracaoVO planoDuracaoDestinoVO : planoUnidade.getPlanoDuracaoVOs()) {
                            if (planoDuracaoDestinoVO.getNumeroMeses().equals(planoDuracaoOrigemVO.getNumeroMeses())) {
                                incluirDuracao = false;
                                planoDuracaoDestinoVO.setValorDesejadoMensal(planoDuracaoOrigemVO.getValorDesejadoMensal());
                                planoDuracaoDestinoVO.setValorDesejadoParcela(planoDuracaoOrigemVO.getValorDesejadoParcela());
                                planoDuracaoDestinoVO.setValorDesejado(planoDuracaoOrigemVO.getValorDesejado());
                                planoDuracaoDestinoVO.setValorEspecifico(planoDuracaoOrigemVO.getValorEspecifico());
                                planoDuracaoDAODestino.alterar(planoDuracaoDestinoVO);
                                System.out.println("PlanoDuração de " + planoDuracaoDestinoVO.getNumeroMeses() + " atualizado no plano " + planoUnidade.getDescricao());
                            }
                        }
                        if (incluirDuracao) {
                            PlanoDuracaoVO planoDuracaoDestinoVO = (PlanoDuracaoVO) planoDuracaoOrigemVO.getClone(true);
                            planoDuracaoDestinoVO.setPlano(planoUnidade.getCodigo());
                            planoDuracaoDAODestino.incluir(planoDuracaoDestinoVO);
                            System.out.println("PlanoDuração de " + planoDuracaoDestinoVO.getNumeroMeses() + " incluído no plano " + planoUnidade.getDescricao());
                        }
                    }

                    for (PlanoModalidadeVO planoModalidadeOrigemVO : planoOrigem.getPlanoModalidadeVOs()) {
                        for (PlanoModalidadeVO planoModalidadeDestinoVO : planoUnidade.getPlanoModalidadeVOs()) {
                            if (planoModalidadeDestinoVO.getModalidade().getNome().equals(planoModalidadeOrigemVO.getModalidade().getNome())) {
                                boolean atualizarPlanoModalidade = false;
                                for (Object objectPmvsOrigemVO : planoModalidadeOrigemVO.getPlanoModalidadeVezesSemanaVOs()) {
                                    PlanoModalidadeVezesSemanaVO pmvsOrigemVO = (PlanoModalidadeVezesSemanaVO) objectPmvsOrigemVO;
                                    for (Object objectPmvsDestinoVO : planoModalidadeDestinoVO.getPlanoModalidadeVezesSemanaVOs()) {
                                        PlanoModalidadeVezesSemanaVO pmvsDestinoVO = (PlanoModalidadeVezesSemanaVO) objectPmvsDestinoVO;

                                        if (pmvsDestinoVO.getNrVezes().equals(pmvsOrigemVO.getNrVezes()) ||
                                                (planoModalidadeOrigemVO.getPlanoModalidadeVezesSemanaVOs().size() == 1 && planoModalidadeDestinoVO.getPlanoModalidadeVezesSemanaVOs().size() == 1)) {
                                            atualizarPlanoModalidade = true;
                                            pmvsDestinoVO.setTipoOperacao(pmvsOrigemVO.getTipoOperacao());
                                            pmvsDestinoVO.setTipoValor(pmvsOrigemVO.getTipoValor());
                                            pmvsDestinoVO.setValorEspecifico(pmvsOrigemVO.getValorEspecifico());
                                            pmvsDestinoVO.setPercentualDesconto(pmvsOrigemVO.getPercentualDesconto());
                                            pmvsDestinoVO.setReferencia(pmvsOrigemVO.isReferencia());
                                            if (planoModalidadeOrigemVO.getPlanoModalidadeVezesSemanaVOs().size() == 1) {
                                                pmvsDestinoVO.setNrVezes(pmvsOrigemVO.getNrVezes());
                                            }
                                            System.out.println("PlanoModalidadeVezesSemana de " + pmvsDestinoVO.getNrVezes() + " atualizado no planoModalidade " + planoModalidadeDestinoVO.getModalidade().getNome());
                                        }
                                    }
                                }
                                if (atualizarPlanoModalidade) {
                                    planoModalidadeDestinoVO.montarNrVezesSemana();
                                    planoModalidadeDAODestino.alterar(planoModalidadeDestinoVO);
                                }
                            }
                        }
                    }
                    System.out.println("Plano " + planoUnidade.getDescricao() + " (" + planoUnidade.getCodigo() + ") ajustado em url " + urlBanco);
                }
            }
        }
    }

    private static void atualizarValorModalidadePorNome(String urlBancoOrigem, String nomeModalidade, String[] bancosDestino) throws Exception {
        Connection conOrigem = DriverManager.getConnection(urlBancoOrigem, "postgres", "pactodb");
        Modalidade modalidadeDAO = new Modalidade(conOrigem);
        ModalidadeVO modalidadeOrigem = modalidadeDAO.consultarPorNomeModalidade(nomeModalidade, false, Uteis.NIVELMONTARDADOS_TODOS);
        modalidadeDAO = null;

        for (String urlBanco : bancosDestino) {
            try (Connection con = DriverManager.getConnection(urlBanco, "postgres", "pactodb")) {
                Modalidade modalidadeDAODestino = new Modalidade(con);
                ModalidadeVO modalidadeDestino = modalidadeDAODestino.consultarPorNomeModalidade(nomeModalidade, false, Uteis.NIVELMONTARDADOS_TODOS);
                if (modalidadeDestino != null && modalidadeDestino.getCodigo() > 0) {
                    modalidadeDestino.setValorMensal(modalidadeOrigem.getValorMensal());
                    modalidadeDAODestino.alterar(modalidadeDestino);
                    System.out.println("Modalidade " + modalidadeDestino.getNome() + ") ajustado em url " + urlBanco);
                }
            }
        }
    }

    private static void incluirModeloMensagemCRM(String urlBancoOrigem, Integer codigoModeloMensagem, String[] bancosDestino) throws Exception {
        Connection conOrigem = DriverManager.getConnection(urlBancoOrigem, "postgres", "pactodb");
        ModeloMensagem modeloMensagemDAO = new ModeloMensagem(conOrigem);
        ModeloMensagemVO modeloMensagemOrigem = modeloMensagemDAO.consultarPorChavePrimaria(codigoModeloMensagem, Uteis.NIVELMONTARDADOS_TODOS);
        modeloMensagemDAO = null;

        for (String urlBanco : bancosDestino) {
            try (Connection con = DriverManager.getConnection(urlBanco, "postgres", "pactodb")) {
                ModeloMensagem modeloMensagemDAODestino = new ModeloMensagem(con);
                modeloMensagemDAODestino.incluir(modeloMensagemOrigem);
                modeloMensagemDAODestino = null;
                System.out.println("Modelo de mensagem  " + modeloMensagemOrigem.getTitulo() + " inserido em url " + urlBanco);

            }
        }
    }

    private static void ajustarSequenciasPostgres(String[] bancosDestino) throws Exception {
        String obterComandos = "SELECT 'SELECT setval(''' || n.nspname || '.' ||\n" +
                "replace(replace(replace(replace(replace(replace(replace(replace(a.adsrc,\n" +
                "'(',''),')',''),'::',''),'textregclass',''),'nextval',''),'regclass',''),'''',''),\n" +
                "n.nspname||'.','')||''',(SELECT coalesce(MAX('||ab.attname||'),1) FROM '||\n" +
                "n.nspname|| '.'||c.relname||'),true);' as seqname\n" +
                "FROM pg_class c\n" +
                "JOIN pg_attrdef a ON c.oid=a.adrelid\n" +
                "JOIN pg_namespace n ON c.relnamespace = n.oid AND n.nspname NOT LIKE 'pg_%'\n" +
                "JOIN pg_index i ON i.indrelid=c.oid AND i.indisprimary='t'\n" +
                "JOIN pg_attribute ab ON ab.attrelid=c.oid AND ab.attisdropped='f' AND ab.atthasdef='t' AND i.indkey[0]=ab.attnum AND i.indkey[1] IS NULL\n" +
                "Where a.adsrc like 'nextval%';";
        for (String urlBanco : bancosDestino) {
            try (Connection con = DriverManager.getConnection(urlBanco, "postgres", "pactodb")) {
                Statement stm = con.createStatement();
                ResultSet rs = stm.executeQuery(obterComandos);
                List<String> updateNextVal = new ArrayList<>();
                while (rs.next()) {
                    updateNextVal.add(rs.getString("seqname"));
                }

                for (String sql : updateNextVal) {
                    stm = con.createStatement();
                    stm.execute(sql);
                }
            }
        }

    }

    private static void inativarPlano(String urlBancoOrigem, Integer codigoPlanoOrigem, String[] bancosDestino) throws Exception {
        Connection conOrigem = DriverManager.getConnection(urlBancoOrigem, "postgres", "pactodb");
        Plano planoDAO = new Plano(conOrigem);
        PlanoVO planoOrigem = planoDAO.consultarPorChavePrimaria(codigoPlanoOrigem, Uteis.NIVELMONTARDADOS_TODOS);
        for (String urlBanco : bancosDestino) {
            try (Connection con = DriverManager.getConnection(urlBanco, "postgres", "pactodb")) {
                Plano planoDAODestino = new Plano(con);
                PlanoVO planoDestinoVO = planoDAODestino.consultarPorDescricao(planoOrigem.getDescricao(), Uteis.NIVELMONTARDADOS_TODOS);
                if(planoDestinoVO != null && UteisValidacao.notEmptyNumber(planoDestinoVO.getCodigo())){
                    if(Calendario.igual(planoDestinoVO.getVigenciaAte() , planoOrigem.getVigenciaAte())){
                        Date novaVigencia = Uteis.somarDias(Calendario.hoje(), -1);
                        SuperFacadeJDBC.executarUpdate("update plano set descricao = '"+planoDestinoVO.getDescricao()+"(INATIVO)', vigenciaate = '"+Uteis.getDataFormatoBD(novaVigencia)+"', ingressoate = '"+Uteis.getDataFormatoBD(novaVigencia)+"' where codigo = "+planoDestinoVO.getCodigo(), con);
                    } else {
                        throw new Exception("plano com a descrição "+ planoOrigem.getDescricao() + " tem vencimento divergente da origem: "+Uteis.getData(planoDestinoVO.getVigenciaAte()));
                    }
                } else {
                    throw new Exception("plano com a descrição "+ planoOrigem.getDescricao() + " não encontrado");
                }

                System.out.println("Plano inativado na url " + urlBanco);
            } catch (Exception e) {
                System.out.println("Erro ao inativar plano no banco "+urlBanco+". Erro: " + e.getMessage());
            }
        }
        planoDAO = null;
    }
}

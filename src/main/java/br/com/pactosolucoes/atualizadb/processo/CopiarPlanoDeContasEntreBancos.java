package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.PlanoConta;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class CopiarPlanoDeContasEntreBancos {

    public static void main(String... args) throws Exception {
        //será copiado o plano de contas de um banco para o outro
        //será apagado o relacionamento com as contas já lançadas, e rateio integração será excluido!

        Connection conOrigem = DriverManager.getConnection("*********************************************************************", "postgres", "pactodb");
        Connection conDestino = DriverManager.getConnection("*********************************************************************", "postgres", "pactodb");

        copiarPlanoContas(conOrigem, conDestino);
    }

    private static void copiarPlanoContas(Connection conOrigem, Connection conDestino) throws Exception {
        PlanoConta planoContaDAO;
        PlanoConta planoContaDAODest;
        try {
            conDestino.setAutoCommit(false);

            planoContaDAO = new PlanoConta(conOrigem);
            planoContaDAODest = new PlanoConta(conDestino);

            excluirPlanoAtualRemoverRelacionamentos(conDestino);

            List<PlanoContaTO> lista = planoContaDAO.consultarTodos();

            int atual = 0;
            for (PlanoContaTO obj : lista) {
                Uteis.logarDebug(++atual + "/" + lista.size() + " - Importando... " + obj.getDescricao());
                planoContaDAODest.incluirSemCommit(obj);
            }

            atualizar(conDestino);

            conDestino.commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            conDestino.rollback();
        } finally {
            conDestino.setAutoCommit(true);
            planoContaDAO = null;
            planoContaDAODest = null;
        }
    }

    private static void excluirPlanoAtualRemoverRelacionamentos(Connection conDestino) throws Exception {
        try {
//            conDestino.setAutoCommit(false);

            String dataHora = Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMddHHmmss");
            String tabelaBackup = ("planoContas" + dataHora);

            List<String> listaSQL = new ArrayList<>();
            listaSQL.add("create table " + tabelaBackup + " as table planoconta;");
            listaSQL.add("alter table fornecedor add column backup_planoconta integer;");
            listaSQL.add("update fornecedor set backup_planoconta  = planoconta;");
            listaSQL.add("update fornecedor set planoconta = null;");
            listaSQL.add("alter table movcontarateio add column backup_planoconta integer;");
            listaSQL.add("alter table movcontarateio add column backup_planoconta_descricao CHARACTER VARYING;");
            listaSQL.add("update movcontarateio set backup_planoconta  = planoconta;");
            listaSQL.add("update movcontarateio set backup_planoconta_descricao = (select codigoplanocontas || ' - ' || nome from planoconta where codigo = movcontarateio.planoconta);");
            listaSQL.add("update movcontarateio set planoconta  = null;");
            listaSQL.add("update configuracaofinanceiro set planocontastaxaboleto  = null, planocontastaxa  = null, planocontasdevolucao  = null;");
            listaSQL.add("delete from rateiointegracao;");
            listaSQL.add("delete from planoconta;");

            for (String sql : listaSQL) {
                String sqlExe = "DO $$ \n" +
                        "    BEGIN\n" +
                        "        BEGIN\n" +
                        "            " + sql + " \n" +
                        "        EXCEPTION\n" +
                        "            WHEN duplicate_column THEN RAISE NOTICE 'Erro sql " + sql.substring(0, 20) + "';\n" +
                        "        END;\n" +
                        "    END;\n" +
                        "$$";
                SuperFacadeJDBC.executarConsulta(sqlExe, conDestino);
            }
//            conDestino.commit();
        } catch (Exception ex) {
            ex.printStackTrace();
//            conDestino.rollback();
            throw ex;
        } finally {
//            conDestino.setAutoCommit(true);
        }
    }

    private static void atualizar(Connection conDestino) {
        Set<String> atualiza = new HashSet<>();
        atualiza.add("DO $$ \n" +
                "    BEGIN\n" +
                "        BEGIN\n" +
                "            ALTER TABLE planoconta ADD COLUMN nomeconsulta text;\n" +
                "        EXCEPTION\n" +
                "            WHEN duplicate_column THEN RAISE NOTICE 'Coluna nomeconsulta já existe';\n" +
                "        END;\n" +
                "    END;\n" +
                "$$");
        atualiza.add("UPDATE planoconta SET nomeconsulta = remove_acento_upper(nome);");
        for (String update : atualiza) {
            try {
                SuperFacadeJDBC.executarConsulta(update, conDestino);
            } catch (Exception ignored) {
            }
        }
    }
}
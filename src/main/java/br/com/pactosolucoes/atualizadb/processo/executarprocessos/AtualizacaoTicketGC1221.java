package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "10/12/2024",
        descricao = "GC-1221 - Integração ManyChat",
        motivacao = "GC-1221 - Integração ManyChat")
public class AtualizacaoTicketGC1221 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.clienteredeempresa (\n" +
                    "\tcodigo serial4 NOT NULL,\n" +
                    "\tcodigomatricula int4,\n" +
                    "\tnome varchar(80) NOT NULL,\n" +
                    "\tcpf bpchar(11) NOT NULL,\n" +
                    "\tchaveEmpresa varchar(50),\n" +
                    "\tcodigoEmpresa int4 NOT NULL,\n" +
                    "\tnomeEmpresa varchar(80) NOT NULL,\n" +
                    "\tdataSincronizacao timestamp NOT NULL,\n" +
                    "\tCONSTRAINT clienteredeempresa_pkey PRIMARY KEY (codigo)\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_clienteredeempresa_chaveempresa ON public.clienteredeempresa USING btree (chaveempresa);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_clienteredeempresa_cpf ON public.clienteredeempresa USING btree (cpf);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cliente ADD COLUMN dataSincronizacaoFranqueadora timestamp;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cliente ADD COLUMN dataSincronizacaoManyChat timestamp;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE cliente ADD COLUMN idManyChat int8;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN integracaoManyChatHabilitada BOOLEAN DEFAULT false;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN integracaoManyChatTokenApi varchar(255);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN integracaoManyChatTagUnidade varchar(255);", c);
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class AjustarVinculosParcelasRenegociadas {
    public static void main(String... args) {
        try {
           // Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "mega");
            Connection c = DriverManager.getConnection("**********************************************", "zillyonweb", "pactodb");
            Conexao.guardarConexaoForJ2SE(c);
            ajustarProdutosParcelas(c);

        } catch (Exception ex) {
            Logger.getLogger(RefazerVinculoMovProdutoParcelaContratos.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public static void ajustarParcelas(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        ajustarProdutosParcelas(con);
    }

    public static void ajustarProdutosParcelas(Connection con) throws Exception {
        String consultaProdutos = "select situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, \n" +
                    "anoreferencia, mesreferencia, responsavellancamento, datalancamento, \n" +
                    "valordesconto, precounitario, quantidade, descricao, \n" +
                    "empresa, pessoa, contrato, produto, naogerarmensagem, \n" +
                    "pacote, descricaomovprodutomodalidade, movpagamentocc, lancamentocoletivo, \n" +
                    "vendaavulsa, pro.codigo,totalfinal, coalesce(sum(valorpago),0) as valorvinculado \n" +
                    "from movproduto pro left join movprodutoparcela mov on mov.movproduto = pro.codigo  where %s group by \n" +
                    "situacao, quitado, datafinalvigencia, datainiciovigencia, apresentarmovproduto, \n" +
                    "anoreferencia, mesreferencia, responsavellancamento, datalancamento, \n" +
                    "totalfinal, valordesconto, precounitario, quantidade, descricao, \n" +
                    "empresa, pessoa, contrato, produto, naogerarmensagem, \n" +
                    "pacote, descricaomovprodutomodalidade, movpagamentocc, lancamentocoletivo, \n" +
                    "vendaavulsa, pro.codigo having  totalfinal - coalesce(sum(valorpago),0) > 0.5";
        String where = "";

        
        ResultSet consultaParcelas = SuperFacadeJDBC.criarConsulta("select par.* from movparcela par left join movprodutoparcela mov on mov.movparcela = par.codigo  "
                + "where descricao = 'PARCELA RENEGOCIADA' and mov.codigo is null and situacao not in ('RG', 'CA')  and valorparcela > 1  "
                + "and dataregistro > '2015-06-08 23:59:00'order by pessoa,par.codigo", con); 
        List<MovParcelaVO> listaParcelas = MovParcela.montarDadosConsulta(consultaParcelas, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
        StringBuffer sbErros = new StringBuffer("Resumo de Erros: \n");
        Map<Integer, List<MovParcelaVO>> pessoaParcela = new HashMap<Integer, List<MovParcelaVO>>();
        for(MovParcelaVO parcela: listaParcelas){
            parcela.setMovProdutoParcelaVOs(new ArrayList());
            if(!pessoaParcela.containsKey(parcela.getPessoa().getCodigo())){
                pessoaParcela.put(parcela.getPessoa().getCodigo(), new ArrayList<MovParcelaVO>());
            }
            pessoaParcela.get(parcela.getPessoa().getCodigo()).add(parcela);
        }
                
        List<MovProdutoVO> listaProdutos;
        List<Integer> recibos = new ArrayList<Integer>();
        ProdutosPagosServico produtosPagosServico = new ProdutosPagosServico();
        boolean todasProcessadas = false;
        for (Integer codPessoa: pessoaParcela.keySet()) {
            try{
                con.setAutoCommit(false);
                todasProcessadas = false; 
                boolean isContrato = true;
                while (!todasProcessadas){
                    List<MovParcelaVO> parcelasProcessadas = new ArrayList<MovParcelaVO>();
                    listaProdutos = new ArrayList<MovProdutoVO>();
                    for(MovParcelaVO parcela: pessoaParcela.get(codPessoa)){
                        if(parcelasProcessadas.isEmpty()){
                            isContrato = !UteisValidacao.emptyNumber(parcela.getContrato().getCodigo());
                            parcelasProcessadas.add(parcela);
                        } else {
                            if((isContrato && parcela.getContrato().getCodigo().equals(parcelasProcessadas.get(0).getContrato().getCodigo())) 
                                    || (!isContrato && parcela.getVendaAvulsaVO().getCodigo().equals(parcelasProcessadas.get(0).getVendaAvulsaVO().getCodigo()))){
                                continue;
                            }else{
                                parcelasProcessadas.add(parcela);
                            }
                        }
                        if(isContrato){
                            where = "contrato = "+parcela.getContrato().getCodigo();
                        } else {
                             where = "vendaavulsa = "+parcela.getVendaAvulsaVO().getCodigo();
                        }
                        ResultSet rsProdutos = SuperFacadeJDBC.criarConsulta(String.format(consultaProdutos, where),con);
                        while (rsProdutos.next()){
                             MovProdutoVO pro = MovProduto.montarDados(rsProdutos, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                             pro.setQuitado(false);
                             pro.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(rsProdutos.getDouble("totalfinal") - rsProdutos.getDouble("valorvinculado")));
                             pro.setMovProdutoParcelaVOs(new ArrayList<MovProdutoParcelaVO>());
                             listaProdutos.add(pro);
                        }

                    }
                    for (MovParcelaVO movParcelaVO : parcelasProcessadas) {
                        movParcelaVO.setMovProdutoParcelaVOs(new ArrayList());
                        getFacade().getZWFacade().dividirProdutosNasParcelas(listaProdutos, movParcelaVO);
                        if(movParcelaVO.getSituacao().equals("PG")){
                            ResultSet reciboPar = SuperFacadeJDBC.criarConsulta("select recibopagamento  from pagamentomovparcela  where movparcela  = " + movParcelaVO.getCodigo(), con);
                            if(reciboPar.next()){
                                for (Iterator it = movParcelaVO.getMovProdutoParcelaVOs().iterator(); it.hasNext();) {
                                    MovProdutoParcelaVO movPro = (MovProdutoParcelaVO) it.next();
                                    movPro.getReciboPagamento().setCodigo(reciboPar.getInt("recibopagamento"));
                                }
                                if(!recibos.contains(reciboPar.getInt("recibopagamento"))){
                                    recibos.add(reciboPar.getInt("recibopagamento"));
                                }
                            }
                        }
                        getFacade().getMovProdutoParcela().incluirMovProdutoParcelas(movParcelaVO.getCodigo(), movParcelaVO.getMovProdutoParcelaVOs());
                        pessoaParcela.get(codPessoa).remove(movParcelaVO);
                    }
                    for (MovProdutoVO produto : listaProdutos){
                        ResultSet produtoPago = SuperFacadeJDBC.criarConsulta("select totalfinal,sum(valorpago) as valorpago from  movproduto pro left join movprodutoparcela mov on mov.movproduto = pro.codigo  where recibopagamento is not null and pro.codigo = "+ produto.getCodigo()+" group by totalfinal",con);
                        if(produtoPago.next() && (Uteis.arredondarForcando2CasasDecimais(produtoPago.getDouble("totalfinal")) == Uteis.arredondarForcando2CasasDecimais(produtoPago.getDouble("valorpago")))){
                            SuperFacadeJDBC.executarConsultaUpdate("update movproduto set situacao = 'PG' where codigo ="+ produto.getCodigo(),con);
                        }
                    }
                    if(pessoaParcela.get(codPessoa).isEmpty()){
                        todasProcessadas = true;
                    }


                }
                con.commit();
            } catch (Exception e ){
                con.rollback();
                con.setAutoCommit(true);
                Logger.getLogger(RefazerVinculoMovProdutoParcelaContratos.class.getName()).log(Level.SEVERE, null, e);
            } finally {
                con.setAutoCommit(true);
            }
        }
        for (Integer recibo : recibos){
            produtosPagosServico.setarProdutosPagos(con, recibo);
        }
            
    }
}

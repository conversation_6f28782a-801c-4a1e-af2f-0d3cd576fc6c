package br.com.pactosolucoes.atualizadb.processo;

import java.sql.*;
import java.text.DecimalFormat;
import java.util.List;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.basico.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.*;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

public class ImportarCadastrosClientesWeb {

    public static void main(String[] args) {
        try {
            /* INFORMAÇÕES DE CONFIGURAÇÃO
             *
             * EXEMPLO DE URL CONEXÃO: "***************************************"
             *
             * INFORMAÇÕES DO BANCO DE ORIGEM:
             *
             * args[0] = BANCO QUE IRÁ FORNECER OS CLIENTES PARA IMPORTAR
             * args[1] = CODIGO DA EMPRESA QUE TODOS SEUS CLIENTE SERÃO IMPORTADOR PARA O BANCO DESTINO
             *
             * INFORMAÇÕES DO BANCO DE DESTINO:
             *
             * args[3] = BANCO PARA ONDE OS CLIENTES SERÃO IMPORTADOS
             * args[4] = BANCO PARA ONDE OS CLIENTES SERÃO IMPORTADOS
             */

            if (args.length == 4) {

                String urlBancoOrigem = args[0];
                String empresaOrigem = args[1];
                String urlBancoDestino = args[2];
                String empresaDestino = args[3];

                Uteis.logar(null, "Configurações:");
                Uteis.logar(null, "urlBancoOrigem: " + urlBancoOrigem);
                Uteis.logar(null, "empresaOrigem: " + empresaOrigem);
                Uteis.logar(null, "urlBancoDestino: " + urlBancoDestino);
                Uteis.logar(null, "empresaDestino: " + empresaDestino);

                Connection conOrigem = DriverManager.getConnection(urlBancoOrigem, "zillyonweb", "pactodb");
                Connection conDestino = DriverManager.getConnection(urlBancoDestino, "zillyonweb", "pactodb");
                importarClientes(conOrigem, Integer.parseInt(empresaOrigem), conDestino, Integer.parseInt(empresaDestino));

            } else {
                Uteis.logar(null, "Parametros não informados: 1º urlBancoOrigem // 2º empresaOrigem // 3º urlBancoDestino // 4º empresaDestino");
            }
        } catch (Exception e) {
            Uteis.logar(null, e.getMessage());
        }
    }

    private static void importarClientes(Connection conOrigem, Integer empresaOrigem, Connection conDestino, Integer empresaDestino) throws Exception {

        //AJUSTAR NUMERO DA MATRICULA ATUAL
        PreparedStatement sqlMatricula = conOrigem.prepareStatement("update numeromatricula set matricula = (select max(codigomatricula) from cliente)");
        sqlMatricula.execute();

        //DELETAR TELEFONES INVALIDOS
        PreparedStatement sqlDeletar = conOrigem.prepareStatement("delete from telefone where numero = ''");
        sqlDeletar.execute();

        Cliente clienteAntigo = new Cliente(conOrigem);
        List<ClienteVO> clientes = clienteAntigo.consultar("empresa = " + empresaOrigem, Uteis.NIVELMONTARDADOS_MINIMOS);

        ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(conDestino);
        ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        Empresa emp = new Empresa(conDestino);
        EmpresaVO empresaDestinoVO = emp.consultarPorChavePrimaria(empresaDestino, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        Colaborador col = new Colaborador(conDestino);
        ColaboradorVO consultor = col.consultarPorNomeColaborador("PACTO - M", empresaDestino, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        VinculoVO vinculo = new VinculoVO();
        vinculo.setColaborador(consultor);
        vinculo.setNovoObj(true);
        vinculo.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());

        Pessoa p = new Pessoa(conOrigem);

        Cliente clienteDaoDestino = new Cliente(conDestino);
        ZillyonWebFacade facade = new ZillyonWebFacade(conDestino);

        Cidade cidade = new Cidade(conDestino);
        int naoImportado = 1;
        int contador = 1;
        for (ClienteVO clienteOrigem : clientes) {
            Uteis.logar(null, "Atual " + contador++ + "/" + clientes.size());

            ClienteVO clienteNovoDestino = (ClienteVO) clienteOrigem.getClone(true);

            //LIMPAR CATEGORIA
            clienteNovoDestino.getCategoria().setCodigo(0);
            clienteNovoDestino.setCodigo(0);

            clienteNovoDestino.setIdExterno(clienteOrigem.getCodigoMatricula().longValue());

            try {

                String sql = "select codigo from cliente where idexterno = " + clienteNovoDestino.getIdExterno();
                ResultSet rs = conDestino.prepareStatement(sql).executeQuery();
                if (rs.next()) {
                    Uteis.logar(null, "Cliente já importado: " + clienteNovoDestino.getPessoa().getNome());
                    continue;
                }

                clienteNovoDestino.setEmpresa(empresaDestinoVO);
                clienteNovoDestino.setPessoa(p.consultarPorChavePrimaria(clienteNovoDestino.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));

                CidadeVO novaCidadeVO = cidade.consultarPorNomeCidadeSiglaEstado(clienteNovoDestino.getPessoa().getCidade().getNomeSemAcento(), clienteNovoDestino.getPessoa().getCidade().getEstado().getSigla());
                clienteNovoDestino.getPessoa().setCidade(novaCidadeVO);
                clienteNovoDestino.getPessoa().setEstadoVO(novaCidadeVO.getEstado());
                clienteNovoDestino.getPessoa().setPais(novaCidadeVO.getPais());

                vinculo.setCliente(clienteNovoDestino);
                clienteNovoDestino.getPessoa().setTipoPessoa("CLI");
                clienteNovoDestino.getVinculoVOs().add(vinculo);

                //APAGAR A MATRICULA PARA O SISTEMA GERAR UMA NOVA
                clienteNovoDestino.setMatricula("");

                //gerar a matricula do aluno
                clienteDaoDestino.gerarNumeroMatricula(clienteNovoDestino, empresaDestinoVO, configuracaoSistemaVO);

                //incluir aluno.. pessoa... etc...
                clienteDaoDestino.incluirClienteSimplificadoImportacao(clienteNovoDestino);

                //atualizar numero de matricula
                clienteDaoDestino.atualizarMatriculaAluno(clienteNovoDestino.getCodigoMatricula());

                //criar o sintético
                facade.atualizarSintetico(clienteNovoDestino, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);

                //setar o vinculo como novo
                vinculo.setNovoObj(true);
                vinculo.setCodigo(0);

            } catch (Exception e) {
                naoImportado++;
                Uteis.logar(null, "Aluno não importado " + clienteNovoDestino.getPessoa().getNome() + " --- ERRO: " + e.getMessage());
            }

        }
        Uteis.logar(null, "###### TOTAL ALUNOS QUE NÃO FORAM IMPORTADOS: " + naoImportado);

    }

    private static void gerarCodAcesso(Connection con) throws SQLException {
        ResultSet dados = con.prepareStatement("select codigo from cliente where codacesso  is null").executeQuery();
        while (dados.next()) {
            Integer codigoCliente = dados.getInt("codigo");

            DecimalFormat idFormat = new DecimalFormat("00000");
            String numero = TipoAcessoEnum.TA_ALUNO.getId() + idFormat.format(codigoCliente) + "00" + "0";
            Integer dv = Uteis.gerarDV(numero, 0);
            String codAcesso = numero + dv;

            String updateFecharMetaDetalhado = "update cliente set codacesso = '"+codAcesso+"' where codigo = " + codigoCliente + " ;";
            System.out.println(updateFecharMetaDetalhado);
        }
    }

}

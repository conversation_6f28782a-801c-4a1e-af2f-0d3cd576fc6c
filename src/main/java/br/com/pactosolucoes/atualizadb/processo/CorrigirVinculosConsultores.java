package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.basico.HistoricoVinculoVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.HistoricoVinculo;
import negocio.facade.jdbc.basico.Vinculo;

public class CorrigirVinculosConsultores {
	public static void main(String[] args) {
		try {
			Connection con = DriverManager.getConnection("********************************************************************", "zillyonweb", "pactodb");
			corrigirVinculos(con);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private static void corrigirVinculos(Connection con) throws Exception{
		String sql = "select cliente,count(cliente) from vinculo where tipovinculo = 'CO' group by cliente having count(cliente) > 1";
		ResultSet query = con.prepareStatement(sql).executeQuery();
		int i = 1;
		Vinculo v = new Vinculo(con);
		HistoricoVinculo hv = new HistoricoVinculo(con);
		while(query.next()){
			System.out.println( i++ + " - Atualizados dados de cliente = "+query.getInt("cliente"));
			List<VinculoVO>listaVinculos = v.consultarPorClienteTipoVinculo(query.getInt("cliente"), TipoColaboradorEnum.CONSULTOR.getSigla(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
			Collections.reverse(listaVinculos);
			Boolean atual = true;
			Date dataSaida = null;
			Date dataEntrada = null; 
			for(VinculoVO vinculo : listaVinculos ){
				if (atual){
					String sqlhorafim = "select max(dataregistro) as datafim from historicovinculo where tipocolaborador = 'CO' and cliente ="+vinculo.getCliente().getCodigo()
					+"AND tipohistoricovinculo = 'EN' AND colaborador = "+vinculo.getColaborador().getCodigo();
					ResultSet queryhorafim = con.prepareStatement(sqlhorafim).executeQuery();
					queryhorafim.next();
					dataSaida = queryhorafim.getTimestamp("datafim");
					Calendar calendar = Calendar.getInstance();  
			          
				    calendar.setTime(dataSaida);  
			        calendar.add(Calendar.SECOND, -1);  
			        dataSaida = calendar.getTime();
			        calendar.add(Calendar.SECOND, -2);
			        dataEntrada = calendar.getTime();
					atual = false;
				} else {
					HistoricoVinculoVO his = new HistoricoVinculoVO();
					his.setCliente(vinculo.getCliente());
					his.setColaborador(vinculo.getColaborador());
					his.setDataRegistro(dataSaida);
					his.setOrigem("SCRIPT");
					his.setTipoColaborador(TipoColaboradorEnum.CONSULTOR.getSigla());
					his.setTipoHistoricoVinculo("SD");
					his.setValidarDados(false);
					hv.incluirSemCommit(his);
					String sqlUpdate= "UPDATE historicovinculo set dataregistro = '"+dataEntrada+"' where tipocolaborador = 'CO' and cliente ="+vinculo.getCliente().getCodigo()
					+" and tipohistoricovinculo = 'EN' AND colaborador = "+vinculo.getColaborador().getCodigo()+" AND dataregistro in (select max(dataregistro)from historicovinculo where tipocolaborador = 'CO' and cliente ="+vinculo.getCliente().getCodigo()
					+" AND tipohistoricovinculo = 'EN' AND colaborador = "+vinculo.getColaborador().getCodigo()+")";
					con.prepareStatement(sqlUpdate).execute();
					v.excluir(vinculo);
					
				}
			}
			
		}
	}

}
			
		

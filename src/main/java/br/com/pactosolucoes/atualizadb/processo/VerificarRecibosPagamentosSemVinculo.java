package br.com.pactosolucoes.atualizadb.processo;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.utilitarias.UteisValidacao;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;

public class VerificarRecibosPagamentosSemVinculo implements Serializable {
	
	private transient Connection con;
	private List<ClientesProblemasPagamento> pgtosParcelasSemRecibo;
        private List<ClientesProblemasPagamento> pgtosSemProdutosPagos;
	private List<ClientesProblemasPagamento> pgtosSemVinculosParcelas;
	private List<ClientesProblemasPagamento> movparcelasSemMovprodutos;
	private List<ClientesProblemasPagamento> movprodutosSemMovparcelas;
	private List<ClientesProblemasPagamento> parcelasEAComPagamento;
	private ArrayList<ClientesProblemasPagamento> recibosSemProdutoContaCorrente;

	public VerificarRecibosPagamentosSemVinculo(Connection con){
		this.con = con;
	}

	public class ClientesProblemasPagamento implements Serializable{
		String nome;
		String matricula;
		int recibo;
		int codigoCliente;
		int movparcela;
		int movproduto;
		int pagamento;

		public ClientesProblemasPagamento(String n, String m, int r, int c, int par, int prod){
			nome = n;
			matricula = m;
			recibo = r;
			codigoCliente = c;
			movparcela = par;
			movproduto = prod;
		}

		public ClientesProblemasPagamento(String nome, int recibo, int pagamento){
			this.nome = nome;
			this.recibo = recibo;
			this.pagamento = pagamento;
		}
		public String getNome(){
			return nome;
		}
		public String getMatricula(){
			return matricula;
		}
		public int getRecibo(){
			return recibo;
		}
		public int getCodigoCliente(){
			return codigoCliente;
		}
		public int getMovparcela(){
			return movparcela;
		}
		public int getMovproduto(){
			return movproduto;
		}
	}
	
	public void obterPgtosSemVinculoParcela() throws Exception{
		pgtosSemVinculosParcelas = new ArrayList<ClientesProblemasPagamento>();
		ResultSet dados = SuperFacadeJDBC.criarConsulta(" SELECT distinct mp.nomepagador, cli.codigo, cli.matricula, mp.recibopagamento FROM movpagamento mp\n" +
                                                "inner join cliente cli on mp.pessoa = cli.pessoa \n" +
                                                " left join pagamentomovparcela pmp on mp.codigo = pmp.movpagamento\n" +
                                                "where pmp.codigo is null and valor > 0  and not mp.credito ", con);
                                                                while(dados.next()){
			pgtosSemVinculosParcelas.add(new ClientesProblemasPagamento(dados.getString("nomepagador"), dados.getString("matricula"), 
					                                                    dados.getInt("recibopagamento"), dados.getInt("codigo"), 0, 0));
		}
	}
	
	public void corrigirPgtosParcelasSemRecibo() throws Exception{
		SuperFacadeJDBC.executarConsulta("UPDATE movprodutoparcela mpp SET recibopagamento = " +
										 " (select max(recibopagamento) from pagamentomovparcela where pagamentomovparcela.movparcela = mpp.movparcela)"
				                         +" where mpp.movparcela in (SELECT pmp.movparcela FROM movpagamento mp "+
									     " inner join pagamentomovparcela pmp on pmp.movpagamento = mp.codigo "+
										 " inner join movprodutoparcela mpp on mpp.movparcela = pmp.movparcela" +
										 " and mpp.recibopagamento is null);", con);
		pgtosParcelasSemRecibo = new ArrayList<ClientesProblemasPagamento>();
	}

	public void obterPgtosParcelasSemRecibo() throws Exception{
		pgtosParcelasSemRecibo = new ArrayList<ClientesProblemasPagamento>();
		ResultSet dados = SuperFacadeJDBC.criarConsulta("SELECT distinct mp.nomepagador, cli.codigo, cli.matricula, mp.recibopagamento FROM movpagamento mp "
													   +" inner join cliente cli on mp.pessoa = cli.pessoa " 
													   +" inner join pagamentomovparcela pmp on pmp.movpagamento = mp.codigo "
													   +" inner join movprodutoparcela mpp on mpp.movparcela = pmp.movparcela " +
													   	" and mpp.recibopagamento is null", con);
		while(dados.next()){
			pgtosParcelasSemRecibo.add(new ClientesProblemasPagamento(dados.getString("nomepagador"), dados.getString("matricula"), 
                    dados.getInt("recibopagamento"), dados.getInt("codigo"),0, 0));
		}
	}
	
        public void obterPgtosSemProdutosPagos() throws Exception{
		pgtosSemProdutosPagos = new ArrayList<ClientesProblemasPagamento>();
		ResultSet dados = SuperFacadeJDBC.criarConsulta("SELECT distinct mp.nomepagador, cli.codigo, cli.matricula, mp.recibopagamento FROM movpagamento mp "
													   +" inner join cliente cli on mp.pessoa = cli.pessoa " 
													   +"  where  "
													   +" mp.valortotal > 0 and mp.recibopagamento  is not null and mp.produtospagos = '' limit 50 " , con);
		while(dados.next()){
			pgtosSemProdutosPagos .add(new ClientesProblemasPagamento(dados.getString("nomepagador"), dados.getString("matricula"), 
                    dados.getInt("recibopagamento"), dados.getInt("codigo"),0, 0));
		}
	}
	
	public void obterMovparcelasSemMovprodutos() throws Exception{
		movparcelasSemMovprodutos = new ArrayList<ClientesProblemasPagamento>();
		ResultSet dados = SuperFacadeJDBC.criarConsulta("SELECT mp.codigo, cli.matricula, pes.nome, cli.codigo as cliente FROM movparcela mp "+
														" inner join cliente cli on cli.pessoa = mp.pessoa " +
										                " inner join pessoa pes on cli.pessoa = pes.codigo "
                                                                                                    + " left join movprodutoparcela mpp on mpp.movparcela = mp.codigo " +
										                " WHERE mpp.codigo is null and mp.situacao <> 'RG' and mp.valorparcela > 0 and mp.contrato is not null "
										                +" AND mp.descricao LIKE 'PARCELA%' order by mp.contrato desc", con);
		while(dados.next()){
			movparcelasSemMovprodutos.add(new ClientesProblemasPagamento(dados.getString("nome"), dados.getString("matricula"),0,
	                 dados.getInt("cliente"), dados.getInt("codigo"), 0));
		}
	}
	
	public void obterMovprodutosSemMovparcelas() throws Exception{
		movprodutosSemMovparcelas = new ArrayList<ClientesProblemasPagamento>();
		ResultSet dados = SuperFacadeJDBC.criarConsulta(" SELECT DISTINCT pes.nome, cli.codigo, cli.matricula, movproduto.codigo as movproduto FROM movproduto \n" +
                                                "INNER JOIN produto ON produto.codigo = movproduto.produto AND produto.tipoproduto IN ('PM', 'RE', 'RN','MA', 'PE', 'SE', 'TD') \n" +
                                                "INNER JOIN cliente cli ON cli.pessoa = movproduto.pessoa \n" +
                                                "inner join pessoa pes on cli.pessoa = pes.codigo \n" +
                                                "left join movprodutoparcela mpp on mpp.movproduto = movproduto.codigo														\n" +
                                                "WHERE mpp.codigo is null\n" +
                                                "AND contrato IS NOT NULL;", con);
		while(dados.next()){
			movprodutosSemMovparcelas.add(new ClientesProblemasPagamento(dados.getString("nome"), dados.getString("matricula"),0,
					dados.getInt("codigo"), 0, dados.getInt("movproduto")));
		}
	}

	public void obterRecibosSemProdutoContaCorrente() throws Exception{
		recibosSemProdutoContaCorrente = new ArrayList<ClientesProblemasPagamento>();
		String encontrarRecibosComDiferencas = "SELECT\n" +
				"pessoa.nome,\n" +
				"  t.*,\n" +
				"  valortotalpagamento - valorpg AS dif\n" +
				"FROM\n" +
				"  (SELECT\n" +
				"     m.recibopagamento      AS recibo,\n" +
				"     m.pessoa               AS pessoapagador,\n" +
				"     m.responsavelpagamento AS responsavellancamento,\n" +
				"     m.codigo               AS movpagamento,\n" +
				"     m.valortotal           AS valortotalpagamento,\n" +
				"     m.datalancamento       AS datalancamento,\n" +
				"     sum(pmp.valorpago)     AS valorpg\n" +
				"   FROM public.pagamentomovparcela pmp\n" +
				"     INNER JOIN public.movpagamento m\n" +
				"       ON m.codigo = pmp.movpagamento\n" +
				"   GROUP BY 1, 2, 3, 4, 5, 6\n" +
				"   ORDER BY m.recibopagamento) AS t\n" +
				"inner join pessoa on pessoa.codigo = t.pessoapagador \n" +
				"WHERE abs(t.valortotalpagamento - t.valorpg) >= 0.01";

		PreparedStatement stm = con.prepareStatement(encontrarRecibosComDiferencas,  ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
		ResultSet dados = stm.executeQuery();

		while(dados.next()){
			recibosSemProdutoContaCorrente.add(new ClientesProblemasPagamento(dados.getString("nome"),
					dados.getInt("recibo"),
					dados.getInt("movpagamento")));
		}
	}

	public void obterParcelasEAComPagamento() throws Exception{
		parcelasEAComPagamento = new ArrayList<ClientesProblemasPagamento>();
		ResultSet dados = SuperFacadeJDBC.criarConsulta(" SELECT mp.codigo, cli.matricula, pes.nome, cli.codigo as cliente FROM movparcela mp \n" +
                                                    " inner join cliente cli on cli.pessoa = mp.pessoa \n" +
                                                    " inner join pessoa pes on cli.pessoa = pes.codigo \n" +
                                                    " inner join pagamentomovparcela pmp on mp.codigo = pmp.movparcela\n" +
                                                    " WHERE mp.situacao like 'EA'", con);
		while(dados.next()){
			parcelasEAComPagamento.add(new ClientesProblemasPagamento(dados.getString("nome"), dados.getString("matricula"),0,
					                 dados.getInt("cliente"), dados.getInt("codigo"), 0));
		}
	}

	public void corrigirParcelasEAComPagamento() throws Exception{
		SuperFacadeJDBC.executarConsulta("delete from  clientemensagem WHERE movparcela IN ( " +
										 " SELECT codigo FROM movparcela WHERE situacao like 'EA' " +
										 " AND codigo in (select movparcela from pagamentomovparcela))", con);
		
		SuperFacadeJDBC.executarConsulta("UPDATE movparcela SET situacao = 'PG' WHERE situacao like 'EA' " +
				" AND codigo in (select movparcela from pagamentomovparcela); " +
				" UPDATE movproduto SET situacao = 'PG' WHERE situacao like 'EA' " +
				" and not exists(select * from movprodutoparcela where recibopagamento is null and movproduto = movproduto.codigo);", con);
		
		SuperFacadeJDBC.executarConsulta("delete from clientemensagem where movparcela in " +
				                         " (SELECT codigo FROM movparcela WHERE situacao like 'PG') and tipomensagem like 'PA'", con);
	}

	public void setPgtosParcelasSemRecibo(List<ClientesProblemasPagamento> pgtosParcelasSemRecibo) {
		this.pgtosParcelasSemRecibo = pgtosParcelasSemRecibo;
	}


	public List<ClientesProblemasPagamento> getPgtosParcelasSemRecibo() {
		return pgtosParcelasSemRecibo;
	}


	public void setPgtosSemVinculosParcelas(List<ClientesProblemasPagamento> pgtosSemVinculosParcelas) {
		this.pgtosSemVinculosParcelas = pgtosSemVinculosParcelas;
	}


	public List<ClientesProblemasPagamento> getPgtosSemVinculosParcelas() {
		return pgtosSemVinculosParcelas;
	}


	public void setMovparcelasSemMovprodutos(List<ClientesProblemasPagamento> movparcelasSemMovprodutos) {
		this.movparcelasSemMovprodutos = movparcelasSemMovprodutos;
	}


	public List<ClientesProblemasPagamento> getMovparcelasSemMovprodutos() {
		return movparcelasSemMovprodutos;
	}


	public void setMovprodutosSemMovparcelas(List<ClientesProblemasPagamento> movprodutosSemMovparcelas) {
		this.movprodutosSemMovparcelas = movprodutosSemMovparcelas;
	}


	public List<ClientesProblemasPagamento> getMovprodutosSemMovparcelas() {
		return movprodutosSemMovparcelas;
	}


	public void setParcelasEAComPagamento(List<ClientesProblemasPagamento> parcelasEAComPagamento) {
		this.parcelasEAComPagamento = parcelasEAComPagamento;
	}


	public List<ClientesProblemasPagamento> getParcelasEAComPagamento() {
		return parcelasEAComPagamento;
	}
	
	public static String sqlVerificador(){
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT distinct mp.codigo as pagamento, mp.recibopagamento, mp.pessoa as pessoa, 0 as parcela, 0 as movproduto, 'PGTOS SEM PARCELA' AS tipo FROM movpagamento mp \n");
		sql.append(" where mp.codigo not in (select movpagamento from pagamentomovparcela) and valor > 0 and not mp.credito UNION ALL \n");

		sql.append(" SELECT distinct mp.codigo as pagamento, mp.recibopagamento, mp.pessoa as pessoa, 0 as parcela, 0 as movproduto, 'PGTOS COM PARCELA SEM RECIBO' AS tipo FROM movpagamento mp \n"); 
		sql.append(" inner join pagamentomovparcela pmp on pmp.movpagamento = mp.codigo  \n");
		sql.append(" inner join movprodutoparcela mpp on mpp.movparcela = pmp.movparcela and mpp.recibopagamento is null UNION ALL \n");

		sql.append(" SELECT distinct 0, 0, mp.pessoa as pessoa, mp.codigo, 0 as movproduto, 'PARCELAS PAGAS EM ABERTO' AS tipo FROM movparcela mp \n");  
		sql.append(" inner join cliente cli on cli.pessoa = mp.pessoa  \n");
		sql.append(" inner join pessoa pes on cli.pessoa = pes.codigo  \n");
		sql.append(" WHERE mp.situacao like 'EA' AND mp.codigo in (select movparcela from pagamentomovparcela) limit 50 \n");
		
		return sql.toString();
	}

    public List<ClientesProblemasPagamento> getPgtosSemProdutosPagos() {
        return pgtosSemProdutosPagos;
    }

    public void setPgtosSemProdutosPagos(List<ClientesProblemasPagamento> pgtosSemProdutosPagos) {
        this.pgtosSemProdutosPagos = pgtosSemProdutosPagos;
    }
    
    
    public void corrigirPagementosSemProdutosPagos() throws Exception {
        if (!UteisValidacao.emptyList(pgtosSemProdutosPagos)) {
            ResultSet dados = SuperFacadeJDBC.criarConsulta("SELECT distinct mp.nomepagador, cli.codigo, cli.matricula, mp.recibopagamento FROM movpagamento mp "
                    + " inner join cliente cli on mp.pessoa = cli.pessoa "
                    + "  where  "
                    +" mp.valortotal > 0 and mp.recibopagamento  is not null and mp.produtospagos = '' " , con);
            ProdutosPagosServico servico = new ProdutosPagosServico();
            while (dados.next()) {
                servico.setarProdutosPagos(con, dados.getInt("recibopagamento"));
            }
            servico = null;
        }
        
    }
    public int getPgtosSemProdutosPagosSize(){
        if (!UteisValidacao.emptyList(pgtosSemProdutosPagos)) {
            return pgtosSemProdutosPagos.size();
        }
        return 0;
    }

	public ArrayList<ClientesProblemasPagamento> getRecibosSemProdutoContaCorrente() {
		return recibosSemProdutoContaCorrente;
	}

	public void setRecibosSemProdutoContaCorrente(ArrayList<ClientesProblemasPagamento> recibosSemProdutoContaCorrente) {
		this.recibosSemProdutoContaCorrente = recibosSemProdutoContaCorrente;
	}

}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "25/07/2024",
        descricao = "Flag para ativar marcação automatica recebiveis a devolver no cancelamento",
        motivacao = "GC-625")
public class AtualizacaoTicketGC625 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN marcarAutoRecebiveisCartaoChequeCancelamento BOOLEAN;", c);
        }
    }
}

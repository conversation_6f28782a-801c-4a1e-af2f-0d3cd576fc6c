package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import br.com.pactosolucoes.enumeradores.TipoContatoCRM;
import negocio.comuns.financeiro.enumerador.TipoEnvioPactoPayEnum;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Hibrael Araújo Alves",
        data = "07/10/2024",
        descricao = "FP-54 - Realizar a integração do GymbotPro para que funcione na Régua de cobrança",
        motivacao = "FP-54 - Realizar a integração do GymbotPro para que funcione na Régua de cobrança")
public class AtualizacaoTicketFP54 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            //Campo facilitePayReguaCobrancaGymbotPro para habilitar o recurso nas configurações de integração
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ADD COLUMN facilitePayReguaCobrancaGymbotPro BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN cobrancaAntecipadaGymbotPro BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN comunicadoResultadoCobrancaGymbotPro BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN comunicadoAtrasoGymbotPro BOOLEAN;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN comunicadoCartaoGymbotPro BOOLEAN;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE empresa SET facilitePayReguaCobrancaGymbotPro = false WHERE facilitePayReguaCobrancaGymbotPro IS NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE pactopayconfig SET cobrancaAntecipadaGymbotPro = false WHERE cobrancaAntecipadaGymbotPro IS NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE pactopayconfig SET comunicadoResultadoCobrancaGymbotPro = false WHERE comunicadoResultadoCobrancaGymbotPro IS NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE pactopayconfig SET comunicadoAtrasoGymbotPro = false WHERE comunicadoAtrasoGymbotPro IS NULL;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE pactopayconfig SET comunicadoCartaoGymbotPro = false WHERE comunicadoCartaoGymbotPro IS NULL;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopayconfig ADD COLUMN configuracaoGymbotPro text;", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE historicocontato " +
                    "SET tipocontato = '" + TipoContatoCRM.CONTATO_GYMBOT.getSigla() + "' " +
                    "WHERE tipocontato = '" +TipoContatoCRM.CONTATO_WHATSAPP.getSigla() +"' " +
                    "and origem in (" +
                            "'" + TipoEnvioPactoPayEnum.CARTAO_A_VENCER.getIdentificador() + "', " +
                            "'" + TipoEnvioPactoPayEnum.PARCELA_PENDENTE.getIdentificador() + "', " +
                            "'" + TipoEnvioPactoPayEnum.COBRANCA_ANTECIPADA.getIdentificador() + "', " +
                            "'" + TipoEnvioPactoPayEnum.RESULTADO_COBRANCA.getIdentificador() + "', " +
                            "'" + TipoEnvioPactoPayEnum.CARTAO_VENCIDO.getIdentificador() + "'" +
                    ");", c);
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProcessoSincronizarBoletosPendentes {

    public static void main(String[] args) {
        try {
            Connection conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT chave,robocontrole FROM empresa WHERE ativa ");
            if (!UteisValidacao.emptyString(getChaves())) {//filtrar por chaves
                sql.append("and chave in (").append(getChaves()).append(") ");
            }

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", conOAMD);

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conOAMD);
            Integer atual = 0;
            while (rs.next()) {
                String chave = "";
                String roboControle = "";
                String msg = "";
                try {
                    chave = rs.getString("chave");
                    roboControle = rs.getString("robocontrole");
                    Map<String, String> params = new HashMap<>();
                    params.put("op", "verificadorBoletos");
                    params.put("chave", chave);

                    String path = roboControle + "/prest/util/transacao";
                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type", "application/json");
                    RequestHttpService service2 = new RequestHttpService();
                    RespostaHttpDTO respostaHttpChave = service2.executeRequest(path, headers, params, null, MetodoHttpEnum.POST);
                    msg = (chave + " | Resposta: " + respostaHttpChave.getResponse());
                } catch (Exception ex) {
                    ex.printStackTrace();
                    msg = (chave + " | ERRO: " + ex.getMessage());
                } finally {
                    Uteis.logarDebug(++atual + "/" + total + " | Chave " + msg);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static String getChaves() {
        StringBuilder chaves = new StringBuilder();
        for (String chave : getListaBancos()) {
            chaves.append(",'").append(chave).append("'");
        }
        return chaves.toString().replaceFirst(",", "");
    }

    private static List<String> getListaBancos() {
        List<String> chaves = new ArrayList<>();
        chaves.add("24f76f3d10e690809a26d615e4f929f4");
        chaves.add("d3d0b77ac3dea5c6f3fa6ab575524486");
        chaves.add("62298f1ae04e1110f0d8e3ac3a554175");
        chaves.add("e37d62976d355e4f50fd9e62a8d4c986");
        chaves.add("b8eabfa7d85e2ea45923529b915dbf0c");
        chaves.add("9bf3df54d1dedce4d2a7c6b5341fb4e3");
        chaves.add("d4c53c619401433a339c501c2981f146");
        chaves.add("91b920449ea936d83a5e46e6779ac1a0");
        chaves.add("15aa2e189d28f78286cfdac3046d3d39");
        chaves.add("5e558c11d90f9276a6d3b285061e533d");
        chaves.add("13759969dbfa2ae6480590acb1c2ed7d");
        chaves.add("6a6a280394cb56065fdc255bbf0a6d0f");
        chaves.add("4f2eadf1f849d33a5f73ac8054dd370b");
        chaves.add("6c619696e1c8c0d67ffd3643e87a02db");
        chaves.add("d9d9ee5cf61adf25efd955ac7004e736");
        chaves.add("5c7b8b142e310384ad79888eb82fe46a");
        chaves.add("9604445785e0d4d27b3a56430a440840");
        chaves.add("c42bd5ee497c50012dd346bd7b765724");
        chaves.add("202d49f1605e34567fe428282cbf2a8c");
        chaves.add("f0030b6fb2e6d45df2434c06e07bb370");
        chaves.add("6b774caa64735c129f2b912a12d33139");
        chaves.add("864c0b200851d5404281631f0611a160");
        chaves.add("89b03017e00f84c22244c833d131fc60");
        chaves.add("33016610da734adbdb2320d5e98844fa");
        chaves.add("35f977242c98ee778739729b6853d1d9");
        chaves.add("6a4762f10be930b180e816c65bb4a2ec");
        chaves.add("29b37da1cfc4ed44dc7196596fd72a52");
        chaves.add("9a12b1fcf2f3c2b5904c98500bfd5bc3");
        chaves.add("ad4ba77ad7e13231d321de9472c48ee6");
        chaves.add("d2457895750a09682bead86334cf02dc");
        chaves.add("5ebe6e4043b98ff7c7dcd6c3ab213441");
        chaves.add("41fe89ec1ced26e17873d583cd4b542c");
        chaves.add("f3c8e5e9d574887f5f2e298536cb83e8");
        chaves.add("777baaa429f15d1c58b502b169f04ad");
        chaves.add("361c17591c1cc6007b683224842c0c42");
        chaves.add("8cda67fb84413b5ba3ea3aec4eb066fe");
        chaves.add("a0f29f1475240b5041c3d79580449b11");
        chaves.add("bb19eda629f418acb64c88566fd431f1");
        chaves.add("e8dadc5f101a2c6c190f23b976ecddbe");
        chaves.add("aa73858f029062dd7c86c5ec637cc119");
        chaves.add("92f17477be4b159f51092ebdf9e711c6");
        chaves.add("50a3c8e373324da3eb2fa78e745388");
        chaves.add("7562fc5dec77b2fe67ca98ef8bba11f0");
        chaves.add("b495df69d1569ded66566271a1c0f3e0");
        chaves.add("d612b2e0f6d26dadeefe349eca60fb5b");
        chaves.add("f57bc62407db841398593a5de62ed12a");
        chaves.add("3f1ee899910827a2aa14fd48c7c70cc8");
        chaves.add("57cfc6e4e730d8147104f5212012583b");
        chaves.add("a8aa0b596d579da8ead39c57b9a4ff43");
        chaves.add("b3c45912a4d82e89052572ed3b621458");
        chaves.add("ad1505c19b03f5621551f4456724ce3");
        chaves.add("fc5171fc73ca01a11be7973a112a5dca");
        chaves.add("91a6af57123ba1ff04ee9300660777b2");
        chaves.add("7fd180546b9945df2ef59294cefbecea");
        chaves.add("9cfd38cb3aee545af1daf3162ec42f16");
        chaves.add("aca438e8c9e947e64db2236bb2f1f7a9");
        chaves.add("adbd8170be062d57d67b515f59f8a934");
        chaves.add("549b0cddb498a6169df5be41940218e4");
        chaves.add("2f02182b746299a9ca93c1cda4f341b4");
        chaves.add("fbb86515a810c900248bacfc5d638ed4");
        chaves.add("65c4714e324bfc545b1dc11b3e12f772");
        chaves.add("ae0e03faeb7aed2d71152a49a541737b");
        chaves.add("87dafdb351c463889ffdf4611200ff3");
        chaves.add("dda27f5ceaa92378df98ebc974c0d1ed");
        chaves.add("9174e02fa3bbcb78ee777c8d481f1d8c");
        chaves.add("a2f3a6ed5ba836e6938114df3c152ca8");
        chaves.add("d9e463c73e7b8b59f02cfebeb8788bce");
        chaves.add("67f6ab171119549036f6e4d07647d062");
        chaves.add("834cd60b819944e0b4a3401b13ce3fad");
        chaves.add("bbe2d795e5786b506f4f72f639fa5fe");
        chaves.add("1deca5e4e43e6aca13681ecfd99ecb6a");
        chaves.add("eb847a1dad5be79142db654fab89fac4");
        chaves.add("ce560898c3a54cd5a4fe5c1d6573577d");
        chaves.add("9e27fa2ffcfff46eb6555fb76531d8f7");
        chaves.add("e2a2b7c75647adf741db331ec847a3ad");
        chaves.add("35bdd9f322a042c7dd5f40781116c5d0");
        chaves.add("830646ff20fc90bfc6a9d6b3aeaa5e5b");
        chaves.add("701006bc77d7190a0c42006d4ec85e2f");
        chaves.add("9fe5efa91ec0625b18d8f4cf8eddf95f");
        chaves.add("2f3e6321d8fd0ea7aa9997c9ac8bf3a6");
        chaves.add("328c384a917b614c1b77fdfaba7f2133");
        chaves.add("5a227a3fde1558f970ffa7e66c8ee10");
        chaves.add("cbbc31fde85d5ffb8fbef5df291906f4");
        chaves.add("f5fc0d21384f1e88297c6803f2ef6551");
        chaves.add("a175db64085ec5c3aa7446446ec6adf3");
        chaves.add("9c0a065a21003a884a6ebe590090df85");
        chaves.add("52a90e90c58e87596a8fdd845dd8f812");
        chaves.add("b47944d9cfae69f4211eacd61a1ec373");
        chaves.add("7508e1834fa327218cc0e5219a89e4c7");
        chaves.add("5d35802c0b1c059182a8573214e06c25");
        chaves.add("6147e74aeeb02dceaeb413f967750745");
        chaves.add("fd549c61db3cd6ecf363706d41dff65a");
        chaves.add("9b6d516ff8351bcd4427377bdd0edb16");
        chaves.add("ecc3e784c47ec3c73ab81e4bd12f01b5");
        chaves.add("a1ed855d6c94ee0bbeccaf3fd9065084");
        chaves.add("a23352616c44bb33116052213eb00751");
        chaves.add("fb932e7eb4187044603218d4408637bd");
        chaves.add("34b9605d538cf1c8f4ea3f7d5b382a3");
        chaves.add("59684b22310b565c2a3b78becc04914f");
        chaves.add("ed01ea5305f96d2fd81e620c0fefbf33");
        chaves.add("a0f326b92ba4698b190245b88b45fa0f");
        chaves.add("6919e5376d13c8681c6c7b8729a8c185");
        chaves.add("b142bb0425849203638a622b55e8bda2");
        chaves.add("c474863219a443a15895dd45feadb076");
        chaves.add("573193b2629d2523d224ef490f1283da");
        chaves.add("d336593f01e2cd123d510af09adba994");
        chaves.add("74116215d558af26d8e1b271bbea8283");
        chaves.add("20ca45095c486418004d4c9460d76e97");
        chaves.add("16235730116637ebfa2277e1c5682003");
        chaves.add("be705de41f31876274593c446a2966a7");
        chaves.add("96a119745bbe3e502b2b7031c7338f58");
        chaves.add("ab2c6efd755b70a40c60f5f05f382dd");
        chaves.add("9fc4d53bfdca6d434e8e02909f2c949c");
        return chaves;
    }

}

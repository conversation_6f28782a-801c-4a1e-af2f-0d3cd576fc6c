/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pacto.priv.utils.Uteis;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class PreencherNumeroParcelaCartao {
    public static void main(String... args) {
        try {
               String nomeThread = args.length > 0 ? args[0] : "cia";
            DAO dao = new DAO();
            Connection con = dao.obterConexaoEspecifica(nomeThread);
            setNumeroParcelaCartao(con);

            } catch (Exception ex) {
                Logger.getLogger(PreencherNumeroParcelaCartao.class.getName()).log(Level.SEVERE, null, ex);
            }
    }

    public static void setNumeroParcelaCartao(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select mp.codigo as pagamento, recibopagamento, nrparcelacartaocredito,mp.formapagamento,mp.datalancamento,mp.autorizacaocartao from   movpagamento mp inner join formapagamento fp on mp.formapagamento = fp.codigo where fp.tipoformapagamento = 'CA' and movpagamentoorigemcredito is null and mp.valor > 0 and exists(select codigo from cartaocredito where nrparcela is null and movpagamento = mp.codigo)", con);
        ResultSet consultaCartoes;  
        ResultSet consultaPagamentoCancelado;
        ResultSet consultaPagamentoCredito; 
        String where = "";
        while (consulta.next()) {
            int numeroParcela = 1;
            int numeroParcelaCancelada = 1;
            boolean parcelaValorMenor = false;
            double valorParcial = 0.0;
            consultaCartoes = SuperFacadeJDBC.criarConsulta("select codigo, movpagamento, valor, valortotal, situacao, composicao, case when dataoriginal is null then datacompesancao else dataoriginal end as dataAvaliar from cartaocredito  where movpagamento =  "+consulta.getInt("pagamento")+"  order by 7", con);
            while(consultaCartoes.next()){
                SuperFacadeJDBC.executarConsultaUpdate("update cartaocredito set nrparcela = "+numeroParcela+" where codigo ="+consultaCartoes.getInt("codigo"), con);
                if(!(Uteis.arredondarForcando2CasasDecimais(consultaCartoes.getDouble("valor")) < Uteis.arredondarForcando2CasasDecimais(consultaCartoes.getDouble("valortotal")))){
                    numeroParcela++;
                } else { 
                    parcelaValorMenor = true;
                    valorParcial = Uteis.arredondarForcando2CasasDecimais(consultaCartoes.getDouble("valor"));
                }
            }
            if(numeroParcela <= consulta.getInt("nrparcelacartaocredito") || parcelaValorMenor){
                numeroParcelaCancelada = numeroParcela;
                if(!UteisValidacao.emptyString(consulta.getString("autorizacaocartao"))){
                    where = " datalancamento = '"+Uteis.getDataAplicandoFormatacao(consulta.getTimestamp("datalancamento"),"yyyy-MM-dd HH:mm:ss.SSS")+"' ";
                } else {
                    where = " datalancamento = '"+Uteis.getDataAplicandoFormatacao(consulta.getTimestamp("datalancamento"),"yyyy-MM-dd HH:mm:ss.SSS")+"' ";
                }
                consultaPagamentoCancelado = SuperFacadeJDBC.criarConsulta("select mp.codigo as pagamento, recibopagamento, nrparcelacartaocredito,mp.formapagamento,mp.datalancamento,mp.valor from   movpagamento mp where codigo in(select codigo from movpagamento where valor = 0 and  recibopagamento = "+consulta.getInt("recibopagamento")+" and formapagamento = "+consulta.getInt("formapagamento")+" and "+where+") ", con);
                if(consultaPagamentoCancelado.next()){
                    consultaCartoes = SuperFacadeJDBC.criarConsulta("select codigo, movpagamento, valor, valortotal, situacao, composicao, case when dataoriginal is null then datacompesancao else dataoriginal end as dataAvaliar from cartaocredito  where movpagamento =  "+consultaPagamentoCancelado.getInt("pagamento")+"  order by 7", con);
                    while(consultaCartoes.next()){ 
                        SuperFacadeJDBC.executarConsultaUpdate("update cartaocredito set nrparcela = "+numeroParcelaCancelada+" where codigo ="+consultaCartoes.getInt("codigo"), con);
                        numeroParcelaCancelada++;
                    }
                    consultaPagamentoCredito = SuperFacadeJDBC.criarConsulta("select mp.codigo as pagamento, recibopagamento, nrparcelacartaocredito,mp.formapagamento,mp.datalancamento,mp.valor from   movpagamento mp  where movpagamentoorigemcredito ="+consultaPagamentoCancelado.getInt("pagamento")+" order by codigo",con);
                    while(consultaPagamentoCredito.next()){
                        consultaCartoes = SuperFacadeJDBC.criarConsulta("select codigo, movpagamento, valor, valortotal, situacao, composicao, case when dataoriginal is null then datacompesancao else dataoriginal end as dataAvaliar from cartaocredito  where movpagamento =  "+consultaPagamentoCredito.getInt("pagamento")+"  order by 7", con);
                        if(!(consultaPagamentoCredito.getDouble("valor") > 0)){
                            numeroParcelaCancelada = numeroParcela;
                             while(consultaCartoes.next()){ 
                                SuperFacadeJDBC.executarConsultaUpdate("update cartaocredito set nrparcela = "+numeroParcelaCancelada+" where codigo ="+consultaCartoes.getInt("codigo"), con);
                                numeroParcelaCancelada++;
                            }
                        } else {
                            while(consultaCartoes.next()){ 
                                SuperFacadeJDBC.executarConsultaUpdate("update cartaocredito set nrparcela = "+numeroParcela+" where codigo ="+consultaCartoes.getInt("codigo"), con);
                                if(!(Uteis.arredondarForcando2CasasDecimais(consultaCartoes.getDouble("valor")) < Uteis.arredondarForcando2CasasDecimais(consultaCartoes.getDouble("valortotal")))){
                                    numeroParcela++;
                                } else { 
                                    valorParcial = Uteis.arredondarForcando2CasasDecimais(consultaCartoes.getDouble("valor") + valorParcial);
                                    if(!(valorParcial < Uteis.arredondarForcando2CasasDecimais(consultaCartoes.getDouble("valortotal")))){
                                       numeroParcela++;
                                       valorParcial = 0.0;
                                    }
                                }
                            }
                        }
                        
                    }
                    
                }
            } 
        }
        consulta = SuperFacadeJDBC.criarConsulta("select distinct mp.* from cartaocredito cc  inner join movpagamento mp on mp.codigo = cc.movpagamento where cc.nrparcela is null order by codigo", con);
        while (consulta.next()) {
            int numeroParcela = 1;
            consultaCartoes = SuperFacadeJDBC.criarConsulta("select codigo, movpagamento, valor, valortotal, situacao, composicao, case when dataoriginal is null then datacompesancao else dataoriginal end as dataAvaliar from cartaocredito  where movpagamento =  "+consulta.getInt("codigo")+"  order by 7", con);
            while(consultaCartoes.next()){
                SuperFacadeJDBC.executarConsultaUpdate("update cartaocredito set nrparcela = "+numeroParcela+" where codigo ="+consultaCartoes.getInt("codigo"), con);
                numeroParcela++;
            }
        }
    }
    
}

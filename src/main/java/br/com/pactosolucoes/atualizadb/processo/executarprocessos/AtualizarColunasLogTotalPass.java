package br.com.pactosolucoes.atualizadb.processo.executarprocessos;
import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import java.sql.Connection;

@ClasseProcesso(
        autor = "Anna Carolina",
        data = "15/04/2025",
        descricao = "Alterar tamanho das colunas uri e apikey na tabela logtotalpass",
        motivacao = "TW-2012 - Ajuste de compatibilidade para armazenar URIs e chaves maiores"
)
public class AtualizarColunasLogTotalPass implements MigracaoVersaoInterface {

        @Override
        public void executar(Connection con) throws Exception {
            try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE logtotalpass ALTER COLUMN uri TYPE VARCHAR(255);", c);
                SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE logtotalpass ALTER COLUMN apikey TYPE VARCHAR(255);", c);
            }
        }
    }


package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.contrato.ContratoControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoExcecaoVO;
import negocio.comuns.plano.PlanoHorarioVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.PlanoCondicaoPagamento;
import negocio.facade.jdbc.plano.PlanoDuracao;
import negocio.facade.jdbc.plano.PlanoExcecao;
import negocio.facade.jdbc.plano.PlanoHorario;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Date;
import java.util.List;

public class ProcessoLancarContratosPontoDaDanca {
    public static void main(String[] args) {
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"ponto"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            Uteis.debug = true;

            Connection con = new DAO().obterConexaoEspecifica(chave);

            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            Colaborador colaboradorDao = new Colaborador(con);
            ColaboradorVO colaboradorVO = colaboradorDao.consultarPorNomeColaborador("PACTO",1, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            Conexao.guardarConexaoForJ2SE("ponto", con);
            consultarClientes(empresaVO, colaboradorVO, con);
        } catch (Exception ex) {

        }
    }

    private static void consultarClientes(EmpresaVO empresaVO, ColaboradorVO colaboradorVO, Connection con) throws Exception {
        Uteis.logar("INÍCIO | ProcessoLancarContratosPontoDaDanca");

        String sqlPlano = "SELECT codigo as codigoPlano\n" +
                "FROM plano\n" +
                "WHERE bolsa AND vigenciade >= '2020-01-01' AND vigenciaate <= '2020-01-31' LIMIT 1;";

        Integer codigoPlano = 0;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlPlano)) {
                if (rs.next()) {
                    codigoPlano = rs.getInt("codigoPlano");
                }
            }
        }
        if (codigoPlano == null || codigoPlano == 0){
            throw new Exception("Não existe plano bolsa com vigência a partir de 01/01/2020");
        }

        Date dataContrato = Calendario.getDate("dd/MM/yyyy", "01/01/2020");

        String sql = "SELECT cli.codigo as codigoCliente\n" +
                "FROM pessoa pes\n" +
                "INNER JOIN cliente cli ON cli.pessoa = pes.codigo \n" +
                "WHERE pes.codigo NOT IN (SELECT pessoa FROM periodoacessocliente WHERE tipoacesso IN ('PL', 'CA'))\n" +
                "AND cli.codigomatricula NOT IN (4, 7, 22, 59, 329, 374, 395, 521, 596, 698, 773, 1118, 2740, 5595, 6149, " +
                "6230, 6618, 6658, 6667, 6838, 6839, 6986, 7084, 7103, 7185, 7399, 7446, 7484, 7964, 8072, 8076, 8214, 8372, " +
                "8413, 8430, 8446, 8499, 8517, 8614, 8731, 8904, 9123, 9275, 9292, 9322, 9369, 9414, 9477, 9546, 9570, 9633, " +
                "9708, 9715, 9784, 9798, 9836, 9923, 9948, 10012, 10017, 10134, 10173, 10176, 10180, 10224, 10245, 10252, " +
                "10358, 10401, 10415, 10420, 10421, 10439, 10470, 10512, 10513, 10524, 10544, 10557, 10570, 10573, 10585, " +
                "10588, 10616, 10658, 10672, 10690, 10726, 10727, 10747, 10772, 10783, 10804, 10807, 10809, 10820, 10836, " +
                "10837, 10841, 10847, 10872, 10885, 10916, 10938, 10943, 10968, 11038, 11075, 11084, 11096, 11109, 11110, " +
                "11128, 11135, 11146, 11152, 11193, 11239, 11240, 11264, 11265, 11286, 11302, 11304, 11306, 11307, 11310, " +
                "11315, 11320, 11407, 11425, 11428, 11462, 11477, 11480, 11483, 11484, 11485, 11486, 11488, 11489, 11490, " +
                "11492, 11493, 11494, 11495, 11496, 11506, 11517, 11519, 11525, 11529, 11590, 11592, 11593, 11598, 11609, " +
                "11616, 11617, 11618, 11620, 11622, 11623, 11634, 11638, 11645)\n" +
                "AND pes.datacadastro < '2023-04-24'\n" +
                "ORDER BY cli.codigomatricula;";

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                while (rs.next()) {
                    int codigoCliente = rs.getInt("codigoCliente");
                    criaContrato(colaboradorVO, dataContrato, codigoCliente, codigoPlano, con);
                }
            }
        }

        Uteis.logar("FIM | ProcessoLancarContratosPontoDaDanca");

    }

    private static void criaContrato(ColaboradorVO colaboradorVO, Date dataContrato, int codigoCliente, int codigoPlano, Connection con) throws Exception {
        Calendario.dia = dataContrato;;

        Cliente clienteDao = new Cliente(con);
        ClienteVO clienteVO = clienteDao.consultarPorChavePrimaria(codigoCliente,  Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        Plano planoDao = new Plano(con);
        PlanoVO planoVO = planoDao.consultarPorChavePrimaria(codigoPlano, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        PlanoExcecao planoExcecaoDao = new PlanoExcecao(con);
        List<PlanoExcecaoVO> planosExcecaoVO = planoExcecaoDao.consultarPorPlano(planoVO.getCodigo());

        if (planosExcecaoVO.size() == 1) {
            Contrato contratoDao = new Contrato(con);
            ContratoVO contratoVO = new ContratoVO();
            contratoVO.setConsultor(colaboradorVO);
            ContratoControle contratoControle = contratoDao.obterContratoControle(planoVO.getCodigo(), clienteVO.getCodigo(), false, 1, contratoVO, contratoVO);

            PlanoDuracao planoDuracaoDao = new PlanoDuracao(con);
            PlanoDuracaoVO planoDuracaoVO = planoDuracaoDao.consultarPorPlanoRecorrencia(planoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoControle.getContratoVO().setVigenciaDe(dataContrato);

            Usuario usuarioDao = new Usuario(con);
            UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);

            contratoControle.getContratoVO().setVigenciaAte(Uteis.somarMeses(dataContrato, planoDuracaoVO.getNumeroMeses()));
            contratoControle.getContratoVO().setVigenciaAteAjustada(Uteis.somarMeses(dataContrato, planosExcecaoVO.get(0).getDuracao()));
            contratoControle.getContratoVO().setDataMatricula(dataContrato);
            contratoControle.setDataInicioContrato(dataContrato);
            contratoControle.getContratoVO().setPlano(planoVO);
            contratoControle.getContratoVO().setDataLancamento(dataContrato);
            contratoControle.getContratoVO().setPlanoDuracao(planoDuracaoVO);
            contratoControle.getContratoVO().getContratoModalidadeVOs().get(0).getModalidade().setModalidadeEscolhida(true);

            PlanoHorario planoHorarioDao = new PlanoHorario(con);
            List<PlanoHorarioVO> planoHorarioVO = planoHorarioDao.consultarPlanoHorarios(planoVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            contratoControle.getContratoVO().getPlano().setPlanoHorarioVOs(planoHorarioVO);
            contratoControle.getContratoVO().setPlanoHorario(planoHorarioVO.get(0));

            PlanoCondicaoPagamento planoCondicaoPagamentoDao = new PlanoCondicaoPagamento(con);
            PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = planoCondicaoPagamentoDao.consultarPorPlanoDuracaoCondicao(planoDuracaoVO.getCodigo(), 1, Uteis.NIVELMONTARDADOS_TODOS);

            contratoControle.getContratoVO().setPlanoCondicaoPagamento(planoCondicaoPagamentoVO);
            contratoControle.getContratoVO().setOrigemSistema(OrigemSistemaEnum.ZW);

            String fecharNegociacao = contratoControle.fecharNegociacao();
            if (fecharNegociacao.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }

            contratoControle.getContratoVO().setConsultor(colaboradorVO);

            String gravarContrato = contratoControle.gravar(usuarioVO, false);
            if (gravarContrato.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }
            Log logDao = new Log(con);
            LogVO logVO = logDao.novo();
            logVO.setPessoa(clienteVO.getPessoa().getCodigo());
            logVO.setNomePessoa(clienteVO.getPessoa().getNome());
            logVO.setDataAlteracao(dataContrato);
            logVO.setNomeEntidade("CONTRATO");
            logVO.setNomeEntidadeDescricao("CONTRATO");
            logVO.setResponsavelAlteracao("ADMINISTRADOR");
            logVO.setNomeCampo("TODOS");
            logVO.setCliente(clienteVO.getCodigo());
            logVO.setOperacao("INCLUSAO");
            logVO.setValorCampoAlterado("*Informados via processo Importação*");
            logDao.incluir(logVO);
        } else {
            throw new Exception("Não foi possível criar o contrato, pois não existe nenhum planoexcessao para o plano configurado em Empresas!");
        }
    }

}

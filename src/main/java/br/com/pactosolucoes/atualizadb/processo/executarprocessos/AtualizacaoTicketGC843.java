package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "15/08/2024",
        descricao = "Incluir colunas para controle ordem de compra",
        motivacao = "GC-843")
public class AtualizacaoTicketGC843 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT\n"
                    + "codigo\n"
                    + "FROM perfilacesso where tipo = " + PerfilUsuarioEnum.ADMINISTRADOR.getId(), c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (4, '12.09 - Autorizar/Negar Compra','(0)(1)(2)(3)(9)(12)', "
                        + " 'AutorizarNegarCompra', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
            SuperFacadeJDBC.executarUpdateExecutarProcessos("insert into categoriaproduto (descricao, bloquearacessoseprodutoaberto, avaliacaofisica) values ('ORDEM DE COMPRA', false, false)", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table compra add column autorizada boolean default null", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table compraitens add column quantidadeAutorizada integer default 0", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table configuracaofinanceiro add column ordemCompraEstoque boolean default false", c);
        }
    }
}

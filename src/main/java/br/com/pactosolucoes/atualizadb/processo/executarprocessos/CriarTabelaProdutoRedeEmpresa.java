package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "02/01/2025",
        descricao = "Criar tabela ProdutoRedeEmpresa no banco de dados",
        motivacao = "GC-1167")
public class CriarTabelaProdutoRedeEmpresa implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {

        String sqlCriarTabela = "CREATE TABLE ProdutoRedeEmpresa (\n" +
                "    codigo SERIAL PRIMARY KEY,\n" +
                "    produto INTEGER NOT NULL,\n" +
                "    chave VARCHAR(255) NOT NULL,\n" +
                "    datacadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n" +
                "    produtoReplicado INTEGER,\n" +
                "    nomeUnidade VARCHAR(255),\n" +
                "    mensagemSituacao VARCHAR(500),\n" +
                "    codigoEmpresaDestino INTEGER NOT NULL,\n" +
                "    dataatualizacao TIMESTAMP\n" +
                ");";

        String sqlCriarIndex = "CREATE INDEX idx_produto_chave_empresa " +
                "ON ProdutoRedeEmpresa (produto, chave, codigoEmpresaDestino);";

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlCriarTabela, c);

            // index pra melhorar buscas futuras
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlCriarIndex, c);
        }
    }
}

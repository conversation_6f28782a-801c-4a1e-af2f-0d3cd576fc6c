package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.RemessaItem;
import negocio.facade.jdbc.financeiro.Transacao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

public class ProcessoCorrecaoCartaoVerificado {

    public static Integer corrigir(Integer codigoMatricula, Connection c) throws Exception {
        try {
            Uteis.logarDebug("ProcessoCorrecaoCartaoVerificado | INICIO!");

            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("distinct cl.matricula, \n");
            sql.append("au.codigo, \n");
            sql.append("au.cartaomascaradointerno, \n");
            sql.append("au.tokenaragorn \n");
            sql.append("from autorizacaocobrancacliente au \n");
            sql.append("inner join cliente cl on cl.codigo = au.cliente \n");
            sql.append("inner join movparcela mp on mp.pessoa = cl.pessoa \n");
            sql.append("inner join pagamentomovparcela pag on pag.movparcela = mp.codigo \n");
            sql.append("inner join movpagamento mov on mov.codigo = pag.movpagamento \n");
            sql.append("where au.ativa \n");
            sql.append("and au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
            sql.append("and au.cartaoverificado = false \n");
            if (!UteisValidacao.emptyNumber(codigoMatricula)) {
                sql.append("and cl.codigomatricula = ").append(codigoMatricula).append(" \n");
            }
//            else {
//                sql.append("and cl.situacao = 'AT' \n");
//                sql.append("and mov.datalancamento::date > '01/10/2021' \n");
//                sql.append("and mov.datalancamento::date >= au.dataregistro::date \n");
//                sql.append("and (exists(select codigo from remessaitemmovparcela  where movparcela = mp.codigo) \n");
//                sql.append("or exists(select codigo from remessaitem where movparcela = mp.codigo) \n");
//                sql.append("or exists(select codigo from transacaomovparcela where movparcela = mp.codigo)) \n");
//            }

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", c);
            Integer atual = 0;
            Map<Integer, ConvenioCobrancaVO> mapaConvenio = new HashMap<>();
            try (Statement stm = c.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        String msg = "";
                        String matricula = "";
                        Integer autorizacao = 0;
                        RemessaItem remessaItemDAO;
                        Transacao transacaoDAO;
                        AutorizacaoCobrancaCliente autoDAO;
                        ConvenioCobranca convenioCobrancaDAO;
                        try {
                            autoDAO = new AutorizacaoCobrancaCliente(c);
                            convenioCobrancaDAO = new ConvenioCobranca(c);

                            autorizacao = rs.getInt("codigo");
                            matricula = rs.getString("matricula");
                            String tokenAragorn = rs.getString("tokenaragorn");
                            String cartaoMascarado = rs.getString("cartaomascaradointerno");

                            if (UteisValidacao.emptyString(tokenAragorn) || UteisValidacao.emptyString(cartaoMascarado)) {
                                throw new Exception("Autorização sem token ou cartão mascarado");
                            }

                            StringBuilder sql2 = new StringBuilder();
                            sql2.append("select * from ( \n");
                            sql2.append("select  \n");
                            sql2.append("'TRA' as tipo, \n");
                            sql2.append("t.codigo, \n");
                            sql2.append("t.dataprocessamento as data \n");
                            sql2.append("from transacao t  \n");
                            sql2.append("where t.movpagamento is not null  \n");
                            sql2.append("and (t.tokenaragorn = '").append(tokenAragorn).append("' or t.paramsenvio ilike '%").append(cartaoMascarado).append("%') \n");
                            sql2.append("union  \n");
                            sql2.append("select  \n");
                            sql2.append("'REM' as tipo, \n");
                            sql2.append("ri.codigo as codigo, \n");
                            sql2.append("re.dataregistro as data \n");
                            sql2.append("from remessaitem ri \n");
                            sql2.append("inner join remessa re on re.codigo = ri.remessa \n");
                            sql2.append("where ri.movpagamento is not null  \n");
                            sql2.append("and (ri.props ilike '%TokenAragorn=").append(tokenAragorn).append("%' or ri.props ilike '%CartaoMascarado=").append(cartaoMascarado).append("%') \n");
                            sql2.append(") as sql \n");
                            sql2.append("order by data desc  \n");
                            sql2.append("limit 1 \n");

                            try (Statement stm2 = c.createStatement()) {
                                try (ResultSet rs2 = stm2.executeQuery(sql2.toString())) {
                                    if (rs2.next()) {
                                        String tipo = rs2.getString("tipo");
                                        Integer codigo = rs2.getInt("codigo");

                                        if (tipo.equalsIgnoreCase("TRA")) {
                                            transacaoDAO = new Transacao(c);
                                            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                            ConvenioCobrancaVO convenioCobrancaVO = mapaConvenio.get(transacaoVO.getConvenioCobrancaVO().getCodigo());
                                            if (convenioCobrancaVO == null) {
                                                convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                                mapaConvenio.put(convenioCobrancaVO.getCodigo(), convenioCobrancaVO);
                                            }
                                            transacaoVO.setConvenioCobrancaVO(convenioCobrancaVO);

                                            autoDAO.processarCartaoVerificado(transacaoVO);
                                        } else if (tipo.equalsIgnoreCase("REM")) {
                                            remessaItemDAO = new RemessaItem(c);
                                            RemessaItemVO remessaItemVO = remessaItemDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                                            ConvenioCobrancaVO convenioCobrancaVO = mapaConvenio.get(remessaItemVO.getRemessa().getConvenioCobranca().getCodigo());
                                            if (convenioCobrancaVO == null) {
                                                convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getConvenioCobranca().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                                mapaConvenio.put(convenioCobrancaVO.getCodigo(), convenioCobrancaVO);
                                            }
                                            remessaItemVO.getRemessa().setConvenioCobranca(convenioCobrancaVO);

                                            autoDAO.processarCartaoVerificado(remessaItemVO);
                                        }
                                    }
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            msg = ex.getMessage();
                        } finally {
                            remessaItemDAO = null;
                            transacaoDAO = null;
                            autoDAO = null;
                            convenioCobrancaDAO = null;
                            Uteis.logarDebug("ProcessoCorrecaoCartaoVerificado | " + ++atual + "/" + total + " - Mat. "+ matricula+" | Aut " + autorizacao + " | Resultado: " + msg);
                        }
                    }
                }
            }
            return total;
        } finally {
            Uteis.logarDebug("ProcessoCorrecaoCartaoVerificado | FIM!");
        }
    }

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("*****************************************************", "zillyonweb", "pactodb");
            ProcessoCorrecaoCartaoVerificado.corrigir(null, con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;


import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class DeletarInativos {
    
    public static void processoDelete(Connection con) throws SQLException{
        try {
//            con.setAutoCommit(false);
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo, pessoa, matricula FROM cliente WHERE situacao NOT LIKE 'AT'", con);
            int i = 1;
            while(rs.next()){
                SuperFacadeJDBC.executarConsulta("DELETE FROM periodoacessocliente where contratobaseadorenovacao IN "
                        +" (select codigo FROM contrato WHERE pessoa = "+rs.getInt("pessoa")+")", con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM contrato WHERE pessoa = "+rs.getInt("pessoa"), con);
                
                SuperFacadeJDBC.executarConsulta("UPDATE cliente SET uacodigo = null WHERE codigo = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("UPDATE contratooperacao SET clientetransferedias = null WHERE clientetransferedias = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("UPDATE contratooperacao SET clienterecebedias = null WHERE clienterecebedias = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM acessocliente WHERE cliente = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM maladiretaenviada WHERE cliente = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM itemtaxapersonal WHERE aluno = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM aulaavulsadiaria WHERE cliente = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM clienteobservacao WHERE cliente = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM reciboclienteconsultor WHERE cliente = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM historicocontato WHERE cliente = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM agenda WHERE cliente = "+rs.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM cliente WHERE codigo = "+rs.getInt("codigo"), con);
                System.out.println(i+++";"+rs.getString("matricula")+";");
            }
//            con.commit();
        } catch (Exception e) {
            e.printStackTrace();
//            con.rollback();
//            con.setAutoCommit(true);
        } finally {
//            con.setAutoCommit(true);
        }
        
    }
    
    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("*********************************************", "postgres", "pactodb");
            processoDelete(con);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

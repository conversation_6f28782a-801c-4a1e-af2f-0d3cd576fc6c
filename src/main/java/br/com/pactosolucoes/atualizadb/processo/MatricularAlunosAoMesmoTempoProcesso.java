package br.com.pactosolucoes.atualizadb.processo;

import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import servicos.util.ExecuteRequestHttpService;

public class MatricularAlunosAoMesmoTempoProcesso {

    private String urlTreino = "http://localhost:8085/treino";

    private void matricular(String chave,
                            Integer codigoAula,
                            Integer matricula,
                            Integer codUsuario,
                            String dia) throws Exception{
        String uri = urlTreino + "/prest/aula/"+chave+ (codUsuario > 0 ? "/marcarPresenca?" : "/marcarPresencaOrigem?")+
                "codigoAula=" + codigoAula +
                "&aulaExperimental=false&origem=APP_TREINO" +
                "&matricula="+matricula+"&dia=" + dia + (codUsuario > 0 ? "&codUsuario="+codUsuario : "");
        System.out.println(post(uri));

    }

    public void matricularThread(String chave,
                                 Integer codigoAula,
                                 Integer matricula,
                                 Integer codUsuario,
                                 String dia){
        new Thread() {
            @Override
            public void run() {
                try {
                    System.out.println("matriculando ... " + matricula);
                    matricular(chave,
                            codigoAula,
                            matricula,
                            codUsuario,
                            dia);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }.start();
    }

    private String post(String uri) throws Exception{
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(uri);
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return body;
    }

    public static void main(String[] args) throws Exception{
        MatricularAlunosAoMesmoTempoProcesso mesmoTempoProcesso = new MatricularAlunosAoMesmoTempoProcesso();
        String chave = "boxassessoriaup";
        Integer codigoAula = 746;
        Integer usuario = 18;
        String dia = "06/04/2021";
        mesmoTempoProcesso.matricularThread(chave, codigoAula, 752, usuario, dia);
//        Thread.sleep(3000);
        mesmoTempoProcesso.matricularThread(chave, codigoAula, 752, 0, dia);
    }
}

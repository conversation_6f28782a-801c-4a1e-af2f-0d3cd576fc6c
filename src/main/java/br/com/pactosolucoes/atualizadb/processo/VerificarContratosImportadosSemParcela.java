package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.integracao.protheus.PropriedadesIntegracaoProtheus;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.financeiro.MovParcela;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by <PERSON><PERSON> Alcides on 05/04/2017.
 */
public class VerificarContratosImportadosSemParcela {


    public static void main(String ... args) throws Exception {
        Connection con = DriverManager.getConnection("*******************************************", "postgres", "pactodb");
        corrigir(con);
    }

    public static void corrigir(Connection con) throws Exception {
            try {
                ResultSet rs = SuperFacadeJDBC.criarConsulta(" select codigo from contrato "
                        + " where codigo not in (select contrato from movparcela where contrato is not null)"
                        + " and id_externo  is not null \n" + " ORDER BY codigo", con);
                List<Integer> cods = new ArrayList<Integer>();
                while (rs.next()) {
                    cods.add(rs.getInt("codigo"));
                }
                if(cods.isEmpty()){
                    return;
                }
                GerarPagamentoPorCompetencia.refazerPagamentosParcelas(con, false);
            } catch (Exception e) {
                e.printStackTrace();
            }
    }


    public static MovParcelaVO montarMovParcela(ContratoVO contratoVO, ReciboPagamentoVO recibo) throws SQLException, Exception{

        MovParcelaVO movParcelaVO = new MovParcelaVO();
        movParcelaVO.setContrato(contratoVO);
        movParcelaVO.setDataRegistro(contratoVO.getDataLancamento());
        movParcelaVO.setDataVencimento(contratoVO.getDataLancamento());
        movParcelaVO.setDescricao("PARCELA 1");
        movParcelaVO.setReciboPagamento(recibo);
        movParcelaVO.setResponsavel(contratoVO.getResponsavelContrato());
        movParcelaVO.setSituacao("PG");
        movParcelaVO.setValorParcela(contratoVO.getValorFinal());
        movParcelaVO.setPercentualJuro(0.0);
        movParcelaVO.setPercentualMulta(0.0);
        movParcelaVO.setPessoa(contratoVO.getPessoa());
        movParcelaVO.setEmpresa(contratoVO.getEmpresa());
        return movParcelaVO;
    }

}

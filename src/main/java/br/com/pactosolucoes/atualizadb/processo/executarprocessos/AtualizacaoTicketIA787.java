package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Victor Augusto Machado",
        data = "25/05/2025",
        descricao = "IA-787 Adicionar suporte para envio de contextos de todas as empresas da rede nos endpoints da API de integração com a IA Pacto Conversas",
        motivacao = "IA-787")
public class AtualizacaoTicketIA787 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            SuperFacadeJDBC.executarUpdate(
                    "DELETE FROM public.configuracaocrmia a\n" +
                            "USING public.configuracaocrmia b\n" +
                            "WHERE a.codigoempresa = b.codigoempresa\n" +
                            "  AND a.codigo < b.codigo\n" +
                            "  AND a.codigoempresa IS NOT NULL\n" +
                            "  AND (SELECT COUNT(*) FROM public.configuracaocrmia c WHERE c.codigoempresa = a.codigoempresa) > 1;", c
            );

            SuperFacadeJDBC.executarUpdate(
                    "ALTER TABLE public.configuracaocrmia\n" +
                            "ADD CONSTRAINT unique_codigoempresa_configuracaocrmia UNIQUE (codigoempresa);", c
            );

            SuperFacadeJDBC.executarUpdate(
                    "DELETE FROM public.configuracaoredeia\n" +
                            "WHERE codigo <> (SELECT MAX(codigo) FROM public.configuracaoredeia)\n" +
                            "AND (SELECT COUNT(*) FROM public.configuracaoredeia) > 1;", c
            );

            SuperFacadeJDBC.executarUpdate(
                    "CREATE OR REPLACE FUNCTION limitar_uma_linha_configuracaoredeia()\n" +
                            "RETURNS trigger AS $$\n" +
                            "BEGIN\n" +
                            "    IF (SELECT COUNT(*) FROM public.configuracaoredeia) >= 1 THEN\n" +
                            "        RAISE EXCEPTION 'Só é permitida uma única linha na tabela configuracaoredeia.';\n" +
                            "    END IF;\n" +
                            "    RETURN NEW;\n" +
                            "END;\n" +
                            "$$ LANGUAGE plpgsql;", c
            );

            SuperFacadeJDBC.executarUpdate(
                    "CREATE TRIGGER trigger_limitar_uma_linha\n" +
                            "BEFORE INSERT ON public.configuracaoredeia\n" +
                            "FOR EACH ROW\n" +
                            "EXECUTE PROCEDURE limitar_uma_linha_configuracaoredeia();", c
            );
        }
    }
}

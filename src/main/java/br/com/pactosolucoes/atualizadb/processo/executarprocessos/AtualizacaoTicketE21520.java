package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.ProcessoAjustarClientesSemCodAcesso;
import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "24/10/2024",
        descricao = "Gerar código de acesso para alunos que estão sem código de acesso",
        motivacao = "M1-3117")
public class AtualizacaoTicketE21520 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ProcessoAjustarClientesSemCodAcesso.ajustarClientesSemCodAcesso(c);
        }
    }
}

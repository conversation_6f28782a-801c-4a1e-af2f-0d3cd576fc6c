/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.socialmailing.dao.SocialMailGrupo;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoParticipanteVO;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoVO;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class AtualizarSocialMail{
    
    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("****************************************", "postgres", "pactodb");
            montarNovaEstrutura(con);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public static void montarNovaEstrutura(Connection c) throws Exception{
        SocialMailGrupo dao = new SocialMailGrupo(c);
        ResultSet count = SuperFacadeJDBC.criarConsulta("SELECT * FROM socialmailgrupo", c);
        if(count.next()){
            return;
        }
        
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT grupo,min(dataenvio) as data, min(codigo) as primeiro FROM socialmail GROUP BY grupo ORDER BY grupo", c);
        while(rs.next()){
            int codPr = rs.getInt("primeiro");
            String grupo = rs.getString("grupo");
            String[] pessoas = grupo.split(",");
            ResultSet rsDono = SuperFacadeJDBC.criarConsulta("SELECT pessoaorigem FROM socialmail WHERE codigo = "+codPr, c);
            if(rsDono.next()){
                SocialMailGrupoVO smgp = new SocialMailGrupoVO();
                smgp.setDataCriacao(rs.getDate("data"));
                smgp.setColetivo(pessoas.length > 2);
                smgp.getDono().setCodigo(rsDono.getInt("pessoaorigem"));
                dao.incluir(smgp);
                SuperFacadeJDBC.executarConsulta("UPDATE socialmail SET socialmailgrupo = "
                        +smgp.getCodigo()+" WHERE grupo = '"+grupo+"'", c);
                for(String str : pessoas){
                    SocialMailGrupoParticipanteVO participante = new SocialMailGrupoParticipanteVO();
                    participante.setSocialMailGrupo(smgp);
                    participante.getParticipante().setCodigo(Integer.valueOf(str));
                    dao.incluirParticipante(participante);
                }
            }
            
        }
    }
}

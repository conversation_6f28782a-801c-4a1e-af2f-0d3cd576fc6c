package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Date;

public class ProcessoCorrecaoDatasPJBank {

    public static Integer corrigir(Connection con, Integer recibo) throws Exception {
        Integer ajustado = 0;
        try {
            Uteis.logarDebug("ProcessoCorrecaoDatasPJBank | INICIO!");

            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("rp.codigo as recibo, \n");
            sql.append("mp.codigo as pagamento, \n");
            sql.append("rp.data as datarec, \n");
            sql.append("mp.datalancamento  as lanca_mov, \n");
            sql.append("mp.dataquitacao as quita_mov, \n");
            sql.append("b.datacredito as credito_boleto, \n");
            sql.append("b.datapagamento as pag_boleto   \n");
            sql.append("from boletospjbank b \n");
            sql.append("inner join recibopagamento rp on rp.codigo = b.recibopagamento \n");
            sql.append("inner join movpagamento mp on mp.codigo = b.movpagamento \n");
            sql.append("where rp.data::date <> mp.datalancamento::date \n");
            sql.append("and b.datapagamento is not null \n");
            sql.append("and b.movpagamento is not null \n");
            if (!UteisValidacao.emptyNumber(recibo)) {
                sql.append("and b.recibopagamento = ").append(recibo).append(" \n");
            }

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            Integer atual = 0;
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        String msg = "";
                        Integer movPagamento = 0;
                        try {

                            movPagamento = rs.getInt("pagamento");
                            Date dataRecibo = rs.getTimestamp("datarec");
                            Date dataPagamentoBoleto = rs.getTimestamp("pag_boleto");

                            if (UteisValidacao.emptyNumber(movPagamento)) {
                                throw new Exception("MovPagamento não encontrado");
                            }
                            if (dataRecibo == null) {
                                throw new Exception("dataRecibo não encontrado");
                            }
                            if (dataPagamentoBoleto == null) {
                                throw new Exception("dataPagamentoBoleto não encontrado");
                            }

                            String sqlUpdate = "update movpagamento set datalancamento = ?, dataquitacao = ? where codigo = ?";
                            try (PreparedStatement pst = con.prepareStatement(sqlUpdate)) {
                                pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataRecibo));
                                pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataPagamentoBoleto));
                                pst.setInt(3, movPagamento);
                                pst.execute();
                            }
                            ajustado++;
                            msg += "Ajustado";
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            msg = ex.getMessage();
                        } finally {
                            Uteis.logarDebug("ProcessoCorrecaoDatasPJBank | " + ++atual + "/" + total + " - MovPagamento " + movPagamento + " | Resultado: " + msg);
                        }
                    }
                }
            }
        } finally {
            Uteis.logarDebug("ProcessoCorrecaoDatasPJBank | FIM!");
        }
        return ajustado;
    }

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("******************************************************", "zillyonweb", "pactodb");
            ProcessoCorrecaoDatasPJBank.corrigir(con, 0);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

}

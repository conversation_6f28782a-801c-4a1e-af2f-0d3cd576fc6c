package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lu<PERSON> Felipe",
        data = "28/01/2025",
        descricao = "E2-501 - Caixa em aberto",
        motivacao = "E2-501 - Caixa em aberto")
public class AtualizacaoTicketE2501 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.tokenboleto (\n" +
                    "\tcodigo serial4 NOT NULL,\n" +
                    "\tdataregistro timestamp DEFAULT now() NULL,\n" +
                    "\ttoken text,\n" +
                    "\tdados text,\n" +
                    "\tCONSTRAINT tokenboleto_pkey PRIMARY KEY (codigo)\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX tokenboleto_token_idx ON public.tokenboleto USING btree (token);", c);
        }
    }
}

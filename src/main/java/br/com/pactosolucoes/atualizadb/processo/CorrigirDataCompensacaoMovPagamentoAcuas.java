package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.MovPagamento;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 29/09/2021
 */
public class CorrigirDataCompensacaoMovPagamentoAcuas {

    public static void main(String... args) {
        try {
            Connection con = DriverManager.getConnection("***************************************************", "zillyonweb", "pactodb");
            processar("", con);
        } catch (Exception ex) {
            Logger.getLogger(CorrigirDataCompensacaoMovPagamentoAcuas.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void processar(String listaMatriculas, Connection con) throws Exception {
        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(con);
            Map<Integer, EmpresaVO> mapaEmpresa = empresaDAO.obterMapaEmpresas(Uteis.NIVELMONTARDADOS_TODOS);

            if (UteisValidacao.emptyString(listaMatriculas)) {
                //corrigir todos os alunos
                StringBuilder sql = obterSQL(null);
                Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
                Uteis.logarDebug("TODOS OS CLIENTES | Total --> " + total);
                corrigirMatricula(sql, mapaEmpresa, con);
            } else {
                Uteis.logarDebug("LISTA MATRICULAS | Total --> " + listaMatriculas.split(",").length);
                for (String matricula : listaMatriculas.split(",")) {
                    try {
                        Uteis.logarDebug("Ajustar | Mat.: " + matricula);
                        StringBuilder sql = obterSQL(matricula);
                        corrigirMatricula(sql, mapaEmpresa, con);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            empresaDAO = null;
        }
    }

    private static void corrigirMatricula(StringBuilder sql, Map<Integer, EmpresaVO> mapaEmpresa, Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {
            String msg = "";
            Integer movpagamento = 0;
            String matricula = "";
            String nome = "";
            Date data = null;
            try {
                con.setAutoCommit(false);

                movpagamento = rs.getInt("movpagamento");
                matricula = rs.getString("matricula");
                nome = rs.getString("nome");
                data = rs.getDate("datapagamento");

                corrigirMovPagamento(movpagamento, mapaEmpresa, con);
                msg = "Ajustado";
                con.commit();
            } catch (Exception ex) {
                ex.printStackTrace();
                con.rollback();
                msg = ex.getMessage();
            } finally {
                con.setAutoCommit(true);
                Uteis.logarDebug("Mat " + matricula + " - " + nome + " | MovPagamento " + movpagamento + " | Data " + Uteis.getData(data) + " | Resultado: " + msg);
            }
        }
    }

    private static StringBuilder obterSQL(String matricula) {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("sql.*, \n");
        sql.append("DATE_PART('day', parcela2 - parcela1) as diferenca \n");
        sql.append("from ( \n");
        sql.append("select  \n");
        sql.append("cl.matricula, \n");
        sql.append("p.nome, \n");
        sql.append("mp.codigo as movpagamento, \n");
        sql.append("mp.datapagamento, \n");
        sql.append("(select datacompesancao::timestamp from cartaocredito where movpagamento = mp.codigo and nrparcela = 1) as parcela1, \n");
        sql.append("(select datacompesancao::timestamp from cartaocredito where movpagamento = mp.codigo and nrparcela = 2) as parcela2 \n");
        sql.append("from movpagamento mp \n");
        sql.append("inner join formapagamento fp on fp.codigo = mp.formapagamento \n");
        sql.append("inner join cliente cl on cl.pessoa = mp.pessoa \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
        sql.append("where mp.formapagamento = 9 \n");
        if (UteisValidacao.emptyString(matricula)) {
            sql.append("and not exists(select codigo from chequecartaolote where cartao in (select codigo from cartaocredito  where movpagamento  = mp.codigo)) \n");
            sql.append("and exists (select codigo from cartaocredito where movpagamento = mp.codigo and nrparcela = 12) \n");
            sql.append("and not exists (select codigo from cartaocredito where dataoriginal is not null and movpagamento = mp.codigo) \n");
            sql.append("and (select count(*) from cartaocredito where movpagamento = mp.codigo and nrparcela = 1) = 1 \n");
            sql.append("and (select count(*) from cartaocredito where movpagamento = mp.codigo and nrparcela = 2) = 1 \n");
        } else {
            sql.append("and (cl.codigomatricula = ").append(matricula).append(" or cl.matricula = '").append(matricula).append("') \n");
        }
        sql.append("and DATE_PART('day', (select datacompesancao::timestamp from cartaocredito where movpagamento = mp.codigo and nrparcela = 2) - (select datacompesancao::timestamp from cartaocredito where movpagamento = mp.codigo and nrparcela = 1)) = 0 \n");
        sql.append(") as sql \n");
        sql.append("order by sql.movpagamento \n");
//        sql.append("limit 2 \n");
        return sql;
    }

    private static void corrigirMovPagamento(Integer movPagamento, Map<Integer, EmpresaVO> mapaEmpresa, Connection con) throws Exception {
        MovPagamento movPagamentoDAO = null;
        try {
            movPagamentoDAO = new MovPagamento(con);

            if (UteisValidacao.emptyNumber(movPagamento)) {
                throw new Exception("Necessário informar MovPagamento");
            }

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("c.codigo as cartao, \n");
            sql.append("c.datacompesancao as datacompensacaoanterior, \n");
            sql.append("c.dataoriginal as dataoriginalanterior, \n");
            sql.append("c.nrparcela, \n");
            sql.append("fp.diascompensacaocartaocredito, \n");
            sql.append("fp.compensacaodiasuteis, \n");
            sql.append("mp.datapagamento, \n");
            sql.append("mp.empresa \n");
            sql.append("from cartaocredito c \n");
            sql.append("inner join movpagamento mp on mp.codigo = c.movpagamento \n");
            sql.append("inner join formapagamento fp on fp.codigo = mp.formapagamento \n");
            sql.append("where 1 = 1 \n");
//            if (!UteisValidacao.emptyNumber(recibo)) {
//                sql.append("and mp.recibopagamento = ").append(recibo).append(" \n");
//            }
            if (!UteisValidacao.emptyNumber(movPagamento)) {
                sql.append("and c.movpagamento = ").append(movPagamento).append(" \n");
            }
            sql.append("order by c.nrparcela \n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            Date dataBase = null;

            while (rs.next()) {
                if (dataBase == null) {
                    dataBase = rs.getDate("datapagamento");
                }
                dataBase = Uteis.somarDias(dataBase, rs.getInt("diascompensacaocartaocredito"));

                Date dataCompensacao = dataBase;
                if (rs.getBoolean("compensacaodiasuteis")) {
                    dataCompensacao = movPagamentoDAO.obterProximoDiaUtil(dataCompensacao, mapaEmpresa.get(rs.getInt("empresa")));
                }
                Date dataCompensacaoAnterior = rs.getDate("datacompensacaoanterior");
                Date dataOriginalAnterior = rs.getDate("dataoriginalanterior");


                StringBuilder update = new StringBuilder();
                update.append("update cartaocredito set datacompesancao = '").append(Uteis.getDataJDBC(dataCompensacao)).append("' ");
                if (dataOriginalAnterior == null && dataCompensacaoAnterior != null) {
                    update.append(", dataoriginal = '").append(Uteis.getDataJDBC(dataCompensacaoAnterior)).append("' ");
                }
                update.append("where codigo = ").append(rs.getInt("cartao")).append(";");
                SuperFacadeJDBC.executarConsultaUpdate(update.toString(), con);
            }
        } finally {
            movPagamentoDAO = null;
        }
    }
}

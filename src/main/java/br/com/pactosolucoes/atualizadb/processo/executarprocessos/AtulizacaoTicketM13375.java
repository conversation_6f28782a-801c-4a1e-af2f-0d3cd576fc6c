package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Marcos Andre",
        data = "13/11/2024",
        descricao = "Ajusta tamanho do campo complemento da tabela de empresa para 100 caracteres.",
        motivacao = "M1- 3375 ")
public class AtulizacaoTicketM13375  implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE empresa ALTER COLUMN complemento TYPE character varying(100);", c);
        }
    }
}

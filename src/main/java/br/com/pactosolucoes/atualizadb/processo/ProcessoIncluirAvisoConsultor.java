package br.com.pactosolucoes.atualizadb.processo;

import importador.LeitorExcel2010;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteMensagem;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProcessoIncluirAvisoConsultor {

    private static List<String> falhas = new ArrayList<>();

    public static void main(String[] args) throws Exception {

        Connection con = DriverManager.getConnection("************************************************", "postgres", "pactodb");
        Integer codigoEmpresa = 5;
        String caminhoPlanilha = "C:\\pacto\\backups\\InadimplentesGaranhuns.xlsx";

        Cliente clienteDAO = new Cliente(con);
        ClienteMensagem clienteMensagemDAO = new ClienteMensagem(con);

        Map<String, List<String>> map = new HashMap<>(); // cpf_nome_turma -> mensagens
        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(caminhoPlanilha));
        System.out.println("Lendo dados...");
        for (XSSFRow row : hssfRows) {
            String turma = LeitorExcel2010.obterString(row, 0);
            String cpf = LeitorExcel2010.obterString(row, 1);
            String nome = LeitorExcel2010.obterString(row, 2);
            Date data = LeitorExcel2010.obterData(row, 3, "dd/MM/yyyy");
            Double valor = LeitorExcel2010.obterDouble(row, 4);

            String msg = String.format("Débito remanescente do SCA na Turma %s aluno CPF %s nome %s no valor de R$ %s vencido em %s", turma, Uteis.formatarCpfCnpj(cpf, false), nome,  Uteis.formatarValorEmReal(valor), Calendario.getDataAplicandoFormatacao(data, "dd/MM/yyyy"));

            if (map.get(cpf) == null) {
                map.put(cpf, new ArrayList<>());
            }
            map.get(cpf).add(msg);
        }

        int total = map.size();
        int atual = 0;

        for (String cpf : map.keySet()) {
            System.out.println("Processando " + (++atual) + " de " + total + " - CPF: " + cpf);


            List<ClienteVO> clienteVOS = clienteDAO.consultarPorCPF(cpf, codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyList(clienteVOS)) {
                if (clienteVOS.size() == 1) {
                    ClienteVO clienteVO = clienteVOS.get(0);

                    String mensagem = "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n" +
                            "<html>\n" +
                            "<head>\n" +
                            "<title>Untitled document</title>\n" +
                            "</head>\n" +
                            "<body>";

                    for (String msg: map.get(cpf)) {
                        mensagem += "<p>" + msg + "</p>";
                    }
                    mensagem = mensagem + "</body>\n" +
                            "</html>";

                    ClienteMensagemVO clienteMensagemVO = new ClienteMensagemVO();
                    clienteMensagemVO.setMensagem(mensagem);
                    clienteMensagemVO.setCliente(clienteVO);
                    clienteMensagemVO.setTipomensagem(TiposMensagensEnum.AVISO_CONSULTOR);
                    clienteMensagemVO.setUsuario(new UsuarioVO());
                    clienteMensagemVO.getUsuario().setCodigo(1);
                    clienteMensagemDAO.incluir(clienteMensagemVO);

                } else {
                    String msg = "CPF duplicado: " + cpf;
                    System.out.println(msg);
                    falhas.add(msg);
                }
            } else {
                String msg = "CPF não encontrado: " + cpf;
                System.out.println(msg);
                falhas.add(msg);
            }
        }

    }

}

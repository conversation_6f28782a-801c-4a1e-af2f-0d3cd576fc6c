package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Alisson Melo",
        data = "15/10/2024",
        descricao = "TW-717 - Criar recurso reserva de equipamento para aula coletiva",
        motivacao = "TW-717 ")
public class AtualizacaoTicketTW717 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE turma ADD COLUMN tipoReservaEquipamento VARCHAR(50)", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE turma ADD COLUMN mapaEquipamentos VARCHAR(400);", c);
        }
    }

}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Victor <PERSON>",
        data = "23/04/2025",
        descricao = "Limitar a quantidade de aulas experimentais por aula por dia ",
        motivacao = "IA-884")
public class AtualizacaoTicketIA884 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE horarioturma \n" +
                    " ADD COLUMN qtdemaximaalunoexperimental  INTEGER DEFAULT 0;", c);
        }
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import static br.com.pactosolucoes.atualizadb.processo.AjustarLinhaDoTempoDeContratos.ajustarContratos;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class AjustarDuplicacoesMetaFaltosos {
     public static void main(String... args) {
        try {
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "mega");
            ajustarMetasFaltosos(c);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public static void ajustarMetas(Connection con) throws Exception {
        ajustarMetasFaltosos(con);
    }

    public static void ajustarMetasFaltosos(Connection con) throws Exception {
        String consultaMetas = "select count(fd.cliente), fd.fecharmeta,fd.cliente,max(fd.codigo) as maiorcodigo from fecharmetadetalhado fd inner join fecharmeta fm  on fm.codigo = fd.fecharmeta where fm.identificadormeta = 'FA' group by fd.cliente,fd.fecharmeta having count(fd.cliente) > 1";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(consultaMetas, con);
        while (consulta.next()) {
            ResultSet sucesso = SuperFacadeJDBC.criarConsulta("select obtevesucesso from fecharmetadetalhado where  codigo = "+consulta.getInt("maiorcodigo"), con);
            sucesso.next();
            if (sucesso.getBoolean("obtevesucesso")) {
                SuperFacadeJDBC.executarConsultaUpdate("delete from fecharmetadetalhado  where codigo <> "+consulta.getInt("maiorcodigo")+" and fecharmeta = "+consulta.getInt("fecharmeta")+" and cliente = "+consulta.getInt("cliente"), con);
            } else {
                SuperFacadeJDBC.executarConsultaUpdate("delete from fecharmetadetalhado  where codigo = "+consulta.getInt("maiorcodigo"), con);
            }
             ResultSet meta = SuperFacadeJDBC.criarConsulta("select count(codigo) as meta from fecharmetadetalhado  where fecharmeta  ="+consulta.getInt("fecharmeta"), con);
             meta.next();
             ResultSet metaAtingida = SuperFacadeJDBC.criarConsulta("select count(codigo) as atingida from fecharmetadetalhado  where fecharmeta  = "+consulta.getInt("fecharmeta")+" and obtevesucesso  = 't'", con);
             metaAtingida.next();
             Double porcentagem = 0.0;
             
             if(meta.getInt("meta") > 0){
                     Uteis.arredondarForcando2CasasDecimais((metaAtingida.getInt("atingida") * 100) / meta.getInt("meta"));
             }
             SuperFacadeJDBC.executarConsultaUpdate("update fecharmeta set porcentagem = "+porcentagem+" , metaatingida ="+metaAtingida.getInt("atingida")+"  , meta = "+meta.getInt("meta")+" where codigo = "+consulta.getInt("fecharmeta"), con);
             
             
            
        }
        String afastados = "select fd.fecharmeta,fd.cliente,fd.codigo,ab.dia,fm.identificadormeta,fd.obtevesucesso from fecharmetadetalhado fd "
                + " inner join fecharmeta fm  on fm.codigo = fd.fecharmeta and fm.identificadormeta = 'FA' "
                + " inner join aberturameta ab on ab.codigo = fm.aberturameta  "
                + " inner join cliente c on c.codigo = fd.cliente "
                + " inner join  contrato con on con.pessoa = c.pessoa "
                + " left join contratooperacao op on op.contrato = con.codigo and ab.dia between op.datainicioefetivacaooperacao and op.datafimefetivacaooperacao  and op.tipooperacao in ('AT','CR') "
                + " where op.codigo is not null";
        ResultSet consultaAfastados = SuperFacadeJDBC.criarConsulta(afastados, con);
        while (consultaAfastados.next()) {
            SuperFacadeJDBC.executarConsultaUpdate("delete from fecharmetadetalhado  where codigo = "+consultaAfastados.getInt("codigo"), con);
             ResultSet meta = SuperFacadeJDBC.criarConsulta("select count(codigo) as meta from fecharmetadetalhado  where fecharmeta  ="+consultaAfastados.getInt("fecharmeta"), con);
             meta.next();
             ResultSet metaAtingida = SuperFacadeJDBC.criarConsulta("select count(codigo) as atingida from fecharmetadetalhado  where fecharmeta  = "+consultaAfastados.getInt("fecharmeta")+" and obtevesucesso  = 't'", con);
             metaAtingida.next();
             
             Double porcentagem = 0.0;
             if (meta.getInt("meta") > 0){
                porcentagem = Uteis.arredondarForcando2CasasDecimais((metaAtingida.getInt("atingida") * 100) / meta.getInt("meta"));
             }
             SuperFacadeJDBC.executarConsultaUpdate("update fecharmeta set porcentagem = "+porcentagem+" , metaatingida ="+metaAtingida.getInt("atingida")+"  , meta = "+meta.getInt("meta")+" where codigo = "+consultaAfastados.getInt("fecharmeta"), con);
             
             
        }
    }
    
}

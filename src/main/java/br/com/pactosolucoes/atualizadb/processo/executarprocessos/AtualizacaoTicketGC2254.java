package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "02/06/2025",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-2254")
public class AtualizacaoTicketGC2254 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table contratoassinaturadigital add column ip varchar(50);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table contratoassinaturadigital add column tipoAutenticacao varchar(5);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table contratoassinaturadigital add column dadosAutenticacao text;", c);
        }
    }
}

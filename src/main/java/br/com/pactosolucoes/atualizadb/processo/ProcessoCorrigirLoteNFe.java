/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import controle.basico.SuporteControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ProcessoCorrigirLoteNFe extends SuporteControle {


    public static final String ERRO_AO_VALIDAR_NOTAS_NO_SERVIDOR_VERIFIQUE_NO_SUPORTE = "Erro ao validar notas no servidor. Verifique no Suporte!";

    public static void main(String... args) {
        try {
            String[] chaves = new String[]{
                    "banco###******************************************************************"
            };
            for (String key : chaves) {
                String[] partes = key.split("###");
                String chave = partes[0];
                String nomeBD = partes[1];
                System.out.println("Chave realizada: " + key);

                //DAO dao = new DAO();
                //Connection con = dao.obterConexaoEspecifica(chave);
                Connection con = DriverManager.getConnection(nomeBD, "postgres", "pactodb");

                Conexao.guardarConexaoForJ2SE(chave, con);
                corrigirLoteNFe(con, false, new AtomicInteger(0), new AtomicInteger(0), new AtomicInteger(0));
//                prepararParaReenviarNotasLoteZero(con);
//                prepararParaReenviarNFCsIdZerado(con);
//                reenviarNotasAguardandoEnvio(con);
            }


        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void reenviarNotasAguardandoEnvio(Connection con) throws Exception {
        NotaFiscal notaFiscal = new NotaFiscal(con);
        notaFiscal.enviarNotasAguardando();
    }

    private static void prepararParaReenviarNotasLoteZero(Connection con) throws Exception {
        String prepararNotasLoteZeradoParaReenviar = "UPDATE nfseemitida SET situacaoNotaFiscal = " + SituacaoNotaFiscalEnum.GERADA.getCodigo() + " WHERE coalesce(rps, 0) = 0 and dataemissao >= '2019-10-01'";
        SuperFacadeJDBC.executarConsultaUpdate(prepararNotasLoteZeradoParaReenviar, con);
    }

    private static void prepararParaReenviarNFCsIdZerado(Connection con) throws Exception {
        String prepararNotasLoteZeradoParaReenviar = "UPDATE notafiscalconsumidoreletronica SET situacaonotafiscal = 1" + SituacaoNotaFiscalEnum.GERADA.getCodigo() + " WHERE id_nfce = 0 and dataregistro >= '2019-10-01'";
        SuperFacadeJDBC.executarConsultaUpdate(prepararNotasLoteZeradoParaReenviar, con);
    }

    public static void corrigirLoteNFe(Connection con, boolean excluirNotasZeradas, AtomicInteger qtdNotasEncontradas, AtomicInteger qtdNotasCorrigidas, AtomicInteger qtdNotasExcluidas) throws Exception {

        if (con == null) {
            con = getFacade().getCliente().getCon();
        }
        Empresa empresaDAO = new Empresa(con);
        List<EmpresaVO> empresas = empresaDAO.consultarEmpresas();
        for (EmpresaVO empresa : empresas) {
            if (!empresa.isAtiva()) {
                continue;
            }
            if (!UteisValidacao.emptyString(empresa.getChaveNFSe())) {
                List<NFSeEmitidaVO> notasBugadas = consultarNotasBugadas(empresa, con);

                StringBuilder idReferencias = new StringBuilder();
                for (NFSeEmitidaVO nfse : notasBugadas) {
                    idReferencias.append("'").append(nfse.getIdReferencia()).append("',");
                }

                qtdNotasEncontradas.getAndAdd(notasBugadas.size()) ;
                System.out.println("Encontradas: " + notasBugadas.size() + " notas sem lote");

                if (idReferencias.length() > 0) {
                    idReferencias.deleteCharAt(idReferencias.length() - 1);

                    JSONArray retornoNotas = buscarNotasENotas(idReferencias.toString(), empresa.getChaveNFSe());
                    for (int i = 0; i < retornoNotas.length(); i++) {
                        String idLote = retornoNotas.getJSONObject(i).getString("idLote");
                        String idReferencia = retornoNotas.getJSONObject(i).getString("idReferencia");

                        SuperFacadeJDBC.executarConsultaUpdate("UPDATE nfseemitida SET rps = " + idLote + " WHERE idreferencia = '" + idReferencia + "';", con);

                        System.out.println("Corrigido idReferencia: " + idReferencia);
                        qtdNotasCorrigidas.getAndAdd(1);
                    }
                }

                if (excluirNotasZeradas) {
                    excluirNotasNaoCorrigidas(empresa, con, qtdNotasExcluidas);
                }
            }

        }
    }

    private static List<NFSeEmitidaVO> consultarNotasBugadas(EmpresaVO empresaVO, Connection con) throws SQLException {
        String sql = "SELECT codigo, idreferencia FROM nfseemitida WHERE rps is null AND empresa = " + empresaVO.getCodigo() + " AND coalesce(idreferencia, '') <> '' AND enotas is false ;";

        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        List<NFSeEmitidaVO> listaDasBugdas = new ArrayList<NFSeEmitidaVO>();

        while (rs.next()) {
            NFSeEmitidaVO nfse = new NFSeEmitidaVO();
            nfse.setCodigo(rs.getInt("codigo"));
            nfse.setIdReferencia(rs.getString("idreferencia"));
            listaDasBugdas.add(nfse);
        }

        return listaDasBugdas;
    }

    public static void excluirNotasNaoCorrigidas(EmpresaVO empresaVO, Connection con, AtomicInteger qtdNotasExcluidas) throws Exception {
        if (con == null) {
            con = getFacade().getCliente().getCon();
        }
        String sql = "";
        sql = "DELETE FROM nfseemitidahistorico WHERE nfseemitida IN (\n" +
                "SELECT codigo FROM nfseemitida nf WHERE rps is null AND empresa = " + empresaVO.getCodigo() + " AND coalesce(idreferencia, '') <> '' AND enotas is false \n" +
                ")";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int qtdAfetada = ps.executeUpdate();
            System.out.println("NFSemitidaHistorico deletadas: " + qtdAfetada);
        }

        sql = "DELETE FROM nfseemitida WHERE rps is null AND empresa = " + empresaVO.getCodigo() + " AND coalesce(idreferencia, '') <> '' AND enotas is false ;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int qtdAfetada = ps.executeUpdate();
            System.out.println("Notas excluídas: " + qtdAfetada);
            qtdNotasExcluidas.getAndAdd(qtdAfetada);
        }
    }

    private static Connection getConexaoNotas() throws Exception {
        String driver = "net.sourceforge.jtds.jdbc.Driver";
        String conexao = "jdbc:jtds:sqlserver:";
        Class.forName(driver).newInstance();

        return DriverManager.getConnection(conexao + "//nfe2.pactosolucoes.com.br:1470/DBNFSe", "sa", "pactodb");
    }

    private static JSONArray buscarNotasENotas(String idsReferencia, String chaveEmpresa) throws Exception {
        try {

            URL url = new URL(getUrlModuloNFSe().concat("/nota?corrigirLoteZero=true&chaveEmpresa=" + chaveEmpresa));
            //URL url = new URL("http://localhost:8080/ZillyonWeb".concat("/nota?corrigirLoteZero=true&chaveEmpresa=" + chaveEmpresa));

            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", "application/json; utf-8");
            con.setRequestProperty("Accept", "application/json");
            con.setUseCaches(false);
            con.setDoInput(true);
            con.setDoOutput(true);

            try (OutputStream os = con.getOutputStream()) {
                byte[] input = idsReferencia.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            JSONArray notas;
            try (BufferedReader br = new BufferedReader(new InputStreamReader(con.getInputStream(), "utf-8"))) {
                String jsonResponse = br.lines().collect(Collectors.joining());
                notas = new JSONArray(jsonResponse);
            }
            con.disconnect();
            return notas;
        } catch (Exception e) {
            Uteis.logar(e, ProcessoCorrigirLoteNFe.class);
            throw new Exception(ERRO_AO_VALIDAR_NOTAS_NO_SERVIDOR_VERIFIQUE_NO_SUPORTE);
        }
    }

}

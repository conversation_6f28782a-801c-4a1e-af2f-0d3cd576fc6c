package br.com.pactosolucoes.atualizadb.processo;

import negocio.facade.jdbc.memcached.Wiki;
import servicos.propriedades.PropsService;

/**
 * Created by <PERSON> on 02/07/2016.
 */
public class AtualizarDadosCacheWiki {
    private static boolean integracaoWiki = false;

    static {
      integracaoWiki = PropsService.isTrue(PropsService.integracaoWiki);
    }
    public static void main(String []args){
        atualizarDadosWikiCache();
    }
    public static void atualizarDadosWikiCache(){
        try {
            if(integracaoWiki) {
                Wiki wiki = new Wiki();
                wiki.obterSalvarCacheHintsWiki();
                System.out.println("DADOS WIKI CACHE ATUALIZADO COM SUCESSO!------------------------------------------------");
            }
        }catch (Exception ex){
           System.out.println("FALHA AO ATUALIZAR DADOS WIKI EM CACHE!--------------------------------------------------");
        }

    }
}

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.HistoricoPontos;

import java.sql.*;

public class ProcessoAjustarPontosRemovidosIndevidamenteDesistenciaUnique {

    public static void main(String... args) {
        try {
            Connection con = DriverManager.getConnection("**********************************************************", "postgres", "pactodb");
            ajustarPontosRemovidosIndevidamenteDesistencia(con);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void ajustarPontosRemovidosIndevidamenteDesistencia(Connection con) throws Exception {

        String sqlAlunosHistoricoPontosAjustar = "select \n" +
                "\textract(hour from hp.dataconfirmacao),cli.codigo<PERSON><PERSON>ula,pes.nome,cli.pessoa,s.situacaocontrato,\n" +
                "\te.codigo as empresa,e.nome,\n" +
                "\thp.*\n" +
                "from historicopontos hp\n" +
                "\tinner join cliente cli on cli.codigo = hp.cliente \n" +
                "\tinner join pessoa pes on pes.codigo = cli.pessoa \n" +
                "\tinner join situacaoclientesinteticodw s on s.codigocliente = cli.codigo\n" +
                "\tinner join empresa e on e.codigo = cli.empresa\n" +
                "where 1 = 1\n" +
//                "and hp.cliente = 16758" +
                "and hp.descricao = 'Reajuste de pontos por inativação do contrato'\n" +
                "and hp.tipodepontos = 3\n" +
                "and s.situacaocontrato in ('NO','VE','TR')\n" +
                "and pes.codigo in (select con.pessoa from contrato con where con.pessoa = pes.codigo and hp.dataconfirmacao between con.vigenciade and con.vigenciaateajustada)\n" +
                "and extract(hour from hp.dataconfirmacao) between 0 and 5\n" +
                "order by hp.cliente,hp.codigo;";

        ResultSet rsHistoricosPontosAjustar = SuperFacadeJDBC.criarConsulta(sqlAlunosHistoricoPontosAjustar, con);

        while (rsHistoricosPontosAjustar.next()) {
            System.out.println("Ajustando pontos do cliente: "+rsHistoricosPontosAjustar.getInt("cliente"));
            String sqlDeletarReajustePontosCliente = "delete from historicopontos where codigo = " + rsHistoricosPontosAjustar.getInt("codigo");
            SuperFacadeJDBC.executarUpdate(sqlDeletarReajustePontosCliente, con);

            String sqlHistoricoPontosCliente = "select * from historicopontos where cliente = " + rsHistoricosPontosAjustar.getInt("cliente")
                    + " order by codigo";
            ResultSet rsHistoricoPontosCliente = SuperFacadeJDBC.criarConsulta(sqlHistoricoPontosCliente, con);

            HistoricoPontosVO historicoPontosVOAnterior = new HistoricoPontosVO();
            while (rsHistoricoPontosCliente.next()) {
                HistoricoPontos historicoPontosDao = new HistoricoPontos(con);
                HistoricoPontosVO historicoPontosVO = historicoPontosDao.consultarPorChavePrimaria(rsHistoricoPontosCliente.getInt("codigo"),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if(historicoPontosVOAnterior.getCodigo() == 0){
                    historicoPontosVOAnterior = historicoPontosVO;
                    continue;
                }

                if(historicoPontosVO.getEntrada() == true){
                    historicoPontosVO.setPontostotal(historicoPontosVO.getPontos() + historicoPontosVOAnterior.getPontostotal());
                    String sqlAjustarHistoricoPontos = "update historicopontos set pontostotal = "+historicoPontosVO.getPontostotal()+" where codigo = "+historicoPontosVO.getCodigo();
                    SuperFacadeJDBC.executarUpdate(sqlAjustarHistoricoPontos,con);
                    historicoPontosVOAnterior = historicoPontosVO;
                } else {
                    if(historicoPontosVO.getPontos() == 0 && historicoPontosVO.getPontostotal() == 0){
                        historicoPontosVOAnterior = historicoPontosVO;
                    } else {
                        historicoPontosVO.setPontostotal(historicoPontosVO.getPontos() + historicoPontosVOAnterior.getPontostotal());
                        String sqlAjustarHistoricoPontos = "update historicopontos set pontostotal = "+historicoPontosVO.getPontostotal()+" where codigo = "+historicoPontosVO.getCodigo();
                        SuperFacadeJDBC.executarUpdate(sqlAjustarHistoricoPontos,con);
                        historicoPontosVOAnterior = historicoPontosVO;
                    }
                }
                historicoPontosDao = null;
            }
        }
    }
}

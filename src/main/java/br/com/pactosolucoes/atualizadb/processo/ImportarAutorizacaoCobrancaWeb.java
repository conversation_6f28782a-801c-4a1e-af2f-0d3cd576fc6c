package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import servicos.impl.maxiPago.AdquirenteMaxiPagoEnum;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ImportarAutorizacaoCobrancaWeb {

    public static void main(String[] args) {
        try {
            /* INFORMAÇÕES DE CONFIGURAÇÃO
             *
             * EXEMPLO DE URL CONEXÃO: "***************************************"
             *
             * args[0] = BANCO QUE IRÁ FORNECER OS DADOS DE COBRANÇA CLIENTES PARA IMPORTAR
             * args[1] = BANCO QUE IRÁ RECEBER OS DADOS DE COBRANÇA CLIENTES PARA IMPORTAR
             * args[3] = Convênio destino
             */

            String urlBancoOrigem;
            String urlBancoDestino;
            String convenioDestino;
            if (args.length == 0) {
                urlBancoOrigem = "*******************************************************";
                urlBancoDestino = "*******************************************************";
                convenioDestino = "2";
            } else if (args.length == 3) {
                urlBancoOrigem = args[0];
                urlBancoDestino = args[1];
                convenioDestino = args[2];

                Uteis.logar(null, "Configurações:");
                Uteis.logar(null, "urlBancoOrigem: " + urlBancoOrigem);
                Uteis.logar(null, "urlBancoDestino: " + urlBancoDestino);
                Uteis.logar(null, "convenioDestino: " + convenioDestino);
            } else {
                throw new ConsistirException("Parametros não informados: 1º urlBancoOrigem // 2º urlBancoDestino // 3º convenioDestino");
            }

            Connection conOrigem = DriverManager.getConnection(urlBancoOrigem, "postgres", "pactodb");
            Connection conDestino = DriverManager.getConnection(urlBancoDestino, "postgres", "pactodb");
            importarAutorizacaoCobrancaCliente(conOrigem, conDestino, Integer.parseInt(convenioDestino));
        } catch (Exception e) {
            Uteis.logar(null, e.getMessage());
        }
    }

    private static void importarAutorizacaoCobrancaCliente(Connection conOrigem, Connection conDestino, Integer codConvenioDestino) throws Exception {

        String sqlConsultarAutorizacaoCobrancaCliente = "SELECT \n" +
                "pes.nome, tipoautorizacao, numerocartao, validadecartao, acc.agencia, agenciadv, contacorrente, contacorrentedv, tipoacobrar, conveniocobranca, operadoracartao, nometitularcartao, cvv, adquirentemaxipago, ativa, renovadoautomatico\n" +
                "FROM autorizacaocobrancacliente acc\n" +
                "INNER JOIN cliente cli ON acc.cliente = cli.codigo\n" +
                "INNER JOIN pessoa pes ON pes.codigo = cli.pessoa;";

        PreparedStatement ps = conOrigem.prepareStatement(sqlConsultarAutorizacaoCobrancaCliente);
        ResultSet rs = ps.executeQuery();
        List<AutorizacaoCobrancaClienteVO> listaAutorizacoes = new ArrayList<AutorizacaoCobrancaClienteVO>();
        while (rs.next()) {
            ClienteVO clienteVO = new ClienteVO();
            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setNome(rs.getString("nome"));

            clienteVO.setPessoa(pessoaVO);

            AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO = new AutorizacaoCobrancaClienteVO();
            autorizacaoCobrancaClienteVO.setCliente(clienteVO);

            TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum = TipoAutorizacaoCobrancaEnum.valueOf(rs.getInt("tipoautorizacao"));
            autorizacaoCobrancaClienteVO.setTipoAutorizacao(tipoAutorizacaoCobrancaEnum);

            autorizacaoCobrancaClienteVO.setNumeroCartao(rs.getString("numerocartao"));
            autorizacaoCobrancaClienteVO.setValidadeCartao(rs.getString("validadecartao"));
            autorizacaoCobrancaClienteVO.setAgencia(rs.getInt("agencia"));
            autorizacaoCobrancaClienteVO.setAgenciaDV(rs.getString("agenciadv"));
            autorizacaoCobrancaClienteVO.setContaCorrente(rs.getLong("contacorrente"));
            autorizacaoCobrancaClienteVO.setContaCorrenteDV(rs.getString("contacorrentedv"));

            TipoObjetosCobrarEnum tipoObjetosCobrarEnum = TipoObjetosCobrarEnum.valueOf(rs.getInt("tipoacobrar"));
            autorizacaoCobrancaClienteVO.setTipoACobrar(tipoObjetosCobrarEnum);

            ConvenioCobrancaVO convenioCobrancaVO = new ConvenioCobrancaVO();
            convenioCobrancaVO.setCodigo(codConvenioDestino);
            autorizacaoCobrancaClienteVO.setConvenio(convenioCobrancaVO);

            OperadorasExternasAprovaFacilEnum operadorasExternasAprovaFacilEnum = OperadorasExternasAprovaFacilEnum.valueOf(rs.getInt("operadoracartao"));
            autorizacaoCobrancaClienteVO.setOperadoraCartao(operadorasExternasAprovaFacilEnum);

            autorizacaoCobrancaClienteVO.setNomeTitularCartao(rs.getString("nometitularcartao"));

            AdquirenteMaxiPagoEnum adquirenteMaxiPagoEnum = AdquirenteMaxiPagoEnum.valueOff(rs.getInt("adquirentemaxipago"));
            autorizacaoCobrancaClienteVO.setAdquirenteMaxiPago(adquirenteMaxiPagoEnum);

            autorizacaoCobrancaClienteVO.setAtiva(rs.getBoolean("ativa"));
            autorizacaoCobrancaClienteVO.setRenovadoAutomatico(rs.getBoolean("renovadoautomatico"));

            listaAutorizacoes.add(autorizacaoCobrancaClienteVO);
        }
        finalizarConexaoBD(ps, rs);

        String sqlConsultarPessoa = "SELECT cli.codigo as codCliente FROM cliente cli INNER JOIN pessoa pes ON pes.codigo = cli.pessoa WHERE pes.nome = ?";

        int i = listaAutorizacoes.size();
        int atual = 0;
        for (AutorizacaoCobrancaClienteVO acc : listaAutorizacoes) {

            List<ClienteVO> clientes = new ArrayList<ClienteVO>();
            ps = conDestino.prepareStatement(sqlConsultarPessoa);
            ps.setString(1, acc.getCliente().getPessoa().getNome());
            rs = ps.executeQuery();
            while (rs.next()) {
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(rs.getInt("codCliente"));
                clientes.add(clienteVO);
            }
            finalizarConexaoBD(ps, rs);

            for (ClienteVO clienteVO : clientes) {
                incluirAutorizacaoCobranca(acc, clienteVO, conDestino);
            }

            System.out.println(atual++ + "/" + i);
        }


        Uteis.logar(null, "###### TOTAL ALUNOS QUE NÃO FORAM IMPORTADOS ");
    }

    private static void incluirAutorizacaoCobranca(AutorizacaoCobrancaClienteVO acc, ClienteVO clienteVO, Connection connection) throws SQLException {
        String sqlInsert = "INSERT INTO autorizacaocobrancacliente (cliente, tipoautorizacao, numerocartao, " +
                "validadecartao, agencia, agenciadv, contacorrente, contacorrentedv, tipoacobrar, " +
                "conveniocobranca, operadoracartao, nometitularcartao, adquirentemaxipago, ativa, " +
                "renovadoautomatico) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";

        PreparedStatement ps = connection.prepareStatement(sqlInsert);
        int i = 0;
        ps.setInt(++i, clienteVO.getCodigo());
        ps.setInt(++i, acc.getTipoAutorizacao().getId());
        ps.setString(++i, acc.getNumeroCartao());

        ps.setString(++i, acc.getValidadeCartao());
        ps.setInt(++i, acc.getAgencia());
        ps.setString(++i, acc.getAgenciaDV());
        ps.setLong(++i, acc.getContaCorrente());
        ps.setString(++i, acc.getContaCorrenteDV());
        ps.setInt(++i, acc.getTipoACobrar().getId());

        ps.setInt(++i, acc.getConvenio().getCodigo());
        ps.setInt(++i, acc.getOperadoraCartao().getId());
        ps.setString(++i, acc.getNomeTitularCartao());
        ps.setInt(++i, acc.getAdquirenteMaxiPago().getId());
        ps.setBoolean(++i, acc.isAtiva());

        ps.setBoolean(++i, acc.isRenovadoAutomatico());

        ps.execute();

        finalizarConexaoBD(ps, null);
    }

    private static void finalizarConexaoBD(PreparedStatement ps, ResultSet rs) throws SQLException {
        if (ps != null && !ps.isClosed()) {
            ps.close();
        }

        if (rs != null && !rs.isClosed()) {
            rs.close();
        }

    }
}

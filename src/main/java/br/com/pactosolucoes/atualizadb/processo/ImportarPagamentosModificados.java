/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;


import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;


public class ImportarPagamentosModificados{

	 /**
    * Construtor que recebe um objeto que irá observa-lo
    * @param observador Objeto que irá acompanhar as mudanças deste objeto
    */
   public ImportarPagamentosModificados(){



   }


   public static void main(String[] args) {
	   try {
		Connection con = DriverManager.getConnection("*****************************************", "zillyonweb", "pactodb");
		corrigirChequesLote(con);
	} catch (SQLException e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	}
}

   public static void corrigirChequesLote(Connection con){
	   try {
		ResultSet set = SuperFacadeJDBC.criarConsulta("select * from cheque where codigo in " +
				"(select cheque from chequecartaolote where cheque is not null) " +
				"and movpagamento in (select codigo from movpagamento where id_recebe is not null)", con);
//				"and movpagamento in (68314)", con);
		while(set.next()){
			PreparedStatement stm = con.prepareStatement("SELECT * FROM cheque WHERE " +
					"agencia = '"+set.getString("agencia")+"' AND conta = '"
					+set.getString("conta")+"' AND numero = '"
					+set.getString("numero")+"' AND banco = "+set.getInt("banco")+
					" AND vistaouprazo = '"+set.getString("vistaouprazo")+
					"' and codigo <> "+set.getInt("codigo"));

			ResultSet query = stm.executeQuery();
			if(query.next()){
				SuperFacadeJDBC.executarConsulta("UPDATE chequecartaolote SET cheque = "+query.getInt("codigo")
						+ " where cheque = "+set.getInt("codigo"), con);
				SuperFacadeJDBC.executarConsulta("UPDATE historicocheque SET cheque = "+query.getInt("codigo")
						+ " where cheque = "+set.getInt("codigo"), con);

				SuperFacadeJDBC.executarConsulta("DELETE FROM cheque where codigo = "+set.getInt("codigo"), con);
			}
		}
	} catch (SQLException e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	} catch (Exception e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	}


   }




}

package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.Remessa;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;

public class ProcessoRemessasEConvenioBIN {

    public static void main(String[] args) {
        Connection conOAMD = null;
        try {
            //todas empresa ativas do oamd
            conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");

            String chavesFiltrar = "";
            ProcessoRemessasEConvenioBIN.ajustar(chavesFiltrar, conOAMD);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (conOAMD != null) {
                    conOAMD.close();
                }
            } catch (Exception ignored) {
            }
        }
    }

    public static void ajustar(String chavesFiltrar, Connection conOAMD) throws Exception {
        try {
            Uteis.logarDebug("ProcessoRemessasEConvenioBIN | INICIO!");

            StringBuilder sqlOAMD = new StringBuilder();
            sqlOAMD.append("select \n");
            sqlOAMD.append("distinct \n");
            sqlOAMD.append("e.chave, \n");
            sqlOAMD.append("e.\"hostBD\" as host, \n");
            sqlOAMD.append("e.porta, \n");
            sqlOAMD.append("e.\"userBD\" as user, \n");
            sqlOAMD.append("e.\"passwordBD\" as senha, \n");
            sqlOAMD.append("e.\"nomeBD\" as banco \n");
            sqlOAMD.append("from empresa e \n");
            sqlOAMD.append("inner join empresafinanceiro ef on ef.chavezw = e.chave \n");
            sqlOAMD.append("where e.ativa \n");
            if (!UteisValidacao.emptyString(chavesFiltrar)) {
                sqlOAMD.append("and e.chave in (").append(chavesFiltrar).append(") \n");
            } else {
                sqlOAMD.append("and exists(select codigo from z_oamd_pactopay_convenio_cobranca where empresafinanceiro_codigo = ef.codigo and tipo_convenio = ").append(TipoConvenioCobrancaEnum.DCC_BIN.getCodigo()).append(") \n");
            }

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sqlOAMD + " ) as sql", conOAMD);
            ResultSet rsEmpresa = SuperFacadeJDBC.criarConsulta(sqlOAMD.toString(), conOAMD);

            int atual = 0;
            while (rsEmpresa.next()) {
                Connection conEmp = null;
                try {
                    Uteis.logarDebug("ProcessoRemessasEConvenioBIN | " + ++atual + "/" + total);

                    conEmp = DriverManager.getConnection("jdbc:postgresql://" + rsEmpresa.getString("host") + ":" + rsEmpresa.getInt("porta") + "/" + rsEmpresa.getString("banco"),
                            rsEmpresa.getString("user"), rsEmpresa.getString("senha"));
                    excluirRemessas(rsEmpresa.getString("chave"), conEmp);
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    try {
                        if (conEmp != null) {
                            conEmp.close();
                        }
                    } catch (Exception ignored) {
                    }
                }
            }
        } finally {
            Uteis.logarDebug("ProcessoRemessasEConvenioBIN | FIM!");
        }
    }

    private static void excluirRemessas(String chave, Connection con) {
        Remessa remessaDAO;
        Usuario usuarioDAO;
        try {
            remessaDAO = new Remessa(con);
            usuarioDAO = new Usuario(con);

            UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();

            ResultSet rsRemessa = SuperFacadeJDBC.criarConsulta("select codigo,situacaoremessa,cancelamento,dataregistro,conveniocobranca,empresa from remessa where dataregistro::date >= '14/04/2022' and tipo = " + TipoRemessaEnum.DCC_BIN.getId(), con);
            while (rsRemessa.next()) {
                String msg = ("CHAVE " + chave + " | ");
                try {
                    RemessaVO remessaVO = new RemessaVO();
                    remessaVO.setCodigo(rsRemessa.getInt("codigo"));
                    msg += ("REMESSA " + remessaVO.getCodigo() + " | ");
                    remessaVO.setSituacaoRemessa(SituacaoRemessaEnum.valueOf(rsRemessa.getInt("situacaoremessa")));
                    remessaVO.setCancelamento(rsRemessa.getBoolean("cancelamento"));
                    remessaVO.setDataRegistro(rsRemessa.getTimestamp("dataregistro"));
                    remessaVO.getConvenioCobranca().setCodigo(rsRemessa.getInt("conveniocobranca"));
                    remessaVO.setEmpresa(rsRemessa.getInt("empresa"));
                    remessaVO.getEmpresaVO().setCodigo(rsRemessa.getInt("empresa"));

                    remessaDAO.excluirComLog(remessaVO, usuarioVO);

                    msg += ("SUCESSO");
                } catch (Exception ex) {
                    msg += ("ERRO: " + ex.getMessage());
                } finally {
                    Uteis.logarDebug(msg);
                }
            }

            SuperFacadeJDBC.executarUpdate("update conveniocobranca set bloquearcobrancaautomatica = true where tipoconvenio = " + TipoConvenioCobrancaEnum.DCC_BIN.getCodigo(), con);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            remessaDAO = null;
            usuarioDAO = null;
        }
    }
}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "26/06/2025",
        descricao = "processo para ajustar produtoestoque criado sem productoestoque_alteracaosit",
        motivacao = "GC-2440")
public class AtualizacaoTicketGC2440 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("INSERT INTO produtoestoque_alteracaosit (produtoestoque, datacadastro, usuario, situacao)\n" +
                    "SELECT p.codigo, '2025-05-01'::timestamp, 1, 'A'\n" +
                    "FROM produtoestoque p\n" +
                    "inner join produto pd on pd.codigo = p.produto\n" +
                    "WHERE NOT EXISTS (\n" +
                    "    SELECT 1\n" +
                    "    FROM produtoestoque_alteracaosit a\n" +
                    "    WHERE a.produtoestoque = p.codigo\n" +
                    "      AND a.datacadastro::date = '2025-05-01'\n" +
                    "      AND a.situacao = 'A'\n" +
                    ")\n" +
                    "and pd.tipoproduto = 'OC';", c);
        }
    }
}
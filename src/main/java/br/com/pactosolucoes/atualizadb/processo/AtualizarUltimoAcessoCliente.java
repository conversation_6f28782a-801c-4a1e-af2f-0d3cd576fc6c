/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.acesso.AcessoCliente;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

/**
 *
 * <AUTHOR>
 */
public class AtualizarUltimoAcessoCliente {
    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("*******************************************", "postgres", "pactodb");
            atualizarClientes(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void atualizarClientes(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select sw.codigocliente  from cliente c inner join situacaoclientesinteticodw sw on sw.codigocliente = c.codigo where (uacodigo  is null or sw.dataultimoacesso is null)  and exists (Select codigo from acessocliente where cliente = c.codigo order by codigo limit 1)", con);
        int cont = 0;
        Cliente cliDao = new Cliente(con);
        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
        AcessoCliente acessoDao = new AcessoCliente(con);
        ClienteVO cliente = null;
        while (consulta.next()) {
            cliente = cliDao.consultarPorCodigo(consulta.getInt("codigocliente"), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            cliente.setUaCliente(acessoDao.consultarUltimoAcesso(cliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    cliDao.registrarUltimoAcesso(cliente.getCodigo(), cliente.getUaCliente().getCodigo());
                     zwFacade.atualizarSintetico(cliente, Calendario.hoje(),
                            SituacaoClienteSinteticoEnum.GRUPO_DADOSACESSO, false);
            System.out.println(++cont + " - cliente " + consulta.getInt("codigocliente") + " foi atualizado");
        }
    }
}

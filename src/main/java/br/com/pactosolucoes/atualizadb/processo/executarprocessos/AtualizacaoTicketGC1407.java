package br.com.pactosolucoes.atualizadb.processo.executarprocessos;


import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anderson Rosa",
        data = "15/01/2025",
        descricao = "Controlar preenchimento de campo",
        motivacao = "GC-1407")
public class AtualizacaoTicketGC1407 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE plano ALTER COLUMN descricao type varchar;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE situacaoclientesinteticodw ALTER COLUMN nomeplano type varchar;", c);
        }
    }
}

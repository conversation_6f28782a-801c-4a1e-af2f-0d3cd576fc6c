package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 11/05/2016.
 */
public class CorrigirNomeAtividadeFicha {

    public static void main(String[] args) {
        try {
            corrigir("unique", "localhost", "localhost");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void corrigir(String chave, String hostoamd, String hostoamd2) throws Exception {

        Connection conOamd = DriverManager.getConnection("jdbc:postgresql://" + hostoamd + ":5432/OAMD", "postgres", "pactodb");
        Connection conOamd2 = DriverManager.getConnection("jdbc:postgresql://" + hostoamd2 + ":5432/OAMD2", "postgres", "pactodb");
        Date inicio = Calendario.hoje();
        System.out.println("Começando o processo para alterar nome atividades da ficha." + Uteis.getDataAplicandoFormatacao(inicio, "dd/MM HH:mm:ss"));

        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT * FROM empresa WHERE modulos LIKE '%TR%' "
                + (chave == null ? "" : "AND chave = '" + chave + "'"), conOamd);
        ResultSet rsCount = SuperFacadeJDBC.criarConsulta("SELECT count(codigo) as c FROM empresa "
                + "WHERE modulos LIKE '%TR%' "
                + (chave == null ? "" : "AND chave = '" + chave + "'"), conOamd);
        Integer total = rsCount.next() ? rsCount.getInt("c") : 0;
        int i = 0;
        while (rs.next()) {
            ResultSet rs2 = SuperFacadeJDBC.criarConsulta("SELECT * FROM empresa WHERE chave LIKE '"
                    + rs.getString("chave") + "'", conOamd2);
            if (rs2.next()) {
                try {

                    System.out.println(++i + "/" + total);
                    System.out.println("Alterando nome atividade ficha da chave :" + rs.getString("chave"));
                    boolean usaBDLocal = rs.getBoolean("usarBDLocal");
                    String robocontrole = rs.getString("robocontrole");
                    String urlTreino = rs.getString("urltreino");
                    if (!usaBDLocal) {
                        System.out.println("Servidor local");
                    }
                    String rb = robocontrole == null ? "" : robocontrole.replaceAll("http\\:\\/\\/", "");
                    String hostRC = usaBDLocal || robocontrole == null || robocontrole.isEmpty()
                            ? null : rb.contains("/") ? rb.substring(0, rb.indexOf("/")) : rb;
                    hostRC = hostRC == null ? null : hostRC.contains(":") ? hostRC.substring(0, rb.indexOf(":")) : hostRC;
                    String hosttr = urlTreino == null || urlTreino.isEmpty()
                            || urlTreino.contains("app2.pac") ? rs2.getString("hostBD") : hostRC;
                    String portatr = rs2.getString("porta");
                    String nomebdtr = rs2.getString("nomeBD");
                    String usertr = rs2.getString("userBD");
                    String senhatr = rs2.getString("passwordBD");
                    String urltr = "jdbc:postgresql://" + hosttr + ":" + portatr + "/" + nomebdtr;
                    Connection conTr = DriverManager.getConnection(urltr, usertr, senhatr);



                    String host = hostRC == null ? rs.getString("hostBD") : hostRC;
                    String porta = rs.getString("porta");
                    String nomebd = rs.getString("nomeBD");
                    String user = rs.getString("userBD");
                    String senha = rs.getString("passwordBD");
                    String url = "jdbc:postgresql://" + host + ":" + porta + "/" + nomebd;
                    Connection conZw = DriverManager.getConnection(url, user, senha);


                    System.out.println(corrigirNomeAtividadeFicha(conTr));
                    System.out.println("Terminei de migrar a chave " + rs.getString("chave"));
                } catch (Exception e) {
                    System.out.println("Erro:" + e.getMessage());
                }

            }
        }
        Date fim = Calendario.hoje();
        System.out.println("Processo concluído." + Uteis.getDataAplicandoFormatacao(inicio, "dd/MM HH:mm:ss"));
        System.out.println("Tempo gasto:" + ((fim.getTime() - inicio.getTime()) / 1000) + " segundos");
    }

    public static String corrigirNomeAtividadeFicha(Connection conTr) {
        try {
            ResultSet rsAt = SuperFacadeJDBC.criarConsulta("Select * from atividade ",conTr);
            while(rsAt.next()){
                int codigoAtividade = rsAt.getInt("codigo");
                List<String> revisoesNome = new ArrayList<String>();
                ResultSet atf = SuperFacadeJDBC.criarConsulta("Select * from atividadeficha where  nome != ' "+rsAt.getString("nome")+"' and atividade_codigo = "+codigoAtividade,conTr);
                while(atf.next()) {
                    if (!rsAt.getString("nome").equals(atf.getString("nome"))) {
                        if (revisoesNome.isEmpty()) {
                            ResultSet revs = SuperFacadeJDBC.criarConsulta("Select DISTINCT(NOME) from atividade_aud where revtype = 1 and codigo = " + codigoAtividade, conTr);
                            while (revs.next()) {
                                revisoesNome.add(revs.getString("nome"));
                            }
                        }
                        for (String revNome : revisoesNome) {
                            if (revNome.equals(atf.getString("nome"))) {
                                alterarNomeAtividadeFicha(atf.getInt("codigo"),atf.getInt("versao"), "", conTr);
                                break;
                            }
                        }
                    }
                }
            }
            return "Processado!";
        } catch (Exception e) {
            return e.getMessage();
        }
    }
    public static void alterarNomeAtividadeFicha(int codigoAtividadeFicha,int versao,String nome,Connection con) throws Exception{
        if(!UteisValidacao.emptyNumber(codigoAtividadeFicha)) {
            SuperFacadeJDBC.executarConsulta("UPDATE AtividadeFicha SET nome = '" + nome + "' " +  " ,versao = "+(versao + 1 )+" where codigo = " + codigoAtividadeFicha, con);
        }
    }

}

package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Hibrael A Alves",
        data = "14/04/2025",
        descricao = "PAY-540 - Diária com data inicio no vendas online",
        motivacao = "PAY-540 - Diária com data inicio no vendas online")
public class AtualizacaoTicketPAY540 implements MigracaoVersaoInterface{
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE vendasonlineconfig ADD COLUMN exibedatautilizacao BOOLEAN default false;", c);
        }
    }
}

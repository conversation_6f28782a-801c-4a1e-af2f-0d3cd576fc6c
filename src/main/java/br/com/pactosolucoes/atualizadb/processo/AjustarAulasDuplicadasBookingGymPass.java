package br.com.pactosolucoes.atualizadb.processo;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.commons.io.Charsets;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AjustarAulasDuplicadasBookingGymPass {

    public static void main(String[] args) throws Exception{
        boolean desativarAulasDuplicadas = false;
        String codigoGymPass = "9553";
        //obter token
        String token = getToken();
        JSONArray aulasGymPass = aulasGymPass(codigoGymPass, token);
        Map<String, List<JSONObject>> aulas = new HashMap<>();

        Connection con = DriverManager.getConnection("********************************************************", "postgres", "pactodb");

        for(int i = 0; i < aulasGymPass.length(); i++){
            JSONObject jsonObject = aulasGymPass.getJSONObject(i);
            String reference = jsonObject.getString("reference");
            List list = aulas.get(reference);
            if(list == null){
                list = new ArrayList<JSONObject>();
                aulas.put(reference, list);
            }
            list.add(jsonObject);
        }
        Map<String, String> params = new HashMap<>();
        params.put("Authorization", "Bearer ".concat(token));
        for(String r : aulas.keySet()){
            if(aulas.get(r).size() > 1){
                System.out.println(r);
                for(JSONObject jsonObject : aulas.get(r)){
                    Integer turmaZW = null;
                    ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from turma where idclassegympass = " + jsonObject.getInt("id"), con);
                    if(rs.next()){
                        turmaZW = rs.getInt("codigo");
                    }
                    System.out.println("\tsystem_id: " + jsonObject.getInt("system_id"));
                    System.out.println("\tname: " + jsonObject.getString("name"));
                    System.out.println("\tdescription: " + jsonObject.getString("description"));
                    System.out.println("\tslug: " + jsonObject.getString("slug"));
                    System.out.println("\tid: " + jsonObject.getInt("id"));
                    System.out.println("\tvisible: " + jsonObject.getBoolean("visible"));
                    System.out.println("\tproduct_id: " + jsonObject.getInt("product_id"));
                    System.out.println("\tturmaZW: " + (turmaZW == null ? "-" : turmaZW));

                    if(turmaZW == null && desativarAulasDuplicadas){
                        System.out.println("vou desativar a aula na gympass");
                        jsonObject.put("visible", false);
                        String result = ExecuteRequestHttpService.executeHttpRequest("https://api.partners.gympass.com/booking/v1/gyms/".concat(codigoGymPass).concat("/classes/")
                                        .concat(String.valueOf(jsonObject.getInt("id"))),
                                jsonObject.toString(),
                                params,
                                ExecuteRequestHttpService.METODO_PUT,
                                Charsets.UTF_8.name()
                                );
                        System.out.println(result);
                    }
                    System.out.println("\t-\n\n");


                }
            }

        }
    }

    public static String getToken() throws Exception{
        String result = ExecuteRequestHttpService.executeHttpRequest("https://identity.gympass.com/auth/realms/master/protocol/openid-connect/token",
                "grant_type=client_credentials&client_id=pacto&client_secret=f3677289-46ca-4c0f-a270-3aeb262db4af",
                new HashMap<>(), ExecuteRequestHttpService.METODO_GET,
                Charsets.UTF_8.name());
        return new JSONObject(result).getString("access_token");
    }

    public static JSONArray aulasGymPass(String codigoGymPass, String token) throws Exception{
        Map<String, String> params = new HashMap<>();
        params.put("Authorization", "Bearer ".concat(token));
        String url = "https://api.partners.gympass.com/booking/v1/gyms/".concat(codigoGymPass).concat("/classes");
        String result = ExecuteRequestHttpService.executeHttpRequest(url, null,
                params, ExecuteRequestHttpService.METODO_GET,
                Charsets.UTF_8.name());
        System.out.println(result);
            return new JSONObject(result).getJSONArray("classes");

    }

}

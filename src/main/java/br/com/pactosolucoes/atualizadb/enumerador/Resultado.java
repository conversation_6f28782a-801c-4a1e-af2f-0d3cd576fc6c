/**
 * 
 */
package br.com.pactosolucoes.atualizadb.enumerador;

/**
 * Enumerador que especifica os resultados possíveis da execução de uma atualização.
 * 
 * <AUTHOR>
 */
public enum Resultado {

	SUCESSO(1, "Sucesso"), ERRO(2, "Erro");

	private Integer codigo;
	private String descricao;

	private Resultado(Integer codigo, String descricao) {
		this.codigo = codigo;
		this.descricao = descricao;
	}

	/**
	 * Busca pelo código e retorna o item correspondente
	 * 
	 * @param codigo
	 * @return resultado
	 */
	public static Resultado getResultado(final Integer codigo) {
		Resultado resultado = null;
		for (Resultado res : Resultado.values()) {
			if (res.getCodigo().equals(codigo)) {
				resultado = res;
			}
		}
		return resultado;
	}

	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo descricao.
	 */
	public String getDescricao() {
		return descricao;
	}

	/**
	 * @param descricao
	 *            O novo valor de descricao.
	 */
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

}

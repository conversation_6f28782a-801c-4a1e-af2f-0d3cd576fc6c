/**
 * 
 */
package br.com.pactosolucoes.atualizadb.controle;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

import negocio.comuns.utilitarias.ControleConsulta;

import br.com.pactosolucoes.atualizadb.enumerador.Resultado;
import br.com.pactosolucoes.atualizadb.to.AtualizacaoTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;

/**
 * <AUTHOR>
 */
public class AtualizadorBDControle extends SuperControle {

	private List<AtualizacaoTO> atualizacoes;
	private AtualizacaoTO atualizacao;
	private String detalheAtualizacao;
	
	public AtualizadorBDControle() {
		// Inicializar controle de paginação de consulta
		this.setControleConsulta(new ControleConsulta());
		// Set mensagem informar parâmetros
		this.setMensagemID("parametros.informar");
	}

	/**
	 * @return O campo atualizacoes.
	 * @throws Exception 
	 */
	public List<AtualizacaoTO> getAtualizacoes() {
		return atualizacoes;
	}

	/**
	 * @param atualizacoes
	 *            O novo valor de atualizacoes.
	 */
	public void setAtualizacoes(List<AtualizacaoTO> atualizacoes) {
		this.atualizacoes = atualizacoes;
	}

	/**
	 * @return O campo atualizacao.
	 */
	public AtualizacaoTO getAtualizacao() {
		if (atualizacao == null) {
			atualizacao = new AtualizacaoTO();
		}
		return atualizacao;
	}

	/**
	 * @param atualizacao
	 *            O novo valor de atualizacao.
	 */
	public void setAtualizacao(AtualizacaoTO atualizacao) {
		this.atualizacao = atualizacao;
	}

	/**
	 * @return O campo detalheAtualizacao.
	 */
	public String getDetalheAtualizacao() {
		return detalheAtualizacao;
	}

	/**
	 * @param detalheAtualizacao
	 *            O novo valor de detalheAtualizacao.
	 */
	public void setDetalheAtualizacao(String detalheAtualizacao) {
		this.detalheAtualizacao = detalheAtualizacao;
	}

	/**
	 * Obtem uma lista com os possíveis resultados da execução de uma atualização.
	 * 
	 * @return Lista de Resultados possíveis.
	 */
	public List<SelectItem> getItensResultado() {
		List<SelectItem> itens = new ArrayList<SelectItem>();
		for (Resultado resultado : Resultado.values()) {
			itens.add(new SelectItem(resultado, resultado.getDescricao()));
		}
		return itens;
	}

	/**
	 * Consulta as atualizações de banco de dados já executadas no sistema.
	 */
	@SuppressWarnings("unchecked")
	public void consulta() {
		try {
			super.consultar();
			List<AtualizacaoTO> objs = getFacade().getAtualizadorBD().consultar(this.getAtualizacao());
			objs = ControleConsulta.obterSubListPaginaApresentar(objs, this.controleConsulta);
			this.definirVisibilidadeLinksNavegacao(this.controleConsulta.getPaginaAtual(), this.controleConsulta.getNrTotalPaginas());
			this.setAtualizacoes(objs);

			this.setMensagemID("operacoes.consulta.sucesso");
			this.setSucesso(true);
			this.setErro(false);
		} catch (Exception e) {
			this.getAtualizacoes().clear();

			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(e.getMessage());
			this.setSucesso(false);
			this.setErro(true);
		}
	}

	/**
	 * Vai para a página inicial
	 * 
	 * @throws Exception
	 */
	public void irPaginaInicial() {
		this.controleConsulta.setPaginaAtual(1);
		this.consulta();
	}

	/**
	 * vai para a página atual
	 * 
	 * @throws Exception
	 */
	public void irPaginaAnterior() {
		this.controleConsulta.setPaginaAtual(this.controleConsulta.getPaginaAtual() - 1);
		this.consulta();
	}

	/**
	 * Volta para a página posterior
	 * 
	 * @throws Exception
	 */
	public void irPaginaPosterior() {
		this.controleConsulta.setPaginaAtual(this.controleConsulta.getPaginaAtual() + 1);
		this.consulta();
	}

	/**
	 * vai para a pagina final
	 * 
	 * @throws Exception
	 */
	public void irPaginaFinal() {
		this.controleConsulta.setPaginaAtual(this.controleConsulta.getNrTotalPaginas());
		this.consulta();
	}

	public void exibirDescricaoAtualizacao() {
		final AtualizacaoTO at = (AtualizacaoTO) JSFUtilities.getRequestAttribute("atualizacao");
		this.setDetalheAtualizacao(at.getDescricao());
	}
	
	public void exibirScriptAtualizacao() {
		final AtualizacaoTO at = (AtualizacaoTO) JSFUtilities.getRequestAttribute("atualizacao");
		this.setDetalheAtualizacao(at.getScript());
	}

	public void exibirMensagemAtualizacao() {
		final AtualizacaoTO at = (AtualizacaoTO) JSFUtilities.getRequestAttribute("atualizacao");
		this.setDetalheAtualizacao(at.getMensagem());
	}

	public void exibirStackTraceAtualizacao() {
		final AtualizacaoTO at = (AtualizacaoTO) JSFUtilities.getRequestAttribute("atualizacao");
		this.setDetalheAtualizacao(at.getStackTrace());
	}

}

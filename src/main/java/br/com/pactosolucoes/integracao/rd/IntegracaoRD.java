/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.rd;

import org.json.JSONArray;
import org.json.JSONObject;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import servicos.util.ExecuteRequestHttpService;

/**
 *
 * <AUTHOR>
 */
public class IntegracaoRD {
    
    private Connection con;
    private static String tokenPublico = "34c5cb78794d0ecea2d161bf41fdfe5a";
    private static String tokenPrivado = "77b738ecd37926e1b58d6185a41cf00a";
    private static String urlRequest = "https://www.rdstation.com.br/api/1.3/conversions";
    
    public IntegracaoRD(Connection con){
        this.con = con;
    }

    public IntegracaoRD(){

    }
    
    public void integrarEventoTodasUnidades(EventoIntegracaoRDEnum evento, ResultSet rsDadosCon) throws Exception{
        DadosIntegracaoRD dadosRD = new DadosIntegracaoRD();
        Integer maiorAcessos = 0;
        JSONArray dadosAEnviar = new JSONArray();
        if(Calendario.hoje().getDate() != 1){
            return;
        }
        while (rsDadosCon.next()) {
            Connection con = DriverManager.getConnection("jdbc:postgresql://"
                    + rsDadosCon.getString("hostBD")
                    + ":" + rsDadosCon.getString("porta")
                    + "/" + rsDadosCon.getString("nomeBD"), rsDadosCon.getString("userBD"), rsDadosCon.getString("passwordBD"));
            ResultSet rs = dadosRD.rsMaisAcessou(con);
            while(rs.next()){
                System.out.println(rs.getString("empresa")+";"+rs.getString("matricula")+";"+rs.getString("nomecliente")+";"+rs.getInt("acessos"));
                if(rs.getInt("acessos") > maiorAcessos){
                    dadosAEnviar = new JSONArray();
                    maiorAcessos = rs.getInt("acessos");
                    dadosAEnviar.put(dadosRD.montarJSONAlunoMaisAcessouTodas(rs, con));
                }else if(rs.getInt("acessos") == maiorAcessos){
                    dadosAEnviar.put(dadosRD.montarJSONAlunoMaisAcessouTodas(rs, con));
                }
            }
        }
        for (int i = 0; i < dadosAEnviar.length(); i++) {
            JSONObject jsonConversao = dadosAEnviar.optJSONObject(i);
           enviarConversao(evento.getEntidade(), evento, jsonConversao);
        }
    }

    public void integrar(EventoIntegracaoRDEnum evento) throws Exception{
        DadosIntegracaoRD dadosRD = new DadosIntegracaoRD();
        JSONArray array = null;
        switch (evento) {
        case MAIS_ACESSOU_UNIDADE:
            array = dadosRD.maisAcessouUnidade(con);
            break;
        case MAIS_ANTIGO_UNIDADE:
            array = dadosRD.maisAntigoUnidade(con);
            break;
        case ANIVERSARIANTES:
            array = dadosRD.aniversariantes(con);
            break;
        case CANCELAMENTOSMANUAIS:
            array = dadosRD.contratos(con, "CA", false);
            break;
        case CANCELAMENTOSAUTOMATICOS:
            array = dadosRD.contratos(con, "CA", true);
            break;    
        case FALTOSO:
            array = dadosRD.faltosos(con);
            break;
        case INADIMPLENTES:
            array = dadosRD.inadimplentes(con);
            break;
        case REMATRICULADOS:
            array = dadosRD.contratos(con, "RE", null);
            break;
        case UPGRADE:
            array = dadosRD.upgrade(con);
            break;
        }
        if(array == null){
            return;
        }
        for (int i = 0; i < array.length(); i++) {
            JSONObject jsonConversao = array.optJSONObject(i);
            enviarConversao(evento.getEntidade(), evento, jsonConversao);
        }
    }
    public void integrar() throws Exception{
        for(EventoIntegracaoRDEnum evento : EventoIntegracaoRDEnum.values()){
            integrar(evento);
        }
    }
    
    public static String sqlCriarTabelaIntegracao(){
        StringBuilder create = new StringBuilder();
        create.append("CREATE TABLE integracaord( ");
        create.append("codigo serial PRIMARY KEY,\n");
        create.append("evento character varying(2),\n");
        create.append("entidade character varying(2),\n");
        create.append("chavepacto int,\n");
        create.append("envio timestamp without time zone);\n");
        return create.toString();
    }
    
    public void inserirIntegracao(Integer chavePacto, EntidadeIntegracaoRDEnum entidade, EventoIntegracaoRDEnum evento) throws SQLException{
        PreparedStatement stm = con.prepareStatement("INSERT INTO integracaord(evento, entidade, chavepacto, envio) VALUES (?,?,?,?)");
        stm.setString(1, evento.getSigla());
        stm.setString(2, entidade.getSigla());
        stm.setInt(3, chavePacto);
        stm.setTimestamp(4, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        stm.execute();
    }
    
    public void enviarConversao(EntidadeIntegracaoRDEnum entidade, EventoIntegracaoRDEnum evento, JSONObject json){
        try {
            json.put("token_rdstation", tokenPublico);
            System.out.println("Enviando "+json.optString("identificador")+" para RD do aluno " + json.optString("nome"));
            Integer chavePacto = json.optInt("chavePacto");
            json.remove("chavePacto");
            String sendJSONData = sendJSONData(json);
            if(sendJSONData.contains("OK") && !evento.equals(evento.MAIS_ACESSOU_REDE)){
                inserirIntegracao(chavePacto, entidade, evento);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }
    
    public String sendJSONData(JSONObject json) throws Exception {
        HttpClient httpClient = ExecuteRequestHttpService.createConnector();
        HttpPost post = new HttpPost(urlRequest);
        post.setHeader("Accept", "application/json");
        post.setHeader("headerValue", "HeaderInformation");
        StringEntity entity = new StringEntity(json.toString(), "UTF8");
        entity.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
        post.setEntity(entity);
        HttpResponse response = httpClient.execute(post);
        return response.getStatusLine().toString();
    }
    
    
    
}

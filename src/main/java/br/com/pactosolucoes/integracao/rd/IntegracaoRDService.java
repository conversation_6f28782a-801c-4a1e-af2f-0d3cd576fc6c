/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.rd;

import br.com.pactosolucoes.integracao.protheus.PropriedadesIntegracaoProtheus;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.Date;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class IntegracaoRDService {

    private static String hostOamd = "zw-rds-9.cmgq5kfs7ytf.sa-east-1.rds.amazonaws.com";
    private static String portaOamd = "5432";
    private static String userOamd = "zillyonweb";
    private static String senhaOamd = "pactodb2020";
    private static String chave = "8b682e93b8f5a3548c46a82e9214f9bc";
    private static EventoIntegracaoRDEnum evento = null;

    
    public static void main(String... args) {
        try {
            Connection conOamd = DriverManager.getConnection("jdbc:postgresql://" + hostOamd
                    + ":" + portaOamd + "/OAMD", userOamd, senhaOamd);
            String whereChaves = "";
            if(args != null && args.length > 0){
                whereChaves = " and chave in ('" + args[0] + "')";
            }else if(!chave.isEmpty()){
                whereChaves = " and chave in ('" + chave + "')";
            }
            if (args.length >= 2) {
                try {
                    evento = EventoIntegracaoRDEnum.getEventoPorSigla(args[1]);
                }catch (Exception e){
                    evento = null;
                }
            }
            if (args.length >= 3) {
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                Date hoje = Calendario.getInstance(sdf.parse(args[2])).getTime();
                Calendario.dia = hoje;
            }
            
            if(evento == null || !evento.equals(EventoIntegracaoRDEnum.MAIS_ACESSOU_REDE)){
                ResultSet rsDadosCon = SuperFacadeJDBC.criarConsulta("SELECT * FROM empresa WHERE \"nomeBD\"  like 'bdzillyon%' " + whereChaves, conOamd);
                while (rsDadosCon.next()) {
                    Connection con = DriverManager.getConnection("jdbc:postgresql://"
                            + rsDadosCon.getString("hostBD")
                            + ":" + rsDadosCon.getString("porta")
                            + "/" + rsDadosCon.getString("nomeBD"), rsDadosCon.getString("userBD"), rsDadosCon.getString("passwordBD"));
                    IntegracaoRD integracao = new IntegracaoRD(con);
                    System.out.println("Iniciando integração RD Solutions para o banco "+rsDadosCon.getString("nomeBD"));
                    if(evento == null){
                        integracao.integrar();
                    }else{
                        integracao.integrar(evento);
                    }
                }
            }

            if(evento != null && evento.equals(EventoIntegracaoRDEnum.MAIS_ACESSOU_REDE)){
                ResultSet rsDadosCon = SuperFacadeJDBC.criarConsulta("SELECT * FROM empresa where \"nomeBD\"  like 'bdzillyon%' ", conOamd);
                IntegracaoRD integracao = new IntegracaoRD();
                integracao.integrarEventoTodasUnidades(evento, rsDadosCon);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.rd;

/**
 *
 * <AUTHOR>
 */
public enum EventoIntegracaoRDEnum {
    INADIMPLENTES("IN", EntidadeIntegracaoRDEnum.INADIMPLENTES),
    FALTOSO("FA", EntidadeIntegracaoRDEnum.FALTOSO),
    ANIVERSARIANTES("AV", EntidadeIntegracaoRDEnum.ANIVERSARIANTES),
    CANCELAMENTOSMANUAIS("CA", EntidadeIntegracaoRDEnum.CANCELAMENTOSMANUAIS),
    CANCELAMENTOSAUTOMATICOS("CA", EntidadeIntegracaoRDEnum.CANCELAMENTOSAUTOMATICO),
    CONTRATO_ATIVO("MA", EntidadeIntegracaoRDEnum.CONTRATO),
    MAIS_ACESSOU_UNIDADE("AU", EntidadeIntegracaoRDEnum.MAIS_ACESSOU_UNIDADE),
    MAIS_ACESSOU_REDE("AR", EntidadeIntegracaoRDEnum.MAIS_ACESSOU_REDE),
    MAIS_ANTIGO_UNIDADE("AN", EntidadeIntegracaoRDEnum.MAIS_ANTIGO_UNIDADE),
    CADASTRO_CLIENTE("CL", EntidadeIntegracaoRDEnum.PESSOA),
    UPGRADE("UP", EntidadeIntegracaoRDEnum.UPGRADE),
    REMATRICULADOS("RE", EntidadeIntegracaoRDEnum.REMATRICULADOS)
    ;
    
    private String sigla;
    private EntidadeIntegracaoRDEnum entidade;

    private EventoIntegracaoRDEnum(String sigla, EntidadeIntegracaoRDEnum entidade) {
        this.entidade = entidade;
        this.sigla = sigla;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public EntidadeIntegracaoRDEnum getEntidade() {
        return entidade;
    }

    public void setEntidade(EntidadeIntegracaoRDEnum entidade) {
        this.entidade = entidade;
    }
    
    public static EventoIntegracaoRDEnum getEventoPorSigla(String sigla) {
            EventoIntegracaoRDEnum evento = null;
            for (EventoIntegracaoRDEnum eventoRD : EventoIntegracaoRDEnum.values()) {
                if (eventoRD.getSigla().equals(sigla)) {
                    evento = eventoRD;
                    break;
                }
            }
            return evento;
        }
}

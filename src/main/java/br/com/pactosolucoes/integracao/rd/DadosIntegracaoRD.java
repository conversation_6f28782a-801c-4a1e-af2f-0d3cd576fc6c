/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.rd;

import org.json.JSONArray;
import org.json.JSONObject;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Calendar;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.DiasDaSemana;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class DadosIntegracaoRD {

    

    public JSONArray clientes(Connection con) throws Exception{
        JSONArray array = new JSONArray();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT  cid.nome as cidade, es.descricao as estado, p.datacadastro, con.codigo as codigocontrato, p.codigo as codigopesso<PERSON>, p.webpage, p.sexo, p.nome as pessoa,  \n");
        sql.append(" sw.datamatricula, e.nome as unidade, p.datanasc, pf.descricao AS profissao, gr.descricao AS grauinstrucao,   \n");
        sql.append(" pla.descricao as  plano, sw.codigocliente,con.vigenciade,con.vigenciaateajustada \n");
        sql.append(" FROM situacaoclientesinteticodw sw  \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = sw.codigopessoa  \n");
        sql.append(" INNER JOIN empresa e ON e.codigo = sw.empresacliente \n");
	sql.append(" LEFT JOIN contrato con ON con.codigo = sw.codigocontrato \n");
        sql.append(" LEFT JOIN plano pla ON pla.codigo = con.plano  \n");
        sql.append(" LEFT JOIN integracaord ird ON ird.chavepacto = p.codigo AND ird.entidade = 'PE' \n");
        sql.append(" LEFT JOIN profissao pf ON pf.codigo = p.profissao  \n");
        sql.append(" LEFT JOIN grauinstrucao gr ON gr.codigo = p.grauinstrucao  \n");
        sql.append(" LEFT JOIN cidade cid ON cid.codigo = p.cidade \n");
        sql.append(" LEFT JOIN estado es ON cid.estado = es.codigo \n");
        sql.append(" WHERE ird.codigo IS NULL  \n");
        sql.append(" AND codigopessoa = 285 \n");
        
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        while(rs.next()){
            try {
                array.put(montarJSONCadastroAluno(rs, con));
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        return array;
    }
    public JSONArray contratos(Connection con, String tipo, Boolean opAutomatica) throws Exception{
        JSONArray array = new JSONArray();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  hc.tipohistorico, hc.datainiciosituacao,\n");
        sql.append("	p.datacadastro, con.codigo as codigocontrato, p.codigo as codigopessoa, p.webpage, p.sexo, p.nome as pessoa, \n");
        sql.append("        sw.datamatricula, e.nome as unidade, p.datanasc, pf.descricao AS profissao, gr.descricao AS grauinstrucao,  \n");
        sql.append("        pla.descricao as  plano, sw.codigocliente,con.vigenciade,con.vigenciaateajustada\n");
        sql.append("        FROM historicocontrato hc \n");
        sql.append("        INNER JOIN contrato con ON con.codigo = hc.contrato\n");
        sql.append("        INNER JOIN plano pla ON pla.codigo = con.plano \n");
        sql.append("        INNER JOIN pessoa p ON p.codigo = con.pessoa \n");
        sql.append("        INNER JOIN empresa e ON e.codigo = con.empresa \n");
        sql.append("        INNER JOIN situacaoclientesinteticodw sw ON p.codigo = sw.codigopessoa   \n");
        if(opAutomatica != null){
            sql.append("        INNER JOIN usuario usu ON usu.codigo = hc.responsavelregistro \n");
            if(opAutomatica){
                 sql.append(" and usu.username = 'RECOR'  \n");
            } else {
                sql.append(" and usu.username <> 'RECOR'  \n");
            }
        }
        sql.append("        LEFT JOIN integracaord ird ON ird.chavepacto = con.codigo AND ird.evento = hc.tipohistorico\n");
        sql.append("        LEFT JOIN profissao pf ON pf.codigo = p.profissao \n");
        sql.append("        LEFT JOIN grauinstrucao gr ON gr.codigo = p.grauinstrucao \n");
        sql.append("        WHERE ird.codigo IS NULL AND hc.tipohistorico IN ('");
        sql.append(tipo).append("')\n");
        sql.append("        AND dataregistro > '2017-05-01' and dataregistro < '");
        sql.append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
        sql.append("'");
        if(tipo.equals("RE")){
            sql.append(" and not (pla.descricao like '%BLUE%' AND exists (select ant.codigo from contrato ant inner join plano pant on pant.codigo = ant.plano where ant.codigo = con.contratobaseadorematricula and pant.descricao like '%SELF%')) "); // caso de upgrade
            sql.append(" and (con.vigenciade >  (select vigenciaateajustada from contrato ant where ant.codigo = con.contratobaseadorematricula)) "); // caso de upgrade  
        }
        if(tipo.equals("CA")){
            sql.append(" and (con.contratoresponsavelrematriculamatricula is null or con.contratoresponsavelrematriculamatricula = 0) "); // caso de upgrade  
        }
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        while(rs.next()){
            try {
                if(rs.getString("tipohistorico").equals("CA")){
                    if(opAutomatica != null && opAutomatica){
                        array.put(montarJSONCancelamentoAutomatico(rs, con));
                    } else {
                        array.put(montarJSONCancelamento(rs, con));
                    }
                }else{
                    array.put(montarJSONContrato(rs, con, tipo));
                }
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        return array;
    }
    
    public JSONObject montarJSONCancelamento(ResultSet rs, Connection con) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("Assunto", "Aluno cancelado");
        jsonConversao.put("identificador", "Cancelamento");
        jsonConversao.put("email", getEmail(rs.getInt("codigopessoa"), con));
        jsonConversao.put("chavePacto", rs.getInt("codigocontrato"));
        jsonConversao.put("Plano", rs.getString("plano"));
        jsonConversao.put("nome", rs.getString("pessoa"));
        jsonConversao.put("tags", "ZW");
        jsonConversao.put("Unidade", rs.getString("unidade"));
        jsonConversao.put("Cliente", "SIM");
        jsonConversao.put("Data do Cancelamento", Uteis.getData(rs.getDate("datainiciosituacao")));
        ResultSet rsJust = SuperFacadeJDBC.criarConsulta("select descricao from contratooperacao c "+
                                          " inner join justificativaoperacao j on c.tipojustificativa = j.codigo	"+
                                          " where c.tipooperacao = 'CA' and c.contrato = "+rs.getInt("codigocontrato"), con);
        if(rsJust.next()){
            jsonConversao.put("Motivo", rsJust.getString("descricao"));
        }
        return jsonConversao;
    }
    
    public JSONObject montarJSONCancelamentoAutomatico(ResultSet rs, Connection con) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("Assunto", "Aluno cancelado Automaticamente");
        jsonConversao.put("identificador", "Cancelamento Automatico");
        jsonConversao.put("email", getEmail(rs.getInt("codigopessoa"), con));
        jsonConversao.put("chavePacto", rs.getInt("codigocontrato"));
        jsonConversao.put("Plano", rs.getString("plano"));
        jsonConversao.put("nome", rs.getString("pessoa"));
        jsonConversao.put("Unidade", rs.getString("unidade"));
        jsonConversao.put("tags", "ZW");
        jsonConversao.put("Cliente", "SIM");
        jsonConversao.put("Data do Cancelamento", Uteis.getData(rs.getDate("datainiciosituacao")));
        ResultSet rsJust = SuperFacadeJDBC.criarConsulta("select descricao from contratooperacao c "+
                                          " inner join justificativaoperacao j on c.tipojustificativa = j.codigo	"+
                                          " where c.tipooperacao = 'CA' and c.contrato = "+rs.getInt("codigocontrato"), con);
        if(rsJust.next()){
            jsonConversao.put("Motivo", rsJust.getString("descricao"));
        }
        return jsonConversao;
    }
    
    public JSONObject montarJSONContrato(ResultSet rs, Connection con, String tipo) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("email", getEmail(rs.getInt("codigopessoa"), con));
        jsonConversao.put("chavePacto", rs.getInt("codigocontrato"));
        jsonConversao.put("nome", rs.getString("pessoa"));
        jsonConversao.put("Plano", rs.getString("plano"));
        jsonConversao.put("Data início", Uteis.getData(rs.getDate("vigenciade")));
        jsonConversao.put("Data final", Uteis.getData(rs.getDate("vigenciaateajustada")));
        jsonConversao.put("Unidade", rs.getString("unidade"));
        if(tipo.equals("RE")){
            jsonConversao.put("Assunto", "Aluno rematriculado");
            jsonConversao.put("identificador", "Rematriculado");
        }else{
            jsonConversao.put("Assunto", "Novo Contrato");
            jsonConversao.put("identificador", "Matriculado");
        }

        return jsonConversao;
    }
    public JSONObject montarJSONCadastroAluno(ResultSet rs, Connection con) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("email", getEmail(rs.getInt("codigopessoa"), con));
        jsonConversao.put("chavePacto", rs.getInt("codigopessoa"));
        jsonConversao.put("identificador", "Cadastro do aluno");
        jsonConversao.put("nome", rs.getString("pessoa"));
        jsonConversao.put("Plano", rs.getString("plano"));
        jsonConversao.put("Profissão", rs.getString("profissao"));
        jsonConversao.put("website", rs.getString("webpage"));
        jsonConversao.put("Data de cadastro", Uteis.getData(rs.getDate("datacadastro")));
        jsonConversao.put("Data de Nascimento", Uteis.getData(rs.getDate("datanasc")));
        jsonConversao.put("Aniversário", Uteis.getDataAplicandoFormatacao(rs.getDate("datanasc"), "dd/MM"));
        jsonConversao.put("Data de matrícula", Uteis.getData(rs.getDate("datamatricula")));
        jsonConversao.put("Formação", rs.getString("grauinstrucao"));
        jsonConversao.put("Sexo", rs.getString("sexo") == null ? "" : rs.getString("sexo").equals("M") ? "Masculino" : "Feminino");
        jsonConversao.put("Unidade", rs.getString("unidade"));
        
        ResultSet rsConsultor = SuperFacadeJDBC.criarConsulta("select p.nome from pessoa p INNER JOIN colaborador c ON c.pessoa = p.codigo "
                + " INNER JOIN vinculo v ON v.colaborador = c.codigo "
                + " AND tipovinculo = 'CO' AND cliente = "+rs.getInt("codigocliente"), con);
        if(rsConsultor.next()){
            jsonConversao.put("Origem do Cadastro", rsConsultor.getString("nome"));
        }
        
        ResultSet rsEndereco = SuperFacadeJDBC.criarConsulta("select numero, complemento, endereco, bairro, cep from endereco where pessoa = "
                                          +rs.getInt("codigopessoa")+" ORDER BY tipoendereco DESC LIMIT 1 ", con);
        if (rsEndereco.next()) {
            jsonConversao.put("Endereço", rsEndereco.getString("endereco"));
            jsonConversao.put("Número", rsEndereco.getString("numero"));
            jsonConversao.put("Complemento", rsEndereco.getString("complemento"));
            jsonConversao.put("Bairro", rsEndereco.getString("bairro"));
            jsonConversao.put("CEP", rsEndereco.getString("cep"));
            jsonConversao.put("Cidade", rs.getString("cidade"));
            jsonConversao.put("Estado", rs.getString("estado"));
        } 
        jsonConversao.put("telefone", getTelefone(rs.getInt("codigopessoa"), "RE", con));
        jsonConversao.put("celular", getTelefone(rs.getInt("codigopessoa"), "CE", con));
        
        jsonConversao.put("Assunto", "Origem");
        jsonConversao.put("tags", "ZW");
        jsonConversao.put("Cliente", "SIM");
        
        
        return jsonConversao;

    }

    public String getTelefone(Integer codigoPessoa, String tipoTelefone, Connection con) throws Exception{
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select numero from telefone where pessoa = "+codigoPessoa
                                           +" AND tipotelefone = '"+tipoTelefone+"' ", con);
        return rs.next() ? rs.getString("numero") : "";
    }
    
    public String getEmail(Integer codigoPessoa,  Connection con) throws Exception{
        ResultSet rsEmail = SuperFacadeJDBC.criarConsulta("select email from email where pessoa = "
                                          +codigoPessoa+" ORDER BY emailcorrespondencia DESC LIMIT 1 ", con);
        if (rsEmail.next()) {
            return rsEmail.getString("email");
        } else {
            throw new Exception("Aluno "+codigoPessoa+" não possui email cadastrado");
        }
    }

    public JSONObject montarJSONAlunoMaisAcessouUnidade(ResultSet rs, Connection con) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("Assunto", "Aluno que mais frequentou a unidade no mês");
        jsonConversao.put("identificador", "Aluno mais assíduo do mês");
        jsonConversao.put("email", getEmail(rs.getInt("codigopessoa"), con));
        jsonConversao.put("chavePacto", rs.getInt("codigocliente"));
        jsonConversao.put("Acessos", rs.getInt("acessos"));
        jsonConversao.put("nome", rs.getString("nomecliente"));
        jsonConversao.put("Mês", Uteis.getDataAplicandoFormatacao(Uteis.somarCampoData(Calendario.hoje(), Calendar.MONTH, -1), "MMMM 'de' yyyy"));
        jsonConversao.put("Unidade", rs.getString("empresa"));
        jsonConversao.put("tags", "ZW");
        jsonConversao.put("Cliente", "SIM");
        return jsonConversao;
    }

    public ResultSet rsMaisAcessou(Connection con) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select emp.nome as empresa, nomecliente, matricula, sw.codigopessoa, sw.codigocliente, clientesMax.maxAcessosCliente as acessos  from  \n");
        sql.append("  (select maxAcessoCliente.cliente, maxAcessoCliente.maxAcessosCliente from \n");
        sql.append("(select cliente, count(cliente) as maxAcessosCliente from \n");
        sql.append("      (select  min(acp.codigo), acp.cliente, cast(acp.dthrentrada as date) \n");
        sql.append("                from AcessoCliente acp \n");
        sql.append("               where   acp.dthrentrada between '");
        sql.append(Uteis.getDataAplicandoFormatacao(Uteis.obterPrimeiroDiaMesPrimeiraHora(Uteis.somarCampoData(Calendario.hoje(), Calendar.MONTH, -1)), "yyyy-MM-dd HH:mm:ss"));
        sql.append("' AND '");
        sql.append(Uteis.getDataAplicandoFormatacao(Uteis.obterUltimoDiaMesUltimaHora(Uteis.somarCampoData(Calendario.hoje(), Calendar.MONTH, -1)), "yyyy-MM-dd HH:mm:ss")).append("' ");
        sql.append("        group by acp.cliente, cast(acp.dthrentrada as date) order by 3) as acessosClienteFoo\n");
        sql.append("       group by cliente order by 2) as maxAcessoCliente group by 1,2 having 	maxAcessosCliente = (\n");
        sql.append(" select maxAcessos from ( \n");
        sql.append(" select maxAcessoConsulta.cliente, count(maxAcessoConsulta.cliente) as maxAcessos from \n");
        sql.append("  (select  min(acfoo.codigo), acfoo.cliente, cast(acfoo.dthrentrada as date) \n");
        sql.append("           from AcessoCliente acfoo \n");
        sql.append("            where   acfoo.dthrentrada between '");
        sql.append(Uteis.getDataAplicandoFormatacao(Uteis.obterPrimeiroDiaMesPrimeiraHora(Uteis.somarCampoData(Calendario.hoje(), Calendar.MONTH, -1)), "yyyy-MM-dd HH:mm:ss"));
        sql.append("' AND '");
        sql.append(Uteis.getDataAplicandoFormatacao(Uteis.obterUltimoDiaMesUltimaHora(Uteis.somarCampoData(Calendario.hoje(), Calendar.MONTH, -1)), "yyyy-MM-dd HH:mm:ss")).append("' ");
        sql.append("             group by acfoo.cliente, cast(acfoo.dthrentrada as date) order by 3) as maxAcessoConsulta\n");
        sql.append("              group by cliente order by 2 desc limit 1) as maxAcesso)) as clientesMax \n");
        sql.append("            inner join situacaoclientesinteticodw  sw on  clientesMax.cliente = sw.codigocliente\n");
        sql.append("             INNER JOIN empresa emp ON sw.empresacliente = emp.codigo  \n");
        
        PreparedStatement stm = con.prepareStatement(sql.toString());
        
        return stm.executeQuery();
    }

    public JSONArray maisAcessouUnidade(Connection con) throws Exception{
        JSONArray array = new JSONArray();
        if(Calendario.hoje().getDate() != 1){
            return array;
        }
        ResultSet rs = rsMaisAcessou(con);
        while(rs.next()){
            try {
                array.put(montarJSONAlunoMaisAcessouUnidade(rs, con));
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        return array;
    }
    public JSONObject montarJSONAlunoMaisAntigoUnidade(ResultSet rs, Connection con) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("Assunto", "Aluno que está há mais tempo matriculado na unidade");
        jsonConversao.put("identificador", "Aluno mais antigo da unidade");
        jsonConversao.put("email", getEmail(rs.getInt("codigopessoa"), con));
        jsonConversao.put("chavePacto", rs.getInt("codigocliente"));
        jsonConversao.put("nome", rs.getString("nomecliente"));
        jsonConversao.put("Unidade", rs.getString("empresa"));
        jsonConversao.put("Matriculado desde", Uteis.getDataAplicandoFormatacao(rs.getDate("datamatricula"), "dd/MM/yyyy"));
        jsonConversao.put("tags", "ZW");
        jsonConversao.put("Cliente", "SIM");
        return jsonConversao;
    }

    public JSONArray maisAntigoUnidade(Connection con) throws Exception{
        JSONArray array = new JSONArray();
        if(Calendario.hoje().getDate() != 1 || (Calendario.hoje().getMonth() != 0 && Calendario.hoje().getMonth() != 3 && Calendario.hoje().getMonth() != 6 && Calendario.hoje().getMonth() != 9)){ //executa apenas no primeiro dia, trimestralmente
            return array;
        }
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT Case  when dataultimarematricula  is not null then dataultimarematricula ELSE datamatricula end as datamatricula, emp.nome as empresa, nomecliente, \n");
        sql.append(" matricula, sw.codigopessoa, sw.codigocliente FROM situacaoclientesinteticodw  sw \n");
        sql.append(" INNER JOIN empresa emp ON sw.empresacliente = emp.codigo \n");
        sql.append(" WHERE sw.situacao = 'AT' \n");
        sql.append(" ORDER BY 1,6 \n");
        sql.append(" LIMIT 1 ");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        while(rs.next()){
            try {
                array.put(montarJSONAlunoMaisAntigoUnidade(rs, con));
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        return array;
    }

    public JSONObject montarJSONAniversariantes(ResultSet rs, Connection con) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("Assunto", "Aniversário");
        jsonConversao.put("identificador", "Aniversariante");
        jsonConversao.put("email", getEmail(rs.getInt("codigopessoa"), con));
        jsonConversao.put("chavePacto", rs.getInt("codigocliente"));
        jsonConversao.put("nome", rs.getString("nomecliente"));
        jsonConversao.put("Unidade", rs.getString("empresa"));
        jsonConversao.put("Aniversário", Uteis.getDataAplicandoFormatacao(rs.getDate("datanasc"), "dd 'de' MMMM"));
        jsonConversao.put("tags", "ZW");
        jsonConversao.put("Cliente", "SIM");
        return jsonConversao;
    }

    public JSONArray aniversariantes(Connection con) throws Exception{
        JSONArray array = new JSONArray();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT p.datanasc, emp.nome as empresa, nomecliente, matricula, sw.codigopessoa, sw.codigocliente \n");
        sql.append(" FROM situacaoclientesinteticodw  sw \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = sw.codigopessoa \n");
        sql.append(" INNER JOIN empresa emp ON sw.empresacliente = emp.codigo \n");
        sql.append(" WHERE sw.situacao = 'AT' and to_char(p.datanasc, 'ddMM') = '");
        sql.append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "ddMM"));
        sql.append("' ");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        while(rs.next()){
            try {
                array.put(montarJSONAniversariantes(rs, con));
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        return array;
    }

    public JSONObject montarJSONFaltosos(ResultSet rs, Connection con) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("Assunto", "Aluno que faltou mais de 7 dias");
        jsonConversao.put("identificador", "Faltoso");
        jsonConversao.put("email", getEmail(rs.getInt("codigopessoa"), con));
        jsonConversao.put("chavePacto", rs.getInt("codigocliente"));
        jsonConversao.put("nome", rs.getString("nomecliente"));
        jsonConversao.put("Unidade", rs.getString("empresa"));
        jsonConversao.put("Último acesso", Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dataultimoacesso"), "dd/MM/yyyy HH:mm"));
        jsonConversao.put("tags", "ZW");
        jsonConversao.put("Cliente", "SIM");
        return jsonConversao;
    }

    public JSONArray faltosos(Connection con) throws Exception{
        JSONArray array = new JSONArray();
        if(!Uteis.getDiaDaSemana(Calendario.hoje(), DiasDaSemana.DOMINGO)){
            return array;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT sw.dataultimoacesso, emp.nome as empresa, nomecliente, matricula, sw.codigopessoa, \n");
        sql.append("sw.codigocliente FROM situacaoclientesinteticodw  sw \n");
        sql.append("INNER JOIN empresa emp ON sw.empresacliente = emp.codigo \n");
        sql.append("WHERE sw.situacao = 'AT' \n");
        sql.append("AND (CAST('").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
        sql.append("' AS date) - sw.dataultimoacesso::date) between 8 and 14 ");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        while(rs.next()){
            try {
                array.put(montarJSONFaltosos(rs, con));
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        return array;
    }
    public JSONObject montarJSONInadimplentes(ResultSet rs, Connection con) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("Assunto", "Alunos inadimplentes por mais de 2 meses");
        jsonConversao.put("identificador", "Inadimplente");
        jsonConversao.put("email", getEmail(rs.getInt("codigopessoa"), con));
        jsonConversao.put("chavePacto", rs.getInt("codigocliente"));
        jsonConversao.put("nome", rs.getString("nomecliente"));
        jsonConversao.put("Unidade", rs.getString("empresa"));
        jsonConversao.put("Vencimento da parcela mais antiga em aberto", Uteis.getDataAplicandoFormatacao(rs.getDate("datavencimento"), "dd/MM/yyyy"));
        jsonConversao.put("tags", "ZW");
        jsonConversao.put("Cliente", "SIM");
        return jsonConversao;
    }

    public JSONArray inadimplentes(Connection con) throws Exception{
        JSONArray array = new JSONArray();
        if(Calendario.hoje().getDate() != 1){
            return array;
        }
        StringBuilder sql = new StringBuilder();


        sql.append(" SELECT mp.datavencimento, emp.nome as empresa, nomecliente, matricula, sw.codigopessoa, ");
        sql.append(" sw.codigocliente FROM situacaoclientesinteticodw  sw ");
        sql.append(" INNER JOIN empresa emp ON sw.empresacliente = emp.codigo ");
        sql.append(" LEFT JOIN integracaord ird ON ird.chavepacto = sw.codigocliente AND ird.evento = 'IN' ");
        sql.append(" INNER JOIN (SELECT pessoa, MIN(datavencimento) AS datavencimento FROM movparcela WHERE situacao = 'EA' ");
        sql.append(" AND datavencimento < '").append(Uteis.getDataAplicandoFormatacao(Uteis.somarCampoData(Calendario.hoje(), Calendar.MONTH, -2), "yyyy-MM-dd"));
        sql.append("' and descricao not like '%MULTA%' group by 1) AS mp ");
        sql.append(" ON mp.pessoa = sw.codigopessoa ");
        sql.append(" WHERE sw.situacao = 'AT' ");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        while(rs.next()){
            try {
                array.put(montarJSONInadimplentes(rs, con));
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        return array;
    }

    public JSONObject montarJSONUpgrade(ResultSet rs, Connection con) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("Assunto", "Upgrade de plano SELF para BLUE");
        jsonConversao.put("identificador", "Upgrade de plano");
        jsonConversao.put("email", getEmail(rs.getInt("codigopessoa"), con));
        jsonConversao.put("chavePacto", rs.getInt("codigocontrato"));
        jsonConversao.put("nome", rs.getString("nomecliente"));
        jsonConversao.put("Unidade", rs.getString("empresa"));
        jsonConversao.put("tags", "ZW");
        jsonConversao.put("Cliente", "SIM");
        return jsonConversao;
    }

    public JSONArray upgrade(Connection con) throws Exception{
        JSONArray array = new JSONArray();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT nomeplano, emp.nome as empresa,sw.codigocontrato, nomecliente, matricula, sw.codigopessoa, sw.codigocliente FROM situacaoclientesinteticodw  sw \n");
        sql.append(" INNER JOIN empresa emp ON sw.empresacliente = emp.codigo \n");
        sql.append(" LEFT JOIN integracaord ird ON ird.chavepacto = sw.codigocontrato AND ird.evento = 'UP' \n");
        sql.append(" WHERE sw.situacao = 'AT' AND nomeplano like '%BLUE%' AND ird.codigo is null \n");
        sql.append(" AND  exists (select ant.codigo from contrato ant inner join plano pant on pant.codigo = ant.plano where (ant.contratoresponsavelrematriculamatricula  = sw.codigocontrato or ant.contratoresponsavelrenovacaomatricula   = sw.codigocontrato)  and pant.descricao like '%SELF%') \n");
        sql.append(" AND sw.datavigenciade > '2017-05-01' ");

        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        while(rs.next()){
            try {
                array.put(montarJSONUpgrade(rs, con));
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        return array;
    }

    public JSONObject montarJSONAlunoMaisAcessouTodas(ResultSet rs, Connection con) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("Assunto", "Aluno que mais frequentou a rede");
        jsonConversao.put("identificador", "Aluno mais assíduo da rede");
        jsonConversao.put("email", getEmail(rs.getInt("codigopessoa"), con));
        jsonConversao.put("Acessos", rs.getInt("acessos"));
        jsonConversao.put("chavePacto", rs.getInt("codigocliente"));
        jsonConversao.put("nome", rs.getString("nomecliente"));
        jsonConversao.put("Mês", Uteis.getDataAplicandoFormatacao(Uteis.somarCampoData(Calendario.hoje(), Calendar.MONTH, -1), "MMMM 'de' yyyy"));
        jsonConversao.put("Unidade", rs.getString("empresa"));
//        jsonConversao.put("Acessos no período", rs.getString("acessos"));
        jsonConversao.put("tags", "ZW");
        jsonConversao.put("Cliente", "SIM");
        return jsonConversao;
    }

}

package br.com.pactosolucoes.integracao.conciliadora;


import com.thoughtworks.xstream.annotations.XStreamAlias;
import negocio.comuns.arquitetura.SuperTO;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ListaConciliadoraTO extends SuperTO {

    private CabecalhoConciliadoraTO cabecalho;
    private Collection<RegistroConciliadoraTO> registro;

    public CabecalhoConciliadoraTO getCabecalho() {
        return cabecalho;
    }

    public void setCabecalho(CabecalhoConciliadoraTO cabecalho) {
        this.cabecalho = cabecalho;
    }

    public Collection<RegistroConciliadoraTO> getRegistro() {
        if (registro == null) {
            registro = new ArrayList<RegistroConciliadoraTO>();
        }
        return registro;
    }

    public void setRegistro(Collection<RegistroConciliadoraTO> registro) {
        this.registro = registro;
    }
}

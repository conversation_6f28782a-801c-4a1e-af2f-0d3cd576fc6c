package br.com.pactosolucoes.integracao.conciliadora;

public enum OperadoraEnumConciliadora {

    CIELO(1, "<PERSON>iel<PERSON>"),
    REDE(2, "Rede"),
    AMEX(3, "Amex"),
    GETNET(4, "Getnet"),
    ELAVON(5, "Elavon"),
    BIN(6, "<PERSON>"),
    STONE(7, "<PERSON>"),
    BANRISUL(8, "<PERSON><PERSON><PERSON>"),
    ECX_CARD(9, "Ecx Card"),
    GOODCARD(10, "GoodCard"),
    TICKET(11, "Ticket"),
    POLICARD(12, "Policard"),
    SODEXO(13, "Sodexo"),
    CREDISHOP(14, "Credishop"),
    BANRICARD(15, "Banricard"),
    PAGAR_ME(16, "Pagar.me"),
    SH<PERSON>L(17, "Shell"),
    FORTBRASIL(18, "FortBrasil"),
    BANESCARD(19, "<PERSON><PERSON><PERSON>"),
    NUTRICASH(20, "Nutricash"),
    SENFF(21, "<PERSON><PERSON>"),
    GREEN_CARD(22, "Green Card"),
    SOLLUS(23, "<PERSON><PERSON>"),
    DIRECTFACIL(24, "DirectF<PERSON><PERSON>"),
    COOPERCARD(25, "<PERSON>card"),
    CABAL(26, "Cabal"),
    DACASA(27, "Dacasa"),
    VR(28, "VR"),
    GLOBAL_PAYMENTS(29, "Global Payments"),
    REDE_UZE(30, "Rede Uze"),
    VALECARD(31, "Valecard"),
    TRICARD(32, "Tricard"),
    PLANVALE(33, "Planvale"),
    SOROCRED(34, "Sorocred"),
    PAGSEGURO(35, "PagSeguro"),
    SAFRA_PAY(36, "Safra Pay"),
    VEROCHEQUE(37, "Verocheque"),
    CONVCARD(38, "Convcard"),
    VALEMAIS(39, "Valemais"),
    PAGO(40, "Pago"),
    VALESHOP(41, "Valeshop"),
    BRASIL_CONVENIO(42, "Brasil Convênio"),
    MERCADO_PAGO(43, "Mercado Pago"),
    FIRST_DATA(44, "First Dat"),
    MOIP(45, "Moip"),
    BRASIL_CARD(46, "Brasil Card"),
    FIT_CARD(47, "Fit Card"),
    NUTRICARD(48, "NutriCard"),
    BONUSCRED(49, "BonusCred"),
    CARTAO_PREDATADO(50, "Cartao PreDatado"),
    ALIMENTARE(51, "Alimentare"),
    BIGCARD(52, "Bigcard"),
    INFOCARD_RECEBO(53, "Infocard/Recebo"),
    SICREDI(54, "Sicredi"),
    CREDSYSTEM(55, "Credsystem "),
    MOOZ(56, "Mooz"),
    LOSANGO(57, "Losango");

    private Integer codigo;
    private String descricao;

    OperadoraEnumConciliadora(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

package br.com.pactosolucoes.integracao.conciliadora;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import negocio.comuns.arquitetura.SuperTO;

/**
 * <AUTHOR>
 */
@XStreamAlias("registro")
public class RegistroConciliadoraTO extends SuperTO {

    @XStreamAlias("Produto")
    private Integer produto;
    @XStreamAlias("DescricaoTipoProduto")
    private String descricaoTipoProduto;
    @XStreamAlias("CodigoAutorizacao")
    private String codigoAutorizacao;
    @XStreamAlias("IdentificadorPagamento")
    private String identificadorPagamento;
    @XStreamAlias("DataVenda")
    private String dataVenda;
    @XStreamAlias("DataVencimento")
    private String dataVencimento;
    @XStreamAlias("ValorVendaParcela")
    private String valorVendaParcela;
    @XStreamAlias("ValorLiquidoParcela")
    private String valorLiquidoParcela;
    @XStreamAlias("TotalVenda")
    private String totalVenda;
    @XStreamAlias("Taxa")
    private String taxa;
    @XStreamAlias("Parcela")
    private String parcela;
    @XStreamAlias("TotalDeParcelas")
    private String totalDeParcelas;
    @XStreamAlias("ValorBrutoMoeda")
    private Double valorBrutoMoeda;
    @XStreamAlias("ValorLiquidoMoeda")
    private Double valorLiquidoMoeda;
    @XStreamAlias("CotacaoMoeda")
    private Double cotacaoMoeda;
    @XStreamAlias("Moeda")
    private String moeda;
    @XStreamAlias("NSU")
    private String nsu;
    @XStreamAlias("TID")
    private String tid;
    @XStreamAlias("Terminal")
    private String terminal;
    @XStreamAlias("MeioCaptura")
    private String meioCaptura;
    @XStreamAlias("Operadora")
    private String operadora;
    @XStreamAlias("Modalidade")
    private String modalidade;

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public String getDescricaoTipoProduto() {
        return descricaoTipoProduto;
    }

    public void setDescricaoTipoProduto(String descricaoTipoProduto) {
        this.descricaoTipoProduto = descricaoTipoProduto;
    }

    public String getCodigoAutorizacao() {
        return codigoAutorizacao;
    }

    public void setCodigoAutorizacao(String codigoAutorizacao) {
        this.codigoAutorizacao = codigoAutorizacao;
    }

    public String getIdentificadorPagamento() {
        return identificadorPagamento;
    }

    public void setIdentificadorPagamento(String identificadorPagamento) {
        this.identificadorPagamento = identificadorPagamento;
    }

    public String getDataVenda() {
        return dataVenda;
    }

    public void setDataVenda(String dataVenda) {
        this.dataVenda = dataVenda;
    }

    public String getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public String getValorVendaParcela() {
        return valorVendaParcela;
    }

    public void setValorVendaParcela(String valorVendaParcela) {
        this.valorVendaParcela = valorVendaParcela;
    }

    public String getValorLiquidoParcela() {
        return valorLiquidoParcela;
    }

    public void setValorLiquidoParcela(String valorLiquidoParcela) {
        this.valorLiquidoParcela = valorLiquidoParcela;
    }

    public String getTotalVenda() {
        return totalVenda;
    }

    public void setTotalVenda(String totalVenda) {
        this.totalVenda = totalVenda;
    }

    public String getTaxa() {
        return taxa;
    }

    public void setTaxa(String taxa) {
        this.taxa = taxa;
    }

    public String getParcela() {
        return parcela;
    }

    public void setParcela(String parcela) {
        this.parcela = parcela;
    }

    public String getTotalDeParcelas() {
        return totalDeParcelas;
    }

    public void setTotalDeParcelas(String totalDeParcelas) {
        this.totalDeParcelas = totalDeParcelas;
    }

    public Double getValorBrutoMoeda() {
        return valorBrutoMoeda;
    }

    public void setValorBrutoMoeda(Double valorBrutoMoeda) {
        this.valorBrutoMoeda = valorBrutoMoeda;
    }

    public Double getValorLiquidoMoeda() {
        return valorLiquidoMoeda;
    }

    public void setValorLiquidoMoeda(Double valorLiquidoMoeda) {
        this.valorLiquidoMoeda = valorLiquidoMoeda;
    }

    public Double getCotacaoMoeda() {
        return cotacaoMoeda;
    }

    public void setCotacaoMoeda(Double cotacaoMoeda) {
        this.cotacaoMoeda = cotacaoMoeda;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }

    public String getNsu() {
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getTerminal() {
        return terminal;
    }

    public void setTerminal(String terminal) {
        this.terminal = terminal;
    }

    public String getMeioCaptura() {
        return meioCaptura;
    }

    public void setMeioCaptura(String meioCaptura) {
        this.meioCaptura = meioCaptura;
    }

    public String getOperadora() {
        return operadora;
    }

    public void setOperadora(String operadora) {
        this.operadora = operadora;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }
}

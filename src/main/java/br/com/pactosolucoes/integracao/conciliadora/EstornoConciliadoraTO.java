package br.com.pactosolucoes.integracao.conciliadora;

import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class EstornoConciliadoraTO extends SuperTO {

    private Integer codigo;
    private Integer movPagamento;
    private Integer reciboPagamento;
    private Integer empresa;
    private Date dataLancamento;
    private String empresaConciliadora;
    private String senhaConciliadora;
    private boolean processado;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMovPagamento() {
        return movPagamento;
    }

    public void setMovPagamento(Integer movPagamento) {
        this.movPagamento = movPagamento;
    }

    public Integer getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(Integer reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public boolean isProcessado() {
        return processado;
    }

    public void setProcessado(boolean processado) {
        this.processado = processado;
    }

    public String getEmpresaConciliadora() {
        return empresaConciliadora;
    }

    public void setEmpresaConciliadora(String empresaConciliadora) {
        this.empresaConciliadora = empresaConciliadora;
    }

    public String getSenhaConciliadora() {
        return senhaConciliadora;
    }

    public void setSenhaConciliadora(String senhaConciliadora) {
        this.senhaConciliadora = senhaConciliadora;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
}

package br.com.pactosolucoes.integracao.conciliadora;

public enum ModalidadeEnumConciliadora {

    DEBITO(0, "DÉBITO"),
    CREDITO_A_VISTA(1, "CRÉDITO A VISTA"),
    CREDITO_PARCELADO(2, "CRÉDITO PARCELADO"),
    BENEFICIO(3, "BENEFÍCIO");

    private Integer codigo;
    private String descricao;

    ModalidadeEnumConciliadora(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

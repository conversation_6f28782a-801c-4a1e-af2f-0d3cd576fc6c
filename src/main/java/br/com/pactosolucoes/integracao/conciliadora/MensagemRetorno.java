//
// Este arquivo foi gerado pela Arquitetura JavaTM para Implementação de Referência (JAXB) de Bind XML, v2.2.8-b130911.1802 
// Consulte <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Todas as modificações neste arquivo serão perdidas após a recompilação do esquema de origem. 
// Gerado em: 2019.03.22 às 05:45:37 PM BRT 
//


package br.com.pactosolucoes.integracao.conciliadora;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlValue;


/**
 * <p>Classe Java de anonymous complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Codigo" type="{http://www.w3.org/2001/XMLSchema}byte"/>
 *         &lt;element name="Mensagem" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="XMLErros">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="ErroArquivo" maxOccurs="unbounded" minOccurs="0">
 *                     &lt;complexType>
 *                       &lt;simpleContent>
 *                         &lt;extension base="&lt;http://www.w3.org/2001/XMLSchema>string">
 *                           &lt;attribute name="Linha" type="{http://www.w3.org/2001/XMLSchema}byte" />
 *                           &lt;attribute name="Erro" type="{http://www.w3.org/2001/XMLSchema}string" />
 *                         &lt;/extension>
 *                       &lt;/simpleContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "codigo",
    "mensagem",
    "xmlErros"
})
@XmlRootElement(name = "MensagemRetorno")
public class MensagemRetorno {

    @XmlElement(name = "Codigo")
    protected byte codigo;
    @XmlElement(name = "Mensagem", required = true)
    protected String mensagem;
    @XmlElement(name = "XMLErros", required = true)
    protected MensagemRetorno.XMLErros xmlErros;

    /**
     * Obtém o valor da propriedade codigo.
     * 
     */
    public byte getCodigo() {
        return codigo;
    }

    /**
     * Define o valor da propriedade codigo.
     * 
     */
    public void setCodigo(byte value) {
        this.codigo = value;
    }

    /**
     * Obtém o valor da propriedade mensagem.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMensagem() {
        return mensagem;
    }

    /**
     * Define o valor da propriedade mensagem.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMensagem(String value) {
        this.mensagem = value;
    }

    /**
     * Obtém o valor da propriedade xmlErros.
     * 
     * @return
     *     possible object is
     *     {@link MensagemRetorno.XMLErros }
     *     
     */
    public MensagemRetorno.XMLErros getXMLErros() {
        return xmlErros;
    }

    /**
     * Define o valor da propriedade xmlErros.
     * 
     * @param value
     *     allowed object is
     *     {@link MensagemRetorno.XMLErros }
     *     
     */
    public void setXMLErros(MensagemRetorno.XMLErros value) {
        this.xmlErros = value;
    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="ErroArquivo" maxOccurs="unbounded" minOccurs="0">
     *           &lt;complexType>
     *             &lt;simpleContent>
     *               &lt;extension base="&lt;http://www.w3.org/2001/XMLSchema>string">
     *                 &lt;attribute name="Linha" type="{http://www.w3.org/2001/XMLSchema}byte" />
     *                 &lt;attribute name="Erro" type="{http://www.w3.org/2001/XMLSchema}string" />
     *               &lt;/extension>
     *             &lt;/simpleContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "erroArquivo"
    })
    public static class XMLErros {

        @XmlElement(name = "ErroArquivo")
        protected List<MensagemRetorno.XMLErros.ErroArquivo> erroArquivo;

        /**
         * Gets the value of the erroArquivo property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the erroArquivo property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getErroArquivo().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link MensagemRetorno.XMLErros.ErroArquivo }
         * 
         * 
         */
        public List<MensagemRetorno.XMLErros.ErroArquivo> getErroArquivo() {
            if (erroArquivo == null) {
                erroArquivo = new ArrayList<MensagemRetorno.XMLErros.ErroArquivo>();
            }
            return this.erroArquivo;
        }


        /**
         * <p>Classe Java de anonymous complex type.
         * 
         * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;simpleContent>
         *     &lt;extension base="&lt;http://www.w3.org/2001/XMLSchema>string">
         *       &lt;attribute name="Linha" type="{http://www.w3.org/2001/XMLSchema}byte" />
         *       &lt;attribute name="Erro" type="{http://www.w3.org/2001/XMLSchema}string" />
         *     &lt;/extension>
         *   &lt;/simpleContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "value"
        })
        public static class ErroArquivo {

            @XmlValue
            protected String value;
            @XmlAttribute(name = "Linha")
            protected Byte linha;
            @XmlAttribute(name = "Erro")
            protected String erro;

            /**
             * Obtém o valor da propriedade value.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getValue() {
                return value;
            }

            /**
             * Define o valor da propriedade value.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setValue(String value) {
                this.value = value;
            }

            /**
             * Obtém o valor da propriedade linha.
             * 
             * @return
             *     possible object is
             *     {@link Byte }
             *     
             */
            public Byte getLinha() {
                return linha;
            }

            /**
             * Define o valor da propriedade linha.
             * 
             * @param value
             *     allowed object is
             *     {@link Byte }
             *     
             */
            public void setLinha(Byte value) {
                this.linha = value;
            }

            /**
             * Obtém o valor da propriedade erro.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getErro() {
                return erro;
            }

            /**
             * Define o valor da propriedade erro.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setErro(String value) {
                this.erro = value;
            }

        }

    }

}

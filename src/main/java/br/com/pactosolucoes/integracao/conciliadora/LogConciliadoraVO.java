package br.com.pactosolucoes.integracao.conciliadora;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.PerfilUsuarioNFe;

import java.util.Date;

public class LogConciliadoraVO extends SuperVO {


    private Integer codigo;
    private Date data;
    private EmpresaVO empresaVO;
    private MovPagamentoVO movPagamentoVO;
    private boolean sucesso = false;
    private String resultado;

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getData_Apresentar() {
        if (getData() == null) {
            return "";
        } else {
            return Uteis.getDataComHHMM(getData());
        }
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        if (movPagamentoVO == null) {
            movPagamentoVO = new MovPagamentoVO();
        }
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getResultado() {
        if (resultado == null) {
            resultado = "";
        }
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }
}

package br.com.pactosolucoes.integracao.conciliadora;

public enum MeioCapturaEnumConciliadora {

    POS(1, "POS"),
    PDV(2, "PDV"),
    E_COMMERCE(3, "E-COMMERCE"),
    EDI(4, "EDI"),
    ADP_BSP(5, "ADP/BSP"),
    MANUAL(6, "MANUAL"),
    URA_CVA(7, "URA/CVA"),
    MOBILE(8, "MOBILE"),
    MOEDEIRO(9, "MOEDEIRO ELETRONICO EM REDE");

    private Integer codigo;
    private String descricao;

    MeioCapturaEnumConciliadora(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

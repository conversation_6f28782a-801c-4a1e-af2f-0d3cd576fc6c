package br.com.pactosolucoes.integracao.conciliadora;

public enum StatusPagamentoConciliadora {

    NENHUM(0, "NENHUM", "", ""),
    AGUARDANDO(1, "AGUARDANDO ENVIO", "fa-icon-time", "color: #6e6ddb;"),
    SUCESSO(2, "ENVIADO", "fa-icon-ok-sign", "color: #3cdb5c;"),
    ERRO(3, "ERRO - Clique para verificar detalhes", "fa-icon-ban-circle", "color: #db394f;"),
    OUTRO_FORMA_PG(4, "PAGAMENTO NÃO É CARTÃO", "", "");

    private Integer codigo;
    private String descricao;
    private String styleClass;
    private String style;

    StatusPagamentoConciliadora(Integer codigo, String descricao, String styleClass, String style) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.styleClass = styleClass;
        this.style = style;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getStyleClass() {
        return styleClass;
    }

    public void setStyleClass(String styleClass) {
        this.styleClass = styleClass;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public static StatusPagamentoConciliadora obterPorCodigo(Integer codigo) {
        for (StatusPagamentoConciliadora status : StatusPagamentoConciliadora.values()) {
            if (status.getCodigo().equals(codigo)) {
                return status;
            }
        }
        return NENHUM;
    }

}

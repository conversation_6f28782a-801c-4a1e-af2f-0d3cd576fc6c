package br.com.pactosolucoes.integracao.conciliadora;

public enum ProdutoEnumConciliadora {

    COD_1(1, "VISA CREDITO A VISTA"),
    COD_2(2, "VISA ELECTRON DEBITO A VISTA"),
    COD_3(3, "VISA PARCELADO LOJA"),
    COD_4(4, "MASTER CREDITO"),
    COD_5(5, "<PERSON><PERSON>TR<PERSON>"),
    COD_6(6, "MASTER PARCELADO"),
    COD_7(7, "ELO CREDITO"),
    COD_8(8, "ELO DEBITO A VISTA"),
    COD_9(9, "ELO PARCELADO LOJA"),
    COD_10(10, "DINERS PARCELADO"),
    COD_11(11, "AGIPLAN CREDITO A VISTA"),
    COD_12(12, "AGIPLAN PARCELADO LOJA"),
    COD_13(13, "BANESCARD CREDITO A VISTA"),
    COD_14(14, "BANESCARD PARCELADO LOJA"),
    COD_15(15, "<PERSON><PERSON><PERSON><PERSON><PERSON> CREDITO A VISTA"),
    COD_16(16, "CREDZ CREDITO A VISTA"),
    COD_17(17, "ESPLANADA PARCELADO LOJA"),
    COD_18(18, "CREDZ PARCELADO LOJA"),
    COD_19(19, "ELO AGRO DEBITO"),
    COD_20(20, "ELO AGRO CUSTEIO"),
    COD_21(21, "ELO AGRO INVESTIMENTO"),
    COD_22(22, "ELO AGRO CUSTEIO + DEBITO"),
    COD_23(23, "ELO AGRO INVESTIMENTO + DEBITO"),
    COD_24(24, "DISCOVER CREDITO A VISTA"),
    COD_25(25, "DINERS CREDITO A VISTA"),
    COD_27(27, "AGRO CUSTEIO + ELECTRON"),
    COD_28(28, "AGRO INVESTIMENTO + ELECTRON"),
    COD_29(29, "FCO INVESTIMENTO"),
    COD_30(30, "AGRO ELECTRON"),
    COD_31(31, "AGRO CUSTEIO"),
    COD_32(32, "AGRO INVESTIMENTO"),
    COD_33(33, "FCO GIRO"),
    COD_34(34, "JCB"),
    COD_35(35, "VISA SAQUE CARTAO DEBITO"),
    COD_36(36, "VISA VALE FLEX CAR"),
    COD_37(37, "Credsystem Crédito a Vista"),
    COD_38(38, "CREDSYSTEM PARCELADO LOJA"),
    COD_39(39, "VISA ELECTRON PRE-DATADO"),
    COD_40(40, "VISA ALELO REFEICAO"),
    COD_41(41, "VISA ALELO ALIMENTACAO"),
    COD_42(42, "ALELO AUTO"),
    COD_43(43, "SOROCRED CREDITO A VISTA"),
    COD_44(44, "SOROCRED PARCELADO LOJA"),
    COD_45(45, "VISA CREDIARIO"),
    COD_46(46, "ELO ALELO REFEICAO"),
    COD_47(47, "ELO ALELO ALIMENTACAO"),
    COD_48(48, "VISA VALE CULTURA"),
    COD_49(49, "BANESCARD DEBITO"),
    COD_51(51, "CABAL DEBITO"),
    COD_52(52, "CABAL PARCELADO LOJA"),
    COD_54(54, "CABAL CREDITO A VISTA"),
    COD_55(55, "Hiper Crédito a Vista"),
    COD_56(56, "Hiper Parcelado Loja"),
    COD_58(58, "Credsystem Débito"),
    COD_60(60, "CUP CREDITO A VISTA"),
    COD_61(61, "CUP DEBITO"),
    COD_62(62, "CUP PARCELADO LOJA"),
    COD_64(64, "DINERS DEBITO"),
    COD_66(66, "HIPERCARD CREDITO A VISTA"),
    COD_67(67, "Hiper Débito"),
    COD_68(68, "HIPERCARD PARCELADO LOJA"),
    COD_69(69, "SICREDI DEBITO"),
    COD_70(70, "SICREDI CREDITO A VISTA"),
    COD_71(71, "SICREDI PARCELADO LOJA"),
    COD_72(72, "ELO CREDIARIO"),
    COD_73(73, "ELO CONSTRUCARD"),
    COD_74(74, "VISA PEDAGIO"),
    COD_75(75, "VISA CAPITAL DE GIRO"),
    COD_76(76, "VISA CREDITO IMOBILIARIO"),
    COD_77(77, "VISA ELECTRON PAGAMENTO CARNE"),
    COD_78(78, "MASTERCARD MINHA CASA MELHOR"),
    COD_79(79, "ELO CREDITO IMOBILIARIO"),
    COD_80(80, "ELO MINHA CASA MELHOR"),
    COD_81(81, "SOROCRED DEBITO"),
    COD_82(82, "CREDISHOP"),
    COD_98(98, "Não Conciliado"),
    COD_99(99, "Não Informado"),
    COD_102(102, "TICKET CULTURA"),
    COD_197(197, "AMEX CREDITO A VISTA"),
    COD_198(198, "AMEX CREDITO PARCELADO"),
    COD_300(300, "SODEXO"),
    COD_301(301, "TICKET ALIMENTACAO"),
    COD_302(302, "VR ALIMENTACAO Obsoleto"),
    COD_303(303, "TICKET REFEICAO"),
    COD_304(304, "TICKET PAPEL"),
    COD_305(305, "Banricard Débito"),
    COD_306(306, "Banricard Crédito"),
    COD_307(307, "BANRICARD PARCELADO"),
    COD_308(308, "VERDECARD DEBITO"),
    COD_309(309, "VERDECARD CREDITO"),
    COD_310(310, "VERDECARD PARCELADO"),
    COD_311(311, "Banricompras Débito"),
    COD_312(312, "Banricompras Crédito"),
    COD_313(313, "BANRICOMPRAS PARCELADO"),
    COD_314(314, "Banricompras Pré-Datado"),
    COD_315(315, "ECX CARD CREDITO"),
    COD_316(316, "ECX CARD PARCELADO"),
    COD_317(317, "VISA CREDITO CONVERSOR DE MOEDA"),
    COD_318(318, "MASTER PEDÁGIO"),
    COD_319(319, "ELO CARNÊ"),
    COD_320(320, "MASTER CARNÊ"),
    COD_321(321, "MASTERCARD CREDITO CONVERSOR MOEDA"),
    COD_322(322, "GOODCARD DEBITO"),
    COD_323(323, "GOODCARD CREDITO"),
    COD_324(324, "GOODCARD CREDITO PARCELADO"),
    COD_325(325, "GOODCARD VOUCHER"),
    COD_326(326, "TICKET"),
    COD_327(327, "TICKET PARCELADO"),
    COD_328(328, "POLICARD TEF (Debito)"),
    COD_329(329, "SODEXO REFEICAO"),
    COD_330(330, "SODEXO ALIMENTACAO"),
    COD_331(331, "SODEXO PREMIUM PASS"),
    COD_332(332, "SODEXO GIFT PASS"),
    COD_333(333, "SODEXO CULTURA PASS"),
    COD_334(334, "SODEXO MOBILITY PASS CARRO"),
    COD_335(335, "TICKET CAR"),
    COD_336(336, "ELO CULTURA"),
    COD_337(337, "CARTAO SHELL"),
    COD_338(338, "AVISTA CREDITO"),
    COD_339(339, "PAGAR.ME DEBITO"),
    COD_340(340, "PAGAR.ME CREDITO"),
    COD_341(341, "PAGAR.ME PARCELADO"),
    COD_342(342, "BANRICARD VOUCHER"),
    COD_344(344, "BANESCARD CREDITO");


    private Integer codigo;
    private String descricao;

    ProdutoEnumConciliadora(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

package br.com.pactosolucoes.integracao.conciliadora;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import negocio.comuns.arquitetura.SuperTO;

/**
 * <AUTHOR>
 */
@XStreamAlias("cabecalho")
public class CabecalhoConciliadoraTO extends SuperTO {

    @XStreamAlias("Empresa")
    private String empresa;
    @XStreamAlias("DataInicial")
    private String dataInicial;
    @XStreamAlias("DataFinal")
    private String dataFinal;
    @XStreamAlias("Versao")
    private String versao;
    @XStreamAlias("Lote")
    private Integer lote;
    @XStreamAlias("NomeSistema")
    private String nomeSistema;

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(String dataInicial) {
        this.dataInicial = dataInicial;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getVersao() {
        return versao;
    }

    public void setVersao(String versao) {
        this.versao = versao;
    }

    public Integer getLote() {
        return lote;
    }

    public void setLote(Integer lote) {
        this.lote = lote;
    }

    public String getNomeSistema() {
        return nomeSistema;
    }

    public void setNomeSistema(String nomeSistema) {
        this.nomeSistema = nomeSistema;
    }
}

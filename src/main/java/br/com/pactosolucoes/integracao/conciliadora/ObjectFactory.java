//
// Este arquivo foi gerado pela Arquitetura JavaTM para Implementação de Referência (JAXB) de Bind XML, v2.2.8-b130911.1802 
// Consulte <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Todas as modificações neste arquivo serão perdidas após a recompilação do esquema de origem. 
// Gerado em: 2019.03.22 às 05:45:37 PM BRT 
//


package br.com.pactosolucoes.integracao.conciliadora;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the generated package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: generated
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link MensagemRetorno }
     * 
     */
    public MensagemRetorno createMensagemRetorno() {
        return new MensagemRetorno();
    }

    /**
     * Create an instance of {@link MensagemRetorno.XMLErros }
     * 
     */
    public MensagemRetorno.XMLErros createMensagemRetornoXMLErros() {
        return new MensagemRetorno.XMLErros();
    }

    /**
     * Create an instance of {@link MensagemRetorno.XMLErros.ErroArquivo }
     * 
     */
    public MensagemRetorno.XMLErros.ErroArquivo createMensagemRetornoXMLErrosErroArquivo() {
        return new MensagemRetorno.XMLErros.ErroArquivo();
    }

}

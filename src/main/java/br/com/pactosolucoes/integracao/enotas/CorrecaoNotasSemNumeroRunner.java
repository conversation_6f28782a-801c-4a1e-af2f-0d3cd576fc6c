package br.com.pactosolucoes.integracao.enotas;

import br.com.pactosolucoes.integracao.enotas.to.NotaEnotasTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class CorrecaoNotasSemNumeroRunner {
    private static void atualizarNotasSemNumero(Connection con, ResultSet notas) throws Exception {
        try {
            con.setAutoCommit(false);

            while (notas.next()) {

                TipoNotaFiscalEnum tipoNota = TipoNotaFiscalEnum.obterPorCodigo(notas.getInt("tipo"));
                JSONObject jsonRetorno = new JSONObject(notas.getString("jsonretorno"));

                NotaEnotasTO notaTO = new NotaEnotasTO(tipoNota, jsonRetorno);

                String sql = "UPDATE notafiscal SET numeronota = ?  WHERE codigo = ?";

                PreparedStatement update = con.prepareStatement(sql);

                update.setString(1, notaTO.getNumero());
                update.setInt(2, notas.getInt("codigo"));

                update.execute();

                Uteis.logar("Executando %s", update.toString());

                con.commit();
            }
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;

            List<Map> lista = new ArrayList<Map>();

            if (args.length == 0) {
                Map<String, String> m = new HashMap<>();
                m.put("chave", "teste");
                lista.add(m);
            }

            if (args.length > 0 && args[0].equals("todas")) {
                DAO oamd = new DAO();
                lista = oamd.buscarListaEmpresas();
            }

            if (args.length == 1) {
                Map<String, String> m = new HashMap<>();
                m.put("chave", args[0]);
            }

            for (Map map : lista) {
                final String chave = (String) map.get("chave");
                Uteis.logar(null, "Obter conexão para chave: " + chave);

                Connection con = null;
                try {
                    con = new DAO().obterConexaoEspecifica(chave);
                    try (ResultSet notas = SuperFacadeJDBC.criarConsulta("SELECT * FROM notafiscal WHERE statusnota = 'Autorizada' AND (numeronota = '' OR numeronota IS NULL)", con)) {
                        atualizarNotasSemNumero(con, notas);
                    } catch (Exception ex) {
                        Logger.getLogger(CorrecaoNotasSemNumeroRunner.class.getName()).log(Level.SEVERE, null, ex);
                    }
                } catch (Exception ex) {
                    Logger.getLogger(CorrecaoNotasSemNumeroRunner.class.getName()).log(Level.SEVERE, null, ex);
                } finally {
                    try {
                        con.close();
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

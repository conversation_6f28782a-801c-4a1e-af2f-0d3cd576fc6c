package br.com.pactosolucoes.integracao.enotas.empresa;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.basico.ConfiguracaoNotaFiscalAmbienteVO;
import negocio.comuns.utilitarias.UteisValidacao;

public class EnotasEmpresaConfigNFSeJSON extends SuperJSON {

    private Integer sequencialNFe;
    private String serieNFe;
    private Integer sequencialLoteNFe;
    private String usuarioAcessoProvedor;
    private String senhaAcessoProvedor;
    private String tokenAcessoProvedor;


    public EnotasEmpresaConfigNFSeJSON(ConfiguracaoNotaFiscalAmbienteVO configVO) {
        this.sequencialNFe = configVO.getSequencialNFe();
        this.serieNFe = configVO.getSerieNFe();
        this.sequencialLoteNFe = configVO.getSequencialLoteNFe();
        if (UteisValidacao.emptyString(configVO.getUsuarioAcessoProvedor())) {
            this.usuarioAcessoProvedor = null;
        } else {
            this.usuarioAcessoProvedor = configVO.getUsuarioAcessoProvedor();
        }
        if (UteisValidacao.emptyString(configVO.getSenhaAcessoProvedor())) {
            this.senhaAcessoProvedor = null;
        } else {
            this.senhaAcessoProvedor = configVO.getSenhaAcessoProvedor();
        }

        if (UteisValidacao.emptyString(configVO.getTokenAcessoProvedor())) {
            this.tokenAcessoProvedor = null;
        } else {
            this.tokenAcessoProvedor = configVO.getTokenAcessoProvedor();
        }
    }

    public Integer getSequencialNFe() {
        return sequencialNFe;
    }

    public void setSequencialNFe(Integer sequencialNFe) {
        this.sequencialNFe = sequencialNFe;
    }

    public String getSerieNFe() {
        return serieNFe;
    }

    public void setSerieNFe(String serieNFe) {
        this.serieNFe = serieNFe;
    }

    public Integer getSequencialLoteNFe() {
        return sequencialLoteNFe;
    }

    public void setSequencialLoteNFe(Integer sequencialLoteNFe) {
        this.sequencialLoteNFe = sequencialLoteNFe;
    }

    public String getUsuarioAcessoProvedor() {
        return usuarioAcessoProvedor;
    }

    public void setUsuarioAcessoProvedor(String usuarioAcessoProvedor) {
        this.usuarioAcessoProvedor = usuarioAcessoProvedor;
    }

    public String getSenhaAcessoProvedor() {
        return senhaAcessoProvedor;
    }

    public void setSenhaAcessoProvedor(String senhaAcessoProvedor) {
        this.senhaAcessoProvedor = senhaAcessoProvedor;
    }

    public String getTokenAcessoProvedor() {
        return tokenAcessoProvedor;
    }

    public void setTokenAcessoProvedor(String tokenAcessoProvedor) {
        this.tokenAcessoProvedor = tokenAcessoProvedor;
    }
}

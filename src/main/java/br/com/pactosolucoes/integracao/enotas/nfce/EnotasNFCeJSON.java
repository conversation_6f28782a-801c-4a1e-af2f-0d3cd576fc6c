package br.com.pactosolucoes.integracao.enotas.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.integracao.enotas.nfse.EnotasNFSeClienteJSON;
import br.com.pactosolucoes.integracao.enotas.nfse.EnotasNFSeServicoJSON;
import org.json.JSONException;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaProdutoTO;
import negocio.comuns.notaFiscal.NotaTO;

import java.util.ArrayList;
import java.util.List;

public class EnotasNFCeJSON extends SuperJSON {

    @JsonProperty("id")
    private String id;
    @JsonProperty("ambienteEmissao")
    private String ambienteEmissao;
    @JsonProperty("numero")
    private Integer numero;
    @JsonProperty("serie")
    private String serie;
    @JsonProperty("naturezaOperacao")
    private String naturezaOperacao;
    @JsonProperty("tipoOperacao")
    private String tipoOperacao;
    @JsonProperty("finalidade")
    private String finalidade;
    @JsonProperty("consumidorFinal")
    private boolean consumidorFinal;
    @JsonProperty("indicadorPresencaConsumidor")
    private String indicadorPresencaConsumidor;
    @JsonProperty("enviarPorEmail")
    private boolean enviarPorEmail;
    @JsonProperty("informacoesAdicionais")
    private String informacoesAdicionais;

    @JsonProperty("pedido")
    private EnotasNFCePedidoJSON pedido;

    @JsonProperty("cliente")
    private EnotasNFCeClienteJSON cliente;
    @JsonProperty("itens")
    private List<EnotasNFCeProdutoJSON> itens;


    public EnotasNFCeJSON(NotaTO notaTO) {
        this.id = notaTO.getNotaIDReferencia();
        this.ambienteEmissao = notaTO.getConfiguracaoNotaFiscalVO().getAmbienteEmissao().getDescricaoSemAcentuacao();
        this.numero = Integer.parseInt(notaTO.getNotaNumero());
        this.serie = notaTO.getNotaSerie();
        this.naturezaOperacao = notaTO.getNotaNaturezaOperacao();
        this.tipoOperacao = "Saida";
        this.finalidade = "Normal";
        this.consumidorFinal = true;
        this.indicadorPresencaConsumidor = "OperacaoPresencial";
        this.enviarPorEmail = notaTO.isNotaEnviarEmail();
        this.informacoesAdicionais = notaTO.getNotaObservacao().trim();
        this.cliente = new EnotasNFCeClienteJSON(notaTO);

        this.itens = new ArrayList<>();
        for (NotaProdutoTO notaProdutoTO : notaTO.getNotaProdutos()){
            this.itens.add(new EnotasNFCeProdutoJSON(notaProdutoTO));
        }
    }
}

package br.com.pactosolucoes.integracao.enotas.nfse;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.utilitarias.UteisValidacao;

import java.time.temporal.ChronoUnit;
import java.util.Date;

public class EnotasNFSeJSON extends SuperJSON {

    private String idExterno;
    private String naturezaOperacao;
    private String ambienteEmissao;
    private String dataCompetencia;
    private boolean enviarPorEmail;
    private Double valorTotal;
    private String observacoes;
    private EnotasNFSeClienteJSON cliente;
    private EnotasNFSeServicoJSON servico;


    public EnotasNFSeJSON(NotaTO notaTO) {
        this.idExterno = notaTO.getNotaIDReferencia();
        this.ambienteEmissao = notaTO.getConfiguracaoNotaFiscalVO().getAmbienteEmissao().getDescricaoSemAcentuacao();
        Date dataEmissao = notaTO.getNotaDtEmissao();
        this.dataCompetencia = (dataEmissao != null) ? dataEmissao.toInstant().truncatedTo(ChronoUnit.SECONDS).toString() : null;
        this.enviarPorEmail = notaTO.isNotaEnviarEmail();
        this.valorTotal = notaTO.getNotaValor();
        this.observacoes = notaTO.getNotaObservacao().trim();
        this.naturezaOperacao = UteisValidacao.emptyString(notaTO.getNotaNaturezaOperacao()) ? null : notaTO.getNotaNaturezaOperacao();
        this.cliente = new EnotasNFSeClienteJSON(notaTO);
        this.servico = new EnotasNFSeServicoJSON(notaTO);
    }

    public String getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }

    public String getAmbienteEmissao() {
        return ambienteEmissao;
    }

    public void setAmbienteEmissao(String ambienteEmissao) {
        this.ambienteEmissao = ambienteEmissao;
    }

    public boolean isEnviarPorEmail() {
        return enviarPorEmail;
    }

    public void setEnviarPorEmail(boolean enviarPorEmail) {
        this.enviarPorEmail = enviarPorEmail;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getObservacoes() {
        return observacoes;
    }

    public void setObservacoes(String observacoes) {
        this.observacoes = observacoes;
    }

    public EnotasNFSeClienteJSON getCliente() {
        return cliente;
    }

    public void setCliente(EnotasNFSeClienteJSON cliente) {
        this.cliente = cliente;
    }

    public EnotasNFSeServicoJSON getServico() {
        return servico;
    }

    public void setServico(EnotasNFSeServicoJSON servico) {
        this.servico = servico;
    }

    public String getDataCompetencia() {
        return dataCompetencia;
    }

    public void setDataCompetencia(String dataCompetencia) {
        this.dataCompetencia = dataCompetencia;
    }

    public String getNaturezaOperacao() {
        return naturezaOperacao;
    }

    public void setNaturezaOperacao(String naturezaOperacao) {
        this.naturezaOperacao = naturezaOperacao;
    }
}

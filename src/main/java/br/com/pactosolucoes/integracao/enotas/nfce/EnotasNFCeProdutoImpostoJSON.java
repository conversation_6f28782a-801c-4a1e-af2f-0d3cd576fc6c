package br.com.pactosolucoes.integracao.enotas.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaProdutoTO;

public class EnotasNFCeProdutoImpostoJSON extends SuperJSON {

    @JsonProperty("percentualAproximadoTributos")
    private EnotasNFCeProdutoPercentualTributosFederaisJSON percentualAproximadoTributos;
    @JsonProperty("icms")
    private EnotasNFCeImpostoJSON icms;
    @JsonProperty("pis")
    private EnotasNFCeImpostoJSON pis;
    @JsonProperty("cofins")
    private EnotasNFCeImpostoJSON cofins;


    public EnotasNFCeProdutoImpostoJSON(NotaProdutoTO notaProdutoTO) {
//        this.cfop = notaProdutoTO.getCfop();
//        this.codigo = notaProdutoTO.getProdutoVO().getCodigo().toString();
//        this.descricao = notaProdutoTO.getDescricao();
//        this.ncm = notaProdutoTO.getNcm();
//        this.quantidade = notaProdutoTO.getQuantidade();
//        this.unidadeMedida = notaProdutoTO.getUnidadeMedida();
//        this.valorUnitario = notaProdutoTO.getValorUnitario();
//        this.descontos = notaProdutoTO.getValorDesconto();
//        this.outrasDespesas = notaProdutoTO.getOutrasDespesas();
    }
}

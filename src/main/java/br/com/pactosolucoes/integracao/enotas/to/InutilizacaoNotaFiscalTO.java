package br.com.pactosolucoes.integracao.enotas.to;

import negocio.comuns.arquitetura.SuperTO;
import org.json.JSONObject;

import java.util.Date;

public class InutilizacaoNotaFiscalTO extends SuperTO {

    private String id;
    private String ambienteEmissao;
    private String status;
    private String motivoStatus;
    private String serie;
    private Integer numeroInicial;
    private Integer numeroFinal;
    private String justificativa;
    private String protocoloAutorizacao;
    private String dataCriacao;
    private String dataUltimaAlteracao;
    //Data do registro feito ao inutilizar a nota pelo modulo de notas
    private Date dataRegistro;

    public InutilizacaoNotaFiscalTO() {

    }

    public InutilizacaoNotaFiscalTO(JSONObject jsonObject) {
        this.id = jsonObject.getString("id");
        this.ambienteEmissao = jsonObject.getString("ambienteEmissao");
        this.status = jsonObject.getString("status");
        this.motivoStatus = jsonObject.isNull("motivoStatus") ? "" : jsonObject.getString("motivoStatus").split(":")[2] + ": " + jsonObject.getString("motivoStatus").split(":")[3];
        this.serie = jsonObject.getString("serie");
        this.numeroInicial = jsonObject.getInt("numeroInicial");
        this.numeroFinal = jsonObject.getInt("numeroFinal");
        this.justificativa = jsonObject.getString("justificativa");
        this.protocoloAutorizacao = (jsonObject.isNull("protocoloAutorizacao")) ? "" : jsonObject.getString("protocoloAutorizacao");
        this.dataCriacao = jsonObject.getString("dataCriacao");
        this.dataUltimaAlteracao = jsonObject.getString("dataUltimaAlteracao");
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAmbienteEmissao() {
        return ambienteEmissao;
    }

    public void setAmbienteEmissao(String ambienteEmissao) {
        this.ambienteEmissao = ambienteEmissao;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMotivoStatus() {
        return motivoStatus;
    }

    public void setMotivoStatus(String motivoStatus) {
        this.motivoStatus = motivoStatus;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public Integer getNumeroInicial() {
        return numeroInicial;
    }

    public void setNumeroInicial(Integer numeroInicial) {
        this.numeroInicial = numeroInicial;
    }

    public Integer getNumeroFinal() {
        return numeroFinal;
    }

    public void setNumeroFinal(Integer numeroFinal) {
        this.numeroFinal = numeroFinal;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public String getProtocoloAutorizacao() {
        return protocoloAutorizacao;
    }

    public void setProtocoloAutorizacao(String protocoloAutorizacao) {
        this.protocoloAutorizacao = protocoloAutorizacao;
    }

    public String getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(String dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public String getDataUltimaAlteracao() {
        return dataUltimaAlteracao;
    }

    public void setDataUltimaAlteracao(String dataUltimaAlteracao) {
        this.dataUltimaAlteracao = dataUltimaAlteracao;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }
}

package br.com.pactosolucoes.integracao.enotas.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaPagamentoTO;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.utilitarias.Uteis;

public class EnotasNFCeFormasPagamentoJSON extends SuperJSON {

    @JsonProperty("tipo")
    private String tipo;
    @JsonProperty("valor")
    private Double valor;


    public EnotasNFCeFormasPagamentoJSON(NotaPagamentoTO notaPagamentoTO) {
        this.valor = notaPagamentoTO.getValor();
        this.tipo = Uteis.tirarCaracteres(notaPagamentoTO.getSiglaFormaPagamento(), true);;
    }

}

package br.com.pactosolucoes.integracao.enotas.empresa;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class EnotasEmpresaJSON extends SuperJSON {

    private String id;
    private String cnpj;
    private String razaoSocial;
    private String nomeFantasia;
    private String inscricaoMunicipal;
    private String inscricaoEstadual;
    private boolean optanteSimplesNacional;
    private String email;
    private String telefoneComercial;
    private boolean incentivadorCultural;
    private String regimeEspecialTributacao;
    private String codigoServicoMunicipal;
    private String itemListaServicoLC116;
    private String cnae;
    private Double aliquotaIss;
    private String descricaoServico;
    private boolean enviarEmailCliente;
    private EnotasEmpresaEnderecoJSON endereco;
    @JsonProperty("configuracoesNFSeHomologacao")
    private EnotasEmpresaConfigNFSeJSON configuracoesNFSeHomologacao;
    @JsonProperty("configuracoesNFSeProducao")
    private EnotasEmpresaConfigNFSeJSON configuracoesNFSeProducao;


    public EnotasEmpresaJSON(ConfiguracaoNotaFiscalVO configVO) {
        this.id = configVO.getIdEnotas();
        this.cnpj = Uteis.removerMascara(configVO.getCnpj());
        this.razaoSocial = configVO.getRazaoSocial();
        this.nomeFantasia = configVO.getNomeFantasia();
        if (UteisValidacao.emptyString(configVO.getInscricaoMunicipal())) {
            this.inscricaoMunicipal = null;
        } else {
            this.inscricaoMunicipal = configVO.getInscricaoMunicipal();
        }
        if (!UteisValidacao.emptyString(configVO.getInscricaoEstadual())) {
            this.inscricaoEstadual = Uteis.removerMascara(configVO.getInscricaoEstadual());
        }
        this.optanteSimplesNacional = configVO.isOptanteSimplesNacional();
        this.email = configVO.getEmail();
        this.telefoneComercial = Uteis.tirarCaracteres(configVO.getTelefoneComercial(), true);
        this.incentivadorCultural = configVO.isIncentivadorCultural();
        this.regimeEspecialTributacao = configVO.getRegimeEspecialTributacao();
        this.itemListaServicoLC116 = configVO.getCodListaServico();
        this.codigoServicoMunicipal = configVO.getCodTributacaoMunicipal();
        this.cnae = configVO.getCnae();
        this.aliquotaIss = configVO.getIss();
        if (UteisValidacao.emptyString(configVO.getDescricaoServico())) {
            this.descricaoServico = null;
        } else {
            this.descricaoServico = configVO.getDescricaoServico();
        }
        this.enviarEmailCliente = configVO.isEnviarEmailCliente();
        this.endereco = new EnotasEmpresaEnderecoJSON(configVO);
        this.configuracoesNFSeHomologacao = new EnotasEmpresaConfigNFSeJSON(configVO.getConfigHomologacaoVO());
        this.configuracoesNFSeProducao = new EnotasEmpresaConfigNFSeJSON(configVO.getConfigProducaoVO());
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getNomeFantasia() {
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getInscricaoMunicipal() {
        return inscricaoMunicipal;
    }

    public void setInscricaoMunicipal(String inscricaoMunicipal) {
        this.inscricaoMunicipal = inscricaoMunicipal;
    }

    public String getInscricaoEstadual() {
        return inscricaoEstadual;
    }

    public void setInscricaoEstadual(String inscricaoEstadual) {
        this.inscricaoEstadual = inscricaoEstadual;
    }

    public boolean isOptanteSimplesNacional() {
        return optanteSimplesNacional;
    }

    public void setOptanteSimplesNacional(boolean optanteSimplesNacional) {
        this.optanteSimplesNacional = optanteSimplesNacional;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefoneComercial() {
        return telefoneComercial;
    }

    public void setTelefoneComercial(String telefoneComercial) {
        this.telefoneComercial = telefoneComercial;
    }

    public boolean isIncentivadorCultural() {
        return incentivadorCultural;
    }

    public void setIncentivadorCultural(boolean incentivadorCultural) {
        this.incentivadorCultural = incentivadorCultural;
    }

    public String getRegimeEspecialTributacao() {
        return regimeEspecialTributacao;
    }

    public void setRegimeEspecialTributacao(String regimeEspecialTributacao) {
        this.regimeEspecialTributacao = regimeEspecialTributacao;
    }

    public String getCodigoServicoMunicipal() {
        return codigoServicoMunicipal;
    }

    public void setCodigoServicoMunicipal(String codigoServicoMunicipal) {
        this.codigoServicoMunicipal = codigoServicoMunicipal;
    }

    public String getItemListaServicoLC116() {
        return itemListaServicoLC116;
    }

    public void setItemListaServicoLC116(String itemListaServicoLC116) {
        this.itemListaServicoLC116 = itemListaServicoLC116;
    }

    public String getCnae() {
        return cnae;
    }

    public void setCnae(String cnae) {
        this.cnae = cnae;
    }

    public Double getAliquotaIss() {
        return aliquotaIss;
    }

    public void setAliquotaIss(Double aliquotaIss) {
        this.aliquotaIss = aliquotaIss;
    }

    public String getDescricaoServico() {
        return descricaoServico;
    }

    public void setDescricaoServico(String descricaoServico) {
        this.descricaoServico = descricaoServico;
    }

    public boolean isEnviarEmailCliente() {
        return enviarEmailCliente;
    }

    public void setEnviarEmailCliente(boolean enviarEmailCliente) {
        this.enviarEmailCliente = enviarEmailCliente;
    }

    public EnotasEmpresaEnderecoJSON getEndereco() {
        return endereco;
    }

    public void setEndereco(EnotasEmpresaEnderecoJSON endereco) {
        this.endereco = endereco;
    }

    public EnotasEmpresaConfigNFSeJSON getConfiguracoesNFSeHomologacao() {
        return configuracoesNFSeHomologacao;
    }

    public void setConfiguracoesNFSeHomologacao(EnotasEmpresaConfigNFSeJSON configuracoesNFSeHomologacao) {
        this.configuracoesNFSeHomologacao = configuracoesNFSeHomologacao;
    }

    public EnotasEmpresaConfigNFSeJSON getConfiguracoesNFSeProducao() {
        return configuracoesNFSeProducao;
    }

    public void setConfiguracoesNFSeProducao(EnotasEmpresaConfigNFSeJSON configuracoesNFSeProducao) {
        this.configuracoesNFSeProducao = configuracoesNFSeProducao;
    }

}

package br.com.pactosolucoes.integracao.enotas.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaProdutoTO;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.utilitarias.Uteis;

public class EnotasNFCeProdutoJSON extends SuperJSON {

    @JsonProperty("cfop")
    private String cfop;
    @JsonProperty("codigo")
    private String codigo;
    @JsonProperty("descricao")
    private String descricao;
    @JsonProperty("ncm")
    private String ncm;
    @JsonProperty("quantidade")
    private Integer quantidade;
    @JsonProperty("unidadeMedida")
    private String unidadeMedida;
    @JsonProperty("valorUnitario")
    private Double valorUnitario;
    @JsonProperty("descontos")
    private Double descontos;
    @JsonProperty("outrasDespesas")
    private Double outrasDespesas;


    public EnotasNFCeProdutoJSON(NotaProdutoTO notaProdutoTO) {
        this.cfop = notaProdutoTO.getCfop();
        this.codigo = notaProdutoTO.getProdutoVO().getCodigo().toString();
        this.descricao = notaProdutoTO.getDescricao();
        this.ncm = notaProdutoTO.getNcm();
        this.quantidade = notaProdutoTO.getQuantidade();
        this.unidadeMedida = notaProdutoTO.getUnidadeMedida();
        this.valorUnitario = notaProdutoTO.getValorUnitario();
        this.descontos = notaProdutoTO.getValorDesconto();
        this.outrasDespesas = notaProdutoTO.getOutrasDespesas();
    }
}

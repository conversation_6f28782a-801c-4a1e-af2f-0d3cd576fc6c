package br.com.pactosolucoes.integracao.enotas.to;

import negocio.comuns.arquitetura.SuperTO;
import org.json.JSONObject;

public class InfoServicoEnotasTO extends SuperTO {

    private String json;
    private Integer codigo;
    private String descricao;
    private Integer codigoIBGECidade;
    private Double aliquotaSugerida;
    private boolean construcaoCivil = false;
    private Double percentualAproximadoFederalIBPT;
    private Double percentualAproximadoEstadualIBPT;
    private Double percentualAproximadoMunicipalIBPT;
    private String chaveTabelaIBPT;

    public InfoServicoEnotasTO() {

    }

    public InfoServicoEnotasTO(JSONObject jsonObject) {
        this.json = jsonObject.toString();
        this.codigo = jsonObject.getInt("codigo");

        try {
            this.descricao = jsonObject.getString("descricao");
        } catch (Exception ignored) {
        }

        try {
            this.codigoIBGECidade = jsonObject.getInt("codigoIBGECidade");
        } catch (Exception ignored) {
        }

        try {
            this.aliquotaSugerida = jsonObject.getDouble("aliquotaSugerida");
        } catch (Exception ignored) {
        }

        try {
            this.construcaoCivil = jsonObject.getBoolean("construcaoCivil");
        } catch (Exception ignored) {
        }

        try {
            this.percentualAproximadoFederalIBPT = jsonObject.getDouble("percentualAproximadoFederalIBPT");
        } catch (Exception ignored) {
        }

        try {
            this.percentualAproximadoEstadualIBPT = jsonObject.getDouble("percentualAproximadoEstadualIBPT");
        } catch (Exception ignored) {
        }

        try {
            this.percentualAproximadoMunicipalIBPT = jsonObject.getDouble("percentualAproximadoMunicipalIBPT");
        } catch (Exception ignored) {
        }

        try {
            this.chaveTabelaIBPT = jsonObject.getString("chaveTabelaIBPT");
        } catch (Exception ignored) {
        }
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getAliquotaSugerida() {
        return aliquotaSugerida;
    }

    public void setAliquotaSugerida(Double aliquotaSugerida) {
        this.aliquotaSugerida = aliquotaSugerida;
    }

    public boolean isConstrucaoCivil() {
        return construcaoCivil;
    }

    public void setConstrucaoCivil(boolean construcaoCivil) {
        this.construcaoCivil = construcaoCivil;
    }

    public Double getPercentualAproximadoFederalIBPT() {
        return percentualAproximadoFederalIBPT;
    }

    public void setPercentualAproximadoFederalIBPT(Double percentualAproximadoFederalIBPT) {
        this.percentualAproximadoFederalIBPT = percentualAproximadoFederalIBPT;
    }

    public Double getPercentualAproximadoEstadualIBPT() {
        return percentualAproximadoEstadualIBPT;
    }

    public void setPercentualAproximadoEstadualIBPT(Double percentualAproximadoEstadualIBPT) {
        this.percentualAproximadoEstadualIBPT = percentualAproximadoEstadualIBPT;
    }

    public Double getPercentualAproximadoMunicipalIBPT() {
        return percentualAproximadoMunicipalIBPT;
    }

    public void setPercentualAproximadoMunicipalIBPT(Double percentualAproximadoMunicipalIBPT) {
        this.percentualAproximadoMunicipalIBPT = percentualAproximadoMunicipalIBPT;
    }

    public String getChaveTabelaIBPT() {
        return chaveTabelaIBPT;
    }

    public void setChaveTabelaIBPT(String chaveTabelaIBPT) {
        this.chaveTabelaIBPT = chaveTabelaIBPT;
    }

    public Integer getCodigoIBGECidade() {
        return codigoIBGECidade;
    }

    public void setCodigoIBGECidade(Integer codigoIBGECidade) {
        this.codigoIBGECidade = codigoIBGECidade;
    }

    public String getCodTributacaoMunicipio() {
        try {
            String[] lista = getDescricao().split("\\|");
            return lista[0].trim();
        } catch (Exception ex){
            return "";
        }
    }

    public String getCodListaServico() {
        try {
            String[] lista = getDescricao().split("\\|");
            String[] lista2 = lista[1].split("\\-");
            return lista2[0].trim();
        } catch (Exception ex){
            return "";
        }
    }
}

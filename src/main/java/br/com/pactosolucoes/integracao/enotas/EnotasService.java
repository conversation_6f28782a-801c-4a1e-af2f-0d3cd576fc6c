package br.com.pactosolucoes.integracao.enotas;

import br.com.pactosolucoes.integracao.enotas.empresa.EnotasEmpresaJSON;
import br.com.pactosolucoes.integracao.enotas.to.InfoCidadeEnotasTO;
import br.com.pactosolucoes.integracao.enotas.to.InfoEmpresaEnotasTO;
import br.com.pactosolucoes.integracao.enotas.to.InfoServicoEnotasTO;
import br.com.pactosolucoes.integracao.enotas.to.InutilizacaoNotaFiscalTO;
import br.com.pactosolucoes.integracao.enotas.to.NotaEnotasTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaVO;
import negocio.comuns.financeiro.enumerador.TipoExigibilidadeISSEnum;
import negocio.comuns.notaFiscal.*;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.util.ExecuteRequestHttpService;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.GZIPInputStream;

public class EnotasService extends SuperEntidade {

    private static final String ENVIA_NOTA = Uteis.getUrlServicoNotaFiscal() + "/nf/gravar";
    private static final String ENVIA_OPERACAO = Uteis.getUrlServicoNotaFiscal() + "/op/gravar";
    private static final String ENVIA_EMPRESA = Uteis.getUrlServicoNotaFiscal() + "/enotas/empresa";
    private static final String ENVIA_LOGOTIPO = Uteis.getUrlServicoNotaFiscal() + "/enotas/logotipo";
    private static final String ENVIA_CERTIFICADO = Uteis.getUrlServicoNotaFiscal() + "/enotas/certificado";
    private static final String INFO_CIDADE = Uteis.getUrlServicoNotaFiscal() + "/enotas/infoCidade";
    private static final String INFO_SERVICO = Uteis.getUrlServicoNotaFiscal() + "/enotas/infoServico";
    private static final String CONSULTAR_NOTA = Uteis.getUrlServicoNotaFiscal() + "/enotas/consultarNota";
    private static final String CONSULTAR_NOTA_NOTA = Uteis.getUrlServicoNotaFiscal() + "/nf/consultar";
    private static final String CONSULTAR_EMPRESA = Uteis.getUrlServicoNotaFiscal() + "/enotas/consultarEmpresa";
    private static final String OBTER_SETUP_SAT = Uteis.getUrlServicoNotaFiscal() + "/enotas/setupSAT";
    private static final String HABILITAR_DESABILITAR_EMPRESA = Uteis.getUrlServicoNotaFiscal() + "/enotas/habilitarDesabilitarEmpresa";
    private static final String CONSULTAR_INUTILIZACAO = Uteis.getUrlServicoNotaFiscal()  + "/enotas/consultaInutilizacao";

    public EnotasService() throws Exception {
        super();
    }

    public EnotasService(Connection con) throws Exception {
        super(con);
    }

    public void incluirAtualizarEmpresa(boolean enviarConfiguracoesAmbiente, ConfiguracaoNotaFiscalVO configVO) throws Exception {

        JSONObject jsonObject = new JSONObject(new EnotasEmpresaJSON(configVO).toJSON());

        if (UteisValidacao.emptyString(configVO.getIdEnotas())) {
            jsonObject.remove("id");
        }

        if (configVO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE) ||
                configVO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE) ||
                !enviarConfiguracoesAmbiente) {
            jsonObject.remove("configuracoesNFSeHomologacao");
            jsonObject.remove("configuracoesNFSeProducao");
        }

        if (UteisValidacao.emptyString(configVO.getInscricaoEstadual())) {
            jsonObject.remove("inscricaoEstadual");
        }

        if (configVO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE) && enviarConfiguracoesAmbiente) {
            //AMBIENTE DE PRODUCAO
            JSONObject csc = new JSONObject();
            csc.put("id", configVO.getConfigProducaoVO().getIdCSC());
            csc.put("codigo", configVO.getConfigProducaoVO().getCsc());

            JSONObject ambienteProducao = new JSONObject();
            ambienteProducao.put("sequencialNFe", configVO.getConfigProducaoVO().getSequencialNFe());
            ambienteProducao.put("serieNFe", configVO.getConfigProducaoVO().getSerieNFe());
            ambienteProducao.put("csc", csc);

            //AMBIENTE DE HOMOLOGACAO
            JSONObject cscHomo = new JSONObject();
            cscHomo.put("id", configVO.getConfigHomologacaoVO().getIdCSC());
            cscHomo.put("codigo", configVO.getConfigHomologacaoVO().getCsc());

            JSONObject ambienteHomologacao = new JSONObject();
            ambienteHomologacao.put("sequencialNFe", configVO.getConfigHomologacaoVO().getSequencialNFe());
            ambienteHomologacao.put("serieNFe", configVO.getConfigHomologacaoVO().getSerieNFe());
            ambienteHomologacao.put("csc", cscHomo);

            //adicionar no json
            JSONObject emissaoNFeConsumidor = new JSONObject();
            emissaoNFeConsumidor.put("ambienteProducao", ambienteProducao);
            emissaoNFeConsumidor.put("ambienteHomologacao", ambienteHomologacao);
            jsonObject.put("emissaoNFeConsumidor", emissaoNFeConsumidor);
        }

        if (configVO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE) && enviarConfiguracoesAmbiente) {
            //AMBIENTE DE PRODUCAO
            JSONObject ambienteProducao = new JSONObject();
            ambienteProducao.put("sequencialNFe", configVO.getConfigProducaoVO().getSequencialNFe());
            ambienteProducao.put("serieNFe", configVO.getConfigProducaoVO().getSerieNFe());
            ambienteProducao.put("sequencialLoteNFe", configVO.getConfigProducaoVO().getSequencialLoteNFe());

            //AMBIENTE DE HOMOLOGACAO
            JSONObject ambienteHomologacao = new JSONObject();
            ambienteHomologacao.put("sequencialNFe", configVO.getConfigHomologacaoVO().getSequencialNFe());
            ambienteHomologacao.put("serieNFe", configVO.getConfigHomologacaoVO().getSerieNFe());
            ambienteHomologacao.put("sequencialLoteNFe", configVO.getConfigHomologacaoVO().getSequencialLoteNFe());

            //adicionar no json
            JSONObject emissaoNFeConsumidor = new JSONObject();
            emissaoNFeConsumidor.put("ambienteProducao", ambienteProducao);
            emissaoNFeConsumidor.put("ambienteHomologacao", ambienteHomologacao);
            jsonObject.put("emissaoNFeProduto", emissaoNFeConsumidor);
            jsonObject.put("emissaoNFeServico", emissaoNFeConsumidor);
        }

        JSONObject jsonEnviar = new JSONObject();
        jsonEnviar.put("tipoNotaFiscal", configVO.getTipoNotaFiscal().getCodigo());
        jsonEnviar.put("json", jsonObject.toString());

        String retorno = executaRequisicaoNotaFiscal(ENVIA_EMPRESA, jsonEnviar.toString());

        JSONObject jsonRetorno = new JSONObject(retorno);
        if (jsonRetorno.has("return")) {
            configVO.setIdEnotas(new JSONObject(jsonRetorno.getString("return")).getString("empresaId"));
        } else {

            StringBuilder msg = new StringBuilder();
            JSONArray lista = new JSONArray(jsonRetorno.getString("erro"));
            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                try {
                    msg.append(obj.getString("mensagem")).append("<br/>");
                } catch (Exception ignored) {
                }
            }
            throw new Exception(msg.toString());
        }
    }

    public void obterInformacoesModuloNotas(String chave, ConfiguracaoNotaFiscalVO obj) throws Exception {

        if (UteisValidacao.emptyString(Uteis.removerMascara(obj.getCnpj()))) {
            throw new Exception("Informe um CNPJ para continuar");
        }

        String url = SuperControle.getUrlModuloNFSe() + "/notaFiscalEmpresa?key=" + chave + "&cnpj=" + Uteis.removerMascara(obj.getCnpj());

        String retorno = executaRequisicaoNotaFiscal(url, "");

        JSONObject jsonRetorno = new JSONObject(retorno);
        if (jsonRetorno.has("return")) {

            JSONObject jsonObject = jsonRetorno.getJSONObject("return");

            if (jsonObject.has("optanteSimplesNacional")) {
                obj.setOptanteSimplesNacional(jsonObject.getBoolean("optanteSimplesNacional"));
            }
            if (jsonObject.has("regimeEspecialTributacao")) {
                obj.setRegimeEspecialTributacao(jsonObject.getString("regimeEspecialTributacao"));
            }
            if (jsonObject.has("inscricaoMunicipal")) {
                obj.setInscricaoMunicipal(jsonObject.getString("inscricaoMunicipal"));
            }
            if (jsonObject.has("inscricaoEstadual")) {
                obj.setInscricaoEstadual(jsonObject.getString("inscricaoEstadual"));
            }
            if (jsonObject.has("senhaCertificado")) {
                obj.setSenhaCertificado(jsonObject.getString("senhaCertificado"));
            }
            if (jsonObject.has("incentivadorCultural")) {
                obj.setIncentivadorCultural(jsonObject.getInt("incentivadorCultural") == 1);
            }
            if (jsonObject.has("iSSRetido")) {
                obj.setIssRetido(jsonObject.getBoolean("iSSRetido"));
            }
            if (jsonObject.has("exigibilidadeISS")) {
                obj.setExigibilidadeISS(TipoExigibilidadeISSEnum.getTipo(Integer.parseInt(jsonObject.getString("exigibilidadeISS"))));
            }
            if (jsonObject.has("itemListaServico")) {
                obj.setCodListaServico(jsonObject.getString("itemListaServico"));
            }
            if (jsonObject.has("codigoCnae")) {
                obj.setCnae(jsonObject.getString("codigoCnae"));
            }
            if (jsonObject.has("serierps")) {
                String serierps = jsonObject.getString("serierps");
                obj.setSerie(serierps);
                obj.getConfigProducaoVO().setSerieNFe(serierps);
                obj.getConfigHomologacaoVO().setSerieNFe(serierps);
            }
            if (jsonObject.has("optanteSimplesNacional")) {
                obj.setOptanteSimplesNacional(jsonObject.getBoolean("optanteSimplesNacional"));
            }
            if (jsonObject.has("usuarioInscricaoMunicipal")) {
                String usuarioInscricaoMunicipal = jsonObject.getString("usuarioInscricaoMunicipal");
                obj.getConfigProducaoVO().setSenhaAcessoProvedor(usuarioInscricaoMunicipal);
                obj.getConfigHomologacaoVO().setSenhaAcessoProvedor(usuarioInscricaoMunicipal);
            }
            if (jsonObject.has("senhaInscricaoMunicipal")) {
                String senhaInscricaoMunicipal = jsonObject.getString("senhaInscricaoMunicipal");
                obj.getConfigProducaoVO().setUsuarioAcessoProvedor(senhaInscricaoMunicipal);
                obj.getConfigHomologacaoVO().setUsuarioAcessoProvedor(senhaInscricaoMunicipal);
            }
            if (jsonObject.has("idCsc")) {
                String idCsc = jsonObject.getString("idCsc");
                obj.getConfigProducaoVO().setIdCSC(idCsc);
                obj.getConfigHomologacaoVO().setIdCSC(idCsc);
            }
            if (jsonObject.has("csc")) {
                String csc = jsonObject.getString("csc");
                obj.getConfigProducaoVO().setCsc(csc);
                obj.getConfigHomologacaoVO().setCsc(csc);
            }

            if (obj.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE) && jsonObject.has("numeroenvioNFCE")) {
                Integer numeroenvioNFCE = Integer.parseInt(jsonObject.getString("numeroenvioNFCE"));
                obj.getConfigProducaoVO().setSequencialLoteNFe(numeroenvioNFCE);
                obj.getConfigProducaoVO().setSequencialNFe(numeroenvioNFCE);
                obj.getConfigHomologacaoVO().setSequencialLoteNFe(numeroenvioNFCE);
                obj.getConfigHomologacaoVO().setSequencialNFe(numeroenvioNFCE);
            }

            if (!obj.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE)) {
                if (jsonObject.has("proximoRPS")) {
                    Integer proximoRPS = jsonObject.getInt("proximoRPS");
                    obj.getConfigProducaoVO().setSequencialNFe(proximoRPS);
                    obj.getConfigHomologacaoVO().setSequencialNFe(proximoRPS);
                }

                if (jsonObject.has("proximoLote")) {
                    Integer proximoLote = jsonObject.getInt("proximoLote");
                    obj.getConfigProducaoVO().setSequencialLoteNFe(proximoLote);
                    obj.getConfigHomologacaoVO().setSequencialLoteNFe(proximoLote);
                }
            }

            obj.setUrlDownloadCertificado(jsonObject.getString("certificado"));
            obj.setUrlDownloadLogotipo(jsonObject.getString("logomarca"));

        } else {
            throw new Exception("Erro ao consultar informações do módulo de notas.");
        }
    }

    private JSONObject gerarJSONLogotipoCertificado(String chave, ConfiguracaoNotaFiscalVO configVO) throws JSONException {
        JSONObject jsonObject = new JSONObject();

        jsonObject.put("empresaID", configVO.getIdEnotas());
        jsonObject.put("chave", chave);
        jsonObject.put("config", configVO.getCodigo());

        jsonObject.put("urlLogo", Uteis.getPaintFotoDaNuvem(configVO.getChaveLogotipo()));
        jsonObject.put("formatoLogo", configVO.getFormatoLogotipo());

        jsonObject.put("urlCert", Uteis.getPaintFotoDaNuvem(configVO.getChaveCertificado()));
        jsonObject.put("formatoCert", configVO.getFormatoCertificado());
        jsonObject.put("senhaCert", configVO.getSenhaCertificado());
        return jsonObject;
    }

    public String enviarCertificado(String chave, ConfiguracaoNotaFiscalVO configVO) {
        try {
            if (UteisValidacao.emptyString(configVO.getIdEnotas())) {
                throw new Exception("IdEnotas não informado.");
            }
            if (UteisValidacao.emptyString(configVO.getChaveCertificado())) {
                throw new Exception("Certificado não informado.");
            }

            JSONObject jsonObject = gerarJSONLogotipoCertificado(chave, configVO);
            String retorno = executaRequisicaoNotaFiscal(ENVIA_CERTIFICADO, jsonObject.toString());
            JSONObject jsonRetorno = new JSONObject(retorno);
            if (jsonRetorno.has("return")) {
                return "Enviar certificado: " + jsonRetorno.getString("return");
            } else {
                throw new Exception(jsonRetorno.getString("erro"));
            }
        } catch (Exception ex) {
            return "ERRO:" + ex.getMessage();
        }
    }

    public String enviarLogotipo(String chave, ConfiguracaoNotaFiscalVO configVO) {
        try {
            if (UteisValidacao.emptyString(configVO.getIdEnotas())) {
                throw new Exception("IdEnotas não informado.");
            }
            if (UteisValidacao.emptyString(configVO.getChaveLogotipo())) {
                throw new Exception("Logotipo não informado.");
            }

            JSONObject jsonObject = gerarJSONLogotipoCertificado(chave, configVO);
            String retorno = executaRequisicaoNotaFiscal(ENVIA_LOGOTIPO, jsonObject.toString());
            JSONObject jsonRetorno = new JSONObject(retorno);
            if (jsonRetorno.has("return")) {
                return "Enviar logotipo: " + jsonRetorno.getString("return");
            } else {
                throw new Exception(jsonRetorno.getString("erro"));
            }
        } catch (Exception ex) {
            return "ERRO:" + ex.getMessage();
        }
    }

    public boolean enviarNotaFiscalFamilia(NotaFiscalVO obj) {
        try {
            String retorno = executaRequisicaoNotaFiscal(ENVIA_NOTA, obj.getJsonEnvio(), true);
            JSONObject jsonObject = new JSONObject(retorno);
            if (!(jsonObject.has("return") && jsonObject.getString("return").toLowerCase().equals("ok"))) {
                return false;
            }
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public void enviarNotaFiscalNFSe(NFSeEmitidaVO obj) {
        try {
            String retorno = executaRequisicaoNotaFiscal(ENVIA_NOTA, obj.getJsonEnviar(), true);
            JSONObject jsonObject = new JSONObject(retorno);
            if (!(jsonObject.has("return") && jsonObject.getString("return").toLowerCase().equals("ok"))) {
                obj.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.GERADA);
                Logger.getLogger(NotaFiscal.class.getName()).log(Level.SEVERE,
                        "#### NOTA FISCAL =" + obj.getNotaFiscalVO().getCodigo() +
                                "; NFSE EMITIDA:" + obj.getCodigo() +
                                "; SITUAÇÃO: " + obj.getSituacaoNotaFiscal().getDescricao() +
                                "; STATUS: " + obj.getNotaFiscalVO().getStatusNota() +
                                "; LOG enviarNotaFiscalNFSe, SEM ERRO NO RETORNO REQUISIÇÃO MS NOTAS, MAS SEM RETURN OK.");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            obj.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.GERADA);
            Logger.getLogger(NotaFiscal.class.getName()).log(Level.SEVERE,
                    "#### NOTA FISCAL =" + obj.getNotaFiscalVO().getCodigo() +
                            "; NFSE EMITIDA:" + obj.getCodigo() +
                            "; SITUAÇÃO: " + obj.getSituacaoNotaFiscal().getDescricao() +
                            "; STATUS: " + obj.getNotaFiscalVO().getStatusNota() +
                            "; LOG enviarNotaFiscalNFSe, ERRO NO RETORNO REQUISIÇÃO MS NOTAS.");
        }
    }

    public void enviarNotaFiscalNFCe(NotaFiscalConsumidorEletronicaVO obj) {
        try {
            String retorno = executaRequisicaoNotaFiscal(ENVIA_NOTA, obj.getJsonEnviar(), true);
            JSONObject jsonObject = new JSONObject(retorno);
            if (!(jsonObject.has("return") && jsonObject.getString("return").toLowerCase().equals("ok"))) {
                obj.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.GERADA);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            obj.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.GERADA);
        }
    }

    private static String executaRequisicaoNotaFiscal(String url, String json) {
        return executaRequisicaoNotaFiscal(url, json, false);
    }

    private static String executaRequisicaoNotaFiscal(String url, String json, boolean comURLAplicacao) {
        try {
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-type", "application/json");
            if (comURLAplicacao) {
                headers.put("urlAplicacaoEnvio", Uteis.getUrlAplicacao());
            }
            return executeHttpRequest(url, json, headers, ExecuteRequestHttpService.METODO_POST, "UTF-8");
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO: " + ex.getMessage();
        }
    }

    public static String executeHttpRequest(String urlRequest, String corpo, Map<String, String> headers, String metodo, String encode) throws IOException {
        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for (String keyHeader : headers.keySet()) {
            conn.setRequestProperty(keyHeader, headers.get(keyHeader));
        }
        conn.setRequestMethod(metodo);
        if (corpo != null) {
            conn.setDoOutput(true);
            OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream(), Charset.forName(encode));
            wr.write(corpo);
            wr.flush();
            wr.close();
        }
        // Pega a Resposta
        InputStream in = null;
        try {
            in = conn.getInputStream();
        } catch (IOException e) {
            if (conn.getResponseCode() != 200) {
                in = conn.getErrorStream();
            }
        }

        InputStreamReader inputStreamReader = new InputStreamReader(in, Charset.forName(encode));
        if ("gzip".equals(conn.getContentEncoding())) {
            if (conn.getResponseCode() == 200 || conn.getResponseCode() == 201 || conn.getResponseCode() == 202) {
                inputStreamReader = new InputStreamReader(new GZIPInputStream(conn.getInputStream()));
            } else {
                inputStreamReader = new InputStreamReader(new GZIPInputStream(conn.getErrorStream()));
            }
        }

        BufferedReader rd = new BufferedReader(inputStreamReader);
        String line;
        StringBuilder resposta = new StringBuilder();
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta.append(line);
        }
        rd.close();
        return resposta.toString();
    }

    public InfoCidadeEnotasTO consultaInfoCidade(ConfiguracaoNotaFiscalVO obj) throws Exception {

        if (UteisValidacao.emptyNumber(obj.getCidadeVO().getCodigo())) {
            throw new Exception("Selecione uma cidade na aba Empresa.");
        }

        if (UteisValidacao.emptyString(obj.getCidadeVO().getCodigoIBGE())) {
            throw new Exception("A cidade selecionada está sem o código do IBGE preenchido.");
        }

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type", "application/json");
        headers.put("codigoIBGECidade", obj.getCidadeVO().getCodigoIBGE());

        String retorno = executeHttpRequest(INFO_CIDADE, null, headers, ExecuteRequestHttpService.METODO_GET, "UTF-8");

        JSONObject retornoJson = new JSONObject(retorno);

        if(retornoJson.has("erro")){
            JSONArray retornoArray = new JSONArray(retornoJson.getString("erro"));
            retornoJson = (JSONObject) retornoArray.get(0);
            throw new Exception(retornoJson.getString("mensagem"));
        }
        return new InfoCidadeEnotasTO(new JSONObject(retornoJson.getString("return")));
    }

    public List<InfoServicoEnotasTO> consultaInfoServico(ConfiguracaoNotaFiscalVO obj) throws Exception {
        List<InfoServicoEnotasTO> lista = new ArrayList<>();

        if (UteisValidacao.emptyString(obj.getCidadeVO().getNome())) {
            throw new Exception("Selecione uma cidade na aba Empresa.");
        }

        if (UteisValidacao.emptyString(obj.getCodListaServico())) {
            throw new Exception("Informe um código de lista de serviço.");
        }

        String uf = obj.getCidadeVO().getEstado().getSigla();
        String nome = obj.getCidadeVO().getNomeSemAcento();
        String descricaoServico = obj.getCodListaServico();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type", "application/json");
        headers.put("uf", uf);
        headers.put("nome", nome);
        headers.put("descricaoServico", descricaoServico);

        String retorno = executeHttpRequest(INFO_SERVICO, null, headers, ExecuteRequestHttpService.METODO_GET, "UTF-8");
        JSONObject jsonRetorno = new JSONObject(retorno);
        JSONObject jsonReturn = new JSONObject(jsonRetorno.getString("return"));

        JSONArray jsonArray = new JSONArray(jsonReturn.get("data").toString());
        for (int e = 0; e < jsonArray.length(); e++) {
            try {
                JSONObject json = jsonArray.getJSONObject(e);
                lista.add(new InfoServicoEnotasTO(json));
            } catch (Exception ignored) {
            }
        }
        return lista;
    }

    public void enviarOperacao(NotaFiscalOperacaoVO obj) throws JSONException {
        try {
            String retorno = executaRequisicaoNotaFiscal(ENVIA_OPERACAO, obj.gerarJsonEnvio().toString());
            Uteis.logar(true, null, retorno);
            JSONObject jsonObject = new JSONObject(retorno);
            if (!(jsonObject.has("return") && jsonObject.getString("return").toLowerCase().equals("ok"))) {
                obj.setStatus(StatusOperacaoNotaFiscalEnum.GERADA);
            }
        } catch (Exception ex) {
            obj.setStatus(StatusOperacaoNotaFiscalEnum.GERADA);
        }
    }

    public NotaEnotasTO consultarNota(NotaFiscalVO obj) throws Exception {
        String retorno = executaRequisicaoNotaFiscal(CONSULTAR_NOTA, obj.gerarJSONConsultaNota().toString());
        JSONObject jsonObject = new JSONObject(retorno);
        NotaEnotasTO notaEnotasTO = new NotaEnotasTO();
        if (jsonObject.has("erro")) {
            NotaEnotasTO notaEnotasMicro = consultarNotaMSNotaFiscal(obj);
            if (notaEnotasMicro != null) {
                notaEnotasTO = notaEnotasMicro;
            } else {
                throw new Exception(jsonObject.toString());
            }
        } else {
            notaEnotasTO = new NotaEnotasTO(obj.getTipo(), new JSONObject(jsonObject.getString("return")));
        }

        if (UteisValidacao.emptyString(notaEnotasTO.getIdReferencia())) {
            notaEnotasTO.setIdReferencia(obj.getIdReferencia());
        }
        return notaEnotasTO;
    }

    public NotaEnotasTO consultarNotaMSNotaFiscal(NotaFiscalVO obj) throws Exception {
        JSONObject jsonNota = new JSONObject(obj.getJsonNota());

        String chave = jsonNota.getString("chave");
        if (UteisValidacao.emptyString(chave)){
            chave = DAO.resolveKeyFromConnection(this.con);
        }
        if (UteisValidacao.emptyString(chave)) {
            return null;
        }

        Map<String, String> params = new HashMap<String, String>();
        params.put("chave", chave);
        params.put("notafiscal", obj.getCodigo().toString());
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(CONSULTAR_NOTA_NOTA, headers, params, null, MetodoHttpEnum.GET);
        service = null;
        if (respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            JSONObject jsonResp = new JSONObject(respostaHttpDTO.getResponse()).getJSONObject("return");
            if (!jsonResp.getString("status").equalsIgnoreCase("erro")) {
                return null;
            }

            NotaEnotasTO notaEnotasTO = new NotaEnotasTO();
            notaEnotasTO.setTipo(obj.getTipo().getDescricao());
            notaEnotasTO.setAmbienteEmissao(obj.getAmbienteEmissao());
            notaEnotasTO.setStatus(jsonResp.getString("status"));
            notaEnotasTO.setJson(jsonResp.toString());
            notaEnotasTO.setIdExterno("NOTA_NAO_ENVIADA");

            String motivoStatus = jsonResp.optString("retorno_enotas");
            try {
                JSONArray jsonArray = new JSONArray(jsonResp.getString("retorno_enotas"));
                motivoStatus = jsonArray.getJSONObject(0).getString("mensagem");
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            notaEnotasTO.setMotivoStatus(motivoStatus);
            return notaEnotasTO;
        }
        return null;
    }

    public InfoEmpresaEnotasTO consultarSituacaoEmpresaEnotas(ConfiguracaoNotaFiscalVO configVO) throws Exception {
        try {
            if (UteisValidacao.emptyString(configVO.getIdEnotas())) {
                throw new Exception("IdEnotas não informado.");
            }

            JSONObject jsonEnvio = new JSONObject();
            jsonEnvio.put("idEmpresaEnotas", configVO.getIdEnotas());
            jsonEnvio.put("tipoNotaFiscal", configVO.getTipoNotaFiscal().getCodigo());
            String retorno = executaRequisicaoNotaFiscal(CONSULTAR_EMPRESA, jsonEnvio.toString());

            JSONObject jsonRetorno = new JSONObject(retorno);
            if (jsonRetorno.has("erro")) {
                throw new Exception(jsonRetorno.toString());
            } else if (jsonRetorno.has("return")) {
                return new InfoEmpresaEnotasTO(new JSONObject(jsonRetorno.getString("return")));
            }
            return new InfoEmpresaEnotasTO();
        } catch (Exception ex) {
            throw new Exception("ERRO:" + ex.getMessage());
        }
    }

    public String obterLinkDownloadSetupSAT(ConfiguracaoNotaFiscalVO configVO) throws Exception {
        try {
            if (UteisValidacao.emptyString(configVO.getIdEnotas())) {
                throw new Exception("IdEnotas não informado.");
            }

            String retorno = executaRequisicaoNotaFiscal(OBTER_SETUP_SAT, configVO.getIdEnotas());

            JSONObject jsonRetorno = new JSONObject(retorno);
            if (jsonRetorno.has("erro")) {
                throw new Exception(jsonRetorno.toString());
            } else if (jsonRetorno.has("return")) {
                return jsonRetorno.optString("return");
            } else {
                throw new Exception(jsonRetorno.toString());
            }
        } catch (Exception ex) {
            throw new Exception("ERRO:" + ex.getMessage());
        }
    }

    public String habilitarDesabilitarEmpresa(boolean isEnotas, String idEnotas) throws Exception {
        try {
            if(UteisValidacao.emptyString(idEnotas)) {
                throw new Exception("IdEnotas não informado.");
            }

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-type", "application/json");
            JSONObject body = new JSONObject();
            body.put("isEnotas", isEnotas);
            body.put("idEnotas", idEnotas);

            String  retorno = executeHttpRequest(HABILITAR_DESABILITAR_EMPRESA, body.toString(), headers, ExecuteRequestHttpService.METODO_POST, "UTF-8");

            JSONObject jsonRetorno = new JSONObject(retorno);
            if (jsonRetorno.has("erro")) {
                throw new Exception(jsonRetorno.optString("erro"));
            } else if (jsonRetorno.has("return")) {
                return jsonRetorno.optString("return");
            } else {
                throw new Exception(jsonRetorno.toString());
            }
        } catch (Exception e) {
            throw new Exception("ERRO integração eNotas:" + e.getMessage());
        }
    }

    public InutilizacaoNotaFiscalTO consultarInutilizacao(NotaFiscalOperacaoVO obj) throws Exception {
        String retorno = executaRequisicaoNotaFiscal(CONSULTAR_INUTILIZACAO, obj.gerarJsonEnvio().toString());

        JSONObject jsonObject = new JSONObject(retorno);
        if(jsonObject.has("erro")) {
            throw new Exception(jsonObject.toString());
        }

        InutilizacaoNotaFiscalTO inutilizacaoNotaFiscalTO = new InutilizacaoNotaFiscalTO(new JSONObject(jsonObject.getString("return")));
        if(UteisValidacao.emptyString(inutilizacaoNotaFiscalTO.getId())) {
            inutilizacaoNotaFiscalTO.setId(obj.getChave() + "_INU_" + obj.getCodigo());
        }

        return inutilizacaoNotaFiscalTO;
    }
}

package br.com.pactosolucoes.integracao.enotas.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.utilitarias.Uteis;

public class EnotasNFCePedidoJSON extends SuperJSON {

    @JsonProperty("presencaConsumidor")
    private String presencaConsumidor;
    @JsonProperty("pagamento")
    private EnotasNFCePagamentoJSON pagamento;

    public EnotasNFCePedidoJSON(NotaTO notaTO) {
        this.presencaConsumidor = "OperacaoPresencial";
//        this.nome = notaTO.getCliRazaoSocial();
//        this.cpfCnpj = Uteis.tirarCaracteres(notaTO.getCliCPFCNPJ(), true);
    }

}

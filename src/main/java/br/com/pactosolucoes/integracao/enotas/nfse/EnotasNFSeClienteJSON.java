package br.com.pactosolucoes.integracao.enotas.nfse;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.utilitarias.Uteis;

public class EnotasNFSeClienteJSON extends SuperJSON {

    private String tipoPessoa;
    private String nome;
    private String email;
    private String cpfCnpj;
    private String inscricaoMunicipal;
    private String inscricaoEstadual;
    private String telefone;
    private EnotasNFSeClienteEnderecoJSON endereco;


    public EnotasNFSeClienteJSON(NotaTO notaTO) {
        this.tipoPessoa = notaTO.getCliTipo();
        this.nome = notaTO.getCliRazaoSocial();
        this.email = notaTO.getCliEmail();
        this.cpfCnpj = Uteis.tirarCaracteres(notaTO.getCliCPFCNPJ(), true);
        this.inscricaoMunicipal = Uteis.tirarCaracteres(notaTO.getCliInscMunicipal(), true);
        this.inscricaoEstadual = Uteis.tirarCaracteres(notaTO.getCliInscEstadual(), true);
        this.telefone = Uteis.tirarCaracteres(notaTO.getCliTelefone(), true);
        this.endereco = new EnotasNFSeClienteEnderecoJSON(notaTO);
    }

    public String getTipoPessoa() {
        return tipoPessoa;
    }

    public void setTipoPessoa(String tipoPessoa) {
        this.tipoPessoa = tipoPessoa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public String getInscricaoMunicipal() {
        return inscricaoMunicipal;
    }

    public void setInscricaoMunicipal(String inscricaoMunicipal) {
        this.inscricaoMunicipal = inscricaoMunicipal;
    }

    public String getInscricaoEstadual() {
        return inscricaoEstadual;
    }

    public void setInscricaoEstadual(String inscricaoEstadual) {
        this.inscricaoEstadual = inscricaoEstadual;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public EnotasNFSeClienteEnderecoJSON getEndereco() {
        return endereco;
    }

    public void setEndereco(EnotasNFSeClienteEnderecoJSON endereco) {
        this.endereco = endereco;
    }

}

package br.com.pactosolucoes.integracao.enotas.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaProdutoTO;

public class EnotasNFCeProdutoPercentualTributosFederaisJSON extends SuperJSON {

    @JsonProperty("detalhado")
    private EnotasNFCeProdutoPercentualDetalhadoJSON detalhado;
    @JsonProperty("fonte")
    private String fonte;


    public EnotasNFCeProdutoPercentualTributosFederaisJSON(NotaProdutoTO notaProdutoTO) {
//        this.cfop = notaProdutoTO.getCfop();
        this.fonte = "IBPT x5675s";
    }
}

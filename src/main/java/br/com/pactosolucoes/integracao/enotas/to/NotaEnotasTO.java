package br.com.pactosolucoes.integracao.enotas.to;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.notaFiscal.StatusEnotasEnum;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class NotaEnotasTO extends SuperTO {

    private String idExterno;
    private String idReferencia;
    private String status;
    private String tipo;
    private String motivoStatus;
    private String ambienteEmissao;
    private boolean enviadaPorEmail = false;
    private Date dataCriacao;
    private Date dataUltimaAlteracao;
    private Date dataCompetenciaRps;
    private Integer numeroRps;
    private String serieRps;
    private String numero;
    private String codigoVerificacao;
    private String chaveAcesso;
    private Date dataAutorizacao;
    private String json;

    public NotaEnotasTO() {

    }

    public NotaEnotasTO(TipoNotaFiscalEnum tipoNotaFiscalEnum, JSONObject jsonObject) {
        this.json = jsonObject.toString();
        this.idExterno = jsonObject.getString("id");
        this.idReferencia = jsonObject.optString("idExterno");
        this.status = jsonObject.getString("status");
        this.tipo = jsonObject.getString("tipo");
        this.ambienteEmissao = jsonObject.getString("ambienteEmissao");
        this.enviadaPorEmail = jsonObject.getBoolean("enviadaPorEmail");

        try {
            this.serieRps = jsonObject.getString("serieRps");
        } catch (Exception ignored){ }

        if(this.serieRps == null || this.serieRps.trim().isEmpty()){
            try {
                this.serieRps = jsonObject.getString("serie");
            } catch (Exception e){
                try {
                    this.serieRps = String.valueOf(jsonObject.getInt("serie"));
                } catch (Exception ignoredSerie){
                    Uteis.logar(null, "A nota não tem uma série válida "+ jsonObject);
                }
            }
        }

        try {
            this.numeroRps = jsonObject.getInt("numeroRps");
        } catch (Exception ignored){ }

        try {
            this.chaveAcesso = jsonObject.getString("chaveAcesso");
        } catch (Exception ignored){ }

        try {
            this.codigoVerificacao = jsonObject.getString("codigoVerificacao");
        } catch (Exception ignored){ }

        try {
            this.numero = jsonObject.getString("numero");
        } catch (Exception ignored){}

        if(this.numero == null || this.numero.isEmpty()){
            try {
                this.numero = String.valueOf(jsonObject.getInt("numero"));
            }catch (Exception ignoredIntNumberConverter){
                Uteis.logar(null, "A nota não contém um número válido "+ jsonObject);
            }
        }

        try {
            this.motivoStatus = jsonObject.getString("motivoStatus");
        } catch (Exception ignored){ }

        try {
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            this.dataCriacao = sdf.parse(jsonObject.getString("dataCriacao"));
        } catch (Exception ignored){ }

        try {
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            this.dataUltimaAlteracao = sdf.parse(jsonObject.getString("dataUltimaAlteracao"));
        } catch (Exception ignored){ }

        try {
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            this.dataCompetenciaRps = sdf.parse(jsonObject.getString("dataCompetenciaRps"));
        } catch (Exception ignored){ }

        try {
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            this.dataAutorizacao = sdf.parse(jsonObject.getString("dataAutorizacao"));
        } catch (Exception ignored){ }
    }

    public String getIdExterno() {
        if (idExterno == null) {
            idExterno = "";
        }
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }

    public String getIdReferencia() {
        if (idReferencia == null) {
            idReferencia = "";
        }
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public String getStatus() {
        if (status == null) {
            status = "";
        }
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public StatusEnotasEnum getStatusEnotasEnum() {
        return StatusEnotasEnum.obterPorDescricaoEnotas(getStatus());
    }

    public String getTipo() {
        if (tipo == null) {
            tipo = "";
        }
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getMotivoStatus() {
        if (motivoStatus == null) {
            motivoStatus = "";
        }
        return motivoStatus.replaceAll("\r\n", "<br/>");
    }

    public void setMotivoStatus(String motivoStatus) {
        this.motivoStatus = motivoStatus;
    }

    public String getAmbienteEmissao() {
        if (ambienteEmissao == null) {
            ambienteEmissao = "";
        }
        return ambienteEmissao;
    }

    public void setAmbienteEmissao(String ambienteEmissao) {
        this.ambienteEmissao = ambienteEmissao;
    }

    public boolean isEnviadaPorEmail() {
        return enviadaPorEmail;
    }

    public void setEnviadaPorEmail(boolean enviadaPorEmail) {
        this.enviadaPorEmail = enviadaPorEmail;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public Date getDataUltimaAlteracao() {
        return dataUltimaAlteracao;
    }

    public void setDataUltimaAlteracao(Date dataUltimaAlteracao) {
        this.dataUltimaAlteracao = dataUltimaAlteracao;
    }

    public Date getDataCompetenciaRps() {
        return dataCompetenciaRps;
    }

    public void setDataCompetenciaRps(Date dataCompetenciaRps) {
        this.dataCompetenciaRps = dataCompetenciaRps;
    }

    public Integer getNumeroRps() {
        return numeroRps;
    }

    public void setNumeroRps(Integer numeroRps) {
        this.numeroRps = numeroRps;
    }

    public String getSerieRps() {
        if (serieRps == null) {
            serieRps = "";
        }
        return serieRps;
    }

    public void setSerieRps(String serieRps) {
        this.serieRps = serieRps;
    }

    public String getEnviadaPorEmailApresentar() {
        return isEnviadaPorEmail() ? "SIM" : "NÃO";
    }

    public String getDataCriacaoApresentar() {
        if (getDataCriacao() == null) {
            return "";
        }
        return Uteis.getDataComHora(getDataCriacao());
    }

    public String getDataUltimaAlteracaoApresentar() {
        if (getDataUltimaAlteracao() == null) {
            return "";
        }
        return Uteis.getDataComHora(getDataUltimaAlteracao());
    }

    public String getDataCompetenciaRpsApresentar() {
        if (getDataCompetenciaRps() == null) {
             return "";
        }
        return Uteis.getDataComHora(getDataCompetenciaRps());
    }

    public String getDataAutorizacaoApresentar() {
        if (getDataAutorizacao() == null) {
            return "";
        }
        return Uteis.getDataComHora(getDataAutorizacao());
    }

    public Date getDataAutorizacao() {
        return dataAutorizacao;
    }

    public void setDataAutorizacao(Date dataAutorizacao) {
        this.dataAutorizacao = dataAutorizacao;
    }

    public String getNumero() {
        if (numero == null) {
            numero = "";
        }
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getCodigoVerificacao() {
        if (codigoVerificacao == null) {
            codigoVerificacao = "";
        }
        return codigoVerificacao;
    }

    public void setCodigoVerificacao(String codigoVerificacao) {
        this.codigoVerificacao = codigoVerificacao;
    }

    public String getChaveAcesso() {
        if (chaveAcesso == null) {
            chaveAcesso = "";
        }
        return chaveAcesso;
    }

    public void setChaveAcesso(String chaveAcesso) {
        this.chaveAcesso = chaveAcesso;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }
}

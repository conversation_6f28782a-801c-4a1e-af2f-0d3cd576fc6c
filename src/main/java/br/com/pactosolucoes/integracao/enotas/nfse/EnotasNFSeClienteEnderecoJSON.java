package br.com.pactosolucoes.integracao.enotas.nfse;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class EnotasNFSeClienteEnderecoJSON extends SuperJSON {

    private String logradouro;
    private String numero;
    private String complemento;
    private String bairro;
    private String cep;
    private String uf;
    private String cidade;
    private String pais;


    public EnotasNFSeClienteEnderecoJSON(NotaTO notaTO) {
        this.logradouro = notaTO.getCliEndLogradouro();
        this.numero = UteisValidacao.emptyString(notaTO.getCliEndNumero()) ? "0" : notaTO.getCliEndNumero();
        this.complemento = notaTO.getCliEndComplemento();
        this.bairro =  StringUtilities.limitarTamanho(notaTO.getCliEndBairro() , 30);
        this.cep = Uteis.tirarCaracteres(notaTO.getCliEndCEP(), true);
        this.uf = notaTO.getCliEndUFEstado();
        this.cidade = notaTO.getCliEndCidade();
        this.pais = notaTO.getCliEndPais();
    }

    public String getLogradouro() {
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }
}

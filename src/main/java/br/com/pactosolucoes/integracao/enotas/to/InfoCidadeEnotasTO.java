package br.com.pactosolucoes.integracao.enotas.to;

import negocio.comuns.arquitetura.SuperTO;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class InfoCidadeEnotasTO extends SuperTO {

    private String json;
    private Integer tipoAutenticacao;
    private Integer assinaturaDigital;
    private String helpTipoAutenticacao;
    private Integer campoLoginProvedor;
    private boolean suportaCancelamento = false;
    private boolean usaRegimeEspecialTributacao = false;
    private boolean usaCodigoServicoMunicipal = false;
    private boolean usaDescricaoServico = false;
    private boolean usaCNAE = false;
    private boolean usaItemListaServico = false;
    private boolean usaAEDF = false;
    private String helpInscricaoMunicipal;
    private String helpRegimeEspecialTributacao;
    private String helpCodigoServicoMunicipal;
    private String helpDescricaoServico;
    private String helpCNAE;
    private String helpItemListaServico;
    private boolean suportaEmissaoNFeSemCliente;
    private boolean suportaEmissaoNFeClienteSemCpf;
    private boolean suportaEmissaoNFeClienteSemEndereco;
    private boolean suportaCancelamentoNFeSemCliente;
    private boolean suportaCancelamentoNFeClienteSemCpf;
    private InfoCidadeTipoAutenticacaoTO helpTipoAutenticacaoTO;
    private String regimesEspecialTributacao;
    private List<InfoCidadeRegimeTributacaoTO> listaRegimesEspecialTributacao;

    public InfoCidadeEnotasTO() {

    }

    public InfoCidadeEnotasTO(JSONObject jsonObject) {
        this.json = jsonObject.toString();

        this.suportaCancelamento = jsonObject.getBoolean("suportaCancelamento");
        this.usaRegimeEspecialTributacao = jsonObject.getBoolean("usaRegimeEspecialTributacao");
        this.usaCodigoServicoMunicipal = jsonObject.getBoolean("usaCodigoServicoMunicipal");
        this.usaDescricaoServico = jsonObject.getBoolean("usaDescricaoServico");
        this.usaCNAE = jsonObject.getBoolean("usaCNAE");
        this.usaItemListaServico = jsonObject.getBoolean("usaItemListaServico");
        this.suportaEmissaoNFeSemCliente = jsonObject.getBoolean("suportaEmissaoNFeSemCliente");
        this.suportaEmissaoNFeClienteSemCpf = jsonObject.getBoolean("suportaEmissaoNFeClienteSemCpf");
        this.suportaEmissaoNFeClienteSemEndereco = jsonObject.getBoolean("suportaEmissaoNFeClienteSemEndereco");
        this.suportaCancelamentoNFeSemCliente = jsonObject.getBoolean("suportaEmissaoNFeClienteSemEndereco");
        this.suportaCancelamentoNFeClienteSemCpf = jsonObject.getBoolean("suportaEmissaoNFeClienteSemEndereco");

        try {
            this.helpTipoAutenticacao = jsonObject.getString("helpTipoAutenticacao");
        } catch (Exception ignored) {
        }

        try {
            this.helpTipoAutenticacaoTO = new InfoCidadeTipoAutenticacaoTO(jsonObject.getJSONObject("helpTipoAutenticacao"));
        } catch (Exception ignored) {
        }

        try {
            this.regimesEspecialTributacao = jsonObject.getString("regimesEspecialTributacao");
        } catch (Exception ignored) {
        }

        try {
            this.listaRegimesEspecialTributacao = new ArrayList<>();

            JSONArray lista = new JSONArray(jsonObject.get("regimesEspecialTributacao").toString());
            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                this.listaRegimesEspecialTributacao.add(new InfoCidadeRegimeTributacaoTO(obj));
            }
        } catch (Exception ignored) {
        }


        try {
            this.tipoAutenticacao = jsonObject.getInt("tipoAutenticacao");
        } catch (Exception ignored) {
        }

        try {
            this.assinaturaDigital = jsonObject.getInt("assinaturaDigital");
        } catch (Exception ignored) {
        }

        try {
            this.campoLoginProvedor = jsonObject.getInt("campoLoginProvedor");
        } catch (Exception ignored) {
        }

        try {
            this.helpInscricaoMunicipal = jsonObject.getString("helpInscricaoMunicipal");
        } catch (Exception ignored) {
        }

        try {
            this.helpRegimeEspecialTributacao = jsonObject.getString("helpRegimeEspecialTributacao");
        } catch (Exception ignored) {
        }

        try {
            this.helpCodigoServicoMunicipal = jsonObject.getString("helpCodigoServicoMunicipal");
        } catch (Exception ignored) {
        }

        try {
            this.helpDescricaoServico = jsonObject.getString("codigoVerificacao");
        } catch (Exception ignored) {
        }

        try {
            this.helpCNAE = jsonObject.getString("helpCNAE");
        } catch (Exception ignored) {
        }

        try {
            this.helpItemListaServico = jsonObject.getString("helpItemListaServico");
        } catch (Exception ignored) {
        }

    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getTipoAutenticacaoApresentar() {
         /*
    Nenhuma = 0,
    Certificado = 1,
    UsuarioESenha = 2,
    Token = 3,
    FraseSecretaESenha = 4
    */
        if (getTipoAutenticacao() == null) {
            return "";
        } else if (getTipoAutenticacao().equals(1)) {
            return "Certificado";
        } else if (getTipoAutenticacao().equals(2)) {
            return "UsuarioESenha";
        } else if (getTipoAutenticacao().equals(3)) {
            return "Token";
        } else if (getTipoAutenticacao().equals(4)) {
            return "FraseSecretaESenha";
        } else {
            return "";
        }
    }


    public Integer getTipoAutenticacao() {
        return tipoAutenticacao;
    }

    public void setTipoAutenticacao(Integer tipoAutenticacao) {
        this.tipoAutenticacao = tipoAutenticacao;
    }

    public Integer getAssinaturaDigital() {
        return assinaturaDigital;
    }

    public String getAssinaturaDigitalApresentar() {
 /*
    NaoUtiliza = 0,
    Opcional = 1,
    Obrigatorio = 2
    */
        if (getAssinaturaDigital() == null) {
            return "";
        } else if (getTipoAutenticacao().equals(0)) {
            return "NaoUtiliza";
        } else if (getTipoAutenticacao().equals(1)) {
            return "Opcional";
        } else if (getTipoAutenticacao().equals(2)) {
            return "Obrigatorio";
        } else {
            return "";
        }
    }

    public void setAssinaturaDigital(Integer assinaturaDigital) {
        this.assinaturaDigital = assinaturaDigital;
    }

    public String getHelpTipoAutenticacao() {
        if (helpTipoAutenticacao == null) {
            helpTipoAutenticacao = "";
        }
        return helpTipoAutenticacao;
    }

    public void setHelpTipoAutenticacao(String helpTipoAutenticacao) {
        this.helpTipoAutenticacao = helpTipoAutenticacao;
    }

    public Integer getCampoLoginProvedor() {
        return campoLoginProvedor;
    }

    public void setCampoLoginProvedor(Integer campoLoginProvedor) {
        this.campoLoginProvedor = campoLoginProvedor;
    }

    public String getSuportaCancelamentoApresentar() {
        return isSuportaCancelamento() ? "SIM" : "NÃO";
    }

    public boolean isSuportaCancelamento() {
        return suportaCancelamento;
    }

    public void setSuportaCancelamento(boolean suportaCancelamento) {
        this.suportaCancelamento = suportaCancelamento;
    }

    public String getUsaRegimeEspecialTributacaoApresentar() {
        return isUsaRegimeEspecialTributacao() ? "SIM" : "NÃO";
    }

    public boolean isUsaRegimeEspecialTributacao() {
        return usaRegimeEspecialTributacao;
    }

    public void setUsaRegimeEspecialTributacao(boolean usaRegimeEspecialTributacao) {
        this.usaRegimeEspecialTributacao = usaRegimeEspecialTributacao;
    }

    public String getUsaCodigoServicoMunicipalApresentar() {
        return isUsaCodigoServicoMunicipal() ? "SIM" : "NÃO";
    }

    public boolean isUsaCodigoServicoMunicipal() {
        return usaCodigoServicoMunicipal;
    }

    public void setUsaCodigoServicoMunicipal(boolean usaCodigoServicoMunicipal) {
        this.usaCodigoServicoMunicipal = usaCodigoServicoMunicipal;
    }

    public String getUsaDescricaoServicoApresentar() {
        return isUsaDescricaoServico() ? "SIM" : "NÃO";
    }

    public boolean isUsaDescricaoServico() {
        return usaDescricaoServico;
    }

    public void setUsaDescricaoServico(boolean usaDescricaoServico) {
        this.usaDescricaoServico = usaDescricaoServico;
    }

    public String getUsaCNAEApresentar() {
        return isUsaCNAE() ? "SIM" : "NÃO";
    }

    public boolean isUsaCNAE() {
        return usaCNAE;
    }

    public void setUsaCNAE(boolean usaCNAE) {
        this.usaCNAE = usaCNAE;
    }

    public String getUsaItemListaServicoApresentar() {
        return isUsaItemListaServico() ? "SIM" : "NÃO";
    }

    public boolean isUsaItemListaServico() {
        return usaItemListaServico;
    }

    public void setUsaItemListaServico(boolean usaItemListaServico) {
        this.usaItemListaServico = usaItemListaServico;
    }

    public String getHelpInscricaoMunicipal() {
        if (helpInscricaoMunicipal == null) {
            helpInscricaoMunicipal = "";
        }
        return helpInscricaoMunicipal;
    }

    public void setHelpInscricaoMunicipal(String helpInscricaoMunicipal) {
        this.helpInscricaoMunicipal = helpInscricaoMunicipal;
    }

    public String getHelpRegimeEspecialTributacao() {
        if (helpRegimeEspecialTributacao == null) {
            helpRegimeEspecialTributacao = "";
        }
        return helpRegimeEspecialTributacao;
    }

    public void setHelpRegimeEspecialTributacao(String helpRegimeEspecialTributacao) {
        this.helpRegimeEspecialTributacao = helpRegimeEspecialTributacao;
    }

    public String getHelpCodigoServicoMunicipal() {
        if (helpCodigoServicoMunicipal == null) {
            helpCodigoServicoMunicipal = "";
        }
        return helpCodigoServicoMunicipal;
    }

    public void setHelpCodigoServicoMunicipal(String helpCodigoServicoMunicipal) {
        this.helpCodigoServicoMunicipal = helpCodigoServicoMunicipal;
    }

    public String getHelpDescricaoServico() {
        if (helpDescricaoServico == null) {
            helpDescricaoServico = "";
        }
        return helpDescricaoServico;
    }

    public void setHelpDescricaoServico(String helpDescricaoServico) {
        this.helpDescricaoServico = helpDescricaoServico;
    }

    public String getHelpCNAE() {
        if (helpCNAE == null) {
            helpCNAE = "";
        }
        return helpCNAE;
    }

    public void setHelpCNAE(String helpCNAE) {
        this.helpCNAE = helpCNAE;
    }

    public String getHelpItemListaServico() {
        if (helpItemListaServico == null) {
            helpItemListaServico = "";
        }
        return helpItemListaServico;
    }

    public void setHelpItemListaServico(String helpItemListaServico) {
        this.helpItemListaServico = helpItemListaServico;
    }

    public String getSuportaEmissaoNFeSemClienteApresentar() {
        return isSuportaEmissaoNFeSemCliente() ? "SIM" : "NÃO";
    }

    public boolean isSuportaEmissaoNFeSemCliente() {
        return suportaEmissaoNFeSemCliente;
    }

    public void setSuportaEmissaoNFeSemCliente(boolean suportaEmissaoNFeSemCliente) {
        this.suportaEmissaoNFeSemCliente = suportaEmissaoNFeSemCliente;
    }

    public String getSuportaEmissaoNFeClienteSemCpfApresentar() {
        return isSuportaEmissaoNFeClienteSemCpf() ? "SIM" : "NÃO";
    }

    public boolean isSuportaEmissaoNFeClienteSemCpf() {
        return suportaEmissaoNFeClienteSemCpf;
    }

    public void setSuportaEmissaoNFeClienteSemCpf(boolean suportaEmissaoNFeClienteSemCpf) {
        this.suportaEmissaoNFeClienteSemCpf = suportaEmissaoNFeClienteSemCpf;
    }

    public String getSuportaEmissaoNFeClienteSemEnderecoApresentar() {
        return isSuportaEmissaoNFeClienteSemEndereco() ? "SIM" : "NÃO";
    }

    public boolean isSuportaEmissaoNFeClienteSemEndereco() {
        return suportaEmissaoNFeClienteSemEndereco;
    }

    public void setSuportaEmissaoNFeClienteSemEndereco(boolean suportaEmissaoNFeClienteSemEndereco) {
        this.suportaEmissaoNFeClienteSemEndereco = suportaEmissaoNFeClienteSemEndereco;
    }

    public String getSuportaCancelamentoNFeSemClienteApresentar() {
        return isSuportaCancelamentoNFeSemCliente() ? "SIM" : "NÃO";
    }

    public boolean isSuportaCancelamentoNFeSemCliente() {
        return suportaCancelamentoNFeSemCliente;
    }

    public void setSuportaCancelamentoNFeSemCliente(boolean suportaCancelamentoNFeSemCliente) {
        this.suportaCancelamentoNFeSemCliente = suportaCancelamentoNFeSemCliente;
    }

    public String getSuportaCancelamentoNFeClienteSemCpfApresentar() {
        return isSuportaCancelamentoNFeClienteSemCpf() ? "SIM" : "NÃO";
    }

    public boolean isSuportaCancelamentoNFeClienteSemCpf() {
        return suportaCancelamentoNFeClienteSemCpf;
    }

    public void setSuportaCancelamentoNFeClienteSemCpf(boolean suportaCancelamentoNFeClienteSemCpf) {
        this.suportaCancelamentoNFeClienteSemCpf = suportaCancelamentoNFeClienteSemCpf;
    }

    public String getRegimesEspecialTributacao() {
        if (regimesEspecialTributacao == null) {
            regimesEspecialTributacao = "";
        }
        return regimesEspecialTributacao;
    }

    public void setRegimesEspecialTributacao(String regimesEspecialTributacao) {
        this.regimesEspecialTributacao = regimesEspecialTributacao;
    }

    public List<InfoCidadeRegimeTributacaoTO> getListaRegimesEspecialTributacao() {
        if (listaRegimesEspecialTributacao == null) {
            listaRegimesEspecialTributacao =  new ArrayList<>();
        }
        return listaRegimesEspecialTributacao;
    }

    public void setListaRegimesEspecialTributacao(List<InfoCidadeRegimeTributacaoTO> listaRegimesEspecialTributacao) {
        this.listaRegimesEspecialTributacao = listaRegimesEspecialTributacao;
    }

    public InfoCidadeTipoAutenticacaoTO getHelpTipoAutenticacaoTO() {
        if (helpTipoAutenticacaoTO == null) {
            helpTipoAutenticacaoTO =  new InfoCidadeTipoAutenticacaoTO();
        }
        return helpTipoAutenticacaoTO;
    }

    public void setHelpTipoAutenticacaoTO(InfoCidadeTipoAutenticacaoTO helpTipoAutenticacaoTO) {
        this.helpTipoAutenticacaoTO = helpTipoAutenticacaoTO;
    }

    public boolean isUsaAEDF() {
        return usaAEDF;
    }

    public void setUsaAEDF(boolean usaAEDF) {
        this.usaAEDF = usaAEDF;
    }

    public String getUsaAEDFApresentar() {
        return isUsaAEDF() ? "SIM" : "NÃO";
    }
}

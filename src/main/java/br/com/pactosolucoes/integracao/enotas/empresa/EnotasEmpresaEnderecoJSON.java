package br.com.pactosolucoes.integracao.enotas.empresa;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class EnotasEmpresaEnderecoJSON extends SuperJSON {

    private String codigoIbgeUf;
    private String codigoIbgeCidade;
    private String pais;
    private String uf;
    private String cidade;
    private String logradouro;
    private String numero;
    private String complemento;
    private String bairro;
    private String cep;


    public EnotasEmpresaEnderecoJSON(ConfiguracaoNotaFiscalVO configVO) {
        this.codigoIbgeUf = configVO.getEstadoVO().getCodigoIBGE();
        this.codigoIbgeCidade = configVO.getCidadeVO().getCodigoIBGE();
        this.pais = configVO.getPaisVO().getNome();
        this.uf = configVO.getEstadoVO().getSigla();
        this.cidade = configVO.getCidadeVO().getNome();
        this.logradouro = configVO.getLogradouro();
        if (UteisValidacao.emptyString(configVO.getNumero())) {
            this.numero = "SN";
        } else {
            this.numero = configVO.getNumero();
        }
        this.complemento = configVO.getComplemento();
        this.bairro = configVO.getBairro();
        this.cep = Uteis.removerMascara(configVO.getCep());
    }

    public String getCodigoIbgeUf() {
        return codigoIbgeUf;
    }

    public void setCodigoIbgeUf(String codigoIbgeUf) {
        this.codigoIbgeUf = codigoIbgeUf;
    }

    public String getCodigoIbgeCidade() {
        return codigoIbgeCidade;
    }

    public void setCodigoIbgeCidade(String codigoIbgeCidade) {
        this.codigoIbgeCidade = codigoIbgeCidade;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getLogradouro() {
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }
}

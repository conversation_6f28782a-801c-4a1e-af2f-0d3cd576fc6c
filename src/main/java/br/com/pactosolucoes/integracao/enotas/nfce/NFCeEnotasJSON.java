package br.com.pactosolucoes.integracao.enotas.nfce;

import negocio.comuns.notaFiscal.NotaPagamentoTO;
import negocio.comuns.notaFiscal.NotaProdutoTO;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class NFCeEnotasJSON {

    public JSONObject obterJSONEnotas(NotaTO notaTO) throws JSONException {
        JSONObject json = new JSONObject();

        json.put("id", notaTO.getNotaIDReferencia());
        json.put("ambienteEmissao", notaTO.getConfiguracaoNotaFiscalVO().getAmbienteEmissao().getDescricaoSemAcentuacao());
        json.put("enviarPorEmail", notaTO.isNotaEnviarEmail());

        if (UteisValidacao.emptyString(Uteis.tirarCaracteres(notaTO.getCliCPFCNPJ(), true))) {
            json.put("cliente", JSONObject.NULL);
        } else {
            JSONObject cliente = new JSONObject();
            cliente.put("tipoPessoa", notaTO.getCliTipo());
            cliente.put("nome", notaTO.getCliRazaoSocial());
            cliente.put("cpfCnpj", Uteis.tirarCaracteres(notaTO.getCliCPFCNPJ(), true));
            cliente.put("email", notaTO.getCliEmail());
            cliente.put("telefone", Uteis.tirarCaracteres(notaTO.getCliTelefone(), true));
            json.put("cliente", cliente);
        }

        boolean ignorarCodigoDeBarrasEmissaoNfce = notaTO.getEmpresaVO() != null && notaTO.getEmpresaVO().isIgnorarCodigoDeBarrasEmissaoNfce();

        JSONArray itens = new JSONArray();
        for (NotaProdutoTO notaProdutoTO : notaTO.getNotaProdutos()){
            JSONObject prod = new JSONObject();
            prod.put("cfop", notaProdutoTO.getCfop());
            if(!UteisValidacao.emptyString(notaProdutoTO.getCest())) {
                prod.put("cest", notaProdutoTO.getCest());
            }
            prod.put("codigo", notaProdutoTO.getProdutoVO().getCodigo().toString());
            prod.put("descricao", notaProdutoTO.getDescricao());
            prod.put("ncm", notaProdutoTO.getNcm());
            prod.put("ean", ignorarCodigoDeBarrasEmissaoNfce ? "" : notaProdutoTO.getProdutoVO().getCodigoBarras());
            prod.put("eanFinanceiro", ignorarCodigoDeBarrasEmissaoNfce ? "" : notaProdutoTO.getProdutoVO().getCodigoBarras());
            prod.put("quantidade", notaProdutoTO.getQuantidade());
            prod.put("unidadeMedida", notaProdutoTO.getProdutoVO().getUnidadeMedida());
            prod.put("valorUnitario", notaProdutoTO.getValorUnitario());
            prod.put("descontos", notaProdutoTO.getValorDesconto());

            JSONObject impostos = new JSONObject();
            if (notaProdutoTO.isEnviarPercentualImposto()) {
                JSONObject percentualAproximadoTributos = new JSONObject();
                JSONObject detalhado = new JSONObject();
                detalhado.put("percentualFederal", notaProdutoTO.getPercentualFederal());
                detalhado.put("percentualEstadual", notaProdutoTO.getPercentualEstadual());
                detalhado.put("percentualMunicipal", notaProdutoTO.getPercentualMunicipal());
                percentualAproximadoTributos.put("detalhado", detalhado);
                impostos.put("percentualAproximadoTributos", percentualAproximadoTributos);
                impostos.put("fonte", "IBPT x5675s");
            }

            if (notaProdutoTO.getProdutoVO().getTipoProduto().equals(TipoProduto.PRODUTO_ESTOQUE.getCodigo())) {
                JSONObject icms = new JSONObject();
                String situacaoTributariaICMS = notaProdutoTO.getProdutoVO().getSituacaoTributariaICMS();
                icms.put("situacaoTributaria", situacaoTributariaICMS);
                if(notaProdutoTO.getProdutoVO().isEnviaAliquotaNFeICMS()) {
                    icms.put("aliquota", notaProdutoTO.getProdutoVO().getAliquotaICMS());
                }
                verificaAdicionaPorAliquotaSemValor(notaProdutoTO, notaProdutoTO.isIsentoICMS(), icms);
                impostos.put("icms", icms);

                if(!UteisValidacao.emptyString(situacaoTributariaICMS) && (situacaoTributariaICMS.equals("40") || situacaoTributariaICMS.equals("41"))){
                    prod.put("codigoBeneficioFiscal", notaProdutoTO.getProdutoVO().getCodigoBeneficioFiscal());
                }

            } else {

                JSONObject issqn = new JSONObject();
                issqn.put("itemListaServicoLC116", notaProdutoTO.getProdutoVO().getCodigoListaServico());
                issqn.put("aliquotaIss", notaProdutoTO.getProdutoVO().getAliquotaISSQN());
                impostos.put("issqn", issqn);
            }

            JSONObject pis = new JSONObject();
            String situacaoTributariaPIS = notaProdutoTO.getProdutoVO().getSituacaoTributariaPIS();
            pis.put("situacaoTributaria", situacaoTributariaPIS);
            verificaAdicionaPorAliquotaSemValor(notaProdutoTO, notaProdutoTO.isIsentoPIS(), pis);
            verificaEnviarAliquota(notaProdutoTO.getProdutoVO().isEnviaAliquotaNFePIS(), notaProdutoTO.getProdutoVO().getAliquotaPIS(), pis);
            impostos.put("pis", pis);

            JSONObject cofins = new JSONObject();
            String situacaoTributariaCOFINS = notaProdutoTO.getProdutoVO().getSituacaoTributariaCOFINS();
            cofins.put("situacaoTributaria", situacaoTributariaCOFINS);
            verificaAdicionaPorAliquotaSemValor(notaProdutoTO, notaProdutoTO.isIsentoCOFINS(), cofins);
            verificaEnviarAliquota(notaProdutoTO.getProdutoVO().isEnviaAliquotaNFeCOFINS(), notaProdutoTO.getProdutoVO().getAliquotaCOFINS(), cofins);
            impostos.put("cofins", cofins);

            prod.put("impostos", impostos);

            itens.put(prod);
        }

        json.put("itens", itens);

        JSONArray formas = new JSONArray();
        boolean outras = false;
        for (NotaPagamentoTO notaPagamentoTO : notaTO.getNotaPagamentos()){
            JSONObject paga = new JSONObject();
            if(obterDescricaoTipoFormaPagamento(notaPagamentoTO.getSiglaFormaPagamento()).equals("Outros")) {
                outras = true;
                paga.put("descricao", notaPagamentoTO.getDescricaoFormaPagamento());
            }
            paga.put("tipo", obterDescricaoTipoFormaPagamento(notaPagamentoTO.getSiglaFormaPagamento()));
            paga.put("valor", notaPagamentoTO.getValor());
            if (notaTO.getConfiguracaoNotaFiscalVO().isApresentarTipoIntegracaoPagamento() && isCartao(notaPagamentoTO.getSiglaFormaPagamento())) {
                JSONObject credenciadoraCartao = new JSONObject();
                credenciadoraCartao.put("tipoIntegracaoPagamento", "NaoIntegradoAoSistemaDeGestao");
                paga.put("credenciadoraCartao", credenciadoraCartao);
            }
            formas.put(paga);
        }

        JSONObject pagamento = new JSONObject();
        if(!outras) {
            pagamento.put("tipo", "PagamentoAVista");
        }
        pagamento.put("formas", formas);

        JSONObject pedido = new JSONObject();
        pedido.put("presencaConsumidor", "OperacaoPresencial");
        pedido.put("pagamento", pagamento);

        json.put("pedido", pedido);
        return json;
    }

    private void verificaAdicionaPorAliquotaSemValor(NotaProdutoTO notaProdutoTO, boolean isenta, JSONObject jsonAdicionar) {
        if (isenta) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("baseCalculo", 0.0);
            jsonObject.put("aliquota", 0.0);
            jsonObject.put("valor", 0.0);
            jsonAdicionar.put("porAliquota", jsonObject);
        }
    }

    private void verificaEnviarAliquota(boolean enviar, Double aliquota, JSONObject jsonAdicionar) {
        if(enviar) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("aliquota", aliquota);
            jsonAdicionar.put("porAliquota", jsonObject);
        }
    }

    private String obterDescricaoTipoFormaPagamento(String sigla) {
        if (sigla == null) {
            return "";
        }
        if (sigla.equals("AV")) {
            return "Dinheiro";
        }
        if (sigla.equals("CH")) {
            return "Cheque";
        }
        if (sigla.equals("CA")) {
            return "CartaoDeCredito";
        }
        if (sigla.equals("CD")) {
            return "CartaoDeDebito";
        }
        if (sigla.equals("CC")) {
            return "CreditoLoja";
        }
        return "Outros";
    }

    private boolean isCartao(String sigla) {
        return "CA".equals(sigla) || "CD".equals(sigla);
    }
}

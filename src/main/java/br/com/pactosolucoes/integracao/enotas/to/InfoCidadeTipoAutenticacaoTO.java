package br.com.pactosolucoes.integracao.enotas.to;

import negocio.comuns.arquitetura.SuperTO;
import org.json.JSONObject;

public class InfoCidadeTipoAutenticacaoTO extends SuperTO {

    private String certificadoDigital;
    private String usuario;
    private String senha;
    private String token;
    private String fraseSecreta;


    public InfoCidadeTipoAutenticacaoTO() {
    }

    public InfoCidadeTipoAutenticacaoTO(JSONObject jsonObject) {
        try {
            this.certificadoDigital = jsonObject.getString("certificadoDigital");
        } catch (Exception ignored){}

        try {
            this.usuario = jsonObject.getString("usuario");
        } catch (Exception ignored){}

        try {
            this.senha = jsonObject.getString("senha");
        } catch (Exception ignored){}

        try {
            this.token = jsonObject.getString("token");
        } catch (Exception ignored){}

        try {
            this.fraseSecreta = jsonObject.getString("fraseSecreta");
        } catch (Exception ignored){}
    }

    public String getCertificadoDigital() {
        if (certificadoDigital == null) {
            certificadoDigital =  "";
        }
        return certificadoDigital;
    }

    public void setCertificadoDigital(String certificadoDigital) {
        this.certificadoDigital = certificadoDigital;
    }

    public String getUsuario() {
        if (usuario == null) {
            usuario =  "";
        }
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getSenha() {
        if (senha == null) {
            senha =  "";
        }
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getToken() {
        if (token == null) {
            token =  "";
        }
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getFraseSecreta() {
        if (fraseSecreta == null) {
            fraseSecreta =  "";
        }
        return fraseSecreta;
    }

    public void setFraseSecreta(String fraseSecreta) {
        this.fraseSecreta = fraseSecreta;
    }
}

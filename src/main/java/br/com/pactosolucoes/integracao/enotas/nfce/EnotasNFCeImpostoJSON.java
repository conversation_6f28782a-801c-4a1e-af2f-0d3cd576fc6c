package br.com.pactosolucoes.integracao.enotas.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaProdutoTO;

public class EnotasNFCeImpostoJSON extends SuperJSON {

    @JsonProperty("situacaoTributaria")
    private String situacaoTributaria;
    @JsonProperty("codigo")
    private String codigo;
    @JsonProperty("aliquota")
    private String aliquota;


    public EnotasNFCeImpostoJSON(NotaProdutoTO notaProdutoTO) {
//        this.cfop = notaProdutoTO.getCfop();
//        this.codigo = notaProdutoTO.getProdutoVO().getCodigo().toString();
//        this.descricao = notaProdutoTO.getDescricao();
//        this.ncm = notaProdutoTO.getNcm();
//        this.quantidade = notaProdutoTO.getQuantidade();
//        this.unidadeMedida = notaProdutoTO.getUnidadeMedida();
//        this.valorUnitario = notaProdutoTO.getValorUnitario();
//        this.descontos = notaProdutoTO.getValorDesconto();
//        this.outrasDespesas = notaProdutoTO.getOutrasDespesas();
    }
}

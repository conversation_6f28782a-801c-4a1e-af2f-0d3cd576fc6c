package br.com.pactosolucoes.integracao.enotas.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaProdutoTO;

public class EnotasNFCeProdutoPercentualDetalhadoJSON extends SuperJSON {

    @JsonProperty("percentualFederal")
    private Double percentualAproximadoTributos;
    @JsonProperty("percentualEstadual")
    private Integer percentualEstadual;
    @JsonProperty("percentualMunicipal")
    private String percentualMunicipal;


    public EnotasNFCeProdutoPercentualDetalhadoJSON(NotaProdutoTO notaProdutoTO) {
//        this.cfop = notaProdutoTO.getCfop();
//        this.codigo = notaProdutoTO.getProdutoVO().getCodigo().toString();
//        this.descricao = notaProdutoTO.getDescricao();
//        this.ncm = notaProdutoTO.getNcm();
//        this.quantidade = notaProdutoTO.getQuantidade();
//        this.unidadeMedida = notaProdutoTO.getUnidadeMedida();
//        this.valorUnitario = notaProdutoTO.getValorUnitario();
//        this.descontos = notaProdutoTO.getValorDesconto();
//        this.outrasDespesas = notaProdutoTO.getOutrasDespesas();
    }
}

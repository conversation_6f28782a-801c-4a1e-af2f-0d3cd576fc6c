package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.basico.ConviteVO;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class HistoricoConvidadoDTO {
    private Integer codigo;
    private String convidado;
    private Date dia;
    private String situacao;

    public HistoricoConvidadoDTO() {
    }

    public HistoricoConvidadoDTO(ConviteVO obj) {
        this.codigo = obj.getCodigo();
        this.convidado = obj.getConvidado().getPessoa().getNome();
        this.dia = obj.getDia();
        this.situacao = obj.getSituacao();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getConvidado() {
        return convidado;
    }

    public void setConvidado(String convidado) {
        this.convidado = convidado;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }
}

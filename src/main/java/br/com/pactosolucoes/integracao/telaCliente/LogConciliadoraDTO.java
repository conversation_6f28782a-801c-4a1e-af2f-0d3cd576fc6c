package br.com.pactosolucoes.integracao.telaCliente;

import br.com.pactosolucoes.integracao.conciliadora.LogConciliadoraVO;

public class LogConciliadoraDTO {
    private Integer codigo;
    private Long data;
    private EmpresaDTO empresa;
    private MovPagamentoDTO movPagamento;
    private Boolean sucesso = false;
    private String resultado;

    public LogConciliadoraDTO(LogConciliadoraVO logConciliadoraVO) {
        this.codigo = logConciliadoraVO.getCodigo();
        this.data = logConciliadoraVO.getData().getTime();
        this.empresa = new EmpresaDTO(logConciliadoraVO.getEmpresaVO());
        this.movPagamento = new MovPagamentoDTO(logConciliadoraVO.getMovPagamentoVO());
        this.sucesso = logConciliadoraVO.isSucesso();
        this.resultado = logConciliadoraVO.getResultado();
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Long getData() {
        return data;
    }

    public void setData(Long data) {
        this.data = data;
    }

    public EmpresaDTO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaDTO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public MovPagamentoDTO getMovPagamento() {
        if (movPagamento == null) {
            movPagamento = new MovPagamentoDTO();
        }
        return movPagamento;
    }

    public void setMovPagamento(MovPagamentoDTO movPagamento) {
        this.movPagamento = movPagamento;
    }

    public Boolean getSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getResultado() {
        if (resultado == null) {
            resultado = "";
        }
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }
}

package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.basico.ClienteVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConvidadoClienteDTO {
    private Integer codigo;
    private String nome;
    private String cpf;

    public ConvidadoClienteDTO() {
    }

    public ConvidadoClienteDTO(ClienteVO obj) {
        this.codigo = obj.getCodigo();
        this.nome = obj.getPessoa().getNome();
        this.cpf = obj.getPessoa().getCfp();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}

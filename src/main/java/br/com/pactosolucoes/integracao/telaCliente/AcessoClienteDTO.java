package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AcessoClienteDTO {
    private Date dataHoraEntrada;
    private Date dataHoraSaida;
    private Integer localAcesso;
    private Integer coletor;
    private Integer codigo;

    public AcessoClienteDTO() {
    }

    public AcessoClienteDTO(Date dataHoraEntrada, Date dataHoraSaida, Integer localAcesso, Integer coletor, Integer codigo) {
        this.dataHoraEntrada = dataHoraEntrada;
        this.dataHoraSaida = dataHoraSaida;
        this.localAcesso = localAcesso;
        this.coletor = coletor;
        this.codigo = codigo;
    }

    public Date getDataHoraEntrada() {
        return dataHoraEntrada;
    }

    public void setDataHoraEntrada(Date dataHoraEntrada) {
        this.dataHoraEntrada = dataHoraEntrada;
    }

    public Date getDataHoraSaida() {
        return dataHoraSaida;
    }

    public void setDataHoraSaida(Date dataHoraSaida) {
        this.dataHoraSaida = dataHoraSaida;
    }

    public Integer getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(Integer localAcesso) {
        this.localAcesso = localAcesso;
    }

    public Integer getColetor() {
        return coletor;
    }

    public void setColetor(Integer coletor) {
        this.coletor = coletor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}

package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContratoDTO {
    Integer codigo;
    String planoDescricao;
    Long dataInicio;
    Long dataTermino;
    String situacao;
    Boolean renovarContrato;
    Boolean rematricularContrato;
    private Integer tipoContrato;

    private ColaboradorDTO consultorResponsavelDTO;
    private Boolean permiteRenovacaoAutomatica;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getPlanoDescricao() {
        return planoDescricao;
    }

    public void setPlanoDescricao(String planoDescricao) {
        this.planoDescricao = planoDescricao;
    }

    public Long getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Long dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Long getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Long dataTermino) {
        this.dataTermino = dataTermino;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Boolean getRenovarContrato() {
        return renovarContrato;
    }

    public void setRenovarContrato(Boolean renovarContrato) {
        this.renovarContrato = renovarContrato;
    }

    public Boolean getRematricularContrato() {
        return rematricularContrato;
    }

    public void setRematricularContrato(Boolean rematricularContrato) {
        this.rematricularContrato = rematricularContrato;
    }

    public ColaboradorDTO getConsultorResponsavelDTO() {
        return consultorResponsavelDTO;
    }

    public void setConsultorResponsavelDTO(ColaboradorDTO consultorResponsavelDTO) {
        this.consultorResponsavelDTO = consultorResponsavelDTO;
    }

    public Integer getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(Integer tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public Boolean getPermiteRenovacaoAutomatica() {
        return permiteRenovacaoAutomatica;
    }

    public void setPermiteRenovacaoAutomatica(Boolean permiteRenovacaoAutomatica) {
        this.permiteRenovacaoAutomatica = permiteRenovacaoAutomatica;
    }

}

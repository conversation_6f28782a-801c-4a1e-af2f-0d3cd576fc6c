package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.RiscoVO;
import negocio.comuns.basico.ClienteMensagemVO;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClienteMensagemDTO {

    private Integer codigo;
    private String mensagem;
    private Date dataDesbloqueio;
    private Boolean apresentarEdicaoMensagem;
    private Boolean bloqueio;
    private Date dataBloqueio;
    private String mensagemTratada;
    private Boolean desabilitado;
    private Boolean bloqueioCheque;
    private String tipoMensagem;
    private String tipoMensagemDescricao;

    public ClienteMensagemDTO() {
        
    }

    public ClienteMensagemDTO(ClienteMensagemVO clienteMensagemVO) {
        this.codigo = clienteMensagemVO.getCodigo();
        this.mensagem = clienteMensagemVO.getMensagem();
        this.dataDesbloqueio = clienteMensagemVO.getDataDesbloqueio();
        this.apresentarEdicaoMensagem = clienteMensagemVO.getApresentarEdicaoMensagem();
        this.bloqueio = clienteMensagemVO.getBloqueio();
        this.dataBloqueio = clienteMensagemVO.getDataBloqueio();
        this.mensagemTratada = clienteMensagemVO.getMensagemTratada();
        this.desabilitado = clienteMensagemVO.isDesabilitado();
        this.bloqueioCheque = clienteMensagemVO.isBloqueioCheque();
        this.tipoMensagem = clienteMensagemVO.getTipomensagem().getSigla();
        this.tipoMensagemDescricao = clienteMensagemVO.getTipomensagem().name();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Date getDataDesbloqueio() {
        return dataDesbloqueio;
    }

    public void setDataDesbloqueio(Date dataDesbloqueio) {
        this.dataDesbloqueio = dataDesbloqueio;
    }

    public Boolean getApresentarEdicaoMensagem() {
        return apresentarEdicaoMensagem;
    }

    public void setApresentarEdicaoMensagem(Boolean apresentarEdicaoMensagem) {
        this.apresentarEdicaoMensagem = apresentarEdicaoMensagem;
    }

    public Boolean getBloqueio() {
        return bloqueio;
    }

    public void setBloqueio(Boolean bloqueio) {
        this.bloqueio = bloqueio;
    }

    public Date getDataBloqueio() {
        return dataBloqueio;
    }

    public void setDataBloqueio(Date dataBloqueio) {
        this.dataBloqueio = dataBloqueio;
    }

    public String getMensagemTratada() {
        return mensagemTratada;
    }

    public void setMensagemTratada(String mensagemTratada) {
        this.mensagemTratada = mensagemTratada;
    }

    public Boolean getDesabilitado() {
        return desabilitado;
    }

    public void setDesabilitado(Boolean desabilitado) {
        this.desabilitado = desabilitado;
    }

    public Boolean getBloqueioCheque() {
        return bloqueioCheque;
    }

    public void setBloqueioCheque(Boolean bloqueioCheque) {
        this.bloqueioCheque = bloqueioCheque;
    }

    public String getTipoMensagem() {
        return tipoMensagem;
    }

    public void setTipoMensagem(String tipoMensagem) {
        this.tipoMensagem = tipoMensagem;
    }

    public String getTipoMensagemDescricao() {
        return tipoMensagemDescricao;
    }

    public void setTipoMensagemDescricao(String tipoMensagemDescricao) {
        this.tipoMensagemDescricao = tipoMensagemDescricao;
    }
}

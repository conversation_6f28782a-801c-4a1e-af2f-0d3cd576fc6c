package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContratoAssinaturaDigitalDTO {
    private Integer contrato;
    private String documentos;
    private String endereco;
    private String assinatura;
    private String atestado;
    private String anexo1;
    private String anexo2;
    private String anexoCancelamento;

    public ContratoAssinaturaDigitalDTO(ContratoAssinaturaDigitalVO obj) {
        this.contrato = obj.getContrato().getCodigo();
        this.documentos = obj.getDocumentos_Apresentar();
        this.endereco = obj.getEndereco_Apresentar();
        this.assinatura = (obj.getAssinatura_Apresentar() != null && obj.getAssinatura_Apresentar().contains("fotoPadrao")) ? null : obj.getAssinatura_Apresentar();
        this.atestado = obj.getAtestado_Apresentar();
        this.anexo1 = obj.getAnexo1_Apresentar();
        this.anexo2 = obj.getAnexo2_Apresentar();
        this.anexoCancelamento = obj.getAnexoCancelamento_Apresentar();
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public String getDocumentos() {
        return documentos;
    }

    public void setDocumentos(String documentos) {
        this.documentos = documentos;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(String assinatura) {
        this.assinatura = assinatura;
    }

    public String getAtestado() {
        return atestado;
    }

    public void setAtestado(String atestado) {
        this.atestado = atestado;
    }

    public String getAnexo1() {
        return anexo1;
    }

    public void setAnexo1(String anexo1) {
        this.anexo1 = anexo1;
    }

    public String getAnexo2() {
        return anexo2;
    }

    public void setAnexo2(String anexo2) {
        this.anexo2 = anexo2;
    }

    public String getAnexoCancelamento() {
        return anexoCancelamento;
    }

    public void setAnexoCancelamento(String anexoCancelamento) {
        this.anexoCancelamento = anexoCancelamento;
    }
}

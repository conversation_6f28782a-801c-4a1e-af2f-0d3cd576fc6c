package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class DadosPagamentoClienteDTO {

    private String bloqueioCobrancaAutomaticaData;
    private Integer bloqueioCobrancaAutomaticaTipo;
    private String convenioCobranca;
    private String hintExibir;

    public DadosPagamentoClienteDTO() {

    }

    public String getBloqueioCobrancaAutomaticaData() {
        return bloqueioCobrancaAutomaticaData;
    }

    public void setBloqueioCobrancaAutomaticaData(String bloqueioCobrancaAutomaticaData) {
        this.bloqueioCobrancaAutomaticaData = bloqueioCobrancaAutomaticaData;
    }

    public Integer getBloqueioCobrancaAutomaticaTipo() {
        return bloqueioCobrancaAutomaticaTipo;
    }

    public void setBloqueioCobrancaAutomaticaTipo(Integer bloqueioCobrancaAutomaticaTipo) {
        this.bloqueioCobrancaAutomaticaTipo = bloqueioCobrancaAutomaticaTipo;
    }

    public String getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(String convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public String getHintExibir() {
        return hintExibir;
    }

    public void setHintExibir(String hintExibir) {
        this.hintExibir = hintExibir;
    }
}

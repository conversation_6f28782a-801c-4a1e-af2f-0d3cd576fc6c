package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.basico.BrindeVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BrindeDTO {
    private Integer codigo;
    private String descricao;
    private Integer pontos;

    public BrindeDTO() {
    }

    public BrindeDTO(BrindeVO obj) {
        this.codigo = obj.getCodigo();
        this.descricao = obj.getDescricao();
        this.pontos = obj.getPontos();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }
}

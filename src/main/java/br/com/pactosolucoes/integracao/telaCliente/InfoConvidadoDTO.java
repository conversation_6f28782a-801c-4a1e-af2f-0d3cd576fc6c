package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class InfoConvidadoDTO {
    private Integer usados;
    private Integer disponivel;
    private Integer direito;
    private boolean exibirConvite;

    public InfoConvidadoDTO() {
    }

    public Integer getUsados() {
        return usados;
    }

    public void setUsados(Integer usados) {
        this.usados = usados;
    }

    public Integer getDisponivel() {
        return disponivel;
    }

    public void setDisponivel(Integer disponivel) {
        this.disponivel = disponivel;
    }

    public Integer getDireito() {
        return direito;
    }

    public void setDireito(Integer direito) {
        this.direito = direito;
    }

    public boolean isExibirConvite() {
        return exibirConvite;
    }

    public void setExibirConvite(boolean exibirConvite) {
        this.exibirConvite = exibirConvite;
    }

}

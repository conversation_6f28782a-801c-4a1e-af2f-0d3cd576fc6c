package br.com.pactosolucoes.integracao.telaCliente;

import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.basico.*;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class NovoConvidadoClienteDTO {

    private Integer codigo;
    private String matricula;
    private String nomeCompleto;
    private String cpf;
    private String rne;
    private String sexo;
    private Long nascimento;
    private String nomeResponsavel;
    private String cpfResponsavel;
    private String emailResponsavel;
    private Long nascimentoResponsavel;
    private String telefoneCelular;
    private String telefoneFixo;
    private String email;
    private String empresa;

    public NovoConvidadoClienteDTO() {
    }

    public NovoConvidadoClienteDTO(JSONObject jsonDados) {
        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setCodigoMatricula(0);
        clienteVO.setMatricula("");
        clienteVO.setVinculoVOs(new ArrayList<>());

        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setNome(jsonDados.optString("nomeCompleto"));
        pessoaVO.setCfp(jsonDados.optString("cpf"));
        pessoaVO.setRne(jsonDados.optString("rne"));
        pessoaVO.setSexo(jsonDados.optString("sexo"));

        Long dataNascimento = jsonDados.optLong("nascimento");
        pessoaVO.setDataNasc(UteisValidacao.emptyNumber(dataNascimento) ? null : new Date(dataNascimento));
        pessoaVO.setNomeMae(jsonDados.optString("nomeResponsavel"));
        pessoaVO.setCpfMae(jsonDados.optString("cpfResponsavel"));
        pessoaVO.setEmailMae(jsonDados.optString("emailResponsavel"));

        Long nascimentoResponsavel = jsonDados.optLong("nascimentoResponsavel");
        pessoaVO.setDataNascimentoResponsavel(UteisValidacao.emptyNumber(nascimentoResponsavel) ? null : new Date(nascimentoResponsavel));

        TelefoneVO telefoneCelularVO = new TelefoneVO();
        telefoneCelularVO.setNumero(jsonDados.optString("telefoneCelular"));

        TelefoneVO telefoneResidencialVO = new TelefoneVO();
        telefoneResidencialVO.setNumero(jsonDados.optString("telefoneFixo"));

        JSONObject jsonEndereco = jsonDados.optJSONObject("endereco");
        EnderecoVO enderecoResidencialVO = new EnderecoVO();
        if (jsonEndereco != null) {
            enderecoResidencialVO.setEndereco(jsonEndereco.optString("endereco"));
            enderecoResidencialVO.setComplemento(jsonEndereco.optString("complemento"));
            enderecoResidencialVO.setNumero(jsonEndereco.optString("numero"));
            enderecoResidencialVO.setBairro(jsonEndereco.optString("bairro"));
            enderecoResidencialVO.setCep(jsonEndereco.optString("cep"));
        }

        EnderecoVO enderecoComercialVO = new EnderecoVO();
        JSONObject jsonEnderecoComercial = jsonDados.optJSONObject("enderecoComercial");
        if (jsonEnderecoComercial != null) {
            enderecoComercialVO.setEndereco(jsonEnderecoComercial.optString("endereco"));
            enderecoComercialVO.setComplemento(jsonEnderecoComercial.optString("complemento"));
            enderecoComercialVO.setNumero(jsonEnderecoComercial.optString("numero"));
            enderecoComercialVO.setBairro(jsonEnderecoComercial.optString("bairro"));
            enderecoComercialVO.setCep(jsonEnderecoComercial.optString("cep"));
        }

        EmailVO emailVO = new EmailVO();
        emailVO.setEmail(jsonDados.optString("email"));

        boolean estrangeira = jsonDados.getBoolean("alunoEstrangeiro");

        JSONObject jsonConsultor = jsonDados.optJSONObject("consultor");
        Integer codConsultor = jsonConsultor != null ? jsonConsultor.getInt("codigo") : 0;

        JSONObject jsonEvento = jsonDados.optJSONObject("evento");
        Integer codEvento = jsonEvento != null ? jsonEvento.optInt("codigo") : 0;


    }

    public NovoConvidadoClienteDTO(ClienteVO obj) {
        this.codigo = obj.getCodigo();
        this.matricula = obj.getMatricula();
        this.nomeCompleto = obj.getPessoa().getNome();
        this.cpf = obj.getPessoa().getCfp();
        this.rne = obj.getPessoa().getRne();
        this.sexo = obj.getPessoa().getSexo();
        this.nascimento = obj.getPessoa().getDataNasc() != null ? obj.getPessoa().getDataNasc().getTime() : null;
        this.nomeResponsavel = obj.getPessoa().getNomeMae();
        this.cpfResponsavel = obj.getPessoa().getCpfMae();
        this.emailResponsavel = obj.getPessoa().getEmailMae();
        this.nascimentoResponsavel = obj.getPessoa().getDataNascimentoResponsavel() != null ? obj.getPessoa().getDataNascimentoResponsavel().getTime() : null;
        this.empresa = obj.getEmpresa().getNome();

        this.telefoneCelular = null;
        this.telefoneFixo = null;

        for (TelefoneVO telefoneVO : obj.getPessoa().getTelefoneVOs()) {
            if (this.telefoneCelular == null &&
                    telefoneVO.getTipoTelefone() != null &&
                    telefoneVO.getTipoTelefone().equalsIgnoreCase(TipoTelefoneEnum.CELULAR.getCodigo())) {
                this.telefoneCelular = telefoneVO.getNumero();
            }
            if (this.telefoneFixo == null &&
                    telefoneVO.getTipoTelefone() != null &&
                    telefoneVO.getTipoTelefone().equalsIgnoreCase(TipoTelefoneEnum.RESIDENCIAL.getCodigo())) {
                this.telefoneFixo = telefoneVO.getNumero();
            }
        }
        this.email = UteisValidacao.emptyList(obj.getPessoa().getEmailVOs()) ? "" : obj.getPessoa().getEmailVOs().get(0).getEmail();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeCompleto() {
        return nomeCompleto;
    }

    public void setNomeCompleto(String nomeCompleto) {
        this.nomeCompleto = nomeCompleto;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getRne() {
        return rne;
    }

    public void setRne(String rne) {
        this.rne = rne;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public Long getNascimento() {
        return nascimento;
    }

    public void setNascimento(Long nascimento) {
        this.nascimento = nascimento;
    }

    public String getNomeResponsavel() {
        return nomeResponsavel;
    }

    public void setNomeResponsavel(String nomeResponsavel) {
        this.nomeResponsavel = nomeResponsavel;
    }

    public String getCpfResponsavel() {
        return cpfResponsavel;
    }

    public void setCpfResponsavel(String cpfResponsavel) {
        this.cpfResponsavel = cpfResponsavel;
    }

    public String getEmailResponsavel() {
        return emailResponsavel;
    }

    public void setEmailResponsavel(String emailResponsavel) {
        this.emailResponsavel = emailResponsavel;
    }

    public Long getNascimentoResponsavel() {
        return nascimentoResponsavel;
    }

    public void setNascimentoResponsavel(Long nascimentoResponsavel) {
        this.nascimentoResponsavel = nascimentoResponsavel;
    }

    public String getTelefoneCelular() {
        return telefoneCelular;
    }

    public void setTelefoneCelular(String telefoneCelular) {
        this.telefoneCelular = telefoneCelular;
    }

    public String getTelefoneFixo() {
        return telefoneFixo;
    }

    public void setTelefoneFixo(String telefoneFixo) {
        this.telefoneFixo = telefoneFixo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }
}

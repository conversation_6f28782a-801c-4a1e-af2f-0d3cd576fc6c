package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class InfoFamiliaresDTO extends SuperTO {

    private Integer quantidadeCompartilhamentosPlano;
    private boolean permiteCompartilharPlano = false;
    private boolean permitirEditarFamiliarComPlanoCompartilhado = false;

    public Integer getQuantidadeCompartilhamentosPlano() {
        if (quantidadeCompartilhamentosPlano == null) {
            quantidadeCompartilhamentosPlano = 0;
        }
        return quantidadeCompartilhamentosPlano;
    }

    public void setQuantidadeCompartilhamentosPlano(Integer quantidadeCompartilhamentosPlano) {
        this.quantidadeCompartilhamentosPlano = quantidadeCompartilhamentosPlano;
    }

    public boolean isPermiteCompartilharPlano() {
        return permiteCompartilharPlano;
    }

    public void setPermiteCompartilharPlano(boolean permiteCompartilharPlano) {
        this.permiteCompartilharPlano = permiteCompartilharPlano;
    }

    public boolean isPermitirEditarFamiliarComPlanoCompartilhado() {
        return permitirEditarFamiliarComPlanoCompartilhado;
    }

    public void setPermitirEditarFamiliarComPlanoCompartilhado(boolean permitirEditarFamiliarComPlanoCompartilhado) {
        this.permitirEditarFamiliarComPlanoCompartilhado = permitirEditarFamiliarComPlanoCompartilhado;
    }
}

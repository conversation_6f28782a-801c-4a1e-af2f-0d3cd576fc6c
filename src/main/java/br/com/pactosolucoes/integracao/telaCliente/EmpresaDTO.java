package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.EmpresaVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmpresaDTO extends SuperTO {

    private Integer codigo;
    private String nome;
    private String razaoSocial;
    private String cnpj;
    private String email;
    private boolean ativa;
    private boolean usaEnotas;
    private boolean usaNotasDelphi;
    private boolean permiteContratosConcomintante;
    private boolean usarNFCe;
    private boolean usarNFSe;
    private Integer tipoGestaoNFSe;
    private boolean habilitarSomaDeAulaNaoVigente;
    private boolean habilitarCobrancaAutomaticaNaVenda;
    private boolean adicionarAulasDesmarcadasContratoAnterior;
    private boolean usarConciliadora;
    private boolean habilitarValidacaoHorariosMesmaTurma;

    public EmpresaDTO() {
    }

    public EmpresaDTO(EmpresaVO obj) {
        this.codigo = obj.getCodigo();
        this.nome = obj.getNome();
        this.razaoSocial = obj.getRazaoSocial();
        this.cnpj = obj.getCNPJ();
        this.email = obj.getEmail();
        this.ativa = obj.isAtiva();
        this.usaEnotas = obj.isUsaEnotas();
        this.usaNotasDelphi = obj.isUsaNotasDelphi();
        this.permiteContratosConcomintante = obj.isPermiteContratosConcomintante();
        this.usarNFCe = obj.isUsarNFCe();
        this.usarNFSe = obj.getUsarNFSe();
        this.tipoGestaoNFSe = obj.getTipoGestaoNFSe();
        this.habilitarSomaDeAulaNaoVigente = obj.isHabilitarSomaDeAulaNaoVigente();
        this.adicionarAulasDesmarcadasContratoAnterior = obj.isAdicionarAulasDesmarcadasContratoAnterior();
        this.habilitarCobrancaAutomaticaNaVenda = obj.isHabilitarCobrancaAutomaticaNaVenda();
        this.usarConciliadora = obj.isUsarConciliadora();
        this.habilitarValidacaoHorariosMesmaTurma = obj.getHabilitarValidacaoHorariosMesmaTurma();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isUsaEnotas() {
        return usaEnotas;
    }

    public void setUsaEnotas(boolean usaEnotas) {
        this.usaEnotas = usaEnotas;
    }

    public boolean isUsaNotasDelphi() {
        return usaNotasDelphi;
    }

    public void setUsaNotasDelphi(boolean usaNotasDelphi) {
        this.usaNotasDelphi = usaNotasDelphi;
    }

    public boolean isPermiteContratosConcomintante() {
        return permiteContratosConcomintante;
    }

    public void setPermiteContratosConcomintante(boolean permiteContratosConcomintante) {
        this.permiteContratosConcomintante = permiteContratosConcomintante;
    }

    public boolean isUsarNFCe() {
        return usarNFCe;
    }

    public void setUsarNFCe(boolean usarNFCe) {
        this.usarNFCe = usarNFCe;
    }

    public boolean isUsarNFSe() {
        return usarNFSe;
    }

    public void setUsarNFSe(boolean usarNFSe) {
        this.usarNFSe = usarNFSe;
    }

    public Integer getTipoGestaoNFSe() {
        return tipoGestaoNFSe;
    }

    public void setTipoGestaoNFSe(Integer tipoGestaoNFSe) {
        this.tipoGestaoNFSe = tipoGestaoNFSe;
    }

    public boolean isHabilitarSomaDeAulaNaoVigente() {
        return habilitarSomaDeAulaNaoVigente;
    }

    public void setHabilitarSomaDeAulaNaoVigente(boolean habilitarSomaDeAulaNaoVigente) {
        this.habilitarSomaDeAulaNaoVigente = habilitarSomaDeAulaNaoVigente;
    }

    public boolean isAdicionarAulasDesmarcadasContratoAnterior() {
        return adicionarAulasDesmarcadasContratoAnterior;
    }

    public void setAdicionarAulasDesmarcadasContratoAnterior(boolean adicionarAulasDesmarcadasContratoAnterior) {
        this.adicionarAulasDesmarcadasContratoAnterior = adicionarAulasDesmarcadasContratoAnterior;
    }

    public boolean isUsarConciliadora() {
        return usarConciliadora;
    }

    public void setUsarConciliadora(boolean usarConciliadora) {
        this.usarConciliadora = usarConciliadora;
    }

    public boolean isAtiva() {
        return ativa;
    }

    public void setAtiva(boolean ativa) {
        this.ativa = ativa;
    }

    public boolean isHabilitarCobrancaAutomaticaNaVenda() {
        return habilitarCobrancaAutomaticaNaVenda;
    }

    public void setHabilitarCobrancaAutomaticaNaVenda(boolean habilitarCobrancaAutomaticaNaVenda) {
        this.habilitarCobrancaAutomaticaNaVenda = habilitarCobrancaAutomaticaNaVenda;
    }

    public boolean isHabilitarValidacaoHorariosMesmaTurma() {
        return habilitarValidacaoHorariosMesmaTurma;
    }

    public void setHabilitarValidacaoHorariosMesmaTurma(boolean habilitarValidacaoHorariosMesmaTurma) {
        this.habilitarValidacaoHorariosMesmaTurma = habilitarValidacaoHorariosMesmaTurma;
    }
}

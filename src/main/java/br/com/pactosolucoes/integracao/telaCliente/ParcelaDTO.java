package br.com.pactosolucoes.integracao.telaCliente;

import br.com.pacto.priv.utils.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.UteisValidacao;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ParcelaDTO extends SuperTO {

    private Integer codigo;
    private Integer pessoa;
    private Integer contrato;
    private String descricao;
    private String situacao;
    private Double valorParcela;
    private String vencimento;

    public ParcelaDTO() {

    }

    public ParcelaDTO(MovParcelaVO movParcelaVO) {
        this.codigo = movParcelaVO.getCodigo();
        this.pessoa = movParcelaVO.getPessoa().getCodigo();
        this.contrato = UteisValidacao.emptyNumber(movParcelaVO.getCodigoContrato()) ? null : movParcelaVO.getCodigoContrato();
        this.descricao = movParcelaVO.getDescricao();
        this.situacao = movParcelaVO.getSituacao();
        this.valorParcela = Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela());
        this.vencimento = Uteis.getDataAplicandoFormatacao(movParcelaVO.getDataVencimento(), "yyyy-MM-dd");
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public String getVencimento() {
        return vencimento;
    }

    public void setVencimento(String vencimento) {
        this.vencimento = vencimento;
    }
}

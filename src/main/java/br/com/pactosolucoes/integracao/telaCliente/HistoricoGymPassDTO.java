package br.com.pactosolucoes.integracao.telaCliente;

import br.com.pacto.priv.utils.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class HistoricoGymPassDTO extends SuperTO {

    private Integer codigo;
    private String data;
    private String token;
    private boolean permiteAcesso;
    private Double valor;

    public HistoricoGymPassDTO() {

    }

    public HistoricoGymPassDTO(PeriodoAcessoClienteVO obj) {
        this.codigo = obj.getCodigo();
        this.data = obj.getDataInicioAcesso_Apresentar();
        this.token = obj.getTokenGymPass();
        this.permiteAcesso = obj.getPermiteAcesso();
        this.valor = Uteis.arredondarForcando2CasasDecimais(obj.getValorGympass());
    }

    public HistoricoGymPassDTO(PeriodoAcessoClienteVO obj, String token) {
        this.codigo = obj.getCodigo();
        this.data = obj.getDataInicioAcesso_Apresentar();
        this.token = token;
        this.permiteAcesso = obj.getPermiteAcesso();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public boolean isPermiteAcesso() {
        return permiteAcesso;
    }

    public void setPermiteAcesso(boolean permiteAcesso) {
        this.permiteAcesso = permiteAcesso;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }
}

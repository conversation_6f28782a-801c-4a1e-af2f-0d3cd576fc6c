package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.utilitarias.Calendario;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AulaAgendaTO {

    private Integer codigo;
    private Integer horarioTurma;
    private String dataLancamento;
    private String aulaDesmarcada;
    private String aulaReposicao;
    private String presenca;
    private String usuario;

    public AulaAgendaTO() {

    }

    public AulaAgendaTO(AulaDesmarcadaVO obj) {
        this.codigo = obj.getCodigo();
        this.dataLancamento = Calendario.getDataAplicandoFormatacao(obj.getDataLancamento(), "dd/MM/yyyy HH:mm");
        this.aulaDesmarcada = obj.getDescricaoOrigem();
        this.usuario = obj.getUsuarioVO().getUsername();
        this.horarioTurma = obj.getHorarioTurmaVO().getCodigo();
    }

    public AulaAgendaTO(ReposicaoVO obj) {
        this.codigo = obj.getCodigo();
        this.dataLancamento = Calendario.getDataAplicandoFormatacao(obj.getDataLancamento(), "dd/MM/yyyy HH:mm");
        this.aulaDesmarcada = obj.getDescricaoOrigem();
        this.aulaReposicao = obj.getDescricaoDestino();
        this.presenca = obj.getDataPresenca() == null ? "Falta" : Calendario.getDataAplicandoFormatacao(obj.getDataPresenca(), "dd/MM/yyyy");
        this.usuario = obj.getUsuario().getUsername();
    }

    public String getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getAulaDesmarcada() {
        return aulaDesmarcada;
    }

    public void setAulaDesmarcada(String aulaDesmarcada) {
        this.aulaDesmarcada = aulaDesmarcada;
    }

    public String getAulaReposicao() {
        return aulaReposicao;
    }

    public void setAulaReposicao(String aulaReposicao) {
        this.aulaReposicao = aulaReposicao;
    }

    public String getPresenca() {
        return presenca;
    }

    public void setPresenca(String presenca) {
        this.presenca = presenca;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(Integer horarioTurma) {
        this.horarioTurma = horarioTurma;
    }
}

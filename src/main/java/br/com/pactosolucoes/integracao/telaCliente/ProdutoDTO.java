package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.plano.ProdutoVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProdutoDTO extends SuperTO {

    private Integer codigo;
    private String descricao;
    private Double valor;

    public ProdutoDTO() {

    }

    public ProdutoDTO(ProdutoVO produtoVO) {
        this.codigo = produtoVO.getCodigo();
        this.descricao = produtoVO.getDescricao();
        this.valor = produtoVO.getValorFinal();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }
}

package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class InfoAfastamentoContratoDependenteDTO extends SuperTO {

    private Integer diasPermitidos;
    private Integer diasUtilizados;
    private Integer diasRestantes;
    private Integer diasMinimoSolicitar;

    public InfoAfastamentoContratoDependenteDTO() {

    }

    public Integer getDiasPermitidos() {
        return diasPermitidos;
    }

    public void setDiasPermitidos(Integer diasPermitidos) {
        this.diasPermitidos = diasPermitidos;
    }

    public Integer getDiasUtilizados() {
        return diasUtilizados;
    }

    public void setDiasUtilizados(Integer diasUtilizados) {
        this.diasUtilizados = diasUtilizados;
    }

    public Integer getDiasRestantes() {
        return diasRestantes;
    }

    public void setDiasRestantes(Integer diasRestantes) {
        this.diasRestantes = diasRestantes;
    }

    public Integer getDiasMinimoSolicitar() {
        return diasMinimoSolicitar;
    }

    public void setDiasMinimoSolicitar(Integer diasMinimoSolicitar) {
        this.diasMinimoSolicitar = diasMinimoSolicitar;
    }
}

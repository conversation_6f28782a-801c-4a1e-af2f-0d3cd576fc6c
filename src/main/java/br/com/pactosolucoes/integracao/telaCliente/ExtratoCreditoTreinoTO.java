package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.contrato.ControleCreditoTreinoVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExtratoCreditoTreinoTO {

    private Integer codigo;
    private String data;
    private String operacao;
    private String usuario;
    private String observacao;
    private String aulaDesmarcada;
    private String aulaMarcada;
    private Integer qtd;
    private Integer saldo;

    public ExtratoCreditoTreinoTO() {

    }

    public ExtratoCreditoTreinoTO(ControleCreditoTreinoVO obj) throws Exception {
        this.codigo = obj.getCodigo();
        this.data = obj.getDataOperacao_Apresentar();
        this.operacao = obj.getOperacao();
        this.usuario = obj.getUsuarioVO().getNome();
        this.aulaDesmarcada = obj.getAulaDesmarcada_Apresentar();
        this.aulaMarcada = obj.getAulaMarcada_Apresentar();
        this.qtd = obj.getQuantidade();
        this.saldo = obj.getSaldo();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getAulaDesmarcada() {
        return aulaDesmarcada;
    }

    public void setAulaDesmarcada(String aulaDesmarcada) {
        this.aulaDesmarcada = aulaDesmarcada;
    }

    public Integer getQtd() {
        return qtd;
    }

    public void setQtd(Integer qtd) {
        this.qtd = qtd;
    }

    public Integer getSaldo() {
        return saldo;
    }

    public void setSaldo(Integer saldo) {
        this.saldo = saldo;
    }

    public String getAulaMarcada() {
        return aulaMarcada;
    }

    public void setAulaMarcada(String aulaMarcada) {
        this.aulaMarcada = aulaMarcada;
    }
}

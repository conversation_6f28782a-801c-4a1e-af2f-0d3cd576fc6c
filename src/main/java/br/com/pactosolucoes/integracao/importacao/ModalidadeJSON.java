/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.importacao;

import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public class ModalidadeJSON {

    private Integer codigo;
    private Boolean utilizarTurma;
    private String nome;
    private Integer vezesPorSemana;

    public ModalidadeJSON() {
        
    }
    
    public ModalidadeJSON(JSONObject json) {
        this.nome = json.optString("nome");
        this.vezesPorSemana = json.optInt("vezesPorSemana");
        try{
            this.codigo = json.optInt("codigo");
            this.utilizarTurma = json.getBoolean("utilizarTurma");
            this.vezesPorSemana = json.getInt("nrVezesSemana");
        }catch (Exception e){
            utilizarTurma = false;
        }
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getVezesPorSemana() {
        return vezesPorSemana;
    }

    public void setVezesPorSemana(Integer vezesPorSemana) {
        this.vezesPorSemana = vezesPorSemana;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getUtilizarTurma() {
        return utilizarTurma;
    }

    public void setUtilizarTurma(Boolean utilizarTurma) {
        this.utilizarTurma = utilizarTurma;
    }
}

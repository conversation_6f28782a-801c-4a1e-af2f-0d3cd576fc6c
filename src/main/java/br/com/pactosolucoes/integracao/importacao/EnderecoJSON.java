package br.com.pactosolucoes.integracao.importacao;


import org.json.JSONObject;

public class EnderecoJSON {
    private String logradouro;
    private String cep;
    private String bairro;
    private String cidade;
    private String uf;
    private String pais;
    private String numero;
    private String complemento;

    public EnderecoJSON() {

    }

    public EnderecoJSON(JSONObject json) {
        if (json == null) {
            return;
        }
        this.logradouro = json.optString("logradouro");
        this.cep = json.optString("cep");
        this.bairro = json.optString("bairro");
        this.cidade = json.optString("cidade");
        this.uf = json.optString("uf");
        this.pais = json.optString("pais");
        this.numero = json.optString("numero");
        this.complemento = json.optString("complemento");
    }

    public String getLogradouro() {
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }
}

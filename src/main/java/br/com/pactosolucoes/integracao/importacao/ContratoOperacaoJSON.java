/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.importacao;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import java.util.Date;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public class ContratoOperacaoJSON extends SuperJSON{
    private Integer idExternoContrato;
    private String dataCadastro;
    private String dataInicio;
    private String dataFinal;
    private String tipoOperacao;

    public ContratoOperacaoJSON() {
    }
    
    public ContratoOperacaoJSON(int idExternoContrato,JSONObject json) {
        this.idExternoContrato = idExternoContrato;
        this.dataCadastro = json.optString("dataCadastro");
        this.dataInicio = json.optString("dataInicio");
        this.dataFinal = json.optString("dataFinal");
        this.tipoOperacao = json.optString("tipoOperacao");
    }

    
    
    public Integer getIdExternoContrato() {
        return idExternoContrato;
    }

    public void setIdExternoContrato(Integer idExternoContrato) {
        this.idExternoContrato = idExternoContrato;
    }

    public String getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(String dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }
    
    public Date getDataInicioDate() throws Exception{
        return Uteis.getDate(this.getDataInicio());
    }
    
    
}

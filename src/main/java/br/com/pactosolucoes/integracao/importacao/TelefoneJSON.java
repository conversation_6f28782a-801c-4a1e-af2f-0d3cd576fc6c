package br.com.pactosolucoes.integracao.importacao;


import org.json.JSONObject;

public class TelefoneJSON {
    private String tipo;
    private String numero;
    private String descricao;

    public TelefoneJSON() {

    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TelefoneJSON(JSONObject json) {
        this.tipo = json.optString("tipo");
        this.numero = json.optString("numero");
        this.descricao = json.optString("descricao");
    }

    public TelefoneJSON(String numero, String tipo, String descricao) {
        this.tipo = tipo;
        this.numero = numero;
        this.descricao = descricao;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }
}

package br.com.pactosolucoes.integracao.importacao;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class ClienteJSON extends SuperJSON{

    private String codigoCliente;
    private String nomeCompleto;
    private String matricula;
    private String dataCadastro;
    private String sexo;
    private String estadoCivil;
    private String grauInstrucao;
    private String profissao;
    private String rguf;
    private String naturalidade;
    private String escolaridade;
    private String rg;
    private String nacionalidade;
    private String categoria;
    private List<Integer> classificacoes;
    private String orgaoEmissaoRG;
    private String ufEmissaoRG;
    private String cpf;
    private String dataNascimento;
    private String codigoAcesso;
    private String senha;
    private EnderecoJSON endereco;
    private String email;
    private String observacao;
    private String orientador;
    private String consultor;
    private String nomePai;
    private String nomeMae;
    private List<TelefoneJSON> telefones;
    private boolean sesc;
    private Double renda;
    private String matriculasesc;
    private String nomeSocial;
    private String validadeCartaoSesc;
    private Long idExterno;
    private String codigoAcessoAlternativo;
    private Long matriculaExterna;

    private String responsavelFinanceiro;

    private String cpfResponsavel;
    private String cpfMae;
    private String cpfPai;

    private boolean optin = false;

    public ClienteJSON(){

    }
    public ClienteJSON(JSONObject json){
        this.codigoCliente = json.optString("codigoCliente");
        this.nomeCompleto = json.optString("nomeCompleto");
        this.matricula = json.optString("matricula");
        this.dataCadastro = json.optString("dataCadastro");
        this.sexo = json.optString("sexo");
        this.estadoCivil = json.optString("estadoCivil");
        this.grauInstrucao = json.optString("grauInstrucao");
        this.profissao = json.optString("profissao");
        this.rguf = json.optString("rguf");
        this.naturalidade = json.optString("naturalidade");
        this.rg = json.optString("rg");
        this.orgaoEmissaoRG = json.optString("orgaoEmissaoRG");
        this.ufEmissaoRG = json.optString("ufEmissaoRG");
        this.cpf = json.optString("cpf");
        this.dataNascimento = json.optString("dataNascimento");
        this.codigoAcesso = json.optString("codigoAcesso");
        this.senha = json.optString("senha");
        this.endereco = new EnderecoJSON(json.optJSONObject("endereco"));
        this.nacionalidade = json.optString("nacionalidade");
        this.email = json.optString("email");
        this.observacao = json.optString("observacao");
        this.orientador = json.optString("orientador");
        this.consultor = json.optString("consultor");
        this.nomePai = json.optString("nomePai");
        this.cpfPai = json.optString("cpfPai");
        this.nomeMae = json.optString("nomeMae");
        this.cpfMae = json.optString("cpfMae");
        this.escolaridade = json.optString("escolaridade");
        this.telefones = new ArrayList<TelefoneJSON>();
        JSONArray telefones = json.optJSONArray("telefones");
        if(telefones != null){
            for(int i = 0; i < telefones.length(); i++){
                this.telefones.add(new TelefoneJSON(telefones.optJSONObject(i)));
            }
        }

        this.classificacoes = new ArrayList<Integer>();
        JSONArray classificacoes = json.optJSONArray("classificacoes");
        if(classificacoes != null){
            for(int i = 0; i < classificacoes.length(); i++){
                this.classificacoes.add(classificacoes.optInt(i));
            }
        }

        this.categoria = json.optString("categoria");
        this.idExterno  = json.optLong("idExterno");
        this.codigoAcessoAlternativo = json.optString("codigoAcessoAlternativo");
        this.matriculaExterna = json.optLong("matriculaExterna");
        this.sesc = json.optBoolean("sesc");
        if(this.sesc){
            this.optin = json.optBoolean("optin");
            renda = json.optDouble("renda");
            matriculasesc = json.optString("matriculasesc");
            nomeSocial = json.optString("nomesocial");
            validadeCartaoSesc = json.optString("validadecartaosesc");
            responsavelFinanceiro = json.optString("responsavelFinanceiro");
            cpfResponsavel =  json.optString("cpfResponsavel");

            try {
                if (UteisValidacao.emptyNumber(this.idExterno)) {
                    idExterno = Long.valueOf(matriculasesc);
                }
                if (UteisValidacao.emptyNumber(this.matriculaExterna)) {
                    matriculaExterna = Long.valueOf(matriculasesc);
                }
            } catch (Exception ignored){

            }
        }

    }


    public String getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(String codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getNomeCompleto() {
        return nomeCompleto;
    }

    public void setNomeCompleto(String nomeCompleto) {
        this.nomeCompleto = nomeCompleto;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(String dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getEstadoCivil() {
        return estadoCivil;
    }

    public void setEstadoCivil(String estadoCivil) {
        this.estadoCivil = estadoCivil;
    }

    public String getGrauInstrucao() {
        return grauInstrucao;
    }

    public void setGrauInstrucao(String grauInstrucao) {
        this.grauInstrucao = grauInstrucao;
    }

    public String getProfissao() {
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public String getNaturalidade() {
        return naturalidade;
    }

    public void setNaturalidade(String naturalidade) {
        this.naturalidade = naturalidade;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getOrgaoEmissaoRG() {
        return orgaoEmissaoRG;
    }

    public void setOrgaoEmissaoRG(String orgaoEmissaoRG) {
        this.orgaoEmissaoRG = orgaoEmissaoRG;
    }

    public String getUfEmissaoRG() {
        return ufEmissaoRG;
    }

    public void setUfEmissaoRG(String ufEmissaoRG) {
        this.ufEmissaoRG = ufEmissaoRG;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public EnderecoJSON getEndereco() {
        return endereco;
    }

    public void setEndereco(EnderecoJSON endereco) {
        this.endereco = endereco;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getOrientador() {
        return orientador;
    }

    public void setOrientador(String orientador) {
        this.orientador = orientador;
    }

    public String getConsultor() {
        return consultor;
    }

    public void setConsultor(String consultor) {
        this.consultor = consultor;
    }

    public String getNomePai() {
        return nomePai;
    }

    public void setNomePai(String nomePai) {
        this.nomePai = nomePai;
    }

    public String getNomeMae() {
        return nomeMae;
    }

    public void setNomeMae(String nomeMae) {
        this.nomeMae = nomeMae;
    }

    public List<TelefoneJSON> getTelefones() {
        return telefones;
    }

    public void setTelefones(List<TelefoneJSON> telefones) {
        this.telefones = telefones;
    }

    public boolean isSesc() {
        return sesc;
    }

    public void setSesc(boolean sesc) {
        this.sesc = sesc;
    }

    public Double getRenda() {
        return renda;
    }

    public void setRenda(Double renda) {
        this.renda = renda;
    }

    public String getMatriculasesc() {
        return matriculasesc;
    }

    public void setMatriculasesc(String matriculasesc) {
        this.matriculasesc = matriculasesc;
    }

    public String getNomeSocial() {
        return nomeSocial;
    }

    public void setNomeSocial(String nomeSocial) {
        this.nomeSocial = nomeSocial;
    }

    public String getValidadeCartaoSesc() {
        return validadeCartaoSesc;
    }

    public void setValidadeCartaoSesc(String validadeCartaoSesc) {
        this.validadeCartaoSesc = validadeCartaoSesc;
    }

    public String getEscolaridade() {
        return escolaridade;
    }

    public void setEscolaridade(String escolaridade) {
        this.escolaridade = escolaridade;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public List<Integer> getClassificacoes() {
        return classificacoes;
    }

    public void setClassificacoes(List<Integer> classificacoes) {
        this.classificacoes = classificacoes;
    }

    public String getNacionalidade() {
        return nacionalidade;
    }

    public void setNacionalidade(String nacionalidade) {
        this.nacionalidade = nacionalidade;
    }

    public String getRguf() {
        return rguf;
    }

    public void setRguf(String rguf) {
        this.rguf = rguf;
    }

    public Long getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Long idExterno) {
        this.idExterno = idExterno;
    }
    
    public String getCodigoAcessoAlternativo() {
        return codigoAcessoAlternativo;
    }

    public void setCodigoAcessoAlternativo(String codigoAcessoAlternativo) {
        this.codigoAcessoAlternativo = codigoAcessoAlternativo;
    }
    
    public Long getMatriculaExterna() {
        return matriculaExterna;
    }

    public void setMatriculaExterna(Long matriculaExterna) {
        this.matriculaExterna = matriculaExterna;
    }

    public String trim(String s){
        return s == null ? "" : s.trim();
    }

    public void montarClienteInadimplente(JSONObject json){
        this.nomeCompleto = trim(json.optString("NOMEALUNO"));
        this.endereco = new EnderecoJSON();
        this.endereco.setLogradouro(trim(json.optString("ENDERECO")));
        this.endereco.setCep(trim(json.optString("CEP")));
        this.endereco.setBairro(trim(json.optString("BAIRRO")));
        this.endereco.setCidade(trim(json.optString("MUNICIPIO")));
        this.endereco.setUf(trim(json.optString("ESTADO")));
        this.endereco.setNumero(trim(json.optString("NUMERO")));
        this.dataCadastro = Uteis.getData(Calendario.hoje());
        this.sexo = json.optString("SEXO").equals("2") ? "F" : "M";
        this.rg = trim(json.optString("RGALUNO"));
        this.cpf = trim(json.optString("CPFALUNO"));
        try {
            this.dataNascimento = Uteis.getData(Uteis.getDate(json.optString("DTANIVERSARIO"), "YYYY-MM-dd'T'HH:mm:ss"));
        } catch (Exception ignored) { }
        this.email = json.optString("EMAIL");
        String tel = trim(json.optString("TELEFONE"));
        String cel = trim(json.optString("CELULAR"));
        this.telefones = new ArrayList<TelefoneJSON>();
        if(!UteisValidacao.emptyString(tel)){
            this.telefones.add(new TelefoneJSON(tel, "RE", "RESIDENCIAL"));
        }
        if(!UteisValidacao.emptyString(cel)){
            this.telefones.add(new TelefoneJSON(cel, "CE", "CELULAR"));
        }

        this.nacionalidade = json.optString("nacionalidade");

        this.idExterno = json.optLong("MATRICULA");
        this.matriculaExterna = json.optLong("MATRICULA");
    }

    public boolean isOptin() {
        return optin;
    }

    public void setOptin(boolean optin) {
        this.optin = optin;
    }

    public String getResponsavelFinanceiro() {
        return responsavelFinanceiro;
    }

    public void setResponsavelFinanceiro(String responsavelFinanceiro) {
        this.responsavelFinanceiro = responsavelFinanceiro;
    }

    public String getCpfResponsavel() {
        return cpfResponsavel;
    }

    public void setCpfResponsavel(String cpfResponsavel) {
        this.cpfResponsavel = cpfResponsavel;
    }

    public String getCpfMae() { return cpfMae; }
    public void setCpfMae(String cpfMae) { this.cpfMae = cpfMae; }

    public String getCpfPai() { return cpfPai; }
    public void setCpfPai(String cpfPai) { this.cpfPai = cpfPai; }
}
    

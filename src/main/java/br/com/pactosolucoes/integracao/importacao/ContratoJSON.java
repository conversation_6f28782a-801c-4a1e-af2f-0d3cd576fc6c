/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.importacao;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public class ContratoJSON extends SuperJSON{

    private String codigoContrato;
    private Integer idExterno;
    private Long idClienteExterno;
    private Integer pessoa;
    private String dataCadastro;
    private String dataInicio;
    private String dataFinal;
    private Integer duracao;
    private Double valorMatricula;
    private Double valorContrato;
    private List<ModalidadeJSON> modalidades;
    private String consultor;
    private String observacaoContrato;
    private Boolean renovacaoContrato = false;
    private Integer codigoPlano;
    private Integer codigoMatricula;
    private Boolean contratoComTurma = false;
    private List<Integer> horariosTurma;
    private Integer qtdParcelas;

    public ContratoJSON() {
    }

    public ContratoJSON(JSONObject json) {
        this.codigoContrato = json.optString("codigoContrato");
        this.idExterno = json.optInt("idExterno");
        this.idClienteExterno = json.optLong("codigoChaveEstrangeira");
        if(UteisValidacao.emptyNumber(this.idClienteExterno)){
            try{
                this.idClienteExterno = Long.valueOf(json.optString("codigoChaveEstrangeira"));
            } catch (Exception ignored){

            }
        }
        this.pessoa = json.optInt("pessoa");
        this.dataCadastro = json.optString("dataCadastro");
        this.dataInicio = json.optString("dataInicio");
        this.dataFinal = json.optString("dataFinal");
        this.duracao = json.optInt("duracao");
        this.valorMatricula = json.optDouble("valorMatricula");
        this.valorContrato = json.optDouble("valorContrato");
        this.modalidades = new ArrayList<ModalidadeJSON>();
        JSONArray modalidades = json.optJSONArray("modalidades");
        if(modalidades != null){
            for(int i = 0; i < modalidades.length(); i++){
                ModalidadeJSON modalidadeJSON = new ModalidadeJSON(modalidades.optJSONObject(i));
                if(!this.contratoComTurma) {
                    this.contratoComTurma = modalidadeJSON.getUtilizarTurma();
                }
                this.modalidades.add(modalidadeJSON);
            }
        }
        this.consultor = json.optString("consultor");
        this.observacaoContrato = json.optString("observacaoContrato");
        this.renovacaoContrato = json.optBoolean("renovacaoContrato");
        this.codigoPlano = json.optInt("codigoPlano");
        this.codigoMatricula = json.optInt("codigoMatricula");
        try {
            JSONArray horariosTurma = json.optJSONArray("horariosTurma");
            this.horariosTurma = new ArrayList<>();
            for (int i = 0; i < horariosTurma.length(); i++) {
                this.horariosTurma.add(horariosTurma.getJSONObject(i).optInt("codigo"));
            }
        } catch (Exception ignore) {
        }
        this.qtdParcelas = json.optInt("qtdParcelas");
    }

    public String getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(String codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Integer getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Integer idExterno) {
        this.idExterno = idExterno;
    }

    public Long getIdClienteExterno() {
        return idClienteExterno;
    }

    public void setIdClienteExterno(Long idClienteExterno) {
        this.idClienteExterno = idClienteExterno;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(String dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Double getValorMatricula() {
        return valorMatricula;
    }

    public void setValorMatricula(Double valorMatricula) {
        this.valorMatricula = valorMatricula;
    }

    public Double getValorContrato() {
        return valorContrato;
    }

    public void setValorContrato(Double valorContrato) {
        this.valorContrato = valorContrato;
    }

    public List<ModalidadeJSON> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ModalidadeJSON> modalidades) {
        this.modalidades = modalidades;
    }

    public String getConsultor() {
        return consultor;
    }

    public void setConsultor(String consultor) {
        this.consultor = consultor;
    }

    public Boolean isRenovacaoContrato() {
        if(renovacaoContrato == null) {
            return false;
        }
        return renovacaoContrato;
    }

    public void setRenovacaoContrato(Boolean renovacaoContrato) {
        this.renovacaoContrato = renovacaoContrato;
    }

    public String getObservacaoContrato() {
        return observacaoContrato;
    }

    public void setObservacaoContrato(String observacaoContrato) {
        this.observacaoContrato = observacaoContrato;
    }

    public Integer getCodigoPlano() {return codigoPlano;}

    public void setCodigoPlano(Integer codigoPlano) {this.codigoPlano = codigoPlano;}

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public List<Integer> getHorariosTurma() {
        return horariosTurma;
    }

    public void setHorariosTurma(List<Integer> horariosTurma) {
        this.horariosTurma = horariosTurma;
    }

    public Boolean getContratoComTurma() {
        if(contratoComTurma == null){
            contratoComTurma = false;
        }
        return contratoComTurma;
    }

    public void setContratoComTurma(Boolean contratoComTurma) {
        this.contratoComTurma = contratoComTurma;
    }

    public Integer getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(Integer qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }
}

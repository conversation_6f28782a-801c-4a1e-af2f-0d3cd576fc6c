/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.importacao;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class AcessoClienteJSON extends SuperJSON {
    private Long idExternoCliente;
    private Date data;

    public AcessoClienteJSON() {
    }

    public AcessoClienteJSON(Long idExternoCliente, JSONObject json) throws Exception {
        this.idExternoCliente = idExternoCliente;
        try {
            this.data = Uteis.getDate(json.getString("data"), "dd/MM/yyyy hh:mm");
        } catch (Exception e) {
            throw new Exception("Formato da data informado está errado. Correto é dd/MM/yyyy hh:mm");
        }
    }

    public Long getIdExternoCliente() {
        return idExternoCliente;
    }

    public void setIdExternoCliente(Long idExternoCliente) {
        this.idExternoCliente = idExternoCliente;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

}

package br.com.pactosolucoes.integracao.amigoFit;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.crm.IndicadoVO;
import org.json.JSONObject;

import java.util.Date;

public class ClienteAmigoFitJSON extends SuperJSON {

    private int codigoclienteIndicou;
    private String nome;
    private String username;
    private String senha;
    private String token;
    private String contato;
    private String email;
    private String cpf;
    private double valorPagamento;
    private Date dataVencimentoParcela;
    private Date dataPagamentoParcela;
    private String situacao;
    private Date dataCadastro;
    private Integer pontos;

    public int getCodigoclienteIndicou() {
        return codigoclienteIndicou;
    }

    public void setCodigoclienteIndicou(int codigoclienteIndicou) {
        this.codigoclienteIndicou = codigoclienteIndicou;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getContato() {
        return contato;
    }

    public void setContato(String contato) {
        this.contato = contato;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public static JSONObject toJsonCadastro(ClienteVO clienteVO){

        JSONObject object = new JSONObject();
        object.put("password",clienteVO.getSenhaUsuarioAmigoFit());
        object.put("cpf",clienteVO.getPessoa().getCfp());
        object.put("nome",clienteVO.getPessoa().getNome());
        object.put("email",clienteVO.getPessoa().getEmailCorrespondencia());
        object.put("contato",clienteVO.getPessoa().getTelefonesCelular());
        object.put("banco",clienteVO.getBancoAmigoFit().getNome());
        object.put("agencia",clienteVO.getAgenciaAmigoFit()+"-"+clienteVO.getDigitoAgenciaAmigoFit());
        object.put("conta",clienteVO.getContaAmigoFit()+"-"+clienteVO.getDigitoContaAmigoFit());
        object.put("tipo",clienteVO.getContaCorrenteAmigoFit()==true?"CC":"CI");

        return object;
    }

    public static JSONObject toJsonIndicacaoBonus(IndicadoVO indicadoVO){

        JSONObject object = new JSONObject();
        object.append("nome",indicadoVO.getNomeIndicado());
        object.append("cpf",indicadoVO.getCpf());
        object.append("contato",indicadoVO.getTelefone());

        return object;
    }

    public static JSONObject toJSONPagamento(Date dataPagamento,Date dataVencimento, String cpf, double valorPagamento){
        JSONObject object = new JSONObject();
        object.put("data_pagamento",dataPagamento.toInstant());
        object.put("data_vencimento",dataVencimento.toInstant());
        object.put("cpf",cpf);
        object.put("valor",valorPagamento);

        return object;
    }

    public double getValorPagamento() {
        return valorPagamento;
    }

    public void setValorPagamento(double valorPagamento) {
        this.valorPagamento = valorPagamento;
    }

    public Date getDataVencimentoParcela() {
        return dataVencimentoParcela;
    }

    public void setDataVencimentoParcela(Date dataVencimentoParcela) {
        this.dataVencimentoParcela = dataVencimentoParcela;
    }

    public Date getDataPagamentoParcela() {
        return dataPagamentoParcela;
    }

    public void setDataPagamentoParcela(Date dataPagamentoParcela) {
        this.dataPagamentoParcela = dataPagamentoParcela;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getDataCadastro_Apresentar() {
        return getDataCadastro().toString();
    }

    public static JSONObject toJsonConsulta(ClienteAmigoFitJSON cliente){

        JSONObject object = new JSONObject();
        object.put("codigo",cliente.getCodigoclienteIndicou());
        object.put("cpf",cliente.getCpf());
        object.put("nome",cliente.getNome());
        object.put("email",cliente.getEmail());
        object.put("dataCadastro",cliente.getDataCadastro_Apresentar());
        object.put("situacao",cliente.getSituacao());
        object.put("valorTotal",cliente.getValorPagamento());
        object.put("dataPag",cliente.getDataPagamentoParcela().toString());
        object.put("pontos", cliente.getPontos());

        return object;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }
}

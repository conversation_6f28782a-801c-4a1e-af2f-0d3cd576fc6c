package br.com.pactosolucoes.integracao.login.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class InfoBloqueioDTO extends SuperTO {
    private String chave;
    private int codigoEmpresa;
    private String dataExpiracao;
    private String dataSuspensao;
    private Integer totalDiasSolicitados;
    private String linkWhatsapp;
    private Boolean sistemaBloqueado = false;
    private Boolean sistemaSuspenso = false;
    private Boolean perfilAdministrador = false;
    private String tipoPerfilAcesso;
    private Boolean funcionamentoParcial = false;
    private Integer  diasParaBloqueio;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public int getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(int codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getDataExpiracao() {
        return dataExpiracao;
    }

    public void setDataExpiracao(String dataExpiracao) {
        this.dataExpiracao = dataExpiracao;
    }

    public String getDataSuspensao() {
        return dataSuspensao;
    }

    public void setDataSuspensao(String dataSuspensao) {
        this.dataSuspensao = dataSuspensao;
    }

    public Integer getDiasParaBloqueio() {
        return diasParaBloqueio;
    }

    public void setDiasParaBloqueio(Integer diasParaBloqueio) {
        this.diasParaBloqueio = diasParaBloqueio;
    }

    public Integer getTotalDiasSolicitados() {
        return totalDiasSolicitados;
    }

    public void setTotalDiasSolicitados(Integer totalDiasSolicitados) {
        this.totalDiasSolicitados = totalDiasSolicitados;
    }

    public String getLinkWhatsapp() {
        return linkWhatsapp;
    }

    public void setLinkWhatsapp(String linkWhatsapp) {
        this.linkWhatsapp = linkWhatsapp;
    }

    public Boolean getSistemaBloqueado() {
        return sistemaBloqueado;
    }

    public void setSistemaBloqueado(Boolean sistemaBloqueado) {
        this.sistemaBloqueado = sistemaBloqueado;
    }

    public Boolean getSistemaSuspenso() {
        return sistemaSuspenso;
    }

    public void setSistemaSuspenso(Boolean sistemaSuspenso) {
        this.sistemaSuspenso = sistemaSuspenso;
    }

    public Boolean getPerfilAdministrador() {
        return perfilAdministrador;
    }

    public void setPerfilAdministrador(Boolean perfilAdministrador) {
        this.perfilAdministrador = perfilAdministrador;
    }

    public String getTipoPerfilAcesso() {
        return this.tipoPerfilAcesso;
    }

    public void setTipoPerfilAcesso(final String tipoPerfilAcesso) {
        this.tipoPerfilAcesso = tipoPerfilAcesso;
    }

    public Boolean getFuncionamentoParcial() {
        return funcionamentoParcial;
    }

    public void setFuncionamentoParcial(Boolean funcionamentoParcial) {
        this.funcionamentoParcial = funcionamentoParcial;
    }
}

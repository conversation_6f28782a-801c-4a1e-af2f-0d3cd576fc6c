package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoContratoDTO extends SuperJSON {

    private Integer codigo;
    private Integer plano;
    private String planoDescricao;
    private String tipoContrato;
    private Double valor;
    private String dataLancamento;
    private String dataMatricula;
    private String dataInicio;
    private String dataFim;
    private JSONArray modalidades;
    private List<IntegracaoProdutoDTO> produtos;

    public IntegracaoContratoDTO(ContratoVO contratoVO) {
        this.codigo = contratoVO.getCodigo();
        this.plano = contratoVO.getPlano().getCodigo();
        this.planoDescricao = contratoVO.getPlano().getDescricao();
        this.tipoContrato = contratoVO.getSituacaoContrato();
        this.valor = contratoVO.getValorFinal();
        this.dataLancamento = Calendario.getDataAplicandoFormatacao(contratoVO.getDataLancamento(), "dd/MM/yyyy HH:mm:ss");
        this.dataInicio = Calendario.getDataAplicandoFormatacao(contratoVO.getVigenciaDe(), "dd/MM/yyyy");
        this.dataFim = Calendario.getDataAplicandoFormatacao(contratoVO.getVigenciaAteAjustada(), "dd/MM/yyyy");
        this.modalidades = getModalidades(contratoVO.getContratoModalidadeVOs());
        this.dataMatricula = Calendario.getDataAplicandoFormatacao(contratoVO.getDataMatricula(), "dd/MM/yyyy");
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public String getPlanoDescricao() {
        return planoDescricao;
    }

    public void setPlanoDescricao(String planoDescricao) {
        this.planoDescricao = planoDescricao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFim() {
        return dataFim;
    }

    public void setDataFim(String dataFim) {
        this.dataFim = dataFim;
    }

    public String getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(String tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    private JSONArray getModalidades(List<ContratoModalidadeVO> listaModalidades) {
        if (UteisValidacao.emptyList(listaModalidades)) {
            return null;
        }

        JSONArray array = new JSONArray();
        int i = 0;
        for (ContratoModalidadeVO obj : listaModalidades) {
            JSONObject json = new JSONObject();
            json.put("seq", ++i);
            json.put("modalidade", obj.getModalidade().getNome());
            array.put(json);
        }
        return array;
    }

    public JSONArray getModalidades() {
        return modalidades;
    }

    public void setModalidades(JSONArray modalidades) {
        this.modalidades = modalidades;
    }

    public String getDataMatricula() {
        return dataMatricula;
    }

    public void setDataMatricula(String dataMatricula) {
        this.dataMatricula = dataMatricula;
    }

    public List<IntegracaoProdutoDTO> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<IntegracaoProdutoDTO> produtos) {
        this.produtos = produtos;
    }
}

package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoPagamentoDTO extends SuperJSON {

    private Integer recibo;
    private Double valor;
    private String dataPagamento;
    private List<IntegracaoPagamentoParcelaDTO> parcelas;
    private List<IntegracaoProdutoDTO> produtos;

    public IntegracaoPagamentoDTO(ReciboPagamentoVO reciboPagamentoVO) {
        this.recibo = reciboPagamentoVO.getCodigo();
        this.valor = reciboPagamentoVO.getValorTotal();
        this.dataPagamento = Calendario.getDataAplicandoFormatacao(reciboPagamentoVO.getData(), "dd/MM/yyyy HH:mm:ss");

        try {
            this.parcelas = new ArrayList<>();
            Set<Integer> mapa = new HashSet<>();
            for (MovPagamentoVO movPagamentoVO : reciboPagamentoVO.getPagamentosDesteRecibo()) {
                for (PagamentoMovParcelaVO mov : movPagamentoVO.getPagamentoMovParcelaVOs()) {
                    if (!mapa.contains(mov.getMovParcela().getCodigo())) {
                        this.parcelas.add(new IntegracaoPagamentoParcelaDTO(mov.getMovParcela()));
                        mapa.add(mov.getMovParcela().getCodigo());
                    }
                }
            }
        } catch (Exception ignored) {
        }
    }

    public String getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(String dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getRecibo() {
        return recibo;
    }

    public void setRecibo(Integer recibo) {
        this.recibo = recibo;
    }

    public List<IntegracaoPagamentoParcelaDTO> getParcelas() {
        return parcelas;
    }

    public void setParcelas(List<IntegracaoPagamentoParcelaDTO> parcelas) {
        this.parcelas = parcelas;
    }

    public List<IntegracaoProdutoDTO> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<IntegracaoProdutoDTO> produtos) {
        this.produtos = produtos;
    }
}

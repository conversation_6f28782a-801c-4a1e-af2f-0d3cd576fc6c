package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoUsuarioDTO extends SuperJSON {

    private Integer codigo;
    private String username;
    private String nome;

    public IntegracaoUsuarioDTO(UsuarioVO usuarioVO) {
        this.codigo = usuarioVO.getCodigo();
        this.username = usuarioVO.getUsername();
        this.nome = usuarioVO.getNome();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}

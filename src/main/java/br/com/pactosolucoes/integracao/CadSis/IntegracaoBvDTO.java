package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaClienteVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoBvDTO extends SuperJSON {

    private Integer codigo;
    private String consultor;
    private String tipo;
    private String observacao;
    private String evento;
    private Integer origem;
    private String origemDescricao;
    private String dataRegistro;
    private List<IntegracaoBVPerguntaCliente> itensBVPerguntaCliente;

    public IntegracaoBvDTO(QuestionarioClienteVO questionarioClienteVO) {
        this.codigo = questionarioClienteVO.getCodigo();
        this.consultor = questionarioClienteVO.getConsultor() != null ? questionarioClienteVO.getConsultor().getPessoa().getNome() : null;
        this.tipo = questionarioClienteVO.getTipoBV() != null ? questionarioClienteVO.getTipoBV().getDescricao() : null;
        this.observacao = questionarioClienteVO.getObservacao();
        this.evento = questionarioClienteVO.getEventoVO() != null ? questionarioClienteVO.getEventoVO().getDescricao() : null;
        this.origem = questionarioClienteVO.getOrigemSistemaEnum() != null ? questionarioClienteVO.getOrigemSistemaEnum().getCodigo() : 0;
        this.origemDescricao = questionarioClienteVO.getOrigemSistemaEnum() != null ? questionarioClienteVO.getOrigemSistemaEnum().getDescricao() : null;
        this.dataRegistro = Calendario.getDataAplicandoFormatacao(questionarioClienteVO.getUltimaAtualizacao(), "dd/MM/yyyy HH:mm:ss");

        if (questionarioClienteVO.getQuestionarioPerguntaClienteVOs() != null) {
            this.itensBVPerguntaCliente = new ArrayList<>();
            for (QuestionarioPerguntaClienteVO item : (List<QuestionarioPerguntaClienteVO>)questionarioClienteVO.getQuestionarioPerguntaClienteVOs()) {
                this.itensBVPerguntaCliente.add(new IntegracaoBVPerguntaCliente(item));
            }
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getConsultor() {
        return consultor;
    }

    public void setConsultor(String consultor) {
        this.consultor = consultor;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getEvento() {
        return evento;
    }

    public void setEvento(String evento) {
        this.evento = evento;
    }

    public Integer getOrigem() {
        return origem;
    }

    public void setOrigem(Integer origem) {
        this.origem = origem;
    }

    public String getOrigemDescricao() {
        return origemDescricao;
    }

    public void setOrigemDescricao(String origemDescricao) {
        this.origemDescricao = origemDescricao;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public List<IntegracaoBVPerguntaCliente> getItensBVPerguntaCliente() {
        return itensBVPerguntaCliente;
    }

    public void setItensBVPerguntaCliente(List<IntegracaoBVPerguntaCliente> itensBVPerguntaCliente) {
        this.itensBVPerguntaCliente = itensBVPerguntaCliente;
    }
}

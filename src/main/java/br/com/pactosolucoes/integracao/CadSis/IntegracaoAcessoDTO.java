package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.utilitarias.Calendario;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoAcessoDTO extends SuperJSON {

    private String dataEntrada;
    private String dataSaida;
    private String dataRegistro;
    private String situacao;
    private String sentido;
    private String coletor;
    private String meioEntrada;
    private String meioSaida;

    public IntegracaoAcessoDTO(AcessoClienteVO obj) {
        if(obj.getDataHoraEntrada() != null) {
            this.dataEntrada = Calendario.getDataAplicandoFormatacao(obj.getDataHoraEntrada(), "dd/MM/yyyy HH:mm:ss");
        }
        if(obj.getDataHoraSaida() != null) {
            this.dataSaida = Calendario.getDataAplicandoFormatacao(obj.getDataHoraSaida(), "dd/MM/yyyy HH:mm:ss");
        }
        if(obj.getDataRegistro() != null) {
            this.dataRegistro = Calendario.getDataAplicandoFormatacao(obj.getDataRegistro(), "dd/MM/yyyy HH:mm:ss");
        }
        this.situacao = obj.getSituacao() != null ? obj.getSituacao().getDescricao() : null;
        this.sentido = obj.getSentido();
        this.coletor = obj.getColetor() != null ? obj.getColetor().getDescricao() : null;
        this.meioEntrada = obj.getMeioIdentificacaoEntrada() != null ? obj.getMeioIdentificacaoEntrada().getDescricao() : null;
        this.meioSaida = obj.getMeioIdentificacaoSaida() != null ? obj.getMeioIdentificacaoSaida().getDescricao() : null;
    }

    public String getDataEntrada() {
        return dataEntrada;
    }

    public void setDataEntrada(String dataEntrada) {
        this.dataEntrada = dataEntrada;
    }

    public String getDataSaida() {
        return dataSaida;
    }

    public void setDataSaida(String dataSaida) {
        this.dataSaida = dataSaida;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSentido() {
        return sentido;
    }

    public void setSentido(String sentido) {
        this.sentido = sentido;
    }

    public String getMeioEntrada() {
        return meioEntrada;
    }

    public void setMeioEntrada(String meioEntrada) {
        this.meioEntrada = meioEntrada;
    }

    public String getMeioSaida() {
        return meioSaida;
    }

    public void setMeioSaida(String meioSaida) {
        this.meioSaida = meioSaida;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getColetor() {
        return coletor;
    }

    public void setColetor(String coletor) {
        this.coletor = coletor;
    }
}

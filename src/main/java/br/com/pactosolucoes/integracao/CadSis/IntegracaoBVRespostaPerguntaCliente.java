package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.basico.PerguntaClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaClienteVO;
import negocio.comuns.basico.RespostaPergClienteVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoBVRespostaPerguntaCliente extends SuperJSON {

    protected Integer codigo;
    protected Integer perguntaCliente;
    protected Integer marcado;
    protected String descricaoRespota;
    protected Boolean respostaOpcao;

    public IntegracaoBVRespostaPerguntaCliente(RespostaPergClienteVO respostaPergClienteVO) {
        if (respostaPergClienteVO == null) {
            return;
        }

        this.codigo = respostaPergClienteVO.getCodigo();
        this.perguntaCliente = respostaPergClienteVO.getPerguntaCliente();
        this.marcado = respostaPergClienteVO.getMarcado();
        this.descricaoRespota = respostaPergClienteVO.getDescricaoRespota();
        this.respostaOpcao = respostaPergClienteVO.getRespostaOpcao();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPerguntaCliente() {
        return perguntaCliente;
    }

    public void setPerguntaCliente(Integer perguntaCliente) {
        this.perguntaCliente = perguntaCliente;
    }

    public Integer getMarcado() {
        return marcado;
    }

    public void setMarcado(Integer marcado) {
        this.marcado = marcado;
    }

    public String getDescricaoRespota() {
        return descricaoRespota;
    }

    public void setDescricaoRespota(String descricaoRespota) {
        this.descricaoRespota = descricaoRespota;
    }

    public Boolean getRespostaOpcao() {
        return respostaOpcao;
    }

    public void setRespostaOpcao(Boolean respostaOpcao) {
        this.respostaOpcao = respostaOpcao;
    }
}
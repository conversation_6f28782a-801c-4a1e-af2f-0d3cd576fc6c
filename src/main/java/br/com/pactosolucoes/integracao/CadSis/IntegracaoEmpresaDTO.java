package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.basico.EmpresaVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoEmpresaDTO extends SuperJSON {

    private Integer codigo;
    private String nome;
    private String cnpj;
    private String idExterno;

    public IntegracaoEmpresaDTO(EmpresaVO empresaVO) {
        this.codigo = empresaVO.getCodigo();
        this.nome = empresaVO.getNome();
        this.cnpj = empresaVO.getCNPJ();
        this.idExterno = empresaVO.getIdExterno();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }
}

package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoClienteDTO extends SuperJSON {

    private Integer codigoCliente;
    private Integer codigoPessoa;
    private String matricula;
    private String nome;
    private String sexo;
    private String cpf;
    private String dataNascimento;
    private String dataCadastro;
    private String dataMatricula;
    private String situacao;
    private JSONArray telefones;
    private JSONArray emails;
    private JSONArray enderecos;

    public IntegracaoClienteDTO(ClienteVO clienteVO) {
        this.codigoCliente = clienteVO.getCodigo();
        this.matricula = clienteVO.getMatricula();
        this.situacao = clienteVO.getSituacao();
        try {
            this.dataMatricula = Uteis.getData(clienteVO.getSituacaoClienteSinteticoVO().getDataMatricula());
        } catch (Exception ex) {
            this.dataMatricula = "";
        }
        preencherDados(clienteVO.getPessoa());
    }

    public IntegracaoClienteDTO(VendaAvulsaVO vendaAvulsaVO) {
        this.nome = vendaAvulsaVO.getNomeComprador();
    }

    public void preencherDados(PessoaVO pessoaVO) {
        this.codigoPessoa = pessoaVO.getCodigo();
        this.nome = pessoaVO.getNome();
        this.cpf = pessoaVO.getCfp();
        this.sexo = pessoaVO.getSexo();
        this.dataCadastro = pessoaVO.getDataCadastro_Apresentar();
        this.dataNascimento = pessoaVO.getDataNasc_Apresentar();
        this.telefones = getTelefones(pessoaVO.getTelefoneVOs());
        this.emails = getEmails(pessoaVO.getEmailVOs());
        this.enderecos = getEnderecos(pessoaVO.getEnderecoVOs());
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(String dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public JSONArray getTelefones() {
        return telefones;
    }

    public void setTelefones(JSONArray telefones) {
        this.telefones = telefones;
    }

    public JSONArray getEmails() {
        return emails;
    }

    public void setEmails(JSONArray emails) {
        this.emails = emails;
    }

    public JSONArray getEnderecos() {
        return enderecos;
    }

    public void setEnderecos(JSONArray enderecos) {
        this.enderecos = enderecos;
    }

    private JSONArray getTelefones(List<TelefoneVO> listaTelefones) {
        if (UteisValidacao.emptyList(listaTelefones)) {
            return null;
        }

        Ordenacao.ordenarListaReverse(listaTelefones, "codigo");
        JSONArray array = new JSONArray();
        int i = 0;
        for (TelefoneVO obj : listaTelefones) {
            JSONObject json = new JSONObject();
            json.put("seq", ++i);
            json.put("tipo", obj.getTipoTelefone_Apresentar());
            json.put("numero", obj.getNumeroApresentar());
            array.put(json);
        }
        return array;
    }

    private JSONArray getEmails(List<EmailVO> listaEmails) {
        if (UteisValidacao.emptyList(listaEmails)) {
            return null;
        }

        Ordenacao.ordenarListaReverse(listaEmails, "codigo");
        JSONArray array = new JSONArray();
        int i = 0;
        for (EmailVO obj : listaEmails) {
            if (obj.getEmailCorrespondencia() != null &&
                    !obj.getEmailCorrespondencia()) {
                continue;
            }
            JSONObject json = new JSONObject();
            json.put("seq", ++i);
            json.put("email", obj.getEmail());
            array.put(json);
        }
        return array;
    }

    private JSONArray getEnderecos(List<EnderecoVO> listaEndereco) {
        if (UteisValidacao.emptyList(listaEndereco)) {
            return null;
        }

        Ordenacao.ordenarListaReverse(listaEndereco, "codigo");
        JSONArray array = new JSONArray();
        int i = 0;
        for (EnderecoVO obj : listaEndereco) {
            JSONObject json = new JSONObject();
            json.put("seq", ++i);
            json.put("cep", obj.getCep());
            json.put("logradouro", obj.getEndereco());
            json.put("complemento", obj.getComplemento());
            json.put("bairro", obj.getBairro());
            json.put("numero", obj.getNumero());
            array.put(json);
        }
        return array;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getDataMatricula() {
        return dataMatricula;
    }

    public void setDataMatricula(String dataMatricula) {
        this.dataMatricula = dataMatricula;
    }
}

package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisTelefone;
import servicos.vendasonline.dto.VendaDTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoBotConversaDTO extends SuperJSON {
    private String chave;
    private String operacao;
    private String data;
    private String nome;
    private String cpf;
    private String celular;
    private String plano;
    private String email;
    private double valor;
    private String formaPagamento;
    private int nrVezesParcelar;
    private String status;

    public IntegracaoBotConversaDTO(String chave, String operacao, VendaDTO vendaDTO, PlanoVO planoVO, double valor, String data) {
        this.chave = chave;
        this.operacao = operacao;
        this.data = data;
        this.nome = vendaDTO.getNome();
        this.cpf = Uteis.formatarCpfCnpj(vendaDTO.getCpf(), true);
        this.celular = UteisTelefone.removerCaracteresEspeciais(vendaDTO.getTelefone());
        this.plano = planoVO.getDescricao();
        this.email = vendaDTO.getEmail();
        this.valor = valor;
        this.nrVezesParcelar = vendaDTO.getNrVezesDividir();
        this.formaPagamento = "CARTAO DE CREDITO";
        if (this.getOperacao().equalsIgnoreCase("VENDA")) {
            this.status = "APROVADA";
        } else if (this.getOperacao().equalsIgnoreCase("CARRINHO")) {
            this.status = "NAO_APROVADA";
        } else {
            this.status = "DESCONHECIDO";
        }

    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getPlano() {
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public String getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public int getNrVezesParcelar() {
        return nrVezesParcelar;
    }

    public void setNrVezesParcelar(int nrVezesParcelar) {
        this.nrVezesParcelar = nrVezesParcelar;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}

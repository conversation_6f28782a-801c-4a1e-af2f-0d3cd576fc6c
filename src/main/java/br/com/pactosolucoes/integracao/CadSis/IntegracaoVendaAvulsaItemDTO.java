package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.utilitarias.UteisValidacao;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoVendaAvulsaItemDTO extends SuperJSON {

    private Integer codigo;
    private Integer produtoCodigo;
    private String produtoDescricao;
    private String produtoTipo;
    private Integer qtd;
    private Double valorParcial;
    private Double valorFinal;
    private Double valorDescontoManual;
    private boolean descontoManual;
    private IntegracaoUsuarioDTO usuarioDesconto;

    public IntegracaoVendaAvulsaItemDTO(ItemVendaAvulsaVO obj) {
        this.codigo = obj.getCodigo();
        this.produtoCodigo = obj.getProduto().getCodigo();
        this.produtoDescricao = obj.getProduto().getDescricao();
        this.produtoTipo = obj.getProduto().getTipoProduto();
        this.qtd = obj.getQuantidade();
        this.valorFinal = obj.getValorFinalItem();
        this.valorParcial = obj.getValorParcial();
        this.valorDescontoManual = obj.getValorDescontoManual();
        this.descontoManual = obj.getDescontoManual();
        if (obj.getResponsavelAutorizacaoDesconto() != null &&
                !UteisValidacao.emptyNumber(obj.getResponsavelAutorizacaoDesconto().getCodigo())) {
            this.usuarioDesconto = new IntegracaoUsuarioDTO(obj.getResponsavelAutorizacaoDesconto());
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getProdutoCodigo() {
        return produtoCodigo;
    }

    public void setProdutoCodigo(Integer produtoCodigo) {
        this.produtoCodigo = produtoCodigo;
    }

    public String getProdutoDescricao() {
        return produtoDescricao;
    }

    public void setProdutoDescricao(String produtoDescricao) {
        this.produtoDescricao = produtoDescricao;
    }

    public Integer getQtd() {
        return qtd;
    }

    public void setQtd(Integer qtd) {
        this.qtd = qtd;
    }

    public Double getValorParcial() {
        return valorParcial;
    }

    public void setValorParcial(Double valorParcial) {
        this.valorParcial = valorParcial;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }

    public Double getValorDescontoManual() {
        return valorDescontoManual;
    }

    public void setValorDescontoManual(Double valorDescontoManual) {
        this.valorDescontoManual = valorDescontoManual;
    }

    public boolean isDescontoManual() {
        return descontoManual;
    }

    public void setDescontoManual(boolean descontoManual) {
        this.descontoManual = descontoManual;
    }

    public IntegracaoUsuarioDTO getUsuarioDesconto() {
        return usuarioDesconto;
    }

    public void setUsuarioDesconto(IntegracaoUsuarioDTO usuarioDesconto) {
        this.usuarioDesconto = usuarioDesconto;
    }

    public String getProdutoTipo() {
        return produtoTipo;
    }

    public void setProdutoTipo(String produtoTipo) {
        this.produtoTipo = produtoTipo;
    }
}

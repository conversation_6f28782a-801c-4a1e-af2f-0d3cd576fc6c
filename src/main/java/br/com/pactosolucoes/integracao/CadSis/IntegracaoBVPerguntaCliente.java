package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.basico.PerguntaClienteVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaClienteVO;
import negocio.comuns.basico.RespostaPergClienteVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoBVPerguntaCliente extends SuperJSON {

    private Integer codigo;
    private String descricao;
    private String tipoPergunta;
    private Boolean textual;
    private Boolean multipla;
    private Boolean simples;
    private List<IntegracaoBVRespostaPerguntaCliente> itensBVRespostaPerguntaCliente;

    public IntegracaoBVPerguntaCliente(QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO) {
        if (questionarioPerguntaClienteVO.getPerguntaCliente() == null) {
            return;
        }

        PerguntaClienteVO perguntaClienteVO = questionarioPerguntaClienteVO.getPerguntaCliente();
        this.codigo = perguntaClienteVO.getCodigo();
        this.descricao = perguntaClienteVO.getDescricao();
        this.tipoPergunta = perguntaClienteVO.getTipoPergunta();
        this.multipla = perguntaClienteVO.getMultipla();
        this.simples = perguntaClienteVO.getSimples();
        this.textual = perguntaClienteVO.getTextual();

        if (perguntaClienteVO.getRespostaPergClienteVOs() != null) {
            itensBVRespostaPerguntaCliente = new ArrayList<>();
            for (RespostaPergClienteVO respostaPergClienteVO : (List<RespostaPergClienteVO>)perguntaClienteVO.getRespostaPergClienteVOs()) {
                itensBVRespostaPerguntaCliente.add(new IntegracaoBVRespostaPerguntaCliente(respostaPergClienteVO));
            }
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipoPergunta() {
        return tipoPergunta;
    }

    public void setTipoPergunta(String tipoPergunta) {
        this.tipoPergunta = tipoPergunta;
    }

    public Boolean getTextual() {
        return textual;
    }

    public void setTextual(Boolean textual) {
        this.textual = textual;
    }

    public Boolean getMultipla() {
        return multipla;
    }

    public void setMultipla(Boolean multipla) {
        this.multipla = multipla;
    }

    public Boolean getSimples() {
        return simples;
    }

    public void setSimples(Boolean simples) {
        this.simples = simples;
    }

    public List<IntegracaoBVRespostaPerguntaCliente> getItensBVRespostaPerguntaCliente() {
        return itensBVRespostaPerguntaCliente;
    }

    public void setItensBVRespostaPerguntaCliente(List<IntegracaoBVRespostaPerguntaCliente> itensBVRespostaPerguntaCliente) {
        this.itensBVRespostaPerguntaCliente = itensBVRespostaPerguntaCliente;
    }
}

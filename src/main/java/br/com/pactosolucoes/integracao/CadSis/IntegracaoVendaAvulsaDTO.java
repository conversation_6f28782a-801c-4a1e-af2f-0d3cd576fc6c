package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoVendaAvulsaDTO extends SuperJSON {

    private Integer codigo;
    private String tipoComprador;
    private String nomeComprador;
    private Integer origem;
    private String origemDescricao;
    private Double valor;
    private String dataRegistro;
    private List<IntegracaoVendaAvulsaItemDTO> itens;

    public IntegracaoVendaAvulsaDTO(VendaAvulsaVO vendaAvulsaVO) {
        this.codigo = vendaAvulsaVO.getCodigo();
        this.tipoComprador = vendaAvulsaVO.getTipoComprador();
        this.nomeComprador = vendaAvulsaVO.getNomeComprador();
        this.origem = vendaAvulsaVO.getOrigemSistema().getCodigo();
        this.origemDescricao = vendaAvulsaVO.getOrigemSistema().getDescricao();
        this.valor = vendaAvulsaVO.getValorTotal();
        this.dataRegistro = Calendario.getDataAplicandoFormatacao(vendaAvulsaVO.getDataRegistro(), "dd/MM/yyyy HH:mm:ss");

        this.itens = new ArrayList<>();
        for (ItemVendaAvulsaVO itemVO : vendaAvulsaVO.getItemVendaAvulsaVOs()) {
            this.itens.add(new IntegracaoVendaAvulsaItemDTO(itemVO));
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoComprador() {
        return tipoComprador;
    }

    public void setTipoComprador(String tipoComprador) {
        this.tipoComprador = tipoComprador;
    }

    public String getNomeComprador() {
        return nomeComprador;
    }

    public void setNomeComprador(String nomeComprador) {
        this.nomeComprador = nomeComprador;
    }

    public Integer getOrigem() {
        return origem;
    }

    public void setOrigem(Integer origem) {
        this.origem = origem;
    }

    public String getOrigemDescricao() {
        return origemDescricao;
    }

    public void setOrigemDescricao(String origemDescricao) {
        this.origemDescricao = origemDescricao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public List<IntegracaoVendaAvulsaItemDTO> getItens() {
        return itens;
    }

    public void setItens(List<IntegracaoVendaAvulsaItemDTO> itens) {
        this.itens = itens;
    }

}

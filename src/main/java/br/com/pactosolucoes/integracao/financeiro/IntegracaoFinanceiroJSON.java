package br.com.pactosolucoes.integracao.financeiro;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class IntegracaoFinanceiroJSON extends SuperJSON {

    //private static final long serialVersionUID = 1L;

    private Integer codigo;
    private String chave;
    private Integer codigoEmpresa;
    private Integer ativos;
    private Integer vencidos;
    private Integer agregadores;
    private Integer checkinsAgregadores;
    private Integer diaAtualizarAtivosVencidos;
    private Date uaAtivosVencidos;
    private Integer dataExpiracaoZW;
    private Date uaDataExpiracaoZW;
    private Date ucDataExpiracaoZW;

    public IntegracaoFinanceiroJSON() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public Integer getAtivos() {
        return ativos;
    }

    public void setAtivos(Integer ativos) {
        this.ativos = ativos;
    }

    public Integer getVencidos() {
        return vencidos;
    }

    public void setVencidos(Integer vencidos) {
        this.vencidos = vencidos;
    }

    public Integer getAgregadores() {
        return agregadores;
    }

    public void setAgregadores(Integer agregadores) {
        this.agregadores = agregadores;
    }

    public Integer getCheckinsAgregadores() {
        return checkinsAgregadores;
    }

    public void setCheckinsAgregadores(Integer checkinsAgregadores) {
        this.checkinsAgregadores = checkinsAgregadores;
    }

    public Integer getDiaAtualizarAtivosVencidos() {
        return 15; //Sempre é dia 15;
    }

    public void setDiaAtualizarAtivosVencidos(Integer diaAtualizarAtivosVencidos) {
        this.diaAtualizarAtivosVencidos = diaAtualizarAtivosVencidos;
    }

    public Date getUaAtivosVencidos() {
        return uaAtivosVencidos;
    }

    public void setUaAtivosVencidos(Date uaAtivosVencidos) {
        this.uaAtivosVencidos = uaAtivosVencidos;
    }

    public Integer getDataExpiracaoZW() {
        return dataExpiracaoZW;
    }

    public void setDataExpiracaoZW(Integer dataExpiracaoZW) {
        this.dataExpiracaoZW = dataExpiracaoZW;
    }

    public Date getUaDataExpiracaoZW() {
        return uaDataExpiracaoZW;
    }

    public void setUaDataExpiracaoZW(Date uaDataExpiracaoZW) {
        this.uaDataExpiracaoZW = uaDataExpiracaoZW;
    }

    public Date getUcDataExpiracaoZW() {
        return ucDataExpiracaoZW;
    }

    public void setUcDataExpiracaoZW(Date ucDataExpiracaoZW) {
        this.ucDataExpiracaoZW = ucDataExpiracaoZW;
    }
}

package br.com.pactosolucoes.integracao.notasDelphi.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class NFCeDelphiJSON extends SuperJSON {

    @JsonProperty("CNPJEmpresa")
    private String cnpjEmpresa;
    @JsonProperty("CNAE")
    private String cnae;
    @JsonProperty("natOp")
    private String natOp;
    @JsonProperty("complemento")
    private String complemento;
    @JsonProperty("destNome")
    private String destNome;
    @JsonProperty("destCPFCNPJ")
    private String destCPFCNPJ;
    @JsonProperty("nomeAluno")
    private String nomeAluno;
    @JsonProperty("destCEP")
    private String destCEP;
    @JsonProperty("destLogradouro")
    private String destLogradouro;
    @JsonProperty("destNumero")
    private String destNumero;
    @JsonProperty("destBairro")
    private String destBairro;
    @JsonProperty("destComplemento")
    private String destComplemento;
    @JsonProperty("destUF")
    private String destUF;
    @JsonProperty("destCidade")
    private String destCidade;
    @JsonProperty("destFone")
    private String destFone;


    public NFCeDelphiJSON(NotaTO notaTO) {

        this.cnpjEmpresa = Uteis.removerMascara(notaTO.getEmpCNPJ());
        this.cnae = notaTO.getNotaCNAE();
        this.natOp = StringUtilities.doRemoverAcentos(notaTO.getNotaNaturezaOperacao());
        this.complemento = notaTO.getNotaObservacao().trim();
        this.destNome = StringUtilities.doRemoverAcentos(notaTO.getCliRazaoSocial());
        this.destCPFCNPJ = Uteis.removerMascara(notaTO.getCliCPFCNPJ());
        this.nomeAluno = StringUtilities.doRemoverAcentos(notaTO.getCliNomeAluno());

        if (UteisValidacao.emptyString(notaTO.getCliEndCEP())) {
            this.destCEP = "00000000";
        } else {
            this.destCEP = Formatador.removerMascara(notaTO.getCliEndCEP());
        }

        if (UteisValidacao.emptyString(notaTO.getCliEndLogradouro())) {
            this.destLogradouro = "NAO INFORMADO";
        } else {
            this.destLogradouro = Uteis.retirarAcentuacao(notaTO.getCliEndLogradouro());
        }

        if (UteisValidacao.emptyString(notaTO.getCliEndNumero())) {
            this.destNumero = "0";
        } else {
            this.destNumero = notaTO.getCliEndNumero();
        }

        if (UteisValidacao.emptyString(notaTO.getCliEndBairro())) {
            this.destBairro = "NAO INFORMADO";
        } else {
            this.destBairro = Uteis.retirarAcentuacao(notaTO.getCliEndBairro());
        }

        if (UteisValidacao.emptyString(notaTO.getCliEndComplemento())) {
            this.destComplemento = "NAO INFORMADO";
        } else {
            this.destComplemento = Uteis.retirarAcentuacao(notaTO.getCliEndComplemento());
        }

        if (UteisValidacao.emptyString(notaTO.getCliTelefone())) {
            this.destFone = "0";
        } else {
            this.destFone = Uteis.removerMascara(notaTO.getCliTelefone());
        }

        this.destUF = notaTO.getCliEndUFEstado();
        this.destCidade = notaTO.getCliEndCidade();

        if (!UteisValidacao.emptyString(notaTO.getNomePagador())) {
            this.destNome = StringUtilities.doRemoverAcentos(notaTO.getNomePagador());
            this.destCPFCNPJ = null;
            this.nomeAluno = null;
        }

    }
}

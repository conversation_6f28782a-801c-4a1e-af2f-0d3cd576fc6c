package br.com.pactosolucoes.integracao.notasDelphi.nfse;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.comuns.util.Formatador;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaTO;

public class LoteNFSeDelphiJSON extends SuperJSON {

    @JsonProperty("CPFCNPJ")
    private String cpfCNPJ;
    @JsonProperty("referencia")
    private String referencia;
    @JsonProperty("urlConfirmacao")
    private String urlConfirmacao;


    public LoteNFSeDelphiJSON(NotaTO notaTO, String urlConfirmacao) {
        this.cpfCNPJ = Formatador.removerMascara(notaTO.getEmpCNPJ());
        this.referencia = notaTO.getNotaIDReferencia();
        this.urlConfirmacao = urlConfirmacao;
    }
}

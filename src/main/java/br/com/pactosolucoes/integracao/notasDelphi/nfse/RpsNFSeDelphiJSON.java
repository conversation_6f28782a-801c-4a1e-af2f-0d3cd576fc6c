package br.com.pactosolucoes.integracao.notasDelphi.nfse;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.utilitarias.Uteis;

public class RpsNFSeDelphiJSON extends SuperJSON {

    @JsonProperty("DataEmissao")
    private String dataEmissao;
    @JsonProperty("DataCompetencia")
    private String dataCompetencia;
    @JsonProperty("InscricaoMunicipal")
    private String inscricaoMunicipal;
    @JsonProperty("RazaoSocial")
    private String razaoSocial;
    @JsonProperty("CPFCNPJ")
    private String cpfCNPJ;
    @JsonProperty("NomeAluno")
    private String nomeAluno;
    @JsonProperty("NatOp")
    private String natOp;
    @JsonProperty("SerieRPS")
    private String serieRPS;
    @JsonProperty("TipoBairro")
    private String tipoBairro;
    @JsonProperty("TipoLogradouro")
    private String tipoLogradouro;
    @JsonProperty("AliquotaAtividade")
    private Double aliquotaAtividade;
    @JsonProperty("AliquotaPIS")
    private Double aliquotaPIS;
    @JsonProperty("AliquotaCOFINS")
    private Double aliquotaCOFINS;
    @JsonProperty("ISSRetido")
    private Integer iSSRetido;
    @JsonProperty("ValorISSRetido")
    private Double valorISSRetido;
    @JsonProperty("ValorPIS")
    private Double valorPIS;
    @JsonProperty("ValorCOFINS")
    private Double valorCOFINS;
    @JsonProperty("ValorIRRFR")
    private Double valorIRRFR;
    @JsonProperty("Logradouro")
    private String logradouro;
    @JsonProperty("NumeroEndereco")
    private String numeroEndereco;
    @JsonProperty("ComplementoEndereco")
    private String complementoEndereco;
    @JsonProperty("Bairro")
    private String bairro;
    @JsonProperty("UF")
    private String uf;
    @JsonProperty("Cidade")
    private String cidade;
    @JsonProperty("CFOP")
    private String cfop;
    @JsonProperty("UFPrestacao")
    private String ufPrestacao;
    @JsonProperty("CidadePrestacao")
    private String cidadePrestacao;
    @JsonProperty("CEP")
    private String cep;
    @JsonProperty("Email")
    private String email;
    @JsonProperty("EnviarEmail")
    private boolean enviarEmail = true;
    @JsonProperty("TelefoneCons")
    private String telefoneCons;
    @JsonProperty("ValorServicos")
    private Double valorServicos;
    @JsonProperty("ItemListaServico")
    private String itemListaServico;
    @JsonProperty("CodigoTributacaoMunicipio")
    private String codigoTributacaoMunicipio;
    @JsonProperty("CodigoCnae")
    private String codigoCnae;
    @JsonProperty("Descricao")
    private String descricao;
    @JsonProperty("Observacao")
    private String observacao;
    @JsonProperty("ExigibilidadeISS")
    private Integer exigibilidadeISS;
    @JsonProperty("InscricaoEstadual")
    private String inscricaoEstadual;
    @JsonProperty("CFDF")
    private String cfdf;
    @JsonProperty("InformacaoFisco")
    private String informacaoFisco;
    @JsonProperty("IdRPSAnterior")
    private Integer idRPSAnterior;
    @JsonProperty("CodigoAtividade")
    private String codigoAtividade;
    @JsonProperty("ValorINSS")
    private Double valorINSS;
    @JsonProperty("ValorIR")
    private Double valorIR;
    @JsonProperty("ValorCSLL")
    private Double valorCSLL;
    @JsonProperty("AliquotaINSS")
    private Double aliquotaINSS;
    @JsonProperty("AliquotaIR")
    private Double aliquotaIR;
    @JsonProperty("AliquotaCSLL")
    private Double aliquotaCSLL;
    @JsonProperty("ValorDeducoes")
    private Double valorDeducoes;
    @JsonProperty("OutrasRetencoes")
    private Double outrasRetencoes;
    @JsonProperty("DescontoIncondicionado")
    private Double descontoIncondicionado;
    @JsonProperty("DescontoCondicionado")
    private Double descontoCondicionado;


    public RpsNFSeDelphiJSON(NotaTO notaTO) {
        this.dataEmissao = Uteis.getDataAplicandoFormatacao(notaTO.getNotaDtEmissao(), "yyyy-MM-dd HH:mm:ss");
        this.dataCompetencia = Uteis.getDataAplicandoFormatacao(notaTO.getNotaDtCompetencia(), "yyyy-MM-dd HH:mm:ss");
        this.inscricaoMunicipal = "";
        this.razaoSocial = StringUtilities.doRemoverAcentos(notaTO.getCliRazaoSocial());
        this.cpfCNPJ = Formatador.removerMascara(notaTO.getCliCPFCNPJ());
        this.nomeAluno = StringUtilities.doRemoverAcentos(notaTO.getCliNomeAluno());
        this.natOp = StringUtilities.doRemoverAcentos(notaTO.getNotaNaturezaOperacao());
        this.serieRPS = notaTO.getNotaSerie();
        this.tipoBairro = " ";
        this.tipoLogradouro = " ";
        this.aliquotaAtividade = (notaTO.getNotaAliquotaISS() / 100.0);
        this.aliquotaPIS = notaTO.getNotaAliquotaPIS();
        this.aliquotaCOFINS = notaTO.getNotaAliquotaCOFINS();
        this.iSSRetido = notaTO.isNotaIssRetido() ? 1 : 2;
        this.valorISSRetido = notaTO.getNotaValorISS();
        this.valorPIS = notaTO.getNotaValorPIS();
        this.valorCOFINS = notaTO.getNotaValorCOFINS();
        if (notaTO.getNotaValor() >= notaTO.getConfiguracaoNotaFiscalVO().getValorMinimoIRRF()) {
            this.valorIRRFR = notaTO.getNotaValorIRRF();
        } else {
            this.valorIRRFR = 0.0;
        }
        this.logradouro = StringUtilities.doRemoverAcentos(notaTO.getCliEndLogradouro());
        this.numeroEndereco = notaTO.getCliEndNumero();
        this.complementoEndereco = StringUtilities.doRemoverAcentos(notaTO.getCliEndComplemento());
        this.bairro = StringUtilities.doRemoverAcentos(notaTO.getCliEndBairro());
        this.uf = notaTO.getCliEndUFEstado();
        this.cidade = StringUtilities.doRemoverAcentos(notaTO.getCliEndCidade());
        if (!notaTO.getCliEndUFEstado().equals(notaTO.getEmpEndUFEstado())) {
            this.cfop = "6933";
        } else {
            this.cfop = "";
        }
        this.ufPrestacao = notaTO.getEmpEndUFEstado();
        this.cidadePrestacao = StringUtilities.doRemoverAcentos(notaTO.getEmpEndCidade());
        this.cep = notaTO.getCliEndCEP();
        this.email = notaTO.getCliEmail();
        this.enviarEmail = notaTO.isNotaEnviarEmail();
        this.telefoneCons = notaTO.getCliTelefone();
        this.valorServicos = notaTO.getNotaValor();
        this.itemListaServico = notaTO.getNotaItemListaServico();
        this.codigoTributacaoMunicipio = notaTO.getNotaCodigoTributacaoMunicipio();
        this.codigoCnae = notaTO.getNotaCNAE();
        this.descricao = StringUtilities.doRemoverAcentos(notaTO.getNotaDescricao());
        this.observacao = StringUtilities.doRemoverAcentos(notaTO.getNotaObservacao().trim());
        this.exigibilidadeISS = notaTO.getNotaExigibilidadeISS();
        this.inscricaoEstadual = notaTO.getCliInscEstadual();
        this.cfdf = notaTO.getCliCFDF();
        this.informacaoFisco = "";
        this.idRPSAnterior = 0;
        this.codigoAtividade = "";
        this.valorINSS = 0.0;
        this.valorIR = 0.0;
        this.valorCSLL = 0.0;
        this.aliquotaINSS = 0.0;
        this.aliquotaIR = 0.0;
        this.aliquotaCSLL = 0.0;
        this.valorDeducoes = 0.0;
        this.outrasRetencoes = 0.0;
        this.descontoIncondicionado = 0.0;
        this.descontoCondicionado = 0.0;
    }
}

package br.com.pactosolucoes.integracao.notasDelphi.nfse;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaPagamentoTO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class PagamentoNFSeDelphiJSON extends SuperJSON {

    @JsonProperty("formaPagamento")
    private String formaPagamento;
    @JsonProperty("valor")
    private Double valor;
    @JsonProperty("numero")
    private Integer numero;
    @JsonProperty("dtVencimento")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ssZ")
    private Date dtVencimento;

    public PagamentoNFSeDelphiJSON(NotaPagamentoTO notaPagamentoTO) {
        this.formaPagamento = StringUtilities.doRemoverAcentos(notaPagamentoTO.getDescricaoFormaPagamento());
        this.valor = notaPagamentoTO.getValor();
        this.numero = notaPagamentoTO.getNumero();
        this.dtVencimento = notaPagamentoTO.getDataVencimento();
    }
}

package br.com.pactosolucoes.integracao.notasDelphi.nfse;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaPagamentoTO;
import negocio.comuns.notaFiscal.NotaProdutoTO;

public class ProdutoNFSeDelphiJSON extends SuperJSON {

    @JsonProperty("ValorUnitario")
    private Double valorUnitario;
    @JsonProperty("Quantidade")
    private Integer quantidade;
    @JsonProperty("Descricao")
    private String descricao;
    @JsonProperty("Tributavel")
    private boolean tributavel;
    @JsonProperty("NCM")
    private String ncm;
    @JsonProperty("IdProduto")
    private Integer idProduto;

    public ProdutoNFSeDelphiJSON(NotaProdutoTO notaProdutoTO) {
        this.valorUnitario = notaProdutoTO.getValorUnitario();
        this.quantidade = notaProdutoTO.getQuantidade();
        this.descricao = StringUtilities.doRemoverAcentos(notaProdutoTO.getDescricao());
        this.tributavel = true;
        this.ncm = notaProdutoTO.getNcm();
        this.idProduto = notaProdutoTO.getProdutoVO().getCodigo();
    }
}

package br.com.pactosolucoes.integracao.notasDelphi.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaPagamentoTO;
import negocio.comuns.notaFiscal.NotaTO;

public class PagamentoNFCeDelphiJSON extends SuperJSON {

    @JsonProperty("formaPagamento")
    private String formaPagamento;
    @JsonProperty("valor")
    private Double valor;

    public PagamentoNFCeDelphiJSON(NotaPagamentoTO notaPagamentoTO) {
        this.formaPagamento = notaPagamentoTO.getDescricaoFormaPagamento();
        this.valor = notaPagamentoTO.getValor();
    }
}

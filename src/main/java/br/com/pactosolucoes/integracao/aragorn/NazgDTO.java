package br.com.pactosolucoes.integracao.aragorn;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import com.fasterxml.jackson.annotation.JsonIgnore;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.apf.APF;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/01/2020
 */
public class NazgDTO {

    private String token;
    private String name;
    private String card;
    private Integer flag; //OperadorasExternasAprovaFacilEnum
    private int month;
    private int year;
    private String cpf;

    public NazgDTO() {
    }

    public NazgDTO(JSONObject nazg) {
        this.name = nazg.optString("name");
        this.card = nazg.optString("card");
        this.flag = nazg.optInt("flag");
        this.month = nazg.optInt("month");
        this.year = nazg.optInt("year");
        this.cpf = nazg.optString("cpf");
    }

    public NazgDTO(RemessaItemVO itemVO) {
        this.token = itemVO.get(APF.TokenAragorn);
        this.name = "";
        this.card = APF.decifrar(itemVO.get(APF.NumCartao));
        this.month = Integer.parseInt(itemVO.get(APF.MesValidade));
        this.year = Integer.parseInt(itemVO.get(APF.AnoValidade));
        this.cpf = "";
        try {
            this.flag = OperadorasExternasAprovaFacilEnum.valueOf(itemVO.get(APF.Bandeira)).getId();
        } catch (Exception ex) {
            this.flag = Uteis.obterOperadora(this.getCard()).getId();
            ex.printStackTrace();
        }
    }

    public NazgDTO(AutorizacaoCobrancaVO autorizacaoCobrancaVO) {
        this.token = autorizacaoCobrancaVO.getTokenAragorn();
        this.name = autorizacaoCobrancaVO.getNomeTitularCartao();
        this.card = autorizacaoCobrancaVO.getNumeroCartao();
        this.month = autorizacaoCobrancaVO.getMesValidade();
        this.year = autorizacaoCobrancaVO.getAnoValidade();
        this.cpf = autorizacaoCobrancaVO.getCpfTitular();
        try {
            this.flag = autorizacaoCobrancaVO.getOperadoraCartao().getId();
        } catch (Exception ex) {
            this.flag = Uteis.obterOperadora(this.getCard()).getId();
            ex.printStackTrace();
        }
    }

    public NazgDTO(CartaoCreditoTO cartaoCreditoTO) {
        this.token = cartaoCreditoTO.getTokenAragorn();
        this.name = cartaoCreditoTO.getNomeTitular();
        this.month = cartaoCreditoTO.getMesValidade();
        this.year = cartaoCreditoTO.getAnoValidade();
        this.cpf = cartaoCreditoTO.getCpfCnpjSomenteNumeros();

        if (!UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
            this.card = cartaoCreditoTO.getNumero();
        }

        try {
            this.flag = cartaoCreditoTO.getBand().getId();
        } catch (Exception ex) {
            this.flag = Uteis.obterOperadora(this.getCard()).getId();
            ex.printStackTrace();
        }
    }

    public String getToken() {
        if (token == null) {
            token = "";
        }
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getName() {
        if (name == null) {
            name = "";
        }
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCard() {
        if (card == null) {
            card = "";
        }
        return card;
    }

    public void setCard(String card) {
        this.card = card;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public String getCpf() {
        if (cpf == null) {
            cpf = "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getCardMask() {
        return APF.getCartaoMascarado(getCard());
    }

    public String getValidadeMMYYYY(boolean comBarra) {
        return Uteis.getValidadeMMYYYY(getMonth(), getYear(), comBarra);
    }

    public void validarDados() throws Exception {
        if (UteisValidacao.emptyString(getCard())) {
            throw new Exception("Número do cartão não informado.");
        }
        if (!UteisValidacao.somenteNumeros(getCard())) {
            throw new Exception("O número do cartão deve conter somente números.");
        }
    }
}

package br.com.pactosolucoes.integracao.aragorn;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.apf.APF;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 09/03/2020
 */
public class RemoverCartao {

    private Connection con;

    public RemoverCartao(Connection con) {
        this.con = con;
    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "INICIO | RemoverCartao...");

            String chave = null;
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }

            Connection conOAMD = Conexao.obterConexaoBancoEmpresas();
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT chave from empresa \n");
            sql.append("WHERE 1 = 1 \n");
            if (!UteisValidacao.emptyString(chave)) {
                sql.append(" AND chave = '").append(chave).append("'");
            }

            PreparedStatement st = conOAMD.prepareStatement(sql.toString());
            ResultSet rs = st.executeQuery();
            while (rs.next()) {
                chave = rs.getString("chave");
                Connection con = null;
                try {
                    Uteis.logar(null, "Obter conexão para chave: " + chave);
                    con = new DAO().obterConexaoEspecifica(chave);
                    RemoverCartao removerCartao = new RemoverCartao(con);
                    removerCartao.removerCartoes();
                    removerCartao = null;
                } catch (Exception ex) {
                    Uteis.logar(null, "Erro chave: " + chave);
                    ex.printStackTrace();
                } finally {
                    if (con != null) {
                        con.close();
                    }
                }
            }

            Uteis.logar(true, null, "FIM | RemoverCartao...");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void removerCartoes() throws Exception {
        Date inicio = getDataInicial();
        if (inicio == null) {
            Uteis.logar(true, null, "Sem data de início!");
            return;
        }

        List<Date> meses = Uteis.getMesesEntreDatas(inicio, Calendario.hoje());
        for (Date mes : meses) {
            remover(mes);
        }
    }

    private void remover(Date mes) {
        Integer sucesso = 0;
        Integer falha = 0;
        Integer total = 0;
        try {
            Uteis.logar(true, null, "INICIO | Remover cartões... Mês | " + Calendario.getDataAplicandoFormatacao(mes, "MM/yyyy"));

            String sql = getSQL(mes);

            total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            if (UteisValidacao.emptyNumber(total)) {
                Uteis.logar(true, null, "Sem cartões para ajustar!");
                return;
            }

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            int i = 0;
            while (rs.next()) {
                String tipo = "";
                Integer codigo = 0;
                try {
                    con.setAutoCommit(false);

                    Uteis.logar(true, null, "Ajustando... " + ++i + "/" + total);

                    codigo = rs.getInt("codigo");
                    tipo = rs.getString("tipo");

                    String cartao = rs.getString("cartao");
                    String cartaoMask = APF.getCartaoMascarado(cartao);

                    if (!cartaoMask.contains("****")) {
                        throw new Exception("Número mascarado incorreto!");
                    }

                    if (tipo.equalsIgnoreCase("REMESSA")) {
                        SuperFacadeJDBC.executarUpdate("update remessaitem set props = replace(props, '" + cartao + "', '" + cartaoMask + "') where codigo = " + codigo, con);
                    } else if (tipo.equalsIgnoreCase("TRANSACAO")) {
                        SuperFacadeJDBC.executarUpdate("update transacao set paramsenvio = replace(paramsenvio, '" + cartao + "', '" + cartaoMask + "') where codigo = " + codigo, con);
                    } else {
                        throw new Exception("Tipo não encontrado!");
                    }

                    ++sucesso;
                    con.commit();
                } catch (Exception ex) {
                    ++falha;
                    con.rollback();
                    ex.printStackTrace();
                    Uteis.logar(true, null, "Erro... " + tipo + " | " + codigo + " | " + ex.getMessage());
                } finally {
                    con.setAutoCommit(true);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            if (!UteisValidacao.emptyNumber(total)) {
                Uteis.logar(true, null, "FIM | Mês | " + Calendario.getDataAplicandoFormatacao(mes, "MM/yyyy"));
                Uteis.logar(true, null, "FIM | Total " + total);
                Uteis.logar(true, null, "FIM | Sucesso " + sucesso);
                Uteis.logar(true, null, "FIM | Falha " + falha);
            }
            Uteis.logar(true, null, "FIM | Remover cartões... Mês | " + Calendario.getDataAplicandoFormatacao(mes, "MM/yyyy"));
        }
    }

    private Date getDataInicial() throws Exception {
        String sql = "select * from (\n" +
                "select min(dataprocessamento::date) as data, 'T' as t from transacao\n" +
                "union all\n" +
                "select min(dataregistro::date) as data, 'R' as t from remessa\n" +
                ") as sql\n" +
                "order by 1";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        if (rs.next()) {
            return rs.getDate("data");
        }
        return null;
    }

    public String getSQL(Date mes) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from ( \n");
        sql.append("select \n");
        sql.append("t.codigo as codigo, \n");
        sql.append("CASE \n");
        sql.append("WHEN t.tipo = 2 THEN split_part(split_part(t.paramsenvio, 'card_number\":\"', 2), '\"', 1) \n");
        sql.append("WHEN t.tipo = 3 THEN split_part(split_part(t.paramsenvio, 'CardNumber\":\"', 2), '\"', 1) \n");
        sql.append("WHEN t.tipo = 7 THEN split_part(split_part(t.paramsenvio, 'cardNumber\":\"', 2), '\"', 1) \n");
        sql.append("WHEN t.tipo = 10 THEN split_part(split_part(t.outrasinformacoes, 'card\":\"', 2), '\"', 1) \n");
        sql.append("WHEN t.tipo = 11 THEN split_part(split_part(t.paramsenvio, '<PAN>', 2), '</', 1)   \n");
        sql.append("ELSE '' END as cartao, \n");
        sql.append("'TRANSACAO' as tipo \n");
        sql.append("from transacao t \n");
        sql.append("where t.tipo in (2,3,7,10,11) \n");
        sql.append("and t.dataprocessamento::date between 'DATA_INICIO' and 'DATA_FIM' \n");
        sql.append("union \n");
        sql.append("select \n");
        sql.append("ri.codigo, \n");
        sql.append("split_part(split_part(split_part(ri.props, 'NumeroCartao=', 2), ',', 1), '}', 1) as cartao, \n");
        sql.append("'REMESSA' as tipo \n");
        sql.append("from remessaitem ri \n");
        sql.append("inner join remessa r on r.codigo = ri.remessa \n");
        sql.append("where r.tipo in (2,8,12) \n");
        sql.append("and ri.props ilike '%NumeroCartao=%' \n");
        sql.append("and r.dataregistro::date between 'DATA_INICIO' and 'DATA_FIM' \n");
        sql.append("and r.situacaoremessa = 2) as sql \n");
        sql.append("where sql.cartao not ilike '%****%' \n");

        String retorno = sql.toString();
        retorno = retorno.replaceAll("DATA_INICIO", Calendario.getDataAplicandoFormatacao(Uteis.obterPrimeiroDiaMes(mes), "yyyy-MM-dd"));
        retorno = retorno.replaceAll("DATA_FIM",  Calendario.getDataAplicandoFormatacao(Uteis.obterUltimoDiaMes(mes), "yyyy-MM-dd"));
        return retorno;
    }
}

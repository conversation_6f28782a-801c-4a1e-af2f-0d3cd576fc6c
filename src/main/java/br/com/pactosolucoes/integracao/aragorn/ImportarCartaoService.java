package br.com.pactosolucoes.integracao.aragorn;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.enumeradores.TipoImportacaoCartaoEnum;
import br.com.pactosolucoes.integracao.aragorn.uteis.ImportarTabelaAuxiliarImportacaoCartao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ItemAuxiliarCartaoTO;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.propriedades.PropsService;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.StringReader;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 19/06/2020
 */
public class ImportarCartaoService {

    public Integer empresa;
    public Integer convenioCobranca;
    public String authorization;
    public String key;
    public TipoImportacaoCartaoEnum tipoImportacao;
    public ConvenioCobrancaVO convenioCobrancaVO;
    private boolean usarMatriculaExterna = false;
    private Integer modeloTabelaAuxiliar = null;
    private boolean arquivoVindi = false;
    private boolean validarCartaoExistente = false;
    private boolean importarVencido = false;


    public static void main(String[] args) {
        try {
            Uteis.debug = true;
            Uteis.logarDebug("ImportarCartaoService | INICIO...");

//            args = new String[]{"fly", "1", "3", "C:\\Pacto", "arquivotteste_token", "9", "false"};

            if (args != null && args.length > 0) {
                Uteis.logarDebug("ImportarCartaoService | ARGS...");
                int argI = 0;
                for (String arg : args) {
                    Uteis.logarDebug("Args[" + ++argI + "] | " + arg);
                }
            }

            String chave = args[0];
            Integer empresa = Integer.parseInt(args[1]);
            Integer convenioCobranca = Integer.parseInt(args[2]);
            String pathOrigem = args[3];
            String inicioArq = args[4];
            Integer tipoImportacaoCod = args.length > 5 ? Integer.parseInt(args[5]) : 0;
            boolean desativarImportadas = args.length > 6 && Boolean.parseBoolean(args[6]);
            String authorization = args.length > 7 ? args[7] : "";

            TipoImportacaoCartaoEnum tipoImportacaoEnum = TipoImportacaoCartaoEnum.getConsultarPorCodigo(tipoImportacaoCod);
            if (tipoImportacaoEnum == null) {
                throw new Exception("TipoImportacao não identificado!");
            }

            Uteis.logarDebug("Obter conexao para chave: " + chave);
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                ImportarCartaoService service;
                try {
                    service = new ImportarCartaoService(chave, empresa, convenioCobranca, tipoImportacaoEnum, desativarImportadas, authorization, con);
                    service.processarArquivos(inicioArq, pathOrigem, con);
                } finally {
                    service = null;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("main | Erro: " + ex.getMessage());
        } finally {
            Uteis.logarDebug("ImportarCartaoService | FIM...");
        }
    }

    public ImportarCartaoService(String key, Integer empresa, Integer convenioCobranca,
                                 TipoImportacaoCartaoEnum tipoImportacaoCartaoEnum,
                                 boolean desativarAutorizacoesImportadas,
                                 String authorization, Connection con) {
        this.empresa = empresa;
        this.convenioCobranca = convenioCobranca;
        this.tipoImportacao = tipoImportacaoCartaoEnum;
        this.authorization = authorization;
        this.key = key;
        this.prepararImportacao(desativarAutorizacoesImportadas, con);
    }

    private void prepararImportacao(boolean desativarAutorizacoesImportadas, Connection con) {
        processarTipoImportacao();
        carregarConvenioCobranca(con);

        Uteis.debug = false;
        //atualizar banco com tabelas e colunas necessárias
        SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacliente ADD COLUMN obsimportmundipaag text;", con);
        SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacliente ADD COLUMN tokenPagoLivre VARCHAR(120);", con);
        ImportarTabelaAuxiliarImportacaoCartao.criarTabela(con); //criar a tabela de auxiliar para evitar erro no sql caso utilize
        Uteis.debug = true;

        if (desativarAutorizacoesImportadas) {
            desativarAutorizacoesImportadas(this.empresa, this.convenioCobranca, con);
        }
    }

    private void carregarConvenioCobranca(Connection con) {
        ConvenioCobranca convenioCobrancaDAO;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            this.convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(this.convenioCobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            convenioCobrancaDAO = null;
        }
    }

    private void processarTipoImportacao() {
        //TipoImportacao
        // 0 - Padrão
        // 1 - Usando Matricula Externa (Considera que o "idClienteMundi" é a matriculaexterna no zw)
        // 2 - Usando Tabela Externa (Usa a "importacaocartaoauxiliar" que contem informação de nome e ultimos 4 digitos do cartão)
        // 3 - Arquivo da Vindi
        // 4 - Usando Tabela Externa Modelo Mouve
        // 5 - Busca o cliente pelo nome CustomerName
        // 6 - Busca padrão se não encontrar busca pelo CPF do Terceiro
        // 7 - Importacao EVO - Busca pelo nome CustomerName e caso não encontre então busca pela tabela auxiliar "ProcessoPreencherTabelaAuxiliarImportacaoCartaoEVO"
        // 8 - Importacao CloudGym

        if (tipoImportacao.equals(TipoImportacaoCartaoEnum.MATRICULA_EXTERNA_1)) {
            usarMatriculaExterna = true;
        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.TABELA_EXTERNA_2)) {
            modeloTabelaAuxiliar = 0;
        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.VINDI_3)) {
            arquivoVindi = true;
        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.TABELA_EXTERNA_MODELO_MOUVE_4)) {
            modeloTabelaAuxiliar = 1;
        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CUSTOMERNAME_5)) {
            arquivoVindi = false;
            modeloTabelaAuxiliar = null;
            validarCartaoExistente = true;
        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.PADRAO_CPF_TERCEIRO_6)) {
            arquivoVindi = false;
            modeloTabelaAuxiliar = null;
        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.EVO_7)) {
            arquivoVindi = false;
            modeloTabelaAuxiliar = 2;
            validarCartaoExistente = true;
        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_8) ||
                tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_TOKEN_CARTAO_9)) {
            arquivoVindi = false;
            modeloTabelaAuxiliar = null;
            validarCartaoExistente = true;
        }
    }

    private void desativarAutorizacoesImportadas(Integer empresa, Integer convenioCobranca, Connection con) {
        SuperFacadeJDBC.executarUpdateExecutarProcessos("update autorizacaocobrancacliente set ativa = false where length(coalesce(obsimportmundipaag,'')) > 0 " +
                "and conveniocobranca = " + convenioCobranca + " " +
                "and cliente in (select codigo from cliente where empresa = " + empresa + ");", con);
    }

    private void processarArquivo(File f, String conteudoArquivo, Connection con) {
        List<String> listaErros = new ArrayList<>();
        int sucesso = 0;
        int falha = 0;
        int total = 0;
        String nomeArquivo = f.getName();
        try {
            Uteis.logarDebug("Processar Arquivo => " + f.length() + " => " + f.getAbsolutePath());

            if (conteudoArquivo.length() > 2) {
                BufferedReader br = new BufferedReader(new StringReader(conteudoArquivo));
                String linha;

                int qtdLinhas = 0;
                while (br.readLine() != null) qtdLinhas++;

                br = new BufferedReader(new StringReader(conteudoArquivo));
                int n = 0;
                while ((linha = br.readLine()) != null) {
                    String idCardMundiPagg = "";
                    String card = "";
                    String titular = "";
                    String titularDoc = null;
                    String brand;
                    String anoVenc;
                    String mesVenc;
                    String idClienteMundi = null;
                    String nomeCliente = "";
                    String cpf = null;
                    String token = "";
                    String refId = "";
                    try {
                        if (linha.startsWith("CardId") ||
                                (arquivoVindi && linha.startsWith("id,")) ||
                                ((tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_8) ||
                                        tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_TOKEN_CARTAO_9)) && linha.contains("member_id"))) {
                            System.out.println(linha);
                            qtdLinhas--;
                            continue;
                        }
                        n++;
                        ++total;

                        if (n == 1 && !tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_8)) {
                            System.out.println(linha);
                        }


                        String[] dados = linha.split(",");

                        if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_TOKEN_CARTAO_9)) {
                            // member_id,cardholder_name,expire_date,card_brad,tkn,refid

                            idClienteMundi = dados[0];
                            titular = dados[1];
                            String vencimento = dados[2];
                            anoVenc = vencimento.substring(0,4);
                            mesVenc = vencimento.substring(4);
                            brand = dados[3];
                            token = dados[4];
                            refId = dados[5];

                        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_8)) {
                            //"member_id","cardholder_name","card_number","expire_date","card_brad"

                            idClienteMundi = dados[0];
                            titular = dados[1];
                            card = dados[2];
                            String vencimento = dados[3];
                            anoVenc = vencimento.substring(0, 4);
                            mesVenc = vencimento.substring(4);
                            brand = dados[4];

                        } else if (arquivoVindi && !tipoImportacao.equals(TipoImportacaoCartaoEnum.CUSTOMERNAME_5)) {
                            // id,created_at,name,full_number,expiration_month,expiration_year,payment_company_code
                            // 0015b13e-c13b-48ee-badd-a3c8b48e5e0b,2021-11-24 17:15:17 -0300,VICTOR OLIVEIRA DA SILVA,4824********1107,12,2023,visa

                            idCardMundiPagg = dados[0];
                            titular = dados[2];
                            card = dados[3];
                            mesVenc = dados[4];
                            anoVenc = dados[5];
                            brand = dados[6];
                            nomeCliente = dados[2];

                        } else {

                            // CardId-0,CardNumber-1,CardHolderName-2,CardHolderDocument-3,CardBrand-4,ExpirationDate-5,ExpirationYear-6,ExpirationMonth-7,CustomerId-8,CustomerCode-9,CustomerName-10,CustomerEmail-11,CustomerHomePhone-12,CustomerMobilePhone-13,CustomerType-14,CustomerBirthDate-15,CustomerDocument-16,CustomerAddressStreet-17,CustomerAddressComplement-18,CustomerAddressNeighborhood-19,CustomerAddressCity-20,CustomerAddressState-21,CustomerAddressCountry-22,CustomerAddressZipCode-23,BillingAddressStreet-24,BillingAddressComplement-25,BillingAddressNeighborhood-26,BillingAddressCity-27,BillingAddressState-28,BillingAddressCountry-29,BillingAddressZipCode-30
                            // 53C3FDEF-FA85-4F72-AFE1-CF72973C6FA8,****************,HENRIQUE SOARES DOS SANTOS,,Elo,202401,2024,01,53C3FDEF-FA85-4F72-AFE1-CF72973C6FA8,53C3FDEF-FA85-4F72-AFE1-CF72973C6FA8,HENRIQUE SOARES DOS SANTOS,,,,Individual,,,,,,,,,,,,,,,,
                            // idCardMundiPagg,card,titular,

                            idCardMundiPagg = dados[0];
                            card = dados[1];
                            titular = dados[2];
                            titularDoc = dados[3];
                            brand = dados[4];

                            String venc = dados[5];
                            anoVenc = venc.substring(0, 4);
                            mesVenc = venc.substring(4, 6);
                            idClienteMundi = dados[8];
                            nomeCliente = dados[10];
                            try {
                                cpf = dados[16];
                            } catch (Exception e) {
                                Uteis.logarDebug("Informação: CPF nao informado para " + nomeCliente);
                            }
                        }


                        if (!tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_8) &&
                                !tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_TOKEN_CARTAO_9)) {
                            Uteis.logarDebug(String.format("Linha %s => Id_card: %s Card Suffix: %s Titular: %s Cliente Old Gateway: %s CPF: %s",
                                    n, idCardMundiPagg, card.substring(12), titular, nomeCliente, cpf));
                        }

                        ImportacaoCartaoDTO cartaoDTO = new ImportacaoCartaoDTO();
                        cartaoDTO.setCard(card.replaceAll(" ", ""));
                        cartaoDTO.setTitular(titular);
                        cartaoDTO.setCpf(cpf);
                        cartaoDTO.setNomeCliente(nomeCliente);
                        cartaoDTO.setTitularDoc(titularDoc);
                        cartaoDTO.setBrand(brand);
                        cartaoDTO.setMesVenc(mesVenc);
                        cartaoDTO.setAnoVenc(anoVenc);
                        cartaoDTO.setIdCliente(idClienteMundi);
                        cartaoDTO.setIdCard(idCardMundiPagg);
                        cartaoDTO.setNomeArquivo(nomeArquivo);
                        cartaoDTO.setTokenAdquirente(token);
                        cartaoDTO.setRefId(refId);

                        if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_8)) {
                            try {
                                String resp = executaRequestAPI(cartaoDTO);
                                Uteis.logarDebug(n + "/" + qtdLinhas + " | Cliente | " + (UteisValidacao.emptyString(nomeCliente) ? idClienteMundi : nomeCliente) + " | IMPORTADO | " + resp);
                            } catch (Exception ex) {
                                Uteis.logarDebug(n + "/" + qtdLinhas + " | Cliente | " + (UteisValidacao.emptyString(nomeCliente) ? idClienteMundi : nomeCliente) + " | FALHA | " + ex.getMessage());
                                throw ex;
                            }
                        } else {
                            AutorizacaoCobrancaClienteVO obj = importarIndividual(cartaoDTO, con);
                        }
                        ++sucesso;
                    } catch (Exception ex) {
                        if (!tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_8)) {
//                            ex.printStackTrace();
                            Uteis.logarDebug("ERRO | ProcessarArquivo | " + nomeArquivo + " | Linha " + n + " | Erro: " + ex.getMessage());
                            listaErros.add("Cliente | " + (UteisValidacao.emptyString(nomeCliente) ? idClienteMundi : nomeCliente) +
                                    (!UteisValidacao.emptyString(cpf) ? (" | CPF | " + cpf) : "") +
                                    " | Titular | " + titular + " | ERRO | " + ex.getMessage());
                        }
                        ++falha;
                    }
                }

                if (!listaErros.isEmpty()) {
                    Uteis.logarDebug("############################");
                    Uteis.logarDebug("########## FALHAS ##########");
                    Uteis.logarDebug("############################");
                    int er = 0;
                    for (String erro : listaErros) {
                        System.out.println(++er + "/" + listaErros.size() + " | " + erro);
                    }
                    Uteis.logarDebug("############################");
                }

                Uteis.logarDebug("############################");
                Uteis.logarDebug("######## RESULTADO #########");
                Uteis.logarDebug("############################");
                Uteis.logarDebug("Arquivo: " + n);
                Uteis.logarDebug("Total: " + total);
                Uteis.logarDebug("Sucesso: " + sucesso);
                Uteis.logarDebug("Falha: " + falha);
                Uteis.logarDebug("############################");
                Uteis.logarDebug("############################");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("ERRO | ProcessarArquivo: " + nomeArquivo + " | " + ex.getMessage());
        }
    }

    private AutorizacaoCobrancaClienteVO montarAutorizacaoCobrancaCliente(ImportacaoCartaoDTO cartaoDTO, Connection con) throws Exception {

        String card = cartaoDTO.getCard();
        String titular = cartaoDTO.getTitular();
        String cpf = cartaoDTO.getCpf();
        String nomeCliente = cartaoDTO.getNomeCliente();
        String titularDoc = cartaoDTO.getTitularDoc();
        String brand = cartaoDTO.getBrand();
        String mesVenc = cartaoDTO.getMesVenc();
        String anoVenc = cartaoDTO.getAnoVenc();
        String idClienteMundi = cartaoDTO.getIdCliente();
        String idCardMundiPagg = cartaoDTO.getIdCard();
        String nomeArquivo = cartaoDTO.getNomeArquivo();
        String tokenAdquirente = cartaoDTO.getTokenAdquirente();
        String refId = cartaoDTO.getRefId();

        AutorizacaoCobrancaClienteVO novo = new AutorizacaoCobrancaClienteVO();
        novo.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
        novo.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
        novo.setNumeroCartao(card);
        novo.setNomeTitularCartao(titular);
        novo.setImportacaoCPF(cpf);
        novo.setImportacaoCliente(nomeCliente);
        novo.setImportacaoTitular(titular);

        if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_TOKEN_CARTAO_9)) {
            novo.setImportacaoTokenCartao(true);
            TipoConvenioCobrancaEnum tipoConvenio = this.convenioCobrancaVO.getTipo();
            if (tipoConvenio.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                novo.setTokenCielo(tokenAdquirente);
            } else if (tipoConvenio.equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY) ||
                    tipoConvenio.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
                novo.setTokenPagoLivre(tokenAdquirente);
            }
        }

        if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CUSTOMERNAME_5) ||
                tipoImportacao.equals(TipoImportacaoCartaoEnum.EVO_7)) {

            if (UteisValidacao.emptyString(novo.getNomeTitularCartao().replaceAll(" ", "").trim())) {
                //usar o nome do cliente caso o titular esteja vazio
                novo.setNomeTitularCartao(nomeCliente);
                Uteis.logarDebug("Vou usar o nome do cliente como nome do titular | " + novo.getNomeTitularCartao());
            } else {
                Uteis.logarDebug("NomeTitularCartao | Titular " + novo.getNomeTitularCartao());
            }
        }

        if (!UteisValidacao.emptyString(titularDoc)) {
            novo.setCpfTitular(titularDoc);
        }

        novo.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.obterPorDescricao(brand));
        novo.setValidadeCartao(mesVenc + "/" + anoVenc);
        novo.setConvenio(new ConvenioCobrancaVO());
        novo.getConvenio().setCodigo(convenioCobranca);
        novo.setCliente(new ClienteVO());
        novo.setValidarBinCartao(false);
        novo.setDesabilitarValidacaoValidade(this.importarVencido);

        if (!novo.isDesabilitarValidacaoValidade()) {
            //validar aqui para nem precisar buscar o cliente no banco
            UteisValidacao.validarVencimentoCartao(AutorizacaoCobrancaVO.getValidadeMMYYYY(true, novo.getMesValidade(), novo.getAnoValidade()));
        }

        if (tipoImportacao.equals(TipoImportacaoCartaoEnum.PADRAO_CPF_TERCEIRO_6)) {
            //buscar pelo cpf caso não encontre buscar pelo cpf do responsável

            Integer cliente;
            try {
                cliente = obterCliente(idClienteMundi, cpf, nomeCliente, false, con);
            } catch (Exception ex) {
                if (UteisValidacao.emptyString(Uteis.formatarCpfCnpj(cpf, true))) {
                    throw new Exception("Não é possível buscar pelo CPF Terceiro | CPF não informado");
                }
                Uteis.logarDebug("INFORMAÇÃO: Buscar pelo CPF Terceiro | CPF " + cpf);
                cliente = obterCliente(idClienteMundi, cpf, nomeCliente, true, con);
            }
            novo.getCliente().setCodigo(cliente);
        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CUSTOMERNAME_5)) {
            Integer cliente = obterCliente(idClienteMundi, cpf, nomeCliente, false, con);
            novo.getCliente().setCodigo(cliente);
        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.EVO_7)) {
            String msgErro = "";
            try {
                Integer cliente = obterCliente(idClienteMundi, cpf, nomeCliente, false, con);
                novo.getCliente().setCodigo(cliente);
            } catch (Exception ex) {
                msgErro = ex.getMessage();
            }

            if (UteisValidacao.emptyNumber(novo.getCliente().getCodigo()) &&
                    modeloTabelaAuxiliar != null && modeloTabelaAuxiliar.equals(2)) {
                obterClienteTabelaAuxiliarEvo(novo, con);
            } else if (UteisValidacao.emptyNumber(novo.getCliente().getCodigo())) {
                throw new Exception(msgErro);
            }
        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_8) ||
                tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_TOKEN_CARTAO_9)) {
            Integer cliente = obterCliente(idClienteMundi, cpf, nomeCliente, false, con);
            novo.getCliente().setCodigo(cliente);
        } else if (modeloTabelaAuxiliar != null) {
            String ultimos4Numeros = novo.getNumeroCartao().substring(novo.getNumeroCartao().length() - 4);
            if (UteisValidacao.emptyString(ultimos4Numeros)) {
                throw new Exception("ultimos4Numeros nao informado | Titular " + titular);
            }

            if (modeloTabelaAuxiliar.equals(0)) {
                obterClienteTabelaAuxiliar(novo, ultimos4Numeros, con);
            } else if (modeloTabelaAuxiliar.equals(1)) {
                obterClienteTabelaAuxiliarMouve(novo, ultimos4Numeros, con);
            }
        } else {
            Integer cliente = obterCliente(idClienteMundi, cpf, nomeCliente, false, con);
            novo.getCliente().setCodigo(cliente);
        }

        if (novo.getCliente() == null ||
                UteisValidacao.emptyNumber(novo.getCliente().getCodigo())) {
            throw new Exception("Cliente nao encontrado | Final card " + card.substring(12) + " | Titular " + titular);
        }

        novo.getCliente().getEmpresa().setCodigo(empresa);

        JSONObject jsonObs = new JSONObject();
        jsonObs.put("tipo", "CARTÃO IMPORTADO");
        jsonObs.put("data", Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
        if (!UteisValidacao.emptyString(refId)) {
            jsonObs.put("refId", refId);
        }
        if (!UteisValidacao.emptyString(nomeArquivo)) {
            jsonObs.put("arq", nomeArquivo);
        }
        if (!UteisValidacao.emptyString(novo.getObservacao())) {
            jsonObs.put("obs", novo.getObservacao());
        }
        if (!UteisValidacao.emptyString(idClienteMundi)) {
            jsonObs.put("idCliente", idClienteMundi);
        }
        if (!UteisValidacao.emptyString(idCardMundiPagg)) {
            jsonObs.put("idCard", idCardMundiPagg);
        }
        if (!UteisValidacao.emptyString(cartaoDTO.getTokenAdquirente())) {
            jsonObs.put("tokenAdquirente", cartaoDTO.getTokenAdquirente());
        }
        if (!UteisValidacao.emptyString(this.authorization)) {
            jsonObs.put("authorization", this.authorization);
        }
        novo.getCliente().setObservacao(jsonObs.toString());
        return novo;
    }

    private Integer obterCliente(String idClienteMundi, String cpf, String nomeCliente,
                                 boolean buscarCpfTerceiro, Connection con) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cl.codigo \n");
        sql.append("from cliente cl \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
        sql.append("where cl.empresa = ").append(empresa).append(" \n");

        String textoLog = "";
        if (tipoImportacao.equals(TipoImportacaoCartaoEnum.PADRAO_CPF_TERCEIRO_6) && buscarCpfTerceiro) {

            textoLog = "cpfcnpjterceiro \"" + cpf + "\" ";
            sql.append("and (regexp_replace(p.cpfcnpjterceiro, '\\D', '', 'g') = '").append(cpf).append("' or \n");
            sql.append(" p.cpfcnpjterceiro = '").append(Uteis.formatarCpfCnpj(cpf, false)).append("' or \n");
            sql.append(" p.cpfcnpjterceiro = '").append(Uteis.formatarCpfCnpj(cpf, true)).append("') \n");

        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CUSTOMERNAME_5) ||
                tipoImportacao.equals(TipoImportacaoCartaoEnum.EVO_7)) {

            textoLog = "nome \"" + nomeCliente + "\" ";

            sql.append("and ( \n");

            sql.append("p.nome ilike '").append(nomeCliente.toUpperCase()).append("' or \n");

            sql.append("(UPPER(translate(regexp_replace(p.nome, '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')) ilike ");
            sql.append("UPPER(translate(regexp_replace('").append(nomeCliente.toUpperCase()).append("', '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')) \n");
            sql.append(") or \n");

            sql.append("(replace(UPPER(translate(regexp_replace(p.nome, '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','') ilike ");
            sql.append("replace(UPPER(translate(regexp_replace('").append(nomeCliente.toUpperCase()).append("', '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','') \n");
            sql.append(") \n");

            sql.append(") \n");

        } else if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_8) ||
                tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_TOKEN_CARTAO_9)) {

            textoLog = "matriculaexterna \"" + idClienteMundi + "\" ";
            sql.append("and cl.matriculaexterna = '").append(Integer.valueOf(idClienteMundi)).append("' \n");


        } else if (usarMatriculaExterna && !UteisValidacao.emptyString(idClienteMundi)) {

            textoLog = "matriculaexterna \"" + idClienteMundi + "\" ";
            sql.append("and cl.matriculaexterna = '").append(idClienteMundi).append("' \n");

        } else if (!UteisValidacao.emptyString(cpf)) {

            textoLog = "CPF \"" + cpf + "\" ";
            sql.append("and (regexp_replace(p.cfp, '\\D', '', 'g') = '").append(cpf).append("' or \n");
            sql.append(" p.cfp = '").append(Uteis.formatarCpfCnpj(cpf, false)).append("' or \n");
            sql.append(" p.cfp = '").append(Uteis.formatarCpfCnpj(cpf, true)).append("') \n");

        } else {

            textoLog = "nome \"" + nomeCliente + "\" ";
            if (arquivoVindi) {
                sql.append("and ( \n");
                sql.append("p.nome ilike '").append(nomeCliente).append("' \n");
                sql.append("or \n");
                sql.append("p.nome ilike (select titular from importacaocartaoauxiliar where nome = '").append(nomeCliente).append("') \n");
                sql.append("or \n");
                sql.append("p.nome ilike (select nome from importacaocartaoauxiliar where titular = '").append(nomeCliente).append("') \n");
                sql.append("or \n");
                sql.append("(length(coalesce(regexp_replace(p.cfp, '[^0-9]+', '', 'g'),'')) > 0 \n");
                sql.append("and ( \n");
                sql.append("(regexp_replace(p.cfp, '[^0-9]+', '', 'g') = (select regexp_replace(t.cpf, '[^0-9]+', '', 'g') from importacaocartaoauxiliar t where t.nome = '").append(nomeCliente).append("')) \n");
                sql.append("or \n");
                sql.append("(regexp_replace(p.cfp, '[^0-9]+', '', 'g') = (select regexp_replace(t.cpf, '[^0-9]+', '', 'g') from importacaocartaoauxiliar t where t.titular = '").append(nomeCliente).append("')) \n");
                sql.append("))) \n");
            } else {
                sql.append("and p.nome ilike '").append(nomeCliente).append("' \n");
            }
        }

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        if (UteisValidacao.emptyNumber(total)) {
            throw new Exception("Nao foi encontrado cliente com " + textoLog);
        }
        if (total > 1) {
            throw new Exception("Foi encontrado " + total + " clientes com " + textoLog);
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getInt("codigo");
        }
        throw new Exception("Nao foi encontrado cliente com " + textoLog);
    }

    private void incluirAuto(AutorizacaoCobrancaClienteVO obj, Connection con) throws Exception {
        AutorizacaoCobrancaCliente autoDAO;
        try {
            con.setAutoCommit(false);
            autoDAO = new AutorizacaoCobrancaCliente(con);

            if (validarCartaoExistente) {

                obj.setValidarQtdCartoes(false);

                if (tipoImportacao.equals(TipoImportacaoCartaoEnum.CLOUD_GYM_TOKEN_CARTAO_9)) {
                    TipoConvenioCobrancaEnum tipoConvenio = this.convenioCobrancaVO.getTipo();

                    if (tipoConvenio.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) && UteisValidacao.emptyString(obj.getTokenCielo())) {
                        throw new Exception("Token CIELO não informado.");
                    } else if (UteisValidacao.emptyString(obj.getTokenPagoLivre()) &&
                            (tipoConvenio.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) || tipoConvenio.equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY))) {
                        throw new Exception("Token " + tipoConvenio.getTipoTransacao().getDescricao().toUpperCase() + " não informado.");
                    }

                    String sqlExiste = "";
                    if (tipoConvenio.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                        sqlExiste = "select codigo from autorizacaocobrancacliente where ativa and tokencielo ilike '" + obj.getTokenCielo() + "' and cliente = " + obj.getCliente().getCodigo();
                    } else if (tipoConvenio.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) || tipoConvenio.equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
                        sqlExiste = "select codigo from autorizacaocobrancacliente where ativa and tokenpagolivre ilike '" + obj.getTokenPagoLivre() + "' and cliente = " + obj.getCliente().getCodigo();
                    }

                    boolean existeCartao = SuperFacadeJDBC.existe(sqlExiste, con);
                    if (existeCartao) {
                        throw new Exception("Já existe uma autorização de cobrança para esse cliente com o mesmo token.");
                    }

                } else {

                    String numeroCardMas = obj.getCartaoMascarado();
                    if (UteisValidacao.emptyString(numeroCardMas)) {
                        numeroCardMas = obj.getCartaoMascarado_Apresentar();
                    }
                    if (UteisValidacao.emptyString(numeroCardMas)) {
                        throw new Exception("Cartão mascarado não identificado.");
                    }

                    boolean existeCartao = SuperFacadeJDBC.existe("select codigo from autorizacaocobrancacliente where ativa and cartaomascaradointerno ilike '" + numeroCardMas + "' and cliente = " + obj.getCliente().getCodigo(), con);
                    if (existeCartao) {
                        throw new Exception("Já existe uma autorização de cobrança para esse cliente com o mesmo cartão " + numeroCardMas);
                    }

                }
            }

            obj.setValidarAutorizacaoCobrancaSemelhante(false);
            obj.setRealizandoImportacao(true); // para não validar o cvv na hora de incluir
            autoDAO.incluir(obj);
            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                SuperFacadeJDBC.executarUpdate("update autorizacaocobrancacliente set obsimportmundipaag = '" + obj.getCliente().getObservacao() + "' where codigo = " + obj.getCodigo(), con);
            } else {
                throw new Exception("Token nao gerado!");
            }
            con.commit();
        } catch (Exception ex) {
//            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            autoDAO = null;
            con.setAutoCommit(true);
        }
    }

    public void processarArquivos(String prefixoArq, String pathOrigem, Connection con) {
        try {
            List<Map<String, File>> arquivos = FileUtilities.readListFilesDirectory(pathOrigem);
            for (Map<String, File> entrada : arquivos) {
                Set<String> s = entrada.keySet();
                for (String fileName : s) {
                    File f = entrada.get(fileName);
//                    Uteis.logarDebug(String.format("Verificando prefixo %s do arquivo %s encontrado", prefixoArq, f.getName()));
                    if (!f.getName().startsWith(prefixoArq))
                        continue;
                    if (f.length() > 0) {
                        processarArquivo(f, readContentFile(f.getAbsolutePath()).toString(), con);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("ERRO | Processar Arquivos | Erro: " + ex.getMessage());
        }
    }

    private void obterClienteTabelaAuxiliar(AutorizacaoCobrancaClienteVO autoVO, String ultimos, Connection con) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cl.codigo as cliente_codigo,  \n");
        sql.append("p.codigo as pessoa_codigo, \n");
        sql.append("p.nome as pessoa_nome, \n");
        sql.append("imp.* \n");
        sql.append("from importacaocartaoauxiliar imp \n");
        sql.append("inner join cliente cl on cl.matriculaexterna = imp.matricula::bigint \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
        sql.append("where coalesce(cartao_ultimos,'') <> '' \n");
        sql.append("and cartao_ultimos = '").append(ultimos).append("' \n");

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        if (UteisValidacao.emptyNumber(total)) {
            Uteis.logarDebug("Nao foi encontrado nenhum registro na tabela importacaocartaoauxiliar com os últimos dígitos: " + ultimos);
            return;
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

        List<ItemAuxiliarCartaoTO> lista = new ArrayList<>();
        while (rs.next()) {
            ItemAuxiliarCartaoTO dto = new ItemAuxiliarCartaoTO();

            dto.setMatricula(rs.getString("matricula"));
            dto.setTitular(rs.getString("titular"));
            dto.setNome(rs.getString("nome"));

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setCodigo(rs.getInt("pessoa_codigo"));
            pessoaVO.setNome(rs.getString("pessoa_nome"));

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setCodigo(rs.getInt("cliente_codigo"));
            dto.setClienteVO(clienteVO);
            lista.add(dto);
        }

        //filtrar o cliente
        for (ItemAuxiliarCartaoTO dto : lista) {
            if (UteisValidacao.emptyString(dto.getTitular()) ||
                    UteisValidacao.emptyString(autoVO.getNomeTitularCartao()) ||
                    UteisValidacao.emptyString(dto.getClienteVO().getPessoa().getNome()) ||
                    UteisValidacao.emptyString(dto.getNome())) {
                continue;
            }

//            String titularCard = autoVO.getNomeTitularCartao();
//            String titularTabela = dto.getTitular();
//            String omeTabela = dto.getNome();
//            String clienteNomeTabela = dto.getClienteVO().getPessoa().getNome();

            //verificar se o nome do titular e o nome do cliente é o mesmo.
            //devido a importação que o cliente tinha 2 matriculas externa iguais
            if (dto.getTitular().trim().equalsIgnoreCase(autoVO.getNomeTitularCartao().trim()) &&
                    dto.getNome().trim().equalsIgnoreCase(dto.getClienteVO().getPessoa().getNome().trim())) {
                autoVO.setCliente(dto.getClienteVO());
                autoVO.setObservacao("Nome titular e do aluno correspondem");
                return;
            }
        }
    }

    private void obterClienteTabelaAuxiliarMouve(AutorizacaoCobrancaClienteVO autoVO, String ultimos, Connection con) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cl.codigo as cliente_codigo,  \n");
        sql.append("p.codigo as pessoa_codigo, \n");
        sql.append("p.nome as pessoa_nome, \n");
        sql.append("imp.* \n");
        sql.append("from importacaocartaoauxiliar imp \n");
        sql.append("inner join pessoa p on (p.nome ilike imp.nome or (length(coalesce(regexp_replace(p.cfp, '[^0-9]+', '', 'g'),'')) > 0 and regexp_replace(p.cfp, '[^0-9]+', '', 'g') = regexp_replace(imp.cpf, '[^0-9]+', '', 'g'))) \n");
        sql.append("inner join cliente cl on cl.pessoa = p.codigo \n");
        sql.append("where coalesce(cartao_ultimos,'') <> '' \n");
        sql.append("and imp.cartao_ultimos = '").append(ultimos).append("' \n");
        sql.append("and imp.titular ilike '").append(autoVO.getNomeTitularCartao()).append("' \n");

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        if (UteisValidacao.emptyNumber(total)) {
            Uteis.logarDebug("Nao foi encontrado nenhum registro na tabela importacaocartaoauxiliar com os últimos dígitos: " + ultimos);
            return;
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

        List<ItemAuxiliarCartaoTO> lista = new ArrayList<>();
        while (rs.next()) {
            ItemAuxiliarCartaoTO dto = new ItemAuxiliarCartaoTO();

            dto.setMatricula(rs.getString("matricula"));
            dto.setTitular(rs.getString("titular"));
            dto.setNome(rs.getString("nome"));

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setCodigo(rs.getInt("pessoa_codigo"));
            pessoaVO.setNome(rs.getString("pessoa_nome"));

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setCodigo(rs.getInt("cliente_codigo"));
            dto.setClienteVO(clienteVO);
            lista.add(dto);
        }

        //filtrar o cliente
        for (ItemAuxiliarCartaoTO dto : lista) {
            if (UteisValidacao.emptyString(dto.getTitular()) ||
                    UteisValidacao.emptyString(autoVO.getNomeTitularCartao()) ||
                    UteisValidacao.emptyString(dto.getClienteVO().getPessoa().getNome()) ||
                    UteisValidacao.emptyString(dto.getNome())) {
                continue;
            }

//            String titularCard = autoVO.getNomeTitularCartao();
//            String titularTabela = dto.getTitular();
//            String omeTabela = dto.getNome();
//            String clienteNomeTabela = dto.getClienteVO().getPessoa().getNome();

            //verificar se o nome do titular e o nome do cliente é o mesmo.
            //devido a importação que o cliente tinha 2 matriculas externa iguais
            if (dto.getTitular().trim().equalsIgnoreCase(autoVO.getNomeTitularCartao().trim()) &&
                    dto.getNome().trim().equalsIgnoreCase(dto.getClienteVO().getPessoa().getNome().trim())) {
                autoVO.setCliente(dto.getClienteVO());
                autoVO.setObservacao("Nome titular e do aluno correspondem");
                return;
            }
        }
    }

    private void obterClienteTabelaAuxiliarEvo(AutorizacaoCobrancaClienteVO autoVO, Connection con) throws Exception {

        String ultimos = autoVO.getNumeroCartao().substring(autoVO.getNumeroCartao().length() - 4);
        if (UteisValidacao.emptyString(ultimos)) {
            throw new Exception("ultimos4Numeros nao informado | Titular " + autoVO.getNomeTitularCartao());
        }

        String nomeCliente = autoVO.getImportacaoCliente();
        if (UteisValidacao.emptyString(nomeCliente)) {
            throw new Exception("nomeCliente nao informado | Titular " + autoVO.getNomeTitularCartao());
        }

        String nomeTitular = autoVO.getNomeTitularCartao();
        if (UteisValidacao.emptyString(nomeTitular)) {
            throw new Exception("nomeTitular nao informado | Cartão: " + autoVO.getCartaoMascarado_Apresentar());
        }

        Uteis.logarDebug("Buscar obterClienteTabelaAuxiliarEvo | UltimosDigitos: " + ultimos + " | Nome Cliente: " + nomeTitular + " | Titular: " + nomeTitular);

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("distinct \n");
        sql.append("cl.codigo as cliente_codigo, \n");
        sql.append("cl.matricula as cliente_matricula, \n");
        sql.append("p.codigo as pessoa_codigo, \n");
        sql.append("p.nome as pessoa_nome \n");
        sql.append("from importacaocartaoauxiliar imp \n");
        sql.append("inner join cliente cl on cl.matriculaexterna = imp.matricula::integer \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa  \n");
        sql.append("where coalesce(imp.cartao_ultimos,'') <> '' \n");
        sql.append("and imp.cartao_ultimos = '").append(ultimos).append("' \n");
        sql.append("and (replace(UPPER(translate(regexp_replace(imp.titular, '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','') ilike ");
        sql.append("replace(UPPER(translate(regexp_replace('").append(nomeTitular.toUpperCase()).append("', '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','')) \n");
        sql.append("and (replace(UPPER(translate(regexp_replace(split_part(imp.nome, ' ', 1), '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','') ilike ");
        sql.append("replace(UPPER(translate(regexp_replace(split_part('").append(nomeCliente.toUpperCase()).append("', ' ', 1), '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','')) \n");

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        if (UteisValidacao.emptyNumber(total)) {
            Uteis.logarDebug("Nao foi encontrado nenhum registro na tabela importacaocartaoauxiliar com os últimos dígitos: " + ultimos + " | Titular: " + nomeTitular + " | Cliente: " + nomeCliente);
            return;
        }
        if (total > 1) {
            Uteis.logarDebug("Foi encontrado mais de 1 registro na tabela importacaocartaoauxiliar com os últimos dígitos: " + ultimos + " | Titular: " + nomeTitular + " | Cliente: " + nomeCliente);
            return;
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setCodigo(rs.getInt("pessoa_codigo"));
            pessoaVO.setNome(rs.getString("pessoa_nome"));

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setCodigo(rs.getInt("cliente_codigo"));
            clienteVO.setMatricula(rs.getString("cliente_matricula"));

            autoVO.setCliente(clienteVO);
            autoVO.setObservacao("Tabela auxiliar EVO");
        }
    }

    private StringBuilder readContentFile(final String fileName) throws IOException {
        FileInputStream fis = new FileInputStream(fileName);
        return new StringBuilder(Uteis.convertStreamToStringBuffer(fis, "UTF-8"));
    }

    public AutorizacaoCobrancaClienteVO importarIndividual(ImportacaoCartaoDTO cartaoDTO, Connection con) throws Exception {
        AutorizacaoCobrancaClienteVO obj = montarAutorizacaoCobrancaCliente(cartaoDTO, con);
        incluirAuto(obj, con);
        return obj;
    }

    private String executaRequestAPI(ImportacaoCartaoDTO cartaoDTO) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", this.authorization);

        ImportacaoCartaoAPIDTO dto = new ImportacaoCartaoAPIDTO();
        dto.setKey(this.key);
        dto.setTipo(this.tipoImportacao.name());
        dto.setEmpresa(this.empresa);
        dto.setConvenioCobranca(this.convenioCobranca);
        dto.setCard(cartaoDTO);
//        String dataCripto = Uteis.encriptar(dto.toString(), PropsService.getPropertyValue(PropsService.chaveCriptoImportCc));
        String dataCripto = new String(new Base64().encode(dto.toString().getBytes()));

        JSONObject jsonBody = new JSONObject();
        jsonBody.put("data", dataCripto);

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(Uteis.getUrlAPI() + "/prest/pactopay/" + this.key + "/import/cc", headers, null, jsonBody.toString(), MetodoHttpEnum.POST);
        if (respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            return respostaHttpDTO.getResponse();
        } else {
            throw new Exception(respostaHttpDTO.getResponse());
        }
    }
}

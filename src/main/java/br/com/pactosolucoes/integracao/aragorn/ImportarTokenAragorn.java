package br.com.pactosolucoes.integracao.aragorn;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.propriedades.PropsService;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 27/05/2020
 */
public class ImportarTokenAragorn {

    private static StringBuilder logGravar;

    public static void main(String[] args) throws IOException {
        logGravar = new StringBuilder();
        String diretorioLog = "";
        String nomeBancoDestino = "";
        try {
            //origem dos dados de onde foi importado
            Connection conOrigem = DriverManager.getConnection("***************************************************", "postgres", "pactodb");
            Integer codigoConvenioCobrancaOrigem = 2;

            //Onde a autorização será criada
            Connection conDestino = DriverManager.getConnection("***********************************************", "postgres", "pactodb");
            nomeBancoDestino = conDestino.getCatalog();
            Integer codigoConvenioCobrancaDestino = 2;

            //Diretório salvar log
            diretorioLog = "C:\\PactoJ\\log";


            importar(codigoConvenioCobrancaOrigem, conOrigem, codigoConvenioCobrancaDestino, conDestino);
        } catch (Exception ex) {
            adicionarLog(ex.getMessage());
            ex.printStackTrace();
        } finally {
            if (UteisValidacao.emptyString(diretorioLog)) {
                diretorioLog = PropsService.getPropertyValue(PropsService.diretorioArquivos);
            }

            String arquivo = Uteis.salvarArquivo("ImportarTokenAragorn--" + nomeBancoDestino + "--" + Calendario.hoje().getTime() + ".txt", getLogGravar().toString(), diretorioLog + File.separator);

            adicionarLog("###############################################################################################################");
            adicionarLog("###############################################################################################################");
            adicionarLog("########## ARQUIVO LOG -->> " + arquivo);
            adicionarLog("###############################################################################################################");
            adicionarLog("###############################################################################################################");
        }
    }

    public static void importar(Integer convenioOrigem, Connection conOrigem,
                                Integer convenioDestino, Connection conDestino) throws Exception {

        String nomeBancoOrigem = conOrigem.getCatalog();

        //consultar os alunos sem autorizacao de cobranca no banco de destino
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("p.codigo as pessoa, \n");
        sql.append("p.nome, \n");
        sql.append("cl.codigo as cliente, \n");
        sql.append("p.cfp as cpf \n");
        sql.append("from cliente cl \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
        sql.append("where coalesce(p.cfp, '') <> '' \n");
        sql.append("and not exists(select codigo from autorizacaocobrancacliente  where ativa and cliente = cl.codigo) \n");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", conDestino);

        adicionarLog("Total " + total + " - Alunos Verificar");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conDestino);

        int atual = 0;
        while (rs.next()) {
            String nome = "";
            try {

                Integer pessoa = rs.getInt("pessoa");
                Integer cliente = rs.getInt("cliente");
                nome = rs.getString("nome");
                String cpf = rs.getString("cpf");

                adicionarLog("Atual " + ++atual + "/" + total + " - " + nome);

                StringBuilder consulta = new StringBuilder();
                consulta.append("select \n");
                consulta.append("au.conveniocobranca, \n");
                consulta.append("au.tokenaragorn, \n");
                consulta.append("au.cartaomascaradointerno, \n");
                consulta.append("au.nometitularcartao, \n");
                consulta.append("au.validadecartao, \n");
                consulta.append("au.cvv, \n");
                consulta.append("au.cpftitular, \n");
                consulta.append("au.tipoacobrar, \n");
                consulta.append("au.listaobjetosacobrar, \n");
                consulta.append("au.operadoracartao \n");
                consulta.append("from cliente cl \n");
                consulta.append("inner join pessoa p on p.codigo = cl.pessoa \n");
                consulta.append("inner join autorizacaocobrancacliente au on au.cliente = cl.codigo \n");
                consulta.append("where (p.cfp = '").append(Uteis.formatarCpfCnpj(cpf, true)).append("' or p.cfp = '").append(Uteis.formatarCpfCnpj(cpf, false)).append("') \n");
                consulta.append("and au.ativa \n");
                consulta.append("and au.conveniocobranca = ").append(convenioOrigem).append(" \n");
                consulta.append("and au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId());

                Integer totalConsulta = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + consulta + " ) as sql", conOrigem);

                if (UteisValidacao.emptyNumber(totalConsulta)) {
                    throw new Exception("Nenhuma autorização encontrada para o CPF " + Uteis.formatarCpfCnpj(cpf, false));
                }

                adicionarLog("Encontrado " + totalConsulta + " - Autorização");
                ResultSet rsConsulta = SuperFacadeJDBC.criarConsulta(consulta.toString(), conOrigem);

                while (rsConsulta.next()) {

                    try {

                        String tokenaragorn = rsConsulta.getString("tokenaragorn");
                        String cartaomascaradointerno = rsConsulta.getString("cartaomascaradointerno");
                        String nometitularcartao = rsConsulta.getString("nometitularcartao");
                        String validadecartao = rsConsulta.getString("validadecartao");
                        String cvv = rsConsulta.getString("cvv");
                        String cpftitular = rsConsulta.getString("cpftitular");
                        Integer tipoacobrar = rsConsulta.getInt("tipoacobrar");
                        String listaobjetosacobrar = rsConsulta.getString("listaobjetosacobrar");
                        Integer operadoracartao = rsConsulta.getInt("operadoracartao");


                        String insert = "INSERT INTO AutorizacaoCobrancaCliente(cliente, tipoAutorizacao, tipoACobrar, listaObjetosACobrar, conveniocobranca, cpftitular, " +
                                "nomeTitularCartao, ativa, cartaomascaradointerno, operadoracartao, validadecartao, cvv, tokenAragorn, dataregistro) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?);";

                        int i = 0;
                        try (PreparedStatement ps = conDestino.prepareStatement(insert, Statement.RETURN_GENERATED_KEYS)) {
                            ps.setInt(++i, cliente);
                            ps.setInt(++i, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId());
                            ps.setInt(++i, tipoacobrar);
                            ps.setString(++i, listaobjetosacobrar);
                            ps.setInt(++i, convenioDestino);
                            ps.setString(++i, cpftitular);
                            ps.setString(++i, nometitularcartao);
                            ps.setBoolean(++i, true);
                            ps.setString(++i, cartaomascaradointerno);
                            ps.setInt(++i, operadoracartao);
                            ps.setString(++i, validadecartao);
                            ps.setString(++i, cvv);
                            ps.setString(++i, tokenaragorn);
                            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                            ps.execute();

                            Integer codigoAuto = 0;
                            ResultSet rs1 = ps.getGeneratedKeys();
                            if (rs1.next()) {
                                codigoAuto = rs1.getInt("codigo");
                            }

                            if (UteisValidacao.emptyNumber(codigoAuto)) {
                                throw new Exception("Autorização não incluida");
                            }
                            gerarLog(codigoAuto, pessoa, "IMPORTADO DO BANCO " + nomeBancoOrigem, conDestino);
                        }

                    } catch (Exception ex) {
                        adicionarLog("ERRO Incluir Autorizacao: " + nome + " | " + ex.getMessage());
                    }
                }
            } catch (Exception ex) {
                adicionarLog("ERRO Cliente: " + nome + " | " + ex.getMessage());
            }
        }
    }

    public static void gerarLog(Integer codigo, Integer pessoa, String descricao, Connection con) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("AUTORIZACAOCOBRANCACLIENTE");
            log.setNomeEntidadeDescricao("AUTORIZACAOCOBRANCACLIENTE");
            log.setOperacao("INCLUSÃO");
            log.setNomeCampo("IMPORTACAO");
            log.setResponsavelAlteracao("IMPORTACAO");
            log.setChavePrimaria(codigo.toString());
            log.setPessoa(pessoa);
            log.setValorCampoAlterado(descricao);
            log.setValorCampoAnterior("");
            Log logEntidade = new Log(con);
            logEntidade.incluirSemCommit(log);
            logEntidade = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}

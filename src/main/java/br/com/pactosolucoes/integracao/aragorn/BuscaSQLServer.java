package br.com.pactosolucoes.integracao.aragorn;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class BuscaSQLServer {

    public static void main(String... args) {
        try {
            String busca = "f57c570b4a6a53c54a96e248e3e104cd6f0db9d0";
            String bancoSQL = "engenharia";

//            f57c570b4a6a53c54a96e248e3e104cd6f0db9d0
//            135a7cc5a1ebdc26bcdc77d54beae4de4cfcdb8b

            buscar(busca, bancoSQL);
        } catch (Exception ex) {
            Logger.getLogger(BuscaSQLServer.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void buscar(String busca, String bancoSQL) throws Exception {
        Connection conSQL = sqlServerConnectionZW(bancoSQL);

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("sc.name as tabela, \n");
        sql.append("s.name as coluna \n");
        sql.append("from syscolumns s \n");
        sql.append("inner join sysobjects sc on  \n");
        sql.append("sc.id = s.id and sc.xtype = 'U' \n");
//        sql.append("where sc.name = 'CLIENTES' \n");

        PreparedStatement stm = conSQL.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();

        List<String> encontrei = new ArrayList<>();
        while (rs.next()) {
            String tabela = rs.getString("tabela");
            String coluna = rs.getString("coluna");
            try {

                String sql1 = "select count(1) as qtd from " + tabela + " where cast(" + coluna + " as varchar(255)) like '%" + busca + "%'";
//            System.out.println("Buscar... Tabela: " + tabela + " | Coluna: " + coluna + " | SQL: " + sql1);
                PreparedStatement stm1 = conSQL.prepareStatement(sql1);
                ResultSet rs1 = stm1.executeQuery();
                if (rs1.next()) {
                    if (rs1.getInt("qtd") > 0) {
                        System.out.println("#################");
                        System.out.println("#################");
                        String teste = "Achei \"" + busca + "\" | Tabela: " + tabela + " | Coluna: " + coluna;
                        System.out.println(teste);
                        encontrei.add(teste);
                        System.out.println("#################");
                        System.out.println("#################");
                    }
                }
            } catch (Exception ex) {
                System.out.println("ERRO | Tabela: " + tabela + " | Coluna: " + coluna + " | " + ex.getMessage());
            }

        }

        System.out.println("#######################");
        System.out.println("###### RESULTADO ######");
        System.out.println("#######################");
        for (String obj : encontrei) {
            System.out.println(obj);
        }
        System.out.println("#######################");
        System.out.println("#######################");
    }

    public static Connection sqlServerConnectionZW(String banco) throws ClassNotFoundException, SQLException, IllegalAccessException, InstantiationException {
        String driver = "net.sourceforge.jtds.jdbc.Driver";
        String conexao = "jdbc:jtds:sqlserver:";
        Class.forName(driver).newInstance();

        return DriverManager.getConnection(conexao + "//localhost:1433/" + banco, "sa", "pactodb");
    }
}

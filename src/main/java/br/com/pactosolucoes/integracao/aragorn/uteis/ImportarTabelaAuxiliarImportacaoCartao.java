package br.com.pactosolucoes.integracao.aragorn.uteis;

import importador.LeitorExcel2010;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.json.JSONObject;

import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 30/01/2023
 */
public class ImportarTabelaAuxiliarImportacaoCartao {

    public static void main(String[] args) {
        Connection con = null;
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "ImportarTabelaAuxiliarImportacaoCartao | INICIO...");

            //Deve ser usado o arquivo "padrao_importacao_tabela_auxiliar.xlsx"
            // como padrão para executar a importação da tabela auxiliar

            // Coluna A - Matricula igual a matricula externa no ZW (Caso não seja igual então deixar em branco)
            // Coluna B - NOME DO ALUNO
            // Coluna C - CPF DO ALUNO
            // Coluna D - ID DO CARTAO
            // Coluna E - NOME DO TITULAR DO CARTÃO
            // Coluna F - NUMERO DO CARTÃO
            // Coluna G - ULTIMOS 4 DIGITOS DO CARTÃO

            // CASO O EXCEL DE ORIGEM QUE FOI PASSADO TENHA MAIS COLUNAS. ADICIONAR APÓS A COLUNA G (DEVE SER COLOCADO NA LINHA 1 UM NOME PARA O DADO)
            // ASSIM QUANDO RODAR ESSA IMPORTAÇÃO TODOS OS DADOS DO EXCEL SERÃO SALVOS.

            con = DriverManager.getConnection("********************************************", "postgres", "pactodb");
            String arquivo = "C:\\Pacto\\live\\padrao_importacao_tabela_auxiliar.xlsx";

            processar(arquivo, con);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(true, null, "ImportarTabelaAuxiliarImportacaoCartao | ERRO: " + ex.getMessage());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            Uteis.logar(true, null, "ImportarTabelaAuxiliarImportacaoCartao | FIM...");
        }
    }

    public static void criarTabela(Connection con) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE importacaocartaoauxiliar ( \n");
        sql.append("codigo serial PRIMARY KEY, \n");
        sql.append("dataregistro TIMESTAMP WITHOUT TIME ZONE, \n");
        sql.append("matricula varchar, \n");
        sql.append("nome varchar, \n");
        sql.append("cpf varchar, \n");
        sql.append("cartao varchar, \n");
        sql.append("cartao_ultimos varchar, \n");
        sql.append("titular varchar, \n");
        sql.append("id_cartao varchar, \n");
        sql.append("id_importacao varchar, \n");
        sql.append("dados text) \n");
        SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), con);
        SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_importacaocartaoauxiliar_id_importacao ON importacaocartaoauxiliar USING btree (id_importacao);", con);
        SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_importacaocartaoauxiliar_matricula ON public.importacaocartaoauxiliar USING btree (matricula);", con);
    }

    public static void processar(String arquivo, Connection con) throws Exception {
        criarTabela(con);

        String id_importacao = String.valueOf(new Date().getTime());

        //essa matricula deve ser a mesma da matricula externa do cliente
        Integer colunaExcelMatricula = 0;            //Coluna A
        Integer colunaExcelNomeAluno = 1;            //Coluna B
        Integer colunaExcelCPF = 2;                  //Coluna C
        Integer colunaExcelCartaoId = 3;             //Coluna D
        Integer colunaExcelCartaoTitular = 4;        //Coluna E
        Integer colunaExcelCartao = 5;               //Coluna F
        Integer colunaExcelCartaoUltimosDigitos = 6; //Coluna G

        XSSFRow primeiraLinha = LeitorExcel2010.obterLinha(0, new FileInputStream(arquivo), 0);
        List<XSSFRow> hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(arquivo));
        int atual = 0;
        for (XSSFRow linha : hssfRows) {
            String matricula = "";
            try {
                Uteis.logarDebug(++atual + "/" + hssfRows.size());
                int qtdColunas = linha.getPhysicalNumberOfCells();

                matricula = LeitorExcel2010.obterString(linha, colunaExcelMatricula);
                String nome = LeitorExcel2010.obterString(linha, colunaExcelNomeAluno);
                String cartao = LeitorExcel2010.obterString(linha, colunaExcelCartao);
                String cartao_ultimos = LeitorExcel2010.obterString(linha, colunaExcelCartaoUltimosDigitos);
                String titular = LeitorExcel2010.obterString(linha, colunaExcelCartaoTitular);
                String card_id = LeitorExcel2010.obterString(linha, colunaExcelCartaoId);
                String cpf = LeitorExcel2010.obterString(linha, colunaExcelCPF);

                JSONObject json = new JSONObject();
                int li = 0;
                while (li <= qtdColunas) {
                    json.put(LeitorExcel2010.obterString(primeiraLinha, li).toLowerCase(), LeitorExcel2010.obterString(linha, li));
                    li++;
                }

                incluir(matricula, nome, cartao, cartao_ultimos, titular, card_id, id_importacao, json.toString(), cpf, con);
            } catch (Exception ex) {
                Uteis.logarDebug("Mat. " + matricula + " | ERRO: " + ex.getMessage());
            }
        }
    }

    public static void incluir(String matricula, String nome, String cartao, String ultimos, String titular,
                                String id_cartao, String id_importacao, String dados, String cpf, Connection con) throws SQLException {
        String sql = "INSERT INTO importacaocartaoauxiliar(dataregistro, matricula, nome, cartao, cartao_ultimos," +
                "titular, id_cartao, id_importacao, dados, cpf) VALUES (?,?,?,?,?,?,?,?,?,?);";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setString(++i, matricula);
            pst.setString(++i, nome);
            pst.setString(++i, cartao);
            pst.setString(++i, ultimos);
            pst.setString(++i, titular);
            pst.setString(++i, id_cartao);
            pst.setString(++i, id_importacao);
            pst.setString(++i, dados);
            pst.setString(++i, Uteis.formatarCpfCnpj(cpf, true));
            pst.execute();
        }
    }
}

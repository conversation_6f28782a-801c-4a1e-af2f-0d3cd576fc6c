package br.com.pactosolucoes.integracao.aragorn;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.enumerador.TipoBloqueioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Pessoa;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 20/01/2022
 */
public class ExportarStripe {

    public static void main(String[] args) {
        Connection con = null;
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "ExportarStripe | INICIO...");

            if (args != null && args.length > 0) {
                Uteis.logar(true, null, "ExportarStripe | ARGS...");
                int argI = 0;
                for (String arg : args) {
                    Uteis.logar(true, null, "Args[" + ++argI + "] | " + arg);
                }
            }

            String chave = args[0];
//            String chave = "teste";

            //producao personalfit
//            String urlAPI = ""; //url da api
//            String apiKey = "***********************************************************************************************************";//producao personalfit
//            String price = "price_1JhiSGCIyS0Xgq00JlC6WYmI"; //price na stripe 49,90

            //ambiente de teste
            String urlAPI = "http://localhost:8088/stripe"; //url da api
            String apiKey = "sk_test_51KO5v1L6ZeXjBqMEv62JADHM0G3booqiJFTOmja7x5eSUDciTC6AT3XlCV8THLx1VLDbgrTX0UESyanF4nt7CTHZ00Q9vccpg7"; //ambiente de teste
            String price = "price_1KO5w1L6ZeXjBqMENtHnWVF3"; //price na stripe 49,90


            Uteis.logar(null, "Obter conexão para chave: " + chave);
            con = new DAO().obterConexaoEspecifica(chave);

            StringBuilder sql = new StringBuilder();
            sql.append("CREATE TABLE exportastripe ( \n");
            sql.append("codigo serial PRIMARY KEY,  \n");
            sql.append("dataregistro TIMESTAMP WITHOUT TIME ZONE,  \n");
            sql.append("cliente integer,  \n");
            sql.append("resultado text, \n");
            sql.append("sucesso boolean) \n");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), con);
            processar(urlAPI, apiKey, price, con);
        } catch (Exception ex) {
            System.err.println(ex.getMessage());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            Uteis.logar(true, null, "ExportarStripe | FIM...");
        }
    }

    private static void incluirLog(Integer cliente, JSONObject resultado, boolean sucesso, Connection con) throws Exception {
        String sql = "INSERT INTO exportastripe(dataRegistro, cliente, resultado, sucesso) VALUES (?,?,?,?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setInt(++i, cliente);
            ps.setString(++i, resultado.toString());
            ps.setBoolean(++i, sucesso);
            ps.execute();
        }
    }

    private static void processar(String urlAPI, String apiKey, String price, Connection con) throws Exception {
        AragornService aragornService;
        try {

            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("p.codigo as pessoa, \n");
            sql.append("cl.codigo as cliente, \n");
            sql.append("cl.matricula, \n");
            sql.append("p.nome, \n");
            sql.append("tel.tipotelefone, \n");
            sql.append("tel.numero as celular, \n");
            sql.append("em.email, \n");
            sql.append("auto.codigo as auto, \n");
            sql.append("auto.tokenaragorn, \n");
            sql.append("mov.valorparcela, \n");
            sql.append("mov.datavencimento \n");
            sql.append("from cliente cl  \n");
            sql.append("inner join pessoa p on p.codigo = cl.pessoa  \n");
            sql.append("left join email em on em.codigo = (select max(codigo) from email where pessoa = p.codigo) \n");
            sql.append("left join telefone tel on tel.codigo = (select max(codigo) from telefone where pessoa = p.codigo) \n");
            sql.append("left join autorizacaocobrancacliente auto on auto.codigo = (select max(codigo) from autorizacaocobrancacliente where ativa and cliente = cl.codigo) \n");
            sql.append("left join movparcela mov on mov.codigo = (select codigo from movparcela where pessoa = p.codigo order by datavencimento desc limit 1) \n");
            sql.append("where not exists(select codigo from exportastripe  where cliente = cl.codigo) \n");
            sql.append("and cl.situacao = 'AT' \n");
            sql.append("order by cl.codigo \n");
//            sql.append("limit 1 \n");

            Usuario usuarioDAO = new Usuario(con);
            UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
            usuarioDAO = null;

            aragornService = new AragornService();
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                JSONObject resultado = new JSONObject();
                Integer cliente = rs.getInt("cliente");
                Integer pessoa = rs.getInt("pessoa");
                RequestHttpService service;
                Pessoa pessoaDAO;
                boolean sucesso = false;
                try {
                    con.setAutoCommit(false);

                    service = new RequestHttpService();
                    pessoaDAO = new Pessoa(con);

                    resultado.put("cliente", cliente);
                    resultado.put("pessoa", pessoa);

                    JSONObject jsonEnvio = new JSONObject();
                    jsonEnvio.put("price", price);//price na stripe 49,90

                    if (apiKey.toUpperCase().contains("SK_TEST_")) {
                        jsonEnvio.put("nome", "LUIZ FELIPE ALMEIDA");
                        jsonEnvio.put("email", "<EMAIL>");
                        jsonEnvio.put("celular", "(62)983239757");
//                        NazgDTO nazgDTO = aragornService.obterNazg(rs.getString("tokenaragorn"));

                        jsonEnvio.put("cartao", "5226885160996188");//cartão de teste
                        jsonEnvio.put("titular", "LUIZ TESTE");
                        jsonEnvio.put("validade", "04/2029");
                        jsonEnvio.put("cvc", ""); //nao tem cvv
                    } else {
                        jsonEnvio.put("nome", rs.getString("nome"));
                        jsonEnvio.put("email", rs.getString("email"));
                        jsonEnvio.put("celular", rs.getString("celular"));
                        NazgDTO nazgDTO = aragornService.obterNazg(rs.getString("tokenaragorn"));

                        jsonEnvio.put("cartao", nazgDTO.getCard());
                        jsonEnvio.put("titular", nazgDTO.getName());
                        jsonEnvio.put("validade", nazgDTO.getValidadeMMYYYY(true));
                        jsonEnvio.put("cvc", ""); //nao tem cvv
                    }


                    Date dataVencimentoUltimaParcela = rs.getDate("datavencimento");

                    Integer diasAteVencer = Calendario.diferencaEmDias(Calendario.hoje(), Calendario.somarMeses(dataVencimentoUltimaParcela, 1));
                    jsonEnvio.put("diasTeste", diasAteVencer); //colocar como trial até o dia que vencer as cobranças
//                    jsonEnvio.put("inicio", Calendario.getDataAplicandoFormatacao(Calendario.somarMeses(dataVencimentoUltimaParcela, 1), "yyyyMMdd"));

                    resultado.put("envio", jsonEnvio);//salvar json de envio para log

                    Map<String, String> headers = new HashMap<>();
                    headers.put("apiKey", apiKey);
                    headers.put("Content-Type", "application/json");
                    RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlAPI + "/assinatura", headers, null, jsonEnvio.toString(), MetodoHttpEnum.POST);
                    System.out.println(respostaHttpDTO.getResponse());
                    resultado.put("resposta", respostaHttpDTO.getResponse());

                    JSONObject jsonResposta;
                    try {
                        jsonResposta = new JSONObject(respostaHttpDTO.getResponse());
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        throw new Exception(respostaHttpDTO.getResponse());
                    }

                    String subscriptionId = jsonResposta.optString("subscriptionId");
                    if (!UteisValidacao.emptyString(subscriptionId)) {
                        //bloquear as cobranças 1 dia depois
                        pessoaDAO.alterarDataBloqueioCobrancaAutomatica(
                                Calendario.somarDias(dataVencimentoUltimaParcela, 1),
                                TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS,
                                pessoa,
                                usuarioVO, false,
                                "Migração para a Stripe - Bloquear 1 dia após a data da ultima parcela (" + Calendario.getDataAplicandoFormatacao(dataVencimentoUltimaParcela, "dd/MM/yyyy") + "). SubscriptionId: " + subscriptionId);
                    } else {
                        throw new Exception(respostaHttpDTO.getResponse());
                    }

                    sucesso = true;
                    con.commit();
                } catch (Exception ex) {
                    ex.printStackTrace();
                    resultado.put("erro", ex.getMessage());
                    con.rollback();
                } finally {
                    con.setAutoCommit(true);
                    resultado.put("sucesso", sucesso);
                    incluirLog(cliente, resultado, sucesso, con);
                    service = null;
                    pessoaDAO = null;
                }
            }

        } catch (Exception ex) {
            System.err.println(ex.getMessage());
            Uteis.logar(true, null, "ExportarStripe | descriptografarArquivo...");
            throw ex;
        } finally {
            aragornService = null;
        }
    }

}

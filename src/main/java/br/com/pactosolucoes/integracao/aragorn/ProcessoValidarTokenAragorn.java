package br.com.pactosolucoes.integracao.aragorn;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 08/04/2022
 */
public class ProcessoValidarTokenAragorn {

    public static void main(String[] args) throws IOException {
        try {
            Uteis.debug = true;
            Uteis.logar("ProcessoValidarTokenAragorn | INICIO!");

            String chave = null;
            if (args.length == 0) {
                args = new String[]{"teste"};
            }
            if (args.length > 0) {
                chave = args[0];
            }

            Uteis.logar(null, "Obter conexão para chave: " + chave);
            Connection con = new DAO().obterConexaoEspecifica(chave);
//            Connection con = DriverManager.getConnection("************************************************", "postgres", "pactodb");

            validar(con);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar("ERRO: " + ex.getMessage());
        } finally {
            Uteis.logar("ProcessoValidarTokenAragorn | FIM!");
        }
    }

    public static void validar(Connection con) throws Exception {
        AragornService aragornService;
        try {
            aragornService = new AragornService();

            StringBuilder sql = new StringBuilder();
            sql.append("select 'cliente' as tipo, codigo,tokenaragorn from autorizacaocobrancacliente where ativa and tipoautorizacao = 1 and coalesce(tokenaragorn,'') <> '' \n");
            sql.append("union \n");
            sql.append("select 'colaborador' as tipo, codigo,tokenaragorn from autorizacaocobrancacolaborador where ativa and tipoautorizacao = 1 and coalesce(tokenaragorn,'') <> '' \n");
            sql.append("order by 1,2 \n");

            List<String> tokens = new ArrayList<>();
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                try {
                    tokens.add(rs.getString("tokenaragorn"));
                } catch (Exception ex) {
                    Uteis.logar("ProcessoValidarTokenAragorn | ERRO: " + ex.getMessage());
                }
            }

            Uteis.logar("ProcessoValidarTokenAragorn | Token Total: " + tokens.size());
            Map<String, NazgDTO> mapaAragorn = aragornService.obterMapaNazg(tokens);
            Uteis.logar("ProcessoValidarTokenAragorn | MapaAragorn Total: " + mapaAragorn.size());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            aragornService = null;
        }
    }
}

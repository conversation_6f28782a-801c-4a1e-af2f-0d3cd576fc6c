package br.com.pactosolucoes.integracao.aragorn;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.impl.apf.APF;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/01/2020
 */
public class MigradorAragorn {

    private Connection con;
    private AragornService aragornService;

    public MigradorAragorn(Connection con) {
        this.con = con;
        aragornService = new AragornService();
    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "INICIO | MigradorAragorn...");

            String chave = null;
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }

            Connection conOAMD = Conexao.obterConexaoBancoEmpresas();
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT chave from empresa \n");
            sql.append("WHERE 1 = 1 \n");
            if (!UteisValidacao.emptyString(chave)) {
                sql.append(" AND chave = '").append(chave).append("'");
            }

            PreparedStatement st = conOAMD.prepareStatement(sql.toString());
            ResultSet rs = st.executeQuery();
            while (rs.next()) {
                chave = rs.getString("chave");
                Connection con = null;
                try {
                    Uteis.logar(null, "Obter conexão para chave: " + chave);
                    con = new DAO().obterConexaoEspecifica(chave);
                    MigradorAragorn migradorAragorn = new MigradorAragorn(con);
                    migradorAragorn.atualizarTabelas();
                    migradorAragorn.migrarAutorizacoesClientes();
                    migradorAragorn.migrarAutorizacoesColaboradores();
                    migradorAragorn = null;
                } catch (Exception ex) {
                    Uteis.logar(null, "Erro chave: " + chave);
                    ex.printStackTrace();
                } finally {
                    if (con != null) {
                        con.close();
                    }
                }
            }

            Uteis.logar(true, null, "FIM | MigradorAragorn...");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public JSONObject migrarAutorizacoesClientes() {
        Integer sucesso = 0;
        Integer falha = 0;
        try {
            Uteis.logar(true, null, "INICIO | AtualizarAutorizacao de Clientes...");

            String sql = "select codigo, numerocartao, cvv, validadecartao, cpftitular, nometitularcartao, operadoracartao from autorizacaocobrancacliente " +
                    "where coalesce(tokenAragorn, '') = '' and coalesce(numerocartao, '') <> '' and tipoautorizacao = " + TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId() + " order by codigo";

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            int i = 0;

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                Integer autorizacao = 0;
                try {
                    con.setAutoCommit(false);

                    Uteis.logar(true, null, "Auto Cliente... " + ++i + "/" + total);

                    autorizacao = rs.getInt("codigo");
                    String numerocartao = APF.decifrar(rs.getString("numerocartao"));
                    String cvv = rs.getString("cvv");
                    String validadecartao = rs.getString("validadecartao");
                    String cpftitular = rs.getString("cpftitular");
                    String nometitularcartao = rs.getString("nometitularcartao");
                    Integer operadoracartao = rs.getInt("operadoracartao");

                    NazgDTO nazgDTO = gerarEnviarNazgDTO(numerocartao, cvv, validadecartao, cpftitular, nometitularcartao, operadoracartao);

                    if (!UteisValidacao.emptyString(nazgDTO.getToken())) {
                        SuperFacadeJDBC.executarUpdate("update autorizacaocobrancacliente set tokenAragorn = '" + nazgDTO.getToken() + "' where codigo = " + autorizacao, con);
                    } else {
                        throw new Exception("Token não gerado!");
                    }
                    ++sucesso;
                    con.commit();
                } catch (Exception ex) {
                    ++falha;
                    con.rollback();
                    ex.printStackTrace();
                    Uteis.logar(true, null, "AutorizacaoCliente :" + autorizacao + " | " + ex.getMessage());
                } finally {
                    con.setAutoCommit(true);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            Uteis.logar(true, null, "FIM | AtualizarAutorizacao de Clientes...");
        }

        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("falha", falha);
        return json;
    }

    public JSONObject migrarAutorizacoesColaboradores() {
        Integer sucesso = 0;
        Integer falha = 0;
        try {

            Uteis.logar(true, null, "INICIO | AtualizarAutorizacao de Colaboradores...");

            String sql = "select codigo, numerocartao, validadecartao, cpftitular, nometitularcartao, operadoracartao from autorizacaocobrancacolaborador " +
                    "where coalesce(tokenAragorn, '') = '' and coalesce(numerocartao, '') <> '' and tipoautorizacao = " + TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId() + " order by codigo";

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            int i = 0;

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                Integer autorizacao = 0;
                try {
                    con.setAutoCommit(false);

                    Uteis.logar(true, null, "Auto Colaborador... " + ++i + "/" + total);

                    autorizacao = rs.getInt("codigo");
                    String numerocartao = APF.decifrar(rs.getString("numerocartao"));
                    String validadecartao = rs.getString("validadecartao");
                    String cpftitular = rs.getString("cpftitular");
                    String nometitularcartao = rs.getString("nometitularcartao");
                    Integer operadoracartao = rs.getInt("operadoracartao");

                    NazgDTO nazgDTO = gerarEnviarNazgDTO(numerocartao, "", validadecartao, cpftitular, nometitularcartao, operadoracartao);

                    if (!UteisValidacao.emptyString(nazgDTO.getToken())) {
                        String cartaoMascarado = APF.getCartaoMascarado(numerocartao);
                        SuperFacadeJDBC.executarUpdate("update autorizacaocobrancacolaborador set cartaomascaradointerno = '" + cartaoMascarado + "', tokenAragorn = '" + nazgDTO.getToken() + "' where codigo = " + autorizacao, con);
                    } else {
                        throw new Exception("Token não gerado!");
                    }
                    ++sucesso;
                    con.commit();
                } catch (Exception ex) {
                    ++falha;
                    con.rollback();
                    ex.printStackTrace();
                    Uteis.logar(true, null, "AutorizacaoColaborador :" + autorizacao + " | " + ex.getMessage());
                } finally {
                    con.setAutoCommit(true);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            Uteis.logar(true, null, "FIM | AtualizarAutorizacao de Colaboradores...");
        }

        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("falha", falha);
        return json;
    }

    private NazgDTO gerarEnviarNazgDTO(String numerocartao, String cvv, String validadecartao,
                                       String cpftitular, String nometitularcartao, Integer operadoracartao) throws Exception {
        NazgDTO nazgDTO = new NazgDTO();
        nazgDTO.setCard(numerocartao);
//        nazgDTO.setCvv(cvv);
        nazgDTO.setCpf(cpftitular);
        nazgDTO.setName(nometitularcartao);
        nazgDTO.setFlag(operadoracartao);
        nazgDTO.setMonth(getMesValidade(validadecartao));
        nazgDTO.setYear(getAnoValidade(validadecartao));

        //enviar aragorn
        aragornService.enviarNazg(nazgDTO);
        return nazgDTO;
    }


    private Integer getMesValidade(String validade) {
        if (!UteisValidacao.emptyString(validade)) {
            return Integer.parseInt(validade.substring(0, 2));
        }
        return null;
    }

    private Integer getAnoValidade(String validade) {
        if (!UteisValidacao.emptyString(validade)) {
            if (validade.length() == 7) {
                return Integer.valueOf(validade.substring(3, 7));
            } else if (validade.length() == 5) {
                return Integer.valueOf("20" + validade.substring(3, 5));
            } else {
                return Integer.parseInt(validade.split("/")[1]);
            }
        }
        return null;
    }

    public void atualizarTabelas() {
        try {
            Uteis.logar(true, null, "INICIO | AtualizarTabelas...");
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacliente ADD COLUMN tokenAragorn character varying(120);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacolaborador ADD COLUMN tokenAragorn character varying(120);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacolaborador ADD COLUMN cartaomascaradointerno CHARACTER VARYING(20);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE transacao ADD COLUMN tokenAragorn character varying(120);", con);

            //limpar as autorizacoes
//            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacliente ADD COLUMN numerotemp CHARACTER VARYING(100);", con);
//            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacliente ADD COLUMN cvvtemp CHARACTER VARYING(4);", con);
//            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE autorizacaocobrancacliente SET numerotemp  = numerocartao, cvvtemp = cvv WHERE tipoautorizacao = 1 and coalesce(numerocartao, '') <> '';", con);
//            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacolaborador ADD COLUMN numerotemp CHARACTER VARYING(100);", con);
//            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacolaborador ADD COLUMN cvvtemp CHARACTER VARYING(4);", con);
//            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE autorizacaocobrancacolaborador SET numerotemp  = numerocartao WHERE tipoautorizacao = 1 and coalesce(numerocartao, '') <> '';", con);
//            SuperFacadeJDBC.executarUpdateExecutarProcessos("update autorizacaocobrancacliente set numerocartao = '' WHERE tipoautorizacao = 1;", con);
//            SuperFacadeJDBC.executarUpdateExecutarProcessos("update autorizacaocobrancacolaborador set numerocartao = '' WHERE tipoautorizacao = 1;", con);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            Uteis.logar(true, null, "FIM | AtualizarTabelas...");
        }
    }
}

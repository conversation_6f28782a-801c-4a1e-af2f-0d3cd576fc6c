package br.com.pactosolucoes.integracao.aragorn;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import importador.LeitorExcel;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.validator.CreditCardValidator;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 26/09/2022
 */
public class ImportarCartaoWinner {

    public static void main(String[] args) {
        Connection con = null;
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "ImportarCartaoWinner | INICIO...");

            if (args != null && args.length > 0) {
                Uteis.logar(true, null, "ImportarCartaoWinner | ARGS...");
                int argI = 0;
                for (String arg : args) {
                    Uteis.logar(true, null, "Args[" + ++argI + "] | " + arg);
                }
            }

            String chave = args[0];
            String empresa = args[1];
            String convenio = args[2];
            String pathBase = args[3];
            String nomeArq = args[4];

//            String chave = "teste";
//            String convenio = "2";
//            String pathBase = "C:\\PactoJ\\winner\\excel";
//            String nomeArq = "card.csv";

            Uteis.logar(null, "Obter conexão para chave: " + chave);
            con = new DAO().obterConexaoEspecifica(chave);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacliente ADD COLUMN obsimportacao text;", con);
            processar(pathBase, nomeArq, Integer.parseInt(convenio), con);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(true, null, "ImportarCartaoWinner | ERRO: " + ex.getMessage());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            Uteis.logar(true, null, "ImportarCartaoWinner | FIM...");
        }
    }

    public static void processar(String pathBase, String nomeArq, Integer convenio, Connection con) throws Exception {
        Map<String, List<AutorizacaoCobrancaClienteVO>> mapaAuto = new HashMap<>();
//        List<HSSFRow> linhasCartao = LeitorExcel.lerLinhas(pathBase + File.separator + nomeArq);
//        for (HSSFRow linhaCompleta : linhasCartao) {
        List<String[]> linhasCard = lerArquivo(pathBase + File.separator + nomeArq);
        for (String[] linhaT : linhasCard) {
            try {
//                String linha = LeitorExcel.obterString(linhaCompleta, 0);
                String linha = linhaT[0];
                if (linha.startsWith("vault_type")) {
                    continue;
                }

                String[] itens = linha.split(",");
                String token = itens[1];
                String externalId = itens[2];
                String card = itens[4];
                String titular = itens[5];
                String vencimento = itens[6];

                if (!vencimento.contains("/") || vencimento.length() != 7) {
                    throw new Exception("Vencimento incorreto: " + token + " | " + titular + " | " + vencimento);
                }

                JSONObject jsonObs = new JSONObject();
                jsonObs.put("token", token);
                jsonObs.put("externalId", externalId);

                card = card.replaceAll("[^0-9]", "");
                String ultimos = (card.substring(card.length() - 4, card.length()));

                List<AutorizacaoCobrancaClienteVO> listaAuto = mapaAuto.get(ultimos);
                if (listaAuto == null) {
                    listaAuto = new ArrayList<>();
                }

                AutorizacaoCobrancaClienteVO novo = new AutorizacaoCobrancaClienteVO();
                novo.setObservacao(jsonObs.toString());
                novo.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
                novo.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
                novo.setNumeroCartao(card);
                novo.setNomeTitularCartao(titular);
                novo.setValidadeCartao(vencimento);

                try {
                    CreditCardValidator cartaoCredito = new CreditCardValidator();
                    novo.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(cartaoCredito.operadora(card)));
                    if (novo.getOperadoraCartao() == null) {
                        try {
                            UteisValidacao.validarNumeroCartaoCreditoElo(card);
                            novo.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.ELO);
                        } catch (Exception ignored) {
                        }
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug("INFO: Não foi possível identificar operadora cartão: " + token + " | " + titular + " | " + vencimento);
                }
                if (novo.getOperadoraCartao() == null) {
//                throw new Exception("Cartão inválido, revise os dados do cartão e tente novamente.");
                    novo.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.MASTERCARD);
                }

                novo.setConvenio(new ConvenioCobrancaVO());
                novo.getConvenio().setCodigo(convenio);
                novo.setCliente(new ClienteVO());

                try {
                    UteisValidacao.validarVencimentoCartao(getValidadeMMYYYY(true, novo.getMesValidade(), novo.getAnoValidade()));
                } catch (Exception ex) {
                    throw new Exception("Cartão vencido: " + token + " | " + titular + " | " + vencimento);
                }

                listaAuto.add(novo);
                mapaAuto.put(ultimos, listaAuto);
            } catch (Exception ex) {
                Uteis.logarDebug("ERRO: " + ex.getMessage());
            }
        }

        Integer totalGeral = 0;
        Integer totalFalha = 0;
        Integer totalSucesso = 0;
        Integer clientesNaoEncontrado = 0;
        Integer clientesMesmoNome = 0;

        List<HSSFRow> linhasRelacionamento = LeitorExcel.lerLinhas(pathBase + File.separator + "last.xls");
        for (HSSFRow linha : linhasRelacionamento) {
            try {
                totalGeral++;

                String nomeExcel = LeitorExcel.obterString(linha, 0);
                Integer ultimosInt = LeitorExcel.obterNumero(linha, 4).intValue();
                String ultimos = ultimosInt.toString();

                if (UteisValidacao.emptyString(ultimos)) {
                    throw new Exception("Não foi encontrado os últimos dígitos do cartão " + nomeExcel);
                }

                ultimos = ultimos.replace(" ", "");

                if (ultimos.length() < 4) {
                    ultimos = StringUtilities.formatarCampoForcandoZerosAEsquerda(ultimos, 4);
                } else if (ultimos.length() > 4) {
                    ultimos = ultimos.substring(ultimos.length() - 4, ultimos.length());
                }

                AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO = null;
                List<AutorizacaoCobrancaClienteVO> listaAuto = mapaAuto.get(ultimos);

                String infoAutorizacao = "";
                if (listaAuto == null || listaAuto.isEmpty()) {
                    throw new Exception("Nenhum cartão encontrado com o final " + ultimos + " | Nome: " + nomeExcel);
                } else if (listaAuto.size() == 1) {
                    autorizacaoCobrancaClienteVO = listaAuto.get(0);
                    infoAutorizacao = "Encontrou somente um cartao";
                } else {

                    //buscar pelo nome e sobrenome
                    for (AutorizacaoCobrancaClienteVO autoVO : listaAuto) {
                        String primeiroNomeAluno = Uteis.getPrimeiroNome(nomeExcel);
                        String sobrenomeAluno = Uteis.getSobrenome(nomeExcel);
                        String primeiroNomeCartao = Uteis.getPrimeiroNome(autoVO.getNomeTitularCartao());
                        String sobrenomeCartao = Uteis.getSobrenome(autoVO.getNomeTitularCartao());

                        if (UteisValidacao.emptyString(primeiroNomeAluno) ||
                                UteisValidacao.emptyString(sobrenomeAluno)||
                                UteisValidacao.emptyString(primeiroNomeCartao) ||
                                UteisValidacao.emptyString(sobrenomeCartao)) {
                            continue;
                        }

                        if (primeiroNomeAluno.equalsIgnoreCase(primeiroNomeCartao) && sobrenomeAluno.equalsIgnoreCase(sobrenomeCartao)) {
                            autorizacaoCobrancaClienteVO = autoVO;
                            infoAutorizacao = "Primeiro nome e sobrenome";
                            break;
                        }
                    }

                    //se não encontrou buscar pelo sobrenome
                    if (autorizacaoCobrancaClienteVO == null) {
                        for (AutorizacaoCobrancaClienteVO autoVO : listaAuto) {
                            String primeiroNomeAluno = Uteis.getPrimeiroNome(nomeExcel);
                            String sobrenomeAluno = Uteis.getSobrenome(nomeExcel);
                            String primeiroNomeCartao = Uteis.getPrimeiroNome(autoVO.getNomeTitularCartao());
                            String sobrenomeCartao = Uteis.getSobrenome(autoVO.getNomeTitularCartao());

                            if (UteisValidacao.emptyString(sobrenomeAluno) ||
                                    UteisValidacao.emptyString(sobrenomeCartao)) {
                                continue;
                            }
                            if (sobrenomeCartao.equalsIgnoreCase(sobrenomeAluno)) {
                                autorizacaoCobrancaClienteVO = autoVO;
                                infoAutorizacao = "Somente sobrenome";
                                break;
                            }
                        }
                    }

                    //se não encontrou verificar se o sobrenome do aluno tem no nome do cartão
                    if (autorizacaoCobrancaClienteVO == null) {
                        for (AutorizacaoCobrancaClienteVO autoVO : listaAuto) {
                            String sobrenomeAluno = Uteis.getSobrenome(nomeExcel);

                            if (UteisValidacao.emptyString(sobrenomeAluno) ||
                                    UteisValidacao.emptyString(autoVO.getNomeTitularCartao())) {
                                continue;
                            }
                            if (autoVO.getNomeTitularCartao().toUpperCase().contains(sobrenomeAluno.toUpperCase())) {
                                autorizacaoCobrancaClienteVO = autoVO;
                                infoAutorizacao = "Sobrenome tem no nome do titular";
                                break;
                            }
                        }
                    }

                    //se não encontrou buscar pelo primeiro nome
                    if (autorizacaoCobrancaClienteVO == null) {
                        for (AutorizacaoCobrancaClienteVO autoVO : listaAuto) {
                            String primeiroNomeAluno = Uteis.getPrimeiroNome(nomeExcel);
                            String sobrenomeAluno = Uteis.getSobrenome(nomeExcel);
                            String primeiroNomeCartao = Uteis.getPrimeiroNome(autoVO.getNomeTitularCartao());
                            String sobrenomeCartao = Uteis.getSobrenome(autoVO.getNomeTitularCartao());

                            if (UteisValidacao.emptyString(primeiroNomeAluno) ||
                                    UteisValidacao.emptyString(primeiroNomeCartao)) {
                                continue;
                            }

                            if (primeiroNomeAluno.equalsIgnoreCase(primeiroNomeCartao)) {
                                autorizacaoCobrancaClienteVO = autoVO;
                                infoAutorizacao = "Somente primeiro nome";
                                break;
                            }
                        }
                    }
                }

                if (autorizacaoCobrancaClienteVO == null) {
                    throw new Exception("Encontrado " + listaAuto.size() + " cartões com o final " + ultimos + " | Nenhum compatível de acordo com o nome do aluno");
                }

                try {
                    JSONObject json = new JSONObject(autorizacaoCobrancaClienteVO.getObservacao());
                    json.put("info", infoAutorizacao);
                    autorizacaoCobrancaClienteVO.setObservacao(json.toString());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                String sql = obterSQLCliente(nomeExcel);
                Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as a", con);
                if (UteisValidacao.emptyNumber(total)) {
                    clientesNaoEncontrado++;
                    throw new Exception("Cliente não encontrado com o nome \"" + nomeExcel + "\" ");
                } else if (total > 1) {
                    clientesMesmoNome++;
                    throw new Exception("Mais de 1 cliente encontrado com o nome \"" + nomeExcel + "\" ");
                }

                ResultSet rsCli = SuperFacadeJDBC.criarConsulta(sql, con);
                if (rsCli.next()) {
                    Integer pessoa = rsCli.getInt("pessoa");
                    String nome = rsCli.getString("nome");
                    Integer cliente = rsCli.getInt("cliente");
                    String matricula = rsCli.getString("matricula");
                    String situacao = rsCli.getString("situacao");
                    Integer empresa = rsCli.getInt("empresa");

                    Integer qtdAuto = rsCli.getInt("qtdAuto");
                    if (qtdAuto > 0) {
                        Uteis.logarDebug("INFO: Cliente já possui " + qtdAuto + " autorizações de cobrança | Mat.: " + matricula + " - " + nome);
                    }

                    ClienteVO clienteVO = new ClienteVO();
                    clienteVO.setCodigo(cliente);
                    clienteVO.setMatricula(matricula);
                    clienteVO.getPessoa().setCodigo(pessoa);
                    clienteVO.getPessoa().setNome(nome);
                    clienteVO.getEmpresa().setCodigo(empresa);

                    autorizacaoCobrancaClienteVO.setCliente(clienteVO);
                    incluirAutorizacao(autorizacaoCobrancaClienteVO, con);
                    totalSucesso++;
                }
            } catch (Exception ex) {
                Uteis.logarDebug("ERRO: " + ex.getMessage());
                totalFalha++;
            }
        }

        Uteis.logarDebug("Total Processado: " + totalGeral);
        Uteis.logarDebug("Total Falha: " + totalFalha);
        Uteis.logarDebug("Total Sucesso: " + totalSucesso);
        Uteis.logarDebug("Qtd clientes não encontrado: " + clientesNaoEncontrado);
        Uteis.logarDebug("Qtd clientes com mesmo nome: " + clientesMesmoNome);
    }

    private static String obterSQLCliente(String nomeCliente) {
//        StringBuilder sql = new StringBuilder();
//        sql.append("select * from  \n");
//        sql.append("(select \n");
//        sql.append("1 as ordem, \n");
//        sql.append("cl.codigo as cliente, \n");
//        sql.append("cl.matricula, \n");
//        sql.append("p.nome, \n");
//        sql.append("p.codigo as pessoa \n");
//        sql.append("from cliente cl \n");
//        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
//        sql.append("where p.nome ilike '").append(nomeCliente).append("' \n");
//        sql.append("union  \n");
//        sql.append("select \n");
//        sql.append("2 as ordem, \n");
//        sql.append("cl.codigo as cliente, \n");
//        sql.append("cl.matricula, \n");
//        sql.append("p.nome, \n");
//        sql.append("p.codigo as pessoa \n");
//        sql.append("from cliente cl \n");
//        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
//        sql.append("where p.nome ilike '").append(nomeCliente).append(" %' \n");
//        sql.append("union  \n");
//        sql.append("select \n");
//        sql.append("3 as ordem, \n");
//        sql.append("cl.codigo as cliente, \n");
//        sql.append("cl.matricula, \n");
//        sql.append("p.nome, \n");
//        sql.append("p.codigo as pessoa \n");
//        sql.append("from cliente cl \n");
//        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
//        sql.append("where p.nome ilike '").append(Uteis.getPrimeiroNome(nomeCliente)).append(" %' \n");
//        sql.append(") as sql \n");
//        sql.append("order by ordem \n");

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cl.codigo as cliente, \n");
        sql.append("cl.matricula, \n");
        sql.append("cl.empresa, \n");
        sql.append("p.nome, \n");
        sql.append("p.codigo as pessoa, \n");
        sql.append("(select count(*) from autorizacaocobrancacliente  where tipoautorizacao  = 1 and ativa and cliente = cl.codigo) as qtdAuto, \n");
        sql.append("cl.situacao \n");
        sql.append("from cliente cl \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
        sql.append("where p.nome ilike '").append(nomeCliente).append("' \n");
        return sql.toString();
    }

    private static void incluirAutorizacao(AutorizacaoCobrancaClienteVO obj, Connection con) throws Exception {
        AutorizacaoCobrancaCliente autoDAO = null;
        try {
            con.setAutoCommit(false);
            autoDAO = new AutorizacaoCobrancaCliente(con);

            if (UteisValidacao.emptyNumber(obj.getCliente().getCodigo())) {
                throw new Exception("Não foi encontrado cliente para o cartão: " + obj.getCartaoMascarado());
            }

            obj.setValidarAutorizacaoCobrancaSemelhante(false);
            obj.setDesabilitarValidacaoValidade(true);
            obj.setValidarQtdCartoes(false);
            autoDAO.incluir(obj);
            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                SuperFacadeJDBC.executarUpdate("update autorizacaocobrancacliente set obsimportacao = '" + obj.getObservacao() + "' where codigo = " + obj.getCodigo(), con);
            } else {
                throw new Exception("Token não gerado!");
            }
            con.commit();
        } catch (Exception ex) {
            con.rollback();
            throw ex;
        } finally {
            autoDAO = null;
            con.setAutoCommit(true);
        }
    }

    public static List<String[]> lerArquivo(String csvFile) {
        List<String[]> linhas = new ArrayList<>();
        BufferedReader br = null;
        String line = "";
        String cvsSplitBy = ";";
        try {
            br = new BufferedReader(new FileReader(csvFile));

            while ((line = br.readLine()) != null) {
                linhas.add(line.split(cvsSplitBy));
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return linhas;
    }

    private static String getValidadeMMYYYY(boolean comBarra, int mes, int ano) {
        String barra = comBarra ? "/" : "";
        String v = Formatador.formatarValorNumerico(Double.valueOf(mes), "00")
                + barra
                + Formatador.formatarValorNumerico(Double.valueOf(ano), "0000");

        return v;
    }
}

package br.com.pactosolucoes.integracao.aragorn;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 21/03/2024
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImportacaoCartaoDTO {

    private String card;
    private String titular;
    private String cpf;
    private String nomeCliente;
    private String titularDoc;
    private String brand;
    private String mesVenc;
    private String anoVenc;
    private String idCliente;
    private String idCard;
    private String nomeArquivo;
    private String tokenAdquirente;
    private String refId;

    public String getCard() {
        return card;
    }

    public void setCard(String card) {
        this.card = card;
    }

    public String getTitular() {
        return titular;
    }

    public void setTitular(String titular) {
        this.titular = titular;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getTitularDoc() {
        return titularDoc;
    }

    public void setTitularDoc(String titularDoc) {
        this.titularDoc = titularDoc;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getMesVenc() {
        return mesVenc;
    }

    public void setMesVenc(String mesVenc) {
        this.mesVenc = mesVenc;
    }

    public String getAnoVenc() {
        return anoVenc;
    }

    public void setAnoVenc(String anoVenc) {
        this.anoVenc = anoVenc;
    }

    public String getIdCliente() {
        return idCliente;
    }

    public void setIdCliente(String idCliente) {
        this.idCliente = idCliente;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getTokenAdquirente() {
        return tokenAdquirente;
    }

    public void setTokenAdquirente(String tokenAdquirente) {
        this.tokenAdquirente = tokenAdquirente;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }
}

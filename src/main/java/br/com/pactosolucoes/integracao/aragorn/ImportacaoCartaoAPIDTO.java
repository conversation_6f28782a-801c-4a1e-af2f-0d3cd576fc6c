package br.com.pactosolucoes.integracao.aragorn;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 21/03/2024
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImportacaoCartaoAPIDTO {

    private String key;
    private String tipo;
    private Integer empresa;
    private Integer convenioCobranca;
    private ImportacaoCartaoDTO card;

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(Integer convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public ImportacaoCartaoDTO getCard() {
        return card;
    }

    public void setCard(ImportacaoCartaoDTO card) {
        this.card = card;
    }

    public String toString() {
        return new JSONObject(this).toString();
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}

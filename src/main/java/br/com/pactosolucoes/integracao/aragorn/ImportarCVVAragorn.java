package br.com.pactosolucoes.integracao.aragorn;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 10/08/2020
 */
public class ImportarCVVAragorn {

    public static void main(String[] args) {
        try {
            Uteis.debug = true;

            String chave = "teste";
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            Connection con = new DAO().obterConexaoEspecifica(chave);
//            Connection con = DriverManager.getConnection("***************************************************", "postgres", "pactodb");
            processar(con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void processar(Connection con) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("au.codigo, \n");
        sql.append("au.cvv, \n");
        sql.append("au.tokenaragorn \n");
        sql.append("from autorizacaocobrancacliente au \n");
        sql.append("where au.tipoautorizacao = 1 \n");
        sql.append("and coalesce(au.cvv,'') = '' \n");
        sql.append("and coalesce(au.tokenaragorn,'') <> '' \n");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);

        Uteis.logarDebug("Total " + total + " - Autorizações Verificar");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

        int atual = 0;
        while (rs.next()) {
            try {

                Integer codigo = rs.getInt("codigo");
                String tokenAragorn = rs.getString("tokenaragorn");

                Uteis.logarDebug("Atual " + ++atual + "/" + total + " - " + tokenAragorn);

                AragornService aragornService = new AragornService();
                NazgDTO nazgDTO = aragornService.obterNazg(tokenAragorn);
                aragornService = null;

//                if (UteisValidacao.emptyString(nazgDTO.getCvv())) {
//                    Uteis.logarDebug("Não existe CVV aragorn | " + tokenAragorn);
//                    continue;
//                }

//                String update = "update AutorizacaoCobrancaCliente set cvv = ? where codigo = ?;";
//                int i = 0;
//                try (PreparedStatement ps = con.prepareStatement(update)) {
//                    ps.setString(++i, nazgDTO.getCvv());
//                    ps.setInt(++i, codigo);
//                    ps.execute();
//                }

            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }
}

package br.com.pactosolucoes.integracao.aragorn;

import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 21/03/2024
 */
public class ImportacaoCartaoRespostaDTO {

    private String token;
    private Integer autorizacao;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(Integer autorizacao) {
        this.autorizacao = autorizacao;
    }

    public String toString() {
        return new JSONObject(this).toString();
    }
}

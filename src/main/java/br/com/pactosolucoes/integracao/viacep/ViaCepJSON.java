package br.com.pactosolucoes.integracao.viacep;

import org.json.JSONObject;

public class ViaCepJSON {

    private String cep;
    private String logradouro;
    private String complemento;
    private String bairro;
    private String localidade;
    private String uf;
    private String ibge;

    public ViaCepJSON(JSONObject jsonObject) {
        this.bairro = jsonObject.getString("bairro");
        this.cep = jsonObject.getString("cep");
        this.complemento = jsonObject.getString("complemento");
        this.ibge = jsonObject.getString("ibge");
        this.uf = jsonObject.getString("uf");
        this.localidade = jsonObject.getString("localidade");
        this.logradouro = jsonObject.getString("logradouro");
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getLogradouro() {
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getLocalidade() {
        return localidade;
    }

    public void setLocalidade(String localidade) {
        this.localidade = localidade;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public String getIbge() {
        return ibge;
    }

    public void setIbge(String ibge) {
        this.ibge = ibge;
    }
}

package br.com.pactosolucoes.integracao.viacep;

import com.google.api.client.json.Json;
import negocio.comuns.utilitarias.CepVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class ViaCepServico {
    public ViaCepJSON consultarCepJson(String cep) throws Exception {
        if (cep == null || cep.isEmpty()) {
            throw new Exception("Por favor informe o CEP!");
        }
        Pattern p = Pattern.compile("\\d+");
        Matcher m = p.matcher(cep);
        StringBuilder cepBuilder = new StringBuilder();
        while (m.find()) {
            cepBuilder.append(m.group());
        }
        cep = cepBuilder.toString();
        if (cep.length() != 8 && !cep.matches("[0-9]+")) {
            throw new Exception("CEP com formato inválido!");
        }

        String url = "https://viacep.com.br/ws/" + cep + "/json";

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, null, null, MetodoHttpEnum.GET);
        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            throw new Exception(respostaHttpDTO.getResponse());
        }
        JSONObject jsonObject = new JSONObject(respostaHttpDTO.getResponse());
        if (jsonObject.has("erro") && jsonObject.getBoolean("erro")) {
            throw new Exception("CEP não encontrado");
        }
        return new ViaCepJSON(jsonObject);
    }

    public List<ViaCepJSON> consultarCepJson(CepVO cepVO) throws Exception {
        if (cepVO.getUfSigla().isEmpty()) {
            throw new Exception("Falha ao buscar pela API do ViaCEP: Estado não informado.");
        }

        if (cepVO.getUfSigla().length() != 2) {
            throw new Exception("Falha ao buscar pela API do ViaCEP: UF inválida.");
        }

        if (cepVO.getCidadeDescricao().isEmpty()) {
            throw new Exception("Falha ao buscar pela API do ViaCEP: Cidade não informada.");
        }

        if (cepVO.getEnderecoLogradouro().isEmpty()) {
            throw new Exception("Falha ao buscar pela API do ViaCEP: Logradouro não informado.");
        }

        String url = "https://viacep.com.br/ws/" + cepVO.getUfSigla().toUpperCase() + "/" + cepVO.getCidadeDescricao().trim() + "/" + cepVO.getEnderecoLogradouro().trim() + "/json";

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = null;
        try {
            respostaHttpDTO = service.executeRequest(url.replace(" ", "%20"), headers, null, null, MetodoHttpEnum.GET);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            throw new Exception(respostaHttpDTO.getResponse());
        }
        JSONArray jsonArray = new JSONArray(respostaHttpDTO.getResponse());
        List<ViaCepJSON> viaCepJSONList = new ArrayList<>();
        if (jsonArray.length() == 0) {
            return null;
        }
        for (int i = 0; i < jsonArray.length(); i++) {
            viaCepJSONList.add(new ViaCepJSON((JSONObject) jsonArray.get(i)));
        }
        return viaCepJSONList;
    }

    //Essa API não busca alguns CEPs completos por isso nesses casos
    public List<ViaCepJSON> consultarCepJsonExtendido(CepVO cepVO) throws Exception {
        String logradouroArray[] = cepVO.getEnderecoLogradouro().replace("  ", " ").trim().split(" ");
        CepVO cepVOAux = (CepVO) cepVO.getClone(true);
        List<ViaCepJSON> listaCepsTotal = null;

        int repetidor = 0;

        if (logradouroArray.length > 2) {
            repetidor = 2;
        } else if (logradouroArray.length > 1) {
            repetidor = 1;
        }

        for (;repetidor > 0; repetidor--) {
            String logradouro = Stream.of(logradouroArray).limit(repetidor).collect(Collectors.joining(" "));
            cepVOAux.setEnderecoLogradouro(logradouro);
            listaCepsTotal = consultarCepJson(cepVOAux);

            if (!UteisValidacao.emptyList(listaCepsTotal)) {
                break;
            }
        }

        if (UteisValidacao.emptyList(listaCepsTotal)) {
            return null;
        }

        List<ViaCepJSON> listaCeps = new ArrayList<>();

        for (ViaCepJSON viaCepJSON : listaCepsTotal) {
            if (Uteis.retirarAcentuacao(viaCepJSON.getLogradouro().toUpperCase()).contains(Uteis.retirarAcentuacao(cepVO.getEnderecoLogradouro().toUpperCase()))) {
                listaCeps.add(viaCepJSON);
            }
        }

        return listaCeps;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class ProcessamentoProtheus {
    
    private Connection conOamd;

    public ProcessamentoProtheus(Connection conOamd) {
        this.conOamd = conOamd;
    }
    
    public void incluirProcessamento(String chaveZW, String chaveGrav, Date hora, TipoEnvioEnum tipo, String cnpj)throws Exception{
        PreparedStatement stm = conOamd.prepareStatement("INSERT INTO processamentoprotheus (chaveprocessamento, hora, tipo, chavezw, cnpj) "
                + "VALUES (?,?,?,?,?) ");
        stm.setString(1, chaveGrav);
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(hora));
        stm.setInt(3, tipo.ordinal());
        stm.setString(4, chaveZW);
        stm.setString(5, cnpj);
        stm.execute();
    }
    
    public void removerProcessamento(String chaveGrav) throws Exception{
        PreparedStatement stm = conOamd.prepareStatement("DELETE FROM processamentoprotheus WHERE chaveprocessamento = '"+chaveGrav+"'");
        stm.execute();
    }
    
    public Map<TipoEnvioEnum, Map<String,List<String>>> consultarProcessamentos() throws Exception{
        ResultSet rs = conOamd.prepareStatement("SELECT * FROM processamentoprotheus").executeQuery();
        Map<TipoEnvioEnum, Map<String,List<String>>> mapa = new EnumMap<TipoEnvioEnum, Map<String,List<String>>>(TipoEnvioEnum.class);
        while(rs.next()){
            TipoEnvioEnum tipo = TipoEnvioEnum.getFromOrdinal(rs.getInt("tipo"));
            Map<String,List<String>> mapacnpj = mapa.get(tipo);
            if(mapacnpj == null){
                mapacnpj = new HashMap<String, List<String>>();
                mapa.put(tipo, mapacnpj);
            }
            List<String> lista = mapacnpj.get(rs.getString("cnpj"));
            if(lista == null){
                lista = new ArrayList<String>();
                mapacnpj.put(rs.getString("cnpj"), lista);
            }
            lista.add(rs.getString("chaveprocessamento"));
        }
        return mapa;
    }
    
//CREATE TABLE processamentoprotheus
//(
//  chaveprocessamento text NOT NULL,
//  hora timestamp without time zone,
//  tipo integer,
//  chavezw text,
//  cnpj text,
//  CONSTRAINT pk_processamentoprotheus PRIMARY KEY (chaveprocessamento)
//)
//WITH (
//  OIDS=FALSE
//);
//ALTER TABLE processamentoprotheus
//  OWNER TO postgres;

}

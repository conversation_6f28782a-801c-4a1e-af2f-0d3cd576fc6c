/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.nfe.NotaFiscalDeServicoVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;

/**
 *
 * <AUTHOR>
 */
public class NotaFiscalProtheus extends SuperEntidade {
//    ALTER TABLE nfseemitida ADD COLUMN chavgravprotheus TEXT;
    
        
    public NotaFiscalProtheus() throws Exception {
        super();
    }

    public NotaFiscalProtheus(Connection conexao) throws Exception {
        super(conexao);
    }
    
    public List<ItemEnvioNFSe> consultarRPS(Integer empresa) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT distinct  mp.codigo , nf.rps, cli.matricula, p.cfp,  \n");
        sql.append("        (SELECT descricao from plano \n");
	    sql.append("        	where codigo in (select plano from contrato where codigo = sw.codigocontrato)) as plano, \n");
        sql.append("        sw.codigocontrato as contrato, (SELECT ARRAY_TO_STRING(ARRAY(\n");
        sql.append("        SELECT distinct(tipoproduto) FROM produto p\n");
        sql.append("        INNER JOIN movproduto mpd ON mpd.produto = p.codigo\n");
        sql.append("        INNER JOIN movprodutoparcela mpp ON mpp.movproduto = mpd.codigo\n");
        sql.append("        WHERE mpp.movparcela = mp.codigo), ',')) as produtos\n");
        sql.append("        FROM movparcela mp\n");
        sql.append("        INNER JOIN pessoa p ON p.codigo = mp.pessoa\n");
        sql.append("        INNER JOIN cliente cli ON p.codigo = cli.pessoa\n");
        sql.append("        INNER JOIN situacaoclientesinteticodw sw ON cli.codigo = sw.codigocliente\n");
        sql.append("        INNER JOIN pagamentomovparcela pmpg ON mp.codigo = pmpg.movparcela\n");
        sql.append("        INNER JOIN movpagamento mpg ON mpg.codigo = pmpg.movpagamento\n");
        sql.append("        INNER JOIN autorizacaocobrancacliente aut ON aut.cliente = cli.codigo\n");
        sql.append("        INNER JOIN operadoracartao oc ON oc.codigo = aut.operadoracartao\n");
        sql.append("        LEFT JOIN cartaocredito cc ON cc.movpagamento = mpg.codigo\n");
        sql.append("        INNER JOIN nfseemitida nf ON nf.recibopagamento = mpg.recibopagamento OR nf.cartaocredito = cc.codigo \n");
        sql.append("        WHERE mp.situacao LIKE 'PG' \n");
        sql.append("        AND exists(select codigo from integracaoprotheus where chavepacto = mp.codigo and tipo = 'OP' and status in ('S'))  \n");
        sql.append("        AND not exists(select codigo from integracaoprotheus where chavepacto = nf.rps and tipo = 'NF' and status in ('S', 'A'))\n");
        sql.append("        ORDER BY mp.codigo ");
        
        ResultSet rs = criarConsulta(sql.toString(), con);
        List<ItemEnvioNFSe> lista = new ArrayList<ItemEnvioNFSe>();
        while (rs.next()) {
            ItemEnvioNFSe item = new ItemEnvioNFSe();
            ClienteVO cliente = new ClienteVO();
            cliente.getPessoa().setCfp(rs.getString("cfp"));
            cliente.setMatricula(rs.getString("matricula"));
            item.setClienteVO(cliente);
            item.setRps(rs.getInt("rps"));
            item.setContrato(rs.getInt("contrato"));
            String plano = rs.getString("plano");
            String tiposProdutos = rs.getString("produtos");
            String chaveProd;
            if(tiposProdutos.contains("PM")){
             chaveProd = plano.contains("BLUE") ? "BLU" : "SEL";
            }else if(tiposProdutos.contains("TD") || tiposProdutos.contains("MA")){
                chaveProd = "ADE";
            }else if(tiposProdutos.contains("TA")){
                chaveProd = "ANU";
            }else{
               chaveProd = "AVA";
            }
            item.setProduto(getMapaProduto().get(chaveProd));
            lista.add(item);
        }
        return lista;
    }
    
    public Map<String, String> getMapaProduto(){
        Map<String, String> mapa = new HashMap<String, String>();
        mapa.put("ADE", "08010000      ");
        mapa.put("ANU", "08010001      ");
        mapa.put("PER", "08010002      ");
        mapa.put("BLU", "08010003      ");
        mapa.put("SEL", "08010004      ");
        return mapa;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.MemCachedManager;

/**
 *
 * <AUTHOR>
 */
public class ChaveDiariaProtheus implements Serializable{
    
    public Connection conOAMD;

    public ChaveDiariaProtheus(Connection conOAMD) {
        this.conOAMD = conOAMD;
    }
    
    public String obterChaveDiariaAutenticacao(String chave, String cnpj) throws Exception{
        String chaveAut = null;
        if(chaveAut == null){
            PropriedadesIntegracaoProtheus.print("Vou consultar a chave no BD");
            try {
                chaveAut = consultarChaveAutenticacaoBD(cnpj);
            } catch (Exception e) {
                criarTabelaChaveDiaria();
            }
        }
        if (chaveAut == null) {
            PropriedadesIntegracaoProtheus.print("Vou consultar a chave no SERVIÇO ");
            chaveAut = ProtheusConsumer.getChaveAutenticacao(chave, cnpj);
            PropriedadesIntegracaoProtheus.print("Vou gravar a chave no banco ");
            inserirChaveAutenticacao(cnpj, chaveAut);
        }
        PropriedadesIntegracaoProtheus.print("chave: "+chaveAut);
        return chaveAut;
        
    }

    public void criarTabelaChaveDiaria() throws SQLException{
        StringBuilder create = new StringBuilder();
        create.append("CREATE TABLE chaveautenticacaoprotheus (\n");
        create.append("dia date,\n");
        create.append("chave character varying(200),\n");
        create.append("cnpj character varying(20),\n");
        create.append("CONSTRAINT pk_chaveautenticacaoprotheus primary key(dia,cnpj)\n");
        create.append(");");
        PreparedStatement pst = conOAMD.prepareStatement(create.toString());
        pst.execute();
    }
    
    public void inserirChaveAutenticacao(String cnpj, String chave)throws Exception{
        if(chave == null || chave.isEmpty()){
            return;
        }
        PreparedStatement pst = conOAMD.prepareStatement("INSERT INTO chaveautenticacaoprotheus (cnpj, chave, dia) VALUES (?,?,?)");
        pst.setString(1, cnpj);
        pst.setString(2, chave);
        pst.setDate(3, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(Calendario.hoje())));
        pst.execute();
    }

    public String consultarChaveAutenticacaoBD(String cnpj) throws Exception{
        PreparedStatement pst = conOAMD.prepareStatement("SELECT chave FROM chaveautenticacaoprotheus WHERE dia = ? AND cnpj = ?");
        pst.setDate(1, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(Calendario.hoje())));
        pst.setString(2, cnpj);
        ResultSet rs = pst.executeQuery();
        return rs.next() ? rs.getString("chave") : null;
    }

}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import org.json.JSONObject;

import java.util.Map;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.nfe.NotaFiscalDeServicoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.integracao.IntegracaoNFSeWSConsumer;
import servicos.integracao.selfit.cliente.ARRAYOFWSRETCLI;
import servicos.integracao.selfit.cliente.WSDADCLI;
import servicos.integracao.selfit.cliente.WSRETCLI;
import servicos.integracao.selfit.notafiscal.ARRAYOFWSRETCNFIS;
import servicos.integracao.selfit.notafiscal.WSCHVNFISC;
import servicos.integracao.selfit.notafiscal.WSRETCNFIS;
import servicos.integracao.selfit.notafiscal.WSRETNFISC;
import servicos.integracao.selfit.titulooperadora.ARRAYOFWSCHVTOPE;
import servicos.integracao.selfit.titulooperadora.ARRAYOFWSRETCTOPE;
import servicos.integracao.selfit.titulooperadora.WSCHVTOPE;
import servicos.integracao.selfit.titulooperadora.WSDADTOPE;
import servicos.integracao.selfit.titulooperadora.WSRETCTOPE;
import servicos.integracao.selfit.titulooperadora.WSRETTOPER;
import servicos.integracao.selfit.tituloprovisorio.ARRAYOFWSCHVPROV;
import servicos.integracao.selfit.tituloprovisorio.ARRAYOFWSRETTPRO;
import servicos.integracao.selfit.tituloprovisorio.WSCHVPROV;
import servicos.integracao.selfit.tituloprovisorio.WSDADPROV;
import servicos.integracao.selfit.tituloprovisorio.WSRETPROV;
import servicos.integracao.selfit.tituloprovisorio.WSRETTPRO;

/**
 *
 * <AUTHOR>
 */
public class IntegracaoProtheus {

    public Connection con;
    public static final String chaveMestra = "521254125232521";
    public static String chave;
    public Connection conOAMD;
    public ClienteProtheus clienteProtheus;
    public TituloProvisorioProtheus titProvProtheus;
    public TituloOperadoraProtheus titOperadoraProtheus;
    public ChaveDiariaProtheus chaveProtheus;
    public NotaFiscalProtheus notaProtheus;
    public ProcessamentoProtheus procProtheus;

    public IntegracaoProtheus(Connection con, Connection conOAMD) throws Exception {
        this.con = con;
        this.conOAMD = conOAMD;
        clienteProtheus = new ClienteProtheus(con);
        chaveProtheus = new ChaveDiariaProtheus(conOAMD);
        procProtheus = new ProcessamentoProtheus(conOAMD);
        titProvProtheus = new TituloProvisorioProtheus(con);
        titOperadoraProtheus = new TituloOperadoraProtheus(con);
        notaProtheus = new NotaFiscalProtheus(con);
    }

    public String obterChaveAutenticacao(String cnpj) {
        try {
            if (cnpj == null) {
                ResultSet rs = con.prepareStatement("SELECT codigo, cnpj FROM empresa").executeQuery();
                while (rs.next()) {
                    cnpj = rs.getString("cnpj");
                }
            }
            PropriedadesIntegracaoProtheus.print(Uteis.removerMascara(cnpj));
            return chaveProtheus.obterChaveDiariaAutenticacao(chave, cnpj);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoProtheus.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public void enviarClientes(Integer empresa, String chaveAutenticacao, String cnpj, TipoOperacaoEnum rotina) throws Exception {
        Map<String, WSDADCLI> clientesSincronizar = clienteProtheus.obterClienteSincronizar(empresa, rotina, getListaCpf(),
                PropriedadesIntegracaoProtheus.getPropertyValueInt("limitCL"));
        System.out.println("Total de "+clientesSincronizar.keySet().size()+" alunos");
        Map<String, ARRAYOFWSRETCLI> clientesSalvos = ProtheusConsumer.salvarClientes(con, chave, chaveAutenticacao, clientesSincronizar, cnpj);
        for (String cpf : clientesSalvos.keySet()) {
            for (WSRETCLI wsretcli : clientesSalvos.get(cpf).getWSRETCLI()) {
                if (!UteisValidacao.emptyString(wsretcli.getCHAVGRV())) {
                    PreparedStatement stm = con.prepareStatement("SELECT codigo from cliente WHERE pessoa in (SELECT codigo FROM pessoa WHERE cfp = ?)");
                    stm.setString(1, cpf);
                    ResultSet rs = stm.executeQuery();
                    if (rs.next()) {
                        insertTabelaIntegracao(wsretcli.getCHAVGRV(), rs.getInt("codigo"), TipoEnvioEnum.CLIENTE, rotina);
                    }
                }
            }
        }
    }

    public void insertTabelaIntegracao(String chavedts, Integer chavePacto, TipoEnvioEnum tipo, TipoOperacaoEnum operacao) throws Exception {
        PreparedStatement stm = con.prepareStatement("INSERT INTO integracaoprotheus(chavedts, chavepacto, tipo, operacao, status) VALUES (?,?,?,?,?)");
        stm.setString(1, chavedts);
        stm.setInt(2, chavePacto);
        stm.setString(3, tipo.getSigla());
        stm.setString(4, operacao.getSigla());
        stm.setString(5, "A");
        stm.execute();
    }

    public void alterarStatusTabelaIntegracao(String chavedts, String status) throws Exception {
        PreparedStatement stm = con.prepareStatement("UPDATE integracaoprotheus set status = ? WHERE chavedts = ?");
        stm.setString(1, status);
        stm.setString(2, chavedts);
        stm.execute();
    }

    public void enviarNotaFiscal(Integer empresa, String chaveAutenticacao, String cnpj, TipoOperacaoEnum rotina) throws Exception {
        List<ItemEnvioNFSe> listaRPS = notaProtheus.consultarRPS(empresa);
        for (ItemEnvioNFSe rps : listaRPS) {
            try {
                JSONObject nota = IntegracaoNFSeWSConsumer.consultarNota(chave, rps.getRps());
                NotaFiscalDeServicoVO notaVO = new NotaFiscalDeServicoVO();
                notaVO.fromJson(nota);
                if (UteisValidacao.emptyString(notaVO.getNumeroNota())) {
                    PropriedadesIntegracaoProtheus.print("Numero da nota zerado.");
                    continue;
                }
                WSRETNFISC envioNotaFiscal = ProtheusConsumer.enviarNotaFiscal(chave, chaveAutenticacao, cnpj, notaVO, rps.getClienteVO(), rotina,
                        rps.getContrato(), rps.getProduto());
                for (WSCHVNFISC wsch : envioNotaFiscal.getCHAVGRV().getWSCHVNFISC()) {
                    insertTabelaIntegracao(wsch.getCCHAVPROV(), rps.getRps(), TipoEnvioEnum.NOTA_FISCAL, rotina);
                    PropriedadesIntegracaoProtheus.print(wsch.getCCHAVPROV());
                }
            } catch (Exception e) {
                System.err.println(e.getMessage());
            }

        }
    }

    public void enviarTitulosProvisorios(Integer empresa, String chaveAutenticacao, String cnpj, TipoOperacaoEnum rotina) throws Exception {
        Map<String, Map<Integer, WSDADPROV>> parcelasSincronizar = titProvProtheus.obterParcelasSincronizar(empresa, rotina, getListaCpf(),
                PropriedadesIntegracaoProtheus.getPropertyValueInt("limitPR"));
        for (String cpf : parcelasSincronizar.keySet()) {
            Map<Integer, WSDADPROV> mapaParcelas = parcelasSincronizar.get(cpf);
            for (Integer codigoParcela : mapaParcelas.keySet()) {
                List<WSRETPROV> titulosProvisorios = ProtheusConsumer.salvarTitulosProvisorios(chave,
                        chaveAutenticacao, cpf, mapaParcelas.get(codigoParcela), cnpj);
                for (WSRETPROV wsRet : titulosProvisorios) {
                    ARRAYOFWSCHVPROV chavgrv = wsRet.getCHAVGRV();
                    for (WSCHVPROV wsProv : chavgrv.getWSCHVPROV()) {
                        insertTabelaIntegracao(wsProv.getCCHAVPROV(), codigoParcela, TipoEnvioEnum.TITULO_PROVISORIO, rotina);
                        PropriedadesIntegracaoProtheus.print(wsProv.getCCHAVPROV());
                    }
                }
            }
        }

    }

    public void enviarTitulosOperadora(Integer empresa, String chaveAutenticacao, String cnpj, TipoOperacaoEnum t) throws Exception {
        Map<String, Map<Integer, WSDADTOPE>> parcelasSincronizar = titOperadoraProtheus.obterParcelasSincronizar(empresa, t, cnpj, getListaCpf(),
                null);
        for (String cpf : parcelasSincronizar.keySet()) {
            Map<Integer, WSDADTOPE> mapaParcelas = parcelasSincronizar.get(cpf);
            for (Integer codigoParcela : mapaParcelas.keySet()) {
                List<WSRETTOPER> salvarTitulosOperadora = ProtheusConsumer.salvarTitulosOperadora(chave, chaveAutenticacao, cpf, mapaParcelas.get(codigoParcela), cnpj);
                for (WSRETTOPER oper : salvarTitulosOperadora) {
                    ARRAYOFWSCHVTOPE chavgrv = oper.getCHAVGRV();
                    for (WSCHVTOPE wsProv : chavgrv.getWSCHVTOPE()) {
                        insertTabelaIntegracao(wsProv.getCCHAVPROV(), codigoParcela, TipoEnvioEnum.TITULO_OPERADORA, t);
                        PropriedadesIntegracaoProtheus.print(wsProv.getCCHAVPROV());
                    }
                }
            }
        }
    }

    public void criarTabelaIntegracao() {
        try {
            con.prepareStatement(" create table integracaoprotheus( "
                    + " codigo serial primary key,"
                    + " chavedts text,"
                    + "chavepacto int, "
                    + "operacao character varying(1), "
                    + "status character varying(1), "
                    + "tipo character varying(2))").execute();
        } catch (Exception e) {
        }

    }

    public void reiniciarTabelaIntegracao(){
        try {
            con.prepareStatement(" drop table integracaoprotheus;").execute();
        } catch (Exception e) {
        }
        criarTabelaIntegracao();
    }

    public void verificarEmpresaProntaProtheus(ResultSet rs) throws Exception {
        try {
            if (rs.getBoolean("prontoprotheus")) {
                return;
            }
        } catch (Exception e) {
            //NADA
        }
        criarTabelaIntegracao();
        SuperFacadeJDBC.executarConsultaUpdate("ALTER TABLE cliente ADD COLUMN codprotheus character varying (30);", con);
        SuperFacadeJDBC.executarConsultaUpdate("ALTER TABLE cliente ADD COLUMN chavgravdts character varying (30);", con);
        SuperFacadeJDBC.executarConsultaUpdate("ALTER TABLE movparcela ADD COLUMN codprotheus character varying (30);", con);
        SuperFacadeJDBC.executarConsultaUpdate("ALTER TABLE movparcela ADD COLUMN chavgravdts character varying (30);", con);
        SuperFacadeJDBC.executarConsultaUpdate("ALTER TABLE movparcela ADD COLUMN chavgravdtsoperadora character varying (30);", con);
        SuperFacadeJDBC.executarConsultaUpdate("ALTER TABLE cidade ADD COLUMN codigomunicipio character varying (20);", con);
        SuperFacadeJDBC.executarConsultaUpdate("ALTER TABLE cliente ADD COLUMN codigolojaprotheus character varying (20);", con);
        SuperFacadeJDBC.executarConsultaUpdate("ALTER TABLE cliente ADD COLUMN codigoprotheus character varying (20);", con);
        SuperFacadeJDBC.executarConsultaUpdate("ALTER TABLE empresa ADD COLUMN prontoprotheus boolean default true;", con);
    }

    public void inicializarIntegracao(TipoOperacaoEnum rotina, TipoEnvioEnum... tipos) {
        try {

            ResultSet rs = con.prepareStatement("SELECT * FROM empresa").executeQuery();
            while (rs.next()) {
                Integer empresa = rs.getInt("codigo");
                String cnpj = Uteis.removerMascara(rs.getString("cnpj"));
                verificarEmpresaProntaProtheus(rs);
                String chaveAutenticacao = obterChaveAutenticacao(cnpj);
                for (TipoEnvioEnum tipo : tipos) {
                    switch (tipo) {
                        case PROCESSAMENTO:
                            consultarProcessamentosClientes(chaveAutenticacao, cnpj);
                            consultarProcessamentos(chaveAutenticacao, cnpj);
                            break;
                        case CLIENTE:
                            enviarClientes(empresa, chaveAutenticacao, cnpj, rotina);
                            break;
                        case TITULO_PROVISORIO:
                            enviarTitulosProvisorios(empresa, chaveAutenticacao, cnpj, rotina);
                            break;
                        case TITULO_OPERADORA:
                            enviarTitulosOperadora(empresa, chaveAutenticacao, cnpj, rotina);
                            break;
                        case NOTA_FISCAL:
                            enviarNotaFiscal(empresa, chaveAutenticacao, cnpj, rotina);
                            break;
                        

                    }
                }

            }
        } catch (Exception e) {
            PropriedadesIntegracaoProtheus.print(e.getMessage());
        }

    }

    public void consultarProcessamentos(String chaveAutenticacao, String cnpj) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select chavedts, tipo from integracaoprotheus \n"
                + " where tipo <> '" + TipoEnvioEnum.CLIENTE.getSigla() + "' and status = 'A' and chavedts is not null and chavedts not like '' ORDER BY codigo DESC ", con);
        while (rs.next()) {
            try {
                TipoEnvioEnum fromSigla = TipoEnvioEnum.getFromSigla(rs.getString("tipo"));
                switch (fromSigla) {
                    case NOTA_FISCAL:
                        ARRAYOFWSRETCNFIS resultNF = ProtheusConsumer.consultarProcessamentoNotaFiscal(chaveAutenticacao, cnpj, rs.getString("chavedts"));
                        for (WSRETCNFIS retonf : resultNF.getWSRETCNFIS()) {
                            tratarRetorno(retonf.getCSTATUS(), retonf.getMSGERRO(), rs.getString("chavedts"));
                        }
                        break;
                    case TITULO_OPERADORA:
                        ARRAYOFWSRETCTOPE resultOp = ProtheusConsumer.consultarProcessamentoTituloOperadora(chaveAutenticacao, cnpj, rs.getString("chavedts"));
                        List<WSRETCTOPE> wsrettoP = resultOp.getWSRETCTOPE();
                        for (WSRETCTOPE retoP : wsrettoP) {
                            tratarRetorno(retoP.getCSTATUS(), retoP.getMSGERRO(), rs.getString("chavedts"));
                        }
                        break;
                    case TITULO_PROVISORIO:
                        ARRAYOFWSRETTPRO result = ProtheusConsumer.consultarProcessamentoTituloProvisorio(chaveAutenticacao, cnpj, rs.getString("chavedts"));
                        List<WSRETTPRO> wsrettpro = result.getWSRETTPRO();
                        for (WSRETTPRO ret : wsrettpro) {
                            tratarRetorno(ret.getCSTATUS(), ret.getMSGERRO(), rs.getString("chavedts"));
                        }
                        break;
                }
            }catch (Exception e){
                System.out.println(e.getMessage());
            }

        }
    }

    private void tratarRetorno(String cstatus, String msgerro, String chavedts) throws Exception {
        PropriedadesIntegracaoProtheus.print(msgerro);
        PropriedadesIntegracaoProtheus.print(chavedts + " - "+cstatus);
        if (cstatus.equals("1")) {
            alterarStatusTabelaIntegracao(chavedts, "S");
            FeedBackEnvioProtheus.enviarFeedBackRetornoProcessamento(chavedts, StatusEnvioEnum.SUCESSO, "");
        } else if (!UteisValidacao.emptyString(msgerro)) {
            alterarStatusTabelaIntegracao(chavedts, "F");
            FeedBackEnvioProtheus.enviarFeedBackRetornoProcessamento(chavedts, StatusEnvioEnum.FALHA, msgerro);
        }
    }

    public void consultarProcessamentosClientes(String chaveAutenticacao, String cnpj) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select chavedts, cfp from cliente \n"
                + " inner join integracaoprotheus ip on ip.chavepacto = cliente.codigo and ip.tipo = 'CL'  \n"
                + " inner join pessoa pes ON pes.codigo = cliente.pessoa  \n"
                + " where codigoprotheus is null and codigolojaprotheus is null ", con);
        while (rs.next()) {
            ARRAYOFWSRETCLI retorno = ProtheusConsumer.consultarProcessamentoCliente(chaveAutenticacao, Uteis.removerMascara(cnpj), rs.getString("chavedts"));
            for (WSRETCLI cliRet : retorno.getWSRETCLI()) {
                PropriedadesIntegracaoProtheus.print(cliRet.getMSGERRO());
                try {
                    if (cliRet.getCSTATUS().equals("1")) {
                        PreparedStatement stm = con.prepareStatement("UPDATE cliente SET codigolojaprotheus = ?, "
                                + " codigoprotheus = ? WHERE codigo in (select chavepacto from integracaoprotheus "
                                + " WHERE chavedts = ? AND tipo = 'CL')");
                        stm.setString(1, cliRet.getA1LOJA());
                        stm.setString(2, cliRet.getA1COD());
                        PropriedadesIntegracaoProtheus.print("loja: " + cliRet.getA1LOJA() + "\ncodigo: " + cliRet.getA1COD()
                                + "\nchave:" + rs.getString("chavedts") + "\n----------------------");
                        stm.setString(3, rs.getString("chavedts"));
                        stm.execute();
                        alterarStatusTabelaIntegracao(rs.getString("chavedts"), "S");
                        FeedBackEnvioProtheus.enviarFeedBackRetornoProcessamento(rs.getString("chavedts"), StatusEnvioEnum.SUCESSO, "");
                    } else if (!UteisValidacao.emptyString(cliRet.getMSGERRO())) {
                        alterarStatusTabelaIntegracao(rs.getString("chavedts"), "F");
                        if(cliRet.getMSGERRO().toLowerCase().contains("cliente ja cadastrado")){
                            ARRAYOFWSRETCLI retornoConsultaCadastro = ProtheusConsumer.consultarCadastroCliente(chaveAutenticacao, Uteis.removerMascara(cnpj), rs.getString("cfp"));
                            for (WSRETCLI cliCad : retornoConsultaCadastro.getWSRETCLI()) {
                                PropriedadesIntegracaoProtheus.print(cliRet.getMSGERRO());
                                try {
                                    if (cliCad.getCSTATUS().equals("1")) {
                                        PreparedStatement stm = con.prepareStatement("UPDATE cliente SET codigolojaprotheus = ?, " + " codigoprotheus = ? WHERE codigo in (select chavepacto from integracaoprotheus " + " WHERE chavedts = ? AND tipo = 'CL')");
                                        stm.setString(1, cliCad.getA1LOJA());
                                        stm.setString(2, cliCad.getA1COD());
                                        PropriedadesIntegracaoProtheus.print("loja: " + cliCad.getA1LOJA() + "\ncodigo: " + cliCad.getA1COD() + "\nchave:" + rs.getString("chavedts") + "\n----------------------");
                                        stm.setString(3, rs.getString("chavedts"));
                                        stm.execute();
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }else{
                            FeedBackEnvioProtheus.enviarFeedBackRetornoProcessamento(rs.getString("chavedts"), StatusEnvioEnum.FALHA, cliRet.getMSGERRO());
                        }

                    }

                } catch (Exception e) {
                }
            }
        }
    }

    public static void main(String... args) {
        try {

            Connection conOamd = DriverManager.getConnection("jdbc:postgresql://" + PropriedadesIntegracaoProtheus.getPropertyValue("hostOamd")
                            + ":" + PropriedadesIntegracaoProtheus.getPropertyValue("portaOamd")
                            + "/OAMD",
                    PropriedadesIntegracaoProtheus.getPropertyValue("userOamd"), PropriedadesIntegracaoProtheus.getPropertyValue("senhaOamd"));
            String whereChaves = "";
            if(args != null && args.length > 0){
                whereChaves = " WHERE chave in ('" + args[0] + "')";
            }else{
                String[] chaves = PropriedadesIntegracaoProtheus.getPropertyValue("chaves").split("\\,");
                if (chaves != null && chaves.length > 0) {
                    for (String ch : chaves) {
                        if(ch == null || ch.trim().isEmpty()){
                            continue;
                        }
                        whereChaves = (whereChaves.isEmpty() ? "'" : ",'") + ch + "'";
                    }
                    if(!whereChaves.isEmpty()){
                        whereChaves = " WHERE chave in (" + whereChaves + ")";
                    }

                }
            }
            ResultSet rsDadosCon = SuperFacadeJDBC.criarConsulta("SELECT * FROM empresa "+whereChaves+" ORDER by ordemprotheus ", conOamd);
            while (rsDadosCon.next()) {
                Date iniciou = Calendario.hoje();
                chave = rsDadosCon.getString("nomeBD").replaceFirst("bdzillyon", "").toUpperCase();
                PropriedadesIntegracaoProtheus.print("Varrendo banco " + rsDadosCon.getString("nomeBD").toUpperCase()
                        + " para integração com o Protheus - " + Uteis.getDataComHora(Calendario.hoje()));

                Connection con = DriverManager.getConnection("jdbc:postgresql://"
                        + rsDadosCon.getString("hostBD")
                        + ":" + rsDadosCon.getString("porta")
                        + "/" + rsDadosCon.getString("nomeBD"), rsDadosCon.getString("userBD"), rsDadosCon.getString("passwordBD"));

                IntegracaoProtheus integracao = new IntegracaoProtheus(con, conOamd);

                if(args != null && args.length > 1){
                    integracao.inicializarIntegracao(TipoOperacaoEnum.INCLUSAO,TipoEnvioEnum.getFromSigla(args[1]));
                }else{
                    integracao.inicializarIntegracao(TipoOperacaoEnum.INCLUSAO,TipoEnvioEnum.PROCESSAMENTO);

                }
                System.out.println("iniciou:" + Uteis.getDataComHHMM(iniciou));
                System.out.println("terminou:" + Uteis.getDataComHHMM(Calendario.hoje()));
            }

        } catch (Exception ex) {
            Logger.getLogger(IntegracaoProtheus.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public List<String> getListaCpf() {
        List<String> lista = new ArrayList<String>();
        String cpfs = PropriedadesIntegracaoProtheus.getPropertyValue("cpfs");
        if (cpfs == null || cpfs.isEmpty()) {
            return lista;
        }
        String[] split = cpfs.split("\\,");
        lista.addAll(Arrays.asList(split));
        return lista;
    }
}

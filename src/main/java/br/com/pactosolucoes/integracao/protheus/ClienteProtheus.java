/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;
import servicos.integracao.selfit.cliente.WSDADCLI;

/**
 *
 * <AUTHOR>
 */
public class ClienteProtheus extends SuperEntidade {
    
//    ALTER TABLE cliente ADD COLUMN codprotheus character varying (30);
//ALTER TABLE cliente ADD COLUMN chavgravdts character varying (30);

    public ClienteProtheus() throws Exception {
        super();
    }

    public ClienteProtheus(Connection conexao) throws Exception {
        super(conexao);
    }

    public Map<String,WSDADCLI> obterClienteSincronizar(Integer empresa, TipoOperacaoEnum rotina, 
            List<String> cpfs, Integer limit) throws Exception {
        Map<String, WSDADCLI> mapa = new HashMap<String, WSDADCLI>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT cid.codigomunicipio, p.codigo as codigopessoa, p.cfp, p.nome, est.sigla, cid.nome, p.rg, pais.nome, p.datanasc, p.datacadastro, cid.nome as cidade, pais.nome as pais FROM cliente cli \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = cli.pessoa \n");
        sql.append(" LEFT JOIN estado est ON est.codigo = p.estado \n");
        sql.append(" LEFT JOIN cidade cid ON cid.codigo = p.cidade \n");
        sql.append(" LEFT JOIN pais ON pais.codigo = p.pais \n");
        sql.append(" WHERE cli.empresa = ").append(empresa);
        sql.append(" AND not exists(select codigo from integracaoprotheus where chavepacto = cli.codigo and tipo = 'CL' and status IN ('A','S') AND operacao = 'I')\n");
        sql.append(" AND situacao = 'AT'\n");
        if(!UteisValidacao.emptyList(cpfs)){
            Boolean first = true;
            sql.append(" AND P.CFP IN (");
            for(String cpf : cpfs){
                sql.append(first ? "'":",'").append(cpf).append("'");
                first = false;
            }
            sql.append(")");
        }
        
        sql.append(" ORDER BY p.nome ");
        if(!UteisValidacao.emptyNumber(limit)){
            sql.append(" LIMIT ").append(limit);
        }
        PropriedadesIntegracaoProtheus.print(sql.toString());
        
        ResultSet rs = criarConsulta(sql.toString(), con);
        int i = 0;
        while (rs.next()) {
            String cpf = rs.getString("cfp");
            mapa.put(cpf, montarMapaAtributosCliente(rs, rotina));
            PropriedadesIntegracaoProtheus.print("Montando dados - "+(++i));
        }
        return mapa;
    }

    public static void imprimirValores(WSDADCLI cliente){
        List<String> listAttributes = UtilReflection.getListAttributes(WSDADCLI.class);
        for(String s : listAttributes){
            PropriedadesIntegracaoProtheus.print(s + ": "+UtilReflection.getValor(cliente, s));
        }
    }
    
    public WSDADCLI montarMapaAtributosCliente(ResultSet rs, TipoOperacaoEnum rotina) throws Exception {
        WSDADCLI cliente = new WSDADCLI();
        String codigoMunicipio = rs.getString("codigomunicipio");
        if(rs.getString("codigomunicipio") != null && rs.getString("codigomunicipio").length() > 5){
            codigoMunicipio = rs.getString("codigomunicipio").substring(2);
        }
        cliente.setA1NOME(StringUtilities.limitarTamanho(rs.getString("nome"), CampoClienteProtheusEnum.A1_NOME.getTamanho()));
        cliente.setA1PESSOA("F");
        ResultSet rsEndereco = criarConsulta("SELECT * FROM endereco WHERE pessoa = " + rs.getInt("codigopessoa")
                + " LIMIT 1", con);
        cliente.setA1EST(StringUtilities.limitarTamanho(rs.getString("sigla"), CampoClienteProtheusEnum.A1_EST.getTamanho()));
        cliente.setA1REGIAO("001");
        cliente.setA1DSCREG("NORTE");
        
        if (rsEndereco.next()) {
            cliente.setA1END(StringUtilities.limitarTamanho(rsEndereco.getString("endereco"), CampoClienteProtheusEnum.A1_END.getTamanho()));
            cliente.setA1BAIRRO(StringUtilities.limitarTamanho(rsEndereco.getString("bairro"), CampoClienteProtheusEnum.A1_BAIRRO.getTamanho()));
            cliente.setA1CEP(rsEndereco.getString("cep") == null ? "" : StringUtilities.limitarTamanho(Uteis.removerMascara(rsEndereco.getString("cep")), CampoClienteProtheusEnum.A1_CEP.getTamanho()));
            cliente.setA1COMPLEM(rsEndereco.getString("complemento") == null ? "" : StringUtilities.limitarTamanho(Uteis.removerMascara(rsEndereco.getString("complemento")), CampoClienteProtheusEnum.A1_COMPLEM.getTamanho()));
        }
        ResultSet rsEmail = criarConsulta("SELECT * FROM email WHERE pessoa = " + rs.getInt("codigopessoa") + " LIMIT 1", con);
        if(rsEmail.next()){
            cliente.setA1EMAIL(StringUtilities.limitarTamanho(rsEmail.getString("email"), CampoClienteProtheusEnum.A1_EMAIL.getTamanho()));
        }
        ResultSet rsTelefone = criarConsulta("SELECT * FROM telefone WHERE pessoa = " + rs.getInt("codigopessoa") + " LIMIT 1", con);
        if(rsTelefone.next()){
            cliente.setA1TEL(StringUtilities.limitarTamanho(rsTelefone.getString("numero"), CampoClienteProtheusEnum.A1_TEL.getTamanho()));
            cliente.setA1TEL(cliente.getA1TEL().indexOf(")") >= 0 ? 
                    cliente.getA1TEL().substring(cliente.getA1TEL().indexOf(")")+2) : 
                    cliente.getA1TEL());
            cliente.setA1DDD(extrairDDD(rsTelefone.getString("numero")));
        }
        cliente.setA1TIPO("F");
        cliente.setA1TIPOROT(rotina.getSigla());
        
        cliente.setA1CODMUN(codigoMunicipio);
        cliente.setA1PESSOA("F");
        cliente.setA1PFISICA(StringUtilities.limitarTamanho(rs.getString("rg"), CampoClienteProtheusEnum.A1_PFISICA.getTamanho()));
        cliente.setA1NREDUZ(StringUtilities.limitarTamanho(rs.getString("nome"), CampoClienteProtheusEnum.A1_NREDUZ.getTamanho()));
        cliente.setA1TIPO("F");
        cliente.setA1INSCR("");
        cliente.setA1INSCRM("");
        cliente.setA1MUN(StringUtilities.limitarTamanho(rs.getString("cidade"), CampoClienteProtheusEnum.A1_MUN.getTamanho()));
        cliente.setA1PAIS("105");
        cliente.setA1DTNASC(StringUtilities.limitarTamanho(Uteis.getDataAplicandoFormatacao(rs.getDate("datanasc"), "yyyyMMdd"), CampoClienteProtheusEnum.A1_DTNASC.getTamanho()));
//        imprimirValores(cliente);
        return cliente;
    }

    public String getDescRegiao(String regiao) {
        Map<String, String> mapaRegiao = new HashMap<String, String>();
        mapaRegiao.put("001", "NORTE");
        mapaRegiao.put("002", "SUL");
        mapaRegiao.put("003", "LESTE");
        mapaRegiao.put("004", "OESTE");
        mapaRegiao.put("005", "CENTRO");
        mapaRegiao.put("006", "CENTRO OESTE");
        return mapaRegiao.get(regiao);
    }
    public String getCodigoRegiao(String estado) {

        Map<String, String> mapaRegiao = new HashMap<String, String>();
        mapaRegiao.put("AL", "001");
        mapaRegiao.put("AM", "001");
        mapaRegiao.put("AP", "001");
        mapaRegiao.put("BA", "001");
        mapaRegiao.put("CE", "001");
        mapaRegiao.put("DF", "001");
        mapaRegiao.put("ES", "001");
        mapaRegiao.put("EX", "001");
        mapaRegiao.put("GO", "001");
        mapaRegiao.put("MA", "001");
        mapaRegiao.put("MG", "001");
        mapaRegiao.put("MS", "001");
        mapaRegiao.put("MT", "001");
        mapaRegiao.put("PA", "001");
        mapaRegiao.put("PB", "001");
        mapaRegiao.put("PE", "001");
        mapaRegiao.put("PI", "001");
        mapaRegiao.put("PR", "001");
        mapaRegiao.put("RJ", "001");
        mapaRegiao.put("RN", "001");
        mapaRegiao.put("RO", "001");
        mapaRegiao.put("RR", "001");
        mapaRegiao.put("RS", "001");
        mapaRegiao.put("SC", "001");
        mapaRegiao.put("SE", "001");
        mapaRegiao.put("SP", "001");
        return mapaRegiao.get(estado) == null ? "" : mapaRegiao.get(estado);

    }

    public static String extrairDDD(String nr) {
        String ddd = "";
        String regex = "(\\((\\w+)\\))+";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(nr);
        while (m.find()) {
            ddd = m.group(1);
        }
        return ddd.replaceAll("\\(", "0").replaceAll("\\)", "");
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import br.com.pactosolucoes.comuns.util.Formatador;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasPagamentoDigitalEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;
import servicos.integracao.selfit.cliente.WSDADCLI;
import servicos.integracao.selfit.tituloprovisorio.WSDADPROV;

/**
 *
 * <AUTHOR>
 */
public class TituloProvisorioProtheus extends SuperEntidade {
    
    
//ALTER TABLE movparcela ADD COLUMN codprotheus character varying (30);
//ALTER TABLE movparcela ADD COLUMN chavgravdts character varying (30);
//ALTER TABLE movparcela ADD COLUMN chavgravdtsoperadora character varying (30);
    
    public TituloProvisorioProtheus() throws Exception {
        super();
    }

    public TituloProvisorioProtheus(Connection conexao) throws Exception {
        super(conexao);
    }
    
    public Map<String, Map<Integer, WSDADPROV>> obterParcelasSincronizar(Integer empresa, TipoOperacaoEnum tipo,
            List<String> cpfs, Integer limit) throws Exception {
        Map<String, Map<Integer, WSDADPROV>> mapa = new HashMap<String, Map<Integer, WSDADPROV>>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT distinct aut.operadoracartao,cli.matricula, p.cfp, mp.codigo as codigoparcela, mp.descricao, mp.datavencimento, mp.dataregistro, mp.valorparcela,  \n");
        sql.append("        (SELECT descricao from plano \n");
	sql.append("        	where codigo in (select plano from contrato where codigo = mp.contrato)) as plano, \n");
        sql.append(" cli.codigolojaprotheus, cli.codigoprotheus, mp.contrato, (SELECT ARRAY_TO_STRING(ARRAY(\n");
        sql.append(" SELECT distinct(tipoproduto) FROM produto p\n");
        sql.append(" INNER JOIN movproduto mpd ON mpd.produto = p.codigo\n");
        sql.append(" INNER JOIN movprodutoparcela mpp ON mpp.movproduto = mpd.codigo\n");
        sql.append(" WHERE mpp.movparcela = mp.codigo), ',')) as produtos, codigocontrato\n");
        sql.append(" FROM movparcela mp\n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = mp.pessoa\n");
        sql.append(" INNER JOIN cliente cli ON p.codigo = cli.pessoa AND cli.codigoprotheus IS NOT NULL\n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sw ON sw.codigocliente = cli.codigo\n");
        sql.append(" INNER JOIN autorizacaocobrancacliente aut ON aut.cliente = cli.codigo\n");
        sql.append(" LEFT JOIN pagamentomovparcela pmp ON mp.codigo = pmp.movparcela\n");
        sql.append(" LEFT JOIN movpagamento mpg ON mpg.codigo = pmp.movpagamento\n");

        sql.append(" WHERE ((mp.contrato is not null and mp.contrato > 0) OR mp.descricao LIKE '%MULTA%' ) \n");
        sql.append(" AND (mp.situacao = 'EA' OR (mpg.datapagamento > '2016-07-31 23:59:59')) ");
        sql.append(" AND not exists(select codigo from integracaoprotheus where chavepacto = mp.codigo and tipo = 'PR' and status IN ('A','S') AND operacao = 'I') ");
        
        
        if(!UteisValidacao.emptyList(cpfs)){
            Boolean first = true;
            sql.append(" AND p.cfp IN (");
            for(String cpf : cpfs){
                sql.append(first ? "'":",'").append(cpf).append("'");
                first = false;
            }
            sql.append(")");
        }
        sql.append(" ORDER BY mp.codigo");
        if(!UteisValidacao.emptyNumber(limit)){
            sql.append(" LIMIT ").append(limit);
        }
        PropriedadesIntegracaoProtheus.print(sql.toString());
        ResultSet rs = criarConsulta(sql.toString(), con);
        int i = 0;
        while (rs.next()) {
            try {
                String cpf = rs.getString("cfp");
                PropriedadesIntegracaoProtheus.print("TITPROV: "+(++i));
                Map<Integer, WSDADPROV> mapaParcela = mapa.get(cpf);
                if(mapaParcela == null){
                    mapaParcela = new HashMap<Integer, WSDADPROV>();
                    mapa.put(cpf, mapaParcela);
                }
                mapaParcela.put(rs.getInt("codigoparcela"), montarMapaAtributosParcela(rs, tipo));
            }catch (Exception e){

            }

        }
        return mapa;
    }

     public WSDADPROV montarMapaAtributosParcela(ResultSet rs, TipoOperacaoEnum tipo) throws Exception {
         WSDADPROV item = new WSDADPROV();
         item.setZ2TIPOROT(tipo.getSigla());
         String plano = rs.getString("plano");
         String tiposProdutos = rs.getString("produtos");
         String prefixo;
         String descricao = rs.getString("descricao");
         String contrato = rs.getString("contrato");
         item.setZ2EMISSAO(Uteis.getDataAplicandoFormatacao(rs.getDate("dataregistro"), "yyyyMMdd"));
         String vencimento = Uteis.getDataAplicandoFormatacao(rs.getDate("datavencimento"), "yyyyMMdd");
         item.setZ2VENCTO(vencimento);
         item.setZ2VENCREA(vencimento);
         if(descricao.contains("MULTA")){
             prefixo = "MUL";
             contrato = rs.getString("codigocontrato");
             item.setZ2EMISSAO(vencimento);
         }else if(tiposProdutos.contains("PM")){
             prefixo = plano.contains("BLUE") ? "BLU" : "SEL";
         }else if(tiposProdutos.contains("TD") || tiposProdutos.contains("MA")){
             prefixo = "ADE";
         }else if(tiposProdutos.contains("TA")){
             prefixo = "ANU";
         }else{
            prefixo = "AVA";
         }
         item.setZ2PREFIXO(prefixo);
         item.setZ2NUM(rs.getString("contrato"));
         
         String parcela;
         if(descricao.startsWith("PARCELA")){
             parcela = descricao.replaceFirst("PARCELA ", "").replaceAll("- MULTA E JUROS", "").trim();
         }else if(descricao.startsWith("ADESÃO PARCELA")){
             parcela = descricao.replaceFirst("ADESÃO PARCELA ", "").trim();
         }else{
             parcela = "1";
         }
         item.setZ2MATRICU(rs.getString("matricula"));
         item.setZ2VTXADMI("0.00");
         item.setZ2PARCELA(parcela);
         item.setZ2CNO("");
         OperadorasExternasAprovaFacilEnum operadora = OperadorasExternasAprovaFacilEnum.valueOf(rs.getInt("operadoracartao"));
         if(operadora == null){
             item.setZ2ADM("999");
         }else{
             item.setZ2ADM(operadora.getCodProtheus().toString());
         }

         item.setZ2NUM(contrato);
         
         String valor = Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("valorparcela"));
         item.setZ2VALOR(valor.replaceAll("\\,", "."));
         item.setZ2NUMCONT(contrato);
         return item;
     }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.nfe.NotaFiscalDeServicoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.selfit.autenticacao.SELFAUTENTIC;
import servicos.integracao.selfit.autenticacao.SELFAUTENTICSOAP;
import servicos.integracao.selfit.cliente.ARRAYOFWSDADCLI;
import servicos.integracao.selfit.cliente.ARRAYOFWSRETCLI;
import servicos.integracao.selfit.cliente.INCCLIE;
import servicos.integracao.selfit.cliente.WSDADCLI;
import servicos.integracao.selfit.cliente.WSRETCLI;
import servicos.integracao.selfit.cliente.WSSELFCL;
import servicos.integracao.selfit.cliente.WSSELFCLSOAP;
import servicos.integracao.selfit.notafiscal.ARRAYOFWSDADITNF;
import servicos.integracao.selfit.notafiscal.ARRAYOFWSDADNFISC;
import servicos.integracao.selfit.notafiscal.ARRAYOFWSRETCNFIS;
import servicos.integracao.selfit.notafiscal.INCNFISC;
import servicos.integracao.selfit.notafiscal.WSCHVNFISC;
import servicos.integracao.selfit.notafiscal.WSDADITNF;
import servicos.integracao.selfit.notafiscal.WSDADNFISC;
import servicos.integracao.selfit.notafiscal.WSRETNFISC;
import servicos.integracao.selfit.notafiscal.WSSELFNF;
import servicos.integracao.selfit.notafiscal.WSSELFNFSOAP;
import servicos.integracao.selfit.titulooperadora.ARRAYOFWSDADTOPE;
import servicos.integracao.selfit.titulooperadora.ARRAYOFWSRETCTOPE;
import servicos.integracao.selfit.titulooperadora.INCTOPE;
import servicos.integracao.selfit.titulooperadora.WSDADTOPE;
import servicos.integracao.selfit.titulooperadora.WSRETTOPER;
import servicos.integracao.selfit.titulooperadora.WSSELFOP;
import servicos.integracao.selfit.titulooperadora.WSSELFOPSOAP;
import servicos.integracao.selfit.tituloprovisorio.ARRAYOFWSDADPROV;
import servicos.integracao.selfit.tituloprovisorio.ARRAYOFWSRETTPRO;
import servicos.integracao.selfit.tituloprovisorio.INCPROV;
import servicos.integracao.selfit.tituloprovisorio.WSCHVPROV;
import servicos.integracao.selfit.tituloprovisorio.WSDADPROV;
import servicos.integracao.selfit.tituloprovisorio.WSRETPROV;
import servicos.integracao.selfit.tituloprovisorio.WSSELFPR;
import servicos.integracao.selfit.tituloprovisorio.WSSELFPRSOAP;

/**
 *
 * <AUTHOR>
 */
public class ProtheusConsumer {

    private static final int TIMEOUT = 99000;
    public static final String CONNECT_TIMEOUT = "com.sun.xml.ws.connect.timeout";
    public static final String REQUEST_TIMEOUT = "com.sun.xml.ws.request.timeout";
    public static final String URL_PROTEUS = PropriedadesIntegracaoProtheus.getPropertyValue("urlServico")+"ws";
    public static final String CNPJ_PACTO = "05.870.588/0001-58";

    private static SELFAUTENTICSOAP getInstanceAutenticacao() {
        try {
            URL u = new URL(URL_PROTEUS + "/SELFAUTENTIC.apw?WSDL");
            QName qName = new QName(PropriedadesIntegracaoProtheus.getPropertyValue("urlServico"), "SELFAUTENTIC");
            SELFAUTENTICSOAP servico = new SELFAUTENTIC(u, qName).getSELFAUTENTICSOAP();
            Map<String, Object> reqContext = ((BindingProvider) servico).getRequestContext();
            reqContext.put(CONNECT_TIMEOUT, TIMEOUT);
            reqContext.put(REQUEST_TIMEOUT, TIMEOUT);
            return servico;
        } catch (MalformedURLException ex) {
            Logger.getLogger(TreinoWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    private static WSSELFCLSOAP getInstanceCliente() {
        try {
            URL u = new URL(URL_PROTEUS + "/WSSELFCL.apw?WSDL");
            QName qName = new QName(PropriedadesIntegracaoProtheus.getPropertyValue("urlServico"), "WSSELFCL");
            WSSELFCLSOAP servico = new WSSELFCL(u, qName).getWSSELFCLSOAP();
            Map<String, Object> reqContext = ((BindingProvider) servico).getRequestContext();
            reqContext.put(CONNECT_TIMEOUT, TIMEOUT);
            reqContext.put(REQUEST_TIMEOUT, TIMEOUT);
            return servico;
        } catch (MalformedURLException ex) {
            Logger.getLogger(TreinoWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    private static WSSELFPRSOAP getInstanceTituloProvisorio() {
        try {
            URL u = new URL(URL_PROTEUS + "/WSSELFPR.apw?WSDL");
            QName qName = new QName(PropriedadesIntegracaoProtheus.getPropertyValue("urlServico"), "WSSELFPR");
            WSSELFPRSOAP servico = new WSSELFPR(u, qName).getWSSELFPRSOAP();
            Map<String, Object> reqContext = ((BindingProvider) servico).getRequestContext();
            reqContext.put(CONNECT_TIMEOUT, TIMEOUT);
            reqContext.put(REQUEST_TIMEOUT, TIMEOUT);
            return servico;
        } catch (MalformedURLException ex) {
            Logger.getLogger(TreinoWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    private static WSSELFNFSOAP getInstanceNotaFiscal() {
        try {
            URL u = new URL(URL_PROTEUS + "/WSSELFNF.apw?WSDL");
            QName qName = new QName(PropriedadesIntegracaoProtheus.getPropertyValue("urlServico"), "WSSELFNF");
            WSSELFNFSOAP servico = new WSSELFNF(u, qName).getWSSELFNFSOAP();
            Map<String, Object> reqContext = ((BindingProvider) servico).getRequestContext();
            reqContext.put(CONNECT_TIMEOUT, TIMEOUT);
            reqContext.put(REQUEST_TIMEOUT, TIMEOUT);
            return servico;
        } catch (MalformedURLException ex) {
            Logger.getLogger(TreinoWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public static WSRETNFISC enviarNotaFiscal(String chave, String chaveAutenticacao, String cnpj, NotaFiscalDeServicoVO nota, 
            ClienteVO cliente, TipoOperacaoEnum tipo, 
            Integer contrato, String produto) throws Exception{
        
        EnvioProtheusJSON envioJSON = new EnvioProtheusJSON();
        envioJSON.setChaveEmpresa(chave);
        envioJSON.setHoraEnvio(Calendario.hoje());
        envioJSON.setEntidade("NOTA FISCAL");
        envioJSON.setChaveAutenticacao(IntegracaoProtheus.chaveMestra);
        JSONObject jo = new JSONObject();
        jo.put("chaveEmpresa", chave);
        jo.put("cnpjSelfit", cnpj);
        
        
        ARRAYOFWSDADITNF arrayItem = new ARRAYOFWSDADITNF();
        WSDADITNF itemEnvio = new WSDADITNF();
        String valor = Formatador.formatarValorMonetarioSemMoeda(nota.getAliquotaAtividade());
        itemEnvio.setZ5ALIQISS(valor.replaceAll("\\,", "."));
        
        valor = Formatador.formatarValorMonetarioSemMoeda(nota.getBaseCalculo());
        itemEnvio.setZ5BASEISS(valor.replaceAll("\\,", "."));
        itemEnvio.setZ5CODISS(nota.getCodAtividade());
        String datfat = Uteis.getDataAplicandoFormatacao(nota.getDataEmissao(), "yyyyMMdd");
        itemEnvio.setZ5DATFAT(datfat);
        itemEnvio.setZ5DESCRI(nota.getDescricao());
        itemEnvio.setZ5ITEM("01");
        
        String numeroNota = StringUtilities.formatarCampo(new BigDecimal(nota.getNumeroNota()), 9);
        itemEnvio.setZ5NOTA(numeroNota);
        
        valor = Formatador.formatarValorMonetarioSemMoeda(nota.getValorTotal());
        itemEnvio.setZ5PRCVEN(valor.replaceAll("\\,", "."));
        itemEnvio.setZ5PRODUTO(produto);
        itemEnvio.setZ5QTDVEN("01");
        itemEnvio.setZ5SERIE(nota.getSerieRPS());
        valor = Formatador.formatarValorMonetarioSemMoeda(0.0);
        itemEnvio.setZ5VALDESC(valor.replaceAll("\\,", "."));
        valor = Formatador.formatarValorMonetarioSemMoeda(nota.getValorISSRetido());
        itemEnvio.setZ5VALISS(valor.replaceAll("\\,", "."));
        valor = Formatador.formatarValorMonetarioSemMoeda(nota.getValorTotal());
        itemEnvio.setZ5VALOR(valor.replaceAll("\\,", "."));
        arrayItem.getWSDADITNF().add(itemEnvio);
        WSDADNFISC notaEnvio = new WSDADNFISC();

        notaEnvio.setNITEMNF(arrayItem);
        String protocolo = StringUtilities.formatarCampo(new BigDecimal(nota.getLote().getId_lote()), 15);
        notaEnvio.setZ4PROTOC(protocolo);
        notaEnvio.setZ4CGC(Uteis.removerMascara(cliente.getPessoa().getCfp()));
        
        notaEnvio.setZ4CHVNFE(nota.getCodigoVerificacao());
        notaEnvio.setZ4CODNFE(nota.getCodigoVerificacao());
        
        String emissao = Uteis.getDataAplicandoFormatacao(nota.getDataEmissao(), "yyyyMMdd");
        notaEnvio.setZ4EMISSAO(emissao);
        notaEnvio.setZ4EMINFE(emissao);
        notaEnvio.setZ4HORNFE(Uteis.getDataAplicandoFormatacao(nota.getDataEmissao(), "HH:mm:ss"));
        
        String processamento = Uteis.getDataAplicandoFormatacao(nota.getDataProcessamento(), "yyyyMMdd");
        notaEnvio.setZ4DAUTNFE(processamento);
        notaEnvio.setZ4HAUTNFE(Uteis.getDataAplicandoFormatacao(nota.getDataProcessamento(), "HH:mm"));
        
        
//        nota.setDataCancelamento(Calendario.hoje());
        if(nota.getDataCancelamento() != null && tipo.equals(TipoOperacaoEnum.EXCLUSAO)){
            String cancelamento = Uteis.getDataAplicandoFormatacao(nota.getDataCancelamento(), "yyyyMMdd");
            notaEnvio.setZ4DTCANC(cancelamento);
        }
        
        notaEnvio.setZ4NFELETR(numeroNota);
        notaEnvio.setZ4MATRICU(cliente.getMatricula());
        notaEnvio.setZ4NOTA(numeroNota);
        notaEnvio.setZ4OBSENF(nota.getObservacao());
        notaEnvio.setZ4SERIE(nota.getSerieRPS());
        notaEnvio.setZ4ESPECI1("NFS");
        notaEnvio.setZ4TIPOROT(tipo.getSigla());
        notaEnvio.setZ4MDCONTR(contrato.toString());
        notaEnvio.setZ4CGC(Uteis.removerMascara(cliente.getPessoa().getCfp()));
        notaEnvio.setZ4MATRICU(cliente.getMatricula());
        notaEnvio.setZ4STATNF("");
        notaEnvio.setZ4MENNOTA("");
        jo.put("dados", getInformacoes(notaEnvio, itemEnvio));
        ARRAYOFWSDADNFISC array = new ARRAYOFWSDADNFISC();
        array.getWSDADNFISC().add(notaEnvio);
        INCNFISC inc = new INCNFISC();
        inc.setNINCNFISC(array);
        envioJSON.setDadosEnviados(jo.toString());
        envioJSON.setHoraEnvio(Calendario.hoje());
        WSRETNFISC wsincnotafisc = null;
        try {
            wsincnotafisc = getInstanceNotaFiscal().wsincnotafisc(chaveAutenticacao, Uteis.removerMascara(cnpj), inc);
            for (WSCHVNFISC wsch : wsincnotafisc.getCHAVGRV().getWSCHVNFISC()) {
                envioJSON.setCodigoRetorno(wsch.getCCHAVPROV());
                envioJSON.setMsErro(wsch.getMENSGRAVA());
                if (UteisValidacao.emptyString(wsch.getCCHAVPROV())) {
                        envioJSON.setStatus(StatusEnvioEnum.FALHA);
                    } else {
                        envioJSON.setStatus(StatusEnvioEnum.AGUARDANDO);
                    }
            }
        } catch (Exception e) {
            envioJSON.setStatus(StatusEnvioEnum.FALHA);
            envioJSON.setMsErro(e.getMessage());
        }
        FeedBackEnvioProtheus.enviarFeedBack(envioJSON);
        return wsincnotafisc;
    }

    private static WSSELFOPSOAP getInstanceTituloOperadora() {
        try {
            URL u = new URL(URL_PROTEUS + "/WSSELFOP.apw?WSDL");
            QName qName = new QName(PropriedadesIntegracaoProtheus.getPropertyValue("urlServico"), "WSSELFOP");
            WSSELFOPSOAP servico = new WSSELFOP(u, qName).getWSSELFOPSOAP();
            Map<String, Object> reqContext = ((BindingProvider) servico).getRequestContext();
            reqContext.put(CONNECT_TIMEOUT, TIMEOUT);
            reqContext.put(REQUEST_TIMEOUT, TIMEOUT);
            return servico;
        } catch (MalformedURLException ex) {
            Logger.getLogger(TreinoWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public static String getChaveAutenticacao(String chave, String cnpjSelfit) throws JSONException {
        EnvioProtheusJSON envioJSON = new EnvioProtheusJSON();
        envioJSON.setChaveEmpresa(chave);
        envioJSON.setHoraEnvio(Calendario.hoje());
        envioJSON.setEntidade("CH. AUTENTICAÇÃO");
        envioJSON.setChaveAutenticacao(IntegracaoProtheus.chaveMestra);
        JSONObject jo = new JSONObject();
        jo.put("chaveEmpresa", chave);
        jo.put("chaveMestra", IntegracaoProtheus.chaveMestra);
        jo.put("cnpjSelfit", cnpjSelfit);
        envioJSON.setDadosEnviados(jo.toString());
        envioJSON.setHoraEnvio(Calendario.hoje());
        String chaveAutenticacao = null;
        try {
            chaveAutenticacao = getInstanceAutenticacao().autenticacao(IntegracaoProtheus.chaveMestra, Uteis.removerMascara(cnpjSelfit), Uteis.removerMascara(CNPJ_PACTO));
            envioJSON.setCodigoRetorno(chaveAutenticacao);
            envioJSON.setStatus(StatusEnvioEnum.SUCESSO);
        } catch (Exception e) {
            envioJSON.setMsErro(e.getMessage());
            envioJSON.setStatus(StatusEnvioEnum.FALHA);
        }
        FeedBackEnvioProtheus.enviarFeedBack(envioJSON);
        return chaveAutenticacao;
    }

    public static Map<String, ARRAYOFWSRETCLI> salvarClientes(Connection con, String chave, String autorizacao, Map<String, WSDADCLI> wsdadcli, String cnpj) {
        Map<String, ARRAYOFWSRETCLI> mapa = new HashMap<String, ARRAYOFWSRETCLI>();
        FORCLI: for (String cpf : wsdadcli.keySet()) {
            EnvioProtheusJSON envioJSON = new EnvioProtheusJSON();
            envioJSON.setChaveEmpresa(chave);
            envioJSON.setEntidade("CLIENTE");
            envioJSON.setChaveAutenticacao(autorizacao);
            envioJSON.setHoraEnvio(Calendario.hoje());
            try {
                JSONObject informacoes = getInformacoes(wsdadcli.get(cpf), WSDADCLI.class);
                PropriedadesIntegracaoProtheus.print("ENVIANDO ALUNO COM CPF: " + cpf);
                if(UteisValidacao.emptyString(wsdadcli.get(cpf).getA1CODMUN())){
                    throw new Exception("Aluno sem cidade ou código do município não informado.");
                }
                INCCLIE param = new INCCLIE();
                ARRAYOFWSDADCLI array = new ARRAYOFWSDADCLI();
                array.getWSDADCLI().add(wsdadcli.get(cpf));
                
                informacoes.put("CPF", cpf);
                envioJSON.setDadosEnviados(informacoes.toString());
                param.setNINCCLIE(array);
                ARRAYOFWSRETCLI ret = getInstanceCliente().wscliincl(autorizacao, Uteis.removerMascara(cnpj), Uteis.removerMascara(cpf), param);
                for (WSRETCLI wsretcli : ret.getWSRETCLI()) {
                    envioJSON.setCodigoRetorno(wsretcli.getCHAVGRV());
                    envioJSON.setMsErro(wsretcli.getMSGERRO());
                    if(wsretcli.getMSGERRO().toLowerCase().contains("cliente ja cadastrado")){
                        ARRAYOFWSRETCLI retornoConsultaCadastro = ProtheusConsumer.consultarCadastroCliente(autorizacao, Uteis.removerMascara(cnpj), cpf);
                        for (WSRETCLI cliCad : retornoConsultaCadastro.getWSRETCLI()) {
                            try {
                                if (cliCad.getCSTATUS().equals("1")) {
                                    PreparedStatement stm = con.prepareStatement("UPDATE cliente SET codigolojaprotheus = ?, " + " codigoprotheus = ? WHERE pessoa in (select codigo from pessoa " + " WHERE cfp = ? )");
                                    stm.setString(1, cliCad.getA1LOJA());
                                    stm.setString(2, cliCad.getA1COD());
                                    PropriedadesIntegracaoProtheus.print("loja: " + cliCad.getA1LOJA() + "\ncodigo: " + cliCad.getA1COD() + "\nchave:" + cpf + "\n----------------------");
                                    stm.setString(3, cpf);
                                    stm.execute();
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        continue FORCLI;
                    }
                    if (wsretcli.getCHAVGRV() == null
                            || wsretcli.getCHAVGRV().isEmpty()) {
                        envioJSON.setStatus(StatusEnvioEnum.FALHA);
                    } else {
                        envioJSON.setStatus(StatusEnvioEnum.AGUARDANDO);
                    }

                }
                mapa.put(cpf, ret);
            } catch (Exception e) {
                PropriedadesIntegracaoProtheus.print(e.getMessage());
                envioJSON.setMsErro(e.getMessage());
                envioJSON.setStatus(StatusEnvioEnum.FALHA);
            }
            FeedBackEnvioProtheus.enviarFeedBack(envioJSON);
        }
        return mapa;
    }

    private static JSONObject getInformacoes(Object ws, Class classe) throws JSONException {
        JSONObject ret = new JSONObject();
        List<String> listAttributes = UtilReflection.getListAttributes(classe);
        for (String s : listAttributes) {
            ret.put(s, UtilReflection.getValor(ws, s));
        }
        return ret;
    }
    
    private static String getInformacoes(WSDADNFISC notaEnvio, WSDADITNF itemEnvio) throws JSONException {
        StringBuilder log = new StringBuilder();
        log.append("<ns:WSDADNFISC> \n");
        log.append("<ns:NITEMNF> \n");
        log.append("<!--Zero or more repetitions:-->\n");
        log.append("<ns:WSDADITNF>\n");
        log.append("<!--Optional:-->\n");
        log.append("<ns:z5ALIQISS>").append(itemEnvio.getZ5ALIQISS()).append("</ns:z5ALIQISS>\n");
        log.append("<!--Optional:-->\n");
        log.append("<ns:Z5_BASEISS>").append(itemEnvio.getZ5BASEISS()).append("</ns:Z5_BASEISS>\n");
        log.append("<!--Optional:-->\n");
        log.append("<ns:Z5_CODISS>").append(itemEnvio.getZ5CODISS()).append("</ns:Z5_CODISS>\n");
        log.append("<ns:Z5_DATFAT>").append(itemEnvio.getZ5DATFAT()).append("</ns:Z5_DATFAT>\n");
        log.append("<ns:Z5_DESCRI>").append(itemEnvio.getZ5DESCRI()).append("</ns:Z5_DESCRI>\n");
        log.append("<ns:Z5_ITEM>").append(itemEnvio.getZ5ITEM()).append("</ns:Z5_ITEM>\n");
        log.append("<ns:Z5_NOTA>").append(itemEnvio.getZ5NOTA()).append("</ns:Z5_NOTA>\n");
        log.append("<ns:Z5_PRCVEN>").append(itemEnvio.getZ5PRCVEN()).append("</ns:Z5_PRCVEN>\n");
        log.append("<ns:Z5_PRODUTO>").append(itemEnvio.getZ5PRODUTO()).append("</ns:Z5_PRODUTO>\n");
        log.append("<ns:Z5_QTDVEN>").append(itemEnvio.getZ5QTDVEN()).append("</ns:Z5_QTDVEN>\n");
        log.append("<ns:Z5_SERIE>").append(itemEnvio.getZ5SERIE()).append("</ns:Z5_SERIE>\n");
        log.append("<!--Optional:-->\n");
        log.append("<ns:Z5_VALDESC>").append(itemEnvio.getZ5VALDESC()).append("</ns:Z5_VALDESC>\n");
        log.append("<!--Optional:-->\n");
        log.append("<ns:Z5_VALISS>").append(itemEnvio.getZ5VALISS()).append("</ns:Z5_VALISS>\n");
        log.append("<ns:Z5_VALOR>").append(itemEnvio.getZ5VALOR()).append("</ns:Z5_VALOR>\n");
        log.append("</ns:WSDADITNF>\n");
        log.append("</ns:NITEMNF>\n");
        log.append("<ns:Z4_CGC>").append(notaEnvio.getZ4CGC()).append("</ns:Z4_CGC>\n");
        log.append("<ns:Z4_CHVNFE>").append(notaEnvio.getZ4CHVNFE()).append("</ns:Z4_CHVNFE>\n");
        log.append("<ns:Z4_CODNFE>").append(notaEnvio.getZ4CODNFE()).append("</ns:Z4_CODNFE>\n");
        log.append("<ns:Z4_DAUTNFE>").append(notaEnvio.getZ4DAUTNFE()).append("</ns:Z4_DAUTNFE>\n");
        log.append("<!--Optional:-->\n");
        log.append("<ns:Z4_DTCANC>").append(notaEnvio.getZ4DTCANC()).append("</ns:Z4_DTCANC>\n");
        log.append("<ns:Z4_EMINFE>").append(notaEnvio.getZ4EMINFE()).append("</ns:Z4_EMINFE>\n");
        log.append("<ns:Z4_EMISSAO>").append(notaEnvio.getZ4EMISSAO()).append("</ns:Z4_EMISSAO>\n");
        log.append("<!--Optional:-->\n");
        log.append("<ns:Z4_ESPECI1>").append(notaEnvio.getZ4ESPECI1()).append("</ns:Z4_ESPECI1>\n");
        log.append("<ns:Z4_HAUTNFE>").append(notaEnvio.getZ4HAUTNFE()).append("</ns:Z4_HAUTNFE>\n");
        log.append("<ns:Z4_HORNFE>").append(notaEnvio.getZ4HORNFE()).append("</ns:Z4_HORNFE>\n");
        log.append("<ns:Z4_MATRICU>").append(notaEnvio.getZ4MATRICU()).append("</ns:Z4_MATRICU>\n");
        log.append("<ns:Z4_MDCONTR>").append(notaEnvio.getZ4MDCONTR()).append("</ns:Z4_MDCONTR>\n");
        log.append("<!--Optional:-->\n");
        log.append("<ns:Z4_MENNOTA>").append(notaEnvio.getZ4MENNOTA()).append("</ns:Z4_MENNOTA>\n");
        log.append("<ns:Z4_NFELETR>").append(notaEnvio.getZ4NFELETR()).append("</ns:Z4_NFELETR>\n");
        log.append("<ns:Z4_NOTA>").append(notaEnvio.getZ4NOTA()).append("</ns:Z4_NOTA>\n");
        log.append("<ns:Z4_OBSENF>").append(notaEnvio.getZ4OBSENF()).append("</ns:Z4_OBSENF>\n");
        log.append("<ns:Z4_PROTOC>").append(notaEnvio.getZ4PROTOC()).append("</ns:Z4_PROTOC>\n");
        log.append("<ns:Z4_SERIE>").append(notaEnvio.getZ4SERIE()).append("</ns:Z4_SERIE>\n");
        log.append("<ns:Z4_STATNF>").append(notaEnvio.getZ4STATNF()).append("</ns:Z4_STATNF>\n");
        log.append("<ns:Z4_TIPOROT>").append(notaEnvio.getZ4TIPOROT()).append("</ns:Z4_TIPOROT>\n");
        log.append("</ns:WSDADNFISC>\n");
//        PropriedadesIntegracaoProtheus.print(log.toString().replaceAll("null", ""));
        return log.toString();
    }

    public static ARRAYOFWSRETCLI consultarProcessamentoCliente(String chaveAutenticacao, String cnpj, String codigo) {
        PropriedadesIntegracaoProtheus.print("Consultar retorno cliente. CNPJ:" + Uteis.removerMascara(cnpj));
        return getInstanceCliente().wsconproc(chaveAutenticacao, Uteis.removerMascara(cnpj), codigo);
    }

    public static ARRAYOFWSRETCLI consultarCadastroCliente(String chaveAutenticacao, String cnpj, String cpf) {
        PropriedadesIntegracaoProtheus.print("Consultar cadastro cliente. CPF:" + Uteis.removerMascara(cpf));
        return getInstanceCliente().wsclicons(chaveAutenticacao, Uteis.removerMascara(cnpj), Uteis.removerMascara(cpf));
    }

    public static ARRAYOFWSRETTPRO consultarProcessamentoTituloProvisorio(String chaveAutenticacao, String cnpj, String codigo) {
        PropriedadesIntegracaoProtheus.print("Consultar retorno titulo provisorio. CNPJ:" + Uteis.removerMascara(cnpj));
        return getInstanceTituloProvisorio().wsconproc(chaveAutenticacao, Uteis.removerMascara(cnpj), codigo);
        
    }

    public static ARRAYOFWSRETCTOPE consultarProcessamentoTituloOperadora(String chaveAutenticacao, String cnpj, String codigo) {
        PropriedadesIntegracaoProtheus.print("Consultar retorno titulo operadora. CNPJ:" + Uteis.removerMascara(cnpj));
        return getInstanceTituloOperadora().wscontiop(chaveAutenticacao, Uteis.removerMascara(cnpj), codigo);
        
    }

    public static ARRAYOFWSRETCNFIS consultarProcessamentoNotaFiscal(String chaveAutenticacao, String cnpj, String codigo) {
        PropriedadesIntegracaoProtheus.print("Consultar retorno nota fiscal. CNPJ:" + Uteis.removerMascara(cnpj));
        return getInstanceNotaFiscal().wsconnfisc(chaveAutenticacao, Uteis.removerMascara(cnpj), codigo);
    }

    public static List<WSRETPROV> salvarTitulosProvisorios(String chave, String autorizacao,
            String cpf,
            WSDADPROV item, String cnpj) {
        List<WSRETPROV> listaReturn = new ArrayList<WSRETPROV>();
            EnvioProtheusJSON envioJSON = new EnvioProtheusJSON();
            envioJSON.setChaveEmpresa(chave);
            envioJSON.setEntidade("TITULO PROVISORIO");
            envioJSON.setChaveAutenticacao(autorizacao);
            envioJSON.setHoraEnvio(Calendario.hoje());
            try {
                PropriedadesIntegracaoProtheus.print("ENVIANDO TIT PROV DO CPF: " + cpf);
                INCPROV adadprov = new INCPROV();
                ARRAYOFWSDADPROV value = new ARRAYOFWSDADPROV();
                value.getWSDADPROV().add(item);
                adadprov.setNINCPROV(value);
                JSONObject informacoes = new JSONObject();
                informacoes.put("CPF", cpf);
                JSONArray arr = new JSONArray();
                arr.put(getInformacoes(item, WSDADPROV.class));
                
                informacoes.put("dados", arr);
                envioJSON.setDadosEnviados(informacoes.toString());
                WSRETPROV wsinctitprov = getInstanceTituloProvisorio().wsinctitprov(autorizacao, Uteis.removerMascara(cnpj), Uteis.removerMascara(cpf), adadprov);
                listaReturn.add(wsinctitprov);
                for(WSCHVPROV wsret : wsinctitprov.getCHAVGRV().getWSCHVPROV()){
                    envioJSON.setCodigoRetorno(wsret.getCCHAVPROV());
                }
                envioJSON.setStatus(StatusEnvioEnum.AGUARDANDO);
            } catch (Exception e) {
                PropriedadesIntegracaoProtheus.print(e.getMessage());
                envioJSON.setMsErro(e.getMessage());
                envioJSON.setStatus(StatusEnvioEnum.FALHA);
            }
            FeedBackEnvioProtheus.enviarFeedBack(envioJSON);
        return listaReturn;
    }
    
    public ARRAYOFWSRETCLI verificarRetornoProcessamentoCliente(String chave, String autorizacao, 
            String cnpj,
            String codigoProcessamento){
        return getInstanceCliente().wsconproc(autorizacao, Uteis.removerMascara(cnpj), codigoProcessamento);
    }

    public static List<WSRETTOPER> salvarTitulosOperadora(String chave, String autorizacao,String cpf,
            WSDADTOPE itemprov, String cnpj) {
        List<WSRETTOPER> lista = new ArrayList<WSRETTOPER>();
            EnvioProtheusJSON envioJSON = new EnvioProtheusJSON();
            envioJSON.setChaveEmpresa(chave);
            envioJSON.setEntidade("TITULO OPERADORA");
            envioJSON.setChaveAutenticacao(autorizacao);
            envioJSON.setHoraEnvio(Calendario.hoje());
            try {
                INCTOPE param = new INCTOPE();
                ARRAYOFWSDADTOPE value = new ARRAYOFWSDADTOPE();
                value.getWSDADTOPE().add(itemprov);
                param.setNINCTOPE(value);
                JSONObject informacoes = new JSONObject();
                informacoes.put("CPF", cpf);
                JSONArray arr = new JSONArray();
                arr.put(getInformacoes(itemprov, WSDADTOPE.class));
                informacoes.put("dados", arr);
                envioJSON.setDadosEnviados(informacoes.toString());
                WSRETTOPER wsinctitopera = getInstanceTituloOperadora().wsinctitopera(autorizacao, Uteis.removerMascara(cnpj), param);
                lista.add(wsinctitopera);
                envioJSON.setStatus(StatusEnvioEnum.AGUARDANDO);
            } catch (Exception e) {
                PropriedadesIntegracaoProtheus.print(e.getMessage());
                envioJSON.setMsErro(e.getMessage());
                envioJSON.setStatus(StatusEnvioEnum.FALHA);
            }
            FeedBackEnvioProtheus.enviarFeedBack(envioJSON);
        return lista;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

/**
 *
 * <AUTHOR>
 */
public enum CampoClienteProtheusEnum {
    
    A1_NOME("C",40, "Nome do cliente"),
    A1_PESSOA("C", 1, "Pessoa Fisica/Juridica"),
    A1_END("C", 80, "Endereco do cliente"),
    A1_NREDUZ("C", 20, "Fantasia Nome Reduzido do cliente"),
    A1_BAIRRO("C",40, "Bairro Bairro do cliente"),
    A1_TIPO("C",1, "Tipo do Cliente"),
    A1_EST("C",2, "Estado do cliente"), 
    A1_CEP("C",8, "CEP"),
    A1_COD_MUN("C",5, "Cd.Municipio"),
    A1_MUN("C",60, "Municipio do cliente"),
    A1_REGIAO("C",3, "Regiao do Cliente"),
    A1_DSCREG("C",15, "Desc.Regiao"),
    A1_DDD("C",3, "DDD Codigo do DDD"),
    A1_TEL("C",15, "Telefone"),
    A1_CGC("C",14, "CNPJ/CPF"),
    A1_PFISICA("C",18, "RG/Ced.Estr."),
    A1_INSCR("C",18, "Inscricao Estadual"),
    A1_INSCRM("C",18, "Inscricao Municipal"),
    A1_PAIS("C",3, "Pais"),
    A1_DTNASC("D",8, "Dt.Aber/Nasc"),
    A1_EMAIL("C",30, "E-Mail"),
    A1_COMPLEM("C",50, "Complemento"), 
    A1_HRCAD("C",5, "Hr.Cadastro"),
    A1_DTCAD("D",8, "Dt.Cadastro");
        
     String tipo;   
     Integer tamanho;   
     String descricao;   

    private CampoClienteProtheusEnum(String tipo, Integer tamanho, String descricao) {
        this.tipo = tipo;
        this.tamanho = tamanho;
        this.descricao = descricao;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getTamanho() {
        return tamanho;
    }

    public void setTamanho(Integer tamanho) {
        this.tamanho = tamanho;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
     
    
        
}

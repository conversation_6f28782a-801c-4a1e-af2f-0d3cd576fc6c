/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import controle.arquitetura.SuperControle;
import java.io.File;
import java.io.FileReader;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class PropriedadesIntegracaoProtheus {
    private static Properties props = null;

    public PropriedadesIntegracaoProtheus() {
    }
    
    
    private static void loadProps() {
        props = new Properties();
        try {
            props.load(new FileReader(new File(SuperControle.class.getResource(
                    "/servicos/propriedades/IntegracaoProtheus.properties").toURI())));
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
        }
    }
    
    
    public static String getPropertyValue(final String property) {
        if(props == null){
            loadProps();
        }
        return props.getProperty(property);
    }
    
    public static Integer getPropertyValueInt(final String property) {
        try {
            return Integer.valueOf(props.getProperty(property));
        } catch (Exception e) {
            return null;
        }
        
    }
    
    public static void print(String str){
        if(Boolean.valueOf(getPropertyValue("imprimirLog").trim())){
            System.out.println(str);
        }
        
    }
}

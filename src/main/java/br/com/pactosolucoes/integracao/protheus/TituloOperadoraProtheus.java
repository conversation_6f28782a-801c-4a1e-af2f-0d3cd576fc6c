/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import br.com.pactosolucoes.comuns.util.Formatador;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;
import servicos.integracao.selfit.titulooperadora.WSDADTOPE;

/**
 *
 * <AUTHOR>
 */
public class TituloOperadoraProtheus extends SuperEntidade {
        
    public TituloOperadoraProtheus() throws Exception {
        super();
    }

    public TituloOperadoraProtheus(Connection conexao) throws Exception {
        super(conexao);
    }
    public Map<String, Map<Integer,WSDADTOPE>> obterParcelasSincronizar(Integer empresa, TipoOperacaoEnum tipo, String cnpj, 
            List<String> cpfs, Integer limit) throws Exception {
        Map<String, Map<Integer,WSDADTOPE>>  mapa = new HashMap<String, Map<Integer,WSDADTOPE>>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT distinct fp.descricao as formapagamento, fp.tipoformapagamento, oc.codigointegracaoapf as operadoracartao, aut.operadoracartao as operadoraaut, mpg.autorizacaocartao, mpg.datalancamento as mpdata, cc.datacompesancao as ccdata, mpg.dataquitacao as mpdataquitacao,\n");
        sql.append("	    cli.matricula, p.cfp, mp.codigo as codigoparcela, mp.descricao, mp.datavencimento, mp.dataregistro, mp.valorparcela,  \n");
        sql.append("        cli.codigoprotheus, cli.codigolojaprotheus, \n");
        sql.append("        (SELECT descricao from plano \n");
	sql.append("        	where codigo in (select plano from contrato where codigo = mp.contrato)) as plano, \n");
        sql.append("        mp.contrato, (SELECT ARRAY_TO_STRING(ARRAY(\n");
        sql.append("        SELECT distinct(tipoproduto) FROM produto p\n");
        sql.append("        INNER JOIN movproduto mpd ON mpd.produto = p.codigo\n");
        sql.append("        INNER JOIN movprodutoparcela mpp ON mpp.movproduto = mpd.codigo\n");
        sql.append("        WHERE mpp.movparcela = mp.codigo), ',')) as produtos, codigocontrato\n");
        sql.append("        FROM movparcela mp\n");
        sql.append("        INNER JOIN pessoa p ON p.codigo = mp.pessoa\n");
        sql.append("        INNER JOIN cliente cli ON p.codigo = cli.pessoa\n");
        sql.append("        INNER JOIN situacaoclientesinteticodw sw ON sw.codigocliente = cli.codigo\n");
        sql.append("        INNER JOIN pagamentomovparcela pmpg ON mp.codigo = pmpg.movparcela\n");
        sql.append("        INNER JOIN movpagamento mpg ON mpg.codigo = pmpg.movpagamento\n");
        sql.append("        INNER JOIN formapagamento fp ON fp.codigo = mpg.formapagamento\n");
        sql.append("        INNER JOIN autorizacaocobrancacliente aut ON aut.cliente = cli.codigo\n");
        sql.append("        LEFT JOIN operadoracartao oc ON oc.codigo = mpg.operadoracartao\n");
        sql.append("        LEFT JOIN cartaocredito cc ON cc.movpagamento = mpg.codigo\n");
        sql.append("        LEFT JOIN integracaoprotheus ipPR ON ipPR.chavepacto = mp.codigo AND ipPR.tipo = 'PR'\n");
        sql.append("        LEFT JOIN integracaoprotheus ipOP ON ipOP.chavepacto = mp.codigo AND ipOP.tipo = 'OP'\n");
        sql.append("        WHERE mp.situacao LIKE 'PG' \n");
        sql.append("        AND mpg.datapagamento > '2016-07-31 23:59:59' \n");
        sql.append("        AND exists(select codigo from integracaoprotheus where chavepacto = mp.codigo and tipo = 'PR' and status IN ('S') AND operacao = 'I')\n");
        sql.append("        AND not exists(select codigo from integracaoprotheus where chavepacto = mp.codigo and tipo = 'OP' and status IN ('A','S') AND operacao = 'I')\n");
        
              
        
        if(!UteisValidacao.emptyList(cpfs)){
            Boolean first = true;
            sql.append(" AND p.cfp IN (");
            for(String cpf : cpfs){
                sql.append(first ? "'":",'").append(cpf).append("'");
                first = false;
            }
            sql.append(")");
        }
        sql.append("        ORDER BY mp.codigo ");
        if(!UteisValidacao.emptyNumber(limit)){
            sql.append(" LIMIT ").append(limit);
        }
        
        PropriedadesIntegracaoProtheus.print(sql.toString());
        ResultSet rs = criarConsulta(sql.toString(), con);
        while (rs.next()) {
            String cpf = rs.getString("cfp");
            PropriedadesIntegracaoProtheus.print("CPF: "+Uteis.removerMascara(cpf));
            Map<Integer,WSDADTOPE> mapParcela = mapa.get(cpf);
            if(mapParcela == null){
                mapParcela = new HashMap<Integer, WSDADTOPE>();
                mapa.put(cpf, mapParcela);
            }
            mapParcela.put(rs.getInt("codigoparcela"),montarMapaAtributosParcela(rs, tipo, cnpj));
        }
        return mapa;
    }

         public WSDADTOPE montarMapaAtributosParcela(ResultSet rs, TipoOperacaoEnum tipo, String cnpj) throws Exception {
         WSDADTOPE item = new WSDADTOPE();
         item.setZ3TIPOROT(tipo.getSigla());
         String tiposProdutos = rs.getString("produtos");
         String prefixo;
         Date datamp = rs.getDate("mpdata");
         Date datacc = rs.getDate("ccdata");
         Date dataquitacao = rs.getDate("mpdataquitacao");
         item.setZ3EMISSAO(Uteis.getDataAplicandoFormatacao(datamp, "yyyyMMdd"));
         String vencimento = Uteis.getDataAplicandoFormatacao(datacc == null ? dataquitacao : datacc, "yyyyMMdd");
         item.setZ3VENCTO(vencimento);
         String contrato = rs.getString("contrato");
         String plano = rs.getString("plano");
         String descricao = rs.getString("descricao");
         if(descricao.contains("MULTA")){
             prefixo = "MUL";
             contrato = rs.getString("codigocontrato");
             item.setZ3EMISSAO(vencimento);
         }else if(tiposProdutos.contains("PM")){
             prefixo = plano.contains("BLUE") ? "BLU" : "SEL";
         }else if(tiposProdutos.contains("TD") || tiposProdutos.contains("MA")){
             prefixo = "ADE";
         }else if(tiposProdutos.contains("TA")){
             prefixo = "ANU";
         }else{
            prefixo = "AVA";
         }
         item.setZ3PRFORIG(prefixo);
         item.setZ3NUMORIG(contrato);
         
         String parcela;
         if(descricao.startsWith("PARCELA")){
             parcela = descricao.replaceFirst("PARCELA ", "").replaceAll("- MULTA E JUROS", "").trim();
         }else if(descricao.startsWith("ADESÃO PARCELA")){
             parcela = descricao.replaceFirst("ADESÃO PARCELA ", "").trim();
         }else{
             parcela = "1";
         }
         item.setZ3CLIEORI(rs.getString("codigoprotheus"));
         item.setZ3MATRICU(rs.getString("matricula"));
         String valorTaxa = Formatador.formatarValorMonetarioSemMoeda((rs.getDouble("valorparcela")*2.1)/100);
         item.setZ3VTXADMI(valorTaxa.replaceAll("\\,", "."));
         item.setZ3PARCORI(parcela);
         item.setZ3CNO(rs.getString("autorizacaocartao"));
         OperadorasExternasAprovaFacilEnum operadora = OperadorasExternasAprovaFacilEnum.valueOf(rs.getInt("operadoracartao"));
         if(operadora == null){
             if(rs.getString("tipoformapagamento").equals("BB")){
                 item.setZ3ADM("006");
             } else if((rs.getString("formapagamento").toLowerCase().contains("débito")
                    || rs.getString("formapagamento").toLowerCase().contains("debito")) && rs.getString("formapagamento").toLowerCase().contains("conta")){
                 item.setZ3ADM("007");
             }else{
                 item.setZ3ADM("999");
             }
         }else{
             item.setZ3ADM(operadora.getCodProtheus().toString());
         }

         item.setZ3NUMCONT(contrato);
         
         
         
         item.setZ3LOJAORI(rs.getString("codigolojaprotheus"));
         item.setZ3TIPOORI("PRV");
         
         String valor = Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("valorparcela"));
         item.setZ3VALOR(valor.replaceAll("\\,", "."));
         return item;
     }
    
}

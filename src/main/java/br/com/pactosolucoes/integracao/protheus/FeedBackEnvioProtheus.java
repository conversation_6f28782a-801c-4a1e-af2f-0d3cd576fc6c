/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import org.json.JSONObject;
import java.util.HashMap;
import java.util.Map;
import negocio.comuns.utilitarias.Uteis;
import servicos.util.ExecuteRequestHttpService;

/**
 *
 * <AUTHOR>
 */
public class FeedBackEnvioProtheus {
    
    public static void enviarFeedBack(EnvioProtheusJSON envio){
        try {
            Map<String, String> params = new HashMap<String, String>();
            params.put("jsonEnvio", envio.toJSON());
            System.out.println("Enviando feedback");
            String executeRequest = ExecuteRequestHttpService.executeRequest(PropriedadesIntegracaoProtheus.getPropertyValue("urlFeedback")+
                    "/prest/envioProtheus/inserir", params);
            System.out.println("Feedback enviado: "+executeRequest);
        } catch (Exception e) {
            Uteis.logar(e, FeedBackEnvioProtheus.class);
        }
    }

    public static void enviarFeedBackRetornoProcessamento(String chavedts, StatusEnvioEnum status, String msg){
        try {
            JSONObject json = new JSONObject();
            json.put("chavedts", chavedts);
            json.put("status", status.name());
            json.put("msg", msg);
            Map<String, String> params = new HashMap<String, String>();
            params.put("jsonEnvio", json.toString());
            System.out.println("Enviando feedback");
            String executeRequest = ExecuteRequestHttpService.executeRequest(PropriedadesIntegracaoProtheus.getPropertyValue("urlFeedback")
                    +"/prest/envioProtheus/alterar", params);
            System.out.println("Feedback enviado: "+executeRequest);
        } catch (Exception e) {
            Uteis.logar(e, FeedBackEnvioProtheus.class);
        }
    }
}

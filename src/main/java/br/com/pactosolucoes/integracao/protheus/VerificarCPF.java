package br.com.pactosolucoes.integracao.protheus;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.InputMismatchException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON> on 22/03/2017.
 */
public class VerificarCPF {

    public static void main(String... args) {
        try {

            Connection conOamd = DriverManager.getConnection("jdbc:postgresql://" + PropriedadesIntegracaoProtheus.getPropertyValue("hostOamd")
                            + ":" + PropriedadesIntegracaoProtheus.getPropertyValue("portaOamd")
                            + "/OAMD",
                    PropriedadesIntegracaoProtheus.getPropertyValue("userOamd"), PropriedadesIntegracaoProtheus.getPropertyValue("senhaOamd"));
            String whereChaves = "";
            if(args != null && args.length > 0){
                whereChaves = " WHERE chave in ('" + args[0] + "')";
            }else{
                String[] chaves = PropriedadesIntegracaoProtheus.getPropertyValue("chaves").split("\\,");
                if (chaves != null && chaves.length > 0) {
                    for (String ch : chaves) {
                        if(ch == null || ch.trim().isEmpty()){
                            continue;
                        }
                        whereChaves = (whereChaves.isEmpty() ? "'" : ",'") + ch + "'";
                    }
                    if(!whereChaves.isEmpty()){
                        whereChaves = " WHERE chave in (" + whereChaves + ")";
                    }

                }
            }
            ResultSet rsDadosCon = SuperFacadeJDBC.criarConsulta("SELECT * FROM empresa "+whereChaves+" ORDER by ordemprotheus ", conOamd);
            while (rsDadosCon.next()) {
                String chave = rsDadosCon.getString("nomeBD").replaceFirst("bdzillyon", "").toUpperCase();
                Connection con = DriverManager.getConnection("jdbc:postgresql://"
                        + rsDadosCon.getString("hostBD")
                        + ":" + rsDadosCon.getString("porta")
                        + "/" + rsDadosCon.getString("nomeBD"), rsDadosCon.getString("userBD"), rsDadosCon.getString("passwordBD"));

                ResultSet rsCliente = SuperFacadeJDBC.criarConsulta("SELECT cli.matricula, p.cfp, p.nome, e.nome as unidade FROM pessoa p "
                        + " inner join cliente cli on cli.pessoa = p.codigo "
                        + " inner join empresa e on cli.empresa = e.codigo " +
                        " where cli.codigoprotheus is null and cli.situacao IN ('AT', 'IN') ORDER BY p.nome", con);

                while(rsCliente.next()){
                    if(!isCPF(Uteis.removerMascara(rsCliente.getString("cfp")))){
                        System.out.println(rsCliente.getString("unidade")+";"+rsCliente.getString("matricula")+";"+rsCliente.getString("nome")+";"+rsCliente.getString("cfp")+";");
                    }
                }


            }

        } catch (Exception ex) {
            Logger.getLogger(IntegracaoProtheus.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static boolean isCPF(String CPF) {
        // considera-se erro CPF's formados por uma sequencia de numeros iguais
        if (CPF.equals("00000000000") || CPF.equals("11111111111") ||
                CPF.equals("22222222222") || CPF.equals("33333333333") ||
                CPF.equals("44444444444") || CPF.equals("55555555555") ||
                CPF.equals("66666666666") || CPF.equals("77777777777") ||
                CPF.equals("88888888888") || CPF.equals("99999999999") ||
                (CPF.length() != 11))
            return(false);

        char dig10, dig11;
        int sm, i, r, num, peso;

        // "try" - protege o codigo para eventuais erros de conversao de tipo (int)
        try {
            // Calculo do 1o. Digito Verificador
            sm = 0;
            peso = 10;
            for (i=0; i<9; i++) {
                // converte o i-esimo caractere do CPF em um numero:
                // por exemplo, transforma o caractere '0' no inteiro 0
                // (48 eh a posicao de '0' na tabela ASCII)
                num = (int)(CPF.charAt(i) - 48);
                sm = sm + (num * peso);
                peso = peso - 1;
            }

            r = 11 - (sm % 11);
            if ((r == 10) || (r == 11))
                dig10 = '0';
            else dig10 = (char)(r + 48); // converte no respectivo caractere numerico

            // Calculo do 2o. Digito Verificador
            sm = 0;
            peso = 11;
            for(i=0; i<10; i++) {
                num = (int)(CPF.charAt(i) - 48);
                sm = sm + (num * peso);
                peso = peso - 1;
            }

            r = 11 - (sm % 11);
            if ((r == 10) || (r == 11))
                dig11 = '0';
            else dig11 = (char)(r + 48);

            // Verifica se os digitos calculados conferem com os digitos informados.
            if ((dig10 == CPF.charAt(9)) && (dig11 == CPF.charAt(10)))
                return(true);
            else return(false);
        } catch (InputMismatchException erro) {
            return(false);
        }
    }
}

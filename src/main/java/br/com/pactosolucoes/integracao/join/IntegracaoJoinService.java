package br.com.pactosolucoes.integracao.join;

import negocio.comuns.crm.ConversaoLeadVO;
import negocio.comuns.utilitarias.Uteis;
import org.apache.commons.io.Charsets;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;

public class IntegracaoJoinService {

    public static String url() throws Exception {
        try {
            String result = ExecuteRequestHttpService.executeHttpRequest(PropsService.getPropertyValue("DISCOVERY_URL") + "/paths", null,
                    new HashMap<>(), ExecuteRequestHttpService.METODO_GET,
                    Charsets.UTF_8.name());
            return new JSONObject(result).getJSONObject("content").optString("join");
        }catch (Exception e){
            return "http://api.agenciajo.in/api/leads/Pacto";
        }

    }

    public void enviar(String key, ConversaoLeadVO conversaoleadVO, Integer step) throws Exception {
        String urlParameters = "name=" + conversaoleadVO.getLead().getPassivo().getNome() +
                "&telephone=" + conversaoleadVO.getLead().getPassivo().getTelefoneCelular() +
                "&email=" + conversaoleadVO.getLead().getEmail() +
                "&step=" + step +
                "&token=" + key;
        byte[] postData = urlParameters.getBytes( StandardCharsets.UTF_8 );
        int postDataLength = postData.length;

        String request = url() + "/" + conversaoleadVO.getLead().getCodigo();
        URL url = new URL( request );
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setDoOutput(true);
        conn.setInstanceFollowRedirects(false);
        conn.setRequestMethod("PUT");
        conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        conn.setRequestProperty("charset", "utf-8");
        conn.setRequestProperty("Content-Length", Integer.toString(postDataLength));
        conn.setUseCaches(false);
        try(DataOutputStream wr = new DataOutputStream(conn.getOutputStream())) {
            wr.write( postData );
        }

        // Pega a Resposta
        InputStream in = null;
        try{
            in = conn.getInputStream();
        }catch (IOException e){
            if(conn.getResponseCode() != 200){
                in = conn.getErrorStream();
            }
        }
        BufferedReader rd = new BufferedReader(new InputStreamReader(in, StandardCharsets.UTF_8));
        String line;
        StringBuilder resposta = new StringBuilder();
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta.append(line);
        }
        rd.close();
        System.out.println(resposta.toString());

    }

}

package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class InfoWeHelpPersonDTO extends SuperTO {

    private String internal_code;
    private String name;
    private String email;
    private String type;
    private String phone;
    private String date_of_birth;
    private String language;
    private String created_at;
    private String state;
    private String country;
    private String gender;
    private String document;
    private String company_unit;

    public InfoWeHelpPersonDTO() {

    }

    public InfoWeHelpPersonDTO(String key, UsuarioVO usuarioVO) {
        this.internal_code = key + "-" + usuarioVO.getCodigo();
        this.name = usuarioVO.getNomeSemAcentuacao();
        this.email = usuarioVO.getEmail();
        if (UteisValidacao.emptyString(this.email)) {
            this.email = usuarioVO.getColaboradorVO().getPessoa().getEmailCorrespondencia();
        }
        this.type = "CUSTOMER";
        this.phone = usuarioVO.getColaboradorVO().getPrimeiroTelefoneNaoNuloWeHelp();
        this.date_of_birth = Calendario.getDataAplicandoFormatacao(usuarioVO.getColaboradorVO().getPessoa().getDataNasc(), "yyyy-MM-dd");
        this.language = "PORTUGUESE";
        this.created_at = Calendario.getDataAplicandoFormatacao(usuarioVO.getColaboradorVO().getPessoa().getDataCadastro(), "yyyy-MM-dd");
        this.state = usuarioVO.getColaboradorVO().getPessoa().getEstadoVO().getSigla();
        this.country = usuarioVO.getColaboradorVO().getPessoa().getPais().getSiglaNome();
        this.gender = usuarioVO.getColaboradorVO().getPessoa().getSexo();
        this.document = "";
        this.company_unit = key + usuarioVO.getCodigo();
    }

    public String getInternal_code() {
        return internal_code;
    }

    public void setInternal_code(String internal_code) {
        this.internal_code = internal_code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getDate_of_birth() {
        return date_of_birth;
    }

    public void setDate_of_birth(String date_of_birth) {
        this.date_of_birth = date_of_birth;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getDocument() {
        return document;
    }

    public void setDocument(String document) {
        this.document = document;
    }

    public String getCompany_unit() {
        return company_unit;
    }

    public void setCompany_unit(String company_unit) {
        this.company_unit = company_unit;
    }

}

package br.com.pactosolucoes.integracao.pactopay.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class SftpDTO extends SuperTO {

    private String usersftp;
    private String pwdsftp;
    private String hostsftp;
    private Integer portsftp;
    private String diretorioDown;
    private String diretorioUp;

    public String getUsersftp() {
        return usersftp;
    }

    public void setUsersftp(String usersftp) {
        this.usersftp = usersftp;
    }

    public String getPwdsftp() {
        return pwdsftp;
    }

    public void setPwdsftp(String pwdsftp) {
        this.pwdsftp = pwdsftp;
    }

    public String getHostsftp() {
        return hostsftp;
    }

    public void setHostsftp(String hostsftp) {
        this.hostsftp = hostsftp;
    }

    public Integer getPortsftp() {
        return portsftp;
    }

    public void setPortsftp(Integer portsftp) {
        this.portsftp = portsftp;
    }

    public String getDiretorioDown() {
        return diretorioDown;
    }

    public void setDiretorioDown(String diretorioDown) {
        this.diretorioDown = diretorioDown;
    }

    public String getDiretorioUp() {
        return diretorioUp;
    }

    public void setDiretorioUp(String diretorioUp) {
        this.diretorioUp = diretorioUp;
    }
}

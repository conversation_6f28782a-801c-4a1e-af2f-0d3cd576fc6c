package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PactoPayConfigConvenioDTO extends SuperTO {

    private Integer codigo;
    private Integer posicao;
    private String descricao;
    private String tipo;
    private String tipo_cobranca;

    public PactoPayConfigConvenioDTO() {

    }

    public PactoPayConfigConvenioDTO(Integer posicao, ConvenioCobrancaVO obj) {
        this.posicao = posicao;
        this.codigo = obj.getCodigo();
        this.descricao = obj.getDescricao();
        this.tipo = obj.getTipo().getDescricao();
        this.tipo_cobranca = obj.getTipo().getTipoCobranca().getDescricao();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getPosicao() {
        return posicao;
    }

    public void setPosicao(Integer posicao) {
        this.posicao = posicao;
    }

    public String getTipo_cobranca() {
        return tipo_cobranca;
    }

    public void setTipo_cobranca(String tipo_cobranca) {
        this.tipo_cobranca = tipo_cobranca;
    }
}

package br.com.pactosolucoes.integracao.pactopay.front;

import br.com.pacto.priv.utils.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.MovParcelaVO;
import org.json.JSONObject;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ParcelaDTO extends SuperTO {

    private Integer codigo;
    private Integer cliente;
    private Integer pessoa;
    private String matricula;
    private String nome;
    private String descricao;
    private Integer nrTentativas;
    private String situacao;
    private String situacaoApresentar;
    private Double valorParcela;
    private Double valorTotal;
    private Double multa;
    private Double juros;
    private String vencimento;
    private Integer contrato;
    private String plano;
    private String empresa;
    private String inicioContrato;
    private String fimContrato;
    private boolean pendenteRetorno;
    private boolean seraCobradaHojeAutomaticamente;
    private boolean foiCobradaHojeManualmente;

    public ParcelaDTO() {

    }

    public ParcelaDTO(MovParcelaVO movParcelaVO, ClienteVO clienteVO) {
        this.codigo = movParcelaVO.getCodigo();
        this.descricao = movParcelaVO.getDescricao();
        this.nrTentativas = movParcelaVO.getNrTentativas();
        this.situacao = movParcelaVO.getSituacao();
        this.situacaoApresentar = movParcelaVO.getSituacao_Apresentar();
        this.valorParcela = Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela());
        this.multa = Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorMulta());
        this.juros = Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorJuros());
        this.valorTotal = Uteis.arredondarForcando2CasasDecimais(this.getValorParcela() + this.getMulta() + this.getJuros());
        this.vencimento = Uteis.getDataAplicandoFormatacao(movParcelaVO.getDataVencimento(), "dd/MM/yyyy");
        this.pessoa = movParcelaVO.getPessoa().getCodigo();
        this.nome = movParcelaVO.getPessoa().getNome();
        if (clienteVO != null) {
            this.cliente = clienteVO.getCodigo();
            this.matricula = clienteVO.getMatricula();
        }
        this.seraCobradaHojeAutomaticamente = movParcelaVO.isSeraCobradaHojeAutomaticamente();
        this.foiCobradaHojeManualmente = movParcelaVO.isFoiCobradaHojeManualmente();
    }


    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getVencimento() {
        return vencimento;
    }

    public void setVencimento(String vencimento) {
        this.vencimento = vencimento;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getNrTentativas() {
        return nrTentativas;
    }

    public void setNrTentativas(Integer nrTentativas) {
        this.nrTentativas = nrTentativas;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Double getMulta() {
        return multa;
    }

    public void setMulta(Double multa) {
        this.multa = multa;
    }

    public Double getJuros() {
        return juros;
    }

    public void setJuros(Double juros) {
        this.juros = juros;
    }

    public Double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getSituacaoApresentar() {
        return situacaoApresentar;
    }

    public void setSituacaoApresentar(String situacaoApresentar) {
        this.situacaoApresentar = situacaoApresentar;
    }

    public String getPlano() {
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getInicioContrato() {
        return inicioContrato;
    }

    public void setInicioContrato(String inicioContrato) {
        this.inicioContrato = inicioContrato;
    }

    public String getFimContrato() {
        return fimContrato;
    }

    public void setFimContrato(String fimContrato) {
        this.fimContrato = fimContrato;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public boolean isPendenteRetorno() {
        return pendenteRetorno;
    }

    public void setPendenteRetorno(boolean pendenteRetorno) {
        this.pendenteRetorno = pendenteRetorno;
    }

    public boolean isSeraCobradaHojeAutomaticamente() {
        return seraCobradaHojeAutomaticamente;
    }

    public void setSeraCobradaHojeAutomaticamente(boolean seraCobradaHojeAutomaticamente) {
        this.seraCobradaHojeAutomaticamente = seraCobradaHojeAutomaticamente;
    }

    public boolean isFoiCobradaHojeManualmente() {
        return foiCobradaHojeManualmente;
    }

    public void setFoiCobradaHojeManualmente(boolean foiCobradaHojeManualmente) {
        this.foiCobradaHojeManualmente = foiCobradaHojeManualmente;
    }

}

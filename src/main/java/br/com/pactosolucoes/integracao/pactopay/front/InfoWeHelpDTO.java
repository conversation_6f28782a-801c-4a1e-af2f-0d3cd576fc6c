package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.oamd.CustomerSuccessTO;
import negocio.oamd.RedeEmpresaVO;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class InfoWeHelpDTO extends SuperTO {

    private InfoWeHelpPersonDTO person;
    private List<InfoWeHelpCustomFieldsDTO> custom_fields;

    public InfoWeHelpDTO() {

    }

    public InfoWeHelpDTO(String key, UsuarioVO usuarioVO, EmpresaVO empresaVO,
                         RedeEmpresaVO redeEmpresaVO, CustomerSuccessTO customerSuccessTO) {
        this.person = new InfoWeHelpPersonDTO(key, usuarioVO);

        this.custom_fields = new ArrayList<>();
        InfoWeHelpCustomFieldsDTO tag_empresa = new InfoWeHelpCustomFieldsDTO();
        tag_empresa.setName("EMPRESA");
        tag_empresa.setValue(empresaVO.getNomeWeHelp());
        this.custom_fields.add(tag_empresa);

        InfoWeHelpCustomFieldsDTO tag_funcao = new InfoWeHelpCustomFieldsDTO();
        tag_funcao.setName("FUNÇÃO");
        tag_funcao.setValue(usuarioVO.getColaboradorVO().getPessoa().getFuncaoEnum() != null ? usuarioVO.getColaboradorVO().getPessoa().getFuncaoEnum().name() : "");
        this.custom_fields.add(tag_funcao);

        InfoWeHelpCustomFieldsDTO tag_cargo = new InfoWeHelpCustomFieldsDTO();
        tag_cargo.setName("CARGO");
        tag_cargo.setValue(usuarioVO.getColaboradorVO().getPessoa().getCargoEnum() != null ? usuarioVO.getColaboradorVO().getPessoa().getCargoEnum().name() : "");
        this.custom_fields.add(tag_cargo);

        InfoWeHelpCustomFieldsDTO tag_perfil = new InfoWeHelpCustomFieldsDTO();
        tag_perfil.setName("PERFIL DE ACESSO");
        UsuarioPerfilAcessoVO perfilAcessoVO = null;
        for (UsuarioPerfilAcessoVO perfilVO : usuarioVO.getUsuarioPerfilAcessoVOs()) {
            if (perfilVO.getEmpresa().getCodigo().equals(empresaVO.getCodigo())) {
                perfilAcessoVO = perfilVO;
                break;
            }
        }
        if (perfilAcessoVO != null) {
            tag_perfil.setValue(perfilAcessoVO.getPerfilAcesso().getTipoPerfilFromOrdinal());
            this.custom_fields.add(tag_perfil);
        }

        InfoWeHelpCustomFieldsDTO tag_chave = new InfoWeHelpCustomFieldsDTO();
        tag_chave.setName("CHAVE");
        tag_chave.setValue(key);
        this.custom_fields.add(tag_chave);

        InfoWeHelpCustomFieldsDTO tag_cod_empresa = new InfoWeHelpCustomFieldsDTO();
        tag_cod_empresa.setName("CODIGO DA EMPRESA");
        tag_cod_empresa.setValue(empresaVO.getCodigo().toString());
        this.custom_fields.add(tag_cod_empresa);

        InfoWeHelpCustomFieldsDTO tag_cidade = new InfoWeHelpCustomFieldsDTO();
        tag_cidade.setName("CIDADE");
        tag_cidade.setValue(empresaVO.getCidade_ApresentarSemAcentuacao());
        this.custom_fields.add(tag_cidade);

        InfoWeHelpCustomFieldsDTO tag_cod_finan = new InfoWeHelpCustomFieldsDTO();
        tag_cod_finan.setName("CODIGO DA EMPRESA FINANCEIRO");
        tag_cod_finan.setValue(empresaVO.getCodEmpresaFinanceiro().toString());
        this.custom_fields.add(tag_cod_finan);

        InfoWeHelpCustomFieldsDTO tag_rede = new InfoWeHelpCustomFieldsDTO();
        tag_rede.setName("REDE");
        tag_rede.setValue(redeEmpresaVO != null ? redeEmpresaVO.getNome() : "");
        this.custom_fields.add(tag_rede);

        InfoWeHelpCustomFieldsDTO tag_cs_responsavel = new InfoWeHelpCustomFieldsDTO();
        tag_cs_responsavel.setName("CS_RESPONSAVEL");
        tag_cs_responsavel.setValue(customerSuccessTO != null ? customerSuccessTO.getNome() : "");
        this.custom_fields.add(tag_cs_responsavel);
    }

    public InfoWeHelpPersonDTO getPerson() {
        return person;
    }

    public void setPerson(InfoWeHelpPersonDTO person) {
        this.person = person;
    }

    public List<InfoWeHelpCustomFieldsDTO> getCustom_fields() {
        return custom_fields;
    }

    public void setCustom_fields(List<InfoWeHelpCustomFieldsDTO> custom_fields) {
        this.custom_fields = custom_fields;
    }
}

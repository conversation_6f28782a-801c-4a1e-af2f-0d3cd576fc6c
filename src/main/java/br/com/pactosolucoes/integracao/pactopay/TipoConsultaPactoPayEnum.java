package br.com.pactosolucoes.integracao.pactopay;

public enum TipoConsultaPactoPayEnum {

    LISTA(1, "LISTA"),
    TOTALIZADOR(2, "TOTALIZADOR"),
    TOTALIZADOR_TIPO(3, "TOTALIZADOR_TIPO"),
    TOTALIZADOR_PARCELA(4, "TOTALIZADOR_PARCELA"),
    TOTALIZADOR_PARCELA_LISTA(5, "TOTALIZADOR_PARCELA_LISTA"),
    TOTALIZADOR_BANDEIRA(6, "TOTALIZADOR_BANDEIRA");

    private Integer codigo;
    private String descricao;

    TipoConsultaPactoPayEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoConsultaPactoPayEnum obterPorCodigo(Integer codigo) {
        for (TipoConsultaPactoPayEnum tipo : TipoConsultaPactoPayEnum.values()) {
            if (tipo.getCodigo().equals(codigo)) {
                return tipo;
            }
        }
        return null;
    }
}

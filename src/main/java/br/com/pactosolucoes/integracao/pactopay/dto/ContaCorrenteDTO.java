package br.com.pactosolucoes.integracao.pactopay.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContaCorrenteDTO extends SuperTO {

    private String agencia;
    private String agenciaDigitoVerificador;
    private String documento;
    private String tipoDocumento;
    private String contaCorrente;
    private String contaCorrenteDigitoVerificador;
    private String codigoBanco;
    private String operacao;

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getAgenciaDigitoVerificador() {
        return agenciaDigitoVerificador;
    }

    public void setAgenciaDigitoVerificador(String agenciaDigitoVerificador) {
        this.agenciaDigitoVerificador = agenciaDigitoVerificador;
    }

    public String getDocumento() {
        return documento;
    }

    public void setDocumento(String documento) {
        this.documento = documento;
    }

    public String getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(String tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public String getContaCorrente() {
        return contaCorrente;
    }

    public void setContaCorrente(String contaCorrente) {
        this.contaCorrente = contaCorrente;
    }

    public String getContaCorrenteDigitoVerificador() {
        return contaCorrenteDigitoVerificador;
    }

    public void setContaCorrenteDigitoVerificador(String contaCorrenteDigitoVerificador) {
        this.contaCorrenteDigitoVerificador = contaCorrenteDigitoVerificador;
    }

    public String getCodigoBanco() {
        return codigoBanco;
    }

    public void setCodigoBanco(String codigoBanco) {
        this.codigoBanco = codigoBanco;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }
}

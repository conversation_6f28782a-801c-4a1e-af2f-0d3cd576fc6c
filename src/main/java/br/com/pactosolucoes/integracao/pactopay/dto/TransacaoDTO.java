package br.com.pactosolucoes.integracao.pactopay.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransacaoDTO extends SuperTO {

    private String id;
    private Integer situacao;
    private Integer idReferencia;
    private String idTransacaoAdquirente;
    private ClienteDTO cliente;
    private String tokenAragorn;
    private String idCartaoAdquirente;
    private ContaCorrenteDTO contaCorrente;
    private Double valor;
    private boolean capturar = true;
    private boolean recorrente = false;
    private boolean parceladoLojista = false;
    private Integer nrParcelas;
    private String descricao;
    private String texto;
    private String instrucoes;
    private String instrucoesAdicional;
    private String vencimento;
    private String metadata;
    private String dataRegistro;
    private String codigoSeguranca;
    private String ipOrigem;
    private String softDescriptor;
    private String tipoParcelamento;
    private String gatewayTokenVindi;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getIdReferencia() {
        return idReferencia;
    }

    public void setIdReferencia(Integer idReferencia) {
        this.idReferencia = idReferencia;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public String getTokenAragorn() {
        return tokenAragorn;
    }

    public void setTokenAragorn(String tokenAragorn) {
        this.tokenAragorn = tokenAragorn;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getNrParcelas() {
        return nrParcelas;
    }

    public void setNrParcelas(Integer nrParcelas) {
        this.nrParcelas = nrParcelas;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public String getInstrucoes() {
        return instrucoes;
    }

    public void setInstrucoes(String instrucoes) {
        this.instrucoes = instrucoes;
    }

    public String getInstrucoesAdicional() {
        return instrucoesAdicional;
    }

    public void setInstrucoesAdicional(String instrucoesAdicional) {
        this.instrucoesAdicional = instrucoesAdicional;
    }

    public String getVencimento() {
        return vencimento;
    }

    public void setVencimento(String vencimento) {
        this.vencimento = vencimento;
    }

    public ContaCorrenteDTO getContaCorrente() {
        return contaCorrente;
    }

    public void setContaCorrente(ContaCorrenteDTO contaCorrente) {
        this.contaCorrente = contaCorrente;
    }

    public boolean isParceladoLojista() {
        return parceladoLojista;
    }

    public void setParceladoLojista(boolean parceladoLojista) {
        this.parceladoLojista = parceladoLojista;
    }

    public Integer getSituacao() {
        return situacao;
    }

    public void setSituacao(Integer situacao) {
        this.situacao = situacao;
    }

    public String getCodigoSeguranca() {
        return codigoSeguranca;
    }

    public void setCodigoSeguranca(String codigoSeguranca) {
        this.codigoSeguranca = codigoSeguranca;
    }

    public boolean isCapturar() {
        return capturar;
    }

    public void setCapturar(boolean capturar) {
        this.capturar = capturar;
    }

    public String getIdTransacaoAdquirente() {
        return idTransacaoAdquirente;
    }

    public void setIdTransacaoAdquirente(String idTransacaoAdquirente) {
        this.idTransacaoAdquirente = idTransacaoAdquirente;
    }

    public String getIdCartaoAdquirente() {
        return idCartaoAdquirente;
    }

    public void setIdCartaoAdquirente(String idCartaoAdquirente) {
        this.idCartaoAdquirente = idCartaoAdquirente;
    }

    public String getIpOrigem() {
        return ipOrigem;
    }

    public void setIpOrigem(String ipOrigem) {
        this.ipOrigem = ipOrigem;
    }

    public String getSoftDescriptor() {
        return softDescriptor;
    }

    public void setSoftDescriptor(String softDescriptor) {
        this.softDescriptor = softDescriptor;
    }

    public boolean isRecorrente() {
        return recorrente;
    }

    public void setRecorrente(boolean recorrente) {
        this.recorrente = recorrente;
    }

    public String getTipoParcelamento() {
        return tipoParcelamento;
    }

    public void setTipoParcelamento(String tipoParcelamento) {
        this.tipoParcelamento = tipoParcelamento;
    }

    public String getGatewayTokenVindi() {
        return gatewayTokenVindi;
    }

    public void setGatewayTokenVindi(String gatewayTokenVindi) {
        this.gatewayTokenVindi = gatewayTokenVindi;
    }
}

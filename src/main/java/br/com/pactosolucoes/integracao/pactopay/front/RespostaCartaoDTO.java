package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class RespostaCartaoDTO extends SuperTO {

    private Integer codigo;
    private boolean sucesso = false;
    private String mensagem;
    private Boolean apresentarConfirmar;
    private Boolean permiteAdicionar;


    public RespostaCartaoDTO() {

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Boolean getApresentarConfirmar() {
        return apresentarConfirmar;
    }

    public void setApresentarConfirmar(Boolean apresentarConfirmar) {
        this.apresentarConfirmar = apresentarConfirmar;
    }

    public Boolean getPermiteAdicionar() {
        return permiteAdicionar;
    }

    public void setPermiteAdicionar(Boolean permiteAdicionar) {
        this.permiteAdicionar = permiteAdicionar;
    }
}

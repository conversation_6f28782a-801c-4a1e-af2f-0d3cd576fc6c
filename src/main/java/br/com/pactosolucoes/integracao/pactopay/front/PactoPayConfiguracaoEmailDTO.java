package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.enumerador.TagReguaCobrancaPactoPayEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PactoPayConfiguracaoEmailDTO extends SuperTO {

    private boolean wagi = false;
    private String wagiEmail;
    private String remetente;
    private String smtpEmail;
    private String smtpLogin;
    private String smtpSenha;
    private boolean smtpConexaoSegura = false;
    private String smtpServer;
    private boolean usaSMTPS = false;
    private boolean iniciarTLS = false;
    private Integer smtpPorta;
    private String remetenteSMS;
    private String appKeyWagiCriado;
    private String cssCorEmail;
    private String cssCorBtnPagar;
    private String cssCorBtnComprovante;
    private String cssCorBtnCadastrar;

    public PactoPayConfiguracaoEmailDTO() {

    }

    public PactoPayConfiguracaoEmailDTO(String dados, boolean carregarDadosSMS) {
        try {
            JSONObject json = new JSONObject(dados);
            this.wagi = json.optBoolean("wagi");
            this.wagiEmail = json.optString("wagiEmail");
            this.remetente = json.optString("remetente");
            this.smtpEmail = json.optString("smtpEmail");
            this.smtpLogin = json.optString("smtpLogin");
            this.smtpSenha = json.optString("smtpSenha");
            this.smtpConexaoSegura = json.optBoolean("smtpConexaoSegura");
            this.smtpServer = json.optString("smtpServer");
            this.usaSMTPS = json.optBoolean("usaSMTPS");
            this.iniciarTLS = json.optBoolean("iniciarTLS");
            this.smtpPorta = json.optInt("smtpPorta");
            if (carregarDadosSMS) {
                this.remetenteSMS = json.optString("remetenteSMS");
                this.appKeyWagiCriado = json.optString("appKeyWagiCriado");
            }
            this.cssCorEmail = json.optString("cssCorEmail");
            this.cssCorBtnPagar = json.optString("cssCorBtnPagar");
            this.cssCorBtnComprovante = json.optString("cssCorBtnComprovante");
            this.cssCorBtnCadastrar = json.optString("cssCorBtnCadastrar");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String getRemetente() {
        return remetente;
    }

    public void setRemetente(String remetente) {
        this.remetente = remetente;
    }

    public String getSmtpEmail() {
        return smtpEmail;
    }

    public void setSmtpEmail(String smtpEmail) {
        this.smtpEmail = smtpEmail;
    }

    public String getSmtpLogin() {
        return smtpLogin;
    }

    public void setSmtpLogin(String smtpLogin) {
        this.smtpLogin = smtpLogin;
    }

    public String getSmtpSenha() {
        return smtpSenha;
    }

    public void setSmtpSenha(String smtpSenha) {
        this.smtpSenha = smtpSenha;
    }

    public boolean isIniciarTLS() {
        return iniciarTLS;
    }

    public void setIniciarTLS(boolean iniciarTLS) {
        this.iniciarTLS = iniciarTLS;
    }

    public Integer getSmtpPorta() {
        return smtpPorta;
    }

    public void setSmtpPorta(Integer smtpPorta) {
        this.smtpPorta = smtpPorta;
    }

    public boolean isWagi() {
        return wagi;
    }

    public void setWagi(boolean wagi) {
        this.wagi = wagi;
    }

    public boolean isSmtpConexaoSegura() {
        return smtpConexaoSegura;
    }

    public void setSmtpConexaoSegura(boolean smtpConexaoSegura) {
        this.smtpConexaoSegura = smtpConexaoSegura;
    }

    public String getSmtpServer() {
        return smtpServer;
    }

    public void setSmtpServer(String smtpServer) {
        this.smtpServer = smtpServer;
    }

    public boolean isUsaSMTPS() {
        return usaSMTPS;
    }

    public void setUsaSMTPS(boolean usaSMTPS) {
        this.usaSMTPS = usaSMTPS;
    }

    public String getWagiEmail() {
        return wagiEmail;
    }

    public void setWagiEmail(String wagiEmail) {
        this.wagiEmail = wagiEmail;
    }

    public String toString() {
        return new JSONObject(this).toString();
    }

    public ConfiguracaoSistemaCRMVO obterConfigCRMVO() {
        ConfiguracaoSistemaCRMVO configCRMVO = new ConfiguracaoSistemaCRMVO();
        configCRMVO.setIntegracaoPacto(this.isWagi());
        configCRMVO.setLogin(this.getSmtpLogin());
        configCRMVO.setEmailPadrao(this.getSmtpEmail());
        configCRMVO.setSenha(this.getSmtpSenha());
        configCRMVO.setConexaoSegura(this.isSmtpConexaoSegura());
        configCRMVO.setPortaServer(this.getSmtpPorta().toString());
        configCRMVO.setIniciarTLS(this.isIniciarTLS());
        configCRMVO.setMailServer(this.getSmtpServer());
        configCRMVO.setUsaSMTPS(this.isUsaSMTPS());
        configCRMVO.setRemetentePadrao(this.getRemetente());

//        configCRMVO.setIntegracaoPacto(false);
//        configCRMVO.setLogin("<EMAIL>");
//        configCRMVO.setEmailPadrao("<EMAIL>");
//        configCRMVO.setSenha("**************************************************");
//        configCRMVO.setConexaoSegura(true);
//        configCRMVO.setPortaServer("587");
//        configCRMVO.setIniciarTLS(false);
//        configCRMVO.setMailServer("smtp.mailgun.org");
//        configCRMVO.setUsaSMTPS(false);
//        configCRMVO.setRemetentePadrao("LUIZ FELIPE REGUA");
        return configCRMVO;
    }

    public boolean isConfiguracaoEmailValida() {
        return (!this.isWagi() &&
                !UteisValidacao.emptyString(this.getSmtpLogin()) &&
                !UteisValidacao.emptyString(this.getSmtpSenha()) &&
                !UteisValidacao.emptyString(this.getSmtpEmail()) &&
                !UteisValidacao.emptyString(this.getRemetente()) &&
                !UteisValidacao.emptyString(this.getSmtpServer()))
                || (this.isWagi() &&
                !UteisValidacao.emptyString(this.getWagiEmail()) &&
                !UteisValidacao.emptyString(this.getRemetente()));
    }

    public String getRemetenteSMS() {
        if (remetenteSMS == null) {
            remetenteSMS = "";
        }
        return remetenteSMS;
    }

    public void setRemetenteSMS(String remetenteSMS) {
        this.remetenteSMS = remetenteSMS;
    }

    public String getAppKeyWagiCriado() {
        if (appKeyWagiCriado == null) {
            appKeyWagiCriado = "";
        }
        return appKeyWagiCriado;
    }

    public void setAppKeyWagiCriado(String appKeyWagiCriado) {
        this.appKeyWagiCriado = appKeyWagiCriado;
    }

    public boolean isEmailWagiCriado() {
        return !UteisValidacao.emptyString(this.getAppKeyWagiCriado());
    }

    public String getCssCorEmail() {
        if (cssCorEmail == null) {
            cssCorEmail = TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_COLOR.getPadrao();
        }
        return cssCorEmail;
    }

    public void setCssCorEmail(String cssCorEmail) {
        this.cssCorEmail = cssCorEmail;
    }

    public String getCssCorBtnPagar() {
        if (cssCorBtnPagar == null) {
            cssCorBtnPagar = TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_BTN_PAGAR_COLOR.getPadrao();
        }
        return cssCorBtnPagar;
    }

    public void setCssCorBtnPagar(String cssCorBtnPagar) {
        this.cssCorBtnPagar = cssCorBtnPagar;
    }

    public String getCssCorBtnComprovante() {
        if (cssCorBtnComprovante == null) {
            cssCorBtnComprovante = TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_BTN_COMPROVANTE_COLOR.getPadrao();
        }
        return cssCorBtnComprovante;
    }

    public void setCssCorBtnComprovante(String cssCorBtnComprovante) {
        this.cssCorBtnComprovante = cssCorBtnComprovante;
    }

    public String getCssCorBtnCadastrar() {
        if (cssCorBtnCadastrar == null) {
            cssCorBtnCadastrar = TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_BTN_CADASTRAR_COLOR.getPadrao();
        }
        return cssCorBtnCadastrar;
    }

    public void setCssCorBtnCadastrar(String cssCorBtnCadastrar) {
        this.cssCorBtnCadastrar = cssCorBtnCadastrar;
    }
}

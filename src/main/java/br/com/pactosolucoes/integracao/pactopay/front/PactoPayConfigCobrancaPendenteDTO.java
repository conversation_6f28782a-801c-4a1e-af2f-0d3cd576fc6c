package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PactoPayConfigCobrancaPendenteDTO extends SuperTO {

    private boolean ativo = false;
    private boolean sms = false;
    private boolean email = false;
    private boolean whatsapp = false;
    private boolean app = false;
    private boolean gymbotpro = false;
    private boolean com_autorizacao = false;
    private Integer dias_vencido_minimo;
    private Integer dias_vencido_maximo;
    private Integer intervalo_dias;
    private Integer quantidade_maxima_envios;

    public PactoPayConfigCobrancaPendenteDTO() {

    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public boolean isSms() {
        return sms;
    }

    public void setSms(boolean sms) {
        this.sms = sms;
    }

    public boolean isEmail() {
        return email;
    }

    public void setEmail(boolean email) {
        this.email = email;
    }

    public Integer getDias_vencido_minimo() {
        return dias_vencido_minimo;
    }

    public void setDias_vencido_minimo(Integer dias_vencido_minimo) {
        this.dias_vencido_minimo = dias_vencido_minimo;
    }

    public Integer getDias_vencido_maximo() {
        return dias_vencido_maximo;
    }

    public void setDias_vencido_maximo(Integer dias_vencido_maximo) {
        this.dias_vencido_maximo = dias_vencido_maximo;
    }

    public Integer getIntervalo_dias() {
        return intervalo_dias;
    }

    public void setIntervalo_dias(Integer intervalo_dias) {
        this.intervalo_dias = intervalo_dias;
    }

    public Integer getQuantidade_maxima_envios() {
        return quantidade_maxima_envios;
    }

    public void setQuantidade_maxima_envios(Integer quantidade_maxima_envios) {
        this.quantidade_maxima_envios = quantidade_maxima_envios;
    }

    public boolean isCom_autorizacao() {
        return com_autorizacao;
    }

    public void setCom_autorizacao(boolean com_autorizacao) {
        this.com_autorizacao = com_autorizacao;
    }

    public boolean isWhatsapp() {
        return whatsapp;
    }

    public void setWhatsapp(boolean whatsapp) {
        this.whatsapp = whatsapp;
    }

    public boolean isApp() {
        return app;
    }

    public void setApp(boolean app) {
        this.app = app;
    }

    public boolean isGymbotpro() {
        return gymbotpro;
    }

    public void setGymbotpro(boolean gymbotpro) {
        this.gymbotpro = gymbotpro;
    }
}

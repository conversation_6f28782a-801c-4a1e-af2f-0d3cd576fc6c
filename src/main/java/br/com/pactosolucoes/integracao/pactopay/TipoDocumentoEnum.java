package br.com.pactosolucoes.integracao.pactopay;

public enum TipoDocumentoEnum {

    CPF(1, "CPF"),
    CNPJ(2, "CNPJ");

    private Integer codigo;
    private String descricao;

    TipoDocumentoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoDocumentoEnum obterPorCodigo(Integer codigo) {
        for (TipoDocumentoEnum tipo : TipoDocumentoEnum.values()) {
            if (tipo.getCodigo().equals(codigo)) {
                return tipo;
            }
        }
        return null;
    }
}

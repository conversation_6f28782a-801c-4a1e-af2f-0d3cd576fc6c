package br.com.pactosolucoes.integracao.pactopay;

public enum TipoConsultaEnum {

    QUANTIDADE(1, "QUANTIDADE"),
    TOTALIZADOR(2, "TOTALIZADOR"),
    LISTA(3, "LISTA"),
    GRAFICO(4, "GRAFICO");

    private Integer codigo;
    private String descricao;

    TipoConsultaEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoConsultaEnum obterPorCodigo(Integer codigo) {
        for (TipoConsultaEnum tipo : TipoConsultaEnum.values()) {
            if (tipo.getCodigo().equals(codigo)) {
                return tipo;
            }
        }
        return null;
    }
}

package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import javax.servlet.ServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class FiltroPactoPayDTO extends SuperTO {

    private String inicio;
    private String fim;
    private String parametro;
    private List<Integer> empresas;
    private List<Integer> convenios;
    private List<Integer> situacoes;
    private List<Integer> origens;
    private List<Integer> usuarios;
    private boolean apresentarVerificacao = false;
    private Integer codigoTransacao;

    public FiltroPactoPayDTO() {
    }

    public FiltroPactoPayDTO(ServletRequest request) {

        //se tiver no filters pega tudo do filters
        if (!UteisValidacao.emptyString(request.getParameter("filters"))) {

            JSONObject jsonFilters = new JSONObject(request.getParameter("filters"));
            this.parametro = jsonFilters.optString("quicksearchValue").toUpperCase();
            this.inicio = jsonFilters.optString("inicio");
            this.fim = jsonFilters.optString("fim");

            try {
                String empresas = jsonFilters.optString("empresas");
                this.empresas = new ArrayList<>();
                for (String emp : empresas.split(",")) {
                    try {
                        this.empresas.add(Integer.parseInt(emp));
                    } catch (Exception ignored) {
                    }
                }
            } catch (Exception ignored) {
            }

            try {
                String convenios = jsonFilters.optString("convenios");
                this.convenios = new ArrayList<>();
                for (String conv : convenios.split(",")) {
                    try {
                        this.convenios.add(Integer.parseInt(conv));
                    } catch (Exception ignored) {
                    }
                }
            } catch (Exception ignored) {
            }

            try {
                String situacao = jsonFilters.optString("situacoes");
                this.situacoes = new ArrayList<>();
                for (String emp : situacao.split(",")) {
                    try {
                        this.situacoes.add(Integer.parseInt(emp));
                    } catch (Exception ignored) {
                    }
                }
            } catch (Exception ignored) {
            }

            try {
                String origem = jsonFilters.optString("origens");
                this.origens = new ArrayList<>();
                for (String emp : origem.split(",")) {
                    try {
                        this.origens.add(Integer.parseInt(emp));
                    } catch (Exception ignored) {
                    }
                }
            } catch (Exception ignored) {
            }

            try {
                String usuario = jsonFilters.optString("usuarios");
                this.usuarios = new ArrayList<>();
                for (String emp : usuario.split(",")) {
                    try {
                        this.usuarios.add(Integer.parseInt(emp));
                    } catch (Exception ignored) {
                    }
                }
            } catch (Exception ignored) {
            }

            try {
                if (jsonFilters.has("apresentarVerificacao")) {
                    this.apresentarVerificacao = jsonFilters.getBoolean("apresentarVerificacao");
                }
            } catch (Exception ignored) {
            }

        } else {
            //se não tiver então pega como parametro

            try {
                if (request.getParameter("filters") != null) {
                    JSONObject json = new JSONObject(request.getParameter("filters"));
                    this.parametro = json.optString("quicksearchValue").toUpperCase();
                }
            } catch (Exception ignored) {
            }

            try {
                this.inicio = request.getParameter("inicio");
            } catch (Exception ignored) {
            }
            try {
                this.fim = request.getParameter("fim");
            } catch (Exception ignored) {
            }

            try {
                String empresas = request.getParameter("empresas");
                this.empresas = new ArrayList<>();
                for (String emp : empresas.split(",")) {
                    try {
                        this.empresas.add(Integer.parseInt(emp));
                    } catch (Exception ignored) {
                    }
                }
            } catch (Exception ignored) {
            }

            try {
                String convenios = request.getParameter("convenios");
                this.convenios = new ArrayList<>();
                for (String conv : convenios.split(",")) {
                    try {
                        this.convenios.add(Integer.parseInt(conv));
                    } catch (Exception ignored) {
                    }
                }
            } catch (Exception ignored) {
            }

            try {
                String situacao = request.getParameter("situacoes");
                this.situacoes = new ArrayList<>();
                for (String emp : situacao.split(",")) {
                    try {
                        this.situacoes.add(Integer.parseInt(emp));
                    } catch (Exception ignored) {
                    }
                }
            } catch (Exception ignored) {
            }

            try {
                String usuario = request.getParameter("usuarios");
                this.usuarios = new ArrayList<>();
                for (String emp : usuario.split(",")) {
                    try {
                        this.usuarios.add(Integer.parseInt(emp));
                    } catch (Exception ignored) {
                    }
                }
            } catch (Exception ignored) {
            }

            try {
                if (request.getParameter("apresentarVerificacao") != null) {
                    this.apresentarVerificacao = Boolean.parseBoolean(request.getParameter("apresentarVerificacao"));
                }
            } catch (Exception ignored) {
            }
        }
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public List<Integer> getEmpresas() {
        if (empresas == null) {
            empresas = new ArrayList<>();
        }
        return empresas;
    }

    public void setEmpresas(List<Integer> empresas) {
        this.empresas = empresas;
    }

    public List<Integer> getConvenios() {
        if (convenios == null) {
            convenios = new ArrayList<>();
        }
        return convenios;
    }

    public void setConvenios(List<Integer> convenios) {
        this.convenios = convenios;
    }

    public String getEmpresasString() {
        StringBuilder list = new StringBuilder();
        for (Integer emp : this.getEmpresas()) {
            if (list.length() > 0) {
                list.append(",");
            }
            list.append(emp);
        }
        return list.toString();
    }

    public String getConveniosString() {
        StringBuilder list = new StringBuilder();
        for (Integer emp : this.getConvenios()) {
            if (list.length() > 0) {
                list.append(",");
            }
            list.append(emp);
        }
        return list.toString();
    }

    public Date getInicioDate() {
        try {
            return Calendario.getDate("yyyyMMdd", this.getInicio());
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public Date getFimDate() {
        try {
            return Calendario.getDate("yyyyMMdd", this.getFim());
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public void validarDatas() throws Exception {
        if (this.getInicioDate() == null) {
            throw new Exception("Data inicio inválida");
        }
        if (this.getFimDate() == null) {
            throw new Exception("Data fim inválida");
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public boolean isApresentarVerificacao() {
        return apresentarVerificacao;
    }

    public void setApresentarVerificacao(boolean apresentarVerificacao) {
        this.apresentarVerificacao = apresentarVerificacao;
    }

    public List<Integer> getSituacoes() {
        if (situacoes == null) {
            situacoes = new ArrayList<>();
        }
        return situacoes;
    }

    public void setSituacoes(List<Integer> situacoes) {
        this.situacoes = situacoes;
    }

    public String getSituacoesString() {
        StringBuilder list = new StringBuilder();
        for (Integer emp : this.getSituacoes()) {
            if (list.length() > 0) {
                list.append(",");
            }
            list.append(emp);
        }
        return list.toString();
    }

    public List<Integer> getOrigens() {
        if (origens == null) {
            origens = new ArrayList<>();
        }
        return origens;
    }

    public void setOrigens(List<Integer> origens) {
        this.origens = origens;
    }

    public String getOrigensString() {
        StringBuilder list = new StringBuilder();
        for (Integer emp : this.getOrigens()) {
            if (list.length() > 0) {
                list.append(",");
            }
            list.append(emp);
        }
        return list.toString();
    }

    public Integer getCodigoTransacao() {
        return codigoTransacao;
    }

    public void setCodigoTransacao(Integer codigoTransacao) {
        this.codigoTransacao = codigoTransacao;
    }

    public List<Integer> getUsuarios() {
        if (usuarios == null) {
            usuarios = new ArrayList<>();
        }
        return usuarios;
    }

    public void setUsuarios(List<Integer> usuarios) {
        this.usuarios = usuarios;
    }

}

package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.utilitarias.Uteis;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CobrancaBoletoDTO extends SuperTO {

    private Integer remessaItem;
    private Integer remessa;
    private String convenio;
    private String codigoRetorno;
    private String descricaoRetorno;
    private String vencimento;
    private String usuario;
    private Double valor;
    private Integer recibo;

    public CobrancaBoletoDTO() {
    }

    public CobrancaBoletoDTO(RemessaItemVO obj) {
        this.remessaItem = obj.getCodigo();
        this.remessa = obj.getRemessa().getCodigo();
        this.convenio = obj.getRemessa().getConvenioCobranca().getDescricao();
        this.usuario = obj.getRemessa().getUsuario().getNome();
        this.codigoRetorno = obj.getCodigoStatus();
        this.descricaoRetorno = obj.getDescricaoStatus();
        this.vencimento = Uteis.getData(obj.getDataVencimentoBoleto());
        this.valor = obj.getValorItemRemessa();
        this.recibo = obj.getReciboPagamentoVO().getCodigo();
    }

    public Integer getRemessaItem() {
        return remessaItem;
    }

    public void setRemessaItem(Integer remessaItem) {
        this.remessaItem = remessaItem;
    }

    public Integer getRemessa() {
        return remessa;
    }

    public void setRemessa(Integer remessa) {
        this.remessa = remessa;
    }

    public String getConvenio() {
        return convenio;
    }

    public void setConvenio(String convenio) {
        this.convenio = convenio;
    }

    public String getCodigoRetorno() {
        return codigoRetorno;
    }

    public void setCodigoRetorno(String codigoRetorno) {
        this.codigoRetorno = codigoRetorno;
    }

    public String getDescricaoRetorno() {
        return descricaoRetorno;
    }

    public void setDescricaoRetorno(String descricaoRetorno) {
        this.descricaoRetorno = descricaoRetorno;
    }

    public String getVencimento() {
        return vencimento;
    }

    public void setVencimento(String vencimento) {
        this.vencimento = vencimento;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getRecibo() {
        return recibo;
    }

    public void setRecibo(Integer recibo) {
        this.recibo = recibo;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }
}

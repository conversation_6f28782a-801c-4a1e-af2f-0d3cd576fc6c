package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BandeiraCartaoDTO extends SuperTO {

    private boolean valido = false;
    private String mensagem;
    private String card_mask;
    private String bandeira;


    public BandeiraCartaoDTO() {

    }

    public boolean isValido() {
        return valido;
    }

    public void setValido(boolean valido) {
        this.valido = valido;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getCard_mask() {
        return card_mask;
    }

    public void setCard_mask(String card_mask) {
        this.card_mask = card_mask;
    }

    public String getBandeira() {
        return bandeira;
    }

    public void setBandeira(String bandeira) {
        this.bandeira = bandeira;
    }
}

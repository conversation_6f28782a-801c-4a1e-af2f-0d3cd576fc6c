package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConvenioCobrancaDTO extends SuperTO {

    private Integer codigo;
    private String descricao;
    private String situacao;
    private List<Integer> empresas;
    private String tipo_convenio_descricao;
    private String tipo_cobranca_descricao;
    private Integer tipoConvenioCodigo;

    public ConvenioCobrancaDTO() {

    }

    public ConvenioCobrancaDTO(ConvenioCobrancaVO obj) {
        this.codigo = obj.getCodigo();
        this.descricao = obj.getDescricao();
        this.situacao = obj.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) ? "ATIVO" : "INATIVO";
        this.tipo_convenio_descricao = obj.getTipo().getDescricao();
        this.tipo_cobranca_descricao = obj.getTipo().getTipoCobranca().getDescricao();
        this.tipoConvenioCodigo = obj.getTipo().getCodigo();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public List<Integer> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<Integer> empresas) {
        this.empresas = empresas;
    }

    public String getTipo_convenio_descricao() {
        return tipo_convenio_descricao;
    }

    public void setTipo_convenio_descricao(String tipo_convenio_descricao) {
        this.tipo_convenio_descricao = tipo_convenio_descricao;
    }

    public String getTipo_cobranca_descricao() {
        return tipo_cobranca_descricao;
    }

    public void setTipo_cobranca_descricao(String tipo_cobranca_descricao) {
        this.tipo_cobranca_descricao = tipo_cobranca_descricao;
    }

    public Integer getTipoConvenioCodigo() {
        return tipoConvenioCodigo;
    }

    public void setTipoConvenioCodigo(Integer tipoConvenioCodigo) {
        this.tipoConvenioCodigo = tipoConvenioCodigo;
    }
}

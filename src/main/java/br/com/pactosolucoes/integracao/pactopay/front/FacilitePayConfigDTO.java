package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class FacilitePayConfigDTO extends SuperTO {

    private Boolean conciliacaoContasPagar;
    private Boolean reguaCobranca;
    private Boolean reguaCobrancaConfiguracaoEmail;

    public FacilitePayConfigDTO(EmpresaVO empresaVO, UsuarioVO usuarioVO) {
        this.conciliacaoContasPagar = empresaVO.isConcContasPagarFacilitePay();
        this.reguaCobranca = empresaVO.isFacilitePayReguaCobranca();
        this.reguaCobrancaConfiguracaoEmail = (this.reguaCobranca && usuarioVO.getUsuarioPactoSolucoes());
    }
    public FacilitePayConfigDTO() {

    }

    public Boolean getConciliacaoContasPagar() {
        return conciliacaoContasPagar;
    }

    public void setConciliacaoContasPagar(Boolean conciliacaoContasPagar) {
        this.conciliacaoContasPagar = conciliacaoContasPagar;
    }

    public Boolean getReguaCobranca() {
        return reguaCobranca;
    }

    public void setReguaCobranca(Boolean reguaCobranca) {
        this.reguaCobranca = reguaCobranca;
    }

    public Boolean getReguaCobrancaConfiguracaoEmail() {
        return reguaCobrancaConfiguracaoEmail;
    }

    public void setReguaCobrancaConfiguracaoEmail(Boolean reguaCobrancaConfiguracaoEmail) {
        this.reguaCobrancaConfiguracaoEmail = reguaCobrancaConfiguracaoEmail;
    }
}

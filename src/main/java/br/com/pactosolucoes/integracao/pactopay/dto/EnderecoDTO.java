package br.com.pactosolucoes.integracao.pactopay.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.microsservice.integracoes.EnderecoCDLTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class EnderecoDTO extends SuperTO {

    private String cep;
    private String endereco;
    private String numero;
    private String complemento;
    private String bairro;
    private String cidade;
    private String uf;
    private String pais;

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public EnderecoCDLTO toEnderecoCDLTO() {
        EnderecoCDLTO enderecoCDL = new EnderecoCDLTO();

        if (!UteisValidacao.emptyString(cep)) {
            enderecoCDL.setCep(cep);
        }
        if (!UteisValidacao.emptyString(endereco)) {
            enderecoCDL.setLogradouro(endereco);
        }
        if (!UteisValidacao.emptyString(numero)) {
            enderecoCDL.setNumero(numero);
        }
        if (!UteisValidacao.emptyString(complemento)) {
            enderecoCDL.setComplemento(complemento);
        }
        if (!UteisValidacao.emptyString(bairro)) {
            enderecoCDL.setBairro(bairro);
        }
        if (!UteisValidacao.emptyString(cidade)) {
//            enderecoCDL.setCidade();
        }
        return enderecoCDL;
    }
}

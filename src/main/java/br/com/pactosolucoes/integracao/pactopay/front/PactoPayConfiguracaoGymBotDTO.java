package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import org.json.JSONObject;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PactoPayConfiguracaoGymBotDTO extends SuperTO {

    private String urlCobrancaAntecipada;
    private String urlCobrancaPendente;
    private String urlCobrancaCartao;
    private String urlCobrancaResultadoCobranca;

    public PactoPayConfiguracaoGymBotDTO() {

    }

    public PactoPayConfiguracaoGymBotDTO(String dados) {
        try {
            JSONObject json = new JSONObject(dados);
            this.urlCobrancaAntecipada = json.optString("urlCobrancaAntecipada");
            this.urlCobrancaPendente = json.optString("urlCobrancaPendente");
            this.urlCobrancaCartao = json.optString("urlCobrancaCartao");
            this.urlCobrancaResultadoCobranca = json.optString("urlCobrancaResultadoCobranca");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String getUrlCobrancaAntecipada() {
        return urlCobrancaAntecipada;
    }

    public void setUrlCobrancaAntecipada(String urlCobrancaAntecipada) {
        this.urlCobrancaAntecipada = urlCobrancaAntecipada;
    }

    public String getUrlCobrancaPendente() {
        return urlCobrancaPendente;
    }

    public void setUrlCobrancaPendente(String urlCobrancaPendente) {
        this.urlCobrancaPendente = urlCobrancaPendente;
    }

    public String getUrlCobrancaCartao() {
        return urlCobrancaCartao;
    }

    public void setUrlCobrancaCartao(String urlCobrancaCartao) {
        this.urlCobrancaCartao = urlCobrancaCartao;
    }

    public String toString() {
        return new JSONObject(this).toString();
    }

    public String getUrlCobrancaResultadoCobranca() {
        return urlCobrancaResultadoCobranca;
    }

    public void setUrlCobrancaResultadoCobranca(String urlCobrancaResultadoCobranca) {
        this.urlCobrancaResultadoCobranca = urlCobrancaResultadoCobranca;
    }
}

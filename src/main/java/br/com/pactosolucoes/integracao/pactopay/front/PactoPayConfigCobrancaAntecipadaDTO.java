package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PactoPayConfigCobrancaAntecipadaDTO extends SuperTO {

    private boolean ativo = false;
    private boolean sms = false;
    private boolean email = false;
    private boolean whatsapp = false;
    private boolean app = false;
    private boolean gymbotpro = false;
    private boolean com_autorizacao = false;
    private boolean aplicar_desconto = false;
    private Double desconto;
    private boolean valor_fixo = false;
    private Integer dias_anteriores;
    private Integer dias_limite;

    public PactoPayConfigCobrancaAntecipadaDTO() {

    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public boolean isCom_autorizacao() {
        return com_autorizacao;
    }

    public void setCom_autorizacao(boolean com_autorizacao) {
        this.com_autorizacao = com_autorizacao;
    }

    public boolean isAplicar_desconto() {
        return aplicar_desconto;
    }

    public void setAplicar_desconto(boolean aplicar_desconto) {
        this.aplicar_desconto = aplicar_desconto;
    }

    public Double getDesconto() {
        return desconto;
    }

    public void setDesconto(Double desconto) {
        this.desconto = desconto;
    }

    public Integer getDias_anteriores() {
        return dias_anteriores;
    }

    public void setDias_anteriores(Integer dias_anteriores) {
        this.dias_anteriores = dias_anteriores;
    }

    public Integer getDias_limite() {
        return dias_limite;
    }

    public void setDias_limite(Integer dias_limite) {
        this.dias_limite = dias_limite;
    }

    public boolean isValor_fixo() {
        return valor_fixo;
    }

    public void setValor_fixo(boolean valor_fixo) {
        this.valor_fixo = valor_fixo;
    }

    public boolean isSms() {
        return sms;
    }

    public void setSms(boolean sms) {
        this.sms = sms;
    }

    public boolean isEmail() {
        return email;
    }

    public void setEmail(boolean email) {
        this.email = email;
    }

    public boolean isWhatsapp() {
        return whatsapp;
    }

    public void setWhatsapp(boolean whatsapp) {
        this.whatsapp = whatsapp;
    }

    public boolean isApp() {
        return app;
    }

    public void setApp(boolean app) {
        this.app = app;
    }

    public boolean isGymbotpro() {
        return gymbotpro;
    }

    public void setGymbotpro(boolean gymbotpro) {
        this.gymbotpro = gymbotpro;
    }
}

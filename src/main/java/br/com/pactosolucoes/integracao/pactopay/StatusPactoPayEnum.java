package br.com.pactosolucoes.integracao.pactopay;

public enum StatusPactoPayEnum {

    NENHUM(                     0, "NENHUM", "", ""),
    ERRO                       (1, "ERRO", "", "Inválida"),
    APROVADA_AGUARDANDO_CAPTURA(2, "APROVADA_AGUARDANDO_CAPTURA", "", "Aguardando aprovação"),
    NAO_APROVADA(               3, "NAO_APROVADA", "", "Não aprovada"),
    ENVIADA(                    4, "ENVIADA", "", "Aguardando aprovação"),
    CONCLUIDA_COM_SUCESSO(      5, "CONCLUIDA_COM_SUCESSO", "PAGA", "Aprovada"),
    CANCELAMENTO_SOLICITADO(    6, "CANCELAMENTO_SOLICITADO", "", "Cancelamento solicitado"),
    CANCELADA(                  7, "CANCELADA", "CANCELADA", "Cancelada"),
    PENDENTE(                   8, "PENDENT<PERSON>", "", "<PERSON>dente"),
    GERADA(                     9, "GERADA", "GERADA", "Gerada"),
    ESTORNADA(                  10, "ESTORNADA", "", "Estornada"),
    PROCESSADO(                 11, "PROCESSADO", "", "Processado"),
    ERRO_RETORNO(               12, "ERRO_RETORNO", "", "Erro no retorno"),
    AGUARDANDO(                 13, "AGUARDANDO", "ATIVA", "Ativa"),
    EXPIRADO(                   14, "EXPIRADO", "EXPIRADA", "Expirada");

    private Integer codigo;
    private String descricao;
    private String descricaoPix;
    private String descricaoFront;

    StatusPactoPayEnum(Integer codigo, String descricao,
                       String descricaoPix, String descricaoFront) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.descricaoPix = descricaoPix;
        this.descricaoFront = descricaoFront;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static StatusPactoPayEnum obterPorCodigo(Integer codigo) {
        for (StatusPactoPayEnum situacaoTransacaoEnum : StatusPactoPayEnum.values()) {
            if (situacaoTransacaoEnum.getCodigo().equals(codigo)) {
                return situacaoTransacaoEnum;
            }
        }
        return null;
    }

    public String getDescricaoPix() {
        return descricaoPix;
    }

    public String getDescricaoFront() {
        return descricaoFront;
    }

    public void setDescricaoFront(String descricaoFront) {
        this.descricaoFront = descricaoFront;
    }
}

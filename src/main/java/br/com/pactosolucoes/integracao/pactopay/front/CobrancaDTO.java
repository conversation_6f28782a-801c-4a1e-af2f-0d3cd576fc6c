package br.com.pactosolucoes.integracao.pactopay.front;

import br.com.pactosolucoes.autorizacaocobranca.controle.util.ValidaBandeira;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CobrancaDTO extends SuperTO {

    private Integer remessaItem;
    private Integer transacao;
    private String identificador;
    private String tipo;
    private String bandeira;
    private String cartao;
    private String titular;
    private String convenio;
    private String status;
    private Integer statusCodigo;
    private String statusDescricao;
    private String codigoRetorno;
    private String descricaoRetorno;
    private String data;
    private Double valor;
    private String origem;
    private Integer recibo;
    private Integer qtdParcelas;
    private String usuario;
    private Integer codigo;
    private Integer codConvenio;
    private String data_cancelamento;
    private Integer parcelamento;
    private Integer qtd_parcelas;
    private String situacao_descricao;
    private String autorizacao;
    private String nsu;

    public CobrancaDTO() {
    }

    //TRANSAÇÃO
    public CobrancaDTO(TransacaoVO obj) {
        this.transacao = obj.getCodigo();
        this.tipo = "TRANSAÇÃO";
        this.identificador = obj.getCodigoExterno();
        this.cartao = obj.getCartaoMascarado();
        this.bandeira = obj.getBandeira().toUpperCase();
        this.titular = obj.getNomePessoa();
        this.convenio = obj.getConvenioCobrancaVO().getDescricao();
        this.status = obj.getSituacao().name();
        this.statusCodigo = obj.getCodigoStatusPactoPay();
        this.statusDescricao = obj.getSituacao().getDescricao();
        this.codigoRetorno = obj.getCodigoRetornoGestaoTransacao();
        this.descricaoRetorno = obj.getCodigoRetornoGestaoTransacaoMotivo();
        this.data = Uteis.getDataComHora(obj.getDataProcessamento());
        this.valor = obj.getValor();
        this.recibo = obj.getReciboPagamento();
        this.usuario = obj.getUsuarioResponsavel().getNomeAbreviado();
        if (!obj.getOrigem().equals(OrigemCobrancaEnum.NENHUM)) {
            this.origem = obj.getOrigem().getDescricao();
        } else {
            this.origem = obj.getUsuarioResponsavel().getUsuarioRecorrencia() ? "Automático" : "Manual";
        }
        String nr_vezes_parcelamento = obterItemOutrasInformacoes(AtributoTransacaoEnum.numeroParcelas, obj.getOutrasInformacoes());
        this.parcelamento = UteisValidacao.emptyString(nr_vezes_parcelamento) ? 1 : Integer.parseInt(nr_vezes_parcelamento);
        this.codigo = obj.getCodigo();
        this.codConvenio = UteisValidacao.emptyNumber(obj.getConvenioCobrancaVO().getCodigo()) ? 0 : obj.getConvenioCobrancaVO().getCodigo();
        this.data_cancelamento = obj.getDataHoraCancelamento() == null ? "" : Uteis.getDataComHora(obj.getDataHoraCancelamento());
        this.qtd_parcelas =  UteisValidacao.emptyList(obj.getListaParcelas()) ? 1 : obj.getListaParcelas().size();
        this.preencherOrigem(obj.getOrigem().getCodigo(), obj.getUsuarioResponsavel());
        this.situacao_descricao = obj.getSituacao().getStatusPactoPayEnum() != null ? obj.getSituacao().getStatusPactoPayEnum().getDescricao() : "";
        this.autorizacao = obj.getAutorizacao();
        this.nsu = obj.getNsuTransacao();
    }

    //REMESSA
    public CobrancaDTO(RemessaItemVO obj) {
        this.remessaItem = obj.getCodigo();
        this.tipo = "REMESSA";
        this.identificador = obj.getRemessa().getIdentificador();
        this.cartao = obj.getValorCartaoMascaradoOuAgenciaConta();
        if (!UteisValidacao.emptyString(obj.getNomePessoa())) {
            this.titular = obj.getNomePessoa();
        } else if (!UteisValidacao.emptyString(obj.getNomePessoaApresentar())) {
            this.titular = obj.getNomePessoaApresentar();
        }
        this.convenio = obj.getRemessa().getConvenioCobranca().getDescricao();
        SituacaoTransacaoEnum situacaoTransacaoEnum;
        if (obj.getMovPagamento() != null && !UteisValidacao.emptyNumber(obj.getMovPagamento().getCodigo())) {
            situacaoTransacaoEnum = SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO;
        } else if (obj.getMovPagamento_Apresentar().toUpperCase().contains("CANCELADO")) {
            situacaoTransacaoEnum = SituacaoTransacaoEnum.CANCELADA;
        } else {
            situacaoTransacaoEnum = SituacaoTransacaoEnum.NAO_APROVADA;
        }
        this.status = situacaoTransacaoEnum.name();
        this.statusCodigo = situacaoTransacaoEnum.getStatusPactoPayEnum().getCodigo();
        this.statusDescricao = situacaoTransacaoEnum.getDescricao();
        this.codigoRetorno = obj.getCodigoStatus();
        this.descricaoRetorno = obj.getDescricaoStatus();
        this.data = Uteis.getDataComHora(obj.getRemessa().getDataRegistro());
        this.valor = obj.getValorItemRemessa();
        this.origem = obj.getRemessa().getUsuario().getUsuarioRecorrencia() ? "Automático" : "Gestão de Remessas";
        try {
            ValidaBandeira.Bandeira bandeiraCard = ValidaBandeira.buscarBandeira(obj.getValorCartaoMascaradoOuAgenciaConta().replace(" ", ""));
            if (bandeiraCard != null) {
                this.bandeira = bandeiraCard.getDescricao().toUpperCase();
            }
        } catch (Exception ignored) {
        }
        this.recibo = obj.getReciboPagamentoVO().getCodigo();
        this.usuario = obj.getRemessa().getUsuario().getNomeAbreviado();
    }

    private void preencherOrigem(Integer origem, UsuarioVO usuario) {
        OrigemCobrancaEnum origemCobrancaEnum = OrigemCobrancaEnum.obterPorCodigo(origem);
        if (!origemCobrancaEnum.equals(OrigemCobrancaEnum.NENHUM)) {
            this.origem = origemCobrancaEnum.getDescricao();
        } else {
            this.origem = (usuario != null && usuario.getUsername() != null &&
                    (usuario.getUsername().equalsIgnoreCase("RECOR") ||
                            usuario.getUsername().equalsIgnoreCase("ADMIN"))) ? "Automático" : "Manual";
        }
    }

    public String obterItemOutrasInformacoes(AtributoTransacaoEnum atributoEnum, String outrasInformacoes) {
        try {
            JSONObject jsonOutrasInformacoes = new JSONObject(outrasInformacoes);
            try {
                return jsonOutrasInformacoes.get(atributoEnum.name()).toString();
            } catch (Exception ex) {
            }
            return jsonOutrasInformacoes.getString(atributoEnum.name());
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer getRemessaItem() {
        return remessaItem;
    }

    public void setRemessaItem(Integer remessaItem) {
        this.remessaItem = remessaItem;
    }

    public Integer getTransacao() {
        return transacao;
    }

    public void setTransacao(Integer transacao) {
        this.transacao = transacao;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getCartao() {
        return cartao;
    }

    public void setCartao(String cartao) {
        this.cartao = cartao;
    }

    public String getTitular() {
        return titular;
    }

    public void setTitular(String titular) {
        this.titular = titular;
    }

    public String getConvenio() {
        return convenio;
    }

    public void setConvenio(String convenio) {
        this.convenio = convenio;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCodigoRetorno() {
        return codigoRetorno;
    }

    public void setCodigoRetorno(String codigoRetorno) {
        this.codigoRetorno = codigoRetorno;
    }

    public String getDescricaoRetorno() {
        return descricaoRetorno;
    }

    public void setDescricaoRetorno(String descricaoRetorno) {
        this.descricaoRetorno = descricaoRetorno;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public String getBandeira() {
        return bandeira;
    }

    public void setBandeira(String bandeira) {
        this.bandeira = bandeira;
    }

    public String getStatusDescricao() {
        return statusDescricao;
    }

    public void setStatusDescricao(String statusDescricao) {
        this.statusDescricao = statusDescricao;
    }

    public Integer getRecibo() {
        return recibo;
    }

    public void setRecibo(Integer recibo) {
        this.recibo = recibo;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public Integer getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(Integer qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public Integer getStatusCodigo() {
        return statusCodigo;
    }

    public void setStatusCodigo(Integer statusCodigo) {
        this.statusCodigo = statusCodigo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodConvenio() {
        return codConvenio;
    }

    public void setCodConvenio(Integer codConvenio) {
        this.codConvenio = codConvenio;
    }

    public String getData_cancelamento() {
        return data_cancelamento;
    }

    public void setData_cancelamento(String data_cancelamento) {
        this.data_cancelamento = data_cancelamento;
    }

    public Integer getParcelamento() {
        return parcelamento;
    }

    public void setParcelamento(Integer parcelamento) {
        this.parcelamento = parcelamento;
    }

    public Integer getQtd_parcelas() {
        return qtd_parcelas;
    }

    public void setQtd_parcelas(Integer qtd_parcelas) {
        this.qtd_parcelas = qtd_parcelas;
    }

    public String getSituacao_descricao() {
        return situacao_descricao;
    }

    public void setSituacao_descricao(String situacao_descricao) {
        this.situacao_descricao = situacao_descricao;
    }

    public String getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(String autorizacao) {
        this.autorizacao = autorizacao;
    }

    public String getNsu() {
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }
}

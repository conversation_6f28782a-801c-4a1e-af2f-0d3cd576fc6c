package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Calendario;
import org.json.JSONArray;
import org.json.JSONObject;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PactoPayCobrancaAntecipadaDTO extends SuperTO {

    private String dataLimitePagamento;
    private boolean aplicarDesconto = false;
    private List<Integer> parcelas;

    public PactoPayCobrancaAntecipadaDTO() {

    }

    public PactoPayCobrancaAntecipadaDTO(JSONObject json) {
        this.dataLimitePagamento = json.getString("dataLimitePagamento");
        this.aplicarDesconto = json.getBoolean("aplicarDesconto");

        JSONArray lista = json.getJSONArray("parcelas");
        this.parcelas = new ArrayList<>();
        for (int e = 0; e < lista.length(); e++) {
            this.parcelas.add(lista.getInt(e));
        }
    }

    public List<Integer> getParcelas() {
        if (parcelas == null) {
            parcelas = new ArrayList<>();
        }
        return parcelas;
    }

    public void setParcelas(List<Integer> parcelas) {
        this.parcelas = parcelas;
    }

    public String toString() {
        return new JSONObject(this).toString();
    }

    public boolean isAplicarDesconto() {
        return aplicarDesconto;
    }

    public void setAplicarDesconto(boolean aplicarDesconto) {
        this.aplicarDesconto = aplicarDesconto;
    }

    public String getDataLimitePagamento() {
        return dataLimitePagamento;
    }

    public void setDataLimitePagamento(String dataLimitePagamento) {
        this.dataLimitePagamento = dataLimitePagamento;
    }

    public Date getDataLimitePagamento_Date() throws ParseException {
        return Calendario.getDate("dd/MM/yyyy HH:mm:ss", getDataLimitePagamento());
    }
}

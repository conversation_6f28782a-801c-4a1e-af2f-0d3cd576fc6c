package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.utilitarias.Uteis;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CobrancaPixDTO extends SuperTO {

    private Integer codigo;
    private String identificador; //txid
    private String convenio;
    private String situacao;
    private String situacaoDescricao;
    private String data;
    private String usuario;
    private Double valor;
    private Integer recibo;
    private Integer qtdParcelas;

    public CobrancaPixDTO() {
    }

    public CobrancaPixDTO(PixVO obj) {
        this.codigo = obj.getCodigo();
        this.identificador = obj.getTxid();
        this.convenio = obj.getConveniocobranca().getDescricao();
        this.situacao = obj.getStatus();
        this.situacaoDescricao = obj.getStatusDescricao();
        this.data = Uteis.getDataComHora(obj.getData());
        this.valor = obj.getValor();
        this.recibo = obj.getReciboPagamento();
        this.usuario = obj.getUsuarioResponsavel().getNome();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getConvenio() {
        return convenio;
    }

    public void setConvenio(String convenio) {
        this.convenio = convenio;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoDescricao() {
        return situacaoDescricao;
    }

    public void setSituacaoDescricao(String situacaoDescricao) {
        this.situacaoDescricao = situacaoDescricao;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getRecibo() {
        return recibo;
    }

    public void setRecibo(Integer recibo) {
        this.recibo = recibo;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public Integer getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(Integer qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }
}

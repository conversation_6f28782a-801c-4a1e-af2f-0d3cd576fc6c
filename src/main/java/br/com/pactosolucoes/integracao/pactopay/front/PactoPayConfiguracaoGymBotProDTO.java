package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.json.JSONObject;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PactoPayConfiguracaoGymBotProDTO {

    private String tokenCobrancaAntecipada;
    private String idIntegracaoCobrancaAntecipada;

    private String tokenComunicadoResultadoCobranca;
    private String idIntegracaoComunicadoResultadoCobranca;

    private String tokenComunicadoAtraso;
    private String idIntegracaoComunicadoAtraso;

    private String tokenComunicadoCartao;
    private String idIntegracaoComunicadoCartao;

    public PactoPayConfiguracaoGymBotProDTO() {

    }

    public PactoPayConfiguracaoGymBotProDTO(String dados) {
        try {

            JSONObject json = new JSONObject(dados);
            this.tokenCobrancaAntecipada = json.optString("tokenCobrancaAntecipada");
            this.idIntegracaoCobrancaAntecipada = json.optString("idIntegracaoCobrancaAntecipada");

            this.tokenComunicadoResultadoCobranca = json.optString("tokenComunicadoResultadoCobranca");
            this.idIntegracaoComunicadoResultadoCobranca = json.optString("idIntegracaoComunicadoResultadoCobranca");

            this.tokenComunicadoAtraso = json.optString("tokenComunicadoAtraso");
            this.idIntegracaoComunicadoAtraso = json.optString("idIntegracaoComunicadoAtraso");

            this.tokenComunicadoCartao = json.optString("tokenComunicadoCartao");
            this.idIntegracaoComunicadoCartao = json.optString("idIntegracaoComunicadoCartao");

        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getTokenCobrancaAntecipada() {
        return tokenCobrancaAntecipada;
    }

    public void setTokenCobrancaAntecipada(String tokenCobrancaAntecipada) {
        this.tokenCobrancaAntecipada = tokenCobrancaAntecipada;
    }

    public String getIdIntegracaoCobrancaAntecipada() {
        return idIntegracaoCobrancaAntecipada;
    }

    public void setIdIntegracaoCobrancaAntecipada(String idIntegracaoCobrancaAntecipada) {
        this.idIntegracaoCobrancaAntecipada = idIntegracaoCobrancaAntecipada;
    }

    public String getTokenComunicadoResultadoCobranca() {
        return tokenComunicadoResultadoCobranca;
    }

    public void setTokenComunicadoResultadoCobranca(String tokenComunicadoResultadoCobranca) {
        this.tokenComunicadoResultadoCobranca = tokenComunicadoResultadoCobranca;
    }

    public String getIdIntegracaoComunicadoResultadoCobranca() {
        return idIntegracaoComunicadoResultadoCobranca;
    }

    public void setIdIntegracaoComunicadoResultadoCobranca(String idIntegracaoComunicadoResultadoCobranca) {
        this.idIntegracaoComunicadoResultadoCobranca = idIntegracaoComunicadoResultadoCobranca;
    }

    public String getTokenComunicadoAtraso() {
        return tokenComunicadoAtraso;
    }

    public void setTokenComunicadoAtraso(String tokenComunicadoAtraso) {
        this.tokenComunicadoAtraso = tokenComunicadoAtraso;
    }

    public String getIdIntegracaoComunicadoAtraso() {
        return idIntegracaoComunicadoAtraso;
    }

    public void setIdIntegracaoComunicadoAtraso(String idIntegracaoComunicadoAtraso) {
        this.idIntegracaoComunicadoAtraso = idIntegracaoComunicadoAtraso;
    }

    public String getTokenComunicadoCartao() {
        return tokenComunicadoCartao;
    }

    public void setTokenComunicadoCartao(String tokenComunicadoCartao) {
        this.tokenComunicadoCartao = tokenComunicadoCartao;
    }

    public String getIdIntegracaoComunicadoCartao() {
        return idIntegracaoComunicadoCartao;
    }

    public void setIdIntegracaoComunicadoCartao(String idIntegracaoComunicadoCartao) {
        this.idIntegracaoComunicadoCartao = idIntegracaoComunicadoCartao;
    }

    @Override
    public String toString() {
        return new JSONObject(this).toString();
    }
}

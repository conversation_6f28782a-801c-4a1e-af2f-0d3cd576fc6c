package br.com.pactosolucoes.integracao.pactopay.front.cartao;


import negocio.comuns.arquitetura.SuperTO;

public class HistoricoDTO extends SuperTO {

    private String data;
    private String status;
    private Integer statusCodigo;
    private Integer qtdTotal;
    private Double valorTotal;
    private Integer qtdRecebidos;
    private Double valorRecebidos;
    private Integer qtdPendentes;
    private Double valorPendentes;
    private Integer qtdNaoAprovada;
    private Double valorNaoAprovada;
    private Double eficiencia;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getQtdTotal() {
        return qtdTotal;
    }

    public void setQtdTotal(Integer qtdTotal) {
        this.qtdTotal = qtdTotal;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Integer getQtdRecebidos() {
        return qtdRecebidos;
    }

    public void setQtdRecebidos(Integer qtdRecebidos) {
        this.qtdRecebidos = qtdRecebidos;
    }

    public Double getValorRecebidos() {
        return valorRecebidos;
    }

    public void setValorRecebidos(Double valorRecebidos) {
        this.valorRecebidos = valorRecebidos;
    }

    public Integer getQtdPendentes() {
        return qtdPendentes;
    }

    public void setQtdPendentes(Integer qtdPendentes) {
        this.qtdPendentes = qtdPendentes;
    }

    public Double getValorPendentes() {
        return valorPendentes;
    }

    public void setValorPendentes(Double valorPendentes) {
        this.valorPendentes = valorPendentes;
    }

    public Double getEficiencia() {
        return eficiencia;
    }

    public void setEficiencia(Double eficiencia) {
        this.eficiencia = eficiencia;
    }

    public Integer getQtdNaoAprovada() {
        return qtdNaoAprovada;
    }

    public void setQtdNaoAprovada(Integer qtdNaoAprovada) {
        this.qtdNaoAprovada = qtdNaoAprovada;
    }

    public Double getValorNaoAprovada() {
        return valorNaoAprovada;
    }

    public void setValorNaoAprovada(Double valorNaoAprovada) {
        this.valorNaoAprovada = valorNaoAprovada;
    }

    public Integer getStatusCodigo() {
        return statusCodigo;
    }

    public void setStatusCodigo(Integer statusCodigo) {
        this.statusCodigo = statusCodigo;
    }
}

package br.com.pactosolucoes.integracao.pactopay;

public enum TipoConsultaHistoricoCartaoEnum {

    TRANSACAO(1, "TRANSACAO"),
    REMESSA(2, "REMESSA");

    private Integer codigo;
    private String descricao;

    TipoConsultaHistoricoCartaoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoConsultaHistoricoCartaoEnum obterPorCodigo(Integer codigo) {
        for (TipoConsultaHistoricoCartaoEnum tipo : TipoConsultaHistoricoCartaoEnum.values()) {
            if (tipo.getCodigo().equals(codigo)) {
                return tipo;
            }
        }
        return null;
    }
}

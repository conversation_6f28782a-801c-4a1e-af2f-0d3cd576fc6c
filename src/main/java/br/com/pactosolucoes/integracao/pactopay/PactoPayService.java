package br.com.pactosolucoes.integracao.pactopay;

import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.integracao.pactopay.dto.RemessaDTO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.legolas.LegolasService;
import servicos.propriedades.PropsService;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 04/11/2020
 */
public class PactoPayService {

    private static final String HEADER_AUTHORIZATION = "Authorization";
    private static final String HEADER_ACCEPT = "Accept";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION_JSON = "application/json";
    private static final String CHARSET_UTF8 = "UTF-8";
    private static final String DATA = "data";
    private static final String STATUS = "status";
    private static String token = null;

    public PactoPayService() throws Exception {
    }

    public static String obterUrl(String aplicacao) {
        JSONObject urls = LegolasService.obterUrls();
        return urls.optString(aplicacao);
    }

    public static String obterToken() {
        //Obter token de autenticação no autenticacaoMS
        if (token == null) {
            try {
                String urlAutenticacaoMS = PropsService.getPropertyValue(PropsService.URL_AUTENTICACAO_MS); //Em produção, preenchido sempre
                if (UteisValidacao.emptyString(urlAutenticacaoMS)) {
                    //vai entrar aqui quando for local ou swarm
                    urlAutenticacaoMS = obterUrl("autenticacao");
                }
                token = LegolasService.obterToken(urlAutenticacaoMS, false);
            } catch (Exception e) {
                Uteis.logar(e, AragornService.class);
            }
        }
        return token;
    }

    public static String enviarRemessa(RemessaVO remessaVO, String chave, boolean cancelamento) throws Exception {
        RemessaDTO remessaDTO = remessaVO.toRemessaDTO(chave);
        remessaDTO.setAsync(true);
        RespostaHttpDTO respostaHttpDTO = enviar("/remessa" + (cancelamento ? "/cancelamento" : ""), new JSONObject(remessaDTO).toString(), MetodoHttpEnum.POST);

        JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
        if (json.has(DATA)) {
            return json.getJSONObject(DATA).getJSONObject("remessa").getString("id");
        } else {
            throw new Exception(respostaHttpDTO.getResponse());
        }
    }

    public static RespostaHttpDTO enviar(String metodo, String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_CONTENT_TYPE, APPLICATION_JSON);
        headers.put(HEADER_AUTHORIZATION, obterToken());

        String path = obterUrl("pactopay") + metodo;

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, body, metodoHttpEnum);
        service = null;
        return respostaHttpDTO;
    }
}

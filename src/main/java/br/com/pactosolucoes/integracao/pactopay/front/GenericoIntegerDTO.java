package br.com.pactosolucoes.integracao.pactopay.front;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.integracao.pactopay.StatusPactoPayEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class GenericoIntegerDTO {

    private Integer codigo;
    private String descricao;

    public GenericoIntegerDTO() {

    }

    public GenericoIntegerDTO(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public GenericoIntegerDTO(TipoAutorizacaoCobrancaEnum obj) {
        this.codigo = obj.getId();
        this.descricao = obj.getDescricao();
    }

    public GenericoIntegerDTO(TipoObjetosCobrarEnum obj) {
        this.codigo = obj.getId();
        this.descricao = obj.getDescricao();
    }

    public GenericoIntegerDTO(TipoTransacaoEnum obj) {
        this.codigo = obj.getId();
        this.descricao = obj.getDescricao();
    }

    public GenericoIntegerDTO(SituacaoTransacaoEnum obj) {
        this.codigo = obj.getId();
        this.descricao = obj.getDescricao();
    }

    public GenericoIntegerDTO(StatusPactoPayEnum obj) {
        this.codigo = obj.getCodigo();
        this.descricao = obj.getDescricao();
    }

    public GenericoIntegerDTO(SituacaoBoletoEnum obj) {
        this.codigo = obj.getCodigo();
        this.descricao = obj.getDescricao();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

package br.com.pactosolucoes.integracao.pactopay.front.pix;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;

public class CobrancaPixDetalheDTO extends SuperTO {

    private Integer transacao;
    private Integer remessa;
    private Integer remessaItem;
    private Integer cliente;
    private Integer pessoa;
    private String identificador;
    private String matricula;
    private String nome;
    private String convenio;
    private String status;
    private Integer statusCodigo;
    private String codigoRetorno;
    private String data;
    private String dataPagamento;
    private Double valor;
    private String tipoOperacao;
    private String usuario;
    private Integer qtdParcelas;
    private Integer recibo;
    private String descricaoRetorno;
    private boolean permiteComprovantePagamento = false;
    private String txId;

    public CobrancaPixDetalheDTO() {

    }

    public CobrancaPixDetalheDTO(PixVO obj) {
        this.transacao = obj.getCodigo();
        this.txId = obj.getTxid();
        this.pessoa = obj.getPessoa();
        this.identificador = getIdentificador();
        if (!UteisValidacao.emptyNumber(obj.getClienteVO().getCodigo())) {
            this.cliente = obj.getClienteVO().getCodigo();
            this.matricula = obj.getClienteVO().getMatricula();
        }
        this.nome = obj.getPessoaVO().getNome();
        this.convenio = obj.getConveniocobranca().getDescricao();
        this.status = obj.getStatusEnum().getStatusPactoPayEnum().getDescricaoPix();
        this.statusCodigo = obj.getStatusEnum().getStatusPactoPayEnum().getCodigo();
        this.codigoRetorno = obj.getStatusDescricao();
        this.descricaoRetorno = obj.getStatusDescricao();
        this.data = Calendario.getDataAplicandoFormatacao(obj.getData(), "dd/MM/yyyy HH:mm:ss");
        this.dataPagamento = Calendario.getDataAplicandoFormatacao(obj.getDataPagamento(), "dd/MM/yyyy HH:mm:ss");
        this.valor = obj.getValor();
        this.tipoOperacao = obj.getOrigem().getDescricao();
        this.usuario = obj.getUsuarioResponsavel().getNome();
        this.recibo = UteisValidacao.emptyNumber(obj.getReciboPagamento()) ? null : obj.getReciboPagamento();
        this.qtdParcelas = obj.getQtdMovParcelas();
        this.permiteComprovantePagamento = !UteisValidacao.emptyNumber(obj.getReciboPagamento());
    }

    public Integer getTransacao() {
        return transacao;
    }

    public void setTransacao(Integer transacao) {
        this.transacao = transacao;
    }

    public Integer getRemessaItem() {
        return remessaItem;
    }

    public void setRemessaItem(Integer remessaItem) {
        this.remessaItem = remessaItem;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getConvenio() {
        return convenio;
    }

    public void setConvenio(String convenio) {
        this.convenio = convenio;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCodigoRetorno() {
        return codigoRetorno;
    }

    public void setCodigoRetorno(String codigoRetorno) {
        this.codigoRetorno = codigoRetorno;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public Integer getRemessa() {
        return remessa;
    }

    public void setRemessa(Integer remessa) {
        this.remessa = remessa;
    }

    public Integer getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(Integer qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getDescricaoRetorno() {
        if (descricaoRetorno == null) {
            descricaoRetorno = "";
        }
        return descricaoRetorno;
    }

    public void setDescricaoRetorno(String descricaoRetorno) {
        this.descricaoRetorno = descricaoRetorno;
    }

    public Integer getStatusCodigo() {
        return statusCodigo;
    }

    public void setStatusCodigo(Integer statusCodigo) {
        this.statusCodigo = statusCodigo;
    }

    public boolean isPermiteComprovantePagamento() {
        return permiteComprovantePagamento;
    }

    public void setPermiteComprovantePagamento(boolean permiteComprovantePagamento) {
        this.permiteComprovantePagamento = permiteComprovantePagamento;
    }

    public Integer getRecibo() {
        return recibo;
    }

    public void setRecibo(Integer recibo) {
        this.recibo = recibo;
    }

    public String getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(String dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public String getTxId() {
        return txId;
    }

    public void setTxId(String txId) {
        this.txId = txId;
    }
}

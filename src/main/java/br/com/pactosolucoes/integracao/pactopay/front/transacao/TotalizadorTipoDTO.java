package br.com.pactosolucoes.integracao.pactopay.front.transacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TotalizadorTipoDTO extends SuperTO {

    private Integer tipo_codigo;
    private String tipo_descricao;
    private List<TotalizadorDTO> totalizadores;

    public Integer getTipo_codigo() {
        return tipo_codigo;
    }

    public void setTipo_codigo(Integer tipo_codigo) {
        this.tipo_codigo = tipo_codigo;
    }

    public String getTipo_descricao() {
        return tipo_descricao;
    }

    public void setTipo_descricao(String tipo_descricao) {
        this.tipo_descricao = tipo_descricao;
    }

    public List<TotalizadorDTO> getTotalizadores() {
        return totalizadores;
    }

    public void setTotalizadores(List<TotalizadorDTO> totalizadores) {
        this.totalizadores = totalizadores;
    }
}

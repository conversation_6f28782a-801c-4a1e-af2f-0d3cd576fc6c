package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PactoPayConfigComunicadoCartaoDTO extends SuperTO {

    private boolean ativo = false;
    private boolean sms = false;
    private boolean email = false;
    private boolean whatsapp = false;
    private boolean app = false;
    private boolean gymbotpro = false;
    private boolean vencido = false;
    private boolean proximo_vencimento = false;

    public PactoPayConfigComunicadoCartaoDTO() {

    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public boolean isSms() {
        return sms;
    }

    public void setSms(boolean sms) {
        this.sms = sms;
    }

    public boolean isEmail() {
        return email;
    }

    public void setEmail(boolean email) {
        this.email = email;
    }

    public boolean isVencido() {
        return vencido;
    }

    public void setVencido(boolean vencido) {
        this.vencido = vencido;
    }

    public boolean isProximo_vencimento() {
        return proximo_vencimento;
    }

    public void setProximo_vencimento(boolean proximo_vencimento) {
        this.proximo_vencimento = proximo_vencimento;
    }

    public boolean isWhatsapp() {
        return whatsapp;
    }

    public void setWhatsapp(boolean whatsapp) {
        this.whatsapp = whatsapp;
    }

    public boolean isApp() {
        return app;
    }

    public void setApp(boolean app) {
        this.app = app;
    }

    public boolean isGymbotpro() {
        return gymbotpro;
    }

    public void setGymbotpro(boolean gymbotpro) {
        this.gymbotpro = gymbotpro;
    }
}

package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TotalizadorPendenteDTO extends SuperTO {

    private Integer operacao;
    private String operacaoDescricao;
    private Double valorTotal;
    private Integer qtdTotal;
    private Integer qtdAutomatica;
    private Integer qtdManual;

    public Integer getOperacao() {
        return operacao;
    }

    public void setOperacao(Integer operacao) {
        this.operacao = operacao;
    }

    public Integer getQtdTotal() {
        return qtdTotal;
    }

    public void setQtdTotal(Integer qtdTotal) {
        this.qtdTotal = qtdTotal;
    }

    public Integer getQtdAutomatica() {
        return qtdAutomatica;
    }

    public void setQtdAutomatica(Integer qtdAutomatica) {
        this.qtdAutomatica = qtdAutomatica;
    }

    public Integer getQtdManual() {
        return qtdManual;
    }

    public void setQtdManual(Integer qtdManual) {
        this.qtdManual = qtdManual;
    }

    public String getOperacaoDescricao() {
        return operacaoDescricao;
    }

    public void setOperacaoDescricao(String operacaoDescricao) {
        this.operacaoDescricao = operacaoDescricao;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }
}

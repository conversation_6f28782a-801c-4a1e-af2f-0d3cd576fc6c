package br.com.pactosolucoes.integracao.pactopay;

public enum OperacaoEnum {

    RETORNO_REMESSA(1, "RETORNO_REMESSA"),
    ALTERAR_SITUACAO_REMESSA(2, "ALTERAR_SITUACAO_REMESSA"),

    RET<PERSON><PERSON>O_TRANSACAO_PAGAR(3, "RETORNO_TRANSACAO_PAGAR"),
    RETOR<PERSON>O_TRANSACAO_CONSULTAR(4, "RETORNO_TRANSACAO_CONSULTAR"),
    RETORNO_TRANSACAO_CANCELAR(5, "RETORNO_TRANSACAO_CANCELAR"),
    RETORNO_TRANSACAO_CAPTURAR(6, "RETORNO_TRANSACAO_CAPTURAR");

    private Integer codigo;
    private String descricao;

    OperacaoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static OperacaoEnum obterPorCodigo(Integer codigo) {
        for (OperacaoEnum tipo : OperacaoEnum.values()) {
            if (tipo.getCodigo().equals(codigo)) {
                return tipo;
            }
        }
        return null;
    }
}

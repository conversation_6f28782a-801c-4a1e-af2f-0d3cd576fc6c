package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Calendario;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class RemessaDTO extends SuperTO {

    private String data;
    private String tipo;
    private String status;
    private Integer statusCodigo;
    private Integer statusDescricao;
    private Integer codigo;
    private String sequencial;
    private Integer qtdTotal;
    private Double valorTotal;
    private Integer qtdRecebidos;
    private Double valorRecebidos;
    private Integer qtdPendentes;
    private Double valorPendentes;
    private Integer qtdNaoAprovada;
    private Double valorNaoAprovada;
    private Double eficiencia;
    private String usuario;

    public RemessaDTO() {
    }

    public RemessaDTO(RemessaVO remessaVO) {
        this.data = Calendario.getDataAplicandoFormatacao(remessaVO.getDataRegistro(), "dd/MM/yyyy HH:mm:ss");
        this.tipo = remessaVO.isCancelamento() ? "Cancelado" : "Normal";
        this.status = remessaVO.getSituacaoRemessa().getDescricao();
        this.statusCodigo = remessaVO.getSituacaoRemessa().getStatusPactoPayEnum().getCodigo();
        this.statusDescricao = remessaVO.getSituacaoRemessa().getId();
        this.codigo = remessaVO.getCodigo();
        this.sequencial = remessaVO.getSequencialArquivoExibicao();
        this.qtdTotal = remessaVO.getQtdRegistros();
        this.valorTotal = remessaVO.getValorBruto();
        this.qtdRecebidos = remessaVO.getQtdAceito();
        this.valorRecebidos = remessaVO.getValorAceito();

//        this.qtdPendentes = remessaVO.getqt();
//        this.valorPendentes = remessaVO.getValorAceito();
//
//        this.qtdNaoAprovada = remessaVO.();
//        this.valorNaoAprovada = remessaVO.getValorAceito();
//
//        this.eficiencia = remessaVO.getValorAceito();

        this.usuario = remessaVO.getUsuario().getNome();
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getQtdTotal() {
        return qtdTotal;
    }

    public void setQtdTotal(Integer qtdTotal) {
        this.qtdTotal = qtdTotal;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Integer getQtdRecebidos() {
        return qtdRecebidos;
    }

    public void setQtdRecebidos(Integer qtdRecebidos) {
        this.qtdRecebidos = qtdRecebidos;
    }

    public Double getValorRecebidos() {
        return valorRecebidos;
    }

    public void setValorRecebidos(Double valorRecebidos) {
        this.valorRecebidos = valorRecebidos;
    }

    public Integer getQtdPendentes() {
        return qtdPendentes;
    }

    public void setQtdPendentes(Integer qtdPendentes) {
        this.qtdPendentes = qtdPendentes;
    }

    public Double getValorPendentes() {
        return valorPendentes;
    }

    public void setValorPendentes(Double valorPendentes) {
        this.valorPendentes = valorPendentes;
    }

    public Double getEficiencia() {
        return eficiencia;
    }

    public void setEficiencia(Double eficiencia) {
        this.eficiencia = eficiencia;
    }

    public Integer getQtdNaoAprovada() {
        return qtdNaoAprovada;
    }

    public void setQtdNaoAprovada(Integer qtdNaoAprovada) {
        this.qtdNaoAprovada = qtdNaoAprovada;
    }

    public Double getValorNaoAprovada() {
        return valorNaoAprovada;
    }

    public void setValorNaoAprovada(Double valorNaoAprovada) {
        this.valorNaoAprovada = valorNaoAprovada;
    }

    public String getSequencial() {
        return sequencial;
    }

    public void setSequencial(String sequencial) {
        this.sequencial = sequencial;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getStatusDescricao() {
        return statusDescricao;
    }

    public void setStatusDescricao(Integer statusDescricao) {
        this.statusDescricao = statusDescricao;
    }

    public Integer getStatusCodigo() {
        return statusCodigo;
    }

    public void setStatusCodigo(Integer statusCodigo) {
        this.statusCodigo = statusCodigo;
    }
}

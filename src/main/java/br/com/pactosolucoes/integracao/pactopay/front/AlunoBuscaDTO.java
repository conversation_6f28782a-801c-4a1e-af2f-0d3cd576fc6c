package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoBuscaDTO extends SuperTO {

    private String matricula;
    private String nome;
    private String foto;
    private Integer pessoa;
    private Integer cliente;
    private String empresa;

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getFoto() {
        return foto;
    }

    public void setFoto(String foto) {
        this.foto = foto;
    }
}

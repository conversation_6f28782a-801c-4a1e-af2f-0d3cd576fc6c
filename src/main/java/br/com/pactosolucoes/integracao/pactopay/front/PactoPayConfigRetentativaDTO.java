package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PactoPayConfigRetentativaDTO extends SuperTO {

    private boolean ativo = false;
    private Integer limite_dias;
    private Integer intervalo_dias;

    public PactoPayConfigRetentativaDTO() {

    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getLimite_dias() {
        return limite_dias;
    }

    public void setLimite_dias(Integer limite_dias) {
        this.limite_dias = limite_dias;
    }

    public Integer getIntervalo_dias() {
        return intervalo_dias;
    }

    public void setIntervalo_dias(Integer intervalo_dias) {
        this.intervalo_dias = intervalo_dias;
    }
}

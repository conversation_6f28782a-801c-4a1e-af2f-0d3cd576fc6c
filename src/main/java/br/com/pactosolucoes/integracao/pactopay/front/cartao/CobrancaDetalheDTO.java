package br.com.pactosolucoes.integracao.pactopay.front.cartao;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

public class CobrancaDetalheDTO extends SuperTO {
    private Integer transacao;
    private Integer remessa;
    private Integer remessaItem;
    private Integer cliente;
    private Integer pessoa;
    private String identificador;
    private String matricula;
    private String nome;
    private String cartao;
    private String titular;
    private String convenio;
    private String status;
    private Integer statusCodigo;
    private String codigoRetorno;
    private String data;
    private Double valor;
    private String tipoOperacao;
    private String usuario;
    private String autorizacao;
    private String nsu;
    private String bandeira;
    private Integer qtdParcelas;
    private Integer recibo;
    private String descricaoRetorno;
    private String origem;
    private boolean permiteCancelar = false;
    private boolean permiteSincronizar = false;
    private boolean permiteComprovanteCancelamento = false;
    private boolean permiteComprovantePagamento = false;
    private Integer codigo;
    private Integer codConvenio;
    private String data_cancelamento;
    private Integer parcelamento;
    private Integer qtd_parcelas;
    private String situacao_descricao;

    public CobrancaDetalheDTO() {

    }

    public CobrancaDetalheDTO(TransacaoVO transacaoVO) {
        this.transacao = transacaoVO.getCodigo();
        this.pessoa = transacaoVO.getPessoaPagador().getCodigo();
        this.identificador = transacaoVO.getCodigoExterno();
        if (transacaoVO.getClienteVO() != null) {
            this.cliente = transacaoVO.getClienteVO().getCodigo();
            this.matricula = transacaoVO.getClienteVO().getMatricula();
        }
        this.nome = transacaoVO.getPessoaPagador().getNome();
        this.cartao = transacaoVO.getCartaoMascarado();
        this.titular = transacaoVO.getNomePessoa();
        this.convenio = transacaoVO.getConvenioCobrancaVO().getDescricao();
        this.status = transacaoVO.getSituacao().getStatusPactoPayEnum().getDescricao();
        this.statusCodigo = transacaoVO.getSituacao().getStatusPactoPayEnum().getCodigo();
        this.codigoRetorno = transacaoVO.getCodigoRetornoGestaoTransacao();
        this.data = Calendario.getDataAplicandoFormatacao(transacaoVO.getDataProcessamento(), "dd/MM/yyyy HH:mm:ss");
        this.valor = transacaoVO.getValor();
        this.tipoOperacao = transacaoVO.getOrigem().getDescricao();
        this.usuario = transacaoVO.getUsuarioResponsavel().getNome();

        //NSU
        this.nsu = transacaoVO.getCodigoNSU();
        if (UteisValidacao.emptyString(this.nsu) && !UteisValidacao.emptyString(transacaoVO.getNsuTransacao())) {
            this.nsu = transacaoVO.getNsuTransacao();
        }
        //AUTORIZAÇÃO
        this.autorizacao = transacaoVO.getAutorizacao();
        if (UteisValidacao.emptyString(this.autorizacao)) {
            if (!UteisValidacao.emptyString(transacaoVO.getCodigoAutorizacao())) {
                this.autorizacao = transacaoVO.getCodigoAutorizacao();
            }
        }

        this.recibo = UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento()) ? null : transacaoVO.getReciboPagamento();
        this.bandeira = transacaoVO.getBandeira();
        this.qtdParcelas = transacaoVO.getQtdParcelasTransacao();
        this.permiteCancelar = transacaoVO.isPermiteCancelar();
        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
            this.descricaoRetorno = SituacaoTransacaoEnum.CANCELADA.getHint();
        } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
            this.descricaoRetorno = SituacaoTransacaoEnum.ESTORNADA.getHint();
        } else {
            this.descricaoRetorno = transacaoVO.getCodigoRetornoGestaoTransacaoMotivo();
        }
        this.permiteSincronizar = transacaoVO.isPermiteSincronizar();
        this.permiteComprovanteCancelamento = !UteisValidacao.emptyString(transacaoVO.getUrlComprovanteCancelamento());
        this.permiteComprovantePagamento = !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento());
        String nr_vezes_parcelamento = obterItemOutrasInformacoes(AtributoTransacaoEnum.numeroParcelas, transacaoVO.getOutrasInformacoes());
        this.parcelamento = UteisValidacao.emptyString(nr_vezes_parcelamento) ? 1 : Integer.parseInt(nr_vezes_parcelamento);
        this.codigo = transacaoVO.getCodigo();
        this.codConvenio = UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo()) ? 0 : transacaoVO.getConvenioCobrancaVO().getCodigo();
        this.data_cancelamento = transacaoVO.getDataHoraCancelamento() == null ? "" : Uteis.getDataComHora(transacaoVO.getDataHoraCancelamento());
        this.qtd_parcelas = UteisValidacao.emptyList(transacaoVO.getListaParcelas()) ? 1 : transacaoVO.getListaParcelas().size();
        this.preencherOrigem(transacaoVO.getOrigem().getCodigo(), transacaoVO.getUsuarioResponsavel());
        this.situacao_descricao = transacaoVO.getSituacao().getStatusPactoPayEnum() != null ? transacaoVO.getSituacao().getStatusPactoPayEnum().getDescricao() : "";
    }

    private void preencherOrigem(Integer origem, UsuarioVO usuario) {
        OrigemCobrancaEnum origemCobrancaEnum = OrigemCobrancaEnum.obterPorCodigo(origem);
        if (!origemCobrancaEnum.equals(OrigemCobrancaEnum.NENHUM)) {
            this.origem = origemCobrancaEnum.getDescricao();
        } else {
            this.origem = (usuario != null && usuario.getUsername() != null &&
                    (usuario.getUsername().equalsIgnoreCase("RECOR") ||
                            usuario.getUsername().equalsIgnoreCase("ADMIN"))) ? "Automático" : "Manual";
        }
    }

    public String obterItemOutrasInformacoes(AtributoTransacaoEnum atributoEnum, String outrasInformacoes) {
        try {
            JSONObject jsonOutrasInformacoes = new JSONObject(outrasInformacoes);
            try {
                return jsonOutrasInformacoes.get(atributoEnum.name()).toString();
            } catch (Exception ex) {
            }
            return jsonOutrasInformacoes.getString(atributoEnum.name());
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer getTransacao() {
        return transacao;
    }

    public void setTransacao(Integer transacao) {
        this.transacao = transacao;
    }

    public Integer getRemessaItem() {
        return remessaItem;
    }

    public void setRemessaItem(Integer remessaItem) {
        this.remessaItem = remessaItem;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCartao() {
        return cartao;
    }

    public void setCartao(String cartao) {
        this.cartao = cartao;
    }

    public String getTitular() {
        return titular;
    }

    public void setTitular(String titular) {
        this.titular = titular;
    }

    public String getConvenio() {
        return convenio;
    }

    public void setConvenio(String convenio) {
        this.convenio = convenio;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCodigoRetorno() {
        return codigoRetorno;
    }

    public void setCodigoRetorno(String codigoRetorno) {
        this.codigoRetorno = codigoRetorno;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(String autorizacao) {
        this.autorizacao = autorizacao;
    }

    public String getBandeira() {
        return bandeira;
    }

    public void setBandeira(String bandeira) {
        this.bandeira = bandeira;
    }

    public Integer getRemessa() {
        return remessa;
    }

    public void setRemessa(Integer remessa) {
        this.remessa = remessa;
    }

    public Integer getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(Integer qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public boolean isPermiteCancelar() {
        return permiteCancelar;
    }

    public void setPermiteCancelar(boolean permiteCancelar) {
        this.permiteCancelar = permiteCancelar;
    }

    public String getDescricaoRetorno() {
        if (descricaoRetorno == null) {
            descricaoRetorno = "";
        }
        return descricaoRetorno;
    }

    public void setDescricaoRetorno(String descricaoRetorno) {
        this.descricaoRetorno = descricaoRetorno;
    }

    public Integer getStatusCodigo() {
        return statusCodigo;
    }

    public void setStatusCodigo(Integer statusCodigo) {
        this.statusCodigo = statusCodigo;
    }

    public boolean isPermiteSincronizar() {
        return permiteSincronizar;
    }

    public void setPermiteSincronizar(boolean permiteSincronizar) {
        this.permiteSincronizar = permiteSincronizar;
    }

    public boolean isPermiteComprovanteCancelamento() {
        return permiteComprovanteCancelamento;
    }

    public void setPermiteComprovanteCancelamento(boolean permiteComprovanteCancelamento) {
        this.permiteComprovanteCancelamento = permiteComprovanteCancelamento;
    }

    public boolean isPermiteComprovantePagamento() {
        return permiteComprovantePagamento;
    }

    public void setPermiteComprovantePagamento(boolean permiteComprovantePagamento) {
        this.permiteComprovantePagamento = permiteComprovantePagamento;
    }

    public String getNsu() {
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public Integer getRecibo() {
        return recibo;
    }

    public void setRecibo(Integer recibo) {
        this.recibo = recibo;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodConvenio() {
        return codConvenio;
    }

    public void setCodConvenio(Integer codConvenio) {
        this.codConvenio = codConvenio;
    }

    public String getData_cancelamento() {
        return data_cancelamento;
    }

    public void setData_cancelamento(String data_cancelamento) {
        this.data_cancelamento = data_cancelamento;
    }

    public Integer getParcelamento() {
        return parcelamento;
    }

    public void setParcelamento(Integer parcelamento) {
        this.parcelamento = parcelamento;
    }

    public Integer getQtd_parcelas() {
        return qtd_parcelas;
    }

    public void setQtd_parcelas(Integer qtd_parcelas) {
        this.qtd_parcelas = qtd_parcelas;
    }

    public String getSituacao_descricao() {
        return situacao_descricao;
    }

    public void setSituacao_descricao(String situacao_descricao) {
        this.situacao_descricao = situacao_descricao;
    }
}

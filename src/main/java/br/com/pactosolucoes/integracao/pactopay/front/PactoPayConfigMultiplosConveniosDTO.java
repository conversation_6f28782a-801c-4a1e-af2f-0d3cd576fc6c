package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PactoPayConfigMultiplosConveniosDTO extends SuperTO {

    private boolean ativo = false;
    private Integer qtd_tentativas;
    private List<PactoPayConfigConvenioDTO> convenios;

    public PactoPayConfigMultiplosConveniosDTO() {

    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getQtd_tentativas() {
        return qtd_tentativas;
    }

    public void setQtd_tentativas(Integer qtd_tentativas) {
        this.qtd_tentativas = qtd_tentativas;
    }

    public List<PactoPayConfigConvenioDTO> getConvenios() {
        if (convenios == null) {
            convenios = new ArrayList<>();
        }
        return convenios;
    }

    public void setConvenios(List<PactoPayConfigConvenioDTO> convenios) {
        this.convenios = convenios;
    }
}

package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PactoPayConfigDTO extends SuperTO {

    private Integer codigo;
    private Integer empresa;
    private boolean envio_automatico_cobranca = true;
    private boolean sms_configurado = false;
    private boolean email_configurado = false;
    private boolean whatsapp_configurado = false;
    private boolean gymbotpro_configurado = false;
    private boolean app_configurado = false;
    private boolean email_wagi_configurado = false;
    private PactoPayConfigCobrancaAntecipadaDTO cobranca_antecipada;
    private PactoPayConfigResultadoCobrancaDTO comunicado_resultado;
    private PactoPayConfigCobrancaPendenteDTO comunicado_atraso;
    private PactoPayConfigMultiplosConveniosDTO multiplos_convenios;
    private PactoPayConfigComunicadoCartaoDTO comunicado_cartao;
    private PactoPayConfigRetentativaDTO retentativa;
    private PactoPayConfiguracaoEmailDTO configuracao_email;
    private PactoPayConfiguracaoGymBotDTO configuracao_gymbot;
    private PactoPayConfiguracaoGymBotProDTO configuracao_gymbotpro;
    private Integer qtdDiasEficienciaComunicacao;

    public PactoPayConfigDTO() {

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public boolean isEnvio_automatico_cobranca() {
        return envio_automatico_cobranca;
    }

    public void setEnvio_automatico_cobranca(boolean envio_automatico_cobranca) {
        this.envio_automatico_cobranca = envio_automatico_cobranca;
    }

    public PactoPayConfigCobrancaAntecipadaDTO getCobranca_antecipada() {
        if (cobranca_antecipada == null) {
            cobranca_antecipada = new PactoPayConfigCobrancaAntecipadaDTO();
        }
        return cobranca_antecipada;
    }

    public void setCobranca_antecipada(PactoPayConfigCobrancaAntecipadaDTO cobranca_antecipada) {
        this.cobranca_antecipada = cobranca_antecipada;
    }

    public PactoPayConfigResultadoCobrancaDTO getComunicado_resultado() {
        if (comunicado_resultado == null) {
            comunicado_resultado = new PactoPayConfigResultadoCobrancaDTO();
        }
        return comunicado_resultado;
    }

    public void setComunicado_resultado(PactoPayConfigResultadoCobrancaDTO comunicado_resultado) {
        this.comunicado_resultado = comunicado_resultado;
    }

    public PactoPayConfigCobrancaPendenteDTO getComunicado_atraso() {
        if (comunicado_atraso == null) {
            comunicado_atraso = new PactoPayConfigCobrancaPendenteDTO();
        }
        return comunicado_atraso;
    }

    public void setComunicado_atraso(PactoPayConfigCobrancaPendenteDTO comunicado_atraso) {
        this.comunicado_atraso = comunicado_atraso;
    }

    public PactoPayConfigMultiplosConveniosDTO getMultiplos_convenios() {
        if (multiplos_convenios == null) {
            multiplos_convenios = new PactoPayConfigMultiplosConveniosDTO();
        }
        return multiplos_convenios;
    }

    public void setMultiplos_convenios(PactoPayConfigMultiplosConveniosDTO multiplos_convenios) {
        this.multiplos_convenios = multiplos_convenios;
    }

    public PactoPayConfigComunicadoCartaoDTO getComunicado_cartao() {
        if (comunicado_cartao == null) {
            comunicado_cartao = new PactoPayConfigComunicadoCartaoDTO();
        }
        return comunicado_cartao;
    }

    public void setComunicado_cartao(PactoPayConfigComunicadoCartaoDTO comunicado_cartao) {
        this.comunicado_cartao = comunicado_cartao;
    }

    public PactoPayConfigRetentativaDTO getRetentativa() {
        if (retentativa == null) {
            retentativa = new PactoPayConfigRetentativaDTO();
        }
        return retentativa;
    }

    public void setRetentativa(PactoPayConfigRetentativaDTO retentativa) {
        this.retentativa = retentativa;
    }

    public boolean isSms_configurado() {
        return sms_configurado;
    }

    public void setSms_configurado(boolean sms_configurado) {
        this.sms_configurado = sms_configurado;
    }

    public boolean isEmail_configurado() {
        return email_configurado;
    }

    public void setEmail_configurado(boolean email_configurado) {
        this.email_configurado = email_configurado;
    }

    public boolean isEmail_wagi_configurado() {
        return email_wagi_configurado;
    }

    public void setEmail_wagi_configurado(boolean email_wagi_configurado) {
        this.email_wagi_configurado = email_wagi_configurado;
    }

    public PactoPayConfiguracaoEmailDTO getConfiguracao_email() {
        return configuracao_email;
    }

    public void setConfiguracao_email(PactoPayConfiguracaoEmailDTO configuracao_email) {
        this.configuracao_email = configuracao_email;
    }

    public boolean isWhatsapp_configurado() {
        return whatsapp_configurado;
    }

    public void setWhatsapp_configurado(boolean whatsapp_configurado) {
        this.whatsapp_configurado = whatsapp_configurado;
    }

    public boolean isGymbotpro_configurado() {
        return gymbotpro_configurado;
    }

    public void setGymbotpro_configurado(boolean gymbotpro_configurado) {
        this.gymbotpro_configurado = gymbotpro_configurado;
    }

    public boolean isApp_configurado() {
        return app_configurado;
    }

    public void setApp_configurado(boolean app_configurado) {
        this.app_configurado = app_configurado;
    }

    public PactoPayConfiguracaoGymBotDTO getConfiguracao_gymbot() {
        return configuracao_gymbot;
    }

    public void setConfiguracao_gymbot(PactoPayConfiguracaoGymBotDTO configuracao_gymbot) {
        this.configuracao_gymbot = configuracao_gymbot;
    }

    public Integer getQtdDiasEficienciaComunicacao() {
        return qtdDiasEficienciaComunicacao;
    }

    public void setQtdDiasEficienciaComunicacao(Integer qtdDiasEficienciaComunicacao) {
        this.qtdDiasEficienciaComunicacao = qtdDiasEficienciaComunicacao;
    }

    public PactoPayConfiguracaoGymBotProDTO getConfiguracao_gymbotpro() {
        return configuracao_gymbotpro;
    }

    public void setConfiguracao_gymbotpro(PactoPayConfiguracaoGymBotProDTO configuracao_gymbotpro) {
        this.configuracao_gymbotpro = configuracao_gymbotpro;
    }
}

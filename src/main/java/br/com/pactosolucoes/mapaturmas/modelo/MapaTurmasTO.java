package br.com.pactosolucoes.mapaturmas.modelo;

import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import negocio.comuns.arquitetura.SuperTO;

public class MapaTurmasTO extends SuperTO {

    private static final long serialVersionUID = -836588858395390198L;
    private DiaSemanaMapaTO segunda = new DiaSemanaMapaTO(DiaSemana.SEGUNDA_FEIRA.getDescricaoSimples());
    private DiaSemanaMapaTO terca = new DiaSemanaMapaTO(DiaSemana.TERCA_FEIRA.getDescricaoSimples());
    private DiaSemanaMapaTO quarta = new DiaSemanaMapaTO(DiaSemana.QUARTA_FEIRA.getDescricaoSimples());
    private DiaSemanaMapaTO quinta = new DiaSemanaMapaTO(DiaSemana.QUINTA_FEIRA.getDescricaoSimples());
    private DiaSemanaMapaTO sexta = new DiaSemanaMapaTO(DiaSemana.SEXTA_FEIRA.getDescricaoSimples());
    private DiaSemanaMapaTO sabado = new DiaSemanaMapaTO(DiaSemana.SABADO.getDescricaoSimples());
    private DiaSemanaMapaTO domingo = new DiaSemanaMapaTO(DiaSemana.DOMINGO.getDescricaoSimples());
    private List<DiaSemanaMapaTO> lista = new ArrayList<DiaSemanaMapaTO>();

    public void completarAlunos() {
        for (DiaSemanaMapaTO dia : lista) {
            for (ItemMapaTurmasTO item : dia.getItens()) {
                item.completarAlunos();
            }
        }
    }

    public void povoarLista(String ordenacao) {
        lista = new ArrayList<DiaSemanaMapaTO>();
        segunda.ordenar(ordenacao);
        terca.ordenar(ordenacao);
        quarta.ordenar(ordenacao);
        quinta.ordenar(ordenacao);
        sexta.ordenar(ordenacao);
        sabado.ordenar(ordenacao);
        domingo.ordenar(ordenacao);
        lista.add(segunda);
        lista.add(terca);
        lista.add(quarta);
        lista.add(quinta);
        lista.add(sexta);
        lista.add(sabado);
        lista.add(domingo);
    }

    public void adicionarItem(ItemMapaTurmasTO item) {
        DiaSemana diaSemana = DiaSemana.getDiaSemana(item.getDiaSemana());
        switch (diaSemana) {
            case DOMINGO:
                domingo.getItens().add(item);
                break;
            case SEGUNDA_FEIRA:
                segunda.getItens().add(item);
                break;
            case TERCA_FEIRA:
                terca.getItens().add(item);
                break;
            case QUARTA_FEIRA:
                quarta.getItens().add(item);
                break;
            case QUINTA_FEIRA:
                quinta.getItens().add(item);
                break;
            case SEXTA_FEIRA:
                sexta.getItens().add(item);
                break;
            case SABADO:
                sabado.getItens().add(item);
                break;
        }
    }

    public DiaSemanaMapaTO getSegunda() {
        return segunda;
    }

    public void setSegunda(DiaSemanaMapaTO segunda) {
        this.segunda = segunda;
    }

    public DiaSemanaMapaTO getTerca() {
        return terca;
    }

    public void setTerca(DiaSemanaMapaTO terca) {
        this.terca = terca;
    }

    public DiaSemanaMapaTO getQuarta() {
        return quarta;
    }

    public void setQuarta(DiaSemanaMapaTO quarta) {
        this.quarta = quarta;
    }

    public DiaSemanaMapaTO getQuinta() {
        return quinta;
    }

    public void setQuinta(DiaSemanaMapaTO quinta) {
        this.quinta = quinta;
    }

    public DiaSemanaMapaTO getSexta() {
        return sexta;
    }

    public void setSexta(DiaSemanaMapaTO sexta) {
        this.sexta = sexta;
    }

    public DiaSemanaMapaTO getSabado() {
        return sabado;
    }

    public void setSabado(DiaSemanaMapaTO sabado) {
        this.sabado = sabado;
    }

    public DiaSemanaMapaTO getDomingo() {
        return domingo;
    }

    public void setDomingo(DiaSemanaMapaTO domingo) {
        this.domingo = domingo;
    }

    public List<DiaSemanaMapaTO> getLista() {
        return lista;
    }

    public void setLista(List<DiaSemanaMapaTO> lista) {
        this.lista = lista;
    }
}

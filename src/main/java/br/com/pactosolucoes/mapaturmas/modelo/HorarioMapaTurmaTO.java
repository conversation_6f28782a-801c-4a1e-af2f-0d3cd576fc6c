package br.com.pactosolucoes.mapaturmas.modelo;

import negocio.comuns.arquitetura.SuperTO;

import java.util.List;

public class HorarioMapaTurmaTO extends SuperTO {

    private String horario = "";
    private MapaTurmasTO mapa = new MapaTurmasTO();

    public HorarioMapaTurmaTO(String horario) {
        this.horario = horario;
    }

    public static HorarioMapaTurmaTO obterHorario(String horario, List<HorarioMapaTurmaTO> lista) {
        HorarioMapaTurmaTO hmpt = new HorarioMapaTurmaTO(horario);
        int indexOf = lista.indexOf(hmpt);
        if (indexOf >= 0) {
            return lista.get(indexOf);
        } else
            lista.add(hmpt);
        return hmpt;
    }

    public int getHorarioNumeral() {
        try {
            return Integer.valueOf(horario.replaceAll(":", ""));
        } catch (Exception e) {
            return 0;
        }
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public MapaTurmasTO getMapa() {
        return mapa;
    }

    public void setMapa(MapaTurmasTO mapa) {
        this.mapa = mapa;
    }

    public boolean equals(Object o) {
        boolean equal = false;
        if (o instanceof HorarioMapaTurmaTO) {
            HorarioMapaTurmaTO item = (HorarioMapaTurmaTO) o;
            if (this.getHorario().equals(item.getHorario())) {
                equal = true;
            }
        }
        return equal;

    }
}

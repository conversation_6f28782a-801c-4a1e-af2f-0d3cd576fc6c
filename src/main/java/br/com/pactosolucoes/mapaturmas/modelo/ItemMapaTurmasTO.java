package br.com.pactosolucoes.mapaturmas.modelo;

import java.util.ArrayList;
import java.util.List;


import negocio.comuns.utilitarias.Uteis;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import negocio.comuns.arquitetura.SuperTO;

public class ItemMapaTurmasTO extends SuperTO {

    private static final long serialVersionUID = -4566294093832504839L;
    private String nomeProfessor = "";
    private int codigoProfessor;
    private String horarioInicial = "";
    private String horarioFinal = "";
    private String ambiente = "";
    private String nivel = "";
    private String modalidade = "";
    private int hora;
    private int minuto;
    private String nomeTurma;
    private Integer nrMaxAlunos = 0;
    private Integer vagas = 0;
    private List<AlunoMapaTurmasTO> alunos = new ArrayList<AlunoMapaTurmasTO>();
    private String diaSemana = "";
    private boolean horarioAtivo;

    public JRDataSource getAlunosJr() {
        alunos.addAll(completarAlunos());
        JRDataSource jr1 = new JRBeanArrayDataSource(alunos.toArray());
        return jr1;
    }


    public String getNrMaxApresentar() {
        return nrMaxAlunos.toString().length() > 1 ? nrMaxAlunos.toString() : "0" + nrMaxAlunos.toString();
    }

    public String getVagasApresentar() {
        return vagas.toString().length() > 1 ? vagas.toString() : "0" + vagas.toString();
    }

    public String getLabelDiaSemana() {
        DiaSemana diaSemana = DiaSemana.getDiaSemana(getDiaSemana());
        if (diaSemana != null) {
            return diaSemana.getDescricaoSimples();
        }
        return "";
    }

    public List<AlunoMapaTurmasTO> completarAlunos() {
        List<AlunoMapaTurmasTO> alunosT = new ArrayList<AlunoMapaTurmasTO>();
        for (int i = alunos.size(); i < nrMaxAlunos; i++) {
            alunosT.add(new AlunoMapaTurmasTO(i + 1));
        }
        return alunosT;
    }

    public String getNomeProfessor() {
        return Uteis.getNomeAbreviado(nomeProfessor);
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public int getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(int codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horario) {
        this.horarioInicial = horario;
    }

    public int getHora() {
        return hora;
    }

    public void setHora(int hora) {
        this.hora = hora;
    }

    public int getMinuto() {
        return minuto;
    }

    public void setMinuto(int minuto) {
        this.minuto = minuto;
    }

    public String getNomeTurma() {
        return nomeTurma;
    }

    public void setNomeTurma(String nomeTurma) {
        this.nomeTurma = nomeTurma;
    }

    public int getNrMaxAlunos() {
        return nrMaxAlunos;
    }

    public void setNrMaxAlunos(int nrMaxAlunos) {
        this.nrMaxAlunos = nrMaxAlunos;
    }

    public List<AlunoMapaTurmasTO> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<AlunoMapaTurmasTO> alunos) {
        this.alunos = alunos;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setVagas(int vagas) {
        this.vagas = vagas;
    }

    public int getVagas() {
        return vagas;
    }

    public int getNrMatriculas() {
        return alunos.size();
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getNivel() {
        return nivel;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public boolean isHorarioAtivo() {
        return horarioAtivo;
    }

    public void setHorarioAtivo(boolean horarioAtivo) {
        this.horarioAtivo = horarioAtivo;
    }
}

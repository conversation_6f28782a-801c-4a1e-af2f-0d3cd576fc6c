package br.com.pactosolucoes.mapaturmas.modelo;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;

import negocio.comuns.utilitarias.Ordenacao;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;

public class DiaSemanaMapaTO extends SuperTO {

    private static final long serialVersionUID = 6858755887085215367L;
    private String label = "";
    private List<ItemMapaTurmasTO> itens = new ArrayList<ItemMapaTurmasTO>();

    public void ordenar(String ordem) {
        Ordenacao.ordenarLista(itens,ordem);
    }

    public JRDataSource getItensJr() {
        JRDataSource jr1 = new JRBeanArrayDataSource(itens.toArray());
        return jr1;
    }

    public DiaSemanaMapaTO(String label) {
        this.label = label;
    }

    public int getSize() {
        return itens.size();
    }

    public void setItens(List<ItemMapaTurmasTO> itens) {
        this.itens = itens;
    }

    public List<ItemMapaTurmasTO> getItens() {
        return itens;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }
}

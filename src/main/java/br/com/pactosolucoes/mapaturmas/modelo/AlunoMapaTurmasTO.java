package br.com.pactosolucoes.mapaturmas.modelo;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;

public class AlunoMapaTurmasTO extends SuperTO {

    private int codigoCliente = 0;
    private String nome = "";
    private String matricula = "";
    private int codigo = 0;

    public AlunoMapaTurmasTO() {
    }

    public AlunoMapaTurmasTO(Integer codigo) {
        this.codigo = codigo;
    }

    public void setCodigoCliente(int codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public int getCodigoCliente() {
        return codigoCliente;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getMatricula() {
        return matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getCodigo() {
        return codigo;
    }
}

package br.com.pactosolucoes.comuns.notificacao;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import org.apache.commons.lang.StringUtils;
import servicos.impl.stone.StoneOnlineService;
import servicos.impl.stone.xml.authorization.response.ResponseRspn;

/**
 * <AUTHOR>
 * @since 12/02/19
 */
public enum RecursoSistema {

    GESTAO_DE_TRANSACOES("GESTAO_DE_TRANSACOES"),
    GESTAO_DE_REMESSAS("GESTAO_DE_REMESSAS"),
    GESTAO_DE_BOLETOS_ONLINE("GESTAO_DE_BOLETOS_ONLINE"),
    GESTAO_DE_ARMARIOS("GESTAO_DE_ARMARIOS"),
    O_QUE_HA_DE_NOVO_RODAPE("oQueHaDeNovoRodape"),
    LOGIN("LOGIN"),
    GESTAO_DE_CARTEIRAS("GESTAO_DE_CARTEIRAS"),
    CANAL_PACTO_DOWNLOAD_BOLETO("CANAL_PACTO_DOWNLOAD_BOLETO"),
    CONVITE_CLIENTE("CONVITE_CLIENTE"),
    CAIXA_EM_ABERTO("CAIXA_EM_ABERTO"),
    NEGOCIACAO("NEGOCIACAO"),
    VENDA_AVULSA("VENDA_AVULSA"),
    DIARIA("DIARIA"),
    FREE_PASS("FREE_PASS"),
    CONSULTA_DE_TURMAS("CONSULTA_DE_TURMAS"),
    RELATORIO_FREQUENCIA_TURMAS("RELATORIO_FREQUENCIA_TURMAS"),
    MAPA_TURMAS("MAPA_TURMAS"),
    GESTAO_PERSONAL("GESTAO_PERSONAL"),
    RELATORIO_DE_PERSONAL("RELATORIO_DE_PERSONAL"),
    GESTAO_DE_COMISSAO("GESTAO_DE_COMISSAO"),
    COMISSAO_VARIAVEL("COMISSAO_VARIAVEL"),
    CADASTROS_AUXILIARES("CADASTROS_AUXILIARES"),
    CADASTROS_PRODUTOS_TURMAS_PLANOS("CADASTROS_PRODUTOS_TURMAS_PLANOS"),
    CADASTROS_CONFIG_FINANCEIRA("CADASTROS_CONFIG_FINANCEIRA"),
    CADASTROS_ACESSO_SISTEMA("CADASTROS_ACESSO_SISTEMA"),
    CADASTROS_CONFIG_CONTRATO("CADASTROS_CONFIG_CONTRATO"),
    COLABORADOR("COLABORADOR"),
    CLIENTE("CLIENTE"),
    CLASSIFICACAO("CLASSIFICACAO"),
    QUESTIONARIO("QUESTIONARIO"),
    CATEGORIA_CLIENTES("CATEGORIA_CLIENTES"),
    PROFISSAO("PROFISSAO"),
    GRAU_DE_INSTRUCAO("GRAU_DE_INSTRUCAO"),
    GRUPO_DESCONTO("GRUPO_DESCONTO"),
    PARENTESCO("PARENTESCO"),
    PAIS("PAIS"),
    CIDADE("CIDADE"),
    PERGUNTA("PERGUNTA"),
    CATEGORIA_PRODUTO("CATEGORIA_PRODUTO"),
    PRODUTO("PRODUTO"),
    BRINDE("BRINDE"),
    BRINDE_RESGATADO_SUCESSO("BRINDE_RESGATADO_COM_SUCESSO"),
    LANCAMENTO_PRODUTO_COLETIVO("LANCAMENTO_PRODUTO_COLETIVO"),
    CONTROLE_ESTOQUE("CONTROLE_ESTOQUE"),
    TAMANHO_ARMARIO("TAMANHO_ARMARIO"),
    PLANO("PLANO"),
    MODALIDADE("MODALIDADE"),
    DESCONTO("DESCONTO"),
    PACOTE("PACOTE"),
    CONDICAO_DE_PAGAMENTO("CONDICAO_DE_PAGAMENTO"),
    CONVENIO_DE_DESCONTO("CONVENIO_DE_DESCONTO"),
    TIPO_PLANO("TIPO_PLANO"),
    AMBIENTE("AMBIENTE"),
    NIVEL_TURMA("NIVEL_TURMA"),
    TURMA("TURMA"),
    HORARIO("HORARIO"),
    BALANCO("BALANCO"),
    CARDEX("CARDEX"),
    COMPRA("COMPRA"),
    CONFIGURAR_PRODUTO_ESTOQUE("CONFIGURAR_PRODUTO_ESTOQUE"),
    POSICAO_DO_ESTOQUE("POSICAO_DO_ESTOQUE"),
    CONVITE_AULA_EXPERIMENTAL("CONVITE_AULA_EXPERIMENTAL"),
    CAMPANHA_CUPOM_DESCONTO("CAMPANHA_CUPOM_DESCONTO"),
    INDICE_FINANCEIRO_REAJUSTE_PRECO("INDICE_FINANCEIRO_REAJUSTE_PRECO"),
    DEPARTAMENTO("DEPARTAMENTO"),
    DADOS_USUSARIO("DADOS_USUSARIO"),
    CONSULTA_DE_RECIBOS("CONSULTA_DE_RECIBOS"),
    CONSULTA_DE_CUPONS_FISCAIS("CONSULTA_DE_CUPONS_FISCAIS"),
    GESTAO_NOTAS("GESTAO_NOTAS"),
    GESTAO_NFCE("GESTAO_NFCE"),
    CONFIGURACAO_NOTAFISCAL("CONFIGURACAO_NOTAFISCAL"),
    VENDA_CONSUMIDOR("VENDA_CONSUMIDOR"),
    MOVIMENTO_CC_CLIENTE("MOVIMENTO_CC_CLIENTE"),
    BANCO("BANCO"),
    CONTA_CORRENTE("CONTA_CORRENTE"),
    TIPO_REMESSA("TIPO_REMESSA"),
    TIPO_RETORNO("TIPO_RETORNO"),
    CONVENIO_COBRANCA("CONVENIO_COBRANCA"),
    FORMA_PAGAMENTO("FORMA_PAGAMENTO"),
    OPERADORA_CARTAO("OPERADORA_CARTAO"),
    METAS_FINANCEIRO("METAS_FINANCEIRO"),
    TAXA_COMISSAO("TAXA_COMISSAO"),
    EMPRESA("EMPRESA"),
    PERFIL_ACESSO("PERFIL_ACESSO"),
    PERFIL_ACESSO_UNIFICADO("PERFIL_ACESSO_UNIFICADO"),
    USUARIO("USUARIO"),
    LOCAL_ACESSO("LOCAL_ACESSO"),
    SERVIDOR_FACIAL("SERVIDOR_FACIAL"),
    IMPORTACAO("IMPORTACAO"),
    CONTROLE_LOG("CONTROLE_LOG"),
    AUTORIZACAO_ACESSO("AUTORIZACAO_ACESSO"),
    ATUALIZACOES_BD("ATUALIZACOES_BD"),
    INTEGRACAO_ACESSO("INTEGRACAO_ACESSO"),
    GERADOR_CONSULTAS("GERADOR_CONSULTAS"),
    SORTEIO("SORTEIO"),
    OPERACOES_COLETIVAS("OPERACOES_COLETIVAS"),
    MODELO_CONTRATO("MODELO_CONTRATO"),
    MODELO_ORCAMENTO("MODELO_ORCAMENTO"),
    RELATORIO_CLIENTES_ORCAMENTOS("RELATORIO_CLIENTES_ORCAMENTOS"),
    ORCAMENTO("ORCAMENTO"),
    MOVIMENTO_PRODUTO("MOVIMENTO_PRODUTO"),
    JUSTIFICATIVA_OPERACAO("JUSTIFICATIVA_OPERACAO"),
    IMPRIME_RECIBO_BANCO("IMPRIME_RECIBO_BANCO"),
    GERAL_CLIENTES("GERAL_CLIENTES"),
    RELATORIO_VISITANTES("RELATORIO_VISITANTES"),
    RELATORIO_CLIENTES_CANCELADOS("RELATORIO_CLIENTES_CANCELADOS"),
    RELATORIO_CLIENTES_TRANCADOS("RELATORIO_CLIENTES_TRANCADOS"),
    RELATORIO_BONUS("RELATORIO_BONUS"),
    RELATORIO_ATESTADO("RELATORIO_ATESTADO"),
    CONTRATOS_DURACAO("CONTRATOS_DURACAO"),
    ANIVERSARIANTES("ANIVERSARIANTES"),
    LISTA_ACESSOS("LISTA_ACESSOS"),
    INDICADOR_ACESSOS("INDICADOR_ACESSOS"),
    TOTALIZADOR_ACESSOS("TOTALIZADOR_ACESSOS"),
    TOTALIZADOR_TICKETS("TOTALIZADOR_TICKETS"),
    FECHAMENTO_ACESSOS("FECHAMENTO_ACESSOS"),
    LISTA_CLIENTES_SIMPLIFICADA("LISTA_CLIENTES_SIMPLIFICADA"),
    SALDO_CREDITO("SALDO_CREDITO"),
    LISTA_CHAMADA("LISTA_CHAMADA"),
    FREQUENCIA_OCUPACAO_TURMAS("FREQUENCIA_OCUPACAO_TURMAS"),
    FECHAMENTO_CAIXA_OPERADOR("FECHAMENTO_CAIXA_OPERADOR"),
    COMPETENCIA_MENSAL("COMPETENCIA_MENSAL"),
    FATURAMENTO_PERIODO("FATURAMENTO_PERIODO"),
    FATURAMENTO_RECEBIDO_PERIODO("FATURAMENTO_RECEBIDO_PERIODO"),
    RECEITA_PERIODO("RECEITA_PERIODO"),
    RELATORIO_PARCELAS("RELATORIO_PARCELAS"),
    RELATORIO_PARCELAS2("RELATORIO_PARCELAS2"),
    GESTAO_NEGATIVACOES("GESTAO_NEGATIVACOES"),
    RELATORIO_CONSOLIDADO_PARCELAS("RELATORIO_CONSOLIDADO_PARCELAS"),
    RELATORIO_PRODUTOS("RELATORIO_PRODUTOS"),
    SALDO_CONTA_CORRENTE("SALDO_CONTA_CORRENTE"),
    PREVISAO_RENOVACAO_CONTRATO("PREVISAO_RENOVACAO_CONTRATO"),
    RELATORIO_CLIENTES("RELATORIO_CLIENTES"),
    RELATORIO_BVS("RELATORIO_BVS"),
    RELATORIO_REPASSE("RELATORIO_REPASSE"),
    RELATORIO_GERAL_ARMARIOS("RELATORIO_GERAL_ARMARIOS"),
    HISTORICO_PONTOS_ALUNO("HISTORICO_PONTOS_ALUNO"),
    DESCONTO_OCUPACAO_TURMAS("DESCONTO_OCUPACAO_TURMAS"),
    CARTEIRAS("CARTEIRAS"),
    CRM_META_EXTA("CRM_META_EXTA"),
    GRUPO_COLABORADOR("GRUPO_COLABORADOR"),
    EVENTO("EVENTO"),
    OBJECAO("OBJECAO"),
    MODELO_MENSAGEM("MODELO_MENSAGEM"),
    TEXTO_PADRAO("TEXTO_PADRAO"),
    FERIADO("FERIADO"),
    MARCAR_COMPARECIMENTO("MARCAR_COMPARECIMENTO"),
    MAILING("MAILING"),
    CONSULTA_HISTORICO_CONTATO("CONSULTA_HISTORICO_CONTATO"),
    TOTALIZADOR_METAS("TOTALIZADOR_METAS"),
    PASSIVO("PASSIVO"),
    INDICACAO("INDICACAO"),
    CONTATO_APP("CONTATO_APP"),
    AGENDAMENTOS_CRM("AGENDAMENTOS_CRM"),
    LANCAMENTOS("LANCAMENTOS"),
    LANCAMENTO_CONTA_RAPIDO("LANCAMENTO_CONTA_RAPIDO"),
    CONTAS_PAGAR("CONTAS_PAGAR"),
    NOVA_CONTA_PAGAR("NOVA_CONTA_PAGAR"),
    CONTAS_RECEBER("CONTAS_RECEBER"),
    NOVA_CONTAS_RECEBER("NOVA_CONTAS_RECEBER"),
    ULTIMOS_LANCAMENTOS("ULTIMOS_LANCAMENTOS"),
    RECEBIVEIS("RECEBIVEIS"),
    LOTES("LOTES"),
    FORNCEDOR("FORNCEDOR"),
    PESSOA("PESSOA"),
    CAIXA_ADIMISTRATIVO("CAIXA_ADIMISTRATIVO"),
    CONSULTAR_CAIXA("CONSULTAR_CAIXA"),
    ABRIR_CAIXA("ABRIR_CAIXA"),
    FECHAR_CAIXA("FECHAR_CAIXA"),
    RESUMO_CONTAS("RESUMO_CONTAS"),
    FIN_CADASTROS_AUXILIARES("FIN_CADASTROS_AUXILIARES"),
    FIN_CONFIG_FINANCEIRAS("FIN_CONFIG_FINANCEIRAS"),
    FINAN_CONTA("FINAN_CONTA"),
    RESUMO_CONTAS_MOVIMENTACAO("RESUMO_CONTAS_MOVIMENTACAO"),
    DEMONSTRATIVO_FINAN("DEMONSTRATIVO_FINAN"),
    FECHAMENTO_CAIXA_PLANO_CONTAS("FECHAMENTO_CAIXA_PLANO_CONTAS"),
    DRE("DRE"),
    FLUXO_CAIXA_FINAN("FLUXO_CAIXA_FINAN"),
    MOVIMENTACOES_FINAN("MOVIMENTACOES_FINAN"),
    RELATORIO_DEVOLUCAO_CHEQUE("RELATORIO_DEVOLUCAO_CHEQUE"),
    RATEIO_INTEGRACAO("RATEIO_INTEGRACAO"),
    PLANO_CONTAS("PLANO_CONTAS"),
    CHEQUES_CARTOES_AVULSOS("CHEQUES_CARTOES_AVULSOS"),
    ESTORNO_PAGAMENTO("ESTORNO_PAGAMENTO"),
    CENTRO_CUSTOS("CENTRO_CUSTOS"),
    TIPO_CONTA("TIPO_CONTA"),
    CONTA_CONTABIL("CONTA_CONTABIL"),
    TIPO_DOCUMENTO("TIPO_DOCUMENTO"),
    SIMULAR_ORCAMENTO("SIMULAR_ORCAMENTO"),
    LISTA_PROSPECTS("LISTA_PROSPECTS"),
    AGENDA_VISISTA("AGENDA_VISISTA"),
    CONVERSAS("CONVERSAS"),
    CAIXA_EM_ABERTO_CE("CAIXA_EM_ABERTO_CE"),
    PESQUISA_GERAL("PESQUISA_GERAL"),
    GESTAO_CREDITO("GESTAO_CREDITO"),
    TIPO_AMBIENTE("TIPO_AMBIENTE"),
    PERFIL_EVENTO("PERFIL_EVENTO"),
    SERVICOS("SERVICOS"),
    CADASTRO_INICIAL_CE("CADASTRO_INICIAL_CE"),
    AGENDA_EVENTOS("AGENDA_EVENTOS"),
    ANIVERSARIANTES_CE("ANIVERSARIANTES_CE"),
    FECHAMENTO_CAIXA_CE("FECHAMENTO_CAIXA_CE"),
    RECEITA_PERIODO_CE("RECEITA_PERIODO_CE"),
    PARCELA_ABERTO("PARCELA_ABERTO"),
    SALDO_CONTA_CORRENT("SALDO_CONTA_CORRENT"),
    CATEGORIA_CLIENTE_CE("CATEGORIA_CLIENTE_CE"),
    CLIENTE_CE("CLIENTE_CE"),
    COLABORADOR_CE("COLABORADOR_CE"),
    GRAU_DE_INSTRUCAO_CE("GRAU_DE_INSTRUCAO_CE"),
    PROFISSAO_CE("PROFISSAO_CE"),
    PAIS_CE("PAIS_CE"),
    CIDADE_CE("CIDADE_CE"),
    PRODUTO_LOCACAO("PRODUTO_LOCACAO"),
    EMPRESA_CE("EMPRESA_CE"),
    PERFIL_ACESSO_CE("PERFIL_ACESSO_CE"),
    USUARIO_CE("USUARIO_CE"),
    CONTROLE_LOG_CE("CONTROLE_LOG_CE"),
    FORNECEDOR_CE("FORNECEDOR_CE"),
    AMBIENTE_CE("AMBIENTE_CE"),
    ESTORNO_RECIBO_CE("ESTORNO_RECIBO_CE"),
    MOVIMENTO_CC_CLIENTE_CE("MOVIMENTO_CC_CLIENTE_CE"),
    FORMA_PAGAMENTO_CE("FORMA_PAGAMENTO_CE"),
    OPERADORA_CARTAO_CE("OPERADORA_CARTAO_CE"),
    COMISSAO_EST("COMISSAO_EST"),
    DIARIO("DIARIO"),
    AGENDAMENTOS("AGENDAMENTOS"),
    CLIENTES_SEM_SESSAO("CLIENTES_SEM_SESSAO"),
    CONFIG_EST("CONFIG_EST"),
    DISPONIBILIDADE_EST("DISPONIBILIDADE_EST"),
    VENDA_AVULSA_EST("VENDA_AVULSA_EST"),
    CAIXA_ABERTO_EST("CAIXA_ABERTO_EST"),
    PACOTE_EST("PACOTE_EST"),
    AGENDA_MENSAL_EST("AGENDA_MENSAL_EST"),
    AMBIENTE_EST("AMBIENTE_EST"),
    PROFISSIONAL_EST("PROFISSIONAL_EST"),
    INDIVIDUAL_EST("INDIVIDUAL_EST"),
    CONFIG_ZW("CONFIG_ZW"),
    GOOG_CALENDAR("GOOG_CALENDAR"),
    VELOCIMETRO("VELOCIMETRO"),
    DOC_VELOCIDADE("DOC_VELOCIDADE"),
    ALTERAR_SENHA("ALTERAR_SENHA"),
    UCP("UCP"),
    WIKI_PACTO("WIKI_PACTO"),
    CONFIG_CRM("CONFIG_CRM"),
    CONFIG_FIN("CONFIG_FIN"),
    SUPORTE("SUPORTE"),
    BOLETO("BOLETO"),
    CONFIG_ESTUDIO("CONFIG_ESTUDIO"),
    CONVITES_AULAS("CONVITES_AULAS"),
    BLOQUEIO_CAIXA("BLOQUEIO_CAIXA"),
    GYM_PASS_RELATORIO("GYM_PASS_RELATORIO"),
    REGISTRAR_ACESSO_AVULSO("REGISTRAR_ACESSO_AVULSO"),
    ADQUIRENTE("ADQUIRENTE"),
    CONTAS_A_PAGAR("CONTAS_A_PAGAR"),
    CONTAS_A_RECEBER("CONTAS_A_RECEBER"),
    IMPORTADOR_FINANCEIRO("IMPORTADOR_FINANCEIRO"),
    MAPA_ESTATISTICO("MAPA_ESTATISTICO"),
    HISTORICO_PONTOS_DOTZ("HISTORICO_PONTOS_DOTZ"),
    GESTAO_DE_TURMA("GESTAO_DE_TURMA"),
    META_DIARIA("META_DIARIA"),
    AGENDAMENTO_CONTA_PAGAR("AGENDAMENTO_CONTA_PAGAR"),
    CONCILIACAO("CONCILIACAO"),
    LIMPAR_CONTAS_SELECIONADAS_FINANCEIRO("limparContasSelecionadasFinanceiro"),
    QUITACAO_CONTA("QUITACAO_CONTA"),
    LAYOUT_LIVRO_CAIXA("LayoutLivroCaixa"),
    ADICIONAR_LISTA_PIN_PAD_FORMA_PAGAMENTO("adicionarListaPinPadFormaPagamento"),
    FLUXO_DE_CAIXA("Fluxo_de_caixa"),
    EXCLUSAO_MOVIMENTACOES_DO_FINANCEIRO("exclusaoMovimentacoesDoFinanceiro"),
    CONTAS_SELECIONADAS_FINANCEIRO("contasselecionadasfinanceiro"),
    TRANSFERIR_CARTEIRA_POR_QUANTIDADE("transferirCarteiraPorQuantidade"),
    TRANSFERIR_CARTEIRA_POR_SELECAO("transferirCarteiraPorSelecao"),
    BI_DE_ALUNOS_SEM_GEO_LOCALIZACAO("bidealunossemgeolocalizacao"),
    HISTORICO_DE_FALTAS_ALUNO_TURMA("historicodefaltasalunoturma"),
    EXCLUIR_CONTAS_SELECIONADAS_FINANCEIRO("excluirContasSelecionadasFinanceiro"),
    GYMPASS("GYMPASS"),
    TOTALPASS("TOTALPASS"),
    BOTAO_CONTAS_SELECIONADAS_FINANCEIRO("botaoContasSelecionadasFinanceiro"),
    NUMERO_PARCELAS_ADICIONADAS_TAXA_CARTAO("nrParcelasAdicionadasTaxaCartao"),
    ADQUIRENTES_ADICIONADAS_TAXA_CARTAO("adquirentesAdicionadasTaxaCartao"),
    OPERADORAS_ADICIONADAS_TAXA_CARTAO("operadorasAdicionadasTaxaCartao"),
    DEMONSTRATIVOFINANCEIRO("demonstrativoFinanceiro"),
    DRE_FINANCEIRO("dreFinanceiro"),
    MOV_FINANCEIRAS("movFinanceiras"),
    CHEQUES_DEVOLVIDOS_E_TROCADOS_FINANCEIRO("chequesDevolvidoseTrocadosFinanceiro"),
    FLUXO_DE_CAIXA_FINANCEIRO("fluxoDeCaixaFinanceiro"),
    BI_FINANCEIRO("biFinanceiro"),
    TRANSFERIR_TURMA_PROFESSOR_GESTAO_TURMA("transferirTurmaProfessorGestaoTurma"),
    TRANSFERIR_TURMA_PROFESSOR_GESTAO_TURMA_SEM_CONFLITO("transferirTurmaProfessorGestaoTurmaSemConflito"),
    CHAVE_DEVOLVIDA_ALUGUEL_ARMARIO("chaveDevolvidaAluguelArmario"),
    FILTRO_CONTAS_CENTRO_DE_CUSTO_DRE("filtroContasCentrosDeCustosDRE"),
    EDITAR_VALOR_PARCELAS_NEGOCIACAO_CONTRATO("editarValorParcelasNegociacaoContrato"),
    DOTZ_ACUMULO("DOTZ_ACUMULO"),
    DOTZ_ACUMULO_RECORRENCIA("DOTZ_ACUMULO_RECORRENCIA"),
    DOTZ_RESGATE("DOTZ_RESGATE"),
    GYM_PASS_SUCESSO("GYM_PASS_SUCESSO"),
    GOGOOD_SUCESSO("GOGOOD_SUCESSO"),
    GYM_PASS_ACESSO_SUCESSO("GYM_PASS_ACESSO_SUCESSO"),
    PESQUISA("PESQUISA"),
    RELATORIO_PESQUISA("RELATORIO_PESQUISA"),
    TIPO_MODALIDADE("TIPO_MODALIDADE"),
    GESTAO_FAMILIA("GESTAO_FAMILIA"),
    CLUBE_VANTAGENS_CONFIGURACOES("CLUBE_VANTAGENS_CONFIGURACOES"),
    CLUBE_VANTAGENS_CAMPANHA("CLUBE_VANTAGENS_CAMPANHA"),
    CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE("CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE"),
    CLUBE_VANTAGEM_HISTORICO_DE_PONTOS("CLUBE_DE_VANTEGENS_RELATORIO_HISTORICO_DE_PONTOS"),
    CLUBE_VANTAGENS_ATIVAR("CLUBE_VANTAGENS_ATIVAR"),
    /**
     * Deve sinalizar a ação de salvar (criar/alterar) um convênio de cobrança que seja do tipo {@link TipoConvenioCobrancaEnum#DCC_STONE_ONLINE}.
     */
    CRIAR_CONVENIO_STONE_ONLINE("criarConvenioStoneOnline"),
    /**
     * Deve sinalizar um pedido de autorização de cobrança realizado em {@link StoneOnlineService#tentarAprovacao(CartaoCreditoTO)}, logo no seu início,
     * ou seja, isto significa que nada ainda foi realizado, apenas o pedido pelo sistema.
     */
    STONE_TRANSACAO_AUTORIZACAO_PEDIDO("STONE_TRANSACAO_AUTORIZACAO_PEDIDO"),
    /**
     * Deve sinalizar um pedido de autorização de cobrança realizado em {@link StoneOnlineService#tentarAprovacao(CartaoCreditoTO)}, e que houve
     * retorno por parte da Stone, isso quer dizer, que o Status da transação não importa.
     */
    STONE_TRANSACAO_AUTORIZACAO_RESPOSTA_SUCESSO("STONE_TRANSACAO_AUTORIZACAO_RESPOSTA_SUCESSO"),
    /**
     * Deve sinalizar um pedido de autorização de cobrança realizado em {@link StoneOnlineService#tentarAprovacao(CartaoCreditoTO)}, e que obteve o
     * retorno de {@link ResponseRspn#APROVADO_APROVED}.
     */
    STONE_TRANSACAO_AUTORIZACAO_APROVADO("STONE_TRANSACAO_AUTORIZACAO_APROVADO"),
    /**
     * Deve sinalizar um pedido de autorização de cobrança realizado em {@link StoneOnlineService#tentarAprovacao(CartaoCreditoTO)}, e que obteve o
     * algum retorno <b>diferente de {@link ResponseRspn#APROVADO_APROVED}</b>.
     */
    STONE_TRANSACAO_AUTORIZACAO_NAO_APROVADO("STONE_TRANSACAO_AUTORIZACAO_NAO_APROVADO"),
    /**
     * Deve sinalizar um pedido de autorização de cobrança realizado em {@link StoneOnlineService#tentarAprovacao(CartaoCreditoTO)}, e que foi rejeitado o pedido,
     * isto signfica que a transação em si foi rejeitada, ou seja, <b>A mensagem enviada no pedido, possui um formato inválido!</b>
     * <br>
     * <h2>
     * Isto representa há algum erro na comunicação em {@link StoneOnlineService#tentarAprovacao(CartaoCreditoTO)} que deve ser tratado no sistema.
     * </h2>
     */
    STONE_TRANSACAO_AUTORIZACAO_REJEITADO("STONE_TRANSACAO_AUTORIZACAO_REJEITADO"),
    /**
     * Deve sinalizar um pedido de autorização de cobrança realizado em {@link StoneOnlineService#tentarAprovacao(CartaoCreditoTO)}, e que obteve
     * houve:
     *
     * <ul>
     * <li>Alguma falha na hora do envio ou</li>
     * <li>Recebimento do envio ou</li>
     * <li>Processamento do retorno</li>
     * </ul>
     */
    STONE_TRANSACAO_AUTORIZACAO_FALHA_ENVIO("STONE_TRANSACAO_AUTORIZACAO_FALHA_ENVIO"),
    /**
     * O mesmo de {@link #STONE_TRANSACAO_AUTORIZACAO_PEDIDO}, porém trata-se do cancelamento em {@link StoneOnlineService#cancelarTransacao(TransacaoVO, Boolean)}.
     */
    STONE_TRANSACAO_CANCELAMENTO_PEDIDO("STONE_TRANSACAO_CANCELAMENTO_PEDIDO"),
    /**
     * O mesmo de {@link #STONE_TRANSACAO_AUTORIZACAO_RESPOSTA_SUCESSO}, porém trata-se do cancelamento em {@link StoneOnlineService#cancelarTransacao(TransacaoVO, Boolean)}.
     */
    STONE_TRANSACAO_CANCELAMENTO_RESPOSTA_SUCESSO("STONE_TRANSACAO_CANCELAMENTO_RESPOSTA_SUCESSO"),
    /**
     * O mesmo de {@link #STONE_TRANSACAO_AUTORIZACAO_APROVADO}, porém trata-se do cancelamento em {@link StoneOnlineService#cancelarTransacao(TransacaoVO, Boolean)}.
     */
    STONE_TRANSACAO_CANCELAMENTO_APROVADO("STONE_TRANSACAO_CANCELAMENTO_APROVADO"),
    /**
     * O mesmo de {@link #STONE_TRANSACAO_AUTORIZACAO_NAO_APROVADO}, porém trata-se do cancelamento em {@link StoneOnlineService#cancelarTransacao(TransacaoVO, Boolean)}.
     */
    STONE_TRANSACAO_CANCELAMENTO_NAO_APROVADO("STONE_TRANSACAO_CANCELAMENTO_NAO_APROVADO"),
    /**
     * O mesmo de {@link #STONE_TRANSACAO_AUTORIZACAO_REJEITADO}, porém trata-se do cancelamento em {@link StoneOnlineService#cancelarTransacao(TransacaoVO, Boolean)}.
     */
    STONE_TRANSACAO_CANCELAMENTO_REJEITADO("STONE_TRANSACAO_CANCELAMENTO_REJEITADO"),
    /**
     * O mesmo de {@link #STONE_TRANSACAO_AUTORIZACAO_FALHA_ENVIO}, porém trata-se do cancelamento em {@link StoneOnlineService#cancelarTransacao(TransacaoVO, Boolean)}.
     */
    STONE_TRANSACAO_CANCELAMENTO_FALHA_ENVIO("STONE_TRANSACAO_CANCELAMENTO_FALHA_ENVIO"),

    GRUPO_RISCO_PESO_6("GRUPO_RISCO_PESO_6"),
    GRUPO_RISCO_PESO_7("GRUPO_RISCO_PESO_7"),
    GRUPO_RISCO_PESO_8("GRUPO_RISCO_PESO_8"),
    GRUPO_RISCO_VER_MAIS("GRUPO_RISCO_VER_MAIS"),
    GRUPO_RISCO_SEM_CONTATO("GRUPO_RISCO_SEM_CONTATO"),
    GRUPO_RISCO_IMPRIMIR("GRUPO_RISCO_IMPRIMIR"),
    ASSINATURA_DIGITAL_TOTALACESSOS_GERAL("ASSINATURA_DIGITAL_TOTALACESSOS_GERAL"),
    ASSINATURA_DIGITAL_TOTALSUCESSO("ASSINATURA_DIGITAL_TOTALSUCESSO"),
    ASSINATURA_DIGITAL_VISUALIZACAOANEXOS("ASSINATURA_DIGITAL_VISUALIZACAOANEXOS"),
    ASSINATURA_DIGITAL_CONTRATOSSELECIONADOS("ASSINATURA_DIGITAL_CONTRATOSSELECIONADOS"),
    ASSINATURA_DIGITAL_FOTOSALTERADAS("ASSINATURA_DIGITAL_FOTOSALTERADAS"),
    ASSINATURA_DIGITAL_TOTALACESSOS_VIANAVEGADOR("ASSINATURA_DIGITAL_TOTALACESSOS_VIANAVEGADOR"),
    CANCELANDO_TRANSFERINDO_DIAS_EM_CONTRATO("CANCELANDO_TRANSFERINDO_DIAS_EM_CONTRATO"),
    ADICIONAR_CAPPTA_FORMA_PAGAMENTO("ADICIONAR_CAPPTA_FORMA_PAGAMENTO"),
    ASSISTENTE_GESTAO("ASSISTENTE_GESTAO"),
    CANAL_PACTO("CANAL_PACTO"),
    CANAL_CLIENTE_PACTO_STORE("CANAL_CLIENTE_PACTO_STORE"),
    CANAL_CLIENTE_FATURAS("CANAL_CLIENTE_FATURAS"),
    APRENDA_MAIS_SOBRE_ESTA_TELA("APRENDA_MAIS_SOBRE_ESTA_TELA"),
    APRENDA_MAIS_SOBRE_ESTA_TELA_CLIQUE_DENTRO("APRENDA_MAIS_SOBRE_ESTA_TELA_CLIQUE_DENTRO"),
    CAIXA_EM_ABERTO_BTN_CONSULTAR("CAIXA_EM_ABERTO_BTN_CONSULTAR"),
    DRE_COMPETENCIA("DRE_COMPETENCIA"),
    DRE_RECEITA("DRE_RECEITA"),
    DRE_FATURAMENTO_RECEBIDO("DRE_FATURAMENTO_RECEBIDO"),
    DRE_FATURAMENTO("DRE_FATURAMENTO"),
    DRE_RECEITAPROVISAO("DRE_RECEITAPROVISAO"),
    GESTAO_DE_REMESSAS_CONSULTAR_REMESSAS("GESTAO_DE_REMESSAS_CONSULTAR_REMESSAS"),
    GESTAO_DE_REMESSAS_CONSULTAR_PARCELAS("GESTAO_DE_REMESSAS_CONSULTAR_PARCELAS"),
    GESTAO_DE_REMESSAS_CONSULTAR_REMESSAS_CANCELAMENTO("GESTAO_DE_REMESSAS_CONSULTAR_REMESSAS_CANCELAMENTO"),
    CLIQUE_BANNER_PRINCIPAL("CLIQUE_BANNER_PRINCIPAL"),
    CLIQUE_BANNER_CRM("CLIQUE_BANNER_CRM"),
    CLIQUE_BANNER_FINANCEIRO("CLIQUE_BANNER_FINANCEIRO"),
    CLIQUE_BANNER_CADASTRO("CLIQUE_BANNER_CADASTRO"),
    CLIQUE_BANNER_BI("CLIQUE_BANNER_BI"),
    GESTAO_RECEBIVEIS_FATURAMENTO_RECEBIDO("GESTAO_RECEBIVEIS_FATURAMENTO_RECEBIDO"),
    GESTAO_RECEBIVEIS_PERIODO_COMPENSACAO("GESTAO_RECEBIVEIS_PERIODO_COMPENSACAO"),
    PLANO_CONDICAO_PAGAMENTO("PLANO_CONDICAO_PAGAMENTO"),
    PLANO_DURACAO_PAGAMENTO("PLANO_DURACAO_PAGAMENTO"),
    NAO_VOLTAR_PAGINA("NAO_VOLTAR_PAGINA"),
    RELATORIO_CONVIDADOS("RELATORIO_CONVIDADOS"),
    MANUTENCAO_MODALIDADE("MANUTENCAO_MODALIDADE"),
    MANUTENCAO_MODALIDADE_SUCESSO("MANUTENCAO_MODALIDADE_SUCESSO"),
    AFASTAMENTO_CONTRATO("AFASTAMENTO_CONTRATO"),
    AFASTAMENTO_CARENCIA("AFASTAMENTO_CARENCIA"),
    AFASTAMENTO_CARENCIA_SUCESSO("AFASTAMENTO_CARENCIA_SUCESSO"),
    AFASTAMENTO_ATESTADO("AFASTAMENTO_ATESTADO"),
    AFASTAMENTO_ATESTADO_SUCESSO("AFASTAMENTO_ATESTADO_SUCESSO"),
    AFASTAMENTO_TRANCAMENTO("AFASTAMENTO_TRANCAMENTO"),
    AFASTAMENTO_TRANCAMENTO_SUCESSO("AFASTAMENTO_TRANCAMENTO_SUCESSO"),
    AFASTAMENTO_CANCELAMENTO("AFASTAMENTO_CANCELAMENTO"),
    AFASTAMENTO_CANCELAMENTO_SUCESSO("AFASTAMENTO_CANCELAMENTO_SUCESSO"),
    CONTRATO_CONCOMITANTE_MARCOU("CONTRATO_CONCOMITANTE_MARCOU"),
    CONTRATO_CONCOMITANTE_DESMARCOU("CONTRATO_CONCOMITANTE_DESMARCOU"),
    VENDA_RAPIDA("VENDA_RAPIDA"),
    VENDA_RAPIDA_SUCESSO("VENDA_RAPIDA_SUCESSO"),
    VENDA_RAPIDA_SUCESSO_BV("VENDA_RAPIDA_SUCESSO_BV"),
    CADASTRO_PLANO_ABA_EMPRESA_MARCOU("CADASTRO_PLANO_ABA_EMPRESA_MARCOU"),
    INDICE_FINANCEIRO_ESPECIFICO_MARCOU("INDICE_FINANCEIRO_ESPECIFICO_MARCOU"),
    CADASTRO_PLANO_ANUIDADE_PARCELA_ESPECIFICA_MARCOU("CADASTRO_PLANO_ANUIDADE_PARCELA_ESPECIFICA_MARCOU"),
    PERMISSAO_CONSULTAR_REDE_DE_EMPRESAS_MARCOU("PERMISSAO_CONSULTAR_REDE_DE_EMPRESAS_MARCOU"),
    PERMISSAO_CAIXA_EM_ABERTO_REDE_DE_EMPRESAS_MARCOU("PERMISSAO_CAIXA_EM_ABERTO_REDE_DE_EMPRESAS_MARCOU"),
    CANCELAMENTO_PROXIMA_PARCELA_EM_ABERTO_MARCOU("CANCELAMENTO_PROXIMA_PARCELA_EM_ABERTO_MARCOU"),
    CONVENIO_COBRANCA_GETNET_ONLINE("CONVENIO_COBRANCA_GETNET_ONLINE"),
    CONVENIO_COBRANCA_DCO_AUTOMATICO_MARCOU("CONVENIO_COBRANCA_DCO_AUTOMATICO_MARCOU"),
    RELATORIO_DE_CONVIDADOS("RELATORIO_DE_CONVIDADOS"),
    CONVIDADO_LANCADO_SUCESSO("CONVIDADO_LANCADO_SUCESSO"),
    HISTORICO_CONVIDADO_CLIENTE("HISTORICO_CONVIDADO_CLIENTE"),
    CUPOM_DESCONTO_NEGOCIACAO_PLANO("CUPOM_DESCONTO_NEGOCIACAO_PLANO"),
    CADASTRO_NOVA_CAMPANHA_CUPOM_DESCONTO("CADASTRO_NOVA_CAMPANHA_CUPOM_DESCONTO"),
    CADASTRO_BRINDE_POR_INDICACAO("CADASTRO_BRINDE_POR_INDICACAO"),
    MUDANCA_DE_PLANO("MUDANCA_DE_PLANO"),
    UPGRADE_PLANO_SUCESSO("UPGRADE_PLANO_SUCESSO"),
    DOWNGRADE_PLANO_SUCESSO("DOWNGRADE_PLANO_SUCESSO"),
    TRANSFERENCIA_DE_EMPRESA_PERFIL_ALUNO("TRANSFERENCIA_DE_EMPRESA_PERFIL_ALUNO"),
    TRANSFERENCIA_DE_EMPRESA_PERFIL_ALUNO_SUCESSO("TRANSFERENCIA_DE_EMPRESA_PERFIL_ALUNO_SUCESSO"),
    MIGRAR_PARCELA_E_PLANO_TRANSFERENCIA_MARCOU("MIGRAR_PARCELA_E_PLANO_TRANSFERENCIA_MARCOU"),
    NAO_ESTORNAR_CONTRATO_PARCELA_PAGA_DESMARCOU("NAO_ESTORNAR_CONTRATO_PARCELA_PAGA_DESMARCOU"),
    DEFINIR_PARCELAS_VALOR_DIFERENTE_MARCOU("DEFINIR_PARCELAS_VALOR_DIFERENTE_MARCOU"),
    MUDANCA_DE_VENCIMENTO_PERFIL_ALUNO("MUDANCA_DE_VENCIMENTO_PERFIL_ALUNO"),
    MUDANCA_DE_VENCIMENTO_PERFIL_ALUNO_SUCESSO("MUDANCA_DE_VENCIMENTO_PERFIL_ALUNO_SUCESSO"),
    FLUXO_DE_CAIXA_FILTRO_PARCELAS_RECORRENCIA("FLUXO_DE_CAIXA_FILTRO_PARCELAS_RECORRENCIA"),
    QUESTIONARIO_PERGUNTA_OBRIGATORIA_MARCOU("QUESTIONARIO_PERGUNTA_OBRIGATORIA_MARCOU"),
    ADICIONAR_TODAS_EMPRESAS_CADASTRO_USUARIO("ADICIONAR_TODAS_EMPRESAS_CADASTRO_USUARIO"),
    PLANO_PERSONAL_CADASTRO_PLANO_MARCOU("PLANO_PERSONAL_CADASTRO_PLANO_MARCOU"),
    PLANO_PERSONAL_LANCADO_SUCESSO("PLANO_PERSONAL_LANCADO_SUCESSO"),
    CADASTRO_USUARIO_NAO_PEDIR_PERMISSAO_DESMARCOU("CADASTRO_USUARIO_NAO_PEDIR_PERMISSAO_DESMARCOU"),
    REGISTRAR_NOVA_VISITA("REGISTRAR_NOVA_VISITA"),
    REGISTRAR_NOVA_VISITA_SUCESSO("REGISTRAR_NOVA_VISITA_SUCESSO"),
    CADASTRO_MODALIDADE_ADICIONAR_TODAS_EMPRESAS("CADASTRO_MODALIDADE_ADICIONAR_TODAS_EMPRESAS"),
    CADASTRO_MODALIDADE_ADICIONAR_EMPRESA("CADASTRO_MODALIDADE_ADICIONAR_EMPRESA"),
    //    CADASTRO_REPASSE_GYMPASS("CADASTRO_REPASSE_GYMPASS"),
    ABRIR_CLIENTE_TREINO_WEB_ATRAVES_ZW("ABRIR_CLIENTE_TREINO_WEB_ATRAVES_ZW"),
    ESTORNO_RECIBO_INFORMANDO_DATA_ESTORNO("ESTORNO_RECIBO_INFORMANDO_DATA_ESTORNO"),
    CONFIG_PLANO_CONTRATOS_INICIAM_EM_INFORMADO_SITE_OU_JUST("CONFIG_PLANO_CONTRATOS_INICIAM_EM_INFORMADO_SITE_OU_JUST"),
    EXCEL_RESUMIDO_GESTAO_RECEBIVEIS("EXCEL_RESUMIDO_GESTAO_RECEBIVEIS"),
    MENU_ACESSO_RAPIDO_FIXOU("MENU_ACESSO_RAPIDO_FIXOU"),
    MENU_ACESSO_RAPIDO_DESAFIXOU("MENU_ACESSO_RAPIDO_DESAFIXOU"),
    TRANSFERIR_ALUNOS_TURMA_EM_MASSA_SUCESSO("TRANSFERIR_ALUNOS_TURMA_EM_MASSA_SUCESSO"),
    VENDAS_ONLINE("VENDAS_ONLINE"),
    VENDAS_ONLINE_ADQUIRA("VENDAS_ONLINE_ADQUIRA"),
    CONTRATOS_AUTOTENDIMENTO("CONTRATOS_AUTOTENDIMENTO"),
    VENDA_AVULSA_PRODUTO_SERVICO("VENDA_AVULSA_PRODUTO_SERVICO"),
    VENDA_AVULSA_DIARIA("VENDA_AVULSA_DIARIA"),
    CADASTRO_PESQUISA("CADASTRO_PESQUISA"),
    COMPARTILHAR_LINK_PAGAMENTO_NEGOCIACAO("COMPARTILHAR_LINK_PAGAMENTO_NEGOCIACAO"),
    GESTAO_TRANSACAO("GESTAO_TRANSACAO"),
    NOTA_TODAS_FORMAS_PGTO_RECEITA("NOTA_TODAS_FORMAS_PGTO_RECEITA"),
    NOTA_TODAS_FORMAS_PGTO_COMPETENCIA("NOTA_TODAS_FORMAS_PGTO_COMPETENCIA"),
    NOTA_TODAS_FORMAS_PGTO_FATURAMENTO_DE_CAIXA("NOTA_TODAS_FORMAS_PGTO_FATURAMENTO_DE_CAIXA"),
    NOTA_TODAS_FORMAS_PGTO_FATURAMENTO("NOTA_TODAS_FORMAS_PGTO_FATURAMENTO"),
    NOTA_TODAS_FORMAS_PGTO_COMPETENCIA_QUITADA("NOTA_TODAS_FORMAS_PGTO_COMPETENCIA_QUITADA"),
    NOTA_TODAS_FORMAS_PGTO_COMPETENCIA_NAO_QUITADA("NOTA_TODAS_FORMAS_PGTO_COMPETENCIA_NAO_QUITADA"),
    NOTA_TODAS_FORMAS_PGTO_RECEITAPROVISAO("NOTA_TODAS_FORMAS_PGTO_RECEITAPROVISAO"),
    NOTA_TODAS_FORMAS_PGTO_COMPETENCIA_INDEPENDENTE_QUITACAO("NOTA_TODAS_FORMAS_PGTO_COMPETENCIA_INDEPENDENTE_QUITACAO"),
    NOTA_OUTRAS_FORMAS_PGTO_RECEITA("NOTA_OUTRAS_FORMAS_PGTO_RECEITA"),
    NOTA_OUTRAS_FORMAS_PGTO_COMPETENCIA("NOTA_OUTRAS_FORMAS_PGTO_COMPETENCIA"),
    NOTA_OUTRAS_FORMAS_PGTO_FATURAMENTO_DE_CAIXA("NOTA_OUTRAS_FORMAS_PGTO_FATURAMENTO_DE_CAIXA"),
    NOTA_OUTRAS_FORMAS_PGTO_FATURAMENTO("NOTA_OUTRAS_FORMAS_PGTO_FATURAMENTO"),
    NOTA_OUTRAS_FORMAS_PGTO_COMPETENCIA_QUITADA("NOTA_OUTRAS_FORMAS_PGTO_COMPETENCIA_QUITADA"),
    NOTA_OUTRAS_FORMAS_PGTO_COMPETENCIA_NAO_QUITADA("NOTA_OUTRAS_FORMAS_PGTO_COMPETENCIA_NAO_QUITADA"),
    NOTA_OUTRAS_FORMAS_PGTO_RECEITAPROVISAO("NOTA_OUTRAS_FORMAS_PGTO_RECEITAPROVISAO"),
    RELATORIO_FATURAMENTO_SUCESSO("RELATORIO_FATURAMENTO_SUCESSO"),
    RELATORIO_FATURAMENTO_RECEBIDO("RELATORIO_FATURAMENTO_RECEBIDO"),
    RELATORIO_RECEITA("RELATORIO_RECEITA"),
    RELATORIO_COMPETENCIA("RELATORIO_COMPETENCIA"),
    GAME_OF_RESULTS("GAME_OF_RESULTS"),
    GOR_MONITORAMENTO("GOR_MONITORAMENTO"),
    GOR_GRAFICOS("GOR_GRAFICOS"),
    GOR_RELATORIOS("GOR_RELATORIOS"),
    GOR_DADOS_DO_GRUPO("GOR_DADOS_DO_GRUPO"),
    GOR_GEOLOCALIZACAO("GOR_GEOLOCALIZACAO"),
    GOR_GAME_POR_UNIDADE("GOR_GAME_POR_UNIDADE"),
    GOR_REGIOES("GOR_REGIOES"),
    GOR_PRINCIPAL("GOR_PRINCIPAL"),
    ALUNO_MARCADO_PERFIL_ALUNO("ALUNO_MARCADO_PERFIL_ALUNO"),
    ALUNO_MARCADO_TELA_CLIENTES("ALUNO_MARCADO_TELA_CLIENTES"),
    ALUNO_DESMARCADO("ALUNO_DESMARCADO"),
    DEMONSTRATIVO_FINANCEIRO_RECEITA("DEMONSTRATIVO_FINANCEIRO_RECEITA"),
    DEMONSTRATIVO_FINANCEIRO_COMPETENCIA("DEMONSTRATIVO_FINANCEIRO_COMPETENCIA"),
    DEMONSTRATIVO_FINANCEIRO_FATURAMENTO_RECEBIDO("DEMONSTRATIVO_FINANCEIRO_FATURAMENTO_RECEBIDO"),
    DEMONSTRATIVO_FINANCEIRO_FATURAMENTO("DEMONSTRATIVO_FINANCEIRO_FATURAMENTO"),
    DEMONSTRATIVO_FINANCEIRO_RECEITAPROVISAO("DEMONSTRATIVO_FINANCEIRO_RECEITAPROVISAO"),
    CONSULTA_DE_TURMAS_CONSULTOU("CONSULTA_DE_TURMAS_CONSULTOU"),
    RELATORIO_DE_PERSONAL_CONSULTOU("RELATORIO_DE_PERSONAL_CONSULTOU"),
    MAPA_TURMAS_CONSULTOU("MAPA_TURMAS_CONSULTOU"),
    GERAR_RELATORIO_GERADOR_CONSULTA("GERAR_RELATORIO_GERADOR_CONSULTA"),
    ENTROU_GERADOR_CONSULTA("ENTROU_GERADOR_CONSULTA"),
    INATIVAR_USUARIO("INATIVAR_USUARIO"),
    VENDA_APROVADA_VENDAS_ONLINE("VENDA_APROVADA_VENDAS_ONLINE"),
    VENDA_NEGADA_VENDAS_ONLINE("VENDA_NEGADA_VENDAS_ONLINE"),
    VENDA_ERRO_VENDAS_ONLINE("VENDA_ERRO_VENDAS_ONLINE"),
    META_DIARIA_NAO_ABERTA("META_DIARIA_NAO_ABERTA"),
    NAO_EXIBIR_MAIS_HOJE_MODAL_INATIVAR_USUARIO("NAO_EXIBIR_MAIS_HOJE_MODAL_INATIVAR_USUARIO"),
    EXIBIR_MODAL_INATIVAR_USUARIO("EXIBIR_MODAL_INATIVAR_USUARIO"),
    META_DIARIA_ABERTA_TELA_CRM_AUTOMATICA("META_DIARIA_ABERTA_TELA_CRM_AUTOMATICA"),
    ENVIOU_LEAD_WORDPRESS_PARA_CRM("ENVIOU_LEAD_WORDPRESS_PARA_CRM"),
    CLICOU_COMPRAR_SMS("CLICOU_COMPRAR_SMS"),
    COMPROU_SMS("COMPROU_SMS"),
    USOU_SIMPLES_REGISTRO_NOVO("USOU_SIMPLES_REGISTRO_NOVO"),
    ATUALIZACAO_CADASTRO_EMPRESA("ATUALIZACAO_CADASTRO_EMPRESA"),
    ATUALIZACAO_CADASTRO_USUARIO("ATUALIZACAO_CADASTRO_USUARIO"),
    ADIOU_ATUALIZACAO_CADASTRAL("ADIOU_ATUALIZACAO_CADASTRAL"),
    ABRIU_TELA_GEOLOCALIZACAO("ABRIU_TELA_GEOLOCALIZACAO"),
    ENVIOU_LEAD_RDSTATION_PARA_CRM("ENVIOU_LEAD_RDSTATION_PARA_CRM"),
    CLICOU_ABRIR_MODAL_COMPRAR_MODULO_VENDAS_ONLINE("CLICOU_ABRIR_MODAL_COMPRAR_MODULO_VENDAS_ONLINE"),
    FIXOU_SALDO_FLUXO_CAIXA("FIXOU_SALDO_FLUXO_CAIXA"),
    FIXOU_SALDO_INICIAL_FLUXO_CAIXA("FIXOU_SALDO_INICIAL_FLUXO_CAIXA"),
    CONFIRMOU_MODAL_COMPRAR_MODULO_VENDAS_ONLINE("CONFIRMOU_MODAL_COMPRAR_VENDAS_ONLINE"),
    USOU_REVERTE_TRANSFERENCIA_CARTEIRAS_CRM("USOU_REVERTE_TRANSFERENCIA_CARTEIRAS_CRM"),
    CANAL_CLIENTE("CANAL_CLIENTE"),
    COMPARTILHAR_LINK_PAGAMENTO_ALUNO("COMPARTILHAR_LINK_PAGAMENTO_ALUNO"),
    CLICOU_ABRIR_MODAL_ATIVAR_CLUBE_VANTAGENS("CLICOU_ABRIR_MODAL_ATIVAR_CLUBE_VANTAGENS"),
    CONFIRMOU_MODAL_ATIVAR_CLUBE_VANTAGENS("CONFIRMOU_MODAL_ATIVAR_CLUBE_VANTAGENS"),
    ENVIOU_LEAD_RDSTATION_PARA_CRM_OLD("ENVIOU_LEAD_RDSTATION_PARA_CRM_OLD"),
    ADICIONAR_BONUS_COLETIVO("ADICIONAR_BONUS_COLETIVO"),
    REMOVER_BONUS_COLETIVO("REMOVER_BONUS_COLETIVO"),
    QUITACAO_CONTA_ONLINE("QUITACAO_CONTA_ONLINE"),
    COBRANCA_BLOQUEADA_BLOQUEAR("COBRANCA_BLOQUEADA_BLOQUEAR"),
    COBRANCA_BLOQUEADA_DESBLOQUEAR("COBRANCA_BLOQUEADA_DESBLOQUEAR"),
    RELATORIO_CLIENTES_COBRANCA_BLOQUEADA("RELATORIO_CLIENTES_COBRANCA_BLOQUEADA"),
    CRM_REGISTRO_PARALISACAO("CRM_REGISTRO_PARALISACAO"),
    INICIOU_PARALISACAO("INICIOU_PARALISACAO"),
    FINALIZOU_PARALISACAO("FINALIZOU_PARALISACAO"),
    PARALISACAO_RETROATIVA("PARALISACAO_RETROATIVA"),
    CONTATO_PESSOAL("CONTATO_PESSOAL"),
    VENDA_APROVADA_PACTO_STORE("VENDA_APROVADA_PACTO_STORE"),
    VENDA_APP_CONSULTOR("VENDA_APP_CONSULTOR"),
    VENDA_NEGADA_PACTO_STORE("VENDA_NEGADA_PACTO_STORE"),
    VENDA_ERRO_PACTO_STORE("VENDA_ERRO_PACTO_STORE"),
    SGP_MODALIDADES_COM_TURMAS("SGP_MODALIDADES_COM_TURMAS"),
    SGP_MODALIDADES_SEM_TURMAS("SGP_MODALIDADES_SEM_TURMAS"),
    SGP_TURMAS("SGP_TURMAS"),
    SGP_AVALIACOES_FISICAS("SGP_AVALIACOES_FISICAS"),
    ERRO_RETORNO_REMESSA("ERRO_RETORNO_REMESSA"),
    PACTO_UNIVERSIDADE("PACTO_UNIVERSIDADE"),
    NOVO_PLANO_ACESSO("NOVO_PLANO_ACESSO"),
    NOVO_PLANO_SALVAR("NOVO_PLANO_SALVAR"),
    TRANSACOES_PIX("TRANSACOES_PIX"),
    PEDIDOS_PINPAD("PEDIDOS_PINPAD"),
    CARREGAMENTO_BI("CARREGAMENTO_BI"),
    BUSCOU_INTERVALO_MAIOR_7_DIAS_META_CRM("BUSCOU_INTERVALO_MAIOR_7_DIAS_META_CRM"),
    DASHBOARD_PACTO_PAY("DASHBOARD_PACTO_PAY"),
    CARTAO_CREDITO_PACTO_PAY("CARTAO_CREDITO_PACTO_PAY"),
    PARCELAS_ABERTO_PACTO_PAY("PARCELAS_ABERTO_PACTO_PAY"),
    CARTAO_DE_VACINA_TOTALACESSOS_GERAL("CARTAO_DE_VACINA_TOTALACESSOS_GERAL"),
    CARTAO_DE_VACINA_TOTALACESSOS_VIANAVEGADOR("CARTAO_DE_VACINA_TOTALACESSOS_VIANAVEGADOR"),
    CARTAO_DE_VACINA_VISUALIZACAOANEXOS("CARTAO_DE_VACINA_VISUALIZACAOANEXOS"),
    NAO_EXIBIR_MODAL_PACTO_PAY_REMESSA("NAO_EXIBIR_MODAL_PACTO_PAY_REMESSA"),
    NAO_EXIBIR_MODAL_PACTO_PAY_TRANSACAO("NAO_EXIBIR_MODAL_PACTO_PAY_TRANSACAO"),
    GYM_PASS_ERRO_VALIDACAO_EXISTE_NO_DIA("GYM_PASS_ERRO_VALIDACAO_EXISTE_NO_DIA"),
    VALIDAR_EMAIL_MAIS_TARDE_SEM_EMAIL("VALIDAR_EMAIL_MAIS_TARDE_SEM_EMAIL"),
    VALIDAR_EMAIL_MAIS_TARDE_COM_EMAIL("VALIDAR_EMAIL_MAIS_TARDE_COM_EMAIL"),
    VALIDAR_EMAIL_SOLICITACAO("VALIDAR_EMAIL_SOLICITACAO"),
    VALIDAR_EMAIL_SOLICITACAO_ERRO("VALIDAR_EMAIL_SOLICITACAO_ERRO"),
    VALIDAR_EMAIL_VALIDAR_CODIGO("VALIDAR_EMAIL_VALIDAR_CODIGO"),
    VALIDAR_EMAIL_VALIDAR_CODIGO_ERRO("VALIDAR_EMAIL_VALIDAR_CODIGO_ERRO"),
    LOGIN_ANTIGO("LOGIN_ANTIGO"),
    LOGIN_NOVO("LOGIN_NOVO"),
    ADM_BUSINESS_INTELLIGENCE("ADM_BUSINESS_INTELLIGENCE"),
    BUSINESS_INTELLIGENCE("BUSINESS_INTELLIGENCE"),
    ADM_VENDA_RAPIDA("ADM_VENDA_RAPIDA"),
    METAS_FINANCEIRO_VENDA("METAS_FINANCEIRO_VENDA"),
    OBSERVACAO_CLIENTE("OBSERVACAO_CLIENTE"),
    BLOQUEIO_CATRACA_CLIENTE("BLOQUEIO_CATRACA_CLIENTE"),
    HISTORICO_INDICACAO_CLIENTE("HISTORICO_INDICACAO_CLIENTE"),
    HISTORICO_ARMARIO_CLIENTE("HISTORICO_ARMARIO_CLIENTE"),
    IMPRESSAO_CONTRATO_CLIENTE("IMPRESSAO_CONTRATO_CLIENTE"),
    DOCUMENTOS_CONTRATOS_CLIENTE("DOCUMENTOS_CONTRATOS_CLIENTE"),
    ENVIO_CONTRATO_CLIENTE("ENVIO_CONTRATO_CLIENTE"),
    ALTERAR_PARCELA_CONTRATO_CLIENTE("ALTERAR_PARCELA_CONTRATO_CLIENTE"),
    CONVIDADO_CADASTRO_CLIENTE("CONVIDADO_CADASTRO_CLIENTE"),
    RECIBO_CLIENTE_ENVIAR("RECIBO_CLIENTE_ENVIAR"),
    RECIBO_CLIENTE_EDITAR("RECIBO_CLIENTE_EDITAR"),
    RECIBO_CLIENTE_CONSULTA("RECIBO_CLIENTE_CONSULTA"),
    RECIBO_CLIENTE_IMPRIME("RECIBO_CLIENTE_IMPRIME"),
    ENVIO_NFSE_CLIENTE("ENVIO_NFSE_CLIENTE"),
    ENVIO_NFCE_CLIENTE("ENVIO_NFCE_CLIENTE"),
    EDICAO_CLIENTE("EDICAO_CLIENTE"),
    NOTA_FISCAL_CLIENTE("NOTA_FISCAL_CLIENTE"),
    LINK_CADASTRO_CARTAO_ONLINE("LINK_CADASTRO_CARTAO_ONLINE_CLIENTE"),
    LINK_PAGAMENTO_ONLINE("LINK_PAGAMENTO_ONLINE_CLIENTE"),
    VER_HISTORICO_VINCULO_ALUNO("VER_HISTORICO_VINCULO_ALUNO"),
    VER_ACESSOS_CLIENTE("VER_ACESSOS_CLIENTE"),
    ESTORNO_CONTRATO_CLIENTE("ESTORNO_CONTRATO_CLIENTE"),
    CANCELAMENTO_TRANSFERENCIA_CLIENTE("CANCELAMENTO_TRANSFERENCIA_CLIENTE"),
    CANCELAMENTO_DEVOLUCAO_CLIENTE("CANCELAMENTO_DEVOLUCAO_CLIENTE"),
    BONUS_CLIENTE("BONUS_CLIENTE"),
    ALTERAR_HORARIO_CLIENTE("ALTERAR_HORARIO_CLIENTE"),
    ALTERAR_TIPO_CONTRATO_EXPONTANEO_CLIENTE("ALTERAR_TIPO_CONTRATO_EXPONTANEO_CLIENTE"),
    ALTERAR_TIPO_CONTRATO_AGENDADO_CLIENTE("ALTERAR_TIPO_CONTRATO_AGENDADO_CLIENTE"),
    CONSULTOR_RESPONSAVEL_CONTRATO_CLIENTE("CONSULTOR_RESPONSAVEL_CONTRATO_CLIENTE"),
    MOSTRAR_DETALHES_CONTRATO_CLIENTE("MOSTRAR_DETALHES_CONTRATO_CLIENTE"),
    HISTORICO_OPERACAO_CONTRATO_CLIENTE("HISTORICO_OPERACAO_CONTRATO_CLIENTE"),
    TURMA_REPOR_CLIENTE("TURMA_REPOR_CLIENTE"),
    TURMA_DESMARCAR_CLIENTE("TURMA_DESMARCAR_CLIENTE"),
    TURMA_FALTAS_CLIENTE("TURMA_FALTAS_CLIENTE"),
    TURMA_REPOSICOES_CLIENTE("TURMA_REPOSICOES_CLIENTE"),
    TURMA_DESMARCADAS_CLIENTE("TURMA_DESMARCADAS_CLIENTE"),
    REALIZAR_CONTATO_FAZER_INDICACAO("REALIZAR_CONTATO_FAZER_INDICACAO"),
    REALIZAR_CONTATO_SCRIPT("REALIZAR_CONTATO_SCRIPT"),
    RENOVAVEL_AUTOMATICAMENTE_CLIENTE("RENOVAVEL_AUTOMATICAMENTE_CLIENTE"),
    FOTO_CLIENTE("FOTO_CLIENTE"),
    CONTROLE_CREDITO_CLIENTE("CONTROLE_CREDITO_CLIENTE"),
    CONTROLE_CREDITO_LOG_CLIENTE("CONTROLE_CREDITO_LOG_CLIENTE"),
    CRM_BI("CRM_BI"),
    FIN_BI("FIN_BI"),
    TREINO_BI("TREINO_BI"),
    TREINO_BI_PERSONALIZADO("TREINO_BI_PERSONALIZADO"),
    TREINO_EM_CASA("TREINO_EM_CASA"),
    TREINO_APLICATIVOS_ATIVOS("TREINO_APLICATIVOS_ATIVOS"),
    TREINO_CARTEIRA("TREINO_CARTEIRA"),
    TREINO_ATIVIDADES_PROFESSORES("TREINO_ATIVIDADES_PROFESSORES"),
    TREINO_PROFESSORES_ALUNOS_AVISO_MEDICO("TREINO_PROFESSORES_ALUNOS_AVISO_MEDICO"),
    TREINO_PROFESSORES_SUBSTITUIDOS("TREINO_PROFESSORES_SUBSTITUIDOS"),
    TREINO_ANDAMENTO("TREINO_ANDAMENTO"),
    TREINO_PERSONAL("TREINO_PERSONAL"),
    TREINO_GESTAO_CREDITOS("TREINO_GESTAO_CREDITOS"),
    TREINO_RANKING("TREINO_RANKING"),
    TREINO_ACAMPANHAMENTO_PERSONAL("TREINO_ACAMPANHAMENTO_PERSONAL"),
    TREINO_COLABORADORES("TREINO_COLABORADORES"),
    TREINO_APARELHOS("TREINO_APARELHOS"),
    TREINO_ATIVIDADE("TREINO_ATIVIDADE"),
    TREINO_CATETORIA_ATIVIDADE("TREINO_CATETORIA_ATIVIDADE"),
    TREINO_FICHAS_PREDEFINIDAS("TREINO_FICHAS_PREDEFINIDAS"),
    TREINO_PROGRAMAS_PREDEFINIDOS("TREINO_PROGRAMAS_PREDEFINIDOS"),
    TREINO_NIVEIS("TREINO_NIVEIS"),
    AVALIACAO_FISICA_BI("AVALIACAO_FISICA_BI"),
    AVALIACAO_ANAMNESES("AVALIACAO_ANAMNESES"),
    AVALIACAO_OBJETIVOS("AVALIACAO_OBJETIVOS"),
    PACTOPAY_BI("PACTOPAY_BI"),
    PACTOPAY_CARTAO_CREDITO("PACTOPAY_CARTAO_CREDITO"),
    PACTOPAY_PARCELAS_EM_ABERTO("PACTOPAY_PARCELAS_EM_ABERTO"),
    PACTOPAY_REGUA_COBRANCA("PACTOPAY_REGUA_COBRANCA"),
    PACTOPAY_CARTAO_CREDITO_ONLINE("PACTOPAY_CARTAO_CREDITO_ONLINE"),
    PACTOPAY_PIX("PACTOPAY_PIX"),
    CROSS_BI("CROSS_BI"),
    CROSS_WOD("CROSS_WOD"),
    CROSS_MONITOR("CROSS_MONITOR"),
    CROSS_APARELHOS("CROSS_APARELHOS"),
    CROSS_ATIVIDADE("CROSS_ATIVIDADE"),
    CROSS_BENCHMARKS("CROSS_BENCHMARKS"),
    CROSS_TIPOS_BENCHMARK("CROSS_TIPOS_BENCHMARK"),
    CROSS_TIPO_WOD("CROSS_TIPO_WOD"),
    GRADUACAO_AVALIACOES_PROGRESSO("GRADUACAO_AVALIACOES_PROGRESSO"),
    GRADUACAO_ATIVIDADES("GRADUACAO_ATIVIDADES"),
    GRADUACAO_FICHA_TENICA("GRADUACAO_FICHA_TENICA"),
    CRM_META_DIARIA("CRM_META_DIARIA"),
    RELATORIO_ORCAMENTARIO("RELATORIO_ORCAMENTARIO"),
    RELATORIO_ORCAMENTARIO_CONFIGURACAO("RELATORIO_ORCAMENTARIO_CONFIGURACAO"),
    BUSINESS_INTELLIGENCE_CE("BUSINESS_INTELLIGENCE_CE"),
    PINPAD("PINPAD"),
    TREINO_PRESCRICAO("TREINO_PRESCRICAO"),
    TREINO_ALUNOS("TREINO_ALUNOS"),
    TREINO_CADASTRO_COLABORADORES("TREINO_CADASTRO_COLABORADORES"),
    TREINO_USUARIOS("TREINO_USUARIOS"),
    TREINO_PERFIL_ACESSO("TREINO_PERFIL_ACESSO"),
    AGENDA_BI("AGENDA_BI"),
    AGENDA_TV_AULA("AGENDA_TV_AULA"),
    AGENDA_TV_GESTOR("AGENDA_TV_GESTOR"),
    AGENDA_AULAS("AGENDA_AULAS"),
    AGENDA_SERVICOS("AGENDA_SERVICOS"),
    AGENDA_AMBIENTE("AGENDA_AMBIENTE"),
    AGENDA_MODALIDADE("AGENDA_MODALIDADE"),
    AGENDA_INDICADORES("AGENDA_INDICADORES"),
    AGENDA_AULA_EXCLUIDA("AGENDA_AULA_EXCLUIDA"),
    AGENDA_CONFIGURAR_AULAS("AGENDA_CONFIGURAR_AULAS"),
    AGENDA_TIPOS_AGENDAMENTO("AGENDA_TIPOS_AGENDAMENTO"),
    METAS_DO_FINANCEIRO("METAS_DO_FINANCEIRO"),
    NOTAS_FISCAIS("NOTAS_FISCAIS"),
    PESSOAS_CLIENTES("PESSOAS_CLIENTES"),
    PESSOAS_INCLUIR_CLEINTE("PESSOAS_INCLUIR_CLEINTE"),
    TOTAL_PASS_ERRO_VALIDACAO_EXISTE_NO_DIA("TOTAL_PASS_ERRO_VALIDACAO_EXISTE_NO_DIA"),
    PACTOPAY_CONFIGURACAO_FASES("PACTOPAY_CONFIGURACAO_FASES"),
    PACTOPAY_CONFIGURACAO_EMAIL("PACTOPAY_CONFIGURACAO_EMAIL"),
    RELATORIO_PONTUACAO("RELATORIO_PONTUACAO"),
    LANCAMENTO_BRINDE("LANCAMENTO_BRINDE"),
    TRANSFERENCIA_SALDO_CONTA_CORRENTE("TRANSFERENCIA_SALDO_CONTA_CORRENTE"),
    RECEBER_DEBITO_CONTA_CORRENTE("RECEBER_DEBITO_CONTA_CORRENTE"),
    AJUSTE_SALDO_CONTA_CORRENTE("AJUSTE_SALDO_CONTA_CORRENTE"),
    AJUSTE_PONTUACAO_CONTA_CORRENTE("AJUSTE_PONTUACAO_CONTA_CORRENTE"),
    EXPERIMENTE_TELA_ALUNO("EXPERIMENTE_TELA_ALUNO"),
    PADRAO_NOVA_TELA_ALUNO("PADRAO_NOVA_TELA_ALUNO"),
    NOVA_TELA_ALUNO_ABRIR("NOVA_TELA_ALUNO_ABRIR"),
    TELA_ALUNO_CARREGOU("TELA_ALUNO_CARREGOU"),
    PRE_MUDANCA_CHAT("PRE_MUDANCA_CHAT"),
    ERRO_EXTRATO_STONE("ERRO_EXTRATO_STONE"),
    TOKENS_OPERACOES("TOKENS_OPERACOES"),
    BOLETIM_VISITA_CLIENTE("BOLETIM_VISITA_CLIENTE"),
    CONTATO_AVULSO("CONTATO_AVULSO"),
    EXPERIMENTE_TELA_CLIENTES("EXPERIMENTE_TELA_CLIENTES"),
    PADRAO_NOVA_TELA_CLIENTES("PADRAO_NOVA_TELA_CLIENTES"),
    NOVA_TELA_CLIENTES_ABRIR("NOVA_TELA_CLIENTES_ABRIR"),
    ERRO_LOGIN_SEM_MODULOS("ERRO_LOGIN_SEM_MODULOS"),
    CLUBE_DE_BENEFICIOS("CLUBE_DE_BENEFICIOS"),
    CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_GEROU("CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_GEROU"),
    CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_SUCESSO("CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_SUCESSO"),
    CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_ERRO("CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_ERRO"),
    CODIGO_AUTENTICACAO_EMPRESA_ABA_COBRANCA_GEROU("CODIGO_AUTENTICACAO_EMPRESA_ABA_COBRANCA_GEROU"),
    CODIGO_AUTENTICACAO_EMPRESA_ABA_COBRANCA_SUCESSO("CODIGO_AUTENTICACAO_EMPRESA_ABA_COBRANCA_SUCESSO"),
    CODIGO_AUTENTICACAO_EMPRESA_ABA_COBRANCA_ERRO("CODIGO_AUTENTICACAO_EMPRESA_ABA_COBRANCA_ERRO"),
    SOCIAL_MAILING("SOCIAL_MAILING"),
    PADRAO_NOVA_VENDA_AVULSA("PADRAO_NOVA_VENDA_AVULSA"),
    PADRAO_NOVA_CONFIGURACOES("PADRAO_NOVA_CONFIGURACOES"),
    PACTOPAY_COMUNICACAO("PACTOPAY_COMUNICACAO"),
    PACTOPAY_BI_REGUA_COBRANCA("PACTOPAY_BI_REGUA_COBRANCA"),
    ATUALIZAR_BV("ATUALIZAR_BV"),
    CLIENTES_COM_RESTRICOES("CLIENTES_COM_RESTRICOES"),
    LOCAL_IMPRESSAO("LOCAL_IMPRESSAO"),
    EXIBIR_MODAL_PLANOS_INATIVOS_MES_SEGUINTE("EXIBIR_MODAL_PLANOS_INATIVOS_MES_SEGUINTE"),
    NAO_EXIBIR_MODAL_PLANOS_INATIVOS_MES_SEGUINTE("NAO_EXIBIR_MODAL_PLANOS_INATIVOS_MES_SEGUINTE"),
    SOLICITACAO_COMPRA("SOLICITACAO_COMPRA"),
    IMPOSTO("IMPOSTO"),
    SMD_RELATORIO("SMD_RELATORIO"),
    PADRAO_NOVA_TELA_CAIXA_ABERTO("PADRAO_NOVA_TELA_CAIXA_ABERTO"),
    EXPERIMENTE_TELA_CAIXA_ABERTO("EXPERIMENTE_TELA_CAIXA_ABERTO"),
    PADRAO_NOVA_TELA_INCLUIR_CLIENTE("PADRAO_NOVA_TELA_INCLUIR_CLIENTE"),
    PADRAO_NOVA_TELA_BI("PADRAO_NOVA_TELA_BI"),
    ABRIU_EDITAR_EMPRESA("ABRIU_EDITAR_EMPRESA"),
    ABRIU_EMPRESA("ABRIU_EMPRESA"),
    ;
    private final String descricao;

    RecursoSistema(String descricao) {
        this.descricao = descricao;
    }

    public static RecursoSistema fromDescricao(final String descricao) {
        if (StringUtils.isBlank(descricao)) {
            return null;
        }

        for (RecursoSistema recursoSistema : RecursoSistema.values()) {
            if (recursoSistema.getDescricao().equalsIgnoreCase(descricao)) {
                return recursoSistema;
            }
        }

        return null;
    }

    public String getDescricao() {
        return descricao;
    }
}

package br.com.pactosolucoes.pactopay;

import br.com.pactosolucoes.integracao.pactopay.StatusPactoPayEnum;
import br.com.pactosolucoes.integracao.pactopay.TipoConsultaEnum;
import br.com.pactosolucoes.integracao.pactopay.TipoConsultaHistoricoCartaoEnum;
import br.com.pactosolucoes.integracao.pactopay.front.FiltroPactoPayDTO;
import br.com.pactosolucoes.integracao.pactopay.front.cartao.CobrancaDetalheDTO;
import br.com.pactosolucoes.integracao.pactopay.front.cartao.HistoricoDTO;
import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.RemessaItem;
import negocio.facade.jdbc.financeiro.Transacao;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PactoPayDashCartao extends SuperEntidade {

    private static final int MAXIMO_RESULTADOS = 10;

    public PactoPayDashCartao(Connection c) throws Exception {
        super(c);
    }

    public List<HistoricoDTO> consultarHistorico(FiltroPactoPayDTO filtro, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = getBaseSQLHistorico(TipoConsultaEnum.TOTALIZADOR, null, null, filtro);
        processarPaginador(sql, "", paginadorDTO);

        List<HistoricoDTO> lista = new ArrayList<>();
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    HistoricoDTO dto = new HistoricoDTO();
                    dto.setData(Uteis.getDataAplicandoFormatacao(rs.getDate("data"), "dd/MM/yyyy"));
                    dto.setQtdTotal(rs.getInt("qtdTotal"));
                    dto.setValorTotal(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorTotal")));
                    dto.setQtdRecebidos(rs.getInt("qtdRecebida"));
                    dto.setValorRecebidos(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorRecebida")));
                    dto.setQtdNaoAprovada(rs.getInt("qtdNaoAprovada"));
                    dto.setValorNaoAprovada(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorNaoAprovada")));
                    dto.setQtdPendentes(rs.getInt("qtdPendente"));
                    dto.setValorPendentes(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorPendente")));

                    if (UteisValidacao.emptyNumber(dto.getQtdPendentes())) {
                        dto.setStatus("PROCESSADO");
                        dto.setStatusCodigo(StatusPactoPayEnum.PROCESSADO.getCodigo());
                    } else {
                        dto.setStatus("AGUARDANDO");
                        dto.setStatusCodigo(StatusPactoPayEnum.AGUARDANDO.getCodigo());
                    }

                    if (UteisValidacao.emptyNumber(dto.getQtdRecebidos())) {
                        dto.setEficiencia(0.0);
                    } else {
                        dto.setEficiencia(Uteis.arredondarForcando2CasasDecimais(((dto.getQtdRecebidos().doubleValue() * 100) / (dto.getQtdTotal()))));
                    }
                    lista.add(dto);
                }
            }
        }
        return lista;
    }

    public void processarPaginador(StringBuilder sql, String orderByDefault, PaginadorDTO paginadorDTO) throws Exception {
        if (paginadorDTO != null) {

            int maxResults = paginadorDTO.getSize() == null ? MAXIMO_RESULTADOS : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;

            paginadorDTO.setQuantidadeTotalElementos(obterCount(sql).longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            //adicionar ordenação
            if (!UteisValidacao.emptyString(orderByDefault)
                    && paginadorDTO.getSQLOrderByUse() != null
                    && UteisValidacao.emptyString(paginadorDTO.getSQLOrderByUse().trim())) {
                sql.append(" ORDER BY ").append(orderByDefault).append(" \n");
            } else {
                sql.append(paginadorDTO.getSQLOrderByUse());
            }

            //adicionar limit
            sql.append(paginadorDTO.getSQLLimitByUse());

            //adicionar offset
            sql.append(paginadorDTO.getSQLOffsetByUse());
        }
    }

    private StringBuilder getBaseSQLHistorico(TipoConsultaEnum tipoConsultaEnum, Date dia,
                                              TipoConsultaHistoricoCartaoEnum tipoConsultaHistoricoCartaoEnum, FiltroPactoPayDTO filtro) {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        if (tipoConsultaEnum.equals(TipoConsultaEnum.TOTALIZADOR)) {
            sql.append("sql.data::date, \n");
            sql.append("count(sql.*) as qtdTotal, \n");
            sql.append("sum(sql.valor::numeric) as valorTotal, \n");
            sql.append("sum(case when sql.situacaocobranca = 'RECEBIDA' then 1 else 0 end) as qtdRecebida, \n");
            sql.append("sum(case when sql.situacaocobranca = 'RECEBIDA' then sql.valor::numeric else 0 end) as valorRecebida, \n");
            sql.append("sum(case when sql.situacaocobranca = 'NEGADA' then 1 else 0 end) as qtdNaoAprovada, \n");
            sql.append("sum(case when sql.situacaocobranca = 'NEGADA' then sql.valor::numeric else 0 end) as valorNaoAprovada, \n");
            sql.append("sum(case when sql.situacaocobranca = 'PENDENTE' then 1 else 0 end) as qtdPendente, \n");
            sql.append("sum(case when sql.situacaocobranca = 'PENDENTE' then sql.valor::numeric else 0 end) as valorPendente \n");
        } else {
            sql.append("sql.* \n");
        }
        sql.append("from ( \n");
        sql.append("select \n");
        sql.append("cl.matricula, \n");
        sql.append("cl.codigo as cliente, \n");
        sql.append("p.codigo as pessoa, \n");
        sql.append("p.nome, \n");
        sql.append("'REMESSA' as tipo, \n");
        sql.append("'REMESSA_' || ri.codigo as codunico,  \n");
        sql.append("r.dataregistro as data,  \n");
        sql.append("r.situacaoremessa as situacao, \n");
        sql.append("case  \n");
        sql.append("when ((ri.props not ilike '%statusvenda%' and r.situacaoremessa = 2) or r.situacaoremessa in (1,4)) then 'PENDENTE' \n");
        sql.append("when coalesce(ri.movpagamento,0) > 0 then 'RECEBIDA' \n");
        sql.append("else 'NEGADA' \n");
        sql.append("end as situacaocobranca, \n");
        sql.append("ri.codigo as remessaitem, \n");
        sql.append("r.codigo as remessa, \n");
        sql.append("0 as transacao, \n");
        sql.append("ri.movpagamento, \n");
        sql.append("ri.valoritemremessa as valor, \n");
        sql.append("p.nome as titular, \n");
        sql.append("split_part(split_part(split_part(ri.props, 'StatusVenda=', 2), ',', 1), '}', 1) AS codigoRetorno, \n");
        sql.append("split_part(split_part(split_part(ri.props, 'CartaoMascarado=', 2), ',', 1), '}', 1) AS cartao, \n");
        sql.append("split_part(split_part(split_part(ri.props, 'CodigoAutorizacao=', 2), ',', 1), '}', 1) as autorizacao, \n");
        sql.append("split_part(split_part(split_part(ri.props, 'Bandeira=', 2), ',', 1), '}', 1) as bandeira, \n");
        sql.append("cc.tipoconvenio, \n");
        sql.append("cc.descricao as convenio, \n");
        sql.append("us.nome as usuario, \n");
        sql.append("us.username as usuariousername, \n");
        sql.append("0 as qtdparcelas, \n");
        sql.append("r.identificador as identificador \n");
        sql.append("from remessaitem ri  \n");
        sql.append("inner join remessa r on r.codigo = ri.remessa  \n");
        sql.append("inner join usuario us on us.codigo = r.usuario  \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = r.conveniocobranca \n");
        sql.append("inner join pessoa p on p.codigo = ri.pessoa \n");
        sql.append("left join cliente cl on cl.pessoa = p.codigo \n");
        sql.append("where r.tipo in (2,8,11)  \n");
        if (tipoConsultaHistoricoCartaoEnum != null && tipoConsultaHistoricoCartaoEnum.equals(TipoConsultaHistoricoCartaoEnum.TRANSACAO)) {
            sql.append("and false \n");
        }

        if (!filtro.getEmpresas().isEmpty()) {
            sql.append("and r.empresa in (").append(filtro.getEmpresasString()).append(") \n");
        }
        if (!filtro.getConvenios().isEmpty()) {
            sql.append("and r.conveniocobranca in (").append(filtro.getConveniosString()).append(") \n");
        }
        sql.append("union  \n");
        sql.append("select  \n");
        sql.append("cl.matricula, \n");
        sql.append("cl.codigo as cliente, \n");
        sql.append("p.codigo as pessoa, \n");
        sql.append("p.nome, \n");
        sql.append("'TRANSACAO' as tipo,  \n");
        sql.append("'TRANSACAO_' || t.codigo as codunico,  \n");
        sql.append("t.dataprocessamento as data,  \n");
        sql.append("t.situacao as situacao, \n");
        sql.append("case \n");
        sql.append("when t.situacao = 2 then 'PENDENTE' \n");
        sql.append("when t.situacao = 4 then 'RECEBIDA' \n");
        sql.append("else 'NEGADA' \n");
        sql.append("end as situacaocobranca, \n");
        sql.append("0 as remessaitem, \n");
        sql.append("0 as remessa, \n");
        sql.append("t.codigo as transacao, \n");
        sql.append("t.movpagamento, \n");
        sql.append("t.valor as valor, \n");
        sql.append("t.nomepessoa as titular, \n");
        sql.append("t.codigoretorno, \n");
        sql.append("CASE   \n");
        sql.append("WHEN t.outrasinformacoes ilike '%cartaoMascarado%' THEN split_part(split_part(t.outrasinformacoes, 'cartaoMascarado\":\"', 2), '\"', 1)  \n");
        sql.append("WHEN t.tipo = 2 THEN split_part(split_part(t.paramsenvio, 'card_number\":\"', 2), '\"', 1)  \n");
        sql.append("WHEN t.tipo = 3 THEN split_part(split_part(t.paramsenvio, 'CardNumber\":\"', 2), '\"', 1)  \n");
        sql.append("WHEN t.tipo = 7 THEN split_part(split_part(t.paramsenvio, 'cardNumber\":\"', 2), '\"', 1)  \n");
        sql.append("WHEN t.tipo = 10 THEN split_part(split_part(t.outrasinformacoes, 'card\":\"', 2), '\"', 1)  \n");
        sql.append("WHEN t.tipo = 11 THEN split_part(split_part(t.paramsenvio, '<PAN>', 2), '</', 1)  \n");
        sql.append("ELSE '' END as cartao, \n");
        sql.append("t.codigoautorizacao as autorizacao, \n");
        sql.append("split_part(split_part(split_part(t.outrasinformacoes, 'cartaoBandeira\":\"', 2), '\"', 1), '}', 1) as bandeira, \n");
        sql.append("cc.tipoconvenio, \n");
        sql.append("cc.descricao as convenio, \n");
        sql.append("us.nome as usuario, \n");
        sql.append("us.username as usuariousername, \n");
        sql.append("(select count(*) from transacaomovparcela  where transacao = t.codigo) as qtdparcelas, \n");
        sql.append("t.codigoexterno as identificador \n");
        sql.append("from transacao t  \n");
        sql.append("inner join usuario us on us.codigo = t.usuarioresponsavel \n");
        sql.append("left join conveniocobranca cc on cc.codigo = t.conveniocobranca \n");
        sql.append("inner join pessoa p on p.codigo = t.pessoapagador \n");
        sql.append("left join cliente cl on cl.pessoa = p.codigo \n");
        sql.append("where t.transacaoverificarcartao = false  \n");
        if (!UteisValidacao.emptyNumber(filtro.getCodigoTransacao())) {
            sql.append("and t.codigo = ").append(filtro.getCodigoTransacao()).append(" \n");
        }

        if (tipoConsultaHistoricoCartaoEnum != null && tipoConsultaHistoricoCartaoEnum.equals(TipoConsultaHistoricoCartaoEnum.REMESSA)) {
            sql.append("and false \n");
        }

        if (!filtro.getEmpresas().isEmpty()) {
            sql.append("and t.empresa in (").append(filtro.getEmpresasString()).append(") \n");
        }
        if (!filtro.getConvenios().isEmpty()) {
            sql.append("and t.conveniocobranca in (").append(filtro.getConveniosString()).append(") \n");
        }
        sql.append(") as sql  \n");
        sql.append("where 1 = 1 \n");
        if (dia != null) {
            sql.append("and sql.data >= '").append(Uteis.getDataFormatoBD(dia)).append(" 00:00:00.000' \n");
            sql.append("and sql.data <= '").append(Uteis.getDataFormatoBD(dia)).append(" 23:59:59.999' \n");
        } else if (filtro.getInicioDate() != null ) {
            sql.append("and sql.data >= '").append(Uteis.getDataFormatoBD(filtro.getInicioDate())).append(" 00:00:00.000' \n");
            sql.append("and sql.data <= '").append(Uteis.getDataFormatoBD(filtro.getFimDate())).append(" 23:59:59.999' \n");
        }

        if (tipoConsultaEnum.equals(TipoConsultaEnum.TOTALIZADOR)) {
            sql.append("group by 1 \n");
            sql.append("order by 1 desc \n");
        }
        return sql;
    }

    private Integer obterCount(StringBuilder sql) throws Exception {
        return obterCount(sql, null);
    }

    private Integer obterCount(StringBuilder sql, String count) throws Exception {
        StringBuilder sqlCount = new StringBuilder();
        sqlCount.append("select ");
        if (count != null && count.length() > 0) {
            sqlCount.append(count).append("\n");
        } else {
            sqlCount.append("count(*) \n");
        }
        sqlCount.append("as total from ( \n");
        sqlCount.append(sql).append(" \n");
        sqlCount.append(") as sql ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlCount.toString())) {
                if (rs.next()) {
                    return rs.getInt("total");
                } else {
                    return 0;
                }
            }
        }
    }

    public List<CobrancaDetalheDTO> historicoLista(Date dia, TipoConsultaHistoricoCartaoEnum tipoConsultaHistoricoCartaoEnum,
                                                   FiltroPactoPayDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException {
        Usuario usuarioDAO;
        ConvenioCobranca convenioCobrancaDAO;
        Transacao transacaoDAO;
        RemessaItem remessaItemDAO;
        try {
            usuarioDAO = new Usuario(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            transacaoDAO = new Transacao(con);
            remessaItemDAO = new RemessaItem(con);

            Map<Integer, UsuarioVO> mapaUsuario = new HashMap<>();
            Map<Integer, ConvenioCobrancaVO> mapaConve = new HashMap<>();

            StringBuilder sql = getBaseSQLHistorico(TipoConsultaEnum.LISTA, dia, tipoConsultaHistoricoCartaoEnum, filtro);
            processarPaginador(sql, "sql.data desc", paginadorDTO);

            List<CobrancaDetalheDTO> lista = new ArrayList<>();
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        CobrancaDetalheDTO dto = new CobrancaDetalheDTO();
                        dto.setTransacao(rs.getInt("transacao"));
                        dto.setRemessa(rs.getInt("remessa"));
                        dto.setRemessaItem(rs.getInt("remessaItem"));
                        dto.setCliente(rs.getInt("cliente"));
                        dto.setPessoa(rs.getInt("pessoa"));
                        dto.setIdentificador(rs.getString("identificador"));
                        dto.setMatricula(rs.getString("matricula"));
                        dto.setNome(rs.getString("nome"));
                        dto.setTitular(rs.getString("titular"));
                        dto.setConvenio(rs.getString("convenio"));
                        dto.setCartao(rs.getString("cartao"));
                        dto.setAutorizacao(rs.getString("autorizacao"));
                        dto.setBandeira(rs.getString("bandeira"));
                        dto.setCodigoRetorno(rs.getString("codigoRetorno"));
                        dto.setUsuario(rs.getString("usuario"));
                        dto.setData(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("data"), "dd/MM/yyyy HH:mm:ss"));
                        dto.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));

                        String usuariousername = rs.getString("usuariousername");
                        dto.setTipoOperacao("");
                        if (usuariousername != null) {
                            if (usuariousername.equalsIgnoreCase("admin") ||
                                    usuariousername.equalsIgnoreCase("RECOR")) {
                                dto.setTipoOperacao("Automático");
                            }
                        } else {
                            dto.setTipoOperacao(dto.getUsuario());
                        }

                        if (dto.getTransacao() != null) {

                            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(dto.getTransacao(), Uteis.NIVELMONTARDADOS_CONSULTA_PACTO_PAY);

                            if (!UteisValidacao.emptyNumber(transacaoVO.getUsuarioResponsavel().getCodigo())) {
                                UsuarioVO usuarioVO = mapaUsuario.get(transacaoVO.getUsuarioResponsavel().getCodigo());
                                if (usuarioVO == null) {
                                    usuarioVO = usuarioDAO.consultarPorChavePrimaria(transacaoVO.getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                                    mapaUsuario.put(usuarioVO.getCodigo(), usuarioVO);
                                }
                                transacaoVO.setUsuarioResponsavel(usuarioVO);
                            }

                            if (!UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                                ConvenioCobrancaVO convenioVO = mapaConve.get(transacaoVO.getConvenioCobrancaVO().getCodigo());
                                if (convenioVO == null) {
                                    convenioVO = convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    mapaConve.put(convenioVO.getCodigo(), convenioVO);
                                }
                                transacaoVO.setConvenioCobrancaVO(convenioVO);
                            }

                            if (UteisValidacao.emptyString(dto.getAutorizacao()) && transacaoVO.getConvenioCobrancaVO() != null
                                    && !UteisValidacao.emptyString(transacaoVO.getParamsResposta())) {
                                obterCodAutorizacaoParamsResposta(transacaoVO, dto);
                            }

                            dto.setStatusCodigo(transacaoVO.getCodigoStatusPactoPay());
                            dto.setStatus(transacaoVO.getSituacao().getDescricao());
                            dto.setPermiteCancelar(transacaoVO.isPermiteCancelar());
                            dto.setQtdParcelas(rs.getInt("qtdparcelas"));
                            dto.setCodigoRetorno(transacaoVO.getCodigoRetornoGestaoTransacao());
                            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
                                dto.setDescricaoRetorno(SituacaoTransacaoEnum.CANCELADA.getHint());
                            } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                                dto.setDescricaoRetorno(SituacaoTransacaoEnum.ESTORNADA.getHint());
                            } else {
                                dto.setDescricaoRetorno(transacaoVO.getCodigoRetornoGestaoTransacaoMotivo());
                            }
                            dto.setRecibo(UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento()) ? null : transacaoVO.getReciboPagamento());
                            //NSU
                            dto.setNsu(transacaoVO.getCodigoNSU());
                            if (UteisValidacao.emptyString(dto.getNsu()) && !UteisValidacao.emptyString(transacaoVO.getNsuTransacao())) {
                                dto.setNsu(transacaoVO.getNsuTransacao());
                            }
                            //AUTORIZAÇÃO
                            if (UteisValidacao.emptyString(dto.getAutorizacao())) {
                                if (!UteisValidacao.emptyString(transacaoVO.getAutorizacao())) {
                                    dto.setAutorizacao(transacaoVO.getAutorizacao());
                                } else if (!UteisValidacao.emptyString(transacaoVO.getCodigoAutorizacao())) {
                                    dto.setAutorizacao(transacaoVO.getCodigoAutorizacao());
                                }
                            }
                            dto.setOrigem(transacaoVO.getOrigem().getDescricao());
                            if (UteisValidacao.emptyString(dto.getTipoOperacao())) {
                                dto.setTipoOperacao(transacaoVO.getOrigem().getDescricao());
                            }
                            String nr_vezes_parcelamento = obterItemOutrasInformacoes(AtributoTransacaoEnum.numeroParcelas, transacaoVO.getOutrasInformacoes());
                            dto.setParcelamento(UteisValidacao.emptyString(nr_vezes_parcelamento) ? 1 : Integer.parseInt(nr_vezes_parcelamento));
                            dto.setCodigo(transacaoVO.getCodigo());
                            dto.setCodConvenio(UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo()) ? 0 : transacaoVO.getConvenioCobrancaVO().getCodigo());
                            dto.setData_cancelamento(transacaoVO.getDataHoraCancelamento() == null ? "" : Uteis.getDataComHora(transacaoVO.getDataHoraCancelamento()));
                            dto.setQtd_parcelas(UteisValidacao.emptyList(transacaoVO.getListaParcelas()) ? 1 : transacaoVO.getListaParcelas().size());
                            dto.setOrigem(preencherOrigem(transacaoVO.getOrigem().getCodigo(), transacaoVO.getUsuarioResponsavel()));
                            dto.setSituacao_descricao(transacaoVO.getSituacao().getStatusPactoPayEnum() != null ? transacaoVO.getSituacao().getStatusPactoPayEnum().getDescricao() : "");
                            //CARTÃO
                            if (UteisValidacao.emptyString(dto.getCartao())) {
                                try {
                                   dto.setCartao(transacaoVO.getCartaoMascarado());
                                } catch (Exception ex) {}
                            }
                        } else if (dto.getRemessaItem() != null) {

                            RemessaItemVO remessaItemVO = remessaItemDAO.consultarPorChavePrimaria(dto.getRemessaItem(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                            if (!UteisValidacao.emptyNumber(remessaItemVO.getRemessa().getUsuario().getCodigo())) {
                                UsuarioVO usuarioVO = mapaUsuario.get(remessaItemVO.getRemessa().getUsuario().getCodigo());
                                if (usuarioVO == null) {
                                    usuarioVO = usuarioDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                                    mapaUsuario.put(usuarioVO.getCodigo(), usuarioVO);
                                }
                                remessaItemVO.getRemessa().setUsuario(usuarioVO);
                            }
                            dto.setStatusCodigo(remessaItemVO.getCodigoStatusPactoPay());
                            dto.setStatus(remessaItemVO.getRemessa().getSituacaoRemessa().getDescricao());
                            dto.setDescricaoRetorno(remessaItemVO.getDescricaoStatus());
                            dto.setQtdParcelas(consultarQtdParcelasItem(dto.getRemessaItem()));
                            dto.setRecibo(UteisValidacao.emptyNumber(remessaItemVO.getReciboPagamentoVO().getCodigo()) ? null : remessaItemVO.getReciboPagamentoVO().getCodigo());
                        }

                        lista.add(dto);
                    }
                }
            }
            return lista;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        } finally {
            usuarioDAO = null;
            convenioCobrancaDAO = null;
            transacaoDAO = null;
            remessaItemDAO = null;
        }
    }

    private void obterCodAutorizacaoParamsResposta(TransacaoVO transacaoVO, CobrancaDetalheDTO dto) {
        //tentar pegar dos parâmetros de resposta
        try {
            if (transacaoVO.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                JSONObject json = new JSONObject(transacaoVO.getParamsResposta());
                JSONObject payment = json.getJSONObject("Payment");
                dto.setAutorizacao(payment.getString("AuthorizationCode"));
            }
        } catch (Exception ignore) {
        }
    }

    private String preencherOrigem(Integer origem, UsuarioVO usuario) {
        OrigemCobrancaEnum origemCobrancaEnum = OrigemCobrancaEnum.obterPorCodigo(origem);
        if (!origemCobrancaEnum.equals(OrigemCobrancaEnum.NENHUM)) {
            return origemCobrancaEnum.getDescricao();
        } else {
            return (usuario != null && usuario.getUsername() != null &&
                    (usuario.getUsername().equalsIgnoreCase("RECOR") ||
                            usuario.getUsername().equalsIgnoreCase("ADMIN"))) ? "Automático" : "Manual";
        }
    }

    public String obterItemOutrasInformacoes(AtributoTransacaoEnum atributoEnum, String outrasInformacoes) {
        try {
            JSONObject jsonOutrasInformacoes = new JSONObject(outrasInformacoes);
            try {
                return jsonOutrasInformacoes.get(atributoEnum.name()).toString();
            } catch (Exception ex) {
            }
            return jsonOutrasInformacoes.getString(atributoEnum.name());
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer consultarQtdParcelasItem(Integer remessaItem) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select max(qtd) as qtd from ( \n");
        sql.append("select  \n");
        sql.append("'novo' as tipo, \n");
        sql.append("count(rim.codigo) as qtd \n");
        sql.append("from remessaitem ri \n");
        sql.append("inner join remessa r on r.codigo = ri.remessa \n");
        sql.append("left join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
        sql.append("where r.novoformato  \n");
        sql.append("and ri.codigo = ").append(remessaItem).append(" \n");
        sql.append("union \n");
        sql.append("select  \n");
        sql.append("'ant' as tipo, \n");
        sql.append("1 as qtd \n");
        sql.append("from remessaitem ri \n");
        sql.append("inner join remessa r on r.codigo = ri.remessa \n");
        sql.append("where r.novoformato = false \n");
        sql.append("and ri.codigo = ").append(remessaItem).append(") as sql \n");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("qtd");
                } else {
                    return 0;
                }
            }
        }
    }
}

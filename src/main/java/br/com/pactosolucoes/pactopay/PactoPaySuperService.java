package br.com.pactosolucoes.pactopay;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoContatoCRM;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfiguracaoEmailDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AlgoritmoCriptoEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PactoPayCobrancaAntecipadaVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayConfigVO;
import negocio.comuns.basico.PactoPayEnvioEmailVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.PactoPayEnvioEmailStatusEnum;
import negocio.comuns.financeiro.enumerador.StatusPactoPayComunicacaoEnum;
import negocio.comuns.financeiro.enumerador.TagReguaCobrancaPactoPayEnum;
import negocio.comuns.financeiro.enumerador.TipoEnvioPactoPayEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.PactoPayComunicacao;
import negocio.facade.jdbc.basico.PactoPayEnvioEmail;
import negocio.facade.jdbc.crm.Feriado;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import servicos.JenkinsService;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.pactoPay.PactoPayComunicacaoService;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.vendasonline.VendasOnlineService;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;

public class PactoPaySuperService extends SuperEntidade {

    //ASSUNTO DOS EMAILS ENVIADOS

    //ANTIGOS - Agora são sem o nome do cliente devido o envio em massa
//    public static String EMAIL_ASSUNTO_CARTAO_A_VENCER = "NOME_CLIENTE, seu cartão vence em breve!";
//    public static String EMAIL_ASSUNTO_CARTAO_VENCIDO = "NOME_CLIENTE, seu cartão está vencido!";
//    public static String EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_SEM_DESCONTO = "NOME_CLIENTE, sua cobrança está disponível!";
//    public static String EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_COM_DESCONTO = "NOME_CLIENTE, garanta descontos no seu pagamento!";
//    public static String EMAIL_ASSUNTO_COBRANCA_ATRASADA = "NOME_CLIENTE, seu pagamento está atrasado!";
//    public static String EMAIL_ASSUNTO_RESULTADO_COBRANCA_CANCELADA = "NOME_CLIENTE, seu pagamento foi cancelado!";
//    public static String EMAIL_ASSUNTO_RESULTADO_COBRANCA_NEGADA = "NOME_CLIENTE, seu pagamento foi negado!";
//    public static String EMAIL_ASSUNTO_RESULTADO_COBRANCA_APROVADA = "NOME_CLIENTE, seu pagamento foi aprovado!";

    public static String EMAIL_ASSUNTO_CARTAO_A_VENCER = "Seu cartão vence em breve!";
    public static String EMAIL_ASSUNTO_CARTAO_VENCIDO = "Seu cartão está vencido!";
    public static String EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_SEM_DESCONTO = "Sua cobrança está disponível!";
    public static String EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_COM_DESCONTO = "Garanta descontos no seu pagamento!";
    public static String EMAIL_ASSUNTO_COBRANCA_ATRASADA = "Seu pagamento está atrasado!";
    public static String EMAIL_ASSUNTO_RESULTADO_COBRANCA_CANCELADA = "Seu pagamento foi cancelado!";
    public static String EMAIL_ASSUNTO_RESULTADO_COBRANCA_NEGADA = "Seu pagamento foi negado!";
    public static String EMAIL_ASSUNTO_RESULTADO_COBRANCA_APROVADA = "Seu pagamento foi aprovado!";

    //MENSAGENS DE ENVIO DO SMS
    public static String SMS_CARTAO_A_VENCER = "Olá! Seu cartão de crédito utilizado para pagamento de parcelas vence no próximo mês. Entre em contato com a gente para atualizar os dados de cobrança.";
    public static String SMS_CARTAO_A_VENCER_COM_TAGS = ("Olá! Seu cartão de crédito utilizado para pagamento de parcelas vence no próximo mês. " + TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());
    public static String SMS_CARTAO_VENCIDO = "Olá! O cartão de crédito utilizado nas cobranças de suas parcelas está vencido. Atualize o número diretamente com a nossa equipe.";
    public static String SMS_CARTAO_VENCIDO_COM_TAGS = ("Olá! O cartão de crédito utilizado nas cobranças de suas parcelas está vencido. " + TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());
    public static String SMS_COBRANCA_ATRASADA = "Olá! A sua parcela deste mês está atrasada. Entre em contato com a gente e saiba como regularizar esta situação.";
    public static String SMS_COBRANCA_ATRASADA_COM_TAGS = ("Olá! A sua parcela deste mês está atrasada. " + TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());
    public static String SMS_RESULTADO_COBRANCA_APROVADA = "Olá! A cobrança da sua parcela deste mês foi aprovada. Para baixar o recibo e ver mais informações, entre em contato com a gente!";
    public static String SMS_RESULTADO_COBRANCA_APROVADA_COM_TAGS = "Olá! A cobrança da sua parcela deste mês foi aprovada. Para baixar o recibo e ver mais informações, entre em contato com a gente!";
    public static String SMS_RESULTADO_COBRANCA_NEGADA = "Olá! A cobrança da sua parcela deste mês foi negada. Tentaremos realizar outra cobrança em breve e te avisaremos assim que ela for aprovada.";
    public static String SMS_RESULTADO_COBRANCA_NEGADA_COM_TAGS = "Olá! A cobrança da sua parcela deste mês foi negada. Tentaremos realizar outra cobrança em breve e te avisaremos assim que ela for aprovada.";
    public static String SMS_RESULTADO_COBRANCA_CANCELADA = "Olá! Foi realizado o cancelamento de uma cobrança. Para receber o comprovante, entre em contato.";
    public static String SMS_RESULTADO_COBRANCA_CANCELADA_COM_TAGS = "Olá! Foi realizado o cancelamento de uma cobrança. Para receber o comprovante, entre em contato.";
    //MENSAGENS DE ENVIO DO WHATSAPP
    public static String WHATSAPP_CARTAO_A_VENCER = "Olá! Seu cartão de crédito utilizado para pagamento de parcelas vence no próximo mês. Entre em contato com a gente para atualizar os dados de cobrança.";
    public static String WHATSAPP_CARTAO_A_VENCER_COM_TAGS = ("Olá! Seu cartão de crédito utilizado para pagamento de parcelas vence no próximo mês. " + TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());
    public static String WHATSAPP_CARTAO_VENCIDO = "Olá! O cartão de crédito utilizado nas cobranças de suas parcelas está vencido. Atualize o número diretamente com a nossa equipe.";
    public static String WHATSAPP_CARTAO_VENCIDO_COM_TAGS = ("Olá! O cartão de crédito utilizado nas cobranças de suas parcelas está vencido. " + TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());
    public static String WHATSAPP_COBRANCA_ATRASADA = "Olá! A sua parcela deste mês está atrasada. Entre em contato com a gente e saiba como regularizar esta situação.";
    public static String WHATSAPP_COBRANCA_ATRASADA_COM_TAGS = ("Olá! A sua parcela deste mês está atrasada. " + TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());
    public static String WHATSAPP_RESULTADO_COBRANCA_APROVADA = "Olá! A cobrança da sua parcela deste mês foi aprovada. Para baixar o recibo e ver mais informações, entre em contato com a gente!";
    public static String WHATSAPP_RESULTADO_COBRANCA_APROVADA_COM_TAGS = "Olá! A cobrança da sua parcela deste mês foi aprovada. Para baixar o recibo e ver mais informações, entre em contato com a gente!";
    public static String WHATSAPP_RESULTADO_COBRANCA_NEGADA = "Olá! A cobrança da sua parcela deste mês foi negada. Tentaremos realizar outra cobrança em breve e te avisaremos assim que ela for aprovada.";
    public static String WHATSAPP_RESULTADO_COBRANCA_NEGADA_COM_TAGS = "Olá! A cobrança da sua parcela deste mês foi negada. Tentaremos realizar outra cobrança em breve e te avisaremos assim que ela for aprovada.";
    public static String WHATSAPP_RESULTADO_COBRANCA_CANCELADA = "Olá! Foi realizado o cancelamento de uma cobrança. Para receber o comprovante, entre em contato.";
    public static String WHATSAPP_RESULTADO_COBRANCA_CANCELADA_COM_TAGS = "Olá! Foi realizado o cancelamento de uma cobrança. Para receber o comprovante, entre em contato.";
    //MENSAGENS DE ENVIO DO APP
    public static String APP_CARTAO_A_VENCER = "Olá! Seu cartão de crédito utilizado para pagamento de parcelas vence no próximo mês. Entre em contato com a gente para atualizar os dados de cobrança.";
    public static String APP_CARTAO_A_VENCER_COM_TAGS = ("Olá! Seu cartão de crédito utilizado para pagamento de parcelas vence no próximo mês. " + TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());
    public static String APP_CARTAO_VENCIDO = "Olá! O cartão de crédito utilizado nas cobranças de suas parcelas está vencido. Atualize o número diretamente com a nossa equipe.";
    public static String APP_CARTAO_VENCIDO_COM_TAGS = ("Olá! O cartão de crédito utilizado nas cobranças de suas parcelas está vencido. " + TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());
    public static String APP_COBRANCA_ATRASADA = "Olá! A sua parcela deste mês está atrasada. Entre em contato com a gente e saiba como regularizar esta situação.";
    public static String APP_COBRANCA_ATRASADA_COM_TAGS = ("Olá! A sua parcela deste mês está atrasada. " + TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());
    public static String APP_RESULTADO_COBRANCA_APROVADA = "Olá! A cobrança da sua parcela deste mês foi aprovada. Para baixar o recibo e ver mais informações, entre em contato com a gente!";
    public static String APP_RESULTADO_COBRANCA_APROVADA_COM_TAGS = "Olá! A cobrança da sua parcela deste mês foi aprovada. Para baixar o recibo e ver mais informações, entre em contato com a gente!";
    public static String APP_RESULTADO_COBRANCA_NEGADA = "Olá! A cobrança da sua parcela deste mês foi negada. Tentaremos realizar outra cobrança em breve e te avisaremos assim que ela for aprovada.";
    public static String APP_RESULTADO_COBRANCA_NEGADA_COM_TAGS = "Olá! A cobrança da sua parcela deste mês foi negada. Tentaremos realizar outra cobrança em breve e te avisaremos assim que ela for aprovada.";
    public static String APP_RESULTADO_COBRANCA_CANCELADA = "Olá! Foi realizado o cancelamento de uma cobrança. Para receber o comprovante, entre em contato.";
    public static String APP_RESULTADO_COBRANCA_CANCELADA_COM_TAGS = "Olá! Foi realizado o cancelamento de uma cobrança. Para receber o comprovante, entre em contato.";

    public PactoPaySuperService(Connection conexao) throws Exception {
        super(conexao);
    }

    public String getUrlLogoEmpresa(EmpresaVO empresaVO) {
        try {
            String chave = DAO.resolveKeyFromConnection(this.con);
            String genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, empresaVO.getCodigo().toString());
            if (UteisValidacao.emptyString(genKey)) {
                genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA, empresaVO.getCodigo().toString());
            }
            return Uteis.getPaintFotoDaNuvem(genKey);
        } catch (Exception ignored) {
            return "";
        }
    }

    public String getUrlLinkPagamento(EmpresaVO empresaVO, ClienteVO clienteVO,
                                      boolean cobrarParcelas, OrigemCobrancaEnum origemCobrancaEnum,
                                      PactoPayCobrancaAntecipadaVO cobrancaAntecipadaVO,
                                      PactoPayComunicacaoVO comunicacaoVO) {
        VendasOnlineService vendasOnlineService;
        try {
            vendasOnlineService = new VendasOnlineService(null, this.con);
            String chave = DAO.resolveKeyFromConnection(this.con);
            return vendasOnlineService.obterLinkPagamentoVendasOnline(chave, clienteVO, empresaVO.getCodigo(), cobrarParcelas, origemCobrancaEnum,
                    cobrancaAntecipadaVO, null, comunicacaoVO, null, null, 1);
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        } finally {
            vendasOnlineService = null;
        }
    }


    public void processarTags(EmpresaVO empresaVO, ClienteVO clienteVO, StringBuilder mensagem,
                              OrigemCobrancaEnum origemCobrancaEnum, PactoPayCobrancaAntecipadaVO cobrancaAntecipadaVO,
                              boolean cobrarParcelas, AutorizacaoCobrancaClienteVO autoVO,
                              TransacaoVO transacaoVO, PactoPayComunicacaoVO comunicacaoVO, PactoPayConfigVO configVO) throws Exception {
        PactoPayComunicacao pactoPayComunicacaoDAO;
        try {
            pactoPayComunicacaoDAO = new PactoPayComunicacao(this.con);

            String urlLinkPagamento = "";
            String telefoneEmpresa = getTelefoneEmpresa(empresaVO);
            String emailEmpresa = getEmailEmpresa(empresaVO);

            JSONObject jsonTags = new JSONObject();
            jsonTags.put(TagReguaCobrancaPactoPayEnum.EMPRESA_NOME.getTag(), empresaVO.getNome().toUpperCase());
            jsonTags.put(TagReguaCobrancaPactoPayEnum.EMPRESA_CNPJ.getTag(), empresaVO.getCNPJ());
            jsonTags.put(TagReguaCobrancaPactoPayEnum.EMPRESA_RAZAO_SOCIAL.getTag(), empresaVO.getRazaoSocial().toUpperCase());
            jsonTags.put(TagReguaCobrancaPactoPayEnum.EMPRESA_TELEFONE.getTag(), telefoneEmpresa);
            jsonTags.put(TagReguaCobrancaPactoPayEnum.EMPRESA_EMAIL.getTag(), emailEmpresa);
            jsonTags.put(TagReguaCobrancaPactoPayEnum.EMPRESA_ENDERECO.getTag(), getEnderecoEmpresa(empresaVO));

            if (comunicacaoVO.getMeioEnvio().equals(MeioEnvio.EMAIL)) {
                boolean existeTelefone = !UteisValidacao.emptyString(telefoneEmpresa);
                boolean existeEmail = !UteisValidacao.emptyString(emailEmpresa);
                jsonTags.put(TagReguaCobrancaPactoPayEnum.EMPRESA_LOGO.getTag(), getUrlLogoEmpresa(empresaVO));
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_DISPLAY_DIV.getTag(), (existeTelefone && existeEmail) ? "" : "display: none;");
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_DISPLAY_TELEFONE.getTag(), existeTelefone ? "" : "display: none;");
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_DISPLAY_EMAIL.getTag(), existeEmail ? "" : "display: none;");
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_URL_UNSUBSCRIBE.getTag(), gerarLinkUnsubscribe(empresaVO, clienteVO, comunicacaoVO));

                PactoPayConfiguracaoEmailDTO configuracaoEmailDTO = (configVO != null && configVO.getConfiguracaoEmail() != null) ? configVO.getConfiguracaoEmail() : new PactoPayConfiguracaoEmailDTO();
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_COLOR.getTag(), UteisValidacao.emptyString(configuracaoEmailDTO.getCssCorEmail()) ? TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_COLOR.getPadrao() : configuracaoEmailDTO.getCssCorEmail());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_BTN_PAGAR_COLOR.getTag(), UteisValidacao.emptyString(configuracaoEmailDTO.getCssCorBtnPagar()) ? TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_BTN_PAGAR_COLOR.getPadrao() : configuracaoEmailDTO.getCssCorBtnPagar());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_BTN_COMPROVANTE_COLOR.getTag(), UteisValidacao.emptyString(configuracaoEmailDTO.getCssCorBtnComprovante()) ? TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_BTN_COMPROVANTE_COLOR.getPadrao() : configuracaoEmailDTO.getCssCorBtnComprovante());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_BTN_CADASTRAR_COLOR.getTag(), UteisValidacao.emptyString(configuracaoEmailDTO.getCssCorBtnCadastrar()) ? TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_CSS_BTN_CADASTRAR_COLOR.getPadrao() : configuracaoEmailDTO.getCssCorBtnCadastrar());
            }

            StringBuilder parcelas = new StringBuilder();
            for (MovParcelaVO parcelaVO : comunicacaoVO.getListaMovParcela()) {
                parcelas.append(parcelaVO.getDescricao()).append(" - Venc.: ").append(parcelaVO.getDataVencimento_Apresentar()).append(" - Valor: ").append(Formatador.formatarValorMonetario(parcelaVO.getValorParcela())).append("<br/>");
            }
            jsonTags.put(TagReguaCobrancaPactoPayEnum.PARCELAS.getTag(), parcelas.toString());

            if (clienteVO != null) {
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CLIENTE_NOME.getTag(), clienteVO.getPessoa().getNome());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CLIENTE_PRIMEIRO_NOME.getTag(), Uteis.getPrimeiroNome(clienteVO.getPessoa().getNome()));
            }

            if (mensagem != null) {
                jsonTags.put(TagReguaCobrancaPactoPayEnum.MENSAGEM.getTag(), mensagem.toString());
            }

            if (autoVO != null) {
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CARTAO_ULTIMOS_DIGITOS.getTag(), autoVO.getCartaoMascaradoUltimos4Digitos());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CARTAO_BANDEIRA.getTag(), autoVO.getOperadoraCartaoApresentar());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.CARTAO_VALIDADE.getTag(), autoVO.getValidadeCartao().replace("/20", "/"));
            }

            if (transacaoVO != null) {

                boolean apresentarBotaoPagarAgora = transacaoVO.getOrigem().equals(OrigemCobrancaEnum.ZW_AUTOMATICO);
                if (apresentarBotaoPagarAgora) {
                    urlLinkPagamento = validarUrlParaEnvio(getUrlLinkPagamento(empresaVO, clienteVO, true, origemCobrancaEnum, null, comunicacaoVO), comunicacaoVO);
                }

                jsonTags.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_COMPROVANTE_CANCELAMENTO.getTag(), validarUrlParaEnvio(transacaoVO.getUrlComprovanteCancelamento(), comunicacaoVO));
                jsonTags.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_LINK_RECIBO.getTag(), validarUrlParaEnvio(obterLinkDownloadRecibo(transacaoVO.getReciboPagamento()), comunicacaoVO));
                jsonTags.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_VALOR.getTag(), transacaoVO.getValor_Apresentar());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_NSU.getTag(), transacaoVO.getNSU());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_AUTORIZACAO.getTag(), transacaoVO.getAutorizacao());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_DATA.getTag(), transacaoVO.getDataProcessamento_Apresentar());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_DATA_CANCELAMENTO.getTag(), transacaoVO.getDataHoraCancelamento_Apresentar());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_COD_RETORNO.getTag(), transacaoVO.getCodigoRetornoGestaoTransacao());
                jsonTags.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_MOTIVO.getTag(), transacaoVO.getCodigoRetornoGestaoTransacaoMotivo().replace("Motivo:", ""));
                jsonTags.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_ULTIMOS_DIGITOS.getTag(), transacaoVO.getUltimos4Digitos());
            }

            if (UteisValidacao.emptyString(urlLinkPagamento)) {
                urlLinkPagamento = validarUrlParaEnvio(getUrlLinkPagamento(empresaVO, clienteVO, cobrarParcelas, origemCobrancaEnum, cobrancaAntecipadaVO, comunicacaoVO), comunicacaoVO);
            }

            jsonTags.put(TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag(), urlLinkPagamento);

            comunicacaoVO.setTags(jsonTags.toString());
            pactoPayComunicacaoDAO.alterarTags(comunicacaoVO);
        } finally {
            pactoPayComunicacaoDAO = null;
        }
    }

    public StringBuilder replaceInformacoes(File arquivoModelo, StringBuilder html, EmpresaVO empresaVO, ClienteVO clienteVO, StringBuilder mensagem,
                                            OrigemCobrancaEnum origemCobrancaEnum, PactoPayCobrancaAntecipadaVO cobrancaAntecipadaVO,
                                            boolean cobrarParcelas, AutorizacaoCobrancaClienteVO autoVO, TransacaoVO transacaoVO,
                                            PactoPayComunicacaoVO comunicacaoVO, PactoPayConfigVO configVO) throws Exception {

        //salvar o nome do arquivo usado para poder separar a fila para envio em massa de emails
        comunicacaoVO.setArquivoModelo(arquivoModelo != null ? arquivoModelo.getName() : "");

        //replace Head Header Footer
        if (comunicacaoVO.getMeioEnvio().equals(MeioEnvio.EMAIL)) {
            html = replaceHeadHeaderFooterEmailPreparaTagsSendy(html);
        }

        processarTags(empresaVO, clienteVO, mensagem, origemCobrancaEnum, cobrancaAntecipadaVO, cobrarParcelas, autoVO, transacaoVO, comunicacaoVO, configVO);

        String aux = html.toString();

        //email não altera a mensagem com as tags, quem altera é o sendy
        if (!comunicacaoVO.getMeioEnvio().equals(MeioEnvio.EMAIL)) {
            for (TagReguaCobrancaPactoPayEnum tagEnum : TagReguaCobrancaPactoPayEnum.values()) {
                aux = replaceTag(aux, comunicacaoVO, tagEnum);
            }
        }
        return new StringBuilder(aux);
    }

    public StringBuilder replaceHeadHeaderFooterEmailPreparaTagsSendy(StringBuilder html) {
        String aux1 = html.toString();
        aux1 = aux1.replaceAll(TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_HEAD.getTag(), obterHeadHTML());
        aux1 = aux1.replaceAll(TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_HEADER.getTag(), obterHeaderHTML());
        aux1 = aux1.replaceAll(TagReguaCobrancaPactoPayEnum.CONFIG_EMAIL_FOOTER.getTag(), obterFooterHTML());
        return replaceTagsParaSend(new StringBuilder(aux1));
    }

    public StringBuilder replaceTagsParaSend(StringBuilder html) {
        String aux1 = html.toString();
        for (TagReguaCobrancaPactoPayEnum tag : TagReguaCobrancaPactoPayEnum.values()) {
            String tagSendy = ("["+tag.getTag()+",fallback=]");
            aux1 = aux1.replaceAll(tag.getTag(), tagSendy);
        }
        return new StringBuilder(aux1);
    }

    private String replaceTag(String aux, PactoPayComunicacaoVO comunicacaoVO, TagReguaCobrancaPactoPayEnum tagEnum) {
        Object obj = comunicacaoVO.getTagsMap().get(tagEnum);
        String valorTag = (obj != null ? obj.toString() : "");
        valorTag = validarTrocarAcentuacao(valorTag, comunicacaoVO);
        return aux.replaceAll(tagEnum.getTag(), valorTag);
    }

    private String obterLinkDownloadRecibo(Integer recibo) {
        try {
            if (!UteisValidacao.emptyNumber(recibo)) {
                String chave = DAO.resolveKeyFromConnection(this.con);
                JSONObject json = new JSONObject();
                json.put("chave", chave);
                json.put("recibo", recibo);
                return (Uteis.getUrlAPI() + "/prest/util/" + chave + "/impressao/" + Uteis.encriptar(json.toString(), Uteis.getChaveCriptoImpressaoServlet()));
            }
        } catch (Exception ignored) {
        }
        return "";
    }

    private String getTelefoneEmpresa(EmpresaVO empresaVO) {
        String nrTelefone = empresaVO.getTelComercial1().trim();
        if (nrTelefone.length() < 8) {
            nrTelefone = empresaVO.getTelComercial2().trim();
        }
        if (nrTelefone.length() < 8) {
            nrTelefone = empresaVO.getTelComercial3().trim();
        }
        return nrTelefone;
    }

    private String getEmailEmpresa(EmpresaVO empresaVO) {
        if (!UteisValidacao.emptyString(empresaVO.getEmail().trim())) {
            return empresaVO.getEmail();
        }
        return "";
    }

    private String getEnderecoEmpresa(EmpresaVO empresaVO) {
        StringBuilder end = new StringBuilder();

        if (!UteisValidacao.emptyString(empresaVO.getEndereco().trim())) {
            end.append(empresaVO.getEndereco());
        }

        if (!UteisValidacao.emptyString(empresaVO.getCidade().getNome().trim())) {
            if (end.length() > 0) {
                end.append(" - ");
            }
            end.append(empresaVO.getCidade().getNome());
        }

        if (!UteisValidacao.emptyString(empresaVO.getEstado().getSigla().trim())) {
            if (end.length() > 0) {
                end.append(" - ");
            }
            end.append(empresaVO.getEstado().getSigla());
        }
        return end.toString();
    }

    public void enviarSMS(EmpresaVO empresaVO, String codigoReferencia, List<Message> listaMensagensSMS) throws Exception {
        String chaveEnvioSMS = Uteis.getSMSChaveFacilitePay();
        String tokenEnvioSMS = Uteis.getSMSTokenFacilitePay();
        SmsController smsController = new SmsController(tokenEnvioSMS, chaveEnvioSMS, TimeZone.getTimeZone(empresaVO.getTimeZoneDefault()));
        String retorno = smsController.sendMessage(codigoReferencia, listaMensagensSMS);
        if (retorno == null || !retorno.contains("status: ok")) {
            throw new Exception("ERRO ENVIO SMS. Retorno: " + retorno);
        }
    }

    public PactoPayComunicacaoVO iniciarComunicado(PactoPayComunicacaoVO pactoPayComunicacaoVO,
                                                   String key, String codigoReferencia,
                                                   EmpresaVO empresaVO, String assunto, String mensagemHTML,
                                                   ClienteVO clienteVO, TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum,
                                                   String observacaoHistorico, Integer codigoOrigem, Set<String> destinatario,
                                                   boolean exemplo, boolean enviarSemJenkins, PactoPayConfigVO configVO) throws Exception {
        return iniciarComunicado(pactoPayComunicacaoVO,
                key, codigoReferencia,
                empresaVO, assunto, mensagemHTML,
                clienteVO, tipoEnvioPactoPayEnum,
                observacaoHistorico, codigoOrigem, destinatario,
                exemplo, enviarSemJenkins, null, configVO);
    }

    public PactoPayComunicacaoVO iniciarComunicado(PactoPayComunicacaoVO pactoPayComunicacaoVO,
                                                   String key, String codigoReferencia,
                                                   EmpresaVO empresaVO, String assunto, String mensagemHTML,
                                                   ClienteVO clienteVO, TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum,
                                                   String observacaoHistorico, Integer codigoOrigem, Set<String> destinatario,
                                                   boolean exemplo, boolean enviarSemJenkins, String link, PactoPayConfigVO configVO) throws Exception {
        PactoPayComunicacao pactoPayComunicacaoDAO;
        try {
            pactoPayComunicacaoDAO = new PactoPayComunicacao(this.con);

            PactoPayComunicacaoDTO comunicacaoTO = new PactoPayComunicacaoDTO();
            comunicacaoTO.setChave(key);
            comunicacaoTO.setCodigoReferencia(codigoReferencia);
            comunicacaoTO.setCodigoOrigem(codigoOrigem);
            comunicacaoTO.setEmpresa(empresaVO.getCodigo());
            comunicacaoTO.setPessoa(clienteVO.getPessoa().getCodigo());
            comunicacaoTO.setCliente(clienteVO.getCodigo());
            comunicacaoTO.setTipoEnvioPactoPay(tipoEnvioPactoPayEnum.getId());
            comunicacaoTO.setMeioEnvio(pactoPayComunicacaoVO.getMeioEnvio().getCodigo());
            comunicacaoTO.setAssunto(assunto);
            comunicacaoTO.setMensagem(mensagemHTML);
            comunicacaoTO.setObservacaoHistorico(observacaoHistorico);
            comunicacaoTO.setDestinatario(new ArrayList<>(destinatario));
            comunicacaoTO.setExemplo(exemplo);
            comunicacaoTO.setEnvioSemJenkins(enviarSemJenkins);
            comunicacaoTO.setLink(link);
            comunicacaoTO.setArquivoModelo(pactoPayComunicacaoVO.getArquivoModelo());

            final String chaveJobJenkins = obterChaveJobJenkins(key, pactoPayComunicacaoVO.getMeioEnvio());
            comunicacaoTO.setChaveJobJenkins(chaveJobJenkins);

            //GYMBOT
            if (pactoPayComunicacaoVO.getMeioEnvio().equals(MeioEnvio.GYMBOT)) {
                if (tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.CARTAO_A_VENCER) ||
                        tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.CARTAO_VENCIDO)) {
                    comunicacaoTO.setUrlWebhookGymBot(configVO.getConfiguracaoGymBot().getUrlCobrancaCartao());
                } else if (tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.COBRANCA_ANTECIPADA)) {
                    comunicacaoTO.setUrlWebhookGymBot(configVO.getConfiguracaoGymBot().getUrlCobrancaAntecipada());
                } else if (tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.PARCELA_PENDENTE)) {
                    comunicacaoTO.setUrlWebhookGymBot(configVO.getConfiguracaoGymBot().getUrlCobrancaPendente());
                } else if (tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.RESULTADO_COBRANCA)) {
                    comunicacaoTO.setUrlWebhookGymBot(configVO.getConfiguracaoGymBot().getUrlCobrancaResultadoCobranca());
                }

            }
            //GYMBOT PRO
            if (pactoPayComunicacaoVO.getMeioEnvio().equals(MeioEnvio.GYMBOT_PRO)) {
                if (tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.CARTAO_A_VENCER) ||
                        tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.CARTAO_VENCIDO)) {
                    comunicacaoTO.setTokenGymBotPro(configVO.getConfiguracaoGymBotPro().getTokenComunicadoCartao());
                    comunicacaoTO.setIdFluxoGymBotPro(configVO.getConfiguracaoGymBotPro().getIdIntegracaoComunicadoCartao());
                } else if (tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.COBRANCA_ANTECIPADA)) {
                    comunicacaoTO.setTokenGymBotPro(configVO.getConfiguracaoGymBotPro().getTokenCobrancaAntecipada());
                    comunicacaoTO.setIdFluxoGymBotPro(configVO.getConfiguracaoGymBotPro().getIdIntegracaoCobrancaAntecipada());
                } else if (tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.PARCELA_PENDENTE)) {
                    comunicacaoTO.setTokenGymBotPro(configVO.getConfiguracaoGymBotPro().getTokenComunicadoAtraso());
                    comunicacaoTO.setIdFluxoGymBotPro(configVO.getConfiguracaoGymBotPro().getIdIntegracaoComunicadoAtraso());
                } else if (tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.RESULTADO_COBRANCA)) {
                    comunicacaoTO.setTokenGymBotPro(configVO.getConfiguracaoGymBotPro().getTokenComunicadoResultadoCobranca());
                    comunicacaoTO.setIdFluxoGymBotPro(configVO.getConfiguracaoGymBotPro().getIdIntegracaoComunicadoResultadoCobranca());
                }
            }

            pactoPayComunicacaoVO.processarComunicacao(comunicacaoTO, configVO);
            pactoPayComunicacaoDAO.gravarSemCommit(pactoPayComunicacaoVO);

            //resultado de cobranca envia a comunicação sem ser em massa - envio individual
            boolean envioDeEmailEmMassa = (pactoPayComunicacaoVO.getMeioEnvio().equals(MeioEnvio.EMAIL) &&
                    !pactoPayComunicacaoVO.getTipoEnvioPactoPay().equals(TipoEnvioPactoPayEnum.RESULTADO_COBRANCA));

            //validar se pode enviar
            if (!pactoPayComunicacaoVO.getTipoEnvioPactoPay().equals(TipoEnvioPactoPayEnum.RESULTADO_COBRANCA)) {
                diaHorarioPermiteEnviarAgora(pactoPayComunicacaoVO.getEmpresaVO());
            }

            if (envioDeEmailEmMassa) {

                pactoPayComunicacaoVO.setStatus(StatusPactoPayComunicacaoEnum.FILA_ENVIO_MASSA);
                pactoPayComunicacaoDAO.gravarSemCommit(pactoPayComunicacaoVO);

            } else if (exemplo || enviarSemJenkins) {
                //como ele consulta pelo código deve atualizar o obj que foi passado pois mais na frente ele grava no banco esse obj
                pactoPayComunicacaoVO = realizarEnvioDireto(pactoPayComunicacaoVO);

            } else {

                diaHorarioPermiteEnviarAgora(pactoPayComunicacaoVO.getEmpresaVO());
                JenkinsService.createTaskPactoPay(chaveJobJenkins, pactoPayComunicacaoVO.getCodigo().toString(), null, null);
                JenkinsService.buildTaskPactoPay(chaveJobJenkins, pactoPayComunicacaoVO.getCodigo().toString(), null);

            }
        } finally {
            pactoPayComunicacaoDAO = null;
        }
        return pactoPayComunicacaoVO;
    }

    public void diaHorarioPermiteEnviarAgora(EmpresaVO empresaVO) throws Exception {
        Feriado feriadoDAO;
        try {
            feriadoDAO = new Feriado(this.con);

            DiaSemana diaSemana = DiaSemana.getDiaSemana(Calendario.getDiaDaSemanaAbreviado(Calendario.hoje()));
            if (diaSemana.equals(DiaSemana.DOMINGO)) {
                throw new Exception("Domingo não é permitido envio de comunicação");
            }

            String horaInicialPodeEnviar = "08:00";
            String horaFinalPodeEnviar = "20:00";
            if (diaSemana.equals(DiaSemana.SABADO)) {
                horaFinalPodeEnviar = "14:00";
            }

            String horaAtual = Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "HH:mm");
            if (!Calendario.horaEstaEntreIntervaloHoras(horaAtual, horaInicialPodeEnviar, horaFinalPodeEnviar)) {
                throw new Exception("Fora do horário permitido para envio de comunicação | Dia semana atual: " + diaSemana.getDescricao() + " | Hora atual: " + horaAtual);
            }

            // validar se existe feriado cadastrado
            if (empresaVO != null && !UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
                boolean feriado = feriadoDAO.validarFeriadoPorEmpresaParaCalculoAberturaMeta(empresaVO, Calendario.hoje());
                if (feriado) {
                    throw new ConsistirException("Feriado cadastrado: " + Uteis.getData(Calendario.hoje()) + " | Não é permitido envio de comunicação");
                }
            }
        } finally {
            feriadoDAO = null;
        }
    }

    public String montarCron() throws ConsistirException {
        Date dataEnvio = Calendario.hoje();
        String cron = "";
//        if (UteisValidacao.emptyNumber(horaInicio)) {
//            horaInicio = 0;
//        }
//        if (UteisValidacao.emptyNumber(horaFim)) {
//            horaFim = 23;
//        }
//        String hora = "(" + horaInicio + "-" + horaFim + ")";
//       String dia = "" + Uteis.getDiaMesData(dataEnvio);
//       String diaSemana = "*";
//       String meses = "" + Uteis.getMesData(dataEnvio);
//
//        if(getOcorrencia().equals(OcorrenciaEnum.INCLUSAO_VISITANTE)){
//            cron = "";
//        }else{
//            cron = "H\tH" + hora + "\t" + dia + "\t" + meses + "\t" + diaSemana;
//        }
        return cron;
    }

    public String obterChaveJobJenkins(String key, MeioEnvio meioEnvio) {
        return String.format("%s_%s", key, meioEnvio.name());
    }

    public String obterChaveJobJenkinsEmailLista(String key) {
        return String.format("%s_%s_%s", key, MeioEnvio.EMAIL.name(), "LISTA");
    }

    public boolean recebeuContato(Integer codPessoa, Integer origemCodigo, Date dia,
                                  TipoContatoCRM tipoContatoCRM, TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select exists( \n");
        sql.append("select \n");
        sql.append("h.codigo \n");
        sql.append("from historicocontato h \n");
        sql.append("inner join cliente cl on cl.codigo = h.cliente \n");
        sql.append("where h.origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("' \n");
        sql.append("and h.dia::date = '").append(Uteis.getDataFormatoBD(dia)).append("' \n");
        sql.append("and cl.pessoa = ").append(codPessoa).append(" \n");
        sql.append("and h.tipocontato = '").append(tipoContatoCRM.getSigla()).append("' \n");
        if (!UteisValidacao.emptyNumber(origemCodigo)) {
            sql.append("and h.origemcodigo = ").append(origemCodigo).append(" \n");
        }
        sql.append(") as recebeu_contato \n");
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getBoolean(1);
                } else {
                    return false;
                }
            }
        }
    }

    public String processarAssunto(String assunto, ClienteVO clienteVO) {
        return assunto.replace("NOME_CLIENTE", Uteis.getPrimeiroNome(clienteVO.getPessoa().getNome()));
    }

    private String obterHeadHTML() {
        try {
            File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_head.txt").toURI());
            return FileUtilities.readContentFile(arq.getAbsolutePath()).toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private String obterHeaderHTML() {
        try {
            File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_header.txt").toURI());
            return FileUtilities.readContentFile(arq.getAbsolutePath()).toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private String obterFooterHTML() {
        try {
            File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_footer.txt").toURI());
            return FileUtilities.readContentFile(arq.getAbsolutePath()).toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private PactoPayComunicacaoVO realizarEnvioDireto(PactoPayComunicacaoVO pactoPayComunicacaoVO) throws Exception {
        PactoPayComunicacaoService service = new PactoPayComunicacaoService(this.con);
        //como ele consulta pelo código deve atualizar o obj que foi passado pois mais na frente ele grava no banco esse obj
        pactoPayComunicacaoVO = service.enviarPactoPayComunicacao(pactoPayComunicacaoVO.getCodigo());
        return pactoPayComunicacaoVO;
    }

    private PactoPayEnvioEmailVO realizarEnvioDiretoListaEmail(PactoPayEnvioEmailVO pactoPayEnvioEmailVO) throws Exception {
        PactoPayComunicacaoService service = new PactoPayComunicacaoService(this.con);
        //como ele consulta pelo código deve atualizar o obj que foi passado pois mais na frente ele grava no banco esse obj
        pactoPayEnvioEmailVO = service.enviarPactoPayEnvioEmail(pactoPayEnvioEmailVO.getCodigo());
        return pactoPayEnvioEmailVO;
    }

    public String obterAppKey(String key, Integer empresa) {
        return (key + "_" + empresa + "_PACTOPAY");
    }

    public ConfiguracaoSistemaCRMVO obterConfiguracaoEmail(PactoPayConfigVO configVO) throws Exception {
//        ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO;
        try {
            //para teste
//            ConfiguracaoSistemaCRMVO configCRMVO = SuperControle.getConfiguracaoSMTPNoReply();
//            configCRMVO.setRemetentePadrao("Sistema Pacto");
//            return configCRMVO;

//            configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(this.con);
//            return configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            return configVO.getConfiguracaoEmail().obterConfigCRMVO();
        } finally {
//            configuracaoSistemaCRMDAO = null;
        }
    }

    public String gerarLinkUnsubscribe(EmpresaVO empresaVO, ClienteVO clienteVO, PactoPayComunicacaoVO comunicacaoVO) {
        try {
            if (comunicacaoVO != null &&
                    comunicacaoVO.getMeioEnvio() != null &&
                    !comunicacaoVO.getMeioEnvio().equals(MeioEnvio.EMAIL)) {
                return "";
            }

            String chave = DAO.resolveKeyFromConnection(this.con);
            String urlUnsubscribe = PropsService.getPropertyValue(PropsService.urlVendasOnline) + "/unsubscribe-optin?key=";
            String parametrosEmpresa = chave + "&" + empresaVO.getCodigo() + "&" + empresaVO.getEmail() + "&";
            parametrosEmpresa = Criptografia.encrypt(parametrosEmpresa, SuperControle.Crypt_KEY, AlgoritmoCriptoEnum.ALGORITMO_AES);

            String parametrosCliente = "&TAG_CODCLIENTE" + "&TAG_EMAIL_CLIENTE";
            parametrosCliente = Criptografia.encrypt(parametrosCliente, SuperControle.Crypt_KEY, AlgoritmoCriptoEnum.ALGORITMO_AES);
            return (urlUnsubscribe + parametrosEmpresa + "+" + parametrosCliente);
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private String encurtarURLBitly(String urlEncurtar) {
        try {
            if (UteisValidacao.emptyString(urlEncurtar)) {
                throw new Exception("URL para encurtar não informada.");
            }

            String tokenBitlyFacilitePay = Uteis.getTokenBitlyFacilitePay();
            if (UteisValidacao.emptyString(tokenBitlyFacilitePay)) {
                throw new Exception("Sem token do Bitly preenchido.");
            }

            String urlAPIBitly = (Uteis.getUrlAPIBitlyV4() + "/shorten");
            JSONObject body = new JSONObject();
            body.put("domain", "bit.ly");
            body.put("long_url", urlEncurtar);

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            headers.put("Authorization", "Bearer " + tokenBitlyFacilitePay);

            RequestHttpService requestHttpService = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = requestHttpService.executeRequest(urlAPIBitly, headers, null, body.toString(), MetodoHttpEnum.POST);
            String erro = "";
            try {
                String linkCurto = new JSONObject(respostaHttpDTO.getResponse()).optString("link");
                if (UteisValidacao.emptyString(linkCurto)) {
                    throw new Exception("Erro gerar link");
                }
                return linkCurto;
            } catch (Exception ignored) {
                try {
                    erro = new JSONObject(respostaHttpDTO).toString();
                } catch (Exception ex2) {
                    erro = respostaHttpDTO.getResponse();
                }
            }
            throw new Exception("Não foi possível encurtar a url. | " + erro);
        } catch (Exception ex) {
//            ex.printStackTrace();
            return null;
        }
    }

    private String validarUrlParaEnvio(String urlEncurtar, PactoPayComunicacaoVO comunicacaoVO) throws Exception {

        //aqui caso seja necessário encurtar a url (hoje só iremos fazer isso para o SMS)
        if (comunicacaoVO.getMeioEnvio().equals(MeioEnvio.SMS)) {

            //caso não consiga tem que dar exceção para que seja utilizado a mensagem sem o link.
            String urlCurta = encurtarURLBitly(urlEncurtar);
            if (UteisValidacao.emptyString(urlCurta)) {
                throw new Exception("Não foi possível encurtar a url.");
            }
            return urlCurta;
        }

        return urlEncurtar;
    }

    public String obterMensagemEnviarSMS(String mensagemSemTags, String mensagemComTags,
                                         EmpresaVO empresaVO, ClienteVO clienteVO,
                                         OrigemCobrancaEnum origemCobrancaEnum,
                                         boolean cobrarParcelas,
                                         TransacaoVO transacaoVO,
                                         AutorizacaoCobrancaClienteVO autoVO,
                                         PactoPayCobrancaAntecipadaVO cobrancaAntecipadaVO,
                                         PactoPayComunicacaoVO pactoPayComunicacaoVO,
                                         PactoPayConfigVO configVO) {
        try {
            return replaceInformacoes(null, new StringBuilder(mensagemComTags), empresaVO, clienteVO, null,
                    origemCobrancaEnum, cobrancaAntecipadaVO,
                    cobrarParcelas, autoVO,
                    transacaoVO, pactoPayComunicacaoVO, configVO).toString();
        } catch (Exception ex) {
            //caso ocorra algum erro no replace é devido provavelmente a encurtar url para fazer o envio.
            //então será enviado nesse caso a mensagem sem as tags
//            ex.printStackTrace();
            return mensagemSemTags;
        }
    }

    private String validarTrocarAcentuacao(String texto, PactoPayComunicacaoVO pactoPayComunicacaoVO) {
        if (pactoPayComunicacaoVO.getMeioEnvio().equals(MeioEnvio.EMAIL)) {
            return Uteis.trocarAcentuacaoPorAcentuacaoHTML(texto);
        }
        return texto;
    }

    public void configuracaoInicialGymBot(String urlWebHook, TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum) throws Exception {

        if (UteisValidacao.emptyString(urlWebHook) || !urlWebHook.toLowerCase().startsWith("http")) {
            throw new Exception("URL informada não é válida");
        }

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");

        JSONObject body = new JSONObject();
        body.put(TagReguaCobrancaPactoPayEnum.CLIENTE_NOME.getNomeEnvio(), "NOME MOCK");
        body.put(TagReguaCobrancaPactoPayEnum.CLIENTE_TELEFONE.getNomeEnvio(), "5511999999999");
        body.put(TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getNomeEnvio(), "LINK_MOCK");
        if (tipoEnvioPactoPayEnum != null) {
            if (tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.CARTAO_A_VENCER) ||
                    tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.CARTAO_VENCIDO)) {
                body.put(TagReguaCobrancaPactoPayEnum.CARTAO_ULTIMOS_DIGITOS.getNomeEnvio(), "9999");
            }
            if (tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.PARCELA_PENDENTE) ||
                    tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.COBRANCA_ANTECIPADA)) {
                body.put(TagReguaCobrancaPactoPayEnum.PARCELAS.getNomeEnvio(), "PARCELAS_MOCK");
            }
            if (tipoEnvioPactoPayEnum.equals(TipoEnvioPactoPayEnum.RESULTADO_COBRANCA)) {
                body.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_VALOR.getNomeEnvio(), "TRANSACAO_VALOR");
                body.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_NSU.getNomeEnvio(), "TRANSACAO_NSU");
                body.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_AUTORIZACAO.getNomeEnvio(), "TRANSACAO_AUTORIZACAO");
                body.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_DATA.getNomeEnvio(), "TRANSACAO_DATA");
                body.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_DATA_CANCELAMENTO.getNomeEnvio(), "TRANSACAO_DATA_CANCELAMENTO");
                body.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_COD_RETORNO.getNomeEnvio(), "TRANSACAO_COD_RETORNO");
                body.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_MOTIVO.getNomeEnvio(), "TRANSACAO_MOTIVO");
                body.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_ULTIMOS_DIGITOS.getNomeEnvio(), "TRANSACAO_ULTIMOS_DIGITOS");
                body.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_COMPROVANTE_CANCELAMENTO.getNomeEnvio(), "TRANSACAO_COMPROVANTE_CANCELAMENTO");
                body.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_LINK_RECIBO.getNomeEnvio(), "TRANSACAO_LINK_RECIBO");
            }
        }

        String msgErro = "";
        try {
            RequestHttpService requestHttpService = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = requestHttpService.executeRequest(urlWebHook, headers, null, body.toString(), MetodoHttpEnum.POST);
            requestHttpService = null;
            if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
                msgErro = respostaHttpDTO.getRequestBody();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            msgErro = ex.getMessage();
        }

        if (!UteisValidacao.emptyString(msgErro)) {
            if (msgErro.toLowerCase().contains("illegal character")) {
                msgErro = "URL informada não é válida, caractere inválido.";
            }
            throw new Exception("Erro GymBot: " + msgErro);
        }
    }

    public boolean empresaTemAplicativo(String chave, EmpresaVO empresaVO) {
        try {
            return empresaVO.isFacilitePayReguaCobrancaApp();
        } catch (Exception ex) {
            return false;
        }
    }

    public boolean empresaTemGymBot(String chave, EmpresaVO empresaVO) {
        try {
            return empresaVO.isFacilitePayReguaCobrancaWhatsApp();
        } catch (Exception ex) {
            return false;
        }
    }

    public boolean empresaTemGymBotPro(String chave, EmpresaVO empresaVO) {
        try {
            return empresaVO.isFacilitePayReguaCobrancaGymbotPro();
        } catch (Exception ex) {
            return false;
        }
    }

    public boolean empresaTemSMS(String chave, EmpresaVO empresaVO) {
        try {
            return empresaVO.isFacilitePayReguaCobrancaSms();
        } catch (Exception ex) {
            return false;
        }
    }

    public boolean empresaTemEmail(String chave, EmpresaVO empresaVO, PactoPayConfigVO configVO) {
        try {
            ConfiguracaoSistemaCRMVO configCRMVO = obterConfiguracaoEmail(configVO);
            return empresaVO.isFacilitePayReguaCobrancaEmail() && configCRMVO.isConfiguracaoEmailValida();
        } catch (Exception ex) {
            return false;
        }
    }

    public void iniciarComunicadoEnvioEmMassaEmail(String key, List<PactoPayComunicacaoVO> listaEnviar,
                                                   boolean enviarSemJenkins) throws Exception {
        if (UteisValidacao.emptyList(listaEnviar)) {
            return;
        }

        Map<String, List<PactoPayComunicacaoVO>> mapaModelo = new HashMap<>();
        for (PactoPayComunicacaoVO comunicacaoVO : listaEnviar) {
            List<PactoPayComunicacaoVO> lista = mapaModelo.get(comunicacaoVO.getPactoPayComunicacaoDTO().getArquivoModelo());
            if (lista == null) {
                lista = new ArrayList<>();
            }

            if (!comunicacaoVO.getStatus().equals(StatusPactoPayComunicacaoEnum.FILA_ENVIO_MASSA)) {
                continue;
            }
            lista.add(comunicacaoVO);
            mapaModelo.put(comunicacaoVO.getPactoPayComunicacaoDTO().getArquivoModelo(), lista);
        }


        for (String modeloArquivo : mapaModelo.keySet()) {
            PactoPayEnvioEmail pactoPayEnvioEmailDAO = new PactoPayEnvioEmail(this.con);
            PactoPayEnvioEmailVO obj = new PactoPayEnvioEmailVO();
            try {
                obj.setStatus(PactoPayEnvioEmailStatusEnum.CRIADO);

                List<PactoPayComunicacaoVO> lista = mapaModelo.get(modeloArquivo);
                if (UteisValidacao.emptyList(lista)) {
                    continue;
                }

                File arquivoModelo = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/" + modeloArquivo).toURI());
                StringBuilder html = FileUtilities.readContentFile(arquivoModelo.getAbsolutePath());
                html = replaceHeadHeaderFooterEmailPreparaTagsSendy(html);

                PactoPayComunicacaoVO comunicacaoBaseVO = lista.get(0);

                obj.setPactoPayConfigVO(comunicacaoBaseVO.getPactoPayConfigVO());
                obj.setEmpresaVO(comunicacaoBaseVO.getEmpresaVO());
                obj.setMensagem(html.toString());
                obj.setAssunto(comunicacaoBaseVO.getPactoPayComunicacaoDTO().getAssunto());
                obj.setListaPactoPayComunicacao(lista);

                final String chaveJobJenkins = obterChaveJobJenkinsEmailLista(key);

                PactoPayEnvioEmailDadosDTO dadosDTO = new PactoPayEnvioEmailDadosDTO();
                dadosDTO.setChaveJobJenkins(chaveJobJenkins);
                dadosDTO.setEnvioSemJenkins(enviarSemJenkins);
                obj.setDados(dadosDTO.toString());

                pactoPayEnvioEmailDAO.gravarSemCommit(obj);

                if (enviarSemJenkins) {
                    //como ele consulta pelo código deve atualizar o obj que foi passado pois mais na frente ele grava no banco esse obj
                    obj = realizarEnvioDiretoListaEmail(obj);
                } else {

                    diaHorarioPermiteEnviarAgora(obj.getEmpresaVO());
                    JenkinsService.createTaskPactoPay(chaveJobJenkins, obj.getCodigo().toString(), null, obj);
                    JenkinsService.buildTaskPactoPay(chaveJobJenkins, obj.getCodigo().toString(), obj);
                    obj.setStatus(PactoPayEnvioEmailStatusEnum.ENVIADO_JENKINS);
                }

            } catch (Exception ex) {
                ex.printStackTrace();
                obj.setStatus(PactoPayEnvioEmailStatusEnum.ERRO);
                obj.setResposta(ex.getMessage());
            } finally {
                pactoPayEnvioEmailDAO.gravarSemCommit(obj);
            }
        }
    }
}

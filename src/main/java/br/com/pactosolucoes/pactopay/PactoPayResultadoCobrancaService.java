package br.com.pactosolucoes.pactopay;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.enumeradores.TipoContatoCRM;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayConfigVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.TransacaoCieloVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoEnvioPactoPayEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.PactoPayComunicacao;
import negocio.facade.jdbc.basico.PactoPayConfig;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.cieloecommerce.CieloECommerceRetornoEnum;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashSet;
import java.util.Set;

public class PactoPayResultadoCobrancaService extends PactoPaySuperService {

    public PactoPayResultadoCobrancaService(Connection conexao) throws Exception {
        super(conexao);
    }

    public void processar(TransacaoVO transacaoVO, boolean exception, Integer codigoPessoa, boolean enviarSemJenkins) throws Exception {
        PactoPayConfig pactoPayConfigDAO;
        try {
            pactoPayConfigDAO = new PactoPayConfig(this.con);

            if (transacaoVO == null ||
                    UteisValidacao.emptyNumber(transacaoVO.getCodigo()) ||
                    !(transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                            transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) ||
                            transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA))) {
                Uteis.logarDebug("Transação não está em algumas das situações (CONCLUIDA_COM_SUCESSO, CANCELADA, NAO_APROVADA). Ignorar envio PactoPayDashResultado...");
                return;
            }

            TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum = TipoEnvioPactoPayEnum.RESULTADO_COBRANCA;

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT \n");
            sql.append("p.codigo as pessoa_codigo, \n");
            sql.append("p.nome as cliente_nome, \n");
            sql.append("cl.codigo as cliente_codigo, \n");
            sql.append("cl.matricula as cliente_matricula, \n");
            sql.append("array_to_string(array(SELECT numero FROM telefone WHERE pessoa = cl.pessoa and tipotelefone = 'CE'), '###', '') as cliente_celular, \n");
            sql.append("array_to_string(array(SELECT email FROM email WHERE bloqueadobounce IS FALSE  and pessoa = cl.pessoa), '###', '') as cliente_emails, \n");

            sql.append("not exists(select codigo from historicocontato where origemcodigo = t.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_EMAIL.getSigla()).append("' ");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("' ");
            sql.append("and dia::date = '").append(Uteis.getDataFormatoBD(Calendario.hoje())).append("') as pode_enviar_email, \n");

            sql.append("not exists(select codigo from historicocontato where origemcodigo = t.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_SMS.getSigla()).append("' ");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("' ");
            sql.append("and dia::date = '").append(Uteis.getDataFormatoBD(Calendario.hoje())).append("') as pode_enviar_sms, \n");

            sql.append("not exists(select codigo from historicocontato where origemcodigo = t.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_GYMBOT.getSigla()).append("' ");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("' ");
            sql.append("and dia::date = '").append(Uteis.getDataFormatoBD(Calendario.hoje())).append("') as pode_enviar_whatsapp, \n");

            sql.append("not exists(select codigo from historicocontato where origemcodigo = t.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_APP.getSigla()).append("' ");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("' ");
            sql.append("and dia::date = '").append(Uteis.getDataFormatoBD(Calendario.hoje())).append("') as pode_enviar_app, \n");

            sql.append("not exists(select codigo from historicocontato where origemcodigo = t.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_GYMBOT_PRO.getSigla()).append("' ");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("' ");
            sql.append("and dia::date = '").append(Uteis.getDataFormatoBD(Calendario.hoje())).append("') as pode_enviar_gymbotpro, \n");

            sql.append("usm.codigo as usuariomovel_codigo, \n");
            sql.append("usm.nome as usuariomovel_nome \n");
            sql.append("FROM transacao t \n");
            sql.append("INNER JOIN empresa e on e.codigo = t.empresa  \n");
            sql.append("INNER JOIN pactopayconfig pc on pc.empresa = e.codigo \n");
            sql.append("INNER JOIN pessoa p on p.codigo = t.pessoapagador \n");
            sql.append("INNER JOIN cliente cl on cl.pessoa = p.codigo \n");
            sql.append("LEFT JOIN usuariomovel usm on usm.codigo = (select max(codigo) from usuariomovel where ativo and cliente = cl.codigo) \n");
            sql.append("WHERE pc.comunicadoResultadoCobrancaAtivo \n");
            sql.append("AND t.transacaoverificarcartao = false \n");
            sql.append("AND t.codigo = ").append(transacaoVO.getCodigo()).append(" \n");
            sql.append("AND ( \n");
            sql.append("(pc.comunicadoResultadoCobrancaAprovada and t.situacao = ").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(") or \n");
            sql.append("(pc.comunicadoResultadoCobrancaNegada and t.situacao = ").append(SituacaoTransacaoEnum.NAO_APROVADA.getId()).append(") or \n");
            sql.append("(pc.comunicadoResultadoCobrancaCancelada and t.situacao = ").append(SituacaoTransacaoEnum.CANCELADA.getId()).append(") \n");
            sql.append(") \n");

            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        PactoPayConfigVO configVO = pactoPayConfigDAO.consultarPorEmpresa(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                        boolean pode_enviar_sms = rs.getBoolean("pode_enviar_sms");
                        boolean pode_enviar_email = rs.getBoolean("pode_enviar_email");
                        boolean pode_enviar_whatsapp = rs.getBoolean("pode_enviar_whatsapp");
                        boolean pode_enviar_app = rs.getBoolean("pode_enviar_app");
                        boolean pode_enviar_gymbotpro = rs.getBoolean("pode_enviar_gymbotpro");

                        //caso tenha codigopessoa é pq está forçando o envio então vamos forçar
                        if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                            pode_enviar_sms = true;
                            pode_enviar_email = true;
                            pode_enviar_whatsapp = true;
                            pode_enviar_app = true;
                            pode_enviar_gymbotpro = true;
                        }

                        PessoaVO pessoaVO = new PessoaVO();
                        pessoaVO.setCodigo(rs.getInt("pessoa_codigo"));
                        pessoaVO.setNome(rs.getString("cliente_nome"));
                        pessoaVO.setTelefones(rs.getString("cliente_celular"));
                        pessoaVO.setEmails(rs.getString("cliente_emails"));

                        ClienteVO clienteVO = new ClienteVO();
                        clienteVO.setCodigo(rs.getInt("cliente_codigo"));
                        clienteVO.setMatricula(rs.getString("cliente_matricula"));
                        clienteVO.setPessoa(pessoaVO);

                        clienteVO.getUsuarioMovelVO().setCodigo(rs.getInt("usuariomovel_codigo"));
                        clienteVO.getUsuarioMovelVO().setNome(rs.getString("usuariomovel_nome"));
                        clienteVO.getUsuarioMovelVO().setCliente(clienteVO);

                        realizarEnvio(tipoEnvioPactoPayEnum, clienteVO, transacaoVO,
                                configVO.isComunicadoResultadoCobrancaEmail(), configVO.isComunicadoResultadoCobrancaSMS(),
                                pode_enviar_email, pode_enviar_sms,
                                false, false,
                                configVO.isComunicadoResultadoCobrancaWhatsApp(), pode_enviar_whatsapp,
                                configVO.isComunicadoResultadoCobrancaApp(), pode_enviar_app,
                                configVO.isComunicadoResultadoCobrancaGymbotPro(), pode_enviar_gymbotpro,
                                enviarSemJenkins);
                    }
                }
            }
        } catch (Exception ex) {
//            ex.printStackTrace();
            Uteis.logarDebug("PactoPayResultadoCobrancaService | processar: " + ex.getMessage());
            if (exception) {
                throw ex;
            }
        } finally {
            pactoPayConfigDAO = null;
        }
    }

    public void realizarEnvioExemplo(Integer empresa, String celular,
                                     String email, String modeloEnvio, String meioEnvio) throws Exception {
        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(this.getCon());

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setNome("CLIENTE EXEMPLO");
            pessoaVO.setTelefones(celular);
            pessoaVO.setEmails(email);

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setMatricula("000000");
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setEmpresa(empresaVO);
            clienteVO.getUsuarioMovelVO().setNome(email);

            boolean todosModelos = modeloEnvio.equalsIgnoreCase("todas");
            boolean modeloAprovada = modeloEnvio.equalsIgnoreCase("aprovada");
            boolean modeloNegada = modeloEnvio.equalsIgnoreCase("negada");
            boolean modeloCancelada = modeloEnvio.equalsIgnoreCase("cancelada");


            JSONObject objResposta = new JSONObject();
            JSONObject payment = new JSONObject();
            payment.put("ProofOfSale", "NSU12345"); //nsu teste
            payment.put("AuthorizationCode", "AUT12345"); //nsu teste
            JSONObject creditCard = new JSONObject();
            creditCard.put("CardNumber", "9999999999999999");
            payment.put("CreditCard", creditCard);
            objResposta.put("Payment", payment);

            TransacaoVO transacaoVO = new TransacaoCieloVO();
            transacaoVO.setTipo(TipoTransacaoEnum.CIELO_ONLINE);
            transacaoVO.setParamsEnvio(objResposta.toString());
            transacaoVO.setParamsResposta(objResposta.toString());
            transacaoVO.setCodigo(999999999);
            transacaoVO.setEmpresaVO(empresaVO);
            transacaoVO.setEmpresa(empresaVO.getCodigo());
            transacaoVO.setValor(10.00);
            transacaoVO.setDataProcessamento(Calendario.hoje());
            transacaoVO.setDataHoraCancelamento(Calendario.hoje());
            transacaoVO.setCodigoRetorno(CieloECommerceRetornoEnum.Status04.getId());
            transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.cartaoMascarado, APF.getCartaoMascarado("9999999999999999"));
            transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.codigoRetornoPacto, CieloECommerceRetornoEnum.Status04.getId());
            transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.erroGenericoTransacao, CieloECommerceRetornoEnum.Status04.getDescricao());

            boolean pode_enviar_email = !UteisValidacao.emptyString(email);
            boolean pode_enviar_sms = !UteisValidacao.emptyString(celular);
            boolean pode_enviar_whatsapp = !UteisValidacao.emptyString(celular);
            boolean pode_enviar_app = !UteisValidacao.emptyString(email);
            boolean pode_enviar_gymbotpro = !UteisValidacao.emptyString(celular);

            if (!meioEnvio.equalsIgnoreCase("TODOS")) {
                pode_enviar_email = false;
                pode_enviar_sms = false;
                pode_enviar_whatsapp = false;
                pode_enviar_app = false;
                pode_enviar_gymbotpro = false;

                if (meioEnvio.equalsIgnoreCase("EMAIL")) {
                    pode_enviar_email = true;
                } else if (meioEnvio.equalsIgnoreCase("SMS")) {
                    pode_enviar_sms = true;
                } else if (meioEnvio.equalsIgnoreCase("WHATSAPP")) {
                    pode_enviar_whatsapp = true;
                } else if (meioEnvio.equalsIgnoreCase("APP")) {
                    pode_enviar_app = true;
                } else if (meioEnvio.equalsIgnoreCase("GYMBOTPRO")) {
                    pode_enviar_gymbotpro = true;
                }
            }

            if (!pode_enviar_email && !pode_enviar_sms &&
                    !pode_enviar_whatsapp && !pode_enviar_app) {
                throw new Exception("Nenhum meio de envio informado");
            }

            if (todosModelos || modeloAprovada) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                realizarEnvio(TipoEnvioPactoPayEnum.RESULTADO_COBRANCA, clienteVO, transacaoVO, true, true,
                        pode_enviar_email, pode_enviar_sms, true, true,
                        true, pode_enviar_whatsapp,
                        true, pode_enviar_app,
                        true, pode_enviar_gymbotpro,
                        true);
            }

            if (todosModelos || modeloNegada) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
                transacaoVO.setOrigem(OrigemCobrancaEnum.ZW_AUTOMATICO);
                realizarEnvio(TipoEnvioPactoPayEnum.RESULTADO_COBRANCA, clienteVO, transacaoVO, true, true,
                        pode_enviar_email, pode_enviar_sms, true, true,
                        true, pode_enviar_whatsapp,
                        true, pode_enviar_app,
                        true, pode_enviar_gymbotpro,
                        true);
            }

            if (todosModelos || modeloCancelada) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                realizarEnvio(TipoEnvioPactoPayEnum.RESULTADO_COBRANCA, clienteVO, transacaoVO, true, true,
                        pode_enviar_email, pode_enviar_sms, true, true,
                        true, pode_enviar_whatsapp,
                        true, pode_enviar_app,
                        true, pode_enviar_gymbotpro,
                        true);
            }
        } finally {
            empresaDAO = null;
        }
    }

    private void realizarEnvio(TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum,
                               ClienteVO clienteVO, TransacaoVO transacaoVO,
                               boolean comunicadoEmailAtiva, boolean comunicadoSMSAtiva,
                               boolean pode_enviar_email, boolean pode_enviar_sms,
                               boolean comException, boolean exemplo,
                               boolean comunicadoWhatsAppAtiva, boolean pode_enviar_whatsapp,
                               boolean comunicadoAppAtiva, boolean pode_enviar_app,
                               boolean comunicadoGymbotProAtiva, boolean pode_enviar_gymbotpro,
                               boolean enviarSemJenkins) throws Exception {

        Empresa empresaDAO;
        PactoPayConfig pactoPayConfigDAO;
        PactoPayComunicacao pactoPayComunicacaoDAO;
        try {
            empresaDAO = new Empresa(this.con);
            pactoPayConfigDAO = new PactoPayConfig(this.con);
            pactoPayComunicacaoDAO = new PactoPayComunicacao(this.con);

            String key = DAO.resolveKeyFromConnection(this.getCon());
            PactoPayConfigVO configVO = pactoPayConfigDAO.consultarPorEmpresa(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(configVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            boolean configuracaoEmailValida = empresaTemEmail(key, empresaVO, configVO);
            boolean configuracaoSMSValida = empresaTemSMS(key, empresaVO);
            boolean configuracaoWhatsAppValida = empresaTemGymBot(key, empresaVO);
            boolean configuracaoAppValida = empresaTemAplicativo(key, empresaVO);
            boolean configuracaoGymbotProValida = empresaTemGymBotPro(key, empresaVO);

            String assunto = "";
            OrigemCobrancaEnum origemCobrancaEnum = null;
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                assunto = processarAssunto(EMAIL_ASSUNTO_RESULTADO_COBRANCA_APROVADA, clienteVO);
                origemCobrancaEnum = OrigemCobrancaEnum.REGUA_COBRANCA_COBRANCA_APROVADA;

            } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
                assunto = processarAssunto(EMAIL_ASSUNTO_RESULTADO_COBRANCA_CANCELADA, clienteVO);
                origemCobrancaEnum = OrigemCobrancaEnum.REGUA_COBRANCA_COBRANCA_CANCELADA;

            } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                assunto = processarAssunto(EMAIL_ASSUNTO_RESULTADO_COBRANCA_NEGADA, clienteVO);
                origemCobrancaEnum = OrigemCobrancaEnum.REGUA_COBRANCA_COBRANCA_NEGADA;
            }

            StringBuilder obsHistoricoContato = new StringBuilder();
            obsHistoricoContato.append("<p>Título: ").append(assunto).append(" </p>");
            obsHistoricoContato.append("<p>Remente: RÉGUA DE COBRANÇA - ").append(tipoEnvioPactoPayEnum.getIdentificador()).append(" </p>");

            //realizar envio de email
            if (configuracaoEmailValida &&
                    comunicadoEmailAtiva &&
                    pode_enviar_email &&
                    !UteisValidacao.emptyString(clienteVO.getPessoa().getEmails())) {
                PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(clienteVO.getPessoa(), empresaVO, MeioEnvio.EMAIL);
                try {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                    Set<String> listaEmails = new HashSet<>();
                    for (String email : clienteVO.getPessoa().getEmails().split("###")) {
                        if (UteisValidacao.validaEmail(email)) {
                            listaEmails.add(email);
                        }
                    }

                    if (UteisValidacao.emptyList(listaEmails)) {
                        throw new Exception("Nenhum email válido para realizar envio");
                    }

                    String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + transacaoVO.getCodigo());
                    StringBuilder html = gerarCorpoEmail(comunicacaoVO, empresaVO, clienteVO, transacaoVO, origemCobrancaEnum, configVO);

                    comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, html.toString(), clienteVO,
                            tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), transacaoVO.getCodigo(), listaEmails, exemplo, enviarSemJenkins, configVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    comunicacaoVO.erro(ex);
                    if (comException) {
                        throw ex;
                    }
                } finally {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                }
            }

            //realizar envio de sms
            if (configuracaoSMSValida &&
                    comunicadoSMSAtiva &&
                    pode_enviar_sms &&
                    !UteisValidacao.emptyString(clienteVO.getPessoa().getTelefonesString())) {
                PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(clienteVO.getPessoa(), empresaVO, MeioEnvio.SMS);
                try {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                    Set<String> listaCelular = new HashSet<>();
                    for (String celular : clienteVO.getPessoa().getTelefonesString().split("###")) {
                        if (Uteis.validarTelefoneCelular(celular)) {
                            listaCelular.add(celular);
                        }
                    }

                    if (UteisValidacao.emptyList(listaCelular)) {
                        throw new Exception("Nenhum número de celular válido");
                    }

                    String mensagem = "";
                    if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                        mensagem = obterMensagemEnviarSMS(
                                SMS_RESULTADO_COBRANCA_APROVADA,
                                SMS_RESULTADO_COBRANCA_APROVADA_COM_TAGS,
                                empresaVO, clienteVO, origemCobrancaEnum, false, transacaoVO,
                                null, null, comunicacaoVO, configVO);

                    } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
                        mensagem = obterMensagemEnviarSMS(
                                SMS_RESULTADO_COBRANCA_CANCELADA,
                                SMS_RESULTADO_COBRANCA_CANCELADA_COM_TAGS,
                                empresaVO, clienteVO, origemCobrancaEnum, false, transacaoVO,
                                null, null, comunicacaoVO, configVO);
                    } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                        mensagem = obterMensagemEnviarSMS(
                                SMS_RESULTADO_COBRANCA_NEGADA,
                                SMS_RESULTADO_COBRANCA_NEGADA_COM_TAGS,
                                empresaVO, clienteVO, origemCobrancaEnum, true, transacaoVO,
                                null, null, comunicacaoVO, configVO);
                    }

                    String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + transacaoVO.getCodigo());
                    comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, mensagem, clienteVO,
                            tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), transacaoVO.getCodigo(), listaCelular, exemplo, enviarSemJenkins, configVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    comunicacaoVO.erro(ex);
                    if (comException) {
                        throw ex;
                    }
                } finally {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                }
            }


            //realizar envio de Whatsapp
            if (configuracaoWhatsAppValida &&
                    comunicadoWhatsAppAtiva &&
                    pode_enviar_whatsapp &&
                    !UteisValidacao.emptyString(clienteVO.getPessoa().getTelefonesString())) {
                PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(clienteVO.getPessoa(), empresaVO, MeioEnvio.GYMBOT);
                try {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                    Set<String> listaCelular = new HashSet<>();
                    for (String celular : clienteVO.getPessoa().getTelefonesString().split("###")) {
                        if (Uteis.validarTelefoneCelular(celular)) {
                            listaCelular.add(celular);
                        }
                    }

                    if (UteisValidacao.emptyList(listaCelular)) {
                        throw new Exception("Nenhum número de celular válido");
                    }

                    String mensagem = "";
                    if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                        mensagem = WHATSAPP_RESULTADO_COBRANCA_APROVADA;
                    } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
                        mensagem = WHATSAPP_RESULTADO_COBRANCA_CANCELADA;
                    } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                        mensagem = WHATSAPP_RESULTADO_COBRANCA_NEGADA;
                    }

                    String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + transacaoVO.getCodigo());
                    comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, mensagem, clienteVO,
                            tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), transacaoVO.getCodigo(), listaCelular, exemplo, enviarSemJenkins, configVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    comunicacaoVO.erro(ex);
                    if (comException) {
                        throw ex;
                    }
                } finally {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                }
            }


            //realizar envio de notificação APP
            if (configuracaoAppValida &&
                    comunicadoAppAtiva &&
                    pode_enviar_app &&
                    !UteisValidacao.emptyString(clienteVO.getUsuarioMovelVO().getNome())) {
                PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(clienteVO.getPessoa(), empresaVO, MeioEnvio.APP);
                try {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                    if (!UteisValidacao.validaEmail(clienteVO.getUsuarioMovelVO().getNome())) {
                        throw new Exception("Email válido para realizar envio APP");
                    }
                    Set<String> listaEmails = new HashSet<>();
                    listaEmails.add(clienteVO.getUsuarioMovelVO().getNome());

                    String mensagem = "";
                    if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                        mensagem = APP_RESULTADO_COBRANCA_APROVADA;
                    } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
                        mensagem = APP_RESULTADO_COBRANCA_CANCELADA;
                    } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                        mensagem = APP_RESULTADO_COBRANCA_NEGADA;
                    }

                    String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + transacaoVO.getCodigo());
                    comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, mensagem, clienteVO,
                            tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), transacaoVO.getCodigo(), listaEmails, exemplo, enviarSemJenkins, configVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    comunicacaoVO.erro(ex);
                    if (comException) {
                        throw ex;
                    }
                } finally {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                }
            }


            //realizar envio de GymBotPro
            if (configuracaoGymbotProValida &&
                    comunicadoGymbotProAtiva &&
                    pode_enviar_gymbotpro &&
                    !UteisValidacao.emptyString(clienteVO.getPessoa().getTelefonesString())) {
                PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(clienteVO.getPessoa(), empresaVO, MeioEnvio.GYMBOT_PRO);
                try {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                    Set<String> listaCelular = new HashSet<>();
                    for (String celular : clienteVO.getPessoa().getTelefonesString().split("###")) {
                        if (Uteis.validarTelefoneCelular(celular)) {
                            listaCelular.add(celular);
                        }
                    }

                    if (UteisValidacao.emptyList(listaCelular)) {
                        throw new Exception("Nenhum número de celular válido");
                    }

                    String mensagem = "";
                    if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                        mensagem = WHATSAPP_RESULTADO_COBRANCA_APROVADA;
                    } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
                        mensagem = WHATSAPP_RESULTADO_COBRANCA_CANCELADA;
                    } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                        mensagem = WHATSAPP_RESULTADO_COBRANCA_NEGADA;
                    }

                    String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + transacaoVO.getCodigo());
                    comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, mensagem, clienteVO,
                            tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), transacaoVO.getCodigo(), listaCelular, exemplo, enviarSemJenkins, configVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    comunicacaoVO.erro(ex);
                    if (comException) {
                        throw ex;
                    }
                } finally {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                }
            }

        } finally {
            empresaDAO = null;
            pactoPayConfigDAO = null;
        }
    }

    private StringBuilder gerarCorpoEmail(PactoPayComunicacaoVO comunicacaoVO,
                                          EmpresaVO empresaVO, ClienteVO clienteVO,
                                          TransacaoVO transacaoVO,
                                          OrigemCobrancaEnum origemCobrancaEnum,
                                          PactoPayConfigVO configVO) throws Exception {
        File arquivoModelo = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_resultado_cobranca_aprovada.txt").toURI());
        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
            arquivoModelo = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_resultado_cobranca_cancelada.txt").toURI());
        } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
            arquivoModelo = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_resultado_cobranca_negada.txt").toURI());
        }

        StringBuilder html = FileUtilities.readContentFile(arquivoModelo.getAbsolutePath());
        return replaceInformacoes(arquivoModelo, html, empresaVO, clienteVO, null, origemCobrancaEnum, null, false, null, transacaoVO, comunicacaoVO, configVO);
    }
}

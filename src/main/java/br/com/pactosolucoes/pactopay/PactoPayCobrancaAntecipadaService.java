package br.com.pactosolucoes.pactopay;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.enumeradores.TipoContatoCRM;
import br.com.pactosolucoes.integracao.pactopay.front.FiltroPactoPayDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayCobrancaAntecipadaDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PactoPayCobrancaAntecipadaVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayConfigVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TagReguaCobrancaPactoPayEnum;
import negocio.comuns.financeiro.enumerador.TipoEnvioPactoPayEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.PactoPayCobrancaAntecipada;
import negocio.facade.jdbc.basico.PactoPayComunicacao;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.MovParcela;

import java.io.File;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class PactoPayCobrancaAntecipadaService extends PactoPaySuperService {

    public PactoPayCobrancaAntecipadaService(Connection conexao) throws Exception {
        super(conexao);
    }

    public void processar(PactoPayConfigVO configVO, Integer codigoPessoa, boolean enviarSemJenkins) throws Exception {
        Pessoa pessoaDAO;
        Empresa empresaDAO;
        AutorizacaoCobrancaCliente autoCliDAO;
        try {
            pessoaDAO = new Pessoa(this.con);
            empresaDAO = new Empresa(this.con);
            autoCliDAO = new AutorizacaoCobrancaCliente(this.con);

            if (!configVO.isCobrancaAntecipadaAtivo()) {
                throw new Exception("Cobrança antecipada não está ativa");
            }

            String key = DAO.resolveKeyFromConnection(this.getCon());
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(configVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            boolean configuracaoEmailValida = empresaTemEmail(key, empresaVO, configVO);
            boolean configuracaoSMSValida = empresaTemSMS(key, empresaVO);
            boolean configuracaoGymBotValida = empresaTemGymBot(key, empresaVO);
            boolean configuracaoAppValida = empresaTemAplicativo(key, empresaVO);
            boolean configuracaoGymBotProValida = empresaTemGymBotPro(key, empresaVO);

            TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum = TipoEnvioPactoPayEnum.COBRANCA_ANTECIPADA;

            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO();
            filtroDTO.getEmpresas().add(empresaVO.getCodigo());

            Date dataVencimento = Calendario.somarDias(Calendario.hoje(), configVO.getCobrancaAntecipadaDiasAnteriores());
            Map<Integer, List<MovParcelaVO>> mapa = obterMapaParcelas(dataVencimento, filtroDTO, codigoPessoa);

            //data que o link será valido e a data que será o vencimento
            Date dataLimitePagamento;
            if (configVO.isCobrancaAntecipadaAplicarDesconto()) {
                dataLimitePagamento = Calendario.somarDias(dataVencimento, -configVO.getCobrancaAntecipadaDiasLimitePagamento());
            } else {
                //se não tem desconto então pode pagar até o vencimento
                dataLimitePagamento = dataVencimento;
            }

            if (!UteisValidacao.emptyNumber(codigoPessoa) && mapa.isEmpty()) {
                throw new Exception("Nenhuma parcela encontrada para cobrança antecipada");
            }

            List<PactoPayComunicacaoVO> comunicacoesCriadasEmail = new ArrayList<>();
            for (Integer codPessoa : mapa.keySet()) {
                try {

                    if (configVO.isCobrancaAntecipadaComAutorizacao() &&
                            !autoCliDAO.existeAutorizacaoCobranca(null, codPessoa, null)) {
                        //enviar somente para aluno que tem autorização de cobrança
                        continue;
                    }

                    PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(codPessoa, Uteis.NIVELMONTARDADOS_TELACONSULTA);
                    List<MovParcelaVO> movParcelasCobrar = mapa.get(pessoaVO.getCodigo());

                    if (configuracaoEmailValida && configVO.isCobrancaAntecipadaEmail()) {
                        PactoPayComunicacaoVO comunicacaoVO = criarComunicacaoEmail(key, pessoaVO, empresaVO, tipoEnvioPactoPayEnum, movParcelasCobrar, dataLimitePagamento, configVO, enviarSemJenkins);
                        comunicacoesCriadasEmail.add(comunicacaoVO);
                    }
                    if (configuracaoGymBotValida && configVO.isCobrancaAntecipadaWhatsApp()) {
                        criarComunicacaoGymBot(key, pessoaVO, empresaVO, tipoEnvioPactoPayEnum, movParcelasCobrar, dataLimitePagamento, configVO, enviarSemJenkins);
                    }
                    if (configuracaoAppValida && configVO.isCobrancaAntecipadaApp()) {
                        criarComunicacaoApp(key, pessoaVO, empresaVO, tipoEnvioPactoPayEnum, movParcelasCobrar, dataLimitePagamento, configVO, enviarSemJenkins);
                    }
                    if (configuracaoSMSValida && configVO.isCobrancaAntecipadaSMS()) {
                        //todo terminar de validar
//                        criarComunicacaoSMS(key, pessoaVO, empresaVO, tipoEnvioPactoPayEnum, movParcelasCobrar, dataLimitePagamento, configVO, enviarSemJenkins);
                    }
                    if (configuracaoGymBotProValida && configVO.isCobrancaAntecipadaGymbotPro()) {
                        criarComunicacaoGymBotPro(key, pessoaVO, empresaVO, tipoEnvioPactoPayEnum, movParcelasCobrar, dataLimitePagamento, configVO, enviarSemJenkins);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                        throw ex;
                    }
                }
            }

            iniciarComunicadoEnvioEmMassaEmail(key, comunicacoesCriadasEmail, enviarSemJenkins);
        } catch (Exception ex) {
            ex.printStackTrace();
            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                throw ex;
            }
        } finally {
            pessoaDAO = null;
            empresaDAO = null;
            autoCliDAO = null;
        }
    }

    private PactoPayComunicacaoVO criarComunicacaoEmail(String key, PessoaVO pessoaVO, EmpresaVO empresaVO, TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum,
                                                        List<MovParcelaVO> movParcelasCobrar, Date dataLimitePagamento, PactoPayConfigVO configVO,
                                                        boolean enviarSemJenkins) {
        PactoPayCobrancaAntecipada antecipadaDAO;
        Cliente clienteDAO;
        PactoPayComunicacao pactoPayComunicacaoDAO = null;
        PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.EMAIL);
        try {
            antecipadaDAO = new PactoPayCobrancaAntecipada(this.con);
            clienteDAO = new Cliente(this.con);
            pactoPayComunicacaoDAO = new PactoPayComunicacao(this.con);

            comunicacaoVO.setListaMovParcela(movParcelasCobrar);
            pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

            //caso tenha codigopessoa é pq está forçando o envio então vamos ignorar se já teve contato
            if (UteisValidacao.emptyNumber(pessoaVO.getCodigo()) &&
                    recebeuContato(pessoaVO.getCodigo(), null, Calendario.hoje(), TipoContatoCRM.CONTATO_EMAIL, tipoEnvioPactoPayEnum)) {
                throw new Exception("Cliente já recebeu contato hoje - " + TipoContatoCRM.CONTATO_EMAIL.name());
            }

            Set<String> listaEmails = new HashSet<>();
            for (EmailVO emailVO : pessoaVO.getEmailVOs()) {
                if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                    listaEmails.add(emailVO.getEmail());
                }
            }

            if (UteisValidacao.emptyList(listaEmails)) {
                throw new Exception("Nenhum email válido para realizar envio");
            }

            PactoPayCobrancaAntecipadaDTO dto = new PactoPayCobrancaAntecipadaDTO();
            dto.setDataLimitePagamento(Calendario.getDataAplicandoFormatacao(dataLimitePagamento, "dd/MM/yyyy HH:mm:ss"));
            dto.setAplicarDesconto(configVO.isCobrancaAntecipadaAplicarDesconto());
            for (MovParcelaVO movParcelaVO : movParcelasCobrar) {
                dto.getParcelas().add(movParcelaVO.getCodigo());
            }

            PactoPayCobrancaAntecipadaVO antecipadaVO = new PactoPayCobrancaAntecipadaVO(pessoaVO, dto, comunicacaoVO);
            antecipadaDAO.incluirSemCommit(antecipadaVO);


            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            //utilizar os dados já carregados da pessoa
            clienteVO.setPessoa(pessoaVO);

            String assunto = processarAssunto(EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_SEM_DESCONTO, clienteVO);
            if (configVO.isCobrancaAntecipadaAplicarDesconto()) {
                assunto = processarAssunto(EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_COM_DESCONTO, clienteVO);
            }

            StringBuilder obsHistoricoContato = new StringBuilder();
            obsHistoricoContato.append("<p>Título: ").append(assunto).append(" </p>");
            obsHistoricoContato.append("<p>Remetente: RÉGUA DE COBRANÇA - ").append(tipoEnvioPactoPayEnum.getIdentificador()).append(" </p>");

            String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + antecipadaVO.getCodigo());
            StringBuilder html = gerarCorpoEmail(comunicacaoVO, configVO, empresaVO, clienteVO, antecipadaVO, dataLimitePagamento);

            comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, html.toString(), clienteVO,
                    tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), antecipadaVO.getCodigo(), listaEmails, false, enviarSemJenkins, configVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            comunicacaoVO.erro(ex);
        } finally {
            if (pactoPayComunicacaoDAO != null) {
                try {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            antecipadaDAO = null;
        }
        return comunicacaoVO;
    }

    private void criarComunicacaoGymBot(String key, PessoaVO pessoaVO, EmpresaVO empresaVO,
                                        TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum,
                                        List<MovParcelaVO> movParcelasCobrar,
                                        Date dataLimitePagamento,
                                        PactoPayConfigVO configVO,
                                        boolean enviarSemJenkins) {
        PactoPayCobrancaAntecipada antecipadaDAO;
        Cliente clienteDAO;
        PactoPayComunicacao pactoPayComunicacaoDAO = null;
        PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.GYMBOT);
        try {
            antecipadaDAO = new PactoPayCobrancaAntecipada(this.con);
            clienteDAO = new Cliente(this.con);
            pactoPayComunicacaoDAO = new PactoPayComunicacao(this.con);

            comunicacaoVO.setListaMovParcela(movParcelasCobrar);
            pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

            //caso tenha codigopessoa é pq está forçando o envio então vamos ignorar se já teve contato
            if (UteisValidacao.emptyNumber(pessoaVO.getCodigo()) &&
                    recebeuContato(pessoaVO.getCodigo(), null, Calendario.hoje(), TipoContatoCRM.CONTATO_GYMBOT, tipoEnvioPactoPayEnum)) {
                throw new Exception("Cliente já recebeu contato hoje - " + TipoContatoCRM.CONTATO_GYMBOT.name());
            }

            Set<String> listaEmails = new HashSet<>();
            for (EmailVO emailVO : pessoaVO.getEmailVOs()) {
                if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                    listaEmails.add(emailVO.getEmail());
                }
            }

            if (UteisValidacao.emptyList(listaEmails)) {
                throw new Exception("Nenhum email válido para realizar envio");
            }

            PactoPayCobrancaAntecipadaDTO dto = new PactoPayCobrancaAntecipadaDTO();
            dto.setDataLimitePagamento(Calendario.getDataAplicandoFormatacao(dataLimitePagamento, "dd/MM/yyyy HH:mm:ss"));
            dto.setAplicarDesconto(configVO.isCobrancaAntecipadaAplicarDesconto());
            for (MovParcelaVO movParcelaVO : movParcelasCobrar) {
                dto.getParcelas().add(movParcelaVO.getCodigo());
            }

            PactoPayCobrancaAntecipadaVO antecipadaVO = new PactoPayCobrancaAntecipadaVO(pessoaVO, dto, comunicacaoVO);
            antecipadaDAO.incluirSemCommit(antecipadaVO);

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            //utilizar os dados já carregados da pessoa
            clienteVO.setPessoa(pessoaVO);

            String assunto = processarAssunto(EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_SEM_DESCONTO, clienteVO);
            if (configVO.isCobrancaAntecipadaAplicarDesconto()) {
                assunto = processarAssunto(EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_COM_DESCONTO, clienteVO);
            }

            StringBuilder obsHistoricoContato = new StringBuilder();
            obsHistoricoContato.append("<p>Título: ").append(assunto).append(" </p>");
            obsHistoricoContato.append("<p>Remente: RÉGUA DE COBRANÇA - ").append(tipoEnvioPactoPayEnum.getIdentificador()).append(" </p>");

            String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + antecipadaVO.getCodigo());
            StringBuilder html = gerarCorpoEmail(comunicacaoVO, configVO, empresaVO, clienteVO, antecipadaVO, dataLimitePagamento);

            comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, html.toString(), clienteVO,
                    tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), antecipadaVO.getCodigo(), listaEmails, false, enviarSemJenkins, configVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            comunicacaoVO.erro(ex);
        } finally {
            if (pactoPayComunicacaoDAO != null) {
                try {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            antecipadaDAO = null;
        }
    }

    private void criarComunicacaoGymBotPro(String key, PessoaVO pessoaVO, EmpresaVO empresaVO,
                                           TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum,
                                           List<MovParcelaVO> movParcelasCobrar,
                                           Date dataLimitePagamento,
                                           PactoPayConfigVO configVO,
                                           boolean enviarSemJenkins) {
        PactoPayCobrancaAntecipada antecipadaDAO;
        Cliente clienteDAO;
        PactoPayComunicacao pactoPayComunicacaoDAO = null;
        PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.GYMBOT_PRO);
        try {
            antecipadaDAO = new PactoPayCobrancaAntecipada(this.con);
            clienteDAO = new Cliente(this.con);
            pactoPayComunicacaoDAO = new PactoPayComunicacao(this.con);

            comunicacaoVO.setListaMovParcela(movParcelasCobrar);
            pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

            //caso tenha codigopessoa é pq está forçando o envio então vamos ignorar se já teve contato
            if (UteisValidacao.emptyNumber(pessoaVO.getCodigo()) &&
                    recebeuContato(pessoaVO.getCodigo(), null, Calendario.hoje(), TipoContatoCRM.CONTATO_GYMBOT_PRO, tipoEnvioPactoPayEnum)) {
                throw new Exception("Cliente já recebeu contato hoje - " + TipoContatoCRM.CONTATO_GYMBOT_PRO.name());
            }

            Set<String> listaEmails = new HashSet<>();
            for (EmailVO emailVO : pessoaVO.getEmailVOs()) {
                if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                    listaEmails.add(emailVO.getEmail());
                }
            }

            if (UteisValidacao.emptyList(listaEmails)) {
                throw new Exception("Nenhum email válido para realizar envio");
            }

            PactoPayCobrancaAntecipadaDTO dto = new PactoPayCobrancaAntecipadaDTO();
            dto.setDataLimitePagamento(Calendario.getDataAplicandoFormatacao(dataLimitePagamento, "dd/MM/yyyy HH:mm:ss"));
            dto.setAplicarDesconto(configVO.isCobrancaAntecipadaAplicarDesconto());
            for (MovParcelaVO movParcelaVO : movParcelasCobrar) {
                dto.getParcelas().add(movParcelaVO.getCodigo());
            }

            PactoPayCobrancaAntecipadaVO antecipadaVO = new PactoPayCobrancaAntecipadaVO(pessoaVO, dto, comunicacaoVO);
            antecipadaDAO.incluirSemCommit(antecipadaVO);

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            //utilizar os dados já carregados da pessoa
            clienteVO.setPessoa(pessoaVO);

            String assunto = processarAssunto(EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_SEM_DESCONTO, clienteVO);
            if (configVO.isCobrancaAntecipadaAplicarDesconto()) {
                assunto = processarAssunto(EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_COM_DESCONTO, clienteVO);
            }

            StringBuilder obsHistoricoContato = new StringBuilder();
            obsHistoricoContato.append("<p>Título: ").append(assunto).append(" </p>");
            obsHistoricoContato.append("<p>Remente: RÉGUA DE COBRANÇA - ").append(tipoEnvioPactoPayEnum.getIdentificador()).append(" </p>");

            String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + antecipadaVO.getCodigo());
            StringBuilder html = gerarCorpoEmail(comunicacaoVO, configVO, empresaVO, clienteVO, antecipadaVO, dataLimitePagamento);

            comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, html.toString(), clienteVO,
                    tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), antecipadaVO.getCodigo(), listaEmails, false, enviarSemJenkins, configVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            comunicacaoVO.erro(ex);
        } finally {
            if (pactoPayComunicacaoDAO != null) {
                try {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            antecipadaDAO = null;
        }
    }

    private void criarComunicacaoApp(String key, PessoaVO pessoaVO, EmpresaVO empresaVO,
                                     TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum,
                                     List<MovParcelaVO> movParcelasCobrar,
                                     Date dataLimitePagamento,
                                     PactoPayConfigVO configVO,
                                     boolean enviarSemJenkins) {
        PactoPayCobrancaAntecipada antecipadaDAO;
        Cliente clienteDAO;
        PactoPayComunicacao pactoPayComunicacaoDAO = null;
        PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.APP);
        try {
            antecipadaDAO = new PactoPayCobrancaAntecipada(this.con);
            clienteDAO = new Cliente(this.con);
            pactoPayComunicacaoDAO = new PactoPayComunicacao(this.con);

            comunicacaoVO.setListaMovParcela(movParcelasCobrar);
            pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

            //caso tenha codigopessoa é pq está forçando o envio então vamos ignorar se já teve contato
            if (UteisValidacao.emptyNumber(pessoaVO.getCodigo()) &&
                    recebeuContato(pessoaVO.getCodigo(), null, Calendario.hoje(), TipoContatoCRM.CONTATO_APP, tipoEnvioPactoPayEnum)) {
                throw new Exception("Cliente já recebeu contato hoje - " + TipoContatoCRM.CONTATO_APP.name());
            }

            Set<String> listaEmails = new HashSet<>();
            for (EmailVO emailVO : pessoaVO.getEmailVOs()) {
                if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                    listaEmails.add(emailVO.getEmail());
                }
            }

            if (UteisValidacao.emptyList(listaEmails)) {
                throw new Exception("Nenhum email válido para realizar envio");
            }

            PactoPayCobrancaAntecipadaDTO dto = new PactoPayCobrancaAntecipadaDTO();
            dto.setDataLimitePagamento(Calendario.getDataAplicandoFormatacao(dataLimitePagamento, "dd/MM/yyyy HH:mm:ss"));
            dto.setAplicarDesconto(configVO.isCobrancaAntecipadaAplicarDesconto());
            for (MovParcelaVO movParcelaVO : movParcelasCobrar) {
                dto.getParcelas().add(movParcelaVO.getCodigo());
            }

            PactoPayCobrancaAntecipadaVO antecipadaVO = new PactoPayCobrancaAntecipadaVO(pessoaVO, dto, comunicacaoVO);
            antecipadaDAO.incluirSemCommit(antecipadaVO);


            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            //utilizar os dados já carregados da pessoa
            clienteVO.setPessoa(pessoaVO);

            String assunto = processarAssunto(EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_SEM_DESCONTO, clienteVO);
            if (configVO.isCobrancaAntecipadaAplicarDesconto()) {
                assunto = processarAssunto(EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_COM_DESCONTO, clienteVO);
            }

            StringBuilder obsHistoricoContato = new StringBuilder();
            obsHistoricoContato.append("<p>Título: ").append(assunto).append(" </p>");
            obsHistoricoContato.append("<p>Remente: RÉGUA DE COBRANÇA - ").append(tipoEnvioPactoPayEnum.getIdentificador()).append(" </p>");

            String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + antecipadaVO.getCodigo());
            StringBuilder html = gerarCorpoEmail(comunicacaoVO, configVO, empresaVO, clienteVO, antecipadaVO, dataLimitePagamento);

            //gerar link de pagamento
//            String link = getUrlLinkPagamento(empresaVO, clienteVO, true, OrigemCobrancaEnum.REGUA_COBRANCA_COBRANCA_ANTECIPADA, antecipadaVO, comunicacaoVO);
            //todo validar
            String link = comunicacaoVO.getTagsJSON().optString(TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());

            comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, html.toString(), clienteVO,
                    tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), antecipadaVO.getCodigo(), listaEmails, false, enviarSemJenkins, link, configVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            comunicacaoVO.erro(ex);
        } finally {
            if (pactoPayComunicacaoDAO != null) {
                try {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            antecipadaDAO = null;
        }
    }

    private void criarComunicacaoSMS(String key, PessoaVO pessoaVO, EmpresaVO empresaVO,
                                     TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum,
                                     List<MovParcelaVO> movParcelasCobrar,
                                     Date dataLimitePagamento,
                                     PactoPayConfigVO configVO,
                                     boolean enviarSemJenkins) {
        PactoPayCobrancaAntecipada antecipadaDAO;
        Cliente clienteDAO;
        PactoPayComunicacao pactoPayComunicacaoDAO = null;
        PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.SMS);
        try {
            antecipadaDAO = new PactoPayCobrancaAntecipada(this.con);
            clienteDAO = new Cliente(this.con);
            pactoPayComunicacaoDAO = new PactoPayComunicacao(this.con);

            comunicacaoVO.setListaMovParcela(movParcelasCobrar);
            pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

            //caso tenha codigopessoa é pq está forçando o envio então vamos ignorar se já teve contato
            if (UteisValidacao.emptyNumber(pessoaVO.getCodigo()) &&
                    recebeuContato(pessoaVO.getCodigo(), null, Calendario.hoje(), TipoContatoCRM.CONTATO_SMS, tipoEnvioPactoPayEnum)) {
                throw new Exception("Cliente já recebeu contato hoje - " + TipoContatoCRM.CONTATO_SMS.name());
            }

            Set<String> listaCelular = new HashSet<>();
            for (String celular : pessoaVO.getTelefonesString().split("###")) {
                if (Uteis.validarTelefoneCelular(celular)) {
                    listaCelular.add(celular);
                }
            }

            if (UteisValidacao.emptyList(listaCelular)) {
                throw new Exception("Nenhum número de celular válido");
            }

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            //utilizar os dados já carregados da pessoa
            clienteVO.setPessoa(pessoaVO);

            String assunto = processarAssunto(EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_SEM_DESCONTO, clienteVO);
            if (configVO.isCobrancaAntecipadaAplicarDesconto()) {
                assunto = processarAssunto(EMAIL_ASSUNTO_COBRANCA_ANTECIPADA_COM_DESCONTO, clienteVO);
            }

            PactoPayCobrancaAntecipadaDTO dto = new PactoPayCobrancaAntecipadaDTO();
            dto.setDataLimitePagamento(Calendario.getDataAplicandoFormatacao(dataLimitePagamento, "dd/MM/yyyy HH:mm:ss"));
            dto.setAplicarDesconto(configVO.isCobrancaAntecipadaAplicarDesconto());
            for (MovParcelaVO movParcelaVO : movParcelasCobrar) {
                dto.getParcelas().add(movParcelaVO.getCodigo());
            }

            PactoPayCobrancaAntecipadaVO antecipadaVO = new PactoPayCobrancaAntecipadaVO(pessoaVO, dto, comunicacaoVO);
            antecipadaDAO.incluirSemCommit(antecipadaVO);

            StringBuilder obsHistoricoContato = new StringBuilder();
            obsHistoricoContato.append("<p>Título: ").append(assunto).append(" </p>");
            obsHistoricoContato.append("<p>Remente: RÉGUA DE COBRANÇA - ").append(tipoEnvioPactoPayEnum.getIdentificador()).append(" </p>");

            String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + antecipadaVO.getCodigo());
            StringBuilder html = gerarCorpoEmail(comunicacaoVO, configVO, empresaVO, clienteVO, antecipadaVO, dataLimitePagamento);

            //gerar link de pagamento
//            String link = getUrlLinkPagamento(empresaVO, clienteVO, true, OrigemCobrancaEnum.REGUA_COBRANCA_COBRANCA_ANTECIPADA, antecipadaVO, comunicacaoVO);
            //todo validar
            String link = comunicacaoVO.getTagsJSON().optString(TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());

            comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, html.toString(), clienteVO,
                    tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), antecipadaVO.getCodigo(), listaCelular, false, enviarSemJenkins, link, configVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            comunicacaoVO.erro(ex);
        } finally {
            if (pactoPayComunicacaoDAO != null) {
                try {
                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            antecipadaDAO = null;
        }
    }

    private Map<Integer, List<MovParcelaVO>> obterMapaParcelas(Date dataVencimento, FiltroPactoPayDTO filtroDTO, Integer codigoPessoa) throws Exception {
        MovParcela movParcelaDAO;
        try {
            movParcelaDAO = new MovParcela(this.con);

            List<MovParcelaVO> listaGeral = movParcelaDAO.consultarParcelasEmAbertoPactoPay(null, null,
                    dataVencimento, dataVencimento, null, filtroDTO, codigoPessoa);

            Map<Integer, List<MovParcelaVO>> mapa = new HashMap<>();
            for (MovParcelaVO movParcelaVO : listaGeral) {
                if (movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO)) {
                    continue;
                }

                List<MovParcelaVO> listaParcelas = mapa.get(movParcelaVO.getPessoa().getCodigo());
                if (listaParcelas == null) {
                    listaParcelas = new ArrayList<>();
                }

                listaParcelas.add(movParcelaVO);
                mapa.put(movParcelaVO.getPessoa().getCodigo(), listaParcelas);
            }
            return mapa;
        } finally {
            movParcelaDAO = null;
        }
    }

    private StringBuilder gerarCorpoEmail(PactoPayComunicacaoVO comunicacaoVO, PactoPayConfigVO configVO,
                                          EmpresaVO empresaVO, ClienteVO clienteVO,
                                          PactoPayCobrancaAntecipadaVO cobrancaAntecipadaVO,
                                          Date limitePagamento) throws Exception {

        String parcelas = "";
        for (MovParcelaVO movParcelaVO : comunicacaoVO.getListaMovParcela()) {
            parcelas += (", " + movParcelaVO.getDescricao());
        }
        parcelas = parcelas.replaceFirst(", ", "");

        File arquivoModelo = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_cobranca_antecipada.txt").toURI());
        StringBuilder mensagem = new StringBuilder();

        if (configVO.isCobrancaAntecipadaAplicarDesconto()) {
            mensagem.append("Olá, <b>").append(Uteis.getPrimeiroNome(clienteVO.getPessoa().getNome())).append("</b>. ");
            mensagem.append("Temos uma ótima notícia para você!<br/><br/> ");
            mensagem.append("O pagamento da(s) parcela(s): <b>").append(parcelas).append("</b> ");
            mensagem.append("pode ser antecipado e você garante um desconto especial se realizá-lo ");
            mensagem.append("até ").append(Calendario.getDataAplicandoFormatacao(limitePagamento, "dd/MM/yyyy")).append(".");
        } else {
            mensagem.append("Olá, ").append(Uteis.getPrimeiroNome(clienteVO.getPessoa().getNome())).append(". ");
            mensagem.append("Temos uma ótima notícia para você!<br/><br/> ");
            mensagem.append("O pagamento da(s) parcela(s): <b>").append(parcelas).append("</b> ");
            mensagem.append("pode ser antecipado, garantindo assim mais facilidade de planejamento para você.");
        }

        StringBuilder html = FileUtilities.readContentFile(arquivoModelo.getAbsolutePath());
        return replaceInformacoes(arquivoModelo, html, empresaVO, clienteVO, mensagem, OrigemCobrancaEnum.REGUA_COBRANCA_COBRANCA_ANTECIPADA, cobrancaAntecipadaVO, true, null, null, comunicacaoVO, configVO);
    }
}

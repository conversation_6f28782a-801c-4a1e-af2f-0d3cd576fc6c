package br.com.pactosolucoes.pactopay;

import org.json.JSONObject;

public class PactoPayEnvioEmailDadosDTO {

    private String chaveJobJenkins;
    private boolean envioSemJenkins = false;

    public PactoPayEnvioEmailDadosDTO() {

    }

    public PactoPayEnvioEmailDadosDTO(JSONObject json) {
        this.chaveJobJenkins = json.optString("chaveJobJenkins");
        this.envioSemJenkins = json.optBoolean("envioSemJenkins");
    }

    public String getChaveJobJenkins() {
        if (chaveJobJenkins == null) {
            chaveJobJenkins = "";
        }
        return chaveJobJenkins;
    }

    public void setChaveJobJenkins(String chaveJobJenkins) {
        this.chaveJobJenkins = chaveJobJenkins;
    }

    public String toString() {
        return new JSONObject(this).toString();
    }

    public boolean isEnvioSemJenkins() {
        return envioSemJenkins;
    }

    public void setEnvioSemJenkins(boolean envioSemJenkins) {
        this.envioSemJenkins = envioSemJenkins;
    }
}

package br.com.pactosolucoes.pactopay;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.enumeradores.TipoContatoCRM;
import br.com.pactosolucoes.integracao.pactopay.front.EnvioComunicacaoDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayConfigVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TagReguaCobrancaPactoPayEnum;
import negocio.comuns.financeiro.enumerador.TipoEnvioPactoPayEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.PactoPayComunicacao;
import negocio.facade.jdbc.financeiro.MovParcela;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class PactoPayCobrancaPendenteService extends PactoPaySuperService {

    public PactoPayCobrancaPendenteService(Connection conexao) throws Exception {
        super(conexao);
    }

    public void processar(PactoPayConfigVO configVO, Integer codigoPessoa, boolean enviarSemJenkins) throws Exception {
        Empresa empresaDAO;
        MovParcela movParcelaDAO;
        AutorizacaoCobrancaCliente autoCliDAO;
        PactoPayComunicacao pactoPayComunicacaoDAO;
        try {
            empresaDAO = new Empresa(this.con);
            movParcelaDAO = new MovParcela(this.con);
            autoCliDAO = new AutorizacaoCobrancaCliente(this.con);
            pactoPayComunicacaoDAO = new PactoPayComunicacao(this.con);

            if (!configVO.isComunicadoAtrasoAtivo()) {
                throw new Exception("Comunicado de atraso não está ativo");
            }

            String key = DAO.resolveKeyFromConnection(this.getCon());
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(configVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            boolean configuracaoEmailValida = empresaTemEmail(key, empresaVO, configVO);
            boolean configuracaoSMSValida = empresaTemSMS(key, empresaVO);
            boolean configuracaoGymBotValida = empresaTemGymBot(key, empresaVO);
            boolean configuracaoAppValida = empresaTemAplicativo(key, empresaVO);
            boolean configuracaoGymBotProValida = empresaTemGymBotPro(key, empresaVO);
            TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum = TipoEnvioPactoPayEnum.PARCELA_PENDENTE;

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT \n");
            sql.append("cl.codigo as cliente_codigo, \n");
            sql.append("cl.matricula as cliente_matricula, \n");
            sql.append("p.nome as cliente_nome, \n");
            sql.append("p.codigo as pessoa_codigo, \n");
            sql.append("array_to_string(array(SELECT numero FROM telefone WHERE pessoa = cl.pessoa and tipotelefone = 'CE'), '###', '') as cliente_celular, \n");
            sql.append("array_to_string(array(SELECT email FROM email WHERE bloqueadobounce IS FALSE and pessoa = cl.pessoa), '###', '') as cliente_emails, \n");
            sql.append("mp.codigo as parcela_codigo, \n");
            sql.append("mp.descricao as parcela_descricao, \n");
            sql.append("mp.valorparcela as parcela_valor, \n");
            sql.append("mp.datavencimento as parcela_datavencimento, \n");

            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_EMAIL.getSigla()).append("' \n");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("' ");
            sql.append("AND dia::date >= '").append(Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), -configVO.getComunicadoAtrasoIntervaloDias()))).append("') as pode_enviar_email, \n");
            sql.append("(select count(codigo) from historicocontato where cliente = cl.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_EMAIL.getSigla()).append("' \n");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("') as qtd_total_email, \n");

            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_SMS.getSigla()).append("' \n");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("' ");
            sql.append("AND dia::date >= '").append(Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), -configVO.getComunicadoAtrasoIntervaloDias()))).append("') as pode_enviar_sms, \n");
            sql.append("(select count(codigo) from historicocontato where cliente = cl.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_SMS.getSigla()).append("' \n");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("') as qtd_total_sms, \n");

            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_GYMBOT.getSigla()).append("' \n");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("' ");
            sql.append("AND dia::date >= '").append(Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), -configVO.getComunicadoAtrasoIntervaloDias()))).append("') as pode_enviar_gymbot, \n");
            sql.append("(select count(codigo) from historicocontato where cliente = cl.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_GYMBOT.getSigla()).append("' \n");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("') as qtd_total_gymbot, \n");

            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_APP.getSigla()).append("' \n");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("' ");
            sql.append("AND dia::date >= '").append(Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), -configVO.getComunicadoAtrasoIntervaloDias()))).append("') as pode_enviar_app, \n");
            sql.append("(select count(codigo) from historicocontato where cliente = cl.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_APP.getSigla()).append("' \n");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("') as qtd_total_app, \n");

            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_GYMBOT_PRO.getSigla()).append("' \n");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("' ");
            sql.append("AND dia::date >= '").append(Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), -configVO.getComunicadoAtrasoIntervaloDias()))).append("') as pode_enviar_gymbotpro, \n");
            sql.append("(select count(codigo) from historicocontato where cliente = cl.codigo and tipocontato = '").append(TipoContatoCRM.CONTATO_GYMBOT_PRO.getSigla()).append("' \n");
            sql.append("and origem = '").append(tipoEnvioPactoPayEnum.getIdentificador()).append("') as qtd_total_gymbotpro, \n");

            sql.append("usm.codigo as usuariomovel_codigo, \n");
            sql.append("usm.nome as usuariomovel_nome \n");
            sql.append("FROM movparcela mp \n");
            sql.append("INNER JOIN pessoa p ON p.codigo = mp.pessoa \n");
            sql.append("INNER JOIN cliente cl ON cl.pessoa = p.codigo \n");
            sql.append("LEFT JOIN usuariomovel usm on usm.codigo = (select max(codigo) from usuariomovel where ativo and cliente = cl.codigo) \n");
            sql.append("WHERE mp.situacao = 'EA' \n");
            sql.append("AND mp.valorparcela > 0 \n");
            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                sql.append("AND mp.pessoa = ").append(codigoPessoa).append(" \n");
            }
            sql.append("AND mp.empresa = ").append(empresaVO.getCodigo()).append(" \n");
            sql.append("AND mp.datavencimento::date between '").append(Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), -configVO.getComunicadoAtrasoDiasVencidoMaximo()))).append("' \n");
            sql.append("and '").append(Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), -configVO.getComunicadoAtrasoDiasVencidoMinimo()))).append("' \n");
            sql.append("order by mp.pessoa, mp.datavencimento \n");

            Map<Integer, EnvioComunicacaoDTO> mapa = new HashMap<>();
            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        try {
                            Integer qtd_total_sms = rs.getInt("qtd_total_sms");
                            Integer qtd_total_email = rs.getInt("qtd_total_email");
                            Integer qtd_total_gymbot = rs.getInt("qtd_total_gymbot");
                            Integer qtd_total_app = rs.getInt("qtd_total_app");
                            Integer qtd_total_gymbotpro = rs.getInt("qtd_total_gymbotpro");

                            boolean pode_enviar_sms = rs.getBoolean("pode_enviar_sms");
                            boolean pode_enviar_email = rs.getBoolean("pode_enviar_email");
                            boolean pode_enviar_gymbot = rs.getBoolean("pode_enviar_gymbot");
                            boolean pode_enviar_app = rs.getBoolean("pode_enviar_app");
                            boolean pode_enviar_gymbotpro = rs.getBoolean("pode_enviar_gymbotpro");

                            //caso tenha codigopessoa é pq está forçando o envio então vamos forçar
                            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                                qtd_total_email = 0;
                                pode_enviar_email = true;
                                qtd_total_sms = 0;
                                pode_enviar_sms = true;
                                qtd_total_gymbot = 0;
                                pode_enviar_gymbot = true;
                                qtd_total_app = 0;
                                pode_enviar_app = true;
                                qtd_total_gymbotpro = 0;
                                pode_enviar_gymbotpro = true;
                            }

                            PessoaVO pessoaVO = new PessoaVO();
                            pessoaVO.setCodigo(rs.getInt("pessoa_codigo"));
                            pessoaVO.setNome(rs.getString("cliente_nome"));
                            pessoaVO.setTelefones(rs.getString("cliente_celular"));
                            pessoaVO.setEmails(rs.getString("cliente_emails"));

                            ClienteVO clienteVO = new ClienteVO();
                            clienteVO.setCodigo(rs.getInt("cliente_codigo"));
                            clienteVO.setMatricula(rs.getString("cliente_matricula"));
                            clienteVO.setPessoa(pessoaVO);

                            clienteVO.getUsuarioMovelVO().setCodigo(rs.getInt("usuariomovel_codigo"));
                            clienteVO.getUsuarioMovelVO().setNome(rs.getString("usuariomovel_nome"));
                            clienteVO.getUsuarioMovelVO().setCliente(clienteVO);

                            MovParcelaVO movParcelaVO = new MovParcelaVO();
                            movParcelaVO.setCodigo(rs.getInt("parcela_codigo"));
                            movParcelaVO.setDescricao(rs.getString("parcela_descricao"));
                            movParcelaVO.setValorParcela(rs.getDouble("parcela_valor"));
                            movParcelaVO.setDataVencimento(rs.getDate("parcela_datavencimento"));
                            movParcelaVO.setPessoa(pessoaVO);
                            movParcelaVO.setClienteVO(clienteVO);

                            if (configVO.isComunicadoAtrasoComAutorizacao() &&
                                    !autoCliDAO.existeAutorizacaoCobranca(null, pessoaVO.getCodigo(), null)) {
                                //enviar somente para aluno que tem autorização de cobrança
                                continue;
                            }

                            if (movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO)) {
                                //parcela está bloqueada para cobrança
                                continue;
                            }

                            EnvioComunicacaoDTO envioComunicacaoDTO = mapa.get(pessoaVO.getCodigo());
                            if (envioComunicacaoDTO == null) {
                                envioComunicacaoDTO = new EnvioComunicacaoDTO();
                                envioComunicacaoDTO.setPessoaVO(pessoaVO);
                                envioComunicacaoDTO.setClienteVO(clienteVO);
                                envioComunicacaoDTO.setPodeEnviarEmail(pode_enviar_email);
                                envioComunicacaoDTO.setTotalEmailEnviado(qtd_total_email);
                                envioComunicacaoDTO.setPodeEnviarSMS(pode_enviar_sms);
                                envioComunicacaoDTO.setTotalSMSEnviado(qtd_total_sms);
                                envioComunicacaoDTO.setPodeEnviarWhatsApp(pode_enviar_gymbot);
                                envioComunicacaoDTO.setTotalWhatsAppEnviado(qtd_total_gymbot);
                                envioComunicacaoDTO.setPodeEnviarApp(pode_enviar_app);
                                envioComunicacaoDTO.setTotalAppEnviado(qtd_total_app);
                                envioComunicacaoDTO.setPodeEnviarGymBotPro(pode_enviar_gymbotpro);
                                envioComunicacaoDTO.setTotalGymBotProEnviado(qtd_total_gymbotpro);
                            }

                            envioComunicacaoDTO.getParcelas().add(movParcelaVO);
                            mapa.put(pessoaVO.getCodigo(), envioComunicacaoDTO);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                                throw ex;
                            }
                        }
                    }
                }
            }

            if (!UteisValidacao.emptyNumber(codigoPessoa) && mapa.isEmpty()) {
                throw new Exception("Nenhuma parcela pendente encontrada");
            }

            List<PactoPayComunicacaoVO> comunicacoesCriadasEmail = new ArrayList<>();
            for (Integer codPessoa : mapa.keySet()) {

                EnvioComunicacaoDTO envioComunicacaoDTO = mapa.get(codPessoa);
                ClienteVO clienteVO = envioComunicacaoDTO.getClienteVO();
                PessoaVO pessoaVO = clienteVO.getPessoa();

                String assunto = processarAssunto(EMAIL_ASSUNTO_COBRANCA_ATRASADA, clienteVO);

                StringBuilder obsHistoricoContato = new StringBuilder();
                obsHistoricoContato.append("<p>Unidade: ").append(empresaVO.getNome()).append(" </p>");
                obsHistoricoContato.append("<p>Título: ").append(assunto).append(" </p>");
                for (MovParcelaVO movParcelaVO : envioComunicacaoDTO.getParcelas()) {
                    obsHistoricoContato.append("<p>Parcela: ").append(movParcelaVO.getCodigo()).append(" - ").append(movParcelaVO.getDescricao());
                    obsHistoricoContato.append("- ").append(movParcelaVO.getValorParcela_Apresentar()).append(" - ").append(movParcelaVO.getDataVencimento_Apresentar()).append(" </p>");
                }
                obsHistoricoContato.append("<p>Remente: RÉGUA DE COBRANÇA - ").append(tipoEnvioPactoPayEnum.getIdentificador()).append(" </p>");

                String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + clienteVO.getCodigo());

                //realizar envio de email
                if (configuracaoEmailValida &&
                        configVO.isComunicadoAtrasoEmail() &&
                        envioComunicacaoDTO.isPodeEnviarEmail() &&
                        !UteisValidacao.emptyString(pessoaVO.getEmails()) &&
                        envioComunicacaoDTO.getTotalEmailEnviado() < configVO.getComunicadoAtrasoQtdMaximaEnvios()) {
                    PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.EMAIL);
                    try {
                        comunicacaoVO.getListaMovParcela().addAll(envioComunicacaoDTO.getParcelas());
                        pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                        Set<String> listaEmails = new HashSet<>();
                        for (String email : pessoaVO.getEmails().split("###")) {
                            if (UteisValidacao.validaEmail(email)) {
                                listaEmails.add(email);
                            }
                        }

                        if (UteisValidacao.emptyList(listaEmails)) {
                            throw new Exception("Nenhum email válido para realizar envio");
                        }

                        StringBuilder html = gerarCorpoEmail(comunicacaoVO, empresaVO, clienteVO, configVO);

                        comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, html.toString(), clienteVO,
                                tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), clienteVO.getCodigo(), listaEmails, false, enviarSemJenkins, configVO);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        comunicacaoVO.erro(ex);
                        if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                            throw ex;
                        }
                    } finally {
                        pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                    }
                    comunicacoesCriadasEmail.add(comunicacaoVO);
                }

                //realizar envio de sms
                if (configuracaoSMSValida &&
                        configVO.isComunicadoAtrasoSMS() &&
                        envioComunicacaoDTO.isPodeEnviarSMS() &&
                        !UteisValidacao.emptyString(pessoaVO.getTelefonesString()) &&
                        envioComunicacaoDTO.getTotalSMSEnviado() < configVO.getComunicadoAtrasoQtdMaximaEnvios()) {
                    PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.SMS);
                    try {
                        comunicacaoVO.getListaMovParcela().addAll(envioComunicacaoDTO.getParcelas());
                        pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                        Set<String> listaCelular = new HashSet<>();
                        for (String celular : pessoaVO.getTelefonesString().split("###")) {
                            if (Uteis.validarTelefoneCelular(celular)) {
                                listaCelular.add(celular);
                            }
                        }

                        if (UteisValidacao.emptyList(listaCelular)) {
                            throw new Exception("Nenhum número de celular válido");
                        }

                        String mensagemEnviar = obterMensagemEnviarSMS(
                                SMS_COBRANCA_ATRASADA,
                                SMS_COBRANCA_ATRASADA_COM_TAGS,
                                empresaVO, clienteVO,
                                OrigemCobrancaEnum.REGUA_COBRANCA_COBRANCA_ATRASO, true,
                                null, null, null, comunicacaoVO, configVO);

                        comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, mensagemEnviar, clienteVO,
                                tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), clienteVO.getCodigo(), listaCelular, false, enviarSemJenkins, configVO);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        comunicacaoVO.erro(ex);
                        if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                            throw ex;
                        }
                    } finally {
                        pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                    }
                }

                //realizar envio de GymBot
                if (configuracaoGymBotValida &&
                        configVO.isComunicadoAtrasoWhatsApp() &&
                        envioComunicacaoDTO.isPodeEnviarWhatsApp() &&
                        !UteisValidacao.emptyString(pessoaVO.getTelefonesString()) &&
                        envioComunicacaoDTO.getTotalWhatsAppEnviado() < configVO.getComunicadoAtrasoQtdMaximaEnvios()) {
                    PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.GYMBOT);
                    try {
                        comunicacaoVO.getListaMovParcela().addAll(envioComunicacaoDTO.getParcelas());
                        pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                        Set<String> listaCelular = new HashSet<>();
                        for (String celular : pessoaVO.getTelefonesString().split("###")) {
                            if (Uteis.validarTelefoneCelular(celular)) {
                                listaCelular.add(celular);
                            }
                        }

                        if (UteisValidacao.emptyList(listaCelular)) {
                            throw new Exception("Nenhum número de celular válido");
                        }

                        comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, WHATSAPP_COBRANCA_ATRASADA, clienteVO,
                                tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), clienteVO.getCodigo(), listaCelular, false, enviarSemJenkins, configVO);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        comunicacaoVO.erro(ex);
                        if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                            throw ex;
                        }
                    } finally {
                        pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                    }
                }

                //realizar envio de App
                if (configuracaoAppValida &&
                        configVO.isComunicadoAtrasoApp() &&
                        envioComunicacaoDTO.isPodeEnviarApp() &&
                        !UteisValidacao.emptyString(clienteVO.getUsuarioMovelVO().getNome()) &&
                        envioComunicacaoDTO.getTotalAppEnviado() < configVO.getComunicadoAtrasoQtdMaximaEnvios()) {
                    PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.APP);
                    try {
                        comunicacaoVO.getListaMovParcela().addAll(envioComunicacaoDTO.getParcelas());
                        pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                        if (!UteisValidacao.validaEmail(clienteVO.getUsuarioMovelVO().getNome())) {
                            throw new Exception("Email válido para realizar envio APP");
                        }
                        Set<String> listaEmails = new HashSet<>();
                        listaEmails.add(clienteVO.getUsuarioMovelVO().getNome());

                        processarTags(empresaVO, clienteVO, new StringBuilder(APP_COBRANCA_ATRASADA),
                                OrigemCobrancaEnum.REGUA_COBRANCA_COBRANCA_ANTECIPADA, null,
                                true, null, null, comunicacaoVO, configVO);

                        //gerar link de pagamento
//                        String link = getUrlLinkPagamento(empresaVO, clienteVO, true, OrigemCobrancaEnum.REGUA_COBRANCA_COBRANCA_ATRASO, null, comunicacaoVO);
                        //todo validar
                        String link = comunicacaoVO.getTagsJSON().optString(TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag());

                        comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, APP_COBRANCA_ATRASADA, clienteVO,
                                tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), clienteVO.getCodigo(), listaEmails, false, enviarSemJenkins, link, configVO);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        comunicacaoVO.erro(ex);
                        if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                            throw ex;
                        }
                    } finally {
                        pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                    }
                }


                //realizar envio de GymBotPro
                if (configuracaoGymBotProValida &&
                        configVO.isComunicadoAtrasoGymbotPro() &&
                        envioComunicacaoDTO.isPodeEnviarGymBotPro() &&
                        !UteisValidacao.emptyString(pessoaVO.getTelefonesString()) &&
                        envioComunicacaoDTO.getTotalGymBotProEnviado() < configVO.getComunicadoAtrasoQtdMaximaEnvios()) {
                    PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.GYMBOT_PRO);
                    try {
                        comunicacaoVO.getListaMovParcela().addAll(envioComunicacaoDTO.getParcelas());
                        pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                        Set<String> listaCelular = new HashSet<>();
                        for (String celular : pessoaVO.getTelefonesString().split("###")) {
                            if (Uteis.validarTelefoneCelular(celular)) {
                                listaCelular.add(celular);
                            }
                        }

                        if (UteisValidacao.emptyList(listaCelular)) {
                            throw new Exception("Nenhum número de celular válido");
                        }

                        comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, WHATSAPP_COBRANCA_ATRASADA, clienteVO,
                                tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), clienteVO.getCodigo(), listaCelular, false, enviarSemJenkins, configVO);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        comunicacaoVO.erro(ex);
                        if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                            throw ex;
                        }
                    } finally {
                        pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                    }
                }
            }

            iniciarComunicadoEnvioEmMassaEmail(key, comunicacoesCriadasEmail, enviarSemJenkins);
        } finally {
            empresaDAO = null;
            movParcelaDAO = null;
            autoCliDAO = null;
            pactoPayComunicacaoDAO = null;
        }
    }

    private StringBuilder gerarCorpoEmail(PactoPayComunicacaoVO comunicacaoVO, EmpresaVO empresaVO, ClienteVO clienteVO, PactoPayConfigVO configVO) throws Exception {
        File arquivoModelo = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_cobranca_pendente.txt").toURI());
        StringBuilder html = FileUtilities.readContentFile(arquivoModelo.getAbsolutePath());
        return replaceInformacoes(arquivoModelo, html, empresaVO, clienteVO, null, OrigemCobrancaEnum.REGUA_COBRANCA_COBRANCA_ATRASO, null, true, null, null, comunicacaoVO, configVO);
    }
}

package br.com.pactosolucoes.pactopay;

import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.enumerador.TipoEnvioPactoPayEnum;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class PactoPayComunicacaoDTO {

    private String chave;
    private String codigoReferencia;
    private Integer codigoOrigem;
    private Integer empresa;
    private Integer pessoa;
    private Integer cliente;
    private Integer tipoEnvioPactoPay;
    private Integer meioEnvio;
    private String assunto;
    private String mensagem;
    private String observacaoHistorico;
    private String link; //link para abrir no aplicativo
    private String chaveJobJenkins;
    private String urlWebhookGymBot;
    private String arquivoModelo;
    private List<String> destinatario;
    private boolean exemplo = false;
    private boolean envioSemJenkins = false;
    private String tokenGymBotPro;
    private String idFluxoGymBotPro;

    public PactoPayComunicacaoDTO() {

    }

    public PactoPayComunicacaoDTO(JSONObject json) {
        this.chave = json.optString("codigoReferencia");
        this.codigoReferencia = json.optString("codigoReferencia");
        this.codigoOrigem = json.optInt("codigoOrigem");
        this.empresa = json.optInt("empresa");
        this.pessoa = json.optInt("pessoa");
        this.cliente = json.optInt("cliente");
        this.tipoEnvioPactoPay = json.optInt("tipoEnvioPactoPay");
        this.meioEnvio = json.optInt("meioEnvio");
        this.assunto = json.optString("assunto");
        this.mensagem = json.optString("mensagem");
        this.observacaoHistorico = json.optString("observacaoHistorico");
        this.link = json.optString("link");
        this.chaveJobJenkins = json.optString("chaveJobJenkins");
        this.exemplo = json.optBoolean("exemplo");
        this.envioSemJenkins = json.optBoolean("envioSemJenkins");
        this.urlWebhookGymBot = json.optString("urlWebhookGymBot");
        this.arquivoModelo = json.optString("arquivoModelo");
        this.tokenGymBotPro = json.optString("tokenGymBotPro");
        this.idFluxoGymBotPro = json.optString("idFluxoGymBotPro");
        try {
            this.destinatario = new ArrayList<>();
            JSONArray jsonArray = json.optJSONArray("destinatario");
            if (jsonArray != null) {
                for (int e = 0; e < jsonArray.length(); e++) {
                    this.destinatario.add(jsonArray.getString(e));
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String getIdentificador() {
        return this.getChave() + "|" + this.getTipoEnvioPactoPayEnum().getIdentificador() + "|" + this.getMeioEnvioEnum().name() + "|" + this.getPessoa();
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getCodigoReferencia() {
        return codigoReferencia;
    }

    public void setCodigoReferencia(String codigoReferencia) {
        this.codigoReferencia = codigoReferencia;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public TipoEnvioPactoPayEnum getTipoEnvioPactoPayEnum() {
        return TipoEnvioPactoPayEnum.obterPorId(this.getTipoEnvioPactoPay());
    }

    public Integer getTipoEnvioPactoPay() {
        return tipoEnvioPactoPay;
    }

    public void setTipoEnvioPactoPay(Integer tipoEnvioPactoPay) {
        this.tipoEnvioPactoPay = tipoEnvioPactoPay;
    }

    public MeioEnvio getMeioEnvioEnum() {
        return MeioEnvio.getMeioEnvioPorCodigo(this.getMeioEnvio());
    }

    public Integer getMeioEnvio() {
        return meioEnvio;
    }

    public void setMeioEnvio(Integer meioEnvio) {
        this.meioEnvio = meioEnvio;
    }

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getObservacaoHistorico() {
        return observacaoHistorico;
    }

    public void setObservacaoHistorico(String observacaoHistorico) {
        this.observacaoHistorico = observacaoHistorico;
    }

    public boolean isExemplo() {
        return exemplo;
    }

    public void setExemplo(boolean exemplo) {
        this.exemplo = exemplo;
    }

    public Integer getCodigoOrigem() {
        return codigoOrigem;
    }

    public void setCodigoOrigem(Integer codigoOrigem) {
        this.codigoOrigem = codigoOrigem;
    }

    public List<String> getDestinatario() {
        if (destinatario == null) {
            destinatario = new ArrayList<>();
        }
        return destinatario;
    }

    public void setDestinatario(List<String> destinatario) {
        this.destinatario = destinatario;
    }

    public boolean isEnvioSemJenkins() {
        return envioSemJenkins;
    }

    public void setEnvioSemJenkins(boolean envioSemJenkins) {
        this.envioSemJenkins = envioSemJenkins;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getUrlWebhookGymBot() {
        return urlWebhookGymBot;
    }

    public void setUrlWebhookGymBot(String urlWebhookGymBot) {
        this.urlWebhookGymBot = urlWebhookGymBot;
    }

    public String getChaveJobJenkins() {
        if (chaveJobJenkins == null) {
            chaveJobJenkins = "";
        }
        return chaveJobJenkins;
    }

    public void setChaveJobJenkins(String chaveJobJenkins) {
        this.chaveJobJenkins = chaveJobJenkins;
    }

    public String getArquivoModelo() {
        if (arquivoModelo == null) {
            arquivoModelo = "";
        }
        return arquivoModelo;
    }

    public void setArquivoModelo(String arquivoModelo) {
        this.arquivoModelo = arquivoModelo;
    }

    public String getTokenGymBotPro() {
        return tokenGymBotPro;
    }

    public void setTokenGymBotPro(String tokenGymBotPro) {
        this.tokenGymBotPro = tokenGymBotPro;
    }

    public String getIdFluxoGymBotPro() {
        return idFluxoGymBotPro;
    }

    public void setIdFluxoGymBotPro(String idFluxoGymBotPro) {
        this.idFluxoGymBotPro = idFluxoGymBotPro;
    }
}

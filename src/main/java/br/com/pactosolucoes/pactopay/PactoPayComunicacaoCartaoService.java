package br.com.pactosolucoes.pactopay;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.enumeradores.TipoContatoCRM;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayConfigVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoEnvioPactoPayEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.PactoPayComunicacao;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class PactoPayComunicacaoCartaoService extends PactoPaySuperService {

    public PactoPayComunicacaoCartaoService(Connection conexao) throws Exception {
        super(conexao);
    }

    public void processar(PactoPayConfigVO configVO, Integer codigoPessoa, boolean enviarSemJenkins) throws Exception {
        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(this.con);

            if (!configVO.isComunicadoCartaoAtivo()) {
                throw new Exception("Comunicado de Cartão não está ativo");
            }

            String key = DAO.resolveKeyFromConnection(this.getCon());
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(configVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            boolean configuracaoEmailValida = empresaTemEmail(key, empresaVO, configVO);
            boolean configuracaoSMSValida = empresaTemSMS(key, empresaVO);
            boolean configuracaoWhatsAppValida = empresaTemGymBot(key, empresaVO);
            boolean configuracaoAppValida = empresaTemAplicativo(key, empresaVO);
            boolean configuracaoGymBotProValida = empresaTemGymBotPro(key, empresaVO);

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT \n");
            sql.append("au.codigo as autorizacao_codigo, \n");
            sql.append("au.operadoracartao as autorizacao_operadoracartao, \n");
            sql.append("au.cartaomascaradointerno as autorizacao_cartaomascaradointerno, \n");
            sql.append("au.validadecartao as autorizacao_validadecartao, \n");
            sql.append("au.cliente as cliente_codigo, \n");
            sql.append("cl.matricula as cliente_matricula, \n");
            sql.append("p.codigo as pessoa_codigo, \n");
            sql.append("p.nome as cliente_nome, \n");
            sql.append("array_to_string(array(SELECT numero FROM telefone WHERE pessoa = cl.pessoa and tipotelefone = 'CE'), '###', '') as cliente_celular, \n");
            sql.append("array_to_string(array(SELECT email FROM email WHERE bloqueadobounce IS FALSE and pessoa = cl.pessoa), '###', '') as cliente_emails, \n");
            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and origem = '").append(TipoEnvioPactoPayEnum.CARTAO_VENCIDO.getIdentificador()).append("' ");
            sql.append("and origemcodigo = au.codigo and dia::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and tipocontato = '").append(TipoContatoCRM.CONTATO_SMS.getSigla()).append("') as pode_enviar_sms_vencido, \n");
            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and origem = '").append(TipoEnvioPactoPayEnum.CARTAO_VENCIDO.getIdentificador()).append("' ");
            sql.append("and origemcodigo = au.codigo and dia::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and tipocontato = '").append(TipoContatoCRM.CONTATO_EMAIL.getSigla()).append("') as pode_enviar_email_vencido, \n");
            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and origem = '").append(TipoEnvioPactoPayEnum.CARTAO_A_VENCER.getIdentificador()).append("' ");
            sql.append("and origemcodigo = au.codigo and dia::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and tipocontato = '").append(TipoContatoCRM.CONTATO_SMS.getSigla()).append("') as pode_enviar_sms_avencer, \n");
            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and origem = '").append(TipoEnvioPactoPayEnum.CARTAO_A_VENCER.getIdentificador()).append("' ");
            sql.append("and origemcodigo = au.codigo and dia::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and tipocontato = '").append(TipoContatoCRM.CONTATO_EMAIL.getSigla()).append("') as pode_enviar_email_avencer, \n");
            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and origem = '").append(TipoEnvioPactoPayEnum.CARTAO_VENCIDO.getIdentificador()).append("' ");
            sql.append("and origemcodigo = au.codigo and dia::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and tipocontato = '").append(TipoContatoCRM.CONTATO_GYMBOT.getSigla()).append("') as pode_enviar_whatsapp_vencido, \n");
            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and origem = '").append(TipoEnvioPactoPayEnum.CARTAO_VENCIDO.getIdentificador()).append("' ");
            sql.append("and origemcodigo = au.codigo and dia::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and tipocontato = '").append(TipoContatoCRM.CONTATO_APP.getSigla()).append("') as pode_enviar_app_vencido, \n");
            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and origem = '").append(TipoEnvioPactoPayEnum.CARTAO_A_VENCER.getIdentificador()).append("' ");
            sql.append("and origemcodigo = au.codigo and dia::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and tipocontato = '").append(TipoContatoCRM.CONTATO_GYMBOT.getSigla()).append("') as pode_enviar_whatsapp_avencer, \n");
            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and origem = '").append(TipoEnvioPactoPayEnum.CARTAO_A_VENCER.getIdentificador()).append("' ");
            sql.append("and origemcodigo = au.codigo and dia::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and tipocontato = '").append(TipoContatoCRM.CONTATO_APP.getSigla()).append("') as pode_enviar_app_avencer, \n");
            //gymbotpro
            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and origem = '").append(TipoEnvioPactoPayEnum.CARTAO_VENCIDO.getIdentificador()).append("' ");
            sql.append("and origemcodigo = au.codigo and dia::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and tipocontato = '").append(TipoContatoCRM.CONTATO_GYMBOT_PRO.getSigla()).append("') as pode_enviar_gymbotpro_vencido, \n");
            sql.append("not exists(select codigo from historicocontato where cliente = cl.codigo and origem = '").append(TipoEnvioPactoPayEnum.CARTAO_A_VENCER.getIdentificador()).append("' ");
            sql.append("and origemcodigo = au.codigo and dia::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(Calendario.hoje()))).append("' ");
            sql.append("and tipocontato = '").append(TipoContatoCRM.CONTATO_GYMBOT_PRO.getSigla()).append("') as pode_enviar_gymbotpro_avencer, \n");

            sql.append("usm.codigo as usuariomovel_codigo, \n");
            sql.append("usm.nome as usuariomovel_nome \n");
            sql.append("FROM autorizacaocobrancacliente au \n");
            sql.append("INNER JOIN cliente cl ON au.cliente = cl.codigo \n");
            sql.append("INNER JOIN pessoa p ON p.codigo = cl.pessoa \n");
            sql.append("LEFT JOIN usuariomovel usm on usm.codigo = (select max(codigo) from usuariomovel where ativo and cliente = cl.codigo) \n");
            sql.append("WHERE au.ativa \n");
            sql.append("and coalesce(au.cartaomascaradointerno, '') <> '' \n");
            sql.append("AND au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                sql.append("AND cl.pessoa = ").append(codigoPessoa).append(" \n");
            }
            sql.append("AND cl.empresa = ").append(empresaVO.getCodigo()).append(" \n");
            if (configVO.isComunicadoCartaoVencido() &&
                    configVO.isComunicadoCartaoProximoVencimento()) {
                sql.append("AND (\n");
                //cartao vencido
                sql.append("(TO_DATE(au.validadecartao, 'MM/YYYY') < '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("') \n");
                sql.append("or \n");
                //cartao a vencer próximo mês
                sql.append("(TO_DATE(au.validadecartao, 'MM/YYYY') = '").append(Uteis.getDataFormatoBD(Uteis.somarMeses(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), 1))).append("') \n");
                sql.append(") \n");
            } else if (configVO.isComunicadoCartaoVencido()) {
                sql.append("AND (TO_DATE(au.validadecartao, 'MM/YYYY') < '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))).append("') \n");
            } else if (configVO.isComunicadoCartaoProximoVencimento()) {
                sql.append("AND (TO_DATE(au.validadecartao, 'MM/YYYY') = '").append(Uteis.getDataFormatoBD(Uteis.somarMeses(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), 1))).append("') \n");
            }
            sql.append("AND exists (select codigo from movparcela where situacao = 'EA' and pessoa = cl.pessoa) \n");
            sql.append("order by au.codigo \n");


            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            if (!UteisValidacao.emptyNumber(codigoPessoa) && UteisValidacao.emptyNumber(total)) {
                throw new Exception("Nenhum cartão encontrado");
            }

            List<PactoPayComunicacaoVO> comunicacoesCriadasEmail = new ArrayList<>();
            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        PactoPayComunicacao pactoPayComunicacaoDAO;
                        try {
                            pactoPayComunicacaoDAO = new PactoPayComunicacao(this.con);

                            String numerosCelular = rs.getString("cliente_celular");
                            String emails = rs.getString("cliente_emails");
                            boolean pode_enviar_sms_vencido = rs.getBoolean("pode_enviar_sms_vencido");
                            boolean pode_enviar_sms_avencer = rs.getBoolean("pode_enviar_sms_avencer");
                            boolean pode_enviar_email_vencido = rs.getBoolean("pode_enviar_email_vencido");
                            boolean pode_enviar_email_avencer = rs.getBoolean("pode_enviar_email_avencer");
                            boolean pode_enviar_whatsapp_vencido = rs.getBoolean("pode_enviar_whatsapp_vencido");
                            boolean pode_enviar_whatsapp_avencer = rs.getBoolean("pode_enviar_whatsapp_avencer");
                            boolean pode_enviar_app_vencido = rs.getBoolean("pode_enviar_app_vencido");
                            boolean pode_enviar_app_avencer = rs.getBoolean("pode_enviar_app_avencer");
                            boolean pode_enviar_gymbotpro_vencido = rs.getBoolean("pode_enviar_gymbotpro_vencido");
                            boolean pode_enviar_gymbotpro_avencer = rs.getBoolean("pode_enviar_gymbotpro_avencer");

                            PessoaVO pessoaVO = new PessoaVO();
                            pessoaVO.setCodigo(rs.getInt("pessoa_codigo"));
                            pessoaVO.setNome(rs.getString("cliente_nome"));

                            ClienteVO clienteVO = new ClienteVO();
                            clienteVO.setCodigo(rs.getInt("cliente_codigo"));
                            clienteVO.setMatricula(rs.getString("cliente_matricula"));
                            clienteVO.setPessoa(pessoaVO);

                            clienteVO.getUsuarioMovelVO().setCodigo(rs.getInt("usuariomovel_codigo"));
                            clienteVO.getUsuarioMovelVO().setNome(rs.getString("usuariomovel_nome"));
                            clienteVO.getUsuarioMovelVO().setCliente(clienteVO);

                            AutorizacaoCobrancaClienteVO autoVO = new AutorizacaoCobrancaClienteVO();
                            autoVO.setCodigo(rs.getInt("autorizacao_codigo"));
                            autoVO.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(rs.getInt("autorizacao_operadoracartao")));
                            autoVO.setCartaoMascarado(rs.getString("autorizacao_cartaomascaradointerno"));
                            autoVO.setValidadeCartao(rs.getString("autorizacao_validadecartao"));
                            autoVO.setCliente(clienteVO);

                            OrigemCobrancaEnum origemCobrancaEnum = autoVO.isCartaoVencido() ? OrigemCobrancaEnum.REGUA_COBRANCA_CARTAO_VENCIDO : OrigemCobrancaEnum.REGUA_COBRANCA_CARTAO_A_VENCER;
                            TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum = autoVO.isCartaoVencido() ? TipoEnvioPactoPayEnum.CARTAO_VENCIDO : TipoEnvioPactoPayEnum.CARTAO_A_VENCER;

                            boolean pode_enviar_sms;
                            boolean pode_enviar_email;
                            boolean pode_enviar_whatsapp;
                            boolean pode_enviar_app;
                            boolean pode_enviar_gymbotpro;
                            if (autoVO.isCartaoVencido()) {
                                pode_enviar_sms = pode_enviar_sms_vencido;
                                pode_enviar_email = pode_enviar_email_vencido;
                                pode_enviar_whatsapp = pode_enviar_whatsapp_vencido;
                                pode_enviar_app = pode_enviar_app_vencido;
                                pode_enviar_gymbotpro = pode_enviar_gymbotpro_vencido;
                            } else {
                                pode_enviar_sms = pode_enviar_sms_avencer;
                                pode_enviar_email = pode_enviar_email_avencer;
                                pode_enviar_whatsapp = pode_enviar_whatsapp_avencer;
                                pode_enviar_app = pode_enviar_app_avencer;
                                pode_enviar_gymbotpro = pode_enviar_gymbotpro_avencer;
                            }

                            //caso tenha codigopessoa é pq está forçando o envio então vamos forçar
                            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                                pode_enviar_sms = true;
                                pode_enviar_email = true;
                                pode_enviar_whatsapp = true;
                                pode_enviar_app = true;
                                pode_enviar_gymbotpro = true;
                            }

                            String assunto = autoVO.isCartaoVencido() ? processarAssunto(EMAIL_ASSUNTO_CARTAO_VENCIDO, clienteVO) : processarAssunto(EMAIL_ASSUNTO_CARTAO_A_VENCER, clienteVO);

                            StringBuilder obsHistoricoContato = new StringBuilder();
                            obsHistoricoContato.append("<p>Título: ").append(assunto).append(" </p>");
                            obsHistoricoContato.append("<p>Cartão: ").append(autoVO.getCartaoMascaradoUltimos4Digitos()).append(" </p>");
                            obsHistoricoContato.append("<p>Bandeira: ").append(autoVO.getOperadoraCartaoApresentar()).append(" </p>");
                            obsHistoricoContato.append("<p>Validade: ").append(autoVO.getValidadeCartao()).append(" </p>");
                            obsHistoricoContato.append("<p>Remente: RÉGUA DE COBRANÇA - ").append(tipoEnvioPactoPayEnum.getIdentificador()).append(" </p>");

                            //realizar envio de email
                            if (configuracaoEmailValida &&
                                    configVO.isComunicadoCartaoEmail() &&
                                    pode_enviar_email &&
                                    !UteisValidacao.emptyString(emails)) {
                                PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.EMAIL);
                                try {
                                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                                    Set<String> listaEmails = new HashSet<>();
                                    for (String email : emails.split("###")) {
                                        if (UteisValidacao.validaEmail(email)) {
                                            listaEmails.add(email);
                                        }
                                    }

                                    if (UteisValidacao.emptyList(listaEmails)) {
                                        throw new Exception("Nenhum email válido para realizar envio");
                                    }

                                    String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + autoVO.getCodigo());
                                    StringBuilder html = gerarCorpoEmail(comunicacaoVO, empresaVO, clienteVO, autoVO, origemCobrancaEnum, configVO);

                                    comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, html.toString(), clienteVO,
                                            tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), autoVO.getCodigo(), listaEmails, false, enviarSemJenkins, configVO);
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                    comunicacaoVO.erro(ex);
                                    if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                                        throw ex;
                                    }
                                } finally {
                                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                                }
                                comunicacoesCriadasEmail.add(comunicacaoVO);
                            }

                            //realizar envio de sms
                            if (configuracaoSMSValida &&
                                    configVO.isComunicadoCartaoSMS() &&
                                    pode_enviar_sms &&
                                    !UteisValidacao.emptyString(numerosCelular)) {
                                PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.SMS);
                                try {
                                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                                    String mensagemEnviar = obterMensagemEnviarSMS(
                                            (autoVO.isCartaoVencido() ? SMS_CARTAO_VENCIDO : SMS_CARTAO_A_VENCER),
                                            (autoVO.isCartaoVencido() ? SMS_CARTAO_VENCIDO_COM_TAGS : SMS_CARTAO_A_VENCER_COM_TAGS),
                                            empresaVO, clienteVO, origemCobrancaEnum, false, null, autoVO, null, comunicacaoVO, configVO);

                                    Set<String> listaCelular = new HashSet<>();
                                    for (String celular : numerosCelular.split("###")) {
                                        if (Uteis.validarTelefoneCelular(celular)) {
                                            listaCelular.add(celular);
                                        }
                                    }

                                    if (UteisValidacao.emptyList(listaCelular)) {
                                        throw new Exception("Nenhum número de celular válido");
                                    }

                                    String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + autoVO.getCodigo());

                                    comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, mensagemEnviar, clienteVO,
                                            tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), autoVO.getCodigo(), listaCelular, false, enviarSemJenkins, configVO);
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                    comunicacaoVO.erro(ex);
                                    if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                                        throw ex;
                                    }
                                } finally {
                                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                                }
                            }

                            //realizar envio de WhatsApp
                            if (configuracaoWhatsAppValida &&
                                    configVO.isComunicadoCartaoWhatsApp() &&
                                    pode_enviar_whatsapp &&
                                    !UteisValidacao.emptyString(numerosCelular)) {
                                PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.GYMBOT);
                                try {
                                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                                    String mensagemEnviar = (autoVO.isCartaoVencido() ? WHATSAPP_CARTAO_VENCIDO : WHATSAPP_CARTAO_A_VENCER);

                                    Set<String> listaCelular = new HashSet<>();
                                    for (String celular : numerosCelular.split("###")) {
                                        if (Uteis.validarTelefoneCelular(celular)) {
                                            listaCelular.add(celular);
                                        }
                                    }

                                    if (UteisValidacao.emptyList(listaCelular)) {
                                        throw new Exception("Nenhum número de celular válido");
                                    }

                                    String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + autoVO.getCodigo());

                                    comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, mensagemEnviar, clienteVO,
                                            tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), autoVO.getCodigo(), listaCelular, false, enviarSemJenkins, configVO);
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                    comunicacaoVO.erro(ex);
                                    if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                                        throw ex;
                                    }
                                } finally {
                                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                                }
                            }

                            //realizar envio de notificação APP
                            if (configuracaoAppValida &&
                                    configVO.isComunicadoCartaoApp() &&
                                    pode_enviar_app &&
                                    !UteisValidacao.emptyString(clienteVO.getUsuarioMovelVO().getNome())) {
                                PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.APP);
                                try {
                                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                                    if (!UteisValidacao.validaEmail(clienteVO.getUsuarioMovelVO().getNome())) {
                                        throw new Exception("Email válido para realizar envio APP");
                                    }

                                    String mensagemEnviar = (autoVO.isCartaoVencido() ? APP_CARTAO_VENCIDO : APP_CARTAO_A_VENCER);
                                    Set<String> listaEmails = new HashSet<>();
                                    listaEmails.add(clienteVO.getUsuarioMovelVO().getNome());

                                    String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + autoVO.getCodigo());

                                    comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, mensagemEnviar, clienteVO,
                                            tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), autoVO.getCodigo(), listaEmails, false, enviarSemJenkins, configVO);
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                    comunicacaoVO.erro(ex);
                                    if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                                        throw ex;
                                    }
                                } finally {
                                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                                }
                            }

                            //realizar envio de GymBotPro
                            if (configuracaoGymBotProValida &&
                                    configVO.isComunicadoCartaoGymbotPro() &&
                                    pode_enviar_gymbotpro &&
                                    !UteisValidacao.emptyString(numerosCelular)) {
                                PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO(pessoaVO, empresaVO, MeioEnvio.GYMBOT_PRO);
                                try {
                                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);

                                    String mensagemEnviar = (autoVO.isCartaoVencido() ? WHATSAPP_CARTAO_VENCIDO : WHATSAPP_CARTAO_A_VENCER);

                                    Set<String> listaCelular = new HashSet<>();
                                    for (String celular : numerosCelular.split("###")) {
                                        if (Uteis.validarTelefoneCelular(celular)) {
                                            listaCelular.add(celular);
                                        }
                                    }

                                    if (UteisValidacao.emptyList(listaCelular)) {
                                        throw new Exception("Nenhum número de celular válido");
                                    }

                                    String codigoReferencia = (tipoEnvioPactoPayEnum.getIdentificador() + "|" + autoVO.getCodigo());

                                    comunicacaoVO = iniciarComunicado(comunicacaoVO, key, codigoReferencia, empresaVO, assunto, mensagemEnviar, clienteVO,
                                            tipoEnvioPactoPayEnum, obsHistoricoContato.toString(), autoVO.getCodigo(), listaCelular, false, enviarSemJenkins, configVO);
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                    comunicacaoVO.erro(ex);
                                    if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                                        throw ex;
                                    }
                                } finally {
                                    pactoPayComunicacaoDAO.gravarSemCommit(comunicacaoVO);
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            if (!UteisValidacao.emptyNumber(codigoPessoa)) {
                                throw ex;
                            }
                        }
                    }
                }
            }

            iniciarComunicadoEnvioEmMassaEmail(key, comunicacoesCriadasEmail, enviarSemJenkins);
        } finally {
            empresaDAO = null;
        }
    }

    private StringBuilder gerarCorpoEmail(PactoPayComunicacaoVO comunicacaoVO,
                                          EmpresaVO empresaVO, ClienteVO clienteVO,
                                          AutorizacaoCobrancaClienteVO autoVO,
                                          OrigemCobrancaEnum origemCobrancaEnum,
                                          PactoPayConfigVO configVO) throws Exception {
        File arquivoModelo = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_cartao_vencendo.txt").toURI());
        if (autoVO.isCartaoVencido()) {
            arquivoModelo = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_cartao_vencido.txt").toURI());
        }
        StringBuilder html = FileUtilities.readContentFile(arquivoModelo.getAbsolutePath());
        return replaceInformacoes(arquivoModelo, html, empresaVO, clienteVO, null, origemCobrancaEnum, null, false, autoVO, null, comunicacaoVO, configVO);
    }
}

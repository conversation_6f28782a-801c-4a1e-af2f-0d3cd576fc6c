package br.com.pactosolucoes.pactopay;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayConfigVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.PactoPayConfig;
import negocio.facade.jdbc.financeiro.Transacao;

import java.io.File;
import java.sql.Connection;

public class PactoPayTesteEmail {

    public static void main(String[] args) {
        try {
            String email = "<EMAIL>";

            File arq1 = new File(PactoPayTesteEmail.class.getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_cartao_vencendo.txt").toURI());
            enviarEmailTeste(email, arq1);

            File arq2 = new File(PactoPayTesteEmail.class.getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_cartao_vencido.txt").toURI());
            enviarEmailTeste(email, arq2);

            File arq3 = new File(PactoPayTesteEmail.class.getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_cobranca_antecipada.txt").toURI());
            enviarEmailTeste(email, arq3);

            File arq4 = new File(PactoPayTesteEmail.class.getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_cobranca_pendente.txt").toURI());
            enviarEmailTeste(email, arq4);

            File arq5 = new File(PactoPayTesteEmail.class.getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_resultado_cobranca_aprovada.txt").toURI());
            enviarEmailTeste(email, arq5);

            File arq6 = new File(PactoPayTesteEmail.class.getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_resultado_cobranca_cancelada.txt").toURI());
            enviarEmailTeste(email, arq6);

            File arq7 = new File(PactoPayTesteEmail.class.getResource("/br/com/pactosolucoes/comuns/util/resources/pactopay_email_resultado_cobranca_negada.txt").toURI());
            enviarEmailTeste(email, arq7);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void enviarEmailTeste(String email, File arquivoModelo) {
        Empresa empresaDAO;
        PactoPayConfig pactoPayConfigDAO;
        Transacao transacaoDAO;
        Cliente clienteDAO;
        PactoPaySuperService service;
        try (Connection con = new DAO().obterConexaoEspecifica("teste")) {
            empresaDAO = new Empresa(con);
            pactoPayConfigDAO = new PactoPayConfig(con);
            transacaoDAO = new Transacao(con);
            clienteDAO = new Cliente(con);
            service = new PactoPaySuperService(con);

            Integer empresa = 1;
            Integer transacao = 110899;

            ConfiguracaoSistemaCRMVO configCRMVO = SuperControle.getConfiguracaoSMTPNoReply();
            configCRMVO.setRemetentePadrao("Sistema Pacto");

            StringBuilder html = FileUtilities.readContentFile(arquivoModelo.getAbsolutePath());

            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(transacaoVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            PactoPayConfigVO configVO = pactoPayConfigDAO.consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_TODOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(configVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            PactoPayComunicacaoVO comunicacaoVO = new PactoPayComunicacaoVO();
            comunicacaoVO.setMeioEnvio(MeioEnvio.EMAIL);

            html = service.replaceInformacoes(arquivoModelo, html, empresaVO, clienteVO, null, null, null, false, null, transacaoVO, comunicacaoVO, configVO);

            String assunto = ("Luiz Felipe - Arq: " + arquivoModelo.getName());

            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo(assunto, configCRMVO);
            uteisEmail.enviarEmail(email, null, html.toString(), "");
            Uteis.logarDebug("Email enviado | " + email + " | " + assunto);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            empresaDAO = null;
            pactoPayConfigDAO = null;
            transacaoDAO = null;
            clienteDAO = null;
            service = null;
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.agendatotal.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Uteis;

import static negocio.comuns.utilitarias.Uteis.getPaintFotoDaNuvem;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 *
 * <AUTHOR>
 */
public class TurmaAulaCheiaJSON extends SuperJSON{
    private String nome;
    private String nomeModalidade;
    private Integer modalidade;
    private Integer codigo;
    private Integer professor;
    private Integer professor<PERSON>odigoPessoa;
    private String nomeProfessor;
    private Integer ambiente;
    private String nomeAmbiente;
    private Integer tolerancia;
    private Integer capacidade;
    private Integer limiteVagasAgregados;
    private String dias;
    private String horarios;
    private Integer horarioCodigo;
    private String inicio;
    private String fim;
    private String mensagem;
    private Double bonificacao;
    private Integer pontosBonus;
    private Double meta;
    private Integer ocupacao;
    private Integer codigoAulaCheia;
    private Integer minutosTolerancia;
    private Integer empresa;
    private String campoOrdenacao;
    private boolean validarRestricoesMarcacao;
    private boolean naoValidarModalidadeContrato;
    private String modalidadeFotokey;
    private String professorFotokey;
    private boolean integracaoSpivi;
    private String dataInicio;
    private String dataFim;
    private Integer produtoGymPass;
    private Integer idClasseGymPass;
    private String urlTurmaVirtual;
    private Integer tipoTolerancia;
    private Integer usuario;
    private String urlVideoYoutube;
    private byte[] image;
    private String fotoKey;
    private String imageUrl;
    private boolean visualizarProdutosGympass;
    private boolean visualizarProdutosTotalpass;
    private boolean permiteFixar;
    private boolean aulaIntegracaoSelfloops;
    private boolean manterFotoAnterior;

    private Integer idadeMaximaAnos;
    private Integer idadeMaximaMeses;
    private Integer idadeMinimaAnos;
    private Integer idadeMinimaMeses;
    private String niveis;
    private String tipoReservaEquipamento;
    private String mapaEquipamentos;

    public void setCampoOrdenacao(String campoOrdenacao) {
        this.campoOrdenacao = campoOrdenacao;
    }
    
    public String getCampoOrdenacao(){
        if(campoOrdenacao == null){
            campoOrdenacao = nome+" - "+nomeProfessor+" - "+codigo;
        }
        return campoOrdenacao;
    }
    
    public TurmaVO toTurmaVO() throws Exception{
        TurmaVO turma = new TurmaVO();
        turma.setDataInicialVigencia(Uteis.getDate(this.getInicio(), "dd/MM/yyyy"));
        turma.setDataFinalVigencia(Uteis.getDate(this.getFim(), "dd/MM/yyyy"));
        turma.setDataInicioMatricula(Uteis.getDate(this.getInicio(), "dd/MM/yyyy"));
        turma.setDataFimMatricula(Uteis.getDate(this.getFim(), "dd/MM/yyyy"));
        turma.setOcupacao(this.getOcupacao());
        turma.setMensagem(this.getMensagem());
        turma.setMeta(this.getMeta());
        turma.setPontosBonus(this.getPontosBonus());
        turma.setBonificacao(this.getBonificacao());
        turma.setIdentificador(this.getNome());
        turma.setDescricao(this.getNome());
        turma.setModalidade(new ModalidadeVO());
        turma.setIdadeMaxima(130);
        turma.setEmpresa(new EmpresaVO());
        turma.getEmpresa().setCodigo(this.getEmpresa());
        turma.getModalidade().setCodigo(this.getModalidade());
        turma.setAulaColetiva(true);
        turma.setProfessor(this.getProfessor());
        turma.setTolerancia(this.getTolerancia());
        turma.setTipoTolerancia(this.getTipoTolerancia());
        turma.setCapacidade(this.getCapacidade());
        turma.setLimiteVagasAgregados(this.getLimiteVagasAgregados());
        turma.setAmbiente(this.getAmbiente());
        turma.setHorarios(this.getHorarios());
        turma.setDias(this.getDias());
        turma.setCodigo(this.getCodigo());
        turma.setValidarRestricoesMarcacao(this.isValidarRestricoesMarcacao());
        turma.setNaoValidarModalidadeContrato(this.isNaoValidarModalidadeContrato());
        turma.setIntegracaoSpivi(this.isIntegracaoSpivi());
        turma.setProdutoGymPass(this.getProdutoGymPass());
        turma.setIdClasseGymPass(this.getIdClasseGymPass());
        turma.setUrlTurmaVirtual(this.getUrlTurmaVirtual());
        turma.setUsuario(this.getUsuario());
        turma.setUrlVideoYoutube(this.getUrlVideoYoutube());
        turma.setVisualizarProdutosGympass(this.getVisualizarProdutosGympass());
        turma.setVisualizarProdutosTotalpass(this.getVisualizarProdutosTotalpass());
        turma.setPermiteFixar(this.isPermiteFixar());
        turma.setAulaIntegracaoSelfloops(this.isAulaIntegracaoSelfloops());
        turma.setManterFotoAnterior(this.isManterFotoAnterior());

        turma.setIdadeMaxima(this.getIdadeMaximaAnos());
        turma.setIdadeMaximaMeses(this.getIdadeMaximaMeses());
        turma.setIdadeMinima(this.getIdadeMinimaAnos());
        turma.setIdadeMinimaMeses(this.getIdadeMinimaMeses());
        turma.setNiveis(this.getNiveis());
        turma.setTipoReservaEquipamento(this.getTipoReservaEquipamento());
        turma.setMapaEquipamentos(this.getMapaEquipamentos());
        return turma;
    }


    public boolean isPermiteFixar() {
        return permiteFixar;
    }

    public void setPermiteFixar(boolean permiteFixar) {
        this.permiteFixar = permiteFixar;
    }

    public Integer getHorarioCodigo() {
        return horarioCodigo;
    }

    public void setHorarioCodigo(Integer horarioCodigo) {
        this.horarioCodigo = horarioCodigo;
    }

    public String getNomeModalidade() {
        return nomeModalidade;
    }

    public void setNomeModalidade(String nomeModalidade) {
        this.nomeModalidade = nomeModalidade;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public void setProfessor(Integer professor) {
        this.professor = professor;
    }

    public Integer getProfessorCodigoPessoa() {
        return professorCodigoPessoa;
    }

    public void setProfessorCodigoPessoa(Integer professorCodigoPessoa) {
        this.professorCodigoPessoa = professorCodigoPessoa;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public void setTolerancia(Integer tolerancia) {
        this.tolerancia = tolerancia;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public void setLimiteVagasAgregados(Integer limiteVagasAgregados) {
        this.limiteVagasAgregados = limiteVagasAgregados;
    }

    public void setDias(String dias) {
        this.dias = dias;
    }

    public void setHorarios(String horarios) {
        this.horarios = horarios;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public void setBonificacao(Double bonificacao) {
        this.bonificacao = bonificacao;
    }

    public void setPontosBonus(Integer pontosBonus) {
        this.pontosBonus = pontosBonus;
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public void setCodigoAulaCheia(Integer codigoAulaCheia) {
        this.codigoAulaCheia = codigoAulaCheia;
    }

    
    
    public String getNomeAmbiente() {
        return nomeAmbiente;
    }

    public void setNomeAmbiente(String nomeAmbiente) {
        this.nomeAmbiente = nomeAmbiente;
    }

    public Integer getMinutosTolerancia() {
        return minutosTolerancia;
    }

    public void setMinutosTolerancia(Integer minutosTolerancia) {
        this.minutosTolerancia = minutosTolerancia;
    }
    
    public String getNome() {
        return nome;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public Integer getProfessor() {
        return professor;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public Integer getTolerancia() {
        return tolerancia;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public Integer getLimiteVagasAgregados() {
        return limiteVagasAgregados;
    }

    public String getDias() {
        return dias;
    }

    public String getHorarios() {
        return horarios;
    }

    public String getInicio() {
        return inicio;
    }

    public String getFim() {
        return fim;
    }

    public String getMensagem() {
        return mensagem;
    }

    public Double getBonificacao() {
        return bonificacao;
    }

    public Integer getPontosBonus() {
        return pontosBonus;
    }

    public Double getMeta() {
        return meta;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public Integer getCodigoAulaCheia() {
        return codigoAulaCheia;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public boolean isValidarRestricoesMarcacao() {
        return validarRestricoesMarcacao;
    }

    public void setValidarRestricoesMarcacao(boolean validarRestricoesMarcacao) {
        this.validarRestricoesMarcacao = validarRestricoesMarcacao;
    }

    public String getModalidadeFotokey() {
        return modalidadeFotokey;
    }

    public void setModalidadeFotokey(String modalidadeFotokey) {
        this.modalidadeFotokey = modalidadeFotokey;
    }

    public String getProfessorFotokey() {
        return professorFotokey;
    }

    public void setProfessorFotokey(String professorFotokey) {
        this.professorFotokey = professorFotokey;
    }

    public boolean isIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public void setIntegracaoSpivi(boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFim() {
        return dataFim;
    }

    public void setDataFim(String dataFim) {
        this.dataFim = dataFim;
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public String getUrlTurmaVirtual() {
        return urlTurmaVirtual;
    }

    public void setUrlTurmaVirtual(String urlTurmaVirtual) {
        this.urlTurmaVirtual = urlTurmaVirtual;
    }

    public boolean isNaoValidarModalidadeContrato() {
        return naoValidarModalidadeContrato;
    }

    public void setNaoValidarModalidadeContrato(boolean naoValidarModalidadeContrato) {
        this.naoValidarModalidadeContrato = naoValidarModalidadeContrato;
    }

    public Integer getTipoTolerancia() {
        return tipoTolerancia;
    }

    public void setTipoTolerancia(Integer tipoTolerancia) {
        this.tipoTolerancia = tipoTolerancia;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String linkVideoYoutube) {
        this.urlVideoYoutube = linkVideoYoutube;
    }

    public byte[] getImage() {
        return image;
    }

    public void setImage(byte[] image) {
        this.image = image;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getImageUrl() {
        if(isNotBlank(this.fotoKey)) {
            this.imageUrl = getPaintFotoDaNuvem(this.fotoKey);
        }
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public boolean getVisualizarProdutosGympass() {return visualizarProdutosGympass;  }

    public void setVisualizarProdutosGympass(boolean visualizarProdutosGympass) {
        this.visualizarProdutosGympass = visualizarProdutosGympass;
    }

    public boolean getVisualizarProdutosTotalpass() {return visualizarProdutosTotalpass; }

    public void setVisualizarProdutosTotalpass(boolean visualizarProdutosTotalpass) {
        this.visualizarProdutosTotalpass = visualizarProdutosTotalpass;
    }

    public boolean isManterFotoAnterior() {
        return manterFotoAnterior;
    }

    public void setManterFotoAnterior(boolean manterFotoAnterior) {
        this.manterFotoAnterior = manterFotoAnterior;
    }

    public Integer getIdadeMaximaAnos() {
        return idadeMaximaAnos;
    }

    public void setIdadeMaximaAnos(Integer idadeMaximaAnos) {
        this.idadeMaximaAnos = idadeMaximaAnos;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getIdadeMinimaAnos() {
        return idadeMinimaAnos;
    }

    public void setIdadeMinimaAnos(Integer idadeMinimaAnos) {
        this.idadeMinimaAnos = idadeMinimaAnos;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public String getNiveis() {
        return niveis;
    }

    public void setNiveis(String niveis) {
        this.niveis = niveis;
    }

    public String getTipoReservaEquipamento() {
        return tipoReservaEquipamento;
    }

    public void setTipoReservaEquipamento(String tipoReservaEquipamento) {
        this.tipoReservaEquipamento = tipoReservaEquipamento;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public boolean isAulaIntegracaoSelfloops() {
        return aulaIntegracaoSelfloops;
    }

    public void setAulaIntegracaoSelfloops(boolean aulaIntegracaoSelfloops) {
        this.aulaIntegracaoSelfloops = aulaIntegracaoSelfloops;
    }
}

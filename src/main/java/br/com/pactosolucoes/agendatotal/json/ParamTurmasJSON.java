/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.agendatotal.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class ParamTurmasJSON extends SuperJSON{
    private String key;
    private String dataOrigem;
    private String dataReposicao;
    private Integer idHorarioTurmaOrigem;
    private Integer idHorarioTurmaReposicao;
    private Integer codigoCliente;
    private Integer codigoPassivo;
    private Integer autorizado;
    private Integer codigoContrato;
    private Integer usuario; 
    private Integer empresa; 
    private Integer origemSistema;
    private Integer nivel;
    private Integer minutosAgendarAntecedencia;
    private boolean experimental = false;
    private boolean forcarMarcar = false;
    private boolean validarModalidade = false;
    private boolean validarHorario = false;
    private boolean controlarFreepass = false;
    private Integer nrAulasExperimentais;
    private Integer minutosDesmarcarAntecedencia;
    private boolean proibirComParcelaVencida = false;
    private boolean proibirMarcarAulaAntesPagamentoPrimeiraParcela = false;
    private boolean permAlunoMarcarAulaOutraEmpresa = false;
    private Integer spiviSeat;
    private Integer spiviEvenID;
    private Integer spiviClientID;
    private Boolean somenteValidar = false;
    private Boolean ignorarTolerancia = false;
    private String bookingId;
    private String justificativa;
    private Integer limiteReposicoes;
    private Boolean manterRenovacao;
    private Boolean filaEspera;
    private Integer nrValidarVezesModalidade;
    private Integer modalidade;
    private Integer qtdfaltasbloqueioaluno;
    private Integer qtdtempobloqueioaluno;
    private Boolean descontarCreditoContratoAulaMarcada = false;
    private Boolean bloquearGerarReposicaoAulaJaReposta;

    public Integer getLimiteReposicoes() {
        return limiteReposicoes;
    }

    public void setLimiteReposicoes(Integer limiteReposicoes) {
        this.limiteReposicoes = limiteReposicoes;
    }

    public Boolean getManterRenovacao() {
        return manterRenovacao;
    }

    public void setManterRenovacao(Boolean manterRenovacao) {
        this.manterRenovacao = manterRenovacao;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDataOrigem() {
        return dataOrigem;
    }

    public void setDataOrigem(String dataOrigem) {
        this.dataOrigem = dataOrigem;
    }

    public String getDataReposicao() {
        return dataReposicao;
    }

    public void setDataReposicao(String dataReposicao) {
        this.dataReposicao = dataReposicao;
    }

    public Integer getIdHorarioTurmaOrigem() {
        return idHorarioTurmaOrigem;
    }

    public void setIdHorarioTurmaOrigem(Integer idHorarioTurmaOrigem) {
        this.idHorarioTurmaOrigem = idHorarioTurmaOrigem;
    }

    public Integer getIdHorarioTurmaReposicao() {
        return idHorarioTurmaReposicao;
    }

    public void setIdHorarioTurmaReposicao(Integer idHorarioTurmaReposicao) {
        this.idHorarioTurmaReposicao = idHorarioTurmaReposicao;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public Integer getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(Integer origemSistema) {
        this.origemSistema = origemSistema;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getMinutosAgendarAntecedencia() {
        return minutosAgendarAntecedencia;
    }

    public void setMinutosAgendarAntecedencia(Integer minutosAgendarAntecedencia) {
        this.minutosAgendarAntecedencia = minutosAgendarAntecedencia;
    }

    public boolean isExperimental() {
        return experimental;
    }

    public void setExperimental(boolean experimental) {
        this.experimental = experimental;
    }

    public boolean isValidarModalidade() {
        return validarModalidade;
    }

    public void setValidarModalidade(boolean validarModalidade) {
        this.validarModalidade = validarModalidade;
    }

    public Integer getNrAulasExperimentais() {
        return nrAulasExperimentais;
    }

    public void setNrAulasExperimentais(Integer nrAulasExperimentais) {
        this.nrAulasExperimentais = nrAulasExperimentais;
    }

    public boolean isForcarMarcar() {
        return forcarMarcar;
    }

    public void setForcarMarcar(boolean forcarMarcar) {
        this.forcarMarcar = forcarMarcar;
    }

    public boolean isValidarHorario() {
        return validarHorario;
    }

    public void setValidarHorario(boolean validarHorario) {
        this.validarHorario = validarHorario;
    }
    
    public Integer getMinutosDesmarcarAntecedencia() {
        return minutosDesmarcarAntecedencia;
    }

    public void setMinutosDesmarcarAntecedencia(Integer minutosDesmarcarAntecedencia) {
        this.minutosDesmarcarAntecedencia = minutosDesmarcarAntecedencia;
    }

    public boolean isProibirComParcelaVencida() {
        return proibirComParcelaVencida;
    }

    public void setProibirComParcelaVencida(boolean proibirComParcelaVencida) {
        this.proibirComParcelaVencida = proibirComParcelaVencida;
    }

    public boolean isProibirMarcarAulaAntesPagamentoPrimeiraParcela() {
        return proibirMarcarAulaAntesPagamentoPrimeiraParcela;
    }

    public void setProibirMarcarAulaAntesPagamentoPrimeiraParcela(boolean proibirMarcarAulaAntesPagamentoPrimeiraParcela) {
        this.proibirMarcarAulaAntesPagamentoPrimeiraParcela = proibirMarcarAulaAntesPagamentoPrimeiraParcela;
    }

    public boolean isPermAlunoMarcarAulaOutraEmpresa() {
        return permAlunoMarcarAulaOutraEmpresa;
    }

    public void setPermAlunoMarcarAulaOutraEmpresa(boolean permAlunoMarcarAulaOutraEmpresa) {
        this.permAlunoMarcarAulaOutraEmpresa = permAlunoMarcarAulaOutraEmpresa;
    }

    public Integer getSpiviSeat() {
        return spiviSeat;
    }

    public void setSpiviSeat(final Integer spiviSeat) {
        this.spiviSeat = spiviSeat;
    }

    public Integer getSpiviEvenID() {
        return spiviEvenID;
    }

    public void setSpiviEvenID(final Integer spiviEvenID) {
        this.spiviEvenID = spiviEvenID;
    }

    public Integer getSpiviClientID() {
        return spiviClientID;
    }

    public void setSpiviClientID(final Integer spiviClientID) {
        this.spiviClientID = spiviClientID;
    }

    public Boolean getSomenteValidar() {
        return somenteValidar;
    }

    public void setSomenteValidar(Boolean somenteValidar) {
        this.somenteValidar = somenteValidar;
    }

    public boolean isControlarFreepass() {
        return controlarFreepass;
    }

    public void setControlarFreepass(boolean controlarFreepass) {
        this.controlarFreepass = controlarFreepass;
    }

    public Boolean getIgnorarTolerancia() {
        return ignorarTolerancia;
    }

    public void setIgnorarTolerancia(Boolean ignorarTolerancia) {
        this.ignorarTolerancia = ignorarTolerancia;
    }

    public String getBookingId() {
        return bookingId;
    }

    public void setBookingId(String bookingId) {
        this.bookingId = bookingId;
    }

    public Integer getAutorizado() {
        return autorizado;
    }

    public void setAutorizado(Integer autorizado) {
        this.autorizado = autorizado;
    }

    public String getJustificativa() { return justificativa; }

    public void setJustificativa(String justificativa) { this.justificativa = justificativa; }

    public Integer getNivel() {
        return nivel;
    }

    public void setNivel(Integer nivel) {
        this.nivel = nivel;
    }

    public Integer getCodigoPassivo() {
        return codigoPassivo;
    }

    public void setCodigoPassivo(Integer codigoPassivo) {
        this.codigoPassivo = codigoPassivo;
    }

    public Boolean getFilaEspera() {
        return filaEspera;
    }

    public void setFilaEspera(Boolean filaEspera) {
        this.filaEspera = filaEspera;
    }

    public Integer getNrValidarVezesModalidade() {
        return nrValidarVezesModalidade;
    }

    public void setNrValidarVezesModalidade(Integer nrValidarVezesModalidade) {
        this.nrValidarVezesModalidade = nrValidarVezesModalidade;
    }
    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getQtdtempobloqueioaluno() {
        return qtdtempobloqueioaluno;
    }

    public void setQtdtempobloqueioaluno(Integer qtdtempobloqueioaluno) {
        this.qtdtempobloqueioaluno = qtdtempobloqueioaluno;
    }

    public Integer getQtdfaltasbloqueioaluno() {
        return qtdfaltasbloqueioaluno;
    }

    public void setQtdfaltasbloqueioaluno(Integer qtdfaltasbloqueioaluno) {
        this.qtdfaltasbloqueioaluno = qtdfaltasbloqueioaluno;
    }


    public Boolean getDescontarCreditoContratoAulaMarcada() {
        if (descontarCreditoContratoAulaMarcada == null) {
            descontarCreditoContratoAulaMarcada = false;
        }
        return descontarCreditoContratoAulaMarcada;
    }

    public void setDescontarCreditoContratoAulaMarcada(Boolean descontarCreditoContratoAulaMarcada) {
        this.descontarCreditoContratoAulaMarcada = descontarCreditoContratoAulaMarcada;
    }

    public Boolean getBloquearGerarReposicaoAulaJaReposta() {
        return bloquearGerarReposicaoAulaJaReposta;
    }

    public void setBloquearGerarReposicaoAulaJaReposta(Boolean bloquearGerarReposicaoAulaJaReposta) {
        this.bloquearGerarReposicaoAulaJaReposta = bloquearGerarReposicaoAulaJaReposta;
    }
}

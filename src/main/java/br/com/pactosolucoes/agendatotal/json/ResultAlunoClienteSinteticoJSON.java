package br.com.pactosolucoes.agendatotal.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.List;

public class ResultAlunoClienteSinteticoJSON extends SuperJSON {

    List<ClienteSintenticoJson> resultCliSintentico;
    List<AgendadoJSON> resultAgendado;

    public ResultAlunoClienteSinteticoJSON() {
    }

    public ResultAlunoClienteSinteticoJSON(List<ClienteSintenticoJson> resultCliSintentico, List<AgendadoJSON> resultAgendado) {
        this.resultCliSintentico = resultCliSintentico;
        this.resultAgendado = resultAgendado;
    }

    public List<ClienteSintenticoJson> getResultCliSintentico() {
        return resultCliSintentico;
    }

    public void setResultCliSintentico(List<ClienteSintenticoJson> resultCliSintentico) {
        this.resultCliSintentico = resultCliSintentico;
    }

    public List<AgendadoJSON> getResultAgendado() {
        return resultAgendado;
    }

    public void setResultAgendado(List<AgendadoJSON> resultAgendado) {
        this.resultAgendado = resultAgendado;
    }
}

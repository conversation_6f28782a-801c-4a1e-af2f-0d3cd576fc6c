package br.com.pactosolucoes.agendatotal.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;

public class AgendaReposicaoJSON extends SuperJSON {

    private Integer codigoPessoa;
    private Integer codigoCliente;
    private Integer codigoContrato = 0;
    private String matricula;
    private Boolean confirmadoReposicao = false;

    public AgendaReposicaoJSON() {

    }

    public AgendaReposicaoJSON(Integer codigoPessoa, Integer codigoCliente, Integer codigoContrato, String matricula, Boolean confirmadoReposicao) {
        this.codigoPessoa = codigoPessoa;
        this.codigoCliente = codigoCliente;
        this.codigoContrato = codigoContrato;
        this.matricula = matricula;
        this.confirmadoReposicao = confirmadoReposicao;
    }

    public Integer getCodigoPessoa() { return codigoPessoa; }

    public void setCodigoPessoa(Integer codigoPessoa) { this.codigoPessoa = codigoPessoa; }

    public Integer getCodigoCliente() { return codigoCliente; }

    public void setCodigoCliente(Integer codigoCliente) { this.codigoCliente = codigoCliente; }

    public Integer getCodigoContrato() { return codigoContrato; }

    public void setCodigoContrato(Integer codigoContrato) { this.codigoContrato = codigoContrato; }

    public String getMatricula() { return matricula; }

    public void setMatricula(String matricula) { this.matricula = matricula; }

    public Boolean getConfirmadoReposicao() { return confirmadoReposicao; }

    public void setConfirmadoReposicao(Boolean confirmadoReposicao) { this.confirmadoReposicao = confirmadoReposicao; }
}

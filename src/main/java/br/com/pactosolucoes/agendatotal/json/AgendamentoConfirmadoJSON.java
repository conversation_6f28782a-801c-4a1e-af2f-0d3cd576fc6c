package br.com.pactosolucoes.agendatotal.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;

/**
 * Created by <PERSON> on 20/12/2016.
 */
public class AgendamentoConfirmadoJSON extends SuperJSON {

    private String idAgendamento;
    private Integer codigoCliente;

    public AgendamentoConfirmadoJSON() {
    }

    public AgendamentoConfirmadoJSON(String idAgendamento, Integer codigoCliente) {
        this.idAgendamento = idAgendamento;
        this.codigoCliente = codigoCliente;
    }

    public String getIdAgendamento() {
        return idAgendamento;
    }

    public void setIdAgendamento(String idAgendamento) {
        this.idAgendamento = idAgendamento;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

}

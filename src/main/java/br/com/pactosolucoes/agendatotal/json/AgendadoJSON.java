/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.agendatotal.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AgendadoJSON extends SuperJSON {

    private Integer codigoPessoa;
    private Integer codigoCliente;
    private Integer codigoContrato;
    private String id_agendamento;
    private String inicio;
    private String fim;
    private String nome;
    private String matricula;
    private String urlFoto;
    private String telefones;
    private Integer saldoCreditoTreino = 0;
    private Boolean usaSaldo = false;
    private Boolean usaTurma = false;
    private Boolean confirmado = false;
    private String situacaoContrato;
    private String situacao;
    private Boolean contratoFuturo = false;
    private List<Integer> modalidadesContratoFuturo = new ArrayList<Integer>();
    private Integer qtdReposicoesFuturas = 0;
    private Integer codContratoFuturo = 0;
    private Integer qtdCreditoFuturo = 0;
    private boolean gymPass = false;
    private boolean diaria = false;
    private boolean totalPass = false;
    private boolean experimental = false;
    private boolean presencaReposicao = false;
    private boolean desmarcado = false;
    private String fotokey;
    private boolean desafio = false;
    private String dataNascimento;
    private String email;
    private String sexo;
    private String cidade;

    private Integer spiviSeat;
    private Integer spiviEventID;
    private Integer spiviClientID;

    public AgendadoJSON() {

    }

    public AgendadoJSON(Integer codigoPessoa, Integer codigoCliente, String nome, String matricula, Integer codigoContrato,
                        String telefones, boolean usasaldo, Integer saldo, Integer qtdReposicoesFuturas) {
        this.codigoPessoa = codigoPessoa;
        this.codigoCliente = codigoCliente;
        this.nome = nome;
        this.matricula = matricula;
        this.codigoContrato = codigoContrato;
        this.telefones = telefones;
        this.usaSaldo = usasaldo;
        this.saldoCreditoTreino = saldo;
        this.qtdReposicoesFuturas = qtdReposicoesFuturas;
    }

    public AgendadoJSON(JSONObject json) throws Exception {
        this.codigoPessoa = json.getInt("codigoPessoa");
        this.codigoCliente = json.getInt("codigoCliente");
        this.nome = json.getString("nome");
        this.matricula = json.getString("matricula");
        this.codigoContrato = json.optInt("codigoContrato");
        this.telefones = json.getString("telefones");
        this.saldoCreditoTreino = json.getInt("saldoCreditoTreino");
        this.usaSaldo = json.getBoolean("validarSaldoCreditoTreino");
        this.contratoFuturo = json.getBoolean("contratoFuturo");

        if (contratoFuturo) {
            try {
                this.codContratoFuturo = json.getInt("codContratoFuturo");
                this.qtdCreditoFuturo = json.getInt("qtdCreditoFuturo");
                for (int i = 0; i < json.getJSONArray("modalidadesContratoFuturo").length(); i++) {
                    this.modalidadesContratoFuturo.add(json.getJSONArray("modalidadesContratoFuturo").getInt(i));
                }
            } catch (Exception e) {
                this.modalidadesContratoFuturo.clear();
            }
        }

        this.qtdReposicoesFuturas = json.getInt("qtdReposicoesFuturas");
        try {
            this.situacao = json.getString("situacao");
        } catch (Exception e) {
            this.situacao = "";
        }
        try {
            this.situacaoContrato = json.getString("situacaoContrato");
        } catch (Exception e) {
            this.situacaoContrato = null;
        }
        try {
            this.confirmado = json.getBoolean("confirmado");
        } catch (Exception ignored) {
        }
        try {
            Integer tipoHorario = json.getInt("tipohorario");
            this.usaTurma = TipoHorarioCreditoTreinoEnum.getTipo(tipoHorario).equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA);
        } catch (Exception e) {
            this.usaTurma = true;
        }
        try {
            this.diaria = json.getBoolean("diaria");
        } catch (Exception ignored) {
        }

        try {
            this.experimental = json.getBoolean("experimental");
        } catch (Exception ignored) {
        }

        try {
            this.presencaReposicao = json.getBoolean("presencareposicao");
        } catch (Exception ignored) {
        }

        try {
            this.desmarcado = json.getBoolean("desmarcado");
        } catch (Exception ignored) {
        }

        try {
            this.dataNascimento = json.getString("dataNascimento");
        } catch (Exception ignored) {
        }
        try {
            this.cidade = json.getString("cidade");
        } catch (Exception ignored) {
        }

        try {
            this.sexo = json.getString("sexo");
        } catch (Exception ignored) {
        }
        try {
            this.spiviClientID = json.optInt("spiviClientID",0);
        } catch (Exception ignored) {
        }
    }


    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getId_agendamento() {
        return id_agendamento;
    }

    public void setId_agendamento(String id_agendamento) {
        this.id_agendamento = id_agendamento;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getTelefones() {
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public Integer getSaldoCreditoTreino() {
        return saldoCreditoTreino;
    }

    public void setSaldoCreditoTreino(Integer saldoCreditoTreino) {
        this.saldoCreditoTreino = saldoCreditoTreino;
    }

    public Boolean getUsaSaldo() {
        return usaSaldo;
    }

    public void setUsaSaldo(Boolean usaSaldo) {
        this.usaSaldo = usaSaldo;
    }

    public Boolean getUsaTurma() {
        return usaTurma;
    }

    public void setUsaTurma(Boolean usaTurma) {
        this.usaTurma = usaTurma;
    }

    public Boolean getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Boolean confirmado) {
        this.confirmado = confirmado;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }


    public Boolean getContratoFuturo() {
        return contratoFuturo;
    }

    public void setContratoFuturo(Boolean contratoFuturo) {
        this.contratoFuturo = contratoFuturo;
    }

    public List<Integer> getModalidadesContratoFuturo() {
        return modalidadesContratoFuturo;
    }

    public void setModalidadesContratoFuturo(List<Integer> modalidadesContratoFuturo) {
        this.modalidadesContratoFuturo = modalidadesContratoFuturo;
    }

    public Integer getQtdReposicoesFuturas() {
        return qtdReposicoesFuturas;
    }

    public void setQtdReposicoesFuturas(Integer qtdReposicoesFuturas) {
        this.qtdReposicoesFuturas = qtdReposicoesFuturas;
    }

    public Integer getCodContratoFuturo() {
        return codContratoFuturo;
    }

    public void setCodContratoFuturo(Integer codContratoFuturo) {
        this.codContratoFuturo = codContratoFuturo;
    }

    public Integer getQtdCreditoFuturo() {
        return qtdCreditoFuturo;
    }

    public void setQtdCreditoFuturo(Integer qtdCreditoFuturo) {
        this.qtdCreditoFuturo = qtdCreditoFuturo;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public boolean isGymPass() {
        return gymPass;
    }

    public void setGymPass(boolean gymPass) {
        this.gymPass = gymPass;
    }

    public boolean isTotalPass() {
        return totalPass;
    }

    public void setTotalPass(boolean totalPass) {
        this.totalPass = totalPass;
    }

    public boolean isDiaria() {
        return diaria;
    }

    public void setDiaria(boolean diaria) {
        this.diaria = diaria;
    }

    public boolean isExperimental() {
        return experimental;
    }

    public void setExperimental(boolean experimental) {
        this.experimental = experimental;
    }

    public boolean getPresencaReposicao() {
        return presencaReposicao;
    }

    public void setPresencaReposicao(boolean presencaReposicao) {
        this.presencaReposicao = presencaReposicao;
    }

     public boolean isDesmarcado() {
        return desmarcado;
    }

    public void setDesmarcado(boolean desmarcado) {
        this.desmarcado = desmarcado;
    }

    public String getFotokey() {
        return fotokey;
    }

    public void setFotokey(String fotokey) {
        this.fotokey = fotokey;
    }

    public Integer getSpiviSeat() {
        return spiviSeat;
    }

    public void setSpiviSeat(final Integer spiviSeat) {
        this.spiviSeat = spiviSeat;
    }

    public Integer getSpiviEventID() {
        return spiviEventID;
    }

    public void setSpiviEventID(final Integer spiviEventID) {
        this.spiviEventID = spiviEventID;
    }

    public Integer getSpiviClientID() {
        return spiviClientID;
    }

    public void setSpiviClientID(final Integer spiviClientID) {
        this.spiviClientID = spiviClientID;
    }

    public boolean isDesafio() {
        return desafio;
    }

    public void setDesafio(boolean desafio) {
        this.desafio = desafio;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }


}

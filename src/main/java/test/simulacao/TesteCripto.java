package test.simulacao;


import negocio.comuns.utilitarias.Criptografia;

public class TesteCripto {

    /**
     * @param aArgs
     */
    public static void main(String[] aArgs) {
        /*
         *
        <PERSON><PERSON> geradas por cliente

        Athletics -> 322c3978a670d51e4f03f7856e59b795
        VidaAtiva -> 3437c357b240be9362232d1a94cf97b9
        Ipanema   -> ce4c5aaa540c74b7167fc23283f7c42b

         *
         * */
        System.out.println("Athletics -> "
                + Criptografia.encryptMD5("Athletics"));
        System.out.println("VidaAtiva -> "
                + Criptografia.encryptMD5("VidaAtiva"));
        System.out.println("Ipanema Sports -> "
                + Criptografia.encryptMD5("Ipanema"));
        System.out.println("MyGym -> "
                + Criptografia.encryptMD5("MyGym"));
        System.out.println("Tchibum -> "
                + Criptografia.encryptMD5("Tchibum"));
        System.out.println("Kletterhaus -> "
                + Criptografia.encryptMD5("Kletterhaus"));


    }
}

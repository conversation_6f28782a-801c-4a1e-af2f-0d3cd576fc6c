package test.simulacao;

import br.com.pactosolucoes.enumeradores.SituacaoContratoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoTextoPadrao;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class ProcessoPropagarContratoHtml {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";

    public static void main(String args[]) throws Exception {

        logGravar = new StringBuilder();

        try {
            Uteis.debug = true;

            Connection con = new Conexao("****************************************************************", "postgres", "pactodb").getConexao();
            nomeBanco = con.getCatalog();

            Integer codigoEmpresa = 1;
            String codigosContratos = "";

            processarContratos(codigoEmpresa, codigosContratos, con);
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(ex.getMessage());
        } finally {
            Uteis.salvarArquivo(nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\PactoJ\\log\\" + File.separator);
        }
    }


    public static void processarContratos(Integer codigoEmpresa, String codigosContratos, Connection con) throws Exception {
        Contrato contratoDAO = new Contrato(con);
        ZillyonWebFacade zwFacadeDAO = new ZillyonWebFacade(con);
        ContratoTextoPadrao contratoTextoPadraoDAO = new ContratoTextoPadrao(con);
        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioRecorrencia = usuarioDAO.getUsuarioRecorrencia();

        verificarUltimoContratoAssinado(codigoEmpresa, codigosContratos, usuarioRecorrencia, con);

        Integer sucesso = 0;
        Integer falha = 0;

        String sql = "SELECT con.codigo, con.primeiroContratoBaseadoRenovacao FROM contrato con \n" +
                "LEFT JOIN contratoassinaturadigital cad ON cad.contrato = con.codigo\n" +
                "WHERE con.responsavelcontrato = " + usuarioDAO.getUsuarioRecorrencia().getCodigo() + " \n" +
                "AND con.situacaocontrato = 'RN' \n" +
                "AND coalesce(con.primeiroContratoBaseadoRenovacao,0) <> 0 \n" +
                "AND cad.codigo IS NULL \n" +
                "AND EXISTS (select c2.codigo from contrato c2 where c2.pessoa= con.pessoa and c2.situacao = 'AT' and c2.situacaocontrato = 'RN' and c2.responsavelcontrato = " + usuarioDAO.getUsuarioRecorrencia().getCodigo() + ") \n";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql += "AND con.empresa = " + codigoEmpresa + " \n";
        }
        if (!UteisValidacao.emptyString(codigosContratos)) {
            sql += "AND con.codigo IN (" + codigosContratos + ")";
        }

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") AS sql", con);
        adicionarLog("--- TOTAL CONTRATOS: " + total);

        int ind = 0;
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {

            adicionarLog(++ind + "/" + total + " Processando contrato: " + rs.getInt("codigo"));

            try {
                con.setAutoCommit(false);

                Integer codigoPrimeiroContrato = rs.getInt("primeiroContratoBaseadoRenovacao");

                ContratoVO contratoAtivo = contratoDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                contratoAtivo.setContratoTextoPadrao(contratoTextoPadraoDAO.consultarContratoTextoPadrao(contratoAtivo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                if (contratoEstaAssinado(codigoPrimeiroContrato, con)) {
                    adicionarLog("\tPropagando assinatura digital do contrato: " + codigoPrimeiroContrato + " para: " + contratoAtivo.getCodigo() + " pessoa: " + contratoAtivo.getPessoa().getNome());
                    zwFacadeDAO.propagarAssinaturaDigital(contratoAtivo, codigoPrimeiroContrato);
                }
                String html = contratoTextoPadraoDAO.consultarHtmlContrato(rs.getInt("primeiroContratoBaseadoRenovacao"), false);
                if (!UteisValidacao.emptyString(html)) {
                    String update = "update contratotextopadrao set contratohtml = ? where codigo = ?";
                    PreparedStatement pstm = con.prepareStatement(update);
                    pstm.setString(1, html);
                    pstm.setInt(2, contratoAtivo.getContratoTextoPadrao().getCodigo());
                    pstm.execute();
                }

                sucesso++;
                con.commit();
            } catch (Exception ex) {
                falha++;
                con.rollback();
                con.setAutoCommit(true);
            } finally {
                con.setAutoCommit(true);
            }
        }
        contratoDAO = null;
        zwFacadeDAO = null;
        contratoTextoPadraoDAO = null;

        adicionarLog("--- TOTAL | " + total);
        adicionarLog("--- TOTAL | SUCESSO | " + sucesso);
        adicionarLog("--- TOTAL | FALHA   | " + falha);
    }

    private static void verificarUltimoContratoAssinado(Integer codigoEmpresa, String codigosContratos, UsuarioVO usuarioRecorrencia, Connection con) throws Exception {
        Contrato contratoDAO = new Contrato(con);

        String sql = "SELECT con.codigo FROM contrato con \n" +
                "WHERE coalesce(con.primeiroContratoBaseadoRenovacao,0) = 0 \n" +
                "AND con.responsavelcontrato = " + usuarioRecorrencia.getCodigo() + " \n" +
                "AND con.situacaocontrato = 'RN' \n";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql += "AND con.empresa = " + codigoEmpresa + " \n";
        }
        if (!UteisValidacao.emptyString(codigosContratos)) {
            sql += "AND con.codigo IN (" + codigosContratos + ") \n";
        }
        sql += "ORDER BY con.codigo \n";

        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", con);

        adicionarLog("Verificando primeiroContratoBaseadoRenovacao..... TOTAL: " + total);

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {
            Integer codigoPrimeiroContrato = contratoDAO.obterCodigoPrimeiroContratoBaseadoRenovacao(rs.getInt("codigo"));
            if (UteisValidacao.emptyNumber(codigoPrimeiroContrato)) {
                adicionarLog("Falha ao obter primeiro contrato baseado renovação para o contrato atual codigo: " + rs.getInt("codigo"));
                continue;
            } else if (!contratoEstaAssinado(codigoPrimeiroContrato, con)) {
                Integer codigoUltimoContratoAssinado;
                if (contratoEstaAssinado(rs.getInt("codigo"), con)) {
                    codigoUltimoContratoAssinado = rs.getInt("codigo");
                } else {
                    codigoUltimoContratoAssinado = obterUltimoContratoRenovacaoAssinado(rs.getInt("codigo"), con);
                    codigoPrimeiroContrato = codigoUltimoContratoAssinado.intValue();
                }
                if (!UteisValidacao.emptyNumber(codigoUltimoContratoAssinado)) {
                    adicionarLog("\tAlterando tipo do ultimo contrato assinado codigo: " + codigoUltimoContratoAssinado + " para Rematricula");
                    alterarSituacaoContratoParaRematricula(codigoUltimoContratoAssinado, usuarioRecorrencia, con);
                }
            }

            if (!UteisValidacao.emptyNumber(codigoPrimeiroContrato)) {
                SuperFacadeJDBC.executarUpdate("update contrato set primeiroContratoBaseadoRenovacao = " + codigoPrimeiroContrato + " \n" +
                        "where codigo = " + rs.getInt("codigo"), con);
            }
        }
        contratoDAO = null;
    }

    private static void alterarSituacaoContratoParaRematricula(Integer codigoContrato, UsuarioVO usuarioRecorrencia, Connection con) throws Exception {
        Contrato contratoDAO = new Contrato(con);
        ContratoVO contratoVO = contratoDAO.consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (contratoVO.getSituacaoContrato().equals(SituacaoContratoEnum.RENOVACAO.getCodigo())) {
            Integer codigoContratoAnterior =  contratoVO.getContratoBaseadoRenovacao();
            // Contrato atual
            SuperFacadeJDBC.executarUpdate("update contrato set situacaocontrato = '" + SituacaoContratoEnum.REMATRICULA.getCodigo() + "',\n" +
                    "contratobaseadorenovacao = 0,\n" +
                    "contratobaseadorematricula = " + codigoContratoAnterior + " \n" +
                    "where codigo = " + codigoContrato, con);
            SuperFacadeJDBC.executarUpdate("UPDATE historicocontrato SET tipohistorico = 'RE'\n" +
                    "WHERE tipohistorico = 'RN'\n" +
                    "AND contrato = " + contratoVO.getCodigo(), con);
            SuperFacadeJDBC.executarUpdate("UPDATE periodoacessocliente SET contratobaseadorenovacao = NULL\n" +
                    "WHERE contratobaseadorenovacao = " + codigoContratoAnterior, con);
            SuperFacadeJDBC.executarUpdate("update contrato set responsavelcontrato = 1 where codigo = " + contratoVO.getCodigo() + " and responsavelcontrato = 3", con);

            // Contrato anterior
            SuperFacadeJDBC.executarUpdate("UPDATE contrato SET contratoresponsavelrenovacaomatricula = 0, \n" +
                    "contratoresponsavelrematriculamatricula = " + contratoVO.getCodigo() + ", \n" +
                    "datarematricularealizada = datarenovarrealizada \n" +
                    "where codigo = " + codigoContratoAnterior, con);
            SuperFacadeJDBC.executarUpdate("UPDATE contrato SET datarenovarrealizada = null \n" +
                    "where codigo = " + codigoContratoAnterior, con);
            SuperFacadeJDBC.executarUpdate("UPDATE historicocontrato SET tipohistorico = 'RE', descricao = 'REMATRICULADO' \n" +
                    "WHERE tipohistorico = 'RN'\n" +
                    "AND contrato = " + codigoContratoAnterior, con);

        }
        contratoDAO = null;
    }

    private static boolean contratoEstaAssinado(Integer codigoContrato, Connection con) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from contratoassinaturadigital where contrato = " + codigoContrato, con)) {
            return rs.next();
        }
    }

    private static Integer obterUltimoContratoRenovacaoAssinado(Integer codigoContrato, Connection con) throws Exception {
        Integer codigoUltimoContratoAssinado = 0;

        String sql = "SELECT con.codigo, con.contratobaseadorenovacao, cad.codigo as contratoassinaturadigital FROM contrato con \n" +
                "LEFT JOIN contratoassinaturadigital cad ON cad.contrato = con.codigo \n" +
                "WHERE con.contratoresponsavelrenovacaomatricula = ? \n";
        Integer limiteContratosVerificar = SuperFacadeJDBC.contar("SELECT COUNT(codigo) FROM contrato WHERE pessoa = (SELECT c.pessoa FROM contrato c WHERE c.codigo = " + codigoContrato + ")" , con);
        Integer contratosVerificados = 0;

        Integer contratoVerificar = codigoContrato.intValue();

        while (contratosVerificados <= limiteContratosVerificar) {
            PreparedStatement pstm = con.prepareStatement(sql);
            pstm.setInt(1, contratoVerificar);
            ResultSet rs = pstm.executeQuery();

            if (rs.next()) {
                if (!UteisValidacao.emptyNumber(rs.getInt("contratoassinaturadigital"))) {
                    codigoUltimoContratoAssinado = rs.getInt("codigo");
                    break;
                } else if (!UteisValidacao.emptyNumber(rs.getInt("contratobaseadorenovacao"))) {
                    contratoVerificar = rs.getInt("contratobaseadorenovacao");
                } else {
                    break;
                }
            } else {
                break;
            }
            contratosVerificados++;
        }

        return codigoUltimoContratoAssinado;
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    public void setLogGravar(StringBuilder logGravar) {
        this.logGravar = logGravar;
    }
}

package test.simulacao;

import br.com.pactosolucoes.enumeradores.TipoPlanoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ReajusteContratoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.IndiceFinanceiroReajustePrecoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.arquitetura.LogInterfaceFacade;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * Created by ulisses on 28/07/2016.
 */
public class ReajusteContratoManualSelfit {


    public static void main(String... args) {

        List<ConexaoOAMD> listaCon = retornarListaConexoesBdLocal();
        // Rodar o processo em produção, basta descomentar a linha abaixo e comentar a linha acima
        //List<ConexaoOAMD> listaCon = retornarListaConexoesBDProducaoSelfit();
        try {
            reajustarContratos(listaCon);

        } catch (Exception ex) {
            ex.printStackTrace();
        }finally {
            for (ConexaoOAMD conexaoOAMD: listaCon){
                try {
                    conexaoOAMD.getConnection().close();
                }catch (Exception e){
                    System.out.println("ERRO AO FECHAR CONEXÃO COM O BD. ERRO: " +e.getMessage());
                }
            }

        }
    }



    public static void reajustarContratos(List<ConexaoOAMD> listaCon)throws Exception{
        System.out.println("INICIO REAJUSTE CONTRATOS em "+ Calendario.hoje());
        for (ConexaoOAMD conexaoOAMD: listaCon){
            System.out.println("INICIO  REAJUSTE CHAVE " + conexaoOAMD.getChave() + " em "+ Calendario.hoje());

            Conexao.guardarConexaoForJ2SE(conexaoOAMD.getChave(), conexaoOAMD.getConnection());
            ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(conexaoOAMD.getConnection());
            List<ContratoReajustado>listaContrato = consultarContratosParaReajustar(conexaoOAMD.getConnection());
            try{
                conexaoOAMD.getConnection().setAutoCommit(false);
                UsuarioVO usuarioVO = FacadeManager.getFacade().getUsuario().getUsuarioRecorrencia();
                for (ContratoReajustado contratoReajustado: listaContrato){
                    boolean reajustouContrato = false;
                    StringBuilder log = new StringBuilder();
                    for (MovParcelaVO movParcelaVO: contratoReajustado.getListaMovParcela()){
                        reajustarParcela(conexaoOAMD.getConnection(),contratoReajustado,movParcelaVO,log);
                    }
                    if (!log.toString().trim().equals("")){
                        gravarLogReajusteContrato(contratoReajustado.getCodigoContrato(), contratoReajustado.getCodigoPessoa(),usuarioVO, log.toString());
                        incluirReajusteContrato(contratoReajustado, usuarioVO);
                    }

                }

                conexaoOAMD.getConnection().commit();
            }catch (Exception ex){
                conexaoOAMD.getConnection().rollback();
                ex.printStackTrace();
            }finally {
                conexaoOAMD.getConnection().setAutoCommit(true);
            }
            System.out.println("FIM  REAJUSTE CHAVE " + conexaoOAMD.getChave() + " em "+ Calendario.hoje());
        }
        System.out.println("FIM REAJUSTE CONTRATOS em "+ Calendario.hoje());
   }

    private static void reajustarParcela(Connection connection, ContratoReajustado contratoReajustado, MovParcelaVO movParcelaVO, StringBuilder log)throws Exception{
        Calendar dataBase = Calendario.getInstance();
        dataBase.setTime(contratoReajustado.getDataInicioContrato());
        IndiceFinanceiroReajustePrecoVO indiceFinanceiroVO = getFacade().getIndiceFinanceiroReajustePreco().consultarIndiceFinanceiroPeriodo((new DecimalFormat("00")).format(dataBase.get(Calendar.MONTH) + 1), String.valueOf(dataBase.get(Calendar.YEAR)),true, TipoPlanoEnum.PLANO_RECORRENCIA, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Double valorParcela = movParcelaVO.getValorParcela();
        Double valorParcelaReajustada = null;
        if (indiceFinanceiroVO != null){
            indiceFinanceiroVO.setValorMensalidadeOriginal(valorParcela);
            contratoReajustado.setIndiceFinanceiroReajustePrecoVO(indiceFinanceiroVO);
            Double valorReajuste = (valorParcela *  indiceFinanceiroVO.getPercentualAcumulado()) /100;
            valorParcelaReajustada = valorParcela + valorReajuste;
            indiceFinanceiroVO.setValorMensalidadeReajustada(valorParcelaReajustada);
            log.append("Codigo contrato:").append(contratoReajustado.getCodigoContrato());
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            log.append(" \n\rCodigo parcela:").append(movParcelaVO.getCodigo()).append(" Vencimento:").append(sdf.format(movParcelaVO.getDataVencimento()));
            log.append(" \n\rvalor original: ").append(String.format( "%.2f", movParcelaVO.getValorParcela()));
            log.append(" \n\rpercentual reajuste:").append(String.format( "%.2f", indiceFinanceiroVO.getPercentualAcumulado())).append(" %");
            log.append(" \n\rvalor reajustado: ").append(String.format( "%.2f", valorParcelaReajustada));
            reajustarContratoRenegociandoParcela(movParcelaVO, valorParcelaReajustada, valorReajuste);
        }

    }

    private static void incluirReajusteContrato(ContratoReajustado contratoReajustado, UsuarioVO usuarioVO)throws Exception{
        ReajusteContratoVO reajusteContratoVO = new ReajusteContratoVO();
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(contratoReajustado.getCodigoContrato());
        reajusteContratoVO.setContratoVO(contratoVO);
        reajusteContratoVO.setUsuarioVO(usuarioVO);
        reajusteContratoVO.setIndiceFinanceiroReajustePrecoVO(contratoReajustado.getIndiceFinanceiroReajustePrecoVO());
        reajusteContratoVO.setPercentualIndice(contratoReajustado.getIndiceFinanceiroReajustePrecoVO().getPercentualAcumulado());
        reajusteContratoVO.setValorMensalAnterior(contratoReajustado.getIndiceFinanceiroReajustePrecoVO().getValorMensalidadeOriginal());
        reajusteContratoVO.setValorMensalNovo(contratoReajustado.getIndiceFinanceiroReajustePrecoVO().getValorMensalidadeReajustada());
        getFacade().getReajusteContrato().incluir(reajusteContratoVO);
    }

    private static void gravarLogReajusteContrato(Integer codigoContrato, Integer codigoPessoa, UsuarioVO usuarioVO, String descricaoLog) throws Exception {
        LogVO logVO = new LogVO();
        logVO.setOperacao("REAJUSTE DE PREÇO CONTRATO");
        logVO.setChavePrimaria(String.valueOf(codigoContrato));
        logVO.setNomeEntidade("CONTRATO");
        logVO.setNomeEntidadeDescricao("Contrato");
        logVO.setOperacao("REAJUSTE PREÇO - CONTRATO");
        logVO.setResponsavelAlteracao(usuarioVO.getNome());
        logVO.setNomeCampo("TODOS");
        logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        logVO.setValorCampoAnterior("");
        logVO.setValorCampoAlterado("--------------------------------------\n\r");
        StringBuilder campoAlterado = new StringBuilder();
        campoAlterado.append(descricaoLog);
        logVO.setValorCampoAlterado(campoAlterado.toString());

        LogInterfaceFacade logFacade = FacadeManager.getFacade().getLog();
        if (logVO != null) {
            logVO.setPessoa(codigoPessoa);
            logFacade.incluirSemCommit(logVO);
        }
    }

    private static void reajustarContratoRenegociandoParcela(MovParcelaVO movParcelaVO, Double valorReajustado, Double valorRejuste)throws Exception{
        //movParcelaVO.setValorParcela(valorReajustado);
        movParcelaVO.setMovProdutoParcelaVOs(FacadeManager.getFacade().getMovProdutoParcela().consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        movParcelaVO.setParcelaEscolhida(true);

        List<MovParcelaVO> parcelasRenegociar = new ArrayList<MovParcelaVO>();
        parcelasRenegociar.add(movParcelaVO);

        // parcela acrescimo
        MovParcelaVO parcelaRenegociar = new MovParcelaVO();
        parcelaRenegociar.setDescricao("ACRESCIMO");
        parcelaRenegociar.setValorParcela(valorRejuste);
        parcelaRenegociar.setDataVencimento(Calendario.hoje());
        parcelasRenegociar.add(parcelaRenegociar);

        MovParcelaVO parcelaAcrescimo = new MovParcelaVO();
        parcelaAcrescimo.setDescricao("");
        parcelaAcrescimo.setValorParcela(valorRejuste);
        parcelaAcrescimo.setDataVencimento(Calendario.hoje());

        // Parcelas Renegociadas
        List<MovParcelaVO> parcelasRenegociadas = new ArrayList<MovParcelaVO>();
        MovParcelaVO novaParcela = (MovParcelaVO) movParcelaVO.getClone(true);
        novaParcela.setDescricao("PARCELA RENEGOCIADA");
        novaParcela.setValorParcela(valorReajustado);
        novaParcela.setDataRegistro(Calendario.hoje());
        parcelasRenegociadas.add(novaParcela);
        parcelaAcrescimo.setDescricao("RAJUSTE CONTRATO");
        UsuarioVO usuarioVO = FacadeManager.getFacade().getUsuario().getUsuarioRecorrencia();
        FacadeManager.getFacade().getMovParcela().renegociarParcelas(parcelasRenegociar, parcelasRenegociadas, null, parcelaAcrescimo, "TX", false, null, null, 0.0, false, usuarioVO, true,false, true, null, null);

    }

    public static List<ContratoReajustado> consultarContratosParaReajustar(Connection connection)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select reajuste.codigo as codigoReajuste, c.codigo as codigoContrato, c.*  \n");
        sql.append("from contrato c \n");
        sql.append("inner join contratorecorrencia cr on cr.contrato = c.codigo \n");
        sql.append("left join reajusteContrato reajuste on reajuste.contrato = c.codigo \n");
        sql.append("where  c.situacaoContrato = 'RN' and reajuste.codigo is null \n");
        Statement st = connection.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<ContratoReajustado> lista = new ArrayList<ContratoReajustado>();
        while (rs.next()){
            ContratoReajustado contratoReajustado = (new ReajusteContratoManualSelfit()).new ContratoReajustado();
            contratoReajustado.setCodigoContrato(rs.getInt("codigoContrato"));
            contratoReajustado.setCodigoPessoa(rs.getInt("pessoa"));
            contratoReajustado.setDataInicioContrato(rs.getDate("vigenciade"));
            contratoReajustado.setListaMovParcela(consultarMovParcelaEmAberto(connection, rs.getInt("codigoContrato")));
            lista.add(contratoReajustado);
        }
        return lista;
    }

    private static List<MovParcelaVO> consultarMovParcelaEmAberto(Connection connection, Integer codigoContrato)throws Exception{
        StringBuilder sql = new StringBuilder();
        Date dataBase = Calendario.hoje();
        Date dataComparar = br.com.pacto.priv.utils.Uteis.somarDias(dataBase, -4);
        dataBase = br.com.pacto.priv.utils.Uteis.somarDias(dataBase, 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sql.append("select distinct parc.*  \n");
        sql.append("from movParcela parc \n");
        sql.append("inner join movProdutoParcela movProdParc on movProdParc.movParcela = parc.codigo \n");
        sql.append("inner join movProduto movProd on movProd.codigo = movProdParc.movProduto \n");
        sql.append("inner join produto prod on prod.codigo = movProd.produto \n");
        sql.append("where parc.contrato = ").append(codigoContrato).append(" \n");
        sql.append("   and parc.situacao = 'EA' \n");
        sql.append(" and prod.tipoproduto = 'PM' \n");
        sql.append(" and parc.valorParcela > 0 \n");
        // O débito que estiver em remessa e estiver no prazo de três dias, então isentar o débito. Observação: O retorno da remessa acontece no prazo máximo de três dias.
        sql.append(" and NOT EXISTS (SELECT 1\n" +
                "FROM remessaitem i\n" +
                "INNER JOIN remessa r ON r.codigo = i.remessa\n" +
                "WHERE coalesce(r.situacaoRemessa,0) in(1,4)\n" +
                "AND r.dataRegistro between '" +sdf.format(dataComparar) + "' and '" + sdf.format(dataBase) + "'\n" +
                "AND i.movparcela = parc.codigo)");
        sql.append(" order by dataVencimento \n");
        Statement st = connection.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<MovParcelaVO> lista = new ArrayList<MovParcelaVO>();
        while (rs.next()){
            MovParcelaVO movParcelaVO = MovParcela.montarDadosBasico(rs);
            lista.add(movParcelaVO);
        }
        return lista;
    }


    public static List<ConexaoOAMD> retornarListaConexoesBdLocal(){
        List<ConexaoOAMD> lista = new ArrayList<ConexaoOAMD>();
        try{
            Connection conOAMD = obterConexao("OAMD");
            Connection conEpitacio = obterConexao("bdzillyonselfitparalela");
            if (true){
                ConexaoOAMD conexaoOAMD = (new ReajusteContratoManualSelfit()).new ConexaoOAMD();
                conexaoOAMD.setConnection(conEpitacio);
                conexaoOAMD.setChave("gleidsonapresentacao");
                lista.add(conexaoOAMD);
                return lista;
            }

           /* StringBuilder sql = new StringBuilder();
            sql.append("select emp.chave, \"nomeBD\" \n");
            sql.append("from empresa emp \n");
            sql.append("inner join empresaFinanceiro ef on ef.chaveZw = emp.chave \n");
            sql.append("inner join redeempresa re on re.id = ef.redeEmpresa_id \n");
            sql.append("where upper(re.nome) = 'REDESELFIT' ");
            //sql.append(" and \"nomeBD\" = 'bdzillyonselfitpontaverde' ");
            Statement st = conOAMD.createStatement();
            ResultSet rs = st.executeQuery(sql.toString());
            while (rs.next()){
               ConexaoOAMD conexaoOAMD = (new ReajusteContratoManualSelfit()).new ConexaoOAMD();
               conexaoOAMD.setConnection(obterConexao(rs.getString("nomeBD")));
               conexaoOAMD.setChave(rs.getString("chave"));
               lista.add(conexaoOAMD);
            }
            conOAMD.close();*/
        }catch (Exception e){
            System.out.println("ERRO AO CRIAR CONEXÃO COM O BD. ERRO: " +e.getMessage());
        }
        return lista;
    }

    public static List<ConexaoOAMD> retornarListaConexoesBDProducaoSelfit(){
        List<ConexaoOAMD> lista = new ArrayList<ConexaoOAMD>();
        try{
            Connection conOAMD = obterConexaoProducaoSelfit("OAMD");
            StringBuilder sql = new StringBuilder();
            sql.append("select emp.chave, \"nomeBD\" \n");
            sql.append("from empresa emp \n");
            sql.append("inner join empresaFinanceiro ef on ef.chaveZw = emp.chave \n");
            sql.append("inner join redeempresa re on re.id = ef.redeEmpresa_id \n");
            sql.append("where upper(re.nome) = 'REDESELFIT' ");
            //sql.append(" and \"nomeBD\" = 'bdzillyonselfitpontaverde' ");
            Statement st = conOAMD.createStatement();
            ResultSet rs = st.executeQuery(sql.toString());
            while (rs.next()){
                ConexaoOAMD conexaoOAMD = (new ReajusteContratoManualSelfit()).new ConexaoOAMD();
                conexaoOAMD.setConnection(obterConexaoProducaoSelfit(rs.getString("nomeBD")));
                conexaoOAMD.setChave(rs.getString("chave"));
                lista.add(conexaoOAMD);
            }
            conOAMD.close();
        }catch (Exception e){
            System.out.println("ERRO AO CRIAR CONEXÃO COM O BD. ERRO: " +e.getMessage());
        }
        return lista;
    }



    public static Connection obterConexao(String nomeBD)throws Exception{
        //String hostBD = "zw-rds-9.cmgq5kfs7ytf.sa-east-1.rds.amazonaws.com";
        //String porta = "5432";
        //String userBD = "zillyonweb";
        //String passwordBD = "pactodb2020";

        String hostBD = "localhost";
        String porta = "5432";
        String userBD = "postgres";
        String passwordBD = "pactodb";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }

    public static Connection obterConexaoProducaoSelfit(String nomeBD)throws Exception{
        String hostBD = "zw-rds-9.cmgq5kfs7ytf.sa-east-1.rds.amazonaws.com";
        String porta = "5432";
        String userBD = "zillyonweb";
        String passwordBD = "pactodb2020";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }




    public static Integer retornarCodigoCidade(CidadeBDCEP cidadeBDCEP, Connection conZillyon)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select c.* \n");
        sql.append("from cidade c \n");
        sql.append("inner join estado est on est.codigo = c.estado \n");
        sql.append("where est.sigla = '").append(cidadeBDCEP.getUf()).append("' \n");
        sql.append("and ((c.nome = '").append(cidadeBDCEP.getNomeCidade().toUpperCase()).append("') or (c.nomesemacento = '").append(Uteis.retirarAcentuacao(cidadeBDCEP.getNomeCidade())).append("')) \n");
        Statement st = conZillyon.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return rs.getInt("codigo");
        }
        return null;
    }


    public class ConexaoOAMD {

        private Connection connection;
        private String chave;

        public Connection getConnection() {
            return connection;
        }

        public void setConnection(Connection connection) {
            this.connection = connection;
        }

        public String getChave() {
            return chave;
        }

        public void setChave(String chave) {
            this.chave = chave;
        }
    }

    public class ContratoReajustado{
        private Integer codigoContrato;
        private Date dataInicioContrato;
        private Integer codigoPessoa;
        private IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO;
        private List<MovParcelaVO> listaMovParcela;

        public Integer getCodigoContrato() {
            return codigoContrato;
        }

        public void setCodigoContrato(Integer codigoContrato) {
            this.codigoContrato = codigoContrato;
        }

        public List<MovParcelaVO> getListaMovParcela() {
            return listaMovParcela;
        }

        public void setListaMovParcela(List<MovParcelaVO> listaMovParcela) {
            this.listaMovParcela = listaMovParcela;
        }

        public Integer getCodigoPessoa() {
            return codigoPessoa;
        }

        public void setCodigoPessoa(Integer codigoPessoa) {
            this.codigoPessoa = codigoPessoa;
        }

        public Date getDataInicioContrato() {
            return dataInicioContrato;
        }

        public void setDataInicioContrato(Date dataInicioContrato) {
            this.dataInicioContrato = dataInicioContrato;
        }

        public IndiceFinanceiroReajustePrecoVO getIndiceFinanceiroReajustePrecoVO() {
            return indiceFinanceiroReajustePrecoVO;
        }

        public void setIndiceFinanceiroReajustePrecoVO(IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO) {
            this.indiceFinanceiroReajustePrecoVO = indiceFinanceiroReajustePrecoVO;
        }
    }
}

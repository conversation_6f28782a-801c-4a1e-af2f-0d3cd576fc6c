/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package test.simulacao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Endereco;
import negocio.facade.jdbc.basico.Telefone;
import org.jdom.Element;

/**
 *
 * <AUTHOR>
 */
public class PreencherDadosPessoaImportacao {

    public static String retirarNull(String valor) {
        if (valor.equals("null") || valor.equals("")) {
            return null;
        } else {
            return valor;
        }


    }

    public static String retirarNulo(Element e, String campo) {
        try {
            return retirarNull(e.getAttributeValue(campo));
        } catch (Exception ie) {
            return null;
        }


    }

    public static void main(String[] args) {
        try {
            Connection con1 = DriverManager.getConnection("*****************************************", "zillyonweb", "pactodb");
            preencher(con1);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public static void preencher(Connection con) throws Exception {
        ResultSet rs = con.prepareStatement("SELECT codigo, idexterno FROM pessoa WHERE idexterno is not null").executeQuery();
        Endereco enderecoDAO = new Endereco(con);
        Telefone telefoneDAO = new Telefone(con);
        Email emailDAO = new Email(con);


        Map<Integer, Integer> pessoas = new HashMap<Integer, Integer>();
        while (rs.next()) {
            pessoas.put(rs.getInt("idexterno"), rs.getInt("codigo"));
        }
        con.prepareStatement("DELETE FROM email WHERE pessoa in (select codigo FROM pessoa WHERE idexterno is not null)").execute();
        con.prepareStatement("DELETE FROM telefone WHERE pessoa in (select codigo FROM pessoa WHERE idexterno is not null)").execute();
        con.prepareStatement("DELETE FROM endereco WHERE pessoa in (select codigo FROM pessoa WHERE idexterno is not null)").execute();

        LeitorXML leitor = new LeitorXML();
        List<Element> registros = leitor.lerXML("D:\\Clientes.xml");

        for (Element e : registros) {
            Integer idpessoa = Integer.valueOf(retirarNulo(e, "id_pessoa"));
            Integer codigoPessoa = pessoas.get(idpessoa);
            if(codigoPessoa == null){
               continue;
            }
            List<TelefoneVO> telefones = new ArrayList<TelefoneVO>();
            EmailVO emailvo = new EmailVO();

            EnderecoVO endereco = montarEndereco(e);
            endereco.setPessoa(codigoPessoa);
            enderecoDAO.incluir(endereco);

            //---------telefones
            //celular
            String cel = retirarNulo(e, "fonecelular");
            if (cel != null && !cel.equals("") && cel.length() > 5) {
                telefones.add(montarTelefone(cel, "CE"));
            }
            //comercial
            String com = retirarNulo(e, "fonecomercial");
            if (com != null && !com.equals("") && com.length() > 5) {
                telefones.add(montarTelefone(com, "CO"));
            }
            //residencial
            String res = retirarNulo(e, "foneresidencial");
            if (res != null && !res.equals("") && res.length() > 5) {
                telefones.add(montarTelefone(res, "RE"));
            }
            //fax
            String fax = retirarNulo(e, "fonefax");
            if (fax != null && !fax.equals("") && fax.length() > 5) {
                telefones.add(montarTelefone(fax, "FA"));
            }

            for(TelefoneVO obj : telefones){
                obj.setPessoa(codigoPessoa);
                obj.realizarUpperCaseDados();
                String sql = "INSERT INTO Telefone( numero, tipoTelefone, pessoa, descricao ) VALUES ( ?, ?, ?,? )";
                PreparedStatement sqlInserir = con.prepareStatement(sql);
                sqlInserir.setString(1, obj.getNumero());
                sqlInserir.setString(2, obj.getTipoTelefone());
                if (obj.getPessoa().intValue() != 0) {
                    sqlInserir.setInt(3, obj.getPessoa().intValue());
                } else {
                    sqlInserir.setNull(3, 0);
                }
                sqlInserir.setString(4, obj.getDescricao());
                sqlInserir.execute();
            }

            //email
            String email = retirarNulo(e, "email");
            if (email != null && UteisValidacao.validaEmail(email) && !email.equals("")) {
                emailvo = montarEmail(formatarTamanho(email, 45), codigoPessoa);
                emailDAO.incluir(emailvo);
            }
        }
    }

    private static EmailVO montarEmail(String email, Integer codigoPessoa) {
        EmailVO emailVO = new EmailVO();
        emailVO.setEmail(email);
        emailVO.setPessoa(codigoPessoa);
        emailVO.setEmailCorrespondencia(Boolean.FALSE);
        return emailVO;
    }

    public static String formatarTamanho(String str, int tamanho) {
        if (str == null) {
            return null;
        } else if (str.length() > tamanho) {
            String strNovo = "";
            for (int i = 0; i < tamanho; i++) {
                strNovo = strNovo + str.charAt(i);
            }
            return strNovo;
        } else {
            return str;
        }
    }

    private static TelefoneVO montarTelefone(String numero, String tipo) {
        TelefoneVO telefone = new TelefoneVO();
        telefone.setTipoTelefone(tipo);
        telefone.setNumero(formatarTamanho(numero, 15));
        return telefone;
    }

    private static EnderecoVO montarEndereco(Element e) {
        EnderecoVO endereco = new EnderecoVO();
        String end = retirarNulo(e, "endereco");
        if(UteisValidacao.emptyString(end)){
            endereco.setEndereco(" - ");
        }else{
            endereco.setEndereco(formatarTamanho(retirarNulo(e, "endereco"), 40));
        }
        

        String numero = retirarNulo(e, "endnumero");
        if (numero == null) {
            endereco.setNumero("0");
        } else {
            endereco.setNumero(numero);
        }


        endereco.setCep(retirarNulo(e, "endcep"));
        endereco.setBairro(retirarNulo(e, "endbairro"));
        endereco.setComplemento(retirarNulo(e, "complemento"));


        return endereco;
    }
}

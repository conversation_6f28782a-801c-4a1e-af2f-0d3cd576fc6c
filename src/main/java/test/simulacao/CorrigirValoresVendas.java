/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package test.simulacao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.logging.Logger;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class CorrigirValoresVendas {

    public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("********************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con1);
            corrigir(con1);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    
    public static void corrigir(Connection con) throws Exception{
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from vendaavulsa  where id_movimento is not null", con);
        Integer contar = SuperFacadeJDBC.contar("select count(codigo) from vendaavulsa  where id_movimento is not null", con);
        int nr = 1;
        while(rs.next()){
            System.out.println(nr+++"/"+contar);
            Double valorVenda = 0.0;

            Integer codigoVenda = rs.getInt("codigo");
            ResultSet rsMovParcela = SuperFacadeJDBC.criarConsulta("select * from movparcela  where vendaavulsa = "+codigoVenda, con);
            while(rsMovParcela.next()){

                Integer codigoMovParcela = rsMovParcela.getInt("codigo");
                ResultSet rsMovProduto =
                        SuperFacadeJDBC.criarConsulta("select * from movproduto  where codigo  "
                        + "in (select movproduto from movprodutoparcela  where movparcela = "+codigoMovParcela+")", con);
                while(rsMovProduto.next()){
                       Integer codigoMovProd = rsMovProduto.getInt("codigo");
                       Double desconto = rsMovProduto.getDouble("valordesconto");
                       Double precounitario = rsMovProduto.getDouble("precounitario");
                       Integer quantidade = rsMovProduto.getInt("quantidade");
                       Double totalfinal = (precounitario * quantidade) - desconto;
                       valorVenda += totalfinal;
                       SuperFacadeJDBC.executarConsulta("UPDATE movproduto SET totalfinal = "+totalfinal+" where codigo = "+codigoMovProd, con );

                }
                SuperFacadeJDBC.executarConsulta("UPDATE movparcela SET valorparcela = "+valorVenda+" where codigo = "+codigoMovParcela, con );
                SuperFacadeJDBC.executarConsulta("UPDATE vendaavulsa SET valortotal = "+valorVenda+" where codigo = "+codigoVenda, con );
            }
        }
    }

}

package test.simulacao;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.text.SimpleDateFormat;

/**
 * Created by ulisses on 14/02/2017.
 */
public class TesteAcesso {

    public static void main(String args[]) throws Exception {
        String chave = "bdteste";
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(chave);
        Conexao.guardarConexaoForJ2SE(chave, acessoControle.getCon());
        acessoControle.setValidacaoAcessoOffline(true);
        //validarAcesso(acessoControle,"5003070004", 4,18,1);
        gravarAcesso(chave,"09/12/2019 11:08", acessoControle,353,1,4,1,18);
    }

    public static void validarAcesso(AcessoControle acessoControle, String codigoAcessoCliente, Integer codigoEmpresa, Integer codigoLocalAcesso, Integer numeroTerminal){
        try{
//            SituacaoAcessoEnum situacaoAcessoEnum = acessoControle.tentarAcesso(codigoAcessoCliente, DirecaoAcessoEnum.DA_ENTRADA,codigoEmpresa,codigoLocalAcesso,String.valueOf(numeroTerminal),false, MeioIdentificacaoEnum.MATRICULATECLADOCOMPUTADOR, false);
//            System.out.println(situacaoAcessoEnum.getDescricao());
        }catch (Exception e){
            System.out.println("Erro ao tentar acesso. Erro:" + e.getMessage());
        }
    }

    public static void gravarAcesso(String chave, String dataAcesso, AcessoControle acessoControle,Integer codigoCliente, Integer codigoUsuario, Integer codigoEmpresa, Integer codigoLocalAcesso, Integer numeroTerminal){
        try{
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
            acessoControle.registrarAcesso(dateFormat.parse(dataAcesso),"5",codigoCliente, SituacaoAcessoEnum.RV_LIBACESSOAUTORIZADO, DirecaoAcessoEnum.DA_ENTRADA,codigoLocalAcesso,numeroTerminal,codigoUsuario, MeioIdentificacaoEnum.MATRICULATECLADOCOMPUTADOR,chave,codigoEmpresa, "", null, null);
            System.out.println("GRAVOU ACESSO COM SUCESSO.");
        }catch (Exception e){
            System.out.println("Erro ao tentar gravar acesso. Erro:" + e.getMessage());
        }
    }
}

package test.simulacao;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

import java.io.DataInputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.Criptografia;
import controle.arquitetura.SuperControle;
import java.io.InputStream;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class TesteRegex {

    public static void main(String args[]) throws Exception {
        // Obtém a entrada
        //analisaFormatoTags();
        //analisaChavesClientes();
        //replaceImagensHtmlPorCID();
        //testaPagamentoDigital();
        //testaSMS();
        //extraiParamsURL();
        //testaLeituraArquivoInputStream();
        System.out.println("wallerMACIEL^~123".replaceAll("[^0-9]", ""));

        //String regex = "<[^>]+>[^<]*</[^>]+>";
        /*String regex = "<[^>]+>[^<]*</[^>]+>";
        Pattern pattern = Pattern.compile(regex);

        StringBuilder sb = new StringBuilder();
        sb.append("<ResultadoAPC>");
        sb.append("<TransacaoAprovada>False</TransacaoAprovada>");
        sb.append("<ResultadoSolicitacaoAprovacao>Não Autorizado - 93 - 93ALEATORIO</ResultadoSolicitacaoAprovacao>");
        sb.append("<CodigoAutorizacao></CodigoAutorizacao>");
        sb.append("<Transacao>73435133900915</Transacao>");
        sb.append("<CartaoMascarado>Cartao Nao Informado</CartaoMascarado>");
        sb.append("<NumeroDocumento></NumeroDocumento>");
        sb.append("<ComprovanteAdministradora></ComprovanteAdministradora>");
        sb.append("<NacionalidadeEmissor></NacionalidadeEmissor>");
        sb.append("<ResultadoAVS></ResultadoAVS>");
        sb.append("<EnderecoAVS>");
        sb.append("<Endereco></Endereco>");
        sb.append("<Numero></Numero>");
        sb.append("<Complemento></Complemento>");
        sb.append("<Cep></Cep>");
        sb.append("</EnderecoAVS>");
        sb.append("<NumeroSequencialUnico></NumeroSequencialUnico>");
        sb.append("<NumeroControleRede></NumeroControleRede>");
        sb.append("<CodigoResultadoAprovacao>93</CodigoResultadoAprovacao>");
        sb.append("<CodigoResultadoAVS></CodigoResultadoAVS>");
        sb.append("</ResultadoAPC>");

        Matcher matcher = pattern.matcher(sb.toString());
        // Mostra as similaridades
        while (matcher.find()) {
            String trecho = matcher.group();
            String valor = trecho.substring(trecho.indexOf(">") + 1, trecho.indexOf("</"));
            System.out.printf("Encontrado: \"%s\" de %d à %d.%n",
                    valor, matcher.start(), matcher.end());
        }*/

        /*String text = "QuantidadeParcelas=1, TransacaoAnterior=73435133563913, ValorDocumento=49.900002, NumeroDocumento=, Moeda=BRL, Bandeira=MASTERCARD, EnderecoIPComprador=**************";
        Map<String, String> map = new LinkedHashMap<String, String>();
        for (String keyValue : text.split(" *, *")) {
            String[] pairs = keyValue.split(" *= *", 2);
            map.put(pairs[0], pairs.length == 1 ? "" : pairs[1]);
        }
        System.out.println(map.toString());*/

    }

    private static void analisaChavesClientes() {
        String source = "ZillyonWeb-3437c357b240be9362232d1a94cf97b9\n"
                + "ZillyonWebCE.key\n"
                + "ZillyonWebTeste";

        String regexTag = ".*key";

        Pattern pattern = Pattern.compile(regexTag);

        Matcher matcher = pattern.matcher(source);

        // Mostra as similaridades
        while (matcher.find()) {
            System.out.printf("Encontrado: \"%s\" de %d à %d.%n",
                    matcher.group(), matcher.start(), matcher.end());
        }


    }

    private static void analisaFormatoTags() {
        String source = "[(60){}tipoFormaPagamento_MovPagamento,"
                + "(60){}valor_MovPagamento,(20){}banco_Cheque, "
                + "(20){}agencia_Cheque, (20){}numero_Cheque]";

        String nomeSubTag = "agencia_Cheque";

        String regexSubTag = Pattern.quote("(") + "[\\d]*" + Pattern.quote(")")
                + Pattern.quote("{") + "*" + Pattern.quote("}") + nomeSubTag;


        String regexTag = Pattern.quote("[") + ".*?" + Pattern.quote("]");


        Pattern pattern = Pattern.compile(regexTag);

        Matcher matcher = pattern.matcher(source);

        // Mostra as similaridades
        while (matcher.find()) {
            System.out.printf("Encontrado: \"%s\" de %d à %d.%n",
                    matcher.group(), matcher.start(), matcher.end());
        }

        pattern = Pattern.compile(regexSubTag);

        matcher = pattern.matcher(source);

        // Mostra as similaridades
        while (matcher.find()) {
            System.out.printf("Encontrado: \"%s\" de %d à %d.%n",
                    matcher.group(), matcher.start(), matcher.end());
        }
    }

    private static void replaceImagensHtmlPorCID() throws Exception {

        String html = "<img id=\"_x0000_i1025\" src=\"imagensCRM/email/cima_default1.png\" "
                + "width=\"626\" height=\"77\" />";

        UteisEmail uteisMail = new UteisEmail();
        uteisMail.gerarListaImagensHtmlParaEmail(html);

    }

    private static void extraiParamsURL() {
        String teste = "";
        try {
            teste = Criptografia.encrypt("validade=" + negocio.comuns.utilitarias.Calendario.hoje().getTime()
                    + "&contrato=12395", SuperControle.Crypt_KEY,
                    SuperControle.Crypt_ALGORITM);
        } catch (BadPaddingException ex) {
            Logger.getLogger(TesteRegex.class.getName()).log(Level.SEVERE, null, ex);
        } catch (NoSuchPaddingException ex) {
            Logger.getLogger(TesteRegex.class.getName()).log(Level.SEVERE, null, ex);
        } catch (IllegalBlockSizeException ex) {
            Logger.getLogger(TesteRegex.class.getName()).log(Level.SEVERE, null, ex);
        } catch (InvalidKeyException ex) {
            Logger.getLogger(TesteRegex.class.getName()).log(Level.SEVERE, null, ex);
        } catch (NoSuchAlgorithmException ex) {
            Logger.getLogger(TesteRegex.class.getName()).log(Level.SEVERE, null, ex);
        } catch (InvalidAlgorithmParameterException ex) {
            Logger.getLogger(TesteRegex.class.getName()).log(Level.SEVERE, null, ex);
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(TesteRegex.class.getName()).log(Level.SEVERE, null, ex);
        } catch (InvalidKeySpecException ex) {
            Logger.getLogger(TesteRegex.class.getName()).log(Level.SEVERE, null, ex);
        }

        teste = (Criptografia.decrypt(teste,
                SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM));
        Pattern imagem = Pattern.compile("validade=?\\s*\\w*\\s*");
        Matcher matcher = imagem.matcher(teste);
        while (matcher.find()) {
            String validade = (teste.substring(matcher.start(),
                    matcher.end()));
            validade = validade.substring(validade.indexOf("=") + 1);
            Date data = negocio.comuns.utilitarias.Calendario.hoje();
            data.setTime(new Long(validade));
            System.out.println(data);
        }
        imagem = Pattern.compile("contrato=?\\s*\\w*\\s*");
        matcher = imagem.matcher(teste);
        while (matcher.find()) {
            String contrato = (teste.substring(matcher.start(),
                    matcher.end()));
            contrato = contrato.substring(contrato.indexOf("=") + 1);
        }
    }

    private static void testaPagamentoDigital() throws UnsupportedEncodingException, MalformedURLException, IOException {
        String enderecoPost = "https://www.pagamentodigital.com.br/checkout/verify/";
        /*
        token=195590D82BF202B24D413&transacao=3269730&status=Transac%E3o%20em%20Andamento&valor_original=10.00&valor_loja=9.71
         */

        String params = "?token=" + URLEncoder.encode("195590D82BF202B24D413", "UTF-8")
                + "&transacao=" + URLEncoder.encode("3269730", "UTF-8")
                + "&status=" + URLEncoder.encode("Transação Cancelada", "UTF-8")
                + "&valor_original=" + URLEncoder.encode("10.00", "UTF-8")
                + "&valor_loja=" + URLEncoder.encode("9.71", "UTF-8");

        URL url = new URL(enderecoPost + params);

        URLConnection connection = url.openConnection();
        ((HttpURLConnection) connection).setRequestMethod("POST");

        connection.setDoOutput(true);
        connection.setDoInput(true); //para esperar resposta
        connection.setUseCaches(false); //desabilitar cache

        /*connection.setRequestProperty("token", token);
        connection.setRequestProperty("transacao", id_transacao);
        connection.setRequestProperty("status", status);
        connection.setRequestProperty("valor_original", valor_original);
        connection.setRequestProperty("valor_loja", valor_loja);*/


        PrintWriter output = new PrintWriter(
                new OutputStreamWriter(connection.getOutputStream()));

        output.flush();
        output.close();

        DataInputStream inStream = new DataInputStream(connection.getInputStream());

        // pega  a resposta

        String buffer;
        while ((buffer = inStream.readLine()) != null) {
            System.out.println(buffer);
        }


        inStream.close();
    }

    private static void testaLeituraArquivoInputStream() throws IOException {
        InputStream in = TesteRegex.class.getResourceAsStream("ZILLYON.sql");
        String resultado = Uteis.convertStreamToString(in);
        System.out.println(resultado);
    }
}

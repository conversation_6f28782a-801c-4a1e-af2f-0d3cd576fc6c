package test.simulacao;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;

public class ProcessoSincronizarClientesRedeEmpresa {

    private String chave;
    private Integer codigoEmpresa;
    private Connection con;
    private Connection conFranqueadora;
    private Empresa empresaDAO;
    private Cliente clienteDAO;

    ProcessoSincronizarClientesRedeEmpresa(Connection con, Connection conFranqueadora, String chave, Integer codigoEmpresa) throws Exception {
        this.con = con;
        this.conFranqueadora = conFranqueadora;
        Conexao.guardarConexaoForJ2SE(this.con);
        this.chave = chave;
        this.codigoEmpresa = codigoEmpresa;
        this.empresaDAO = new Empresa(this.con);
        this.clienteDAO = new Cliente(this.con);
    }

    public static void main(String[] args) throws Exception {

        Integer redeEmpresaId = 467;
        String urlAdmCoreFranqueadora = "";
        String tokenAdmCoreFranqueadora = "";

        Connection conOAMD = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");

        String sql = "SELECT\n" +
                " r.chavefranqueadora,\n" +
                " r.codigounidadefranqueadora,\n" +
                " r.sincronizarclientesnafranqueadora,\n" +
                " ef.empresazw,\n" +
                " e.chave,\n" +
                " e.identificadorempresa,\n" +
                " e.\"hostBD\",\n" +
                " e.\"nomeBD\",\n" +
                " e.porta,\n" +
                " e.\"passwordBD\",\n" +
                " e.\"userBD\", \n" +
                " efran.\"hostBD\" as hostBD_franqueadora,\n" +
                " efran.\"nomeBD\" as nomeBD_franqueadora,\n" +
                " efran.porta as porta_franqueadora,\n" +
                " efran.\"passwordBD\" as passwordBD_franqueadora,\n" +
                " efran.\"userBD\" as userBD_franqueadora \n" +
                "FROM redeempresa r \n" +
                " INNER JOIN empresafinanceiro ef ON ef.redeempresa_id = r.id \n" +
                " INNER JOIN empresa e ON e.chave = ef.chavezw \n" +
                " INNER JOIN empresa efran ON efran.chave = r.chavefranqueadora \n" +
                "WHERE 1 = 1\n" +
                "AND e.chave <> r.chavefranqueadora\n" +
                "AND e.usoteste IS FALSE \n" +
                "AND e.ativa IS TRUE \n" +
                "AND r.id = " + redeEmpresaId;

        ResultSet rs = conOAMD.createStatement().executeQuery(sql);

        int atual = 0;
        int total = SuperFacadeJDBC.contar("select count(s.*) from (" + sql + ") as s", conOAMD);

        while(rs.next()) {
            Integer codigoEmpresa = rs.getInt("empresazw");
            String chave = rs.getString("chave");

            String urlCon = String.format("jdbc:postgresql://%s:%d/%s", rs.getString("hostBD"), rs.getInt("porta"), rs.getString("nomeBD"));
            Connection con = DriverManager.getConnection(urlCon, rs.getString("userBD"), rs.getString("passwordBD"));
            String urlConFranqueadora = String.format("jdbc:postgresql://%s:%d/%s", rs.getString("hostBD_franqueadora"), rs.getInt("porta_franqueadora"), rs.getString("nomeBD_franqueadora"));
            Connection conFranqueadora = DriverManager.getConnection(urlConFranqueadora, rs.getString("userBD_franqueadora"), rs.getString("passwordBD_franqueadora"));

            Uteis.logarDebug(String.format("%d\\%d - Processando clientes da chave %s - %s", ++atual, total, chave, rs.getString("identificadorempresa")));

            ProcessoSincronizarClientesRedeEmpresa processo = new ProcessoSincronizarClientesRedeEmpresa(con, conFranqueadora, chave, codigoEmpresa);
            processo.sincronizarClientesRedeEmpresa();
            processo = null;
        }

    }

    private void sincronizarClientesRedeEmpresa() throws Exception {
        EmpresaVO empresa = this.empresaDAO.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        String sql = "SELECT cli.codigo, cli.codigomatricula, pes.nome, pes.cfp as cpf FROM cliente cli \n" +
                " INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n" +
                "WHERE cli.empresa =  " + codigoEmpresa + " \n" +
                "AND coalesce(pes.cfp,'') <> '' \n" +
                "AND cli.dataSincronizacaoFranqueadora IS NULL \n";

        int atual = 0;
        int total = SuperFacadeJDBC.contar("select count(s.*) from (" + sql + ") as s", con);

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);

        while (rs.next()) {
            Uteis.logarDebug(String.format("[%s] %d\\%d - Sincronizando cliente %d - %s ", con.getCatalog(), ++atual, total, rs.getInt("codigomatricula"), rs.getString("nome")));

            String cpf = Uteis.tirarCaracteres(rs.getString("cpf"), true);
            if (cpf.length() != 11) {
                continue;
            }
            if (UteisValidacao.emptyString(rs.getString("nome"))) {
                continue;
            }
            String sqlInserir = "INSERT INTO clienteredeempresa(codigomatricula, cpf, nome, codigoempresa, chaveempresa, nomeempresa, datasincronizacao) \n" +
                    "VALUES (?,?,?,?,?,?,?);";
            PreparedStatement pstm = conFranqueadora.prepareStatement(sqlInserir);
            pstm.setInt(1, rs.getInt("codigomatricula"));
            pstm.setString(2, cpf);
            pstm.setString(3, rs.getString("nome"));
            pstm.setInt(4, empresa.getCodigo());
            pstm.setString(5, chave);
            pstm.setString(6, empresa.getNome());
            Date dataSinc = Calendario.hoje();
            pstm.setTimestamp(7, Uteis.getDataJDBCTimestamp(dataSinc));
            pstm.execute();

            clienteDAO.atualizarDataSincronizacaoFranqueadora(rs.getInt("codigo"), dataSinc);
        }
    }

}

package test.simulacao;

import br.com.pactosolucoes.atualizadb.processo.RefazerVinculoMovProdutoParcelaContratos;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;

public class AjustarMovProdutosContratosCanceladosImportacao {


    public static void main(String[] args) throws Exception {

        Connection con = DriverManager.getConnection("******************************************", "postgres", "pactodb");
        Integer codigoEmpresa = 1;
        String codigosContratos = "";

        ajustarMovProdutosContratosCanceladosImportacao(codigoEmpresa, codigosContratos, con);

    }

    public static void ajustarMovProdutosContratosCanceladosImportacao(Integer codigoEmpresa, String codigosContratos, Connection con) throws Exception {
        try {
            String sql = "select \n" +
                    "\tmpar.codigo as movparcela,\n" +
                    "\tmpar.contrato,\n" +
                    "\tmpar.valorparcela,\n" +
                    "\tmpar.situacao as situacao_movparcela,\n" +
                    "\tmpro.codigo as movproduto,\n" +
                    "\tmpro.totalfinal,\n" +
                    "\tmpro.situacao as situacao_movproduto,\n" +
                    "\tarray_to_string(array(select mpro2.codigo from movproduto mpro2 where mpro2.contrato = mpro.contrato and mpro2.codigo <> mpro.codigo order by mpro2.codigo),',','') as movprodutos_ajustar, \n" +
                    "\tarray_to_string(array(select rpg.codigo from recibopagamento rpg where rpg.contrato = mpro.contrato),',','') as recibos_ajustar \n" +
                    "from movparcela mpar\n" +
                    "\tinner join contrato con on con.codigo = mpar.contrato \n" +
                    "\tinner join movprodutoparcela mpp on mpp.movparcela = mpar.codigo \n" +
                    "\tinner join movproduto mpro on mpro.codigo = mpp.movproduto \n" +
                    "where 1 = 1\n" +
                    "and mpar.empresa = " + codigoEmpresa + " \n" +
                    "and coalesce(con.id_externo,0) > 0\n" +
                    "and con.situacao = 'CA'\n" +
                    "and exists (select cp.codigo from contratooperacao cp where cp.tipooperacao = 'CA' and cp.descricaocalculo ilike '%OPERA%O IMPORTADA%')\n" +
                    "and exists(select count(mpp2.movproduto) from movprodutoparcela mpp2 where mpp2.movparcela = mpar.codigo having count(mpp2.movproduto)=1)\n" +
                    "and trunc(mpar.valorparcela::numeric,2) < trunc(mpro.totalfinal)\n" +
                    "and mpar.situacao = 'PG'\n" +
                    "and mpro.situacao = 'CA'\n";

            if (!UteisValidacao.emptyString(codigosContratos)) {
                sql += "and con.codigo in (" + codigosContratos + ")\n";
            }

            int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", con);
            int atual = 0;

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                Integer codigoMovProduto = rs.getInt("movproduto");
                Integer codigoContrato = rs.getInt("contrato");
                Double valorParcela = rs.getDouble("valorparcela");
                Double totalFinalMovProduto = rs.getDouble("totalfinal");
                String codigosMovProdutosAjustar = rs.getString("movprodutos_ajustar");
                String codigosRecibosAjustar = rs.getString("recibos_ajustar");

                try {
                    con.setAutoCommit(false);

                    adicionarLog(String.format("%d\\%d - Ajustando movprodutos do contrato: %d", ++atual, total, codigoContrato));

                    SuperFacadeJDBC.executarConsulta("update movproduto set situacao = 'PG', \n" +
                            "totalfinal = " + valorParcela + ", \n" +
                            " precounitario = " + valorParcela + ",\n" +
                            " valorfaturado = " + valorParcela + " \n" +
                            "where codigo = " + codigoMovProduto, con);

                    if (!UteisValidacao.emptyString(codigosMovProdutosAjustar)) {
                        int qtdMovProdutos = codigosMovProdutosAjustar.split(",").length;
                        double diferenca = totalFinalMovProduto - valorParcela;
                        double valorAdicionar = diferenca / qtdMovProdutos;
                        double valorAdicionarArredondado = Uteis.arredondarForcando2CasasDecimais(valorAdicionar);
                        double residuo = valorAdicionarArredondado - valorAdicionar;

                        SuperFacadeJDBC.executarConsulta("update movproduto set totalfinal = totalfinal + " + valorAdicionarArredondado + ", \n" +
                                " precounitario = precounitario + " + valorAdicionarArredondado + ",\n" +
                                " valorfaturado = valorfaturado + " + valorAdicionarArredondado + " \n" +
                                "where codigo in (" + codigosMovProdutosAjustar + ")", con);

                        if (residuo != 0.0) {
                            String codigoUltimoMovProduto = codigosMovProdutosAjustar.split(",")[codigosMovProdutosAjustar.split(",").length - 1];
                            SuperFacadeJDBC.executarConsulta("update movproduto set totalfinal = totalfinal + (" + residuo + "), \n" +
                                    " precounitario = precounitario + (" + residuo + "),\n" +
                                    " valorfaturado = valorfaturado + (" + residuo + ") \n" +
                                    "where codigo = " + codigoUltimoMovProduto, con);
                        }
                    }
                    con.commit();
                } catch (Exception e) {
                    con.rollback();
                } finally {
                    con.setAutoCommit(true);
                }

                RefazerVinculoMovProdutoParcelaContratos.refazerMovProdutoParcela(con, codigoContrato);
                if (!UteisValidacao.emptyString(codigosRecibosAjustar)) {
                    for (String codigoRecibo : codigosRecibosAjustar.split(",")) {
                        ProdutosPagosServico.setarProdutosPagos(con, Integer.parseInt(codigoRecibo));
                    }
                }
            }
        } catch (Exception e) {
            adicionarLog(e.getMessage());
        }
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.getData(new Date(), "dd/MM/yyyy HH:mm:mm:sss") + " --> " + msg;
        System.out.println(s);
    }
}

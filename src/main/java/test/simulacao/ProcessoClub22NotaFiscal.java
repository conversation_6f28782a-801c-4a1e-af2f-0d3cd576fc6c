package test.simulacao;

import negocio.comuns.financeiro.ItemGestaoNotasTO;
import negocio.comuns.nfe.FiltroNFeTO;
import negocio.comuns.nfe.NotaFiscalConsumidorNFCeVO;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.GestaoNotas;
import negocio.facade.jdbc.nfe.NotaFiscalDeServico;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ProcessoClub22NotaFiscal {

    public static void main(String[] args) {
        GestaoNotas gestaoNotasDAO = null;
        try {
            Uteis.debug = true;
            //Esse processo pega as NFCe emitidas de um período e coloca como NFSE emitida.
            //processo para a Club 22
            //by Luiz Felipe

            Connection con = new Conexao("***************************************", "postgres", "pactodb").getConexao();

            Integer empresa = 1;
            Integer idEmpresaNotaFiscal = 10352;
            Date dtInicial = Uteis.getDate("01/06/2020", "dd/MM/yyyy");
            Date dtFinal = Uteis.getDate("30/06/2020", "dd/MM/yyyy");


            Set<Integer> listaIdNFCeReenviadasNaoAutorizadas = obterListaIdNFCeReenviadasNaoAutorizadas(idEmpresaNotaFiscal, dtInicial, dtFinal);

            gestaoNotasDAO = new GestaoNotas(con);
            List<ItemGestaoNotasTO> listaGestao = gestaoNotasDAO.consultarFaturamentoCaixa(empresa, dtInicial, dtFinal, null, false, false, true, null);
            String identificador = ("PROCESSO - " + Calendario.hoje().getTime());
            Uteis.logarDebug("#### Identificador: " + identificador);

            int i = 0;
            for (ItemGestaoNotasTO itemTO : listaGestao) {
                if (itemTO.getNfseemitida() &&
                        !UteisValidacao.emptyNumber(itemTO.getReciboPagamentoVO().getCodigo()) &&
                        !UteisValidacao.emptyNumber(itemTO.getId_NFCe())) {
                    if (listaIdNFCeReenviadasNaoAutorizadas.contains(itemTO.getId_NFCe())) {
                        Uteis.logarDebug(++i + " - Recibo " + itemTO.getReciboPagamentoVO().getCodigo() + " | IdNFCe " + itemTO.getId_NFCe() + " - Não marcar como emitido - Está na lista de Não autorizado ou Reenviado");
                    } else {
                        Uteis.logarDebug(++i + " - Recibo " + itemTO.getReciboPagamentoVO().getCodigo() + " | IdNFCe " + itemTO.getId_NFCe() + " - Marcar como emitido - Não está na lista de Não autorizado ou Reenviado");
                        SuperFacadeJDBC.executarUpdate("insert into nfseemitida(recibopagamento,nrnotamanual) values(" + itemTO.getReciboPagamentoVO().getCodigo() + ", '" + identificador + "');", con);
                    }
                }
            }

            Uteis.logarDebug("#################################");
            Uteis.logarDebug("##### Select Itens Marcados #####");
            Uteis.logarDebug("select * from nfseemitida where nrnotamanual ilike '" + identificador + "';");
            Uteis.logarDebug("#################################");
            Uteis.logarDebug("##### Delete Itens Marcados #####");
            Uteis.logarDebug("delete from nfseemitida where nrnotamanual ilike '" + identificador + "';");
            Uteis.logarDebug("#################################");
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            gestaoNotasDAO = null;
        }
    }

    private static Set<Integer> obterListaIdNFCeReenviadasNaoAutorizadas(Integer idEmpresaNotaFiscal, Date dtInicial, Date dtFinal) throws Exception {
        Connection conNFSe = getConexaoNotas();
        NotaFiscalDeServico nfseDAO = new NotaFiscalDeServico(conNFSe);
        FiltroNFeTO filtroTO = new FiltroNFeTO();
        filtroTO.setDataEmissao_inicio(dtInicial);
        filtroTO.setDataEmissao_fim(dtFinal);
        List<NotaFiscalConsumidorNFCeVO> listaNFCe = nfseDAO.listarNFCe(idEmpresaNotaFiscal, filtroTO, "");
        Set<Integer> lista = new HashSet<>();
        for (NotaFiscalConsumidorNFCeVO obj : listaNFCe) {
            if (obj.getStatus().equalsIgnoreCase(StatusNotaEnum.NAO_AUTORIZADO.getDescricao())) {
                verificarReenvio(lista, obj.getId_NFCe(), conNFSe);
                lista.add(obj.getId_NFCe());
            }
        }
        return lista;
    }

    private static void verificarReenvio(Set<Integer> lista, Integer idNFCe, Connection conNFSe) throws SQLException {
        PreparedStatement sqlConsulta = conNFSe.prepareStatement("select id_nfce from nfce where idreenvio = " + idNFCe);
        ResultSet rs = sqlConsulta.executeQuery();
        if (rs.next()) {
            Integer idConsulta = rs.getInt("id_nfce");
            lista.add(idConsulta);
            verificarReenvio(lista, idConsulta, conNFSe);
        }
    }

    private static Connection getConexaoNotas() throws Exception {
        String driver = "net.sourceforge.jtds.jdbc.Driver";
        String conexao = "jdbc:jtds:sqlserver:";
        Class.forName(driver).newInstance();
        return DriverManager.getConnection(conexao + "//186.202.37.151:1470/DBNFSe", "sa", "pactodb");
    }

}

package test.simulacao;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProcessoDeletarParcelasDuplicadasImportacaoPolisystem {

    private static StringBuilder logGravar;
    private static String nomeBanco;

    public static void main(String args[]) throws Exception {

        logGravar = new StringBuilder();

        try {
            Uteis.debug = true;

            Connection conBDZwDeletarParcelas = new Conexao("*************************************************************************", "postgres", "pactodb").getConexao();
            Connection conBDBackupReimportado = new Conexao("**************************************************************************************", "postgres", "pactodb").getConexao();
            Integer codigoEmpresa = 1;
            nomeBanco = conBDZwDeletarParcelas.getCatalog();

            String matriculasSeparadasPorVirgula = ""; // Opcional

            deletarParcelasDuplicadasImportacao(codigoEmpresa, matriculasSeparadasPorVirgula, conBDZwDeletarParcelas, conBDBackupReimportado);
        } catch (Exception ex) {
            adicionarLog(ex.getMessage());
        } finally {
            Uteis.salvarArquivo(nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\PactoJ\\log\\" + File.separator);
        }
    }


    private static void deletarParcelasDuplicadasImportacao(Integer codigoEmpresa, String matriculasSeparadasPorVirgula, Connection conBDZwDeletarParcelas, Connection conBDBackupReimportado) throws Exception {
        StringBuilder sqlParcelas = new StringBuilder();
        sqlParcelas.append("select \n");
        sqlParcelas.append("cli.codigomatricula, pes.nome,con.id_externo,mpar.contrato, mpar.dataregistro, mpar.datavencimento, mpar.valorparcela, count(mpar.codigo) as qtd \n");
        sqlParcelas.append("from movparcela mpar \n");
        sqlParcelas.append("\tinner join pessoa pes on pes.codigo = mpar.pessoa\n");
        sqlParcelas.append("\tinner join cliente cli on cli.pessoa = pes.codigo\n");
        sqlParcelas.append("\tinner join contrato con on con.codigo = mpar.contrato\n");
        sqlParcelas.append("where 1 = 1\n");
        if (!matriculasSeparadasPorVirgula.isEmpty()) {
            sqlParcelas.append("and cli.codigomatricula in (").append(matriculasSeparadasPorVirgula).append(")\n");
        }
        sqlParcelas.append("and con.empresa = ").append(codigoEmpresa).append("\n");
        sqlParcelas.append("and con.id_externo > 0\n");
        sqlParcelas.append("and con.situacao != 'CA'\n");
//        sqlParcelas.append("and not exists(select mpar2.codigo from movparcela mpar2 where mpar2.contrato = con.codigo and mpar2.situacao in ('CA', 'RN')) \n");
        sqlParcelas.append("group by cli.codigomatricula, pes.nome,con.id_externo,mpar.contrato, mpar.dataregistro, mpar.datavencimento, mpar.valorparcela having count(mpar.codigo) > 1\n");
        sqlParcelas.append("order by cli.codigomatricula,con.id_externo, mpar.datavencimento ");

        Integer total = SuperFacadeJDBC.contar("select count(*) from (" + sqlParcelas.toString() + ") as sql", conBDZwDeletarParcelas);
        adicionarLog("--- TOTAL PARCELAS: " + total);

        List<Integer> idsExternosContratosAfetados = new ArrayList<>();
        int ind = 0;
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlParcelas.toString(), conBDZwDeletarParcelas);
        while (rs.next()) {
            try {
                Integer id_externo = rs.getInt("id_externo");
                Date dataVencimento = rs.getDate("datavencimento");
                Double valorParcela = rs.getDouble("valorparcela");
                Integer qtdParcelas = rs.getInt("qtd");
                Integer codigoMatricula = rs.getInt("codigomatricula");
                String nome = rs.getString("nome");

                if(!idsExternosContratosAfetados.contains(id_externo)) {
                    idsExternosContratosAfetados.add(id_externo);
                }

                // Verificar quantas parcelas existem no backup reimportado
                StringBuilder sqlParcelasPorIdExterno = new StringBuilder();
                sqlParcelasPorIdExterno.append("select count(mpar.codigo) as qtd from movparcela mpar\n");
                sqlParcelasPorIdExterno.append("\tinner join contrato con on con.codigo = mpar.contrato\n");
                sqlParcelasPorIdExterno.append("where con.id_externo = ").append(id_externo).append("\n");
                sqlParcelasPorIdExterno.append("and con.empresa = ").append(codigoEmpresa).append("\n");
                sqlParcelasPorIdExterno.append("and mpar.datavencimento::date = '").append(Uteis.getData(dataVencimento, "bd")).append("'\n");
                sqlParcelasPorIdExterno.append("and trunc(mpar.valorparcela::numeric, 2) = trunc(").append(valorParcela).append("::numeric, 2) \n");
                sqlParcelasPorIdExterno.append("group by con.id_externo, mpar.contrato, mpar.dataregistro, mpar.datavencimento, mpar.valorparcela");

                ResultSet rsParcelasBackup = SuperFacadeJDBC.criarConsulta(sqlParcelasPorIdExterno.toString(), conBDBackupReimportado);

                if (rsParcelasBackup.next()) {
                    int qtdParcelasBackup = rsParcelasBackup.getInt("qtd");
                    if (qtdParcelas > qtdParcelasBackup) {
                        int qtdParcelasDeletar = qtdParcelas - qtdParcelasBackup;
                        // consultar e deletar parcelas em aberto importadas duplicadas, que não foram pagas ou renegociadas pelo sistema
                        StringBuilder sqlConsultarParcelasDeletar = new StringBuilder();
                        sqlConsultarParcelasDeletar.append("select \n");
                        sqlConsultarParcelasDeletar.append("\tmpar.codigo, mpar.descricao, mpar.situacao, mpar.valorparcela, mpar.datavencimento, con.id_externo\n");
                        sqlConsultarParcelasDeletar.append("from movparcela mpar\n");
                        sqlConsultarParcelasDeletar.append("\tinner join contrato con on con.codigo = mpar.contrato\n");
                        sqlConsultarParcelasDeletar.append("where con.id_externo = ").append(id_externo).append("\n");
                        sqlConsultarParcelasDeletar.append("and con.empresa = ").append(codigoEmpresa).append("\n");
                        sqlConsultarParcelasDeletar.append("and mpar.datavencimento::date = '").append(Uteis.getData(dataVencimento, "bd")).append("'\n");
                        sqlConsultarParcelasDeletar.append("and trunc(mpar.valorparcela::numeric, 2) = trunc(").append(valorParcela).append("::numeric, 2) \n");
                        sqlConsultarParcelasDeletar.append("and not exists (select pmp.codigo from pagamentomovparcela pmp inner join movpagamento mpg on mpg.codigo = pmp.movpagamento where pmp.movparcela = mpar.codigo and mpg.responsavelpagamento != 2)");
                        sqlConsultarParcelasDeletar.append("order by mpar.situacao");

                        ResultSet rsConsultarParcelasDeletar = SuperFacadeJDBC.criarConsulta(sqlConsultarParcelasDeletar.toString(), conBDZwDeletarParcelas);

                        int countParcelasDeletas = 0;
                        while (rsConsultarParcelasDeletar.next()) {
                            if (countParcelasDeletas < qtdParcelasDeletar) {
                                Integer codigoParcela = rsConsultarParcelasDeletar.getInt("codigo");
                                String descricao = rsConsultarParcelasDeletar.getString("descricao");

                                String sqlDeletarMovProduto = "delete from movproduto where codigo = (select mpp.movproduto from movprodutoparcela mpp where mpp.movparcela = "
                                        + codigoParcela + ");";
                                String sqlDeletarParcela = "delete from movparcela where codigo = " + codigoParcela;


                                adicionarLog(++ind + "/" + total + " | Deletando movparcela: " + codigoParcela + " - " + descricao + " do aluno MAT: " + codigoMatricula + " - " + nome);
                                SuperFacadeJDBC.executarUpdate(sqlDeletarMovProduto, conBDZwDeletarParcelas);
                                SuperFacadeJDBC.executarUpdate(sqlDeletarParcela, conBDZwDeletarParcelas);
                                countParcelasDeletas++;
                            } else {
                                break;
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        for (Integer idExterno: idsExternosContratosAfetados) {
            ajustarDescricaoMovParcelas(idExterno, conBDZwDeletarParcelas);
            ajustarDescricaoCompetenciaMovProdutos(idExterno, conBDZwDeletarParcelas);
            ajustarValoresContrato(idExterno, conBDZwDeletarParcelas);
        }
    }

    private static void ajustarDescricaoMovParcelas(Integer idExterno, Connection con) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("mpar.codigo, mpar.descricao \n");
            sql.append("from movparcela mpar\n");
            sql.append("inner join contrato con on con.codigo = mpar.contrato \n");
            sql.append("where con.id_externo = " + idExterno);
            sql.append("and mpar.situacao in ('PG', 'EA')");
            sql.append("order by mpar.datavencimento");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            int numeroParcela = 1;
            while (rs.next()) {
                Integer codigo = rs.getInt("codigo");
                String descricao = rs.getString("descricao");
                String novaDescricao = "PARCELA " + numeroParcela;
                String sqlDescricaoMovParcela = "update movparcela set descricao = '" + novaDescricao + "' where codigo = " + codigo;

                adicionarLog("Ajustando descrição da movparcela: " + codigo + " - " + descricao + " para " + novaDescricao);

                SuperFacadeJDBC.executarUpdate(sqlDescricaoMovParcela, con);
                numeroParcela++;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void ajustarDescricaoCompetenciaMovProdutos(Integer idExterno, Connection con) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("mpro.codigo,mpro.descricao, mpro.datalancamento, mpro.anoreferencia, mpro.mesreferencia, con.vigenciade\n");
            sql.append("from movproduto mpro \n");
            sql.append("inner join contrato con on con.codigo = mpro.contrato \n");
            sql.append("inner join produto pro on pro.codigo = mpro.produto \n");
            sql.append("where con.id_externo = ").append(idExterno).append(" \n");
            sql.append("and pro.tipoproduto = 'PM' \n");
            sql.append("order by mpro.codigo");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            int mesSomarDataLancamento = 0;
            while(rs.next()) {
                Integer codigoMovProduto = rs.getInt("codigo");
                String descricao = rs.getString("descricao");

                Date contratoVigenciaDe = rs.getDate("vigenciade");
                Date dataReferencia =Uteis.somarMeses(contratoVigenciaDe, mesSomarDataLancamento);

                String anoReferencia = Uteis.getData(dataReferencia, "bd").split("-")[0];
                String mesReferencia = Uteis.getData(dataReferencia, "bd").split("-")[1] + "/" + anoReferencia;
                String novaDescricao = descricao.split(" - ")[0] + " - " + mesReferencia;

                Date dataInicioVigencia = dataReferencia;
                Date dataFinalVigencia = Uteis.somarMeses(dataInicioVigencia, 1);

                StringBuilder sqlUpdateMovProduto = new StringBuilder();
                sqlUpdateMovProduto.append("update movproduto set datainiciovigencia = '").append(Uteis.getData(dataInicioVigencia, "bd")).append("',");
                sqlUpdateMovProduto.append("datafinalvigencia = '").append(Uteis.getData(dataFinalVigencia, "bd")).append("',");
                sqlUpdateMovProduto.append("mesreferencia = '").append(mesReferencia).append("',");
                sqlUpdateMovProduto.append("anoreferencia = ").append(anoReferencia).append(",");
                sqlUpdateMovProduto.append("descricao = '").append(novaDescricao).append("'");
                sqlUpdateMovProduto.append(" where codigo = ").append(codigoMovProduto);

                SuperFacadeJDBC.executarUpdate(sqlUpdateMovProduto.toString(), con);

                adicionarLog("Ajustando descrição do movproduto: " + codigoMovProduto + " - " + descricao + " para: " + novaDescricao);

                mesSomarDataLancamento++;
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void ajustarValoresContrato(Integer idExterno, Connection con) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("update contrato con set valorfinal = ");
            sql.append("(select sum(mpro.totalfinal) from movproduto mpro inner join produto pro on pro.codigo = mpro.produto where mpro.contrato = con.codigo), ");
            sql.append("valorbasecalculo = (select sum(mpro.totalfinal) from movproduto mpro inner join produto pro on pro.codigo = mpro.produto where mpro.contrato = con.codigo and pro.tipoproduto = 'PM')");
            sql.append(" where con.id_externo = ").append(idExterno);

            SuperFacadeJDBC.executarUpdate(sql.toString(), con);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    public void setLogGravar(StringBuilder logGravar) {
        this.logGravar = logGravar;
    }
}

package test.simulacao;

import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 28/07/2016.
 */
public class CorrecoesSelfit {


    public static void main(String... args) {

        List<ConexaoOAMD> listaCon = retornarListaConexoes();
        try {
//            corrigirSinteticoDW(listaCon);
            verificarClientesCreditoComSituacaoSinteticoErrada();


        } catch (Exception ex) {
            ex.printStackTrace();
        }finally {
            for (ConexaoOAMD conexaoOAMD: listaCon){
                try {
                    conexaoOAMD.getConnection().close();
                }catch (Exception e){
                    System.out.println("ERRO AO FECHAR CONEXÃO COM O BD. ERRO: " +e.getMessage());
                }
            }

        }
    }

    public static void verificarClientesCreditoComSituacaoSinteticoErrada(){
        try{
            Connection con = obterConexao("bdzillyoncemedfit");
            Statement st = con.createStatement();
            ResultSet rs = st.executeQuery("select * from situacaoclientesinteticodw where validarSaldoCreditoTreino = true");
            StringBuilder sql = new StringBuilder();
            Integer codigoPessoa = 0;
            while (rs.next()){
                sql.delete(0, sql.length());
                codigoPessoa = rs.getInt("codigoPessoa");
                sql.append("select vendaCreditoTreino \n");
                sql.append("from contrato \n");
                sql.append("where pessoa = ").append(codigoPessoa);
                sql.append(" and vigenciaAteAjustada >= current_date ");
                Statement stContrato = con.createStatement();
                ResultSet rsContrato = stContrato.executeQuery(sql.toString());
                boolean temContratoCreditoAtivo = false;
                boolean temContratoAtivo = false;
                while (rsContrato.next()){
                    temContratoAtivo = true;
                    if (rsContrato.getBoolean("vendaCreditoTreino")){
                        temContratoCreditoAtivo = true;
                        break;
                    }
                }
                if ((temContratoAtivo) && (!temContratoCreditoAtivo)){
                    System.out.println("PESSOA COM SITUACAO SINTETICO ERRADA: " + codigoPessoa);
                }

            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public static void corrigirSinteticoDW(List<ConexaoOAMD> listaCon){
        try{
            System.out.println("INICIO CORRECAO SINTETICO em "+ Calendario.hoje());
            for (ConexaoOAMD conexaoOAMD: listaCon){
                System.out.println("INICIO  CORRECAO CHAVE " + conexaoOAMD.getChave() + " em "+ Calendario.hoje());
                criarCamposNecessarioSintetico(conexaoOAMD.getConnection());
                Conexao.guardarConexaoForJ2SE(conexaoOAMD.getChave(), conexaoOAMD.getConnection());
                ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(conexaoOAMD.getConnection());
                List<ClienteVO> listaCliente = consultarClienteComSinteticoErrado(conexaoOAMD.getConnection());
                for (ClienteVO clienteVO: listaCliente){
                    zillyonWebFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                }
                System.out.println("FIM  CORRECAO CHAVE " + conexaoOAMD.getChave() + " em "+ Calendario.hoje());
            }
            excluirCamposCriadosTemporariamente(listaCon);
            System.out.println("FIM CORRECAO SINTETICO em "+ Calendario.hoje());

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void excluirCamposCriadosTemporariamente(List<ConexaoOAMD> listaCon){
        try{
            System.out.println("INICIO  em "+ Calendario.hoje());
            for (ConexaoOAMD conexaoOAMD: listaCon){
                excluirCamposNecessarioSintetico(conexaoOAMD.getConnection());
            }
            System.out.println("FIM em "+ Calendario.hoje());

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void excluirCamposNecessarioSintetico(Connection connection){
        try {
            try{
                Statement st = connection.createStatement();
                st.execute("alter table situacaoclientesinteticodw drop column situacaocontratooperacao");

            }catch (Exception e){
            }

            try{
                Statement st2 = connection.createStatement();
                st2.execute("alter table usuariomovel drop column indicado");
            }catch (Exception e){
            }

        }catch (Exception e){
            //
        }
    }

    public static void criarCamposNecessarioSintetico(Connection connection){
        try {
            try{
                Statement st = connection.createStatement();
                st.execute("alter table situacaoclientesinteticodw add column situacaocontratooperacao varchar(255);");

            }catch (Exception e){
            }

            try{
                Statement st2 = connection.createStatement();
                st2.execute("alter table usuariomovel add indicado integer;");
            }catch (Exception e){
            }

        }catch (Exception e){
            //
        }
    }

    public static List<ClienteVO> consultarClienteComSinteticoErrado(Connection connection)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cli.codigo , c.vigenciaAteAjustada \n");
        sql.append("from contrato c \n");
        sql.append("inner join situacaoClienteSinteticoDw dw on dw.codigoPessoa = c.pessoa \n");
        sql.append("inner join cliente cli on cli.pessoa = c.pessoa \n");
        sql.append("inner join plano p on p.codigo = c.plano \n");
        //sql.append("and c.dataLancamento >= '2016-08-01 00:00:00' \n");
        sql.append("where dw.situacao = 'VI' \n");
        Statement st = connection.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<ClienteVO> lista = new ArrayList<ClienteVO>();
        Cliente clienteDao = new Cliente(connection);
        while (rs.next()){
            ClienteVO clienteVO = clienteDao.consultarPorCodigo(rs.getInt("codigo"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (Calendario.maiorOuIgual(rs.getDate("vigenciaAteAjustada"), Calendario.hoje())){
                alterarSituacaoCliente(connection, rs.getInt("codigo"), "AT");
                clienteVO.setSituacao("AT");
            }else{
                alterarSituacaoCliente(connection, rs.getInt("codigo"), "IN");
                clienteVO.setSituacao("IN");
            }
            lista.add(clienteVO);
        }
        return lista;

    }

    public static void alterarSituacaoCliente(Connection connection, Integer codigoCliente, String situacao){
        try{
            Statement st = connection.createStatement();
            StringBuilder sql = new StringBuilder();
            sql.append("update cliente set situacao = '").append(situacao).append("' where codigo = ").append(codigoCliente);
            st.execute(sql.toString());
        }catch (Exception e){
            //
        }
    }

    public static void incluirTodasCidadesDoBrasil(List<ConexaoOAMD> listaCon){
        try{
            Connection conCEP = obterConexao("BDCEP");
            List<CidadeBDCEP> listaTodasCidades = consultarCidadesBDCep(conCEP);
            System.out.println("Inicio do processo em "+ Calendario.hoje());
            for (ConexaoOAMD conexaoOAMD: listaCon){
                System.out.println("Inicio do processo em " + Calendario.hoje() + " BD:" + conexaoOAMD.getConnection().getClientInfo());
                importarCidadeBDCepParaZillyon(listaTodasCidades, conexaoOAMD.getConnection());
                System.out.println("Fim do processo em "+Calendario.hoje() + " BD:" + conexaoOAMD.getConnection().getClientInfo());
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    public static List<ConexaoOAMD> retornarListaConexoes(){

        List<ConexaoOAMD> lista = new ArrayList<ConexaoOAMD>();
        try{
            Connection conOAMD = obterConexao("OAMD");
            Connection conPontaVerde = obterConexao("bdzillyonselfitpontaverde");

            StringBuilder sql = new StringBuilder();
            sql.append("select emp.chave, \"nomeBD\" \n");
            sql.append("from empresa emp \n");
            sql.append("inner join empresaFinanceiro ef on ef.chaveZw = emp.chave \n");
            sql.append("inner join redeempresa re on re.id = ef.redeEmpresa_id \n");
            sql.append("where upper(re.nome) = 'REDESELFIT' ");
            //sql.append(" and \"nomeBD\" = 'bdzillyonselfitpontaverde' ");
            Statement st = conOAMD.createStatement();
            ResultSet rs = st.executeQuery(sql.toString());
            while (rs.next()){
               ConexaoOAMD conexaoOAMD = (new CorrecoesSelfit()).new ConexaoOAMD();
               conexaoOAMD.setConnection(obterConexao(rs.getString("nomeBD")));
               conexaoOAMD.setChave(rs.getString("chave"));
               lista.add(conexaoOAMD);
            }
            conOAMD.close();
        }catch (Exception e){
            System.out.println("ERRO AO CRIAR CONEXÃO COM O BD. ERRO: " +e.getMessage());
        }
        return lista;
    }



    public static Connection obterConexao(String nomeBD)throws Exception{
        String hostBD = "zw-rds-9.cmgq5kfs7ytf.sa-east-1.rds.amazonaws.com";
        String porta = "5432";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        String userBD = "zillyonweb";
        String passwordBD = "pactodb2020";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }

    public static void importarCidadeBDCepParaZillyon(List<CidadeBDCEP> listaCidades, Connection conZillyon)throws Exception{
        for (CidadeBDCEP cidade: listaCidades){
            Integer codigoEstado = retornarCodigoEstado(cidade.getUf(), conZillyon);
            if (codigoEstado == null){
                System.out.println("ESTADO DA CIDADE NÃO CADASTRADO NO ZILLYON. Cidade: " + cidade.getNomeCidade() + " Estado:" + cidade.getUf());
                continue;
            }
            Integer codigoCidade = retornarCodigoCidade(cidade,conZillyon);
            if (codigoCidade == null){
                Cidade cidadeDao = new Cidade(conZillyon);
                CidadeVO cidadeVO = new CidadeVO();
                cidadeVO.setEstado(new EstadoVO());
                cidadeVO.getEstado().setCodigo(codigoEstado);
                cidadeVO.setPais(new PaisVO());
                cidadeVO.getPais().setCodigo(1);
                cidadeVO.setNome(cidade.getNomeCidade());
                try{
                    cidadeDao.incluir(cidadeVO);
                }catch (Exception e){
                    System.out.println(e.getMessage());
                }

                System.out.println("NEW CIDADE: " + cidade.getNomeCidade() + " Estado:" + cidade.getUf());
            }
        }


    }

    public static Integer retornarCodigoCidade(CidadeBDCEP cidadeBDCEP, Connection conZillyon)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select c.* \n");
        sql.append("from cidade c \n");
        sql.append("inner join estado est on est.codigo = c.estado \n");
        sql.append("where est.sigla = '").append(cidadeBDCEP.getUf()).append("' \n");
        sql.append("and ((c.nome = '").append(cidadeBDCEP.getNomeCidade().toUpperCase()).append("') or (c.nomesemacento = '").append(Uteis.retirarAcentuacao(cidadeBDCEP.getNomeCidade())).append("')) \n");
        Statement st = conZillyon.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return rs.getInt("codigo");
        }
        return null;
    }

    public static Integer retornarCodigoEstado(String UF, Connection conZillyon)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from estado \n");
        sql.append("where sigla = '").append(UF).append("'");
        Statement st = conZillyon.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return rs.getInt("codigo");
        }
        return null;
    }

    public static List<CidadeBDCEP> consultarCidadesBDCep(Connection con)throws Exception{
        List<CidadeBDCEP> lista = new ArrayList<CidadeBDCEP>();
        StringBuilder sql = new StringBuilder();
        sql.append("select uf.sigla, c.descricao as nomeCidade \n");
        sql.append("from cidade c \n");
        sql.append("inner join uf on uf.codigo = c.ufCodigo \n");
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        while (rs.next()){
            CidadeBDCEP obj = new CidadeBDCEP();
            obj.setUf(rs.getString("sigla"));
            obj.setNomeCidade(rs.getString("nomeCidade"));
            lista.add(obj);
        }
        return lista;
    }

    public class ConexaoOAMD {

        private Connection connection;
        private String chave;

        public Connection getConnection() {
            return connection;
        }

        public void setConnection(Connection connection) {
            this.connection = connection;
        }

        public String getChave() {
            return chave;
        }

        public void setChave(String chave) {
            this.chave = chave;
        }
    }
}

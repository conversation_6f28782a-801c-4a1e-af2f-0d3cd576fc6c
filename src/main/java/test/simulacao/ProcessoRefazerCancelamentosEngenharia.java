package test.simulacao;

import br.com.pactosolucoes.enumeradores.OcorrenciaEnum;
import br.com.pactosolucoes.enumeradores.TipoParcelaCancelamento;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.crm.MalaDireta;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.operacoes.CancelamentoContratoAutomaticoService;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class ProcessoRefazerCancelamentosEngenharia {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";
    private static String diretorioLog = "C:\\PactoJ\\log";
    private static Map<String, List<String>> mapCasosAvaliar = new HashMap<>();

    public static void main(String[] args) throws Exception {

        String chave = "";
        Integer codigoRedeEmpresa = 467; // 467 Engenharia
        boolean simulacao = true;

        executarEmTodasUnidadesRedeEmpresa(codigoRedeEmpresa, chave, simulacao); // 467 - Engenharia
    }

    private static void executarEmTodasUnidadesRedeEmpresa(Integer redeEmpresaId, String chave, Boolean simulacao) throws Exception {
        Connection conOAMD = new Conexao("*****************************************************", "postgres", "pactodb").getConexao();

        if (UteisValidacao.emptyNumber(redeEmpresaId)) {
            throw new Exception("O rede empresa id não foi informado!");
        }
        String sqlEmpresas = "select e.* from empresa e \n" +
                " inner join empresafinanceiro ef on ef.chavezw = e.chave \n" +
                "where ef.redeempresa_id = " + redeEmpresaId + " \n";
        if (!UteisValidacao.emptyString(chave)) {
            sqlEmpresas += "and e.chave = '" + chave + "'";
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmpresas, conOAMD);

        int count = 0;
        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sqlEmpresas + ") as sql", conOAMD);

        if (total == 0) {
            throw new Exception("Nenhuma empresa encontrada para o redeEmpresaId + " + redeEmpresaId);
        }

        while (rs.next()) {
            String host = rs.getString("hostBD");
            String porta = rs.getString("porta");
            String nomeBD = rs.getString("nomeBD");
            String user = rs.getString("userBD");
            String passWord = rs.getString("passwordBD");

            try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + nomeBD, user, passWord)) {
                Conexao.guardarConexaoForJ2SE(con);
                System.out.println((++count) + "\\" + total + " " + con.getCatalog() + " - Executando processo refazer cancelamento!");
                ResultSet rsEmpresa = SuperFacadeJDBC.criarConsulta("select codigo from empresa where ativa is true", con);
                while(rsEmpresa.next()) {
                    refazerCancelamentosContratosCanceladosAutomaticamente(con, rsEmpresa.getInt("codigo"), "", simulacao);
                }
            }
        }
    }

    private static void refazerCancelamentosContratosCanceladosAutomaticamente(Connection con, Integer codigoEmpresa, String codigosContratos, boolean simulacao) throws IOException {
        try {
            nomeBanco = con.getCatalog();
            logGravar = new StringBuilder();

            Empresa empresaDAO = new Empresa(con);
            EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            empresaDAO = null;

            MalaDireta malaDiretaDAO = new MalaDireta(con);
            List<Integer> codigosMalaDiretaCancelamento = malaDiretaDAO.consultarCodigosPorOcorrencia(OcorrenciaEnum.APOS_CANCELAMENTO, codigoEmpresa);
            if (!UteisValidacao.emptyList(codigosMalaDiretaCancelamento)) {
                String codigos = codigosMalaDiretaCancelamento.stream().map(Objects::toString).collect(Collectors.joining(","));
                adicionarLog("Desativando mala diretas: " + codigos);
                SuperFacadeJDBC.executarConsulta("update maladireta set excluida = true where codigo in (" + codigos + ");", con);
            }

            mapCasosAvaliar.put("FALHA", new ArrayList<>());
            mapCasosAvaliar.put("AVALIAR", new ArrayList<>());

            Contrato contratoDAO = new Contrato(con);

            String sql = "select \n" +
                    " cli.codigomatricula, \n" +
                    " pes.nome, \n" +
                    " con.codigo as contrato,\n" +
                    " cp.codigo as contratooperacao,\n" +
                    " cp.dataoperacao as dataoperacao,\n" +
                    " mcc.codigo as mcc_cancelamento,\n" +
                    " mcc_max.saldoatual,\n" +
                    " (coalesce(mcc.codigo,0) > 0 and coalesce(mcc_max.codigo,0) > 0 and mcc.codigo <> mcc_max.codigo) as saldodevedoralterado,\n" +
                    " (select count(mpar.codigo) from movparcela mpar where mpar.contrato = con.codigo and mpar.situacao = 'RG') as qtdParcelasRenegociadas," +
                    " (select count(mpar.codigo) from movparcela mpar \n" +
                    "  inner join observacaooperacao op on op.movparcela = mpar.codigo\n" +
                    "  where mpar.contrato = con.codigo \n" +
                    "  and mpar.situacao = 'CA'\n" +
                    "  and op.tipooperacao = 'PC'\n" +
                    "  and op.dataoperacao::date > cp.dataoperacao::date) as qtdParcelasCanceladasPosCancelamento,\n" +
                    " (select count(rpg.codigo) from recibopagamento rpg where rpg.contrato = con.codigo and rpg.data::date > cp.dataoperacao) as qtdPagamentosPosCancelamento," +
                    " (select array_to_string(array(\n" +
                    " select mpar.codigo from movparcela mpar \n" +
                    "  inner join observacaooperacao op on op.movparcela = mpar.codigo\n" +
                    " where mpar.contrato = con.codigo \n" +
                    " and mpar.situacao = 'CA'\n" +
                    " and op.tipooperacao = 'PC'\n" +
                    " and op.dataoperacao::date = cp.dataoperacao::date\n" +
                    "),',','')) as movparcelas_canceladas\n" +
                    "from contrato con\n" +
                    "\tinner join pessoa pes on pes.codigo = con.pessoa \n" +
                    "\tinner join cliente cli on cli.pessoa = pes.codigo \n" +
                    "\tinner join contratooperacao cp on cp.contrato = con.codigo\n" +
                    "\tleft join movimentocontacorrentecliente mcc \n" +
                    "\t\ton mcc.codigo = (select mcc2.codigo from movimentocontacorrentecliente mcc2\n" +
                    "\t\t\twhere mcc2.pessoa = con.pessoa\n" +
                    "\t\t\tand mcc2.descricao = 'DEBITO DO VALOR DO CANCELAMENTO'\n" +
                    "\t\t\tand mcc2.dataregistro::date = cp.dataoperacao::date\n" +
                    "\t\t\tand trunc(mcc2.saldoatual::numeric,2) = trunc((cp.valor)::numeric,2)\n" +
                    "\t\t)\n" +
                    "\tleft join movimentocontacorrentecliente mcc_max on mcc_max.codigo = (select max(mcc2.codigo) from movimentocontacorrentecliente mcc2 where mcc2.pessoa = con.pessoa)\n" +
                    "where 1 = 1\n" +
                    "and coalesce(con.id_externo,0) > 0\n" +
                    "and con.situacao = 'CA'\n" +
                    "and cp.responsavel = 3\n" +
                    "and cp.observacao ilike '%MOTIVO: CANCELAMENTO POR MAIS DE 3 PARCELAS VENCIDAS SEGUIDAS%'\n" +
                    "and cp.descricaocalculo ilike '%VALOR DEPOSITADO NA CONTA DO ALUNO%'\n";

            if (!UteisValidacao.emptyString(codigosContratos)) {
                sql += "and con.codigo in (" + codigosContratos + ") \n";
            }

            int total = SuperFacadeJDBC.contar("select count(sql.contrato) from (" + sql + ") as sql", con);
            int atual = 0;
            int sucesso = 0;

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                Integer codigoContrato = rs.getInt("contrato");
                String nome = rs.getString("nome");
                Integer codigoMatricula = rs.getInt("codigomatricula");
                Integer codigoContratoOperacao = rs.getInt("contratooperacao");
                Integer codigoMovimentoContaCorrente = rs.getInt("mcc_cancelamento");
                Boolean saldoDevedorAlterado = rs.getBoolean("saldodevedoralterado");
                String movparcelasCanceladas = rs.getString("movparcelas_canceladas");
                Date dataOperacaoCancelamento = rs.getDate("dataoperacao");

                con.setAutoCommit(false);

                // remover saldo gerado pelo cancelamento
                if (saldoDevedorAlterado) {
                    mapCasosAvaliar.get("AVALIAR").add(String.format("Contrato: %d | MAT: %d - %s - Saldo devedor não foi ajustado!", codigoContrato, codigoMatricula, nome));
                } else if (!UteisValidacao.emptyNumber(codigoMovimentoContaCorrente)) {
                    if (!simulacao) {
                        SuperFacadeJDBC.executarConsulta("delete from movimentocontacorrenteclientecomposicao where movimentocontacorrentecliente = " + codigoMovimentoContaCorrente, con);
                        SuperFacadeJDBC.executarConsulta("delete from movimentocontacorrentecliente where codigo = " + codigoMovimentoContaCorrente, con);
                    }
                }

                try {

                    adicionarLog(String.format("%s%d\\%d - Processando contrato: %d dt_cancelamento: %s | MAT: %d - %s", simulacao ? " [MODO SIMULAÇÃO] " : "", ++atual, total, codigoContrato, Uteis.getData(rs.getDate("dataoperacao")), codigoMatricula, nome));

                    if (rs.getInt("qtdParcelasRenegociadas") > 0) {
                        mapCasosAvaliar.get("AVALIAR").add(String.format("Contrato: %d | MAT: %d - %s - Possui %d parcelas renegocadas!", rs.getInt("contrato"), rs.getInt("codigoMatricula"), rs.getString("nome"), rs.getInt("qtdParcelasRenegociadas")));
                        continue;
                    }
                    if (rs.getInt("qtdParcelasCanceladasPosCancelamento") > 0) {
                        mapCasosAvaliar.get("AVALIAR").add(String.format("Contrato: %d | MAT: %d - %s - Possui %d parcelas canceladas manualmente após o cancelamento automatico!", rs.getInt("contrato"), rs.getInt("codigoMatricula"), rs.getString("nome"), rs.getInt("qtdParcelasCanceladasPosCancelamento")));
                        continue;
                    }
                    if (rs.getInt("qtdPagamentosPosCancelamento") > 0) {
                        mapCasosAvaliar.get("AVALIAR").add(String.format("Contrato: %d | MAT: %d - %s - Possui %d pagamentos lançados após o cancelamento automatico!", rs.getInt("contrato"), rs.getInt("codigoMatricula"), rs.getString("nome"), rs.getInt("qtdPagamentosPosCancelamento")));
                        continue;
                    }


                    reverterCancelamentoSemCommit(codigoContrato, codigoContratoOperacao, movparcelasCanceladas, saldoDevedorAlterado, codigoMovimentoContaCorrente, con);

                    Calendario.dia = dataOperacaoCancelamento;
                    Calendario.setDateThread(dataOperacaoCancelamento);
                    ContratoVO contratoVO = contratoDAO.consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_TODOS);
                    CancelamentoContratoAutomaticoService cancelamentoAutomatico = new CancelamentoContratoAutomaticoService(con);
                    cancelamentoAutomatico.cancelarAutomatico(contratoVO, TipoParcelaCancelamento.obterEnumPorSigla(contratoVO.getEmpresa().getTipoParcelaCancelamento()), false, true, false);

                    if (!simulacao) {
                        con.commit();
                    }
                    sucesso++;
                } catch (Exception e) {
                    mapCasosAvaliar.get("FALHA").add(String.format("Contrato: %d | MAT: %d - %s - erro: %s", codigoContrato, codigoMatricula, nome, e.getMessage()));
                    con.rollback();
                }
            }

            if (!UteisValidacao.emptyList(codigosMalaDiretaCancelamento)) {
                String codigos = codigosMalaDiretaCancelamento.stream().map(Objects::toString).collect(Collectors.joining(","));
                adicionarLog("Re-ativando maladiretas: " + codigos);
                SuperFacadeJDBC.executarConsulta("update maladireta set excluida = false where codigo in (" + codigos + ");", con);
            }

            adicionarLog("==================================================");
            adicionarLog("TOTAL: " + total);
            adicionarLog("SUCESSO: " + sucesso);
            adicionarLog("FALHA: " + mapCasosAvaliar.get("FALHA").size());
            adicionarLog("AVALIAR: " + mapCasosAvaliar.get("AVALIAR").size());
            if (mapCasosAvaliar.get("FALHA").size() > 0) {
                adicionarLog("==================================================");
                adicionarLog("FALHAS: ");
                mapCasosAvaliar.get("FALHA").forEach(ProcessoRefazerCancelamentosEngenharia::adicionarLog);
            }
            if (mapCasosAvaliar.get("AVALIAR").size() > 0)
                adicionarLog("==================================================");
            adicionarLog("CASOS AVALIAR: ");
            mapCasosAvaliar.get("AVALIAR").forEach(ProcessoRefazerCancelamentosEngenharia::adicionarLog);
        } catch (Exception e) {
            adicionarLog(e.getMessage());
        } finally {
            Uteis.salvarArquivo(String.format("%s-%s-%s.txt", "log-refazer-cancelamento", nomeBanco, Calendario.getData("yyyyMMddHHmmss")), getLogGravar().toString(), diretorioLog + File.separator);
        }
    }

    private static void reverterCancelamentoSemCommit(Integer codigoContrato, Integer codigoContratoOperacao, String movParcelasCanceladas, Boolean saldoDevedorAlterado, Integer codigoMovimentoContaCorrente, Connection con) throws Exception {
        Contrato contratoDAO = new Contrato(con);
        ContratoVO contratoVO = contratoDAO.consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_PARCELA_PRODUTOS);
        contratoDAO.montarDadosPessoa(contratoVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        contratoDAO.montarDadosCliente(contratoVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        contratoDAO = null;

        reverterAlteracoesMovProdutos(contratoVO, con);

        if (!UteisValidacao.emptyString(movParcelasCanceladas)) {
            SuperFacadeJDBC.executarConsulta("update movparcela set situacao = 'EA' where codigo in (" + movParcelasCanceladas + ");", con);
            SuperFacadeJDBC.executarConsulta("update movproduto mpro set situacao = mpar.situacao from movprodutoparcela mpp, movparcela mpar \n" +
                    " where mpp.movproduto = mpro.codigo \n" +
                    " and mpar.codigo = mpp.movparcela \n" +
                    " and mpar.codigo in (" + movParcelasCanceladas + ");", con);
        }

        SuperFacadeJDBC.executarConsulta("UPDATE contrato\n" +
                "SET vigenciaateajustada = vigenciaate, situacao = 'AT'\n" +
                "WHERE codigo = " + codigoContrato, con);

        SuperFacadeJDBC.executarConsulta("UPDATE periodoacessocliente p SET datafinalacesso = con.vigenciaateajustada FROM contrato con\n" +
                "WHERE con.codigo = p.contrato " +
                "AND con.codigo = " + codigoContrato, con);

        SuperFacadeJDBC.executarConsulta("DELETE FROM periodoacessocliente WHERE tipoacesso = 'CN' AND contrato = " + codigoContrato, con);

        SuperFacadeJDBC.executarConsulta("DELETE FROM historicocontrato h\n" +
                "WHERE tipohistorico = 'CA' \n" +
                "AND contrato = " + codigoContrato, con);

        SuperFacadeJDBC.executarConsulta("UPDATE historicocontrato h SET datafinalsituacao = con.vigenciaateajustada FROM contrato con\n" +
                "WHERE con.codigo = h.contrato \n" +
                "AND h.tipohistorico = 'MA' \n" +
                "AND con.codigo = " + codigoContrato, con);

        SuperFacadeJDBC.executarConsulta("DELETE FROM contratooperacao WHERE codigo = " + codigoContratoOperacao, con);
    }

    private static void reverterAlteracoesMovProdutos(ContratoVO contratoVO, Connection con) throws Exception {
        Map<String, List<MovProdutoVO>> mapaMovProdutosMesReferencia = new HashMap<>();
        contratoVO.getMovProdutoVOs().forEach(mpro -> {
            if (mpro.getProduto().getTipoProduto().equals("PM")) {
                if (mapaMovProdutosMesReferencia.get(mpro.getMesReferencia()) == null) {
                    mapaMovProdutosMesReferencia.put(mpro.getMesReferencia(), new ArrayList<>());
                }
                mapaMovProdutosMesReferencia.get(mpro.getMesReferencia()).add(mpro);
            }
        });

        for (String mesRef : mapaMovProdutosMesReferencia.keySet()) {
            if (mapaMovProdutosMesReferencia.get(mesRef).size() > 1) {
                List<MovProdutoVO> movProdutoVOS = Ordenacao.ordenarLista(mapaMovProdutosMesReferencia.get(mesRef), "codigo");
                if (!UteisValidacao.emptyNumber(movProdutoVOS.get(movProdutoVOS.size() - 1).getMovProdutoBase())
                        && UteisValidacao.emptyNumber(movProdutoVOS.get(0).getMovProdutoBase())) {
                    MovProdutoVO movProdutoBase = movProdutoVOS.get(0);
                    Double somaProduto = 0.0;
                    if (UteisValidacao.emptyNumber(movProdutoBase.getTotalFinal())) {
                        for (MovProdutoVO mpro : movProdutoVOS) {
                            if (!mpro.getCodigo().equals(movProdutoBase.getCodigo())) {
                                somaProduto += mpro.getTotalFinal();
                                SuperFacadeJDBC.executarUpdate("delete from movproduto where codigo = " + mpro.getCodigo(), con);
                            }
                        }
                        movProdutoBase.setTotalFinal(somaProduto);
                        movProdutoBase.setPrecoUnitario(somaProduto);
                        movProdutoBase.setValorFaturado(somaProduto);
                        String update = "update movproduto set totalfinal = ?, precounitario = ?, valorfaturado = ?, situacao = 'EA' where codigo = ?";
                        PreparedStatement pstm = con.prepareStatement(update);
                        pstm.setDouble(1, movProdutoBase.getTotalFinal());
                        pstm.setDouble(2, movProdutoBase.getPrecoUnitario());
                        pstm.setDouble(3, movProdutoBase.getValorFaturado());
                        pstm.setInt(4, movProdutoBase.getCodigo());
                        pstm.execute();
                    }
                }
            }
        }
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.getData(new Date(), "dd/MM/yyyy HH:mm:mm:sss") + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

}

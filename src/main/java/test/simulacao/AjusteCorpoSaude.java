/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package test.simulacao;

import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class AjusteCorpoSaude {

    public static void main(String... args) {
        List<MovProdutoParcelaVO> listaMovProdutoParcelaVOs = new ArrayList<MovProdutoParcelaVO>();
        String nomeBD = "bdzillyoncorposaudeguara";

        MovProdutoParcela movProdutoParcelaDAO = null;
        try {
            Connection con = DriverManager.getConnection("********************************/" + nomeBD, "postgres", "pactodb");
            movProdutoParcelaDAO = new MovProdutoParcela(con);

            String sql = "SELECT * FROM movprodutoparcela m WHERE movproduto IN (\n" +
                    "\tSELECT m.codigo\n" +
                    "\tFROM movproduto m\n" +
                    "\tINNER JOIN movprodutoparcela m2 ON m.codigo = m2.movproduto\n" +
                    "\tWHERE CAST(totalfinal AS NUMERIC) = 0.99\n" +
                    "\tAND m.situacao ILIKE 'EA'\n" +
                    "\tAND m.responsavellancamento = 1\n" +
                    ");";

//            String sql = "SELECT * FROM movprodutoparcela m WHERE movproduto IN (3874369);"; //Para teste local, validndo apenas um registro no DBeaver

            try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
                try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                    while (tabelaResultado.next()) {
                        listaMovProdutoParcelaVOs.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
                    }
                }
            }

            for (MovProdutoParcelaVO movProdutoParcelaAlterar : listaMovProdutoParcelaVOs) {
                int codigoMovProdutoConsultar = movProdutoParcelaAlterar.getCodigo() - 1;
                MovProdutoParcelaVO movProdutoParcelaReferencia = movProdutoParcelaDAO.consultarPorChavePrimaria(codigoMovProdutoConsultar, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(movProdutoParcelaReferencia.getReciboPagamento().getCodigo()) &&
                    UteisValidacao.emptyNumber(movProdutoParcelaReferencia.getValorPago()) &&
                    (movProdutoParcelaAlterar.getMovParcela() - movProdutoParcelaReferencia.getMovParcela() == 1)) {

                    String updade = "UPDATE movprodutoparcela SET reciboPagamento = " + movProdutoParcelaReferencia.getReciboPagamento().getCodigo() + ", movparcela = " + movProdutoParcelaReferencia.getMovParcela() +
                            " WHERE codigo = " + movProdutoParcelaAlterar.getCodigo() + ";";
                    try (PreparedStatement sqlUpdate = con.prepareStatement(updade)) {
                        sqlUpdate.execute();
                    } catch (Exception ex) {
                        Uteis.logarDebug("Erro ao atualizar movprodutoparcela: " + movProdutoParcelaAlterar.getCodigo() + " - " + ex.getMessage());
                    }

                    String sqlUpdateMovProduto = "UPDATE movproduto SET situacao = 'PG' WHERE codigo = " + movProdutoParcelaAlterar.getMovProduto() + ";";
                    try (PreparedStatement sqlUpdate = con.prepareStatement(sqlUpdateMovProduto)) {
                        sqlUpdate.execute();
                    } catch (Exception ex) {
                        Uteis.logarDebug("Erro ao atualizar movprodutoparcela: " + movProdutoParcelaAlterar.getCodigo() + " - " + ex.getMessage());
                    }

                }
            }

        } catch (Exception ex) {
            Logger.getLogger(AjusteCorpoSaude.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            movProdutoParcelaDAO = null;
        }

    }

    public static MovProdutoParcelaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MovProdutoParcelaVO obj = new MovProdutoParcelaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setMovProduto(dadosSQL.getInt("movProduto"));
        obj.setMovParcela(dadosSQL.getInt("movParcela"));
        obj.setValorPago(dadosSQL.getDouble("valorPago"));
        obj.getReciboPagamento().setCodigo(dadosSQL.getInt("reciboPagamento"));
        obj.getMovParcelaOriginalMultaJuros().setCodigo(dadosSQL.getInt("movParcelaOriginalMultaJuros"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            MovProduto movProduto = new MovProduto(con);
            obj.setMovProdutoVO(movProduto.consultarPorChavePrimaria(obj.getMovProduto(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            movProduto = null;
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS || nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            MovProduto movProduto = new MovProduto(con);
            obj.setMovProdutoVO(movProduto.consultarPorChavePrimaria(obj.getMovProduto(), Uteis.NIVELMONTARDADOS_VENDA));
            movProduto = null;
            return obj;
        }

        return obj;
    }

}

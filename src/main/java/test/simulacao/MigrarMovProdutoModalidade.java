/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package test.simulacao;

import br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD;
import br.com.pactosolucoes.atualizadb.processo.GerarMovProdutoModalidade;
import java.sql.Connection;
import java.sql.ResultSet;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class MigrarMovProdutoModalidade {

    private static Connection obterConexao(String nomeHost, String porta, String superUser, String senha, String nomeBanco)
            throws Exception {
        Conexao conex = new Conexao("jdbc:postgresql://" + nomeHost + ":" + porta + "/" + nomeBanco, superUser, senha);
        return conex.getConexao();
    }

    public static void main(String... args) {

        try {
            String sql = "select * from pg_database where datname like('bdzillyon%') order by datname";
            String nomeHostPG = null;
            String portaPG = null;
            String superUserPG = null;
            String senhaPG = null;
            if (args != null && args.length > 1) {
                nomeHostPG = args[0];
                portaPG = args[1];
                superUserPG = args[2];
                senhaPG = args[3];
            } else {

                nomeHostPG = "desenv-waller";
                portaPG = "5432";
                superUserPG = "zillyonweb";
                senhaPG = "pactodb";

            }
            Connection con = obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, "postgres");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                String nomeBanco = rs.getString("datname");
                Uteis.logar(null, nomeBanco);
                Connection conAtual = obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, nomeBanco);
                Conexao.guardarConexaoForJ2SE(conAtual);
                try {
                    //
                    StringBuilder sb = new StringBuilder();
                    sb.append("CREATE TABLE movprodutomodalidade( ");
                    sb.append("codigo serial NOT NULL PRIMARY KEY, ");
                    sb.append("modalidade int NOT NULL, ");
                    sb.append("movproduto int NOT NULL, ");
                    sb.append("valor real NOT NULL, ");
                    sb.append("datainicio timestamp NOT NULL, ");
                    sb.append("datafim timestamp NOT NULL, ");
                    sb.append("FOREIGN KEY (modalidade) REFERENCES modalidade(codigo) ON DELETE CASCADE, ");
                    sb.append("FOREIGN KEY (movproduto) REFERENCES movproduto(codigo) ON DELETE CASCADE );");


                    SuperFacadeJDBC.executarConsultaUpdate(sb.toString(), con);

                    GerarMovProdutoModalidade.gravarDados(conAtual);

                    sb = new StringBuilder();
                    sb.append("CREATE INDEX movproduto_codigo ON movproduto (codigo);  ");
                    sb.append("CREATE INDEX cheque_datacompensacao ON cheque (datacompesancao); ");
                    sb.append("CREATE INDEX cheque_situacao ON cheque (situacao);  ");
                    sb.append("CREATE INDEX cheque_movpagamento ON cheque (movpagamento); ");
                    sb.append("CREATE INDEX cartaocredito_datacompensacao ON cartaocredito (datacompesancao); ");
                    sb.append("CREATE INDEX cartaocredito_movpagamento ON cartaocredito (movpagamento); ");
                    sb.append("CREATE INDEX movprodutomodalidade_movproduto ON movprodutomodalidade (movproduto); ");
                    sb.append("CREATE INDEX movprodutomodalidade_modalidade ON movprodutomodalidade (modalidade); ");
                    sb.append("CREATE INDEX movprodutomodalidade_valor ON movprodutomodalidade (valor);");
                    sb.append("CREATE INDEX movprodutoparcela_recibopagamento ON movprodutoparcela (recibopagamento);");
                    sb.append("CREATE INDEX rateiointegracao_codigo ON rateiointegracao (codigo);");
                    sb.append("CREATE INDEX rateiointegracao_planoconta ON rateiointegracao (planoconta);");
                    SuperFacadeJDBC.executarConsultaUpdate(sb.toString(), conAtual);

                    //
                } catch (Exception e) {
                    Uteis.logar(null, e.getMessage());
                } finally {
                    conAtual.close();
                }
            }
            con.close();
        } catch (Exception ex) {
            Uteis.logar(null, ex.getMessage());
        }
    }
}

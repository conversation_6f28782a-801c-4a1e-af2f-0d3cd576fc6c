package test.simulacao;

import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.util.ExecuteRequestHttpService;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;

public class EnviarCartaoMundiPagg {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";

    private static String urlAPI = "https://api.mundipagg.com/core/v1";
    private static String secretKey = "";
    private static String publicKey = "";
    private static String token = "";
    private static final String HEADER_AUTHORIZATION = "Authorization";
    private static final String HEADER_ACCEPT = "Accept";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION_JSON = "application/json";
    private static final String CHARSET_UTF8 = "UTF-8";

    public static void main(String[] args) throws IOException {
        try {
            logGravar = new StringBuilder();
            Uteis.debug = true;

            Connection con = new Conexao("*************************************", "postgres", "pactodb").getConexao();
            nomeBanco = con.getCatalog();


//            1;"MEGA ACADEMIA CASCATINHA"
//            Unidade Cascatinha - sk_plGZm7XcWSebQ7Y6
//            Unidade Cascatinha - pk_16VX4jlCBsz5Nb4G


//            2;"MEGA ACADEMIA SANTA LUZIA"
//            Unidade Santa Luzia - sk_lQZKL24iBTJJDGXp
//            Unidade Santa Luzia -  pk_qzAY2otjMiya80kg


//            3;"MEGA ACADEMIA ALTO DOS PASSOS"
//            Unidade Alto dos Passos - sk_QedLgbnh5CWg0qPO
//            Unidade Alto dos Passos -  pk_EBLK95OtWEh4vKvq

            Integer empresa = 3;
            secretKey = "sk_QedLgbnh5CWg0qPO";
            publicKey = "pk_EBLK95OtWEh4vKvq";

            String auto = secretKey + ":" + publicKey;
            token = "Basic " + new String(new Base64().encode(auto.getBytes()));


            enviarCartoes(empresa, con);

        } catch (Exception ex) {
            adicionarLog(ex.getMessage());
        } finally {
            Uteis.salvarArquivo("EnviarCartaoMundiPagg_" + nomeBanco + "_" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\PactoJ\\log\\" + File.separator);
        }
    }

    private static void enviarCartoes(Integer empresa, Connection con) {
        try {

            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table pessoa add column idMundiPagg character varying(150);", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table autorizacaocobrancacliente add column idMundiPagg character varying(150);", con);


            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("cl.matricula, \n");
            sql.append("p.codigo as pessoa, \n");
            sql.append("p.nome, \n");
            sql.append("p.cfp as cpf, \n");
            sql.append("au.codigo as autorizacaocobrancacliente, \n");
            sql.append("au.numerocartao, \n");
            sql.append("au.nometitularcartao as nometitular, \n");
            sql.append("au.cpftitular, \n");
            sql.append("au.validadecartao, \n");
            sql.append("au.operadoracartao    \n");
            sql.append("from autorizacaocobrancacliente au  \n");
            sql.append("inner join cliente cl on cl.codigo = au.cliente \n");
            sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
            sql.append("where au.ativa = true \n");
            sql.append("and coalesce(au.idmundipagg, '') = '' \n");
            sql.append("and au.tipoautorizacao = 1 \n");
//            sql.append("and au.nometitularcartao ilike '%.%' \n");
            sql.append("and cl.empresa = ").append(empresa);
            sql.append("order by au.codigo \n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            String cpf = "";
            while (rs.next()) {
                try {
                    con.setAutoCommit(false);

                    cpf = "";

                    String matricula = rs.getString("matricula");
                    String nome = rs.getString("nome");
//                    nome = Uteis.removerCaracteresNaoAscii(nome);
                    cpf = rs.getString("cpf");

                    Integer autorizacaocobrancacliente = rs.getInt("autorizacaocobrancacliente");
                    Integer pessoa = rs.getInt("pessoa");

                    String numerocartao = rs.getString("numerocartao");
                    String nometitular = rs.getString("nometitular");
                    if (nometitular.contains(".")) {
                        nometitular = nometitular.replaceAll("\\.", "");
                    }
                    nometitular = Uteis.removerCaracteresNaoAscii(nometitular);

                    String cpftitular = rs.getString("cpftitular");
                    String validadecartao = rs.getString("validadecartao");
                    OperadorasExternasAprovaFacilEnum operadora = OperadorasExternasAprovaFacilEnum.valueOf(rs.getInt("operadoracartao"));
                    if (operadora == null) {
                        throw new Exception("Operadora não encontrada!");
                    }

                    Integer mesVencimento = Integer.valueOf(validadecartao.substring(0, 2));
                    Integer anoVencimento = null;

                    if (validadecartao.length() == 7) {
                        anoVencimento = Integer.valueOf(validadecartao.substring(3, 7));
                    } else if (validadecartao.length() == 5) {
                        anoVencimento = Integer.valueOf(validadecartao.substring(3, 5));
                    } else {
                        anoVencimento = Integer.parseInt(validadecartao.split("/")[1]);
                    }

                    if (anoVencimento <= 2019) {
                        adicionarLog("Cartão vencido... | " + cpf + " | " + nome);
                        continue;
                    }

                    String idCustomer = consultarIdCustomer(cpf);
                    if (idCustomer.equalsIgnoreCase("naoEncontrado")) {
                        adicionarLog("Enviar cliente... | " + cpf + " | " + nome);
                        idCustomer = enviarCliente(nome, cpf);
                    }

                    String cartaoDescripto = APF.decifrar(numerocartao);

                    String idCartaoMundiPagg = consultarCartao(idCustomer, cartaoDescripto);
                    if (idCartaoMundiPagg.equalsIgnoreCase("naoEncontrado")) {
                        adicionarLog("Enviar Cartão... | " + autorizacaocobrancacliente + " | IdCustomer | " + idCustomer);
                        idCartaoMundiPagg = enviarCartao(idCustomer, cartaoDescripto, nometitular, cpftitular, mesVencimento, anoVencimento, operadora.getDescricao());
                    }

                    SuperFacadeJDBC.executarUpdateExecutarProcessos("update pessoa set idMundiPagg = '"+idCustomer+"' where codigo = " + pessoa, con);
                    SuperFacadeJDBC.executarUpdateExecutarProcessos("update autorizacaocobrancacliente set idMundiPagg = '"+idCartaoMundiPagg+"' where codigo = " + autorizacaocobrancacliente, con);

                    adicionarLog("Sucesso! " + matricula + " | IdCustomer | " + idCustomer + " | idCartao | " + idCartaoMundiPagg);

                    adicionarLog(matricula + " | " + cpf + " | " + nome + " | Sucesso | " + idCartaoMundiPagg);

                    con.commit();
                } catch (Exception ex) {
                    con.rollback();
                    adicionarLog(cpf + " | " + ex.getMessage());
                } finally {
                    con.setAutoCommit(true);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static String consultarIdCustomer(String cpf) throws Exception {
        JSONObject retorno = requestGET("customers", cpf);
        if (retorno.has("data")) {
            JSONArray jsonArray = retorno.getJSONArray("data");
            for (int e = 0; e < jsonArray.length(); e++) {
                JSONObject obj = jsonArray.getJSONObject(e);
                return obj.optString("id");
            }
        }
        return "naoEncontrado";
    }

    private static String consultarCartao(String idCustomer, String numeroCartao) throws Exception {
        JSONObject retorno = requestGET("customers/" + idCustomer + "/cards", null);
        if (retorno.has("data")) {
            JSONArray jsonArray = retorno.getJSONArray("data");
            for (int e = 0; e < jsonArray.length(); e++) {
                JSONObject obj = jsonArray.getJSONObject(e);

                String first_six_digits = obj.optString("first_six_digits");
                String last_four_digits = obj.optString("last_four_digits");

                if (numeroCartao.startsWith(first_six_digits) && numeroCartao.endsWith(last_four_digits)) {
                    return obj.optString("id");
                }
            }
        }
        return "naoEncontrado";
    }

    private static String enviarCliente(String nome, String cpf) throws Exception {
        JSONObject json = new JSONObject();
        json.put("name", nome);
        json.put("type", "individual");
        json.put("code", Uteis.removerMascara(cpf));
        json.put("document", Uteis.removerMascara(cpf));

        JSONObject retorno = requestPOST("customers", json);
        if (retorno.has("id")) {
            return retorno.optString("id");
        } else {
            throw new Exception(retorno.toString());
        }
    }

    private static String enviarCartao(String idCustomer, String cartao, String nomeCartao, String cpfTitular,
                                       Integer mesVencimento, Integer anoVencimento, String bandeira) throws Exception {
        cpfTitular = Uteis.removerMascara(cpfTitular);
        JSONObject json = new JSONObject();
        json.put("number", cartao);
        json.put("holder_name", nomeCartao);
        if (!UteisValidacao.emptyString(cpfTitular)) {
            json.put("holder_document", cpfTitular);
        }
        json.put("exp_month", mesVencimento);
        json.put("exp_year", anoVencimento);
        json.put("brand", bandeira);

        JSONObject retorno = requestPOST("customers/" + idCustomer + "/cards", json);
        if (retorno.has("id")) {
            return retorno.optString("id");
        } else {
            throw new Exception(retorno.toString());
        }
    }

    private static JSONObject requestPOST(String metodo, JSONObject json) throws Exception {
        try {

            StringEntity entity = new StringEntity(json.toString(), "UTF-8");
            HttpPost httpPost = new HttpPost(urlAPI + "/" + metodo);
            httpPost.setEntity(entity);

            httpPost.setHeader(HEADER_AUTHORIZATION, token);
            httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);

            HttpClient client = ExecuteRequestHttpService.createConnector();

            HttpResponse response = client.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            if (statusCode == 200) {
                return new JSONObject(responseBody);
            } else {
                throw new Exception(responseBody);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private static JSONObject requestGET(String metodo, String cpf) throws Exception {
        try {

            String request = urlAPI + "/" + metodo;

            if (cpf != null) {
                cpf = Uteis.removerMascara(cpf);
                request = urlAPI + "/" + metodo + "?page=0&size=10&document=" + cpf;
            }

            HttpGet httpGet = new HttpGet(request);
            httpGet.setHeader(HEADER_AUTHORIZATION, token);
            httpGet.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);

            HttpClient client = ExecuteRequestHttpService.createConnector();
            HttpResponse response = client.execute(httpGet);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            if (statusCode == 200) {
                return new JSONObject(responseBody);
            } else {
                throw new Exception(responseBody);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }


    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    public void setLogGravar(StringBuilder logGravar) {
        this.logGravar = logGravar;
    }
}

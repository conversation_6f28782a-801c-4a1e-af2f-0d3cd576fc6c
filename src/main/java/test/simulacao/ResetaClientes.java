/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package test.simulacao;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

/**
 *
 * <AUTHOR>
 * Esta é uma classe de apoio que deve ser usada uma única vez, que realiza estas operações:
 * 1. Estorna todos os contratos ativos cadastrados;
 * 2. Excluir turmas, modalidades, planos, ambientes exceto os informados pelo array de codigos respectivos;
 */
public class ResetaClientes extends SuperFacadeJDBC {

    public static int[] arrayPlanosNaoExcluir = {8, 9, 10, 11};
    public static int[] arrayModalidadesNaoExcluir = {118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140};
    public static int[] arrayTurmasNaoExcluir = {411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442};
    public static int[] arrayAmbientesNaoExcluir = {127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137};

    private static boolean estornarContratos() throws Exception {
        ContratoInterfaceFacade contratoFacade = getFacade().getContrato();
        ReciboPagamento reciboFacade = getFacade().getReciboPagamento();
        List<ContratoVO> listaContratos = contratoFacade.consultar("select * from contrato ORDER BY codigo DESC",
                Uteis.NIVELMONTARDADOS_MINIMOS);
        //
        int cont = 0;
        for (ContratoVO contratoVO : listaContratos) {
            ClienteVO cliente = null;
            contratoVO.setUsuarioVO(getFacade().getUsuario().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            List<ReciboPagamentoVO> listaReciboPagamento = reciboFacade.consularReciboPagamParcelaPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!listaReciboPagamento.isEmpty()) {
                contratoVO.setMovParcelaVOs(new ArrayList());
                contratoVO.setListaEstornoRecibo(new ArrayList<EstornoReciboVO>());
                for (ReciboPagamentoVO recibo : listaReciboPagamento) {
                    EstornoReciboVO estornoRecibo = new EstornoReciboVO();
                    estornoRecibo.setReciboPagamentoVO(recibo);
                    estornoRecibo.setListaMovPagamento(getFacade().getMovPagamento().consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR));
                    estornoRecibo.setListaMovParcela(getFacade().getMovParcela().consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS));

                    //transações de cartão de crédito
                    contratoVO.montarListaTransacoes(estornoRecibo.getListaMovParcela(), getFacade().getReciboPagamento().getCon());
                    contratoVO.getListaEstornoRecibo().add(estornoRecibo);
                }

            } else {
                contratoVO.setMovParcelaVOs(getFacade().getMovParcela().
                        consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

                //transações de cartão de crédito
                contratoVO.montarListaTransacoes(contratoVO.getMovParcelaVOs(), getFacade().getReciboPagamento().getCon());
            }

            contratoVO.setPrecisaEstornarTransacoes(false);

            contratoVO.montarListaItensRemessa(getFacade().getMovParcela().
                    consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA), getFacade().getZWFacade().getCon());


            try {
                cliente = getFacade().getCliente().consultarPorCodigoPessoa(
                        contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                contratoFacade.estornoContrato(contratoVO, cliente, null,null);
                cont++;
                getFacade().getZWFacade().atualizarSintetico(cliente, Calendario.hoje(), 
                        SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                Uteis.logar(null, "Estornado com sucesso o contrato -> " + contratoVO.getCodigo() + " do cliente -> " + cliente.getCodigo());
                System.out.println("");
            } catch (Exception e) {
                Uteis.logar(null, "Erro ao estornar contrato -> " + contratoVO.getCodigo() + " do cliente -> " + cliente.getCodigo());
                Uteis.logar(null, "Exceção -> " + e.getMessage());
            }

        }
        Uteis.logar(null, "Total de contratos cadastrados -> " + listaContratos.size());
        Uteis.logar(null, "Total de contratos estornados -> " + cont);
        if ((listaContratos.size() - cont) != 0) {
            Uteis.logar(null, "Não estornados -> " + (listaContratos.size() - cont) + ". Vou executar o processo novamente para estornar os contratos faltantes!");
            return false;
        }
        return true;


    }

    private static void estornarRecibosRestante(Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from recibopagamento order by codigo", con);


        while (rs.next()) {
            ReciboPagamentoVO obj = ReciboPagamento.montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
            EstornoReciboVO estorno = new EstornoReciboVO();
            obj.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            estorno.setReciboPagamentoVO(obj);
            estorno.setListaMovPagamento(getFacade().getMovPagamento().consultarPorCodigoRecibo(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            estorno.setListaMovParcela(getFacade().getMovParcela().consultarPorCodigoRecibo(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS));
            //consulta as transações de cartão de crédito relacionadas aos contratos do recibo
            estorno.montarListaTransacoes(estorno.getListaMovParcela(), getFacade().getZWFacade().getCon());
            estorno.montarListaItensRemessa(estorno.getListaMovParcela(), estorno.getListaMovPagamento(), getFacade().getZWFacade().getCon());
            estorno.setEstornarOperadora(false);
            getFacade().getReciboPagamento().estornarReciboPagamento(estorno, getFacade().getMovPagamento(), getFacade().getMovProdutoParcela(), null, null);

            Uteis.logar(null, "Recibo -> " + obj.getCodigo() + " estornado com sucesso!");

        }

        SuperFacadeJDBC.executarConsulta("delete from transacaomovparcela", con);
        SuperFacadeJDBC.executarConsulta("delete from transacao", con);
        SuperFacadeJDBC.executarConsulta("delete from vendaavulsa", con);
        SuperFacadeJDBC.executarConsulta("delete from movimentocontacorrentecliente", con);
        SuperFacadeJDBC.executarConsulta("delete from movparcela", con);
        SuperFacadeJDBC.executarConsulta("delete from movproduto", con);

    }

    private static String montaArrayIn(int[] vetor) {
        String ret = "(";
        for (int i = 0; i < vetor.length; i++) {
            ret += vetor[i];
            if (((i + 1) < vetor.length)) {
                ret += ",";
            }
        }
        ret += ")";
        return ret;
    }

    private static void excluirTurmas(Connection con) throws Exception {
        String notIn = montaArrayIn(arrayTurmasNaoExcluir);
        String sql = "delete from turma where codigo not in " + notIn;
        executarConsulta(sql, con);
        //
        sql = "delete from horarioturma where turma not in " + notIn;
        executarConsulta(sql, con);
    }

    private static void excluirModalidades(Connection con) throws Exception {
        String notIn = montaArrayIn(arrayModalidadesNaoExcluir);
        //composicao->modalidade
        String sql = "delete from composicaomodalidade where modalidade not in " + notIn;
        executarConsulta(sql, con);
        //plano->modalidade
        sql = "delete from planomodalidade where modalidade not in " + notIn;
        executarConsulta(sql, con);
        //turma
        sql = "delete from turma where modalidade not in " + notIn;
        executarConsulta(sql, con);
        //modalidades
        sql = "delete from modalidade where codigo not in " + notIn;
        executarConsulta(sql, con);
        Uteis.logar(null, "Excluidos registros relacionados as modalidades diferentes de " + notIn);
    }

    private static void excluirPlanos(Connection con) throws Exception {
        String notIn = montaArrayIn(arrayPlanosNaoExcluir);
        String sql = "delete from plano where codigo not in " + notIn;
        executarConsulta(sql, con);
        Uteis.logar(null, "Excluidos registros relacionados aos planos diferentes de " + notIn);
    }

    private static void excluirAmbientes(Connection con) throws Exception {
        String notIn = montaArrayIn(arrayAmbientesNaoExcluir);
        String sql = "delete from horarioturma where ambiente not in " + notIn;
        executarConsulta(sql, con);
        //
        sql = "delete from ambiente where codigo not in " + notIn;
        executarConsulta(sql, con);
        //        
        Uteis.logar(null, "Excluidos ambientes diferentes de " + notIn);
    }

    private static void excluirPacotes(Connection con) throws Exception {
        String sql = "delete from planocomposicao";
        executarConsulta(sql, con);
        //
        sql = "delete from composicaomodalidade";
        executarConsulta(sql, con);
        //
        sql = "delete from composicao";
        executarConsulta(sql, con);
        //        
        Uteis.logar(null, "Excluidos todos os pacotes (composicao)");

    }

    private static void excluirVinculos(Connection con) throws Exception {
        String sql = "delete from vinculo";
        executarConsulta(sql, con);
        //
        Uteis.logar(null, "Excluidos todos os vinculos");

    }

    public static void main(String... args) {
        if (args.length == 0) {
            args = new String[]{""};
        }
        if (args.length >= 1) {
            String chave = args[0];
            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica(chave);
                Conexao.guardarConexaoForJ2SE(con);
                while (!ResetaClientes.estornarContratos()) {
                    //repete infinitamente até estornar todos os contratos
                }

                estornarRecibosRestante(con);

//                ResetaClientes.excluirAmbientes();
//                ResetaClientes.excluirPacotes();
//                ResetaClientes.excluirTurmas();
//                ResetaClientes.excluirModalidades();
//                ResetaClientes.excluirPlanos();
//                ResetaClientes.excluirVinculos();

                //
            } catch (Exception ex) {
                Logger.getLogger(ResetaClientes.class.getName()).log(Level.SEVERE,
                        "Erro ao iniciar execução com a chave " + chave, ex);
            }

        }

    }
}

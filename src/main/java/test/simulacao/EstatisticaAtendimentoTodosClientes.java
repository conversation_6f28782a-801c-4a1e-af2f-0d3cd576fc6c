package test.simulacao;

import br.com.pactosolucoes.comuns.json.EstatisticaSolicitacaoJSON;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.oamd.EmpresaFinanceiroVO;
import servicos.bi.exportador.Exportador;
import servicos.bi.exportador.RelatorioBuilder;
import servicos.integracao.intranet.service.DashboardService;
import servicos.propriedades.PropsService;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 15/08/2017.
 */
public class EstatisticaAtendimentoTodosClientes {

    public static void main(String[] args){
        EmpresaFinanceiroVO empresaFinanceiroVO = new EmpresaFinanceiroVO();
        try{
            System.out.println("INICIO PROCESSO: " + Calendario.getData(Calendario.hoje(), "dd-MM-yyyy HH:mm"));
            Connection connection = obterConexao();
            List<EmpresaFinanceiroVO> listaClientes = consultarTodosClientes(connection);
            EstatisticaSolicitacaoJSON estatisticaSolicitacaoJSON ;
            DashboardService dashboardService = new DashboardService();
            for(EmpresaFinanceiroVO obj: listaClientes){
                empresaFinanceiroVO = obj;
                if (obj.getCodigoFinanceiro() != null) {
                //if ((obj.getCodigoFinanceiro() != null) && (obj.getCodigo().equals(838))) {
                    estatisticaSolicitacaoJSON = dashboardService.consultarEstatisticaSolicitacaoPorCodigoFinanceiro(obj.getCodigoFinanceiro(), 30);
                    obj.setTempoMedioAtendimento(estatisticaSolicitacaoJSON.getTempoMedioRespostaApresentar());
                }
            }
            exportarParaExcel(listaClientes);
            System.out.println("FIM PROCESSO: " + Calendario.getData(Calendario.hoje(), "dd-MM-yyyy HH:mm"));
        }catch (Exception e){
            System.out.println(empresaFinanceiroVO.getCodigoFinanceiro());
            e.printStackTrace();
        }

    }

    private static void exportarParaExcel(List<EmpresaFinanceiroVO> resultado) throws  Exception{
        File arquivo = criarArquivo("tempoAtendimentoClientesPacto.xlt");
        RelatorioBuilder relatorio = new RelatorioBuilder();
        relatorio.dado(resultado);
        relatorio.addColuna("Codigo Financeiro", "codigoFinanceiro")
                .addColuna("Nome Fantasia", "nomeFantasia")
                .addColuna("Tempo médio Atendimento","tempoMedioAtendimento");
        Exportador.exportarExcel(relatorio, arquivo);
    }


    private static File criarArquivo(String nomeArquivo) throws Exception{
        String diretorio = PropsService.getPropertyValue(PropsService.diretorioArquivos);
        File arquivo = new File(diretorio + File.separator + nomeArquivo);
        if (arquivo.exists()) {
            arquivo.delete();
        }else{
            new File(diretorio).mkdirs();
        }
        arquivo.createNewFile();
        return arquivo;
    }

    private static List<EmpresaFinanceiroVO> consultarTodosClientes(Connection connection)throws Exception{
        Statement st = connection.createStatement();
        ResultSet rs = st.executeQuery("select codigo, codigoFinanceiro, nomeFantasia from empresaFinanceiro ");
        List<EmpresaFinanceiroVO> lista = new ArrayList<EmpresaFinanceiroVO>();
        while (rs.next()){
            EmpresaFinanceiroVO obj = new EmpresaFinanceiroVO();
            obj.setCodigo(rs.getInt("codigo"));
            obj.setCodigoFinanceiro(rs.getInt("codigoFinanceiro"));
            obj.setNomeFantasia(rs.getString("nomeFantasia"));
            lista.add(obj);
        }
        return lista;
    }

    private static Connection obterConexao()throws Exception{
        //String hostBD = "zw-rds-9.cmgq5kfs7ytf.sa-east-1.rds.amazonaws.com";
        //String porta = "5432";
        //String userBD = "zillyonweb";
        //String passwordBD = "pactodb2020";
        String hostBD = "oamd.pactosolucoes.com.br";
        String porta = "5432";
        String userBD = "postgres";
        String passwordBD = "pactodb";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + "OAMD";
        String driver = "org.postgresql.Driver";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }
}

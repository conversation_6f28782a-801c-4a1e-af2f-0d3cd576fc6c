package test.simulacao;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class ProcessoRemoverPagamentosCreditoVendaImportadosComAVista {

    private static List<String> falhasAvaliar = new ArrayList<>();
    private static List<String> pagamentosNaoEncontrados = new ArrayList<>();
    private static List<String> pagamentosDeletados = new ArrayList<>();
    private static StringBuilder logGravar;
    private static String nomeBanco = "";
    private static boolean simulacao = false;

    public static void main(String[] args) throws Exception {

        Connection conMSSql = getConSQLServer("riogrande");
        Connection conZW = DriverManager.getConnection("****************************************************************************************************", "postgres", "pactodb");
        String idsClientes = "";

        simulacao = false;

        nomeBanco = conZW.getCatalog();

        try {
            removerPagamentosCreditoVendaImportadosComAVista(conZW, conMSSql, idsClientes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            Uteis.salvarArquivo(nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\PactoJ\\log\\");
        }
    }

    private static void removerPagamentosCreditoVendaImportadosComAVista(Connection conZW, Connection conMSSql, String idsClientes) throws Exception {

        String sqlSelect = "SELECT \n" +
                "\tcli.id_cliente,\n" +
                "\tconcat(cli.nome, ' ', cli.snome) as NOME, \n" +
                "\tr.ID_RECEBIMENTO,\n" +
                "\tr.DS_RECEBIMENTO,\n" +
                "\tr.VALOR,\n" +
                "\tr.DT_RECEBIMENTO,\n" +
                "\tr.ID_VENDA,\n" +
                "\t(SELECT COUNT(r2.ID_RECEBIMENTO) FROM RECEBIMENTOS r2 WHERE r2.ID_VENDA = r.ID_VENDA AND r2.DS_RECEBIMENTO LIKE '%Crédito de venda%' and ROUND(r2.valor,2) = ROUND(r.valor,2) and convert(date, r2.dt_recebimento) = convert(date,r.dt_recebimento)) as qtdRecebimentoCreditoVenda, \n" +
                "\t(SELECT STRING_AGG(cc.ID_CLIENTE_CONTRATO, ',') FROM CLIENTES_CONTRATOS cc INNER JOIN VENDAS_ITENS vi ON vi.ID_ITEM_VENDA = cc.ID_ITEM_VENDA WHERE vi.ID_VENDA = r.ID_VENDA) as IDS_CONTRATOS\n";
        String sql = "FROM clientes cli\n" +
                "\tINNER JOIN RECEBIMENTOS r ON r.ID_CLIENTE_PAGADOR = cli.ID_CLIENTE \n" +
                "WHERE r.DS_RECEBIMENTO LIKE '%Crédito de venda%' \n" +
                "AND r.DT_RECEBIMENTO IS NOT NULL \n";
        if (!UteisValidacao.emptyString(idsClientes)) {
            sql += "AND cli.ID_CLIENTE IN (" + idsClientes + ") \n";
        }


        int qtdRegistros = SuperFacadeJDBC.contar("select count(r.id_recebimento) " + sql, conMSSql);
        int atual = 0;

        sql += "ORDER BY cli.id_cliente, r.id_recebimento";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlSelect + sql, conMSSql);


        while(rs.next()) {
            Integer idCliente = rs.getInt("ID_CLIENTE");
            String nomeCliente = rs.getString("NOME");
            Integer idVenda = rs.getInt("ID_VENDA");
            Integer idRecebimento = rs.getInt("ID_RECEBIMENTO");
            String dsRecebimento = rs.getString("DS_RECEBIMENTO");
            Date dtRecebimento = rs.getDate("DT_RECEBIMENTO");
            Double valor = rs.getDouble("VALOR");
            String idsContratos = rs.getString("IDS_CONTRATOS");

            adicionarLog(String.format("%d\\%d - Processando recebimento: %d %s", ++atual, qtdRegistros, idRecebimento, dsRecebimento));

            String sqlPagamento = "select \n" +
                    "\tmpg.codigo as movpagamento,\n" +
                    "\tmpg.valor,\n" +
                    "\tmpg.datalancamento,\n" +
                    "\tfpg.tipoformapagamento,\n" +
                    "\tfpg.descricao,\n" +
                    "\trpg.codigo as recibopagamento\n" +
                    "from movpagamento mpg\n" +
                    "\tinner join recibopagamento rpg on rpg.codigo = mpg.recibopagamento\n" +
                    "\tinner join formapagamento fpg on fpg.codigo = mpg.formapagamento \n" +
                    "\tinner join cliente cli on cli.pessoa = mpg.pessoa\n" +
                    "where 1 = 1\n" +
                    "and cli.matriculaexterna  = " + idCliente + "\n" +
                    "and fpg.tipoformapagamento = 'AV'\n" +
                    "and (fpg.descricao ilike '%vista%' or fpg.descricao ilike '%dinheiro%')\n" +
                    "and mpg.datalancamento::date = '" + (Uteis.getDataJDBC(dtRecebimento)) + "'::date\n" +
                    "and trunc(mpg.valor::numeric,2) = trunc(" + valor + "::numeric,2)\n";

            if (!UteisValidacao.emptyString(idsContratos)) {
                sqlPagamento += "and (exists(select con.codigo from contrato con \n" +
                        " where con.codigo = rpg.contrato \n" +
                        "and coalesce(con.id_externo,con.idexterno) in (" + idsContratos + "))\n";
                sqlPagamento += "or exists(select mpar.codigo from movparcela mpar \n" +
                        "inner join pagamentomovparcela pmp on pmp.movparcela = mpar.codigo \n" +
                        "left join vendaavulsa v ON v.codigo = mpar.vendaavulsa \n" +
                        "left join aulaavulsadiaria a on a.codigo = mpar.aulaavulsadiaria \n" +
                        "where pmp.movpagamento = mpg.codigo \n " +
                        "and coalesce(v.id_movimento,a.id_venda) = " + idVenda + "))";
            } else {
                sqlPagamento += "and exists(select mpar.codigo from movparcela mpar \n" +
                        "inner join pagamentomovparcela pmp on pmp.movparcela = mpar.codigo \n" +
                        "left join vendaavulsa v ON v.codigo = mpar.vendaavulsa \n" +
                        "left join aulaavulsadiaria a on a.codigo = mpar.aulaavulsadiaria \n" +
                        "where pmp.movpagamento = mpg.codigo \n " +
                        "and coalesce(v.id_movimento,a.id_venda) = " + idVenda + ")";
            }

            int qtdRecebimentosPacto = SuperFacadeJDBC.contar("select count(sql.*) from (" + sqlPagamento + ") as sql", conZW);
            int qtdRecebimentoCreditoVendaEvo = rs.getInt("qtdRecebimentoCreditoVenda");

            if (qtdRecebimentosPacto == 0) {
                String msg = String.format("%d | %s | %d | %s | %s | %f", idCliente, nomeCliente, idRecebimento, dsRecebimento, Uteis.getDataJDBC(dtRecebimento), valor);
                pagamentosNaoEncontrados.add(msg);
            } if (qtdRecebimentosPacto > 1 && qtdRecebimentosPacto != qtdRecebimentoCreditoVendaEvo) {
                String msg = String.format("Foram encontrados %d pagamentos para o recebimento: | %d | %s | %d | %s | %s | %f", qtdRecebimentosPacto, idCliente, nomeCliente, idRecebimento, dsRecebimento, Uteis.getDataJDBC(dtRecebimento), valor);
                adicionarLog(msg);
                falhasAvaliar.add(msg);
                continue;
            }

            ResultSet rsPagamento = SuperFacadeJDBC.criarConsulta(sqlPagamento, conZW);
            while (rsPagamento.next()) {
                Integer codigoMovPagamento = rsPagamento.getInt("movpagamento");
                Integer codigorReciboPagamento = rsPagamento.getInt("recibopagamento");

                adicionarLog(String.format("\tDeletando movpagamento: %s", codigoMovPagamento));
                String msg = String.format("  %d | %s | %d | %s | %s | %f | %d", idCliente, nomeCliente, idRecebimento, dsRecebimento, Uteis.getDataJDBC(dtRecebimento), valor, codigoMovPagamento);

                pagamentosDeletados.add(msg);

                if (simulacao) {
                    continue;
                }
                SuperFacadeJDBC.executarUpdate("delete from movparcela where codigo in (\n" +
                        "select movparcela from pagamentomovparcela where movpagamento = " + codigoMovPagamento + ");", conZW);
                SuperFacadeJDBC.executarUpdate("update movprodutoparcela set recibopagamento = null where recibopagamento = " + codigorReciboPagamento, conZW);
                SuperFacadeJDBC.executarUpdate("delete from movpagamento where codigo = " + codigoMovPagamento, conZW);
                SuperFacadeJDBC.executarUpdate("delete from recibopagamento where codigo = " + codigorReciboPagamento, conZW);
            }
        }


        if (!pagamentosNaoEncontrados.isEmpty()) {
            adicionarLog("======================== NÃO ENCONTRADOS");
            pagamentosNaoEncontrados.forEach(ProcessoRemoverPagamentosCreditoVendaImportadosComAVista::adicionarLog);
        }
        if (!falhasAvaliar.isEmpty()) {
            adicionarLog("======================== FALHAS AVALIAR");
            falhasAvaliar.forEach(ProcessoRemoverPagamentosCreditoVendaImportadosComAVista::adicionarLog);
        }
        if (!pagamentosDeletados.isEmpty()) {
            adicionarLog("======================== DELETADOS");
            pagamentosDeletados.forEach(ProcessoRemoverPagamentosCreditoVendaImportadosComAVista::adicionarLog);
        }

        adicionarLog("======================== ======================== ======================== ");
        adicionarLog("======================== TOTAL: " + qtdRegistros);
        adicionarLog("======================== NÃO ENCONTRADOS: " + pagamentosNaoEncontrados.size());
        adicionarLog("======================== FALHAS AVALIAR: " + falhasAvaliar.size());
        adicionarLog("======================== DELETADOS: " + pagamentosDeletados.size()
                + (pagamentosDeletados.size() > 0 ? " movPagamentos: " + pagamentosDeletados.stream().map(p -> p.split("\\|")[6].trim()).collect(Collectors.joining(","))  : ""));
        adicionarLog("======================== ======================== ======================== ");
    }

    private static int obterQtdRecebimentosCreditoVendaPorIdVendaEvo(Integer idVenda, Connection conMSSql) throws Exception {
        String sql = "";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conMSSql);
        return rs.getInt("qtd");
    }

    public static Connection getConSQLServer(String bd) {
        try {
            String driver = "net.sourceforge.jtds.jdbc.Driver";
            String conexao = "jdbc:jtds:sqlserver:";
            Class.forName(driver).newInstance();
            return DriverManager.getConnection(conexao + "//localhost/" + bd, "sa", "pactodb");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}

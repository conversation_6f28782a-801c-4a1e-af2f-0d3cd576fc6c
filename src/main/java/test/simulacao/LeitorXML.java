package test.simulacao;

import java.io.File;
import java.io.FileWriter;
import java.util.List;
import java.util.Map;
import java.util.Set;


import org.jdom.Document;
import org.jdom.Element;
import org.jdom.input.SAXBuilder;
import org.jdom.output.Format;
import org.jdom.output.XMLOutputter;

/**
 * <AUTHOR>
 *
 */
public class LeitorXML {

    /**
     * @param arquivo
     * @return lista de elementos contidos no XML
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public List<Element> lerXML(String arquivo) {

        //criar documento
        Document doc = null;
        //ler documento
        SAXBuilder builder = new SAXBuilder();
        try {
            doc = builder.build(arquivo);

        } catch (Exception e) {

            e.printStackTrace();
        }
        //criar uma lista dos dados contidos no documento
        Element dados = doc.getRootElement();
        //retornar lista dos dados lidos no xml
        Element root = dados.getChild("ROWDATA");
        return root.getChildren("ROW");
    }

    /**
     * Gravar num arquivo XML a lista de atributos e valores
     * @param corpo
     * <AUTHOR>
     * @throws Exception
     */
    public void gravarXML(List<Map<Integer, String[]>> corpo, String path) throws Exception {
        Element rowDataPacket = new Element("DATAPACKET");
        Element rowData = new Element("ROWDATA");
        Document arquivo = new Document(rowDataPacket);
        rowDataPacket.addContent(rowData);
        //percorrer a lista de mapas
        for (Map<Integer, String[]> atributos : corpo) {
            Element row = new Element("ROW");
            Set<Integer> keys = atributos.keySet();
            for (Integer key : keys) {
                //adicionar ao elemento (linha), todos os registros do mapa, com a chave como atributo
                row.setAttribute(atributos.get(key)[0], atributos.get(key)[1]);
            }
            rowData.addContent(row);

        }
        XMLOutputter xout = new XMLOutputter();
        //xout.output(arquivo, System.out);
        Format formatXML = Format.getPrettyFormat();
        formatXML.setEncoding("ISO-8859-1");
        xout.setFormat(formatXML);
        FileWriter xml = new FileWriter(new File(path));
        xout.output(arquivo, xml);
    }
}

package test.simulacao;

import acesso.webservice.AcessoControle;
import br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URL;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ComposicaoVO;
import negocio.comuns.plano.PlanoComposicaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.TransacaoMovParcelaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.AutorizacaoAcessoGrupoEmpresarial;
import negocio.facade.jdbc.arquitetura.FacadeFactory;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.financeiro.TransacaoMovParcela;
import negocio.facade.jdbc.plano.Composicao;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import servicos.impl.apf.APF;
import servicos.impl.apf.RecorrenciaService;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import static servicos.impl.dcc.base.LayoutRemessaBase.preencherAtributosTransientes;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.dcc.cielo.LayoutRemessaCieloDCC;
import servicos.oamd.OamdMsService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
/**
 *
 * <AUTHOR>
 */
public class TesteCargaAcesso {

    private AcessoControle acessoControle = new AcessoControle("waller");
    private ClienteInterfaceFacade clienteFacade;

    public TesteCargaAcesso() throws Exception {
        clienteFacade = new Cliente();
    }

    public static void main(String args[]) throws Exception {
//        corrigirBiometrias();
//        corrigirReplicadoRedeEmpresa();
//        importarBiometriasParaGateway();
//        removerAutorizacoesEmpresasImportadas();


        /*List<Integer> codigosItens = new ArrayList<Integer>();
         codigosItens.add(222);
         codigosItens.add(153);
         codigosItens.add(229);
         codigosItens.add(230);
         codigosItens.add(197);
         codigosItens.add(202);
         codigosItens.add(207);
         codigosItens.add(212);
         codigosItens.add(217);
         codigosItens.add(181);
         codigosItens.add(186);
         codigosItens.add(154);
         codigosItens.add(155);
         codigosItens.add(183);
         codigosItens.add(184);
         codigosItens.add(163);
         codigosItens.add(164);
         codigosItens.add(165);
         codigosItens.add(167);
         codigosItens.add(168);
         codigosItens.add(169);
         codigosItens.add(170);
         codigosItens.add(172);
         codigosItens.add(173);
         codigosItens.add(174);
         codigosItens.add(175);
         codigosItens.add(177);
         codigosItens.add(178);
         codigosItens.add(195);
         codigosItens.add(196);
         codigosItens.add(198);
         codigosItens.add(199);
         codigosItens.add(200);
         codigosItens.add(201);
         codigosItens.add(203);
         codigosItens.add(204);
         codigosItens.add(205);
         codigosItens.add(206);
         codigosItens.add(209);
         codigosItens.add(210);
         codigosItens.add(211);
         codigosItens.add(213);
         codigosItens.add(214);
         codigosItens.add(215);
         codigosItens.add(216);
         codigosItens.add(218);
         codigosItens.add(219);
         codigosItens.add(220);
         codigosItens.add(221);
         codigosItens.add(223);
         codigosItens.add(224);
         codigosItens.add(166);
         codigosItens.add(171);
         codigosItens.add(176);
         codigosItens.add(179);
         codigosItens.add(180);
         codigosItens.add(225);
         codigosItens.add(227);
         codigosItens.add(182);
         codigosItens.add(228);
         codigosItens.add(185);

         incluirReciboDoItemDaRemessa(codigosItens);
         * */
        //TesteCargaAcesso.alterarVencimentoParcelasDeUmBancoParaOutro();
        //TesteCargaAcesso.corrigirMovPagamentoEReciboSemPessoaRecorrencia();
        //TesteCargaAcesso.corrigirTransacoesRetransmitidasQueSobreporamATransacaoPassado();        
        //TesteCargaAcesso.corrigirEncodingParametrosRespostaTransacoes();
        //TesteCargaAcesso.atualizarTodosBancos(new StringBuffer());
        //TesteCargaAcesso.gravarReciboETransacao();
        //TesteCargaAcesso.executaCommandoPosgreSQL();
        //TesteCargaAcesso.procurarPessoaEmTodosOsBancos("NADIA MOHAMAD TAHA");
        //TesteCargaAcesso.verificarExecucaoRoboEmTodosOsBancos();
        //System.out.println(Uteis.encriptar("VSL12ANBR"));
        //System.out.println(Uteis.encriptar("BR12PMGVSL"));
        //TesteCargaAcesso.restaurarCodReciboECodMovpagamento();
        //TesteCargaAcesso.verificarQuaisBancoDadosPossuemSinteticoFaltandoRegistros(new StringBuffer());
        //TesteCargaAcesso.testeConsultaPessoas("59b3744e3981bfd69dbc7bdff3c1d3a8");
        //TesteCargaAcesso.corrigirContratosAtivosSemPeriodoAcesso();
        //TesteCargaAcesso.testeConsultaPessoasCPFInvalido();
        //TesteCargaAcesso.testeConsultaPessoasNomePaiMaeVazio();
        //TesteCargaAcesso.testeCargaAcesso();
        //TesteCargaAcesso.testarIncluirContratosMatriculaNaRotatividadeAposImportacao();
        //TesteCargaAcesso.testargerarListaContratosInconformes();
        //TesteCargaAcesso.corrigirMatriculasQueNaVerdadeForamRenovacoesOuRematriculas();        
        //TesteCargaAcesso.corrigirSituacaoSinteticoDW();        
        //gerando toda movimentação de contratos de um banco de dados
        //Contrato.gerarSituacoesTemporaisContratos();        
        //TesteCargaAcesso.corrigirContratosAtivosSemPeriodoAcesso();
        //Calendario.setData(Uteis.getDate("04/05/2011"));
        //TesteCargaAcesso.testarVendaAvulsa();
        //TesteCargaAcesso.executaCommandoPosgreSQL();
        //Contrato.testarGerarSituacoesUmContrato(1030);
        //TesteCargaAcesso.validarDiferencaMovimentacaoListaClientes();
        //TesteCargaAcesso.validarMovimentacaoContratosSegundoHistorico("01/03/2011", 1);
        //TesteCargaAcesso.geraEmails();
        //TesteCargaAcesso.validarRotatividadeFinalMesEmDoisDiasDiferentes();
        //TesteCargaAcesso.validarIR();
        //TesteCargaAcesso.validarMovimentacaoContratosSegundoHistorico();
        //TesteCargaAcesso.gerarVinculosClientes();

        /*System.out.println(Uteis.getDataAplicandoFormatacao(negocio.comuns.utilitarias.Calendario.hoje(), "yyyyMMdd"));*/

        /*new ValidacaoAcessoWS().validarAcessoPeloCodigoAcesso("1003730001", 1, 2,
         "31", DirecaoAcessoEnum.DA_OESPERADO, Boolean.FALSE,
         MeioIdentificacaoEnum.DIGITAL, "2c0033a8ac79bdcff56cd499dfa7874b");*/
        //TesteCargaAcesso.executarComandoUpdateEmTodosOsBancos("update modelomensagem set mensagem = replace(mensagem, 'http://app.pactosolucoes.com.br', 'https://app.pactosolucoes.com.br')");
        //TesteCargaAcesso.executarConsultaEmTodosOsBancos("select dataExpiracao from configuracaosistema");        
        //TesteCargaAcesso.executarConsultaEmTodosOsBancos("select dia,datahorafim from robo order by codigo desc limit 1", "app.pactosolucoes.com.br");

        /*File file = new File("C://termo.htm");
         ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
         byte buffer[] = new byte[4096];
         int bytesRead = 0;
         FileInputStream fi = new FileInputStream(file);
         while ((bytesRead = fi.read(buffer)) != -1) {
         arrayOutputStream.write(buffer, 0, bytesRead);
         }
        
         arrayOutputStream.close();
         fi.close();
        
         byte[] termo = arrayOutputStream.toByteArray();        
        
         PreparedStatement stm = Conexao.getInstance().getConexao().prepareStatement("update planoTextoPadrao set termo = ?");
         stm.setBytes(1, termo);
         stm.execute();*/
        /*byte buffer[] = new byte[termo.length];
         int bytesRead = 0;
         FileInputStream fi = new FileInputStream("temp.dat");
         //chamar aqui o Jasper passando o objeto fi como parametro para o termo
         fi.close();*/
        //System.out.println(Uteis.obterPrimeiroNomeConcatenadoSobreNome("WALLER MACIEL QUEIROZ VIEIRA"));
        //System.out.println(Uteis.retirarAcentuacao("àáâãèéê~eíîì~iòóôõùúû~uüç".toUpperCase()));
        /*Calendar cal1 = Calendario.getInstance();
         Calendar cal2 = Calendario.getInstance();
         SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        
         cal1.setTime(sdf.parse("08/06/2010"));
         cal2.setTime(sdf.parse(sdf.format(negocio.comuns.utilitarias.Calendario.hoje())));
         System.out.println(Uteis.obterDiferencaEntreDatasPorExtenso(cal1.getTime(), cal2.getTime()));*/
        //System.out.println(Calendario.hoje());
        //TesteCargaAcesso.unificarTodosScriptsAtualizacoes();
        //TesteCargaAcesso.preencherAnnotationsExecutarProcesso();
        //atualizarTodosBancos(null);
        /*SuperFacadeJDBC.executarConsultaUpdate("CREATE LANGUAGE plpgsql");
         SuperFacadeJDBC.executarConsultaUpdate(MetodoEstudio.funcaoDisponibilidade());
         SuperFacadeJDBC.executarConsultaUpdate(MetodoEstudio.visaoColaboradorProduto());
         SuperFacadeJDBC.executarConsultaUpdate(MetodoEstudio.visaoContadorAmbiente());
         SuperFacadeJDBC.executarConsultaUpdate(MetodoEstudio.visaoContadorColaborador());
         SuperFacadeJDBC.executarConsultaUpdate(MetodoEstudio.visaoContador());
         SuperFacadeJDBC.executarConsultaUpdate(MetodoEstudio.insertTipoHorario());
         SuperFacadeJDBC.executarConsultaUpdate(MetodoEstudio.campoTabelaProduto());
         SuperFacadeJDBC.executarConsultaUpdate(MetodoEstudio.campoTabelaAmbiente());*/
//        TesteCargaAcesso.gerarSenhasPZWProducao();
//        executaCommandoPosgreSQL();
//        incluirReciboDaTransacao(149197);
//        incluirReciboDaTransacao(149408);
//        incluirReciboDaTransacao(149401);
//        incluirReciboDaTransacao(5636);
//        incluirReciboDaTransacao(5638);
//        restaurarHistoricoContrato();
//        excluirMovProdutosContrato(new int[]{
//                    119796,
//                    120528,120529,120530,120531,
//                    124418,124419,124420,124421,124422,
//                    124605,124606,124607,124608,124609,124610,124611,
//                    124905,124906,124907,124908,124909,124910,124911,124912,
//                    124979,124980,124981,124982,124983,124984,
//                    125768,125769,125770,125771,125772,125773,125774
//                });

        /*gravarReciboETransacao(
         "SANDRO A DE LIMA",
         79.0,
         OperadorasExternasAprovaFacilEnum.VISA,
         "73516358270838",
         "{MesValidade=5, QuantidadeParcelas=1, TransacaoAnterior=, CodigoSeguranca=805, NomePortadorCartao=SANDRO A DE LIMA, IPClientePacto=***************, CodExternoTransacaoRetransmitida=, ValorDocumento=79.0, Bandeira=VISA, EnderecoIPComprador=RecorrENtE, UrlRequest=https://www.aprovafacil.com/cgi-bin/APFW/easyfit001/APC, NumeroCartao=****************, AnoValidade=15, Moeda=BRL, NumeroDocumento=}",
         "<ResultadoAPC>"
         + "<TransacaoAprovada>True</TransacaoAprovada>"
         + "<ResultadoSolicitacaoAprovacao>Autorização - 058340 [25755829516111081001] 00</ResultadoSolicitacaoAprovacao>"
         + "<CodigoAutorizacao>058340</CodigoAutorizacao>"
         + "<Transacao>73516358270838</Transacao>"
         + "<CartaoMascarado>422200******5670</CartaoMascarado>"
         + "<NumeroDocumento></NumeroDocumento>",
         Calendario.getInstance(2013, 10, 22, 16, 11, 0, 0).getTime(),
         131643);*/
        /*gravarReciboETransacao(
         "ADONIRAN DE CARVALHO PINHE",
         79.0,
         OperadorasExternasAprovaFacilEnum.VISA,
         "73516364942825",
         "{MesValidade=5, QuantidadeParcelas=1, TransacaoAnterior=, CodigoSeguranca=582, NomePortadorCartao=ADONIRAN DE CARVALHO PINHE, IPClientePacto=***************, CodExternoTransacaoRetransmitida=, ValorDocumento=79.0, Bandeira=VISA, EnderecoIPComprador=RecorrENtE, UrlRequest=https://www.aprovafacil.com/cgi-bin/APFW/easyfit001/APC, NumeroCartao=****************, AnoValidade=18, Moeda=BRL, NumeroDocumento=}",
         "<ResultadoAPC>"
         + "<TransacaoAprovada>True</TransacaoAprovada>"
         + "<ResultadoSolicitacaoAprovacao>Autorização - 993311 [25755829518022281001] 00</ResultadoSolicitacaoAprovacao>"
         + "<CodigoAutorizacao>993311</CodigoAutorizacao>"
         + "<Transacao>73516364942825</Transacao>"
         + "<CartaoMascarado>400217******5317</CartaoMascarado>"
         + "<NumeroDocumento></NumeroDocumento>"
         + "<ComprovanteAdministradora></ComprovanteAdministradora>"
         + "<NacionalidadeEmissor></NacionalidadeEmissor>"
         + "<ResultadoAVS></ResultadoAVS>"
         + "<EnderecoAVS>"
         + "<Endereco></Endereco>"
         + "<Numero></Numero>"
         + "<Complemento></Complemento>"
         + "<Cep></Cep>"
         + "</EnderecoAVS>"
         + "</ResultadoAPC>",
         Calendario.getInstance(2013, 10, 22, 18, 02, 0, 0).getTime(),
         131667);
         */
        /*gravarReciboETransacao(
         "CREUZA SILVA",
         79.0,
         OperadorasExternasAprovaFacilEnum.MASTERCARD,
         "73516373648683",
         "{MesValidade=11, QuantidadeParcelas=1, TransacaoAnterior=, CodigoSeguranca=447, NomePortadorCartao=CREUZA SILVA, IPClientePacto=, CodExternoTransacaoRetransmitida=, ValorDocumento=79.0, Bandeira=MASTERCARD, EnderecoIPComprador=RecorrENtE, UrlRequest=https://www.aprovafacil.com/cgi-bin/APFW/easyfit001/APC, NumeroCartao=****************, AnoValidade=15, Moeda=BRL, NumeroDocumento=}",
         "<ResultadoAPC>"
         + "<TransacaoAprovada>True</TransacaoAprovada>"
         + "<ResultadoSolicitacaoAprovacao>Autorização - 091155 [25755829520272871001] 00</ResultadoSolicitacaoAprovacao>"
         + "<CodigoAutorizacao>091155</CodigoAutorizacao>"
         + "<Transacao>73516373648683</Transacao>"
         + "<CartaoMascarado>525320******8930</CartaoMascarado>"
         + "<NumeroDocumento></NumeroDocumento>"
         + "<ComprovanteAdministradora></ComprovanteAdministradora>"
         + "<NacionalidadeEmissor></NacionalidadeEmissor>"
         + "<ResultadoAVS></ResultadoAVS>"
         + "<EnderecoAVS>"
         + "<Endereco></Endereco>"
         + "<Numero></Numero>"
         + "<Complemento></Complemento>"
         + "<Cep></Cep>"
         + "</EnderecoAVS>"
         + "</ResultadoAPC>",
         Calendario.getInstance(2013, 10, 22, 20, 27, 0, 0).getTime(),
         131691);*/
//        gravarReciboETransacao(
//                "ANDRE SANTOS RODRIGUES",
//                79.0,
//                OperadorasExternasAprovaFacilEnum.VISA,
//                "73516427160637",
//                "{MesValidade=12, QuantidadeParcelas=1, TransacaoAnterior=, CodigoSeguranca=789, NomePortadorCartao=ANDRE SANTOS RODRIGUES, IPClientePacto=***************, CodExternoTransacaoRetransmitida=, ValorDocumento=79.0, Bandeira=VISA, EnderecoIPComprador=RecorrENtE, UrlRequest=https://www.aprovafacil.com/cgi-bin/APFW/easyfit001/APC, NumeroCartao=****************, AnoValidade=15, Moeda=BRL, NumeroDocumento=}",
//                "<ResultadoAPC>"
//                + "<TransacaoAprovada>True</TransacaoAprovada>"
//                + "<ResultadoSolicitacaoAprovacao>Autorização - 404015 [25755829607324061001] 00</ResultadoSolicitacaoAprovacao>"
//                + "<CodigoAutorizacao>404015</CodigoAutorizacao>"
//                + "<Transacao>73516427160637</Transacao>"
//                + "<CartaoMascarado>482425******2105</CartaoMascarado>"
//                + "<NumeroDocumento></NumeroDocumento>"
//                + "<ComprovanteAdministradora></ComprovanteAdministradora>"
//                + "<NacionalidadeEmissor></NacionalidadeEmissor>"
//                + "<ResultadoAVS></ResultadoAVS>"
//                + "<EnderecoAVS>"
//                + "<Endereco></Endereco>"
//                + "<Numero></Numero>"
//                + "<Complemento></Complemento>"
//                + "<Cep></Cep>"
//                + "</EnderecoAVS>"
//                + "</ResultadoAPC>",
//                Calendario.getInstance(2013, 10, 23, 07, 32, 0, 0).getTime(),
//                125478);
//
//        gravarReciboETransacao(
//                "ANDRE SANTOS RODRIGUES",
//                79.0,
//                OperadorasExternasAprovaFacilEnum.VISA,
//                "73516430848310",
//                "{MesValidade=12, QuantidadeParcelas=1, TransacaoAnterior=, CodigoSeguranca=789, NomePortadorCartao=ANDRE SANTOS RODRIGUES, IPClientePacto=***************, CodExternoTransacaoRetransmitida=, ValorDocumento=79.0, Bandeira=VISA, EnderecoIPComprador=RecorrENtE, UrlRequest=https://www.aprovafacil.com/cgi-bin/APFW/easyfit001/APC, NumeroCartao=****************, AnoValidade=15, Moeda=BRL, NumeroDocumento=}",
//                "<ResultadoAPC>"
//                + "<TransacaoAprovada>True</TransacaoAprovada>"
//                + "<ResultadoSolicitacaoAprovacao>Autorização - 400512 [25755829608340831001] 00</ResultadoSolicitacaoAprovacao>"
//                + "<CodigoAutorizacao>400512</CodigoAutorizacao>"
//                + "<Transacao>73516430848310</Transacao>"
//                + "<CartaoMascarado>482425******2105</CartaoMascarado>"
//                + "<NumeroDocumento></NumeroDocumento>"
//                + "<ComprovanteAdministradora></ComprovanteAdministradora>"
//                + "<NacionalidadeEmissor></NacionalidadeEmissor>"
//                + "<ResultadoAVS></ResultadoAVS>"
//                + "<EnderecoAVS>"
//                + "<Endereco></Endereco>"
//                + "<Numero></Numero>"
//                + "<Complemento></Complemento>"
//                + "<Cep></Cep>"
//                + "</EnderecoAVS>"
//                + "</ResultadoAPC>",
//                Calendario.getInstance(2013, 10, 23, 8, 34, 0, 0).getTime(),
//                125466);

        /*Connection c = new DAO().obterConexaoEspecifica("sequence");
         SuperFacadeJDBC.atualizarSequences(c);*/
//        gerarRemessaCancelamento();
//        if (args.length == 0) {
//            args = new String[]{"10.1.1.5"};
//        }
//        pesquisaAlunoTodosBancosTreino(args[0], new String[]{
//            "ADRIANO CORREIA DE SALES",
//            "ANA FLAVIA MOTTA",
//            "CAMILA CARDOSO ANDRADE",
//            "ELIZABETH A. SCHUMACKER",
//            "ERIC LUIS M ALELUIA",
//            "ERIKA APARECIDA HORIY MOTTA",
//            "EVELIZE CAROLINE DO PRADO",
//            "FLAVIA A. BAISTA",
//            "GLORIA M. PEDROSO",
//            "HEITOR NANNI GODINHO",
//            "ISABELA REGINA COMODO PIO",
//            "ISABELLA C. REBELO",
//            "ISABELLA T. ASSIS",
//            "KARILIN ELISA DIAS DA SILVA",
//            "LISANDRA DE ALBUQUERQUE",
//            "LUANA A. M. MOREIRA",
//            "MARIANA G OLIVEIRA",
//            "NATALIA CRISTINA ARAÚJO  PEREIRA",
//            "SUE E  LLEN S. DOS SANTOS"});
//        if (args.length == 0) {
//            args = new String[]{"rds-zw-9.catyoshpfj8a.us-east-1.rds.amazonaws.com"};
//            args = new String[]{"*********"};
//        }
//        pesquisaAlunoTodosBancosTreino(args[0],
//                "zillyonweb", "pactodb", false,
//                new String[]{
//            "DIANA JARDIM DA CONCEIÇÃO"});

        //copiarPlanosEntreEmpresas(1, 2);
        //copiarPlanosEntreEmpresas(1, 3);

//        String hostBD = "localhost";
//        String porta = "5432";
//        String chave = "banco";
//        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + chave;
//        String driver = "org.postgresql.Driver";
//        String userBD = "postgres";
//        String passwordBD = "pactodb";
//        Class.forName(driver);
//
//        Cliente cliente = new Cliente(DriverManager.getConnection(url, userBD, passwordBD));
//        ClienteVO clienteVO = cliente.consultarPorCodigo(1, true, Uteis.NIVELMONTARDADOS_TODOS);
//        Validador.validarAcesso(clienteVO.getCodAcesso(), clienteVO.getEmpresa().getCodigo(), 1, "1", DirecaoAcessoEnum.DA_ENTRADA, false, MeioIdentificacaoEnum.AVULSO, "banco");
//        criarPacotesPlanosEmpresaDiferente();

/*
        String hostBD = "localhost";
        String porta = "5432";
        String chave = "bdzillyonwrguarulhos";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + chave;
        String driver = "org.postgresql.Driver";
        String userBD = "postgres";
        String passwordBD = "pactodb";
        Class.forName(driver);
        Cliente cliente = new Cliente(DriverManager.getConnection(url, userBD, passwordBD));
        ClienteVO clienteVO = cliente.consultarPorCodigo(2951, true, Uteis.NIVELMONTARDADOS_TODOS);
        Validador.validarAcesso(clienteVO.getCodAcesso(), clienteVO.getEmpresa().getCodigo(), 1, "1", DirecaoAcessoEnum.DA_ENTRADA, false, MeioIdentificacaoEnum.AVULSO, "banco");
*/


//        Código para a "Feature/IN-759"


        int qtdSimultaneos = 100;
        ExecutorService executor = Executors.newFixedThreadPool(qtdSimultaneos);
        List<Future<Integer>> responses = new ArrayList<>();

        for (int i = 0; i < qtdSimultaneos; i++) {
            int finalI = i;
            responses.add(executor.submit(() -> {
                try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                    System.out.println("Iniciando requisição " + finalI + "..." + Thread.currentThread().getName());
                    HttpGet request = new HttpGet(new URI("http://localhost:8081/ZillyonWeb/prest/meta-diaria?empresa=1&dataInicio=01%2F10%2F2024&empresa=1&dataFim=31%2F10%2F2024&reloadFull=true"));
                    request.setHeader("Authorization", "bdzillyonpowerfitnessitabirito");

                    try (CloseableHttpResponse response = httpClient.execute(request)) {
                        System.out.println("Finalizado requisição " + finalI + "..." + Thread.currentThread().getName());
                        return response.getStatusLine().getStatusCode();
                    }
                }
            }));
        }

        for (Future<Integer> response : responses) {
            try {
                System.out.println("Status Code: " + response.get()); // Imprime o código de status
            } catch (ExecutionException | InterruptedException e) {
                System.err.println("Erro ao obter o status: " + e.getMessage());
            }
        }
        executor.shutdown();


    }

    private static void testeConsultaPessoasCPFInvalido() throws Exception {
        Date inicioTeste = negocio.comuns.utilitarias.Calendario.hoje();
        int nInvalidos = 0;
        List<ClienteVO> listaClientes = new ArrayList<ClienteVO>();
        try {
            ClienteInterfaceFacade clienteFacade = new Cliente();
            System.out.println("Início TESTE CPF DE CLIENTES -> " + Uteis.getDataAplicandoFormatacao(inicioTeste, "dd/MM/yyyy HH:mm:ss"));
            listaClientes = clienteFacade.consultarClientesResumidos(Uteis.NIVELMONTARDADOS_TELACONSULTA);
            inicioTeste = negocio.comuns.utilitarias.Calendario.hoje();
            StringBuffer sb = new StringBuffer();
            sb.append("<html><body><table>");
            sb.append("<b><tr><td>Matricula</td><td>Nome</td><td>Situação</td><td>CPF</td><td>Mensagem</td></tr></b>");
            for (Iterator<ClienteVO> it = listaClientes.iterator(); it.hasNext();) {
                ClienteVO clienteVO = it.next();
                try {
                    if (clienteVO.getPessoa().getCfp().trim().length() > 0) {
                        PessoaVO.validarCPF(clienteVO.getPessoa(), clienteVO.getEmpresa().getNome());
                    }
                } catch (Exception e) {
                    nInvalidos++;

                    sb.append("<tr><td>" + clienteVO.getMatricula() + "</td>");
                    sb.append("<td>" + clienteVO.getPessoa().getNome() + "</td>");
                    sb.append("<td>" + clienteVO.getSituacao() + "</td>");
                    sb.append("<td>" + clienteVO.getPessoa().getCfp() + "</td>");
                    sb.append("<td>" + e.getMessage() + ")</td></tr>\n");


                }

            }
            sb.append("</table></body></html>");
            System.out.println(sb);
            System.out.println("Total clientes -> " + nInvalidos);

        } catch (Exception ex) {
            Logger.getLogger(TesteCargaAcesso.class.getName()).log(Level.SEVERE,
                    ex.getMessage(), ex);
        }

    }

    protected void testeCargaAcesso() {
        Date inicioTeste = negocio.comuns.utilitarias.Calendario.hoje();
        List<ClienteVO> listaClientes = new ArrayList<ClienteVO>();
        try {
            System.out.println("Início TESTE ACESSO DE ALUNOS -> " + Uteis.getDataAplicandoFormatacao(inicioTeste, "dd/MM/yyyy HH:mm:ss"));
            listaClientes = clienteFacade.consultarPorSituacao(
                    "AT", 1, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            /*listaClientes = clienteFacade.consultarPorCodAcesso("1008450006", 1, false,
             Uteis.NIVELMONTARDADOS_DADOSBASICOS);*/
            inicioTeste = negocio.comuns.utilitarias.Calendario.hoje();
            for (Iterator<ClienteVO> it = listaClientes.iterator(); it.hasNext();) {
                ClienteVO clienteVO = it.next();
                tentarAcesso(clienteVO);
            }

        } catch (Exception ex) {
            Logger.getLogger(TesteCargaAcesso.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
        }
        Date terminoTeste = negocio.comuns.utilitarias.Calendario.hoje();
        System.out.println("Término -> " + Uteis.getDataAplicandoFormatacao(terminoTeste, "dd/MM/yyyy HH:mm:ss"));
        long dif = (terminoTeste.getTime() - inicioTeste.getTime());

        System.out.println("Tempo total gasto para " + listaClientes.size() + " pessoas -> " + dif);
    }

    public void tentarAcesso(ClienteVO clienteVO) {
        try {
            Date dthrInicio = negocio.comuns.utilitarias.Calendario.hoje();
            SituacaoAcessoEnum st = acessoControle.tentarAcesso(
                    clienteVO.getCodAcesso(), DirecaoAcessoEnum.DA_ENTRADA, 1, 3,
                    "1", false, false);
            Date dthrFim = negocio.comuns.utilitarias.Calendario.hoje();
            StringBuilder sb = new StringBuilder();
            sb.append("Cliente -> ").append(clienteVO.getPessoa().getNome());
            sb.append(" Cd.Acesso -> ").append(clienteVO.getCodAcesso());
            sb.append(" Situação acesso: ").append(st.getId());
            long dif = dthrFim.getTime() - dthrInicio.getTime();
            sb.append(" Tempo -> ").append(String.valueOf(dif)).append("ms");
            System.out.println(sb);
        } catch (Exception ex) {
            Logger.getLogger(TesteCargaAcesso.class.getName()).log(Level.SEVERE,
                    ex.getMessage(), ex);
        }
    }

    private static void testeConsultaPessoas(String chave) throws Exception {
        Conexao.guardarConexaoForJ2SE(new DAO().obterConexaoEspecifica(chave));
        Date inicioTeste = negocio.comuns.utilitarias.Calendario.hoje();
        List<ClienteVO> listaClientes = new ArrayList<ClienteVO>();
        try {
            ClienteInterfaceFacade clienteFacade = new Cliente();
            System.out.println("Início -> " + Uteis.getDataAplicandoFormatacao(inicioTeste, "dd/MM/yyyy HH:mm:ss"));
            listaClientes = clienteFacade.consultarClientesResumidos(Uteis.NIVELMONTARDADOS_TODOS);
            inicioTeste = negocio.comuns.utilitarias.Calendario.hoje();
            StringBuffer sb = new StringBuffer();
            sb.append("<html>");
            sb.append("<body><table style=\"border:none;font: 8pt arial; margin:1px;\">");
            sb.append("<b><tr><td><b>S.</b></td><td><b>Matricula</b></td><td><b>Nome</b></td><td><b>Situação</b></td><td><b>Email</b></td><td><b>Endereço</b></td><td><b>Cidade</b></td><td><b>Estado</b></td><td><b>Telefone</b></td></tr></b>");
            int c = 1;
            for (Iterator<ClienteVO> it = listaClientes.iterator(); it.hasNext();) {
                ClienteVO clienteVO = it.next();
                try {
                    boolean par = false;
                    if (c % 2 == 0) {
                        par = true;

                    }
                    String clazz = par ? " bgcolor=\"#E1F1F5\"" : "";
                    sb.append("<tr").append(clazz).append(">");
                    sb.append("<td>").append(c).append("</td>");
                    sb.append("<td>").append(clienteVO.getMatricula()).append("</td>");
                    sb.append("<td>").append(clienteVO.getPessoa().getNome()).append("</td>");
                    sb.append("<td>").append(clienteVO.getSituacao()).append("</td>");
                    sb.append("<td>").append(clienteVO.getPessoa().getEmail()).append("</td>");
                    List<EnderecoVO> listaEnderecos = clienteVO.getPessoa().getEnderecoVOs();
                    String enderecos = "";
                    for (EnderecoVO enderecoVO : listaEnderecos) {
                        enderecos += String.format("%s, %s - CEP.: %s ",
                                new Object[]{enderecoVO.getEndereco(), enderecoVO.getBairro(), enderecoVO.getCep()});
                    }
                    sb.append("<td>").append(enderecos).append("</td>");
                    sb.append("<td>").append(clienteVO.getPessoa().getCidade().getNome()).append("</td>");
                    sb.append("<td>").append(clienteVO.getPessoa().getCidade().getEstado().getSigla()).append("</td>");
                    List<TelefoneVO> listaTelefones = clienteVO.getPessoa().getTelefoneVOs();
                    String telefones = "";
                    for (TelefoneVO telefoneVO : listaTelefones) {
                        telefones += telefoneVO.getNumero() + " / ";
                    }
                    sb.append("<td>").append(telefones).append("</td>");
                    sb.append("</tr>\n");

                } catch (Exception e) {
                    System.out.println("Erro -> " + e.getMessage());
                }
                c++;

            }
            sb.append("</table></body></html>");
            System.out.println(sb);

        } catch (Exception ex) {
            Logger.getLogger(TesteCargaAcesso.class.getName()).log(Level.SEVERE,
                    ex.getMessage(), ex);
        }

    }

    /*private static void testarIncluirContratosMatriculaNaRotatividadeAposImportacao() throws Exception {
     ArrayList<ContratoVO> lista = (ArrayList<ContratoVO>) FacadeManager.getFacade().getContrato().consultarPorCodigo(0,
     false, Uteis.NIVELMONTARDADOS_INDICERENOVACAO);
     int nContratos = 0;
     for (Iterator<ContratoVO> it = lista.iterator(); it.hasNext();) {
     ContratoVO contratoVO = it.next();
     FacadeManager.getFacade().getContrato().inicializarRotatividadeAnalitico(contratoVO);
     nContratos++;
     }
    
     }*/
    private static void gerarRotatividadeAnaliticoAPartirHistoricoContrato() throws Exception {
//        FacadeManager.getFacade().getContrato().gerarRotatividadeAnaliticoAPartirHistoricoContrato();
    }

    private static void testargerarListaContratosInconformes() throws Exception {
        FacadeManager.getFacade().getContrato().gerarListaContratosInconformes();
    }

    private static void gerarVinculosClientes() throws Exception {
        List<ClienteVO> clientes = (ArrayList<ClienteVO>) FacadeManager.getFacade().getCliente().consultarClientesResumidos(
                Uteis.NIVELMONTARDADOS_MINIMOS);

        for (ClienteVO cli : clientes) {
            FacadeManager.getFacade().getVinculo().alterarVinculo(cli.getCodigo(), cli.getUsuarioResponsavelVinculo(),
                    FacadeManager.getFacade().getVinculo().consultarPorCodigoCliente(cli.getCodigo(),
                    Uteis.NIVELMONTARDADOS_TODOS, true), "TESTE CARGA ACESSO");
        }


    }

    public static void geraEmails() {
        try {
            Conexao.guardarConexaoForJ2SE(new DAO().obterConexaoEspecifica("59b3744e3981bfd69dbc7bdff3c1d3a8"));
            List<ClienteVO> lista = FacadeManager.getFacade().getCliente().consultar(
                    "pessoa in (select p.codigo from pessoa p "
                    + "inner join email m on m.pessoa = p.codigo where p.sexo = 'F' "
                    + "and p.estadocivil='S' and m.email like ('%@hotmail.com') "
                    + "and length(p.foto) > 0)", Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            System.out.println("<?xml version=\"1.0\"?>");
            System.out.println("    <messenger>");
            System.out.println("        <service name=\".NET Messenger Service\">");
            System.out.println("            <contactlist>");



            for (ClienteVO clienteVO : lista) {
                System.out.println("             <contact type=\"1\">" + clienteVO.getPessoa().getEmail() + "</contact>");
            }
            System.out.println("            </contactlist>");
            System.out.println("        </service>");
            System.out.println("    </messenger>");

        } catch (Exception ex) {
            Logger.getLogger(TesteCargaAcesso.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void testarVendaAvulsa() {
        VendaAvulsaVO venda = new VendaAvulsaVO();
        ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
        item.setValorParcial(60.0);
        venda.getItemVendaAvulsaVOs().add(item);
        System.out.println(venda.getValorTotal());
    }

    private static void corrigirContratosAtivosSemPeriodoAcesso() throws Exception {
        String sql = "select codigo,pessoa from contrato "
                + "where codigo not in (select coalesce(contrato,0) from periodoacessocliente) "
                + "and codigo not in (select contrato from historicocontrato where tipohistorico = 'CA')";
        ResultSet rs = null;
        try {
            rs = SuperFacadeJDBC.criarConsulta(sql,FacadeManager.getFacade().getRisco().getCon());
            FacadeManager.getFacade().getRisco().getCon().setAutoCommit(false);

            while (rs.next()) {
                int codContrato = rs.getInt("codigo");
                System.out.println(codContrato);
                ContratoVO contrato = FacadeManager.getFacade().getContrato().
                        consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                PeriodoAcessoClienteVO periodo = new PeriodoAcessoClienteVO();
                periodo.setContrato(codContrato);

                if (contrato.getContratoBaseadoRenovacao() != 0) {
                    periodo.setContratoBaseadoRenovacao(contrato.getContratoBaseadoRenovacao());
                }

                periodo.setPessoa(contrato.getPessoa().getCodigo());
                periodo.setDataInicioAcesso(contrato.getVigenciaDe());
                periodo.setDataFinalAcesso(contrato.getVigenciaAteAjustada());
                periodo.setTipoAcesso("CA");
                FacadeManager.getFacade().getPeriodoAcessoCliente().incluir(periodo);
                System.out.println("Contrato " + periodo.getContrato());
            }
            FacadeManager.getFacade().getRisco().getCon().commit();
        } catch (Exception ex) {
            FacadeManager.getFacade().getRisco().getCon().rollback();
        }
    }

    public static void alterarVencimentoParcelasDeUmBancoParaOutro() throws Exception {
        Connection conOrigem = new Conexao("*************************************************", "postgres", "pactodb").getConexao();
        Connection conDestino = new Conexao("*************************************************-destino", "postgres", "pactodb").getConexao();
        ResultSet rsOrigem = SuperFacadeJDBC.criarConsulta("select * from movparcela where regimerecorrencia order by codigo", conOrigem);
        while (rsOrigem.next()) {
            Date dataVenc = rsOrigem.getDate("dataVencimento");
            boolean ret = SuperFacadeJDBC.executarConsulta("update movparcela set dataVencimento = '" + Uteis.getDataFormatoBD(dataVenc)
                    + "' where codigo = " + rsOrigem.getInt("codigo"), conDestino);
            System.out.println("Atualizou movparcela " + rsOrigem.getInt("codigo") + " venc para: " + Uteis.getData(dataVenc) + " -> " + ret);
        }

    }

    public static void corrigirMovPagamentoEReciboSemPessoaRecorrencia() throws Exception {
        Connection conOrigem = new Conexao("*************************************************", "postgres", "pactodb").getConexao();
        String sql = "select * from movpagamento "
                + "where codigo in (select movpagamento from pagamentomovparcela  "
                + "where movparcela in (select codigo from movparcela "
                + "where regimerecorrencia  is true)) and pessoa is null order by codigo";


        ResultSet rsOrigem = SuperFacadeJDBC.criarConsulta(sql, conOrigem);
        while (rsOrigem.next()) {

            int codRecibo = rsOrigem.getInt("recibopagamento");
            int codMovPagamento = rsOrigem.getInt("codigo");

            ResultSet rsPessoa = SuperFacadeJDBC.criarConsulta(
                    "select pessoa from contrato where codigo = "
                    + "(select contrato from recibopagamento  where codigo = " + codRecibo + ")", conOrigem);
            if (rsPessoa.next()) {
                int codPessoa = rsPessoa.getInt("pessoa");
                SuperFacadeJDBC.executarConsulta(
                        "update movpagamento set pessoa = " + codPessoa + " where codigo = " + codMovPagamento, conOrigem);
                System.out.println("Atualizou movpagamento " + codMovPagamento);
                SuperFacadeJDBC.executarConsulta(
                        "update recibopagamento set pessoapagador = " + codPessoa + " where codigo = " + codRecibo, conOrigem);
                System.out.println("Atualizou recibopagamento " + codRecibo);

            }
        }
    }

    public static void corrigirTransacoesRetransmitidasQueSobreporamATransacaoPassado() throws Exception {
        Connection conPassado = new Conexao("*************************************************anterior", "postgres", "pactodb").getConexao();
        Connection conPresente = new Conexao("*************************************************", "postgres", "pactodb").getConexao();
        Transacao transacaoPassadoFacade = new Transacao(conPassado);
        Transacao transacaoPresenteFacade = new Transacao(conPresente);
        TransacaoMovParcela transacaoMovParcelaFacade = new TransacaoMovParcela(conPresente);
        ReciboPagamento reciboFacade = new ReciboPagamento(conPresente);


        String sql = "select * from transacao where recibopagamento in (select codigo from recibopagamento "
                + "where codigo in (select recibopagamento from transacao "
                + "where transacao.recibopagamento = recibopagamento.codigo and "
                + "cast(recibopagamento.data as date) <> cast(transacao.dataprocessamento as date))) order by codigo";


        ResultSet rsPresente = SuperFacadeJDBC.criarConsulta(sql, conPresente);

        List<TransacaoVO> listaTransPresente = new ArrayList();
        //listaTransPresente = Transacao.montarDadosConsulta(rsPresente);
        List<TransacaoVO> listaPresenteModificavel = new ArrayList(listaTransPresente);
        List<TransacaoVO> listaNovasTransacoes = new ArrayList();

        for (TransacaoVO transPresente : listaTransPresente) {
            TransacaoVO transacaoNova = (TransacaoVO) transPresente.getClone(true);
            transacaoNova.setListaParcelas(new ArrayList());
            TransacaoVO transPassado = null;
            try {
                transPassado = transacaoPassadoFacade.consultarPorChavePrimaria(
                        transPresente.getCodigo());
            } catch (Exception e) {
                System.out.println("WARNING -> " + e.getMessage());
                continue;
            }

            List<TransacaoMovParcelaVO> listaTransacaoMovParcela =
                    transacaoMovParcelaFacade.cosultarPorCodigoTransacao(transPresente.getCodigo());
            for (TransacaoMovParcelaVO transacaoMovParcelaVO : listaTransacaoMovParcela) {
                transacaoNova.getListaParcelas().add(transacaoMovParcelaVO.getMovParcela());
                transPassado.getListaParcelas().add(transacaoMovParcelaVO.getMovParcela());
            }

            ReciboPagamentoVO recibo = reciboFacade.consultarPorChavePrimaria(transacaoNova.getReciboPagamento(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            transacaoNova.setUsuarioResponsavel(recibo.getResponsavelLancamento());
            transacaoNova.setDataProcessamento(recibo.getData());
            listaNovasTransacoes.add(transacaoNova);



            System.out.println("============================ATUAL============================");
            System.out.println(transPresente.toString());

            System.out.println("===========================PASSADO============================");
            System.out.println(transPassado.toString());

            TransacaoVO t = listaPresenteModificavel.get(listaPresenteModificavel.indexOf(transPresente));
            t = transPassado;
            t.setSituacao(SituacaoTransacaoEnum.DESCARTADA);
            transacaoPresenteFacade.alterar(t);

        }

        System.out.println("\nAlteradas " + listaTransPresente.size() + " transações.");

        for (TransacaoVO transNova : listaNovasTransacoes) {
            System.out.println("============================INCLUSÃO============================");
            System.out.println(transNova.toString());
            transacaoPresenteFacade.incluir(transNova);
            System.out.println("============================FIM-INCLUSÃO============================");

        }

        System.out.println("\nIncluídas " + listaNovasTransacoes.size() + " novas transações.");

    }

    public static void restaurarCodReciboECodMovpagamento() throws Exception {
        Connection conPassado = new Conexao("*************************************************", "postgres", "pactodb").getConexao();
        Connection conPresente = new Conexao("*************************************************", "postgres", "pactodb").getConexao();



        String sql = "select * from transacao order by codigo";


        ResultSet rsPassado = SuperFacadeJDBC.criarConsulta(sql, conPassado);
        while (rsPassado.next()) {
            int codReciboAnterior = rsPassado.getInt("recibopagamento");
            int codMovPagamentoAnterior = rsPassado.getInt("movpagamento");
            int pk = rsPassado.getInt("codigo");
            String updateRecibo = null;
            String updateMovPagamento = null;
            if (codMovPagamentoAnterior == 0) {
                updateRecibo = "update transacao set recibopagamento=null where codigo=" + pk;
            } else {
                updateRecibo = "update transacao set recibopagamento=" + codReciboAnterior + " where codigo=" + pk;
            }
            SuperFacadeJDBC.executarConsulta(updateRecibo, conPresente);

            if (codMovPagamentoAnterior == 0) {
                updateMovPagamento = "update transacao set movpagamento=null where codigo=" + pk;
            } else {
                updateMovPagamento = "update transacao set movpagamento=" + codMovPagamentoAnterior + " where codigo=" + pk;
            }
            SuperFacadeJDBC.executarConsulta(updateMovPagamento, conPresente);

            System.out.println("Atualizado -> " + pk);

        }


    }

    public static void corrigirEncodingParametrosRespostaTransacoes() throws Exception {
        String sqlTodos = "select * from transacao where length(paramsresposta) > 0 and substring(paramsresposta from 1 for 1) <> '<'";

        Connection connBase = new Conexao("*************************************************", "postgres", "pactodb").getConexao();
        Connection connBackup = new Conexao("*************************************************", "postgres", "pactodb").getConexao();
        try {
            ResultSet rsPresente = SuperFacadeJDBC.criarConsulta(sqlTodos, connBase);

            List<TransacaoVO> listaTransPresente = new ArrayList();
            Transacao transacaoDAO = new Transacao(connBase);
            listaTransPresente = transacaoDAO.montarDadosConsulta(rsPresente, connBase);
            transacaoDAO = null;
            int i = 1;
            for (TransacaoVO transacaoVO : listaTransPresente) {
                try {
                    ResultSet rs = SuperFacadeJDBC.criarConsulta("select paramsresposta from transacao where codigo = "
                            + transacaoVO.getCodigo() + " and codigoexterno = '" + transacaoVO.getCodigoExterno() + "'",
                            connBackup);
                    if (rs.next()) {
                        String paramAnterior = rs.getString("paramsresposta");
                        transacaoVO.setParamsResposta(paramAnterior);
                        SuperFacadeJDBC.executarConsulta("update transacao "
                                + "set paramsresposta = '" + transacaoVO.getParamsResposta()
                                + "' where codigo = " + transacaoVO.getCodigo().intValue()
                                + " and codigoexterno = '" + transacaoVO.getCodigoExterno() + "'", connBase);
                    }

                } catch (Exception e) {
                    System.out.println("ERRO -> " + transacaoVO.getCodigo() + " " + e.getMessage());
                }

            }
        } finally {
        }
    }

    public static void corrigirEmails() throws Exception {
        String sqlTodos = "select * from email";

        Connection connBase = new Conexao("*********************************************************", "postgres", "pactodb").getConexao();
        Connection connBackup = new Conexao("*********************************************************", "postgres", "pactodb").getConexao();
        try {
            ResultSet rsPresente = SuperFacadeJDBC.criarConsulta(sqlTodos, connBackup);



            while (rsPresente.next()) {
                String email = rsPresente.getString("email");
                int codigo = rsPresente.getInt("codigo");
                try {
                    SuperFacadeJDBC.executarConsulta(
                            "update email set email ='"
                            + email + "' where codigo = " + codigo, connBase);
                    System.out.println(email);

                } catch (Exception e) {
                    System.out.println("EXC -> " + e.getMessage());
                }


            }

        } finally {
        }
    }

    public static void gravarReciboETransacao(final String titular, double valor, OperadorasExternasAprovaFacilEnum operadora,
            final String codigoExterno,
            final String paramsEnvio, final String paramsResposta, final Date dataProcessamento, int movparcela) throws Exception {
        Connection con = new Conexao("*******************************************************************",
                "postgres", "pactodb").getConexao();
        Conexao.guardarConexaoForJ2SE(con);
        //
        RecorrenciaService recorrenciaService = new RecorrenciaService(con);
        MovParcela movParcelaDAO = new MovParcela(con);
        Transacao transacaoDAO = new Transacao(con);
        List<MovPagamentoVO> listaPagamento = new ArrayList();
        List<MovParcelaVO> listaParcelas = new ArrayList();

        MovParcelaVO movParcela = movParcelaDAO.consultarPorChavePrimaria(movparcela, Uteis.NIVELMONTARDADOS_TODOS);
        listaParcelas.add(movParcela);
        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
        movPagamentoVO.setFormaPagamento(recorrenciaService.getFormaPagamento());
        movPagamentoVO.setMovPagamentoEscolhida(true);
        movPagamentoVO.setValor(movParcela.getValorParcela());
        movPagamentoVO.setValorTotal(movParcela.getValorParcela());
        movPagamentoVO.setPessoa(movParcela.getContrato().getPessoa());
        movPagamentoVO.setNomePagador(movParcela.getContrato().getPessoa().getNome());
        movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
        movPagamentoVO.setNrParcelaCartaoCredito(1);
        movPagamentoVO.setOperadoraCartaoVO(
                recorrenciaService.obterOperadoraPorEnum(
                operadora));
        movPagamentoVO.setResponsavelPagamento(recorrenciaService.getUsuario());
        movPagamentoVO.setEmpresa(movParcela.getEmpresa());
        listaPagamento.add(movPagamentoVO);

        ReciboPagamentoVO reciboObj = recorrenciaService.getMovPagamentoDAO().incluirListaPagamento(
                listaPagamento,
                listaParcelas,
                null,
                movParcela.getContrato(),
                false, 0.0);
        System.out.println("Incluído recibo de Nº " + reciboObj.getCodigo());

        TransacaoVO transacao = new TransacaoVO();
        transacao.setCodigoExterno(codigoExterno);
        transacao.setEmpresa(movParcela.getEmpresa().getCodigo());
        transacao.setDataProcessamento(dataProcessamento);
        transacao.setMovPagamento(movPagamentoVO.getCodigo());

        transacao.setNomePessoa(titular);
        //"{QuantidadeParcelas=1, TransacaoAnterior=73443602909210, NomePortadorCartao=AGENOR P DA SILVA, IPClientePacto=, CodExternoTransacaoRetransmitida=73446703132218, ValorDocumento=69.0, NumeroDocumento=, Moeda=BRL, Bandeira=VISA, UrlRequest=https://www.aprovafacil.com/cgi-bin/APFW/easyfit001/APC, EnderecoIPComprador=RecorrENtE}"
        transacao.setParamsEnvio(paramsEnvio);

        transacao.setParamsResposta(paramsResposta
                + "<ComprovanteAdministradora></ComprovanteAdministradora>"
                + "<NacionalidadeEmissor></NacionalidadeEmissor>"
                + "<ResultadoAVS></ResultadoAVS>"
                + "<EnderecoAVS>"
                + "<Endereco></Endereco>"
                + "<Numero></Numero>"
                + "<Complemento></Complemento>"
                + "<Cep></Cep>"
                + "</EnderecoAVS>"
                + "</ResultadoAPC>");

        final String resultadoCap = String.format("<ResultadoCAP>"
                + "<ResultadoSolicitacaoConfirmacao>"
                + "Confirmado %s"
                + "</ResultadoSolicitacaoConfirmacao>"
                + "<ComprovanteAdministradora>"
                + "</ComprovanteAdministradora>"
                + "</ResultadoCAP>", new Object[]{codigoExterno});
        transacao.setResultadoCaptura(resultadoCap);

        transacao.setReciboPagamento(reciboObj.getCodigo());

        transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
        transacao.setTipo(TipoTransacaoEnum.AprovaFacilCB);
        transacao.setUsuarioResponsavel(recorrenciaService.getUsuario());
        transacao.setValor(valor);
        transacao.setListaParcelas(listaParcelas);
        transacaoDAO.incluir(transacao);
        System.out.println("Incluída transação nº " + transacao.getCodigo());

    }

    public static void incluirReciboDaTransacao(int codigoTransacao) throws Exception {
        Connection con = new Conexao("*************************************************2410",
                "postgres", "pactodb").getConexao();
        Conexao.guardarConexaoForJ2SE(con);
        //
        RecorrenciaService recorrenciaService = new RecorrenciaService(con);
        Transacao transacaoDAO = new Transacao(con);
        TransacaoMovParcela transMovParcela = new TransacaoMovParcela(con);
        MovParcela movParcela = new MovParcela(con);
        List<TransacaoMovParcelaVO> listaTransacoesParcelas = transMovParcela.cosultarPorCodigoTransacao(codigoTransacao);
        TransacaoVO transacao = transacaoDAO.consultarPorChavePrimaria(codigoTransacao);
        if (transacao.getCodigo().intValue() != 0) {
            Calendario.dia = (Date) transacao.getDataProcessamento().clone();
            List<MovPagamentoVO> listaPagamento = new ArrayList();
            List<MovParcelaVO> listaParcelas = new ArrayList();
            for (TransacaoMovParcelaVO tMovParcela : listaTransacoesParcelas) {
                listaParcelas.add(movParcela.consultarPorChavePrimaria(tMovParcela.getMovParcela().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            }

            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setFormaPagamento(recorrenciaService.getFormaPagamento());
            movPagamentoVO.setMovPagamentoEscolhida(true);
            movPagamentoVO.setValor(transacao.getValor());
            movPagamentoVO.setValorTotal(transacao.getValor());
            movPagamentoVO.setPessoa(listaParcelas.get(0).getContrato().getPessoa());
            movPagamentoVO.setNomePagador(listaParcelas.get(0).getContrato().getPessoa().getNome());
            movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
            movPagamentoVO.setNrParcelaCartaoCredito(1);
            movPagamentoVO.setOperadoraCartaoVO(
                    recorrenciaService.obterOperadoraPorEnum(
                    OperadorasExternasAprovaFacilEnum.valueOf(transacao.getValorAtributoEnvio(APF.Bandeira))));
            movPagamentoVO.setResponsavelPagamento(transacao.getUsuarioResponsavel());
            movPagamentoVO.setEmpresa(listaParcelas.get(0).getEmpresa());
            listaPagamento.add(movPagamentoVO);

            ReciboPagamentoVO reciboObj = recorrenciaService.getMovPagamentoDAO().incluirListaPagamento(
                    listaPagamento,
                    listaParcelas,
                    null,
                    listaParcelas.get(0).getContrato(),
                    false, 0.0);
            System.out.println("Incluído recibo de Nº " + reciboObj.getCodigo());

            transacao.setMovPagamento(movPagamentoVO.getCodigo());

            transacao.setReciboPagamento(reciboObj.getCodigo());
            transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);

            transacaoDAO.alterar(transacao);
            System.out.println("Alterada transação nº " + transacao.getCodigo());
        }

    }

    public static void executaCommandoPosgreSQL() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("delete from robo where cast(dia as date) >= current_date;");
        sql.append("update empresa set urlrecorrencia = null;");
        sql.append("update configuracaosistema  set urlrecorrencia = 'https://teste.aprovafacil.com/cgi-bin/APFW/pactosolucoes/APC',dataUltimaRepescagem=null, qtdDiasRepetirCobrancaParcelasRecorrencia=-1;");
        sql.append("delete from emailsrecorrencia;");
        sql.append("insert into emailsrecorrencia(email)values('<EMAIL>');");

        SuperFacadeJDBC.executarConsulta(sql.toString(),FacadeManager.getFacade().getRisco().getCon());

    }

    public static void corrigirBiometrias() {
        try {
            Connection conOrigem = new Conexao("******************************************************************", "postgres", "pactodb").getConexao();
            Connection conDestino = new Conexao("************************************************************************", "postgres", "pactodb").getConexao();;

            long a = System.currentTimeMillis();
            System.out.println("Consultando clientes na chave de origem");
            Map<Integer, ClienteVO> clientes = new HashMap<>();
            String sql = "select cli.codigo as codcliente, cli.codacesso, pes.cfp as cpf, pes.assinaturadigitalbiometria, pes.codigo as codpessoa from cliente cli\n" +
                    "inner join pessoa pes on cli.pessoa = pes.codigo";
            try (PreparedStatement ps = conOrigem.prepareStatement(sql)) {
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        ClienteVO clienteVO = new ClienteVO();
                        clienteVO.setCodigo(rs.getInt("codcliente"));
                        clienteVO.setCodAcesso(rs.getString("codacesso"));
                        clienteVO.getPessoa().setCodigo(rs.getInt("codpessoa"));
                        clienteVO.getPessoa().setCfp(rs.getString("cpf"));
                        clienteVO.getPessoa().setAssinaturaBiometriaDigital(rs.getString("assinaturadigitalbiometria"));
                        clientes.put(clienteVO.getPessoa().getCodigo(), clienteVO);
                    }
                }
            }
            System.out.println("Finalizada a consuta de clientes na chave de origem");

            System.out.println("Consultando autorizacoes de acesso na chave destino");
            AutorizacaoAcessoGrupoEmpresarial autorizacaoAcessoGrupoEmpresarialDAO = new AutorizacaoAcessoGrupoEmpresarial(conDestino);
            List<AutorizacaoAcessoGrupoEmpresarialVO> autorizacoes = autorizacaoAcessoGrupoEmpresarialDAO.consultarPorIntegracaoAcesso(2);
            for (AutorizacaoAcessoGrupoEmpresarialVO aut : autorizacoes) {
                ClienteVO cliente = clientes.get(aut.getCodigoPessoa());
                if (cliente != null) {
                    aut.setAssinaturaBiometriaDigital(cliente.getPessoa().getAssinaturaBiometriaDigital());
                    aut.setCpf(cliente.getPessoa().getCfp());
                    aut.setCodAcesso("NU" + cliente.getCodAcesso());
                    autorizacaoAcessoGrupoEmpresarialDAO.atualizarCamposEspecificos(aut);
                    System.out.println("Atualizado o código " + aut.getCodigo());
//                    break;
                }
            }
            long b = System.currentTimeMillis();
            System.out.println("Processo finalizado em " + (b - a) + "ms");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void corrigirReplicadoRedeEmpresa() {
        try {
            Integer redeEmpresaId = 467; // 467 Engenharia do Corpo
            Connection conOAMD = new Conexao("*****************************************************", "postgres", "pactodb").getConexao();;
            String chaveIgnorar = "";

            Connection conGateway = null;
            ResultSet rsGateWay = SuperFacadeJDBC.criarConsulta("select 'jdbc:postgresql://' || e.\"hostBD\" || ':' || e.porta || '/' || e.\"nomeBD\" as urlcon from empresa e \n" +
                    " inner join empresafinanceiro ef on ef.chavezw = e.chave \n" +
                    " inner join redeempresa r on r.id = ef.redeempresa_id \n" +
                    "where r.id = " + redeEmpresaId + "\n" +
                    "and e.chave = r.chavefranqueadora", conOAMD);
            if (rsGateWay.next()) {
                conGateway = new Conexao(rsGateWay.getString("urlcon"), "postgres", "pactodb").getConexao();
            } else {
                throw new Exception("Empresa franqueadora\\Gateway não encontrada!");
            }
            String sqlUnidades = "select \n" +
                    " e.chave,\n" +
                    " 'jdbc:postgresql://' || e.\"hostBD\" || ':' || e.porta || '/' || e.\"nomeBD\" as urlcon,\n" +
                    " ef.redeempresa_id,\n" +
                    " r.nome as nome_redeempresa\n" +
                    "from empresa e\n" +
                    " inner join empresafinanceiro ef on ef.chavezw = e.chave\n" +
                    " inner join redeempresa r on r.id = ef.redeempresa_id \n" +
                    "where ef.redeempresa_id = " + redeEmpresaId + "\n" +
                    "and coalesce(ef.chavezw,'') <> '' \n" +
                    "and e.chave <> r.chavefranqueadora \n" +
                    "and e.ativa is true \n" +
                    "and e.usoteste is false \n";

            if (UteisValidacao.emptyString(chaveIgnorar)) {
                sqlUnidades += "and e.chave not in (" + chaveIgnorar + ") \n";
            }

            ResultSet rsEmpresas = SuperFacadeJDBC.criarConsulta(sqlUnidades, conOAMD);

            while (rsEmpresas.next()) {
                Connection conOrigem = new Conexao(rsEmpresas.getString("urlcon"), "postgres", "pactodb").getConexao();

                long a = System.currentTimeMillis();
                System.out.println(conOrigem.getCatalog() + " - Consultando clientes na chave de origem");
                List<Integer> codigosPessoasSincronizados = new ArrayList<>();
                String sql = "select pessoa from cliente where sincronizadoredeempresa is not null;";
                try (PreparedStatement ps = conOrigem.prepareStatement(sql)) {
                    try (ResultSet rs = ps.executeQuery()) {
                        while (rs.next()) {
                            codigosPessoasSincronizados.add(rs.getInt("pessoa"));
                        }
                    }
                }
                System.out.println("\tFinalizada a consuta de clientes na chave de origem");


                String sqlFranqueadora = "select codigopessoa from autorizacaoacessogrupoempresarial a\n" +
                        " inner join integracaoacessogrupoempresarial i on i.codigo = a.integracaoacessogrupoempresarial \n" +
                        "where i.chave = '" + rsEmpresas.getString("chave") + "';";

                System.out.println(conGateway.getCatalog() + " - Consultando autorizacoes de acesso na chave destino(Franqueadora\\GateWay)");
                List<Integer> codigosPessoasGateway = new ArrayList<>();
                try (PreparedStatement ps = conGateway.prepareStatement(sqlFranqueadora)) {
                    try (ResultSet rs = ps.executeQuery()) {
                        while (rs.next()) {
                            codigosPessoasGateway.add(rs.getInt("codigopessoa"));
                        }
                    }
                }

                List<String> pessoasLimparSincronizacao = new ArrayList<>();
                //Encontrar quem foi sincronizado e não está...
                for (Integer pessoaUnidade : codigosPessoasSincronizados) {
                    boolean estaNoGateway = false;
                    for (Integer pessoaGateway : codigosPessoasGateway) {
                        if (pessoaGateway.equals(pessoaUnidade)) {
                            estaNoGateway = true;
                            break;
                        }
                    }
                    if (!estaNoGateway) {
                        pessoasLimparSincronizacao.add(pessoaUnidade.toString());
                    }
                }

                if (!pessoasLimparSincronizacao.isEmpty()) {
                    String sqlLimparSincronizacao = "update cliente set sincronizadoredeempresa = null where pessoa IN (" + String.join(",", pessoasLimparSincronizacao) + ");";
                    try (PreparedStatement psUpdate = conOrigem.prepareStatement(sqlLimparSincronizacao)) {
                        psUpdate.execute();
                    }
                }

                long b = System.currentTimeMillis();
                System.out.println("\tProcesso finalizado em " + (b - a) + "ms. Total de pessoas para limpar: " + pessoasLimparSincronizacao.size());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void removerAutorizacoesEmpresasImportadas() {
        try {
            Connection conGateway = new Conexao("************************************************************************", "postgres", "pactodb").getConexao();
            final Integer codigoIntegracaoBDEvo = 2;

            Connection conBDIntegracao = new Conexao("******************************************************************", "postgres", "pactodb").getConexao();

            List<String> codigoIntegracaoMember = new ArrayList<>();
            codigoIntegracaoMember.add("11"); //BGT30-DESVIO RIZZO-28
            codigoIntegracaoMember.add("2"); //SERRANO - 47
            codigoIntegracaoMember.add("28"); //SHOPPING-4
            codigoIntegracaoMember.add("18"); //LOURDES-96
            codigoIntegracaoMember.add("4"); //ESPLANADA-3
            codigoIntegracaoMember.add("3"); //MATRIZ-1

            List<String> pessoasIntegracaoEVOtoRemove = new ArrayList<>();
            String sql = "select cli.pessoa from \"member\" m \n" +
                    "inner join integracaomember im on m.integracaomember = im.codigo \n" +
                    "inner join cliente cli on m.idmember = cli.codigomatricula \n" +
                    "where im.codigo in (" + String.join(",", codigoIntegracaoMember) + ");";
            try (PreparedStatement ps = conBDIntegracao.prepareStatement(sql)) {
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        pessoasIntegracaoEVOtoRemove.add(String.valueOf(rs.getInt("pessoa")));
                    }
                }
            }

            if (!pessoasIntegracaoEVOtoRemove.isEmpty()) {
                String sqlRemoveGateway = "DELETE FROM autorizacaoacessogrupoempresarial WHERE integracaoacessogrupoempresarial = %d and codigopessoa IN (%s)";
                try (Statement statement = conGateway.createStatement()) {
                    statement.execute(String.format(sqlRemoveGateway, codigoIntegracaoBDEvo, String.join(",", pessoasIntegracaoEVOtoRemove)));
                }
            }

            if (!codigoIntegracaoMember.isEmpty()) {
                String sqlRemoveMemberBDIntegracao = "DELETE FROM \"member\" WHERE integracaomember IN (%s);";
                try (Statement statement = conBDIntegracao.createStatement()) {
                    statement.execute(String.format(sqlRemoveMemberBDIntegracao, String.join(",", codigoIntegracaoMember)));
                }

                String sqlRemoveIntegracaoBDIntegracao = "DELETE FROM integracaomember WHERE codigo IN (%s);";
                try (Statement statement = conBDIntegracao.createStatement()) {
                    statement.execute(String.format(sqlRemoveIntegracaoBDIntegracao, String.join(",", codigoIntegracaoMember)));
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void importarBiometriasParaGateway() {
        try {
            Connection conGateway = new Conexao("************************************************************************", "postgres", "pactodb").getConexao();

            Map<Integer, String> integracoesUrlConexoesMap = new HashMap<>();
            integracoesUrlConexoesMap.put(3, "*********************************************************************");
            integracoesUrlConexoesMap.put(4, "************************************************************************");
            integracoesUrlConexoesMap.put(5, "*********************************************************************");
            integracoesUrlConexoesMap.put(6, "***********************************************************************");
            integracoesUrlConexoesMap.put(7, "**************************************************************************");
            integracoesUrlConexoesMap.put(8, "*********************************************************************");
            integracoesUrlConexoesMap.put(9, "*****************************************************************************");
            integracoesUrlConexoesMap.put(10, "********************************************************************");
            integracoesUrlConexoesMap.put(11, "**********************************************************************");
            integracoesUrlConexoesMap.put(12, "*******************************************************************************");
            integracoesUrlConexoesMap.put(13, "**********************************************************************");
            integracoesUrlConexoesMap.put(14, "************************************************************************");

            for (Map.Entry<Integer, String> entry : integracoesUrlConexoesMap.entrySet()) {
                Connection conOrigem = new Conexao(entry.getValue(), "postgres", "pactodb").getConexao();
                Integer codigoIntegracaoNoGateway = entry.getKey();

                long a = System.currentTimeMillis();
                System.out.println("Consultando pessoas sincronizadas e suas biometrias na chave de origem");
                Map<Integer, String> pessoasBiometrias = new HashMap<>();
                String sqlBiometriasPessoasSincronizadas = "select\n" +
                        "   p.codigo as codPessoa,\n" +
                        "   p.assinaturadigitalbiometria\n" +
                        "from cliente cli\n" +
                        "inner join pessoa p on cli.pessoa = p.codigo\n" +
                        "where cli.sincronizadoredeempresa is not null;";
                try (PreparedStatement ps = conOrigem.prepareStatement(sqlBiometriasPessoasSincronizadas)) {
                    try (ResultSet rs = ps.executeQuery()) {
                        while (rs.next()) {
                            pessoasBiometrias.put(rs.getInt("codPessoa"), rs.getString("assinaturadigitalbiometria"));
                        }
                    }
                }
                System.out.println("Finalizada a consulta de pessoas sincronizadas e suas biometrias na chave de origem");


                System.out.println("Atualizando biometria na autorização de acesso no Gateway");
                for (Map.Entry<Integer, String> entryPessoa : pessoasBiometrias.entrySet()) {
                    String sqlUpdateGateway = "UPDATE autorizacaoacessogrupoempresarial " +
                            "SET assinaturabiometriadigital = ? " +
                            "WHERE codigopessoa = ? AND integracaoacessogrupoempresarial = ?;";

                    try (PreparedStatement ps = conGateway.prepareStatement(sqlUpdateGateway)) {
                        ps.setString(1, entryPessoa.getValue());
                        ps.setInt(2, entryPessoa.getKey());
                        ps.setInt(3, codigoIntegracaoNoGateway);
                        ps.execute();
                    }
                }
                long b = System.currentTimeMillis();
                System.out.println("Processo finalizado em " + (b - a) + "ms. BD Realizado: " + entry.getValue());
            }


        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    public static void verificarExecucaoRoboEmTodosOsBancos() {
        DAO oamd = new DAO();
        List<String> listaChaves;
        try {
            listaChaves = oamd.buscarListaChaves();
            String chave = null;
            for (Iterator it = listaChaves.iterator(); it.hasNext();) {
                try {
                    chave = it.next().toString();
                    Conexao conexao = Conexao.getInstance();
                    if (!conexao.getUrlOAMD().isEmpty()) {
//                        Removido o método que obtinhaConexãoEspecíficaParaServidor, qualquer coisa, olhar no git.
//                        Connection con = oamd.obterConexaoEspecificaParaServidor(chave, "zwserver1");
//                        Conexao.guardarConexaoForJ2SE(con);
                        //Uteis.logar(null, "Guardada conexao para " + chave + "\r");
                    } else {
                        //Uteis.logar(null, "Usando cfgBD.xml para -> " + conexao.getIpServidor());
                    }

                    ResultSet rs = SuperFacadeJDBC.criarConsulta(
                            "select * from robo order by codigo desc limit 1",
                            Conexao.getConexaoForJ2SE());
                    while (rs.next()) {
                        //Uteis.logar(null,Conexao.getConexaoForJ2SE() + " -> " + chave + " -> " + Conexao.getConexaoForJ2SE().getMetaData().getURL());
                        System.out.println(Conexao.getConexaoForJ2SE().getMetaData().getURL() + " " + rs.getDate("dia"));
                    }


                } catch (Exception e) {
                    Logger.getLogger(TesteCargaAcesso.class.getName()).log(Level.SEVERE, null, e);
                } finally {
                    FacadeManager.limparFactory();
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(TesteCargaAcesso.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    public static void gerarSenhasPZWProducao() throws UnsupportedEncodingException {
        String senhaAdmin = "4DMBR15ZW";
        String senhaPactoBR = "P4ZW20PMG";

        String adminCrypto = Uteis.encriptar(senhaAdmin);
        Uteis.logar(null, String.format("COMANDO: %s", String.format("update usuario set senha = '%s' where upper(username) = 'ADMIN'", adminCrypto)));
        //
        String pactobrCrypto = Uteis.encriptar(senhaPactoBR);
        Uteis.logar(null, String.format("COMANDO: %s", String.format("update usuario set senha = '%s' where upper(username) = 'PACTOBR'", pactobrCrypto)));
        //
        String recorCrypto = Uteis.encriptar(PropsService.getPropertyValue(PropsService.RECORRENCIA_USER_PASSWORD));
        Uteis.logar(null, String.format("COMANDO: %s", String.format("update usuario set senha = '%s' where upper(username) = 'RECOR'", recorCrypto)));
        //
        Uteis.logar(null, "Senhas exclusivas para empresa SOLLOS:");
        String senhaCryptoSOLLOS = Uteis.encriptar("P4CT0SOLL0");
        Uteis.logar(null, String.format("COMANDO: %s", String.format("update usuario set senha = '%s' where upper(username) = 'ADMIN'", senhaCryptoSOLLOS)));
        //
        Uteis.logar(null, String.format("COMANDO: %s", String.format("update usuario set senha = '%s' where upper(username) = 'PACTOBR'", senhaCryptoSOLLOS)));
        //
        Uteis.logar(null, "Senhas exclusivas para NFSe:");
        String adminNFSe = Uteis.encriptarNFe(senhaAdmin);
        Uteis.logar(null, String.format("COMANDO: %s", String.format("UPDATE Usuario SET senha = '%s' WHERE Usuario = 'admin'", adminNFSe)));

        String pactobrNFSe = Uteis.encriptarNFe(senhaPactoBR);
        Uteis.logar(null, String.format("COMANDO: %s", String.format("UPDATE Usuario SET senha = '%s' WHERE Usuario = 'PACTOBR'", pactobrNFSe)));
    }

    public static void unificarTodosScriptsAtualizacoes() {
        for (int i = 1; i < 77; i++) {
            String script = "/negocio/facade/jdbc/utilitarias/scripts/zw-" + Formatador.formatarValorNumerico(new Double(i), "00000")
                    + ".sql";
            // Obter stream de entrada para leitura do arquivo
            InputStream in = TesteCargaAcesso.class.getResourceAsStream(script);

            try {
                if (in == null) {
                    Uteis.logar(null, "Arquivo " + script + " nao encontrado.");
                } else {
                    String atualizacao = Uteis.convertStreamToString(in);
                    in.close();
                    System.out.println(atualizacao);
                }
            } catch (Exception e) {
                Uteis.logar(null, e.getMessage());
            }
        }
    }

    public static void preencherAnnotationsExecutarProcesso() throws IOException {
        InputStream inProcessos = TesteCargaAcesso.class.getResourceAsStream("/negocio/facade/jdbc/utilitarias/scripts/temp.txt");
        StringBuilder sbProcessos = new StringBuilder(Uteis.convertStreamToString(inProcessos));
        inProcessos.close();
        for (int i = 77; i < 142; i++) {
            String script = "/negocio/facade/jdbc/utilitarias/scripts/zw-" + Formatador.formatarValorNumerico(new Double(i), "00000")
                    + ".sql";
            // Obter stream de entrada para leitura do arquivo
            InputStream in = TesteCargaAcesso.class.getResourceAsStream(script);
            //
            try {
                if (in == null) {
                    Uteis.logar(null, "Arquivo " + script + " nao encontrado.");
                } else {
                    StringBuilder sb = new StringBuilder(Uteis.convertStreamToString(in));
                    in.close();
                    //
                    String descricao = AtualizadorBD.obterDescricaoAtualizacao(sb);
                    if (!descricao.isEmpty()) {
                        String desc = descricao.substring(descricao.indexOf("-- Descricao:"), descricao.indexOf("-- Autor:"));
                        desc = desc.replace("-- Descricao:", "");
                        String autor = descricao.substring(descricao.indexOf("-- Autor:"), descricao.indexOf("-- Data:"));
                        autor = autor.replace("-- Autor:", "");
                        String data = descricao.substring(descricao.indexOf("-- Data:"), descricao.indexOf("-- Motivacao:"));
                        data = data.replace("-- Data:", "");
                        String motivacao = descricao.substring(descricao.indexOf("-- Motivacao:"), descricao.indexOf("-- @"));
                        motivacao = motivacao.replace("-- Motivacao:", "");
//                        System.out.println(desc + autor + data + motivacao);
                        String metodo = "public void migracaoVersao" + i + "()";
                        int inicio = sbProcessos.indexOf(metodo);
                        String replace = "@Processo(autor = \"" + autor.trim() + "\", \ndata = \"" + data.trim() + "\", \ndescricao = \"" + desc.trim() + "\", \nmotivacao = \"" + motivacao.trim() + "\")\n" + metodo;
                        sbProcessos = sbProcessos.replace(inicio, inicio + metodo.length(), replace);



                    }
                }
            } catch (Exception e) {
                Uteis.logar(null, script + " -> EXC.: " + e.getMessage());
            }
        }
        System.out.println(sbProcessos.toString());
    }

    /**
     * Existem usuário como 'administrador = TRUE' incorretamente, este deviam
     * ter um perfil de acesso relacionado e com horários acesso padrão. Este
     * método só funciona para banco de dados com uma única Empresa!
     *
     * @param nomeHost
     * @param nomeBanco
     * @throws Exception
     */
    private static void corrigirUsuariosComPerfisErrados(String nomeHost, String nomeBanco) throws Exception {
        Conexao conexao = new Conexao("jdbc:postgresql://" + nomeHost + ":5432/" + nomeBanco, "postgres", "pactodb");
        Connection con = conexao.getConexao();

        int count = SuperFacadeJDBC.contar("select count(*) from empresa", con);
        try {
            con.setAutoCommit(false);
            if (count == 1) {//processo não resolve mais de uma empresa!

                String sql = "select u.codigo, u.username from usuario u "
                        + "inner join colaborador c on c.codigo = u.colaborador "
                        + "where u.codigo <> 1 and u.administrador is true and u.username not in ('Master','RECOR', 'admin', 'ADMIN') "
                        + "and u.codigo not in (select usuario from usuarioperfilacesso) "
                        + "and c.situacao = 'AT' order by u.username";
                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
                while (rs.next()) {
                    int codigo = rs.getInt("codigo");
                    String login = rs.getString("username");
                    //INSERIR HORÁRIO PADRÃO DE ACESSO 06:00 ÀS 23:00 TODOS OS DIAS
                    ResultSet rsExisteHorarioAcesso = SuperFacadeJDBC.criarConsulta("select codigo from horarioacessosistema where usuario = " + codigo, con);
                    if (!rsExisteHorarioAcesso.next()) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(" INSERT INTO horarioacessosistema  (horaInicial,horaFinal,usuario,diasemana) values ('06:00', '23:00',").append(codigo).append(" ,'SG');");
                        sb.append(" INSERT INTO horarioacessosistema  (horaInicial,horaFinal,usuario,diasemana) values ('06:00', '23:00',").append(codigo).append(" ,'TR');");
                        sb.append(" INSERT INTO horarioacessosistema  (horaInicial,horaFinal,usuario,diasemana) values ('06:00', '23:00',").append(codigo).append("  ,'QA');");
                        sb.append(" INSERT INTO horarioacessosistema  (horaInicial,horaFinal,usuario,diasemana) values ('06:00', '23:00',").append(codigo).append(" , 'QI');");
                        sb.append(" INSERT INTO horarioacessosistema  (horaInicial,horaFinal,usuario,diasemana) values ('06:00', '23:00',").append(codigo).append(" ,'SX');");
                        sb.append(" INSERT INTO horarioacessosistema  (horaInicial,horaFinal,usuario,diasemana) values ('06:00', '23:00',").append(codigo).append(" ,'SB');");
                        sb.append(" INSERT INTO horarioacessosistema  (horaInicial,horaFinal,usuario,diasemana) values ('06:00', '23:00',").append(codigo).append(" ,'DM');");
                        SuperFacadeJDBC.executarConsulta(sb.toString(), con);
                    }
                    //INSERIR PERFIL 'ADMINISTRADOR', OU SEJA, COM MAIOR NÚMERO PERMISSÕES PARA OS USUÁRIOS QUE NÃO POSSUEM PERFIL E ESTÃO COMO ADMIN
                    String sqlPerfilAdministrador = "select codperfilacesso, count(codperfilacesso) "
                            + "from permissao group by codperfilacesso order by count(codperfilacesso) desc limit 1";
                    ResultSet rsPerfil = SuperFacadeJDBC.criarConsulta(sqlPerfilAdministrador, con);
                    String perfil = "";
                    while (rsPerfil.next()) {
                        int codperfil = rsPerfil.getInt("codperfilacesso");

                        ResultSet rsNomePerfil = SuperFacadeJDBC.criarConsulta("select nome from perfilacesso where codigo = " + codperfil, con);
                        if (rsNomePerfil.next()) {
                            perfil = rsNomePerfil.getString("nome");
                        }
                        String sqlInsertPerfilUsuario = "insert into usuarioperfilacesso (empresa,perfilacesso,usuario) values((select codigo from empresa)," + codperfil + "," + codigo + ")";
                        SuperFacadeJDBC.executarConsulta(sqlInsertPerfilUsuario, con);
                    }
                    //
                    //
                    //
                    SuperFacadeJDBC.executarConsulta("update usuario set administrador = false where codigo = " + codigo, con);
                    System.out.println(String.format("Alterado usuário: '%s' para perfil de acesso: '%s' e "
                            + "horário de acesso todos os dias das 06:00 às 23:00 da empresa: %s",
                            new Object[]{login, perfil, nomeBanco}));


                }
                con.commit();
            } else {
                System.out.println("Ignorando " + nomeBanco + " mais de uma empresa, deve-se atribuir os perfis e horário de acesso manualmente.");
            }

        } catch (Exception e) {
            con.rollback();
        } finally {
            con.setAutoCommit(true);
            con.close();
        }
    }

    public static void restaurarHistoricoContrato() throws Exception {
        Connection conPassado = new Conexao("*****************************************************************", "postgres", "pactodb").getConexao();
        Connection conPresente = new Conexao("******************************************************************************", "postgres", "pactodb").getConexao();

        String sql = "select * from historicocontrato where contrato in (select c.codigo "
                + "from contrato c "
                + "where c.codigo in (select contrato from historicocontrato where tipohistorico = 'TR') "
                + "and (select count(contrato) from historicocontrato where tipohistorico = 'TR' and contrato = c.codigo) > (select count(contrato) from historicocontrato where tipohistorico = 'RT' and contrato = c.codigo) "
                + "and (select count(contrato) from contratooperacao where tipooperacao = 'TV' and contrato = c.codigo) > 0 "
                + "and current_date <> cast((select max(datafimefetivacaooperacao) from contratooperacao  where contrato = c.codigo and tipooperacao = 'TV') as date) "
                + "and coalesce(contratoresponsavelrenovacaomatricula, 0) = 0 and coalesce(contratoresponsavelrematriculamatricula,0) = 0)";

        ResultSet rsPassado = SuperFacadeJDBC.criarConsulta(sql, conPassado);
        try {
            conPresente.setAutoCommit(false);
            while (rsPassado.next()) {
                if (!SuperFacadeJDBC.existe("select contrato from historicocontrato where tipohistorico = '"
                        + rsPassado.getString("tipohistorico") + "' and contrato = " + rsPassado.getInt("contrato"), conPresente)) {
                    String s = "insert into historicocontrato (datainiciosituacao,datafinalsituacao,tipohistorico,dataregistro,responsavelregistro,descricao,contrato,situacaorelativahistorico) values (?,?,?,?,?,?,?,?)";
                    PreparedStatement ps = conPresente.prepareStatement(s);
                    int i = 1;
                    ps.setDate(i++, rsPassado.getDate("datainiciosituacao"));
                    ps.setDate(i++, rsPassado.getDate("datafinalsituacao"));
                    ps.setString(i++, rsPassado.getString("tipohistorico"));
                    ps.setDate(i++, rsPassado.getDate("dataregistro"));
                    ps.setInt(i++, rsPassado.getInt("responsavelregistro"));
                    ps.setString(i++, rsPassado.getString("descricao"));
                    ps.setInt(i++, rsPassado.getInt("contrato"));
                    ps.setString(i++, "");

                    ps.execute();

                    Uteis.logar(null, String.format("Contrato: %s, Inserido Historico: %s ",
                            new Object[]{rsPassado.getInt("contrato"), rsPassado.getString("tipohistorico")}));
                }

            }
            conPresente.commit();
        } catch (Exception e) {
            conPresente.rollback();
        }

    }

    public static void corrigirTrancadosVencidos(final int contrato) throws Exception {
        try {
            SuperFacadeJDBC.executarConsulta("update contrato set situacao = 'TR' where codigo = " + contrato, FacadeManager.getFacade().getRisco().getCon());
            SuperFacadeJDBC.executarConsulta("delete from historicocontrato where contrato = " + contrato + " and tipohistorico in ('VE','DE')", FacadeManager.getFacade().getRisco().getCon());
            SuperFacadeJDBC.executarConsulta(String.format("update contratooperacao "
                    + "set datafimefetivacaooperacao = current_date "
                    + "where codigo = (select codigo from contratooperacao "
                    + "where contrato = %s and tipooperacao = 'TV' order by codigo desc limit 1)", new Object[]{contrato, contrato}),FacadeManager.getFacade().getRisco().getCon());
            Uteis.logar(null, String.format("Contrato: %s, Atualizado para Trancamento Vencido (TV)",
                    new Object[]{contrato}));
        } catch (Exception e) {
            Uteis.logar(e, TesteCargaAcesso.class);
        }
    }

    public static void corrigirTrancamentosDomBoscoSamambaia() throws Exception {
        corrigirTrancadosVencidos(2420);
        SuperFacadeJDBC.executarConsulta(String.format("update contratooperacao "
                + "set datafimefetivacaooperacao = '2013-07-22' "
                + "where codigo = (select codigo from contratooperacao "
                + "where contrato = %s and tipooperacao = 'TV' order by codigo desc limit 1)", new Object[]{2289}), FacadeManager.getFacade().getRisco().getCon());
        UsuarioVO u = new UsuarioVO();
        u.setCodigo(1);
        HistoricoContratoVO h = new HistoricoContratoVO();
        h.setContrato(2289);
        h.setTipoHistorico("VE");
        h.setDescricao("VENCIDO");
        h.setResponsavelRegistro(u);
        h.setDataInicioSituacao(Calendario.getInstance(2013, 07, 25).getTime());
        h.setDataFinalSituacao(Calendario.getInstance(2013, 07, 29).getTime());
        FacadeManager.getFacade().getHistoricoContrato().incluirSemCommit(h, Boolean.FALSE);
        //
        h = new HistoricoContratoVO();
        h.setContrato(2289);
        h.setTipoHistorico("RT");
        h.setDescricao("RETORNO TRANCAMENTO");
        h.setResponsavelRegistro(u);
        h.setDataInicioSituacao(Calendario.getInstance(2013, 07, 23).getTime());
        h.setDataFinalSituacao(Calendario.getInstance(2013, 07, 24).getTime());
        FacadeManager.getFacade().getHistoricoContrato().incluirSemCommit(h, Boolean.FALSE);

        ContratoOperacaoVO co = new ContratoOperacaoVO();
        co.setContrato(2289);
        co.setTipoOperacao("RT");
        co.setDataInicioEfetivacaoOperacao(Calendario.getInstance(2013, 07, 23).getTime());
        co.setDataFimEfetivacaoOperacao(Calendario.getInstance(2013, 07, 24).getTime());
        co.setResponsavel(u);
        FacadeManager.getFacade().getContratoOperacao().incluirSemCommit(co, Boolean.FALSE);
        SuperFacadeJDBC.executarConsulta("update contrato set vigenciaDe='2013-08-05',vigenciaAte='2014-05-04',vigenciaAteAjustada='2014-05-04' where codigo = 5654",FacadeManager.getFacade().getRisco().getCon());
        SuperFacadeJDBC.executarConsulta("update historicocontrato set datainiciosituacao  = '2013-08-05', datafinalsituacao='2014-05-04' where contrato = 5654 and tipohistorico = 'RN'",FacadeManager.getFacade().getRisco().getCon());

    }

    public static void excluirMovProdutosContrato(final int[] movprodutos) {
        /**
         * passo 1: excluir movprodutos que estão passando passo 2: excluir
         * relacionamento com movparcela do produto excluido passo 3: atualizar
         * valor do movparcela do produto excluido passo 4: atualizar valor
         * total do contrato e valor base cálculo
         *
         */
        FacadeFactory f = FacadeManager.getFacade();
        for (int i = 0; i < movprodutos.length; i++) {
            int movproduto = movprodutos[i];
            try {
                MovProdutoVO mp = f.getMovProduto().consultarPorChavePrimaria(movproduto,
                        Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (mp.getSituacao().equals("EA")) {
                    List<MovProdutoParcelaVO> l = f.getMovProdutoParcela().consultarMovProdutoParcelas(movproduto, Uteis.NIVELMONTARDADOS_TODOS);
                    MovProdutoParcelaVO movProdParc = l.get(0);
                    MovParcelaVO mparc = f.getMovParcela().consultarPorChavePrimaria(movProdParc.getMovParcela(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    mparc.setValorParcela(mparc.getValorParcela() - mp.getTotalFinal());
                    SuperFacadeJDBC.executarConsulta("delete from movprodutoparcela where codigo = " + movProdParc.getCodigo(), FacadeManager.getFacade().getRisco().getCon());
                    SuperFacadeJDBC.executarConsulta("delete from movproduto where codigo = " + movproduto, FacadeManager.getFacade().getRisco().getCon());
                    SuperFacadeJDBC.executarConsulta("update contrato set valorfinal = valorfinal - "
                            + mp.getTotalFinal() + ", valorbasecalculo=valorbasecalculo - "
                            + mp.getTotalFinal() + " where codigo = " + mp.getContrato().getCodigo(),FacadeManager.getFacade().getRisco().getCon());
                    SuperFacadeJDBC.executarConsulta("update movparcela set valorparcela = valorparcela - "
                            + mp.getTotalFinal() + " where codigo = " + mparc.getCodigo(),FacadeManager.getFacade().getRisco().getCon());
                }
            } catch (Exception e) {
            }
        }
    }

    public static void copiarPlanosEntreEmpresas(Integer codEmpresaOrigem, Integer codEmpresaDestino) throws Exception {
        Connection con = new Conexao("********************************************************************************", "postgres", "pactodb").getConexao();
        Conexao.guardarConexaoForJ2SE(con);

        Plano planoDAO = new Plano(con);
        Composicao composicaoDAO = new Composicao(con);

        List<PlanoVO> planosOrigem = planoDAO.consultarPorCodigoEmpresa(codEmpresaOrigem, Uteis.NIVELMONTARDADOS_TODOS);

        EmpresaVO empresaDestino = new EmpresaVO();
        empresaDestino.setCodigo(codEmpresaDestino);
        HashMap<Integer, ComposicaoVO> pacotes = new HashMap<Integer, ComposicaoVO>();

        for (PlanoVO planoVO : planosOrigem) {
            if (planoVO.getDescricao().contains("IMPORTACAO")) {
                continue;
            }
            planoVO.setEmpresa(empresaDestino);
            Iterator i =planoVO.getPlanoComposicaoVOs().iterator();
            while (i.hasNext()) {
                PlanoComposicaoVO obj = (PlanoComposicaoVO) i.next();
                if (!pacotes.containsKey(obj.getComposicao().getCodigo())) {
                    ComposicaoVO nova = composicaoDAO.consultarPorChavePrimaria(obj.getComposicao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                    nova.setEmpresa(empresaDestino);
                    composicaoDAO.incluirSemCommit(nova);
                    pacotes.put(obj.getComposicao().getCodigo(), nova);
                }
                obj.setComposicao(pacotes.get(obj.getComposicao().getCodigo()));
            }
            planoDAO.incluir(planoVO);
        }
    }

    public static void incluirReciboDoItemDaRemessa(List<Integer> codigosItens) throws Exception {
        Connection con = new Conexao("*************************************************",
                "postgres", "pactodb").getConexao();
        Conexao.guardarConexaoForJ2SE(con);
        //
        Map<Integer, RemessaVO> remessas = new HashMap<Integer, RemessaVO>();
        RemessaService remessaService = new RemessaService();
        for (Integer codigoItem : codigosItens) {
            RemessaItemVO item = FacadeManager.getFacade().getZWFacade().getRemessaItem().consultarPorChavePrimaria(codigoItem, Uteis.NIVELMONTARDADOS_TODOS);
            if (remessas.containsKey(item.getRemessa().getCodigo())) {
                item.setRemessa(remessas.get(item.getRemessa().getCodigo()));
            } else {
                LayoutRemessaBase.preencherArquivoRemessa(item.getRemessa());
                LayoutRemessaBase l = new LayoutRemessaBase();
                l.lerHeaderETrailerRemessa(item.getRemessa());
                LayoutRemessaCieloDCC.lerRetorno(item.getRemessa(), false);
                preencherAtributosTransientes(item.getRemessa(), item.getRemessa().getTrailerRetorno().getAtributos().get(5));
                remessas.put(item.getRemessa().getCodigo(), item.getRemessa());
            }

            remessaService.incluirPagamentoItem(item);
            FacadeManager.getFacade().getZWFacade().getRemessaItem().alterar(item);
            System.out.println("Pagamento gerado para parcela nº " + item.getMovParcela().getCodigo());
        }
    }

    public static void gerarRemessaCancelamento() throws Exception {
        Connection con = new Conexao("*****************************************************************",
                "postgres", "pactodb").getConexao();
        final String sql = "select ri.codigo as codItem, p.codigo as codPessoa, p.nome,mp.codigo as codParc, \n"
                + "	mp.valorparcela,mp.datavencimento,mp.nrtentativas,\n"
                + "	mp.situacao as sitParcela, c.codigo as codContr,c.situacao as sitContr,\n"
                + "	r.codigo as codRemessa, r.dataregistro, ri.props\n"
                + "from remessaitem ri\n"
                + "inner join movparcela mp on mp.codigo = ri.movparcela\n"
                + "inner join contrato c on c.codigo = mp.contrato\n"
                + "inner join pessoa p on p.codigo = mp.pessoa\n"
                + "inner join cliente cli on cli.pessoa = p.codigo\n"
                + "inner join remessa r on r.codigo = ri.remessa\n"
//                + "left join acessocliente ac on cli.uacodigo = ac.codigo\n"
                + "where ri.remessa = 1120\n"
//                + "and ri.props like ('%StatusVenda=00%')\n"
//                + "and mp.datavencimento <= '2015-05-02'\n"
                + "order by p.nome, mp.datavencimento";

        String dataDeposito = Calendario.getData(Calendario.hoje(), "ddMMyyyy");
        String numeroEstabelecimento = "1045297051";
        Integer numeroResumoOperacoes = 1;
        String nomeArq = "REM" + Calendario.getData(Calendario.hoje(), "yyMMddHHmm") + "_CA_33ca8215be6d5e60e2f4e20aa4707360.rem";
        String arquivoDestino = "C:\\PactoJ\\" + nomeArq;
        //REGISTRO HEADER
        //[TAMANHO] [NOME_CAMPO]        
        //02 fixo
        StringBuilder texto = new StringBuilder("00");
        //08 dataDeposito
        texto.append(dataDeposito);
        //07 Resumo Operações (R.O)
        texto.append(StringUtilities.formatarCampo(new BigDecimal(numeroResumoOperacoes), 7));
        //10 Reservado Estabelecimento
        texto.append(StringUtilities.formatarCampoZerado(10));
        //03 Reservado Cielo
        texto.append(StringUtilities.formatarCampoZerado(3));
        //10 Numero Estabelecimento
        texto.append(numeroEstabelecimento);
        //03 Moeda
        texto.append("986");
        //01 Indicador de Processo
        texto.append("P");
        //01 Indicador de Cancelamento de Venda 'C' - À Vista 'P' - Parcelado Loja
        texto.append("C");
        //01 Indicação de Ec Especial
        texto.append(" ");
        //03 Versão do Layout
        texto.append("   ");
        //201 Reservado Cielo
        texto.append(StringUtilities.formatarCampoEmBranco(201));
        texto.append("\r\n");
        //REGISTRO DETALHE
        //[TAMANHO] [NOME_CAMPO]
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        int cont = 0;
        double valorTotalBruto = 0.0;
        while (rs.next()) {            String props = rs.getString("props");
            String autorizacao = LayoutRemessaBase.obterValorCampoProps(DCCAttEnum.CodigoAutorizacao.name(), props);
            String nrcartao = LayoutRemessaBase.obterValorCampoProps(DCCAttEnum.NumeroCartao.name(), props);
            Double valorVenda = rs.getDouble("valorParcela");
            Date dataVenda = rs.getTimestamp("dataRegistro");
            Integer comprovanteVenda = rs.getInt("codParc");
            String mesValidade = LayoutRemessaBase.obterValorCampoProps("MesValidade", props);
            String anoValidade = LayoutRemessaBase.obterValorCampoProps("AnoValidade", props);
            String validadeCartao = mesValidade + anoValidade.substring(2);
            Integer codRemessa = rs.getInt("codRemessa");
            //
            if (UteisValidacao.emptyString(autorizacao)) {
                continue;
            }
            //02 Tipo Registro
            texto.append("01");
            //07 Comprovante de Venda
            texto.append(StringUtilities.formatarCampo(new BigDecimal(comprovanteVenda), 7));
            //19 numero cartão
            texto.append(StringUtilities.formatarCampo(new BigDecimal(APF.decifrar(nrcartao)), 19));
            //06 Código Autorização Venda
            texto.append(autorizacao);
            //08 Data Venda            
            texto.append(Calendario.getData(dataVenda, "ddMMyyyy"));
            //01 Opção da Venda: 0 - A vista 2 - Parcelado Loja
            texto.append("0");
            //15 Valor Venda
            texto.append(StringUtilities.formatarCampoMonetario(valorVenda, 15));
            //03 Quantidade parcelas
            texto.append(StringUtilities.formatarCampoZerado(3));
            //15 Valor Financiado
            texto.append(StringUtilities.formatarCampoZerado(15));
            //15 Valor Entrada
            texto.append(StringUtilities.formatarCampoZerado(15));
            //15 Valor taxa de Embartque
            texto.append(StringUtilities.formatarCampoZerado(15));
            //15 Valor Parcela
            texto.append(StringUtilities.formatarCampoZerado(15));
            //07 Numero Resumo Operações (RO)
            texto.append(StringUtilities.formatarCampoForcandoZerosAEsquerda(numeroResumoOperacoes, 7));
            //03 Reservado Cielo
            texto.append(StringUtilities.formatarCampoZerado(3));
            //10 Numero Estabelecimento
            texto.append(numeroEstabelecimento);
            //30 Reservado Estabelecimento
            texto.append(StringUtilities.formatarCampoEmBranco(30));
            //02 Status Venda (Retorno)
            texto.append(StringUtilities.formatarCampoZerado(2));
            //08 Data prevista crédito (Retorno)
            texto.append(StringUtilities.formatarCampoZerado(8));
            //04 Validade Cartão
            texto.append(validadeCartao);
            //07 Número Resumo Operações RO Original
            texto.append(StringUtilities.formatarCampoForcandoZerosAEsquerda(codRemessa, 7));
            //15 Valor reembolso
            texto.append(StringUtilities.formatarCampoMonetario(valorVenda, 15));
            //03 Reservado Cielo
            texto.append(StringUtilities.formatarCampoEmBranco(3));
            //04 Código Erro
            texto.append(StringUtilities.formatarCampoEmBranco(4));
            //11 Número Referência
            texto.append(StringUtilities.formatarCampoEmBranco(11));
            //19 Cartão novo
            texto.append(StringUtilities.formatarCampoEmBranco(19));
            //04 Vencto Novo
            texto.append(StringUtilities.formatarCampoEmBranco(4));
            //02 Reservado Cielo
            texto.append(StringUtilities.formatarCampoEmBranco(2));
            //
            cont++;
            valorTotalBruto += valorVenda;
            texto.append("\r\n");
        }
        //REGISTRO TRAILER
        //[TAMANHO] [NOME_CAMPO]
        //02 Tipo de Registro
        texto.append("99");
        //07 Quantidade registros
        texto.append(StringUtilities.formatarCampo(new BigDecimal(cont), 7));
        //15 Valor total bruto
        texto.append(StringUtilities.formatarCampoMonetario(valorTotalBruto, 15));
        //15 Valor total aceito
        texto.append(StringUtilities.formatarCampoZerado(15));
        //15 Valor total liquido
        texto.append(StringUtilities.formatarCampoZerado(15));
        //08 Data prevista crédito
        texto.append(StringUtilities.formatarCampoZerado(8));
        //188 Reservado Cielo
        texto.append(StringUtilities.formatarCampoEmBranco(188));
        texto.append("\r\n");

        StringUtilities.saveToFile(texto, arquivoDestino);
    }

    public static void pesquisaAlunoTodosBancosTreino(final String hostOAMD2,
            final String user,
            final String pwd,
            boolean excluir,
            final String[] nomes) throws Exception {
        Connection conPGSQL = new Conexao("jdbc:postgresql://" + hostOAMD2 + "/postgres", user, pwd).getConexao();
        final String sqlBancos = "select datname from pg_database where datname like 'bdmusc%' order by datname";
        final String sqlCountBancos = "select count(*) from pg_database where datname like 'bdmusc%'";
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlBancos, conPGSQL);
            int cont = SuperFacadeJDBC.contar(sqlCountBancos, conPGSQL);
            int i = 1;
            while (rs.next()) {
                try {
                    final String nomeBD = rs.getString("datname");
                    final String hostBD = hostOAMD2;
                    System.out.println(String.format("%s/%s %s", i, cont, nomeBD));
                    //
                    final String url = String.format("****************************", hostBD, nomeBD);
                    Connection conBanco = new Conexao(url, "zillyonweb", pwd).getConexao();
                    try {
                        final String sqlAluno = String.format("select codigo,dia,matricula,nome,codigopessoa,empresa,email from clientesintetico where nome in (%s)", Uteis.splitFromArray(nomes, true));
                        ResultSet rsAluno = SuperFacadeJDBC.criarConsulta(sqlAluno, conBanco);
                        while (rsAluno.next()) {
                            System.out.println(String.format("######### ENCONTREI #########\n"
                                    + "Aluno: %s - %s\n"
                                    + "sintetico dia: %s\n"
                                    + "email: %s\n"
                                    + "Onde: %s\n"
                                    + "###########################",
                                    rsAluno.getInt("matricula"),
                                    rsAluno.getString("nome"),
                                    rsAluno.getTimestamp("dia"),
                                    rsAluno.getString("email"),
                                    conBanco.getMetaData().getURL()));
                            //excluir!!!!!
                            if (excluir) {
                                SuperFacadeJDBC.executarConsulta(String.format("delete from  usuario where cliente_codigo in (select codigo from clientesintetico where nome = '%s');", rsAluno.getString("nome")), conBanco);
                                SuperFacadeJDBC.executarConsulta(String.format("delete from clientesintetico  where nome = '%s'", rsAluno.getString("nome")), conBanco);
                                System.out.println("######### EXCLUIDO!!! #########");
                            }
                        }
                    } catch (Exception e) {
                        System.out.println(e.getMessage());
                    } finally {
                        conBanco.close();
                    }
                } catch (Exception e) {
                    System.out.println(e.getMessage());
                } finally {
                    i++;
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, TesteCargaAcesso.class);
        } finally {
            conPGSQL.close();
        }
    }

    public static void criarPacotesPlanosEmpresaDiferente() throws Exception {
        Connection con = new Conexao("*********************************************************", "zillyonweb", "pactodb").getConexao();
        Conexao.guardarConexaoForJ2SE(con);

        ResultSet rs = SuperFacadeJDBC.criarConsulta("select p.codigo as plano, p.descricao, p.empresa novaempresa, c.codigo as composicao ,c.descricao,c.empresa as antigaempresa" +
                        " from plano p inner join planocomposicao pc on pc.plano=p.codigo inner join composicao c on c.codigo = pc.composicao  where p.empresa <> c.empresa"
            , con);
        Composicao composicaoDAO = new Composicao(con);
        HashMap<Integer, Integer> pacotes = new HashMap<Integer, Integer>();
        while (rs.next()) {
            if(!pacotes.containsKey(rs.getInt("composicao"))){
                EmpresaVO empresaDestino = new EmpresaVO();
                empresaDestino.setCodigo(rs.getInt("novaempresa"));
                ComposicaoVO nova = composicaoDAO.consultarPorChavePrimaria(rs.getInt("composicao"), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                nova.setEmpresa(empresaDestino);
                composicaoDAO.incluirSemCommit(nova);
                pacotes.put(rs.getInt("composicao"),nova.getCodigo());
            }
            SuperFacadeJDBC.executarUpdate("update planocomposicao set composicao = "+pacotes.get(rs.getInt("composicao"))+" where plano = "+rs.getInt("plano")+ "and composicao ="+ rs.getInt("composicao"), con);
            SuperFacadeJDBC.executarUpdate("update planoexcecao  set pacote = "+pacotes.get(rs.getInt("composicao"))+" where plano = "+rs.getInt("plano")+" and pacote = "+ rs.getInt("composicao"), con);
            SuperFacadeJDBC.executarUpdate("update contratocomposicao set composicao = "+pacotes.get(rs.getInt("composicao"))+" where contrato in (  SELECt codigo from contrato where plano = "+rs.getInt("plano")+ " ) and composicao = "+rs.getInt("composicao"),con);
        }
    }
}

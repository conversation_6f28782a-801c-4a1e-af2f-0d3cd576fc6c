package test.simulacao;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.nfe.RetornoEnvioNotaFiscalTO;
import negocio.comuns.nfe.enumerador.ResultadoEnvioNFSeEnum;
import negocio.facade.jdbc.nfe.LoteNFe;

import java.sql.Connection;

public class ReenviarNFSe {


    public static void main(String args[]) throws Exception {
        DAO dao = new DAO();
        Connection con = dao.obterConexaoEspecifica("");

        LoteNFe loteNFeDAO = new LoteNFe(con);

        //LISTA DEVE SER A LISTA DOS ID_RPS !!!
        String dataEmissao = "2019-03-31 00:00:00"; //yyyy-MM-dd HH:mm:ss
        boolean zerarAliquotaSimples = false;
        String listaRPS = "";
//        String listaRPS = "4999418";
//        String listaRPS = "4959593,4959592,4959591,4959590,4959589,4959588,4959587,4959586,4959585,4959584,4959583,4959582,4959581,4959580,4959579,4959578,4959577,4959576,4959575,4959574,4959573,4959572,4959571,4959570,4959569,4959568,4959567,4959566,4959565,4959564,4959563,4959562,4959561,4959560,4959559,4959558,4959557,4959556,4959555,4959554,4959553,4959552,4959551,4959550,4959549,4959548,4959547,4959546,4959545,4959544,4959543,4959542,4959541,4959540,4959539,4959538,4959537,4959536,4959535,4959534,4959533,4959532,4959531,4959530,4959529,4959528,4959527,4959526,4959525,4959524,4959523,4959522,4959521,4959520,4959519,4959518,4959517,4959516,4959515,4959514,4959513,4959512,4959511,4959510,4959509,4959508,4959507,4959506,4959505,4959504,4959503,4959502,4959501,4959500,4959499,4959498,4959497,4959496,4959495,4959494,4959493,4959492,4959491";

        String[] arrayRPS = listaRPS.split(",");
        int qtd = arrayRPS.length;

        JSONArray jsonArray = lancarCamposAlterados(dataEmissao, zerarAliquotaSimples);
        int i = 0;
        for (String rps : arrayRPS) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("idRPS", Integer.parseInt(rps));
            jsonObject.put("alteracoes", jsonArray);
            RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = loteNFeDAO.reenviarRPS(jsonObject, Integer.parseInt(rps));
            System.out.print(++i + " de " + qtd + " - ");
            if (!retornoEnvioNotaFiscalTO.getResultadoEnvio().equals(ResultadoEnvioNFSeEnum.OK)) {
                System.out.println("Ocorreu um erro ao reeviar a nota! RPS " + rps);
            } else {
                System.out.println("Reenviado RPS: " + rps);
            }
        }
    }

    private static JSONArray lancarCamposAlterados(String dataEmissao, boolean zerarAliquotaSimples) throws Exception {
        JSONArray camposAlterados = new JSONArray();

        //Pode enviar vazio
//        camposAlterados.put(montarCamposAlterados("CFDF", getNotaReenviar().getCfdf().toUpperCase(), true));


        camposAlterados.put(montarCamposAlterados("DataEmissao", dataEmissao, true));
//
//        if (!getNotaReenviar().getRazaoSocialCons().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("RazaoSocialCons", getNotaReenviar().getRazaoSocialCons(), true));
//        }
//        if (!getNotaReenviar().getCpfCnpjCons().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("CPFCNPJCons", Uteis.removerMascara(getNotaReenviar().getCpfCnpjCons()), true));
//        }
//        if (!getNotaReenviar().getInscricaoMunicipalCons().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("InscricaoMunicipalCons", getNotaReenviar().getInscricaoMunicipalCons(), true));
//        }
//        if (!getNotaReenviar().getCepCons().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("CEPCons", Uteis.removerMascara(getNotaReenviar().getCepCons()), true));
//        }
//        if (!getNotaReenviar().getEmailCons().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("EmailCons", getNotaReenviar().getEmailCons(), true));
//        }
//        if (!getNotaReenviar().getLogradouroCons().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("LogradouroCons", getNotaReenviar().getLogradouroCons().toUpperCase(), true));
//        }
//        if (!getNotaReenviar().getComplementoEnderecoCons().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("ComplementoEnderecoCons", getNotaReenviar().getComplementoEnderecoCons().toUpperCase(), true));
//        }
//        if (!getNotaReenviar().getNumeroEnderecoCons().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("NumeroEnderecoCons", getNotaReenviar().getNumeroEnderecoCons().toUpperCase(), true));
//        }
//        if (!getNotaReenviar().getBairroCons().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("BairroCons", getNotaReenviar().getBairroCons().toUpperCase(), true));
//        }
//        if (!getNotaReenviar().getTelefoneCons().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("TelefoneCons", getNotaReenviar().getTelefoneCons().toUpperCase(), true));
//        }
//        if (!getNotaReenviar().getDescricao().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("Descricao", getNotaReenviar().getDescricao().toUpperCase(), true));
//        }
//        if (!getNotaReenviar().getInscricaoEstadual().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("InscricaoEstadual", getNotaReenviar().getInscricaoEstadual().toUpperCase(), true));
//        }
//        if (!getNotaReenviar().getObservacao().isEmpty()) {
//            camposAlterados.put(montarCamposAlterados("Observacao", getNotaReenviar().getObservacao().toUpperCase(), true));
//        }

        if (zerarAliquotaSimples) {
            camposAlterados.put(montarCamposAlterados("AliquotaAtividade", "0.0", false));
            camposAlterados.put(montarCamposAlterados("ValorISSRetido", "0.0", false));
        }

        return camposAlterados;
    }


    private static JSONObject montarCamposAlterados(String campo, String valor, boolean aspas) throws Exception {
        JSONObject alteracao = new JSONObject();
        alteracao.put("campo", campo);
        alteracao.put("valor", valor);
        alteracao.put("colocarAspas", aspas);
        return alteracao;
    }
}

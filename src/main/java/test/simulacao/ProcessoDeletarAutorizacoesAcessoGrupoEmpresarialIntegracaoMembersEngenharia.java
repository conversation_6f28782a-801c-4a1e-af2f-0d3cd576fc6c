package test.simulacao;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.ResultSet;

public class ProcessoDeletarAutorizacoesAcessoGrupoEmpresarialIntegracaoMembersEngenharia {

    private static boolean simulacao;

    public static void main(String[] args) throws Exception {

        String chaveFranqueadora = "e82cdafdf149e399e18ae3eb096bd873"; // ENGENHARIA DO CORPO - FRANQUEADORA
        String chaveBDIntegracaoEvo = "628519d73cf98a91c81fa2492e0a9189"; // ENGENHARIA DO CORPO - INTEGRAÇÃO
        String codigosIntegracoesMembers = "55"; // Consultar na tabela integracaomember do bdintegracaoengenharia.
        simulacao = false;

        deletarAutorizacoesAcessoGrupoEmpresarialIntegracaoMembersEngenharia(codigosIntegracoesMembers, chaveFranqueadora, chaveBDIntegracaoEvo);
    }

    public static void deletarAutorizacoesAcessoGrupoEmpresarialIntegracaoMembersEngenharia(String codigosIntegracoesMembers, String chaveFranqueadora, String chaveBDIntegracaoEvo) throws Exception {
        if (UteisValidacao.emptyString(codigosIntegracoesMembers)) {
            System.out.println("Informe os códigos de integração de members para deletar as autorizações de acesso.");
            return;
        }

        DAO dao = new DAO();
        Connection conFranqueadora = dao.obterConexaoEspecifica(chaveFranqueadora);
        conFranqueadora.setAutoCommit(false);
        Connection conBDIntegracao = dao.obterConexaoEspecifica(chaveBDIntegracaoEvo);
        conBDIntegracao.setAutoCommit(false);
        try {
            String sql = "select array_to_string(array(\n" +
                    "\tselect cli.pessoa from \"member\" m \n" +
                    "\tinner join integracaomember im on im.codigo = m.integracaomember \n" +
                    "\tinner join cliente cli on cli.idexterno = m.idmember\n" +
                    "\tinner join pessoa pes on pes.codigo = cli.pessoa \n" +
                    "where im.codigo IN (" + codigosIntegracoesMembers + ")), ',') as codigosPessoas";

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conBDIntegracao);

            if (rs.next() && !UteisValidacao.emptyString(rs.getString("codigosPessoas"))) {
                String sqlAutDeletar = "SELECT aut.codigo FROM autorizacaoacessogrupoempresarial aut \n" +
                        " INNER JOIN integracaoacessogrupoempresarial int ON int.codigo = aut.integracaoacessogrupoempresarial \n" +
                        " AND int.chave = '" + chaveBDIntegracaoEvo + "' \n " +
                        " AND aut.codigopessoa IN (" + rs.getString("codigosPessoas") + ")\n";

                int qtd = SuperFacadeJDBC.contar("select count(s.*) from (" + sqlAutDeletar + ") as s", conFranqueadora);
                SuperFacadeJDBC.executarConsulta("DELETE FROM autorizacaoacessogrupoempresarial WHERE codigo IN (\n" + sqlAutDeletar + ")", conFranqueadora);

                SuperFacadeJDBC.executarConsulta("DELETE FROM \"member\" WHERE integracaomember IN (" + codigosIntegracoesMembers + ")", conBDIntegracao);
                SuperFacadeJDBC.executarConsulta("DELETE FROM integracaomember WHERE codigo IN (" + codigosIntegracoesMembers + ")", conBDIntegracao);

                System.out.printf("%s - %s - %d autorizações de acesso de %d alunos \n", conFranqueadora.getCatalog(), simulacao ? "[MODO SIMULCAO]" : "[PROCESSO EXECUTADO]", qtd, rs.getString("codigosPessoas").split(",").length);
                if (!simulacao) {
                    conFranqueadora.commit();
                    conBDIntegracao.commit();
                }
            }

        } catch (Exception e) {
            conFranqueadora.rollback();
            conBDIntegracao.rollback();
            e.printStackTrace();
        } finally {
            conFranqueadora.close();
            conBDIntegracao.close();
        }
    }
}

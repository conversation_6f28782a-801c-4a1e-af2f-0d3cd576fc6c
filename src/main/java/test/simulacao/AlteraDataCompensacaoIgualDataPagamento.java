/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package test.simulacao;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class AlteraDataCompensacaoIgualDataPagamento {
    
    public static void main(String... args) {
        if (args.length == 0) {
            args = new String[]{""};
        }
        if (args.length >= 1) {
            String chave = args[0];
            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica(chave);
                Conexao.guardarConexaoForJ2SE(con);
                String sql = "update movpagamento "
                        + "set datapagamento = datalancamento "
                        + "where formapagamento in (select codigo from formapagamento where tipoformapagamento = 'CD')";
                Statement stm = con.createStatement();
                int rows = stm.executeUpdate(sql);
                
                Uteis.logar(null, rows + " pagamentos alterados com sucesso");

                //
            } catch (Exception ex) {
                Logger.getLogger(ResetaClientes.class.getName()).log(Level.SEVERE,
                        "Erro ao iniciar execução com a chave " + chave, ex);
            }

        }

    }
    
}

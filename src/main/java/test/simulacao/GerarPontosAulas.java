package test.simulacao;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.HistoricoPontos;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;

public class GerarPontosAulas {

    public static void gerarPontosAulasConfirmadas(Connection con){
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select distinct diaaula from aulaconfirmada where colaborador is null and diaaula > '2020-05-25'", con);
            while(rs.next()){
                Date diaaula = rs.getDate("diaaula");
                System.out.println("gerando pontos para aulas do dia " + Uteis.getData(diaaula));
                HistoricoPontos historicoPontosDao = new HistoricoPontos(con);
                historicoPontosDao.acresentarPontoPorAulaComfirmadaRobo(diaaula);
                System.out.println(" dia " + Uteis.getData(diaaula) + " processado");
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

}

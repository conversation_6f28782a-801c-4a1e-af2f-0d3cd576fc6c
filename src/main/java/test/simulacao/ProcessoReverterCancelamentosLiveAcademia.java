package test.simulacao;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.util.ExecuteRequestHttpService;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProcessoReverterCancelamentosLiveAcademia {

    private final String chave;
    private final Integer codigoEmpresa;
    private final Connection con;
    private final boolean simulacao;
    private final StringBuilder logGravar;
    private final String diretorioLog;
    private final Map<String, List<String>> mapCasosAvaliar;
    private final String urlDiscovery;
    private final ClientDiscoveryDataDTO clientDiscoveryDataDTO;

    public ProcessoReverterCancelamentosLiveAcademia(String chave, Integer codigoEmpresa, boolean simulacao, String diretorioLog, String urlDiscovery) throws Exception {
        this.con = new DAO().obterConexaoEspecifica(chave);
        Conexao.guardarConexaoForJ2SE(this.con);
        this.chave = chave;
        this.codigoEmpresa = codigoEmpresa;
        this.simulacao = simulacao;
        this.logGravar = new StringBuilder();
        this.diretorioLog = diretorioLog;
        this.mapCasosAvaliar = new HashMap<>();
        this.urlDiscovery = urlDiscovery;
        this.clientDiscoveryDataDTO = getClientDiscovery();
    }

    private ClientDiscoveryDataDTO getClientDiscovery() throws Exception {
        String response = ExecuteRequestHttpService.get(this.urlDiscovery + "/find/" + chave, new HashMap<>());
        return JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), ClientDiscoveryDataDTO.class);
    }

    public static void main(String[] args) throws Exception {

        String chave = "acadliveunidv8am";
        Integer codigoEmpresa = 1;
        String codigosContratos = "";
        String diretorioLog = "C:\\pacto\\log";
        String urlDiscovery = "https://discovery.ms.pactosolucoes.com.br";
        boolean simulacao = false;

        ProcessoReverterCancelamentosLiveAcademia processo = new ProcessoReverterCancelamentosLiveAcademia(chave, codigoEmpresa, simulacao, diretorioLog, urlDiscovery);
        processo.refazerCancelamentosContratosCanceladosAutomaticamente(codigosContratos);
    }

    private void refazerCancelamentosContratosCanceladosAutomaticamente(String codigosContratos) throws IOException, SQLException {
        try {
            mapCasosAvaliar.put("FALHA", new ArrayList<>());
            mapCasosAvaliar.put("AVALIAR", new ArrayList<>());

            String sql = getSqlConsultarAlunosCanceladosAutomaticamentePor30DiasSemAssinarContrato(codigosContratos);

            int total = SuperFacadeJDBC.contar("select count(sql.contrato) from (" + sql + ") as sql", con);
            int atual = 0;
            int sucesso = 0;

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                Integer codigoContrato = rs.getInt("contrato");
                String nome = rs.getString("nome");
                Integer codigoMatricula = rs.getInt("codigomatricula");
                Integer codigoContratoOperacao = rs.getInt("contratooperacao");
                Integer codigoMovimentoContaCorrente = rs.getInt("mcc_cancelamento");
                Boolean saldoDevedorAlterado = rs.getBoolean("saldodevedoralterado");
                String movparcelasCanceladas = rs.getString("movparcelas_canceladas");

                con.setAutoCommit(false);

                // remover saldo gerado pelo cancelamento
                if (saldoDevedorAlterado) {
                    mapCasosAvaliar.get("AVALIAR").add(String.format("Contrato: %d | MAT: %d - %s - Saldo devedor não foi ajustado!", codigoContrato, codigoMatricula, nome));
                } else if (!UteisValidacao.emptyNumber(codigoMovimentoContaCorrente)) {
                    if (!simulacao) {
                        SuperFacadeJDBC.executarConsulta("delete from movimentocontacorrenteclientecomposicao where movimentocontacorrentecliente = " + codigoMovimentoContaCorrente, con);
                        SuperFacadeJDBC.executarConsulta("delete from movimentocontacorrentecliente where codigo = " + codigoMovimentoContaCorrente, con);
                    }
                }

                try {
                    adicionarLog(String.format("%s%d\\%d - Processando contrato: %d dt_cancelamento: %s | MAT: %d - %s", simulacao ? " [MODO SIMULAÇÃO] " : "", ++atual, total, codigoContrato, Uteis.getData(rs.getDate("dataoperacao")), codigoMatricula, nome));

                    if (rs.getInt("qtdParcelasRenegociadas") > 0) {
                        mapCasosAvaliar.get("AVALIAR").add(String.format("Contrato: %d | MAT: %d - %s - Possui %d parcelas renegocadas!", rs.getInt("contrato"), rs.getInt("codigoMatricula"), rs.getString("nome"), rs.getInt("qtdParcelasRenegociadas")));
                        continue;
                    }
                    if (rs.getInt("qtdParcelasCanceladasPosCancelamento") > 0) {
                        mapCasosAvaliar.get("AVALIAR").add(String.format("Contrato: %d | MAT: %d - %s - Possui %d parcelas canceladas manualmente após o cancelamento automatico!", rs.getInt("contrato"), rs.getInt("codigoMatricula"), rs.getString("nome"), rs.getInt("qtdParcelasCanceladasPosCancelamento")));
                        continue;
                    }
                    if (rs.getInt("qtdPagamentosPosCancelamento") > 0) {
                        mapCasosAvaliar.get("AVALIAR").add(String.format("Contrato: %d | MAT: %d - %s - Possui %d pagamentos lançados após o cancelamento automatico!", rs.getInt("contrato"), rs.getInt("codigoMatricula"), rs.getString("nome"), rs.getInt("qtdPagamentosPosCancelamento")));
                        continue;
                    }

                    reverterCancelamentoSemCommit(codigoContrato, codigoContratoOperacao, movparcelasCanceladas);

                    if (!this.simulacao) {
                        con.commit();
                    }

                    atualizarSintetico(rs.getInt("pessoa"));
                    sucesso++;
                } catch (Exception e) {
                    mapCasosAvaliar.get("FALHA").add(String.format("Contrato: %d | MAT: %d - %s - erro: %s", codigoContrato, codigoMatricula, nome, e.getMessage()));
                    con.rollback();
                }
            }

            adicionarLog("==================================================");
            adicionarLog("TOTAL: " + total);
            adicionarLog("SUCESSO: " + sucesso);
            adicionarLog("FALHA: " + mapCasosAvaliar.get("FALHA").size());
            adicionarLog("AVALIAR: " + mapCasosAvaliar.get("AVALIAR").size());
            if (mapCasosAvaliar.get("FALHA").isEmpty()) {
                adicionarLog("==================================================");
                adicionarLog("FALHAS: ");
                mapCasosAvaliar.get("FALHA").forEach(this::adicionarLog);
            }
            if (mapCasosAvaliar.get("AVALIAR").isEmpty())
                adicionarLog("==================================================");
            adicionarLog("CASOS AVALIAR: ");
            mapCasosAvaliar.get("AVALIAR").forEach(this::adicionarLog);
        } catch (Exception e) {
            adicionarLog(e.getMessage());
        } finally {
            Uteis.salvarArquivo(String.format("%s-%s-%s.txt", "log-refazer-cancelamento", this.con.getCatalog(), Calendario.getData("yyyyMMddHHmmss")), logGravar.toString(), diretorioLog + File.separator);
        }
    }

    private String getSqlConsultarAlunosCanceladosAutomaticamentePor30DiasSemAssinarContrato(String codigosContratos) {
        String sql = "select  \n" +
                "  cli.codigomatricula,  \n" +
                "  pes.nome,  \n" +
                "  con.codigo as contrato,\n" +
                "  con.pessoa, \n" +
                "  con.situacao,\n" +
                "  con2.codigo as contrato_lancado_pelo_sistema,\n" +
                "  con2.situacao as situacao_contrato_lancado_pelo_sistema,\n" +
                "  cp.codigo as contratooperacao, \n" +
                "  ur.nome as responsavel,\n" +
                "  cp.dataoperacao as dataoperacao, \n" +
                "  cp.observacao,\n" +
                "  cp.descricaocalculo,\n" +
                "  mcc.codigo as mcc_cancelamento, \n" +
                "  mcc_max.saldoatual, \n" +
                "  (coalesce(mcc.codigo,0) > 0 and coalesce(mcc_max.codigo,0) > 0 and mcc.codigo <> mcc_max.codigo) as saldodevedoralterado, \n" +
                "  (select count(mpar.codigo) from movparcela mpar where mpar.contrato = con.codigo and mpar.situacao = 'RG') as qtdParcelasRenegociadas, \n" +
                "  (select count(mpar.codigo) from movparcela mpar  \n" +
                "   inner join observacaooperacao op on op.movparcela = mpar.codigo \n" +
                "   where mpar.contrato = con.codigo  \n" +
                "   and mpar.situacao = 'CA' \n" +
                "   and op.tipooperacao = 'PC' \n" +
                "   and op.dataoperacao::date > cp.dataoperacao::date) as qtdParcelasCanceladasPosCancelamento, \n" +
                "  (select count(rpg.codigo) from recibopagamento rpg where rpg.contrato = con.codigo and rpg.data::date > cp.dataoperacao) as qtdPagamentosPosCancelamento, \n" +
                "  (select array_to_string(array( \n" +
                "  select mpar.codigo from movparcela mpar  \n" +
                "   inner join observacaooperacao op on op.movparcela = mpar.codigo \n" +
                "  where mpar.contrato = con.codigo  \n" +
                "  and mpar.situacao = 'CA' \n" +
                "  and op.tipooperacao = 'PC' \n" +
                "  and op.dataoperacao::date = cp.dataoperacao::date \n" +
                " ),',','')) as movparcelas_canceladas \n" +
                "from contrato con \n" +
                " inner join pessoa pes on pes.codigo = con.pessoa  \n" +
                " inner join cliente cli on cli.pessoa = pes.codigo  \n" +
                " inner join contratooperacao cp on cp.contrato = con.codigo \n" +
                " left join usuario ur on ur.codigo = cp.responsavel \n" +
                " left join contrato con2 on con2.codigo = (select con_sub.codigo from contrato con_sub \n" +
                " \t\t\t\t\t\t\t\t\t\t\twhere con_sub.pessoa = con.pessoa \n" +
                "\t\t\t\t \t\t\t\t\t\t\tand coalesce(coalesce(con_sub.idexterno, con_sub.id_externo),0) = 0 \n" +
                "\t\t\t\t \t\t\t\t\t\t\torder by con_sub.codigo desc limit 1)\n" +
                " left join movimentocontacorrentecliente mcc  on mcc.codigo = (select mcc2.codigo from movimentocontacorrentecliente mcc2 \n" +
                "\t\t where mcc2.pessoa = con.pessoa \n" +
                "\t\t and mcc2.descricao = 'DEBITO DO VALOR DO CANCELAMENTO' \n" +
                "\t\t and mcc2.dataregistro::date = cp.dataoperacao::date \n" +
                "\t\t and trunc(mcc2.saldoatual::numeric,2) = trunc((cp.valor)::numeric,2) \n" +
                "\t\t ) \n" +
                " left join movimentocontacorrentecliente mcc_max on mcc_max.codigo = (select max(mcc2.codigo) from movimentocontacorrentecliente mcc2 where mcc2.pessoa = con.pessoa) \n" +
                " where 1 = 1 \n" +
                " and coalesce(con.idexterno,0) > 0 \n" +
                " and con.situacao = 'CA' \n" +
                " and cp.dataoperacao > '01/05/2025'\n" +
                " and cp.descricaocalculo not ilike '%IMPORTADA%'\n" +
                " and cp.responsavel = 3\n" +
                " and cp.observacao ilike '%MOTIVO: CANCELAMENTO POR MAIS DE 30 DIAS SEM ASSINAR O CONTRATO%'\n" +
                " and con2.codigo is null \n";

        if (!UteisValidacao.emptyString(codigosContratos)) {
            sql += " and con.codigo in (" + codigosContratos + ") \n";
        }
        if (!UteisValidacao.emptyNumber(this.codigoEmpresa)) {
            sql += " and con.empresa = " + this.codigoEmpresa + " \n";
        }
        return sql;
    }

    private void reverterCancelamentoSemCommit(Integer codigoContrato, Integer codigoContratoOperacao, String movParcelasCanceladas) throws Exception {
        Contrato contratoDAO = new Contrato(this.con);
        ContratoVO contratoVO = contratoDAO.consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_PARCELA_PRODUTOS);
        contratoDAO.montarDadosPessoa(contratoVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
        contratoDAO.montarDadosCliente(contratoVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);
        contratoDAO = null;

        reverterAlteracoesMovProdutos(contratoVO, this.con);

        if (!UteisValidacao.emptyString(movParcelasCanceladas)) {
            SuperFacadeJDBC.executarConsulta("update movparcela set situacao = 'EA' where codigo in (" + movParcelasCanceladas + ");", this.con);
            SuperFacadeJDBC.executarConsulta("update movproduto mpro set situacao = mpar.situacao from movprodutoparcela mpp, movparcela mpar \n" +
                    " where mpp.movproduto = mpro.codigo \n" +
                    " and mpar.codigo = mpp.movparcela \n" +
                    " and mpar.codigo in (" + movParcelasCanceladas + ");", this.con);
        }

        SuperFacadeJDBC.executarConsulta("UPDATE contrato\n" +
                "SET vigenciaateajustada = vigenciaate, situacao = 'AT'\n" +
                "WHERE codigo = " + codigoContrato, this.con);

        SuperFacadeJDBC.executarConsulta("UPDATE periodoacessocliente p SET datafinalacesso = con.vigenciaateajustada FROM contrato con\n" +
                "WHERE con.codigo = p.contrato " +
                "AND con.codigo = " + codigoContrato, this.con);

        SuperFacadeJDBC.executarConsulta("DELETE FROM periodoacessocliente WHERE tipoacesso = 'CN' AND contrato = " + codigoContrato, this.con);

        SuperFacadeJDBC.executarConsulta("DELETE FROM historicocontrato h\n" +
                "WHERE tipohistorico = 'CA' \n" +
                "AND contrato = " + codigoContrato, this.con);

        SuperFacadeJDBC.executarConsulta("UPDATE historicocontrato h SET datafinalsituacao = con.vigenciaateajustada FROM contrato con\n" +
                "WHERE con.codigo = h.contrato \n" +
                "AND h.tipohistorico = 'MA' \n" +
                "AND con.codigo = " + codigoContrato, this.con);

        SuperFacadeJDBC.executarConsulta("DELETE FROM contratooperacao WHERE codigo = " + codigoContratoOperacao, this.con);
    }

    private static void reverterAlteracoesMovProdutos(ContratoVO contratoVO, Connection con) throws Exception {
        Map<String, List<MovProdutoVO>> mapaMovProdutosMesReferencia = new HashMap<>();
        contratoVO.getMovProdutoVOs().forEach(mpro -> {
            if (mpro.getProduto().getTipoProduto().equals("PM")) {
                if (mapaMovProdutosMesReferencia.get(mpro.getMesReferencia()) == null) {
                    mapaMovProdutosMesReferencia.put(mpro.getMesReferencia(), new ArrayList<>());
                }
                mapaMovProdutosMesReferencia.get(mpro.getMesReferencia()).add(mpro);
            }
        });

        for (String mesRef : mapaMovProdutosMesReferencia.keySet()) {
            if (mapaMovProdutosMesReferencia.get(mesRef).size() > 1) {
                List<MovProdutoVO> movProdutoVOS = Ordenacao.ordenarLista(mapaMovProdutosMesReferencia.get(mesRef), "codigo");
                if (!UteisValidacao.emptyNumber(movProdutoVOS.get(movProdutoVOS.size() - 1).getMovProdutoBase())
                        && UteisValidacao.emptyNumber(movProdutoVOS.get(0).getMovProdutoBase())) {
                    MovProdutoVO movProdutoBase = movProdutoVOS.get(0);
                    Double somaProduto = 0.0;
                    if (UteisValidacao.emptyNumber(movProdutoBase.getTotalFinal())) {
                        for (MovProdutoVO mpro : movProdutoVOS) {
                            if (!mpro.getCodigo().equals(movProdutoBase.getCodigo())) {
                                somaProduto += mpro.getTotalFinal();
                                SuperFacadeJDBC.executarUpdate("delete from movproduto where codigo = " + mpro.getCodigo(), con);
                            }
                        }
                        movProdutoBase.setTotalFinal(somaProduto);
                        movProdutoBase.setPrecoUnitario(somaProduto);
                        movProdutoBase.setValorFaturado(somaProduto);
                        String update = "update movproduto set totalfinal = ?, precounitario = ?, valorfaturado = ?, situacao = 'EA' where codigo = ?";
                        PreparedStatement pstm = con.prepareStatement(update);
                        pstm.setDouble(1, movProdutoBase.getTotalFinal());
                        pstm.setDouble(2, movProdutoBase.getPrecoUnitario());
                        pstm.setDouble(3, movProdutoBase.getValorFaturado());
                        pstm.setInt(4, movProdutoBase.getCodigo());
                        pstm.execute();
                    }
                }
            }
        }
    }

    private void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.getData(new Date(), "dd/MM/yyyy HH:mm:mm:sss") + " --> " + msg;
        System.out.println(s);
        logGravar.append(s).append("\n");
    }

    public void atualizarSintetico(Integer codigoPessoa) throws IOException {
        StringBuilder urlZw = new StringBuilder(clientDiscoveryDataDTO.getServiceUrls().getZwUrl());
        urlZw.append("/prest/contratooperacao")
                .append("?chave=").append(chave)
                .append("&operacao=atualizarSintetico")
                .append("&codigoPessoa=").append(codigoPessoa);
        Map<String, String> headers = new HashMap<>();
        String responseZw = ExecuteRequestHttpService.executeHttpRequest(urlZw.toString(), null, headers, "POST", "UTF-8", false);
        JSONObject jsonObject = new JSONObject(responseZw);
        adicionarLog("\tSintetico atualizado! response zw: " + jsonObject);
        if (!jsonObject.optString("erro").isEmpty()) {
            adicionarLog("\tFalha ao tentar atualizar situação do aluno! " + jsonObject.optString("erro"));
        }
    }

}

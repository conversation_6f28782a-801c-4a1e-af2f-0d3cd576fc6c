package test.simulacao;

import br.com.pactosolucoes.atualizadb.processo.RefazerVinculoMovProdutoParcelaContratos;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;

public class ProcessoAjustarValorFinalContratosCanceladosImportacaoEvo {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";

    public static void main(String args[]) throws Exception {

        logGravar = new StringBuilder();

        try {
            Uteis.debug = true;

            Connection conAdm = new Conexao("**********************************************************************", "postgres", "pactodb").getConexao();
            Connection conEscava = new Conexao("******************************************************", "postgres", "pactodb").getConexao();
            nomeBanco = conAdm.getCatalog();

            Integer codEmpresa = 1;
            String matriculas = ""; // separadas por virgula

            ajustarValorFinalContratosCanceladosImportacaoEvo(codEmpresa, matriculas, conAdm, conEscava);
        } catch (Exception ex) {
            adicionarLog(ex.getMessage());
        } finally {
            Uteis.salvarArquivo(nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\PactoJ\\log\\" + File.separator);
        }
    }


    private static void ajustarValorFinalContratosCanceladosImportacaoEvo(Integer codEmpresa, String matriculas, Connection conAdm, Connection conEscava) throws Exception {
        Integer sucesso = 0;
        Integer falha = 0;

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append(" cli.matriculaexterna, con.codigo, con.id_externo, con.valorfinal,\n");
        sql.append(" (select count(mpro.codigo) from movproduto mpro inner join produto pro on pro.codigo = mpro.produto where pro.tipoproduto = 'PM' and mpro.contrato = con.codigo ) as qtdmovprodutos \n");
        sql.append("from contrato con \n");
        sql.append(" inner join cliente cli on cli.pessoa = con.pessoa \n");
        sql.append("where coalesce(con.id_externo,0) <> 0 \n");
        sql.append("and con.situacao = 'CA' \n");
        sql.append("and con.valorfinal > 0 \n");
        sql.append("and exists (select cp.codigo from contratooperacao cp where cp.contrato = con.codigo and cp.descricaocalculo ilike '%OPERA__O%IMPORTADA%') \n");
        sql.append("and con.empresa = ").append(codEmpresa).append(" \n");
        if (!UteisValidacao.emptyString(matriculas)) {
            sql.append("and cli.matriculaexterna in (").append(matriculas).append(") \n");
        }
        Integer total = SuperFacadeJDBC.contar("select count(*) from (" + sql + ") as sql", conAdm);

        int count = 0;
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conAdm);
        while (rs.next()) {

            Integer codigoContrato = rs.getInt("codigo");
            Integer idExterno = rs.getInt("id_externo");
            Double valorFinal = rs.getDouble("valorfinal");
            Integer qtdMovProdutos = rs.getInt("qtdmovprodutos");

            adicionarLog(++count + "/" + total + " [mat: " + rs.getInt("matriculaexterna") + "] - Verificando contrato id_externo: " + idExterno + ", valor atual: " + valorFinal);

            try {
                conAdm.setAutoCommit(false);

                StringBuilder sqlContratoEvo = new StringBuilder();
                sqlContratoEvo.append("select con.idclientecontrato, (con.contrato->>'valorVenda')::numeric as valorvenda from contratos con \n");
                sqlContratoEvo.append("where con.idclientecontrato = ").append(idExterno).append(" \n");

                ResultSet rsContratoEvo = SuperFacadeJDBC.criarConsulta(sqlContratoEvo.toString(), conEscava);

                if (rsContratoEvo.next()) {
                    Double valorVenda = rsContratoEvo.getDouble("valorvenda");

                    if (Uteis.arredondarForcando2CasasDecimais(valorVenda) == Uteis.arredondarForcando2CasasDecimais(valorFinal)) {
                        adicionarLog("\t Não ajustado! Valor já está correto!");
                        continue;
                    } else if (valorVenda < valorFinal) {
                        adicionarLog("\t Não ajustado! Valor da venda menor que o valor final importado! ");
                        continue;
                    } else if (valorVenda == 0.0) {
                        adicionarLog("\t Não ajustado! Valor da venda zerado! ");
                        continue;
                    }

                    String updateContrato = "update contrato set valorfinal = ?, valorbasecalculo = ? where codigo = ?";
                    PreparedStatement pstmContrato = conAdm.prepareStatement(updateContrato);
                    pstmContrato.setDouble(1, valorVenda);
                    pstmContrato.setDouble(2, valorVenda);
                    pstmContrato.setInt(3, codigoContrato);
                    pstmContrato.execute();

                    Double valorMovProduto = valorVenda / qtdMovProdutos;

                    String updateMovProudto = "update movproduto mpro set totalfinal = ?, valorfaturado = ?, precounitario = ? from produto pro \n" +
                            "where pro.codigo = mpro.produto \n" +
                            "and pro.tipoproduto = 'PM' \n" +
                            "and mpro.contrato = ? ";
                    PreparedStatement pstmMovProduto = conAdm.prepareStatement(updateMovProudto);
                    pstmMovProduto.setDouble(1, valorMovProduto);
                    pstmMovProduto.setDouble(2, valorMovProduto);
                    pstmMovProduto.setDouble(3, valorMovProduto);
                    pstmMovProduto.setInt(4, codigoContrato);
                    pstmMovProduto.execute();

                    conAdm.commit();

                    adicionarLog("\t Refazendo vinculos movprodutosparcelas do contrato: " + codigoContrato);
                    RefazerVinculoMovProdutoParcelaContratos.refazerMovProdutoParcela(conAdm, codigoContrato);

                    String updateSituacaoMovProduto = "update movproduto set situacao = 'CA' where codigo in (\n" +
                            "select mpro.codigo from movproduto mpro \n" +
                            "inner join produto pro on pro.codigo = mpro.produto \n" +
                            "left join movprodutoparcela mpp on mpp.movproduto = mpro.codigo \n" +
                            "where mpp.codigo is null \n" +
                            "and pro.tipoproduto = 'PM' \n" +
                            "and mpro.contrato = " + codigoContrato + " )";
                    SuperFacadeJDBC.executarConsulta(updateSituacaoMovProduto, conAdm);

                    sucesso++;
                    adicionarLog("\t Ajuste realizado! novo valor: " + valorVenda);
                }
            } catch (Exception ex) {
                falha++;
                conAdm.rollback();
                conAdm.setAutoCommit(true);
                adicionarLog("Contrato id_externo | " + idExterno + " | ERRO: " + ex.getMessage());
            } finally {
                conAdm.setAutoCommit(true);
            }
        }

        adicionarLog("--- TOTAL | " + total);
        adicionarLog("--- TOTAL | SUCESSO | " + sucesso);
        adicionarLog("--- TOTAL | FALHA   | " + falha);
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    public void setLogGravar(StringBuilder logGravar) {
        this.logGravar = logGravar;
    }
}

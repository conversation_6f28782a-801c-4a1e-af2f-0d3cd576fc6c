package test.simulacao;


import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
/**
 *
 * <AUTHOR>
 */
public class ThreadConsulta extends Thread {

    private Connection con;

    public ThreadConsulta() throws Exception {
        con = new DAO().obterConexaoEspecifica("7922cb331835c254fbbdbe4ce5ae7ccd");
    }



    @Override
    public void run() {
        try {
            while (true) {
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from cliente", con);
                System.out.println("---BEGIN SELECT---");
                while (rs.next()){
                    int n = rs.getInt("codigoMatricula");
                    //System.out.println("        " + rs.getInt("codigoMatricula"));
                }
                System.out.println("---END SELECT---");

                Thread.sleep(100);
            }
        } catch (SQLException ex) {
            Logger.getLogger(ThreadConsulta.class.getName()).log(Level.SEVERE, null, ex);
        } catch (Exception ex) {
            Logger.getLogger(ThreadConsulta.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}

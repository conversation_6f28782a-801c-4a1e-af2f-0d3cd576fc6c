package test.simulacao;


import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import negocio.comuns.utilitarias.Uteis;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
/**
 *
 * <AUTHOR>
 */
public class PesquisarArquivos {

    private static void listarDiretorios(String fname, List<String> lista) {
        File dir = new File(fname);
        String[] chld = dir.list();
        if (chld == null) {
            //System.out.println("Diretório especificado não existe ou não é um diretório..");
            //System.exit(0);
        } else if (!dir.isHidden()) {
            for (int i = 0; i < chld.length; i++) {
                String fileName = chld[i];
                //System.out.println(fileName);
                String aux = fname + "/" + fileName;
                if (aux.contains(".java")) {
                    lista.add(aux);
                }
                listarDiretorios(aux, lista);
            }
        }
    }

    public static void main(String[] args) {

        verificarTransacoesAbertasEternamente();

    }

    public static void verificarTransacoesAbertasEternamente(){
        List<String> listaArquivosJava = new ArrayList<String>();

        listarDiretorios("D:/PactoJ/Desenvolvimento/Sistemas/ZillyonWeb/tronco-novo/src/java/negocio/facade/jdbc",
                listaArquivosJava);

        for (String arqJava : listaArquivosJava) {

            try {
                InputStream in = new FileInputStream(arqJava);
                String conteudo = Uteis.convertStreamToString(in);
                in.close();

                int iRollback = contarOcorrenciasNoTexto("rollback()", conteudo);
                int iCommit = contarOcorrenciasNoTexto("commit()", conteudo);

                if (iRollback != iCommit) {

                    System.out.println(String.format("Arquivo: %s \n Rollbacks -> %s \n Commits -> %s",
                            new Object[]{arqJava, iRollback, iCommit}));

                }

            } catch (IOException ex) {
                Logger.getLogger(PesquisarArquivos.class.getName()).log(Level.SEVERE, null, ex);
            }

        }
    }

    public static int contarOcorrenciasNoTexto(String textoProcurado, String texto) {
        Pattern padrao = Pattern.compile(textoProcurado);
        Matcher matcher = padrao.matcher(texto);
        int cont = 0;
        while (matcher.find()) {
            cont++;
        }
        return cont;
    }
}

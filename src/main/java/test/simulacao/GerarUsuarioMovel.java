package test.simulacao;

import br.com.pactosolucoes.comuns.util.Formatador;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import negocio.facade.jdbc.crm.ModeloMensagem;
import negocio.facade.jdbc.oamd.LoginSiteRedeEmpresa;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import negocio.oamd.RedeEmpresaVO;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 28/07/2016.
 */
public class GerarUsuarioMovel{


    public static String NOME_BD_OAMD = "OAMD";
    public static Integer CODIGO_REDE = 1;
    public static String HOST_DB = "zw-rds-9.cmgq5kfs7ytf.sa-east-1.rds.amazonaws.com";
    public static String USER_DB = "zillyonweb";
    public static String PASSWORD_DB = "pactodb2020";
    public static String CHAVE_REDE = "chredeselfit";

    /*public static String NOME_BD_OAMD = "OAMD2";
    public static Integer CODIGO_REDE = 5;
    public static String HOST_DB = "localhost";
    public static String USER_DB = "postgres";
    public static String PASSWORD_DB = "pactodb";
    public static String CHAVE_REDE = "123456";*/

    public static void main(String... args) {

        System.out.println("Inicio do processo em "+ Calendario.hoje());
        Connection conOAMD = obterConexao(NOME_BD_OAMD);
        List<ConexaoEmpresaFinanceiro> listaCon = consultarListaConexaoEmpresaFinanceiro(conOAMD);
        try {
            StringBuilder logProcesso = new StringBuilder();
            for (ConexaoEmpresaFinanceiro conexaoEmpresaFinanceiro: listaCon){
                System.out.println("INICIO: " + conexaoEmpresaFinanceiro.getNomeUnidade() + " em "+ Calendario.hoje());
                gerarUsuarioMovelAPI(conexaoEmpresaFinanceiro, conOAMD, logProcesso);
                System.out.println("FIM: " + conexaoEmpresaFinanceiro.getNomeUnidade() + " em "+ Calendario.hoje() );
            }
            System.out.println(logProcesso.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
        }finally {
            for (ConexaoEmpresaFinanceiro obj: listaCon){
                try {
                    obj.getCon().close();
                }catch (Exception e){
                    System.out.println("ERRO AO FECHAR CONEXÃO COM O BD. ERRO: " +e.getMessage());
                }

            }
        }
        System.out.println("Fim do processo em "+Calendario.hoje());
    }

    public static List<ConexaoEmpresaFinanceiro> consultarListaConexaoEmpresaFinanceiro(Connection conOAMD){
        List<ConexaoEmpresaFinanceiro> lista = new ArrayList<ConexaoEmpresaFinanceiro>();
        try{
            StringBuilder sql = new StringBuilder();
            sql.append("select ef.codigo, ef.nomeFantasia, \"nomeBD\" \n ");
            sql.append("from empresaFinanceiro ef \n");
            sql.append("inner join empresa emp on emp.chave = ef.chaveZw \n");
            sql.append("where ef.redeEmpresa_id = ").append(CODIGO_REDE);
            Statement st = conOAMD.createStatement();
            ResultSet rs = st.executeQuery(sql.toString());

            while (rs.next()){
                ConexaoEmpresaFinanceiro obj = ((new GerarUsuarioMovel()).new ConexaoEmpresaFinanceiro());
                obj.setCodigo(rs.getInt("codigo"));
                obj.setNomeUnidade(rs.getString("nomeFantasia"));
                obj.setCon(obterConexao(rs.getString("nomeBD")));
                lista.add(obj);
            }
            return lista;

        }catch (Exception e){
            e.printStackTrace();
        }
        return lista;
    }


    public static void gerarUsuarioMovelAPI(ConexaoEmpresaFinanceiro conexaoEmpresaFinanceiro, Connection conOAMD, StringBuilder logProcesso)throws Exception{
        List<ClienteVO> listaCliente = consultarClientesAtivos(conexaoEmpresaFinanceiro.getCon());
        UsuarioMovel usuarioMovelDao = new UsuarioMovel(conexaoEmpresaFinanceiro.getCon());
        for (ClienteVO clienteVO: listaCliente){
            String email =  consultarEmail(clienteVO, conexaoEmpresaFinanceiro.getCon());
            if (email == null){
                logProcesso.append(conexaoEmpresaFinanceiro.getNomeUnidade()).append(" - ").append("Cliente:").append(clienteVO.getCodigoMatricula()).append("-").append(clienteVO.getPessoa().getNome()).append(" NÃO TEM EMAIL. \n");
                continue;
            }
            if ((clienteVO.getPessoa().getCfp() == null) || (clienteVO.getPessoa().getCfp().trim().equals(""))){
                logProcesso.append(conexaoEmpresaFinanceiro.getNomeUnidade()).append(" - ").append("Cliente:").append(clienteVO.getCodigoMatricula()).append("-").append(clienteVO.getPessoa().getNome()).append(" NÃO TEM CPF. \n");
                continue;
            }
            UsuarioMovelVO usuarioMovelVO = consultarUsuarioMovel(email, conexaoEmpresaFinanceiro.getCon());
            clienteVO.getPessoa().setEmail(email);
            boolean incluir = false;
            if (usuarioMovelVO == null){
                incluir = true;
                usuarioMovelVO  = new UsuarioMovelVO();
                usuarioMovelVO.setNome(email);
                usuarioMovelVO.setEmpresa(1);
                usuarioMovelVO.setOrigem("API");
            }
            usuarioMovelVO.setCliente(clienteVO);
            usuarioMovelVO.setAtivo(true);
            // gerar nova senha para o usuário móvel.
            usuarioMovelVO.setSenha(Formatador.removerMascara(clienteVO.getPessoa().getCfp()));
            if (incluir){
                usuarioMovelDao.incluir(usuarioMovelVO);
            }else{
                alterarSenhaUsuarioMovel(usuarioMovelVO, conexaoEmpresaFinanceiro.getCon());
            }
            alterarSenhaLoginSiteOAMD(email, clienteVO.getPessoa().getCfp(), conexaoEmpresaFinanceiro.getCodigo(),conOAMD);
            //enviarEmail(conModeloMensagem, email,usuarioMovelVO.getSenha());
        }
    }

    public static void enviarEmail(Connection conModeloMensagem, String email, String senha)throws Exception{
        ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = SuperControle.getConfiguracaoSMTPNoReply();
        UteisEmail uteisEmail = new UteisEmail();
        String assuntoEmail = "NOVO PORTAL DA SELF IT";
        ModeloMensagem modeloMensagem = new ModeloMensagem(conModeloMensagem);
        ModeloMensagemVO modeloMensagemVO = null;
        List<ModeloMensagemVO> listaModelo = modeloMensagem.consultarPorTitulo("NOVO PORTAL", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if ((listaModelo != null) && (listaModelo.size() > 0)){
            modeloMensagemVO = listaModelo.get(0);
        }else{
            return ;
        }
        uteisEmail.novo(assuntoEmail, configuracaoSistemaCRM);
        String mensagemEmail =  prepararMensagemEmail(modeloMensagemVO, email, senha);
        uteisEmail.enviarEmailN(new String[]{email}, mensagemEmail, assuntoEmail, "");
    }

    public static String prepararMensagemEmail(ModeloMensagemVO modeloMensagemVO, String email, String senha) throws Exception {
        String modeloMensagem = modeloMensagemVO.getMensagem();
        modeloMensagem = modeloMensagem.replace("@USUARIO_VENDAS_ONLINE@", email);
        modeloMensagem = modeloMensagem.replace("@SENHA_VENDAS_ONLINE@", senha);
        return modeloMensagem;
    }

    public static void alterarSenhaUsuarioMovel(UsuarioMovelVO usuarioMovelVO ,Connection conZillyon)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("update UsuarioMovel set ativo = true, senha = '").append(Uteis.encriptar(usuarioMovelVO.getSenha())).append("' ");
        sql.append("where codigo = ").append(usuarioMovelVO.getCodigo());
        Statement st = conZillyon.createStatement();
        st.execute(sql.toString());
    }

    public static void alterarSenhaLoginSiteOAMD(String email, String cpf, Integer codigoEmpresaFinanceiro, Connection conOAMD)throws Exception{
        LoginSiteRedeEmpresa loginSiteRedeEmpresa = new LoginSiteRedeEmpresa(conOAMD);
        RedeEmpresaVO redeEmpresaVO = new RedeEmpresaVO();
        redeEmpresaVO.setId(CODIGO_REDE);
        redeEmpresaVO.setChaverede(CHAVE_REDE);
        loginSiteRedeEmpresa.registrarLoginRedeEmpresa(email,redeEmpresaVO,codigoEmpresaFinanceiro, cpf);
        loginSiteRedeEmpresa = null;
    }

    public static String consultarEmail(ClienteVO clienteVO,Connection conZillyon)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from email \n");
        sql.append("where pessoa = ").append(clienteVO.getPessoa().getCodigo());
        Statement st = conZillyon.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            return rs.getString("email");
        }
        return null;
    }

    public static UsuarioMovelVO consultarUsuarioMovel(String email ,Connection conZillyon)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from usuarioMovel \n");
        sql.append("where nome = '").append(email).append("'");
        Statement st = conZillyon.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            UsuarioMovelVO usuarioMovelVO = new UsuarioMovelVO();
            usuarioMovelVO.setCodigo(rs.getInt("codigo"));
            return usuarioMovelVO;
        }
        return null;
    }

    public static List<ClienteVO> consultarClientesAtivos(Connection conZillyon)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select p.cfp, p.nome, cli.codigoMatricula, cli.* \n");
        sql.append("from cliente cli \n");
        sql.append("inner join pessoa p on p.codigo = cli.pessoa \n");
        sql.append("inner join situacaoClienteSinteticoDW dw on dw.codigoCliente = cli.codigo \n");
        sql.append("where dw.situacao = 'AT' ");
        Statement st = conZillyon.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<ClienteVO> listaCliente = new ArrayList<ClienteVO>();
        while (rs.next()){
            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setCodigo(rs.getInt("codigo"));
            clienteVO.setCodigoMatricula(rs.getInt("codigomatricula"));
            clienteVO.setPessoa(new PessoaVO());
            clienteVO.getPessoa().setCodigo(rs.getInt("pessoa"));
            clienteVO.getPessoa().setCfp(rs.getString("cfp"));
            clienteVO.getPessoa().setNome(rs.getString("nome"));
            listaCliente.add(clienteVO);
        }
        return listaCliente;
    }


    /*public static List<Connection> retornarListaConexoes(){
        List<Connection> lista = new ArrayList<Connection>();
        try{
            lista.add(obterConexao("bdzillyonselfitboaviagem"));
            lista.add(obterConexao("bdzillyonselfitgraca"));
            lista.add(obterConexao("bdzillyonselfitbarra"));
            lista.add(obterConexao("bdzillyonselfitmagshopping"));
            lista.add(obterConexao("bdzillyonselfitparalela"));
            lista.add(obterConexao("bdzillyonselfitpituba2"));
            lista.add(obterConexao("bdzillyonselfitpontaverde"));
            lista.add(obterConexao("bdzillyonselfitcaruaru"));
            lista.add(obterConexao("bdzillyonselfitriverside"));
            lista.add(obterConexao("bdzillyonselfitpaulovi"));
            lista.add(obterConexao("bdzillyonselfitmangabeira"));
            lista.add(obterConexao("bdzillyonselfitcabula"));
            lista.add(obterConexao("bdzillyonselfitiguatemi"));
            lista.add(obterConexao("bdzillyonselfitepitaciopessoa"));
            lista.add(obterConexao("bdzillyonselfitsaoraphael"));
        }catch (Exception e){
            System.out.println("ERRO AO CRIAR CONEXÃO COM O BD. ERRO: " +e.getMessage());
        }
        return lista;
    }*/

    /*public static Connection obterConexao(String nomeBD)throws Exception{
        String hostBD = "zw-rds-9.cmgq5kfs7ytf.sa-east-1.rds.amazonaws.com";
        String porta = "5432";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        String userBD = "zillyonweb";
        String passwordBD = "pactodb2020";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }*/
    public static Connection obterConexao(String nomeBD){
        try{
            //String hostBD = "localhost";
            String porta = "5432";
            String url = "jdbc:postgresql://" + HOST_DB + ":" + porta + "/" + nomeBD;
            String driver = "org.postgresql.Driver";
            //String userBD = "postgres";
            //String passwordBD = "pactodb";
            Class.forName(driver);
            return DriverManager.getConnection(url, USER_DB, PASSWORD_DB);
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }


    public  class ConexaoEmpresaFinanceiro{
        private Integer codigo;
        private Connection con;
        private String nomeUnidade;

        public Integer getCodigo() {
            return codigo;
        }

        public void setCodigo(Integer codigo) {
            this.codigo = codigo;
        }

        public Connection getCon() {
            return con;
        }

        public void setCon(Connection con) {
            this.con = con;
        }

        public String getNomeUnidade() {
            return nomeUnidade;
        }

        public void setNomeUnidade(String nomeUnidade) {
            this.nomeUnidade = nomeUnidade;
        }
    }
}

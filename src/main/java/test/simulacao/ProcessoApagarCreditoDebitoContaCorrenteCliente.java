package test.simulacao;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteCliente;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by <PERSON><PERSON> on 24/01/220.
 */
public class ProcessoApagarCreditoDebitoContaCorrenteCliente {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";

    public static void main(String[] args) throws IOException {
        try {
            logGravar = new StringBuilder();
            Uteis.debug = true;

            Connection con = new Conexao("****************************************", "postgres", "pactodb").getConexao();
            Conexao.guardarConexaoForJ2SE(con);
            nomeBanco = con.getCatalog();


            //colocar o código dos clientes na lista
            //OBS.: lista específica de alunos (Colocar o código do cliente!)
            //Caso não queira somente os alunos da lista deve colocar os parametros  "todosComCredito" e "todosComDebito" como FALSE
            Set<Integer> clientesAjustar = listaAjustar();

            //alunos da empresa
            Integer empresa = 1;

            // todos alunos da academia com crédito na conta corrente
            // OBS. IRÁ PEGAR TODOS OS ALUNOS DA EMPRESA + OS ALUNOS PASSADOS NA LISTA "clientesAjustar"
            boolean todosComCredito = false;

            // todos alunos da academia com débito na conta corrente
            // OBS. IRÁ PEGAR TODOS OS ALUNOS DA EMPRESA + OS ALUNOS PASSADOS NA LISTA "clientesAjustar"
            boolean todosComDebito = false;

            // lançar no financeiro a conta
            boolean lancarSaidaFinanceiro = false;

            ajustarContaCorrente(empresa, todosComCredito, todosComDebito, lancarSaidaFinanceiro, clientesAjustar, con);

        } catch (Exception ex) {
            adicionarLog(ex.getMessage());
        } finally {
            Uteis.salvarArquivo("ApagarContaCorrenteCliente" + nomeBanco + "_" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\PactoJ\\log\\" + File.separator);
        }
    }

    private static Set<Integer> listaAjustar() {
        Set<Integer> clientesAjustar = new HashSet<>();
        clientesAjustar.add(123);
        clientesAjustar.add(12);
        clientesAjustar.add(1245);
        return clientesAjustar;
    }

    private static void ajustarContaCorrente(Integer empresa, boolean todosComCredito,
                                                    boolean todosComDebito, boolean lancarSaidaFinanceiro,
                                                    Set<Integer> clientesAjustar, Connection con) {
        try {

            MovimentoContaCorrenteCliente moviDAO = new MovimentoContaCorrenteCliente(con);

            if (todosComCredito) {
                ResultSet rs = moviDAO.consultarPendenciasClienteCreditoContaCorrente(empresa, null, null, null);
                while (rs.next()) {
                    clientesAjustar.add(rs.getInt("cli"));
                }
            }

            if (todosComDebito) {
                ResultSet rs = moviDAO.consultarPendenciasClienteDevendoContaCorrente(empresa, null, null, null);
                while (rs.next()) {
                    clientesAjustar.add(rs.getInt("cli"));
                }
            }

            Usuario usuarioDAO = new Usuario();
            UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
            usuarioDAO = null;

            adicionarLog("TOTAL DE CLIENTES AJUSTAR: " + clientesAjustar.size());

            for (Integer codigoCliente : clientesAjustar) {
                String matricula = "";
                try {
                    Cliente clienteDAO = new Cliente(con);
                    ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    clienteDAO = null;

                    matricula = clienteVO.getMatricula();

                    moviDAO = new MovimentoContaCorrenteCliente(con);
                    MovimentoContaCorrenteClienteVO ultimoMovimento = moviDAO.consultarPorCodigoPessoa(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    moviDAO.ajustarSaldoContaCorrenteCliente(clienteVO , ultimoMovimento, 0.0, usuarioVO, Calendario.hoje(), true, 0.0, lancarSaidaFinanceiro);
                    moviDAO = null;
                    adicionarLog("Mat.: " + matricula + " | SUCESSO");
                } catch (Exception ex) {
                    adicionarLog("Mat.: " + matricula + " | ERRO - " + ex.getMessage());
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    public void setLogGravar(StringBuilder logGravar) {
        this.logGravar = logGravar;
    }
}

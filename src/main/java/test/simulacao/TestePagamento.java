/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package test.simulacao;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class TestePagamento {

    public static void main(String... args) {
        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
        MovParcelaVO movParcela;

        try {
            Connection con = Conexao.getInstance().getConexao();
            Conexao.guardarConexaoForJ2SE(con);
            movParcela = new MovParcela().consultarPorChavePrimaria(17096, Uteis.NIVELMONTARDADOS_TODOS);
            List lista = new ArrayList();
            lista.add(movParcela);
            FormaPagamentoVO formaPagamento = new FormaPagamentoVO();
            formaPagamento.setDefaultRecorrencia(true);
            formaPagamento.setDescricao("DINHEIRO");
            formaPagamento.setTipoFormaPagamento("AV");
            formaPagamento = new FormaPagamento().criarOuConsultarSeExistePorDescricao(formaPagamento);
            UsuarioVO u = new Usuario().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setFormaPagamento(formaPagamento);
            movPagamentoVO.setMovPagamentoEscolhida(true);
            movPagamentoVO.setValor(movParcela.getValorParcela());
            movPagamentoVO.setPessoa(movParcela.getContrato().getPessoa());
            movPagamentoVO.setNomePagador(movParcela.getContrato().getPessoa().getNome());
            movPagamentoVO.setOpcaoPagamentoDinheiro(true);

            movPagamentoVO.setResponsavelPagamento(u);
            movPagamentoVO.setEmpresa(movParcela.getEmpresa());
            listaPagamento.add(movPagamentoVO);

            ReciboPagamentoVO reciboObj = new MovPagamento().incluirListaPagamento(
                    listaPagamento,
                    lista,
                    null,
                    movParcela.getContrato(),
                    false, 0.0);

            Uteis.logar(null, "Recibo -> " + reciboObj.getCodigo());

        } catch (Exception ex) {
            Logger.getLogger(TestePagamento.class.getName()).log(Level.SEVERE, null, ex);
        }

    }
}

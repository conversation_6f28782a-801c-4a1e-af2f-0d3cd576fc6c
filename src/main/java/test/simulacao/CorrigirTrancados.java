package test.simulacao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.TrancamentoContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.TrancamentoContrato;
import negocio.facade.jdbc.utilitarias.Conexao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

/**
 * <AUTHOR>
 *
 */
public class CorrigirTrancados {
	
	
	public static List<TrancamentoContratoVO> montarRetornos(Integer empresa, Connection con) throws Exception{
		List<TrancamentoContratoVO> retornos = new ArrayList<TrancamentoContratoVO>();
		String sql = "SELECT codigo FROM contrato WHERE situacao LIKE 'TR' AND empresa = "+empresa;
		
		PreparedStatement preStm1 = con.prepareStatement(sql);
		
		ResultSet resultSet = preStm1.executeQuery();
		while(resultSet.next()){
			sql = "DELETE FROM contratooperacao WHERE contrato = "+resultSet.getInt("codigo")+" AND tipooperacao LIKE 'TV'";
			PreparedStatement preStm2 = con.prepareStatement(sql);
			preStm2 = con.prepareStatement(sql);
			preStm2.execute();
			
			sql = " SELECT t.* FROM contratooperacao co "+
				  " INNER JOIN contrato c ON c.codigo = co.contrato AND co.contrato = "+resultSet.getInt("codigo")+
				  " INNER JOIN trancamentocontrato t ON c.codigo = t.contrato "+
				  " WHERE co.tipooperacao LIKE 'TR' AND co.datafimefetivacaooperacao <= '"+Uteis.getDataJDBC(Calendario.hoje())+
				  "' ORDER BY co.datafimefetivacaooperacao DESC";
			
			PreparedStatement preStm3 = con.prepareStatement(sql);
			ResultSet consultaData = preStm3.executeQuery();
			
			if(consultaData.next()){
				TrancamentoContrato trancamentoContratoDao = new TrancamentoContrato(con);
				TrancamentoContratoVO co = trancamentoContratoDao.montarDados(consultaData, Uteis.NIVELMONTARDADOS_MINIMOS);
				retornos.add(co);
			}
		}
		return retornos;
	}
	
	public static void gravarRetornos(Integer empresa, Connection con) throws SQLException{
		try{
			con.setAutoCommit(false);
			List<TrancamentoContratoVO> retornos = montarRetornos(empresa, con);
			for(TrancamentoContratoVO obj : retornos){
				Contrato contrato = new Contrato(con);
				Cliente clienteDAO = new Cliente(con);
				ZillyonWebFacade zw = new ZillyonWebFacade(con);

				obj.getContratoVO().setSituacao("AT");
				contrato.alterarSituacaoContrato(obj.getContratoVO());
	            ClienteVO cliente = clienteDAO.consultarPorCodigoPessoa(obj.getContratoVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
	            cliente.setSituacao("AT");
	            clienteDAO.alterarSituacaoClienteSemPessoaCommit(cliente);
	            
//	            obj.inicializarDadosOperacaoContratoRetornoNoPrazo();
	            inserirContratoOperacao(obj, con);
	            
//	            obj.inicializarDadosHistoricoContratoRetornoNoPrazo();
	            inserirHistorico(obj, con);
	            

	            zw.atualizarSintetico(clienteDAO.
	                    consultarPorCodigoPessoa(obj.getContratoVO().getPessoa().getCodigo(),
	                    Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW),
	                    Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
	            System.out.println("Trancamento do Contrato "+obj.getContratoVO().getCodigo()+" foi dado retorno.");
	            
	            
			}
			con.commit();
		}catch (Exception e){
			con.rollback();
            e.printStackTrace();
        }finally{
        	con.setAutoCommit(true);
        }
		
	}
	
	private static void inserirContratoOperacao(TrancamentoContratoVO trancamento, Connection con) throws Exception{
		String sqlOperacao = "SELECT dataFimEfetivacaoOperacao, responsavel FROM ContratoOperacao " +
		"WHERE tipooperacao LIKE 'TR' AND contrato = "+trancamento.getContratoVO().getCodigo()+
		" AND dataFimEfetivacaoOperacao <= '"+Uteis.getDataJDBC(Calendario.hoje())+"'";
		
		PreparedStatement stm = con.prepareStatement(sqlOperacao);
		ResultSet query = stm.executeQuery();
		while(query.next()){
			String sql = "INSERT INTO ContratoOperacao( contrato, tipoOperacao, "
					+ "operacaoPaga, dataOperacao, dataInicioEfetivacaoOperacao, "
					+ "dataFimEfetivacaoOperacao, responsavel, descricaoCalculo) "
					+ "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?)";
			PreparedStatement pstm = con.prepareStatement(sql);
			int i = 1;
			pstm.setInt(i++, trancamento.getContratoVO().getCodigo());
			pstm.setString(i++, "RT");
			pstm.setBoolean(i++, false);
			Date dataAnterior = Uteis.somarCampoData(query.getDate("dataFimEfetivacaoOperacao"), Calendar.DAY_OF_MONTH, 1);
			pstm.setDate(i++, Uteis.getDataJDBC(dataAnterior));
			pstm.setDate(i++, Uteis.getDataJDBC(dataAnterior));
			pstm.setDate(i++, Uteis.getDataJDBC(dataAnterior));
			pstm.setInt(i++, query.getInt("responsavel"));
			pstm.setString(i++, "RETORNO DE TRANCAMENTO");

			pstm.execute();
		}
		
	}
	
	private static void inserirHistorico(TrancamentoContratoVO trancamento, Connection con) throws Exception{
		String sqlHistorico = "SELECT datafinalsituacao, responsavelregistro FROM historicocontrato " +
				"WHERE tipohistorico LIKE 'TR' AND contrato = "+trancamento.getContratoVO().getCodigo()+
				" AND datafinalsituacao <= '"+Uteis.getDataJDBC(Calendario.hoje())+"'";
		PreparedStatement stm = con.prepareStatement(sqlHistorico);
		ResultSet query = stm.executeQuery();
		while(query.next()){
			String sql = "INSERT INTO HistoricoContrato( contrato, descricao, "
	            + "responsavelRegistro,  "
	            + "dataRegistro, dataInicioSituacao, "
	            + "dataFinalSituacao, tipoHistorico, retornoManual, situacaorelativahistorico ) "
	            + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ? )";
			
			PreparedStatement pstm = con.prepareStatement(sql);
			int i = 1;
			pstm.setInt(i++, trancamento.getContratoVO().getCodigo());
			pstm.setString(i++, "RETORNO TRANCAMENTO");
			pstm.setInt(i++, query.getInt("responsavelregistro"));
			
			Date dataAnterior = Uteis.somarCampoData(query.getDate("datafinalsituacao"), Calendar.DAY_OF_MONTH, 1);
			pstm.setDate(i++, Uteis.getDataJDBC(dataAnterior));
			pstm.setDate(i++, Uteis.getDataJDBC(dataAnterior));
			pstm.setDate(i++, Uteis.getDataJDBC(dataAnterior));
			pstm.setString(i++, "RT");
			pstm.setBoolean(i++, false);
			pstm.setString(i++, "");
			pstm.execute();
		}
	}
	
	public static void main(String... args) {
		System.out.println("Inicio do processo em "+Calendario.hoje());
		try {
			if (args.length == 0) {
				Conexao conexao = Conexao.getInstance();
				Conexao.guardarConexaoForJ2SE(conexao.getConexao());
		        gravarRetornos(5, conexao.getConexao());
		    }else {
				String chave = args[0];
				DAO dao = new DAO();
				Connection con = dao.obterConexaoEspecifica(chave);
				Conexao.guardarConexaoForJ2SE(con);
				Integer empresa = Integer.parseInt(args[1]);
				gravarRetornos(empresa, con);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		System.out.println("Fim do processo em "+Calendario.hoje());
		
	}
	
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package test.simulacao;

import br.com.pactosolucoes.comuns.util.Formatador;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.contrato.MovProdutoInterfaceFacade;
import org.jdom.Element;

/**
 *
 * <AUTHOR>
 */
public class CorrigirBancoWinner {


    public static void main(String args[]){
        Connection con = obterConexao();
        /*corrigirContratoModalidade(con);
        corrigirValorContrato_E_PeriodoAcesso(con);
        corrigirDadosDiversos(con);
        importarCodigoAcesso(con);*/
        corrigirPeriodoAcessoBonus(con);

    }


    private static Connection obterConexao(){
        try{
          Conexao conexao = Conexao.getInstance();
          return conexao.getConexao();
       }catch(Exception e){
           e.printStackTrace();
           return null;
       }

    }

    private static void corrigirDadosDiversos(Connection con){
          try{
            con.setAutoCommit(false);
            //Corrigir os trancamentos que estão com situação "Em aberto"
            String sqlMovProduto = "update movProduto set situacao = 'PG' where descricao like 'TRANCAMENTO%' and TOTALFINAL = 0";
            PreparedStatement pstMovProduto = con.prepareStatement(sqlMovProduto);
            pstMovProduto.execute();

            //Corrigir os registros duplicados na tabela movParcela
            String sqlMovParcela = "delete from movParcela where dataregistro = '2011-12-27'";
            PreparedStatement pstMovParcela = con.prepareStatement(sqlMovParcela);
            pstMovParcela.execute();

            PagamentoMovParcela.corrigirPagamentosSemMovParcelas(con);

           con.commit();
           System.out.println("Excução do método 'corrigirDadosDiversos' realizado com sucesso ");
        }catch(Exception e){
            try {
                con.rollback();
            } catch (SQLException ex) {
                Logger.getLogger(CorrigirBancoWinner.class.getName()).log(Level.SEVERE, null, ex);
            }
           System.out.println("Erro ao corrigirDadosDiversos");
           e.printStackTrace();
       }finally{
            try {
                con.setAutoCommit(true);
            } catch (SQLException ex) {
                Logger.getLogger(CorrigirBancoWinner.class.getName()).log(Level.SEVERE, null, ex);
            }
       }

    }


    private static void corrigirContratoPeriodoAcesso(int codigoContrato, Date dataUltimoPeriodoAcesso, Connection con)throws Exception{
        // Pegar a operação de bônus lançada, pois a (data fim ajustada)"corretamente" do contrato,
        // está no valor do campo "dataFimEfetivacaoOperacao".
        String sql = "select dataFimEfetivacaoOperacao from contratoOperacao where tipoOperacao = 'BA' and contrato = " + codigoContrato;
        PreparedStatement pst = con.prepareStatement(sql);
        ResultSet resultDadosBonus = pst.executeQuery();
        if (resultDadosBonus.next()){
            // Corrigir a dataFim ajustada do contrato.
            String sqlContrato = "update contrato set vigenciaAteAjustada = ? where codigo = ?";
            PreparedStatement pstContrato = con.prepareStatement(sqlContrato);
            pstContrato.setDate(1, Uteis.getDataJDBC(resultDadosBonus.getDate("dataFimEfetivacaoOperacao")));
            pstContrato.setInt(2, codigoContrato);
            pstContrato.execute();



            // corrigir o periodoAcesso do cliente.
            Calendar dataIniAcesso = Calendario.getInstance();
            dataIniAcesso.setTime(dataUltimoPeriodoAcesso);
            dataIniAcesso.add(Calendar.DAY_OF_MONTH, 1);
            if (Calendario.maior(dataIniAcesso.getTime(), resultDadosBonus.getDate("dataFimEfetivacaoOperacao"))){
                // apagar o periodo de acesso do bonus. Neste caso o registro foi incluido de forma errada.
                String sqlExcluirAcessoCliente = "delete from periodoAcessoCliente where contrato = ? and tipoAcesso = 'BO'";
                PreparedStatement pstExcluirAcessoCliente = con.prepareStatement(sqlExcluirAcessoCliente);
                pstExcluirAcessoCliente.setInt(1, codigoContrato);
                pstExcluirAcessoCliente.execute();
            }else{
                String sqlAcessoCliente = "update periodoAcessoCliente set datainicioacesso =?, dataFinalAcesso = ? where contrato = ? and tipoAcesso = 'BO'";
                PreparedStatement pstAcessoCliente = con.prepareStatement(sqlAcessoCliente);
                pstAcessoCliente.setDate(1, Uteis.getDataJDBC(dataIniAcesso.getTime()));
                pstAcessoCliente.setDate(2, Uteis.getDataJDBC(resultDadosBonus.getDate("dataFimEfetivacaoOperacao")));
                pstAcessoCliente.setInt(3, codigoContrato);
                pstAcessoCliente.execute();
            }
        }

    }

    private static Date retornarUltimaDataAcessoCliente(Connection con, int codigoContrato) throws Exception{
        // Pegar a maior data lançada para o Acesso do Cliente.
        String sqlPeriodoAcesso = "select max(dataFinalAcesso) dataFinalAcesso " +
                                  "  from periodoAcessoCliente " +
                                  "  where contrato = " + codigoContrato +
                                  "  and tipoAcesso <> 'BO' ";
        PreparedStatement pst = con.prepareStatement(sqlPeriodoAcesso);
        ResultSet resultDados = pst.executeQuery();
        if (resultDados.next()){
            return resultDados.getDate("dataFinalAcesso");
        }
        return null;
    }

    private static void corrigirValorContrato_E_PeriodoAcesso(Connection con) {
        try{
            /*
             * Corrigir Data fim do contrato e PeriodoAcesso lançado para Bonus.
             */

            con.setAutoCommit(false);
            String sqlContrato = "select distinct(contrato) contrato from periodoAcessoCliente where datafinalAcesso is null and dataInicioAcesso is null";
            PreparedStatement pst = con.prepareStatement(sqlContrato);
            ResultSet resultContratos = pst.executeQuery();
            // Excluir os periodo de acesso que estão com valores nullos para os campos "dataInicioAcesso" e "datafinalAcesso"
            String sqlExcluir = "delete from periodoAcessoCliente where datafinalAcesso is null and dataInicioAcesso is null";
            PreparedStatement pstExcluir = con.prepareStatement(sqlExcluir);
            pstExcluir.execute();
            while (resultContratos.next()){
                // Pegar o último acesso lançado para o cliente.
                Date dataUltimoPeriodoAcesso = retornarUltimaDataAcessoCliente(con,resultContratos.getInt("contrato"));
                if (dataUltimoPeriodoAcesso != null){
                    corrigirContratoPeriodoAcesso(resultContratos.getInt("contrato"), dataUltimoPeriodoAcesso, con);
                }
            }
           con.commit();
           System.out.println("Excução do método 'corrigirValorContrato_E_PeriodoAcesso' realizado com sucesso ");
        }catch(Exception e){
            try {
                con.rollback();
            } catch (SQLException ex) {
                Logger.getLogger(CorrigirBancoWinner.class.getName()).log(Level.SEVERE, null, ex);
            }
           System.out.println("Erro ao corrigirValorContrato_E_PeriodoAcesso");
           e.printStackTrace();
       }finally{
            try {
                con.setAutoCommit(true);
            } catch (SQLException ex) {
                Logger.getLogger(CorrigirBancoWinner.class.getName()).log(Level.SEVERE, null, ex);
            }
       }


    }

    private static void corrigirContratoModalidade(Connection con){
          /*Corrigir os registros de ContratoModalidade que estão com valores zerados para 
           * os campos "valorModalidade"  e "valorFinalModalidade"
           *Obs.: Consultar todos os registros que tem somente uma modalidade por contrato, pois
           *      a importação insere somente uma modalidade para o plano.
           *
           */
        try{

          ContratoModalidade contratoModalidade = new ContratoModalidade(con);
          List<ContratoModalidadeVO> listaContratoModalidade;
          String sql = "select *  " + 
                       " from contratoModalidade " + 
                       " where contrato IN( " + 
                       "                 select contrato " + 
                       "                 from contratoModalidade  " + 
                       "                 group by contrato " + 
                       "                 having (count(*)=1))";
          listaContratoModalidade =   contratoModalidade.consultar(sql, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
          MovProdutoInterfaceFacade movProduto = new MovProduto(con);
          MovProdutoVO movProdutoVO;
          for (ContratoModalidadeVO obj: listaContratoModalidade){
              movProdutoVO = consultarMovProduto(movProduto, obj.getContrato());
              if (movProdutoVO != null)
                corrigirValoresContratoModalidade(obj.getCodigo(), movProdutoVO, con);

          }
         System.out.println("Excução do método 'corrigirContratoModalidade' realizado com sucesso ");
       }catch(Exception e){
           System.out.println("Erro ao corrigirContratoModalidade");
           e.printStackTrace();
       }
    }

    private static void corrigirValoresContratoModalidade(int codigoContratoModalidade, MovProdutoVO movProdutoVO, Connection con) throws Exception{
      String sql = "update contratoModalidade set valorModalidade = ?, valorFinalModalidade = ? where codigo =? "  ;
      PreparedStatement pst = con.prepareStatement(sql);
      pst.setDouble(1, movProdutoVO.getTotalFinal());
      pst.setDouble(2, movProdutoVO.getTotalFinal());
      pst.setInt(3, codigoContratoModalidade);
      pst.execute();
    
    }

    private static MovProdutoVO consultarMovProduto(MovProdutoInterfaceFacade movProduto, int contrato)throws Exception{
        MovProdutoVO movProdutoVO = null;
        // Consultar um movProduto do tipo "Plano".
        String sql = "select mov.*  " +
                     "  from movProduto  mov  " +
                     "   inner join produto prod on prod.codigo = mov.produto " +
                     "   where prod.tipoProduto = 'PM' and  contrato = " + contrato +
                     "   limit 1";
        List<MovProdutoVO> lista = movProduto.consultar(sql, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if ((lista != null) && lista.size() > 0)
            movProdutoVO = lista.get(0);
        return movProdutoVO;
        
    }

    private static void importarCodigoAcesso(Connection con){
        try{
            con.setAutoCommit(false);

            LeitorXML leitorXML = new LeitorXML();
            List<Element> listaCliente = leitorXML.lerXML("C:\\Users\\<USER>\\Desktop\\xmls\\ClientesWinner.xml");
            for (Element cliente: listaCliente){
                corrigirCodAcesso(cliente.getAttributeValue("nome"), cliente.getAttributeValue("codacesso"),cliente.getAttributeValue("cpf"), con);
            }

            con.commit();
            System.out.println("Excução do método 'importarCodigoAcesso' realizado com sucesso ");
        }catch (Exception e){
            try {
                con.rollback();
            } catch (SQLException ex) {
                Logger.getLogger(CorrigirBancoWinner.class.getName()).log(Level.SEVERE, null, ex);
            }
            System.out.println("Erro ao 'importarCodigoAcesso'");
            e.printStackTrace();
        }finally{
            try {
                con.setAutoCommit(true);
            } catch (SQLException ex) {
                Logger.getLogger(CorrigirBancoWinner.class.getName()).log(Level.SEVERE, null, ex);
            }
        }


    }

    private static boolean ultimoPeriodoAcessoEhBonus(Connection con, int codigoContrato) throws Exception{
        String sql = "select * " +
                    "from periodoacessocliente  " +
                    "where dataFinalAcesso  = (select max(dataFinalAcesso) from periodoacessocliente where contrato = " + codigoContrato + ") " +
                    "and contrato = " + codigoContrato;
        PreparedStatement pst = con.prepareStatement(sql);
        ResultSet resultDados = pst.executeQuery();
        return  ((resultDados.next()) && (resultDados.getString("TipoAcesso").equals("BO")));
    }

    private static void corrigirPeriodoAcessoBonus(Connection con){
        try{
            con.setAutoCommit(false);

            // Este select traz todos os contratos que tem mais de 2 registros lançados para periodoAcesso, e que possivelmente tem "Bonus" lançado com periodo errado.
            String sqlContrato = "select contrato " +
                                "from periodoAcessoCliente  "+
                                "group by contrato " +
                                "having (count(*) > 2)";
            PreparedStatement pst = con.prepareStatement(sqlContrato);
            ResultSet resultContratos = pst.executeQuery();
            // Verifica se o último periodoAceso é para Bonus, caso verdadeiro, então corrigir periodo de acesso.
            while (resultContratos.next()){
                if (ultimoPeriodoAcessoEhBonus(con,resultContratos.getInt("contrato"))){
                  Date dataUltimoPeriodoAcesso = retornarUltimaDataAcessoCliente(con,resultContratos.getInt("contrato"));
                  if (dataUltimoPeriodoAcesso != null){
                    Calendar dataIniAcesso = Calendario.getInstance();
                    dataIniAcesso.setTime(dataUltimoPeriodoAcesso);
                    dataIniAcesso.add(Calendar.DAY_OF_MONTH, 1);
                    String sqlAcessoCliente = "update periodoAcessoCliente set datainicioacesso =? where contrato = ? and tipoAcesso = 'BO'";
                    PreparedStatement pstAcessoCliente = con.prepareStatement(sqlAcessoCliente);
                    pstAcessoCliente.setDate(1, Uteis.getDataJDBC(dataIniAcesso.getTime()));
                    pstAcessoCliente.setInt(2, resultContratos.getInt("contrato"));
                    pstAcessoCliente.execute();

                  }else{
                      System.out.println("Não encontrado data do ultimo acesso diferente de bonus. contrato: " + resultContratos.getInt("contrato"));
                  }

                }else{
                    System.out.println("Ultimo Periodo Acesso do contrato não é bonus. Contrato: " + resultContratos.getInt("contrato") + "," );
                }

            }


            con.commit();
            System.out.println("Excução do método 'corrigirPeriodoAcessoBonus' realizado com sucesso ");

        }catch (Exception e){
            try {
                con.rollback();
            } catch (SQLException ex) {
                Logger.getLogger(CorrigirBancoWinner.class.getName()).log(Level.SEVERE, null, ex);
            }
            System.out.println("Erro ao 'corrigirPeriodoAcessoBonus'");
            e.printStackTrace();
        }finally{
            try {
                con.setAutoCommit(true);
            } catch (SQLException ex) {
                Logger.getLogger(CorrigirBancoWinner.class.getName()).log(Level.SEVERE, null, ex);
            }
        }


    }

    private static void alterarCodigoAcessoCliente(int codigoCliente, String codAcesso, Connection con)throws Exception{
       String sqlAterar = "update cliente set codacessoalternativo =? where codigo =?";
       PreparedStatement pstAlterar = con.prepareStatement(sqlAterar);
       pstAlterar.setString(1, codAcesso);
       pstAlterar.setInt(2, codigoCliente);
       pstAlterar.execute();
    }

    private static void corrigirCodAcesso(String nomeCliente, String codAcessoCliente, String cpf, Connection con) throws Exception{
        try{
           String sqlConsultar = "select cli.codigo as codigoCliente " +
                                 "from cliente  cli " +
                                 "inner join pessoa p on p.codigo = cli.pessoa " +
                                 "where upper(p.nome) = ? ";
           PreparedStatement pstConsultar = con.prepareStatement(sqlConsultar,ResultSet.TYPE_SCROLL_INSENSITIVE,ResultSet.CONCUR_READ_ONLY);
           pstConsultar.setString(1, nomeCliente.toUpperCase());
           ResultSet resultDados = pstConsultar.executeQuery();
           resultDados.last();
           if (resultDados.getRow() == 1){
               alterarCodigoAcessoCliente(resultDados.getInt("codigoCliente"),codAcessoCliente, con );

           }else if (resultDados.getRow() > 1){
               // Fazer nova pesquisa pelo CPF
               String cpfFormatado = "";
               if ((cpf != null) && (!cpf.trim().equals(""))){
                  cpfFormatado = Formatador.formatarString("###.###.###-##", cpf);
                  String sql = "select cli.codigo as codigoCliente " +
                               "from cliente  cli " +
                               "inner join pessoa p on p.codigo = cli.pessoa " +
                               "where upper(p.nome) = ? and cfp = ? ";
                  PreparedStatement pstConsultarCliente = con.prepareStatement(sql);
                  pstConsultarCliente.setString(1, nomeCliente.toUpperCase());
                  pstConsultarCliente.setString(2, cpfFormatado);
                  ResultSet resultDadosCli = pstConsultarCliente.executeQuery();
                  if (resultDadosCli.next()){
                     alterarCodigoAcessoCliente(resultDadosCli.getInt("codigoCliente"), codAcessoCliente, con);
                  }else{
                      System.out.println("Não foi encontrado nenhum cadastro para o cpf: '" +cpfFormatado + "'");
                  }

               }else{
                   System.out.println("Não foi encontrado nenhum cadastro para o nome: '" +nomeCliente + "'");
               }
           }else{
               System.out.println("Não foi encontrado nenhum cadastro para o nome '" +nomeCliente + "'");
           }

        }catch (Exception e){
            System.out.println("Erro ao alterar cliente: " + nomeCliente);
            //throw e;
        }
    }


}

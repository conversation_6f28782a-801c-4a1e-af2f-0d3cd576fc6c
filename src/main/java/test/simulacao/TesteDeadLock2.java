package test.simulacao;


import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
/**
 *
 * <AUTHOR>
 */
public class TesteDeadLock2 {

    public static void main(String... args) throws Exception {

        Connection con = new DAO().obterConexaoEspecifica("7922cb331835c254fbbdbe4ce5ae7ccd");
        con.setAutoCommit(false);
        SuperFacadeJDBC.executarConsulta("update cliente set uacodigo = uacodigo", con);
        Thread.sleep(120000);

    }
}

package test.simulacao;


import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
/**
 *
 * <AUTHOR>
 */
public class ThreadUpdate extends Thread {

    private Connection con;

    public ThreadUpdate() throws Exception {
        con = new DAO().obterConexaoEspecifica("7922cb331835c254fbbdbe4ce5ae7ccd");
    }



    @Override
    public void run() {
        try {
            System.out.println(this.getName() + " Iniciei ...");
            while (true) {
                con.setAutoCommit(false);
                SuperFacadeJDBC.executarConsulta("update cliente set uacodigo = uacodigo", con);
                Thread.sleep(200);
                con.commit();
            }


        } catch (Exception ex) {
            try {
                con.rollback();
            } catch (Exception ex1) {
                Logger.getLogger(ThreadUpdate.class.getName()).log(Level.SEVERE, null, ex1);
            }
            Logger.getLogger(ThreadUpdate.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            try {
                con.setAutoCommit(true);
            } catch (Exception ex) {
                Logger.getLogger(ThreadUpdate.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }
}

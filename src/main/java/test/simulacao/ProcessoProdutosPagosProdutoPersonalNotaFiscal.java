package test.simulacao;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.HashSet;
import java.util.Set;

public class ProcessoProdutosPagosProdutoPersonalNotaFiscal {
    public static void main(String[] args) throws IOException {
        try {
            Uteis.debug = true;

            Connection con = new Conexao("*************************************************************", "postgres", "pactodb").getConexao();
            Conexao.guardarConexaoForJ2SE(con);

            //Processo referente ao ticket PAY-3399
            processar(con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static Set<Integer> listaAjustar() {
        Set<Integer> clientesAjustar = new HashSet<>();
        clientesAjustar.add(123);
        clientesAjustar.add(12);
        clientesAjustar.add(1245);
        return clientesAjustar;
    }

    private static void processar(Connection con) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("mpp.codigo as movprodutoparcela, \n");
            sql.append("mpp.valorpago, \n");
            sql.append("mpp.recibopagamento, \n");
            sql.append("mov.codigo as movproduto, \n");
            sql.append("mov.contrato, \n");
            sql.append("mov.situacao, \n");
            sql.append("mov.totalfinal, \n");
            sql.append("mov.valorfaturado, \n");
            sql.append("mov.datalancamento, \n");
            sql.append("case  \n");
            sql.append("when coalesce(mpp.valorpago,0) > 0 then 'PRODUTOS_PAGOS' \n");
            sql.append("else 'UPDATE' end as operacao, \n");
            sql.append("('update movprodutoparcela set valorpago = '||mov.valorfaturado||' where codigo = '||mpp.codigo||';') as updatesql \n");
            sql.append("from movproduto mov \n");
            sql.append("left join movprodutoparcela mpp on mpp.movproduto = mov.codigo \n");
            sql.append("left join movpagamento mp on mp.recibopagamento = mpp.recibopagamento \n");
            sql.append("inner join produto pr on pr.codigo = mov.produto \n");
            sql.append("where pr.tipoproduto = 'TP' \n");
            sql.append("and (coalesce(mpp.valorpago,0) = 0 or (coalesce(mp.codigo,0) > 0 and coalesce(mp.produtospagos,'') = '')) \n");
            sql.append("order by mov.datalancamento desc  \n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                Integer recibo = 0;
                Integer movproduto = 0;
                try {
                    recibo = rs.getInt("recibopagamento");
                    movproduto = rs.getInt("movproduto");
                    log("Ajustar movproduto " + movproduto);
                    String operacao = rs.getString("operacao");
                    String update = rs.getString("updatesql");

                    if (operacao.equalsIgnoreCase("UPDATE") && !UteisValidacao.emptyString(update)) {
                        SuperFacadeJDBC.executarUpdate(update, con);
                    }

                    if (!UteisValidacao.emptyNumber(recibo)) {
                        ProdutosPagosServico.setarProdutosPagos(con, recibo);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    log("Erro movproduto " + movproduto + " | Erro: " + ex.getMessage());
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void log(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
    }
}

package negocio.comuns;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.CadastroDinamicoVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * Created by ulisses on 14/08/2015.
 */
public class CadastroDinamicoItemVO extends SuperVO {

    private Integer codigo;
    private CadastroDinamicoVO cadastroDinamicoVO;
    private String nomeCampo;
    private String labelCampo;
    private boolean mostrarCampo;
    private boolean campoObrigatorio;

    public CadastroDinamicoItemVO(){

    }

    public CadastroDinamicoItemVO(CadastroDinamicoVO cadastroDinamicoVO, String nomeCampo){
        this.cadastroDinamicoVO = cadastroDinamicoVO;
        this.nomeCampo = nomeCampo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public CadastroDinamicoVO getCadastroDinamicoVO() {
        return cadastroDinamicoVO;
    }

    public void setCadastroDinamicoVO(CadastroDinamicoVO cadastroDinamicoVO) {
        this.cadastroDinamicoVO = cadastroDinamicoVO;
    }

    public String getNomeCampo() {
        return nomeCampo;
    }

    public void setNomeCampo(String nomeCampo) {
        this.nomeCampo = nomeCampo;
    }

    public boolean isMostrarCampo() {
        return mostrarCampo;
    }

    public void setMostrarCampo(boolean mostrarCampo) {
        this.mostrarCampo = mostrarCampo;
    }

    public boolean isCampoObrigatorio() {
        return campoObrigatorio;
    }

    public void setCampoObrigatorio(boolean campoObrigatorio) {
        this.campoObrigatorio = campoObrigatorio;
    }

    public String getLabelCampo() {
        return labelCampo;
    }

    public void setLabelCampo(String labelCampo) {
        this.labelCampo = labelCampo;
    }

    public void validarDados() throws ConsistirException, Exception {
        if (!getValidarDados().booleanValue()) {
            return;
        }
        if ((getCadastroDinamicoVO() == null) || (getCadastroDinamicoVO().getCodigo() == null) ||
                (getCadastroDinamicoVO().getCodigo() <= 0)) {
            throw new ConsistirException("O campo nome cadastro dinamico (Cadastro Dinamico Item) deve ser informado.");
        }
        if ((getNomeCampo() == null) || (getNomeCampo().trim().equals(""))) {
            throw new ConsistirException("O campo nome campo (Cadastro Dinamico Item) deve ser informado.");
        }

    }

}

package negocio.comuns.plano;

import java.util.Date;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade CondicaoPagamentoParcela. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see CondicaoPagamento
 */
public class CondicaoPagamentoParcelaVO extends SuperVO {

    protected Integer codigo;
    protected Integer condicaoPagamento;
   // protected Double percentualParcela;
    protected Integer nrDiasParcela;
    protected Integer nrParcela;

    /**
     * Construtor padr<PERSON> da classe <code>CondicaoPagamentoParcela</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public CondicaoPagamentoParcelaVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>CondicaoPagamentoParcelaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(CondicaoPagamentoParcelaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
//        if (obj.getNrParcela().intValue() == 0) {
//            throw new ConsistirException("O campo NR. PARCELA (Parcelas Condicão de Pagamento) deve ser informado.");
//        }
//        if (obj.getPercentualParcela().doubleValue() == 0) {
//            throw new ConsistirException("O campo VALOR(%) (Parcelas Condicão de Pagamento) deve ser informado.");
//        }
//        if (obj.getNrDiasParcela().intValue() == 0 && obj.getNrParcela().intValue() > 1) {
//            throw new ConsistirException("O campo INTERVALO(DIAS) (Parcelas Condicão de Pagamento) deve ser informado.");
//        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
//        setPercentualParcela(new Double(0));
        setNrParcela(new Integer(0));
        setNrDiasParcela(new Integer(0));
    }

//    public Double getPercentualParcela() {
//        return (percentualParcela);
//    }
//
//    public void setPercentualParcela(Double percentualParcela) {
//        this.percentualParcela = percentualParcela;
//    }

    public Integer getCondicaoPagamento() {
        return (condicaoPagamento);
    }

    public void setCondicaoPagamento(Integer condicaoPagamento) {
        this.condicaoPagamento = condicaoPagamento;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getNrDiasParcela() {
        return nrDiasParcela;
    }

    public void setNrDiasParcela(Integer nrDiasParcela) {
        this.nrDiasParcela = nrDiasParcela;
    }

    public Integer getNrParcela() {
        return nrParcela;
    }

    public void setNrParcela(Integer nrParcela) {
        this.nrParcela = nrParcela;
    }
}

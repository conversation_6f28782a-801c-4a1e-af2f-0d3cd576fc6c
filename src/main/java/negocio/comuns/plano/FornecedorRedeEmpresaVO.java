package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import servicos.discovery.RedeDTO;

import java.util.Date;

public class FornecedorRedeEmpresaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer fornecedor;
    private String chave;
    private Date dataCadastro;
    private Date dataAtualizacao;
    private Integer fornecedorReplicado;
    private String nomeUnidade;
    private String mensagemSituacao;
    private RedeDTO redeDTO;
    private boolean selecionado;
    private Integer codigoEmpresaDestino;

    public FornecedorRedeEmpresaVO(Integer fornecedor, String chave, Integer fornecedorReplicado, Integer codigoEmpresaDestino) {
        this.fornecedor = fornecedor;
        this.chave = chave;
        this.fornecedorReplicado = fornecedorReplicado;
        this.codigoEmpresaDestino = codigoEmpresaDestino;
    }

    public FornecedorRedeEmpresaVO(String nomeUnidade, Integer fornecedor, String chave, Integer fornecedorReplicado) {
        this.nomeUnidade = nomeUnidade;
        this.fornecedor = fornecedor;
        this.chave = chave;
        this.fornecedorReplicado = fornecedorReplicado;
    }

    public FornecedorRedeEmpresaVO() {
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getFornecedor() {
        return fornecedor;
    }

    public void setFornecedor(Integer fornecedor) {
        this.fornecedor = fornecedor;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Boolean getDataAtualizacaoInformada() {
        return getDataAtualizacao() != null;
    }

    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }

    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }

    public Integer getFornecedorReplicado() {
        return fornecedorReplicado;
    }

    public void setFornecedorReplicado(Integer fornecedorReplicado) {
        this.fornecedorReplicado = fornecedorReplicado;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getMensagemSituacao() {
        return mensagemSituacao;
    }

    public void setMensagemSituacao(String mensagemSituacao) {
        this.mensagemSituacao = mensagemSituacao;
    }

    public RedeDTO getRedeDTO() {
        return redeDTO;
    }

    public void setRedeDTO(RedeDTO redeDTO) {
        this.redeDTO = redeDTO;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Integer getCodigoEmpresaDestino() {
        return codigoEmpresaDestino;
    }

    public void setCodigoEmpresaDestino(Integer codigoEmpresaDestino) {
        this.codigoEmpresaDestino = codigoEmpresaDestino;
    }
}

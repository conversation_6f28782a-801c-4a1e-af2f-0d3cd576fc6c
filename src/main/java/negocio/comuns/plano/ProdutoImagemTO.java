package negocio.comuns.plano;

import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

public class ProdutoImagemTO {

    private String fotoKey;
    private Integer ordem;

    public ProdutoImagemTO() {

    }

    public ProdutoImagemTO(JSONObject json) {
        this.fotoKey = json.optString("fotoKey");
        this.ordem = json.optInt("ordem");
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }
}

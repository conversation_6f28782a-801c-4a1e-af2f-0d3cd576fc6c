package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import org.apache.commons.lang.StringUtils;

/**
 * Reponsável por manter os dados da entidade TipoModalidade. Classe do tipo VO -
 * Value Object composta pelos atributos da entidade com visibilidade protegida
 * e os métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class TipoModalidadeVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String nome;
    protected Integer identificador;

    /**
     * Construtor padrão da classe <code>TipoModalidade</code>. Cria uma nova
     * instância desta entidade, inicializando automaticamente seus atributos
     * (Classe VO).
     */
    public TipoModalidadeVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>TipoModalidadeVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDadosIncluir(TipoModalidadeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (StringUtils.isBlank(obj.getNome())) {
            throw new ConsistirException("O campo NOME (TipoModalidade) deve ser informado.");
        }

        if (obj.getIdentificador() == null || obj.getIdentificador() == 0) {
            throw new ConsistirException("O campo IDENTIFICADOR (TipoModalidade) deve ser informado.");
        }
        if (!Uteis.isNumeroValido(String.valueOf(obj.getIdentificador()))) {
            throw new ConsistirException("O campo IDENTIFICADOR aceita somente numeros.");
        }
    }

    public static void validarDadosAlterar(TipoModalidadeVO obj) throws ConsistirException {
        validarDadosIncluir(obj);
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        setNome(getNome().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
    }


    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return (nome);
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getIdentificador() {
        return identificador;
    }

    public void setIdentificador(Integer identificador) {
        this.identificador = identificador;
    }
}

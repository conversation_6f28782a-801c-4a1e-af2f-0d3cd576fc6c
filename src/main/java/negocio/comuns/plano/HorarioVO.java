package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Reponsável por manter os dados da entidade Horario. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class HorarioVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected Boolean livre;
    @NaoControlarLogAlteracao
    protected Boolean domingo;
    @NaoControlarLogAlteracao
    protected Boolean segunda;
    @NaoControlarLogAlteracao
    protected Boolean terca;
    @NaoControlarLogAlteracao
    protected Boolean quarta;
    @NaoControlarLogAlteracao
    protected Boolean quinta;
    @NaoControlarLogAlteracao
    protected Boolean sexta;
    @NaoControlarLogAlteracao
    protected Boolean sabado;
    protected Boolean horarioDefault;

    protected Boolean horarioEscolhida;
    /** Atributo responsável por manter os objetos da classe <code>HorarioDisponibilidade</code>. */
    @NaoControlarLogAlteracao
    @ListJson(clazz = HorarioDisponibilidadeVO.class)
    private List<HorarioDisponibilidadeVO> horarioDisponibilidadeVOs;
     @NaoControlarLogAlteracao
    private List<HorarioDisponibilidadeVO> horarioDisponibilidadeVOsAntesAlteracao;
     @NaoControlarLogAlteracao
    private String correspondenciaZD;
     private Boolean ativo;

    /**
     * Construtor padrão da classe <code>Horario</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public HorarioVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>HorarioVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(HorarioVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Horário) deve ser informado.");
        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setDescricao("");
        setLivre(false);
        setDomingo(false);
        setSegunda(false);
        setTerca(false);
        setQuarta(false);
        setQuinta(false);
        setSexta(false);
        setSabado(false);
        setHorarioDefault(false);
        setHorarioEscolhida(false);
        setHorarioDisponibilidadeVOs(new ArrayList());
        setHorarioDisponibilidadeVOsAntesAlteracao(new ArrayList());
        setAtivo(true);
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>HorarioDisponibilidadeVO</code>
     * ao List <code>horarioDisponibilidadeVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>HorarioDisponibilidade</code> - getIdentificador() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>HorarioDisponibilidadeVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjHorarioDisponibilidadeVOs(HorarioDisponibilidadeVO obj) throws Exception {
        HorarioDisponibilidadeVO.validarDados(obj);
        int index = 0;
        Iterator i = getHorarioDisponibilidadeVOs().iterator();
        while (i.hasNext()) {
            HorarioDisponibilidadeVO objExistente = (HorarioDisponibilidadeVO) i.next();
            if (objExistente.getIdentificador().equals(obj.getIdentificador())) {
                getHorarioDisponibilidadeVOs().set(index, obj);
                return;
            }
            index++;
        }
        getHorarioDisponibilidadeVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>HorarioDisponibilidadeVO</code>
     * no List <code>horarioDisponibilidadeVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>HorarioDisponibilidade</code> - getIdentificador() - como identificador (key) do objeto no List.
     * @param identificador  Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjHorarioDisponibilidadeVOs(String identificador) throws Exception {
        int index = 0;
        Iterator i = getHorarioDisponibilidadeVOs().iterator();
        while (i.hasNext()) {
            HorarioDisponibilidadeVO objExistente = (HorarioDisponibilidadeVO) i.next();
            if (objExistente.getIdentificador().equals(identificador)) {
                getHorarioDisponibilidadeVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>HorarioDisponibilidadeVO</code>
     * no List <code>horarioDisponibilidadeVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>HorarioDisponibilidade</code> - getIdentificador() - como identificador (key) do objeto no List.
     * @param identificador  Parâmetro para localizar o objeto do List.
     */
    public HorarioDisponibilidadeVO consultarObjHorarioDisponibilidadeVO(String identificador) throws Exception {
        Iterator i = getHorarioDisponibilidadeVOs().iterator();
        while (i.hasNext()) {
            HorarioDisponibilidadeVO objExistente = (HorarioDisponibilidadeVO) i.next();
            if (objExistente.getIdentificador().equals(identificador)) {
                return objExistente;
            }
        }
        return null;
    }

    public static void verificarHorario(HorarioVO obj) throws Exception {
        int horarioPreenchido = 0;
        int horarioMarcado = 0;
        if (!obj.getLivre()) {
            Iterator i = obj.getHorarioDisponibilidadeVOs().iterator();
            while (i.hasNext()) {
                HorarioDisponibilidadeVO objExistente = (HorarioDisponibilidadeVO) i.next();
                if (objExistente.getHora0000()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0030()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0100()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0130()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0200()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0230()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0300()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0330()) {
                    horarioMarcado = 1;
                    horarioPreenchido = 1;
                }
                if (objExistente.getHora0400()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0430()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0500()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0530()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0600()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0630()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0700()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0730()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0800()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0830()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0900()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora0930()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1000()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1030()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1100()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1130()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1200()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1230()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1300()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1330()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1400()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1430()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1500()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1530()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1600()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1630()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1700()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1730()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1800()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1830()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1900()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora1930()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora2000()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora2030()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora2100()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora2130()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora2200()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora2230()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora2300()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (objExistente.getHora2330()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }

                if (objExistente.getHora2330()) {
                    horarioPreenchido = 1;
                    horarioMarcado = 1;
                }
                if (horarioMarcado == 1) {
                    if (objExistente.getIdentificador().equals("Domingo")) {
                        obj.setDomingo(true);
                    }
                    if (objExistente.getIdentificador().equals("Segunda")) {
                        obj.setSegunda(true);
                    }
                    if (objExistente.getIdentificador().equals("Terça")) {
                        obj.setTerca(true);

                    }
                    if (objExistente.getIdentificador().equals("Quarta")) {
                        obj.setQuarta(true);

                    }
                    if (objExistente.getIdentificador().equals("Quinta")) {
                        obj.setQuinta(true);

                    }
                    if (objExistente.getIdentificador().equals("Sexta")) {
                        obj.setSexta(true);

                    }
                    if (objExistente.getIdentificador().equals("Sábado")) {
                        obj.setSabado(true);

                    }
                    horarioMarcado = 0;
                }
            }
            if (horarioPreenchido == 0) {
                throw new Exception("A Marcação da grade do Horario (Horário) deve ser informado.");
            }
        }
    }

    /** Retorna Atributo responsável por manter os objetos da classe <code>HorarioDisponibilidade</code>. */
    public List<HorarioDisponibilidadeVO> getHorarioDisponibilidadeVOs() {
        return (horarioDisponibilidadeVOs);
    }

    /** Define Atributo responsável por manter os objetos da classe <code>HorarioDisponibilidade</code>. */
    public void setHorarioDisponibilidadeVOs(List<HorarioDisponibilidadeVO>  horarioDisponibilidadeVOs) {
        this.horarioDisponibilidadeVOs = horarioDisponibilidadeVOs;
    }

    public Boolean getHorarioDefault() {
        return (horarioDefault);
    }

    public Boolean isHorarioDefault() {
        return (horarioDefault);
    }

    public void setHorarioDefault(Boolean horarioDefault) {
        this.horarioDefault = horarioDefault;
    }

    public Boolean getLivre() {
        return (livre);
    }

    public Boolean isLivre() {
        return (livre);
    }

    public void setLivre(Boolean livre) {
        this.livre = livre;
    }



    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }



    public Boolean getDomingo() {
        return domingo;
    }

    public void setDomingo(Boolean domingo) {
        this.domingo = domingo;
    }

    public Boolean getQuarta() {
        return quarta;
    }

    public void setQuarta(Boolean quarta) {
        this.quarta = quarta;
    }

    public Boolean getQuinta() {
        return quinta;
    }

    public void setQuinta(Boolean quinta) {
        this.quinta = quinta;
    }

    public Boolean getSabado() {
        return sabado;
    }

    public void setSabado(Boolean sabado) {
        this.sabado = sabado;
    }

    public Boolean getSegunda() {
        return segunda;
    }

    public void setSegunda(Boolean segunda) {
        this.segunda = segunda;
    }

    public Boolean getSexta() {
        return sexta;
    }

    public void setSexta(Boolean sexta) {
        this.sexta = sexta;
    }

    public Boolean getTerca() {
        return terca;
    }

    public void setTerca(Boolean terca) {
        this.terca = terca;
    }


    public Boolean getHorarioEscolhida() {
        return horarioEscolhida;
    }

    public void setHorarioEscolhida(Boolean horarioEscolhida) {
        this.horarioEscolhida = horarioEscolhida;
    }

    public String toString(){
        return this.getDescricao();
    }

    public String getCorrespondenciaZD() {
        return correspondenciaZD;
    }

    public void setCorrespondenciaZD(String correspondenciaZD) {
        this.correspondenciaZD = correspondenciaZD;
    }
    
    public void registrarDisponibilidadesAntesAlteracao() throws Exception{
        horarioDisponibilidadeVOsAntesAlteracao = new ArrayList<HorarioDisponibilidadeVO>();
        for (HorarioDisponibilidadeVO disponibilidade : horarioDisponibilidadeVOs){
            if(!UteisValidacao.emptyString(disponibilidade.getIdentificador())){
                horarioDisponibilidadeVOsAntesAlteracao.add((HorarioDisponibilidadeVO) disponibilidade.getClone(true));
            }
        }
    }

    public List<HorarioDisponibilidadeVO> getHorarioDisponibilidadeVOsAntesAlteracao() {
        return horarioDisponibilidadeVOsAntesAlteracao;
    }

    public void setHorarioDisponibilidadeVOsAntesAlteracao(List<HorarioDisponibilidadeVO> horarioDisponibilidadeVOsAntesAlteracao) {
        this.horarioDisponibilidadeVOsAntesAlteracao = horarioDisponibilidadeVOsAntesAlteracao;
    }


    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}

package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class OrcamentoRelVO extends SuperVO {

    private Integer codigo;
    private String nomeCliente;
    private String modeloorcamento;
    private String nomeColaborador;
    private Date dataregistro;


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getModeloorcamento() {
        return modeloorcamento;
    }

    public void setModeloorcamento(String modeloorcamento) {
        this.modeloorcamento = modeloorcamento;
    }

    public String getNomeColaborador() {
        return nomeColaborador;
    }

    public void setNomeColaborador(String nomeColaborador) {
        this.nomeColaborador = nomeColaborador;
    }

    public Date getDataregistro() {
        return dataregistro;
    }

    public void setDataregistro(Date dataregistro) {
        this.dataregistro = dataregistro;
    }
}

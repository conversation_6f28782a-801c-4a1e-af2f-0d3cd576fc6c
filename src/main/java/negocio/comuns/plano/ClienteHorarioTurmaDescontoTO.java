package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;

public class ClienteHorarioTurmaDescontoTO extends SuperTO {
    private HorarioTurmaVO horarioTurma;
    private String aluno;
    private Integer contrato;
    private Date dataContrato;
    private Double percOcupacao;
    private Double percDesconto;

    public HorarioTurmaVO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaVO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public String getAluno() {
        return aluno;
    }

    public void setAluno(String aluno) {
        this.aluno = aluno;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Date getDataContrato() {
        return dataContrato;
    }

    public void setDataContrato(Date dataContrato) {
        this.dataContrato = dataContrato;
    }

    public Double getPercOcupacao() {
        return percOcupacao;
    }

    public void setPercOcupacao(Double percOcupacao) {
        this.percOcupacao = percOcupacao;
    }

    public Double getPercDesconto() {
        return percDesconto;
    }

    public void setPercDesconto(Double percDesconto) {
        this.percDesconto = percDesconto;
    }
}

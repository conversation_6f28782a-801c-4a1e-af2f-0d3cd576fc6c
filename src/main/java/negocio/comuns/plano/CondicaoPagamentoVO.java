package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import java.util.Iterator;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;

/**
 * Reponsável por manter os dados da entidade CondicaoPagamento. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class CondicaoPagamentoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected Integer nrParcelas;
    protected Boolean entrada;
    protected Double percentualValorEntrada;
    protected Integer intervaloEntreParcela;
    protected Boolean msgErroGerarParcela;
    protected Boolean condicaoPagamentoDefault;
    protected Boolean condicaoPagamentoEscolhida;
    private TipoConvenioCobrancaEnum tipoConvenioCobranca = TipoConvenioCobrancaEnum.NENHUM;
    private String tipoconvenio;
    /** Atributo responsável por manter os objetos da classe <code>CondicaoPagamentoParcela</code>. */
    @NaoControlarLogAlteracao
    @ListJson(clazz = CondicaoPagamentoParcelaVO.class)
    private List condicaoPagamentoParcelaVOs;
    private Boolean recebimentoPrePago;
    private boolean recorrencia = false;

    @NaoControlarLogAlteracao
    private List<CondicaoPagamentoPlanoTO> listaCondicaoPagamentoPlano = new ArrayList<CondicaoPagamentoPlanoTO>();

    @NaoControlarLogAlteracao
    private List<CondicaoPagamentoPlanoTO> listaCondicaoPagamentoPlanoAntesDeAlterar = new ArrayList<CondicaoPagamentoPlanoTO>();


    /**
     * Construtor padrão da classe <code>CondicaoPagamento</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public CondicaoPagamentoVO() {
        super();
        inicializarDados();
    }

    public String getConvenio_Apresentar() {
        return getTipoConvenioCobranca().getDescricao();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>CondicaoPagamentoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(CondicaoPagamentoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Condição de Pagamento) deve ser informado.");
        }
        if ((obj.getNrParcelas().intValue() == 0) && ((obj.getEntrada().equals(false)))) {
            throw new ConsistirException("O campo NÚMERO DE PARCELAS (Condição de Pagamento) deve ser informado.");
        }
//        if (obj.getEntrada().equals(true)) {
//            if (obj.getPercentualValorEntrada().intValue() == 0) {
//                throw new ConsistirException("O campo PERCENTUAL DE ENTRADA (Condição de Pagamento) deve ser informado.");
//            }
//        }
//        if (obj.getPercentualValorEntrada().intValue() > 100) {
//            throw new ConsistirException("O campo PERCENTUAL DE ENTRADA (Condição de Pagamento) não pode ser maior que 100%.");
//        }
        if(obj.getIntervaloEntreParcela() < 0){
            throw new ConsistirException("O campo Intervalo de Dias Entre as Parcelas (Condição de Pagamento) deve ser maior ou igual a zero.");
        }
        if (obj.getCondicaoPagamentoParcelaVOs().size() > 0) {
            if (obj.getNrParcelas().intValue() != obj.getCondicaoPagamentoParcelaVOs().size()) {
                throw new ConsistirException("O NÚMERO de PARCELA(S) é diferente da quantidade de PARCELA(S) GERADA(S).");
            }
            CondicaoPagamentoParcelaVO cpp = (CondicaoPagamentoParcelaVO) obj.getCondicaoPagamentoParcelaVOs().get(0);
            if (obj.getIntervaloEntreParcela().intValue() != cpp.getNrDiasParcela().intValue() && !obj.getEntrada()) {
                throw new ConsistirException("O NÚMERO de INTERVALOR ENTRE PARCELA(S) é diferente do NÚMERO de de DIA da PARCELA(S) GERADA(S).");
            } else if (obj.getCondicaoPagamentoParcelaVOs().size() > 1) {
                CondicaoPagamentoParcelaVO cpp1 = (CondicaoPagamentoParcelaVO) obj.getCondicaoPagamentoParcelaVOs().get(1);
                if (obj.getIntervaloEntreParcela().intValue() != cpp1.getNrDiasParcela().intValue() && obj.getEntrada()) {
                    throw new ConsistirException("O NÚMERO de INTERVALOR ENTRE PARCELA(S) é diferente do NÚMERO de de DIA da PARCELA(S) GERADA(S).");
                }
            }
        }
    }

    public static void validarDadosAdiconar(CondicaoPagamentoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Condição de Pagamento) deve ser informado.");
        }
        if ((obj.getNrParcelas().intValue() == 0) && ((obj.getEntrada().equals(false)))) {
            throw new ConsistirException("É necessário que tenha no mínimo um NÚMERO DE PARCELA ou uma ENTRADA.");
        }
        if(obj.getIntervaloEntreParcela() < 0){
            throw new ConsistirException("O campo Intervalo de Dias Entre as Parcelas (Condição de Pagamento) deve ser maior ou igual a zero.");
        }
        if(obj.getNrParcelas() < 1){
            throw new ConsistirException("O campo Número de Parcela (Condição de Pagamento) deve ser maior que zero.");
        }
//        if ((obj.getIntervaloEntreParcela().intValue() == 0) && (obj.getNrParcelas().intValue() > 0)) {
//            throw new ConsistirException("O campo INTERVALO ENTRE PARCELAS (Condição de Pagamento) deve ser informado.");
//        }
//        if (obj.getEntrada().equals(true)) {
//            if (obj.getPercentualValorEntrada().intValue() == 0) {
//                throw new ConsistirException("O campo PERCENTUAL DE ENTRADA (Condição de Pagamento) deve ser informado.");
//            }
//        }
        if (obj.getPercentualValorEntrada().intValue() > 100) {
            throw new ConsistirException("O campo PERCENTUAL DE ENTRADA (Condição de Pagamento) não pode ser maior que 100%.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setDescricao("");
        setNrParcelas(new Integer(0));
        setEntrada(new Boolean(false));
        setCondicaoPagamentoDefault(new Boolean(false));
        setCondicaoPagamentoEscolhida(new Boolean(false));
        setMsgErroGerarParcela(new Boolean(false));
        setPercentualValorEntrada(new Double(0));
        setCondicaoPagamentoParcelaVOs(new ArrayList());
        setIntervaloEntreParcela(new Integer(0));
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>CondicaoPagamentoParcelaVO</code>
     * ao List <code>condicaoPagamentoParcelaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>CondicaoPagamentoParcela</code> - getDataVencimento() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>CondicaoPagamentoParcelaVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjCondicaoPagamentoParcelaVOs(CondicaoPagamentoParcelaVO obj) throws Exception {
        CondicaoPagamentoParcelaVO.validarDados(obj);
        int index = 0;
        Iterator i = getCondicaoPagamentoParcelaVOs().iterator();
        while (i.hasNext()) {
            CondicaoPagamentoParcelaVO objExistente = (CondicaoPagamentoParcelaVO) i.next();
            if (objExistente.getNrParcela().equals(obj.getNrParcela())) {
                getCondicaoPagamentoParcelaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getCondicaoPagamentoParcelaVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>CondicaoPagamentoParcelaVO</code>
     * no List <code>condicaoPagamentoParcelaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>CondicaoPagamentoParcela</code> - getDataVencimento() - como identificador (key) do objeto no List.
     * @param dataVencimento  Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjCondicaoPagamentoParcelaVOs(CondicaoPagamentoParcelaVO obj) throws Exception {
        int index = 0;
        Iterator i = getCondicaoPagamentoParcelaVOs().iterator();
        while (i.hasNext()) {
            CondicaoPagamentoParcelaVO objExistente = (CondicaoPagamentoParcelaVO) i.next();
            if (objExistente.getNrParcela().equals(obj.getNrParcela())) {
                getCondicaoPagamentoParcelaVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>CondicaoPagamentoParcelaVO</code>
     * no List <code>condicaoPagamentoParcelaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>CondicaoPagamentoParcela</code> - getDataVencimento() - como identificador (key) do objeto no List.
     * @param dataVencimento  Parâmetro para localizar o objeto do List.
     */
    public CondicaoPagamentoParcelaVO consultarObjCondicaoPagamentoParcelaVO(CondicaoPagamentoParcelaVO obj) throws Exception {
        Iterator i = getCondicaoPagamentoParcelaVOs().iterator();
        while (i.hasNext()) {
            CondicaoPagamentoParcelaVO objExistente = (CondicaoPagamentoParcelaVO) i.next();
            if (objExistente.getNrParcela().equals(obj.getNrParcela())) {
                return objExistente;
            }
        }
        return null;
    }

    /** Retorna Atributo responsável por manter os objetos da classe <code>CondicaoPagamentoParcela</code>. */
    public List getCondicaoPagamentoParcelaVOs() {
        return (condicaoPagamentoParcelaVOs);
    }

    /** Define Atributo responsável por manter os objetos da classe <code>CondicaoPagamentoParcela</code>. */
    public void setCondicaoPagamentoParcelaVOs(List condicaoPagamentoParcelaVOs) {
        this.condicaoPagamentoParcelaVOs = condicaoPagamentoParcelaVOs;
    }

    public Double getPercentualValorEntrada() {
        return (percentualValorEntrada);
    }

    public void setPercentualValorEntrada(Double percentualValorEntrada) {
        this.percentualValorEntrada = percentualValorEntrada;
    }

    public Boolean getEntrada() {
        return (entrada);
    }

    public Boolean isEntrada() {
        return (entrada);
    }

    public void setEntrada(Boolean entrada) {
        this.entrada = entrada;
    }

    public Integer getNrParcelas() {
        return (nrParcelas);
    }

    public void setNrParcelas(Integer nrParcelas) {
        this.nrParcelas = nrParcelas;
    }

    public String getDescricao() {
        return (descricao);
    }
    public String getDescricaoMinusculo(){
        return (descricao).toLowerCase();
    }

    public void setDescricao(String descricao) {
        if (descricao == null) {
            descricao = "";
        }
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;

    }

    public Integer getIntervaloEntreParcela() {
        return intervaloEntreParcela;
    }

    public void setIntervaloEntreParcela(Integer intervaloEntreParcela) {
        this.intervaloEntreParcela = intervaloEntreParcela;
    }

    public Boolean getMsgErroGerarParcela() {
        return msgErroGerarParcela;
    }

    public void setMsgErroGerarParcela(Boolean msgErroGerarParcela) {
        this.msgErroGerarParcela = msgErroGerarParcela;
    }

    public Boolean getCondicaoPagamentoDefault() {
        return condicaoPagamentoDefault;
    }

    public void setCondicaoPagamentoDefault(Boolean condicaoPagamentoDefault) {
        this.condicaoPagamentoDefault = condicaoPagamentoDefault;
    }

    public Boolean getCondicaoPagamentoEscolhida() {
        return condicaoPagamentoEscolhida;
    }

    public void setCondicaoPagamentoEscolhida(Boolean condicaoPagamentoEscolhida) {
        this.condicaoPagamentoEscolhida = condicaoPagamentoEscolhida;
    }

    public TipoConvenioCobrancaEnum getTipoConvenioCobranca() {
        return tipoConvenioCobranca;
    }

    public void setTipoConvenioCobranca(TipoConvenioCobrancaEnum tipoConvenioCobranca) {
        this.tipoConvenioCobranca = tipoConvenioCobranca;
    }

    public String getTipoconvenio() {
        return tipoconvenio;
    }

    public void setTipoconvenio(String tipoconvenio) {
        this.tipoconvenio = tipoconvenio;
    }


    public Boolean getRecebimentoPrePago() {
        if (recebimentoPrePago == null){
            recebimentoPrePago = false;
        }
        return recebimentoPrePago;
    }

    public void setRecebimentoPrePago(Boolean recebimentoPrePago) {
        this.recebimentoPrePago = recebimentoPrePago;
    }

    public List<CondicaoPagamentoPlanoTO> getListaCondicaoPagamentoPlano() {
        return listaCondicaoPagamentoPlano;
    }

    public void setListaCondicaoPagamentoPlano(List<CondicaoPagamentoPlanoTO> listaCondicaoPagamentoPlano) {
        this.listaCondicaoPagamentoPlano = listaCondicaoPagamentoPlano;
    }

    public List<CondicaoPagamentoPlanoTO> getListaCondicaoPagamentoPlanoAntesDeAlterar() {
        return listaCondicaoPagamentoPlanoAntesDeAlterar;
    }

    public void setListaCondicaoPagamentoPlanoAntesDeAlterar(List<CondicaoPagamentoPlanoTO> listaCondicaoPagamentoPlanoAntesDeAlterar) {
        this.listaCondicaoPagamentoPlanoAntesDeAlterar = listaCondicaoPagamentoPlanoAntesDeAlterar;
    }

    public boolean isRecorrencia() {
        return recorrencia;
    }

    public void setRecorrencia(boolean recorrencia) {
        this.recorrencia = recorrencia;
    }
}

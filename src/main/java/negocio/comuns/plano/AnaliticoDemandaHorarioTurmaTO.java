package negocio.comuns.plano;

import org.json.JSONObject;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

public class AnaliticoDemandaHorarioTurmaTO {
    private Date data;
    private String nome;
    private String aula;
    private String turma;
    private String ambiente;
    private String horario;

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getAula() {
        return aula;
    }

    public void setAula(String aula) {
        this.aula = aula;
    }

    public String getTurma() {
        return turma;
    }

    public void setTurma(String turma) {
        this.turma = turma;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public JSONObject toJSON() throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", Calendario.getData(getData(), "dd/MM/yyyy"));
        jsonObject.put("nome", getNome());
        jsonObject.put("aula", getAula());
        jsonObject.put("turma", getTurma());
        jsonObject.put("ambiente", getAmbiente());
        jsonObject.put("horario", getHorario());
        return jsonObject;
    }
}

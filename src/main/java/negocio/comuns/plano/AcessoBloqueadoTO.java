package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;

public class AcessoBloqueadoTO extends SuperVO {

    private Integer codigoCliente;
    private String matriculaAluno;
    private String nomeAluno;
    private String meioIdentificacao;
    private String motivoBloqueio;
    private String dataEntrada;

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatriculaAluno() {
        return matriculaAluno;
    }

    public void setMatriculaAluno(String matriculaAluno) {
        this.matriculaAluno = matriculaAluno;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getMeioIdentificacao() {
        return meioIdentificacao;
    }

    public void setMeioIdentificacao(String meioIdentificacao) {
        this.meioIdentificacao = meioIdentificacao;
    }

    public String getMotivoBloqueio() {
        return motivoBloqueio;
    }

    public void setMotivoBloqueio(String motivoBloqueio) {
        this.motivoBloqueio = motivoBloqueio;
    }

    public String getDataEntrada() {
        return dataEntrada;
    }

    public void setDataEntrada(String dataEntrada) {
        this.dataEntrada = dataEntrada;
    }
}

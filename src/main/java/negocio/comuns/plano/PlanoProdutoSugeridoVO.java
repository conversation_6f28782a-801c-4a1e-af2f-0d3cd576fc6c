/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class PlanoProdutoSugeridoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer plano;
    protected Boolean obrigatorio;
    @NaoControlarLogAlteracao
    protected Boolean produtoSugeridoEscolhida;
    @NaoControlarLogAlteracao
    protected Boolean apresentaProdutoMaRenoRema;
    private boolean editarDesconto;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Produto </code>.*/
    @ChaveEstrangeira
    @FKJson
    protected ProdutoVO produto;
    protected Double valorProduto;
    @NaoControlarLogAlteracao
    protected Date dataVenda;
    @NaoControlarLogAlteracao
    protected Date dataValidade;
    
    private Integer quantidade = 1;
    private String numeroCupomDesconto;
    private Double valorDescontoCupom;
    private Map<Integer,Integer> produtoMultiplicadorCampanha = new HashMap<Integer,Integer>();
    private Boolean ativoPlano = true;

    /**
     * Construtor padrão da classe <code>ProdutoSugerido</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public PlanoProdutoSugeridoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ProdutoSugeridoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(PlanoProdutoSugeridoVO obj) throws ConsistirException {

        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getProduto() == null) ||
                (obj.getProduto().getCodigo() == 0)) {
            throw new ConsistirException("O campo PRODUTO (Produto Sugerido) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setObrigatorio(false);
        setProdutoSugeridoEscolhida(false);
        setApresentaProdutoMaRenoRema(true);
        setProduto(new ProdutoVO());
        setPlano(0);
        setValorProduto(0.0);
        setEditarDesconto(false);
    }

    /**
     * Retorna o objeto da classe <code>Produto</code> relacionado com (<code>ProdutoSugerido</code>).
     */
    public ProdutoVO getProduto() {
        if (produto == null) {
            produto = new ProdutoVO();
        }
        return (produto);
    }

    /**
     * Define o objeto da classe <code>Produto</code> relacionado com (<code>ProdutoSugerido</code>).
     */
    public void setProduto(ProdutoVO obj) {
        this.produto = obj;
    }

    public String getObrigatorio_Apresentar() {
        if (obrigatorio) {
            return "Sim";
        }
        if (!obrigatorio) {
            return "Não";
        }
        return ("");
    }

    public Boolean getObrigatorio() {
        return (obrigatorio);
    }

    public Boolean isObrigatorio() {
        return (obrigatorio);
    }

    public void setObrigatorio(Boolean Obrigatorio) {
        this.obrigatorio = Obrigatorio;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getProdutoSugeridoEscolhida() {
        return produtoSugeridoEscolhida;
    }

    public void setProdutoSugeridoEscolhida(Boolean produtoSugeridoEscolhida) {
        this.produtoSugeridoEscolhida = produtoSugeridoEscolhida;
    }

    public Double getValorProduto() {
        return valorProduto;
    }
    
    public Double getValorProdutoQtd() {
        return valorProduto*quantidade;
    }
    
    public Double getValorProdutoQtdDesconto() {
        try {
            if(getProduto().getDesconto().getTipoDesconto() != null 
                    && getProduto().getDesconto().getTipoDesconto().equals(TipoDesconto.PE)){
                return (valorProduto*quantidade)-((valorProduto*quantidade)*getProduto().getDesconto().getValor()/100);
            }
            return Uteis.arredondarForcando2CasasDecimais((valorProduto*quantidade)-getProduto().getDesconto().getValor());
        } catch (Exception e) {
            return 0.0;
        }
    }
    
    public Double getValorDesconto() {
        try {
            if(getProduto().getDesconto().getTipoDesconto() != null 
                    && getProduto().getDesconto().getTipoDesconto().equals(TipoDesconto.PE)){
                return ((valorProduto*quantidade)*getProduto().getDesconto().getValor()/100);
            }
            return getProduto().getDesconto().getValor();
        } catch (Exception e) {
            return 0.0;
        }
    }
    public Double getValorRealDesconto(){
        if(getProduto().getDesconto().getTipoDesconto() != null
                && getProduto().getDesconto().getTipoDesconto().equals(TipoDesconto.PE)){
            return ((valorProduto/100)*getProduto().getDesconto().getValor());
        }
        return getProduto().getDesconto().getValor();
    }
    public void setValorRealDesconto(Double valorRealDesconto){
       getProduto().getDesconto().setValor(valorRealDesconto);
    }
    public void setValorProduto(Double valorProduto) {
        this.valorProduto = valorProduto;
    }

    public Boolean getApresentaProdutoMaRenoRema() {
        return apresentaProdutoMaRenoRema;
    }

    public void setApresentaProdutoMaRenoRema(Boolean apresentaProdutoMaRenoRema) {
        this.apresentaProdutoMaRenoRema = apresentaProdutoMaRenoRema;
    }

    public Date getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(Date dataValidade) {
        this.dataValidade = dataValidade;
    }

    public Date getDataVenda() {
        return dataVenda;
    }

    public void setDataVenda(Date dataVenda) {
        this.dataVenda = dataVenda;
    }

    public boolean isMostrarDesconto() {
        return !editarDesconto;
    }

    public boolean isEditarDesconto() {
        return editarDesconto;
    }

    public void setEditarDesconto(boolean editarDesconto) {
        this.editarDesconto = editarDesconto;
    }

    public boolean isDesabilitarObrigatoriedade() {
        return this.getProduto().getTipoProduto().equals(TipoProduto.MATRICULA.getCodigo()) ||
                this.getProduto().getTipoProduto().equals(TipoProduto.REMATRICULA.getCodigo()) ||
                this.getProduto().getTipoProduto().equals(TipoProduto.RENOVACAO.getCodigo()) ||
                this.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo());
    }
    public Double getValorTotalFinal(){
        return (getValorProduto() * getQuantidade()) -  getValorRealDesconto();
    }
    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getNumeroCupomDesconto() {
        return numeroCupomDesconto;
    }

    public void setNumeroCupomDesconto(String numeroCupomDesconto) {
        this.numeroCupomDesconto = numeroCupomDesconto;
    }

    public PlanoProdutoWS toWS() {
        PlanoProdutoWS produtoWS = new PlanoProdutoWS();
        produtoWS.setCodigoProduto(getProduto().getCodigo());
        produtoWS.setProduto(getProduto().getDescricao());
        produtoWS.setTipoProduto(getProduto().getTipoProduto_Apresentar());
        produtoWS.setValor(Uteis.arredondarForcando2CasasDecimais(getValorTotalFinal()));
        return produtoWS;
    }

    public Double getValorDescontoCupom() {
        if (valorDescontoCupom == null) {
            valorDescontoCupom = 0.0;
        }
        return valorDescontoCupom;
    }

    public void setValorDescontoCupom(Double valorDescontoCupom) {
        this.valorDescontoCupom = valorDescontoCupom;
    }

    public Map<Integer, Integer> getProdutoMultiplicadorCampanha() {
        return produtoMultiplicadorCampanha;
    }

    public void setProdutoMultiplicadorCampanha(Map<Integer, Integer> produtoMultiplicadorCampanha) {
        this.produtoMultiplicadorCampanha = produtoMultiplicadorCampanha;
    }

    public Integer getObterMultiplicadorCampanhaProduto(){

        if ( produtoMultiplicadorCampanha.containsKey(getProduto().getCodigo())) {
            return produtoMultiplicadorCampanha.get(getProduto().getCodigo());
        }

        return 0;
    }

    public Boolean getAtivoPlano() {
        if (ativoPlano == null){
            ativoPlano = false;
        }
        return ativoPlano;
    }

    public void setAtivoPlano(Boolean ativoPlano) {
        this.ativoPlano = ativoPlano;
    }
}

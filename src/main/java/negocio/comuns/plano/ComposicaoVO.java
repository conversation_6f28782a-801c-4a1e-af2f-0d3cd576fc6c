package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.facade.jdbc.plano.Plano;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade Composicao. Classe do tipo VO -
 * Value Object composta pelos atributos da entidade com visibilidade protegida
 * e os métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class ComposicaoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected Double precoComposicao = 0.0;
    protected Boolean composicaoAdicional;
    protected Boolean composicaoDefault;
    private boolean modalidadesEspecificas;
    private int qtdeModalidades;
    @NaoControlarLogAlteracao
    protected Boolean composicaoEscolhida;
    /**
     * Atributo responsável por manter os objetos da classe
     * <code>ComposicaoModalidade</code>.
     */
    @NaoControlarLogAlteracao
    @ListJson(clazz = ComposicaoModalidadeVO.class)
    private List<ComposicaoModalidadeVO> composicaoModalidadeVOs;
    @NaoControlarLogAlteracao
    private List<ComposicaoModalidadeVO> composicaoModalidadeVOsAntesAlteracao;
    @NaoControlarLogAlteracao
    @FKJson
    private EmpresaVO empresa;
    private boolean composicaoPodeSerEscolhida; // variavel usada na tela de negociacao do contrato

    /**
     * Construtor padrão da classe <code>Composicao</code>. Cria uma nova
     * instância desta entidade, inicializando automaticamente seus atributos
     * (Classe VO).
     */
    public ComposicaoVO() {
        super();
        inicializarDados();
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ComposicaoVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @exception ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDados(ComposicaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getEmpresa() == null || obj.getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("O campo EMPRESA (Composição) deve ser informado.");
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Composição) deve ser informado.");
        }
        if (obj.getComposicaoModalidadeVOs().isEmpty()) {
            throw new ConsistirException("Modalidades não encontradas. Informe pelo menos uma para o pacote.");
        }
        if (!obj.isModalidadesEspecificas()) {
            if (obj.getQtdeModalidades() <= 0) {
                throw new ConsistirException("Informe uma quantidade de modalidades válida para este pacote.");
            }
            if (obj.getPrecoComposicao() <= 0) {
                throw new ConsistirException("Informe um valor válido para este pacote.");
            }
        }
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    public void inicializarDados() {
        setCodigo(new Integer(0));
        setDescricao("");
        setPrecoComposicao(0.0);
        setComposicaoAdicional(false);
        setComposicaoDefault(false);
        setComposicaoEscolhida(false);
        setComposicaoModalidadeVOs(new ArrayList<ComposicaoModalidadeVO>());
        setComposicaoModalidadeVOsAntesAlteracao(new ArrayList<ComposicaoModalidadeVO>());        
        setEmpresa(new EmpresaVO());
        setModalidadesEspecificas(true);
        setQtdeModalidades(0);
        setComposicaoPodeSerEscolhida(true);
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ComposicaoModalidadeVO</code> ao List
     * <code>composicaoModalidadeVOs</code>. Utiliza o atributo padrão de
     * consulta da classe <code>ComposicaoModalidade</code> -
     * getModalidade().getCodigo() - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ComposicaoModalidadeVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjComposicaoModalidadeVOs(ComposicaoModalidadeVO obj) throws Exception {
        ComposicaoModalidadeVO.validarDados(obj, isModalidadesEspecificas());     
        int index = 0;
        for (Object o : getComposicaoModalidadeVOs()) {
            ComposicaoModalidadeVO objExistente = (ComposicaoModalidadeVO) o;
            if (objExistente.getModalidade().getCodigo().equals(obj.getModalidade().getCodigo())
                    && objExistente.getNrVezes().equals(obj.getNrVezes())) {
                getComposicaoModalidadeVOs().set(index, obj);
                return;
            }
            index++;
        }
        getComposicaoModalidadeVOs().add(obj);
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>ComposicaoModalidadeVO</code> no List
     * <code>composicaoModalidadeVOs</code>. Utiliza o atributo padrão de
     * consulta da classe <code>ComposicaoModalidade</code> -
     * getModalidade().getCodigo() - como identificador (key) do objeto no List.
     *
     * @param modalidade Parâmetro para localizar o objeto do List.
     */
    public ComposicaoModalidadeVO consultarObjComposicaoModalidadeVO(Integer modalidade) throws Exception {
        Iterator i = getComposicaoModalidadeVOs().iterator();
        while (i.hasNext()) {
            ComposicaoModalidadeVO objExistente = (ComposicaoModalidadeVO) i.next();
            if (objExistente.getModalidade().getCodigo().equals(modalidade)) {
                return objExistente;
            }
        }
        return null;
    }

    public void processarPlanoQueUtilizaEssaComposicao(List lista) throws Exception {
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            PlanoVO plano = (PlanoVO) i.next();
            obterComposicaoModalidades(plano);
        }
    }

    public void obterComposicaoModalidades(PlanoVO plano) throws Exception {
        for (ComposicaoModalidadeVO cm : getComposicaoModalidadeVOs()) {
            verificarComposicaoModalidadeIgualPlanoModalidade(cm, plano);
        }
    }

    public void verificarComposicaoModalidadeIgualPlanoModalidade(ComposicaoModalidadeVO cm, PlanoVO plano) throws Exception {
        final NumberFormat nf = NumberFormat.getInstance();
        nf.setMinimumFractionDigits(2);
        boolean modalidadeExisteNessePlano = false;
        Iterator i = plano.getPlanoModalidadeVOs().iterator();
        while (i.hasNext()) {
            PlanoModalidadeVO pm = (PlanoModalidadeVO) i.next();
            if (cm.getModalidade().getCodigo().equals(pm.getModalidade().getCodigo())) {
                modalidadeExisteNessePlano = true;
            }
        }
        if (!modalidadeExisteNessePlano) {
            PlanoModalidadeVO obj = new PlanoModalidadeVO();
            PlanoModalidadeVezesSemanaVO pmvs = new PlanoModalidadeVezesSemanaVO();
            pmvs.setNrVezes(cm.getModalidade().getNrVezes());
            obj.setModalidade(cm.getModalidade());
            obj.setPlano(plano.getCodigo());
            obj.setListaVezesSemana(" " + pmvs.getNrVezes().toString() + " Vezes - R$ " + nf.format(Double.valueOf(cm.getModalidade().getValorMensal().toString())) + ", ");
            obj.getPlanoModalidadeVezesSemanaVOs().add(pmvs);
            plano.getPlanoModalidadeVOs().add(obj);
            new Plano().alterarSemCommit(plano);
        }

    }

    public List<ComposicaoModalidadeVO> getComposicaoModalidadeVOs() {
        return composicaoModalidadeVOs;
    }

    public void setComposicaoModalidadeVOs(List<ComposicaoModalidadeVO> composicaoModalidadeVOs) {
        this.composicaoModalidadeVOs = composicaoModalidadeVOs;
    }

    public Boolean getComposicaoDefault() {
        return (composicaoDefault);
    }

    public Boolean isComposicaoDefault() {
        return (composicaoDefault);
    }

    public void setComposicaoDefault(Boolean composicaoDefault) {
        this.composicaoDefault = composicaoDefault;
    }

    public Boolean getComposicaoAdicional() {
        return (composicaoAdicional);
    }

    public Boolean isComposicaoAdicional() {
        return (composicaoAdicional);
    }

    public void setComposicaoAdicional(Boolean composicaoAdicional) {
        this.composicaoAdicional = composicaoAdicional;
    }

    public Double getPrecoComposicao() {
        return (precoComposicao);
    }

    public String getPrecoComposicao_Apresentar() {
        return Formatador.formatarValorMonetario(precoComposicao);
    }

    public void setPrecoComposicao(Double precoComposicao) {
        this.precoComposicao = precoComposicao;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Boolean getComposicaoEscolhida() {
        return composicaoEscolhida;
    }

    public void setComposicaoEscolhida(Boolean composicaoEscolhida) {
        this.composicaoEscolhida = composicaoEscolhida;
    }

    public Boolean getComposicaoNova() {
        return this.codigo != 0;
    }

    public boolean isModalidadesEspecificas() {
        return modalidadesEspecificas;
    }

    public void setModalidadesEspecificas(boolean modalidadesEspecificas) {
        this.modalidadesEspecificas = modalidadesEspecificas;
    }

    public int getQtdeModalidades() {
        return qtdeModalidades;
    }

    public void setQtdeModalidades(int qtdeModalidades) {
        this.qtdeModalidades = qtdeModalidades;
    }

    public boolean isComposicaoPodeSerEscolhida() {
        return composicaoPodeSerEscolhida;
    }

    public void setComposicaoPodeSerEscolhida(boolean composicaoPodeSerEscolhida) {
        this.composicaoPodeSerEscolhida = composicaoPodeSerEscolhida;
    }

    public List<ComposicaoModalidadeVO> getComposicaoModalidadeVOsAntesAlteracao() {
        return composicaoModalidadeVOsAntesAlteracao;
    }

    public void setComposicaoModalidadeVOsAntesAlteracao(List<ComposicaoModalidadeVO> composicaoModalidadeVOsAntesAlteracao) {
        this.composicaoModalidadeVOsAntesAlteracao = composicaoModalidadeVOsAntesAlteracao;
    }

    public void registrarModalidadesAntesDaAlteracao() throws Exception {
        composicaoModalidadeVOsAntesAlteracao = new ArrayList<ComposicaoModalidadeVO>();
        for(ComposicaoModalidadeVO modalidade: composicaoModalidadeVOs){
            ComposicaoModalidadeVO existente = (ComposicaoModalidadeVO) modalidade.getClone(true);
            existente.setModalidade((ModalidadeVO) modalidade.getModalidade().getClone(true));
            composicaoModalidadeVOsAntesAlteracao.add(existente);
        }
    }
}

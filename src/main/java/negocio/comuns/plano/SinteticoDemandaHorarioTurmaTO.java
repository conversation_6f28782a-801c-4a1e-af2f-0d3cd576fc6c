package negocio.comuns.plano;

import org.json.JSONObject;

public class SinteticoDemandaHorarioTurmaTO {
    private int demanda;
    private String detalhes;
    private String horarioTurma;

    public void setDemanda(int demanda) {
        this.demanda = demanda;
    }

    public void setDetalhes(String detalhes) {
        this.detalhes = detalhes;
    }

    public void setHorarioTurma(String horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public int getDemanda() {
        return demanda;
    }

    public String getDetalhes() {
        return detalhes;
    }

    public String getHorarioTurma() {
        return horarioTurma;
    }

    public JSONObject toJSON() throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("horarioTurma", getHorarioTurma());
        jsonObject.put("demanda", getDemanda());
        jsonObject.put("detalhes", getDetalhes());
        return jsonObject;
    }
}

package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.TipoAntecedenciaMarcarAulaEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import com.sun.istack.NotNull;
import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.AgendaTotalTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.basico.CampanhaDuracao;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade Turma. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class TurmaVO extends SuperVO implements Cloneable {

    protected Integer codigo;
    protected String identificador;
    protected String descricao;
    protected Date dataInicialVigencia;
    protected Date dataFinalVigencia;
    protected Integer idadeMinima;
    protected Integer idadeMaxima;
    private Integer idadeMinimaMeses;
    private Integer idadeMaximaMeses;
    protected Boolean bloquearMatriculasAcimaLimite;
    @NaoControlarLogAlteracao
    protected Boolean turmaEscolhida;
    /** Atributo responsável por manter os objetos da classe <code>HorarioTurma</code>. */
    @Lista
    @ListJson(clazz = HorarioTurmaVO.class)
    private List<HorarioTurmaVO> horarioTurmaVOs;
    @Lista
    private List<HorarioTurmaVO> horarioTurmaVOsExclusao;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Modalidade </code>.*/
    @FKJson
    protected ModalidadeVO modalidade;
    @ChaveEstrangeira
    @FKJson
    protected EmpresaVO empresa;
    private boolean monitorada = false;
    private int totalAulas = 0;
    private int totalAulasAteHoje = 0;
    private int totalPresencas = 0;
    private int totalReposicoes = 0;
    private int totalReposicoesPresentes = 0;
    private int totalAulasDesmarcadas = 0;
    private int totalAulasDesmarcadasPassado = 0;

    private int totalAulasDesmarcadasSemRepsicao = 0;
    //transiente
    @NaoControlarLogAlteracao
    private Date dataInicioMatricula;
    @NaoControlarLogAlteracao
    private Date dataFimMatricula;
    private Integer minutosAntecedenciaMarcarAula=0;
    private Integer minutosAntecedenciaDesmarcarAula=0;
    private Integer tipoAntecedenciaMarcarAula = TipoAntecedenciaMarcarAulaEnum.NAO_VALIDAR.getCodigo();
    private boolean permitirAulaExperimental = false;
    //AULA CHEIA
    private String mensagem;
    private String dias;
    private String horarios;
    private Double bonificacao;
    private Integer pontosBonus;
    private Integer professor;
    private Double meta;
    private Integer ocupacao;
    private Integer codigoAulaCheia;
    private Integer tolerancia;
    private Integer tipoTolerancia;
    private Integer capacidade;
    private Integer limiteVagasAgregados;
    private Integer ambiente;
    private boolean aulaColetiva = false;
    private boolean bloquearReposicaoAcimaLimite = false;
    private boolean permitirDesmarcarReposicoes = true;
    @NaoControlarLogAlteracao
    private int totalAulasDesmarcadasContratoPassado = 0;
    private int totalFaltas = 0;
    private List<AgendaTotalTO> listaDeFaltas = new ArrayList<AgendaTotalTO>();
    private boolean validarRestricoesMarcacao = true;
    private boolean naoValidarModalidadeContrato = false;
    private Integer qtdeNivelOcupacao;
    private Double percDescOcupacaoNivel1;
    private Double percDescOcupacaoNivel2;
    private Double percDescOcupacaoNivel3;
    private Double percDescOcupacaoNivel4;
    private Double percDescOcupacaoNivel5;
    private boolean marcado;
    @NaoControlarLogAlteracao
    private List<NivelDesconto> nivelDescontos = new ArrayList<NivelDesconto>();
    private Integer minutosAposInicioApp;
    protected Boolean integracaoSpivi = false;
    private boolean permiteAlunoOutraEmpresa = false;
    private Integer produtoGymPass;
    private Integer idClasseGymPass;
    private String urlTurmaVirtual;
    private Integer usuario;
    private String urlVideoYoutube;
    private String fotokey;
    private String niveis;
    private boolean visualizarProdutosGympass;
    private boolean visualizarProdutosTotalpass;
    private boolean permiteFixar;
    private boolean aulaIntegracaoSelfloops;
    private boolean manterFotoAnterior;

    private boolean bloquearLotacaoFutura = false;
    private String tipoReservaEquipamento;
    private String mapaEquipamentos;

    private String idExterno;
    /**
     * Construtor padrão da classe <code>Turma</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public TurmaVO() {
        super();
        inicializarDados();
    }

    public String getModalidade_Apresentar() {
        return getModalidade().getNome();
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>TurmaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(TurmaVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo NOME DA TURMA (Turma) deve ser informado.");
        }
        if (obj.getIdentificador().equals("")) {
            throw new ConsistirException("O campo IDENTIFICADOR DA TURMA (Turma) deve ser informado.");
        }
        if ((obj.getModalidade() == null) || (obj.getModalidade().getCodigo() == 0)) {
            throw new ConsistirException("O campo MODALIDADE (Turma) deve ser informado.");
        }
        if (obj.getDataInicialVigencia() == null) {
            throw new ConsistirException("O campo DATA INICIO DE VIGÊNCIA (Turma) deve ser informado.");
        }
        if (obj.getDataFinalVigencia() == null) {
            throw new ConsistirException("O campo DATA FINAL DE VIGÊNCIA (Turma) deve ser informado.");
        }
        if(!Calendario.entre(obj.getDataInicialVigencia(), Uteis.getDate("01/01/1000"), Uteis.getDate("31/12/9999"))){
            throw new ConsistirException("O campo DATA INICIO DE VIGÊNCIA (Turma) está fora do padrão dd/mm/aaaa");
        }
        if(!Calendario.entre(obj.getDataFinalVigencia(), Uteis.getDate("01/01/1000"), Uteis.getDate("31/12/9999"))){
            throw new ConsistirException("O campo DATA FINAL DE VIGÊNCIA (Turma) está fora do padrão dd/mm/aaaa");
        }
        if (obj.getDataFinalVigencia().before(obj.getDataInicialVigencia())) {
            throw new ConsistirException("O campo DATA FINAL DE VIGÊNCIA (Turma) não pode ser antes da DATA DE INICIO.");
        }
        if(obj.getIdadeMinima() == null){
            obj.setIdadeMinima(0);
        }
        if(obj.getIdadeMinimaMeses() == null){
            obj.setIdadeMinimaMeses(0);
        }
//        if (UteisValidacao.emptyNumber(obj.getIdadeMaxima())) {
//            throw new ConsistirException("O campo IDADE MÁXIMA (Turma) deve ser informado.");
//        }

        if(obj.getIdadeMaximaMeses() == null){
            obj.setIdadeMaximaMeses(0);
        }

        /*if (obj.getIdadeMinima().intValue() == 0) {
        throw new ConsistirException("O campo IDADE MÍNIMA (Turma) deve ser informado.");
        }*/
        if (Uteis.getMesesIdade(obj.getIdadeMinima()) + obj.getIdadeMinimaMeses() > Uteis.getMesesIdade(obj.getIdadeMaxima()) + obj.getIdadeMaximaMeses()) {
            throw new ConsistirException("O campo IDADE MÍNIMA (Turma) não pode ser MAIOR que o campo IDADE MÁXIMA.");
        }
//        if (obj.getIdadeMaxima() == 0) {
//            throw new ConsistirException("O campo IDADE MÁXIMA (Turma) deve ser informado.");
//        }
        if(obj.getIdadeMaxima() <= 0 && obj.getIdadeMaximaMeses() <= 0 && !obj.getAulaColetiva()){
            throw  new ConsistirException("O campo IDADE Máxima deve ser maior que 0 ");
        }
        if (!(obj.getIdadeMinimaMeses() >= 0 && obj.getIdadeMinimaMeses() <= 11)  && !obj.getAulaColetiva()) {
            throw new ConsistirException("O campo IDADE MÍNIMA MESES (Turma) deve ser entre 0 e 11 meses ");
        }
        if (!(obj.getIdadeMaximaMeses() >= 0 && obj.getIdadeMaximaMeses() <= 11) && !obj.getAulaColetiva()) {
            throw new ConsistirException("O campo IDADE MÁXIMA MESES (Turma) deve ser entre 0 e 11 meses ");
        }
        if (obj.getTipoAntecedenciaMarcarAulaEnum() != TipoAntecedenciaMarcarAulaEnum.NAO_VALIDAR){
            if (!UtilReflection.objetoMaiorQueZero(obj, "getMinutosAntecedenciaMarcarAula()")){
                throw new ConsistirException("O campo MINUTOS ANTECEDÊNCIA MARCAR AULA (Turma) deve ser informado. ");
            }
        }
        if (obj.getMinutosAposInicioApp() < 0 || obj.getMinutosAposInicioApp() > 60) {
            throw new ConsistirException("O campo \"Tolerância em minutos após início para apresentar aula APP (Turma)\" deve ser entre 0 e 60 minutos");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setIdentificador(getIdentificador().toUpperCase());
        setDescricao(getDescricao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setIdentificador("");
        setDescricao("");
        setDataInicialVigencia(negocio.comuns.utilitarias.Calendario.hoje());
        setDataFinalVigencia(negocio.comuns.utilitarias.Calendario.hoje());
        setIdadeMinima(0);
        setIdadeMaxima(0);
        setIdadeMinimaMeses(0);
        setIdadeMaximaMeses(0);
        setBloquearMatriculasAcimaLimite(false);
        setPermitirDesmarcarReposicoes(true);
        setTurmaEscolhida(false);
        setHorarioTurmaVOs(new ArrayList());
        setHorarioTurmaVOsExclusao(new ArrayList());
        setModalidade(new ModalidadeVO());
        setEmpresa(new EmpresaVO());
        setVisualizarProdutosGympass(false);
        setVisualizarProdutosTotalpass(false);
        setPermiteFixar(false);
        setAulaIntegracaoSelfloops(false);
    }

    public static boolean validarSeEstaNoIntervaloHoras(String horasInicial1, String horasFinal1, String horasInicial2, String horasFinal2) {
        return ((horasInicial2.compareTo(horasInicial1) >= 0) && (horasInicial2.compareTo(horasFinal1)) <= 0)
                || (horasFinal2.compareTo(horasInicial1) >= 0 && horasFinal2.compareTo(horasFinal1) <= 0)
                || (horasInicial2.compareTo(horasInicial1) < 0 && horasFinal2.compareTo(horasFinal1) > 0);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>HorarioTurmaVO</code>
     * no List <code>horarioTurmaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>HorarioTurma</code> - getIdentificador() - como identificador (key) do objeto no List.
     * @param obj  Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjHorarioTurmaVOs(HorarioTurmaVO obj) throws Exception {
        int index = 0;
        Iterator i = getHorarioTurmaVOs().iterator();
        while (i.hasNext()) {
            HorarioTurmaVO objExistente = (HorarioTurmaVO) i.next();
            if (objExistente.getDiaSemana().equals(obj.getDiaSemana())
                    && objExistente.getHoraInicial().equals(obj.getHoraInicial())
                    && objExistente.getHoraFinal().equals(obj.getHoraFinal())) {
                getHorarioTurmaVOs().remove(index);
                getHorarioTurmaVOsExclusao().add(obj);
                //      new HorarioTurma().excluir(obj);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>HorarioTurmaVO</code>
     * no List <code>horarioTurmaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>HorarioTurma</code> - getIdentificador() - como identificador (key) do objeto no List.
     * @param obj Parâmetro para localizar o objeto do List.
     */
    public HorarioTurmaVO consultarObjHorarioTurmaVO(HorarioTurmaVO obj) throws Exception {
        for (Object o : getHorarioTurmaVOs()) {
            HorarioTurmaVO objExistente = (HorarioTurmaVO) o;
            if (objExistente.getDiaSemana().equals(obj.getDiaSemana())
                    && objExistente.getHoraInicial().equals(obj.getHoraInicial())
                    && objExistente.getHoraFinal().equals(obj.getHoraFinal())) {
                if(!(getEmpresa().isPermiteHorariosConcorrentesParaProfessor() && obj.getAmbiente().getCodigo().equals(objExistente.getAmbiente().getCodigo()))
                      && !(getEmpresa().isProfessorEmAmbientesDiferentesMesmoHorario() && !obj.getAmbiente().getCodigo().equals(objExistente.getAmbiente().getCodigo()))){
                    return objExistente;
                }
            }
        }
        return null;
    }

    public Boolean getDesenhaModalidade() {
        return getEmpresa().getCodigo() != 0;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public ModalidadeVO getModalidade() {
        if (modalidade == null) {
            modalidade = new ModalidadeVO();
        }
        return modalidade;
    }

    public void setModalidade(ModalidadeVO obj) {
        this.modalidade = obj;
    }

    public List<HorarioTurmaVO> getHorarioTurmaVOs() {
        return (horarioTurmaVOs);
    }

    public void setHorarioTurmaVOs(List horarioTurmaVOs) {
        this.horarioTurmaVOs = horarioTurmaVOs;
    }

    public Boolean getBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public void setBloquearMatriculasAcimaLimite(Boolean bloquearMatriculasAcimaLimite) {
        this.bloquearMatriculasAcimaLimite = bloquearMatriculasAcimaLimite;
    }

    public Boolean isBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public Integer getIdadeMaxima() {
        if(idadeMaxima == null)
            idadeMaxima = 0;
        return (idadeMaxima);
    }

    public void setIdadeMaxima(Integer idadeMaxima) {
        this.idadeMaxima = idadeMaxima;
    }

    public Integer getIdadeMinima() {
        return (idadeMinima);
    }

    public void setIdadeMinima(Integer idadeMinima) {
        this.idadeMinima = idadeMinima;
    }

    public Date getDataFinalVigencia() {
        return (dataFinalVigencia);
    }

    public String getDataFinalVigencia_Apresentar() {
        return (Uteis.getData(dataFinalVigencia));
    }

    public void setDataFinalVigencia(Date dataFinalVigencia) {
        this.dataFinalVigencia = dataFinalVigencia;
    }

    public Date getDataInicialVigencia() {
        return (dataInicialVigencia);
    }

    public String getDataInicialVigencia_Apresentar() {
        return (Uteis.getData(dataInicialVigencia));
    }

    public void setDataInicialVigencia(Date dataInicialVigencia) {
        this.dataInicialVigencia = dataInicialVigencia;
    }

    public String getIdentificador() {
        if (identificador == null) {
            identificador = "";
        }
        return (identificador);
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getTurmaEscolhida() {
        return turmaEscolhida;
    }

    public void setTurmaEscolhida(Boolean turmaEscolhida) {
        this.turmaEscolhida = turmaEscolhida;
    }

    public Boolean getIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public void setIntegracaoSpivi(Boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<HorarioTurmaVO> getHorarioTurmaVOsExclusao() {
        return horarioTurmaVOsExclusao;
    }

    public void setHorarioTurmaVOsExclusao(List<HorarioTurmaVO> horarioTurmaVOsExclusao) {
        this.horarioTurmaVOsExclusao = horarioTurmaVOsExclusao;
    }

    public boolean isMonitorada() {
        return monitorada;
    }

    public void setMonitorada(boolean monitorada) {
        this.monitorada = monitorada;
    }

    public String getMonitorada_Apresentar() {
        return monitorada ? "Sim" : "Não";
    }

    public TurmaVO clone() throws CloneNotSupportedException {
        return (TurmaVO) super.clone();
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public int getTotalAulas() {
        return totalAulas;
    }

    public void setTotalAulas(int totalAulas) {
        this.totalAulas = totalAulas;
    }

    public int getTotalPresencas() {
        return totalPresencas;
    }

    public void setTotalPresencas(int totalPresencas) {
        this.totalPresencas = totalPresencas;
    }

    public int getTotalReposicoes() {
        return totalReposicoes;
    }

    public void setTotalReposicoes(int totalReposicoes) {
        this.totalReposicoes = totalReposicoes;
    }

    public int getTotalReposicoesPresentes() {
        return totalReposicoesPresentes;
    }

    public void setTotalReposicoesPresentes(int totalReposicoesPresentes) {
        this.totalReposicoesPresentes = totalReposicoesPresentes;
    }

    public int getTotalAulasAteHoje() {
        return totalAulasAteHoje;
    }

    public void setTotalAulasAteHoje(int totalAulasAteHoje) {
        this.totalAulasAteHoje = totalAulasAteHoje;
    }

    public Date getDataInicioMatricula() {
        return dataInicioMatricula;
    }

    public void setDataInicioMatricula(Date dataInicioMatricula) {
        this.dataInicioMatricula = dataInicioMatricula;
    }

    public Date getDataFimMatricula() {
        return dataFimMatricula;
    }

    public void setDataFimMatricula(Date dataFimMatricula) {
        this.dataFimMatricula = dataFimMatricula;
    }

    public String getDataInicioMatricula_Apresentar() {
        return dataInicioMatricula != null ? Uteis.getData(dataInicioMatricula, "dd/MM/yyyy") : "";
    }

    public String getDataFimMatricula_Apresentar() {
        return dataFimMatricula != null ? Uteis.getData(dataFimMatricula, "dd/MM/yyyy") : "";
    }

    public int getTotalAulasDesmarcadas() {
        return totalAulasDesmarcadas;
    }

    public void setTotalAulasDesmarcadas(int totalAulasDesmarcadas) {
        this.totalAulasDesmarcadas = totalAulasDesmarcadas;
    }

    public Integer getMinutosAntecedenciaMarcarAula() {
        return minutosAntecedenciaMarcarAula;
    }

    public void setMinutosAntecedenciaMarcarAula(Integer minutosAntecedenciaMarcarAula) {
        this.minutosAntecedenciaMarcarAula = minutosAntecedenciaMarcarAula;
    }

    public Integer getMinutosAntecedenciaDesmarcarAula() {
        return minutosAntecedenciaDesmarcarAula;
    }

    public void setMinutosAntecedenciaDesmarcarAula(Integer minutosAntecedenciaDesmarcarAula) {
        this.minutosAntecedenciaDesmarcarAula = minutosAntecedenciaDesmarcarAula;
    }

    public String getMensagem() {
        if(mensagem == null){
            mensagem = "";
        }
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Double getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(Double bonificacao) {
        this.bonificacao = bonificacao;
    }

    public Double getMeta() {
        return meta;
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public boolean getAulaColetiva() {
        return aulaColetiva;
    }

    public void setAulaColetiva(boolean aulaColetiva) {
        this.aulaColetiva = aulaColetiva;
    }

    public Integer getPontosBonus() {
        return pontosBonus;
    }

    public void setPontosBonus(Integer pontosBonus) {
        this.pontosBonus = pontosBonus;
    }

    public Integer getCodigoAulaCheia() {
        return codigoAulaCheia;
    }

    public void setCodigoAulaCheia(Integer codigoAulaCheia) {
        this.codigoAulaCheia = codigoAulaCheia;
    }

    public String getDias() {
        return dias;
    }

    public void setDias(String dias) {
        this.dias = dias;
    }

    public String getHorarios() {
        return horarios;
    }

    public void setHorarios(String horarios) {
        this.horarios = horarios;
    }

    public Integer getProfessor() {
        return professor;
    }

    public void setProfessor(Integer professor) {
        this.professor = professor;
    }

    public Integer getTolerancia() {
        return tolerancia;
    }

    public void setTolerancia(Integer tolerancia) {
        this.tolerancia = tolerancia;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getLimiteVagasAgregados() {
        return limiteVagasAgregados;
    }

    public void setLimiteVagasAgregados(Integer limiteVagasAgregados) {
        this.limiteVagasAgregados = limiteVagasAgregados;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public TipoAntecedenciaMarcarAulaEnum getTipoAntecedenciaMarcarAulaEnum() {
        return TipoAntecedenciaMarcarAulaEnum.getTipo(this.tipoAntecedenciaMarcarAula);
    }

    public Integer getTipoAntecedenciaMarcarAula() {
        return tipoAntecedenciaMarcarAula;
    }

    public void setTipoAntecedenciaMarcarAula(Integer tipoAntecedenciaMarcarAula) {
        this.tipoAntecedenciaMarcarAula = tipoAntecedenciaMarcarAula;
    }

    public boolean isBloquearReposicaoAcimaLimite() {
        return bloquearReposicaoAcimaLimite;
    }

    public void setBloquearReposicaoAcimaLimite(boolean bloquearReposicaoAcimaLimite) {
        this.bloquearReposicaoAcimaLimite = bloquearReposicaoAcimaLimite;
    }

    public boolean isPermitirAulaExperimental() {
        return permitirAulaExperimental;
    }

    public void setPermitirAulaExperimental(boolean permitirAulaExperimental) {
        this.permitirAulaExperimental = permitirAulaExperimental;
    }
    
    public int getTotalAulasDesmarcadasPassado() {
        return totalAulasDesmarcadasPassado;
    }

    public void setTotalAulasDesmarcadasPassado(int totalAulasDesmarcadasPassado) {
        this.totalAulasDesmarcadasPassado = totalAulasDesmarcadasPassado;
    }

    public boolean isPermitirDesmarcarReposicoes() {
        return permitirDesmarcarReposicoes;
    }

    public void setPermitirDesmarcarReposicoes(boolean permitirDesmarcarReposicoes) {
        this.permitirDesmarcarReposicoes = permitirDesmarcarReposicoes;
    }
    
    public int getTotalAulasDesmarcadasContratoPassado() {
        return totalAulasDesmarcadasContratoPassado;
    }

    public void setTotalAulasDesmarcadasContratoPassado(int totalAulasDesmarcadasContratoPassado) {
        this.totalAulasDesmarcadasContratoPassado = totalAulasDesmarcadasContratoPassado;
    }

    public int getTotalFaltas() {
        return totalFaltas;
    }

    public void setTotalFaltas(int totalFaltas) {
        this.totalFaltas = totalFaltas;
    }

    public List<AgendaTotalTO> getListaDeFaltas() {
        return listaDeFaltas;
    }

    public void setListaDeFaltas(List<AgendaTotalTO> listaDeFaltas) {
        this.listaDeFaltas = listaDeFaltas;
    }

    public boolean isValidarRestricoesMarcacao() {
        return validarRestricoesMarcacao;
    }

    public void setValidarRestricoesMarcacao(boolean validarRestricoesMarcacao) {
        this.validarRestricoesMarcacao = validarRestricoesMarcacao;
    }

    public Integer getQtdeNivelOcupacao() {
        return null == qtdeNivelOcupacao ? 0 : qtdeNivelOcupacao;
    }

    public void setQtdeNivelOcupacao(Integer qtdeNivelOcupacao) {
        this.qtdeNivelOcupacao = qtdeNivelOcupacao;
    }

    public Double getPercDescOcupacaoNivel1() {
        return null == percDescOcupacaoNivel1 ? 0D : percDescOcupacaoNivel1;
    }

    public void setPercDescOcupacaoNivel1(Double percDescOcupacaoNivel1) {
        this.percDescOcupacaoNivel1 = percDescOcupacaoNivel1;
    }

    public Double getPercDescOcupacaoNivel2() {
        return null == percDescOcupacaoNivel2 ? 0D : percDescOcupacaoNivel2;
    }

    public void setPercDescOcupacaoNivel2(Double percDescOcupacaoNivel2) {
        this.percDescOcupacaoNivel2 = percDescOcupacaoNivel2;
    }

    public Double getPercDescOcupacaoNivel3() {
        return null == percDescOcupacaoNivel3 ? 0D : percDescOcupacaoNivel3;
    }

    public void setPercDescOcupacaoNivel3(Double percDescOcupacaoNivel3) {
        this.percDescOcupacaoNivel3 = percDescOcupacaoNivel3;
    }

    public Double getPercDescOcupacaoNivel4() {
        return null == percDescOcupacaoNivel4 ? 0D : percDescOcupacaoNivel4;
    }

    public void setPercDescOcupacaoNivel4(Double percDescOcupacaoNivel4) {
        this.percDescOcupacaoNivel4 = percDescOcupacaoNivel4;
    }

    public Double getPercDescOcupacaoNivel5() {
        return null == percDescOcupacaoNivel5 ? 0D : percDescOcupacaoNivel5;
    }

    public void setPercDescOcupacaoNivel5(Double percDescOcupacaoNivel5) {
        this.percDescOcupacaoNivel5 = percDescOcupacaoNivel5;
    }

    public int getTotalAulasDesmarcadasSemRepsicao() {
        return totalAulasDesmarcadasSemRepsicao;
    }

    public void setTotalAulasDesmarcadasSemRepsicao(int totalAulasDesmarcadasSemRepsicao) {
        this.totalAulasDesmarcadasSemRepsicao = totalAulasDesmarcadasSemRepsicao;
    }

    public List<NivelDesconto> getNivelDescontos() {

        if(null == nivelDescontos || getQtdeNivelOcupacao() != nivelDescontos.size()) {
            nivelDescontos = new ArrayList<NivelDesconto>();
            if (getQtdeNivelOcupacao() > 0) {
                for (int i = 0; i < getQtdeNivelOcupacao(); i++) {
                    BigDecimal pi = new BigDecimal(i * 100. / getQtdeNivelOcupacao());
                    if (i > 0) {
                        pi = pi.add(new BigDecimal("0.01"));
                    }
                    BigDecimal pf = new BigDecimal((i + 1) * 100. / getQtdeNivelOcupacao());
                    nivelDescontos.add(new TurmaVO.NivelDesconto(pi.setScale(2, RoundingMode.HALF_EVEN),
                            pf.setScale(2, RoundingMode.HALF_EVEN), getPerc(i), TurmaVO.NivelDesconto.NivelCor.values()[i].getColor()));
                }
            }
        }
        return nivelDescontos;
    }

    public NivelDesconto getByPercentualOcupacao(double perc){
        BigDecimal p = new BigDecimal(perc).setScale(2, RoundingMode.HALF_EVEN);
        for(NivelDesconto nd :getNivelDescontos()){
            if(p.compareTo(nd.getPercentualIncial()) >= 0 && p.compareTo(nd.getPercentualFinal()) <= 0){
                return nd;
            }
        }
        return null;
    }

    private Double getPerc(int i) {
        switch (i) {
            case 0:
                return getPercDescOcupacaoNivel1();
            case 1:
                return getPercDescOcupacaoNivel2();
            case 2:
                return getPercDescOcupacaoNivel3();
            case 3:
                return getPercDescOcupacaoNivel4();
            case 4:
                return getPercDescOcupacaoNivel5();
            default:
                return 0D;
        }
    }

    private void setPerc(int i, Double v) {
        switch (i) {
            case 0:
                setPercDescOcupacaoNivel1(v); break;
            case 1:
                setPercDescOcupacaoNivel2(v);break;
            case 2:
                setPercDescOcupacaoNivel3(v);break;
            case 3:
                setPercDescOcupacaoNivel4(v);break;
            case 4:
                setPercDescOcupacaoNivel5(v);break;
        }
    }

    public void setNivelDescontos() {
        if (getQtdeNivelOcupacao() > 0) {
            for (int i = 0; i < 5; i++) {
                setPerc(i, i < getQtdeNivelOcupacao() ? getNivelDescontos().get(i).getPercentualDesconto(): 0);
            }
        }
    }


    public Integer getMinutosAposInicioApp() {
        if (minutosAposInicioApp == null) {
            minutosAposInicioApp = 0;
        }
        return minutosAposInicioApp;
    }

    public void setMinutosAposInicioApp(Integer minutosAposInicioApp) {
        this.minutosAposInicioApp = minutosAposInicioApp;
    }

    public boolean isPermiteAlunoOutraEmpresa() {
        return permiteAlunoOutraEmpresa;
    }

    public void setPermiteAlunoOutraEmpresa(boolean permiteAlunoOutraEmpresa) {
        this.permiteAlunoOutraEmpresa = permiteAlunoOutraEmpresa;
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public String getUrlTurmaVirtual() {
        return urlTurmaVirtual;
    }

    public void setUrlTurmaVirtual(String urlTurmaVirtual) {
        this.urlTurmaVirtual = urlTurmaVirtual;
    }

    public String getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }

    public static class NivelDesconto implements Serializable {
        private BigDecimal percentualIncial;
        private BigDecimal percentualFinal;
        private double percentualDesconto;
        private String color;

        public static enum NivelCor {

            ONE("#A6FFA6"), TWO("#A6FFFF"), THREE("#FFFFA6"), FOUR("#FEE3BB"), FIVE("#FFA6A6");

            private String color;

            NivelCor(String color) {
                this.color = color;
            }

            public String getColor() {
                return color;
            }

            public static NivelCor fromColor(String c) {
                for (NivelCor nc : NivelCor.values()) {
                    if (nc.getColor().equalsIgnoreCase(c)) {
                        return nc;
                    }
                }
                return NivelCor.ONE;
            }

        }

        public NivelDesconto() {
        }

        public NivelDesconto(BigDecimal percentualIncial, BigDecimal percentualFinal, double percentualDesconto, String color) {
            this.percentualIncial = percentualIncial;
            this.percentualFinal = percentualFinal;
            this.percentualDesconto = percentualDesconto;
            this.color = color;
        }

        public BigDecimal getPercentualIncial() {
            return percentualIncial;
        }

        public void setPercentualIncial(BigDecimal percentualIncial) {
            this.percentualIncial = percentualIncial;
        }

        public BigDecimal getPercentualFinal() {
            return percentualFinal;
        }

        public void setPercentualFinal(BigDecimal percentualFinal) {
            this.percentualFinal = percentualFinal;
        }

        public double getPercentualDesconto() {
            return Uteis.arredondarForcando2CasasDecimais(percentualDesconto);
        }

        public void setPercentualDesconto(double percentualDesconto) {
            this.percentualDesconto = percentualDesconto;
        }

        public String getColor() {
            return color;
        }

        public void setColor(String color) {
            this.color = color;
        }
    }

    @Override
    public boolean equals(Object obj) {
        return obj instanceof TurmaVO && ((TurmaVO)obj).getCodigo() == this.codigo;
    }

    public int compareTo(@NotNull Object obj) {
        if (obj instanceof TurmaVO) {
            return this.descricao.compareTo(((TurmaVO)obj).getDescricao());
        }
        return -1;
    }

    @Override
    public String toString() {
        return this.descricao;
    }

    public boolean isMarcado() {
        return marcado;
    }

    public void setMarcado(boolean marcado) {
        this.marcado = marcado;
    }

    public boolean isNaoValidarModalidadeContrato() {
        return naoValidarModalidadeContrato;
    }

    public void setNaoValidarModalidadeContrato(boolean naoValidarModalidadeContrato) {
        this.naoValidarModalidadeContrato = naoValidarModalidadeContrato;
    }

    public Integer getTipoTolerancia() {
        return tipoTolerancia;
    }

    public void setTipoTolerancia(Integer tipoTolerancia) {
        this.tipoTolerancia = tipoTolerancia;
    }

    public Integer getUsuario() { return usuario; }

    public void setUsuario(Integer usuario) { this.usuario = usuario; }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String urlVideoYoutube) {
        this.urlVideoYoutube = urlVideoYoutube;
    }

    public String getFotokey() {
        return fotokey;
    }

    public void setFotokey(String fotokey) {
        this.fotokey = fotokey;
    }

    public boolean getVisualizarProdutosGympass() {return visualizarProdutosGympass;  }

    public void setVisualizarProdutosGympass(boolean visualizarProdutosGympass) {
        this.visualizarProdutosGympass = visualizarProdutosGympass;
    }
    public boolean getVisualizarProdutosTotalpass() {return visualizarProdutosTotalpass; }

    public void setVisualizarProdutosTotalpass(boolean visualizarProdutosTotalpass) {
        this.visualizarProdutosTotalpass = visualizarProdutosTotalpass;
    }

    public boolean isPermiteFixar() {
        return permiteFixar;
    }

    public void setPermiteFixar(boolean permiteFixar) {
        this.permiteFixar = permiteFixar;
    }


    public boolean isManterFotoAnterior() {
        return manterFotoAnterior;
    }

    public void setManterFotoAnterior(boolean manterFotoAnterior) {
        this.manterFotoAnterior = manterFotoAnterior;
    }

    public boolean isBloquearLotacaoFutura() {
        return bloquearLotacaoFutura;
    }

    public void setBloquearLotacaoFutura(boolean bloquearLotacaoFutura) {
        this.bloquearLotacaoFutura = bloquearLotacaoFutura;
    }

    public String getNiveis() {
        return niveis;
    }

    public void setNiveis(String niveis) {
        this.niveis = niveis;
    }

    public String getTipoReservaEquipamento() {
        return tipoReservaEquipamento;
    }

    public void setTipoReservaEquipamento(String tipoReservaEquipamento) {
        this.tipoReservaEquipamento = tipoReservaEquipamento;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public boolean isAulaIntegracaoSelfloops() {
        return aulaIntegracaoSelfloops;
    }

    public void setAulaIntegracaoSelfloops(boolean aulaIntegracaoSelfloops) {
        this.aulaIntegracaoSelfloops = aulaIntegracaoSelfloops;
    }
}

package negocio.comuns.plano;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import java.util.Date;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.facade.jdbc.plano.Modalidade;

/**
 * Reponsável por manter os dados da entidade ProdutoSugerido. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see Modalidade
*/
public class ProdutoSugeridoVO extends SuperVO {
	
    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer modalidade;    
    protected Boolean obrigatorio;
    @NaoControlarLogAlteracao
    protected Boolean produtoSugeridoEscolhida;
    @NaoControlarLogAlteracao
    protected Date dataVenda;
    @NaoControlarLogAlteracao
    protected Date dataValidade;

    /** Atributo responsável por manter o objeto relacionado da classe <code>Produto </code>.*/
    @ChaveEstrangeira
    @FKJson
    protected ProdutoVO produto;
	
    /**
     * Construtor padrão da classe <code>ProdutoSugerido</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public ProdutoSugeridoVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ProdutoSugeridoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(ProdutoSugeridoVO obj) throws ConsistirException {
        
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
        if ((obj.getProduto() == null) ||
            (obj.getProduto().getCodigo().intValue() == 0)) { 
            throw new ConsistirException("O campo PRODUTO (Produto Sugerido) deve ser informado.");
        }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setObrigatorio( new Boolean(false) );
        setProdutoSugeridoEscolhida( new Boolean(false) );
        setProduto( new ProdutoVO() );
        setModalidade(new Integer(0));
    }
	

    /**
     * Retorna o objeto da classe <code>Produto</code> relacionado com (<code>ProdutoSugerido</code>).
    */
    public ProdutoVO getProduto() {
        if (produto == null) {
            produto = new ProdutoVO();
        }
        return (produto);
    }
     
    /**
     * Define o objeto da classe <code>Produto</code> relacionado com (<code>ProdutoSugerido</code>).
    */
    public void setProduto( ProdutoVO obj) {
        this.produto = obj;
    }
    
     public String getObrigatorio_Apresentar() {
        if (obrigatorio) {
            return "Sim";
        }
        if (!obrigatorio) {
            return "Não";
        }
        return ("");
    }

    public Boolean getObrigatorio() {
        return (obrigatorio);
    }
    public Boolean isObrigatorio() {
        return (obrigatorio);
    }
     
    public void setObrigatorio( Boolean Obrigatorio ) {
        this.obrigatorio = Obrigatorio;
    }

    public Integer getModalidade() {
        return (modalidade);
    }
     
    public void setModalidade( Integer modalidade ) {
        this.modalidade = modalidade;
    }

    public Integer getCodigo() {
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }

    public Boolean getProdutoSugeridoEscolhida() {
        return produtoSugeridoEscolhida;
    }

    public void setProdutoSugeridoEscolhida(Boolean produtoSugeridoEscolhida) {
        this.produtoSugeridoEscolhida = produtoSugeridoEscolhida;
    }

    public Date getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(Date dataValidade) {
        this.dataValidade = dataValidade;
    }

    public Date getDataVenda() {
        return dataVenda;
    }

    public void setDataVenda(Date dataVenda) {
        this.dataVenda = dataVenda;
    }

    
}
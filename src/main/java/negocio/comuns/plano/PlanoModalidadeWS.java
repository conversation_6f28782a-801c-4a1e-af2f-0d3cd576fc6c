package negocio.comuns.plano;

import java.util.List;

/**
 * Created by glauco on 02/12/2014.
 */
public class PlanoModalidadeWS {

    private int codigo;
    private String modalidade;
    private boolean utilizarTurma;
    private List<Integer> nrsVezesSemana;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public List<Integer> getNrsVezesSemana() {
        return nrsVezesSemana;
    }

    public void setNrsVezesSemana(List<Integer> nrsVezesSemana) {
        this.nrsVezesSemana = nrsVezesSemana;
    }

    public boolean isUtilizarTurma() {
        return utilizarTurma;
    }

    public void setUtilizarTurma(boolean utilizarTurma) {
        this.utilizarTurma = utilizarTurma;
    }
}

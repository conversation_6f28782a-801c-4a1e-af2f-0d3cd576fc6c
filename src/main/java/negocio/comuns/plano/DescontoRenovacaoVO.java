
package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.plano.enumerador.TipoIntervalo;

/**
 *
 * <AUTHOR>
 */
public class DescontoRenovacaoVO extends SuperVO {
    @ChavePrimaria
    private Integer codigo = 0;
    private int desconto = 0;
    private TipoIntervalo tipoIntervalo = TipoIntervalo.AN;
    private int intervaloDe = 0;
    private int intervaloAte = 0;
    protected TipoDesconto tipoDesconto = TipoDesconto.PE;
    private double valor = 0.0;
    private JustificativaOperacaoVO justificativaBonus = new JustificativaOperacaoVO();

    public void validarDados() throws Exception {
        // codigo e desconto não precisam ser testados
        if(intervaloDe < 0)
            throw new Exception("'Intervalo de' não Pode ser Negativo.");
        if(intervaloAte < 0)
            throw new Exception("'Intervalo ate' não Pode ser Negativo.");
        if(intervaloDe > getIntervaloAte())
            throw new Exception("'Intervalo de' não Pode ser Maior que o 'Intervalo ate'.");
        if(valor <= 0)
            throw new Exception("Valor não pode ser Menor ou igual a Zero.");
        if(tipoDesconto.equals(TipoDesconto.BO) && (justificativaBonus == null ||
           justificativaBonus.getCodigo().intValue() == 0))
            throw new Exception("Informe Uma Justificativa de Bonus.");
        if(tipoDesconto.equals(TipoDesconto.BO) && (Math.round(valor) == 0 || valor/Math.round(valor) != 1))
            throw new Exception("Para Desconto do Tipo Bônus o Valor Deve ser em Quantidade de Dias. Somente Inteiros.");
    }

    public String getTipoDesconto_Apresentar() {
        return tipoDesconto.getDescricao();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getDesconto() {
        return desconto;
    }

    public void setDesconto(int desconto) {
        this.desconto = desconto;
    }

    public TipoIntervalo getTipoIntervalo() {
        return tipoIntervalo;
    }

    public void setTipoIntervalo(TipoIntervalo tipoIntervalo) {
        this.tipoIntervalo = tipoIntervalo;
    }

    public int getIntervaloDe() {
        return intervaloDe;
    }

    public void setIntervaloDe(int intervaloDe) {
        this.intervaloDe = intervaloDe;
    }

    public int getIntervaloAte() {
        return intervaloAte;
    }

    public void setIntervaloAte(int intervaloAte) {
        this.intervaloAte = intervaloAte;
    }

    public TipoDesconto getTipoDesconto() {
        return tipoDesconto;
    }

    public void setTipoDesconto(TipoDesconto tipoDesconto) {
        this.tipoDesconto = tipoDesconto;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public JustificativaOperacaoVO getJustificativaBonus() {
        return justificativaBonus;
    }

    public void setJustificativaBonus(JustificativaOperacaoVO tipoJustificativa) {
        this.justificativaBonus = tipoJustificativa;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof DescontoRenovacaoVO) {
            DescontoRenovacaoVO aux = (DescontoRenovacaoVO)obj;
            return aux.getIntervaloDe() == this.getIntervaloDe() &&
                   aux.getIntervaloAte() == this.getIntervaloAte() &&
                   aux.getTipoIntervalo() == this.getTipoIntervalo() &&
                   aux.getTipoDesconto() == this.getTipoDesconto() &&
                   aux.getJustificativaBonus().getCodigo().intValue() == this.getJustificativaBonus().getCodigo().intValue();
        }
        return false;
    }
}

package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import servicos.discovery.RedeDTO;

import java.util.Date;

public class PlanoRedeEmpresaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer plano;
    private String chave;
    private Date datacadastro;
    private Date dataatualizacao;
    private Integer planoReplicado;
    private String nomeUnidade;
    private String mensagemSituacao;
    private RedeDTO redeDTO;
    private boolean selecionado;
    private Integer codigoEmpresaDestino;

    public PlanoRedeEmpresaVO(Integer plano, String chave, Integer planoReplicado, Integer codigoEmpresaDestino) {
        this.plano = plano;
        this.chave = chave;
        this.planoReplicado = planoReplicado;
        this.codigoEmpresaDestino = codigoEmpresaDestino;
    }

    public PlanoRedeEmpresaVO(String nomeUnidade, Integer plano, String chave, Integer planoReplicado) {
        this.nomeUnidade = nomeUnidade;
        this.plano = plano;
        this.chave = chave;
        this.planoReplicado = planoReplicado;
    }

    public PlanoRedeEmpresaVO() {
        
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Date getDatacadastro() {
        return datacadastro;
    }

    public void setDatacadastro(Date datacadastro) {
        this.datacadastro = datacadastro;
    }

    public Boolean getDataAtualizacaoInformada() {
        return getDataatualizacao() != null;
    }
    public Date getDataatualizacao() {
        return dataatualizacao;
    }

    public void setDataatualizacao(Date dataatualizacao) {
        this.dataatualizacao = dataatualizacao;
    }

    public Integer getPlanoReplicado() {
        return planoReplicado;
    }

    public void setPlanoReplicado(Integer planoReplicado) {
        this.planoReplicado = planoReplicado;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getMensagemSituacao() {
        return mensagemSituacao;
    }

    public void setMensagemSituacao(String mensagemSituacao) {
        this.mensagemSituacao = mensagemSituacao;
    }

    public RedeDTO getRedeDTO() {
        return redeDTO;
    }

    public void setRedeDTO(RedeDTO redeDTO) {
        this.redeDTO = redeDTO;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Integer getCodigoEmpresaDestino() {
        return codigoEmpresaDestino;
    }

    public void setCodigoEmpresaDestino(Integer codigoEmpresaDestino) {
        this.codigoEmpresaDestino = codigoEmpresaDestino;
    }
}

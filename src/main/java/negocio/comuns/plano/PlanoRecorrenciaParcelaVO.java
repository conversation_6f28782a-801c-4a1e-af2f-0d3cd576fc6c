package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;

public class PlanoRecorrenciaParcelaVO extends SuperVO {
    private int numero;
    private Double valor = 0.0;
    private Double valorDesconto = 0.0;

    public PlanoRecorrenciaParcelaVO() {
    }

    public PlanoRecorrenciaParcelaVO(int numero, double valor) {
        setNumero(numero);
        setValor(valor);
    }

    public PlanoRecorrenciaParcelaWS toWS(){
        return new PlanoRecorrenciaParcelaWS(this);
    }

    public void parseValorFormatado(String valorFormatado){
        this.valor = Double.parseDouble(valorFormatado.replace(",", "."));
    }

    public int getNumero() {
        return numero;
    }

    public void setNumero(int numero) {
        this.numero = numero;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getValorFormatado() {
        return valor == null ? "" : String.format("%.2f",valor - valorDesconto).replace(".", ",");
    }



    public Double getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public Double getValorFinal(){
        return valor - valorDesconto;
    }
}

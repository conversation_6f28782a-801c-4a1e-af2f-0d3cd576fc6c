/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.plano;

/**
 *
 * <AUTHOR>
 */
public class MarcadorVO {
    protected String nome;
    protected String tag;
    protected Boolean selecionado;

    public MarcadorVO(){
        inicializarDados();
    }
    
    public void inicializarDados(){
        setNome("");
        setTag("");
        setSelecionado(new Boolean(false));
    }

    public String getTag() {
        if (tag == null) {
            tag = "";
        }
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }
    
   
    
    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getSelecionado() {
    	if(this.selecionado == null){
    		this.selecionado = Boolean.FALSE;
    	}
        return this.selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }
    
    
}

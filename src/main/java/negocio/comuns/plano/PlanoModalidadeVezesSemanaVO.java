package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.plano.enumerador.ReferenciaValorModalidadeEnum;

/**
 * Reponsável por manter os dados da entidade PlanoModalidadeVezesSemana. Classe
 * do tipo VO - Value Object composta pelos atributos da entidade com
 * visibilidade protegida e os métodos de acesso a estes atributos. Classe
 * utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class PlanoModalidadeVezesSemanaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer planoModalidade;
    protected Integer nrVezes;
    @NaoControlarLogAlteracao
    protected Boolean vezeSemanaEscolhida;
    protected String tipoValor;
    protected Double percentualDesconto;
    protected Double valorEspecifico;
    protected String tipoOperacao;
    private boolean referencia = false;
    @NaoControlarLogAlteracao
    protected Boolean desenharValorEspecificoVezesSemana;
    @NaoControlarLogAlteracao
    protected Boolean desenharValorDescontoVezesSemana;
    @NaoControlarLogAlteracao
    private Double valorReferencia;
    @NaoControlarLogAlteracao
    private ReferenciaValorModalidadeEnum origem;

    /**
     * Construtor padrão da classe <code>PlanoModalidadeVezesSemana</code>. Cria
     * uma nova instância desta entidade, inicializando automaticamente seus
     * atributos (Classe VO).
     */
    public PlanoModalidadeVezesSemanaVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>PlanoModalidadeVezesSemanaVO</code>. Todos os tipos de consistência
     * de dados são e devem ser implementadas neste método. São validações
     * típicas: verificação de campos obrigatórios, verificação de valores
     * válidos para os atributos.
     *
     * @exception ConsistirExecption Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDados(PlanoModalidadeVezesSemanaVO obj, boolean validarValor) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getNrVezes().intValue() == 0) {
            throw new ConsistirException("O campo NÚMERO DE VEZES POR SEMANA(Modalidade Vezes Semana) deve ser informado.");
        }

        if (obj.getPlanoModalidade() == null) {
            throw new ConsistirException("O campo PLANO MODALIDADE (Modalidade Vezes Semana) deve ser informado.");
        }

        if (obj.getTipoValor() == null) {
            obj.setTipoValor("");
        }

        if (obj.getTipoValor().equals("PD")) {
            //para melhorar a usabilidade, ao inves de não deixar o sistema gravar o plano inteiro por causa de dado zerado,
            // vamos tratar aqui os tipos de operação quando os valores estiverem zerados
            if(!validarValor && UteisValidacao.emptyNumber(obj.getPercentualDesconto())){
                obj.setTipoValor("");
                obj.setTipoOperacao("");
            } else if (obj.getTipoOperacao() == null || obj.getTipoOperacao().equals("")) {
                throw new ConsistirException("O campo TIPO DE OPERAÇÃO (Modalidade Vezes Semana) deve ser informado.");
            }
            if (validarValor && obj.getPercentualDesconto().doubleValue() == 0) {
                throw new ConsistirException("O campo PERCENTUAL (Modalidade Vezes Semana) deve ser informado.");
            }

        }
        if (obj.getTipoValor().equals("VE")) {
            //para melhorar a usabilidade, ao inves de não deixar o sistema gravar o plano inteiro por causa de dado zerado,
            // vamos tratar aqui os tipos de operação quando os valores estiverem zerados
            if(!validarValor && UteisValidacao.emptyNumber(obj.getValorEspecifico())){
                obj.setTipoValor("");
                obj.setTipoOperacao("");
            } else if (obj.getTipoOperacao() == null || obj.getTipoOperacao().equals("")) {
                throw new ConsistirException("O campo TIPO DE OPERAÇÃO (Modalidade Vezes Semana) deve ser informado.");
            }
            if (validarValor && UteisValidacao.emptyNumber(obj.getValorEspecifico())) {
                throw new ConsistirException("O campo VALOR (Modalidade Vezes Semana) deve ser informado.");
            }
        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setPlanoModalidade(new Integer(0));
        setNrVezes(new Integer(0));
        setVezeSemanaEscolhida(new Boolean(false));
        //setVezesSemana(new VezesSemanaVO());
        setPercentualDesconto(new Double(0));
        setValorEspecifico(new Double(0));
        setDesenharValorEspecificoVezesSemana(new Boolean(false));
        setDesenharValorDescontoVezesSemana(new Boolean(false));

    }

//    /**
//     * Retorna o objeto da classe <code>VezesSemana</code> relacionado com (<code>PlanoModalidadeVezesSemana</code>).
//     */
//    public VezesSemanaVO getVezesSemana() {
//        if (vezesSemana == null) {
//            vezesSemana = new VezesSemanaVO();
//        }
//        return (vezesSemana);
//    }
//
//    /**
//     * Define o objeto da classe <code>VezesSemana</code> relacionado com (<code>PlanoModalidadeVezesSemana</code>).
//     */
//    public void setVezesSemana(VezesSemanaVO obj) {
//        this.vezesSemana = obj;
//    }
    public Integer getNrVezes() {
        return (nrVezes);
    }

    public void setNrVezes(Integer nrVezes) {
        this.nrVezes = nrVezes;
    }

    public Boolean getVezeSemanaEscolhida() {
        return vezeSemanaEscolhida;
    }

    public void setVezeSemanaEscolhida(Boolean vezeSemanaEscolhida) {
        this.vezeSemanaEscolhida = vezeSemanaEscolhida;
    }

    public Integer getPlanoModalidade() {
        return (planoModalidade);
    }

    public void setPlanoModalidade(Integer planoModalidade) {
        this.planoModalidade = planoModalidade;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorEspecifico() {
        return valorEspecifico;
    }

    public void setValorEspecifico(Double valorEspecifico) {
        this.valorEspecifico = valorEspecifico;
    }

    public Double getPercentualDesconto() {
        return (percentualDesconto);
    }

    public void setPercentualDesconto(Double percentualDesconto) {
        this.percentualDesconto = percentualDesconto;
    }

    public String getTipoValor() {
        if (tipoValor == null) {
            tipoValor = "";
        }
        return tipoValor;
    }

    public void setTipoValor(String tipoValor) {
        this.tipoValor = tipoValor;
    }

    public String getTipoOperacao() {
        if (tipoOperacao == null) {
            tipoOperacao = "";
        }
        return tipoOperacao;
    }

    public String getTipoOperacao_Apresentar() {
        if (tipoOperacao == null || tipoOperacao.equals("")) {
            return "";
        }
        if (tipoOperacao.equals("AC")) {
            return "Acrescimo";
        }
        if (tipoOperacao.equals("RE")) {
            return "Redução";
        }
        if (tipoOperacao.equals("EX")) {
            return "Exatamente";
        }

        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public Boolean getDesenharValorDescontoVezesSemana() {
        return desenharValorDescontoVezesSemana;
    }

    public void setDesenharValorDescontoVezesSemana(Boolean desenharValorDescontoVezesSemana) {
        this.desenharValorDescontoVezesSemana = desenharValorDescontoVezesSemana;
    }

    public Boolean getDesenharValorEspecificoVezesSemana() {
        return desenharValorEspecificoVezesSemana;
    }

    public void setDesenharValorEspecificoVezesSemana(Boolean desenharValorEspecificoVezesSemana) {
        this.desenharValorEspecificoVezesSemana = desenharValorEspecificoVezesSemana;
    }

    public void desenhaTipoValor() {
        if (getTipoValor() == null || getTipoValor().equals("")) {
            setTipoValor("");
            setPercentualDesconto(new Double(0));
            setValorEspecifico(new Double(0));
            setDesenharValorEspecificoVezesSemana(false);
            setDesenharValorDescontoVezesSemana(false);
        }

        if (getTipoValor().equals("VE")) {
            setPercentualDesconto(new Double(0));
            setValorEspecifico(new Double(0));
            setDesenharValorDescontoVezesSemana(false);
            setDesenharValorEspecificoVezesSemana(true);
        }

        if (getTipoValor().equals("PD")) {
            setValorEspecifico(new Double(0));
            setPercentualDesconto(new Double(0));
            setDesenharValorEspecificoVezesSemana(false);
            setDesenharValorDescontoVezesSemana(true);

        }
    }

    public boolean getApresentarValorEspecifico() {
        if (getTipoValor() == null || getTipoValor().equals("")) {
            return false;
        } else if (getTipoValor().equals("VE")) {
            return true;
        }
        return false;
    }

    public boolean getApresentarValorDesconto() {
        if (getTipoValor() == null || getTipoValor().equals("")) {
            return false;
        }
        if (getTipoValor().equals("PD")) {
            return true;
        }
        return false;
    }

    public boolean isReferencia() {
        return referencia;
    }

    public void setReferencia(boolean referencia) {
        this.referencia = referencia;
    }

    public String getReferenciaApresentar() {
        if (referencia) {
            return "Sim";
        }
        return "Não";
    }

    public Double getValorReferencia() {
        return valorReferencia;
    }

    public void setValorReferencia(Double valorReferencia) {
        this.valorReferencia = valorReferencia;
    }

    public ReferenciaValorModalidadeEnum getOrigem() {
        return origem;
    }

    public void setOrigem(ReferenciaValorModalidadeEnum origem) {
        this.origem = origem;
    }

}

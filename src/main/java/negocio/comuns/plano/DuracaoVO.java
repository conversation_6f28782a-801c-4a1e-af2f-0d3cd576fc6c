package negocio.comuns.plano;

import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade Duracao. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class DuracaoVO extends SuperVO {

    protected Integer codigo;
    protected Integer numeroMeses;  
    protected Boolean duracaoDefault;
    protected Boolean duracaoEscolhida;

    /**
     * Construtor padrão da classe <code>Duracao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public DuracaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>DuracaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(DuracaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getNumeroMeses().intValue() == 0) {
            throw new ConsistirException("O campo NÚMERO DE MESES (Duraçõe) deve ser informado.");
        }        
      
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setNumeroMeses(new Integer(0));      
        setDuracaoDefault(new Boolean(false));
        setDuracaoEscolhida(new Boolean(false));
    }

    public Boolean getDuracaoDefault() {
        return (duracaoDefault);
    }

    public Boolean isDuracaoDefault() {
        return (duracaoDefault);
    }
   

    public void setDuracaoDefault(Boolean duracaoDefault) {
        this.duracaoDefault = duracaoDefault;
    }
   
    public Integer getNumeroMeses() {
        return (numeroMeses);
    }

    public void setNumeroMeses(Integer numeroMeses) {
        this.numeroMeses = numeroMeses;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getDuracaoEscolhida() {
        return duracaoEscolhida;
    }

    public void setDuracaoEscolhida(Boolean duracaoEscolhida) {
        this.duracaoEscolhida = duracaoEscolhida;
    }
    
}

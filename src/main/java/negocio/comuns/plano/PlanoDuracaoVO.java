package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.facade.jdbc.plano.Plano;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Reponsável por manter os dados da entidade PlanoDuracao. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 * @see Plano
 */
public class PlanoDuracaoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer plano;
    protected Integer nrMaximoParcelasCondPagamento;
    protected Integer numeroMeses;
    protected Integer carencia;
    @NaoControlarLogAlteracao
    protected Boolean duracaoEscolhida;
    protected String tipoValor;
    protected String tipoOperacao;
    protected Double percentualDesconto;
    protected Double valorEspecifico;
    @NaoControlarLogAlteracao
    protected Double valorDesejadoMensal;
    @NaoControlarLogAlteracao
    protected Double valorDesejadoParcela;
    @NaoControlarLogAlteracao
    protected Double valorDesejado;
    @NaoControlarLogAlteracao
    protected Double valorMaximoDescontoPlano;
    @NaoControlarLogAlteracao
    protected Boolean desenharValorEspecificoDuracao;
    @NaoControlarLogAlteracao
    protected Boolean desenharValorDescontoDuracao;
    @Lista
    @ListJson(clazz = PlanoCondicaoPagamentoVO.class)
    protected List<PlanoCondicaoPagamentoVO> planoCondicaoPagamentoVOs;

    @Lista
    @ListJson(clazz = PlanoDuracaoCreditoTreinoVO.class)
    private List<PlanoDuracaoCreditoTreinoVO> listaPlanoDuracaoCreditoTreino;

    private Integer quantidadeDiasExtra = 0;
    
    private Integer pontos;
    
    private Boolean situacao = Boolean.TRUE;
    private Double valorFinalContrato;
    @NaoControlarLogAlteracao
    private boolean duracaoPlanoRecorrencia=false; // atributo trasient
    /**
     * Construtor padrão da classe <code>PlanoDuracao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public PlanoDuracaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PlanoDuracaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(PlanoDuracaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getNumeroMeses() == null || obj.getNumeroMeses() == 0) {
            throw new ConsistirException("O campo NÚMERO DE MESES (Durações) deve ser informado.", "dadosDuracao");
        }
        if (obj.getNrMaximoParcelasCondPagamento() == null || obj.getNrMaximoParcelasCondPagamento() == 0) {
            throw new ConsistirException("O campo NÚMERO MÁXIMO DE PARCELAS DA CONDIÇÃO DE PAGAMENTO (Durações) deve ser informado.", "dadosDuracao");
        }
        if (obj.getTipoValor() == null || obj.getTipoValor().equals("")) {
            throw new ConsistirException("O campo FORMA DE CÁLCULO (Durações) deve ser informado.", "dadosDuracao");
        }
        if (obj.getValorDesejado() != 0 && (obj.getTipoOperacao() == null || obj.getTipoOperacao().equals(""))) {
            throw new ConsistirException("O campo TIPO OPERAÇÃO (Durações) deve ser informado.", "dadosDuracao");
        }
//        if (obj.getCarencia().intValue() != 0  && obj.getCarencia() == null) {
//            throw new ConsistirException("O campo CARÊNCIA (Configurações Gerais) deve ser informado.");
//        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setNumeroMeses(0);
        setDuracaoEscolhida(false);
        setDesenharValorEspecificoDuracao(false);
        setDesenharValorDescontoDuracao(false);
        setNrMaximoParcelasCondPagamento(0);
        setPercentualDesconto(0.0);
        setValorEspecifico(0.0);
        setValorDesejado(0.0);
        setValorDesejadoMensal(0.0);
        setValorDesejadoParcela(0.0);
        setTipoValor("");
        setTipoOperacao("");
        setPlanoCondicaoPagamentoVOs(new ArrayList<PlanoCondicaoPagamentoVO>());
        setCarencia(0);
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>PlanoCondicaoPagamentoVO</code>
     * ao List <code>planoCondicaoPagamentoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>PlanoCondicaoPagamento</code> - getCodigo() - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>PlanoCondicaoPagamentoVO</code> que será adiocionado ao Hashtable correspondente.
     * @throws java.lang.Exception
     */
    public void adicionarObjPlanoCondicaoPagamentoVOs(PlanoCondicaoPagamentoVO obj) throws Exception {
        PlanoCondicaoPagamentoVO.validarDados(obj);
        int index = 0;
        for (PlanoCondicaoPagamentoVO objExistente : getPlanoCondicaoPagamentoVOs()) {
            if (objExistente.getCondicaoPagamento().getCodigo().equals(obj.getCondicaoPagamento().getCodigo())) {
                getPlanoCondicaoPagamentoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getPlanoCondicaoPagamentoVOs().add(obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>PlanoCondicaoPagamentoVO</code>
     * no List <code>planoCondicaoPagamentoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>PlanoCondicaoPagamento</code> - getCodigo() - como identificador (key) do objeto no List.
     *
     * @param codigo Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjPlanoCondicaoPagamentoVOs(PlanoCondicaoPagamentoVO obj) throws Exception {
        int index = 0;
        Iterator i = getPlanoCondicaoPagamentoVOs().iterator();
        while (i.hasNext()) {
            PlanoCondicaoPagamentoVO objExistente = (PlanoCondicaoPagamentoVO) i.next();
            if (objExistente.getCondicaoPagamento().getCodigo().equals(obj.getCondicaoPagamento().getCodigo())) {
                getPlanoCondicaoPagamentoVOs().remove(index);
                return;
            }
            index++;
        }
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>PlanoCondicaoPagamentoVO</code>
     * no List <code>planoCondicaoPagamentoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>PlanoCondicaoPagamento</code> - getCodigo() - como identificador (key) do objeto no List.
     *
     * @param codigo Parâmetro para localizar o objeto do List.
     */
    public PlanoCondicaoPagamentoVO consultarObjPlanoCondicaoPagamentoVO(Integer codigo) throws Exception {
        Iterator i = getPlanoCondicaoPagamentoVOs().iterator();
        while (i.hasNext()) {
            PlanoCondicaoPagamentoVO objExistente = (PlanoCondicaoPagamentoVO) i.next();
            if (objExistente.getCodigo().equals(codigo)) {
                return objExistente;
            }
        }
        return null;
        //consultarObjSubordinadoOC
    }


    public Integer getNumeroMeses() {
        return numeroMeses;
    }

    public void setNumeroMeses(Integer numeroMeses) {
        this.numeroMeses = numeroMeses;
    }

    public String getNumeroMeses_Apresentar() {
        if (numeroMeses == 1) {
            return "01 mês";
        } else {
            if (numeroMeses < 10) {
                return "0" + numeroMeses + " meses";
            } else {
                return numeroMeses + " meses";
            }
        }
    }

    public Boolean getDuracaoEscolhida() {
        return duracaoEscolhida;
    }

    public void setDuracaoEscolhida(Boolean duracaoEscolhida) {
        this.duracaoEscolhida = duracaoEscolhida;
    }

    public Integer getNrMaximoParcelasCondPagamento() {
        return (nrMaximoParcelasCondPagamento);
    }

    public void setNrMaximoParcelasCondPagamento(Integer nrMaximoParcelasCondPagamento) {
        this.nrMaximoParcelasCondPagamento = nrMaximoParcelasCondPagamento;
    }

    public Integer getPlano() {
        return (plano);
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorEspecifico() {
        return (valorEspecifico);
    }

    public void setValorEspecifico(Double valorEspecifico) {
        this.valorEspecifico = valorEspecifico;
    }

    public Double getValorTotal() {
        return (getValorDesejadoMensal() * getNumeroMeses());
    }

    public Double getPercentualDesconto() {
        return (percentualDesconto);
    }

    public void setPercentualDesconto(Double percentualDesconto) {
        this.percentualDesconto = percentualDesconto;
    }

    public String getTipoValor() {
        return tipoValor;
    }

    public void setTipoValor(String tipoValor) {
        this.tipoValor = tipoValor;
    }

    public String getTipoOperacao() {
        if(tipoOperacao == null){
            tipoOperacao = "";
        }
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getTipoOperacao_Apresentar() {
        if (tipoOperacao == null || tipoOperacao.equals("")) {
            return ("");
        }
        if (getTipoOperacaoReducao()) {
            return ("Redução");
        } else if(!getTipoOperacaoReducao()) {
            return ("Acréscimo");
        }

        return tipoOperacao;
    }
    public boolean getTipoOperacaoReducao(){
        if(getTipoOperacao().equals("AC")){
            return false;
        }else if(getTipoOperacao().equals("RE")){
            return true;
        }
        return false;
    }

    public Boolean getDesenharValorDescontoDuracao() {
        return desenharValorDescontoDuracao;
    }

    public void setDesenharValorDescontoDuracao(Boolean desenharValorDescontoDuracao) {
        this.desenharValorDescontoDuracao = desenharValorDescontoDuracao;
    }

    public Boolean getDesenharValorEspecificoDuracao() {
        return desenharValorEspecificoDuracao;
    }

    public void setDesenharValorEspecificoDuracao(Boolean desenharValorEspecificoDuracao) {
        this.desenharValorEspecificoDuracao = desenharValorEspecificoDuracao;
    }

    public Double getValorDesejadoMensal() {
        return valorDesejadoMensal;
    }

    public void setValorDesejadoMensal(Double valorDesejadoMensal) {
        this.valorDesejadoMensal = valorDesejadoMensal;
    }

    public Double getValorDesejadoParcela() {
        return valorDesejadoParcela;
    }

    public void setValorDesejadoParcela(Double valorDesejadoParcela) {
        this.valorDesejadoParcela = valorDesejadoParcela;
    }

    public Double getValorDesejado() {
        return valorDesejado;
    }

    public Double getValorMaximoDescontoPlano() {
        if (valorMaximoDescontoPlano == null) {
            return 0D;
        }
        return valorMaximoDescontoPlano;
    }

    public void setValorMaximoDescontoPlano(Double valorMaximoDescontoPlano) {
        this.valorMaximoDescontoPlano = valorMaximoDescontoPlano;
    }

    public void setValorDesejado(Double valorDesejado) {
        this.valorDesejado = valorDesejado;
    }

    public List<PlanoCondicaoPagamentoVO> getPlanoCondicaoPagamentoVOs() {
        return planoCondicaoPagamentoVOs;
    }

    public void setPlanoCondicaoPagamentoVOs(List<PlanoCondicaoPagamentoVO> planoCondicaoPagamentoVOs) {
        this.planoCondicaoPagamentoVOs = planoCondicaoPagamentoVOs;
    }


    public void desenhaTipoValor() {
        if (getTipoValor() == null || getTipoValor().equals("")) {
            setTipoValor("");
            setPercentualDesconto(0.0);
            setValorEspecifico(0.0);
            setDesenharValorEspecificoDuracao(false);
            setDesenharValorDescontoDuracao(false);
        }
        if (getTipoValor().equals("VE")) {
            setPercentualDesconto(0.0);
            setDesenharValorDescontoDuracao(false);
            setDesenharValorEspecificoDuracao(true);
        }
        if (getTipoValor().equals("PD")) {
            setValorEspecifico(0.0);
            setDesenharValorEspecificoDuracao(false);
            setDesenharValorDescontoDuracao(true);

        }
    }

    public boolean getApresentarValorEspecifico() {
        return !(getTipoValor() == null || getTipoValor().equals("")) && getTipoValor().equals("VE");
    }

    public boolean getApresentarValorDesconto() {
        return !(getTipoValor() == null || getTipoValor().equals("")) && getTipoValor().equals("PD");
    }

    public Integer getCarencia() {
        return carencia;
    }

    public void setCarencia(Integer carencia) {
        this.carencia = carencia;
    }

    public void adicionarDuracaoCreditoTreino(PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO)throws Exception{
        planoDuracaoCreditoTreinoVO.validarDados();
        if (!this.listaPlanoDuracaoCreditoTreino.contains(planoDuracaoCreditoTreinoVO)){
            this.listaPlanoDuracaoCreditoTreino.add(planoDuracaoCreditoTreinoVO);
        }
    }

    public List<PlanoDuracaoCreditoTreinoVO> getListaPlanoDuracaoCreditoTreino() {
        return listaPlanoDuracaoCreditoTreino;
    }

    public void setListaPlanoDuracaoCreditoTreino(List<PlanoDuracaoCreditoTreinoVO> listaPlanoDuracaoCreditoTreino) {
        this.listaPlanoDuracaoCreditoTreino = listaPlanoDuracaoCreditoTreino;
    }

    public Integer getQuantidadeDiasExtra() {
        if(quantidadeDiasExtra == null){
            quantidadeDiasExtra = 0;
        }
        return quantidadeDiasExtra;
    }

    public void setQuantidadeDiasExtra(Integer quantidadeDiasExtra) {
        this.quantidadeDiasExtra = quantidadeDiasExtra;
    }

    public Integer getTotalDias(){
        return (this.numeroMeses * 30) + (UteisValidacao.emptyNumber(this.quantidadeDiasExtra) ? 0 : this.quantidadeDiasExtra);
    }

    public String getDescricaoDuracao(){
        if (this.quantidadeDiasExtra > 0){
            return getTotalDias() + " Dias";
        }
        if (this.numeroMeses == 1)
          return "1 Mês";
        else
          return this.numeroMeses + " Meses";
    }

    public Integer getPontos() {
        if (pontos == null) {
            pontos = 0;
        }
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Boolean getSituacao() {
        return situacao;
    }

    public void setSituacao(Boolean situacao) {
        this.situacao = situacao;
    }
    
    public String getSituacao_Apresentar(){
        if(situacao){
            return "Ativa";
        } else {
            return "Desativada";
        }
    }

    public Double getValorFinalContrato() {
        if (valorFinalContrato == null) {
            valorFinalContrato = 0.0;
}
        return valorFinalContrato;
    }

    public void setValorFinalContrato(Double valorFinalContrato) {
        this.valorFinalContrato = valorFinalContrato;
    }

    public boolean isDuracaoPlanoRecorrencia() {
        return duracaoPlanoRecorrencia;
    }

    public void setDuracaoPlanoRecorrencia(boolean duracaoPlanoRecorrencia) {
        this.duracaoPlanoRecorrencia = duracaoPlanoRecorrencia;
    }
}

package negocio.comuns.plano;

import annotations.arquitetura.FKJson;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * Reponsável por manter os dados da entidade ComposicaoModalidade. Classe do
 * tipo VO - Value Object composta pelos atributos da entidade com visibilidade
 * protegida e os métodos de acesso a estes atributos. Classe utilizada para
 * apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 * @see ComposicaoVO
 */
public class ComposicaoModalidadeVO extends SuperVO {

    protected Integer codigo;
    protected Integer composicao;
    protected Double precoModalidade;
    protected Double valorMensalComposicao;
    private Integer nrVezes = 0;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Modalidade </code>.
     */
    @FKJson
    protected ModalidadeVO modalidade;

    /**
     * Construtor padrão da classe <code>ComposicaoModalidade</code>. Cria uma
     * nova instância desta entidade, inicializando automaticamente seus
     * atributos (Classe VO).
     */
    public ComposicaoModalidadeVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ComposicaoModalidadeVO</code>. Todos os tipos de consistência de
     * dados são e devem ser implementadas neste método. São validações típicas:
     * verificação de campos obrigatórios, verificação de valores válidos para
     * os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDados(ComposicaoModalidadeVO obj, boolean validarValor) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getModalidade() == null) || (obj.getModalidade().getCodigo() == 0)) {
            throw new ConsistirException("O campo MODALIDADE (Composição de Modalidade) deve ser informado.");
        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setPrecoModalidade(0.0);
        setValorMensalComposicao(0.0);
        setModalidade(new ModalidadeVO());
    }

    public ModalidadeVO getModalidade() {
        if (modalidade == null) {
            modalidade = new ModalidadeVO();
        }
        return (modalidade);
    }

    public void setModalidade(ModalidadeVO obj) {
        this.modalidade = obj;
    }

    public Double getValorMensalComposicao() {
        return (valorMensalComposicao);
    }

    public void setValorMensalComposicao(Double valorMensalComposicao) {
        this.valorMensalComposicao = valorMensalComposicao;
    }

    public Double getPrecoModalidade() {
        return (precoModalidade);
    }

    public void setPrecoModalidade(Double precoModalidade) {
        this.precoModalidade = precoModalidade;
    }

    public Integer getComposicao() {
        return (composicao);
    }

    public void setComposicao(Integer composicao) {
        this.composicao = composicao;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getNrVezes() {
        return nrVezes;
    }

    public void setNrVezes(Integer nrVezes) {
        this.nrVezes = nrVezes;
    }
}

package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.estudio.util.Validador;

/**
 * Reponsável por manter os dados da entidade Ambiente. Classe do tipo VO -
 * Value Object composta pelos atributos da entidade com visibilidade protegida
 * e os métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class AmbienteVO extends SuperVO implements Cloneable {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected Integer tipoAmbiente;
    protected Boolean selecionado;
    private Integer situacaoAmbiente;
    protected Integer capacidade = 0;
    private String tipoModulo = "";
    private Boolean ambienteEscolhido;
    private Integer coletor;
    private String codigoPiscinaMgb;

    public Integer getColetor() {
        return coletor;
    }

    public void setColetor(Integer coletor) {
        this.coletor = coletor;
    }

    /**
     * @return the tipoAmbiente
     */
    public Integer getTipoAmbiente() {
        return tipoAmbiente;
    }

    /**
     * @param tipoAmbiente the tipoAmbiente to set
     */
    public void setTipoAmbiente(Integer tipoAmbiente) {
        this.tipoAmbiente = tipoAmbiente;
    }
    protected Integer capacidadeMaximaConvidados;

    /**
     * Construtor padrão da classe
     * <code>Ambiente</code>. Cria uma nova instância desta entidade,
     * inicializando automaticamente seus atributos (Classe VO).
     */
    public AmbienteVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>AmbienteVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @exception ConsistirException Se uma inconsistência for encontrada

     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro

     * ocorrido.
     */
    public static void validarDados(AmbienteVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Ambiente) deve ser informado.");
        }
        if (obj.getCapacidade() != -1) {
            if (!Validador.isValidaInteger(obj.getCapacidade())) {
                throw new ConsistirException("O campo CAPACIDADE (Ambiente) deve ser informado.");
            }
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Ambiente) deve ser informado.");
        }
        if (obj.getTipoModulo() == null || obj.getTipoModulo().equals("")) {
            throw new ConsistirException("O campo TIPO AMBIENTE (Ambiente) deve ser informado.");
        }
        if (obj.getSituacaoAmbiente() == null) {
            throw new ConsistirException("O campo SITUAÇÃO DO AMBIENTE (Ambiente) deve ser informado.");
        }
    }

    public static void validarDadosCE(AmbienteVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Ambiente) deve ser informado.");
        }

        if (!Validador.isValidaInteger(obj.getTipoAmbiente())) {
            throw new ConsistirException("O campo TIPO AMBIENTE (Ambiente) deve ser informado.");
        }

        if (!Validador.isValidaInteger(obj.getSituacaoAmbiente())) {
            throw new ConsistirException("O campo SITUAÇÃO (Ambiente) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setDescricao("");
        setTipoAmbiente(0);
        setCapacidadeMaximaConvidados(0);
    }

    @Override
    public AmbienteVO clone() throws CloneNotSupportedException {
        return (AmbienteVO) super.clone();
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return O campo capacidadeMaximaConvidados.
     */
    public Integer getCapacidadeMaximaConvidados() {
        return capacidadeMaximaConvidados;
    }

    /**
     * @param capacidadeMaximaConvidados O novo valor de
     * capacidadeMaximaConvidados.
     */
    public void setCapacidadeMaximaConvidados(Integer capacidadeMaximaConvidados) {
        this.capacidadeMaximaConvidados = capacidadeMaximaConvidados;
    }

    /**
     * @return O campo situacaoAmbiente.
     */
    public Integer getSituacaoAmbiente() {
        if (situacaoAmbiente == null) {
            situacaoAmbiente = 0;
        }
        return this.situacaoAmbiente;
    }

    /**
     * @param situacaoAmbiente O novo valor de situacaoAmbiente.
     */
    public void setSituacaoAmbiente(Integer situacaoAmbiente) {
        this.situacaoAmbiente = situacaoAmbiente;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AmbienteVO other = (AmbienteVO) obj;
        if (this.codigo != other.codigo && (this.codigo == null || !this.codigo.equals(other.codigo))) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        return hash;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    /**
     * @return the tipoModulo
     */
    public String getTipoModulo() {
        return tipoModulo;
    }

    /**
     * @param tipoModulo the tipoModulo to set
     */
    public void setTipoModulo(String tipoModulo) {
        this.tipoModulo = tipoModulo;
    }

    public String getSituacao() {
        if (this.getSelecionado()) {
            return "Ativo";
        } else {
            return "Inativo";
        }
    }

    public Boolean getAmbienteEscolhido() {
        return ambienteEscolhido;
    }

    public void setAmbienteEscolhido(Boolean ambienteEscolhido) {
        this.ambienteEscolhido = ambienteEscolhido;
    }

    public String getCodigoPiscinaMgb() {
        return codigoPiscinaMgb;
    }

    public void setCodigoPiscinaMgb(String codigoPiscinaMgb) {
        this.codigoPiscinaMgb = codigoPiscinaMgb;
    }

}

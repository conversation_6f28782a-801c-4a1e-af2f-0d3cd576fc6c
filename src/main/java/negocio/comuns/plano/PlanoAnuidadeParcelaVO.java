package negocio.comuns.plano;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;

public class PlanoAnuidadeParcelaVO extends SuperVO {

    private Integer codigo;
    private Integer planoRecorrencia;
    private Integer numero;
    private Double valor;
    private Integer parcela;
    private Double valorDesconto;

    public PlanoAnuidadeParcelaVO() {
    }

    public PlanoAnuidadeParcelaVO(Integer numero, Double valor, Integer parcela) {
        setNumero(numero);
        setValor(valor);
        setParcela(parcela);
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }


    public Integer getNumero() {
        if (numero == null) {
            numero = 0;
        }
        return numero;
    }

    public void setNumero(Integer numero) {
        this.numero = numero;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getParcela() {
        if (parcela == null) {
            parcela = 0;
        }
        return parcela;
    }

    public void setParcela(Integer parcela) {
        this.parcela = parcela;
    }

    public String getParcelaApresentar() {
        return "No mesmo dia da PARCELA " + getParcela();
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetario(getValor());
    }

    public Integer getPlanoRecorrencia() {
        if (planoRecorrencia == null) {
            planoRecorrencia = 0;
        }
        return planoRecorrencia;
    }

    public void setPlanoRecorrencia(Integer planoRecorrencia) {
        this.planoRecorrencia = planoRecorrencia;
    }

    public PlanoAnuidadeParcelaWS toWS(){
        return new PlanoAnuidadeParcelaWS(this);
    }

    public Double getValorDesconto() {
        if (valorDesconto == null) {
            valorDesconto = 0.0;
        }
        return valorDesconto;
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public Double getValorTotal(){
        return getValor() - getValorDesconto();
    }
}

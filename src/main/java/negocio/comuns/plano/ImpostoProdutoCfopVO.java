package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;


public class ImpostoProdutoCfopVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String cfop;
    protected String ncm;
    @ChaveEstrangeira
    protected EmpresaVO empresa = new EmpresaVO();

    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFSe;
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFCe;

    private String codigoListaServico;
    private String codigoTributacaoMunicipio;
    private String descricaoServicoMunicipio;

    private boolean enviarPercentualImposto = true;
    private Double percentualFederal;
    private Double percentualEstadual;
    private Double percentualMunicipal;

    private String situacaoTributariaISSQN;
    private double aliquotaISSQN = 0.0;

    private String situacaoTributariaPIS;
    private double aliquotaPIS = 0.0;
    private boolean isentoPIS = false;
    private boolean enviaAliquotaNFePIS = false;

    private String situacaoTributariaCOFINS;
    private double aliquotaCOFINS = 0.0;
    private boolean isentoCOFINS = false;

    private boolean enviaAliquotaNFeCOFINS = false;
    private boolean isentoICMS = false;
    private String situacaoTributariaICMS;

    private double aliquotaICMS = 0.0;
    private boolean enviaAliquotaNFeICMS = false;

    private String codigoBeneficioFiscal;

    protected Boolean desativado;


    public ImpostoProdutoCfopVO() {
        super();
        //inicializarDados();
    }


    public static void validarDados(ProdutoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getCfop()  ==null || obj.getCfop().equals("")) {
            throw new ConsistirException("O campo CFOP  deve ser informado.");
        }
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCfop() {
        return cfop;
    }

    public void setCfop(String cfop) {
        this.cfop = cfop;
    }

    public String getNcm() {
        return ncm;
    }

    public void setNcm(String ncm) {
        this.ncm = ncm;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalNFSe() {
        return configuracaoNotaFiscalNFSe;
    }

    public void setConfiguracaoNotaFiscalNFSe(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFSe) {
        this.configuracaoNotaFiscalNFSe = configuracaoNotaFiscalNFSe;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalNFCe() {
        return configuracaoNotaFiscalNFCe;
    }

    public void setConfiguracaoNotaFiscalNFCe(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalNFCe) {
        this.configuracaoNotaFiscalNFCe = configuracaoNotaFiscalNFCe;
    }

    public String getCodigoListaServico() {
        return codigoListaServico;
    }

    public void setCodigoListaServico(String codigoListaServico) {
        this.codigoListaServico = codigoListaServico;
    }

    public String getCodigoTributacaoMunicipio() {
        return codigoTributacaoMunicipio;
    }

    public void setCodigoTributacaoMunicipio(String codigoTributacaoMunicipio) {
        this.codigoTributacaoMunicipio = codigoTributacaoMunicipio;
    }

    public String getDescricaoServicoMunicipio() {
        return descricaoServicoMunicipio;
    }

    public void setDescricaoServicoMunicipio(String descricaoServicoMunicipio) {
        this.descricaoServicoMunicipio = descricaoServicoMunicipio;
    }

    public boolean isEnviarPercentualImposto() {
        return enviarPercentualImposto;
    }

    public void setEnviarPercentualImposto(boolean enviarPercentualImposto) {
        this.enviarPercentualImposto = enviarPercentualImposto;
    }

    public Double getPercentualFederal() {
        return percentualFederal;
    }

    public void setPercentualFederal(Double percentualFederal) {
        this.percentualFederal = percentualFederal;
    }

    public Double getPercentualEstadual() {
        return percentualEstadual;
    }

    public void setPercentualEstadual(Double percentualEstadual) {
        this.percentualEstadual = percentualEstadual;
    }

    public Double getPercentualMunicipal() {
        return percentualMunicipal;
    }

    public void setPercentualMunicipal(Double percentualMunicipal) {
        this.percentualMunicipal = percentualMunicipal;
    }

    public String getSituacaoTributariaISSQN() {
        return situacaoTributariaISSQN;
    }

    public void setSituacaoTributariaISSQN(String situacaoTributariaISSQN) {
        this.situacaoTributariaISSQN = situacaoTributariaISSQN;
    }

    public double getAliquotaISSQN() {
        return aliquotaISSQN;
    }

    public void setAliquotaISSQN(double aliquotaISSQN) {
        this.aliquotaISSQN = aliquotaISSQN;
    }

    public String getSituacaoTributariaPIS() {
        return situacaoTributariaPIS;
    }

    public void setSituacaoTributariaPIS(String situacaoTributariaPIS) {
        this.situacaoTributariaPIS = situacaoTributariaPIS;
    }

    public double getAliquotaPIS() {
        return aliquotaPIS;
    }

    public void setAliquotaPIS(double aliquotaPIS) {
        this.aliquotaPIS = aliquotaPIS;
    }

    public boolean isIsentoPIS() {
        return isentoPIS;
    }

    public void setIsentoPIS(boolean isentoPIS) {
        this.isentoPIS = isentoPIS;
    }

    public boolean isEnviaAliquotaNFePIS() {
        return enviaAliquotaNFePIS;
    }

    public void setEnviaAliquotaNFePIS(boolean enviaAliquotaNFePIS) {
        this.enviaAliquotaNFePIS = enviaAliquotaNFePIS;
    }

    public String getSituacaoTributariaCOFINS() {
        return situacaoTributariaCOFINS;
    }

    public void setSituacaoTributariaCOFINS(String situacaoTributariaCOFINS) {
        this.situacaoTributariaCOFINS = situacaoTributariaCOFINS;
    }

    public double getAliquotaCOFINS() {
        return aliquotaCOFINS;
    }

    public void setAliquotaCOFINS(double aliquotaCOFINS) {
        this.aliquotaCOFINS = aliquotaCOFINS;
    }

    public boolean isIsentoCOFINS() {
        return isentoCOFINS;
    }

    public void setIsentoCOFINS(boolean isentoCOFINS) {
        this.isentoCOFINS = isentoCOFINS;
    }

    public boolean isEnviaAliquotaNFeCOFINS() {
        return enviaAliquotaNFeCOFINS;
    }

    public void setEnviaAliquotaNFeCOFINS(boolean enviaAliquotaNFeCOFINS) {
        this.enviaAliquotaNFeCOFINS = enviaAliquotaNFeCOFINS;
    }

    public boolean isIsentoICMS() {
        return isentoICMS;
    }

    public void setIsentoICMS(boolean isentoICMS) {
        this.isentoICMS = isentoICMS;
    }

    public String getSituacaoTributariaICMS() {
        return situacaoTributariaICMS;
    }

    public void setSituacaoTributariaICMS(String situacaoTributariaICMS) {
        this.situacaoTributariaICMS = situacaoTributariaICMS;
    }

    public double getAliquotaICMS() {
        return aliquotaICMS;
    }

    public void setAliquotaICMS(double aliquotaICMS) {
        this.aliquotaICMS = aliquotaICMS;
    }

    public boolean isEnviaAliquotaNFeICMS() {
        return enviaAliquotaNFeICMS;
    }

    public void setEnviaAliquotaNFeICMS(boolean enviaAliquotaNFeICMS) {
        this.enviaAliquotaNFeICMS = enviaAliquotaNFeICMS;
    }

    public String getCodigoBeneficioFiscal() {
        return codigoBeneficioFiscal;
    }

    public void setCodigoBeneficioFiscal(String codigoBeneficioFiscal) {
        this.codigoBeneficioFiscal = codigoBeneficioFiscal;
    }

    public Boolean getDesativado() {
        return desativado;
    }

    public void setDesativado(Boolean desativado) {
        this.desativado = desativado;
    }
}

package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade PlanoModalidade. Classe do tipo VO
 * - Value Object composta pelos atributos da entidade com visibilidade
 * protegida e os métodos de acesso a estes atributos. Classe utilizada para
 * apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 * @see PlanoVO
 */
public class PlanoModalidadeVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer plano;
    /**
     * Atributo responsável por manter o objeto relacionado da classe <code>Modalidade
     * </code>.
     */
    //@ChaveEstrangeira
    @ChaveEstrangeira
    @FKJson
    protected ModalidadeVO modalidade;
    @NaoControlarLogAlteracao
    protected String listaVezesSemana;
    @Lista
    @ListJson(clazz = PlanoModalidadeVezesSemanaVO.class)
    protected List planoModalidadeVezesSemanaVOs;

    /**
     * Construtor padrão da classe <code>PlanoModalidade</code>. Cria uma nova
     * instância desta entidade, inicializando automaticamente seus atributos
     * (Classe VO).
     */
    public PlanoModalidadeVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>PlanoModalidadeVO</code>. Todos os tipos de consistência de dados
     * são e devem ser implementadas neste método. São validações típicas:
     * verificação de campos obrigatórios, verificação de valores válidos para
     * os atributos.
     *
     * @exception ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDados(PlanoModalidadeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getModalidade() == null) || (obj.getModalidade().getCodigo().equals(0))) {
            throw new ConsistirException("O campo MODALIDADE (Plano Modalidade) deve ser informado.");
        }
        if ((obj.getPlanoModalidadeVezesSemanaVOs().isEmpty())) {
            throw new ConsistirException("A MODALIDADE ( " + obj.getModalidade().getNome() + " ) deve ter no MÍNIMO uma VEZ POR SEMANA CADASTRADA. (Aba Modalidade)");
        }
        List<PlanoModalidadeVezesSemanaVO> vezesSemana = obj.getPlanoModalidadeVezesSemanaVOs();
        for (PlanoModalidadeVezesSemanaVO vs : vezesSemana) {
            if (vs.getTipoOperacao().equals("EX") && !vs.getTipoValor().equals("VE")) {
                throw new ConsistirException(
                        String.format("Na Modalidade %s em %s vezes/semana para o tipo de operação EXATAMENTE a Forma de Cálculo deve ser por VALOR e não PORCENTAGEM",
                                obj.getModalidade().getNome(), vs.getNrVezes()));
            }
        }
    }

    public static void validarDadosEspecial(PlanoModalidadeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getModalidade() == null) || (obj.getModalidade().getCodigo().equals(0))) {
            throw new ConsistirException("O campo MODALIDADE (Plano Modalidade) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setListaVezesSemana("");
        setModalidade(new ModalidadeVO());
        setPlanoModalidadeVezesSemanaVOs(new ArrayList());
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>HorarioTurmaVO</code> ao List <code>horarioTurmaVOs</code>. Utiliza
     * o atributo padrão de consulta da classe <code>HorarioTurma</code> -
     * getIdentificador() - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>HorarioTurmaVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjPlanoModalidadeVezesSemanaVOs(PlanoModalidadeVezesSemanaVO obj) throws Exception {
        if (obj.getTipoOperacao().equals("EX")){
            obj.setTipoValor("VE");
        }
        PlanoModalidadeVezesSemanaVO.validarDados(obj, true);
        int index = 0;
        Iterator i = getPlanoModalidadeVezesSemanaVOs().iterator();
        while (i.hasNext()) {
            PlanoModalidadeVezesSemanaVO oe = (PlanoModalidadeVezesSemanaVO) i.next();
            if (oe.getNrVezes().equals(obj.getNrVezes().intValue())) {
                getPlanoModalidadeVezesSemanaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getPlanoModalidadeVezesSemanaVOs().add(obj);
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>HorarioTurmaVO</code> no List <code>horarioTurmaVOs</code>. Utiliza
     * o atributo padrão de consulta da classe <code>HorarioTurma</code> -
     * getIdentificador() - como identificador (key) do objeto no List.
     *
     * @param vezesSemana Parâmetro para localizar o objeto do List.
     */
    public PlanoModalidadeVezesSemanaVO consultarObjHorarioTurmaVO(Integer vezesSemana) throws Exception {
        Iterator i = getPlanoModalidadeVezesSemanaVOs().iterator();
        while (i.hasNext()) {
            PlanoModalidadeVezesSemanaVO objExistente = (PlanoModalidadeVezesSemanaVO) i.next();
            if (objExistente.getNrVezes().equals(vezesSemana)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Retorna o objeto da classe <code>Modalidade</code> relacionado com
     * (<code>PlanoModalidade</code>).
     */
    public ModalidadeVO getModalidade() {
        if (modalidade == null) {
            modalidade = new ModalidadeVO();
        }
        return (modalidade);
    }

    /**
     * Define o objeto da classe <code>Modalidade</code> relacionado com
     * (<code>PlanoModalidade</code>).
     */
    public void setModalidade(ModalidadeVO obj) {
        this.modalidade = obj;
    }

    public Integer getPlano() {
        return (plano);
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List getPlanoModalidadeVezesSemanaVOs() {
        return planoModalidadeVezesSemanaVOs;
    }
    public boolean isExisteUnicaVezSemana(){
        return getPlanoModalidadeVezesSemanaVOs().size() > 1 ? true : false;
    }

    public void setPlanoModalidadeVezesSemanaVOs(List planoModalidadeVezesSemanaVOs) {
        this.planoModalidadeVezesSemanaVOs = planoModalidadeVezesSemanaVOs;
    }

    public String getListaVezesSemana() {
        if (listaVezesSemana == null) {
            listaVezesSemana = "";
        }
        return listaVezesSemana;
    }

    public void setListaVezesSemana(String listaVezesSemana) {
        this.listaVezesSemana = listaVezesSemana;
    }

    public String getNomeModalidade_Apresentar() {
        return this.modalidade.getNome();
    }
    
    public PlanoModalidadeWS toWS() {
        PlanoModalidadeWS planoModalidadeWS = new PlanoModalidadeWS();
        planoModalidadeWS.setCodigo(this.getCodigo());
        planoModalidadeWS.setModalidade(this.getNomeModalidade_Apresentar());
        return planoModalidadeWS;
    }

    public void montarNrVezesSemana() {
        if (!this.getPlanoModalidadeVezesSemanaVOs().isEmpty()) {
            this.setListaVezesSemana("");
            Ordenacao.ordenarLista(this.getPlanoModalidadeVezesSemanaVOs(), "nrVezes");
            for (Iterator it = this.getPlanoModalidadeVezesSemanaVOs().iterator(); it.hasNext();) {
                String descricao = "";
                PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemana = (PlanoModalidadeVezesSemanaVO) it.next();
                Double valor;
                if (planoModalidadeVezesSemana.getTipoOperacao() == null) {
                    planoModalidadeVezesSemana.setTipoOperacao("");
                }
                switch (planoModalidadeVezesSemana.getTipoOperacao()) {
                    case "RE":
                        descricao = " Redução ";
                        if (planoModalidadeVezesSemana.getTipoValor().equals("PD")) {
                            valor = Uteis.arredondarForcando2CasasDecimais(planoModalidadeVezesSemana.getPercentualDesconto());
                            descricao += Formatador.formatarValorMonetarioSemMoeda(valor) + "%";
                        } else {
                            valor = Uteis.arredondarForcando2CasasDecimais(planoModalidadeVezesSemana.getValorEspecifico());
                            descricao += Formatador.formatarValorMonetario(valor);
                        }
                        break;
                    case "AC":
                        descricao = " Acréscimo ";
                        if (planoModalidadeVezesSemana.getTipoValor().equals("PD")) {
                            valor = Uteis.arredondarForcando2CasasDecimais(planoModalidadeVezesSemana.getPercentualDesconto());
                            descricao += Formatador.formatarValorMonetarioSemMoeda(valor) + "%";
                        } else {
                            valor = Uteis.arredondarForcando2CasasDecimais(planoModalidadeVezesSemana.getValorEspecifico());
                            descricao += Formatador.formatarValorMonetario(valor);
                        }
                        break;
                    case "EX":
                        descricao = " Exatamente " + Formatador.formatarValorMonetario(planoModalidadeVezesSemana.getValorEspecifico());
                        break;
                }
                this.setListaVezesSemana(String.format("%s %s Vezes%s,",
                        this.getListaVezesSemana(),
                        planoModalidadeVezesSemana.getNrVezes(),
                        descricao.isEmpty() ? "" : " - " + descricao));
            }
            if (this.getListaVezesSemana().lastIndexOf(",") == (this.getListaVezesSemana().length() - 1)) {
                this.setListaVezesSemana(this.getListaVezesSemana().substring(0, this.getListaVezesSemana().length() - 1));
            }
        }
    }

}

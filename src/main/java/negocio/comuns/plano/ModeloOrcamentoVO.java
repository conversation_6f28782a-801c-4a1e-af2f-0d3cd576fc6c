package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.util.*;

import static br.com.pactosolucoes.comuns.util.JSFUtilities.context;

public class ModeloOrcamentoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected String situacao;
    protected int codModalidade;
    protected int codPacote;
    protected Date dataDefinicao;
    @NaoControlarLogAlteracao
    protected String texto;
    /** Atributo respons?vel por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    @NaoControlarLogAlteracao
    @FKJson
    protected UsuarioVO responsavelDefinicao;
    @NaoControlarLogAlteracao
    protected List listaTagUtilizado;
    @NaoControlarLogAlteracao
    private transient byte imagemModelo[];
    protected String nomeImagem;
    String[] manha = {"01:","02:","03:","04:","05:","06:","07:","08:","09:","10:","11:","12:"};
    String[] tarde = {"12:","13:","14:","15:","16:","17:","18:"};
    String[] noite = {"18:","19:","20:","21:","22:","23:","00:"};

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    public void substituirTagsTextoEmBranco(OrcamentoVO orcamento, EmpresaVO emp, Connection con, boolean retornaTexto) throws Exception {
        try {
            HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
            String nomeEmp = Uteis.retirarAcentuacaoRegex(emp.getNome().replaceAll(" ", ""));
            if (getTexto().length() != 0) {
                char aspas = '"';
                String espaco = "<p style=" + aspas + "text-align: left;" + aspas + ">";
                String htmlBotao = "<form> ";
                String textoSub = getTexto();
                textoSub = textoSub.replace("Untitled document", "MODELO DE ORÇAMENTO");
                int posicaoBody = textoSub.indexOf("<body>");
                String parte1 = textoSub.substring(0, posicaoBody + 6);
                String parte2 = textoSub.substring(posicaoBody + 6);
                parte1 = parte1.replace("</head>", "<style>@media print {#imprimir {display: none;}}</style></br></head>");
                textoSub = textoSub.replace("</head>", "<link href="+aspas+"beta/css/font-awesome_2.0.min.css"+aspas+" type="+aspas+"text/css"+aspas+" rel="+aspas+"stylesheet"+aspas+"/>" +
                        "<style>@page{margin: 3px} #fundo {background:url("+aspas+"./imagens/"+nomeEmp+"_fundoOrcamento.jpg"+aspas+"); background-size:100% !important; -webkit-print-color-adjust: exact !important;}</style></head>");
                posicaoBody = textoSub.indexOf("</body>");
                parte1 = textoSub.substring(0, posicaoBody);
                parte2 = textoSub.substring(posicaoBody);
                textoSub = parte1 + espaco + "</form>" + parte2;
                textoSub = substituirPorTagsTurmaOrcamento(orcamento, textoSub, emp.getCodigo(), retornaTexto);

                textoSub = textoSub.replace("<body>", "<body><div id="+aspas+"fundo"+aspas+"><div style="+aspas+" width: 80% "+aspas+">");
                textoSub = textoSub.replace("</body>", "</div></div></body>");
                if(retornaTexto){
                    textoSub = textoSub.replace("img src="+aspas, "img src="+aspas+request.getRequestURL().toString().replace("cliente.jsp", ""));
                    setTexto(textoSub);
                }else{
                    request.getSession().setAttribute("textoRelatorio", textoSub);
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    private String substituirPorTagsTurmaOrcamento(OrcamentoVO orcamento, String textoSub, int codEmpresa, boolean retornaTexto) throws Exception {
        char aspas = '"';
        String nomeModalidade = "";
        List<TurmaVO> listTurmas = new ArrayList<>();
        if(getCodModalidade() == 0 && getCodPacote() != 0){
            ComposicaoVO pacote = getFacade().getComposicao().consultarPorChavePrimaria(getCodPacote(), Uteis.NIVELMONTARDADOS_TODOS);
            nomeModalidade = pacote.getDescricao();
            for(ComposicaoModalidadeVO modalidadePacote : pacote.getComposicaoModalidadeVOs()){
                List<TurmaVO> turmas = getFacade().getTurma().consultar(true, false, modalidadePacote.getModalidade().getCodigo(), codEmpresa, Uteis.NIVELMONTARDADOS_TODOS, false);
                for (TurmaVO turma : turmas){
                    listTurmas.add(turma);
                }
            }
        }else if (getCodModalidade() != 0){
            ModalidadeVO modalidade = getFacade().getModalidade().consultarPorChavePrimaria(getCodModalidade(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            nomeModalidade = modalidade.getNome();
            listTurmas = getFacade().getTurma().consultar(true, false, getCodModalidade(), codEmpresa, Uteis.NIVELMONTARDADOS_TODOS, false);
        }

        StringBuilder diasHorariosVagasTurmas = new StringBuilder();
        textoSub = textoSub.replace("[ORCA]NomeModalidade", nomeModalidade);

        diasHorariosVagasTurmas.append(" <table style="+aspas+"width: 100%"+aspas+"> ");
        diasHorariosVagasTurmas.append(" <tr style="+aspas+"text-align: center; background-color: #0070c0; color: white"+aspas+"> ");
        diasHorariosVagasTurmas.append(" <td> ");
        diasHorariosVagasTurmas.append(" Nome ");
        diasHorariosVagasTurmas.append(" </td> ");
        diasHorariosVagasTurmas.append(" <td> ");
        diasHorariosVagasTurmas.append(" Professor");
        diasHorariosVagasTurmas.append(" </td> ");
        diasHorariosVagasTurmas.append(" <td> ");
        diasHorariosVagasTurmas.append(" Horário ");
        diasHorariosVagasTurmas.append(" </td> ");
        diasHorariosVagasTurmas.append(" <td> ");
        diasHorariosVagasTurmas.append(" Segunda ");
        diasHorariosVagasTurmas.append(" </td> ");
        diasHorariosVagasTurmas.append(" <td> ");
        diasHorariosVagasTurmas.append(" Terça ");
        diasHorariosVagasTurmas.append(" </td> ");
        diasHorariosVagasTurmas.append(" <td> ");
        diasHorariosVagasTurmas.append(" Quarta ");
        diasHorariosVagasTurmas.append(" </td> ");
        diasHorariosVagasTurmas.append(" <td> ");
        diasHorariosVagasTurmas.append(" Quinta ");
        diasHorariosVagasTurmas.append(" </td> ");
        diasHorariosVagasTurmas.append(" <td> ");
        diasHorariosVagasTurmas.append(" Sexta ");
        diasHorariosVagasTurmas.append(" </td> ");
        diasHorariosVagasTurmas.append(" <td> ");
        diasHorariosVagasTurmas.append(" Sábado ");
        diasHorariosVagasTurmas.append(" </td> ");
        diasHorariosVagasTurmas.append(" </tr> ");
        Ordenacao.ordenarLista(listTurmas, "idadeMaxima");
        for (TurmaVO turma : listTurmas) {
            if(orcamento != null && orcamento.getTipoTurma() == 2 && !(turma.getIdadeMinima() <= 14)){//Infantil
                continue;
            }else if(orcamento != null && orcamento.getTipoTurma() == 3 && !(turma.getIdadeMinima() >= 15)){//Adulto
                continue;
            }

            HashMap<String, Boolean> professorHorarioExecutado = new HashMap<>();
            List<HorarioTurmaVO> newListHorarios = new ArrayList<>();
            for (HorarioTurmaVO horarioTurma : turma.getHorarioTurmaVOs()) {
                //validar periodo
                if (orcamento != null && orcamento.getPeriodo() == 1 && !validarPeriodo(horarioTurma.getHoraInicial(), manha)) {//Manhã
                    continue;
                } else if (orcamento != null && orcamento.getPeriodo() == 2 && !validarPeriodo(horarioTurma.getHoraInicial(), tarde)) {//Tarde
                    continue;
                } else if (orcamento != null && orcamento.getPeriodo() == 3 && !validarPeriodo(horarioTurma.getHoraInicial(), noite)) {//Noite
                    continue;
                }
                newListHorarios.add(horarioTurma);
            }

            Ordenacao.ordenarLista(newListHorarios, "horaInicial");
            for (HorarioTurmaVO horarioTurma : newListHorarios) {
                if (professorHorarioExecutado.isEmpty() || !professorHorarioJaExecutado(horarioTurma, professorHorarioExecutado)) {
                    diasHorariosVagasTurmas.append(" <tr style=" + aspas + "text-align: center; background-color: #EEEEEE" + aspas + "> ");
                    diasHorariosVagasTurmas.append(" <td> ");
                    diasHorariosVagasTurmas.append(turma.getDescricao());
                    diasHorariosVagasTurmas.append(" </td> ");
                    diasHorariosVagasTurmas.append(" <td> ");
                    diasHorariosVagasTurmas.append(horarioTurma.getProfessor().getPessoa().getNomeAbreviado());
                    diasHorariosVagasTurmas.append(" </td> ");
                    diasHorariosVagasTurmas.append(" <td> ");
                    diasHorariosVagasTurmas.append(horarioTurma.getHoraInicial() + " - " + horarioTurma.getHoraFinal());
                    diasHorariosVagasTurmas.append(" </td> ");
                    montarVagasSemana(diasHorariosVagasTurmas, horarioTurma.getHoraInicial() + " - " + horarioTurma.getHoraFinal(), newListHorarios, retornaTexto, horarioTurma.getProfessor().getPessoa().getNomeAbreviado());
                    diasHorariosVagasTurmas.append(" </tr> ");
                    professorHorarioExecutado.put(horarioTurma.getProfessor().getPessoa().getNomeAbreviado(), true);
                    professorHorarioExecutado.put(horarioTurma.getHoraInicial() + " - " + horarioTurma.getHoraFinal(), true);
                    continue;
                }

            }
        }

        diasHorariosVagasTurmas.append(" </table> ");
        textoSub = textoSub.replace("[ORCA]HorariosTurma", diasHorariosVagasTurmas.toString());
        return textoSub;
    }

    private boolean validarPeriodo(String horaInicial, String[] manha) {
        boolean horarioEquivaleAoPeriodo = false;
        for(int i = 0 ; i < manha.length ; i++){
            if(horaInicial.contains(manha[i])){
                horarioEquivaleAoPeriodo = true;
                break;
            }
        }
        return horarioEquivaleAoPeriodo;
    }

    private boolean professorHorarioJaExecutado(HorarioTurmaVO horarioTurma, HashMap<String,Boolean> professorHorarioExecutado) {
        if(professorHorarioExecutado.get(horarioTurma.getHoraInicial() + " - " + horarioTurma.getHoraFinal()) != null
                && professorHorarioExecutado.get(horarioTurma.getProfessor().getPessoa().getNomeAbreviado()) != null){
            return professorHorarioExecutado.get(horarioTurma.getHoraInicial() + " - " + horarioTurma.getHoraFinal()) && professorHorarioExecutado.get(horarioTurma.getProfessor().getPessoa().getNomeAbreviado());
        }else{
         return false;
        }
    }

    private StringBuilder montarVagasSemana(StringBuilder diasHorariosVagasTurmas, String horas, List<HorarioTurmaVO> horariosTurma, boolean retornaTurma, String professor) {
        char aspas = '"';
        String semVaga = "";
        String comVaga = "";
        String semTurma = "";
        if (retornaTurma) {
            semVaga = "<span style=" + aspas + "color: red" + aspas + "><strong>" + "X" + "</strong></span>";
            comVaga = "<span style=" + aspas + "color: green" + aspas + "><strong>" + "V" + "</strong></span>";
            semTurma = "<span style=" + aspas + "color: grey" + aspas + "><strong>" + "-" + "</strong></span>";
        } else {
            semVaga = "<i style=" + aspas + "color: red" + aspas + " class=" + aspas + "fa fa-icon-remove" + aspas + " aria-hidden=" + aspas + "true" + aspas + ">" + " </i> ";
            comVaga = "<i style=" + aspas + "color: green" + aspas + " class=" + aspas + "fa fa-icon-check" + aspas + " aria-hidden=" + aspas + "true" + aspas + ">" + " </i> ";
            semTurma = "<i style=" + aspas + "color: grey" + aspas + " class=" + aspas + "fa fa-icon-minus" + aspas + " aria-hidden=" + aspas + "true" + aspas + ">" + " </i> ";
        }

        int[] semanas = new int[6];
        boolean[] temTurmaNoDiaSemanaSemVaga = new boolean[6];
        int i = 6;
        for (HorarioTurmaVO horarioTurma : horariosTurma) {
        boolean temVaga = horarioTurma.getQuantidadeVagasDisponiveis() > 0;
            if (horas.equalsIgnoreCase(horarioTurma.getHoraInicial() + " - " + horarioTurma.getHoraFinal()) && professor.equalsIgnoreCase(horarioTurma.getProfessor().getPessoa().getNomeAbreviado())) {

                if (horarioTurma.getDiaSemana_Apresentar().toLowerCase().contains("domingo")) {
                    continue;
                }

                if (horarioTurma.getDiaSemana_Apresentar().toLowerCase().contains("segunda")) {
                    if(temVaga) {
                        i--;
                        semanas[0] = 2;
                        diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                        diasHorariosVagasTurmas.append(comVaga);
                        diasHorariosVagasTurmas.append(" </td> ");
                        continue;
                    }else {
                        temTurmaNoDiaSemanaSemVaga[0] = true;
                    }
                } else if (horarioTurma.getDiaSemana_Apresentar().toLowerCase().contains("terça")) {
                    if(temVaga) {
                        i--;
                        if (semanas[0] == 2) {
                            semanas[1] = 3;
                            diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                            diasHorariosVagasTurmas.append(comVaga);
                            diasHorariosVagasTurmas.append(" </td> ");
                        } else {
                            semanas[1] = 3;
                            i = preencherPosicoesVazias(semanas, diasHorariosVagasTurmas, semVaga, 3, i, temTurmaNoDiaSemanaSemVaga, semTurma);
                            if (semanas[0] == 2) {
                                diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                diasHorariosVagasTurmas.append(comVaga);
                                diasHorariosVagasTurmas.append(" </td> ");
                            } else {
                                i--;
                                if(temTurmaNoDiaSemanaSemVaga[0]){
                                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                    diasHorariosVagasTurmas.append(semVaga);
                                    diasHorariosVagasTurmas.append(" </td> ");
                                }else{
                                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                    diasHorariosVagasTurmas.append(semTurma);
                                    diasHorariosVagasTurmas.append(" </td> ");
                                }
                                diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                diasHorariosVagasTurmas.append(comVaga);
                                diasHorariosVagasTurmas.append(" </td> ");
                            }
                        }
                        continue;
                    }else {
                        temTurmaNoDiaSemanaSemVaga[1] = true;
                    }
                } else if (horarioTurma.getDiaSemana_Apresentar().toLowerCase().contains("quarta")) {
                    if(temVaga) {
                        i--;
                        if (semanas[1] == 3) {
                            semanas[2] = 4;
                            diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                            diasHorariosVagasTurmas.append(comVaga);
                            diasHorariosVagasTurmas.append(" </td> ");
                        } else {
                            semanas[2] = 4;
                            i = preencherPosicoesVazias(semanas, diasHorariosVagasTurmas, semVaga, 4, i, temTurmaNoDiaSemanaSemVaga, semTurma);
                            if (semanas[1] == 3) {
                                diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                diasHorariosVagasTurmas.append(comVaga);
                                diasHorariosVagasTurmas.append(" </td> ");
                            } else {
                                i--;
                                if(temTurmaNoDiaSemanaSemVaga[1]){
                                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                    diasHorariosVagasTurmas.append(semVaga);
                                    diasHorariosVagasTurmas.append(" </td> ");
                                }else{
                                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                    diasHorariosVagasTurmas.append(semTurma);
                                    diasHorariosVagasTurmas.append(" </td> ");
                                }
                                diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                diasHorariosVagasTurmas.append(comVaga);
                                diasHorariosVagasTurmas.append(" </td> ");
                            }
                        }
                        continue;
                    }else {
                        temTurmaNoDiaSemanaSemVaga[2] = true;
                    }
                } else if (horarioTurma.getDiaSemana_Apresentar().toLowerCase().contains("quinta")) {
                    if(temVaga) {
                        i--;
                        if (semanas[2] == 4) {
                            semanas[3] = 5;
                            diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                            diasHorariosVagasTurmas.append(comVaga);
                            diasHorariosVagasTurmas.append(" </td> ");
                        } else {
                            semanas[3] = 5;
                            i = preencherPosicoesVazias(semanas, diasHorariosVagasTurmas, semVaga, 5, i, temTurmaNoDiaSemanaSemVaga, semTurma);
                            if (semanas[2] == 4) {
                                diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                diasHorariosVagasTurmas.append(comVaga);
                                diasHorariosVagasTurmas.append(" </td> ");
                            } else {
                                i--;
                                if(temTurmaNoDiaSemanaSemVaga[2]){
                                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                    diasHorariosVagasTurmas.append(semVaga);
                                    diasHorariosVagasTurmas.append(" </td> ");
                                }else{
                                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                    diasHorariosVagasTurmas.append(semTurma);
                                    diasHorariosVagasTurmas.append(" </td> ");
                                }
                                diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                diasHorariosVagasTurmas.append(comVaga);
                                diasHorariosVagasTurmas.append(" </td> ");
                            }
                        }
                        continue;
                    }else {
                        temTurmaNoDiaSemanaSemVaga[3] = true;
                    }
                } else if (horarioTurma.getDiaSemana_Apresentar().toLowerCase().contains("sexta")) {
                    if(temVaga) {
                        i--;
                        if (semanas[3] == 5) {
                            semanas[4] = 6;
                            diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                            diasHorariosVagasTurmas.append(comVaga);
                            diasHorariosVagasTurmas.append(" </td> ");
                        } else {
                            semanas[4] = 6;
                            i = preencherPosicoesVazias(semanas, diasHorariosVagasTurmas, semVaga, 6, i, temTurmaNoDiaSemanaSemVaga, semTurma);
                            if (semanas[3] == 5) {
                                diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                diasHorariosVagasTurmas.append(comVaga);
                                diasHorariosVagasTurmas.append(" </td> ");
                            } else {
                                i--;
                                if(temTurmaNoDiaSemanaSemVaga[3]){
                                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                    diasHorariosVagasTurmas.append(semVaga);
                                    diasHorariosVagasTurmas.append(" </td> ");
                                }else{
                                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                    diasHorariosVagasTurmas.append(semTurma);
                                    diasHorariosVagasTurmas.append(" </td> ");
                                }
                                diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                diasHorariosVagasTurmas.append(comVaga);
                                diasHorariosVagasTurmas.append(" </td> ");
                            }
                        }
                        continue;
                    }else {
                        temTurmaNoDiaSemanaSemVaga[4] = true;
                    }
                 } else if (horarioTurma.getDiaSemana_Apresentar().toLowerCase().contains("sábado")) {
                    if(temVaga) {
                        i--;
                        if (semanas[4] == 6) {
                            semanas[5] = 7;
                            diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                            diasHorariosVagasTurmas.append(comVaga);
                            diasHorariosVagasTurmas.append(" </td> ");
                        } else {
                            semanas[5] = 7;
                            i = preencherPosicoesVazias(semanas, diasHorariosVagasTurmas, semVaga, 7, i, temTurmaNoDiaSemanaSemVaga, semTurma);
                            if (semanas[4] == 6) {
                                diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                diasHorariosVagasTurmas.append(comVaga);
                                diasHorariosVagasTurmas.append(" </td> ");
                            } else {
                                i--;
                                if(temTurmaNoDiaSemanaSemVaga[4]){
                                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                    diasHorariosVagasTurmas.append(semVaga);
                                    diasHorariosVagasTurmas.append(" </td> ");
                                }else{
                                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                    diasHorariosVagasTurmas.append(semTurma);
                                    diasHorariosVagasTurmas.append(" </td> ");
                                }
                                diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                                diasHorariosVagasTurmas.append(comVaga);
                                diasHorariosVagasTurmas.append(" </td> ");
                            }
                        }
                        continue;
                    }else {
                        temTurmaNoDiaSemanaSemVaga[5] = true;
                    }
                }
            }

        }

        while(i > 0){
            i--;
            diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
            diasHorariosVagasTurmas.append(semTurma);
            diasHorariosVagasTurmas.append(" </td> ");
        }

        return diasHorariosVagasTurmas;
    }

    private int preencherPosicoesVazias(int[] semanas, StringBuilder diasHorariosVagasTurmas, String semVaga, int diaSemana, int contador, boolean[] temTurmaNoDiaSemanaSemVaga, String semTurma) {
        char aspas = '"';
        int posicaoAnterior = 0;
        boolean todasZero = false;
        for(int i = 0; i < semanas.length; i++){
            if(semanas[i] == 0 && semanas[i] != diaSemana){
                todasZero = true;
            }else{
                todasZero = false;
            }

            if(todasZero){
                int valorPosicaoAnterior = semanas[posicaoAnterior];
                contador--;
                semanas[i] = semanas[0] == 2 ? valorPosicaoAnterior+1 : 2;
                if(temTurmaNoDiaSemanaSemVaga[i]){
                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                    diasHorariosVagasTurmas.append(semVaga);
                    diasHorariosVagasTurmas.append(" </td> ");
                }else{
                    diasHorariosVagasTurmas.append(" <td style=" + aspas + "text-align: center" + aspas + "> ");
                    diasHorariosVagasTurmas.append(semTurma);
                    diasHorariosVagasTurmas.append(" </td> ");
                }
                posicaoAnterior = i;
            }

            if(todasZero == false){
                break;
            }
        }

        return contador;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getDataDefinicao() {
        return dataDefinicao;
    }

    public void setDataDefinicao(Date dataDefinicao) {
        this.dataDefinicao = dataDefinicao;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public UsuarioVO getResponsavelDefinicao() {
        if (responsavelDefinicao == null) {
            responsavelDefinicao = new UsuarioVO();
        }
        return responsavelDefinicao;
    }

    public void setResponsavelDefinicao(UsuarioVO responsavelDefinicao) {
        this.responsavelDefinicao = responsavelDefinicao;
    }

    public byte[] getImagemModelo() {
        return imagemModelo;
    }

    public void setImagemModelo(byte[] imagemModelo) {
        this.imagemModelo = imagemModelo;
    }

    public int getCodModalidade() {
        return codModalidade;
    }

    public void setCodModalidade(int codModalidade) {
        this.codModalidade = codModalidade;
    }

    public String getNomeImagem() {
        return nomeImagem;
    }

    public void setNomeImagem(String nomeImagem) {
        this.nomeImagem = nomeImagem;
    }

    public int getCodPacote() {
        return codPacote;
    }

    public void setCodPacote(int codPacote) {
        this.codPacote = codPacote;
    }
}

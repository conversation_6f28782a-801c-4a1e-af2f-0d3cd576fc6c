package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.utilitarias.ConsistirException;

public class PlanoCategoriaVO extends SuperVO {

    private Integer codigo;
    private Integer plano;
    private CategoriaVO categoria;

    public PlanoCategoriaVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setCodigo(0);
        setPlano(0);
        setCategoria(new CategoriaVO());
    }

    public static void validarDados(PlanoCategoriaVO obj) throws ConsistirException{
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getCategoria() == null) || (obj.getCategoria().getCodigo().equals(0))) {
            throw new ConsistirException("O campo CATEGORIA DO PLANO deve ser informado.");
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public CategoriaVO getCategoria() {
        return categoria;
    }

    public void setCategoria(CategoriaVO categoria) {
        this.categoria = categoria;
    }
}

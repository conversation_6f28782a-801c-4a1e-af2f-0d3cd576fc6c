package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

public class ConvenioCobrancaEmpresaVO extends SuperVO {

    private Integer codigo;
    private ConvenioCobrancaVO convenioCobranca;
    private EmpresaVO empresa;
    private String siglaPagarMe;

    public ConvenioCobrancaEmpresaVO() {
        super();
    }

    public ConvenioCobrancaEmpresaVO(ConvenioCobrancaVO convenioCobranca, EmpresaVO empresa) {
        super();
        this.empresa = empresa;
        this.convenioCobranca = convenioCobranca;
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ConvenioCobrancaVO getConvenioCobranca() {
        if (convenioCobranca == null) {
            convenioCobranca = new ConvenioCobrancaVO();
        }
        return convenioCobranca;
    }

    public void setConvenioCobranca(ConvenioCobrancaVO convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public static void validarDados(ConvenioCobrancaEmpresaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (UteisValidacao.emptyNumber(obj.getEmpresa().getCodigo())) {
            throw new ConsistirException("O campo EMPRESA deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getConvenioCobranca().getCodigo())) {
            throw new ConsistirException("O campo CONVENIO COBRANÇA deve ser informado.");
        }
    }

    @Override
    public ConvenioCobrancaEmpresaVO clone() {
        return new ConvenioCobrancaEmpresaVO(new ConvenioCobrancaVO(this.getConvenioCobranca().getCodigo()), new EmpresaVO(this.getEmpresa().getCodigo(), this.getEmpresa().getNome()));
    }

    public String getSiglaPagarMe() {
        if (siglaPagarMe == null) {
            siglaPagarMe = "";
        }
        return siglaPagarMe;
    }

    public void setSiglaPagarMe(String siglaPagarMe) {
        this.siglaPagarMe = siglaPagarMe;
    }
}

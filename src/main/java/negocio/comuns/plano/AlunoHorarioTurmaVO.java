/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.plano;

import java.util.Date;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class AlunoHorarioTurmaVO extends SuperVO{
    
    private Integer cliente;
    private HorarioTurmaVO horarioTurma;
    private Date data;
    private Boolean experimental = false;
    private Boolean desafio = false;
    private Date datalancamento;
    private OrigemSistemaEnum origemSistema;
    private Integer usuario;
    private Integer autorizado;
    private Boolean autorizadoGestaoRede;
    private String codAcessoAutorizado;
    private Integer matriculaAutorizado;
    private Integer passivo;
    private Integer indicado;
    private Boolean espera = false;
    private String vinculoComAula;

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public HorarioTurmaVO getHorarioTurma() {
        if(horarioTurma == null){
            horarioTurma = new HorarioTurmaVO();
        }
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaVO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public String getData_Apresentar() {
        return data != null ? Uteis.getData(this.data, "dd/MM/yyyy") : "";
    }

    public Boolean getExperimental() {
        return experimental;
    }

    public void setExperimental(Boolean experimental) {
        this.experimental = experimental;
    }

    public Boolean getDesafio() {
        return desafio;
    }

    public void setDesafio(Boolean desafio) {
        this.desafio = desafio;
    }

    public Date getDatalancamento() {
        return datalancamento;
    }

    public void setDatalancamento(Date datalancamento) {
        this.datalancamento = datalancamento;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        if(origemSistema == null){
            return OrigemSistemaEnum.ZW;
        }
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public Integer getUsuario() {
        if(usuario == null){
            return 0;
        }
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public Integer getAutorizado() {
        return autorizado;
    }

    public void setAutorizado(Integer autorizado) {
        this.autorizado = autorizado;
    }

    public Boolean getAutorizadoGestaoRede() {
        if(autorizadoGestaoRede == null){
            autorizadoGestaoRede = false;
        }
        return autorizadoGestaoRede;
    }

    public void setAutorizadoGestaoRede(Boolean autorizadoGestaoRede) {
        this.autorizadoGestaoRede = autorizadoGestaoRede;
    }

    public String getCodAcessoAutorizado() {
        if (codAcessoAutorizado == null) {
            codAcessoAutorizado = "";
        }
        return codAcessoAutorizado;
    }

    public void setCodAcessoAutorizado(String codAcessoAutorizado) {
        this.codAcessoAutorizado = codAcessoAutorizado;
    }

    public Integer getMatriculaAutorizado() {
        if (matriculaAutorizado == null){
            matriculaAutorizado = 0;
        }
        return matriculaAutorizado;
    }

    public void setMatriculaAutorizado(Integer matriculaAutorizado) {
        this.matriculaAutorizado = matriculaAutorizado;
    }

    public Integer getPassivo() {
        return passivo;
    }

    public void setPassivo(Integer passivo) {
        this.passivo = passivo;
    }

    public Integer getIndicado() {
        return indicado;
    }

    public void setIndicado(Integer indicado) {
        this.indicado = indicado;
    }

    public Boolean getEspera() {
        return espera;
    }

    public void setEspera(Boolean espera) {
        this.espera = espera;
    }

    public String getVinculoComAula() {
        return vinculoComAula;
    }

    public void setVinculoComAula(String vinculoComAula) {
        this.vinculoComAula = vinculoComAula;
    }
}

package negocio.comuns.plano;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ReposicaoVO;

/**
 *
 * <AUTHOR>
 */
public class HorarioTurmaTO extends SuperTO {

    private static final long serialVersionUID = 7006061304541977644L;
    private HorarioTurmaVO horario = new HorarioTurmaVO();
    private List<ConsultarAlunosTurmaVO> listaAlunos = new ArrayList<ConsultarAlunosTurmaVO>();
    private List<ReposicaoVO> listaReposicoes = new ArrayList<ReposicaoVO>();
    private int qtdPresencas = 0;
    private int qtdPrevisto = 0;

    public HorarioTurmaVO getHorario() {
        return horario;
    }

    public void setHorario(HorarioTurmaVO horario) {
        this.horario = horario;
    }

    public List<ConsultarAlunosTurmaVO> getListaAlunos() {
        return listaAlunos;
    }

    public void setListaAlunos(List<ConsultarAlunosTurmaVO> listaAlunos) {
        this.listaAlunos = listaAlunos;
    }

    public List<ReposicaoVO> getListaReposicoes() {
        return listaReposicoes;
    }

    public void setListaReposicoes(List<ReposicaoVO> listaReposicoes) {
        this.listaReposicoes = listaReposicoes;
    }

    public int getQtdPresencas() {
        return qtdPresencas;
    }

    public void setQtdPresencas(int qtdPresencas) {
        this.qtdPresencas = qtdPresencas;
    }

    public int getQtdPrevisto() {
        return qtdPrevisto;
    }

    public void setQtdPrevisto(int qtdPrevisto) {
        this.qtdPrevisto = qtdPrevisto;
    }

    public int getQtdAlunos() {
        return this.getListaAlunos().size();
    }
}

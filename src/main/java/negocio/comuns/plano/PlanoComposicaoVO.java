package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * Reponsável por manter os dados da entidade PlanoComposicao. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 * @see negocio.comuns.plano.PlanoVO
 */
public class PlanoComposicaoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer plano;
    /**
     * Atributo responsável por manter o objeto relacionado da classe <code>Composicao </code>.
     */
    @ChaveEstrangeira
    @FKJson
    protected ComposicaoVO composicao;
    @NaoControlarLogAlteracao
    private int qtdeModalidadesSelecionadas = 0;
    @NaoControlarLogAlteracao
    private boolean selecionouModalidade = false;

    /**
     * Construtor padrão da classe <code>PlanoComposicao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public PlanoComposicaoVO() {
        super();
        inicializarDados();
    }


    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PlanoComposicaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(PlanoComposicaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getComposicao() == null) ||
                (obj.getComposicao().getCodigo() == 0)) {
            throw new ConsistirException("O campo COMPOSIÇÃO (Plano de Composição) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setComposicao(new ComposicaoVO());
    }


    /**
     * Retorna o objeto da classe <code>Composicao</code> relacionado com (<code>PlanoComposicao</code>).
     */
    public ComposicaoVO getComposicao() {
        if (composicao == null) {
            composicao = new ComposicaoVO();
        }
        return (composicao);
    }

    /**
     * Define o objeto da classe <code>Composicao</code> relacionado com (<code>PlanoComposicao</code>).
     */
    public void setComposicao(ComposicaoVO obj) {
        this.composicao = obj;
    }

    public Integer getPlano() {
        return (plano);
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getQtdeModalidadesSelecionadas() {
        return qtdeModalidadesSelecionadas;
    }

    public void setQtdeModalidadesSelecionadas(int qtdeModalidadesSelecionadas) {
        this.qtdeModalidadesSelecionadas = qtdeModalidadesSelecionadas;
    }

    public boolean isSelecionouModalidade() {
        return selecionouModalidade;
    }

    public void setSelecionouModalidade(boolean selecionouModalidade) {
        this.selecionouModalidade = selecionouModalidade;
    }
}
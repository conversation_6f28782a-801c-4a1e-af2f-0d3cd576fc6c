package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class HistoricoProfessorTurmaVO extends SuperVO implements Cloneable {

    @ChavePrimaria
    private Integer codigo;
    private HorarioTurmaVO horarioTurma;
    private <PERSON>borador<PERSON> professor;
    private Date lancamento;
    private Date inicio;
    private Date fim;
    @NaoControlarLogAlteracao
    private  String fotoKey;


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public HorarioTurmaVO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaVO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public ColaboradorVO getProfessor() {
        return professor;
    }

    public void setProfessor(Colaborador<PERSON> professor) {
        this.professor = professor;
    }

    public Date getLancamento() {
        return lancamento;
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }
}

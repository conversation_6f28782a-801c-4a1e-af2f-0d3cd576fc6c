/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;

/**
 *  Classe usada para buscar informações de alunos 
 * <AUTHOR>
 */
public class ConsultarAlunosTurmaVO extends SuperVO {

    protected ClienteVO clienteVO = new ClienteVO();
    protected MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO = new MatriculaAlunoHorarioTurmaVO();
    protected ContratoDuracaoVO contratoDuracaoVO = new ContratoDuracaoVO();
    private int qtdeAulasPeriodo = 0;
    private int qtdeReposicoes = 0;
    private int qtdeDesmarcacoes = 0;
    private int qtdePresencasPeriodo = 0;
    private double frequenciaAluno = 0.0;
    private boolean selecionado = false;

    public ConsultarAlunosTurmaVO() {
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public MatriculaAlunoHorarioTurmaVO getMatriculaAlunoHorarioTurmaVO() {
        return matriculaAlunoHorarioTurmaVO;
    }

    public void setMatriculaAlunoHorarioTurmaVO(MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO) {
        this.matriculaAlunoHorarioTurmaVO = matriculaAlunoHorarioTurmaVO;
    }

    public ContratoDuracaoVO getContratoDuracaoVO() {
        return contratoDuracaoVO;
    }

    public void setContratoDuracaoVO(ContratoDuracaoVO contratoDuracaoVO) {
        this.contratoDuracaoVO = contratoDuracaoVO;
    }

    public double getFrequenciaAluno() {
        return frequenciaAluno;
    }

    public void setFrequenciaAluno(double frequenciaAluno) {
        this.frequenciaAluno = frequenciaAluno;
    }

    public int getQtdeAulasPeriodo() {
        return qtdeAulasPeriodo;
    }

    public void setQtdeAulasPeriodo(int qtdeAulasPeriodo) {
        this.qtdeAulasPeriodo = qtdeAulasPeriodo;
    }

    public int getQtdePresencasPeriodo() {
        return qtdePresencasPeriodo;
    }

    public void setQtdePresencasPeriodo(int qtdePresencasPeriodo) {
        this.qtdePresencasPeriodo = qtdePresencasPeriodo;
    }

    public int getQtdeDesmarcacoes() {
        return qtdeDesmarcacoes;
    }

    public void setQtdeDesmarcacoes(int qtdeDesmarcacoes) {
        this.qtdeDesmarcacoes = qtdeDesmarcacoes;
    }

    public int getQtdeReposicoes() {
        return qtdeReposicoes;
    }

    public void setQtdeReposicoes(int qtdeReposicoes) {
        this.qtdeReposicoes = qtdeReposicoes;
    }

    @Override
    public boolean equals(Object obj) {
        return obj instanceof ConsultarAlunosTurmaVO &&
               ((ConsultarAlunosTurmaVO)obj).getClienteVO().getCodigo().intValue() == this.getClienteVO().getCodigo().intValue();
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }
}

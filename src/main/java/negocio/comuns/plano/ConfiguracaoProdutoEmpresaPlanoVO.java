package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;

public class ConfiguracaoProdutoEmpresaPlanoVO extends SuperVO {

    private Integer produto;
    private EmpresaVO empresa;
    private PlanoVO plano;
    private Double valor;



    public ConfiguracaoProdutoEmpresaPlanoVO() {
        this.empresa = new EmpresaVO();
    }



    public ConfiguracaoProdutoEmpresaPlanoVO(Integer produto, EmpresaVO empresa, PlanoVO plano, Double valor) {
        this.produto = produto;
        this.empresa = empresa;
        this.plano = plano;
        this.valor = valor;
    }

    @Override
    public ConfiguracaoProdutoEmpresaPlanoVO clone(){
        return new ConfiguracaoProdutoEmpresaPlanoVO(this.getProduto(),
                new EmpresaVO(this.getEmpresa().getCodigo(), this.getEmpresa().getNome()), this.getPlano(), this.getValor());
    }

    public static void validarDados(ConfiguracaoProdutoEmpresaPlanoVO obj) throws Exception {
        if (obj.getProduto() == null || obj.getEmpresa() == null
                || obj.getPlano() == null || obj.getValor() == null) {
            throw new Exception("Dados obrigatórios ausentes na configuração do valor do produto por plano.");
        }
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public PlanoVO getPlano() {
        if(plano == null) {
            plano = new PlanoVO();
        }
        return plano;
    }

    public void setPlano(PlanoVO plano) {
        this.plano = plano;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }


}

package negocio.comuns.plano;

import javax.faces.context.FacesContext;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade PlanoTextoPadrao. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class PlanoTextoPadraoTagVO extends SuperVO {

    protected Integer codigo;
    protected String tag;
    protected Integer planoTextoPadrao;
    

    /**
     * Construtor padrão da classe <code>PlanoTextoPadrao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public PlanoTextoPadraoTagVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PlanoTextoPadraoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(PlanoTextoPadraoTagVO obj) throws ConsistirException {
//        if (!obj.isValidarDados().booleanValue()) {
//            return;
//        }
//        if (obj.getTag().equals("")) {
//            throw new ConsistirException("O campo TAG (Plano Texto Padrao Tag) deve ser informado.");
//        }
    }


    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setPlanoTextoPadrao(new Integer(0));
        setTag("");
    }

    protected FacesContext context() {
        return (FacesContext.getCurrentInstance());
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTag() {
        if (tag == null) {
            tag = "";
        }
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Integer getPlanoTextoPadrao() {
        return planoTextoPadrao;
    }

    public void setPlanoTextoPadrao(Integer planoTextoPadrao) {
        this.planoTextoPadrao = planoTextoPadrao;
    }

}

package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.plano.enumerador.TipoIntervalo;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.List;

public class DescontoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo = 0;
    private String descricao = "";
    private Double valor = 0.0;
    private Double valorProdudoComDesconto = 0.0;
    private Double valorTemporarioProdudoDoDesconto = 0.0;
    private TipoDesconto tipoDesconto = TipoDesconto.NA;
    private String tipoProduto = "";
    private int nrDiasAntecipado = 0;
    private Boolean ativo = false;
    @NaoControlarLogAlteracao
    private boolean descontoEscolhido = false;
    @Lista
    @ListJson(clazz = DescontoRenovacaoVO.class)
    private List<DescontoRenovacaoVO> listaIntervalos = new ArrayList<DescontoRenovacaoVO>();
    private boolean aplicarEmpresas = false;
    private List<DescontoEmpresaVO> empresas = new ArrayList<DescontoEmpresaVO>();

    public DescontoVO() {
    }

    public DescontoVO(Integer codigo) {
        this.codigo = codigo;
    }

    public static void validarDados(DescontoVO obj) throws Exception {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDescricao().trim().isEmpty()) {
            throw new Exception("O campo DESCRIÇÃO (Desconto) deve ser informado.");
        }
        if (obj.getTipoProduto() == null || obj.getTipoProduto().trim().isEmpty()) {
            throw new Exception("O campo TIPOPRODUTO (Desconto) deve ser informado.");
        }
        if (obj.getTipoProduto().equals("DR")) {
            if(obj.getListaIntervalos().isEmpty())
                throw new Exception("Informe Pelo Menos um Intervalo para Desconto de Renovação.");
        } else {
            if (obj.getValor().doubleValue() == 0.0)
                throw new Exception("O campo VALOR (Desconto) não pode ser igual a Zero.");
        }
        if(obj.getListaIntervalos().isEmpty() && obj.getTipoDesconto().equals(TipoDesconto.NA)) {
            throw new Exception("O campo TIPODESCONTO (Desconto) deve ser informado.");
        }
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setTipoProduto(getTipoProduto().toUpperCase());
    }

    public DescontoRenovacaoVO getIntervalo(int nrDias) {
        DescontoRenovacaoVO ret = new DescontoRenovacaoVO();
        boolean antecipado = (nrDias >= 0);
        nrDias = Math.abs(nrDias);
        for(DescontoRenovacaoVO intervalo : listaIntervalos) {
            // se é antecipação
            if(antecipado && intervalo.getTipoIntervalo().equals(TipoIntervalo.AN)) {
                // este teste não é feito fora do if pois intervalos antecipados podem ser diferentes dos intervalos atrasados
                if(nrDias < intervalo.getIntervaloDe())
                    break;
                ret = intervalo;
                // se o nr de dias de antecipação corresponde ao periodo deste intervalo
                if(intervalo.getIntervaloDe() <= nrDias && intervalo.getIntervaloAte() >= nrDias)
                    return ret;
                
            // se atrasado
            } else if(!antecipado && intervalo.getTipoIntervalo().equals(TipoIntervalo.AT)) {
                // este teste não é feito fora do if pois intervalos antecipados podem ser diferentes dos intervalos atrasados
                if(nrDias < intervalo.getIntervaloDe())
                    break;
                // para intervalos atrasados o ultimo intervalo nao conta para atrasos maiores que o previsto
                // ret = intervalo;
                // se o nr de dias atrasado corresponde ao periodo deste intervalo
                if(intervalo.getIntervaloDe() <= nrDias && intervalo.getIntervaloAte() >= nrDias)
                    return intervalo;
            }
        }
        return ret;
    }

    public String getDescricaoMinusculo(){
        if (descricao == null) {
            descricao = "";
        }
        return Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(descricao);
    }
    public String getTipoProduto() {
        return tipoProduto;
    }

    public String getTipoProduto_Apresentar() {
        return ProdutoVO.getTipoProduto_Apresentar(tipoProduto);
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public TipoDesconto getTipoDesconto() {
        if (tipoDesconto == null){
            tipoDesconto = TipoDesconto.NA;
        }
        return tipoDesconto;
    }

    public String getTipoDesconto_Apresentar() {
        return getTipoDesconto().getDescricao();
    }

    public void setTipoDesconto(TipoDesconto tipoDesconto) {
        this.tipoDesconto = tipoDesconto;
    }

    public Double getValor() {
        return valor;
    }
    public String getValor_Apresentar() {
        if(tipoDesconto != null && tipoDesconto.equals(TipoDesconto.PE)){
            return Formatador.formatarValorMonetarioSemMoeda(valor) + "%";
        }
        return Formatador.formatarValorMonetario(valor);
    }

    public void setValor(Double valor) {
        this.valor = Uteis.arredondarForcando2CasasDecimais(valor);
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorProdudoComDesconto() {
        return valorProdudoComDesconto;
    }

    public void setValorProdudoComDesconto(Double valorProdudoComDesconto) {
        this.valorProdudoComDesconto = valorProdudoComDesconto;
    }

    public Boolean getApresentarDescontoPorcentagem() {
        return getTipoDesconto().equals(TipoDesconto.PE);
    }

    public Boolean getApresentarDescontoValor() {
        return getTipoDesconto().equals(TipoDesconto.VA);
    }

    public Boolean getApresentarDescontoBonus() {
        return getTipoDesconto().equals(TipoDesconto.BO);
    }

    public Double getValorTemporarioProdudoDoDesconto() {
        return valorTemporarioProdudoDoDesconto;
    }

    public void setValorTemporarioProdudoDoDesconto(Double valorTemporarioProdudoDoDesconto) {
        this.valorTemporarioProdudoDoDesconto = valorTemporarioProdudoDoDesconto;
    }

    public List<DescontoRenovacaoVO> getListaIntervalos() {
        return listaIntervalos;
    }

    public void setListaIntervalos(List<DescontoRenovacaoVO> listaIntervalos) {
        this.listaIntervalos = listaIntervalos;
    }

    public boolean isDescontoEscolhido() {
        return descontoEscolhido;
    }

    public void setDescontoEscolhido(boolean descontoEscolhido) {
        this.descontoEscolhido = descontoEscolhido;
    }

    public int getNrDiasAntecipado() {
        return nrDiasAntecipado;
    }

    public void setNrDiasAntecipado(int nrDiasAntecipado) {
        this.nrDiasAntecipado = Math.abs(nrDiasAntecipado);
    }

    public Boolean getAtivo() {
        if(ativo==null){
            ativo = false;
        }
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getSituacao_Apresentar() {
        return getAtivo() ? "Sim" : "Não";
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        DescontoVO that = (DescontoVO) o;

        return getCodigo().equals(that.getCodigo());
    }

    @Override
    public int hashCode() {
        return getCodigo().hashCode();
    }

    public List<DescontoEmpresaVO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<DescontoEmpresaVO> empresas) {
        this.empresas = empresas;
    }


    public boolean isAplicarEmpresas() {
        return aplicarEmpresas;
    }

    public void setAplicarEmpresas(boolean aplicarEmpresas) {
        this.aplicarEmpresas = aplicarEmpresas;
    }
}
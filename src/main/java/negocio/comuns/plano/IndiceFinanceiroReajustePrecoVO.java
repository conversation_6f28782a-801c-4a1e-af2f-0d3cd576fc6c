package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoIndiceFinanceiroEnum;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.Date;

/**
 * Created by ulisses on 19/11/2016.
 */
public class IndiceFinanceiroReajustePrecoVO extends SuperVO {

    private Integer tipoIndice;
    private String ano;
    private String mes;
    private Integer tipoPlano;
    private Double percentualAcumulado = 0.0;
    private Boolean aplicarreajusterenovacaocontratorecorrencia = true;
    private Date datalancamento;

    private Double valorMensalidadeOriginal; // atributo transient
    private Double valorMensalidadeReajustada; // atributo transient


    public Integer getTipoIndice() {
        return tipoIndice;
    }

    public void setTipoIndice(Integer tipoIndice) {
        this.tipoIndice = tipoIndice;
    }

    public TipoIndiceFinanceiroEnum getTipoIndiceEnum() throws Exception{
        return TipoIndiceFinanceiroEnum.getIndice(tipoIndice);
    }

    public String getAno() {
        return ano;
    }

    public void setAno(String ano) {
        this.ano = ano;
    }

    public String getMes() {
        return mes;
    }

    public Integer getTipoPlano() {
        return tipoPlano;
    }

    public void setTipoPlano(Integer tipoPlano) {
        this.tipoPlano = tipoPlano;
    }

    public void setMes(String mes) {
        this.mes = mes;
    }

    public Double getPercentualAcumulado() {
        return percentualAcumulado;
    }

    public void setPercentualAcumulado(Double percentualAcumulado) {
        this.percentualAcumulado = percentualAcumulado;
    }

    public Double getValorMensalidadeOriginal() {
        return valorMensalidadeOriginal;
    }

    public void setValorMensalidadeOriginal(Double valorMensalidadeOriginal) {
        this.valorMensalidadeOriginal = valorMensalidadeOriginal;
    }

    public Double getValorMensalidadeReajustada() {
        return valorMensalidadeReajustada;
    }

    public void setValorMensalidadeReajustada(Double valorMensalidadeReajustada) {
        this.valorMensalidadeReajustada = valorMensalidadeReajustada;
    }

    public Boolean getAplicarreajusterenovacaocontratorecorrencia() {
        return aplicarreajusterenovacaocontratorecorrencia;
    }

    public void setAplicarreajusterenovacaocontratorecorrencia(Boolean aplicarreajusterenovacaocontratorecorrencia) {
        this.aplicarreajusterenovacaocontratorecorrencia = aplicarreajusterenovacaocontratorecorrencia;
    }

    public Date getDatalancamento() {
        return datalancamento;
    }

    public void setDatalancamento(Date datalancamento) {
        this.datalancamento = datalancamento;
    }

    public static void validarDados(IndiceFinanceiroReajustePrecoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getTipoIndice() == null) || (obj.getTipoIndice() <= 0)){
            throw new ConsistirException("O campo Tipo Indice (IndiceFinanceiroReajustePreco) deve ser informado.");
        }
        if ((obj.getMes() == null) || (obj.getMes().trim().equals(""))) {
            throw new ConsistirException("O campo Mês (IndiceFinanceiroReajustePreco) deve ser informado.");
        }
        if ((Integer.parseInt(obj.getMes()) <=0) || (Integer.parseInt(obj.getMes()) >12)){
            throw new ConsistirException("Deve ser informado um valor entre 1 e 12 para o campo mês(IndiceFinanceiroReajustePreco)");
        }
        if ((obj.getAno() == null) || (obj.getAno().trim().equals(""))) {
            throw new ConsistirException("O campo Ano (IndiceFinanceiroReajustePreco) deve ser informado.");
        }
        if ((obj.getPercentualAcumulado() == null) || (obj.getPercentualAcumulado() < 0)){
            throw new ConsistirException("Deve ser informado um valor igual ou superior a zero para o campo Percentual acumulado (IndiceFinanceiroReajustePreco).");
        }

    }

    public String getTipoIndice_Apresentar()throws Exception{
        return (getTipoIndiceEnum() != null) ? getTipoIndiceEnum().getSigla() : "";
    }

    public String getAplicarreajusterenovacaocontratorecorrencia_apresentar(){
        return (aplicarreajusterenovacaocontratorecorrencia) ? "SIM": "NÃO";
    }

    public String getDescricaoLog() throws Exception{
        StringBuilder desc = new StringBuilder();
        desc.append("Tipo Índice Financeiro Reajuste: ").append(this.getTipoIndiceEnum().getSigla());
        desc.append(" Mês/Ano:").append(this.mes).append("/").append(this.ano);
        desc.append(" percentual reajuste: ").append(String.format( "%.2f", this.getPercentualAcumulado())).append(" %");
        desc.append(" valor mensalidade original: ").append(String.format( "%.2f", this.valorMensalidadeOriginal));
        desc.append(" valor mensalidade reajustada: ").append(String.format( "%.2f",this.getValorMensalidadeReajustada()));
        return desc.toString();
    }
}

package negocio.comuns.plano;

import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import controle.plano.PlanoControle;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UtilReflection;
import org.json.JSONObject;

import java.util.Comparator;

/**
 * Created by ulisses on 10/11/2015.
 */
public class PlanoDuracaoCreditoTreinoVO extends SuperVO {

    private Integer codigo = 0;
    @NaoControlarLogAlteracao
    @FKJson
    private PlanoDuracaoVO planoDuracaoVO;
    private Integer tipoHorarioCreditoTreino;
    private Integer numeroVezesSemana =0;
    private Integer quantidadeCreditoCompra;
    private Integer quantidadeCreditoMensal;
    private double valorUnitario;
    private PlanoVO planoVO;
    private boolean selecionado = false;

    public PlanoDuracaoCreditoTreinoVO() {
    }

    public PlanoDuracaoCreditoTreinoVO(JSONObject obj, PlanoVO planoVO, boolean selecionado) {
        this.codigo = obj.optInt("codigo");
        this.planoDuracaoVO = new PlanoDuracaoVO();
        this.planoDuracaoVO.setCodigo(obj.optInt("planoDuracao"));
        this.tipoHorarioCreditoTreino = obj.optInt("tipoHorarioCreditoTreino");
        this.numeroVezesSemana = obj.optInt("numeroVezesSemana");
        this.quantidadeCreditoCompra = obj.optInt("quantidadeCreditoCompra");
        this.quantidadeCreditoMensal = obj.optInt("quantidadeCreditoMensal");
        this.valorUnitario = obj.optDouble("valorUnitario");
        this.planoVO = planoVO;
        this.selecionado = selecionado;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }


    public PlanoDuracaoVO getPlanoDuracaoVO() {
        return planoDuracaoVO;
    }

    public void setPlanoDuracaoVO(PlanoDuracaoVO planoDuracaoVO) {
        this.planoDuracaoVO = planoDuracaoVO;
    }

    public Integer getNumeroVezesSemana() {
        return numeroVezesSemana;
    }

    public void setNumeroVezesSemana(Integer numeroVezesSemana) {
        this.numeroVezesSemana = numeroVezesSemana;
    }

    public Integer getQuantidadeCreditoCompra() {
        return quantidadeCreditoCompra;
    }

    public void setQuantidadeCreditoCompra(Integer quantidadeCreditoCompra) {
        this.quantidadeCreditoCompra = quantidadeCreditoCompra;
    }

    public double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public Integer getTipoHorarioCreditoTreino() {
        return tipoHorarioCreditoTreino;
    }

    public void setTipoHorarioCreditoTreino(Integer tipoHorarioCreditoTreino) {
        this.tipoHorarioCreditoTreino = tipoHorarioCreditoTreino;
    }

    public TipoHorarioCreditoTreinoEnum getTipoHorarioCreditoTreinoEnum(){
        return TipoHorarioCreditoTreinoEnum.getTipo(this.tipoHorarioCreditoTreino);
    }

    public void validarDados(boolean creditoNaoAcumulativo) throws Exception {

        if (!UtilReflection.objetoMaiorQueZero(this.tipoHorarioCreditoTreino, null)) {
            throw new ConsistirException("O campo Tipo Horário (PlanoDuracaoCreditoTreino) deve ser informado.");
        }
        if (!creditoNaoAcumulativo && !UtilReflection.objetoMaiorQueZero(this.quantidadeCreditoCompra, null)) {
            throw new ConsistirException("O campo Quantidade Crédito (PlanoDuracaoCreditoTreino) deve ser informado.");
        }
        if (!UtilReflection.objetoMaiorQueZero(this.numeroVezesSemana, null)) {
            throw new ConsistirException("O campo Vezes Semana (PlanoDuracaoCreditoTreino) deve ser informado.");
        }

        if (!UtilReflection.objetoMaiorQueZero(this.valorUnitario, null)) {
            throw new ConsistirException("O campo Valor Unitário (PlanoDuracaoCreditoTreino) deve ser informado.");
        }
    }

    public void validarDados() throws Exception {
        validarDados(false);
    }

    @Override
    public boolean equals(Object obj){
        if ((obj ==null) || (!(obj instanceof PlanoDuracaoCreditoTreinoVO))){
            return false;
        }
        return ((PlanoDuracaoCreditoTreinoVO)obj).getPlanoDuracaoVO().getNumeroMeses().equals(this.planoDuracaoVO.getNumeroMeses()) &&
                ((PlanoDuracaoCreditoTreinoVO)obj).getTipoHorarioCreditoTreino().equals(this.tipoHorarioCreditoTreino) &&
                ((PlanoDuracaoCreditoTreinoVO)obj).getQuantidadeCreditoCompra().equals(this.quantidadeCreditoCompra);
    }

    @Override
    public int hashCode(){
        return this.getPlanoDuracaoVO().getCodigo().hashCode();
    }


    public static Comparator COMPARATOR_QTDE_CREDITO = new Comparator() {
        public int compare(Object o1, Object o2) {
            PlanoDuracaoCreditoTreinoVO p1 = (PlanoDuracaoCreditoTreinoVO) o1;
            PlanoDuracaoCreditoTreinoVO p2 = (PlanoDuracaoCreditoTreinoVO) o2;
            int resultado = p1.getPlanoDuracaoVO() != null && p2.getPlanoDuracaoVO() != null ? p1.getPlanoDuracaoVO().getNumeroMeses().compareTo(p2.getPlanoDuracaoVO().getNumeroMeses()) : 0;
            if (resultado == 0){
                resultado = p1.getTipoHorarioCreditoTreino().compareTo(p2.getTipoHorarioCreditoTreino());
                if (resultado == 0){
                   resultado  = p1.getQuantidadeCreditoCompra().compareTo(p2.getQuantidadeCreditoCompra());
                }
            }
            return resultado;
        }
    };

    public double getValorTotal(){
        return this.quantidadeCreditoCompra * this.valorUnitario;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Integer getQuantidadeCreditoMensal() {
        return quantidadeCreditoMensal;
    }

    public void setQuantidadeCreditoMensal(Integer quantidadeCreditoMensal) {
        this.quantidadeCreditoMensal = quantidadeCreditoMensal;
    }

    public PlanoVO getPlanoVO() {
        return planoVO;
    }

    public void setPlanoVO(PlanoVO planoVO) {
        this.planoVO = planoVO;
    }
}

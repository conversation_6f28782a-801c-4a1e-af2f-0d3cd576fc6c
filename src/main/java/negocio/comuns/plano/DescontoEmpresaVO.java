package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;

public class DescontoEmpresaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @ChaveEstrangeira
    @FKJson
    protected Integer desconto;
    @ChaveEstrangeira
    @FKJson
    protected Integer empresa;
    protected EmpresaVO empresaVO;
    private boolean utilizaDesconto;

    public Integer getDesconto() {
        return desconto;
    }

    public void setDesconto(Integer desconto) {
        this.desconto = desconto;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public boolean isUtilizaDesconto() {
        return utilizaDesconto;
    }

    public void setUtilizaDesconto(boolean utilizaDesconto) {
        this.utilizaDesconto = utilizaDesconto;
    }
}

package negocio.comuns.plano.enumerador;

import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public enum TipoProduto {

    MATRICULA("MA", "Matrícula", false, true, true),
    REMATRICULA("RE", "Rematrícula", false, true, true),
    RENOVACAO("RN", "Renovação", false, true, false),
    PRODUTO_ESTOQUE("PE", "Produto Estoque", true, true),
    MES_REFERENCIA_PLANO("PM", "Mês de Referência Plano", false, true, true),
    SERVICO("SE", "Serviço", true, true, true),
    CONVENIO_DESCONTO("CD", "Convênio de Desconto", false, false),
    DESCONTO("DE", "Desconto", false, false),
    DEVOLUCAO("DV", "Devolução", false, false),
    TRANCAMENTO("TR", "Trancamento", false, true),
    RETORNO_TRANCAMENTO("RT", "Retorno Trancamento", false, true),
    AULA_AVULSA("AA", "Aula Avulsa", false, true, false),
    DIARIA("DI", "Diária", false, true, true),
    FREEPASS("FR", "FreePass", false, false),
    ALTERAR_HORARIO("AH", "Alterar - Horário", false, true),
    MANUTENCAO_MODALIDADE("MM", "Manutenção Modalidade", false, true),
    MANUTENCAO_CONTA_CORRENTE("MC", "Manutenção Conta Corrente", false, true),
    DESCONTO_RENOVACAO_ANTECIPADA("DR", "Desconto em Renovação Antecipada", false, true),
    TAXA_PERSONAL("TP", "Taxa de Personal", false, true),
    SESSAO("SS", "Sessão", true, true),
    DEVOLUCAO_CREDITO("DC", "Devolução de crédito de conta corrente do cliente", false, false),
    ATESTADO("AT", "Atestado", false, true),
    TAXA_DE_ADESAO_PLANO_RECORRENCIA("TD", "Taxa de Adesão Plano Recorrência", false, true, true),
    TAXA_RENEGOCIACAO("TN", "Taxa de Renegociação", true, true, true),
    CREDITO_PERSONAL("CP", "Crédito de personal", true, true),
    TAXA_DE_ANUIDADE_PLANO_RECORRENCIA("TA", "Taxa de Anuidade Plano Recorrência", false, true, true),
    DEVOLUCAO_DE_RECEBIVEIS("RD", "Devolução de recebíveis", false, false),
    DEPOSITO_CONTA_CORRENTE_ALUNO("CC", "Depósito conta corrente do aluno", false, true),
    ACERTO_CONTA_CORRENTE_ALUNO("AC", "Acerto conta corrente do aluno", false, false),
    QUITACAO_DE_DINHEIRO("QU", "Quitação de dinheiro - Cancelamento", false, true),
    ARMARIO("AR", "Armário", false, true),
    MULTA_JUROS("MJ", "Multa e Juros", false, true),
    CHEQUE_DEVOLVIDO("CH", "Cheques devolvidos", false, false),
    DESAFIO("DS", "Desafio", true, false),
    HOMEFIT("HM","App Home Fit",true,false),
    BIO_TOTEM("BT", "Bio Totem", true, false),
    CONSULTA_NUTRICIONAL("CN", "Consulta Nutricional", true, false),
    ORDEM_COMPRA("OC", "Ordem de compra", false, false),
    LOCACAO("LC", "Locação", false, false),
    CARTEIRINHA("CT", "Carteirinha", false, false);

    private String codigo;
    private String descricao;
    private Boolean vendaAvulsa;
    private Boolean relacionarEmPlanoTipo;
    private boolean apresentarAutorizacaoCobranca;

    public static String filtroVendaAvulsa() {
        String filtro = "";
        for (TipoProduto tipo : values()) {
            if (tipo.getVendaAvulsa()) {
                filtro += (filtro.isEmpty() ? "tipoProduto IN (" : ",") + "'" + tipo.getCodigo() + "'";
            }
        }
        return filtro.isEmpty() ? "" : filtro + ")";
    }

    public static Boolean tipoProdutoVendaAvulsa(String tipo) {
        TipoProduto tipoProdutoCodigo = getTipoProdutoCodigo(tipo);
        return tipoProdutoCodigo == null ? false : tipoProdutoCodigo.getVendaAvulsa();
    }

    TipoProduto(String codigo, String descricao, Boolean vendaAvulsa, boolean apresentarAutorizacaoCobranca) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.vendaAvulsa = vendaAvulsa;
        this.apresentarAutorizacaoCobranca = apresentarAutorizacaoCobranca;
    }

    TipoProduto(String codigo, String descricao, Boolean vendaAvulsa, Boolean relacionarEmPlanoTipo, boolean apresentarAutorizacaoCobranca) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.vendaAvulsa = vendaAvulsa;
        this.relacionarEmPlanoTipo = relacionarEmPlanoTipo;
        this.apresentarAutorizacaoCobranca = apresentarAutorizacaoCobranca;
    }

    public static List<TipoProduto> obterRelacionadosEmPlanoTipo() {
        List<TipoProduto> tipos = new ArrayList<TipoProduto>();
        for (TipoProduto tipo : TipoProduto.values()) {
            if (tipo.getRelacionarEmPlanoTipo())
                tipos.add(tipo);
        }

        return tipos;
    }

    public static TipoProduto getTipoProdutoCodigo(String codigo) {
        for (TipoProduto tp : TipoProduto.values())
            if (tp.getCodigo().equals(codigo))
                return tp;
        return null;
    }

    public static String retornaPorDescricao(String descricao) {
        for (TipoProduto tp : TipoProduto.values())
            if (tp.name().equalsIgnoreCase(descricao))
                return tp.getCodigo();
        return "";
    }

    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public Boolean getVendaAvulsa() {
        return vendaAvulsa;
    }

    public Boolean getRelacionarEmPlanoTipo() {
        if (relacionarEmPlanoTipo == null)
            return false;

        return relacionarEmPlanoTipo;
    }

    public static TipoProduto retornaPorDescricaoImportacao(String descricao) {
        if (UteisValidacao.emptyString(descricao)) {
            return null;
        }
        descricao = Uteis.retirarAcentuacaoEPontuacao(descricao.toUpperCase());

        //perquisar igual
        for (TipoProduto tp : TipoProduto.values()) {
            String descricaoTipo = Uteis.retirarAcentuacaoEPontuacao(tp.getDescricao().toUpperCase());
            if (descricaoTipo.toUpperCase().equalsIgnoreCase(descricao.toUpperCase())) {
                return tp;
            }
        }

        //perquisar inicia
        for (TipoProduto tp : TipoProduto.values()) {
            String descricaoTipo = Uteis.retirarAcentuacaoEPontuacao(tp.getDescricao().toUpperCase());
            if (descricaoTipo.toUpperCase().startsWith(descricao.toUpperCase())) {
                return tp;
            }
        }

        //se não encontrar perquisar contem
        for (TipoProduto tp : TipoProduto.values()) {
            String descricaoTipo = Uteis.retirarAcentuacaoEPontuacao(tp.getDescricao().toUpperCase());
            if (descricaoTipo.toUpperCase().contains(descricao.toUpperCase())) {
                return tp;
            }
        }
        return null;
    }

    public boolean isApresentarAutorizacaoCobranca() {
        return apresentarAutorizacaoCobranca;
    }

    public static List<SelectItem> getTiposProdutosParaAutorizacaoCobranca(boolean adicionarVazio) {
        List<SelectItem> lista = new ArrayList<>();
        for (TipoProduto tipoProduto : TipoProduto.values()) {

            if (!tipoProduto.isApresentarAutorizacaoCobranca()) {
                continue;
            }

            String descricao = tipoProduto.getDescricao();
            if (tipoProduto.equals(TipoProduto.MES_REFERENCIA_PLANO)) {
                descricao = "Plano";
            } else if (tipoProduto.equals(TipoProduto.QUITACAO_DE_DINHEIRO)) {
                descricao = "Quitação de Cancelamento";
            }

            lista.add(new SelectItem(tipoProduto.getCodigo(), descricao));

        }
        Ordenacao.ordenarLista(lista, "label");
        if(adicionarVazio) {
            lista.add(0, new SelectItem("", ""));
        }
        return lista;
    }
}

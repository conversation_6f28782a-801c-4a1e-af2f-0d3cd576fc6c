
package negocio.comuns.plano.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum TipoInterval<PERSON> {
    AN(1, "Antecipado"),
    AT(2, "Atrasado");

    private int codigo;
    private String descricao;

    TipoIntervalo(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TipoIntervalo getTipoIntervalo(int codigo) {
        for (TipoIntervalo ti : TipoIntervalo.values())
            if (ti.getCodigo() == codigo)
                return ti;
        return null;
    }

    public static TipoIntervalo getTipoIntervalo(String name) {
        for (TipoIntervalo ti : TipoIntervalo.values())
            if (ti.name().equals(name))
                return ti;
        return null;
    }

    public int getCodigo() {
        return this.codigo;
    }

    public String getDescricao() {
        return this.descricao;
    }
}

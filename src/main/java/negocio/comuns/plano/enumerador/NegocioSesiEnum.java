package negocio.comuns.plano.enumerador;

import negocio.comuns.utilitarias.Ordenacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public enum NegocioSesiEnum {
    ACADEMIA(1, "Academia"),
    NUTRICAO(2, "Nutrição");


    private Integer codigo;
    private String descricao;

    NegocioSesiEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static List<SelectItem> getListaSelectItem() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        for (NegocioSesiEnum negocio :NegocioSesiEnum.values()) {
            objs.add(new SelectItem(negocio.getCodigo(), negocio.getDescricao()));
        }
        Ordenacao.ordenarLista(objs, "label");

        return objs;
    }
}

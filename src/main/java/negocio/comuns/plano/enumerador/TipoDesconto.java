
package negocio.comuns.plano.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum TipoDescon<PERSON> {
    NA(0, ""),
    PE(1, "Percentual"),
    VA(2, "Valor"),
    BO(3, "Bônus");

    private int codigo;
    private String descricao;

    TipoDesconto(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TipoDesconto getTipoDesconto(int codigo) {
        for (TipoDesconto to : TipoDesconto.values())
            if (to.getCodigo() == codigo)
                return to;
        return null;
    }

    public static TipoDesconto getTipoDesconto(String name) {
        for (TipoDesconto to : TipoDesconto.values())
            if (to.name().equals(name))
                return to;
        return null;
    }

    public int getCodigo() {
        return this.codigo;
    }

    public String getDescricao() {
        return this.descricao;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}

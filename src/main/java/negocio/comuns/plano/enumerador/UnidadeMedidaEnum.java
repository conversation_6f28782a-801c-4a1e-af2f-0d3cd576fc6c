package negocio.comuns.plano.enumerador;

import negocio.comuns.utilitarias.Ordenacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public enum UnidadeMedidaEnum {

    UNIDADE("UN", "Unidade (UN)"),
    GRAMA("G", "Grama (g)"),
    TEMPO_HORA("HR", "Hora (HR)"),
//    QUILOGRAMA("KG", "Quilograma (Kg)"),
//    COMPRIMENTO("M", "Metro (m)"),
    ;

    private String codigo;
    private String descricao;

    UnidadeMedidaEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static List<SelectItem> getListaSelectItem(boolean objeto, boolean adicionarVazio) {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        for (UnidadeMedidaEnum uni : UnidadeMedidaEnum.values()) {
            if (uni.getCodigo().equals(UnidadeMedidaEnum.TEMPO_HORA.getCodigo())) {
                continue;
            }
            if (objeto) {
                objs.add(new SelectItem(uni, uni.getDescricao()));
            } else{
                objs.add(new SelectItem(uni.getCodigo(), uni.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(objs, "label");
        if (adicionarVazio) {
            objs.add(0, new SelectItem("", ""));
        }
        return objs;
    }

    public static List<SelectItem> getListaSelectItemLocacao() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(UnidadeMedidaEnum.TEMPO_HORA.getCodigo(), UnidadeMedidaEnum.TEMPO_HORA.getDescricao()));
        return objs;
    }
}

package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.AgendaTotalTO;

import java.util.ArrayList;
import java.util.List;

public class HistoricoTurmasContratoTO extends SuperTO {

    private Integer qtd = 0;
    private List<AgendaTotalTO> faltas = new ArrayList<AgendaTotalTO>();

    public void add(List<AgendaTotalTO> f){
        faltas.addAll(f);
        qtd = faltas.size();
    }

    public Integer getQtd() {
        return qtd;
    }

    public void setQtd(Integer qtd) {
        this.qtd = qtd;
    }

    public List<AgendaTotalTO> getFaltas() {
        return faltas;
    }

    public void setFaltas(List<AgendaTotalTO> faltas) {
        this.faltas = faltas;
    }
}

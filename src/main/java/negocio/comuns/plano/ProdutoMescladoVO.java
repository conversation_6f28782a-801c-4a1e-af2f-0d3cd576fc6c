package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.facade.jdbc.plano.ProdutoMesclado;

import java.util.Date;

public class ProdutoMescladoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Produto </code>.*/
    @ChaveEstrangeira
    @FKJson
    protected ProdutoVO produtoDestino;
    @ChaveEstrangeira
    @FKJson
    protected ProdutoVO produtoOrigem;

    @NaoControlarLogAlteracao
    protected Date dataExecucao;

    protected String responsavel;

    public ProdutoMescladoVO() {
    }


    public ProdutoMescladoVO(ProdutoVO destino,  ProdutoVO origem, String responsavel) {
        this.produtoDestino = destino;
        this.produtoOrigem = origem;
        this.responsavel = responsavel;
        this.dataExecucao = new Date();

    }
    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProdutoVO getProdutoDestino() {
        return produtoDestino;
    }

    public void setProdutoDestino(ProdutoVO produtoDestino) {
        this.produtoDestino = produtoDestino;
    }

    public ProdutoVO getProdutoOrigem() {
        return produtoOrigem;
    }

    public void setProdutoOrigem(ProdutoVO produtoOrigem) {
        this.produtoOrigem = produtoOrigem;
    }

    public Date getDataExecucao() {
        return dataExecucao;
    }

    public void setDataExecucao(Date dataExecucao) {
        this.dataExecucao = dataExecucao;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }
}

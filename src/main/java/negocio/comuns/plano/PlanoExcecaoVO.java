/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import java.util.HashMap;
import java.util.Map;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.CategoriaVO;

/**
 *
 * <AUTHOR>
 */
public class PlanoExcecaoVO extends SuperVO {

    @NaoControlarLogAlteracao
    public static final String TITLE_PACOTE_E_MODALIDADE = "Valor de Exceção obtido através de Regra no Plano para o Pacote e Modalidade selecionados";
    @NaoControlarLogAlteracao
    public static final String TITLE_MODALIDADE = "Valor de Exceção obtido através de Regra do Plano contendo a Modalidade selecionada";
    @NaoControlarLogAlteracao
    public static final String TITLE_PACOTE = "Valor de Exceção obtido através de Regra no Plano para o Pacote selecionado";
    private Integer codigo = 0;
    @NaoControlarLogAlteracao
    @FKJson
    private PlanoVO plano = new PlanoVO();
    @ChaveEstrangeira
    @FKJson
    private ComposicaoVO pacote = new ComposicaoVO();
    @ChaveEstrangeira
    @FKJson
    private ModalidadeVO modalidade = new ModalidadeVO();
    @ChaveEstrangeira
    @FKJson
    private HorarioVO horario = new HorarioVO();
    private Integer vezesSemana = 0;
    private Integer duracao = 0;
    @NaoControlarLogAlteracao
    private Double valor = 0.0;
    @NaoControlarLogAlteracao
    private Double valorCalculo = 0.0;
    @NaoControlarLogAlteracao
    private Double valorCalculoBase = 0.0;
    @NaoControlarLogAlteracao
    private int modalidadesAvaliadas = 0;
    private int modalidadesJaAvaliadas = 0;
    @NaoControlarLogAlteracao
    Map <Integer,PlanoExcecaoVO> modalidadesPacoteExcecaoPropria;
    @NaoControlarLogAlteracao
    private String title;
    @ChaveEstrangeira
    @FKJson
    private CategoriaVO categoria = new CategoriaVO();

    public PlanoExcecaoVO() {
        super();
    }

    public PlanoExcecaoVO(Integer codigo, PlanoVO plano, ComposicaoVO pacote,
            ModalidadeVO modalidade, HorarioVO horario, Integer vezesSemana, Integer duracao, Double valor, CategoriaVO categoria) {
        this.codigo = codigo;
        this.plano = plano;
        this.pacote = pacote;
        this.modalidade = modalidade;
        this.horario = horario;
        this.vezesSemana = vezesSemana;
        this.duracao = duracao;
        this.vezesSemana = vezesSemana;
        this.duracao = duracao;
        this.valor = valor;
        this.valorCalculo = valor;
        this.categoria = categoria;
    }

    @Override
    public void setCodigo(Integer codigo) {
        super.setCodigo(codigo);
    }

    @Override
    public Integer getCodigo() {
        return super.getCodigo();
    }

    public PlanoVO getPlano() {
        return plano;
    }

    public void setPlano(PlanoVO plano) {
        this.plano = plano;
    }

    public ComposicaoVO getPacote() {
        return pacote;
    }

    public void setPacote(ComposicaoVO pacote) {
        this.pacote = pacote;
    }

    public ModalidadeVO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeVO modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public HorarioVO getHorario() {
        return horario;
    }

    public void setHorario(HorarioVO horario) {
        this.horario = horario;
    }

    public Double getValor() {
        return valor;
    }

    public Double getValorTotal() {
        return (this.valor * this.duracao);
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getVezesSemana() {
        return vezesSemana;
    }

    public void setVezesSemana(Integer nrVezesSemana) {
        this.vezesSemana = nrVezesSemana;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Object getClone() throws IllegalArgumentException, IllegalAccessException, InstantiationException {
        PlanoExcecaoVO novo = new PlanoExcecaoVO(codigo, plano, pacote, modalidade, horario, vezesSemana, duracao, valor, categoria);
        return novo;
    }

    public static Double obterPercentual(PlanoDuracaoVO planoDuracao) {
        if (planoDuracao != null && planoDuracao.getNumeroMeses() != 0) {
            if (planoDuracao.getTipoOperacao() == null) {
                planoDuracao.setTipoOperacao("");
            }
            if (planoDuracao.getTipoOperacao().equals("RE")) {
                if (planoDuracao.getTipoValor().equals("PD")) {
                    return planoDuracao.getPercentualDesconto();
                }

            }
            if (planoDuracao.getTipoOperacao().equals("AC")) {
                if (planoDuracao.getTipoValor().equals("PD")) {
                    return planoDuracao.getPercentualDesconto() * -1;
                }

            } 
        }
        return 0.0;
    }

    public Double getValorCalculo() {
        return valorCalculo;
    }

    public void setValorCalculo(Double valorCalculo) {
        this.valorCalculo = valorCalculo;
    }

    public int getModalidadesAvaliadas() {
        return modalidadesAvaliadas;
    }

    public void setModalidadesAvaliadas(int modalidadesAvaliadas) {
        this.modalidadesAvaliadas = modalidadesAvaliadas;
    }

    public Double getValorCalculoBase() {
        return valorCalculoBase;
    }

    public void setValorCalculoBase(Double valorCalculoBase) {
        this.valorCalculoBase = valorCalculoBase;
    }

    public Map<Integer, PlanoExcecaoVO> getModalidadesPacoteExcecaoPropria() {
        return modalidadesPacoteExcecaoPropria;
    }

    public void setModalidadesPacoteExcecaoPropria(Map<Integer, PlanoExcecaoVO> modalidadesPacoteExcecaoPropria) {
        this.modalidadesPacoteExcecaoPropria = modalidadesPacoteExcecaoPropria;
    }

    public int getModalidadesJaAvaliadas() {
        return modalidadesJaAvaliadas;
    }

    public void setModalidadesJaAvaliadas(int modalidadesJaAvaliadas) {
        this.modalidadesJaAvaliadas = modalidadesJaAvaliadas;
    }

    public CategoriaVO getCategoria() {
        return categoria;
    }

    public void setCategoria(CategoriaVO categoria) {
        this.categoria = categoria;
    }
}

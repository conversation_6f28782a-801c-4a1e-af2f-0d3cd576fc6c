package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;

public class PlanoEmpresaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @ChaveEstrangeira
    @FKJson
    protected EmpresaVO empresa;
    protected boolean venda;
    protected boolean acesso;

    private Double taxaAdesao;
    private Double valorAnuidade;
    private Double valorMensal;
    private PlanoTextoPadraoVO modeloContrato;
    private Double percentualMultaCancelamento;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public boolean isVenda() {
        return venda;
    }

    public void setVenda(boolean venda) {
        this.venda = venda;
    }

    public boolean isAcesso() {
        return acesso;
    }

    public void setAcesso(boolean acesso) {
        this.acesso = acesso;
    }

    public Double getTaxaAdesao() {
        if (taxaAdesao == null) {
            taxaAdesao = 0.0;
        }
        return taxaAdesao;
    }

    public void setTaxaAdesao(Double taxaAdesao) {
        this.taxaAdesao = taxaAdesao;
    }

    public Double getValorAnuidade() {
        if (valorAnuidade == null) {
            valorAnuidade = 0.0;
        }
        return valorAnuidade;
    }

    public void setValorAnuidade(Double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public Double getValorMensal() {
        if (valorMensal == null) {
            valorMensal = 0.0;
        }
        return valorMensal;
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public PlanoTextoPadraoVO getModeloContrato() {
        if (modeloContrato == null) {
            modeloContrato = new PlanoTextoPadraoVO();
        }
        return modeloContrato;
    }

    public void setModeloContrato(PlanoTextoPadraoVO modeloContrato) {
        this.modeloContrato = modeloContrato;
    }

    public Double getPercentualMultaCancelamento() {
        if (percentualMultaCancelamento == null) {
            percentualMultaCancelamento = 0.0;
        }
        return percentualMultaCancelamento;
    }

    public void setPercentualMultaCancelamento(Double percentualMultaCancelamento) {
        this.percentualMultaCancelamento = percentualMultaCancelamento;
    }
}

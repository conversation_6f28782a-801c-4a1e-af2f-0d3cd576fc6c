package negocio.comuns.plano;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;

import java.util.List;

import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade HorarioDisponibilidade. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class HorarioDisponibilidadeVO extends SuperVO {

    protected Integer codigo;
    protected String identificador;
    protected Integer horario;
    protected Boolean desenharTodos;
    protected Boolean matutino;
    protected Boolean vespertino;
    protected Boolean noturno;
    protected Boolean madrugada;
    protected Boolean hora0000;
    protected Boolean hora0030;
    protected Boolean hora0100;
    protected Boolean hora0130;
    protected Boolean hora0200;
    protected Boolean hora0230;
    protected Boolean hora0300;
    protected Boolean hora0330;
    protected Boolean hora0400;
    protected Boolean hora0430;
    protected Boolean hora0500;
    protected Boolean hora0530;
    protected Boolean hora0600;
    protected Boolean hora0630;
    protected Boolean hora0700;
    protected Boolean hora0730;
    protected Boolean hora0800;
    protected Boolean hora0830;
    protected Boolean hora0900;
    protected Boolean hora0930;
    protected Boolean hora1000;
    protected Boolean hora1030;
    protected Boolean hora1100;
    protected Boolean hora1130;
    protected Boolean hora1200;
    protected Boolean hora1230;
    protected Boolean hora1300;
    protected Boolean hora1330;
    protected Boolean hora1400;
    protected Boolean hora1430;
    protected Boolean hora1500;
    protected Boolean hora1530;
    protected Boolean hora1600;
    protected Boolean hora1630;
    protected Boolean hora1700;
    protected Boolean hora1730;
    protected Boolean hora1800;
    protected Boolean hora1830;
    protected Boolean hora1900;
    protected Boolean hora1930;
    protected Boolean hora2000;
    protected Boolean hora2030;
    protected Boolean hora2100;
    protected Boolean hora2130;
    protected Boolean hora2200;
    protected Boolean hora2230;
    protected Boolean hora2300;
    protected Boolean hora2330;

    public static final Integer MATUTINO = 0;
    public static final Integer VESPERTINO = 1;
    public static final Integer NOTURNO = 2;
    public static final Integer MADRUGADA = 3;

    /**
     * Construtor padrão da classe <code>HorarioDisponibilidade</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public HorarioDisponibilidadeVO() {
        super();
        inicializarDados();
    }

    public void alterarTodoPeriodo(Integer periodo, Boolean alterarPara) {
        if (periodo.equals(MATUTINO)) {
            this.setHora0500(alterarPara);
            this.setHora0530(alterarPara);
            this.setHora0600(alterarPara);
            this.setHora0630(alterarPara);
            this.setHora0700(alterarPara);
            this.setHora0730(alterarPara);
            this.setHora0800(alterarPara);
            this.setHora0830(alterarPara);
            this.setHora0900(alterarPara);
            this.setHora0930(alterarPara);
            this.setHora1000(alterarPara);
            this.setHora1030(alterarPara);
            this.setHora1100(alterarPara);
            this.setHora1130(alterarPara);
        } else if (periodo.equals(VESPERTINO)) {
            this.setHora1200(alterarPara);
            this.setHora1230(alterarPara);
            this.setHora1300(alterarPara);
            this.setHora1330(alterarPara);
            this.setHora1400(alterarPara);
            this.setHora1430(alterarPara);
            this.setHora1500(alterarPara);
            this.setHora1530(alterarPara);
            this.setHora1600(alterarPara);
            this.setHora1630(alterarPara);
            this.setHora1700(alterarPara);
            this.setHora1730(alterarPara);
        } else if (periodo.equals(NOTURNO)) {
            this.setHora1800(alterarPara);
            this.setHora1830(alterarPara);
            this.setHora1900(alterarPara);
            this.setHora1930(alterarPara);
            this.setHora2000(alterarPara);
            this.setHora2030(alterarPara);
            this.setHora2100(alterarPara);
            this.setHora2130(alterarPara);
            this.setHora2200(alterarPara);
            this.setHora2230(alterarPara);
            this.setHora2300(alterarPara);
            this.setHora2330(alterarPara);
        }else if(periodo.equals(MADRUGADA)){
            this.setHora0000(alterarPara);
            this.setHora0030(alterarPara);
            this.setHora0100(alterarPara);
            this.setHora0130(alterarPara);
            this.setHora0200(alterarPara);
            this.setHora0230(alterarPara);
            this.setHora0300(alterarPara);
            this.setHora0330(alterarPara);
            this.setHora0400(alterarPara);
            this.setHora0430(alterarPara);
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>HorarioDisponibilidadeVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(HorarioDisponibilidadeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        //     setIdentificador( identificador.toUpperCase() );
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setIdentificador("");
        setDesenharTodos(false);
        setHora0000(false);
        setHora0030(false);
        setHora0100(false);
        setHora0130(false);
        setHora0200(false);
        setHora0230(false);
        setHora0300(false);
        setHora0330(false);
        setHora0400(false);
        setHora0430(false);
        setHora0500(false);
        setHora0530(false);
        setHora0600(false);
        setHora0630(false);
        setHora0700(false);
        setHora0730(false);
        setHora0800(false);
        setHora0830(false);
        setHora0900(false);
        setHora0930(false);
        setHora1000(false);
        setHora1030(false);
        setHora1100(false);
        setHora1130(false);
        setHora1200(false);
        setHora1230(false);
        setHora1300(false);
        setHora1330(false);
        setHora1400(false);
        setHora1430(false);
        setHora1500(false);
        setHora1530(false);
        setHora1600(false);
        setHora1630(false);
        setHora1700(false);
        setHora1730(false);
        setHora1800(false);
        setHora1830(false);
        setHora1900(false);
        setHora1930(false);
        setHora2000(false);
        setHora2030(false);
        setHora2100(false);
        setHora2130(false);
        setHora2200(false);
        setHora2230(false);
        setHora2300(false);
        setHora2330(false);
        setMatutino(false);
        setVespertino(false);
        setNoturno(false);
    }

    public Boolean getHora2330() {
        return (hora2330);
    }

    public Boolean isHora2330() {
        return (hora2330);
    }

    public void setHora2330(Boolean hora2330) {
        this.hora2330 = hora2330;
    }

    public Boolean getHora2300() {
        return (hora2300);
    }

    public Boolean isHora2300() {
        return (hora2300);
    }

    public void setHora2300(Boolean hora2300) {
        this.hora2300 = hora2300;
    }

    public Boolean getHora2230() {
        return (hora2230);
    }

    public Boolean isHora2230() {
        return (hora2230);
    }

    public void setHora2230(Boolean hora2230) {
        this.hora2230 = hora2230;
    }

    public Boolean getHora2200() {
        return (hora2200);
    }

    public Boolean isHora2200() {
        return (hora2200);
    }

    public void setHora2200(Boolean hora2200) {
        this.hora2200 = hora2200;
    }

    public Boolean getHora2130() {
        return (hora2130);
    }

    public Boolean isHora2130() {
        return (hora2130);
    }

    public void setHora2130(Boolean hora2130) {
        this.hora2130 = hora2130;
    }

    public Boolean getHora2100() {
        return (hora2100);
    }

    public Boolean isHora2100() {
        return (hora2100);
    }

    public void setHora2100(Boolean hora2100) {
        this.hora2100 = hora2100;
    }

    public Boolean getHora2030() {
        return (hora2030);
    }

    public Boolean isHora2030() {
        return (hora2030);
    }

    public void setHora2030(Boolean hora2030) {
        this.hora2030 = hora2030;
    }

    public Boolean getHora2000() {
        return (hora2000);
    }

    public Boolean isHora2000() {
        return (hora2000);
    }

    public void setHora2000(Boolean hora2000) {
        this.hora2000 = hora2000;
    }

    public Boolean getHora1930() {
        return (hora1930);
    }

    public Boolean isHora1930() {
        return (hora1930);
    }

    public void setHora1930(Boolean hora1930) {
        this.hora1930 = hora1930;
    }

    public Boolean getHora1900() {
        return (hora1900);
    }

    public Boolean isHora1900() {
        return (hora1900);
    }

    public void setHora1900(Boolean hora1900) {
        this.hora1900 = hora1900;
    }

    public Boolean getHora1830() {
        return (hora1830);
    }

    public Boolean isHora1830() {
        return (hora1830);
    }

    public void setHora1830(Boolean hora1830) {
        this.hora1830 = hora1830;
    }

    public Boolean getHora1800() {
        return (hora1800);
    }

    public Boolean isHora1800() {
        return (hora1800);
    }

    public void setHora1800(Boolean hora1800) {
        this.hora1800 = hora1800;
    }

    public Boolean getHora1730() {
        return (hora1730);
    }

    public Boolean isHora1730() {
        return (hora1730);
    }

    public void setHora1730(Boolean hora1730) {
        this.hora1730 = hora1730;
    }

    public Boolean getHora1700() {
        return (hora1700);
    }

    public Boolean isHora1700() {
        return (hora1700);
    }

    public void setHora1700(Boolean hora1700) {
        this.hora1700 = hora1700;
    }

    public Boolean getHora1630() {
        return (hora1630);
    }

    public Boolean isHora1630() {
        return (hora1630);
    }

    public void setHora1630(Boolean hora1630) {
        this.hora1630 = hora1630;
    }

    public Boolean getHora1600() {
        return (hora1600);
    }

    public Boolean isHora1600() {
        return (hora1600);
    }

    public void setHora1600(Boolean hora1600) {
        this.hora1600 = hora1600;
    }

    public Boolean getHora1530() {
        return (hora1530);
    }

    public Boolean isHora1530() {
        return (hora1530);
    }

    public void setHora1530(Boolean hora1530) {
        this.hora1530 = hora1530;
    }

    public Boolean getHora1500() {
        return (hora1500);
    }

    public Boolean isHora1500() {
        return (hora1500);
    }

    public void setHora1500(Boolean hora1500) {
        this.hora1500 = hora1500;
    }

    public Boolean getHora1430() {
        return (hora1430);
    }

    public Boolean isHora1430() {
        return (hora1430);
    }

    public void setHora1430(Boolean hora1430) {
        this.hora1430 = hora1430;
    }

    public Boolean getHora1400() {
        return (hora1400);
    }

    public Boolean isHora1400() {
        return (hora1400);
    }

    public void setHora1400(Boolean hora1400) {
        this.hora1400 = hora1400;
    }

    public Boolean getHora1330() {
        return (hora1330);
    }

    public Boolean isHora1330() {
        return (hora1330);
    }

    public void setHora1330(Boolean hora1330) {
        this.hora1330 = hora1330;
    }

    public Boolean getHora1300() {
        return (hora1300);
    }

    public Boolean isHora1300() {
        return (hora1300);
    }

    public void setHora1300(Boolean hora1300) {
        this.hora1300 = hora1300;
    }

    public Boolean getHora1230() {
        return (hora1230);
    }

    public Boolean isHora1230() {
        return (hora1230);
    }

    public void setHora1230(Boolean hora1230) {
        this.hora1230 = hora1230;
    }

    public Boolean getHora1200() {
        return (hora1200);
    }

    public Boolean isHora1200() {
        return (hora1200);
    }

    public void setHora1200(Boolean hora1200) {
        this.hora1200 = hora1200;
    }

    public Boolean getHora1130() {
        return (hora1130);
    }

    public Boolean isHora1130() {
        return (hora1130);
    }

    public void setHora1130(Boolean hora1130) {
        this.hora1130 = hora1130;
    }

    public Boolean getHora1100() {
        return (hora1100);
    }

    public Boolean isHora1100() {
        return (hora1100);
    }

    public void setHora1100(Boolean hora1100) {
        this.hora1100 = hora1100;
    }

    public Boolean getHora1030() {
        return (hora1030);
    }

    public Boolean isHora1030() {
        return (hora1030);
    }

    public void setHora1030(Boolean hora1030) {
        this.hora1030 = hora1030;
    }

    public Boolean getHora1000() {
        return (hora1000);
    }

    public Boolean isHora1000() {
        return (hora1000);
    }

    public void setHora1000(Boolean hora1000) {
        this.hora1000 = hora1000;
    }

    public Boolean getHora0930() {
        return (hora0930);
    }

    public Boolean isHora0930() {
        return (hora0930);
    }

    public void setHora0930(Boolean hora0930) {
        this.hora0930 = hora0930;
    }

    public Boolean getHora0900() {
        return (hora0900);
    }

    public Boolean isHora0900() {
        return (hora0900);
    }

    public void setHora0900(Boolean hora0900) {
        this.hora0900 = hora0900;
    }

    public Boolean getHora0830() {
        return (hora0830);
    }

    public Boolean isHora0830() {
        return (hora0830);
    }

    public void setHora0830(Boolean hora0830) {
        this.hora0830 = hora0830;
    }

    public Boolean getHora0800() {
        return (hora0800);
    }

    public Boolean isHora0800() {
        return (hora0800);
    }

    public void setHora0800(Boolean hora0800) {
        this.hora0800 = hora0800;
    }

    public Boolean getHora0730() {
        return (hora0730);
    }

    public Boolean isHora0730() {
        return (hora0730);
    }

    public void setHora0730(Boolean hora0730) {
        this.hora0730 = hora0730;
    }

    public Boolean getHora0700() {
        return (hora0700);
    }

    public Boolean isHora0700() {
        return (hora0700);
    }

    public void setHora0700(Boolean hora0700) {
        this.hora0700 = hora0700;
    }

    public Boolean getHora0630() {
        return (hora0630);
    }

    public Boolean isHora0630() {
        return (hora0630);
    }

    public void setHora0630(Boolean hora0630) {
        this.hora0630 = hora0630;
    }

    public Boolean getHora0600() {
        return (hora0600);
    }

    public Boolean isHora0600() {
        return (hora0600);
    }

    public void setHora0600(Boolean hora0600) {
        this.hora0600 = hora0600;
    }

    public Boolean getHora0530() {
        return (hora0530);
    }

    public Boolean isHora0530() {
        return (hora0530);
    }

    public void setHora0530(Boolean hora0530) {
        this.hora0530 = hora0530;
    }

    public Boolean getHora0500() {
        return (hora0500);
    }

    public Boolean isHora0500() {
        return (hora0500);
    }

    public void setHora0500(Boolean hora0500) {
        this.hora0500 = hora0500;
    }

    public Boolean getHora0430() {
        return (hora0430);
    }

    public Boolean isHora0430() {
        return (hora0430);
    }

    public void setHora0430(Boolean hora0430) {
        this.hora0430 = hora0430;
    }

    public Boolean getHora0400() {
        return (hora0400);
    }

    public Boolean isHora0400() {
        return (hora0400);
    }

    public void setHora0400(Boolean hora0400) {
        this.hora0400 = hora0400;
    }

    public Boolean getHora0330() {
        return (hora0330);
    }

    public Boolean isHora0330() {
        return (hora0330);
    }

    public void setHora0330(Boolean hora0330) {
        this.hora0330 = hora0330;
    }

    public Boolean getHora0300() {
        return (hora0300);
    }

    public Boolean isHora0300() {
        return (hora0300);
    }

    public void setHora0300(Boolean hora0300) {
        this.hora0300 = hora0300;
    }

    public Boolean getHora0230() {
        return (hora0230);
    }

    public Boolean isHora0230() {
        return (hora0230);
    }

    public void setHora0230(Boolean hora0230) {
        this.hora0230 = hora0230;
    }

    public Boolean getHora0200() {
        return (hora0200);
    }

    public Boolean isHora0200() {
        return (hora0200);
    }

    public void setHora0200(Boolean hora0200) {
        this.hora0200 = hora0200;
    }

    public Boolean getHora0130() {
        return (hora0130);
    }

    public Boolean isHora0130() {
        return (hora0130);
    }

    public void setHora0130(Boolean hora0130) {
        this.hora0130 = hora0130;
    }

    public Boolean getHora0100() {
        return (hora0100);
    }

    public Boolean isHora0100() {
        return (hora0100);
    }

    public void setHora0100(Boolean hora0100) {
        this.hora0100 = hora0100;
    }

    public Boolean getHora0030() {
        return (hora0030);
    }

    public Boolean isHora0030() {
        return (hora0030);
    }

    public void setHora0030(Boolean hora0030) {
        this.hora0030 = hora0030;
    }

    public Boolean getHora0000() {
        return (hora0000);
    }

    public Boolean isHora0000() {
        return (hora0000);
    }

    public void setHora0000(Boolean hora0000) {
        this.hora0000 = hora0000;
    }

    public Integer getHorario() {
        return (horario);
    }

    public void setHorario(Integer horario) {
        this.horario = horario;
    }

    public String getIdentificador() {
        if (identificador == null) {
            identificador = "";
        }
        return (identificador);
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getDesenharTodos() {
        return desenharTodos;
    }

    public void setDesenharTodos(Boolean desenharTodos) {
        this.desenharTodos = desenharTodos;
    }

    public Boolean getMatutino() {
        return matutino;
    }

    public void setMatutino(Boolean matutino) {
        this.matutino = matutino;
    }

    public Boolean getNoturno() {
        return noturno;
    }

    public void setNoturno(Boolean noturno) {
        this.noturno = noturno;
    }

    public Boolean getMadrugada() {
        return madrugada;
    }

    public void setMadrugada(Boolean madrugada) {
        this.madrugada = madrugada;
    }

    public Boolean getVespertino() {
        return vespertino;
    }

    public void setVespertino(Boolean vespertino) {
        this.vespertino = vespertino;
    }

    public String montarStringHorariosPermitidos() {
        List<String> atributos = UtilReflection.getListAttributes(HorarioDisponibilidadeVO.class,
                "hora", 8, Boolean.class);
        StringBuilder ret = new StringBuilder();

        String inicio = "";
        String fim = "";
        for (String att : atributos) {
            if (Boolean.valueOf(UtilReflection.getValor(this, att).toString())) {
                if (inicio.isEmpty()) {
                    inicio = att.substring(4, 6) + ":" + att.substring(6, 8);
                }
            } else {
                if (!inicio.isEmpty()) {
                    fim = att.substring(4, 6) + ":" + att.substring(6, 8);
                    ret.append(inicio).append("-").append(fim).append("_");
                    inicio = "";
                    fim = "";
                }
            }
        }

        if (!inicio.isEmpty() && fim.isEmpty()) {
            // Chegou ao fim e o fim não foi definido
            ret.append(inicio).append("-23:59_");
        }

        return ret.toString() + "_";
    }

    public Integer getOrdenar() {
        DiaSemana dia = DiaSemana.getDiaSemanaDescSimples(identificador);
        if (dia == null) {
            return 0;
        }
        return dia.getNumeral();

    }
}

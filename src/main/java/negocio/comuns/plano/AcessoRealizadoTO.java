package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;

import java.time.LocalDateTime;

public class AcessoRealizadoTO extends SuperVO {

    private Integer codigoCliente;
    private String matriculaAluno;
    private String nomeAluno;
    private String meioIdentificacao;
    private String dataRegistroFormatada;
    private LocalDateTime dataRegistro;

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatriculaAluno() {
        return matriculaAluno;
    }

    public void setMatriculaAluno(String matriculaAluno) {
        this.matriculaAluno = matriculaAluno;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getMeioIdentificacao() {
        return meioIdentificacao;
    }

    public void setMeioIdentificacao(String meioIdentificacao) {
        this.meioIdentificacao = meioIdentificacao;
    }

    public String getDataRegistroFormatada() {
        return dataRegistroFormatada;
    }

    public void setDataRegistroFormatada(String dataRegistroFormatada) {
        this.dataRegistroFormatada = dataRegistroFormatada;
    }

    public LocalDateTime getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(LocalDateTime dataRegistro) {
        this.dataRegistro = dataRegistro;
    }
}

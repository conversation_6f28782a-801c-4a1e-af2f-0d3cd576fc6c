package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.contrato.ComissaoProdutoConfiguracaoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.ArrayList;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade CategoriaProduto. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class CategoriaProdutoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    private boolean bloquearAcessoSeProdutoAberto = false;
    private List<ComissaoProdutoConfiguracaoVO> comissaoCategoriaProdutos;
    private Boolean avaliacaoFisica;
    private FormaPagamentoVO formaPagamento;
    /**
     * Construtor padrão da classe <code>CategoriaProduto</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public CategoriaProdutoVO() {
        super();
        inicializarDados();
    }


    /**
     * Operação responsável por validar os dados de um objeto da classe <code>CategoriaProdutoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(CategoriaProdutoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Categoria de Produto) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setDescricao("");
    }


    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public boolean isBloquearAcessoSeProdutoAberto() {
        return bloquearAcessoSeProdutoAberto;
    }

    public void setBloquearAcessoSeProdutoAberto(boolean bloquearAcessoSeProdutoAberto) {
        this.bloquearAcessoSeProdutoAberto = bloquearAcessoSeProdutoAberto;
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CategoriaProdutoVO other = (CategoriaProdutoVO) obj;
        if (this.codigo != other.codigo && (this.codigo == null || !this.codigo.equals(other.codigo))) {
            return false;
        }
        return (this.descricao == null) ? (other.descricao == null) : this.descricao.equals(other.descricao);
    }

    @Override
    public int hashCode() {
        return 3;
    }

    public List<ComissaoProdutoConfiguracaoVO> getComissaoCategoriaProdutos() {
        if (comissaoCategoriaProdutos == null) {
            comissaoCategoriaProdutos = new ArrayList<>();
        }
        return comissaoCategoriaProdutos;
    }

    public void setComissaoCategoriaProdutos(List<ComissaoProdutoConfiguracaoVO> comissaoCategoriaProdutos) {
        this.comissaoCategoriaProdutos = comissaoCategoriaProdutos;
    }

    public Boolean getAvaliacaoFisica() {
        if (avaliacaoFisica == null) {
            avaliacaoFisica = false;
        }
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(Boolean avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public FormaPagamentoVO getFormaPagamento() {
        if (formaPagamento == null){
            formaPagamento = new FormaPagamentoVO();
        }
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamentoVO formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.plano;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class AlunoHorarioTurmaTO extends SuperVO{

    private Integer codigoCliente;
    private String matricula;
    private String nome;
    private String inicioAula;
    private String finalAula;
    private String descricaoAula;

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getInicioAula() {
        return inicioAula;
    }

    public void setInicioAula(String inicioAula) {
        this.inicioAula = inicioAula;
    }

    public String getFinalAula() {
        return finalAula;
    }

    public void setFinalAula(String finalAula) {
        this.finalAula = finalAula;
    }

    public String getDescricaoAula() {
        return descricaoAula;
    }

    public void setDescricaoAula(String descricaoAula) {
        this.descricaoAula = descricaoAula;
    }
}

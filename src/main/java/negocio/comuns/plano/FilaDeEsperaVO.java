/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.plano;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.SuperVO;
import org.joda.time.DateTime;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class FilaDeEsperaVO extends SuperVO{

    private String matricula;
    private Integer codigoHorarioTurma;
    private String dia;
    private Integer codigoAluno;
    private Date dataEntrada;
    private Integer ordem;
    private String vinculoComAula;

    public Integer getCodigoHorarioTurma() {
        return codigoHorarioTurma;
    }

    public void setCodigoHorarioTurma(Integer codigoHorarioTurma) {
        this.codigoHorarioTurma = codigoHorarioTurma;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public Integer getCodigoAluno() {
        return codigoAluno;
    }

    public void setCodigoAluno(Integer codigoAluno) {
        this.codigoAluno = codigoAluno;
    }

    public Date getDataEntrada() {
        return dataEntrada;
    }

    public void setDataEntrada(Date dataEntrada) {
        this.dataEntrada = dataEntrada;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getVinculoComAula() {
        return vinculoComAula;
    }

    public void setVinculoComAula(String vinculoComAula) {
        this.vinculoComAula = vinculoComAula;
    }


}

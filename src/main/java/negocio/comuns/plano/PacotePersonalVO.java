/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.plano;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class PacotePersonalVO extends SuperVO {

    private Integer produto;
    private Integer quantidade = 1;
    private Double valorPosPago = 0.0;
    private Double valorPrePago = 0.0;

    public PacotePersonalVO() {
    }

    public String getValorPosPagoApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorPosPago);
    }
    public String getValorPrePagoApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorPrePago);
    }

    public PacotePersonalVO(Integer produto, Integer quantidade, Double valorPosPago, Double valorPrePago) {
        this.produto = produto;
        this.quantidade = quantidade;
        this.valorPosPago = valorPosPago;
        this.valorPrePago = valorPrePago;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Double getValorPosPago() {
        return valorPosPago;
    }

    public void setValorPosPago(Double valorPosPago) {
        this.valorPosPago = valorPosPago;
    }

    public Double getValorPrePago() {
        return valorPrePago;
    }

    public void setValorPrePago(Double valorPrePago) {
        this.valorPrePago = valorPrePago;
    }

    
}

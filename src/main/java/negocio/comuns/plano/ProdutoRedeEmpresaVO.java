package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import servicos.discovery.RedeDTO;

import java.util.Date;

public class ProdutoRedeEmpresaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer produto;
    private String chave;
    private Date datacadastro;
    private Date dataAtualizacao;
    private Integer produtoReplicado;
    private String nomeUnidade;
    private String mensagemSituacao;
    private RedeDTO redeDTO;
    private boolean selecionado;
    private Integer codigoEmpresaDestino;

    public ProdutoRedeEmpresaVO(Integer produto, String chave, Integer produtoReplicado, Integer codigoEmpresaDestino) {
        this.produto = produto;
        this.chave = chave;
        this.produtoReplicado = produtoReplicado;
        this.codigoEmpresaDestino = codigoEmpresaDestino;
    }

    public ProdutoRedeEmpresaVO(String nomeUnidade, Integer produto, String chave, Integer produtoReplicado) {
        this.nomeUnidade = nomeUnidade;
        this.produto = produto;
        this.chave = chave;
        this.produtoReplicado = produtoReplicado;
    }

    public ProdutoRedeEmpresaVO() {

    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Date getDatacadastro() {
        return datacadastro;
    }

    public void setDatacadastro(Date datacadastro) {
        this.datacadastro = datacadastro;
    }

    public Boolean getDataAtualizacaoInformada() {
        return getDataAtualizacao() != null;
    }
    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }

    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }

    public Integer getProdutoReplicado() {
        return produtoReplicado;
    }

    public void setProdutoReplicado(Integer produtoReplicado) {
        this.produtoReplicado = produtoReplicado;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getMensagemSituacao() {
        return mensagemSituacao;
    }

    public void setMensagemSituacao(String mensagemSituacao) {
        this.mensagemSituacao = mensagemSituacao;
    }

    public RedeDTO getRedeDTO() {
        return redeDTO;
    }

    public void setRedeDTO(RedeDTO redeDTO) {
        this.redeDTO = redeDTO;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Integer getCodigoEmpresaDestino() {
        return codigoEmpresaDestino;
    }

    public void setCodigoEmpresaDestino(Integer codigoEmpresaDestino) {
        this.codigoEmpresaDestino = codigoEmpresaDestino;
    }
}

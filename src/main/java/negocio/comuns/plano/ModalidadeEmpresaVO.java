package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade ModalidadeEmpresa. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see ModalidadeVO
 */
public class ModalidadeEmpresaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer modalidade;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Empresa </code>.*/
    @FKJson
    @ChaveEstrangeira
    protected EmpresaVO empresa;

    /**
     * Construtor padrão da classe <code>ModalidadeEmpresa</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ModalidadeEmpresaVO() {
        super();
        inicializarDados();
    }

    public ModalidadeEmpresaVO(int codigoEmpresa, String nomeEmpresa, int codigoModalidade) {
        getEmpresa().setCodigo(codigoEmpresa);
        getEmpresa().setNome(nomeEmpresa);
        setModalidade(codigoModalidade);
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ModalidadeEmpresaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ModalidadeEmpresaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getEmpresa() == null)
                || (obj.getEmpresa().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo EMPRESA (Modalidade Empresa) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setEmpresa(new EmpresaVO());
    }

    /**
     * Retorna o objeto da classe <code>Empresa</code> relacionado com (<code>ModalidadeEmpresa</code>).
     */
    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return (empresa);
    }

    /**
     * Define o objeto da classe <code>Empresa</code> relacionado com (<code>ModalidadeEmpresa</code>).
     */
    public void setEmpresa(EmpresaVO obj) {
        this.empresa = obj;
    }

    public Integer getModalidade() {
        return (modalidade);
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

}

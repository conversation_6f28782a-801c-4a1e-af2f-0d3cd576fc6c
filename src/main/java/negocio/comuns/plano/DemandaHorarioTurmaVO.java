package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;

import java.util.Date;

public class DemandaHorarioTurmaVO  extends SuperVO implements Cloneable {
    @ChavePrimaria
    protected Integer codigo;
    @ChaveEstrangeira
    protected ClienteVO cliente;
    @ChaveEstrangeira
    protected HorarioTurmaVO horarioTurma;
    protected Date data;
    protected Date dataProcura;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public HorarioTurmaVO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaVO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Date getDataProcura() {
        return dataProcura;
    }

    public void setDataProcura(Date dataProcura) {
        this.dataProcura = dataProcura;
    }

}

package negocio.comuns.plano;

import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade VezesSemana. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class VezesSemanaVO extends SuperVO {

    protected Integer codigo;
    protected Integer nrVezes;   
    protected Boolean vezeSemanaDefault;
    protected Boolean vezeSemanaEscolhida;

    /**
     * Construtor padrão da classe <code>VezesSemana</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public VezesSemanaVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>VezesSemanaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(VezesSemanaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getNrVezes().intValue() == 0) {
            throw new ConsistirException("O campo NÚMERO DE VEZES (Vezes Semana) deve ser informado.");
        }
           }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setNrVezes(new Integer(0));      
        setVezeSemanaDefault(new Boolean(false));
        setVezeSemanaEscolhida(new Boolean(false));
    }

    public String getVezeSemanaDefault_Apresentar() {
        if (vezeSemanaDefault) {
            return "Sim";
        }
        if (!vezeSemanaDefault) {
            return "Não";
        }
        return "";
    }

    public Boolean getVezeSemanaDefault() {
        return (vezeSemanaDefault);
    }

    public Boolean isVezeSemanaDefault() {
        return (vezeSemanaDefault);
    }

    public void setVezeSemanaDefault(Boolean vezeSemanaDefault) {
        this.vezeSemanaDefault = vezeSemanaDefault;
    }

  
    public Integer getNrVezes() {
        return (nrVezes);
    }

    public void setNrVezes(Integer nrVezes) {
        this.nrVezes = nrVezes;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

   
    public Boolean getVezeSemanaEscolhida() {
        return vezeSemanaEscolhida;
    }

    public void setVezeSemanaEscolhida(Boolean vezeSemanaEscolhida) {
        this.vezeSemanaEscolhida = vezeSemanaEscolhida;
    }
}

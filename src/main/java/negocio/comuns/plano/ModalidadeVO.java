package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade Modalidade. Classe do tipo VO -
 * Value Object composta pelos atributos da entidade com visibilidade protegida
 * e os métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class ModalidadeVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer nrVezes;
    protected String nome;
    protected Boolean ativo;
    protected Double valorMensal;
    protected Boolean crossfit;
    protected Boolean utilizarTurma;
    @NaoControlarLogAlteracao
    protected Boolean utilizarProduto;
    protected Boolean modalidadeDefault;
    @NaoControlarLogAlteracao
    protected Boolean modalidadeEscolhida;
    @NaoControlarLogAlteracao
    protected Boolean composicao;
    @NaoControlarLogAlteracao
    private  Boolean modalidadEscolhida;
    @NaoControlarLogAlteracao
    private int composicaoLivre;
    @Lista
    @ListJson(clazz = ProdutoSugeridoVO.class)
    private List produtoSugeridoVOs;
    @Lista
    @ListJson(clazz = ModalidadeEmpresaVO.class)
    private List<ModalidadeEmpresaVO> modalidadeEmpresaVOs;
    private boolean parteComposicao;
    private String empresa;
    private boolean selecionado = false;
    @NaoControlarLogAlteracao
    private Double valorOriginal;
    private Boolean usaTreino = true;
    @NaoControlarLogAlteracao
    private boolean editarModalidade = false;
    @NaoControlarLogAlteracao
    private boolean pendente = false;
    @NaoControlarLogAlteracao
    private Double valorMensalFinal = 0.0;

    private String fotokey="";

    private Integer tipo;

    private boolean integracaoSpivi = false;
    private boolean modalidadeTurmaParaSelecionar;
    public boolean modalidadeSemTurma = false;
    @NaoControlarLogAlteracao
    private boolean editarModalidadeSemTurma = false;
    private Integer frequenciasPossiveis = 0;
    private Integer vagasDisponiveis = 0;

    /**
     * Construtor padrão da classe <code>Modalidade</code>. Cria uma nova
     * instância desta entidade, inicializando automaticamente seus atributos
     * (Classe VO).
     */
    public ModalidadeVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ModalidadeVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDados(ModalidadeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getNome().equals("")) {
            throw new ConsistirException("O campo NOME (Modalidade) deve ser informado.");
        }
        /*if (obj.getTipo() == null || obj.getTipo() == 0) {
            throw new ConsistirException("O campo TIPO (Modalidade) deve ser informado.");
        }*/
        if (obj.getNrVezes() == 0) {
            throw new ConsistirException("O campo NÚMERO DE VEZES POR SEMANA (Modalidade) deve ser informado.");
        }
        if (obj.getProdutoSugeridoVOs().size() > 0) {
            obj.setUtilizarProduto(true);
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        setNome(getNome().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ModalidadeEmpresaVO</code> ao List
     * <code>modalidadeEmpresaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ModalidadeEmpresa</code> - getEmpresa().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ModalidadeEmpresaVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjModalidadeEmpresaVOs(ModalidadeEmpresaVO obj) throws Exception {
        ModalidadeEmpresaVO.validarDados(obj);
        int index = 0;
        Iterator i = getModalidadeEmpresaVOs().iterator();
        while (i.hasNext()) {
            ModalidadeEmpresaVO objExistente = (ModalidadeEmpresaVO) i.next();
            if (objExistente.getEmpresa().getCodigo().equals(obj.getEmpresa().getCodigo())) {
                getModalidadeEmpresaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getModalidadeEmpresaVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>ModalidadeEmpresaVO</code> no List
     * <code>modalidadeEmpresaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ModalidadeEmpresa</code> - getEmpresa().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param empresa Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjModalidadeEmpresaVOs(Integer empresa) {
        int index = 0;
        Iterator i = getModalidadeEmpresaVOs().iterator();
        while (i.hasNext()) {
            ModalidadeEmpresaVO objExistente = (ModalidadeEmpresaVO) i.next();
            if (objExistente.getEmpresa().getCodigo().equals(empresa)) {
                getModalidadeEmpresaVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>ModalidadeEmpresaVO</code> no List
     * <code>modalidadeEmpresaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ModalidadeEmpresa</code> - getEmpresa().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param empresa Parâmetro para localizar o objeto do List.
     */
    public ModalidadeEmpresaVO consultarObjModalidadeEmpresaVO(Integer empresa) throws Exception {
        Iterator i = getModalidadeEmpresaVOs().iterator();
        while (i.hasNext()) {
            ModalidadeEmpresaVO objExistente = (ModalidadeEmpresaVO) i.next();
            if (objExistente.getEmpresa().getCodigo().equals(empresa)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ProdutoSugeridoVO</code> ao List <code>produtoSugeridoVOs</code>.
     * Utiliza o atributo padrão de consulta da classe
     * <code>ProdutoSugerido</code> - getModalidade().getCodigo() - como
     * identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ProdutoSugeridoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjProdutoSugeridoVOs(ProdutoSugeridoVO obj) throws Exception {
        ProdutoSugeridoVO.validarDados(obj);
        int index = 0;
        Iterator i = getProdutoSugeridoVOs().iterator();
        while (i.hasNext()) {
            ProdutoSugeridoVO objExistente = (ProdutoSugeridoVO) i.next();
            if (objExistente.getProduto().getCodigo().equals(obj.getProduto().getCodigo())) {
                getProdutoSugeridoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getProdutoSugeridoVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>ProdutoSugeridoVO</code> no List <code>produtoSugeridoVOs</code>.
     * Utiliza o atributo padrão de consulta da classe
     * <code>ProdutoSugerido</code> - getModalidade().getCodigo() - como
     * identificador (key) do objeto no List.
     */
    public void excluirObjProdutoSugeridoVOs(Integer produto) throws Exception {
        int index = 0;
        Iterator i = getProdutoSugeridoVOs().iterator();
        while (i.hasNext()) {
            ProdutoSugeridoVO objExistente = (ProdutoSugeridoVO) i.next();
            if (objExistente.getProduto().getCodigo().equals(produto)) {
                getProdutoSugeridoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>ProdutoSugeridoVO</code> no List <code>produtoSugeridoVOs</code>.
     * Utiliza o atributo padrão de consulta da classe
     * <code>ProdutoSugerido</code> - getModalidade().getCodigo() - como
     * identificador (key) do objeto no List.
     */
    public ProdutoSugeridoVO consultarObjProdutoSugeridoVO(Integer produto) throws Exception {
        Iterator i = getProdutoSugeridoVOs().iterator();
        while (i.hasNext()) {
            ProdutoSugeridoVO objExistente = (ProdutoSugeridoVO) i.next();
            if (objExistente.getProduto().getCodigo().equals(produto)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe
     * <code>ModalidadeEmpresa</code>.
     */
    public List<ModalidadeEmpresaVO> getModalidadeEmpresaVOs() {
        if (modalidadeEmpresaVOs == null) {
            modalidadeEmpresaVOs = new ArrayList<ModalidadeEmpresaVO>();
        }
        return (modalidadeEmpresaVOs);
    }

    /**
     * Define Atributo responsável por manter os objetos da classe
     * <code>ModalidadeEmpresa</code>.
     */
    public void setModalidadeEmpresaVOs(List modalidadeEmpresaVOs) {
        this.modalidadeEmpresaVOs = modalidadeEmpresaVOs;
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe
     * <code>ProdutoSugerido</code>.
     */
    public List getProdutoSugeridoVOs() {
        if (produtoSugeridoVOs == null) {
            produtoSugeridoVOs = new ArrayList();
        }
        return (produtoSugeridoVOs);
    }

    /**
     * Define Atributo responsável por manter os objetos da classe
     * <code>ProdutoSugerido</code>.
     */
    public void setProdutoSugeridoVOs(List produtoSugeridoVOs) {
        this.produtoSugeridoVOs = produtoSugeridoVOs;
    }

    public Boolean getUtilizarTurma() {
        return (utilizarTurma);
    }

    public void setUtilizarTurma(Boolean utilizarTurma) {
        this.utilizarTurma = utilizarTurma;
    }

    public Boolean isUtilizarTurma() {
        if (utilizarTurma == null) {
            utilizarTurma = false;
        }
        return (utilizarTurma);
    }

    public Double getValorMensal() {
        if (valorMensal == null) {
            valorMensal = 0.0;
        }
        return (valorMensal);
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public String getValorMensal_Apresentar() {
        if (valorMensal == null) {
            valorMensal = 0.0;
        }
        return Formatador.formatarValorMonetario(valorMensal);
    }

    public Boolean getAtivo() {
        if (ativo == null) {
            ativo = false;
        }
        return (ativo);
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean isAtivo() {
        if (ativo == null) {
            ativo = false;
        }
        return (ativo);
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return (nome);
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getModalidadeDefault() {
        if (modalidadeDefault == null) {
            modalidadeDefault = false;
        }
        return modalidadeDefault;
    }

    public void setModalidadeDefault(Boolean modalidadeDefault) {
        this.modalidadeDefault = modalidadeDefault;
    }

    public Boolean getModalidadeEscolhida() {
        if (modalidadeEscolhida == null) {
            modalidadeEscolhida = false;
        }
        return modalidadeEscolhida;
    }

    public void setModalidadeEscolhida(Boolean modalidadeEscolhida) {
        this.modalidadeEscolhida = modalidadeEscolhida;
    }

    public Boolean getUtilizarProduto() {
        if (utilizarProduto == null) {
            utilizarProduto = false;
        }
        return utilizarProduto;
    }

    public void setUtilizarProduto(Boolean utilizarProduto) {
        this.utilizarProduto = utilizarProduto;
    }

    public Integer getNrVezes() {
        if (nrVezes == null) {
            nrVezes = 0;
        }
        return nrVezes;
    }

    public void setNrVezes(Integer nrVezes) {
        this.nrVezes = nrVezes;
    }

    public Boolean getComposicao() {
        if (composicao == null) {
            composicao = false;
        }
        return composicao;
    }

    public void setComposicao(Boolean composicao) {
        this.composicao = composicao;
    }

    public int getComposicaoLivre() {
        return composicaoLivre;
    }

    public void setComposicaoLivre(int composicaoLivre) {
        this.composicaoLivre = composicaoLivre;
    }

    public boolean isParteComposicao() {
        return parteComposicao;
    }

    public void setParteComposicao(boolean parteComposicao) {
        this.parteComposicao = parteComposicao;
    }

    public String getEmpresas() {
        if (modalidadeEmpresaVOs != null && !modalidadeEmpresaVOs.isEmpty()) {
            String empresas = "";
            List<ModalidadeEmpresaVO> l = modalidadeEmpresaVOs;
            for (ModalidadeEmpresaVO me : l) {
                empresas += String.format("%s, ", me.getEmpresa().getNome());
            }
            return empresas.substring(0, empresas.length() - 2);
        } else {
            return "TODAS";
        }
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getSituacao() {
        if (this.getAtivo()) {
            return "Ativo";
        } else {
            return "Inativo";
        }
    }

    public boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Double getValorOriginal() {
        return valorOriginal;
    }

    public void setValorOriginal(Double valorOriginal) {
        this.valorOriginal = valorOriginal;
    }

    public Boolean getUsaTreino() {
        if(usaTreino == null){
            usaTreino = true;
        }
        return usaTreino;
    }

    public Double getValorMensalFinal() {
        return valorMensalFinal <= 0.0 ? valorMensal : valorMensalFinal;
    }

    public void setValorMensalFinal(Double valorMensalFinal) {
        this.valorMensalFinal = valorMensalFinal;
    }

    public void setUsaTreino(Boolean usaTreino) {
        this.usaTreino = usaTreino;
    }

    public boolean isEditarModalidade() {
        return editarModalidade;
    }

    public boolean isPendente() {
        return pendente;
    }

    public void setPendente(boolean pendente) {
        this.pendente = pendente;
    }

    public void setEditarModalidade(boolean editarModalidade) {
        this.editarModalidade = editarModalidade;
    }

    public Boolean getCrossfit() {
        if(crossfit == null){
            crossfit = false;
        }
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }

    public String getFotokey() {
        return fotokey;
    }

    public void setFotokey(String fotokey) {
        this.fotokey = fotokey;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }


    public boolean isIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public void setIntegracaoSpivi(final boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public boolean isModalidadeTurmaParaSelecionar() {
        return modalidadeTurmaParaSelecionar;
    }

    public void setModalidadeTurmaParaSelecionar(boolean modalidadeTurmaParaSelecionar) {
        this.modalidadeTurmaParaSelecionar = modalidadeTurmaParaSelecionar;
    }

    public boolean isModalidadeSemTurma() {
        return modalidadeSemTurma;
    }

    public void setModalidadeSemTurma(boolean modalidadeSemTurma) {
        this.modalidadeSemTurma = modalidadeSemTurma;
    }

    public boolean isEditarModalidadeSemTurma() {
        return editarModalidadeSemTurma;
    }

    public void setEditarModalidadeSemTurma(boolean editarModalidadeSemTurma) {
        this.editarModalidadeSemTurma = editarModalidadeSemTurma;
    }

    public Integer getFrequenciasPossiveis() {
        return this.frequenciasPossiveis;
    }

    public void setFrequenciasPossiveis(final Integer frequenciasPossiveis) {
        this.frequenciasPossiveis = frequenciasPossiveis;
    }

    public Integer getVagasDisponiveis() {
        return this.vagasDisponiveis;
    }

    public void setVagasDisponiveis(final Integer vagasDisponiveis) {
        this.vagasDisponiveis = vagasDisponiveis;
    }

    public int compareTo(Object obj) {
        if (obj instanceof ModalidadeVO) {
            return getNome().compareTo(((ModalidadeVO)obj).getNome());
        }
        return -1;
    }
}

package negocio.comuns.plano;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;


public class ConfiguracaoProdutoNotaFiscalVO extends SuperVO {

    private ProdutoVO produtoVO;

    public ConfiguracaoProdutoNotaFiscalVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    public static void validarDados(ConfiguracaoProdutoNotaFiscalVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (UteisValidacao.emptyNumber(obj.getProdutoVO().getCodigo())) {
            throw new ConsistirException("O campo PRODUTO (Configuração do Produto na Empresa) deve ser informado.");
        }
//        if (obj.getEmpresa() == null || UteisValidacao.emptyNumber(obj.getEmpresa().getCodigo())) {
//            throw new ConsistirException("O campo EMPRESA (Configuração do Produto na Empresa) deve ser informado.");
//        }
//        if (obj.getValor() == null) {
//            throw new ConsistirException("O campo VALOR (Configuração do Produto na Empresa) não pode ser vazio.");
//        }

    }

    public ProdutoVO getProdutoVO() {
        if (produtoVO == null) {
            produtoVO = new ProdutoVO();
        }
        return produtoVO;
    }

    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }
}

package negocio.comuns.pactoprint;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import org.json.JSONObject;

import java.util.Date;

/**
 * date : 01/04/2015 13:28:26
 * autor: Ulisses
 */
public class LocalImpressaoVO extends SuperVO {

    private Integer codigo;
    private String nome;
    private String nomeComputador;
    private EmpresaVO empresaVO;
    private Date dataLancamento;

    public static final String CHAVE_LOCAL_IMPRESSAO = "keyLocalImpressao";

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomeComputador() {
        return nomeComputador;
    }

    public void setNomeComputador(String nomeComputador) {
        this.nomeComputador = nomeComputador;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public JSONObject getJson() throws Exception {
        JSONObject jSONObject = new JSONObject();
        jSONObject.put("codigo", getCodigo());
        jSONObject.put("nome", getNome());
        jSONObject.put("nomeComputador", getNomeComputador());
        jSONObject.put("empresa", getEmpresaVO().getCodigo());
        return jSONObject;
    }

    public void validarDados(LocalImpressaoVO localImpressaoVO) throws ConsistirException {
        if ((localImpressaoVO.getNome() == null) || (localImpressaoVO.getNome().trim().equals(""))) {
            throw new ConsistirException("O campo Nome (Local Impressão) deve ser informado.");
        }
        if ((localImpressaoVO.getNomeComputador() == null) || (localImpressaoVO.getNomeComputador().trim().equals(""))) {
            throw new ConsistirException("O campo Nome Computador (Local Impressão) deve ser informado.");
        }
        if ((localImpressaoVO.getEmpresaVO().getCodigo() == null) || localImpressaoVO.getEmpresaVO().getCodigo() == 0) {
            throw new ConsistirException("O campo Empresa (Local Impressão) deve ser informado.");
        }
    }

}

package negocio.comuns.pactoprint;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;

public class CarteirinhaClienteVO extends SuperVO {

	private Integer   codigo;
	private ClienteVO clienteVO;
	private Date      dataLancamento;
	private UsuarioVO usuarioVO;
	private String    codigoAcesso;
	private Date      dataImpressao;
	private String    urlFoto;
	private Date 	  validadeAnterior;
	private MovParcelaVO movParcelaVO;
	private Integer    via;
	private ColaboradorVO colaboradorVO;
	private Integer origemAtualizacaoCodigoAcesso;
	
	private String  nomeCategoria;
	private String  nomeCategoriaTitular;
	private String  nomeSituacao;
	private Date    dataValidadeCarteirinha;
	private boolean pagamentoAVista = true;
	private String nomeDepartamento;
	private boolean concessionario ;
	private String profissao;
	private Date vigenciaInicialConselho;
	private Date vigenciaFinalConselho;
	private String nomeEmpresa;
	private String presidente;
	private String superintendente;
	private String numeroCarteirinha;

    public static final String JSON_NAME = "carteirinhas";
	 
	public Integer getCodigo() {
		return codigo;
	}
	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}
	public ClienteVO getClienteVO() {
		if (clienteVO == null) {
			clienteVO = new ClienteVO();
		}
		return clienteVO;
	}
	public void setClienteVO(ClienteVO clienteVO) {
		this.clienteVO = clienteVO;
	}
	public Date getDataLancamento() {
		return dataLancamento;
	}
	public void setDataLancamento(Date dataLancamento) {
		this.dataLancamento = dataLancamento;
	}
	public UsuarioVO getUsuarioVO() {
		return usuarioVO;
	}
	public void setUsuarioVO(UsuarioVO usuarioVO) {
		this.usuarioVO = usuarioVO;
	}

	public String getNomeDepartamento() {
		return nomeDepartamento;
	}

	public void setNomeDepartamento(String nomeDepartamento) {
		this.nomeDepartamento = nomeDepartamento;
	}

	public boolean isConcessionario() {
		return concessionario;
	}

	public void setConcessionario(boolean concessionario) {
		this.concessionario = concessionario;
	}

	public String getQrCode(){
    	if (this.codigoAcesso != null) 
    	  return this.codigoAcesso;
    	else
    	  return "";	
    }
    
    
    public void validarDados() throws ConsistirException {
        if (!getValidarDados().booleanValue()) {
            return;
        }
        if ((getUsuarioVO() == null) || (getUsuarioVO().getCodigo() == null) || (getUsuarioVO().getCodigo() <=0)) {
            throw new ConsistirException("O campo usuário (CarteirinhaCliente) deve ser informado.");
        }
    }
    
	
    public String getCodigoAcesso() {
		return codigoAcesso;
	}
	public void setCodigoAcesso(String codigoAcesso) {
		this.codigoAcesso = codigoAcesso;
	}
	public JSONObject getJson() throws Exception{
    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        JSONObject jSONObject = new JSONObject();
        jSONObject.put("codigo", getCodigo());
        jSONObject.put("nomeCliente", getClienteVO().getPessoa().getNome());
        jSONObject.put("codigoAcesso", this.codigoAcesso);
        jSONObject.put("qrCode", getQrCode());
        jSONObject.put("urlFoto", getUrlFoto());
        jSONObject.put("codigoMatricula", getClienteVO().getCodigoMatricula());
        jSONObject.put("matricula", String.format("%08d", getClienteVO().getCodigoMatricula()));
        jSONObject.put("dataLancamento", sdf.format(this.dataLancamento));
        jSONObject.put("dataImpressao", this.dataImpressao != null ? sdf.format(this.dataImpressao) : "");
        jSONObject.put("nomeCategoria", getNomeCategoria());
        jSONObject.put("nomeCategoriaTitular", getNomeCategoriaTitular());
        jSONObject.put("nomeSituacao", getNomeSituacao());
		jSONObject.put("dataValidadeCarteirinha", this.dataValidadeCarteirinha != null ? sdf.format(this.dataValidadeCarteirinha) : "");
		jSONObject.put("nomeDepartamento", getNomeDepartamento());
		jSONObject.put("concessionario", isConcessionario());
		jSONObject.put("profissao", getProfissao());
		jSONObject.put("vigenciaInicialConselho", getVigenciaInicialConselho());
		jSONObject.put("vigenciaFinalConselho", getVigenciaFinalConselho());
		jSONObject.put("via",getVia());
		jSONObject.put("nomeEmpresaSesc", getNomeEmpresa());
		jSONObject.put("nomeEmpresa", getNomeEmpresa());
		jSONObject.put("numeroCarteirinha", getNumeroCarteirinha());
		jSONObject.put("presidente", getPresidente());
		jSONObject.put("superintendente", getSuperintendente());
		jSONObject.put("historicoCargoTitulacao", "");

    	return jSONObject;
    }
	public Date getDataImpressao() {
		return dataImpressao;
	}
	public void setDataImpressao(Date dataImpressao) {
		this.dataImpressao = dataImpressao;
	}
	public String getUrlFoto() {
		return urlFoto;
	}
	public void setUrlFoto(String urlFoto) {
		this.urlFoto = urlFoto;
	}
	public Date getValidadeAnterior() {
		return validadeAnterior;
	}
	public void setValidadeAnterior(Date validadeAnterior) {
		this.validadeAnterior = validadeAnterior;
	}
	public MovParcelaVO getMovParcelaVO() {
		if (movParcelaVO == null) {
			movParcelaVO = new MovParcelaVO();
		}
		return movParcelaVO;
	}
	public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
		this.movParcelaVO = movParcelaVO;
	}
	
	public String getDataLancamento_Apresentar(){
		return Uteis.getDataAplicandoFormatacao(this.dataLancamento, "dd/MM/yyyy HH:mm:ss");	
	}
	
	public String getDataImpressao_Apresentar(){
		return Uteis.getDataAplicandoFormatacao(this.dataImpressao, "dd/MM/yyyy HH:mm:ss");	
	}
	
	public String getValidadeAnterior_Apresentar(){
		return Uteis.getDataAplicandoFormatacao(this.validadeAnterior, "dd/MM/yyyy HH:mm:ss");	
	}
	public Integer getVia() {
		return via;
	}
	public void setVia(Integer via) {
		this.via = via;
	}
	
	
	public String getNomeCategoriaTitular() {
		return nomeCategoriaTitular;
	}
	public void setNomeCategoriaTitular(String nomeCategoriaTitular) {
		this.nomeCategoriaTitular = nomeCategoriaTitular;
	}
	public String getNomeSituacao() {
		return nomeSituacao;
	}
	public void setNomeSituacao(String nomeSituacao) {
		this.nomeSituacao = nomeSituacao;
	}
	public String getVia_Apresentar(){
		return this.via + "° via";
	}
	public String getNomeCategoria() {
		return nomeCategoria;
	}
	public void setNomeCategoria(String nomeCategoria) {
		this.nomeCategoria = nomeCategoria;
	}

	public Date getDataValidadeCarteirinha() {
		return dataValidadeCarteirinha;
	}

	public void setDataValidadeCarteirinha(Date dataValidadeCarteirinha) {
		this.dataValidadeCarteirinha = dataValidadeCarteirinha;
	}

	public String getDataValidadeCarteirinha_Apresentar(){
		return Uteis.getDataAplicandoFormatacao(this.dataValidadeCarteirinha, "MM/yyyy");
	}

	public boolean isPagamentoAVista() {
		return pagamentoAVista;
	}

	public void setPagamentoAVista(boolean pagamentoAVista) {
		this.pagamentoAVista = pagamentoAVista;
	}

	public ColaboradorVO getColaboradorVO() {
		if (colaboradorVO == null) {
			colaboradorVO = new ColaboradorVO();
		}
		return colaboradorVO;
	}

	public String getProfissao() {
		return profissao;
	}

	public void setProfissao(String profissao) {
		this.profissao = profissao;
	}

	public void setColaboradorVO(ColaboradorVO colaboradorVO) {
		this.colaboradorVO = colaboradorVO;
	}

	public Integer getOrigemAtualizacaoCodigoAcesso() {
		return origemAtualizacaoCodigoAcesso;
	}

	public void setOrigemAtualizacaoCodigoAcesso(Integer origemAtualizacaoCodigoAcesso) {
		this.origemAtualizacaoCodigoAcesso = origemAtualizacaoCodigoAcesso;
	}

    public Date getVigenciaInicialConselho() {
        return vigenciaInicialConselho;
    }

    public void setVigenciaInicialConselho(Date vigenciaInicialConselho) {
        this.vigenciaInicialConselho = vigenciaInicialConselho;
    }

    public Date getVigenciaFinalConselho() {
        return vigenciaFinalConselho;
    }

    public void setVigenciaFinalConselho(Date vigenciaFinalConselho) {
        this.vigenciaFinalConselho = vigenciaFinalConselho;
    }

	public String getNomeEmpresa() {
		return nomeEmpresa;
	}

	public void setNomeEmpresa(String nomeEmpresa) {
		this.nomeEmpresa = nomeEmpresa;
	}

	public String getPresidente() {
		if (presidente == null) {
			presidente = "";
		}
		return presidente;
	}

	public void setPresidente(String presidente) {
		this.presidente = presidente;
	}

	public String getSuperintendente() {
		if (superintendente == null) {
			superintendente = "";
		}
		return superintendente;
	}

	public void setSuperintendente(String superintendente) {
		this.superintendente = superintendente;
	}

	public String getNumeroCarteirinha() {
		return numeroCarteirinha;
	}

	public void setNumeroCarteirinha(String numeroCarteirinha) {
		this.numeroCarteirinha = numeroCarteirinha;
	}
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.PeriodoMensal;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class BonusContratoVO extends SuperVO {

    protected ContratoVO contratoVO;
    protected Integer empresa;
    protected Date dataInicio;
    protected Date dataTermino;
    protected Date dataRegistro;
    protected Integer tipoJustificativa;
    protected Integer nrDias;
    protected String observacao;
    protected UsuarioVO responsavelOperacao;
    protected Boolean mensagemErro;
    protected Boolean apresentarPeriodoBonus;
    protected String acrescentarDiaContrato;
    @NaoControlarLogAlteracao
    private PeriodoAcessoClienteVO periodoAcessoClienteVO;
    private ContratoOperacaoVO contratoOperacaoVO;
    private boolean bonusRetiraDiasParaRenovarAntecipado = false;
    private PeriodoMensal periodoOperacao;

    public BonusContratoVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setContratoVO(new ContratoVO());
        setResponsavelOperacao(new UsuarioVO());
        setObservacao("");
        setMensagemErro(false);
        setTipoJustificativa(new Integer(0));
        setEmpresa(new Integer(0));
        setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
        setDataTermino(negocio.comuns.utilitarias.Calendario.hoje());
        setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        setApresentarPeriodoBonus(false);
    }

    public static void validarDados(BonusContratoVO obj) throws ConsistirException, Exception {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getTipoJustificativa().equals(0)) {
            throw new ConsistirException("A JUSTIFICATIVA DO BÔNUS deve ser Informada");
        }
        if (obj.getAcrescentarDiaContrato() == null || obj.getAcrescentarDiaContrato().equals("")) {
            throw new ConsistirException("os campos (Acrescenta Dias no contrato ou Retirar dias do Contrato) deve ser Informada");
        }
        if (obj.getNrDias() == null || obj.getNrDias().intValue() == 0) {
            if (obj.getAcrescentarDiaContrato().equals("AC")) {
                throw new ConsistirException("o campo (Quantidade de Dias para o Bônus) deve ser Informada");
            } else {
                throw new ConsistirException("o campo (Quantidade de Dias a serem retirados do contrato)deve ser Informada");
            }
        }
        ValidacaoHistoricoContrato.validarBonus(obj);
        if (!obj.getContratoVO().getSituacao().equals(SituacaoClienteEnum.ATIVO.getCodigo())){
            if (!obj.getAcrescentarDiaContrato().equals("AC") && !obj.getAcrescentarDiaContrato().equals("BC")) {
                if (obj.getPeriodoAcessoClienteVO() == null || !obj.getPeriodoAcessoClienteVO().getTipoAcesso().equals("BO")) {
                    throw new ConsistirException("Não existe Bônus lançado para fazer retirada de dias para Período de Acesso! (Contrato Inativo)");
                }
            }
        }
        if(obj.getContratoVO().isVendaCreditoTreino() && !obj.getContratoVO().getSituacao().equals("AT")){
            throw new ConsistirException("Não é possível lançar bônus para um contrato de créditos que está vencido.\n" +
                "Caso queira aproveitar os créditos restantes desse contrato, renove ou rematricule o contrato, adicionando a quantidade desejada manualmente.");
        }
    }

    public void inicializarDadosOperacaoContratoBonus(Date dataVigenteAjustada) throws Exception {
        try {
            ContratoOperacaoVO obj = new ContratoOperacaoVO();
            obj.setContrato(this.contratoVO.getCodigo().intValue());
            obj.setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNrDiasOperacao(this.getNrDias());
            obj.setObservacao(this.observacao);
            obj.setOperacaoPaga(false);
            obj.setResponsavel(this.responsavelOperacao);
            obj.getTipoJustificativa().setCodigo(this.tipoJustificativa);
            if (getAcrescentarDiaContrato().equals("AC")) {
                if (getContratoVO().getSituacao().equals("IN")) {
                    obj.setDescricaoCalculo("Operação de Bonus de Acrescimo.\r\n"
                            + "Foi lançado ao contrato " + getNrDias() + " "
                            + "dias de bonus para que o aluno possa usar a academia.\n\r"
                            + "Foi adicionado um período de acesso para que o cliente tenha acesso a academia.");
                } else {
                    obj.setDescricaoCalculo("Operação de Bonus de Acrescimo.\r\n"
                            + "Foi lançado ao contrato " + getNrDias() + " "
                            + "dias de bonus para que o aluno possa usar a academia.\n\r"
                            + "Modificando assim a data termino e data prevista para renovar e data prevista rematricula para: " + getContratoVO().getVigenciaAteAjustada_Apresentar());
                }
                obj.setTipoOperacao("BA");
                obj.setDataInicioEfetivacaoOperacao(this.dataInicio);
                obj.setDataFimEfetivacaoOperacao(this.dataTermino);
            } else if (getAcrescentarDiaContrato().equals("BC")) {
                    obj.setDescricaoCalculo("Operação de Afastamento Coletivo.\r\n"
                            + "Foi lançado ao contrato " + getNrDias() + " "
                            + "dias de bonus. Esses dias são abonados nos cálculos do sistema.\n\r"
                            + "Modificando assim a data termino e data prevista para renovar e data prevista rematricula para: " + getContratoVO().getVigenciaAteAjustada_Apresentar());
                    obj.setTipoOperacao("BC");
                    obj.setDataInicioEfetivacaoOperacao(this.getPeriodoOperacao().getDataInicio());
                    obj.setDataFimEfetivacaoOperacao(this.getPeriodoOperacao().getDataTermino());
            } else if (getAcrescentarDiaContrato().equals("BX")) {
                obj.setDescricaoCalculo("Operação de Bonus de Redução para exclusão bonus coletivo.\r\n");
                obj.setDataFimEfetivacaoOperacao(dataVigenteAjustada);
                obj.setDataInicioEfetivacaoOperacao(Uteis.obterDataFutura2(getDataTermino(), 1));
                obj.setTipoOperacao("BX");
            } else {
                if (this.getContratoVO().getSituacao().equals(SituacaoClienteEnum.ATIVO.getCodigo())){
                    String msgModificacao = "";
                    if (this.bonusRetiraDiasParaRenovarAntecipado){
                        msgModificacao = "Modificando assim a data termino e data prevista para rematricula para: " + getContratoVO().getVigenciaAteAjustada_Apresentar();
                    }else{
                        msgModificacao = "Modificando assim a data termino e data prevista para renovar e data prevista rematricula para: " + getContratoVO().getVigenciaAteAjustada_Apresentar();
                    }
                    obj.setDescricaoCalculo("Operação de Bonus de Redução.\r\n"
                            + "Foi retirado do seu contrato " + getNrDias() + " "
                            + "dias de uso na academia.\n\r"
                            + msgModificacao);
                    obj.setDataFimEfetivacaoOperacao(dataVigenteAjustada);
                    obj.setDataInicioEfetivacaoOperacao(Uteis.obterDataFutura2(getDataTermino(), 1));
                    obj.setTipoOperacao("BR");
                }else {
                    obj.setDescricaoCalculo("Operação de Bônus de Redução.\r\n"
                            + "Foi retirado do seu Acesso de " + getNrDias() + " "
                            + "dias de uso na acadêmia.\n\r"
                            + "Modificando assim a data término para: " + this.getDataTermino_Apresentar());
                    obj.setDataInicioEfetivacaoOperacao(Uteis.obterDataFutura2(getDataTermino(), 1));
                    obj.setDataFimEfetivacaoOperacao(this.getPeriodoAcessoClienteVO().getDataFinalAcesso());
                    obj.setTipoOperacao("BR");

                }
            }
            getFacade().getContratoOperacao().incluirSemCommit(obj, false);
            this.setContratoOperacaoVO(obj);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosHistoricoContrato() throws Exception {
        HistoricoContratoVO obj;
        if (getAcrescentarDiaContrato().equals("AC") || getAcrescentarDiaContrato().equals("BC")) {
            //obtenho a data de um dia anterior ao inico do bonus para modificar a data final do historico anterior a esse bonus
            getFacade().getHistoricoContrato().excluirHistoricoAVencerContrato(getContratoVO().getCodigo());
            obj = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(getContratoVO().getCodigo(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (obj != null && obj.getCodigo() > 0) {
                obj.setDataFinalSituacao(getDataTermino());
                getFacade().getHistoricoContrato().alterarSemCommit(obj, true);
            }
        } else {
            getFacade().getHistoricoContrato().excluirHistoricoAVencerContrato(getContratoVO().getCodigo());
            obj = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(getContratoVO().getCodigo(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (obj != null && !UteisValidacao.emptyNumber(obj.getCodigo())) {
                obj.setDataFinalSituacao(getDataTermino());
                getFacade().getHistoricoContrato().alterarSemCommit(obj, true);
            }
        }
    }

    public void inicializarDadosPeridoAcesso() throws Exception {
        try {
            if (getAcrescentarDiaContrato().equals("AC") || getAcrescentarDiaContrato().equals("BC")) {
                PeriodoAcessoClienteVO periodoAcesso = new PeriodoAcessoClienteVO();
                periodoAcesso.setDataInicioAcesso(this.dataInicio);
                periodoAcesso.setDataFinalAcesso(this.dataTermino);
                periodoAcesso.setContrato(this.contratoVO.getCodigo().intValue());
                periodoAcesso.setPessoa(this.contratoVO.getPessoa().getCodigo().intValue());
                periodoAcesso.setTipoAcesso("BO");
                getFacade().getPeriodoAcessoCliente().incluirSemCommit(periodoAcesso);
            } else {
                PeriodoAcessoClienteVO  periodoAcesso = null;
                
                boolean vigente = false;
                while (!vigente){
                    periodoAcesso = getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoContrato(this.contratoVO.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if(periodoAcesso != null && Calendario.maior(periodoAcesso.getDataInicioAcesso(), this.dataTermino)){
                        getFacade().getPeriodoAcessoCliente().excluirSemCommit(periodoAcesso);
                    } else {
                        vigente =true;
                    }
                }
                if(periodoAcesso != null){
                    periodoAcesso.setDataFinalAcesso(this.dataTermino);
                    getFacade().getPeriodoAcessoCliente().alterarSemCommit(periodoAcesso);
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj) throws Exception {
        try {
            Date dataFimAnterior = contratoRenovacao.getVigenciaAte();
            contratoRenovacao.obterDataFinalContratoComContratoDuracao(Uteis.obterDataFutura2(obj.getVigenciaAteAjustada(), 1));
            getFacade().getContrato().alterarDatasVigenciaContrato(contratoRenovacao);
            inicializarDadosMatriculaTurmaContratoRenovacao(contratoRenovacao, dataFimAnterior);
        } catch (Exception e) {
            throw e;
        }

    }

    public void inicializarDadosPeriodoAcessoContratoRenovacao(ContratoVO contrato, Date data) throws Exception {
        try {
            PeriodoAcessoClienteVO periodoAcesso = new PeriodoAcessoClienteVO();
            periodoAcesso = getFacade().getPeriodoAcessoCliente().consultarPorDataEspecificaECodigoContrato(data, contrato.getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (periodoAcesso != null) {
                getFacade().getPeriodoAcessoCliente().excluirSemCommit(periodoAcesso);
            }
            periodoAcesso = new PeriodoAcessoClienteVO();
            periodoAcesso.setContrato(contrato.getCodigo().intValue());
            periodoAcesso.setDataInicioAcesso(contrato.getVigenciaDe());
            periodoAcesso.setDataFinalAcesso(contrato.getVigenciaAteAjustada());
            periodoAcesso.setPessoa(contrato.getPessoa().getCodigo().intValue());
            periodoAcesso.setTipoAcesso("CA");
            getFacade().getPeriodoAcessoCliente().incluirSemCommit(periodoAcesso);
        } catch (Exception e) {
            throw e;
        }

    }

    public void inicializarDadosContratoOperacaoContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj) throws Exception {
        try {
            ContratoOperacaoVO objContratoOperacaoVO = new ContratoOperacaoVO();
            objContratoOperacaoVO.setContrato(contratoRenovacao.getCodigo().intValue());
            objContratoOperacaoVO.setDataFimEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDataInicioEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDescricaoCalculo("");
            objContratoOperacaoVO.setObservacao("Modificação: \n\r"
                    + " Data de Início,\n\r"
                    + " Data de Término,\n\r"
                    + " Data de Previsão Renovação,\n\r"
                    + " Data de Previsão Rematricula,\n\r"
                    + " Devido uma OPERAÇÃO de Bônus no contrato de Numero: " + obj.getCodigo().intValue() + ".");
            objContratoOperacaoVO.setOperacaoPaga(false);
            objContratoOperacaoVO.setResponsavel(getResponsavelOperacao());
            objContratoOperacaoVO.setTipoOperacao("AC");
            objContratoOperacaoVO.getTipoJustificativa().setCodigo(this.tipoJustificativa);
            getFacade().getContratoOperacao().incluirSemCommit(objContratoOperacaoVO, false);
            this.setContratoOperacaoVO(objContratoOperacaoVO);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosHistoricoContratoContratoRenovacao(ContratoVO contratoRenovacao,
            ContratoVO obj, Date dataVigencia) throws Exception {
        try {
            //ajustar o ultimo historico do contrato anterior
            Date data = Calendario.getDataComHoraZerada(Calendario.hoje());
            HistoricoContratoVO objHistoricoContratoVO = getFacade().
                    getHistoricoContrato().
                    obterHistoricoContratoPorDataEspecifica(
                    getContratoVO().getCodigo().intValue(), data, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (objHistoricoContratoVO != null) {
                objHistoricoContratoVO.setDataFinalSituacao(Uteis.obterDataAnterior(contratoRenovacao.getVigenciaDe(), 1));
                getFacade().getHistoricoContrato().alterarSemCommit(objHistoricoContratoVO, false);
            } else {
                throw new Exception(String.format("Não foi possível encontrar histórico do contrato renovado \"%s\" nesta data",
                        new Object[]{
                            getContratoVO().getCodigo()
                        }));
            }
            //ajustar o histórico do contrato posterior            
            objHistoricoContratoVO = getFacade().getHistoricoContrato().
                    obterHistoricoContratoPorDataEspecifica(
                    contratoRenovacao.getCodigo().intValue(), dataVigencia, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (objHistoricoContratoVO != null) {
                objHistoricoContratoVO.setDataInicioSituacao(contratoRenovacao.getVigenciaDe());
                objHistoricoContratoVO.setDataFinalSituacao(contratoRenovacao.getVigenciaAteAjustada());
                getFacade().getHistoricoContrato().alterarSemCommit(objHistoricoContratoVO, false);
            } else {
                throw new Exception(String.format("Não foi possível encontrar histórico do contrato posterior \"%s\" nesta data",
                        new Object[]{
                            contratoRenovacao.getCodigo()
                        }));
            }

//            objHistoricoContratoVO = new HistoricoContratoVO();
//            objHistoricoContratoVO.setContrato(contratoRenovacao.getCodigo().intValue());
//            objHistoricoContratoVO.setDataFinalSituacao(negocio.comuns.utilitarias.Calendario.hoje());
//            objHistoricoContratoVO.setDataInicioSituacao(negocio.comuns.utilitarias.Calendario.hoje());
//            objHistoricoContratoVO.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
//            objHistoricoContratoVO.setDescricao("ALTERAÇÃO DE CONTRATO");
//            objHistoricoContratoVO.setResponsavelRegistro(getResponsavelOperacao());
//            objHistoricoContratoVO.setSituacaoRelativaHistorico("");            
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosMatriculaTurmaContratoRenovacao(ContratoVO contratoSucessor, Date dataFimAnterior) throws Exception {
        List<MatriculaAlunoHorarioTurmaVO> lista = getFacade().getMatriculaAlunoHorarioTurma().consultarMatriculaAtiva(contratoSucessor.getCodigo(), dataFimAnterior);
        for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
            matricula.setDataInicio(contratoSucessor.getVigenciaDe());
            if (contratoSucessor.getContratoResponsavelRenovacaoMatricula() == null || contratoSucessor.getContratoResponsavelRenovacaoMatricula() == 0) {
                matricula.setDataFim(Uteis.somarDias(contratoSucessor.getVigenciaAte(), contratoSucessor.getEmpresa().getToleranciaOcupacaoTurma()));
            } else {
                matricula.setDataFim(contratoSucessor.getVigenciaAte());
            }
            getFacade().getMatriculaAlunoHorarioTurma().alterarInicioFimMatriculaSemCommit(matricula);
        }

    }

    public void obterDataInicioTermino() {
        if (this.getNrDias() != null && this.getNrDias() != 0) {
            if (this.getAcrescentarDiaContrato().equals("AC")) {
                if (Calendario.maior(Calendario.hoje(), this.getContratoVO().getVigenciaAteAjustada())) {
                    this.setDataInicio(Calendario.getDataComHoraZerada(Calendario.hoje()));
                } else {
                    this.setDataInicio(Uteis.obterDataFutura2(this.getContratoVO().getVigenciaAteAjustada(), 1));
                }
                this.setDataTermino(Uteis.obterDataFutura2(this.getDataInicio(), (this.getNrDias() - 1)));
            } else if(this.getAcrescentarDiaContrato().equals("BC")) {
                this.setDataInicio(Uteis.obterDataFutura2(this.getContratoVO().getVigenciaAteAjustada(), 1));
                this.setDataTermino(Uteis.obterDataFutura2(this.getDataInicio(), (this.getNrDias() - 1)));
            } else {
                this.setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
                this.setDataTermino(Uteis.obterDataAnterior(this.getContratoVO().getVigenciaAteAjustada(), (this.getNrDias())));
            }
            this.setApresentarPeriodoBonus(true);
        } else {
            this.setApresentarPeriodoBonus(false);
        }
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public String getDataInicio_Apresenta() {
        return Uteis.getData(dataInicio);
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public String getDataTermino_Apresentar() {
        return Uteis.getData(dataTermino);
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Boolean getMensagemErro() {
        return mensagemErro;
    }

    public void setMensagemErro(Boolean mensagemErro) {
        this.mensagemErro = mensagemErro;
    }

    public Integer getNrDias() {
        return nrDias;
    }

    public void setNrDias(Integer nrDias) {
        this.nrDias = nrDias;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public UsuarioVO getResponsavelOperacao() {
        return responsavelOperacao;
    }

    public void setResponsavelOperacao(UsuarioVO responsavelOperacao) {
        this.responsavelOperacao = responsavelOperacao;
    }

    public Integer getTipoJustificativa() {
        return tipoJustificativa;
    }

    public void setTipoJustificativa(Integer tipoJustificativa) {
        this.tipoJustificativa = tipoJustificativa;
    }

    public Boolean getApresentarPeriodoBonus() {
        return apresentarPeriodoBonus;
    }

    public Boolean getApresentarPeriodoBonusNegativo() {
        return getAcrescentarDiaContrato().equals("RE");
    }

    public Boolean getApresentarPeriodoBonusPositivo() {
        return getAcrescentarDiaContrato().equals("AC");
    }

    public void setApresentarPeriodoBonus(Boolean apresentarPeriodoBonus) {
        this.apresentarPeriodoBonus = apresentarPeriodoBonus;
    }

    public String getAcrescentarDiaContrato() {
        if (acrescentarDiaContrato == null) {
            acrescentarDiaContrato = "";
        }
        return acrescentarDiaContrato;
    }

    public void setAcrescentarDiaContrato(String acrescentarDiaContrato) {
        this.acrescentarDiaContrato = acrescentarDiaContrato;
    }


    public PeriodoAcessoClienteVO getPeriodoAcessoClienteVO() {
        return periodoAcessoClienteVO;
    }

    public void setPeriodoAcessoClienteVO(PeriodoAcessoClienteVO periodoAcessoClienteVO) {
        this.periodoAcessoClienteVO = periodoAcessoClienteVO;
    }

    public ContratoOperacaoVO getContratoOperacaoVO() {
        if (contratoOperacaoVO == null) {
            contratoOperacaoVO = new ContratoOperacaoVO();
        }
        return contratoOperacaoVO;
    }

    public void setContratoOperacaoVO(ContratoOperacaoVO contratoOperacaoVO) {
        this.contratoOperacaoVO = contratoOperacaoVO;
    }

    public boolean isBonusRetiraDiasParaRenovarAntecipado() {
        return bonusRetiraDiasParaRenovarAntecipado;
    }

    public void setBonusRetiraDiasParaRenovarAntecipado(boolean bonusRetiraDiasParaRenovarAntecipado) {
        this.bonusRetiraDiasParaRenovarAntecipado = bonusRetiraDiasParaRenovarAntecipado;
    }

    public PeriodoMensal getPeriodoOperacao() {
        return periodoOperacao;
    }

    public void setPeriodoOperacao(PeriodoMensal periodoOperacao) {
        this.periodoOperacao = periodoOperacao;
    }
}

package negocio.comuns.contrato;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class UtilizacaoAvaliacaoFisicaVO extends SuperVO {

    private Integer codigo;
    private Integer codAvaliacaoFisica;
    private Date dataAvaliacaoFisica;
    private MovProdutoVO movProdutoVO;
    private Boolean primeiraavaliacao;

    @NaoControlarLogAlteracao
    private ClienteVO clienteVO;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodAvaliacaoFisica() {
        return codAvaliacaoFisica;
    }

    public void setCodAvaliacaoFisica(Integer codAvaliacaoFisica) {
        this.codAvaliacaoFisica = codAvaliacaoFisica;
    }

    public Date getDataAvaliacaoFisica() {
        return dataAvaliacaoFisica;
    }

    public void setDataAvaliacaoFisica(Date dataAvaliacaoFisica) {
        this.dataAvaliacaoFisica = dataAvaliacaoFisica;
    }

    public String getDataAvaliacaoFisicaApresentar() {
        return Uteis.getData(getDataAvaliacaoFisica());
    }

    public MovProdutoVO getMovProdutoVO() {
        if (movProdutoVO == null) {
            movProdutoVO = new MovProdutoVO();
        }
        return movProdutoVO;
    }

    public void setMovProdutoVO(MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    public Boolean getPrimeiraavaliacao() {
        if (primeiraavaliacao == null) {
            primeiraavaliacao = true;
        }
        return primeiraavaliacao;
    }

    public void setPrimeiraavaliacao(Boolean primeiraavaliacao) {
        this.primeiraavaliacao = primeiraavaliacao;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }
}

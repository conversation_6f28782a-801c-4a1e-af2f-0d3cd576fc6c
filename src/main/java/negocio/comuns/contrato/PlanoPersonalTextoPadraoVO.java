package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * <AUTHOR>
 */
public class PlanoPersonalTextoPadraoVO extends SuperVO {

    protected Integer codigo;
    protected Integer codigoTaxaPersonal;
    protected PlanoTextoPadraoVO planoTextoPadrao;

    public PlanoPersonalTextoPadraoVO(){
        super();
    }

    public static void validarDados(PlanoPersonalTextoPadraoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
    }

    public Integer getCodigo() {
        if (codigo == null){
            codigo = new Integer(0);
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoTaxaPersonal() {
        if(codigoTaxaPersonal == null){
            codigoTaxaPersonal = new Integer(0);
        }
        return codigoTaxaPersonal;
    }

    public void setCodigoTaxaPersonal(Integer codigoTaxaPersonal) {
        this.codigoTaxaPersonal = codigoTaxaPersonal;
    }

    public PlanoTextoPadraoVO getPlanoTextoPadrao() {
        if(planoTextoPadrao == null){
            planoTextoPadrao = new PlanoTextoPadraoVO();
        }
        return planoTextoPadrao;
    }

    public void setPlanoTextoPadrao(PlanoTextoPadraoVO planoTextoPadrao) {
        this.planoTextoPadrao = planoTextoPadrao;
    }
}

package negocio.comuns.contrato;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class MatriculaAlunoHorarioTurmaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private int empresa = 0;
    @ChaveEstrangeira
    private PessoaVO pessoa = new PessoaVO();
    @ChaveEstrangeira
    private ContratoVO contrato = new ContratoVO();
    private Date dataInicio = negocio.comuns.utilitarias.Calendario.hoje();
    private Date dataFim = negocio.comuns.utilitarias.Calendario.hoje();
    @ChaveEstrangeira
    private HorarioTurmaVO horarioTurma = new HorarioTurmaVO();
    private Double valorComissao = 0.0;
    private ReposicaoVO reposicao;
    private String tipoComissao;
    private Double porcComissao = 0.0;
    @NaoControlarLogAlteracao
    private Double valor = 0.0;
    
    public void setPorcComissao(double d){
    	porcComissao = d;
    	
    }
    public Double getPorcComissao(){
    	return porcComissao;
    }
    
    public String getPorcComissao_Apresentar() {
        if (!UteisValidacao.emptyString(this.getTipoComissao()) &&
                this.getTipoComissao().equalsIgnoreCase("R$")) {
            return Formatador.formatarValorMonetario(this.porcComissao);
        } else {
            return Formatador.formatarValorNumerico(porcComissao, "#0.00") + "%";
        }
    }
    
    public void validarDados() throws ConsistirException {
        if (!getValidarDados().booleanValue()) {
            return;
        }
        if (getEmpresa() == 0) {
            throw new ConsistirException("O campo Empresa deve ser informado.");
        }
        if (getPessoa() == null || getPessoa().getCodigo().intValue() == 0) {
            throw new ConsistirException("O campo Pessoa deve ser informado.");
        }
        if (getContrato() == null || getContrato().getCodigo().intValue() == 0) {
            throw new ConsistirException("O campo Contrato deve ser informado.");
        }
        if (getDataInicio() == null || getDataFim() == null) {
            throw new ConsistirException("O período de Matrícula do Aluno deve ser informado.");
        }
        if (Calendario.maior(getDataInicio(),getDataFim())) {
            throw new ConsistirException("A Data Final deve ser Sempre Posterior a Data Inicial.");
        }
        if (getHorarioTurma() == null || getHorarioTurma().getCodigo().intValue() == 0) {
            throw new ConsistirException("O campo Horário da Turma deve ser informado.");
        }
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public HorarioTurmaVO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaVO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public ReposicaoVO getReposicao() {
        return reposicao;
    }

    public void setReposicao(ReposicaoVO reposicao) {
        this.reposicao = reposicao;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof MatriculaAlunoHorarioTurmaVO && obj != null) {
            MatriculaAlunoHorarioTurmaVO aux = (MatriculaAlunoHorarioTurmaVO) obj;
            return this.codigo == aux.codigo;
        }
        return false;
    }

    public String getNomeAluno() {
        return getPessoa().getNome();
    }

    public Double getValorComissao() {
        return valorComissao;
    }

    public void setValorComissao(Double valorComissao) {
        this.valorComissao = valorComissao;
    }

    public String getDataInicio_Apresentar() {
        return Uteis.getData(dataInicio, "dd/MM/yyyy");
    }

    public String getDataFim_Apresentar() {
        return Uteis.getData(dataFim, "dd/MM/yyyy");
    }

    public String getValorComissao_Apresentar() {
        return Formatador.formatarValorMonetario(valorComissao);
    }

    public boolean isVigente() {
        if (Calendario.entre(Calendario.hoje(), dataInicio, dataFim)) {
            return true;
        } else if (Calendario.igual(Calendario.hoje(), dataInicio) || Calendario.igual(Calendario.hoje(), dataFim)) {
            return true;

        } else {
            return false;
        }
    }

    public String getHrInicio() {
        return getHorarioTurma().getHoraInicial();
    }
	public void setValor(Double valor) {
		this.valor = valor;
	}
	public Double getValor() {
		return valor;
	}

    public String getTipoComissao() {
        return tipoComissao;
    }

    public void setTipoComissao(String tipoComissao) {
        this.tipoComissao = tipoComissao;
    }
}

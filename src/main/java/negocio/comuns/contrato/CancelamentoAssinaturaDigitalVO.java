package negocio.comuns.contrato;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;

import java.util.Date;

public class CancelamentoAssinaturaDigitalVO extends SuperVO {
    @ChavePrimaria
    private Integer codigo;
    private ContratoVO contrato;
    private ContratoOperacaoVO contratoOperacao;
    private UsuarioVO usuarioResponsavel;
    private String assinatura;
    private Date lancamento;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public ContratoOperacaoVO getContratoOperacao() {
        return contratoOperacao;
    }

    public void setContratoOperacao(ContratoOperacaoVO contratoOperacao) {
        this.contratoOperacao = contratoOperacao;
    }

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public String getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(String assinatura) {
        this.assinatura = assinatura;
    }

    public Date getLancamento() {
        return lancamento;
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }
}

package negocio.comuns.contrato;

public enum TipoInclusaoContratoEnum {
    VENDA("Inclusão a partir de formulário de venda"),
    TRANSFERENCIA("Inclusão a partir de transferência de unidade");

    private String descricao;

    TipoInclusaoContratoEnum(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

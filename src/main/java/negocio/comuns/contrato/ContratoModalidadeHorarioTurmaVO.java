package negocio.comuns.contrato;

import annotations.arquitetura.ChaveEstrangeira;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.plano.HorarioTurmaVO;

/**
 * Reponsável por manter os dados da entidade ContratoModalidadeHorarioTurma. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see ContratoModalidadeTurma
 */
public class ContratoModalidadeHorarioTurmaVO extends SuperVO {

    protected Integer codigo;
    protected Integer contratoModalidadeTurma;    
    @ChaveEstrangeira
    protected HorarioTurmaVO horarioTurma;

    protected Double percOcupacao;
    protected Double percDesconto;

    /**
     * Construtor padrão da classe <code>ContratoModalidadeHorarioTurma</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ContratoModalidadeHorarioTurmaVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ContratoModalidadeHorarioTurmaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getHorarioTurma().getCodigo().intValue() == 0) {
            throw new ConsistirException("O campo HORÁRIO TURMA (Contrato Modalidade Horário Turma) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setHorarioTurma(new HorarioTurmaVO());
        setPercDesconto(0D);
        setPercOcupacao(0D);
    }

    public HorarioTurmaVO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaVO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public Integer getContratoModalidadeTurma() {
        return (contratoModalidadeTurma);
    }

    public void setContratoModalidadeTurma(Integer contratoModalidadeTurma) {
        this.contratoModalidadeTurma = contratoModalidadeTurma;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
    
    public Integer getDiaDaSemana_Apresentar(){
        if(getHorarioTurma() != null && !UteisValidacao.emptyNumber(getHorarioTurma().getDiaSemanaNumero())) {
            return getHorarioTurma().getDiaSemanaNumero();
        }
        return 0;
    }

    public Double getPercOcupacao() {
        return null != percOcupacao ? percOcupacao : 0D;
    }

    public void setPercOcupacao(Double percOcupacao) {
        this.percOcupacao = percOcupacao;
    }

    public Double getPercDesconto() {
        return null != percDesconto ? percDesconto : 0D;
    }

    public void setPercDesconto(Double percDesconto) {
        this.percDesconto = percDesconto;
    }
}
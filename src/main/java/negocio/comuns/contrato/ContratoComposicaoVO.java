/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ComposicaoVO;
import negocio.comuns.plano.PlanoComposicaoVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class ContratoComposicaoVO extends SuperVO {

    protected Integer codigo;
    protected Integer contrato;
    protected ComposicaoVO composicaoVO;

    /**
     * Construtor padrão da classe <code>ContratoModalidade</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ContratoComposicaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setContrato(new Integer(0));
        setComposicaoVO(new ComposicaoVO());
    }

     /**
     * Operação responsável por validar os dados de um objeto da classe <code>ContratoModalidadeVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ContratoComposicaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getComposicaoVO() == null) ||
                (obj.getComposicaoVO().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo Plano  (Contrato Modalidade) deve ser informado.");
        }

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public ComposicaoVO getComposicaoVO() {
        return composicaoVO;
    }

    public void setComposicaoVO(ComposicaoVO composicaoVO) {
        this.composicaoVO = composicaoVO;
    }

    
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoSugeridoVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class ContratoModalidadeProdutoSugeridoVO extends SuperVO{

    protected Integer codigo;
    protected Integer contratoModalidade;
    protected ProdutoSugeridoVO produtoSugerido;
    protected Double valorFinalProduto;
    
    public ContratoModalidadeProdutoSugeridoVO() {
        super();
        inicializarDados();
    }
     public static void validarDados(ContratoModalidadeProdutoSugeridoVO obj) throws ConsistirException {
         if (!obj.getValidarDados().booleanValue()) {
            return;
            }
     }
     
      public void inicializarDados() {
        setCodigo(new Integer(0));
        setContratoModalidade(new Integer(0));
        setProdutoSugerido(new ProdutoSugeridoVO());
        setValorFinalProduto(new Double(0));
    }
    

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getContratoModalidade() {
        return contratoModalidade;
    }

    public void setContratoModalidade(Integer contratoModalidade) {
        this.contratoModalidade = contratoModalidade;
    }

    public ProdutoSugeridoVO getProdutoSugerido() {
        return produtoSugerido;
    }

    public void setProdutoSugerido(ProdutoSugeridoVO produtoSugerido) {
        this.produtoSugerido = produtoSugerido;
    }

    public Double getValorFinalProduto() {
        return valorFinalProduto;
    }

    public void setValorFinalProduto(Double valorFinalProduto) {
        this.valorFinalProduto = valorFinalProduto;
    }

    
    

}

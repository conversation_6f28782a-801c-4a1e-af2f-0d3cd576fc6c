package negocio.comuns.contrato;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.ReciboDevolucaoVO;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * Reponsável por manter os dados da entidade ContratoOperacao. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class ContratoOperacaoVO extends SuperVO {

    protected Integer codigo;
    protected Integer contrato;
    protected String tipoOperacao;
    protected Boolean operacaoPaga;
    protected Date dataOperacao;
    protected Date dataInicioEfetivacaoOperacao;
    protected Date dataFimEfetivacaoOperacao;
    protected UsuarioVO responsavel;
    protected JustificativaOperacaoVO tipoJustificativa;
    protected String observacao;
    protected String descricaoCalculo;
    protected UsuarioVO responsavelLiberacao;
    protected ClienteVO clienteTransfereDias;
    protected ClienteVO clienteRecebeDias;
    private Double valor;
    private Double valorCobrado;
    private ReciboDevolucaoVO reciboDevolucao;
    @NaoControlarLogAlteracao
    private boolean permiteEstorno = false;
    private Integer nrDiasOperacao;
    private String matricula;
    private Date dia;
    private String justificativa;
    private String colaboradorResp;
    private String nome;
    private String justificativaApresentar;
    private String informacoes;
    private String chaveArquivo;
    @NaoControlarLogAlteracao
    private String urlCompletaArquivoAtestado;
    private OrigemSistemaEnum origemSistema;
    private String informacoesDesfazer;
    private String assinaturaDigitalBiometria;
    private String diasOperacao = null;

    /**
     * Construtor padrão da classe <code>ContratoOperacao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ContratoOperacaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ContratoOperacaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ContratoOperacaoVO obj) throws ConsistirException {
        validarDados(obj,true);
    }

    public static void validarDados(ContratoOperacaoVO obj, boolean incluindoTrancamento) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (incluindoTrancamento) {
            if (obj.getTipoOperacao().equals("TR") && Calendario.diferencaEmDias(obj.getDataInicioEfetivacaoOperacao(), obj.getDataFimEfetivacaoOperacao()) > 730) {
                throw new ConsistirException("Operação de trancamento não permitida pois o número de dias trancados é acima de 730 dias.");
            }
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setTipoOperacao(getTipoOperacao().toUpperCase());
        if(getObservacao() != null){
            setObservacao(getObservacao().toUpperCase());
        }
        if(getDescricaoCalculo() != null ){
            setDescricaoCalculo(getDescricaoCalculo().toUpperCase());
        }
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setContrato(new Integer(0));
        setTipoOperacao("");
        setOperacaoPaga(new Boolean(false));
        setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
        setDataInicioEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
        setDataFimEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
        setResponsavel(new UsuarioVO());
        setObservacao("");
        setDescricaoCalculo("");
        setTipoJustificativa(new JustificativaOperacaoVO());
        setResponsavelLiberacao(new UsuarioVO());
        setClienteRecebeDias(new ClienteVO());
        setClienteTransfereDias(new ClienteVO());
        setNrDiasOperacao(new Integer(0));
    }

    public Integer obterNrDiasContratoOperacao() {
        long nrDias = Uteis.nrDiasEntreDatas(getDataInicioEfetivacaoOperacao(), getDataFimEfetivacaoOperacao()) + 1;
        Integer nrDiasOperacao = new Long(nrDias).intValue();
        return nrDiasOperacao;
    }

    public Integer obterNrDiasUtilizadoOperacaoAteDataEspecifica(Date dataEspecifica) throws Exception {
        long nrDias = Uteis.nrDiasEntreDatas(getDataInicioEfetivacaoOperacao(), dataEspecifica) + 1;
        Integer nrDiasDataEspecifica = new Long(nrDias).intValue();
        return nrDiasDataEspecifica;
    }

    public String getDescricaoCalculo() {
        return (descricaoCalculo);
    }

    public void setDescricaoCalculo(String descricaoCalculo) {
        this.descricaoCalculo = descricaoCalculo;
    }

    public String getObservacao() {
    	if(observacao == null){
    		observacao = "";
    	}
        return (observacao);
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }

    public Date getDataFimEfetivacaoOperacao() {
        return (dataFimEfetivacaoOperacao);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
     */
    public String getDataFimEfetivacaoOperacao_Apresentar() {
        return (Uteis.getData(dataFimEfetivacaoOperacao));
    }

    public void setDataFimEfetivacaoOperacao(Date dataFimEfetivacaoOperacao) {
        this.dataFimEfetivacaoOperacao = dataFimEfetivacaoOperacao;
    }

    public Date getDataInicioEfetivacaoOperacao() {
        return (dataInicioEfetivacaoOperacao);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
     */
    public String getDataInicioEfetivacaoOperacao_Apresentar() {
        return (Uteis.getData(dataInicioEfetivacaoOperacao));
    }

    public void setDataInicioEfetivacaoOperacao(Date dataInicioEfetivacaoOperacao) {
        this.dataInicioEfetivacaoOperacao = dataInicioEfetivacaoOperacao;
    }

    public Date getDataOperacao() {
        return (dataOperacao);
    }

    public String getDataOperacao_Apresentar() {
        return Uteis.formataComHorarioSeDiferenteZero(dataOperacao);
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public Boolean getOperacaoPaga() {
        return (operacaoPaga);
    }

    public Boolean isOperacaoPaga() {
        return (operacaoPaga);
    }

    public void setOperacaoPaga(Boolean operacaoPaga) {
        this.operacaoPaga = operacaoPaga;
    }

    public String getTipoOperacao() {
        return (tipoOperacao);
    }

    public String getTipoOperacao_Apresentar() {
        if (tipoOperacao == null) {
            tipoOperacao = "";
        }
        if (tipoOperacao.equals("RM")) {
            return "Rematrícula";
        }
        if (tipoOperacao.equals("TS")) {
            return "Transferência Saída";
        }
        if (tipoOperacao.equals("MA")) {
            return "Matrícula";
        }
        if (tipoOperacao.equals("AD")) {
            return "Alteração Duração";
        }
        if (tipoOperacao.equals("RE")) {
            return "Renovação";
        }
        if (tipoOperacao.equals("BA")) {
            return "Bônus- Acréscimo de dias ";
        }
        if (tipoOperacao.equals("BR")) {
            return "Bônus -Redução de dias ";
        }
        if (tipoOperacao.equals("CR")) {
            if (this.getDataOperacao() != null && this.getDataInicioEfetivacaoOperacao() != null
                    && this.getDataFimEfetivacaoOperacao() != null) {

                if (Calendario.maior(this.getDataOperacao(), this.getDataInicioEfetivacaoOperacao())
                        && Calendario.maior(this.getDataOperacao(), this.getDataFimEfetivacaoOperacao())) {
                    return "Férias (RETROATIVO)";
                }

            }
            return "Férias";
        }
        
        if (tipoOperacao.equals("TE")) {
            return "Transferência Entrada";
        }
        if (tipoOperacao.equals("CA")) {
            return "Cancelamento";
        }
        if (tipoOperacao.equals("AH")) {
            return "Alteração Horário";
        }
        if (tipoOperacao.equals("TR")) {
            return "Trancamento";
        }
        if (tipoOperacao.equals("TV")) {
            return "Trancamento Vencido";
        }
        if (tipoOperacao.equals("RT")) {
            return "Retorno Trancamento";
        }
        if (tipoOperacao.equals("IM")) {
            return "Incluir Modalidade";
        }
        if (tipoOperacao.equals("EM")) {
            return "Excluir Modalidade";
        }
        if (tipoOperacao.equals("AM")) {
            return "Alterar Modalidade";
        }
        if (tipoOperacao.equals("AC")) {
            return "Alteração Contrato";
        }
        if (tipoOperacao.equals("AT")) {
            if (this.getDataOperacao() != null && this.getDataInicioEfetivacaoOperacao() != null
                    && this.getDataFimEfetivacaoOperacao() != null) {

                if (Calendario.maior(this.getDataOperacao(), this.getDataInicioEfetivacaoOperacao())
                        && Calendario.maior(this.getDataOperacao(), this.getDataFimEfetivacaoOperacao())) {
                    return "Atestado (RETROATIVO)";
                }
                
            }
            return "Atestado";
        }

        if (tipoOperacao.equals("RA")) {
            return "Retorno - Atestado";
        }
        if (tipoOperacao.equals("LV"))
            return "Liberação de Vaga";

        if (tipoOperacao.equals("BC"))
            return "Afastamento Coletivo";

        if (tipoOperacao.equals("TD")) {
            return "Transferência dos Direitos de uso";
        }

        if (tipoOperacao.equals("RD")) {
            return "Retorno dos Direitos de uso";
        }


        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public Integer getContrato() {
        return (contrato);
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public JustificativaOperacaoVO getTipoJustificativa() {
        if(tipoJustificativa == null){
            tipoJustificativa = new JustificativaOperacaoVO();
        }
        return tipoJustificativa;
    }

    public void setTipoJustificativa(JustificativaOperacaoVO tipoJustificativa) {
        this.tipoJustificativa = tipoJustificativa;
    }

    public UsuarioVO getResponsavelLiberacao() {
        return responsavelLiberacao;
    }

    public void setResponsavelLiberacao(UsuarioVO responsavelLiberacao) {
        this.responsavelLiberacao = responsavelLiberacao;
    }

    public ClienteVO getClienteRecebeDias() {
        return clienteRecebeDias;
    }

    public void setClienteRecebeDias(ClienteVO clienteRecebeDias) {
        this.clienteRecebeDias = clienteRecebeDias;
    }

    public ClienteVO getClienteTransfereDias() {
        return clienteTransfereDias;
    }

    public void setClienteTransfereDias(ClienteVO clienteTransfereDias) {
        this.clienteTransfereDias = clienteTransfereDias;
    }

    public Boolean getRematricula() {
        return tipoOperacao.equals("RM");
    }

    public Boolean getTransferenciaSaida() {
        return tipoOperacao.equals("TS");
    }

    public Boolean getMatricula() {
        return tipoOperacao.equals("MA");
    }

    public Boolean getAlteracaoDuracao() {
        return tipoOperacao.equals("AD");
    }

    public Boolean getRenovacao() {
        return tipoOperacao.equals("RE");
    }

    public Boolean getBonusAcrescimo() {
        return tipoOperacao.equals("BA");
    }

    public Boolean getBonusReducao() {
        return tipoOperacao.equals("BR");
    }

    public Boolean getCarenciaFerias() {
        return tipoOperacao.equals("CR");
    }

    public Boolean getTransfenciaEntrada() {
        return tipoOperacao.equals("TE");
    }

    public Boolean getCancelamento() {
        return tipoOperacao.equals("CA");
    }

    public Boolean getAlteracaoHorario() {
        return tipoOperacao.equals("AH");
    }

    public Boolean getTrancamento() {
        return tipoOperacao.equals("TR");
    }

    public Boolean getTrancamentoVencido() {
        return tipoOperacao.equals("TV");
    }

    public Boolean getRetornoTrancamento() {
        return tipoOperacao.equals("RT");
    }

    public Boolean getIncluirModalidade() {
        return tipoOperacao.equals("IM");
    }

    public Boolean getRemoverModalidade() {
        return tipoOperacao.equals("RM");
    }

    public Boolean getAtestado() {
        return tipoOperacao.equals("AT");
    }

	public void setValor(Double valor) {
		this.valor = valor;
	}

	public Double getValor() {
		return valor;
	}

	public void setReciboDevolucao(ReciboDevolucaoVO reciboDevolucao) {
		this.reciboDevolucao = reciboDevolucao;
	}

	public ReciboDevolucaoVO getReciboDevolucao() {
		if(reciboDevolucao == null){
			reciboDevolucao = new ReciboDevolucaoVO();
		}
		return reciboDevolucao;
	}
	
	public boolean getPossuiReciboDevolucao(){
		return !UteisValidacao.emptyNumber(getReciboDevolucao().getCodigo());
	}

    public Double getValorCobrado() {
        return valorCobrado;
    }

    public void setValorCobrado(Double valorCobrado) {
        this.valorCobrado = valorCobrado;
    }

    public void setPermiteEstorno(boolean permiteEstorno) {
            this.permiteEstorno = permiteEstorno;
    }

    public boolean getPermiteEstorno() {
            return permiteEstorno;
    }

    public Integer getNrDiasOperacao() {
        return nrDiasOperacao;
    }

    public void setNrDiasOperacao(Integer nrDiasOperacao) {
        this.nrDiasOperacao = nrDiasOperacao;
    }

    public String getMatriculaApresentar() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNomeApresentar() {
        return nome;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getColaboradorResp() {
        return colaboradorResp;
    }

    public void setColaboradorResp(String colaboradorResp) {
        this.colaboradorResp = colaboradorResp;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getJustificativaApresentar() {
        return justificativaApresentar;
    }

    public void setJustificativaApresentar(String justificativaApresentar) {
        this.justificativaApresentar = justificativaApresentar;
    }

    public JRDataSource getContratoOperacaoDatasource() {
        List<ContratoOperacaoVO> contratoOperacaoVOs = new ArrayList<>();
        contratoOperacaoVOs.add(this);
        return new JRBeanCollectionDataSource(contratoOperacaoVOs);
    }

    public String getInformacoes() {
	    if (informacoes == null) {
            informacoes = "";
        }
        return informacoes;
    }

    public void setInformacoes(String informacoes) {
        this.informacoes = informacoes;
    }

    public String getChaveArquivo() {
        if (chaveArquivo == null) {
            chaveArquivo = "";
        }
        return chaveArquivo;
    }

    public void setChaveArquivo(String chaveArquivo) {
        this.chaveArquivo = chaveArquivo;
    }

    public String getUrlCompletaArquivoAtestado() {
        if (urlCompletaArquivoAtestado == null) {
            urlCompletaArquivoAtestado = "";
        }
        return urlCompletaArquivoAtestado;
    }

    public void setUrlCompletaArquivoAtestado(String urlCompletaArquivoAtestado) {
        this.urlCompletaArquivoAtestado = urlCompletaArquivoAtestado;
    }
    
    public boolean getUrlCompletaEmpty(){
        return !getUrlCompletaArquivoAtestado().isEmpty();
    }

    public OrigemSistemaEnum getOrigemSistema() {
        if (origemSistema == null) {
            origemSistema = OrigemSistemaEnum.ZW;
        }
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public ContratoOperacaoWS toWS() {
        ContratoOperacaoWS contratoOperacaoWS = new ContratoOperacaoWS();
        contratoOperacaoWS.setCodigo(this.codigo);
        contratoOperacaoWS.setContrato(this.getContrato());
        contratoOperacaoWS.setDescricaoCalculo(this.getDescricaoCalculo());
        contratoOperacaoWS.setObservacao(this.getObservacao());
        contratoOperacaoWS.setDataRegistro(Uteis.getDataAplicandoFormatacao(this.getDataOperacao(), "dd/MM/yyyy"));
        contratoOperacaoWS.setDataInicio(Uteis.getDataAplicandoFormatacao(this.getDataInicioEfetivacaoOperacao(), "dd/MM/yyyy"));
        contratoOperacaoWS.setDataFim(Uteis.getDataAplicandoFormatacao(this.getDataFimEfetivacaoOperacao(), "dd/MM/yyyy"));
        contratoOperacaoWS.setTipoOperacao(this.getTipoOperacao());
        contratoOperacaoWS.setTipoOperacaoApresentar(this.getTipoOperacao_Apresentar());
        return contratoOperacaoWS;
    }

    public String getInformacoesDesfazer() {
        if (informacoesDesfazer == null) {
            informacoesDesfazer = "";
        }
        return informacoesDesfazer;
    }

    public void setInformacoesDesfazer(String informacoesDesfazer) {
        this.informacoesDesfazer = informacoesDesfazer;
    }

    public String getAssinaturaDigitalBiometria() {
        if(assinaturaDigitalBiometria == null){
            assinaturaDigitalBiometria = "";
        }
        return assinaturaDigitalBiometria;
    }

    public void setAssinaturaDigitalBiometria(String assinaturaDigitalBiometria) {
        this.assinaturaDigitalBiometria = assinaturaDigitalBiometria;
    }

    private boolean isDeveCalcularNrDias() {
        return !(tipoOperacao.contains("CA")
                || tipoOperacao.contains("AH")
                || tipoOperacao.contains("IM")
                || tipoOperacao.contains("AM")
                || tipoOperacao.contains("EM")
                || tipoOperacao.contains("TS")
                || tipoOperacao.contains("TE"));
    }

    public String getDiasOperacao() {
        if (diasOperacao == null) {
            diasOperacao = "";
            if (isDeveCalcularNrDias()) {
                long nrDias = Uteis.nrDiasEntreDatas(getDataInicioEfetivacaoOperacao(), getDataFimEfetivacaoOperacao()) + 1;
                diasOperacao = Long.toString(nrDias);
            }
        }
        return diasOperacao;
    }

    public void setDiasOperacao(String diasOperacao) {
        this.diasOperacao = diasOperacao;
    }

    public Boolean getAfastamentoColetivo() {
        return tipoOperacao.equals("BC");
    }
}

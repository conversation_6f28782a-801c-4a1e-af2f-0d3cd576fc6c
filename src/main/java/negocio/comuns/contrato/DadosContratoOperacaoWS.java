/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.plano.ContratoModalidadeWS;
import negocio.comuns.plano.ProdutoWS;

/**
 *
 * <AUTHOR>
 */
public class DadosContratoOperacaoWS extends SuperTO {
    private int contrato = 0;
    private int qtdCarenciaPermitida = 0;
    private int qtdCarenciaUtilizado = 0;
    private int qtdMinimaCarencia = 0;
    private int qtdRestanteContrato = 0;
    private List<JustificativaOperacaoWS> justificativas = new ArrayList<JustificativaOperacaoWS>();
    private List<ProdutoWS> produtos = new ArrayList<ProdutoWS>();
    private String tipoOperacao = "";
    
    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public int getQtdCarenciaPermitida() {
        return qtdCarenciaPermitida;
    }

    public void setQtdCarenciaPermitida(int qtdCarenciaPermitida) {
        this.qtdCarenciaPermitida = qtdCarenciaPermitida;
    }

    public int getQtdCarenciaUtilizado() {
        return qtdCarenciaUtilizado;
    }

    public void setQtdCarenciaUtilizado(int qtdCarenciaUtilizado) {
        this.qtdCarenciaUtilizado = qtdCarenciaUtilizado;
    }

    public int getQtdMinimaCarencia() {
        return qtdMinimaCarencia;
    }

    public void setQtdMinimaCarencia(int qtdMinimaCarencia) {
        this.qtdMinimaCarencia = qtdMinimaCarencia;
    }

    public int getQtdRestanteContrato() {
        return qtdRestanteContrato;
    }

    public void setQtdRestanteContrato(int qtdRestanteContrato) {
        this.qtdRestanteContrato = qtdRestanteContrato;
    }

    public List<JustificativaOperacaoWS> getJustificativas() {
        return justificativas;
    }

    public void setJustificativas(List<JustificativaOperacaoWS> justificativas) {
        this.justificativas = justificativas;
    }

    public List<ProdutoWS> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<ProdutoWS> produtos) {
        this.produtos = produtos;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }
}

package negocio.comuns.contrato;

import annotations.arquitetura.ChaveEstrangeira;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

public class OperacaoColetivaVO extends SuperVO {
    private Integer codigo;
    @ChaveEstrangeira
    private EmpresaVO empresa;
    private Date dataInicio;
    private Date dataFinal;
    private Date dataCadastro;
    private TipoOperacaoColetivaEnum tipo;
    private StatusOperacaoColetivaEnum status = StatusOperacaoColetivaEnum.AGUARDANDO;
    private String observacao = "";
    @ChaveEstrangeira
    private PlanoVO planoVO;
    private String resultado;
    private String usuario;
    private String usuarioExclusao;
    private Date dataprocessamento;
    private Boolean operacaoRetroativa = false;
    @ChaveEstrangeira
    private ModalidadeVO modalidadeVO;
    @ChaveEstrangeira
    private TurmaVO turmaVO;
    private Integer idadeMinima;
    private Integer idadeMaxima;
    private String mensagemCatraca;
    private Boolean bloquearAcessoAniversario = false;
    private boolean cancelarParcelasEmAbertoAlunosDesmarcados;

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public TipoOperacaoColetivaEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoOperacaoColetivaEnum tipoOperacao) {
        this.tipo = tipoOperacao;
    }

    public StatusOperacaoColetivaEnum getStatus() {
        return status;
    }

    public void setStatus(StatusOperacaoColetivaEnum status) {
        this.status = status;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public PlanoVO getPlanoVO() {
        if (planoVO == null) {
            planoVO = new PlanoVO();
        }
        return planoVO;
    }

    public void setPlanoVO(PlanoVO planoVO) {
        this.planoVO = planoVO;
    }

    public String getResultado() {
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public Date getDataprocessamento() {
        return dataprocessamento;
    }

    public void setDataprocessamento(Date dataprocessamento) {
        this.dataprocessamento = dataprocessamento;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDataInicio_Apresentar() {
        return (Uteis.getData(this.dataInicio));
    }

    public String getDataFinal_Apresentar() {
        return (Uteis.getData(this.dataFinal));
    }

    public String getDataCadastro_Apresentar() {
        return (Uteis.getDataComHora(this.dataCadastro));
    }

    public String getDataprocessamento_Apresentar() {
        return (Uteis.getDataComHora(this.dataprocessamento));
    }

    public String getTipo_Apresentar() {
        if (this.tipo == null) {
            return "";
        }
        return this.tipo.getDescricao();
    }


    public String getStatus_Apresentar() {
        if (this.status == null) {
            return "";
        }
        return this.status.getDescricao();
    }

    public void validarDados() throws ConsistirException {
        if (UteisValidacao.emptyNumber(this.getEmpresa().getCodigo())) {
            throw new ConsistirException("Informe uma Empresa");
        }
        if (dataInicio == null || dataFinal == null) {
            throw new ConsistirException("Informe corretamente o inicio e o fim do periodo de recesso");
        }
        if (Calendario.menor(dataFinal, dataInicio)) {
            throw new ConsistirException("Data de início do recesso não pode ser menor que data final");
        }
        int qtdDiasLimite = 240;
        if (Uteis.nrDiasEntreDatas(dataInicio, Calendario.hoje()) > qtdDiasLimite) {
            throw new ConsistirException("Data de início não pode ser anterior a mais de " + qtdDiasLimite + " dias da data atual");
        }
        if (getTipo() == null) {
            throw new ConsistirException("O tipo da operação coletiva deve ser informado!");
        }
        if (this.getTipo().equals(TipoOperacaoColetivaEnum.DESMARCAR_AULAS_ALUNOS_PERIODO)) {
            if (UteisValidacao.emptyNumber(this.getModalidadeVO().getCodigo())) {
                throw new ConsistirException("Campo Modalidade deve ser informado!");
            }
            if (UteisValidacao.emptyNumber(this.getTurmaVO().getCodigo())) {
                throw new ConsistirException("Campo Turma deve ser informado!");
            }
        }
        if(UteisValidacao.notEmptyNumber(getIdadeMinima()) || UteisValidacao.notEmptyNumber(getIdadeMaxima())){
            if(idadeMinima == null){
                throw new ConsistirException("Campo Idade Máxima foi informado, então o campo Idade Minima também deve ser informado!");
            }
            if(UteisValidacao.emptyNumber(idadeMaxima) ){
                throw new ConsistirException("Campo Idade Mímima foi informado, então o campo Idade Máxima também deve ser informado!");
            }
            if(idadeMinima > idadeMaxima) {
                throw new ConsistirException("A Idade Mímima informada não pode ser menor que a Idade Máxima");
            }
            if(idadeMaxima > 120){
                throw new ConsistirException("A Idade Máxima deve ser menor que 120");
            }
            if(idadeMinima < 0){
                throw new ConsistirException("A Idade Minima deve ser maior ou igual a 0");
            }
        }
        if (this.getTipo().equals(TipoOperacaoColetivaEnum.BLOQUEAR_ACESSO_CATRACA_POR_PERIODO)) {
            if (UteisValidacao.emptyString(this.mensagemCatraca)) {
                throw new ConsistirException("Informe a mensagem a ser exibida na catraca");
            }
        }
    }

    public String getUsuarioExclusao() {
        return usuarioExclusao;
    }

    public void setUsuarioExclusao(String usuarioExclusao) {
        this.usuarioExclusao = usuarioExclusao;
    }

    public Boolean getOperacaoRetroativa() {
        return operacaoRetroativa;
    }

    public void setOperacaoRetroativa(Boolean operacaoRetroativa) {
        this.operacaoRetroativa = operacaoRetroativa;
    }

    public void validarDataRetroativa() {
        if (dataInicio != null && Calendario.menor(dataInicio, Calendario.hoje())) {
            this.operacaoRetroativa=  true;
        } else {
            this.operacaoRetroativa = false;
        }
    }

    public ModalidadeVO getModalidadeVO() {
        if (modalidadeVO ==null){
            modalidadeVO = new ModalidadeVO();
        }
        return modalidadeVO;
    }

    public void setModalidadeVO(ModalidadeVO modalidadeVO) {
        this.modalidadeVO = modalidadeVO;
    }

    public TurmaVO getTurmaVO() {
        if (turmaVO == null){
            turmaVO = new TurmaVO();
        }
        return turmaVO;
    }

    public void setTurmaVO(TurmaVO turmaVO) {
        this.turmaVO = turmaVO;
    }

    public Integer getIdadeMinima() {
        if(idadeMinima == null){
            idadeMinima = 0;
        }
        return idadeMinima;
    }

    public void setIdadeMinima(Integer idadeMinima) {
        this.idadeMinima = idadeMinima;
    }

    public Integer getIdadeMaxima() {
        if(idadeMaxima == null){
            idadeMaxima = 0;
        }
        return idadeMaxima;
    }

    public void setIdadeMaxima(Integer idadeMaxima) {
        this.idadeMaxima = idadeMaxima;
    }

    public String getDescricaoFaixaEtaria(){
        if(UteisValidacao.notEmptyNumber(getIdadeMinima()) || UteisValidacao.notEmptyNumber(getIdadeMaxima())){
            return "De "+idadeMinima +" até " + idadeMaxima;
        }
        return "";
    }

    public String getMensagemCatraca() {
        return mensagemCatraca;
    }

    public void setMensagemCatraca(String mensagemCatraca) {
        this.mensagemCatraca = mensagemCatraca;
    }

    public Boolean getBloquearAcessoAniversario() {
        return bloquearAcessoAniversario;
    }

    public void setBloquearAcessoAniversario(Boolean bloquearAcessoAniversario) {
        this.bloquearAcessoAniversario = bloquearAcessoAniversario;
    }

    public boolean isCancelarParcelasEmAbertoAlunosDesmarcados() {
        return cancelarParcelasEmAbertoAlunosDesmarcados;
    }

    public void setCancelarParcelasEmAbertoAlunosDesmarcados(boolean cancelarParcelasEmAbertoAlunosDesmarcados) {
        this.cancelarParcelasEmAbertoAlunosDesmarcados = cancelarParcelasEmAbertoAlunosDesmarcados;
    }
}

package negocio.comuns.contrato;

import annotations.arquitetura.FKJson;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * Reponsável por manter os dados da entidade JustificativaOperacao. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class JustificativaOperacaoVO extends SuperVO {

    public static final String JUSTIFICATIVA_RENOVACAO_ANTECIPADA_CREDITO_TREINO = "RENOVACAO ANTECIPADA CREDITO TREINO";
    public static final String JUSTIFICATIVA_ESGOTADOS_CREDITO_TREINO = "CREDITOS DE TREINO ESGOSTADOS";
    public static final String CANCELAMENTO_TRANSFERENCIA_EMPRESA = "TRANSFERÊNCIA DE EMPRESA";
    public static final String CANCELAMENTO_MUDANCA_PLANO = "MUDANÇA DE PLANO";
    public static final String CANCELAMENTO_TRANSFERENCIA_DIAS_CONTRATO = "TRANSFERÊNCIA DIAS CONTRATO";
    protected Integer codigo;
    protected String tipoOperacao;
    protected String descricao;
    /**
     * Atributo responsável por manter o objeto relacionado da classe <code>Empresa </code>.
     */
    @FKJson
    protected EmpresaVO empresa;
    private Boolean isentarMultaCancelamento = false;
    private Boolean naoCobrarParcelasAtrasadasCancelamento = false;
    private Boolean necessarioAnexarComprovante = false;
    private Boolean apresentarTodasEmpresas;
    private Boolean ativa;

    /**
     * Construtor padrão da classe <code>JustificativaOperacao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public JustificativaOperacaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>JustificativaOperacaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(JustificativaOperacaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (!obj.getApresentarTodasEmpresas() && ((obj.getEmpresa() == null) || (obj.getEmpresa().getCodigo() == 0))) {
            throw new ConsistirException("O campo EMPRESA (JustificativaOperacao) deve ser informado.");
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (JustificativaOperacao) deve ser informado.");
        }
        if (obj.getTipoOperacao().equals("")) {
            throw new ConsistirException("TIPO OPERAÇÃO (JustificativaOperacao) deve ser informado.");
        }
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setTipoOperacao(getTipoOperacao().toUpperCase());
        setDescricao(getDescricao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setTipoOperacao("");
        setDescricao("");
        setEmpresa(new EmpresaVO());
    }


    /**
     * Retorna o objeto da classe <code>Empresa</code> relacionado com (<code>JustificativaOperacao</code>).
     */
    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return (empresa);
    }

    /**
     * Define o objeto da classe <code>Empresa</code> relacionado com (<code>JustificativaOperacao</code>).
     */
    public void setEmpresa(EmpresaVO obj) {
        this.empresa = obj;
    }

    public String getDescricao() {
        if (descricao == null) {
            this.descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipoOperacao() {
        if (tipoOperacao == null) {
            tipoOperacao = "";
        }
        return (tipoOperacao);
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico.
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc.
     */
    public String getTipoOperacao_Apresentar() {
        if (tipoOperacao == null) {
            return "";
        }
        if (tipoOperacao.equals("AT")) {
            return "Atestado";
        }
        if (tipoOperacao.equals("CA")) {
            return "Cancelamento";
        }
        if (tipoOperacao.equals("CR")) {
            return "Férias";
        }
        if (tipoOperacao.equals("TR")) {
            return "Trancamento";
        }
        if (tipoOperacao.equals("BO")) {
            return "Bônus";
        }
        return (tipoOperacao);
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getIsentarMultaCancelamento() {
        if (isentarMultaCancelamento == null) {
            isentarMultaCancelamento = false;
        }
        return isentarMultaCancelamento;
    }

    public void setIsentarMultaCancelamento(Boolean isentarMultaCancelamento) {
        this.isentarMultaCancelamento = isentarMultaCancelamento;
    }

    public Boolean getNaoCobrarParcelasAtrasadasCancelamento() {
        if (naoCobrarParcelasAtrasadasCancelamento == null) {
            naoCobrarParcelasAtrasadasCancelamento = false;
        }
        return naoCobrarParcelasAtrasadasCancelamento;
    }

    public void setNaoCobrarParcelasAtrasadasCancelamento(Boolean naoCobrarParcelasAtrasadasCancelamento) {
        this.naoCobrarParcelasAtrasadasCancelamento = naoCobrarParcelasAtrasadasCancelamento;
    }

    public Boolean getNecessarioAnexarComprovante() {
        if (necessarioAnexarComprovante == null) {
            necessarioAnexarComprovante = false;
        }
        return necessarioAnexarComprovante;
    }

    public void setNecessarioAnexarComprovante(Boolean necessarioAnexarComprovante) {
        this.necessarioAnexarComprovante = necessarioAnexarComprovante;
    }

    public Boolean getApresentarTodasEmpresas() {
        if (apresentarTodasEmpresas == null) {
            apresentarTodasEmpresas = false;
        }
        return apresentarTodasEmpresas;
    }

    public void setApresentarTodasEmpresas(Boolean apresentarTodasEmpresas) {
        this.apresentarTodasEmpresas = apresentarTodasEmpresas;
    }

    public Boolean getAtiva() {
        if (ativa == null) {
            ativa = true;
        }
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }
}

package negocio.comuns.contrato;

import java.util.Date;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.CaixaAbertoTO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;

public class MovProdutoModalidadeVO extends SuperVO {

    private Integer codigo = 0;
    private MovProdutoVO movProdutoVO = new MovProdutoVO();
    private ModalidadeVO modalidadeVO = new ModalidadeVO();
    private Date dataInicio;
    private Date dataFim;
    private Double valor = 0.0;
    private ContratoVO contrato = new ContratoVO();

    public static void validarDados(MovProdutoModalidadeVO obj) throws Exception {
        if ((obj.getMovProdutoVO() == null) || (obj.getMovProdutoVO().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo MOVPRODUTO (Movimento Produto Modalidade) deve ser informado.");
        }
        if ((obj.getModalidadeVO() == null) || (obj.getModalidadeVO().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo MODALIDADE (Movimento Produto Modalidade) deve ser informado.");
        }
        if (obj.getDataInicio() == null) {
            throw new ConsistirException("O campo DATAINICIO (Movimento Produto Modalidade) deve ser informado.");
        }
        if (obj.getDataFim() == null) {
            throw new ConsistirException("O campo DATAFIM (Movimento Produto Modalidade) deve ser informado.");
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public MovProdutoVO getMovProdutoVO() {
        return movProdutoVO;
    }

    public void setMovProdutoVO(MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    public ModalidadeVO getModalidadeVO() {
        return modalidadeVO;
    }

    public void setModalidadeVO(ModalidadeVO modadlidadeVO) {
        this.modalidadeVO = modadlidadeVO;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public StringBuilder getDescricaoMovProdutoModalidade(Double valorMensalidade) {
        //regMM.codM||';'||regMM.nome||';'||regMM.valor||';'||porc||';'||regMM.contrato || '^';
        double porcentagem = 1;
        if (valorMensalidade != 0.0f) {
            porcentagem = this.getValor() / (valorMensalidade + 0.0f);
        }
        return new StringBuilder().append(this.getModalidadeVO().getCodigo()).append(";").
                append(this.getModalidadeVO().getNome() == null ? "" :
                        this.getModalidadeVO().getNome().replace(";", "")).append(";").
                append(this.getValor()).append(";").
                append(porcentagem).append(";").
                append(this.getContrato().getCodigo()).append("^");
    }
    
	public boolean equals(Object o) {
		boolean equal = false;
		if (o instanceof CaixaAbertoTO) {
			MovProdutoModalidadeVO item = (MovProdutoModalidadeVO) o;
			 if(this.getMovProdutoVO().getCodigo().equals(item.getMovProdutoVO().getCodigo())
					 && this.getModalidadeVO().getCodigo().equals(item.getModalidadeVO().getCodigo())
					 && Calendario.igual(this.getDataInicio(),item.getDataInicio())
					 && Calendario.igual(this.getDataFim(),item.getDataFim())
					 && this.getValor().equals(item.getValor())
					 && this.getContrato().getCodigo().equals(item.getContrato().getCodigo())){
				 equal = true;
			 }
				 
				 
		}
		return equal;
	}
}

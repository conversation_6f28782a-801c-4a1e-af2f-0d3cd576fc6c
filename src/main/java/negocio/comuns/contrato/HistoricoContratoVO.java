package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.Contrato;

import java.util.Date;

/**
 * Reponsável por manter os dados da entidade HistoricoContrato. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 * @see Contrato
 */
public class HistoricoContratoVO extends SuperVO {

    protected Integer codigo;
    protected Integer contrato;
    protected String descricao;
    protected String tipoHistorico;
    //dataCadastro
    protected Date dataRegistro;
    //tipoHistorico
    protected String situacaoRelativaHistorico;
    protected Date dataInicioSituacao;
    protected Date dataFinalSituacao;
    /**
     * Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.
     */
    //responsavelCadastro
    protected UsuarioVO responsavelRegistro;
    /**
     * Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.
     */
    protected UsuarioVO responsavelLiberacaoMudancaHistorico;
    protected boolean retornoManual = false;
    protected Date dataInicioTemporal;

    /**
     * Construtor padrão da classe <code>HistoricoContrato</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public HistoricoContratoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>HistoricoContratoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(HistoricoContratoVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getResponsavelRegistro() == null) || (obj.getResponsavelRegistro().getCodigo() == 0)) {
            throw new ConsistirException("O campo RESPONSÁVEL REGISTRO (Histórico do Contrato) deve ser informado.");
        }
//        if ((obj.getResponsavelLiberacaoMudancaHistorico() == null) ||
//            (obj.getResponsavelLiberacaoMudancaHistorico().getCodigo().intValue() == 0)) { 
//            throw new ConsistirException("O campo RESPONSÁVEL LIBERAÇÃO MUDANÇA HISTÓRICO (Histórico do Contrato) deve ser informado.");
//        }
        if (obj.getDataRegistro() == null) {
            throw new ConsistirException("O campo DATA REGISTRO (Histórico do Contrato) deve ser informado.");
        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setSituacaoRelativaHistorico(getSituacaoRelativaHistorico().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setDescricao("");
        setTipoHistorico("");
        setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        setSituacaoRelativaHistorico("");
        setDataInicioSituacao(negocio.comuns.utilitarias.Calendario.hoje());
        setDataFinalSituacao(negocio.comuns.utilitarias.Calendario.hoje());
        setResponsavelRegistro(new UsuarioVO());
        setResponsavelLiberacaoMudancaHistorico(new UsuarioVO());
    }

    public Integer obterNrDiasHistorico() {
        long nrDias = Uteis.nrDiasEntreDatas(getDataInicioSituacao(), getDataFinalSituacao()) + 1;
        return new Long(nrDias).intValue();
    }

    public Integer obterNrDiasUtilizadoHistoricoAteDataEspecifica(Date dataEspecifica) throws Exception {
        long nrDias = Uteis.nrDiasEntreDatas(getDataInicioSituacao(), dataEspecifica) + 1;
        return new Long(nrDias).intValue();
    }

    public UsuarioVO getResponsavelLiberacaoMudancaHistorico() {
        return responsavelLiberacaoMudancaHistorico;
    }

    public void setResponsavelLiberacaoMudancaHistorico(UsuarioVO responsavelLiberacaoMudancaHistorico) {
        this.responsavelLiberacaoMudancaHistorico = responsavelLiberacaoMudancaHistorico;
    }

    public UsuarioVO getResponsavelRegistro() {
        return responsavelRegistro;
    }

    public void setResponsavelRegistro(UsuarioVO responsavelRegistro) {
        this.responsavelRegistro = responsavelRegistro;
    }

    public Date getDataFinalSituacao() {
        return (dataFinalSituacao);
    }

    public void setDataFinalSituacao(Date dataFinalSituacao) {
        this.dataFinalSituacao = dataFinalSituacao;
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataFinalSituacao_Apresentar() {
        return (Uteis.getData(dataFinalSituacao));
    }

    public Date getDataInicioSituacao() {
        return (dataInicioSituacao);
    }

    public void setDataInicioSituacao(Date dataInicioSituacao) {
        this.dataInicioSituacao = dataInicioSituacao;
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataInicioSituacao_Apresentar() {
        return (Uteis.getData(dataInicioSituacao));
    }

    public String getSituacaoRelativaHistorico() {
        if (situacaoRelativaHistorico == null) {
            situacaoRelativaHistorico = "";
        }
        return (situacaoRelativaHistorico);
    }

    public void setSituacaoRelativaHistorico(String situacaoRelativaHistorico) {
        this.situacaoRelativaHistorico = situacaoRelativaHistorico;
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico.
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc.
     */
    public String getSituacaoRelativaHistorico_Apresentar() {
        if (situacaoRelativaHistorico == null) {
            situacaoRelativaHistorico = "";
        }
        if (situacaoRelativaHistorico.equals("TR")) {
            return "Trancado";
        }
        if (situacaoRelativaHistorico.equals("CO")) {
            return "Congelado";
        }
        return (situacaoRelativaHistorico);
    }

    public Date getDataRegistro() {
        return (dataRegistro);
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataRegistro_Apresentar() {
        return (Uteis.getData(dataRegistro));
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public String getDescricaoMin() {
        String desc = getDescricao().trim().toUpperCase().indexOf("TRANSFERENCIA (") == 0 ? "TRANSFERENCIA" : getDescricao().toLowerCase();
        return isRetornoManual() ? desc + " (MANUAL)" : desc;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getContrato() {
        return (contrato);
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoHistorico() {
        return tipoHistorico;
    }

    public void setTipoHistorico(String tipoHistorico) {
        this.tipoHistorico = tipoHistorico;
    }

    public Boolean getMatricula() {
        return tipoHistorico.equals("MA");
    }

    public Boolean getReMatricula() {
        return tipoHistorico.equals("RE");
    }

    public Boolean getRenovacao() {
        return tipoHistorico.equals("RN");
    }

    public Boolean getAtestado() {
        return tipoHistorico.equals("AT");
    }

    public Boolean getRetornoAtestado() {
        return tipoHistorico.equals("RA");
    }

    public Boolean getCarencia() {
        return tipoHistorico.equals("CR");
    }

    public Boolean getRetornoCarencia() {
        return tipoHistorico.equals("RC");
    }

    public Boolean getTrancado() {
        return tipoHistorico.equals("TR");
    }

    public Boolean getTrancadoVencido() {
        return tipoHistorico.equals("TV");
    }

    public Boolean getRetornoTrancado() {
        return tipoHistorico.equals("RT");
    }

    public Boolean getBonusAcrescimo() {
        return tipoHistorico.equals("BA");
    }

    public Boolean getBonusReducao() {
        return tipoHistorico.equals("BR");
    }

    public Boolean getCancelado() {
        return tipoHistorico.equals("CA");
    }

    public Boolean getAvencer() {
        return tipoHistorico.equals("AV");
    }

    public Boolean getDesistente() {
        return tipoHistorico.equals("DE");
    }

    public Boolean getVencido() { return tipoHistorico.equals("VE"); }

    public Boolean getTransferidoDeOutraEmpresa() { return tipoHistorico.equals("TE"); }

    public boolean isRetornoManual() {
        return retornoManual;
    }

    public void setRetornoManual(boolean retornoManual) {
        this.retornoManual = retornoManual;
    }

    public String getDescricaoManual() {
        return isRetornoManual() ? getDescricao() + " (MANUAL)" : getDescricao();
    }

    public Date getDataInicioTemporal() {
        return dataInicioTemporal;
    }

    public void setDataInicioTemporal(Date dataInicioTemporal) {
        this.dataInicioTemporal = dataInicioTemporal;
    }

}

package negocio.comuns.contrato;

public enum StatusOperacaoColetivaEnum {
    AGUARDANDO(1, "AGUARDANDO PROCESSSAMENTO"),
    PROCESSADA(2, "PROCESSADA"),
    EXCLUIDA(3, "EXCLUÍDA"),
    ERRO(4, "ERRO"),
    AGUARDANDO_EXCLUSAO(5, "AGUARDANDO EXCLUSAO"),
    PROCESSANDO(6, "PROCESSANDO");

    private Integer codigo;
    private String descricao;

    StatusOperacaoColetivaEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static StatusOperacaoColetivaEnum getStatusOperacaoColetiva(Integer codigo) {
        for (StatusOperacaoColetivaEnum obj : StatusOperacaoColetivaEnum.values()) {
            if (obj.getCodigo().equals(codigo)) {
                return obj;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }


    public String getDescricao() {
        return descricao;
    }
}

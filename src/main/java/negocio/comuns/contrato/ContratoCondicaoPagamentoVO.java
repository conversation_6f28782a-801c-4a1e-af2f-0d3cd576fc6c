package negocio.comuns.contrato;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.facade.jdbc.plano.Plano;

/**
 * Reponsável por manter os dados da entidade PlanoCondicaoPagamento. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see Plano
 */
public class ContratoCondicaoPagamentoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer contrato;
    protected Double percentualDesconto;
    /** Atributo responsável por manter o objeto relacionado da classe <code>CondicaoPagamento </code>.*/
    @ChaveEstrangeira
    protected CondicaoPagamentoVO condicaoPagamento;
    protected String tipoValor;
    protected String tipoOperacao;
    protected Double valorEspecifico;

    /**
     * Construtor padrão da classe <code>PlanoCondicaoPagamento</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ContratoCondicaoPagamentoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PlanoCondicaoPagamentoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ContratoCondicaoPagamentoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getCondicaoPagamento() == null) ||
                (obj.getCondicaoPagamento().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo CONDIÇÃO DE PAGAMENTO (Condições de Pagamento) deve ser informado.");
        }
        if (obj.getTipoValor() == null) {
            obj.setTipoValor("");
        }
        if (obj.getTipoValor().equals("PD")) {
            if (obj.getTipoOperacao() == null || obj.getTipoOperacao().equals("")) {
                throw new ConsistirException("O campo TIPO OPERAÇÃO (Condições de Pagamento) deve ser informado.");
            }
            if (obj.getPercentualDesconto().doubleValue() == 0) {
                throw new ConsistirException("O campo PERCENTUAL (Condições de Pagamento) deve ser informado.");
            }
        }
        if (obj.getTipoValor().equals("VE")) {
            if (obj.getTipoOperacao() == null || obj.getTipoOperacao().equals("")) {
                throw new ConsistirException("O campo TIPO OPERAÇÃO (Condições de Pagamento) deve ser informado.");
            }
            if (obj.getValorEspecifico().doubleValue() == 0) {
                throw new ConsistirException("O campo VALOR (Condições de Pagamento) deve ser informado.");
            }
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setPercentualDesconto(new Double(0));
        setContrato(new Integer(0));
        setCondicaoPagamento(new CondicaoPagamentoVO());
        setValorEspecifico(new Double(0));
        setTipoValor("");
        setTipoOperacao("");
    }

    /**
     * Retorna o objeto da classe <code>CondicaoPagamento</code> relacionado com (<code>PlanoCondicaoPagamento</code>).
     */
    public CondicaoPagamentoVO getCondicaoPagamento() {
        if (condicaoPagamento == null) {
            condicaoPagamento = new CondicaoPagamentoVO();
        }
        return (condicaoPagamento);
    }

    /**
     * Define o objeto da classe <code>CondicaoPagamento</code> relacionado com (<code>PlanoCondicaoPagamento</code>).
     */
    public void setCondicaoPagamento(CondicaoPagamentoVO obj) {
        this.condicaoPagamento = obj;
    }

    public Double getPercentualDesconto() {
        return (percentualDesconto);
    }

    public void setPercentualDesconto(Double percentualDesconto) {
        this.percentualDesconto = percentualDesconto;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }


    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }



    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getTipoOperacao() {
        if (tipoOperacao == null) {
            tipoOperacao = "";
        }
        return tipoOperacao;
    }

    public String getTipoOperacao_Apresentar() {
        if (tipoOperacao == null || tipoOperacao.equals("")) {
            return ("");
        }

        if (tipoOperacao.equals("AC")) {
            return ("Acrescimo");
        }
        if (tipoOperacao.equals("RE")) {
            return ("Redução");
        }

        return tipoOperacao;
    }
    public String getTipoValor() {
        if (tipoValor == null) {
            tipoValor = "";
        }
        return tipoValor;
    }

    public void setTipoValor(String tipoValor) {
        this.tipoValor = tipoValor;
    }

    public Double getValorEspecifico() {
        return valorEspecifico;
    }

    public void setValorEspecifico(Double valorEspecifico) {
        this.valorEspecifico = valorEspecifico;
    }

}

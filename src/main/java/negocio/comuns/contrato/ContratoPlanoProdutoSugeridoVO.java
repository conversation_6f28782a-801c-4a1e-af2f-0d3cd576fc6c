/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.PlanoProdutoSugeridoVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class ContratoPlanoProdutoSugeridoVO extends SuperVO {

    protected Integer codigo;
    protected Integer contrato;
    protected PlanoProdutoSugeridoVO planoProdutoSugerido;
    protected Double valorFinalProduto;

    public ContratoPlanoProdutoSugeridoVO() {
        super();
        inicializarDados();

    }

    public void inicializarDados() {
        setCodigo(new Integer(0));
        setContrato(new Integer(0));
        setPlanoProdutoSugerido(new PlanoProdutoSugeridoVO());
        setValorFinalProduto(new Double(0));
    }
    
     public static void validarDados(ContratoPlanoProdutoSugeridoVO obj) throws ConsistirException {
         if (!obj.getValidarDados().booleanValue()) {
            return;
            }
     }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public PlanoProdutoSugeridoVO getPlanoProdutoSugerido() {
        return planoProdutoSugerido;
    }

    public void setPlanoProdutoSugerido(PlanoProdutoSugeridoVO planoProdutoSugerido) {
        this.planoProdutoSugerido = planoProdutoSugerido;
    }

    public Double getValorFinalProduto() {
        return valorFinalProduto;
    }

    public void setValorFinalProduto(Double valorFinalProduto) {
        this.valorFinalProduto = valorFinalProduto;
    }

}

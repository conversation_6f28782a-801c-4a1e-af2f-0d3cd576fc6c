package negocio.comuns.contrato;

import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.plano.AlunoHorarioTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;

import java.util.Date;

/**
 * Created by ulisses on 17/11/2015.
 */
public class ControleCreditoTreinoVO extends SuperVO {

    private Integer codigo;
    private ContratoVO contratoVO;
    private TipoOperacaoCreditoTreinoEnum tipoOperacaoCreditoTreinoEnum;
    private Integer quantidade;
    private Date datalancamento;
    private Date dataOperacao;
    private UsuarioVO usuarioVO;
    private AcessoClienteVO acessoClienteVO;
    private ReposicaoVO reposicaoVO;
    private AulaDesmarcadaVO aulaDesmarcadaVO;
    private String observacao;
    private Integer saldo = 0;
    private Integer codigoTipoAjusteManualCreditoTreino;// atributo transient.
    private ContratoVO contratoOrigem;
    private HorarioTurmaVO horarioTurmaFalta;
    private ModalidadeVO modalidadeVO;
    private String descricaoAulaMarcada;
    private HorarioTurmaVO horarioTurma;
    private Date diaAula;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public TipoOperacaoCreditoTreinoEnum getTipoOperacaoCreditoTreinoEnum() {
        return tipoOperacaoCreditoTreinoEnum;
    }

    public void setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum tipoOperacaoCreditoTreinoEnum) {
        this.tipoOperacaoCreditoTreinoEnum = tipoOperacaoCreditoTreinoEnum;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Date getDatalancamento() {
        return datalancamento;
    }

    public void setDatalancamento(Date datalancamento) {
        this.datalancamento = datalancamento;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public AcessoClienteVO getAcessoClienteVO() {
        return acessoClienteVO;
    }

    public void setAcessoClienteVO(AcessoClienteVO acessoClienteVO) {
        this.acessoClienteVO = acessoClienteVO;
    }

    public ReposicaoVO getReposicaoVO() {
        return reposicaoVO;
    }

    public void setReposicaoVO(ReposicaoVO reposicaoVO) {
        this.reposicaoVO = reposicaoVO;
    }



    public String getObservacao() {
        if(observacao == null){
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getDataLancamento_Apresentar(){
        return Calendario.getData(this.datalancamento, "dd/MM/yyyy HH:mm:ss");
    }
    public String getDataOperacao_Apresentar(){
        if (this.dataOperacao != null)
          return Calendario.getData(this.dataOperacao, "dd/MM/yyyy HH:mm:ss");
        return "";
    }

    public String getAulaDesmarcada_Apresentar()throws Exception{
        if (UtilReflection.objetoMaiorQueZero(this.reposicaoVO , "getCodigo()")){
            return this.reposicaoVO.getDescricaoOrigemCurta();
        }if (UtilReflection.objetoMaiorQueZero(this.aulaDesmarcadaVO , "getCodigo()")){
            return this.aulaDesmarcadaVO.getDescricaoOrigemCurta();
        }
        return "";
    }

    public String getAulaDesmarcadaCurta()throws Exception{
        String desc = getAulaDesmarcada_Apresentar();
        if (desc.toString().length() > 45){
            desc = desc.substring(0,44) + "...";
        }
        return desc.toString();
    }

    public String getAulaMarcada_Apresentar()throws Exception{
        if (UtilReflection.objetoMaiorQueZero(this.reposicaoVO , "getCodigo()")){
            return this.reposicaoVO.getDescricaoDestinoCurta();
        }
        if (!UteisValidacao.emptyString(getDescricaoAulaMarcada())) {
            return getDescricaoAulaMarcada();
        }
        return "";
    }

    public String getAulaMarcadaCurta()throws Exception{
        String desc = getAulaMarcada_Apresentar();
        if (desc.toString().length() > 48){
            desc = desc.substring(0,45) + "...";
        }
        return desc.toString();
    }


    public Integer getSaldo() {
        return saldo;
    }


    public void setSaldo(Integer saldo) {
        this.saldo = saldo;
    }

    public AulaDesmarcadaVO getAulaDesmarcadaVO() {
        return aulaDesmarcadaVO;
    }

    public void setAulaDesmarcadaVO(AulaDesmarcadaVO aulaDesmarcadaVO) {
        this.aulaDesmarcadaVO = aulaDesmarcadaVO;
    }

    public String getObservacaoCurta(){
        if ((this.observacao != null) && (!this.observacao.trim().equals(""))){
            if (this.observacao.length() > 15) {
                return this.observacao.substring(0, 14) + "...";
            } else {
                return this.observacao;
            }
        }
        return "";
    }

    public String getNomeUsuarioCurto()throws Exception{
        if (UtilReflection.objetoMaiorQueZero(usuarioVO, "getCodigo()")){
            if (this.usuarioVO.getNome().length() > 15)
                return this.usuarioVO.getNome().substring(0, 12) + "...";
            else
                return this.usuarioVO.getNome();
        }
        return "";

    }

    public String getOperacao(){
        return this.tipoOperacaoCreditoTreinoEnum.getDescricao();
    }

    public String getUsuario_Apresentar(){
        return this.usuarioVO.getNome();
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getSistemaOrigem(){
        try{
            if (UtilReflection.objetoMaiorQueZero(reposicaoVO, "getCodigo()")){
                return reposicaoVO.getOrigemSistemaEnum().getDescricao();
            }else if (UtilReflection.objetoMaiorQueZero(aulaDesmarcadaVO, "getCodigo()")){
                return aulaDesmarcadaVO.getOrigemSistemaEnum().getDescricao();
            }
        }catch (Exception e){
            return null;
        }
        return null;
    }

    public static void validarDadosInclusao(ControleCreditoTreinoVO obj) throws Exception {
        if (obj.getCodigoTipoAjusteManualCreditoTreino() == 0) {
            throw new ConsistirException("O campo Tipo Operação (ControleCreditoTreino) deve ser informado.");
        }
        validarDados(obj);
        if (obj.getCodigoTipoAjusteManualCreditoTreino() == 2){
            if (obj.getQuantidade() > 0)
              obj.setQuantidade(obj.getQuantidade() * -1);
        }else if (obj.getCodigoTipoAjusteManualCreditoTreino() == 1){
            if (obj.getQuantidade() < 0)
                obj.setQuantidade(obj.getQuantidade() * -1);
        }

    }

    public static void validarDados(ControleCreditoTreinoVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }

        if (!UtilReflection.objetoMaiorQueZero(obj, "getContratoVO().getCodigo()")) {
            throw new ConsistirException("O campo CONTRATO (ControleCreditoTreino) deve ser informado.");
        }
        if (obj.getTipoOperacaoCreditoTreinoEnum() == null) {
            throw new ConsistirException("O campo TIPO OPERAÇÃO (ControleCreditoTreino) deve ser informado.");
        }
        if (!UtilReflection.objetoMaiorQueZero(obj, "getUsuarioVO().getCodigo()")) {
            throw new ConsistirException("O campo USUÁRIO (ControleCreditoTreino) deve ser informado.");
        }
        if (obj.getTipoOperacaoCreditoTreinoEnum() == TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL){
            if ((obj.getQuantidade() == null) || (obj.getQuantidade() == 0)){
                throw new ConsistirException("O campo QUANTIDADE (ControleCreditoTreino) deve ser informado.");
            }
        }
    }

    public Integer getCodigoTipoAjusteManualCreditoTreino() {
        if (codigoTipoAjusteManualCreditoTreino == null) {
            codigoTipoAjusteManualCreditoTreino = 0;
        }
        return codigoTipoAjusteManualCreditoTreino;
    }

    public void setCodigoTipoAjusteManualCreditoTreino(Integer codigoTipoAjusteManualCreditoTreino) {
        this.codigoTipoAjusteManualCreditoTreino = codigoTipoAjusteManualCreditoTreino;
    }

    public ContratoVO getContratoOrigem() {
        return contratoOrigem;
    }

    public void setContratoOrigem(ContratoVO contratoOrigem) {
        this.contratoOrigem = contratoOrigem;
    }

    public HorarioTurmaVO getHorarioTurmaFalta() {
        return horarioTurmaFalta;
    }

    public void setHorarioTurmaFalta(HorarioTurmaVO horarioTurmaFalta) {
        this.horarioTurmaFalta = horarioTurmaFalta;
    }

    public ModalidadeVO getModalidadeVO() {
        if (modalidadeVO == null) {
            modalidadeVO = new ModalidadeVO();
        }
        return modalidadeVO;
    }

    public void setModalidadeVO(ModalidadeVO modalidadeVO) {
        this.modalidadeVO = modalidadeVO;
    }

    public String getDescricaoAulaMarcada() {
        return descricaoAulaMarcada;
    }

    public void setDescricaoAulaMarcada(String descricaoAulaMarcada) {
        this.descricaoAulaMarcada = descricaoAulaMarcada;
    }

    public HorarioTurmaVO getHorarioTurma() {
        if (horarioTurma == null) {
            horarioTurma = new HorarioTurmaVO();
        }
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaVO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public Date getDiaAula() {
        return diaAula;
    }

    public void setDiaAula(Date diaAula) {
        this.diaAula = diaAula;
    }
}

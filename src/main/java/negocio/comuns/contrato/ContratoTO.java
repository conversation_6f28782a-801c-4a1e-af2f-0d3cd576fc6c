package negocio.comuns.contrato;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.MovParcelaTO;

/**
 *
 * <AUTHOR>
 */
public class ContratoTO extends SuperTO {

    private static final long serialVersionUID = 2593651321051219289L;
    private int codigo = 0;
    private String nomePessoa = "";
    private String situacaoContrato = "";
    private List<MovParcelaTO> parcelas = new ArrayList<MovParcelaTO>();

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public List<MovParcelaTO> getParcelas() {
        return parcelas;
    }

    public void setParcelas(List<MovParcelaTO> parcelas) {
        this.parcelas = parcelas;
    }
}

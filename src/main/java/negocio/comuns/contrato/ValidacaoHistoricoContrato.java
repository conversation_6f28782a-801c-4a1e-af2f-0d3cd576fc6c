/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import java.sql.Connection;
import java.util.Date;
import java.util.List;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.contrato.HistoricoContrato;
import negocio.interfaces.contrato.HistoricoContratoInterfaceFacade;

/**
 * Tem por objetivo impor regras de validação quanto ao lançamento de operações
 * que alteram o histórico de contrato, nao permitir datas chocantes ou inválidas,
 * entre outros.
 *
 * <AUTHOR>
 * @date 26/04/2011
 */
public class ValidacaoHistoricoContrato extends FacadeManager {

    /**
     * Verifica se a partir de uma dada operação com dataInicio e fim, vai chocar
     * com um histórico de contrato já existente.
     */
    public static void validarPeriodoHistoricoContratoOperacao(final Date dataInicio,
            final Date dataFim, final int codigoContrato, HistoricoContratoInterfaceFacade historicoContrato,
            final HistoricoContratoVO historicoNovo) throws Exception {
        /**
         * Não validar se a operação é retroativa, pois esta não mudará o histórico de contrato,
         * exceto para Cancelamento
         */
        if (Calendario.menor(dataInicio, Calendario.getDataComHoraZerada(Calendario.hoje()))) {
            if (historicoNovo.getTipoHistorico().equals("CA")) {
                List<HistoricoContratoVO> lista = historicoContrato.consultarPorContrato(codigoContrato, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (HistoricoContratoVO historicoContratoVO : lista) {
                    //verificar se existe um histórico de trancamento ou retorno de trancamento depois da data de cancelamento
                    if (historicoContratoVO.getTipoHistorico().equals("TR")) {
                        if (Calendario.menorOuIgual(historicoNovo.getDataInicioSituacao(), historicoContratoVO.getDataInicioSituacao())) {
                            throw new Exception(String.format(
                                    "Esta Operação não é suportada. "
                                    + "A data de um Cancelamento não pode anteceder ou começar no mesmo dia de uma operação de: \"%s\".",
                                    new Object[]{
                                        historicoContratoVO.getDescricao()
                                    }));
                        }
                    } else if (historicoContratoVO.getTipoHistorico().equals("RT")) {
                        if (Calendario.menor(historicoNovo.getDataInicioSituacao(), historicoContratoVO.getDataInicioSituacao())) {
                            throw new Exception(String.format(
                                    "Esta Operação não é suportada. "
                                            + "A data de um Cancelamento não pode anteceder uma operação de: \"%s\".",
                                    new Object[]{
                                            historicoContratoVO.getDescricao()
                                    }));
                        }
                    }
                }
            }
            return;
        }
        if (codigoContrato != 0) {
            List<HistoricoContratoVO> lista = historicoContrato.consultarPorContrato(codigoContrato, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (HistoricoContratoVO historicoContratoVO : lista) {
                //historico a vencer não deve ser considerado nessa validação
                if (historicoContratoVO.getTipoHistorico().equals("AV")) {
                    continue;
                }

                if (!historicoNovo.getTipoHistorico().equals("CA")) {
                    if (Calendario.igual(dataInicio, historicoContratoVO.getDataInicioSituacao())) {
                        throw new Exception(String.format(
                                "Já existe um histórico de contrato \"%s\" para o contrato \"%s\" com a mesma data de início \"%s\".",
                                new Object[]{
                                    historicoContratoVO.getDescricao(),
                                    historicoContratoVO.getContrato().toString(),
                                    Uteis.getData(historicoContratoVO.getDataInicioSituacao())
                                }));
                    }
                    if (Calendario.igual(dataFim, historicoContratoVO.getDataFinalSituacao())) {
                        throw new Exception(String.format(
                                "Já existe um histórico de contrato \"%s\" para o contrato \"%s\" com a mesma data final \"%s\".",
                                new Object[]{
                                    historicoContratoVO.getDescricao(),
                                    historicoContratoVO.getContrato().toString(),
                                    Uteis.getData(historicoContratoVO.getDataFinalSituacao())
                                }));
                    }
                }

                if ((Calendario.entre(dataInicio,
                        historicoContratoVO.getDataInicioSituacao(), historicoContratoVO.getDataFinalSituacao())
                        || Calendario.entre(dataFim,
                        historicoContratoVO.getDataInicioSituacao(), historicoContratoVO.getDataFinalSituacao()))
                && !(historicoNovo.getCancelado() && historicoContratoVO.getRetornoTrancado()) ) {

                    throw new Exception(String.format(
                            "Já existe um histórico de contrato \"%s\" para o contrato \"%s\" no período de \"%s\" até \"%s\".",
                            new Object[]{
                                historicoContratoVO.getDescricao(),
                                historicoContratoVO.getContrato().toString(),
                                Uteis.getData(historicoContratoVO.getDataInicioSituacao()),
                                Uteis.getData(historicoContratoVO.getDataFinalSituacao())
                            }));

                }


            }

        }

    }

    /**
     * Executa as seguintes validações e lança uma Excetion:
     * 1. Se a quantidade de dias do bônus é maior que a duração do contrato em dias
     * 2. Se o bônus está sendo lançado a mais de 90 dias do vencimento do contrato, considerando a data final original do contrato.
     * 3. Se o bônus de retirada está tentando retirar mais dias do que o restante do contrato na data atual.
     * 
     * @throws Exception
     */
    public static void validarBonus(final BonusContratoVO bonus) throws Exception {
        if (bonus.getContratoVO().getVigenciaAteAjustada().compareTo(Calendario.hoje()) < 0) {
            if (Math.abs(Uteis.nrDiasEntreDatas(bonus.dataRegistro, bonus.getContratoVO().getVigenciaAteAjustada())) > 90) {
                throw new Exception("Para contratos vencidos há mais de 90 dias não é permitido o lançamento de bônus.");
            }
        }

        List<ContratoOperacaoVO> lista = getFacade().getContratoOperacao().consultarPorContrato(bonus.getContratoVO().getCodigo(), false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
      

        if (bonus.getAcrescentarDiaContrato().equals("AC")) {
            int diasUtilizados = 0;

            for (ContratoOperacaoVO op : lista) {
                if (op.getBonusAcrescimo()) {
                    diasUtilizados += op.obterNrDiasContratoOperacao();
                }
            }
            if ((diasUtilizados + bonus.getNrDias()) >
                    Uteis.nrDiasEntreDatas(bonus.getContratoVO().getVigenciaDe(), bonus.getContratoVO().getVigenciaAte()) + 1) {
                throw new Exception("A quantidade de dias utilizados em bônus "
                        + "para este contrato ultrapassa a quantidade de dias do Contrato originalmente.");
            }
        }

        if (bonus.getAcrescentarDiaContrato().equals("RE")) {
            long diasRestantes = Math.abs(Uteis.nrDiasEntreDatas(bonus.getContratoVO().getVigenciaAteAjustada(), Calendario.hoje()));
            long diasRestantesLanFuturo = Math.abs(Uteis.nrDiasEntreDatas(bonus.getContratoVO().getVigenciaAteAjustada(), bonus.getContratoVO().vigenciaDe));
            if (bonus.getNrDias() > diasRestantes || bonus.getNrDias() > diasRestantesLanFuturo) {
                throw new Exception(String.format("Quantidade de dias do bônus de "
                        + "retirada não pode ser superior ou igual ao número de dias "
                        + "restantes do Contrato (%s).", new Object[]{(bonus.getContratoVO().vigenciaDe.after(Calendario.hoje()) ? diasRestantesLanFuturo : diasRestantes)}));
            }

            for (ContratoOperacaoVO op : lista) {
                if (Calendario.menorOuIgual(bonus.getDataTermino(), op.getDataFimEfetivacaoOperacao())) {
                    if (op.getTrancamento() || op.getAtestado() || op.getCarenciaFerias() || op.getTrancamentoVencido()) {
                        throw new Exception(String.format("A retirada de %s dia(s) não pode ser realizada, "
                                + " pois existe uma operação de '%s' no período de '%s' até '%s', e com essa retirada a data final do contrato seria entre esse período",
                                new Object[]{ bonus.getNrDias(),
                                    op.getTipoOperacao_Apresentar().toUpperCase(),
                                    op.getDataInicioEfetivacaoOperacao_Apresentar(),
                                    op.getDataFimEfetivacaoOperacao_Apresentar()
                                }));
                }

                }
            }
        }
        
    }

    public static void validarTrancamentoRetornoPendente(final TrancamentoContratoVO trancamento)
            throws Exception {
        HistoricoContratoVO ultimoTrancamento = getFacade().getHistoricoContrato().
                obterUltimoHistoricoContratoPorContratoTipoHistorico(trancamento.getContratoVO().getCodigo(),
                "TR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (ultimoTrancamento != null) {//se existe um trancamento procurar se o retorno já foi dado manualmente,
            //ou se já está no período do retorno de trancamento automático (caso exista)

            HistoricoContratoVO ultimoRetorno = getFacade().getHistoricoContrato().
                    obterUltimoHistoricoContratoPorContratoTipoHistorico(trancamento.getContratoVO().getCodigo(),
                    "RT", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (ultimoRetorno != null) {

                if (!ultimoRetorno.isRetornoManual()
                        && !Calendario.entre(trancamento.getDataTrancamento(), ultimoRetorno.getDataInicioSituacao(),
                        ultimoRetorno.getDataFinalSituacao())) {
                    throw new Exception(
                            "Não é possível lançar o trancamento para o contrato porque existe um retorno de trancamento pendente para este mesmo contrato. Efetue o retorno e tente novamente.");

                }
            } else {
                throw new Exception(
                        "Não é possível realizar a operação, pois não existe o retorno de trancamento para o último trancamento. Efetue o retorno e tente novamente.");

            }
        }
    }

    /**
     * Não é permitido lançar um retorno de operação na mesma data da operação de origem
     */
    public static void validarOperacaoRetornoNoMesmoDiaOperacaoOrigem(Date dataOperacaoRetorno,
            Date dataOperacaoOrigem) throws Exception {
        if (Calendario.igual(dataOperacaoRetorno, dataOperacaoOrigem)) {
            throw new Exception("Não é permitido lançar o retorno desta operação no mesmo dia da operação de origem.");
        }
    }
    
     public static void validarTrancamentoRetornoPendente(Integer contrato, Date dataReferencia, Connection con)
            throws Exception {
        HistoricoContrato historicoDAO = new HistoricoContrato(con);
        try{
            HistoricoContratoVO ultimoTrancamento = historicoDAO.
                    obterUltimoHistoricoContratoPorContratoTipoHistorico(contrato,
                    "TR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (ultimoTrancamento != null) {//se existe um trancamento procurar se o retorno já foi dado manualmente,
                //ou se já está no período do retorno de trancamento automático (caso exista)

                HistoricoContratoVO ultimoRetorno = historicoDAO.
                        obterUltimoHistoricoContratoPorContratoTipoHistorico(contrato,
                        "RT", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (ultimoRetorno != null) {

                    if (!ultimoRetorno.isRetornoManual()
                            && !Calendario.entre(dataReferencia, ultimoRetorno.getDataInicioSituacao(),
                            ultimoRetorno.getDataFinalSituacao())) {
                        throw new Exception(
                                "Não é possível lançar o trancamento para o contrato porque existe um retorno de trancamento pendente para este mesmo contrato. Efetue o retorno e tente novamente.");

                    }
                } else {
                    throw new Exception(
                            "Não é possível realizar a operação, pois não existe o retorno de trancamento para o último trancamento. Efetue o retorno e tente novamente.");

                }
            }
        }catch (Exception e){
            throw  e;
        } finally {
            historicoDAO = null;
        }
    }
}

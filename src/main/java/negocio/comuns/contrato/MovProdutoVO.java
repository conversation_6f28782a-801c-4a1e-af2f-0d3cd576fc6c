package negocio.comuns.contrato;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.estudio.modelo.PacoteVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.ReciboDevolucaoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade MovProduto. Classe do tipo VO -
 * Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos
 * de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * 
 * @see SuperVO
 */
public class MovProdutoVO extends SuperVO implements Cloneable {

    protected Integer codigo;
    protected String descricao = "";
    //protected String nomePessoa;
    protected Integer quantidade;
    protected Double precoUnitario;
    private Double juros;
    private Double multa;
    private Double jurosNaoRecebidos;
    private Double multaNaoRecebida;
    protected Double valorDesconto;
    protected Double totalFinal;
    protected Double valorFaturado;
    protected Date dataLancamento;
    protected String mesReferencia;
    protected String situacao;
    protected Integer anoReferencia;
    protected Date dataInicioVigencia;
    protected Date dataFinalVigencia;
    protected Date dataFinalVigenciaOriginal;
    protected Boolean apresentarMovProduto;
    protected Boolean quitado;
    protected String mudarCorSituacaoEmAberto;
    protected String statusProtheus;
    protected String linkNota;
    protected Boolean naoGerarMensagem = false;
    private String numeroCupomDesconto;
    private Integer clienteTitular; // atributo transiente
    private Double totalFinalApresentarTelaNegociacao; // atributo transient
    private Integer numeroParcela; // atributo transient
    private Double valorMovProdutoParcela; // atributo transient
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Produto </code>.
     */
    protected ProdutoVO produto;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Contrato </code>.
     */
    protected ContratoVO contrato;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Pessoa </code>.
     */
    protected PessoaVO pessoa;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Empresa </code>.
     */
    protected EmpresaVO empresa;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Colaborador </code>.
     */
    protected UsuarioVO responsavelLancamento;
    /*atributo criado para ser utilizado somente na hora de fazer o relatorio de caixa*/
    protected Double valorPagoMovProdutoParcela;
    private Double valorParcialmentePago;
    private String movpagamentocc;
    private ReciboDevolucaoVO reciboDevolucao;
    private String nomeAlunoPersonal;
    private Integer vendaAvulsa;
    /**
     * Atributo responsável por manter os objetos da classe
     * <code>MovProdutoParcela</code>.
     */
    private List movProdutoParcelaVOs;
    @NaoControlarLogAlteracao
    private List<MovProdutoModalidadeVO> movProdutoModalidades = new ArrayList<>();
    @NaoControlarLogAlteracao
    private StringBuilder descricaoMovProdutoModalidade;
    private PacoteVO pacoteVO = new PacoteVO();
    private Integer lancamentoColetivo = null;
    private ChequeVO chequeDevolucao;
    private String codigoOperacaoFinanceira;
    private Date dataCancelamento;
    private Date dataPagamento;
    @NaoControlarLogAlteracao
    private MovPagamentoVO movPagamento;

    private Integer movProdutoBase;

    private Boolean renovavelAutomaticamente = false;

    @NaoControlarLogAlteracao
    private Boolean vigenciaJaCalculada = false;

    /**
     * Construtor padrão da classe <code>MovProduto</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente
     * seus atributos (Classe VO).
     */
    public MovProdutoVO() {
        super();
        inicializarDados();
    }

    public MovProdutoVO(Integer codigo) {
            this.codigo = codigo;
    }

    public String getProduto_Apresentar() {
        return getProduto().getDescricao();
    }

    public int getContrato_Apresentar() {
        return getContrato().getCodigo();
    }

    public String getPessoa_Apresentar() {
        return getPessoa().getNome();
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public String getResponsavel_Apresentar() {
        return getResponsavelLancamento().getNome();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>MovProdutoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas
     * neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação
     * de valores válidos para os atributos.
     *
     * @exception ConsistirException
     *                Se uma inconsistência for encontrada aumaticamente é
     *                gerada uma exceção descrevendo
     *                o atributo e o erro ocorrido.
     */
    public static void validarDados(MovProdutoVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getProduto() == null) || (obj.getProduto().getCodigo() == 0)) {
            throw new ConsistirException("O campo PRODUTO (Movimento Produto) deve ser informado.");
        }
        if ((obj.getEmpresa() == null) || (obj.getEmpresa().getCodigo() == 0)) {
            throw new ConsistirException("O campo EMPRESA (Movimento Produto) deve ser informado.");
        }
        if (obj.getDescricao().isEmpty()) {
            throw new ConsistirException("O campo DESCRIÇÃO (Movimento Produto) deve ser informado.");
        }
        if ((obj.getResponsavelLancamento() == null) || (obj.getResponsavelLancamento().getCodigo() == 0)) {
            throw new ConsistirException("O campo RESPONSÁVEL LANÇAMENTO (Movimento Produto) deve ser informado.");
        }
    }

    public static void validarDataFinalVigencia(MovProdutoVO obj) throws Exception {
        if (!obj.getNovoObj()
                || obj.getDataFinalVigencia().compareTo(obj.getDataFinalVigenciaOriginal()) != 0) {

            Date hoje = Calendario.hoje();
            hoje = Uteis.retirarHoraDaData(hoje);

            if (obj.getDataFinalVigencia().compareTo(hoje) < 0) {
                throw new ConsistirException("A data final da vigência não pode ser definida para antes de hoje.");
            }
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setMesReferencia(getMesReferencia().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setDescricao("");
        setSituacao("EA");
        setQuantidade(0);
        setPrecoUnitario(0.0);
        setValorDesconto(0.0);
        setTotalFinal(0.0);

        setDataLancamento(Calendario.hoje());
        setMesReferencia("");
        setAnoReferencia(0);
        setDataInicioVigencia(Calendario.hoje());
        setDataFinalVigencia(Calendario.hoje());
        setMovProdutoParcelaVOs(new ArrayList<>());
        setApresentarMovProduto(false);
        setQuitado(false);
        setValorPagoMovProdutoParcela(0.0);
        setMudarCorSituacaoEmAberto("blue");
        setNomeAlunoPersonal("");
    }

    /**
     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (<code>MovProduto</code>).
     */
    public UsuarioVO getResponsavelLancamento() {
        if (responsavelLancamento == null) {
            responsavelLancamento = new UsuarioVO();
        }
        return responsavelLancamento;
    }

    /**
     * Define o objeto da classe <code>Colaborador</code> relacionado com (<code>MovProduto</code>).
     */
    public void setResponsavelLancamento(UsuarioVO obj) {
        this.responsavelLancamento = obj;
    }

    /**
     * Retorna o objeto da classe <code>Empresa</code> relacionado com (<code>MovProduto</code>).
     */
    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    /**
     * Define o objeto da classe <code>Empresa</code> relacionado com (<code>MovProduto</code>).
     */
    public void setEmpresa(EmpresaVO obj) {
        this.empresa = obj;
    }

    /**
     * Retorna o objeto da classe <code>Pessoa</code> relacionado com (<code>MovProduto</code>).
     */
    public PessoaVO getPessoa() {
        if (pessoa == null) {
            pessoa = new PessoaVO();
        }
        return pessoa;
    }

    /**
     * Define o objeto da classe <code>Pessoa</code> relacionado com (<code>MovProduto</code>).
     */
    public void setPessoa(PessoaVO obj) {
        this.pessoa = obj;
    }

    /**
     * Retorna o objeto da classe <code>Contrato</code> relacionado com (<code>MovProduto</code>).
     */
    public ContratoVO getContrato() {
        if (contrato == null) {
            contrato = new ContratoVO();
        }
        return contrato;
    }

    /**
     * Define o objeto da classe <code>Contrato</code> relacionado com (<code>MovProduto</code>).
     */
    public void setContrato(ContratoVO obj) {
        this.contrato = obj;
    }

    /**
     * Retorna o objeto da classe <code>Produto</code> relacionado com (<code>MovProduto</code>).
     */
    public ProdutoVO getProduto() {
        if (produto == null) {
            produto = new ProdutoVO();
        }
        return produto;
    }

    /**
     * Define o objeto da classe <code>Produto</code> relacionado com (<code>MovProduto</code>).
     */
    public void setProduto(ProdutoVO obj) {
        this.produto = obj;
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe <code>MovProdutoParcela</code>.
     */
    public List<MovProdutoParcelaVO> getMovProdutoParcelaVOs() {
        return movProdutoParcelaVOs;
    }

    /**
     * Define Atributo responsável por manter os objetos da classe <code>MovProdutoParcela</code>.
     */
    public void setMovProdutoParcelaVOs(List movProdutoParcelaVOs) {
        this.movProdutoParcelaVOs = movProdutoParcelaVOs;
    }

    public Date getDataFinalVigencia() {
        return (dataFinalVigencia);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataFinalVigencia_Apresentar() {
        return (Uteis.getData(dataFinalVigencia));
    }

    public void setDataFinalVigencia(Date dataFinalVigencia) {
        this.dataFinalVigencia = dataFinalVigencia;
    }

    public Date getDataInicioVigencia() {
        return (dataInicioVigencia);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataInicioVigencia_Apresentar() {
        return (Uteis.getData(dataInicioVigencia));
    }

    public void setDataInicioVigencia(Date dataInicioVigencia) {
        this.dataInicioVigencia = dataInicioVigencia;
    }

    public Integer getAnoReferencia() {
        return (anoReferencia);
    }

    public void setAnoReferencia(Integer anoReferencia) {
        this.anoReferencia = anoReferencia;
    }

    public String getMesReferencia() {
        if (mesReferencia == null) {
            mesReferencia = "";
        }
        return (mesReferencia);
    }

    public void setMesReferencia(String mesReferencia) {
        this.mesReferencia = mesReferencia;
    }

    public Date getDataLancamento() {
        return (dataLancamento);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato
     * padrão dd/mm/aaaa.
     */
    public String getDataLancamento_Apresentar() {
        return (Uteis.getData(dataLancamento));
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Double getTotalFinal() {
        return (totalFinal);
    }

    public void setTotalFinal(Double totalFinal) {
        this.totalFinal = totalFinal;
    }

    public Double getValorFaturado() { return this.valorFaturado; }

    public void setValorFaturado(Double valorFaturado) {
        this.valorFaturado =  valorFaturado;
    }

    public Double getValorDesconto() {
        return (valorDesconto);
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public Double getPrecoUnitario() {
        return (precoUnitario);
    }

    public void setPrecoUnitario(Double precoUnitario) {
        this.precoUnitario = precoUnitario;
    }

    public Integer getQuantidade() {
        return (quantidade);
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getApresentarMovProduto() {
        return apresentarMovProduto;
    }

    public void setApresentarMovProduto(Boolean apresentarMovProduto) {
        this.apresentarMovProduto = apresentarMovProduto;
    }

    public Boolean getQuitado() {
        return quitado;
    }

    public void setQuitado(Boolean quitado) {
        this.quitado = quitado;
    }

    public Double getValorPagoMovProdutoParcela() {
        return valorPagoMovProdutoParcela;
    }

    public Boolean getAprensetarBotaoEstorno() {
        // para permitir estorno o produto deve ser de um dos tipos indicados E nao fazer parte de um contrato
        return (getContrato() == null || getContrato().getCodigo() == null || getContrato().getCodigo() == 0)
                && (getProduto().getTipoProduto().equals("PE") || getProduto().getTipoProduto().equals("SE")
                || getProduto().getTipoProduto().equals("DI") || getProduto().getTipoProduto().equals("AA")
                || getProduto().getTipoProduto().equals("SS") || (getProduto().getTipoProduto().equals("CC") && getSituacao().equals("EA")) // produto de crédito só pode ser estornado se ainda estiver em aberto.
                || (getProduto().getTipoProduto().equals("AC") && getSituacao().equals("EA")) // Acerto de conta corrente também só pode ser estornado quando estiver em aberto
                || getProduto().getTipoProduto().equals("TP") || getProduto().getTipoProduto().equals("TD")
                //gestão de armário
                || getProduto().getTipoProduto().equals(TipoProduto.ARMARIO.getCodigo())
                || getProduto().getTipoProduto().equals(TipoProduto.DESAFIO.getCodigo())
                || getProduto().getTipoProduto().equals("CP")
                || getProduto().getTipoProduto().equals(TipoProduto.HOMEFIT.getCodigo())
                || getProduto().getTipoProduto().equals(TipoProduto.BIO_TOTEM.getCodigo())
                || getProduto().getTipoProduto().equals(TipoProduto.CONSULTA_NUTRICIONAL.getCodigo()));
    }

    public Boolean getAprensetarBotaoEstornoComParcela() {
        return getAprensetarBotaoEstorno() && !UteisValidacao.emptyList(getMovProdutoParcelaVOs());
    }

    public Boolean getAprensetarBotaoExcluirSemParcela() {
        return getAprensetarBotaoEstorno() && UteisValidacao.emptyList(getMovProdutoParcelaVOs());
    }

    public Boolean getApresentarBotaoImprimirContrato() {
    // para permitir estorno o produto deve ser de um dos tipos indicados E nao fazer parte de um contrato
      return (getContrato() == null || getContrato().getCodigo() == null || getContrato().getCodigo() == 0)
              && (getProduto().getTipoProduto().equals("SS") && !getSituacao().equals("CA"));
    }

    public Boolean getApresentarBotaoCancelarSessao() throws Exception {
        // para permitir cancelar a sessão é necessário que seja somente do tipo sessão
        return (getContrato() == null || getContrato().getCodigo() == null || getContrato().getCodigo() == 0)
                && (getProduto().getTipoProduto().equals("SS") && !getSituacao().equals("CA"))
                && getFacade().getControleAcesso().verificarPermissaoFuncionalidade("CancelarSessao");
    }

    public void setValorPagoMovProdutoParcela(Double valorPagoMovProdutoParcela) {
        this.valorPagoMovProdutoParcela = valorPagoMovProdutoParcela;
    }

    public String getSituacao_Apresentar() {
        if (situacao == null) {
            situacao = "";
        }
        if (situacao.equals("CA")) {
            return "Cancelado";
        }
        if (situacao.equals("PG")) {
            return "Pago";
        }
        if (situacao.equals("EA")) {
            setMudarCorSituacaoEmAberto("red");
            return "Em Aberto";
        }
        return situacao;
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getMudarCorSituacaoEmAberto() {
        return mudarCorSituacaoEmAberto;
    }

    public void setMudarCorSituacaoEmAberto(String mudarCorSituacaoEmAberto) {
        this.mudarCorSituacaoEmAberto = mudarCorSituacaoEmAberto;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof MovProdutoVO) {
            MovProdutoVO aux = (MovProdutoVO) obj;
            return this.getCodigo().intValue() == aux.getCodigo().intValue();
        }
        return false;
    }

    public void setValorParcialmentePago(Double valorParcialmentePago) {
        this.valorParcialmentePago = valorParcialmentePago;
    }

    public Double getValorParcialmentePago() {
        return valorParcialmentePago;
    }

    public String getValorParcialmentePagoApresentar() {
        if (this.valorParcialmentePago == null || this.valorParcialmentePago == 0.0) {
            return "";
        }
        return Formatador.formatarValorMonetario(valorParcialmentePago);
    }

    public Boolean getNaoGerarMensagem() {
        if (naoGerarMensagem == null) {
            naoGerarMensagem = false;
        }
        return naoGerarMensagem;
    }

    public void setNaoGerarMensagem(Boolean naoGerarMensagem) {
        this.naoGerarMensagem = naoGerarMensagem;
    }

    public Date getDataFinalVigenciaOriginal() {
        return dataFinalVigenciaOriginal;
    }

    public void setDataFinalVigenciaOriginal(Date dataFinalVigenciaOriginal) {
        this.dataFinalVigenciaOriginal = dataFinalVigenciaOriginal;
    }

    public void setMovProdutoModalidades(List<MovProdutoModalidadeVO> movProdutoModalidades) {
        this.movProdutoModalidades = movProdutoModalidades;
    }

    public List<MovProdutoModalidadeVO> getMovProdutoModalidades() {
        return movProdutoModalidades;
    }

    public StringBuilder getDescricaoMovProdutoModalidade() {
        return descricaoMovProdutoModalidade;
    }

    public String getMovpagamentocc() {
        return movpagamentocc;
    }

    public void setMovpagamentocc(String movpagamentocc) {
        this.movpagamentocc = movpagamentocc;
    }

    public void setDescricaoMovProdutoModalidade(StringBuilder descricaoMovProdutoModalidade) {
        this.descricaoMovProdutoModalidade = descricaoMovProdutoModalidade;
    }

    public PacoteVO getPacoteVO() {
        if (pacoteVO == null) {
            pacoteVO = new PacoteVO();
        }
        return pacoteVO;
    }

    public void setPacoteVO(PacoteVO pacoteVO) {
        this.pacoteVO = pacoteVO;
    }

    public String getMesReferenciaOrdenacao() {
        try {
            return anoReferencia + "/" + mesReferencia.substring(0, mesReferencia.indexOf("/"));
        } catch (Exception e) {
            return mesReferencia;
        }
    }

    public void setReciboDevolucao(ReciboDevolucaoVO reciboDevolucao) {
        this.reciboDevolucao = reciboDevolucao;
    }

    public ReciboDevolucaoVO getReciboDevolucao() {
        return reciboDevolucao;
    }

    public boolean getPossuiReciboDevolucao() {
        return getReciboDevolucao() != null && !UteisValidacao.emptyNumber(getReciboDevolucao().getCodigo());
    }

    public Integer getLancamentoColetivo() {
        return lancamentoColetivo;
    }

    public void setLancamentoColetivo(Integer lancamentoColetivo) {
        this.lancamentoColetivo = lancamentoColetivo;
    }

    public String getNomeAlunoPersonal() {
        return (Uteis.obterPrimeiroNomeConcatenadoSobreNome(nomeAlunoPersonal));
    }

    public void setNomeAlunoPersonal(String nomeAlunoPersonal) {
        this.nomeAlunoPersonal = nomeAlunoPersonal;
    }

    public Integer getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(Integer vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public MovProdutoWS toWS() {
        MovProdutoWS movProdutoWS = new MovProdutoWS();
        movProdutoWS.setCodigo(this.codigo);
        movProdutoWS.setContrato(this.getContrato().getCodigo());
        movProdutoWS.setDescricao(this.getDescricao());
        movProdutoWS.setDataLancamento(this.getDataLancamento_Apresentar());
        movProdutoWS.setQuantidade(this.getQuantidade());
        movProdutoWS.setValorUnitario(Uteis.arredondarForcando2CasasDecimais(this.getPrecoUnitario()));
        movProdutoWS.setValorDesconto(Uteis.arredondarForcando2CasasDecimais(this.getValorDesconto()));
        movProdutoWS.setValorTotal(Uteis.arredondarForcando2CasasDecimais(this.getTotalFinal()));
        movProdutoWS.setSituacao(this.getSituacao_Apresentar());
        return movProdutoWS;
    }

    public String getTipoProduto() {
        return getProduto().getTipoProduto();
    }

    public Double getJuros() {
        if (juros == null) {
            juros = 0.0;
        }
        return juros;
    }

    public void setJuros(Double juros) {
        this.juros = juros;
    }

    public Double getMulta() {
        if (multa == null) {
            multa = 0.0;
        }
        return multa;
    }

    public void setMulta(Double multa) {
        this.multa = multa;
    }

    public String getOrdenacaoBoleto() {
        return getTipoProduto() + getMulta() + getJuros();
    }

    public String getDataLancamentoComHora_Apresentar() {
        return (Uteis.getDataComHora(dataLancamento));
    }

    public String getDataPagamentoComHora_Apresentar() {
        if(dataPagamento != null) {
            return (Uteis.getDataComHora(dataPagamento));
        } else {
            return null;
        }
    }

    public String getNumeroCupomDesconto() {
        return UteisValidacao.emptyString(numeroCupomDesconto) ? numeroCupomDesconto : numeroCupomDesconto.toUpperCase();
    }

    public void setNumeroCupomDesconto(String numeroCupomDesconto) {
        this.numeroCupomDesconto = numeroCupomDesconto;
    }

    public Integer getClienteTitular() {
        return clienteTitular;
    }

    public void setClienteTitular(Integer clienteTitular) {
        this.clienteTitular = clienteTitular;
    }

    public boolean isComissionavel() {
        return !getProduto().getComissaoProdutos().isEmpty() || !getProduto().getCategoriaProduto().getComissaoCategoriaProdutos().isEmpty();
    }

    public Double getTotalFinalApresentarTelaNegociacao() {
        if (totalFinalApresentarTelaNegociacao == null){
            return this.totalFinal;
}
        return totalFinalApresentarTelaNegociacao;
    }

    public void setTotalFinalApresentarTelaNegociacao(Double totalFinalApresentarTelaNegociacao) {
        this.totalFinalApresentarTelaNegociacao = totalFinalApresentarTelaNegociacao;
    }

    public Integer getNumeroParcela() {
        return numeroParcela;
    }

    public void setNumeroParcela(Integer numeroParcela) {
        this.numeroParcela = numeroParcela;
    }

    public ChequeVO getChequeDevolucao() {
        return chequeDevolucao;
    }

    public void setChequeDevolucao(ChequeVO chequeDevolucao) {
        this.chequeDevolucao = chequeDevolucao;
    }

    public String getStatusProtheus() {
        return statusProtheus;
    }

    public void setStatusProtheus(String statusProtheus) {
        this.statusProtheus = statusProtheus;
    }

    public String getLinkNota() {
        return linkNota;
    }

    public void setLinkNota(String linkNota) {
        this.linkNota = linkNota;
    }

    public void setCodigoOperacaoFinanceira(String codigoOperacaoFinanceira) {
        this.codigoOperacaoFinanceira = codigoOperacaoFinanceira;
    }

    public String getCodigoOperacaoFinanceira() {
        return codigoOperacaoFinanceira;
    }

    public Double getJurosNaoRecebidos() {
        if(jurosNaoRecebidos == null){
            jurosNaoRecebidos = 0.0;
        }
        return jurosNaoRecebidos;
    }

    public void setJurosNaoRecebidos(Double jurosNaoRecebidos) {
        this.jurosNaoRecebidos = jurosNaoRecebidos;
    }

    public Double getMultaNaoRecebida() {
        if(multaNaoRecebida == null){
            multaNaoRecebida = 0.0;
        }
        return multaNaoRecebida;
    }

    public void setMultaNaoRecebida(Double multaNaoRecebida) {
        this.multaNaoRecebida = multaNaoRecebida;
    }

    public Double getValorMovProdutoParcela() {
        if(valorMovProdutoParcela == null){
            valorMovProdutoParcela = 0.0;
        }
        return valorMovProdutoParcela;
    }

    public void setValorMovProdutoParcela(Double valorMovProdutoParcela) {
        this.valorMovProdutoParcela = valorMovProdutoParcela;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public String getDataCancelamento_Hint() {
        if (dataCancelamento == null) {
            return getSituacao_Apresentar();
        }
        return "Parcela cancelada na data " + Calendario.getData(dataCancelamento, "dd/MM/yyyy");
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public String getDataPagamento_Hint() {
        if (dataPagamento == null) {
            return getSituacao_Apresentar();
        }
        return "Parcela paga na data " + Calendario.getData(dataPagamento, "dd/MM/yyyy");
    }

    public void setMovPagamento(MovPagamentoVO movPagamento) {
        this.movPagamento = movPagamento;
    }

    public MovPagamentoVO getMovPagamento() {
        return movPagamento;
    }

    public Integer getMovProdutoBase() {
        return movProdutoBase;
    }

    public void setMovProdutoBase(Integer movProdutoBase) {
        this.movProdutoBase = movProdutoBase;
    }

    public Boolean getRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(Boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public String getRenovavelAutomaticamente_Apresentar() {
        if(renovavelAutomaticamente){
            return "SIM";
        }
        return "NÃO";
    }

    public Boolean getVigenciaJaCalculada() {
        return vigenciaJaCalculada;
    }

    public void setVigenciaJaCalculada(Boolean vigenciaJaCalculada) {
        this.vigenciaJaCalculada = vigenciaJaCalculada;
    }


    public Boolean getApresentarAlterarRenovavelAutomaticamente() {
        return this.getProduto().getRenovavelAutomaticamente() && this.getDataFinalVigencia() != null && Calendario.menorOuIgual(Uteis.somarDias(Calendario.hoje(), -1), this.getDataFinalVigencia());
    }
}

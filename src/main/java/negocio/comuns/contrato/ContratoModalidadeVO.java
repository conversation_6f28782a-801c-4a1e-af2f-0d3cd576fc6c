package negocio.comuns.contrato;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ContratoModalidadeWS;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.NivelTurmaVO;
import negocio.comuns.plano.PlanoExcecaoVO;
import negocio.comuns.plano.PlanoModalidadeVO;
import negocio.comuns.plano.PlanoModalidadeVezesSemanaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.facade.jdbc.contrato.Contrato;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade ContratoModalidade. Classe do tipo
 * VO - Value Object composta pelos atributos da entidade com visibilidade
 * protegida e os métodos de acesso a estes atributos. Classe utilizada para
 * apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 * @see Contrato
 */
public class ContratoModalidadeVO extends SuperVO {

    protected Integer codigo;
    protected Integer contrato;
    protected Double valorFinalModalidade;
    protected Double valorModalidade;
    protected Boolean domingo;
    protected Boolean segunda;
    protected Boolean terca;
    protected Boolean quarta;
    protected Boolean quinta;
    protected Boolean sexta;
    protected Boolean sabado;
    protected Boolean h0001as0200;
    protected Boolean h0201as0400;
    protected Boolean h0401as0600;
    protected Boolean h0601as0800;
    protected Boolean h0801as1000;
    protected Boolean h1001as1200;
    protected Boolean h1201as1400;
    protected Boolean h1401as1600;
    protected Boolean h1601as1800;
    protected Boolean h1801as2000;
    protected Boolean h2001as2200;
    protected Boolean h2201as0000;
    protected String horarioTurmaEscolhido;
    //atributo para guarda os texto na hora de manutencao modaliade
    protected String textoInclusao;
    protected String textoAlteracao;
    protected String textoExclusao;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Modalidade </code>.
     */
    @ChaveEstrangeira
    protected ModalidadeVO modalidade = new ModalidadeVO();
    @Lista
    protected List contratoModalidadeTurmaVOs;
    protected List contratoModalidadeProdutoSugeridoVOs;
    protected PlanoModalidadeVezesSemanaVO planoVezesSemanaVO;
    protected ContratoModalidadeVezesSemanaVO contratoModalidadeVezesSemanaVO;
    protected Integer nrVezesSemana;
    protected List listaHorario;
    private Double valorIncideComissao = 0.0;
    @NaoControlarLogAlteracao
    private Double valorDepoisManutencao = 0.0;
    private NivelTurmaVO nivelTurma = new NivelTurmaVO();
    @NaoControlarLogAlteracao
    private PlanoExcecaoVO excecao;
    private boolean calculoManutencao;
    @NaoControlarLogAlteracao
    private PlanoModalidadeVO planoModalidade;
    private boolean advertenciaAcimaLimite = false;      //caso a turma esteja acima do limite, mas isso seja permitido
    private boolean obstrucaoConclusaoManutencao = false; //caso a turma não permita matricula acima do limite, e ela esteja acima(retorno trancamento)
    private boolean advertenciaMatriculasFuturas = false;
    private boolean calculaDescontoOcupacao = true; //marcado para ser o default conceder o desconto
    @NaoControlarLogAlteracao
    private ContratoModalidadeCreditoVO contratoModalidadeCredito;
    @NaoControlarLogAlteracao
    private Integer qtdModalidadeEscolhidas; //quantidade modalidade escolhidas dentro de um pacote livre

    /**
     * Construtor padrão da classe
     * <code>ContratoModalidade</code>. Cria uma nova instância desta entidade,
     * inicializando automaticamente seus atributos (Classe VO).
     */
    public ContratoModalidadeVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ContratoModalidadeVO</code>. Todos os tipos de consistência de
     * dados são e devem ser implementadas neste método. São validações típicas:
     * verificação de campos obrigatórios, verificação de valores válidos para
     * os atributos.
     *
     * @exception ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDados(ContratoModalidadeVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getModalidade() == null)
                || (obj.getModalidade().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo MODALIDADE (Contrato Modalidade) deve ser informado.");
        }

    }

    public void realizarUpperCaseDados() {
    }

    public void inicializarDados() {
        setCodigo(new Integer(0));
        setValorFinalModalidade(new Double(0));
        setValorModalidade(new Double(0));
        setModalidade(new ModalidadeVO());
        setDomingo(false);
        setSegunda(false);
        setTerca(false);
        setQuarta(false);
        setQuinta(false);
        setSexta(false);
        setSabado(false);
        setH0001as0200(false);
        setH0201as0400(false);
        setH0401as0600(false);
        setH0601as0800(false);
        setH0801as1000(false);
        setH1001as1200(false);
        setH1201as1400(false);
        setH1401as1600(false);
        setH1601as1800(false);
        setH1801as2000(false);
        setH2001as2200(false);
        setH2201as0000(false);
        setHorarioTurmaEscolhido("");
        setTextoAlteracao("");
        setTextoExclusao("");
        setTextoInclusao("");
        setContratoModalidadeTurmaVOs(new ArrayList());
        setContratoModalidadeProdutoSugeridoVOs(new ArrayList());
        setNrVezesSemana(new Integer(0));
        setPlanoVezesSemanaVO(new PlanoModalidadeVezesSemanaVO());
        setContratoModalidadeVezesSemanaVO(new ContratoModalidadeVezesSemanaVO());
        setListaHorario(new ArrayList());
        setCalculoManutencao(false);
        setQtdModalidadeEscolhidas(0);
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>MovProdutoVO</code> ao List
     * <code>movProdutoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>MovProduto</code> - getProduto().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>MovProdutoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjContratoModalidadeTurmaVOs(ContratoModalidadeTurmaVO obj) throws Exception {
        ContratoModalidadeTurmaVO.validarDadosAdicionar(obj);
        int index = 0;
        Iterator i = getContratoModalidadeTurmaVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeTurmaVO objExistente = (ContratoModalidadeTurmaVO) i.next();
            if (objExistente.getTurma().getCodigo().equals(obj.getTurma().getCodigo())) {
                getContratoModalidadeTurmaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getContratoModalidadeTurmaVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>MovProdutoVO</code> no List
     * <code>movProdutoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>MovProduto</code> - getProduto().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param turma Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjContratoModalidadeTurmaVOs(Integer turma) throws Exception {
        int index = 0;
        Iterator i = getContratoModalidadeTurmaVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeTurmaVO objExistente = (ContratoModalidadeTurmaVO) i.next();
            if (objExistente.getTurma().getCodigo().equals(turma)) {
                getContratoModalidadeTurmaVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>MovProdutoVO</code> no List
     * <code>movProdutoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>MovProduto</code> - getProduto().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param turma Parâmetro para localizar o objeto do List.
     */
    public ContratoModalidadeTurmaVO consultarObjContratoModalidadeTurmaVO(Integer turma) throws Exception {
        Iterator i = getContratoModalidadeTurmaVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeTurmaVO objExistente = (ContratoModalidadeTurmaVO) i.next();
            if (objExistente.getTurma().getCodigo().equals(turma)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>MovProdutoVO</code> ao List
     * <code>movProdutoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>MovProduto</code> - getProduto().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>MovProdutoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjManuntencaoModalidade(ContratoModalidadeTurmaVO obj) throws Exception {
        ContratoModalidadeTurmaVO.validarDadosAdicionar(obj);
        int index = 0;
        Iterator i = getContratoModalidadeTurmaVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeTurmaVO objExistente = (ContratoModalidadeTurmaVO) i.next();
            if (objExistente.getTurma().getCodigo().equals(obj.getTurma().getCodigo())) {
                return;
            }
            index++;
        }
        getContratoModalidadeTurmaVOs().add(obj);
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>MovProdutoVO</code> ao List
     * <code>movProdutoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>MovProduto</code> - getProduto().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>MovProdutoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjContratoModalidadeProdutoSugeridoVOs(ContratoModalidadeProdutoSugeridoVO obj) throws Exception {
        ContratoModalidadeProdutoSugeridoVO.validarDados(obj);
        int index = 0;
        Iterator i = getContratoModalidadeProdutoSugeridoVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeProdutoSugeridoVO objExistente = (ContratoModalidadeProdutoSugeridoVO) i.next();
            if (objExistente.getProdutoSugerido().getProduto().getCodigo().equals(obj.getProdutoSugerido().getProduto().getCodigo())) {
                getContratoModalidadeProdutoSugeridoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getContratoModalidadeProdutoSugeridoVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>MovProdutoVO</code> no List
     * <code>movProdutoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>MovProduto</code> - getProduto().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param produto Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjContratoModalidadeProdutoSugeridoVOs(Integer produto) throws Exception {
        int index = 0;
        Iterator i = getContratoModalidadeProdutoSugeridoVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeProdutoSugeridoVO objExistente = (ContratoModalidadeProdutoSugeridoVO) i.next();
            if (objExistente.getProdutoSugerido().getProduto().getCodigo().equals(produto)) {
                getContratoModalidadeProdutoSugeridoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>MovProdutoVO</code> no List
     * <code>movProdutoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>MovProduto</code> - getProduto().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param produto Parâmetro para localizar o objeto do List.
     */
    public ContratoModalidadeProdutoSugeridoVO consultarObjContratoModalidadeProdutoSugeridoVO(Integer produto) throws Exception {
        Iterator i = getContratoModalidadeProdutoSugeridoVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeProdutoSugeridoVO objExistente = (ContratoModalidadeProdutoSugeridoVO) i.next();
            if (objExistente.getProdutoSugerido().getProduto().getCodigo().equals(produto)) {
                return objExistente;
            }
        }
        return null;
    }

    public void adicionarObjHorario(String horario) {
        getListaHorario().add(horario);
    }

    public void removerObjHorario(String horario) {
        int index = 0;
        Iterator i = getListaHorario().iterator();
        while (i.hasNext()) {
            String objExistent = (String) i.next();
            if (objExistent.equals(horario)) {
                getListaHorario().remove(index);
                return;
            }
            index++;
        }
    }

    public void gerarContratoModalidade() {
        getContratoModalidadeVezesSemanaVO().setNrVezes(getPlanoVezesSemanaVO().getNrVezes());
        getContratoModalidadeVezesSemanaVO().setPercentualDesconto(getPlanoVezesSemanaVO().getPercentualDesconto());
        getContratoModalidadeVezesSemanaVO().setTipoOperacao(getPlanoVezesSemanaVO().getTipoOperacao());
        getContratoModalidadeVezesSemanaVO().setTipoValor(getPlanoVezesSemanaVO().getTipoValor());
        getContratoModalidadeVezesSemanaVO().setValorEspecifico(getPlanoVezesSemanaVO().getValorEspecifico());
        getContratoModalidadeVezesSemanaVO().setVezeSemanaEscolhida(true);
    }

    public void gerarPlanoModalidadeApartirContratoModalidadeVezesSemana() {
        getPlanoVezesSemanaVO().setNrVezes(getContratoModalidadeVezesSemanaVO().getNrVezes());
        getPlanoVezesSemanaVO().setPercentualDesconto(getContratoModalidadeVezesSemanaVO().getPercentualDesconto());
        getPlanoVezesSemanaVO().setTipoOperacao(getContratoModalidadeVezesSemanaVO().getTipoOperacao());
        getPlanoVezesSemanaVO().setTipoValor(getContratoModalidadeVezesSemanaVO().getTipoValor());
        getPlanoVezesSemanaVO().setValorEspecifico(getContratoModalidadeVezesSemanaVO().getValorEspecifico());
        getPlanoVezesSemanaVO().setVezeSemanaEscolhida(true);
    }

    public  List<Integer> gerarTextoInclusao(ContratoModalidadeVO obj) {
        List<Integer> codigosHorariosNovos = new ArrayList<Integer>();
        if (getTextoInclusao().equals("")) {
            setTextoInclusao(getTextoInclusao() + "Inclusão da Modalidade: " + obj.getModalidade().getNome() + ".\r\n");
        } else {
            setTextoInclusao(getTextoInclusao() + "\r\n");
        }
        if (obj.getModalidade().getUtilizarTurma()) {
            Iterator k = obj.getContratoModalidadeTurmaVOs().iterator();
            while (k.hasNext()) {
                ContratoModalidadeTurmaVO cmtNovo = (ContratoModalidadeTurmaVO) k.next();
                setTextoInclusao(getTextoInclusao() + gerarTextoInclusaoTurma(obj, cmtNovo, codigosHorariosNovos));
            }
        }
        return codigosHorariosNovos;
    }

    public void gerarTextoAlteracao(ContratoModalidadeVO cmNovo,
            ContratoModalidadeVO cmAntigo,
            ContratoModalidadeTurmaVO cmtAntigo,
            ContratoModalidadeHorarioTurmaVO cmhtAntigo) {

        gerarTextoAlteracao(cmNovo, cmAntigo, cmtAntigo);

        if (cmhtAntigo != null) {
            setTextoAlteracao(getTextoAlteracao() + "Horário Alterado : " + cmhtAntigo.getHorarioTurma().getDiaSemana_Apresentar() + " as " + cmhtAntigo.getHorarioTurma().getHoraInicial() + " até " + cmhtAntigo.getHorarioTurma().getHoraFinal() + ".\r\n");
            setTextoAlteracao(getTextoAlteracao() + "*Professor: " + cmhtAntigo.getHorarioTurma().getProfessor().getPessoa_Apresentar() + ".\r\n");
        }
    }

    public void gerarTextoInclusaoDeHorario(ContratoModalidadeVO cmNovo,
            ContratoModalidadeVO cmAntigo,
            ContratoModalidadeTurmaVO cmtAntigo,
            ContratoModalidadeHorarioTurmaVO cmhtAntigo,
            ContratoModalidadeHorarioTurmaVO cmhtNovo) {

        gerarTextoAlteracao(cmNovo, cmAntigo, cmtAntigo);

        if (cmhtAntigo != null) {
            setTextoAlteracao(getTextoAlteracao() + "Horário incluído: " + cmhtNovo.getHorarioTurma().getDiaSemana_Apresentar() + " as " + cmhtNovo.getHorarioTurma().getHoraInicial() + " até " + cmhtNovo.getHorarioTurma().getHoraFinal() + ".\r\n");
            setTextoAlteracao(getTextoAlteracao() + "*Professor: " + cmhtNovo.getHorarioTurma().getProfessor().getPessoa_Apresentar() + ".\r\n");
        }
    }

    public void gerarTextoExclusaoDeHorario(ContratoModalidadeVO cmNovo,
            ContratoModalidadeVO cmAntigo,
            ContratoModalidadeTurmaVO cmtAntigo,
            ContratoModalidadeHorarioTurmaVO cmhtAntigo) {

        gerarTextoAlteracao(cmNovo, cmAntigo, cmtAntigo);

        if (cmhtAntigo != null) {
            setTextoAlteracao(getTextoAlteracao() + "Horário retirado: " + cmhtAntigo.getHorarioTurma().getDiaSemana_Apresentar() + " as " + cmhtAntigo.getHorarioTurma().getHoraInicial() + " até " + cmhtAntigo.getHorarioTurma().getHoraFinal() + ".\r\n");
            setTextoAlteracao(getTextoAlteracao() + "*Professor: " + cmhtAntigo.getHorarioTurma().getProfessor().getPessoa_Apresentar() + ".\r\n");
        }
    }

    private void gerarTextoAlteracao(ContratoModalidadeVO cmNovo, ContratoModalidadeVO cmAntigo, ContratoModalidadeTurmaVO cmtAntigo) {
        if (getTextoAlteracao().equals("")) {
            setTextoAlteracao(getTextoAlteracao() + "Alteração da Modalidade: " + cmNovo.getModalidade().getNome() + ".\r\n");

        } else {
            setTextoAlteracao(getTextoAlteracao() + "\r\n");
        }
        if (cmAntigo != null) {
            setTextoAlteracao(getTextoAlteracao() + "Vezes Semana Anterior a Manutenção: " + cmAntigo.getNrVezesSemana().toString() + ".\r\n");
            setTextoAlteracao(getTextoAlteracao() + "Vezes Semana Após a Manutenção: " + cmNovo.getNrVezesSemana().toString() + ".\r\n");
        }
        if (cmtAntigo != null) {
            setTextoAlteracao(getTextoAlteracao() + "Turma Alterada: " + cmtAntigo.getTurma().getIdentificador() + ".\r\n");
            if (cmAntigo != null && cmAntigo.getModalidade() != null && cmAntigo.getModalidade().getUtilizarTurma()) {
                Iterator k = cmAntigo.getContratoModalidadeTurmaVOs().iterator();
                while (k.hasNext()) {
                    ContratoModalidadeTurmaVO cmtNovo = (ContratoModalidadeTurmaVO) k.next();
                    Iterator e = cmtNovo.getContratoModalidadeHorarioTurmaVOs().iterator();
                    while (e.hasNext()) {
                        ContratoModalidadeHorarioTurmaVO cmhtNovo = (ContratoModalidadeHorarioTurmaVO) e.next();
                        setTextoAlteracao(getTextoAlteracao() + "Exclusão do Horário: " + cmhtNovo.getHorarioTurma().getDiaSemana_Apresentar() + " as " + cmhtNovo.getHorarioTurma().getHoraInicial() + " até " + cmhtNovo.getHorarioTurma().getHoraFinal() + ".\r\n");
                        setTextoAlteracao(getTextoAlteracao() + "*Professor: " + cmhtNovo.getHorarioTurma().getProfessor().getPessoa_Apresentar() + ".\r\n");
                    }
                }
            }
        }
    }

    public void gerarTextoExclusao(ContratoModalidadeVO obj) {
        if (getTextoExclusao().equals("")) {
            setTextoExclusao(getTextoExclusao() + "Exclusão da Modalidade: " + obj.getModalidade().getNome() + ".\r\n");
        } else {
            setTextoExclusao(getTextoExclusao() + "\r\n");
        }
        if (obj.getModalidade().getUtilizarTurma()) {
            Iterator k = obj.getContratoModalidadeTurmaVOs().iterator();
            while (k.hasNext()) {
                ContratoModalidadeTurmaVO cmtNovo = (ContratoModalidadeTurmaVO) k.next();
                if (cmtNovo.getTurma().getTurmaEscolhida()) {
                    setTextoExclusao(getTextoExclusao() + "Exclusão da Turma: " + cmtNovo.getTurma().getIdentificador() + ".\r\n");
                    Iterator e = cmtNovo.getContratoModalidadeHorarioTurmaVOs().iterator();
                    while (e.hasNext()) {
                        ContratoModalidadeHorarioTurmaVO cmhtNovo = (ContratoModalidadeHorarioTurmaVO) e.next();
                        if (cmhtNovo.getHorarioTurma().getHorarioTurmaEscolhida()) {
                            setTextoExclusao(getTextoExclusao() + "Exclusão do Horário: " + cmhtNovo.getHorarioTurma().getDiaSemana_Apresentar() + " as " + cmhtNovo.getHorarioTurma().getHoraInicial() + " até " + cmhtNovo.getHorarioTurma().getHoraFinal() + ".\r\n");
                            setTextoExclusao(getTextoExclusao() + "*Professor: " + cmhtNovo.getHorarioTurma().getProfessor().getPessoa_Apresentar() + ".\r\n");
                        }
                    }
                }
            }
        }
    }

    public PlanoModalidadeVezesSemanaVO getPlanoVezesSemanaVO() {
        return planoVezesSemanaVO;
    }

    public void setPlanoVezesSemanaVO(PlanoModalidadeVezesSemanaVO planoVezesSemanaVO) {
        this.planoVezesSemanaVO = planoVezesSemanaVO;
    }

    public ModalidadeVO getModalidade() {
        if (modalidade == null) {
            modalidade = new ModalidadeVO();
        }
        return (modalidade);
    }

    public void setModalidade(ModalidadeVO obj) {
        this.modalidade = obj;
    }

    public Double getValorFinalModalidade() {
        return (valorFinalModalidade);
    }

    public void setValorFinalModalidade(Double valorFinalModalidade) {
        this.valorFinalModalidade = valorFinalModalidade;
    }

    public Double getValorModalidade() {
        return valorModalidade;
    }

    public void setValorModalidade(Double valorModalidade) {
        this.valorModalidade = valorModalidade;
    }

    public Integer getContrato() {
        return (contrato);
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List getContratoModalidadeTurmaVOs() {
        return contratoModalidadeTurmaVOs;
    }

    public void setContratoModalidadeTurmaVOs(List contratoModalidadeTurmaVOs) {
        this.contratoModalidadeTurmaVOs = contratoModalidadeTurmaVOs;
    }

    public Integer getNrVezesSemana() {
        return nrVezesSemana;
    }

    public void setNrVezesSemana(Integer nrVezesSemana) {
        this.nrVezesSemana = nrVezesSemana;
    }

    public List getContratoModalidadeProdutoSugeridoVOs() {
        return contratoModalidadeProdutoSugeridoVOs;
    }

    public void setContratoModalidadeProdutoSugeridoVOs(List contratoModalidadeProdutoSugeridoVOs) {
        this.contratoModalidadeProdutoSugeridoVOs = contratoModalidadeProdutoSugeridoVOs;
    }

    public Boolean getDomingo() {
        return domingo;
    }

    public void setDomingo(Boolean domingo) {
        this.domingo = domingo;
    }

    public Boolean getQuarta() {
        return quarta;
    }

    public void setQuarta(Boolean quarta) {
        this.quarta = quarta;
    }

    public Boolean getQuinta() {
        return quinta;
    }

    public void setQuinta(Boolean quinta) {
        this.quinta = quinta;
    }

    public Boolean getSabado() {
        return sabado;
    }

    public void setSabado(Boolean sabado) {
        this.sabado = sabado;
    }

    public Boolean getSegunda() {
        return segunda;
    }

    public void setSegunda(Boolean segunda) {
        this.segunda = segunda;
    }

    public Boolean getSexta() {
        return sexta;
    }

    public void setSexta(Boolean sexta) {
        this.sexta = sexta;
    }

    public Boolean getTerca() {
        return terca;
    }

    public void setTerca(Boolean terca) {
        this.terca = terca;
    }

    public Boolean getH0001as0200() {
        return h0001as0200;
    }

    public void setH0001as0200(Boolean h0001as0200) {
        this.h0001as0200 = h0001as0200;
    }

    public Boolean getH0201as0400() {
        return h0201as0400;
    }

    public void setH0201as0400(Boolean h0201as0400) {
        this.h0201as0400 = h0201as0400;
    }

    public Boolean getH0401as0600() {
        return h0401as0600;
    }

    public void setH0401as0600(Boolean h0401as0600) {
        this.h0401as0600 = h0401as0600;
    }

    public Boolean getH0601as0800() {
        return h0601as0800;
    }

    public void setH0601as0800(Boolean h0601as0800) {
        this.h0601as0800 = h0601as0800;
    }

    public Boolean getH0801as1000() {
        return h0801as1000;
    }

    public void setH0801as1000(Boolean h0801as1000) {
        this.h0801as1000 = h0801as1000;
    }

    public Boolean getH1001as1200() {
        return h1001as1200;
    }

    public void setH1001as1200(Boolean h1001as1200) {
        this.h1001as1200 = h1001as1200;
    }

    public Boolean getH1201as1400() {
        return h1201as1400;
    }

    public void setH1201as1400(Boolean h1201as1400) {
        this.h1201as1400 = h1201as1400;
    }

    public Boolean getH1401as1600() {
        return h1401as1600;
    }

    public void setH1401as1600(Boolean h1401as1600) {
        this.h1401as1600 = h1401as1600;
    }

    public Boolean getH1601as1800() {
        return h1601as1800;
    }

    public void setH1601as1800(Boolean h1601as1800) {
        this.h1601as1800 = h1601as1800;
    }

    public Boolean getH1801as2000() {
        return h1801as2000;
    }

    public void setH1801as2000(Boolean h1801as2000) {
        this.h1801as2000 = h1801as2000;
    }

    public Boolean getH2001as2200() {
        return h2001as2200;
    }

    public void setH2001as2200(Boolean h2001as2200) {
        this.h2001as2200 = h2001as2200;
    }

    public Boolean getH2201as0000() {
        return h2201as0000;
    }

    public void setH2201as0000(Boolean h2201as0000) {
        this.h2201as0000 = h2201as0000;
    }

    public String getHorarioTurmaEscolhido() {
        if (horarioTurmaEscolhido == null) {
            horarioTurmaEscolhido = "";
        }
        return horarioTurmaEscolhido;
    }

    public void setHorarioTurmaEscolhido(String horarioTurmaEscolhido) {
        this.horarioTurmaEscolhido = horarioTurmaEscolhido;
    }

    public Boolean isModalidadeContratoAtiva() {
        if (this.getModalidade().getAtivo()) {
            return true;
        }
        return false;
    }

    public List getListaHorario() {
        return listaHorario;
    }

    public void setListaHorario(List listaHorario) {
        this.listaHorario = listaHorario;
    }

    public ContratoModalidadeVezesSemanaVO getContratoModalidadeVezesSemanaVO() {
        return contratoModalidadeVezesSemanaVO;
    }

    public void setContratoModalidadeVezesSemanaVO(ContratoModalidadeVezesSemanaVO contratoModalidadeVezesSemanaVO) {
        this.contratoModalidadeVezesSemanaVO = contratoModalidadeVezesSemanaVO;
    }

    public String getTextoAlteracao() {
        if (textoAlteracao == null) {
            textoAlteracao = "";
        }
        return textoAlteracao;
    }

    public void setTextoAlteracao(String textoAlteracao) {
        this.textoAlteracao = textoAlteracao;
    }

    public String getTextoExclusao() {
        if (textoExclusao == null) {
            textoExclusao = "";
        }
        return textoExclusao;
    }

    public void setTextoExclusao(String textoExclusao) {
        this.textoExclusao = textoExclusao;
    }

    public String getTextoInclusao() {
        if (textoInclusao == null) {
            textoInclusao = "";
        }
        return textoInclusao;
    }

    public void setTextoInclusao(String textoInclusao) {
        this.textoInclusao = textoInclusao;
    }

    public Double getValorIncideComissao() {
        return valorIncideComissao;
    }

    public void setValorIncideComissao(Double valorIncideComissao) {
        this.valorIncideComissao = valorIncideComissao;
    }

    public void setValorDepoisManutencao(Double valorAntesManutencao) {
        this.valorDepoisManutencao = valorAntesManutencao;
    }

    public Double getValorDepoisManutencao() {
        return valorDepoisManutencao;
    }

    public NivelTurmaVO getNivelTurma() {
        return nivelTurma;
    }

    public void setNivelTurma(NivelTurmaVO nivelTurma) {
        this.nivelTurma = nivelTurma;
    }

    public String getNomeModalidade_Apresentar() {
        return this.modalidade.getNome();
    }

    public PlanoExcecaoVO getExcecao() {
        return excecao;
    }

    public void setExcecao(PlanoExcecaoVO excecao) {
        this.excecao = excecao;
    }

    public boolean isCalculoManutencao() {
        return calculoManutencao;
    }

    public void setCalculoManutencao(boolean calculoManutencao) {
        this.calculoManutencao = calculoManutencao;
    }

    public PlanoModalidadeVO getPlanoModalidade() {
        return planoModalidade;
    }

    public void setPlanoModalidade(PlanoModalidadeVO planoModalidade) {
        this.planoModalidade = planoModalidade;
    }

    public ContratoModalidadeWS toWS() {
        ContratoModalidadeWS contratoModalidadeWS = new ContratoModalidadeWS();
        contratoModalidadeWS.setCodigo(this.getCodigo());
        contratoModalidadeWS.setNrVezesSemana(this.getNrVezesSemana());
        contratoModalidadeWS.setModalidade(this.getNomeModalidade_Apresentar());
        return contratoModalidadeWS;
    }

    public boolean isAdvertenciaAcimaLimite() {
        return advertenciaAcimaLimite;
    }

    public void setAdvertenciaAcimaLimite(boolean advertenciaAcimaLimite) {
        this.advertenciaAcimaLimite = advertenciaAcimaLimite;
    }

    public boolean isObstrucaoConclusaoManutencao() {
        return obstrucaoConclusaoManutencao;
    }

    public void setObstrucaoConclusaoManutencao(boolean obstrucaoConclusaoManutencao) {
        this.obstrucaoConclusaoManutencao = obstrucaoConclusaoManutencao;
    }
    
    public List<ContratoModalidadeHorarioTurmaVO> getListaContratoModalidadesHorarioTurmaVOs(){
        List<ContratoModalidadeHorarioTurmaVO> retorno = new ArrayList<ContratoModalidadeHorarioTurmaVO>();
        for (Iterator it = getContratoModalidadeTurmaVOs().iterator(); it.hasNext();) {
            ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO) it.next();
            retorno.addAll(cmt.getContratoModalidadeHorarioTurmaVOs());
        }
        return retorno;
    }

    public boolean isAdvertenciaMatriculasFuturas() {
        return advertenciaMatriculasFuturas;
    }

    public void setAdvertenciaMatriculasFuturas(boolean advertenciaMatriculasFuturas) {
        this.advertenciaMatriculasFuturas = advertenciaMatriculasFuturas;
    }

    public boolean isCalculaDescontoOcupacao() {
        return calculaDescontoOcupacao;
    }

    public void setCalculaDescontoOcupacao(boolean calculaDescontoOcupacao) {
        this.calculaDescontoOcupacao = calculaDescontoOcupacao;
    }

    public ContratoModalidadeCreditoVO getContratoModalidadeCredito() {
        if (contratoModalidadeCredito == null) {
            contratoModalidadeCredito = new ContratoModalidadeCreditoVO();
        }
        return contratoModalidadeCredito;
    }

    public void setContratoModalidadeCredito(ContratoModalidadeCreditoVO contratoModalidadeCredito) {
        this.contratoModalidadeCredito = contratoModalidadeCredito;
    }

    public  String gerarTextoInclusaoTurma(ContratoModalidadeVO cmNovo, ContratoModalidadeTurmaVO cmtNovo, List<Integer> codigosHorariosNovos) {
        String textoAlteracoes = "";
        if (cmNovo.getModalidade().getUtilizarTurma() && cmtNovo.getTurma().getTurmaEscolhida()) {
            textoAlteracoes += "Inclusão da Turma: " + cmtNovo.getTurma().getIdentificador() + ".\r\n";
            Iterator e = cmtNovo.getContratoModalidadeHorarioTurmaVOs().iterator();
            while (e.hasNext()) {
                ContratoModalidadeHorarioTurmaVO cmhtNovo = (ContratoModalidadeHorarioTurmaVO) e.next();
                if (cmhtNovo.getHorarioTurma().getHorarioTurmaEscolhida()) {
                    codigosHorariosNovos.add(cmhtNovo.getHorarioTurma().getCodigo());
                    textoAlteracoes += "Inclusão do Horário: " + cmhtNovo.getHorarioTurma().getDiaSemana_Apresentar() + " as " + cmhtNovo.getHorarioTurma().getHoraInicial() + " até " + cmhtNovo.getHorarioTurma().getHoraFinal()+ ".\r\n";
                    textoAlteracoes += "*Professor: "+cmhtNovo.getHorarioTurma().getProfessor().getPessoa_Apresentar()+ ".\r\n";
                }
            }
        }
        return textoAlteracoes;
    }

    public Integer getQtdModalidadeEscolhidas() {
        return qtdModalidadeEscolhidas;
    }

    public void setQtdModalidadeEscolhidas(Integer qtdModalidadeEscolhidas) {
        this.qtdModalidadeEscolhidas = qtdModalidadeEscolhidas;
    }
}

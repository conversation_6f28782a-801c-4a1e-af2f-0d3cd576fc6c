package negocio.comuns.contrato;

import java.util.Date;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade PeriodoAcessoCliente. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class PeriodoAcessoClienteVO extends SuperVO implements Comparable {

    protected Integer codigo;
    protected Integer pessoa;
    protected Integer contrato;
    protected Integer aulaAvulsaDiaria;
    protected Integer contratoBaseadoRenovacao;
    protected Date dataInicioAcesso;
    protected Date dataFinalAcesso;
    protected String tipoAcesso;
    protected Boolean legenda;
    private Integer responsavel;
    private Date dataLancamento;
    private String tokenGymPass;
    private String tipoGymPass;
    private Integer reposicao;
    private Double valorGympass;
    private Boolean tipototalpass;
    @NaoControlarLogAlteracao
    private Integer codigoEmpresa;
    private String produtoGymPass;
    private String tokenGoGood;
    private Integer produto;

    public PeriodoAcessoClienteVO() {
        super();
        inicializarDados();
    }

    public static void validarDados(PeriodoAcessoClienteVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
    }

    public void realizarUpperCaseDados() {
        setTipoAcesso(tipoAcesso.toUpperCase());
    }

    public void inicializarDados() {
        setCodigo(0);
        setPessoa(0);
        setContrato(0);
        setAulaAvulsaDiaria(0);
        setContratoBaseadoRenovacao(0);
        setDataInicioAcesso(negocio.comuns.utilitarias.Calendario.hoje());
        setDataFinalAcesso(negocio.comuns.utilitarias.Calendario.hoje());
        setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
        setTipoAcesso("");
    }

    public String getTipoAcesso() {
        return (tipoAcesso);
    }

    public String getTipoAcesso_Apresentar() {
        if (tipoAcesso == null) {
            return "";
        }
        if (tipoAcesso.equals("CA")) {
            return "Contrato Ativo";
        }
        if (tipoAcesso.equals("BO")) {
            return "Bônus";
        }
        if (tipoAcesso.equals("TO")) {
            return "Tolerância";
        }
        if (tipoAcesso.equals("TD")) {
            return "Transfêrencia de dias";
        }
        if (tipoAcesso.equals("PL")) {
            try {
                if (!UteisValidacao.emptyString(getTokenGymPass())) {
                    return "GymPass - Token: " + getTokenGymPass();
                }
                if (getTipototalpass()) {
                    return "Passe Livre - TotalPass";
                } else {
                    return "Passe Livre - Free Pass";
                }
            } catch (Exception e) {
                return "Passe Livre - Free Pass";
            }
        }
        if (tipoAcesso.equals("AA")) {
            return "Aula Avulsa";
        }
        if (tipoAcesso.equals("DI")) {
            return "Diária";
        }
        if (tipoAcesso.equals("RT")) {
            return "Retorno Trancamento";
        }
        if (tipoAcesso.equals("RR")) {
            return "Retorno Retroativo";
        }
        if (tipoAcesso.equals("RA")) {
            return "Retorno - Atestado";
        }
        if (tipoAcesso.equals("TR")) {
            return "Trancado";
        }
        if (tipoAcesso.equals("AT")) {
            return "Atestado";
        }
        if (tipoAcesso.equals("PF")) {
            return "Pendente Financeiro";
        }
        if (tipoAcesso.equals("CN")) {
            return "Cancelado";
        }
        if (tipoAcesso.equals("CR")) {
            return "Férias";
        }
        if (tipoAcesso.equals("RC")) {
            return "Retorno-Férias";
        }
        return tipoAcesso;
    }

    public String getLegenda_Apresentar() {
        if (getTiposQuePermiteAcesso().indexOf(getTipoAcesso()) >= 0)
            return "./imagens/acessoPermitido.png";
         else 
           return "./imagens/acessoBloqueado.png";
    }

    public boolean getPermiteAcesso() {
        return  (getTiposQuePermiteAcesso().indexOf(getTipoAcesso()) >= 0);
    }

    public static String getTiposQuePermiteAcesso(){
        StringBuilder sb = new StringBuilder();
        sb.append("'CA',");
        sb.append("'BO',");
        sb.append("'TO',");
        sb.append("'TD',");
        sb.append("'PL',");
        sb.append("'AA',");
        sb.append("'DI',");
        sb.append("'RT',");
        sb.append("'RR',");
        sb.append("'RA',");
        sb.append("'RC'");

        return sb.toString();
    }

    public void setTipoAcesso(String tipoAcesso) {
        this.tipoAcesso = tipoAcesso;
    }

    public Date getDataFinalAcesso() {
        return (dataFinalAcesso);
    }

    public String getDataFinalAcesso_Apresentar() {
        return (Uteis.getData(dataFinalAcesso));
    }

    public void setDataFinalAcesso(Date dataFinalAcesso) {
        this.dataFinalAcesso = dataFinalAcesso;
    }

    public Date getDataInicioAcesso() {
        return (dataInicioAcesso);
    }

    public String getDataInicioAcesso_Apresentar() {
        return (Uteis.getData(dataInicioAcesso));
    }

    public void setDataInicioAcesso(Date dataInicioAcesso) {
        this.dataInicioAcesso = dataInicioAcesso;
    }

    public Integer getContrato() {
        return (contrato);
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getLegenda() {
        return legenda;
    }

    public void setLegenda(Boolean legenda) {
        this.legenda = legenda;
    }

    public Integer getContratoBaseadoRenovacao() {
        return contratoBaseadoRenovacao;
    }

	public Integer getAulaAvulsaDiaria() {
		return aulaAvulsaDiaria;
	}

	public void setAulaAvulsaDiaria(Integer aulaAvulsaDiaria) {
		this.aulaAvulsaDiaria = aulaAvulsaDiaria;
	}

	public void setContratoBaseadoRenovacao(Integer contratoBaseadoRenovacao) {
        this.contratoBaseadoRenovacao = contratoBaseadoRenovacao;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getDataLancamento_Apresentar(){
        return Uteis.getData(getDataLancamento());
    }

    public Integer getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Integer responsavel) {
        this.responsavel = responsavel;
    }

    public String getTokenGymPass() {
        if (tokenGymPass == null) {
            tokenGymPass = "";
        }
        return tokenGymPass;
    }

    public void setTokenGymPass(String tokenGymPass) {
        this.tokenGymPass = tokenGymPass;
    }

    public Integer getReposicao() {
        return reposicao;
    }

    public void setReposicao(Integer reposicao) {
        this.reposicao = reposicao;
    }

    public String getTipoGymPass() {
        if (tipoGymPass == null) {
            tipoGymPass = "";
        }
        return tipoGymPass;
    }

    public void setTipoGymPass(String tipoGymPass) {
        this.tipoGymPass = tipoGymPass;
    }

    public Double getValorGympass() {
        if (valorGympass == null) {
            valorGympass = 0.0;
        }
        return valorGympass;
    }

    public void setValorGympass(Double valorGympass) {
        this.valorGympass = valorGympass;
    }

    public String getValorGympass_Apresentar() {
        if (valorGympass == null) {
            valorGympass = 0.0;
        }
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(valorGympass);
    }

    public String getValorGympass_ApresentarReal() {
        if (valorGympass == null) {
            valorGympass = 0.0;
        }
        return Formatador.formatarValorMonetario(valorGympass);
    }

    @Override
    public int compareTo(Object o) {
        int pessoa = this.getPessoa().compareTo(((PeriodoAcessoClienteVO)o).getPessoa());
        int data = this.getDataLancamento().compareTo(((PeriodoAcessoClienteVO)o).getDataLancamento());
        return pessoa == 0 ? data : pessoa;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public Boolean getTipototalpass() {
        if(tipototalpass == null){
            tipototalpass = false;
        }
        return tipototalpass;
    }

    public void setTipototalpass(Boolean tipototalpass) {
        this.tipototalpass = tipototalpass;
    }

    public String getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(String produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public String getProdutoGymPassApresentar() {
        try {
            if (!UteisValidacao.emptyString(this.produtoGymPass)) {
                return this.produtoGymPass.split(";")[1];
            }
            return "";
        } catch (Exception ignore) {
            return "";
        }
    }

    public String getTokenGoGood() {
        return tokenGoGood;
    }

    public void setTokenGoGood(String tokenGoGood) {
        this.tokenGoGood = tokenGoGood;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }
}

package negocio.comuns.contrato;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.interfaces.contrato.ContratoOperacaoInterfaceFacade;

public class CederUsoContratoVO {

    private final ContratoOperacaoInterfaceFacade contratoOperacaoDAO;
    private final UsuarioVO responsavelOperacao;
    private final ContratoVO contratoVO;
    private final ClienteVO clienteDestinoVO;
    private ContratoOperacaoVO contratoOperacaoVO;

    public CederUsoContratoVO(ContratoOperacaoInterfaceFacade contratoOperacaoDAO, UsuarioVO responsavelOperacao, ContratoVO contratoVO, ClienteVO clienteDestinoVO) {
        this.contratoOperacaoDAO = contratoOperacaoDAO;
        this.responsavelOperacao = responsavelOperacao;
        this.contratoVO = contratoVO;
        this.clienteDestinoVO = clienteDestinoVO;
    }

    public void inicializarDadosOperacaoTransferirDireito() throws Exception {
        ContratoOperacaoVO obj = new ContratoOperacaoVO();
        obj.setContrato(contratoVO.getCodigo());
        obj.setDataOperacao(Calendario.hoje());
        obj.setOperacaoPaga(false);
        obj.setTipoOperacao("TD");
        obj.setDataInicioEfetivacaoOperacao(Calendario.hoje());
        obj.setDataFimEfetivacaoOperacao(Calendario.hoje());
        obj.setResponsavel(responsavelOperacao);
        String sbTransferencia = String.format("Operação de Transferir direito de uso.\r\n" +
                        "Os direito de uso do contrato foram transferidos do aluno %s para o aluno %s.",
                contratoVO.getNome_Apresentar(),
                clienteDestinoVO.getNome_Apresentar());
        obj.setDescricaoCalculo(sbTransferencia);
        contratoOperacaoDAO.incluirSemCommit(obj, false);
        contratoOperacaoVO = obj;
    }

    public void inicializarDadosOperacaoRecuperarDireito() throws Exception {
        ContratoOperacaoVO obj = new ContratoOperacaoVO();
        obj.setContrato(contratoVO.getCodigo());
        obj.setDataOperacao(Calendario.hoje());
        obj.setOperacaoPaga(false);
        obj.setTipoOperacao("RD");
        obj.setDataInicioEfetivacaoOperacao(Calendario.hoje());
        obj.setDataFimEfetivacaoOperacao(Calendario.hoje());
        obj.setResponsavel(responsavelOperacao);
        String sbTransferencia = String.format("Operação de Recuperar os direito de uso.\r\n" +
                        "Os direito de uso do contrato foram retornados para o aluno %s.",
                contratoVO.getPessoaOriginal().getNome());
        obj.setDescricaoCalculo(sbTransferencia);
        contratoOperacaoDAO.incluirSemCommit(obj, false);
        contratoOperacaoVO = obj;
    }

    public ContratoOperacaoVO getContratoOperacaoVO() {
        return contratoOperacaoVO;
    }
}

package negocio.comuns.contrato;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.financeiro.ComissaoMetaFinananceiraVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by glauco on 21/01/14
 */
public class ComissaoGeralConfiguracaoVO extends ComissaoConfiguracaoVO {

    private Integer codigo = 0;
    private Integer duracao = 0;
    private String situacao = "";
    private Double valorFixoEspontaneo = 0.0;
    private Double porcentagemEspontaneo = 0.0;
    private Double valorFixoAgendado = 0.0;
    private Double porcentagemAgendado = 0.0;

    private List<ComissaoMetaFinananceiraVO> listaComissaoMeta = new ArrayList<ComissaoMetaFinananceiraVO>();


    public String toString() {
        if (getDuracao() == 1) {
            return "01 MÊS - " + getSituacaoApresentar();
        } else {
            if (getDuracao() < 10) {
                return "0" + getDuracao() + " MESES - " + getSituacaoApresentar();
            } else {
                return getDuracao() + " MESES" + " - " + getSituacaoApresentar();
            }
        }
    }

    public void validarDados() throws Exception {
        if (getEmpresa().getCodigo() == 0)
            throw new ConsistirException("Selecione a Empresa.");
        if (getSituacao().equals(""))
            throw new Exception("Informe a Situação.");
        if (getDuracao() == 0)
            throw new Exception("Informe a Duração.");
        if (getVigenciaFinal() == null)
            throw new Exception("Informe a Vigência Inicial.");
        if (getVigenciaInicio() == null)
            throw new Exception("Informe a Vigência Final.");

        if ((getListaComissaoMeta() != null) && (getListaComissaoMeta().size() > 0)) {
            for (ComissaoMetaFinananceiraVO obj : getListaComissaoMeta()) {
                if (obj.getValorAgendado() == 0.0 && obj.getValorEspontaneo() == 0.0 &&
                        obj.getPorcentagemAgendado() == 0.0 && obj.getPorcentagemEspontaneo() == 0.0) {
                    throw new Exception("Informe os valores da comissão para a '" + obj.getCodigoMeta() + "ª Meta'.");
                }
            }

        } else {
            if (getValorFixoAgendado() == 0.0 && getValorFixoEspontaneo() == 0.0 &&
                    getPorcentagemAgendado() == 0.0 && getPorcentagemEspontaneo() == 0.0) {
                throw new Exception("Informe os Valores da Comissão.");
            }
        }

    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoApresentar() {
        if (getSituacao().equals("MA")) {
            return "MATRÍCULA";
        } else if (getSituacao().equals("RE")) {
            return "REMATRÍCULA";
        } else if (getSituacao().equals("RN")) {
            return "RENOVAÇÃO";
        }
        return "";
    }

    public Double getValorFixoEspontaneo() {
        return valorFixoEspontaneo;
    }

    public void setValorFixoEspontaneo(Double valorFixoEspontaneo) {
        this.valorFixoEspontaneo = valorFixoEspontaneo;
    }

    public String getValorFixoEspontaneo_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorFixoEspontaneo);
    }

    public Double getPorcentagemEspontaneo() {
        return porcentagemEspontaneo;
    }

    public void setPorcentagemEspontaneo(Double porcentagemEspontaneo) {
        this.porcentagemEspontaneo = porcentagemEspontaneo;
    }

    public String getPorcentagemEspontaneo_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(porcentagemEspontaneo) + "%";
    }

    public Double getValorFixoAgendado() {
        return valorFixoAgendado;
    }

    public void setValorFixoAgendado(Double valorFixoAgendado) {
        this.valorFixoAgendado = valorFixoAgendado;
    }

    public String getValorFixoAgendado_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorFixoAgendado);
    }

    public Double getPorcentagemAgendado() {
        return porcentagemAgendado;
    }

    public void setPorcentagemAgendado(Double porcentagemAgendado) {
        this.porcentagemAgendado = porcentagemAgendado;
    }

    public String getPorcentagemAgendado_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(porcentagemAgendado) + "%";
    }

    public List<ComissaoMetaFinananceiraVO> getListaComissaoMeta() {
        return listaComissaoMeta;
    }

    public void setListaComissaoMeta(List<ComissaoMetaFinananceiraVO> listaComissaoMeta) {
        this.listaComissaoMeta = listaComissaoMeta;
    }
}

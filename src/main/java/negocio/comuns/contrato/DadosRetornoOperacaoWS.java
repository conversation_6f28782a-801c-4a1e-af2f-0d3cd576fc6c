/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class DadosRetornoOperacaoWS extends SuperTO {
    private int contrato = 0;
    private String tipoOperacao = "";
    private String dataInicioRetorno = "";
    private String dataFinalRetorno = "";
    private int diasOperacao = 0;
    private int diasCongelados = 0;
    private int diasContrato = 0;
    private int diasUtilizados = 0;
    private int diasRestantes = 0;
    private Double valorOperacao = 0.0;
    
    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }


    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getDataInicioRetorno() {
        return dataInicioRetorno;
    }

    public void setDataInicioRetorno(String dataInicioRetorno) {
        this.dataInicioRetorno = dataInicioRetorno;
    }

    public String getDataFinalRetorno() {
        return dataFinalRetorno;
    }

    public void setDataFinalRetorno(String dataFinalRetorno) {
        this.dataFinalRetorno = dataFinalRetorno;
    }

    public int getDiasOperacao() {
        return diasOperacao;
    }

    public void setDiasOperacao(int diasOperacao) {
        this.diasOperacao = diasOperacao;
    }

    public int getDiasCongelados() {
        return diasCongelados;
    }

    public void setDiasCongelados(int diasCongelados) {
        this.diasCongelados = diasCongelados;
    }

    public int getDiasContrato() {
        return diasContrato;
    }

    public void setDiasContrato(int diasContrato) {
        this.diasContrato = diasContrato;
    }

    public int getDiasUtilizados() {
        return diasUtilizados;
    }

    public void setDiasUtilizados(int diasUtilizados) {
        this.diasUtilizados = diasUtilizados;
    }

    public int getDiasRestantes() {
        return diasRestantes;
    }

    public void setDiasRestantes(int diasRestantes) {
        this.diasRestantes = diasRestantes;
    }

    public Double getValorOperacao() {
        return valorOperacao;
    }

    public void setValorOperacao(Double valorOperacao) {
        this.valorOperacao = valorOperacao;
    }
}

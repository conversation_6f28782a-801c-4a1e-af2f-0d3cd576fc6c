package negocio.comuns.contrato;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.facade.jdbc.plano.Plano;

/**
 * Reponsável por manter os dados da entidade PlanoDuracao. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see Plano
 */
public class ContratoDuracaoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer contrato;
    protected Integer nrMaximoParcelasCondPagamento;
    protected Integer numeroMeses;
    protected Integer carencia;
    @NaoControlarLogAlteracao
    protected Boolean duracaoEscolhida;
    protected String tipoValor;
    protected String tipoOperacao;
    protected Double percentualDesconto;
    protected Double valorEspecifico;
    @NaoControlarLogAlteracao
    protected Double valorDesejadoMensal;
    @NaoControlarLogAlteracao
    protected Double valorDesejadoParcela;
    @NaoControlarLogAlteracao
    protected Double valorDesejado;
    @NaoControlarLogAlteracao
    private boolean selecionado = false;

    private ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO;
    private ContratoVO contratoVO; // atributo transient.
    private Integer quantidadeDiasExtra = 0;
    /**
     * Construtor padrão da classe <code>PlanoDuracao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ContratoDuracaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PlanoDuracaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ContratoDuracaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (  obj.getNumeroMeses()== null || obj.getNumeroMeses().intValue() == 0  ){
            throw new ConsistirException("O campo NÚMERO DE MESES (Durações) deve ser informado.");
        }
        if ( obj.getNrMaximoParcelasCondPagamento() == null || obj.getNrMaximoParcelasCondPagamento().intValue() == 0 ) {
            throw new ConsistirException("O campo NÚMERO MAXIMO DE PARCELAS DA CONDIÇÃO DE PAGAMENTO (Durações) deve ser informado.");
        }
        if (obj.getTipoValor() == null || obj.getTipoValor().equals("")) {
            throw new ConsistirException("O campo FORMA (Durações) deve ser informado.");
        }
        if (obj.getValorDesejado().doubleValue() != 0  && (obj.getTipoOperacao() == null  || obj.getTipoOperacao().equals(""))) {
            throw new ConsistirException("O campo TIPO OPERAÇÃO (Durações) deve ser informado.");
        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
//        setCodigo(new Integer(0));
//        setContrato(new Integer(0));
//        setNumeroMeses(new Integer(0));
//        setDuracaoEscolhida(new Boolean(false));       
//        setNrMaximoParcelasCondPagamento(new Integer(0));
//        setPercentualDesconto(new Double(0));
//        setValorEspecifico(new Double(0));
//        setValorDesejado(new Double(0));
//        setValorDesejadoMensal(new Double(0));
//        setValorDesejadoParcela(new Double(0));
//        setTipoValor("");
//        setTipoOperacao("");
//        setCarencia(new Integer(0));

    }


    public Integer getNumeroMeses() {
    	if(numeroMeses == null){
    		numeroMeses= new Integer(0);
    	}
        return (numeroMeses);
    }

    public String getNumeroMesesStr() {
    	return getNumeroMeses().toString();
    }

    public void setNumeroMeses(Integer numeroMeses) {
        this.numeroMeses = numeroMeses;
    }

    public Boolean getDuracaoEscolhida() {
    	if(duracaoEscolhida == null){
    		duracaoEscolhida= false;
    	}
        return duracaoEscolhida;
    }

    public void setDuracaoEscolhida(Boolean duracaoEscolhida) {
        this.duracaoEscolhida = duracaoEscolhida;
    }

    public Integer getNrMaximoParcelasCondPagamento() {
    	if(nrMaximoParcelasCondPagamento == null){
    		nrMaximoParcelasCondPagamento= new Integer(0);
    	}
        return (nrMaximoParcelasCondPagamento);
    }

    public void setNrMaximoParcelasCondPagamento(Integer nrMaximoParcelasCondPagamento) {
        this.nrMaximoParcelasCondPagamento = nrMaximoParcelasCondPagamento;
    }

    public Integer getContrato() {
    	if(contrato == null){
    		contrato= new Integer(0);
    	}
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }



    public Integer getCodigo() {
    	if(codigo == null){
    		codigo= new Integer(0);
    	}
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorEspecifico() {
    	if(valorEspecifico == null){
    		valorEspecifico= new Double(0);
    	}
        return (valorEspecifico);
    }

    public Double getValorTotal() {
        return (getValorDesejadoMensal() * getNumeroMeses());
    }

    public void setValorEspecifico(Double valorEspecifico) {
        this.valorEspecifico = valorEspecifico;
    }

    public Double getPercentualDesconto() {
    	if(percentualDesconto == null){
    		percentualDesconto= new Double(0);
    	}
        return (percentualDesconto);
    }

    public void setPercentualDesconto(Double percentualDesconto) {
        this.percentualDesconto = percentualDesconto;
    }

    public String getTipoValor() {
    	if(tipoValor == null){
    		tipoValor= "";
    	}
        return tipoValor;
    }

    public void setTipoValor(String tipoValor) {
        this.tipoValor = tipoValor;
    }

    public String getTipoOperacao() {
    	if(tipoOperacao == null){
    		tipoOperacao= "";
    	}
        return tipoOperacao;
    }

    public String getTipoOperacao_Apresentar() {
        if (tipoOperacao == null || tipoOperacao.equals("")) {
            return ("");
        }
        if (tipoOperacao.equals("AC")) {
            return ("Acrescimo");
        }
        if (tipoOperacao.equals("RE")) {
            return ("Redução");
        }

        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }
  

    public Double getValorDesejadoMensal() {
    	if(valorDesejadoMensal == null){
    		valorDesejadoMensal= new Double(0);
    	}
        return valorDesejadoMensal;
    }

    public void setValorDesejadoMensal(Double valorDesejadoMensal) {
        this.valorDesejadoMensal = valorDesejadoMensal;
    }

    public Double getValorDesejadoParcela() {
    	if(valorDesejadoParcela == null){
    		valorDesejadoParcela= new Double(0);
    	}
        return valorDesejadoParcela;
    }

    public void setValorDesejadoParcela(Double valorDesejadoParcela) {
        this.valorDesejadoParcela = valorDesejadoParcela;
    }

    public Double getValorDesejado() {
    	if(valorDesejado == null){
    		valorDesejado= new Double(0);
    	}
        return valorDesejado;
    }

    public void setValorDesejado(Double valorDesejado) {
        this.valorDesejado = valorDesejado;
    }

    public Integer getCarencia() {
    	if(carencia == null){
    		carencia= new Integer(0);
    	}
        return carencia;
    }

    public void setCarencia(Integer carencia) {
        this.carencia = carencia;
    }


    public boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public ContratoDuracaoCreditoTreinoVO getContratoDuracaoCreditoTreinoVO() {
        return contratoDuracaoCreditoTreinoVO;
    }

    public void setContratoDuracaoCreditoTreinoVO(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO) {
        this.contratoDuracaoCreditoTreinoVO = contratoDuracaoCreditoTreinoVO;
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public Integer getQuantidadeDiasExtra() {
        return quantidadeDiasExtra;
    }

    public void setQuantidadeDiasExtra(Integer quantidadeDiasExtra) {
        this.quantidadeDiasExtra = quantidadeDiasExtra;
    }

    public Integer getTotalDias(){
        return (this.numeroMeses * 30) + this.quantidadeDiasExtra;
    }

    public String getDescricaoDuracao(){
        if (this.quantidadeDiasExtra > 0){
            return getTotalDias() + " Dias";
        }
        if (this.numeroMeses == 1)
            return "1 Mês";
        else
            return this.numeroMeses + " Meses";
    }


}

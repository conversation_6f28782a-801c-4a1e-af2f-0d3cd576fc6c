/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AfastamentoContratoDependenteVO extends SuperVO {

    private ContratoDependenteVO contratoDependenteVO;
    private ContratoVO contratoVO;
    private Date dataInicio;
    private Date dataTermino;
    private Date dataInicioRetorno;
    private Date dataTerminoRetorno;
    private Date dataRegistro;
    private Integer justificativa;
    private Integer nrDiasContrato;
    private Integer nrDiasUsadoContrato;
    private Integer nrDiasRestanteContrato;
    private Integer nrDiasUsados;
    private Integer nrDiasFeriasPermitido;
    private Integer nrDiasRestam;
    private Integer nrDias;
    private Integer nrDiasSomar;
    private Long periodoCarencia;
    private String observacao;
    private UsuarioVO responsavelOperacao;
    private Boolean mensagemErro;
    private Boolean qtdDiasCarenciaMaiorQueContrato;
    private String tipoAfastamento;
    private OrigemSistemaEnum origemSistema;

    public AfastamentoContratoDependenteVO() {
        inicializarDados();
    }

    public void inicializarDados() {
        setContratoVO(new ContratoVO());
        setPeriodoCarencia(0L);
        setDataRegistro(Calendario.hoje());
        setNrDiasFeriasPermitido(0);
        setNrDiasUsados(0);
        setNrDiasRestam(0);
        setNrDias(0);
        setNrDiasSomar(0);
        setResponsavelOperacao(new UsuarioVO());
        setQtdDiasCarenciaMaiorQueContrato(false);
        setNrDiasContrato(0);
        setNrDiasRestanteContrato(0);
        setNrDiasUsadoContrato(0);
    }

    public void validarPeriodoCarencia(List<AfastamentoContratoDependenteVO> listaAfastamentos) throws Exception {
        if (getDataInicio() == null || getDataTermino() == null) {
            throw new ConsistirException("O período da Férias deve ser informado");
        }
        if (Calendario.maior(getDataInicio(), getDataTermino())) {
            throw new ConsistirException("O campo ATÉ não pode ser antes do campo INÍCIO");
        }

        validarDiasUtilizados(listaAfastamentos, this.getContratoVO().getContratoDuracao().getCarencia());

        if (getJustificativa().equals(0)) {
            throw new ConsistirException("A Justificativa das Férias deve ser informado");
        }
        if (periodoCarencia.intValue() > getNrDiasRestam()) {
            throw new ConsistirException("O período informado para Férias contém: " + periodoCarencia.intValue() + " dia(s)."
                    + " Ultrapassando a quantidade de dias restantes. Por favor, informe novamente o período para as Férias");
        }
    }

    private void validarDiasUtilizados(List<AfastamentoContratoDependenteVO> listaAfastamentos, Integer diasCarencia) throws Exception {
        this.setNrDiasFeriasPermitido(diasCarencia);
        this.setNrDiasUsados(0);

        for (AfastamentoContratoDependenteVO afastamento : listaAfastamentos) {
            if (afastamento.isFerias()) {
                setNrDiasUsados(this.getNrDiasUsados() + afastamento.getNrDias());
            }
        }
        this.setNrDiasRestam(this.getNrDiasFeriasPermitido() - this.getNrDiasUsados());

        if (isFerias()) {
            if (this.getNrDiasFeriasPermitido() <= 0) {
                throw new ConsistirException("Não será possível realizar as férias para este contrato, pois ele não permite dias para férias.");
            }
            if (this.getNrDiasRestam() <= 0) {
                throw new ConsistirException("Não será possível realizar a férias para este contrato, pois os dias permitidos para férias já foram utilizados.");
            }
        }
    }

    public void alterarSituacaoContrato(Date data) throws Exception {
        getContratoVO().setDataPrevistaRenovar(data);
        getContratoVO().setDataPrevistaRematricula(data);
        getContratoVO().setVigenciaAteAjustada(data);
        getFacade().getContrato().alterarSituacaoContrato(getContratoVO());
    }

    public ContratoDependenteVO getContratoDependenteVO() {
        if (contratoDependenteVO == null) {
            contratoDependenteVO = new ContratoDependenteVO();
        }
        return contratoDependenteVO;
    }

    public void setContratoDependenteVO(ContratoDependenteVO contratoDependenteVO) {
        this.contratoDependenteVO = contratoDependenteVO;
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public Date getDataInicio() {
        if (dataInicio == null) {
            dataInicio = Calendario.hoje();
        }
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataInicioRetorno_Apresentar() {
        return Uteis.getData(dataInicioRetorno);
    }

    public Date getDataInicioRetorno() {
        return dataInicioRetorno;
    }

    public void setDataInicioRetorno(Date dataInicioRetorno) {
        this.dataInicioRetorno = dataInicioRetorno;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataTermino() {
        if (dataTermino == null) {
            dataTermino = Calendario.hoje();
        }
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public String getDataTerminoRetorno_Apresentar() {
        return Uteis.getData(dataTerminoRetorno);
    }

    public Date getDataTerminoRetorno() {
        return dataTerminoRetorno;
    }

    public void setDataTerminoRetorno(Date dataTerminoRetorno) {
        this.dataTerminoRetorno = dataTerminoRetorno;
    }

    public Boolean getMensagemErro() {
        return mensagemErro;
    }

    public void setMensagemErro(Boolean mensagemErro) {
        this.mensagemErro = mensagemErro;
    }

    public Integer getNrDiasFeriasPermitido() {
        return nrDiasFeriasPermitido;
    }

    public void setNrDiasFeriasPermitido(Integer nrDiasFeriasPermitido) {
        this.nrDiasFeriasPermitido = nrDiasFeriasPermitido;
    }

    public Integer getNrDiasUsados() {
        return nrDiasUsados;
    }

    public void setNrDiasUsados(Integer nrDiasUsados) {
        this.nrDiasUsados = nrDiasUsados;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public UsuarioVO getResponsavelOperacao() {
        return responsavelOperacao;
    }

    public void setResponsavelOperacao(UsuarioVO responsavelOperacao) {
        this.responsavelOperacao = responsavelOperacao;
    }

    public Integer getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(Integer justificativa) {
        this.justificativa = justificativa;
    }

    public Integer getNrDiasRestam() {
        return nrDiasRestam;
    }

    public void setNrDiasRestam(Integer nrDiasRestam) {
        this.nrDiasRestam = nrDiasRestam;
    }

    public Boolean getQtdDiasCarenciaMaiorQueContrato() {
        return qtdDiasCarenciaMaiorQueContrato;
    }

    public void setQtdDiasCarenciaMaiorQueContrato(Boolean qtdDiasCarenciaMaiorQueContrato) {
        this.qtdDiasCarenciaMaiorQueContrato = qtdDiasCarenciaMaiorQueContrato;
    }

    public Integer getNrDias() {
        if (nrDias == null) {
            nrDias = 0;
        }
        return nrDias;
    }

    public void setNrDias(Integer nrDias) {
        this.nrDias = nrDias;
    }

    public Integer getNrDiasSomar() {
        if (nrDiasSomar == null) {
            nrDiasSomar = 0;
        }
        return nrDiasSomar;
    }

    public void setNrDiasSomar(Integer nrDiasSomar) {
        this.nrDiasSomar = nrDiasSomar;
    }

    public Long getPeriodoCarencia() {
        return periodoCarencia;
    }

    public void setPeriodoCarencia(Long periodoCarencia) {
        this.periodoCarencia = periodoCarencia;
    }


    public Integer getNrDiasContrato() {
        return nrDiasContrato;
    }

    public void setNrDiasContrato(Integer nrDiasContrato) {
        this.nrDiasContrato = nrDiasContrato;
    }

    public Integer getNrDiasRestanteContrato() {
        return nrDiasRestanteContrato;
    }

    public void setNrDiasRestanteContrato(Integer nrDiasRestanteContrato) {
        this.nrDiasRestanteContrato = nrDiasRestanteContrato;
    }

    public Integer getNrDiasUsadoContrato() {
        return nrDiasUsadoContrato;
    }

    public void setNrDiasUsadoContrato(Integer nrDiasUsadoContrato) {
        this.nrDiasUsadoContrato = nrDiasUsadoContrato;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        if (origemSistema == null) {
            origemSistema = OrigemSistemaEnum.ZW;
        }
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public String getTipoAfastamento() {
        if (tipoAfastamento == null) {
            tipoAfastamento = "";
        }
        return tipoAfastamento;
    }

    public void setTipoAfastamento(String tipoAfastamento) {
        this.tipoAfastamento = tipoAfastamento;
    }

    public String getTipoAfastamentoApresentar() {
        if (isFerias()) {
            return "Férias";
        } else if (isAtestado()) {
            return "Atestado";
        }
        return " - ";
    }

    public boolean isFerias() {
        return getTipoAfastamento().equals("CR");
    }

    public boolean isAtestado() {
        return getTipoAfastamento().equals("AT");
    }

    public void validarRegrasAfastamento() throws Exception {
        if (getContratoDependenteVO().getContrato().getSituacao().equals("CA")) {
            throw new ConsistirException("Operação não permitida, pois o contrato titular está cancelado.");
        }
        if (Calendario.maior(getContratoDependenteVO().getDataInicio(), Calendario.hoje())) {
            throw new ConsistirException("Não é possivel lançar um afastamento para um contrato que ainda não iniciou.");
        }
        if (isFerias() && Calendario.menor(getContratoDependenteVO().getDataFinalAjustada(), Calendario.hoje())) {
            throw new ConsistirException("Não é possivel lançar um afastamento do tipo férias para um contrato inativo.");
        }
    }

    public void validarDatasAfastamento() throws Exception {
        if (Calendario.maior(getDataInicio(), getContratoDependenteVO().getDataFinalAjustada())) {
            throw new Exception("A data de INÍCIO do afastamento não pode ser depois da data de término do contrato");
        }
        if (Calendario.maior(getDataInicio(), getDataTermino())) {
            throw new Exception("O campo ATÉ não pode ser antes do campo INÍCIO");
        }
    }
}

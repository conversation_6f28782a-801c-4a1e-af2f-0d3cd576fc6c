package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperTO;

/**
 * Created by <PERSON><PERSON> on 20/06/2015
 */
public class MovParcelaWS extends SuperTO {

    private int codigo;
    private int contrato;
    private int nrParcela;
    private String descricao = "";
    private String dataLancamento = "";
    private String dataVencimento = "";
    private double valor;
    private String situacao = "";
    private String plano = "";
    private Double multa;
    private Double juros;
    private int parcelamentooperadora;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getPlano() {
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public int getNrParcela() {
        return nrParcela;
    }

    public void setNrParcela(int nrParcela) {
        this.nrParcela = nrParcela;
    }

    public Double getMulta() {
        return multa;
    }

    public void setMulta(Double multa) {
        this.multa = multa;
    }

    public Double getJuros() {
        return juros;
    }

    public void setJuros(Double juros) {
        this.juros = juros;
    }

    public int getParcelamentooperadora() {
        return parcelamentooperadora;
    }

    public void setParcelamentooperadora(int parcelamentooperadora) {
        this.parcelamentooperadora = parcelamentooperadora;
    }
}

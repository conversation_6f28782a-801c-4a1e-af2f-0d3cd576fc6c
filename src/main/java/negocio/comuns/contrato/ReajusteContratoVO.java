package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.plano.IndiceFinanceiroReajustePrecoVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.Date;

/**
 * Created by ulisses on 23/11/2016.
 */
public class ReajusteContratoVO extends SuperVO {

    private Integer codigo;
    private ContratoVO contratoVO;
    private IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO;
    private Double percentualIndice;
    private Double valorMensalAnterior;
    private Double valorMensalNovo;
    private UsuarioVO usuarioVO;
    private Date dataLancamento;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public IndiceFinanceiroReajustePrecoVO getIndiceFinanceiroReajustePrecoVO() {
        return indiceFinanceiroReajustePrecoVO;
    }

    public void setIndiceFinanceiroReajustePrecoVO(IndiceFinanceiroReajustePrecoVO indiceFinanceiroReajustePrecoVO) {
        this.indiceFinanceiroReajustePrecoVO = indiceFinanceiroReajustePrecoVO;
    }

    public Double getPercentualIndice() {
        return percentualIndice;
    }

    public void setPercentualIndice(Double percentualIndice) {
        this.percentualIndice = percentualIndice;
    }

    public Double getValorMensalAnterior() {
        return valorMensalAnterior;
    }

    public void setValorMensalAnterior(Double valorMensalAnterior) {
        this.valorMensalAnterior = valorMensalAnterior;
    }

    public Double getValorMensalNovo() {
        return valorMensalNovo;
    }

    public void setValorMensalNovo(Double valorMensalNovo) {
        this.valorMensalNovo = valorMensalNovo;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public static void validarDados(ReajusteContratoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getPercentualIndice() == null){
            throw new ConsistirException("O campo PercentualIndice (ReajusteContrato) deve ser informado.");
        }
        if ((obj.getValorMensalAnterior() == null) || (obj.getValorMensalAnterior() <= 0)) {
            throw new ConsistirException("O campo ValorMensalAnterior (ReajusteContrato) deve ser informado.");
        }
        if ((obj.getValorMensalNovo() == null) || (obj.getValorMensalNovo() <= 0)) {
            throw new ConsistirException("O campo ValorMensalNovo (ReajusteContrato) deve ser informado.");
        }
        if ((obj.getUsuarioVO() == null) || (obj.getUsuarioVO().getCodigo() <= 0)) {
            throw new ConsistirException("O campo usuario (ReajusteContrato) deve ser informado.");
        }
        if ((obj.getContratoVO() == null) || (obj.getContratoVO().getCodigo() <= 0)) {
            throw new ConsistirException("O campo contrato (ReajusteContrato) deve ser informado.");
        }
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }
}

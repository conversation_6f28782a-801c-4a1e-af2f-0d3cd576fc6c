package negocio.comuns.contrato;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.Lista;
import java.util.Iterator;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;

/**
 * Reponsável por manter os dados da entidade ContratoModalidadeTurma. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class ContratoModalidadeTurmaVO extends SuperVO {

    protected Integer codigo;
    protected Integer contratoModalidade;
    /** Atributo responsável por manter os objetos da classe <code>ContratoModalidadeHorarioTurma</code>. */
    @Lista
    private List contratoModalidadeHorarioTurmaVOs;
    /** Atributo responsável por manter o objeto relacionado da classe <code>ContratoModalidadeTurma </code>.*/
    @ChaveEstrangeira
    protected TurmaVO turma;

    /**
     * Construtor padrão da classe <code>ContratoModalidadeTurma</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ContratoModalidadeTurmaVO() {
        super();
        inicializarDados();
    }

    public ContratoModalidadeTurmaVO(boolean inicializarRecursivamente) {
        super();
        if (inicializarRecursivamente) {
            inicializarDadosComRecursividade();
        } else {
            inicializarDados();
        }
    }

    public static void validarDados(ContratoModalidadeTurmaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getContratoModalidade().intValue() == 0) {
            throw new ConsistirException("O campo CONTRATOMODALIDADE (Contrato Modalidade Turma) deve ser informado.");
        }
        if ((obj.getTurma() == null) ||
                (obj.getTurma().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo TURMA (Contrato Modalidade Turma) deve ser informado.");
        }
    }

    public static void validarDadosAdicionar(ContratoModalidadeTurmaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getTurma() == null) ||
                (obj.getTurma().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo TURMA (Contrato Modalidade Turma) deve ser informado.");
        }
    }

    public void finalizarMatriculaAlunoNaTurma(ContratoVO contrato, Integer modalidade) throws Exception {
        List obj = getFacade().getMatriculaAlunoHorarioTurma().consultarPorEmpresaContratoModalidade(contrato.getEmpresa().getCodigo(), contrato.getCodigo(), modalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Iterator i = obj.iterator();
        while (i.hasNext()) {
            MatriculaAlunoHorarioTurmaVO mat = (MatriculaAlunoHorarioTurmaVO) i.next();
            if (Uteis.getCompareData(mat.getDataInicio(), negocio.comuns.utilitarias.Calendario.hoje()) == 0) {
                getFacade().getMatriculaAlunoHorarioTurma().excluirSemCommit(mat);
            } else {
                mat.setDataFim(Uteis.obterDataAnterior(negocio.comuns.utilitarias.Calendario.hoje(), 1));
                getFacade().getMatriculaAlunoHorarioTurma().alterarSemCommit(mat);
            }
        }
    }

    public void finalizarMatriculaAlunoNaTurmaHorario(ContratoVO contrato, ContratoModalidadeHorarioTurmaVO cmhtAntigo, Integer modalidade) throws Exception {
        MatriculaAlunoHorarioTurmaVO obj = getFacade().getMatriculaAlunoHorarioTurma().consultarPorEmpresaPessoaContratoHorarioTurma(contrato.getEmpresa().getCodigo(), contrato.getPessoa().getCodigo(), contrato.getCodigo(), cmhtAntigo.getHorarioTurma().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (obj != null) {
            if (Uteis.getCompareData(obj.getDataInicio(), negocio.comuns.utilitarias.Calendario.hoje()) == 0) {
                getFacade().getMatriculaAlunoHorarioTurma().excluirSemCommit(obj);
            } else {
                obj.setDataFim(Uteis.obterDataAnterior(negocio.comuns.utilitarias.Calendario.hoje(), 1));
                getFacade().getMatriculaAlunoHorarioTurma().alterarSemCommit(obj);
            }
        }
    }

    public void inicializarDadosComRecursividade() {
        setCodigo(new Integer(0));
        setContratoModalidade(new Integer(0));
        setTurma(null);
    }

    public void realizarUpperCaseDados() {
    }

    public void inicializarDados() {
        setCodigo(new Integer(0));
        setContratoModalidade(new Integer(0));
        setContratoModalidadeHorarioTurmaVOs(new ArrayList());
        setTurma(new TurmaVO());
    }

    public void carregarListaContratoModalidadeHorarioTurmaVOs(ContratoModalidadeHorarioTurmaVO obj, List lista) throws Exception {
        try {
            Iterator i = lista.iterator();
            while (i.hasNext()) {
                HorarioTurmaVO horarioTurma = (HorarioTurmaVO) i.next();
                obj.setHorarioTurma(horarioTurma);
                adicionarObjContratoModalidadeHorarioTurmaVOs(obj);
                obj = new ContratoModalidadeHorarioTurmaVO();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void removerContratoModalidadeHorarioTurmaVOsLista(String horario) throws Exception {
        try {
            Iterator i = getContratoModalidadeHorarioTurmaVOs().iterator();
            int index = 0;
            while (i.hasNext()) {
                ContratoModalidadeHorarioTurmaVO contratoHorarioTurma = (ContratoModalidadeHorarioTurmaVO) i.next();
                if (contratoHorarioTurma.getHorarioTurma().getHoraInicial().compareTo(horario.substring(0, 5)) == -1 || contratoHorarioTurma.getHorarioTurma().getHoraFinal().compareTo(horario.substring(8, 13)) > 0) {
                    getContratoModalidadeHorarioTurmaVOs().remove(index);
                    removerContratoModalidadeHorarioTurmaVOsLista(horario);
                    return;
                }
                index++;
            }

        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>
     * ao List <code>contratoModalidadeHorarioTurmaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>ContratoModalidadeHorarioTurma</code> - getHorarioTurma() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjContratoModalidadeHorarioTurmaVOs(ContratoModalidadeHorarioTurmaVO obj) throws Exception {
        ContratoModalidadeHorarioTurmaVO.validarDados(obj);
        int index = 0;
        Iterator i = getContratoModalidadeHorarioTurmaVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeHorarioTurmaVO objExistente = (ContratoModalidadeHorarioTurmaVO) i.next();
            if (objExistente.getHorarioTurma().getDiaSemana().equals(obj.getHorarioTurma().getDiaSemana()) &&
                    objExistente.getHorarioTurma().getHoraInicial().equals(obj.getHorarioTurma().getHoraInicial()) &&
                    objExistente.getHorarioTurma().getHoraFinal().equals(obj.getHorarioTurma().getHoraFinal()) &&
                    objExistente.getHorarioTurma().getAmbiente().getCodigo().equals(obj.getHorarioTurma().getAmbiente().getCodigo().intValue()) &&
                    objExistente.getHorarioTurma().getNivelTurma().getCodigo().equals(obj.getHorarioTurma().getNivelTurma().getCodigo().intValue())) {

//                    getContratoModalidadeHorarioTurmaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getContratoModalidadeHorarioTurmaVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>
     * no List <code>contratoModalidadeHorarioTurmaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>ContratoModalidadeHorarioTurma</code> - getHorarioTurma() - como identificador (key) do objeto no List.
     * @param horarioTurma  Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjContratoModalidadeHorarioTurmaVOs(Integer horarioTurma) throws Exception {
        int index = 0;
        Iterator i = getContratoModalidadeHorarioTurmaVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeHorarioTurmaVO objExistente = (ContratoModalidadeHorarioTurmaVO) i.next();
            if (objExistente.getHorarioTurma().getCodigo().equals(horarioTurma)) {
                getContratoModalidadeHorarioTurmaVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>ContratoModalidadeHorarioTurmaVO</code>
     * no List <code>contratoModalidadeHorarioTurmaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>ContratoModalidadeHorarioTurma</code> - getHorarioTurma() - como identificador (key) do objeto no List.
     * @param horarioTurma  Parâmetro para localizar o objeto do List.
     */
    public ContratoModalidadeHorarioTurmaVO consultarObjContratoModalidadeHorarioTurmaVO(Integer horarioTurma) throws Exception {
        Iterator i = getContratoModalidadeHorarioTurmaVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeHorarioTurmaVO objExistente = (ContratoModalidadeHorarioTurmaVO) i.next();
            if (objExistente.getHorarioTurma().getCodigo().equals(horarioTurma)) {
                return objExistente;
            }
        }
        return null;
    }

    public boolean existeHorarioTurma(int codigo) {
        Iterator i = contratoModalidadeHorarioTurmaVOs.iterator();
        while(i.hasNext()) {
            ContratoModalidadeHorarioTurmaVO aux = (ContratoModalidadeHorarioTurmaVO)i.next();
            if(aux.getHorarioTurma().getCodigo() == codigo)
                return true;
        }
        return false;
    }

    /**
     * Retorna o objeto da classe <code>Turma</code> relacionado com (<code>Turma</code>).
     */
    public TurmaVO getTurma() {
        return turma;
    }

    public void setTurma(TurmaVO turma) {
        this.turma = turma;
    }

    /** Retorna Atributo responsável por manter os objetos da classe <code>ContratoModalidadeHorarioTurma</code>. */
    public List getContratoModalidadeHorarioTurmaVOs() {
        return (contratoModalidadeHorarioTurmaVOs);
    }

    /** Define Atributo responsável por manter os objetos da classe <code>ContratoModalidadeHorarioTurma</code>. */
    public void setContratoModalidadeHorarioTurmaVOs(List contratoModalidadeHorarioTurmaVOs) {
        this.contratoModalidadeHorarioTurmaVOs = contratoModalidadeHorarioTurmaVOs;
    }

    public Integer getContratoModalidade() {
        return (contratoModalidade);
    }

    public void setContratoModalidade(Integer contratoModalidade) {
        this.contratoModalidade = contratoModalidade;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    @Override
    public boolean equals(Object obj) {
        try {
            ContratoModalidadeTurmaVO cmht = (ContratoModalidadeTurmaVO)obj;
            return this.codigo == cmht.codigo;
        } catch(Exception e) {
            return false;
        }
    }
    
    public String getNomeTurma_Apresentar(){
        if(getTurma() != null && !UteisValidacao.emptyString(getTurma().getDescricao())) {
            return getTurma().getDescricao();
        }
        return "";
    }
}
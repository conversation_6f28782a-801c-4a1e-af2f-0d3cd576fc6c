/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class ContratoOperacaoWS extends SuperTO {
    private Integer codigo = 0;
    private Integer contrato = 0;
    private String tipoOperacao = "";
    private String tipoOperacaoApresentar = "";
    private Boolean operacaoPaga = false;
    private String dataRegistro= "";
    private String dataInicio= "";
    private String dataFim= "";
    private String dataInicioRetorno= "";
    private String dataFinalRetorno= "";
    private int quantidadeDiasOperacao= 0;
    private int quantidadeDiasRestantesContrato= 0;
    private JustificativaOperacaoWS justificativa = new JustificativaOperacaoWS(0, "");
    private String observacao = "";
    private String descricaoCalculo = "";
    private Double valor = 0.0;

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public Boolean getOperacaoPaga() {
        return operacaoPaga;
    }

    public void setOperacaoPaga(Boolean operacaoPaga) {
        this.operacaoPaga = operacaoPaga;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFim() {
        return dataFim;
    }

    public void setDataFim(String dataFim) {
        this.dataFim = dataFim;
    }

    public String getDataInicioRetorno() {
        return dataInicioRetorno;
    }

    public void setDataInicioRetorno(String dataInicioRetorno) {
        this.dataInicioRetorno = dataInicioRetorno;
    }

    public String getDataFinalRetorno() {
        return dataFinalRetorno;
    }

    public void setDataFinalRetorno(String dataFinalRetorno) {
        this.dataFinalRetorno = dataFinalRetorno;
    }

    public int getQuantidadeDiasOperacao() {
        return quantidadeDiasOperacao;
    }

    public void setQuantidadeDiasOperacao(int quantidadeDiasOperacao) {
        this.quantidadeDiasOperacao = quantidadeDiasOperacao;
    }

    public int getQuantidadeDiasRestantesContrato() {
        return quantidadeDiasRestantesContrato;
    }

    public void setQuantidadeDiasRestantesContrato(int quantidadeDiasRestantesContrato) {
        this.quantidadeDiasRestantesContrato = quantidadeDiasRestantesContrato;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getDescricaoCalculo() {
        return descricaoCalculo;
    }

    public void setDescricaoCalculo(String descricaoCalculo) {
        this.descricaoCalculo = descricaoCalculo;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoOperacaoApresentar() {
        return tipoOperacaoApresentar;
    }

    public void setTipoOperacaoApresentar(String tipoOperacaoApresentar) {
        this.tipoOperacaoApresentar = tipoOperacaoApresentar;
    }
}

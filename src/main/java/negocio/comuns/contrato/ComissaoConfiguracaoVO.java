package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.Calendar;
import java.util.Date;

/**
 * Created by GlaucoT on 25/01/2017
 */
public abstract class ComissaoConfiguracaoVO extends SuperVO {

    private Date vigenciaInicio = null;
    private Date vigenciaFinal = null;
    private EmpresaVO empresa = new EmpresaVO();

    public Date getVigenciaInicio() {
        return vigenciaInicio;
    }

    public void setVigenciaInicio(Date vigenciaInicio) {
        this.vigenciaInicio = vigenciaInicio;
    }

    public String getVigenciaInicio_Apresentar() {
        if (getVigenciaInicio() == null) {
            return "";
        }
        return Calendario.getData(getVigenciaInicio(), "MM/yyyy");
    }

    public long getVigenciaInicio_Long() {
        if (getVigenciaInicio() == null) {
            return Long.MIN_VALUE;
        }
        return getVigenciaInicio().getTime();
    }

    public Date getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(Date vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }

    public String getVigenciaFinal_Apresentar() {
        if (getVigenciaFinal() == null) {
            return "";
        }
        return Calendario.getData(getVigenciaFinal(), "MM/yyyy");
    }

    public long getVigenciaFinal_Long() {
        if (getVigenciaFinal() == null) {
            return Long.MAX_VALUE;
        }
        return getVigenciaFinal().getTime();
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }


    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }
}

package negocio.comuns.contrato;


import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.comuns.utilitarias.Uteis;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class PlanoPersonalAssinaturaDigitalVO extends SuperVO {
    @ChavePrimaria
    private Integer codigo;
    private ControleTaxaPersonalVO taxaPersonal;
    private PlanoPersonalTextoPadraoVO planoPersonalTextoPadrao;
    private UsuarioVO usuarioResponsavel;
    private String documentos;
    private String endereco;
    private String assinatura;
    private String atestado;
    private String anexo1;
    private String anexo2;
    private Date lancamento;

    public ControleTaxaPersonalVO getTaxaPersonal() {
        if (taxaPersonal == null){
            taxaPersonal = new ControleTaxaPersonalVO();
        }
        return taxaPersonal;
    }

    public void setTaxaPersonal(ControleTaxaPersonalVO taxaPersonalVO) {
        this.taxaPersonal = taxaPersonalVO;
    }

    public UsuarioVO getUsuarioResponsavel() {
            if(usuarioResponsavel == null){
            usuarioResponsavel = new UsuarioVO();
        }
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public PlanoPersonalTextoPadraoVO getPlanoTextoPadrao() {
        if (planoPersonalTextoPadrao == null){
            planoPersonalTextoPadrao = new PlanoPersonalTextoPadraoVO();
        }
        return planoPersonalTextoPadrao;
    }

    public void setPlanoTextoPadrao(PlanoPersonalTextoPadraoVO planoPersonalTextoPadrao) {
        this.planoPersonalTextoPadrao = planoPersonalTextoPadrao;
    }

    public String getDocumentos() {
        return documentos;
    }

    public void setDocumentos(String documentos) {
        this.documentos = documentos;
    }

    public String getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(String assinatura) {
        this.assinatura = assinatura;
    }

    public String getAtestado() {
        return atestado;
    }

    public void setAtestado(String atestado) {
        this.atestado = atestado;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public void setAnexo1(String anexo1) {
        this.anexo1 = anexo1;
    }

    public String getAnexo1() {
        return anexo1;
    }

    public void setAnexo2(String anexo2) {
        this.anexo2 = anexo2;
    }

    public String getAnexo2() {
        return anexo2;
    }

    public Date getLancamento() {
        return lancamento;
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }

    public void initImagens() {
        String imagemPadrao = Uteis.getPaintImagemDaNuvem("", "../");
        if (getDocumentos() == null || getDocumentos().isEmpty())
            setDocumentos(imagemPadrao);
        if (getEndereco() == null || getEndereco().isEmpty())
            setEndereco(imagemPadrao);
        if (getAtestado() == null || getAtestado().isEmpty())
            setAtestado(imagemPadrao);
        if (getAnexo1() == null || getAnexo1().isEmpty())
            setAnexo1(imagemPadrao);
        if (getAnexo2() == null || getAnexo2().isEmpty())
            setAnexo2(imagemPadrao);
    }
}

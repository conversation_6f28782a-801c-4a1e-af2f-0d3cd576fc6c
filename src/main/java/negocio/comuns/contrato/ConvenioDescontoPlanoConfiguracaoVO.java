package negocio.comuns.contrato;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

public class ConvenioDescontoPlanoConfiguracaoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Double valorDesconto;
    protected Double porcentagemDesconto;
    protected String tipoDesconto;
    protected Integer convenioDesconto;
    protected Integer plano;
    protected Integer duracao;
    protected Boolean convenioDescontoConfiguracaoEscolhida;

    public ConvenioDescontoPlanoConfiguracaoVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setCodigo(0);
        setValorDesconto(0.0);
        setPorcentagemDesconto(0.0);
        setTipoDesconto("");
        setDuracao(0);
        setPlano(0);
        setConvenioDescontoConfiguracaoEscolhida(false);
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getConvenioDesconto() {
        return (convenioDesconto);
    }

    public void setConvenioDesconto(Integer convenioDesconto) {
        this.convenioDesconto = convenioDesconto;
    }

    public String getTipoDesconto() {
        if (tipoDesconto == null) {
            tipoDesconto = "";
        }
        return (tipoDesconto);
    }

    public void setTipoDesconto(String tipoDesconto) {
        this.tipoDesconto = tipoDesconto;
    }

    public Double getPorcentagemDesconto() {
        return (porcentagemDesconto);
    }

    public void setPorcentagemDesconto(Double porcentagemDesconto) {
        this.porcentagemDesconto = porcentagemDesconto;
    }

    public Double getValorDesconto() {
        return (valorDesconto);
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getConvenioDescontoConfiguracaoEscolhida() {
        return convenioDescontoConfiguracaoEscolhida;
    }

    public void setConvenioDescontoConfiguracaoEscolhida(Boolean convenioDescontoConfiguracaoEscolhida) {
        this.convenioDescontoConfiguracaoEscolhida = convenioDescontoConfiguracaoEscolhida;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }
}
package negocio.comuns.contrato;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoParcelaCancelamento;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.CancelamentoAntecipadoTO;
import negocio.comuns.financeiro.CancelamentoAvaliandoParcelaTO;
import negocio.comuns.financeiro.CancelamentoProporcionalTO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoTO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CancelamentoContratoVO extends SuperVO {

    protected Date dataCancelamento;
    protected Integer nrDiasContrato;
    protected Integer nrDiasUtilizadosPeloClienteContrato;
    protected Integer nrDiasRestamContrato;
    protected Integer nrDiasOperacao;
    protected Double nrDiasCreditarNoContrato;
    protected Double valorBaseContrato;
    protected Double valorTotalBaseContratoRenovacao;
    //atributo com valor total da somas dos produtos que cada contrato contem
    protected Double valorTotalSomaProdutoContratos;
    protected Double valorTotalSomaProdutoContratosDevolver = 0.0;
    protected Double valorMensalContrato;
    protected Double valorDiaContratoValorBase;
    protected Double valorDiaContratoCreditadoValorBase;
    protected Double valorDiaContratoValorMensal;
    protected Double valorCreditoRestanteContratoValorBase;
    protected Double valorCreditoRestanteContratoValorMensal;
    protected Double valorCreditoRestanteContratoVendaCreditoSemTaxaEMulta;
    protected Double valorCreditoRestanteContratoValorMensalSemTaxaEMulta;
    protected Double valorTaxaCancelamento;
    protected Double percentualMultaCancelamento;
    protected Double percentualMultaCancelamentoRec;
    protected Double valorSerRecebidoPeloCliente;
    protected Double saldoContaCorrenteCliente;
    protected Double valorTotalPagoPeloCliente;
    //protected Double valorUtilizadoPeloCliente;
    protected Double valorUtilizadoPeloClienteMensal;
    protected Double valorUtilizadoPeloClienteBase;
    protected Double valorASerDevolvido;
    protected Double valorASerDevolvidoBaseCalculo;
    protected Double somaValorPagoPeloClienteComCheque;
    protected Double valorQuitacaoCancelamento;
    private Double valorDevolucaoCancelamento;
    @NaoControlarLogAlteracao
    protected String tipoDevolucaoCancelamento;
    protected String mensagemTransferencia;
    protected String mensagemDevolucao;
    protected String observacao;
    @NaoControlarLogAlteracao
    protected Integer tipoJustificativaOperacao;
    protected Boolean mensagemErro;
    @ChaveEstrangeira
    protected MovProdutoVO movProdutoDevolucao;
    @ChaveEstrangeira
    protected MovProdutoVO movProdutoQuitacao;
    @ChaveEstrangeira
    private MovProdutoVO movProdutoDevolucaoRecebiveis;
    @NaoControlarLogAlteracao
    protected List<ContratoVO> listaContratos;
    @NaoControlarLogAlteracao
    protected List<MovPagamentoVO> listaPagamentos;
	@NaoControlarLogAlteracao
    protected List<MovPagamentoVO> listaPagamentosMovimento;
    @NaoControlarLogAlteracao
    protected List<MovParcelaVO> listaParcelas;
    @NaoControlarLogAlteracao
    protected String tipoTranferenciaCancelamento;
    @NaoControlarLogAlteracao
    protected ClienteVO cliente;
    @NaoControlarLogAlteracao
    protected ContratoVO contratoCreditadoNrDias;
    protected Date ajustadaContratoCreditadoAntiga;
    protected UsuarioVO responsavelCancelamento;
    @NaoControlarLogAlteracao
    protected Boolean quitacaoCancelamento;
    @NaoControlarLogAlteracao
    protected Boolean liberacaoCancelamento;
    @NaoControlarLogAlteracao
    protected Boolean apresentaListaCheque;
    @NaoControlarLogAlteracao
    protected Boolean apresentaListaChequeMovimento;
    protected UsuarioVO responsavelLiberacaoCancelamento;
    private double valorASerPagoPeloCliente;
    @NaoControlarLogAlteracao
    private boolean quitacaoManualCancelamento;
    @NaoControlarLogAlteracao
    private boolean devolucaoManualCancelamento;
    @NaoControlarLogAlteracao
    private boolean apresentaListaCartao;
    @NaoControlarLogAlteracao
    private boolean apresentaListaCartaoMovimento;
    @NaoControlarLogAlteracao
    private double valorASerDevolvidoRec;
    @NaoControlarLogAlteracao
    private boolean liberacaoDevolucao;
    private Double valorRecebiveis;
    private boolean alteracaoValorTrans;
    @NaoControlarLogAlteracao
    private boolean credito = false;
    @NaoControlarLogAlteracao
    private boolean alterarTipoCancelamento = false;
    @NaoControlarLogAlteracao
    private List <Integer> chequesComComposicao;
    @NaoControlarLogAlteracao
    private List <Integer> cartoesComComposicao;
    private List <ChequeVO> chequesCreditoCCUsado;  // cheque que foram para conta corrente do aluno e estão sendo usados por outro pagamento.
    private List <CartaoCreditoVO> cartoesCreditoCCUsado; // cartoes que foram para conta corrente do aluno e estão sendo usados por outro pagamento.
    private List <MovPagamentoVO>  pagamentosCredito; // credito não devolvidos;
    private ContratoOperacaoVO contratoOperacaoVO;
    private ContratoOperacaoVO comprovanteTransDiasVO;

    //CANCELAMENTO ANTECIPADO
    private List<MovParcelaVO> parcelasEmAtrasoCancelamentoAntecipado;
    private Double valorMultaRestanteCancelamentoAntecipado;
    private Double valorMultaSobreValorTotalContratoCancelamentoAntecipado;
    private Double valorMultaProximaParcelaCancelamentoAntecipado;
    private Integer movParcelaCancelamentoAntecipado;
    private boolean pagarProximaParcelaCancelamentoAntecipado = false;
    private Date dataFinalAcessoCancelamentoAntecipado;
    private Date dataParcelaGerarCancelamentoAntecipado;
    private String mensagemCancelamentoAntecipado;
    private String mensagemGravarOperacaoCancelamentoAntecipado;
    private boolean planoEspecialCancelamentoAntecipado = false;
    private Double valorTotalCancelamentoAntecipado;
    private Double valorTotalCancelamentoAntecipadoOutrosContratos;
    private List<CancelamentoAntecipadoTO> listaParcelasGerarCancelamentoAntecipado;
    private String informacoesCancelamentoAntecipado;

    private double valorDevolverSaldoTransferenciaCredito = 0;
    private ContratoVO contratoTransferenciaSaldo;

    private TipoParcelaCancelamento tipoParcelaCancelamento = TipoParcelaCancelamento.TODAS_PARCELAS;
    private boolean cancelarSaldoContaCorrente = true;
    private ContratoVO contratoCancelar;
    boolean cancelarParcelaAnuidade = false;
    private boolean contratoVendaCredito = false;
    private CancelamentoProporcionalTO cancelamentoProporcional;
    private boolean cancelamentoProporcionalValidar = false;

    private CancelamentoAvaliandoParcelaTO cancelamentoAvaliandoParcelaTO;

    private List<MovParcelaVO> parcelasVencidasCanceladas = new ArrayList<MovParcelaVO>();

    private int empresaDestinoTransferencia;
    private String nomePlanoOrigem;
    private String nomePlanoDestino;

    private Boolean configuracaoSesc = false;
    private Double valorEmAberto;
    private JustificativaOperacaoVO justificativaOperacao;
    @NaoControlarLogAlteracao
    private double valorBaseCalculoMultaCancelamento =0;

    private Boolean transferenciaEmpresaClientePlano = false;

    private Boolean cancelamentoAutomatico = false;

    public CancelamentoContratoVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setDataCancelamento(Calendario.hoje());
        setNrDiasContrato(0);
        setNrDiasUtilizadosPeloClienteContrato(0);
        setNrDiasRestamContrato(0);
        setNrDiasCreditarNoContrato(0.0);
        setValorBaseContrato(0.0);
        setValorTotalBaseContratoRenovacao(0.0);
        setValorMensalContrato(0.0);
        setValorDiaContratoValorBase(0.0);
        setValorDiaContratoValorMensal(0.0);
        setValorCreditoRestanteContratoValorBase(0.0);
        setValorCreditoRestanteContratoValorMensal(0.0);
        setValorCreditoRestanteContratoValorMensalSemTaxaEMulta(0.0);
        setValorCreditoRestanteContratoVendaCreditoSemTaxaEMulta(0.0);
        setValorTaxaCancelamento(0.0);
        setSaldoContaCorrenteCliente(0.0);
        setPercentualMultaCancelamento(0.0);
        setValorSerRecebidoPeloCliente(0.0);
        setValorTotalPagoPeloCliente(0.0);
        setValorASerDevolvido(0.0);
        setValorASerDevolvidoBaseCalculo(0.0);
        setValorASerPagoPeloCliente(0.0);
        setSomaValorPagoPeloClienteComCheque(0.0);
        setValorTotalSomaProdutoContratos(0.0);
        setValorRecebiveis(0.0);
        setTipoDevolucaoCancelamento("");
        setTipoTranferenciaCancelamento("");
        setMensagemDevolucao("");
        setMensagemTransferencia("");
        setObservacao("");
        setTipoJustificativaOperacao(0);
        setMensagemErro(false);
        setListaContratos(new ArrayList<ContratoVO>());
        setListaPagamentos(new ArrayList<MovPagamentoVO>());
        setListaPagamentosMovimento(new ArrayList<MovPagamentoVO>());
        setListaParcelas(new ArrayList<MovParcelaVO>());
        setMovProdutoDevolucao(new MovProdutoVO());
        setMovProdutoDevolucaoRecebiveis(new MovProdutoVO());
        setCliente(new ClienteVO());
        setContratoCreditadoNrDias(new ContratoVO());
        setResponsavelCancelamento(new UsuarioVO());
        setResponsavelLiberacaoCancelamento(new UsuarioVO());
        setQuitacaoCancelamento(false);
        setLiberacaoCancelamento(false);
        setApresentaListaCheque(false);
        setApresentaListaChequeMovimento(false);
        setApresentaListaCartaoMovimento(false);
        setApresentaListaCartao(false);
        setDevolucaoManualCancelamento(false);
        setValorQuitacaoCancelamento(0.0);
        setValorDevolucaoCancelamento(0.0);
        setValorUtilizadoPeloClienteBase(0.0);
        setValorUtilizadoPeloClienteMensal(0.0);
        setMovProdutoQuitacao(new MovProdutoVO());
        setNrDiasOperacao(0);
        setQuitacaoManualCancelamento(false);
        setLiberacaoDevolucao(false);
        setAlteracaoValorTrans(false);
        setChequesComComposicao(new ArrayList<Integer>());
        setCartoesComComposicao(new ArrayList<Integer>());
        setCartoesCreditoCCUsado(new ArrayList<CartaoCreditoVO>());
        setChequesCreditoCCUsado(new ArrayList<ChequeVO>());
        setPagamentosCredito(new ArrayList<MovPagamentoVO>());
        setMensagemCancelamentoAntecipado("");
        setValorMultaRestanteCancelamentoAntecipado(0.0);
        setTransferenciaEmpresaClientePlano(false);
        
    }

    public void validarDados(CancelamentoContratoVO obj, String numeroTela, ContratoVO contrato, boolean alterouCancelamento) throws ConsistirException, ParseException {

        if (numeroTela.equals("telaCancelamento") || numeroTela.equals("telaCancelamentoBolsa")) {
            if (obj.getDataCancelamento() == null) {
                throw new ConsistirException("O campo DATA CANCELAMENTO (Cancelamento) deve ser informado.");
            }
            if (Uteis.getCompareData(obj.getDataCancelamento(), contrato.getVigenciaDe()) == 0) {
                throw new ConsistirException("O campo DATA CANCELAMENTO (Cancelamento) não pode ser na mesma data que começa o contrato.");
            }
            if (Uteis.getCompareData(obj.getDataCancelamento(), contrato.getVigenciaDe()) < 0) {
                throw new ConsistirException("O campo DATA CANCELAMENTO (Cancelamento) não pode ser antes da data que começa o contrato.");
            }
            if (Uteis.getCompareData(obj.getDataCancelamento(), contrato.getVigenciaAteAjustada()) > 0 && !obj.isCancelamentoProporcionalValidar()) {
                throw new ConsistirException("O campo DATA CANCELAMENTO (Cancelamento) não pode ser Depois da data que Termina o contrato.");
            }
            if (obj.getTipoJustificativaOperacao() == null || obj.getTipoJustificativaOperacao() == 0) {
                throw new ConsistirException("O campo TIPO DA JUSTIFICATIVA OPERAÇÃO (Cancelamento) deve ser informado.");
            }
        }else if (numeroTela.equals("telaCancelamentoCalculo")) {
            if (obj.getTipoDevolucaoCancelamento().equals("")) {
                throw new ConsistirException("A forma de pagamento (Transferência ou Devolução) do Crédito Restante deve ser informado.");
            }
        } else if (numeroTela.equals("telaCancelamentoListaCheque")) {
            if (obj.getValorASerDevolvidoBaseCalculo() < 0) {
                double valorPositivo = Uteis.arredondarForcando2CasasDecimais(obj.getValorASerDevolvidoBaseCalculo() * -1);
                if (obj.getValorQuitacaoCancelamento() == 0 && !obj.getLiberacaoCancelamento() && !alterouCancelamento) {
                    throw new ConsistirException("Valor a ser cobrado deve ser maior que zero ou selecione a opção \"Não Cobrar do Cliente\"");
                }
                if (obj.isQuitacaoManualCancelamento()) {
                    if (obj.getValorQuitacaoCancelamento() < 0) {
                        throw new ConsistirException("Não é possível realizar a operação (VALOR DE QUITAÇÃO NÃO PODE SER NEGATIVO)");
                    }
                    if (obj.getResponsavelLiberacaoCancelamento() == null || obj.getResponsavelLiberacaoCancelamento().getCodigo().intValue() == 0) {
                        throw new ConsistirException("Não é possível finalizar o Cancelamento, pois o Responsável Quitação Manual do Cancelamento não foi informado.");
                    }
                } else {
                    if (obj.getValorQuitacaoCancelamento() > 0 && Uteis.arredondarForcando2CasasDecimais(obj.getValorQuitacaoCancelamento()) < valorPositivo) {
                        throw new ConsistirException("Não é possível finalizar o Cancelamento, pois o VALOR DE QUITAÇÃO é menor que o  VALOR A SER DEVOLVIDO.");
                    }
                    if (obj.getLiberacaoCancelamento() && (obj.getResponsavelLiberacaoCancelamento() == null || obj.getResponsavelLiberacaoCancelamento().getCodigo().intValue() == 0)) {
                        throw new ConsistirException("Não é possível finalizar o Cancelamento, pois o Responsável Liberação Cancelamento não foi informado.");
                    }
                }
            }
        } else if (numeroTela.equals("telaTransferencia")) {
            if (obj.getTipoTranferenciaCancelamento().equals("")) {
                throw new ConsistirException("Uma Opção de Transfêrencia deve ser selecionada.");
            }

            if (getValorASerDevolvido().intValue() < 0) {
                throw new ConsistirException("Não é possível realizar a operação (VALOR A SER TRANSFERIDO NÃO PODE SER NEGATIVO)");
            }
            if (getValorASerDevolvido().intValue() == 0) {
            	throw new ConsistirException("Não é possível realizar a operação pois o contrato não tem crédito para transferir. O indicado é fazer o cancelamento com Devolução");
            }

        } else if (numeroTela.equals("telaTransferenciaCliente")) {
            if (getCliente() == null || getCliente().getPessoa().getNome().equals("")) {
                throw new ConsistirException("Campo NOME DE CLIENTE deve ser informado.");
            }
        }
    }

    public ContratoOperacaoVO inicializarDadosOperacaoContratoTransferirDias(ContratoVO contrato, UsuarioVO usuario) throws Exception {
        try {
            ContratoOperacaoVO operacao = new ContratoOperacaoVO();
            operacao.setDescricaoCalculo(getMensagemDevolucao() + "\n\r" + getNrDiasCreditarNoContrato().intValue() + " dias Transferido  para o cliente: " + getCliente().getPessoa().getNome());
            operacao.setTipoOperacao("TS");
            operacao.setContrato(contrato.getCodigo().intValue());
            operacao.setClienteTransfereDias(getFacade().getCliente().consultarPorCodigoPessoa(contrato.getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            operacao.setClienteRecebeDias(getCliente());
            operacao.setObservacao(getObservacao());
            operacao.setResponsavel(usuario);
            operacao.setOperacaoPaga(false);
            operacao.setDataOperacao(Calendario.hoje());
            operacao.setDataFimEfetivacaoOperacao(Calendario.hoje());
            operacao.setDataInicioEfetivacaoOperacao(Calendario.hoje());
            return operacao;
        } catch (Exception e) {
            throw e;
        }
    }

    public ContratoOperacaoVO inicializarDadosOperacaoContratoReceberDias(ContratoVO contrato, UsuarioVO usuario) throws Exception {
        try {
            ContratoOperacaoVO operacao = new ContratoOperacaoVO();
            operacao.setDescricaoCalculo("" + getNrDiasCreditarNoContrato().intValue() + " dias Recebido do cliente: " + contrato.getPessoa().getNome());
            operacao.setTipoOperacao("TE");
            operacao.setNrDiasOperacao(getNrDiasCreditarNoContrato().intValue());
            operacao.setContrato(getContratoCreditadoNrDias().getCodigo().intValue());
            operacao.setClienteTransfereDias(getFacade().getCliente().consultarPorCodigoPessoa(contrato.getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            operacao.setClienteRecebeDias(getCliente());
            operacao.setObservacao(getObservacao());
            operacao.setResponsavel(usuario);
            operacao.setOperacaoPaga(false);
            operacao.setDataOperacao(Calendario.hoje());
            operacao.setDataFimEfetivacaoOperacao(Calendario.hoje());
            operacao.setDataInicioEfetivacaoOperacao(Calendario.hoje());
            //prorrogar também o histórico de contrato atual
            getFacade().getHistoricoContrato().excluirHistoricoAVencerContrato(operacao.getContrato());
            HistoricoContratoVO hist = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(operacao.getContrato(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            
            if (hist != null && !hist.getTipoHistorico().equals("TR")) {
                hist.setDataFinalSituacao(getContratoCreditadoNrDias().getVigenciaAteAjustada());
                getFacade().getHistoricoContrato().alterarSemCommit(hist, false);
            }
            return operacao;
        } catch (Exception e) {
            throw e;
        }
    }

    public void obterNrDiasAdicionarContratoClienteCreditado() throws Exception {
        try {
            
            this.setValorDiaContratoCreditadoValorBase(getFacade().getZWFacade().obterValorDiaContratoValorBase(getContratoCreditadoNrDias()));
            Double valorDiaContratoClienteCreditado = getValorDiaContratoCreditadoValorBase();
            if( getValorDiaContratoCreditadoValorBase() < getValorDiaContratoValorBase()){
                valorDiaContratoClienteCreditado = getValorDiaContratoValorBase();
            }
            if (valorDiaContratoClienteCreditado > 0.0) {
                setNrDiasCreditarNoContrato(getValorASerDevolvidoBaseCalculo() / valorDiaContratoClienteCreditado);
            } else {
                setNrDiasCreditarNoContrato(0.0);
            }
            getContratoCreditadoNrDias().setDataPrevistaRematricula(Uteis.obterDataFutura2(getContratoCreditadoNrDias().getVigenciaAteAjustada(), (getNrDiasCreditarNoContrato().intValue() + 1)));
            getContratoCreditadoNrDias().setDataPrevistaRenovar(Uteis.obterDataFutura2(getContratoCreditadoNrDias().getVigenciaAteAjustada(), (getNrDiasCreditarNoContrato().intValue() + 1)));
            getContratoCreditadoNrDias().setVigenciaAteAjustada(Uteis.obterDataFutura2(getContratoCreditadoNrDias().getVigenciaAteAjustada(), (getNrDiasCreditarNoContrato().intValue() + 1)));
            getFacade().getContrato().alterarDatasVigenciaContrato(getContratoCreditadoNrDias());
        } catch (Exception e) {
            if(getContratoCreditadoNrDias().getCodigo() == 0){
                throw new Exception("Informe o contrato que irá receber os dias.");
            }
            throw e;
        }


    }

    public void inicializarDadosPeriodoAcessoClienteRecebeDias(UsuarioVO usuario) throws Exception {
        try {
            List lista = new ArrayList();
            Double valorContratoClienteCreditado = 0.0;
            lista = getFacade().getPeriodoAcessoCliente().consultarPorContrato(getContratoCreditadoNrDias().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (lista.size() > 0) {
                valorContratoClienteCreditado = getFacade().getZWFacade().obterValorDiaContratoValorBase(getContratoCreditadoNrDias());
                if (valorContratoClienteCreditado > getValorDiaContratoValorBase() || valorContratoClienteCreditado < getValorDiaContratoValorBase()) {
                    gerarNumeroDiasContrato(valorContratoClienteCreditado, usuario);
                } else {
                    valorContratoIgualGerarNumeroDiasContrato(true);
                }
            } else {
                valorContratoIgualGerarNumeroDiasContrato(false);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void gerarNumeroDiasContrato(Double valorDiaContrato, UsuarioVO usuario) throws Exception {
        try {

            PeriodoAcessoClienteVO periodoAcesso = getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoContrato(getContratoCreditadoNrDias().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PeriodoAcessoClienteVO novoPeriodo = new PeriodoAcessoClienteVO();
            novoPeriodo.setDataInicioAcesso(Uteis.obterDataFutura2(periodoAcesso.getDataFinalAcesso(), 1));
            novoPeriodo.setDataFinalAcesso(Uteis.obterDataFutura2(novoPeriodo.getDataInicioAcesso(), getNrDiasCreditarNoContrato().intValue()));
            novoPeriodo.setPessoa(getCliente().getPessoa().getCodigo().intValue());
            novoPeriodo.setContrato(getContratoCreditadoNrDias().getCodigo().intValue());
            novoPeriodo.setTipoAcesso("TD");
            getFacade().getPeriodoAcessoCliente().incluirSemCommit(novoPeriodo);
           
        } catch (Exception e) {
            throw e;
        }
    }

    public void valorContratoIgualGerarNumeroDiasContrato(Boolean existeContrato) throws Exception {
        try {
            PeriodoAcessoClienteVO novoPeriodo = new PeriodoAcessoClienteVO();
            if (existeContrato) {
                PeriodoAcessoClienteVO periodoAcesso = getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoContrato(getContratoCreditadoNrDias().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                novoPeriodo.setDataInicioAcesso(Uteis.obterDataFutura2(periodoAcesso.getDataFinalAcesso(), 1));
                novoPeriodo.setDataFinalAcesso(Uteis.obterDataFutura2(novoPeriodo.getDataInicioAcesso(), getNrDiasRestamContrato()));
            } else {
                PeriodoAcessoClienteVO periodoAcesso = getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoPessoa(getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, false, false);
                if (periodoAcesso != null) {
                    novoPeriodo.setDataInicioAcesso(Uteis.obterDataFutura2(periodoAcesso.getDataFinalAcesso(), 1));
                    novoPeriodo.setDataFinalAcesso(Uteis.obterDataFutura2(novoPeriodo.getDataInicioAcesso(), getNrDiasRestamContrato()));
                } else {
                    novoPeriodo.setDataInicioAcesso(Calendario.hoje());
                    novoPeriodo.setDataFinalAcesso(Uteis.obterDataFutura2(Calendario.hoje(), getNrDiasRestamContrato()));
                }
            }
            novoPeriodo.setContrato(getContratoCreditadoNrDias().getCodigo().intValue());
            novoPeriodo.setPessoa(getCliente().getPessoa().getCodigo().intValue());
            novoPeriodo.setTipoAcesso("TD");
            getFacade().getPeriodoAcessoCliente().incluirSemCommit(novoPeriodo);
        } catch (Exception e) {
            throw e;
        }
    }

    public void gerarMovProdutoQuitacaoCancelamento(ContratoVO contrato, EmpresaVO empresa, UsuarioVO usuario, ProdutoVO produto) throws Exception {
        if(produto == null){
            produto = getFacade().getProduto().consultarPorTipoProduto("QU", true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        if (produto.getCodigo() == 0) {
            throw new Exception("Não foi possível realizar essa operação de cancelamento, pois não existe um produto do tipo QUITAÇÃO DE CANCELAMENTO cadastrado.");
        } else {

            double valorReduzir = 0.0;
            for (MovParcelaVO parcela : getListaParcelas()) {
                if (!parcela.getSituacao().equals("EA")) {
                    continue;
                }

                if (getTipoParcelaCancelamento().equals(TipoParcelaCancelamento.MAIOR_IGUAL_MES_ATUAL)) {
                    if (!Calendario.vencimentoMaiorOuIgualAoMesAtual(parcela.getDataVencimento())){
                        valorReduzir += parcela.getValorParcela();
                        continue;
                    }
                }

                if (getTipoParcelaCancelamento().equals(TipoParcelaCancelamento.MAIOR_IGUAL)) {
                    if (!Calendario.maior(parcela.getDataVencimento(), getDataCancelamento()) && !(contrato.getContratoRecorrenciaVO().isCancelamentoProporcional() && parcela.getDescricao().contains("ANUIDADE"))) {
                        valorReduzir += parcela.getValorParcela();
                    }
                }
            }

            getMovProdutoQuitacao().setEmpresa(empresa);
            getMovProdutoQuitacao().setAnoReferencia(Uteis.getAnoData(Calendario.hoje()));
            getMovProdutoQuitacao().setContrato(contrato);
            getMovProdutoQuitacao().setDataLancamento(Calendario.hoje());
            getMovProdutoQuitacao().setMesReferencia(Uteis.getMesReferenciaData(Calendario.hoje()));
            if (contrato.getPessoaOriginal() != null && !UteisValidacao.emptyNumber(contrato.getPessoaOriginal().getCodigo())) {
                getMovProdutoQuitacao().setPessoa(contrato.getPessoaOriginal());
            } else {
                getMovProdutoQuitacao().setPessoa(contrato.getPessoa());
            }
            getMovProdutoQuitacao().setProduto(produto);
            getMovProdutoQuitacao().setDescricao(getMovProdutoQuitacao().getProduto().getDescricao());
            getMovProdutoQuitacao().setQuantidade(1);
            getMovProdutoQuitacao().setSituacao("EA");
            getMovProdutoQuitacao().setQuitado(true);
            getMovProdutoQuitacao().setResponsavelLancamento(usuario);
            getMovProdutoQuitacao().setValorDesconto(0.0);
            if (isQuitacaoManualCancelamento() || getQuitacaoCancelamento()) {

                if (valorReduzir >= getValorQuitacaoCancelamento()) {
                    getMovProdutoQuitacao().setSituacao("PG");
                    getMovProdutoQuitacao().setTotalFinal(0.0);
                    getMovProdutoQuitacao().setPrecoUnitario(0.0);
                    setValorQuitacaoCancelamento(0.0);
                } else {
                    getMovProdutoQuitacao().setTotalFinal(getValorQuitacaoCancelamento() - valorReduzir);
                    getMovProdutoQuitacao().setPrecoUnitario(getValorQuitacaoCancelamento() - valorReduzir);
                    setValorQuitacaoCancelamento(getValorQuitacaoCancelamento() - valorReduzir);
                }
            } else {
                getMovProdutoQuitacao().setTotalFinal(getValorASerDevolvidoBaseCalculo() * -1);
                getMovProdutoQuitacao().setPrecoUnitario(getValorASerDevolvidoBaseCalculo() * -1);
            }
        }

    }

    public void obterValorFinalASerDevolvido(String tipoCancelamento, ContratoVO contrato) {
        if (tipoCancelamento.equals("DE") && contrato.getEmpresa().isRetrocederValorMensalPlanoCancelamento()) {
            setValorASerDevolvido(getValorTotalPagoPeloCliente() - getValorUtilizadoPeloClienteMensal() - getValorTaxaCancelamento() - getValorMensalComBaseNoPercentual() - getValorTotalSomaProdutoContratos());
        } else if (tipoCancelamento.equals("DE") && !contrato.getEmpresa().isRetrocederValorMensalPlanoCancelamento()) {
            setValorASerDevolvido(getValorTotalPagoPeloCliente() - getValorUtilizadoPeloClienteMensal() - getValorTaxaCancelamento() - getValorMensalComBaseNoPercentual() - getValorTotalSomaProdutoContratos());
        } else {
            setValorASerDevolvido(getValorTotalPagoPeloCliente() - getValorUtilizadoPeloClienteBase() - getValorTotalSomaProdutoContratos());
        }
        if (getValorTotalBaseContratoRenovacao() != 0) {
            this.valorASerDevolvido = this.valorASerDevolvido + getValorTotalBaseContratoRenovacao();
        }
        setValorASerDevolvidoRec(getValorASerDevolvido());
        this.valorASerDevolvido = (Uteis.arredondarForcando2CasasDecimaisMantendoSinal(this.valorASerDevolvido));
        setValorASerDevolvidoBaseCalculo(getValorASerDevolvido() - getValorDevolverSaldoTransferenciaCredito());
    }

    public void obterValorFinalASerDevolvidoComCheque() {
        valorASerPagoPeloCliente = 0.0;
        if (isDevolucao()) {
            valorASerPagoPeloCliente = getValorUtilizadoPeloClienteMensal() + getValorTaxaCancelamento() + getValorMensalComBaseNoPercentual() + getValorTotalSomaProdutoContratos();
        } else {
            valorASerPagoPeloCliente = getValorUtilizadoPeloClienteBase() + getValorTaxaCancelamento() + getValorMensalComBaseNoPercentual() + getValorTotalSomaProdutoContratos();
        }
        if (getValorTotalBaseContratoRenovacao().doubleValue() > 0) {
            setSomaValorPagoPeloClienteComCheque(getSomaValorPagoPeloClienteComCheque() + getValorTotalBaseContratoRenovacao());
        }
        // validando se o saldo do cliente e negativo
        if (getSaldoContaCorrenteCliente() < 0) {
            // saldo ficar com valor positivo para realizar o calculo da soma.
            double valorSaldo = getSaldoContaCorrenteCliente() * -1;
            valorASerPagoPeloCliente = valorASerPagoPeloCliente + valorSaldo;
        } else {
            setSomaValorPagoPeloClienteComCheque(getSomaValorPagoPeloClienteComCheque() + getSaldoContaCorrenteCliente());
        }
        valorASerPagoPeloCliente = Uteis.arredondarForcando2CasasDecimais(valorASerPagoPeloCliente);
        if (getSomaValorPagoPeloClienteComCheque() < valorASerPagoPeloCliente) {
            setValorASerDevolvido((valorASerPagoPeloCliente - getSomaValorPagoPeloClienteComCheque()) * -1);
            setValorASerDevolvidoBaseCalculo((valorASerPagoPeloCliente - getSomaValorPagoPeloClienteComCheque()) * -1);
        } else {
            setValorASerDevolvido(getSomaValorPagoPeloClienteComCheque() - valorASerPagoPeloCliente);
            setValorASerDevolvidoBaseCalculo(getSomaValorPagoPeloClienteComCheque() - valorASerPagoPeloCliente);
        }
    }

    private List<Integer> codigosContratos(){
        List<Integer> codigos = new ArrayList<Integer>();
        for(ContratoVO contrato : listaContratos){
            codigos.add(contrato.getCodigo());
        }
        return codigos;
    }
    
    public void obterPagamentoEfetuado() throws Exception {
        List<Integer> codigosContratos = codigosContratos();
        List<PagamentoMovParcelaVO> lista = new ArrayList<>();
        List<PagamentoMovParcelaVO> lista1 = new ArrayList<>();
        setSomaValorPagoPeloClienteComCheque(0.0);
        for (MovParcelaVO parcela : getListaParcelas()) {
            lista = getFacade().getPagamentoMovParcela().consultarPorCodigoMovParcela(parcela.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (parcela.getSituacao().equals("PG")) {
                List<MovProdutoParcelaVO> listaMovProdutosParcelas = getFacade().getMovProdutoParcela().consultarPorCodigoMovParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for(MovProdutoParcelaVO prodParcela : listaMovProdutosParcelas){
                    MovProdutoVO prod = getFacade().getMovProduto().consultarPorChavePrimaria(prodParcela.getMovProduto(),Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if(!prod.getSituacao().equals("CA") && (UteisValidacao.emptyNumber(prod.getContrato().getCodigo()) || codigosContratos.contains(prod.getContrato().getCodigo()))){
                        setSomaValorPagoPeloClienteComCheque(getSomaValorPagoPeloClienteComCheque() + prodParcela.getValorPago());
                    }
                }
            }
            if (lista1.isEmpty()) {
                lista1 = lista;
            } else {
                Iterator i = lista.iterator();
                while (i.hasNext()) {
                    PagamentoMovParcelaVO pagamentoMovParcela = (PagamentoMovParcelaVO) i.next();
                    lista1.add(pagamentoMovParcela);
                }
            }
        }
        for (PagamentoMovParcelaVO pagamentoMovParcela : lista1) {
            MovPagamentoVO obj = getFacade().getMovPagamento().consultarPorChavePrimaria(pagamentoMovParcela.getMovPagamento(), Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO);
            adicionarObjMovPagamentoVOs(obj);
        }
        Ordenacao.ordenarLista(getListaPagamentos(), "codigo");
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>ClienteGrupoVO</code>
     * ao List <code>clienteGrupoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ClienteGrupo</code> - getGrupo().getCodigo() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>ClienteGrupoVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjMovPagamentoVOs(MovPagamentoVO obj) throws Exception {
        int index = 0;
        Iterator i = getListaPagamentos().iterator();
        while (i.hasNext()) {
            MovPagamentoVO objExistente = (MovPagamentoVO) i.next();
            if (objExistente.getCodigo().equals(obj.getCodigo())) {
                getListaPagamentos().set(index, obj);
                return;
            }
            index++;
        }
        getListaPagamentos().add(obj);
    }

    public Boolean validarSeExistePagamentoComCheque(List recibos, Integer contrato) throws Exception {
        try {
            Boolean apresentarListaCheque = false;
            for (MovPagamentoVO movPagamento : getListaPagamentos()) {
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                	Double valorPagoContrato = 0.0;
                	boolean pagaOutroContrato = false;
                	if (!recibos.isEmpty()){
                		Iterator i = recibos.iterator();
                		while (i.hasNext()){
                			ReciboPagamentoTO recibo = (ReciboPagamentoTO) i.next();
                			if (movPagamento.getReciboPagamento().getCodigo().intValue() == recibo.getCodigo()){
                				valorPagoContrato = getFacade().getPagamentoMovParcela().consultarValorPagoContratoPagamento(contrato, movPagamento.getCodigo());
                				if (valorPagoContrato < movPagamento.getValorTotal()){
                					pagaOutroContrato = true;
                                                        for(ChequeVO ch : movPagamento.getChequeVOs()){
                                                            if (ch.getSituacao().equals("EA")) {
                                                                continue;
                                                            }
                                                            String[] split = ch.getProdutosPagos().split("\\|");
                                                            for (String str : split) {
                                                                if (!str.isEmpty() && !str.equals("null")) {
                                                                    String[] produto= str.split(",");
                                                                    if(contrato.equals(Integer.parseInt(produto[2])) && TipoProduto.DEVOLUCAO_DE_RECEBIVEIS.getCodigo().equals(produto[1])){
                                                                        valorPagoContrato = Uteis.arredondarForcando2CasasDecimais(valorPagoContrato - Double.parseDouble(produto[3]));
                                                                    }
                                                                }
                                                            }
                                                            
                                                        }
                				}
                				break;
                			}
                			
                		}
                	}
                    movPagamento.setMovPagamentoEscolhida(true);
                    movPagamento.setChequeVOs(Ordenacao.ordenarLista(movPagamento.getChequeVOs(), "dataCompensacao"));
                	Collections.reverse(movPagamento.getChequeVOs());
                    Iterator i = movPagamento.getChequeVOs().iterator();
                    while (i.hasNext()) {
                        ChequeVO cheque = (ChequeVO) i.next();
                      
                        // todos os cheques já serão marcados como compensar
                        cheque.setChequeCompensar(true);
                    	if (pagaOutroContrato && cheque.getSituacao().equals("EA")){
                    		valorPagoContrato = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorPagoContrato - cheque.getValor());
                    		if(Uteis.getCompareData(cheque.getDataCompensacao(), getDataCancelamento()) > 0 && !chequesComComposicao.contains(cheque.getCodigo())){
                    			apresentarListaCheque = true;
                    			cheque.setApresentarCheque(true);
                                        if(cheque.getTemComposicao()){
                                            cheque.setValor(cheque.getValorTotal());
                                            cheque.setAvisoVinculos(getFacade().getCheque().obterAvisosVinculos(cheque.getComposicao(), null, false));
                                            String[]composicoes = cheque.getComposicao().split(",");
                                            for (String str : composicoes) {
                                                chequesComComposicao.add(Integer.parseInt(str));
                                            }
                                        }
                    			if(valorPagoContrato < 0.00){
                    				cheque.setPagaOutroContrato(true);
                    			}
                    		} else {
                    			cheque.setApresentarCheque(false);
                    		}
                        		
                        } else {
       
	                        if (Uteis.getCompareData(cheque.getDataCompensacao(), getDataCancelamento()) > 0 && cheque.getSituacao().equals("EA") && !chequesComComposicao.contains(cheque.getCodigo()) ) {
	                            cheque.setApresentarCheque(true);
	                            apresentarListaCheque = true;
                                    if(cheque.getTemComposicao()){
                                            cheque.setValor(cheque.getValorTotal());
                                            cheque.setAvisoVinculos(getFacade().getCheque().obterAvisosVinculos(cheque.getComposicao(),null, false));
                                            String[]composicoes = cheque.getComposicao().split(",");
                                            for (String str : composicoes) {
                                                chequesComComposicao.add(Integer.parseInt(str));
                                            }
                                      }
	                        } else {
	                            cheque.setApresentarCheque(false);
	                        }
                        }
                    }
                    Collections.reverse(movPagamento.getChequeVOs());
                } else if (!movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    movPagamento.setMovPagamentoEscolhida(false);
                }
            }
            return apresentarListaCheque;
        } catch (Exception e) {
            throw e;
        }
    }
     public Boolean validarSeExistePagamentoComChequeMovimento() throws Exception {
        try {
            Boolean apresentarListaCheque = false;
            for (MovPagamentoVO movPagamento : getListaPagamentosMovimento()) {
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    movPagamento.setMovPagamentoEscolhida(true);
                    movPagamento.setChequeVOs(Ordenacao.ordenarLista(movPagamento.getChequeVOs(), "dataCompensacao"));
                	Collections.reverse(movPagamento.getChequeVOs());
                    Iterator i = movPagamento.getChequeVOs().iterator();
                    while (i.hasNext()) {
                        ChequeVO cheque = (ChequeVO) i.next();

                        // todos os cheques já serão marcados como compensar
                        cheque.setChequeCompensar(true);
                        if (Uteis.getCompareData(cheque.getDataCompensacao(), getDataCancelamento()) > 0 && !cheque.getSituacao().equals("CA") ) {
                            cheque.setApresentarCheque(true);
                        } else {
                            cheque.setApresentarCheque(false);
                        }
                        if (!cheque.getTemComposicao() || !chequesComComposicao.contains(cheque.getCodigo())){
                            apresentarListaCheque = true;
                        }
                    }
                    Collections.reverse(movPagamento.getChequeVOs());
                } else if (!movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    movPagamento.setMovPagamentoEscolhida(false);
                }
            }
            return apresentarListaCheque;
        } catch (Exception e) {
            throw e;
        }
    }

    public Boolean validarSeExistePagamentoComCartao(List recibos, Integer contrato) throws Exception {
        try {
            boolean apresentarListaCartao = false;
            for (MovPagamentoVO movPagamento : getListaPagamentos()) {
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                	Double valorPagoContrato = 0.0;
                	boolean pagaOutroContrato = false;
                	if (!recibos.isEmpty()){
                		Iterator i = recibos.iterator();
                		while (i.hasNext()){
                			ReciboPagamentoTO recibo = (ReciboPagamentoTO) i.next();
                			if (movPagamento.getReciboPagamento().getCodigo().intValue() == recibo.getCodigo()){
                				valorPagoContrato = Uteis.arredondarForcando2CasasDecimais(getFacade().getPagamentoMovParcela().consultarValorPagoContratoPagamento(contrato, movPagamento.getCodigo()));
                				if (valorPagoContrato < Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorTotal())){
                					pagaOutroContrato = true;
                                                        for(CartaoCreditoVO ca : movPagamento.getCartaoCreditoVOs()){
                                                            if (ca.getSituacao().equals("EA")) {
                                                                continue;
                                                            }
                                                            String[] split = ca.getProdutosPagos().split("\\|");
                                                            for (String str : split) {
                                                                if (!str.isEmpty() && !str.equals("null")) {
                                                                    String[] produto= str.split(",");
                                                                    if(contrato.equals(Integer.parseInt(produto[2])) && TipoProduto.DEVOLUCAO_DE_RECEBIVEIS.getCodigo().equals(produto[1])){
                                                                        valorPagoContrato = Uteis.arredondarForcando2CasasDecimais(valorPagoContrato - Double.parseDouble(produto[3]));
                                                                    }
                                                                }
                                                            }
                                                            
                                                        }
                				}
                				break;
                			}
                			
                		}
                	}
                        if(recibos.isEmpty() || !pagaOutroContrato) {
                            valorPagoContrato = Uteis.arredondarForcando2CasasDecimais(movPagamento.getValorTotal());
                        }
                    movPagamento.setValorPagaContrato(valorPagoContrato);
                    movPagamento.setMovPagamentoEscolhida(true);
                    movPagamento.setCartaoCreditoVOs(Ordenacao.ordenarLista(movPagamento.getCartaoCreditoVOs(), "dataCompensacao"));
                  	Collections.reverse(movPagamento.getCartaoCreditoVOs());
                    Iterator i = movPagamento.getCartaoCreditoVOs().iterator();
                    while (i.hasNext()) {
                        CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
                        // todos os cheques já serão marcados como compensar
                        cartao.setCartaoCompensar(true);
                        if (Uteis.getCompareData(cartao.getDataCompensacao(), getDataCancelamento()) > 0 && !cartao.getSituacao().equals("CA") && !cartoesComComposicao.contains(cartao.getCodigo()) ) {
                            if(cartao.getTemComposicao()){
                                    if(!cartoesComComposicao.contains(cartao.getCodigo())){
                                        cartao.setApresentarCartao(true);
                                        apresentarListaCartao = true;
                                        cartao.setValor(cartao.getValorTotal());
                                        cartao.setAvisoVinculos(getFacade().getCartaoCredito().obterAvisosVinculos(cartao.getComposicao()));
                                        String[]composicoes = cartao.getComposicao().split(",");
                                        for (String str : composicoes) {
                                            cartoesComComposicao.add(Integer.parseInt(str));
                                        }
                                    } else {
                                        cartao.setApresentarCartao(false);
                                        cartao.setAvisoVinculos("Cartão já está na lista de cartões");
                                    }
                            } else {
                                cartao.setApresentarCartao(true);
                                apresentarListaCartao = true;
                            }
                        } else {
                            cartao.setApresentarCartao(false);
                        }
                    }
                    Collections.reverse(movPagamento.getCartaoCreditoVOs());
                } else if (!movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    movPagamento.setMovPagamentoEscolhida(false);
                }
            }
            return apresentarListaCartao;
        } catch (Exception e) {
            throw e;
        }
    }

     public Boolean validarSeExistePagamentoComCartaoMovimento() throws Exception {
        try {
            boolean apresentarListaCartao = false;
            for (MovPagamentoVO movPagamento : getListaPagamentosMovimento()) {
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {

                    movPagamento.setMovPagamentoEscolhida(true);
                    movPagamento.setCartaoCreditoVOs(Ordenacao.ordenarLista(movPagamento.getCartaoCreditoVOs(), "dataCompensacao"));
                  	Collections.reverse(movPagamento.getCartaoCreditoVOs());
                    Iterator i = movPagamento.getCartaoCreditoVOs().iterator();
                    boolean adicionar;
                    while (i.hasNext()) {
                        adicionar = true;
                        CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
                        // todos os cheques já serão marcados como compensar
                        cartao.setCartaoCompensar(true);

                        if (Uteis.getCompareData(cartao.getDataCompensacao(), getDataCancelamento()) > 0 && !cartao.getSituacao().equals("CA") ) {
                            cartao.setApresentarCartao(true);
                        } else {
                            cartao.setApresentarCartao(false);
                        }
                        if (!cartao.getTemComposicao() || !cartoesComComposicao.contains(cartao.getCodigo())){
                            apresentarListaCartao = true;
                        }
                    }
                    Collections.reverse(movPagamento.getCartaoCreditoVOs());
                } else if (!movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    movPagamento.setMovPagamentoEscolhida(false);
                }
            }
            return apresentarListaCartao;
        } catch (Exception e) {
            throw e;
        }
    }

    public Double obterValorChequesDevolvidos() {
        Double valorCheque = 0.0;
        for (MovPagamentoVO pagamento : getListaPagamentos()) {
            if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                Iterator i = pagamento.getChequeVOs().iterator();
                while (i.hasNext()) {
                    ChequeVO cheque = (ChequeVO) i.next();
                    if (cheque.getChequeEscolhido()) {
                        valorCheque = valorCheque + cheque.getValor();
                    }
                }
            }
        }
        return valorCheque;
    }

    public Double obterValorParcelaEmAberto() {
        Double valor = 0.0;
        for (MovParcelaVO parcela : getListaParcelas()) {
            if (parcela.getSituacao().equals("EA")) {
                valor = valor + parcela.getValorParcela();
            }
        }
        return valor;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public String getDataCancelamentoApresentar() {
        if (getDataCancelamento() == null) {
            return "";
        } else {
            return Uteis.getData(getDataCancelamento());
        }
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public Integer getNrDiasContrato() {
        return nrDiasContrato;
    }

    public void setNrDiasContrato(Integer nrDiasContrato) {
        this.nrDiasContrato = nrDiasContrato;
    }

    public Integer getNrDiasUtilizadosPeloClienteContrato() {
        return nrDiasUtilizadosPeloClienteContrato;
    }

    public void setNrDiasUtilizadosPeloClienteContrato(Integer nrDiasUtilizadosPeloClienteContrato) {
        this.nrDiasUtilizadosPeloClienteContrato = nrDiasUtilizadosPeloClienteContrato;
    }

    public Double getValorDiaContratoValorBase() {
        return valorDiaContratoValorBase;
    }

    public void setValorDiaContratoValorBase(Double valorDiaContratoValorBase) {
        this.valorDiaContratoValorBase = valorDiaContratoValorBase;
    }

    public Double getValorDiaContratoValorMensal() {
        return valorDiaContratoValorMensal;
    }

    public void setValorDiaContratoValorMensal(Double valorDiaContratoValorMensal) {
        this.valorDiaContratoValorMensal = valorDiaContratoValorMensal;
    }

    public Integer getNrDiasRestamContrato() {
        return nrDiasRestamContrato;
    }

    public void setNrDiasRestamContrato(Integer nrDiasRestamContrato) {
        this.nrDiasRestamContrato = nrDiasRestamContrato;
    }

    public Double getValorCreditoRestanteContratoValorBase() {
        return valorCreditoRestanteContratoValorBase;
    }

    public void setValorCreditoRestanteContratoValorBase(Double valorCreditoRestanteContratoValorBase) {
        this.valorCreditoRestanteContratoValorBase = valorCreditoRestanteContratoValorBase;
    }

    public Double getValorCreditoRestanteContratoValorMensal() {
        return valorCreditoRestanteContratoValorMensal;
    }

    public void setValorCreditoRestanteContratoValorMensal(Double valorCreditoRestanteContratoValorMensal) {
        this.valorCreditoRestanteContratoValorMensal = valorCreditoRestanteContratoValorMensal;
    }

    public Double getValorCreditoRestanteContratoValorMensalSemTaxaEMulta() {
        return valorCreditoRestanteContratoValorMensalSemTaxaEMulta;
    }

    public void setValorCreditoRestanteContratoValorMensalSemTaxaEMulta(Double valorCreditoRestanteContratoValorMensalSemTaxaEMulta) {
        this.valorCreditoRestanteContratoValorMensalSemTaxaEMulta = valorCreditoRestanteContratoValorMensalSemTaxaEMulta;
    }

    public String getValorCreditoRestanteContratoValorMensalSemTaxaEMulta_Apresentar() {
        return Formatador.formatarValorMonetario(getValorCreditoRestanteContratoValorMensalSemTaxaEMulta());
    }

    public List<ContratoVO> getListaContratos() {
        return listaContratos;
    }

    public void setListaContratos(List<ContratoVO> listaContratos) {
        this.listaContratos = listaContratos;
    }

    public Double getValorBaseContrato() {
        return valorBaseContrato;
    }

    public void setValorBaseContrato(Double valorBaseContrato) {
        this.valorBaseContrato = valorBaseContrato;
    }

    public Double getValorTotalBaseContratoRenovacao() {
        return valorTotalBaseContratoRenovacao;
    }

    public void setValorTotalBaseContratoRenovacao(Double valorTotalBaseContratoRenovacao) {
        this.valorTotalBaseContratoRenovacao = valorTotalBaseContratoRenovacao;
    }

    public Double getValorMensalContrato() {
        return valorMensalContrato;
    }

    public void setValorMensalContrato(Double valorMensalContrato) {
        this.valorMensalContrato = valorMensalContrato;
    }

    public String getTipoDevolucaoCancelamento() {
        if (tipoDevolucaoCancelamento == null) {
            tipoDevolucaoCancelamento = "";
        }
        return tipoDevolucaoCancelamento;
    }

    public void setTipoDevolucaoCancelamento(String tipoDevolucaoCancelamento) {
        this.tipoDevolucaoCancelamento = tipoDevolucaoCancelamento;
    }

    public Boolean getMensagemErro() {
        return mensagemErro;
    }

    public void setMensagemErro(Boolean mensagemErro) {
        this.mensagemErro = mensagemErro;
    }

    public Double getValorTaxaCancelamento() {
        return valorTaxaCancelamento;
    }

    public void setValorTaxaCancelamento(Double valorTaxaCancelamento) {
        this.valorTaxaCancelamento = valorTaxaCancelamento;
    }

    public Double getPercentualMultaCancelamento() {
        if(percentualMultaCancelamento == null){
            percentualMultaCancelamento = 0.0;
        }
        return percentualMultaCancelamento;
    }

    public void setPercentualMultaCancelamento(Double percentualMultaCancelamento) {
        this.percentualMultaCancelamento = percentualMultaCancelamento;
    }

    public Double getValorSerRecebidoPeloCliente() {
        return valorSerRecebidoPeloCliente;
    }

    public void setValorSerRecebidoPeloCliente(Double valorSerRecebidoPeloCliente) {
        this.valorSerRecebidoPeloCliente = valorSerRecebidoPeloCliente;
    }

    public Double getSaldoContaCorrenteCliente() {
        return saldoContaCorrenteCliente;
    }

    public void setSaldoContaCorrenteCliente(Double saldoContaCorrenteCliente) {
        this.saldoContaCorrenteCliente = saldoContaCorrenteCliente;
    }

    public Double getValorTotalPagoPeloCliente() {
        return valorTotalPagoPeloCliente;
    }

    public void setValorTotalPagoPeloCliente(Double valorTotalPagoPeloCliente) {
        this.valorTotalPagoPeloCliente = valorTotalPagoPeloCliente;
    }

    public void setValorASerDevolvido(Double valorASerDevolvido) {
        this.valorASerDevolvido = valorASerDevolvido;
    }

    public Double getValorASerDevolvido() {
        if ((tipoParcelaCancelamento == TipoParcelaCancelamento.MAIOR_IGUAL_MES_ATUAL || tipoParcelaCancelamento == TipoParcelaCancelamento.MAIOR_IGUAL) && getContratoCancelar().getRegimeRecorrencia() && getContratoCancelar().getEmpresa().isZerarValorCancelamentoTransferencia()){
            return 0.0;
        }
        return valorASerDevolvido + valorDevolverSaldoTransferenciaCredito;
    }

    public boolean isApresentarMensagemCobranca() {
        if (tipoParcelaCancelamento == TipoParcelaCancelamento.MAIOR_IGUAL_MES_ATUAL || tipoParcelaCancelamento == TipoParcelaCancelamento.MAIOR_IGUAL) {
            return getValorQuitacaoCancelamento() > 0.0 &&
                    (isQuitacaoManualCancelamento() || getQuitacaoCancelamento());
        }
        return false;
    }

    public String getMensagemTransferencia() {
        if (mensagemTransferencia == null) {
            mensagemTransferencia = "";
        }
        return mensagemTransferencia;
    }

    public void setMensagemTransferencia(String mensagemTransferencia) {
        this.mensagemTransferencia = mensagemTransferencia;
    }

    public List<MovPagamentoVO> getListaPagamentos() {
        return listaPagamentos;
    }

    public void setListaPagamentos(List<MovPagamentoVO> listaPagamentos) {
        this.listaPagamentos = listaPagamentos;
    }

    public List<MovParcelaVO> getListaParcelas() {
        if(listaParcelas == null){
            listaParcelas = new ArrayList<MovParcelaVO>();
        }
        return listaParcelas;
    }

    public List<MovParcelaVO> getListaParcelasCancelar() {
        List<MovParcelaVO> parcelasCancelar = new ArrayList<MovParcelaVO>();
        for(MovParcelaVO parc : getListaParcelas()){
            if(parc.getSituacao().equals("CA")){
                parcelasCancelar.add(parc);
            }

        }

        return parcelasCancelar;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public String getMensagemDevolucao() {
        if (mensagemDevolucao == null) {
            mensagemDevolucao = "";
        }
        return mensagemDevolucao;
    }

    public void setMensagemDevolucao(String mensagemDevolucao) {
        this.mensagemDevolucao = mensagemDevolucao;
    }

    public MovProdutoVO getMovProdutoDevolucao() {
        return movProdutoDevolucao;
    }

    public void setMovProdutoDevolucao(MovProdutoVO movProdutoDevolucao) {
        this.movProdutoDevolucao = movProdutoDevolucao;
    }

    public Integer getTipoJustificativaOperacao() {
        return tipoJustificativaOperacao;
    }

    public void setTipoJustificativaOperacao(Integer tipoJustificativaOperacao) {
        this.tipoJustificativaOperacao = tipoJustificativaOperacao;
    }

    public Double getSomaValorPagoPeloClienteComCheque() {
        return somaValorPagoPeloClienteComCheque;
    }

    public void setSomaValorPagoPeloClienteComCheque(Double somaValorPagoPeloClienteComCheque) {
        this.somaValorPagoPeloClienteComCheque = somaValorPagoPeloClienteComCheque;
    }

    public Double getValorTotalSomaProdutoContratos() {
        return valorTotalSomaProdutoContratos;
    }

    public void setValorTotalSomaProdutoContratos(Double valorTotalSomaProdutoContratos) {
        this.valorTotalSomaProdutoContratos = valorTotalSomaProdutoContratos;
    }

    public Double getValorASerDevolvidoBaseCalculo() {
        if (configuracaoSesc) {
            if(valorASerDevolvidoBaseCalculo > 0){
                return valorASerDevolvidoBaseCalculo;
            }
            return 0.0;
        }
        if ((tipoParcelaCancelamento == TipoParcelaCancelamento.MAIOR_IGUAL_MES_ATUAL
                || tipoParcelaCancelamento == TipoParcelaCancelamento.MAIOR_IGUAL)
                && getContratoCancelar().getRegimeRecorrencia() && getContratoCancelar().getEmpresa().isZerarValorCancelamentoTransferencia()){
            return 0.0;
        }
        return valorASerDevolvidoBaseCalculo + this.valorDevolverSaldoTransferenciaCredito;
    }

    public Double getValorASerDevolvidoBaseCalculo_Apresentar() {
        return getValorASerDevolvidoBaseCalculo() < 0 ? getValorASerDevolvidoBaseCalculo() * -1 : getValorASerDevolvidoBaseCalculo();
    }

    public String getValorASerDevolvidoBaseCalculoMonetario() {
        return Formatador.formatarValorMonetario(getValorASerDevolvidoBaseCalculo_Apresentar());
    }

    public void setValorASerDevolvidoBaseCalculo(Double valorASerDevolvidoBaseCalculo) {
        this.valorASerDevolvidoBaseCalculo = valorASerDevolvidoBaseCalculo;
    }

    public Double getValorMensalComBaseNoPercentual() {
        if(this.isContratoVendaCredito()){
            return getValorCreditoRestanteContratoVendaCreditoSemTaxaEMulta() * (getPercentualMultaCancelamento() / 100);
        } else {
            return getValorCreditoRestanteContratoValorMensalSemTaxaEMulta() * (getPercentualMultaCancelamento() / 100);
        }
        
    }

    public String getAbrirRichModal() {
        if (tipoDevolucaoCancelamento == null) {
            tipoDevolucaoCancelamento = "";
        }
        if (getTipoDevolucaoCancelamento().equals("DE")) {
            return "Richfaces.showModalPanel('panelDevolucao')";
        } else {
            return "";
        }
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Boolean isDevolucao() {
        return getTipoDevolucaoCancelamento().equals("DE");
    }

    public Boolean getDevolucao() {
        return getTipoDevolucaoCancelamento().equals("DE");
    }

    public Boolean isTransferencia() {
        return getTipoDevolucaoCancelamento().equals("TR");
    }

    public Boolean getTransferencia() {
        return getTipoDevolucaoCancelamento().equals("TR");
    }

    public Boolean getDepositaNaConta() {
        return getTipoTranferenciaCancelamento().equals("DE");
    }

    public Boolean getDepositaNaContaTerceiro() {
        return getTipoTranferenciaCancelamento().equals("DT");
    }

    public Boolean getTransferirEmDias() {
        return getTipoTranferenciaCancelamento().equals("TR");
    }

    public Boolean getExisteContratoRenovacao() {
        return getListaContratos().size() != 1;
    }

    public String getDefinirCorCampo() {
        if (getValorSerRecebidoPeloCliente() >= 0) {
            return "tituloCamposVerdeGrande";
        }
        return "tituloCamposVermelhoGrande";
    }

    public String getDefinirCorCampoValorASerDevolvido() {
        if (getValorPositivo()) {
            return "tituloCamposVerdeGrande";
        }
        return "tituloCamposVermelhoGrande";
    }

	/**
	 * <AUTHOR> Alcides
	 * 28/05/2013
	 */
    public boolean getValorPositivo() {
		return getValorASerDevolvido() >= 0;
	}

    public String getDefinirCorCampoValorFinal() {
        if (getValorFinal() >= 0) {
            return "tituloCamposVerde";
        }
        return "tituloCamposVermelho";
    }

    public String getDefinirCorCampoBase() {
        if (getValorASerDevolvidoBaseCalculo() >= 0) {
            return "tituloCamposVerde";
        }
        return "tituloCamposVermelho";
    }

    public String getTipoTranferenciaCancelamento() {
        if (tipoTranferenciaCancelamento == null) {
            tipoTranferenciaCancelamento = "";
        }
        return tipoTranferenciaCancelamento;
    }

    public ClienteVO getCliente() {
        if (cliente == null) {
            cliente = new ClienteVO();
        }
        return (cliente);
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public void setTipoTranferenciaCancelamento(String tipoTranferenciaCancelamento) {
        this.tipoTranferenciaCancelamento = tipoTranferenciaCancelamento;
    }

    public ContratoVO getContratoCreditadoNrDias() {
        return contratoCreditadoNrDias;
    }

    public void setContratoCreditadoNrDias(ContratoVO contratoCreditadoNrDias) {
        this.contratoCreditadoNrDias = contratoCreditadoNrDias;
    }

    public UsuarioVO getResponsavelCancelamento() {
        return responsavelCancelamento;
    }

    public void setResponsavelCancelamento(UsuarioVO responsavelCancelamento) {
        this.responsavelCancelamento = responsavelCancelamento;
    }

    public Boolean getLiberacaoCancelamento() {
        return liberacaoCancelamento;
    }

    public void setLiberacaoCancelamento(Boolean liberacaoCancelamento) {
        this.liberacaoCancelamento = liberacaoCancelamento;
    }

    public Boolean getQuitacaoCancelamento() {
        return quitacaoCancelamento;
    }

    public void setQuitacaoCancelamento(Boolean quitacaoCancelamento) {
        this.quitacaoCancelamento = quitacaoCancelamento;
    }

    public UsuarioVO getResponsavelLiberacaoCancelamento() {
        return responsavelLiberacaoCancelamento;
    }

    public void setResponsavelLiberacaoCancelamento(UsuarioVO responsavelLiberacaoCancelamento) {
        this.responsavelLiberacaoCancelamento = responsavelLiberacaoCancelamento;
    }

    public Double getValorQuitacaoCancelamento() {
        return valorQuitacaoCancelamento;
    }

    public String getValorQuitacaoCancelamento_Apresentar() {
        return Formatador.formatarValorMonetario(valorQuitacaoCancelamento);
    }

    public void setValorQuitacaoCancelamento(Double valorQuitacaoCancelamento) {
        this.valorQuitacaoCancelamento = valorQuitacaoCancelamento;
    }

    public Boolean getApresentaListaCheque() {
        return apresentaListaCheque;
    }

    public void setApresentaListaCheque(Boolean apresentaListaCheque) {
        this.apresentaListaCheque = apresentaListaCheque;
    }

    public MovProdutoVO getMovProdutoQuitacao() {
        return movProdutoQuitacao;
    }

    public void setMovProdutoQuitacao(MovProdutoVO movProdutoQuitacao) {
        this.movProdutoQuitacao = movProdutoQuitacao;
    }

    public Double getValorFinal() {
        Double valor = 0.0;
        if (isQuitacaoManualCancelamento() || getLiberacaoCancelamento()) {
            valor = 0.0;
        } else if (getQuitacaoCancelamento()) {
            valor = (getValorASerDevolvidoBaseCalculo() + getValorQuitacaoCancelamento());
        } else {
            valor = getValorASerDevolvidoBaseCalculo();
        }
        return valor;
    }

    public Double getValorUtilizadoPeloClienteBase() {
        return valorUtilizadoPeloClienteBase;
    }

    public void setValorUtilizadoPeloClienteBase(Double valorUtilizadoPeloClienteBase) {
        this.valorUtilizadoPeloClienteBase = valorUtilizadoPeloClienteBase;
    }

    public Double getValorUtilizadoPeloClienteMensal() {
        return valorUtilizadoPeloClienteMensal;
    }

    public void setValorUtilizadoPeloClienteMensal(Double valorUtilizadoPeloClienteMensal) {
        this.valorUtilizadoPeloClienteMensal = valorUtilizadoPeloClienteMensal;
    }

    public Integer getNrDiasOperacao() {
        return nrDiasOperacao;
    }

    public void setNrDiasOperacao(Integer nrDiasOperacao) {
        this.nrDiasOperacao = nrDiasOperacao;
    }

    public Double getNrDiasCreditarNoContrato() {
        return nrDiasCreditarNoContrato;
    }

    public void setNrDiasCreditarNoContrato(Double nrDiasCreditarNoContrato) {
        this.nrDiasCreditarNoContrato = nrDiasCreditarNoContrato;
    }

    public Boolean getApresentarPanelLancaProdutoLiberacao() {
        return getValorASerDevolvidoBaseCalculo() <= 0;
    }

    public double getValorASerPagoPeloCliente() {
        return valorASerPagoPeloCliente;
    }

    public void setValorASerPagoPeloCliente(double valorASerPagoPeloCliente) {
        this.valorASerPagoPeloCliente = valorASerPagoPeloCliente;
    }

    public boolean isQuitacaoManualCancelamento() {
        return quitacaoManualCancelamento;
    }

    public void setQuitacaoManualCancelamento(boolean quitacaoManualCancelamento) {
        this.quitacaoManualCancelamento = quitacaoManualCancelamento;
    }

    public boolean isApresentaListaCartao() {
        return apresentaListaCartao;
    }

    public void setApresentaListaCartao(boolean apresentaListaCartao) {
        this.apresentaListaCartao = apresentaListaCartao;
    }

    public String getDescricao() {
        StringBuilder descricao = new StringBuilder();
        if(getDevolucao()){
        	descricao.append("\nValor Pago Pelo Cliente: " + Formatador.formatarValorMonetario(getSomaValorPagoPeloClienteComCheque()));
            if (getValorDevolverSaldoTransferenciaCredito() > 0){
                descricao.append("\nValor Transferência de Saldo: " + Formatador.formatarValorMonetario(getValorDevolverSaldoTransferenciaCredito()));
            }
        	descricao.append("\nValor Utilizado Pelo Cliente: " + Formatador.formatarValorMonetario(getValorUtilizadoPeloClienteMensal()));
        	descricao.append("\nValor dos Produto(s) na Compra do Contrato: "
                     + Formatador.formatarValorMonetario(this.getValorTotalSomaProdutoContratos()));
            descricao.append("\nValor dos Custos Administrativos: " + Formatador.formatarValorMonetario(this.getValorTaxaCancelamento()));
  	        descricao.append("\nValor da Multa de Cancelamento: " + Formatador.formatarValorMonetario(this.getValorMensalComBaseNoPercentual()));
                if(this.getValorRecebiveis() > 0){
                    descricao.append("\nValor devolvido em recebíveis: " + Formatador.formatarValorMonetario(this.getValorRecebiveis()));
                }
  	        if (this.getValorASerDevolvidoBaseCalculo() >= 0) {
  	            if (getLiberacaoDevolucao()) {
  	                descricao.append("\nValor final retido: " + Formatador.formatarValorMonetario(this.getValorASerDevolvidoBaseCalculo()));
  	            } else {
                    if (getDevolucaoManualCancelamento() && isAlterarTipoCancelamento()) {
                        if (this.getValorASerDevolvido() < 0){
                            descricao.append("\nResíduo que deveria ser quitado pelo cliente: ").append(Formatador.formatarValorMonetario(this.getValorASerDevolvido() * -1));
                        } else {
                            descricao.append("\nResíduo que deveria ser quitado pelo cliente: ").append(Formatador.formatarValorMonetario(this.getValorASerDevolvido()));
                        }
                        /* Retirado 05/11/14 - Atividade: 87096
                        descricao.append("\nValor devolvido em dinheiro (Alt. Tipo do Cancelamento): ").append(Formatador.formatarValorMonetario(this.getValorDevolucaoCancelamento()));*/
                    }/* Retirado 05/11/14 - Atividade: 87096
                        else if(getDevolucaoManualCancelamento() && !isAlterarTipoCancelamento()){
  	            		descricao.append("\nValor calculado pelo sistema para devolução: ").append(Formatador.formatarValorMonetario(this.getValorASerDevolvidoBaseCalculo()));
  	            		descricao.append("\nValor devolvido em dinheiro (Alt. Manual): ").append(Formatador.formatarValorMonetario(this.getValorDevolucaoCancelamento()));
  	            	}*/
                    else {
                        double valor;
                        if (getDevolucaoManualCancelamento()) {
                            valor = this.getValorDevolucaoCancelamento();
                        } else {
                            valor = this.getValorASerDevolvidoBaseCalculo();
                        }
                        descricao.append("\nValor devolvido em dinheiro: ").append(Formatador.formatarValorMonetario(valor));
                    }
  	            }
  	        } else {
  	            if (getQuitacaoCancelamento()) {
  	                descricao.append("\nResíduo a ser quitado pelo cliente: " + Formatador.formatarValorMonetario(this.getValorASerDevolvidoBaseCalculo() * -1));
  	            } else if (isQuitacaoManualCancelamento()) {
                    /* Retirado 05/11/14 - Atividade: 87096
  	                descricao.append("\nResíduo a ser quitado pelo cliente: " + Formatador.formatarValorMonetario(this.getValorASerDevolvidoBaseCalculo() * -1) + " (QUITAÇÃO MANUAL)");*/
  	            } else if (getLiberacaoCancelamento()) {
  	                descricao.append("\nValor liberado ao cliente: " + Formatador.formatarValorMonetario(this.getValorASerDevolvidoBaseCalculo() * -1));
  	            }
  	        }

        } else {
        	descricao.append("\nValor Pago Pelo Cliente: " + Formatador.formatarValorMonetario(getValorTotalPagoPeloCliente()));
            if (getValorDevolverSaldoTransferenciaCredito() > 0){
                descricao.append("\nValor Transferência de Saldo: " + Formatador.formatarValorMonetario(getValorDevolverSaldoTransferenciaCredito()));
            }
        	descricao.append("\nValor Utilizado Pelo Cliente: " + Formatador.formatarValorMonetario(getValorUtilizadoPeloClienteBase()));
        	descricao.append("\nValor dos Produto(s) na Compra do Contrato: "
                     + Formatador.formatarValorMonetario(this.getValorTotalSomaProdutoContratos()));
        	descricao.append("\nSaldo da Conta Corrente do Cliente: " + Formatador.formatarValorMonetario(this.getSaldoContaCorrenteCliente()));
        	if (getDepositaNaContaTerceiro() || getTransferirEmDias()){
 	      		 descricao.append("\nValor Transferido: "+Formatador.formatarValorMonetario(this.getAlteracaoValorTrans() ? this.getValorASerDevolvido() : this.getValorASerDevolvidoBaseCalculo()));
 	      		 descricao.append("\nAluno beneficiado: " + getCliente().getPessoa().getNome());
        	} else {
        		descricao.append("\nValor depositado na conta do aluno: "+Formatador.formatarValorMonetario(this.getAlteracaoValorTrans() ? this.getValorASerDevolvido() : this.getValorASerDevolvidoBaseCalculo()));
        	}
        }
        if (!UteisValidacao.emptyNumber(getValorEmAberto())) {
            descricao.append("\nValor das parcelas em aberto: ").append(Formatador.formatarValorMonetario(this.getValorEmAberto()));
        }
        return descricao.toString();
    }

    public void setValorASerDevolvidoRec(double valorASerDevolvidoRec) {
        this.valorASerDevolvidoRec = valorASerDevolvidoRec;
    }

    public double getValorASerDevolvidoRec() {
        return valorASerDevolvidoRec;
    }

    public void setMovProdutoDevolucaoRecebiveis(MovProdutoVO movProdutoDevolucaoRecebiveis) {
        this.movProdutoDevolucaoRecebiveis = movProdutoDevolucaoRecebiveis;
    }

    public MovProdutoVO getMovProdutoDevolucaoRecebiveis() {
        return movProdutoDevolucaoRecebiveis;
    }

    public void setLiberacaoDevolucao(boolean liberacaoDevolucao) {
        this.liberacaoDevolucao = liberacaoDevolucao;
    }

    public boolean getLiberacaoDevolucao() {
        return liberacaoDevolucao;
    }

    public void setValorRecebiveis(Double valorRecebiveis) {
        this.valorRecebiveis = valorRecebiveis;
    }

    public Double getValorRecebiveis() {
        return valorRecebiveis;
    }

    public String getValorDevolvidoEmDinheiroApresentar() {
        return Formatador.formatarValorMonetario(getValorASerDevolvidoBaseCalculo_Apresentar());
    }
    
    public String getValorDevolvidoManualApresentar() {
        return Formatador.formatarValorMonetario(getValorDevolucaoCancelamento());
    }

	public void setDevolucaoManualCancelamento(boolean devolucaoManualCancelamento) {
		this.devolucaoManualCancelamento = devolucaoManualCancelamento;
	}

	public boolean getDevolucaoManualCancelamento() {
		return devolucaoManualCancelamento;
	}

	public void setValorDevolucaoCancelamento(Double valorDevolucaoCancelamento) {
		this.valorDevolucaoCancelamento = valorDevolucaoCancelamento;
	}

	public Double getValorDevolucaoCancelamento() {
		return valorDevolucaoCancelamento;
	}
    public List<MovPagamentoVO> getListaPagamentosMovimento() {
		return listaPagamentosMovimento;
	}

	public void setListaPagamentosMovimento(
			List<MovPagamentoVO> listaPagamentosMovimento) {
		this.listaPagamentosMovimento = listaPagamentosMovimento;
	}

	public Date getAjustadaContratoCreditadoAntiga() {
		return ajustadaContratoCreditadoAntiga;
	}

	public void setAjustadaContratoCreditadoAntiga(
			Date ajustadaContratoCreditadoAntiga) {
		this.ajustadaContratoCreditadoAntiga = ajustadaContratoCreditadoAntiga;
	}

	public void setAlteracaoValorTrans(boolean alteracaoValorTrans) {
		this.alteracaoValorTrans = alteracaoValorTrans;
	}

	public boolean getAlteracaoValorTrans() {
		return alteracaoValorTrans;
	}

    public boolean getCredito() {
        return credito;
    }

    public void setCredito(boolean credito) {
        this.credito = credito;
    }


    public boolean isAlterarTipoCancelamento() {
        return alterarTipoCancelamento;
    }

    public void setAlterarTipoCancelamento(boolean alterarTipoCancelamento) {
        this.alterarTipoCancelamento = alterarTipoCancelamento;
    }

    public boolean isApresentaListaCartaoMovimento() {
        return apresentaListaCartaoMovimento;
    }

    public void setApresentaListaCartaoMovimento(boolean apresentaListaCartaoMovimento) {
        this.apresentaListaCartaoMovimento = apresentaListaCartaoMovimento;
    }

    public Boolean getApresentaListaChequeMovimento() {
        return apresentaListaChequeMovimento;
    }

    public void setApresentaListaChequeMovimento(Boolean apresentaListaChequeMovimento) {
        this.apresentaListaChequeMovimento = apresentaListaChequeMovimento;
    }

    public List<Integer> getCartoesComComposicao() {
        return cartoesComComposicao;
    }

    public void setCartoesComComposicao(List<Integer> cartoesComComposicao) {
        this.cartoesComComposicao = cartoesComComposicao;
    }

    public List<Integer> getChequesComComposicao() {
        return chequesComComposicao;
    }

    public void setChequesComComposicao(List<Integer> chequesComComposicao) {
        this.chequesComComposicao = chequesComComposicao;
    }

    public Double getPercentualMultaCancelamentoRec() {
        return percentualMultaCancelamentoRec;
    }

    public void setPercentualMultaCancelamentoRec(Double percentualMultaCancelamentoRec) {
        this.percentualMultaCancelamentoRec = percentualMultaCancelamentoRec;
    }

    public List<ChequeVO> getChequesCreditoCCUsado() {
        return chequesCreditoCCUsado;
    }

    public void setChequesCreditoCCUsado(List<ChequeVO> chequesCreditoCCUsado) {
        this.chequesCreditoCCUsado = chequesCreditoCCUsado;
    }

    public List<CartaoCreditoVO> getCartoesCreditoCCUsado() {
        return cartoesCreditoCCUsado;
    }

    public void setCartoesCreditoCCUsado(List<CartaoCreditoVO> cartoesCreditoCCUsado) {
        this.cartoesCreditoCCUsado = cartoesCreditoCCUsado;
    }

    public List<MovPagamentoVO> getPagamentosCredito() {
        return pagamentosCredito;
    }

    public void setPagamentosCredito(List<MovPagamentoVO> pagamentosCredito) {
        this.pagamentosCredito = pagamentosCredito;
    }

    public ContratoOperacaoVO getContratoOperacaoVO() {
        if (contratoOperacaoVO == null) {
            contratoOperacaoVO = new ContratoOperacaoVO();
        }
        return contratoOperacaoVO;
    }

    public void setContratoOperacaoVO(ContratoOperacaoVO contratoOperacaoVO) {
        this.contratoOperacaoVO = contratoOperacaoVO;
    }

    public ContratoOperacaoVO getComprovanteTransDiasVO() {
        if (comprovanteTransDiasVO == null) {
            comprovanteTransDiasVO = new ContratoOperacaoVO();
        }
        return comprovanteTransDiasVO;
    }

    public void setComprovanteTransDiasVO(ContratoOperacaoVO comprovanteTransDiasVO) {
        this.comprovanteTransDiasVO = comprovanteTransDiasVO;
    }

    public String getMensagemCancelamentoAntecipado() {
        if (mensagemCancelamentoAntecipado == null) {
            mensagemCancelamentoAntecipado = "";
        }
        return mensagemCancelamentoAntecipado;
    }

    public void setMensagemCancelamentoAntecipado(String mensagemCancelamentoAntecipado) {
        this.mensagemCancelamentoAntecipado = mensagemCancelamentoAntecipado;
    }

    public Integer getMovParcelaCancelamentoAntecipado() {
        if (movParcelaCancelamentoAntecipado == null) {
            movParcelaCancelamentoAntecipado = 0;
        }
        return movParcelaCancelamentoAntecipado;
    }

    public void setMovParcelaCancelamentoAntecipado(Integer movParcelaCancelamentoAntecipado) {
        this.movParcelaCancelamentoAntecipado = movParcelaCancelamentoAntecipado;
    }

    public boolean isPagarProximaParcelaCancelamentoAntecipado() {
        return pagarProximaParcelaCancelamentoAntecipado;
    }

    public void setPagarProximaParcelaCancelamentoAntecipado(boolean pagarProximaParcelaCancelamentoAntecipado) {
        this.pagarProximaParcelaCancelamentoAntecipado = pagarProximaParcelaCancelamentoAntecipado;
    }

    public Date getDataFinalAcessoCancelamentoAntecipado() {
        return dataFinalAcessoCancelamentoAntecipado;
    }

    public void setDataFinalAcessoCancelamentoAntecipado(Date dataFinalAcessoCancelamentoAntecipado) {
        this.dataFinalAcessoCancelamentoAntecipado = dataFinalAcessoCancelamentoAntecipado;
    }

    public Double getValorMultaProximaParcelaCancelamentoAntecipado() {
        if (valorMultaProximaParcelaCancelamentoAntecipado == null) {
            valorMultaProximaParcelaCancelamentoAntecipado = 0.0;
        }
        return valorMultaProximaParcelaCancelamentoAntecipado;
    }

    public void setValorMultaProximaParcelaCancelamentoAntecipado(Double valorMultaProximaParcelaCancelamentoAntecipado) {
        this.valorMultaProximaParcelaCancelamentoAntecipado = valorMultaProximaParcelaCancelamentoAntecipado;
    }

    public String getMensagemGravarOperacaoCancelamentoAntecipado() {
        if (mensagemGravarOperacaoCancelamentoAntecipado == null) {
            mensagemGravarOperacaoCancelamentoAntecipado = "";
        }
        return mensagemGravarOperacaoCancelamentoAntecipado;
    }

    public void setMensagemGravarOperacaoCancelamentoAntecipado(String mensagemGravarOperacaoCancelamentoAntecipado) {
        this.mensagemGravarOperacaoCancelamentoAntecipado = mensagemGravarOperacaoCancelamentoAntecipado;
    }

    public double getValorDevolverSaldoTransferenciaCredito() {
        return valorDevolverSaldoTransferenciaCredito;
    }

    public void setValorDevolverSaldoTransferenciaCredito(double valorDevolverSaldoTransferenciaCredito) {
        this.valorDevolverSaldoTransferenciaCredito = valorDevolverSaldoTransferenciaCredito;
    }

    public ContratoVO getContratoTransferenciaSaldo() {
        return contratoTransferenciaSaldo;
    }

    public void setContratoTransferenciaSaldo(ContratoVO contratoTransferenciaSaldo) {
        this.contratoTransferenciaSaldo = contratoTransferenciaSaldo;
    }

    public TipoParcelaCancelamento getTipoParcelaCancelamento() {
        return tipoParcelaCancelamento;
    }

    public void setTipoParcelaCancelamento(TipoParcelaCancelamento tipoParcelaCancelamento) {
        this.tipoParcelaCancelamento = tipoParcelaCancelamento;
    }

    public boolean isCancelarSaldoContaCorrente() {
        return cancelarSaldoContaCorrente;
    }

    public void setCancelarSaldoContaCorrente(boolean cancelarSaldoContaCorrente) {
        this.cancelarSaldoContaCorrente = cancelarSaldoContaCorrente;
    }

    public ContratoVO getContratoCancelar() {
        return contratoCancelar;
    }

    public void setContratoCancelar(ContratoVO contratoCancelar) {
        this.contratoCancelar = contratoCancelar;
    }

    public boolean isCancelarParcelaAnuidade() {
        return cancelarParcelaAnuidade;
    }

    public void setCancelarParcelaAnuidade(boolean cancelarParcelaAnuidade) {
        this.cancelarParcelaAnuidade = cancelarParcelaAnuidade;
    }

    public Double getValorMultaRestanteCancelamentoAntecipado() {
        if (valorMultaRestanteCancelamentoAntecipado == null) {
            valorMultaRestanteCancelamentoAntecipado = 0.0;
        }
        return valorMultaRestanteCancelamentoAntecipado;
    }

    public void setValorMultaRestanteCancelamentoAntecipado(Double valorMultaRestanteCancelamentoAntecipado) {
        this.valorMultaRestanteCancelamentoAntecipado = valorMultaRestanteCancelamentoAntecipado;
    }

    public Double getValorMultaSobreValorTotalContratoCancelamentoAntecipado() {
        return valorMultaSobreValorTotalContratoCancelamentoAntecipado;
    }

    public void setValorMultaSobreValorTotalContratoCancelamentoAntecipado(Double valorMultaSobreValorTotalContratoCancelamentoAntecipado) {
        this.valorMultaSobreValorTotalContratoCancelamentoAntecipado = valorMultaSobreValorTotalContratoCancelamentoAntecipado;
    }

    public List<MovParcelaVO> getParcelasEmAtrasoCancelamentoAntecipado() {
        if (parcelasEmAtrasoCancelamentoAntecipado == null) {
            parcelasEmAtrasoCancelamentoAntecipado = new ArrayList<MovParcelaVO>();
        }
        return parcelasEmAtrasoCancelamentoAntecipado;
    }

    public void setParcelasEmAtrasoCancelamentoAntecipado(List<MovParcelaVO> parcelasEmAtrasoCancelamentoAntecipado) {
        this.parcelasEmAtrasoCancelamentoAntecipado = parcelasEmAtrasoCancelamentoAntecipado;
    }

    public Double getValorDiaContratoCreditadoValorBase() {
        return valorDiaContratoCreditadoValorBase;
    }

    public void setValorDiaContratoCreditadoValorBase(Double valorDiaContratoCreditadoValorBase) {
        this.valorDiaContratoCreditadoValorBase = valorDiaContratoCreditadoValorBase;
    }

    public boolean isPlanoEspecialCancelamentoAntecipado() {
        return planoEspecialCancelamentoAntecipado;
    }

    public void setPlanoEspecialCancelamentoAntecipado(boolean planoEspecialCancelamentoAntecipado) {
        this.planoEspecialCancelamentoAntecipado = planoEspecialCancelamentoAntecipado;
    }

    public Double getValorTotalCancelamentoAntecipado() {
        if (valorTotalCancelamentoAntecipado == null) {
            valorTotalCancelamentoAntecipado = 0.0;
        }
        return valorTotalCancelamentoAntecipado;
    }

    public void setValorTotalCancelamentoAntecipado(Double valorTotalCancelamentoAntecipado) {
        this.valorTotalCancelamentoAntecipado = valorTotalCancelamentoAntecipado;
    }

    public String getInformacoesCancelamentoAntecipado() {
        if (informacoesCancelamentoAntecipado == null) {
            informacoesCancelamentoAntecipado = "";
        }
        return informacoesCancelamentoAntecipado;
    }

    public void setInformacoesCancelamentoAntecipado(String informacoesCancelamentoAntecipado) {
        this.informacoesCancelamentoAntecipado = informacoesCancelamentoAntecipado;
    }

    public Date getDataParcelaGerarCancelamentoAntecipado() {
        return dataParcelaGerarCancelamentoAntecipado;
    }

    public void setDataParcelaGerarCancelamentoAntecipado(Date dataParcelaGerarCancelamentoAntecipado) {
        this.dataParcelaGerarCancelamentoAntecipado = dataParcelaGerarCancelamentoAntecipado;
    }

    public Double getValorTotalCancelamentoAntecipadoOutrosContratos() {
        if (valorTotalCancelamentoAntecipadoOutrosContratos == null) {
            valorTotalCancelamentoAntecipadoOutrosContratos = 0.0;
        }
        return valorTotalCancelamentoAntecipadoOutrosContratos;
    }

    public void setValorTotalCancelamentoAntecipadoOutrosContratos(Double valorTotalCancelamentoAntecipadoOutrosContratos) {
        this.valorTotalCancelamentoAntecipadoOutrosContratos = valorTotalCancelamentoAntecipadoOutrosContratos;
    }

    public List<CancelamentoAntecipadoTO> getListaParcelasGerarCancelamentoAntecipado() {
        if (listaParcelasGerarCancelamentoAntecipado == null) {
            listaParcelasGerarCancelamentoAntecipado = new ArrayList<CancelamentoAntecipadoTO>();
        }
        return listaParcelasGerarCancelamentoAntecipado;
    }

    public void setListaParcelasGerarCancelamentoAntecipado(List<CancelamentoAntecipadoTO> listaParcelasGerarCancelamentoAntecipado) {
        this.listaParcelasGerarCancelamentoAntecipado = listaParcelasGerarCancelamentoAntecipado;
    }

    public Double getValorCreditoRestanteContratoVendaCreditoSemTaxaEMulta() {
        return valorCreditoRestanteContratoVendaCreditoSemTaxaEMulta;
    }

    public void setValorCreditoRestanteContratoVendaCreditoSemTaxaEMulta(Double valorCreditoRestanteContratoVendaCreditoSemTaxaEMulta) {
        this.valorCreditoRestanteContratoVendaCreditoSemTaxaEMulta = valorCreditoRestanteContratoVendaCreditoSemTaxaEMulta;
    }

    public boolean isContratoVendaCredito() {
        return contratoVendaCredito;
    }

    public void setContratoVendaCredito(boolean contratoVendaCredito) {
        this.contratoVendaCredito = contratoVendaCredito;
    }

    public CancelamentoProporcionalTO getCancelamentoProporcional() {
        if (cancelamentoProporcional == null) {
            cancelamentoProporcional = new CancelamentoProporcionalTO();
        }
        return cancelamentoProporcional;
    }

    public void setCancelamentoProporcional(CancelamentoProporcionalTO cancelamentoProporcional) {
        this.cancelamentoProporcional = cancelamentoProporcional;
    }

    public CancelamentoAvaliandoParcelaTO getCancelamentoAvaliandoParcelaTO() {
        if (cancelamentoAvaliandoParcelaTO == null) {
            cancelamentoAvaliandoParcelaTO = new CancelamentoAvaliandoParcelaTO();
        }
        return cancelamentoAvaliandoParcelaTO;
    }

    public void setCancelamentoAvaliandoParcelaTO(CancelamentoAvaliandoParcelaTO cancelamentoAvaliandoParcelaTO) {
        this.cancelamentoAvaliandoParcelaTO = cancelamentoAvaliandoParcelaTO;
    }

    public List<MovParcelaVO> getParcelasVencidasCanceladas() {
        return parcelasVencidasCanceladas;
    }

    public void setParcelasVencidasCanceladas(List<MovParcelaVO> parcelasVencidasCanceladas) {
        this.parcelasVencidasCanceladas = parcelasVencidasCanceladas;
    }

    public int getEmpresaDestinoTransferencia() {
        return empresaDestinoTransferencia;
    }

    public void setEmpresaDestinoTransferencia(int empresaDestinoTransferencia) {
        this.empresaDestinoTransferencia = empresaDestinoTransferencia;
    }

    public String getNomePlanoOrigem() {
        return nomePlanoOrigem;
    }

    public void setNomePlanoOrigem(String nomePlanoOrigem) {
        this.nomePlanoOrigem = nomePlanoOrigem;
    }

    public String getNomePlanoDestino() {
        return nomePlanoDestino;
    }

    public void setNomePlanoDestino(String nomePlanoDestino) {
        this.nomePlanoDestino = nomePlanoDestino;
    }

    public boolean isCancelamentoProporcionalValidar() {
        return cancelamentoProporcionalValidar;
    }

    public void setCancelamentoProporcionalValidar(boolean cancelamentoProporcionalValidar) {
        this.cancelamentoProporcionalValidar = cancelamentoProporcionalValidar;
    }

    public void setValorEmAberto(Double valorEmAberto) {
        this.valorEmAberto = valorEmAberto;
    }

    public Double getValorEmAberto() {
        if (valorEmAberto == null) {
            valorEmAberto = 0.0;
        }
        return valorEmAberto;
    }

    public Boolean getConfiguracaoSesc() {
        return configuracaoSesc;
    }

    public void setConfiguracaoSesc(Boolean configuracaoSesc) {
        this.configuracaoSesc = configuracaoSesc;
    }

    public JustificativaOperacaoVO getJustificativaOperacao() {
        if (justificativaOperacao == null) {
            justificativaOperacao = new JustificativaOperacaoVO();
        }
        return justificativaOperacao;
    }

    public void setJustificativaOperacao(JustificativaOperacaoVO justificativaOperacao) {
        this.justificativaOperacao = justificativaOperacao;
    }

    public double getValorBaseCalculoMultaCancelamento() {
        return valorBaseCalculoMultaCancelamento;
    }

    public void setValorBaseCalculoMultaCancelamento(double valorBaseCalculoMultaCancelamento) {
        this.valorBaseCalculoMultaCancelamento = valorBaseCalculoMultaCancelamento;
    }

    public String getValorBaseCalculoMultaCancelamento_Apresentar() {
        return Formatador.formatarValorMonetario(this.valorBaseCalculoMultaCancelamento);
    }

    public Double getValorTotalSomaProdutoContratosDevolver() {
        return valorTotalSomaProdutoContratosDevolver;
    }

    public void setValorTotalSomaProdutoContratosDevolver(Double valorTotalSomaProdutoContratosDevolver) {
        this.valorTotalSomaProdutoContratosDevolver = valorTotalSomaProdutoContratosDevolver;
    }

    public Boolean getTransferenciaEmpresaClientePlano() {
        return transferenciaEmpresaClientePlano;
    }

    public void setTransferenciaEmpresaClientePlano(Boolean transferenciaEmpresaClientePlano) {
        this.transferenciaEmpresaClientePlano = transferenciaEmpresaClientePlano;
    }

    public Boolean getCancelamentoAutomatico() {
        return cancelamentoAutomatico;
    }

    public void setCancelamentoAutomatico(Boolean cancelamentoAutomatico) {
        this.cancelamentoAutomatico = cancelamentoAutomatico;
    }
}

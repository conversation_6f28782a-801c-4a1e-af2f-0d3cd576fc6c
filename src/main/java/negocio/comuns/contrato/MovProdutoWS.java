package negocio.comuns.contrato;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;

/**
 * Created by <PERSON><PERSON> on 20/06/2015
 */
public class MovProdutoWS extends SuperTO {

    private int codigo;
    private int contrato;
    private String descricao = "";
    private String dataLancamento = "";
    private int quantidade;
    private double valorUnitario;
    private double valorDesconto;
    private double valorTotal;
    private String situacao = "";

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public int getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(int quantidade) {
        this.quantidade = quantidade;
    }

    public double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public String getValorUnitarioApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorUnitario());
    }

    public double getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public String getValorDescontoApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorDesconto());
    }

    public double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getValorTotalApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorTotal());
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }
}
package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class HorarioTurmaConcatenadoTO extends SuperVO {
    private static final long serialVersionUID = 7736236653586603873L;

    private TurmaVO turma;
    private String professor;
    private String nivel;
    private String ambiente;
    private String horaInicial;
    private String horaFinal;
    private HorarioTurmaVO horarioTurmaDom = new HorarioTurmaVO();
    private HorarioTurmaVO horarioTurmaSeg = new HorarioTurmaVO();
    private HorarioTurmaVO horarioTurmaTer = new HorarioTurmaVO();
    private HorarioTurmaVO horarioTurmaQua = new HorarioTurmaVO();
    private HorarioTurmaVO horarioTurmaQui = new HorarioTurmaVO();
    private HorarioTurmaVO horarioTurmaSex = new HorarioTurmaVO();
    private HorarioTurmaVO horarioTurmaSab = new HorarioTurmaVO();

    private boolean renovacaoContrato = false;
    private boolean empresaPermiteRenovarContratosEmTurmasLotadas = false;

    private String siglaSemana = "";

    public Integer getQuantidadeVagasSeg() {
        if (horarioTurmaSeg.getQuantidadeVagas() <= 0) {
            return 0;
        } else {
            return horarioTurmaSeg.getQuantidadeVagas();
        }
    }

    public int getOcupacaoSeg() {
        return horarioTurmaSeg.getNrMaximoAluno() - horarioTurmaSeg.getQuantidadeVagas();
    }

    public int getQuantidadeVagasDom() {
        return horarioTurmaDom.getQuantidadeVagas();
    }

    public int getOcupacaoDom() {
        return horarioTurmaDom.getNrMaximoAluno() - horarioTurmaDom.getQuantidadeVagas();
    }

    public int getQuantidadeVagasTer() {
        return horarioTurmaTer.getQuantidadeVagas();
    }

    public int getOcupacaoTer() {
        return horarioTurmaTer.getNrMaximoAluno() - horarioTurmaTer.getQuantidadeVagas();
    }

    public int getQuantidadeVagasQua() {
        return horarioTurmaQua.getQuantidadeVagas();
    }

    public int getOcupacaoQua() {
        return horarioTurmaQua.getNrMaximoAluno() - horarioTurmaQua.getQuantidadeVagas();
    }

    public int getQuantidadeVagasQui() {
        return horarioTurmaQui.getQuantidadeVagas();
    }

    public int getOcupacaoQui() {
        return horarioTurmaQui.getNrMaximoAluno() - horarioTurmaQui.getQuantidadeVagas();
    }

    public int getQuantidadeVagasSex() {
        return horarioTurmaSex.getQuantidadeVagas();
    }

    public int getOcupacaoSex() {
        return horarioTurmaSex.getNrMaximoAluno() - horarioTurmaSex.getQuantidadeVagas();
    }

    public int getQuantidadeVagasSab() {
        return horarioTurmaSab.getQuantidadeVagas();
    }

    public int getOcupacaoSab() {
        return horarioTurmaSab.getNrMaximoAluno() - horarioTurmaSab.getQuantidadeVagas();
    }

    public boolean existeHorarioTurma(HorarioTurmaVO ht) {
        HorarioTurmaVO htm = null;
        if (ht.getDiaSemana().equals("DM")) {
            htm = horarioTurmaDom;
        }
        if (ht.getDiaSemana().equals("SG")) {
            htm = horarioTurmaSeg;
        }
        if (ht.getDiaSemana().equals("TR")) {
            htm = horarioTurmaTer;
        }
        if (ht.getDiaSemana().equals("QA")) {
            htm = horarioTurmaQua;
        }
        if (ht.getDiaSemana().equals("QI")) {
            htm = horarioTurmaQui;
        }
        if (ht.getDiaSemana().equals("SX")) {
            htm = horarioTurmaSex;
        }
        if (ht.getDiaSemana().equals("SB")) {
            htm = horarioTurmaSab;
        }
        return htm != null && (!UteisValidacao.emptyNumber(htm.getCodigo()) && htm.getCodigo().intValue() != ht.getCodigo());
    }

    public void adicionaSimples(HorarioTurmaVO ht) {
        if (ht.getDiaSemana().equals("DM")) {
            horarioTurmaDom = ht;
        }
        if (ht.getDiaSemana().equals("SG")) {
            horarioTurmaSeg = ht;
        }
        if (ht.getDiaSemana().equals("TR")) {
            horarioTurmaTer = ht;
        }
        if (ht.getDiaSemana().equals("QA")) {
            horarioTurmaQua = ht;
        }
        if (ht.getDiaSemana().equals("QI")) {
            horarioTurmaQui = ht;
        }
        if (ht.getDiaSemana().equals("SX")) {
            horarioTurmaSex = ht;
        }
        if (ht.getDiaSemana().equals("SB")) {
            horarioTurmaSab = ht;
        }
    }

    public String getTurma_Apresentar() {
        return getTurma().getIdentificador();
    }

    public void adiciona(HorarioTurmaVO ht) {
        professor = ht.getProfessor().getPessoa().getNome();
        nivel = ht.getNivelTurma().getDescricao();
        ambiente = ht.getAmbiente().getDescricao();
        horaInicial = ht.getHoraInicial();
        horaFinal = ht.getHoraFinal();
        siglaSemana = ht.getDiaSemana();
        adicionaSimples(ht);
    }

    public double getMediaOcupacao(){
        int matriculados = 0;
        int vagas =0;

        if(null != horarioTurmaDom){
            matriculados += horarioTurmaDom.getNrAlunoMatriculado();
            vagas += horarioTurmaDom.getNrMaximoAluno();
        }

        if(null != horarioTurmaSeg){
            matriculados += horarioTurmaSeg.getNrAlunoMatriculado();
            vagas += horarioTurmaSeg.getNrMaximoAluno();
        }

        if(null != horarioTurmaTer){
            matriculados += horarioTurmaTer.getNrAlunoMatriculado();
            vagas += horarioTurmaTer.getNrMaximoAluno();
        }

        if(null != horarioTurmaQua){
            matriculados += horarioTurmaQua.getNrAlunoMatriculado();
            vagas += horarioTurmaQua.getNrMaximoAluno();
        }

        if(null != horarioTurmaQui){
            matriculados += horarioTurmaQui.getNrAlunoMatriculado();
            vagas += horarioTurmaQui.getNrMaximoAluno();
        }

        if(null != horarioTurmaSex){
            matriculados += horarioTurmaSex.getNrAlunoMatriculado();
            vagas += horarioTurmaSex.getNrMaximoAluno();
        }

        if(null != horarioTurmaSab){
            matriculados += horarioTurmaSab.getNrAlunoMatriculado();
            vagas += horarioTurmaSab.getNrMaximoAluno();
        }

        if(vagas > 0) {
            double m = (double) matriculados / vagas;
            return Uteis.arredondarForcando2CasasDecimais(m * 100);
        }
        return 0;

    }

    public TurmaVO.NivelDesconto getNivelDescontoTurmaHorarioDom(){

        if(null != horarioTurmaDom && horarioTurmaDom.getNrMaximoAluno() > 0){
            return getTurma().getByPercentualOcupacao(horarioTurmaDom.getPercOcupacao());
        }

        return null;
    }

    public TurmaVO.NivelDesconto getNivelDescontoTurmaHorarioSeg(){

        if(null != horarioTurmaSeg && horarioTurmaSeg.getNrMaximoAluno() > 0){
            return getTurma().getByPercentualOcupacao(horarioTurmaSeg.getPercOcupacao());
        }

        return null;
    }

    public TurmaVO.NivelDesconto getNivelDescontoTurmaHorarioTer(){

        if(null != horarioTurmaTer && horarioTurmaTer.getNrMaximoAluno() > 0){
            return getTurma().getByPercentualOcupacao(horarioTurmaTer.getPercOcupacao());
        }

        return null;
    }

    public TurmaVO.NivelDesconto getNivelDescontoTurmaHorarioQua(){

        if(null != horarioTurmaQua && horarioTurmaQua.getNrMaximoAluno() > 0){
            return getTurma().getByPercentualOcupacao(horarioTurmaQua.getPercOcupacao());
        }

        return null;
    }

    public TurmaVO.NivelDesconto getNivelDescontoTurmaHorarioQui(){

        if(null != horarioTurmaQui && horarioTurmaQui.getNrMaximoAluno() > 0){
            return getTurma().getByPercentualOcupacao(horarioTurmaQui.getPercOcupacao());
        }

        return null;
    }

    public TurmaVO.NivelDesconto getNivelDescontoTurmaHorarioSex(){

        if(null != horarioTurmaSex && horarioTurmaSex.getNrMaximoAluno() > 0){
            return getTurma().getByPercentualOcupacao(horarioTurmaSex.getPercOcupacao());
        }

        return null;
    }

    public TurmaVO.NivelDesconto getNivelDescontoTurmaHorarioSab(){

        if(null != horarioTurmaSab && horarioTurmaSab.getNrMaximoAluno() > 0){
            return getTurma().getByPercentualOcupacao(horarioTurmaSab.getPercOcupacao());
        }

        return null;
    }

    public TurmaVO getTurma() {
        return turma;
    }

    public void setTurma(TurmaVO turma) {
        this.turma = turma;
    }

    public String getProfessor() {

        return (Uteis.obterPrimeiroNomeConcatenadoSobreNome(professor));
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public String getHoraInicial() {
        return horaInicial;
    }

    public void setHoraInicial(String horaInicial) {
        this.horaInicial = horaInicial;
    }

    public String getHoraFinal() {
        return horaFinal;
    }

    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    public HorarioTurmaVO getHorarioTurmaDom() {
        return horarioTurmaDom;
    }

    public boolean isMostrarHorarioDom() {
        return (!getTurma().getBloquearMatriculasAcimaLimite()
                || getHorarioTurmaDom().getQuantidadeVagas() > 0
                || (isRenovacaoContrato() && isEmpresaPermiteRenovarContratosEmTurmasLotadas()))
                && getHorarioTurmaDom().getNrMaximoAluno() != 0;
    }

    public void setHorarioTurmaDom(HorarioTurmaVO horarioTurmaDom) {
        this.horarioTurmaDom = horarioTurmaDom;
    }

    public HorarioTurmaVO getHorarioTurmaSeg() {
        return horarioTurmaSeg;
    }

    public boolean isMostrarHorarioSeg() {
        return (!getTurma().getBloquearMatriculasAcimaLimite() || getHorarioTurmaSeg().getQuantidadeVagas() > 0
                || (isRenovacaoContrato() && isEmpresaPermiteRenovarContratosEmTurmasLotadas()))
                && getHorarioTurmaSeg().getNrMaximoAluno() != 0;
    }

    public void setHorarioTurmaSeg(HorarioTurmaVO horarioTurmaSeg) {
        this.horarioTurmaSeg = horarioTurmaSeg;
    }

    public HorarioTurmaVO getHorarioTurmaTer() {
        return horarioTurmaTer;
    }

    public boolean isMostrarHorarioTer() {
        return (!getTurma().getBloquearMatriculasAcimaLimite() || getHorarioTurmaTer().getQuantidadeVagas() > 0
                || (isRenovacaoContrato() && isEmpresaPermiteRenovarContratosEmTurmasLotadas()))
                && getHorarioTurmaTer().getNrMaximoAluno() != 0;
    }

    public void setHorarioTurmaTer(HorarioTurmaVO horarioTurmaTer) {
        this.horarioTurmaTer = horarioTurmaTer;
    }

    public HorarioTurmaVO getHorarioTurmaQua() {
        return horarioTurmaQua;
    }

    public boolean isMostrarHorarioQua() {
        return (!getTurma().getBloquearMatriculasAcimaLimite() || getHorarioTurmaQua().getQuantidadeVagas() > 0
                || (isRenovacaoContrato() && isEmpresaPermiteRenovarContratosEmTurmasLotadas()))
                && getHorarioTurmaQua().getNrMaximoAluno() != 0;
    }

    public void setHorarioTurmaQua(HorarioTurmaVO horarioTurmaQua) {
        this.horarioTurmaQua = horarioTurmaQua;
    }

    public HorarioTurmaVO getHorarioTurmaQui() {
        return horarioTurmaQui;
    }

    public boolean isMostrarHorarioQui() {
        return (!getTurma().getBloquearMatriculasAcimaLimite() || getHorarioTurmaQui().getQuantidadeVagas() > 0
                || (isRenovacaoContrato() && isEmpresaPermiteRenovarContratosEmTurmasLotadas()))
                && getHorarioTurmaQui().getNrMaximoAluno() != 0;
    }

    public void setHorarioTurmaQui(HorarioTurmaVO horarioTurmaQui) {
        this.horarioTurmaQui = horarioTurmaQui;
    }

    public HorarioTurmaVO getHorarioTurmaSex() {
        return horarioTurmaSex;
    }

    public boolean isMostrarHorarioSex() {
        return (!getTurma().getBloquearMatriculasAcimaLimite() || getHorarioTurmaSex().getQuantidadeVagas() > 0
                || (isRenovacaoContrato() && isEmpresaPermiteRenovarContratosEmTurmasLotadas()))
                && getHorarioTurmaSex().getNrMaximoAluno() != 0;
    }

    public void setHorarioTurmaSex(HorarioTurmaVO horarioTurmaSex) {
        this.horarioTurmaSex = horarioTurmaSex;
    }

    public HorarioTurmaVO getHorarioTurmaSab() {
        return horarioTurmaSab;
    }

    public boolean isMostrarHorarioSab() {
        return (!getTurma().getBloquearMatriculasAcimaLimite() || getHorarioTurmaSab().getQuantidadeVagas() > 0
                || (isRenovacaoContrato() && isEmpresaPermiteRenovarContratosEmTurmasLotadas()))
                && getHorarioTurmaSab().getNrMaximoAluno() != 0;
    }

    public void setHorarioTurmaSab(HorarioTurmaVO horarioTurmaSab) {
        this.horarioTurmaSab = horarioTurmaSab;
    }

    public boolean igual(HorarioTurmaVO ht) {
        return professor.equals(ht.getProfessor().getPessoa().getNome())
                && ambiente.equals(ht.getAmbiente().getDescricao())
                && horaInicial.equals(ht.getHoraInicial())
                && horaFinal.equals(ht.getHoraFinal())
                && nivel.equals(ht.getNivelTurma().getDescricao());
    }

    public boolean isRenovacaoContrato() {
        return renovacaoContrato;
    }

    public void setRenovacaoContrato(boolean renovacaoContrato) {
        this.renovacaoContrato = renovacaoContrato;
    }

    public boolean isEmpresaPermiteRenovarContratosEmTurmasLotadas() {
        return empresaPermiteRenovarContratosEmTurmasLotadas;
    }

    public void setEmpresaPermiteRenovarContratosEmTurmasLotadas(boolean empresaPermiteRenovarContratosEmTurmasLotadas) {
        this.empresaPermiteRenovarContratosEmTurmasLotadas = empresaPermiteRenovarContratosEmTurmasLotadas;
    }
    
    public HorarioTurmaVO obterHorarioTurmaDiaSemana(int diaSemana){
        if (diaSemana == 1) {
            return horarioTurmaDom;
        }
        if (diaSemana == 2) {
            return horarioTurmaSeg;
        }
        if (diaSemana == 3) {
            return horarioTurmaTer;
        }
        if (diaSemana == 4) {
             return horarioTurmaQua;
        }
        if (diaSemana == 5) {
            return horarioTurmaQui;
        }
        if (diaSemana == 6) {
            return horarioTurmaSex;
        }
        if (diaSemana == 7) {
            return horarioTurmaSab;
        }
        return new HorarioTurmaVO();
    }

    public String getTooltipDescDom(){
        if(null != getNivelDescontoTurmaHorarioDom() && null != getHorarioTurmaDom()){
            if(getHorarioTurmaDom().getQuantidadeVagas() > 0){
                return String.format("Ocupação %,.2f%%<br>Desconto para este horário %,.2f%%", getHorarioTurmaDom().getPercOcupacao(), getNivelDescontoTurmaHorarioDom().getPercentualDesconto());
            }else{
                return String.format("Ocupação %,.2f%%", getHorarioTurmaDom().getPercOcupacao());
            }
        }
        return "";
    }

    public String getTooltipDescSeg(){
        if(null != getNivelDescontoTurmaHorarioSeg() && null != getHorarioTurmaSeg()){
            if(getHorarioTurmaSeg().getQuantidadeVagas() > 0){
                return String.format("Ocupação %,.2f%%<br>Desconto para este horário %,.2f%%", getHorarioTurmaSeg().getPercOcupacao(), getNivelDescontoTurmaHorarioSeg().getPercentualDesconto());
            }else{
                return String.format("Ocupação %,.2f%%", getHorarioTurmaSeg().getPercOcupacao());
            }
        }
        return "";
    }

    public String getTooltipDescTer(){
        if(null != getNivelDescontoTurmaHorarioTer() && null != getHorarioTurmaTer()){
            if(getHorarioTurmaTer().getQuantidadeVagas() > 0){
                return String.format("Ocupação %,.2f%%<br>Desconto para este horário %,.2f%%", getHorarioTurmaTer().getPercOcupacao(), getNivelDescontoTurmaHorarioTer().getPercentualDesconto());
            }else{
                return String.format("Ocupação %,.2f%%", getHorarioTurmaTer().getPercOcupacao());
            }
        }
        return "";
    }

    public String getTooltipDescQua(){
        if(null != getNivelDescontoTurmaHorarioQua() && null != getHorarioTurmaQua()){
            if(getHorarioTurmaQua().getQuantidadeVagas() > 0){
                return String.format("Ocupação %,.2f%%<br>Desconto para este horário %,.2f%%", getHorarioTurmaQua().getPercOcupacao(), getNivelDescontoTurmaHorarioQua().getPercentualDesconto());
            }else{
                return String.format("Ocupação %,.2f%%", getHorarioTurmaQua().getPercOcupacao());
            }
        }
        return "";
    }

    public String getTooltipDescQui(){
        if(null != getNivelDescontoTurmaHorarioQui() && null != getHorarioTurmaQui()){
            if(getHorarioTurmaQui().getQuantidadeVagas() > 0){
                return String.format("Ocupação %,.2f%%<br>Desconto para este horário %,.2f%%", getHorarioTurmaQui().getPercOcupacao(), getNivelDescontoTurmaHorarioQui().getPercentualDesconto());
            }else{
                return String.format("Ocupação %,.2f%%", getHorarioTurmaQui().getPercOcupacao());
            }
        }
        return "";
    }

    public String getTooltipDescSex(){
        if(null != getNivelDescontoTurmaHorarioSex() && null != getHorarioTurmaSex()){
            if(getHorarioTurmaSex().getQuantidadeVagas() > 0){
                return String.format("Ocupação %,.2f%%<br>Desconto para este horário %,.2f%%", getHorarioTurmaSex().getPercOcupacao(), getNivelDescontoTurmaHorarioSex().getPercentualDesconto());
            }else{
                return String.format("Ocupação %,.2f%%", getHorarioTurmaSex().getPercOcupacao());
            }

        }
        return "";
    }

    public String getTooltipDescSab(){
        if(null != getNivelDescontoTurmaHorarioSab() && null != getHorarioTurmaSab()){
            if(getHorarioTurmaSab().getQuantidadeVagas() > 0){
                return String.format("Ocupação %,.2f%%<br>Desconto para este horário %,.2f%%", getHorarioTurmaSab().getPercOcupacao(), getNivelDescontoTurmaHorarioSab().getPercentualDesconto());
            }else{
                return String.format("Ocupação %,.2f%%", getHorarioTurmaSab().getPercOcupacao());
            }
        }
        return "";
    }

    public String getSiglaSemana() {
        return siglaSemana;
    }

    public void setSiglaSemana(String siglaSemana) {
        this.siglaSemana = siglaSemana;
    }

    private String obterResultadoOcupacao(Double porcentagem) {
        if (porcentagem <= 20) {
            return "#A6FFA6";
        } else if ( porcentagem > 20 && porcentagem <= 40) {
            return "#A6FFFF";
        } else if ( porcentagem > 40 && porcentagem <= 60) {
            return "#FFFFA6";
        } else if ( porcentagem > 60 && porcentagem <= 80) {
            return "#FEE3BB";
        } else if ( porcentagem > 80) {
            return "#FFA6A6";
        } else {
            return "#A6FFA6";
        }
    }

    public String getResultadoOcupacaoDom() {
        Double qtOcupacao = Double.valueOf(horarioTurmaDom.getNrAlunoMatriculado() + horarioTurmaDom.getNrAlunoEntraramPorReposicao() - horarioTurmaDom.getNrAlunoSairamPorReposicao());
        Double porcentagem = (qtOcupacao * 100) / horarioTurmaDom.getNrMaximoAluno();
        return obterResultadoOcupacao(porcentagem);
    }

    public String getResultadoOcupacaoSeg() {
        Double qtOcupacao = Double.valueOf(horarioTurmaSeg.getNrAlunoMatriculado() + horarioTurmaSeg.getNrAlunoEntraramPorReposicao() - horarioTurmaSeg.getNrAlunoSairamPorReposicao());
        Double porcentagem = (qtOcupacao * 100) / horarioTurmaSeg.getNrMaximoAluno();
        return obterResultadoOcupacao(porcentagem);
    }

    public String getResultadoOcupacaoTer() {
        Double qtOcupacao = Double.valueOf(horarioTurmaTer.getNrAlunoMatriculado() + horarioTurmaTer.getNrAlunoEntraramPorReposicao() - horarioTurmaTer.getNrAlunoSairamPorReposicao());
        Double porcentagem = (qtOcupacao * 100) / horarioTurmaTer.getNrMaximoAluno();
        return obterResultadoOcupacao(porcentagem);
    }

    public String getResultadoOcupacaoQua() {
        Double qtOcupacao = Double.valueOf(horarioTurmaQua.getNrAlunoMatriculado() + horarioTurmaQua.getNrAlunoEntraramPorReposicao() - horarioTurmaQua.getNrAlunoSairamPorReposicao());
        Double porcentagem = (qtOcupacao * 100) / horarioTurmaQua.getNrMaximoAluno();
        return obterResultadoOcupacao(porcentagem);
    }

    public String getResultadoOcupacaoQui() {
        Double qtOcupacao = Double.valueOf(horarioTurmaQui.getNrAlunoMatriculado() + horarioTurmaQui.getNrAlunoEntraramPorReposicao() - horarioTurmaQui.getNrAlunoSairamPorReposicao());
        Double porcentagem = (qtOcupacao * 100) / horarioTurmaQui.getNrMaximoAluno();
        return obterResultadoOcupacao(porcentagem);
    }

    public String getResultadoOcupacaoSex() {
        Double qtOcupacao = Double.valueOf(horarioTurmaSex.getNrAlunoMatriculado() + horarioTurmaSex.getNrAlunoEntraramPorReposicao() - horarioTurmaSex.getNrAlunoSairamPorReposicao());
        Double porcentagem = (qtOcupacao * 100) / horarioTurmaSex.getNrMaximoAluno();
        return obterResultadoOcupacao(porcentagem);
    }

    public String getResultadoOcupacaoSab() {
        Double qtOcupacao = Double.valueOf(horarioTurmaSab.getNrAlunoMatriculado() + horarioTurmaSab.getNrAlunoEntraramPorReposicao() - horarioTurmaSab.getNrAlunoSairamPorReposicao());
        Double porcentagem = (qtOcupacao * 100) / horarioTurmaSab.getNrMaximoAluno();
        return obterResultadoOcupacao(porcentagem);
    }

}

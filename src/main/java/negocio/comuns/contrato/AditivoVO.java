package negocio.comuns.contrato;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;

import java.util.Date;

public class AditivoVO extends SuperVO {
    @ChavePrimaria
    private Integer codigo;
    private String nome;
    private String descricao;
    private Date dataProcessamento;
    private Date dataCriacao;
    private PlanoTextoPadraoVO plano;
    private UsuarioVO responsavel;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Date getDataProcessamento() {
        return dataProcessamento;
    }

    public void setDataProcessamento(Date dataProcessamento) {
        this.dataProcessamento = dataProcessamento;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public PlanoTextoPadraoVO getPlano() {
        return plano;
    }

    public void setPlano(PlanoTextoPadraoVO plano) {
        this.plano = plano;
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }
}


package negocio.comuns.contrato;

import controle.contrato.ParcelasEditarNegociacaoNovo;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import org.apache.commons.lang3.SerializationUtils;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Prorata extends SuperTO {
    private static final long serialVersionUID = 1815778015949828877L;
    private int qtdeDiasTolerancia;
    // dia de vencimento escolhido
    private int diaReferenciaProrata;
    //numero maximo de dias para gerar um mês pro-rata a mais
    private int qtdeDiasAplicarProrata;
    private Date dataInicio;
    private int qtdeDiasCalculado;
    private ContratoVO contratoOriginal;
    private boolean alterouDias = false;

    /**
     * processa o contrato alterando os dados para corresponder ou nao ao pro-rata
     * @param contrato
     * @return ContratoVO
     */
    public ContratoVO processarContrato(ContratoVO contrato) throws Exception {
        // se ainda não houve alteração no contrato original
        String numeroCupomDesconto = contrato.getNumeroCupomDesconto();
        List<ParcelasEditarNegociacaoNovo> parcs = new ArrayList<>(contrato.getListParcelasEditadas()); // guardar parcelas para sair do clone usado somente para o front.
        contrato.setListParcelasEditadas(null);
        if (contratoOriginal == null) {
            contrato.setDiaVencimentoProrata(diaReferenciaProrata);
            try {
                // clona o contrato para voltar se necessario
                contratoOriginal = (ContratoVO) SerializationUtils.clone(contrato);
                // se o contrato já está alterado
            } catch (Exception ignore) {
                contratoOriginal = (ContratoVO) SerializationUtils.clone(contrato);
            }
        } else {
            try {
                // volta ao original para começar um novo pro-rata
                contrato = (ContratoVO) SerializationUtils.clone(contratoOriginal);
            } catch (Exception ignore) {
                contrato = (ContratoVO)  SerializationUtils.clone(contratoOriginal);
            }
            contrato.setDiaVencimentoProrata(diaReferenciaProrata);
        }
        // se algum dia de referência foi escolhido
        if(diaReferenciaProrata > 0) {
            // inicializa dados necessários
            dataInicio = contrato.getVigenciaDe();
            // processamento dos produtos
            contrato = processarMovProdutos(contrato);
        } else {
            contratoOriginal = null;
            setAlterouDias(false);
        }
        contrato.setListParcelasEditadas(parcs);
        contrato.setNumeroCupomDesconto(numeroCupomDesconto);
        return contrato;
    }

    public void verificarConfiguracoesSistema(EmpresaVO empresa, Connection con) throws Exception {
        // pega a configuracao da empresa
        qtdeDiasAplicarProrata = empresa.getNrDiasProrata();
        // se nao existe dado configurado na empresa
        if(qtdeDiasAplicarProrata == 0) {
            ConfiguracaoSistemaVO aux;
            if (con != null) {
                ConfiguracaoSistema confDAO = new ConfiguracaoSistema(con);
                aux = confDAO.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                confDAO = null;
            } else {
                aux = FacadeManager.getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            // pega a configuracao do sistema 
            qtdeDiasAplicarProrata = aux.getNrDiasProrata();
        }
        // tolerancia para considerar os calculos do pro-rata
        qtdeDiasTolerancia = empresa.getToleranciaProrata();
    }

    public boolean verificarTolerancia(int numeroDias) {
         if(numeroDias <= getQtdeDiasAplicarProrata()){
                if( numeroDias <= qtdeDiasTolerancia ){
                    return false;
                }
        } else {
            int mes = Uteis.getMesData(dataInicio)-1;
            int ano = Uteis.getAnoData(dataInicio);

            int mesAuxiliar = mes+1;
            int anoAuxiliar = ano;
            // obs.: para Calendar os meses vão de 0 (janeiro) a 11 (dezembro)
            if(mesAuxiliar > 11) {
                mesAuxiliar = 0;
                anoAuxiliar = ano+1;
            }

            // cria uma data para o dia de referencia pro-rata com o mes e o ano atual
            Calendar aux = Calendario.getInstance();
            aux.set(ano, mes, diaReferenciaProrata);
            // verifico se o nr de dias entre as datas se encontra na tolerancia
          //  Date inicio = Uteis.obterDataAnterior(dataInicio, 1);
            Date inicio = Uteis.obterDataAnterior(dataInicio, 0);
            int nrDias = (int)Uteis.nrDiasEntreDatas(inicio, aux.getTime());

            if (nrDias < 0) {
                aux.set(anoAuxiliar, mesAuxiliar, diaReferenciaProrata);
                inicio = Uteis.obterDataAnterior(dataInicio, 0);
                nrDias = (int)Uteis.nrDiasEntreDatas(inicio, aux.getTime());
            }
            if( Math.abs(nrDias) <= qtdeDiasTolerancia ){
                return false;
            }

            // cria uma data para o dia de referencia pro-rata para o proximo mes e o ano relativo
            aux.set(anoAuxiliar, mesAuxiliar, diaReferenciaProrata);
            // verifico se o nr de dias entre as datas se encontra na tolerancia
            inicio = Uteis.obterDataAnterior(dataInicio, 1);
            nrDias = (int)Uteis.nrDiasEntreDatas(inicio, aux.getTime());
            // se a diferença estiver na tolerancia
            if( Math.abs(nrDias) <= qtdeDiasTolerancia )
                return false;
         }
        return true;
    }

    /**
     * modifica a lista de produtos para corresponder a inclusao do pro-rata no contrato.
     * @param contrato
     */
    public ContratoVO processarMovProdutos(ContratoVO contrato) throws Exception {
        MovProdutoVO aux;
        List<MovProdutoVO> produtos = contrato.getMovProdutoVOs();
        List<MovProdutoVO> ret = new ArrayList<MovProdutoVO>();
        Date dataFinal = null;

        // verifica se o pro-rata devera criar um novo mes (true) ou apenas recalcular o mes atual (false)
        int nrDias = verificaProrata();
        // somente processa os calculos se estiver fora da tolerancia
        boolean calculos = verificarTolerancia(nrDias);
        // somente modifica Mes se o mes for quase cheio e estiver fora da tolerancia
        boolean modificaMes = true;
        // se a qtde de dias entre a data de inicio e a data referencia for menor que o padrao do pro-rata
        // cria-se um novo mes para o contrato.
        setAlterouDias(false);
        if(nrDias <= getQtdeDiasAplicarProrata()) {
            dataFinal = obterDataFinalContrato(contrato, true);
            // somente processa os calculos se estiver fora da tolerancia
            if(calculos) {
                aux = (MovProdutoVO) SerializationUtils.clone(produtos.get(0));
                // altera o prazo do contrato aumentando a vigencia
                aux.setDataFinalVigencia(dataFinal);
                // calcula o valor do produto baseado na qtde de dias ate o vencimento
                aux.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(aux.getTotalFinal() / 30 * nrDias));
                aux.setPrecoUnitario(aux.getTotalFinal());
                aux.setValorDesconto(0.00);
                // adiciona o novo produto no valor do contrato
                contrato.setValorBaseCalculo(contrato.getValorBaseCalculo()+aux.getTotalFinal());
                contrato.setValorFinal(contrato.getValorFinal()+aux.getTotalFinal());
                aux.setDescricao(aux.getDescricao().substring(0, aux.getDescricao().length()-7)+aux.getMesReferencia()+" PRO-RATA "+nrDias);
                contrato.setValorProRata(aux.getTotalFinal());
                // adiciona o novo produto na lista
                ret.add(aux);
                modificaMes = false;
                setAlterouDias(true);
            }

        } else {
            setAlterouDias(true);
            // diminui o prazo do contrato
            dataFinal = obterDataFinalContrato(contrato, false);
        }
        // modifica os outros produtos do contrato
        for (MovProdutoVO produto : produtos) {
            // clona o produto
            aux =(MovProdutoVO) SerializationUtils.clone(produto);
            // se for produto que corresponde ao plano mensal
            if (aux.getProduto().getTipoProduto().equals("PM")) {
                // verifica se modifica o primeiro produto da lista para mes quase cheio
                if(modificaMes) {
                    // verifica se esta fora da tolerancia
                    if (calculos) {
                        // modifica o valor do primeiro mes baseando-se na qtde de dias ate o vencimento
                        aux.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(aux.getTotalFinal() / 30 * nrDias));
                        // modifica o valor do produto
                        aux.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(aux.getTotalFinal() + aux.getValorDesconto()));
                        // modifica os valores do contrato
                        contrato.setValorFinal(contrato.getValorFinal() - (produto.getTotalFinal() - aux.getTotalFinal()));
                        contrato.setValorBaseCalculo(contrato.getValorBaseCalculo() - (produto.getTotalFinal() - aux.getTotalFinal()));
                        contrato.setValorProRata((produto.getTotalFinal() - aux.getTotalFinal()) * -1);
                        // modifica sua descricao
                        aux.setDescricao(aux.getDescricao().substring(0, aux.getDescricao().length() - 7) + aux.getMesReferencia() + " PRO-RATA " + nrDias);
                        // se modificou para um mes quase cheio entao nao deve alterar mais nenhum produto
                        modificaMes = calculos = false;
                    }
                } else if (calculos) {
                    // prepara um novo mes de referencia para este produto
                    int mesReferencia = Integer.valueOf(aux.getMesReferencia().substring(0, 2)) + 1;
                    int anoReferencia = Integer.valueOf(aux.getMesReferencia().substring(3, 7));
                    // se passou do mes 12, pega janeiro do proximo ano
                    if (mesReferencia > 12) {
                        mesReferencia = 1;
                        if (anoReferencia == aux.getAnoReferencia())
                            aux.setAnoReferencia(aux.getAnoReferencia() + 1);
                    }
                    // modifica o mes de referencia do produto
                    aux.setMesReferencia((mesReferencia < 10 ? "0" : "") + mesReferencia + "/" + aux.getAnoReferencia());
                    // muda a descricao do produto
                    aux.setDescricao(aux.getDescricao().substring(0, aux.getDescricao().length() - 7) + aux.getMesReferencia());
                }
            }
            // modifica a data de vigencia do produto diminuindo ou aumentando o prazo
            aux.setDataFinalVigencia(dataFinal);
            // adiciona-o na nova lista de produtos
            ret.add(aux);
        }
        // substitui a lista de produtos
        contrato.setMovProdutoVOs(ret);
        // modifica as data relevantes do contrato
        contrato.setVigenciaAte(dataFinal);
        contrato.setVigenciaAteAjustada(dataFinal);
        contrato.setDataPrevistaRenovar(dataFinal);
        contrato.setDataPrevistaRematricula(dataFinal);
        // retorna o novo contrato
        return contrato;
    }

    /**
     * verifica se o pro-rata devera criar um novo mes ou apenas recalcular o mes inicial
     * @return true se for necessario criar um novo mes
     */
    private int verificaProrata() {
        int dia = Uteis.getDiaMesData(dataInicio);
        int mes = Uteis.getMesData(dataInicio);
        int ano = Uteis.getAnoData(dataInicio);
        if(diaReferenciaProrata >= dia) mes--;
        // cria uma data para o dia de referencia pro-rata com o mes e o ano atual
        Calendar aux = Calendario.getInstance();
        aux.set(ano, mes, diaReferenciaProrata);
        // retorna a qtde de dias entre a data inicio e o vencimento desejado
        return (int)Uteis.nrDiasEntreDatas(dataInicio, aux.getTime());
    }

    public Date obterDataFinalContrato(ContratoVO contrato, boolean aumentarMes) throws Exception {
        // data inicio do contrato - 1 dia
        Date inicio = Uteis.obterDataAnterior(contrato.getVigenciaDe(), 1);
        int diaInicio = Uteis.getDiaMesData(contrato.getVigenciaDe());
        int mesInicio = Uteis.getMesData(contrato.getVigenciaDe()) - 1;
        Calendar aux = Calendario.getInstance();
        // prepara a data de inicio
        aux.setTime(inicio);
        int dia = Uteis.getDiaMesData(inicio);
        int mes = aux.get(Calendar.MONTH);
        int ano = Uteis.getAnoData(inicio);
        if(mesInicio == 0 && mes != 0){
            mesInicio = 12;
        }
        // se o dia da data de inicio for maior que o dia referencia e se nao deve-se aumentar o mes, atrasar um mês para o calculo correto
        if(diaInicio > dia && diaReferenciaProrata > dia && !aumentarMes){
        	mes--;
        }
        // J.A. o sistema faz o calculo retrocedendo um dia do inicio do contrato. 
        // no caso do contrato começar no dia 1, a data base do calculo irá pro mês anterior e isso gerava problema, pois ao somar 
        // a duracao do plano ele começava um mês antes.
        // por isso verificar se o mês calculado é menor do que o mês do inicio do contrato de fato
        if(mesInicio > mes && aumentarMes){
        	mes++;
        }else{
        	//J.A. existe o cenario onde o contrato começa (por exemplo) dia 29, e o dia de referencia escolhido é 1.
        	//neste caso ainda estamos dentro da tolerancia de dias (3) mas não podemos acrescentar apenas um, pois deve-se
        	//considerar a mudança de mês dentro desse periodo de tolerancia.
        	if(diaReferenciaProrata < diaInicio  && aumentarMes){
        		mes++;
        	}
        }
        // modifica a data de inicio para uma data ajustada pelo pro-rata
        aux.set(ano, mes, diaReferenciaProrata);
        // pega a data final do contrato considerando-se a nova data de inicio
        Date data = Uteis.obterDataFuturaParcela(aux.getTime(), contrato.getPlanoDuracao().getNumeroMeses());
        // J.A. verificar se o dia ficou correto, pois pode acontecer do cliente escolher dia 31
        // o mês de inicio não ter 31 dias mas o mês final ter. então verificar se não foi jogada uma data posterior 
        // sem necessidade
        int diaFinal = Uteis.getDiaMesData(data);
        if(diaFinal != diaReferenciaProrata){
        	aux.setTime(data);
        	int mesFinal = mes + contrato.getPlanoDuracao().getNumeroMeses();
        	aux.set(ano, mesFinal, diaReferenciaProrata);
        	data = aux.getTime();
        }
        return Uteis.obterDataAnterior(data, 1);
    }

    public ContratoVO resetarProrata(ContratoVO contrato) throws Exception {
        if(contratoOriginal != null)
            contrato = (ContratoVO) SerializationUtils.clone(contratoOriginal);
        diaReferenciaProrata = 0;
        qtdeDiasAplicarProrata = 0;
        qtdeDiasCalculado = 0;
        dataInicio = null;
        contratoOriginal = null;
        return contrato;
    }

    public int getDiaReferenciaProrata() {
        return diaReferenciaProrata;
    }

    public void setDiaReferenciaProrata(int diaReferenciaProrata) {
        this.diaReferenciaProrata = diaReferenciaProrata;
    }

    public int getQtdeDiasAplicarProrata() {
        return qtdeDiasAplicarProrata;
    }

    public void setQtdeDiasAplicarProrata(int qtdeDiasAplicarProrata) {
        this.qtdeDiasAplicarProrata = qtdeDiasAplicarProrata;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public int getQtdeDiasCalculado() {
        return qtdeDiasCalculado;
    }

    public void setQtdeDiasCalculado(int qtdeDiasCalculado) {
        this.qtdeDiasCalculado = qtdeDiasCalculado;
    }

    public int getQtdeDiasTolerancia() {
        return qtdeDiasTolerancia;
    }

    public void setQtdeDiasTolerancia(int qtdeDiasTolerancia) {
        this.qtdeDiasTolerancia = qtdeDiasTolerancia;
    }

    public boolean isAlterouDias() {
        return alterouDias;
    }

    public void setAlterouDias(boolean alterouDias) {
        this.alterouDias = alterouDias;
    }

    public ContratoVO getContratoOriginal() {
        return contratoOriginal;
    }

    public void setContratoOriginal(ContratoVO contratoOriginal) {
        this.contratoOriginal = contratoOriginal;
    }
}

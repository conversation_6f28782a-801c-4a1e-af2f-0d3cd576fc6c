package negocio.comuns.contrato;

public enum TipoOperacaoColetivaEnum {
    AFASTAMENTO_COLETIVO(1, "Afastamento Coletivo"),
    AFASTAMENTO_COLETIVO_ALTERANDO_PARCELAS(2, "Afastamento Coletivo alterando vencimento de todas as parcelas do contrato"),
    AFASTAMENTO_COLETIVO_ALTERANDO_PARCELAS_SOMENTE_PLANOS(3, "Afastamento Coletivo alterando vencimento das parcelas somente de planos"),
    DESMARCAR_AULAS_ALUNOS_PERIODO(4, "Desmarcar aulas de turmas dos contratos dos alunos"),
    BLOQUEAR_ACESSO_CATRACA_POR_PERIODO(5, "Bloqueio de aluno na catraca");

    private Integer codigo;
    private String descricao;
    TipoOperacaoColetivaEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TipoOperacaoColetivaEnum getTipoOperacaoColetiva(Integer codigo) {
        for (TipoOperacaoColetivaEnum obj : TipoOperacaoColetivaEnum.values()) {
            if (obj.getCodigo().equals(codigo)) {
                return obj;
            }
        }
        return null;
    }

    public static String getDescricao(Integer codigo) {
        for (TipoOperacaoColetivaEnum obj : TipoOperacaoColetivaEnum.values()) {
            if (obj.getCodigo().equals(codigo)) {
                return obj.getDescricao();
            }
        }
        return "";
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}


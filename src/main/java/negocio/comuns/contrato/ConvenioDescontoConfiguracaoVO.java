package negocio.comuns.contrato;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Reponsável por manter os dados da entidade ConvenioDescontoConfiguracao. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 * @see negocio.facade.jdbc.contrato.ConvenioDesconto
 */
public class ConvenioDescontoConfiguracaoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Double valorDesconto;
    protected Double porcentagemDesconto;
    protected String tipoDesconto;
    protected Integer convenioDesconto;
    protected Integer duracao;
    protected Boolean convenioDescontoConfiguracaoEscolhida;
    @NaoControlarLogAlteracao
    protected Boolean apresentarPorcentagem;
    @NaoControlarLogAlteracao
    protected Boolean apresentarValorEspecifico;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Duracao </code>.*/


    /**
     * Construtor padrão da classe <code>ConvenioDescontoConfiguracao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ConvenioDescontoConfiguracaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ConvenioDescontoConfiguracaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(ConvenioDescontoConfiguracaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getDuracao() == 0)) {
            throw new ConsistirException("O campo DURAÇÃO (Configuração do Convênio de Desconto) deve ser informado.");
        }
        if (obj.getTipoDesconto() == null || obj.getTipoDesconto().equals("")) {
            throw new ConsistirException("O campo TIPO DE DESCONTO (Configuração do Convênio de Desconto) deve ser informado.");
        }
        if (obj.getTipoDesconto().equals("PD") && obj.getPorcentagemDesconto() == 0.0) {
            throw new ConsistirException("O campo VALOR PORCENTAGEM DESCONTO (Configuração do Convênio de Desconto) deve ser informado.");
        }
        if (obj.getTipoDesconto().equals("VD") && obj.getValorDesconto() == 0.0) {
            throw new ConsistirException("O campo VALOR DESCONTO(Configuração do Convênio de Desconto) deve ser informado.");
        }

    }

    public String getPorcentagemDescontoApresentar() {
        if (!UteisValidacao.emptyNumber(valorDesconto)) {
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula(valorDesconto);
        } else {
            return Formatador.formatarValorPercentual(porcentagemDesconto / 100);
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setTipoDesconto(getTipoDesconto().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setValorDesconto(0.0);
        setPorcentagemDesconto(0.0);
        setTipoDesconto("");
        setDuracao(0);
        setConvenioDescontoConfiguracaoEscolhida(false);
        setApresentarPorcentagem(false);
        setApresentarValorEspecifico(false);
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getConvenioDesconto() {
        return (convenioDesconto);
    }

    public void setConvenioDesconto(Integer convenioDesconto) {
        this.convenioDesconto = convenioDesconto;
    }

    public String getTipoDesconto() {
        if (tipoDesconto == null) {
            tipoDesconto = "";
        }
        return (tipoDesconto);
    }

    public void setTipoDesconto(String tipoDesconto) {
        this.tipoDesconto = tipoDesconto;
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico.
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc.
     */
    public String getTipoDesconto_Apresentar() {
        if (tipoDesconto == null) {
            tipoDesconto = "";
        }
        if (tipoDesconto.equals("VE")) {
            return "Valor Específico";
        }
        if (tipoDesconto.equals("PD")) {
            return "Porcentagem de Desconto";
        }
        return (tipoDesconto);
    }

    public Double getPorcentagemDesconto() {
        return (porcentagemDesconto);
    }

    public void setPorcentagemDesconto(Double porcentagemDesconto) {
        this.porcentagemDesconto = porcentagemDesconto;
    }

    public Double getValorDesconto() {
        return (valorDesconto);
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getConvenioDescontoConfiguracaoEscolhida() {
        return convenioDescontoConfiguracaoEscolhida;
    }

    public void setConvenioDescontoConfiguracaoEscolhida(Boolean convenioDescontoConfiguracaoEscolhida) {
        this.convenioDescontoConfiguracaoEscolhida = convenioDescontoConfiguracaoEscolhida;
    }

    public Boolean getApresentarPorcentagem() {
        return apresentarPorcentagem;
    }

    public void setApresentarPorcentagem(Boolean apresentarPorcentagem) {
        this.apresentarPorcentagem = apresentarPorcentagem;
    }

    public Boolean getApresentarValorEspecifico() {
        return apresentarValorEspecifico;
    }

    public void setApresentarValorEspecifico(Boolean apresentarValorEspecifico) {
        this.apresentarValorEspecifico = apresentarValorEspecifico;
    }


}
package negocio.comuns.contrato;

import java.text.ParseException;
import java.util.Date;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.MovProduto;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.utilitarias.Calendario;

/**
 *
 * <AUTHOR>
 */
public class TrancamentoContratoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected ContratoVO contratoVO;
    protected Double valorCongelado;
    protected Integer nrDiasCongelado;
    protected Integer nrDiasBonus;
    protected Integer empresa;
    protected Date dataTrancamento;
    protected Date dataFimTrancamento;
    protected Date dataRetorno;
    protected Date dataRegistro;
    protected Double valorTrancamento;
    protected Integer tipoJustificativa;
    protected String observacao;
    protected UsuarioVO responsavelOperacao;
    @NaoControlarLogAlteracao
    protected ProdutoVO produtoTrancamento;
    protected Boolean mensagemErro;
    protected Boolean apresentarPeriodoTrancamento;
    protected Integer nrDiasContrato;
    protected Integer nrDiasUtilizadosPeloClienteContrato;
    protected Double valorBaseContrato;
    protected Double valorDiaContratoValorBase;
    protected Double valorUtilizadoPeloClienteBase;
    protected Boolean apresentarPanelClienteRetornoNoPrazo;
    protected Boolean apresentarPanelClienteRetornoForaPrazo;
    protected Boolean taxaDiasExcedidos;
    protected Boolean descontoDiasExcedidos;
    protected Integer numeroDiasDescontar;
    private UsuarioVO responsavelAlteracaoDataRetroativa;
    private Boolean contratoVencido;
    private ContratoOperacaoVO contratoOperacaoVO;
    private Date dataPrimeiroTrancamentoSeq; //armazena a data do primeiro trancamento de uma sequencia consecutiva de  trancamentos
    private List<MatriculaAlunoHorarioTurmaVO> matriculaVigentes;
    private  Boolean mensagemManutencao;
    private OrigemSistemaEnum origemSistema;
    private boolean alterarVencimentoparcelas = false;
    private UsuarioVO responsavelAlteracaoDataVencimentoParcelas;

    public TrancamentoContratoVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setCodigo(new Integer(0));
        setContratoVO(new ContratoVO());
        setResponsavelOperacao(new UsuarioVO());
        setProdutoTrancamento(new ProdutoVO());
        setDataTrancamento(Calendario.hoje());
        setDataFimTrancamento(Calendario.hoje());
        setDataRetorno(Calendario.hoje());
        setDataRegistro(Calendario.hoje());
        setObservacao("");
        setMensagemErro(false);
        setValorTrancamento((double) 0);
        setTipoJustificativa(0);
        setApresentarPeriodoTrancamento(false);
        setValorCongelado((double) 0);
        setNrDiasCongelado(0);
        setNrDiasContrato(0);
        setNrDiasUtilizadosPeloClienteContrato(0);
        setValorBaseContrato((double) 0);
        setValorDiaContratoValorBase((double) 0);
        setValorUtilizadoPeloClienteBase((double) 0);
        setEmpresa(0);
        setApresentarPanelClienteRetornoForaPrazo(false);
        setApresentarPanelClienteRetornoNoPrazo(false);
        setTaxaDiasExcedidos(false);
        setDescontoDiasExcedidos(false);
        setNumeroDiasDescontar(0);
        setNrDiasBonus(0);
        setResponsavelAlteracaoDataRetroativa(new UsuarioVO());
        setContratoVencido(Boolean.FALSE);
        matriculaVigentes = new ArrayList<MatriculaAlunoHorarioTurmaVO>();
        setMensagemManutencao(false);
        setAlterarVencimentoparcelas(false);
        setResponsavelAlteracaoDataVencimentoParcelas(new UsuarioVO());
    }

    public static void validarDados(TrancamentoContratoVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }

        if (obj.getDataTrancamento() == null) {
            throw new ConsistirException("O campo DATA TRANCAMENTO (Trancamento) deve ser informado. ");
        }

        if (obj.getProdutoTrancamento() == null || obj.getProdutoTrancamento().getCodigo() == 0) {
            throw new ConsistirException("O Produto referente ao TRANCAMENTO deve ser informado.");
        }
        if (obj.getTipoJustificativa().equals(0)) {
            throw new ConsistirException("A JUSTIFICATIVA DO TRANCAMENTO deve ser Informada");
        }
    }

    public static void validarDadosRetorno(TrancamentoContratoVO obj) throws ConsistirException, ParseException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getApresentarPanelClienteRetornoForaPrazo()) {
            if (!obj.getTaxaDiasExcedidos() && !obj.getDescontoDiasExcedidos()) {
                throw new ConsistirException("O Campo (Pagar Taxa pelos Dias Excedidos ou Descontar Dias Excedidos) deve ser Informada");
            }
            if (obj.getTaxaDiasExcedidos()) {
                if (obj.produtoTrancamento == null || obj.produtoTrancamento.getCodigo() == 0) {
                    throw new ConsistirException("O Produto referente ao TRANCAMENTO deve ser informado.");
                }
                if (obj.tipoJustificativa.equals(0)) {
                    throw new ConsistirException("A JUSTIFICATIVA DO TRANCAMENTO deve ser Informada");
                }
            }
        }
    }

    public void gerarParcela(Boolean taxaDias) throws Exception {
        MovParcelaVO parcela = new MovParcelaVO();
        parcela.setContrato(this.contratoVO);
        parcela.setDataRegistro(Calendario.hoje());
        if(getDataRegistro().after(getDataTrancamento())){
            parcela.setDataRegistro(getDataTrancamento());
        }
        if (taxaDias) {
            parcela.setDataVencimento(Calendario.hoje());
        } else {
            parcela.setDataVencimento(this.dataTrancamento);
        }
        parcela.setDescricao("Trancamento");
        parcela.setResponsavel(this.responsavelOperacao);
        if (this.valorTrancamento == 0) {
            parcela.setSituacao("PG");
        } else {
            parcela.setSituacao("EA");
        }
        parcela.setValorBaseCalculo(this.valorTrancamento);
        parcela.setValorParcela(this.valorTrancamento);
        parcela.setEmpresa(contratoVO.getEmpresa());
        parcela.setPessoa(contratoVO.getPessoa());
        gerarMovProduto(parcela, taxaDias);
        getFacade().getZWFacade().incluirMovParcelaSemCommit(parcela);
    }

    public void gerarMovProduto(MovParcelaVO parcelaVO, Boolean taxaDias) throws Exception {
        MovProdutoVO movProdutoVO = new MovProdutoVO();
        if (taxaDias) {
            movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(Calendario.hoje()));
            movProdutoVO.setAnoReferencia(Uteis.getAnoData(Calendario.hoje()));
            movProdutoVO.setDataLancamento(Calendario.hoje());
        } else {
            movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(parcelaVO.getDataVencimento()));
            movProdutoVO.setAnoReferencia(Uteis.getAnoData(parcelaVO.getDataVencimento()));
            movProdutoVO.setDataLancamento(parcelaVO.getDataVencimento());
        }
        movProdutoVO.setContrato(this.contratoVO);
        movProdutoVO.setProduto(this.produtoTrancamento);
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao("Trancamento Contrato");
        movProdutoVO.setQuantidade(1);
        movProdutoVO.setResponsavelLancamento(this.responsavelOperacao);
        movProdutoVO.setPrecoUnitario(this.valorTrancamento);
        movProdutoVO.setTotalFinal(movProdutoVO.getPrecoUnitario());
        movProdutoVO.getEmpresa().setCodigo(this.contratoVO.getEmpresa().getCodigo());
        movProdutoVO.setPessoa(this.contratoVO.getPessoa());
        if (this.valorTrancamento == 0) {
            movProdutoVO.setQuitado(true);
            movProdutoVO.setSituacao("PG");
        } else {
            movProdutoVO.setQuitado(false);
            movProdutoVO.setSituacao("EA");
        }
        new MovProduto().incluirSemCommit(movProdutoVO);
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProduto(movProdutoVO.getCodigo());
        movProdutoParcela.setMovProdutoVO(movProdutoVO);
        movProdutoParcela.setValorPago(movProdutoVO.getTotalFinal());
        parcelaVO.getMovProdutoParcelaVOs().add(movProdutoParcela);
    }

    public void modificarPeriodoAcesso() throws Exception {
        List<PeriodoAcessoClienteVO> listaPeriodos = getFacade().getPeriodoAcessoCliente().consultarPorVigenteOuFuturoContrato( this.dataTrancamento, this.contratoVO.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
        for (PeriodoAcessoClienteVO periodoAcesso : listaPeriodos) {
            if (Uteis.getCompareData(periodoAcesso.getDataInicioAcesso(), this.dataTrancamento) == 0) {
                getFacade().getPeriodoAcessoCliente().excluirSemCommit(periodoAcesso);
            } else {
                periodoAcesso.setDataFinalAcesso(Uteis.obterDataAnterior(this.dataTrancamento, 1));
                getFacade().getPeriodoAcessoCliente().alterarSemCommit(periodoAcesso);
            }
        }
    }

    public void gravarPeriodoAcesso() throws Exception {
        PeriodoAcessoClienteVO obj = new PeriodoAcessoClienteVO();
        obj.setPessoa(getContratoVO().getPessoa().getCodigo());
        obj.setContrato(getContratoVO().getCodigo());
        obj.setTipoAcesso("TR");
        obj.setDataFinalAcesso(getDataFimTrancamento());
        obj.setDataInicioAcesso(getDataTrancamento());
        getFacade().getPeriodoAcessoCliente().incluirSemCommit(obj);
    }

    public void modificarPeriodoAcessoRetorno() throws Exception {
        PeriodoAcessoClienteVO periodoAcessoAnterior = getFacade().getPeriodoAcessoCliente().consultarPorDataEspecificaECodigoContrato(Calendario.hoje(), getContratoVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (periodoAcessoAnterior != null) {
            if ((Uteis.getCompareData(periodoAcessoAnterior.getDataFinalAcesso(), Calendario.hoje()) >= 0)) {
                periodoAcessoAnterior.setDataFinalAcesso(Uteis.obterDataAnterior(Calendario.hoje(), 1));
                //se a alteração da data deixou a data final menor que a data inicial, atribuir a data inicial na data final
                if (Calendario.menor(periodoAcessoAnterior.getDataFinalAcesso(),
                        periodoAcessoAnterior.getDataInicioAcesso())) {
                    periodoAcessoAnterior.setDataFinalAcesso(
                            periodoAcessoAnterior.getDataInicioAcesso());
                }
                getFacade().getPeriodoAcessoCliente().alterarSemCommit(periodoAcessoAnterior);
            }
        }
        PeriodoAcessoClienteVO periodoRetorno = new PeriodoAcessoClienteVO();
        periodoRetorno.setContrato(getContratoVO().getCodigo());
        if (periodoAcessoAnterior != null
                && Calendario.igual(periodoAcessoAnterior.getDataFinalAcesso(), Calendario.hoje())) {

            periodoRetorno.setDataInicioAcesso(Uteis.obterDataFutura2(Calendario.hoje(), 1));

        } else {
            periodoRetorno.setDataInicioAcesso(Calendario.hoje());

        }
        periodoRetorno.setDataInicioAcesso(Calendario.hoje());
        periodoRetorno.setDataFinalAcesso(getContratoVO().getVigenciaAteAjustada());
        periodoRetorno.setPessoa(getContratoVO().getPessoa().getCodigo());
        periodoRetorno.setTipoAcesso("RT");
        getFacade().getPeriodoAcessoCliente().incluirSemCommit(periodoRetorno);
    }

    public void modificarPeriodoAcessoRetornoForaPrazo() throws Exception {
        new PeriodoAcessoClienteVO();// Inicialização de dados
        PeriodoAcessoClienteVO obj = getFacade().getPeriodoAcessoCliente().consultarPorDataEspecificaECodigoContrato(Calendario.hoje(), this.contratoVO.getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
        if (obj != null) {
            obj.setDataFinalAcesso(Uteis.obterDataAnterior(Calendario.hoje(), 1));
            getFacade().getPeriodoAcessoCliente().alterarSemCommit(obj);
        }
        PeriodoAcessoClienteVO periodoAcesso = new PeriodoAcessoClienteVO();
        periodoAcesso.setContrato(getContratoVO().getCodigo());
        periodoAcesso.setDataInicioAcesso(Calendario.hoje());
        periodoAcesso.setDataFinalAcesso(getContratoVO().getVigenciaAteAjustada());
        periodoAcesso.setPessoa(getContratoVO().getPessoa().getCodigo());
        periodoAcesso.setTipoAcesso("RT");
        getFacade().getPeriodoAcessoCliente().incluirSemCommit(periodoAcesso);
    }

    public void inicializarDadosOperacaoContrato() throws Exception {
        ContratoOperacaoVO obj = getFacade().getContratoOperacao().consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(this.dataTrancamento, getContratoVO().getCodigo(), "TR",false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (obj == null || obj.getCodigo() == 0) {
            obj = getFacade().getContratoOperacao().consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(this.dataTrancamento, getContratoVO().getCodigo(), "TV",false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        if (obj != null) {
            if (Uteis.getCompareData(this.dataTrancamento, obj.getDataInicioEfetivacaoOperacao()) > 0) {
                obj.setDataFimEfetivacaoOperacao(Uteis.obterDataAnterior(this.dataTrancamento, 1));
                getFacade().getContratoOperacao().alterarSemCommit(obj);
            }
        }
        ContratoOperacaoVO retorno = getFacade().getContratoOperacao().consultarOperacaoContratoApenasInicioCodigoContratoTipoOperacao(this.dataTrancamento, getContratoVO().getCodigo(), "RT",false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (retorno != null && !retorno.getCodigo().equals(0)) {
            getFacade().getContratoOperacao().excluirSemCommit(retorno); //exclui retorno no mesmo dia de inicio do trancamento
        }
        obj = new ContratoOperacaoVO();
        obj.setContrato(this.contratoVO.getCodigo());
        obj.setDataFimEfetivacaoOperacao(this.dataFimTrancamento);
        obj.setDataInicioEfetivacaoOperacao(this.dataTrancamento);
        obj.setDataOperacao(Calendario.hoje());
        long dias = Uteis.nrDiasEntreDatas(dataTrancamento, dataFimTrancamento) + 1;
        if (Uteis.getCompareData(getDataTrancamento(), Calendario.hoje()) >= 0) {
            obj.setDescricaoCalculo("Operação de trancamento.\n\r"
                    + " foi lançado no contrato uma paralização de " + dias + " dias.\r\n"
                    + " que começa no dia " + Uteis.getData(dataTrancamento) + " e termina no dia " + Uteis.getData(dataFimTrancamento) + ".\n\r"
                    + "Número de dias congelados: " + nrDiasCongelado + "\n\r"
                    + "Valor congelado: " +  Formatador.formatarValorMonetario(valorCongelado) + "\n\r"
                    + " Vigência do contrato será ajustada após retorno do trancamento,"
                    + " Pois depende da quantidade de dias em que o contrato ficou trancado.");

        } else {
            obj.setDescricaoCalculo("Operação de trancamento retroativo.\r\n"
                    + " pois no dia: " + Uteis.getData(getDataRegistro()) + ",\n\r"
                    + " foi lançado no contrato uma paralização de " + dias + " dias.\r\n"
                    + " que começa no dia " + Uteis.getData(dataTrancamento) + " e termina no dia " + Uteis.getData(dataFimTrancamento) + ".\n\r"
                    + "Número de dias congelados: " + nrDiasCongelado + ".\n\r"
                    + "Valor congelado: " +  Formatador.formatarValorMonetario(valorCongelado) + ".\n\r"
                    + " Vigência do contrato será ajustada após retorno do trancamento,"
                    + " Pois depende da quantidade de dias em que o  contrato ficou trancado.\r\n"
                    + " A Alteração da data de trancamento para uma data retroativa foi autorizada por " + getResponsavelAlteracaoDataRetroativa().getNome());
        }

        if(this.alterarVencimentoparcelas){
            obj.setDescricaoCalculo(obj.getDescricaoCalculo() + "\n\r" +
                    "Parcelas em aberto com vencimento superior ao início do trancamento, tiveram os vencimentos alterados, autorizado por "+getResponsavelAlteracaoDataVencimentoParcelas().getNome());
        }
        obj.setObservacao(this.observacao);
        obj.setOperacaoPaga(true);
        obj.setResponsavel(this.responsavelOperacao);
        obj.setTipoOperacao("TR");
        obj.getTipoJustificativa().setCodigo(this.tipoJustificativa);
        obj.setOrigemSistema(this.getOrigemSistema());
        getFacade().getContratoOperacao().incluirSemCommit(obj, false);
        this.setContratoOperacaoVO(obj);
        List<ContratoOperacaoVO> lista = getFacade().getContratoOperacao().consultarPorContrato(getContratoVO().getCodigo(), false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (ContratoOperacaoVO op : lista) {
            if (op.getTipoOperacao().equals("TV") && Calendario.igual(op.getDataInicioEfetivacaoOperacao(), dataTrancamento)) {
                getFacade().getContratoOperacao().excluirSemCommit(op);
            }
        }
    }

    public void inicializarDadosOperacaoContratoRetornoNoPrazo() throws Exception {
        ContratoOperacaoVO operacaoAnterior = getFacade().getContratoOperacao().consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(Calendario.hoje(), getContratoVO().getCodigo(), "TR", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (operacaoAnterior == null || operacaoAnterior.getCodigo() == 0) {
            operacaoAnterior = getFacade().getContratoOperacao().consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(Calendario.hoje(), getContratoVO().getCodigo(), "TV",false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        if (operacaoAnterior != null) {
            if (operacaoAnterior.getTrancamentoVencido()) {
                getFacade().getContratoOperacao().excluirSemCommit(operacaoAnterior);
                //se houver outra operação no mesmo período do trancamento vencido também deve ajustar
                operacaoAnterior = getFacade().getContratoOperacao().
                        obterOperacaoContratoPorDataEspecifica(Calendario.hoje(),
                                getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (operacaoAnterior != null) {

                    operacaoAnterior.setDataFimEfetivacaoOperacao(Uteis.obterDataAnterior(Calendario.hoje(), 1));
                    //se a alteração da data deixou a data final menor que a data inicial, atribuir a data inicial na data final
                    if (Calendario.menor(operacaoAnterior.getDataFimEfetivacaoOperacao(),
                            operacaoAnterior.getDataInicioEfetivacaoOperacao())) {
                        operacaoAnterior.setDataFimEfetivacaoOperacao(operacaoAnterior.getDataInicioEfetivacaoOperacao());
                    }
                    getFacade().getContratoOperacao().alterarSemCommit(operacaoAnterior);

                }
            } else {
                operacaoAnterior.setDataFimEfetivacaoOperacao(Uteis.obterDataAnterior(Calendario.hoje(), 1));
                //se a alteração da data deixou a data final menor que a data inicial, atribuir a data inicial na data final
                if (Calendario.menor(operacaoAnterior.getDataFimEfetivacaoOperacao(),
                        operacaoAnterior.getDataInicioEfetivacaoOperacao())) {
                    operacaoAnterior.setDataFimEfetivacaoOperacao(operacaoAnterior.getDataInicioEfetivacaoOperacao());
                }
                getFacade().getContratoOperacao().alterarSemCommit(operacaoAnterior);
            }
        }
        ContratoOperacaoVO obj = new ContratoOperacaoVO();
        obj.setContrato(this.contratoVO.getCodigo());
        if (operacaoAnterior != null
                && Calendario.igual(operacaoAnterior.getDataFimEfetivacaoOperacao(), Calendario.hoje())) {

            obj.setDataInicioEfetivacaoOperacao(Uteis.obterDataFutura2(Calendario.hoje(), 1));
        } else {
            obj.setDataInicioEfetivacaoOperacao(Calendario.hoje());
        }
        obj.setDataFimEfetivacaoOperacao(getContratoVO().getVigenciaAteAjustada());
        obj.setDataOperacao(Calendario.hoje());
        obj.setDescricaoCalculo("Retorno do trancamento \r\n"
                + "O cliente retornou de sua paralização. Modificando assim a data término do contrato, data prevista para renovar e a data prevista para rematricula  para o dia: " + getContratoVO().getVigenciaAteAjustada_Apresentar());
        obj.setObservacao(this.observacao);
        obj.setOperacaoPaga(false);
        obj.setResponsavel(this.responsavelOperacao);
        obj.setTipoOperacao("RT");
        getFacade().getContratoOperacao().incluirSemCommit(obj, false);
        this.setContratoOperacaoVO(obj);
    }

    public void inicializarDadosOperacaoContratoRetornoForaPrazo() throws Exception {
        ContratoOperacaoVO obj = getFacade().getContratoOperacao().consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(Calendario.hoje(), getContratoVO().getCodigo(), "TR",false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (obj == null || obj.getCodigo() == 0) {
            obj = getFacade().getContratoOperacao().consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(Calendario.hoje(), getContratoVO().getCodigo(), "TV",false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        if (obj != null) {
            obj.setDataFimEfetivacaoOperacao(Uteis.obterDataAnterior(Calendario.hoje(), 1));
            getFacade().getContratoOperacao().alterarSemCommit(obj);
        }
        obj = new ContratoOperacaoVO();
        obj.setContrato(this.contratoVO.getCodigo());
        obj.setDataInicioEfetivacaoOperacao(Calendario.hoje());
        obj.setDataOperacao(Calendario.hoje());
        if(getContratoVencido()){
            obj.setDataFimEfetivacaoOperacao(Calendario.hoje());
            obj.setDescricaoCalculo("O Cliente Retornou do Trancamento sabendo que tinha mais dias a descontar do que dias congelados. Portanto seu contrato foi cancelado.\n\r"
                    + "Modificando assim a data término ajustada do contrato, data prevista para renovar e a data prevista para rematricula para o dia " + getContratoVO().getVigenciaAteAjustada_Apresentar());

        }else{
            obj.setDataFimEfetivacaoOperacao(getContratoVO().getVigenciaAteAjustada());
            if (Calendario.menor(obj.getDataFimEfetivacaoOperacao(), obj.getDataInicioEfetivacaoOperacao())) {
                obj.setDataFimEfetivacaoOperacao(Calendario.hoje());
            }
            obj.setDescricaoCalculo("O Cliente Retornou do Trancamento sabendo que perdeu " + getNumeroDiasDescontar().toString() + "  dias no contrato.\n\r"
                    + "pois ele retornou do trancamento Vencido. Modificando assim a data término ajustada do contrato, data prevista para renovar e a data prevista para rematricula para o dia " + getContratoVO().getVigenciaAteAjustada_Apresentar());
        }
        obj.setObservacao(this.observacao);
        obj.setOperacaoPaga(false);
        obj.setResponsavel(this.responsavelOperacao);
        obj.setTipoOperacao("RT");
        getFacade().getContratoOperacao().incluirSemCommit(obj, false);
        this.setContratoOperacaoVO(obj);
    }

    public void inicializarDadosHistoricoContratoRetornoNoPrazo() throws Exception {
        HistoricoContratoVO histAnterior = getFacade().getHistoricoContrato().obterHistoricoContratoPorDataEspecifica(
                getContratoVO().getCodigo(),
                Calendario.hoje(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (histAnterior != null) {
            histAnterior.setDataFinalSituacao(Uteis.obterDataAnterior(Calendario.hoje(), 1));
            //se a alteração da data deixou a data final menor que a data inicial, atribuir a data inicial na data final
            if (Calendario.menor(histAnterior.getDataFinalSituacao(), histAnterior.getDataInicioSituacao())) {
                histAnterior.setDataFinalSituacao(histAnterior.getDataInicioSituacao());
            }
            getFacade().getHistoricoContrato().alterarSemCommit(histAnterior, true);
        }
        HistoricoContratoVO histRetorno = new HistoricoContratoVO();
        histRetorno.setContrato(this.contratoVO.getCodigo());
        if (histAnterior != null
                && Calendario.igual(histAnterior.getDataFinalSituacao(), Calendario.hoje())) {

            histRetorno.setDataInicioSituacao(Uteis.obterDataFutura2(Calendario.hoje(), 1));
        } else {
            histRetorno.setDataInicioSituacao(Calendario.hoje());
        }
        histRetorno.setDataFinalSituacao(getContratoVO().getVigenciaAteAjustada());
        histRetorno.setDataRegistro(Calendario.hoje());
        histRetorno.setDescricao("RETORNO TRANCAMENTO");
        histRetorno.setTipoHistorico("RT");
        histRetorno.setRetornoManual(true);
        histRetorno.setResponsavelRegistro(this.responsavelOperacao);
        histRetorno.setSituacaoRelativaHistorico("");
        ValidacaoHistoricoContrato.validarPeriodoHistoricoContratoOperacao(
                histRetorno.getDataInicioSituacao(),
                histRetorno.getDataFinalSituacao(),
                histRetorno.getContrato(),
                getFacade().getHistoricoContrato(), histRetorno);
        getFacade().getHistoricoContrato().incluirSemCommit(histRetorno, true);
    }

    public void inicializarDadosHistoricoContratoRetornoForaPrazo() throws Exception {
        new HistoricoContratoVO(); //Inicialização de dados
        HistoricoContratoVO obj = getFacade().getHistoricoContrato().obterHistoricoContratoPorDataEspecifica(getContratoVO().getCodigo(), Calendario.hoje(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (obj != null) {
            if (getContratoVencido()) {
               obj.setDataFinalSituacao(Uteis.obterDataAnterior(getContratoVO().getVigenciaAteAjustada(), 1));
            }else{
               obj.setDataFinalSituacao(Uteis.obterDataAnterior(Calendario.hoje(), 1));
            }

            getFacade().getHistoricoContrato().alterarSemCommit(obj, true);
        }
        obj = new HistoricoContratoVO();
        obj.setContrato(this.contratoVO.getCodigo());
        if (getContratoVencido()) {
            obj.setDataInicioSituacao(getContratoVO().getVigenciaAteAjustada());
            obj.setDataFinalSituacao(getContratoVO().getVigenciaAteAjustada());
        } else {
           obj.setDataInicioSituacao(Calendario.hoje());
           obj.setDataFinalSituacao(getContratoVO().getVigenciaAteAjustada());
            if (Calendario.menor(obj.getDataFinalSituacao(), obj.getDataInicioSituacao())) {
                obj.setDataFinalSituacao(Calendario.hoje());
            }
        }
        obj.setDataRegistro(Calendario.hoje());
        obj.setDescricao("RETORNO TRANCAMENTO");
        obj.setTipoHistorico("RT");
        obj.setResponsavelRegistro(this.responsavelOperacao);
        obj.setSituacaoRelativaHistorico("");
        obj.setRetornoManual(true);
        getFacade().getHistoricoContrato().incluirSemCommit(obj, true);
    }

    public void validarSeExisteOperacaoHistorioTrancamentoVencido() throws Exception {
        ContratoOperacaoVO operacaoRetorno = getFacade().getContratoOperacao().obterUltimaOperacaoContratoPorCodigoContratoTipoOperacao(getContratoVO().getCodigo(), "TV", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        HistoricoContratoVO historicoRetorno = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContratoTipoHistorico(getContratoVO().getCodigo(), "TV", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (operacaoRetorno != null) {
            if (Uteis.getCompareData(operacaoRetorno.getDataInicioEfetivacaoOperacao(), Calendario.hoje()) == 0) {
                getFacade().getContratoOperacao().excluirSemCommit(operacaoRetorno);
            }
        }
        if (historicoRetorno != null) {
            if (Uteis.getCompareData(historicoRetorno.getDataInicioSituacao(), Calendario.hoje()) == 0) {
                getFacade().getHistoricoContrato().excluir(historicoRetorno);
            }
        }
    }

    public void validarContratoRenovadosAntecipados() throws Exception {
        if (getContratoVO().getContratoResponsavelRenovacaoMatricula() != 0) {
            ContratoVO objContratoVO = getFacade().getContrato().consultarPorChavePrimaria(getContratoVO().getContratoResponsavelRenovacaoMatricula(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (objContratoVO.getSituacaoRenovacao().equals("AN")) {
                Date data = objContratoVO.getVigenciaDe();
                inicializarDadosContratoRenovacao(objContratoVO, getContratoVO());
                inicializarDadosPeriodoAcessoContratoRenovacao(objContratoVO, data);
                inicializarDadosContratoOperacaoContratoRenovacao(objContratoVO, getContratoVO());
                inicializarDadosHistoricoContratoContratoRenovacao(objContratoVO, getContratoVO());
            }
        }
    }


    public void inicializarDadosContratoOperacaoContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj) throws Exception {
        ContratoOperacaoVO objContratoOperacaoVO = new ContratoOperacaoVO();
        objContratoOperacaoVO.setContrato(contratoRenovacao.getCodigo());
        objContratoOperacaoVO.setDataFimEfetivacaoOperacao(Calendario.hoje());
        objContratoOperacaoVO.setDataInicioEfetivacaoOperacao(Calendario.hoje());
        objContratoOperacaoVO.setDataOperacao(Calendario.hoje());
        objContratoOperacaoVO.setDescricaoCalculo("");
        objContratoOperacaoVO.setObservacao("Modificações: \n\r"
                + " Data de Início,\n\r"
                + " Data de Término,\n\r"
                + " Data de Previsão Renovação,\n\r"
                + " Data de Previsão Rematricula,\n\r"
                + " Devido uma OPERAÇÃO de TRANCAMENTO no contrato de Numero: " + obj.getCodigo() + ".");
        objContratoOperacaoVO.setOperacaoPaga(false);
        objContratoOperacaoVO.setResponsavel(getResponsavelOperacao());
        objContratoOperacaoVO.setTipoOperacao("AC");
        objContratoOperacaoVO.getTipoJustificativa().setCodigo(this.tipoJustificativa);
        getFacade().getContratoOperacao().incluirSemCommit(objContratoOperacaoVO, false);
        this.setContratoOperacaoVO(objContratoOperacaoVO);
    }

    public void inicializarDadosHistoricoContratoContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj) throws Exception {
        Date data = Uteis.obterDataFutura2(obj.getVigenciaAteAjustada(), 1);
        new HistoricoContratoVO(); //Inicialização de dados
        HistoricoContratoVO objHistoricoContratoVO = getFacade().getHistoricoContrato().obterHistoricoContratoPorDataEspecifica(getContratoVO().getCodigo(), data, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (objHistoricoContratoVO != null) {
            objHistoricoContratoVO.setDataInicioSituacao(contratoRenovacao.getVigenciaDe());
            objHistoricoContratoVO.setDataFinalSituacao(contratoRenovacao.getVigenciaAteAjustada());
            getFacade().getHistoricoContrato().alterarSemCommit(objHistoricoContratoVO, true);
        }
    }

    public void inicializarDadosPeriodoAcessoContratoRenovacao(ContratoVO contrato, Date data) throws Exception {
        new PeriodoAcessoClienteVO(); //Inicialização de Dados
        PeriodoAcessoClienteVO periodoAcesso = getFacade().getPeriodoAcessoCliente().consultarPorDataEspecificaECodigoContrato(data, contrato.getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (periodoAcesso != null) {
            getFacade().getPeriodoAcessoCliente().excluirSemCommit(periodoAcesso);
        }
        periodoAcesso = new PeriodoAcessoClienteVO();
        periodoAcesso.setContrato(contrato.getCodigo());
        periodoAcesso.setDataInicioAcesso(contrato.getVigenciaDe());
        periodoAcesso.setDataFinalAcesso(contrato.getVigenciaAteAjustada());
        periodoAcesso.setPessoa(contrato.getPessoa().getCodigo());
        periodoAcesso.setTipoAcesso("CA");
        getFacade().getPeriodoAcessoCliente().incluirSemCommit(periodoAcesso);
    }

    public void inicializarDadosContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj) throws Exception {
        contratoRenovacao.obterDataFinalContratoComContratoDuracao(Uteis.obterDataFutura2(obj.getVigenciaAteAjustada(), 1));
        getFacade().getContrato().alterarDatasVigenciaContrato(contratoRenovacao);
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public Date getDataTrancamento() {
        return dataTrancamento;
    }

    public String getDataTrancamento_Apresentar() {
        return Uteis.getData(dataTrancamento);
    }

    public void setDataTrancamento(Date dataTrancamento) {
        this.dataTrancamento = dataTrancamento;
    }

    public String getObservacao() {
        if (this.observacao == null) {
            this.observacao = "";
        }

        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public UsuarioVO getResponsavelOperacao() {
        return responsavelOperacao;
    }

    public void setResponsavelOperacao(UsuarioVO responsavelOperacao) {
        this.responsavelOperacao = responsavelOperacao;
    }

    public Integer getTipoJustificativa() {

        return tipoJustificativa;
    }

    public void setTipoJustificativa(Integer tipoJustificativa) {
        this.tipoJustificativa = tipoJustificativa;
    }

    public Double getValorTrancamento() {
        return valorTrancamento;
    }

    public void setValorTrancamento(Double valorTrancamento) {
        this.valorTrancamento = valorTrancamento;
    }

    public ProdutoVO getProdutoTrancamento() {
        return produtoTrancamento;
    }

    public void setProdutoTrancamento(ProdutoVO produtoTrancamento) {
        this.produtoTrancamento = produtoTrancamento;
    }

    public Date getDataRetorno() {
        return dataRetorno;
    }

    public String getDataRetorno_Apresentar() {
        return Uteis.getData(dataRetorno);
    }

    public void setDataRetorno(Date dataRetorno) {
        this.dataRetorno = dataRetorno;
    }

    public Boolean getMensagemErro() {
        return mensagemErro;
    }

    public void setMensagemErro(Boolean mensagemErro) {
        this.mensagemErro = mensagemErro;
    }

    public Boolean getApresentarPeriodoTrancamento() {
        return apresentarPeriodoTrancamento;
    }

    public void setApresentarPeriodoTrancamento(Boolean apresentarPeriodoTrancamento) {
        this.apresentarPeriodoTrancamento = apresentarPeriodoTrancamento;
    }

    public Integer getNrDiasCongelado() {
        return nrDiasCongelado;
    }

    public void setNrDiasCongelado(Integer nrDiasCongelado) {
        this.nrDiasCongelado = nrDiasCongelado;
    }

    public Double getValorCongelado() {
        return valorCongelado;
    }

    public void setValorCongelado(Double valorCongelado) {
        this.valorCongelado = valorCongelado;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getNrDiasContrato() {
        return nrDiasContrato;
    }

    public void setNrDiasContrato(Integer nrDiasContrato) {
        this.nrDiasContrato = nrDiasContrato;
    }

    public Integer getNrDiasUtilizadosPeloClienteContrato() {
        return nrDiasUtilizadosPeloClienteContrato;
    }

    public void setNrDiasUtilizadosPeloClienteContrato(Integer nrDiasUtilizadosPeloClienteContrato) {
        this.nrDiasUtilizadosPeloClienteContrato = nrDiasUtilizadosPeloClienteContrato;
    }

    public Double getValorBaseContrato() {
        return valorBaseContrato;
    }

    public void setValorBaseContrato(Double valorBaseContrato) {
        this.valorBaseContrato = valorBaseContrato;
    }

    public Double getValorDiaContratoValorBase() {
        return valorDiaContratoValorBase;
    }

    public void setValorDiaContratoValorBase(Double valorDiaContratoValorBase) {
        this.valorDiaContratoValorBase = valorDiaContratoValorBase;
    }

    public Double getValorUtilizadoPeloClienteBase() {
        return valorUtilizadoPeloClienteBase;
    }

    public void setValorUtilizadoPeloClienteBase(Double valorUtilizadoPeloClienteBase) {
        this.valorUtilizadoPeloClienteBase = valorUtilizadoPeloClienteBase;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Boolean getApresentarPanelClienteRetornoForaPrazo() {
        return apresentarPanelClienteRetornoForaPrazo;
    }

    public void setApresentarPanelClienteRetornoForaPrazo(Boolean apresentarPanelClienteRetornoForaPrazo) {
        this.apresentarPanelClienteRetornoForaPrazo = apresentarPanelClienteRetornoForaPrazo;
    }

    public Boolean getApresentarPanelClienteRetornoNoPrazo() {
        return apresentarPanelClienteRetornoNoPrazo;
    }

    public void setApresentarPanelClienteRetornoNoPrazo(Boolean apresentarPanelClienteRetornoNoPrazo) {
        this.apresentarPanelClienteRetornoNoPrazo = apresentarPanelClienteRetornoNoPrazo;
    }

    public Boolean getDescontoDiasExcedidos() {
        return descontoDiasExcedidos;
    }

    public void setDescontoDiasExcedidos(Boolean descontoDiasExcedidos) {
        this.descontoDiasExcedidos = descontoDiasExcedidos;
    }

    public Boolean getTaxaDiasExcedidos() {
        return taxaDiasExcedidos;
    }

    public void setTaxaDiasExcedidos(Boolean taxaDiasExcedidos) {
        this.taxaDiasExcedidos = taxaDiasExcedidos;
    }

    public Integer getNumeroDiasDescontar() {
        return numeroDiasDescontar;
    }

    public void setNumeroDiasDescontar(Integer numeroDiasDescontar) {
        this.numeroDiasDescontar = numeroDiasDescontar;
    }

    public String getDataFimTrancamento_Apresentar() {
        return Uteis.getData(dataFimTrancamento);
    }

    public Date getDataFimTrancamento() {
        return dataFimTrancamento;
    }

    public void setDataFimTrancamento(Date dataFimTrancamento) {
        this.dataFimTrancamento = dataFimTrancamento;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Integer getNrDiasBonus() {
        return nrDiasBonus;
    }

    public void setNrDiasBonus(Integer nrDiasBonus) {
        this.nrDiasBonus = nrDiasBonus;
    }

    /**
     * @return the responsavelAlteracaoDataRetroativa
     */
    public UsuarioVO getResponsavelAlteracaoDataRetroativa() {
        return responsavelAlteracaoDataRetroativa;
    }

    /**
     * @param responsavelAlteracaoDataRetroativa the responsavelAlteracaoDataRetroativa to set
     */
    public void setResponsavelAlteracaoDataRetroativa(UsuarioVO responsavelAlteracaoDataRetroativa) {
        this.responsavelAlteracaoDataRetroativa = responsavelAlteracaoDataRetroativa;
    }

    public void inicializarDadosHistoricoContrato() throws Exception {
         HistoricoContratoVO existente = getFacade().getHistoricoContrato().obterHistoricoContratoPorDataEspecifica(getContratoVO().getCodigo(),
                getDataTrancamento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (existente != null) {
            if (Calendario.igual(existente.getDataInicioSituacao(), getDataTrancamento())) {
                 existente.setDataFinalSituacao(existente.getDataInicioSituacao());
            } else {
                existente.setDataFinalSituacao(Uteis.obterDataAnterior(this.getDataTrancamento(), 1));
            }
            getFacade().getHistoricoContrato().alterarSemCommit(existente, false);
        }
        HistoricoContratoVO obj = new HistoricoContratoVO();
        obj.setContrato(this.contratoVO.getCodigo());
        obj.setDataFinalSituacao(this.dataFimTrancamento);
        obj.setDataInicioSituacao(this.dataTrancamento);
        obj.setDataRegistro(Calendario.hoje());
        if (Uteis.getCompareData(getDataTrancamento(), Calendario.hoje()) >= 0) {
            obj.setDescricao("TRANCADO");
        } else {
            obj.setDescricao("TRANCADO RETROATIVO");
        }
        obj.setTipoHistorico("TR");
        obj.setResponsavelRegistro(this.responsavelOperacao);
        obj.setSituacaoRelativaHistorico("");
        
        ValidacaoHistoricoContrato.validarPeriodoHistoricoContratoOperacao(this.getDataTrancamento(),
                this.getDataFimTrancamento(), this.getContratoVO().getCodigo(),
                getFacade().getHistoricoContrato(), obj);
        getFacade().getHistoricoContrato().excluirHistoricosFuturosContrato(this.contratoVO.getCodigo(), this.dataTrancamento);
        getFacade().getHistoricoContrato().incluirSemCommit(obj, false);
    }

    public Boolean getContratoVencido() {
        return contratoVencido;
    }

    public void setContratoVencido(Boolean contratoVencido) {
        this.contratoVencido = contratoVencido;
    }

    public ContratoOperacaoVO getContratoOperacaoVO() {
        if (contratoOperacaoVO == null) {
            contratoOperacaoVO = new ContratoOperacaoVO();
        }
        return contratoOperacaoVO;
    }

    public void setContratoOperacaoVO(ContratoOperacaoVO contratoOperacaoVO) {
        this.contratoOperacaoVO = contratoOperacaoVO;
    }

    private int quantidadeDeDiasDoTrancamento() {
        if(this.getDataPrimeiroTrancamentoSeq() != null){
             return (int) Uteis.nrDiasEntreDatas(this.getDataPrimeiroTrancamentoSeq(),this.getDataFimTrancamento()) +1;
        }
        return (int) Uteis.nrDiasEntreDatas(this.getDataTrancamento(),this.getDataFimTrancamento()) +1;
    }

    public Date obtenhaDataFinalAposTrancamento() {
        if (contratoVO.deveLiberarVaga(quantidadeDeDiasDoTrancamento())){
            if(this.getDataPrimeiroTrancamentoSeq() != null){
                return Uteis.somarDias(getDataPrimeiroTrancamentoSeq(), -1);
            }
            return Uteis.somarDias(getDataTrancamento(), -1);
        }
        return getDataFimTrancamento();
    }

    public Date getDataPrimeiroTrancamentoSeq() {
        return dataPrimeiroTrancamentoSeq;
    }

    public void setDataPrimeiroTrancamentoSeq(Date dataPrimeiroTrancamentoSeq) {
        this.dataPrimeiroTrancamentoSeq = dataPrimeiroTrancamentoSeq;
    }

    public List<MatriculaAlunoHorarioTurmaVO> getMatriculaVigentes() {
        return matriculaVigentes;
    }

    public void setMatriculaVigentes(List<MatriculaAlunoHorarioTurmaVO> matriculaVigentes) {
        this.matriculaVigentes = matriculaVigentes;
    }

    public Boolean getMensagemManutencao() {
        return mensagemManutencao;
    }

    public void setMensagemManutencao(Boolean mensagemManutencao) {
        this.mensagemManutencao = mensagemManutencao;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        if (origemSistema == null) {
            origemSistema = OrigemSistemaEnum.ZW;
        }
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public boolean isAlterarVencimentoparcelas() {
        return alterarVencimentoparcelas;
    }

    public void setAlterarVencimentoparcelas(boolean alterarVencimentoparcelas) {
        this.alterarVencimentoparcelas = alterarVencimentoparcelas;
    }

    public UsuarioVO getResponsavelAlteracaoDataVencimentoParcelas() {
        return responsavelAlteracaoDataVencimentoParcelas;
    }

    public void setResponsavelAlteracaoDataVencimentoParcelas(UsuarioVO responsavelAlteracaoDataVencimentoParcelas) {
        this.responsavelAlteracaoDataVencimentoParcelas = responsavelAlteracaoDataVencimentoParcelas;
    }
}

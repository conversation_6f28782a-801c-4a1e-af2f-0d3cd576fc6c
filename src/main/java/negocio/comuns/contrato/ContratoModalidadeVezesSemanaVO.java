package negocio.comuns.contrato;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade ContratoModalidadeVezesSemana.
 * Classe do tipo VO - Value Object composta pelos atributos da entidade com
 * visibilidade protegida e os métodos de acesso a estes atributos. Classe
 * utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class ContratoModalidadeVezesSemanaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer contratoModalidade;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>VezesSemana </code>.
     */
    // protected VezesSemanaVO vezesSemana;
    protected Integer nrVezes;
    @NaoControlarLogAlteracao
    protected Boolean vezeSemanaEscolhida;
    protected String tipoValor;
    protected Double percentualDesconto;
    protected Double valorEspecifico;
    protected String tipoOperacao;
    @NaoControlarLogAlteracao
    protected Boolean desenharValorEspecificoVezesSemana;
    @NaoControlarLogAlteracao
    protected Boolean desenharValorDescontoVezesSemana;    

    /**
     * Construtor padrão da classe
     * <code>ContratoModalidadeVezesSemana</code>. Cria uma nova instância desta
     * entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ContratoModalidadeVezesSemanaVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ContratoModalidadeVezesSemanaVO</code>. Todos os tipos de
     * consistência de dados são e devem ser implementadas neste método. São
     * validações tópicas: verificação de campos obrigatórios, verificação de
     * valores válidos para os atributos.
     *
     * @exception ConsistirExecption Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDados(ContratoModalidadeVezesSemanaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getNrVezes().intValue() == 0) {
            throw new ConsistirException("O campo NÙMERO DE VEZES POR SEMANA(Modalidade Vezes Semana) deve ser informado.");
        }

        if (obj.getContratoModalidade() == null) {
            throw new ConsistirException("O campo Contrato MODALIDADE (Modalidade Vezes Semana) deve ser informado.");
        }

        if (obj.getTipoValor() == null) {
            obj.setTipoValor("");
        }
        if (obj.getTipoValor().equals("PD")) {
            if (obj.getTipoOperacao() == null || obj.getTipoOperacao().equals("")) {
                throw new ConsistirException("O campo TIPO DE OPERAÇÃO (Modalidade Vezes Semana) deve ser informado.");
            }
            if (obj.getPercentualDesconto().doubleValue() == 0) {
                throw new ConsistirException("O campo PERCENTUAL (Modalidade Vezes Semana) deve ser informado.");
            }
        }
        if (obj.getTipoValor().equals("VE")) {
            if (obj.getTipoOperacao() == null || obj.getTipoOperacao().equals("")) {
                throw new ConsistirException("O campo TIPO DE OPERAÇÃO (Modalidade Vezes Semana) deve ser informado.");
            }

           /* if (obj.getValorEspecifico().doubleValue() == 0) {
                throw new ConsistirException("O campo VALOR (Modalidade Vezes Semana) deve ser informado.");
            }*/
        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setContratoModalidade(new Integer(0));
        setNrVezes(new Integer(0));
        setVezeSemanaEscolhida(new Boolean(false));
        setPercentualDesconto(new Double(0));
        setValorEspecifico(new Double(0));
        setDesenharValorEspecificoVezesSemana(new Boolean(false));
        setDesenharValorDescontoVezesSemana(new Boolean(false));

    }

    public Integer getNrVezes() {
        return (nrVezes);
    }

    public void setNrVezes(Integer nrVezes) {
        this.nrVezes = nrVezes;
    }

    public Boolean getVezeSemanaEscolhida() {
        return vezeSemanaEscolhida;
    }

    public void setVezeSemanaEscolhida(Boolean vezeSemanaEscolhida) {
        this.vezeSemanaEscolhida = vezeSemanaEscolhida;
    }

    public Integer getContratoModalidade() {
        return (contratoModalidade);
    }

    public void setContratoModalidade(Integer contratoModalidade) {
        this.contratoModalidade = contratoModalidade;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorEspecifico() {
        return (valorEspecifico);
    }

    public void setValorEspecifico(Double valorEspecifico) {
        this.valorEspecifico = valorEspecifico;
    }

    public Double getPercentualDesconto() {
        return (percentualDesconto);
    }

    public void setPercentualDesconto(Double percentualDesconto) {
        this.percentualDesconto = percentualDesconto;
    }

    public String getTipoValor() {
        if (tipoValor == null) {
            tipoValor = "";
        }
        return tipoValor;
    }

    public void setTipoValor(String tipoValor) {
        this.tipoValor = tipoValor;
    }

    public String getTipoOperacao() {
        if (tipoOperacao == null) {
            tipoOperacao = "";
        }
        return tipoOperacao;
    }

    public String getTipoOperacao_Apresentar() {
        if (tipoOperacao == null || tipoOperacao.equals("")) {
            return ("");
        }
        if (tipoOperacao.equals("AC")) {
            return ("Acrescimo");
        }

        if (tipoOperacao.equals("RE")) {
            return ("Redução");
        }


        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public Boolean getDesenharValorDescontoVezesSemana() {
        return desenharValorDescontoVezesSemana;
    }

    public void setDesenharValorDescontoVezesSemana(Boolean desenharValorDescontoVezesSemana) {
        this.desenharValorDescontoVezesSemana = desenharValorDescontoVezesSemana;
    }

    public Boolean getDesenharValorEspecificoVezesSemana() {
        return desenharValorEspecificoVezesSemana;
    }

    public void setDesenharValorEspecificoVezesSemana(Boolean desenharValorEspecificoVezesSemana) {
        this.desenharValorEspecificoVezesSemana = desenharValorEspecificoVezesSemana;
    }

    public void desenhaTipoValor() {
        if (getTipoValor() == null || getTipoValor().equals("")) {
            setTipoValor("");
            setPercentualDesconto(new Double(0));
            setValorEspecifico(new Double(0));
            setDesenharValorEspecificoVezesSemana(new Boolean(false));
            setDesenharValorDescontoVezesSemana(new Boolean(false));
        }

        if (getTipoValor().equals("VE")) {
            setPercentualDesconto(new Double(0));
            setValorEspecifico(new Double(0));
            setDesenharValorDescontoVezesSemana(new Boolean(false));
            setDesenharValorEspecificoVezesSemana(new Boolean(true));
        }

        if (getTipoValor().equals("PD")) {
            setValorEspecifico(new Double(0));
            setPercentualDesconto(new Double(0));
            setDesenharValorEspecificoVezesSemana(new Boolean(false));
            setDesenharValorDescontoVezesSemana(new Boolean(true));

        }
    }

    public boolean getApresentarValorEspecifico() {
        if (getTipoValor() == null || getTipoValor().equals("")) {
            return false;
        }
        if (getTipoValor().equals("VE")) {
            return true;
        }
        return false;
    }

    public boolean getApresentarValorDesconto() {
        if (getTipoValor() == null || getTipoValor().equals("")) {
            return false;
        }
        if (getTipoValor().equals("PD")) {
            return true;
        }
        return false;
    }
}

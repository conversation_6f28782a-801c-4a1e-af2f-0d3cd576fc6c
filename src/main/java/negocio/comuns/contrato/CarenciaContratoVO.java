/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import java.util.Date;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import java.text.ParseException;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.contrato.ContratoOperacao;

/**
 *
 * <AUTHOR>
 */
public class CarenciaContratoVO extends SuperVO {

    protected ContratoVO contratoVO;
//    protected Integer chavePrimariaDoBonusOperacao;
//    protected Integer chavePrimariaDoBonusHistorico;
    protected Integer empresa;
    protected Date dataInicio;
    protected Date dataTermino;
    protected Date dataInicioRetorno;
    protected Date dataTerminoRetorno;
    protected Date dataRegistro;
    protected Integer tipoJustificativa;
    protected Integer nrDiasContrato;
    protected Integer nrDiasUsadoContrato;
    protected Integer nrDiasRestanteContrato;
    protected Integer nrDiasUsados;
    protected Integer nrDiasPermitido;
    protected Integer nrDiasRestam;
    protected Integer nrDias;
    protected Long periodoCarencia;
//    protected Integer nrDiasBonusOperacao;
//    protected Integer nrDiasBonusHistorico;
//    protected Integer diasRestanteOperacao;
//    protected Integer diasRestanteHistorico;
    protected String observacao;
    protected UsuarioVO responsavelOperacao;
    protected Boolean mensagemErro;
    protected Boolean apresentarPeriodoCarencia;
    protected Boolean qtdDiasCarenciaMaiorQueContrato;
    private ContratoOperacaoVO contratoOperacaoVO;
    private OrigemSistemaEnum origemSistema;

    public CarenciaContratoVO() {
        inicializarDados();
    }

    public void inicializarDados() {
        setContratoVO(new ContratoVO());
        setApresentarPeriodoCarencia(false);
        // setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
        // setDataInicioRetorno(negocio.comuns.utilitarias.Calendario.hoje());
        //  setDataTermino(negocio.comuns.utilitarias.Calendario.hoje());
        setPeriodoCarencia(new Long(0));
        setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        setNrDiasPermitido(0);
        setNrDiasUsados(0);
        setNrDiasRestam(0);
        setNrDias(0);
        setResponsavelOperacao(new UsuarioVO());
        setQtdDiasCarenciaMaiorQueContrato(false);
        setNrDiasContrato(0);
        setNrDiasRestanteContrato(0);
        setNrDiasUsadoContrato(0);
    }

    public void validarPeriodoCarencia(List<ContratoOperacaoVO> listaOperacao) throws Exception {
        if (getDataInicio() == null || getDataTermino() == null) {
            throw new ConsistirException("O período da Férias deve ser informado");
        }
        if (Uteis.getCompareData(getDataInicio(), getDataTermino()) > 0) {
            throw new ConsistirException("O campo ATÉ não pode ser antes do campo INÍCIO");
        }
        validarDiasUtilizados(listaOperacao);
        ValidacaoContratoOperacao.validarPeriodoOperacao("Carencia", getDataInicio(), this.contratoVO, listaOperacao);

        /*if(getDataInicio().compareTo(getContratoVO().getVigenciaDe())<0){
        throw new ConsistirException("A data de INÍCIO da carência deve ser maior ou igual a data de início do contrato");
        }
        if(getDataTermino().compareTo(getContratoVO().getVigenciaAteAjustada())>0){
        throw new ConsistirException("A data de TÉRMINO da carência deve ser anterior ou igual a data de término do contrato");
        }*/
        if (getTipoJustificativa().equals(0)) {
            throw new ConsistirException("A Justificativa das Férias deve ser informado");
        }
        if (periodoCarencia.intValue() > getNrDiasRestam()) {
            throw new ConsistirException("O período informado para Férias contém: " + periodoCarencia.intValue() + " dia(s)."
                    + " Ultrapassando a quantidade de dias restantes. Por favor, informe novamente o período para as Férias");
        }
    }

    public void validarDiasUtilizados(List<ContratoOperacaoVO> listaOperacoes) throws Exception {
        validarDiasUtilizados(listaOperacoes, this.getContratoVO().getContratoDuracao().getCarencia());
    }
    public void validarDiasUtilizados(List<ContratoOperacaoVO> listaOperacoes, Integer diasCarencia) throws Exception {
        this.setNrDiasPermitido(diasCarencia);
        this.setNrDiasUsados(0);
        for (ContratoOperacaoVO op : listaOperacoes) {
            if (op.getCarenciaFerias()) {
                this.setNrDiasUsados(this.getNrDiasUsados() + op.obterNrDiasContratoOperacao());
            }
        }
        this.setNrDiasRestam(this.getNrDiasPermitido() - this.getNrDiasUsados());
        if (this.getNrDiasPermitido() <= 0) {
            throw new ConsistirException("Não será possível realizar as férias para este contrato, pois ele não permite dias para férias.");
        }
        if (this.getNrDiasRestam().intValue() <= 0) {
            throw new ConsistirException("Não será possível realizar a férias para este contrato, pois os dias permitidos para férias já foram utilizados.");
        }
    }

    public void alterarSituacaoContrato(Date data) throws Exception {
        try {
            getContratoVO().setDataPrevistaRenovar(data);
            getContratoVO().setDataPrevistaRematricula(data);
            getContratoVO().setVigenciaAteAjustada(data);
            getFacade().getContrato().alterarSituacaoContrato(getContratoVO());
        } catch (Exception e) {
            throw e;
        }
    }

//    public void alterarSituacaoContratoRetroativo() throws Exception {
//        try {
//            Date data = Uteis.obterDataFutura2(getContratoVO().getVigenciaAteAjustada(), getNrDias());
//            getContratoVO().setDataPrevistaRenovar(data);
//            getContratoVO().setDataPrevistaRematricula(data);
//            getContratoVO().setVigenciaAteAjustada(data);
//            getFacade().getContrato().alterarSituacaoContrato(getContratoVO());
//        } catch (Exception e) {
//            throw e;
//        }
//    }
    public void inicializarDadosOperacaoContrato(Boolean retroativo) throws Exception {
        try {
            ContratoOperacaoVO obj = new ContratoOperacaoVO();
            obj.setContrato(this.contratoVO.getCodigo().intValue());
            obj.setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setOrigemSistema(this.getOrigemSistema());
            if (retroativo) {
                if (getQtdDiasCarenciaMaiorQueContrato()) {
                    obj.setDescricaoCalculo("Férias Retroativo.\r\n"
                            + "Lançamento de Férias retroativa portanto não foi modificado seu historico e nem seu periodo acesso.\r\n"
                            + "Nova data de termino ajustada de seu contrato: "
                            + contratoVO.getDataPrevistaRenovar_Apresentar()
                            + "\r\n---------------------------------------------------------------------------------------\n"
                            + "A quantidade de dias informado para as Férias é maior que os dias restante do contrato portanto os dias\r\n"
                            + " de acrescimo serão os que ainda faltam no contrato.");
                } else {
                    obj.setDescricaoCalculo("Férias Retroativo.\r\n"
                            + "Lançamento de Férias retroativa portanto não foi modificado seu historico e nem seu periodo acesso.\r\n"
                            + "Nova data de termino ajustada de seu contrato: "
                            + contratoVO.getDataPrevistaRenovar_Apresentar()
                            + "");
                }
            } else {
                if (getQtdDiasCarenciaMaiorQueContrato()) {
                    obj.setDescricaoCalculo("Férias.\r\n"
                            + "Lançamento de Férias: \r\n"
                            + "Nova data de termino ajustada de seu contrato : " + contratoVO.getVigenciaAteAjustada_Apresentar()
                            + "---------------------------------------------------------------------------------------\n"
                            + "A quantidade de dias informado para as Férias é maior que os dias restante do contrato portanto os dias\r\n"
                            + " de acrescimo serão os que ainda faltam no contrato.");
                } else {
                    obj.setDescricaoCalculo("Férias.\r\n"
                            + "Lançamento de uma Férias: \r\n"
                            + "Nova data de termino ajustada de seu contrato : " + contratoVO.getVigenciaAteAjustada_Apresentar());
                }
            }
            obj.setObservacao(this.observacao);
            obj.setOperacaoPaga(false);
            obj.setResponsavel(this.responsavelOperacao);
            obj.getTipoJustificativa().setCodigo(this.tipoJustificativa);
            obj.setTipoOperacao("CR");
            obj.setDataInicioEfetivacaoOperacao(this.dataInicio);
            obj.setDataFimEfetivacaoOperacao(this.dataTermino);
            obj.setOrigemSistema(this.getOrigemSistema());
            getFacade().getContratoOperacao().incluirSemCommit(obj, false);
            this.setContratoOperacaoVO(obj);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosHistoricoContratoCarenciaOLD() throws Exception {
        try {
            HistoricoContratoVO obj = new HistoricoContratoVO();
            obj.setContrato(this.contratoVO.getCodigo().intValue());
            obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setResponsavelRegistro(this.responsavelOperacao);
            obj.setSituacaoRelativaHistorico("");
            obj.setDescricao("RETORNO-FÉRIAS");
            obj.setTipoHistorico("RC");
            obj.setDataFinalSituacao(this.dataTerminoRetorno);
            obj.setDataInicioSituacao(this.dataInicioRetorno);
            getFacade().getHistoricoContrato().incluirSemCommit(obj, true);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarPeriodoAcessoRetornoRetroativo() throws Exception {
        try {
            PeriodoAcessoClienteVO novoPeriodoAcesso = new PeriodoAcessoClienteVO();
            novoPeriodoAcesso.setContrato(getContratoVO().getCodigo());
            novoPeriodoAcesso.setContratoBaseadoRenovacao(getContratoVO().getContratoBaseadoRenovacao());
            novoPeriodoAcesso.setDataFinalAcesso(this.dataTerminoRetorno);
            novoPeriodoAcesso.setDataInicioAcesso(this.dataInicioRetorno);
            novoPeriodoAcesso.setTipoAcesso("RC");
            novoPeriodoAcesso.setPessoa(getContratoVO().getPessoa().getCodigo());
            getFacade().getPeriodoAcessoCliente().incluirSemCommit(novoPeriodoAcesso);

        } catch (Exception e) {
            throw e;
        }
    }

    public Boolean inicializarDadosHistoricoContratoCarencia() throws Exception {
        try {
            Boolean existeAvencer = false;
             HistoricoContratoVO hisAVencer = null;
            HistoricoContratoVO obj = new HistoricoContratoVO();
            obj = getFacade().getHistoricoContrato().obterHistoricoContratoPorCodigoContratoDataInicioDataFim(getContratoVO().getCodigo().intValue(), this.dataInicio, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (obj != null && obj.getAvencer()) {
                 hisAVencer = (HistoricoContratoVO) obj.getClone(true);
                //valido que dia começou o historio se foi no mesmo dia do novo registro de historico.
                if (Uteis.getCompareData(obj.getDataInicioSituacao(), this.dataInicio) == 0) {
                    getFacade().getHistoricoContrato().excluir(obj);
                } else {
                    obj.setDataFinalSituacao(Uteis.obterDataAnterior(this.dataInicio, 1));
                    getFacade().getHistoricoContrato().alterarSemCommit(obj, true);
                }
                existeAvencer = true;
            } else if (obj != null) {
                //valido que dia começou o historio se foi no mesmo dia do novo registro de historico.
                if (Uteis.getCompareData(obj.getDataInicioSituacao(), this.dataInicio) == 0) {
                    obj.setDataFinalSituacao(Uteis.obterDataAnterior(this.dataInicio, 0));
                } else {
                    obj.setDataFinalSituacao(Uteis.obterDataAnterior(this.dataInicio, 1));
                }
                getFacade().getHistoricoContrato().alterarSemCommit(obj, true);
            }

            obj = new HistoricoContratoVO();
            obj.setContrato(this.contratoVO.getCodigo().intValue());
            obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setResponsavelRegistro(this.responsavelOperacao);
            obj.setSituacaoRelativaHistorico("");
            obj.setDescricao("FÉRIAS");
            obj.setTipoHistorico("CR");
            obj.setDataFinalSituacao(this.dataTermino);
            obj.setDataInicioSituacao(this.dataInicio);

            ValidacaoHistoricoContrato.validarPeriodoHistoricoContratoOperacao(
                    this.getDataInicio(),
                    this.getDataTermino(),
                    this.getContratoVO().getCodigo(),
                    getFacade().getHistoricoContrato(), obj);

            getFacade().getHistoricoContrato().incluirSemCommit(obj, true);

            obj = new HistoricoContratoVO();
            obj.setContrato(this.contratoVO.getCodigo().intValue());
            obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setResponsavelRegistro(this.responsavelOperacao);
            obj.setSituacaoRelativaHistorico("");
            obj.setDescricao("RETORNO-FÉRIAS");
            obj.setTipoHistorico("RC");
            obj.setDataInicioSituacao(this.dataInicioRetorno);
            if (existeAvencer) {
                hisAVencer.setCodigo(0);
                hisAVencer.setNovoObj(true);
                hisAVencer.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
                obj.setDataFinalSituacao(this.dataInicioRetorno);
                hisAVencer.setDataInicioSituacao(obj.getDataFinalSituacao());
                hisAVencer.setDataFinalSituacao(this.dataTerminoRetorno);
            } else {
                obj.setDataFinalSituacao(this.dataTerminoRetorno);
            }
            getFacade().getHistoricoContrato().incluirSemCommit(obj, true);
            if (existeAvencer) {
                getFacade().getHistoricoContrato().incluirSemCommit(hisAVencer, true);
            }

            return existeAvencer;
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterarUltimoHistorico() throws Exception {
         HistoricoContratoVO obj = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(this.contratoVO.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if(obj.getAvencer()){
            obj.setDataFinalSituacao(this.dataTerminoRetorno);
         }
         getFacade().getHistoricoContrato().alterarSemCommit(obj, false);
     }

    public void inicializarDadosPeriodoAcessoCliente() throws Exception {
        try {
            List<PeriodoAcessoClienteVO> listaPeriodos = getFacade().getPeriodoAcessoCliente().consultarPorVigenteOuFuturoContrato(this.dataInicio, this.contratoVO.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
            for (PeriodoAcessoClienteVO periodoAcesso : listaPeriodos) {
                if (Uteis.getCompareData(periodoAcesso.getDataInicioAcesso(), this.dataInicio) == 0) {
                    getFacade().getPeriodoAcessoCliente().excluirSemCommit(periodoAcesso);
                } else {
                    periodoAcesso.setDataFinalAcesso(Uteis.obterDataAnterior(this.dataInicio, 1));
                    getFacade().getPeriodoAcessoCliente().alterarSemCommit(periodoAcesso);
                }
            }
            gravarPeriodoAcesso();
            inicializarPeriodoAcessoRetorno();
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarPeriodoAcessoRetorno() throws Exception {
        try {
            PeriodoAcessoClienteVO novoPeriodoAcesso = new PeriodoAcessoClienteVO();
            novoPeriodoAcesso.setContrato(getContratoVO().getCodigo());
            novoPeriodoAcesso.setContratoBaseadoRenovacao(getContratoVO().getContratoBaseadoRenovacao());
            novoPeriodoAcesso.setDataFinalAcesso(getDataTerminoRetorno());
            novoPeriodoAcesso.setDataInicioAcesso(getDataInicioRetorno());
            novoPeriodoAcesso.setTipoAcesso("RC");
            novoPeriodoAcesso.setPessoa(getContratoVO().getPessoa().getCodigo());
            List <PeriodoAcessoClienteVO> periodosFuturos = getFacade().getPeriodoAcessoCliente().consultarPorDataInicioContrato(getDataInicio() ,getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            if(!periodosFuturos.isEmpty()){
                Iterator i = periodosFuturos.iterator();
                while (i.hasNext()){
                    PeriodoAcessoClienteVO periodo = (PeriodoAcessoClienteVO) i.next();
                    getFacade().getPeriodoAcessoCliente().excluirSemCommit(periodo);
                }
            }
            getFacade().getPeriodoAcessoCliente().incluirSemCommit(novoPeriodoAcesso);
        } catch (Exception e) {
            throw e;
        }
    }

    public void gravarPeriodoAcesso() throws Exception {
        try {
            PeriodoAcessoClienteVO obj = new PeriodoAcessoClienteVO();
            obj.setPessoa(getContratoVO().getPessoa().getCodigo().intValue());
            obj.setContrato(getContratoVO().getCodigo().intValue());
            obj.setContratoBaseadoRenovacao(getContratoVO().getContratoBaseadoRenovacao());
            obj.setTipoAcesso("CR");
            obj.setDataFinalAcesso(getDataTermino());
            obj.setDataInicioAcesso(getDataInicio());
            getFacade().getPeriodoAcessoCliente().incluirSemCommit(obj);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj) throws Exception {
        try {
            Date dataFimAnterior = contratoRenovacao.getVigenciaAte();
            contratoRenovacao.obterDataFinalContratoComContratoDuracao(Uteis.obterDataFutura2(obj.getVigenciaAteAjustada(), 1));
            getFacade().getContrato().alterarDatasVigenciaContrato(contratoRenovacao);
            inicializarDadosMatriculaTurmaContratoRenovacao(contratoRenovacao, dataFimAnterior);
        } catch (Exception e) {
            throw e;
        }

    }

    public void inicializarDadosPeriodoAcessoContratoRenovacao(ContratoVO contrato, Date data) throws Exception {
        try {
            PeriodoAcessoClienteVO periodoAcesso = new PeriodoAcessoClienteVO();
            periodoAcesso = getFacade().getPeriodoAcessoCliente().consultarPorDataEspecificaECodigoContrato(data, contrato.getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (periodoAcesso != null) {
                getFacade().getPeriodoAcessoCliente().excluirSemCommit(periodoAcesso);
            }
            periodoAcesso = new PeriodoAcessoClienteVO();
            periodoAcesso.setContrato(contrato.getCodigo().intValue());
            periodoAcesso.setDataInicioAcesso(contrato.getVigenciaDe());
            periodoAcesso.setDataFinalAcesso(contrato.getVigenciaAteAjustada());
            periodoAcesso.setPessoa(contrato.getPessoa().getCodigo().intValue());
            periodoAcesso.setTipoAcesso("CA");
            getFacade().getPeriodoAcessoCliente().incluirSemCommit(periodoAcesso);
        } catch (Exception e) {
            throw e;
        }

    }

    public void inicializarDadosContratoOperacaoContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj) throws Exception {
        try {
            ContratoOperacaoVO objContratoOperacaoVO = new ContratoOperacaoVO();
            objContratoOperacaoVO.setContrato(contratoRenovacao.getCodigo().intValue());
            objContratoOperacaoVO.setDataFimEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDataInicioEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDescricaoCalculo("");
            objContratoOperacaoVO.setObservacao("Modificação: \n\r"
                    + " Data de Início,\n\r"
                    + " Data de Término,\n\r"
                    + " Data de Previsão Renovação,\n\r"
                    + " Data de Previsão Rematricula,\n\r"
                    + " Devido uma OPERAÇÃO de Férias no contrato de Numero: " + obj.getCodigo().intValue() + ".");
            objContratoOperacaoVO.setOperacaoPaga(false);
            objContratoOperacaoVO.setResponsavel(getResponsavelOperacao());
            objContratoOperacaoVO.setTipoOperacao("AC");
            objContratoOperacaoVO.getTipoJustificativa().setCodigo(this.tipoJustificativa);
            getFacade().getContratoOperacao().incluirSemCommit(objContratoOperacaoVO, false);
            this.setContratoOperacaoVO(objContratoOperacaoVO);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosHistoricoContratoContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj, Date data) throws Exception {
        try {
            //Date data = Uteis.obterDataFutura2(contratoRenovacao.getVigenciaDe(), 1);
            HistoricoContratoVO objHistoricoContratoVO = new HistoricoContratoVO();
            objHistoricoContratoVO = getFacade().getHistoricoContrato().
                    obterHistoricoContratoPorDataEspecifica(
                    contratoRenovacao.getCodigo().intValue(),
                    data, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (objHistoricoContratoVO != null) {
                objHistoricoContratoVO.setDataInicioSituacao(contratoRenovacao.getVigenciaDe());
                objHistoricoContratoVO.setDataFinalSituacao(contratoRenovacao.getVigenciaAteAjustada());
                getFacade().getHistoricoContrato().alterarSemCommit(objHistoricoContratoVO, true);
            } else {
                throw new Exception(String.format("Não foi possível encontrar um "
                        + "histórico de contrato na data %s para o contrato sucessor %s",
                        new Object[]{
                            Uteis.getData(data),
                            contratoRenovacao.getCodigo()
                        }));
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosMatriculaTurmaContratoRenovacao(ContratoVO contratoSucessor, Date dataFimAnterior) throws Exception {
        List<MatriculaAlunoHorarioTurmaVO> lista = getFacade().getMatriculaAlunoHorarioTurma().consultarMatriculaAtiva(contratoSucessor.getCodigo(), dataFimAnterior);
        for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
            matricula.setDataInicio(contratoSucessor.getVigenciaDe());
            if (contratoSucessor.getContratoResponsavelRenovacaoMatricula() == null || contratoSucessor.getContratoResponsavelRenovacaoMatricula() == 0) {
                matricula.setDataFim(Uteis.somarDias(contratoSucessor.getVigenciaAte(), contratoSucessor.getEmpresa().getToleranciaOcupacaoTurma()));
            } else {
                matricula.setDataFim(contratoSucessor.getVigenciaAte());
            }
            getFacade().getMatriculaAlunoHorarioTurma().alterarInicioFimMatriculaSemCommit(matricula);
        }

    }

    public void alterarOperacaoCarencia() throws Exception {
        try {
            ContratoOperacaoVO obj = new ContratoOperacaoVO();
            obj = getFacade().getContratoOperacao().consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(negocio.comuns.utilitarias.Calendario.hoje(), getContratoVO().getCodigo().intValue(), "CR", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (obj != null) {
                if (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(), obj.getDataInicioEfetivacaoOperacao()) == 0) {
                    Long nrDias = Uteis.nrDiasEntreDatas(obj.getDataInicioEfetivacaoOperacao(), obj.getDataFimEfetivacaoOperacao());
                    setNrDias(nrDias.intValue() + 1);
                    obj.setDataFimEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
                } else {
                    Long nrDias = Uteis.nrDiasEntreDatas(negocio.comuns.utilitarias.Calendario.hoje(), obj.getDataFimEfetivacaoOperacao());
                    setNrDias(nrDias.intValue() + 1);
                    obj.setDataFimEfetivacaoOperacao(Uteis.obterDataAnterior(negocio.comuns.utilitarias.Calendario.hoje(), 1));
                }
                getFacade().getContratoOperacao().alterarSemCommit(obj);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public Date inicializarDadosRetornoCarenciaHistoricoContrato() throws Exception {
        try {
            Date data = negocio.comuns.utilitarias.Calendario.hoje();

            //alterando Férias
            HistoricoContratoVO carencia = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContratoTipoHistorico(
                    getContratoVO().getCodigo().intValue(), "CR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (carencia != null) {
                if (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(), carencia.getDataInicioSituacao()) == 0) {
                    getFacade().getHistoricoContrato().excluir(carencia);
                    HistoricoContratoVO retorno = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContratoTipoHistorico(
                            getContratoVO().getCodigo().intValue(), "RC", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    getFacade().getHistoricoContrato().excluir(retorno);
                    data = Uteis.obterDataAnterior(retorno.getDataFinalSituacao(), getNrDias());
                    HistoricoContratoVO ultimo = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if(ultimo.getAvencer() && Calendario.maior(ultimo.getDataInicioSituacao(),negocio.comuns.utilitarias.Calendario.hoje() )){
                        data = Uteis.obterDataAnterior(ultimo.getDataFinalSituacao(), getNrDias());
                        getFacade().getHistoricoContrato().excluir(ultimo);
                        ultimo = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    }
                    if (getResponsavelOperacao() != null && !UteisValidacao.emptyNumber(getResponsavelOperacao().getCodigo())){
                        ultimo.setResponsavelRegistro(getResponsavelOperacao());
                    }
                    ultimo.setDataFinalSituacao(data);
                    getFacade().getHistoricoContrato().alterarSemCommit(ultimo, false);
                } else {
                    carencia.setDataFinalSituacao(Uteis.obterDataAnterior(negocio.comuns.utilitarias.Calendario.hoje(), 1));
                    getFacade().getHistoricoContrato().alterarSemCommit(carencia, false);
                    HistoricoContratoVO retorno = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContratoTipoHistorico(
                            getContratoVO().getCodigo().intValue(), "RC", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                     HistoricoContratoVO ultimo = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (retorno != null) {
                        if (retorno.isRetornoManual()) {
                            throw new Exception(String.format("Retorno manual para o histórico \"%s\" já foi realizado.",
                                    new Object[]{
                                        retorno.getDescricao()
                                    }));
                        }
                        if (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(), retorno.getDataInicioSituacao()) == 0) {
                            retorno.setDataFinalSituacao(negocio.comuns.utilitarias.Calendario.hoje());
                        } else {
                            //se é o mesmo dia da férias e retorno, então o retorno começa amanhã
                            retorno.setDataInicioSituacao(Uteis.obterDataFutura(carencia.getDataFinalSituacao(), 2));
                            retorno.setDataFinalSituacao(Uteis.obterDataAnterior(retorno.getDataFinalSituacao(), getNrDias()));
                        }
                        data = retorno.getDataFinalSituacao();
                        //setando que o retorno manual da operação de origem já foi executado
                        retorno.setRetornoManual(true);
                        if(!ultimo.getCodigo().equals(retorno.getCodigo()) && ultimo.getAvencer()){ // trata hisotico a vencer
                            ultimo.setDataInicioSituacao(retorno.getDataInicioSituacao());
                            ultimo.setDataFinalSituacao(Uteis.obterDataAnterior(ultimo.getDataFinalSituacao(), getNrDias()));
                            retorno.setDataFinalSituacao(retorno.getDataInicioSituacao());
                            getFacade().getHistoricoContrato().alterarSemCommit(ultimo, false);
                            data = ultimo.getDataFinalSituacao();
                        }
                        if (getResponsavelOperacao() != null && !UteisValidacao.emptyNumber(getResponsavelOperacao().getCodigo())){
                            retorno.setResponsavelRegistro(getResponsavelOperacao());
                        }
                        getFacade().getHistoricoContrato().alterarSemCommit(retorno, false);
                    }
                }
            }

            return data;
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosRetornoManualPeriodoAcesso() throws Exception {
        try {
            //alterando periodo Férias
            PeriodoAcessoClienteVO carencia = getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoContratoTipo(getContratoVO().getCodigo(), "CR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (carencia != null) {
                if (Calendario.igual(negocio.comuns.utilitarias.Calendario.hoje(), carencia.getDataInicioAcesso())) {
                    carencia.setDataFinalAcesso(negocio.comuns.utilitarias.Calendario.hoje());
                } else {
                    carencia.setDataFinalAcesso(Uteis.obterDataAnterior(negocio.comuns.utilitarias.Calendario.hoje(), 1));
                }
                getFacade().getPeriodoAcessoCliente().alterarSemCommit(carencia);
            }

            //alterando periodo retorno Férias
            PeriodoAcessoClienteVO retorno = getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoContratoTipo(getContratoVO().getCodigo(), "RC", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (retorno != null) {
                if (Calendario.igual(negocio.comuns.utilitarias.Calendario.hoje(), retorno.getDataInicioAcesso())) {
                    retorno.setDataFinalAcesso(negocio.comuns.utilitarias.Calendario.hoje());
                } else {
                    //no mesmo dia do retorno manual o aluno estará de atestado, porém, o seu período de acesso de retorno do atestado
                    //liberará o acesso para o aluno. Isso é uma regra!
                    retorno.setDataInicioAcesso(Uteis.obterDataFutura(carencia.getDataFinalAcesso(), 2));
                }
                getFacade().getPeriodoAcessoCliente().alterarSemCommit(retorno);
            }
            PeriodoAcessoClienteVO ultimo = getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoContrato(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (ultimo != null) {
            	ultimo.setDataFinalAcesso(Uteis.obterDataAnterior(ultimo.getDataFinalAcesso(), getNrDias()));
            	getFacade().getPeriodoAcessoCliente().alterarSemCommit(ultimo);
            }
        } catch (Exception e) {
            throw e;
        }

    }

    public Boolean getApresentarPeriodoCarencia() {
        return apresentarPeriodoCarencia;
    }

    public void setApresentarPeriodoCarencia(Boolean apresentarPeriodoCarencia) {
        this.apresentarPeriodoCarencia = apresentarPeriodoCarencia;
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataInicioRetorno_Apresentar() {
        return Uteis.getData(dataInicioRetorno);
    }

    public Date getDataInicioRetorno() {
        return dataInicioRetorno;
    }

    public void setDataInicioRetorno(Date dataInicioRetorno) {
        this.dataInicioRetorno = dataInicioRetorno;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public String getDataTerminoRetorno_Apresentar() {
        return Uteis.getData(dataTerminoRetorno);
    }

    public Date getDataTerminoRetorno() {
        return dataTerminoRetorno;
    }

    public void setDataTerminoRetorno(Date dataTerminoRetorno) {
        this.dataTerminoRetorno = dataTerminoRetorno;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Boolean getMensagemErro() {
        return mensagemErro;
    }

    public void setMensagemErro(Boolean mensagemErro) {
        this.mensagemErro = mensagemErro;
    }

    public Integer getNrDiasPermitido() {
        return nrDiasPermitido;
    }

    public void setNrDiasPermitido(Integer nrDiasPermitido) {
        this.nrDiasPermitido = nrDiasPermitido;
    }

    public Integer getNrDiasUsados() {
        return nrDiasUsados;
    }

    public void setNrDiasUsados(Integer nrDiasUsados) {
        this.nrDiasUsados = nrDiasUsados;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public UsuarioVO getResponsavelOperacao() {
        return responsavelOperacao;
    }

    public void setResponsavelOperacao(UsuarioVO responsavelOperacao) {
        this.responsavelOperacao = responsavelOperacao;
    }

    public Integer getTipoJustificativa() {
        return tipoJustificativa;
    }

    public void setTipoJustificativa(Integer tipoJustificativa) {
        this.tipoJustificativa = tipoJustificativa;
    }

    public Integer getNrDiasRestam() {
        return nrDiasRestam;
    }

    public void setNrDiasRestam(Integer nrDiasRestam) {
        this.nrDiasRestam = nrDiasRestam;
    }

    public Boolean getQtdDiasCarenciaMaiorQueContrato() {
        return qtdDiasCarenciaMaiorQueContrato;
    }

    public void setQtdDiasCarenciaMaiorQueContrato(Boolean qtdDiasCarenciaMaiorQueContrato) {
        this.qtdDiasCarenciaMaiorQueContrato = qtdDiasCarenciaMaiorQueContrato;
    }

    public Integer getNrDias() {
        return nrDias;
    }

    public void setNrDias(Integer nrDias) {
        this.nrDias = nrDias;
    }

    public Long getPeriodoCarencia() {
        return periodoCarencia;
    }

    public void setPeriodoCarencia(Long periodoCarencia) {
        this.periodoCarencia = periodoCarencia;
    }


    public Integer getNrDiasContrato() {
        return nrDiasContrato;
    }

    public void setNrDiasContrato(Integer nrDiasContrato) {
        this.nrDiasContrato = nrDiasContrato;
    }

    public Integer getNrDiasRestanteContrato() {
        return nrDiasRestanteContrato;
    }

    public void setNrDiasRestanteContrato(Integer nrDiasRestanteContrato) {
        this.nrDiasRestanteContrato = nrDiasRestanteContrato;
    }

    public Integer getNrDiasUsadoContrato() {
        return nrDiasUsadoContrato;
    }

    public void setNrDiasUsadoContrato(Integer nrDiasUsadoContrato) {
        this.nrDiasUsadoContrato = nrDiasUsadoContrato;
    }

    public ContratoOperacaoVO getContratoOperacaoVO() {
        if (contratoOperacaoVO == null) {
            contratoOperacaoVO = new ContratoOperacaoVO();
        }
        return contratoOperacaoVO;
    }

    public void setContratoOperacaoVO(ContratoOperacaoVO contratoOperacaoVO) {
        this.contratoOperacaoVO = contratoOperacaoVO;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        if (origemSistema == null) {
            origemSistema = OrigemSistemaEnum.ZW;
        }
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }
}

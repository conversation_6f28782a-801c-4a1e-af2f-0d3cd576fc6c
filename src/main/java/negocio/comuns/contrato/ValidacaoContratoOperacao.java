/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import java.sql.Connection;
import java.util.List;
import negocio.comuns.utilitarias.ConsistirException;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.contrato.HistoricoContrato;

/**
 *
 * <AUTHOR>
 * Data:24/03/2011
 * Objetivo: Centralizar as validações comuns às operações: Trancamento, Atestado e Férias.
 */
public class ValidacaoContratoOperacao extends FacadeManager {

    /*
     * Author:Ulisses
     * Data:24/03/2011
     * Objetivo: Realizar as validações de datas, comuns às operações: Trancamento, Atestado e Carência.
     */
    /*
    * # - Alterar nomenclatura | Carência para Férias
    * */
    public static void validarPeriodoOperacao(String nomeOperacao,
            Date dtInicioOperacao, ContratoVO contrato,
            List<ContratoOperacaoVO> listaOperacoesContrato) throws
            ConsistirException, ParseException, Exception {

        Calendar dataInicioOperacao = Calendario.getInstance();
        dataInicioOperacao.setTime(dtInicioOperacao);
        Calendar vigenciaDe = Calendario.getInstance();
        vigenciaDe.setTime(contrato.getVigenciaDe());
        Calendar vigenciaAte = Calendario.getInstance();
        vigenciaAte.setTime(contrato.getVigenciaAteAjustada());
        // Validar se a data inicial da operação é maior que a data início do contrato
        if (Calendario.menorOuIgual(dataInicioOperacao.getTime(), vigenciaDe.getTime())) {
            throw new ConsistirException(String.format("A data de INÍCIO da(o) '%s' deve ser maior que a data de início do contrato",
                    new Object[]{nomeOperacao}));
        }
        // Se a operação for de trancamento, então a data de trancamento não pode ser maior que a data fim do contrato.
        if (nomeOperacao.charAt(0) == 'T') {
            if (dataInicioOperacao.after(vigenciaAte)) {
                throw new ConsistirException("A data de trancamento não pode ser maior que a data de encerramento do contrato.");
            }
        }
        // Validar se a operação que está sendo lançada não vai chocar com outra Operacao já Cadastrada.
        dataInicioOperacao.add(Calendar.DAY_OF_MONTH, -1);
        for (ContratoOperacaoVO operacao : listaOperacoesContrato) {
            Calendar dataFim = Calendario.getInstance();
            dataFim.setTime(operacao.getDataFimEfetivacaoOperacao());
            if (dataInicioOperacao.before(dataFim)) {
                if (operacao.getTrancamento() || operacao.getAtestado() || operacao.getCarenciaFerias() || operacao.getTrancamentoVencido() || operacao.getAfastamentoColetivo()) {
                    throw new ConsistirException(String.format("O período informado para o(a) '%s' "
                            + "não pode ser lançado, pois existe uma operação de '%s' no período de '%s' até '%s'",
                            new Object[]{
                                nomeOperacao,
                                operacao.getTipoOperacao_Apresentar().toUpperCase(),
                                operacao.getDataInicioEfetivacaoOperacao_Apresentar(),
                                operacao.getDataFimEfetivacaoOperacao_Apresentar()
                            }));
                }

            }
        }

    }

    public static void validarSeExisteTrancamentoSemRetorno(ContratoVO contrato) throws Exception {
        //verifica se o contrato está trancado
        HistoricoContratoVO operacao = getFacade().getHistoricoContrato()
                .obterUltimoHistoricoContratoPorContratoTipoHistorico(contrato.getCodigo(), "TR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        //se possui trancamento
        if (operacao != null) {
            //verifica se possui retorno de trancamento
            HistoricoContratoVO retornoPrevisto = getFacade().getHistoricoContrato()
                    .obterUltimoHistoricoContratoPorContratoTipoHistorico(contrato.getCodigo(), "RT", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            //se o retorno existente não é referente ao trancamento ou não existe retorno de trancamento
            if (retornoPrevisto == null || Calendario.menor(retornoPrevisto.getDataFinalSituacao(), operacao.getDataInicioSituacao())) {
                throw new ConsistirException("Não pode lançar outra operação de contrato antes do aluno retornar do trancamento");
            }
        }
    }

    public static boolean validarSeExisteTrancamentoSemRetornoBoolean(ContratoVO contrato) throws Exception {
        try {
            validarSeExisteTrancamentoSemRetorno(contrato);
        } catch (ConsistirException ce) {
            return true;
        }
        return false;
    }

    public static void validarSeExisteTrancamentoDepoisDoCancelamento(ContratoVO contrato, Date dataCancelamento) throws Exception {
        if(dataCancelamento != null){
            //verifica se o o contrato possui operação de trancamento
            HistoricoContratoVO operacao =
                    getFacade().getHistoricoContrato().
                            obterUltimoHistoricoContratoPorContratoTipoHistorico(
                                    contrato.getCodigo().intValue(),
                                    "TR",
                                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            //se possui trancamento
            if (operacao!=null) {
                // Verifica se a data de cancelamento está no período do trancamento
                if(Calendario.menorOuIgual(dataCancelamento, operacao.getDataFinalSituacao())){
                    throw new ConsistirException(String.format("Não é possível cancelar o contrato, pois existe uma operação de TRANCAMENTO no período de '%s' até '%s'. " +
                            "Necessário estornar o trancamento para realizar o cancelamento nesta data.",
                            operacao.getDataInicioSituacao_Apresentar(), operacao.getDataFinalSituacao_Apresentar()));
                }

                HistoricoContratoVO retornoPrevisto = getFacade().getHistoricoContrato()
                        .obterUltimoHistoricoContratoPorContratoTipoHistorico(contrato.getCodigo(), "RT", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                //se o retorno existente e é posterior a data de cancelamento, então contrato estava trancado no periodo, e não pode ser cancelado nessa data
                if (retornoPrevisto == null || Calendario.menor(dataCancelamento, retornoPrevisto.getDataInicioSituacao())) {
                    throw new ConsistirException(String.format("Não é possível cancelar o contrato, pois existe uma operação de TRANCAMENTO no período de '%s' até '%s'. " +
                                    "Necessário estornar o trancamento para realizar o cancelamento nesta data.",
                            operacao.getDataInicioSituacao_Apresentar(), retornoPrevisto.getDataInicioSituacao_Apresentar()));
                }
            }
        }
    }
    
    public static void validarSeExisteTrancamentoSemRetorno(ContratoVO contrato, Connection con) throws Exception {
        //verifica se o contrato está trancado
        HistoricoContrato historicoDAO = new HistoricoContrato(con);
        HistoricoContratoVO operacao =
                historicoDAO.
                obterUltimoHistoricoContratoPorContratoTipoHistorico(
                contrato.getCodigo().intValue(),
                "TR",
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        //verifica se possui retorno de trancamento
        HistoricoContratoVO retornoPrevisto =
                 historicoDAO.
                obterUltimoHistoricoContratoPorContratoTipoHistorico(
                contrato.getCodigo().intValue(),
                "RT",
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        //se possui trancamento
        if (operacao!=null) {
           //se o retorno existente não é referente ao trancamento ou não existe retorno de trancamento
            if ((retornoPrevisto != null && Calendario.menor(retornoPrevisto.getDataFinalSituacao(), operacao.getDataInicioSituacao())
                    ) || (retornoPrevisto ==null)) {
                    throw new ConsistirException("Não pode lançar outra operação de contrato antes do aluno retornar do trancamento");
            }
        }
    }
    
}

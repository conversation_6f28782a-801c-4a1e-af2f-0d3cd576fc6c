/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.financeiro.ComissaoMetaFinananceiraVO;

/**
 *
 * <AUTHOR>
 */
public class ComissaoRelTO  extends SuperTO{
    private String matriculaCliente = "";
    private String nomePessoa = "";
    private Integer codigoCliente = 0;
    private Integer codigoPessoa = 0;
    private Integer codigoConsultorResponsavel = 0;
    private String consultorResponsavel ="";
    private Integer codigoResponsavelRecebimento = 0;
    private String responsavelRecebimento = "";
    private Integer codigoResponsavelLancamento = 0;
    private String responsavelLancamento = "";
    private Double valor = 0.0;
    private String formaPagamento = "";
    private Integer codigoContrato = 0;
    private String situacaoContrato = "";
    private Integer codigoProduto = 0;
    private String contratoAgendadoEspontaneo;
    private Integer duracaoContrato = 0;
    private String tipoContrato = "";
    private Double valorContrato = 0.0;
    private Double valorProduto = 0.0;
    private String nomePlano = "";
    private String nomeProduto = "";
    private Date dataPagamento;
    private Date dataCompensacao;
    private String produtosPagos = "";
    private Double valorDaComissao = 0.0;
    private String configuracao = "";
    private Integer codigoRecibo = 0;
    private int qtdParcelas = 0;
    private Date dataLancamentoContrato;
    private Date dataCompetencia;

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodigoConsultorResponsavel() {
        return codigoConsultorResponsavel;
    }

    public void setCodigoConsultorResponsavel(Integer codigoConsultorResponsavel) {
        this.codigoConsultorResponsavel = codigoConsultorResponsavel;
    }

    public String getConsultorResponsavel() {
        return consultorResponsavel;
    }

    public void setConsultorResponsavel(String consultorResponsavel) {
        this.consultorResponsavel = consultorResponsavel;
    }

    public Integer getCodigoResponsavelRecebimento() {
        return codigoResponsavelRecebimento;
    }

    public void setCodigoResponsavelRecebimento(Integer CodigoResponsavelRecebimento) {
        this.codigoResponsavelRecebimento = CodigoResponsavelRecebimento;
    }

    public String getResponsavelRecebimento() {
        return responsavelRecebimento;
    }

    public void setResponsavelRecebimento(String responsavelRecebimento) {
        this.responsavelRecebimento = responsavelRecebimento;
    }

    public Integer getCodigoResponsavelLancamento() {
        return codigoResponsavelLancamento;
    }

    public void setCodigoResponsavelLancamento(Integer codigoResponsavelLancamento) {
        this.codigoResponsavelLancamento = codigoResponsavelLancamento;
    }

    public String getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(String responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getContratoAgendadoEspontaneo() {
        return contratoAgendadoEspontaneo;
    }

    public void setContratoAgendadoEspontaneo(String contratoAgendadoEspontaneo) {
        this.contratoAgendadoEspontaneo = contratoAgendadoEspontaneo;
    }

    public Integer getDuracaoContrato() {
        return duracaoContrato;
    }

    public void setDuracaoContrato(Integer duracaoContrato) {
        this.duracaoContrato = duracaoContrato;
    }

    public String getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(String tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public Double getValorContrato() {
        return valorContrato;
    }

    public void setValorContrato(Double valorContrato) {
        this.valorContrato = valorContrato;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public String getProdutosPagos() {
        return produtosPagos;
    }

    public void setProdutosPagos(String produtosPagos) {
        this.produtosPagos = produtosPagos;
    }

    public Double getValorDaComissao() {
        return valorDaComissao;
    }

    public void setValorDaComissao(Double valorDaComissao) {
        this.valorDaComissao = valorDaComissao;
    }

    public String getConfiguracao() {
        return configuracao;
    }

    public void setConfiguracao(String configuracao) {
        this.configuracao = configuracao;
    }

    public Integer getCodigoRecibo() {
        return codigoRecibo;
    }

    public void setCodigoRecibo(Integer codigoRecibo) {
        this.codigoRecibo = codigoRecibo;
    }

    public int getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(int qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public Double getValorProduto() {
        return valorProduto;
    }

    public void setValorProduto(Double valorProduto) {
        this.valorProduto = valorProduto;
    }

    public String getNomeProduto() {
        return nomeProduto;
    }

    public void setNomeProduto(String nomeProduto) {
        this.nomeProduto = nomeProduto;
    }

    public Date getDataLancamentoContrato() {
        return dataLancamentoContrato;
    }

    public void setDataLancamentoContrato(Date dataLancamentoContrato) {
        this.dataLancamentoContrato = dataLancamentoContrato;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public Date getDataCompetencia() {
        return dataCompetencia;
    }

    public void setDataCompetencia(Date dataCompetencia) {
        this.dataCompetencia = dataCompetencia;
    }
}

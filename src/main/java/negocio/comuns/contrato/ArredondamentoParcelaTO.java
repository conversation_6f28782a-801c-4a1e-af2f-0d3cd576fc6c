/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

/**
 *
 * <AUTHOR>
 */
public class ArredondamentoParcelaTO {
    
    private Double valorEntrada;
    private Double valorParcelas;
    private Boolean selecionado = false;

    public ArredondamentoParcelaTO(Double valorEntrada, Double valorParcelas) {
        this.valorEntrada = valorEntrada;
        this.valorParcelas = valorParcelas;
    }

    public Double getValorEntrada() {
        return valorEntrada;
    }

    public void setValorEntrada(Double valorEntrada) {
        this.valorEntrada = valorEntrada;
    }

    public Double getValorParcelas() {
        return valorParcelas;
    }

    public void setValorParcelas(Double valorParcelas) {
        this.valorParcelas = valorParcelas;
    }


    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }
    
    
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.plano.IndiceFinanceiroReajustePrecoVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class ContratoRecorrenciaVO extends SuperVO {

    private ContratoVO contrato = null;
    private PessoaVO pessoa = null;
    private Integer fidelidade = 0;
    private String numeroCartao = "";//apenas os quatro primeiros e os quatro últimos digitos, demais preencher com '*'
    private Integer diaVencimentoCartao = 0;
    private String ultimaTransacaoAprovada = "";
    private Double valorMensal = 0.0;
    private Integer diasBloqueioAcesso = 0;
    private Integer diasCancelamentoAutomatico = 0;
    private Boolean renovavelAutomaticamente = false;
    private boolean gerarParcelasValorDifente = true;
    private Double valorAnuidade = 0.0;
    private Integer diaVencimentoAnuidade = 0;
    private Integer mesVencimentoAnuidade = 0;
    private Date dataInutilizada = null;
    private IndiceFinanceiroReajustePrecoVO indiceFinanceiroVO; // atributo Transient
    private boolean anuidadeNaParcela = false;
    private Integer parcelaAnuidade;
    private boolean cancelamentoProporcional = false;
    private Integer qtdDiasCobrarProximaParcela;
    private Integer qtdDiasCobrarAnuidadeTotal;
    private boolean parcelarAnuidade = false;

    private Double valorMensalNegociado = 0.0;

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public static void validarDados(ContratoRecorrenciaVO obj) throws ConsistirException {
        if (obj.getContrato() == null || obj.getContrato().getCodigo().intValue() == 0) {
            throw new ConsistirException("Contrato deve ser informado (ContratoRecorrencia)");
        }
        if (obj.getPessoa() == null || obj.getPessoa().getCodigo().intValue() == 0) {
            throw new ConsistirException("Pessoa deve ser informado (ContratoRecorrencia)");
        }
        if (obj.getFidelidade().intValue() == 0) {
            throw new ConsistirException("A Fidelidade do Contrato de Recorrência deve ser informada (ContratoRecorrencia)");
        }
        if (obj.getDiaVencimentoCartao().intValue() == 0) {
            throw new ConsistirException("O Dia do Vencimento Cartão vinculado ao Contrato de Recorrência deve ser informado (ContratoRecorrencia)");
        }
        if (obj.getValorMensal().doubleValue() == 0) {
            throw new ConsistirException("O Valor Mensal do Contrato Recorrência deve ser informado (ContratoRecorrencia)");
        }
        if (obj.getValorMensal().doubleValue() == 0) {
            throw new ConsistirException("O Valor Mensal do Contrato Recorrência deve ser informado (ContratoRecorrencia)");
        }
    }

    public Integer getDiaVencimentoAnuidade() {
        return diaVencimentoAnuidade;
    }

    public void setDiaVencimentoAnuidade(Integer diaVencimentoAnuidade) {
        this.diaVencimentoAnuidade = diaVencimentoAnuidade;
    }

    public Integer getDiaVencimentoCartao() {
        return diaVencimentoCartao;
    }

    public void setDiaVencimentoCartao(Integer diaVencimentoCartao) {
        this.diaVencimentoCartao = diaVencimentoCartao;
    }

    public Integer getDiasBloqueioAcesso() {
        return diasBloqueioAcesso;
    }

    public void setDiasBloqueioAcesso(Integer diasBloqueioAcesso) {
        this.diasBloqueioAcesso = diasBloqueioAcesso;
    }

    public Integer getDiasCancelamentoAutomatico() {
        return diasCancelamentoAutomatico;
    }

    public void setDiasCancelamentoAutomatico(Integer diasCancelamentoAutomatico) {
        this.diasCancelamentoAutomatico = diasCancelamentoAutomatico;
    }

    public Integer getFidelidade() {
        return fidelidade;
    }

    public void setFidelidade(Integer fidelidade) {
        this.fidelidade = fidelidade;
    }

    public Integer getMesVencimentoAnuidade() {
        return mesVencimentoAnuidade;
    }

    public void setMesVencimentoAnuidade(Integer mesVencimentoAnuidade) {
        this.mesVencimentoAnuidade = mesVencimentoAnuidade;
    }

    public String getNumeroCartao() {
        return numeroCartao;
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public Boolean getRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public String getRenovavelAutomaticamente_Apresentar() {
        return renovavelAutomaticamente ? "Sim" : "Não";
    }

    public void setRenovavelAutomaticamente(Boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public String getUltimaTransacaoAprovada() {
        return ultimaTransacaoAprovada;
    }

    public void setUltimaTransacaoAprovada(String ultimaTransacaoAprovada) {
        this.ultimaTransacaoAprovada = ultimaTransacaoAprovada;
    }

    public Double getValorAnuidade() {
        return valorAnuidade;
    }

    public void setValorAnuidade(Double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public Double getValorMensal() {
        return valorMensal;
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public Date getDataInutilizada() {
        return dataInutilizada;
    }

    public void setDataInutilizada(Date dataInutilizada) {
        this.dataInutilizada = dataInutilizada;
    }

    public String getValorAnuidade_Apresentar(){
        return Formatador.formatarValorMonetario(valorAnuidade);
    }

    public String getValorMensal_Apresentar(){
        return Formatador.formatarValorMonetario(valorMensal);
    }

    public IndiceFinanceiroReajustePrecoVO getIndiceFinanceiroVO() {
        return indiceFinanceiroVO;
    }

    public void setIndiceFinanceiroVO(IndiceFinanceiroReajustePrecoVO indiceFinanceiroVO) {
        this.indiceFinanceiroVO = indiceFinanceiroVO;

}

    public boolean isAnuidadeNaParcela() {
        return anuidadeNaParcela;
    }

    public void setAnuidadeNaParcela(boolean anuidadeNaParcela) {
        this.anuidadeNaParcela = anuidadeNaParcela;
    }

    public Integer getParcelaAnuidade() {
        if (parcelaAnuidade == null) {
            parcelaAnuidade = 0;
        }
        return parcelaAnuidade;
    }

    public void setParcelaAnuidade(Integer parcelaAnuidade) {
        this.parcelaAnuidade = parcelaAnuidade;
    }

    public boolean isCancelamentoProporcional() {
        return cancelamentoProporcional;
    }

    public void setCancelamentoProporcional(boolean cancelamentoProporcional) {
        this.cancelamentoProporcional = cancelamentoProporcional;
    }

    public Integer getQtdDiasCobrarProximaParcela() {
        if (qtdDiasCobrarProximaParcela == null) {
            qtdDiasCobrarProximaParcela = 0;
        }
        return qtdDiasCobrarProximaParcela;
    }

    public void setQtdDiasCobrarProximaParcela(Integer qtdDiasCobrarProximaParcela) {
        this.qtdDiasCobrarProximaParcela = qtdDiasCobrarProximaParcela;
    }

    public Integer getQtdDiasCobrarAnuidadeTotal() {
        if (qtdDiasCobrarAnuidadeTotal == null) {
            qtdDiasCobrarAnuidadeTotal = 0;
        }
        return qtdDiasCobrarAnuidadeTotal;
    }

    public void setQtdDiasCobrarAnuidadeTotal(Integer qtdDiasCobrarAnuidadeTotal) {
        this.qtdDiasCobrarAnuidadeTotal = qtdDiasCobrarAnuidadeTotal;
    }

    public boolean isGerarParcelasValorDifente() {
        return gerarParcelasValorDifente;
    }

    public void setGerarParcelasValorDifente(boolean gerarParcelasValorDifente) {
        this.gerarParcelasValorDifente = gerarParcelasValorDifente;
    }

    public boolean isParcelarAnuidade() {
        return parcelarAnuidade;
    }

    public void setParcelarAnuidade(boolean parcelarAnuidade) {
        this.parcelarAnuidade = parcelarAnuidade;
    }

    public Double getValorMensalNegociado() {
        return valorMensalNegociado;
    }

    public void setValorMensalNegociado(Double valorMensalNegociado) {
        this.valorMensalNegociado = valorMensalNegociado;
    }
}

package negocio.comuns.contrato;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.PlanoDuracaoCreditoTreinoVO;
import negocio.comuns.plano.PlanoVO;

import java.util.Date;

/**
 * Created by ulisses on 13/11/2015.
 */
public class ContratoDuracaoCreditoTreinoVO extends SuperVO {

    private Integer codigo;
    private ContratoDuracaoVO contratoDuracaoVO;
    private TipoHorarioCreditoTreinoEnum tipoHorarioCreditoTreinoEnum;
    private Integer numeroVezesSemana;
    private Integer quantidadeCreditoCompra;
    private Integer quantidadeCreditoMensal;
    private Integer quantidadeCreditoDisponivel;
    private double  valorUnitario;
    private Integer quantidadeTransferenciaSaldo = 0; // atributo transient
    private double totalTransferenciaSaldo =0;// atributo transient
    private ContratoVO contratoTransferenciaSaldo;
    private double  valorUnitarioCompraOriginal;// atributo transient
    private boolean creditoTreinoNaoCumulativo = false;
    private Date dataUltimoCreditoMensal;
    private boolean vindoDeTransferencia = false;

    @NaoControlarLogAlteracao
    private Integer quantidadeCreditoUtilizado;


    private ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoBaseCalculo;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ContratoDuracaoVO getContratoDuracaoVO() {
        return contratoDuracaoVO;
    }

    public void setContratoDuracaoVO(ContratoDuracaoVO contratoDuracaoVO) {
        this.contratoDuracaoVO = contratoDuracaoVO;
    }

    public TipoHorarioCreditoTreinoEnum getTipoHorarioCreditoTreinoEnum() {
        return tipoHorarioCreditoTreinoEnum;
    }

    public void setTipoHorarioCreditoTreinoEnum(TipoHorarioCreditoTreinoEnum tipoHorarioCreditoTreinoEnum) {
        this.tipoHorarioCreditoTreinoEnum = tipoHorarioCreditoTreinoEnum;
    }

    public Integer getNumeroVezesSemana() {
        return numeroVezesSemana;
    }

    public void setNumeroVezesSemana(Integer numeroVezesSemana) {
        this.numeroVezesSemana = numeroVezesSemana;
    }

    public Integer getQuantidadeCreditoCompra() {
        return quantidadeCreditoCompra;
    }

    public void setQuantidadeCreditoCompra(Integer quantidadeCreditoCompra) {
        this.quantidadeCreditoCompra = quantidadeCreditoCompra;
    }

    public Integer getQuantidadeCreditoDisponivel() {
        return quantidadeCreditoDisponivel;
    }

    public void setQuantidadeCreditoDisponivel(Integer quantidadeCreditoDisponivel) {
        this.quantidadeCreditoDisponivel = quantidadeCreditoDisponivel;
    }

    public double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(double valorUnitario) {
        this.valorUnitario = valorUnitario;

    }

    public double getTotalCompra(){
        return this.getQuantidadeCreditoCompra() * this.valorUnitario;
    }


    public static ContratoDuracaoCreditoTreinoVO criarInstancia(PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO, ContratoDuracaoVO contratoDuracaoVO, PlanoVO planoVO){
        ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = new ContratoDuracaoCreditoTreinoVO();
        contratoDuracaoCreditoTreinoVO.setContratoDuracaoVO(contratoDuracaoVO);
        contratoDuracaoCreditoTreinoVO.setQuantidadeCreditoCompra(planoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra());
        contratoDuracaoCreditoTreinoVO.setQuantidadeCreditoMensal(planoDuracaoCreditoTreinoVO.getQuantidadeCreditoMensal());
        contratoDuracaoCreditoTreinoVO.setCreditoTreinoNaoCumulativo(planoVO.isCreditoTreinoNaoCumulativo());
        contratoDuracaoCreditoTreinoVO.setTipoHorarioCreditoTreinoEnum(planoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreinoEnum());
        contratoDuracaoCreditoTreinoVO.setNumeroVezesSemana(planoDuracaoCreditoTreinoVO.getNumeroVezesSemana());
        contratoDuracaoCreditoTreinoVO.setValorUnitario(planoDuracaoCreditoTreinoVO.getValorUnitario());
        return contratoDuracaoCreditoTreinoVO;
    }

    public Integer getQuantidadeCreditoUtilizadoMensal(){
        Integer valor = 0;
        if ((this.quantidadeCreditoCompra != null) && (this.quantidadeCreditoDisponivel != null)){
            Integer totalCompraETransferencia =  this.quantidadeCreditoCompra + ((this.quantidadeTransferenciaSaldo != null) ? this.quantidadeTransferenciaSaldo : 0);
            valor= totalCompraETransferencia - quantidadeCreditoDisponivel;
        }
        return valor;
    }

    public double getValorUtilizadoMensal(){
       return getQuantidadeCreditoUtilizadoMensal() * this.valorUnitario;
    }

    public double getValorUtilizadoTotal(){
        return getQuantidadeCreditoUtilizado() * this.valorUnitario;
    }

    public double getValorTotal(){
        return this.quantidadeCreditoCompra * this.valorUnitario;
    }


    public ContratoDuracaoCreditoTreinoVO getContratoDuracaoCreditoTreinoBaseCalculo() {
        return contratoDuracaoCreditoTreinoBaseCalculo;
    }

    public void setContratoDuracaoCreditoTreinoBaseCalculo(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoBaseCalculo) {
        this.contratoDuracaoCreditoTreinoBaseCalculo = contratoDuracaoCreditoTreinoBaseCalculo;
    }

    public Integer getQuantidadeTransferenciaSaldo() {
        return quantidadeTransferenciaSaldo;
    }

    public void setQuantidadeTransferenciaSaldo(Integer quantidadeTransferenciaSaldo) {
        this.quantidadeTransferenciaSaldo = quantidadeTransferenciaSaldo;
    }

    public void setTotalTransferenciaSaldo(double totalTransferenciaSaldo) {
        this.totalTransferenciaSaldo = totalTransferenciaSaldo;
    }

    public double getTotalTransferenciaSaldo() {
        return totalTransferenciaSaldo;
    }

    public double getValorUnitarioTransferenciaSaldo(){
        if (this.totalTransferenciaSaldo > 0)
            return  this.totalTransferenciaSaldo/quantidadeTransferenciaSaldo;
        return 0;
    }

    public ContratoVO getContratoTransferenciaSaldo() {
        return contratoTransferenciaSaldo;
    }

    public void setContratoTransferenciaSaldo(ContratoVO contratoTransferenciaSaldo) {
        this.contratoTransferenciaSaldo = contratoTransferenciaSaldo;
    }

    public double getValorUnitarioCompraOriginal() {
        return valorUnitarioCompraOriginal;
    }

    public void setValorUnitarioCompraOriginal(double valorUnitarioCompraOriginal) {
        this.valorUnitarioCompraOriginal = valorUnitarioCompraOriginal;
    }

    public Integer getQuantidadeCreditoMensal() {
        return quantidadeCreditoMensal;
    }

    public void setQuantidadeCreditoMensal(Integer quantidadeCreditoMensal) {
        this.quantidadeCreditoMensal = quantidadeCreditoMensal;
    }

    public boolean isCreditoTreinoNaoCumulativo() {
        return creditoTreinoNaoCumulativo;
    }

    public void setCreditoTreinoNaoCumulativo(boolean creditoTreinoNaoCumulativo) {
        this.creditoTreinoNaoCumulativo = creditoTreinoNaoCumulativo;
    }

    public Date getDataUltimoCreditoMensal() {
        return dataUltimoCreditoMensal;
    }

    public void setDataUltimoCreditoMensal(Date dataUltimoCreditoMensal) {
        this.dataUltimoCreditoMensal = dataUltimoCreditoMensal;
    }

    public Integer getQuantidadeCreditoUtilizado() {
        if (quantidadeCreditoUtilizado == null){
            quantidadeCreditoUtilizado = 0;
        }
        return quantidadeCreditoUtilizado < 0 ? quantidadeCreditoUtilizado * (-1) : quantidadeCreditoUtilizado;
    }

    public void setQuantidadeCreditoUtilizado(Integer quantidadeCreditoUtilizado) {
        this.quantidadeCreditoUtilizado = quantidadeCreditoUtilizado;
    }

    public boolean isVindoDeTransferencia() {
        return vindoDeTransferencia;
    }

    public void setVindoDeTransferencia(boolean vindoDeTransferencia) {
        this.vindoDeTransferencia = vindoDeTransferencia;
    }
}

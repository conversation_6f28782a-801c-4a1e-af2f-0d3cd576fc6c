package negocio.comuns.utilitarias;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import java.text.NumberFormat;

public class FormatadorNumerico3Casa implements Converter {

    public FormatadorNumerico3Casa() {
    }

    public Object getAsObject(final FacesContext context, final UIComponent component, final String value) {
        try {
            final NumberFormat nf = NumberFormat.getInstance();
            nf.setMinimumFractionDigits(3);
            nf.setMaximumFractionDigits(3);
            return nf.parse(value).doubleValue();
        } catch (final Exception e) {
            return 0.0;
        }
    }

    public String getAsString(final FacesContext context,
                              final UIComponent component, Object value) {
        if ((value == null) || (value.toString().trim().equals(""))) {
            value = 0.0;
        }
        final NumberFormat nf = NumberFormat.getInstance();
        nf.setMinimumFractionDigits(3);
        nf.setMaximumFractionDigits(3);
        return nf.format(Double.valueOf(value.toString()));
    }

}

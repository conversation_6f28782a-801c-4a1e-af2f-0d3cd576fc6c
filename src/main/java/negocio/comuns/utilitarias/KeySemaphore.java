package negocio.comuns.utilitarias;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;

public class KeySemaphore {
    private final ConcurrentHashMap<String, Semaphore> semaphoreMap = new ConcurrentHashMap<>();
    private String createCompositeKey(String nomeMetodo, String key) {
        return nomeMetodo + ":" + key;
    }
    public void acquire(String key, String nomeMetodo) throws InterruptedException {
        String compositeKey = createCompositeKey(nomeMetodo, key);
        Semaphore semaphore = semaphoreMap.computeIfAbsent(compositeKey, k -> new Semaphore(1));
        semaphore.acquire();
    }

    public void release(String key, String nomeMetodo) {
        String compositeKey = createCompositeKey(nomeMetodo, key);
        Semaphore semaphore = semaphoreMap.get(compositeKey);
        if (semaphore != null) {
            semaphore.release();
        }
    }
}
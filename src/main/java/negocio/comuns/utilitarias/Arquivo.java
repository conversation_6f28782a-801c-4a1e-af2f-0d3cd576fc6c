/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.utilitarias;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;

/**
 *
 * <AUTHOR>
 */
public class Arquivo {

    public static byte[] ConverterArquivoParaArrayByte(File arquivo){
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        try {            
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(arquivo.toString());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            arrayOutputStream.close();
            fi.close();            
        } catch (Exception e) {}
        return arrayOutputStream.toByteArray();                        
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

import negocio.comuns.utilitarias.queue.ExecutorEmail;
import negocio.comuns.utilitarias.queue.MsgTO;

/**
 *
 * <AUTHOR>
 */
public final class ThreadEnviarEmail {

    /**
     * Coloca uma mensagem na fila para ser enviada por e-mail
     * @param mensagem
     */
    public static void enfileirar(final MsgTO mensagem) {
        ExecutorEmail.enfileirar(mensagem);
    }

    /**
     * Reponsável por 'finalmente' a Thread Executor, senão ficaria infinitamente viva
     */
    public static void finalmente() {
        ExecutorEmail.enfileirar(new MsgTO(false));
    }
}

package negocio.comuns.utilitarias.validator;

import negocio.comuns.plano.HorarioTurmaVO;
import org.apache.commons.collections4.Predicate;

public class PredicadoHorarioTurma implements Predicate<HorarioTurmaVO> {

    private Integer codigo;

    public PredicadoHorarioTurma(Integer codigo) {
        this.codigo = codigo;
    }

    @Override
    public boolean evaluate(HorarioTurmaVO horarioTurmaVO) {
        return horarioTurmaVO.getCodigo().equals(codigo);
    }
}

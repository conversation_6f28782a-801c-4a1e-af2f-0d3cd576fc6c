/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

/**
 *
 * <AUTHOR>
 */
import java.util.List;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.ec2.AmazonEC2;
import com.amazonaws.services.ec2.AmazonEC2Client;
import com.amazonaws.services.ec2.model.DescribeInstancesRequest;
import com.amazonaws.services.ec2.model.DescribeInstancesResult;
import com.amazonaws.services.ec2.model.Filter;
import com.amazonaws.services.ec2.model.Instance;
import com.amazonaws.services.ec2.model.Reservation;
import java.util.ArrayList;
import java.util.logging.Level;
import java.util.logging.Logger;
import servicos.propriedades.PropsService;

public class AwsConsoleApp {

    /*
     * Before running the code:
     *      Fill in your AWS access credentials in the provided credentials
     *      file template, and be sure to move the file to the default location
     *      (~/.aws/credentials) where the sample code will load the
     *      credentials from.
     *      https://console.aws.amazon.com/iam/home?#security_credential
     *
     * WANRNING:
     *      To avoid accidental leakage of your credentials, DO NOT keep
     *      the credentials file in your source directory.
     */
    static AmazonEC2 ec2;

    static {
        try {
            init();
        } catch (Exception ex) {
            Logger.getLogger(AwsConsoleApp.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    /**
     * The only information needed to create a client are security credentials
     * consisting of the AWS Access Key ID and Secret Access Key. All other
     * configuration, such as the service endpoints, are performed
     * automatically. Client parameters, such as proxies, can be specified in an
     * optional ClientConfiguration object when constructing a client.
     *
     * @see com.amazonaws.auth.BasicAWSCredentials
     * @see com.amazonaws.auth.PropertiesCredentials
     * @see com.amazonaws.ClientConfiguration
     */
    private static void init() throws Exception {
        ec2 = new AmazonEC2Client(new AWSCredentials() {
            @Override
            public String getAWSAccessKeyId() {
                return PropsService.getPropertyValue(PropsService.AWSAccessKeyId);
            }

            @Override
            public String getAWSSecretKey() {
                return PropsService.getPropertyValue(PropsService.AWSSecretKey);
            }
        });
        ec2.setRegion(Region.getRegion(Regions.fromName(PropsService.getPropertyValue(PropsService.AWSRegion))));
    }

    public static List<String> getInstancesAddresses(final String tagSearch) {
        List<String> lista = new ArrayList<String>();

        Filter f = new Filter();
        List<String> values = new ArrayList<String>();
        f.setName("tag:Name");
//        values.add("prod-zw-*");
        values.add(tagSearch);
        f.setValues(values);
        DescribeInstancesRequest describeInstanceRequest = new DescribeInstancesRequest().withFilters(f);
        DescribeInstancesResult describeInstanceResult = ec2.describeInstances(describeInstanceRequest);
        List<Reservation> reservs = describeInstanceResult.getReservations();
        for (Reservation reserv : reservs) {
            List<Instance> instances = reserv.getInstances();
            for (Instance instance : instances) {
                Uteis.logar(null, String.format("%s - %s ", instance.getTags(),
                        instance.getPrivateIpAddress()));
                lista.add(String.format("%s:28080", instance.getPrivateIpAddress()));
            }
        }

        return lista;


    }

    public static void main(String[] args) throws Exception {
        try {
            System.out.println(getInstancesAddresses("prod-zw*"));
        } catch (AmazonServiceException ase) {
            System.out.println("Caught Exception: " + ase.getMessage());
            System.out.println("Reponse Status Code: " + ase.getStatusCode());
            System.out.println("Error Code: " + ase.getErrorCode());
            System.out.println("Request ID: " + ase.getRequestId());
        }
    }
}

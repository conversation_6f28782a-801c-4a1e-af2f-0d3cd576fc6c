/*
 * OpcaoPerfilAcesso.java
 *
 * Created on 6 de Setembro de 2007, 16:58
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package negocio.comuns.utilitarias;

/**
 *
 * <AUTHOR>
 */
public class OpcaoPerfilAcesso {
    public static int TP_ENTIDADE = 1;
    public static int TP_FUNCIONALIDADE = 2;
    public static int TP_FUNCIONALIDADE_FINANCEIRO = 3;
    public static int TP_FUNCIONALIDADE_ESTOQUE = 4;
    
    private String nome;
    private String titulo;
    private int tipo;
    private String apresentarPermissao;
    private String hint;
    private AgrupadorFuncionalidadeEnum agrupador;

    /** Creates a new instance of OpcaoPerfilAcesso */
    public OpcaoPerfilAcesso() {
        inicializar();
    }

    public OpcaoPerfilAcesso(String nomePrm, String tituloPrm, int tipoPrm, String nomePermissao) {
        this(nomePrm, tituloPrm, tipoPrm, nomePermissao, null);
    }

    public OpcaoPerfilAcesso(String nomePrm, String tituloPrm, int tipoPrm,
                             String nomePermissao, AgrupadorFuncionalidadeEnum agrupadorEnum) {
        nome = nomePrm;
        titulo = tituloPrm;
        tipo = tipoPrm;
        apresentarPermissao = nomePermissao;
        agrupador = agrupadorEnum;
    }
    
    private void inicializar() {
        setNome("");
        setTitulo("");
        setTipo(TP_ENTIDADE);
        setApresentarPermissao("");
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public int getTipo() {
        return tipo;
    }

    public void setTipo(int tipo) {
        this.tipo = tipo;
    }

    public String getApresentarPermissao() {
        return apresentarPermissao;
    }

    public void setApresentarPermissao(String apresentarPermissao) {
        this.apresentarPermissao = apresentarPermissao;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }

    public AgrupadorFuncionalidadeEnum getAgrupador() {
        return agrupador;
    }

    public void setAgrupador(AgrupadorFuncionalidadeEnum agrupador) {
        this.agrupador = agrupador;
    }
}

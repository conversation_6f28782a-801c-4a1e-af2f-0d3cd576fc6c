package negocio.comuns.utilitarias.reflexao;

/**
 * Created by johny<PERSON> on 16/01/2017.
 */
public class PropriedadePadrao {

    private Propriedade propriedade;

    private Object valorPadrao;

    private Propriedade[] substitutas = new Propriedade[0];

    public PropriedadePadrao(){}

    public PropriedadePadrao(Propriedade propriedade, Object valorPadrao){
        this.propriedade = propriedade;
        this.valorPadrao = valorPadrao;
    }

    public PropriedadePadrao(Propriedade propriedade, Object valorPadrao, Propriedade[] substitutas){
        this(propriedade, valorPadrao);
        this.substitutas = substitutas;
    }

    public static PropriedadePadrao criar(Propriedade propriedade, Object valorPadrao){
        return new PropriedadePadrao(propriedade, valorPadrao);
    }

    public static PropriedadePadrao criar(Propriedade propriedade, Object valorPadrao, Propriedade substituta){
        return new PropriedadePadrao(propriedade, valorPadrao, new Propriedade[]{substituta});
    }

    public static PropriedadePadrao criar(Propriedade propriedade, Object valorPadrao, Propriedade[] substitutas){
        return new PropriedadePadrao(propriedade, valorPadrao, substitutas);
    }

    public Propriedade getPropriedade() {
        return propriedade;
    }

    public void setPropriedade(Propriedade propriedade) {
        this.propriedade = propriedade;
    }

    public Object getValorPadrao() {
        return valorPadrao;
    }

    public void setValorPadrao(Object valorPadrao) {
        this.valorPadrao = valorPadrao;
    }

    public Propriedade[] getSubstitutas() {
        return substitutas;
    }
}

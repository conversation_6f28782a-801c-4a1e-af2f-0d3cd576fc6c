package negocio.comuns.utilitarias.reflexao;

/**
 * Created by johny<PERSON> on 16/01/2017.
 */
public enum Propriedade {

    CONVENIO_EXTANSAO_ARQUIVO_REMESSA("extensaoArquivoRemessa"),
    CONVENIO_EXTANSAO_ARQUIVO_RETORNO("extensaoArquivoRetorno"),
    CONVENIO_SEQUENCIAL_ARQUIVO("sequencialDoArquivo"),
    CONVENIO_NUMERO_CONTRADO("numeroContrato"),
    CONVENIO_HOST_SFTP("hostSFTP"),
    CONVENIO_PORTA_SFTP("portSFTP"),
    CONVENIO_USUARIO_SFTP("userSFTP"),
    CONVENIO_SENHA_SFTP("pwdSFTP"),
    CONVENIO_DIRETORIO_OUT("diretorioRemotoTIVIT_OUT"),
    CONVENIO_DIRETORIO_IN("diretorioRemotoTIVIT_IN"),
    CONVENIO_DIRETORIO_LOCAL("diretorioLocalTIVIT"),
    CONVENIO_DIRETORIO_LOCAL_UPLOAD("diretorioLocalUploadTIVIT"),
    CONVENIO_DIRETORIO_LOCAL_DOWNLOAD("diretorioLocalDownloadTIVIT"),
    CONVENIO_SEQUENCIAL_ARQUIVO_CANCELAMENTO("sequencialArquivoCancelamento"),
    CONVENIO_DIRETORIO_CANCELAMENTO_OUT("diretorioGETNET_CANCELAMENTO_OUT"),
    CONVENIO_DIRETORIO_CANCELAMENTO_IN("diretorioGETNET_CANCELAMENTO_IN"),
    NOME_CHAVE_BIN("nomeChaveBIN"),
    CHAVE_BIN("chaveBIN");


    private String nome;

    private Propriedade(String nome){
        this.nome = nome;
    }

    public String getNome() {
        return nome;
    }

}


/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Predicate;

/**
 *
 * <AUTHOR>
 */
public final class ColecaoUtils {

    public static Object find(Collection lista, Predicate predicado) {
        return CollectionUtils.find(lista, predicado);
    }

    public static void filter(Collection lista, Predicate predicado) {
        CollectionUtils.filter(lista, predicado);
    }

    public static boolean exists(Collection lista, Predicate predicado) {
        return CollectionUtils.exists(lista, predicado);
    }
    
    public static int count(Collection lista, Predicate predicado){
        return CollectionUtils.countMatches(lista, predicado);
    }

    public static List<Integer> convertStringToInt(List<String> list){
        List<Integer> result = new ArrayList<Integer>();
        for(String obj : list){
            result.add(Integer.valueOf(obj));
        }
        return result;
    }
}

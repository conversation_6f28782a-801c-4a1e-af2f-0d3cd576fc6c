/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias.queue;

import java.net.InetAddress;

import controle.arquitetura.SuperControle;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import servicos.propriedades.PropsService;

/**
 * Toda vez que utilizar este Executor, precisa ser invocado o método 'finalmente' dentro de um bloco 'finally'.
 * Caso contrário o processo ficará eternamente vivo.
 * <AUTHOR>
 */
public class ExecutorEmail extends Thread {

    private static final String[] emailsPara;

    static {
        String value = PropsService.getPropertyValue(PropsService.emailsNotificarValidacaoBI);
        emailsPara = value.split(",");

        ExecutorEmail exec = new ExecutorEmail();
        exec.start();

    }

    public static void enfileirar(final MsgTO msg) {
        Fila.enfileirar(msg);
    }

    private void send(final MsgTO msg) {
        if (msg.isEmailRobo()) {
            UteisEmail uteis;
            try {
                System.out.println("Tentando ENVIAR EMAIL...");
                uteis = new UteisEmail();
                ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPRobo();
                uteis.novo("RobotRunner - Erro processamento", config);
                msg.getMsg().append("<b>From: ").append(InetAddress.getLocalHost().getHostAddress()).append("</b>");
                uteis.enviarEmailN(emailsPara, msg.toString(), msg.getAssunto(), msg.getEmpresa());

                System.out.println("Email enviado com SUCESSO!!");
            } catch (Exception ex) {
                System.out.println("EXC.: Não foi possível enviar email de erro ocorrido. MENSAGEM: '"
                        + msg.toString()
                        + "' , por causa do seguinte erro de email: " + ex.getMessage());
            }
        } else {
            try {
                System.out.println("Tentando ENVIAR EMAIL...");
                UteisEmail uteis = new UteisEmail();
                uteis.novo(msg.getAssunto(), msg.getConfig());
                uteis.enviarEmailN(msg.getDestinatarios(), msg.toString(), msg.getAssunto(), msg.getEmpresa());
                System.out.println("Email enviado com SUCESSO!!");
            } catch (Exception ex) {
                System.out.println("EXC.: Não foi possível enviar email. MENSAGEM: '" + msg.toString() + "', por causa do seguinte erro: " + ex.getMessage());
            }
        }
    }

    @Override
    public void run() {
        while (!this.isInterrupted()) {
            try {
                MsgTO obj = Fila.desenfileirar();
                if (obj != null) {
                    if (!obj.isAlive()) {
                        this.interrupt();
                    } else if (obj != null) {
                        send(obj);
                    }
                }
                if (!this.isInterrupted()) {
                    Thread.sleep(500);
                }
            } catch (InterruptedException ex) {
                Uteis.logar(ex, ExecutorEmail.class);
//                Logger.getLogger(ExecutorEmail.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }
}

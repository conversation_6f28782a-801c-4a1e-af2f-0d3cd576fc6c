/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias.queue;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;

/**
 *
 * <AUTHOR>
 */
public class MsgTO extends SuperTO {

    private static final long serialVersionUID = -3152349660251021142L;
    private StringBuffer msg;
    private String assunto;
    private String empresa;
    private boolean alive = true;

    private ConfiguracaoSistemaCRMVO config;
    private boolean emailRobo = true;
    private String[] destinatarios;

    public MsgTO(final String empresa, final String assunto, final StringBuffer msg) {
        this.msg = msg;
        this.empresa = empresa;
        this.assunto = assunto;
        this.emailRobo = true;
    }

    public MsgTO(StringBuffer msg, String assunto, String empresa, boolean alive,
                 ConfiguracaoSistemaCRMVO config, boolean emailRobo, String[] destinatarios) {
        this.msg = msg;
        this.assunto = assunto;
        this.empresa = empresa;
        this.alive = alive;
        this.config = config;
        this.emailRobo = emailRobo;
        this.destinatarios = destinatarios;
    }

    public MsgTO(boolean alive) {
        this.alive = alive;
    }

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public StringBuffer getMsg() {
        return msg;
    }

    public void setMsg(StringBuffer msg) {
        this.msg = msg;
    }

    public boolean isAlive() {
        return alive;
    }

    public void setAlive(boolean alive) {
        this.alive = alive;
    }

    @Override
    public String toString() {
        return msg.toString();
    }

    public ConfiguracaoSistemaCRMVO getConfig() {
        return config;
    }

    public void setConfig(ConfiguracaoSistemaCRMVO config) {
        this.config = config;
    }

    public boolean isEmailRobo() {
        return emailRobo;
    }

    public void setEmailRobo(boolean emailRobo) {
        this.emailRobo = emailRobo;
    }

    public String[] getDestinatarios() {
        return destinatarios;
    }

    public void setDestinatarios(String[] destinatarios) {
        this.destinatarios = destinatarios;
    }
}

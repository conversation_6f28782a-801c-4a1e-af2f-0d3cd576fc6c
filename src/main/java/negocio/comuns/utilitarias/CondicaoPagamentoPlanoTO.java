package negocio.comuns.utilitarias;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.comuns.plano.PlanoVO;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by ulisses on 03/08/2017.
 */
public class CondicaoPagamentoPlanoTO extends SuperTO {

    private CondicaoPagamentoVO condicaoPagamentoVO = new CondicaoPagamentoVO();
    private PlanoVO planoVO = new PlanoVO();
    private Integer duracaoPlano = 0;

    public CondicaoPagamentoVO getCondicaoPagamentoVO() {
        return condicaoPagamentoVO;
    }

    public void setCondicaoPagamentoVO(CondicaoPagamentoVO condicaoPagamentoVO) {
        this.condicaoPagamentoVO = condicaoPagamentoVO;
    }

    public PlanoVO getPlanoVO() {
        return planoVO;
    }

    public void setPlanoVO(PlanoVO planoVO) {
        this.planoVO = planoVO;
    }

    public Integer getDuracaoPlano() {
        return duracaoPlano;
    }

    public void setDuracaoPlano(Integer duracaoPlano) {
        this.duracaoPlano = duracaoPlano;
    }

    @Override
    public boolean equals(Object obj){
        if ((obj == null) || (!(obj instanceof CondicaoPagamentoPlanoTO))){
            return false;
        }
        CondicaoPagamentoPlanoTO objComp = (CondicaoPagamentoPlanoTO)obj;
        return objComp.getPlanoVO().getCodigo().equals(this.planoVO.getCodigo()) &&
               objComp.getDuracaoPlano().equals(this.duracaoPlano);

    }

    public static List<CondicaoPagamentoPlanoTO> consultarListaExcluir(List<CondicaoPagamentoPlanoTO> listaAntesAlteracao, List<CondicaoPagamentoPlanoTO> listaAlterada)throws Exception {
        List<CondicaoPagamentoPlanoTO> listaExcluir = new ArrayList<CondicaoPagamentoPlanoTO>();
        for (CondicaoPagamentoPlanoTO condicaoPagamentoPlanoTO : listaAntesAlteracao) {
            if (!listaAlterada.contains(condicaoPagamentoPlanoTO)) {
                listaExcluir.add(condicaoPagamentoPlanoTO);
            }
        }
        return listaExcluir;
    }

    public static List<CondicaoPagamentoPlanoTO> consultarListaIncluir(List<CondicaoPagamentoPlanoTO> listaAntesAlteracao, List<CondicaoPagamentoPlanoTO> listaAlterada)throws Exception {
        List<CondicaoPagamentoPlanoTO> listaIncluir = new ArrayList<CondicaoPagamentoPlanoTO>();
        for (CondicaoPagamentoPlanoTO condicaoPagamentoPlanoTO : listaAlterada) {
            if (!listaAntesAlteracao.contains(condicaoPagamentoPlanoTO)) {
                listaIncluir.add(condicaoPagamentoPlanoTO);
            }
        }
        return listaIncluir;
    }


    public static Comparator COMPARATOR_PLANO_DURACAO = new Comparator() {
        public int compare(Object o1, Object o2) {
            CondicaoPagamentoPlanoTO p1 = (CondicaoPagamentoPlanoTO) o1;
            CondicaoPagamentoPlanoTO p2 = (CondicaoPagamentoPlanoTO) o2;
            int resultado = p1.getPlanoVO().getDescricao().compareTo(p2.getPlanoVO().getDescricao());
            if (resultado == 0){
                return  p1.getDuracaoPlano().compareTo(p2.getDuracaoPlano());
            }
            return resultado;
        }
    };


}

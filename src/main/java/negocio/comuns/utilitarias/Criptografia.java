/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

import controle.arquitetura.security.AlgoritmoCriptoEnum;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.spec.AlgorithmParameterSpec;
import java.security.spec.InvalidKeySpecException;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

/**
 *
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class Criptografia {
    //private static SecretKey skey;

    @SuppressWarnings("unchecked")
    private static Map tamanhosChaves = new HashMap();
    private static BASE64Encoder enc = new BASE64Encoder();
    private static BASE64Decoder dec = new BASE64Decoder();

    static {
        tamanhosChaves.put(AlgoritmoCriptoEnum.ALGORITMO_TRIPLE_DES.getId(),
                AlgoritmoCriptoEnum.ALGORITMO_TRIPLE_DES.getTamanho());

        tamanhosChaves.put(AlgoritmoCriptoEnum.ALGORITMO_DES.getId(),
                AlgoritmoCriptoEnum.ALGORITMO_DES.getTamanho());

        tamanhosChaves.put(AlgoritmoCriptoEnum.ALGORITMO_BLOWFISH.getId(),
                AlgoritmoCriptoEnum.ALGORITMO_BLOWFISH.getTamanho());

        tamanhosChaves.put(AlgoritmoCriptoEnum.ALGORITMO_AES.getId(),
                AlgoritmoCriptoEnum.ALGORITMO_AES.getTamanho());

    }

    public static String encrypt(String text, String chave, AlgoritmoCriptoEnum algoritmo) throws BadPaddingException, NoSuchPaddingException, IllegalBlockSizeException, InvalidKeyException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, UnsupportedEncodingException, InvalidKeySpecException {
        return encrypt(text, chave, algoritmo, true);
    }

    public static String encrypt(String text, String chave, AlgoritmoCriptoEnum algoritmo, boolean usarWorkaround) throws BadPaddingException, NoSuchPaddingException, IllegalBlockSizeException, InvalidKeyException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, UnsupportedEncodingException, InvalidKeySpecException {
        SecretKey skeySpec = getSecretKey(chave, algoritmo.getId());
        AlgorithmParameterSpec paramSpec = new IvParameterSpec(new byte[algoritmo.getTamanho()]);
        Cipher cipher = Cipher.getInstance(algoritmo.getCbc());
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, paramSpec);
        String codigoCripto = enc.encode(cipher.doFinal(text.getBytes()));
        //Workaround para contornar problemas de código criptografado em URLs
        if (usarWorkaround) {
            codigoCripto = codigoCripto.replace("+", "*");
        }
        codigoCripto = codigoCripto.replace("/", "@");
        return codigoCripto;
    }

    public static String decrypt(String text, String chave, AlgoritmoCriptoEnum algoritmo) {
        return decrypt(text, chave, algoritmo, true);
    }

    public static String decrypt(String text, String chave, AlgoritmoCriptoEnum algoritmo, boolean usarWorkaround) {
        StringBuilder ret = new StringBuilder();
        try {
            SecretKey skeySpec = getSecretKey(chave, algoritmo.getId());
            AlgorithmParameterSpec paramSpec = new IvParameterSpec(new byte[algoritmo.getTamanho()]);
            Cipher cipher = Cipher.getInstance(algoritmo.getCbc());
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, paramSpec);
            //Workaround para contornar problemas de código criptografado em URLs
            if (usarWorkaround) {
                text = text.replace("*", "+");
            }
            text = text.replace("@", "/");

            byte[] b = dec.decodeBuffer(text);

            ret.append(new String(cipher.doFinal(b)));
        } catch (Exception e) {
            return e.getMessage();
        }
        return ret.toString();
    }

    public static void encryptFile(String pathAqruivoOriginal, String pathArquivoDestino, String chave, String algoritmo) throws Exception {
        FileInputStream fis;
        FileOutputStream fos;
        CipherInputStream cis;
        SecretKey skey = getSecretKey(chave, algoritmo);
        Cipher cipher = Cipher.getInstance(algoritmo);
        cipher.init(Cipher.ENCRYPT_MODE, skey);

        fis = new FileInputStream(pathAqruivoOriginal);
        cis = new CipherInputStream(fis, cipher);
        fos = new FileOutputStream(pathArquivoDestino);
        int tam = new Long(tamanhosChaves.get(algoritmo).toString()).intValue();
        byte[] b = new byte[tam];
        int i = cis.read(b);
        while (i != -1) {
            fos.write(b, 0, i);
            i = cis.read(b);
        }
        fos.close();
        cis.close();
        fis.close();
    }

    public static void decryptFile(String pathAqruivoOriginal, String pathArquivoDestino, String chave, String algoritmo) throws Exception {
        FileInputStream fis;
        FileOutputStream fos;
        CipherInputStream cis;
        SecretKey skey = getSecretKey(chave, algoritmo);
        Cipher cipher = Cipher.getInstance(algoritmo);
        cipher.init(Cipher.DECRYPT_MODE, skey);

        fis = new FileInputStream(pathAqruivoOriginal);
        cis = new CipherInputStream(fis, cipher);
        fos = new FileOutputStream(pathArquivoDestino);
        int tam = new Long(tamanhosChaves.get(algoritmo).toString()).intValue();
        byte[] b = new byte[tam];
        int i = cis.read(b);
        while (i != -1) {
            fos.write(b, 0, i);
            i = cis.read(b);
        }
        fos.close();
        cis.close();
        fis.close();
    }

    /**
     * Utiliza SHA1, este metodo de criptografia nao pode serdesfeito, uma vez a
     * senha criptografada, ela nao pode ser recuperada
     *
     * @param password
     * @return senha criptografada com SHA1
     */
    public static String encriptaSenha(String password) {
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("SHA-512");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        md5.reset();
        md5.update(password.getBytes());
        return new String(md5.digest());
    }

    /**
     * verifica se uma senha em texto claro é igual a uma senha criptografada
     * com SHA1.
     *
     * @return true se for iigual , false se for diferente
     */
    public static boolean comparaSenhaCriptografada(String passwordClear, String passwordEncriptado) {
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("SHA-512");
        } catch (NoSuchAlgorithmException e) {
            return false;
        }
        md5.reset();
        md5.update(passwordClear.getBytes());
        String senha1 = new String(md5.digest());
        return MessageDigest.isEqual(senha1.getBytes(), passwordEncriptado.getBytes());
    }

    public static SecretKey getSecretKey(String chave, String algoritmo) {
        String keyString = chave;

        int tam = new Long(tamanhosChaves.get(algoritmo).toString()).intValue();
        byte[] keyB = new byte[tam];
        for (int i = 0; i < keyString.length() && i < keyB.length; i++) {
            keyB[i] = (byte) keyString.charAt(i);
        }

        SecretKey skey = new SecretKeySpec(keyB, algoritmo);
        return skey;
    }

    public static String encryptMD5(String senha) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");

            md.update(senha.getBytes());
            BigInteger hash = new BigInteger(1, md.digest());
            String retornaSenha = hash.toString(16);
            return retornaSenha;
        } catch (NoSuchAlgorithmException ns) {
            return null;
        }
    }

    public static void main(String... args) {
        /*String senhaDecrypt = Criptografia.decrypt("d71cb1078e6de5b0a4583e10592819e5dd457311c9121d81f65c82e99c03fdbc==",
         SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM);
         System.out.println(senhaDecrypt);*/
        final String texto = Uteis.encriptar("98b9a58dc46802a8951a7cd69bcbdd84|<EMAIL>|123", "Tr3in0");
        System.out.println("pactotreino://" + texto);
        System.out.println("pactotreino://" + Uteis.desencriptar(texto, "Tr3in0"));
        System.out.println("pactotreino://" + Uteis.desencriptar(texto, "Tr3in0"));
    }

    public static String encriptarHexadecimal(String textoPlano, String chave) {
        int faixa = 256;
        int tamanhoChave = chave.length();
        int posChave = -1;
        int offset = new Random().nextInt(faixa);
        int posTextoPlano = 0;
        int codigo;

        if (tamanhoChave == 0) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        result.append(converterParaHexa(offset));

        for (; posTextoPlano < textoPlano.length(); posTextoPlano++) {
            codigo = ((int) textoPlano.charAt(posTextoPlano) + offset) % 255;

            if (posChave < (tamanhoChave - 1)) {
                posChave++;
            } else {
                posChave = 0;
            }

            codigo = codigo ^ ((int) chave.charAt(posChave));
            result.append(converterParaHexa(codigo));
            offset = codigo;
        }

        return result.toString();
    }

    private static String converterParaHexa(int codigo) {
        String hexConvertido = Integer.toHexString(codigo);
        if (hexConvertido.length() == 1) {
            hexConvertido = '0' + hexConvertido;
        }
        return hexConvertido;
    }

}

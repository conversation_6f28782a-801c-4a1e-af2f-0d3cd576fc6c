package negocio.comuns.utilitarias;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.security.LoginControle;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.FacadeManager;

import java.util.Enumeration;
import java.util.Hashtable;

public class OpcoesPerfilAcesso {

    private static Hashtable<String, Hashtable> aplicacao;
    public static String ARQUITETURA = "ARQUITETURA";
    public static String ZW_ADM = "ZW ADM";
    public static String ZW_EST = "ZW ESTUDIO";
    public static String ZW_CRM = "ZW CRM";
    public static String ZW_FIN = "ZW FIN";
    public static String ZW_CE = "ZW CE";
    public static String ZW_NOTAS = "ZW NOTA FISCAL";
    public static String ZW_PESSOAS = "PESSOAS";

    private static void inicializarOpcoesAplicacao() {
        aplicacao = new Hashtable<String, Hashtable>();
        aplicacao.put(ARQUITETURA, inicializarArquitetura());
        aplicacao.put(ZW_ADM, inicializarModuloZillyon());
        aplicacao.put(ZW_EST, inicializarEstudio());
        aplicacao.put(ZW_CRM, inicializarModuloCRM());
        aplicacao.put(ZW_FIN, inicializarModuloFinanceiro());
        aplicacao.put(ZW_CE, inicializarCE());
        aplicacao.put(ZW_NOTAS, inicializarNOTAS());
        aplicacao.put(ZW_PESSOAS, inicializarPessoas());
    }

    public static Hashtable getAplicacao() {
        inicializarOpcoesAplicacao();
        return aplicacao;
    }

    public static Hashtable inicializarArquitetura(){

        Hashtable<String, OpcaoPerfilAcesso> opcoesPacote = new Hashtable<String, OpcaoPerfilAcesso>();

        opcoesPacote.put("Controle de Log", new OpcaoPerfilAcesso("Log",
                "1.01 - Controle de Log", OpcaoPerfilAcesso.TP_ENTIDADE, "1.01 - Logs"));

        opcoesPacote.put("Perfil de Acesso", new OpcaoPerfilAcesso("PerfilAcesso",
                "1.02 - Perfil de Acesso", OpcaoPerfilAcesso.TP_ENTIDADE, "1.02 - Perfis de acessos"));

        opcoesPacote.put("Usuário", new OpcaoPerfilAcesso("Usuario",
                "1.03 - Usuário", OpcaoPerfilAcesso.TP_ENTIDADE, "1.03 - Usuários"));

        opcoesPacote.put("Local de Acesso", new OpcaoPerfilAcesso("LocalAcesso",
                "1.05 - Local de Acesso", OpcaoPerfilAcesso.TP_ENTIDADE, "1.05 - Locais de acesso"));

        opcoesPacote.put("Gerador de Consultas", new OpcaoPerfilAcesso("GeradorConsultas",
                "1.11 - Gerador de Consultas", OpcaoPerfilAcesso.TP_ENTIDADE, "1.11 - Gerador de Consultas"));

        opcoesPacote.put("Gestão Transações", new OpcaoPerfilAcesso("GestaoTransacoes",
                "4.20 - Gestão Transações", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.20 - Permitir acesso ao Gestão de Transações"));

        opcoesPacote.put("Permissão para alterar senha de outro usuário", new OpcaoPerfilAcesso("Alterar Senhas",
                "1.08 - Permissão para alterar senha de outro usuário", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.08 - Permissão para alterar a senha de outro usuário através do cadastro de usuário"));

        opcoesPacote.put("Permissão para permitir Ativar o BI - Importação", new OpcaoPerfilAcesso("AtivarVerificacaoClientesAtivos",
                "1.12 - Permissão para ativar o BI - Importação", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.12 - Permissão para ativar o BI - Importação"));

        opcoesPacote.put("Permissão para verificar Clientes Ativos", new OpcaoPerfilAcesso("VerificarClientesAtivos",
                "1.13 - Permissão para verificar Clientes Ativos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.13 - Permissão para verificar Clientes Ativos"));

        opcoesPacote.put("Permissão para cadastrar mais de uma digital", new OpcaoPerfilAcesso("CadastrarMaisDeUmaDigital",
                "1.14 - Permissão para cadastrar mais de uma digital", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.14 - Permitir cadastrar mais de uma digital para os clientes, com autenticação."));

        opcoesPacote.put("Servidor Facial", new OpcaoPerfilAcesso("ServidorFacial",
                "1.15 - Servidor Facial", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.15 - Servidor Facial"));

        opcoesPacote.put("Importação de Clientes", new OpcaoPerfilAcesso("Importacao",
                "1.16 - Importação de Clientes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.16 - Importação de Clientes"));

        opcoesPacote.put("Importação de Produtos", new OpcaoPerfilAcesso("ImportacaoProduto",
                "1.17 - Importação  de Produtos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.17 - Importação de Produtos"));

        opcoesPacote.put("Importação de Colaborador", new OpcaoPerfilAcesso("ImportacaoColaborador",
                "1.18 - Importação de Colaborador", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.18 - Importação de Colaborador"));

        opcoesPacote.put("Importação de Fornecedor", new OpcaoPerfilAcesso("ImportacaoFornecedor",
                "1.19 - Importação de Fornecedor", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.19 - Importação de Fornecedor"));

        opcoesPacote.put("Importação de Contas Financeiro", new OpcaoPerfilAcesso("ImportacaoConta",
                "1.20 - Importação de Contas Financeiro", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.20 - Importação de Contas Financeiro"));
        opcoesPacote.put("Importação de Aluno nas Turmas", new OpcaoPerfilAcesso("ImportacaoAlunoTurma",
                "1.21 - Importação de Turmas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.21 - Importação de Aluno nas Turmas"));

        opcoesPacote.put("Importação de Turma", new OpcaoPerfilAcesso("ImportacaoTurma",
                "1.22 - Importação de Turmas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.22 - Importação de Turmas"));

        return opcoesPacote;
    }

    private static boolean integracaoFinanceiroComSistemaAlterData() {
        try {
            if (JSFUtilities.isJSFContext()) {
                if (FacadeManager.getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isHabilitarExportacaoAlterData()) {
                    return true;
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, OpcoesPerfilAcesso.class);
        }
        return false;
    }

    public static Hashtable inicializarModuloFinanceiro(){
        Hashtable<String, OpcaoPerfilAcesso> opcoesPacote = new Hashtable<String, OpcaoPerfilAcesso>();

        opcoesPacote.put("Movimento de Conta Corrente do Cliente", new OpcaoPerfilAcesso("MovimentoContaCorrenteCliente",
                "2.16 - Movimento de Conta Corrente do Cliente", OpcaoPerfilAcesso.TP_ENTIDADE, "2.16 - Movimentos de conta corrente do cliente"));

        opcoesPacote.put("Banco", new OpcaoPerfilAcesso("Banco",
                "4.02 - Banco", OpcaoPerfilAcesso.TP_ENTIDADE, "4.02 - Bancos"));

        opcoesPacote.put("Conta Corrente", new OpcaoPerfilAcesso("ContaCorrente",
                "4.03 - Conta Corrente", OpcaoPerfilAcesso.TP_ENTIDADE, "4.03 - Contas correntes"));

        opcoesPacote.put("Estorno de Movimentação Produto", new OpcaoPerfilAcesso("EstornoMovProduto",
                "4.14 - Estorno de Movimentação Produto", OpcaoPerfilAcesso.TP_ENTIDADE, "4.14 - Estornar movimentações de produto"));

        opcoesPacote.put("Venda de Consumidor", new OpcaoPerfilAcesso("VendaConsumidor",
                "4.16 - Venda de Consumidor", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.16 - Relatório de Venda de Consumidor"));

        opcoesPacote.put("Cupom Fiscal", new OpcaoPerfilAcesso("CupomFiscal",
                "4.17 - Cupons Fiscais", OpcaoPerfilAcesso.TP_ENTIDADE, "4.17 - Emitir e consultar cupom fiscal"));

        opcoesPacote.put("Edição de Pagamento", new OpcaoPerfilAcesso("EdicaoPagamento",
                "4.22 - Edição de Pagamento", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.22 - Edição de Pagamento"));

        opcoesPacote.put("Venda Avulsa - Consultor", new OpcaoPerfilAcesso("ConsultorVendaAvulsa",
                "4.23 - Venda Avulsa - Consultor", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.23 - Realizar uma venda avulsa"));

        opcoesPacote.put("Taxas da Comissão", new OpcaoPerfilAcesso("TaxasComissao",
                "4.26 - Taxas de Comissão para Consultor", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.26 - Configurar taxas de comissão para consultor nas configurações financeiras do ZW"));

        opcoesPacote.put("Renegociação de Parcelas", new OpcaoPerfilAcesso("RenegociacaoParcelas",
                "4.29 - Renegociação de Parcelas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.29 - Renegociar parcelas em aberto"));

        opcoesPacote.put("Renegociação de Valores (Desconto/Juros)", new OpcaoPerfilAcesso("RenegociacaoParcelaValores",
                "4.44 - Renegociação de Valores (Desconto/Juros)", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.44 - Renegociação de Valores (Desconto/Juros)"));

        opcoesPacote.put("Alterar Data na Renegociação de Parcelas", new OpcaoPerfilAcesso("AlterarDataRenegociacaoParcela",
                "4.45 - Alterar Data na Renegociação de Parcelas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.45 - Alterar Data na Renegociação de Parcelas"));


        opcoesPacote.put("Nova conta rateada para um plano de contas pai.", new OpcaoPerfilAcesso("NovaContaPlanoContasRatear",
                "4.33 - Gravar Conta, onde algum plano de contas selecionado é do tipo 'Pai'.", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "4.33 - Gravar Conta, onde algum plano de contas selecionado é do tipo 'Pai'."));

        if (integracaoFinanceiroComSistemaAlterData()){
            opcoesPacote.put("Conta Contabil", new OpcaoPerfilAcesso("ContaContabil",
                    "4.37 - ContaContabil", OpcaoPerfilAcesso.TP_ENTIDADE, "4.37 - Conta contábil"));
        }

        //funcionalidades do financeiro
        opcoesPacote.put("Gestão de Recebíveis", new OpcaoPerfilAcesso("GestaoRecebiveis",
                "9.01 - Gestão de Recebíveis", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.01 - Gestão de Recebíveis"));

        opcoesPacote.put("Gestão de Lotes", new OpcaoPerfilAcesso("GestaoLotes",
                "9.02 - Gestão de Lotes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.02 - Gestão de Lotes"));

        opcoesPacote.put("Rateio de Integração", new OpcaoPerfilAcesso("RateioIntegracao",
                "9.03 - Rateio de Integração", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.03 - Manipular dados de rateio de integração"));

        opcoesPacote.put("Plano de Contas", new OpcaoPerfilAcesso("PlanoContas",
                "9.04 - Plano de Contas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.04 - Manipular dados de plano de contas"));

        opcoesPacote.put("Centro de Custos", new OpcaoPerfilAcesso("CentroCustos",
                "9.05 - Centro de Custos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.05 - Manipular dados de Centro de Custos"));

        opcoesPacote.put("Conta", new OpcaoPerfilAcesso("Conta",
                "9.06 - Conta", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.06 - Manipular dados de Conta"));

        opcoesPacote.put("Lançamentos de Cheques/Cartões Avulsos", new OpcaoPerfilAcesso("LancamentosAvulsos",
                "9.07 - Lançamentos de Cheques/Cartões Avulsos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.07 - Lançamento de cheques\\cartões avulso. Inclusive importar os cheques"));

        opcoesPacote.put("Configurações do Financeiro", new OpcaoPerfilAcesso("ConfiguracaoFinanceiro",
                "9.08 - Configurações do Financeiro", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.08 - Configurações do financeiro"));

        opcoesPacote.put("Tipo de Conta", new OpcaoPerfilAcesso("TipoConta",
                "9.09 - Tipo de Conta", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.09 - Manipular dados de tipo de conta"));

        opcoesPacote.put("Tipo de Documento", new OpcaoPerfilAcesso("TipoDocumento",
                "9.10 - Tipo de Documento", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.10 - Tipo de documento"));

        opcoesPacote.put("Fornecedor", new OpcaoPerfilAcesso("Fornecedor",
                "9.11 - Fornecedor", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.11 - Cadastrar um novo fornecedor"));



        opcoesPacote.put("Agendamento de Lançamentos", new OpcaoPerfilAcesso("AgendamentoFinanceiro",
                "9.12 - Agendamento de Lançamentos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.12 - Agendamento de Lançamentos"));

        opcoesPacote.put("Visualizar Relatórios", new OpcaoPerfilAcesso("VisualizarRelatorios",
                "9.14 - Visualizar Relatórios", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.14 - Visualizar relatórios"));

        opcoesPacote.put("Cadastro de Metas", new OpcaoPerfilAcesso("CadastroMetas",
                "9.15 - Cadastro de Metas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.15 - Cadastro de Metas Financeiras"));

        opcoesPacote.put("Visualizar Meta Financeira da Empresa no BI", new OpcaoPerfilAcesso("VisualizaRMetaFinanceiraEmpresaBI",
                "9.16 - Visualizar Meta Financeira da Empresa no BI", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.16 - Visualizar BI - Metas Financeiras de vendas"));

        opcoesPacote.put("Abrir Caixa-Administrativo", new OpcaoPerfilAcesso("AbrirCaixaAdm",
                "9.17 - Abrir Caixa-Administrativo", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.17 - Abrir caixa administrativo e movimentar contas"));

        opcoesPacote.put("Consultar Histórico Caixa-Administrativo", new OpcaoPerfilAcesso("ConsultarHistCaixaAdm",
                "9.18 - Consultar Histórico Caixa-Administrativo", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.18 - Visualizar as movimentações de caixas anteriores e de outros usuários"));

        opcoesPacote.put("Fechar Caixa de Outro Usuário", new OpcaoPerfilAcesso("FecharCaixaOutroUsuario",
                "9.19 - Fechar Caixa de Outro Usuário", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.19 - Fechar Caixa de Outro Usuário"));

        opcoesPacote.put("Conciliar Saldo", new OpcaoPerfilAcesso("ConciliarSaldo",
                "9.20 - Conciliar Saldo", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.20 - Conciliar o saldo das contas de banco"));

        opcoesPacote.put("Reabrir Caixa", new OpcaoPerfilAcesso("ReabrirCaixa",
                "9.21 - Reabrir Caixa", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.21 - Reabrir e movimentar um caixa administrativo"));

        opcoesPacote.put("Retirar recebíveis dos lotes", new OpcaoPerfilAcesso("RetirarRecebiveisLotes",
                "9.22 - Retirar recebíveis dos lotes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.22 - Retirar cartões\\cheques\\dinheiros de um lote"));

        if (ControleAcesso.isPermiteMultiEmpresas()) {
            opcoesPacote.put("Lançar/Visualizar Lançamentos Financeiros para todas as Empresas", new OpcaoPerfilAcesso("LancarVisualizarLancamentosEmpresas",
                    "9.23 - Lançar/Visualizar Lançamentos Financeiros para todas as Empresas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.23 - Manipular dados (lançar e visualizar) de lançamentos financeiros em todas as empresas"));

            opcoesPacote.put("Visualizar Metas Financeiras de todas as Empresas", new OpcaoPerfilAcesso("VisualizarMetasFinanceirasTodasEmpresas",
                    "9.24 - Visualizar Metas Financeiras de todas as Empresas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.24 - Visualizar as metas financeiras de todas as unidades"));

            opcoesPacote.put("Abrir/Consultar Caixa-Administrativo para todas as Empresas", new OpcaoPerfilAcesso("AbrirConsultarHistCaixaAdmTodasEmpresas",
                    "9.25 - Abrir/Consultar Caixa-Administrativo para todas as Empresas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.25 - Reabrir e consultar caixa administrativo de todos os usuários de todas as unidades"));

            opcoesPacote.put("Consultar relatórios de todas as empresas", new OpcaoPerfilAcesso("ConsultarInfoTodasEmpresas",
                    "9.47 - Consultar relatórios de todas as empresas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.47 - Consultar relatórios de todas as empresas"));
        }

        opcoesPacote.put("Resumo de Contas", new OpcaoPerfilAcesso("ResumoContas",
                "9.26 - Resumo de Contas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.26 - Resumo de contas"));

        opcoesPacote.put("BI Financeiro", new OpcaoPerfilAcesso("BIFinanceiro",
                "9.27 - BI Financeiro", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.27 - BI do Financeiro"));

        opcoesPacote.put("Editar data de lançamento do lote", new OpcaoPerfilAcesso("EditarDataLancamentoLote",
                "9.28 - Editar data de lançamento do lote", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.28 - Editar data de lançamento ou criação do lote de recebíveis"));

        opcoesPacote.put("Lançar Contas a Pagar", new OpcaoPerfilAcesso("LancarContasPagar",
                "9.29 - Lançar Contas a Pagar - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.29 - Lançar contas a pagar/receber no financeiro"));

        opcoesPacote.put("Quitar Contas a Pagar", new OpcaoPerfilAcesso("QuitarContasPagar",
                "9.30 - Quitar Contas a Pagar - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.30 - Quitar contas a pagar/receber no financeiro"));

        opcoesPacote.put("Excluir conciliação de saldo", new OpcaoPerfilAcesso("ExcluirConciliacaoSaldo",
                "9.31 - Excluir conciliação de saldo - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.31 - Excluir conciliação de saldo"));

        opcoesPacote.put("Visualizar lançamentos", new OpcaoPerfilAcesso("VisualizarLancamentos",
                "9.32 - Visualizar lançamentos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.32 - Visualizar lançamentos"));

        opcoesPacote.put("Retirada automática de recebíveis do financeiro", new OpcaoPerfilAcesso("RetiradaAutomaticaRecebiveisFinanceiro",
                "9.33 - Retirada automática de recebíveis do financeiro", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.33 - Retirada automática de recebíveis do financeiro durante cancelamento de contrato"));

        opcoesPacote.put("Adicionar data de bloqueio de caixa", new OpcaoPerfilAcesso("BloqueioCaixa",
                "9.35 - Adicionar data de bloqueio de caixa", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.35 - Adicionar data de bloqueio de caixa"));

        opcoesPacote.put("Permitir excluir conta a Pagar ou Receber", new OpcaoPerfilAcesso("ExcluirContaPagarEReceber",
                "9.45 - Permitir excluir conta a Pagar ou Receber - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.45 - Permitir excluir conta a Pagar ou Receber - Autorizar"));


        opcoesPacote.put("Fluxo de Caixa", new OpcaoPerfilAcesso("FluxoCaixa",
                "9.58 - Permitir consultar fluxo de caixa", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.58 - Permitir consultar fluxo de caixa"));

        opcoesPacote.put("Lançamento de Conta", new OpcaoPerfilAcesso("LancamentoDeConta",
                "9.59 - Restrição de Lançamento de Conta sem Plano de Conta", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.59 - Restrição de Lançamento de Conta sem Plano de Conta"));

        opcoesPacote.put("Permitir acesso à Conciliação de Contas a Pagar", new OpcaoPerfilAcesso("ConciliacaoContasPagar",
                "9.71 - Permitir acessar Conciliação de Contas a Pagar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.71 - Permitir acessar Conciliação de Contas a Pagar"));
        opcoesPacote.put("Permitir acesso à Conciliação de Contas a Receber", new OpcaoPerfilAcesso("ConciliacaoContasReceber",
                "9.72 - Permitir acessar Conciliação de Contas a Receber", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.72 - Permitir acessar Conciliação de Contas a Receber"));

        opcoesPacote.put("Permite consultar Lotes de Pagamento", new OpcaoPerfilAcesso("ConsultarLotesPagamento",
                "9.102 - Permite consultar Lotes de Pagamento", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.102 - Permite consultar Lotes de Pagamento"));

        opcoesPacote.put("Permite criar Lotes de Pagamento", new OpcaoPerfilAcesso("CriarLotesPagamento",
                "9.103 - Permite criar Lotes de Pagamento", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "9.103 - Permite criar Lotes de Pagamento"));

        return  opcoesPacote;
    }
    public static Hashtable inicializarModuloZillyon(){

        Hashtable<String, OpcaoPerfilAcesso> opcoesPacote = new Hashtable<String, OpcaoPerfilAcesso>();

        opcoesPacote.put("Colaborador", new OpcaoPerfilAcesso("Colaborador",
                "2.07 - Colaborador", OpcaoPerfilAcesso.TP_ENTIDADE, "2.07 - Colaboradores"));

        opcoesPacote.put("Categoria", new OpcaoPerfilAcesso("Categoria",
                "2.01 - Categoria de Clientes", OpcaoPerfilAcesso.TP_ENTIDADE, "2.01 - Categoria de clientes"));

        opcoesPacote.put("País", new OpcaoPerfilAcesso("Pais",
                "2.17 - País", OpcaoPerfilAcesso.TP_ENTIDADE, "2.17 - Países"));

        opcoesPacote.put("Cidade", new OpcaoPerfilAcesso("Cidade",
                "2.02 - Cidade", OpcaoPerfilAcesso.TP_ENTIDADE, "2.02 - Cidades"));

        opcoesPacote.put("Classificação", new OpcaoPerfilAcesso("Classificacao",
                "2.03 - Classificação", OpcaoPerfilAcesso.TP_ENTIDADE, "2.03 - Classificações"));

        opcoesPacote.put("Cliente", new OpcaoPerfilAcesso("Cliente",
                "2.04 - Cliente", OpcaoPerfilAcesso.TP_ENTIDADE, "2.04 - Clientes"));

        opcoesPacote.put("Cliente Classificação", new OpcaoPerfilAcesso("ClienteClassificacao",
                "2.05 - Cliente Classificação", OpcaoPerfilAcesso.TP_ENTIDADE, "2.05 - Classificação de clientes"));

        opcoesPacote.put("Cliente Grupo", new OpcaoPerfilAcesso("ClienteGrupo",
                "2.06 - Cliente Grupo", OpcaoPerfilAcesso.TP_ENTIDADE, "2.06 - Grupo de clientes"));

        opcoesPacote.put("Email", new OpcaoPerfilAcesso("Email",
                "2.09 - Email", OpcaoPerfilAcesso.TP_ENTIDADE, "2.09 - Emails"));

        opcoesPacote.put("Configuração do Sistema", new OpcaoPerfilAcesso("ConfiguracaoSistema",
                "2.08 - Configuração do Sistema", OpcaoPerfilAcesso.TP_ENTIDADE, "2.08 - Configuração do sistema"));

        opcoesPacote.put("Empresa", new OpcaoPerfilAcesso("Empresa",
                "2.10 - Empresa", OpcaoPerfilAcesso.TP_ENTIDADE, "2.10 - Empresa"));

        opcoesPacote.put("Endereço", new OpcaoPerfilAcesso("Endereco",
                "2.11 - Endereço", OpcaoPerfilAcesso.TP_ENTIDADE, "2.11 - Endereço"));

        opcoesPacote.put("Familiar", new OpcaoPerfilAcesso("Familiar",
                "2.12 - Familiar", OpcaoPerfilAcesso.TP_ENTIDADE, "2.12 - Grupo familiar"));

        opcoesPacote.put("Parentesco", new OpcaoPerfilAcesso("Parentesco",
                "2.18 - Parentesco", OpcaoPerfilAcesso.TP_ENTIDADE, "2.18 - Parentesco"));

        opcoesPacote.put("Grau de Instrução", new OpcaoPerfilAcesso("GrauInstrucao",
                "2.13 - Grau de Instrução", OpcaoPerfilAcesso.TP_ENTIDADE, "2.13 -  Grau de Instrução"));

        opcoesPacote.put("Grupo", new OpcaoPerfilAcesso("Grupo",
                "2.14 - Grupo", OpcaoPerfilAcesso.TP_ENTIDADE, "2.14 - Grupos"));

        opcoesPacote.put("Pergunta", new OpcaoPerfilAcesso("Pergunta",
                "2.19 - Pergunta", OpcaoPerfilAcesso.TP_ENTIDADE, "2.19 - Perguntas"));

        opcoesPacote.put("Pergunta Cliente", new OpcaoPerfilAcesso("PerguntaCliente",
                "2.20 - Pergunta Cliente", OpcaoPerfilAcesso.TP_ENTIDADE, "2.20 - Perguntas de clientes"));

        opcoesPacote.put("Pessoa", new OpcaoPerfilAcesso("Pessoa",
                "2.21 - Pessoa", OpcaoPerfilAcesso.TP_ENTIDADE, "2.21 - Pessoa"));

        opcoesPacote.put("Profissão", new OpcaoPerfilAcesso("Profissao",
                "2.22 - Profissão", OpcaoPerfilAcesso.TP_ENTIDADE, "2.22 - Profissões"));

        opcoesPacote.put("Questionário", new OpcaoPerfilAcesso("Questionario",
                "2.23 - Questionário", OpcaoPerfilAcesso.TP_ENTIDADE, "2.23 - Questionários"));

        opcoesPacote.put("Questionário Cliente", new OpcaoPerfilAcesso("QuestionarioCliente",
                "2.24 -Questionário Cliente", OpcaoPerfilAcesso.TP_ENTIDADE, "2.24 - Questionários de clientes"));

        opcoesPacote.put("Questionário Pergunta", new OpcaoPerfilAcesso("QuestionarioPergunta",
                "2.25 - Questionário Pergunta", OpcaoPerfilAcesso.TP_ENTIDADE, "2.25  - Perguntas de questionários"));

        opcoesPacote.put("Reposta da Pergunta", new OpcaoPerfilAcesso("RespostaPergunta",
                "2.26 - Resposta da Pergunta", OpcaoPerfilAcesso.TP_ENTIDADE, "2.26 - Respostas de perguntas"));

        opcoesPacote.put("Resposta Pergunta do Cliente", new OpcaoPerfilAcesso("RespostaPerguntaCliente",
                "2.27 - Resposta da Pergunta do Cliente", OpcaoPerfilAcesso.TP_ENTIDADE, "2.27 - Resposta de perguntas dadas pelos clientes"));

        opcoesPacote.put("Telefone", new OpcaoPerfilAcesso("Telefone",
                "2.28 - Telefone", OpcaoPerfilAcesso.TP_ENTIDADE, "2.28 - Telefones"));

        opcoesPacote.put("Vínculo", new OpcaoPerfilAcesso("Vinculo",
                "2.29 - Incluir Vínculos Cliente/Colaborador", OpcaoPerfilAcesso.TP_ENTIDADE, "2.29 - Vínculos de cliente e de colaborador"));

        opcoesPacote.put("BI LTV", new OpcaoPerfilAcesso("BiLtv",
                "2.30 - Visualizar valor CAC no contrato na página do cliente", OpcaoPerfilAcesso.TP_ENTIDADE, "2.30 - Visualizar valor CAC no contrato na página do cliente"));

        opcoesPacote.put("ConviteAulaExperimental", new OpcaoPerfilAcesso("ConviteAulaExperimental",
                "5.61 - Convite Aula Experimental", OpcaoPerfilAcesso.TP_ENTIDADE, "5.61 - Convite Aula Experimental"));

        opcoesPacote.put("CampanhaCupomDesconto", new OpcaoPerfilAcesso("CampanhaCupomDesconto",
                "5.62 - Campanha Cupom Desconto", OpcaoPerfilAcesso.TP_ENTIDADE, "5.62 - Campanha Cupom Desconto"));

        opcoesPacote.put("IndiceFinanceiroReajustePreco", new OpcaoPerfilAcesso("IndiceFinanceiroReajustePreco",
                "5.63 - Índice Financeiro para Reajuste Preços", OpcaoPerfilAcesso.TP_ENTIDADE, "5.63 - Índice Financeiro para Reajuste Preços"));
        
        opcoesPacote.put("Brinde", new OpcaoPerfilAcesso("Brinde",
                "5.66 - Cadastro de brinde", OpcaoPerfilAcesso.TP_ENTIDADE, "5.66 - Cadastro de brinde"));

        opcoesPacote.put("HistoricoPontos", new OpcaoPerfilAcesso("HistoricoPontos",
                "5.67 - Visualizar relatório de histórico de pontos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "5.67 - Visualizar relatório de histórico de pontos"));
        
        opcoesPacote.put("LancamentoBrindeAluno", new OpcaoPerfilAcesso("LancamentoBrindeAluno",
                "5.68 - Permitir lançar brinde para aluno", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "5.68 - Permitir lançar brinde para aluno"));
        
        opcoesPacote.put("Permitir ajuste manual de pontos", new OpcaoPerfilAcesso("AjusteManualPontos",
                "5.69 - Permitir ajuste manual de pontos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "5.69 - Permitir ajuste manual de pontos"));

        opcoesPacote.put("CampanhaDuracao", new OpcaoPerfilAcesso("CampanhaDuracao",
                "5.70 - Cadastro de campanha pontuação", OpcaoPerfilAcesso.TP_ENTIDADE, "5.70 - Cadastro de campanha pontuação"));

        opcoesPacote.put("ItemCampanha", new OpcaoPerfilAcesso("ItemCampanha",
                "5.71 - Cadastro de item campanha", OpcaoPerfilAcesso.TP_ENTIDADE, "5.71 - Cadastro de item campanha"));

//        opcoesPacote.put("Gympass", new OpcaoPerfilAcesso("Gympass",
//                "5.72 - Cadastro de repasse Gympass", OpcaoPerfilAcesso.TP_ENTIDADE, "5.72 - Cadastro de repasse Gympass"));

        opcoesPacote.put("AutorizacaoCobrancaCliente", new OpcaoPerfilAcesso("AutorizacaoCobrancaCliente",
                "5.73 - Cadastro de autorização de cobrança cliente", OpcaoPerfilAcesso.TP_ENTIDADE, "5.73 - Cadastro de autorização de cobrança cliente"));

        //GTC 14/03/2017: Alterado o único lugar que usava essa permissão 2.30 para usar a 2.29 que é uma permissão de Vínculo mais completa.
        //opcoesPacote.put("Alterar vínculo consultor", new OpcaoPerfilAcesso("AlterarVinculoConsultor",
        //        "2.30 - Alterar Vínculo Cliente/Consultor", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.30 - Alterar o consultor responsável pelo aluno no Boletim de visita"));

        opcoesPacote.put("Lançar mensagem para catraca", new OpcaoPerfilAcesso("LancarMensagemCatraca",
                "2.31 - Lançar ou atualizar mensagem para a catraca", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "2.31 - Lançar ou atualizar mensagem para a catraca"));

        opcoesPacote.put("Lançar aviso ao consultor", new OpcaoPerfilAcesso("LancarMensagemConsultor",
                "2.32 - Lançar aviso ao consultor", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.32 - Lançar aviso ao consultor a partir da tela de informações de cliente"));

        opcoesPacote.put("Lançar aviso médico", new OpcaoPerfilAcesso("LancarMensagemMedico",
                "2.33 - Lançar aviso médico", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.33 - Lançar aviso médico a partir da tela de informações de cliente"));

        opcoesPacote.put("Lançar objetivo do aluno academia", new OpcaoPerfilAcesso("LancarMensagemObjetivoAluno",
                "2.34 - Lançar objetivo do aluno academia", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.34 - Lançar objetivo do aluno na academia"));

        opcoesPacote.put("ClienteObservacao", new OpcaoPerfilAcesso("ClienteObservacao",
                "2.36 - Lançar observação para o cliente", OpcaoPerfilAcesso.TP_ENTIDADE, "2.36 - Lançar observação para o cliente"));

        opcoesPacote.put("Exclusão de Visitantes após Data de Cadastro", new OpcaoPerfilAcesso("ExclusaoVisitantes",
                "2.37 - Exclusão de Visitantes após Data de Cadastro", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.37 - Excluir visitantes após data de cadastro"));

        opcoesPacote.put("Desconto Venda Avulsa", new OpcaoPerfilAcesso("DescontoVendaAvulsa",
                "2.38 - Desconto em produto de Venda Avulsa", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.38 - Lançar descontos em vendas avulsas"));

        opcoesPacote.put("Data Venda Avulsa", new OpcaoPerfilAcesso("DataVendaAvulsa",
                "2.39 - Alterar data de Venda Avulsa", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.39 - Alterar a data de registro da venda"));

        opcoesPacote.put("Permissão para acessar o Social Mailing", new OpcaoPerfilAcesso("PermissaoAcessarSocialMailing",
                "2.40 - Permissão para Acessar o Social Mailing", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.40 - Acessar o Social Mailing"));

        opcoesPacote.put("Permissão para alterar a data de vencimento de parcelas", new OpcaoPerfilAcesso("AlterarDataVencimentoParcela",
                "2.41 - Permissão para alterar a data de vencimento de parcelas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.41 - Alterar a data de vencimento de parcelas"));

        opcoesPacote.put("Alterar saldo da conta corrente do cliente", new OpcaoPerfilAcesso("AlterarSaldoContaCorrenteCliente",
                "2.42 - Alterar saldo da conta corrente do cliente", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.42 - Alterar o saldo da conta corrente (cancelar débito ou devolver crédito) do aluno"));

        opcoesPacote.put("Relatório Business Intelligence", new OpcaoPerfilAcesso("VisualizarBI",
                "2.43 - Visualizar Business Intelligence", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.43 - Visualizar Business Intelligence"));

        opcoesPacote.put("Operação - Caixa em Aberto", new OpcaoPerfilAcesso("CaixaEmAberto",
                "2.44 - Operação - Caixa em Aberto", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.44 - Visualizar ou operar o Caixa em aberto"));

        opcoesPacote.put("Desconto Venda Diaria", new OpcaoPerfilAcesso("DescontoVendaDiaria",
                "2.46 - Desconto em produto de Venda de Diária", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.46 - Lançar desconto em produto de Venda de Diária"));

        opcoesPacote.put("Lançamento de produto coletivo", new OpcaoPerfilAcesso("LancamentoProdutoColetivo",
                "2.47 - Lançamento de produto coletivo", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.47 - Realizar venda de produto para grupo específico"));

        opcoesPacote.put("Estorno de produtos em aberto deste produto coletivo", new OpcaoPerfilAcesso("EstornoProdutoColetivo",
                "2.48 - Estorno de produtos em aberto deste produto coletivo", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.48 - Estornar venda de produto em aberto lançado coletivamente"));

        opcoesPacote.put("Lançamento de atestado de aptidão física", new OpcaoPerfilAcesso("LancamentoAtestadoAptidaoFisica",
                "2.49 - Lançamento de atestado de aptidão física", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.49 - Lançar atestado de aptidão física no cadastro do aluno"));

        opcoesPacote.put("Exclusão de atestado de aptidão física", new OpcaoPerfilAcesso("ExclusaoAtestadoAptidaoFisica",
                "2.50 - Exclusão de atestado de aptidão física", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.50 - Excluir atestado de aptidão física no cadastro do aluno"));

        opcoesPacote.put("Permissão para lançar Free Pass", new OpcaoPerfilAcesso("PermissaoFreePass",
                "2.51 - Lançar Free Pass", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.51 - Lançar Free Pass"));

        opcoesPacote.put("Permissão para transferir cliente de empresa", new OpcaoPerfilAcesso("PermissaoTransferirClienteEmpresa",
                "2.52 - Permissão para transferir cliente de empresa", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.52 - Transferir alunos de uma unidade para outra"));

        opcoesPacote.put("Fechar Negociação do contrato", new OpcaoPerfilAcesso("FecharNegociacaoContrato",
                "2.53 - Fechar Negociação do contrato", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "2.53 - Fechar negociação em um fechamento de venda de contrato"));

        opcoesPacote.put("Enviar SMS no Social Mailing", new OpcaoPerfilAcesso("EnviarSMSSocialMailing",
                "2.54 - Enviar SMS no Social Mailing", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "2.54 - Enviar mensagens via sms para colaborador através do chat social mailing"));

        opcoesPacote.put("Permissão para cadastrar configuração de empresa para produto", new OpcaoPerfilAcesso(
                "ConfiguracaoEmpresaProduto",
                "5.13 - Configuração de empresa para produto", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "5.13 - Cadastrar configuração de empresa para produto"));

        opcoesPacote.put("Cadastros de convênio de cobrança por múltiplas empresas", new OpcaoPerfilAcesso(
                "ConvenioCobrancaEmpresa",
                "5.14 - Cadastros de convênio de cobrança por múltiplas empresas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "5.14 - Cadastros de convênio de cobrança por múltiplas empresas"));

        opcoesPacote.put("Cadastros de modalidades por múltiplas empresas", new OpcaoPerfilAcesso(
                "ModalidadeEmpresa",
                "5.15 - Cadastros de modalidades por múltiplas empresas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "5.15 - Cadastros de modalidades por múltiplas empresas"));

        opcoesPacote.put("Tamanho de Armário", new OpcaoPerfilAcesso("TamanhoArmario",
                "2.55 - Tamanho de Armário",
                OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.55 - Tamanho de Armário"));

        opcoesPacote.put("Alugar Armário", new OpcaoPerfilAcesso("AlugarArmario",
                "2.56 - Alugar Armário",
                OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.56 - Alugar Armário"));

        opcoesPacote.put("Administrar Armários", new OpcaoPerfilAcesso("AdministrarArmarios",
                "2.57 - Administrar Armários",
                OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.57 - Administrar Armários"));

        opcoesPacote.put("Alterar Vigência Armário", new OpcaoPerfilAcesso("AlterarVigenciaArmario",
                "2.58 - Alterar Vigência Armário",
                OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.58 - Alterar Vigência Armário"));

        opcoesPacote.put("Permite alterar matricula", new OpcaoPerfilAcesso("AlterarMatricula",
                "2.59 - Permite alterar matricula",
                OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "2.59 - Alterar número de matrícula do cliente"));

        opcoesPacote.put("Alterar data do boletim de visita", new OpcaoPerfilAcesso("AlterarDataBoletim",
                "2.65 - Alterar data do boletim de visita", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.65 - Alterar data do boletim de visita"));

        opcoesPacote.put("Game Of Results", new OpcaoPerfilAcesso("GameOfResults",
                "2.61 - Acessar Game Of Results", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.61 - Acessar Game Of Results"));


        opcoesPacote.put ("Permissão para lançar desconto no valor Gestão de Personal", new OpcaoPerfilAcesso("DescontoGestãodePersonal",
                "5.59 - Lançar desconto no valor Gestão de Personal",
                OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "5.59 - Lançar desconto Gestão de Personal"));

        opcoesPacote.put("Vizualizar Colaboradores Inativos", new OpcaoPerfilAcesso("ColaboradorInativoSocialMailing",
                "5.60 - Vizualizar Colaboradores Inativos no Social Mailing", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "5.60 - Visualizar e enviar mensagens para colaborador inativo do chat social mailing"));

        opcoesPacote.put ("Permissão para alterar a data de lançamento da parcela em Gestão de Personal", new OpcaoPerfilAcesso("DataLancamentoGestaoPersonal",
                "2.60 - Alterar a data de lançamento da parcela em Gestão de Personal",
                OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.60 - Alterar a data de lançamento da parcela em Gestão de Personal"));

        opcoesPacote.put("Contrato", new OpcaoPerfilAcesso("Contrato",
                "3.01 - Contrato", OpcaoPerfilAcesso.TP_ENTIDADE, "3.01 - Contratos"));

        opcoesPacote.put("Configurações do Convênio de Desconto", new OpcaoPerfilAcesso("ConfiguracaoConvenio",
                "3.02 - Configurações do Convênio de Desconto", OpcaoPerfilAcesso.TP_ENTIDADE, "3.02 - Configurações do Convênio de Desconto"));

        opcoesPacote.put("Convênio de Desconto", new OpcaoPerfilAcesso("ConvenioDesconto",
                "3.03 - Convênio de Desconto", OpcaoPerfilAcesso.TP_ENTIDADE, "3.03 - Convênios de desconto"));

        opcoesPacote.put("Justificativa de Operação", new OpcaoPerfilAcesso("JustificativaOperacao",
                "3.04 - Justificativa de Operação", OpcaoPerfilAcesso.TP_ENTIDADE, "3.04 - Justificativas de operação"));

        opcoesPacote.put("Movimentação do Produto", new OpcaoPerfilAcesso("MovProduto",
                "3.05 - Movimentação do Produto", OpcaoPerfilAcesso.TP_ENTIDADE, "3.05 - Movimentações de produto"));

        opcoesPacote.put("Liberação de Data Base", new OpcaoPerfilAcesso("DataBase",
                "3.06 - Liberação de Data Base", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.06 - Alterar a data em que o contrato do aluno será lançado"));

        opcoesPacote.put("Cancelamento Contrato - Autorizar", new OpcaoPerfilAcesso("CancelamentoContrato_Autorizar",
                "3.07 - Cancelamento Contrato - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.07 - Cancelar contratos"));

        opcoesPacote.put("Liberar cobrança de valores no Cancelamento Contrato - Autorizar", new OpcaoPerfilAcesso("Liberar_CancelamentoContrato_Autorizar",
                "3.08 - Liberar cobrança de valores no Cancelamento Contrato - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.08 - Liberar cobrança de valores no Cancelamento Contrato"));

        opcoesPacote.put("Valor Manual Cancelamento Contrato - Autorizar", new OpcaoPerfilAcesso("ValorManual_CancelamentoContrato_Autorizar",
                "3.09 - Valor Manual Cancelamento Contrato - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.09 - Autorizar valor manual no cancelamento de contrato"));

        opcoesPacote.put("Trancamento Contrato - Autorizar", new OpcaoPerfilAcesso("Trancamento_Autorizar",
                "3.10 - Trancamento do  Contrato - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.10 - Lançar trancamento de contratos"));

        opcoesPacote.put("Bonus para Contrato - Autorizar", new OpcaoPerfilAcesso("Bonus_Autorizar",
                "3.11 - Bonus para Contrato - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.11 - Lançamento de bônus para o contrato do cliente"));

        opcoesPacote.put("Atestado para Contrato - Autorizar", new OpcaoPerfilAcesso("Atestado_Autorizar",
                "3.12 - Atestado para Contrato - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.12 - Lançamento de atestado para o contrato do cliente"));

        opcoesPacote.put("Férias para Contrato - Autorizar", new OpcaoPerfilAcesso("Carencia_Autorizar",
                "3.13 - Férias  para Contrato - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.13 - Lançamento de férias para o contrato do cliente"));

        opcoesPacote.put("Manutenção Modalidade - Autorizar", new OpcaoPerfilAcesso("ManutencaoModalidade_Autorizar",
                "3.14 - Manutenção Modalidade - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.14 - Lançamento de manutenção de modalidade"));

        opcoesPacote.put("Alterar Horário - Autorizar", new OpcaoPerfilAcesso("AlterarHorario_Autorizar",
                "3.15 - Alterar Horário - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.15 - Alterar horário do contrato"));

        opcoesPacote.put("Alterar Data de Trancamento - Autorizar", new OpcaoPerfilAcesso("LiberarDataTrancamentoRetroativo_Autorizar",
                "3.16 - Liberar Data de Trancamento Retroativo - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.16 - Lançamento de data de trancamento retroativo"));

        opcoesPacote.put("Liberar Manutenção Modalidade", new OpcaoPerfilAcesso("LiberarManutencaoModalidade",
                "3.17 - Liberar Manutenção Modalidade", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.17 - Lançamento de liberação da manutenção de modalidade de contrato"));

        opcoesPacote.put("Atestado, Trancamento ou Férias com Frequência - Autorizar", new OpcaoPerfilAcesso("AtestadoCarencia_Autorizar",
                "3.18 - Atestado, Trancamento ou Férias com Frequência - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.18 - Autorizar o atestado, trancamento ou férias em um período que o aluno teve acesso"));

        opcoesPacote.put("Estorno de Contrato - Autorizar", new OpcaoPerfilAcesso("EstornoContrato_Autorizar",
                "3.19 - Estorno de Contrato - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.19 - Permissão para visualizar o Botão Estornar contratos"));

        opcoesPacote.put("Alterar Contrato Agendado/Espontâneo - Autorizar", new OpcaoPerfilAcesso("Alterar_Contrato_AgendadoEspontaneo_Autorizar",
                "3.20 - Alterar Contrato Agendado/Espontâneo - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.20 - Alterar Contrato Agendado/Espontâneo"));

        opcoesPacote.put("Incluir Reposição em Turmas", new OpcaoPerfilAcesso("IncluirReposicaoEmTurmas_Autorizar",
                "3.21 - Incluir Reposição em Turmas - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.21 - Incluir reposição em turmas"));

        opcoesPacote.put("Excluir Reposição em Turmas", new OpcaoPerfilAcesso("ExcluirReposicaoEmTurmas_Autorizar",
                "3.22 - Excluir Reposição em Turmas - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.22 - Desmarcar uma reposição em turmas"));

        opcoesPacote.put("Remarcar Aula Perdida", new OpcaoPerfilAcesso("RemarcarAulaPerdida_Autorizar",
                "3.23 - Remarcar Aula Perdida - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.23 - Remarcar Aula perdida"));

        opcoesPacote.put("Estorno de Operação de Contrato - Autorizar", new OpcaoPerfilAcesso("EstornoOperacaoContrato_Autorizar",
                "3.24 - Estorno de Operação de Contrato - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.24 - Estornar uma operação de contrato"));

        opcoesPacote.put("Liberar multa e custos de Cancelamento de Contrato - Autorizar", new OpcaoPerfilAcesso("LiberarMultaCustosCancelamento_Autorizar",
                "3.25 - Liberar multa e custos de Cancelamento de Contrato - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.25 - Liberar multa e custos de Cancelamento de Contrato"));

        opcoesPacote.put("Alterar Consultor do Contrato",
                new OpcaoPerfilAcesso("AlterarConsultorContrato", "3.26 - Alterar Consultor do Contrato", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.26 - Alterar o consultor responsável pelo contrato"));

        opcoesPacote.put("Alterar Data de Cancelamento - Autorizar", new OpcaoPerfilAcesso("LiberarDataCancelamentoRetroativo_Autorizar",
                "3.27 - Liberar Data de Cancelamento Retroativo - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.27 - Liberar Cancelamento Retroativo"));

        opcoesPacote.put("Alterar Tipo do Cancelamento", new OpcaoPerfilAcesso("AlterarTipoCancelamento",
                "3.28 - Alterar Tipo do Cancelamento", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.28 - Alterar tipo do cancelamento como Quitação Manual, Não devolver ao Cliente e Não cobrar do cliente"));

        opcoesPacote.put("Alterar Data de Vigência de produto com validade - Autorizar", new OpcaoPerfilAcesso("AlterarDataVigenciaValidade_Autorizar",
                "3.29 - Alterar Data de Vigência de produto com validade - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.29 - Alterar data de vigência do produto com validade"));

        opcoesPacote.put("Valor Manual Percentual Multa Cancelamento - Autorizar", new OpcaoPerfilAcesso("ValorManualPercentualMultaCancelamento",
                "3.30 - Valor Manual Percentual Multa Cancelamento - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.30 - Alterar porcentual de multa do cancelamento no momento do lançamento"));

        opcoesPacote.put("Alterar valor e Aplicar Desconto Manutenção Modalidade - Autorizar", new OpcaoPerfilAcesso("AlterarValorManutencaoModalidade",
                "3.31 - Alterar valor e Aplicar Desconto Manutenção Modalidade - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.31 - Alterar e dar desconto na manutenção de modalidade"));

        opcoesPacote.put("Lançar Planos Tipo Bolsa - Autorizar", new OpcaoPerfilAcesso("LancarPlanosTipoBolsa",
                "3.32 - Lançar Planos Tipo Bolsa - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.32 - Realizar a negociação do plano tipo bolsa"));

        opcoesPacote.put("Alterar Horário - Alterar Data da Operação", new OpcaoPerfilAcesso("AlterarHorario_AlterarDatadaOperacao",
                "3.33 - Alterar Horário - Alterar Data da Operação", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.33 - Alterar a data da operação da alteração de horário do contrato"));

        opcoesPacote.put("Adicionar/Alterar Senha Acesso - Autorizar", new OpcaoPerfilAcesso("AdicionarAlterarSenhaAcesso",
                "3.34 - Adicionar/Alterar Senha Acesso - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.34 - Adicionar e alterar Senha do aluno na catraca."));

        opcoesPacote.put("Lançar Ajuste Manual Crédito de Treino", new OpcaoPerfilAcesso("NOVOAJUSTEMANUALCREDITOTREINO",
                "3.35 - Lançar Ajuste Manual Crédito de Treino", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.35 - Lançar ajuste manual de crédito de treino"));
        
        opcoesPacote.put("Alterar manualmente a quantidade de dias de férias permitidos", new OpcaoPerfilAcesso("AlterarManualmenteCarencia",
                "3.36 - Alterar manualmente a quantidade de dias de férias permitidos",
                OpcaoPerfilAcesso.TP_FUNCIONALIDADE, 
                "3.36 - Alterar manualmente a quantidade de dias de férias permitidos"));

        opcoesPacote.put("Permite isentar cobrança de multa/juros da parcela", new OpcaoPerfilAcesso("IsentarMultaJurosParcela",
                "3.37 - Permite isentar cobrança de multa/juros da parcela", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.37 - Permite isentar cobrança de multa/juros da parcela"));
        opcoesPacote.put("Alterar vencimento das parcelas no trancamento", new OpcaoPerfilAcesso("alterarVencimentoParcelasTrancamento",
                "3.38 - Alterar vencimento das parcelas no trancamento", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.38 - Alterar vencimento das parcelas no trancamento"));

        opcoesPacote.put("NFSe Recibo - Autorizar", new OpcaoPerfilAcesso("ApresentarNfseRecibo",
                "3.43 - NFSe Recibo - Autorizar", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.43 - Permissão para visualizar o botão NFSe do recibo"));


        opcoesPacote.put("Aula Avulsa", new OpcaoPerfilAcesso("AulaAvulsaDiaria",
                "4.01 - Aula Avulsa", OpcaoPerfilAcesso.TP_ENTIDADE, "4.01 - Diária"));

        opcoesPacote.put("Gestão Comissão", new OpcaoPerfilAcesso("GestaoComissao",
                "4.21 - Comissão para Professor", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.21 - Relatório de Comissão para Professor"));

        opcoesPacote.put("Gestão de Vendas Online", new OpcaoPerfilAcesso("VendasOnline",
                "4.43 - Gestão de Vendas Online",
                OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "4.43 - Gestão de Vendas Online"));

        opcoesPacote.put("Ambiente", new OpcaoPerfilAcesso("Ambiente",
                "5.01 - Ambiente", OpcaoPerfilAcesso.TP_ENTIDADE, "5.01 - Ambiente"));

        opcoesPacote.put("Categoria Produto", new OpcaoPerfilAcesso("CategoriaProduto",
                "5.02 - Categoria de Produto", OpcaoPerfilAcesso.TP_ENTIDADE, "5.02 - Categorias de produto"));

        opcoesPacote.put("Condição de Pagamento", new OpcaoPerfilAcesso("CondicaoPagamento",
                "5.03 - Condição de Pagamento", OpcaoPerfilAcesso.TP_ENTIDADE, "5.03 - Condições de pagamento"));

        opcoesPacote.put("Desconto", new OpcaoPerfilAcesso("Desconto",
                "5.04 - Desconto", OpcaoPerfilAcesso.TP_ENTIDADE, "5.04 - Desconto"));

        opcoesPacote.put("Horário", new OpcaoPerfilAcesso("Horario",
                "5.05 - Horário", OpcaoPerfilAcesso.TP_ENTIDADE, "5.05 - Horários"));

        opcoesPacote.put("Modalidade", new OpcaoPerfilAcesso("Modalidade",
                "5.06 - Modalidade", OpcaoPerfilAcesso.TP_ENTIDADE, "5.06 - Modalidades"));

        opcoesPacote.put("Nível da Turma", new OpcaoPerfilAcesso("NivelTurma",
                "5.07 - Nível da Turma", OpcaoPerfilAcesso.TP_ENTIDADE, "5.07 - Níveis de turma"));

        opcoesPacote.put("Pacote", new OpcaoPerfilAcesso("Composicao",
                "5.08 - Pacote", OpcaoPerfilAcesso.TP_ENTIDADE, "5.08 - Pacotes"));

        opcoesPacote.put("Plano", new OpcaoPerfilAcesso("Plano",
                "5.09 - Plano", OpcaoPerfilAcesso.TP_ENTIDADE, "5.09 - Planos"));

        opcoesPacote.put("Produto", new OpcaoPerfilAcesso("Produto",
                "5.10 - Produto", OpcaoPerfilAcesso.TP_ENTIDADE, "5.10 - Produtos"));

        opcoesPacote.put("Plano de Texto Padrão", new OpcaoPerfilAcesso("PlanoTextoPadrao",
                "5.11 - Modelo de Contrato e Recibo", OpcaoPerfilAcesso.TP_ENTIDADE, "5.11 - Modelos de contrato e recibo"));

        opcoesPacote.put("Turma", new OpcaoPerfilAcesso("Turma",
                "5.12 - Turma", OpcaoPerfilAcesso.TP_ENTIDADE, "5.12 - Turmas"));

        opcoesPacote.put("Tipo de Plano", new OpcaoPerfilAcesso("PlanoTipo",
                "5.16 - Tipos de plano", OpcaoPerfilAcesso.TP_ENTIDADE, "5.16 - Tipos de plano"));

        opcoesPacote.put("Tipo de Modalidade", new OpcaoPerfilAcesso("TipoModalidade",
                "5.17 - Tipo de Modalidade", OpcaoPerfilAcesso.TP_ENTIDADE, "5.17 - Tipo de modalidade"));

        opcoesPacote.put("Orçamento Turmas", new OpcaoPerfilAcesso("ModeloOrcamento",
                "5.18 - Orçamento Turmas", OpcaoPerfilAcesso.TP_ENTIDADE, "5.18 - Orçamento Turmas"));

        opcoesPacote.put("Caixa Por Operador", new OpcaoPerfilAcesso("CaixaPorOperadorRel",
                "6.01 - Fechamento de Caixa Por Operador", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.01 - Relatório de Fechamento de Caixa por Operador"));

        opcoesPacote.put("Relatório Cliente ", new OpcaoPerfilAcesso("ClienteRel",
                "6.02 - Situação de Clientes Sintético", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.02 - Relatório de Lista de Clientes Simplificada"));

        opcoesPacote.put("Lista Chamada", new OpcaoPerfilAcesso("ListaChamada",
                "6.03 - Lista Chamada", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.03 - Relatório de Lista de Chamada"));

        opcoesPacote.put("Receita Por Periodo Sintetico", new OpcaoPerfilAcesso("ReceitaPorPeriodoSinteticoRel",
                "6.04 - Receita Por Período Sintético", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.04 - Relatório de Receita por Período"));

        opcoesPacote.put("Competência Mensal", new OpcaoPerfilAcesso("CompetenciaMensalRel",
                "6.05 - Competência Mensal", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.05 - Relatório de Competência Mensal"));

        opcoesPacote.put("Faturamento Sintético", new OpcaoPerfilAcesso("FaturamentoSinteticoRel",
                "6.06 - Faturamento Sintético", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.06 - Relatório de Faturamento por período"));

        opcoesPacote.put("Parcela Em Aberto", new OpcaoPerfilAcesso("ParcelaEmAbertoRel",
                "6.07 - Parcela Em Aberto", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.07 - Relatório de Parcelas"));

        opcoesPacote.put("Renegociação de Parcelas com data retroativa", new OpcaoPerfilAcesso("RenegociacaoParcelasRetroativas",
                "4.39 - Renegociação de Parcelas com data retroativa", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.39 - Renegociar parcelas em aberto com data retroativa"));

        opcoesPacote.put("Saldo Conta Corrente de Cliente", new OpcaoPerfilAcesso("SaldoContaCorrenteRel",
                "6.08 - Saldo Conta Corrente de Cliente", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.08 - Relatório de Saldo de Conta Corrente"));

        opcoesPacote.put("Totalizadores de Frenquência", new OpcaoPerfilAcesso("TotalizadorFrequenciaRel",
                "6.09 - Relatórios de Acessos - Lista e Totalizador", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.09 - Relatório de lista de Acessos"));

        opcoesPacote.put("Cliente Por Duração", new OpcaoPerfilAcesso("ClientePorDuracaoRel",
                "6.10 - Contratos Por Duração", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.10 - Relatório de Contratos por Duração"));

        opcoesPacote.put("Relatório Índice de Renovação", new OpcaoPerfilAcesso("IndiceRenovacao",
                "6.11 - BI - Índice de Renovação (IR)", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.11 - Visualizar BI - Índice de Renovação"));

        opcoesPacote.put("Relatório Índice de Conversão", new OpcaoPerfilAcesso("IndiceConversao",
                "6.12 - BI - Conversão de Vendas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.12 - Visualizar BI - Conversão de Vendas"));

        opcoesPacote.put("Relatório Rotatividade", new OpcaoPerfilAcesso("Rotatividade",
                "6.13 - BI - Movimentação de Contratos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.13 - Visualizar BI - Movimentação de Contratos"));

        opcoesPacote.put("Relatório Grupo Risco", new OpcaoPerfilAcesso("Business",
                "6.14 - BI - Grupo de Risco", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.14 - Visualizar BI - Grupo de Risco"));

        opcoesPacote.put("Relatório Pendência Cliente", new OpcaoPerfilAcesso("PendenciaCliente",
                "6.15 - BI - Pendências de Clientes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.15 - Visualizar BI - Pendências de Clientes"));

        opcoesPacote.put("Relatório Contrato Recorrência", new OpcaoPerfilAcesso("ContratoRecorrencia",
                "6.16 - BI - Contrato Recorrência", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.16 - Visualizar relatórios de Contrato Recorrência"));

        opcoesPacote.put("Liberar Relatório de Fechamento de Caixa por Operador - Todos os dias",
                new OpcaoPerfilAcesso("LiberarTodosDiasRelatorioFechamentoCaixaOperador",
                        "6.17 - Visualizar fechamento de caixa por operador sem limite padrão (definido nas configurações da empresa) para a pesquisa", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.17 - Visualizar fechamento de caixa por operador sem limite padrão (definido nas configurações da empresa) para a pesquisa"));

        opcoesPacote.put("Liberar Relatório de Fechamento de Caixa por Operador - Todos os colaboradores",
                new OpcaoPerfilAcesso("LiberarTodosColaboradoresRelatorioFechamentoCaixaOperador",
                        "6.18 - Liberar Relatório de Fechamento de Caixa por Operador - Todos os colaboradores", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.18 - Visualizar o relatório de Fechamento de Caixa por Operador consultando por todos os colaboradores"));

        opcoesPacote.put("Justificar Liberação de Acesso", new OpcaoPerfilAcesso("GestaoControleAcesso",
                "6.19 - Justificar Liberação de Acesso", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.19 - Justificar liberação de acesso a um aluno/visitante"));

        opcoesPacote.put("Enviar email de  fechamento de Acesso", new OpcaoPerfilAcesso("GestaoControleAcessoEmail",
                "6.20 - Enviar Email de Fechamento de Acesso", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.20 - Envio de e-mail de fechamento de acesso"));

        opcoesPacote.put("BI Ticket Médio", new OpcaoPerfilAcesso("TicketMedio",
                "6.21 - BI Ticket Médio", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.21 - Visualizar BI - Ticket Médio"));

        opcoesPacote.put("Geral de Clientes", new OpcaoPerfilAcesso("GeralClientes",
                "6.22 - Geral de Clientes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.22 - Relatório Geral de Clientes"));

        opcoesPacote.put("Relatório de Clientes", new OpcaoPerfilAcesso("RelatorioClientes",
                "6.23 - Relatório de Clientes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.23 - Relatório de Clientes"));

        opcoesPacote.put("Visualizar BI - Aulas experimentais", new OpcaoPerfilAcesso("BiAulaExperimental",
                "6.28 - Visualizar BI - Aulas experimentais", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.28 - Visualizar BI - Aulas experimentais"));
        
        /*opcoesPacote.put("BI Convite Aula Experimental", new OpcaoPerfilAcesso("ConviteBI",
                "6.25 - BI Convite Aula Experimental", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.25 - Permissão para visualizar BI Convite Aula Experimental"));*/

        opcoesPacote.put("Relatório de saldo de créditos de clientes", new OpcaoPerfilAcesso("SaldoCreditosClientes",
                "6.26 - Relatório de saldo de créditos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.26 - Relatório de Saldo de Créditos"));
        
        opcoesPacote.put("Aniversariantes ", new OpcaoPerfilAcesso("Aniversariantes",
                "6.29 - Visualizar relatórios de aniversariantes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.29 - Relatório de Aniversariantes"));

        opcoesPacote.put("Visualizar BI - Inadimplência", new OpcaoPerfilAcesso("BiInadimplencia",
                "6.30 - Visualizar BI - Inadimplência", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.30 - Visualizar BI - Inadimplência"));

        opcoesPacote.put("Mapa estatístico", new OpcaoPerfilAcesso("MapaEstatistico",
                "6.31 - Mapa estatístico", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.31 - Mapa estatístico"));

        opcoesPacote.put("Pacto IA - Churn Rate", new OpcaoPerfilAcesso("ChurnRate",
                "6.32 - Pacto IA - Churn Rate", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.32 - Pacto IA - Churn Rate"));

        opcoesPacote.put("Visualizar BI - Controle de Operações de Exceções", new OpcaoPerfilAcesso("BiOperacaoExececoes",
                "6.33 - Visualizar BI - Controle de Operações de Exceções", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.33 - Visualizar BI - Controle de Operações de Exceções"));

        opcoesPacote.put("Visualizar BI - Gestão de Acessos", new OpcaoPerfilAcesso("BiGestaoAcesso",
                "6.34 - Visualizar BI - Gestão de Acessos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.34 - Visualizar BI - Gestão de Acessos"));

        opcoesPacote.put("Visualizar BI - Cobranças por Convênio", new OpcaoPerfilAcesso("BiCobrancaConvenio",
                "6.35 - Visualizar BI - Cobranças por Convênio", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.35 - Visualizar BI - Cobranças por Convênio"));

        opcoesPacote.put("Visualizar BI - Verificação de Clientes", new OpcaoPerfilAcesso("BiVerificarcaoClientes",
                "6.36 - Visualizar BI - Verificação de Clientes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.36 - Visualizar BI - Verificação de Clientes"));

        opcoesPacote.put("Relatorio de Parcelas - Visualizar Dados Sensíveis Cliente", new OpcaoPerfilAcesso("RelParcelasDadosClientes",
                "6.37 - Relatorio de Parcelas - Visualizar Dados Sensíveis Cliente", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.37 - Relatorio de Parcelas - Visualizar Dados Sensíveis Cliente"));

        opcoesPacote.put("Comprovante de prestação de serviço", new OpcaoPerfilAcesso("RelAssinaturaContratos",
                "6.38 - Comprovante de prestação de serviço", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "6.38 - Comprovante de prestação de serviço"));

        opcoesPacote.put("Cadastrar Compra", new OpcaoPerfilAcesso("CadastrarCompra",
                "12.01 - Cadastrar Compra", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_ESTOQUE, "12.01 - Compras"));

        opcoesPacote.put("Cancelar Compra", new OpcaoPerfilAcesso("CancelarCompra",
                "12.02 - Cancelar Compra", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_ESTOQUE, "12.02 - Cancelar Compra"));

        opcoesPacote.put("Autorizar/Negar Compra", new OpcaoPerfilAcesso("AutorizarNegarCompra",
                "12.09 - Autorizar/Negar Compra", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_ESTOQUE, "12.09 - Autorizar/Negar Compras"));

        opcoesPacote.put("Cadastrar Balanço", new OpcaoPerfilAcesso("CadastrarBalanco",
                "12.03 - Cadastrar Balanço", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_ESTOQUE, "12.03 - Balanço"));

        opcoesPacote.put("Cancelar Balanço", new OpcaoPerfilAcesso("CancelarBalanco",
                "12.04 - Cancelar Balanço", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_ESTOQUE, "12.04 - Cancelar um balanço"));

        opcoesPacote.put("Visualizar Cardex", new OpcaoPerfilAcesso("VisualizarCardex",
                "12.05 - Visualizar Cardex", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_ESTOQUE, "12.05 - Cardex"));

        opcoesPacote.put("Adicionar Produto ao Controle de Estoque", new OpcaoPerfilAcesso("ConfigurarProdutoEstoque",
                "12.06 - Adicionar Produto ao Controle de Estoque", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_ESTOQUE, "12.06 - Configuração produto Estoque"));

        opcoesPacote.put("Alterar Situação do Produto Estoque", new OpcaoPerfilAcesso("AlterarSituacaoProdutoEstoque",
                "12.07 - Alterar Situação do Produto Estoque", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_ESTOQUE, "12.07 - Alterar a situação do produto (ativo ou cancelado) ao controle de estoque"));

        opcoesPacote.put("Visualizar Posição do Estoque", new OpcaoPerfilAcesso("VisualizarPosicaoEstoque",
                "12.08 - Visualizar Posição do Estoque", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_ESTOQUE, "12.08 - Relatório posição do estoque"));

        opcoesPacote.put("Permissão para liberar acesso", new OpcaoPerfilAcesso("LiberacaoAcesso",
                "1.07 - Permissão para liberar acesso", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.07 - Liberar acesso de clientes, bloqueados, com autenticação"));

        opcoesPacote.put("Consultar autorização de acesso em grupo empresarial", new OpcaoPerfilAcesso("ConsultarAutorizacaoAGE",
                "1.09 - Consultar autorização de acesso em grupo empresarial", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.09 - Consultar autorização de acesso em grupo empresarial"));

        opcoesPacote.put("Incluir / Alterar autorização de acesso em grupo empresarial", new OpcaoPerfilAcesso("IncluirAutorizacaoAGE",
                "1.10 - Incluir / Alterar autorização de acesso em grupo empresarial", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "1.10 - Autorização de acesso em grupo empresarial"));

        opcoesPacote.put("Lançar desconto cadastrado no produto em Contrato", new OpcaoPerfilAcesso("DescontoProdutoContrato",
                "7.51 - LLançar desconto cadastrado no produto em Contrato", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.51 - Lançar desconto cadastrado no produto em Contrato."));

        opcoesPacote.put("Lançar desconto por meio do Convênio de Desconto no Contrato", new OpcaoPerfilAcesso("DescontoProdutoContratoConvenio",
                "7.55 - Lançar desconto por meio do Convênio de Desconto no Contrato", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.55 - Lançar desconto por meio do Convênio de Desconto no Contrato."));

        opcoesPacote.put("Desconto do Plano", new OpcaoPerfilAcesso("DescontoPlano",
                "4.24 - Desconto no Plano", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.24 - Desconto manual durante negociação de contrato"));

        opcoesPacote.put("Boletos do Sistema", new OpcaoPerfilAcesso("BoletosSistema",
                "4.31 - Boletos do Sistema", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.31 - Emitir boleto para pagamento de contrato mensal para Pacto Software e Gestão"));


        opcoesPacote.put("Retorno Manual de Itens de Remessa(Itaú CNAB400)", new OpcaoPerfilAcesso("RetornoManualItensItau",
                "4.32 - Retorno Manual de Itens de Remessa(Itaú CNAB400)", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "4.32 - Retornar um item, de forma manual, que está em remessa de boleto Itaú CNAB 400"));

        opcoesPacote.put("Movimento de Pagamento - Autoriza Pagamento Cheque Data Retroativa",
                new OpcaoPerfilAcesso("MovPagamento_AutorizaPagamentoDataRetroativa",
                        "4.30 - Movimento de Pagamento - Autoriza Pagamento Cheque Data Retroativa", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.30 - Lançar ou editar um cheque com data retroativa"));

        opcoesPacote.put("Permitir alterar a data de vencimento de um boleto", new OpcaoPerfilAcesso("AlterarDataVencimentoBoleto",
                "4.34 - Alterar a data de vencimento de um boleto", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "4.34 - Alterar a data de vencimento de um boleto ao cadastrar o mesmo"));

        opcoesPacote.put("Emitir boletos para Cliente", new OpcaoPerfilAcesso("EmitirBoleto",
                "4.35 - Emitir boletos para Cliente", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "4.35 - Emitir boletos para algum cliente pelo Caixa em Aberto"));

        opcoesPacote.put("Notificacao Expiracao Sistema", new OpcaoPerfilAcesso("NotificacaoExpiracaoSistema",
                "4.36 - Notificacao Expiracao Sistema", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.36 - Visualizar notificação de expiração do sistema"));

        opcoesPacote.put("Cancelamento de Parcelas em Aberto", new OpcaoPerfilAcesso("CancelamentoParcela",
                "4.25 - Cancelamento de Parcelas em Aberto", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.25 - Cancelar parcela em aberto"));

        opcoesPacote.put("Movimento de Pagamento - Autoriza Pagamento Cheque Data Posterior a Vencimento",
                new OpcaoPerfilAcesso("MovPagamento_AutorizaPagamentoPosteriorDataVencimento",
                        "4.19 - Movimento de Pagamento - Autoriza Pagamento Cheque Data Posterior a Vencimento", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.19 - Permissão para autorizar pagamento de cheque após a data de vencimento"));

        opcoesPacote.put("Convênio de Cobrança", new OpcaoPerfilAcesso("ConvenioCobranca",
                "4.04 - Convênio de Cobrança", OpcaoPerfilAcesso.TP_ENTIDADE, "4.04 - Convênios de cobrança"));

        opcoesPacote.put("Forma de Pagamento", new OpcaoPerfilAcesso("FormaPagamento",
                "4.06 - Forma de Pagamento", OpcaoPerfilAcesso.TP_ENTIDADE, "4.06 - Formas de pagamento"));

        opcoesPacote.put("Movimento de Pagamento", new OpcaoPerfilAcesso("MovPagamento",
                "4.07 - Movimento de Pagamento", OpcaoPerfilAcesso.TP_ENTIDADE, "4.07 - Movimentos de pagamento"));

        opcoesPacote.put("Movimento da Parcela", new OpcaoPerfilAcesso("MovParcela",
                "4.08 - Movimento da Parcela", OpcaoPerfilAcesso.TP_ENTIDADE, "4.08 - Movimentos de parcela"));

        opcoesPacote.put("Tipo de Remessa", new OpcaoPerfilAcesso("TipoRemessa",
                "4.09 - Tipo de Remessa", OpcaoPerfilAcesso.TP_ENTIDADE, "4.09 - Tipos de remessa"));

        opcoesPacote.put("Tipo de Retorno", new OpcaoPerfilAcesso("TipoRetorno",
                "4.10 - Tipo de Retorno", OpcaoPerfilAcesso.TP_ENTIDADE, "4.10 - Tipos de retornos"));

        opcoesPacote.put("Venda Avulsa", new OpcaoPerfilAcesso("VendaAvulsa",
                "4.11 - Venda Avulsa", OpcaoPerfilAcesso.TP_ENTIDADE, "4.11 - Vendas avulsas"));

        opcoesPacote.put("Estorno de Recibo", new OpcaoPerfilAcesso("EstornoRecibo",
                "4.12 - Estorno de Recibo", OpcaoPerfilAcesso.TP_ENTIDADE, "4.12 - Estornar recibo"));

        opcoesPacote.put("Estorno do Contrato", new OpcaoPerfilAcesso("EstornoContrato",
                "4.13 - Estorno do Contrato", OpcaoPerfilAcesso.TP_ENTIDADE, "4.13 -  Estornar contrato"));

        opcoesPacote.put("Data no Estorno de Recibo", new OpcaoPerfilAcesso("DataEstornoRecibo",
                "4.42 - Informar data no estorno de recibo", OpcaoPerfilAcesso.TP_ENTIDADE, "4.42 - Informar data no estorno de recibo"));

//        opcoesPacote.put("Operadora de Carto", new OpcaoPerfilAcesso("OperadoraCartao",
//                "4.15 - Operadora de Carto", OpcaoPerfilAcesso.TP_ENTIDADE, "4.15 - Operadoras de carto"));

        opcoesPacote.put("Comissão Variável", new OpcaoPerfilAcesso("ComissaoVariavel",
                "4.27 - Comissão para Consultor", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.27 - Relatório de Comissão para Consultor"));

        opcoesPacote.put("Liberar vaga na turma de aluno inativo", new OpcaoPerfilAcesso("LiberarVagaNaTurma",
                "9.34 - Liberar vaga na turma de aluno inativo", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.34 - Liberar vaga de aluno inativo na turma "));
        
        opcoesPacote.put("Permite visualizar agenda de outros colaboradores", new OpcaoPerfilAcesso("PermitirVisualizarTodasAulasEstudio",
                "9.36 - Permite visualizar agenda de outros colaboradores", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.36 - Permite visualizar agenda de outros colaboradores"));

        opcoesPacote.put("Permite visualizar relatorio Wellhub por periodo", new OpcaoPerfilAcesso("PermiteVisualizaGymPassPeriodo",
                "9.37 - Permite visualizar relatório Wellhub por periodo", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.37 - Relatório de Wellhub por Período"));

        opcoesPacote.put("Relatório de convidados", new OpcaoPerfilAcesso("PermiteVisualizaConvitesPeriodo",
                "9.49 - Relatório de convidados", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.49 - Relatório de convidados"));


        opcoesPacote.put("Permite registrar acesso manual", new OpcaoPerfilAcesso("PermiteRegistrarAcessoAvulso",
                "9.38 - Permite registrar acesso manual", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.38 - Permite registrar acesso manual"));

        opcoesPacote.put("Ordenar fila de espera turma", new OpcaoPerfilAcesso("PermiteOrdenarFilaEsperaTurma",
                "10.05 - Ordenar fila de espera turma", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.05 - Ordenar fila de espera turma"));

        opcoesPacote.put("Fila de espera turma", new OpcaoPerfilAcesso("PermiteOperarFilaEsperaTurma",
                "10.11 - Fila de espera turma", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.11 - Fila de espera turma"));

        opcoesPacote.put("Imposto padrão", new OpcaoPerfilAcesso("ImpostoProduto",
                "10.12 - Imposto padrão para produtos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.12 - Imposto padrão para produtos"));

        opcoesPacote.put("Permitir visualizar o relatório de Consulta de Turma", new OpcaoPerfilAcesso("ConsultaTurma",
                "9.39 - Permitir visualizar o relatório de Consulta de Turma", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.39 - Relatório de Consulta de Turma"));
        
        opcoesPacote.put("Permitir visualizar o Mapa de Turma", new OpcaoPerfilAcesso("MapaTurma",
                "9.40 - Permitir visualizar o Mapa de Turma", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.40 - Relatório de Mapa de Turma"));
        
        opcoesPacote.put("Permitir acesso ao Gestão de Personal", new OpcaoPerfilAcesso("GestaoPersonal",
                "9.41 - Permitir acesso ao Gestão de Personal", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.41 - Permitir acesso ao Gestão de Personal"));
        
        opcoesPacote.put("Permitir visualizar o Relatório de Personal", new OpcaoPerfilAcesso("RelatorioPersonal",
                "9.42 - Permitir visualizar o Relatório de Personal", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.42 - Relatório de Personal"));
         
        opcoesPacote.put("Permitir acesso ao Gestão de Armários", new OpcaoPerfilAcesso("GestaoArmario",
                "9.43 - Permitir acesso ao Gestão de Armários", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.43 - Permitir acesso ao Gestão de Armários"));
          
        opcoesPacote.put("Permitir alterar acesso manual", new OpcaoPerfilAcesso("AlterarAcessoManual",
                "9.44 - Permitir alterar acesso manual", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.44 - Permitir alterar acesso manual"));

        opcoesPacote.put("Permitir realizar download da remessa", new OpcaoPerfilAcesso("PermiteDownloadRemessa",
                "9.46 - Permitir realizar download da remessa", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.46 - Permitir realizar download da remessa"));
        
        
        opcoesPacote.put("Sorteio", new OpcaoPerfilAcesso("Sorteio",
                "2.62 - Permitir acesso ao recurso de Sorteio", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.62 - Sorteio"));

        opcoesPacote.put("TotalClienteConsulta", new OpcaoPerfilAcesso("TotalClienteConsulta",
                "2.63 - Apresentar o total de clientes na consulta de clientes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.63 - Apresentar o total de clientes na consulta de clientes"));

        opcoesPacote.put("DesmarcarAulaForaTolerancia", new OpcaoPerfilAcesso("DesmarcarAulaForaTolerancia",
                "2.64 - Permitir desmarcar aula fora da tolerância da turma", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.64 - Permitir desmarcar aula fora da tolerância da turma"));

        opcoesPacote.put("Renovação Automática", new OpcaoPerfilAcesso("adicionarRemoverContratoRenovacaoAutomatica",
                "2.66 - Alterar renovação automática do contrato.", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.66 - Alterar renovação automática do contrato."));

        opcoesPacote.put("Data lançamento diária", new OpcaoPerfilAcesso("alterarLancamentoDiaria",
                "2.67 - Alterar data de lançamento da Diária", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.67 - Alterar data de lançamento da Diária"));

        opcoesPacote.put("Gestão de Remessas", new OpcaoPerfilAcesso("GestaoRemessas",
                "2.68 Gestão de Remessas", OpcaoPerfilAcesso.TP_ENTIDADE, "2.68 Gestão de Remessas"));
        
        opcoesPacote.put("Permite Fechar e Receber Contrato", new OpcaoPerfilAcesso("FecharReceberContrato",
                "2.69 Permite Fechar e Receber Contrato", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.69 Permite Fechar e Receber Contrato"));

        opcoesPacote.put("PermiteMarcarAlunoForaTolerancia", new OpcaoPerfilAcesso("PermiteMarcarAlunoForaTolerancia",
                "2.70 - Permite marcar e repor aula no passado", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.70 - Permite marcar e repor aula no passado"));
        
        opcoesPacote.put("PermiteAlterarConsultorBV", new OpcaoPerfilAcesso("PermiteAlterarConsultorBV",
                "2.71 - Permite alterar consultor BV", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.71 - Permite alterar consultor BV"));

        opcoesPacote.put("PermiteAlterarProfessorConsultaTurma", new OpcaoPerfilAcesso("PermiteAlterarProfessorConsultaTurma",
                "2.72 - Permite alterar professor através da tela de turma", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.72 - Permite alterar professor através da tela de turma"));
        opcoesPacote.put("Venda Rápida", new OpcaoPerfilAcesso("VendaRapida",
                "4.40 - Venda Rápida", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.40 - Venda Rápida"));

        opcoesPacote.put("Incluir/Alterar personal através da venda rápida", new OpcaoPerfilAcesso("personalVendaRapida",
                "4.46 - Incluir/Alterar personal através da venda rápida", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.46 - Incluir/Alterar personal através da venda rápida"));

        opcoesPacote.put("Permite alterar data primeira parcela negociacao", new OpcaoPerfilAcesso("dataPrimeiraParcela",
                "4.47 - Alterar data da primeira parcela na negociação", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.47 - Alterar data da primeira parcela na negociação"));

        opcoesPacote.put("Mudar plano de um contrato ativo", new OpcaoPerfilAcesso("MudarPlanoContratoAtivo",
                "9.48 - Realizar mudança de plano de um contrato ativo (upgrade/downgrade). Cancelar o contrato atual e gerar um contrato com novo plano.",
                OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "9.48 - Realizar mudança de plano de um contrato ativo (upgrade/downgrade). Cancelar o contrato atual e gerar um contrato com novo plano."));

        opcoesPacote.put("Consultar alunos de todas as empresas", new OpcaoPerfilAcesso("ConsultarAlunosCaixaAbertoTodasEmpresas",
                "9.50 - Consultar alunos de todas as empresas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.50 - Consultar alunos de todas as empresas"));

        opcoesPacote.put("Consultar parcelas de todas as empresas", new OpcaoPerfilAcesso("ConsultarParcelasTodasEmpresas",
                "10.07 - Consultar parcelas de todas as empresas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.07 - Consultar parcelas de todas as empresas"));

        opcoesPacote.put("Consultar relatório de frequência de turmas", new OpcaoPerfilAcesso("ConsultarRelatorioFrequenciaTurmas",
                "10.08 - Consultar relatório de frequência de turmas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.08 - Consultar relatório de frequencia de turmas"));

        opcoesPacote.put("Permitir cadastrar solicitacao de compras", new OpcaoPerfilAcesso("PermitirCadastrarSolicitacaoCompras",
                "10.09 - Permitir cadastrar solicitacao de compras", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.09 - Permitir cadastrar solicitacao de compras"));

        opcoesPacote.put("Permitir aprovar ou negar solicitacao de compras", new OpcaoPerfilAcesso("PermitirAprovarNegarSolicitacaoCompras",
                "10.10 - Permitir aprovar ou negar solicitacao de compras", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.10 - Permitir aprovar ou negar solicitacao de compras"));

        opcoesPacote.put("Mudar data de vencimento das parcelas de contratos recorrentes", new OpcaoPerfilAcesso("MudarDataVencimentoParcelas",
                "9.51 - Realizar alteração na data de vencimento de contratos recorrentes vigentes",
                OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "9.51 - Realizar alteração na data de vencimento de contratos recorrentes vigentes"));

        opcoesPacote.put("Permitir ao usuário consultar turmas até as que não for responsável", new OpcaoPerfilAcesso("PermitirConsultarTurmasUsuarioNaoForResponsavel",
                "9.53 - Permitir ao usuário consultar turmas até as que não for responsável", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.53 - Permitir ao usuário consultar turmas até as que não for responsável"));

        opcoesPacote.put("Permitir acesso ao Gestão de Turma", new OpcaoPerfilAcesso("GestaoTurma",
                "9.54 - Permitir acesso ao Gestão de Turma", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.54 - Permitir acesso ao Gestão de Turma"));

        opcoesPacote.put("Permite alterar CPF Dotz", new OpcaoPerfilAcesso("PermiteAlterarCPFDotz",
                "9.55 - Permitir alterar o CPF Dotz para acúmulo e resgate", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.55 - Permitir alterar o CPF Dotz para acúmulo e resgate"));

        opcoesPacote.put("Permite editar valor das parcelas na negociação de contrato", new OpcaoPerfilAcesso("PermiteEditarValorParcelaNegociacao",
                "9.56 - Permite editar  valor das parcelas na negociação de contrato", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.56 - Permite editar  valor das parcelas na negociação de contrato"));

        opcoesPacote.put("Relatório de Pesquisas", new OpcaoPerfilAcesso("Pesquisa",
                "9.57 - Relatório de Pesquisas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.57 - Relatório de Pesquisas"));

        opcoesPacote.put("Alterar cobrança de pró-rata na alteração de vencimento de contratos recorrentes", new OpcaoPerfilAcesso("AlterarCobrancaProRataDiaRecorrente",
                "9.58 - Alterar cobrança de pró-rata na alteração de vencimento de contratos recorrentes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.58 - Alterar cobrança de pró-rata na alteração de vencimento de contratos recorrentes"));

        opcoesPacote.put("Lançamento de Carência Retroativa para o contrato do cliente", new OpcaoPerfilAcesso("LancamentoCarenciaRetroativa",
                "9.59 - Lançamento de Carência Retroativa para o contrato do cliente", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.59 - Lançamento de Carência Retroativa para o contrato do cliente"));

        opcoesPacote.put("ConfigurarClubeDeVantagens", new OpcaoPerfilAcesso("ConfigurarClubeDeVantagens",
                "9.60 - Configurar Clube de Vantagens", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,"9.60 - Configurar Clube de Vantagens"));

        opcoesPacote.put("AlterarConvenioCobranca", new OpcaoPerfilAcesso("AlterarConvenioCobranca",
                "9.61 - Alterar convênio de cobrança", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,"9.61 - Alterar convênio de cobrança"));

        opcoesPacote.put("ReciboTransacaoErro99Cielo", new OpcaoPerfilAcesso("ReciboTransacaoErro99Cielo",
                "9.62 - Criação de recibo remessa erro 99 (CIELO)", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,"9.62 - Criação de recibo remessa erro 99 (CIELO)"));

        opcoesPacote.put("ExcluirTokemGympass", new OpcaoPerfilAcesso("ExcluirTokemGympass",
                "9.63 - Excluir Token Wellhub", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,"9.63 - Excluir Token Wellhub"));

        opcoesPacote.put("ExcluirTokemGoGood", new OpcaoPerfilAcesso("ExcluirTokemGoGood",
                "10.06 - Excluir Token GoGood", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,"10.06 - Excluir Token GoGood"));

        opcoesPacote.put("PermiteBloquearDesbloquearClienteCobrancaAutomatica", new OpcaoPerfilAcesso("PermiteBloquearDesbloquearClienteCobrancaAutomatica",
                "9.64 - Permite bloquear ou desbloquear cobrança automática do cliente", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,"9.64 - Permite bloquear ou desbloquear cobrança automática do cliente"));

        opcoesPacote.put("PermiteRelatorioClientesCobrancaBloqueada", new OpcaoPerfilAcesso("PermiteRelatorioClientesCobrancaBloqueada",
                "9.65 - Permitir visualizar o Relatório de clientes com cobrança automática bloqueada", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,"9.65 - Permitir visualizar o Relatório de clientes com cobrança automática bloqueada"));

        opcoesPacote.put("ExportarRelatorioCliente", new OpcaoPerfilAcesso("ExportarRelatorioCliente",
                "9.66 - Exportar relatório de clientes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.66 - Exportar relatório de clientes"));

        opcoesPacote.put("PermiteAlterarLancamentoContratosIguais", new OpcaoPerfilAcesso("PermiteAlterarLancamentoContratosIguais",
                "2.73 - Permite visualizar a configuração 'Permitir o lançamento de contratos com planos iguais desconsiderando os 30 minutos'", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.73 - Permite visualizar a configuração 'Permitir o lançamento de contratos com planos iguais desconsiderando os 30 minutos'"));

        opcoesPacote.put("PermiteConferirNegociacaoSemInformarCartao", new OpcaoPerfilAcesso("PermiteConferirNegociacaoSemInformarCartao",
                "2.74 - Permite passar para tela de conferir negociação sem informar dados do cartão", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.74 - Permite passar para tela de conferir negociação sem informar dados do cartão"));

        opcoesPacote.put("Permite Alterar RPS", new OpcaoPerfilAcesso("PermiteAlterarRPS",
                "7.57 - Permite alterar o RPS na Config. de Nota Fiscal", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.57 - Permite alterar o RPS na Config. de Nota Fiscal"));

        opcoesPacote.put("Operações Coletivas", new OpcaoPerfilAcesso("OperacoesColetivas",
                "2.75 - Permitir acesso ao recurso de Operações Coletivas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.75 - Operações Coletivas"));

        opcoesPacote.put("Alterar IdVindi", new OpcaoPerfilAcesso("AlterarIdVindi",
                "2.76 - Permite alterar IdVindi nos dados do cliente", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.76 - Permite alterar IdVindi nos dados do cliente"));

        opcoesPacote.put("Remover mensagem da catraca", new OpcaoPerfilAcesso("RemoverMensagemCatraca",
                "2.77 - Excluir mensagem da catraca", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "2.77 - Excluir mensagem da catraca"));

        opcoesPacote.put("Apresentar Link para Cadastrar Cartão Online", new OpcaoPerfilAcesso("ApresentarLinkCadastrarCartaoOnline",
                "3.39 - Apresentar Link para Cadastrar Cartão Online", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "3.39 - Apresentar Link para Cadastrar Cartão Online"));

        opcoesPacote.put("Tela (Venda Rápida) como tela padrão para lançamento de contrato", new OpcaoPerfilAcesso("VendaRapidaTelaPadraoLancarContrato",
                "3.40 - Tela (Venda Rápida) como tela padrão para lançamento de contrato", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.40 - Tela (Venda Rápida) como tela padrão para lançamento de contrato"));

        opcoesPacote.put("Transferir direitos de uso do contrato", new OpcaoPerfilAcesso("TransferirDireitosDeUso",
                "3.41 - Transferir direitos de uso do contrato", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "3.41 - Transferir direitos de uso do contrato"));

        opcoesPacote.put("Permitir gerar Link de Pagamento Escolher Parcelas", new OpcaoPerfilAcesso("PermitirLinkPagamentoParceladoStone",
                "3.42 - Permitir parcelamento pela operadora ao gerar link de pagamento", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "3.42 - Permitir parcelamento pela operadora ao gerar link de pagamento"));


        if (JSFUtilities.isJSFContext()) {
            try {
                LoginControle loginControle = (LoginControle) JSFUtilities.getManagedBean("LoginControle");
                if (loginControle != null && loginControle.isPossuiModuloPAY()) {
                    opcoesPacote.put("ModuloPactoPay", new OpcaoPerfilAcesso("ModuloPactoPay",
                            "2.78 - Módulo PactoPay", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.78 - Módulo PactoPay"));
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        opcoesPacote.put("Relatório de Parcelas Consolidado", new OpcaoPerfilAcesso("RelatorioParcelasConsolidado",
                "2.79 - Relatório de Parcelas Consolidado", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "2.79 - Relatório de Parcelas Consolidado"));

        opcoesPacote.put("PermiteFinalizarVendaRapidaSemAutorizacaoCobranca", new OpcaoPerfilAcesso("PermiteFinalizarVendaRapidaSemAutorizacaoCobranca",
                "2.80 - Permitir finalizar venda rápida sem autorização de cobrança", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.80 - Permitir finalizar venda rápida sem autorização de cobrança"));

        opcoesPacote.put("Desvincular Usuário da Academia", new OpcaoPerfilAcesso("DesvincularUsuarioAcademia",
                "9.67 - Desvincular Usuário da Academia", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "9.67 - Desvincular Usuário da Academia"));

        opcoesPacote.put("Visualizar Gestão de Negativações", new OpcaoPerfilAcesso("GestaoNegativacoes",
                "2.81 - Visualizar Gestão de Negativações", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.81 - Visualizar Gestão de Negativações"));

        opcoesPacote.put("Negativar/Liberar parcelas na Gestão de Negativações", new OpcaoPerfilAcesso("NegativarLiberarParcelasGestaoNegativacoes",
                "2.82 - Negativar/Liberar parcelas na Gestão de Negativações", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.82 - Negativar/Liberar parcelas na Gestão de Negativações"));
        opcoesPacote.put("Renovação Automática Produto", new OpcaoPerfilAcesso("adicionarRemoverProdutoRenovacaoAutomatica",
                "2.83 - Alterar renovação automática do Produto.", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.83 - Alterar renovação automática do produto."));

        opcoesPacote.put("Lançar afastamento para contratos dependentes", new OpcaoPerfilAcesso("LancarAfastamentoContratoDependente",
                "2.84 - Lançar afastamento para clientes que são dependentes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "2.84 - Lançar afastamento para clientes que são dependentes"));

        opcoesPacote.put("Permitir transferir clientes com contrato ativo", new OpcaoPerfilAcesso("PermissaoTransferirClienteEmpresaContratoAtivo",
                "2.85 - Permissão para transferir clientes com contrato ativo", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.85 - Transferir clientes com contrato ativo"));

        opcoesPacote.put("Permitir criar/editar e excluir avisos internos", new OpcaoPerfilAcesso("PermitirAvisosInternos",
                "2.86 - Permitir criar/editar e excluir avisos internos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "2.86 - Permitir criar/editar e excluir avisos internos"));

//        opcoesPacote.put("App do Gestor", new OpcaoPerfilAcesso("AppDoGestor",
//                "9.68 - App do Gestor", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
//                "9.68 - App do Gestor"));

        opcoesPacote.put("Permitir excluir conta a Pagar ou Receber", new OpcaoPerfilAcesso("ExcluirCliente",
                "9.69 - Permitir excluir cliente da base de dados", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.69 - Permitir excluir cliente da base de dados"));

        opcoesPacote.put("Permite remover assinatura digital de contratos", new OpcaoPerfilAcesso("RemoverAssinatura",
                "9.70 - Permite remover assinatura digital de contratos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.70 - Permite remover assinatura digital de contratos"));

        opcoesPacote.put("RelatorioFrequenciaOcupacaoTurmas", new OpcaoPerfilAcesso("FrequenciaOcupacaoTurmasRel",
                "9.73 - Relatório Frequência e Ocupação de Turmas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.73 - Relatório de Frequência e Ocupação de Turmas"));

        opcoesPacote.put("Relatório de BVs", new OpcaoPerfilAcesso("RelatorioBVs",
                "9.74 - Relatório de BVs", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.74 - Relatório de BVs"));

        opcoesPacote.put("Relatório de Clientes com Atestado", new OpcaoPerfilAcesso("RelatorioClientesComAtestado",
                "9.75 - Relatório de Clientes com Atestado", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.75 - Relatório de Clientes com Atestado"));

        opcoesPacote.put("Relatório de Clientes com Bônus", new OpcaoPerfilAcesso("RelatorioClientesComBonus",
                "9.76 - Relatório de Clientes com Bônus", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.76 - Relatório de Clientes com Bônus"));

        opcoesPacote.put("Relatório de Clientes Trancados", new OpcaoPerfilAcesso("RelatorioClientesTrancados",
                "9.77 - Relatório de Clientes Trancados", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.77 - Relatório de Clientes Trancados"));

        opcoesPacote.put("Relatório de Desconto por Ocupação na Turma", new OpcaoPerfilAcesso("RelatorioDescontoPorOcupacao",
                "9.78 - Relatório de Desconto por Ocupação na Turma", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.78 - Relatório de Desconto por Ocupação na Turma"));


        opcoesPacote.put("Relatório de Fechamento de Acessos", new OpcaoPerfilAcesso("RelatorioFechamentoDeAcessos",
                "9.79 - Relatório de Fechamento de Acessos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.79 - Relatório de Fechamento de Acessos"));

        opcoesPacote.put("Relatório de Indicador de Acessos", new OpcaoPerfilAcesso("RelatorioIndicadorDeAcessos",
                "9.80 - Relatório de Indicador de Acessos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.80 - Relatório de Indicador de Acessos"));

        opcoesPacote.put("Relatório de Repasse", new OpcaoPerfilAcesso("RelatorioDeRepasse",
                "9.81 - Relatório de Repasse", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.81 - Relatório de Repasse"));

        opcoesPacote.put("Relatório de Visitantes", new OpcaoPerfilAcesso("RelatorioDeVisitantes",
                "9.82 - Relatório de Visitantes", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.82 - Relatório de Visitantes"));

        opcoesPacote.put("Relatório de Pedidos Pinpad", new OpcaoPerfilAcesso("RelatorioDePedidosPinpad",
                "9.83 - Relatório de Pedidos Pinpad", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.83 - Relatório de Pedidos Pinpad"));

        opcoesPacote.put("Relatório de Totalizador de Tickets", new OpcaoPerfilAcesso("RelatorioDeTotalizadorDeTickets",
                "9.84 - Relatório de Totalizador de Tickets", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.84 - Relatório de Totalizador de Tickets"));

        opcoesPacote.put("Relatório de Transações Pix", new OpcaoPerfilAcesso("RelatorioDeTransacoesPix",
                "9.85 - Relatório de Transações Pix", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.85 - Relatório de Transações Pix"));

        opcoesPacote.put("Visualizar BI - Wellhub", new OpcaoPerfilAcesso("BiGymPass",
                "9.86 - Visualizar BI - Wellhub", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.86 - Visualizar BI - Wellhub"));

        opcoesPacote.put("Visualizar BI - Ciclo de Vida do seu Cliente", new OpcaoPerfilAcesso("BiCicloDeVida",
                "9.87 - Visualizar BI - Ciclo de Vida do seu Cliente", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.87 - Visualizar BI - Ciclo de Vida do seu Cliente"));

        opcoesPacote.put("Relatório de Cliente", new OpcaoPerfilAcesso("RelatorioDeCliente",
                    "9.88 - Relatório de Cliente", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.88 - Relatório de Cliente"));

        opcoesPacote.put("Relatório de Consulta de Recibos", new OpcaoPerfilAcesso("RelatorioDeConsultaRecibo",
                    "9.89 - Relatório de Consulta de Recibos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.89 - Relatório de Consulta de Recibos"));

        opcoesPacote.put("Relatório de Armários", new OpcaoPerfilAcesso("RelatorioDeArmarios",
                    "9.90 - Relatório de Armários", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.90 - Relatório de Armários"));

        opcoesPacote.put("Relatório de Previsão de Renovação", new OpcaoPerfilAcesso("RelatorioDePrevisaoRenovacao",
                    "9.91 - Relatório de Previsão de Renovação", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.91 - Relatório de Previsão de Renovação"));

        opcoesPacote.put("Relatório de Faturamento Recebido Por Período", new OpcaoPerfilAcesso("RelatorioDeFaturamentoRecebidoPeríodo",
                    "9.92 - Relatório de Faturamento Recebido Por Período", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.92 - Relatório de Faturamento Recebido Por Período"));

        opcoesPacote.put("Relatório de Movimentação de Conta Corrente do Cliente", new OpcaoPerfilAcesso("RelatorioDeMovimentacaoContaCorrenteCliente",
                    "9.93 - Relatório de Movimentação de Conta Corrente do Cliente", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.93 - Relatório de Movimentação de Conta Corrente do Cliente"));

        opcoesPacote.put("Relatório de Produtos com Vigência", new OpcaoPerfilAcesso("RelatorioDeProdutosComVigencia",
                    "9.94 - Relatório de Produtos com Vigência", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.94 - Relatório de Produtos com Vigência"));

        opcoesPacote.put("Relatório de Clientes Cancelados", new OpcaoPerfilAcesso("RelatorioDeClientesCancelados",
                    "9.95 - Relatório de Clientes Cancelados", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.95 - Relatório de Clientes Cancelados"));

        opcoesPacote.put("Relatório de Totalizador de Acessos", new OpcaoPerfilAcesso("RelatorioDeTotalizadorDeAcessos",
                    "9.96 - Relatório de Totalizador de Acessos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.96 - Relatório de Totalizador de Acessos"));

        opcoesPacote.put("Permitir Imprimir Recibo em Branco", new OpcaoPerfilAcesso("PermitirImprimirReciboEmBranco",
                    "9.97 - Permitir Imprimir Recibo em Branco", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.97 - Permitir Imprimir Recibo em Branco"));

        opcoesPacote.put("Permitir Exportar Dados", new OpcaoPerfilAcesso("PermitirExportarDados",
                "9.98 - Permitir Exportar Dados", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.98 - Permitir Exportar Dados"));

        opcoesPacote.put("Pacto App", new OpcaoPerfilAcesso("PactoApp",
                "9.99 - Pacto App", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "9.99 - Pacto App"));

        opcoesPacote.put("IncluirOuRetirarClienteRestricao", new OpcaoPerfilAcesso("IncluirOuRetirarClienteRestricao",
                "9.100 - Incluir/Retirar cliente da lista de clientes com restrições", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.100- Incluir/Retirar cliente da lista de clientes com restrições"));

        opcoesPacote.put("Permitir Excluir Senha de Acesso da Catraca", new OpcaoPerfilAcesso("PermitirExcluirSenhaAcessoCatraca",
                "9.101 - Permitir Excluir Senha de Acesso da Catraca", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.101 - Permitir Excluir Senha de Acesso da Catraca"));

        opcoesPacote.put("Permitir acesso ao Gestão de Boletos Online", new OpcaoPerfilAcesso("GestaoBoletosOnline",
                "4.48 - Permitir acesso ao Gestão de Boletos Online", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,"4.48 - Permitir acesso ao Gestão de Boletos Online"));

        opcoesPacote.put("Permite visualizar relatório SMD por período", new OpcaoPerfilAcesso("PermiteVisualizaSMDPeriodo",
                "10.11 - Permite visualizar relatório SMD por período", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.11 - Relatório de SMD por Período"));

        return opcoesPacote;
    }


    public static Hashtable inicializarModuloCRM(){

        Hashtable<String, OpcaoPerfilAcesso> opcoesPacote = new Hashtable<>();

//        opcoesPacote.put("Módulo CRM", new OpcaoPerfilAcesso("ModuloCRM",
//                "7.01 - Módulo CRM", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.01 - Visualizar todo módulo do CRM"));

//        opcoesPacote.put("Definir Layout", new OpcaoPerfilAcesso("DefinirLayout",
//                "7.02 - Definir Layout", OpcaoPerfilAcesso.TP_ENTIDADE, "7.02 - Permissão para cadastrar, editar, consultar e/ou excluir link para Definir Layout e também para habilitar os BI's das metas do CRMWEB"));

        opcoesPacote.put("Índice de Renovação CRM", new OpcaoPerfilAcesso("IndiceRenovacaoCRM",
                "7.03 - Índice de Renovação CRM", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.03 - Visualizar Índice de Renovação CRM"));

        opcoesPacote.put("Índice de Conversão CRM", new OpcaoPerfilAcesso("IndiceConversaoCRM",
                "7.04 - Índice de Conversão CRM", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.04 -  Relatório de Índice de Conversão CRM"));

        opcoesPacote.put("Rotatividade CRM", new OpcaoPerfilAcesso("RotatividadeCRM",
                "7.05 - Rotatividade CRM", OpcaoPerfilAcesso.TP_ENTIDADE, "7.05 - Metas de renovação"));

        opcoesPacote.put("Configuração Sistema CRM", new OpcaoPerfilAcesso("ConfiguracaoSistemaCRM",
                "7.06 - Configuração Sistema CRM", OpcaoPerfilAcesso.TP_ENTIDADE, "7.06 - Configuração de sistema do CRM"));

        opcoesPacote.put("Pendência Cliente CRM", new OpcaoPerfilAcesso("PendenciaClienteCRM",
                "7.07 - Pendência Cliente CRM", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.07 - Visualizar relatórios de Pendência de Cliente do CRM"));

        opcoesPacote.put("Grupo Colaborador", new OpcaoPerfilAcesso("GrupoColaborador",
                "7.08 - Grupo Colaborador", OpcaoPerfilAcesso.TP_ENTIDADE, "7.08 -Grupos de Colaboradores"));

        opcoesPacote.put("Modelo Mensagem", new OpcaoPerfilAcesso("ModeloMensagem",
                "7.09 - Modelo Mensagem", OpcaoPerfilAcesso.TP_ENTIDADE, "7.09 - Modelo de Mensagem"));

        opcoesPacote.put("Mala Direta", new OpcaoPerfilAcesso("MalaDireta",
                "7.10 - Mailing", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.10 - Enviar emails do tipo Mala Direta"));

        opcoesPacote.put("Organizar Carteira", new OpcaoPerfilAcesso("OrganizadorCarteira",
                "7.11 - Organizador de Carteira", OpcaoPerfilAcesso.TP_ENTIDADE, "7.11 - Visualizar o relatório de Organizador de Carteira"));

        opcoesPacote.put("Cliente Potencial", new OpcaoPerfilAcesso("Passivo",
                "7.12 - Cliente Potencial", OpcaoPerfilAcesso.TP_ENTIDADE, "7.12 - Receptivos"));

        opcoesPacote.put("Indicação", new OpcaoPerfilAcesso("Indicacao",
                "7.13 - Indicação", OpcaoPerfilAcesso.TP_ENTIDADE, "7.13 - Indicados"));

        opcoesPacote.put("Feriado", new OpcaoPerfilAcesso("Feriado",
                "7.14 - Feriado", OpcaoPerfilAcesso.TP_ENTIDADE, "7.14 - Feriados"));

        opcoesPacote.put("Agenda", new OpcaoPerfilAcesso("Agenda",
                "7.15 - Agenda", OpcaoPerfilAcesso.TP_ENTIDADE, "7.15 - Agendas"));

        opcoesPacote.put("Abertura de Meta", new OpcaoPerfilAcesso("AberturaMeta",
                "7.16 - Abertura de Meta", OpcaoPerfilAcesso.TP_ENTIDADE, "7.16 - Abrir meta no CRM"));

        opcoesPacote.put("Evento", new OpcaoPerfilAcesso("Evento",
                "7.17 - Evento", OpcaoPerfilAcesso.TP_ENTIDADE, "7.17 - Eventos"));

        opcoesPacote.put("Indicador de Vendas", new OpcaoPerfilAcesso("IndicadorVenda",
                "7.18 - Indicador de Vendas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.18 - Visualizar Indicador de Vendas"));

        opcoesPacote.put("Indicador de Retenção", new OpcaoPerfilAcesso("IndicadorRetencao",
                "7.19 - Indicador de Retenção", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.19 - Visualizar Indicador de Retenção"));

        opcoesPacote.put("Objeção", new OpcaoPerfilAcesso("Objecao",
                "7.20 - Objeção", OpcaoPerfilAcesso.TP_ENTIDADE, "7.20 - Cadastrar objeções"));

        opcoesPacote.put("Visualizar Meta", new OpcaoPerfilAcesso("VisualizarMeta",
                "7.21 - Visualizar Meta", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.21 - Visualizar metas"));

        opcoesPacote.put("Histórico Contato", new OpcaoPerfilAcesso("HistoricoContato",
                "7.22 - Histórico Contato", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.22 - Consultar Histórico de Contatos"));

        opcoesPacote.put("Realizar Contato", new OpcaoPerfilAcesso("RealizarContato",
                "7.23 - Realizar Contato", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.23 - Realizar Contato"));

        opcoesPacote.put("Agendamento", new OpcaoPerfilAcesso("MetaAgendamento",
                "7.24 - Meta Agendamento", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.24 - Visualizar metas em agendamento"));

        opcoesPacote.put("24h", new OpcaoPerfilAcesso("MetaVinteQuatroHoras",
                "7.25 - Meta Vinte Quatro Horas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.25 - Visualizar metas 24 horas"));

        opcoesPacote.put("Renovação", new OpcaoPerfilAcesso("MetaRenovacao",
                "7.26 - Meta Renovação", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.26 - Visualizar metas de renovação"));

        opcoesPacote.put("Pós Venda", new OpcaoPerfilAcesso("MetaPosVenda",
                "7.27 - Meta Pós Venda", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.27 - Visualizar metas pós vendas"));

        opcoesPacote.put("Indicados", new OpcaoPerfilAcesso("MetaIndicado",
                "7.30 - Meta Indicados", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.30 - Metas de indicados"));

        opcoesPacote.put("Passivos", new OpcaoPerfilAcesso("MetaPassivo",
                "7.31 - Meta Passivos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.31 - Metas de passivos"));

        opcoesPacote.put("Grupo Risco", new OpcaoPerfilAcesso("MetaGrupoRisco",
                "7.32 - Meta Grupo Risco", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.32 - Visualizar metas de grupo de risco"));

        opcoesPacote.put("Perda", new OpcaoPerfilAcesso("MetaPerda",
                "7.33 - Meta Perda", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.33 - Visualizar metas de desistentes"));

        opcoesPacote.put("Aniversariante", new OpcaoPerfilAcesso("MetaAniversariante",
                "7.34 - Meta Aniversariante", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.34 - Metas de aniversariantes"));

        opcoesPacote.put("Faltosos", new OpcaoPerfilAcesso("MetaFaltosos",
                "7.35 - Meta Faltosos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.35 - Metas de faltosos"));

        opcoesPacote.put("Vencidos", new OpcaoPerfilAcesso("MetaVencidos",
                "7.36 - Meta Vencidos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.36 - Visualizar metas de vencidos"));

        opcoesPacote.put("Conversão de Agendados", new OpcaoPerfilAcesso("MetaConversaoAgendados",
                "7.37 - Meta Conversão de Agendados", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.37 - Visualizar metas de Conversão de Agendados"));

        opcoesPacote.put("Ligação Agendados de Amanhã", new OpcaoPerfilAcesso("MetaLigacaoAgendamentoAmanha",
                "7.38 - Meta Agendados de Amanhã", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.38 - Visualizar metas de Agendados de Amanhã"));

        //Funcionalidades
        opcoesPacote.put("Liberar Trocar de Colabordores Abertura Dia", new OpcaoPerfilAcesso("LiberarTrocarColabordorAberturaDia",
                "7.39 - Liberar Trocar de Colaboradores Abertura Dia", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.39 - Colaborador, logado em seu usuário, abrir meta para outro colaborador"));

        opcoesPacote.put("Permissão para Visualizar Todas Carteiras", new OpcaoPerfilAcesso("PermitirVisualizarTodasCarteiras",
                "7.40 - Permissão para Visualizar Todas Carteiras", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.40 - Coordenador da academia visualizar todas as carteiras independente se ele estiver no grupo ou não"));

        opcoesPacote.put("Ex-Alunos", new OpcaoPerfilAcesso("MetaExAlunos",
                "7.41 - Meta Ex-Alunos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.41 - Visualizar metas de Ex-Alunos"));

        opcoesPacote.put("Conversão de Ex-Alunos", new OpcaoPerfilAcesso("MetaConversaoExAlunos",
                "7.42 - Meta Conversão de Ex-Alunos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.42 - Meta Conversão de Ex-Alunos"));

        opcoesPacote.put("Visitantes Antigos", new OpcaoPerfilAcesso("MetaVisitantesAntigos",
                "7.43 - Meta Visitantes Antigos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.43 - Visualizar meta de Visitantes Antigos"));

        opcoesPacote.put("Meta Extra", new OpcaoPerfilAcesso("CrmExtraCRM",
                "7.46 - Meta Extra", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.46 - Meta Extra"));

        opcoesPacote.put("Totalizador de Metas", new OpcaoPerfilAcesso("TotalizadorMeta",
                "7.47 - Totalizador de Metas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.47 - Visualizar Totalizador de Metas"));

        opcoesPacote.put("Pesquisar meta por período", new OpcaoPerfilAcesso("PesquisarPeriodoCRM",
                "7.48 - Pesquisar meta por período", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.48 - Pesquisar meta por período"));

        opcoesPacote.put("Pesquisar meta passada", new OpcaoPerfilAcesso("PesquisarMetaPassadaCRM",
                "7.49 - Pesquisar meta passada", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.49 - Pesquisar meta passada"));

        opcoesPacote.put("Visualizar Business Intelligence CRM", new OpcaoPerfilAcesso("BusinessIntelligenceCRM",
                "7.50 - Visualizar Business Intelligence CRM", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.50 - Visualizar o Business Intelligence do CRM"));

        opcoesPacote.put("Script", new OpcaoPerfilAcesso("Script",
                "7.52 - Script", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.52 - Script"));

        opcoesPacote.put("Relatório de Agendamentos", new OpcaoPerfilAcesso("RelatorioAgendamentos",
                "7.53 - Relatório de Agendamentos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.53 - Relatório de Agendamentos"));

        opcoesPacote.put("Relatório de Contatos via APP", new OpcaoPerfilAcesso("RelatorioContatosAPP",
                "7.54 - Relatório de Contatos via APP", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.54 - Relatório de Contatos via APP"));

        opcoesPacote.put("Realizar agendamentos de contato para outros colaboradores", new OpcaoPerfilAcesso("SelecionarColaboradorMetas",
                "7.56 - Realizar agendamentos de contato para outros colaboradores", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.56 - Realizar agendamentos de contato para outros colaboradores"));

        opcoesPacote.put("Quarentena", new OpcaoPerfilAcesso("CrmQuarentena",
                "7.58 - Quarentena", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.58 - Quarentena"));

        opcoesPacote.put("Editar Simples Registro", new OpcaoPerfilAcesso("CrmEditarSimplesRegistro",
                "7.59 - Editar Simples Registro", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "7.59 - Editar Simples Registro"));
        return opcoesPacote;
    }


    private static Hashtable inicializarEstudio() {
        Hashtable<String, OpcaoPerfilAcesso> opcoesPacote = new Hashtable<String, OpcaoPerfilAcesso>();

        //funcionalidades do Estúdio com permissão
        opcoesPacote.put("Cancelar Sessões", new OpcaoPerfilAcesso("CancelarSessao",
                "11.01 - Cancelar Sessões", OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO, "11.01 - Cancelar as sessões dos alunos"));

        opcoesPacote.put("AgendaEstudio", new OpcaoPerfilAcesso("AgendaEstudio",
                "11.02 - Agenda Estúdio", OpcaoPerfilAcesso.TP_ENTIDADE, "11.02 - Agenda do Estúdio"));

        opcoesPacote.put("AgendarSessaoForaVigencia", new OpcaoPerfilAcesso("AgendarSessaoForaVigencia",
                "11.03 - Agendar Sessão fora Vigencia", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "11.03 - Agendar Produto(Sessão) fora vigência."));

        opcoesPacote.put("AgendarPacoteForaPeriodoAgendamento", new OpcaoPerfilAcesso("AgendarPacoteForaPeriodoAgendamento",
                "11.04 - Agendar Pacote fora do Período de Agendamento.", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "11.04 - Agendar Pacote(Sessão) fora do período de agendamento."));

        opcoesPacote.put("AgendaMensal", new OpcaoPerfilAcesso("AgendaMensal",
                "11.05 - Agenda Mensal", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "11.05 - Agendar Mensal"));

        opcoesPacote.put("AgendaAmbiente", new OpcaoPerfilAcesso("AgendaAmbiente",
                "11.06 - Agenda Ambiente", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "11.06 - Agendar Ambiente"));

        opcoesPacote.put("AgendaProfissional", new OpcaoPerfilAcesso("AgendaProfissional",
                "11.07 - Agenda Profissional", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "11.07 - Agendar Profissional"));

        opcoesPacote.put("AgendaIndividual", new OpcaoPerfilAcesso("AgendaIndividual",
                "11.08 - Agenda Individual", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "11.08 - Agendar Individual"));

        return opcoesPacote;
    }

    private static Hashtable inicializarCE() {
        Hashtable<String, OpcaoPerfilAcesso> opcoesPacote = new Hashtable<String, OpcaoPerfilAcesso>();

        opcoesPacote.put("Módulo CE", new OpcaoPerfilAcesso("ModuloCE",
                "8.01 - Módulo CE", OpcaoPerfilAcesso.TP_ENTIDADE, "8.01 - Visualizar o módulo do Central de Eventos"));

        return opcoesPacote;
    }

    private static Hashtable inicializarNOTAS() {
        Hashtable<String, OpcaoPerfilAcesso> opcoesPacote = new Hashtable<String, OpcaoPerfilAcesso>();

        opcoesPacote.put("ModuloNotaFiscal", new OpcaoPerfilAcesso("ModuloNotaFiscal",
                "10.00 - Módulo Nota Fiscal", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.00 - Módulo Nota Fiscal"));

        opcoesPacote.put("PermiteCancelarNotaFiscal", new OpcaoPerfilAcesso("PermiteCancelarNotaFiscal",
                "10.01 - Permitir cancelar nota fiscal", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.01 - Permitir cancelar nota fiscal"));

        opcoesPacote.put("PermiteInutilizarNotaFiscal", new OpcaoPerfilAcesso("PermiteInutilizarNotaFiscal",
                "10.02 - Permitir inutilizar nota fiscal", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.02 - Permitir inutilizar nota fiscal"));

        opcoesPacote.put("PermiteExcluirNotaFiscal", new OpcaoPerfilAcesso("PermiteExcluirNotaFiscal",
                "10.03 - Permitir excluir nota no Gestão de Notas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.03 - Permitir excluir nota no Gestão de Notas"));

        opcoesPacote.put("PermiteAlterarStatusNotaFiscal", new OpcaoPerfilAcesso("PermiteAlterarStatusNotaFiscal",
                "10.04 - Permitir alterar status nota fiscal", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "10.04 - Permitir alterar status nota fiscal"));
//        foi substituida pela permissão 13.10
//        opcoesPacote.put("Mostrar Aba Nota Fiscais no Perfil do Aluno", new OpcaoPerfilAcesso("MostrarAbaNotaFiscaisPerfilAluno",
//                "9.52 - Mostrar Aba Nota Fiscais no Perfil do Aluno", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "9.52 - Mostrar Aba Nota Fiscais no Perfil do Aluno"));

        opcoesPacote.put("Envio NFSe", new OpcaoPerfilAcesso("EnvioNFSe",
                "4.18 - Envio NFSe", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.18 - Envio NFSe"));

        opcoesPacote.put("Gestão de Notas", new OpcaoPerfilAcesso("GestaoNotas",
                "4.28 - Gestão de Notas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.28 - Acessar e enviar NFSE através da gestão de notas"));

        opcoesPacote.put("Gestão de NFCe", new OpcaoPerfilAcesso("GestaoNFCe",
                "4.38 - Gestão de NFC-e", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.38 - Acessar e enviar NFC-e através da gestão de NFC-e"));

        opcoesPacote.put("Acesso ao modulo de notas", new OpcaoPerfilAcesso("AcessoModuloNotasUsuario",
                "4.41 - Acesso ao Módulo de Notas", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "4.41 - Acesso ao Módulo de Notas"));

        return opcoesPacote;
    }

    public static Hashtable inicializarPessoas() {
        Hashtable<String, OpcaoPerfilAcesso> opcoesPacote = new Hashtable<String, OpcaoPerfilAcesso>();

        opcoesPacote.put("AlunoAbaContratos", new OpcaoPerfilAcesso("AlunoAbaContratos",
                "13.00 - Ver aba Contratos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.00 - Ver aba Contratos", AgrupadorFuncionalidadeEnum.CONTRATO));

        opcoesPacote.put("AlunoAbaAvaliacaoFisica", new OpcaoPerfilAcesso("AlunoAbaAvaliacaoFisica",
                "13.01 - Ver aba Avaliação Física", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.01 - Ver aba Avaliação Física"));

        opcoesPacote.put("AlunoAbaAcessos", new OpcaoPerfilAcesso("AlunoAbaAcessos",
                "13.02 - Ver aba Acessos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.02 - Ver aba Acessos"));

        opcoesPacote.put("AlunoAbaDocumentos", new OpcaoPerfilAcesso("AlunoAbaDocumentos",
                "13.03 - Ver aba Documentos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.03 - Ver aba Documentos"));

        opcoesPacote.put("AlunoAbaFinanceiro", new OpcaoPerfilAcesso("AlunoAbaFinanceiro",
                "13.04 - Ver aba Financeiro", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.04 - Ver aba Financeiro"));

        opcoesPacote.put("AlunoAbaGraduacao", new OpcaoPerfilAcesso("AlunoAbaGraduacao",
                "13.05 - Ver aba Graduação", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.05 - Ver aba Graduação"));

        opcoesPacote.put("AlunoAbaPactoPay", new OpcaoPerfilAcesso("AlunoAbaPactoPay",
                "13.06 - Ver aba PactoPay", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.06 - Ver aba PactoPay"));

        opcoesPacote.put("AlunoAbaProdutos", new OpcaoPerfilAcesso("AlunoAbaProdutos",
                "13.07 - Ver aba Produtos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.07 - Ver aba Produtos"));

        opcoesPacote.put("AlunoAbaTreino", new OpcaoPerfilAcesso("AlunoAbaTreino",
                "13.08 - Ver aba Treino", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.08 - Ver aba Treino"));

        opcoesPacote.put("AlunoAbaCrm", new OpcaoPerfilAcesso("AlunoAbaCrm",
                "13.09 - Ver aba CRM", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.09 - Ver aba CRM"));

        opcoesPacote.put("AlunoAbaNotaFiscal", new OpcaoPerfilAcesso("AlunoAbaNotaFiscal",
                "13.10 - Ver aba Nota Fiscal", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.10 - Ver aba Nota Fiscal"));

        opcoesPacote.put("AlunoCadastrarDocumento", new OpcaoPerfilAcesso("AlunoCadastrarDocumento",
                "13.11 - Cadastrar documentos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.11 - Cadastrar documentos"));

        opcoesPacote.put("AlunoExcluirDocumento", new OpcaoPerfilAcesso("AlunoExcluirDocumento",
                "13.12 - Excluir documentos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.12 - Excluir documentos"));

        opcoesPacote.put("AlunoVerCardPrincipal", new OpcaoPerfilAcesso("AlunoVerCardPrincipal",
                "13.13 - Ver informações pessoais dentro do card principal", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.13 - Ver informações pessoais dentro do card principal"));

        opcoesPacote.put("AlunoVerAvisos", new OpcaoPerfilAcesso("AlunoVerAvisos",
                "13.14 - Ver Avisos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.14 - Ver Avisos"));

        opcoesPacote.put("VisualizarCPFBusca", new OpcaoPerfilAcesso("VisualizarCPFBusca",
                "13.15 - Visualizar CPF no resultado da busca de alunos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE, "13.15 - Visualizar CPF no resultado da busca de alunos"));

        opcoesPacote.put("VerPendenciasClienteListaAcessos", new OpcaoPerfilAcesso("VerPendenciasClienteListaAcessos",
                "13.16 - Ver as pendências do cliente pela lista de últimos acessos", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "13.16 - Ver as pendências do cliente pela lista de últimos acessos"));

        opcoesPacote.put("PermitirAtualizacaoContratoAssinado", new OpcaoPerfilAcesso("PermitirAtualizacaoContratoAssinado",
                "13.17 - Permitir atualização manual de contratos não assinados", OpcaoPerfilAcesso.TP_FUNCIONALIDADE,
                "13.17 - Permitir atualização manual de contratos não assinados"));
        return opcoesPacote;
    }

    public String getPermissaoPorEntidade(String entidade) {
        String permissaoApresentar = "";
        Hashtable modulos = (Hashtable) OpcoesPerfilAcesso.getAplicacao();
        Enumeration e = modulos.elements();
        while (e.hasMoreElements()) {
            Hashtable modulo = (Hashtable) e.nextElement();
            Enumeration i = modulo.elements();
            while (i.hasMoreElements()) {
                OpcaoPerfilAcesso opcao = (OpcaoPerfilAcesso) i.nextElement();
                if (opcao.getNome().equals(entidade)) {
                    permissaoApresentar = opcao.getApresentarPermissao();
                    break;
                }
            }
        }
        return permissaoApresentar;
    }
}

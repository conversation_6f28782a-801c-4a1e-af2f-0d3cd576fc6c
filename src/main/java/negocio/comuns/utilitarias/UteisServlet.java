package negocio.comuns.utilitarias;

import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.bi.JasperGenerics;
import servicos.propriedades.PropsService;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class UteisServlet {

    public static final String MSG_SEMDADOS = "Não existe resultado para essa consulta!";
    public static final String APPLICATION_NAME = "ApplicationName";
    public static final String INSTANCE_NAME = System.getProperty("com.sun.aas.instanceName", "ZW-UpdateServlet");
    public static final String CSV_DELIMITER = "-----";
    public static final String COL_CHAVE = "_chave";

    public static final String NONE = "";    public static final String EMPTY = " ";
    public static final String BREAK = "\n";

    public static final String RETURN = "\r";

    public static String getUrlWithAppName(final String url, final String appName) {
        String applicationName = appName == null ? INSTANCE_NAME : appName;
        return url != null
                && !url.contains(APPLICATION_NAME) ? String.format("%s?%s=%s-UpdateServlet", url, APPLICATION_NAME, applicationName)
                : url;
    }

    public Connection obterConexao(String nomeHost, String porta, String superUser, String senha, String nomeBanco) throws Exception {
        return obterConexao(nomeHost, porta, superUser, senha, nomeBanco, null);
    }

    public Connection obterConexao(String nomeHost, String porta, String superUser, String senha, String nomeBanco, String appName) throws Exception {
        Conexao conex = new Conexao(
                getUrlWithAppName(Conexao.getConexaoPadraoPGSQL() + "//" + nomeHost + ":" + porta + "/" + nomeBanco, appName),
                superUser, senha);
        return conex.getConexao();
    }

    public void doJasperGenerics(List<Connection> conexoes, String sql, String prefixoArquivo, HttpServletResponse response, ServletContext servletContext) throws Exception {
        JasperGenerics oJasper = new JasperGenerics();
        oJasper.setSql(sql);
        oJasper.setPath(servletContext.getRealPath("relatorio") + File.separator);
        if (!UteisValidacao.emptyString(prefixoArquivo)) {
            oJasper.setPrefixoArquivo(prefixoArquivo);
        }

        oJasper.setConexoes(conexoes);
        oJasper.prepare(false, false);
        oJasper.xlsx();

        doDownloadFile(oJasper.getDestFile().getName(), null, "application/vnd.ms-excel", response, servletContext);
    }

    public void doDownloadFile(String fileName, String relatorio, String mimeType, HttpServletResponse response, ServletContext servletContext) throws Exception {
        File file;
        if (relatorio != null) {
            file = new File(fileName);
        } else {
            file = new File(servletContext.getRealPath("relatorio" + File.separator + fileName));
        }
        OutputStream out = response.getOutputStream();
        if (!fileName.equals("")) {
            response.setHeader("Content-Disposition", "attachment; filename=\"" + file.getName() + "\"");
            response.setContentType(mimeType);
            byte[] byteBuffer = new byte[4096];
            DataInputStream in = new DataInputStream(new FileInputStream(file));
            int length;
            // reads the file's bytes and writes them to the response stream
            while (((length = in.read(byteBuffer)) != -1)) {
                out.write(byteBuffer, 0, length);
                out.flush();
            }

            in.close();
            out.flush();
            out.close();
        } else {
            out.write("Não existe arquivo para ser gerado.".getBytes());
            out.flush();
            out.close();
        }
    }

    private String getDiretorioArquivos() {
        String diretorio = PropsService.getPropertyValue(PropsService.diretorioArquivos);
        diretorio = diretorio ;
        return diretorio;
    }

    public void doDownloadFileDiretorioArquivos(String fileName, String mimeType, HttpServletResponse response, ServletContext servletContext) throws Exception {
        File file = new File(getDiretorioArquivos() + File.separator + fileName);
        OutputStream out = response.getOutputStream();
        if (!fileName.equals("")) {
            response.setHeader("Content-Disposition", "attachment; filename=\"" + file.getName() + "\"");
            response.setContentType(mimeType);
            byte[] byteBuffer = new byte[4096];
            DataInputStream in = new DataInputStream(new FileInputStream(file));
            int length;
            // reads the file's bytes and writes them to the response stream
            while (((length = in.read(byteBuffer)) != -1)) {
                out.write(byteBuffer, 0, length);
                out.flush();
            }

            in.close();
            out.flush();
            out.close();
        } else {
            out.write("Não existe arquivo para ser gerado.".getBytes());
            out.flush();
            out.close();
        }
    }


    public CopyOnWriteArrayList obterColunas(ResultSet rsConsulta) throws SQLException {
        ResultSetMetaData rsmd = rsConsulta.getMetaData();
        int numColumns = rsmd.getColumnCount();

        CopyOnWriteArrayList<String> colunas =
                new CopyOnWriteArrayList<String>();

        for (int i = 1; i < numColumns + 1; i++) {
            String columnName = rsmd.getColumnName(i);
            colunas.add(columnName);
        }

        Collections.sort(colunas);
        return colunas;
    }

    public CopyOnWriteArrayList obterColunasComTipo(ResultSet rsConsulta) throws SQLException {
        ResultSetMetaData rsmd = rsConsulta.getMetaData();
        int numColumns = rsmd.getColumnCount();

        CopyOnWriteArrayList<String> colunas = new CopyOnWriteArrayList<>();

        for (int i = 1; i < numColumns + 1; i++) {
            String columnName = rsmd.getColumnName(i);
            String columnType = Uteis.getColumnTypeName(rsmd.getColumnType(i), rsmd.getColumnDisplaySize(i));
            colunas.add(String.format("%s:%s", columnName, columnType));
        }

        Collections.sort(colunas);
        return colunas;
    }

    public void logar(final String msg) {
        System.out.println(String.format("%s - %s", Calendario.getData(new Date(), "dd/MM/yyyy HH:mm:ss"), msg));
    }

    public void print(PrintWriter printer, final String msg) {
        if (printer != null) {
            final String tmp = msg.contains("<td>") ? "" : "</br>";
            printer.println(msg + tmp);
        }
    }

    public void printLine(PrintWriter printer, final String msg) {
        if (printer != null) {
            printer.println(msg);
        }
    }

    public void print(StringBuffer printer, final String msg, boolean csv) {
        if (csv) {
            printer.append(msg).append(msg);
        } else {
            final String tmp = msg.contains("<td>") ? "" : "</br>";
            printer.append(msg).append(tmp);
        }

    }

    public boolean isExcept(String text, String list) {
        if (list != null && text != null && !text.trim().isEmpty()) {
            return list.toLowerCase().contains(text.toLowerCase());
        }
        return false;
    }

    public StringBuffer doConsultaSQL(Connection con, ResultSet rsConsulta, CopyOnWriteArrayList<String> colunas,
                                      String formato, boolean exibirTitulo, int seq, String chave) {
        StringBuffer out = new StringBuffer();
        try {
            if (formato == null) {
                formato = "html";
            }
            try {
                if (formato.equals("html")) {
                    print(out, con.getMetaData().getURL(), false);
                }
                out.append(obterResultado(colunas, rsConsulta, formato, exibirTitulo, seq, chave));

            } catch (Exception e) {
                if (!formato.startsWith("csv"))
                    print(out, con.getMetaData().getURL() + ": " + e.getMessage(), false);
            }
        } catch (Exception ex) {
            if (!formato.startsWith("csv"))
                print(out, ex.getMessage(), false);
        }
        return out;
    }

    public static String getDelimiter(String format) {
        if (format.split(",").length >1 ){
            return format.split(",")[1];
        }
        return UteisServlet.CSV_DELIMITER;
    }

    public StringBuffer obterResultado(CopyOnWriteArrayList<String> colunas, ResultSet rsConsulta, String formato, boolean exibirTitulo, int seq, String chave) throws Exception {
        if (formato != null && formato.equals("json")) {
            return obterJSON(colunas, rsConsulta, seq);
        } else if (formato != null && formato.equals("excel")) {
            return obterExcel(colunas, rsConsulta, exibirTitulo, seq);
        } else {
            return obterHTML(colunas, rsConsulta, seq);
        }
    }

    public String obterResultadoCSV(CopyOnWriteArrayList<String> colunas, ResultSet rsConsulta, String formato, boolean exibirTitulo, int seq, String chave) throws Exception {
        return obterCSV(colunas, rsConsulta, chave, seq, getDelimiter(formato));
    }

    private StringBuffer obterJSON(List<String> colunas, ResultSet rsConsulta, int seq) throws Exception {
        StringBuffer conteudo = new StringBuffer();
        //
        JSONArray jsonArray = new JSONArray();

        do {
            JSONObject json = new JSONObject();
            json.put("seq", seq);
            for (String nomeCol : colunas) {
                try {
                    Object obj = rsConsulta.getObject(nomeCol);

                    if (obj != null) {
                        json.put(nomeCol, obj);
                    } else {
                        json.put(nomeCol, "");

                    }

                } catch (Exception e) {                    
                    Uteis.logar(e.getMessage());
                    e.printStackTrace();
                }
            }
            jsonArray.put(json);
            seq++;
        } while (rsConsulta.next());

        conteudo.append(jsonArray.toString());

        return conteudo;
    }

    private StringBuffer obterExcel(CopyOnWriteArrayList<String> colunas, ResultSet rsConsulta, boolean exibirTitulo, int seq) throws Exception {
        StringBuffer conteudo = new StringBuffer();

        if (exibirTitulo) {
            conteudo.append("<table style=\"font-size:10px;font-family:Arial;\" border=\"1\">");
            conteudo.append("<tr>");
            conteudo.append("<td>").append("Seq.").append("</td>");
            for (String nomeCol : colunas) {
                conteudo.append("<td>").append(nomeCol).append("</td>");
            }
            conteudo.append("</tr>");
        }
        do {
            conteudo.append("<tr>");
            conteudo.append("<td>").append(seq).append("</td>");
            for (String nomeCol : colunas) {
                Object obj = rsConsulta.getObject(nomeCol);
                if (obj != null) {
                    conteudo.append("<td>").append(obj.toString()).append("</td>");
                } else {
                    conteudo.append("<td>").append("").append("</td>");

                }
            }

            conteudo.append("</tr>");
            seq++;
        } while (rsConsulta.next());
        return conteudo;
    }

    private StringBuffer obterHTML(CopyOnWriteArrayList<String> colunas, ResultSet rsConsulta, int seq) throws Exception {
        StringBuffer conteudo = new StringBuffer();

        conteudo.append("Resultado: ");
        conteudo.append("<table style=\"font-size:10px;font-family:Arial;\" border=\"1\">");
        //
        conteudo.append("<tr>");
        conteudo.append("<td>").append("Seq.").append("</td>");
        for (String nomeCol : colunas) {
            conteudo.append("<td>").append(nomeCol).append("</td>");
        }
        conteudo.append("</tr>");

        //
        do {
            conteudo.append("<tr>");
            conteudo.append("<td>").append(seq).append("</td>");
            for (String nomeCol : colunas) {
                Object obj = rsConsulta.getObject(nomeCol);
                if (obj != null) {
                    conteudo.append("<td>").append(obj.toString()).append("</td>");
                } else {
                    conteudo.append("<td>").append("").append("</td>");

                }
            }

            conteudo.append("</tr>");
            seq++;
        } while (rsConsulta.next());

        conteudo.append("</table></br>");
        return conteudo;
    }

    private String obterCSV(final CopyOnWriteArrayList<String> colunas, ResultSet rsConsulta, final String chave, int seq,
                                  final String csvDelimiter) throws Exception {

        File dir = new File(System.getProperty("java.io.tmpdir"));

        if (!dir.exists()){
            dir = new File(PropsService.getPropertyValue(PropsService.diretorioArquivos));
        }

        File f = File.createTempFile(String.format("zwcsv-%s-%s", chave, System.currentTimeMillis()),".csv", dir);

        try (BufferedWriter csvWriter = Files.newBufferedWriter(Paths.get(f.getAbsolutePath()), StandardCharsets.UTF_8)) {

            int columnCount = colunas.size();

            // Write the data rows
            int row = 1;
            do {
                int i = 1;
                //Uteis.logarDebug("Registro => " + row);
                try {
                    for (String col : colunas) {
                        try {
                            final Object val = rsConsulta.getObject(col);
                            if (val != null) {
                                String value = val.toString();
                                if (value.contains(csvDelimiter))
                                    value = value.replaceAll(csvDelimiter, NONE);
                                if (value.contains(BREAK))
                                    value = value.replaceAll(BREAK, EMPTY);
                                if (value.contains(RETURN))
                                    value = value.replaceAll(RETURN, EMPTY);

                                csvWriter.append(value);
                                //Uteis.logarDebug(String.format("      Registro => %s coluna => %s valor %s ", row, col, value));

                            } else {
                                csvWriter.append(NONE);
                            }
                            try {
                                if (i < columnCount) {
                                    csvWriter.append(csvDelimiter);
                                }
                            } catch (Exception e) {
                                Uteis.logarDebug(String.format("ERROR %s to appender %s delimiter %s", e.getMessage(), colunas.get(i), csvDelimiter));
                            }
                        } finally {
                            i++;
                        }
                    }

                    if (!UteisValidacao.emptyString(chave))
                        csvWriter.append(csvDelimiter).append(chave);

                } finally {
                    csvWriter.append(BREAK);
                    row++;
                }
            } while (rsConsulta.next());


        }

        if (f.length() > 0)
            return f.getAbsolutePath();
        return null;
    }

    private JSONArray addFieldsString(JSONArray arr, String... names) {
        for (String n : names) {
            JSONObject oField = new JSONObject();
            oField.put("name", n);
            JSONArray arrType = new JSONArray();
            arrType.put("string");
            arrType.put("null");
            oField.put("type", arrType);
            arr.put(oField);
        }
        return arr;
    }

    private JSONObject createField(String name, String type) {
        JSONObject oField = new JSONObject();
        oField.put("name", name);
        JSONArray arrType = new JSONArray();
        arrType.put(type);
        arrType.put("null");
        oField.put("type", arrType);
        return oField;
    }

    private String createFieldSimple(String name, String type) {
        return String.format("%s:%s", name, type);
    }

    private JSONObject createFieldTimestamp(String name) {
        JSONObject oField = new JSONObject();
        oField.put("name", name);
        oField.put("logicalType", "timestamp-millis");
        JSONArray arrType = new JSONArray();
        arrType.put("long");
        arrType.put("null");
        oField.put("type", arrType);

        return oField;
    }

    public Schema obterSchemaParquet(ResultSet rsConsulta) throws Exception {
        JSONObject rootParquet = new JSONObject("{\n" +
                "\t\"namespace\": \"br.com.sistemapacto.ns\",\n" +
                "\t\"type\": \"record\",\n" +
                "\t\"name\": \"pactoParquetZW\"\n" +
                "}");
        //
        List<String> colunas = obterColunasComTipo(rsConsulta);
        JSONArray arrFields = new JSONArray();

        for (String col : colunas) {

            String nomeCol = col.split(":")[0];
            String tipoCol = col.split(":")[1];

            if (tipoCol.startsWith("int") || tipoCol.startsWith("bigint")) {
                arrFields.put(createField(nomeCol, "int"));
            } else if (tipoCol.startsWith("double") || tipoCol.startsWith("float") || tipoCol.startsWith("numeric")) {
                arrFields.put(createField(nomeCol, "double"));
            } else if (tipoCol.contains("date") || tipoCol.contains("datetime") || tipoCol.contains("timestamp")) {
                arrFields.put(createFieldTimestamp(nomeCol));
            } else {
                arrFields.put(createField(nomeCol, "string"));
            }
        }
        //metadados
        arrFields = addFieldsString( arrFields, "_chave", "_nomeBanco", "_host", "_versaoBD", "_versao", "_createdAt");

        rootParquet.put("fields", arrFields);

        Schema.Parser parser = new Schema.Parser().setValidate(true);
        Uteis.logarDebug("Parquet Schema: " + rootParquet.toString());
        return parser.parse(rootParquet.toString());
    }

    public String obterSchemaJsonParquet(ResultSet rsConsulta) throws Exception {
        List<String> colunas = obterColunasComTipo(rsConsulta);
        List<String> arrFields = new ArrayList<>();

        for (String col : colunas) {

            String nomeCol = col.split(":")[0];
            String tipoCol = col.split(":")[1];

             if (tipoCol.startsWith("int") || tipoCol.startsWith("bigint") || tipoCol.startsWith("serial")) {
                arrFields.add(createFieldSimple(nomeCol, "Int64"));
            } else if (tipoCol.startsWith("double") || tipoCol.startsWith("float") || tipoCol.startsWith("numeric")) {
                arrFields.add(createFieldSimple(nomeCol, "float64"));
            } else if (tipoCol.contains("date") || tipoCol.contains("timestamp")) {
                arrFields.add(createFieldSimple(nomeCol, "datetime64[ns]"));
            } else if (tipoCol.contains("bool")) {
                arrFields.add(createFieldSimple(nomeCol, "bool"));
            } else {
                arrFields.add(createFieldSimple(nomeCol, "object"));
            }
        }
        //metadados
        arrFields.add(createFieldSimple("_chave", "object"));
        String array = Uteis.splitFromArray(arrFields.toArray(), false);
        Uteis.logarDebug("Parquet Schema JSON: " + array);

        return array;
    }

    public GenericData.Record addMetadata(GenericData.Record record, JSONObject o) throws Exception {
        for (Object k : o.keySet()) {
            record.put(k.toString(), o.get(k.toString()));
        }
        return record;
    }

    public GenericData.Record obterParquetData(List<String> colunas, Schema schema, ResultSet rsConsulta, JSONObject metadata) throws Exception {
        GenericData.Record record = new GenericData.Record(schema);

        for (String nomeCol : colunas) {
            Object obj = rsConsulta.getObject(nomeCol);
            if (obj instanceof java.sql.Timestamp)
                record.put(nomeCol, ((Timestamp)obj).getTime());
            else if (obj instanceof java.sql.Date)
                record.put(nomeCol, ((java.sql.Date)obj).getTime());
            else
                record.put(nomeCol, obj);
        }

        record = addMetadata(record, metadata);

        return record;
    }
}

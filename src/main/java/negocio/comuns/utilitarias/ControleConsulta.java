package negocio.comuns.utilitarias;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.faces.context.FacesContext;

import javax.servlet.http.HttpServletRequest;

/**
 * Classe responsável por controlar a paginação de um conjunto de objetos armazenados em um lista <code>List</code>.
 * Fornecer atributos que persistem informações sobre: os parâmetros para realização da consulta, página atual da 
 * da consulta, posicição inicial e final da lista que está sendo apresentada e, por fim, o número de itens total
 * armazenados na lista que está sendo paginada. Adicionalmente, esta classe fornece métodos que permitem navegar
 * para primeira, próxima, anterior e última página. Calculando automaticamente a posição inicial e final da próxima 
 * página. Também fornece informações como número total de páginas.
 */
public class ControleConsulta implements Serializable {

    private String campoConsulta;
    private String valorConsulta;
    //Inicio - Novos filtros para a consultar o log
    private String campoConsulta2;
    private String valorConsulta2;
    //Fim - Novos filtros para a consultar o log    
    private int posInicialListar;
    private int posFinalListar;
    private int paginaAtual;
    private int tamanhoConsulta;
    private int numeroPaginas;
    private Date inicio;
    private Date fim;

    private int numeroPaginaScroll;
    private long totalRegistrosEncontrados;
    private int totalPaginas;
    private int limitePorPagina;
    private List<Integer> listaPaginas;
    private int offset;

    public ControleConsulta() {
        campoConsulta = "";
        valorConsulta = "";
        campoConsulta2 = "";
        valorConsulta2 = "";
        paginaAtual = 1;
        posInicialListar = 0;
        posFinalListar = Uteis.TAMANHOLISTA;
        tamanhoConsulta = 0;

        numeroPaginaScroll =  Uteis.TAMANHOLISTA;
        limitePorPagina = Uteis.TAMANHOLISTA;
    }

    /**
     * Inicializa todos os atributos necessários para o início de uma nova consulta.
     */
    public static void inicializar(ControleConsulta controleConsulta) {
        controleConsulta.paginaAtual = 1;
        controleConsulta.posInicialListar = 0;
        controleConsulta.posFinalListar = Uteis.TAMANHOLISTA;
        controleConsulta.tamanhoConsulta = 0;
    }

    /**
     * Configura todos os atributos necessários para apresentação da página fornecida pelo parâmetro.
     * Permitindo uma fácil navegação para a página desejada.
     */
    public void definirProximaPaginaApresentar(int pagina) {
        paginaAtual = pagina;
        if (paginaAtual > 1) {
            posInicialListar = (pagina - 1) * Uteis.TAMANHOLISTA;
            posFinalListar = (pagina) * Uteis.TAMANHOLISTA;
        } else {
            posInicialListar = 0;
            posFinalListar = Uteis.TAMANHOLISTA;
        }
    }

    public String getURLPrimeiraPagina() {
        String url = null;
        if ((paginaAtual > 1)
                && (getNrTotalPaginas() > 1)) {
            url = "?consultar=0"
                    + "&pagina=" + 1
                    + "&campoConsulta=" + campoConsulta
                    + "&valorConsulta=" + valorConsulta
                    + "&campoConsulta2=" + campoConsulta2
                    + "&valorConsulta2=" + valorConsulta2;
        }
        return url;
    }

    public String getURLPaginaAnterior() {
        String url = null;
        if (paginaAtual > 1) {
            url = "?consultar=0"
                    + "&pagina=" + (paginaAtual - 1)
                    + "&campoConsulta=" + campoConsulta
                    + "&valorConsulta=" + valorConsulta
                    + "&campoConsulta2=" + campoConsulta2
                    + "&valorConsulta2=" + valorConsulta2;
        }
        return url;
    }

    public String getURLPaginaPosterior() {
        String url = null;
        if (paginaAtual < getNrTotalPaginas()) {
            url = "?consultar=0"
                    + "&pagina=" + (paginaAtual + 1)
                    + "&campoConsulta=" + campoConsulta
                    + "&valorConsulta=" + valorConsulta
                    + "&campoConsulta2=" + campoConsulta2
                    + "&valorConsulta2=" + valorConsulta2;
        }

        return url;
    }

    public String getURLUltimaPagina() {
        String url = null;
        if ((getNrTotalPaginas() > 1)
                && (paginaAtual < getNrTotalPaginas())) {
            url = "?consultar=0"
                    + "&pagina=" + getNrTotalPaginas()
                    + "&campoConsulta=" + campoConsulta
                    + "&valorConsulta=" + valorConsulta
                    + "&campoConsulta2=" + campoConsulta2
                    + "&valorConsulta2=" + valorConsulta2;
        }
        return url;
    }

    public static List paginarConsulta(HttpServletRequest request, List resultado, ControleConsulta controleConsulta) throws Exception {
        ControleConsulta.registrarTamanhoConsulta(resultado, controleConsulta);
        resultado = ControleConsulta.obterSubListPaginaApresentar(resultado, controleConsulta);
        ControleConsulta.registrarParametrosConsulta(request, controleConsulta);
        return resultado;
    }

    public static void registrarParametrosConsulta(HttpServletRequest request, ControleConsulta controleConsulta) throws Exception {
        request.setAttribute("campoConsulta", controleConsulta.getCampoConsulta());
        request.setAttribute("valorConsulta", controleConsulta.getValorConsulta());
        request.setAttribute("campoConsulta2", controleConsulta.getCampoConsulta2());
        request.setAttribute("valorConsulta2", controleConsulta.getValorConsulta2());
        request.setAttribute("prmPrimeiraPg", controleConsulta.getURLPrimeiraPagina());
        request.setAttribute("prmPgAnterior", controleConsulta.getURLPaginaAnterior());
        request.setAttribute("prmPgPosterior", controleConsulta.getURLPaginaPosterior());
        request.setAttribute("prmUltimaPg", controleConsulta.getURLUltimaPagina());
        request.setAttribute("paginaAtual", controleConsulta.getPaginaAtualDeTodas());
        request.setAttribute("tamanhoTotalConsulta", String.valueOf(controleConsulta.getTamanhoConsulta()));
        request.setAttribute("controleConsulta", controleConsulta);
    }

    public static void registrarTamanhoConsulta(List resultado, ControleConsulta controleConsulta) {
        if (resultado == null) {
            return;
        }
        controleConsulta.setTamanhoConsulta(resultado.size());
    }

    public static List obterSubListPaginaApresentar(List resultado, ControleConsulta controleConsulta) {
        if (resultado == null) {
            inicializar(controleConsulta);
            return null;
        }
        if (resultado.isEmpty()) {
            inicializar(controleConsulta);
            return resultado;
        }

        List subList = new ArrayList();
        if (controleConsulta.getPosFinalListar() > resultado.size()) {
            controleConsulta.setPosFinalListar(resultado.size());
        }
        if (controleConsulta.getPosInicialListar() < resultado.size()) {
            subList = resultado.subList(controleConsulta.getPosInicialListar(), controleConsulta.getPosFinalListar());
        }
        controleConsulta.setTamanhoConsulta(resultado.size());
        return subList;
    }

    public int getNrTotalPaginas() {
        double tamanhoPagina = Uteis.TAMANHOLISTA;
        double nrPaginasDouble = Math.ceil(tamanhoConsulta / tamanhoPagina);
        String nrTotalPaginas = String.valueOf(nrPaginasDouble);
        nrTotalPaginas = nrTotalPaginas.substring(0, nrTotalPaginas.indexOf("."));
        return (Integer.parseInt(nrTotalPaginas));
    }

    public String getPaginaAtualDeTodas() {
        return paginaAtual + "/" + getNrTotalPaginas();
    }

    public int getPosInicialListar() {
        return posInicialListar;
    }

    public void setPosInicialListar(int posInicialListar) {
        this.posInicialListar = posInicialListar;
    }

    public int getPosFinalListar() {
        return posFinalListar;
    }

    public void setPosFinalListar(int posFinalListar) {
        this.posFinalListar = posFinalListar;
    }

    public int getTamanhoConsulta() {
        return tamanhoConsulta;
    }

    public void setTamanhoConsulta(int tamanhoConsulta) {
        this.tamanhoConsulta = tamanhoConsulta;
    }

    public String getCampoConsulta() {
        return campoConsulta;
    }

    public void setCampoConsulta(String campoConsulta) {
        if (campoConsulta == null) {
            campoConsulta = "";
        }
        this.campoConsulta = campoConsulta;
    }

    public String getValorConsulta() {
        return valorConsulta;
    }

    public void setValorConsulta(String valorConsulta) {
        if (valorConsulta == null) {
            valorConsulta = "";
        }
        this.valorConsulta = valorConsulta;
    }

    /**
     * @return O campo campoConsulta2.
     */
    public String getCampoConsulta2() {
        return campoConsulta2;
    }

    /**
     * @param campoConsulta2 O novo valor de campoConsulta2.
     */
    public void setCampoConsulta2(String campoConsulta2) {
        if (campoConsulta2 == null) {
            campoConsulta2 = "";
        }
        this.campoConsulta2 = campoConsulta2;
    }

    /**
     * @return O campo valorConsulta2.
     */
    public String getValorConsulta2() {
        return valorConsulta2;
    }

    /**
     * @param valorConsulta2 O novo valor de valorConsulta2.
     */
    public void setValorConsulta2(String valorConsulta2) {
        if (valorConsulta2 == null) {
            valorConsulta2 = "";
        }
        this.valorConsulta2 = valorConsulta2;
    }

    public int getPaginaAtual() {
        return paginaAtual;
    }

    public void setPaginaAtual(int paginaAtual) {
        this.paginaAtual = paginaAtual;
    }

    /**
     * @return O campo numeroPaginas.
     */
    public int getNumeroPaginas() {
        this.numeroPaginas = getNrTotalPaginas();
        return this.numeroPaginas;
    }

    /**
     * @param numeroPaginas O novo valor de numeroPaginas.
     */
    public void setNumeroPaginas(int numeroPaginas) {
        this.numeroPaginas = numeroPaginas;
    }

    public void irPaginaInicial() throws Exception {
        this.setPaginaAtual(1);
    }

    public void irPaginaAnterior() throws Exception {
        this.setPaginaAtual(this.getPaginaAtual() - 1);
    }

    public void irPaginaPosterior() throws Exception {
        this.setPaginaAtual(this.getPaginaAtual() + 1);
    }

    public void irPaginaFinal() throws Exception {
        this.setPaginaAtual(this.getNrTotalPaginas());
    }

	public void setFim(Date fim) {
		this.fim = fim;
	}

	public Date getFim() {
		return fim;
	}

	public void setInicio(Date inicio) {
		this.inicio = inicio;
	}

	public Date getInicio() {
		return inicio;
	}








    public int getOffset() {
        return getLimitePorPagina() * (getPaginaAtual() - 1);
    }

    public void setOffset(int offset) {
        if (offset < 0) {
            this.offset = 0;
        }
        this.offset = offset;
    }


    public int getProximaPagina() {
        if (getIsTemProximo()) {
            return (getPaginaAtual() + 1);
        }
        return getTotalPaginas();
    }


    public Boolean getIsTemProximo() {
        if (getPaginaAtual() >= getTotalPaginas()) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public String getCssPaginaAtual() {
        Integer pagina = (Integer) FacesContext.getCurrentInstance().getExternalContext().getRequestMap().get("pagina");
        if (getPaginaAtual() == pagina) {
            return "paginaAtual";
        }
        return "tituloCamposReduzidos";
    }

    public List<Integer> getListaPaginas() {
        montarListaPaginas();
        return listaPaginas;
    }


    public String getNumeroMaximoPagina() {
        if (listaPaginas != null) {
            return "" + listaPaginas.size();
        }
        return "0";
    }


    public int getPaginaAnterior() {
        if (getIsTemAnterior()) {
            return (getPaginaAtual() - 1);
        }
        return 1;
    }

    public Boolean getIsTemAnterior() {
        if (getPaginaAtual() > 1) {
            return Boolean.TRUE;
        }
        return true;
    }

    public int getNumeroPaginaScroll() {
        return numeroPaginaScroll;
    }

    public void setNumeroPaginaScroll(int numeroPaginaScroll) {
        this.numeroPaginaScroll = numeroPaginaScroll;
    }


    public long getTotalRegistrosEncontrados() {
        return totalRegistrosEncontrados;
    }

    public void setTotalRegistrosEncontrados(long totalRegistrosEncontrados) {
        this.totalRegistrosEncontrados = totalRegistrosEncontrados;
    }

    public void setLimitePorPagina(int limitePorPagina) {
        this.limitePorPagina = limitePorPagina;
    }

    public int getLimitePorPagina() {
        return limitePorPagina;
    }
    public int getTotalPaginas() {
        totalPaginas = (int) Math.ceil(((double) getTotalRegistrosEncontrados()) / ((double) getLimitePorPagina()));
        return totalPaginas;
    }


    private void montarListaPaginas() {
        listaPaginas = null;
        listaPaginas = new ArrayList<Integer>(0);

        int paginasEsquerda = (getPaginaAtual() - getNumeroPaginaScroll());
        int paginasDireita = (getPaginaAtual() + (getNumeroPaginaScroll() - 1));
        int numeroPaginas = paginasDireita;

        if (getTotalPaginas() < paginasDireita) {
            numeroPaginas = getTotalPaginas();
        }

        for (int i = (getPaginaAtual() <= getNumeroPaginaScroll() ? 0 : (paginasEsquerda - 1)); i < numeroPaginas; i++) {
            listaPaginas.add(i + 1);
        }
    }
}

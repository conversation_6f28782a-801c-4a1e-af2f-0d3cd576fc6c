package negocio.comuns.utilitarias;

import java.util.Arrays;


public enum AgrupadorFuncionalidadeEnum {

    APLICATIVO_ATIVO("Aplicativos Ativos"),
    AUTORIZACAO_DE_ACESSO("Autorização de Acesso"),
    PERFIL_DE_ACESSO("Perfil de Acesso"),
    AMBIENTE("Ambiente"),
    CLIENTE_COM_COBRANCA_AUTOMATICA_BLOQUEADA("Clientes com Cobrança Automática Bloqueada"),
    ANIVERSARIANTES("Aniversariantes"),
    PACOTE("Pacote"),
    EVENTO("Evento"),
    IMPOSTO("Imposto"),
    HORARIO("Horário"),
    PESQUISA_DE_SATISFACAO_NPS("Pesquisa de Satisfação / NPS"),
    FECHAMENTO_ACESSOS("Fechamento Acessos"),
    CONTRATOS_POR_DURACAO("Contratos por Duração"),
    PARCELA_EM_ABERTO("Parcela em Aberto"),
    LISTA_DE_ACESSO("Lista de Acesso"),
    NIVEL_DE_TURMA("Nível de Turma"),
    BALANCO("Balanço"),
    SOCIAL("Social"),
    BRINDES("Brindes"),
    LANCAMENTO_DE_PRODUTO_COLETIVO("Lançamento de Produto Coletivo"),
    AGENDAMENTOS("Agendamentos"),
    BANCO("Banco"),
    OBJECAO("Objeção"),
    ESTORNO_DE_RECIBOS("Estorno de Recibos"),
    ALTERAR_SENHA("Alterar Senha"),
    OPERACAO_COLETIVA("Operação Coletiva"),
    FERIADO("Feriado"),
    CONTATO_EM_GRUPO("Contato em Grupo"),
    CONTATOS_APP("Contatos APP"),
    REGISTRO_DE_PARALISACAO("Registro de Paralisação"),
    REGISTRAR_ACESSO_MANUAL("Registrar Acesso Manual"),
    JUSTIFICATIVA_DE_OERACAO("Justificativa de Operação"),
    SCRIPT("Script"),
    BI_ADMINISTRATIVO("BI Administrativo"),
    BI_FINANCEIRO("BI Financeiro"),
    CONCILIACAO("Conciliação"),
    BOLETO_PACTO("Boleto Pacto"),
    BLOQUEIO_DE_CAIXA("Bloqueio de Caixa"),
    ABRIR_CAIXA("Abrir Caixa"),
    FLUXO_DE_CAIXA("Fluxo de Caixa"),
    VER_LANCAMENTOS("Ver Lançamentos"),
    CONTA_CORRENTE("Conta Corrente"),
    DEMOSNTRATIVO_FINANCEIRO("Demonstrativo Financeiro"),
    RESUMO_DE_CONTAS("Resumo de Contas"),
    NOVA_CONTA_A_PAGAR("Nova Conta a Pagar"),
    CAIXA_EM_ABERTO("Caixa em Aberto"),
    CAMPANHA_CUPOM_DESCONTO("Campanha Cupom Desconto"),
    CAMPANHA("Campanha"),
    CARDEX("Cardex"),
    LOTES("Lotes"),
    RECEBIVEIS("Recebíveis"),
    CATEGORIA_DE_CLIENTES("Categoria de Clientes"),
    CATEGORIA_DE_PRODUTOS("Categoria de Produtos"),
    MOVIMENTO_DO_PRODUTO("Movimento do Produto"),
    CATEGORIA_DE_ATIVIDADES("Categoria de Atividades"),
    METAS_FINANCEIRAS_DE_VENDAS("Metas Financeiras de Vendas"),
    META_DIARIA("Meta Diária"),
    META_EXTRA("Meta Extra"),
    CIDADE("Cidade"),
    REALIZAR_CONTATO("Realizar Contato"),
    CLASSIFICACAO("Classificação"),
    CONFIGURACOES("Configurações"),
    CONFIGURACOES_CLUBE_DE_VANTAGENS("Configurações Clube de Vantagens"),
    CONFIGURACOES_FINANCEIRAS("Configurações Financeiras"),
    CONFIGURAR_PRODUTO_ESTOQUE("Configurar Produto Estoque"),
    CONVENIO_DE_DESCONTO("Convênio de Desconto"),
    CONVENIO_DE_COBRANCA("Convênio de Cobrança"),
    CLIENTE("Cliente"),
    COLABORADOR("Colaborador"),
    COMISSAO_PARA_CONSULTOR("Comissão para Consultor"),
    COMPETENCIA_MENSAL("Competência Mensal"),
    COMISSAO_PARA_PROFESSOR("Comissão para Professor"),
    CONDICAO_DE_PAGAMENTO("Condição de Pagamento"),
    COMPRA("Compra"),
    CONFIG_NOTA_FISCAL("Config. Nota Fiscal"),
    SALDO_CONTA_CORRENTE("Saldo Conta Corrente"),
    SALDO_DE_CREDITOS("Saldo de Créditos"),
    DESCONTO("Desconto"),
    DIARIA("Diária"),
    EMPRESA("Empresa"),
    FECHAMENTO_DE_CAIXA_POR_OPERADOR("Fechamento de Caixa por Operador"),
    FORMAS_DE_PAGAMENTO("Formas de Pagamento"),
    FREEPASS("FreePass"),
    FORNECEDOR("Fornecedor"),
    GERAL_DE_CLIENTES("Geral de Clientes"),
    GESTAO_DE_NEGATIVACOES("Gestão de Negativações"),
    GESTAO_DE_VENDAS_ONLINE("Gestão de Vendas Online"),
    GESTAO_DE_PERSONAL("Gestão de Personal"),
    GESTAO_DE_TRANSACOES("Gestão de Transações"),
    GESTAO_DE_CARTEIRAS("Gestão de Carteiras"),
    GESTAO_DE_NOTAS("Gestão de Notas"),
    GESTAO_DE_NFC_E("Gestão de NFC-e"),
    GESTAO_DE_ARMARIO("Gestão de Armário"),
    GESTAO_DE_REMESSAS("Gestão de Remessas"),
    GESTAO_DE_BOLETOS_ONLINE("Gestão de Boletos Online"),
    GRAU_DE_INSTRUCAO("Grau de Instrução"),
    GRUPO_COM_DESCONTO("Grupo com desconto"),
    GRUPO_COLABORADOR("Grupo Colaborador"),
    INDICE_FINANCEIRO_REAJUSTE_PRECOS("Índice Financeiro Reajuste Preços"),
    MAPA_DE_TURMAS("Mapa de Turmas"),
    RATEIO_INTEGRACAO("Rateio Integração"),
    MODELO_DE_CONTRATO("Modelo de Contrato"),
    MODELO_DE_ORCAMENTO("Modelo de Orçamento"),
    TAXA_DE_COMISSAO("Taxa de Comissão"),
    OPERADORA_DE_CARTAO("Operadora de Cartão"),
    PAIS("País"),
    PARENTESCO("Parentesco"),
    PERGUNTA("Pergunta"),
    PESSOA("Pessoa"),
    POSICAO_DO_ESTOQUE("Posição do Estoque"),
    PLANO("Plano"),
    PLANO_DE_CONTAS("Plano de Contas"),
    PRODUTO("Produto"),
    PRODUTOS("Produtos"),
    PROFISSAO("Profissão"),
    QUESTIONARIO("Questionário"),
    RECEITA_POR_PERIODO("Receita por Período"),
    RELATORIO_DE_BVS("Relatório de Bvs"),
    RELATORIO_DE_FATURAMENTO("Relatório de Faturamento"),
    RELATORIO_DE_PARCELAS("Relatório de PARCELAS"),
    RELATORIO_DE_CLIENTES("Relatório de Clientes"),
    RELATORIO_GYMPASS("Relatório Gympass"),
    RELATORIO_DE_PERSONAL("Relatório de Personal"),
    RELATORIO_DE_CONVIDADOS("Relatório de Convidados"),
    SORTEIO("Sorteio"),
    TIPO_DE_MODALIDADE("Tipo de Modalidade"),
    TIPO_DE_PLANO("Tipo de Plano"),
    TIPO_DE_REMESSA("Tipo de Remessa"),
    TIPO_DE_RETORNO("Tipo de Retorno"),
    TIPO_DE_DOCUMENTO("Tipo de Documento"),
    TIPO_DE_CONTA("Tipo de Conta"),
    VENDA_AVULSA("Venda Avulsa"),
    VENDA_RAPIDA("Venda Rápida"),
    VENDA_DE_CONSUMIDOR("Venda de Comsumidor"),
    TURMA("Turma"),
    CONFIGURAR_AULAS("Configurar Aulas"),
    AULA_EXCLUIDA("Aula Excluída"),
    AGENDA_DE_AULAS("Agenda de Aulas"),
    MODALIDADE_AGENDA("Modalidade (Agenda)"),
    AMBIENTE_AGENDA("Ambiente (Agenda)"),
    CONSULTA_DE_TURMA("Consulta de Turma"),
    CONSULTA_HISTORICO_CONTATO("Consulta Histórico Contato"),
    HISTORICO_DE_PONTOS("Histórico de Pontos"),
    APARELHOS("Aparelhos"),
    ATIVIDADES("Atividades"),
    FICHAS_PREDEFINIDAS("Fichas Predefinidas"),
    PROGRAMAS_PREDEFINIDOS("Programas Predefinidos"),
    NIVEIS("Níveis"),
    OBJETIVOS("Objetivos"),
    CADASTROS_AUXILIARES("Cadastros Auxiliares"),
    ANAMNESES("Anamneses"),
    BENCHMARKS("Benchmarks"),
    TIPO_BENCHMARK("Tipo Benchmark"),
    WOD("Wod"),
    CONTA("Conta"),
    TIPO_WOD("Tipo Wod"),
    BI_CROSS("BI Cross"),
    BI_TREINO("BI Treino"),
    BI_CRM("BI CRM"),
    RANKING("Ranking"),
    USUARIO("Usuário"),
    PRESCRICAO_DE_TREINO("Prescrição de treino"),
    ACOMPANHAMENTO_DE_PERSONAL("Acompanhamento de Personal"),
    IMPORTACAO("Importação"),
    CONTROLE_DE_LOG("Controle de Log"),
    LOCAL_DE_ACESSO("Local de Acesso"),
    SERVIDOR_FACIAL("Servidor Facial"),
    CENTRO_DE_CUSTOS("Centro de Custos"),
    MOVIMENTACAO_DE_CONTA_CORRENTE_DO_CLIENTE("Movimento de Conta Corrente do Cliente"),
    CHEQUES_CARTOES_AVULSOS("Cheques/Cartões Avulsos"),
    AGENDA_DE_SERVICO("Agenda de Serviços"),

    CONTRATO("Contrato"),
    PACTOPAY("PactoPay"),
    ;
    private String nome;
    AgrupadorFuncionalidadeEnum(String nome){
        this.nome = nome;
    }

    public static AgrupadorFuncionalidadeEnum findByCodigo(String nome) {
        return Arrays.stream(values())
                .filter(AgrupadorFuncionalidadeEnum -> AgrupadorFuncionalidadeEnum.getNome()== nome)
                .findFirst().orElse(null);

    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}

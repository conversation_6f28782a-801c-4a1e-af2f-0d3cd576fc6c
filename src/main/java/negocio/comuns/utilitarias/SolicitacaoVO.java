package negocio.comuns.utilitarias;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import org.json.JSONObject;

import java.io.File;
import java.util.Date;

public class SolicitacaoVO extends SuperVO implements Cloneable{

    private Integer codigo;
    private TipoSolicitacaoEnum tipo;

    private Date dataLancamento;
    private Date dataProcessamento;

    private StatusSolicitacaoEnum status = StatusSolicitacaoEnum.AGUARDANDO_PROCESSAMENTO;

    private String resultado;

    private String dadosSolicitacao;

    private UsuarioVO usuarioSolicitante;

    private EmpresaVO empresa;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoSolicitacaoEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoSolicitacaoEnum tipo) {
        this.tipo = tipo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataProcessamento() {
        return dataProcessamento;
    }

    public void setDataProcessamento(Date dataProcessamento) {
        this.dataProcessamento = dataProcessamento;
    }

    public StatusSolicitacaoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusSolicitacaoEnum status) {
        this.status = status;
    }

    public String getResultado() {
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

    public String getDadosSolicitacao() {
        return dadosSolicitacao;
    }

    public void setDadosSolicitacao(String dadosSolicitacao) {
        this.dadosSolicitacao = dadosSolicitacao;
    }

    public UsuarioVO getUsuarioSolicitante() {
        if(usuarioSolicitante == null){
            usuarioSolicitante =  new UsuarioVO();
        }
        return usuarioSolicitante;
    }

    public void setUsuarioSolicitante(UsuarioVO usuarioSolicitante) {
        this.usuarioSolicitante = usuarioSolicitante;
    }

    public EmpresaVO getEmpresa() {
        if(empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getMensagemErroProcessamento(){
        try {
            if (this.getStatus().getCodigo().equals(StatusSolicitacaoEnum.ERRO_PROCESSAMENTO.getCodigo())) {
                JSONObject resultado = new JSONObject(this.resultado);
                return resultado.optString("Erro");
            }
        } catch (Exception ignored) {
        }
        return "";
    }

    public String getLinkArquivoNotas(){
        try {
            if (this.getStatus().getCodigo().equals(StatusSolicitacaoEnum.CONCLUIDA.getCodigo())) {
                JSONObject resultado = new JSONObject(this.resultado);
                return resultado.optString("linkArquivo");
            }
        } catch (Exception ignored) {
        }
        return "";
    }

    public boolean getApresentarErro(){
        return this.getStatus().getCodigo().equals(StatusSolicitacaoEnum.ERRO_PROCESSAMENTO.getCodigo());
    }

    public boolean getApresentarLink(){
        return this.getStatus().getCodigo().equals(StatusSolicitacaoEnum.CONCLUIDA.getCodigo());
    }

    public StringBuilder gerarCorpoEmailSolicitacaoNotas() throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailLinkDownloadNotas_V1.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());

        String aux = texto.toString()
                .replaceAll("#NOME_USUARIO", Uteis.trocarAcentuacaoPorAcentuacaoHTML(this.getUsuarioSolicitante().getNomeAbreviado()))
                .replaceAll("#NOME_EMPRESA", Uteis.trocarAcentuacaoPorAcentuacaoHTML(this.getEmpresa().getNome()))
                .replaceAll("#URL_LINK", this.getLinkArquivoNotas())
                .replace("#CODIGO_SOLICITACAO", this.getCodigo().toString())
                .replace("#DATA_SOLICITACAO", Uteis.trocarAcentuacaoPorAcentuacaoHTML(Uteis.getDataComHHMM(this.getDataLancamento())));
        return new StringBuilder(aux);
    }
}

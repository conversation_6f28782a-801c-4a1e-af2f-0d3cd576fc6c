/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

import br.com.pactosolucoes.integracao.viacep.ViaCepJSON;
import negocio.comuns.arquitetura.SuperVO;

import javax.swing.text.MaskFormatter;

/**
 *
 * <AUTHOR>
 */
public class CepVO extends SuperVO {

    protected String ufDescricao;
    protected String ufSigla;
    protected String cidadeCep;
    protected String cidadeDescricao;
    protected String bairroDescricao;
    protected String enderecoCompleto;
    protected String enderecoLogradouro;
    protected String enderecoCep;

    public CepVO() {
        inicializarDados();
    }

    public CepVO(ViaCepJSON viaCepJSON) {
        this.ufSigla = viaCepJSON.getUf();
        this.enderecoCep = viaCepJSON.getCep();
        this.cidadeDescricao = viaCepJSON.getLocalidade();
        this.bairroDescricao = viaCepJSON.getBairro();
        this.enderecoLogradouro = viaCepJSON.getLogradouro();
    }

    public void inicializarDados() {
        setBairroDescricao("");
        setCidadeCep("");
        setCidadeDescricao("");
        setEnderecoCep("");
        setEnderecoCompleto("");
        setUfDescricao("");
        setUfSigla("");
    }

    public String getBairroDescricao() {
        if (bairroDescricao == null) {
            bairroDescricao = "";
        }
        return bairroDescricao;
    }

    public void setBairroDescricao(String bairroDescricao) {
        this.bairroDescricao = bairroDescricao;
    }

    public String getCidadeCep() {
        if (cidadeCep == null) {
            cidadeCep = "";
        }
        return cidadeCep;
    }

    public void setCidadeCep(String cidadeCep) {

        this.cidadeCep = cidadeCep;
    }

    public String getCidadeDescricao() {
        if (cidadeDescricao == null) {
            cidadeDescricao = "";
        }
        return cidadeDescricao;
    }

    public void setCidadeDescricao(String cidadeDescricao) {
        this.cidadeDescricao = cidadeDescricao;
    }

    public String getEnderecoCep() {
        if (enderecoCep == null) {
            enderecoCep = "";
        }
        return enderecoCep;
    }
    public String getEnderecoCepComMascara(){
        try {
            MaskFormatter mf = new MaskFormatter("##.###-###");
            mf.setValueContainsLiteralCharacters(false);
            return mf.valueToString(getEnderecoCep().trim());
        }catch (Exception ex){
            return getEnderecoCep();
        }
    }
    public void setEnderecoCep(String enderecoCep) {
        this.enderecoCep = enderecoCep;
    }

    public String getEnderecoCompleto() {
        if (enderecoCompleto == null) {
            enderecoCompleto = "";
        }
        return enderecoCompleto;
    }

    public void setEnderecoCompleto(String enderecoCompleto) {
        this.enderecoCompleto = enderecoCompleto;
    }

    public String getEnderecoLogradouro() {
        if (enderecoLogradouro == null) {
            enderecoLogradouro = "";
        }
        return enderecoLogradouro;
    }

    public void setEnderecoLogradouro(String enderecoLogradouro) {
        this.enderecoLogradouro = enderecoLogradouro;
    }

    public String getUfDescricao() {
        if (ufDescricao == null) {
            ufDescricao = "";
        }
        return ufDescricao;
    }

    public void setUfDescricao(String ufDescricao) {
        this.ufDescricao = ufDescricao;
    }

    public String getUfSigla() {
        if (ufSigla == null) {
            ufSigla = "";
        }
        return ufSigla;
    }

    public void setUfSigla(String ufSigla) {
        this.ufSigla = ufSigla;
    }
}

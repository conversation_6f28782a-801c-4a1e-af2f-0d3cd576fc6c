package negocio.comuns.utilitarias;

import org.apache.commons.lang.CharEncoding;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;

import java.io.StringWriter;
import java.util.Map;

/**
 * Implementação de {@link ProcessadorTemplateThymeleaf}, juntamente com o padrão <b>SINGLETON</b>.
 *
 * <AUTHOR>
 * @since 12/12/2018
 */
public class ProcessadorTemplateThymeleafSingletonImpl implements ProcessadorTemplateThymeleaf {

    /**
     * Esta definição permite que a busca pelo template seja sempre por <b>arquivos HTML</b>.
     */
    private static final String SUFIXO_HTML = ".html";
    /**
     * Veja mais em <a href="https://github.com/thymeleaf/thymeleaf/issues/391">Gith<PERSON> - Thymeleaf</a>
     */
    private static final TemplateMode MODE_PADRAO_TEMPLATE = TemplateMode.HTML;
    /**
     * O mesmo <b>encoding</b> deve ser usado nos arquivos <b>html</b> para evitar problemas de codificação.
     */
    private static final String CHARACTER_ENCODING = CharEncoding.ISO_8859_1;

    private static final ProcessadorTemplateThymeleafSingletonImpl INSTANCE = new ProcessadorTemplateThymeleafSingletonImpl();

    {
        CLASS_LOADER_TEMPLATE_RESOLVER_HTML = inicializeHTMLResolver();
        TEMPLATE_ENGINE = inicializeTemplateEngine();
    }

    /**
     * Veja em <a href="https://www.thymeleaf.org/doc/tutorials/3.0/usingthymeleaf.html">Thymeleaf 3.0</a>.
     */
    private final ClassLoaderTemplateResolver CLASS_LOADER_TEMPLATE_RESOLVER_HTML;
    /**
     * Veja em <a href="https://www.thymeleaf.org/doc/tutorials/3.0/usingthymeleaf.html">Thymeleaf 3.0</a>.
     */
    private final TemplateEngine TEMPLATE_ENGINE;

    private ProcessadorTemplateThymeleafSingletonImpl() { }

    public static ProcessadorTemplateThymeleafSingletonImpl getInstance() {
        return INSTANCE;
    }

    private ClassLoaderTemplateResolver inicializeHTMLResolver() {
        ClassLoaderTemplateResolver resolver = new ClassLoaderTemplateResolver();
        resolver.setTemplateMode(MODE_PADRAO_TEMPLATE);
        resolver.setPrefix(DIRETORIO_PADRAO_TEMPLATES);
        resolver.setSuffix(SUFIXO_HTML);
        resolver.setCharacterEncoding(CHARACTER_ENCODING);

        return resolver;
    }

    private TemplateEngine inicializeTemplateEngine() {
        TemplateEngine engine = new TemplateEngine();
        engine.setTemplateResolver(CLASS_LOADER_TEMPLATE_RESOLVER_HTML);

        return engine;
    }

    public String processarTemplateHTML(String path, Context context) {
        StringWriter writer = new StringWriter();
        TEMPLATE_ENGINE.process(path, context, writer);

        return writer.toString();
    }

    public Context returnContextWithVariables(Map<String, Object> mapContextVariables) {
        Context context = new Context();

        for (Map.Entry<String, Object> entry : mapContextVariables.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }

        return context;
    }

}

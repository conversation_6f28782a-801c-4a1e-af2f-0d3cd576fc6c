/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.utilitarias;

import java.io.File;

/**
 *
 * <AUTHOR>
 */
public class ImageEmailHtml {

    private String caminhoReplace = "";
    private File arquivo;

    public ImageEmailHtml(String caminhoReplace, File arquivo) {
        this.caminhoReplace = caminhoReplace;
        this.arquivo = arquivo;
    }

    public ImageEmailHtml() {
    }

    public void setArquivo(File arquivo) {
        this.arquivo = arquivo;
    }

    public File getArquivo() {
        return arquivo;
    }

    public void setCaminhoReplace(String caminhoReplace) {
        this.caminhoReplace = caminhoReplace;
    }

    public String getCaminhoReplace() {
        return caminhoReplace;
    }

}

package negocio.comuns.utilitarias;

import br.com.pactosolucoes.enumeradores.BIEnum;

/**
 * Created by <PERSON> on 06/05/2016
 */
public class BICarregarTO {

    private BIEnum bi;
    private boolean naLixeira;
    private boolean carregado;
    private boolean carregar;
    private String mensagem;
    private String containerRenderizar;
    private Integer ordem;
    private long tempoCarregamento = 0;

    public BICarregarTO(BIEnum bi,String mensagem,boolean carregar){
        this.bi = bi;
        this.ordem = bi == null ? 0 : bi.getOrdemOriginal();
        this.mensagem = mensagem;
        this.carregado = false;
        this.carregar = carregar;
        this.naLixeira = false;
        this.containerRenderizar = "";
    }
    public BICarregarTO(){
        this.bi = null;
        this.mensagem = "";
        this.carregado = false;
        this.carregar = true;
        this.naLixeira = false;
        this.containerRenderizar = "";
    }
    public BIEnum getBi() {
        return bi;
    }

    public void setBi(BIEnum bi) {
        this.bi = bi;
    }

    public boolean isCarregado() {
        return carregado;
    }

    public boolean isApresentarBoxCarregar(){
        return !this.carregado && this.carregar;
    }

    public void setCarregado(boolean carregado) {
        this.carregado = carregado;
    }

    public boolean isCarregar() {
        return carregar;
    }

    public void setCarregar(boolean carregar) {
        this.carregar = carregar;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public boolean isNaLixeira() {
        return naLixeira;
    }

    public void setNaLixeira(boolean naLixeira) {
        this.naLixeira = naLixeira;
    }

    public String getContainerRenderizar() {
        return containerRenderizar;
    }

    public void setContainerRenderizar(String containerRenderizar) {
        this.containerRenderizar = containerRenderizar;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public void inicializarDadosSobreCarregamento(){
        this.carregado = false;
        this.carregar = true;
    }

    public long getTempoCarregamento() {
        return tempoCarregamento;
    }

    public void setTempoCarregamento(long tempoCarregamento) {
        this.tempoCarregamento = tempoCarregamento;
    }
}

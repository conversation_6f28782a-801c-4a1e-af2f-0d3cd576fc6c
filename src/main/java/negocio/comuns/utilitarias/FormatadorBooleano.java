/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;

/**
 *
 * <AUTHOR>
 */
public class FormatadorBooleano implements Converter {

    private static final String TRUE = "Sim";
    private static final String FALSE = "Não";

    public FormatadorBooleano() {
    }

    @Override
    public Object getAsObject(final FacesContext context, final UIComponent component, final String value) {
        try {
            return Boolean.valueOf(value);
        } catch (final Exception e) {
            return false;
        }
    }

    @Override
    public String getAsString(final FacesContext context,
            final UIComponent component, Object value) {
        try {
            if (Boolean.valueOf(value.toString())) {
                return TRUE;
            } else {
                return FALSE;
            }
        } catch (Exception e) {
            return FALSE;
        }
    }

}

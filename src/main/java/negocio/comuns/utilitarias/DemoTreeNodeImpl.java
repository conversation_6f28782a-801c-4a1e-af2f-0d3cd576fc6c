package negocio.comuns.utilitarias;

import org.richfaces.model.TreeNodeImpl;
public class DemoTreeNodeImpl<T> extends TreeNodeImpl<T>{

    private String icon;
    private String leafIcon;
    
	private String type;

    /**
     * @return the type
     */
    public String getType() {
        return type;
    }

    /**
     * @param type the type to set
     */
    public void setType(String type) {
        this.type = type;
    }

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getLeafIcon() {
		return leafIcon;
	}

	public void setLeafIcon(String leafIcon) {
		this.leafIcon = leafIcon;
	}  
    
}
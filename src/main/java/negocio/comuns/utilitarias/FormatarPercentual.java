/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

import java.text.NumberFormat;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
/**
 *
 * <AUTHOR>
 */
public class FormatarPercentual implements Converter{

    public FormatarPercentual() {
    }

    @Override
    public Object getAsObject(final FacesContext context, final UIComponent component, final String value) {
        try {
            String valor = "";
            final NumberFormat nf = NumberFormat.getInstance();
            nf.setMinimumFractionDigits(2);
            nf.setMaximumFractionDigits(2);
            if (value.contains("%")) {
                valor =  value.replaceAll("%", "");
            }else{
                valor = value;
            }

            return new Double(nf.parse(valor).doubleValue());
        } catch (final Exception e) {
            return new Double(0.0);
        }
    }

    @Override
    public String getAsString(final FacesContext context,
            final UIComponent component, Object value) {
        if ((value == null) || (value.toString().trim().equals(""))) {
            value = new Double(0.0);
        }
        final NumberFormat nf = NumberFormat.getInstance();
        nf.setMinimumFractionDigits(2);
        nf.setMaximumFractionDigits(2);
        String  resultado = nf.format(Double.valueOf(value.toString()))+"%";
        return resultado;
    }
    
}

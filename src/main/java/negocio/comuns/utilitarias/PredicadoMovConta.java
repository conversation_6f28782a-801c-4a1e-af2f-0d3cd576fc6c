package negocio.comuns.utilitarias;

import negocio.comuns.financeiro.MovContaVO;
import org.apache.commons.collections4.Predicate;

public class PredicadoMovConta implements Predicate<MovContaVO> {

    private Integer codigo;

    public PredicadoMovConta(Integer codigo) {
        this.codigo = codigo;
    }

    @Override
    public boolean evaluate(MovContaVO movContaVO) {
        return movContaVO.getCodigo().equals(codigo);
    }
}

package negocio.comuns.utilitarias;

public enum StatusSolicitacaoEnum {
    AGUARDANDO_PROCESSAMENTO(0, "Aguardando Processamento"),
    PROCESSANDO(1, "PROCESSANDO"),
    ERRO_PROCESSAMENTO(2, "Erro no Processamento"),

    CONCLUIDA(3, "Concluída")
    ;


    private Integer codigo;
    private String descricao;

    StatusSolicitacaoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static StatusSolicitacaoEnum obterPorCodigo(int codigo){
        for(StatusSolicitacaoEnum a : StatusSolicitacaoEnum.values()){
            if(a.getCodigo() == codigo){
                return a;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}

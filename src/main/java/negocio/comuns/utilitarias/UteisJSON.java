package negocio.comuns.utilitarias;

import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.SuperEmpresaVO;
import negocio.facade.jdbc.arquitetura.FacadeManager;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.*;

/**
 * Created by ulisses on 11/11/2016.
 */
public class UteisJSON {

    private static Field getField(Object obj, String nameField)throws Exception{
        List<Field> lista = UtilReflection.getFields(obj.getClass());
        for (Field field: lista){
            if (field.getName().toUpperCase().equals(nameField.toUpperCase())){
                return field;
            }
        }
        return null;
    }

    public static <T> List<T> jsonToListObject(Class<T> clazz, JSONArray jsonArray) throws Exception{
        List lista = new ArrayList();
        for (int i = 0; i < jsonArray.length(); i++) {
            Object object = UteisJSON.jsonToObject(clazz, (JSONObject) jsonArray.get(i));
            lista.add(object);
        }
        return lista;
    }

    public static <T> T jsonToObject(Class<T> clazz, JSONObject jsonObject) throws Exception{
        T object  = clazz.newInstance();
        Iterator it = jsonObject.keys();
        String key = "";
        try{
            while (it.hasNext()){
                key = (String)it.next();
                Object valor = jsonObject.get(key);
                if (valor.toString().equals("null")){
                    continue;
                }
                //Field field = clazz.getDeclaredField(key);
                Field field = getField(object, key);
                if( (field == null) || field.getName().toUpperCase().equals("CHANGED")){
                    continue;
                }
                field.setAccessible(true);
                Annotation anotationJson = (Annotation) getAnnotationJson(field);
                if (anotationJson != null) {
                    if (anotationJson instanceof ListJson){
                        List lista = new ArrayList();
                        JSONArray jsonArray = (JSONArray)valor;
                        for (int i=0; i<jsonArray.length(); i++){
                            Object obj = jsonToObject(((ListJson) anotationJson).clazz(), (JSONObject) jsonArray.get(i));
                            lista.add(obj);
                        }
                        UtilReflection.setValor(object,lista,key,false);
                    }else if (anotationJson instanceof FKJson){
                        Object obj = jsonToObject(field.getType(), (JSONObject) valor);
                        UtilReflection.setValor(object,obj,key,false);
                    }
                }else{
                    if (field.getType().isEnum()){
                        Object objEnum = getEnumField(field, ((String)valor));
                        UtilReflection.setValor(object, objEnum, key, false);
                    }else{
                        if ((field.getType() == Date.class) && (valor instanceof Long)){
                            Calendar data = Calendar.getInstance();
                            data.setTimeInMillis((Long)valor);
                            valor = data.getTime();
                        }
                        UtilReflection.setValor(object,valor,key,false);
                    }

                }
            }
        }catch (Exception e){
            throw new ConsistirException("Erro ao fazer reflect no campo: " + key + " Erro:" + e.getMessage());
        }
        return  object;

    }

    public static Annotation getAnnotationJson(Field field) {
        for (Annotation annotacao : field.getAnnotations()) {
            if ((annotacao instanceof FKJson) || (annotacao instanceof ListJson)  ) {
                return annotacao;
            }
        }
        return null;
    }


    public static Object getEnumField(Field field, String valor)throws Exception{
        Enum<?>[] enums = (Enum[]) field.getType().getEnumConstants();
        for (Enum e: enums) {
            if (e.name().equals(valor)){
                return e;
            }
        }
        return null;
    }

    public static List<Field> getFieldsAteSuperVO(Class<?> clazz) {
        List<Field> listFields = new ArrayList<Field>();
        Field[] fields = null;
        while (clazz != null)  {
            fields = clazz.getDeclaredFields();
            listFields.addAll(Arrays.asList(fields));
            clazz = clazz.getSuperclass();
            if (clazz.equals(FacadeManager.class) || (clazz.equals(Observable.class)) || clazz.equals(Object.class) ) {
                clazz = null;
            }
        }
        return listFields;
    }

    public static Field[] getArrayFields(Class<?> clazz) {
        List<Field> lista = getFieldsAteSuperVO(clazz);
        Field[] fields = lista.toArray(new Field[lista.size()]);
        return fields;
    }

   /* private static boolean fieldSomenteFK(Object object)throws Exception{
        if ((object instanceof PlanoCondicaoPagamentoVO) ||
           (object instanceof PaisVO) ||
           (object instanceof EstadoVO) ||
           (object instanceof PlanoDuracaoVO)){
            return false;
        }
        return true;
    }*/

    public static JSONArray objectToJSONArray(List<Object> listaObject, boolean somenteFK)throws Exception {
        JSONArray jsonArrayDados = new JSONArray();
        for (Object object: listaObject){
            jsonArrayDados.put(objectToJSON(object, somenteFK));
        }
        return jsonArrayDados;
    }

    public static JSONObject objectToJSON(Object object, boolean somenteFK)throws Exception{
        return objectToJSON(object, somenteFK, true);
    }

    public static String arrayJsonToString(JSONObject json){
        return json.toString()
                .replace("\\", "")
                .replace("\"[", "[")
                .replace("]\"","]");
    }
    
    public static JSONObject objectToJSON(Object object, boolean somenteFK, boolean somenteFKChilds)throws Exception{
        if (object == null) {
            return new JSONObject();
        }
        JSONObject jsonObject = new JSONObject();
        Field[] fields = getArrayFields(object.getClass());
        /*if (object instanceof CategoriaVO){
            fields =  UtilReflection.getArrayFieldsSuperClass(CategoriaVO.class);
        }else{
            fields = object.getClass().getDeclaredFields();
        }*/
        String ultimoFieldName = "";
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                ultimoFieldName = field.getName();

                if (somenteFK){
                    if (field.getName().toUpperCase().equals("CODIGO")){
                        jsonObject.put(field.getName(), field.get(object));
                        break;
                    }else{
                        continue;
                    }
                }

                if (isFieldJson(field)){
                    if (field.getType().isEnum()){
                        Object objEnum = field.get(object);
                        if (objEnum != null){
                            jsonObject.put(field.getName(), ((Enum) field.get(object)).name());
                        }
                    }else{
                        if ((field.getDeclaringClass().equals(SuperVO.class)) && (field.getName().toUpperCase().equals("CODIGO"))) {
                            continue;
                        }
                        jsonObject.put(field.getName(), field.get(object));
                    }
                } else{
                    Annotation anotacaoJson = (Annotation) getAnnotationJson(field);
                    if (anotacaoJson != null){
                        if (anotacaoJson instanceof ListJson){
                            List lista = (List)field.get(object);
                            if (lista != null) {
                                JSONArray jsonArray = new JSONArray();
                                for (Object obj: lista){
                                    //JSONObject objJson = objectToJSON(obj, fieldSomenteFK(obj));
                                    JSONObject objJson = objectToJSON(obj, false, somenteFKChilds);
                                    jsonArray.put(objJson);
                                }
                                jsonObject.put(field.getName(), jsonArray);
                            }
                        }else if (anotacaoJson instanceof FKJson){
                            if ((field.getDeclaringClass().equals(SuperEmpresaVO.class)) && (field.getName().toUpperCase().equals("CIDADE"))){
                                continue;
                            }
                            JSONObject objJson = objectToJSON(field.get(object), somenteFK, somenteFKChilds);
                            jsonObject.put(field.getName(), objJson);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            throw new ConsistirException("Erro ao realizar reflect no field: " + ultimoFieldName + " erro:"+ ex.getMessage());
        }
        return jsonObject;
    }


    public static boolean isFieldJson(Field field){
        return (!(java.lang.reflect.Modifier.isStatic(field.getModifiers()))) &&
                ( (field.getType().isPrimitive()) ||
                        (field.getType().isEnum()) ||
                        (field.getType().equals(Integer.class)) ||
                        (field.getType().equals(Double.class)) ||
                        (field.getType().equals(String.class)) ||
                        (field.getType().equals(Boolean.class)) ||
                        (field.getType().equals(Date.class)));
    }

    public static void alterarValorFKs(Object VO, boolean atualizarFKAposInclusao,  Map<String, Map<Integer, Integer>> mapaFK)throws Exception{
        List<Field> listaField = UtilReflection.getFields(VO.getClass());
        for (Field field: listaField){
            field.setAccessible(true);
            Annotation anotationJson = (Annotation) UteisJSON.getAnnotationJson(field);
            if ((anotationJson != null) && (anotationJson instanceof FKJson) ) {
                Object objFK = field.get(VO);
                if (atualizarFKAposInclusao){
                    UtilReflection.setValor(objFK,0,"codigo",false);
                }else{
                    Integer codigoAntigo = (Integer)  UtilReflection.invoke(objFK, "getCodigo");
                    Integer codigoNovo = consultarMapaFK(mapaFK, objFK.getClass().getSimpleName(), codigoAntigo);
                    UtilReflection.setValor(objFK,codigoNovo,"codigo",false);
                }
            }else if ((anotationJson != null) && (anotationJson instanceof ListJson) ) {
                alterarValorFKsLista(VO, mapaFK);
            }
        }
    }

    private static Integer consultarMapaFK(Map<String, Map<Integer, Integer>> mapaFK, String chave, Integer codigoAntigo){
        Map<Integer, Integer> mapa = mapaFK.get(chave);
        if (mapa == null){
            return null;
        }
        return mapa.get(codigoAntigo);
    }

    public static void alterarValorFKsLista(Object VO,  Map<String, Map<Integer, Integer>> mapaFK)throws Exception{
        Field[] listaField = VO.getClass().getDeclaredFields();
        for (Field field: listaField){
            field.setAccessible(true);
            Annotation anotationJson = (Annotation) UteisJSON.getAnnotationJson(field);
            if ((anotationJson != null) && (anotationJson instanceof ListJson) ) {
                List lista = (List)field.get(VO);
                if (lista != null){
                    for (Object obj: lista){
                        alterarValorFKs(obj, false, mapaFK);
                    }
                }
            }
        }
    }

    public static JSONArray convert(ResultSet rs) throws SQLException, JSONException {
        JSONArray json = new JSONArray();
        ResultSetMetaData rsmd = rs.getMetaData();
        while (rs.next()) {
            int numColumns = rsmd.getColumnCount();
            JSONObject obj = new JSONObject();
            for (int i = 1; i < numColumns + 1; i++) {
                final String column_name = rsmd.getColumnName(i);
                if (rsmd.getColumnType(i) == java.sql.Types.ARRAY) {
                    obj.put(column_name, rs.getArray(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.BIGINT) {
                    obj.put(column_name, rs.getInt(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.BOOLEAN) {
                    obj.put(column_name, rs.getBoolean(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.BLOB) {
                    obj.put(column_name, rs.getBlob(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.DOUBLE) {
                    obj.put(column_name, rs.getDouble(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.FLOAT) {
                    obj.put(column_name, rs.getFloat(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.REAL) {
                    obj.put(column_name, rs.getFloat(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.INTEGER) {
                    obj.put(column_name, rs.getInt(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.NVARCHAR) {
                    obj.put(column_name, rs.getNString(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.VARCHAR) {
                    obj.put(column_name, rs.getString(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.TINYINT) {
                    obj.put(column_name, rs.getInt(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.SMALLINT) {
                    obj.put(column_name, rs.getInt(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.DATE) {
                    obj.put(column_name, rs.getDate(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.TIMESTAMP) {
                    obj.put(column_name, rs.getTimestamp(column_name));
                } else {
                    obj.put(column_name, rs.getObject(column_name));
                }
            }
            json.put(obj);
        }
        rs.close();
        return json;
    }

    public static JSONObject toJSONObject(ResultSet rs) throws SQLException, JSONException {
        ResultSetMetaData rsmd = rs.getMetaData();
        JSONObject obj = new JSONObject();
        while (rs.next()) {
            int numColumns = rsmd.getColumnCount();
            for (int i = 1; i < numColumns + 1; i++) {
                final String column_name = rsmd.getColumnName(i);
                if (rsmd.getColumnType(i) == java.sql.Types.ARRAY) {
                    obj.put(column_name, rs.getArray(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.BIGINT) {
                    obj.put(column_name, rs.getInt(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.BOOLEAN) {
                    obj.put(column_name, rs.getBoolean(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.BLOB) {
                    obj.put(column_name, rs.getBlob(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.DOUBLE) {
                    obj.put(column_name, rs.getDouble(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.FLOAT) {
                    obj.put(column_name, rs.getFloat(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.REAL) {
                    obj.put(column_name, rs.getFloat(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.INTEGER) {
                    obj.put(column_name, rs.getInt(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.NVARCHAR) {
                    obj.put(column_name, rs.getNString(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.VARCHAR) {
                    obj.put(column_name, rs.getString(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.TINYINT) {
                    obj.put(column_name, rs.getInt(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.SMALLINT) {
                    obj.put(column_name, rs.getInt(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.DATE) {
                    obj.put(column_name, rs.getDate(column_name));
                } else if (rsmd.getColumnType(i) == java.sql.Types.TIMESTAMP) {
                    obj.put(column_name, rs.getTimestamp(column_name));
                } else {
                    obj.put(column_name, rs.getObject(column_name));
                }
            }
            break;
        }
        rs.close();
        return obj;
    }

    public static String toJSON(Object objeto) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(objeto);
        } catch (Exception e) {
            return null;
        }
    }

    public static String convertLinkedHashMapToJsonString(LinkedHashMap linkedHashMap) {
        // Cria uma instância do Gson com pretty printing
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        // Converte o LinkedHashMap para JSON e retorna como string
        return gson.toJson(linkedHashMap);
    }

}

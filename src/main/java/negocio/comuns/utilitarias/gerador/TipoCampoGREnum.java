package negocio.comuns.utilitarias.gerador;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by glauco on 21/03/2014.
 */
public enum TipoCampoGREnum {

    NENHUM(""),
    VARCHAR("varchar"),
    DATA("date"),
    CHAVE_ESTRANGEIRA("int");

    private String dataType;

    private TipoCampoGREnum(String dataType) {
        this.dataType = dataType;
    }

    public List<SelectItem> getCondicoes() {
        List<SelectItem> condicoes = new ArrayList<SelectItem>();
        if (this.equals(VARCHAR)) {
            condicoes.add(new SelectItem("=", "igual a"));
            condicoes.add(new SelectItem("like '%s%'", "inicia com"));
            condicoes.add(new SelectItem("like '%%s'", "termina com"));
        } else if (this.equals(DATA)) {
            condicoes.add(new SelectItem(">", "maior que"));
            condicoes.add(new SelectItem(">=", "maior ou igual a"));
            condicoes.add(new SelectItem("=", "igual a"));
            condicoes.add(new SelectItem("<=", "menor ou igual a"));
            condicoes.add(new SelectItem("<", "menor que"));
            condicoes.add(new SelectItem("between", "entre"));
        } else if (this.equals(CHAVE_ESTRANGEIRA)) {
            condicoes.add(new SelectItem("=", "igual a"));
            condicoes.add(new SelectItem("!=", "diferente de"));
        }

        return condicoes;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
}

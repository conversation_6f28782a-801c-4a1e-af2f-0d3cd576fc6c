package negocio.comuns.utilitarias.gerador;

import negocio.comuns.arquitetura.SuperTO;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by glauco on 21/03/2014.
 */
public class CampoGR extends SuperTO {

    private String coluna = "";
    private String tabelaJoin = "";
    private String nomeApresentar = "";
    private String entidade = "";
    private TipoCampoGREnum tipoCampo = TipoCampoGREnum.NENHUM;
    private List<SelectItem> valoresChaveEstrangeira;

    public CampoGR() {

    }

    public CampoGR(String coluna,String tabelaJoin,String entidade, String nomeApresentar, TipoCampoGREnum tipoCampo) {
        this.coluna = coluna;
        this.nomeApresentar = nomeApresentar;
        this.tabelaJoin = tabelaJoin;
        this.tipoCampo = tipoCampo;
        this.entidade = entidade;
    }

    public String getColuna() {
        return coluna;
    }

    public void setColuna(String coluna) {
        this.coluna = coluna;
    }

    public String getNomeApresentar() {
        return nomeApresentar;
    }

    public void setNomeApresentar(String nomeApresentar) {
        this.nomeApresentar = nomeApresentar;
    }

    public TipoCampoGREnum getTipoCampo() {
        return tipoCampo;
    }

    public List<SelectItem> getValoresChaveEstrangeira() {
        if(valoresChaveEstrangeira == null){
            valoresChaveEstrangeira = new ArrayList<SelectItem>();
        }
        return valoresChaveEstrangeira;
    }

    public void setValoresChaveEstrangeira(List<SelectItem> valoresChaveEstrangeira) {
        this.valoresChaveEstrangeira = valoresChaveEstrangeira;
    }

    public String getEntidade() {
        return entidade;
    }

    public void setEntidade(String entidade) {
        this.entidade = entidade;
    }

    public String getTabelaJoin() {
        return tabelaJoin;
    }

    public void setTabelaJoin(String tabelaJoin) {
        this.tabelaJoin = tabelaJoin;
    }

    public void setTipoCampo(TipoCampoGREnum tipoCampo) {
        this.tipoCampo = tipoCampo;
    }

}

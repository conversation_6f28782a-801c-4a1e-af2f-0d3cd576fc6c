package negocio.comuns.utilitarias.gerador;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by glauco on 24/03/2014.
 */
public class ItemFiltroGR extends SuperTO {

    private CampoGR campoGR = new CampoGR();
    private List<SelectItem> condicoesPermitidas = new ArrayList<>();
    private String condicao = "";
    private Object valor1 = "";
    private Object valor2 = "";
    private String agrupador = "";
    private boolean apresentarValor1 = true;
    private boolean apresentarValor2 = false;
    private boolean permiteAgrupador = false;
    private boolean apresentarEntradas = false;

    public void validarItem() throws ConsistirException {
        if (getCampoGR() == null || getCampoGR().getColuna() == null || getCampoGR().getColuna().isEmpty()) {
            throw new ConsistirException("Você quer filtrar por?");
        }

        if (getCondicao() == null || getCondicao().isEmpty()) {
            throw new ConsistirException("Selecione todas as condições");
        }

        if (getValor1() == null) {
            throw new ConsistirException("Preencha todos os valores");
        }

        if (getCondicao() != null && getCondicao().equals("between") && (getValor2() == null)) {
            throw new ConsistirException("Preencha todos os valores");
        }
    }

    public void preencherCondicoes() {
        setCondicoesPermitidas(getCampoGR().getTipoCampo().getCondicoes());
    }

    public CampoGR getCampoGR() {
        return campoGR;
    }

    public void setCampoGR(CampoGR campoGR) {
        this.campoGR = campoGR;
    }

    public Object getValor1() {
        return valor1;
    }

    public void setValor1(Object valor1) {
        this.valor1 = valor1;
    }

    public Object getValor2() {
        return valor2;
    }
    public String obterValor1_Apresentar(){
        for(SelectItem li : this.getCampoGR().getValoresChaveEstrangeira()){
            if(li.getValue().toString().equals(this.getValor1())){
                return li.getLabel();
            }
        }
        return "";
    }
    public void setValor2(Object valor2) {
        this.valor2 = valor2;
    }

    public String getAgrupador() {
        return agrupador;
    }

    public void setAgrupador(String agrupador) {
        this.agrupador = agrupador;
    }

    public String getCondicao() {
        return condicao;
    }

    public void setCondicao(String condicao) {
        this.condicao = condicao;
    }

    public List<SelectItem> getCondicoesPermitidas() {
        return condicoesPermitidas;
    }

    public void setCondicoesPermitidas(List<SelectItem> condicoesPermitidas) {
        this.condicoesPermitidas = condicoesPermitidas;
    }

    public boolean isApresentarValor2() {
        return apresentarValor2;
    }

    public void setApresentarValor2(boolean apresentarValor2) {
        this.apresentarValor2 = apresentarValor2;
    }

    public boolean isPermiteAgrupador() {
        return permiteAgrupador;
    }

    public void setPermiteAgrupador(boolean permiteAgrupador) {
        this.permiteAgrupador = permiteAgrupador;
    }

    public boolean isApresentarEntradas() {
        return apresentarEntradas;
    }

    public void setApresentarEntradas(boolean apresentarEntradas) {
        this.apresentarEntradas = apresentarEntradas;
    }

    public String obterSQL() {
        String campo = getCampoGR().getColuna();
        if (getCampoGR().getTipoCampo().equals(TipoCampoGREnum.DATA)) {
            campo = campo + "::date " + getCondicao() + " '" + Uteis.getDataJDBC((Date) getValor1()) + "' ";

            if (getCondicao().equals("between")) {
                campo = campo + " AND " + " '" + Uteis.getDataJDBC((Date) getValor2()) + "' ";
            }
        } else if (getCampoGR().getTipoCampo().equals(TipoCampoGREnum.CHAVE_ESTRANGEIRA)) {
            if (campo.trim().equals("sdw.nomeplano")) {
                campo = campo + getCondicao() + "'" + getValor1() + "' ";
            } else if (campo.trim().equals("pl.descricao")) {
                campo = campo + getCondicao() + "'" + getValor1() + "' ";
            } else {
                String condition = "%s %s %s ";
                campo = String.format(condition, campo, getCondicao(), getValor1());
            }
        }

        if (!getAgrupador().equals("")) {
            campo = campo + getAgrupador() + "\n";
        }

        return campo;
    }

    public String obterTexto() throws Exception {
        String campo = getCampoGR().getNomeApresentar();
        if (getCampoGR().getTipoCampo().equals(TipoCampoGREnum.DATA)) {
            campo = campo + " " + obterCondicaoUsada(getCondicao()) + " " + Uteis.getDataAplicandoFormatacao((Date) getValor1(), "dd/MM/yyyy") + " ";

            if (getCondicao().equals("between")) {
                campo = campo + " e " + Uteis.getDataAplicandoFormatacao((Date) getValor2(), "dd/MM/yyyy") + " ";
            }
        }else if(getCampoGR().getTipoCampo().equals(TipoCampoGREnum.CHAVE_ESTRANGEIRA)){
            campo = campo +" "+getCondicao()+" ' "+ obterValor1_Apresentar() +"' ";
        }

        if (!getAgrupador().equals("")) {
            if (getAgrupador().equals("AND")) {
                campo = campo + " e ";
            } else if (getAgrupador().equals("OR")) {
                campo = campo + " ou ";
            }
        }

        return campo;
    }
    public boolean getApresentarListaChaveEstrangeira(){
        return getCampoGR().getTipoCampo() == TipoCampoGREnum.CHAVE_ESTRANGEIRA ? true : false;
    }

    private String obterCondicaoUsada(String condicaoUsada) {
        for (SelectItem condicao : getCondicoesPermitidas()) {
            if (condicaoUsada.equals(condicao.getValue())) {
                return condicao.getLabel();
            }
        }
        return condicaoUsada;
    }

    public boolean isApresentarValor1() {
        return apresentarValor1;
    }

    public void setApresentarValor1(boolean apresentarValor1) {
        this.apresentarValor1 = apresentarValor1;
    }
}

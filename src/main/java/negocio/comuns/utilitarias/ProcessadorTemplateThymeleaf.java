package negocio.comuns.utilitarias;

import org.thymeleaf.context.Context;

import java.util.Map;

/**
 * Responsável por disponibilizar uma forma de processamento de templates <b>Thymeleaf</b>.
 *
 * <AUTHOR>
 * @since 12/12/2018
 * @see <a href="https://www.thymeleaf.org/doc/tutorials/3.0/usingthymeleaf.html">Documentação oficial Thymeleaf 3.0</a>
 */
public interface ProcessadorTemplateThymeleaf {

    /**
     * Este é o diretório raiz padrão, definido para concentrar os <b>templates Thymeleafs</b>.
     */
    String DIRETORIO_PADRAO_TEMPLATES = "thymeleaf/";

    /**
     * Realiza o processamento, dado o <code>path</code> relativo do template e
     * o <code>context</code> com o mapeamento.
     *
     * @param path    repassar o restante do caminho, para concatenar com {@link #DIRETORIO_PADRAO_TEMPLATES}.
     * @param context contexto contendo o mapeamento das variáveis.
     *                Pode ser usado o método {@link #returnContextWithVariables(Map)} para retornar o mesmo.
     *
     * @return o html processado pelo Thymeleaf.
     */
    String processarTemplateHTML(String path, Context context);

    /**
     * @param mapContextVariables que será transformado em um {@link Context}.
     *
     * @return um contexto contendo todo o mapeamento das variáveis que deveram estar no template e que será processado pelo {@link #processarTemplateHTML(String, Context)}.
     */
    Context returnContextWithVariables(Map<String, Object> mapContextVariables);

}

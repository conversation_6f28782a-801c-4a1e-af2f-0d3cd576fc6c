package negocio.comuns.utilitarias;

import com.amazonaws.util.Base64;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLConnection;

public class ImageBase64Util {

    private String mimeType;
    private String fileExtension;
    private byte[] imageByteArray;
    private BufferedImage image;

    public ImageBase64Util(String base64ImageString) {


        String delims = "[,]";
        String[] parts = base64ImageString.split(delims);
        String imageString = parts[1];
        imageByteArray = Base64.decode(imageString);

        InputStream is = new ByteArrayInputStream(imageByteArray);
        ByteArrayInputStream bis = new ByteArrayInputStream(imageByteArray);

        try {
            image = ImageIO.read(bis);
            mimeType = URLConnection.guessContentTypeFromStream(is); //mimeType is something like "image/jpeg"
            String delimiter = "[/]";
            String[] tokens = mimeType.split(delimiter);
            fileExtension = tokens[1];
        } catch (IOException ioException) {

        }


    }

    public String getMimeType() {
        return mimeType;
    }

    public String getFileExtension() {
        return fileExtension;
    }

    public byte[] getImageByteArray() {
        return imageByteArray;
    }

    public BufferedImage getImage() {
        return image;
    }
}

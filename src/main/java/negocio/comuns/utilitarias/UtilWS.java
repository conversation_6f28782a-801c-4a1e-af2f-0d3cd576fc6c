package negocio.comuns.utilitarias;

import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.Map;

/*
 * Created by ulisses on 11/11/2016.
 */
public class UtilWS {

    private static int CONNECT_TIMEOUT = 3000;
    private static int READ_TIMEOUT = 30000;
    public static final String STATUS_ERRO_REQUISICAO_WS = "ERRO_REQUISICAO_WS";

    public static String executeRequestRestFul(String urlRequest, Map<String, String> params)throws Exception{
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        executeRequestHttpService.connectTimeout = CONNECT_TIMEOUT;
        executeRequestHttpService.readTimeout = READ_TIMEOUT;
        String retorno = executeRequestHttpService.executeRequestInner(urlRequest, params, "UTF-8");
        return retorno;
    }

    public static String executeRequestOAMDRestFul(String requestMapping, Map<String, String> params)throws Exception{
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/" + requestMapping;
        // String url = "http://localhost:8082/oamd/prest/" + requestMapping;
        return executeRequestRestFul(url,params);
    }

    public static String executeRequestOAMDRestFulComTratamentoDeErro(String requestMapping, Map<String, String> params)throws Exception{
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/" + requestMapping;
        String resultado = executeRequestRestFul(url,params);
        tratarErroRequisicao(resultado);
        return resultado;
    }

    private static void tratarErroRequisicao(String resultado)throws Exception{
        if (resultado.toUpperCase().contains(UtilWS.STATUS_ERRO_REQUISICAO_WS)){
            String msgErro = resultado.substring(resultado.indexOf(UtilWS.STATUS_ERRO_REQUISICAO_WS) + UtilWS.STATUS_ERRO_REQUISICAO_WS.length() + 2, resultado.indexOf("}"));
            throw new Exception(msgErro);
        }
    }

    public static String executeRequestDashBoardRestFulComTratamentoDeErro(String requestMapping, Map<String, String> params)throws Exception{
        String url = PropsService.getPropertyValue(PropsService.urlDashBoard) + "/prest/" + requestMapping;
        String resultado = executeRequestRestFul(url,params);
        tratarErroRequisicao(resultado);
        return resultado;

    }

}

package negocio.comuns.utilitarias;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.facade.jdbc.financeiro.MovParcela;

import java.util.Comparator;

/**
 * Created by johny<PERSON> on 15/12/2016.
 */
public class OrdenacaoMovParcelaPagamento implements Comparator<MovParcelaVO>{

    @Override
    public int compare(MovParcelaVO parcela1, MovParcelaVO parcela2) {
        int compare = 0;
        if(parcela1.getDataVencimento() != null && parcela2.getDataVencimento() != null){
            compare = parcela1.getDataVencimento().compareTo(parcela2.getDataVencimento());
        }
        return compare == 0 ? parcela1.getCodigo().compareTo(parcela2.getCodigo()) : compare;
    }
}

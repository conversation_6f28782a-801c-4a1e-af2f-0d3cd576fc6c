package negocio.comuns.utilitarias;

import br.com.pactosolucoes.aas.authorization.enumerator.Funcionalidade;
import controle.arquitetura.FuncionalidadeSistemaEnum;

/**
 * Enum para configurar as urls da tela nova
 * <AUTHOR>
 * @since 04/08/2021
 */
public enum UrlRedirectNZWEnum {
    PLANO("planos", FuncionalidadeSistemaEnum.PLANO),
    PLANOTIPO("tipo-plano", FuncionalidadeSistemaEnum.TIPO_PLANO, "planos"),
    PLANO_CONDICAO_PAGAMENTO("condicao-pagamento", FuncionalidadeSistemaEnum.CONDICAO_DE_PAGAMENTO, "planos"),
    DESCONTO("desconto", FuncionalidadeSistemaEnum.DESCONTO, "planos"),
    PACOTE("pacotes", FuncionalidadeSistemaEnum.PACOTE, "planos"),
    HORARIO("horarios", FuncionalidadeSistemaEnum.HORARIO, "planos"),
    PRODUTO_TAMANHO_ARMARIO("tamanho-armario", FuncionalidadeSistemaEnum.TAMANHO_ARMARIO, "produtos"),
    PRODUTO_CATEGORIA_PRODUTO("categoria-produto", FuncionalidadeSistemaEnum.CATEGORIA_PRODUTO, "produtos"),
    PRODUTO_BALANCO("balanco", FuncionalidadeSistemaEnum.BALANCO,"produtos"),
    PRODUTO_ESTOQUE("produto-estoque", FuncionalidadeSistemaEnum.CONFIGURAR_PRODUTO_ESTOQUE,"produtos"),
    CAD_AUX_PAIS("pais", FuncionalidadeSistemaEnum.PAIS, "cad-aux"),
    CAD_AUX_CIDADE("cidade", FuncionalidadeSistemaEnum.CIDADE, "cad-aux"),
    CAD_AUX_INSTRUCAO("instrucao", FuncionalidadeSistemaEnum.GRAU_DE_INSTRUCAO, "cad-aux"),
    CAD_AUX_DEPARTAMENTO("departamentos", FuncionalidadeSistemaEnum.DEPARTAMENTO, "cad-aux"),
    CAD_AUX_GRUPO_DESCONTO("grupo", FuncionalidadeSistemaEnum.GRUPO_DESCONTO, "cad-aux"),
    CAD_AUX_PARENTESCO("parentesco", FuncionalidadeSistemaEnum.PARENTESCO, "cad-aux"),
    CAD_AUX_CATEGORIA_CLIENTE("categoria", FuncionalidadeSistemaEnum.CATEGORIA_CLIENTES, "cad-aux"),
    CAD_AUX_PROFISSAO("profissao", FuncionalidadeSistemaEnum.PROFISSAO, "cad-aux"),
    CAD_AUX_CLASSIFICACAO("classificacao", FuncionalidadeSistemaEnum.CLASSIFICACAO, "cad-aux"),
    CAD_AUX_PERGUNTA("pergunta", FuncionalidadeSistemaEnum.PERGUNTA, "cad-aux"),
    CAD_AUX_QUESTIONARIO("questionario", FuncionalidadeSistemaEnum.QUESTIONARIO, "cad-aux"),
    CAD_AUX_PESQUISA("pesquisa-satisfacao", FuncionalidadeSistemaEnum.PESQUISA, "cad-aux"),
    CAD_AUX_INDICE("indice-financeiro", FuncionalidadeSistemaEnum.INDICE_FINANCEIRO_REAJUSTE_PRECO, "cad-aux"),
    CONVENIO_DESCONTO("convenio-desconto", FuncionalidadeSistemaEnum.CONVENIO_DE_DESCONTO, "planos"),
    BRINDE("brinde", FuncionalidadeSistemaEnum.BRINDE, "clube-vantagens"),
    RELATORIO_VISITANTES("relatorio-visitantes", FuncionalidadeSistemaEnum.RELATORIO_VISITANTES, "relatorios"),
    RELATORIO_CUPOM_FISCAL("relatorio-cupomfiscal", FuncionalidadeSistemaEnum.CONSULTA_DE_CUPONS_FISCAIS, "relatorios"),
    INTEGRACAO_ACESSO("integracao-acesso", FuncionalidadeSistemaEnum.INTEGRACAO_ACESSO, "acesso-sistema"),
    SERVIDOR_FACIAL("servidor-facial", FuncionalidadeSistemaEnum.SERVIDOR_FACIAL, "acesso-sistema"),
    RELATORIO_GERAL_ARMARIOS("relatorio-armario", FuncionalidadeSistemaEnum.RELATORIO_GERAL_ARMARIOS, "relatorios"),
    RELATORIO_CONVIDADOS("relatorio-convidados", FuncionalidadeSistemaEnum.RELATORIO_CONVIDADOS, "relatorios"),
    RELATORIO_DE_PERSONAL("relatorio-personal", FuncionalidadeSistemaEnum.RELATORIO_DE_PERSONAL, "relatorios"),
    RELATORIO_CONTRATOS_POR_DURACAO("contratos-por-duracao", FuncionalidadeSistemaEnum.CONTRATOS_DURACAO, "relatorios"),
    INDICADOR_ACESSOS("indicador-acessos", FuncionalidadeSistemaEnum.INDICADOR_ACESSOS, "relatorios/relatorios-acessos"),
    LISTA_ACESSOS("lista-acessos", FuncionalidadeSistemaEnum.LISTA_ACESSOS, "relatorios/relatorios-acessos"),
    TOTALIZADOR_ACESSOS("totalizador-acessos", FuncionalidadeSistemaEnum.TOTALIZADOR_ACESSOS, "relatorios/relatorios-acessos"),
    RELATORIO_PRODUTOS("relatorio-produtos-vigencia",FuncionalidadeSistemaEnum.RELATORIO_PRODUTOS,"relatorios"),
    SALDO_CREDITO("relatorio-saldo-credito", FuncionalidadeSistemaEnum.SALDO_CREDITO, "relatorios"),
    GYM_PASS_RELATORIO("relatorio-gympass", FuncionalidadeSistemaEnum.GYM_PASS_RELATORIO, "relatorios"),
    CONFIG_FINANCEIRAS_BANCO("banco", FuncionalidadeSistemaEnum.BANCO, "config-financeiras"),
    CONFIG_FINANCEIRAS_CONTA_CORRENTE("conta-corrente", FuncionalidadeSistemaEnum.CONTA_CORRENTE, "config-financeiras"),
    CONFIG_FINANCEIRAS_METAFINANCEIRA("meta-financeira", FuncionalidadeSistemaEnum.METAS_FINANCEIRO_VENDA, "config-financeiras"),
    CONFIG_FINANCEIRAS_METAFINANCEIRA_VENDA("meta-financeira", FuncionalidadeSistemaEnum.METAS_FINANCEIRO_VENDA, "config-financeiras"),
    CONFIG_CONTRATO("justificativa-operacao", FuncionalidadeSistemaEnum.JUSTIFICATIVA_OPERACAO, "config-contrato"),
    CONFIG_FINANCEIRAS_ADQUIRENTE("adquirente", FuncionalidadeSistemaEnum.ADQUIRENTE, "config-financeiras"),
    OUTRAS_OPCOES("sorteios", FuncionalidadeSistemaEnum.SORTEIO, "outras-opcoes"),
    NEGOCIACAO("contrato", FuncionalidadeSistemaEnum.NEGOCIACAO, "negociacao"),
    VENDA_AVULSA("venda-avulsa", FuncionalidadeSistemaEnum.VENDA_AVULSA),
    OPERADORA_CARTAO("operadora-cartao",FuncionalidadeSistemaEnum.OPERADORA_CARTAO, "config-financeiras"),
    CONFIG_CONTRATO_MODELO_CONTRATO("modelo-contrato", FuncionalidadeSistemaEnum.MODELO_CONTRATO, "config-contrato"),
    PERFIL_ACESSO_UNIFICADO("perfil-acesso-unificado", FuncionalidadeSistemaEnum.PERFIL_ACESSO_UNIFICADO),
    CLIENTES_COM_RESTRICOES("clientes-com-restricoes", FuncionalidadeSistemaEnum.CLIENTES_COM_RESTRICOES, "relatorios"),
    MODALIDADE("modalidade", FuncionalidadeSistemaEnum.MODALIDADE),
    FREE_PASS("freepass", FuncionalidadeSistemaEnum.FREE_PASS),
    IMPOSTO("imposto-produto", FuncionalidadeSistemaEnum.IMPOSTO, "config-financeiras"),
    SMD_RELATORIO("relatorio-smd", FuncionalidadeSistemaEnum.SMD_RELATORIO, "relatorios"),
    CAIXA_EM_ABERTO("caixa-em-aberto", FuncionalidadeSistemaEnum.CAIXA_EM_ABERTO),
    FECHAMENTO_CAIXA_OPERADOR("fechamento-caixa-operador", FuncionalidadeSistemaEnum.FECHAMENTO_CAIXA_OPERADOR),
    POSICAO_ESTOQUE("posicao-estoque", FuncionalidadeSistemaEnum.POSICAO_DO_ESTOQUE),
    DIARIA("diarias", FuncionalidadeSistemaEnum.DIARIA);

    private final String uri;
    private final FuncionalidadeSistemaEnum funcionalidadeSistemaEnum;

    /**
     * @see negocio.comuns.utilitarias.UrlRedirectNZWEnum#UrlRedirectNZWEnum(String, FuncionalidadeSistemaEnum, String...)
     */
    UrlRedirectNZWEnum(String uri, FuncionalidadeSistemaEnum funcionalidadeSistemaEnum) {
        this.uri = uri;
        this.funcionalidadeSistemaEnum = funcionalidadeSistemaEnum;
    }

    /**
     * @param uri Path/URI principal para acessar o recurso/funcionalidade
     * @param auxiliarPaths Paths auxiliar da url
     * @param funcionalidadeSistemaEnum Enum referente a funcionalidade antiga
     */
    UrlRedirectNZWEnum(String uri, FuncionalidadeSistemaEnum funcionalidadeSistemaEnum, String ...auxiliarPaths) {
        this(getAuxiliarPaths(auxiliarPaths) + uri, funcionalidadeSistemaEnum);
    }

    private static String getAuxiliarPaths(String ...auxiliarPaths) {
        StringBuilder auxiliarPath = new StringBuilder();
        for (String path : auxiliarPaths) {
            auxiliarPath.append(path).append("/");
        }
        return auxiliarPath.toString();
    }

    public static String getUriByFuncSisEnum(FuncionalidadeSistemaEnum funcionalidadeSistemaEnum) {
        for (UrlRedirectNZWEnum url: values()) {
            if (url.getFuncionalidadeSistemaEnum().equals(funcionalidadeSistemaEnum)) {
                return url.getUri();
            }
        }
        return null;
    }

    public String getUri() {
        return uri;
    }

    public FuncionalidadeSistemaEnum getFuncionalidadeSistemaEnum() {
        return funcionalidadeSistemaEnum;
    }
}



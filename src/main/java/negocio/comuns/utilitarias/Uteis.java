package negocio.comuns.utilitarias;

import br.com.pactosolucoes.autorizacaocobranca.controle.util.ValidaBandeira;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.oamd.controle.basico.BetaTestersService;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import controle.arquitetura.SuperControle;
import controle.arquitetura.exceptions.SecretException;
import controle.arquitetura.security.AlgoritmoCriptoEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.StatusAntifraudeGetNetEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.codehaus.jettison.json.JSONException;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.RobotRunner;
import servicos.compressao.sevenzip.compression.lzma.Encoder;
import servicos.impl.cieloecommerce.CieloECommerceRetornoEnum;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.redepay.ERedeRetornoEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.xml.ws.WebServiceContext;
import javax.xml.ws.handler.MessageContext;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.awt.image.VolatileImage;
import java.io.*;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.URL;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.sql.*;
import java.text.*;
import java.util.Date;
import java.util.List;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;
import java.util.zip.Adler32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static java.lang.String.format;

public class Uteis {

    public static final int TAMANHOLISTA = 10;
    public static final int NIVELMONTARDADOS_TODOS = 0;
    public static final int NIVELMONTARDADOS_DADOSBASICOS = 1;
    public static final int NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS = 2;
    public static final int NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS = 3;
    public static final int NIVELMONTARDADOS_PARCELA = 4;
    public static final int NIVELMONTARDADOS_MINIMOS = 5;
    public static final int NIVELMONTARDADOS_TELACONSULTA = 6;
    public static final int NIVELMONTARDADOS_CAIXAPARAOPERADOR = 7;
    public static final int NIVELMONTARDADOS_ROBO = 8;
    public static final int NIVELMONTARDADOS_INDICERENOVACAO = 9;
    public static final int NIVELMONTARDADOS_ORGANIZADORCARTEIRA = 10;
    public static final int NIVELMONTARDADOS_ABERTURAMETA = 11;
    public static final int NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE = 12;
    public static final int NIVELMONTARDADOS_FECHARMETADETALHADOPASSIVO = 13;
    public static final int NIVELMONTARDADOS_FECHARMETADETALHADOINDICACAO = 14;
    public static final int NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW = 15;
    public static final int NIVELMONTARDADOS_HISTORICOPASSIVO = 17;
    public static final int NIVELMONTARDADOS_HISTORICOINDICADO = 18;
    public static final int NIVELMONTARDADOS_HISTORICOCLIENTE = 19;
    public static final int NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL = 20;
    public static final int NIVELMONTARDADOS_METAPASSIVODETALHADA = 21;
    public static final int NIVELMONTARDADOS_METAINDICACAODETALHADA = 22;
    public static final int NIVELMONTARDADOS_METAAGENDAMENTODETALHADA = 23;
    public static final int NIVELMONTARDADOS_VALIDACAOACESSO = 24;
    public static final int NIVELMONTARDADOS_CONEXAOESPECIFICA = 25;
    public static final int NIVELMONTARDADOS_DADOSRESPONSAVELALTERACAO = 26;
    public static final int NIVELMONTARDADOS_TIPOPESSOA = 27;
    public static final int NIVELMONTARDADOS_TELACONSULTAESPECIAL = 28;
    public static final int NIVELMONTARDADOS_PACOTE = 29;
    public static final int NIVELMONTARDADOS_VENDA = 30;
    public static final int NIVELMONTARDADOS_USUARIO = 31;
    public static final int NIVELMONTARDADOS_SOMENTEVENDA = 32;
    public static final int NIVELMONTARDADOS_AGENDA = 33;
    public static final int NIVELMONTARDADOS_VENDACOMPACOTE = 34;
    public static final int NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO = 35;
    public static final int NIVELMONTARDADOS_CLIENTES_MAILING = 36;
    public static final int NIVELMONTARDADOS_LOTE_AVULSO = 36;
    public static final int NIVELMONTARDADOS_PAGAMENTOS_TELA_CLIENTE = 37;
    public static final int NIVELMONTARDADOS_PAGAMENTOS_CONJUNTO_RESUMIDOS = 38;
    public static final int NIVELMONTARDADOS_CONSULTA_WS = 39;
    public static final int NIVELMONTARDADOS_LOGIN = 40;
    public static final int NIVELMONTARDADOS_GESTAOREMESSA = 41;
    public static final int NIVELMONTARDADOS_IMPRESSAOCONTRATO = 42;
    public static final int NIVELMONTARDADOS_RECIBOPAGAMENTO = 43;
    public static final int NIVELMONTARDADOS_GESTAOREMESSABOLETO = 44;
    public static final int NIVELMONTARDADOS_PESSOAJURIDICA = 45;
    public static final int NIVELMONTARDADOS_VINCULOSFAMILIA = 46;
    public static final int NIVELMONTARDADOS_GESTAORECEBIVEIS = 47;
    public static final int NIVELMONTARDADOS_MOVIMENTACAOCONTRATOS = 48;
    public static final int NIVELMONTARDADOS_RESULTADOS_BI = 49;
    public static final int NIVELMONTARDADOS_COMISSAO = 50;
    public static final int NIVELMONTARDADOS_INDICARDORACESSOS = 51;
    public static final int NIVELMONTARDADOS_PONTUACAO = 52;
    public static final int NIVELMONTARDADOS_EMPRESAS = 53;
    public static final int NIVELMONTARDADOS_PARCELA_PRODUTOS = 54;
    public static final int NIVELMONTARDADOS_GYMPASS = 55;
    public static final int NIVELMONTARDADOS_COM_CLIENTE = 56;
    public static final int NIVELMONTARDADOS_CONSULTA_WS_COMPLETA = 57;
    public static final int NIVELMONTARDADOS_CONSULTA_DADOS_BOLETO = 58;
    public static final int NIVELMONTARDADOS_CONSULTA_DADOS_FOTOS_IMPORTADOR = 59;
    public static final int NIVELMONTARDADOS_GESTAOTRANSACAO = 60;
    public static final int NIVELMONTARDADOS_FECHAMENTO_ACESSO = 61;
    public static final int NIVELMONTARDADOS_REGISTRAR_JUSTIFICATIVA = 62;
    public static final int NIVELMONTARDADOS_RELATORIO_SGP = 63;
    public static final int NIVELMONTARDADOS_PESSOA_PACTO_PAY = 64;
    public static final int NIVELMONTARDADOS_AUTORIZACAO_COBRANCA = 65;
    public static final int NIVELMONTARDADOS_BASICO_TRANSACAO = 66;
    public static final int NIVELMONTARDADOS_GESTAO_PERSONAL = 67;
    public static final int NIVELMONTARDADOS_CONSULTA_WS_COM_BIOMETRIAS = 68;
    public static final int NIVELMONTARDADOS_EMPRESA_BASICO = 69;
    public static final int NIVELMONTARDADOS_CONSULTA_CODIGOMOVPARCELA = 70;
    public static final int NIVELMONTARDADOS_MINIMOS_COM_BIOMETRIAS = 71;
    public static final int NIVELMONTARDADOS_RENOVACAO_AUTOMATICA = 72;
    public static final int NIVELMONTARDADOS_ESPECIFICO_RECEBIVEIS = 73;
    public static final int NIVELMONTARDADOS_LANCAMENTOS_FINANCEIROS = 74;
    public static final int NIVELMONTARDADOS_ADMINISTRATIVORUNNER_PROCESSARNOTAS = 75;
    public static final int NIVELMONTARDADOS_GESTAO_NOTAS = 76;
    public static final int NIVELMONTARDADOS_VALIDADOR_BI = 77;
    public static final int NIVELMONTARDADOS_TELA_ALUNO = 78;
    public static final int NIVELMONTARDADOS_CONSULTA_PACTO_PAY = 79;
    public static final int NIVELMONTARDADOS_CREDENCIAIS_CONVENIO = 80;
    public static final int NIVELMONTARDADOS_DADOSMINIMOS_TRANSACAO = 81;
    public static final char A_AGUDO = 'á';
    public static final char A_AGUDOMAIUSCULO = 'Á';
    public static final char A_CRASE = 'à';
    public static final char A_CRASEMAIUSCULO = 'À';
    public static final char A_CIRCUNFLEXO = 'â';
    public static final char A_TREMA = 'ä';
    public static final char A_CIRCUNFLEXOMAIUSCULO = 'Â';
    public static final char A_TIO = 'ã';
    public static final char A_TIOMAIUSCULO = 'Ã';
    public static final char A_TREMAMAIUSCULO = 'Ä';
    public static final char E_AGUDO = 'é';
    public static final char E_AGUDOMAIUSCULO = 'É';
    public static final char E_CIRCUNFLEXO = 'ê';
    public static final char E_CIRCUNFLEXOMAIUSCULO = 'Ê';
    public static final char I_AGUDO = 'í';
    public static final char I_AGUDOMAIUSCULO = 'Í';
    public static final char O_AGUDO = 'ó';
    public static final char O_AGUDOMAIUSCULO = 'Ó';
    public static final char O_CIRCUNFLEXO = 'ô';
    public static final char O_TREMA = 'ö';
    public static final char O_CIRCUNFLEXOMAIUSCULO = 'Ô';
    public static final char O_TIO = 'õ';
    public static final char O_TIOMAIUSCULO = 'Õ';
    public static final char O_TREMAMAIUSCULO = 'Ö';
    public static final char U_AGUDO = 'ú';
    public static final char U_TREMA = 'ü';
    public static final char U_AGUDOMAIUSCULO = 'Ú';
    public static final char U_TREMAMAIUSCULO = 'Ü';
    public static final char C_CEDILHA = 'ç';
    public static final char C_CEDILHAMAIUSCULO = 'Ç';
    public static final char N_TIL = 'ñ';
    public static final char N_TILMAIUSCULO = 'Ñ';

    public static boolean realizarUpperCaseDadosAntesPersistencia = true;
    public static Uteis.EntidadeCompatorPorCodigo entidadeCompatorPorCodigo;
    public static Uteis uteis;
    public static final long UM_DIA = 86400000L;// em millisegundos
    public static final long UM_MES = UM_DIA * 30;// em millisegundos
    public static final long UM_ANO = 31536000000L;// em millisegundos
    public static final int DEPOIS = 1;
    public static final int IGUAL = 0;
    public static final int ANTES = -1;
    public static final String nomeArqCFG = "negocio/facade/jdbc/utilitarias/cfgBD.xml";
    public static boolean debug = false;
    private static final String CHAVE_CRIPTOGRAFIA = "PACTONFENFSE2012";
    private static final String CHAVE_CRIPT_ZAW_OFFLINE = "ZAW:{OFFLINE}";
    public static final int TAMANHO_MSG_SMS = 140;
    private static final String CHAVE_CRIPT_SOL = "SOL:{INTRANET}";
    private static final String CHAVE_CRIPT_MIDIA = "MID:{AMAZON}";
    public static final String TEMP_DIR = System.getProperty("java.io.tmpdir");

    public static final String URL_SESC = "https://sistemas.sesc.com.br/ServicosCatalogoSnm/api/EndpointSnm/";

    public static final String URL_SESC_NOVA_API = "https://webapicentralatendimento.sescgo.com.br/api/json/";

    public static final String SESC_NOVA_API_AUTH_USERNAME = "<EMAIL>";

    public static final String SESC_NOVA_API_AUTH_PASSWORD = "#!USER_CONSULTA123ab!#";

    public static final String URL_SESC_DF_API = "https://api-sesc.sescdf.com.br/api/";

    public static String regexTelefone = "(\\(?\\d{2}\\)?)?(\\d{4,5}\\d{4})";

    private static String authZwSecret = null;
    private static String secretKeyword = null;

    public static final int CORES = Runtime.getRuntime().availableProcessors();
    private static final ExecutorService executorService = Executors.newFixedThreadPool(CORES);
    private static final String INSTANCE_NAME = System.getProperty("com.sun.aas.instanceName", "");
    private static final String CDN_PREFIX = "cdn1.pactorian.net";
    private static final String URL_S3_PREFIX = "https://s3-sa-east-1.amazonaws.com/prod-zwphotos";
    private static final String S3_SUFFIX_TIME = "\\?time=";
    private static final String FMT_URL = "%s/%s";

    private static final java.time.format.DateTimeFormatter[] DATE_FORMATTERS = {
            java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy"),
            java.time.format.DateTimeFormatter.ofPattern("dd-MM-yyyy"),
            java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd"),
            java.time.format.DateTimeFormatter.ofPattern("dd/MM/yy"),
            java.time.format.DateTimeFormatter.ofPattern("MM/dd/yyyy") // US format
    };


    static {
        Uteis.debug = PropsService.isTrue(PropsService.logOutputDebug);
    }

    private Uteis() {
    }

    public static Integer converterInteiro(String numero) {
        return converterInteiro(numero, null);
    }

    public static Integer converterInteiro(String numero, Integer numeroPadrao){
        if(numero == null || numero.trim().isEmpty()){
            return numeroPadrao;
        }

        try{
            return Integer.parseInt(numero);
        }catch (NumberFormatException e){
            return numeroPadrao;
        }
    }
    public static String removerZerosEsquerda(String number){
        return number.replaceFirst("^0+(?!$)", "");
    }

    public static double contarDiasUteis(Date inicioPeriodo, Date fimPeriodo, Boolean sabado, Boolean domingo) {
        double qtdDias = 0.0;
        double periodo = Uteis.nrDiasEntreDatas(inicioPeriodo, fimPeriodo);
        for (int i = 0; i <= periodo; i++) {
            Date dataTemp = Calendario.getDataComHoraZerada(Uteis.somarDias(inicioPeriodo, i));
            if ((Calendario.getInstance(dataTemp).get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY && sabado)
                    || (Calendario.getInstance(dataTemp).get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY && domingo)
                    || (Calendario.getInstance(dataTemp).get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY
                    && Calendario.getInstance(dataTemp).get(Calendar.DAY_OF_WEEK) != Calendar.SATURDAY)) {
                qtdDias++;
            }
        }
        return qtdDias;
    }

    public static int contarNrVezesPorSemanaEmDiasUteisNoPeriodo(Date dataInicio, Date dataFim, Integer nrVezesPorSemana, boolean contabilizarSabado, boolean contabilizarDomingo) {
        int qtdFrequencia = 0;

        Date dataTemp = new Date(dataInicio.getTime());
        while(dataTemp.before(dataFim)) {
            int dia = Uteis.diaDaSemana(dataTemp);
            Date dataFimSemana = Uteis.somarDias(dataTemp, (7 - dia));
            if (dataFimSemana.after(dataFim)) {
                dataFimSemana = dataFim;
            }
            double diasUteisRestantesSemana = Uteis.contarDiasUteis(dataTemp, dataFimSemana, false, false);
            if (nrVezesPorSemana <= diasUteisRestantesSemana) {
                qtdFrequencia += nrVezesPorSemana;
            } else {
                qtdFrequencia += diasUteisRestantesSemana;
            }
            // ir para o primeiro dia da proxima semana
            dataTemp = Uteis.somarDias(dataTemp, (7 - dia) + 1);
        }
        return qtdFrequencia;
    }

    public static Date obterPimeiroDiaUltimosSeisMesesPrimeiraHora(Date hoje) throws Exception {
        return obterPrimeiroDiaMesPrimeiraHora(somarMeses(hoje, -6));
    }

    public static Date obterUltimoDiaUltimosSeisMesesUltimaHora(Date hoje) {
        return obterUltimaHora(obterUltimoDiaMes(somarMeses(hoje, -1)));
    }

    public static String formatarValorEmReal(Double valor) {
        DecimalFormat df = new DecimalFormat("0.00");
        return  df.format(valor).replace(".", ",");
    }

    public static String formatarValorEmRealSemAlterarPontuacao(Double valor) {
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(valor).replace(",", ".");
    }

    public static boolean datasMesmoDiaMesAno(Date data1, Date data2) {
        return data1 != null && data2 != null && nrDiasEntreDatas(data1, data2) == 0;
    }

    public static String removerComentariosHtml(String texto) {
//        return texto.replaceAll("(&lt;!--.*(\\n.*)+--&gt;)+", "");

        //retira primeiro se estiver codificada
        while(texto.contains("&lt;!--") && texto.contains("--&gt;") ) {
            if (texto.lastIndexOf("&lt;!--") < texto.lastIndexOf("--&gt;")) {
                texto = texto.substring(0, texto.lastIndexOf("&lt;!--")) + texto.substring(texto.lastIndexOf("--&gt;") + 6);
            } else {
                texto =  texto.substring(0, texto.lastIndexOf("&lt;!--")) + texto.substring(texto.lastIndexOf("&lt;!--") + 7);
            }
        }
        if(texto.contains("&lt;!--")){
            texto = texto.replaceAll("&lt;!--", "");
        } else if(texto.contains("--&gt;")) {

            texto = texto.replaceAll("--&gt;", "");
        }
        //depois se estiver como tag
        while(texto.contains("<!--") && texto.contains("-->") ) {
            if (texto.lastIndexOf("<!--") < texto.lastIndexOf("-->")) {
                texto = texto.substring(0, texto.lastIndexOf("<!--")) + texto.substring(texto.lastIndexOf("-->") + 3);
            } else{
                texto = texto.substring(0, texto.lastIndexOf("<!--")) + texto.substring(texto.lastIndexOf("<!--") + 4);
            }
        }
        if(texto.contains("<!--")){
            texto = texto.replaceAll("<!--", "");
        } else if(texto.contains("-->")) {
            texto = texto.replaceAll("-->", "");
        }
        return texto;
    }

    public static String join (String[] parts, String separator) {
        StringBuilder sb = new StringBuilder();
        if (parts.length > 0) {
            sb.append(parts[0]);
            for (int i = 1; i < parts.length; ++i) {
                sb.append (separator).append (parts[i]);
            }
        }
        return sb.toString();
    }

    public static String join (String[] parts) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < parts.length; ++i) {
            sb.append (parts[i]);
        }
        return sb.toString();
    }

    public static String toCamelCase (String strData, boolean keepBlanks) {
        String[] parts = strData.split("\\s+");
        for (int i = 0; i < parts.length; ++i) {
            if (parts[i].length() > 1)
                parts [i] = Character.toUpperCase(parts[i].charAt(0)) + parts[i].substring(1).toLowerCase();
            else
                parts [i] = parts[i].toUpperCase();
        }
        if (keepBlanks)
            return join (parts, " ");
        else
            return join (parts);
    }

    public class EntidadeCompatorPorCodigo implements Comparator<SuperVO> {

        public int compare(SuperVO o1, SuperVO o2) {
            return o1.getCodigo().compareTo(o2.getCodigo());
        }
    }

    public static Boolean valoresIguaisComTolerancia(Double valor1, Double valor2, Double tolerancia, int composicao){
        if(valor1.equals(valor2) || composicao != 0)
            return true;
        Double diferenca = valor1 - valor2;
        diferenca = diferenca < 0.0 ? diferenca * -1 : diferenca;
        return diferenca <= tolerancia;

    }

    public static Uteis.EntidadeCompatorPorCodigo getEntidadeCompatorPorCodigo() {
        if (entidadeCompatorPorCodigo == null) {
            entidadeCompatorPorCodigo = Uteis.getInstance().new EntidadeCompatorPorCodigo();
        }
        return entidadeCompatorPorCodigo;
    }

    public static Uteis getInstance() {
        if (uteis == null) {
            uteis = new Uteis();
        }
        return uteis;
    }

    public static java.sql.Timestamp getDataJDBCTimestamp(java.util.Date dataConverter) {
        if (dataConverter == null) {
            return null;
        }
        return new java.sql.Timestamp(dataConverter.getTime());
    }

    public static String encriptar(String senha) throws UnsupportedEncodingException {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256"); // "MD5"
            md.update(senha.getBytes());
            return converterParaHexa(md.digest());
        } catch (Exception e) {
            return "";
        }
    }

    private static String converterParaHexa(byte[] bytes) {
        StringBuilder s = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            int parteAlta = ((bytes[i] >> 4) & 0xf) << 4;
            int parteBaixa = bytes[i] & 0xf;
            if (parteAlta == 0) {
                s.append('0');
            }
            s.append(Integer.toHexString(parteAlta | parteBaixa));
        }
        return s.toString();
    }

    public static java.sql.Date getSQLData(java.util.Date dataConverter) {
        if (dataConverter == null) {
            return null;
        }
        return new java.sql.Date(dataConverter.getTime());
    }

    public static java.sql.Timestamp getDataHoraJDBC(java.util.Date dataConverter, String hora) throws Exception {
        if (dataConverter == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(dataConverter);
        if (hora.length() > 5){
            cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), Integer.valueOf(hora.substring(0, 2)), Integer.valueOf(hora.substring(3, 5)),Integer.valueOf(hora.substring(6, 8)));
        }else {
            cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), Integer.valueOf(hora.substring(0, 2)), Integer.valueOf(hora.substring(3, 5)));
        }
        return new java.sql.Timestamp(cal.getTimeInMillis());
    }

    public static java.sql.Date getDataJDBC(java.util.Date dataConverter) {
        if (dataConverter == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(dataConverter);
        return new java.sql.Date(dataConverter.getTime());
    }

    public static Double converterMoedaParaDouble(String valor) {
        return Double.parseDouble(valor.replace(".","").replace(",", "."));
    }

    /**
     * Método usado para consultas de valores monetários transformando um tipo
     * String para um valor double
     *
     * @param valorConsulta
     * @return
     * @throws Exception
     */
    public static double getObterDoubleDeValorReal(String valorConsulta) throws Exception {
        // Aceita valor com virgula, inteiro e com ponto
        String v = valorConsulta;
        DecimalFormat dff = (DecimalFormat) DecimalFormat.getInstance();
        double valorDouble = 0;
        try {
            if (!v.contains(".")) {
                valorDouble = dff.parse(v).doubleValue();
            } else {
                valorDouble = Double.parseDouble(valorConsulta);
            }

        } catch (ParseException ex) {
            throw new Exception("Para consultar coloque o valor com vírgula");
        }
        return valorDouble;
    }

    public static void setIfZeroOrNull(Supplier<Double> getter, Consumer<Double> setter, Double newValue) {
        if (getter.get() == null || getter.get() == 0.0) {
            setter.accept(newValue);
        }
    }

    public static void setIfBlank(Supplier<String> getter, Consumer<String> setter, String newValue) {
        if (org.apache.commons.lang.StringUtils.isBlank(getter.get())) {
            setter.accept(newValue);
        }
    }

    public static <T> void setIfNull(Supplier<T> getter, Consumer<T> setter, T newValue) {
        if (getter.get() == null) {
            setter.accept(newValue);
        }
    }

    public static Boolean getDiaDaSemana(Date data, DiasDaSemana diasDaSemana) throws Exception {
        if (data == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        return diasDaSemana.getDayOfWeekEmCalendar() == cal.get(Calendar.DAY_OF_WEEK);
    }

    public static String getDiaDaSemana(Date data) throws Exception {
        if (data == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        return getDiaDaSemanaApresentar(cal.get(Calendar.DAY_OF_WEEK));
    }


    public static String getDiaDaSemanaApresentar(int diaCalendar) {
        if (diaCalendar == 1) {
            return "Dom";
        } else if (diaCalendar == 2) {
            return "Seg";
        } else if (diaCalendar == 3) {
            return "Ter";
        } else if (diaCalendar == 4) {
            return "Qua";
        } else if (diaCalendar == 5) {
            return "Qui";
        } else if (diaCalendar == 6) {
            return "Sex";
        } else if (diaCalendar == 7) {
            return "Sáb";
        }
        return "";
    }

    public static List<String> getMesesStringEntreDatas(Date dataInicial, Date dataFinal) throws Exception {
        List<String> listaMeses = new ArrayList<String>();
        List<Date> mesesEntreDatas = getMesesEntreDatas(dataInicial, dataFinal);
        for (Date data : mesesEntreDatas) {
            Calendar calendarInicial = Calendario.getInstance();
            calendarInicial.setTime(data);
            int mes = calendarInicial.get(Calendar.MONTH) + 1;
            int ano = calendarInicial.get(Calendar.YEAR);
            listaMeses.add(String.format("%02d/%d", mes, ano));
        }

        return listaMeses;
    }

    public static String getDataString(String data) throws Exception{
        if(data == null || data.trim().equals("")){
            return "";
        } else {
            return getData(getDate(data,"yyyy-MM-dd hh:mm:ss"));
        }
    }

    public static List<Date> getMesesEntreDatas(Date dataInicial, Date dataFinal) throws Exception {
        if (dataInicial.getTime() > dataFinal.getTime()) {
            return null;
        }

        Calendar calendarInicial = Calendario.getInstance();
        calendarInicial.setTime(dataInicial);
        Calendar calendarFinal = Calendario.getInstance();
        calendarFinal.setTime(dataFinal);
        List<Date> listaMeses = new ArrayList<Date>();

        while (calculaIntegerData(calendarInicial) <= calculaIntegerData(calendarFinal)) {
            Date data = calendarInicial.getTime();
            // WM - 23/12/2010
            // Exemplo: data inicial - 02/11/2010 data final 04/02/2010
            // a lista de meses teria: novembro: 02/11 - 30/11
            // dezembro: 01/12 - 31/12
            // janeiro: 01/01 - 30/01
            // fevereiro:01/02 - 04/02
            if (listaMeses.isEmpty()) {
                listaMeses.add(getDataComHoraZerada(data));
            } else {
                Date dataInicioNova = getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(data));
                listaMeses.add(dataInicioNova);
                calendarInicial.setTime(dataInicioNova);
            }
            calendarInicial.add(Calendar.MONTH, 1);
        }

        return listaMeses;
    }

    public static List<Date> getMesesEntreDatasAleatorias(Date dataInicial, Date dataFinal) throws Exception {
        if (dataInicial.getTime() > dataFinal.getTime()) {
            return null;
        }

        Calendar calendarInicial = Calendario.getInstance();
        calendarInicial.setTime(dataInicial);
        Calendar calendarFinal = Calendario.getInstance();
        calendarFinal.setTime(dataFinal);
        List<Date> listaMeses = new ArrayList<Date>();

        int aux = 0;
        while (calculaIntegerData(calendarInicial) <= calculaIntegerData(calendarFinal)) {
            Date data = calendarInicial.getTime();
            data = (aux++ > 0 ? obterPrimeiroDiaMes(data) : data);
            listaMeses.add(getDataComHoraZerada(data));
            calendarInicial.add(Calendar.MONTH, 1);
        }

        return listaMeses;
    }

    public static void setInt(PreparedStatement ps, Integer valor, int index) throws SQLException {
        if(valor == null){
            ps.setNull(index, Types.NULL);
        }else{
            ps.setInt(index, valor);
        }
    }

    public static boolean isInteger(String s){
        try{
            Integer.parseInt(s);
        }catch (Exception e){
            return false;
        }

        return true;
    }

    private static int calculaIntegerData(Calendar data) { // mmYYYY
        return (data.get(Calendar.MONTH) + 1) + (data.get(Calendar.YEAR) * 100);
    }

    /**
     * @param dataInicial Data inicial do período
     * @param dataFinal   Data final do período
     * @return A diferença, em mêses, entre as duas datas
     */
    public static Integer getQuantidadeMesesEntreDatas(Date dataInicial, Date dataFinal) {
        final Calendar calendarioInicial = Calendar.getInstance();
        final Calendar calendarioFinal = Calendar.getInstance();

        calendarioInicial.setTime(dataInicial);
        calendarioFinal.setTime(dataFinal);

        final int diferencaAnos = calendarioFinal.get(Calendar.YEAR) - calendarioInicial.get(Calendar.YEAR);
        return diferencaAnos * 12 + calendarioFinal.get(Calendar.MONTH) - calendarioInicial.get(Calendar.MONTH);
    }

    public static List<PeriodoMensal> getPeriodosMensaisEntreDatas(Date dataInicial, Date dataFinal) throws Exception {
        List<PeriodoMensal> listaPeriodoMensal = new ArrayList<PeriodoMensal>();
        List<Date> listaDatasIniciais = getMesesEntreDatas(dataInicial, dataFinal);
        for (Date data : listaDatasIniciais) {
            PeriodoMensal periodoMensal = new PeriodoMensal(data, obterUltimoDiaMesUltimaHora(data));
            listaPeriodoMensal.add(periodoMensal);
        }
        listaPeriodoMensal.get(0).setDataInicio(dataInicial);
        listaPeriodoMensal.get(listaPeriodoMensal.size() - 1).setDataTermino(dataFinal);

        return listaPeriodoMensal;
    }

    public static List<Date> getMesesEntreDatasFinalMes(Date dataInicial, Date dataFinal) throws Exception {
        Calendar calendarInicial = Calendario.getInstance();
        calendarInicial.setTime(obterUltimoDiaMesUltimaHora(dataInicial));
        Calendar calendarFinal = Calendario.getInstance();
        calendarFinal.setTime(obterPrimeiroDiaMes(dataFinal));
        List<Date> listaMeses = new ArrayList<Date>();

        while ((calendarInicial.get(Calendar.YEAR) != calendarFinal.get(Calendar.YEAR))
                || (calendarInicial.get(Calendar.MONTH) != (calendarFinal.get(Calendar.MONTH) + 1))) {
            Date data = calendarInicial.getTime();
            listaMeses.add(data);
            calendarInicial.add(Calendar.MONTH, 1);
        }

        return listaMeses;
    }

    /**
     * @deprecated Substituído por Calendario.getDataComHoraZerada
     * @param data
     * @return
     */
    public static Date getDataComHoraZerada(Date data) {
        Calendar calendar = Calendario.getInstance(data);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    /**
     * @deprecated ultilizar getDataComUltimaHora(Date data)
     * @see Calendario
     * @param data
     * @return
     */
    public static Date getDataComUltimaHora(Date data) {
        Calendar calendar = Calendario.getInstance(data);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return calendar.getTime();
    }

    public static String getURLLogin() {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        StringBuffer urlAplicacao = request.getRequestURL();
        String urlLogin = urlAplicacao.toString();
        urlLogin = urlLogin.substring(0, urlAplicacao.lastIndexOf("/"));
        return urlLogin;
    }

    public static String getDataFormatoBD(java.util.Date dataConverter) {
        return getData(dataConverter, "bd");
    }

    public static Timestamp getTimestamp() {
        return Timestamp.valueOf(getTimestampFormatoBD());
    }

    public static String getTimestampFormatoBD() {
        return getData(new Date(), "bdtimestamp");
    }

    public static String getDataFormatoBD() {
        return getData(new Date(), "bd");
    }

    public static String getData(java.util.Date dataConverter, String padrao) {
        if (dataConverter == null) {
            return ("");
        }
        String dataStr;
        if (padrao.equals("bdtimestamp")) {
            SimpleDateFormat formatador = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss", Calendario.getDefaultLocale());
            dataStr = formatador.format(dataConverter);
        }else if (padrao.equals("bd")) {

            SimpleDateFormat formatador = new SimpleDateFormat("yyyy-MM-dd", Calendario.getDefaultLocale());
            dataStr = formatador.format(dataConverter);
        } else if (padrao.equals("br")) {
            SimpleDateFormat formatador = new SimpleDateFormat("dd/MM/yyyy", Calendario.getDefaultLocale());
            dataStr = formatador.format(dataConverter);
        } else {
            DateFormat formatador = DateFormat.getDateInstance(DateFormat.SHORT, Calendario.getDefaultLocale());
            dataStr = formatador.format(dataConverter);
        }
        return (dataStr);
    }

    /** @param @Date
     * @return String formatada
     *
     * @deprecated Utilizar Classe Calendario que possui este metodo.
     */
    @Deprecated
    public static String getDataAplicandoFormatacao(Date data, String mascara) {
        if (data == null) {
            return "";
        }
        SimpleDateFormat formatador = new SimpleDateFormat(mascara);
        String dataStr = formatador.format(data);
        return dataStr;
    }

    public static String arrendondarForcando2CadasDecimaisComVirgula(double valor) {
        return arrendondarForcando2CadasDecimaisComVirgula(valor, false);
    }

    public static String arrendondarForcando2CadasDecimaisComVirgula(double valor, boolean manterSinalNegativo) {
        valor = Uteis.arredondar(valor, 2, 1);
        String valorStr = String.valueOf(valor);

        String inteira = valorStr.substring(0, valorStr.indexOf("."));
        String extensao = valorStr.substring(valorStr.indexOf(".") + 1, valorStr.length());
        if (extensao.length() == 1) {
            extensao += "0";
        }

        if (manterSinalNegativo) {
            valorStr = inteira + "," + extensao;
        } else {
            valorStr = Uteis.removerMascara(inteira) + "," + extensao;
        }
        return valorStr;
    }

    /**
     * Apenas para valores até 999.999,99
     * @param valor
     * @return
     */
    public static String formataDuasCasasDecimaisEPontoCasasMilenio(double valor) {
        String retorno = arrendondarForcando2CadasDecimaisComVirgula(valor).trim();
        if (null != retorno && retorno.length() > 6) {
            retorno = retorno.substring(0, retorno.length() - 6) + "." + retorno.substring(retorno.length() - 6);
        }
        return retorno;
    }

    public static String formataDuasCasasDecimaisEPontoCasasMilenioMantendoSinal(double valor) {
        String retorno = arrendondarForcando2CadasDecimaisComVirgula(valor, true).trim();
        if (null != retorno && retorno.length() > 6) {
            retorno = retorno.substring(0, retorno.length() - 6) + "." + retorno.substring(retorno.length() - 6);
        }
        return retorno;
    }

    public static double arredondarForcando2CasasDecimais(double valor) {
        valor = Uteis.arredondar(valor, 2, 1);
        String valorStr = String.valueOf(valor);

        String inteira = valorStr.substring(0, valorStr.indexOf("."));
        String extensao = valorStr.substring(valorStr.indexOf(".") + 1, valorStr.length());
        if (extensao.length() == 1) {
            extensao += "0";
        }
        valorStr = Uteis.removerMascara(inteira) + "." + extensao;
        return Double.parseDouble(valorStr);
    }

    public static Double naoArredondar(double valor){
        DecimalFormat df = new DecimalFormat("#.##");
        df.setRoundingMode(RoundingMode.DOWN);
        df.setMaximumFractionDigits(2);
        return new Double(df.format(valor).replaceAll(",","."));
    }

    public static Double arrendondar(Double valor){
        return Math.round(valor * 100.0) / 100.0;
    }

    public static String naoArredondarRetString(double valor) {
        DecimalFormat df = new DecimalFormat("#.##");
        df.setRoundingMode(RoundingMode.DOWN);
        df.setMaximumFractionDigits(2);
        String valorStr = String.valueOf(new Double(df.format(valor).replaceAll(",",".")));
        String inteira = valorStr.substring(0, valorStr.indexOf("."));
        String extensao = valorStr.substring(valorStr.indexOf(".") + 1, valorStr.length());
        if (extensao.length() == 1) {
            extensao += "0";
        }
        return inteira + "," + extensao;
    }

    public static Double arredondarParaCima(double valor) {
        DecimalFormat df = new DecimalFormat("#.###");
        df.setRoundingMode(RoundingMode.UP);
        return new Double(df.format(valor).replaceAll(",", "."));
    }

    public static Double arredondar(double valor, RoundingMode mode,
            final int precisao) {
        String mascara = "#";
        for (int i = 0; i < precisao; i++) {
            if (i == 0) {
                mascara += ".";
            }
            mascara += "#";
        }
        DecimalFormat df = new DecimalFormat(mascara);
        df.setRoundingMode(mode);
        return new Double(df.format(valor).replaceAll(",", "."));
    }

    public static Double arredondarMod10(double valor) {
        double result = valor;
        double mod10 = valor % 10;
        if (mod10 > 5) {
            mod10 = 10 - mod10;
            result = result + mod10;
        } else {
            result = result - mod10;
        }
        return result;
    }

    public static double arredondarForcando2CasasDecimaisMantendoSinal(double valor) {
        valor = Uteis.arredondar(valor, 2, 1);
        String valorStr = String.valueOf(valor);

        String inteira = valorStr.substring(0, valorStr.indexOf("."));
        String extensao = valorStr.substring(valorStr.indexOf(".") + 1, valorStr.length());
        if (extensao.length() == 1) {
            extensao += "0";
        }
        String sinal = inteira.charAt(0) == '-' ? "-" : "";
        valorStr = sinal + Uteis.removerMascara(inteira) + "." + extensao;
        return Double.parseDouble(valorStr);
    }

    /*
     * Defini-se a mascará a ser aplicada a data atual de acordo com o padrão - dd/mm/yyyy ou MM.yy.dd e assim por diante
     */
    public static String getDataAtualAplicandoFormatacao(String mascara) {
        Date hoje = negocio.comuns.utilitarias.Calendario.hoje();
        return getDataAplicandoFormatacao(hoje, mascara);
    }

    public static String formataComHorarioSeDiferenteZero(java.util.Date data) {
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        int hora = cal.get(Calendar.HOUR_OF_DAY);
        int minuto = cal.get(Calendar.MINUTE);
        int segundo = cal.get(Calendar.SECOND);

        if(hora != 0 || minuto != 0 || segundo != 0){
            try {
                SimpleDateFormat simpleDateFormatormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
                return simpleDateFormatormat.format(data);
            } catch (Exception e) {
                return "";
            }
        }else{
            return (getData(data, "br"));
        }
    }

    public static String getData(java.util.Date dataConverter) {
        return (getData(dataConverter, "br"));
    }

    public static java.util.Date getDate(String data) throws Exception {
        java.util.Date valorData = null;
        DateFormat formatador = DateFormat.getDateInstance(DateFormat.SHORT, Calendario.getDefaultLocale());
        valorData = formatador.parse(data);
        Calendar cal = Calendario.getInstance();
        cal.setTime(negocio.comuns.utilitarias.Calendario.hoje());
        int hora = cal.get(Calendar.HOUR_OF_DAY);
        int minuto = cal.get(Calendar.MINUTE);
        int segundo = cal.get(Calendar.SECOND);

        cal.setTime(valorData);
        cal.set(Calendar.HOUR_OF_DAY, hora);
        cal.set(Calendar.MINUTE, minuto);
        cal.set(Calendar.SECOND, segundo);

        return cal.getTime();
    }

    public static java.util.Date getDate(String data, Locale local) throws Exception {
        java.util.Date valorData = negocio.comuns.utilitarias.Calendario.hoje();
        if (local == null) {
            DateFormat formatador = DateFormat.getDateInstance(DateFormat.SHORT, Calendario.getDefaultLocale());
            valorData = formatador.parse(data);
        } else {
            DateFormat formatador = DateFormat.getDateInstance(DateFormat.SHORT, local);
            valorData = formatador.parse(data);
        }
        return valorData;
    }

    public static java.util.Date getDateTime(String data, Locale local) throws Exception {
        java.util.Date valorData = negocio.comuns.utilitarias.Calendario.hoje();
        if (local == null) {
            DateFormat formatador = DateFormat.getDateTimeInstance(DateFormat.SHORT,
                    DateFormat.SHORT, Calendario.getDefaultLocale());
            valorData = formatador.parse(data);
        } else {
            DateFormat formatador = DateFormat.getDateTimeInstance(DateFormat.SHORT,
                    DateFormat.SHORT,
                    local);
            valorData = formatador.parse(data);
        }
        return valorData;
    }

    public static String getAnoDataAtual() {
        Date hoje;
        String hojeStr;
        DateFormat formatador;
        formatador = DateFormat.getDateInstance(DateFormat.SHORT, Calendario.getDefaultLocale());
        hoje = negocio.comuns.utilitarias.Calendario.hoje();
        hojeStr = formatador.format(hoje);
        return (hojeStr.substring(hojeStr.lastIndexOf("/") + 1));
    }

    public static String getDataAtual() {
        Date hoje = negocio.comuns.utilitarias.Calendario.hoje();
        return (Uteis.getData(hoje));
    }

    public static String getHoraAtual() {
        Date hoje;
        DateFormat formatador;
        formatador = DateFormat.getTimeInstance(DateFormat.SHORT, Calendario.getDefaultLocale());
        hoje = negocio.comuns.utilitarias.Calendario.hoje();

        String horaStr;
        horaStr = formatador.format(hoje);
        return (horaStr);
    }

    public static Date getDateTime(Date data, int hora, int minuto, int segundo) {
        Calendar cal = Calendar.getInstance(Calendario.getDefaultLocale());
        cal.setTime(data);
        cal.set(Calendar.HOUR_OF_DAY, hora);
        cal.set(Calendar.MINUTE, minuto);
        cal.set(Calendar.SECOND, segundo);

        return cal.getTime();
    }

    public static String gethoraHHMMSS(Date data) {
        Calendar cal = Calendar.getInstance(Calendario.getDefaultLocale());
        cal.setTime(data);
        String hora = String.valueOf(cal.get(Calendar.HOUR_OF_DAY) < 10 ? "0" + cal.get(Calendar.HOUR_OF_DAY) : cal.get(Calendar.HOUR_OF_DAY));
        String minuto = String.valueOf(cal.get(Calendar.MINUTE) < 10 ? "0" + cal.get(Calendar.MINUTE) : cal.get(Calendar.MINUTE));
        String segundo = String.valueOf(cal.get(Calendar.SECOND) < 10 ? "0" + cal.get(Calendar.SECOND) : cal.get(Calendar.SECOND));

        return hora + minuto + segundo;
    }

    public static boolean informouSomenteData(String dataComMascara)throws Exception{
        // exemplo da mascara: 10/12/2017 - __:__:__
        String hora = dataComMascara.substring(13, 15);
        return hora.equals("__");
    }

    public static String obterSomenteData(String dataComMascara)throws Exception{
        // exemplo da mascara: 10/12/2017 - __:__:__
        return dataComMascara.substring(0, 10);
    }

    public static void validarDataHoraComMascara(String dataComMascara, String nomeCampo)throws Exception{
           // exemplo da mascara: 10/12/2017 - 10:12:15
        String erroPadrao = "A data " + nomeCampo + " está incorreta. ";
        Integer dia = Integer.parseInt(dataComMascara.substring(0,2));
        Integer mes =  Integer.parseInt(dataComMascara.substring(3,5));
        Integer ano = Integer.parseInt(dataComMascara.substring(6,10));
        Integer hora = Integer.parseInt(dataComMascara.substring(13,15));
        Integer min = Integer.parseInt(dataComMascara.substring(16,18));
        Integer seg =  Integer.parseInt(dataComMascara.substring(19,21));
        if ((seg < 00) || (seg > 59)) {
            throw new ConsistirException(erroPadrao + "Os segundos especificados não são válidos.");
        }
        if ((min < 00) || (min > 59)) {
            throw new ConsistirException(erroPadrao + "Os minutos especificados não são válidos.");
        }

        if ((hora < 00) || (hora > 23)) {
            throw new ConsistirException(erroPadrao + "A hora especificada não é valida.");
        }

        if ((ano < 1900) || (ano > 2099)) {
            throw new ConsistirException(erroPadrao + "O ano especificado não é valido.");
        }
        if ((mes <= 0) || (mes > 12)) {
            throw new ConsistirException(erroPadrao + "O mês especificado não é valido.");
        }
        if (dia <= 0) {
            throw new ConsistirException(erroPadrao + "O dia especificado não é valido.");
        }
        if ((mes == 1 || mes == 3 || mes == 5 || mes == 7 || mes == 8 || mes == 10 || mes == 12) && (dia > 31)) {
            throw new ConsistirException(erroPadrao + "O mês especificado contém no máximo 31 dias.");
        } else if ((mes == 4 || mes == 6 || mes == 9 || mes == 11) && (dia > 30)) {
            throw new ConsistirException(erroPadrao + "O mês especificado contém no máximo 30 dias.");
        } else {
            if ((ano%4!=0) && (mes == 2) && (dia>28)) {
                throw new ConsistirException(erroPadrao + "O mês especificado contém no máximo 28 dias.");
            } else{
                if ((ano%4==0) && (mes == 2) && (dia>29)) {
                    throw new ConsistirException(erroPadrao + "O mês especificado contém no máximo 29 dias.");
                }
            }
        }
    }

    public static String gethoraHHMMSSFormatado(Date data) {
        Calendar cal = Calendar.getInstance(Calendario.getDefaultLocale());
        cal.setTime(data);
        String hora = String.valueOf(cal.get(Calendar.HOUR_OF_DAY) < 10 ? "0" + cal.get(Calendar.HOUR_OF_DAY) : cal.get(Calendar.HOUR_OF_DAY));
        String minuto = String.valueOf(cal.get(Calendar.MINUTE) < 10 ? "0" + cal.get(Calendar.MINUTE) : cal.get(Calendar.MINUTE));
        String segundo = String.valueOf(cal.get(Calendar.SECOND) < 10 ? "0" + cal.get(Calendar.SECOND) : cal.get(Calendar.SECOND));

        return hora + ":" + minuto + ":" + segundo;
    }

    public static String gethoraHHMMFormatado(Date data) {
        Calendar cal = Calendar.getInstance(Calendario.getDefaultLocale());
        cal.setTime(data);
        String hora = String.valueOf(cal.get(Calendar.HOUR_OF_DAY) < 10 ? "0" + cal.get(Calendar.HOUR_OF_DAY) : cal.get(Calendar.HOUR_OF_DAY));
        String minuto = String.valueOf(cal.get(Calendar.MINUTE) < 10 ? "0" + cal.get(Calendar.MINUTE) : cal.get(Calendar.MINUTE));

        return hora + ":" + minuto;
    }


    public static String getDataComHora(Date data) {
        return getData(data) + " - " + gethoraHHMMSSAjustado(data);

    }

    public static String getDataBDComHHMM(Date data) {
        return getData(data, "bd") + " " + gethoraHHMMAjustado(data);

    }

    public static String getDataComHHMM(Date data) {
        return getData(data) + " - " + gethoraHHMMAjustado(data);

    }

    public static String getDataComHHMMSS(Date data) {
        return getData(data) + " - " + gethoraHHMMSSAjustado(data);
    }

    public static Integer gethoraHH(Date data) {
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        Integer hora = cal.get(Calendar.HOUR_OF_DAY);
        return hora;
    }

    public static Integer getMinutos(Date data) {
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        Integer hora = cal.get(Calendar.MINUTE);
        return hora;
    }

    public static double arredondar(double valor, int casas, int abaixo) {
        try {
            valor = (new BigDecimal(valor).setScale(casas, BigDecimal.ROUND_HALF_UP)).doubleValue();
            return valor;
        }catch (NumberFormatException nb){
            return 0.00D;
        }
    }

    public static long nrDiasEntreDatas(Date dataInicial, Date dataFinal) {
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        long dias = 0;
        try {
            //Resolve problema de datas dentro de horário de verão   
            df.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date dataIni = df.parse(df.format(Calendario.getDataComHoraZerada(dataInicial)));
            Date dataFin = df.parse(df.format(Calendario.getDataComHoraZerada(dataFinal)));

            dias = (dataFin.getTime() - dataIni.getTime()) / (Uteis.UM_DIA);
        } catch (Exception e) {
        }
        return dias;
    }

    public static long nrDiasEntreDatasSemHoraZerada(Date dataInicial, Date dataFinal) {
        Date dataIni = dataInicial;
        Date dataFin = dataFinal;
        long dias = (dataFin.getTime() - dataIni.getTime()) / (Uteis.UM_DIA);
        return dias;
    }

    /**
     * Calcula o tempo em minutos entre
     * <code>dataInicial</code> e
     * <code>dataFinal</code>.
     *
     * @param dataInicial Data inicial.
     * @param dataFinal Data final.
     * @return Quantidade de minutos entre as duas datas.
     * <AUTHOR>
     */
    public static long minutosEntreDatas(Date dataInicial, Date dataFinal) {
        Calendar calendar = Calendario.getInstance();
        calendar.setTime(dataFinal);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        long mins = calendar.getTime().getTime() / (1000 * 60);

        calendar = Calendario.getInstance();
        calendar.setTime(dataInicial);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        long minsFinal = calendar.getTime().getTime() / (1000 * 60);

        return mins - minsFinal;
//		return mins - (calendar.getTime().getTime() / (1000 * 60));
    }

    /**
     * Soma algum campo de uma Data. Ex.:
     * somarCampo(negocio.comuns.utilitarias.Calendario.hoje(),
     * Calendar.DAY_OF_MONTH, 5);
     *
     * @param data Data na qual o campo será somado.
     * @param campo O campo do calendário.
     * @param quantidade A quantidade de tempo a ser acrescentada no campo.
     * @return Data com o campo somado.
     * <AUTHOR>
     */
    public static Date somarCampoData(Date data, Integer campo, int quantidade) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(campo, quantidade);

        return cal.getTime();
    }

    public static Date obterDataFutura(Date dataInicial, long nrDiasProgredir) {
        long dataEmDias = dataInicial.getTime() / (Uteis.UM_DIA);
        dataEmDias = dataEmDias + nrDiasProgredir;
        Date dataFutura = new Date(dataEmDias * (Uteis.UM_DIA));
        return dataFutura;
    }

    public static Date obterDataAnterior(Integer mes) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(negocio.comuns.utilitarias.Calendario.hoje());
        dataCalendar.add(Calendar.MONTH, -mes);
        return dataCalendar.getTime();
    }

    public static Date obterDataAnterior(Date dataInicial, int nrDiasProgredir) {
        Date dataIni = getDataComHoraZerada(dataInicial);
        Calendar c = Calendario.getInstance(dataIni);
        c.add(Calendar.DATE, nrDiasProgredir * (-1));
        return getDataComHoraZerada(c.getTime());
    }

    /**
     * incrementa a qtde de dias do parametro na data informada
     *
     * @param dataInicial
     * @param nrDiasProgredir
     * @return
     */
    public static Date obterDataFutura2(Date dataInicial, int nrDiasProgredir) {
        Calendar dataCalendar = Calendario.getInstance();
        Date dataIni = getDataComHoraZerada(dataInicial);
        dataCalendar.setTime(dataIni);

        dataCalendar.add(Calendar.DAY_OF_MONTH, nrDiasProgredir);
        return dataCalendar.getTime();
    }

    /**
     * Incrementa a qtde de meses informada na data atual
     *
     * @param dataInicial
     * @param nrMesesProgredir
     * @return
     */
    public static Date obterDataFutura3(Date dataInicial, int nrMesesProgredir) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataInicial);
        dataCalendar.add(Calendar.MONTH, nrMesesProgredir);
        return dataCalendar.getTime();
    }

    public static int obterNumeroDiasDoMes(Date dataInicial) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataInicial);
        int numeroDias = dataCalendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        return numeroDias;
    }

    public static String obterPrimeiroNomeConcatenadoSobreNome(String nome) {
        try {
            List<String> preposicoes2 = new ArrayList();
            preposicoes2.add("dos");
            preposicoes2.add("das");
            preposicoes2.add("de");
            preposicoes2.add("da");
            preposicoes2.add("e");
            preposicoes2.add("a");
            preposicoes2.add("i");
            preposicoes2.add("o");
            String[] listaNome = nome.trim().split(" ");
            StringBuilder resultado = new StringBuilder();
            for (int i = 0; i < listaNome.length; i++) {
                String s = listaNome[i];
                if (s.trim().isEmpty()) {
                    continue;
                }
                if (i == 0) {
                    resultado.append(s);
                } else if (preposicoes2.contains(s)) {
                    resultado.append("");
                } else {
                    resultado.append(s.subSequence(0, 1)).append(".");
                }
                resultado.append(" ");
            }
            return resultado.toString();
        } catch (Exception e) {
            // TODO: handle exception
        }
        return "";
    }

    // public static void main(String[] args) throws Exception {
    // String x = "aaaaa[(10){}Codigo_Modalidade,aaaaaaa[(10){}Codigo_Modalidade,aaaaa";
    // String tag = "[(10){}Codigo_Modalidade,";
    // String x2 = x.substring(0, x.indexOf(tag)+tag.length()+2);
    // x2 = x2.replace(tag, "X" );
    // x = x2 + x.substring(x.indexOf(tag)+tag.length()+2);
    // System.out.print(x);
    // }
    //
    // }
    // // public static Date obterDataFuturaParcela(Date dataInicial) throws Exception {
    // if (dataInicial == null) {
    // return null;
    // }
    // int nrMesesProgredir = 1;
    //
    // int dia = Uteis.getDiaMesData(dataInicial);
    // int mes = Uteis.getMesData(dataInicial);
    // int ano = Uteis.getAnoData(dataInicial);
    //
    // // // PROGREDINDO OS ANOS
    // // if (nrMesesProgredir > 12) {
    // // while (nrMesesProgredir > 12) {
    // // ano++;
    // // nrMesesProgredir += -12;
    // // }
    // // }
    //
    // // PROGREDINDO OS MESES
    // mes += nrMesesProgredir;
    // if (mes > 12) {
    // mes -= 12;
    // ano++;
    // }
    //
    // if (dia > 30) {
    // dia = 1;
    // mes++;
    // } else {
    // // CASO MES SEJA FEVEREIRO
    // if ((dia == 29) && (mes == 2)) {
    // dia = 1;
    // mes++;
    // }
    // }
    //
    // Date dataFutura = Uteis.getDate(dia + "/" + mes + "/" + ano);
    // return dataFutura;
    // }
    public static Date obterDataFuturaParcela(Date dataInicial, int mesAtual) throws Exception {
        // if (dataInicial == null) {
        // return null;
        // }
        // int nrMesesProgredir = 1;
        //
        // int dia = Uteis.getDiaMesData(dataInicial);
        // int mes = mesAtual;
        // int ano = Uteis.getAnoData(dataInicial);
        //
        // // PROGREDINDO OS MESES
        // mes += nrMesesProgredir;
        // if (mes > 12) {
        // mes -= 12;
        // ano++;
        // }
        //
        // if (dia > 30) {
        // // se o resto da divisao do numero do mês for igual a 1 o mês é impar e se o mes for igual ou menor que 7: (mes 1, 3 , 5, 7)
        // //ou se o resto da divisao do numero do mês for igual a zero e o mês for maior que 8 e menor que 12: (mes 8, 10, 12) significa
        // que o Mês tem 31 dias
        // if ((mes % 2 == 1 && mes <= 7) || (mes % 2 == 0 && (mes >= 8 && mes <= 12))) {
        // dia = 31;
        // } else {
        // dia = 30;
        // }
        // //mes++;
        // if (mes == 2) {
        // Date dataFutura = Uteis.getDate("01/02/" + ano);
        // return dataFutura = obterUltimoDiaMesUltimaHora(dataFutura);
        // }
        // } else {
        // // CASO MES SEJA FEVEREIRO
        // if ((dia >= 29) && (mes == 2)) {
        // // dia = 1;
        // // mes++;
        // Date dataFutura = Uteis.getDate("01/02/" + ano);
        // return dataFutura = obterUltimoDiaMesUltimaHora(dataFutura);
        //
        // }
        // }
        //
        // Date dataFutura = Uteis.getDate(dia + "/" + mes + "/" + ano);
        // return dataFutura;
        GregorianCalendar gc = new GregorianCalendar();
        gc.setTime(dataInicial);
        gc.add(GregorianCalendar.MONTH, mesAtual);
        return gc.getTime();
    }

    public static String getMesReferenciaData(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);

        int ano = dataCalendar.get(Calendar.YEAR);
        int mes = dataCalendar.get(Calendar.MONTH) + 1;

        String mesStr = String.valueOf(mes);
        if (mesStr.length() == 1) {
            mesStr = "0" + mesStr;
        }
        String mesRef = mesStr + "/" + ano;
        return mesRef;
    }

    public static String getAnoMesReferenciaData(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);

        int ano = dataCalendar.get(Calendar.YEAR);
        int mes = dataCalendar.get(Calendar.MONTH) + 1;

        String mesStr = String.valueOf(mes);
        if (mesStr.length() == 1) {
            mesStr = "0" + mesStr;
        }
        return ano + mesStr;
    }

    public static String getMesReferencia(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);

        int mes = dataCalendar.get(Calendar.MONTH) + 1;

        String mesStr = String.valueOf(mes);
        if (mesStr.length() == 1) {
            mesStr = "0" + mesStr;
        }
        String mesRef = mesStr;
        return mesRef;
    }

    public static String getMesNomeReferencia(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);
        int mes = dataCalendar.get(Calendar.MONTH);
        return (mes == 0 ? "Janeiro"
                : mes == 1 ? "Fevereiro"
                : mes == 2 ? "Março"
                : mes == 3 ? "Abril"
                : mes == 4 ? "Maio"
                : mes == 5 ? "Junho"
                : mes == 6 ? "Julho"
                : mes == 7 ? "Agosto"
                : mes == 8 ? "Setembro"
                : mes == 9 ? "Outubro"
                : mes == 10 ? "Novembro"
                : "Dezembro");
    }

    public static String getMesNomeReferenciaAbreviadoPorMes(int mes) {
        return (mes == 1 ? "Jan"
                : mes == 2 ? "Fev"
                : mes == 3 ? "Mar"
                : mes == 4 ? "Abr"
                : mes == 5 ? "Maio"
                : mes == 6 ? "Jun"
                : mes == 7 ? "Jul"
                : mes == 8 ? "Ago"
                : mes == 9 ? "Set"
                : mes == 10 ? "Out"
                : mes == 11 ? "Nov"
                : "Dez");
    }

    public static String getMesReferencia(int mes, int ano) {
        String mesStr = String.valueOf(mes);
        if (mesStr.length() == 1) {
            mesStr = "0" + mesStr;
        }
        mesStr = mesStr + "/" + String.valueOf(ano);
        return mesStr;
    }

    public static Double getGeraNumeroMesReferencia(String mesReferencia) {
        Double valor = 0.0;
        String mes = mesReferencia.substring(0, mesReferencia.indexOf("/"));
        String ano = mesReferencia.substring(mesReferencia.indexOf("/") + 1, mesReferencia.length());
        valor = Double.parseDouble(mes) + Double.parseDouble(ano);
        return valor;
    }

    public static int compareMesReferencia(String mesInicial, String mesFinal) {
        String mesInicialOrdenado = mesInicial.substring(mesInicial.indexOf("/") + 1) + mesInicial.substring(0, mesInicial.indexOf("/"));
        String mesFinalOrdenado = mesFinal.substring(mesFinal.indexOf("/") + 1) + mesFinal.substring(0, mesFinal.indexOf("/"));
        return mesInicialOrdenado.compareTo(mesFinalOrdenado);
    }

    public static String getDataDiaMesAnoConcatenado() {
        String dataAtual = Uteis.getDataAtual();
        String ano = "";
        String mes = "";
        String dia = "";
        int cont = 1;
        while (cont != 3) {
            int posicao = dataAtual.lastIndexOf("/");
            if (posicao != -1) {
                cont++;
                if (cont == 2) {
                    ano = dataAtual.substring(posicao + 1);
                    dataAtual = dataAtual.substring(0, posicao);
                } else if (cont == 3) {
                    mes = dataAtual.substring(posicao + 1);
                    dia = dataAtual.substring(0, posicao);
                }
            }
        }
        return dia + mes + ano;
    }

    public static String removerMascaraTelefone(String input) {
        if (input == null) {
            return null;
        }
        // Remove espaços, pontos, traços e parênteses
        return input.replaceAll("[\\s\\.\\-\\(\\)]", "");
    }

    public static String getDataMesAnoConcatenado() {
        // return MM/AAAA
        int mesAtual = Calendario.getInstance().get(Calendar.MONTH) + 1;
        int anoAtual = Calendario.getInstance().get(Calendar.YEAR);
        String mesAtualStr = String.valueOf(mesAtual);
        if (mesAtualStr.length() == 1) {
            mesAtualStr = "0" + mesAtualStr;
        }
        return mesAtualStr + "/" + anoAtual;
        /*
         * String dataAtual = Uteis.getDataAtual(); String ano = ""; String mes = ""; int cont = 1; while (cont != 3) { int posicao =
         * dataAtual.lastIndexOf("/"); if (posicao != -1) { cont++; if (cont == 2) { ano = dataAtual.substring(posicao + 1); dataAtual =
         * dataAtual.substring(0, posicao); } else if (cont == 3) { mes = dataAtual.substring(posicao + 1); } } } return (mes + "/" + ano);
         */
    }

    public static String getDataMesAnoConcatenado(Date data) {
          // return MM/AAAA
        int mesAtual = data.getMonth() + 1;
        int anoAtual = data.getYear() + 1900;
        String mesAtualStr = String.valueOf(mesAtual);
        if (mesAtualStr.length() == 1) {
            mesAtualStr = "0" + mesAtualStr;
        }
        return mesAtualStr + "/" + anoAtual;
    }

    public static String removerMascara(String campo) {
        if (UteisValidacao.emptyString(campo)) {
            return "";
        }

        String campoSemMascara = "";
        for (int i = 0; i < campo.length(); i++) {
            if ((campo.charAt(i) != ',') && (campo.charAt(i) != '.') && (campo.charAt(i) != '-') && (campo.charAt(i) != ':')
                    && (campo.charAt(i) != '/')) {
                campoSemMascara = campoSemMascara + campo.substring(i, i + 1);
            }
        }
        return campoSemMascara;
    }

    public static String aplicarMascara(String dado, String mascara) {
        if (dado == null) {
            return dado;
        }
        if (dado.equals("")) {
            return dado;
        }
        if (dado.length() == mascara.length()) {
            return dado;
        }
        dado = removerMascara(dado);
        int posDado = 0;
        String dadoComMascara = "";
        for (int i = 0; i < mascara.length(); i++) {
            if (posDado >= dado.length()) {
                break;
            }
            String caracter = mascara.substring(i, i + 1);
            if (caracter.equals("9")) {
                dadoComMascara = dadoComMascara + dado.substring(posDado, posDado + 1);
                posDado++;
            } else {
                dadoComMascara = dadoComMascara + caracter;
            }
        }
        return dadoComMascara;
    }

    public static String getDoubleFormatado(double valor) {
        NumberFormat nf = NumberFormat.getInstance();
        nf.setMinimumFractionDigits(2);
        nf.setMaximumFractionDigits(2);
        return nf.format(valor);
    }

    public static int getDiaMesData(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);

        return dataCalendar.get(Calendar.DAY_OF_MONTH);
    }

    public static int getMesData(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);

        return dataCalendar.get(Calendar.MONTH) + 1;
    }

    public static int getAnoData(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);

        return dataCalendar.get(Calendar.YEAR);
    }

    public static Date obterPimeiroDiaMesAnteriorPrimeiraHora(Date dataPrm) throws Exception {
        return obterPrimeiraHora(obterPrimeiroDiaMes(somarDias(obterPrimeiroDiaMes(Calendario.hoje()), -1)));
    }

    private static Date obterPrimeiraHora(Date date) {
        Calendar c = Calendario.getInstance();
        c.setTime(date);

        c.set(Calendar.AM_PM, 0);
        c.set(Calendar.HOUR, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);

        return c.getTime();
    }

    private static Date obterUltimaHora(Date date) {
        Calendar c = Calendario.getInstance();
        c.setTime(date);

        c.set(Calendar.AM_PM, 0);
        c.set(Calendar.HOUR, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);

        return c.getTime();
    }

    public static Date obterPrimeiroDiaMesPrimeiraHora(Date dataPrm) throws Exception {
        return obterPrimeiraHora(obterPrimeiroDiaMes(dataPrm));
    }

    public static Date obterPrimeiroDiaMes(Date dataPrm) throws Exception {
        if (dataPrm == null) {
            return null;
        }
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);
        int mes = dataCalendar.get(Calendar.MONTH);
        int ano = dataCalendar.get(Calendar.YEAR);

//        DecimalFormat df = new DecimalFormat("00");
//        String mesFormatado = df.format(mes + 1);

//        return getDate(mesFormatado + "/01/" + ano);
        return getDate("01/" + (mes + 1) + "/" + ano);
    }

    public static Date obterUltimoDiaMesAnteriorUltimaHora(Date data) throws Exception {
        return obterUltimaHora(somarDias(obterPrimeiroDiaMes(data), -1));
    }

    public static Date obterUltimoDiaMes(Date data) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(data);

        int dia = obterNumeroDiasDoMes(data);
        dataCalendar.set(Calendar.DAY_OF_MONTH, dia);
        return dataCalendar.getTime();
    }

    public static Date obterUltimoDiaMesUltimaHora(Date dataPrm) throws Exception {
        Date data = obterPrimeiroDiaMes(dataPrm);
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(data);

        dataCalendar.add(Calendar.MONTH, 1);
        dataCalendar.set(Calendar.AM_PM, 0);
        dataCalendar.set(Calendar.HOUR, 0);
        dataCalendar.set(Calendar.MINUTE, 0);
        dataCalendar.set(Calendar.SECOND, 0);
        dataCalendar.set(Calendar.MILLISECOND, 0);

        dataCalendar.add(Calendar.SECOND, -1);

        return dataCalendar.getTime();
    }

    public static String getValorMonetarioParaIntegracao_SemPontoNemVirgula(double valor) {
        String valorStr = String.valueOf(valor);

        String inteira = valorStr.substring(0, valorStr.indexOf("."));
        String extensao = valorStr.substring(valorStr.indexOf(".") + 1, valorStr.length());
        if (extensao.length() == 1) {
            extensao += "0";
        }
        valorStr = Uteis.removerMascara(inteira + extensao);
        return valorStr;
    }

    public static int obterDiaData(Date dataPrm) {
        if (dataPrm == null) {
            return 0;
        }
        return Calendario.getInstance(dataPrm).get(Calendar.DAY_OF_MONTH);
    }

    public static String obterDiaSemanaData(Date dataPrm) {
        if (dataPrm == null) {
            return "";
        }
        // Desmembrando a data de nascimento
        Calendar nascimentoCalendario = Calendario.getInstance();
        nascimentoCalendario.setTime(dataPrm);
        int diaSemana = nascimentoCalendario.get(Calendar.DAY_OF_WEEK);
        if (diaSemana == 1) {
            return "DM";
        } else if (diaSemana == 2) {
            return "SG";
        } else if (diaSemana == 3) {
            return "TR";
        } else if (diaSemana == 4) {
            return "QA";
        } else if (diaSemana == 5) {
            return "QI";
        } else if (diaSemana == 6) {
            return "SX";
        } else {
            return "SB";
        }
    }

    public static String getMontarMatricula(String codigo, int tamanhoMascaraMatricula) throws ConsistirException {
        return String.format("%0"+tamanhoMascaraMatricula+"d", Long.parseLong(codigo));
    }

    public static String getDesmontarMatricula(String matricula) throws ConsistirException {
        int i = 0;
        int tamanho = matricula.length();
        int tamanhoMatricula = matricula.length();
        while (i < tamanhoMatricula) {
            if (matricula.charAt(0) == '0') {
                matricula = matricula.substring(1, tamanho);
                tamanho = tamanho - 1;
                i++;
            } else {
                return matricula;
            }
        }
        return matricula;
    }

    public static Integer calcularIdadePessoa(Date dataAtualPrm, Date dataNascimentoPrm) {
        if ((dataNascimentoPrm == null) || (dataAtualPrm == null)) {
            return 0;
        }
        // Desmembrando a data de nascimento
        Calendar nascimentoCalendario = Calendario.getInstance();
        nascimentoCalendario.setTime(dataNascimentoPrm);
        int anoNascimento = nascimentoCalendario.get(Calendar.YEAR);
        String mesNascimentoStr = String.valueOf(nascimentoCalendario.get(Calendar.MONTH) + 1);
        if (mesNascimentoStr.length() == 1) {
            mesNascimentoStr = "0" + mesNascimentoStr;
        }
        String diaNascimentoStr = String.valueOf(nascimentoCalendario.get(Calendar.DAY_OF_MONTH));
        if (diaNascimentoStr.length() == 1) {
            diaNascimentoStr = "0" + diaNascimentoStr;
        }
        String mesDiaNascimento = mesNascimentoStr + diaNascimentoStr;

        // Desmembrando a data atual (passada como parametro)
        Calendar dataAtualCalendario = Calendario.getInstance();
        dataAtualCalendario.setTime(dataAtualPrm);

        int anoAtual = dataAtualCalendario.get(Calendar.YEAR);

        String mesAtualStr = String.valueOf(dataAtualCalendario.get(Calendar.MONTH) + 1);
        if (mesAtualStr.length() == 1) {
            mesAtualStr = "0" + mesAtualStr;
        }

        String diaAtualStr = String.valueOf(dataAtualCalendario.get(Calendar.DAY_OF_MONTH));
        if (diaAtualStr.length() == 1) {
            diaAtualStr = "0" + diaAtualStr;
        }

        String mesDiaAtual = mesAtualStr + diaAtualStr;

        int idade = anoAtual - anoNascimento;
        if (mesDiaAtual.compareTo(mesDiaNascimento) < 0) {
            idade--;
        }

        return idade;
    }

    public static String substituirTag(String tag, String valor, String texto) {
        int posicaoTag = texto.indexOf(tag);
        String parteTextoInicial = texto.substring(0, posicaoTag);
        String parteTextoFinal = texto.substring(posicaoTag + tag.length());
        return parteTextoInicial + valor + parteTextoFinal;
    }

    public static String trocarAcentuacaoPorAcentuacaoHTML(String prm) {
        String nova = "";
        for (int i = 0; i < prm.length(); i++) {
            if (prm.charAt(i) == Uteis.A_AGUDO) {
                // nova += "a";
                nova += "&aacute;";
            } else if (prm.charAt(i) == Uteis.A_AGUDOMAIUSCULO) {
                // nova += "a";
                nova += "&Aacute;";
            } else if (prm.charAt(i) == Uteis.A_CIRCUNFLEXO) {
                nova += "&acirc;";
            } else if (prm.charAt(i) == Uteis.A_CIRCUNFLEXOMAIUSCULO) {
                nova += "&Acirc;";
            } else if (prm.charAt(i) == Uteis.A_CRASE) {
                nova += "&agrave;";
            } else if (prm.charAt(i) == Uteis.A_AGUDOMAIUSCULO) {
                nova += "&Aacute;";
            } else if (prm.charAt(i) == Uteis.A_TIO) {
                nova += "&atilde;";
            } else if (prm.charAt(i) == Uteis.A_TIOMAIUSCULO) {
                nova += "&Atilde;";
            } else if (prm.charAt(i) == Uteis.E_AGUDO) {
                nova += "&eacute;";
            } else if (prm.charAt(i) == Uteis.E_AGUDOMAIUSCULO) {
                nova += "&Eacute;";
            } else if (prm.charAt(i) == Uteis.E_CIRCUNFLEXO) {
                nova += "&ecirc;";
            } else if (prm.charAt(i) == Uteis.E_CIRCUNFLEXOMAIUSCULO) {
                nova += "&Ecirc;";
            } else if (prm.charAt(i) == Uteis.I_AGUDO) {
                nova += "&iacute;";
            } else if (prm.charAt(i) == Uteis.I_AGUDOMAIUSCULO) {
                nova += "&Iacute;";
            } else if (prm.charAt(i) == Uteis.O_AGUDO) {
                nova += "&oacute;";
            } else if (prm.charAt(i) == Uteis.O_AGUDOMAIUSCULO) {
                nova += "&Oacute;";
            } else if (prm.charAt(i) == Uteis.U_AGUDO) {
                nova += "&uacute;";
            } else if (prm.charAt(i) == Uteis.U_AGUDOMAIUSCULO) {
                nova += "&Uacute;";
            } else if (prm.charAt(i) == Uteis.O_TIO) {
                nova += "&otilde;";
            } else if (prm.charAt(i) == Uteis.O_TIOMAIUSCULO) {
                nova += "&Otilde;";
            } else if (prm.charAt(i) == Uteis.U_TREMA) {
                nova += "&uuml;";
            } else if (prm.charAt(i) == Uteis.C_CEDILHA) {
                nova += "&ccedil;";
            } else if (prm.charAt(i) == Uteis.C_CEDILHAMAIUSCULO) {
                nova += "&Ccedil;";
                // } else if (Character.isSpaceChar(prm.charAt(i))){
                // nova += "_";
            } else {
                nova += prm.charAt(i);
            }
        }
        return nova.replaceAll(java.util.regex.Pattern.quote("$"),  Matcher.quoteReplacement("\\$"));
    }

    public static String retirarAcentuacaoRegex(String texto) {
        texto = texto.replaceAll("[ÂÀÁÄÃ]", "A");
        texto = texto.replaceAll("[âãàáä]", "a");
        texto = texto.replaceAll("[ÊÈÉË]", "E");
        texto = texto.replaceAll("[êèéë]", "e");
        texto = texto.replaceAll("[ÎÍÌÏ]", "I");
        texto = texto.replaceAll("[îíìï]", "i");
        texto = texto.replaceAll("[ÔÕÒÓÖ]", "O");
        texto = texto.replaceAll("[ôõòóö]", "o");
        texto = texto.replaceAll("[ÛÙÚÜ]", "U");
        texto = texto.replaceAll("[ûúùü]", "u");
        texto = texto.replaceAll("Ç", "C");
        texto = texto.replaceAll("ç", "c");
        texto = texto.replaceAll("[ýÿ]", "y");
        texto = texto.replaceAll("Ý", "Y");
        texto = texto.replaceAll("ñ", "n");
        texto = texto.replaceAll("Ñ", "N");
        texto = texto.replaceAll("['<>\\|]", " ");
        texto = texto.replaceAll("/", " ");
        return texto;
    }

    public static String retirarAcentuacao(String prm) {
        String nova = "";
        if (UteisValidacao.emptyString(prm)) {
            return "";
        }
        for (int i = 0; i < prm.length(); i++) {
            if (prm.charAt(i) == Uteis.A_AGUDO || prm.charAt(i) == Uteis.A_CIRCUNFLEXO || prm.charAt(i) == Uteis.A_CRASE
                    || prm.charAt(i) == Uteis.A_TIO || prm.charAt(i) == Uteis.A_TREMA) {
                nova += "a";
            } else if (prm.charAt(i) == Uteis.A_AGUDOMAIUSCULO || prm.charAt(i) == Uteis.A_TIOMAIUSCULO
                    || prm.charAt(i) == Uteis.A_CRASEMAIUSCULO || prm.charAt(i) == Uteis.A_CIRCUNFLEXOMAIUSCULO || prm.charAt(i) == Uteis.A_TREMAMAIUSCULO) {
                nova += "A";
            } else if (prm.charAt(i) == Uteis.E_AGUDO || prm.charAt(i) == Uteis.E_CIRCUNFLEXO) {
                nova += "e";
            } else if (prm.charAt(i) == Uteis.E_AGUDOMAIUSCULO || prm.charAt(i) == Uteis.E_CIRCUNFLEXOMAIUSCULO) {
                nova += "E";
            } else if (prm.charAt(i) == Uteis.I_AGUDO) {
                nova += "i";
            } else if (prm.charAt(i) == Uteis.I_AGUDOMAIUSCULO) {
                nova += "I";
            } else if (prm.charAt(i) == Uteis.O_AGUDO || prm.charAt(i) == Uteis.O_TIO || prm.charAt(i) == Uteis.O_CIRCUNFLEXO || prm.charAt(i) == Uteis.O_TREMA) {
                nova += "o";
            } else if (prm.charAt(i) == Uteis.O_AGUDOMAIUSCULO || prm.charAt(i) == Uteis.O_TIOMAIUSCULO || prm.charAt(i) == Uteis.O_CIRCUNFLEXOMAIUSCULO || prm.charAt(i) == Uteis.O_TREMAMAIUSCULO) {
                nova += "O";
            } else if (prm.charAt(i) == Uteis.U_AGUDO || prm.charAt(i) == Uteis.U_TREMA) {
                nova += "u";
            } else if (prm.charAt(i) == Uteis.U_AGUDOMAIUSCULO || prm.charAt(i) == Uteis.U_TREMAMAIUSCULO) {
                nova += "U";
            } else if (prm.charAt(i) == Uteis.C_CEDILHA) {
                nova += "c";
            } else if (prm.charAt(i) == Uteis.C_CEDILHAMAIUSCULO) {
                nova += "C";
                // } else if (Character.isSpaceChar(prm.charAt(i))){
                // nova += "_";
            } else if (prm.charAt(i) == Uteis.N_TIL) {
                nova += "n";
            } else if (prm.charAt(i) == Uteis.N_TILMAIUSCULO) {
                nova += "N";
            } else {
                nova += prm.charAt(i);
            }
        }
        return (nova);
    }

    // Metodo para incrementar um numero a uma string Ex: 00001 apos execucao do metodo fica 00002
    public static String incrementarNumeroDoTipoString(String valor, int valorIncremento) {
        int tamanho = valor.length();
        int posicaoUltimoZero = 0;
        int cont = 0;
        boolean entrou = false;
        while (cont < tamanho) {
            if (!valor.substring(cont, cont + 1).equals("1") && !valor.substring(cont, cont + 1).equals("2")
                    && !valor.substring(cont, cont + 1).equals("3") && !valor.substring(cont, cont + 1).equals("4")
                    && !valor.substring(cont, cont + 1).equals("5") && !valor.substring(cont, cont + 1).equals("6")
                    && !valor.substring(cont, cont + 1).equals("7") && !valor.substring(cont, cont + 1).equals("8")
                    && !valor.substring(cont, cont + 1).equals("9")) {
                posicaoUltimoZero = cont;
                entrou = true;
            } else {
                cont = tamanho;
            }
            cont++;
        }
        String zeros = "";
        String valorParcial = "";
        if (entrou) {
            zeros = valor.substring(0, posicaoUltimoZero + 1);
            valorParcial = valor.substring(posicaoUltimoZero + 1);
        } else {
            zeros = "";
            valorParcial = valor.substring(0);
        }
        int valorParcialInt = Integer.parseInt(valorParcial);
        return zeros + String.valueOf(valorParcialInt + valorIncremento);
    }

    // Metodo para incrementar um numero a uma string Ex: 00001 apos execucao do metodo fica 00002
    public static String getIncrementarNumeroCheque(String valor) throws Exception {

        String aux = "";
        String zeros = "";
        Integer retorno = 0;
        int tamanho = valor.length();
        int i = 0;
        int posicao = 0;
        int j = 0;
        while (i != tamanho) {
            Character caracter = valor.charAt(i);
            if (Character.isDigit(caracter)) {
                posicao = i;
                break;
            }
            i++;
        }
        aux = valor.substring(0, posicao);
        try {
            retorno = Integer.parseInt(valor.substring(posicao, valor.length()));
            retorno++;
            String tam = retorno.toString();
            while (j != tamanho - tam.length() - aux.length() && tamanho > tam.length()) {
                zeros += '0';
                j++;
            }
            aux += zeros;
            aux += retorno.toString();
            return aux;
        } catch (NumberFormatException n) {
            throw new Exception("Número do cheque" + n.getMessage().substring(n.getMessage().indexOf(":") + 1) + " não é válido. Informe outro.");
        } catch (Exception e) {
            throw new Exception("Não foi possível incrementar o número do cheque informe outro número.");
        }
    }

    // public static String getIncrementarNumeroCheque(String valor) throws Exception {
    // int i = 0;
    // boolean entrou = true;
    // String numeroAnterior = "";
    // int tamanho = valor.length();
    // int tamanhoMatricula = valor.length();
    // while (i < tamanhoMatricula) {
    // /*Esse se validar se o conteudo da String não e um numero a ser somado*/
    // if (valor.charAt(0) != '1' && valor.charAt(0) != '2' && valor.charAt(0) != '3' && valor.charAt(0) != '4' && valor.charAt(0) != '5' &&
    // valor.charAt(0) != '6' && valor.charAt(0) != '7' && valor.charAt(0) != '8' && valor.charAt(0) != '9') {
    // /*a variavel numeroAnteriro armazena se o conteudo da string for um caracter que nao pode ser somado*/
    // numeroAnterior = numeroAnterior + valor.substring(0, 1);
    // /*retiro o caracter da variavel valor para verificar o proximo caracter*/
    // valor = valor.substring(1, tamanho);
    // tamanho = tamanho - 1;
    // i++;
    // } else if (valor.charAt(0) == '9') {/* caso o caracter verificado seja o numero 9 vou validar se existe numero antes dele ou depois
    // dele para q seja feito o calculo */
    // if (valor.length() > 1) {
    // if (valor.charAt(valor.length() - 1) == '9' && valor.charAt(valor.length() - 2) == '9' && entrou) {/*rotina para fazer o calculo com
    // numero a esquerda do 9*/
    // if (numeroAnterior.charAt(numeroAnterior.length() - 1) == '0') {
    // numeroAnterior = numeroAnterior.substring(0, numeroAnterior.length() - 1);
    // entrou = false;
    // }
    // } else if (valor.charAt(valor.length() - 1) != '9') {
    // /**rotina para fazer o calculo com numero a direita do 9*/
    // numeroAnterior = numeroAnterior + valor.substring(0, 1);
    // valor = valor.substring(1, tamanho);
    // tamanho = tamanho - 1;
    // i++;
    // } else {
    // i++;
    // }
    // } else if (numeroAnterior.charAt(numeroAnterior.length() - 1) == '0' && entrou) {
    // numeroAnterior = numeroAnterior.substring(0, numeroAnterior.length() - 1);
    // entrou = false;
    // }
    // } else {
    // i++;
    // }
    // }
    // int incremento = 0;
    // try {
    // incremento = Integer.parseInt(valor);
    // incremento = incremento + 1;
    // } catch (Exception e) {
    // throw new Exception("Não foi possível incrementar o número do cheque informe outro número.");
    // }
    // return numeroAnterior + String.valueOf(incremento);
    // }
    public static String gethoraHHMMSSAjustado(Date data) {
        if(data == null){
            return "";
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        String hora = String.valueOf(cal.get(Calendar.HOUR_OF_DAY));
        String minuto = String.valueOf(cal.get(Calendar.MINUTE));
        String segundo = String.valueOf(cal.get(Calendar.SECOND));

        if (hora.length() == 1) {
            hora = "0" + hora;
        }
        if (minuto.length() == 1) {
            minuto = "0" + minuto;
        }
        if (segundo.length() == 1) {
            segundo = "0" + segundo;
        }

        String horaFormata = hora + ":" + minuto + ":" + segundo;

        return horaFormata;
    }

    public static Date obterProximoProcessamentoRemessaServiceAPartirDeUmaData(Date expiraEm) throws Exception {
        Calendar cal = Calendar.getInstance();
        cal.setTime(expiraEm);
        Calendar meioDia = (Calendar) cal.clone();
        meioDia.set(Calendar.HOUR_OF_DAY, 12);
        meioDia.set(Calendar.MINUTE, 30);
        meioDia.set(Calendar.SECOND, 0);

        Calendar vinteDuasHoras = (Calendar) cal.clone();
        vinteDuasHoras.set(Calendar.HOUR_OF_DAY, 22);
        vinteDuasHoras.set(Calendar.MINUTE, 0);
        vinteDuasHoras.set(Calendar.SECOND, 0);

        if (cal.before(meioDia)) {
            return meioDia.getTime();
        } else if (cal.before(vinteDuasHoras)) {
            return vinteDuasHoras.getTime();
        } else {
            Calendar meioDiaAmanha = (Calendar) meioDia.clone();
            meioDiaAmanha.add(Calendar.DAY_OF_MONTH, 1);
            return meioDiaAmanha.getTime(); // Próximo processamento será no meio-dia do dia seguinte
        }
    }

    public static String gethoraHHMMAjustado(Date data) {
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        String hora = String.valueOf(cal.get(Calendar.HOUR_OF_DAY));
        String minuto = String.valueOf(cal.get(Calendar.MINUTE));

        if (hora.length() == 1) {
            hora = "0" + hora;
        }
        if (minuto.length() == 1) {
            minuto = "0" + minuto;
        }

        String horaFormata = hora + ":" + minuto;

        return horaFormata;
    }

    public static boolean getValidarStringSePossuiLetra(String texto) {
        for (int i = 0; i < texto.length(); i++) {
            if (Character.isLetter(texto.charAt(i))) {
                return true;
            }
        }
        return false;
    }

    /**
     * @deprecated @see Calendario.java
     * @param data1
     * @param data2
     * @return
     * @throws ParseException
     */
    public static Integer getCompareData(Date data1, Date data2) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
        String a = format.format(data1);
        String b = format.format(data2);
        data1 = format.parse(a);
        data2 = format.parse(b);
        if (data1.compareTo(data2) > 0) {
            return 1;
        } else if (data1.compareTo(data2) == 0) {
            return 0;
        } else if (data1.compareTo(data2) < 0) {
            return -1;
        }
        return null;
    }

    public static String getIntervaloHorasEntreDatas(Date dataHoraInicial, Date dataHoraFinal) throws Exception {
        StringBuilder ret = new StringBuilder();
        // se nao existe as datas retorna string vazia
        if (dataHoraInicial == null || dataHoraFinal == null) {
            return ret.toString();
        }
        // calcula os minutos entre essas datas e transforma para o padrão hh:mi
        Long mins = Uteis.minutosEntreDatas(dataHoraInicial, dataHoraFinal);
        Long horas = mins / 60;
        Long minutos = mins % 60;

        String horasString = "";
        if (horas < 10) {
            horasString = "0" + horas.toString();
        } else {
            horasString = horas.toString();
        }
        String minutosString = "";
        if (minutos < 10) {
            minutosString = "0" + minutos.toString();
        } else {
            minutosString = minutos.toString();
        }
        ret.append(horasString);
        ret.append(":");
        ret.append(minutosString);
        return ret.toString();
    }

    public static Integer gerarDV(String numero, Integer soma) {
        int peso = 11;
        int dv = 0;
        int num = 0;

        for (int xi = numero.length() - 1; xi >= 0; --xi) {
            if (peso == 11) {
                peso = 2;
            }

            num = Integer.parseInt(String.valueOf(numero.charAt(xi)));
            soma = soma + (peso * num);
            peso++;
        }

        dv = soma % 11;
        dv = 11 - dv;

        if (dv == 11) {
            dv = 1;
        } else if (dv == 10) {
            dv = 0;
        }
        return new Integer(dv);
    }

    public static String retornaDescricaoDiaSemana(Date data) {
        if (data == null) {
            return "";
        }
        Calendar calen = Calendar.getInstance();
        calen.setTime(data);
        return retornaDescricaoDiaSemana(calen);
    }

    public static String retornaDescricaoDiaSemana(Calendar data) {

        if (data.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            return "Domingo";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY) {
            return "Segunda";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.TUESDAY) {
            return "Terça";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.WEDNESDAY) {
            return "Quarta";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.THURSDAY) {
            return "Quinta";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY) {
            return "Sexta";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
            return "Sábado";
        } else {
            return "";
        }
    }

    public static String retornaDescricaoMes(Date data) {
        Calendar calendario = Calendario.getInstance();
        calendario.setTime(data);
        if (calendario.get(Calendar.MONTH) == Calendar.JANUARY) {
            return "Janeiro";
        } else if (calendario.get(Calendar.MONTH) == Calendar.FEBRUARY) {
            return "Fevereiro";
        } else if (calendario.get(Calendar.MONTH) == Calendar.MARCH) {
            return "Março";
        } else if (calendario.get(Calendar.MONTH) == Calendar.APRIL) {
            return "Abril";
        } else if (calendario.get(Calendar.MONTH) == Calendar.MAY) {
            return "Maio";
        } else if (calendario.get(Calendar.MONTH) == Calendar.JUNE) {
            return "Junho";
        } else if (calendario.get(Calendar.MONTH) == Calendar.JULY) {
            return "Julho";
        } else if (calendario.get(Calendar.MONTH) == Calendar.AUGUST) {
            return "Agosto";
        } else if (calendario.get(Calendar.MONTH) == Calendar.SEPTEMBER) {
            return "Setembro";
        } else if (calendario.get(Calendar.MONTH) == Calendar.OCTOBER) {
            return "Outubro";
        } else if (calendario.get(Calendar.MONTH) == Calendar.NOVEMBER) {
            return "Novembro";
        } else if (calendario.get(Calendar.MONTH) == Calendar.DECEMBER) {
            return "Dezembro";
        } else {
            return "";
        }
    }

    public static Date obterPrimeiroDiaSemanaAtual(){
        Calendar c = Calendario.getInstance();
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek());
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.clear(Calendar.MINUTE);
        c.clear(Calendar.SECOND);
        return c.getTime();
    }

    public static Date obterUltimoDiaSemanaAtual(){
        Calendar c = Calendario.getInstance();
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek());
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.clear(Calendar.MINUTE);
        c.clear(Calendar.SECOND);
        c.add(Calendar.WEEK_OF_YEAR, 1);
        return c.getTime();
    }

    public static Date obterPrimeiroEUltimoDiaSemana(Boolean primeiroDia) throws Exception {
        Date data = negocio.comuns.utilitarias.Calendario.hoje();
        Calendar c = Calendario.getInstance();
        c.setTime(data);
        if (Calendar.SUNDAY == c.get(Calendar.DAY_OF_WEEK)) {
            data = obterDataFutura2(data, 1);
            c.setTime(data);
        }
        if (primeiroDia) {
            c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            data = obterDataAnterior(c.getTime(), 1);

        } else {
            c.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
            return c.getTime();
        }
        return data;
    }

    public static Date obterPrimeiroEUltimoDiaSemana(Boolean primeiroDia, Date dataConsideracao) throws Exception {
        Date data = dataConsideracao;
        Calendar c = Calendario.getInstance();
        c.setTime(data);
        if (Calendar.SUNDAY == c.get(Calendar.DAY_OF_WEEK)) {
            data = obterDataFutura2(data, 1);
            c.setTime(data);
        }
        if (primeiroDia) {
            c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            data = obterDataAnterior(c.getTime(), 1);

        } else {
            c.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
            return c.getTime();
        }
        return data;
    }

    public static String retornarCodigos(List listaSuperVO){
        StringBuilder codigos = new StringBuilder();
        if(UteisValidacao.emptyList(listaSuperVO)){
            return "";
        }
        for (Object object: listaSuperVO){
            SuperVO superVO = (SuperVO)object;
            if (codigos.toString().equals("")){
                codigos.append(superVO.getCodigo());
            }else{
                codigos.append(",").append(superVO.getCodigo());
            }
        }
        return codigos.toString();
    }

    public static Date obterUltimoDiaSemanaUltimaHora(Date dataConsideracao) throws Exception {
        Date data = dataConsideracao;
        Calendar c = Calendario.getInstance();
        c.setTime(data);
        if (Calendar.SUNDAY == c.get(Calendar.DAY_OF_WEEK)) {
            data = obterDataFutura2(data, 1);
            c.setTime(data);
        }

        c.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);

        c.add(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.AM_PM, 0);
        c.set(Calendar.HOUR, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);

        c.add(Calendar.SECOND, -1);
        return c.getTime();
    }

    public static void liberarListaMemoria(List listaLiberar) {
        if (listaLiberar != null) {
            listaLiberar.clear();
        }
    }

    public static String criarTabelaIn(String codigosIn){
        // exemplo: codigosIn: 10,120,12,45
        String[] arrayCodigo = codigosIn.split(",");
        StringBuilder sql = new StringBuilder();
        sql.append("select sqltt.codigo \n");
        sql.append("from ( \n");
        for (int i=0; i<arrayCodigo.length; i++){
            String codigo = arrayCodigo[i];
            if (i == 0)
              sql.append("select ").append(codigo).append(" as codigo \n");
            else
               sql.append("union select ").append(codigo).append(" as codigo \n");
        }
        sql.append(")sqltt");
        return sql.toString();
    }

    public static String criarTabelaInString(String codigosIn){
        // exemplo: codigosIn: 10,120,12,45
        String[] arrayCodigo = codigosIn.split(",");
        StringBuilder sql = new StringBuilder();
        sql.append("select sqltt.codigo \n");
        sql.append("from ( \n");
        for (int i=0; i<arrayCodigo.length; i++){
            String codigo = arrayCodigo[i];
            if (i == 0)
                sql.append("select cast ( ").append("'").append(codigo).append("' as varchar)").append(" as codigo \n");
            else
                sql.append("union select cast ( ").append("'").append(codigo).append("' as varchar)").append(" as codigo \n");
        }
        sql.append(")sqltt");
        return sql.toString();
    }

    public static Date somarMeses(Date data, int meses) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.MONTH, meses);

        return cal.getTime();
    }

    public static Date somarDias(Date data, int dias) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.DAY_OF_MONTH, dias);

        return cal.getTime();
    }

    public static Date somarDiasAPartirHoje(int dias) {
        return somarDias(Calendario.hoje(), dias);
    }

    public static Date voltarDias(Date data, int dias) {
        return somarDias(data, dias * -1);
    }

    public static Date voltarDiasAPartirHoje(int dias) {
        return voltarDias(Calendario.hoje(), dias);
    }

    public static String retiraTags(String textoFormatado, boolean retiraformatacao) {
        // String[] textos = textoFormatado.split("<.*?>");
        // StringBuilder sb = new StringBuilder();
        // for (String s : textos) {
        // sb.append(s);
        // }
        // return sb.toString();
        Pattern p = Pattern.compile("<.*?>");
        Matcher m = p.matcher(textoFormatado);
        String resultado = m.replaceAll("");
        resultado = resultado.replace("Untitled document", "");
        if (retiraformatacao) {
            resultado = resultado.replace("\r", "");
            resultado = resultado.replace("\n", "");
        }
        return resultado;
    }

    public static String obterCaminhoWeb() {
        ServletContext servletContext = (ServletContext) FacesContext.getCurrentInstance().getExternalContext().getContext();
        File caminhoBase = new File(servletContext.getRealPath("/"));
        return caminhoBase.getAbsolutePath();
    }

    /**
     * @Metodo: firstLetterLower
     * @Finalidade: primeira letra MINÚSCULA
     *
     * @param word - String
     * @return palavra com a primeira letra minúscula
     *
     * <AUTHOR> Maciel
     * @date 28/02/2008
     */
    public static String firstLetterLower(String word) {
        return word.replaceFirst(String.valueOf(word.charAt(0)), String.valueOf(word.charAt(0)).toLowerCase());
    }

    /**
     * @Metodo: firstLetterUpper
     * @Finalidade: primeira letra MAIÚSCULA
     *
     * @param word - String
     * @return palavra com a primeira letra maiúscula
     *
     * <AUTHOR> Maciel
     * @date 28/02/2008
     */
    public static String firstLetterUpper(String word) {
        return word.replaceFirst(String.valueOf(word.charAt(0)), String.valueOf(word.charAt(0)).toUpperCase());
    }

    public static String obterNomeComApenasPrimeiraLetraMaiuscula(String word) {
        StringBuilder wordNormalizada = new StringBuilder("");
        String[] nomes = word.toLowerCase().split(" ");
        for (String nome : nomes) {
            if (nome.length() > 0) {
                StringBuilder nomeNormalizado = new StringBuilder("");

                nomeNormalizado.append(nome.substring(0, 1).toUpperCase());
                if (nome.length() > 1){
                    nomeNormalizado.append(nome.substring(1));
                }
                wordNormalizada.append(nomeNormalizado).append(" ");
            }
        }
        return wordNormalizada.toString().trim();
    }

    public static void reinicializarSequencePostgreSQL(String nomeTabela) {
        String sql = "SELECT SETVAL(pg_get_serial_sequence('" + nomeTabela + "', 'codigo'), 1, true)";
        Statement stm;
        try {
            //so para pegar a conexao ativa corrente
            Connection con = FacadeManager.getFacade().getRisco().getCon();
            stm = con.createStatement();
            stm.execute(sql);
        } catch (Exception ex) {
            Logger.getLogger(Uteis.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
        }

    }

    public static String obterDiferencaEntreDatasPorExtenso(Date dataInicial, Date dataFinal) {
        Calendar calIni = Calendario.getInstance();
        Calendar calFim = Calendario.getInstance();
        calIni.setTime(dataInicial);
        calFim.setTime(dataFinal);

        int anos = (int) ((calFim.getTimeInMillis() - calIni.getTimeInMillis()) / UM_ANO);
        if (anos < 0) {
            anos = 0;
        }

        int mesInicial = calIni.get(DateFormat.MONTH_FIELD) + 1;
        int mesFinal = calFim.get(DateFormat.MONTH_FIELD) + 1;
        int diaInicial = calIni.get(Calendar.DAY_OF_MONTH);
        int diaFinal = calFim.get(Calendar.DAY_OF_MONTH);

        int difMeses = 0;
        boolean continua = true;
        if (mesInicial == mesFinal && (diaFinal < diaInicial)) {
            difMeses = 12;
            continua = false;
        }
        while (continua) {
            if (mesInicial == mesFinal) {
                break;
            }

            if (mesInicial == 12) {
                mesInicial = 0;
            }

            mesInicial++;
            difMeses++;
        }
        int meses = difMeses;
        if (meses < 0) {
            meses = 0;
        }

        int dias = 0;
        if ((diaFinal < diaInicial) && (meses > 0)) {
            meses--;
            dias = 30 - Math.abs(diaFinal - diaInicial);
        } else {
            dias = Math.abs(diaFinal - diaInicial);
        }

        return Uteis.montarStringDataPorExtenso(anos, meses, dias);

    }

    private static String montarStringDataPorExtenso(Integer anos, Integer meses, Integer dias) {
        String sAnos = anos.intValue() <= 1 ? " ano" : " anos";
        String sMeses = meses.intValue() <= 1 ? " mês" : " meses";
        String sDias = dias.intValue() <= 1 ? " dia" : " dias";

        String conteudoAnos = anos.intValue() != 0 ? anos.toString() + sAnos : "";
        String conteudoMeses = meses.intValue() != 0 ? meses.toString() + sMeses : "";
        String conteudoDias = dias.intValue() != 0 ? dias.toString() + sDias : "";

        if ((!conteudoAnos.isEmpty()) && (!conteudoMeses.isEmpty()) && (!conteudoDias.isEmpty())) {
            return conteudoAnos + ", " + conteudoMeses + " e " + conteudoDias;
        } else if ((!conteudoAnos.isEmpty()) && (!conteudoMeses.isEmpty())) {
            return conteudoAnos + " e " + conteudoMeses;

        } else if ((!conteudoMeses.isEmpty()) && (!conteudoDias.isEmpty())) {
            return conteudoMeses + " e " + conteudoDias;
        } else {
            return conteudoAnos + conteudoDias + conteudoMeses;
        }

    }

    public static Integer getMesesIdade(Integer anos) {
        Integer meses = 0;
        meses = anos * 12;
        return meses;
    }

    public static String getURLAplicacaoRequest() {
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        StringBuffer urlAplicacao = request.getRequestURL();
        String url = urlAplicacao.toString();
        url = url.substring(0, urlAplicacao.lastIndexOf("/"));
        if (SuperControle.getApplicationProtocol().contains("https")) {
            url = url.replace("http:", "https:");
        }
        return url;
    }

    public static String getUrl(){
        // Este metodo resolve o problema de definir corretamente o protocolo (http/https), quando o ambiente tem loadbalancer
        HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
        return JSFUtilities.getFromSession("urlBrowser") + request.getContextPath();
    }

    protected static FacesContext context() {
        return (FacesContext.getCurrentInstance());
    }

    public static String getURLAplicacao(String diretorio) throws Exception {
        return getURLAplicacaoRequest() + diretorio;
    }

    public static String getLocalhostIP() {
        InetAddress localHost;
        try {
            localHost = InetAddress.getLocalHost();
            // localHost.getHostName();
            return localHost.getHostAddress();
        } catch (UnknownHostException ex) {
            return "localhost";
        }
    }

    // Retorna o caminho absoluto da aplicação
    public static String retornarPathAplicacao() {
        ServletContext servletContext = (ServletContext) Uteis.context().getExternalContext().getContext();
        String path = servletContext.getRealPath("");
        return path;
    }

    public static String convertStreamToString(InputStream is) throws IOException {

        if (is != null) {
            StringBuilder sb = new StringBuilder();
            String line = "";

            try {
                BufferedReader reader = new BufferedReader(new InputStreamReader(is, Charset.forName("LATIN1")));
                while ((line = reader.readLine()) != null) {
                    if (line.trim().length() > 0) {
                        sb.append(line).append("\n");
                    }
                }
            } finally {
                is.close();
            }
            return sb.toString();
        } else {
            return "";
        }
    }

    public static StringBuilder convertStreamToStringBuffer(InputStream is, String charset) throws IOException {

        if (is != null) {
            StringBuilder sb = new StringBuilder();
            String line = "";

            try {
                BufferedReader reader = new BufferedReader(new InputStreamReader(is, Charset.forName(charset)));
                while ((line = reader.readLine()) != null) {
                    if (line.trim().length() > 0) {
                        sb.append(line).append("\n");
                    }
                }
            } finally {
                is.close();
            }
            return sb;
        } else {
            return new StringBuilder();
        }
    }

    public static StringBuilder convertStreamToStringBuffer(InputStream is) throws IOException {

        if (is != null) {
            StringBuilder sb = new StringBuilder();
            String line = "";

            try {
                BufferedReader reader = new BufferedReader(new InputStreamReader(is, Charset.forName("LATIN1")));
                while ((line = reader.readLine()) != null) {
                    if (line.trim().length() > 0) {
                        sb.append(line).append("\n");
                    }
                }
            } finally {
                is.close();
            }
            return sb;
        } else {
            return new StringBuilder();
        }
    }

    public static java.util.Date diaInicioMes(int mes) {
        return null;
    }

    public static void retirarHoraDaData(Calendar calendar) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
        String data = sdf.format(calendar.getTime());
        calendar.setTime(sdf.parse(data));
    }

    public static Date retirarHoraDaData(Date dataHora) throws Exception {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataHora);
        retirarHoraDaData(calendar);
        return calendar.getTime();
    }

    public static void calculaTempoExecucaoDaFuncionalidade(Date dtInicial, String funcionalidade) {
        try {
//            Calendar dataInicial = Calendario.getInstance();
//            dataInicial.setTime(dtInicial);
//
//            long diferenca = System.currentTimeMillis() - dataInicial.getTimeInMillis();
//            long diferencaSeg = diferenca /1000;    //DIFERENCA EM SEGUNDOS
////            long diferencaMin = diferenca /(60*1000);    //DIFERENCA EM MINUTOS
////            long diferencaHoras = diferenca/(60*60*1000);    // DIFERENCA EM HORAS
//
//            System.out.println("Tempo de excecucao de "+ funcionalidade +": " + diferencaSeg);

            Date dtFinal = Calendario.hoje();
            long diferenca = dtFinal.getTime() - dtInicial.getTime();
            long diferencaSeg = diferenca / 1000;    //DIFERENCA EM SEGUNDOS
            System.out.println("Tempo de excecucao de " + funcionalidade + ": " + diferencaSeg + " [dif millis: " + diferenca + "]");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String getXMLDocumentCFG(String file) throws Exception {
        EditorOC editor = new EditorOC();
        editor.montarEsqueleto(file);
        return editor.getText();
    }

    private static String getValor(String xml, String tagName) {
        try {
            String tagNameInicial = "<" + tagName + ">";
            int posIni = xml.indexOf(tagNameInicial) + tagNameInicial.length();
            String tagNameFinal = "</" + tagName + ">";
            int posFinal = xml.lastIndexOf(tagNameFinal);
            if (posFinal != -1 && posIni != -1) {
                return xml.substring(posIni, posFinal);
            } else {
                return "";
            }
        } catch (Exception e) {
            return "";
        }
    }

    public static String getValorTAG(String xml, String tagName) {
        try {
            return getValor(xml, tagName);
        } catch (Exception e) {
            return "";
        }
    }

    public static boolean getValorTAGBoolean(String xml, String tagName) {
        try {
            return Boolean.parseBoolean(getValor(xml, tagName));
        } catch (Exception e) {
            return false;
        }
    }

    public static void logar(boolean debug, StringBuffer sb, String mensagem) {
        String s = "[DEBUG] " + Calendario.hoje() + " - " + mensagem;
        if (sb != null) {
            sb.append("<p>").append(s).append("</p>");
        }
        if (debug) {
            System.out.println(s);
        }
    }

    public static void logarDebug(String mensagem) {
        String s = "[DEBUG] " + Calendario.hoje() + " - " + mensagem;
        System.out.println(s);
    }

    public static void logar(StringBuffer sb, String mensagem) {
        if (sb != null) {
            String s = "[DEBUG] " + Calendario.hoje() + " - " + mensagem;
            sb.append("<p>").append(s).append("</p>");
        }
        if (debug) {
            String s = "[DEBUG] " + Calendario.hoje() + " - " + mensagem;
            System.out.println(s);
        }
    }


    public static void logarPrintStackTrace(final Exception ex) {
        if (debug) {
            ex.printStackTrace();
        }
    }

    public static void logarTempoExecucao(String tag){
        Long inicio = (Long) context().getExternalContext().getRequestMap().get(tag);
        if(inicio == null){
            context().getExternalContext().getRequestMap().put(tag, System.currentTimeMillis());
        }else{
            Long tempo = System.currentTimeMillis() - inicio;
            Uteis.logar("Tempo de execução "+tag+":"+Calendario.getData(new Date(tempo), "mm:ss"));
            context().getExternalContext().getRequestMap().put(tag, null);
        }
    }

    /**
     * @param mensagem parametrizada ou não.
     * @param params (Nenhum, um ou vários) parâmetros que estão mapeados por um <b>%s</b> em <code>mensagem</code>.
     */
    public static void logar(String mensagem, Object... params) {
        logar(format(mensagem, params));
    }

    public static void logar( String mensagem) {
        logar(null, mensagem);
    }

    public static void logar(final Exception e, final Class clazz) {
        Logger.getLogger(clazz.getName()).log(Level.SEVERE, e.getMessage(), e);
    }

    public static Map<String, String> obterMapFromString(String texto) {
        Map<String, String> mapa = new HashMap();
        if (texto == null) {
            return mapa;
        }
        texto = texto.replace("{", "");
        texto = texto.replace("}", "");
        for (String keyValue : texto.split(" *, *")) {
            String[] pairs = keyValue.split(" *= *", 2);
            String atributo = pairs[0];
            String vlr = pairs.length == 1 ? "" : pairs[1];
            mapa.put(atributo, vlr);
        }
        return mapa;
    }

    /**
     * Gera uma Lista genérica de ObjetoGenerico, lendo uma String em formato
     * XML, no formato Map<String,String> ou no formato JSON
     *
     * <AUTHOR> Maciel
     * @param params
     * @return
     */
    public static List<ObjetoGenerico> obterListaParametrosValores(String params) throws Exception {
        List<ObjetoGenerico> lista = new ArrayList<ObjetoGenerico>();
        Boolean objetoJSON = false;
        try{
            JSONObject json = new JSONObject(params);
            objetoJSON = true;
            converterJSON("", json, lista);
        }catch (Exception e){}
        if(!objetoJSON){
            if (params.contains("{") && params.contains("}")) {//padrao HashMap
                params = params.replace("{", "");
                params = params.replace("}", "");
                for (String keyValue : params.split(" *, *")) {
                    String[] pairs = keyValue.split(" *= *", 2);
                    String atributo = pairs[0];
                    String vlr = pairs.length == 1 ? "" : pairs[1];
                    lista.add(new ObjetoGenerico(atributo, vlr));
                }
            } else {//padrao XML
                String regex = "<[^\\/][^>]+>[^<]*</[^>]+>";
                Pattern pattern = Pattern.compile(regex);

                Matcher matcher = pattern.matcher(params);

                while (matcher.find()) {
                    String trecho = matcher.group();
                    if (trecho.contains("</ResultadoAPC>")) {
                        break;
                    }
                    String atributo = trecho.substring(1, trecho.indexOf(">"));
                    String vlr = trecho.substring(trecho.indexOf(">") + 1, trecho.indexOf("</"));
                    lista.add(new ObjetoGenerico(atributo, vlr));
                }
            }
        }
        if (lista.isEmpty()) {
            throw new Exception(params.trim());
        } else {
            return lista;
        }
    }

    private static void converterJSON(String prefixo, JSONObject json, List<ObjetoGenerico> lista) {
        for(Object key : json.keySet()){
            Object valor = json.get(key.toString());
            if(valor instanceof JSONObject){
                String aux = UteisValidacao.emptyString(prefixo) ? key.toString() : prefixo + "." + key.toString();
                converterJSON(aux, (JSONObject) valor, lista);
            }else if(valor instanceof JSONArray){
                JSONArray array = (JSONArray) valor;
                for(int i = 0; i < array.length(); i++){
                    String aux = UteisValidacao.emptyString(prefixo) ? key.toString() : prefixo + "." + key.toString();
                    aux += "[" + i + "]";
                    converterJSON(aux, array.getJSONObject(i), lista);
                }
            }else{
                lista.add(new ObjetoGenerico(prefixo + "." +key.toString(), valor));
            }
        }
    }

    /**
     * Responsável por remover zeros a esquerda de uma string, util
     * principalmente na geração e edição dos códigos de plano de contas e
     * centro de custos, e em qualquer outro tipo de código que utilize um nr
     * fixo de caracteres numéricos
     *
     * <AUTHOR> 23/11/2011
     */
    public static String removerZeroAEsquerda(String string, int quantidadeARemover) {
        int qtdZerosAEsquerda = 0;
        for (int i = 0; i < quantidadeARemover; i++) {
            if (string.charAt(i) != '0') {
                break;
            }
            qtdZerosAEsquerda++;
        }
        return string.substring(qtdZerosAEsquerda);
    }

    /**
     * Responsável por retornar o código pai de uma entidade do financeiro,
     * baseado no código próprio. Ex.: código pai de '001.011.030' é '001.011'
     *
     * <AUTHOR> 24/11/2011
     */
    public static String obterCodigoPaiEntidadeFinanceiro(String codigo) {
        String codigoPai = "";
        //se o código tiver um tamanho maior do que 3, indica que o mesmo possui pai
        if (codigo != null && codigo.length() > 3) {
            codigoPai = codigo.substring(0, codigo.length() - 4);
        }
        return codigoPai;
    }

    public static List<Date> getDiasEntreDatas(Date inicio, Date fim) throws Exception {
        List<Date> dias = new ArrayList<Date>();
        Date recipiente = Calendario.getDataComHoraZerada(inicio);
        fim = Calendario.getDataComHoraZerada(fim);
        //enquanto a data recipiente for menor ou igual ao ultimo dia
        while (Calendario.menorOuIgual(recipiente, fim)) {
            //verificar se a academia foi aberta neste dia
            dias.add(recipiente);
            //somar um dia na data recipiente
            recipiente = Calendario.getDataComHoraZerada(somarCampoData(recipiente, Calendar.DAY_OF_MONTH, 1));
        }
        return dias;
    }

    public static BufferedImage toBufferedImage(final Image image, final int type) {
        if (image instanceof BufferedImage) {
            return (BufferedImage) image;
        }
        if (image instanceof VolatileImage) {
            return ((VolatileImage) image).getSnapshot();
        }
        loadImage(image);
        final BufferedImage buffImg = new BufferedImage(image.getWidth(null), image.getHeight(null), type);
        final Graphics2D g2 = buffImg.createGraphics();
        g2.drawImage(image, null, null);
        g2.dispose();
        return buffImg;
    }

    private static void loadImage(final Image image) {
        class StatusObserver implements ImageObserver {

            boolean imageLoaded = false;

            @Override
            public boolean imageUpdate(final Image img, final int infoflags,
                    final int x, final int y, final int width, final int height) {
                if (infoflags == ALLBITS) {
                    synchronized (this) {
                        imageLoaded = true;
                        notify();
                    }
                    return true;
                }
                return false;
            }
        }
        final StatusObserver imageStatus = new StatusObserver();
        synchronized (imageStatus) {
            if (image.getWidth(imageStatus) == -1 || image.getHeight(imageStatus) == -1) {
                while (!imageStatus.imageLoaded) {
                    try {
                        imageStatus.wait();
                    } catch (InterruptedException ex) {
                    }
                }
            }
        }
    }

    /**
     * Ordena um objeto Map por seus valores, utiliza o pattern Comparator para
     * isso.
     *
     * <AUTHOR> Maciel
     * @param unsortMap
     * @return
     */
    public static Map ordenarMapPorValores(Map unsortMap) {
        List list = new LinkedList(unsortMap.entrySet());

        //ordernar lista através de seu comparador implementado
        Collections.sort(list, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                return ((Comparable) ((Map.Entry) (o1)).getValue()).compareTo(((Map.Entry) (o2)).getValue());
            }
        });

        //adiciona a lista ordenada novamente no Map
        Map sortedMap = new LinkedHashMap();
        for (Iterator it = list.iterator(); it.hasNext();) {
            Map.Entry entry = (Map.Entry) it.next();
            sortedMap.put(entry.getKey(), entry.getValue());
        }
        return sortedMap;
    }

    public static Map ordenarMapPorKey(Map unsortMap) {
        List list = new LinkedList(unsortMap.keySet());

        //ordernar lista através de seu comparador implementado
        Collections.sort(list, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                return ((Comparable) (o1)).compareTo(((o2)));
            }
        });

        //adiciona a lista ordenada novamente no Map
        Map sortedMap = new LinkedHashMap();
        for (Iterator it = list.iterator(); it.hasNext();) {
            Object key = it.next();
            sortedMap.put(key, unsortMap.get(key));
        }
        return sortedMap;
    }

    /**
     * Valida o telefone, se for celular (primeiro número 7 ou 8 ou 9) retorna
     * true se não é considerado fixo e retorna falso. mascara do telefone
     * celular é (62)99999999
     *
     * @param celular
     * @return
     */
    public static boolean validarTelefoneCelular(String celular) {
        if(UteisValidacao.emptyString(celular)){
            return false;
        }
        celular = celular.replaceAll("[^0-9]", "");
        if (celular.length() >= 10) {
            if (Integer.parseInt(celular.substring(2, 3)) > 6) {
                return true;
            }
        }
        return false;
    }

    public static String getPrimeiroNome(String nome) {
        String nomes[] = nome.split(" ");
        if (nomes.length <= 1) {
            return nome;
        }
        return nomes[0];
    }

    public static String getSobrenome(String nomeCompleto) {
        StringBuilder retorno = new StringBuilder();
        String nomes[] = nomeCompleto.split(" ");
        if (nomes.length <= 1) {
            retorno = new StringBuilder();
        } else {
            int i = 0;
            for (String nome : nomes) {
                if (i > 0) {
                    retorno.append(nome).append(" ");
                }
                i++;
            }
            retorno.deleteCharAt(retorno.length() - 1);
        }
        return retorno.toString();
    }

    public static String encriptarNFe(String textoPlano) {
        if (textoPlano.isEmpty()) {
            return null;
        }
        return encriptar(textoPlano, CHAVE_CRIPTOGRAFIA);
    }

    public static String desencriptarNFe(String textoPlano) {
        return (textoPlano != null && !textoPlano.isEmpty()) ? desencriptar(textoPlano, CHAVE_CRIPTOGRAFIA) : null;
    }

    public static String encriptarRetornoZAWOffline(String texto) {
        return encriptar(texto, CHAVE_CRIPT_ZAW_OFFLINE);
    }

    public static String desencriptarRetornoZAWOffline(String texto) {
        return desencriptar(texto, CHAVE_CRIPT_ZAW_OFFLINE);
    }

    public static String encriptarCookie(String textoPlano) {
        return encriptar(textoPlano, CHAVE_CRIPTOGRAFIA);
    }

    public static String desencriptarCookie(String textoPlano) {
        return desencriptar(textoPlano, CHAVE_CRIPTOGRAFIA);
    }

    public static String encriptar(String textoPlano, String chave) {
        int faixa = 256;
        int tamanhoChave = chave.length();
        int posChave = -1;
        int offset = new Random().nextInt(faixa);
        int posTextoPlano = 0;
        int codigo;

        if (tamanhoChave == 0) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        result.append(converterParaHexa(offset));

        for (; posTextoPlano < textoPlano.length(); posTextoPlano++) {
            codigo = ((int) textoPlano.charAt(posTextoPlano) + offset) % 255;

            if (posChave < (tamanhoChave - 1)) {
                posChave++;
            } else {
                posChave = 0;
            }

            codigo = codigo ^ ((int) chave.charAt(posChave));
            result.append(converterParaHexa(codigo));
            offset = codigo;
        }

        return result.toString();
    }

    private static boolean ehNumeroHexa(String texto) {
        if (texto.length() < 2) {
            return false;
        }
        String carecteres = "0123456789abcdefghABCDEFGH";
        return carecteres.indexOf(texto.charAt(0)) > -1 && carecteres.indexOf(texto.charAt(1)) > -1;
    }

    public static String desencriptar(String textoCifrado, String chave) {
        int tamanhoChave = chave.length();
        int posChave = -1;
        int offset;
        int posTextoCifrado;
        int codigoTemp;
        int codigoCaractereDecifrado;
        String textoHexa;
        StringBuilder result = new StringBuilder();

        if (tamanhoChave == 0) {
            return "";
        }

        textoHexa = textoCifrado.substring(0, 2);
        if (!ehNumeroHexa(textoHexa)) {
            return "";
        }

        offset = Integer.parseInt(textoHexa, 16);
        posTextoCifrado = 2;
        do {
            textoHexa = textoCifrado.substring(posTextoCifrado, posTextoCifrado + 2);
            if (!ehNumeroHexa(textoHexa)) {
                return "";
            }
            codigoTemp = Integer.parseInt(textoHexa, 16);
            if (posChave < (tamanhoChave - 1)) {
                posChave++;
            } else {
                posChave = 0;
            }
            codigoCaractereDecifrado = codigoTemp ^ (int) (chave.charAt(posChave));
            if (codigoCaractereDecifrado <= offset) {
                codigoCaractereDecifrado = 255 + codigoCaractereDecifrado - offset;
            } else {
                codigoCaractereDecifrado = codigoCaractereDecifrado - offset;
            }
            result.append((char) codigoCaractereDecifrado);
            offset = codigoTemp;
            posTextoCifrado = posTextoCifrado + 2;
        } while (posTextoCifrado < textoCifrado.length() - 1);

        return result.toString();
    }

    private static String converterParaHexa(int codigo) {
        String hexConvertido = Integer.toHexString(codigo);
        if (hexConvertido.length() == 1) {
            hexConvertido = '0' + hexConvertido;
        }
        return hexConvertido;
    }

    public static String retirarPontosGraficos(String texto) {
        texto = texto.replaceAll("\\?", "");
        texto = texto.replaceAll("\\!", "");
        return texto;
    }

    /**
     * Retorna um array do tipo: "1,2,3,4,5" ou "joana,maria,teste". De valores
     * de um atributo de uma lista. Utiliza-se Reflexão
     *
     * @param nomeAtributo
     * @param listaObjetos
     * @return um array separado por vírgulas ou uma String vazia.
     */
    public static String splitFromList(final String nomeAtributo, List listaObjetos, boolean scape) {
        String retorno = "";
        for (Object object : listaObjetos) {
            String sc = scape ? "'%s'," : "%s,";
            retorno += String.format(sc, new Object[]{
                UtilReflection.getValor(object, nomeAtributo)});
        }
        int i = retorno.lastIndexOf(",");
        if (i != -1) {
            retorno = retorno.substring(0, i);
        }
        return retorno;
    }

    /**
     * Retorna um array do tipo: "1,2,3,4,5" ou "'joana','maria','teste'". De
     * valores de um atributo de uma lista. Utiliza-se Reflexão
     *
     * @param array
     * @param scape
     * @return um array separado por vírgulas ou uma String vazia.
     */
    public static String splitFromArray(final Object[] array, boolean scape) {
        String retorno = "";
        for (int i = 0; i < array.length; i++) {
            String sc = scape ? "'%s'," : "%s,";
            retorno += String.format(sc, new Object[]{array[i]});
        }
        int i = retorno.lastIndexOf(",");
        if (i != -1) {
            retorno = retorno.substring(0, i);
        }
        return retorno;
    }

    public static String splitFromArray(final Object[] array, boolean scape,
            final String attr) {
        String retorno = "";
        for (int i = 0; i < array.length; i++) {
            String sc = scape ? "'%s'," : "%s,";
            retorno += String.format(sc, new Object[]{
                UtilReflection.getValor(array[i], attr)});
        }
        int i = retorno.lastIndexOf(",");
        if (i != -1) {
            retorno = retorno.substring(0, i);
        }
        return retorno;
    }

    public static String splitFromArrayWithStringSeparator(final Object[] array, boolean scape,
                                        final String attr) {
        String retorno = "";
        for (int i = 0; i < array.length; i++) {
            String sc = scape ? "'\'%s\''," : "\'%s\',";
            retorno += String.format(sc, new Object[]{
                    UtilReflection.getValor(array[i], attr)});
        }
        int i = retorno.lastIndexOf(",");
        if (i != -1) {
            retorno = retorno.substring(0, i);
        }
        return retorno;
    }

    public static List<ObjetoGenerico> getAtributosFromStringBuffer(StringBuilder sb) {
        String regex = "\\[[^\\]]+\\]";
        Pattern pattern = Pattern.compile(regex);
        List<ObjetoGenerico> lista = new ArrayList<ObjetoGenerico>();

        Matcher matcher = pattern.matcher(sb.toString());
        // Procura as similaridades
        while (matcher.find()) {
            String trecho = matcher.group();
            trecho = trecho.replace("[", "").replace("]", "");

            String[] v1 = trecho.split(",");
            for (int i = 0; i < v1.length; i++) {
                String o = v1[i];
                lista.add(new ObjetoGenerico(o));
            }
        }
        return lista;
    }

    public static List<String> getTituloAtributosFromStringBuffer(StringBuilder sb) {
        String regex = "\\[[^\\]]+\\]";
        Pattern pattern = Pattern.compile(regex);
        List<String> lista = new ArrayList<String>();

        Matcher matcher = pattern.matcher(sb.toString());
        // Procura as similaridades apenas do primeiro registro, o restante são repetidos
        if (matcher.find()) {
            String trecho = matcher.group();
            trecho = trecho.replace("[", "").replace("]", "");

            String[] v1 = trecho.split(",");
            for (int i = 0; i < v1.length; i++) {
                String o = v1[i];
                String nome = o.substring(0, o.indexOf("="));
                lista.add(nome);
            }
            return lista;
        }
        return null;
    }

    public static List<String> getValoresAtributosFromStringBuffer(StringBuilder sb) {
        String regex = "\\[[^\\]]+\\]";
        Pattern pattern = Pattern.compile(regex);
        List<String> lista = new ArrayList<String>();

        Matcher matcher = pattern.matcher(sb.toString());
        // Procura as similaridades
        while (matcher.find()) {
            String trecho = matcher.group();
            trecho = trecho.replace("[", "").replace("]", "");

            String[] v1 = trecho.split(",");
            for (int i = 0; i < v1.length; i++) {
                String o = v1[i];
                lista.add(o.substring(o.indexOf("=")));
            }
            return lista;
        }
        return null;
    }

    public static void compactarArquivo(String textoOrigem, String arquivoDestino) throws Exception {
        InputStream input = new ByteArrayInputStream(textoOrigem.getBytes());
        FileOutputStream output = new FileOutputStream(arquivoDestino);

        try {
            Encoder encoder = new Encoder();
            encoder.SetAlgorithm(1);
            encoder.SetEndMarkerMode(true);
            encoder.WriteCoderProperties(output);
            long fileSize = -1;
            for (int i = 0; i < 8; i++) {
                output.write((int) (fileSize >>> (8 * i)) & 0xFF);
            }
            encoder.Code(input, output, -1, -1, null);
        } finally {
            output.flush();
            output.close();
            input.close();
        }
    }

    public static void compactarArquivo(File arquivoOrigem, String arquivoDestino) throws Exception {
        InputStream input = new FileInputStream(arquivoOrigem);
        FileOutputStream output = new FileOutputStream(arquivoDestino);

        try {
            Encoder encoder = new Encoder();
            encoder.SetAlgorithm(1);
            encoder.SetEndMarkerMode(true);
            encoder.WriteCoderProperties(output);
            long fileSize = -1;
            for (int i = 0; i < 8; i++) {
                output.write((int) (fileSize >>> (8 * i)) & 0xFF);
            }
            encoder.Code(input, output, -1, -1, null);
        } finally {
            output.flush();
            output.close();
            input.close();
        }
    }

    public static byte[] descompactar(byte[] bytes) throws Exception {
        // Ainda não está funcionando
        return null;
        /*
         StringInputStream input = new StringInputStream(bytes.toString());
         //InputStream input = new BufferedInputStream(inArray);
         ByteArrayOutputStream outArray = new ByteArrayOutputStream();
         BufferedOutputStream output = new BufferedOutputStream(outArray);

         try {
         int propertiesSize = 5;
         byte[] properties = new byte[propertiesSize];
         if (input.read(properties, 0, propertiesSize) != propertiesSize) {
         //"input .lzma file is too short"
         return null;
         }
         Decoder decoder = new Decoder();
         if (!decoder.SetDecoderProperties(properties)) {
         // Incorrect stream properties
         return null;
         }
         long outSize = 0;
         for (int i = 0; i < 8; i++) {
         int v = input.read();
         if (v < 0) {
         //Can't read stream size
         return null;
         }
         outSize |= ((long) v) << (8 * i);
         }
         if (!decoder.Code(input, output, outSize)) {
         //Error in data stream
         return null;
         }

         return outArray.toByteArray();

         } finally {
         output.flush();
         output.close();
         input.close();
         }
         *
         */
    }

    public static void subDivide(List list, int subCollectionSize, List resultList) {
        for (int i = 0; i < list.size() / subCollectionSize + 1; i++) {
            int maxLength;
            if (i * subCollectionSize + subCollectionSize > list.size()) {
                maxLength = list.size();
            } else {
                maxLength = i * subCollectionSize + subCollectionSize;
            }
            List sublist = new ArrayList();
            for (int j = i * subCollectionSize; j < maxLength; j++) {

                sublist.add(list.get(j));
            }
            resultList.add(sublist);
        }
    }

    public static ByteArrayOutputStream criarArray(File arquivo) throws Exception {
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        byte buffer[] = new byte[4096];
        int bytesRead = 0;
        FileInputStream fi = new FileInputStream(arquivo.getAbsolutePath());
        while ((bytesRead = fi.read(buffer)) != -1) {
            arrayOutputStream.write(buffer, 0, bytesRead);
        }
        arrayOutputStream.close();
        fi.close();
        return arrayOutputStream;
    }

    public static String getColumnTypeName(int sqlType, int displaySize) {
        switch (sqlType) {
            case Types.INTEGER:
                return "int4";
            case Types.VARCHAR:
                if (displaySize > 255) {
                    return "text";
                }
                return "varchar";
            case Types.BIGINT:
                return "int8";
            case Types.BIT:
                return "bool";
            case Types.DATE:
                return "date";
            case Types.TIMESTAMP:
                return "timestamp";
            case Types.NUMERIC:
                return "numeric";
            case Types.DOUBLE:
                return "float8";
            case Types.BINARY:
                return "bytea";
            case Types.CHAR:
                return "bpchar";
            case Types.REAL:
                return "float4";
            case Types.SMALLINT:
                return "int2";
            case Types.TIME:
                return "time";
        }
        return "varchar";
    }

    /**
     * Metodo Verifica a existência de uma coluna dentro do ResultSet
     * não fechando o mesmo caso não tenho a coluna
     *
     * @param resultSet
     * @param nomeColuna
     * @return boolean
     * @throws SQLException
     */
    public static boolean resultSetContemColuna(ResultSet resultSet, String nomeColuna) throws SQLException {
        try {
            return resultSet.findColumn(nomeColuna) > 0;
        } catch (SQLException sqlException) {
            return false;
        }
    }

    public static int calcularDiasUtilizadosNoMes(Date data, int nrDiasMes) {
        int diaInicio = Uteis.getDiaMesData(data);
        int diaAtual = Uteis.getDiaMesData(Calendario.hoje());
        if (diaInicio <= diaAtual) {
            return diaAtual - diaInicio;
        } else {
            return (nrDiasMes - diaInicio) + diaAtual;
        }

    }

    public static String getUrlModuloNFSe() {
        return PropsService.getPropertyValue(PropsService.urlModuloNFSe);
    }

    public static String getUrlServiceNFSe() {
        return PropsService.getPropertyValue(PropsService.urlNFSe);
    }

    public static String getUrlServiceNFSeRestAdmin() {
        return PropsService.getPropertyValue(PropsService.urlNFSeRestAdmin);
    }

    public static String getUrlServiceNFSeRest() {
        return PropsService.getPropertyValue(PropsService.urlNFSeRest);
    }

    public static String getUrlFinanceiroPacto() {
        return PropsService.getPropertyValue(PropsService.urlFinanceiroPacto);
    }

    public static String getUrlBoletosFinanceiroPacto() {
        return PropsService.getPropertyValue(PropsService.urlBoletosFinanceiroPacto);
    }

    public static String getUrlDashBoard() {
        return PropsService.getPropertyValue(PropsService.urlDashBoard);
    }

    /**
     * Se a sua intenção é utilizar uma informação estática, utilize o método
     * PropsService.getPropertyValue. Agora, se a sua necessidade é ler as
     * informações em tempo real, garantir que o conteúdo esteja atualizado,
     * pode ter mudado em intervalos curtos de tempo, ou o arquivo não é o
     * padrão SuperControle.properties, você deve utilizar este método.
     *
     * @param caminho
     * @param propriedade
     * @return
     */
    public static String getPropriedade(String caminho, String propriedade) {
        Properties props = new Properties();
        InputStream in = SuperControle.class.getResourceAsStream(caminho);
        try {
            props.load(in);
        } catch (IOException ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            try {
                in.close();
            } catch (IOException ex) {
                Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return props.getProperty(propriedade);
    }

    public static String encriptarSol(String texto) {
        return encriptar(texto, CHAVE_CRIPT_SOL);
    }

    public static String desencriptarSol(String texto) {
        return desencriptar(texto, CHAVE_CRIPT_SOL);
    }

    public static String encriptarAWS(String texto) throws Exception {
        return Criptografia.encrypt(texto, CHAVE_CRIPT_MIDIA, AlgoritmoCriptoEnum.ALGORITMO_AES);
    }

    public static String desencriptarAWS(String texto) {
        return Criptografia.decrypt(texto, CHAVE_CRIPT_MIDIA, AlgoritmoCriptoEnum.ALGORITMO_AES);
    }

    public static String encriptarZWInternal(String texto) throws Exception {
        return Criptografia.encrypt(texto, CHAVE_CRIPT_MIDIA, AlgoritmoCriptoEnum.ALGORITMO_AES, false);
    }

    public static String desencriptarZWInternal(String texto) {
        return Criptografia.decrypt(texto, CHAVE_CRIPT_MIDIA, AlgoritmoCriptoEnum.ALGORITMO_AES, false);
    }

    public static String getNomeAbreviado(String nome) {
        return getNomeAbreviado(nome, false);
    }
    public static String getNomeAbreviado(String nome, boolean segundoTbm) {
        if(nome == null){
            return "";
        }
        String nomes[] = nome.split(" ");
        if (nomes.length <= 1) {
            return nome;
        }
        int tamanho = nomes.length;
        String abv = nomes[0];
        if(segundoTbm){
            abv += " "+nomes[1];
        }

        for (int i = 1; i < tamanho - 1; i++) {
            if (nomes[i].length() > (segundoTbm ? 2 : 1)) {
                abv += " " + nomes[i].charAt(0) + ".";
            }
        }

        abv += " " + nomes[tamanho - 1];

        return abv;
    }

    public static String retornaLabelObjetoSelecionado(List<SelectItem> lista, Integer codigoSelecionado) {
        for (SelectItem si : lista) {
            if (si.getValue() != null && si.getValue().equals(codigoSelecionado)) {
                return si.getLabel();
            }
        }
        return "";
    }

    public static <E extends Enum<E>> boolean enumIn(Enum<E> elemType,
            Enum<E>[] vetor) {
        for (int i = 0; i < vetor.length; i++) {
            Enum<E> valor = vetor[i];
            if (valor == elemType) {
                return true;
            }
        }
        return false;
    }

    public static String normalizarStringJSON(String string) {
        if (string == null) {
            return "";
        }
        String normalizada = string.trim();

        normalizada = normalizada.replaceAll("\r\n", "<br/>");
        normalizada = normalizada.replaceAll("\n{2,}", "\n");
        normalizada = normalizada.replaceAll("\n", "<br/>");
        normalizada = normalizada.replaceAll("\\s{2,}", "</br>");
        normalizada = normalizada.replaceAll("(</br>){2,}", "</br>");
        normalizada = normalizada.replaceAll("\\\\", "\\\\\\\\");
        normalizada = normalizada.replaceAll("\"", "\\\\\"");
        normalizada = normalizada.replaceAll("\t", "");
        return normalizada;
    }

    //joao alcides: pega uma lista de objetos e concatena os valores dos selecionados
    public static String getListaEscolhidos(List lista, String booleano, String atributo, boolean codigo) throws Exception {
        String valores = "";
        for (Object obj : lista) {
            Method getEscolhido = obj.getClass().getDeclaredMethod("get".concat(booleano), null);
            Boolean escolhido = (Boolean) getEscolhido.invoke(obj, null);
            if (escolhido) {
                Method getCodigo = obj.getClass().getDeclaredMethod("get".concat(atributo), null);
                valores += codigo ? ("," + ((Integer) getCodigo.invoke(obj, null))) : ("," + ((String) getCodigo.invoke(obj, null)));
            }
        }
        return valores.replaceFirst(",", "");
    }
    //joao alcides: pega uma string concatenada e marca como selecionados objetos em uma lista

    public static void marcarValoresBaseadoString(List lista, String booleano, String atributo, boolean codigo, String valores) throws Exception {
        if (UteisValidacao.emptyString(valores)) {
            return;
        }
        String[] marcados = valores.split(",");
        for (String valor : marcados) {
            if (!UteisValidacao.emptyString(valor.trim())) {
                for (Object obj : lista) {
                    Method getCodigo = obj.getClass().getDeclaredMethod("get".concat(atributo), null);
                    String valorObjLista = codigo ? ((Integer) getCodigo.invoke(obj, null)).toString() : ((String) getCodigo.invoke(obj, null));
                    if (valorObjLista.trim().equals(valor.trim())) {
                        Method setEscolhido = null;
                        try{
                            setEscolhido = obj.getClass().getDeclaredMethod("set".concat(booleano), boolean.class);
                        } catch (Exception err){
                            setEscolhido = obj.getClass().getDeclaredMethod("set".concat(booleano), Boolean.class);
                        }
                        setEscolhido.invoke(obj, true);
                    }
                }
            }
        }
    }

    public static String getStringNormalizada(String atual) {
        if (atual == null) {
            return null;
        }
        String nova = atual.replaceAll("\\s+", " ");
        return nova.trim();
    }

    public static Date obterDataInTimeZone(Date currentDate, String timeZoneId) {
        TimeZone tz = TimeZone.getTimeZone(timeZoneId);
        Calendar mbCal = new GregorianCalendar(TimeZone.getTimeZone(timeZoneId));
        mbCal.setTimeInMillis(currentDate.getTime());

        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, mbCal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, mbCal.get(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, mbCal.get(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, mbCal.get(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, mbCal.get(Calendar.MINUTE));
        cal.set(Calendar.SECOND, mbCal.get(Calendar.SECOND));
        cal.set(Calendar.MILLISECOND, mbCal.get(Calendar.MILLISECOND));

        return cal.getTime();
    }

    public static String tirarCaracteres(String texto, boolean somenteNrs) {
        String retorno = "";
        char[] c = Uteis.removerMascara(texto).replaceAll("\\(", "").replaceAll("\\)", "").replaceAll("\\-", "").toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (Character.isDigit(c[i]) && somenteNrs) {
                retorno = retorno + c[i];
            }

            if (!Character.isDigit(c[i]) && !somenteNrs) {
                retorno = retorno + c[i];
            }

        }
        return retorno;
    }

    public static byte[] read(File file) throws Exception {
        byte[] result = null;

        if (file != null && !file.isDirectory()) {
            final long length = file.length();
            result = new byte[(int) length];
            InputStream fi = new FileInputStream(file);
            byte b;
            long count = 0;

            while ((b = (byte) fi.read()) != -1) {
                result[(int) count++] = b;
            }
            fi.close();
        }
        return result;
    }

    private static boolean addByteArrayToZip(ZipOutputStream out, String fileName, final byte[] file) throws Exception {
        ZipEntry entry = new ZipEntry(fileName);
        out.putNextEntry(entry);

        if (file.length > 0) {
            out.write(file, 0, file.length);
        }

        out.closeEntry();
        out.flush();
        return true;
    }

    /**
     * Comprime um diretório ou arquivo.
     */
    public static boolean zip(String absolutePath, List<byte[]> arquivos, List<String> nomeArquivos) {
        ZipOutputStream out = null;
        FileOutputStream dest = null;
        CheckedOutputStream checksum = null;
        boolean zipado = false;

        try {
            dest = new FileOutputStream(new File(absolutePath));
            checksum = new CheckedOutputStream(dest, new Adler32());
            out = new ZipOutputStream(new BufferedOutputStream(checksum));
            for (int i = 0; i < arquivos.size(); i++) {
                zipado = addByteArrayToZip(out, nomeArquivos.get(i), arquivos.get(i));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } catch (Error err) {
            err.printStackTrace();
        } finally {
            try {
                if (zipado) {
                    out.flush();
                    out.finish();
                    out.close();
                } else {
                    out = null;
                }
            } catch (IOException e) {
                zipado = false;
                e.printStackTrace();
            } catch (Error err) {
                zipado = false;
                err.printStackTrace();
            }
            System.gc();
        }

        return zipado;
    }

    public static boolean zipFiles(String absolutePath, List<String> absolutePathToZip, List<String> nomeArquivos) {
        ZipOutputStream out = null;
        FileOutputStream dest = null;
        CheckedOutputStream checksum = null;
        boolean zipado = false;

        try {
            dest = new FileOutputStream(new File(absolutePath));
            checksum = new CheckedOutputStream(dest, new Adler32());
            out = new ZipOutputStream(new BufferedOutputStream(checksum));
            for (int i = 0; i < absolutePathToZip.size(); i++) {
                File pdfFile = new File(absolutePathToZip.get(i));
                byte[] b = new byte[(int) pdfFile.length()];
                FileInputStream fileInputStream = new FileInputStream(pdfFile);
                fileInputStream.read(b);
                zipado = addByteArrayToZip(out, nomeArquivos.get(i), b);
                fileInputStream.close();
                if (zipado) {
                    out.flush();
                }
                b = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } catch (Error err) {
            err.printStackTrace();
        } finally {
            try {
                if (zipado) {
                    out.finish();
                    out.close();
                } else {
                    out = null;
                }
            } catch (IOException e) {
                zipado = false;
                e.printStackTrace();
            } catch (Error err) {
                zipado = false;
                err.printStackTrace();
            }
            System.gc();
        }

        return zipado;
    }

    public static String trocarAcentuacaoHTMLPorAcentuacao(String prm) {

        String HTML_A_AGUDO = "&aacute;";
        String HTML_A_AGUDOMAIUSCULO = "&Aacute;";
        String HTML_A_CIRCUNFLEXO = "&acirc;";
        String HTML_A_CIRCUNFLEXOMAIUSCULO = "&Acirc;";
        String HTML_A_CRASE = "&agrave;";
        String HTML_A_TIO = "&atilde;";
        String HTML_A_TIOMAIUSCULO = "&Atilde;";
        String HTML_E_AGUDO = "&eacute;";
        String HTML_E_AGUDOMAIUSCULO = "&Eacute;";
        String HTML_E_CIRCUNFLEXO = "&ecirc;";
        String HTML_E_CIRCUNFLEXOMAIUSCULO = "&Ecirc;";
        String HTML_I_AGUDO = "&iacute;";
        String HTML_I_AGUDOMAIUSCULO = "&Iacute;";
        String HTML_O_AGUDO = "&oacute;";
        String HTML_O_AGUDOMAIUSCULO = "&Oacute;";
        String HTML_U_AGUDO = "&uacute;";
        String HTML_U_AGUDOMAIUSCULO = "&Uacute;";
        String HTML_O_TIO = "&otilde;";
        String HTML_O_TIOMAIUSCULO = "&Otilde;";
        String HTML_U_TREMA = "&uuml;";
        String HTML_C_CEDILHA = "&ccedil;";
        String HTML_C_CEDILHAMAIUSCULO = "&Ccedil;";
        String HTML_ASPAS_ESQUERDA = "&ldquo";


        String nova = prm;

        if (prm.contains(HTML_A_AGUDO)) {
            nova = prm.replaceAll(HTML_A_AGUDO, String.valueOf(Uteis.A_AGUDO));
        }
        if (nova.contains(HTML_A_AGUDOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_A_AGUDOMAIUSCULO, String.valueOf(Uteis.A_AGUDOMAIUSCULO));
        }
        if (nova.contains(HTML_A_CIRCUNFLEXO)) {
            nova = nova.replaceAll(HTML_A_CIRCUNFLEXO, String.valueOf(Uteis.A_CIRCUNFLEXO));
        }
        if (nova.contains(HTML_A_CIRCUNFLEXOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_A_CIRCUNFLEXOMAIUSCULO, String.valueOf(Uteis.A_CIRCUNFLEXOMAIUSCULO));
        }
        if (nova.contains(HTML_A_CRASE)) {
            nova = nova.replaceAll(HTML_A_CRASE, String.valueOf(Uteis.A_CRASE));
        }
        if (nova.contains(HTML_A_TIO)) {
            nova = nova.replaceAll(HTML_A_TIO, String.valueOf(Uteis.A_TIO));
        }
        if (nova.contains(HTML_A_TIOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_A_TIOMAIUSCULO, String.valueOf(Uteis.A_TIOMAIUSCULO));
        }
        if (nova.contains(HTML_E_AGUDO)) {
            nova = nova.replaceAll(HTML_E_AGUDO, String.valueOf(Uteis.E_AGUDO));
        }
        if (nova.contains(HTML_E_AGUDOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_E_AGUDOMAIUSCULO, String.valueOf(Uteis.E_AGUDOMAIUSCULO));
        }
        if (nova.contains(HTML_E_CIRCUNFLEXO)) {
            nova = nova.replaceAll(HTML_E_CIRCUNFLEXO, String.valueOf(Uteis.E_CIRCUNFLEXO));
        }
        if (nova.contains(HTML_E_CIRCUNFLEXOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_E_CIRCUNFLEXOMAIUSCULO, String.valueOf(Uteis.E_CIRCUNFLEXOMAIUSCULO));
        }
        if (nova.contains(HTML_I_AGUDO)) {
            nova = nova.replaceAll(HTML_I_AGUDO, String.valueOf(Uteis.I_AGUDO));
        }
        if (nova.contains(HTML_I_AGUDOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_I_AGUDOMAIUSCULO, String.valueOf(Uteis.I_AGUDOMAIUSCULO));
        }
        if (nova.contains(HTML_O_AGUDO)) {
            nova = nova.replaceAll(HTML_O_AGUDO, String.valueOf(Uteis.O_AGUDO));
        }
        if (nova.contains(HTML_O_AGUDOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_O_AGUDOMAIUSCULO, String.valueOf(Uteis.O_AGUDOMAIUSCULO));
        }
        if (nova.contains(HTML_U_AGUDO)) {
            nova = nova.replaceAll(HTML_U_AGUDO, String.valueOf(Uteis.U_AGUDO));
        }
        if (nova.contains(HTML_U_AGUDOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_U_AGUDOMAIUSCULO, String.valueOf(Uteis.U_AGUDOMAIUSCULO));
        }
        if (nova.contains(HTML_O_TIO)) {
            nova = nova.replaceAll(HTML_O_TIO, String.valueOf(Uteis.O_TIO));
        }
        if (nova.contains(HTML_O_TIOMAIUSCULO)) {
            nova = nova.replaceAll(HTML_O_TIOMAIUSCULO, String.valueOf(Uteis.O_TIOMAIUSCULO));
        }
        if (nova.contains(HTML_U_TREMA)) {
            nova = nova.replaceAll(HTML_U_TREMA, String.valueOf(Uteis.U_TREMA));
        }
        if (nova.contains(HTML_C_CEDILHA)) {
            nova = nova.replaceAll(HTML_C_CEDILHA, String.valueOf(Uteis.C_CEDILHA));
        }
        if (nova.contains(HTML_C_CEDILHAMAIUSCULO)) {
            nova = nova.replaceAll(HTML_C_CEDILHAMAIUSCULO, String.valueOf(Uteis.C_CEDILHAMAIUSCULO));
        }
        if (nova.contains("<p>")) {
            nova = nova.replaceAll("<p>", "");
        }
        if (nova.contains("</p>")) {
            nova = nova.replaceAll("</p>", "");
        }
        if (nova.contains("&nbsp;")) {
            nova = nova.replaceAll("&nbsp;", " ");
        }
        return (nova);
    }

    private static String prependBaseUrlIfRecent(String urlBase, String fotokey) {
        // Extract the timestamp part from the URL
        String[] parts = fotokey.split(S3_SUFFIX_TIME);
        if (parts.length < 2) {
            return String.format(FMT_URL, urlBase, fotokey);
        }

        String originalPart = parts[0];
        String timestampStr = parts[1];

        try {
            long last_modified = Long.parseLong(timestampStr);
            long currentTime = System.currentTimeMillis();

            //aca438e8c9e947e64db2236bb2f1f7a9/97jxGKuba0*PGeIPqAkibQ==/ilgEtXDcyc@39CTz2x@Oow==.jpg?time=1715104079301

            // verifica se a foto foi alterada em menos de 24h, caso positivo força a URL do S3
            if ((currentTime - last_modified ) < UM_DIA) {
                return String.format(FMT_URL, URL_S3_PREFIX, fotokey);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // caso contrário, returna URL original
        return String.format(FMT_URL, urlBase, fotokey);
    }

    public static String getPaintFotoDaNuvem(final String fotokey) {
        if (JSFUtilities.isJSFContext() && JSFUtilities.getRequest() != null) {
            if (UteisValidacao.emptyString(fotokey)) {
                return String.format(FMT_URL,
                        PropsService.getPropertyValue(PropsService.urlFotosNuvem),
                        "fotoPadrao.jpg");
            } else {
                String urlFoto = String.format(FMT_URL,
                        PropsService.getPropertyValue(PropsService.urlFotosNuvem),
                        fotokey);
                if (urlFoto.contains(CDN_PREFIX)) {
                    urlFoto = prependBaseUrlIfRecent(PropsService.getPropertyValue(PropsService.urlFotosNuvem), fotokey);
                }
                try {
                    String urlPath = urlFoto;
                    if (PropsService.getPropertyValue(PropsService.urlFotosNuvem).equals("zw-photos")) {
                        urlPath = JSFUtilities.getRequest().getRequestURL().toString();
                        urlPath = urlPath.substring(0, urlPath.indexOf("/faces/"));
                        if (UteisValidacao.emptyString(urlFoto)) {
                            urlPath = "";
                        }
                        urlPath = urlPath + "/" + urlFoto;
                    }
                    return urlPath;
                } catch (Exception ex) {
                    return urlFoto;
                }
            }
        } else {
            if (UteisValidacao.emptyString(fotokey)) {
                return String.format(FMT_URL,
                        PropsService.getPropertyValue(PropsService.urlFotosNuvem),
                        "fotoPadrao.jpg");
            } else {
                if ("zw-photos".equals(PropsService.getPropertyValue(PropsService.urlFotosNuvem))) {
                    return String.format("%s/%s/%s",
                            PropsService.getPropertyValue(PropsService.urlAplicacao),
                            PropsService.getPropertyValue(PropsService.urlFotosNuvem),
                            fotokey);
                } else {
                    if (PropsService.getPropertyValue(PropsService.urlFotosNuvem).contains(CDN_PREFIX)) {
                        return prependBaseUrlIfRecent(PropsService.getPropertyValue(PropsService.urlFotosNuvem), fotokey);
                    } else
                        return String.format(FMT_URL,
                            PropsService.getPropertyValue(PropsService.urlFotosNuvem),
                            fotokey);
                }
            }
        }
    }

    public static String getPaintImagemDaNuvem(final String fotokey, String url) {
        if (UteisValidacao.emptyString(fotokey)) {
            return url+"imagens/image_icon.jpg";
        } else {
            return String.format("%s/%s",
                    PropsService.getPropertyValue(PropsService.urlFotosNuvem),
                    fotokey);
        }
    }

    public static Date proximaDataPorDiaSemana(Integer numeroDiaSemana){
        Calendar cal = Calendario.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, numeroDiaSemana);

        return cal.getTime();
    }

    public static Date setHoraMinutoString(Date data, String horario){
        String[] horarios = horario.split(":");
        data.setHours(Integer.parseInt(horarios[0]));
        data.setMinutes(Integer.parseInt(horarios[1]));
        data.setSeconds(0);
        return data;
    }

    public static Integer diaDaSemana(Date data) {
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        return cal.get(Calendar.DAY_OF_WEEK);
    }

    public static String getDataProximoMesAnoConcatenado(Date data) {
        // return MM/AAAA
        int mesAtual = data.getMonth() + 2;
        int anoAtual = data.getYear() + 1900;
        String mesAtualStr = String.valueOf(mesAtual);
        if (mesAtualStr.length() == 1) {
            mesAtualStr = "0" + mesAtualStr;
        }
        return mesAtualStr + "/" + anoAtual;
    }

    public static String concatenarListaInteger(List<Integer> lista){
        String retorno = "";
        for(Integer i : lista){
            retorno += "|"+i;
        }
        return retorno.replaceFirst("\\|", "");
    }


    public static String getValuesFromListSelectItems(List<SelectItem> list, boolean isString) {
        StringBuilder codigos = new StringBuilder("");
        if (list != null) {
            for (SelectItem item : list) {
                if (isString) {
                    codigos.append("'").append(item.getValue()).append("'").append(",");
                } else {
                    codigos.append(item.getValue()).append(",");
                }
            }
            if (codigos.length() > 1) {
                codigos.deleteCharAt(codigos.length() - 1);
            }
        }
        return codigos.toString();
    }

    public static void forceDirectory(final String pathDestino) {
        File fDestino = new File(pathDestino);
        if (!fDestino.exists()) {
            try {
                FileUtils.forceMkdir(fDestino);
            } catch (Exception e) {
                Logger.getLogger(Uteis.class.getName()).log(Level.SEVERE, e.getMessage(), e);
            }
        }
    }

    public static String obterRequestURL(WebServiceContext webServiceContext)throws Exception{
        ServletContext servletContext =  (ServletContext) webServiceContext.getMessageContext().get(MessageContext.SERVLET_CONTEXT);
        MessageContext msgCtxt = webServiceContext.getMessageContext();
        HttpServletRequest request = (HttpServletRequest)msgCtxt.get(MessageContext.SERVLET_REQUEST);
        String hostName = request .getServerName();
        return request.getRequestURL().substring(0, request.getRequestURL().lastIndexOf("/"));
    }

    public static String obterStringArquivoTexto(File file){
        try {
            FileReader fileReader = new FileReader(file);
            BufferedReader reader = new BufferedReader(fileReader);
            String data = null;
            StringBuilder txt = new StringBuilder();
            while ((data = reader.readLine()) != null) {
                txt.append(data);
            }
            fileReader.close();
            reader.close();
            return txt.toString();
        }catch (Exception ex){
            return "erro";
        }
    }

    public static String salvarArquivo(String nomeArquivo, String dados, String destinationFile) throws IOException {
        return salvarArquivo(nomeArquivo, dados, destinationFile, false);
    }

    public static String salvarArquivo(String nomeArquivo, String dados, String destinationFile, boolean extensaoTxt) throws IOException {

        final String prefixo = destinationFile;
        String caminho = prefixo;

        String sufixo = extensaoTxt ? nomeArquivo+".txt" : nomeArquivo;
        caminho += sufixo;

//        final String dir = caminho.substring(0, caminho.lastIndexOf(File.separator));
        forceDirectory(caminho);
        File arquivoDados = new File(caminho);
        if (arquivoDados.exists()) {
            arquivoDados.delete();
        }

        arquivoDados.createNewFile();
        FileWriter fw = new FileWriter(arquivoDados);
        BufferedWriter bw = new BufferedWriter(fw);
        String[] linhas = dados.split("#");
        for(String linha : linhas){
            bw.write(linha);
            bw.newLine();
        }

        //fecha os recursos
        bw.close();
        fw.close();
        System.out.println("LOGIN -> gravei DADOS -> " + caminho);
        return arquivoDados.getAbsoluteFile().toString();
    }
    public static java.util.Date getDate(String data, String padrao) throws Exception {
        return getDate(data, padrao, true);
    }

    public static java.util.Date getDate(String data, String padrao, boolean lenient ) throws Exception {
        SimpleDateFormat format = new SimpleDateFormat(padrao);
        format.setLenient(lenient);
        return new java.util.Date(format.parse(data).getTime());
    }

    public static DiasDaSemana getDiaDaSemanaEnum(Date data) throws Exception {
        if (data == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        return DiasDaSemana.getDiasDaSemana(cal.get(Calendar.DAY_OF_WEEK));
    }

    public static Integer mediana(List<Integer> m) {
        int middle = m.size() / 2;
        if (m.size() % 2 == 1) {
            return m.get(middle);
        } else {
            return (m.get(middle - 1) + m.get(middle)) / 2;
        }
    }

    public static Double obterPosicaoData(Date dia, Date inicio, Date fim){
        try {
            long nrDiasGeral = Uteis.nrDiasEntreDatas(inicio, fim);
            long nrDiasAteData = Uteis.nrDiasEntreDatas(inicio, dia);
            Double perc = (nrDiasAteData*100.0)/nrDiasGeral;
            return perc > 100.0 ? 100.0 : perc;
        } catch (Exception e) {
            return 0.0;
        }
    }

        public static String getNomeAbreviandoSomentePrimeiroSobrenome(String nome) {
        if(!UteisValidacao.emptyString(nome) && nome.equals("COLABORADOR SITE")){
            return "Colaborador Site";
        }
        String nomes[] = nome.split(" ");
        if (nomes.length <= 1) {
            return nome;
        }
        int tamanho = nomes.length;
        String abv = nomes[0];

        for (int i = 1; i < tamanho - 1; i++) {
            if (nomes[i].length() > 3) {
                abv += " " + nomes[i].charAt(0) + ".";
                break;
            }
        }
        return abv;
    }

    public static List diferencaEntreListas(List listaMaior, List listaMenor) throws Exception{
        List listaRetorno = new ArrayList();
        for(Object o : listaMaior){
            if(!listaMenor.contains(o)){
                listaRetorno.add(o);
            }
        }
        return listaRetorno;
    }

    public static String removerEspacosInicioFimString(String texto) {
        if (!UteisValidacao.emptyString(texto)) {
            texto = rtrim(texto);
            texto = ltrim(texto);
        }
        return texto;
    }

    public static String rtrim(String toTrim) {
        char[] val = toTrim.toCharArray();
        int len = val.length;

        while (len > 0 && val[len - 1] <= ' ') {
            len--;
        }
        return len < val.length ? toTrim.substring(0, len) : toTrim;
    }

    public static String ltrim(String toTrim) {
        int st = 0;
        char[] val = toTrim.toCharArray();
        int len = val.length;

        while (st < len && val[st] <= ' ') {
            st++;
        }
        return st > 0 ? toTrim.substring(st, len) : toTrim;
    }
    /**
     * Adiciona o valor a esquerda da original qtd vezes.
     * @param valor
     * @param qtd
     * @return
     */
    public static String adicionarValorEsquerda(String valor, String original, int qtd){
        original = original == null ? "" : original;
        qtd = qtd - original.length();
        StringBuilder builder = new StringBuilder(original);
        while(qtd > 0){
            builder.insert(0, valor);
            qtd--;
        }
        return builder.toString();
    }

    /**
     * Realiza a substituição dos textos do mapara para o texto passado como parâmetro.
     * @param substitutos {@link Map} cujas as chaves serão os textos a serem substituidos, e o valor os textos substitutos.
     * @param texto Texto original.
     * @return
     */
    public static String substituirTexto(Map<String, String> substitutos, String texto){
        if(texto != null && !texto.isEmpty()){
            for(String chave : substitutos.keySet()){
                texto = texto.replaceAll(chave, substitutos.get(chave));
            }
        }
        return texto;
    }

    public static Integer contarSequenciaCaracterString(String documento, String seqCaracter) {
        String s = documento;
        int pos = -1;
        int contagem = 0;
        while (true) {
            pos = s.indexOf (seqCaracter, pos + 1);
            if (pos < 0) break;
            contagem++;
        }
        //            System.out.println("Total de Registros: " + contagem);
        return contagem;

    }

    public static String montarListaIN(List<?> valores){
        StringBuilder in = new StringBuilder();
        in.append(valores.get(0).toString());
        for(int i = 1 ; i < valores.size(); i++){
            in.append(",");
            in.append(valores.get(i));
        }
        return in.toString();
    }


    public static boolean isNumeroValido(String valor){
        try{
            if ((valor == null) || (valor.trim().equals(""))){
                return false;
}
            Pattern pat = Pattern.compile("[0-9]+");
            Matcher mat = pat.matcher(valor);
            return mat.matches();
        }catch (Exception e){
            e.printStackTrace();
        }
        return false;
    }

    public static boolean digitosIguais(String dig){
        for(char e : dig.toCharArray()){
            if(e == dig.toCharArray()[0]){
                continue;
            } else {
                return false;
            }
        }
        return true;
    }
    public static int numeroQuebraLinhasString(String value) {
        String breakLine = "\n";
        int numberOfBreakLines = value.split(breakLine).length - 1;
        numberOfBreakLines += value.startsWith(breakLine) ? 1 : 0;
        numberOfBreakLines += value.endsWith(breakLine) ? 1 : 0;
        return numberOfBreakLines;
    }

    public static String diferencaEntreDatas(Date maior,Date menor) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date dataDe = sdf.parse("2014-02-05");
            Date dataAte = sdf.parse("2015-04-05");

            long diferencaSegundos = (dataAte.getTime() - dataDe.getTime()) / (1000);
            long diferencaMinutos = (dataAte.getTime() - dataDe.getTime()) / (1000 * 60);
            long diferencaHoras = (dataAte.getTime() - dataDe.getTime()) / (1000 * 60 * 60);
            long diferencaDias = (dataAte.getTime() - dataDe.getTime()) / (1000 * 60 * 60 * 24);
            long diferencaMeses = (dataAte.getTime() - dataDe.getTime()) / (1000 * 60 * 60 * 24) / 30;
            long diferencaAnos = ((dataAte.getTime() - dataDe.getTime()) / (1000 * 60 * 60 * 24) / 30) / 12;
            return ((diferencaAnos * 364 * 24) + (diferencaMeses * 30 * 24) + (diferencaDias * 24) + diferencaHoras) + ":" + diferencaMinutos + ":" + diferencaSegundos;
        }catch (Exception ex){
            return "00:00:00";
        }
    }

    public static boolean excluirArquivosDoDiretorio(String caminho){
        List<Map<String, File>> fotosTemp = new ArrayList();
        try {
            fotosTemp = FileUtilities.readListFilesDirectory(caminho);
            for (Map<String, File> map : fotosTemp) {
                Set<String> s = map.keySet();
                for (String fileName : s) {
                    File arquivo = map.get(fileName);
                    arquivo.delete();
                }
            }
            return true;
        } catch (Exception ex) {
            return false;
        }
    }

    public static File repurarFotoPessoaTemp(String key,Integer codigoClienteLogado){
        File diretorio = new File(PropsService.getPropertyValue(
                           PropsService.diretorioArquivos) + "/fotos_pessoa_temp");
        String caminhoFoto = String.format(diretorio.getPath()+"/%s@%s@%s.jpg", key, codigoClienteLogado.toString(), 0);
        boolean existeFoto = existeFoto(caminhoFoto);
        File foto = null;
        if (existeFoto) {
            foto = new File(caminhoFoto);
        }
        return foto;
    }

    public static boolean existeFoto(String caminhoFoto){
        File foto = new File(caminhoFoto);
        return foto.exists();
    }

    public static String getChaveCriptoImpressaoServlet() {
        return "IMPRESSAO";
    }

    public static String getPath(HttpServletRequest request) throws Exception{
        if (request == null) {
            request = (HttpServletRequest) context().getExternalContext().getRequest();
        }
        StringBuffer url = request.getRequestURL();
        URL u = new URL(url.toString());
        String path;
        if (UteisValidacao.emptyNumber(u.getPort()) || u.getPort() < 0) {
            path = u.getProtocol() + "://" + u.getHost() + request.getContextPath();
        } else {
            path = u.getProtocol() + "://" + u.getHost() + ":" + u.getPort() + request.getContextPath();
        }
        return path;
    }

    private static boolean servidorLocal(final String url) {
        return (url.contains("10.1.1") || url.contains("192.168") || url.contains("dyndns.org"));
    }

    public static String descobrirEnumFasesSigla(String descricao)throws Exception{
        String retorno = "";

        if (FasesCRMEnum.AGENDAMENTO.getDescricao().equals(descricao)) {
            retorno = FasesCRMEnum.AGENDAMENTO.getSigla();
        }else if(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla();
        }else if(FasesCRMEnum.VINTE_QUATRO_HORAS.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla();
        }else if(FasesCRMEnum.RENOVACAO.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.RENOVACAO.getSigla();
        }else if(FasesCRMEnum.DESISTENTES.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.DESISTENTES.getSigla();
        }else if(FasesCRMEnum.VISITANTES_ANTIGOS.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.VISITANTES_ANTIGOS.getSigla();
        }else if(FasesCRMEnum.EX_ALUNOS.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.EX_ALUNOS.getSigla();
        }else if(FasesCRMEnum.INDICACOES.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.INDICACOES.getSigla();
        }else if(FasesCRMEnum.CONVERSAO_INDICADOS.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.CONVERSAO_INDICADOS.getSigla();
        }else if(FasesCRMEnum.CONVERSAO_AGENDADOS.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla();
        }else if(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla();
        }else if(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla();
        }else if(FasesCRMEnum.CONVERSAO_DESISTENTES.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla();
        }else if(FasesCRMEnum.CONVERSAO_PASSIVO.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.CONVERSAO_PASSIVO.getSigla();
        }else if(FasesCRMEnum.GRUPO_RISCO.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.GRUPO_RISCO.getSigla();
        }else if(FasesCRMEnum.VENCIDOS.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.VENCIDOS.getSigla();
        }else if(FasesCRMEnum.POS_VENDA.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.POS_VENDA.getSigla();
        }else if(FasesCRMEnum.FALTOSOS.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.FALTOSOS.getSigla();
        }else if(FasesCRMEnum.ANIVERSARIANTES.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.ANIVERSARIANTES.getSigla();
        }else if(FasesCRMEnum.ULTIMAS_SESSOES.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.ULTIMAS_SESSOES.getSigla();
        }else if(FasesCRMEnum.SEM_AGENDAMENTO.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.SEM_AGENDAMENTO.getSigla();
        }else if(FasesCRMEnum.AGENDAMENTOS_LIGACOES.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.AGENDAMENTOS_LIGACOES.getSigla();
        }else if(FasesCRMEnum.INDICACOES_SEM_CONTATO.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.INDICACOES_SEM_CONTATO.getSigla();
        }else if(FasesCRMEnum.PASSIVO.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.PASSIVO.getSigla();
        }else if(FasesCRMEnum.ALUNO_GYMPASS.getDescricao().equals(descricao)){
            retorno = FasesCRMEnum.ALUNO_GYMPASS.getSigla();
        }else if(FasesCRMEnum.CRM_EXTRA.getDescricao().equals(descricao)){
           retorno = FasesCRMEnum.CRM_EXTRA.getSigla();
        }else if(FasesCRMEnum.LEADS_ACUMULADAS.getDescricao().equals(descricao)){
           retorno = FasesCRMEnum.LEADS_ACUMULADAS.getSigla();
        }else if(FasesCRMEnum.LEADS_HOJE.getDescricao().equals(descricao)){
           retorno = FasesCRMEnum.LEADS_HOJE.getSigla();
        }

        return retorno;
    }

    public static String getPathPortApp(final String protocol, final String urlAplicacao, final String contextPath) throws Exception {
        URL u = new URL(urlAplicacao);
        String path;
        if (u.getPort() > 0) {
            path = String.format("%s//%s:%s%s", protocol, u.getHost(), u.getPort(), contextPath);
        } else {
            path = String.format("%s//%s%s", protocol, u.getHost(), contextPath);
        }
        return path;
    }



    public static java.util.Date getDate(Integer dia, Integer mes, Integer ano) throws Exception {
        Calendar cal = Calendario.getInstance();
        cal.setTime(negocio.comuns.utilitarias.Calendario.hoje());
        cal.set(Calendar.DAY_OF_MONTH, dia);
        cal.set(Calendar.MONTH, mes);
        cal.set(Calendar.YEAR, ano);
        return cal.getTime();
    }

    public static Integer converteHoraParaInteiro(String horaFormatada) throws Exception{
//        verificando se está no tamnho certou 5 é porque conta com o ":"
        if(horaFormatada.equals("00:00") || horaFormatada.length() < 5  ){
            throw new ConsistirException("Hora inserida inválida.");
        }
        String [] dados = horaFormatada.split(":");
        Integer hora  = Integer.parseInt(dados[0].toString());
        Integer minuto = Integer.parseInt(dados[1].toString());

        Integer horaConvertido = hora * 60;

        Integer valorCompleto = horaConvertido + minuto;
        return valorCompleto;
    }

    public static String converteMinutosParaHora(int minutos){
        Integer min1 = minutos / 60;
        Integer min2 = minutos % 60;

        String horaFormatada = "";
        String minutoFormatado = "";
        if (min1 < 10) {
            horaFormatada = "0"+min1.toString();
        }else{
            horaFormatada = min1.toString();
        }

        if (min2 <= 0) {
            minutoFormatado = "0"+min2.toString();
        }else{
            minutoFormatado = min2.toString();
        }

        return horaFormatada + ":" + minutoFormatado;
    }

    public static boolean compareNullOrZero(Number n1, Number n2){
        if(UteisValidacao.emptyNumber(n1) && UteisValidacao.emptyNumber(n2)){
            return true;
        }
        return !UteisValidacao.emptyNumber(n1) && n1.equals(n2);
    }

    public static String getUrlAplicacao() {
        return PropsService.getPropertyValue(PropsService.urlAplicacao);
    }

    public static String getUrlServicoNotaFiscal() {
        return PropsService.getPropertyValue(PropsService.urlServicoNotaFiscal);
    }

    public static String getUrlOAMD() {
        return getUrlOamd();
    }

    public static String substituirPartePalavra(String nomenovo, String nomeAntigo, String parte){
        if(nomenovo.contains(parte)){
            nomenovo = nomenovo.substring(0, nomenovo.indexOf(parte));
            if(nomeAntigo.contains(parte)){
                nomenovo = nomenovo + (nomeAntigo.substring(nomeAntigo.indexOf(parte), nomeAntigo.length()));
            }
        }
        return nomenovo;
    }

    public static Date obterProximoDiaUtil(Date dia , List<Date> feriados) {
        for(int i = 1; i < 20; i++){
            if(verificarDiaUtil(dia, feriados)){
                return dia;
            }
            dia = Uteis.somarDias(dia, 1);
        }
        return dia;
    }

    public static boolean verificarDiaUtil(Date dia , List<Date> feriados) {
        if(feriados.contains(getDataComHoraZerada(dia))){
            return false;
        }
        return Calendario.getInstance(dia).get(Calendar.DAY_OF_WEEK) != Calendar.SATURDAY
                && Calendario.getInstance(dia).get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY;
    }

    public static void replaceAll(StringBuffer sb, Pattern pattern, String replacement) {
        Matcher m = pattern.matcher(sb);
        while(m.find()) {
            sb.replace(m.start(), m.end(), replacement);
        }
    }

    public static String removerUltimosCaracteres(String texto, int quantidade){
        if(texto != null && texto.length() > 0){
            return texto.substring(0, texto.length() - quantidade);
        }else{
            return texto;
        }
    }

    public static String removerUltimoCaractere(String texto){
        return Uteis.removerUltimosCaracteres(texto, 1);
    }

    /**
     * Método usado Pegar a idade com meses
     * @autor Eder Cristian da Silva
     * @param dataNasc
     * @return String com "ano-mes"
     * @throws ParseException
     */
    public static String CalculaIdadeComMeses(String dataNasc) {

        Calendar dataHoje = GregorianCalendar.getInstance();
        int diaHoje = dataHoje.get(Calendar.DAY_OF_MONTH);
        int mesHoje = dataHoje.get(Calendar.MONTH) + 1;
        int anoHoje = dataHoje.get(Calendar.YEAR);


        String[] quebraDN = dataNasc.split("-");
        int diaNascimento = Integer.valueOf(quebraDN[2]);
        int mesNascimento = Integer.valueOf(quebraDN[1]);
        int anoNascimento = Integer.valueOf(quebraDN[0]);

        String strNiver = anoHoje+"-"+mesNascimento+"-"+diaNascimento;
        Calendar calNiver = Calendar.getInstance();
        try {
            calNiver.setTime(new SimpleDateFormat("yyyy-MM-dd").parse(strNiver));
        } catch (ParseException ex) {
            Logger.getLogger(Uteis.class.getName()).log(Level.SEVERE, null, ex);
        }

        int anos = (dataHoje.getTimeInMillis() < calNiver.getTimeInMillis()) ? (anoHoje - anoNascimento - 1):anoHoje - anoNascimento;
        int meses;
        int dias;

        meses = mesHoje - mesNascimento;
        if (meses > 0) {
            if (diaHoje < diaNascimento) {
                meses--;
            }
        } else if (meses < 0) {
            meses = 12 + meses;

            if (diaHoje < diaNascimento) {
                meses--;
            }
        } else {
            if (diaHoje < diaNascimento) {
                meses = 11;
            }
        }
        return anos + "-" + meses;
    }

    public static boolean objectIntoArray(final Object o, final Object... values) {
        for (int i = 0; i < values.length; i++) {
            if (values[i].equals(o)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Calcula o valor do objeto que deveria ser uma String validando se o mesmo é nulo ou não
     *
     * @param objeto Objeto que será validado
     * @return Uma instância do objeto informado [(String) objeto] ou <b>null</b> caso o objeto ou a classe sejam nulos
     * ou caso o objeto não seja uma instância de {@code String} ou caso o objeto seja uma String vazia
     * @see Uteis#nullableOfString(Object, String)
     */
    public static String nullableOfString(Object objeto) {
        return nullableOfString(objeto, null);
    }

    /**
     * Calcula o valor do objeto que deveria ser uma String validando se o mesmo é nulo ou não
     *
     * @param objeto       Objeto que será validado
     * @param valorDefault Valor padrão que será retornado caso o objeto esteja nulo ou vazio ou não seja uma instância de String
     * @return Uma instância do objeto informado [(String) objeto] ou <b>null</b> caso o objeto ou a classe sejam nulos
     * ou caso o objeto não seja uma instância de {@code String} ou caso o objeto seja uma String vazia
     * @see Uteis#nullableOfTyped(Object, Class, Object)
     */
    public static String nullableOfString(Object objeto, String valorDefault) {
        final String valorTipado = nullableOfTyped(objeto, String.class, valorDefault);
        return StringUtils.isBlank(valorTipado) ? valorDefault : valorTipado;
    }

    /**
     * Valida se um objeto ou sua classe de instancia são nulos.
     *
     * @param objeto Objeto que será validado
     * @param classe Classe da qual o objeto deveria ser instancia
     * @param <T>    Tipo da classe da qual o objeto deveria ser instancia
     * @return Uma instância do objeto informado [(Class&lt;T&gt;) objeto] ou <b>null</b> caso o objeto ou a classe sejam nulos.
     * @see Uteis#nullableOfTyped(Object, Class, Object)
     */
    public static <T> T nullableOfTyped(Object objeto, Class<T> classe) {
        return nullableOfTyped(objeto, classe, null);
    }

    /**
     * Valida se um objeto ou sua classe de instancia são nulos.
     *
     * @param objeto       Objeto que será validado
     * @param classe       Classe da qual o objeto deveria ser instancia
     * @param valorDefault Valor padrão que será retornado caso nenhum valor seja encontrado
     * @param <T>          Tipo da classe da qual o objeto deveria ser instancia
     * @return Uma instância do objeto informado [(Class&lt;T&gt;) objeto] ou o <b>valorDefault</b> caso o objeto ou a classe sejam nulos.
     */
    public static <T> T nullableOfTyped(Object objeto, Class<T> classe, T valorDefault) {
        if (objeto == null || classe == null) {
            return valorDefault;
        }

        if (classe.isInstance(objeto)) {
            return classe.cast(objeto);
        }

        Logger.getLogger(Uteis.class.getSimpleName()).log(Level.SEVERE, "O objeto " + objeto.toString() + " não é instância da classe " + classe.getSimpleName());
        return valorDefault;
    }

    public static String retirarAcentuacaoEPontuacao(String prm){
        String nova = "";
        if (UteisValidacao.emptyString(prm)) {
            return "";
        }
        nova = retirarAcentuacao(prm);
        nova = nova.replaceAll( "[^A-Za-z0-9- ]","");
        return (nova);
    }

    public static String salvarArquivoXML(String nomeArquivo, String dados, String destinationFile) throws IOException {

        final String prefixo = destinationFile;
        String caminho = prefixo;

        caminho += nomeArquivo;

//        final String dir = caminho.substring(0, caminho.lastIndexOf(File.separator));
        forceDirectory(caminho);
        File arquivoDados = new File(caminho);
        if (arquivoDados.exists()) {
            arquivoDados.delete();
        }

        arquivoDados.createNewFile();
        FileWriter fw = new FileWriter(arquivoDados);
        BufferedWriter bw = new BufferedWriter(fw);
        String[] linhas = dados.split("</array>");
        for(String linha : linhas){
            bw.write(linha);
            bw.newLine();
        }

        //fecha os recursos
        bw.close();
        fw.close();
        System.out.println("LOGIN -> gravei DADOS -> " + caminho);
        return arquivoDados.getAbsoluteFile().toString();
    }

    public static String formatarCpfCnpj(String cpfCNPJ, boolean somenteNumeros) {
        if (UteisValidacao.emptyString(cpfCNPJ)) {
            return "";
        } else {
            String semMascara = Uteis.removerMascara(cpfCNPJ);
            if (somenteNumeros) {
                return semMascara;
            }

            if (semMascara.length() == 14) {
                return Uteis.aplicarMascara(semMascara, "99.999.999/9999-99");
            } else if (semMascara.length() == 11) {
                return Uteis.aplicarMascara(semMascara, "999.999.999-99");
            } else {
                return cpfCNPJ;
            }
        }
    }

    public static String removerCaracteresNaoAscii(String texto) {
        return texto.replaceAll("[^\\x00-\\x7F]", "");
    }

    public static String removerCaracteresEspeciaisEAcentuacao(String texto) {
        if (texto == null) return "";
        return Normalizer.normalize(texto, Normalizer.Form.NFD)
                .replaceAll("\\p{InCombiningDiacriticalMarks}+", "")
                .replaceAll("[^a-zA-Z0-9 ]", "")
                .trim();
    }

    public static String removerCaracteresDeControle(String texto) {
        try {
            for (char c : texto.toCharArray()) {
                if (((int) c) <= 31) {
                    return texto.replace(c, ' ').replaceAll(" ", "");
                }
            }
            return texto;
        }catch (Exception e) {
            return texto;
        }
    }

    public static String splitFromListInteger(final List<Integer> lista) {
        if (UteisValidacao.emptyList(lista)) {
            return "";
        }
        String retorno = "";
        for (Integer inte : lista) {
            retorno += (inte + ",");
        }
        int i = retorno.lastIndexOf(",");
        if (i != -1) {
            retorno = retorno.substring(0, i);
        }
        return retorno;
    }

    public static boolean isValidEmailAddressRegex(String email) {
        boolean isEmailIdValid = false;
        if (email != null && email.length() > 0) {
            String expression = "^[\\w\\.-]+@([\\w\\-]+\\.)+[A-Z]{2,4}$";
            Pattern pattern = Pattern.compile(expression, Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(email);
            if (matcher.matches()) {
                isEmailIdValid = true;
            }
        }
        return isEmailIdValid;
    }

    public static String readLineByLineJava8(String filePath)
    {
        StringBuilder contentBuilder = new StringBuilder();

        try (Stream<String> stream = Files.lines( Paths.get(filePath), StandardCharsets.UTF_8))
        {
            stream.forEach(s -> contentBuilder.append(s).append("\n"));
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }

        return contentBuilder.toString();
    }

    public static String getValidadeMMYYYY(Integer month, Integer year, boolean comBarra) {
        String barra = comBarra ? "/" : "";
        return Formatador.formatarValorNumerico((double) month, "00") + barra + Formatador.formatarValorNumerico((double) year, "0000");
    }

    public static Integer getTempoAguardarRemessa() {
        try {
            return Integer.parseInt(PropsService.getPropertyValue(PropsService.tempoAguardarRemessa));
        } catch (Exception ex) {
            ex.printStackTrace();
            return 10000;
        }
    }

    public static String getHorarioEnvioRemessaGetnet() {
        try {
            return PropsService.getPropertyValue(PropsService.horarioEnvioRemessaGetnet);
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public static String obterMensagemRetornoTransacaoOnline(TipoTransacaoEnum tipoTransacaoEnum, String codRetorno) {
        try {
            if (codRetorno == null || codRetorno.trim().isEmpty()) {
                return "Erro não especificado";
            }

            if (tipoTransacaoEnum.equals(TipoTransacaoEnum.CIELO_ONLINE)) {
                return CieloECommerceRetornoEnum.valueOff(codRetorno).getDescricao();
            } else if (tipoTransacaoEnum.equals(TipoTransacaoEnum.GETNET_ONLINE)) {
                return StatusAntifraudeGetNetEnum.valueOff(codRetorno).getDescricao();
            } else if (tipoTransacaoEnum.equals(TipoTransacaoEnum.E_REDE)) {
                return ERedeRetornoEnum.valueOff(codRetorno).getDescricao();
            } else if (tipoTransacaoEnum.equals(TipoTransacaoEnum.STONE_ONLINE)) {
                return ERedeRetornoEnum.valueOff(codRetorno).getDescricao();
            } else if (tipoTransacaoEnum.equals(TipoTransacaoEnum.VINDI)) {

            }
            return "Erro não especificado";
        } catch (Exception ex) {
            return "Erro não especificado";
        }
    }

    public static boolean isEmailGratuito(String email) {
        try {
            email = email.toLowerCase();
            if (email.contains("@gmail.") ||
                    email.contains("@hotmail.") ||
                    email.contains("@outlook.") ||
                    email.contains("@protonmail.") ||
                    email.contains("@tutanota.") ||
                    email.contains("@lockbin.") ||
                    email.contains("@yandex.") ||
                    email.contains("@zoho.") ||
                    email.contains("@aol.") ||
                    email.contains("@yahoo.") ||
                    email.contains("@mail.") ||
                    email.contains("@qq.") ||
                    email.contains("@gmx.") ||
                    email.contains("@icloud.") ||
                    email.contains("@uol.") ||
                    email.contains("@bol.") ||
                    email.contains("@ig.") ||
                    email.contains("@elude.") ||
                    email.contains("@aim.") ||
                    email.contains("@bigstring.") ||
                    email.contains("@gawab.") ||
                    email.contains("@inbox.") ||
                    email.contains("@lavabit.") ||
                    email.contains("@zapak .") ||
                    email.contains("@facebook.") ||
                    email.contains("@myspace.") ||
                    email.contains("@hotpop.") ||
                    email.contains("@fastmail.")) {
               return true;
            }
        } catch (Exception e) {
        }
        return false;
    }

    public static boolean isBuscarRemotoExtratoRetornoRemessa() {
        return PropsService.isTrue(PropsService.BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA);
    }

    public static String encodeValue(String value) {
        try {
            return URLEncoder.encode(value, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException ex) {
            throw new RuntimeException(ex.getCause());
        }
    }


    public static boolean isEnviarEmailErroCreditoDCC() {
        return PropsService.isTrue(PropsService.enviarEmailErroCreditoDCC);
    }

    public static boolean telefoneValido(String telefone){
        Pattern p = Pattern.compile(regexTelefone);
        Matcher m = p.matcher(telefone);
        boolean valido = m.find();
        if(valido){
            String[] invalidos = {"000000000", "00000000", "111111111", "11111111", "222222222", "22222222", "333333333", "33333333",
                    "444444444", "44444444", "555555555", "55555555", "666666666", "66666666", "777777777", "77777777", "888888888", "88888888", "999999999", "99999999", "123456789", "12345678"};
            for(String numInvalido : invalidos){
                if(telefone.contains(numInvalido)){
                    return false;
                }
            }
        }
        return valido;
    }

    public static String limparTelefonesInvalidos(String telefones){
        String[] tels = telefones.split("<br/>");
        if(tels.length > 1){
            String telsValidos = "";
            for(String tel : tels){
                if(Uteis.telefoneValido(tel)){
                    if(telsValidos.isEmpty()){
                        telsValidos = tel;
                    }else{
                        telsValidos+="<br/>"+tel;
                    }
                }
            }
            return telsValidos;
        }
        return telefones;
    }

    public static String executeRequestSintetico(String urlRequest, String corpo, Map<String, String> headers) throws IOException {

        StringEntity entity = new StringEntity(corpo, "UTF-8");
        HttpPost httpPost = new HttpPost(urlRequest);
        httpPost.setEntity(entity);
        httpPost.setHeader("Content-Type", "application/json;");
        for (String key: headers.keySet()) {
            httpPost.setHeader(key, headers.get(key));
        }

        HttpClient client = ExecuteRequestHttpService.createConnector();
        HttpResponse response = client.execute(httpPost);

        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());
        return responseBody;
    }

    public static String getUrlDiscovery(String servico) throws Exception {
        JSONObject caminhos = BetaTestersService.caminhos();
        String url = caminhos.optString(servico);
        if (UteisValidacao.emptyString(url)) {
            throw new Exception(" o discovery não retornou a url, verifique se está preenchida no banco corretamente");
        }
        return url;
    }

    public static boolean isAmbienteDesenvolvimentoTeste() {
        return PropsService.isTrue(PropsService.ambienteDesenvolvimentoTeste);
    }

    public static boolean isMockAragornCards(){
        return PropsService.isTrue(PropsService.mockAragornCards);
    }

    public static boolean isHabilitarTodasFuncionalidadesBeta() {
        return PropsService.isTrue(PropsService.habilitarFuncionalidadesBeta);
    }

    public static String getChaveEmpresaSwarmParaSendy() {
        return PropsService.getPropertyValue(PropsService.ambienteSwarmParaSendy);
    }

    public static String getDomainMail() {
        return PropsService.getPropertyValue(PropsService.domainMail);
    }
    public static String getTokenMailgun() {
        return PropsService.getPropertyValue(PropsService.tokenMailgun);
    }
    public static String getHabilitaMarketing() {return PropsService.getPropertyValue(PropsService.habilitaMarketing); }

    public static String getHabilitaClubeDeBenfeficios() {
        return PropsService.getPropertyValue(PropsService.habilitaClubeDeBeneficios);
    }
    public static String getServidorMemCached() {return PropsService.getPropertyValue(PropsService.servidorMemCached); }
    public static Integer getTempoSegundosExpiracaoCacheBanners() {
        try {
            return UteisValidacao.emptyString(PropsService.getPropertyValue(PropsService.tempoSegundosExpirarCacheBanners)) ? 60000 : Integer.parseInt(PropsService.getPropertyValue(PropsService.tempoSegundosExpirarCacheBanners));
        } catch (Exception e) {
            return 60000;
        }
    }

    public static String getUrlOamd() {
        try {
            return PropsService.getPropertyValue(PropsService.urlOamd);
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public static String getUrlAPI() {
        try {
            return PropsService.getPropertyValue(PropsService.urlZWAPI);
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public static String getUrlOamdSegura() {
        try {
            return PropsService.getPropertyValue(PropsService.urlOamdSegura);
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public static void updateOamdImagemTodasEmpresas(final String key) {
        final String sql = "select codigo from empresa order by codigo";
        ResultSet rs;
        try {
            Uteis.logar("Atualizar logomarcas da empresa: %s no OAMD", key);
            try (Connection c = new DAO().obterConexaoEspecifica(key)) {
                reloadOamdImagem(key);
                rs = SuperFacadeJDBC.criarConsulta(sql, c);
                while (rs.next()) {
                    reloadOamdImagem(key, rs.getInt(1));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void reloadOamdImagem(final String key, final Integer codEmpresa) {
        try {
            Uteis.logar("Executando imagenslogoapp - %s/imagenslogoapp?key=%s&empresa=%s&reload=true",
                    getUrlOamd(), key, codEmpresa);
            ExecuteRequestHttpService.executeHttpRequest(getUrlOamd() + "/imagenslogoapp?key=" + key
                    + "&empresa=" + codEmpresa + "&reload=true", null);
        } catch (Exception e) {
            Uteis.logar(e, Empresa.class);
        }

    }

    public static void reloadOamdImagem(final String key) {
        try {
            Uteis.logar("Executando reloadOamdImagem - %s/prest/config/reloadLogo/%s", getUrlOamd(), key);
            ExecuteRequestHttpService.executeRequest(getUrlOamd() + "/prest/config/reloadLogo/" + key, null);
        } catch (Exception e) {
            Uteis.logar(e, Empresa.class);
        }

    }


    public static void reloadTreinoCacheUsuario(final String key) {
        try {
            String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreinoWeb);
            Uteis.logar("Executando reloadTreinoCacheUsuario - %s/prest/config/reload-usuario?key=%s", urlTreino, key);
            ExecuteRequestHttpService.executeRequest(urlTreino + "/prest/config/reload-usuario?key=" + key, null);
        } catch (Exception e) {
            Uteis.logar(e, Uteis.class);
        }

    }

    public static List arrayJsonToList(JSONArray array){
        return new ArrayList(){{
            for(int i = 0; i < array.length(); i++){
                add(array.get(i));
            }
        }};
    }

    public static String arrayJsonToString(JSONArray array, String separador) {
        String str = "";
        for (int i = 0; i < array.length(); i++) {
           str += separador + array.get(i);
        }
        return str.replaceFirst(separador, "");
    }

    public static String obterMensagemDetalhada(Exception e) {
        //trata a mensagem de erro que pode vir em inglês ou em português
        if (e.getMessage().contains("ERRO: atualização") || e.getMessage().contains("ERROR: update")) {

            String tabelaErro = "";
            String foreignKeyErro = "";

            //descobrir se mensagem está em português ou inglês
            if (e.getMessage().contains("tabela")) {
                //descobrir tabela
                String[] msg = e.getMessage().split("em tabela");
                String[] msgSplit = msg[1].split("viola");
                tabelaErro = msgSplit[0];

                //descobrir foreignKey
                String[] msg2 = e.getMessage().split("em tabela");
                String[] msgSplit2 = msg[1].split("pela tabela");
                foreignKeyErro = msgSplit2[1];

            } else if (e.getMessage().contains("table")) {
                //descobrir tabela
                String[] msg = e.getMessage().split("on table");
                String[] msgSplit = msg[1].split("violates");
                tabelaErro = msgSplit[0];

                //descobrir foreignKey
                String[] msg2 = e.getMessage().split("on table");
                foreignKeyErro = msg2[2];
            }
            return ("Este registro não pode ser excluído da tabela " + tabelaErro + ", pois ainda possui vínculo com a tabela " + foreignKeyErro);
        } else {
            return e.getMessage();
        }
    }

    public static OperadorasExternasAprovaFacilEnum obterOperadora(String numeroCartao) {
        try {
            return obterOperadoraComException(numeroCartao);
        } catch (Exception ex) {
            ex.printStackTrace();
            return OperadorasExternasAprovaFacilEnum.MASTERCARD;
        }
    }

    public static OperadorasExternasAprovaFacilEnum obterOperadoraComException(String numeroCartao) throws Exception {
        ValidaBandeira.Bandeira bandeiraCard = ValidaBandeira.buscarBandeira(numeroCartao.replace(" ", ""));
        return Enum.valueOf(OperadorasExternasAprovaFacilEnum.class, String.valueOf(bandeiraCard).toUpperCase());
    }

    public static String getUrlWebHookDiscordEnotas() {
        return PropsService.getPropertyValue(PropsService.urlWebHookDiscordEnotas);
    }

    public static String getUrlWebHookDiscordEnotasHabilitar() {
        return PropsService.getPropertyValue(PropsService.urlWebHookDiscordEnotasHabilitar);
    }

    public static String getIp(HttpServletRequest request){
        String ip = request.getHeader("X-FORWARDED-FOR");

        if( ip == null){
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if( ip == null){
            ip = request.getHeader("HTTP_CLIENT_IP");
        }

        if (ip == null) {
            ip = request.getRemoteAddr();
        }

        return ip;
    }

    public static String getParamJsonString(JSONObject json, String nomeParam) {
        try {
            return json.getString(nomeParam);
        } catch (Exception ex) {
            return "";
        }
    }

    public static Integer getParamJsonInt(JSONObject json, String nomeParam) {
        try {
            return json.getInt(nomeParam);
        } catch (Exception ex) {
            return 0;
        }
    }

    public static Integer getQtdLimitePactoPay() {
        try {
            return converterInteiro(PropsService.getPropertyValue(PropsService.qtdLimitePactoPay), 50);
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return 0;
    }

    public static String formatarCEP(String cep, boolean somenteNumeros) {
        if (UteisValidacao.emptyString(cep)) {
            return "";
        }

        if (somenteNumeros) {
            return Uteis.removerMascara(cep);
        } else {
            return Formatador.formatarString("#####-###", cep);
        }
    }

    public static void submitExecutor(Runnable task) {
        executorService.submit(task);
    }

    public static boolean isExecutorRunning() {
        return !(executorService.isShutdown() || executorService.isTerminated());
    }

    public static void destroyExecutor() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                // Passo 3: Interromper as tarefas em execução se o tempo limite for atingido
                List<Runnable> droppedTasks = executorService.shutdownNow();
                logar("Tarefas pendentes: " + droppedTasks.size());
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static void finalizarExecutor(int minutosAguardar) {
        int numeroVerificacoes = minutosAguardar * 2; // duas por minuto
        while (isExecutorRunning() && numeroVerificacoes > 0) {
            logar(null, "@@ Executor ainda rodando,aguardar 30 segundos...");
            try {
                Thread.sleep(30000);
            } catch (InterruptedException ex) {
                Logger.getLogger(Uteis.class.getName()).log(Level.SEVERE, null, ex);
            }
            numeroVerificacoes--;
        }
        if (isExecutorRunning()) {
            logar(null, "Executor ainda rodando, finalizando...");
            destroyExecutor();
        }
        logar(null, "Executor finalizado");
    }


    public static String getAuthZwSecret() throws SecretException {
        try {
            if (UteisValidacao.emptyString(authZwSecret)) {
                String authSecretZWPath = PropsService.getPropertyValue(PropsService.AUTH_SECRET_ZW_PATH);
                authZwSecret = Uteis.readLineByLineJava8(authSecretZWPath).replace("\n", "").replace("\r", "");
            }
            if (UteisValidacao.emptyString(authZwSecret)) {
                Uteis.logarDebug("Auth ZW Secret vazia");
                throw new Exception("auth secret vazia");
            }
            return authZwSecret;
        } catch (Exception e) {
            throw new SecretException(e);
        }
    }

    public static void clearAuthZwSecret() {
        authZwSecret = null;
    }

    public static String secretKeyword() throws SecretException {
        try {
            if (UteisValidacao.emptyString(secretKeyword)) {
                String secretKeywordPath = PropsService.getPropertyValue(PropsService.KEYWORD_PATH);
                secretKeyword = Uteis.readLineByLineJava8(secretKeywordPath).replace("\n", "").replace("\r", "");
            }
            if (UteisValidacao.emptyString(secretKeyword)) {
                Uteis.logarDebug("Keyword vazia");
                throw new Exception("Keyword vazia");
            }
            return secretKeyword;
        } catch (Exception e) {
            throw new SecretException(e);
        }
    }

    public static String getUserMailFacilitePay() {
        return PropsService.getPropertyValue(PropsService.userMailFacilitePay);
    }

    public static String getDomainMailFacilitePay() {
        return PropsService.getPropertyValue(PropsService.domainMailFacilitePay);
    }

    public static String getPasswordMailFacilitePay() {
        return PropsService.getPropertyValue(PropsService.passwordMailFacilitePay);
    }

    public static String getSMSChaveFacilitePay() {
        return PropsService.getPropertyValue(PropsService.smsChaveFacilitePay);
    }

    public static String getSMSTokenFacilitePay() {
        return PropsService.getPropertyValue(PropsService.smsTokenFacilitePay);
    }

    public static String getTokenBitlyFacilitePay() {
        return PropsService.getPropertyValue(PropsService.tokenBitlyFacilitePay);
    }

    public static String getUrlAPIBitlyV4() {
        return PropsService.getPropertyValue(PropsService.urlAPIBitlyV4);
    }

    public static boolean isHabilitarNicho() {
        return PropsService.isTrue(PropsService.habilitarNicho);
    }

    public static boolean isHabilitarRecursoPadraoTelaCliente() {
        return PropsService.isTrue(PropsService.habilitarRecursoPadraoTelaCliente);
    }

    public static boolean isInstanciaZW() {
        String instanceName = INSTANCE_NAME.toLowerCase();
        return !(instanceName.contains("cad-") || instanceName.contains("zaw-"));
    }

    public static boolean habilitarCacheInitNicho(){
        return PropsService.isTrue(PropsService.habilitarNicho);
    }

    public static int validadeCacheNichoEmMinutos() {
        String value = PropsService.getPropertyValue(PropsService.validadeCacheNichoEmMinutos);
        int valueDefault = 2880;
        if (value != null && value.equals("@VALIDADE_CACHE_NICHO_EM_MINUTOS@")) {
            return valueDefault;
        } else {
            try {
                return Integer.parseInt(value);
            } catch (Exception e) {
                return valueDefault;
            }
        }
    }

    public static org.json.JSONObject extractField(String jsonString, String labelToFind) {
        String filterLabelToFind = labelToFind; // Substitua pelo valor desejado
        org.json.JSONObject jsonObject = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonString);

            JsonNode resultNode = jsonNode.path("result");
            if (resultNode.isObject()) {
                JsonNode matchingNode = findNodeByFilterLabel(resultNode, filterLabelToFind);
                jsonObject = convertJsonNodeToJsonObject(matchingNode);
                if (matchingNode != null) {
                    System.out.println("Campo encontrado: " + matchingNode);
                } else {
                    System.out.println("Campo não encontrado.");
                }
            }
            return jsonObject;

        } catch (IOException e) {
            e.printStackTrace();
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    private static org.json.JSONObject convertJsonNodeToJsonObject(JsonNode jsonNode) throws JSONException {
        return new org.json.JSONObject(jsonNode.toString());
    }

    public static String findNodeById(String jsonString , String fieldIdToFind, String field){
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonString);

            JsonNode fieldNode = jsonNode.path("result").path(field);
            if (fieldNode.isObject() && fieldNode.has("items") && fieldNode.get("items").isArray()) {
                String value = findValueById(fieldNode.get("items"), fieldIdToFind);
                if (value != null) {
                    return  value;
                } else {
                    System.out.println("ID não encontrado.");
                    return null;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
    private static JsonNode findNodeByFilterLabel(JsonNode resultNode, String filterLabelToFind) {
        Iterator<Map.Entry<String, JsonNode>> fields = resultNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            JsonNode fieldNode = entry.getValue();

            if (fieldNode.isObject()) {
                String filterLabel = fieldNode.path("filterLabel").asText();
                if (filterLabel.equals(filterLabelToFind)) {
                    return fieldNode; // Retorna o nó correspondente ao objeto
                }
            }
        }
        return null; // Retorna null se não encontrar correspondência
    }

    private static String findValueById(JsonNode itemsNode, String idToFind) {
        Iterator<JsonNode> items = itemsNode.elements();
        while (items.hasNext()) {
            JsonNode item = items.next();
            if (item.has("ID") && item.get("ID").asText().equals(idToFind)) {
                return item.get("VALUE").asText();
            }
        }
        return null;
    }

    public static String mascararDado(String valor, Integer qtdUltimosDigitosNaoMascarar) {
        return mascararDado(valor, qtdUltimosDigitosNaoMascarar, false);
    }

    public static String mascararDado(String valor, Integer qtdDigitosNaoMascarar, boolean mascararUltimosDigitos) {
        if (UteisValidacao.emptyString(valor) ||
                UteisValidacao.emptyString(valor.trim())) {
            return valor;
        }
        Integer tamanhoTotal = valor.length();
        Integer totalMascarar = (tamanhoTotal > qtdDigitosNaoMascarar ? (tamanhoTotal - qtdDigitosNaoMascarar) : tamanhoTotal);

        String valorMascarar = valor.substring(0, totalMascarar);
        if (mascararUltimosDigitos) {
            valorMascarar = valor.substring(tamanhoTotal - totalMascarar);
        }

        String valorApresentar = valor.replace(valorMascarar, "");

        Integer atual = 0;
        String valorMascarado = "";
        for(int i = 0; i < valorMascarar.length(); ++i) {
            ++atual;
            if (atual <= totalMascarar) {
                valorMascarado += "*";
            } else {
                valorMascarado += valorMascarar.charAt(i);
            }
        }

        return mascararUltimosDigitos ? (valorApresentar + valorMascarado) : (valorMascarado + valorApresentar);
    }

    public static String enviarSolicitacaoEncurtarLink(JSONObject json, String oAuthToken) throws Exception {
        HttpClient httpClient = ExecuteRequestHttpService.createConnector();
        String url = "https://api-ssl.bitly.com/v4/shorten";
        HttpPost post = new HttpPost(url);
        post.setHeader("Authorization", oAuthToken);
        StringEntity entity = new StringEntity(json.toString(), "UTF8");
        entity.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
        post.setEntity(entity);
        HttpResponse response = httpClient.execute(post);
        String responseString = new BasicResponseHandler().handleResponse(response);
        JSONObject jsonObject = new JSONObject(responseString);
        String shortLink = jsonObject.getString("link");
        return shortLink;
    }

    public static void main(String[] args) {
        //c89bd5098f88524de0cb9c08ed679085/8m5FRrfxx0y*VJImuxezGg==/4XNJp*jycs2Q70wwOhkmcoy0FgdvIGxLNmWQb3xrBr0=.jpeg
        System.out.println(Uteis.desencriptarAWS("8m5FRrfxx0y*VJImuxezGg=="));
    }

    public static String formatarNome(String nome) {
        if (nome == null || nome.trim().isEmpty()) {
            return nome;
        }
        // Converte o nome para minsculas e depois capitaliza cada palavra
        String[] palavras = nome.toLowerCase().split(" ");
        StringBuilder nomeFormatado = new StringBuilder();
        for (String palavra : palavras) {
            if (!palavra.isEmpty()) {
                nomeFormatado.append(Character.toUpperCase(palavra.charAt(0)))
                        .append(palavra.substring(1))
                        .append(" ");
            }
        }
        return nomeFormatado.toString().trim();
    }

    public static void reloadTreinoCacheUsuario(final String key, final UsuarioVO usuarioVO) {
        try {
            String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreinoWeb);
            String urlCompleta = (urlTreino + "/prest/config/reload-usuario?key=" + key + "&u=" + (usuarioVO != null ? usuarioVO.getCodigo() : 0));
            Uteis.logar("Executando reloadTreinoCacheUsuario -" + urlCompleta);
            ExecuteRequestHttpService.executeRequest(urlCompleta, null);
        } catch (Exception e) {
            Uteis.logar(e, Uteis.class);
        }
    }

    // ==================== MÉTODOS DE SEGURANÇA PARA PREVENÇÃO DE SQL INJECTION ====================

    // Lista centralizada de caracteres e comandos perigosos para SQL injection
    private static final String[] CARACTERES_PERIGOSOS_SQL = {
        "'", "\"", ";", "--", "/*", "*/", "\\", "union", "select", "insert",
        "update", "delete", "drop", "exec", "execute", "script", "javascript"
    };

    private static final String[] COMANDOS_PROIBIDOS_SQL = {
        "delete", "update", "insert", "drop", "alter", "create", "truncate",
        "exec", "execute", "sp_", "xp_", "union", "script", "javascript",
        "vbscript", "onload", "onerror", "eval", "expression", "import",
        "include", "require", "system", "shell", "cmd", "powershell",
        "wget", "curl", "nc", "netcat", "telnet", "ssh", "ftp",
        "grant", "revoke", "commit", "rollback", "savepoint",
        "information_schema", "pg_", "mysql", "sys.", "master.",
        "msdb.", "tempdb.", "model.", "northwind.", "pubs.",
        "declare", "cast", "convert", "char(", "ascii(", "substring(",
        "waitfor", "delay", "benchmark(", "sleep(", "pg_sleep(",
        "load_file(", "into outfile", "into dumpfile", "load data",
        "bulk insert", "openrowset", "opendatasource", "openquery",
        "openxml", "sp_oacreate", "sp_oamethod", "sp_oagetproperty",
        "sp_oasetproperty", "sp_oadestroy", "sp_oastop", "sp_oastart"
    };

    /**
     * Método centralizado para verificar caracteres perigosos em strings
     * @param value valor a ser verificado
     * @return true se contém caracteres perigosos, false caso contrário
     */
    private static boolean contemCaracteresPerigososSql(String value) {
        if (value == null) return true;
        String valueLower = value.toLowerCase();
        for (String perigoso : CARACTERES_PERIGOSOS_SQL) {
            if (valueLower.contains(perigoso)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Valida se o nome do campo é seguro para uso em SQL
     * @param fieldName nome do campo a ser validado
     * @return true se o campo é válido, false caso contrário
     */
    public static boolean isValidFieldName(String fieldName) {
        if (UteisValidacao.emptyString(fieldName)) {
            return false;
        }
        // Permitir apenas letras, números e underscore, deve começar com letra ou underscore
        return fieldName.matches("^[a-zA-Z_][a-zA-Z0-9_]*$");
    }

    /**
     * Valida se o valor string é seguro para uso em SQL
     * @param value valor a ser validado
     * @return true se o valor é válido, false caso contrário
     */
    public static boolean isValidStringValue(String value) {
        return !contemCaracteresPerigososSql(value);
    }

    /**
     * Valida se uma string de busca é segura para uso em SQL
     * @param searchString string de busca a ser validada
     * @return true se a string é válida, false caso contrário
     * @deprecated Use {@link #isValidStringValue(String)} instead
     */
    @Deprecated
    public static boolean isValidSearchString(String searchString) {
        return isValidStringValue(searchString);
    }

    /**
     * Valida se os campos SQL são seguros para uso
     * @param fields campos SQL a serem validados
     * @return true se os campos são válidos, false caso contrário
     */
    public static boolean isValidSqlFields(String fields) {
        if (fields == null) {
            return false;
        }
        // Permitir apenas nomes de campos válidos (letras, números, underscore, vírgula, espaço, ponto, asterisco, parênteses)
        if (!fields.matches("^[a-zA-Z0-9_,\\s.*()]+$")) {
            return false;
        }
        // Verificar palavras-chave perigosas usando método centralizado
        return !contemCaracteresPerigososSql(fields);
    }

    /**
     * Valida se uma string representa um número inteiro válido
     * @param value valor a ser validado
     * @return true se é um inteiro válido, false caso contrário
     */
    public static boolean isValidInteger(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        try {
            Integer.parseInt(value.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Valida se uma string representa uma data válida usando a API moderna do Java 8+
     * Aceita formatos ISO (YYYY-MM-DD) e alguns formatos localizados
     * @param date data a ser validada
     * @return true se a data é válida, false caso contrário
     */
    public static boolean isValidDate(String date) {
        if (date == null || date.trim().isEmpty()) {
            return false;
        }

        String dateStr = date.trim();

        // Verificar se não contém caracteres perigosos primeiro
        if (contemCaracteresPerigososSql(dateStr)) {
            return false;
        }

        // Tentar parsear como LocalDate (formato ISO: YYYY-MM-DD)
        try {
            java.time.LocalDate.parse(dateStr);
            return true;
        } catch (java.time.format.DateTimeParseException e) {
            // Tentar outros formatos usando DateTimeFormatter
            for (java.time.format.DateTimeFormatter formatter : Arrays.asList(DATE_FORMATTERS)) {
                try {
                    java.time.LocalDate.parse(dateStr, formatter);
                    return true;
                } catch (java.time.format.DateTimeParseException ex) {
                    // Continua tentando outros formatos
                }
            }
        }

        return false; // Nenhum formato funcionou
    }

    /**
     * Valida a cláusula WHERE para prevenir SQL Injection
     * @param clausulaWhere a cláusula WHERE a ser validada
     * @return a cláusula validada ou null se contiver comandos perigosos
     */
    public static String validarClausulaWhere(String clausulaWhere) {
        if (UteisValidacao.emptyString(clausulaWhere)) {
            return clausulaWhere;
        }

        String clausulaLower = clausulaWhere.toLowerCase().trim();

        // Verificar se contém comandos proibidos
        for (String comando : COMANDOS_PROIBIDOS_SQL) {
            if (clausulaLower.contains(comando)) {
                return null; // Comando perigoso encontrado
            }
        }

        // Verificar padrões suspeitos de SQL injection
        if (clausulaLower.matches(".*['\";].*--.*") ||
            clausulaLower.matches(".*['\";].*#.*") ||
            clausulaLower.matches(".*['\";].*/\\*.*") ||
            clausulaLower.contains("/*") ||
            clausulaLower.contains("*/") ||
            clausulaLower.contains("--") ||
            clausulaLower.contains("@@") ||
            clausulaLower.contains("0x")) {
            return null; // Padrão suspeito encontrado
        }

        return clausulaWhere; // Cláusula considerada segura
    }
    public static double calcularProRataEncerramentoContratoDia(ContratoVO contratoPrm, double valor) {
        if (contratoPrm == null || contratoPrm.getPlano() == null || contratoPrm.getPlano().getContratosEncerramDia() == null) {
            throw new IllegalArgumentException("Contrato ou plano inválido para cálculo de pro-rata encerramento contrato dia");
        }

        int diaVencimento = Calendario.getDia(
                contratoPrm.getDataPrimeiraParcela() == null
                        ? Calendario.hoje()
                        : contratoPrm.getDataPrimeiraParcela()
        );
        int diaVencimentoContrato = Calendario.getDia(
                contratoPrm.getPlano().getContratosEncerramDia()
        );
        double valorDia = Uteis.arredondarForcando2CasasDecimais(valor / 30);
        double valorAtual = valor;
        if (diaVencimentoContrato > diaVencimento) {
            int diasExtras = diaVencimentoContrato - diaVencimento;
            return (valorAtual + (valorDia * diasExtras));
        } else if (diaVencimentoContrato < diaVencimento) {
            int diasReduzidos = diaVencimento - diaVencimentoContrato;
            return (valorAtual - (valorDia * diasReduzidos));
        }
        return valorAtual;
    }

}

package negocio.comuns.utilitarias;

import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;

import static servlet.arquitetura.RecuperadorParametrosServlet.obterParametro;
import static servlet.arquitetura.RecuperadorParametrosServlet.obterParametroString;

/**
 * Responsável conter propriedades do datatable Jquery, quando a propriedade <b>bServerSide</b> é true.
 *
 * <AUTHOR>
 * @since 29/01/2019
 */
public class DataTableServerSideProperties {

    private String sEcho;
    private Integer offset;
    private Integer limit;
    private String clausulaLike;
    private Integer colOrdenar;
    private String dirOrdenar;

    public DataTableServerSideProperties(HttpServletRequest request) {
        sEcho = obterParametroString(request.getParameter("sEcho"));
        offset = obterParametro(request.getParameter("iDisplayStart"));
        limit = obterParametro(request.getParameter("iDisplayLength"));
        clausulaLike = obterParametroString(request.getParameter("sSearch"));
        colOrdenar = obterParametro(request.getParameter("iSortCol_0"));
        dirOrdenar = obterParametroString(request.getParameter("sSortDir_0"));
    }

    public boolean isDataTableServerSidePropertiesAtivado() {
        return isParametrosOrdenacaoPresentes() && isParametrosPaginacaoPresentes();
    }

    public boolean isParametrosOrdenacaoPresentes() {
        return colOrdenar != null && StringUtils.isNotBlank(dirOrdenar);
    }

    public boolean isParametrosPaginacaoPresentes() {
        return offset != null && limit != null;
    }

    /**
     * @return TRUE - representa quando foi solicitado 'itens por página - TODOS'.
     */
    public boolean isSemLimitAndOffSet() {
        return offset == 0 && limit == -1;
    }

    public boolean isClausulaLikePresente() {
        return StringUtils.isNotBlank(clausulaLike);
    }

    public String getsEcho() {
        return sEcho;
    }

    public void setsEcho(String sEcho) {
        this.sEcho = sEcho;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getClausulaLike() {
        return clausulaLike;
    }

    public void setClausulaLike(String clausulaLike) {
        this.clausulaLike = clausulaLike;
    }

    public Integer getColOrdenar() {
        return colOrdenar;
    }

    public void setColOrdenar(Integer colOrdenar) {
        this.colOrdenar = colOrdenar;
    }

    public String getDirOrdenar() {
        return dirOrdenar;
    }

    public void setDirOrdenar(String dirOrdenar) {
        this.dirOrdenar = dirOrdenar;
    }
}

package negocio.comuns.utilitarias;

import controle.arquitetura.FuncionalidadeSistemaEnum;

/**
 * Enum para configurar as urls da PactoPay
 * <AUTHOR>
 * @since 23/01/2023
 */
public enum UrlRedirectPAYEnum {
    GESTAO_DE_TRANSACOES("transacoes/credito-online", FuncionalidadeSistemaEnum.GESTAO_DE_TRANSACOES);

    private final String uri;
    private final FuncionalidadeSistemaEnum funcionalidadeSistemaEnum;

    /**
     */
    UrlRedirectPAYEnum(String uri, FuncionalidadeSistemaEnum funcionalidadeSistemaEnum) {
        this.uri = uri;
        this.funcionalidadeSistemaEnum = funcionalidadeSistemaEnum;
    }

    /**
     * @param uri Path/URI principal para acessar o recurso/funcionalidade
     * @param auxiliarPaths Paths auxiliar da url
     * @param funcionalidadeSistemaEnum Enum referente a funcionalidade antiga
     */
    UrlRedirectPAYEnum(String uri, FuncionalidadeSistemaEnum funcionalidadeSistemaEnum, String ...auxiliarPaths) {
        this(getAuxiliarPaths(auxiliarPaths) + uri, funcionalidadeSistemaEnum);
    }

    private static String getAuxiliarPaths(String ...auxiliarPaths) {
        StringBuilder auxiliarPath = new StringBuilder();
        for (String path : auxiliarPaths) {
            auxiliarPath.append(path).append("/");
        }
        return auxiliarPath.toString();
    }

    public static String getUriByFuncSisEnum(FuncionalidadeSistemaEnum funcionalidadeSistemaEnum) {
        for (UrlRedirectPAYEnum url: values()) {
            if (url.getFuncionalidadeSistemaEnum().equals(funcionalidadeSistemaEnum)) {
                return url.getUri();
            }
        }
        return null;
    }

    public String getUri() {
        return uri;
    }

    public FuncionalidadeSistemaEnum getFuncionalidadeSistemaEnum() {
        return funcionalidadeSistemaEnum;
    }
}



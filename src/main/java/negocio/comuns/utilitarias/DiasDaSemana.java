package negocio.comuns.utilitarias;

public enum DiasDaSemana {
	DOMINGO, SEGUNDA, TERCA, QUARTA, QUINTA, SEXTA, SABADO;

	/**
	 * Retorna o int correspondente a constante obtida em Calendar.DayOfWeek. <br/>
	 * Ex: DiasDaSemana.DOMINGO = Calendar.SUNDAY = 1
	 * @return
	 */
	public int getDayOfWeekEmCalendar() {
		switch (this) {
		case DOMINGO:
			return 1;
		case SEGUNDA:
			return 2;
		case TERCA:
			return 3;
		case QUARTA:
			return 4;
		case QUINTA:
			return 5;
		case SEXTA:
			return 6;
		case SABADO:
			return 7;
		}
		return 0;
	}
	public static DiasDaSemana getDiasDaSemana(Integer codigo) {
		switch (codigo) {
		case 1:
			return DOMINGO;
		case 2:
			return SEGUNDA;
		case 3:
			return TERCA;
		case 4:
			return QUARTA;
		case 5:
			return QUINTA;
		case 6:
			return SEXTA;
		case 7:
			return SABADO;
		}
		return null;
	}
}

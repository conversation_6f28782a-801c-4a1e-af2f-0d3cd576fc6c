/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ExportFormatter;
import annotations.arquitetura.ExportFunctionExecute;
import annotations.arquitetura.Lista;
import org.apache.commons.lang.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperVO;
import servicos.bi.exportador.formatadores.FormatadorEnum;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class UtilReflection {

    public static boolean exists(Class classe, final String name) {
        try {
            if (classe.getDeclaredField(name) != null) {
                return true;
            }
        } catch (Exception ex) {
            //Uteis.logar(ex, UtilReflection.class);
            //não existe
        }
        return false;
    }

    public static List<Field> getFields(Class<?> clazz) {
        List<Field> listFields = new ArrayList<Field>();
        Field[] fields = null;
        while (clazz != null) {
            fields = clazz.getDeclaredFields();
            listFields.addAll(Arrays.asList(fields));
            clazz = clazz.getSuperclass();
        }
        return listFields;
    }

    /*public static Field[] getArrayFieldsSuperClass(Class<?> clazz) {
        List<Field> listFields = new ArrayList<Field>();
        Field[] fields = null;
        fields = clazz.getDeclaredFields();
        listFields.addAll(Arrays.asList(fields));
        clazz = clazz.getSuperclass();
        fields = clazz.getDeclaredFields();
        listFields.addAll(Arrays.asList(fields));
        return fields;
    }*/


    public static Field getField(Class classe, String fieldName){
        List<Field> listaField= getFields(classe);
        for(Field field : listaField){
            if (field.getName().equals(fieldName))
                return field;
        }
        return null;
    }

    public static void setValor(Object obj, final Object valor, final String nomeAtributo, boolean verificarExistencia)
            throws ClassNotFoundException,
            NoSuchMethodException,
            IllegalAccessException,
            IllegalArgumentException,
            InvocationTargetException,
            NoSuchFieldException, ParseException {

        if (valor != null) {

            Class clazz = obj.getClass();
            Class partypes[] = new Class[1];
            Object[] parvalues = new Object[1];

            Method metodo = null;
            Field attr = null;

            if (verificarExistencia && !exists(clazz, nomeAtributo)) {
                return;
            }

            /*try{
            attr = clazz.getDeclaredField(nomeAtributo);
            }catch (Exception e){
                if (e.getClass().equals(NoSuchFieldException.class)){
                    attr = clazz.getSuperclass().getDeclaredField(nomeAtributo);
                }
            }*/
            attr = getField(clazz, nomeAtributo);

            if (attr.getType() == Double.class) {
                partypes[0] = Double.class;
                parvalues[0] = Double.valueOf(valor.toString());
            }else if (attr.getType() == Date.class){
                partypes[0] = Date.class;
                if (valor instanceof String){
                    SimpleDateFormat sdfComHora = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    SimpleDateFormat sdfSemHora = new SimpleDateFormat("yyyy-MM-dd");
                    SimpleDateFormat sdfHora = new SimpleDateFormat("HH:mm:ss");
                    if (valor.toString().contains(":")){
                        if (valor.toString().length() == 8){
                            parvalues[0] = sdfHora.parse((String)valor);
                        }else{
                            parvalues[0] = sdfComHora.parse((String)valor);
                        }

                    }else {
                        parvalues[0] = sdfSemHora.parse((String)valor);
                    }
                }else{
                    parvalues[0] = valor;
                }
            } else if (attr.getType() == Integer.class) {
                partypes[0] = Integer.class;
                parvalues[0] = Integer.valueOf(valor.toString());

            } else {
                partypes[0] = valor.getClass();
                parvalues[0] = valor;
            }

            metodo = clazz.getMethod("set" + firstLetterUpper(nomeAtributo), attr.getType());
            metodo.invoke(obj, parvalues);
        }
    }

    public static Object getValor(Object obj, String nomeAtributo) {
        return getValor(obj, nomeAtributo, null);
    }

    public static Object getValor(Object obj, String nomeAtributo,
            String classesCallStack) {
        try {
            Class clazz = Class.forName(obj.getClass().getCanonicalName());

            Method metodo = null;

            Field attr = clazz.getDeclaredField(nomeAtributo);

            /*if (attributeComposite(attr.getType(), clazz)){
             return "";
             }*/
            if ((classesCallStack != null)
                    && (classesCallStack.contains(attr.getType().getCanonicalName()))) {
                return "";
            }

            metodo = clazz.getMethod("get" + firstLetterUpper(nomeAtributo), null);
            Object retorno = metodo.invoke(obj, null);
            if (retorno != null) {
                attr.setAccessible(true);
                if (attr.getType() == Double.class) {
                    return Double.valueOf(retorno == null ? "0.0" : retorno.toString());

                } else if (attr.getType() == Integer.class) {
                    return Integer.valueOf(retorno == null ? "0" : retorno.toString());
                } else if (attr.getType() == String.class) {
                    return retorno == null ? "" : retorno.toString();
                } else if ((retorno instanceof SuperVO) && (attr.isAnnotationPresent(ChaveEstrangeira.class))) {
                    List<String> atributos = getListAttributes(retorno.getClass());
                    Object object = retorno;
                    classesCallStack += object.getClass().getCanonicalName();
                    for (String atributo : atributos) {
                        Object o = getValor(object, atributo, classesCallStack);
                        String valor = o == null ? "" : o.toString();
                        //Uteis.logar(null, object + "." + atributo + " -> " + valor);
                        String aux = retorno.toString() + (!valor.toString().isEmpty() ? valor.toString() + "|" : "");
                        retorno = aux;
                    }
                    classesCallStack = null;
                    return retorno;
                } else if ((retorno instanceof List) && (attr.isAnnotationPresent(Lista.class))) {
                    if (!((List) retorno).isEmpty()) {
                        if (((List) retorno).get(0) instanceof SuperVO) {
                            List lista = (List) retorno;
                            for (Object object : lista) {
                                classesCallStack += object.getClass().getCanonicalName();
                                List<String> atributos = getListAttributes(object.getClass());
                                for (String atributo : atributos) {
                                    Object o = getValor(object, atributo, classesCallStack);
                                    String valor = o == null ? "" : o.toString();
                                    //Uteis.logar(null, object + "." + atributo + " -> " + valor);
                                    String aux = retorno.toString() + (!valor.toString().isEmpty() ? valor.toString() + "|" : "");
                                    retorno = aux;
                                }
                            }
                            classesCallStack = null;
                            return retorno;
                        }
                    }
                } else {
                    return retorno == null ? "" : retorno.toString();
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, UtilReflection.class);
            return null;
        }
        return null;
    }

    public static Object getValorObject(Object obj, String nomeAtributo) {
        try {
            Class clazz = Class.forName(obj.getClass().getCanonicalName());

            Method metodo = null;

            Field attr = clazz.getDeclaredField(nomeAtributo);

            metodo = clazz.getMethod("get" + firstLetterUpper(nomeAtributo), null);
            Object retorno = metodo.invoke(obj, null);
            if (retorno != null) {
                attr.setAccessible(true);
                if (attr.getType() == Double.class) {
                    return Double.valueOf(retorno == null ? "0.0" : retorno.toString());

                } else if (attr.getType() == Integer.class) {
                    return Integer.valueOf(retorno == null ? "0" : retorno.toString());
                } else if (attr.getType() == String.class) {
                    return retorno == null ? "" : retorno.toString();
                } else if (attr.getType() == List.class) {
                    return retorno == null ? new ArrayList() : retorno;
                } else {
                    return retorno;
                }
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    public static List<String> getListAttributes(Class classe) {
        List<String> lista = new ArrayList();
        Class clazz;
        try {
            clazz = Class.forName(classe.getCanonicalName());
            Field[] attr = clazz.getDeclaredFields();
            for (int i = 0; i < attr.length; i++) {
                Field field = attr[i];
                if (!Modifier.isStatic(field.getModifiers())) {
                    lista.add(field.getName());
                }
            }
        } catch (ClassNotFoundException ex) {
            Logger.getLogger(UtilReflection.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;

    }

    public static List<Field> getListFields(Class classe) {
        List<Field> lista = new ArrayList();
        Class clazz;
        try {
            clazz = Class.forName(classe.getCanonicalName());
            Field[] attr = null;

            attr = clazz.getDeclaredFields();

            for (int i = 0; i < attr.length; i++) {
                Field field = attr[i];
                lista.add(field);
            }
        } catch (ClassNotFoundException ex) {
            Logger.getLogger(UtilReflection.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;

    }

    public static List<ReflectionType> getFields(List l) throws Exception {
        if (!l.isEmpty()) {
            Object o = l.get(0);
            List<Field> fields = UtilReflection.getListFields(o.getClass());
            List<ReflectionType> lista = new ArrayList();
            for (Field f : fields) {
                if ((!f.isAnnotationPresent(annotations.arquitetura.NaoControlarLogAlteracao.class))
                        && (f.getType() == String.class
                        || f.getType() == Boolean.class
                        || f.getType() == Short.class
                        || f.getType() == Integer.class
                        || f.getType() == Long.class
                        || f.getType() == Float.class
                        || f.getType() == Double.class
                        || f.getType() == Byte.class
                        || f.getType() == Date.class
                        || f.getType() == boolean.class
                        || f.getType() == short.class
                        || f.getType() == int.class
                        || f.getType() == long.class
                        || f.getType() == float.class
                        || f.getType() == byte.class)) {
                    lista.add(new ReflectionType(f));
                }
            }
            return lista;
        } else {
            return null;
        }
    }

    public static List<ReflectionType> getFields(Map<String, String> labels, List l) throws Exception {
        if (!l.isEmpty()) {

            if (labels.isEmpty()) {
                return getFields(l);
            }

            List<ReflectionType> lista = new ArrayList();
            Object o = l.get(0);
            Class clazz = o.getClass();

            Set<String> attrs = labels.keySet();

            for (String attr : attrs) {
                if(attr.contains(".")){
                    try {
                        Field f = retrieveObjectValue(o, attr);
                        FormatadorEnum formatadorEnum = getFormatador(f);
                        ReflectionType reflectionType = new ReflectionType(attr, f.getType(), formatadorEnum);
                        reflectionType.setFunctionType(getFunctionExecute(f));
                        lista.add(reflectionType);

                    } catch (NoSuchFieldException noSuchF) {
                        try {
                            Method m = retrieveObjectMethod(o, attr);
                            if (m != null) {
                                FormatadorEnum formatadorEnum = getFormatador(m);
                                lista.add(new ReflectionType(attr, m.getReturnType(), formatadorEnum));
                            }
                        } catch (Exception e) {
                        }
                    }
                } else {
                    try {
                        Field f = clazz.getDeclaredField(attr);
                        FormatadorEnum formatadorEnum = getFormatador(f);
                        ReflectionType reflectionType = new ReflectionType(f, formatadorEnum);
                        reflectionType.setFunctionType(getFunctionExecute(f));
                        lista.add(reflectionType);
                    } catch (NoSuchFieldException noSuchF) {
                        try {
                            Method m = clazz.getMethod("get" + firstLetterUpper(attr), null);
                            if (m != null) {
                                FormatadorEnum formatadorEnum = getFormatador(m);
                                lista.add(new ReflectionType(attr, m.getReturnType(), formatadorEnum));
                            }
                        } catch (Exception e) {
                        }

                    }
                }

            }
            return lista;
        } else {
            return null;
        }
    }

    private static Field retrieveObjectValue(Object obj, String property) throws NoSuchFieldException {
        if (property.contains(".")) {
            // we need to recurse down to final object
            String props[] = property.split("\\.");
            try {
                Object ivalue = null;

                Method m = obj.getClass().getMethod("get" + firstLetterUpper(props[0]), null);
                ivalue = m.invoke(obj);
                if (ivalue==null)
                    return null;
                return retrieveObjectValue(ivalue,property.substring(props[0].length()+1));
            } catch (Exception e) {
                throw new NoSuchFieldException(property);
            }
        } else {
           return obj.getClass().getDeclaredField(property);
        }
    }

    private static Method retrieveObjectMethod(Object obj, String property) throws NoSuchMethodException {
        if (property.contains(".")) {
            // we need to recurse down to final object
            String props[] = property.split("\\.");
            try {
                Object ivalue = null;

                Method m = obj.getClass().getMethod("get" + firstLetterUpper(props[0]), null);
                ivalue = m.invoke(obj);
                if (ivalue==null)
                    return null;
                return retrieveObjectMethod(ivalue,property.substring(props[0].length()+1));
            } catch (Exception e) {
                throw new NoSuchMethodException(property);
            }
        } else {
            return obj.getClass().getMethod("get" + firstLetterUpper(property), null);
        }
    }

    private static String getFunctionExecute(Field f) {
        for (Annotation annotacao : f.getAnnotations()) {
            if (annotacao instanceof ExportFunctionExecute) {
                ExportFunctionExecute a = (ExportFunctionExecute) annotacao;
                if(a.arq().length > 0 && !a.arq()[0].isEmpty()){
                    return String.format("%s(\"%s\")", a.function(), StringUtils.join(a.arq(), "\",\"")) ;
                }
                return String.format("%s()", a.function());
            }
        }
        return "";
    }

    public static FormatadorEnum getFormatador(Field field) {
        for (Annotation annotacao : field.getAnnotations()) {
            if (annotacao instanceof ExportFormatter) {
                return ((ExportFormatter) annotacao).formato();
            }
        }
        return null;
    }

    public static FormatadorEnum getFormatador(Method method) {
        for (Annotation annotacao : method.getAnnotations()) {
            if (annotacao instanceof ExportFormatter) {
                return ((ExportFormatter) annotacao).formato();
            }
        }
        return null;
    }

    public static List<ReflectionType> getFields(ResultSet rsConsulta) throws SQLException {
        ResultSetMetaData rsmd = rsConsulta.getMetaData();
        int numColumns = rsmd.getColumnCount();

        List<ReflectionType> fields = new ArrayList<>();
        //
        for (int i = 1; i < numColumns + 1; i++) {
            ReflectionType f = new ReflectionType(rsmd.getColumnName(i),
                    obtenhaClasse(Uteis.getColumnTypeName(rsmd.getColumnType(i), rsmd.getColumnDisplaySize(i))));
            fields.add(f);
        }
        return fields;
    }

    public static Class obtenhaClasse(String tipoColuna) {
        if (tipoColuna.equals("int4")) {
            return Integer.class;
        } else if (tipoColuna.equals("float4")) {
            return Double.class;
        } else if (tipoColuna.equals("date")) {
            return Date.class;
        } else if (tipoColuna.equals("timestamp")) {
            return Timestamp.class;        
        } else {
            return String.class;
        }
    }

    public static List<String> getListAttributes(Class classe, String iniciaCom, int lenNome, Class tipo) {
        List<String> lista = getListAttributes(classe);
        List<String> ret = new ArrayList();
        for (String att : lista) {
            try {
                if (att.startsWith(iniciaCom) && att.length() == lenNome
                        && classe.getDeclaredField(att).getType() == tipo) {
                    ret.add(att);
                }
            } catch (Exception e) {
            }
        }
        return ret;
    }

    public static boolean attributeComposite(Class classField, Class clazz) {
        try {
            Field[] attr = null;
            attr = clazz.getDeclaredFields();
            for (int i = 0; i < attr.length; i++) {
                Field f = attr[i];
                if (f.getType() == classField) {
                    return true;
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(UtilReflection.class.getName()).log(Level.SEVERE, null, ex);
        }

        return false;

    }

    public static String firstLetterUpper(String palavra) {
        return palavra.replaceFirst(String.valueOf(palavra.charAt(0)),
                String.valueOf(palavra.charAt(0)).toUpperCase());
    }

    public static Double sum(List list, final String attr) {
        Double result = 0.0;
        for (Object obj : list) {
            result += (Double) getValor(obj, attr);
        }
        return result;

    }

    public static Object invoke(Object obj, final String nomeMetodo) throws Exception {
        Class clazz = Class.forName(obj.getClass().getCanonicalName());
        Method metodo = null;
        metodo = clazz.getMethod(nomeMetodo, null);
        return metodo.invoke(obj, null);
    }

    public static Object invoke(Object instance, final String nomeMetodo,
                                final Class[] parameterTypes, final Object[] parameterValues) throws Exception {
        Class clazz = instance.getClass();
        Method metodo = clazz.getMethod(nomeMetodo, parameterTypes);
        return metodo.invoke(instance, parameterValues);
    }

    public static JSONObject toJSON(Object obj) throws JSONException {
        List<String> l = getListAttributes(obj.getClass());
        JSONObject json = new JSONObject();
        for (String attr : l) {
            json.put(attr, getValor(obj, attr));
        }
        l = null;
        return json;
    }

    public static boolean objetoNulo (Object instancia, String objetoVerificar) throws Exception {
        return (getValueFromTag(instancia, objetoVerificar) == null);
    }

    public static boolean objetoMaiorQueZero(Object instancia, String objetoVerificar) throws Exception {
        if (instancia == null){
            return false;
        }
        if ((objetoVerificar == null) || (objetoVerificar.trim().equals(""))){
            return (instancia != null) && (((Number)instancia).intValue() > 0);
        }
        Object ret = getValueFromTag(instancia, objetoVerificar);
        if (ret == null)
            return false;
        return ((Number)ret).intValue() > 0;
    }


    private static Object getValueFromTag (Object object, String tag) throws Exception {
        if (object == null)
            return null;
        String fields[] = tag.split("\\.");
        Object returnValue = object;
        for (int i=0; i < fields.length; i++) {
            if (returnValue == null)
                return null;
            String atributo = fields[i].substring(3,fields[i].length()-2);
            returnValue = getValue(atributo, returnValue);
        }
        return returnValue;
    }

    private static Object getValue (String atributo, Object returnValue) throws Exception{
        String nomeMetodoGet = "get" +  atributo.substring(0, 1).toUpperCase()	+  atributo.substring(1);
        Method metodoGet = returnValue.getClass().getMethod(nomeMetodoGet, new Class[]{});
        return metodoGet.invoke(returnValue);
    }

}

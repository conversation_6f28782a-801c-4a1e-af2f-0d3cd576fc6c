package negocio.comuns.utilitarias;


import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class ConstantesAcesso {

    public static final int CONSULTAR = 0;
    public static final int INCLUIR = 1;
    public static final int ALTERAR = 2;
    public static final int EXCLUIR = 3;
    public static final int PROXIMO = 5;
    public static final int ANTERIOR = 6;
    public static final int INICIALIZAR = 7;
    public static final int EXISTENTE = 8;
    public static final int NOVO = 9;
    public static final int ULTIMO = 10;
    public static final int PRIMEIRO = 11;
    public static final int EMITIRRELATORIO = 12;
    public static final int GRAVAR = 13;
    public static final String PERMISSAOTOTAL = "(" + CONSULTAR + ")" + "(" + INCLUIR + ")" + "(" + ALTERAR + ")" + "(" + EXCLUIR + ")" + "(" + NOVO + ")" + "(" + EMITIRRELATORIO + ")";
    public static final String PERMISSAOTOTALEXCETOEXCLUIR = "(" + CONSULTAR + ")" + "(" + INCLUIR + ")" + "(" + ALTERAR + ")" + "(" + NOVO + ")" + "(" + EMITIRRELATORIO + ")";
    public static final String PERMISSAOINCLUIR = "(" + NOVO + ")" + "(" + INCLUIR + ")";
    public static final String PERMISSAOCONSULTAR = "(" + CONSULTAR + ")" + "(" + EMITIRRELATORIO + ")" + "(" + PROXIMO + ")" + "(" + ANTERIOR + ")";
    public static final String PERMISSAOALTERAR = "(" + ALTERAR + ")";
    public static final String PERMISSAOEXCLUIR = "(" + EXCLUIR + ")";

    public static String getPermissaoValueString(String operacao) {
        if (isNotBlank(operacao)) {
            if (operacao.equals("PERMISSAOTOTAL")) {
                return PERMISSAOTOTAL;
            }
        }
        return null;
    }
}

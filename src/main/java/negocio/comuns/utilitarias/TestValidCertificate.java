package negocio.comuns.utilitarias;

import java.io.FileInputStream;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Date;

public class TestValidCertificate {
    public static void main(String[] args) {
        String pemFilePath = "C:\\Users\\<USER>\\Downloads\\InterAPI_Certificado.crt";

        try {
            CertificateFactory cf = CertificateFactory.getInstance("X.509");

            FileInputStream fis = new FileInputStream(pemFilePath);
            X509Certificate certificate = (X509Certificate) cf.generateCertificate(fis);
            fis.close();

            Date notBefore = certificate.getNotBefore();
            Date notAfter = certificate.getNotAfter();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedNotBefore = sdf.format(notBefore);
            String formattedNotAfter = sdf.format(notAfter);

            System.out.println("Valido de: " + formattedNotBefore);
            System.out.println("Valido at: " + formattedNotAfter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

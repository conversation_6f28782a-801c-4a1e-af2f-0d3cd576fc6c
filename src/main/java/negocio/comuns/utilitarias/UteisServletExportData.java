/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

import controle.arquitetura.SuperControle;
import edu.emory.mathcs.backport.java.util.Arrays;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.lang.management.ManagementFactory;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

/**
 *
 * <AUTHOR>
 */
public class UteisServletExportData extends Thread {

    private static final String VIRGULA = ",";
    private static final String LF = "\n";
    private static final String NULL = "null";
    private static final int ROWS_FLUSH = 10000;
    private UteisServlet uteisServlet = new UteisServlet();
    private String nomeHostPG;
    private String portaPG;
    private String superUserPG;
    private String senhaPG;
    private String nomeBanco;
    private String tables;
    private String formato;
    private String emailDest;
    private String pathFileDest;
    private String fullURLApp;
    private boolean exibirTitulo = true;
    private String fileNameFull;

    public UteisServletExportData(String nomeHostPG, String portaPG,
            String superUserPG, String senhaPG, String nomeBanco, String tables, String formato, boolean exibirTitulo,
            String emailDest, final String pathFileDest, final String fullURLApp) {
        this.nomeHostPG = nomeHostPG;
        this.portaPG = portaPG;
        this.superUserPG = superUserPG;
        this.senhaPG = senhaPG;
        this.nomeBanco = nomeBanco;
        this.tables = tables;
        this.formato = formato;
        this.exibirTitulo = exibirTitulo;
        this.emailDest = emailDest;
        this.pathFileDest = pathFileDest;
        this.fullURLApp = fullURLApp;
    }

    @Override
    public void run() {
        final int ROWS_LIMIT = 5000;
        try {
            final String[] tablesIgnore = new String[]{"bdatualizacao", "serierealizada"};
            if (formato != null && formato.startsWith("csv")) {
                Connection conAtual = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, nomeBanco);
                try {
                    String[] listaTabelasConsiderar = new String[]{"alunohorarioturma", "auladesmarcada",
                        "cartaocredito", "categoria", "categoriaproduto", "cheque", "cidade", "classificacao",
                        "cliente", "colaborador", "contrato", "contratocomposicao", "contratocondicaopagamento",
                        "contratoduracao", "contratohorario", "contratomodalidade", "contratomodalidadehorarioturma",
                        "contratomodalidadeprodutosugerido", "contratomodalidadeturma", "contratomodalidadevezessemana",
                        "contratooperacao", "email", "endereco", "estado", "horario", "horarioturma", "itemvendaavulsa",
                        "modalidade", "movpagamento", "movparcela", "movproduto", "movprodutoparcela",
                        "pagamentomovparcela", "pais", "pessoa", "produto", "profissao", "recibopagamento", "reposicao", "telefone",
                        "turma", "vendaavulsa"};

                    tables = Uteis.splitFromArray(listaTabelasConsiderar, true);

                    String condicaoTables = tables == null || tables.equals("*") ? "" : " and table_name in (" + tables + ")";

                    ResultSet rsBase = SuperFacadeJDBC.criarConsulta(
                            String.format("select table_name, table_schema  from information_schema.tables "
                            + "where table_catalog = '%s' and table_type = 'BASE TABLE' "
                            + "and table_schema in ('public', 'sch_studio') "
                            + condicaoTables + " "
                            + "order by table_name", nomeBanco), conAtual);

                    final String fileName = String.format("%s.csv", nomeBanco);;
                    fileNameFull = pathFileDest + File.separator + fileName;

                    BufferedWriter wr = Files.newBufferedWriter(Paths.get(fileNameFull), Charset.forName("UTF-8"),
                            StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
                    try {
                        while (rsBase.next()) {
                            String tableName = rsBase.getString("table_name");
                            String schemaName = rsBase.getString("table_schema");
                            long l1 = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage().getUsed();
                            long t1 = System.currentTimeMillis();


                            ResultSet rsColumns = SuperFacadeJDBC.criarConsulta("select column_name, data_type "
                                    + "from information_schema.columns "
                                    + "where table_name = '" + tableName + "' and table_schema = '" + schemaName + "' order by column_name", conAtual);

                            List<String> colunas = obterColunasPorValor(rsColumns);

                            String columns_names = Uteis.splitFromArray(colunas.toArray(), false);

                            String sql = String.format("select '%s' as _a_Entidade, " + columns_names + " from %s", tableName, tableName);

                            colunas.add("_a_Entidade");
                            Collections.sort(colunas);

                            try {
                                int rows = SuperFacadeJDBC.contar("select count(*) from " + tableName, conAtual);
                                Uteis.logar(null, "Generating data from table: " + tableName + " total rows: " + rows);

                                int paginaAtual = 0;
                                int pages = rows > ROWS_LIMIT ? rows / ROWS_LIMIT : 1;
                                int resto = rows % ROWS_LIMIT;
                                if (pages > 1 && resto > 0) {
                                    pages++;
                                }

                                for (int page = paginaAtual; page < pages; page++) {
                                    String sqlComOffset = ROWS_LIMIT < rows
                                            ? String.format(" %s limit %s offset %s", sql, ROWS_LIMIT, page * ROWS_LIMIT)
                                            : String.format(" %s ", sql);
                                    System.out.println(tableName + " => " + (page * ROWS_LIMIT) + " rows");
                                    ResultSet rsTabelaAtual = SuperFacadeJDBC.criarConsulta(sqlComOffset, conAtual);
                                    try {
                                        while (rsTabelaAtual.next()) {
                                            try {
                                                obterOutput(colunas, rsTabelaAtual, formato, wr);
                                            } finally {
                                                wr.flush();
                                            }
                                        }
                                    } finally {
                                        rsTabelaAtual.close();
                                    }
                                }

                                long l2 = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage().getUsed();
                                long t2 = System.currentTimeMillis();

                                Uteis.logar(null, "Generated data from table: " + tableName + " in " + (t2 - t1) + "ms");
                                Uteis.logar(null, "Memory used: " + ((l2 - l1) / 1024) + "KB");
                            } finally {
                                wr.flush();
                            }
                        }
                    } finally {
                        wr.close();
                    }
                    send(new StringBuffer("Ola! Segue abaixo o link de download do arquivo exportado:\r\n").                            
                            append(fullURLApp.replace("UpdateServlet", "")).
                                    append("DownloadSV?mimeType=application/vnd.ms-excel&relatorio=").append(fileName).toString());
                    rsBase.close();
                } catch (Exception e) {
                    Uteis.logar(null, conAtual.getMetaData().getURL() + ": " + e.getMessage());
                } finally {
                    conAtual.close();
                }
            } else if (formato != null && formato.equals("excel")) {
                String[] listaTabelasConsiderar = new String[]{};
                if (nomeBanco.toLowerCase().contains("bdzillyon") || nomeBanco.toLowerCase().contains("_zw")) {
                    listaTabelasConsiderar = new String[]{"alunohorarioturma", "auladesmarcada",
                        "cartaocredito", "categoria", "categoriaproduto", "cheque", "cidade", "classificacao",
                        "cliente", "clienteobservacao", "colaborador", "contrato", "contratoassinaturadigital", "contratocomposicao", "contratocondicaopagamento",
                        "contratoduracao", "contratohorario", "contratomodalidade", "contratomodalidadehorarioturma",
                        "contratomodalidadeprodutosugerido", "contratomodalidadeturma", "contratomodalidadevezessemana",
                        "contratooperacao", "email", "endereco", "estado", "formapagamento", "fornecedor", "horario", "horarioturma", "historicocontato", "itemvendaavulsa",
                        "modalidade", "movconta", "movcontarateio", "movpagamento", "movparcela", "movproduto", "movprodutoparcela", "planoconta",
                        "centrocusto", "configuracaofinanceiro","pagamentomovparcela", "pais", "pessoa", "plano", "planoduracao", "planomodalidade", "planomodalidadevezessemana", "produto",
                        "profissao", "recibopagamento", "reposicao", "telefone", "turma", "vendaavulsa","questionario",
                        "questionariopergunta", "evento", "pergunta"};
                } else if (nomeBanco.toLowerCase().contains("bdmusc") || nomeBanco.toLowerCase().contains("_tr")) {
                    listaTabelasConsiderar = new String[]{
                            "atividade", "atividadeficha", "atividadecategoriaatividade", "atividadeempresa", "atividadegrupomuscular",
                            "atividadenivel", "aula", "aulaaluno", "aulahorario", "aulapersonal", "avaliacaofisica",
                            "categoriaatividade", "categoriaficha", "clienteacompanhamento", "clienteobservacao",
                            "clientesintetico", "compromisso", "email", "empresa", "ficha", "grupocliente",
                            "grupomuscular", "historiconivelcliente", "historicorevisao",
                            "itemgrupoindicadores", "musculo", "musculogrupomuscular", "nivel", "objetivopredefinido",
                            "objetivoprograma", "pessoa", "professordisponivel", "professorsintetico", "professorsubstituido",
                            "programatreino", "programatreinoficha", "serie", "treinorealizado"};
                }
                List<String> tabelasConsiderar = new ArrayList<String>(Arrays.asList(listaTabelasConsiderar));

                tables = Uteis.splitFromArray(listaTabelasConsiderar, true);

                Connection conAtual = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, nomeBanco);
                try {
                    boolean allTables = tables == null || tables.equals("*");
                    String condicaoTables = allTables ? "" : " and table_name in (" + tables + ")";
                    ResultSet rsTabelas = SuperFacadeJDBC.criarConsulta(
                            String.format("select table_name, table_schema  from information_schema.tables "
                            + "where table_catalog = '%s' and table_type = 'BASE TABLE' "
                            + "and table_schema in ('public') "
                            + condicaoTables + " "
                            + "order by table_name", nomeBanco), conAtual);

                    final String fileName = nomeBanco + ".xlsx";
                    fileNameFull = pathFileDest + File.separator + fileName;
                    File file = new File(fileNameFull);
                    if (file.exists()) {
                        file.delete();
                    }
                    FileOutputStream fileOut = new FileOutputStream(file);

                    SXSSFWorkbook wb = new SXSSFWorkbook();
//                    final int MAX_ROWS_EXCEL = SpreadsheetVersion.EXCEL2007.getMaxRows();
                    final int MAX_ROWS_EXCEL = 500000;

                    CellStyle styleData = wb.createCellStyle();
                    styleData.setDataFormat(wb.createDataFormat().getFormat("dd/MM/yyyy hh:mm"));

                    while (rsTabelas.next()) {
                        long l1 = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage().getUsed();
                        long t1 = System.currentTimeMillis();

                        String tableName = rsTabelas.getString("table_name");
                        String schemaName = rsTabelas.getString("table_schema");

                        if (tabelasConsiderar != null && !tabelasConsiderar.isEmpty() && !tabelasConsiderar.contains(tableName.toLowerCase())) {
                            continue;
                        }
                        if (tablesIgnore != null && Arrays.asList(tablesIgnore).contains(tableName.toLowerCase())) {
                            System.out.println("IGNORANDO tabela: " + tableName);
                            continue;
                        }

                        final String countSql = String.format("select count(1) from %s ", tableName);
                        ResultSet rsCountTable = SuperFacadeJDBC.criarConsulta(countSql, conAtual);
                        int tableRowCount = 0;
                        if (rsCountTable.next()) {
                            tableRowCount = rsCountTable.getInt("count");
                            Uteis.logar(null, String.format("Tabela: %s => %s registros", tableName, tableRowCount));
                        }
                        if (tableRowCount == 0) {
                            continue;
                        }

                        System.out.println("Iniciando tabela: " + tableName);
                        int indexTabSheet = 0;
                        List<String> colunas = new ArrayList();
                        SXSSFSheet sheet = newSheet(conAtual, wb, tableName, indexTabSheet++, schemaName, colunas);

                        if (sheet == null) continue;

                        String columns_names = Uteis.splitFromArray(colunas.toArray(), false);

                        final String sql = String.format("select " + columns_names + " from %s ", tableName);

                        ResultSet rsCount = SuperFacadeJDBC.criarConsulta(
                                String.format("select count(1) row_count from (%s) t", sql), conAtual);

                        Integer rowCount = 0;
                        if (rsCount.next()) {
                            rowCount = rsCount.getInt("row_count");
                            Uteis.logar(null, String.format("Tabela: %s => %s registros", tableName, rowCount));
                        }

                        int paginaAtual = 0;
                        int pages = rowCount > ROWS_LIMIT ? rowCount / ROWS_LIMIT : 1;
                        int resto = rowCount % ROWS_LIMIT;
                        if ((pages > 0 && resto > 0) && (rowCount > ROWS_LIMIT)) {
                            pages++;
                        }

                        Collections.sort(colunas);

                        try {
                            int linhaAtual = 1;
                            for (int page = paginaAtual; page < pages; page++) {
                                String sqlComOffset = ROWS_LIMIT < rowCount
                                        ? String.format(" %s limit %s offset %s", sql, ROWS_LIMIT, page * ROWS_LIMIT)
                                        : String.format(" %s ", sql);
                                System.out.println(tableName + " => " + sqlComOffset);
                                ResultSet rsTabelaAtual = SuperFacadeJDBC.criarConsulta(sqlComOffset, conAtual);
                                while (rsTabelaAtual.next()) {
                                    Row dataRow = sheet.createRow(linhaAtual++);
                                    if (linhaAtual % ROWS_LIMIT == 0) {
                                        fileOut.flush();
                                    }
                                    int coluna = 0;
                                    for (String nomeCol : colunas) {
                                        Cell celula = dataRow.createCell(coluna);
                                        int i = rsTabelaAtual.findColumn(nomeCol);
                                        Object valor = rsTabelaAtual.getObject(i);
                                        if (valor instanceof String) {
                                            if (valor != null && valor.toString().length() > 32767){
                                                celula.setCellValue(valor.toString().substring(0, 32766));
                                            } else {
                                                celula.setCellValue(valor == null ? "" : valor.toString());
                                            }
                                        } else if (valor instanceof Number) {
                                            celula.setCellValue(((Number) valor).doubleValue());
                                        } else if (valor instanceof Date) {
                                            celula.setCellStyle(styleData);
                                            celula.setCellValue((Date) valor);
                                        } else if (valor instanceof Boolean) {
                                            celula.setCellValue((Boolean) valor);
                                        } else {
                                            if (valor != null && valor.toString().length() > 32767){
                                                celula.setCellValue(valor == null
                                                        ? ""
                                                        : valor.toString().substring(0, 32766));
                                            } else {
                                                celula.setCellValue(valor == null ? "" : valor.toString());
                                            }
                                        }
                                        coluna++;
                                    }
                                    if (linhaAtual + 1 >= MAX_ROWS_EXCEL) {
                                        sheet = newSheet(conAtual, wb, tableName, indexTabSheet++, schemaName, colunas);
                                        linhaAtual = 1;
                                        if (sheet == null) continue;
                                    }
                                }//while rsTabelaAtual.next()
                                rsTabelaAtual.close();
                                long l2 = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage().getUsed();
                                long t2 = System.currentTimeMillis();

                                Uteis.logar(null, "Generated data from table: " + tableName + " in " + (t2 - t1) + "ms");
                                Uteis.logar(null, "Memory used: " + ((l2 - l1) / 1024) + "KB");
                            }
                        } finally {
                            fileOut.flush();
                        }
                    }
                    wb.write(fileOut);
                    fileOut.close();
                    rsTabelas.close();
//                    send(new StringBuffer("Ola! Segue abaixo o link de download do arquivo exportado:\r\n").
//                            append(fullURLApp.replace("UpdateServlet", "")).
//                                    append("DownloadSV?mimeType=application/vnd.ms-excel&relatorio=").append(fileName).toString());
                } catch (Exception e) {
                    e.printStackTrace();
                    Uteis.logar(null, conAtual.getMetaData().getURL() + ": " + e.getMessage());
                    send(new StringBuffer("Ola! Não foi possível exportar o arquivo devido ao ERRO: ").
                            append(conAtual.getMetaData().getURL()).append(": ").append(e.getMessage()).toString());
                } finally {
                    conAtual.close();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "ERRO: " + ex.getMessage());
            send(new StringBuffer("Ola! Não foi possível exportar o arquivo devido ao ERRO: ")
                    .append(ex.getMessage()).toString());
        }
    }

    private SXSSFSheet newSheet(Connection conAtual, Workbook wb, final String tableName, int index, final String schemaName, List colunas) throws Exception {
        try {
            SXSSFSheet sheet = (SXSSFSheet) wb.createSheet(String.format("%s_%s", tableName, index));
            sheet.setRandomAccessWindowSize(100);

            ResultSet rsColumns = SuperFacadeJDBC.criarConsulta("select column_name, data_type "
                    + "from information_schema.columns "
                    + "where table_name = '" + tableName + "' and table_schema = '" + schemaName + "' and column_name not in ('assinaturabiometriafacial','assinaturadigitalbiometria') order by column_name", conAtual);

            Row cabecalho = sheet.createRow(0);
            int coluna = 0;
            while (rsColumns.next()) {
                String tipo = rsColumns.getString("data_type");
                String nome = rsColumns.getString("column_name");
                if (tipo.equals("bytea")) {
                    continue;
                }

                Cell celulaColuna = cabecalho.createCell(coluna);
                celulaColuna.setCellValue(nome);

                coluna++;
                colunas.add(nome);
            }
            return sheet;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private List<String> obterColunasPorValor(ResultSet rsConsulta) throws SQLException {
        List<String> colunas = new ArrayList<String>();

        while (rsConsulta.next()) {
            String tipo = rsConsulta.getString("data_type");
            if (tipo.equals("bytea")) {
                continue;
            }
            colunas.add(rsConsulta.getString("column_name"));
        }

        return colunas;
    }

    public void obterOutput(List<String> colunas, ResultSet rsConsulta, String formato,
            BufferedWriter wr) throws Exception {
        if (formato != null && formato.startsWith("csv")) {
            obterCSV(colunas, rsConsulta, wr);
        }
    }

    private void obterCSV(List<String> colunas, ResultSet rsConsulta, BufferedWriter wr) throws Exception {
        StringBuilder header = new StringBuilder();
        if (exibirTitulo) {
            for (String nomeCol : colunas) {
                header.append(nomeCol).append(VIRGULA);
            }
            header.append(LF);
        }

        wr.write(header.toString());
        wr.newLine();

        int rows = 0;
        while (rsConsulta.next()) {
            StringBuilder linha = new StringBuilder();
            for (String nomeCol : colunas) {
                linha.append(rsConsulta.getObject(nomeCol) != null ? rsConsulta.getObject(nomeCol).toString() : NULL).append(VIRGULA);
            }
            wr.write(linha.toString());
            wr.newLine();
            linha = null;
            if (rows > ROWS_FLUSH) {
                wr.flush();
                rows = 0;
            }
            rows++;
        }
    }

    public String getFileNameFull() {
        return fileNameFull;
    }

    private void send(final String texto) {
        UteisEmail uteis;
        try {
            System.out.println("Tentando ENVIAR EMAIL...");
            uteis = new UteisEmail();
            ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPRobo();
            uteis.novo("", config);
            uteis.enviarEmailN(new String[]{emailDest}, texto, "[ZW] - Export DATA", "PACTO");
            System.out.println("Email enviado com SUCESSO!!");
        } catch (Exception ex) {
            System.out.println("EXC.: Não foi possível enviar email de erro ocorrido. MENSAGEM: '"
                    + texto + "' , por causa do seguinte erro de email: " + ex.getMessage());
        }
    }
}

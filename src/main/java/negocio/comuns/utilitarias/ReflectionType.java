/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

import servicos.bi.exportador.formatadores.FormatadorEnum;

import java.lang.reflect.Field;

/**
 *
 * <AUTHOR>
 */
public class ReflectionType {

    private String name;
    private Class<?> type;
    private FormatadorEnum formatador;
    private String functionType;

    public ReflectionType(Field f) {
        this.name = f.getName();
        this.type = f.getType();
    }

    public ReflectionType(Field f, FormatadorEnum formatador) {
        this.name = f.getName();
        this.type = f.getType();
        this.formatador = formatador;
    }

    public ReflectionType(final String name, final Class<?> type) {
        this.name = name;
        this.type = type;
    }

    public ReflectionType(final String name, final Class<?> type, final FormatadorEnum formatador) {
        this.name = name;
        this.type = type;
        this.formatador = formatador;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Class<?> getType() {
        return type;
    }

    public void setType(Class<?> type) {
        this.type = type;
    }

    public FormatadorEnum getFormatador() {
        return formatador;
    }

    public void setFormatador(FormatadorEnum formatador) {
        this.formatador = formatador;
    }

    public String getFunctionType() {
        return functionType;
    }

    public void setFunctionType(String functionType) {
        this.functionType = functionType;
    }

    @Override
    public String toString() {
        return name + "=" + type.getSimpleName();
    }
}

package negocio.comuns.utilitarias;

import negocio.comuns.basico.enumerador.TipoExceptionEnum;

public class ConsistirException extends Exception {
    private String campo;
    private String aba;
    private TipoExceptionEnum tipoExceptionEnum;
    public ConsistirException(String msgErro) {
        super(msgErro);
    }
    public ConsistirException(String msgErro, String aba) {
        super(msgErro);
        this.aba = aba;
    }

    public ConsistirException(String msgErro, String aba, String campo) {
        super(msgErro);
        this.aba = aba;
        this.campo = campo;
    }

    public ConsistirException(String msgErro, TipoExceptionEnum tipoExceptionEnum) {
        super(msgErro,new Throwable(tipoExceptionEnum.name()));
    }

    public String getCampo() {
        return campo;
    }

    public void setCampo(String campo) {
        this.campo = campo;
    }

    public String getAba() {
        return aba;
    }

    public void setAba(String aba) {
        this.aba = aba;
    }

    public TipoExceptionEnum getTipoExceptionEnum() {
        return tipoExceptionEnum;
    }

    public void setTipoExceptionEnum(TipoExceptionEnum tipoExceptionEnum) {
        this.tipoExceptionEnum = tipoExceptionEnum;
    }
}

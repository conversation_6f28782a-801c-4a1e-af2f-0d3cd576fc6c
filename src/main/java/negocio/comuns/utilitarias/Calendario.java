/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estudio.enumeradores.DiaDaSemanaEnum;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Time;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Encapsular métodos de manipulação de datas
 * <AUTHOR>
 */
public class Calendario {

    public static final String BRAZIL_EAST = "Brazil/East";
    private static final DateFormat DATE_FORMAT = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss SSS a");
    private static Locale DEFAULT_LOCALE = null;
    public static Date dia = null;
    private static final ThreadLocal<Date> threadLocalDate = new ThreadLocal<>();


    public static Locale getDefaultLocale() {
        if (DEFAULT_LOCALE == null) {
            DEFAULT_LOCALE = new Locale("pt", "BR");
        }
        return DEFAULT_LOCALE;
    }

    public static Date getDataFromSession() {
        if (dia != null) {
            return (Date) dia.clone();
        }

        if (!JSFUtilities.isJSFContext()) {
            Date dataThread = getDateThread();
            if(dataThread != null){
                return dataThread;
            }
        }

        Date dataSistema = (Date) JSFUtilities.getFromSession("dataSistema");
        if (dataSistema == null) {
            return getDateInTimeZone(new Date(), "");
            //} else {
            //System.out.println("ATENÇÃO!!! Modo DESENVOLVIMENTO HABILITADO! DataSistema está na SESSÃO!");
        }
        return dataSistema;
    }

    public static void setDataOnSession(Date dataSistema) {
        JSFUtilities.storeOnSession("dataSistema", dataSistema);
    }

    public static void imprimirConsoleTodosTimeZones() {
        Date today = new Date();

        // Get all time zone ids
        String[] zoneIds = TimeZone.getAvailableIDs();
        Map<String, String> mapa = new HashMap();

        // View every time zone
        for (int i = 0; i < zoneIds.length; i++) {
            // Get time zone by time zone id
            TimeZone tz = TimeZone.getTimeZone(zoneIds[i]);

            // Get the display name
            String shortName = tz.getDisplayName(tz.inDaylightTime(today), TimeZone.SHORT);
            String longName = tz.getDisplayName(tz.inDaylightTime(today), TimeZone.LONG);
            mapa.put(tz.getID(), tz.getID() + " - " + shortName + " - " + longName);

            // Get the number of hours from GMT
            int rawOffset = tz.getRawOffset();
            int hour = rawOffset / (60 * 60 * 1000);
            int min = Math.abs(rawOffset / (60 * 1000)) % 60;

            // Does the time zone have a daylight savings time period?
            boolean hasDST = tz.useDaylightTime();

            // Is the time zone currently in a daylight savings time?
            boolean inDST = tz.inDaylightTime(today);

        }
        ArrayList<String> lista = new ArrayList(mapa.values());

        Collections.sort(lista);
        for (String string : lista) {

            System.out.println(string);

        }
    }

    /**
     * Retorna uma data usando 'currentDate' data passada como argumento, aplicando
     * um TimeZone espec&iacute;fico.
     * @param currentDate
     * @param timeZoneId
     * @return
     */
    public static Date getDateInTimeZone(Date currentDate, String timeZoneID) {
        String timeZoneDefault = timeZoneID.isEmpty() ? obterTimeZoneIDDefault() : timeZoneID;
        Calendar mbCal = new GregorianCalendar(TimeZone.getTimeZone(timeZoneDefault));
        mbCal.setTimeInMillis(currentDate.getTime());

        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.set(Calendar.YEAR, mbCal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, mbCal.get(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, mbCal.get(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, mbCal.get(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, mbCal.get(Calendar.MINUTE));
        cal.set(Calendar.SECOND, mbCal.get(Calendar.SECOND));
        cal.set(Calendar.MILLISECOND, mbCal.get(Calendar.MILLISECOND));

        return cal.getTime();
    }

    /**
     * Procura um TimeZone default definido na sessão do Sistema (http session),
     * ou retorna o TimeZone default de Brasilia (Brazil/East)
     * <AUTHOR> Maciel
     * @return
     */
    public static String obterTimeZoneIDDefault() {
        FacesContext context = FacesContext.getCurrentInstance();
        if (context != null) {
            String tzID = (String) JSFUtilities.getFromSession("timeZoneID");

            if (tzID != null && !tzID.isEmpty()) {
                return tzID;
            } else {
                return BRAZIL_EAST;
            }
        } else {
            return BRAZIL_EAST;
        }

    }

    public static Date amanha(){
        Date dt = Calendario.hoje();
        Calendar c = Calendar.getInstance();
        c.setTime(dt);
        c.add(Calendar.DATE, 1);
        dt = c.getTime();

        return dt;
    }

    /**
     * Retorna a data e hora atual. Também encapsula cenários de sobreposição da data/hora atual, se necessário.
     * Deve ser utilizado em todo o sistema no lugar de 'negocio.comuns.utilitarias.Calendario.hoje()'
     * @return Date
     */
    public static Date hoje() {
        //////
        //Para utilizar o sistema numa data específica, deve ser informada a data na tela 'inicio.jsp',
        //que alterará o atributo 'data' estaticamente.
        //////

        Date dataAtual = getDataFromSession();

        Date dataSistemaFromSession = (Date) JSFUtilities.getFromSession("dataSistema");
        if (dataSistemaFromSession != null) {

            String dataSistema = String.format("%s %s",
                    Uteis.getData(getDataFromSession()),
                    Calendario.agora(""));

            try {
                dataAtual = DATE_FORMAT.parse(dataSistema);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }

        // dataBase é uma variavel em request que é utilizada no processo de negociaçao do contrato.
        FacesContext context = FacesContext.getCurrentInstance();
        if (JSFUtilities.isJSFContext()) {
            HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();
            Date dataBase = (Date) request.getAttribute("dataBase");
            if (dataBase != null) {
                return (Date) dataBase.clone();
            }
        }
        return (Date) dataAtual.clone();
    }

    /**
     * Similar ao método Calendario.hoje(), este retorna a data atual obedecendo o TimeZone 'timeZoneID'
     * passado como argumento.
     * @return Date
     */
    public static Date hoje(String timeZoneID) {
        /**
         *Para utilizar o sistema numa data específica, deve ser informada a data na tela 'inicio.jsp',
         *que alterará o atributo 'data' da sessão HTTP
         */
        String dataSistema = String.format("%s %s",
                new Object[]{
                    Uteis.getData(getDateInTimeZone(getDataFromSession(), timeZoneID)),
                    Calendario.agora(timeZoneID)});

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss SSS a");
        Date dataAtual = null;
        try {
            dataAtual = df.parse(dataSistema);
        } catch (ParseException e) {
            e.printStackTrace();
        } finally {
            df = null;
        }
        // dataBase é uma variavel em request que é utilizada no processo de negociaçao do contrato.
        FacesContext context = FacesContext.getCurrentInstance();
        if (context != null) {
            HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();
            Date dataBase = (Date) request.getAttribute("dataBase");
            if (dataBase != null) {
                return (Date) dataBase.clone();
            }
        }
        return (Date) dataAtual.clone();
    }

    public static Date hojeSemRequest() {
        /**
         *Para utilizar o sistema numa data específica, deve ser informada a data na tela 'inicio.jsp',
         *que alterará o atributo 'data' da sessão HTTP
         */
        String dataSistema = String.format("%s %s",
                new Object[]{
                    Uteis.getData(getDataFromSession()),
                    Calendario.agora("")});

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss SSS a");
        Date dataAtual = null;
        try {
            dataAtual = df.parse(dataSistema);
        } catch (ParseException e) {
            e.printStackTrace();
        } finally {
            df = null;
        }
        return (Date) dataAtual.clone();
    }

    public static String agora(String timeZoneID) {
        DateFormat df = new SimpleDateFormat("HH:mm:ss SSS a", getDefaultLocale());
        Date hoje = getDateInTimeZone(new Date(), timeZoneID);
        return df.format(hoje);
    }

    /**
     * Retorna VERDADEIRO se a data passada como parâmetro está entre <i>dataInicio</i> e <i>dataFim</i>
     * @param dataQueEstaEntre data que será verifica entre dataInicio e dataFim
     * @param dataInicio
     * @param dataFim
     * @return <i>true</i> caso a <i>dataQueEstaEntre</i> esteja entre a das duas outras datas,
     * 		<i>false</i> caso não estejam entre.
     * <AUTHOR> Maciel
     */
    public static boolean entre(final Date dataQueEstaEntre, final Date dataInicio, final Date dataFim) {
        if (Calendario.maiorOuIgual(dataQueEstaEntre, dataInicio)
                && Calendario.menorOuIgual(dataQueEstaEntre, dataFim)) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean entreComHora(final Date dataQueEstaEntre, final Date dataInicio, final Date dataFim) {
        if (Calendario.maiorOuIgualComHora(dataQueEstaEntre, dataInicio)
                && Calendario.menorOuIgualComHora(dataQueEstaEntre, dataFim)) {
            return true;
        } else {
            return false;
        }
    }
    
    public static boolean entre(final long dataQueEstaEntre, final long dataInicio, final long dataFim) {
        if ((dataQueEstaEntre > dataInicio)
                && (dataQueEstaEntre < dataFim)) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean menorDataAtual(Date data){
        return Calendario.menor(data, Calendario.hoje());
    }

    /**
     * Retorna VERDADEIRO se a data passada como parâmetro é menor que a data comparada
     * @param <i>dataQueDeveSerMenor</i> data que será verifica se é menor do que a outra.
     * @param dataComparada
     * @return <i>true</i> caso a <i>dataQueDeveSerMenor</i> seja menor do que a dataComparada,
     * 		<i>false</i> caso não seja menor.
     * <AUTHOR> Maciel
     */
    public static boolean menor(final Date dataQueDeveSerMenor, final Date dataComparada) {
        //inicializar calendários, pois a comparação after e before exige que sejam
        //instâncias de Calendar
        Calendar calDataQueDeveSerMenor = Calendario.getInstance();
        calDataQueDeveSerMenor.setTime(getDataComHoraZerada(dataQueDeveSerMenor));

        Calendar calDataComparada = Calendario.getInstance();
        calDataComparada.setTime(getDataComHoraZerada(dataComparada));

        return calDataQueDeveSerMenor.before(calDataComparada);

    }

    public static boolean menorComHora(final Date dataHoraQueDeveSerMenor, final Date dataHoraComparada) {
        //inicializar calendários, pois a comparação after e before exige que sejam
        //instâncias de Calendar
        Calendar calDataQueDeveSerMenor = Calendario.getInstance();
        calDataQueDeveSerMenor.setTime(dataHoraQueDeveSerMenor);

        Calendar calDataComparada = Calendario.getInstance();
        calDataComparada.setTime(dataHoraComparada);


        if (calDataQueDeveSerMenor.before(calDataComparada)) {
            return true;
        } else {
            return false;
        }

    }

    public static boolean menorOuIgualHHMM(final Date dataHoraQueDeveSerMenor, final Date dataHoraComparada) throws ParseException {
        //inicializar calendários, pois a comparação after e before exige que sejam
        //instâncias de Calendar
        Calendar calDataQueDeveSerMenor = Calendario.getInstance();
        calDataQueDeveSerMenor.setTime(dataHoraQueDeveSerMenor);
        calDataQueDeveSerMenor.set(Calendar.SECOND, 0); // Zerar os segundos

        Calendar calDataComparada = Calendario.getInstance();
        calDataComparada.setTime(dataHoraComparada);
        calDataComparada.set(Calendar.SECOND, 0); // Zerar os segundos

        if (calDataQueDeveSerMenor.before(calDataComparada) || igualComHora(calDataQueDeveSerMenor.getTime(), calDataComparada.getTime())) {
            return true;
        } else {
            return false;
        }

    }


    /**
     * Retorna VERDADEIRO se a data passada como parâmetro é maior que a data comparada
     * @param <i>dataQueDeveSerMaior</i> data que será verifica se é menor do que a outra.
     * @param dataComparada
     * @return <i>true</i> caso a <i>dataQueDeveSerMaior</i> seja menor do que a dataComparada,
     * 		<i>false</i> caso não seja menor.
     * <AUTHOR> Maciel
     */
    public static boolean maior(final Date dataQueDeveSerMaior, final Date dataComparada) {
        //inicializar calendários, pois a comparação after e before exige que sejam
        //instâncias de Calendar
        Calendar calDataQueDeveSerMaior = Calendario.getInstance();
        calDataQueDeveSerMaior.setTime(getDataComHoraZerada(dataQueDeveSerMaior));

        Calendar calDataComparada = Calendario.getInstance();
        calDataComparada.setTime(getDataComHoraZerada(dataComparada));

        return calDataQueDeveSerMaior.after(calDataComparada);
    }

    public static boolean maiorComHora(final Date dataHoraQueDeveSerMaior, final Date dataHoraComparada) {
        Calendar calDataQueDeveSerMaior = Calendario.getInstance();
        calDataQueDeveSerMaior.setTime(dataHoraQueDeveSerMaior);

        Calendar calDataComparada = Calendario.getInstance();
        calDataComparada.setTime(dataHoraComparada);

        return calDataQueDeveSerMaior.after(calDataComparada);
    }


    /**
     * Retorna VERDADEIRO se a data passada como parâmetro é igual (no formato dd/MM/yyyy) a data comparada
     * @param <i>dataQueDeveSerIgual</i> data que será verifica se é igual a outra.
     * @param dataComparada
     * @return <i>true</i> caso as datas são iguais
     * <AUTHOR> Maciel
     */
    public static boolean igual(final Date dataQueDeveSerIgual, final Date dataComparada) {
        if (((dataQueDeveSerIgual == null) && (dataComparada != null)) || ((dataComparada == null) && (dataQueDeveSerIgual != null))){
            return false;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        String d1 = sdf.format(dataQueDeveSerIgual);
        String d2 = sdf.format(dataComparada);
        return d1.equals(d2);
    }

    public static boolean igualComHora(final Date dataHoraQueDeveSerIgual, final Date dataHoraComparada) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        String d1 = sdf.format(dataHoraQueDeveSerIgual);
        String d2 = sdf.format(dataHoraComparada);
        return d1.equals(d2);
    }
    
    public static boolean periodosEmInterseccao(final Date periodo1Inicio,
            final Date periodo1Fim,
            final Date periodo2Inicio, final Date periodo2Fim){
        return igualComHora(periodo1Inicio, periodo2Inicio)
                || igualComHora(periodo1Inicio, periodo2Fim)
                || (maiorComHora(periodo1Inicio, periodo2Inicio) && menorComHora(periodo1Inicio, periodo2Fim))
                
                || igualComHora(periodo1Fim, periodo2Inicio)
                || igualComHora(periodo1Fim, periodo2Fim)
                || (maiorComHora(periodo1Fim, periodo2Inicio) && menorComHora(periodo1Fim, periodo2Fim))
                
                || (maiorComHora(periodo2Inicio, periodo1Inicio) && menorComHora(periodo2Inicio, periodo1Fim));
    }


    /**
     * Retorna VERDADEIRO se a data passada como parâmetro é menor ou igual (no formato dd/MM/yyyy) a data comparada
     * @param <i>dataQueDeveSerMenorOuIgual</i> data que será verifica se é menor ou igual a outra.
     * @param dataComparada
     * @return <i>true</i> caso 'dataQueDeveSerMenorOuIgual' seja menor ou igual a 'dataComparada'
     * <AUTHOR> Maciel
     */
    public static boolean menorOuIgual(final Date dataQueDeveSerMenorOuIgual, final Date dataComparada) {
        return Calendario.menor(dataQueDeveSerMenorOuIgual, dataComparada)
                || Calendario.igual(dataQueDeveSerMenorOuIgual, dataComparada);
    }

    public static boolean menorOuIgualComHora(final Date dataHoraQueDeveSerMenorOuIgual, final Date dataHoraComparada) {
        return Calendario.menorComHora(dataHoraQueDeveSerMenorOuIgual, dataHoraComparada)
                || Calendario.igualComHora(dataHoraQueDeveSerMenorOuIgual, dataHoraComparada);
    }


    public static boolean maiorOuIgualDataAtual(final Date dataQueDeveSerMaiorOuIgual) {
        return Calendario.maior(dataQueDeveSerMaiorOuIgual, Calendario.hoje())
                || Calendario.igual(dataQueDeveSerMaiorOuIgual, Calendario.hoje());
    }

    public static boolean maiorOuIgual(final Date dataQueDeveSerMaiorOuIgual, final Date dataComparada) {
        return Calendario.maior(dataQueDeveSerMaiorOuIgual, dataComparada)
                || Calendario.igual(dataQueDeveSerMaiorOuIgual, dataComparada);
    }

    public static boolean maiorOuIgualComHora(final Date dataQueDeveSerMaiorOuIgual, final Date dataComparada) {
        return Calendario.maiorComHora(dataQueDeveSerMaiorOuIgual, dataComparada)
                || Calendario.igualComHora(dataQueDeveSerMaiorOuIgual, dataComparada);
    }


    public static boolean horasMenor(final String horaQueDeveSerMenor, final String horaQueDeveSerMaior) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("kk:mm");
        Date d1 = formatter.parse(horaQueDeveSerMenor);
        Date d2 = formatter.parse(horaQueDeveSerMaior);
        return d1.getTime() < d2.getTime();
    }

    public static Date converterDataPrimeiraHoraDia(String data) throws ParseException {
        return converterDataPrimeiraHoraDia(data, null);
    }

    public static Date converterDataPrimeiraHoraDia(String data, String formato) throws ParseException {
        formato = formato == null ? "dd/MM/yyyy" : formato;
        SimpleDateFormat formatter = new SimpleDateFormat(formato);
        Date date = formatter.parse(data);
        return getDataComHoraZerada(date);
    }

    public static Date converterDataUltimaHoraDia(String data) throws ParseException {
        return converterDataUltimaHoraDia(data, null);
    }

    public static Date converterDataUltimaHoraDia(String data, String formato) throws ParseException {
        formato = formato == null ? "dd/MM/yyyy" : formato;
        SimpleDateFormat formatter = new SimpleDateFormat(formato);
        Date date = formatter.parse(data);
        return getDataComUltimaHora(date);
    }

    /**
     * Retorna a data passada como parâmetro com campos de data e hora zerados
     * @param <i>data</i> data que terá as horas zeradas.
     * @return <i>Date</i>
     * <AUTHOR> Maciel
     */
    public static Date getDataComHoraZerada(Date data) {
        Calendar calendar = Calendario.getInstance();
        calendar.setTime(data);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }


    public static Date getDataComUltimaHora(Date data) {
        Calendar calendar = Calendario.getInstance(data);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return calendar.getTime();
    }


    /**
     * Retorna a data passada como parâmetro com campos de data e hora preenchidos com valores passados na string
     * <AUTHOR>
     * 13/07/2011
     */
    public static Date getDataComHora(Date data, String hora) {
        if (data == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), Integer.valueOf(hora.substring(0, 2)), Integer.valueOf(hora.substring(3, 5)));
        return cal.getTime();

    }

    public static Date getDataComHoraESegundos(Date data, String hora) {
        if (data == null) {
            return null;
        }

        Calendar cal = Calendario.getInstance();
        cal.setTime(data);

        // Extraindo horas, minutos e segundos da string "hora"
        int horaInt = Integer.parseInt(hora.substring(0, 2));  // Horas
        int minutoInt = Integer.parseInt(hora.substring(3, 5)); // Minutos
        int segundoInt = Integer.parseInt(hora.substring(6, 8)); // Segundos

        // Ajustando horas, minutos e segundos
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), horaInt, minutoInt, segundoInt);

        return cal.getTime();
    }

    /**
     * Retorna uma instância de Calendar já no locale pt/BR, forçando os parâmetros passados.
     * @param ano
     * @param mes de 1 a 12
     * @param dia de 0 a 31, se tiver certeza que o mes tem 31 dias
     * @param horas de 0 a 23
     * @param minutos de 0 59
     * @param segundos de 0 59
     * @param milisegundos de 0 999
     * @return Calendar
     */
    public static Calendar getInstance(int ano, int mes, int dia,
            int horas, int minutos, int segundos, int milisegundos) {

        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.setTime(Calendario.hoje());

        cal.set(Calendar.DAY_OF_MONTH, dia);
        cal.set(Calendar.MONTH, mes - 1);//de 0-11
        cal.set(Calendar.YEAR, ano);
        cal.set(Calendar.HOUR_OF_DAY, horas);
        cal.set(Calendar.MINUTE, minutos);
        cal.set(Calendar.SECOND, segundos);
        cal.set(Calendar.MILLISECOND, milisegundos);
        return cal;
    }

    /**
     * Retorna uma instância de Calendar já no locale pt/BR, forçando os parâmetros passados, zerando campos de hora.
     * @param ano
     * @param mes
     * @param dia
     * @return
     */
    public static Calendar getInstance(int ano, int mes, int dia) {

        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.setTime(Calendario.hoje());

        cal.set(Calendar.DAY_OF_MONTH, dia);
        cal.set(Calendar.MONTH, mes - 1);//de 0-11
        cal.set(Calendar.YEAR, ano);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal;
    }

    public static Calendar getInstance(Date data) {
        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.setTime(data);
        return cal;
    }
    
    public static Calendar getInstance(final long millis) {
        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.setTimeInMillis(millis);
        return cal;
    }

    /**
     * Similar a Calendar.getInstance, porém, força o locale "pt/BR"
     * @return Calendar
     */
    public static Calendar getInstance() {

        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.setTimeInMillis(System.currentTimeMillis());
        cal.setFirstDayOfWeek(Calendar.MONDAY);

        return cal;
    }

    public static Date primeiraHoraDia(Date date) {
        Calendar c = Calendario.getInstance();
        c.setTime(date);

        c.set(Calendar.AM_PM, 0);
        c.set(Calendar.HOUR, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);

        return c.getTime();
    }

    public static Date ultimaHoraDia(Date date) {
        Calendar c = Calendario.getInstance();
        c.setTime(date);

        c.set(Calendar.AM_PM, 0);
        c.set(Calendar.HOUR, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);

        return c.getTime();
    }

    public static Calendar getInstance(Locale locale) {
        Calendar cal = Calendar.getInstance(locale);
        cal.setTime(Calendario.hoje());
        cal.setFirstDayOfWeek(Calendar.MONDAY);

        return cal;
    }

    public static Calendar hojeCalendar(TimeZone tz) {
        Calendar mbCal = new GregorianCalendar(tz);
        /* WM 28/10/2016 - não funciona quando o fuso do servidor é diferente do fuso do parametro, deve ser usado o new Date().getTime()
         * mbCal.setTimeInMillis(hoje(tz.getID()).getTime()); 
         */        
        mbCal.setTimeInMillis(new Date().getTime());

        Calendar cal = Calendar.getInstance(Calendario.getDefaultLocale());
        cal.set(Calendar.YEAR, mbCal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, mbCal.get(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, mbCal.get(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, mbCal.get(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, mbCal.get(Calendar.MINUTE));
        cal.set(Calendar.SECOND, mbCal.get(Calendar.SECOND));
        cal.set(Calendar.MILLISECOND, mbCal.get(Calendar.MILLISECOND));
        return cal;
    }

    public static String getDiaDaSemanaAbreviado(Date data) throws Exception {
        if (data == null) {
            return "";
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        return DiaSemana.getDiaSemanaNumeral(cal.get(Calendar.DAY_OF_WEEK)).getCodigo();
    }

    public static String getDiaDaSemana(Date data) throws Exception {
        if (data == null) {
            return "";
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        return DiaDaSemanaEnum.getDescricaoCompleta(cal.get(Calendar.DAY_OF_WEEK));
    }
    
    public static int getDiaSemana(Date data){
        Calendar dataAtual = getInstance();
        dataAtual.setTime(data);
        return dataAtual.get(Calendar.DAY_OF_WEEK);
    }

    public static void validarHoras(String horaInicial, String horaFinal) throws Exception {

        if (horaInicial.length() == 5 & horaFinal.length() == 5) {
            Calendar cal1 = Calendario.getInstance();
            int horasInicio = Integer.parseInt(horaInicial.substring(0, 2));
            int minutosInicio = Integer.parseInt(horaInicial.substring(3, 5));
            cal1.set(Calendar.HOUR_OF_DAY, horasInicio);
            cal1.set(Calendar.MINUTE, minutosInicio);

            Calendar cal2 = Calendario.getInstance();
            int horasFinal = Integer.parseInt(horaFinal.substring(0, 2));
            int minutosFinal = Integer.parseInt(horaFinal.substring(3, 5));
            cal2.set(Calendar.HOUR_OF_DAY, horasFinal);
            cal2.set(Calendar.MINUTE, minutosFinal);
            if ((horasInicio < 00) | (horasInicio > 23) | (minutosInicio < 00) | (minutosInicio > 59)) {
                throw new Exception("A HORA INICIAL deve ser válida");
            }
            if ((horasFinal < 00) | (horasFinal > 23) | (minutosFinal < 00) | (minutosFinal > 59)) {
                throw new Exception("A HORA TÉRMINO deve ser válida");
            }
            if (cal2.before(cal1) | horaInicial.equals(horaFinal)) {
                throw new Exception("A HORA INICIAL deve ser menor que a  HORA TÉRMINO");
            }
        } else {
            throw new Exception("A HORA INICIAL e HORA TÉRMINO do horário de acesso deve estar no formato hh:mm");
        }
    }

    public static boolean validarSeEstaNoIntervaloHoras(String horasInicial1, String horasFinal1, String horasInicial2, String horasFinal2) {
        if (((horasInicial2.compareTo(horasInicial1) >= 0) && (horasInicial2.compareTo(horasFinal1)) <= 0)
                || (horasFinal2.compareTo(horasInicial1) >= 0 && horasFinal2.compareTo(horasFinal1) <= 0)
                || (horasInicial2.compareTo(horasInicial1) < 0 && horasFinal2.compareTo(horasFinal1) > 0)) {
            return true;
        }
        return false;
    }

    /**
     * Calcula quantos dia da semana existe no período informado.
     * Por exemplo: de 28/08/2012 à 27/09/2012 quantas sextas-feiras possui?
     * Use contarDiasDaSemanaEntre(dateIni, dateFim, Calendar.FRIDAY)
     *
     * Obs.: Consdeira-se no total os extremos: dataInicial e datFinal
     * @param dtInicio
     * @param dtFim
     * @param diaDaSemana
     * @return
     */
    public static int contarDiasDaSemanaEntre(final Date dtInicio, final Date dtFim, int diaDaSemana) {
        long dif = Uteis.nrDiasEntreDatas(dtInicio, dtFim);
        Calendar c = Calendario.getInstance();
        c.setTime(dtInicio);
        int cont = 0;
        for (int i = 0; i <= dif; i++) {
            if (c.get(Calendar.DAY_OF_WEEK) == diaDaSemana) {
                cont++;
            }
            c.add(Calendar.DAY_OF_MONTH, 1);
        }
        return cont;
    }

    /**
     * Retorna um Date no padrão desejado por 'pattern'
     * @param date
     * @param pattern
     * @return
     */
    public static String getData(final Date date, final String pattern) {        
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, Calendario.getDefaultLocale());
        return sdf.format(date);
    }

    /**
     * Retorna a Data Atual no padrão desejado por 'pattern'
     * @param pattern
     * @return
     */
    public static String getData(final String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, Calendario.getDefaultLocale());
        return sdf.format(Calendario.hoje());
    }

    public static Date getDate(final String pattern, final String data) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, Calendario.getDefaultLocale());
        return sdf.parse(data);
    }

    public static boolean horaEstaEntreIntervaloHoras(String horaComparacao, String horaInicio, String horaTermino) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
        Date dAtual = formatter.parse(horaInicio);
        Date dProx = formatter.parse(horaTermino);
        Date dComp = formatter.parse(horaComparacao);
        return dAtual.getTime() <= dComp.getTime() && dComp.getTime() < dProx.getTime();
    }

    /**
     * Gera o horário em String com o seguinte formato HH:mm
     * @param horaFinal
     * @return
     */
    public static String gerarHorarioFinal(Time horaFinal) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(horaFinal);
        return calendar.get(Calendar.HOUR_OF_DAY) < 10 ? "0" + String.valueOf(calendar.get(Calendar.HOUR_OF_DAY) + ":59")
                : "" + String.valueOf(calendar.get(Calendar.HOUR_OF_DAY) + ":59");
    }

    /**
     * Gera o horário em String com o seguinte formato HH:mm
     * @param horaInicial
     * @return
     */
    public static String gerarHorarioInicial(Time horaInicial) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(horaInicial);
        return calendar.get(Calendar.HOUR_OF_DAY) < 10 ? "0" + String.valueOf(calendar.get(Calendar.HOUR_OF_DAY) + ":00")
                : "" + String.valueOf(calendar.get(Calendar.HOUR_OF_DAY) + ":00");
    }

    public static Date fimDoDia(Date dia) {
        if (dia == null) {
            return null;
        }
        Calendar c = getInstance();
        c.setTime(dia);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTime();
    }

    public static Date inicioDoDia(Date dia) {
        if (dia == null) {
            return null;
        }
        Calendar c = getInstance();
        c.setTime(dia);
        c.set(Calendar.HOUR_OF_DAY, 00);
        c.set(Calendar.MINUTE, 00);
        c.set(Calendar.SECOND, 00);
        c.set(Calendar.MILLISECOND, 000);
        return c.getTime();
    }

    public static Boolean dataNoMesmoMesAno(Date data1 , Date data2) {
        if(Uteis.getAnoData(data1) == Uteis.getAnoData(data2)){
            if(Uteis.getMesData(data1) ==Uteis.getMesData(data2)){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }

    }
    public static Boolean dataNoMesmoMes(Date data1 , Date data2) {
        if (Uteis.getMesData(data1) == Uteis.getMesData(data2)) {
            return true;
        } else {
            return false;
        }

    }

    public static Boolean dataNoMesmoDiaMes(Date data1, Date data2) {
        if (Uteis.getMesData(data1) == Uteis.getMesData(data2)) {
            return Calendario.getDiaMes(data1) == Calendario.getDiaMes(data2);
        } else {
            return false;
        }
    }
    public static Date proximoDiaUtil(final Date dataBase, final int nrDiasUteis) {
        Calendar c = getInstance();
        c.setTime(dataBase);
        c.add(Calendar.DAY_OF_MONTH, nrDiasUteis);
        Date result = c.getTime();
        if (!isDiaUtil(result)) {
            result = proximoDiaUtil(c.getTime(), 1);
        }
        return result;
    }

    public static Date proximo(final int field, final Date dataBase) {
        Calendar c = getInstance();
        c.setTime((Date) dataBase.clone());
        c.add(field, 1);
        return c.getTime();
    }

    public static Date anterior(final int field, final Date dataBase) {
        Calendar c = getInstance();
        c.setTime(dataBase);
        c.add(field, -1);
        return c.getTime();
    }

    public static Date proximoDiaSemana(final Date dataBase) {
        Calendar c = getInstance(dataBase);
        int diaAtual = c.get(Calendar.DAY_OF_WEEK);
        boolean go = true;
        while (go) {
            c.setTime(proximo(Calendar.DAY_OF_MONTH, c.getTime()));
            go = c.get(Calendar.DAY_OF_WEEK) != diaAtual;
        }
        return c.getTime();
    }

    public static Date proximoDiaSemana(final int nextDayOfWeek, final Date dataBase) {
        Calendar c = Calendar.getInstance();
        c.setTime(dataBase);

        int currentDayOfWeek = c.get(Calendar.DAY_OF_WEEK);
        int daysToAdd = (nextDayOfWeek - currentDayOfWeek + 7) % 7;

        if (daysToAdd > 0) {
            c.add(Calendar.DAY_OF_MONTH, daysToAdd);
        } else {
            c.add(Calendar.DAY_OF_MONTH, 7 + daysToAdd);
        }
        return c.getTime();
    }

    public static boolean isDiaUtil(final Date dataBase) {
        Calendar c = getInstance();
        c.setTime(dataBase);
        if ((c.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY)
                || (c.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY)) {
            return false;
        }
        return true;
    }

    public static long diferencaEmMinutos(Date dataInicial, Date dataFinal){
        return  (((dataFinal.getTime() - dataInicial.getTime())/1000)/60);
    }

    public static long diferencaEmSegundos(Date dataInicial, Date dataFinal){
        return (dataFinal.getTime() - dataInicial.getTime())/1000;
    }

    public static long diferencaHojeEmSegundos(Date dataFinal){
        return (dataFinal.getTime() - Calendario.hoje().getTime())/1000;
    }

    public static String semanaAtualSQL(Date dataBase) {
        Calendar cInicio = getInstance(dataBase);

        Calendar cFim = getInstance(dataBase);
        cFim.add(Calendar.WEEK_OF_YEAR, 1);
        cFim.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);

        return String.format("'%s' and '%s' ",
                new Object[]{
                    Uteis.getDataFormatoBD(cInicio.getTime()),
                    Uteis.getDataFormatoBD(cFim.getTime())});
    }

    public static String periodoSQL(final Date dataInicial, final Date dataFinal) {

        return String.format("'%s' and '%s' ",
                new Object[]{
                    Uteis.getDataFormatoBD(dataInicial),
                    Uteis.getDataFormatoBD(dataFinal)});
    }
    public static Date setMesData(Date data , int mes){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        calendar.set(Calendar.MONTH, mes);
        return calendar.getTime();
    }

    /**
     * Método para retornar a diferença em horas
     * @param dataInicial
     * @param dataFinal
     * @return
     */
    public static Long diferencaEmHoras(Date dataInicial, Date dataFinal){
        BigDecimal horas = new BigDecimal(dataFinal.getTime() - dataInicial.getTime());
        horas = horas.divide(new BigDecimal(1000), 4).divide(new BigDecimal(60), 4).divide(new BigDecimal(60), 4);
        return horas.longValue();
    }

    public static String diferencaEmHoraMinuto(Date dataInicial, Date dataFinal){
        if ((dataInicial == null) || (dataFinal == null))
            return "00:00";
        long minutos = diferencaEmMinutos(dataInicial, dataFinal);
        long ms = minutos *60*1000;
        return String.format( "%02d:%02d", ms / 3600000, ( ms / 60000 ) % 60 );
    }
    public static Date ontem() {
        return Uteis.obterDataAnterior(hoje(), 1);
    }
    
    public static boolean validarStringHora(String stringHora) {
        int horas = 0;
        int minutos = 0;
        if (stringHora.length() == 5) {
            try {
                Calendar cal1 = Calendario.getInstance();
                horas = Integer.parseInt(stringHora.substring(0, 2));
                minutos = Integer.parseInt(stringHora.substring(3, 5));
            } catch (Exception e) {
                return false;
            }

            if ((horas < 00) | (horas > 23) | (minutos < 00) | (minutos > 59)) {
                return false;
            }
        } else {
            return false;
        }
        return true;
    }

//    public static void main(String... args) throws InterruptedException, Exception {
//        TimeZone tz = TimeZone.getTimeZone(TimeZoneEnum.Brazil_West.getId());
//        Date today = Uteis.getDate("23/08/2011 16:19");
//
//        // Get the display name
//        String shortName = tz.getDisplayName(tz.inDaylightTime(today), TimeZone.SHORT);
//        String longName = tz.getDisplayName(tz.inDaylightTime(today), TimeZone.LONG);
//
//
//        // Get the number of hours from GMT
//        int rawOffset = tz.getRawOffset();
//        int hour = rawOffset / (60 * 60 * 1000);
//        int min = Math.abs(rawOffset / (60 * 1000)) % 60;
//
//        // Does the time zone have a daylight savings time period?
//        boolean hasDST = tz.useDaylightTime();
//
//        // Is the time zone currently in a daylight savings time?
//        boolean inDST = tz.inDaylightTime(today);
//
//        System.out.println(longName + ": " + hour + ":" + min);
//        System.out.println("    hasDST: " + hasDST);
//        System.out.println("    inDST: " + inDST);
//
//        System.out.println(getDateInTimeZone(today, tz.getID()));
//
//        Date d1 = Uteis.getDate("29/08/2012");
//        Date d2 = Uteis.getDate("03/09/2012");
//
//        System.out.println(Calendario.contarDiasDaSemanaEntre(d1, d2, Calendar.TUESDAY));
//
//        System.out.println(Calendario.getInstance().get(Calendar.DAY_OF_WEEK));
//
//        Calendar c = Calendario.getInstance(2013, 10, 21);
//        System.out.println(Uteis.obterDataAnterior(c.getTime(), 1));
//        System.out.println(proximoDiaUtil(Calendario.getInstance(2013, 1, 7).getTime(), 5));
//        System.out.println(proximoDiaSemana(Calendario.getInstance(2013, 2, 21).getTime()));
    /*System.out.println("Brazil/Acre - AMT - Amazon Time -> " + getDateInTimeZone(cal.getTime()));
    System.out.println("Brazil/DeNoronha - FNT - Fernando de Noronha Time -> " + getDateInTimeZone(cal.getTime()));
    System.out.println("Brazil/East - BRT - Brasilia Time -> " + getDateInTimeZone(cal.getTime()));
    System.out.println("Brazil/West - AMT - Amazon Time -> " + getDateInTimeZone(cal.getTime()));*/
//    }
    
    public static Date setAnoData(Date data , int ano){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        calendar.set(Calendar.YEAR, ano);
        return calendar.getTime();
    }

    public static Date getMaior(Date d1, Date d2){
        return maiorOuIgual(d1, d2) ?  d1 : d2;
    }

    public static int diferencaEmDias(Date dataInicial, Date dataFinal) {
        if (dataInicial == null || dataFinal == null)
            return 0;
        long dif = dataFinal.getTime() - dataInicial.getTime();
        return (int) TimeUnit.DAYS.convert(dif, TimeUnit.MILLISECONDS);
    }

    public static int diferencaEmMeses(Date dataInicial, Date dataFinal) {
        int data1 = dataInicial.getYear() * 12 + dataInicial.getMonth();
        int data2 = dataFinal.getYear() * 12 + dataFinal.getMonth();
        return data2 - data1 ;
    }

    public static int diferencaEmMesesInteiro(Date dataInicial, Date dataFinal) {
        Calendar iniCalendar = Calendar.getInstance();
        iniCalendar.setTime(dataInicial);
        Calendar fimCalendar = Calendar.getInstance();
        fimCalendar.setTime(dataFinal);

        // Calcula a diferença em meses entre as datas (ano * 12 + mês)
        int anoMesIni = iniCalendar.get(Calendar.YEAR) * 12 + iniCalendar.get(Calendar.MONTH);
        int anoMesFim = fimCalendar.get(Calendar.YEAR) * 12 + fimCalendar.get(Calendar.MONTH);
        int mesesCompletos = anoMesFim - anoMesIni;

        // Se a data final é antes do dia de início do mês final, não deve ser contado como um mês completo
        if (fimCalendar.get(Calendar.DAY_OF_MONTH) < iniCalendar.get(Calendar.DAY_OF_MONTH)) {
            mesesCompletos--; // Subtrai 1 mês
        }

        // Retorna a diferença total em meses inteiros
        return mesesCompletos + 1; // Somamos 1 para incluir o mês inicial
    }

    public static Date converterEmDate(String date) throws ParseException {
        DateFormat format = new SimpleDateFormat("dd/MM/yyyy");
        return format.parse(date);
    }

    public static String getMesAno(Date data) {
        return Calendario.getData(data, "MM/yyyy");
    }

    public static Integer getAno(Date data) {
        return Integer.parseInt(Calendario.getData(data, "yyyy"));
    }

    public static Integer getMes(Date data) {
        return Integer.parseInt(Calendario.getData(data, "MM"));
    }

    public static Integer getDia(Date data) {
        return Integer.parseInt(Calendario.getData(data, "dd"));
    }

    public static Integer anoAtual(){
        return Integer.parseInt(Calendario.getData(new Date() , "yyyy"));
    }

    public static Long pegaHoraEmMilisegundos(String horaString) {
        final SimpleDateFormat conversorDataHora = new SimpleDateFormat("HH:mm");
        try {
            final Date hora = conversorDataHora.parse(horaString);
            final GregorianCalendar calendarioHorario = new GregorianCalendar();
            calendarioHorario.setTime(hora);

            return calendarioHorario.getTimeInMillis();
        } catch (ParseException ex) {
            Uteis.logar(ex, Calendario.class);
            return null;
        }
    }

    public static Date somarMeses(Date data, int meses){
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.MONTH, meses);

        return cal.getTime();
    }

    public static Date somarAnos(Date data, int anos){
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.YEAR, anos);

        return cal.getTime();
    }

    public static Date somarMilesegundos(Date data, int milesegundos){
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.MILLISECOND, milesegundos);

        return cal.getTime();
    }

    public static Date somarSegundos(Date data, Long segundos){
        return somarSegundos(data, segundos.intValue());
    }

    public static Date somarSegundos(Date data, int segundos){
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.SECOND, segundos);

        return cal.getTime();
    }

    public static Date somarMinutos(Date data, int minutos){
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.MINUTE, minutos);

        return cal.getTime();
    }

    public static Date somarDias(Date data, int dias) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.DAY_OF_MONTH, dias);

        return cal.getTime();
    }

    public static int getDiaMes(){
        return Calendario.getDiaMes(new Date());
    }

    public static int getDiaMes(Date data) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(data);
        return cal.get(Calendar.DAY_OF_MONTH);
    }

    public static Date setDiaMes(Date data, Integer dia) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(data);
        cal.set(Calendar.DAY_OF_MONTH, dia);

        return cal.getTime();
    }

    public static Date subtrairMinutos(Date data, int minutos) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.MINUTE, -minutos);
        return cal.getTime();
    }

    public static Date subtrairDias(Date data, int dias) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.DAY_OF_MONTH, -dias);
        return cal.getTime();
    }

    public static Date subtrairMeses(Date data, int meses){
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);
        cal.add(Calendar.MONTH, -meses);
        return cal.getTime();
    }

    public static int quantidadeDiasMes(Date data) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        return cal.getActualMaximum(Calendar.DATE);
    }

    /**
     * @param data
     * @return boolean
     * retorna verdadeiro se a data não estiver nula
     */
    public static boolean validaDataPreenchida(Date data){
        return data != null;
    }

    public static boolean vencimentoMaiorOuIgualAoMesAtual(final Date dataVencimento) {
        Calendar mesAtual = Calendario.getInstance();
        Calendar mesVencimento = Calendario.getInstance();
        mesAtual.setTime(Calendario.getDataComHoraZerada(Calendario.hoje()));
        mesAtual.set(Calendar.DAY_OF_MONTH, 1);
        mesVencimento.setTime(Calendario.getDataComHoraZerada(dataVencimento));
        mesVencimento.set(Calendar.DAY_OF_MONTH, 1);
        return (Calendario.maiorOuIgual(mesVencimento.getTime(), mesAtual.getTime()));
    }

    public static String format(String format){
        return getDataAplicandoFormatacao(Calendario.hoje(), format);
    }

    public static String getDataAplicandoFormatacao(Date data, String mascara) {
        if (data == null) {
            return "";
        }
        SimpleDateFormat formatador = new SimpleDateFormat(mascara);
        String dataStr = formatador.format(data);
        return dataStr;
    }

    public static Date primeiroDiaMes(){
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, 1);
        return c.getTime();
    }

    public static Date primeiroDiaMesDate(Date date){
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DAY_OF_MONTH, 1);
        return c.getTime();
    }

    public static Date ultimoDiaMesDate(Date date){
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        return c.getTime();
    }

    public static Date ultimoDiaMes(){
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        return c.getTime();
    }

    public static int ultimoDiaMes(Date data) {
        LocalDate date = LocalDate.of(Calendario.getAno(data), Calendario.getMes(data), Calendario.getDia(data));
        return date.lengthOfMonth();
    }

    public static Date primeiroDiaProximoMes(){
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, 1); // Avança um mês
        c.set(Calendar.DAY_OF_MONTH, 1); // Define o primeiro dia do próximo mês
        return c.getTime();
    }

    public static Date ultimoDiaProximoMes(){
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, 1); // Avança um mês
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH)); // Último dia do próximo mês
        return c.getTime();
    }


    public static Date getDateThread() {
        return threadLocalDate.get();
    }

    public static void setDateThread(Date date) {
        threadLocalDate.set(date);
    }

    public static Date adicionarUmDiaNaData(Date data){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        Date novaData = calendar.getTime();
        return novaData;
    }

    public static String timeZoneToGMT(final String timeZone) {
        return timeZoneToGMT(timeZone, false);
    }

    public static String timeZoneToGMT(final String timeZone, boolean addGMT) {
        try {
            // Tenta criar um ZoneId com o nome do timezone
            ZoneId zoneId = ZoneId.of(timeZone);

            // Obtém a data e hora atuais na zona especificada
            ZonedDateTime zonedDateTime = ZonedDateTime.now(zoneId);

            // Calcula o offset de GMT/UTC
            int offsetInSeconds = zonedDateTime.getOffset().getTotalSeconds();
            int offsetHours = offsetInSeconds / 3600;
            int offsetMinutes = (offsetInSeconds % 3600) / 60;

            // Formata o offset GMT
            return String.format(addGMT ? "GMT%+d:%02d" : "%+d:%02d", offsetHours, Math.abs(offsetMinutes));

        } catch (Exception ex) {
            // Se falhar, tenta interpretar como ZoneOffset (para offsets GMT como "+03:45")
            ZoneOffset offset = ZoneOffset.of(parseGMTOffset(timeZone));

            int offsetInSeconds = offset.getTotalSeconds();
            int offsetHours = offsetInSeconds / 3600;
            int offsetMinutes = (offsetInSeconds % 3600) / 60;

            return String.format(addGMT ? "GMT%+d:%02d" : "%+d:%02d", offsetHours, Math.abs(offsetMinutes));
        }
    }

    // Método auxiliar para converter GMT+X para um formato aceito por ZoneOffset
    private static String parseGMTOffset(String timeZone) {
        // Remove "GMT" se estiver presente
        if (timeZone.startsWith("GMT")) {
            timeZone = timeZone.substring(3); // Remove "GMT"
        }

        // Verifica se o offset tem apenas uma casa nas horas e adiciona um zero à esquerda, se necessário
        if (timeZone.matches("[+-]\\d(:\\d{2})?")) {
            timeZone = timeZone.replaceFirst("([+-])(\\d)", "$10$2"); // Adiciona o zero à esquerda
        }

        return timeZone; // Retorna o offset formatado
    }


    public static int getDiaAtual() throws Exception {
        Calendar dataAtual = getInstance();
        dataAtual.setTime(hoje());
        return dataAtual.get(Calendar.DAY_OF_MONTH);
    }
}

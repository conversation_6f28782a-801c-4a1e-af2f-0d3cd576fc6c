/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.utilitarias;

import negocio.comuns.arquitetura.SuperTO;

import java.util.Calendar;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class PeriodoMensal extends SuperTO {

    private Date dataInicio;
    private Date dataTermino;

    public PeriodoMensal() {
    }

    public String getMesAnoApresentar(){
        return Uteis.getMesReferenciaData(dataInicio);
    }

    @Override
    public String toString() {
        return String.format("Data Inicio: %s, Data Termino: %s", dataInicio.toString(), dataTermino.toString());
    }

    public PeriodoMensal(Date dataInicio, Date dataTermino) {
        this.dataInicio = dataInicio;
        this.dataTermino = dataTermino;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }
}

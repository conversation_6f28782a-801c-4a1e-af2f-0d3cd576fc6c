package negocio.comuns.utilitarias;

import negocio.comuns.basico.enumerador.AcaoObjcaoLeadEnum;

public enum TipoSolicitacaoEnum {
    NOTAS_XML(0, "Download XML notas"),
    NOTAS_PDF(1, "Download PDF notas")
    ;

    private Integer codigo;
    private String descricao;

    TipoSolicitacaoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TipoSolicitacaoEnum obterPorCodigo(int codigo){
        for(TipoSolicitacaoEnum a : TipoSolicitacaoEnum.values()){
            if(a.getCodigo() == codigo){
                return a;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}

package negocio.comuns.utilitarias;

/**
 * Classe responsável por realizar a contagem do tempo de execução dos métodos
 *
 * <AUTHOR>
 * @since 05/04/2019
 */
public class ContadorTempo {
    /**
     * TEMPO_INFORMACOES_MAQUINA_EXECUTORA variável statica responsável por armazenar o tempo incial do método
     */
    private static Long TEMPO_INFORMACOES_MAQUINA_EXECUTORA = -1l;

    /**
     * Método que inicia a contagem, chamando iniciarCronometro
     */
    public static void iniciarContagem() {
        iniciarCronometro();
    }

    /**
     * Método responsável que encerra a contagem, chamando encerrarCronometro para determinar o tempo gasto de execução
     *
     * @return retorna o tempo que o método gastou para ser executado
     */
    public static Long encerraContagem() {
        return encerrarCronometro();
    }

    /**
     * Seta para TEMPO_INFORMACOES_MAQUINA_EXECUTORA o valor inicial em milissegundos, utilizando a função do System.currentTimeMillis
     */
    private static void iniciarCronometro() {
        TEMPO_INFORMACOES_MAQUINA_EXECUTORA = System.currentTimeMillis();
    }

    /**
     * Método responsável por encerrar a contagem, calcular o tempo gasto de execução e remover da mémoria a váriavel longThreadLocal
     *
     * @return retorna o valor final em milissegundos que foi gasto na execução.
     */
    private static long encerrarCronometro() {

        if (TEMPO_INFORMACOES_MAQUINA_EXECUTORA == null) {
            return -1;
        }
        return System.currentTimeMillis() - TEMPO_INFORMACOES_MAQUINA_EXECUTORA;
    }

    public static void limparCronometro() {
        TEMPO_INFORMACOES_MAQUINA_EXECUTORA = null;
    }

}
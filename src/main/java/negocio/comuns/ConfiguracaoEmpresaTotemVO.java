package negocio.comuns;

import negocio.comuns.basico.enumerador.ConfigTotemEnum;

public class ConfiguracaoEmpresaTotemVO {
    private ConfigTotemEnum configTotem;
    private String totem;
    private String valor;
    private Integer empresa;

    public void setValorAsBoolean(Boolean vlr){
        try {
            valor = Boolean.valueOf(vlr).toString();
        }catch (Exception e){
            valor = Boolean.FALSE.toString();
        }
    }

    public Boolean getValorAsBoolean(){
        try {
            return Boolean.valueOf(valor);
        }catch (Exception e){
            return null;
        }
    }

    public Integer getValorAsInt(){
        try {
            return Integer.valueOf(valor);
        }catch (Exception e){
            return 0;
        }
    }

    public void setValorAsInt(Integer i){
        try {
            valor = i.toString();
        }catch (Exception e){
            valor = "0";
        }
    }

    public ConfigTotemEnum getConfigTotem() {
        return configTotem;
    }

    public void setConfigTotem(ConfigTotemEnum configTotem) {
        this.configTotem = configTotem;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getTotem() {
        return totem;
    }

    public void setTotem(String totem) {
        this.totem = totem;
    }
}

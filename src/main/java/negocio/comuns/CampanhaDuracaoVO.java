/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ItemCampanhaVO;
import negocio.comuns.utilitarias.Calendario;

/**
 * <AUTHOR>
 *
 */
public class CampanhaDuracaoVO extends SuperVO implements Cloneable {

    @ChavePrimaria
    private Integer codigo;
    private Date dataInicial;
    private Date dataFinal;
    private String nome;
    private String descricao;
    @Lista
    private List<ItemCampanhaVO> listaItem;
    private Integer multiplicador;
    private EmpresaVO empresa;

    public static void validarDados(CampanhaDuracaoVO campanhaDuracao) throws Exception{
        if (campanhaDuracao.getNome().isEmpty()) {
            throw new Exception("O campo Nome deve ser informado.");
        }
        if (campanhaDuracao.getDataInicial() == null) {
            throw new Exception("O campo Data Inicial deve ser informado.");
        }
        if (campanhaDuracao.getDataFinal() == null) {
            throw new Exception("O campo Data Final deve ser informado.");
        }
    }
    
    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<ItemCampanhaVO> getListaItem() {
        if (listaItem == null) {
            listaItem = new ArrayList<ItemCampanhaVO>();
        }
        return listaItem;
    }

    public void setListaItem(List<ItemCampanhaVO> listaItem) {
        this.listaItem = listaItem;
    }

    public Integer getMultiplicador() {
        if (multiplicador == null) {
            multiplicador = 0;
        }
        return multiplicador;
    }

    public void setMultiplicador(Integer multiplicador) {
        this.multiplicador = multiplicador;
    }
    
    public String getDataInicial__Apresentar(){
        return Calendario.getData(dataInicial, "dd/MM/yyyy");
    }
    
    public String getDataFinal__Apresentar(){
        return Calendario.getData(dataFinal, "dd/MM/yyyy");
    }
    
    public boolean getCampanhaVigente(){
        return Calendario.entre(Calendario.hoje(), dataInicial, dataFinal);
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    @Override
    public Integer getCodigo() {
        if(codigo==null)
            codigo=0;
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * Texto para apresentar em Historico de Pontos se a pontuação da camapanha ativa for maior que 0 e apresentado valor
     * @return String
     */
    public String getTextoCampanhaApresentar(){
        return (getMultiplicador()>0?" Campanha Ativa:"+getNome()+" Multiplicador:"+getMultiplicador():"");
    }
}

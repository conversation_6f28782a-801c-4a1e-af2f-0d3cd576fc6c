package negocio.comuns.acesso;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ServidorFacialVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private EmpresaVO empresa;
    private String descricao = "";
    private String nomecomputador = "";
    private Integer fullHD = 0;
    private Integer facePreviewWidth = 96;
    private Double falsoPositivo = 0.5d;
    private Integer tempoMinimoReenvio = 5000;
    private Integer portaReconhecimentoFacial = 7348;
    private Integer terminal = 0;
    private String pastaFotosReconhecimentoFacial = "C:\\Pacto\\ZillyonAcessoWeb\\ServidorReconhecimentoFacial\\Fotos\\";
    private String pastaFotosProcessadasReconhecimentoFacial = "C:\\Pacto\\ZillyonAcessoWeb\\ServidorReconhecimentoFacial\\FotosProcessadas\\";
    private String pastaLogsReconhecimentoFacial = "C:\\Pacto\\ZillyonAcessoWeb\\ServidorReconhecimentoFacial\\Logs\\";
    private Integer portaPCReconhecimentoFacial = 7489;
    private String servidorBDFacial = "";
    private Integer distanciaIdentificacao;

    //Cameras
    @ChaveEstrangeira
    private List<CameraVO> cameras;

    public void validarDados() throws ConsistirException {
        if (UteisValidacao.emptyString(descricao)) {
            throw new ConsistirException("Descrição não pode ser vazia");
        }
        if (UteisValidacao.emptyString(nomecomputador)) {
            throw new ConsistirException("Nome do Computador não pode ser vazio");
        }
        if (empresa == null || UteisValidacao.emptyNumber(empresa.getCodigo())) {
            throw new ConsistirException("A Empresa não pode ser vazia");
        }
        if (UteisValidacao.emptyNumber(falsoPositivo) || falsoPositivo < 0.01 || falsoPositivo > 1.0) {
            throw new ConsistirException("O valor Falso positivo dever ser entre 0.01 e 1.00");
        }
        if (!UteisValidacao.emptyNumber(distanciaIdentificacao)) {
            if (distanciaIdentificacao > 500 || distanciaIdentificacao < 80) {
                throw new ConsistirException("O valor do campo Distância de Identificação deve ser entre 80 e 500");
            }
        }
        
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getNomecomputador() {
        return nomecomputador;
    }

    public void setNomecomputador(String nomecomputador) {
        this.nomecomputador = nomecomputador;
    }

    public Integer getFullHD() {
        if (fullHD == null) {
            fullHD = 0;
        }
        return fullHD;
    }

    public void setFullHD(Integer fullHD) {
        this.fullHD = fullHD;
    }

    public Integer getFacePreviewWidth() {
        if (facePreviewWidth == null) {
            facePreviewWidth = 96;
        }
        return facePreviewWidth;
    }

    public void setFacePreviewWidth(Integer facePreviewWidth) {
        this.facePreviewWidth = facePreviewWidth;
    }

    public Double getFalsoPositivo() {
        if (falsoPositivo == null) {
            falsoPositivo = 0.5d;
        }
        return falsoPositivo;
    }

    public void setFalsoPositivo(Double falsoPositivo) {
        BigDecimal auxDecimal = new BigDecimal(falsoPositivo);
        MathContext auxFormat = new MathContext(3);
        BigDecimal bigDecimal = auxDecimal.round(auxFormat);
        falsoPositivo = bigDecimal.doubleValue();
        this.falsoPositivo = falsoPositivo;
    }

    public Integer getTempoMinimoReenvio() {
        if (tempoMinimoReenvio == null) {
            tempoMinimoReenvio = 5000;
        }
        return tempoMinimoReenvio;
    }

    public void setTempoMinimoReenvio(Integer tempoMinimoReenvio) {
        this.tempoMinimoReenvio = tempoMinimoReenvio;
    }

    public Integer getPortaReconhecimentoFacial() {
        if (portaReconhecimentoFacial == null) {
            portaReconhecimentoFacial = 7348;
        }
        return portaReconhecimentoFacial;
    }

    public void setPortaReconhecimentoFacial(Integer portaReconhecimentoFacial) {
        this.portaReconhecimentoFacial = portaReconhecimentoFacial;
    }

    public Integer getTerminal() {
        if (terminal == null) {
            terminal = 0;
        }
        return terminal;
    }

    public void setTerminal(Integer terminal) {
        this.terminal = terminal;
    }

    public String getPastaFotosReconhecimentoFacial() {
        if (pastaFotosReconhecimentoFacial == null) {
            pastaFotosReconhecimentoFacial = "C:\\Pacto\\ZillyonAcessoWeb\\ServidorReconhecimentoFacial\\Fotos\\";
        }
        return pastaFotosReconhecimentoFacial;
    }

    public void setPastaFotosReconhecimentoFacial(String pastaFotosReconhecimentoFacial) {
        this.pastaFotosReconhecimentoFacial = pastaFotosReconhecimentoFacial;
    }

    public String getPastaFotosProcessadasReconhecimentoFacial() {
        if (pastaFotosProcessadasReconhecimentoFacial == null) {
            pastaFotosProcessadasReconhecimentoFacial = "C:\\Pacto\\ZillyonAcessoWeb\\ServidorReconhecimentoFacial\\FotosProcessadas\\";
        }
        return pastaFotosProcessadasReconhecimentoFacial;
    }

    public void setPastaFotosProcessadasReconhecimentoFacial(String pastaFotosProcessadasReconhecimentoFacial) {
        this.pastaFotosProcessadasReconhecimentoFacial = pastaFotosProcessadasReconhecimentoFacial;
    }

    public String getPastaLogsReconhecimentoFacial() {
        if (pastaLogsReconhecimentoFacial == null) {
            pastaLogsReconhecimentoFacial = "C:\\Pacto\\ZillyonAcessoWeb\\ServidorReconhecimentoFacial\\Logs\\";
        }
        return pastaLogsReconhecimentoFacial;
    }

    public void setPastaLogsReconhecimentoFacial(String pastaLogsReconhecimentoFacial) {
        this.pastaLogsReconhecimentoFacial = pastaLogsReconhecimentoFacial;
    }

    public Integer getPortaPCReconhecimentoFacial() {
        if (portaPCReconhecimentoFacial == null) {
            portaPCReconhecimentoFacial = 7489;
        }
        return portaPCReconhecimentoFacial;
    }

    public void setPortaPCReconhecimentoFacial(Integer portaPCReconhecimentoFacial) {
        this.portaPCReconhecimentoFacial = portaPCReconhecimentoFacial;
    }

    public List<CameraVO> getCameras() {
        if (cameras == null) {
            cameras = new ArrayList<CameraVO>();
        }
        return cameras;
    }

    public void setCameras(List<CameraVO> cameras) {
        this.cameras = cameras;
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public String getServidorBDFacial() {
        if (servidorBDFacial == null) {
            servidorBDFacial = "";
        }
        return servidorBDFacial;
    }

    public Integer getDistanciaIdentificacao() {
        if (distanciaIdentificacao == null) {
            distanciaIdentificacao = 0;
        }
        return distanciaIdentificacao;
    }

    public void setDistanciaIdentificacao(Integer distanciaIdentificacao) {
        this.distanciaIdentificacao = distanciaIdentificacao;
    }

    public void setServidorBDFacial(String servidorBDFacial) {
        this.servidorBDFacial = servidorBDFacial;
    }

    public void excluirObjColetorVOs(CameraVO obj) {
        int index = 0;
        Iterator i = getCameras().iterator();
        while (i.hasNext()) {
            CameraVO objExistente = (CameraVO) i.next();
            if (objExistente.getDescricao().equals(obj.getDescricao())) {
                getCameras().remove(index);
                return;
            }
            index++;
        }
    }

    public JSONObject toJSON() throws JSONException {
        JSONObject object = new JSONObject();
        object.put("codigo", codigo);
        object.put("empresa", empresa);
        object.put("descricao", descricao);
        object.put("nomecomputador", nomecomputador);
        object.put("fullHD", fullHD);
        object.put("facePreviewWidth", facePreviewWidth);
        object.put("falsoPositivo", falsoPositivo);
        object.put("tempoMinimoReenvio", tempoMinimoReenvio);
        object.put("portaReconhecimentoFacial", portaReconhecimentoFacial);
        object.put("terminal", terminal);
        object.put("pastaFotosReconhecimentoFacial", pastaFotosReconhecimentoFacial);
        object.put("pastaFotosProcessadasReconhecimentoFacial", pastaFotosProcessadasReconhecimentoFacial);
        object.put("pastaLogsReconhecimentoFacial", pastaLogsReconhecimentoFacial);
        object.put("portaPCReconhecimentoFacial", portaPCReconhecimentoFacial);
        object.put("enderecoRemotoDB", servidorBDFacial);
        object.put("distanciaIdentificacao", distanciaIdentificacao);

        JSONArray jsonCameras = new JSONArray();
        for (CameraVO cameraVO : getCameras()) {
            jsonCameras.put(cameraVO.toJSON());
        }
        object.put("cameras", jsonCameras);

        return object;

    }
}

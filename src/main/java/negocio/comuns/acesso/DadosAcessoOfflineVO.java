/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PessoaVO;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class DadosAcessoOfflineVO extends SuperVO {

    private Integer codigoPK = 0;
    private PessoaVO pessoa = new PessoaVO();
    private ColetorVO coletor = new ColetorVO();
    private UsuarioVO usuario = new UsuarioVO();
    private String tipoPessoa = "";
    private String horariosTurmas = "";
    private String horariosPlanos = "";
    private String retornoValidacao = "";
    private Date dia = new Date();
    // transientes
    private String codigoAcesso = "";
    private String codigoAcessoAlternativo = "";
    private int matricula = 0;
    private String senhaAcesso = "";
    private Integer saldoCreditoTreino =0;
    private boolean validarSaldoCreditoTreino = false;
    private Integer minutosAposUltimoAcessoDiminuirCredito = 0;
    private Date dataUltimoVencimento = null;
    private String idexternointegracao = "";
    private int idexterno = 0;

    public static String CHAVE_NOTIFICAR_ATUALIZAR_BASE_PESSOA = "atualizarBaseOffLinePessoa";
    public static String CHAVE_NOTIFICAR_ATUALIZAR_FOTO_PESSOA = "atualizarFotoPessoa";
    public static String CHAVE_NOTIFICAR_ENVIAR_AUTORIZACAO_ACESSO = "incluidoAutorizacaoDeAcesso";
    public static String CHAVE_NOTIFICAR_ATUALIZAR_TEMPLATE_FACIAL_PESSOA = "atualizarTemplateFacialPessoa";

    private Integer duracaoContrato = 0;

    public Integer getCodigoPK() {
        return codigoPK;
    }

    public void setCodigoPK(Integer codigoPK) {
        this.codigoPK = codigoPK;
    }

    public ColetorVO getColetor() {
        return coletor;
    }

    public void setColetor(ColetorVO coletor) {
        this.coletor = coletor;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public String getRetornoValidacao() {
        return retornoValidacao;
    }

    public void setRetornoValidacao(String retornoValidacao) {
        this.retornoValidacao = retornoValidacao;
    }

    public String getTipoPessoa() {
        return tipoPessoa;
    }

    public void setTipoPessoa(String tipoPessoa) {
        this.tipoPessoa = tipoPessoa;
    }

    public String getHorariosPlanos() {
        return horariosPlanos;
    }

    public void setHorariosPlanos(String horariosPlanos) {
        this.horariosPlanos = horariosPlanos;
    }

    public String getHorariosTurmas() {
        return horariosTurmas;
    }

    public void setHorariosTurmas(String horariosTurmas) {
        this.horariosTurmas = horariosTurmas;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public int getMatricula() {
        return matricula;
    }

    public void setMatricula(int matricula) {
        this.matricula = matricula;
    }

    public String getSenhaAcesso() {
        return senhaAcesso;
    }

    public void setSenhaAcesso(String senhaAcesso) {
        this.senhaAcesso = senhaAcesso;
    }

    public String getCodigoAcessoAlternativo() {
        return codigoAcessoAlternativo;
    }

    public void setCodigoAcessoAlternativo(String codigoAcessoAlternativo) {
        this.codigoAcessoAlternativo = codigoAcessoAlternativo;
    }

    public UsuarioVO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public static DadosAcessoOfflineVO montarDados(ResultSet dadosSQL) throws SQLException {
        DadosAcessoOfflineVO ret = new DadosAcessoOfflineVO();
        ret.setCodigoPK(dadosSQL.getInt("codigopk"));
        ret.getColetor().setNumeroTerminal(dadosSQL.getInt("numeroterminal"));
        ret.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
        ret.setTipoPessoa(dadosSQL.getString("tipopessoa"));
        ret.setHorariosTurmas(dadosSQL.getString("horariosturmas"));
        ret.setHorariosPlanos(dadosSQL.getString("horariosplanos"));
        ret.setCodigoAcesso(dadosSQL.getString("codacesso"));
        ret.setCodigoAcessoAlternativo(dadosSQL.getString("codacessoalternativo"));
        ret.setMatricula(dadosSQL.getInt("codigomatricula"));
        ret.setSenhaAcesso(dadosSQL.getString("senhaacesso"));
        ret.setRetornoValidacao(dadosSQL.getString("retornovalidacao"));
        ret.getUsuario().setCodigo(dadosSQL.getInt("codigousuario"));
        ret.getUsuario().setSenha(dadosSQL.getString("senha"));
        ret.setDia(dadosSQL.getDate("dia"));
        ret.setSaldoCreditoTreino(dadosSQL.getInt("saldoCreditoTreino"));
        ret.setValidarSaldoCreditoTreino(dadosSQL.getBoolean("validarSaldoCreditoTreino"));
        ret.setMinutosAposUltimoAcessoDiminuirCredito(dadosSQL.getInt("minutosAposUltimoAcessoDiminuirCredito"));
        ret.setDataUltimoVencimento(dadosSQL.getDate("ultimoVencimento"));
        ret.setIdexterno(dadosSQL.getInt("idexterno"));
        ret.setIdexternointegracao(dadosSQL.getString("idexternointegracao"));
        ret.setDuracaoContrato(dadosSQL.getInt("duracao"));
        return ret;
    }

    public Integer getSaldoCreditoTreino() {
        return saldoCreditoTreino;
    }

    public void setSaldoCreditoTreino(Integer saldoCreditoTreino) {
        this.saldoCreditoTreino = saldoCreditoTreino;
    }

    public boolean isValidarSaldoCreditoTreino() {
        return validarSaldoCreditoTreino;
    }

    public void setValidarSaldoCreditoTreino(boolean validarSaldoCreditoTreino) {
        this.validarSaldoCreditoTreino = validarSaldoCreditoTreino;
    }

    public Integer getMinutosAposUltimoAcessoDiminuirCredito() {
        return minutosAposUltimoAcessoDiminuirCredito;
    }

    public void setMinutosAposUltimoAcessoDiminuirCredito(Integer minutosAposUltimoAcessoDiminuirCredito) {
        this.minutosAposUltimoAcessoDiminuirCredito = minutosAposUltimoAcessoDiminuirCredito;
    }

    public Date getDataUltimoVencimento() {
        return dataUltimoVencimento;
    }

    public void setDataUltimoVencimento(Date dataUltimoVencimento) {
        this.dataUltimoVencimento = dataUltimoVencimento;
    }

    public String getIdexternointegracao() {
        return idexternointegracao;
    }

    public void setIdexternointegracao(String idexternointegracao) {
        this.idexternointegracao = idexternointegracao;
    }

    public int getIdexterno() {
        return idexterno;
    }

    public void setIdexterno(int idexterno) {
        this.idexterno = idexterno;
    }

    public Integer getDuracaoContrato() {
        return duracaoContrato;
    }

    public void setDuracaoContrato(Integer duracaoContrato) {
        this.duracaoContrato = duracaoContrato;
    }
}

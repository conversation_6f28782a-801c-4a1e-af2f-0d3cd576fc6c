package negocio.comuns.acesso;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Date;

public class PessoaConsultaTO extends SuperTO {

    private static final long serialVersionUID = 7521566459580739754L;
    private final int codigo;
    private final String codAcesso;
    private final String codAcessoAlternativo;
    private final String situacao;
    private final PessoaVO pessoa;
    private final String tipo;
    private Integer codigoMatricula;

    public PessoaConsultaTO(ClienteVO cliente) {
        pessoa = cliente.getPessoa();
        codAcesso = cliente.getCodAcesso();
        codAcessoAlternativo = cliente.getCodAcessoAlternativo();
        codigoMatricula = cliente.getCodigoMatricula();
        situacao = cliente.getSituacao();
        tipo = TipoPessoaEnum.ALUNO.getTipo();
        codigo = cliente.getCodigo();
    }

    public PessoaConsultaTO(ColaboradorVO colaborador) {
        pessoa = colaborador.getPessoa();
        codAcesso = colaborador.getCodAcesso();
        codAcessoAlternativo = colaborador.getCodAcessoAlternativo();
        situacao = colaborador.getSituacao();
        tipo = TipoPessoaEnum.COLABORADOR.getTipo();
        codigo = colaborador.getCodigo();
    }

    public PessoaConsultaTO(JSONObject resposta) {
        codigo = resposta.optInt("codigo");
        codAcesso = resposta.optString("codAcesso");
        codAcessoAlternativo = resposta.optString("codAcessoAlternativo");
        situacao = resposta.optString("situacao");
        tipo = resposta.optString("tipo");
        codigoMatricula = resposta.optInt("codigoMatricula");

        JSONObject objPessoa = resposta.optJSONObject("pessoa");
        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setCodigo(objPessoa.optInt("codigo"));
        pessoaVO.setNome(objPessoa.optString("nome"));
        pessoaVO.setCfp(objPessoa.optString("cpf"));
        pessoaVO.setFotoKey(objPessoa.optString("fotoKey"));
        pessoaVO.setAssinaturaBiometriaFacial(objPessoa.optString("assinaturaBiometriaFacial"));
        pessoaVO.setAssinaturaBiometriaDigital(objPessoa.optString("assinaturaBiometriaDigital"));
        long dataNascimento = objPessoa.optLong("dataNasc");
        if (dataNascimento > 0) {
            pessoaVO.setDataNasc(new Date(dataNascimento));
        }

        JSONArray arrayEmails = objPessoa.optJSONArray("emails");
        for (int i = 0; i < arrayEmails.length(); i++) {
            JSONObject objEmail = arrayEmails.getJSONObject(i);
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(objEmail.optString("email"));
            pessoaVO.getEmailVOs().add(emailVO);
        }

        JSONArray arrayTelefones = objPessoa.optJSONArray("telefones");
        for (int i = 0; i < arrayTelefones.length(); i++) {
            JSONObject objTelefone = arrayTelefones.getJSONObject(i);
            TelefoneVO telefoneVO = new TelefoneVO();
            telefoneVO.setNumero(objTelefone.optString("numero"));
            pessoaVO.getTelefoneVOs().add(telefoneVO);
        }

        pessoa = pessoaVO;
    }

    public String getCodAcesso() {
        return codAcesso;
    }

    public String getCodAcessoAlternativo() {
        return codAcessoAlternativo;
    }

    public int getCodigo() {
        return codigo;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public String getSituacao() {
        return situacao;
    }

    public String getTipo() {
        return tipo;
    }

    public JSONObject toJSON() {
        JSONObject objJSON = new JSONObject();
        objJSON.put("codigo", codigo);
        objJSON.put("codAcesso", codAcesso);
        objJSON.put("codAcessoAlternativo", codAcessoAlternativo);
        objJSON.put("situacao", situacao);
        objJSON.put("tipo", tipo);
        objJSON.put("codigoMatricula", codigoMatricula);


        JSONObject pessoaJSON = new JSONObject();
        pessoaJSON.put("nome", pessoa.getNome());
        pessoaJSON.put("senhaAcesso", pessoa.getSenhaAcesso());
        pessoaJSON.put("codigo", pessoa.getCodigo());
        pessoaJSON.put("fotoKey", pessoa.getFotoKey());
        pessoaJSON.put("assinaturaBiometriaDigital", pessoa.getAssinaturaBiometriaDigital());
        pessoaJSON.put("assinaturaBiometriaFacial", pessoa.getAssinaturaBiometriaFacial());
        pessoaJSON.put("cpf", pessoa.getCfp());
        if (pessoa.getDataNasc() != null) {
            pessoaJSON.put("dataNasc", pessoa.getDataNasc().getTime());
        }

        JSONArray telefonesJSONs = new JSONArray();
        for (TelefoneVO telefoneVO : pessoa.getTelefoneVOs()) {
            JSONObject telefoneJSON = new JSONObject();
            telefoneJSON.put("numero", telefoneVO.getNumero());
            telefonesJSONs.put(telefoneJSON);
        }
        pessoaJSON.put("telefones", telefonesJSONs);

        JSONArray emailsJSONs = new JSONArray();
        for (EmailVO emailVO : pessoa.getEmailVOs()) {
            JSONObject emailJSON = new JSONObject();
            emailJSON.put("email", emailVO.getEmail());
            emailsJSONs.put(emailJSON);
        }
        pessoaJSON.put("emails", emailsJSONs);

        objJSON.put("pessoa", pessoaJSON);

        return objJSON;
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso;

import java.util.ArrayList;
import java.util.List;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public enum TipoHorarioEnum{

    TIPOHORARIO_Todos(0, "Todos"),
    TIPOHORARIO_HorarioContrato(1, "Horario Contrato"),
    TIPOHORARIO_HorarioTurma(2, "Horario Turma"),
    TIPOHORARIO_HorarioEspecifico(3, "Horario Especifico");
    private Integer id;
    private String descricao;

    private TipoHorarioEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public static List getSelectListTipo() {
        List temp = new ArrayList<TipoHorarioEnum>();
        for (int i = 0; i < TipoHorarioEnum.values().length; i++) {
            TipoHorarioEnum obj = TipoHorarioEnum.values()[i];
            temp.add(new SelectItem(obj, obj.getId() + " - " + obj.getDescricao()));
        }
        return temp;
    }

    public static TipoHorarioEnum valueOf(final int id) {
        switch (id) {
            default:
                return TIPOHORARIO_Todos;

            case 1:
                return TIPOHORARIO_HorarioContrato;

            case 2:
                return TIPOHORARIO_HorarioTurma;

            case 3:
                return TIPOHORARIO_HorarioEspecifico;

        }
    }

    @Override
    public String toString(){
        return descricao;
    }
}

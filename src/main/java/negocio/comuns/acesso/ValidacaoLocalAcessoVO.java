/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.acesso;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;

/**
 *
 * <AUTHOR>
 */
public class ValidacaoLocalAcessoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    @ChaveEstrangeira
    private ColetorVO coletor = new ColetorVO();
    @ChaveEstrangeira
    private EmpresaVO empresa = new EmpresaVO();
    private TipoValidacaoEnum tipoValidacao = TipoValidacaoEnum.TIPOVALIDACAO_Todos;
    private Integer chave = 0;
    private TipoHorarioEnum tipoHorario = TipoHorarioEnum.TIPOHORARIO_Todos;
    @ChaveEstrangeira
    private HorarioVO horario = new HorarioVO();
    @ChaveEstrangeira
    private UsuarioVO usuario = new UsuarioVO();
    private Date dataRegistro = Calendario.getInstance().getTime();
    @NaoControlarLogAlteracao
    private ProdutoVO produto = new ProdutoVO();
    @NaoControlarLogAlteracao
    private String tipoValidacao_Apresentar;
    @NaoControlarLogAlteracao
    private ModalidadeVO modalidade = new ModalidadeVO();


    public ValidacaoLocalAcessoVO(){
        super();
        this.empresa.setCodigo(0);
    }

    /**
     * Author: Ulisses
     * Data: 31/01/11
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ValidacaoLocalAcessoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas
     * neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação
     * de valores válidos para os atributos.
     *
     * @exception Exception
     *                Se uma inconsistência for encontrada, automaticamente é
     *                gerada uma exceção com a descrição da inconsistência.
     */
    public static void validarDados(ValidacaoLocalAcessoVO obj) throws Exception {
        if (obj.getColetor().getCodigo() <= 0)
            throw new Exception("Coletor não vinculado à Validação.");
        if ((obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Modalidade) &&
                (obj.getChave().intValue() <= 0))
            throw new Exception("O campo Modalidade, deve ser preenchido.");
        if ((obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Produto) &&
                (obj.getChave().intValue() <= 0))
            throw new Exception("O campo Produto, deve ser preenchido.");
        if ((obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_ProdutoGymPass) &&
                (obj.getChave().intValue() <= 0))
            throw new Exception("O campo Produto Gympass, deve ser preenchido.");
        if ((obj.getTipoHorario() == TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico) &&
                (obj.getHorario().getCodigo() <= 0))
            throw new Exception("O campo Horário, deve ser preenchido.");
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
    public Integer getChave() {
        return chave;
    }

    public void setChave(Integer chave) {
        this.chave = chave;
    }

    public ProdutoVO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public ColetorVO getColetor() {
        return coletor;
    }

    public void setColetor(ColetorVO coletor) {
        this.coletor = coletor;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public HorarioVO getHorario() {
        return horario;
    }

    public void setHorario(HorarioVO horario) {
        this.horario = horario;
    }

    public TipoHorarioEnum getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(TipoHorarioEnum tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public TipoValidacaoEnum getTipoValidacao() {
        return tipoValidacao;
    }

    public void setTipoValidacao(TipoValidacaoEnum tipoValidacao) {
        this.tipoValidacao = tipoValidacao;
    }

    public UsuarioVO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public String getTipoValidacao_Apresentar() {
        if (this.tipoValidacao == tipoValidacao.TIPOVALIDACAO_Produto){
            this.tipoValidacao_Apresentar = this.tipoValidacao.getDescricao() + ": " +
                    this.produto.getDescricao();
        }else if (this.tipoValidacao == tipoValidacao.TIPOVALIDACAO_ProdutoGymPass){
            this.tipoValidacao_Apresentar = this.tipoValidacao.getDescricao() + ": " +
                    this.getChave();
        }else if (this.tipoValidacao == tipoValidacao.TIPOVALIDACAO_Modalidade){
            this.tipoValidacao_Apresentar = this.tipoValidacao.getDescricao() + ": " +
                    this.getModalidade().getNome();
        }else if (this.tipoValidacao == tipoValidacao.TIPOVALIDACAO_Horario){
            if (this.tipoHorario == tipoHorario.TIPOHORARIO_HorarioEspecifico){
                this.tipoValidacao_Apresentar = this.tipoValidacao.getDescricao() + ": " +
                        this.getHorario().getDescricao();

            }else{
                this.tipoValidacao_Apresentar = this.tipoValidacao.getDescricao() + ": " +
                        this.tipoHorario.getDescricao();
            }
        }
        return tipoValidacao_Apresentar;
    }

    public ModalidadeVO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeVO modalidade) {
        this.modalidade = modalidade;
    }


}


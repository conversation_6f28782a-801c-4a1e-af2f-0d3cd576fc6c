/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso;

import java.util.ArrayList;
import java.util.List;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public enum TipoValidacaoEnum {
    TIPOVALIDACAO_Todos(0, "Todos"),
    TIPOVALIDACAO_Modalidade(1, "Modalidade"),
    TIPOVALIDACAO_Produto(2, "Produto"),
    TIPOVALIDACAO_Horario(3, "Horario"),
    TIPOVALIDACAO_ProdutoGymPass(4, "Produto GymPass");

    private Integer id;
    private String descricao;

    private TipoValidacaoEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public static List getSelectListTipo() {
        List temp = new ArrayList<TipoValidacaoEnum>();
        for (int i = 0; i < TipoHorarioEnum.values().length; i++) {
            TipoValidacaoEnum obj = TipoValidacaoEnum.values()[i];
            temp.add(new SelectItem(obj, obj.getId() + " - " + obj.getDescricao()));
        }
        return temp;
    }

    public static TipoValidacaoEnum valueOf(final int id) {
        switch (id) {
            default:
                return TIPOVALIDACAO_Todos;

            case 1:
                return TIPOVALIDACAO_Modalidade;

            case 2:
                return TIPOVALIDACAO_Produto;

            case 3:
                return TIPOVALIDACAO_Horario;

            case 4:
                return TIPOVALIDACAO_ProdutoGymPass;

        }
    }
    @Override
    public String toString(){
        return descricao;
    }
}

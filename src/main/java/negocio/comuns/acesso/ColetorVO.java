/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.acesso.enumerador.DispositivoAlternativoEnum;
import negocio.comuns.acesso.enumerador.ModeloColetorEnum;
import negocio.comuns.acesso.enumerador.ModoTransmissaoEnum;
import negocio.comuns.acesso.enumerador.SentidoAcessoEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ColetorVO extends SuperVO implements Serializable {

    @ChavePrimaria
    private Integer codigo = 0;
    private Integer numeroTerminal;
    private String descricao = "";
    private ModeloColetorEnum modelo;
    private String numSerie = "";
    private String portaComunicacao;
    private String portaLeitorSerial;//quando ModeloColetorEnum for SerialPacto
    private String portaParalela = "378";
    private ModoTransmissaoEnum modoTransmissao;
    private Integer velocTransmissao;
    private Integer resolucaoDPI;
    private Integer releEntrada;
    private Integer tempoReleEntrada;
    private Integer releSaida;
    private Integer tempoReleSaida;
    private String msgDisplay = "";
    private SentidoAcessoEnum sentidoAcesso;
    private Boolean aguardaGiro = true;
    @NaoControlarLogAlteracao
    private transient byte[] arquivoPrograma;
    private Boolean padraoCadastro;
    private Integer localAcesso = 0;
    private Integer sensorEntrada;
    private Integer sensorSaida;
    @Lista(nome="Validações de Acesso")
    private List<ValidacaoLocalAcessoVO> permissoes = new ArrayList<ValidacaoLocalAcessoVO>();
    private Boolean desativado = false;
    private DispositivoAlternativoEnum dispAlternativo = DispositivoAlternativoEnum.NENHUM_DISP;
    private Integer digitosLeituraCartao = 0;
    private Boolean inverterSinal = false;
    private String ip = "";
    private Integer porta = 0;
    private String cartaoMaster = "";
    private Boolean biometrico = false;
    //Passa a não mais buscar a biometria armazenada na catraca mas sim no Acesso
    @NaoControlarLogAlteracao
    private Boolean biometriaNaCatraca = false;
    private int leitorGertec = 0;
    private Integer codigoAux; // atributo transient
    private Integer numTerminalAcionamento;
    private Integer indiceCamera;
    private boolean usarSenhaAcessoComoBiometria = false;
    private boolean usarCatracaOffline = false;
    private boolean usarNumSerieFinger = false;
    private String numSeriePlc = "";
    private Boolean utilizarMatriculaComoSenha;
    private Boolean padraoCadastroFacial;
    private Boolean usaFacial = false;
    private Boolean usaRtsp = false;
    private String ipServidor = "";
    private String mascaraSubrede = "";
    private Integer portaServidor = 0;

    private String codigoNFC;
    private String longitude;
    private String latitude;
    private String usuarioColetor = "";
    private String senhaColetor = "";

    private Double maxTemperatura = 0.0;
    private Boolean usoMascaraObrigatorio = false;

    public ColetorVO() {
        super();
        inicializarDadosDefault();
    }

    public ColetorVO(JSONObject mJsonObject) {
        super();
        try {
            setCodigo(mJsonObject.getInt("codigo"));
            setDescricao(mJsonObject.getString("descricao"));
            setNumeroTerminal(mJsonObject.getInt("numeroTerminal"));
            // TODO mapear o objeto de acordo com as necessidades
        } catch (JSONException ex) {
            Logger.getLogger(ColetorVO.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    public void inicializarDadosDefault() {
        if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALACTUAR
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_PARALELATECNIBRA
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALTECNIBRA
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_TCPTECNIBRA
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_PARALELAHENRY
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_HENRYTCPIP
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_HENRYSERIAL
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY7xV2
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_ACTUARTCPIP) {
            this.setReleEntrada(0);
            this.setSensorEntrada(0);
            this.setReleSaida(1);
            this.setSensorSaida(1);
            this.setTempoReleSaida(5000);
            this.setTempoReleEntrada(5000);
            this.setAguardaGiro(true);
            this.setModoTransmissao(ModoTransmissaoEnum.MODOTRANSMISSAO_COLETOR_AUTO);

            if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_PARALELAHENRY) {
                this.setPortaParalela("378");
            }


            if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_ACTUARTCPIP){
                this.setDigitosLeituraCartao(0);
                this.setIp("");
                this.setPorta(1001);
            }
        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_TCPINNERTOPDATA
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_INNERTOPDATA  ) {
            setPortaComunicacao("1");
            setModoTransmissao(ModoTransmissaoEnum.MODOTRANSMISSAO_COLETOR_AUTO);
            setVelocTransmissao(9600);
            setResolucaoDPI(512);
            setReleEntrada(2);
            setTempoReleEntrada(5000);
            setReleSaida(3);
            setTempoReleSaida(5000);
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_OESPERADO);
            setAguardaGiro(true);
            setPadraoCadastro(false);
            setSensorEntrada(0);
            setSensorSaida(1);
            setPadraoCadastroFacial(false);
        } else if ( this.modelo == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS) {
            setPortaComunicacao("1");
            setModoTransmissao(ModoTransmissaoEnum.MODOTRANSMISSAO_COLETOR_AUTO);
            setVelocTransmissao(9600);
            setResolucaoDPI(512);
            setReleEntrada(3);
            setTempoReleEntrada(5000);
            setReleSaida(2);
            setTempoReleSaida(5000);
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_OESPERADO);
            setAguardaGiro(true);
            setPadraoCadastro(false);
            setSensorEntrada(0);
            setSensorSaida(1);
            setPadraoCadastroFacial(false);
            setUsaFacial(false);
        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_NEOKOROSNKFP2
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_NEOKOROSNKFP3
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_NEOKOROSFP730) {
            setPortaComunicacao("1");
            setModoTransmissao(ModoTransmissaoEnum.MODOTRANSMISSAO_COLETOR_AUTO);
            setVelocTransmissao(9600);
            setResolucaoDPI(512);
            setReleEntrada(4);
            setTempoReleEntrada(5000);
            setReleSaida(5);
            setTempoReleSaida(5000);
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_OESPERADO);
            setAguardaGiro(true);
            setPadraoCadastro(false);
            setSensorEntrada(1);
            setSensorSaida(2);
            setPadraoCadastroFacial(false);
        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_ALMITEC ||
                this.modelo == ModeloColetorEnum.MODELO_COLETOR_ALMITECMAC400 ) {
            setDispAlternativo(DispositivoAlternativoEnum.CODIGO_BARRAS);

            setMsgDisplay("Bem vindo");
            setModoTransmissao(ModoTransmissaoEnum.MODOTRANSMISSAO_COLETOR_AUTO);
            setVelocTransmissao(9600);
            setResolucaoDPI(512);

            setReleEntrada(0);
            setSensorEntrada(0);
            setTempoReleEntrada(5000);

            setReleSaida(2);
            setSensorSaida(1);
            setTempoReleSaida(5000);

            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_OESPERADO);
            setAguardaGiro(true);
            setPadraoCadastro(false);
            setPadraoCadastroFacial(false);
        } else if (this.modelo == ModeloColetorEnum.MODELO_LEITOR_NITGEN || this.modelo == ModeloColetorEnum.MODELO_COLETOR_DESCONHECIDO
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_ZK_TECO
                || this.getModelo() == ModeloColetorEnum.MODELO_CAMERA_ACIONAMENTO || this.getModelo() == ModeloColetorEnum.MODELO_COLETOR_TCA_SERIAL) {
            setReleEntrada(0);
            setSensorEntrada(0);
            setSensorSaida(1);
            setReleSaida(1);
            if(this.modelo == ModeloColetorEnum.MODELO_LEITOR_NITGEN) {
                setNumeroTerminal(255);
            }
            if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_ZK_TECO) {
                setNumeroTerminal(0);
                setReleEntrada(1);
                setSensorEntrada(1);
                setSensorSaida(2);
                setReleSaida(2);
            }

        }
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_TCA_SERIAL){
            setLeitorGertec(0);
            setDigitosLeituraCartao(10);
            setPortaComunicacao("3");
            setReleEntrada(1);
            setSensorEntrada(1);
            setSensorSaida(2);
            setReleSaida(2);
            setTempoReleEntrada(3000);
            setTempoReleSaida(3000);
        }else if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_ALMITECTCPIPSECDS){
            setReleEntrada(1);
            setReleSaida(2);
            setTempoReleEntrada(5000);
            setTempoReleSaida(5000);
            setSensorEntrada(0);
            setSensorSaida(1);
        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8x
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8XFS
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8xLV) {
            setReleEntrada(1);
            setReleSaida(2);
            setTempoReleEntrada(10000);
            setTempoReleSaida(10000);
            setPorta(3000);
            setBiometrico(Boolean.FALSE);
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_OESPERADO);
            setSensorEntrada(0);
            setSensorSaida(1);
            setNumeroTerminal(1);
            setDigitosLeituraCartao(10);
        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8xFLAP) {
            setMsgDisplay("SEJA BEM VINDO");
            setIp("************");
            setPorta(3000);
            setReleEntrada(1);
            setSensorEntrada(0);
            setTempoReleEntrada(3000);
            setReleSaida(2);
            setSensorSaida(1);
            setTempoReleSaida(3000);
            setDigitosLeituraCartao(8);
            setBiometrico(true);
            setAguardaGiro(true);
        } else if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_TRIXXPBLOCK){
            setReleEntrada(1);
            setReleSaida(2);
            setTempoReleEntrada(5000);
            setTempoReleSaida(5000);
            setSensorEntrada(0);
            setSensorSaida(1);
            setMsgDisplay("BEM VINDO!!!");
            setAguardaGiro(true);
            setPortaComunicacao("COM1");
        } else if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8xPRIMME){
            setDispAlternativo(DispositivoAlternativoEnum.NENHUM_DISP);
            setLeitorGertec(0);
            setDigitosLeituraCartao(0);
            setReleEntrada(1);
            setReleSaida(2);
            setTempoReleEntrada(5000);
            setTempoReleSaida(5000);
            setSensorEntrada(0);
            setSensorSaida(1);
            setIp("");
            setPorta(3000);
            setBiometrico(Boolean.TRUE);
        }else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_TCPINNERTOPDATA
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_INNERTOPDATA
                || this.modelo == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS) {
            setPortaComunicacao("1");
            setModoTransmissao(ModoTransmissaoEnum.MODOTRANSMISSAO_COLETOR_AUTO);
            setVelocTransmissao(9600);
            setResolucaoDPI(512);
            setReleEntrada(2);
            setTempoReleEntrada(5000);
            setReleSaida(3);
            setTempoReleSaida(5000);
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_OESPERADO);
            setAguardaGiro(true);
            setPadraoCadastro(false);
            setSensorEntrada(0);
            setSensorSaida(1);
            setPadraoCadastroFacial(false);
            setUsaFacial(false);

        }else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_ALMITEC ||
                this.modelo == ModeloColetorEnum.MODELO_COLETOR_ALMITECMAC400) {
            setDispAlternativo(DispositivoAlternativoEnum.CODIGO_BARRAS);

            setMsgDisplay("Bem vindo");
            setModoTransmissao(ModoTransmissaoEnum.MODOTRANSMISSAO_COLETOR_AUTO);
            setVelocTransmissao(9600);
            setResolucaoDPI(512);

            setReleEntrada(0);
            setSensorEntrada(0);
            setTempoReleEntrada(5000);

            setReleSaida(2);
            setSensorSaida(1);
            setTempoReleSaida(5000);

            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_OESPERADO);
            setAguardaGiro(true);
            setPadraoCadastro(false);
            setPadraoCadastroFacial(false);
        }else if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_ACTUAR_LITENET2){
            setReleEntrada(1);
            setReleSaida(2);
            setSensorEntrada(1);
            setSensorSaida(2);
            setTempoReleSaida(5000);
            setTempoReleEntrada(5000);
            setIp("**************");
            setPorta(7878);
        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_HIKVISION_ISAPI){
            setMsgDisplay("Bem vindo");

            setIpServidor("*************");
            setPortaServidor(7377);

            setIp("************");
            setPorta(80);

            setUsuarioColetor("admin");
            setSenhaColetor("pct12345");

            setMaxTemperatura(0.00);
            setUsoMascaraObrigatorio(false);

            setDigitosLeituraCartao(10);
            setPadraoCadastroFacial(true);

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);

        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX){
            setMsgDisplay("Bem vindo");

            setIpServidor("*************");
            setPortaServidor(7377);

            setIp("*************");
            setPorta(80);

            setUsuarioColetor("admin");
            setSenhaColetor("pct12345");

            setUsoMascaraObrigatorio(false);

            setDigitosLeituraCartao(10);
            setPadraoCadastroFacial(true);

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setAguardaGiro(true);

        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX){
            setMsgDisplay("Bem vindo");

            setIpServidor("*************");
            setPortaServidor(7377);

            setIp("*************");
            setPorta(80);

            setUsuarioColetor("admin");
            setSenhaColetor("pct12345");

            setUsoMascaraObrigatorio(false);

            setDigitosLeituraCartao(10);
            setPadraoCadastroFacial(true);

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setAguardaGiro(true);

        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR){
            setMsgDisplay("Bem vindo");

            setIpServidor("*************");
            setPortaServidor(7377);

            setIp("*************");
            setPorta(80);

            setUsuarioColetor("admin");
            setSenhaColetor("pct12345");

            setUsoMascaraObrigatorio(false);

            setDigitosLeituraCartao(10);
            setPadraoCadastroFacial(true);

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setAguardaGiro(true);

        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR){
            setMsgDisplay("Bem vindo");

            setIpServidor("*************");
            setPortaServidor(7377);

            setIp("*************");
            setPorta(80);

            setUsuarioColetor("admin");
            setSenhaColetor("pct12345");

            setUsoMascaraObrigatorio(false);

            setDigitosLeituraCartao(10);
            setPadraoCadastroFacial(true);

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setAguardaGiro(true);

        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671M_L){
            setMsgDisplay("Bem vindo");

            setIpServidor("*************");
            setPortaServidor(7377);

            setIp("*************");
            setPorta(80);

            setUsuarioColetor("admin");
            setSenhaColetor("pct12345");

            setUsoMascaraObrigatorio(false);

            setDigitosLeituraCartao(10);
            setPadraoCadastroFacial(true);

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setAguardaGiro(true);

        } else if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF){
            setMsgDisplay("Bem vindo");

            setIpServidor("*************");
            setPortaServidor(7377);

            setIp("*************");
            setPorta(80);

            setUsuarioColetor("admin");
            setSenhaColetor("pct12345");

            setMaxTemperatura(0.00);
            setUsoMascaraObrigatorio(false);

            setDigitosLeituraCartao(10);
            setPadraoCadastroFacial(true);

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setAguardaGiro(true);

        } else {
            setPortaComunicacao("1");
            setModoTransmissao(ModoTransmissaoEnum.MODOTRANSMISSAO_COLETOR_AUTO);
            setVelocTransmissao(9600);
            setResolucaoDPI(512);
            setReleEntrada(1);
            setTempoReleEntrada(5000);
            setReleSaida(2);
            setTempoReleSaida(5000);
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_OESPERADO);
            setAguardaGiro(true);
            setPadraoCadastro(false);
            setSensorEntrada(0);
            setSensorSaida(1);
            setPadraoCadastroFacial(false);
        }
        if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALFOCA){
            setMsgDisplay("Bem vindo");
            setSensorEntrada(2);
        }
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALPACTO ||
                this.modelo == ModeloColetorEnum.MODELO_COLETOR_USBSERIAL ||
                this.modelo == ModeloColetorEnum.MODELO_COLETOR_PARALELAHENRY ||
                this.modelo == ModeloColetorEnum.MODELO_COLETOR_PARALELATECNIBRA || this.modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALFOCA){
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_ENTRADA);
        }
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_MA100){
            setCartaoMaster("admin");
        }
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY7xV2){
           setDigitosLeituraCartao(8);
           setPorta(3000);
        }
        setLeitorGertec(0);
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_INOVACESSO){
            setReleEntrada(0);
            setReleSaida(1);
            setTempoReleEntrada(5000);
            setTempoReleSaida(5000);
            setSensorEntrada(1);
            setSensorSaida(2);
            setMsgDisplay("");
            setIp("");
            setPorta(2051);
            setUsarSenhaAcessoComoBiometria(false);

        }
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALNATSO){
            setReleEntrada(1);
            setReleSaida(2);
            setTempoReleEntrada(5000);
            setTempoReleSaida(5000);
            setSensorEntrada(1);
            setSensorSaida(2);
            setMsgDisplay("");
            setPortaComunicacao("1");
        }
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_INTEGRA_FACIL){
            setDigitosLeituraCartao(0);
            setReleEntrada(1);
            setSensorEntrada(3);
            setTempoReleEntrada(5000);
            setReleSaida(2);
            setSensorSaida(4);
            setTempoReleSaida(5000);
            setMsgDisplay("");
            setIp("");
            setPorta(8080);

        }
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_TUPA){
            setDigitosLeituraCartao(8);
            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO_E_CODIGO_BARRAS);
            setReleEntrada(1);
            setSensorEntrada(1);
            setTempoReleEntrada(5000);
            setReleSaida(2);
            setSensorSaida(2);
            setTempoReleSaida(5000);
            setIp("");
            setPorta(5151);
        }
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_SYSTEMTECV4){
            setDigitosLeituraCartao(0);
            setReleEntrada(1);
            setSensorEntrada(1);
            setReleSaida(2);
            setSensorSaida(2);
            setIp("");
            setPorta(1001);
        }
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_INTERLAKEN){
            setDigitosLeituraCartao(0);
            setReleEntrada(1);
            setSensorEntrada(3);
            setTempoReleEntrada(5000);
            setReleSaida(2);
            setSensorSaida(4);
            setTempoReleSaida(5000);
            setMsgDisplay("");
            setIp("localhost");
            setPorta(80);
        }
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_ZUCHIMZ4){
            setMsgDisplay("BEM VINDO!!!");
            setPortaComunicacao("3");
            setDigitosLeituraCartao(10);
            setReleEntrada(1);
            setSensorEntrada(3);
            setTempoReleEntrada(5000);
            setReleSaida(2);
            setSensorSaida(4);
            setTempoReleSaida(5000);
            setAguardaGiro(true);
        }
        if(!isTemLeitorSerial()){
            setPortaLeitorSerial("");
        }
        if(!isTemPortaCOM()){
            setPortaComunicacao("");
        }
        if (isColetorZKTF1700()) {
            setMsgDisplay("BEM VINDO");
            setIpServidor("************");
            setIp("*************");
            setMascaraSubrede("*************");
            setPorta(4370);
            setReleEntrada(1);
            setTempoReleEntrada(3000);
            setResolucaoDPI(500);
        }
        if (isColetorControlIdBlock() ||
                isColetorControlIdFlex()) {
            setIpServidor("************");
            setPortaServidor(8888);
            setIp("*************");
            setPorta(80);
            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO_E_URNA);
            setDigitosLeituraCartao(10);
            setMsgDisplay("BEM VINDO");
            setBiometrico(true);
            setReleEntrada(1);
            setSensorEntrada(3);
            setTempoReleEntrada(5000);
            setReleSaida(2);
            setSensorSaida(4);
            setTempoReleSaida(5000);
            if (isColetorControlIdFlex()) {
                setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
                setTempoReleEntrada(4000);
                setTempoReleSaida(4000);

            }
        }
        if (isColetorControlIdAccessPro()) {
            setIpServidor("************");
            setPortaServidor(8888);
            setIp("*************");
            setPorta(80);
            setTempoReleEntrada(1000);
            setTempoReleSaida(1000);
            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setDigitosLeituraCartao(10);
            setMsgDisplay("BEM VINDO");
            setBiometrico(true);
            setAguardaGiro(true);
        }
        if (isColetorHollemax()) {
            setIp("*************");
            setPorta(8299);
            setTempoReleEntrada(5000);
            setTempoReleSaida(2000);
            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setDigitosLeituraCartao(10);
            setMsgDisplay("BEM VINDO");
            setReleEntrada(1);
            setSensorEntrada(1);
            setReleSaida(2);
            setSensorSaida(2);
            setBiometrico(true);
            setAguardaGiro(true);
            setBiometrico(Boolean.FALSE);
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_OESPERADO);
        }
        if (isColetorControlIdBlockNext()) {
            setIpServidor("************");
            setPortaServidor(8888);
            setIp("*************");
            setPorta(80);
            setReleEntrada(1);
            setSensorEntrada(3);
            setTempoReleEntrada(5000);
            setReleSaida(2);
            setSensorSaida(4);
            setTempoReleSaida(5000);
            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO_E_URNA);
            setDigitosLeituraCartao(10);
            setMsgDisplay("BEM VINDO");
            setBiometrico(true);
        }
        if (this.modelo == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS_LC) {
            setMsgDisplay("BEM VINDO");
            setReleEntrada(2);
            setTempoReleEntrada(5000);
            setReleSaida(3);
            setTempoReleSaida(5000);
            setDispAlternativo(DispositivoAlternativoEnum.CODIGO_BARRAS);
            setDigitosLeituraCartao(10);
            setCartaoMaster("007");
            setAguardaGiro(true);
            setBiometrico(true);
        }
        if(this.modelo == ModeloColetorEnum.MODELO_COLETOR_TECNEW_SERIAL){
            setReleEntrada(1);
            setReleSaida(2);
            setTempoReleEntrada(5000);
            setTempoReleSaida(5000);
            setSensorEntrada(0);
            setSensorSaida(2);
            setMsgDisplay("BEM VINDO!!!");
            setAguardaGiro(true);
            setPortaComunicacao("COM1");
        }
        if (isColetorDimepMicropoint() || isColetorDimepDGate()) {
            setIp("*************");
            setPorta(3000);
            setAguardaGiro(true);
            setReleEntrada(1);
            setSensorEntrada(3);
            setReleSaida(2);
            setSensorSaida(4);
            if (isColetorDimepMicropoint()) {
                setTempoReleEntrada(3500);
                setTempoReleSaida(3500);
                setDigitosLeituraCartao(12);
            } else {
                setTempoReleEntrada(4000);
                setTempoReleSaida(4000);
                setDigitosLeituraCartao(10);
            }
        }
        if (isColetorControlIdIdFace()){
            setDescricao("RECEPCAO | *************");
            setNumTerminalAcionamento(0);
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_ENTRADA);

            setMsgDisplay("Bem Vindo");
            setIpServidor("************");
            setPortaServidor(8888);
            setIp("*************");
            setPorta(80);
            setTempoReleEntrada(1000);
            setTempoReleSaida(1000);

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setDigitosLeituraCartao(10);
            setPadraoCadastroFacial(true);
            setUsoMascaraObrigatorio(false);
        }
        if (isColetorIntelbrasSS5531MF()){
            setDescricao("RECEPCAO | Intelbras SS 5531");
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_ENTRADA);

            setMsgDisplay("Bem Vindo");
            setIpServidor("************");
            setPortaServidor(3000);
            setIp("*************");
            setPorta(80);
            setTempoReleEntrada(2000);
            setTempoReleSaida(2000);

            setUsuarioColetor("admin");
            setSenhaColetor("pct12345");

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setDigitosLeituraCartao(12);
            setPadraoCadastroFacial(false);
            setUsoMascaraObrigatorio(false);
            setUsaRtsp(false);
            setUsaFacial(true);
        }
        if (isColetorIntelbrasSS5530MF()){
            setDescricao("RECEPCAO | Intelbras SS 5531");
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_ENTRADA);

            setMsgDisplay("Bem Vindo");
            setIpServidor("************");
            setPortaServidor(3000);
            setIp("*************");
            setPorta(80);
            setTempoReleEntrada(2000);
            setTempoReleSaida(2000);

            setUsuarioColetor("admin");
            setSenhaColetor("pct12345");

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setDigitosLeituraCartao(12);
            setPadraoCadastroFacial(false);
            setUsoMascaraObrigatorio(false);
            setUsaRtsp(false);
            setUsaFacial(true);
        }
        if(isColetorIntebrasSS3530MF()){
            setDescricao("RECEPCAO | Intelbras SS 3530");
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_ENTRADA);

            setMsgDisplay("Bem Vindo");
            setIpServidor("************");
            setPortaServidor(3000);
            setIp("*************");
            setPorta(80);
            setTempoReleEntrada(2000);

            setUsuarioColetor("admin");
            setSenhaColetor("pct12345");

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setDigitosLeituraCartao(12);
            setPadraoCadastroFacial(false);
            setUsoMascaraObrigatorio(false);
            setUsaRtsp(false);
            setUsaFacial(true);
        }
        if (isColetorZenitZFace()) {
            setDescricao("RECEPCAO | ZFACE");
            setNumTerminalAcionamento(0);
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_ENTRADA);

            setMsgDisplay("Bem Vindo");
            setIpServidor("*************");
            setPortaServidor(6666);
            setIp("************* ");
            setPorta(80);
            setTempoReleEntrada(1000);
            setTempoReleSaida(1000);

            setUsuarioColetor("admin");
            setSenhaColetor("admin");
            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO);
            setDigitosLeituraCartao(10);
            setUsoMascaraObrigatorio(false);
        }
        if (isColetorInnerAcesso3()) {
            setDescricao("RECEPCAO | *************");
            setNumTerminalAcionamento(0);
            setSentidoAcesso(SentidoAcessoEnum.SENTIDOACESSO_COLETOR_ENTRADA);

            setMsgDisplay("Bem Vindo");
            setIpServidor("************");
            setPortaServidor(8888);
            setIp("*************");
            setPorta(80);
            setTempoReleEntrada(1000);
            setReleEntrada(2);
            setReleSaida(3);
            setTempoReleSaida(1000);
            setCartaoMaster("007");

            setDispAlternativo(DispositivoAlternativoEnum.APROXIMACAO_E_CODIGO_BARRAS);
            setDigitosLeituraCartao(10);
            setPadraoCadastroFacial(true);
            setAguardaGiro(true);
            setBiometrico(true);
            setUsoMascaraObrigatorio(false);
        }
    }

    /**
     * Author: Ulisses
     * Data: 18/01/11
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ColetorVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas
     * neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação
     * de valores válidos para os atributos.
     *
     * @exception Exception
     *                Se uma inconsistência for encontrada, automaticamente é
     *                gerada uma exceção com a descrição da inconsistência.
     */
    public static void validarDados(ColetorVO obj) throws Exception {
        if (obj.getLocalAcesso() <= 0) {
            throw new Exception("Local não vinculado ao Coletor");
        }
        if ((obj.getNumeroTerminal() == null) || (obj.getNumeroTerminal() <= 0)) {
            throw new Exception("O campo Número do Terminal, deve ser preenchido.");
        }
        if (obj.getDescricao().trim().isEmpty()) {
            throw new Exception("O campo Descrição(Coletor), deve ser preenchido.");
        }
        if (obj.getModelo() == null) {
            throw new Exception("O campo Protocolo, deve ser preenchido.");
        }
        if (obj.getSentidoAcesso() == null) {
            throw new Exception("O campo Sentido Acesso, deve ser preenchido.");
        }

        if (obj.getPortaLeitorSerial() != null && !obj.getPortaLeitorSerial().isEmpty()) {
            if (obj.getPortaLeitorSerial().equals(obj.getPortaComunicacao())
                    && !obj.getModelo().equals( ModeloColetorEnum.MODELO_COLETOR_SERIALPACTO) && !obj.getModelo().equals( ModeloColetorEnum.MODELO_COLETOR_SERIALFOCA)
                    && !obj.getModelo().equals(ModeloColetorEnum.MODELO_COLETOR_USBSERIAL)) {
                throw new Exception("A porta de comunicação (COM) do Leitor Serial não pode ser a mesma da Catraca.");
            }
        }

        if (obj.getModelo() == ModeloColetorEnum.MODELO_COLETOR_INNERTOPDATA
                || obj.getModelo() == ModeloColetorEnum.MODELO_COLETOR_TCPINNERTOPDATA) {
            if (obj.getReleEntrada() == obj.getReleSaida()) {
                throw new Exception(obj.getDescricao() + ": Os relês não podem ser iguais");
            }
            if ((obj.getReleEntrada() == 2 || obj.getReleEntrada() == 7)
                    && (obj.getReleSaida() == 2 || obj.getReleSaida() == 7)) {
                throw new Exception(obj.getDescricao() + ": Os dois relês estão configurados para entrada");
            }
            if ((obj.getReleEntrada() == 3 || obj.getReleEntrada() == 6)
                    && (obj.getReleSaida() == 3 || obj.getReleSaida() == 6)) {
                throw new Exception(obj.getDescricao() + ": Os dois relês estão configurados para saída");
            }
        }
        if (obj.getModelo() == ModeloColetorEnum.MODELO_COLETOR_PARALELAHENRY
                || obj.getModelo() == ModeloColetorEnum.MODELO_COLETOR_PARALELATECNIBRA) {
            if (obj.getReleEntrada() == obj.getReleSaida()) {
                throw new Exception(obj.getDescricao() + ": Os relês não podem ser iguais");
            }
            if (obj.getReleEntrada() < 0 || obj.getReleEntrada() > 1
                    || obj.getReleSaida() < 0 || obj.getReleSaida() > 1) {
                throw new Exception(obj.getDescricao() + ": Os relês só podem assumir valor 0 ou 1 para o modelo " + obj.getModelo().getDescricao());
            }
        }
    }

    public void atribuirConfiguracoesEspecificas() {
        if (modelo == ModeloColetorEnum.MODELO_COLETOR_INNERTOPDATA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_TCPINNERTOPDATA) {
            sensorEntrada = 1;
            sensorSaida = 0;
            tempoReleSaida = tempoReleEntrada;
        }
    }

    public Boolean getAguardaGiro() {
        return aguardaGiro;
    }

    public void setPermissoes(List<ValidacaoLocalAcessoVO> permissoes) {
        this.permissoes = permissoes;
    }

    public void setAguardaGiro(Boolean aguardaGiro) {
        this.aguardaGiro = aguardaGiro;
    }

    public byte[] getArquivoPrograma() {
        return arquivoPrograma;
    }

    public void setArquivoPrograma(byte[] arquivoPrograma) {
        this.arquivoPrograma = arquivoPrograma;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            return "";
        } else {
            return descricao;
        }

    }

    public void setDescricao(String descricao) {
        this.descricao = descricao.toUpperCase();
    }

    public ModeloColetorEnum getModelo() {
        return modelo;
    }

    public void setModelo(String modelo) {
        this.modelo = ModeloColetorEnum.valueOf(modelo);
    }

    public void setModelo(ModeloColetorEnum modelo) {
        this.modelo = modelo;
    }

    public ModoTransmissaoEnum getModoTransmissao() {
        return modoTransmissao;
    }

    public void setModoTransmissao(ModoTransmissaoEnum modoTransmissao) {
        this.modoTransmissao = modoTransmissao;
    }

    public void setModoTransmissao(String modoTransmissao) {
        this.modoTransmissao = ModoTransmissaoEnum.valueOf(modoTransmissao);
    }

    public String getMsgDisplay() {
        if (msgDisplay == null) {
            return "";
        } else {
            return msgDisplay;
        }
    }

    public void setMsgDisplay(String msgDisplay) {
        this.msgDisplay = msgDisplay.toUpperCase();
    }

    public String getNumSerie() {
        if (numSerie == null) {
            return "";
        } else {
            return numSerie;
        }

    }

    public void setNumSerie(String numSerie) {
        this.numSerie = numSerie;
    }

    public Boolean getPadraoCadastro() {
        return padraoCadastro;
    }

    public void setPadraoCadastro(Boolean padraoCadastro) {
        this.padraoCadastro = padraoCadastro;
    }

    public String getPortaComunicacao() {
        return portaComunicacao;
    }

    public void setPortaComunicacao(String portaComunicacao) {
        this.portaComunicacao = portaComunicacao;
    }

    public String getPortaLeitorSerial() {
        return portaLeitorSerial;
    }

    public void setPortaLeitorSerial(String portaLeitorSerial) {
        this.portaLeitorSerial = portaLeitorSerial;
    }

    public Integer getReleEntrada() {
        return releEntrada;
    }

    public void setReleEntrada(Integer releEntrada) {
        this.releEntrada = releEntrada;
    }

    public Integer getReleSaida() {
        return releSaida;
    }

    public void setReleSaida(Integer releSaida) {
        this.releSaida = releSaida;
    }

    public Integer getResolucaoDPI() {
        return resolucaoDPI;
    }

    public void setResolucaoDPI(Integer resolucaoDPI) {
        this.resolucaoDPI = resolucaoDPI;
    }

    public SentidoAcessoEnum getSentidoAcesso() {
        return sentidoAcesso;
    }

    public void setSentidoAcesso(SentidoAcessoEnum sentidoAcesso) {
        this.sentidoAcesso = sentidoAcesso;
    }

    public void setSentidoAcesso(String sentidoAcesso) {
        this.sentidoAcesso = SentidoAcessoEnum.valueOf(sentidoAcesso);
    }

    public Integer getTempoReleEntrada() {
        return tempoReleEntrada;
    }

    public void setTempoReleEntrada(Integer tempoReleEntrada) {
        this.tempoReleEntrada = tempoReleEntrada;
        // Se é um tempo de acionamento genérico, o tempo de saída é o mesmo que o de entrada
        if(isUsaTempoAcionamentoGenerico()){
            setTempoReleSaida(tempoReleEntrada);
        }
    }

    public Integer getTempoReleSaida() {
        return tempoReleSaida;
    }

    public void setTempoReleSaida(Integer tempoReleSaida) {
        this.tempoReleSaida = tempoReleSaida;
    }

    public Integer getVelocTransmissao() {
        return velocTransmissao;
    }

    public void setVelocTransmissao(Integer velocTransmissao) {
        this.velocTransmissao = velocTransmissao;
    }

    public Integer getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(Integer localAcesso) {
        this.localAcesso = localAcesso;
    }

    public Integer getSensorEntrada() {
        return sensorEntrada;
    }

    public void setSensorEntrada(Integer sensorEntrada) {
        this.sensorEntrada = sensorEntrada;
    }

    public Integer getSensorSaida() {
        return sensorSaida;
    }

    public void setSensorSaida(Integer sensorSaida) {
        this.sensorSaida = sensorSaida;
    }

    public Integer getNumeroTerminal() {
        return numeroTerminal;
    }

    public void setNumeroTerminal(Integer numeroTerminal) {
        this.numeroTerminal = numeroTerminal;
    }

    public void carregarPermissoes() throws Exception {

        permissoes = getFacade().getValidacaoLocalAcesso().consultar(
                "coletor = " + getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

    }

    public List<ValidacaoLocalAcessoVO> getPermissoes() {
        Collections.sort(permissoes, new ComparatorValidacaoPorEmpresa());
        return permissoes;
    }

    public List getListaColunasValidacao() {
        return ValidacaoLocalAcessoVO.obterListaColunasVO(ValidacaoLocalAcessoVO.class);
    }

    @Override
    public String toString() {
        return this.getNumeroTerminal() + " - " + this.getDescricao();
    }

    public Boolean getDesativado() {
        return desativado;
    }

    public void setDesativado(Boolean desativado) {
        this.desativado = desativado;
    }

    public String getPortaParalela() {
        return portaParalela;
    }

    public void setPortaParalela(String portaParalela) {
        this.portaParalela = portaParalela;
    }

    public DispositivoAlternativoEnum getDispAlternativo() {
        return dispAlternativo;
    }

    public void setDispAlternativo(DispositivoAlternativoEnum dispAlternativo) {
        this.dispAlternativo = dispAlternativo;
    }

    public Integer getDigitosLeituraCartao() {
        return digitosLeituraCartao;
    }

    public void setDigitosLeituraCartao(Integer digitosLeituraCartao) {
        this.digitosLeituraCartao = digitosLeituraCartao;
    }

    public Boolean getInverterSinal() {
        return inverterSinal;
    }

    public void setInverterSinal(Boolean inverterSinal) {
        this.inverterSinal = inverterSinal;
    }

    public boolean isTemPortaCOM() {
        return modelo != ModeloColetorEnum.MODELO_COLETOR_PARALELAHENRY
                && modelo != ModeloColetorEnum.MODELO_COLETOR_PARALELATECNIBRA
                && modelo != ModeloColetorEnum.MODELO_COLETOR_TCPINNERTOPDATA
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS
                && modelo != ModeloColetorEnum.MODELO_COLETOR_TCPTECNIBRA
                && modelo != ModeloColetorEnum.MODELO_COLETOR_NEOKOROSNKFP3
                && modelo != ModeloColetorEnum.MODELO_COLETOR_NEOKOROSFP730
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HENRYTCPIP
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ALMITECTCPIPSECDS
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HENRY8x
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HENRY8XFS
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HENRY8xPRIMME
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HENRY7xV2
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ACTUARTCPIP
                && modelo != ModeloColetorEnum.MODELO_COLETOR_BIOMTECH
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INOVACESSO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTEGRA_FACIL
                && modelo != ModeloColetorEnum.MODELO_COLETOR_TUPA
                && modelo != ModeloColetorEnum.MODELO_COLETOR_SYSTEMTECV4
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTERLAKEN
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZK_TF1700
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDBLOCK
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDFLEX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS_LC
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZK_TECO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_DIMEP_MICROPOINT
                && modelo != ModeloColetorEnum.MODELO_COLETOR_DIMEP_DGATE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HENRY8xFLAP
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ACTUAR_LITENET2
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKVISION_ISAPI
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INNERACESSO3
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDBLOCKNEXT
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDACCESSPRO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HOLLEMAX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671M_L
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF
                ;
    }

    public boolean isTemDispAlternativo() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_INNERTOPDATA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_TCPINNERTOPDATA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS
                || modelo == ModeloColetorEnum.MODELO_COLETOR_PARALELATECNIBRA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_TCPTECNIBRA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALTECNIBRA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_NEOKOROSNKFP3
                || modelo == ModeloColetorEnum.MODELO_COLETOR_NEOKOROSFP730
                || modelo == ModeloColetorEnum.MODELO_COLETOR_ALMITEC
                || modelo == ModeloColetorEnum.MODELO_COLETOR_ALMITECMAC400
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDBLOCK
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDFLEX
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS_LC
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDFACE
                || modelo == ModeloColetorEnum.MODELO_COLETOR_ZFACE
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INNERACESSO3
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDBLOCKNEXT
                || modelo == ModeloColetorEnum.MODELO_COLETOR_TUPA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDACCESSPRO
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF;
    }

    public boolean isTemMsgDisplay() {
        return modelo != ModeloColetorEnum.MODELO_COLETOR_TUPA
                && modelo != ModeloColetorEnum.MODELO_COLETOR_SYSTEMTECV4
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZK_TECO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_TCA_SERIAL
                && modelo != ModeloColetorEnum.MODELO_COLETOR_DIMEP_MICROPOINT
                && modelo != ModeloColetorEnum.MODELO_COLETOR_DIMEP_DGATE;
    }

    public boolean isTemDigitosLeituraCartao() {
        return modelo != ModeloColetorEnum.MODELO_COLETOR_BIOMTECH
            && modelo != ModeloColetorEnum.MODELO_COLETOR_SERIALNATSO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_SYSTEMTECV4
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZK_TF1700
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZK_TECO;
    }

    public boolean isTemPortaParalela() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_PARALELAHENRY
                || modelo == ModeloColetorEnum.MODELO_COLETOR_PARALELATECNIBRA;
    }

    public boolean isTemLeitorSerial() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALPACTO
                || modelo == ModeloColetorEnum.MODELO_COLETOR_USBSERIAL
                || modelo == ModeloColetorEnum.MODELO_COLETOR_PARALELAHENRY
                || modelo == ModeloColetorEnum.MODELO_COLETOR_TCA_SERIAL
                || modelo == ModeloColetorEnum.MODELO_COLETOR_ZUCHIMZ4
                || modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALFOCA;
    }

    public boolean isUsaLeitorGertec(){
        return modelo != ModeloColetorEnum.MODELO_COLETOR_BIOMTECH
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZK_TF1700
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDBLOCK
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDFLEX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZK_TECO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_DIMEP_MICROPOINT
                && modelo != ModeloColetorEnum.MODELO_COLETOR_DIMEP_DGATE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKVISION_ISAPI
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDBLOCKNEXT
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671M_L
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF
                ;
    }

    public boolean isModeloInner() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_INNERTOPDATA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_TCPINNERTOPDATA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS;
    }
    public boolean isApresentarCartaoMaster(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_INNERTOPDATA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS
                || modelo == ModeloColetorEnum.MODELO_COLETOR_TCPINNERTOPDATA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_MA100
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS_LC
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INNERACESSO3
                ;
    }

    public boolean isApresentarBiometria() {
        return isModeloInner()
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY7xV2
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8x
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8xPRIMME
                || modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALTECNIBRA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_TCPTECNIBRA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8XFS
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDBLOCK
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDFLEX
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS_LC
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8xFLAP
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INNERACESSO3
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDBLOCKNEXT
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDACCESSPRO
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HOLLEMAX
                ;
    }
    public boolean isTemSensorEntrada() {
        return modelo != ModeloColetorEnum.MODELO_COLETOR_INNERTOPDATA
                && modelo != ModeloColetorEnum.MODELO_COLETOR_TCPINNERTOPDATA
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS
                && modelo != ModeloColetorEnum.MODELO_COLETOR_BIOMTECH
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZK_TF1700
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS_LC
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INNERACESSO3
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKVISION_ISAPI
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDACCESSPRO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671M_L
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF
                ;
    }

    public boolean isTemSensorSaida() {
        return modelo != ModeloColetorEnum.MODELO_COLETOR_INNERTOPDATA
                && modelo != ModeloColetorEnum.MODELO_COLETOR_TCPINNERTOPDATA
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS
                && modelo != ModeloColetorEnum.MODELO_COLETOR_BIOMTECH
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZK_TF1700
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS_LC
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INNERACESSO3
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKVISION_ISAPI
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDACCESSPRO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671M_L
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF
                ;
    }

    public boolean isPodeUsarFacial() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8XFS
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8xFLAP
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8xPRIMME
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8xLV
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8x
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HOLLEMAX
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF;
    }

    public boolean isPodeUsarRtsp() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_IDBLOCKNEXT
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDFACE
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF;
    }

    public boolean isTemIpPorta() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY7xV2
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRYTCPIP
                || modelo == ModeloColetorEnum.MODELO_COLETOR_ALMITECTCPIPSECDS
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8x
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8xLV
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8XFS
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8xPRIMME
                || modelo == ModeloColetorEnum.MODELO_COLETOR_MA100
                || modelo == ModeloColetorEnum.MODELO_COLETOR_ACTUARTCPIP
                || modelo == ModeloColetorEnum.MODELO_COLETOR_BIOMTECH
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INOVACESSO
                || modelo == ModeloColetorEnum.MODELO_COLETOR_TCPTECNIBRA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTEGRA_FACIL
                || modelo == ModeloColetorEnum.MODELO_COLETOR_TUPA
                || modelo == ModeloColetorEnum.MODELO_COLETOR_SYSTEMTECV4
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTERLAKEN
                || modelo == ModeloColetorEnum.MODELO_COLETOR_ZK_TF1700
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDBLOCK
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDFLEX
                || modelo == ModeloColetorEnum.MODELO_COLETOR_DIMEP_MICROPOINT
                || modelo == ModeloColetorEnum.MODELO_COLETOR_DIMEP_DGATE
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HENRY8xFLAP
                || modelo == ModeloColetorEnum.MODELO_COLETOR_ACTUAR_LITENET2
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HIKVISION_ISAPI
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDFACE
                || modelo == ModeloColetorEnum.MODELO_COLETOR_ZFACE
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDBLOCKNEXT
                || modelo == ModeloColetorEnum.MODELO_COLETOR_IDACCESSPRO
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HOLLEMAX
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671M_L
                || modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF
                ;
    }

    public boolean isPodeAguardarGiro() {
        return modelo != ModeloColetorEnum.MODELO_COLETOR_ZUCHIMZ4
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS_LC
                && modelo != ModeloColetorEnum.MODELO_COLETOR_DIMEP_MICROPOINT
                && modelo != ModeloColetorEnum.MODELO_COLETOR_DIMEP_DGATE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HENRY8xFLAP
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDFLEX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDBLOCKNEXT
                ;
    }

    public boolean isTemInversaoSinal() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALACTUAR
                || modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALPACTO
                || modelo == ModeloColetorEnum.MODELO_COLETOR_USBSERIAL
                || modelo == ModeloColetorEnum.MODELO_COLETOR_SERIALFOCA;
    }

    public boolean isValidarUsoMascara(){
        return isColetorHikvisionIsapi() || isColetorHikvisionDSK1T342MFWX() || isColetorHikvisionDSK1T673DX()
                || isColetorHikvisionDSK1T342MWX_BR() || isColetorHikvisionDSK1T673DX_BR()
                || isColetorHikvisionDSK1T671M_L() || isColetorHikvisionDSK1T671TM_3XF();
    }

    public boolean isValidarMaxTemperatura(){
        return isColetorHikvisionIsapi() || isColetorHikvisionDSK1T671TM_3XF();
    }

    public boolean isTemReleEntrada() {
        return  modelo != ModeloColetorEnum.MODELO_COLETOR_BIOMTECH
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKVISION_ISAPI
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDACCESSPRO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671M_L
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF;
    }

    public boolean isUsaColetorComoBiometria() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_INOVACESSO;
    }

    public boolean isUsaCatracaOffline(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_TCPTECNIBRA;
    }

    public boolean isTemIpServidor() {
        return isColetorZKTF1700() || isColetorControlIdBlock() || isColetorControlIdFlex()
                || isColetorControlIdIdFace() || isColetorZenitZFace() || isColetorHikvisionIsapi()
                || isColetorControlIdBlockNext() || isColetorControlIdAccessPro() || isColetorIntelbrasSS5530MF()
                || isColetorIntelbrasSS5531MF() || isColetorIntebrasSS3530MF() || isColetorHikvisionDSK1T342MFWX()
                || isColetorHikvisionDSK1T673DX() || isColetorHikvisionDSK1T342MWX_BR()
                || isColetorHikvisionDSK1T673DX_BR() || isColetorHikvisionDSK1T671M_L()
                || isColetorHikvisionDSK1T671TM_3XF();
    }

    public boolean isTemPortaServidor() {
        return isColetorControlIdBlock() || isColetorControlIdFlex() || isColetorControlIdIdFace()
                || isColetorZenitZFace() || isColetorHikvisionIsapi() || isColetorControlIdBlockNext()
                || isColetorControlIdAccessPro() || isColetorIntelbrasSS5530MF() || isColetorIntelbrasSS5531MF()
                || isColetorIntebrasSS3530MF() || isColetorHikvisionDSK1T342MFWX() || isColetorHikvisionDSK1T673DX()
                || isColetorHikvisionDSK1T342MWX_BR() || isColetorHikvisionDSK1T673DX_BR()
                || isColetorHikvisionDSK1T671M_L() || isColetorHikvisionDSK1T671TM_3XF();
    }

    public boolean isTemUsuario(){
        return isColetorIntebrasSS3530MF() || isColetorHikvisionIsapi() || isColetorZenitZFace()
                || isColetorIntelbrasSS5530MF() || isColetorIntelbrasSS5531MF() || isColetorIntebrasSS3530MF()
                || isColetorHikvisionDSK1T342MFWX() || isColetorHikvisionDSK1T673DX()
                || isColetorHikvisionDSK1T342MWX_BR() || isColetorHikvisionDSK1T673DX_BR()
                || isColetorHikvisionDSK1T671M_L() || isColetorHikvisionDSK1T671TM_3XF();
    }

    public boolean isTemSenha(){
        return isColetorIntebrasSS3530MF() || isColetorHikvisionIsapi() || isColetorZenitZFace()
                || isColetorIntelbrasSS5530MF() || isColetorIntelbrasSS5531MF() || isColetorIntebrasSS3530MF()
                || isColetorHikvisionDSK1T342MFWX() || isColetorHikvisionDSK1T673DX()
                || isColetorHikvisionDSK1T342MWX_BR() || isColetorHikvisionDSK1T673DX_BR()
                || isColetorHikvisionDSK1T671M_L() || isColetorHikvisionDSK1T671TM_3XF();
    }

    public boolean isColetorIntebrasSS3530MF(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF;
    }

    public boolean isColetorHikvisionIsapi(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_HIKVISION_ISAPI;
    }

    public boolean isColetorHikvisionDSK1T342MFWX(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX;
    }

    public boolean isColetorHikvisionDSK1T673DX(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX;
    }

    public boolean isColetorHikvisionDSK1T342MWX_BR(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR;
    }

    public boolean isColetorHikvisionDSK1T673DX_BR(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR;
    }

    public boolean isColetorHikvisionDSK1T671M_L(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671M_L;
    }

    public boolean isColetorHikvisionDSK1T671TM_3XF(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF;
    }

    public boolean isColetorZKTF1700(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_ZK_TF1700;
    }

    public boolean isColetorControlIdBlock(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_IDBLOCK;
    }

    public boolean isColetorControlIdFlex(){
        return modelo == ModeloColetorEnum.MODELO_COLETOR_IDFLEX;
    }

    public boolean isColetorControlIdBlockNext() {
        return  modelo == ModeloColetorEnum.MODELO_COLETOR_IDBLOCKNEXT;
    }

    public boolean isColetorControlIdAccessPro() {
        return  modelo == ModeloColetorEnum.MODELO_COLETOR_IDACCESSPRO;
    }

    public boolean isColetorHollemax() {
        return  modelo == ModeloColetorEnum.MODELO_COLETOR_HOLLEMAX;
    }

    public boolean isColetorDimepMicropoint() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_DIMEP_MICROPOINT;
    }

    public boolean isColetorDimepDGate() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_DIMEP_DGATE;
    }

    public boolean isColetorControlIdIdFace() { return modelo == ModeloColetorEnum.MODELO_COLETOR_IDFACE; }

    public boolean isColetorIntelbrasSS5530MF() { return modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF; }

    public boolean isColetorIntelbrasSS5531MF() { return modelo == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF; }

    public boolean isColetorInnerAcesso3() { return modelo == ModeloColetorEnum.MODELO_COLETOR_INNERACESSO3; }


    public boolean isColetorZenitZFace() { return modelo == ModeloColetorEnum.MODELO_COLETOR_ZFACE; }


    public JSONObject toJSON() {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                o.put(field.getName(), field.get(this));
            }
        } catch (Exception e) {
        }
        return o;
    }

    public boolean isUsaTempoAcionamentoGenerico(){
        return isColetorControlIdIdFace() || isColetorZenitZFace() || isColetorControlIdAccessPro()
                || isColetorIntelbrasSS5530MF() || isColetorIntelbrasSS5531MF() || isColetorIntebrasSS3530MF();
    }

    public boolean isTemReleSaida(){
        return modelo != ModeloColetorEnum.MODELO_COLETOR_BIOMTECH
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZK_TF1700
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKVISION_ISAPI
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDACCESSPRO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671M_L
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF;
    }

    public boolean isUsaTempoAcionamentoEntrada(){
        return modelo != ModeloColetorEnum.MODELO_COLETOR_BIOMTECH
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKVISION_ISAPI
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDACCESSPRO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671M_L
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF;

    }

    public boolean isUsaTempoAcionamentoSaida(){
        return modelo != ModeloColetorEnum.MODELO_COLETOR_BIOMTECH
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZK_TF1700
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_ZFACE
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKVISION_ISAPI
                && modelo != ModeloColetorEnum.MODELO_COLETOR_IDACCESSPRO
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS5531MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MFWX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T342MWX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T673DX_BR
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671M_L
                && modelo != ModeloColetorEnum.MODELO_COLETOR_HIKIVISION_DS_K1T671TM_3XF;
    }

    public Boolean getBiometriaNaCatraca() {
        return biometriaNaCatraca;
    }

    public void setBiometriaNaCatraca(Boolean biometriaNaCatraca) {
        this.biometriaNaCatraca = biometriaNaCatraca;
    }

    public String getCartaoMaster() {
        return cartaoMaster;
    }

    public void setCartaoMaster(String cartaoMaster) {
        this.cartaoMaster = cartaoMaster;
    }

    public Boolean getBiometrico() {
        return biometrico;
    }

    public void setBiometrico(Boolean biometrico) {
        this.biometrico = biometrico;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPorta() {
        return porta;
    }

    public void setPorta(Integer porta) {
        this.porta = porta;
    }

    public String getSituacao_Apresentar() {
        if (desativado) {
            return "Sim";
        } else  {
            return "Não";
        }
    }

    public int getLeitorGertec() {
        return leitorGertec;
    }

    public void setLeitorGertec(int leitorGertec) {
        this.leitorGertec = leitorGertec;
    }

    public Integer getCodigoAux() {
        return codigoAux;
}

    public void setCodigoAux(Integer codigoAux) {
        this.codigoAux = codigoAux;
    }

    public Integer getNumTerminalAcionamento() {
        return numTerminalAcionamento;
    }

    public void setNumTerminalAcionamento(Integer numTerminalAcionamento) {
        this.numTerminalAcionamento = numTerminalAcionamento;
    }

    public Integer getIndiceCamera() {
        return indiceCamera;
    }

    public void setIndiceCamera(Integer indiceCamera) {
        this.indiceCamera = indiceCamera;
    }

    public boolean isUsarSenhaAcessoComoBiometria() {
        return usarSenhaAcessoComoBiometria;
    }

    public void setUsarSenhaAcessoComoBiometria(boolean usarSenhaAcessoComoBiometria) {
        this.usarSenhaAcessoComoBiometria = usarSenhaAcessoComoBiometria;
    }

    public boolean isUsarCatracaOffline() {
        return usarCatracaOffline;
    }

    public void setUsarCatracaOffline(boolean usarCatracaOffline) {
        this.usarCatracaOffline = usarCatracaOffline;
    }

    public boolean isUsarNumSerieFinger() {
        return modelo == ModeloColetorEnum.MODELO_COLETOR_INTEGRA_FACIL
                || modelo == ModeloColetorEnum.MODELO_COLETOR_INTERLAKEN;
    }

    public void setUsarNumSerieFinger(boolean usarNumSerieFinger) {
        this.usarNumSerieFinger = usarNumSerieFinger;
    }

    public String getNumSeriePlc() {
        if (numSeriePlc == null) {
            return "";
        } else {
            return numSeriePlc;
        }
    }
    public void setNumSeriePlc(String numSeriePlc) {
        this.numSeriePlc = numSeriePlc;
    }

    public Boolean getUtilizarMatriculaComoSenha() {
        return utilizarMatriculaComoSenha;
    }

    public void setUtilizarMatriculaComoSenha(Boolean utilizarMatriculaComoSenha) {
        this.utilizarMatriculaComoSenha = utilizarMatriculaComoSenha;
    }

    public Boolean getPadraoCadastroFacial() {
        return padraoCadastroFacial;
    }

    public void setPadraoCadastroFacial(Boolean padraoCadastroFacial) {
        this.padraoCadastroFacial = padraoCadastroFacial;
    }

    public Boolean getUsaFacial() {
        if (usaFacial == null) {
            usaFacial = false;
        }
        return usaFacial;
    }

    public void setUsaFacial(Boolean usaFacial) {
        this.usaFacial = usaFacial;
    }

    public Boolean getUsaRtsp() {
        if (usaRtsp == null) {
            usaRtsp = false;
        }
        return usaRtsp;
    }

    public void setUsaRtsp(Boolean usaRtsp) {
        this.usaRtsp = usaRtsp;
    }

    public String getIpServidor() {
        return ipServidor;
    }

    public void setIpServidor(String ipServidor) {
        this.ipServidor = ipServidor;
    }

    public String getMascaraSubrede() {
        return mascaraSubrede;
    }

    public void setMascaraSubrede(String mascaraSubrede) {
        this.mascaraSubrede = mascaraSubrede;
    }

    public Integer getPortaServidor() {
        return portaServidor;
    }

    public void setPortaServidor(Integer portaServidor) {
        this.portaServidor = portaServidor;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getCodigoNFC() {
        return codigoNFC;
    }

    public void setCodigoNFC(String codigoNFC) {
        this.codigoNFC = codigoNFC;
    }

    public String getUsuarioColetor() {
        return usuarioColetor;
    }

    public void setUsuarioColetor(String usuarioColetor) {
        this.usuarioColetor = usuarioColetor;
    }

    public String getSenhaColetor() {
        return senhaColetor;
    }

    public void setSenhaColetor(String senhaColetor) {
        this.senhaColetor = senhaColetor;
    }

    public Double getMaxTemperatura() {
        return maxTemperatura;
    }

    public void setMaxTemperatura(Double maxTemperatura) {
        this.maxTemperatura = maxTemperatura;
    }

    public Boolean getUsoMascaraObrigatorio() {
        return usoMascaraObrigatorio;
    }

    public void setUsoMascaraObrigatorio(Boolean usoMascaraObrigatorio) {
        this.usoMascaraObrigatorio = usoMascaraObrigatorio;
    }
}

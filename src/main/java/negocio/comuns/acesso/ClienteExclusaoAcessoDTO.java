package negocio.comuns.acesso;

public class ClienteExclusaoAcessoDTO {
    
    private Integer empresa;
    private String matricula;
    private Integer pessoa;
    private String codacesso;
    private String codacessoalternativo;

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getCodacesso() {
        return codacesso;
    }

    public void setCodacesso(String codacesso) {
        this.codacesso = codacesso;
    }

    public String getCodacessoalternativo() {
        return codacessoalternativo;
    }

    public void setCodacessoalternativo(String codacessoalternativo) {
        this.codacessoalternativo = codacessoalternativo;
    }
}

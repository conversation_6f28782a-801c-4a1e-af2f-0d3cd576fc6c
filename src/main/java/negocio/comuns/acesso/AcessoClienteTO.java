/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso;

import br.com.pacto.priv.utils.Uteis;
import br.com.pactosolucoes.comuns.util.Formatador;
import java.util.Date;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.CaixaAbertoTO;

/**
 *
 * <AUTHOR>
 */
public class AcessoClienteTO {
    
    private ClienteVO cliente = new ClienteVO();
    private Date dataHoraEntrada;
    private Date dataHoraSaida;
    private LocalAcessoVO localAcesso = new LocalAcessoVO();
    private ColetorVO coletor = new ColetorVO();
    private UsuarioVO usuario = new UsuarioVO();
    private CaixaAbertoTO caixaAberto = new CaixaAbertoTO();
    private Double valorTotal = 0.0;

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public Date getDataHoraEntrada() {
        return dataHoraEntrada;
    }

    public void setDataHoraEntrada(Date dataHoraEntrada) {
        this.dataHoraEntrada = dataHoraEntrada;
    }

    public Date getDataHoraSaida() {
        return dataHoraSaida;
    }

    public void setDataHoraSaida(Date dataHoraSaida) {
        this.dataHoraSaida = dataHoraSaida;
    }

    public LocalAcessoVO getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(LocalAcessoVO localAcesso) {
        this.localAcesso = localAcesso;
    }

    public ColetorVO getColetor() {
        return coletor;
    }

    public void setColetor(ColetorVO coletor) {
        this.coletor = coletor;
    }

    public UsuarioVO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public CaixaAbertoTO getCaixaAberto() {
        return caixaAberto;
    }

    public void setCaixaAberto(CaixaAbertoTO caixaAberto) {
        this.caixaAberto = caixaAberto;
    }
    
    public String getDataComHoraEntradaApresentar(){
        return Uteis.getDataComHHMM(dataHoraEntrada);
    }
    
    public String getMatriculaCliente(){
        return getCliente().getMatricula();
    }
    
    public String getNomeCliente(){
        return getCliente().getPessoa().getNome();
    }
    
    public String getDescricaoCaixaAberto(){
        return getCaixaAberto().getDescricao();
    }
    
    public String getDataLancamentoCaixaAberto(){
        return getCaixaAberto().getLancamentoFormatada();
    }
    
    public String getValorCaixaAberto(){
        return getCaixaAberto().getValorTotalMonetario();
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }
    
    public String getValorTotalMonetario(){
        return Formatador.formatarValorMonetario(this.getValorTotal());
    }
}


package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for integracaoAcessoWS complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="integracaoAcessoWS">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.acesso/}superJSON">
 *       &lt;sequence>
 *         &lt;element name="chaveEmpresaRemota" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoChaveIntegracaoDigitais" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoEmpresaRemota" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "integracaoAcessoWS", propOrder = {
    "chaveEmpresaRemota",
    "codigoChaveIntegracaoDigitais",
    "codigoEmpresaRemota"
})
public class IntegracaoAcessoWS
    extends SuperJSON
{

    protected String chaveEmpresaRemota;
    protected Integer codigoChaveIntegracaoDigitais;
    protected Integer codigoEmpresaRemota;

    /**
     * Gets the value of the chaveEmpresaRemota property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChaveEmpresaRemota() {
        return chaveEmpresaRemota;
    }

    /**
     * Sets the value of the chaveEmpresaRemota property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChaveEmpresaRemota(String value) {
        this.chaveEmpresaRemota = value;
    }

    /**
     * Gets the value of the codigoChaveIntegracaoDigitais property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoChaveIntegracaoDigitais() {
        return codigoChaveIntegracaoDigitais;
    }

    /**
     * Sets the value of the codigoChaveIntegracaoDigitais property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoChaveIntegracaoDigitais(Integer value) {
        this.codigoChaveIntegracaoDigitais = value;
    }

    /**
     * Gets the value of the codigoEmpresaRemota property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoEmpresaRemota() {
        return codigoEmpresaRemota;
    }

    /**
     * Sets the value of the codigoEmpresaRemota property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoEmpresaRemota(Integer value) {
        this.codigoEmpresaRemota = value;
    }

}


package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for registrarAcesso complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="registrarAcesso">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="dataAcesso" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="direcao" type="{http://webservice.acesso/}direcaoAcessoEnum" minOccurs="0"/>
 *         &lt;element name="empresa" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="local" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="meioIdentificacao" type="{http://webservice.acesso/}meioIdentificacaoEnum" minOccurs="0"/>
 *         &lt;element name="situacao" type="{http://webservice.acesso/}situacaoAcessoEnum" minOccurs="0"/>
 *         &lt;element name="terminal" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="tipo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="usuario" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "registrarAcesso", propOrder = {
    "codigo",
    "dataAcesso",
    "direcao",
    "empresa",
    "key",
    "local",
    "meioIdentificacao",
    "situacao",
    "terminal",
    "tipo",
    "usuario"
})
public class RegistrarAcesso {

    protected Integer codigo;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataAcesso;
    protected DirecaoAcessoEnum direcao;
    protected Integer empresa;
    protected String key;
    protected Integer local;
    protected MeioIdentificacaoEnum meioIdentificacao;
    protected SituacaoAcessoEnum situacao;
    protected Integer terminal;
    protected String tipo;
    protected Integer usuario;

    /**
     * Gets the value of the codigo property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Sets the value of the codigo property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Gets the value of the dataAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataAcesso() {
        return dataAcesso;
    }

    /**
     * Sets the value of the dataAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataAcesso(XMLGregorianCalendar value) {
        this.dataAcesso = value;
    }

    /**
     * Gets the value of the direcao property.
     * 
     * @return
     *     possible object is
     *     {@link DirecaoAcessoEnum }
     *     
     */
    public DirecaoAcessoEnum getDirecao() {
        return direcao;
    }

    /**
     * Sets the value of the direcao property.
     * 
     * @param value
     *     allowed object is
     *     {@link DirecaoAcessoEnum }
     *     
     */
    public void setDirecao(DirecaoAcessoEnum value) {
        this.direcao = value;
    }

    /**
     * Gets the value of the empresa property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getEmpresa() {
        return empresa;
    }

    /**
     * Sets the value of the empresa property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setEmpresa(Integer value) {
        this.empresa = value;
    }

    /**
     * Gets the value of the key property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Sets the value of the key property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Gets the value of the local property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getLocal() {
        return local;
    }

    /**
     * Sets the value of the local property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setLocal(Integer value) {
        this.local = value;
    }

    /**
     * Gets the value of the meioIdentificacao property.
     * 
     * @return
     *     possible object is
     *     {@link MeioIdentificacaoEnum }
     *     
     */
    public MeioIdentificacaoEnum getMeioIdentificacao() {
        return meioIdentificacao;
    }

    /**
     * Sets the value of the meioIdentificacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link MeioIdentificacaoEnum }
     *     
     */
    public void setMeioIdentificacao(MeioIdentificacaoEnum value) {
        this.meioIdentificacao = value;
    }

    /**
     * Gets the value of the situacao property.
     * 
     * @return
     *     possible object is
     *     {@link SituacaoAcessoEnum }
     *     
     */
    public SituacaoAcessoEnum getSituacao() {
        return situacao;
    }

    /**
     * Sets the value of the situacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link SituacaoAcessoEnum }
     *     
     */
    public void setSituacao(SituacaoAcessoEnum value) {
        this.situacao = value;
    }

    /**
     * Gets the value of the terminal property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getTerminal() {
        return terminal;
    }

    /**
     * Sets the value of the terminal property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setTerminal(Integer value) {
        this.terminal = value;
    }

    /**
     * Gets the value of the tipo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipo() {
        return tipo;
    }

    /**
     * Sets the value of the tipo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipo(String value) {
        this.tipo = value;
    }

    /**
     * Gets the value of the usuario property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getUsuario() {
        return usuario;
    }

    /**
     * Sets the value of the usuario property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setUsuario(Integer value) {
        this.usuario = value;
    }

}

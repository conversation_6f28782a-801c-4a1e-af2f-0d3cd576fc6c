
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for retornoRequisicaoWS complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="retornoRequisicaoWS">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="msgErro" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="resultado" type="{http://webservice.acesso/}resultadoWSEnum" minOccurs="0"/>
 *         &lt;element name="terminal" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "retornoRequisicaoWS", propOrder = {
    "msgErro",
    "resultado",
    "terminal"
})
@XmlSeeAlso({
    RetornoRequisicaoValidarPermissaoUsuario.class,
    RetornoRequisicaoBuscarLocais.class,
    RetornoRequisicaoValidacaoAcesso.class,
    RetornoRequisicaoBuscarCodigoAcesso.class,
    RetornoRequisicaoRegistrarAcesso.class,
    RetornoRequisicaoDadosOffline.class,
    RetornoRequisicaoLiberacaoAcesso.class,
    RetornoRequisicaoBuscarIntegracoes.class,
    RetornoRequisicaoConsultarClientes.class
})
public class RetornoRequisicaoWS {

    protected String msgErro;
    protected ResultadoWSEnum resultado;
    protected String terminal;

    /**
     * Gets the value of the msgErro property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsgErro() {
        return msgErro;
    }

    /**
     * Sets the value of the msgErro property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsgErro(String value) {
        this.msgErro = value;
    }

    /**
     * Gets the value of the resultado property.
     * 
     * @return
     *     possible object is
     *     {@link ResultadoWSEnum }
     *     
     */
    public ResultadoWSEnum getResultado() {
        return resultado;
    }

    /**
     * Sets the value of the resultado property.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultadoWSEnum }
     *     
     */
    public void setResultado(ResultadoWSEnum value) {
        this.resultado = value;
    }

    /**
     * Gets the value of the terminal property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTerminal() {
        return terminal;
    }

    /**
     * Sets the value of the terminal property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTerminal(String value) {
        this.terminal = value;
    }

}

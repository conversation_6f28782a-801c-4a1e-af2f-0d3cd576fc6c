
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for retornoRequisicaoRegistrarAcesso complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="retornoRequisicaoRegistrarAcesso">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.acesso/}retornoRequisicaoWS">
 *       &lt;sequence>
 *         &lt;element name="acessoRegistrado" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "retornoRequisicaoRegistrarAcesso", propOrder = {
    "acessoRegistrado"
})
public class RetornoRequisicaoRegistrarAcesso
    extends RetornoRequisicaoWS
{

    protected Boolean acessoRegistrado;

    /**
     * Gets the value of the acessoRegistrado property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isAcessoRegistrado() {
        return acessoRegistrado;
    }

    /**
     * Sets the value of the acessoRegistrado property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setAcessoRegistrado(Boolean value) {
        this.acessoRegistrado = value;
    }

}

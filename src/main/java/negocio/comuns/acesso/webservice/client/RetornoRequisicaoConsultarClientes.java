
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for retornoRequisicaoConsultarClientes complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="retornoRequisicaoConsultarClientes">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.acesso/}retornoRequisicaoWS">
 *       &lt;sequence>
 *         &lt;element name="clientesXML" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "retornoRequisicaoConsultarClientes", propOrder = {
    "clientesXML"
})
public class RetornoRequisicaoConsultarClientes
    extends RetornoRequisicaoWS
{

    protected String clientesXML;

    /**
     * Gets the value of the clientesXML property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClientesXML() {
        return clientesXML;
    }

    /**
     * Sets the value of the clientesXML property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClientesXML(String value) {
        this.clientesXML = value;
    }

}

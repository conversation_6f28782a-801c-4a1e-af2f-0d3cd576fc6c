
package negocio.comuns.acesso.webservice.client;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for retornoRequisicaoBuscarLocais complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="retornoRequisicaoBuscarLocais">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.acesso/}retornoRequisicaoWS">
 *       &lt;sequence>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="descricao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="listaColetores" type="{http://webservice.acesso/}coletorWS" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="nomeComputador" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pedirSenhaCadastrarMaisBiometrias" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="pedirSenhaLibParaCadaAcesso" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="portaImagens" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="portaServidorCamera" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="portaServidorImp" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="servidorImpressoes" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tempoEntreAcessos" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="tempoEntreAcessosColaborador" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="urlServidorCamera" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="usarReconhecimento" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="utilizarModoOffline" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "retornoRequisicaoBuscarLocais", propOrder = {
    "codigo",
    "descricao",
    "listaColetores",
    "nomeComputador",
    "pedirSenhaCadastrarMaisBiometrias",
    "pedirSenhaLibParaCadaAcesso",
    "portaImagens",
    "portaServidorCamera",
    "portaServidorImp",
    "servidorImpressoes",
    "tempoEntreAcessos",
    "tempoEntreAcessosColaborador",
    "urlServidorCamera",
    "usarReconhecimento",
    "utilizarModoOffline"
})
public class RetornoRequisicaoBuscarLocais
    extends RetornoRequisicaoWS
{

    protected Integer codigo;
    protected String descricao;
    @XmlElement(nillable = true)
    protected List<ColetorWS> listaColetores;
    protected String nomeComputador;
    protected Boolean pedirSenhaCadastrarMaisBiometrias;
    protected Boolean pedirSenhaLibParaCadaAcesso;
    protected Integer portaImagens;
    protected Integer portaServidorCamera;
    protected Integer portaServidorImp;
    protected String servidorImpressoes;
    protected Integer tempoEntreAcessos;
    protected Integer tempoEntreAcessosColaborador;
    protected String urlServidorCamera;
    protected Boolean usarReconhecimento;
    protected Boolean utilizarModoOffline;

    /**
     * Gets the value of the codigo property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Sets the value of the codigo property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Gets the value of the descricao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescricao() {
        return descricao;
    }

    /**
     * Sets the value of the descricao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescricao(String value) {
        this.descricao = value;
    }

    /**
     * Gets the value of the listaColetores property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the listaColetores property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getListaColetores().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ColetorWS }
     * 
     * 
     */
    public List<ColetorWS> getListaColetores() {
        if (listaColetores == null) {
            listaColetores = new ArrayList<ColetorWS>();
        }
        return this.listaColetores;
    }

    /**
     * Gets the value of the nomeComputador property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeComputador() {
        return nomeComputador;
    }

    /**
     * Sets the value of the nomeComputador property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeComputador(String value) {
        this.nomeComputador = value;
    }

    /**
     * Gets the value of the pedirSenhaCadastrarMaisBiometrias property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isPedirSenhaCadastrarMaisBiometrias() {
        return pedirSenhaCadastrarMaisBiometrias;
    }

    /**
     * Sets the value of the pedirSenhaCadastrarMaisBiometrias property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setPedirSenhaCadastrarMaisBiometrias(Boolean value) {
        this.pedirSenhaCadastrarMaisBiometrias = value;
    }

    /**
     * Gets the value of the pedirSenhaLibParaCadaAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isPedirSenhaLibParaCadaAcesso() {
        return pedirSenhaLibParaCadaAcesso;
    }

    /**
     * Sets the value of the pedirSenhaLibParaCadaAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setPedirSenhaLibParaCadaAcesso(Boolean value) {
        this.pedirSenhaLibParaCadaAcesso = value;
    }

    /**
     * Gets the value of the portaImagens property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPortaImagens() {
        return portaImagens;
    }

    /**
     * Sets the value of the portaImagens property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPortaImagens(Integer value) {
        this.portaImagens = value;
    }

    /**
     * Gets the value of the portaServidorCamera property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPortaServidorCamera() {
        return portaServidorCamera;
    }

    /**
     * Sets the value of the portaServidorCamera property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPortaServidorCamera(Integer value) {
        this.portaServidorCamera = value;
    }

    /**
     * Gets the value of the portaServidorImp property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPortaServidorImp() {
        return portaServidorImp;
    }

    /**
     * Sets the value of the portaServidorImp property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPortaServidorImp(Integer value) {
        this.portaServidorImp = value;
    }

    /**
     * Gets the value of the servidorImpressoes property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getServidorImpressoes() {
        return servidorImpressoes;
    }

    /**
     * Sets the value of the servidorImpressoes property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setServidorImpressoes(String value) {
        this.servidorImpressoes = value;
    }

    /**
     * Gets the value of the tempoEntreAcessos property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getTempoEntreAcessos() {
        return tempoEntreAcessos;
    }

    /**
     * Sets the value of the tempoEntreAcessos property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setTempoEntreAcessos(Integer value) {
        this.tempoEntreAcessos = value;
    }

    /**
     * Gets the value of the tempoEntreAcessosColaborador property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getTempoEntreAcessosColaborador() {
        return tempoEntreAcessosColaborador;
    }

    /**
     * Sets the value of the tempoEntreAcessosColaborador property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setTempoEntreAcessosColaborador(Integer value) {
        this.tempoEntreAcessosColaborador = value;
    }

    /**
     * Gets the value of the urlServidorCamera property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUrlServidorCamera() {
        return urlServidorCamera;
    }

    /**
     * Sets the value of the urlServidorCamera property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUrlServidorCamera(String value) {
        this.urlServidorCamera = value;
    }

    /**
     * Gets the value of the usarReconhecimento property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isUsarReconhecimento() {
        return usarReconhecimento;
    }

    /**
     * Sets the value of the usarReconhecimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setUsarReconhecimento(Boolean value) {
        this.usarReconhecimento = value;
    }

    /**
     * Gets the value of the utilizarModoOffline property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isUtilizarModoOffline() {
        return utilizarModoOffline;
    }

    /**
     * Sets the value of the utilizarModoOffline property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setUtilizarModoOffline(Boolean value) {
        this.utilizarModoOffline = value;
    }

}

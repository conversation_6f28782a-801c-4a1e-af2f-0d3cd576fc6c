
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for tipoLiberacaoEnum.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="tipoLiberacaoEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="CLIENTE"/>
 *     &lt;enumeration value="COLABORADOR"/>
 *     &lt;enumeration value="TERCEIRIZADO"/>
 *     &lt;enumeration value="CLIENTE_VISITANTE"/>
 *     &lt;enumeration value="VISITANTE_DIVERSO"/>
 *     &lt;enumeration value="NENHUM"/>
 *     &lt;enumeration value="OUTROS"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "tipoLiberacaoEnum")
@XmlEnum
public enum TipoLiberacaoEnum {

    CLIENTE,
    COLABORADOR,
    TERCEIRIZADO,
    CLIENTE_VISITANTE,
    VISITANTE_DIVERSO,
    NENHUM,
    OUTROS;

    public String value() {
        return name();
    }

    public static TipoLiberacaoEnum fromValue(String v) {
        return valueOf(v);
    }

}


package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for gravarSenhaIntegracaoEncriptada complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="gravarSenhaIntegracaoEncriptada">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoAcesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="senhaEncriptada" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "gravarSenhaIntegracaoEncriptada", propOrder = {
    "key",
    "codigoAcesso",
    "senhaEncriptada"
})
public class GravarSenhaIntegracaoEncriptada {

    protected String key;
    protected String codigoAcesso;
    protected String senhaEncriptada;

    /**
     * Gets the value of the key property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Sets the value of the key property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Gets the value of the codigoAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    /**
     * Sets the value of the codigoAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoAcesso(String value) {
        this.codigoAcesso = value;
    }

    /**
     * Gets the value of the senhaEncriptada property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSenhaEncriptada() {
        return senhaEncriptada;
    }

    /**
     * Sets the value of the senhaEncriptada property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSenhaEncriptada(String value) {
        this.senhaEncriptada = value;
    }

}

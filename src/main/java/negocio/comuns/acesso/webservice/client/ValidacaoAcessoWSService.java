
package negocio.comuns.acesso.webservice.client;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.logging.Logger;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.1.6 in JDK 6
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "ValidacaoAcessoWS", targetNamespace = "http://webservice.acesso/", wsdlLocation = "http://app.pactosolucoes.com.br/ValidacaoAcessoWS?wsdl")
public class ValidacaoAcessoWSService
    extends Service
{

    private final static URL VALIDACAOACESSOWSSERVICE_WSDL_LOCATION;
    private final static Logger logger = Logger.getLogger(negocio.comuns.acesso.webservice.client.ValidacaoAcessoWSService.class.getName());

    static {
        URL url = null;
        try {
            URL baseUrl;
            baseUrl = negocio.comuns.acesso.webservice.client.ValidacaoAcessoWSService.class.getResource(".");
            url = new URL(baseUrl, "http://app.pactosolucoes.com.br/ValidacaoAcessoWS?wsdl");
        } catch (MalformedURLException e) {
            logger.warning("Failed to create URL for the wsdl Location: 'http://app.pactosolucoes.com.br/ValidacaoAcessoWS?wsdl', retrying as a local file");
            logger.warning(e.getMessage());
        }
        VALIDACAOACESSOWSSERVICE_WSDL_LOCATION = url;
    }

    public ValidacaoAcessoWSService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ValidacaoAcessoWSService() {
        super(VALIDACAOACESSOWSSERVICE_WSDL_LOCATION, new QName("http://webservice.acesso/", "ValidacaoAcessoWSService"));
    }

    /**
     * 
     * @return
     *     returns ValidacaoAcessoWS
     */
    @WebEndpoint(name = "ValidacaoAcessoWSPort")
    public ValidacaoAcessoWS getValidacaoAcessoWSPort() {
        return super.getPort(new QName("http://webservice.acesso/", "ValidacaoAcessoWSPort"), ValidacaoAcessoWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ValidacaoAcessoWS
     */
    @WebEndpoint(name = "ValidacaoAcessoWSPort")
    public ValidacaoAcessoWS getValidacaoAcessoWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://webservice.acesso/", "ValidacaoAcessoWSPort"), ValidacaoAcessoWS.class, features);
    }

}

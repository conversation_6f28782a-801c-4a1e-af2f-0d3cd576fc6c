
package negocio.comuns.acesso.webservice.client;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "ValidacaoAcessoWS", targetNamespace = "http://webservice.acesso/", wsdlLocation = "http://localhost:8080/ZillyonWeb/ValidacaoAcessoWS?wsdl")
public class ValidacaoAcessoWS_Service
    extends Service
{

    private final static URL VALIDACAOACESSOWS_WSDL_LOCATION;
    private final static WebServiceException VALIDACAOACESSOWS_EXCEPTION;
    private final static QName VALIDACAOACESSOWS_QNAME = new QName("http://webservice.acesso/", "ValidacaoAcessoWS");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://localhost:8080/ZillyonWeb/ValidacaoAcessoWS?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        VALIDACAOACESSOWS_WSDL_LOCATION = url;
        VALIDACAOACESSOWS_EXCEPTION = e;
    }

    public ValidacaoAcessoWS_Service() {
        super(__getWsdlLocation(), VALIDACAOACESSOWS_QNAME);
    }

    public ValidacaoAcessoWS_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns ValidacaoAcessoWS
     */
    @WebEndpoint(name = "ValidacaoAcessoWSPort")
    public ValidacaoAcessoWS getValidacaoAcessoWSPort() {
        return super.getPort(new QName("http://webservice.acesso/", "ValidacaoAcessoWSPort"), ValidacaoAcessoWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ValidacaoAcessoWS
     */
    @WebEndpoint(name = "ValidacaoAcessoWSPort")
    public ValidacaoAcessoWS getValidacaoAcessoWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://webservice.acesso/", "ValidacaoAcessoWSPort"), ValidacaoAcessoWS.class, features);
    }

    private static URL __getWsdlLocation() {
        if (VALIDACAOACESSOWS_EXCEPTION!= null) {
            throw VALIDACAOACESSOWS_EXCEPTION;
        }
        return VALIDACAOACESSOWS_WSDL_LOCATION;
    }

}


package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for dadosAcessoOfflineTO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="dadosAcessoOfflineTO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="acessoEsperado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="bloqueadoLiberado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="categoriaCliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoCliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoPessoa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoacesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoacessoalternativo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigopk" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigousuario" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="dia" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="fimvalidacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="horariosplanos" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="horariosturmas" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="iniciovalidacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="matricula" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="matriculaCliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="msgAniversario" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="msgCliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="msgColetor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="msgValidacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nomeCliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numeroterminal" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="pessoapk" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="senha" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="senhaacesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="situacaoAcesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tipoCartao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tipopessoa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="validarSaldoCreditoTreino" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="vencimento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="vencimentocontrato" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "dadosAcessoOfflineTO", propOrder = {
    "acessoEsperado",
    "bloqueadoLiberado",
    "categoriaCliente",
    "codigoCliente",
    "codigoPessoa",
    "codigoacesso",
    "codigoacessoalternativo",
    "codigopk",
    "codigousuario",
    "dia",
    "fimvalidacao",
    "horariosplanos",
    "horariosturmas",
    "iniciovalidacao",
    "matricula",
    "matriculaCliente",
    "msgAniversario",
    "msgCliente",
    "msgColetor",
    "msgValidacao",
    "nomeCliente",
    "numeroterminal",
    "pessoapk",
    "senha",
    "senhaacesso",
    "situacaoAcesso",
    "tipoCartao",
    "tipopessoa",
    "validarSaldoCreditoTreino",
    "vencimento",
    "vencimentocontrato"
})
public class DadosAcessoOfflineTO {

    protected String acessoEsperado;
    protected String bloqueadoLiberado;
    protected String categoriaCliente;
    protected String codigoCliente;
    protected String codigoPessoa;
    protected String codigoacesso;
    protected String codigoacessoalternativo;
    protected Integer codigopk;
    protected Integer codigousuario;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dia;
    protected String fimvalidacao;
    protected String horariosplanos;
    protected String horariosturmas;
    protected String iniciovalidacao;
    protected Integer matricula;
    protected String matriculaCliente;
    protected String msgAniversario;
    protected String msgCliente;
    protected String msgColetor;
    protected String msgValidacao;
    protected String nomeCliente;
    protected Integer numeroterminal;
    protected Integer pessoapk;
    protected String senha;
    protected String senhaacesso;
    protected String situacaoAcesso;
    protected String tipoCartao;
    protected String tipopessoa;
    protected String validarSaldoCreditoTreino;
    protected String vencimento;
    protected String vencimentocontrato;

    /**
     * Gets the value of the acessoEsperado property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcessoEsperado() {
        return acessoEsperado;
    }

    /**
     * Sets the value of the acessoEsperado property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcessoEsperado(String value) {
        this.acessoEsperado = value;
    }

    /**
     * Gets the value of the bloqueadoLiberado property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBloqueadoLiberado() {
        return bloqueadoLiberado;
    }

    /**
     * Sets the value of the bloqueadoLiberado property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBloqueadoLiberado(String value) {
        this.bloqueadoLiberado = value;
    }

    /**
     * Gets the value of the categoriaCliente property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCategoriaCliente() {
        return categoriaCliente;
    }

    /**
     * Sets the value of the categoriaCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCategoriaCliente(String value) {
        this.categoriaCliente = value;
    }

    /**
     * Gets the value of the codigoCliente property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoCliente() {
        return codigoCliente;
    }

    /**
     * Sets the value of the codigoCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoCliente(String value) {
        this.codigoCliente = value;
    }

    /**
     * Gets the value of the codigoPessoa property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoPessoa() {
        return codigoPessoa;
    }

    /**
     * Sets the value of the codigoPessoa property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoPessoa(String value) {
        this.codigoPessoa = value;
    }

    /**
     * Gets the value of the codigoacesso property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoacesso() {
        return codigoacesso;
    }

    /**
     * Sets the value of the codigoacesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoacesso(String value) {
        this.codigoacesso = value;
    }

    /**
     * Gets the value of the codigoacessoalternativo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoacessoalternativo() {
        return codigoacessoalternativo;
    }

    /**
     * Sets the value of the codigoacessoalternativo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoacessoalternativo(String value) {
        this.codigoacessoalternativo = value;
    }

    /**
     * Gets the value of the codigopk property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigopk() {
        return codigopk;
    }

    /**
     * Sets the value of the codigopk property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigopk(Integer value) {
        this.codigopk = value;
    }

    /**
     * Gets the value of the codigousuario property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigousuario() {
        return codigousuario;
    }

    /**
     * Sets the value of the codigousuario property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigousuario(Integer value) {
        this.codigousuario = value;
    }

    /**
     * Gets the value of the dia property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDia() {
        return dia;
    }

    /**
     * Sets the value of the dia property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDia(XMLGregorianCalendar value) {
        this.dia = value;
    }

    /**
     * Gets the value of the fimvalidacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFimvalidacao() {
        return fimvalidacao;
    }

    /**
     * Sets the value of the fimvalidacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFimvalidacao(String value) {
        this.fimvalidacao = value;
    }

    /**
     * Gets the value of the horariosplanos property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHorariosplanos() {
        return horariosplanos;
    }

    /**
     * Sets the value of the horariosplanos property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHorariosplanos(String value) {
        this.horariosplanos = value;
    }

    /**
     * Gets the value of the horariosturmas property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHorariosturmas() {
        return horariosturmas;
    }

    /**
     * Sets the value of the horariosturmas property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHorariosturmas(String value) {
        this.horariosturmas = value;
    }

    /**
     * Gets the value of the iniciovalidacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIniciovalidacao() {
        return iniciovalidacao;
    }

    /**
     * Sets the value of the iniciovalidacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIniciovalidacao(String value) {
        this.iniciovalidacao = value;
    }

    /**
     * Gets the value of the matricula property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getMatricula() {
        return matricula;
    }

    /**
     * Sets the value of the matricula property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setMatricula(Integer value) {
        this.matricula = value;
    }

    /**
     * Gets the value of the matriculaCliente property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    /**
     * Sets the value of the matriculaCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMatriculaCliente(String value) {
        this.matriculaCliente = value;
    }

    /**
     * Gets the value of the msgAniversario property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsgAniversario() {
        return msgAniversario;
    }

    /**
     * Sets the value of the msgAniversario property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsgAniversario(String value) {
        this.msgAniversario = value;
    }

    /**
     * Gets the value of the msgCliente property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsgCliente() {
        return msgCliente;
    }

    /**
     * Sets the value of the msgCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsgCliente(String value) {
        this.msgCliente = value;
    }

    /**
     * Gets the value of the msgColetor property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsgColetor() {
        return msgColetor;
    }

    /**
     * Sets the value of the msgColetor property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsgColetor(String value) {
        this.msgColetor = value;
    }

    /**
     * Gets the value of the msgValidacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsgValidacao() {
        return msgValidacao;
    }

    /**
     * Sets the value of the msgValidacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsgValidacao(String value) {
        this.msgValidacao = value;
    }

    /**
     * Gets the value of the nomeCliente property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeCliente() {
        return nomeCliente;
    }

    /**
     * Sets the value of the nomeCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeCliente(String value) {
        this.nomeCliente = value;
    }

    /**
     * Gets the value of the numeroterminal property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getNumeroterminal() {
        return numeroterminal;
    }

    /**
     * Sets the value of the numeroterminal property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setNumeroterminal(Integer value) {
        this.numeroterminal = value;
    }

    /**
     * Gets the value of the pessoapk property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPessoapk() {
        return pessoapk;
    }

    /**
     * Sets the value of the pessoapk property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPessoapk(Integer value) {
        this.pessoapk = value;
    }

    /**
     * Gets the value of the senha property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSenha() {
        return senha;
    }

    /**
     * Sets the value of the senha property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSenha(String value) {
        this.senha = value;
    }

    /**
     * Gets the value of the senhaacesso property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSenhaacesso() {
        return senhaacesso;
    }

    /**
     * Sets the value of the senhaacesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSenhaacesso(String value) {
        this.senhaacesso = value;
    }

    /**
     * Gets the value of the situacaoAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSituacaoAcesso() {
        return situacaoAcesso;
    }

    /**
     * Sets the value of the situacaoAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSituacaoAcesso(String value) {
        this.situacaoAcesso = value;
    }

    /**
     * Gets the value of the tipoCartao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoCartao() {
        return tipoCartao;
    }

    /**
     * Sets the value of the tipoCartao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoCartao(String value) {
        this.tipoCartao = value;
    }

    /**
     * Gets the value of the tipopessoa property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipopessoa() {
        return tipopessoa;
    }

    /**
     * Sets the value of the tipopessoa property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipopessoa(String value) {
        this.tipopessoa = value;
    }

    /**
     * Gets the value of the validarSaldoCreditoTreino property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValidarSaldoCreditoTreino() {
        return validarSaldoCreditoTreino;
    }

    /**
     * Sets the value of the validarSaldoCreditoTreino property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setValidarSaldoCreditoTreino(String value) {
        this.validarSaldoCreditoTreino = value;
    }

    /**
     * Gets the value of the vencimento property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVencimento() {
        return vencimento;
    }

    /**
     * Sets the value of the vencimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVencimento(String value) {
        this.vencimento = value;
    }

    /**
     * Gets the value of the vencimentocontrato property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVencimentocontrato() {
        return vencimentocontrato;
    }

    /**
     * Sets the value of the vencimentocontrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVencimentocontrato(String value) {
        this.vencimentocontrato = value;
    }

}


package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for registrarAcessoAvaliandoIntegracaoResponse complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="registrarAcessoAvaliandoIntegracaoResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="return" type="{http://webservice.acesso/}retornoRequisicaoRegistrarAcesso" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "registrarAcessoAvaliandoIntegracaoResponse", propOrder = {
    "_return"
})
public class RegistrarAcessoAvaliandoIntegracaoResponse {

    @XmlElement(name = "return")
    protected RetornoRequisicaoRegistrarAcesso _return;

    /**
     * Gets the value of the return property.
     * 
     * @return
     *     possible object is
     *     {@link RetornoRequisicaoRegistrarAcesso }
     *     
     */
    public RetornoRequisicaoRegistrarAcesso getReturn() {
        return _return;
    }

    /**
     * Sets the value of the return property.
     * 
     * @param value
     *     allowed object is
     *     {@link RetornoRequisicaoRegistrarAcesso }
     *     
     */
    public void setReturn(RetornoRequisicaoRegistrarAcesso value) {
        this._return = value;
    }

}

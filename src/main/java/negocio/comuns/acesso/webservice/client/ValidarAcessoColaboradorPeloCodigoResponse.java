
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for validarAcessoColaboradorPeloCodigoResponse complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="validarAcessoColaboradorPeloCodigoResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="return" type="{http://webservice.acesso/}retornoRequisicaoValidacaoAcesso" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "validarAcessoColaboradorPeloCodigoResponse", propOrder = {
    "_return"
})
public class ValidarAcessoColaboradorPeloCodigoResponse {

    @XmlElement(name = "return")
    protected RetornoRequisicaoValidacaoAcesso _return;

    /**
     * Gets the value of the return property.
     * 
     * @return
     *     possible object is
     *     {@link RetornoRequisicaoValidacaoAcesso }
     *     
     */
    public RetornoRequisicaoValidacaoAcesso getReturn() {
        return _return;
    }

    /**
     * Sets the value of the return property.
     * 
     * @param value
     *     allowed object is
     *     {@link RetornoRequisicaoValidacaoAcesso }
     *     
     */
    public void setReturn(RetornoRequisicaoValidacaoAcesso value) {
        this._return = value;
    }

}

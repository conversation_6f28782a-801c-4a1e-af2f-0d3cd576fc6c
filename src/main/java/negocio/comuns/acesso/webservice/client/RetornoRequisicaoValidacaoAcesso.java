
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for retornoRequisicaoValidacaoAcesso complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="retornoRequisicaoValidacaoAcesso">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.acesso/}retornoRequisicaoWS">
 *       &lt;sequence>
 *         &lt;element name="acessoEsperado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="bloqueadoLiberado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="categoriaCliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoAcesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoCliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoPessoa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="fotoCliente" type="{http://www.w3.org/2001/XMLSchema}base64Binary" minOccurs="0"/>
 *         &lt;element name="matriculaCliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="msgAniversario" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="msgCliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="msgColetor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="msgValidacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nomeCliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="situacaoAcesso" type="{http://webservice.acesso/}situacaoAcessoEnum" minOccurs="0"/>
 *         &lt;element name="tipoCartao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="vencimento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "retornoRequisicaoValidacaoAcesso", propOrder = {
    "acessoEsperado",
    "bloqueadoLiberado",
    "categoriaCliente",
    "codigoAcesso",
    "codigoCliente",
    "codigoPessoa",
    "fotoCliente",
    "matriculaCliente",
    "msgAniversario",
    "msgCliente",
    "msgColetor",
    "msgValidacao",
    "nomeCliente",
    "situacaoAcesso",
    "tipoCartao",
    "vencimento",
    "ticket",
    "cpfCliente",
    "chavesEmpresasPermiteAcesso"
})
public class RetornoRequisicaoValidacaoAcesso
    extends RetornoRequisicaoWS
{

    protected String acessoEsperado;
    protected String bloqueadoLiberado;
    protected String categoriaCliente;
    protected String codigoAcesso;
    protected String codigoCliente;
    protected String codigoPessoa;
    protected byte[] fotoCliente;
    protected String matriculaCliente;
    protected String msgAniversario;
    protected String msgCliente;
    protected String msgColetor;
    protected String msgValidacao;
    protected String nomeCliente;
    protected SituacaoAcessoEnum situacaoAcesso;
    protected String tipoCartao;
    protected String vencimento;
    protected String ticket;
    protected String cpfCliente;
    List<String> chavesEmpresasPermiteAcesso = new ArrayList<>();

    /**
     * Gets the value of the acessoEsperado property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcessoEsperado() {
        return acessoEsperado;
    }

    /**
     * Sets the value of the acessoEsperado property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcessoEsperado(String value) {
        this.acessoEsperado = value;
    }

    /**
     * Gets the value of the bloqueadoLiberado property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBloqueadoLiberado() {
        return bloqueadoLiberado;
    }

    /**
     * Sets the value of the bloqueadoLiberado property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBloqueadoLiberado(String value) {
        this.bloqueadoLiberado = value;
    }

    /**
     * Gets the value of the categoriaCliente property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCategoriaCliente() {
        return categoriaCliente;
    }

    /**
     * Sets the value of the categoriaCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCategoriaCliente(String value) {
        this.categoriaCliente = value;
    }

    /**
     * Gets the value of the codigoAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    /**
     * Sets the value of the codigoAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoAcesso(String value) {
        this.codigoAcesso = value;
    }

    /**
     * Gets the value of the codigoCliente property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoCliente() {
        return codigoCliente;
    }

    /**
     * Sets the value of the codigoCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoCliente(String value) {
        this.codigoCliente = value;
    }

    /**
     * Gets the value of the codigoPessoa property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoPessoa() {
        return codigoPessoa;
    }

    /**
     * Sets the value of the codigoPessoa property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoPessoa(String value) {
        this.codigoPessoa = value;
    }

    /**
     * Gets the value of the fotoCliente property.
     * 
     * @return
     *     possible object is
     *     byte[]
     */
    public byte[] getFotoCliente() {
        return fotoCliente;
    }

    /**
     * Sets the value of the fotoCliente property.
     * 
     * @param value
     *     allowed object is
     *     byte[]
     */
    public void setFotoCliente(byte[] value) {
        this.fotoCliente = value;
    }

    /**
     * Gets the value of the matriculaCliente property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    /**
     * Sets the value of the matriculaCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMatriculaCliente(String value) {
        this.matriculaCliente = value;
    }

    /**
     * Gets the value of the msgAniversario property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsgAniversario() {
        return msgAniversario;
    }

    /**
     * Sets the value of the msgAniversario property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsgAniversario(String value) {
        this.msgAniversario = value;
    }

    /**
     * Gets the value of the msgCliente property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsgCliente() {
        return msgCliente;
    }

    /**
     * Sets the value of the msgCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsgCliente(String value) {
        this.msgCliente = value;
    }

    /**
     * Gets the value of the msgColetor property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsgColetor() {
        return msgColetor;
    }

    /**
     * Sets the value of the msgColetor property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsgColetor(String value) {
        this.msgColetor = value;
    }

    /**
     * Gets the value of the msgValidacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsgValidacao() {
        return msgValidacao;
    }

    /**
     * Sets the value of the msgValidacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsgValidacao(String value) {
        this.msgValidacao = value;
    }

    /**
     * Gets the value of the nomeCliente property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeCliente() {
        return nomeCliente;
    }

    /**
     * Sets the value of the nomeCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeCliente(String value) {
        this.nomeCliente = value;
    }

    /**
     * Gets the value of the situacaoAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link SituacaoAcessoEnum }
     *     
     */
    public SituacaoAcessoEnum getSituacaoAcesso() {
        return situacaoAcesso;
    }

    /**
     * Sets the value of the situacaoAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link SituacaoAcessoEnum }
     *     
     */
    public void setSituacaoAcesso(SituacaoAcessoEnum value) {
        this.situacaoAcesso = value;
    }

    /**
     * Gets the value of the tipoCartao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoCartao() {
        return tipoCartao;
    }

    /**
     * Sets the value of the tipoCartao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoCartao(String value) {
        this.tipoCartao = value;
    }

    /**
     * Gets the value of the vencimento property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVencimento() {
        return vencimento;
    }

    /**
     * Sets the value of the vencimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVencimento(String value) {
        this.vencimento = value;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public String getCpfCliente() {
        return cpfCliente;
    }

    public void setCpfCliente(String cpfCliente) {
        this.cpfCliente = cpfCliente;
    }

    public List<String> getChavesEmpresasPermiteAcesso() {
        return chavesEmpresasPermiteAcesso;
    }

    public void setChavesEmpresasPermiteAcesso(List<String> chavesEmpresasPermiteAcesso) {
        this.chavesEmpresasPermiteAcesso = chavesEmpresasPermiteAcesso;
    }
}

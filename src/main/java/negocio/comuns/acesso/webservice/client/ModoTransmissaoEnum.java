
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for modoTransmissaoEnum.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="modoTransmissaoEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="MODOTRANSMISSAO_COLETOR_HALFDUPLEX"/>
 *     &lt;enumeration value="MODOTRANSMISSAO_COLETOR_FULLDUPLEX"/>
 *     &lt;enumeration value="MODOTRANSMISSAO_COLETOR_AUTO"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "modoTransmissaoEnum")
@XmlEnum
public enum ModoTransmissaoEnum {

    MODOTRANSMISSAO_COLETOR_HALFDUPLEX,
    MODOTRANSMISSAO_COLETOR_FULLDUPLEX,
    MODOTRANSMISSAO_COLETOR_AUTO;

    public String value() {
        return name();
    }

    public static ModoTransmissaoEnum fromValue(String v) {
        return valueOf(v);
    }

}

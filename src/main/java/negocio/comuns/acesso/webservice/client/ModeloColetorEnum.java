
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de modeloColetorEnum.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * <p>
 * <pre>
 * &lt;simpleType name="modeloColetorEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="MODELO_COLETOR_TRIXSTANDARD"/>
 *     &lt;enumeration value="MODELO_COLETOR_SERIALPACTO"/>
 *     &lt;enumeration value="MODELO_COLETOR_DESCONHECIDO"/>
 *     &lt;enumeration value="MODELO_COLETOR_INNERTOPDATA"/>
 *     &lt;enumeration value="MODELO_COLETOR_PARALELAHENRY"/>
 *     &lt;enumeration value="MODELO_COLETOR_TCPINNERTOPDATA"/>
 *     &lt;enumeration value="MODELO_COLETOR_NEOKOROSNKFP2"/>
 *     &lt;enumeration value="MODELO_COLETOR_NEOKOROSFP730"/>
 *     &lt;enumeration value="MODELO_COLETOR_PARALELATECNIBRA"/>
 *     &lt;enumeration value="MODELO_COLETOR_SERIALACTUAR"/>
 *     &lt;enumeration value="MODELO_COLETOR_TCPTECNIBRA"/>
 *     &lt;enumeration value="MODELO_COLETOR_SERIALTECNIBRA"/>
 *     &lt;enumeration value="MODELO_COLETOR_NEOKOROSNKFP3"/>
 *     &lt;enumeration value="MODELO_COLETOR_HENRYTCPIP"/>
 *     &lt;enumeration value="MODELO_COLETOR_ALMITEC"/>
 *     &lt;enumeration value="MODELO_COLETOR_ALMITECMAC400"/>
 *     &lt;enumeration value="MODELO_LEITOR_NITGEN"/>
 *     &lt;enumeration value="MODELO_COLETOR_ALMITECTCPIPSECDS"/>
 *     &lt;enumeration value="MODELO_COLETOR_HENRY8x"/>
 *     &lt;enumeration value="MODELO_COLETOR_HENRY8xLV"/>
 *     &lt;enumeration value="MODELO_COLETOR_TRIXXPBLOCK"/>
 *     &lt;enumeration value="MODELO_COLETOR_HENRY8xPRIMME"/>
 *     &lt;enumeration value="MODELO_COLETOR_MA100"/>
 *     &lt;enumeration value="MODELO_COLETOR_INNER_EVENTOS"/>
 *     &lt;enumeration value="MODELO_COLETOR_HENRY7xV2"/>
 *     &lt;enumeration value="MODELO_COLETOR_ACTUARTCPIP"/>
 *     &lt;enumeration value="MODELO_COLETOR_HENRY8XFS"/>
 *     &lt;enumeration value="MODELO_COLETOR_BIOMTECH"/>
 *     &lt;enumeration value="MODELO_COLETOR_INOVACESSO"/>
 *     &lt;enumeration value="MODELO_COLETOR_SERIALNATSO"/>
 *     &lt;enumeration value="MODELO_COLETOR_INTEGRA_FACIL"/>
 *     &lt;enumeration value="MODELO_COLETOR_TUPA"/>
 *     &lt;enumeration value="MODELO_COLETOR_SYSTEMTECV4"/>
 *     &lt;enumeration value="MODELO_COLETOR_USBSERIAL"/>
 *     &lt;enumeration value="MODELO_CAMERA_ACIONAMENTO"/>
 *     &lt;enumeration value="MODELO_COLETOR_INTERLAKEN"/>
 *     &lt;enumeration value="MODELO_COLETOR_ZK_TF1700"/>
 *     &lt;enumeration value="MODELO_COLETOR_IDBLOCK"/>
 *     &lt;enumeration value="MODELO_COLETOR_HENRYSERIAL"/>
 *     &lt;enumeration value="MODELO_COLETOR_INNER_EVENTOS_LC"/>
 *     &lt;enumeration value="MODELO_COLETOR_TECNEW_SERIAL"/>
 *     &lt;enumeration value="MODELO_COLETOR_ZK_TECO"/>
 *     &lt;enumeration value="MODELO_COLETOR_TCA_SERIAL"/>
 *     &lt;enumeration value="MODELO_COLETOR_ZUCHIMZ4"/>
 *     &lt;enumeration value="MODELO_COLETOR_DIMEP_MICROPOINT"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "modeloColetorEnum")
@XmlEnum
public enum ModeloColetorEnum {

    MODELO_COLETOR_TRIXSTANDARD("MODELO_COLETOR_TRIXSTANDARD"),
    MODELO_COLETOR_SERIALPACTO("MODELO_COLETOR_SERIALPACTO"),
    MODELO_COLETOR_DESCONHECIDO("MODELO_COLETOR_DESCONHECIDO"),
    MODELO_COLETOR_INNERTOPDATA("MODELO_COLETOR_INNERTOPDATA"),
    MODELO_COLETOR_PARALELAHENRY("MODELO_COLETOR_PARALELAHENRY"),
    MODELO_COLETOR_TCPINNERTOPDATA("MODELO_COLETOR_TCPINNERTOPDATA"),
    @XmlEnumValue("MODELO_COLETOR_NEOKOROSNKFP2")
    MODELO_COLETOR_NEOKOROSNKFP_2("MODELO_COLETOR_NEOKOROSNKFP2"),
    @XmlEnumValue("MODELO_COLETOR_NEOKOROSFP730")
    MODELO_COLETOR_NEOKOROSFP_730("MODELO_COLETOR_NEOKOROSFP730"),
    MODELO_COLETOR_PARALELATECNIBRA("MODELO_COLETOR_PARALELATECNIBRA"),
    MODELO_COLETOR_SERIALACTUAR("MODELO_COLETOR_SERIALACTUAR"),
    MODELO_COLETOR_TCPTECNIBRA("MODELO_COLETOR_TCPTECNIBRA"),
    MODELO_COLETOR_SERIALTECNIBRA("MODELO_COLETOR_SERIALTECNIBRA"),
    @XmlEnumValue("MODELO_COLETOR_NEOKOROSNKFP3")
    MODELO_COLETOR_NEOKOROSNKFP_3("MODELO_COLETOR_NEOKOROSNKFP3"),
    MODELO_COLETOR_HENRYTCPIP("MODELO_COLETOR_HENRYTCPIP"),
    MODELO_COLETOR_ALMITEC("MODELO_COLETOR_ALMITEC"),
    @XmlEnumValue("MODELO_COLETOR_ALMITECMAC400")
    MODELO_COLETOR_ALMITECMAC_400("MODELO_COLETOR_ALMITECMAC400"),
    MODELO_LEITOR_NITGEN("MODELO_LEITOR_NITGEN"),
    MODELO_COLETOR_ALMITECTCPIPSECDS("MODELO_COLETOR_ALMITECTCPIPSECDS"),
    @XmlEnumValue("MODELO_COLETOR_HENRY8x")
    MODELO_COLETOR_HENRY_8_X("MODELO_COLETOR_HENRY8x"),
    @XmlEnumValue("MODELO_COLETOR_HENRY8xLV")
    MODELO_COLETOR_HENRY_8_X_LV("MODELO_COLETOR_HENRY8xLV"),
    MODELO_COLETOR_TRIXXPBLOCK("MODELO_COLETOR_TRIXXPBLOCK"),
    @XmlEnumValue("MODELO_COLETOR_HENRY8xPRIMME")
    MODELO_COLETOR_HENRY_8_X_PRIMME("MODELO_COLETOR_HENRY8xPRIMME"),
    @XmlEnumValue("MODELO_COLETOR_MA100")
    MODELO_COLETOR_MA_100("MODELO_COLETOR_MA100"),
    MODELO_COLETOR_INNER_EVENTOS("MODELO_COLETOR_INNER_EVENTOS"),
    @XmlEnumValue("MODELO_COLETOR_HENRY7xV2")
    MODELO_COLETOR_HENRY_7_X_V_2("MODELO_COLETOR_HENRY7xV2"),
    MODELO_COLETOR_ACTUARTCPIP("MODELO_COLETOR_ACTUARTCPIP"),
    @XmlEnumValue("MODELO_COLETOR_HENRY8XFS")
    MODELO_COLETOR_HENRY_8_XFS("MODELO_COLETOR_HENRY8XFS"),
    MODELO_COLETOR_BIOMTECH("MODELO_COLETOR_BIOMTECH"),
    MODELO_COLETOR_INOVACESSO("MODELO_COLETOR_INOVACESSO"),
    MODELO_COLETOR_SERIALNATSO("MODELO_COLETOR_SERIALNATSO"),
    MODELO_COLETOR_INTEGRA_FACIL("MODELO_COLETOR_INTEGRA_FACIL"),
    MODELO_COLETOR_TUPA("MODELO_COLETOR_TUPA"),
    @XmlEnumValue("MODELO_COLETOR_SYSTEMTECV4")
    MODELO_COLETOR_SYSTEMTECV_4("MODELO_COLETOR_SYSTEMTECV4"),
    MODELO_COLETOR_USBSERIAL("MODELO_COLETOR_USBSERIAL"),
    MODELO_CAMERA_ACIONAMENTO("MODELO_CAMERA_ACIONAMENTO"),
    MODELO_COLETOR_INTERLAKEN("MODELO_COLETOR_INTERLAKEN"),
    @XmlEnumValue("MODELO_COLETOR_ZK_TF1700")
    MODELO_COLETOR_ZK_TF_1700("MODELO_COLETOR_ZK_TF1700"),
    MODELO_COLETOR_IDBLOCK("MODELO_COLETOR_IDBLOCK"),
    MODELO_COLETOR_HENRYSERIAL("MODELO_COLETOR_HENRYSERIAL"),
    MODELO_COLETOR_INNER_EVENTOS_LC("MODELO_COLETOR_INNER_EVENTOS_LC"),
    MODELO_COLETOR_TECNEW_SERIAL("MODELO_COLETOR_TECNEW_SERIAL"),
    MODELO_COLETOR_ZK_TECO("MODELO_COLETOR_ZK_TECO"),
    MODELO_COLETOR_TCA_SERIAL("MODELO_COLETOR_TCA_SERIAL"),
    @XmlEnumValue("MODELO_COLETOR_ZUCHIMZ4")
    MODELO_COLETOR_ZUCHIMZ_4("MODELO_COLETOR_ZUCHIMZ4"),
    MODELO_COLETOR_DIMEP_MICROPOINT("MODELO_COLETOR_DIMEP_MICROPOINT");
    private final String value;

    ModeloColetorEnum(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static ModeloColetorEnum fromValue(String v) {
        for (ModeloColetorEnum c: ModeloColetorEnum.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}

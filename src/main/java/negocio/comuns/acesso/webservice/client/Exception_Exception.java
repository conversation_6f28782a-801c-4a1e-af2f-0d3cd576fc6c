
package negocio.comuns.acesso.webservice.client;

import javax.xml.ws.WebFault;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebFault(name = "Exception", targetNamespace = "http://webservice.acesso/")
public class Exception_Exception
    extends java.lang.Exception
{

    /**
     * Java type that goes as soapenv:Fault detail element.
     * 
     */
    private negocio.comuns.acesso.webservice.client.Exception faultInfo;

    /**
     * 
     * @param message
     * @param faultInfo
     */
    public Exception_Exception(String message, negocio.comuns.acesso.webservice.client.Exception faultInfo) {
        super(message);
        this.faultInfo = faultInfo;
    }

    /**
     * 
     * @param message
     * @param faultInfo
     * @param cause
     */
    public Exception_Exception(String message, negocio.comuns.acesso.webservice.client.Exception faultInfo, Throwable cause) {
        super(message, cause);
        this.faultInfo = faultInfo;
    }

    /**
     * 
     * @return
     *     returns fault bean: negocio.comuns.acesso.webservice.client.Exception
     */
    public negocio.comuns.acesso.webservice.client.Exception getFaultInfo() {
        return faultInfo;
    }

}

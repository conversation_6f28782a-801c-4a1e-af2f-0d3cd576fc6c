
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for sentidoAcessoEnum.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="sentidoAcessoEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="SENTIDOACESSO_COLETOR_ENTRADA"/>
 *     &lt;enumeration value="SENTIDOACESSO_COLETOR_SAIDA"/>
 *     &lt;enumeration value="SENTIDOACESSO_COLETOR_INDIFERENTE"/>
 *     &lt;enumeration value="SENTIDOACESSO_COLETOR_OESPERADO"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "sentidoAcessoEnum")
@XmlEnum
public enum SentidoAcessoEnum {

    SENTIDOACESSO_COLETOR_ENTRADA,
    SENTIDOACESSO_COLETOR_SAIDA,
    SENTIDOACESSO_COLETOR_INDIFERENTE,
    SENTIDOACESSO_COLETOR_OESPERADO;

    public String value() {
        return name();
    }

    public static SentidoAcessoEnum fromValue(String v) {
        return valueOf(v);
    }

}

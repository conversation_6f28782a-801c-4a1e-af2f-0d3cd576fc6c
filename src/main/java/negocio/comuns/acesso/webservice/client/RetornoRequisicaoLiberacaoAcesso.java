
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for retornoRequisicaoLiberacaoAcesso complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="retornoRequisicaoLiberacaoAcesso">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.acesso/}retornoRequisicaoWS">
 *       &lt;sequence>
 *         &lt;element name="acessoLiberado" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="mensagem" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "retornoRequisicaoLiberacaoAcesso", propOrder = {
    "acessoLiberado",
    "mensagem"
})
public class RetornoRequisicaoLiberacaoAcesso
    extends RetornoRequisicaoWS
{

    protected Boolean acessoLiberado;
    protected String mensagem;

    /**
     * Gets the value of the acessoLiberado property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isAcessoLiberado() {
        return acessoLiberado;
    }

    /**
     * Sets the value of the acessoLiberado property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setAcessoLiberado(Boolean value) {
        this.acessoLiberado = value;
    }

    /**
     * Gets the value of the mensagem property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMensagem() {
        return mensagem;
    }

    /**
     * Sets the value of the mensagem property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMensagem(String value) {
        this.mensagem = value;
    }

}


package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for buscarPorCodigoAcessoResponse complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="buscarPorCodigoAcessoResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="return" type="{http://webservice.acesso/}retornoRequisicaoBuscarCodigoAcesso" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "buscarPorCodigoAcessoResponse", propOrder = {
    "_return"
})
public class BuscarPorCodigoAcessoResponse {

    @XmlElement(name = "return")
    protected RetornoRequisicaoBuscarCodigoAcesso _return;

    /**
     * Gets the value of the return property.
     * 
     * @return
     *     possible object is
     *     {@link RetornoRequisicaoBuscarCodigoAcesso }
     *     
     */
    public RetornoRequisicaoBuscarCodigoAcesso getReturn() {
        return _return;
    }

    /**
     * Sets the value of the return property.
     * 
     * @param value
     *     allowed object is
     *     {@link RetornoRequisicaoBuscarCodigoAcesso }
     *     
     */
    public void setReturn(RetornoRequisicaoBuscarCodigoAcesso value) {
        this._return = value;
    }

}

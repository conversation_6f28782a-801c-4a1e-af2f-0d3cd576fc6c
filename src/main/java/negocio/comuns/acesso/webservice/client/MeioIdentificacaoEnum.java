
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for meioIdentificacaoEnum.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="meioIdentificacaoEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="DIGITAL"/>
 *     &lt;enumeration value="MATRICULATECLADOCOMPUTADOR"/>
 *     &lt;enumeration value="MATRICULATECLADOCATRACA"/>
 *     &lt;enumeration value="CODIGOACESSOLEITORSERIAL"/>
 *     &lt;enumeration value="SENHACATRACA"/>
 *     &lt;enumeration value="LISTACHAMADA"/>
 *     &lt;enumeration value="LIBERACAOACESSORAPIDO"/>
 *     &lt;enumeration value="COLETORDEDADOS"/>
 *     &lt;enumeration value="DIGITALCATRACA"/>
 *     &lt;enumeration value="CODIGOBARRACOMPUTADOR"/>
 *     &lt;enumeration value="AVULSO"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "meioIdentificacaoEnum")
@XmlEnum
public enum MeioIdentificacaoEnum {

    DIGITAL,
    MATRICULATECLADOCOMPUTADOR,
    MATRICULATECLADOCATRACA,
    CODIGOACESSOLEITORSERIAL,
    SENHACATRACA,
    LISTACHAMADA,
    LIBERACAOACESSORAPIDO,
    COLETORDEDADOS,
    DIGITALCATRACA,
    CODIGOBARRACOMPUTADOR,
    AVULSO,
    RECONHECIMENTOFACIAL,
    APLICATIVO,
    ACESSOFACIL;

    public String value() {
        return name();
    }

    public static MeioIdentificacaoEnum fromValue(String v) {
        return valueOf(v);
    }

}

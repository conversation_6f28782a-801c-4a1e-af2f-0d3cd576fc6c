
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for coletorWS complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="coletorWS">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="aguardaGiro" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="biometriaNaCatraca" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="biometrico" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="cartaoMaster" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="descricao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="descricaoModeloColetor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="descricaoModoTransmissao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="descricaoSentidoAcesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="digitosLeituraCartao" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="dispAlternativo" type="{http://webservice.acesso/}dispositivoAlternativoEnum" minOccurs="0"/>
 *         &lt;element name="indiceCamera" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="inverterSinal" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="ip" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="leitorGertec" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="localAcesso" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="modelo" type="{http://webservice.acesso/}modeloColetorEnum" minOccurs="0"/>
 *         &lt;element name="modoTransmissao" type="{http://webservice.acesso/}modoTransmissaoEnum" minOccurs="0"/>
 *         &lt;element name="msgDisplay" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numSerie" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numeroTerminal" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="numeroTerminalAcionamento" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="padraoCadastro" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="porta" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="portaComunicacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="portaLeitorSerial" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="portaParalela" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="releEntrada" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="releSaida" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="resolucaoDPI" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="senhaAcessoOnzeDigitos" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="sensorEntrada" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="sensorSaida" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="sentidoAcesso" type="{http://webservice.acesso/}sentidoAcessoEnum" minOccurs="0"/>
 *         &lt;element name="tempoReleEntrada" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="tempoReleSaida" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="velocTransmissao" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="registrarTentativaAcesso" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "coletorWS", propOrder = {
    "aguardaGiro",
    "biometriaNaCatraca",
    "biometrico",
    "cartaoMaster",
    "codigo",
    "descricao",
    "descricaoModeloColetor",
    "descricaoModoTransmissao",
    "descricaoSentidoAcesso",
    "digitosLeituraCartao",
    "dispAlternativo",
    "indiceCamera",
    "inverterSinal",
    "ip",
    "leitorGertec",
    "localAcesso",
    "modelo",
    "modoTransmissao",
    "msgDisplay",
    "numSerie",
    "numeroTerminal",
    "numeroTerminalAcionamento",
    "padraoCadastro",
    "porta",
    "portaComunicacao",
    "portaLeitorSerial",
    "portaParalela",
    "registrarTentativaAcesso",
    "releEntrada",
    "releSaida",
    "resolucaoDPI",
    "senhaAcessoOnzeDigitos",
    "sensorEntrada",
    "sensorSaida",
    "sentidoAcesso",
    "tempoReleEntrada",
    "tempoReleSaida",
    "velocTransmissao"
})
public class ColetorWS {

    protected Boolean aguardaGiro;
    protected Boolean biometriaNaCatraca;
    protected Boolean biometrico;
    protected String cartaoMaster;
    protected Integer codigo;
    protected String descricao;
    protected String descricaoModeloColetor;
    protected String descricaoModoTransmissao;
    protected String descricaoSentidoAcesso;
    protected Integer digitosLeituraCartao;
    protected DispositivoAlternativoEnum dispAlternativo;
    protected Integer indiceCamera;
    protected Boolean inverterSinal;
    protected String ip;
    protected int leitorGertec;
    protected Integer localAcesso;
    protected ModeloColetorEnum modelo;
    protected ModoTransmissaoEnum modoTransmissao;
    protected String msgDisplay;
    protected String numSerie;
    protected Integer numeroTerminal;
    protected Integer numeroTerminalAcionamento;
    protected Boolean padraoCadastro;
    protected Integer porta;
    protected String portaComunicacao;
    protected String portaLeitorSerial;
    protected String portaParalela;
    protected Integer releEntrada;
    protected Integer releSaida;
    protected Integer resolucaoDPI;
    protected boolean senhaAcessoOnzeDigitos;
    protected Integer sensorEntrada;
    protected Integer sensorSaida;
    protected SentidoAcessoEnum sentidoAcesso;
    protected Integer tempoReleEntrada;
    protected Integer tempoReleSaida;
    protected Integer velocTransmissao;
    protected boolean registrarTentativaAcesso;

    /**
     * Gets the value of the aguardaGiro property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isAguardaGiro() {
        return aguardaGiro;
    }

    /**
     * Sets the value of the aguardaGiro property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setAguardaGiro(Boolean value) {
        this.aguardaGiro = value;
    }

    /**
     * Gets the value of the biometriaNaCatraca property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isBiometriaNaCatraca() {
        return biometriaNaCatraca;
    }

    /**
     * Sets the value of the biometriaNaCatraca property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setBiometriaNaCatraca(Boolean value) {
        this.biometriaNaCatraca = value;
    }

    /**
     * Gets the value of the biometrico property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isBiometrico() {
        return biometrico;
    }

    /**
     * Sets the value of the biometrico property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setBiometrico(Boolean value) {
        this.biometrico = value;
    }

    /**
     * Gets the value of the cartaoMaster property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCartaoMaster() {
        return cartaoMaster;
    }

    /**
     * Sets the value of the cartaoMaster property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCartaoMaster(String value) {
        this.cartaoMaster = value;
    }

    /**
     * Gets the value of the codigo property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Sets the value of the codigo property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Gets the value of the descricao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescricao() {
        return descricao;
    }

    /**
     * Sets the value of the descricao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescricao(String value) {
        this.descricao = value;
    }

    /**
     * Gets the value of the descricaoModeloColetor property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescricaoModeloColetor() {
        return descricaoModeloColetor;
    }

    /**
     * Sets the value of the descricaoModeloColetor property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescricaoModeloColetor(String value) {
        this.descricaoModeloColetor = value;
    }

    /**
     * Gets the value of the descricaoModoTransmissao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescricaoModoTransmissao() {
        return descricaoModoTransmissao;
    }

    /**
     * Sets the value of the descricaoModoTransmissao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescricaoModoTransmissao(String value) {
        this.descricaoModoTransmissao = value;
    }

    /**
     * Gets the value of the descricaoSentidoAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescricaoSentidoAcesso() {
        return descricaoSentidoAcesso;
    }

    /**
     * Sets the value of the descricaoSentidoAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescricaoSentidoAcesso(String value) {
        this.descricaoSentidoAcesso = value;
    }

    /**
     * Gets the value of the digitosLeituraCartao property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDigitosLeituraCartao() {
        return digitosLeituraCartao;
    }

    /**
     * Sets the value of the digitosLeituraCartao property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDigitosLeituraCartao(Integer value) {
        this.digitosLeituraCartao = value;
    }

    /**
     * Gets the value of the dispAlternativo property.
     * 
     * @return
     *     possible object is
     *     {@link DispositivoAlternativoEnum }
     *     
     */
    public DispositivoAlternativoEnum getDispAlternativo() {
        return dispAlternativo;
    }

    /**
     * Sets the value of the dispAlternativo property.
     * 
     * @param value
     *     allowed object is
     *     {@link DispositivoAlternativoEnum }
     *     
     */
    public void setDispAlternativo(DispositivoAlternativoEnum value) {
        this.dispAlternativo = value;
    }

    /**
     * Gets the value of the indiceCamera property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getIndiceCamera() {
        return indiceCamera;
    }

    /**
     * Sets the value of the indiceCamera property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setIndiceCamera(Integer value) {
        this.indiceCamera = value;
    }

    /**
     * Gets the value of the inverterSinal property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isInverterSinal() {
        return inverterSinal;
    }

    /**
     * Sets the value of the inverterSinal property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setInverterSinal(Boolean value) {
        this.inverterSinal = value;
    }

    /**
     * Gets the value of the ip property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIp() {
        return ip;
    }

    /**
     * Sets the value of the ip property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIp(String value) {
        this.ip = value;
    }

    /**
     * Gets the value of the leitorGertec property.
     * 
     */
    public int getLeitorGertec() {
        return leitorGertec;
    }

    /**
     * Sets the value of the leitorGertec property.
     * 
     */
    public void setLeitorGertec(int value) {
        this.leitorGertec = value;
    }

    /**
     * Gets the value of the localAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getLocalAcesso() {
        return localAcesso;
    }

    /**
     * Sets the value of the localAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setLocalAcesso(Integer value) {
        this.localAcesso = value;
    }

    /**
     * Gets the value of the modelo property.
     * 
     * @return
     *     possible object is
     *     {@link ModeloColetorEnum }
     *     
     */
    public ModeloColetorEnum getModelo() {
        return modelo;
    }

    /**
     * Sets the value of the modelo property.
     * 
     * @param value
     *     allowed object is
     *     {@link ModeloColetorEnum }
     *     
     */
    public void setModelo(ModeloColetorEnum value) {
        this.modelo = value;
    }

    /**
     * Gets the value of the modoTransmissao property.
     * 
     * @return
     *     possible object is
     *     {@link ModoTransmissaoEnum }
     *     
     */
    public ModoTransmissaoEnum getModoTransmissao() {
        return modoTransmissao;
    }

    /**
     * Sets the value of the modoTransmissao property.
     * 
     * @param value
     *     allowed object is
     *     {@link ModoTransmissaoEnum }
     *     
     */
    public void setModoTransmissao(ModoTransmissaoEnum value) {
        this.modoTransmissao = value;
    }

    /**
     * Gets the value of the msgDisplay property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsgDisplay() {
        return msgDisplay;
    }

    /**
     * Sets the value of the msgDisplay property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsgDisplay(String value) {
        this.msgDisplay = value;
    }

    /**
     * Gets the value of the numSerie property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumSerie() {
        return numSerie;
    }

    /**
     * Sets the value of the numSerie property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumSerie(String value) {
        this.numSerie = value;
    }

    /**
     * Gets the value of the numeroTerminal property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getNumeroTerminal() {
        return numeroTerminal;
    }

    /**
     * Sets the value of the numeroTerminal property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setNumeroTerminal(Integer value) {
        this.numeroTerminal = value;
    }

    /**
     * Gets the value of the numeroTerminalAcionamento property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getNumeroTerminalAcionamento() {
        return numeroTerminalAcionamento;
    }

    /**
     * Sets the value of the numeroTerminalAcionamento property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setNumeroTerminalAcionamento(Integer value) {
        this.numeroTerminalAcionamento = value;
    }

    /**
     * Gets the value of the padraoCadastro property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isPadraoCadastro() {
        return padraoCadastro;
    }

    /**
     * Sets the value of the padraoCadastro property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setPadraoCadastro(Boolean value) {
        this.padraoCadastro = value;
    }

    /**
     * Gets the value of the porta property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPorta() {
        return porta;
    }

    /**
     * Sets the value of the porta property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPorta(Integer value) {
        this.porta = value;
    }

    /**
     * Gets the value of the portaComunicacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPortaComunicacao() {
        return portaComunicacao;
    }

    /**
     * Sets the value of the portaComunicacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPortaComunicacao(String value) {
        this.portaComunicacao = value;
    }

    /**
     * Gets the value of the portaLeitorSerial property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPortaLeitorSerial() {
        return portaLeitorSerial;
    }

    /**
     * Sets the value of the portaLeitorSerial property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPortaLeitorSerial(String value) {
        this.portaLeitorSerial = value;
    }

    /**
     * Gets the value of the portaParalela property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPortaParalela() {
        return portaParalela;
    }

    /**
     * Sets the value of the portaParalela property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPortaParalela(String value) {
        this.portaParalela = value;
    }

    /**
     * Gets the value of the releEntrada property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getReleEntrada() {
        return releEntrada;
    }

    /**
     * Sets the value of the releEntrada property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setReleEntrada(Integer value) {
        this.releEntrada = value;
    }

    /**
     * Gets the value of the releSaida property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getReleSaida() {
        return releSaida;
    }

    /**
     * Sets the value of the releSaida property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setReleSaida(Integer value) {
        this.releSaida = value;
    }

    /**
     * Gets the value of the resolucaoDPI property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getResolucaoDPI() {
        return resolucaoDPI;
    }

    /**
     * Sets the value of the resolucaoDPI property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setResolucaoDPI(Integer value) {
        this.resolucaoDPI = value;
    }

    /**
     * Gets the value of the senhaAcessoOnzeDigitos property.
     * 
     */
    public boolean isSenhaAcessoOnzeDigitos() {
        return senhaAcessoOnzeDigitos;
    }

    /**
     * Sets the value of the senhaAcessoOnzeDigitos property.
     * 
     */
    public void setSenhaAcessoOnzeDigitos(boolean value) {
        this.senhaAcessoOnzeDigitos = value;
    }

    /**
     * Gets the value of the sensorEntrada property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getSensorEntrada() {
        return sensorEntrada;
    }

    /**
     * Sets the value of the sensorEntrada property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setSensorEntrada(Integer value) {
        this.sensorEntrada = value;
    }

    /**
     * Gets the value of the sensorSaida property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getSensorSaida() {
        return sensorSaida;
    }

    /**
     * Sets the value of the sensorSaida property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setSensorSaida(Integer value) {
        this.sensorSaida = value;
    }

    /**
     * Gets the value of the sentidoAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link SentidoAcessoEnum }
     *     
     */
    public SentidoAcessoEnum getSentidoAcesso() {
        return sentidoAcesso;
    }

    /**
     * Sets the value of the sentidoAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link SentidoAcessoEnum }
     *     
     */
    public void setSentidoAcesso(SentidoAcessoEnum value) {
        this.sentidoAcesso = value;
    }

    /**
     * Gets the value of the tempoReleEntrada property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getTempoReleEntrada() {
        return tempoReleEntrada;
    }

    /**
     * Sets the value of the tempoReleEntrada property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setTempoReleEntrada(Integer value) {
        this.tempoReleEntrada = value;
    }

    /**
     * Gets the value of the tempoReleSaida property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getTempoReleSaida() {
        return tempoReleSaida;
    }

    /**
     * Sets the value of the tempoReleSaida property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setTempoReleSaida(Integer value) {
        this.tempoReleSaida = value;
    }

    /**
     * Gets the value of the velocTransmissao property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getVelocTransmissao() {
        return velocTransmissao;
    }

    /**
     * Sets the value of the velocTransmissao property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setVelocTransmissao(Integer value) {
        this.velocTransmissao = value;
    }

    public boolean isRegistrarTentativaAcesso() {
        return registrarTentativaAcesso;
    }

    public void setRegistrarTentativaAcesso(boolean registrarTentativaAcesso) {
        this.registrarTentativaAcesso = registrarTentativaAcesso;
    }
}


package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for validarAcessoPeloCodigoAcesso complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="validarAcessoPeloCodigoAcesso">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="codigoCartao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="empresa" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="forcarLib" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="localAcesso" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="meioIdentificacao" type="{http://webservice.acesso/}meioIdentificacaoEnum" minOccurs="0"/>
 *         &lt;element name="sentido" type="{http://webservice.acesso/}direcaoAcessoEnum" minOccurs="0"/>
 *         &lt;element name="terminal" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "validarAcessoPeloCodigoAcesso", propOrder = {
    "codigoCartao",
    "empresa",
    "forcarLib",
    "key",
    "localAcesso",
    "meioIdentificacao",
    "sentido",
    "terminal"
})
public class ValidarAcessoPeloCodigoAcesso {

    protected String codigoCartao;
    protected Integer empresa;
    protected Boolean forcarLib;
    protected String key;
    protected Integer localAcesso;
    protected MeioIdentificacaoEnum meioIdentificacao;
    protected DirecaoAcessoEnum sentido;
    protected String terminal;

    /**
     * Gets the value of the codigoCartao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoCartao() {
        return codigoCartao;
    }

    /**
     * Sets the value of the codigoCartao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoCartao(String value) {
        this.codigoCartao = value;
    }

    /**
     * Gets the value of the empresa property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getEmpresa() {
        return empresa;
    }

    /**
     * Sets the value of the empresa property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setEmpresa(Integer value) {
        this.empresa = value;
    }

    /**
     * Gets the value of the forcarLib property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isForcarLib() {
        return forcarLib;
    }

    /**
     * Sets the value of the forcarLib property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setForcarLib(Boolean value) {
        this.forcarLib = value;
    }

    /**
     * Gets the value of the key property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Sets the value of the key property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Gets the value of the localAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getLocalAcesso() {
        return localAcesso;
    }

    /**
     * Sets the value of the localAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setLocalAcesso(Integer value) {
        this.localAcesso = value;
    }

    /**
     * Gets the value of the meioIdentificacao property.
     * 
     * @return
     *     possible object is
     *     {@link MeioIdentificacaoEnum }
     *     
     */
    public MeioIdentificacaoEnum getMeioIdentificacao() {
        return meioIdentificacao;
    }

    /**
     * Sets the value of the meioIdentificacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link MeioIdentificacaoEnum }
     *     
     */
    public void setMeioIdentificacao(MeioIdentificacaoEnum value) {
        this.meioIdentificacao = value;
    }

    /**
     * Gets the value of the sentido property.
     * 
     * @return
     *     possible object is
     *     {@link DirecaoAcessoEnum }
     *     
     */
    public DirecaoAcessoEnum getSentido() {
        return sentido;
    }

    /**
     * Sets the value of the sentido property.
     * 
     * @param value
     *     allowed object is
     *     {@link DirecaoAcessoEnum }
     *     
     */
    public void setSentido(DirecaoAcessoEnum value) {
        this.sentido = value;
    }

    /**
     * Gets the value of the terminal property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTerminal() {
        return terminal;
    }

    /**
     * Sets the value of the terminal property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTerminal(String value) {
        this.terminal = value;
    }

}

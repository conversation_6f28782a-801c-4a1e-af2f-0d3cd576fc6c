package negocio.comuns.acesso;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.http.auth.UsernamePasswordCredentials;

import java.util.*;

public class IntegracaoMemberVO extends SuperVO {

    private String descricao;
    private String username;
    private String token;
    private String descricaoParcialPlano;
    private EmpresaVO empresa;
    private Date primeiraSincronizacao;
    private Date ultimaSincronizacao;
    private Integer consultor<PERSON>rao;
    private Integer professor<PERSON><PERSON><PERSON>;
    private String dddPadrao;
    private Integer planoPadrao;
    private Integer modalidadePadrao;
    private Integer horarioPadrao;
    private Integer diasCarencia;
    private Date dataInicialConsiderarLancamentos;
    private boolean memberFreePass;
    private Map<String, Date> mapaTokenDateBlocked = new HashMap();

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getToken() {
        return token;
    }

    public String getRandomToken() {
        List<String> tokens = tokensDisponiveisNaoBloqueados();
        return tokens.isEmpty() ? "" : tokens.get(new Random().nextInt(tokens.size()));
    }

    public Map<String, Date> getMapaTokenDateBlocked() {
        if (mapaTokenDateBlocked == null) {
            mapaTokenDateBlocked = new HashMap<>();
        }
        return mapaTokenDateBlocked;
    }

    public void setMapaTokenDateBlocked(Map<String, Date> mapaTokenDateBlocked) {
        this.mapaTokenDateBlocked = mapaTokenDateBlocked;
    }

    public List<String> tokensDisponiveisNaoBloqueados() {
        List<String> tokens = new ArrayList<>();
        for (String t: Arrays.asList(getToken().split(","))) {
            if (mapaTokenDateBlocked.get(t) == null || Calendario.maior(Calendario.hoje(), Calendario.somarMinutos(mapaTokenDateBlocked.get(t), 30))) {
                tokens.add(t);
            }
        }
        return tokens;
    }

    public Date obterProximaDataHoraPrevistaDesbloqueio() {
        Date dtPrev = null;
        for (String key: mapaTokenDateBlocked.keySet()) {
            dtPrev = dtPrev == null || Calendario.menor(mapaTokenDateBlocked.get(key), dtPrev)
                    ? mapaTokenDateBlocked.get(key) : dtPrev;
        }
        return dtPrev == null ? Calendario.hoje() : dtPrev;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getDescricaoParcialPlano() {
        return descricaoParcialPlano;
    }

    public void setDescricaoParcialPlano(String descricaoParcialPlano) {
        this.descricaoParcialPlano = descricaoParcialPlano;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Date getPrimeiraSincronizacao() {
        return primeiraSincronizacao;
    }

    public void setPrimeiraSincronizacao(Date primeiraSincronizacao) {
        this.primeiraSincronizacao = primeiraSincronizacao;
    }

    public Date getUltimaSincronizacao() {
        return ultimaSincronizacao;
    }

    public void setUltimaSincronizacao(Date ultimaSincronizacao) {
        this.ultimaSincronizacao = ultimaSincronizacao;
    }

    public UsernamePasswordCredentials getCredentials() {
        return new UsernamePasswordCredentials(getUsername(), getRandomToken());
    }

    public Integer getConsultorPadrao() {
        return consultorPadrao;
    }

    public void setConsultorPadrao(Integer consultorPadrao) {
        this.consultorPadrao = consultorPadrao;
    }

    public Integer getProfessorPadrao() {
        return professorPadrao;
    }

    public void setProfessorPadrao(Integer professorPadrao) {
        this.professorPadrao = professorPadrao;
    }

    public String getDddPadrao() {
        return dddPadrao;
    }

    public void setDddPadrao(String dddPadrao) {
        this.dddPadrao = dddPadrao;
    }

    public Integer getPlanoPadrao() {
        return planoPadrao;
    }

    public void setPlanoPadrao(Integer planoPadrao) {
        this.planoPadrao = planoPadrao;
    }

    public Integer getModalidadePadrao() {
        return modalidadePadrao;
    }

    public void setModalidadePadrao(Integer modalidadePadrao) {
        this.modalidadePadrao = modalidadePadrao;
    }

    public Integer getHorarioPadrao() {
        return horarioPadrao;
    }

    public void setHorarioPadrao(Integer horarioPadrao) {
        this.horarioPadrao = horarioPadrao;
    }

    public Integer getDiasCarencia() {
        return diasCarencia;
    }

    public void setDiasCarencia(Integer diasCarencia) {
        this.diasCarencia = diasCarencia;
    }

    public Date getDataInicialConsiderarLancamentos() {
        return dataInicialConsiderarLancamentos;
    }

    public void setDataInicialConsiderarLancamentos(Date dataInicialConsiderarLancamentos) {
        this.dataInicialConsiderarLancamentos = dataInicialConsiderarLancamentos;
    }

    public boolean isMemberFreePass() {
        return memberFreePass;
    }

    public void setMemberFreePass(boolean memberFreePass) {
        this.memberFreePass = memberFreePass;
    }

    public void validarDados() throws ValidacaoException {
        if (UteisValidacao.emptyString(username)) {
            throw new ValidacaoException("Necessário informar o DNS.");
        }
        if (UteisValidacao.emptyString(token)) {
            throw new ValidacaoException("Necessário informar o Token.");
        }
        if (UteisValidacao.emptyNumber(consultorPadrao)) {
            throw new ValidacaoException("Necessário informar o Consultor.");
        }
        if (UteisValidacao.emptyNumber(professorPadrao)) {
            throw new ValidacaoException("Necessário informar o Professor.");
        }
        if (UteisValidacao.emptyString(dddPadrao)) {
            throw new ValidacaoException("Necessário informar o DDD padrão.");
        }
        if (dataInicialConsiderarLancamentos == null) {
            throw new ValidacaoException("Necessário informar a data inicial considerar lançamentos");
        }
    }
}

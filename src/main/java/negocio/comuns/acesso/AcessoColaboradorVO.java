/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso;

import annotations.arquitetura.NaoControlarLogAlteracao;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;

/**
 *
 * <AUTHOR>
 */
public class AcessoColaboradorVO extends SuperVO {

    private Integer codigo;
    private ColaboradorVO colaborador;
    private Date dataHoraEntrada;
    private Date dataHoraSaida;
    private LocalAcessoVO localAcesso;
    private ColetorVO coletor;
    private String sentido = "";
    @NaoControlarLogAlteracao
    private String nomeDiaSemanaAcesso = "";
    private MeioIdentificacaoEnum meioIdentificacaoEntrada;
    private MeioIdentificacaoEnum meioIdentificacaoSaida ;
    private String intervaloDataHoras="";
    private LiberacaoAcessoVO liberacaoacesso;

    public MeioIdentificacaoEnum getMeioIdentificacaoEntrada() {
        return meioIdentificacaoEntrada;
    }

    public void setMeioIdentificacaoEntrada(MeioIdentificacaoEnum meioIdentificacaoEntrada) {
        this.meioIdentificacaoEntrada = meioIdentificacaoEntrada;
    }

    public MeioIdentificacaoEnum getMeioIdentificacaoSaida() {
        return meioIdentificacaoSaida;
    }

    public void setMeioIdentificacaoSaida(MeioIdentificacaoEnum meioIdentificacaoSaida) {
        this.meioIdentificacaoSaida = meioIdentificacaoSaida;
    }

    
    public Integer getCodigo() {
        return codigo;
    }


    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    public ColetorVO getColetor() {
        return coletor;
    }

    public void setColetor(ColetorVO coletor) {
        this.coletor = coletor;
    }

    public LocalAcessoVO getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(LocalAcessoVO localAcesso) {
        this.localAcesso = localAcesso;
    }

    public String getSentido() {
        return sentido;
    }

    public void setSentido(String sentido) {
        this.sentido = sentido;
    }

    public Date getDataHoraEntrada() {
        return dataHoraEntrada;
    }

    public void setDataHoraEntrada(Date dataHoraEntrada) {
        this.dataHoraEntrada = dataHoraEntrada;
    }

    public Date getDataHoraSaida() {
        return dataHoraSaida;
    }

    public void setDataHoraSaida(Date dataHoraSaida) {
        this.dataHoraSaida = dataHoraSaida;
    }

    public String getNomeDiaSemanaAcesso() {
        Calendar cal = new GregorianCalendar();
        cal.setTime(this.dataHoraEntrada);
        nomeDiaSemanaAcesso = Uteis.retornaDescricaoDiaSemana(cal);
        return nomeDiaSemanaAcesso;
    }

    public void setNomeDiaSemanaAcesso(String nomeDiaSemanaAcesso) {
        this.nomeDiaSemanaAcesso = nomeDiaSemanaAcesso;
    }
    public String getIntervaloDataHoras() {
        return intervaloDataHoras;
    }
    public void setIntervaloDataHoras(String intervaloDataHoras) {
        this.intervaloDataHoras = intervaloDataHoras;
    }

    public LiberacaoAcessoVO getLiberacaoacesso() {
        if (liberacaoacesso == null){
            liberacaoacesso = new LiberacaoAcessoVO();
        }
        return liberacaoacesso;
    }

    public void setLiberacaoacesso(LiberacaoAcessoVO liberacaoacesso) {
        this.liberacaoacesso = liberacaoacesso;
    }
}

package negocio.comuns.acesso;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Este value object guarnece os dados da quantidade de acessos dos clientes agrupados pelos dias da semana
 *
 * <AUTHOR>
 * @since 23/07/2018
 */
public class AcessoClienteAgrupadoDataVO {

    private Long quantidadeAcessosTotal;
    private Map<String, Long> acessosDiarios;

    /**
     * Inicializa o mapa de acessos diários
     */
    public AcessoClienteAgrupadoDataVO() {
        this.quantidadeAcessosTotal = 0L;
        this.acessosDiarios = new HashMap<String, Long>();
    }

    /**
     * Adiciona um acesso diário somando a quantidade de acessos do dia à quantidade de acessos total e adicionando
     * mais um item de acesso diário à lista.
     *
     * @param quantidadeAcessos quantidade de acessos na data informada
     * @param data              data onde houveram acessos
     */
    public void adicionarAcessoDiario(Long quantidadeAcessos, Date data) {

        if (quantidadeAcessos == null || data == null) {
            return;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        DiaSemana diaSemana = DiaSemana.fromDiaSemana(calendar.get(Calendar.DAY_OF_WEEK));

        Long quantidadeAcessosAnterior = acessosDiarios.get(diaSemana.sigla);
        acessosDiarios.put(diaSemana.sigla, quantidadeAcessosAnterior == null ? quantidadeAcessos : quantidadeAcessosAnterior + quantidadeAcessos);
        quantidadeAcessosTotal += quantidadeAcessos;
    }

    public Long getQuantidadeAcessosTotal() {
        return quantidadeAcessosTotal;
    }

    public Map<String, Long> getAcessosDiarios() {
        return acessosDiarios;
    }

    /**
     * Identificador dos dias da semana
     */
    public enum DiaSemana {

        DOMINGO(Calendar.SUNDAY, "DOM"),
        SEGUNDA(Calendar.MONDAY, "SEG"),
        TERCA(Calendar.TUESDAY, "TER"),
        QUARTA(Calendar.WEDNESDAY, "QUA"),
        QUINTA(Calendar.THURSDAY, "QUI"),
        SEXTA(Calendar.FRIDAY, "SEX"),
        SABADO(Calendar.SATURDAY, "SAB");

        private Integer diaSemanaCalendario;
        private String sigla;

        /**
         * @param diaSemanaCalendario O dia da semana no calendário {@link Calendar}
         * @param sigla               A sigla do dia da semana
         */
        DiaSemana(Integer diaSemanaCalendario, String sigla) {
            this.diaSemanaCalendario = diaSemanaCalendario;
            this.sigla = sigla;
        }

        /**
         * @param diaSemana Inteiro representando o dia da semana
         * @return Uma instância deste enum representando aquele dia na semana
         */
        public static DiaSemana fromDiaSemana(Integer diaSemana) {
            if (diaSemana == null) {
                return null;
            }

            for (DiaSemana dia : DiaSemana.values()) {
                if (dia.diaSemanaCalendario.equals(diaSemana)) {
                    return dia;
                }
            }

            return null;
        }

    }

}

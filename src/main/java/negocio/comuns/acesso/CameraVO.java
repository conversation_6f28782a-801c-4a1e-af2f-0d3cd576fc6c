package negocio.comuns.acesso;

import annotations.arquitetura.ChavePrimaria;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

public class CameraVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private String endereco;
    private String descricao;
    private Integer terminal;
    private ServidorFacialVO servidorFacialVO;
    private Integer indice;
    private String urlRtsp;


    public void validarDados() throws ConsistirException {
        if (UteisValidacao.emptyString(descricao)) {
            throw new ConsistirException("Descrição não pode ser vazia");
        }
        if (UteisValidacao.emptyString(endereco)) {
            throw new ConsistirException("Endereço não pode ser vazio");
        }
        if (UteisValidacao.emptyNumber(getServidorFacialVO().getCodigo())) {
            throw new ConsistirException("A câmera tem que pertencer a algum servidor facial");
        }
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getEndereco() {
        if (endereco == null) {
            endereco = "";
        }
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getTerminal() {
        if (terminal == null) {
            terminal = 0;
        }
        return terminal;
    }

    public void setTerminal(Integer terminal) {
        this.terminal = terminal;
    }

    public ServidorFacialVO getServidorFacialVO() {
        if (servidorFacialVO == null) {
            servidorFacialVO = new ServidorFacialVO();
        }
        return servidorFacialVO;
    }

    public void setServidorFacialVO(ServidorFacialVO servidorFacialVO) {
        this.servidorFacialVO = servidorFacialVO;
    }

    public Integer getIndice() {
        if (indice == null) {
            indice = 0;
        }
        return indice;
    }

    public void setIndice(Integer indice) {
        this.indice = indice;
    }

    public String getUrlRtsp() {
        if (urlRtsp == null){
            urlRtsp = "";
        }
        return urlRtsp;
    }

    public void setUrlRtsp(String urlRtsp) {
        this.urlRtsp = urlRtsp;
    }

    public JSONObject toJSON() throws JSONException {
        JSONObject object = new JSONObject();
        object.put("codigo", codigo);
        object.put("endereco", endereco);
        object.put("descricao", descricao);
        object.put("terminal", terminal);
        object.put("indice", indice);
        object.put("urlRtsp", urlRtsp);
        return object;
    }
}

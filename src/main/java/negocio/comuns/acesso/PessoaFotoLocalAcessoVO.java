/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.acesso;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class PessoaFotoLocalAcessoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    protected LocalAcessoVO localAcesso;
    @ChaveEstrangeira
    protected PessoaVO pessoa;
    protected Date dtHrEnvio;

    public Date getDtHrEnvio() {
        return dtHrEnvio;
    }

    public void setDtHrEnvio(Date dtHrEnvio) {
        this.dtHrEnvio = dtHrEnvio;
    }

    public LocalAcessoVO getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(LocalAcessoVO localAcesso) {
        this.localAcesso = localAcesso;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public static void validarDados(PessoaFotoLocalAcessoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }

        if (obj.getLocalAcesso() == null)
        {
            throw new ConsistirException("O campo LOCAL DE ACESSO deve ser informado.");
        }
        if (obj.getPessoa() == null)
        {
            throw new ConsistirException("O campo PESSOA deve ser informado.");
        }
    }

}

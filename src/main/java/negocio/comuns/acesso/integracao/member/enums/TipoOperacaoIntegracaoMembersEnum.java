package negocio.comuns.acesso.integracao.member.enums;

import negocio.comuns.basico.enumerador.MascaraDataEnum;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public enum TipoOperacaoIntegracaoMembersEnum {

    MEMBERS(1, "Importação Members", "Importação de Cadastros, Contratos, Produtos, Serviços, Aula Avulsa Diaria, Cancelamento, Transferencia direito de uso e Depedentes"),
    MEMBERS_FREEPASS(2, "Members plano VIP","Integração Members, importação de cadastro com lançamento de freepass e autorização de acesso no GateWay da Rede Empresa"),
    RECEBIMENTOS_PARCELAS(3, "Recebimento parcelas", "Verificar recebimentos no Evo e pagar as respectivas parcelas no sistema Pacto"),
    CORRIGIR_PAGAMENTOS_SEM_AUTORIZACAO_NSU(4, "Corrigir pagamentos cartão crédito sem autorização", "Corrigir pagamentos importação em cartão de crédito que deveriam ter autorização NSU"),
    RECEBIMENTOS_PARCELAS_VENDA_PRODUTO_SERVICO_DIARIA(5, "Recebimento parcelas venda de produtos, serviços e diarias", "Verificar recebimentos no Evo e pagar as respectivas parcelas no sistema Pacto"),
    SINCRONIZAR_CANCELAMENTOS(6, "Sincronizar cancelamentos contratos", "Sincronizar cancelamentos de contratos no Evo com o sistema Pacto");

    TipoOperacaoIntegracaoMembersEnum(Integer id, String nome, String descricao) {
        this.id = id;
        this.nome = nome;
        this.descricao = descricao;
    }

    private Integer id;
    private String nome;
    private String descricao;

    public static TipoOperacaoIntegracaoMembersEnum obterPorId(Integer tipoOperacaoIntegracaoMembers) {
        for (TipoOperacaoIntegracaoMembersEnum f : TipoOperacaoIntegracaoMembersEnum.values()) {
            if (f.getId().equals(tipoOperacaoIntegracaoMembers)) {
                return f;
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

package negocio.comuns.acesso.integracao.member.enums;

public enum ContactTypeEnum {

    TELEPHONE(1, "Telephone"),
    CELLPHONE(2, "Cellphone"),
    EMAIL(4, "E-mail");

    ContactTypeEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public static ContactTypeEnum obterPorId(Integer id) {
        for (ContactTypeEnum contactTypeEnum: ContactTypeEnum.values()) {
            if (contactTypeEnum.getId().equals(id)) {
                return contactTypeEnum;
            }
        }
        return null;
    }

    private Integer id;
    private String descricao;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

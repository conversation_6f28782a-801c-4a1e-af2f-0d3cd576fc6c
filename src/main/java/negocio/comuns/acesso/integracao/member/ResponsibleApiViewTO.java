package negocio.comuns.acesso.integracao.member;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ResponsibleApiViewTO {
    private String name;
    private String document;
    private Boolean financialResponsible;

    // Getters and Setters

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDocument() {
        return document;
    }

    public void setDocument(String document) {
        this.document = document;
    }

    public Boolean getFinancialResponsible() {
        return financialResponsible;
    }

    public void setFinancialResponsible(Boolean financialResponsible) {
        this.financialResponsible = financialResponsible;
    }
}

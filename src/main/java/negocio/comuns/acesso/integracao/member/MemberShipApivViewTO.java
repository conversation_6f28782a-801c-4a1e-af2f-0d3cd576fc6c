package negocio.comuns.acesso.integracao.member;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MemberShipApivViewTO {

    private Integer idMembership;
    private String nameMembership;
    private String description;
    private String durationType;
    private Integer duration;

    public MemberShipApivViewTO() {
    }

    public MemberShipApivViewTO(Integer idMembership, String nameMembership) {
        this.idMembership = idMembership;
        this.nameMembership = nameMembership;
    }

    public Integer getIdMembership() {
        return idMembership;
    }

    public void setIdMembership(Integer idMembership) {
        this.idMembership = idMembership;
    }

    public String getNameMembership() {
        return nameMembership;
    }

    public void setNameMembership(String nameMembership) {
        this.nameMembership = nameMembership;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDurationType() {
        return durationType;
    }

    public void setDurationType(String durationType) {
        this.durationType = durationType;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }
}

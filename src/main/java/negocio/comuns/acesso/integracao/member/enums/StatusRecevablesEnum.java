package negocio.comuns.acesso.integracao.member.enums;

import negocio.comuns.utilitarias.UteisValidacao;

public enum StatusRecevablesEnum {

    OPEN(1, "open"),
    RECEIVED(2, "received"),
    CANCELED(3, "Canceled"),
    EXPIRED(4, "Expired")
    ;

    StatusRecevablesEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public static StatusRecevablesEnum obterPorId(Integer id) {
        if (UteisValidacao.emptyNumber(id)) {
            return null;
        }
        for (StatusRecevablesEnum s: StatusRecevablesEnum.values()) {
            if (s.getId().equals(id)) {
                return s;
            }
        }
        return null;
    }

    private Integer id;
    private String name;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

package negocio.comuns.acesso.integracao.member;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FreezeViewTO {
    private String startSuspend;
    private String endSuspend;
    private String unlockDate;
    private Integer idEmployee;
    private String reason;
    private Boolean flUseMembershipFreezeDays;


    public String getStartSuspend() {
        return startSuspend;
    }

    public void setStartSuspend(String startSuspend) {
        this.startSuspend = startSuspend;
    }

    public String getEndSuspend() {
        return endSuspend;
    }

    public void setEndSuspend(String endSuspend) {
        this.endSuspend = endSuspend;
    }

    public String getUnlockDate() {
        return unlockDate;
    }

    public void setUnlockDate(String unlockDate) {
        this.unlockDate = unlockDate;
    }

    public Integer getIdEmployee() {
        return idEmployee;
    }

    public void setIdEmployee(Integer idEmployee) {
        this.idEmployee = idEmployee;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Boolean getFlUseMembershipFreezeDays() {
        return flUseMembershipFreezeDays;
    }

    public void setFlUseMembershipFreezeDays(Boolean flUseMembershipFreezeDays) {
        this.flUseMembershipFreezeDays = flUseMembershipFreezeDays;
    }
}

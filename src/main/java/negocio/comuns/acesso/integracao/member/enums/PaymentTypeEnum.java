package negocio.comuns.acesso.integracao.member.enums;

import negocio.comuns.utilitarias.UteisValidacao;

public enum PaymentTypeEnum {
    CASH(1, "Cash", "<PERSON><PERSON><PERSON>"),
    CREDIT_CARD(2, "Credit card", "Cartão de crédito"),
    DEBIT_CARD(3, "Debit card", "Cartão de débito"),

    CHECK(4, "Check", "Cheque"),
    BANK_SLIP(5, "Bank slip", "Boleto"),
    PAG_SEGURO(6, "PagSeguro", "PagSeguro"),
    DEPOSIT(7, "Deposit", "Deposito"),
    ACCOUNT_DEBIT(8, "Account Debit", "Account Debit"),
    INTERNET(9, "Internet", "Internet"),
    SALES_CREDIT(11, "Sales credit", "Credito de venda"),
    ONLINE_CREDIT_CARD(12, "Credit card", "Cartão de crédito"),
    TRANSFER(13, "Transfer", "Transferência"),
    PIX(18, "Pix", "Pix"),
    BALANCE_DUE(0, "Balance due", "Débito em conta");


    PaymentTypeEnum(Integer id, String name, String nomePt) {
        this.id = id;
        this.name = name;
        this.nomePt = nomePt;
    }

    public static PaymentTypeEnum getById(Integer id) {
        if (!UteisValidacao.emptyNumber(id)) {
            for (PaymentTypeEnum p: PaymentTypeEnum.values()) {
                if (p.getId().equals(id)) {
                    return p;
                }
            }
        }
        return null;
    }

    public static PaymentTypeEnum getByName(String name) {
        if (!UteisValidacao.emptyString(name)) {
            name = name.trim().toUpperCase();
            for (PaymentTypeEnum p: PaymentTypeEnum.values()) {
                if (p.getName().toUpperCase().equals(name)) {
                    return p;
                }
            }
        }
        return null;
    }

    private Integer id;
    private String name;
    private String nomePt;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNomePt() {
        return nomePt;
    }

    public void setNomePt(String nomePt) {
        this.nomePt = nomePt;
    }
}

package negocio.comuns.acesso.integracao.member;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MembersApiViewTO {

    private Integer idMember;
    private String firstName;
    private String lastName;
    private String registerDate;
    private Integer idBranch;
    private String branchName;
    private Boolean accessBlocked;
    private String blockedReason;
    private String document;
    private String documentId;
    private String passport;
    private String maritalStatus;
    private String gender;
    private String birthDate;
    private String address;
    private String state;
    private String city;
    private String zipCode;
    private String complement;
    private String neighborhood;
    private String accessCardNumber;
    private String number;
    private String membershipStatus;
    private String status;
    private List<ContactsApiViewTO> contacts;
    private List<MemberMembershipApiViewTO> memberships;
    private String lastAccessDate;
    private Integer idEmployeeConsultant;
    private String nameEmployeeConsultant;
    private Integer idEmployeeInstructor;
    private String nameEmployeeInstructor;
    private Integer idEmployeePersonalTrainer;
    private String nameEmployeePersonalTrainer;
    private String photoUrl;
    private String country;
    private String idMemberMigration;
    private List<MemberResponsibleViewTO> responsibles;
    private String tokenGympass;
    private String gympassId;
    private JSONObject dataJson;

    public Integer getIdMember() {
        return idMember;
    }

    public void setIdMember(Integer idMember) {
        this.idMember = idMember;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(String registerDate) {
        this.registerDate = registerDate;
    }

    public Integer getIdBranch() {
        return idBranch;
    }

    public void setIdBranch(Integer idBranch) {
        this.idBranch = idBranch;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public Boolean getAccessBlocked() {
        return accessBlocked;
    }

    public void setAccessBlocked(Boolean accessBlocked) {
        this.accessBlocked = accessBlocked;
    }

    public String getBlockedReason() {
        return blockedReason;
    }

    public void setBlockedReason(String blockedReason) {
        this.blockedReason = blockedReason;
    }

    public String getDocument() {
        return document;
    }

    public void setDocument(String document) {
        this.document = document;
    }

    public String getDocumentId() {
        return documentId;
    }

    public String getPassport() {
        return passport;
    }

    public void setPassport(String passport) {
        this.passport = passport;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getComplement() {
        return complement;
    }

    public void setComplement(String complement) {
        this.complement = complement;
    }

    public String getNeighborhood() {
        return neighborhood;
    }

    public void setNeighborhood(String neighborhood) {
        this.neighborhood = neighborhood;
    }

    public String getAccessCardNumber() {
        return accessCardNumber;
    }

    public void setAccessCardNumber(String accessCardNumber) {
        this.accessCardNumber = accessCardNumber;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getMembershipStatus() {
        return membershipStatus;
    }

    public void setMembershipStatus(String membershipStatus) {
        this.membershipStatus = membershipStatus;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<ContactsApiViewTO> getContacts() {
        return contacts;
    }

    public void setContacts(List<ContactsApiViewTO> contacts) {
        this.contacts = contacts;
    }

    public List<MemberMembershipApiViewTO> getMemberships() {
        if (memberships == null) {
            memberships = new ArrayList<>();
        }
        return memberships;
    }

    public void setMemberships(List<MemberMembershipApiViewTO> memberships) {
        this.memberships = memberships;
    }

    public String getLastAccessDate() {
        return lastAccessDate;
    }

    public void setLastAccessDate(String lastAccessDate) {
        this.lastAccessDate = lastAccessDate;
    }

    public Integer getIdEmployeeConsultant() {
        return idEmployeeConsultant;
    }

    public void setIdEmployeeConsultant(Integer idEmployeeConsultant) {
        this.idEmployeeConsultant = idEmployeeConsultant;
    }

    public String getNameEmployeeConsultant() {
        return nameEmployeeConsultant;
    }

    public void setNameEmployeeConsultant(String nameEmployeeConsultant) {
        this.nameEmployeeConsultant = nameEmployeeConsultant;
    }

    public Integer getIdEmployeeInstructor() {
        return idEmployeeInstructor;
    }

    public void setIdEmployeeInstructor(Integer idEmployeeInstructor) {
        this.idEmployeeInstructor = idEmployeeInstructor;
    }

    public String getNameEmployeeInstructor() {
        return nameEmployeeInstructor;
    }

    public void setNameEmployeeInstructor(String nameEmployeeInstructor) {
        this.nameEmployeeInstructor = nameEmployeeInstructor;
    }

    public Integer getIdEmployeePersonalTrainer() {
        return idEmployeePersonalTrainer;
    }

    public void setIdEmployeePersonalTrainer(Integer idEmployeePersonalTrainer) {
        this.idEmployeePersonalTrainer = idEmployeePersonalTrainer;
    }

    public String getNameEmployeePersonalTrainer() {
        return nameEmployeePersonalTrainer;
    }

    public void setNameEmployeePersonalTrainer(String nameEmployeePersonalTrainer) {
        this.nameEmployeePersonalTrainer = nameEmployeePersonalTrainer;
    }

    public String getPhotoUrl() {
        return photoUrl;
    }

    public void setPhotoUrl(String photoUrl) {
        this.photoUrl = photoUrl;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getIdMemberMigration() {
        return idMemberMigration;
    }

    public void setIdMemberMigration(String idMemberMigration) {
        this.idMemberMigration = idMemberMigration;
    }

    public List<MemberResponsibleViewTO> getResponsibles() {
        return responsibles;
    }

    public void setResponsibles(List<MemberResponsibleViewTO> responsibles) {
        this.responsibles = responsibles;
    }

    public String getTokenGympass() {
        return tokenGympass;
    }

    public void setTokenGympass(String tokenGympass) {
        this.tokenGympass = tokenGympass;
    }

    public JSONObject getDataJson() {
        return dataJson;
    }

    public String getGympassId() {
        return gympassId;
    }

    public void setGympassId(String gympassId) {
        this.gympassId = gympassId;
    }

    public void setDataJson(JSONObject dataJson) {
        this.dataJson = dataJson;
    }

    public MemberMembershipApiViewTO getActivePlan(String descricaoParcialPlano) {
        if (UteisValidacao.emptyString(descricaoParcialPlano)) {
            return null;
        }
        List<String> descricoesParciaisPlanos = Arrays.stream(descricaoParcialPlano.split(";")).map(String::trim).collect(Collectors.toList());
        for (MemberMembershipApiViewTO membership : memberships) {
            if (membership.getName() != null &&
                    membership.getMembershipStatus() != null &&
                    descricoesParciaisPlanos.stream().anyMatch(membership.getName()::contains) &&
                    membership.getMembershipStatus().equals("active")) {
                return membership;
            }
        }
        return null;
    }

    public MemberVO toMember() {
        return new MemberVO(this);
    }
}

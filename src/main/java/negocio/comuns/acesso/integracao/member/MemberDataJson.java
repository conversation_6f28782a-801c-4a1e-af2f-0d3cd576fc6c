package negocio.comuns.acesso.integracao.member;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

public class MemberDataJson {

    private Integer codigoMember;
    private Integer idMember;
    private JSONObject memberJson;
    private JSONArray salesJson;
    private List<Integer> idsSales;
    private JSONArray receivablesJson;
    private JSONArray membersDependentesJson;
    private String msgErro;

    public Integer getCodigoMember() {
        return codigoMember;
    }

    public void setCodigoMember(Integer codigoMember) {
        this.codigoMember = codigoMember;
    }

    public Integer getIdMember() {
        return idMember;
    }

    public void setIdMember(Integer idMember) {
        this.idMember = idMember;
    }

    public JSONObject getMemberJson() {
        if (memberJson == null) {
            memberJson = new JSONObject();
        }
        return memberJson;
    }

    public void setMemberJson(JSONObject memberJson) {
        this.memberJson = memberJson;
    }

    public JSONArray getSalesJson() {
        if (salesJson == null) {
            salesJson = new JSONArray();
        }
        return salesJson;
    }

    public void setSalesJson(JSONArray salesJson) {
        this.salesJson = salesJson;
    }

    public List<Integer> getIdsSales() {
        return idsSales;
    }

    public void setIdsSales(List<Integer> idsSales) {
        this.idsSales = idsSales;
    }

    public JSONArray getReceivablesJson() {
        if (receivablesJson == null) {
            receivablesJson = new JSONArray();
        }
        return receivablesJson;
    }

    public void setReceivablesJson(JSONArray receivablesJson) {
        this.receivablesJson = receivablesJson;
    }

    public JSONArray getMembersDependentesJson() {
        if (membersDependentesJson == null) {
            membersDependentesJson = new JSONArray();
        }
        return membersDependentesJson;
    }

    public void setMembersDependentesJson(JSONArray membersDependentesJson) {
        this.membersDependentesJson = membersDependentesJson;
    }

    public String getMsgErro() {
        return msgErro;
    }

    public void setMsgErro(String msgErro) {
        this.msgErro = msgErro;
    }
}

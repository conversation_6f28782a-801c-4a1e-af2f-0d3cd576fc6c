package negocio.comuns.acesso.integracao.member;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MemberResponsibleViewTO {
    private Integer idResponsible;
    private Integer idMember;
    private String name;
    private String cpf;
    private String email;
    private String phone;
    private String observation;
    private Integer idMemberResponsible;
    private Boolean acessFiti;
    private Boolean financialResponsible;

    public Integer getIdResponsible() {
        return idResponsible;
    }

    public void setIdResponsible(Integer idResponsible) {
        this.idResponsible = idResponsible;
    }

    public Integer getIdMember() {
        return idMember;
    }

    public void setIdMember(Integer idMember) {
        this.idMember = idMember;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getObservation() {
        return observation;
    }

    public void setObservation(String observation) {
        this.observation = observation;
    }

    public Integer getIdMemberResponsible() {
        return idMemberResponsible;
    }

    public void setIdMemberResponsible(Integer idMemberResponsible) {
        this.idMemberResponsible = idMemberResponsible;
    }

    public Boolean getAcessFiti() {
        return acessFiti;
    }

    public void setAcessFiti(Boolean acessFiti) {
        this.acessFiti = acessFiti;
    }

    public Boolean getFinancialResponsible() {
        return financialResponsible;
    }

    public void setFinancialResponsible(Boolean financialResponsible) {
        this.financialResponsible = financialResponsible;
    }
}

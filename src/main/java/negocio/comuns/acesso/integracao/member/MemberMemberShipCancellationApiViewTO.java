package negocio.comuns.acesso.integracao.member;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.acesso.integracao.member.financeiro.ReceivablesViewTO;
import org.json.JSONObject;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MemberMemberShipCancellationApiViewTO {
    private Integer idMember;
    private String name;
    private Integer idMembership;
    private Integer idMemberMemberShip;
    private Integer idBranch;
    private Integer numMembers;
    private Integer idSale;
    private Double saleValue;
    private String nameMembership;
    private String membershipStart;
    private String membershipEnd;
    private String registerCancelDate;
    private String cancelDate;
    private String reasonCancellation;
    private String saleDate;
    private Double cancellationFine;
    private Double remainingValue;
    private List<ReceivablesViewTO> receivables;
    private String minPeriodStayMembership;
    private MembershipTrasnferDataApiViewTO membershipTrasnferData;
    private String idMemberMigration;
    private String idSaleMigration;
    private String idMembershipCategory;
    private String memberDocument;
    private JSONObject dadosJson;


    public Integer getIdMember() {
        return idMember;
    }

    public void setIdMember(Integer idMember) {
        this.idMember = idMember;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getIdMembership() {
        return idMembership;
    }

    public void setIdMembership(Integer idMembership) {
        this.idMembership = idMembership;
    }

    public Integer getIdMemberMemberShip() {
        return idMemberMemberShip;
    }

    public void setIdMemberMemberShip(Integer idMemberMemberShip) {
        this.idMemberMemberShip = idMemberMemberShip;
    }

    public Integer getIdBranch() {
        return idBranch;
    }

    public void setIdBranch(Integer idBranch) {
        this.idBranch = idBranch;
    }

    public Integer getNumMembers() {
        return numMembers;
    }

    public void setNumMembers(Integer numMembers) {
        this.numMembers = numMembers;
    }

    public Integer getIdSale() {
        return idSale;
    }

    public void setIdSale(Integer idSale) {
        this.idSale = idSale;
    }

    public Double getSaleValue() {
        return saleValue;
    }

    public void setSaleValue(Double saleValue) {
        this.saleValue = saleValue;
    }

    public String getNameMembership() {
        return nameMembership;
    }

    public void setNameMembership(String nameMembership) {
        this.nameMembership = nameMembership;
    }

    public String getMembershipStart() {
        return membershipStart;
    }

    public void setMembershipStart(String membershipStart) {
        this.membershipStart = membershipStart;
    }

    public String getMembershipEnd() {
        return membershipEnd;
    }

    public void setMembershipEnd(String membershipEnd) {
        this.membershipEnd = membershipEnd;
    }

    public String getRegisterCancelDate() {
        return registerCancelDate;
    }

    public void setRegisterCancelDate(String registerCancelDate) {
        this.registerCancelDate = registerCancelDate;
    }

    public String getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(String cancelDate) {
        this.cancelDate = cancelDate;
    }

    public String getReasonCancellation() {
        return reasonCancellation;
    }

    public void setReasonCancellation(String reasonCancellation) {
        this.reasonCancellation = reasonCancellation;
    }

    public String getSaleDate() {
        return saleDate;
    }

    public void setSaleDate(String saleDate) {
        this.saleDate = saleDate;
    }

    public Double getCancellationFine() {
        return cancellationFine;
    }

    public void setCancellationFine(Double cancellationFine) {
        this.cancellationFine = cancellationFine;
    }

    public Double getRemainingValue() {
        return remainingValue;
    }

    public void setRemainingValue(Double remainingValue) {
        this.remainingValue = remainingValue;
    }

    public List<ReceivablesViewTO> getReceivables() {
        return receivables;
    }

    public void setReceivables(List<ReceivablesViewTO> receivables) {
        this.receivables = receivables;
    }

    public String getMinPeriodStayMembership() {
        return minPeriodStayMembership;
    }

    public void setMinPeriodStayMembership(String minPeriodStayMembership) {
        this.minPeriodStayMembership = minPeriodStayMembership;
    }

    public MembershipTrasnferDataApiViewTO getMembershipTrasnferData() {
        return membershipTrasnferData;
    }

    public void setMembershipTrasnferData(MembershipTrasnferDataApiViewTO membershipTrasnferData) {
        this.membershipTrasnferData = membershipTrasnferData;
    }

    public String getIdMemberMigration() {
        return idMemberMigration;
    }

    public void setIdMemberMigration(String idMemberMigration) {
        this.idMemberMigration = idMemberMigration;
    }

    public String getIdSaleMigration() {
        return idSaleMigration;
    }

    public void setIdSaleMigration(String idSaleMigration) {
        this.idSaleMigration = idSaleMigration;
    }

    public String getIdMembershipCategory() {
        return idMembershipCategory;
    }

    public void setIdMembershipCategory(String idMembershipCategory) {
        this.idMembershipCategory = idMembershipCategory;
    }

    public String getMemberDocument() {
        return memberDocument;
    }

    public void setMemberDocument(String memberDocument) {
        this.memberDocument = memberDocument;
    }

    public JSONObject getDadosJson() {
        return dadosJson;
    }

    public void setDadosJson(JSONObject dadosJson) {
        this.dadosJson = dadosJson;
    }
}

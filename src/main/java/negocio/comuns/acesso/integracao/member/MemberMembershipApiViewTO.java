package negocio.comuns.acesso.integracao.member;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import importador.UteisImportacao;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MemberMembershipApiViewTO {
    private Integer idMember;
    private Integer idMembership;
    private Integer idMemberMembership;
    private Integer idMemberMembershipRenewed;
    private Integer idSale;
    private String startDate;
    private Date dataInicio;
    private Date dataFim;
    private String endDate;
    private String saleDate;
    private String name;
    private String cancelDate;
    private String cancelDateOn;
    private String cancelCreationDate;
    private String membershipStatus;
    private List<FreezeViewTO> freezes;
    private Double valueNextMonth;
    private MemberMemberShipCancellationApiViewTO memberShipCancellation;

    public Integer getIdMember() {
        return idMember;
    }

    public void setIdMember(Integer idMember) {
        this.idMember = idMember;
    }

    public Integer getIdMembership() {
        if (idMembership == null) {
            idMembership = 0;
        }
        return idMembership;
    }

    public void setIdMembership(Integer idMembership) {
        this.idMembership = idMembership;
    }

    public Integer getIdMemberMembership() {
        return idMemberMembership;
    }

    public void setIdMemberMembership(Integer idMemberMembership) {
        this.idMemberMembership = idMemberMembership;
    }

    public Integer getIdMemberMembershipRenewed() {
        return idMemberMembershipRenewed;
    }

    public void setIdMemberMembershipRenewed(Integer idMemberMembershipRenewed) {
        this.idMemberMembershipRenewed = idMemberMembershipRenewed;
    }

    public Integer getIdSale() {
        return idSale;
    }

    public void setIdSale(Integer idSale) {
        this.idSale = idSale;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public Date getDataInicio() {
        if (dataInicio == null) {
            dataInicio = UteisImportacao.getDateFromLocalDateTime(startDate);
        }
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        if (dataFim == null) {
            dataFim = UteisImportacao.getDateFromLocalDateTime(endDate);
        }
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getSaleDate() {
        return saleDate;
    }

    public void setSaleDate(String saleDate) {
        this.saleDate = saleDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(String cancelDate) {
        this.cancelDate = cancelDate;
    }

    public String getCancelDateOn() {
        return cancelDateOn;
    }

    public void setCancelDateOn(String cancelDateOn) {
        this.cancelDateOn = cancelDateOn;
    }

    public String getCancelCreationDate() {
        return cancelCreationDate;
    }

    public void setCancelCreationDate(String cancelCreationDate) {
        this.cancelCreationDate = cancelCreationDate;
    }

    public String getMembershipStatus() {
        if (membershipStatus == null) {
            membershipStatus = "";
        }
        return membershipStatus;
    }

    public void setMembershipStatus(String membershipStatus) {
        this.membershipStatus = membershipStatus;
    }

    public List<FreezeViewTO> getFreezes() {
        return freezes;
    }

    public void setFreezes(List<FreezeViewTO> freezes) {
        this.freezes = freezes;
    }

    public Double getValueNextMonth() {
        return valueNextMonth;
    }

    public void setValueNextMonth(Double valueNextMonth) {
        this.valueNextMonth = valueNextMonth;
    }

    public MemberMemberShipCancellationApiViewTO getMemberShipCancellation() {
        return memberShipCancellation;
    }

    public void setMemberShipCancellation(MemberMemberShipCancellationApiViewTO memberShipCancellation) {
        this.memberShipCancellation = memberShipCancellation;
    }
}

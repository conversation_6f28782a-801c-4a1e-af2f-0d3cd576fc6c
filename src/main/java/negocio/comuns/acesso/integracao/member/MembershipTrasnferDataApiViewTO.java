package negocio.comuns.acesso.integracao.member;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MembershipTrasnferDataApiViewTO {

    private Boolean flTransfer;
    private Integer idMemberTransfer;
    private Integer idMemberMembershipTransfer;

    public Boolean getFlTransfer() {
        return flTransfer;
    }

    public void setFlTransfer(Boolean flTransfer) {
        this.flTransfer = flTransfer;
    }

    public Integer getIdMemberTransfer() {
        return idMemberTransfer;
    }

    public void setIdMemberTransfer(Integer idMemberTransfer) {
        this.idMemberTransfer = idMemberTransfer;
    }

    public Integer getIdMemberMembershipTransfer() {
        return idMemberMembershipTransfer;
    }

    public void setIdMemberMembershipTransfer(Integer idMemberMembershipTransfer) {
        this.idMemberMembershipTransfer = idMemberMembershipTransfer;
    }
}

package negocio.comuns.acesso.integracao.member.financeiro;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CreditDetailViewTO {

    private Integer idCredit;
    private Integer idCancelationCredit;
    private Integer idBranchOrigin;
    private Integer ammount;
    private String branchDocument;
    private Integer idSaleOrigin;
    private Integer idReceivableOrigin;

    public Integer getIdCredit() {
        return idCredit;
    }

    public void setIdCredit(Integer idCredit) {
        this.idCredit = idCredit;
    }

    public Integer getIdCancelationCredit() {
        return idCancelationCredit;
    }

    public void setIdCancelationCredit(Integer idCancelationCredit) {
        this.idCancelationCredit = idCancelationCredit;
    }

    public Integer getIdBranchOrigin() {
        return idBranchOrigin;
    }

    public void setIdBranchOrigin(Integer idBranchOrigin) {
        this.idBranchOrigin = idBranchOrigin;
    }

    public Integer getAmmount() {
        return ammount;
    }

    public void setAmmount(Integer ammount) {
        this.ammount = ammount;
    }

    public String getBranchDocument() {
        return branchDocument;
    }

    public void setBranchDocument(String branchDocument) {
        this.branchDocument = branchDocument;
    }

    public Integer getIdSaleOrigin() {
        return idSaleOrigin;
    }

    public void setIdSaleOrigin(Integer idSaleOrigin) {
        this.idSaleOrigin = idSaleOrigin;
    }

    public Integer getIdReceivableOrigin() {
        return idReceivableOrigin;
    }

    public void setIdReceivableOrigin(Integer idReceivableOrigin) {
        this.idReceivableOrigin = idReceivableOrigin;
    }
}

package negocio.comuns.acesso.integracao.member.financeiro;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.contrato.ContratoVO;
import org.checkerframework.checker.units.qual.C;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SaleItemApiViewTO {
    private Integer idSaleItem;
    private String description;
    private String item;
    private Double itemValue;
    private Double saleValue;
    private Double saleValueWithoutCreditValue;
    private Integer quantity;
    private Integer idMembership;
    private Integer idMembershipRenewed;
    private Integer IdMemberMembership;
    private Integer numMembers;
    private Integer idProduct;
    private Integer idService;
    private String membershipStartDate;
    private Double discount;
    private Double corporateDiscount;
    private Double tax;
    private String voucher;
    private String accountingCode;
    private String municipalServiceCode;
    private boolean flReceiptOnly;
    private String idSaleItemMigration;
    private boolean flSwimming;
    private boolean flAllowLocker;

    public Integer getIdSaleItem() {
        return idSaleItem;
    }

    public void setIdSaleItem(Integer idSaleItem) {
        this.idSaleItem = idSaleItem;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public Double getItemValue() {
        return itemValue;
    }

    public void setItemValue(Double itemValue) {
        this.itemValue = itemValue;
    }

    public Double getSaleValue() {
        return saleValue;
    }

    public void setSaleValue(Double saleValue) {
        this.saleValue = saleValue;
    }

    public Double getSaleValueWithoutCreditValue() {
        return saleValueWithoutCreditValue;
    }

    public void setSaleValueWithoutCreditValue(Double saleValueWithoutCreditValue) {
        this.saleValueWithoutCreditValue = saleValueWithoutCreditValue;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getIdMembership() {
        return idMembership;
    }

    public void setIdMembership(Integer idMembership) {
        this.idMembership = idMembership;
    }

    public Integer getIdMembershipRenewed() {
        return idMembershipRenewed;
    }

    public void setIdMembershipRenewed(Integer idMembershipRenewed) {
        this.idMembershipRenewed = idMembershipRenewed;
    }

    public Integer getIdMemberMembership() {
        return IdMemberMembership;
    }

    public void setIdMemberMembership(Integer idMemberMembership) {
        IdMemberMembership = idMemberMembership;
    }

    public Integer getNumMembers() {
        return numMembers;
    }

    public void setNumMembers(Integer numMembers) {
        this.numMembers = numMembers;
    }

    public Integer getIdProduct() {
        return idProduct;
    }

    public void setIdProduct(Integer idProduct) {
        this.idProduct = idProduct;
    }

    public Integer getIdService() {
        return idService;
    }

    public void setIdService(Integer idService) {
        this.idService = idService;
    }

    public String getMembershipStartDate() {
        return membershipStartDate;
    }

    public void setMembershipStartDate(String membershipStartDate) {
        this.membershipStartDate = membershipStartDate;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Double getCorporateDiscount() {
        return corporateDiscount;
    }

    public void setCorporateDiscount(Double corporateDiscount) {
        this.corporateDiscount = corporateDiscount;
    }

    public Double getTax() {
        return tax;
    }

    public void setTax(Double tax) {
        this.tax = tax;
    }

    public String getVoucher() {
        return voucher;
    }

    public void setVoucher(String voucher) {
        this.voucher = voucher;
    }

    public String getAccountingCode() {
        return accountingCode;
    }

    public void setAccountingCode(String accountingCode) {
        this.accountingCode = accountingCode;
    }

    public String getMunicipalServiceCode() {
        return municipalServiceCode;
    }

    public void setMunicipalServiceCode(String municipalServiceCode) {
        this.municipalServiceCode = municipalServiceCode;
    }

    public boolean isFlReceiptOnly() {
        return flReceiptOnly;
    }

    public void setFlReceiptOnly(boolean flReceiptOnly) {
        this.flReceiptOnly = flReceiptOnly;
    }

    public String getIdSaleItemMigration() {
        return idSaleItemMigration;
    }

    public void setIdSaleItemMigration(String idSaleItemMigration) {
        this.idSaleItemMigration = idSaleItemMigration;
    }

    public boolean isFlSwimming() {
        return flSwimming;
    }

    public void setFlSwimming(boolean flSwimming) {
        this.flSwimming = flSwimming;
    }

    public boolean isFlAllowLocker() {
        return flAllowLocker;
    }

    public void setFlAllowLocker(boolean flAllowLocker) {
        this.flAllowLocker = flAllowLocker;
    }

}

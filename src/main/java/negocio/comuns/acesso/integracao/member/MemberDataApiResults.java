package negocio.comuns.acesso.integracao.member;

import negocio.comuns.acesso.integracao.member.financeiro.ReceivablesViewTO;
import negocio.comuns.acesso.integracao.member.financeiro.SalesApiViewTO;

import java.util.List;

public class MemberDataApiResults {

    private Integer idMember;
    private MembersApiViewTO membersApiViewTO;
    private List<SalesApiViewTO> salesApiViewTOS;
    private List<Integer> idsSales;
    List<ReceivablesViewTO> receivablesViewTOS;

    public Integer getIdMember() {
        return idMember;
    }

    public void setIdMember(Integer idMember) {
        this.idMember = idMember;
    }

    public MembersApiViewTO getMembersApiViewTO() {
        return membersApiViewTO;
    }

    public void setMembersApiViewTO(MembersApiViewTO membersApiViewTO) {
        this.membersApiViewTO = membersApiViewTO;
    }

    public List<SalesApiViewTO> getSalesApiViewTOS() {
        return salesApiViewTOS;
    }

    public void setSalesApiViewTOS(List<SalesApiViewTO> salesApiViewTOS) {
        this.salesApiViewTOS = salesApiViewTOS;
    }

    public List<Integer> getIdsSales() {
        return idsSales;
    }

    public void setIdsSales(List<Integer> idsSales) {
        this.idsSales = idsSales;
    }

    public List<ReceivablesViewTO> getReceivablesViewTOS() {
        return receivablesViewTOS;
    }

    public void setReceivablesViewTOS(List<ReceivablesViewTO> receivablesViewTOS) {
        this.receivablesViewTOS = receivablesViewTOS;
    }
}

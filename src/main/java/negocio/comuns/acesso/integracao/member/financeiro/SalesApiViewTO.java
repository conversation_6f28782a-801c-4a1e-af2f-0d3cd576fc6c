package negocio.comuns.acesso.integracao.member.financeiro;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
public class SalesApiViewTO {
    private Integer idSale;
    private Integer idMember;
    private Integer idEmployee;
    private Integer idProspect;
    private Integer idEmployeeSale;
    private String saleDate;
    private String saleDateServer;
    private Integer idPersonal;
    private boolean removed;
    private Integer idEmployeeRemoval;
    private String removalDate;
    private Integer idBranch;
    private String observations;
    private Integer idSaleRecurrency;
    private Integer saleSource;
    private String idSaleMigration;
    private List<SaleItemApiViewTO> saleItens;
    private List<ReceivablesViewTO> receivables;
    private JSONObject dataJson;

    public Integer getIdSale() {
        return idSale;
    }

    public void setIdSale(Integer idSale) {
        this.idSale = idSale;
    }

    public Integer getIdMember() {
        return idMember;
    }

    public void setIdMember(Integer idMember) {
        this.idMember = idMember;
    }

    public Integer getIdEmployee() {
        return idEmployee;
    }

    public void setIdEmployee(Integer idEmployee) {
        this.idEmployee = idEmployee;
    }

    public Integer getIdProspect() {
        return idProspect;
    }

    public void setIdProspect(Integer idProspect) {
        this.idProspect = idProspect;
    }

    public Integer getIdEmployeeSale() {
        return idEmployeeSale;
    }

    public void setIdEmployeeSale(Integer idEmployeeSale) {
        this.idEmployeeSale = idEmployeeSale;
    }

    public String getSaleDate() {
        return saleDate;
    }

    public void setSaleDate(String saleDate) {
        this.saleDate = saleDate;
    }

    public String getSaleDateServer() {
        return saleDateServer;
    }

    public void setSaleDateServer(String saleDateServer) {
        this.saleDateServer = saleDateServer;
    }

    public Integer getIdPersonal() {
        return idPersonal;
    }

    public void setIdPersonal(Integer idPersonal) {
        this.idPersonal = idPersonal;
    }

    public boolean isRemoved() {
        return removed;
    }

    public void setRemoved(boolean removed) {
        this.removed = removed;
    }

    public Integer getIdEmployeeRemoval() {
        return idEmployeeRemoval;
    }

    public void setIdEmployeeRemoval(Integer idEmployeeRemoval) {
        this.idEmployeeRemoval = idEmployeeRemoval;
    }

    public String getRemovalDate() {
        return removalDate;
    }

    public void setRemovalDate(String removalDate) {
        this.removalDate = removalDate;
    }

    public Integer getIdBranch() {
        return idBranch;
    }

    public void setIdBranch(Integer idBranch) {
        this.idBranch = idBranch;
    }

    public String getObservations() {
        return observations;
    }

    public void setObservations(String observations) {
        this.observations = observations;
    }

    public Integer getIdSaleRecurrency() {
        return idSaleRecurrency;
    }

    public void setIdSaleRecurrency(Integer idSaleRecurrency) {
        this.idSaleRecurrency = idSaleRecurrency;
    }

    public Integer getSaleSource() {
        return saleSource;
    }

    public void setSaleSource(Integer saleSource) {
        this.saleSource = saleSource;
    }

    public String getIdSaleMigration() {
        return idSaleMigration;
    }

    public void setIdSaleMigration(String idSaleMigration) {
        this.idSaleMigration = idSaleMigration;
    }

    public JSONObject getDataJson() {
        return dataJson;
    }

    public void setDataJson(JSONObject dataJson) {
        this.dataJson = dataJson;
    }

    public List<SaleItemApiViewTO> getSaleItens() {
        if (saleItens == null) {
            saleItens = new ArrayList<>();
        }
        return saleItens;
    }

    public void setSaleItens(List<SaleItemApiViewTO> saleItens) {
        this.saleItens = saleItens;
    }

    public List<ReceivablesViewTO> getReceivables() {
        if (receivables == null) {
            receivables = new ArrayList<>();
        }
        return receivables;
    }

    public void setReceivables(List<ReceivablesViewTO> receivables) {
        this.receivables = receivables;
    }

    public Double getSumItens() {
        Double soma = 0.0;
        for (SaleItemApiViewTO si: getSaleItens()) {
            soma += si.getItemValue() * (UteisValidacao.emptyNumber(si.getQuantity()) ? 1 : si.getQuantity());
        }
        return soma;
    }
}

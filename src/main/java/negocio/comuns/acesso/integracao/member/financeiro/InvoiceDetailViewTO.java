package negocio.comuns.acesso.integracao.member.financeiro;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class InvoiceDetailViewTO {

    private String invoiceNumber;
    private Double issuedAmount;
    private String status;
    private String sendDate;
    private String canceledDate;
    private String urlPdf;
    private Integer idInvoiceType;
    private String invoiceType;

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public Double getIssuedAmount() {
        return issuedAmount;
    }

    public void setIssuedAmount(Double issuedAmount) {
        this.issuedAmount = issuedAmount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSendDate() {
        return sendDate;
    }

    public void setSendDate(String sendDate) {
        this.sendDate = sendDate;
    }

    public String getCanceledDate() {
        return canceledDate;
    }

    public void setCanceledDate(String canceledDate) {
        this.canceledDate = canceledDate;
    }

    public String getUrlPdf() {
        return urlPdf;
    }

    public void setUrlPdf(String urlPdf) {
        this.urlPdf = urlPdf;
    }

    public Integer getIdInvoiceType() {
        return idInvoiceType;
    }

    public void setIdInvoiceType(Integer idInvoiceType) {
        this.idInvoiceType = idInvoiceType;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }
}

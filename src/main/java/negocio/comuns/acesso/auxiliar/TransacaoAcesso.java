/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso.auxiliar;

import java.util.Date;
import java.util.TimeZone;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.basico.enumerador.TimeZoneEnum;

/**
 *
 * <AUTHOR>
 */
public class TransacaoAcesso {

    private Date dataAcesso = negocio.comuns.utilitarias.Calendario.hoje(TimeZoneEnum.Brazil_East.getId());
    private String dataLimiteAcesso = "Indefinido";
    private PessoaAcesso cliente = new PessoaAcesso();
    private CodigoAcesso cartao = new CodigoAcesso();
    private LocalAcessoVO local = new LocalAcessoVO();
    private ColetorVO coletor = new ColetorVO();
    private SituacaoAcessoEnum resultado;
    private DirecaoAcessoEnum acessoEsperado;
    private String msgAcesso = "Aguardando registro de acesso...";
    private TimeZone tz = TimeZone.getTimeZone(TimeZoneEnum.Brazil_East.getId());
    private boolean forcarCodigoAlternativo = false;
    private String ticket = "";
    private int permanencia = 0;

    private int duracaoContrato = 0;

    public void setColetor(ColetorVO coletor) {
        this.coletor = coletor;
    }

    public ColetorVO getColetor() {
        return coletor;
    }

    public String getDataLimiteAcesso() {
        return dataLimiteAcesso;
    }

    public String getMsgAcesso() {
        return msgAcesso;
    }

    public void setMsgAcesso(String msgAcesso) {
        this.msgAcesso = msgAcesso;
    }

    public void setDataLimiteAcesso(String dataLimiteAcesso) {
        this.dataLimiteAcesso = dataLimiteAcesso;
    }

    public CodigoAcesso getCartao() {
        return cartao;
    }

    public void setCartao(CodigoAcesso cartao) {
        this.cartao = cartao;
    }

    public PessoaAcesso getCliente() {
        return cliente;
    }

    public void setCliente(PessoaAcesso cliente) {
        this.cliente = cliente;
    }

    public Date getDataAcesso() {
        return dataAcesso;
    }

    public void setDataAcesso(Date dataAcesso) {
        this.dataAcesso = dataAcesso;
    }

    public LocalAcessoVO getLocal() {
        return local;
    }

    public void setLocal(LocalAcessoVO local) {
        this.local = local;
    }

    public SituacaoAcessoEnum getResultado() {
        return resultado;
    }

    public void setResultado(SituacaoAcessoEnum resultado) {
        this.resultado = resultado;
    }

    public DirecaoAcessoEnum getAcessoEsperado() {
        return acessoEsperado;
    }

    public void setAcessoEsperado(DirecaoAcessoEnum acessoEsperado) {
        this.acessoEsperado = acessoEsperado;
    }

    public TimeZone getTz() {
        return tz;
    }

    public void setTz(TimeZone tz) {
        this.tz = tz;
    }

    public boolean isForcarCodigoAlternativo() {
        return forcarCodigoAlternativo;
    }

    public void setForcarCodigoAlternativo(boolean forcarCodigoAlternativo) {
        this.forcarCodigoAlternativo = forcarCodigoAlternativo;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public int getPermanencia() {
        return permanencia;
    }

    public void setPermanencia(int permanencia) {
        this.permanencia = permanencia;
    }

    public int getDuracaoContrato() {
        return duracaoContrato;
    }

    public void setDuracaoContrato(int duracaoContrato) {
        this.duracaoContrato = duracaoContrato;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso.auxiliar;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Calendario;

/**
 *
 * <AUTHOR>
 */
public class PessoaAcesso {

    private Integer codigo;
    private String matricula;
    private Integer empresa;
    private PessoaVO pessoa;
    private String mensagem;
    private String categoria;
    private String situacao;
    private String uaSentido = "";
    private Date uaData = null;
    private Integer freePass;
    private String gympassUniqueToken = "";
    private String gympassTypeNumber = "";

    private String ticket = null;
    private int permanencia = 0;

    private String codAcessoTitularPlanoCompartilhado;
    private List<String> chavesEmpresasPermiteAcesso;

    PessoaAcesso() {
        setCodigo(0);

        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setNome("Não identificado");

        setPessoa(pessoaVO);
        setMatricula("XXX");
        setCategoria("Não identificada");
        setMensagem("");
        setSituacao("");
        setFreePass(0);
    }

    public boolean getAniversarioHj() {

        if (pessoa.getDataNasc() == null) {
            return false;
        }

        Calendar dtNasc = Calendario.getInstance();
        dtNasc.setTime(pessoa.getDataNasc());

        Calendar dtHoje = Calendario.getInstance();

        int diaNiver = dtNasc.get(Calendar.DAY_OF_MONTH);
        int diaHj    = dtHoje.get(Calendar.DAY_OF_MONTH);

        int mesNiver = dtNasc.get(Calendar.MONTH);
        int mesHj    = dtHoje.get(Calendar.MONTH);

        return (diaHj == diaNiver) && (mesHj == mesNiver);
    }

    public String getMatricula() {
        if (matricula == null) {
            return "";
        } else {
            return matricula;
        }
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getMensagem() {
        if (mensagem == null) {
            return "";
        } else {
            return mensagem;
        }
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCategoria() {
        if (categoria == null) {
            return "";
        } else {
            return categoria;
        }
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getSituacao() {
        if (situacao == null) {
            return "";
        } else {
            return situacao;
        }
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getUaSentido() {
        return uaSentido;
    }

    public void setUaSentido(String uaSentido) {
        this.uaSentido = uaSentido;
    }

    public Date getUaData() {
        return uaData;
    }

    public void setUaData(Date uaData) {
        this.uaData = uaData;
    }

    public Integer getFreePass() {
        return freePass;
    }

    public void setFreePass(Integer freePass) {
        this.freePass = freePass;
    }


    public String getGympassUniqueToken() {
        return gympassUniqueToken;
    }

    public void setGympassUniqueToken(String gympassUniqueToken) {
        this.gympassUniqueToken = gympassUniqueToken;
    }

    public String getGympassTypeNumber() {
        return gympassTypeNumber;
    }

    public void setGympassTypeNumber(String gympassTypeNumber) {
        this.gympassTypeNumber = gympassTypeNumber;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public int getPermanencia() {
        return permanencia;
    }

    public void setPermanencia(int permanencia) {
        this.permanencia = permanencia;
    }

    public String getCodAcessoTitularPlanoCompartilhado() {
        return codAcessoTitularPlanoCompartilhado;
    }

    public void setCodAcessoTitularPlanoCompartilhado(String codAcessoTitularPlanoCompartilhado) {
        this.codAcessoTitularPlanoCompartilhado = codAcessoTitularPlanoCompartilhado;
    }

    public List<String> getChavesEmpresasPermiteAcesso() {
        if (chavesEmpresasPermiteAcesso == null) {
            chavesEmpresasPermiteAcesso = new ArrayList<>();
        }
        return chavesEmpresasPermiteAcesso;
    }

    public void setChavesEmpresasPermiteAcesso(List<String> chavesEmpresasPermiteAcesso) {
        this.chavesEmpresasPermiteAcesso = chavesEmpresasPermiteAcesso;
    }
}



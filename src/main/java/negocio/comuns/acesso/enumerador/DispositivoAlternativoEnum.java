/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso.enumerador;

/**
 * <AUTHOR>
 */
public enum DispositivoAlternativoEnum {

    NENHUM_DISP(0, "Nenhum"),
    CODIGO_BARRAS(1, "Código de Barras"),
    APROXIMACAO(2, "Aproximação"),
    MIFARE(3, "MiFare"),
    APROXIMACAO_E_CODIGO_BARRAS(4, "Aproximação e Código de Barras"),
    APROXIMACAO_E_URNA(5, "Aproximação e Urna");
    private Integer id;
    private String descricao;

    DispositivoAlternativoEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public static DispositivoAlternativoEnum getEnum(final int codigo) {
        for (DispositivoAlternativoEnum tipo : DispositivoAlternativoEnum.values()) {
            if (tipo.getId() == codigo) {
                return tipo;
            }
        }
        return DispositivoAlternativoEnum.NENHUM_DISP;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}

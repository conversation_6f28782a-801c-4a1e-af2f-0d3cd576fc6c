/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum SentidoAcessoEnum {

    SENTIDOACESSO_COLETOR_ENTRADA("SENTIDOACESSO_COLETOR_ENTRADA", "Entrada"),
    SENTIDOACESSO_COLETOR_SAIDA("SENTIDOACESSO_COLETOR_SAIDA", "Saí<PERSON>"),
    SENTIDOACESSO_COLETOR_INDIFERENTE("SENTIDOACESSO_COLETOR_INDIFERENTE", "Entrada e Saída (Indiferente)"),
    SENTIDOACESSO_COLETOR_OESPERADO("SENTIDOACESSO_COLETOR_OESPERADO", "Esperado pelo sistema");
    
    private String id;
    private String descricao;

    private SentidoAcessoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso.enumerador;

import org.json.JSONString;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum SituacaoAcessoEnum implements J<PERSON>NString {
    RV_BLOQRESTRICAOACESSO("RV_BLOQRESTRICAOACESSO", "Acesso restrito a alunos dessa academia.", "ALUNO DE OUTRA UNIDADE", "B"),
    RV_BLOQALUNONAOCADASTRADO("RV_BLOQALUNONAOCADASTRADO", "Cartão de aluno não cadastrado.", "NAO CADASTRADO A", "B"),
    RV_BLOQALUNOMATNAOCADASTRADO("RV_BLOQALUNOMATNAOCADASTRADO", "Matrícula de aluno não cadastrada.", "NAO CADASTRADO A", "B"),
    RV_BLOQCOLABORADORNAOCADASTRADO("RV_BLOQCOLABORADORNAOCADASTRADO", "Cartão de colaborador não cadastrado.", "NAO CADASTRADO C", "B"),
    RV_BLOQCOLABORADORCODNAOCADASTRADO("RV_BLOQCOLABORADORCODNAOCADASTRADO", "Código de colaborador não cadastrado.", "NAO CADASTRADO C", "B"),
    RV_BLOQEMPRESANAOCONFERE("RV_BLOQEMPRESANAOCONFERE", "Cartão inválido para esta empresa.", "NAO ACESSA EMP", "B"),
    RV_BLOQTAMCARTAOINVALIDO("RV_BLOQTAMCARTAOINVALIDO", "Transação inválida pelo tamanho", "TRANS. INVALIDA", "B"),
    RV_BLOQFORAHORARIO("RV_BLOQFORAHORARIO", "Fora do horário do Plano.", "FORA HORARIO PLANO", "B"),
    RV_BLOQFORAHORARIOGYMPASS("RV_BLOQFORAHORARIOGYMPASS", "Acesso gympass fora do horário permitido", "FORA HORARIO GYMPASS", "B"),
    RV_BLOQTOKENGYMPASSINVALIDO("RV_BLOQTOKENGYMPASSINVALIDO", "Token gympass inválido", "TOKEN GYMPASS INVALIDO", "B"),
    RV_BLOQFORAHORARIOTURMA("RV_BLOQFORAHORARIOTURMA", "Fora do horário da turma.", "FORA HORARIO TURMA", "B"),
    RV_BLOQCONTRATOTRANCADO("RV_BLOQCONTRATOTRANCADO", "Contrato trancado.", "TRANCADO", "B"),
    RV_BLOQCONTRATOFERIAS("RV_BLOQCONTRATOFERIAS", "Contrato em férias.", "EM FERIAS", "B"),
    RV_BLOQCONTATOVENCIDO("RV_BLOQCONTATOVENCIDO", "Contrato vencido.", "CONTRATO VENCIDO", "B"),
    RV_BLOQCONTRATONAOINICIOU("RV_BLOQCONTRATONAOINICIOU", "Contrato ainda não iniciado.", "NAO INICIADO", "B"),
    RV_BLOQEXAMEVENCIDO("RV_BLOQEXAMEVENCIDO", "Avaliação ou exame médico vencido.", "VER EXAME/AVALIACAO", "B"),
    RV_BLOQMSGPERSONALIZADA("RV_BLOQMSGPERSONALIZADA", "Bloqueio por mensagem personalizada.", "BLOQ. POR MENSAGEM", "B"),
    RV_BLOQACESSOSSEGUIDOS("RV_BLOQACESSOSSEGUIDOS", "Bloqueio por acessos seguidos.", "ACESSOS SEGUIDOS", "B"),
    RV_BLOQCONTRATOATESTADOM("RV_BLOQCONTRATOATESTADOM", "Contrato em atestado médico.", "EM ATESTADO", "B"),
    RV_BLOQSTATUSALUNO("RV_BLOQSTATUSALUNO", "Aluno não encontra-se ativo.", "BLOQ - STATUS", "B"),
    RV_BLOQSEMAUTORIZACAO("RV_BLOQSEMAUTORIZACAO", "Aluno não possui autorização de acesso.", "BLOQ - AUTORIZACAO", "B"),
    RV_BLOQPLANOEMPRESA("RV_BLOQPLANOEMPRESA", "O plano do aluno não permite acesso a essa unidade.", "BLOQ - PLANO EMPRESA", "B"),
    RV_BLOQDVNAOCONFERE("RV_BLOQDVNAOCONFERE", "Dígito verificador não confere.", "BLOQ - DIGITO VERIF.", "B"),
    RV_BLOQCAPACIDADEMAXIMA( "RV_BLOQCAPACIDADEMAXIMA", "Capacidade máxima atingida. Acesso não permitido.", "BLOQ - CAP. MAXIMA ATINGIDA", "B"),
    RV_LIBACESSOAUTORIZADO("RV_LIBACESSOAUTORIZADO", "Acesso autorizado", "ACESSO AUTORIZADO", "L"),
    RV_BLOQREGRA_LIBERACAO("RV_BLOQREGRA_LIBERACAO", "Bloqueio por regra de validação do terminal.", "BLOQ - REGRA VAL", "B"),
    RV_BLOQPERSONAL("RV_BLOQPERSONAL", "Verificar Controle do Personal.", "BLOQ - PERSONAL", "B"),
    RV_BLOQCOLABORADORINATIVO("RV_BLOQCOLABORADORINATIVO", "Colaborador Inativo.", "COLABORADOR IN C", "B"),
    RV_BLOQPESSOASENHAINVALIDA("RV_BLOQPESSOASENHAINVALIDA", "Senha inválida.", "SENHA INVALIDA", "B"),
    RV_BLOQALUNOPARCELAABERTA("RV_BLOQALUNOPARCELAABERTA", "Por favor, compareça à Recepção", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B"),
    RV_BLOQALUNOFREQUENCIAPLANO("RV_BLOQALUNOFREQUENCIAPLANO", "Quantidade máxima de frequência atingida", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B"),
    RV_BLOQCARTEIRINHAVENCIDA("RV_BLOQCARTEIRINHAVENCIDA", "Carteirinha vencida.", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B"),

    //TOTALPASS
    RV_LIBACESSOAUTORIZADOTOTALPASS("RV_LIBACESSOAUTORIZADOTOTALPASS", "Acesso autorizado por TotalPass", "ACESSO AUTORIZADO TOTAL PASS", "L"),
    RV_BLOQTOTALPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO("RV_BLOQTOTALPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO", "Limite de acessos diários TotalPass atingido", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 49),
    RV_BLOQTOTALPASS_NESCESSARIO_CHECKIN_TOTALPASS("RV_BLOQTOTALPASS_NESCESSARIO_CHECKIN_TOTALPASS", "Realize o Check-in no aplicativo TotalPass", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 50),


    // GYMPASS
    RV_LIBACESSOAUTORIZADOGYMPASS("RV_LIBACESSOAUTORIZADOGYMPASS", "Acesso autorizado por Gympass", "ACESSO AUTORIZADO GYMPASS", "L"),
    RV_BLOQGYMPASS5("RV_BLOQGYMPASS5",  "O usuário já visitou esta academia hoje", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 5),
    RV_BLOQGYMPASS11("RV_BLOQGYMPASS11", "Token Diário Inválido", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 11),
    RV_BLOQGYMPASS12("RV_BLOQGYMPASS12", "Validador não autorizado. Um passe só é válido na academia onde foi comprado", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 12),
    RV_BLOQGYMPASS13("RV_BLOQGYMPASS13", "Token já foi usado hoje", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 13),
    RV_BLOQGYMPASS14("RV_BLOQGYMPASS14", "Item não está disponível devido a problemas com o pagamento", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 14),
    RV_BLOQGYMPASS15("RV_BLOQGYMPASS15", "Passe já foi completamente usado", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 15),
    RV_BLOQGYMPASS16("RV_BLOQGYMPASS16", "Passe já expirou e não pode mais ser usado", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 16),
    RV_BLOQGYMPASS21("RV_BLOQGYMPASS21", "Número de Token Diário Inválido", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 21),
    RV_BLOQGYMPASS22("RV_BLOQGYMPASS22", "Cartão não habilitado", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 22),
    RV_BLOQGYMPASS23("RV_BLOQGYMPASS23", "O aluno não fez checkin", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 23),
    RV_BLOQGYMPASS24("RV_BLOQGYMPASS24", "Sem permissão para validar passes diários", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 24),
    RV_BLOQGYMPASS26("RV_BLOQGYMPASS26", "Não há créditos", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 26),
    RV_BLOQGYMPASS27("RV_BLOQGYMPASS27", "Pessoa bloqueada", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 27),
    RV_BLOQGYMPASS28("RV_BLOQGYMPASS28", "Erro ao aprovar o cartão bancário", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 28),
    RV_BLOQGYMPASS29("RV_BLOQGYMPASS29", "Cartão desabilitado", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 29),
    RV_BLOQGYMPASS30("RV_BLOQGYMPASS30", "Cartão expirado", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 30),
    RV_BLOQGYMPASS32("RV_BLOQGYMPASS32", "Esta pessoa não tem passes disponíveis para essa academia", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 32),
    RV_BLOQGYMPASS33("RV_BLOQGYMPASS33", "Academia bloqueada", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 33),
    RV_BLOQGYMPASS34("RV_BLOQGYMPASS34", "Token diário desativado", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 34),
    RV_BLOQGYMPASS35("RV_BLOQGYMPASS35", "Token Diário expirou", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 35),
    RV_BLOQGYMPASS38("RV_BLOQGYMPASS38", "Pessoa não está na lista de permitidos para essa academia", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 38),
    RV_BLOQGYMPASS39("RV_BLOQGYMPASS39", "Número máximo permitido de vezes na semana foi excedido", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 39),
    RV_BLOQGYMPASS40("RV_BLOQGYMPASS40", "Número máximo permitido de vezes este mês foi excedido", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 40),
    RV_BLOQGYMPASS41("RV_BLOQGYMPASS41", "Nenhuma reserva foi encontrada para esta pessoa", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 41),
    RV_BLOQGYMPASS42("RV_BLOQGYMPASS42", "É muito cedo para validar esta reserva", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 42),
    RV_BLOQGYMPASS43("RV_BLOQGYMPASS43", "É tarde demais para validar esta reserva", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 43),
    RV_BLOQGYMPASS45("RV_BLOQGYMPASS45", "O usuário ainda não fez check-in", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 45),
    RV_BLOQGYMPASS46("RV_BLOQGYMPASS46", "Usuário fez check-in em outra academia", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 46),
    RV_BLOQGYMPASS47("RV_BLOQGYMPASS47", "User Check In para essa academia expirou", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 47),
    RV_BLOQGYMPASSGENERICO("RV_BLOQGYMPASSGENERICO", "Token Gympass não foi validado", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 48),
    RV_GYMPASS_AGUARDANDO_RESPOSTA("RV_GYMPASS_AGUARDANDO_RESPOSTA", "Aguardando resposta da Gympass", "AGUARDANDO RESPOSTA GYMPASS", "B"),
    RV_BLOQGYMPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO("RV_BLOQGYMPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO", "Limite de acessos diários Gympass atingido", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B", 47),
    RV_BLOQALUNOSEMASSINATURA("RV_BLOQALUNOSEMASSINATURA", "Verificar Assinatura Digital", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B"),

    RV_BLOQALUNOCAPACIDADESIMULTANEA("RV_BLOQALUNOCAPACIDADESIMULTANEA", "Academia está lotada", "ACADEMIA EM CAPACIDADE MAXIMA (COVID-19)", "B"),
    RV_BLOQSEMCARTAOVACINA("RV_BLOQSEMCARTAOVACINA", "Sem comprovante de vacinação apresentado", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B"),

    RV_BLOQALUNOSEMPARQASSINADO("RV_BLOQALUNOSEMPARQASSINADO", "Aluno sem Par-Q assinado", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B"),

    RV_BLOQCREFVENCIDO("RV_BLOQCREFVENCIDO", "CREF do colaborador está vencido", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B"),

    RV_BLOQPLANOEMPRESA_PERSONAL("RV_BLOQPLANOEMPRESA_PERSONAL", "O plano do personal não permite acesso a essa unidade.", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B"),
    RV_BLOQALUNOSEMTERMORESPONSABILIDADE("RV_BLOQALUNOSEMTERMORESPONSABILIDADE", "Aluno sem Termo de Responsabilidade assinado.", SituacaoAcessoEnum.COMPARECA_A_RECEPCAO, "B");

    private final String id;
    private final String descricao;
    private final String msgColetor;
    private final String bloqueadoLiberado;
    private Integer codigo;

    private static final String COMPARECA_A_RECEPCAO = "COMPARECA A RECEP";

    SituacaoAcessoEnum(String id, String descricao, String msgColetor, String bloqueadoLiberado) {
        this.id = id;
        this.descricao = descricao;
        this.msgColetor = msgColetor;
        this.bloqueadoLiberado = bloqueadoLiberado;
    }

    SituacaoAcessoEnum(String id, String descricao, String msgColetor, String bloqueadoLiberado, Integer codigo) {
        this(id, descricao, msgColetor, bloqueadoLiberado);
        this.codigo = codigo;
    }

    public static SituacaoAcessoEnum consultarPorCodigo(Integer codigo){
        for (SituacaoAcessoEnum item : SituacaoAcessoEnum.values()) {
            if (item.getCodigo() != null && item.getCodigo().equals(codigo)) {
                return item;
            }
        }
        return SituacaoAcessoEnum.RV_BLOQGYMPASSGENERICO;
    }

    public static SituacaoAcessoEnum consultarPorId(String id){
        for (SituacaoAcessoEnum item : SituacaoAcessoEnum.values()) {
            if (item.getId() != null && item.getId().equals(id)) {
                return item;
            }
        }
        return SituacaoAcessoEnum.RV_BLOQGYMPASSGENERICO;
    }

    public static String consultarSituacoesAcessoGympassBloqueio(){
        StringBuilder ids = new StringBuilder();

        for (SituacaoAcessoEnum item : SituacaoAcessoEnum.values()) {
            if (item.getId() != null && item.getId().contains("RV_BLOQGYMPASS")) {
                ids.append("'").append(item.getId()).append("',");
            }
        }

        ids = new StringBuilder(ids.substring(0, ids.length() - 1));

        return ids.toString();
    }

    public static List<SituacaoAcessoEnum> consultarSituacoesLiberacao() {
        List<SituacaoAcessoEnum> situacoes = new ArrayList<>();

        for (SituacaoAcessoEnum situacao: SituacaoAcessoEnum.values()) {
            if(situacao.getId().contains("RV_LIBACESSO")){
                situacoes.add(situacao);
            }
        }

        return situacoes;
    }

    public static String consultarSituacoesLiberacaoIds() {
        StringBuilder ids = new StringBuilder();
        int index = 1;
        List<SituacaoAcessoEnum> situacoesLiberacao = SituacaoAcessoEnum.consultarSituacoesLiberacao();
        for (SituacaoAcessoEnum situacao : situacoesLiberacao) {
            if (index == situacoesLiberacao.size()) {
                ids.append("'").append(situacao).append("'");
            } else {
                ids.append("'").append(situacao).append("', ");
            }
            index++;
        }

        return ids.toString();
    }

    public String getDescricao() {
        return descricao;
    }

    public String getId() {
        return id;
    }

    public String getMsgColetor() {
        return msgColetor;
    }

    public String getBloqueadoLiberado() {
        return bloqueadoLiberado;
    }

    public boolean isBloqueado() {
        return "B".equals(bloqueadoLiberado);
    }

    public boolean isLiberado() {
        return "L".equals(bloqueadoLiberado);
    }

    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public String toJSONString() {
        return "\"" + this + "\"";
    }
}

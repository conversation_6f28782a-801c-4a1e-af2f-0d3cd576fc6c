package negocio.comuns.acesso.enumerador;

public enum CategoriaLocalAcessoEnum {
    CADASTRO("Cadastros"),
    AULAS("Aulas");

    private String descricao;

    CategoriaLocalAcessoEnum(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public static CategoriaLocalAcessoEnum getCategoria(final String categoria) {
        CategoriaLocalAcessoEnum tipoAmbiente = null;
        if (categoria != null) {
            for (CategoriaLocalAcessoEnum tipo : CategoriaLocalAcessoEnum.values()) {
                if (tipo.toString().equals(categoria.toUpperCase())) {
                    tipoAmbiente = tipo;
                }
            }
        }
        return tipoAmbiente;
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.acesso.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum PortaComunicacaoEnum {
    PORTACOMUNICACAO_COLETOR_SERIAL("Com", "COM"), PORTACOMUNICACAO_COLETOR_PARALELO("Lpt", "LPT"),
    PORTACOMUNICACAO_COLETOR_USB("Usb", "USB"), PORTACOMUNICACAO_COLETOR_TCP("Tcp", "TCP");
    
    private String id;
    private String descricao;

    private PortaComunicacaoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum DirecaoAcessoEnum {

    DA_ENTRADA("E", "Entrada"), DA_SAIDA("S", "Saída"),
    DA_INDEFINIDA("I", "Indefinido"), DA_OESPERADO("O", "O Esperado");
    private String id;
    private String descricao;

    private DirecaoAcessoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    /**
     * Busca o código em String do enumerador e retorna o enumerador
     *
     * @param codigo
     * @return direcaoAcessoEnum
     */
    public static DirecaoAcessoEnum getDirecaoAcessoEnum(final String codigo) {
        DirecaoAcessoEnum direcaoAcessoEnum = null;
        for (DirecaoAcessoEnum direcao : DirecaoAcessoEnum.values()) {
            if (direcao.getId().equals(codigo)) {
                direcaoAcessoEnum = direcao;
            }
        }
        return direcaoAcessoEnum;
    }
}

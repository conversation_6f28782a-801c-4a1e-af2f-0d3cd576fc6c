/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum ModoTransmissaoEnum {

    MODOTRANSMISSAO_COLETOR_HALFDUPLEX("MODOT<PERSON><PERSON>MISSAO_COLETOR_HALFDUPLEX", "Half-duplex"), MODOTRANSMISSAO_COLETOR_FULLDUPLEX("MODOTRANSMISSAO_COLETOR_FULLDUPLEX", "Full-duplex"),
    MODOTRANSMISSAO_COLETOR_AUTO("MODOTRANSMISSAO_COLETOR_AUTO", "Automático");
    private String id;
    private String descricao;

    private ModoTransmissaoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}

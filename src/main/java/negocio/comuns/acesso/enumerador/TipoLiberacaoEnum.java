package negocio.comuns.acesso.enumerador;

/**
 * Enumerador para identificar o tipo de pessoa que foi liberada no Acesso.
 * 
 * <AUTHOR>
 */
public enum TipoLiberacaoEnum {

    // Enumeradores
    CLIENTE(1, "Liberação de Cliente"),
    COLABORADOR(2, "Liberação de Colaborador"),
    TERCEIRIZADO(3, "Liberação de Terceirizado"),
    CLIENTE_VISITANTE(4, "Liberação de Cliente-Visitante"),
    VISITANTE_DIVERSO(5, "Liberação de Visitante Diverso"),
    NENHUM(6, "Nenhum"),
    OUTROS(7, "A Definir...");
    // Atributos
    private Integer codigo;
    private String descricao;

    // Métodos da Classe
    /**
     * Método que seta o código e descrição
     *
     * @param codigo
     * @param descricao
     */
    private TipoLiberacaoEnum(final Integer codigo, final String descricao) {
        this.setCodigo(codigo);
        this.setDescricao(descricao);
    }

    /**
     * Busca o código do enumerador e retorna o enumerador
     *
     * @param codigo
     * @return tipoLiberacao
     */
    public static TipoLiberacaoEnum getTipoLiberacao(final Integer codigo) {
        TipoLiberacaoEnum tipoLiberacao = null;
        for (TipoLiberacaoEnum tipo : TipoLiberacaoEnum.values()) {
            if (tipo.getCodigo().equals(codigo)) {
                tipoLiberacao = tipo;
            }
        }
        return tipoLiberacao;
    }

    // Getters and Setters
    /**
     * @return O campo codigo.
     */
    public Integer getCodigo() {
        return this.codigo;
    }

    /**
     * @param codigo
     *            O novo valor de codigo.
     */
    private void setCodigo(final Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return O campo descricao.
     */
    public String getDescricao() {
        return this.descricao;
    }

    /**
     * @param descricao
     *            O novo valor de descricao.
     */
    private void setDescricao(final String descricao) {
        this.descricao = descricao;
    }
}

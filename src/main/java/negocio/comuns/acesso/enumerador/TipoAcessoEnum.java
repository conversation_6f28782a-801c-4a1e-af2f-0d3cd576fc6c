/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum TipoAcessoEnum {

    TA_ALUNO("1", "Acesso de aluno"),
    TA_SOCIO("2", "Acesso de sócio"),
    TA_CONVIDADO("3", "Acesso de convidado"),
    TA_VISITANTE("4", "Acesso de visitante"),
    TA_COLABORADOR("5", "Acesso de colaborador"),
    TA_EXAMEMEDICO("6", "Exame médico"),
    AUTORIZADO("7", "Acesso de autorizado");

    private String id;
    private String descricao;

    private TipoAcessoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

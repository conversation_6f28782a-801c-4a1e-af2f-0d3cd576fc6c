/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.acesso;

import java.util.Comparator;

/**
 *
 * <AUTHOR>
 */
public class ComparatorValidacaoPorEmpresa implements Comparator<ValidacaoLocalAcessoVO> {
    // Comparator criado para ordenar as validações por empresa
    public int compare(ValidacaoLocalAcessoVO o1, ValidacaoLocalAcessoVO o2) {
        ValidacaoLocalAcessoVO d1 = (ValidacaoLocalAcessoVO) o1;
        ValidacaoLocalAcessoVO d2 = (ValidacaoLocalAcessoVO) o2;
        return d1.getEmpresa().getCodigo().intValue() < d2.getEmpresa().getCodigo().intValue() ? -1 : (d1.getEmpresa().getCodigo().intValue() > d2.getEmpresa().getCodigo().intValue() ? +1 : 0);
    }

}

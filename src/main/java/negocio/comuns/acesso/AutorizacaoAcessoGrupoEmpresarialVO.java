package negocio.comuns.acesso;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class AutorizacaoAcessoGrupoEmpresarialVO extends SuperVO implements Serializable {

    @ChaveEstrangeira
    private IntegracaoAcessoGrupoEmpresarialVO integracao = new IntegracaoAcessoGrupoEmpresarialVO();
    @ChaveEstrangeira
    private UsuarioVO usuarioResponsavel = new UsuarioVO();
    @ChavePrimaria
    private Integer codigo = 0;
    private EmpresaVO empresaRemota = new EmpresaVO();
    private String tipoPessoa = "";
    private String nomePessoa = "";
    private Integer codigoPessoa = 0;
    private String codAcesso ="";
    private Integer codigoMatricula = 0;
    private Map<String, Object> props = new HashMap<>();
    private EmpresaVO empresaLocal = new EmpresaVO();
    private Integer codigoGenerico = 0;
    private String codigoAutorizacao = "";
    private String senhaAcesso = "";
    private String codAcessoAlternativo = "";
    private String fotoKey = "";
    private String assinaturaBiometriaFacial;
    private String assinaturaBiometriaDigital;
    private String cpf;

    public AutorizacaoAcessoGrupoEmpresarialVO() {
    }

    public AutorizacaoAcessoGrupoEmpresarialVO(ClienteVO cliente) {
        this.codAcesso = "NU" + cliente.getCodAcesso();
        this.codAcessoAlternativo = cliente.getCodAcessoAlternativo();
        this.nomePessoa = cliente.getPessoa().getNome();
        this.codigoGenerico = cliente.getCodigo();
        this.codigoMatricula = cliente.getCodigoMatricula();
        this.senhaAcesso = cliente.getPessoa().getSenhaAcesso();
        this.codigoPessoa = cliente.getPessoa().getCodigo();
        this.fotoKey = cliente.getPessoa().getFotoKey();
        this.assinaturaBiometriaFacial = cliente.getPessoa().getAssinaturaBiometriaFacial();
        this.assinaturaBiometriaDigital = cliente.getPessoa().getAssinaturaBiometriaDigital();
        this.cpf = cliente.getPessoa().getCfp();
        this.tipoPessoa = TipoPessoaEnum.ALUNO.getTipo();
    }

    public AutorizacaoAcessoGrupoEmpresarialVO(ColaboradorVO colaborador) {
        this.codAcesso = "NU" + colaborador.getCodAcesso();
        this.codAcessoAlternativo = colaborador.getCodAcessoAlternativo();
        this.nomePessoa = colaborador.getPessoa().getNome();
        this.codigoGenerico = colaborador.getCodigo();
        this.senhaAcesso = colaborador.getPessoa().getSenhaAcesso();
        this.codigoPessoa = colaborador.getPessoa().getCodigo();
        this.fotoKey = colaborador.getPessoa().getFotoKey();
        this.assinaturaBiometriaFacial = colaborador.getPessoa().getAssinaturaBiometriaFacial();
        this.assinaturaBiometriaDigital = colaborador.getPessoa().getAssinaturaBiometriaDigital();
        this.cpf = colaborador.getPessoa().getCfp();
        this.tipoPessoa = TipoPessoaEnum.COLABORADOR.getTipo();
    }

    public String getCodAcesso() {
        return codAcesso;
    }

    public String getCodAcesso_Apresentar() {
//        return codAcesso.startsWith("NU") ? "" : codAcesso;
        return codAcesso;
    }

    public void setCodAcesso(String codAcesso) {
        this.codAcesso = codAcesso;
    }

    public String getCodAcessoAlternativo() {
        return codAcessoAlternativo;
    }

    public void setCodAcessoAlternativo(String codAcessoAlternativo) {
        this.codAcessoAlternativo = codAcessoAlternativo;
    }

    public String getCodigoAutorizacao() {
        return codigoAutorizacao;
    }

    public void setCodigoAutorizacao(String codigoAutorizacao) {
        this.codigoAutorizacao = codigoAutorizacao;
    }

    public Integer getCodigoGenerico() {
        return codigoGenerico;
    }

    public void setCodigoGenerico(Integer codigoGenerico) {
        this.codigoGenerico = codigoGenerico;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public EmpresaVO getEmpresaLocal() {
        return empresaLocal;
    }

    public void setEmpresaLocal(EmpresaVO empresaLocal) {
        this.empresaLocal = empresaLocal;
    }

    public EmpresaVO getEmpresaRemota() {
        return empresaRemota;
    }

    public void setEmpresaRemota(EmpresaVO empresaRemota) {
        this.empresaRemota = empresaRemota;
    }

    public IntegracaoAcessoGrupoEmpresarialVO getIntegracao() {
        return integracao;
    }

    public void setIntegracao(IntegracaoAcessoGrupoEmpresarialVO integracao) {
        this.integracao = integracao;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public Map<String, Object> getProps() {
        return props;
    }

    public void setProps(Map<String, Object> props) {
        this.props = props;
    }

    public String getSenhaAcesso() {
        return senhaAcesso;
    }

    public void setSenhaAcesso(String senhaAcesso) {
        this.senhaAcesso = senhaAcesso;
    }

    public String getTipoPessoa() {
        return tipoPessoa;
    }

    public void setTipoPessoa(String tipoPessoa) {
        this.tipoPessoa = tipoPessoa;
    }

    public List<TelefoneVO> getTelefones(){
        Object get = props.get("telefone");
        if(get != null){
            List<TelefoneVO> tels = (List<TelefoneVO>)get;
            return  tels;
        }
        return new ArrayList<>();
    }

    public List<EmailVO> getEmails(){
        Object get = props.get("email");
        if(get != null){
            return (List<EmailVO>)get;
        }
        return new ArrayList<>();
    }

    public Date getDataNasc(){
        Object get = props.get("datanasc");
        if(get != null){
            return (Date)get;
        }
        return null;
    }

    public String getDataNasc_Apresentar() {
        return (Uteis.getData(getDataNasc()));
    }

    public String getTel_Apresentar(){
        String nr = "";
        for(TelefoneVO tel : getTelefones()){
            nr += " / "+tel.getNumero();
        }
        return nr.replaceFirst(" / ", "");
    }

    public String getEmail_Apresentar(){
        String nr = "";
        for(EmailVO tel : getEmails()){
            nr += " / "+tel.getEmail();
        }
        return nr.replaceFirst(" / ", "");
    }


    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public static void validarDados(AutorizacaoAcessoGrupoEmpresarialVO obj) throws Exception{
    	if(UteisValidacao.emptyNumber(obj.getEmpresaLocal().getCodigo())){
    		throw new Exception("O campo EMPRESA LOCAL deve ser preenchido.");
    	}

        if(UteisValidacao.emptyNumber(obj.getEmpresaRemota().getCodigo())){
    		throw new Exception("O campo EMPRESA REMOTA deve ser preenchido.");
    	}
    	if(UteisValidacao.emptyString(obj.getNomePessoa())){
    		throw new Exception("O campo NOME PESSOA deve ser preenchido.");
    	}

    	
    }

    public String getPropsText(){
        StringBuilder propsText = new StringBuilder();
        if(props.get("datanasc") != null){
            propsText.append("datanasc;").append(Uteis.getData((Date)props.get("datanasc"))).append("^");
        }
        Object get = props.get("email");
        if(get != null){
            propsText.append("email;");
            List<EmailVO> emails = (List<EmailVO>)get;
            for(EmailVO obj : emails){
                propsText.append(obj.toJSON()).append("|");
            }
            propsText.append("^");
        }
        Object getTel = props.get("telefone");
        if(get != null){
            propsText.append("telefone;");
            List<TelefoneVO> tels = (List<TelefoneVO>)getTel;
            for(TelefoneVO obj : tels){
                propsText.append(obj.toJSON()).append("|");
            }
            propsText.append("^");
        }
        return propsText.toString();
        
    }

    public void setPropsText(String text) throws Exception{
        
        props = new HashMap<String, Object>();
        String[] split = text.split("\\^");
        for(String item : split){
            String[] splitItem = item.split(";");
            if(splitItem[0].equals("email")){
                try{
                    String[] emails = splitItem[1].split("\\|");
                    List<EmailVO> emailsVO = new ArrayList<EmailVO>();
                    for(String em : emails){
                        emailsVO.add(new EmailVO(new JSONObject(em)));
                    }
                    props.put("email", emailsVO);
                }catch(Exception e){
                }
                continue;
            }
            if(splitItem[0].equals("telefone")){
                try{
                    String[] telefones = splitItem[1].split("\\|");
                    List<TelefoneVO> telefonesVO = new ArrayList<TelefoneVO>();
                    for(String em : telefones){
                        telefonesVO.add(new TelefoneVO(new JSONObject(em)));
                    }
                    props.put("telefone", telefonesVO);
                }catch(Exception e){
                }
                continue;
            }
            if(splitItem[0].equals("datanasc")){
                try{
                   props.put("datanasc", Uteis.getDate(splitItem[1]));
                }catch(Exception e){
                }
                continue;
            }
        }
    }
    
    public boolean senhaAcessoValida(String senhaNova, String senhaNovaConfirmar, boolean permiteOnzeDigitos) throws Exception{
            // Verifica se as duas senhas são iguais.
        if (!senhaNovaConfirmar.equals(senhaNova)) {
            throw new Exception("As senhas não conferem. Digite a senha novamente.");
        }
        //Verifica se a senha tem exatamente 6 dígitos
        if ((permiteOnzeDigitos ? senhaNova.length() != 11 : senhaNova.length() != 5)) {
            throw new Exception("A senha deve ter exatamente " + (permiteOnzeDigitos ? 11 : 5) + " dígitos. Digite a senha novamente.");
        }
        // A senha deverá ser composta de somente números
        if (!UteisValidacao.somenteNumeros(senhaNova)) {
            throw new Exception("A senha deve ter somente números. Digite a senha novamente.");
        }
        if (senhaNova.charAt(0) == senhaNova.charAt(1)
                && senhaNova.charAt(1) == senhaNova.charAt(2)
                && senhaNova.charAt(2) == senhaNova.charAt(3)
                && senhaNova.charAt(3) == senhaNova.charAt(4)) {
            throw new Exception("A senha não pode ter todos os digitos iguais! Digite a senha novamente.");
        }
            return true;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getAssinaturaBiometriaFacial() {
        return assinaturaBiometriaFacial;
    }

    public void setAssinaturaBiometriaFacial(String assinaturaBiometriaFacial) {
        this.assinaturaBiometriaFacial = assinaturaBiometriaFacial;
    }

    public boolean isPossuiBiometriaFacial() {
        return !UteisValidacao.emptyString(getAssinaturaBiometriaFacial());
    }

    public String getAssinaturaBiometriaDigital() {
        return assinaturaBiometriaDigital;
    }

    public void setAssinaturaBiometriaDigital(String assinaturaBiometriaDigital) {
        this.assinaturaBiometriaDigital = assinaturaBiometriaDigital;
    }

    public boolean isPossuiBiometriaDigital() {
        return !UteisValidacao.emptyString(getAssinaturaBiometriaDigital());
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public JSONObject toJSON() {
        JSONObject object = new JSONObject();
        if (!UteisValidacao.emptyNumber(codigo)) {
            object.put("codigo", codigo);
        }
        object.put("tipoPessoa", tipoPessoa);
        object.put("nomePessoa", nomePessoa);
        object.put("codigoPessoa", codigoPessoa);
        object.put("codigoAutorizacao", codigoAutorizacao);
        object.put("assinaturaBiometriaFacial", assinaturaBiometriaFacial);
        object.put("assinaturaBiometriaDigital", assinaturaBiometriaDigital);
        object.put("codigoMatricula", codigoMatricula);
        object.put("codigoGenerico", codigoGenerico);
        object.put("senhaAcesso", senhaAcesso);
        object.put("fotoKey", fotoKey);
        object.put("cpf", cpf);
        object.put("codAcesso", codAcesso);
        if (UteisValidacao.emptyString(getPropsText())) {
            object.put("propsText", getPropsText());
        }
        if (integracao != null && !UteisValidacao.emptyNumber(integracao.getCodigo())) {
            object.put("integracao", integracao.toJSON());
        }
        if (empresaRemota != null && !UteisValidacao.emptyNumber(empresaRemota.getCodigo())) {
            object.put("empresaRemota", empresaRemota.getCodigo());
        }
        return object;
    }

    public void preencherComPessoaConsulta(PessoaConsultaTO pessoa) {
        setCodAcesso(pessoa.getCodAcesso());
        setCodAcessoAlternativo(pessoa.getCodAcessoAlternativo());
        setNomePessoa(pessoa.getPessoa().getNome());
        setCodigoGenerico(pessoa.getCodigo());
        setCodigoMatricula(pessoa.getCodigoMatricula());
        setSenhaAcesso(pessoa.getPessoa().getSenhaAcesso());
        setCodigoPessoa(pessoa.getPessoa().getCodigo());
        setFotoKey(pessoa.getPessoa().getFotoKey());
        setAssinaturaBiometriaDigital(pessoa.getPessoa().getAssinaturaBiometriaDigital());
        setAssinaturaBiometriaFacial(pessoa.getPessoa().getAssinaturaBiometriaFacial());
        setCpf(pessoa.getPessoa().getCfp());
        Map<String, Object> props = new HashMap<>();
        props.put("telefone", pessoa.getPessoa().getTelefoneVOs());
        props.put("email", pessoa.getPessoa().getEmailVOs());
        props.put("datanasc", pessoa.getPessoa().getDataNasc());
        setProps(props);
    }


    public ClienteVO toClienteVO() {
        ClienteVO cliente = new ClienteVO();
        cliente.getPessoa().setNome(getNomePessoa());
        cliente.setCodAcesso(getCodigoAutorizacao());
        cliente.setCodAcessoAlternativo(getCodAcessoAlternativo());
        cliente.getPessoa().setSenhaAcesso(getSenhaAcesso());

        List telefones = (List) getProps().get("telefone");
        if (telefones == null) {
            telefones = new ArrayList<>();
        }
        cliente.getPessoa().setTelefoneVOs(telefones);

        cliente.getPessoa().setEmailVOs((List) getProps().get("email"));
        cliente.getPessoa().setDataNasc((Date) getProps().get("datanasc"));
        return cliente;
    }
}

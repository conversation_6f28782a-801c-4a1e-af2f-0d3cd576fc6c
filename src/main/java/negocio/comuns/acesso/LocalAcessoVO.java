/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class LocalAcessoVO extends SuperVO implements Serializable {

    @NaoControlarLogAlteracao
    private static final long serialVersionUID = -2230960608968493961L;
    @ChavePrimaria
    protected Integer codigo = 0;
    private String descricao = "";
    private String nomeComputador = "";
    @Lista(nome="Validações de Acesso")
    @ListJson(clazz = ColetorVO.class)
    private ArrayList<ColetorVO> listaColetores = new ArrayList<>();
    @ChaveEstrangeira
    @FKJson
    private EmpresaVO empresa;
    private Integer tempoEntreAcessos = 0;
    private Integer tempoEntreAcessosColaborador = 0;
    private Integer tempoToleranciaSaida = 5;
    private String servidorImpressoes = "";
    private String servidorFacialInner = "";
    private Integer portaServidorFacialInner = 7792;
    private Integer portaServidorImp = 5100;
    private Boolean pedirSenhaLibParaCadaAcesso = false;
    private Boolean utilizarModoOffline = true;
    private Date dataBaseOffline = null;
    private Date dataDownloadBase = null;
    private String versaoAcesso = "";
    private Boolean restringirAcessoOutrasUnidades = false;
    private Boolean pedirSenhaCadastrarMaisBiometrias = false;
    private Boolean solicitarJustificativaLiberacaoManual = false;
    private String ip;
    private String categoriaLocalAcesso;
    private Boolean usarReconhecimento = false;
    private Integer portaImagens = 0;
    private String urlServidorCamera = "";
    private Integer portaServidorCamera = 0;
    private Boolean ignorarConsumoCredito = false;

    private Integer capacidadeLimite = 0;

    public LocalAcessoVO() {
        super();
    }

    public LocalAcessoVO(JSONObject mJsonObject) {
        super();
        try {
            this.setCodigo(mJsonObject.getInt("codigo"));
            this.setDescricao(mJsonObject.getString("descricao"));
            // TODO mapear o objeto de acordo com as necessidades
        } catch (JSONException ex) {
            Logger.getLogger(LocalAcessoVO.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    /**
     * Author: Ulisses
     * Data: 18/01/11
     * Operação responsável por validar os dados de um objeto da classe
     * <code>LocalAcessoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas
     * neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação
     * de valores válidos para os atributos.
     *
     * @exception Exception
     *                Se uma inconsistência for encontrada, automaticamente é
     *                gerada uma exceção com a descrição da inconsistência.
     */
    public static void validarDados(LocalAcessoVO obj) throws Exception {
        if (obj.getEmpresa().getCodigo() <= 0) {
            throw new Exception("O campo Empresa, dever ser preenchido.");
        }
        if (obj.getDescricao().trim().isEmpty()) {
            throw new Exception("O campo Descrição(Local), dever ser preenchido.");
        }
        if (obj.getNomeComputador().trim().isEmpty()) {
            throw new Exception("O campo Concentrador, dever ser preenchido.");
        }
    }

    public void adicionarObjColetorVOs(ColetorVO obj) throws Exception {
        int index = 0;
        Iterator i = getListaColetores().iterator();
        while (i.hasNext()) {
            ColetorVO objExistente = (ColetorVO) i.next();
            if (objExistente.getDescricao().equals(obj.getDescricao())) {
                getListaColetores().set(index, obj);
                return;
            }
            index++;
        }
        getListaColetores().add(obj);
    }

    public void excluirObjColetorVOs(ColetorVO obj) throws Exception {
        int index = 0;
        Iterator i = getListaColetores().iterator();
        while (i.hasNext()) {
            ColetorVO objExistente = (ColetorVO) i.next();
            if (objExistente.getDescricao().equals(obj.getDescricao())) {
                getListaColetores().remove(index);
                return;
            }
            index++;
        }
    }

    public ColetorVO consultarObjColetorVO(String numero) throws Exception {
        Iterator i = getListaColetores().iterator();
        while (i.hasNext()) {
            ColetorVO objExistente = (ColetorVO) i.next();
            if (objExistente.getCodigo().toString().equals(numero)) {
                return objExistente;
            }
        }
        return null;
    }

    public Boolean getPedirSenhaLibParaCadaAcesso() {
        return pedirSenhaLibParaCadaAcesso;
    }

    public void setPedirSenhaLibParaCadaAcesso(Boolean pedirSenhaLibParaCadaAcesso) {
        this.pedirSenhaLibParaCadaAcesso = pedirSenhaLibParaCadaAcesso;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            return "";
        } else {
            return descricao;
        }
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao.toUpperCase();
    }

    public ArrayList<ColetorVO> getListaColetores() {
        return listaColetores;
    }

    public void setListaColetores(ArrayList<ColetorVO> listaColetores) {
        this.listaColetores = listaColetores;
    }

    public String getNomeComputador() {
        if (nomeComputador == null) {
            return "";
        } else {
            return nomeComputador;
        }

    }

    public void setNomeComputador(String nomeComputador) {
        this.nomeComputador = nomeComputador.toUpperCase();
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Integer getTempoEntreAcessos() {
        return tempoEntreAcessos;
    }

    public void setTempoEntreAcessos(Integer tempoEntreAcessos) {
        this.tempoEntreAcessos = tempoEntreAcessos;
    }

    public Integer getPortaServidorImp() {
        if(portaServidorImp == null) {
            portaServidorImp = 5100;
        }
        return portaServidorImp;
    }

    public void setPortaServidorImp(Integer portaServidorImp) {
        this.portaServidorImp = portaServidorImp;
    }

    public String getServidorImpressoes() {
        if (servidorImpressoes == null) {
            return "";
        } else {
            return servidorImpressoes;
        }
    }

    public void setServidorImpressoes(String servidorImpressoes) {
        this.servidorImpressoes = servidorImpressoes;
    }


    public String getServidorFacialInner() {
        if (servidorFacialInner == null) {
            servidorFacialInner = "";
        }
        return servidorFacialInner;
    }

    public void setServidorFacialInner(String servidorFacialInner) {
        this.servidorFacialInner = servidorFacialInner;
    }

    public Integer getPortaServidorFacialInner() {
        if (portaServidorFacialInner == null){
            portaServidorFacialInner = 7792;
        }
        return portaServidorFacialInner;
    }

    public void setPortaServidorFacialInner(Integer portaServidorFacialInner) {
        this.portaServidorFacialInner = portaServidorFacialInner;
    }

    public Boolean getUtilizarModoOffline() {
        if (utilizarModoOffline == null){
            utilizarModoOffline = true;
        }
        return utilizarModoOffline;
    }

    public void setUtilizarModoOffline(Boolean utilizarModoOffline) {
        this.utilizarModoOffline = utilizarModoOffline;
    }

    public JSONObject toJSON() {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                o.put(field.getName(), field.get(this));
            }
        } catch (Exception e) {
        }
        return o;
    }

    public Integer getTempoEntreAcessosColaborador() {
        return tempoEntreAcessosColaborador;
    }

    public void setTempoEntreAcessosColaborador(Integer tempoEntreAcessosColaborador) {
        this.tempoEntreAcessosColaborador = tempoEntreAcessosColaborador;
    }

    public Integer getTempoToleranciaSaida() {
        if(null == tempoToleranciaSaida){
            tempoEntreAcessos = 5;
        }
        return tempoToleranciaSaida;
    }

    public void setTempoToleranciaSaida(Integer tempoToleranciaSaida) {
        this.tempoToleranciaSaida = tempoToleranciaSaida;
    }

    public Date getDataBaseOffline() {
        return dataBaseOffline;
    }

    public void setDataBaseOffline(Date dataBaseOffline) {
        this.dataBaseOffline = dataBaseOffline;
    }

    public Date getDataDownloadBase() {
        return dataDownloadBase;
    }

    public void setDataDownloadBase(Date dataDownloadBase) {
        this.dataDownloadBase = dataDownloadBase;
    }

    public String getVersaoAcesso() {
        return versaoAcesso;
    }

    public void setVersaoAcesso(String versaoAcesso) {
        this.versaoAcesso = versaoAcesso;
    }

    public Boolean getPedirSenhaCadastrarMaisBiometrias() {
        return pedirSenhaCadastrarMaisBiometrias;
    }

    public void setPedirSenhaCadastrarMaisBiometrias(Boolean pedirSenhaCadastrarMaisBiometrias) {
        this.pedirSenhaCadastrarMaisBiometrias = pedirSenhaCadastrarMaisBiometrias;
    }

    public Boolean getUsarReconhecimento() {
        if (usarReconhecimento == null) {
            usarReconhecimento = false;
        }
        return usarReconhecimento;
    }

    public void setUsarReconhecimento(Boolean usarReconhecimento) {
        this.usarReconhecimento = usarReconhecimento;
    }

    public Integer getPortaImagens() {
        if (portaImagens == null) {
            portaImagens = 0;
        }
        return portaImagens;
    }

    public void setPortaImagens(Integer portaImagens) {
        this.portaImagens = portaImagens;
    }

    public String getUrlServidorCamera() {
        if (urlServidorCamera == null) {
            urlServidorCamera = "";
        }
        return urlServidorCamera;
    }

    public void setUrlServidorCamera(String urlServidorCamera) {
        this.urlServidorCamera = urlServidorCamera;
    }

    public Integer getPortaServidorCamera() {
        if (portaServidorCamera == null) {
            portaServidorCamera = 0;
        }
        return portaServidorCamera;
    }

    public void setPortaServidorCamera(Integer portaServidorCamera) {
        this.portaServidorCamera = portaServidorCamera;
    }

    public Boolean getSolicitarJustificativaLiberacaoManual() {
        return solicitarJustificativaLiberacaoManual;
    }

    public void setSolicitarJustificativaLiberacaoManual(Boolean solicitarJustificativaLiberacaoManual) {
        this.solicitarJustificativaLiberacaoManual = solicitarJustificativaLiberacaoManual;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getCategoriaLocalAcesso() {
        return categoriaLocalAcesso;
    }

    public void setCategoriaLocalAcesso(String categoriaLocalAcesso) {
        this.categoriaLocalAcesso = categoriaLocalAcesso;
    }

    public Boolean getIgnorarConsumoCredito() {
        return ignorarConsumoCredito;
    }

    public void setIgnorarConsumoCredito(Boolean ignorarConsumoCredito) {
        this.ignorarConsumoCredito = ignorarConsumoCredito;
    }

    public Integer getCapacidadeLimite() {
        return capacidadeLimite;
    }

    public void setCapacidadeLimite(Integer capacidadeLimite) {
        this.capacidadeLimite = capacidadeLimite;
    }

    public Boolean getRestringirAcessoOutrasUnidades() {
        return restringirAcessoOutrasUnidades;
    }

    public void setRestringirAcessoOutrasUnidades(Boolean restringirAcessoOutrasUnidades) {
        this.restringirAcessoOutrasUnidades = restringirAcessoOutrasUnidades;
    }
}

package negocio.comuns.acesso;

import annotations.arquitetura.ChavePrimaria;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IntegracaoAcessoGrupoEmpresarialVO extends SuperVO implements Serializable {
	
    @ChavePrimaria
    protected Integer codigo = 0;
    private String descricao = "";
    private String urlZillyonWeb = "";
    private String nomeEmpresa = "";
    private String chave ="";
    private EmpresaVO empresaRemota = new EmpresaVO();
    private Integer localAcesso;
    private Integer coletor;
    private EmpresaVO empresaLocal = new EmpresaVO();
    private boolean podeExcluir = true;
    private Integer terminal;
	private Integer codigoChaveIntegracaoDigitais;

    public boolean isPodeExcluir() {
        return podeExcluir && !novoObj;
    }

    public void setPodeExcluir(boolean podeExcluir) {
        this.podeExcluir = podeExcluir;
    }
    public static void validarDados(IntegracaoAcessoGrupoEmpresarialVO obj) throws Exception{
    	if(UteisValidacao.emptyNumber(obj.getEmpresaLocal().getCodigo())){
    		throw new Exception("O campo EMPRESA LOCAL deve ser preenchido.");
    	}
    	if(UteisValidacao.emptyString(obj.getDescricao())){
    		throw new Exception("O campo DESCRIÇÃO deve ser preenchido.");
    	}
    	if(UteisValidacao.emptyString(obj.getUrlZillyonWeb())){
    		throw new Exception("O campo URL deve ser preenchido.");
    	}
    	if(UteisValidacao.emptyString(obj.getChave())){
    		throw new Exception("O campo CHAVE deve ser preenchido.");
    	}
    	if(UteisValidacao.emptyNumber(obj.getEmpresaRemota().getCodigo())){
    		throw new Exception("O campo EMPRESA REMOTA deve ser preenchido.");
    	}
    	if(UteisValidacao.emptyNumber(obj.getLocalAcesso())){
    		throw new Exception("O campo LOCAL DE ACESSO deve ser preenchido.");
    	}
    	if(UteisValidacao.emptyNumber(obj.getColetor())){
    		throw new Exception("O campo COLETOR deve ser preenchido.");
    	}
    }
    
	public Integer getCodigo() {
		return codigo;
	}
	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}
	public String getDescricao() {
		return descricao;
	}
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}
	public String getUrlZillyonWeb() {
		return urlZillyonWeb;
	}
	public void setUrlZillyonWeb(String urlZillyonWeb) {
		this.urlZillyonWeb = urlZillyonWeb;
	}
	public String getChave() {
		return chave;
	}
	public void setChave(String chave) {
		this.chave = chave;
	}
	public EmpresaVO getEmpresaRemota() {
		return empresaRemota;
	}
	public void setEmpresaRemota(EmpresaVO empresaRemota) {
		this.empresaRemota = empresaRemota;
	}
	public Integer getLocalAcesso() {
		return localAcesso;
	}
	public void setLocalAcesso(Integer localAcesso) {
		this.localAcesso = localAcesso;
	}
	public Integer getColetor() {
		return coletor;
	}
	public void setColetor(Integer terminal) {
		this.coletor = terminal;
	}
	public EmpresaVO getEmpresaLocal() {
		return empresaLocal;
	}
	public void setEmpresaLocal(EmpresaVO empresaLocal) {
		this.empresaLocal = empresaLocal;
	}

	public Integer getTerminal() {
		return terminal;
	}

	public void setTerminal(Integer terminal) {
		this.terminal = terminal;
	}

	public String getTerminalApresentar() {
		return UteisValidacao.emptyNumber(terminal) ? "" : terminal.toString();
	}

	public Integer getCodigoChaveIntegracaoDigitais() {
		if (codigoChaveIntegracaoDigitais == null) {
			codigoChaveIntegracaoDigitais = 0;
		}
		return codigoChaveIntegracaoDigitais;
	}

	public void setCodigoChaveIntegracaoDigitais(Integer codigoChaveIntegracaoDigitais) {
		this.codigoChaveIntegracaoDigitais = codigoChaveIntegracaoDigitais;
	}

	public String getNomeEmpresa() {
		return nomeEmpresa;
	}

	public void setNomeEmpresa(String nomeEmpresa) {
		this.nomeEmpresa = nomeEmpresa;
	}

	public JSONObject toJSON() {
		JSONObject object = new JSONObject();
		object.put("codigo", codigo);
		object.put("descricao", descricao);
		object.put("urlZillyonWeb", urlZillyonWeb);
		object.put("chave", chave);
		object.put("empresaRemota", empresaRemota.getCodigo());
		object.put("localAcesso", localAcesso);
		object.put("coletor", coletor);
		object.put("empresaLocal", empresaLocal.getCodigo());
		object.put("terminal", terminal);
		return object;
	}
}

package negocio.comuns.crm;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoTelefone;
import com.sun.el.lang.ELSupport;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Date;


import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.plano.ModalidadeVO;

/**
 * Reponsável por manter os dados da entidade Agenda. Classe do tipo VO - Value
 * Object composta pelos atributos da entidade com visibilidade protegida e os
 * métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */

public class AgendaVO extends SuperVO {

	protected Integer codigo;
	protected Date dataAgendamento;
	protected Date dataLancamento;
	protected String hora;
	protected String minuto;
	protected String tipoAgendamento;
	protected String tipoProfessor;
	protected Integer codigoProfessor;
	protected String nomeProfessor;
	protected String professorCrm;

	protected ModalidadeVO modalidade;
	protected Date dataComparecimento;
	protected UsuarioVO responsavelComparecimento;
	protected Integer alunohorarioturma;

	protected String horaMinuto;
	/**
	 * Atributo responsável por manter o objeto relacionado da classe
	 * <code>Passivo </code>.
	 */
	protected PassivoVO passivo;
	/**
	 * Atributo responsável por manter o objeto relacionado da classe
	 * <code>Indicacao </code>.
	 */
	protected UsuarioVO colaboradorResponsavel;
	/**
	 * Atributo responsável por manter o objeto relacionado da classe
	 * <code>Cliente </code>.
	 */
	protected ClienteVO cliente;
	/**
	 * Atributo responsável por manter o objeto relacionado da classe
	 * <code>Cliente </code>.
	 */
	public UsuarioVO responsavelCadastro;
	private IndicadoVO indicado;
	//atributo não sera gravado no banco
	private List listaConfirmacaoComparecimentoAgendamentoHoje;
	private List listaConfirmacaoComparecimentoAgendamentoMes;
	private List listaConfirmacaoComparecimentoAgendamentoHistorico;
	private Boolean isDataComparecimentoNula;
	private Integer totalComparecidos;
	private String abaSelecionada;
	private Integer empresa;
	private Integer responsavelCadastroCodigo;
	private Integer reposicao;
	private boolean presenca;
	private ConviteAulaExperimentalVO conviteAulaExperimentalVO;
	private ContratoVO contrato;
    private boolean gymPass = false;
	@NaoControlarLogAlteracao
    private boolean convertido = false;

	/**
	 * Construtor padrão da classe <code>Agenda</code>. Cria uma nova instância
	 * desta entidade, inicializando automaticamente seus atributos (Classe VO).
	 */
	public AgendaVO() {
		super();
		inicializarDados();
	}

	/**
	 * Operação responsável por validar a unicidade dos dados de um objeto da
	 * classe <code>AgendaVO</code>.
	 */
	public static void validarUnicidade(List<AgendaVO> lista, AgendaVO obj) throws ConsistirException {
		for (AgendaVO repetido : lista) {
		}
	}

	/**
	 * Operação responsável por validar os dados de um objeto da classe
	 * <code>AgendaVO</code>. Todos os tipos de consistência de dados são e
	 * devem ser implementadas neste método. São validações típicas: verificação
	 * de campos obrigatórios, verificação de valores válidos para os atributos.
	 *
	 *                Se uma inconsistência for encontrada aumaticamente é
	 *                gerada uma exceção descrevendo o atributo e o erro
	 *                ocorrido.
	 */
	public static void validarDados(AgendaVO obj) throws ConsistirException {
		if (!obj.getValidarDados().booleanValue()) {
			return;
		}
		if (obj.getTipoAgendamento().equals("")) {
			throw new ConsistirException(("O campo Tipo de Agendamento está vazio !"));
		}
		if (obj.getDataAgendamento() == null) {
			throw new ConsistirException(("O campo Data está vazio !"));
		}
		if (obj.getHora().trim().equals("")) {
			throw new ConsistirException(("O campo Hora está vazio !"));
		}
		if (obj.getMinuto().trim().equals("")) {
			throw new ConsistirException(("O campo Minuto está vazio !"));
		}
		 if (obj.getTipoAgendamento().equals("AE") && UteisValidacao.emptyNumber(obj.getModalidade().getCodigo())) {
			 throw new ConsistirException(("O campo Modalidade está vazio !"));
		 }
		// if ((obj.getResponsavelCadastro() == null) ||
		// (obj.getResponsavelCadastro().getCodigo().intValue() == 0)) {
		// throw new
		// ConsistirException(("O campo Responsável Cadastro está vazio !"));
		// }
	}

	/**
	 * Método responsável por apresentar os dados do agendado, que é um output
	 * dizendo q esta agendado, a data agendada e a hora agendada
	 */
	public Boolean getApresentarDadosAgendado() {
		if (!getTipoAgendamento().equals("") && ((getDataAgendamento() != null)) && (!getHora().equals(""))) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Operação reponsável por realizar o UpperCase dos atributos do tipo
	 * String.
	 */
	public void realizarUpperCaseDados() {
		setHora(getHora().toUpperCase());
		setTipoAgendamento(getTipoAgendamento().toUpperCase());
	}

	/**
	 * Operação reponsável por inicializar os atributos da classe.
	 */
	public void inicializarDados() {
		setDataAgendamento(negocio.comuns.utilitarias.Calendario.hoje());
	}

	public String qualResultadoAgendamento() {
		if (getTipoAgendamento().equals("LI")) {
			return "Ag.Ligação: " + Uteis.getData(getDataAgendamento()) + " " + getApresentarMinutoEmHora();
		} else if (getTipoAgendamento().equals("VI")) {
			return "Ag.Visita: " + Uteis.getData(getDataAgendamento()) + " " + getApresentarMinutoEmHora();
		} else if (getTipoAgendamento().equals("LA")) {
			return "Ag.Visita: " + Uteis.getData(getDataAgendamento()) + " " + getApresentarMinutoEmHora();
		} else {
			return "Ag.Aula Experimental: " + Uteis.getData(getDataAgendamento()) + " " + getApresentarMinutoEmHora();
		}
	}

	/**
	 * Retorna o objeto da classe <code>Cliente</code> relacionado com (
	 * <code>Agenda</code>).
	 */
	public UsuarioVO getResponsavelCadastro() {
		if (responsavelCadastro == null) {
			responsavelCadastro = new UsuarioVO();
		}
		return (responsavelCadastro);
	}

	/**
	 * Define o objeto da classe <code>Cliente</code> relacionado com (
	 * <code>Agenda</code>).
	 */
	public void setResponsavelCadastro(UsuarioVO obj) {
		this.responsavelCadastro = obj;
	}

	/**
	 * Retorna o objeto da classe <code>Cliente</code> relacionado com (
	 * <code>Agenda</code>).
	 */
	public ClienteVO getCliente() {
		if (cliente == null) {
			cliente = new ClienteVO();
		}
		return (cliente);
	}

	/**
	 * Define o objeto da classe <code>Cliente</code> relacionado com (
	 * <code>Agenda</code>).
	 */
	public void setCliente(ClienteVO obj) {
		this.cliente = obj;
	}

	/**
	 * Retorna o objeto da classe <code>Colaborador</code> relacionado com (
	 * <code>Agenda</code>).
	 */
	public UsuarioVO getColaboradorResponsavel() {
		if (colaboradorResponsavel == null) {
			colaboradorResponsavel = new UsuarioVO();
		}
		return (colaboradorResponsavel);
	}

	/**
	 * Define o objeto da classe <code>Colaborador</code> relacionado com (
	 * <code>Agenda</code>).
	 */
	public void setColaboradorResponsavel(UsuarioVO obj) {
		this.colaboradorResponsavel = obj;
	}

	/**
	 * Retorna o objeto da classe <code>Passivo</code> relacionado com (
	 * <code>Agenda</code>).
	 */
	public PassivoVO getPassivo() {
		if (passivo == null) {
			passivo = new PassivoVO();
		}
		return (passivo);
	}

	/**
	 * Define o objeto da classe <code>Passivo</code> relacionado com (
	 * <code>Agenda</code>).
	 */
	public void setPassivo(PassivoVO obj) {
		this.passivo = obj;
	}

	public ModalidadeVO getModalidade() {
		if (modalidade == null) {
			modalidade = new ModalidadeVO();
		}
		return (modalidade);
	}

	public void setModalidade(ModalidadeVO modalidade) {
		this.modalidade = modalidade;
	}

	public String getTipoAgendamento() {
		if (tipoAgendamento == null) {
			tipoAgendamento = "VI";
		}
		return (tipoAgendamento);
	}

	public String getTipoProfessor() {
		if (tipoProfessor == null) {
			tipoProfessor = "";
		}
		return tipoProfessor;
	}

	public void setTipoProfessor(String tipoProfessor) {
		this.tipoProfessor = tipoProfessor;
	}

	public Integer getCodigoProfessor() {
		return codigoProfessor;
	}

	public void setCodigoProfessor(Integer codigoProfessor) {
		this.codigoProfessor = codigoProfessor;
	}

	/**
	 * Operação responsável por retornar o valor de apresentação de um atributo
	 * com um domínio específico. Com base no valor de armazenamento do atributo
	 * esta função é capaz de retornar o de apresentação correspondente. Útil
	 * para campos como sexo, escolaridade, etc.
	 */
	public String getTipoAgendamento_Apresentar() {
		if (tipoAgendamento == null) {
			return "";
		}
		if (tipoAgendamento.equals("LI")) {
			return "Ligação";
		}
		if (tipoAgendamento.equals("VI")) {
			return "Visita";
		}
		if (tipoAgendamento.equals("AE") && !isGymPass()) {
			return "Aula Experimental";
		}
		if (tipoAgendamento.equals("AE") && isGymPass()) {
			return "GymPass";
		}

		if (tipoAgendamento.equals("DI")) {
			return "Diária";
		}

		return (tipoAgendamento);
	}

	public void setTipoAgendamento(String tipoAgendamento) {
		this.tipoAgendamento = tipoAgendamento;
	}

	public String getHora() {
		if (hora == null) {
			hora = "";
		}
		return (hora);
	}

	public String getHora_Apresentar() {
		if (hora == null) {
			hora = "";
		}
		return Dominios.getHoras().get(hora);
	}

	public String getApresentarMinutoEmHora() {
		return getHora() + ":" + getMinuto();
	}

	public void setHora(String hora) {
		this.hora = hora;
	}

	public Date getDataAgendamento() {
		return (dataAgendamento );
	}

	/**
	 * Operação responsável por retornar um atributo do tipo data no formato
	 * padrão dd/mm/aaaa.
	 */
	public String getDataAgendamento_Apresentar() {
		return (Uteis.getData(dataAgendamento));
	}

	public void setDataAgendamento(Date dataAgendamento) {
		this.dataAgendamento = dataAgendamento;
	}

	public Integer getCodigo() {
		if (codigo == null) {
			codigo = new Integer(0);
		}
		return (codigo);
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return the indicado
	 */
	public IndicadoVO getIndicado() {
		if (indicado == null) {
			indicado = new IndicadoVO();
		}
		return indicado;
	}

	/**
	 * @param indicado
	 *            the indicado to set
	 */
	public void setIndicado(IndicadoVO indicado) {
		this.indicado = indicado;
	}

	public String getMinuto() {
		if (minuto == null) {
			minuto = "";
		}
		return minuto;
	}

	public void setMinuto(String minuto) {
		this.minuto = minuto;
	}

	public Date getDataComparecimento() {
		return dataComparecimento;
	}

	public String getDataComparecimento_Apresentar() {
		if(dataComparecimento == null){
			return "";
		}
		return (Uteis.getDataComHora(dataComparecimento));
	}


	public void setDataComparecimento(Date dataComparecimento) {
		this.dataComparecimento = dataComparecimento;
	}

	public UsuarioVO getResponsavelComparecimento() {
		if(responsavelComparecimento == null){
			responsavelComparecimento = new UsuarioVO();
		}
		return responsavelComparecimento;
	}

	public void setResponsavelComparecimento(UsuarioVO responsavelComparecimetno) {
		this.responsavelComparecimento = responsavelComparecimetno;
	}

	public Long getTotalizadorAgendadosComparecimento() {
		if (this == null) {
			return 0L;
		}
		if(getAbaSelecionada().equals("HJ")){
			return new Long(getListaConfirmacaoComparecimentoAgendamentoHoje().size());
		}else if(getAbaSelecionada().equals("ME")){
			return new Long(getListaConfirmacaoComparecimentoAgendamentoMes().size());
		}else if(getAbaSelecionada().equals("HI")){
			return new Long(getListaConfirmacaoComparecimentoAgendamentoHistorico().size());
		}
		return new Long(0);
	}

	public List<AgendaVO> getListaConfirmacaoComparecimentoAgendamentoHoje() {
		if(listaConfirmacaoComparecimentoAgendamentoHoje == null){
			listaConfirmacaoComparecimentoAgendamentoHoje = new ArrayList();
		}
		return listaConfirmacaoComparecimentoAgendamentoHoje;
	}

	public void setListaConfirmacaoComparecimentoAgendamentoHoje(List listaConfirmacaoComparecimentoAgendamentoHoje) {
		this.listaConfirmacaoComparecimentoAgendamentoHoje = listaConfirmacaoComparecimentoAgendamentoHoje;
	}

	public Boolean getApresentarBotaoGravarAgendadosComparecimento(){
		if(getDataComparecimento() == null && !getTipoAgendamento().equals("LI")){
			return true;
		}
		return false;
	}
	public Boolean getApresentarBotaoCancelarAgendadosComparecimento(){
		if(getDataComparecimento() != null && !getTipoAgendamento().equals("LI")){
			return true;
		}
		return false;
	}


	public Boolean getValidarPassivoAgendadoApresentar(){
		if(getPassivo().getCodigo() != 0){
			return true;
		}
		return false;
	}

	public Boolean getValidarIndicadoAgendadoApresentar(){
		if(getIndicado().getCodigo() != 0){
			return true;
		}
		return false;
	}

	public Boolean getValidarClienteAgendadoApresentar(){
		if(getCliente().getCodigo() != 0){
			return true;
		}
		return false;
	}

	public String getDataAgendamentoComparecimento_Apresentar() {
		return Uteis.getData(getDataAgendamento()) + " - " + getApresentarMinutoEmHora();
	}

	public Boolean getIsDataComparecimentoNula() {
		if(isDataComparecimentoNula == null){
			isDataComparecimentoNula = new Boolean(false);
		}
		return isDataComparecimentoNula;
	}

	public void setIsDataComparecimentoNula(Boolean isDataComparecimentoNula) {
		this.isDataComparecimentoNula = isDataComparecimentoNula;
	}

	public Date getDataLancamento() {
		if(dataLancamento == null){
			dataLancamento = negocio.comuns.utilitarias.Calendario.hoje();
		}
		return dataLancamento;
	}

	public String getDataLancamento_Apresentar() {
		return (Uteis.getDataComHora(getDataLancamento()));
	}

	public void setDataLancamento(Date dataLancamento) {
		this.dataLancamento = dataLancamento;
	}

	public Integer getTotalComparecidos() {
		if(totalComparecidos == null){
			totalComparecidos = new Integer(0);
		}
		return totalComparecidos;
	}

	public void setTotalComparecidos(Integer totalComparecidos) {
		this.totalComparecidos = totalComparecidos;
	}

	public List getListaConfirmacaoComparecimentoAgendamentoMes() {
		if(listaConfirmacaoComparecimentoAgendamentoMes == null){
			listaConfirmacaoComparecimentoAgendamentoMes = new ArrayList();
		}
		return listaConfirmacaoComparecimentoAgendamentoMes;
	}

	public void setListaConfirmacaoComparecimentoAgendamentoMes(List listaConfirmacaoComparecimentoAgendamentoMes) {
		this.listaConfirmacaoComparecimentoAgendamentoMes = listaConfirmacaoComparecimentoAgendamentoMes;
	}

	public List getListaConfirmacaoComparecimentoAgendamentoHistorico() {
		if(listaConfirmacaoComparecimentoAgendamentoHistorico == null){
			listaConfirmacaoComparecimentoAgendamentoHistorico = new ArrayList();
		}
		return listaConfirmacaoComparecimentoAgendamentoHistorico;
	}

	public void setListaConfirmacaoComparecimentoAgendamentoHistorico(List listaConfirmacaoComparecimentoAgendamentoHistorico) {
		this.listaConfirmacaoComparecimentoAgendamentoHistorico = listaConfirmacaoComparecimentoAgendamentoHistorico;
	}

	public String getAbaSelecionada() {
		if(abaSelecionada == null){
			abaSelecionada = "";
		}
		return abaSelecionada;
	}

	public void setAbaSelecionada(String abaSelecionada) {
		this.abaSelecionada = abaSelecionada;
	}

	/**
	 * @param empresa the empresa to set
	 */
	public void setEmpresa(Integer empresa) {
		this.empresa = empresa;
	}

	/**
	 * @return the empresa
	 */
	public Integer getEmpresa() {
		return empresa;
	}

        public String getNomePessoa(){
            if(!UteisValidacao.emptyString(getCliente().getPessoa().getNome())){
                return getCliente().getPessoa().getNome();
            }
            if(!UteisValidacao.emptyString(getPassivo().getNome())){
                return getPassivo().getNome();
            }
            if(!UteisValidacao.emptyString(getIndicado().getNomeIndicado())){
                return getIndicado().getNomeIndicado();
            }
            return "";
        }

		public String getTelefonesResidencialPessoa() {
			if(!UteisValidacao.emptyString(getPassivo().getNome())) {
				return getPassivo().getTelefoneResidencial();
			} else if (!UteisValidacao.emptyList(getCliente().getPessoa().getTelefoneVOs())) {
				return getCliente().getPessoa().getTelefonesPorTipo(TipoTelefone.RESIDENCIAL.getCodigo());
			}
			return "";
		}

		public String getTelefonesCelularPessoa() {
			if(!UteisValidacao.emptyString(getPassivo().getNome())) {
				return getPassivo().getTelefoneCelular();
			} else if (!UteisValidacao.emptyList(getCliente().getPessoa().getTelefoneVOs())) {
				return getCliente().getPessoa().getTelefonesPorTipo(TipoTelefone.CELULAR.getCodigo());
			}
			return "";
		}

		public String getTelefonesComercialPessoa() {
			if(!UteisValidacao.emptyString(getPassivo().getNome())) {
				return getPassivo().getTelefoneTrabalho();
			} else if (!UteisValidacao.emptyList(getCliente().getPessoa().getTelefoneVOs())) {
				return getCliente().getPessoa().getTelefonesPorTipo(TipoTelefone.COMERCIAL.getCodigo());
			}
			return "";
		}

        public Date getDataAgendamentoComHora(){
            try {
                return Uteis.getDateTime(dataAgendamento, Integer.valueOf(hora), Integer.valueOf(minuto), 0);
            } catch (Exception e) {
                return Uteis.getDateTime(dataAgendamento, 0, 0, 0);
            }
        }

    public ContratoVO getContrato() {
        if(contrato == null){
            contrato = new ContratoVO();
        }
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public String getColaboradorResponsavelNomeAbreviado(){
        return getColaboradorResponsavel().getNomeAbreviado();
    }

    public Integer getContratoCodigo(){
        return UteisValidacao.emptyNumber(getContrato().getCodigo()) ? null : getContrato().getCodigo();
    }

    public String getConsultorContrato(){
        try {
            return getContrato().getConsultor().getPessoa().getNomeAbreviado();
        } catch (Exception e) {
            return "";
        }

    }

    public String getRespContrato(){
        try {
            return getContrato().getResponsavelContrato().getNomeAbreviado();
        } catch (Exception e) {
            return "";
        }

    }

    public String getHoraMinuto() {
		if (horaMinuto == null) {
			horaMinuto = "";
		}
		return (horaMinuto);
	}

	public void setHoraMinuto(String horaMinuto) {
		this.horaMinuto = horaMinuto;
	}

	public ConviteAulaExperimentalVO getConviteAulaExperimentalVO() {
		return conviteAulaExperimentalVO;
	}

	public void setConviteAulaExperimentalVO(ConviteAulaExperimentalVO conviteAulaExperimentalVO) {
		this.conviteAulaExperimentalVO = conviteAulaExperimentalVO;
	}

	public String getMatricula(){
		if(!UteisValidacao.emptyString(getCliente().getPessoa().getNome())){
			return getCliente().getMatricula();
		}
		if(!UteisValidacao.emptyString(getPassivo().getNome())){
			return "RECEPTIVO";
		}
		if(!UteisValidacao.emptyString(getIndicado().getNomeIndicado())){
			return "INDICADO";
		}
		return "";
	}

	public String getRespCadastro(){
		try {
			return getResponsavelCadastro().getNomeAbreviado();
		} catch (Exception e) {
			return "";
		}

	}

	public boolean isPresenca() {
		return presenca;
	}

	public String getPresenteNaAula() {
		return presenca ? "Sim" : "";
	}

	public void setPresenca(boolean presenca) {
		this.presenca = presenca;
	}

	public Integer getReposicao() {
		return reposicao;
	}

	public void setReposicao(Integer reposicao) {
		this.reposicao = reposicao;
	}

	public boolean isGymPass() {
		return gymPass;
	}

	public void setGymPass(boolean gymPass) {
		this.gymPass = gymPass;
	}

	public String getNomeProfessor() {
		return nomeProfessor == null ? "" : nomeProfessor;
	}

	public void setNomeProfessor(String nomeProfessor) {
		this.nomeProfessor = nomeProfessor;
	}

	public String getProfessorCrm() {
		return professorCrm;
	}

	public void setProfessorCrm(String professorCrm) {
		this.professorCrm = professorCrm;
	}

	public Integer getAlunohorarioturma() { return alunohorarioturma; }

	public void setAlunohorarioturma(Integer alunohorarioturma) { this.alunohorarioturma = alunohorarioturma; }

	public boolean isConvertido() {
		return convertido;
	}

	public void setConvertido(boolean convertido) {
		this.convertido = convertido;
	}
}

package negocio.comuns.crm;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class SmsEnviadosVO extends SuperVO {
    private Integer codigo;
    private Integer cliente;
    private Integer pessoa;
    private Date dataenvio;
    private String situacao;

    public SmsEnviadosVO(Integer cliente, Integer pessoa, Date dataenvio, String situacao){
        this.cliente =cliente;
        this.pessoa = pessoa;
        this.dataenvio = dataenvio;
        this.situacao = situacao;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Date getDataenvio() {
        return dataenvio;
    }

    public void setDataenvio(Date dataenvio) {
        this.dataenvio = dataenvio;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }
}

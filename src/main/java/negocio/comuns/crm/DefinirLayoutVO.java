package negocio.comuns.crm;

import java.util.Iterator;
import java.util.List;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade DefinirLayout. Classe do tipo VO -
 * Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos
 * de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * 
 * @see SuperVO
 */

public class DefinirLayoutVO extends SuperVO {

	protected Integer codigo;
	protected UsuarioVO usuario;
	protected String titulo;
	protected String url;
	protected Integer sequencia;

	/**
	 * Construtor padrão da classe <code>DefinirLayout</code>.
	 * Cria uma nova instância desta entidade, inicializando automaticamente
	 * seus atributos (Classe VO).
	 */
	public DefinirLayoutVO() {
		super();
		inicializarDados();
	}

	/**
	 * Operação responsável por validar a unicidade dos dados de um objeto da
	 * classe <code>DefinirLayoutVO</code>.
	 */
	public static void validarUnicidade(List<DefinirLayoutVO> lista, DefinirLayoutVO obj) throws ConsistirException {
		for (DefinirLayoutVO repetido : lista) {
		}
	}

	/**
	 * Operação responsável por validar os dados de um objeto da classe
	 * <code>DefinirLayoutVO</code>.
	 * Todos os tipos de consistência de dados são e devem ser implementadas
	 * neste método.
	 * São validações típicas: verificação de campos obrigatórios, verificação
	 * de valores válidos para os atributos.
	 * 
	 * @exception ConsistirExecption
	 *                Se uma inconsistência for encontrada aumaticamente é
	 *                gerada uma exceção descrevendo
	 *                o atributo e o erro ocorrido.
	 */
	public static void validarDados(DefinirLayoutVO obj) throws ConsistirException {
		if (!obj.getValidarDados().booleanValue()) {
			return;
		}
		if (obj.getUsuario().getCodigo().intValue() == 0) {
			throw new ConsistirException("");
		}
	}

	/**
	 * Operação reponsável por realizar o UpperCase dos atributos do tipo
	 * String.
	 */
	public void realizarUpperCaseDados() {
		if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
			return;
		}		
	}

	/**
	 * Operação reponsável por inicializar os atributos da classe.
	 */
	public void inicializarDados() {
		setCodigo(null);
		setUsuario(new UsuarioVO());
		setTitulo("");
		setUrl("");
		setSequencia(new Integer(0));
	}

	public static Boolean existePermisao(List<DefinirLayoutVO> listaDefinicaoLayout, String url) {
		Iterator i = listaDefinicaoLayout.iterator();
		while (i.hasNext()) {
			DefinirLayoutVO definicao = (DefinirLayoutVO) i.next();
			if (definicao.getUrl().equals(url)) {
				return true;
			}
		}
		return false;
	}

	public Integer getSequencia() {
		if (sequencia == null) {
			sequencia = new Integer(0);
		}
		return (sequencia);
	}

	public void setSequencia(Integer sequencia) {
		this.sequencia = sequencia;
	}

	public String getUrl() {
		if (url == null) {
			url = "";
		}
		return (url);
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getTitulo() {
		if (titulo == null) {
			titulo = "";
		}
		return (titulo);
	}

	public void setTitulo(String titulo) {
		this.titulo = titulo;
	}

	/**
	 * @return the usuario
	 */
	public UsuarioVO getUsuario() {
		return usuario;
	}

	/**
	 * @param usuario
	 *            the usuario to set
	 */
	public void setUsuario(UsuarioVO usuario) {
		this.usuario = usuario;
	}

	public Integer getCodigo() {
		if (codigo == null) {
			codigo = new Integer(0);
		}
		return (codigo);
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}
}
package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 02/09/13
 * Time: 14:04
 */
public class FiltroCarteiraTO extends SuperTO {

    private static final long serialVersionUID = -6157173249563291855L;
    private String tipoVinculo = "";
    private List<SelectItem> listaGrupoColaborador;
    private GrupoColaboradorVO grupoColaboradorVO = new GrupoColaboradorVO();
    private String situacaoCliente = "";
    private String periodoAcesso = "";
    private ColaboradorVO colaborador = new ColaboradorVO();
    private Boolean todosClientes = true;
    private Date inicioCadastro;
    private Date fimCadastro;
    private EmpresaVO empresaVO = new EmpresaVO();
    private int qtdVinculos = 0;
    private List<FiltroCarteiraTO> listaDeVinculos = new ArrayList<FiltroCarteiraTO>();
    private List<InfoCarteiraTO> infoCarteiraTOs = new ArrayList<InfoCarteiraTO>();
    private Boolean selecionado = false;
    private boolean sugestao = false;
    private boolean inconsistencia = false;

    private String filtroAvancado = "";
    private String filtroJoinTabela = "";
    private String filtroAvancadoTexto = "";

    private Integer empresaLogado = 0;
    private boolean desconsiderarVinculoTreino = true;
    
    private boolean alunosTreinoSemVinculo = false;

    public String getTextoFiltro() {
        StringBuilder texto = new StringBuilder();
        if (!UteisValidacao.emptyString(getSituacaoCliente_Apresentar())) {
            texto.append(", situação: ").append(getSituacaoCliente_Apresentar()).append("s");
        }

        if (todosClientes || (inicioCadastro == null && fimCadastro == null)) {
            texto.append(", todos clientes cadastrados");
        } else {
            texto.append(", clientes cadastrados ");
            if (inicioCadastro != null) {
                texto.append(" a partir de ").append(Uteis.getDataAplicandoFormatacao(inicioCadastro, "dd/MM/yyyy"));
            }
            if (fimCadastro != null) {
                texto.append(" até ").append(Uteis.getDataAplicandoFormatacao(fimCadastro, "dd/MM/yyyy"));
            }
        }

        if (!UteisValidacao.emptyString(getPeriodoAcesso())) {
            texto.append(", período de acesso: ").append(getPeriodoAcesso());
        }

        if (!UteisValidacao.emptyString(getFiltroAvancadoTexto())) {
            texto.append(", ").append(getFiltroAvancadoTexto());
        }
        
        texto.deleteCharAt(0);
        return texto.toString();
    }

    public String getTextoSugestao() {
        if (isClientesSemVinculo()) {
            return "Clientes sem " + this.getTipoVinculo_Apresentar();
        }

        if (isClientesInconsistentes()) {
            return "Clientes com vínculos de " + this.getTipoVinculo_Apresentar() + " de outra empresa";
        }

        if (isConsultorCarteiraVazia()) {
            return this.getTipoVinculo_Apresentar() + " " + this.getNomeColaborador() + " está com a carteira vazia";
        }

        if (isConsultorInativoIrregular()) {
            return this.getTipoVinculo_Apresentar() + " " + this.getNomeColaborador() + " está inativo e com vínculos";
        }

        return "";
    }

    public Integer getCodigoColaborador() {
        if (getColaborador().getCodigo() > 0) {
            return getColaborador().getCodigo();
        }
        return 0;
    }

    public String getNomeColaborador() {
        if (getColaborador().getPessoa().getNomeAbreviado().length() > 0) {
            return getColaborador().getPessoa().getNomeAbreviado();
        }
        return "";
    }

    public Integer getTotalDeVinculos() {
        Integer total = 0;
        for (FiltroCarteiraTO vinculo : listaDeVinculos) {
            total += vinculo.getQtdVinculos();
        }
        return total;
    }

    public String getTotalDeVinculos_Apresentar() {
        if (getTotalDeVinculos() != 1) {
            return getTotalDeVinculos() + " clientes";
        }
        return getTotalDeVinculos() + "cliente";
    }

    public String getSelecionado_Apresentar() {
        return (selecionado) ? "background: #F3F3F4 !important;" : "background: #ffff !important;";
    }

    public String getTipoVinculo() {
        if (tipoVinculo == null) {
            return "";
        }
        return tipoVinculo;
    }

    public void setTipoVinculo(String tipoVinculo) {
        this.tipoVinculo = tipoVinculo;
    }

    public String getTipoVinculo_Apresentar() {
        if (!getTipoVinculo().equals("")) {
            return TipoColaboradorEnum.getTipo(getTipoVinculo()).getDescricao();
        }
        return "";
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    public int getQtdVinculos() {
        return qtdVinculos;
    }

    public void setQtdVinculos(int qtdVinculos) {
        this.qtdVinculos = qtdVinculos;
    }

    public boolean isApresentarConsultor() {
        return getQtdVinculos() < 1 || (!getColaborador().getSituacao().equals("AT") && !getColaborador().getSituacao().equals(""));
    }

    public boolean isTodosClientes() {
        return todosClientes;
    }

    public void setTodosClientes(boolean todosClientes) {
        this.todosClientes = todosClientes;
    }

    public Date getInicioCadastro() {
        return inicioCadastro;
    }

    public void setInicioCadastro(Date inicioCadastro) {
        this.inicioCadastro = inicioCadastro;
    }

    public Date getFimCadastro() {
        return fimCadastro;
    }

    public void setFimCadastro(Date fimCadastro) {
        this.fimCadastro = fimCadastro;
    }

    public String getPeriodoAcesso() {
        if (periodoAcesso == null) {
            return "";
        }
        return periodoAcesso;
    }

    public void setPeriodoAcesso(String periodoAcesso) {
        this.periodoAcesso = periodoAcesso;
    }

    public String getSituacaoCliente() {
        return situacaoCliente;
    }

    public void setSituacaoCliente(String situacaoCliente) {
        this.situacaoCliente = situacaoCliente;
    }

    public String getSituacaoCliente_Apresentar() {
        if (UteisValidacao.emptyString(getSituacaoCliente())) {
            return "";
        }
        return SituacaoClienteEnum.getSituacaoCliente(getSituacaoCliente()).getDescricao();
    }

    public List<FiltroCarteiraTO> getListaDeVinculos() {
        return listaDeVinculos;
    }

    public void setListaDeVinculos(List<FiltroCarteiraTO> listaDeVinculos) {
        this.listaDeVinculos = listaDeVinculos;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public String getEmpresa_Apresentar() {
        return getEmpresaVO().getNome();
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public GrupoColaboradorVO getGrupoColaboradorVO() {
        return grupoColaboradorVO;
    }

    public void setGrupoColaboradorVO(GrupoColaboradorVO grupoColaboradorVO) {
        this.grupoColaboradorVO = grupoColaboradorVO;
    }

    public List<InfoCarteiraTO> getInfoCarteiraTOs() {
        return infoCarteiraTOs;
    }

    public void setInfoCarteiraTOs(List<InfoCarteiraTO> infoCarteiraTOs) {
        this.infoCarteiraTOs = infoCarteiraTOs;
    }

    public List<SelectItem> getListaGrupoColaborador() {
        if(listaGrupoColaborador == null){
            listaGrupoColaborador = new ArrayList<>();
        }
        return listaGrupoColaborador;
    }

    public void setListaGrupoColaborador(List<SelectItem> listaGrupoColaborador) {
        this.listaGrupoColaborador = listaGrupoColaborador;
    }

    public boolean isSugestao() {
        return sugestao;
    }

    public void setSugestao(boolean sugestao) {
        this.sugestao = sugestao;
    }

    public boolean isInconsistencia() {
        return inconsistencia;
    }

    public void setInconsistencia(boolean inconsistencia) {
        this.inconsistencia = inconsistencia;
    }

    public boolean isClientesSemVinculo() {
        return !isApresentarConsultor() && !isInconsistencia();
    }

    public boolean isClientesInconsistentes() {
        return !isApresentarConsultor() && this.isInconsistencia();
    }

    public boolean isConsultorCarteiraVazia() {
        return isApresentarConsultor() && getColaborador().getSituacao().equals("AT");
    }

    public boolean isConsultorInativoIrregular() {
        return isApresentarConsultor() && !getColaborador().getSituacao().equals("AT");
    }

    public String getFiltroAvancado() {
        return filtroAvancado;
    }

    public void setFiltroAvancado(String filtroAvancado) {
        this.filtroAvancado = filtroAvancado;
    }

    public String getFiltroAvancadoTexto() {
        return filtroAvancadoTexto;
    }

    public void setFiltroAvancadoTexto(String filtroAvancadoTexto) {
        this.filtroAvancadoTexto = filtroAvancadoTexto;
    }

    public Integer getEmpresaLogado() {
        return empresaLogado;
    }

    public void setEmpresaLogado(Integer empresaLogado) {
        this.empresaLogado = empresaLogado;
    }

    public boolean isDesconsiderarVinculoTreino() {
        return desconsiderarVinculoTreino;
    }

    public void setDesconsiderarVinculoTreino(boolean desconsiderarVinculoTreino) {
        this.desconsiderarVinculoTreino = desconsiderarVinculoTreino;
    }

    public boolean isAlunosTreinoSemVinculo() {
        return alunosTreinoSemVinculo;
    }

    public void setAlunosTreinoSemVinculo(boolean alunosTreinoSemVinculo) {
        this.alunosTreinoSemVinculo = alunosTreinoSemVinculo;
    }
    public String getFiltroJoinTabela() {
        return filtroJoinTabela;
    }

    public void setFiltroJoinTabela(String filtroJoinTabela) {
        this.filtroJoinTabela = filtroJoinTabela;
    }

    public static Integer descobrirTotalQuantidadeVinculoFiltroSelecionado(FiltroCarteiraTO filtroCarteiraTO) {
        if(filtroCarteiraTO.isSugestao()){
            return filtroCarteiraTO.getQtdVinculos();
        }

        for (FiltroCarteiraTO vinculo : filtroCarteiraTO.getListaDeVinculos()) {
            if (filtroCarteiraTO.getTipoVinculo().equalsIgnoreCase(vinculo.getTipoVinculo())) {
                return vinculo.getQtdVinculos();
            }
        }

        return 0;
    }

}

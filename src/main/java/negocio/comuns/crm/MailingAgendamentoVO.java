package negocio.comuns.crm;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.enumeradores.OcorrenciaEnum;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemanaCRON;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class MailingAgendamentoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private int malaDireta;
    private OcorrenciaEnum ocorrencia = OcorrenciaEnum.INSTANTANEO;
    private Integer horaInicio = 0;
    private Integer horaFim = 23;
    private String cron = "";
    private Date ultimaExecucao;
    private Boolean todosMeses = true;
    private Boolean todosDias = true;
    private Boolean todosDiasSemana = true;
    private Date dataInicial = Calendario.hoje();
    private List<GenericoTO> diasSemana = new ArrayList<GenericoTO>();
    private List<GenericoTO> diasMes = new ArrayList<GenericoTO>();
    private List<GenericoTO> mes = new ArrayList<GenericoTO>();

    public void montarCronDataAtual() throws ConsistirException {
        horaInicio = Uteis.gethoraHH(Calendario.hoje());
        montarCron();

    }

    public MailingAgendamentoVO() {
        montarListaDia();
        montarListaDiasSemana();
        montarListaMes();
    }

    private void montarListaDiasSemana() {
        diasSemana = new ArrayList<GenericoTO>();
        //para o CRON o Domingo é ZERO ou 7
        for (DiaSemanaCRON dia : DiaSemanaCRON.values()) {
            diasSemana.add(new GenericoTO(dia.getNumeral(), dia.getDescricaoSimples()));
        }
    }

    private void montarListaMes() {
        mes = new ArrayList<GenericoTO>();
        for (Mes mes : Mes.values()) {
            if (!UteisValidacao.emptyString(mes.getDescricao())) {
                this.mes.add(new GenericoTO(mes.getCodigo(), mes.getDescricao()));
            }
        }
    }

    private void montarListaDia() {
        diasMes = new ArrayList<GenericoTO>();
        for (Integer i = 1; i <= 31; i++) {
            diasMes.add(new GenericoTO(i, i.toString()));
        }
    }

    public void validarDadosCron(String dia, String diaSemana, String meses) throws ConsistirException {
        String mensagem = "";
        if (!UteisValidacao.emptyNumber(horaInicio) && !UteisValidacao.emptyNumber(horaFim) && horaFim < horaInicio) {
            mensagem = "Hora Início não pode ser maior do que Hora Fim.";
        }
        if (ocorrencia.equals(OcorrenciaEnum.SEMANALMENTE) && UteisValidacao.emptyString(diaSemana)) {
            mensagem = mensagem + "\nInforme o dia, ou dias, da semana em que você deseja agendar o envio.";
        }
        if (ocorrencia.equals(OcorrenciaEnum.MENSALMENTE) && UteisValidacao.emptyString(dia)) {
            mensagem = mensagem + "\nInforme o dia, ou dias, do mês em que você deseja agendar o envio.";
        }
        if (ocorrencia.equals(OcorrenciaEnum.MENSALMENTE) && UteisValidacao.emptyString(meses)) {
            mensagem = mensagem + "\nInforme o mês, ou meses, em que você deseja agendar o envio.";
        }
        if (!UteisValidacao.emptyString(mensagem)) {
            throw new ConsistirException(mensagem);
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getMalaDireta() {
        return malaDireta;
    }

    public void setMalaDireta(int malaDireta) {
        this.malaDireta = malaDireta;
    }

    public OcorrenciaEnum getOcorrencia() {
        return ocorrencia;
    }

    public void setOcorrencia(OcorrenciaEnum ocorrencia) {
        this.ocorrencia = ocorrencia;
    }

    public Integer getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(Integer horaInicio) {
        this.horaInicio = horaInicio;
    }

    public Integer getHoraFim() {
        return horaFim;
    }

    public void setHoraFim(Integer horaFim) {
        this.horaFim = horaFim;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public Date getUltimaExecucao() {
        return ultimaExecucao;
    }

    public void setUltimaExecucao(Date ultimaExecucao) {
        this.ultimaExecucao = ultimaExecucao;
    }

    public List<GenericoTO> getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(List<GenericoTO> diasSemana) {
        this.diasSemana = diasSemana;
    }

    public List<GenericoTO> getDiasMes() {
        return diasMes;
    }

    public void setDiasMes(List<GenericoTO> diasMes) {
        this.diasMes = diasMes;
    }

    public List<GenericoTO> getMes() {
        return mes;
    }

    public void setMes(List<GenericoTO> mes) {
        this.mes = mes;
    }

    public void setTodosMeses(Boolean todosMeses) {
        this.todosMeses = todosMeses;
    }

    public Boolean getTodosMeses() {
        return todosMeses;
    }

    public void setTodosDias(Boolean todosDias) {
        this.todosDias = todosDias;
    }

    public Boolean getTodosDias() {
        return todosDias;
    }

    public void setTodosDiasSemana(Boolean todosDiasSemana) {
        this.todosDiasSemana = todosDiasSemana;
    }

    public Boolean getTodosDiasSemana() {
        return todosDiasSemana;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    private String montarString(List<GenericoTO> lista, Boolean todos) {
        String str = "";
        if (todos) {
            str = "*";
        } else {
            for (GenericoTO gen : lista) {
                str = str + (gen.getSelecionado() ? "," + gen.getCodigo() : "");
            }
            str = str.replaceFirst(",", "");
        }
        return str;
    }

    public void montarCron() throws ConsistirException {
        if (UteisValidacao.emptyNumber(horaInicio)) {
            horaInicio = 0;
        }
        if (UteisValidacao.emptyNumber(horaFim)) {
            horaFim = 23;
        }
        String hora = "(" + horaInicio + "-" + horaFim + ")";
        String dia = "";
        String diaSemana = "";
        String meses = "";

        if(getOcorrencia().equals(OcorrenciaEnum.VENDA_CONTRATO) ||
                getOcorrencia().equals(OcorrenciaEnum.APOS_MATRICULA) ||
                getOcorrencia().equals(OcorrenciaEnum.APOS_REMATRICULA) ||
                getOcorrencia().equals(OcorrenciaEnum.APOS_RENOVACAO) ||
                getOcorrencia().equals(OcorrenciaEnum.APOS_CANCELAMENTO)
        ){
            dia = "*";
            diaSemana = "*";
            meses = "*";
        }


        switch (ocorrencia) {
            case INSTANTANEO:
                dia = "" + Uteis.getDiaMesData(dataInicial);
                diaSemana = "*";
                meses = "" + Uteis.getMesData(dataInicial);
                break;
            case DIARIAMENTE:
                dia = "*";
                diaSemana = "*";
                meses = "*";
                break;
            case SEMANALMENTE:
                diaSemana = todosDiasSemana ? "1-7" : montarString(diasSemana, todosDiasSemana);
                dia = "*";
                meses = "*";
                break;
            case MENSALMENTE:
                diaSemana = "*";
                dia = montarString(diasMes, todosDias);
                meses = todosMeses ? "*" : montarString(mes, todosMeses);
                break;
        }

        if(getOcorrencia().equals(OcorrenciaEnum.INCLUSAO_VISITANTE)){
            cron = "";
        }else{
            validarDadosCron(dia, diaSemana, meses);
            cron = "H\tH" + hora + "\t" + dia + "\t" + meses + "\t" + diaSemana;
        }

    }

    public void interpretarCRON() {
        String[] split = getCron().split("\t");
        String hora = split[1];
        String dia = split[2];
        String mes = split[3];
        String diaSemana = split[4];
        horaInicio = Integer.valueOf(hora.substring(2, hora.indexOf("-")));
        horaFim = Integer.valueOf(hora.substring(hora.indexOf("-") + 1, hora.indexOf(")")));

        verificarOcorrencia(OcorrenciaEnum.DIARIAMENTE, dia);
        verificarOcorrencia(OcorrenciaEnum.SEMANALMENTE, diaSemana);
        verificarOcorrencia(OcorrenciaEnum.MENSALMENTE, mes);

        todosDias = verificarSelecionados(diasMes, dia);
        todosDiasSemana = verificarSelecionados(diasSemana, diaSemana);
        todosMeses = verificarSelecionados(this.mes, mes);
    }

    private boolean verificarSelecionados(List<GenericoTO> genericos, String param) {
        if (param.equals("*")) {
            return Boolean.TRUE;
        }
        param = param.contains("/") ? param.substring(param.indexOf("/") + 1, param.length()) : param;
        if (param.contains(",")) {
            String[] split = param.split(",");
            for (String s : split) {
                marcarGenerico(genericos, s);
            }
        }else if(param.contains("-")){
            String[] split = param.split("-");
            Integer primeiro = Integer.valueOf(split[0]);
            Integer segundo = Integer.valueOf(split[1]);
            for(int i = primeiro;i <= segundo; i++ ){
                marcarGenerico(genericos, String.valueOf(i));
            }
        }else {
            marcarGenerico(genericos, param);
        }
        return false;

    }

    public String getDescricaoAgendamento() {
        String descricao = ocorrencia.getDescricao() + " - ";
        switch (ocorrencia) {
            case DIARIAMENTE:
                break;

            case SEMANALMENTE:
                if (todosDiasSemana) {
                    descricao += "todos os dias da semana - ";
                } else {
                    boolean toda = true;
                    for (GenericoTO g : diasSemana) {
                        if (g.getSelecionado()) {
                            DiaSemanaCRON diaSemana = DiaSemanaCRON.getDiaSemanaNumeral(g.getCodigo());
                            descricao += (toda ? ((diaSemana.equals(DiaSemanaCRON.DOMINGO) || diaSemana.equals(DiaSemanaCRON.SABADO)) ? "todo " : "toda ") : "");
                            descricao += ", " + diaSemana.getDescricaoSimples();
                            toda = false;
                        }
                    }
                    descricao = descricao.replaceFirst(",", "") + (toda ? "" : " - ");
                }
                break;
            case MENSALMENTE:
                descricao += montarDescricaoGenerico("dia", "dias", diasMes, todosDias);
                descricao += montarDescricaoGenerico("mês de", "meses", mes, todosMeses);
                break;
        }
        descricao += "entre " + formatarHora(horaInicio) + " e " + formatarHora(horaFim) + "h iniciando em " + Uteis.getDataComHHMM(dataInicial);
        return descricao;

    }

    private String montarDescricaoGenerico(String label, String label2, List<GenericoTO> genericos, boolean sempre) {
        String descricao = "";
        if (sempre) {
            descricao += "todos os " + label2 + " - ";
        } else {
            boolean toda = true;
            for (GenericoTO g : genericos) {
                if (g.getSelecionado()) {
                    descricao += (toda ? "todo " + label + " " : "");
                    descricao += ", " + g.getLabel();
                    toda = false;
                }
            }
            descricao = descricao.replaceFirst(", ", "") + (toda ? "" : " - ");
        }
        return descricao;
    }

    private String formatarHora(Integer hora) {
        return (hora.toString().length() > 1 ? "" : "0") + hora.toString() + ":00";
    }

    /**
     * Responsável por
     * <AUTHOR> Alcides
     * 24/04/2013
     */
    private void marcarGenerico(List<GenericoTO> genericos, String s) {
        GenericoTO g = new GenericoTO(Integer.valueOf(Integer.valueOf(s)), "");
        GenericoTO genericoTO = genericos.get(genericos.indexOf(g));
        genericoTO.setSelecionado(true);
    }

    private void verificarOcorrencia(OcorrenciaEnum oc, String param) {
        if (param.contains("/")) {
            ocorrencia = oc;
//            repeticao = Integer.valueOf(param.substring(param.indexOf("/") + 1, param.length()));
        }
    }

//    public static void main(String[] args) throws java.lang.Exception {
//        Connection con = DriverManager.getConnection("****************************************", "postgres", "pactodb");
//        MailingAgendamento mai = new MailingAgendamento(con);
//        MalaDiretaVO m = new MalaDiretaVO();
//        m.setCodigo(107);
//        MailingAgendamentoVO ma = mai.consultar(m);
//        System.out.println(ma.getCron());
//        ma.interpretarCRON();
//        System.out.println(ma.getDescricaoAgendamento());
//
//    }
}

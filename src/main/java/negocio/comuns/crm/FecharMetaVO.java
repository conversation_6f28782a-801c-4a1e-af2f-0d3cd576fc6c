package negocio.comuns.crm;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoFaseCRM;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Reponsável por manter os dados da entidade FecharMeta. Classe do tipo VO - Value Object composta pelos atributos da entidade com
 * visibilidade protegida e os métodos de acesso a estes atributos. Classe utilizada para apresentar e manter em memória os dados desta
 * entidade.
 *
 * @see SuperVO
 */
public class FecharMetaVO extends SuperVO {

    protected Integer codigo;
    protected Date dataRegistro;
    protected Double meta;
    protected Double metaAtingida;
    protected Double repescagem;
    protected Double porcentagem;
    protected String justificativa;
    protected String identificadorMeta;
    protected String abaSelecionada;
    protected Integer ordem;
    protected Boolean marcaTodosHoje;
    protected Boolean marcaTodosMes;
    protected Boolean marcaTodosHistorico;
    protected Long totalizadorSelecionadoHoje;
    protected Long totalizadorSelecionadoHistorico;
    private List<FecharMetaDetalhadoVO> fecharMetaDetalhadoVOs;
    private AberturaMetaVO aberturaMetaVO;
    private Integer totalizador;
    private List ListaHistoricoContatoMes;
    private List<FecharMetaDetalhadoVO> ListaHistoricoContatoHistorico;
    private Double totalApresentarRelatorio;
    private Map<String, Integer> totaisOrigens;
    private Integer contAgendaComparecimento;
    private Integer contReAgendados;
    private Integer contFechamentos;
    private Integer contLigacao;
    private String perc;
    private Boolean metaCalculada;
    private FasesCRMEnum fase;
    private String codigosConcat;
    private String nomeMeta;
    private MalaDiretaVO malaDiretaCRMExtra;
    @NaoControlarLogAlteracao
    private List<FecharMetaVO> listaFecharMetaRelacionado;
    @NaoControlarLogAlteracao
    private String codigoMetaExtraSomada;
    @NaoControlarLogAlteracao
    private Integer qtdLigacoesMeta;
    @NaoControlarLogAlteracao
    private Integer qtdContatosSemSucesso;
    @NaoControlarLogAlteracao
    private Integer qtdContatosComSucesso;

    public FecharMetaVO() {
        super();
        inicializarDados();
    }

    public static void validarDados(FecharMetaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getMeta() == null) {
            throw new ConsistirException(("O campo Meta está vazio !"));
        }
        if (obj.getIdentificadorMeta().equals("")) {
            throw new ConsistirException(("Não foi encontrado nenhum identificador de meta !"));
        }
    }

    public void realizarUpperCaseDados() {
        setJustificativa(getJustificativa().toUpperCase());
        setIdentificadorMeta(getIdentificadorMeta().toUpperCase());
    }
    public int getOrdemTotalizadorMeta(){
        fase = FasesCRMEnum.getFasePorSigla(getIdentificadorMeta());
        if(fase!=null)
        return fase.getOrdemTotalizador();
        else
          return 1;
    }
    public void inicializarDados() {
        setCodigo(null);
        setMeta(0.0);
        setMetaAtingida(0.0);
        setPorcentagem(0.0);
        setJustificativa("");
        setIdentificadorMeta("");
        setFecharMetaDetalhadoVOs(new ArrayList<FecharMetaDetalhadoVO>());
        setTotalizador(0);
        setAberturaMetaVO(new AberturaMetaVO());
        setPerc("");
    }

    public String getCorMeta() {
        if (identificadorMeta.equals("TO")) {
            return "background-color: #9DABA6;";
        }
        return "";
    }



    public void adicionarObjFecharMetaDetalhadoVOs(FecharMetaDetalhadoVO obj) throws Exception {
        FecharMetaDetalhadoVO.validarDados(obj);
        obj.setFecharMeta(this);
        int index = 0;
        for (FecharMetaDetalhadoVO objExistente : getFecharMetaDetalhadoVOs()) {
            if (obj.getCliente().getCodigo() != 0 && objExistente.getCliente().getCodigo().equals(obj.getCliente().getCodigo())) {
                getFecharMetaDetalhadoVOs().set(index, obj);
                return;
            }
            if (obj.getPassivo().getCodigo() != 0 && objExistente.getPassivo().getCodigo().equals(obj.getPassivo().getCodigo())) {
                getFecharMetaDetalhadoVOs().set(index, obj);
                return;
            }
            if (obj.getIndicado().getCodigo() != 0
                    && objExistente.getIndicado().getCodigo().equals(obj.getIndicado().getCodigo())) {
                getFecharMetaDetalhadoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getFecharMetaDetalhadoVOs().add(obj);
    }

    public void excluirObjFecharMetaDetalhadoVOs(Integer cliente) throws Exception {
        int index = 0;
        Iterator i = getFecharMetaDetalhadoVOs().iterator();
        while (i.hasNext()) {
            FecharMetaDetalhadoVO objExistente = (FecharMetaDetalhadoVO) i.next();
            if (objExistente.getCliente().getCodigo().equals(cliente)) {
                getFecharMetaDetalhadoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public FecharMetaDetalhadoVO consultarObjFecharMetaDetalhadoVO(Integer cliente) throws Exception {
        Iterator i = getFecharMetaDetalhadoVOs().iterator();
        while (i.hasNext()) {
            FecharMetaDetalhadoVO objExistente = (FecharMetaDetalhadoVO) i.next();
            if (objExistente.getCliente().getCodigo().equals(cliente)) {
                return objExistente;
            }
        }
        return null;
    }

    public void calcularPorcentagem() {
        if (getMeta().intValue() == 0 || (getMetaAtingida()+getRepescagem() == 0)) {
            setPorcentagem(0.0);
        } else {
            setPorcentagem((((getMetaAtingida() + getRepescagem()) / getMeta()) * 100.0));
            setPerc(Formatador.formatarValorMonetarioSemMoeda(getPorcentagem()) + "%");
        }
    }

    public Boolean getIsIndicadorDeVendas() {
        FasesCRMEnum fase = FasesCRMEnum.getFasePorSigla(getIdentificadorMeta());
        return fase != null && fase.getTipoFase().equals(TipoFaseCRM.VENDAS);
    }
    public String getCorPorcentagemBox(){
        if(getResultadoPercentualMetaAtingido() <=40)
            return "color: red;";
        if(getResultadoPercentualMetaAtingido() >40 && getResultadoPercentualMetaAtingido() <=79)
            return "color: #0078D0;";
        if(getResultadoPercentualMetaAtingido() >80 && getResultadoPercentualMetaAtingido() <=100)
            return "color:#006633;";
        return "color: rgb(71, 71, 71);";
    }

    public Boolean getIsMetaAtingidaSucesso() {
        return (getMeta().longValue() <= getMetaAtingida().longValue()) || (!getJustificativa().trim().equals(""));
    }

    public Boolean getIsMetaAtingidaJustificativa() {
        return getMeta().longValue() > getMetaAtingida().longValue();
    }

    public Boolean getIsIndicadorDeRetencao() {
        FasesCRMEnum fase = FasesCRMEnum.getFasePorSigla(getIdentificadorMeta());
        return fase != null && fase.getTipoFase().equals(TipoFaseCRM.RETENCAO);
    }

    public List<FecharMetaDetalhadoVO> getFecharMetaDetalhadoVOs() {
        if (fecharMetaDetalhadoVOs == null) {
            fecharMetaDetalhadoVOs = new ArrayList<FecharMetaDetalhadoVO>();
        }
        return (fecharMetaDetalhadoVOs);
    }

    public void setFecharMetaDetalhadoVOs(List<FecharMetaDetalhadoVO> fecharMetaDetalhadoVOs) {
        this.fecharMetaDetalhadoVOs = fecharMetaDetalhadoVOs;
    }

    public String getIdentificadorMeta() {
        if (identificadorMeta == null) {
            identificadorMeta = "";
        }
        return (identificadorMeta);
    }

    public void setIdentificadorMeta(String identificadorMeta) {
        this.identificadorMeta = identificadorMeta;
    }

    public boolean isApresentarColunaTempoExAluno() {
        return getFase().equals(FasesCRMEnum.EX_ALUNOS);
    }

    public boolean isApresentarColunaRisco() {
        return getFase().equals(FasesCRMEnum.SEM_AGENDAMENTO) ||
                getFase().equals(FasesCRMEnum.ULTIMAS_SESSOES);
    }
    public Boolean getQuebrarLinhaPorcentagem(){
        if(getIdentificadorMeta_Apresentar().length()< 20)
            return true;
        else
            return false;
    }
    public String getIdentificadorMeta_Apresentar() {
        if (identificadorMeta == null) {
            identificadorMeta = "";
        }
        if (identificadorMeta.equals("TO")) {
            return "Totais";
        }
        return FasesCRMEnum.getIdentificador(identificadorMeta);

    }

    public String getIdentificadorMeta_ApresentarMeta() {
        if (identificadorMeta == null) {
            return "";
        }

        FasesCRMEnum fase = FasesCRMEnum.getFasePorSigla(getIdentificadorMeta());
        return fase == null ? "" : fase.getDescricao();
    }

    public String getCss_Apresentar() {
        if ((getIdentificadorMeta().equals("CP") || getIdentificadorMeta().equals("MF") || getIdentificadorMeta().equals("MQ")) || !getCss_ApresentarReadOnly()) {
            return "border-style:solid;background-color: #F4A460";
        }
        return "";
    }

    public Boolean getCss_ApresentarReadOnly() {
        return !(getIdentificadorMeta().equals("CP") || getIdentificadorMeta().equals("MF") || getIdentificadorMeta().equals("MQ"));
    }

    public String getJustificativa() {
        if (justificativa == null) {
            justificativa = "";
        }
        return (justificativa);
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Double getPorcentagem() {
        if (porcentagem == null) {
            porcentagem = 0.0;
        }
        return (porcentagem);
    }

    public void setPorcentagem(Double porcentagem) {
        this.porcentagem = porcentagem;
    }

    public Double getMetaAtingida() {
        if (metaAtingida == null) {
            metaAtingida = 0.0;
        }
        return (metaAtingida);
    }

    public void setMetaAtingida(Double metaAtingida) {
        this.metaAtingida = metaAtingida;
    }

    public Double getMeta() {
        if (meta == null) {
            meta = 0.0;
        }
        return (meta);
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }
    public String getMetaBoxResultado_Apresentar(){
      return getMetaAtingida_Apresentar()+"/"+getMeta_Apresentar();
    }
    public String getPorcentagemBoxResultado_Apresentar(){
        DecimalFormat df = new DecimalFormat("00.00");
        return df.format(porcentagem).replace(",", ".");
    }

    public String getMeta_Apresentar() {
        DecimalFormat format = new DecimalFormat("0");
        return format.format(getMeta());
    }
    public Double getResultadoPercentualMetaAtingido(){
        if(metaAtingida==0)
            return 0.0;
        return (getMetaAtingida()*100)/getMeta();
    }
    public String getMetaAtingida_Apresentar() {
        DecimalFormat format = new DecimalFormat("0");
        return format.format(getMetaAtingida());
    }

    public String getRepescagem_Apresentar() {
        DecimalFormat format = new DecimalFormat("0");
        return format.format(getRepescagem());
    }

    public Date getDataRegistro() {
        if (dataRegistro == null) {
            dataRegistro = Calendario.hoje();
        }
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataRegistro_Apresentar() {
        return (Uteis.getDataComHora(dataRegistro));
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getTotalizador() {
        return totalizador;
    }

    public void setTotalizador(Integer totalizador) {
        this.totalizador = totalizador;
    }

    public AberturaMetaVO getAberturaMetaVO() {
        if (aberturaMetaVO == null) {
            aberturaMetaVO = new AberturaMetaVO();
        }
        return aberturaMetaVO;
    }

    public void setAberturaMetaVO(AberturaMetaVO aberturaMetaVO) {
        this.aberturaMetaVO = aberturaMetaVO;
    }

    public Boolean getApresentarImagemAgendamentos() {
        return getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla());
    }

    public Boolean getApresentarImagemVinteQuatroHoras() {
        return getIdentificadorMeta().equals(FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla());
    }

    public Boolean getApresentarImagemVisitantesAntigos() {
        return getIdentificadorMeta().equals(FasesCRMEnum.VISITANTES_ANTIGOS.getSigla());
    }

    public Boolean getApresentarImagemRenovacoes() {
        return getIdentificadorMeta().equals(FasesCRMEnum.RENOVACAO.getSigla());
    }

    public Boolean getApresentarImagemPosVenda() {
        return getIdentificadorMeta().equals(FasesCRMEnum.POS_VENDA.getSigla());
    }

    public Boolean getApresentarImagemIndicacoes() {
        return getIdentificadorMeta().equals(FasesCRMEnum.INDICACOES.getSigla());
    }

    public Boolean getApresentarImagemClientePotencial() {
        return getIdentificadorMeta().equals("CP");
    }

    public Boolean getApresentarImagemConversaoAgendados() {
        return getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla());
    }

    public Boolean getApresentarImagemConversaoExAlunos() {
        return getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla());
    }

    public Boolean getApresentarImagemConversaoVisitantesAntigos() {
        return getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla());
    }

    public Boolean getApresentarImagemConversaoIndicados() {
        return getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla());
    }

    public Boolean getFaseMetaIndicados() {
        return getIdentificadorMeta().equals(FasesCRMEnum.INDICACOES.getSigla());
    }

    public Boolean getApresentarImagemConversaoDesistentes() {
        return getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla());
    }

    public Boolean getApresentarImagemLigacaoAgendadosAmanha() {
        return getIdentificadorMeta().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla());
    }

    public Boolean getApresentarImagemExAlunos() {
        return getIdentificadorMeta().equals(FasesCRMEnum.EX_ALUNOS.getSigla());
    }

    public Boolean getApresentarImagemRisco() {
        return getIdentificadorMeta().equals("RI");
    }

    public Boolean getApresentarImagemAniversariantes() {
        return getIdentificadorMeta().equals("AN");
    }

    public Boolean getApresentarImagemFaturamento() {
        return getIdentificadorMeta().equals("MF");
    }

    public Boolean getApresentarImagemQtdVenda() {
        return getIdentificadorMeta().equals("MQ");
    }

    public Boolean getApresentarImagemFaltosos() {
        return getIdentificadorMeta().equals("FA");
    }

    public Boolean getApresentarImagemPerdas() {
        return getIdentificadorMeta().equals("PE");
    }

    public Boolean getApresentarImagemVencidos() {
        return getIdentificadorMeta().equals("VE");
    }

    public Long getTotalizadorHoje() {
        if (this.getFecharMetaDetalhadoVOs() == null) {
            return 0L;
        }

        return (long) getFecharMetaDetalhadoVOs().size();
    }

    public Long getTotalizadorMes() {
        if (this.getListaHistoricoContatoMes() == null) {
            return 0L;
        }

        return (long) getListaHistoricoContatoMes().size();
    }

    public Long getTotalizadorHistorico() {
        if (this.getListaHistoricoContatoHistorico() == null) {
            return 0L;
        }

        return (long) getListaHistoricoContatoHistorico().size();
    }

    public String getAbaSelecionada() {
        if (abaSelecionada == null) {
            abaSelecionada = "";
        }
        return abaSelecionada;
    }

    public void setAbaSelecionada(String abaSelecionada) {
        this.abaSelecionada = abaSelecionada;
    }

    public Boolean getIsAbaSelecionadaHoje() {
        return getAbaSelecionada().equals("HJ");
    }

    public Boolean getIsAbaSelecionadaHistorico() {
        return getAbaSelecionada().equals("HI");
    }

    public Integer getOrdem() {
        if (ordem == null) {
            ordem = 0;
        }
        if (getIdentificadorMeta().equals(FasesCRMEnum.EX_ALUNOS.getSigla())) {
            ordem = -2;
        } else if (getIdentificadorMeta().equals("AG")) {
            ordem = -1;
        } else if (getIdentificadorMeta().equals("LA")) {
            ordem = 0;
        } else if (getIdentificadorMeta().equals("HO")) {
            ordem = 1;
        } else if (getIdentificadorMeta().equals(FasesCRMEnum.VISITANTES_ANTIGOS.getSigla())) {
            ordem = 2;
        } else if (getIdentificadorMeta().equals("RE")) {
            ordem = 3;
        } else if (getIdentificadorMeta().equals("PV")) {
            ordem = 4;
        } else if (getIdentificadorMeta().equals("MF")) {
            ordem = 5;
        } else if (getIdentificadorMeta().equals("MQ")) {
            ordem = 6;
        } else if (getIdentificadorMeta().equals("IN")) {
            ordem = 7;
        } else if (getIdentificadorMeta().equals("CP")) {
            ordem = 8;
        } else if (getIdentificadorMeta().equals("RI")) {
            ordem = 9;
        } else if (getIdentificadorMeta().equals("AN")) {
            ordem = 10;
        } else if (getIdentificadorMeta().equals("FA")) {
            ordem = 11;
        } else if (getIdentificadorMeta().equals("PE")) {
            ordem = 12;
        } else if (getIdentificadorMeta().equals("VE")) {
            ordem = 13;
        } else if (getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())) {
            ordem = 14;
        }
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public List<FecharMetaDetalhadoVO> getListaHistoricoContatoMes() {
        if (ListaHistoricoContatoMes == null) {
            ListaHistoricoContatoMes = new ArrayList();
        }
        return ListaHistoricoContatoMes;
    }

    public void setListaHistoricoContatoMes(List listaHistoricoContatoMes) {
        ListaHistoricoContatoMes = listaHistoricoContatoMes;
    }

    public List<FecharMetaDetalhadoVO> getListaHistoricoContatoHistorico() {
        if (ListaHistoricoContatoHistorico == null) {
            ListaHistoricoContatoHistorico = new ArrayList<FecharMetaDetalhadoVO>();
        }
        return ListaHistoricoContatoHistorico;
    }

    public void setListaHistoricoContatoHistorico(List listaHistoricoContatoHistorico) {
        ListaHistoricoContatoHistorico = listaHistoricoContatoHistorico;
    }

    public Boolean getMarcaTodosHoje() {
        if (marcaTodosHoje == null) {
            marcaTodosHoje = false;
        }
        return marcaTodosHoje;
    }

    public void setMarcaTodosHoje(Boolean marcaTodosHoje) {
        this.marcaTodosHoje = marcaTodosHoje;
    }

    public Boolean getMarcaTodosMes() {
        return marcaTodosMes;
    }

    public void setMarcaTodosMes(Boolean marcaTodosMes) {
        this.marcaTodosMes = marcaTodosMes;
    }

    public Boolean getMarcaTodosHistorico() {
        return marcaTodosHistorico;
    }

    public void setMarcaTodosHistorico(Boolean marcaTodosHistorico) {
        this.marcaTodosHistorico = marcaTodosHistorico;
    }

    public Long getTotalizadorSelecionadoHoje() {
        if (totalizadorSelecionadoHoje == null) {
            totalizadorSelecionadoHoje = 0l;
        }
        return totalizadorSelecionadoHoje;
    }

    public void setTotalizadorSelecionadoHoje(Long totalizadorSelecionadoHoje) {
        this.totalizadorSelecionadoHoje = totalizadorSelecionadoHoje;
    }

    public Long getTotalizadorSelecionadoHistorico() {
        if (totalizadorSelecionadoHistorico == null) {
            totalizadorSelecionadoHistorico = 0l;
        }
        return totalizadorSelecionadoHistorico;
    }

    public void setTotalizadorSelecionadoHistorico(Long totalizadorSelecionadoHistorico) {
        this.totalizadorSelecionadoHistorico = totalizadorSelecionadoHistorico;
    }

    public Double getRepescagem() {
        if (repescagem == null) {
            repescagem = 0d;
        }
        return repescagem;
    }

    public void setRepescagem(Double repescagem) {
        this.repescagem = repescagem;
    }

    public Boolean getApresentarMetaAgendamento() {
        return getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla())
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaAgendamento();
    }

    public Boolean getApresentarMetaLigacaoAgendamento() {
        return getIdentificadorMeta().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla())
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaLigacaoAgendamentoAmanha();
    }

    public Boolean getApresentarMetaConversaoAgendados() {
        return getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaConversaoAgendados();
    }

    public Boolean getApresentarMetaVencidos() {
        return getIdentificadorMeta().equals("VE")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaVencidos();
    }

    public Boolean getApresentarMetaVinteQuatroHoras() {
        return getIdentificadorMeta().equals("HO")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaVinteQuatroHoras();
    }

    public Boolean getApresentarMetaRenovacao() {
        return getIdentificadorMeta().equals("RE")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaRenovacao();
    }

    public Boolean getApresentarMetaPosVenda() {
        return getIdentificadorMeta().equals("PV")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaPosVenda();
    }

    public Boolean getApresentarMetaFaturamento() {
        return getIdentificadorMeta().equals("MF")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaFaturamento();
    }

    public Boolean getApresentarMetaQtdeVendas() {
        return getIdentificadorMeta().equals("MQ")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaQtdeVenda();
    }

    public Boolean getApresentarMetaIndicados() {
        return getIdentificadorMeta().equals("IN")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaIndicado();
    }

    public Boolean getApresentarMetaPassivo() {
        return getIdentificadorMeta().equals("CP")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaPassivo();
    }

    public Boolean getApresentarMetaGrupoRisco() {
        return getIdentificadorMeta().equals("RI")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaGrupoRisco();
    }

    public Boolean getApresentarMetaFaltosos() {
        return getIdentificadorMeta().equals("FA")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaFaltosos();
    }

    public Boolean getApresentarMetaAniversariante() {
        return getIdentificadorMeta().equals("AN")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaAniversariante();
    }

    public Boolean getApresentarMetaPerda() {
        return getIdentificadorMeta().equals("PE")
                && getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO().getMetaPerda();
    }

    public Double getTotalApresentarRelatorio() {
        if (totalApresentarRelatorio == null) {
            totalApresentarRelatorio = 0.0;
        }
        return totalApresentarRelatorio;
    }

    public void setTotalApresentarRelatorio(Double totalApresentarRelatorio) {
        this.totalApresentarRelatorio = totalApresentarRelatorio;
    }

    public Map<String, Integer> getTotaisOrigens() {
        if (totaisOrigens == null) {
            totaisOrigens = new HashMap<String, Integer>();
        }
        return totaisOrigens;
    }

    public void setTotaisOrigens(Map<String, Integer> totais) {
        this.totaisOrigens = totais;
    }

    public Integer getContAgendaComparecimento() {
        if (contAgendaComparecimento == null) {
            contAgendaComparecimento = 0;
        }
        return contAgendaComparecimento;
    }

    public void setContAgendaComparecimento(Integer contAgendaComparecimento) {
        this.contAgendaComparecimento = contAgendaComparecimento;
    }

    public Integer getContReAgendados() {
        if (contReAgendados == null) {
            contReAgendados = 0;
        }
        return contReAgendados;
    }

    public void setContReAgendados(Integer contReAgendados) {
        this.contReAgendados = contReAgendados;
    }

    public Integer getContFechamentos() {
        if (contFechamentos == null) {
            contFechamentos = 0;
        }
        return contFechamentos;
    }

    public void setContFechamentos(Integer contFechamentos) {
        this.contFechamentos = contFechamentos;
    }

    public Boolean getApresentarMensagemRetroativo() {
        if (getIdentificadorMeta().equals("RI") || getIdentificadorMeta().equals("FA") || getIdentificadorMeta().equals("PV")
                || getIdentificadorMeta().equals("PE")) {
            return this.getAberturaMetaVO().getAberturaRetroativa();
        } else {
            return Boolean.FALSE;
        }
    }

    public String getPerc() {
        return perc;
    }

    public void setPerc(String perc) {
        this.perc = perc;
    }

    public String getPorcentagemApresentar() {
        String porc;
            if(porcentagem !=null && porcentagem > 0){
            porc = Formatador.formatarValorMonetarioSemMoeda(porcentagem);
            if (!this.getIdentificadorMeta().equals("MF")) {
                porc = porc.substring(0, porc.indexOf(","));
            }
            }else{
                porc = "00.00";
            }

            return porc;

    }

    public Boolean getMetaCalculada() {
        if (metaCalculada == null) {
            metaCalculada = Boolean.TRUE;
        }
        return metaCalculada;
    }

    public void setMetaCalculada(Boolean metaCalculada) {
        this.metaCalculada = metaCalculada;
    }

    public Integer getOrdemTotalizador() {
        int ordemTotalizador = -1;
        FasesCRMEnum fase = FasesCRMEnum.getFasePorSigla(getIdentificadorMeta());
        if (fase != null) {
            ordemTotalizador = fase.getOrdemTotalizador();
        }
        return ordemTotalizador;
    }

    public Integer getContLigacao() {
        if (contLigacao == null) {
            contLigacao = 0;
        }
        return contLigacao;
    }

    public void setContLigacao(Integer contLigacao) {
        this.contLigacao = contLigacao;
    }

    public FasesCRMEnum getFase() {
        return fase;
    }

    public void setFase(FasesCRMEnum fase) {
        this.fase = fase;
    }

    public String getCodigosConcat() {
        return codigosConcat;
    }

    public void setCodigosConcat(String codigosConcat) {
        this.codigosConcat = codigosConcat;
    }

    public String getNomeMeta() {
        if (nomeMeta == null) {
            nomeMeta = "";
        }
        return nomeMeta;
    }

    public void setNomeMeta(String nomeMeta) {
        this.nomeMeta = nomeMeta;
    }

    public MalaDiretaVO getMalaDiretaCRMExtra() {
        if (malaDiretaCRMExtra == null) {
            malaDiretaCRMExtra = new MalaDiretaVO();
        }
        return malaDiretaCRMExtra;
    }

    public void setMalaDiretaCRMExtra(MalaDiretaVO malaDiretaCRMExtra) {
        this.malaDiretaCRMExtra = malaDiretaCRMExtra;
    }

    public String getNomeMetaCRMExtra_Apresentar() {
        if (nomeMeta == null) {
            nomeMeta = "";
        }
        return getNomeMeta();
    }

    public String getPorcentagem_ApresentarBI() {
        if (porcentagem == null) {
            porcentagem = 0.0;
        }
        return Formatador.formatarValorMonetarioSemMoeda(porcentagem) + " %";
    }

    public String getDataRegistro_ApresentarBI() {
        return (Uteis.getData(dataRegistro));
    }

    public String getNomeImpressao() {
        if (getFase().getTipoFase().equals(TipoFaseCRM.CRMEXTRA)) {
            return getNomeMeta();
        } else {
            return getIdentificadorMeta_Apresentar();
        }
    }

    public List<FecharMetaVO> getListaFecharMetaRelacionado() {
        if (listaFecharMetaRelacionado == null) {
            listaFecharMetaRelacionado = new ArrayList<>();
        }
        return listaFecharMetaRelacionado;
    }

    public void setListaFecharMetaRelacionado(List<FecharMetaVO> listaFecharMetaRelacionado) {
        this.listaFecharMetaRelacionado = listaFecharMetaRelacionado;
    }

    public String getCodigoMetaExtraSomada() {
        if (codigoMetaExtraSomada == null) {
            codigoMetaExtraSomada = "";
        }
        return codigoMetaExtraSomada;
    }

    public void setCodigoMetaExtraSomada(String codigoMetaExtraSomada) {
        this.codigoMetaExtraSomada = codigoMetaExtraSomada;
    }

    public Integer getQtdLigacoesMeta() {
        if (qtdLigacoesMeta == null) {
            qtdLigacoesMeta = 0;
        }
        return qtdLigacoesMeta;
    }

    public void setQtdLigacoesMeta(Integer qtdLigacoesMeta) {
        this.qtdLigacoesMeta = qtdLigacoesMeta;
    }

    public Integer getQtdContatosSemSucesso() {
        if (qtdContatosSemSucesso == null) {
            qtdContatosSemSucesso = 0;
        }
        return qtdContatosSemSucesso;
    }

    public void setQtdContatosSemSucesso(Integer qtdContatosSemSucesso) {
        this.qtdContatosSemSucesso = qtdContatosSemSucesso;
    }

    public Integer getQtdContatosComSucesso() {
        if (qtdContatosComSucesso == null) {
            qtdContatosComSucesso = 0;
        }
        return qtdContatosComSucesso;
    }

    public void setQtdContatosComSucesso(Integer qtdContatosComSucesso) {
        this.qtdContatosComSucesso = qtdContatosComSucesso;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        FecharMetaVO that = (FecharMetaVO) o;

        return getCodigo().equals(that.getCodigo());
    }
    @Override
    public int hashCode() {
        return getCodigo().hashCode();
    }

    public boolean isFaseConversao() {
        return getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())
                || getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())
                || getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())
                || getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())
                || getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla());
    }
}

package negocio.comuns.crm;

import java.util.Date;

import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Uteis;

public class ClienteOrganizadorCarteiraVO extends SuperVO  implements Cloneable{

	private ClienteVO cliente;
    private Integer nrCarteira;
    private Integer codigoColaborador;
    private Integer codigoVinculo;
    private String tipoVinculo;
    private String nomeColaboradoreCarteira;
    private String horaMaisAcesso;
    // atributo usado na tela de organizadorCarteira
    private Boolean clienteEscolhido;
    public ClienteOrganizadorCarteiraVO() {
        super();
    }
    public Integer getNrCarteira() {
        if (nrCarteira == null) {
            nrCarteira = new Integer(0);
        }
        return nrCarteira;
    }

    public void setNrCarteira(Integer nrCarteira) {
        this.nrCarteira = nrCarteira;
    }

    public String getNomeColaboradoreCarteira() {
        if (nomeColaboradoreCarteira == null) {
            nomeColaboradoreCarteira = "";
        }
        return nomeColaboradoreCarteira;
    }

    public void setNomeColaboradoreCarteira(String nomeColaboradoreCarteira) {
        this.nomeColaboradoreCarteira = nomeColaboradoreCarteira;
    }
    /**
	 * @return the cliente
	 */
	public ClienteVO getCliente() {
		if(cliente == null){
			cliente = new ClienteVO();
		}
		return cliente;
	}

	/**
	 * @param cliente the cliente to set
	 */
	public void setCliente(ClienteVO cliente) {
		this.cliente = cliente;
	}

	/**
	 * @return the codigoColaborador
	 */
	public Integer getCodigoColaborador() {
		if(codigoColaborador == null){
			codigoColaborador = new Integer(0);
			
		}
		return codigoColaborador;
	}
	/**
	 * @param codigoColaborador the codigoColaborador to set
	 */
	public void setCodigoColaborador(Integer codigoColaborador) {
		this.codigoColaborador = codigoColaborador;
	}
	/**
	 * @return the codigoVinculo
	 */
	public Integer getCodigoVinculo() {
		if(codigoVinculo == null){
			codigoVinculo = new Integer(0);
		}
		return codigoVinculo;
	}
	/**
	 * @param codigoVinculo the codigoVinculo to set
	 */
	public void setCodigoVinculo(Integer codigoVinculo) {
		this.codigoVinculo = codigoVinculo;
	}
	/**
	 * @return the tipoVinculo
	 */
	public String getTipoVinculo() {
		if(tipoVinculo == null){
			tipoVinculo = "";
		}
		return tipoVinculo;
	}
	/**
	 * @param tipoVinculo the tipoVinculo to set
	 */
	public void setTipoVinculo(String tipoVinculo) {
		this.tipoVinculo = tipoVinculo;
	}
	/**
	 * @return the clienteEscolhido
	 */
	public Boolean getClienteEscolhido() {
		if(clienteEscolhido == null){
			clienteEscolhido =Boolean.FALSE;
		}
		return clienteEscolhido;
	}
	/**
	 * @param clienteEscolhido the clienteEscolhido to set
	 */
	public void setClienteEscolhido(Boolean clienteEscolhido) {
		this.clienteEscolhido = clienteEscolhido;
	}
	public ClienteOrganizadorCarteiraVO clone() throws CloneNotSupportedException {
    	ClienteOrganizadorCarteiraVO obj = (ClienteOrganizadorCarteiraVO) super.clone();     
    	
        return obj;
    }
	/**
	 * @param horaMaisAcesso the horaMaisAcesso to set
	 */
	public void setHoraMaisAcesso(String horaMaisAcesso) {
		this.horaMaisAcesso = horaMaisAcesso;
	}
	/**
	 * @return the horaMaisAcesso
	 */
	public String getHoraMaisAcesso() {
		return horaMaisAcesso;
	}
	
	
}

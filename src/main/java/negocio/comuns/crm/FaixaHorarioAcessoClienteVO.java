package negocio.comuns.crm;

import negocio.comuns.arquitetura.SuperVO;

public class FaixaHorarioAcessoClienteVO extends SuperVO {

	private Integer codigo;
	private String nomePeriodo;
	private String horaInicial;
	private String horaFinal;
	private Boolean edicao; 
        private boolean verificado;
	
	
	public FaixaHorarioAcessoClienteVO() {
	
	}
	/**
	 * @return the codigo
	 */
	public Integer getCodigo() {
		if(codigo == null){
			codigo = new Integer(0);
		}
		return codigo;
	}
	/**
	 * @param codigo the codigo to set
	 */
	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}
	/**
	 * @return the nomePeriodo
	 */
	public String getNomePeriodo() {
		return nomePeriodo;
	}
	/**
	 * @param nomePeriodo the nomePeriodo to set
	 */
	public void setNomePeriodo(String nomePeriodo) {
		this.nomePeriodo = nomePeriodo;
	}
	/**
	 * @return the horaInicial
	 */
	public String getHoraInicial() {
		return horaInicial;
	}
	/**
	 * @param horaInicial the horaInicial to set
	 */
	public void setHoraInicial(String horaInicial) {
		this.horaInicial = horaInicial;
	}
	/**
	 * @return the horaFinal
	 */
	public String getHoraFinal() {
		return horaFinal;
	}
	/**
	 * @param horaFinal the horaFinal to set
	 */
	public void setHoraFinal(String horaFinal) {
		this.horaFinal = horaFinal;
	}
	/**
	 * @param edicao the edicao to set
	 */
	public void setEdicao(Boolean edicao) {
		this.edicao = edicao;
	}
	/**
	 * @return the edicao
	 */
	public Boolean getEdicao() {
		if(edicao == null){
			edicao = Boolean.FALSE;
		}
		return edicao;
	}

    public boolean isVerificado() {
        return verificado;
    }

    public void setVerificado(boolean verificado) {
        this.verificado = verificado;
    }

	
	

}

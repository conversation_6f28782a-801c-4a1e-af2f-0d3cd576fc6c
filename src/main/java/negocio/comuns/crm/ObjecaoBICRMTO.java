package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import negocio.comuns.arquitetura.SuperVO;

/**
 * Created by luiz on 05/01/2016.
 */
public class ObjecaoBICRMTO extends SuperVO {

    private Integer quantidade;
    private String objecao;
    private FasesCRMEnum fase;
    private String porcentagem;

    public Integer getQuantidade() {
        if (quantidade == null) {
            quantidade = 0;
        }
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getObjecao() {
        if (objecao == null) {
            objecao = "";
        }
        return objecao;
    }

    public void setObjecao(String objecao) {
        this.objecao = objecao;
    }

    public FasesCRMEnum getFase() {
        return fase;
    }

    public void setFase(FasesCRMEnum fase) {
        this.fase = fase;
    }

    public String getFaseApresentar() {
        return getFase().getDescricao();
    }

    public String getPorcentagem() {
        if (porcentagem == null) {
            porcentagem = "";
        }
        return porcentagem;
    }

    public void setPorcentagem(String porcentagem) {
        this.porcentagem = porcentagem;
    }
}

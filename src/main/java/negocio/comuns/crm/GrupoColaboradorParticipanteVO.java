package negocio.comuns.crm;

import annotations.arquitetura.ChaveEstrangeira;
import java.util.Iterator;
import java.util.List;

import annotations.arquitetura.FKJson;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.crm.GrupoColaborador;

/**
 * Reponsável por manter os dados da entidade GrupoColaboradorParticipante.
 * Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos
 * de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * 
 * @see SuperVO
 * @see GrupoColaborador
 */
public class GrupoColaboradorParticipanteVO extends SuperVO implements Cloneable {

    protected Integer codigo;
    @FKJson
    protected GrupoColaboradorVO grupoColaborador;
    protected String tipoVisao;
    protected Long totalCliente;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Colaborador </code>.
     */
    @ChaveEstrangeira
    @FKJson
    private ColaboradorVO colaboradorParticipante;
    private Boolean grupoColaboradorParticipanteEscolhido;
    private Boolean naoPermitirAlterarTipoVisao;
    // atributo usado na tela de organizadorCarteira
    private Boolean marcaTodosCliente;
    private Boolean marcaClientesDaPagina;
    private Boolean jaFoiConsultada;
    private Boolean exiteClienteVinculado;
    private String tipoGrupo;
    private Long totalizadorFiltro;
    private Long totalizadorSelecionado;
    // uso essa atributo quando vou trabalhar com meta pois la o o participante tem que ser usuario e colaborador.
    @ChaveEstrangeira
    private UsuarioVO usuarioParticipante;
    private Boolean colaboradorIndisponivel;

    /**
     * Construtor padrão da classe <code>GrupoColaboradorParticipante</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente
     * seus atributos (Classe VO).
     */
    public GrupoColaboradorParticipanteVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da
     * classe <code>GrupoColaboradorParticipanteVO</code>.
     */
    public static void validarUnicidade(List<GrupoColaboradorParticipanteVO> lista, GrupoColaboradorParticipanteVO obj) throws ConsistirException {
        for (GrupoColaboradorParticipanteVO repetido : lista) {
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>GrupoColaboradorParticipanteVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas
     * neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação
     * de valores válidos para os atributos.
     *
     * @exception ConsistirExecption
     *                Se uma inconsistência for encontrada aumaticamente é
     *                gerada uma exceção descrevendo
     *                o atributo e o erro ocorrido.
     */
    public static void validarDados(GrupoColaboradorParticipanteVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getColaboradorParticipante() == null) || (obj.getColaboradorParticipante().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo COLABORADOR deve ser informado!");
        }
        if (obj.getTipoVisao().equals("")) {
            throw new ConsistirException("O campo TIPO VISÃO deve ser informado!");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
        setTipoVisao(getTipoVisao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(null);
        setTipoVisao("");
        setColaboradorParticipante(new ColaboradorVO());
        setGrupoColaboradorParticipanteEscolhido(false);
        setNaoPermitirAlterarTipoVisao(false);
    }

    public Boolean validarTipoColaborador(String tipoGrupo) throws Exception {
        if (getColaboradorParticipante().getListaTipoColaboradorVOs() == null || getColaboradorParticipante().getListaTipoColaboradorVOs().size() == 0) {
            getColaboradorParticipante().setListaTipoColaboradorVOs(getFacade().getTipoColaborador().consultarPorCodigoColaborador(getColaboradorParticipante().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        Iterator i = getColaboradorParticipante().getListaTipoColaboradorVOs().iterator();
        while (i.hasNext()) {
            TipoColaboradorVO tipo = (TipoColaboradorVO) i.next();
            if (tipo.getDescricao().equals(tipoGrupo)) {
                return true;
            }
        }
        return false;
    }

    public void executarTrocaDeTipoGrupo(ClienteOrganizadorCarteiraVO cliente) throws Exception {
        if (getColaboradorParticipante().getListaTipoColaboradorVOs() == null || getColaboradorParticipante().getListaTipoColaboradorVOs().size() == 0) {
            getColaboradorParticipante().setListaTipoColaboradorVOs(getFacade().getTipoColaborador().consultarPorCodigoColaborador(getColaboradorParticipante().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        Iterator i = getColaboradorParticipante().getListaTipoColaboradorVOs().iterator();
        while (i.hasNext()) {
            TipoColaboradorVO tipo = (TipoColaboradorVO) i.next();
            cliente.setTipoVinculo(tipo.getDescricao());
            break;
        }
    }

    /**
     * @return the colaboradorParticipante
     */
    public ColaboradorVO getColaboradorParticipante() {
        if (colaboradorParticipante == null) {
            colaboradorParticipante = new ColaboradorVO();
        }
        return colaboradorParticipante;
    }

    /**
     * @param colaboradorParticipante the colaboradorParticipante to set
     */
    public void setColaboradorParticipante(ColaboradorVO colaboradorParticipante) {
        this.colaboradorParticipante = colaboradorParticipante;
    }

    public String getTipoVisao() {
        if (tipoVisao == null) {
            tipoVisao = "";
        }
        return (tipoVisao);
    }

    public String getTipoVisao_Apresentar() {
        if (tipoVisao.equals(null)) {
            return "";
        }
        return Dominios.getTipoVisao().get(tipoVisao);
    }

    public void setTipoVisao(String tipoVisao) {
        this.tipoVisao = tipoVisao;
    }

    public GrupoColaboradorVO getGrupoColaborador() {
        if (grupoColaborador == null) {
            grupoColaborador = new GrupoColaboradorVO();
        }
        return (grupoColaborador);
    }

    public void setGrupoColaborador(GrupoColaboradorVO grupoColaborador) {
        this.grupoColaborador = grupoColaborador;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the grupoColaboradorParticipanteEscolhido
     */
    public Boolean getGrupoColaboradorParticipanteEscolhido() {
        return grupoColaboradorParticipanteEscolhido;
    }

    /**
     * @param grupoColaboradorParticipanteEscolhido
     *            the grupoColaboradorParticipanteEscolhido to set
     */
    public void setGrupoColaboradorParticipanteEscolhido(Boolean grupoColaboradorParticipanteEscolhido) {
        this.grupoColaboradorParticipanteEscolhido = grupoColaboradorParticipanteEscolhido;
    }

    /**
     * @return the naoPermitirAlterarTipoVisao
     */
    public Boolean getNaoPermitirAlterarTipoVisao() {
        return naoPermitirAlterarTipoVisao;
    }

    /**
     * @param naoPermitirAlterarTipoVisao
     *            the naoPermitirAlterarTipoVisao to set
     */
    public void setNaoPermitirAlterarTipoVisao(Boolean naoPermitirAlterarTipoVisao) {
        this.naoPermitirAlterarTipoVisao = naoPermitirAlterarTipoVisao;
    }

    public Long getTotalCliente() {
        if (totalCliente == null) {
            setTotalCliente(new Long(0));
        }
        if (getColaboradorParticipante().getListaVinculos() != null && !getColaboradorParticipante().getListaVinculos().isEmpty()) {
            return new Long(getColaboradorParticipante().getListaVinculos().size());
        }
        return totalCliente;
    }

    /**
     * @param totalCliente
     *            the totalCliente to set
     */
    public void setTotalCliente(Long totalCliente) {
        this.totalCliente = totalCliente;
    }

    /**
     * @param marcaTodosCliente
     *            the marcaTodosCliente to set
     */
    public void setMarcaTodosCliente(Boolean marcaTodosCliente) {
        this.marcaTodosCliente = marcaTodosCliente;
    }

    /**
     * @return the marcaTodosCliente
     */
    public Boolean getMarcaTodosCliente() {
        if (marcaTodosCliente == null) {
            marcaTodosCliente = (new Boolean(false));
        }
        return marcaTodosCliente;
    }

    /**
     * @return the totalizadorFiltro
     */
    public Long getTotalizadorFiltro() {
        if (totalizadorFiltro == null) {
            totalizadorFiltro = (new Long(0));
        }
        return totalizadorFiltro;
    }

    /**
     * @param totalizadorFiltro
     *            the totalizadorFiltro to set
     */
    public void setTotalizadorFiltro(Long totalizadorFiltro) {
        this.totalizadorFiltro = totalizadorFiltro;
    }

    public String getTextoArrastar() {
        return "Total Marcado(s) " + getTotalizadorSelecionado() + ".";
    }

    /**
     * @return the totalizadorSelecionado
     */
    public Long getTotalizadorSelecionado() {
        if (totalizadorSelecionado == null) {
            totalizadorSelecionado = (new Long(0));
        }
        return totalizadorSelecionado;
    }

    /**
     * @param totalizadorSelecionado
     *            the totalizadorSelecionado to set
     */
    public void setTotalizadorSelecionado(Long totalizadorSelecionado) {
        this.totalizadorSelecionado = totalizadorSelecionado;
    }

    /**
     * @return the jaFoiConsultada
     */
    public Boolean getJaFoiConsultada() {
        if (jaFoiConsultada == null) {
            jaFoiConsultada = (new Boolean(false));
        }
        return jaFoiConsultada;
    }

    /**
     * @param jaFoiConsultada the jaFoiConsultada to set
     */
    public void setJaFoiConsultada(Boolean jaFoiConsultada) {
        this.jaFoiConsultada = jaFoiConsultada;
    }

    /**
     * @return the exiteClienteVinculado
     */
    public Boolean getExiteClienteVinculado() {
        if (exiteClienteVinculado == null) {
            exiteClienteVinculado = (new Boolean(false));
        }
        return exiteClienteVinculado;
    }

    /**
     * @param exiteClienteVinculado the exiteClienteVinculado to set
     */
    public void setExiteClienteVinculado(Boolean exiteClienteVinculado) {
        this.exiteClienteVinculado = exiteClienteVinculado;
    }

    /**
     * @return the tipoGrupo
     */
    public String getTipoGrupo() {
        if (tipoGrupo == null) {
            tipoGrupo = "";
        }
        return tipoGrupo;
    }

    /**
     * @param tipoGrupo the tipoGrupo to set
     */
    public void setTipoGrupo(String tipoGrupo) {
        this.tipoGrupo = tipoGrupo;
    }

    /**
     * @return the usuarioParticipante
     */
    public UsuarioVO getUsuarioParticipante() {
        if (usuarioParticipante == null) {
            usuarioParticipante = new UsuarioVO();
        }
        return usuarioParticipante;
    }

    /**
     * @param usuarioParticipante the usuarioParticipante to set
     */
    public void setUsuarioParticipante(UsuarioVO usuarioParticipante) {
        this.usuarioParticipante = usuarioParticipante;
    }

    public GrupoColaboradorParticipanteVO clone() throws CloneNotSupportedException {
        GrupoColaboradorParticipanteVO obj = (GrupoColaboradorParticipanteVO) super.clone();
        obj.setUsuarioParticipante(this.usuarioParticipante.clone());
        return obj;
    }

    public boolean equals(Object obj) {
        if(obj instanceof GrupoColaboradorParticipanteVO && obj != null) {
            GrupoColaboradorParticipanteVO aux = (GrupoColaboradorParticipanteVO)obj;
            return this.getColaboradorParticipante().getCodigo().intValue() == aux.getColaboradorParticipante().getCodigo().intValue();
        }
        return false;
    }

    /**
     * @return the marcaClientesDaPagina
     */
    public Boolean getMarcaClientesDaPagina() {
         if (marcaClientesDaPagina == null) {
            marcaClientesDaPagina = (new Boolean(false));
        }
        return marcaClientesDaPagina;
    }

    /**
     * @param marcaClientesDaPagina the marcaClientesDaPagina to set
     */
    public void setMarcaClientesDaPagina(Boolean marcaClientesDaPagina) {
        this.marcaClientesDaPagina = marcaClientesDaPagina;
    }


    public Boolean getColaboradorIndisponivel() {
        if (colaboradorIndisponivel == null){
            colaboradorIndisponivel = false;
        }
        return colaboradorIndisponivel;
    }

    public void setColaboradorIndisponivel(Boolean colaboradorIndisponivel) {
        this.colaboradorIndisponivel = colaboradorIndisponivel;
    }

    public String getNomeColaboradorParticipante() {
        if (getUsuarioParticipante() != null) {
            return this.getColaboradorParticipante().getPessoa().getNome();
        } else
        return "";
    }

}

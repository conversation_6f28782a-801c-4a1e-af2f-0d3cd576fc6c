package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.SuperVO;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class TiposVinculosFaseVO extends SuperVO {

    private Integer codigo;
    private FasesCRMEnum fase;
    private List<TipoColaboradorEnum> tiposColaborador;
    private List<SelectItem> opcoesTipoColaborador;
    private String tipoSelecionado;
    private boolean verificado = false;

    public TiposVinculosFaseVO() {

    }

    /**
     * Construtor
     *
     * @param fase  fase do CRM;
     * @param tipos tipos de Colaborador;
     */
    public TiposVinculosFaseVO(FasesCRMEnum fase, TipoColaboradorEnum... tipos) {
        this.fase = fase;
        this.tiposColaborador = new ArrayList<TipoColaboradorEnum>();
        if (tipos.length > 0) {
            Collections.addAll(this.tiposColaborador, tipos);
        }
    }

    /**
     * author: alcides
     * 09/11/2011
     */
    public static List<TiposVinculosFaseVO> sugestaoMetodo() {
        List<TiposVinculosFaseVO> sugestao = new ArrayList<TiposVinculosFaseVO>();
        //REGRA DE NEGOCIO
        //sugestões do método para tipos de colaboradores responsaveis pelas fases do CRM
        sugestao.add(new TiposVinculosFaseVO(FasesCRMEnum.AGENDAMENTO, TipoColaboradorEnum.CONSULTOR));
        sugestao.add(new TiposVinculosFaseVO(FasesCRMEnum.ANIVERSARIANTES, TipoColaboradorEnum.CONSULTOR, TipoColaboradorEnum.PROFESSOR));
        sugestao.add(new TiposVinculosFaseVO(FasesCRMEnum.FALTOSOS, TipoColaboradorEnum.PROFESSOR));
        sugestao.add(new TiposVinculosFaseVO(FasesCRMEnum.DESISTENTES, TipoColaboradorEnum.CONSULTOR));
        sugestao.add(new TiposVinculosFaseVO(FasesCRMEnum.RENOVACAO, TipoColaboradorEnum.CONSULTOR, TipoColaboradorEnum.PROFESSOR));
        sugestao.add(new TiposVinculosFaseVO(FasesCRMEnum.VINTE_QUATRO_HORAS, TipoColaboradorEnum.CONSULTOR));
        sugestao.add(new TiposVinculosFaseVO(FasesCRMEnum.VISITA_RECORRENTE, TipoColaboradorEnum.CONSULTOR));
        sugestao.add(new TiposVinculosFaseVO(FasesCRMEnum.INDICACOES, TipoColaboradorEnum.CONSULTOR));
        sugestao.add(new TiposVinculosFaseVO(FasesCRMEnum.GRUPO_RISCO, TipoColaboradorEnum.CONSULTOR));
        sugestao.add(new TiposVinculosFaseVO(FasesCRMEnum.POS_VENDA, TipoColaboradorEnum.CONSULTOR, TipoColaboradorEnum.PROFESSOR));
        sugestao.add(new TiposVinculosFaseVO(FasesCRMEnum.EX_ALUNOS, TipoColaboradorEnum.CONSULTOR));
        return sugestao;
    }

    public FasesCRMEnum getFase() {
        return fase;
    }

    public void setFase(FasesCRMEnum fase) {
        this.fase = fase;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List<TipoColaboradorEnum> getTiposColaborador() {
        if (tiposColaborador == null) {
            tiposColaborador = new ArrayList<TipoColaboradorEnum>();
        }
        return tiposColaborador;
    }

    public void setTiposColaborador(List<TipoColaboradorEnum> tiposColaborador) {
        this.tiposColaborador = tiposColaborador;
    }

    public List<SelectItem> getOpcoesTipoColaborador() {
        if (opcoesTipoColaborador == null) {
            opcoesTipoColaborador = new ArrayList<SelectItem>();
            for (TipoColaboradorEnum tipo : TipoColaboradorEnum.values()) {
                opcoesTipoColaborador.add(new SelectItem(tipo.getSigla(), tipo.getDescricao()));
            }
        }
        return opcoesTipoColaborador;
    }

    public String getTipoSelecionado() {
        return tipoSelecionado;
    }

    public void setTipoSelecionado(String tipoSelecionado) {
        this.tipoSelecionado = tipoSelecionado;
    }

    public boolean isVerificado() {
        return verificado;
    }

    public void setVerificado(boolean verificado) {
        this.verificado = verificado;
    }
}

package negocio.comuns.crm;
import java.util.List;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade Objecao. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
*/

public class ObjecaoVO extends SuperVO {
	
    protected Integer codigo;
    protected String descricao;
    protected String grupo;
    protected String comentario;
    private String tipoGrupo;
    private Boolean ativo;
	
    /**
     * Construtor padrão da classe <code>Objecao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public ObjecaoVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da classe <code>ObjecaoVO</code>.
    */
    public static void validarUnicidade(List<ObjecaoVO> lista, ObjecaoVO obj) throws ConsistirException {
        for (ObjecaoVO repetido : lista) {
            if (repetido.getCodigo().intValue() == obj.getCodigo().intValue()) {
                throw new ConsistirException(("msg_objecaoVO_unicidade_codigo"));
            }
        }
    }
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ObjecaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(ObjecaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if(obj.getDescricao().equals("")){
        	throw new ConsistirException("O campo DESCRIÇÃO não pode ser vazio !");
        }
        if(obj.getGrupo().equals("")){
        	throw new ConsistirException("O campo GRUPO não pode ser vazio !");
        }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
        setDescricao( getDescricao().toUpperCase() );
        setGrupo( getGrupo().toUpperCase() );
        setComentario(getComentario().toUpperCase());
        setTipoGrupo(getTipoGrupo().toUpperCase());
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setAtivo(true);
    }
    public String qualResultadoObjecao() {
		return "Objeção: "+getDescricao();
	}

    public String getGrupo() {
        if (grupo == null) {
            grupo = "";
        }
        return (grupo);
    }
     
    public void setGrupo( String grupo ) {
        this.grupo = grupo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }
     
    public void setDescricao( String descricao ) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }


	public String getComentario() {
		if(comentario == null){
			comentario = "";
		}
		return comentario;
	}


	public void setComentario(String comentario) {
		this.comentario = comentario;
	}


	public String getTipoGrupo() {
		if(tipoGrupo == null){
			tipoGrupo = "OB";
		}
		return tipoGrupo;
	}

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
    public String getAtivo_Apresentar(){
        return  ativo ? "Sim" : "Não";
    }

    public void setTipoGrupo(String tipoGrupo) {
		this.tipoGrupo = tipoGrupo;
	}
}
package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.SituacaoAtualMetaEnum;
import br.com.pactosolucoes.enumeradores.TipoFaseCRM;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 01/09/2015.
 */
public class MetaCRMTO {


    private TipoFaseCRM tipoFaseCRM;
    private List<TipoMetaCRMTO> listaTipoMetaVO = new ArrayList<>();
    private SituacaoAtualMetaEnum situacaoAtualMetaEnum = SituacaoAtualMetaEnum.META_NAO_ATENDIDA;
    private Integer totalMeta = 0;
    private Integer totalMetaRealizada = 0;
    private Integer ordemMeta;

    public TipoFaseCRM getTipoFaseCRM() {
        return tipoFaseCRM;
    }

    public void setTipoFaseCRM(TipoFaseCRM tipoFaseCRM) {
        this.tipoFaseCRM = tipoFaseCRM;
    }

    public List<TipoMetaCRMTO> getListaTipoMetaVO() {
        if (listaTipoMetaVO == null) {
            listaTipoMetaVO = new ArrayList<>();
        }
        return listaTipoMetaVO;
    }

    public void setListaTipoMetaVO(List<TipoMetaCRMTO> listaTipoMetaVO) {
        this.listaTipoMetaVO = listaTipoMetaVO;
    }

    public Integer getTotalMeta() {
        return totalMeta;
    }

    public void setTotalMeta(Integer totalMeta) {
        this.totalMeta = totalMeta;
    }

    @Override
    public int hashCode() {
        return tipoFaseCRM.getCodigo();
    }

    @Override
    public boolean equals(Object obj) {
        if ((obj == null) || (!(obj instanceof MetaCRMTO))) {
            return false;
        }
        return (((MetaCRMTO) obj).getTipoFaseCRM() == this.tipoFaseCRM);
    }

    public SituacaoAtualMetaEnum getSituacaoAtualMetaEnum() {
        return situacaoAtualMetaEnum;
    }

    public void setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum situacaoAtualMetaEnum) {
        this.situacaoAtualMetaEnum = situacaoAtualMetaEnum;
    }

    public Integer getTotalMetaRealizada() {
        return totalMetaRealizada;
    }

    public void setTotalMetaRealizada(Integer totalMetaRealizada) {
        this.totalMetaRealizada = totalMetaRealizada;
    }

    public Integer getOrdemMeta() {
        if (ordemMeta == null) {
            ordemMeta = 0;
        }
        return ordemMeta;
    }

    public void setOrdemMeta(Integer ordemMeta) {
        this.ordemMeta = ordemMeta;
    }

    public JSONObject toJson(MetaCRMTO metaCRMTO) {
        JSONObject metaJson = new JSONObject();

        metaJson.put("totalMeta", totalMeta);
        metaJson.put("totalMetaRealizada", totalMetaRealizada);
        metaJson.put("descricaoTipoFaseCrm", tipoFaseCRM.getDescricao());
        metaJson.put("tipoFaseCrmResumida", tipoFaseCRM.getDescricaoCurta());
        metaJson.put("situacaoAtualMeta", situacaoAtualMetaEnum.getDescricao());
        metaJson.put("tiposMeta", converterListaTiposMetaJson(metaCRMTO.getListaTipoMetaVO()));

        return metaJson;
    }

    private JSONArray converterListaTiposMetaJson(List<TipoMetaCRMTO> listaTipoMetaVO) {
        JSONArray tiposMeta = new JSONArray();
        for (TipoMetaCRMTO tipoMetaCRMTO : listaTipoMetaVO) {
            tiposMeta.put(tipoMetaCRMTO.toJson());
        }
        return tiposMeta;
    }
}

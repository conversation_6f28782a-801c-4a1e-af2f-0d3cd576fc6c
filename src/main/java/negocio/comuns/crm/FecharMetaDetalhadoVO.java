package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.SituacaoAtualMetaEnum;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.crm.FecharMeta;

import java.beans.Transient;
import java.util.Date;

/**
 * Reponsável por manter os dados da entidade FecharMetaDetalhado. Classe do
 * tipo VO - Value Object composta pelos atributos da entidade com visibilidade
 * protegida e os métodos de acesso a estes atributos. Classe utilizada para
 * apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 * @see FecharMeta
 */

public class FecharMetaDetalhadoVO extends SuperVO {

    protected Integer codigo;
    protected FecharMetaVO fecharMeta;
    protected Boolean obteveSucesso;
    protected ConfiguracaoDiasPosVendaVO configuracaoDiasPosVendaVO;
    protected String observacaoFilaEsperaTurmaCrm;

    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Cliente </code>.
     */
    protected ClienteVO cliente;
    // Atributos que não serão gravados no Banco, apenas para controle de
    // negocio
    protected Date dataUltimoContato;
    private PassivoVO passivo;
    private IndicadoVO indicado;
    private HistoricoContatoVO historicoContatoVO;
    private ContratoVO contratoVO;
    private String origem;
    private Integer codigoOrigem;
    private Integer pesoRisco;
    // Esse campo sera usa para saber se um cliente que estava na meta de Perda
    // e depois que teve uma ligação
    // se ele voltou a frequentar academina.
    private AcessoClienteVO acessoClienteVO;
    private Boolean enviarEmailSMS;
    private AgendaVO agenda;
    private String nomePessoaRel;
    private String observacao;
    private int sessoesFinais;
    private int vendaAvulsa;
    private int diasSemAgendamento;
    private String descconfiguracaodiasmetas;

    private SituacaoAtualMetaEnum situacaoAtualMetaEnum = SituacaoAtualMetaEnum.META_NAO_ATENDIDA; // atributo transiente
    private Date dia;
    private Boolean metaEmAberto;
    private boolean repescagem = false;
    private boolean teveContato = false;
    private ConviteAulaExperimentalVO conviteAulaExperimentalVO;
    private String emails;
    private String telefones;

    private boolean clienteCreditoTreino = false;
    private Integer saldoCreditoTreino;
    private String urlRD ="";
    private ConversaoLeadVO conversaoLeadVO = new ConversaoLeadVO();
    private String taxaEvasao;

    /**
     * Construtor padrão da classe <code>FecharMetaDetalhado</code>. Cria uma
     * nova instância desta entidade, inicializando automaticamente seus
     * atributos (Classe VO).
     */
    public FecharMetaDetalhadoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>FecharMetaDetalhadoVO</code>. Todos os tipos de consistência de
     * dados são e devem ser implementadas neste método. São validações típicas:
     * verificação de campos obrigatórios, verificação de valores válidos para
     * os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é
     *                            gerada uma exceção descrevendo o atributo e o erro
     *                            ocorrido.
     */
    public static void validarDados(FecharMetaDetalhadoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
    }

    public void inicializarDados() {
        setCodigo(null);
        setObteveSucesso(false);
        setCliente(new ClienteVO());
        setPassivo(new PassivoVO());
        setIndicado(new IndicadoVO());
    }

    public String getQualResultado() {
        if (getContratoVO().getCodigo() != 0) {
            return "Compra do contrato N°" + getContratoVO().getCodigo();
        } else if (getHistoricoContatoVO().getCodigo() != 0) {
            return getHistoricoContatoVO().getResultado();
        } else if (getAgenda().getDataComparecimento() != null) {
            return "Compareceu";
        }
        return "";

    }

    public Boolean getApresentarClienteQueIndicou() {
        return !(getIndicado().getIndicacaoVO().getClienteQueIndicou() == null || getIndicado().getIndicacaoVO().getClienteQueIndicou().getCodigo() == 0);
    }

    public Boolean getApresentarColaboradorQueIndicou() {
        return !(getIndicado().getIndicacaoVO().getColaboradorQueIndicou() == null || getIndicado().getIndicacaoVO().getColaboradorQueIndicou().getCodigo() == 0);
    }

    public ClienteVO getCliente() {
        if (cliente == null) {
            cliente = new ClienteVO();
        }
        return (cliente);
    }

    public void setCliente(ClienteVO obj) {
        this.cliente = obj;
    }

    public Boolean getObteveSucesso() {
        return (obteveSucesso);
    }

    public String getObteveSucesso_apresentar() {
        return (getObteveSucesso()) ? "Sim" : "Não";
    }

    public void setObteveSucesso(Boolean obteveSucesso) {
        this.obteveSucesso = obteveSucesso;
    }

    public Boolean isObteveSucesso() {
        if (obteveSucesso == null) {
            obteveSucesso = false;
        }
        return (obteveSucesso);
    }

    public FecharMetaVO getFecharMeta() {
        if (fecharMeta == null) {
            fecharMeta = new FecharMetaVO();
        }
        return (fecharMeta);
    }

    public void setFecharMeta(FecharMetaVO fecharMeta) {
        this.fecharMeta = fecharMeta;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PassivoVO getPassivo() {
        if (passivo == null) {
            passivo = new PassivoVO();
        }
        return passivo;
    }

    public void setPassivo(PassivoVO passivo) {
        this.passivo = passivo;
    }

    public IndicadoVO getIndicado() {
        if (indicado == null) {
            indicado = new IndicadoVO();
        }
        return indicado;
    }

    public void setIndicado(IndicadoVO indicado) {
        this.indicado = indicado;
    }

    public ConfiguracaoDiasPosVendaVO getConfiguracaoDiasPosVendaVO() {
        if (configuracaoDiasPosVendaVO == null) {
            configuracaoDiasPosVendaVO = new ConfiguracaoDiasPosVendaVO();
        }
        return configuracaoDiasPosVendaVO;
    }

    public void setConfiguracaoDiasPosVendaVO(ConfiguracaoDiasPosVendaVO configuracaoDiasPosVendaVO) {
        this.configuracaoDiasPosVendaVO = configuracaoDiasPosVendaVO;
    }

    public HistoricoContatoVO getHistoricoContatoVO() {
        if (historicoContatoVO == null) {
            historicoContatoVO = new HistoricoContatoVO();
        }
        return historicoContatoVO;
    }

    public void setHistoricoContatoVO(HistoricoContatoVO historicoContatoVO) {
        this.historicoContatoVO = historicoContatoVO;
    }

    public Boolean getEnviarEmailSMS() {
        if (enviarEmailSMS == null) {
            enviarEmailSMS = false;
        }
        return enviarEmailSMS;
    }

    public void setEnviarEmailSMS(Boolean enviarEmail) {
        this.enviarEmailSMS = enviarEmail;
    }

    /**
     * Método que define na tela metaAgendadosDetalhadaForm se apresenta o campo Passivo.
     */
    public Boolean getValidarAgendadosPassivoApresentar() {
        return getPassivo().getCodigo() != 0;
    }

    /**
     * Método que define na tela metaAgendadosDetalhadaForm se apresenta o campo Indicado.
     */
    public Boolean getValidarAgendadosIndicadoApresentar() {
        return getIndicado().getCodigo() != 0;
    }

    /**
     * Método que define na tela metaAgendadosDetalhadaForm se apresenta o campo Cliente.
     */
    public Boolean getValidarAgendadosClienteApresentar() {
        return getCliente().getCodigo() != 0;
    }

    public ContratoVO getContratoVO() {
        if (contratoVO == null) {
            contratoVO = new ContratoVO();
        }
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public String getOrigem() {
        if (origem == null) {
            origem = "";
        }
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public Integer getCodigoOrigem() {
        if (codigoOrigem == null) {
            codigoOrigem = 0;
        }
        return codigoOrigem;
    }

    public void setCodigoOrigem(Integer codigoOrigem) {
        this.codigoOrigem = codigoOrigem;
    }

    public AcessoClienteVO getAcessoClienteVO() {
        if (acessoClienteVO == null) {
            acessoClienteVO = new AcessoClienteVO();
        }
        return acessoClienteVO;
    }

    public void setAcessoClienteVO(AcessoClienteVO acessoClienteVO) {
        this.acessoClienteVO = acessoClienteVO;
    }

    public AgendaVO getAgenda() {
        if (agenda == null) {
            agenda = new AgendaVO();
        }
        return agenda;
    }

    public void setAgenda(AgendaVO agenda) {
        this.agenda = agenda;
    }

    public String getDataUltimoContato_Apresentar() {
        if (getDataUltimoContato() == null) {
            return "";
        }
        return Uteis.getDataComHora(dataUltimoContato);
    }

    public Date getDataUltimoContato() {
        return dataUltimoContato;
    }

    public void setDataUltimoContato(Date dataUltimoContato) {
        this.dataUltimoContato = dataUltimoContato;
    }

    public String getNomePessoaRel() {
        if (!getCliente().getPessoa().getNome().equals("")) {
            return getCliente().getPessoa().getNome();
        } else if (getFecharMeta() != null && !UteisValidacao.emptyString(getFecharMeta().getIdentificadorMeta())
                && getFecharMeta().getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_PASSIVO.getSigla())) {
            return getPassivo().getNome() + " (RECEPTIVO)";
        } else if (getFecharMeta() != null && !UteisValidacao.emptyString(getFecharMeta().getIdentificadorMeta())
                && getFecharMeta().getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())) {
            return getIndicado().getNomeIndicado() + " (INDICAÇÃO)";
        } else if (getFecharMeta() != null && !UteisValidacao.emptyString(getFecharMeta().getIdentificadorMeta())
                && getFecharMeta().getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_LEAD.getSigla())) {
            return (!UteisValidacao.emptyString(getPassivo().getNome()) ? getPassivo().getNome() : getIndicado().getNomeIndicado()) + " (LEAD)";
        }else{
            return !UteisValidacao.emptyString(getPassivo().getNome()) ? getPassivo().getNome() +"(RECEPTIVO)" : getIndicado().getNomeIndicado() + " (INDICAÇÃO)";
        }
    }

    public Integer getPesoRisco() {
        if (pesoRisco == null) {
            pesoRisco = 0;
        }
        return pesoRisco;
    }

    public void setPesoRisco(Integer pesoRisco) {
        this.pesoRisco = pesoRisco;
    }

    public String getObteveSucessoApresentar() {
        if (getObteveSucesso())
            return "Sim";
        return "Não";
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public int getSessoesFinais() {
        return sessoesFinais;
    }

    public void setSessoesFinais(int sessoesFinais) {
        this.sessoesFinais = sessoesFinais;
    }

    public int getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(int vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public int getDiasSemAgendamento() {
        return diasSemAgendamento;
    }

    public void setDiasSemAgendamento(int diasSemAgendamento) {
        this.diasSemAgendamento = diasSemAgendamento;
    }

    public String getDescconfiguracaodiasmetas() {
        if(descconfiguracaodiasmetas == null){
            descconfiguracaodiasmetas = "";
        }
        return descconfiguracaodiasmetas;
    }

    public void setDescconfiguracaodiasmetas(String descconfiguracaodiasmetas) {
        this.descconfiguracaodiasmetas = descconfiguracaodiasmetas;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final FecharMetaDetalhadoVO other = (FecharMetaDetalhadoVO) obj;
        if (this.cliente == null || !this.cliente.getCodigo().equals(other.cliente.getCodigo())) {
            return false;
        }
        if (!this.codigoOrigem.equals(other.codigoOrigem)) {
            return false;
        }
        if ((this.origem == null) ? (other.origem != null) : !this.origem.equals(other.origem)) {
            return false;
        }
        return true;
    }

    public SituacaoAtualMetaEnum getSituacaoAtualMetaEnum() {
        return situacaoAtualMetaEnum;
    }

    public void setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum situacaoAtualMetaEnum) {
        this.situacaoAtualMetaEnum = situacaoAtualMetaEnum;
    }

    public Integer getCodigoSituacaoAtualMetaEnum() {
        return getSituacaoAtualMetaEnum().getCodigo();
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Boolean getMetaEmAberto() {
        if (metaEmAberto == null) {
            metaEmAberto = false;
        }
        return metaEmAberto;
    }

    public void setMetaEmAberto(Boolean metaEmAberto) {
        this.metaEmAberto = metaEmAberto;
    }

    public boolean isRepescagem() {
        return repescagem;
    }

    public void setRepescagem(boolean repescagem) {
        this.repescagem = repescagem;
    }

    public String getMatricula() {
        return getCliente().getMatricula();
    }

    public String getIdadePessoa() {
        if (!getCliente().getPessoa().getNome().equals("")) {
            return getCliente().getPessoa().getIdadePessoa();
        } else {
            return "";
        }

    }

    public String getDataNascParcialPessoa() {
        if (!getCliente().getCodigo().equals(0)) {
            return getCliente().getPessoa().getDataNasc_Parcial();
        } else {
            return "";
        }

    }

    public String getEstadoCivilPessoa() {
        if (!getCliente().getPessoa().getNome().equals("")) {
            return getCliente().getPessoa().getEstadoCivil_Apresentar();
        } else {
            return "";
        }

    }

    public String getTipoAgendamento() {
        return getAgenda().getTipoAgendamento_Apresentar();
    }

    public String getDataAgendamento() {
        return getAgenda().getDataAgendamento_Apresentar();
    }

    public String getHoraAgendamento() {
        return getAgenda().getApresentarMinutoEmHora();
    }

    public String getSituacaoCliente() {
        return getCliente().getSituacao_Apresentar();
    }

    public String getSituacaoContrato() {
        return getCliente().getSituacaoContrato_Apresentar();
    }

    public String getFaseMeta() {
        return FasesCRMEnum.getIdentificador(getFecharMeta().getIdentificadorMeta());
    }

    public String getDataMeta() {
        try {
            return (Uteis.getData(getFecharMeta().dataRegistro));
        } catch (Exception ignored){
        }
        return "";
    }

    public boolean isTeveContato() {
        return teveContato;
    }

    public void setTeveContato(boolean teveContato) {
        this.teveContato = teveContato;
    }

    public ConviteAulaExperimentalVO getConviteAulaExperimentalVO() {
        return conviteAulaExperimentalVO;
    }

    public void setConviteAulaExperimentalVO(ConviteAulaExperimentalVO conviteAulaExperimentalVO) {
        this.conviteAulaExperimentalVO = conviteAulaExperimentalVO;
    }

    public String getEmails() {
        if (emails == null) {
            emails = "";
        }
        return emails;
    }

    public void setEmails(String emails) {
        this.emails = emails;
    }

    public String getTelefones() {
        if (telefones == null) {
            telefones = "";
        }
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public boolean isClienteCreditoTreino() {
        return clienteCreditoTreino;
    }

    public void setClienteCreditoTreino(boolean clienteCreditoTreino) {
        this.clienteCreditoTreino = clienteCreditoTreino;
    }

    public Integer getSaldoCreditoTreino() {
        if (saldoCreditoTreino == null) {
            saldoCreditoTreino = 0;
        }
        return saldoCreditoTreino;
    }

    public void setSaldoCreditoTreino(Integer saldoCreditoTreino) {
        this.saldoCreditoTreino = saldoCreditoTreino;
    }

    public String getDataRegistro_ApresentarBI() {
        return getFecharMeta().getDataRegistro_ApresentarBI();
    }

    public String getOrdenacao() {
        return getNomePessoaRel() + Calendario.getData(getFecharMeta().getDataRegistro(), "yyyyMMdd");
    }

    public String getUrlRD() {
        if(urlRD == null){
            return "";
        }
        return urlRD;
    }

    public void setUrlRD(String urlRD) {
        this.urlRD = urlRD;
    }

    public ConversaoLeadVO getConversaoLeadVO() {
        return conversaoLeadVO;
    }

    public void setConversaoLeadVO(ConversaoLeadVO conversaoLeadVO) {
        this.conversaoLeadVO = conversaoLeadVO;
    }
    
    public Date getDataLancamentoConversacao(){
        if(this.getConversaoLeadVO().getDataLancamento() == null){
            return Calendario.hoje();
        }
        return this.getConversaoLeadVO().getDataLancamento();
    }

    public Boolean getBuzzlead() {
        if (getConversaoLeadVO() != null) {
            if (!UteisValidacao.emptyString(getConversaoLeadVO().getIdentificador())) {
                return getConversaoLeadVO().getIdentificador().equalsIgnoreCase("Buzzlead");
            }
        }
        return false;
    }


    public String getTaxaEvasao() {
        if (taxaEvasao == null) {
            taxaEvasao = "";
        }
        return taxaEvasao;
    }

    public void setTaxaEvasao(String taxaEvasao) {
        this.taxaEvasao = taxaEvasao;
    }

    public String getObservacaoFilaEsperaTurmaCrm() {
        return observacaoFilaEsperaTurmaCrm;
    }

    public void setObservacaoFilaEsperaTurmaCrm(String observacaoFilaEsperaTurmaCrm) {
        this.observacaoFilaEsperaTurmaCrm = observacaoFilaEsperaTurmaCrm;
    }
}

package negocio.comuns.crm.optin;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.time.LocalDate;

public class OptinVO extends SuperVO {

    private Integer codigo;
    private ClienteVO cliente;
    private EmpresaVO empresa;
    private String email;
    private String ipCliente;
    private Boolean bloqueadoBounce;
    private OrigemEnvioEnum origemEnvio;
    private LocalDate dataRegistro;
    private LocalDate dataInscricao;
    private LocalDate dataExclusao;

    public OptinVO() {
        super();
        inicializarDados();
    }

    public OptinVO(Integer codigo, ClienteVO cliente, EmpresaVO empresa, String email, String ipCliente, Boolean bloqueadoBounce, OrigemEnvioEnum origemEnvio, LocalDate dataRegistro, LocalDate dataInscricao, LocalDate dataExclusao) {
        this.codigo = codigo;
        this.cliente = cliente;
        this.empresa = empresa;
        this.email = email;
        this.ipCliente = ipCliente;
        this.bloqueadoBounce = bloqueadoBounce;
        this.origemEnvio = origemEnvio;
        this.dataRegistro = dataRegistro;
        this.dataInscricao = dataInscricao;
        this.dataExclusao = dataExclusao;
    }

    public void inicializarDados(){
        setCodigo(0);
        setCliente(new ClienteVO());
        setEmpresa(new EmpresaVO());
        setEmail("");
        setIpCliente("");
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>OptinVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(OptinVO obj) throws Exception {

        if (obj.getEmail().equals("") || obj.getEmail() == null){
            throw new ConsistirException("O campo Email (Opt-in) deve ser informado.");
        }
        if (obj.getBloqueadoBounce() == null){
            throw new ConsistirException("O campo BloqueadoBounce (Opt-in) deve ser informado.");
        }

    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteVO getCliente() {
        if (cliente == null){
            cliente = new ClienteVO();
        }
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public String getEmpresa_Apresentar(){
        return getEmpresa().getNome();
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null){
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getIpCliente() {
        if (ipCliente == null) {
            ipCliente = "";
        }
        return ipCliente;
    }

    public void setIpCliente(String ipCliente) {
        this.ipCliente = ipCliente;
    }

    public Boolean getBloqueadoBounce() {
        return bloqueadoBounce;
    }

    public void setBloqueadoBounce(Boolean bloqueadoBounce) {
        this.bloqueadoBounce = bloqueadoBounce;
    }

    public OrigemEnvioEnum getOrigemEnvio() {
        return origemEnvio;
    }

    public void setOrigemEnvio(OrigemEnvioEnum origemEnvio) {
        this.origemEnvio = origemEnvio;
    }

    public LocalDate getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(LocalDate dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public LocalDate getDataInscricao() {
        return dataInscricao;
    }

    public void setDataInscricao(LocalDate dataInscricao) {
        this.dataInscricao = dataInscricao;
    }

    public LocalDate getDataExclusao() {
        return dataExclusao;
    }

    public void setDataExclusao(LocalDate dataExclusao) {
        this.dataExclusao = dataExclusao;
    }
}

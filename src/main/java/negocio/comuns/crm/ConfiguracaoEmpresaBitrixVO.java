package negocio.comuns.crm;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.AcaoObjcaoLeadEnum;

public class ConfiguracaoEmpresaBitrixVO extends SuperVO {

    private Integer codigo;
    private String empresa;
    private int responsavelPadrao;
    private int acaoobjecao;
    private String acao;

    private String url;

    private boolean habilitada;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public int getResponsavelPadrao() {
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(int responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }

    public int getAcaoobjecao() {
        return acaoobjecao;
    }

    public void setAcaoobjecao(int acaoobjecao) {
        this.acaoobjecao = acaoobjecao;
    }

    public String getAcao() {
        return acao;
    }

    public void setAcao(String acao) {
        this.acao = acao;
    }

    public boolean isHabilitada() {
        return habilitada;
    }

    public void setHabilitada(boolean habilitada) {
        this.habilitada = habilitada;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isAcaoObjecaoLeadAtaulizarQualquer() {
        return getAcaoobjecao() == AcaoObjcaoLeadEnum.ATUALIZAR_QUALQUER.getCodigo();
    }

    public boolean isAcaoObjecaoLeadApenasDefinitiva() {
        return getAcaoobjecao() == AcaoObjcaoLeadEnum.APENAS_DEFINTIVA.getCodigo();
    }
}

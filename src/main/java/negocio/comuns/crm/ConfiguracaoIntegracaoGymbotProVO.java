package negocio.comuns.crm;

import negocio.comuns.arquitetura.SuperVO;

public class ConfiguracaoIntegracaoGymbotProVO extends SuperVO {

     private Integer codigo;
     private Boolean ativo;
     private String descricao;
     private Integer empresa;
     private String tipofluxo;
     private String fase;
     private String token;
     private String idFluxo;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getTipofluxo() {
        return tipofluxo;
    }

    public void setTipofluxo(String tipofluxo) {
        this.tipofluxo = tipofluxo;
    }

    public String getFase() {
        return fase;
    }

    public void setFase(String fase) {
        this.fase = fase;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getIdFluxo() {
        return idFluxo;
    }

    public void setIdFluxo(String idFluxo) {
        this.idFluxo = idFluxo;
    }
}

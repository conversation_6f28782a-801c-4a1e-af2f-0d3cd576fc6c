package negocio.comuns.crm;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class MalingEnviadosVO extends SuperVO {

    private Integer codigo;
    private Integer cliente;
    private Integer contrato;
    private Date dataenvio;
    private String situacao;

    public  MalingEnviadosVO(  Integer cliente, Integer contrato, Date dataenvio, String situacao){
        this.cliente =cliente;
        this.contrato = contrato;
        this.dataenvio = dataenvio;
        this.situacao = situacao;
    }
    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Date getDataenvio() {
        return dataenvio;
    }

    public void setDataenvio(Date dataenvio) {
        this.dataenvio = dataenvio;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }
}

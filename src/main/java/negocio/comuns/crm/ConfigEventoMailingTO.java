package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.OcorrenciaEnum;
import br.com.pactosolucoes.enumeradores.TipoEventoEnum;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * <AUTHOR>
 */
public class ConfigEventoMailingTO extends SuperTO {

    public static final String SQL_PADRAO_ESTUDIO = "\n WHERE age.data_aula BETWEEN '%s' AND '%s' %s ";
    private TipoEventoEnum evento = null;
    private Date inicio = null;
    private Date fim = null;
    private Integer ocorrencia = 0;
    private List<GenericoTO> riscos;
    private boolean nrFaltasMaior = true;
    private Integer nrFaltas = 0;
    private Integer minimoDiasVencido = 1;
    private Double minValor = 0.0;
    private Double maxValor = 9999.99;
    private Integer qtdMinParcelasVencidas = 1;
    private boolean boletoParcelasVencendo = false;
    private boolean modeloPadraoBoleto = false;
    private List<GenericoTO> pendencias;
    private boolean naoClientes = true;
    private List<GenericoTO> tipoProdutos;
    private List<GenericoTO> produtosSessao;
    private List<GenericoTO> ambientes;
    private List<GenericoTO> colaboradores;
    private ProdutoVO produtoVO;
    private List<ProdutoVO> listaProdutosVOs;
    private String codigoErroRemessa;
    private int diasRemessa = 0;
    private Integer diasParcelasVencidasInicial;
    private Integer diasParcelasVencidasFinal;
    private Integer nrDiasInicioFreePass = 0;

    private int diaMais = 0;
    private int diaMenos = 0;

    //CODIGOS PERSISTIDOS
    private String nomesPendencias = "";
    private String codigosTiposProdutos = "";
    private String codigosProdutoSessao = "";
    private String codigosProdutosVencidos = "";
    private String codigosAmbientes = "";
    private String codigosColaboradores = "";
    private String codigosRiscos = "";

    public ConfigEventoMailingTO(MeioEnvio meioEnvio, ConfiguracaoSistemaVO cfg,
                                 List<GenericoTO> produtosSessao, List<GenericoTO> ambientes, List<GenericoTO> colaboradores) {
        montarRiscos();
        montarTipos();
        montarPendencias(cfg, meioEnvio);
        this.produtosSessao = produtosSessao;
        this.ambientes = ambientes;
        this.colaboradores = colaboradores;
    }

    public ConfigEventoMailingTO() {

    }

    public void removerProdutoVencidos(ProdutoVO produtoVO) throws Exception {
        for (int i = 0; i < getListaProdutosVOs().size(); i++) {
            ProdutoVO existente = getListaProdutosVOs().get(i);
            if (existente.getCodigo().intValue() == produtoVO.getCodigo().intValue()) {
                getListaProdutosVOs().remove(i);
            }
        }
    }

    public void montarSelecionados() {
        montarSelecionados(nomesPendencias, pendencias, false);
        montarSelecionados(codigosTiposProdutos, tipoProdutos, false);
        montarSelecionados(codigosProdutoSessao, produtosSessao, true);
        montarSelecionados(codigosAmbientes, ambientes, true);
        montarSelecionados(codigosColaboradores, colaboradores, true);
        montarSelecionados(codigosRiscos, riscos, true);
    }

    public List<GenericoTO> getListaGenerica() {
        if (evento == null) {
            return new ArrayList<GenericoTO>();
        }
        switch (evento) {
            case AGENDAMENTOS_AMBIENTE:
                return ambientes;
            case AGENDAMENTOS_PRODUTO:
                return produtosSessao;
            case COMPRA_PRODUTO:
                return tipoProdutos;
            case PRODUTOS_VENDIDOS:
                return produtosSessao;
            case A_AGENDAR:
                return produtosSessao;
            case A_FATURAR:
                return produtosSessao;
            case AGENDAMENTOS_PROFISSIONAL:
                return colaboradores;
            default:
                return new ArrayList<GenericoTO>();
        }
    }

    public List<GenericoTO> getAmbientes() {
        return ambientes;
    }

    public void setAmbientes(List<GenericoTO> produtosAmbientes) {
        this.ambientes = produtosAmbientes;
    }

    public boolean getNaoClientes() {
        return naoClientes;
    }

    public void setNaoClientes(boolean naoClientes) {
        this.naoClientes = naoClientes;
    }

    public List<GenericoTO> getProdutosSessao() {
        return produtosSessao;
    }

    public void setProdutosSessao(List<GenericoTO> produtosSessao) {
        this.produtosSessao = produtosSessao;
    }

    public List<GenericoTO> getTipoProdutos() {
        return tipoProdutos;
    }

    public void setTipoProdutos(List<GenericoTO> tipoProdutos) {
        this.tipoProdutos = tipoProdutos;
    }

    public void montarSelecionados(String codigosConc, List<GenericoTO> lista, boolean integer) {
        String[] codigos = new String[]{""};
        if(isNotBlank(codigosConc)){
            codigos = codigosConc.split(",");
        }
        for (String codigo : codigos) {
            if (UteisValidacao.emptyString(codigo)) {
                continue;
            }
            if (integer) {
                Integer cod = Integer.valueOf(codigo);
                for (GenericoTO generico : lista) {
                    if (generico.getCodigo() == cod) {
                        generico.setSelecionado(true);
                    }

                }
            } else {
                for (GenericoTO generico : lista) {
                    try {
                        if (generico.getCodigoString().equals(codigo)) {
                            generico.setSelecionado(true);
                        }
                    } catch (Exception e) {
                        if (generico.getLabel().equals(codigo)) {
                            generico.setSelecionado(true);
                        }
                    }
                }
            }
        }
    }

    public void montarTipos() {
        tipoProdutos = new ArrayList<GenericoTO>();
        for (TipoProduto tipoProduto : TipoProduto.values()) {
            if (!tipoProduto.equals(TipoProduto.SESSAO)
                    && !tipoProduto.equals(TipoProduto.MES_REFERENCIA_PLANO)
                    && !tipoProduto.equals(TipoProduto.DESCONTO)
                    && !tipoProduto.equals(TipoProduto.DEVOLUCAO)
                    && !tipoProduto.equals(TipoProduto.DESCONTO_RENOVACAO_ANTECIPADA)
                    && !tipoProduto.equals(TipoProduto.MULTA_JUROS)
                    && !tipoProduto.equals(TipoProduto.FREEPASS)
                    && !tipoProduto.equals(TipoProduto.RETORNO_TRANCAMENTO)) {
                tipoProdutos.add(new GenericoTO(tipoProduto.getCodigo(), tipoProduto.getDescricao()));
            }

        }
    }

    public Double getMaxValor() {
        return maxValor;
    }

    public void setMaxValor(Double maxValor) {
        this.maxValor = maxValor;
    }

    public Double getMinValor() {
        return minValor;
    }
    public Integer getMinValorInt() {
        return minValor == null ? 0 : minValor.intValue();
    }

    public void setMinValor(Double minValor) {
        this.minValor = minValor;
    }

    public void setMinValorInt(Integer minValor) {
        this.minValor = minValor == null ? 0.0 : minValor.doubleValue();
    }
    
    public Integer getMinimoDiasVencido() {
        return minimoDiasVencido;
    }

    public void setMinimoDiasVencido(Integer minimoDiasVencido) {
        this.minimoDiasVencido = minimoDiasVencido;
    }

    public boolean getNrFaltasMaior() {
        return nrFaltasMaior;
    }

    public void setNrFaltasMaior(boolean nrFaltasMaior) {
        this.nrFaltasMaior = nrFaltasMaior;
    }

    public void montarRiscos() {
        riscos = new ArrayList<GenericoTO>();
        for (Integer i = 8; i > 0; i--) {
            riscos.add(new GenericoTO(i, i.toString()));
        }
    }

    public void montarPendencias(ConfiguracaoSistemaVO cfg, MeioEnvio meio) {
        pendencias = new ArrayList<GenericoTO>();
        if (cfg.getDataNascPendente())
            pendencias.add(new GenericoTO(1, "Dt Nasc."));
        if (cfg.getNomePaiPendente())
            pendencias.add(new GenericoTO(2, "Nome Pai"));
        if (cfg.getNomeMaePendente())
            pendencias.add(new GenericoTO(1, "Nome Mãe"));
        if (cfg.getCpfPendente())
            pendencias.add(new GenericoTO(1, "CPF"));
        if (cfg.getRgPendente())
            pendencias.add(new GenericoTO(1, "RG"));
        if (cfg.getPaisPendente())
            pendencias.add(new GenericoTO(1, "País"));
        if (cfg.getEstadoPendente())
            pendencias.add(new GenericoTO(1, "Estado"));
        if (cfg.getCidadePendente())
            pendencias.add(new GenericoTO(1, "Cidade"));
        if (cfg.getProfissaoPendente())
            pendencias.add(new GenericoTO(1, "Profissão"));
        if (cfg.getSexoPendente())
            pendencias.add(new GenericoTO(1, "Sexo"));
        if (cfg.getEstadoCivilPendente())
            pendencias.add(new GenericoTO(1, "Estado Civil"));
        if (cfg.getGrauInstrucaoPendente())
            pendencias.add(new GenericoTO(1, "Grau Inst."));
        if (cfg.getCategoriaPendente())
            pendencias.add(new GenericoTO(1, "Categoria"));
        if (cfg.getEnderecoPendente())
            pendencias.add(new GenericoTO(1, "Endereço"));
        if (cfg.getTelefonePendente() && meio.equals(MeioEnvio.EMAIL))
            pendencias.add(new GenericoTO(1, "Telefone"));
        if (cfg.getEmailPendente() && meio.equals(MeioEnvio.SMS))
            pendencias.add(new GenericoTO(1, "Email"));
    }

    public List<GenericoTO> getRiscos() {
        return riscos;
    }

    public void setRiscos(List<GenericoTO> riscos) {
        this.riscos = riscos;
    }

    public TipoEventoEnum getEvento() {
        return evento;
    }

    public void setEvento(TipoEventoEnum evento) {
        this.evento = evento;
    }

    public Integer getEventoCodigo() {
        return evento == null ? null : evento.getCodigo();
    }

    public void setEventoCodigo(Integer eventoCodigo) {
        this.evento = TipoEventoEnum.obter(eventoCodigo);
    }

    public List<SelectItem> getListaOcorrencias() {
        return evento == null ? new ArrayList<SelectItem>() : evento.getListaOcorrencias();
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Integer getOcorrencia() {
        if(ocorrencia == null){
            this.ocorrencia = 0;
        }
        return ocorrencia;
    }

    public void setOcorrencia(Integer ocorrencia) {
        this.ocorrencia = ocorrencia;
    }

    public boolean getApresentarDatas() {
        return getEvento() != null && getEvento().isPermiteDatas() && ocorrencia != null && ocorrencia.equals(OcorrenciaEnum.INSTANTANEO.getCodigo());
    }

    public boolean getAprDmais() {
        return getEvento() != null && getEvento().isDmais() && ocorrencia != null && ocorrencia.equals(OcorrenciaEnum.DIARIAMENTE.getCodigo());
    }

    public boolean getAprDMenos() {
        return getEvento() != null && getEvento().isDmenos() && ocorrencia != null && ocorrencia.equals(OcorrenciaEnum.DIARIAMENTE.getCodigo());
    }

    public boolean getD() {
        return getAprDmais() || getAprDMenos();
    }

    public String getLabelDatas() {
        return getEvento() == null ? "" : getEvento().getLabel();
    }

    public boolean getMostrarDataAniversario() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.EVENTO_ANIVERSARIANTES);
    }

    public boolean getGrupoRisco() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.GRUPO_RISCO);
    }

    public boolean getFaltosos() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.FALTOSOS);
    }

    public boolean getSaldoPontos() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.SALDO_PONTOS);
    }

    public boolean getFreepass() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.FREEPASS);
    }


    public boolean getCreditoTreino() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.CONTRATO_CREDITO_TREINO);
    }

    public boolean getDebito() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.DEBITO);
    }

    public boolean getEvtPendencias() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.PENDENCIAS);
    }

    public boolean getIndicados() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.INDICADOS);
    }

    public boolean getCompra() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.COMPRA_PRODUTO);
    }

    public boolean getAgendamentosProduto() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.AGENDAMENTOS_PRODUTO);
    }

    public boolean getProdutosVencidos() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.PRODUTOS_VENCIDOS);
    }

    public boolean getProdutosVencendo() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.VENCIMENTO_PRODUTO);
    }

    public boolean getParcelasVecendo() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.PARCELA_VENCENDO);
    }

    public boolean getEvtAmbientes() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.AGENDAMENTOS_AMBIENTE);
    }

    public boolean getProdutosVendidos() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.PRODUTOS_VENDIDOS);
    }

    public boolean getAgendar() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.A_AGENDAR);
    }

    public boolean getFaturar() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.A_FATURAR);
    }

    public boolean getProfissional() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.AGENDAMENTOS_PROFISSIONAL);
    }

    public boolean getParcelasVencidas() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS);
    }

    public boolean getPosVenda(){
        return getEvento() != null && getEvento().equals(TipoEventoEnum.POS_VENDA);
    }
    
    public Integer getNrFaltas() {
        return nrFaltas;
    }

    public void setNrFaltas(Integer nrFaltas) {
        this.nrFaltas = nrFaltas;
    }

    public List<GenericoTO> getPendencias() {
        return pendencias;
    }

    public void setPendencias(List<GenericoTO> pendencias) {
        this.pendencias = pendencias;
    }

    public List<GenericoTO> getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(List<GenericoTO> colaboradores) {
        this.colaboradores = colaboradores;
    }

    public String getCodigosAmbientes() {
        return codigosAmbientes;
    }

    public void setCodigosAmbientes(String codigosAmbientes) {
        this.codigosAmbientes = codigosAmbientes;
    }

    public String getCodigosColaboradores() throws Exception {
        if (UteisValidacao.emptyList(getColaboradores())) {
            return codigosColaboradores;
        } else {
            return Uteis.getListaEscolhidos(getColaboradores(), "Selecionado", "Codigo", true);
        }

    }

    public void setCodigosColaboradores(String codigosColaboradores) {
        this.codigosColaboradores = codigosColaboradores;
    }

    public String getCodigosProdutoSessao() throws Exception {
        if (UteisValidacao.emptyList(getProdutosSessao())) {
            return codigosProdutoSessao;
        } else
            return Uteis.getListaEscolhidos(getProdutosSessao(), "Selecionado", "Codigo", true);

    }

    public void setCodigosProdutoSessao(String codigosProdutoSessao) {
        this.codigosProdutoSessao = codigosProdutoSessao;
    }

    public String getCodigosTiposProdutos() throws Exception {
        if (UteisValidacao.emptyList(getTipoProdutos())) {
            return codigosTiposProdutos;
        } else
            return Uteis.getListaEscolhidos(getTipoProdutos(), "Selecionado", "CodigoString", false);

    }

    public void setCodigosTiposProdutos(String codigosTiposProdutos) {
        this.codigosTiposProdutos = codigosTiposProdutos;
    }

    public String getNomesPendencias() throws Exception {
        if (UteisValidacao.emptyList(getPendencias())) {
            return nomesPendencias;
        } else
            return Uteis.getListaEscolhidos(getPendencias(), "Selecionado", "Label", false);

    }

    public void setNomesPendencias(String nomesPendencias) {
        this.nomesPendencias = nomesPendencias;
    }

    public String getCodigosRiscos() throws Exception {
        if (UteisValidacao.emptyList(getRiscos())) {
            return codigosRiscos;
        } else
            return Uteis.getListaEscolhidos(getRiscos(), "Selecionado", "Codigo", true);

    }

    public void setCodigosRiscos(String codigosRiscos) {
        this.codigosRiscos = codigosRiscos;
    }

    public int getDiaMais() {
        return diaMais;
    }

    public void setDiaMais(int diaMais) {
        this.diaMais = diaMais;
    }

    public int getDiaMenos() {
        return diaMenos;
    }

    public void setDiaMenos(int diaMenos) {
        this.diaMenos = diaMenos;
    }

    public Integer getDiasParcelasVencidasInicial() {
        return diasParcelasVencidasInicial;
    }

    public void setDiasParcelasVencidasInicial(Integer diasParcelasVencidasInicial) {
        this.diasParcelasVencidasInicial = diasParcelasVencidasInicial;
    }

    public Integer getDiasParcelasVencidasFinal() {
        return diasParcelasVencidasFinal;
    }

    public void setDiasParcelasVencidasFinal(Integer diasParcelasVencidasFinal) {
        this.diasParcelasVencidasFinal = diasParcelasVencidasFinal;
    }

    public Integer getNrDiasInicioFreePass() {
        return nrDiasInicioFreePass;
    }

    public void setNrDiasInicioFreePass(Integer nrDiasInicioFreePass) {
        this.nrDiasInicioFreePass = nrDiasInicioFreePass;
    }

    public ProdutoVO getProdutoVO() {
        if (produtoVO == null) {
            produtoVO = new ProdutoVO();
        }
        return produtoVO;
    }

    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    public List<ProdutoVO> getListaProdutosVOs() {
        if (listaProdutosVOs == null) {
            listaProdutosVOs = new ArrayList<ProdutoVO>(0);
        }
        return listaProdutosVOs;
    }

    public void setListaProdutosVOs(List<ProdutoVO> listaProdutosVOs) {
        this.listaProdutosVOs = listaProdutosVOs;
    }

    public void adicionarProdutoVencidos(ProdutoVO produtoVO) throws Exception {
        for (ProdutoVO existente : getListaProdutosVOs()) {
            if (existente.getCodigo().intValue() == produtoVO.getCodigo().intValue()) {
                return;
            }
        }
        getListaProdutosVOs().add(produtoVO);
    }

    public Integer getQtdMinParcelasVencidas() {
        return qtdMinParcelasVencidas;
    }

    public void setQtdMinParcelasVencidas(Integer qtdMinParcelasVencidas) {
        this.qtdMinParcelasVencidas = qtdMinParcelasVencidas;
    }

    public boolean isBoletoParcelasVencendo() {
        return boletoParcelasVencendo;
    }

    public void setBoletoParcelasVencendo(boolean boletoParcelasVencendo) {
        this.setModeloPadraoBoleto(boletoParcelasVencendo);
        this.boletoParcelasVencendo = boletoParcelasVencendo;
    }

    public String getCodigoErroRemessa() {
        if (codigoErroRemessa == null) {
            codigoErroRemessa = "";
        }
        return codigoErroRemessa;
    }

    public void setCodigoErroRemessa(String codigoErroRemessa) {
        this.codigoErroRemessa = codigoErroRemessa;
    }

    public boolean getParcelasRecorrencia() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.PARCELAS_RECORRENCIA);
    }

    public String getCodigosProdutosVencidos() {
        return codigosProdutosVencidos;
    }

    public void setCodigosProdutosVencidos(String codigosProdutoVencidos) {
        this.codigosProdutosVencidos = codigosProdutoVencidos;
    }

    public String[] getListaCodigosProdutosVencidos() {
        if (codigosProdutosVencidos != null && !"".equals(codigosProdutosVencidos.trim()))
            return codigosProdutosVencidos.split(",");

        return new String[0];
    }

    public Integer getMaxValorInt() {
        return maxValor == null ? 0 : maxValor.intValue();
    }

    public void setMaxValorInt(Integer maxValor) {
        this.maxValor = maxValor == null ? 0.0 : maxValor.doubleValue();
    }

    public boolean getCartoesVencendo() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.CARTOES_VENCENDO);
    }
    public boolean getCartoesVencidos() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.CARTOES_VENCIDOS);
    }

    public int getDiasRemessa() {
        return diasRemessa;
    }

    public void setDiasRemessa(int diasRemessa) {
        this.diasRemessa = diasRemessa;
    }

    public boolean getAcessosEmIntervaloDias() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.ACESSOS_INTERVALO_DIAS);
    }

    public boolean getCancelados() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.CANCELADO_INTERVALO_DIAS);
    }

    public boolean getItensNaoAprovados() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.ITENS_NAO_APROVADOS_REMESSA);
    }

    public boolean isModeloPadraoBoleto() {
        if (getEvento() == null ||
                (!getEvento().equals(TipoEventoEnum.PARCELA_VENCENDO) && !getEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS))) {
            return false;
        }
        return modeloPadraoBoleto;
    }

    public boolean getItensConvenio() {
        return getEvento() != null && getEvento().equals(TipoEventoEnum.CONVENIO_COBRANCA);
    }

    public void setModeloPadraoBoleto(boolean modeloPadraoBoleto) {
        this.modeloPadraoBoleto = modeloPadraoBoleto;
    }
}

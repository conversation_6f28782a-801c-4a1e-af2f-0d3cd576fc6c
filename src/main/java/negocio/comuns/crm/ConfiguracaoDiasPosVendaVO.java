package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

/**
 * Reponsável por manter os dados da entidade ConfiguracaoDiasPosVenda. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 * @see negocio.comuns.crm.ConfiguracaoSistemaCRMVO
 */

public class ConfiguracaoDiasPosVendaVO extends SuperVO {

    protected Integer codigo;
    protected ConfiguracaoSistemaCRMVO configuracaoSistemaCRM;
    protected Integer nrDia;
    protected String descricao;
    private boolean ativo = true;
    private boolean verificado = false;
    private String siglaResponsavelPeloContato;

    /**
     * Construtor padrão da classe <code>ConfiguracaoDiasPosVenda</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ConfiguracaoDiasPosVendaVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ConfiguracaoDiasPosVendaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(ConfiguracaoDiasPosVendaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO deve ser informado!");
        }
        if (obj.getNrDia() == null) {
            throw new ConsistirException("O campo NÚMERO DE DIAS deve ser informado!");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
        setDescricao(getDescricao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(null);
        setNrDia(0);
        setDescricao("");
        setAtivo(true);
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getNrDia() {
        if (nrDia == null) {
            nrDia = 0;
        }
        return (nrDia);
    }

    public void setNrDia(Integer nrDia) {
        this.nrDia = nrDia;
    }

    public ConfiguracaoSistemaCRMVO getConfiguracaoSistemaCRM() {
        if (configuracaoSistemaCRM == null) {
            configuracaoSistemaCRM = new ConfiguracaoSistemaCRMVO();
        }
        return (configuracaoSistemaCRM);
    }

    public void setConfiguracaoSistemaCRM(ConfiguracaoSistemaCRMVO configuracaoSistemaCRM) {
        this.configuracaoSistemaCRM = configuracaoSistemaCRM;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public String getMontarResponsavel(){
        if(getSiglaResponsavelPeloContato().equals("RPF"))
            return "Responsáveis pelas fases";

        return TipoColaboradorEnum.getTipo(getSiglaResponsavelPeloContato()).getDescricao();
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public boolean isVerificado() {
        return verificado;
    }

    public void setVerificado(boolean verificado) {
        this.verificado = verificado;
    }

    public String getSiglaResponsavelPeloContato() {
        return siglaResponsavelPeloContato;
    }

    public void setSiglaResponsavelPeloContato(String siglaResponsavelPeloContato) {
        this.siglaResponsavelPeloContato = siglaResponsavelPeloContato;
    }
}

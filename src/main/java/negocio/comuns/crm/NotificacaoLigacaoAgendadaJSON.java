package negocio.comuns.crm;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * Descrição: Carrega as informações necessárias para exibição das notificações de<br>
 *     ligações agendadas no módulo CRM
 *
 * Projeto: ZillyonWeb-T
 *
 * <AUTHOR> - 28/mar/2018 às 11:21
 * Pacto Soluções - Todos os direitos reservados
 */
public class NotificacaoLigacaoAgendadaJSON extends SuperJSON {

    private final static String TITULO = "ZW CRM - Ligação Agendada";
    private String conteudo;
    private String nomeCliente;
    private Integer codigoUsuario;
    private Integer codigoAberturaMeta;
    private Integer codigoFecharMeta;
    private Integer codigoFecharMetaDetalhada;
    private Integer codigoAgendamento;
    private Date dataAgendamento;
    private String minuto;
    private String hora;
    private String chaveFotoCliente;

    public String getTitulo() {
        return TITULO;
    }

    public String getConteudo() {
        if (conteudo == null) {
            conteudo = String.format("Você têm uma ligação agendada com o cliente '%s' às %s:%s!", nomeCliente, hora, minuto);
        }
        return conteudo;
    }

    public Integer getCodigoUsuario() {
        return codigoUsuario;
    }

    public void setCodigoUsuario(Integer codigoUsuario) {
        this.codigoUsuario = codigoUsuario;
    }

    public Integer getCodigoAberturaMeta() {
        return codigoAberturaMeta;
    }

    public void setCodigoAberturaMeta(Integer codigoAberturaMeta) {
        this.codigoAberturaMeta = codigoAberturaMeta;
    }

    public Integer getCodigoFecharMeta() {
        return codigoFecharMeta;
    }

    public void setCodigoFecharMeta(Integer codigoFecharMeta) {
        this.codigoFecharMeta = codigoFecharMeta;
    }

    public Integer getCodigoFecharMetaDetalhada() {
        return codigoFecharMetaDetalhada;
    }

    public void setCodigoFecharMetaDetalhada(Integer codigoFecharMetaDetalhada) {
        this.codigoFecharMetaDetalhada = codigoFecharMetaDetalhada;
    }

    public Integer getCodigoAgendamento() {
        return codigoAgendamento;
    }

    public void setCodigoAgendamento(Integer codigoAgendamento) {
        this.codigoAgendamento = codigoAgendamento;
    }

    public Date getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(Date dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getMinuto() {
        return minuto;
    }

    public void setMinuto(String minuto) {
        this.minuto = minuto;
    }

    public String getChaveFotoCliente() {
        return chaveFotoCliente;
    }

    public void setChaveFotoCliente(String chaveFotoCliente) {
        this.chaveFotoCliente = chaveFotoCliente;
    }

    public String getUrlFotoCliente() {
        return Uteis.getPaintFotoDaNuvem(chaveFotoCliente);
    }
}

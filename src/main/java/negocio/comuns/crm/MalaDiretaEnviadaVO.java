package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.crm.MalaDireta;

/**
 * Reponsável por manter os dados da entidade MalaDiretaEnviadaVO. Classe do tipo
 * VO - Value Object composta pelos atributos da entidade com visibilidade
 * protegida e os métodos de acesso a estes atributos. Classe utilizada para
 * apresentar e manter em memória os dados desta entidade.
 * 
 * @see SuperVO
 * @see MalaDireta
 */
public class MalaDiretaEnviadaVO extends SuperVO {

    protected Integer codigo;
    protected MalaDiretaVO malaDiretaVO;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Pessoa </code>.
     */
    protected ClienteVO clienteVO;
    protected PassivoVO passivoVO;
    protected IndicadoVO indicadoVO;

    /**
     * Construtor padrão da classe <code>MalaDiretaEnviadaVO</code>. Cria uma
     * nova instância desta entidade, inicializando automaticamente seus
     * atributos (Classe VO).
     */
    public MalaDiretaEnviadaVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>MalaDiretaEnviadaVO</code>. Todos os tipos de consistência de
     * dados são e devem ser implementadas neste método. São validações típicas:
     * verificação de campos obrigatórios, verificação de valores válidos para
     * os atributos.
     *
     * @exception ConsistirException
     *                Se uma inconsistência for encontrada aumaticamente é
     *                gerada uma exceção descrevendo o atributo e o erro
     *                ocorrido.
     */
    public static void validarDados(MalaDiretaEnviadaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        // if(obj.getPassivoVO().getCodigo() == 0){
        // if (obj.getPessoa().getCodigo() == null ||
        // obj.getPessoa().getCodigo().intValue() == 0 ) {
        // throw new ConsistirException("Pessoa");
        // }
        // }
        // if(obj.getPessoa().getCodigo() == 0){
        // if(obj.getPassivoVO().getCodigo() == null ||
        // obj.getPassivoVO().getCodigo().intValue() == 0){
        // throw new ConsistirException("Passivo");
        // }
        // }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(null);

    }

    /**
     * @return the clienteVO
     */
    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    /**
     * @param clienteVO
     *            the clienteVO to set
     */
    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public MalaDiretaVO getMalaDiretaVO() {
        if (malaDiretaVO == null) {
            malaDiretaVO = new MalaDiretaVO();
        }
        return (malaDiretaVO);
    }

    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PassivoVO getPassivoVO() {
        if (passivoVO == null) {
            passivoVO = new PassivoVO();
        }
        return passivoVO;
    }

    public void setPassivoVO(PassivoVO passivoVO) {
        this.passivoVO = passivoVO;
    }

    public IndicadoVO getIndicadoVO() {
        if (indicadoVO == null) {
            indicadoVO = new IndicadoVO();
        }
        return indicadoVO;
    }

    public void setIndicadoVO(IndicadoVO indicadoVO) {
        this.indicadoVO = indicadoVO;
    }

    public Boolean getApresentarColunaPessoa() {
        return getClienteVO().getPessoa().getCodigo() != 0;
    }

    public Boolean getApresentarColunaPassivo() {
        return getPassivoVO().getCodigo() != 0;
    }

    public Boolean getApresentarColunaIndicado() {
        return getIndicadoVO().getCodigo() != 0;
    }

    public String getEmails() {
        StringBuilder emails = new StringBuilder();
        if (getClienteVO().getPessoa().getEmailVOs().size() > 0) {
            int qtdEmails = getClienteVO().getPessoa().getEmailVOs().size();
            for (int i = 0; i < qtdEmails; i++) {
                Object obj = getClienteVO().getPessoa().getEmailVOs().get(i);
                if (obj instanceof EmailVO) {
                    EmailVO email = (EmailVO) obj;
                    if (i == (qtdEmails - 1)) {
                        emails.append(email.getEmail());
                    } else {
                        emails.append(email.getEmail() + "; ");
                    }
                }
            }
        }
        return emails.toString();
    }

    public String getTelefones() {
        StringBuilder telefones = new StringBuilder();
        if (getClienteVO().getPessoa().getTelefoneVOs().size() > 0) {
            int qtdTelefones = getClienteVO().getPessoa().getTelefoneVOs().size();
            for (int i = 0; i < qtdTelefones; i++) {
                Object obj = getClienteVO().getPessoa().getTelefoneVOs().get(i);
                if (obj instanceof TelefoneVO) {
                    TelefoneVO telefone = (TelefoneVO) obj;
                    if (Uteis.validarTelefoneCelular(telefone.getNumero())) {
                        if (telefones.toString().isEmpty()) {
                            telefones.append(telefone.getNumero());
                        } else {
                            telefones.append(";"+telefone.getNumero());
                        }
                    }
                }
            }
        }
        return telefones.toString();
    }

    public String getTelefonesCelulares() {
        StringBuilder telefones = new StringBuilder();
        if (getClienteVO().getPessoa().getTelefoneVOs().size() > 0) {
            int qtdTelefones = getClienteVO().getPessoa().getTelefoneVOs().size();
            for (int i = 0; i < qtdTelefones; i++) {
                Object obj = getClienteVO().getPessoa().getTelefoneVOs().get(i);
                if (obj instanceof TelefoneVO) {
                    TelefoneVO telefone = (TelefoneVO) obj;
                    if (Uteis.validarTelefoneCelular(telefone.getNumero()) && telefone.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo())) {
                        if (telefones.toString().isEmpty()) {
                            telefones.append(telefone.getNumero());
                        } else {
                            telefones.append(";"+telefone.getNumero());
                        }
                    }
                }
            }
        }
        return telefones.toString();
    }

    public String getNome() {
        if (getClienteVO().getCodigo() != 0 ) {
            return getClienteVO().getNome_Apresentar();
        } else if (getPassivoVO().getCodigo() != 0) {
            return getPassivoVO().getNome();
        } else if (getIndicadoVO().getCodigo() != 0) {
            return getIndicadoVO().getNomeIndicado();
        }
        return "";
    }

    public Integer getCodigoPessoa() {
        if (getClienteVO().getCodigo() != 0) {
            return getClienteVO().getPessoa().getCodigo();
        }
        return 0;
    }
}

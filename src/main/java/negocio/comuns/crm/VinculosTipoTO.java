package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.SuperTO;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 11/09/13
 * Time: 13:41
 */
public class VinculosTipoTO extends SuperTO {

    private static final long serialVersionUID = 894064212307659030L;
    private String tipoVinculo = "";
    private Integer qtdVinculos = 0;
    private String classCSS = "";

    public String getTipoVinculo() {
        return tipoVinculo;
    }

    public void setTipoVinculo(String tipoVinculo) {
        this.tipoVinculo = tipoVinculo;
    }

    public String getTipoVinculo_Apresentar() {
        return TipoColaboradorEnum.getTipo(getTipoVinculo()).getDescricao();
    }

    public Integer getQtdVinculos() {
        return qtdVinculos;
    }

    public void setQtdVinculos(Integer qtdVinculos) {
        this.qtdVinculos = qtdVinculos;
    }

    public String getQtdVinculos_Apresentar() {
        if (getQtdVinculos() == 1) {
            return getQtdVinculos() + " vínculo";
        }
        return getQtdVinculos() + " vínculos";
    }
    public String getClassCSS() {
        return classCSS;
    }

    public void setClassCSS(String classCSS) {
        this.classCSS = classCSS;
    }

    public void selecionarLinha() {
        setClassCSS("#06abfa8a");
    }

    public void desselecionarLinha() {
        setClassCSS("");
    }
}

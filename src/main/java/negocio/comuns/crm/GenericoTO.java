package negocio.comuns.crm;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;

public class GenericoTO extends SuperTO {

    private static final long serialVersionUID = -4454465789323421405L;
    private int codigo;
    private String codigoString;
    private Double valor;
    private boolean escolhido = false;
    private Boolean selecionado = false;
    private String label;
    
    public GenericoTO() {

    }
    public GenericoTO(int codigo, String label) {
        this.codigo = codigo;
        this.label = label;
    }

    public GenericoTO(String codigo, String label) {
        this.codigoString = codigo;
        this.label = label;
    }

    public GenericoTO(String string1, String string2, int codigo, Double valor) {
        this.codigoString = string2;
        this.label = string1;
        this.codigo = codigo;
        this.valor = valor;
    }

    public String getValor_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValor());
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getCodigoString() {
        return codigoString;
    }

    public void setCodigoString(String codigoString) {
        this.codigoString = codigoString;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public boolean equals(Object o) {
        return o instanceof GenericoTO && ((GenericoTO) o).getCodigo() == getCodigo();
    }

    public String getDescricaoCurta() {
        return label.length() > 30 ? label.substring(0, 27) + "..." : label;
    }

    public String getColor() {
        return valor < 0.0 ? "red" : "green";
    }

    public boolean isEscolhido() {
        return escolhido;
    }

    public void setEscolhido(boolean escolhido) {
        this.escolhido = escolhido;
    }


}

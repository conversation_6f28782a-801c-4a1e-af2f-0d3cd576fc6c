package negocio.comuns.crm;

import negocio.comuns.utilitarias.Ordenacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 01/05/2020
 *
 * Enum utilizado no convenio de cobranca e na transação
 *
 */
public enum TipoGymBotEnum {

    NENHUM(0, "NENHUM"),
    GYMBOT_LITE(1, "GYMBOT_LITE"),
    GYMBOT_PRO(2, "GYMBOT_PRO"),
    ;

    private Integer codigo;
    private String descricao;

    private TipoGymBotEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoGymBotEnum consultarPorCodigo(Integer codigo) {
        for (TipoGymBotEnum tipo : values()) {
            if (tipo.getCodigo().equals(codigo)) {
                return tipo;
            }
        }
        return NENHUM;
    }

    public static List<SelectItem> obterListSelectItem() {
        List<SelectItem> lista = new ArrayList<>();
        for (TipoGymBotEnum amb : TipoGymBotEnum.values()) {
            if (!amb.equals(TipoGymBotEnum.NENHUM)) {
                lista.add(new SelectItem(amb, amb.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(TipoGymBotEnum.NENHUM, ""));
        return lista;
    }
}

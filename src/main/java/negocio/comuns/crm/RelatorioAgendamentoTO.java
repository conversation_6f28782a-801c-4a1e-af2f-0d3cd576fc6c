/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.crm;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class RelatorioAgendamentoTO extends SuperTO{
    
    private Integer totalLigacoes = 0;
    private Integer totalVisita = 0;
    private Integer totalAulaExperimental = 0;
    private List<AgendaVO> lista = new ArrayList<AgendaVO>();
    private Integer totalConvertidosLigacoes = 0;
    private Integer totalConvertidosVisita = 0;
    private Integer totalConvertidosAulaExperimental = 0;
    private Integer totalConvertidos = 0;
    private Double totalProfAluVisitantes = 0.0;
    private Double totalProfAluConvertidos = 0.0;
    private Integer totalComparecidos = 0;
    private Integer totalExecutadas = 0;
    private Integer totalAconteceu = 0;
    private Integer totalAcontecer = 0;
    private List<ClienteVO> clientesComAgendamento;
    private List<ClienteVO> clientesQueExecutaramAgendamento;
    private Integer totalDiarias = 0;
    private Integer totalConvertidosDiarias = 0;
    private List<ClienteVO> clientesConvertidos;

    public RelatorioAgendamentoTO() {
    }

    public Integer getTotalLigacoes() {
        return totalLigacoes;
    }

    public void setTotalLigacoes(Integer totalLigacoes) {
        this.totalLigacoes = totalLigacoes;
    }

    public Integer getTotalVisita() {
        return totalVisita;
    }

    public void setTotalVisita(Integer totalVisita) {
        this.totalVisita = totalVisita;
    }

    public Integer getTotalAulaExperimental() {
        return totalAulaExperimental;
    }

    public void setTotalAulaExperimental(Integer totalAulaExperimental) {
        this.totalAulaExperimental = totalAulaExperimental;
    }

    public List<AgendaVO> getLista() {
        return lista;
    }

    public void setLista(List<AgendaVO> lista) {
        this.lista = lista;
    }

    public Integer getTotalConvertidosLigacoes() {
        return totalConvertidosLigacoes;
    }

    public void setTotalConvertidosLigacoes(Integer totalConvertidosLigacoes) {
        this.totalConvertidosLigacoes = totalConvertidosLigacoes;
    }

    public Integer getTotalConvertidosVisita() {
        return totalConvertidosVisita;
    }

    public void setTotalConvertidosVisita(Integer totalConvertidosVisita) {
        this.totalConvertidosVisita = totalConvertidosVisita;
    }

    public Integer getTotalConvertidosAulaExperimental() {
        return totalConvertidosAulaExperimental;
    }

    public void setTotalConvertidosAulaExperimental(Integer totalConvertidosAulaExperimental) {
        this.totalConvertidosAulaExperimental = totalConvertidosAulaExperimental;
    }

    public Integer getTotalConvertidos() {
        return totalConvertidos;
    }

    public void setTotalConvertidos(Integer totalConvertidos) {
        this.totalConvertidos = totalConvertidos;
    }

    public Double getTotalProfAluConvertidos() {
        return totalProfAluConvertidos;
    }

    public void setTotalProfAluConvertidos(Double totalProfAluConvertidos) {
        this.totalProfAluConvertidos = totalProfAluConvertidos;
    }

    public Double getTotalProfAluVisitantes() {
        return totalProfAluVisitantes;
    }

    public void setTotalProfAluVisitantes(Double totalProfAluVisitantes) {
        this.totalProfAluVisitantes = totalProfAluVisitantes;
    }

    public Integer getTotalComparecidos() {
        return totalComparecidos;
    }

    public Integer getTotalExecutadas() {
        return totalExecutadas;
    }

    public void setTotalExecutadas(Integer totalExecutadas) {
        this.totalExecutadas = totalExecutadas;
    }

    public void setTotalComparecidos(Integer totalComparecidos) {
        this.totalComparecidos = totalComparecidos;
    }

    public Integer getTotalAconteceu() {
        return totalAconteceu;
    }

    public void setTotalAconteceu(Integer totalAconteceu) {
        this.totalAconteceu = totalAconteceu;
    }

    public Integer getTotalAcontecer() {
        return totalAcontecer;
    }

    public void setTotalAcontecer(Integer totalAcontecer) {
        this.totalAcontecer = totalAcontecer;
    }

    public List<ClienteVO> getClientesComAgendamento() {
        return clientesComAgendamento;
    }

    public void setClientesComAgendamento(List<ClienteVO> clientesComAgendamento) {
        this.clientesComAgendamento = clientesComAgendamento;
    }

    public List<ClienteVO> getClientesQueExecutaramAgendamento() {
        return clientesQueExecutaramAgendamento;
    }

    public void setClientesQueExecutaramAgendamento(List<ClienteVO> clientesQueExecutaramAgendamento) {
        this.clientesQueExecutaramAgendamento = clientesQueExecutaramAgendamento;
    }

    public Integer getTotalAlunosAgendados() {
        return clientesComAgendamento == null ? 0 : clientesComAgendamento.size();
    }

    public Integer getTotalAlunosQueExecutaramAgendamento() {
        return clientesQueExecutaramAgendamento == null ? 0 : clientesQueExecutaramAgendamento.size();
    }
    
    public Integer getTotalAlunosConvertidos() {
        return clientesConvertidos == null ? 0 : clientesConvertidos.size();
    }

    public String getMediaAulasAgendadas() {
        Double media = 0.0;
        if (clientesComAgendamento != null && !clientesComAgendamento.isEmpty()
                && totalAulaExperimental != null && totalAulaExperimental > 0) {
            media = totalAulaExperimental / (clientesComAgendamento.size() * 1.0);
        }
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(media);
    }

    public String getIndicePresencaAulas() {
        Double indice = 0.0;
        if (totalExecutadas != null && totalExecutadas > 0
                && totalAulaExperimental != null && totalAulaExperimental > 0) {
            indice = (totalExecutadas * 100.0) / totalAulaExperimental;
        }
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(indice);
    }

    public String getIndiceAgendamentosConvertidos() {
        Double indice = 0.0;
        if (totalExecutadas != null && totalExecutadas > 0
                && totalConvertidosAulaExperimental != null && totalConvertidosAulaExperimental > 0) {
            indice = (totalConvertidosAulaExperimental * 100.0) / totalExecutadas;
        }
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(indice);
    }

    public String getIndiceProfessoresAlunos() {
        Double indice = 0.0;

        if (totalProfAluConvertidos > 0) {
            indice = 100 / ((totalProfAluVisitantes + totalProfAluConvertidos) / totalProfAluConvertidos);
        }

        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(indice);
    }

    public String getPercentualAgendamentosExecutados() {
        return "";
    }
    
    public Integer getTotalDiarias() {
        return totalDiarias;
    }

    public void setTotalDiarias(Integer totalDiarias) {
        this.totalDiarias = totalDiarias;
    }

    public Integer getTotalConvertidosDiarias() {
        return totalConvertidosDiarias;
    }

    public void setTotalConvertidosDiarias(Integer totalConvertidosDiarias) {
        this.totalConvertidosDiarias = totalConvertidosDiarias;
    }
    
    public List<ClienteVO> getClientesConvertidos() {
        return clientesConvertidos;
    }

    public void setClientesConvertidos(List<ClienteVO> clientesConvertidos) {
        this.clientesConvertidos = clientesConvertidos;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.TipoContatoCRM;

/**
 * Enumerador necessário para identificar qual o meio de envio de mensagens
 * <AUTHOR>
 */
public enum MeioEnvio {

    EMAIL(1, "E-mail", TipoContatoCRM.CONTATO_EMAIL),
    SMS(2, "SMS", TipoContatoCRM.CONTATO_SMS),
    APP(3, "APP", TipoContatoCRM.CONTATO_APP),
    CRM_EXTRA(4, "CRM-EXTRA", null),
    FTP(5, "FTP", null),
    WHATSAPP(6, "WHATSAPP", TipoContatoCRM.CONTATO_WHATSAPP),
    GYMBOT(7, "GYMBOT", TipoContatoCRM.CONTATO_GYMBOT),
    GYMBOT_PRO(8, "GYMBOT-PRO", TipoContatoCRM.CONTATO_GYMBOT_PRO);

    private Integer codigo;
    private String descricao;
    private TipoContatoCRM tipoContatoCRM;

    private MeioEnvio(Integer codigo, String descricao, TipoContatoCRM tipoContatoCRM) {
        setCodigo(codigo);
        setDescricao(descricao);
        setTipoContatoCRM(tipoContatoCRM);
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return descricao;
    }

    /**
     * @param descricao the descricao to set
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static MeioEnvio getMeioEnvioPorCodigo(Integer codigo) {
        MeioEnvio tipo = null;
        for (MeioEnvio meioEnvio : MeioEnvio.values()) {
            if (meioEnvio.getCodigo().equals(codigo)) {
                tipo = meioEnvio;
            }
        }
        return tipo;
    }

    public TipoContatoCRM getTipoContatoCRM() {
        return tipoContatoCRM;
    }

    public void setTipoContatoCRM(TipoContatoCRM tipoContatoCRM) {
        this.tipoContatoCRM = tipoContatoCRM;
    }
}

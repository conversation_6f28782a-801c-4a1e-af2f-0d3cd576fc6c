/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.crm;

import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public enum ErroEnvioMailing {

    EMAIL_INVALIDO(1, "E-mail inválido"),
    SEM_TELEFONE(2, "Nenhum celular válido"),
    TIMEOUT(9998, "Timeout da requisição"),
    NAO_ESTA_NO_TREINO(9999, "Aluno não está no treino"),
    DESCONHECIDO(10000, "Erro SMTP não informado. Confira o limite diário do seu servidor de e-mail."),
    LIMITE_EXCEDIDO(10001, "Limite Excedido"),
    MESMO_TELEFONE(10002, "SMS já enviado p/ este número"),
    INVALIDO_BLOQUEADO(10003, "Número inválido / Bloqueado denúncia SPAM"),
    BOTCONVERSA_INVALIDO(10004, "Mensagem inválida, permitido apenas tag de pagamento / número inválido"),
    MESMA_CONVERSA(10005, "O Fluxo de conversa já foi encaminhado p/ o cliente."),
    THIRD_PARTY(3, "O provedor de e-mail bloqueou o acesso de aplicativos");

    public static ErroEnvioMailing getErro(int codigo) {
        ErroEnvioMailing tipo = null;
        for (ErroEnvioMailing meioEnvio : ErroEnvioMailing.values()) {
            if (meioEnvio.getCodigo() == codigo) {
                tipo = meioEnvio;
            }
        }
        return tipo;
    }

    private int codigo;
    private String descricao;

    private ErroEnvioMailing(int cod, String desc){
        codigo = cod;
        descricao = desc;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static String normalizarMensagem(String msg){
        if(UteisValidacao.emptyString(msg)){
            return "";
        }
        if(msg.toLowerCase().contains("third party error")){
            return THIRD_PARTY.getDescricao();
        }
        return msg;
    }

}

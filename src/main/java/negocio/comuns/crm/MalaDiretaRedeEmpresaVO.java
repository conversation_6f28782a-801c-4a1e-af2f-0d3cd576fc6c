package negocio.comuns.crm;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import servicos.discovery.RedeDTO;

import java.util.Date;

public class MalaDiretaRedeEmpresaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer malaDireta;
    private String chaveOrigem;
    private String chaveDestino;
    private Integer empresaDestino;
    private Date datacadastro;
    private Date dataatualizacao;
    private String nomeUnidade;
    private String mensagemSituacao;
    private RedeDTO redeDTO;
    private Integer malaDiretaReplicado;
    private boolean selecionado;

    public MalaDiretaRedeEmpresaVO(Integer malaDireta, String chaveOrigem, String chaveDestino, Integer empresaDestino) {
        this.malaDireta = malaDireta;
        this.chaveOrigem = chaveOrigem;
        this.chaveDestino = chaveDestino;
        this.empresaDestino = empresaDestino;
    }

    public MalaDiretaRedeEmpresaVO(String nomeUnidade, Integer malaDireta, String chaveOrigem, String chaveDestino, Integer empresaDestino) {
        this.nomeUnidade = nomeUnidade;
        this.malaDireta = malaDireta;
        this.chaveOrigem = chaveOrigem;
        this.chaveDestino = chaveDestino;
        this.empresaDestino = empresaDestino;
    }

    public MalaDiretaRedeEmpresaVO() {
        
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMalaDireta() {
        return malaDireta;
    }

    public void setMalaDireta(Integer malaDireta) {
        this.malaDireta = malaDireta;
    }

    public String getChaveOrigem() {
        return chaveOrigem;
    }

    public void setChaveOrigem(String chaveOrigem) {
        this.chaveOrigem = chaveOrigem;
    }

    public String getChaveDestino() {
        return chaveDestino;
    }

    public void setChaveDestino(String chaveDestino) {
        this.chaveDestino = chaveDestino;
    }

    public Date getDatacadastro() {
        return datacadastro;
    }

    public void setDatacadastro(Date datacadastro) {
        this.datacadastro = datacadastro;
    }

    public Boolean getDataAtualizacaoInformada() {
        return getDataatualizacao() != null;
    }
    public Date getDataatualizacao() {
        return dataatualizacao;
    }

    public void setDataatualizacao(Date dataatualizacao) {
        this.dataatualizacao = dataatualizacao;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getMensagemSituacao() {
        return mensagemSituacao;
    }

    public void setMensagemSituacao(String mensagemSituacao) {
        this.mensagemSituacao = mensagemSituacao;
    }

    public RedeDTO getRedeDTO() {
        return redeDTO;
    }

    public void setRedeDTO(RedeDTO redeDTO) {
        this.redeDTO = redeDTO;
    }

    public Integer getMalaDiretaReplicado() {
        return malaDiretaReplicado;
    }

    public void setMalaDiretaReplicado(Integer malaDiretaReplicado) {
        this.malaDiretaReplicado = malaDiretaReplicado;
    }

    public Integer getEmpresaDestino() {
        return empresaDestino;
    }

    public void setEmpresaDestino(Integer empresaDestino) {
        this.empresaDestino = empresaDestino;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }
}

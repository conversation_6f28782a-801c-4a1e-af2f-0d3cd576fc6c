package negocio.comuns.crm;

import annotations.arquitetura.FKJson;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created by glauco on 15/01/14.
 */
public class TextoPadraoVO extends SuperVO {

    private Integer codigo = 0;
    private String descricao = "";
    private String linkDocs = "";
    private FasesCRMEnum faseCRM = FasesCRMEnum.AGENDAMENTO;
    private String tipoContato = "EM";
    private String mensagemPadrao = "";
    @FKJson
    private EmpresaVO empresa = new EmpresaVO();

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public FasesCRMEnum getFaseCRM() {
        return faseCRM;
    }

    public void setFaseCRM(FasesCRMEnum faseCRM) {
        this.faseCRM = faseCRM;
    }

    public String getMensagemPadrao() {
        return mensagemPadrao;
    }

    public void setMensagemPadrao(String mensagemPadrao) {
        this.mensagemPadrao = mensagemPadrao;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getLinkDocs() {
        return linkDocs;
    }

    public void setLinkDocs(String linkDocs) {
        this.linkDocs = linkDocs;
    }

    public String getTipoContato() {
        return tipoContato;
    }

    public String getTipoContatoDescricao(){
        return UteisValidacao.emptyString(this.tipoContato) ? "" : Dominios.getTipoContatoHistoricoContato().get(this.tipoContato);
    }

    public void setTipoContato(String tipoContato) {
        this.tipoContato = tipoContato;
    }
}

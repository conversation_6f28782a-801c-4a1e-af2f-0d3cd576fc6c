package negocio.comuns.crm;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.enumeradores.OcorrenciaEnum;
import br.com.pactosolucoes.enumeradores.TagsEmailRemessaEnum;
import br.com.pactosolucoes.enumeradores.TipoAgendamentoEnum;
import br.com.pactosolucoes.enumeradores.TipoEventoEnum;
import edu.emory.mathcs.backport.java.util.Arrays;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.lang.StringUtils;
import relatorio.controle.basico.MailingFiltrosTO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade MalaDireta. Classe do
 * tipo VO - Value Object composta pelos atributos da entidade com visibilidade
 * protegida e os métodos de acesso a estes atributos. Classe utilizada para
 * apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class MalaDiretaVO extends SuperVO {

    protected Integer codigo;
    protected Date dataEnvio;
    protected Date dataCriacao;
    protected String mensagem;
    protected String configs;
    protected String mensagemComAlteracaoTag;
    protected String titulo;
    protected MeioEnvio meioDeEnvio;
    private EmpresaVO empresa;
    private MailingAgendamentoVO agendamento;
    private Integer evento;
    private Integer horaInicio = Uteis.gethoraHH(Calendario.hoje());
    private Date vigenteAte;
    private String sql = "";
    private TipoAgendamentoEnum tipoAgendamento;
    private List<MailingHistoricoVO> historico;
    private MailingFiltrosTO mailingFiltros;
    private Date ultimaExecucao;
    private TipoEventoEnum tipoEvento;
    private ConfigEventoMailingTO cfgEvento;
    private Boolean excluida = false;
    private String chaveAntiga = "";
    private boolean contatoAvulso = false;
    private String faseEnvio;
    private Integer tipoPergunta;
    private String opcoes;
    private boolean crmExtra = false;
    private Boolean importarLista = false;
    private Boolean smsMarketing = false;
    private Boolean envioHabilitado = true;
    private Boolean listaImportada = false;
    private Integer codAberturaMeta; // usado em contatos retroativos para repescagem.
    public static List<String> TAGS = Arrays.asList(new String[]{"TAG_NOME", "TAG_SALDOPONTOS", "TAG_PNOME", "TAG_BOLETO", "TAG_PIX", "TAG_PARCELAS_COBRANCA","TAG_PESQUISA",
            "TAG_PAGONLINE",
            "NOME_EMPRESA",
            "TAG_CADCARTAO",
            TagsEmailRemessaEnum.TAG_PARCELA_VALOR.getTag(),
            TagsEmailRemessaEnum.TAG_CARTAO_MASCARADO.getTag(),
            TagsEmailRemessaEnum.TAG_CODIGO_RETORNO.getTag(),
            TagsEmailRemessaEnum.TAG_DESCRICAO_RETORNO.getTag(),
            TagsEmailRemessaEnum.TAG_ACAO_REALIZAR_RETORNO.getTag(),
            TagsEmailRemessaEnum.TAG_DATA_RETORNO.getTag()});
    private Integer diasPosVenda;
    private Boolean metaExtraIndividual = false;
    private String tipoConsultorMetaExtraIndividual = "";
    private boolean todasEmpresas = false;
    private Integer questionario; //PESQUISA

    private Boolean statusEntregabilidade;

    private Integer idTemplate;

    /**
     * Atributo responsável por manter os objetos da classe
     * <code>MalaDiretaEnviadaVO</code>.
     */
    private List<MalaDiretaEnviadaVO> malaDiretaEnviadaVOs;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Colaborador </code>.
     */
    protected UsuarioVO remetente;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>ModeloMensagem </code>.
     */
    protected ModeloMensagemVO modeloMensagem;
    //atributo declarado aqui pois todas as vezes que vou mandar em email preciso da configuração do sistema.
    private ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO;
    //atributo não será gravado no banco
    protected int totalPessoaMalaDireta;
    protected int totalPessoaMalaDiretaEnviada = 0;
    protected Boolean incluirURL = false;
    protected String urlRenovacaoRematricula = "";
    private int quantidadeMinimaAcessos;
    private int quantidadeMaximaAcessos;
    private int intervaloDias;
    private Integer tipoCancelamento;
    @NaoControlarLogAlteracao
    private List<PixVO> listaPixEnviar;
    private  String urlwebhoobotconversa = "";
    private ConfiguracaoIntegracaoGymbotProVO configGymbotPro;
    private int codigoFluxoGymBotConversa;

    public Integer getTipoCancelamento() {
        return tipoCancelamento;
    }

    public void setTipoCancelamento(Integer tipoCancelamento) {
        this.tipoCancelamento = tipoCancelamento;
    }

    /**
     * Construtor padrão da classe <code>MalaDireta</code>. Cria uma
     * nova instância desta entidade, inicializando automaticamente seus
     * atributos (Classe VO).
     */
    public MalaDiretaVO() {
        super();
        inicializarDados();
    }

    public String getDescricaoEvento(){
    	return UteisValidacao.emptyNumber(evento) ? "" : TipoEventoEnum.obter(evento).getDescricao() + " - ";
    }
    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da
     * classe <code>MalaDiretaVO</code>.
     */
    public static void validarUnicidade(List<MalaDiretaVO> lista, MalaDiretaVO obj) throws ConsistirException {
        for (MalaDiretaVO repetido : lista) {
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>MalaDiretaVO</code>. Todos os tipos de consistência de
     * dados são e devem ser implementadas neste método. São validações típicas:
     * verificação de campos obrigatórios, verificação de valores válidos para
     * os atributos.
     */
    public static void validarDados(MalaDiretaVO obj)
            throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getDataCriacao() == null) {
            throw new ConsistirException("O campo DATA CRIAÇÃO está vazio !");
        }

        if (obj.getDataEnvio() == null) {
            throw new ConsistirException(obj.getUsarAgendamento() ? "O campo INICIAR EM está vazio !" : "O campo DATA ENVIO está vazio !");
        }
        if ((obj.getRemetente() == null)
                || (obj.getRemetente().getCodigo() == 0)) {
            throw new ConsistirException("O campo REMETENTE está vazio !");
        }
        if (obj.getTitulo().equals("") && !obj.isCrmExtra()) {
            throw new ConsistirException("O campo TÍTULO está vazio !");
        }

        if (obj.getCfgEvento() != null && obj.getCfgEvento().getEvento() != null &&
                (obj.getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELA_VENCENDO) || obj.getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS) )&&
                obj.getCfgEvento().isBoletoParcelasVencendo() && obj.getCfgEvento().isModeloPadraoBoleto()) {
            //Adicionado TAG_BOLETO se não no envio não é correto!
            obj.setMensagem("UTILIZA MODELO PADRÃO PARA BOLETO TAG_BOLETO");
        }

        if (!obj.getMensagem().contains("malaDiretaImagemCRM") && !obj.isCrmExtra()){
	        String limpaCampoMensagem = Uteis.retiraTags(obj.getMensagem(), true);
	        if (limpaCampoMensagem.trim().equals("")) {
	            throw new ConsistirException("O campo MENSAGEM está vazio !");
	        }
        }
        if(obj.getCfgEvento() != null && obj.getCfgEvento() != null){
            if (obj.getCfgEvento().getEventoCodigo() != null && obj.getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.PARCELAS_VENCIDAS.getCodigo())) {
                if(UteisValidacao.emptyNumber(obj.getCfgEvento().getDiasParcelasVencidasInicial())){
                    throw new ConsistirException("Adicione a faixa de dias.");
                }else{
                    if(!UteisValidacao.emptyNumber(obj.getCfgEvento().getDiasParcelasVencidasFinal()) &&
                            obj.getCfgEvento().getDiasParcelasVencidasInicial() > obj.getCfgEvento().getDiasParcelasVencidasFinal()){
                        throw new ConsistirException("A faixa inicial de dias deve ser menor que a final.");
                    }
                }
            }
        }
        if(obj.meioDeEnvio != null && obj.meioDeEnvio.equals(MeioEnvio.APP) && obj.mensagem.length() > 255) {
            throw new ConsistirException("Não é possível enviar uma notificação com mais de 255 caracteres");
        }

    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(null);
        setDataEnvio(Calendario.hoje());
        setDataCriacao(Calendario.hoje());
        setMensagem("");
        setTitulo("");
        setmalaDiretaEnviadaVOs(new ArrayList<MalaDiretaEnviadaVO>());
        setRemetente(new UsuarioVO());
        setModeloMensagem(new ModeloMensagemVO());
        setConfiguracaoSistemaCRMVO(new ConfiguracaoSistemaCRMVO());
    }

    public String getMensagemFormatada(String mensagem) {
        String temp = getMensagem();
        temp = temp.replace("<texto>", mensagem);
        return temp;
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>MalaDiretaEnviadaVO</code> ao List
     * <code>MalaDiretaEnviadaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>MalaDiretaEnviadaVO</code> - getPessoa().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param obj
     *            Objeto da classe <code>MalaDiretaEnviadaVO</code> que será
     *            adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjMalaDiretaEnviadaVOs(MalaDiretaEnviadaVO obj) throws Exception {
        MalaDiretaEnviadaVO.validarDados(obj);
        obj.setMalaDiretaVO(this);
        int index = 0;
        for (MalaDiretaEnviadaVO objExistente : getMalaDiretaEnviadaVOs()) {
            if (objExistente.getClienteVO().getPessoa().getCodigo().equals(
                    obj.getClienteVO().getPessoa().getCodigo())) {
                getMalaDiretaEnviadaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getMalaDiretaEnviadaVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>MalaDiretaEnviadaVO</code> no List
     * <code>MalaDiretaEnviadaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>MalaDiretaEnviadaVO</code> - getPessoa().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param pessoa
     *            Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjMalaDiretaEnviadaVOs(Integer pessoa)
            throws Exception {
        int index = 0;
        for (MalaDiretaEnviadaVO objExistente : getMalaDiretaEnviadaVOs()) {
            if (objExistente.getClienteVO().getPessoa().getCodigo().equals(pessoa)) {
                getMalaDiretaEnviadaVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>MalaDiretaEnviadaVO</code> no List
     * <code>MalaDiretaEnviadaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>MalaDiretaEnviadaVO</code> - getPessoa().getCodigo() -
     * como identificador (key) do objeto no List.
     *
     * @param pessoa
     *            Parâmetro para localizar o objeto do List.
     */
    public MalaDiretaEnviadaVO consultarObjMalaDiretaEnviadaVO(Integer pessoa) throws Exception {
        for (MalaDiretaEnviadaVO objExistente : getMalaDiretaEnviadaVOs()) {
            if (objExistente.getClienteVO().getPessoa().getCodigo().equals(pessoa)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Retorna o objeto da classe <code>ModeloMensagem</code> relacionado com (
     * <code>MalaDireta</code>).
     */
    public ModeloMensagemVO getModeloMensagem() {
        if (modeloMensagem == null) {
            modeloMensagem = new ModeloMensagemVO();
        }
        return (modeloMensagem);
    }

    /**
     * Define o objeto da classe <code>ModeloMensagem</code> relacionado com (
     * <code>MalaDireta</code>).
     */
    public void setModeloMensagem(ModeloMensagemVO obj) {
        this.modeloMensagem = obj;
    }

    /**
     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (
     * <code>MalaDireta</code>).
     */
    public UsuarioVO getRemetente() {
        if (remetente == null) {
            remetente = new UsuarioVO();
        }
        return (remetente);
    }

    /**
     * Define o objeto da classe <code>Colaborador</code> relacionado com (
     * <code>MalaDireta</code>).
     */
    public void setRemetente(UsuarioVO obj) {
        this.remetente = obj;
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe
     * <code>MalaDiretaEnviadaVO</code>.
     */
    public List<MalaDiretaEnviadaVO> getMalaDiretaEnviadaVOs() {
        if (malaDiretaEnviadaVOs == null) {
            malaDiretaEnviadaVOs = new ArrayList<MalaDiretaEnviadaVO>();
        }
        return (malaDiretaEnviadaVOs);
    }

    /**
     * Define Atributo responsável por manter os objetos da classe
     * <code>MalaDiretaEnviadaVO</code>.
     */
    public void setmalaDiretaEnviadaVOs(List<MalaDiretaEnviadaVO> malaDiretaEnviadaVOs) {
        this.malaDiretaEnviadaVOs = malaDiretaEnviadaVOs;
    }

    public String getTitulo() {
        if (titulo == null) {
            titulo = "";
        }
        return (titulo);
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getMensagem() {
        if (mensagem == null) {
            mensagem = "";
        }
        return (mensagem);
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Date getDataCriacao() {
        if (dataCriacao == null) {
            dataCriacao = Calendario.hoje();
        }
        return (dataCriacao);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato
     * padrão dd/mm/aaaa.
     */
    public String getDataCriacao_Apresentar() {
        return (Uteis.getData(dataCriacao));
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public Date getDataEnvio() {
        return (dataEnvio);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato
     * padrão dd/mm/aaaa.
     */
    public String getDataEnvio_Apresentar() {
        return ( dataEnvio == null ? "" : Uteis.getData(dataEnvio));
    }

    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ConfiguracaoSistemaCRMVO getConfiguracaoSistemaCRMVO() {
        return configuracaoSistemaCRMVO;
    }

    public void setConfiguracaoSistemaCRMVO(ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) {
        this.configuracaoSistemaCRMVO = configuracaoSistemaCRMVO;
    }

    public int getTotalPessoaMalaDireta() {
        return totalPessoaMalaDireta;
    }

    public void setTotalPessoaMalaDireta(int totalPessoaMalaDiretaEnviada) {
        this.totalPessoaMalaDireta = totalPessoaMalaDiretaEnviada;
    }

    public String getMensagemComAlteracaoTag() {
        if (mensagemComAlteracaoTag == null) {
            mensagemComAlteracaoTag = "";
        }
        return mensagemComAlteracaoTag;
    }

    public void setMensagemComAlteracaoTag(String mensagemComAlteracaoTag) {
        this.mensagemComAlteracaoTag = mensagemComAlteracaoTag;
    }

    public void setUrlRenovacaoRematricula(String urlRenovacaoRematricula) {
        this.urlRenovacaoRematricula = urlRenovacaoRematricula;
    }

    public String getUrlRenovacaoRematricula() {
        return urlRenovacaoRematricula;
    }

    public void setIncluirURL(Boolean incluirURL) {
        this.incluirURL = incluirURL;
    }

    public Boolean getIncluirURL() {
        return incluirURL;
    }

    public MeioEnvio getMeioDeEnvioEnum() {
        if (meioDeEnvio == null) {
            meioDeEnvio = MeioEnvio.EMAIL;
        }
        return meioDeEnvio;
    }

    public void setMeioDeEnvioEnum(MeioEnvio meioDeEnvioEnum) {
        this.meioDeEnvio = meioDeEnvioEnum;
    }

    public void setMeioDeEnvio(Integer meioDeEnvio) throws Exception {
        this.meioDeEnvio = MeioEnvio.getMeioEnvioPorCodigo(meioDeEnvio);
    }

    public Integer getMeioDeEnvio() {
        return meioDeEnvio == null ? MeioEnvio.EMAIL.getCodigo(): getMeioDeEnvioEnum().getCodigo();
    }

    public int getTotalPessoaMalaDiretaEnviada() {
        return totalPessoaMalaDiretaEnviada;
    }

    public void setTotalPessoaMalaDiretaEnviada(int totalPessoaMalaDiretaEnviada) {
        this.totalPessoaMalaDiretaEnviada = totalPessoaMalaDiretaEnviada;
    }

    public String verificarTermosSpam(ConfiguracaoSistemaCRMVO cfg){
    	String msg = Uteis.retirarAcentuacaoRegex(this.getTitulo()).toUpperCase() + " "
    					+ Uteis.retirarAcentuacaoRegex(this.getMensagem()).toUpperCase();

    	return verificar(cfg, msg);
    }

	/**
	 * Responsável por
	 * <AUTHOR> Alcides
	 * 19/10/2012
	 */
	public static String verificar(ConfiguracaoSistemaCRMVO cfg, String msg) {
		String termosEncontrados = "";
    	for(String termo : cfg.getTermosSpam()){
    		if(msg.indexOf(Uteis.retirarAcentuacaoRegex(termo).toUpperCase()) >= 0
    				|| msg.indexOf(Uteis.trocarAcentuacaoPorAcentuacaoHTML(termo).toUpperCase()) >= 0)
    			termosEncontrados += termosEncontrados.indexOf(termo) > 0 ? "" : (","+termo);
    	}
		return termosEncontrados.replaceFirst(",", "");
	}

	public void setEmpresa(EmpresaVO empresa) {
		this.empresa = empresa;
	}

	public EmpresaVO getEmpresa() {
		if(empresa == null){
			empresa = new EmpresaVO();
		}
		return empresa;
	}

	public void setAgendamento(MailingAgendamentoVO agendamento) {
		this.agendamento = agendamento;
	}

	public MailingAgendamentoVO getAgendamento() {
        if (agendamento == null) {
            agendamento = new MailingAgendamentoVO();
        }
		return agendamento;
	}

	public Boolean getUsarAgendamento() {
		return tipoAgendamento != null && (tipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO));
	}

	public void setEvento(Integer evento) {
		this.evento = evento;
	}

	public Integer getEvento() {
		return evento;
	}

	public void setHoraInicio(Integer horaInicio) {
		this.horaInicio = horaInicio;
	}

	public Integer getHoraInicio() {
		return horaInicio;
	}

	public String getHoraInicioString() {
		String hora =  horaInicio.toString();
		return hora.length() > 1 ? hora+":00": "0"+hora+":00";
	}

	public void setVigenteAte(Date vigenteAte) {
		this.vigenteAte = vigenteAte;
	}

	public Date getVigenteAte() {
		return vigenteAte;
	}

	public void setSql(String sql) {
		this.sql = sql;
	}

	public String getSql() {
		return sql;
	}

    public String getSql(String campos) throws Exception {
        if (UteisValidacao.emptyNumber(getCfgEvento().getEventoCodigo())
                || TipoEventoEnum.obter(getCfgEvento().getEventoCodigo()) == null
                || getAgendamento().getOcorrencia() == null) {


            int pos = -1;
            int contagem = 0;
            while (true) {
                pos = getSql().indexOf("%s", pos + 1);
                if (pos < 0) break;
                contagem++;
            }
            String[] args = new String[contagem];
            args[0] = campos;
            for (int i = 1; i < contagem; i++) {
                args[i] = Uteis.getDataFormatoBD(Calendario.hoje());
            }
            return String.format(getSql(), args);

        }

        //aqui devo escolher quais parametros o sistema deve utilizar para terminar de montar a sql
//            de consulta do mailing
        TipoEventoEnum eventoPreDefinido = TipoEventoEnum.obter(getCfgEvento().getEventoCodigo());
        switch (eventoPreDefinido) {
            //para evento 24 horas
            case VINTE_QUATRO_HORAS:
                return String.format(getSql(), campos, Uteis.getDataFormatoBD(Calendario.hoje()), Uteis.getDataFormatoBD(Calendario.hoje()));
            //para evento aniversariantes
            case EVENTO_ANIVERSARIANTES:
                Date data1 = Calendario.hoje();
                Date data2 = Calendario.hoje();

                //se for instantaneo, usar as datas informadas
                if (getAgendamento().getOcorrencia().equals(OcorrenciaEnum.INSTANTANEO)) {
                    data1 = getCfgEvento().getInicio();
                    data2 = getCfgEvento().getFim();
                } else
                    //se for mensal, usar primeiro e ultimo dia do mes
                    if (getAgendamento().getOcorrencia().equals(OcorrenciaEnum.MENSALMENTE)) {
                        data1 = Uteis.obterPrimeiroDiaMes(data1);
                        data2 = Uteis.obterUltimoDiaMes(data2);
                    }
                return String.format(getSql(), campos,
                        Uteis.getDataFormatoBD(data1), Uteis.getDataFormatoBD(data1),
                        Uteis.getDataFormatoBD(data2), Uteis.getDataFormatoBD(data2));
            //evento matriculados
            case EVENTO_MATRICULADOS:
                return setarDatasSQLGenerico(campos, null);
            //evento matriculados
            case EVENTO_REMATRICULADOS:
                return setarDatasSQLGenerico(campos, null);
            //evento matriculados
            case EVENTO_RENOVADOS:
                return setarDatasSQLGenerico(campos, null);
            //evento matriculados
            case VISITANTES:
                return setarDatasSQLGenerico(campos, null);
            //a renovar
            case RENOVAR:
                return setarDatasSQLGenerico(campos, null);
            //GRUPO DE RISCO
            case GRUPO_RISCO:
                return String.format(getSql(), campos, getCfgEvento().getCodigosRiscos());
            case FALTOSOS:
                return String.format(getSql(), campos, Uteis.getDataFormatoBD(Uteis.obterDataAnterior(Calendario.hoje(), 1)),
                        ((getCfgEvento().getNrFaltasMaior() ? " >=  " : " = ") + getCfgEvento().getNrFaltas()));
            case SALDO_PONTOS:
//                if (getCfgEvento().getNrFaltasMaior()) {
//                    return String.format(getSql() + " and sw.situacao = 'AT' ", campos, getCfgEvento().getNrFaltas());
//                } else {
                return String.format(getSql(), campos, getCfgEvento().getNrFaltas());
//                }
            case DEBITO:
                return String.format(getSql(), campos,
                        Uteis.getDataFormatoBD(Calendario.hoje()),
                        Uteis.getDataFormatoBD(Calendario.hoje()),
                        getCfgEvento().getMinimoDiasVencido().toString(),
                        getCfgEvento().getMinValor().toString(),
                        getCfgEvento().getMaxValor().toString());
            case PENDENCIAS:
                String listaEscolhidos = getCfgEvento().getNomesPendencias();
                return String.format(getSql(), campos, ("%(" + listaEscolhidos.replaceAll(",", "|") + ")%"));
            case INDICADOS:
                if (campos.equals(" count (distinct cli.*) ")) {
                    return "SELECT COUNT(*) FROM (" + getSql() + (getCfgEvento().getNaoClientes() ? " and cliente is null" : "") + ") AS i";
                } else {
                    return getSql() + (getCfgEvento().getNaoClientes() ? " and cliente is null" : "");
                }
//a renovar
            case VENCIMENTO_PRODUTO:
                StringBuilder listaProdutosVencendo = new StringBuilder();
                if (!getCfgEvento().getListaProdutosVOs().isEmpty()) {
                    for (int i = 0; i < getCfgEvento().getListaProdutosVOs().size(); i++) {
                        ProdutoVO obj = getCfgEvento().getListaProdutosVOs().get(i);
                        listaProdutosVencendo.append(obj.getCodigo());
                        if (i < getCfgEvento().getListaProdutosVOs().size() - 1) {
                            listaProdutosVencendo.append(",");
                        }
                    }
                } else {
                    setSql(getSql().replace("AND p.codigo in (%s)", (!getCfgEvento().getCodigosProdutosVencidos().isEmpty()? "AND p.codigo in (" + getCfgEvento().getCodigosProdutosVencidos() + ")":"")));
                }
                return setarDatasSQLGenerico(campos, listaProdutosVencendo.toString().isEmpty() ? "" : listaProdutosVencendo.toString());
            case COMPRA_PRODUTO:
                String listaTipos = getCfgEvento().getCodigosTiposProdutos();
                listaTipos = "'" + listaTipos.replaceAll(",", "','") + "'";
                return setarDatasSQLGenerico(campos, listaTipos);
            case AGENDAMENTOS_PRODUTO:
                String listaProdutos = getCfgEvento().getCodigosProdutoSessao();
                return setarDatasSQLGenerico(campos, listaProdutos.isEmpty() ? "" : "and age.id_produto IN(" + listaProdutos + ") ");
            case AGENDAMENTOS_AMBIENTE:
                String listaAmbientes = Uteis.getListaEscolhidos(getCfgEvento().getAmbientes(), "Selecionado", "Codigo", true);
                return setarDatasSQLGenerico(campos, listaAmbientes.isEmpty() ? "" : "and age.id_ambiente IN(" + listaAmbientes + ") ");
            case AGENDAMENTOS_PERIODO:
                return setarDatasSQLGenerico(campos, "");
            case PRODUTOS_VENDIDOS:
                String listaProdutosVendidos = getCfgEvento().getCodigosProdutoSessao();
                return setarDatasSQLGenerico(campos, listaProdutosVendidos.isEmpty() ? "" : "AND p.codigo in (" + listaProdutosVendidos + ")");
            case A_AGENDAR:
                String listaProdutosAgendar = getCfgEvento().getCodigosProdutoSessao();
                return String.format(getSql(), campos, listaProdutosAgendar.isEmpty() ? "" : "and age.id_produto IN (" + listaProdutosAgendar + ")");
            case A_FATURAR:
                String listaProdutosFaturar = getCfgEvento().getCodigosProdutoSessao();
                return String.format(getSql(), campos, listaProdutosFaturar.isEmpty() ? "" : "and age.id_produto IN (" + listaProdutosFaturar + ")");
            case AGENDAMENTOS_PROFISSIONAL:
                String profissionais = getCfgEvento().getCodigosColaboradores();
                return setarDatasSQLGenerico(campos, profissionais.isEmpty() ? "" : "and age.id_colaborador IN(" + profissionais + ") ");
            case PRODUTOS_VENCIDOS:
                String produtosVencidos = getCfgEvento().getCodigosProdutosVencidos();
                if(UteisValidacao.emptyString(produtosVencidos)) {
                    setSql(getSql().replace("AND p.codigo in (%s)", ""));
                }
                return setarDatasSQLGenerico(campos,UteisValidacao.emptyString(produtosVencidos) ? "" : produtosVencidos);

            case PARCELA_VENCENDO:
                StringBuilder sql = new StringBuilder();
                sql.append(getSql());

                if (getCfgEvento().isBoletoParcelasVencendo()) {
                    sql.append(" AND pesp.codigo in \n");
                    sql.append("(SELECT mpar.pessoa FROM movparcela mpar \n");
                    sql.append("left join remessaitemmovparcela RIM ON RIM.movparcela = MPAR.codigo \n");
                    sql.append("left join cliente cl on cl.pessoa = mpar.pessoa \n");
                    sql.append("left join autorizacaocobrancacliente au on au.cliente = cl.codigo \n");
                    sql.append("left join conveniocobranca cc on cc.codigo = au.conveniocobranca \n");
                    sql.append("left join empresa emp on emp.codigo = mpar.empresa \n");
                    sql.append("WHERE MPAR.situacao = 'EA' \n");
                    sql.append("AND (RIM.codigo is not null \n");
                    sql.append("OR (RIM.codigo is null and \n");
                    sql.append("((emp.permitirmaillinggerarautorizacaocobrancaboleto and coalesce(emp.convenioboletopadrao,0) > 0) \n");
                    sql.append("OR (au.codigo is not null and au.ativa and au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO.getId()).append(")))) \n");
                    sql.append("AND mpar.datavencimento::date ");
                }else{
                    sql.append(" AND pesp.codigo in (select pessoa from movparcela where movparcela.situacao = 'EA' AND movparcela.datavencimento::date ");
                }

                if(getAgendamento().getOcorrencia() == OcorrenciaEnum.DIARIAMENTE)
                    sql.append(" BETWEEN '").append(Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), getCfgEvento().getDiaMenos()))).append("' AND '").append(Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), getCfgEvento().getDiaMenos()))).append("')\n");
                else if(getAgendamento().getOcorrencia() == OcorrenciaEnum.INSTANTANEO)
                    sql.append(" BETWEEN '").append(Uteis.getDataFormatoBD(getCfgEvento().getInicio())).append("' AND '").append(Uteis.getDataFormatoBD(getCfgEvento().getFim())).append("')\n");
                else
                    sql.append(" BETWEEN '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMesPrimeiraHora(Calendario.hoje()))).append("' AND '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMesUltimaHora(Calendario.hoje()))).append("')\n");


                setSql(sql.toString());

                return setarDatasSQLGenerico(campos, "");
            case ARMARIO_VENCENDO:
                return setarDatasSQLGenerico(campos, "");
            case PARCELAS_VENCIDAS:
                StringBuilder sb = getDatasVencimentos();
                StringBuilder sqlTemp = new StringBuilder(String.format(getSql(), campos, sb.toString(), getCfgEvento().getQtdMinParcelasVencidas().toString(), this.getCodigo() == null ? "0" : this.getCodigo().toString(),  Uteis.getDataFormatoBD(Calendario.hoje())));

                if (getCfgEvento().isBoletoParcelasVencendo()) {
                    sqlTemp.append(" AND exists (\n");
                    sqlTemp.append("SELECT MPA.codigo \n");
                    sqlTemp.append("FROM movparcela MPA \n");
                    sqlTemp.append("inner JOIN empresa emp on emp.codigo = mpa.empresa  \n");
                    sqlTemp.append("left JOIN cliente cl on cl.pessoa = mpa.pessoa \n");
                    sqlTemp.append("WHERE MPA.situacao = 'EA'  \n");
                    sqlTemp.append("AND MPA.pessoa = pes.codigo \n");
                    sqlTemp.append("AND MPA.descricao NOT LIKE '%MULTA%'  \n");
                    sqlTemp.append("AND ((emp.permitirmaillinggerarautorizacaocobrancaboleto and coalesce(emp.convenioboletopadrao,0) > 0)  \n");
                    sqlTemp.append("OR exists(select codigo from autorizacaocobrancacliente where ativa and tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO.getId()).append(" and cliente = cl.codigo))) \n");
                }
                return sqlTemp.toString();
            case PARCELAS_RECORRENCIA:
                String replace = "";
                if (!UteisValidacao.emptyString(getCfgEvento().getCodigoErroRemessa()) && !getCfgEvento().getCodigoErroRemessa().equals("0") && !getCfgEvento().getCodigoErroRemessa().equals("00")) {
                    replace = " and ((ri.props ilike '%StatusVenda="+getCfgEvento().getCodigoErroRemessa()+"%') or (t.codigoretorno ilike '"+getCfgEvento().getCodigoErroRemessa()+"')) ";
                }
                return String.format(getSql(), campos, getCfgEvento().getDiasRemessa(), replace);
            case CONTRATO_CREDITO_TREINO:
                return String.format(getSql(), campos, getCfgEvento().getMinValorInt().toString(), getCfgEvento().getMaxValorInt().toString());
            case POS_VENDA:
                ConfiguracaoSistemaCRMVO config = getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                String sqlNovo = "";
                if (config.isIncluirContratosRenovados()) {
                    sqlNovo = " where(sw.situacao = 'AT' AND((contrato.situacaocontrato NOT LIKE 'RN' AND cast(sw.datavigenciade as DATE) = (select cast(CURRENT_DATE as date) - INTERVAL '"+getDiasPosVenda()+" days' AS date)) "
                    + " OR(contrato.situacaocontrato LIKE 'RN' AND cast(sw.datavigenciade as DATE) = (select cast(CURRENT_DATE as date) - INTERVAL '"+getDiasPosVenda()+" days' AS date))))";
                }else{
                    sqlNovo = " where(sw.situacao = 'AT' AND((contrato.situacaocontrato NOT LIKE 'RN' AND cast(sw.datavigenciade as DATE) = \n" +
                              " (select cast(CURRENT_DATE as date) - INTERVAL '"+getDiasPosVenda()+" days' AS date))))";
                }
                return String.format(getSql(),campos,sqlNovo);
            case FREEPASS:
                return String.format(getSql(), campos, Uteis.getDataFormatoBD(Calendario.hoje()), (" = " + getCfgEvento().getNrDiasInicioFreePass()));
            case ITENS_NAO_APROVADOS_REMESSA:
                String remessaNaoAprovadas = " AND ri.props not ilike '%%StatusVenda=00%' ";
                return String.format(getSql(), campos, remessaNaoAprovadas);
            case EVENTO_DESISTENTES:
                return setarDatasSQLGenerico(campos, null);
        }

        if(StringUtils.countMatches(getSql(),"BETWEEN")>0)
            return setarDatasSQLGenerico(campos, "");

        return String.format(getSql(), campos);
    }

    public StringBuilder getDatasVencimentos() {
        StringBuilder sb = new StringBuilder();
        if(!UteisValidacao.emptyNumber(getCfgEvento().getDiasParcelasVencidasInicial())) {
            sb.append("(datavencimento  <= (now() - INTERVAL '").append(getCfgEvento().getDiasParcelasVencidasInicial()).append(" days')::date) ");
            if(!UteisValidacao.emptyNumber(getCfgEvento().getDiasParcelasVencidasFinal())){
                sb.append(" AND (datavencimento  >= (now() - INTERVAL '").append(getCfgEvento().getDiasParcelasVencidasFinal()).append(" days')::date) ");
            }
        }
        return sb;
    }

    public String setarDatasSQLGenerico(String campos, String param) throws Exception{

            int diasCRM =  (getConfiguracaoSistemaCRMVO().nrDiasParaClientePrevePerda == null ? 0: getConfiguracaoSistemaCRMVO().nrDiasParaClientePrevePerda );

            Date data1 = Calendario.getDataComHoraESegundos(Calendario.hoje(), "00:00:00");
            Date data2 = Calendario.getDataComHoraESegundos(Calendario.hoje(), "23:59:59");

            TipoEventoEnum eventoPreDefinido = TipoEventoEnum.obter(getCfgEvento().getEventoCodigo());

            if (getAgendamento().getOcorrencia().equals(OcorrenciaEnum.DIARIAMENTE)
            && eventoPreDefinido == TipoEventoEnum.EVENTO_DESISTENTES){
                        data1 = Uteis.somarDias(data1, -1 - diasCRM);
                data2 = Uteis.somarDias(data2, -1 - diasCRM);
            } else if (eventoPreDefinido == TipoEventoEnum.EVENTO_DESISTENTES
            && getAgendamento().getOcorrencia().equals(OcorrenciaEnum.SEMANALMENTE)) {
                data1 = Uteis.somarDias(data1, -7 - diasCRM);
                data2 = Uteis.somarDias(data2, -1 - diasCRM);
            }
            else if (getAgendamento().getOcorrencia().equals(OcorrenciaEnum.DIARIAMENTE)
                    && eventoPreDefinido != null && eventoPreDefinido.isDmais()) {
                data1 = Uteis.somarDias(data1, -1*getCfgEvento().getDiaMais());
                data2 = Uteis.somarDias(data2, -1*getCfgEvento().getDiaMais());
            }else if (getAgendamento().getOcorrencia().equals(OcorrenciaEnum.DIARIAMENTE)
                    && eventoPreDefinido != null && eventoPreDefinido.isDmenos()) {
                data1 = Uteis.somarDias(data1, getCfgEvento().getDiaMenos());
                data2 = Uteis.somarDias(data2, getCfgEvento().getDiaMenos());
            }else if (getAgendamento().getOcorrencia().equals(OcorrenciaEnum.INSTANTANEO)) {
                data1 = Calendario.getDataComHora(getCfgEvento().getInicio(), "00:00:00");
                data2 = Calendario.getDataComHora(getCfgEvento().getFim(), "23:59:59");
            }else if (getAgendamento().getOcorrencia().equals(OcorrenciaEnum.MENSALMENTE)) {
                data1 = Calendario.getDataComHora(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), "00:00:00");
                data2 = Calendario.getDataComHora(Uteis.obterUltimoDiaMes(Calendario.hoje()), "23:59:59");
            }

            if(getAgendamento().getOcorrencia().equals(OcorrenciaEnum.INCLUSAO_VISITANTE) && eventoPreDefinido == TipoEventoEnum.VISITANTES) {
                return substituirDatasComHHMMSS(campos, data1, data2);
            }
            if(param == null && eventoPreDefinido == TipoEventoEnum.EVENTO_REMATRICULADOS && this.getMeioDeEnvio() == MeioEnvio.SMS.getCodigo() )
                return substituirDatas(campos, data1, data2);;

            if(param == null && evento == null)
               return substituirDatasComHHMMSS(campos, data1, data2);;


            if(param == null && eventoPreDefinido == TipoEventoEnum.EVENTO_MATRICULADOS && this.getMeioDeEnvio() == MeioEnvio.EMAIL.getCodigo())
                return substituirDatas(campos, data1, data2);;

            if(param == null && eventoPreDefinido != TipoEventoEnum.EVENTO_MATRICULADOS )
                return substituirDatas(campos, data1, data2);

             return substituirDatas(campos, data1, data2, param);
        }

    public String substituirDatas(String campos, Date data1, Date data2, String param){
        return substituirDatas(campos, data1, data2).replaceFirst("%s", param == null ? "": param);
    }
    public String substituirDatas(String campos, Date data1, Date data2){
        String retorno = getSql().replaceFirst("%s", campos);
        for(int i=0; i<StringUtils.countMatches(getSql(),"BETWEEN"); i++){
            retorno = retorno.replaceFirst("%s", Uteis.getDataFormatoBD(data1));
            retorno = retorno.replaceFirst("%s", Uteis.getDataFormatoBD(data2));
        }
        return retorno;
    }

    public String substituirDatasComHHMMSS(String campos, Date data1, Date data2){
        String retorno = getSql().replaceFirst("%s", campos);
        for(int i=0; i<StringUtils.countMatches(getSql(),"BETWEEN"); i++){
            retorno = retorno.replaceFirst("%s", Uteis.getDataAplicandoFormatacao(data1, "yyyy-MM-dd HH:mm:ss"));
            retorno = retorno.replaceFirst("%s", Uteis.getDataAplicandoFormatacao(data2, "yyyy-MM-dd HH:mm:ss"));
        }
        return retorno;
    }

	public String getSqlCount() throws Exception{
		return getSql(" count (distinct cli.*) ");
	}

	public String getSqlClientes() throws Exception{
	    String campos = " distinct cli.*, sw.nomecliente ";
        if(getCfgEvento() != null &&
                getCfgEvento().getEvento() != null &&
                getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.ACESSOS_INTERVALO_DIAS.getCodigo())){
            campos += " ,CASE WHEN quantidadeAcessos IS NULL THEN 0 ELSE quantidadeAcessos END quantidadeAcessos ";
        }
		return getSql(campos);
	}
        public String getSqlClientesAmostra() throws Exception{
            StringBuilder campos = new StringBuilder(" distinct cli.*, sw.nomecliente as nome, sw.situacaocontrato,sw.datanascimento, sw.idade,"
                    + "sw.pesorisco, ");

            campos.append(" (select nome from empresa where empresa.codigo = cli.empresa) as nomeEmpresa, ");

            if(getCfgEvento() != null &&
                    getCfgEvento().getEvento() != null &&
                    getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.ACESSOS_INTERVALO_DIAS.getCodigo())){
                campos.append(" CASE WHEN quantidadeAcessos IS NULL THEN 0 ELSE quantidadeAcessos END quantidadeAcessos,");
            }
            if(getMailingFiltros().getDataCadastroMin() != null || getMailingFiltros().getDataCadastroMax() != null){
                campos.append(" pes.datacadastro, ");
            }
            if(getCfgEvento() != null && getCfgEvento().getEvento() != null
                    && getCfgEvento().getEventoCodigo() != null
                    && getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.FALTOSOS.getCodigo())){
                campos.append("('"+Uteis.getDataFormatoBD(Uteis.obterDataAnterior(Calendario.hoje(), 1))+"' :: DATE - ac.dthrentrada :: DATE) as diasfaltasemacesso,");
            }

            if(getCfgEvento() != null && getCfgEvento().getItensNaoAprovados()){
                campos.append("re.props as propsRemessa,\n");
                campos.append("ri.codigo as codigoRemessaItem,\n");
                campos.append("ri.props as propsRemessaItem,\n");
                campos.append("ri.tipo as tipoRemessaItem,\n");
                campos.append("mp.codigo as codigoParcela,\n");
                campos.append("mp.descricao as descricaoParcela,\n");
                campos.append("mp.valorparcela as valorParcela,\n");
                campos.append("mp.datavencimento as vencimentoParcela,\n");
            }

            campos.append(" ARRAY_TO_STRING(ARRAY(select numero from telefone where pessoa = cli.pessoa and telefone.tipotelefone = 'CE'), '<br/>') as telefones, ");
            campos.append(" ARRAY_TO_STRING(ARRAY(select email from email where emailcorrespondencia and bloqueadobounce IS FALSE  and pessoa = cli.pessoa), '<br/>') as emails ");

            return getSql(campos.toString());
	}

    public String getRemetenteApresentar() {
        return getRemetente().getNome();
    }

    public String getModeloMensagemApresentar() {
        return getModeloMensagem().getTitulo();
    }

	public String getSqlPessoa() throws Exception{
		return getSql(" cli.pessoa");
	}

    public String getSqlCodCliente() throws Exception{
		return getSql(" cli.codigo as codcliente ");
	}

	public void setTipoAgendamento(TipoAgendamentoEnum tipoAgendamento) {
		this.tipoAgendamento = tipoAgendamento;
	}

	public TipoAgendamentoEnum getTipoAgendamento() {
		return tipoAgendamento;
	}

	public boolean getAgendamentoPrevisto() {
        return tipoAgendamento != null && tipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO);
    }
	public boolean getAgendamentoInstantaneo() {
        return tipoAgendamento != null && tipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
    }

	public void setHistorico(List<MailingHistoricoVO> historico) {
		this.historico = historico;
	}

    public List<MailingHistoricoVO> getHistorico() {
        if (historico == null) {
            historico = new ArrayList<>();
        }
        return historico;
    }

	public void setMailingFiltros(MailingFiltrosTO mailingFiltros) {
		this.mailingFiltros = mailingFiltros;
	}

	public MailingFiltrosTO getMailingFiltros() {
        if (mailingFiltros == null) {
            mailingFiltros = new MailingFiltrosTO();
        }
		return mailingFiltros;
	}

	public void setUltimaExecucao(Date ultimaExecucao) {
		this.ultimaExecucao = ultimaExecucao;
	}

	public Date getUltimaExecucao() {
		return ultimaExecucao;
	}

    public String getDataApresentar() {
        if (tipoAgendamento == null || (tipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO) && ultimaExecucao == null)) {
            return dataEnvio == null ? "" : Uteis.getDataComHHMM(dataEnvio);
        }
        return (ultimaExecucao == null) ?
                ((dataEnvio.after(dataCriacao)) ? Uteis.getDataAplicandoFormatacao(dataEnvio, "dd/MM/yyyy HH:mm:ss") : "") :
                Uteis.getDataAplicandoFormatacao(ultimaExecucao, "dd/MM/yyyy HH:mm:ss");
    }

	public void setTipoEvento(TipoEventoEnum tipoEvento) {
		this.tipoEvento = tipoEvento;
	}

	public TipoEventoEnum getTipoEvento() {
		return tipoEvento;
	}

	public String getDescricaoEventoAgendado(){
		return tipoEvento == null ? "" : tipoEvento.getDescricao();
	}

    public ConfigEventoMailingTO getCfgEvento() {
        if (cfgEvento == null) {
            cfgEvento = new ConfigEventoMailingTO();
        }
        return cfgEvento;
    }

    public void setCfgEvento(ConfigEventoMailingTO cfgEvento) {
        this.cfgEvento = cfgEvento;
    }

    public Boolean getExcluida() {
        return excluida;
    }

    public void setExcluida(Boolean excluida) {
        this.excluida = excluida;
    }

    public boolean getContatoDireto(){
        return !getMalaDiretaEnviadaVOs().isEmpty();
    }

    public String getChaveAntiga() {
        return chaveAntiga;
    }

    public void setChaveAntiga(String chaveAntiga) {
        this.chaveAntiga = chaveAntiga;
    }

    public boolean isContatoAvulso() {
        return contatoAvulso;
    }

    public void setContatoAvulso(boolean contatoAvulso) {
        this.contatoAvulso = contatoAvulso;
    }

    public String getFaseEnvio() {
        if(faseEnvio == null) {
            faseEnvio= "";
        }
        return faseEnvio;
    }

    public void setFaseEnvio(String faseEnvio) {
        this.faseEnvio = faseEnvio;
    }

    public Integer getTipoPergunta() {
        return tipoPergunta;
    }

    public void setTipoPergunta(Integer tipoPergunta) {
        this.tipoPergunta = tipoPergunta;
    }

    public String getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(String opcoes) {
        this.opcoes = opcoes;
    }

    public boolean getApp(){
        return meioDeEnvio != null && getMeioDeEnvioEnum().equals(MeioEnvio.APP);
    }

    public boolean getEmail(){
        return meioDeEnvio != null && getMeioDeEnvioEnum().equals(MeioEnvio.EMAIL);
    }

    public boolean isFtp(){
        return meioDeEnvio != null && getMeioDeEnvioEnum().equals(MeioEnvio.FTP);
    }

    public boolean getSms(){
        return meioDeEnvio != null && getMeioDeEnvioEnum().equals(MeioEnvio.SMS);
    }

    public boolean isCrmExtra() {
        return crmExtra;
    }

    public void setCrmExtra(boolean crmExtra) {
        this.crmExtra = crmExtra;
    }

    public boolean isApresentarTags(ConfiguracaoSistemaCRMVO configCRM) {
        return getSms() || getApp() || (getEmail() && configCRM.isEnviarEmailIndividualmente());
    }

    public Integer getCodAberturaMeta() {
        return codAberturaMeta;
    }

    public void setCodAberturaMeta(Integer codAberturaMeta) {
        this.codAberturaMeta = codAberturaMeta;
    }

    public int getSizeTitulo() {
        if (MeioEnvio.APP.equals(getMeioDeEnvioEnum())) {
            return 50;
        }
        return 70;
    }

    public int getMaxLengthTitulo() {
        return 50;
    }

    public void setDiasPosVenda(Integer diasPosVenda) {
        this.diasPosVenda = diasPosVenda;
    }

    public Integer getDiasPosVenda() {
        if (diasPosVenda == null) {
            diasPosVenda = 0;
        }
        return diasPosVenda;
    }

    public Boolean getMetaExtraIndividual() {
        return metaExtraIndividual;
    }

    public void setMetaExtraIndividual(Boolean metaExtraIndividual) {
        this.metaExtraIndividual = metaExtraIndividual;
    }

    public String getTipoConsultorMetaExtraIndividual() {
        return tipoConsultorMetaExtraIndividual;
    }

    public void setTipoConsultorMetaExtraIndividual(String tipoConsultorMetaExtraIndividual) {
        this.tipoConsultorMetaExtraIndividual = tipoConsultorMetaExtraIndividual;
    }


    public int getQuantidadeMinimaAcessos() {
        return quantidadeMinimaAcessos;
    }

    public void setQuantidadeMinimaAcessos(int quantidadeMinimaAcessos) {
        this.quantidadeMinimaAcessos = quantidadeMinimaAcessos;
    }

    public int getQuantidadeMaximaAcessos() {
        return quantidadeMaximaAcessos;
    }

    public void setQuantidadeMaximaAcessos(int quantidadeMaximaAcessos) {
        this.quantidadeMaximaAcessos = quantidadeMaximaAcessos;
    }

    public int getIntervaloDias() {
        return intervaloDias;
    }

    public void setIntervaloDias(int intervaloDias) {
        this.intervaloDias = intervaloDias;
    }

    public String getNomeEmpresa() {
	    return getEmpresa().getNome();
    }

    public boolean isTodasEmpresas() {
        return todasEmpresas;
    }

    public void setTodasEmpresas(boolean todasEmpresas) {
        this.todasEmpresas = todasEmpresas;
    }

    public Integer getQuestionario() {
        if (questionario == null) {
            questionario = 0;
        }
        return questionario;
    }

    public void setQuestionario(Integer questionario) {
        this.questionario = questionario;
    }

    public Boolean getImportarLista() {
	    if(importarLista == null){
	        importarLista = false;
        }
        return importarLista;
    }

    public void setImportarLista(Boolean importarLista) {
        this.importarLista = importarLista;
	}

    public Boolean getListaImportada() {
        return listaImportada;
    }

    public void setListaImportada(Boolean listaImportada) {
        this.listaImportada = listaImportada;
    }

    public String getConfigs() {
        return configs;
    }

    public void setConfigs(String configs) {
        this.configs = configs;
    }

    public Boolean getSmsMarketing() {
        return smsMarketing;
    }

    public void setSmsMarketing(Boolean smsMarketing) {
        this.smsMarketing = smsMarketing;
    }

    public Boolean getEnvioHabilitado() {
        return envioHabilitado;
    }

    public void setEnvioHabilitado(Boolean envioHabilitado) {
        this.envioHabilitado = envioHabilitado;
    }

    public List<PixVO> getListaPixEnviar() {
	    if (listaPixEnviar == null) {
            listaPixEnviar = new ArrayList<>();
        }
        return listaPixEnviar;
    }

    public void setListaPixEnviar(List<PixVO> listaPixEnviar) {
        this.listaPixEnviar = listaPixEnviar;
    }

    public Boolean getStatusEntregabilidade() {
        return statusEntregabilidade;
    }

    public void setStatusEntregabilidade(Boolean statusEntregabilidade) {
        this.statusEntregabilidade = statusEntregabilidade;
    }

    public Integer getIdTemplate() {
        return idTemplate;
    }

    public void setIdTemplate(Integer idTemplate) {
        this.idTemplate = idTemplate;
    }

    public String getUrlwebhoobotconversa() {
        if (urlwebhoobotconversa == null) {
            urlwebhoobotconversa = "";
        }
        return urlwebhoobotconversa;
    }

    public void setUrlwebhoobotconversa(String urlwebhoobotconversa) {
        this.urlwebhoobotconversa = urlwebhoobotconversa;
    }

    public ConfiguracaoIntegracaoGymbotProVO getConfigGymbotPro() {
        return configGymbotPro;
    }

    public void setConfigGymbotPro(ConfiguracaoIntegracaoGymbotProVO configGymbotPro) {
        this.configGymbotPro = configGymbotPro;
    }

    public int getCodigoFluxoGymBotConversa() {
        return codigoFluxoGymBotConversa;
    }

    public void setCodigoFluxoGymBotConversa(int codigoFluxoGymBot) {
        this.codigoFluxoGymBotConversa = codigoFluxoGymBot;
    }


}

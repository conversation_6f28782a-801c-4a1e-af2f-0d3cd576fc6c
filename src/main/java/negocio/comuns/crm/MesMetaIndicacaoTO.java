package negocio.comuns.crm;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class MesMetaIndicacaoTO extends SuperTO {

    private final Date mesAnalisar;
    private double diasUteisMes;
    private double diasUteisPesquisa;

    private final int quantidadeMeta;
    private final boolean abertoSabado;
    private final boolean abertoDomingo;

    public MesMetaIndicacaoTO(Date mesAnalisar, int quantidadeMeta, boolean abertoSabado, boolean abertoDomingo) {
        this.mesAnalisar = mesAnalisar;
        this.quantidadeMeta = quantidadeMeta;
        this.abertoSabado = abertoSabado;
        this.abertoDomingo = abertoDomingo;
    }

    public void calcular(Date inicioPesquisa, Date finalPesquisa) throws Exception {
        Date primeiroDiaMes = Uteis.obterPrimeiroDiaMes(mesAnalisar);
        Date ultimoDiaMes = Uteis.obterUltimoDiaMes(mesAnalisar);

        this.diasUteisMes = Uteis.contarDiasUteis(primeiroDiaMes, ultimoDiaMes, abertoSabado, abertoDomingo);

        Date dataInicial = inicioPesquisa;
        Date dataFinal = finalPesquisa;
        if (Calendario.menor(inicioPesquisa, primeiroDiaMes)) {
            dataInicial = primeiroDiaMes;
        }
        if (Calendario.maior(finalPesquisa, ultimoDiaMes)) {
            dataFinal = ultimoDiaMes;
        }
        this.diasUteisPesquisa = Uteis.contarDiasUteis(dataInicial, dataFinal, abertoSabado, abertoDomingo);
    }

    public double getMetaMesAnalisado() {
        if (diasUteisMes == 0) {
            return 0.0;
        }
        return  normalizarResultado((diasUteisPesquisa / diasUteisMes) * quantidadeMeta);
    }

    private double normalizarResultado(double resultado) {
        if (resultado <= 0) {
            return 0.0;
        } else if (resultado < 1) {
            return 1.0;
        } else {
            return Math.ceil(resultado);
        }
    }

}

package negocio.comuns.crm;

import annotations.arquitetura.NaoControlarLogAlteracao;
import java.util.List;
import java.util.Date;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;

/**
 * Reponsável por manter os dados da entidade Passivo. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class PassivoVO extends SuperVO {

    protected Integer codigo;
    protected String nome;
    protected String telefoneResidencial;
    protected String telefoneCelular;
    protected String telefoneTrabalho;
    protected boolean wordPress;
    protected String descricaoEvento;
    protected Date dia;
    protected String observacao;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    protected UsuarioVO responsavelCadastro;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    protected UsuarioVO colaboradorResponsavel;
    private String email;
    private int bounced;
    private EventoVO evento;
    private ClienteVO clienteVO;
    private String tipoAgendamentoAnterior;
    private EmpresaVO empresaVO;
    private String colaboradorResp;
    private String empresa;
    protected Boolean selecionado = false;
    private Integer contrato;
    private ObjecaoVO objecao;
    private boolean lead = false;
    @NaoControlarLogAlteracao
    private LeadVO leadVO;
    private OrigemSistemaEnum origemSistemaEnum = OrigemSistemaEnum.ZW;
    private Boolean metaExtra = false;
    /**
     * Construtor padrão da classe <code>Passivo</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public PassivoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da classe <code>PassivoVO</code>.
     */
    public static void validarUnicidade(List<PassivoVO> lista, PassivoVO obj) throws ConsistirException {
        for (PassivoVO repetido : lista) {
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PassivoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(PassivoVO obj, boolean validaRD) throws ConsistirException {
        validarDados(obj, false, validaRD, false);
    }

    public static void validarDados(PassivoVO obj, boolean wordPress, boolean validaRD) throws ConsistirException {
        validarDados(obj, wordPress, validaRD, false);
    }

    public static void validarDados(PassivoVO obj, boolean wordPress, boolean validaRD, boolean bitrix) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getNome().equals("")) {
            throw new ConsistirException(("O campo NOME não pode ser vazio !"));
        }
        if(bitrix){
            if (obj.getEmail().isEmpty() && obj.getTelefoneCelular().isEmpty() ){
                throw new ConsistirException("Nenhum meio de contato informado.");
            }
        }else {
            if (validaRD && obj.getEmail().isEmpty()) {
                throw new ConsistirException("Necessário informar e-mail.");
            }
            if (!wordPress && !bitrix) {
                if (!validaRD && obj.getTelefoneResidencial().equals("") && (obj.getTelefoneCelular().equals("")) && (obj.getTelefoneTrabalho().equals(""))) {
                    throw new ConsistirException("Informe pelo menos um número de telefone !");
                } else if (!validaRD && !obj.getTelefoneCelular().isEmpty() && !obj.getTelefoneCelular().matches("\\(\\d{2}\\)(9)\\d{8}")) {
                    throw new ConsistirException("O número de celular não está correto.");
                }
            }
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setNome(getNome().toUpperCase());
        setTelefoneResidencial(getTelefoneResidencial().toUpperCase());
        setTelefoneCelular(getTelefoneCelular().toUpperCase());
        setTelefoneTrabalho(getTelefoneTrabalho().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
    }

    /**
     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (<code>Passivo</code>).
     */
    public UsuarioVO getColaboradorResponsavel() {
        if (colaboradorResponsavel == null) {
            colaboradorResponsavel = new UsuarioVO();
        }
        return (colaboradorResponsavel);
    }

    /**
     * Define o objeto da classe <code>Colaborador</code> relacionado com (<code>Passivo</code>).
     */
    public void setColaboradorResponsavel(UsuarioVO obj) {
        this.colaboradorResponsavel = obj;
    }

    /**
     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (<code>Passivo</code>).
     */
    public UsuarioVO getResponsavelCadastro() {
        if (responsavelCadastro == null) {
            responsavelCadastro = new UsuarioVO();
        }
        return (responsavelCadastro);
    }

    /**
     * Define o objeto da classe <code>Colaborador</code> relacionado com (<code>Passivo</code>).
     */
    public void setResponsavelCadastro(UsuarioVO obj) {
        this.responsavelCadastro = obj;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return (observacao);
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getDia() {
        if (dia == null) {
            dia = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return (dia);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
     */
    public String getDia_Apresentar() {
        if (dia == null) {
            return "";
        }
        return (Uteis.getDataComHora(dia));
    }

    public String getDia_ApresentarRel() {
        if (dia == null) {
            return "";
        }
        return (Uteis.getData(dia));
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getTelefoneTrabalho() {
        if (telefoneTrabalho == null) {
            telefoneTrabalho = "";
        }
        return (telefoneTrabalho);
    }

    public void setTelefoneTrabalho(String telefoneTrabalho) {
        this.telefoneTrabalho = telefoneTrabalho;
    }

    public String getTelefoneCelular() {
        if (telefoneCelular == null) {
            telefoneCelular = "";
        }
        return (telefoneCelular);
    }

    public void setTelefoneCelular(String telefoneCelular) {
        this.telefoneCelular = telefoneCelular;
    }

    public String getTelefoneResidencial() {
        if (telefoneResidencial == null) {
            telefoneResidencial = "";
        }
        return (telefoneResidencial);
    }

    public void setTelefoneResidencial(String telefoneResidencial) {
        this.telefoneResidencial = telefoneResidencial;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return (nome);
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the email
     */
    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    /**
     * @param email the email to set
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * @return the eventoVO
     */
    public EventoVO getEvento() {
        if (evento == null) {
            evento = new EventoVO();
        }
        return evento;
    }

    /**
     * @param evento the eventoVO to set
     */
    public void setEvento(EventoVO evento) {
        this.evento = evento;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    /**
     * @return the tipoAgendamentoAnterior
     */
    public String getTipoAgendamentoAnterior() {
        if (tipoAgendamentoAnterior == null) {
            tipoAgendamentoAnterior = "";
        }

        return tipoAgendamentoAnterior;
    }

    /**
     * @param tipoAgendamentoAnterior the tipoAgendamentoAnterior to set
     */
    public void setTipoAgendamentoAnterior(String tipoAgendamentoAnterior) {
        this.tipoAgendamentoAnterior = tipoAgendamentoAnterior;
    }

    /**
     * @param empresaVO the empresaVO to set
     */
    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    /**
     * @return the empresaVO
     */
    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public String getTelefones() {
        StringBuilder telefones = new StringBuilder();
        if (Uteis.validarTelefoneCelular(telefoneCelular)) {
            telefones.append(telefoneCelular);
        }

        if (Uteis.validarTelefoneCelular(telefoneResidencial)) {
            if (!telefones.toString().isEmpty()) {
                telefones.append(";").append(telefoneResidencial);
            } else {
                telefones.append(telefoneResidencial);
            }
        }
         if (Uteis.validarTelefoneCelular(telefoneTrabalho)) {
            if (!telefones.toString().isEmpty()) {
                telefones.append(";").append(telefoneTrabalho);
            } else {
                telefones.append(telefoneTrabalho);
            }
        }
        return telefones.toString();
    }

    public void setColaboradorResp(String colaboradorResp) {
        this.colaboradorResp = colaboradorResp;
    }

    public String getColaboradorResp() {
        return colaboradorResp;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getEmpresa() {
        return empresa;
    }

    public boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Integer getContrato() {
        if (contrato == null) {
            contrato = 0;
        }
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public ObjecaoVO getObjecao() {
        if (objecao == null) {
            objecao = new ObjecaoVO();
        }
        return objecao;
    }

    public void setObjecao(ObjecaoVO objecao) {
        this.objecao = objecao;
    }

    public boolean isLead() {
        return lead;
    }

    public void setLead(boolean lead) {
        this.lead = lead;
    }

    public LeadVO getLeadVO() {
        if(leadVO == null){
            return new LeadVO();
        }
        return leadVO;
    }

    public void setLeadVO(LeadVO leadVO) {
        this.leadVO = leadVO;
    }

    public OrigemSistemaEnum getOrigemSistemaEnum() {
        return origemSistemaEnum;
    }

    public void setOrigemSistemaEnum(OrigemSistemaEnum origemSistemaEnum) {
        this.origemSistemaEnum = origemSistemaEnum;
    }

    public String getDescricaoEvento() {
        return descricaoEvento;
    }

    public void setDescricaoEvento(String descricaoEvento) {
        this.descricaoEvento = descricaoEvento;
    }
    public void formatarObservacao(){
        observacao = observacao.replace("</p>", "");
        observacao = observacao.replace("<p>", "");
    }

    public Boolean getMetaExtra() {
        return metaExtra;
    }

    public void setMetaExtra(Boolean metaExtra) {
        this.metaExtra = metaExtra;
    }

    public int getBounced() {
        return bounced;
    }

    public void setBounced(int bounced) {
        this.bounced = bounced;
    }
}

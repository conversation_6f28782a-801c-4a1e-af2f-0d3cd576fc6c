/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.crm;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;

public class MalaDiretaCRMExtraClienteVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private MalaDiretaVO malaDiretaVO;
    private ClienteVO clienteVO;
    private PassivoVO passivoVO;
    @NaoControlarLogAlteracao
    private String vinculos;

    public PassivoVO getPassivoVO() {
        if (passivoVO == null) {
            passivoVO = new PassivoVO();
        }
        return passivoVO;
    }

    public void setPassivoVO(PassivoVO passivoVO) {
        this.passivoVO = passivoVO;
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }


    public MalaDiretaVO getMalaDiretaVO() {
        if (malaDiretaVO == null) {
            malaDiretaVO = new MalaDiretaVO();
        }
        return malaDiretaVO;
    }

    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public String getVinculos() {
        return vinculos;
    }

    public void setVinculos(String vinculos) {
        this.vinculos = vinculos;
    }
}

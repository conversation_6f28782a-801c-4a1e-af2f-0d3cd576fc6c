package negocio.comuns.crm;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ColaboradorVO;

import java.io.OutputStream;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Pessoa;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 11/09/13
 * Time: 13:39
 */
public class InfoCarteiraTO extends SuperTO {

    private static final long serialVersionUID = 4405725544366649776L;
    private ColaboradorVO colaborador = new ColaboradorVO();
    private List<VinculosTipoTO> qtdVinculosColaborador = new ArrayList<VinculosTipoTO>();
    private String aberto = "false";

    public Integer getTotalVinculos() {
        Integer total = 0;
        for (VinculosTipoTO vinculosTipoTO : getQtdVinculosColaborador()) {
            total += vinculosTipoTO.getQtdVinculos();
        }
        return total;
    }

    public String getTotalVinculos_Apresentar() {
        Integer totalVinculos = getTotalVinculos();
        if (totalVinculos != 1) {
            return totalVinculos + " clientes";
        }
        return totalVinculos + " cliente";
    }

    public Integer getQtdCarteiras() {
        return getQtdVinculosColaborador().size();
    }

    public String getQtdCarteiras_Apresentar() {
        if (getQtdCarteiras() == 1) {
            return getQtdCarteiras() + " carteira";
        }
        return getQtdCarteiras() + " carteiras";
    }

    public String getNomeColaborador() {
        if (getColaborador().getPessoa().getNome().length() > 0) {
            return getColaborador().getPessoa().getNome();
        }
        return "";
    }

    public void paintFoto(OutputStream out, Object data) throws Exception {
        try {
            String key = JSFUtilities.getFromSession("key").toString();
            if (!UteisValidacao.emptyString(key)) {
                Connection con = new DAO().obterConexaoEspecifica(key);
                Pessoa pessoaDAO = new Pessoa(con);
                getColaborador().getPessoa().setFoto(
                        pessoaDAO.obterFoto(key,
                                getColaborador().getPessoa().getCodigo()));
                SuperControle.paintFoto(out, getColaborador().getPessoa().getFoto());
            }
        }catch (Exception e){
            e.getStackTrace();
        }
    }

    public String getPaintFotoDaNuvem() {
        return Uteis.getPaintFotoDaNuvem(getColaborador().getPessoa().getFotoKey());
    }

    public String getSituacaoColaborador() {
        return getColaborador().getSituacao_Apresentar();
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    public List<VinculosTipoTO> getQtdVinculosColaborador() {
        return qtdVinculosColaborador;
    }

    public void setQtdVinculosColaborador(List<VinculosTipoTO> qtdVinculosColaborador) {
        this.qtdVinculosColaborador = qtdVinculosColaborador;
    }

    public String getAberto() {
        return aberto;
    }

    public void setAberto(String aberto) {
        this.aberto = aberto;
    }
}

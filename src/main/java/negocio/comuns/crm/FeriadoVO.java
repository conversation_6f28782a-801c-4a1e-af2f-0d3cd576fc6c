package negocio.comuns.crm;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.basico.CidadeVO;
import java.util.List;
import java.util.Date;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;

/**
 * Reponsável por manter os dados da entidade Feriado. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class FeriadoVO extends SuperVO {

    protected Integer codigo;
    protected String descricao;
    protected Date dia;
    protected String mes;
    protected Boolean nacional;
    protected Boolean estadual;
    @ChaveEstrangeira
    @FKJson
    private EstadoVO estado;
    @NaoControlarLogAlteracao
    protected Boolean naoRecorrente;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Cidade </code>.*/
    @FKJson
    @ChaveEstrangeira
    protected CidadeVO cidade;
    @FKJson
    @ChaveEstrangeira
    private PaisVO pais;

    private String feriadoRecorrenteItem = "null";

    /**
     * Construtor padrão da classe <code>Feriado</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public FeriadoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da classe <code>FeriadoVO</code>.
     */
    public static void validarUnicidade(List<FeriadoVO> lista, FeriadoVO obj) throws ConsistirException {
        for (FeriadoVO repetido : lista) {
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>FeriadoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(FeriadoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if(obj.getNaoRecorrente() == null){
            throw new ConsistirException("O campo REPETIR FERIADO NOS PRÓXIMOS ANOS não está informado!");
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO não está informado!");
        }
        if (obj.getDia() == null) {
            throw new ConsistirException("O campo DIA não está informado!");
        }
        if (obj.getNacional()) {
            if (obj.getPais() == null || obj.getPais().getCodigo().intValue() == 0) {
                throw new ConsistirException("O campo PAÍS não está informado!");
            }
            obj.setEstado(new EstadoVO());
            obj.setCidade(new CidadeVO());
        }
        if (obj.getEstadual()) {
            if (obj.getPais() == null || obj.getPais().getCodigo().intValue() == 0) {
                throw new ConsistirException("O campo PAÍS não está informado!");
            }
            if (obj.getEstado() == null || obj.getEstado().getCodigo().intValue() == 0 ) {
                throw new ConsistirException("O campo ESTADO não está informado!");
            }
            obj.setCidade(new CidadeVO());
        }
        if(!obj.getNacional() && !obj.getEstadual()){
            if (obj.getPais() == null || obj.getPais().getCodigo().intValue() == 0) {
                throw new ConsistirException("O campo PAÍS não está informado!");
            }
            if (obj.getEstado() == null || obj.getEstado().getCodigo().intValue() == 0 ) {
                throw new ConsistirException("O campo ESTADO não está informado!");
            }
            if(obj.getCidade() == null || obj.getCidade().getCodigo().intValue() == 0){
            	throw new ConsistirException("O campo CIDADE não está informado!");
            }
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setMes(getMes().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(null);
        setDescricao("");
        setDia(negocio.comuns.utilitarias.Calendario.hoje());
        setMes("");
        setNacional(new Boolean(false));
        setEstadual(new Boolean(false));
        setEstado(new EstadoVO());
        setCidade(new CidadeVO());
        setPais(new PaisVO());
    }

    /**
     * Retorna o objeto da classe <code>Cidade</code> relacionado com (<code>Feriado</code>).
     */
    public CidadeVO getCidade() {
        if (cidade == null) {
            cidade = new CidadeVO();
        }
        return (cidade);
    }

    /**
     * Define o objeto da classe <code>Cidade</code> relacionado com (<code>Feriado</code>).
     */
    public void setCidade(CidadeVO obj) {
        this.cidade = obj;
    }

    public Boolean getNaoRecorrente() {
        return (naoRecorrente);
    }

    public Boolean isNaoRecorrente() {
        if (naoRecorrente == null) {
            naoRecorrente = new Boolean(false);
        }
        return (naoRecorrente);
    }

    public void setNaoRecorrente(Boolean naoRecorrente) {
        this.naoRecorrente = naoRecorrente;
    }

    public Boolean getEstadual() {
        return (estadual);
    }

    public Boolean isEstadual() {
        if (estadual == null) {
            estadual = new Boolean(false);
        }
        return (estadual);
    }

    public void setEstadual(Boolean estadual) {
        this.estadual = estadual;
    }

    public Boolean getNacional() {
        return (nacional);
    }

    public Boolean isNacional() {
        if (nacional == null) {
            nacional = new Boolean(false);
        }
        return (nacional);
    }

    public void setNacional(Boolean nacional) {
        this.nacional = nacional;
    }

    public String getMes() {
        if (mes == null) {
            mes = "";
        }
        return (mes);
    }

    public void setMes(String mes) {
        this.mes = mes;
    }

    public Date getDia() {
        if (dia == null) {
            dia = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return (dia);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
     */
    public String getDia_Apresentar() {
        return (Uteis.getData(dia));
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the estado
     */
    public EstadoVO getEstado() {
        return estado;
    }

    /**
     * @param estado the estado to set
     */
    public void setEstado(EstadoVO estado) {
        this.estado = estado;
    }

    /**
     * @return the pais
     */
    public PaisVO getPais() {
        return pais;
    }

    /**
     * @param pais the pais to set
     */
    public void setPais(PaisVO pais) {
        this.pais = pais;
    }

    public String getFeriadoRecorrenteItem() {
        return feriadoRecorrenteItem;
    }

    public void setFeriadoRecorrenteItem(String feriadoRecorrenteItem) {
        this.feriadoRecorrenteItem = feriadoRecorrenteItem;
    }
}

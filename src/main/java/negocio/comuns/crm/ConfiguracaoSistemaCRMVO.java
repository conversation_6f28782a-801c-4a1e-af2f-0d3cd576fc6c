package negocio.comuns.crm;

import annotations.arquitetura.NaoControlarLogAlteracao;
import annotations.arquitetura.PassWord;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.integracao.sendy.to.CampaignTO;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade ConfiguracaoSistemaCRM. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */

public class ConfiguracaoSistemaCRMVO extends SuperVO {

    protected Integer codigo;
    protected String remetentePadrao;
    protected Boolean usarRemetentePadraoGeral = false;
    protected String emailPadrao;
    protected String mailServer;
    protected Boolean usaSMTPS=Boolean.FALSE;
    protected Boolean usaConfiguracaoEmailManual=Boolean.FALSE;
    protected String login;
    @PassWord
    protected String senha;
    protected Boolean abertoSabado;
    protected Boolean abertoDomingo;
    protected Integer nrFaltaPlanoMensal;
    protected Integer nrFaltaPlanoTrimestral;
    protected Integer nrFaltaPlanoAcimaSemestral;
    protected Integer nrDiasParaClientePreveRenovacao;
    private Integer nrDiasParaClientePreveRenovacaoMaiorUmMes;
    protected Integer nrDiasParaClientePrevePerda;
    private Integer nrDiasAnterioresAgendamento;
    private Integer nrCreditosTreinoRenovar;
    private Integer nrDiasPosterioresAgendamento;
    private Integer nrDiasPosAgendamentoConversaoExAluno = 0;
    private Integer nrRisco;
    private Integer indicacoesMes = 0;
    private Integer conversaoAgendadosMes = 0;
    private boolean conexaoSegura;
    private boolean iniciarTLS;
    private boolean bloquearTermoSpam = false;
    private Integer nrDiasLimiteAgendamentoFuturo;
    private Boolean integracaoPacto = false;
    private Integer limiteDiarioEmails;
    private Integer limiteMensalPacto;
    private Boolean dividirFase;
    @NaoControlarLogAlteracao
    private List<ConfiguracaoDiasPosVendaVO> configuracaoDiasPosVendaVOs;
    @NaoControlarLogAlteracao
    private List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasExAlunos = new ArrayList<ConfiguracaoDiasMetasTO>();
    @NaoControlarLogAlteracao
    private List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasUltimoAcessoGymp= new ArrayList<ConfiguracaoDiasMetasTO>();
    @NaoControlarLogAlteracao
    private List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasSessoesFinais = new ArrayList<ConfiguracaoDiasMetasTO>();

    @NaoControlarLogAlteracao
    private List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasSemAgendamento = new ArrayList<ConfiguracaoDiasMetasTO>();
    @NaoControlarLogAlteracao
    private List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasVisitantesAntigos = new ArrayList<ConfiguracaoDiasMetasTO>();
    private List<String> termosSpam = new ArrayList<String>();
    private String urlJenkins;
    private String urlMailing;
    private Integer qtdConversoesExAlunosMes = 0;
    private boolean incluirContratosRenovados = false;
    private boolean considerarProfessorTreinoWeb = false;
    private boolean baterMetaTodasAcoes = false;
    private int nrDiasContarResultado = 20;
    private String portaServer;
    private boolean obrigatorioSeguirOrdemMetas;
    private String ordenacaoMetas;
    private boolean agendamentoParaMetaConsultor = true;
    private boolean autorrenovavelEntraRenovacao = false;

    private UsuarioVO remetentePadraoMailing = new UsuarioVO();
    private boolean enviarEmailIndividualmente = false;

    private boolean apresentarColaboradoresPorTipoColaborador = false;
    private boolean apresentarColaboradoresInativos = false;
    private String mailingFtpServer = "";
    private String mailingFtpUser = "";
    private String mailingFtpPass = "";
    private Integer mailingFtpPort = 0;
    /**
     * ftp | sftp
     */
    private String mailingFtpType = "";
    private String mailingFtpFolder = "";

    private String tokenBitly;
    private Boolean gerarIndicacaoParaCadastroConvidadosVendasOnline;
    private boolean direcionaragendamentosexperimentaisagenda;

    /**
     * Construtor padrão da classe <code>ConfiguracaoSistemaCRM</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ConfiguracaoSistemaCRMVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ConfiguracaoSistemaCRMVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(ConfiguracaoSistemaCRMVO obj) throws ConsistirException {
        if (obj.getNrDiasAnterioresAgendamento() < 1 || obj.getNrDiasAnterioresAgendamento() > 30) {
            throw new ConsistirException("Número de dias anteriores a um agendamento para considerar contrato Agendado deve ser maior do que 1 e menor do que 30.");
        }
        if (obj.getNrDiasPosterioresAgendamento() < 1 || obj.getNrDiasPosterioresAgendamento() > 30) {
            throw new ConsistirException("Número de dias após um agendamento vencido para ainda considerar contrato Agendado deve ser maior do que 1 e menor do que 30.");
        }
        if (obj.getNrDiasLimiteAgendamentoFuturo() < 1 || obj.getNrDiasLimiteAgendamentoFuturo() > 30) {
            throw new ConsistirException("Número de dias limite para agendamento futuro deve ser maior do que 1 e menor do que 30.");
        }
        if (obj.getNrRisco() > 8 || obj.getNrRisco() < 1) {
            throw new ConsistirException("O Peso para aparecer na meta de grupo de risco deve ser de 1 a 8.");
        }
        if(obj.getNrFaltaPlanoMensal() < 1 ){
            throw new ConsistirException("O Número de faltas permitidos para Plano Mensal deve ser maior que zero");
        }
        if(obj.getNrFaltaPlanoTrimestral() < 1 ){
            throw new ConsistirException("O Número de faltas permitidos para Plano Trimestral deve ser maior que zero");
        }
        if(obj.getNrFaltaPlanoAcimaSemestral() < 1 ){
            throw new ConsistirException("O Número de faltas permitidos para Plano Semestral deve ser maior que zero");
        }
        
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(null);
        setRemetentePadrao("");
        setEmailPadrao("");
        setMailServer("");
        setLogin("");
        setSenha("");
        setAbertoSabado(false);
        setAbertoDomingo(false);
        setNrFaltaPlanoMensal(1);
        setNrFaltaPlanoTrimestral(1);
        setNrFaltaPlanoAcimaSemestral(1);
        setNrRisco(0);
        setConfiguracaoDiasPosVendaVOs(new ArrayList<ConfiguracaoDiasPosVendaVO>());
        setConexaoSegura(false);
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>ConfiguracaoDiasPosVendaVO</code>
     * ao List <code>configuracaoDiasPosVendaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ConfiguracaoDiasPosVenda</code> - getNrDia() - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ConfiguracaoDiasPosVendaVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjConfiguracaoDiasPosVendaVOs(ConfiguracaoDiasPosVendaVO obj) throws Exception {
        ConfiguracaoDiasPosVendaVO.validarDados(obj);
        obj.setConfiguracaoSistemaCRM(this);
        int index = 0;
        Iterator i = getConfiguracaoDiasPosVendaVOs().iterator();
        while (i.hasNext()) {
            ConfiguracaoDiasPosVendaVO objExistente = (ConfiguracaoDiasPosVendaVO) i.next();
            if (objExistente.getNrDia().equals(obj.getNrDia())) {
                if(!objExistente.getCodigo().equals(obj.getCodigo())){
                    throw new ConsistirException("Já existe na lista uma configuração com esse número de dias. Só é possível editar essa configuração");
                }
                getConfiguracaoDiasPosVendaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getConfiguracaoDiasPosVendaVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>ConfiguracaoDiasPosVendaVO</code>
     * no List <code>configuracaoDiasPosVendaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ConfiguracaoDiasPosVenda</code> - getNrDia() - como identificador (key) do objeto no List.
     *
     * @param nrDia Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjConfiguracaoDiasPosVendaVOs(Integer nrDia) throws Exception {
        int index = 0;
        Iterator i = getConfiguracaoDiasPosVendaVOs().iterator();
        while (i.hasNext()) {
            ConfiguracaoDiasPosVendaVO objExistente = (ConfiguracaoDiasPosVendaVO) i.next();
            if (objExistente.getNrDia().equals(nrDia)) {
                getConfiguracaoDiasPosVendaVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>ConfiguracaoDiasPosVendaVO</code>
     * no List <code>configuracaoDiasPosVendaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ConfiguracaoDiasPosVenda</code> - getNrDia() - como identificador (key) do objeto no List.
     *
     * @param nrDia Parâmetro para localizar o objeto do List.
     */
    public ConfiguracaoDiasPosVendaVO consultarObjConfiguracaoDiasPosVendaVO(Integer nrDia) throws Exception {
        Iterator i = getConfiguracaoDiasPosVendaVOs().iterator();
        while (i.hasNext()) {
            ConfiguracaoDiasPosVendaVO objExistente = (ConfiguracaoDiasPosVendaVO) i.next();
            if (objExistente.getNrDia().equals(nrDia)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe <code>ConfiguracaoDiasPosVenda</code>.
     */
    public List<ConfiguracaoDiasPosVendaVO> getConfiguracaoDiasPosVendaVOs() {
        if (configuracaoDiasPosVendaVOs == null) {
            configuracaoDiasPosVendaVOs = new ArrayList<ConfiguracaoDiasPosVendaVO>();
        }
        return (configuracaoDiasPosVendaVOs);
    }

    /**
     * Define Atributo responsável por manter os objetos da classe <code>ConfiguracaoDiasPosVenda</code>.
     */
    public void setConfiguracaoDiasPosVendaVOs(List<ConfiguracaoDiasPosVendaVO> configuracaoDiasPosVendaVOs) {
        this.configuracaoDiasPosVendaVOs = configuracaoDiasPosVendaVOs;
    }

    public Integer getNrRisco() {
        if (nrRisco == null) {
            nrRisco = 0;
        }
        return (nrRisco);
    }

    public void setNrRisco(Integer nrRisco) {
        this.nrRisco = nrRisco;
    }

    public Integer getNrFaltaPlanoAcimaSemestral() {
        if (nrFaltaPlanoAcimaSemestral == null) {
            nrFaltaPlanoAcimaSemestral = 0;
        }
        return (nrFaltaPlanoAcimaSemestral);
    }

    public void setNrFaltaPlanoAcimaSemestral(Integer nrFaltaPlanoAcimaSemestral) {
        this.nrFaltaPlanoAcimaSemestral = nrFaltaPlanoAcimaSemestral;
    }

    public Integer getNrFaltaPlanoTrimestral() {
        if (nrFaltaPlanoTrimestral == null) {
            nrFaltaPlanoTrimestral = 0;
        }
        return (nrFaltaPlanoTrimestral);
    }

    public void setNrFaltaPlanoTrimestral(Integer nrFaltaPlanoTrimestral) {
        this.nrFaltaPlanoTrimestral = nrFaltaPlanoTrimestral;
    }

    public Integer getNrFaltaPlanoMensal() {
        if (nrFaltaPlanoMensal == null) {
            nrFaltaPlanoMensal = 0;
        }
        return (nrFaltaPlanoMensal);
    }

    public void setNrFaltaPlanoMensal(Integer nrFaltaPlanoMensal) {
        this.nrFaltaPlanoMensal = nrFaltaPlanoMensal;
    }

    public Boolean getAbertoDomingo() {
        return (abertoDomingo);
    }

    public void setAbertoDomingo(Boolean abertoDomingo) {
        this.abertoDomingo = abertoDomingo;
    }

    public Boolean isAbertoDomingo() {
        if (abertoDomingo == null) {
            abertoDomingo = false;
        }
        return (abertoDomingo);
    }

    public Boolean getAbertoSabado() {
        return (abertoSabado);
    }

    public void setAbertoSabado(Boolean abertoSabado) {
        this.abertoSabado = abertoSabado;
    }

    public Boolean isAbertoSabado() {
        if (abertoSabado == null) {
            abertoSabado = false;
        }
        return (abertoSabado);
    }

    public String getSenha() {
        if (senha == null) {
            senha = "";
        }
        return (senha);
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getLogin() {
        if (login == null) {
            login = "";
        }
        return (login);
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public String getMailServer() {
        if (mailServer == null) {
            mailServer = "";
        }
        return (mailServer);
    }

    public void setMailServer(String mailServer) {
        this.mailServer = mailServer;
    }

    public String getEmailPadrao() {
        if (emailPadrao == null) {
            emailPadrao = "";
        }
        return (emailPadrao);
    }

    public void setEmailPadrao(String emailPadrao) {
        this.emailPadrao = emailPadrao;
    }

    public String getRemetentePadrao() {
        if (remetentePadrao == null) {
            remetentePadrao = "";
        }
        return (remetentePadrao);
    }

    public void setRemetentePadrao(String remetentePadrao) {
        this.remetentePadrao = remetentePadrao;
    }

    public Boolean getUsarRemetentePadraoGeral() {
        if (usarRemetentePadraoGeral == null) {
            usarRemetentePadraoGeral = Boolean.FALSE;
        }
        return usarRemetentePadraoGeral;
    }

    public void setUsarRemetentePadraoGeral(Boolean usarRemetentePadraoGeral) {
        this.usarRemetentePadraoGeral = usarRemetentePadraoGeral;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getNrDiasParaClientePreveRenovacao() {
        if (nrDiasParaClientePreveRenovacao == null) {
            nrDiasParaClientePreveRenovacao = 0;
        }
        return nrDiasParaClientePreveRenovacao;
    }

    public void setNrDiasParaClientePreveRenovacao(Integer nrDiasParaClientePreveRenovacao) {
        this.nrDiasParaClientePreveRenovacao = nrDiasParaClientePreveRenovacao;
    }

    public Integer getNrDiasParaClientePrevePerda() {
        if (nrDiasParaClientePrevePerda == null) {
            nrDiasParaClientePrevePerda = 0;
        }
        return nrDiasParaClientePrevePerda;
    }

    public void setNrDiasParaClientePrevePerda(Integer nrDiasParaClientePrevePerda) {
        this.nrDiasParaClientePrevePerda = nrDiasParaClientePrevePerda;
    }

    public boolean isConexaoSegura() {
        return conexaoSegura;
    }

    public void setConexaoSegura(boolean conexaoSegura) {
        this.conexaoSegura = conexaoSegura;
    }

    public Boolean getDividirFase() {
        if (dividirFase == null) {
            dividirFase = Boolean.FALSE;
        }
        return dividirFase;
    }

    public void setDividirFase(Boolean dividirFase) {
        this.dividirFase = dividirFase;
    }

    public Integer getNrDiasPosterioresAgendamento() {
        return nrDiasPosterioresAgendamento;
    }

    public void setNrDiasPosterioresAgendamento(
            Integer nrDiasPosterioresAgendamento) {
        this.nrDiasPosterioresAgendamento = nrDiasPosterioresAgendamento;
    }

    public Integer getNrDiasAnterioresAgendamento() {
        return nrDiasAnterioresAgendamento;
    }

    public void setNrDiasAnterioresAgendamento(
            Integer nrDiasAnterioresAgendamento) {
        this.nrDiasAnterioresAgendamento = nrDiasAnterioresAgendamento;
    }

    public Integer getNrDiasLimiteAgendamentoFuturo() {
        return nrDiasLimiteAgendamentoFuturo;
    }

    public void setNrDiasLimiteAgendamentoFuturo(
            Integer nrDiasLimiteAgendamentoFuturo) {
        this.nrDiasLimiteAgendamentoFuturo = nrDiasLimiteAgendamentoFuturo;
    }

    public String getNrDiasLimiteAgendamentoFuturoDescricao() {
        if (nrDiasLimiteAgendamentoFuturo == 1) {
            return "1 dia";
        } else
            return nrDiasLimiteAgendamentoFuturo + " dias";
    }

    public List<String> getTermosSpam() {
        return termosSpam;
    }

    public void setTermosSpam(List<String> termosSpam) {
        this.termosSpam = termosSpam;
    }

    public boolean getBloquearTermoSpam() {
        return bloquearTermoSpam;
    }

    public void setBloquearTermoSpam(boolean bloquearTermoSpam) {
        this.bloquearTermoSpam = bloquearTermoSpam;
    }

    public String getUrlJenkins() {
        return urlJenkins;
    }

    public void setUrlJenkins(String urlJenkins) {
        this.urlJenkins = urlJenkins;
    }

    public String getUrlMailing() {
        return urlMailing;
    }

    public void setUrlMailing(String urlMailing) {
        this.urlMailing = urlMailing;
    }

    public List<ConfiguracaoDiasMetasTO> getConfiguracaoDiasMetasSemAgendamento() {
        return configuracaoDiasMetasSemAgendamento;
    }

    public void setConfiguracaoDiasMetasSemAgendamento(
            List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasSemAgendamento) {
        this.configuracaoDiasMetasSemAgendamento = configuracaoDiasMetasSemAgendamento;
    }

    public List<ConfiguracaoDiasMetasTO> getConfiguracaoDiasMetasSessoesFinais() {
        return configuracaoDiasMetasSessoesFinais;
    }

    public void setConfiguracaoDiasMetasSessoesFinais(
            List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasSessoesFinais) {
        this.configuracaoDiasMetasSessoesFinais = configuracaoDiasMetasSessoesFinais;
    }

    public boolean isIniciarTLS() {
        return iniciarTLS;
    }

    public void setIniciarTLS(boolean iniciarTLS) {
        this.iniciarTLS = iniciarTLS;
    }

    public Integer getIndicacoesMes() {
        return indicacoesMes;
    }

    public void setIndicacoesMes(Integer indicacoesMes) {
        this.indicacoesMes = indicacoesMes;
    }

    public Integer getConversaoAgendadosMes() {
        return conversaoAgendadosMes;
    }

    public void setConversaoAgendadosMes(Integer conversaoAgendadosMes) {
        this.conversaoAgendadosMes = conversaoAgendadosMes;
    }

    public List<ConfiguracaoDiasMetasTO> getConfiguracaoDiasMetasExAlunos() {
        return configuracaoDiasMetasExAlunos;
    }

    public void setConfiguracaoDiasMetasExAlunos(List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasExAlunos) {
        this.configuracaoDiasMetasExAlunos = configuracaoDiasMetasExAlunos;
    }

    public Integer getNrDiasPosAgendamentoConversaoExAluno() {
        return nrDiasPosAgendamentoConversaoExAluno;
    }

    public void setNrDiasPosAgendamentoConversaoExAluno(Integer nrDiasPosAgendamentoConversaoExAluno) {
        this.nrDiasPosAgendamentoConversaoExAluno = nrDiasPosAgendamentoConversaoExAluno;
    }

    public Integer getQtdConversoesExAlunosMes() {
        return qtdConversoesExAlunosMes;
    }

    public void setQtdConversoesExAlunosMes(Integer qtdConversoesExAlunosMes) {
        this.qtdConversoesExAlunosMes = qtdConversoesExAlunosMes;
    }

    public boolean isIncluirContratosRenovados() {
        return incluirContratosRenovados;
    }

    public void setIncluirContratosRenovados(boolean incluirContratosRenovados) {
        this.incluirContratosRenovados = incluirContratosRenovados;
    }

    public List<ConfiguracaoDiasMetasTO> getConfiguracaoDiasMetasVisitantesAntigos() {
        return configuracaoDiasMetasVisitantesAntigos;
    }

    public void setConfiguracaoDiasMetasVisitantesAntigos(List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasVisitantesAntigos) {
        this.configuracaoDiasMetasVisitantesAntigos = configuracaoDiasMetasVisitantesAntigos;
    }

    public boolean isConsiderarProfessorTreinoWeb() {
        return considerarProfessorTreinoWeb;
    }

    public void setConsiderarProfessorTreinoWeb(boolean considerarProfessorTreinoWeb) {
        this.considerarProfessorTreinoWeb = considerarProfessorTreinoWeb;
    }


    public boolean isBaterMetaTodasAcoes() {
        return baterMetaTodasAcoes;
    }

    public void setBaterMetaTodasAcoes(boolean baterMetaTodasAcoes) {
        this.baterMetaTodasAcoes = baterMetaTodasAcoes;
    }

    public int getNrDiasContarResultado() {
        return nrDiasContarResultado;
    }

    public void setNrDiasContarResultado(int nrDiasContarResultado) {
        this.nrDiasContarResultado = nrDiasContarResultado;
    }
    
    public boolean validarInformacoesBasicasEmail(){
        return (!UteisValidacao.emptyString(this.getLogin()) &&
        !UteisValidacao.emptyString(this.getSenha()) &&
        !UteisValidacao.emptyString(this.getMailServer()));
    }

    public String getPortaServer() {
        if (portaServer == null){
            portaServer = "";
        }
        return portaServer;
    }

    public void setPortaServer(String portaServer) {
        this.portaServer = portaServer;
    }


    public boolean isObrigatorioSeguirOrdemMetas() {
        return obrigatorioSeguirOrdemMetas;
    }

    public void setObrigatorioSeguirOrdemMetas(boolean obrigatorioSeguirOrdemMetas) {
        this.obrigatorioSeguirOrdemMetas = obrigatorioSeguirOrdemMetas;
    }

    public String getOrdenacaoMetas() {
        if (ordenacaoMetas == null) {
            ordenacaoMetas = "";
        }
        return ordenacaoMetas;
    }

    public void setOrdenacaoMetas(String ordenacaoMetas) {
        this.ordenacaoMetas = ordenacaoMetas;
    }

    public void adicionarObjConfiguracaoDiasExAlunos(ConfiguracaoDiasMetasTO obj) throws Exception {
        if (obj.getNrDia() == null || UteisValidacao.emptyString(obj.getDescricao())) {
            throw new ConsistirException("Todos os campos são obrigatórios!");
        }
        if (obj.getNrDia().equals(0)) {
            throw new ConsistirException("Valor 0 não é permitido, use valor 1.");
        }
        validarDuplicidadeConfiguracaoMetas(obj, getConfiguracaoDiasMetasExAlunos(), "Já existe na lista uma configuração com esse número de dias");
        getConfiguracaoDiasMetasExAlunos().add(obj);
    }

    public void adicionarObjConfiguracaoDiasUltimosAcessosGymp(ConfiguracaoDiasMetasTO obj) throws Exception {
        if (obj.getNrDia() == null || UteisValidacao.emptyString(obj.getDescricao())) {
            throw new ConsistirException("Todos os campos são obrigatórios!");
        }
        if (obj.getNrDia().equals(0)) {
            throw new ConsistirException("Valor 0 não é permitido, use valor 1.");
        }
        validarDuplicidadeConfiguracaoMetas(obj, getConfiguracaoDiasMetasUltimoAcessoGymp(), "Já existe na lista uma configuração com esse número de dias");
        getConfiguracaoDiasMetasUltimoAcessoGymp().add(obj);
    }

    public void adicionarObjConfiguracaoDiasMetasSemAgendamento(ConfiguracaoDiasMetasTO obj) throws Exception {
        if (obj.getNrDia() == null || UteisValidacao.emptyString(obj.getDescricao())) {
            throw new ConsistirException("Todos os campos são obrigatórios!");
        }
        if (obj.getNrDia().equals(0)) {
            throw new ConsistirException("Valor 0 não é permitido, use valor 1.");
        }
        if (obj.getProduto() == null || obj.getProduto().getCodigo() == 0) {
            throw new ConsistirException("Escolha um produto.");
        }
        validarDuplicidadeConfiguracaoMetasEstudio(obj, getConfiguracaoDiasMetasSemAgendamento(), "Já existe na lista uma configuração com esse produto");
        getConfiguracaoDiasMetasSemAgendamento().add(obj);
    }

    public void adicionarObjConfiguracaoDiasMetasSessoesFinais(ConfiguracaoDiasMetasTO obj) throws Exception {
        if (obj.getNrDia() == null || UteisValidacao.emptyString(obj.getDescricao())) {
            throw new ConsistirException("Todos os campos são obrigatórios!");
        }
        if (obj.getNrDia().equals(0)) {
            throw new ConsistirException("Valor 0 não é permitido, use valor 1.");
        }
        validarDuplicidadeConfiguracaoMetas(obj, getConfiguracaoDiasMetasSessoesFinais(), "Já existe na lista uma configuração com esse número de sessões");
        getConfiguracaoDiasMetasSessoesFinais().add(obj);
    }

    public void adicionarObjConfiguracaoDiasMetasVisitantesAntigos(ConfiguracaoDiasMetasTO obj) throws Exception {
        if (obj.getNrDia() == null || UteisValidacao.emptyString(obj.getDescricao())) {
            throw new ConsistirException("Todos os campos são obrigatórios!");
        }
        if (obj.getNrDia().equals(0)) {
            throw new ConsistirException("Valor 0 não é permitido, use valor 1.");
        }
        validarDuplicidadeConfiguracaoMetas(obj, getConfiguracaoDiasMetasVisitantesAntigos(), "Já existe na lista uma configuração com esse número de dias");
        getConfiguracaoDiasMetasVisitantesAntigos().add(obj);
    }

    private void validarDuplicidadeConfiguracaoMetas(ConfiguracaoDiasMetasTO obj, List<ConfiguracaoDiasMetasTO> lista, String mensagemErro) throws ConsistirException {
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            ConfiguracaoDiasMetasTO objExistente = (ConfiguracaoDiasMetasTO) i.next();
            if (objExistente.getNrDia().equals(obj.getNrDia())) {
                throw new ConsistirException(mensagemErro);
            }
        }
    }

    private void validarDuplicidadeConfiguracaoMetasEstudio(ConfiguracaoDiasMetasTO obj, List<ConfiguracaoDiasMetasTO> lista, String mensagemErro) throws ConsistirException {
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            ConfiguracaoDiasMetasTO objExistente = (ConfiguracaoDiasMetasTO) i.next();
            if ( objExistente.getProduto()!= null && objExistente.getProduto().equals(obj.getProduto()) && objExistente.getNrDia().equals(obj.getNrDia())) {
                throw new ConsistirException(mensagemErro);
            }
        }
    }

    public CampaignTO preparaEnvioSendy() {
        CampaignTO campaignTO = new CampaignTO();
        campaignTO.setFromEmail(getEmailPadrao());
        campaignTO.setReplyTo(getEmailPadrao());
        try {
            campaignTO.setAppKey(JSFUtilities.getFromSession("key").toString());
        } catch (Exception e) {
            campaignTO.setAppKey("");
        }
        campaignTO.getListTO().setAppKey(campaignTO.getAppKey());
        return campaignTO;
    }

    public UsuarioVO getRemetentePadraoMailing() {
        return remetentePadraoMailing;
    }

    public void setRemetentePadraoMailing(UsuarioVO remetentePadraoMailing) {
        this.remetentePadraoMailing = remetentePadraoMailing;
    }

    public boolean isEnviarEmailIndividualmente() {
        return enviarEmailIndividualmente;
    }

    public void setEnviarEmailIndividualmente(boolean enviarEmailIndividualmente) {
        this.enviarEmailIndividualmente = enviarEmailIndividualmente;
    }

    public Integer getNrCreditosTreinoRenovar() {
        return nrCreditosTreinoRenovar;
    }

    public void setNrCreditosTreinoRenovar(Integer nrCreditosTreinoRenovar) {
        this.nrCreditosTreinoRenovar = nrCreditosTreinoRenovar;
    }

    public boolean isAgendamentoParaMetaConsultor() {
        return agendamentoParaMetaConsultor;
    }

    public void setAgendamentoParaMetaConsultor(boolean agendamentoParaMetaConsultor) {
        this.agendamentoParaMetaConsultor = agendamentoParaMetaConsultor;
    }

    public boolean isApresentarColaboradoresPorTipoColaborador() {
        return apresentarColaboradoresPorTipoColaborador;
    }

    public void setApresentarColaboradoresPorTipoColaborador(boolean apresentarColaboradoresPorTipoColaborador) {
        this.apresentarColaboradoresPorTipoColaborador = apresentarColaboradoresPorTipoColaborador;
    }

    public boolean isApresentarColaboradoresInativos() {
        return apresentarColaboradoresInativos;
    }

    public void setApresentarColaboradoresInativos(boolean apresentarColaboradoresInativos) {
        this.apresentarColaboradoresInativos = apresentarColaboradoresInativos;
    }

    public Integer getNrDiasParaClientePreveRenovacaoMaiorUmMes() {
        if (nrDiasParaClientePreveRenovacaoMaiorUmMes == null) {
            nrDiasParaClientePreveRenovacaoMaiorUmMes = 0;
        }
        return nrDiasParaClientePreveRenovacaoMaiorUmMes;
    }

    public void setNrDiasParaClientePreveRenovacaoMaiorUmMes(Integer nrDiasParaClientePreveRenovacaoMaiorUmMes) {
        this.nrDiasParaClientePreveRenovacaoMaiorUmMes = nrDiasParaClientePreveRenovacaoMaiorUmMes;
    }

    public String getMailingFtpServer() {
        return mailingFtpServer;
    }

    public void setMailingFtpServer(String mailingFtpServer) {
        this.mailingFtpServer = mailingFtpServer;
    }

    public String getMailingFtpUser() {
        return mailingFtpUser;
    }

    public void setMailingFtpUser(String mailingFtpUser) {
        this.mailingFtpUser = mailingFtpUser;
    }

    public String getMailingFtpPass() {
        return mailingFtpPass;
    }

    public void setMailingFtpPass(String mailingFtpPass) {
        this.mailingFtpPass = mailingFtpPass;
    }

    public Integer getMailingFtpPort() {
        return mailingFtpPort;
    }

    public void setMailingFtpPort(Integer mailingFtpPort) {
        this.mailingFtpPort = mailingFtpPort;
    }

    public String getMailingFtpType() {
        return mailingFtpType;
    }

    public void setMailingFtpType(String mailingFtpType) {
        this.mailingFtpType = mailingFtpType;
    }

    public String getMailingFtpFolder() {
        return mailingFtpFolder;
    }

    public void setMailingFtpFolder(String mailingFtpFolder) {
        this.mailingFtpFolder = mailingFtpFolder;
    }

    public Boolean getUsaSMTPS() {
        return usaSMTPS;
    }

    public void setUsaSMTPS(Boolean usaSMTPS) {
        this.usaSMTPS = usaSMTPS;
    }

    public Boolean getUsaConfiguracaoEmailManual() {
        return usaConfiguracaoEmailManual;
    }

    public void setUsaConfiguracaoEmailManual(Boolean usaConfiguracaoEmailManual) {
        this.usaConfiguracaoEmailManual = usaConfiguracaoEmailManual;
    }

    public String getTokenBitly() {
        if (tokenBitly == null) {
            tokenBitly = "";
        }
        return tokenBitly;
    }

    public void setTokenBitly(String tokenBitly) {
        this.tokenBitly = tokenBitly;
    }

    public Boolean getGerarIndicacaoParaCadastroConvidadosVendasOnline() {
        if (gerarIndicacaoParaCadastroConvidadosVendasOnline == null) {
            gerarIndicacaoParaCadastroConvidadosVendasOnline = false;
        }
        return gerarIndicacaoParaCadastroConvidadosVendasOnline;
    }

    public void setGerarIndicacaoParaCadastroConvidadosVendasOnline(Boolean gerarIndicacaoParaCadastroConvidadosVendasOnline) {
        this.gerarIndicacaoParaCadastroConvidadosVendasOnline = gerarIndicacaoParaCadastroConvidadosVendasOnline;
    }

    public boolean isAutorrenovavelEntraRenovacao() {
        return autorrenovavelEntraRenovacao;
    }

    public void setAutorrenovavelEntraRenovacao(boolean autorrenovavelEntraRenovacao) {
        this.autorrenovavelEntraRenovacao = autorrenovavelEntraRenovacao;
    }

    public boolean isBloquearTermoSpam() {
        return bloquearTermoSpam;
    }

    public Boolean getIntegracaoPacto() {
        if(integracaoPacto == null){
            integracaoPacto = false;
        }
        return integracaoPacto;
    }

    public void setIntegracaoPacto(Boolean integracaoPacto) {
        this.integracaoPacto = integracaoPacto;
    }

    public Integer getLimiteDiarioEmails() {
        if(limiteDiarioEmails == null){
            limiteDiarioEmails = 0;
        }
        return limiteDiarioEmails;
    }

    public void setLimiteDiarioEmails(Integer limiteDiarioEmails) {
        this.limiteDiarioEmails = limiteDiarioEmails;
    }

    public Integer getLimiteMensalPacto() {
        if(limiteMensalPacto == null){
            limiteMensalPacto = 0;
        }
        return limiteMensalPacto;
    }

    public void setLimiteMensalPacto(Integer limiteMensalPacto) {
        this.limiteMensalPacto = limiteMensalPacto;
    }

    public boolean isConfiguracaoEmailValida() {
        return (!this.getIntegracaoPacto() &&
                !UteisValidacao.emptyString(this.getLogin()) &&
                !UteisValidacao.emptyString(this.getSenha()) &&
                !UteisValidacao.emptyString(this.getEmailPadrao()) &&
                !UteisValidacao.emptyString(this.getRemetentePadrao()) &&
                !UteisValidacao.emptyString(this.getMailServer()))
                || (this.getIntegracaoPacto() &&
                !UteisValidacao.emptyString(this.getEmailPadrao()) &&
                !UteisValidacao.emptyString(this.getRemetentePadrao()));
    }

    public JSONObject toJSONConfigEmail() {
        JSONObject json = null;
        try {
            json = new JSONObject();
            json.put("integracaoPacto", this.getIntegracaoPacto());
            json.put("limiteDiarioEmails", this.getLimiteDiarioEmails());
            json.put("remetentePadrao", this.getRemetentePadrao());
            json.put("emailPadrao", this.getEmailPadrao());
            json.put("login", this.getLogin());
            json.put("senha", this.getSenha());
            json.put("enviarEmailIndividualmente", this.isEnviarEmailIndividualmente());
            json.put("mailServer", this.getMailServer());
            json.put("portaServer", this.getPortaServer());
            json.put("conexaoSegura", this.isConexaoSegura());
            json.put("iniciarTLS", this.isIniciarTLS());
            json.put("usaSMTPS", this.getUsaSMTPS());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return json;
    }

    public List<ConfiguracaoDiasMetasTO> getConfiguracaoDiasMetasUltimoAcessoGymp() {
        return configuracaoDiasMetasUltimoAcessoGymp;
    }

    public void setConfiguracaoDiasMetasUltimoAcessoGymp(List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasUltimoAcessoGymp) {
        this.configuracaoDiasMetasUltimoAcessoGymp = configuracaoDiasMetasUltimoAcessoGymp;
    }

    public boolean isDirecionaragendamentosexperimentaisagenda() {
        return direcionaragendamentosexperimentaisagenda;
    }

    public void setDirecionaragendamentosexperimentaisagenda(boolean direcionaragendamentosexperimentaisagenda) {
        this.direcionaragendamentosexperimentaisagenda = direcionaragendamentosexperimentaisagenda;
    }
}

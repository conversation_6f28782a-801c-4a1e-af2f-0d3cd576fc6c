package negocio.comuns.crm;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Dominios;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade GrupoColaborador. Classe do tipo
 * VO - Value Object composta pelos atributos da entidade com visibilidade
 * protegida e os métodos de acesso a estes atributos. Classe utilizada para
 * apresentar e manter em memória os dados desta entidade.
 * 
 * @see SuperVO
 */
public class GrupoColaboradorVO extends SuperVO implements Cloneable {

    protected Integer codigo;
    protected String descricao;
    @ChaveEstrangeira
    @FKJson
    private UsuarioVO gerente;
    private String tipoGrupo;
    private String tipoGrupoAnterior;
    private Long totalCliente;
    @FKJson
    private EmpresaVO empresa;
    /**
     * Atributo responsável por manter os objetos da classe
     * <code>GrupoColaboradorParticipante</code>.
     */
    @Lista
    @ListJson(clazz = GrupoColaboradorParticipanteVO.class)
    private List<GrupoColaboradorParticipanteVO> grupoColaboradorParticipanteVOs;
    // Booleano que server para dizer se o SimpleToolePanel vira aberto ou
    // fechado na tela inicial do CRM.
    private Boolean abrirSimpleTooglePanelPassivo;
    private Boolean abrirSimpleTooglePanelIndicacao;
    private String nomeListaClienteOrganizador;
    private Boolean todosParticipantesSelecionados;

    private String descricaoOrdenacao;
    private boolean semGrupo;
    /**
     * Construtor padrão da classe <code>GrupoColaborador</code>. Cria uma nova
     * instância desta entidade, inicializando automaticamente seus atributos
     * (Classe VO).
     */
    public GrupoColaboradorVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da
     * classe <code>GrupoColaboradorVO</code>.
     */
    public static void validarUnicidade(List<GrupoColaboradorVO> lista, GrupoColaboradorVO obj) throws ConsistirException {
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>GrupoColaboradorVO</code>. Todos os tipos de consistência de dados
     * são e devem ser implementadas neste método. São validações típicas:
     * verificação de campos obrigatórios, verificação de valores válidos para
     * os atributos.
     *
     * @exception ConsistirException
     *                Se uma inconsistência for encontrada aumaticamente é
     *                gerada uma exceção descrevendo o atributo e o erro
     *                ocorrido.
     */
    public static void validarDados(GrupoColaboradorVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO deve ser informado!");
        }
        if ((obj.getGerente() == null) || (obj.getGerente().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo RESPONSÁVEL PELO GRUPO deve ser informado!");
        }
        if ((obj.getTipoGrupo().equals(""))) {
        	throw new ConsistirException("O campo TIPO GRUPO deve ser informado!");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(null);
        setDescricao("");
        setGerente(new UsuarioVO());
        setTipoGrupo("");
        setTotalCliente(new Long(0));
        setGrupoColaboradorParticipanteVOs(new ArrayList());
        // atributo usado para AberturaMetaControle
        setAbrirSimpleTooglePanelPassivo(false);
        // atributo usado para AberturaMetaControle
        setAbrirSimpleTooglePanelIndicacao(false);
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>GrupoColaboradorParticipanteVO</code> ao List
     * <code>grupoColaboradorParticipanteVOs</code>. Utiliza o atributo padrão
     * de consulta da classe <code>GrupoColaboradorParticipante</code> -
     * getColaborador().getCodigo() - como identificador (key) do objeto no
     * List.
     *
     * @param obj
     *            Objeto da classe <code>GrupoColaboradorParticipanteVO</code>
     *            que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjGrupoColaboradorParticipanteVOs(GrupoColaboradorParticipanteVO obj) throws Exception {
        GrupoColaboradorParticipanteVO.validarDados(obj);
        obj.setGrupoColaborador(this);
        obj.setTipoGrupo(getTipoGrupo());
        int index = 0;
        Iterator i = getGrupoColaboradorParticipanteVOs().iterator();
        while (i.hasNext()) {
            GrupoColaboradorParticipanteVO objExistente = (GrupoColaboradorParticipanteVO) i.next();
            if (objExistente.getColaboradorParticipante().getCodigo().equals(obj.getColaboradorParticipante().getCodigo().intValue())) {
                getGrupoColaboradorParticipanteVOs().set(index, obj);
                return;
            }
            index++;
        }
        getGrupoColaboradorParticipanteVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>GrupoColaboradorParticipanteVO</code> no List
     * <code>grupoColaboradorParticipanteVOs</code>. Utiliza o atributo padrão
     * de consulta da classe <code>GrupoColaboradorParticipante</code> -
     * getColaborador().getCodigo() - como identificador (key) do objeto no
     * List.
     *
     * @param codigo
     *            Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjGrupoColaboradorParticipanteVOs(Integer codigo) throws Exception {
        int index = 0;
        Iterator i = getGrupoColaboradorParticipanteVOs().iterator();
        while (i.hasNext()) {
            GrupoColaboradorParticipanteVO objExistente = (GrupoColaboradorParticipanteVO) i.next();
            if (objExistente.getColaboradorParticipante().getCodigo().equals(codigo)) {
                getGrupoColaboradorParticipanteVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>GrupoColaboradorParticipanteVO</code> no List
     * <code>grupoColaboradorParticipanteVOs</code>. Utiliza o atributo padrão
     * de consulta da classe <code>GrupoColaboradorParticipante</code> -
     * getColaborador().getCodigo() - como identificador (key) do objeto no
     * List.
     *
     * @param colaborador
     *            Parâmetro para localizar o objeto do List.
     */
    public GrupoColaboradorParticipanteVO consultarObjGrupoColaboradorParticipanteVO(Integer colaborador) throws Exception {
        Iterator i = getGrupoColaboradorParticipanteVOs().iterator();
        while (i.hasNext()) {
            GrupoColaboradorParticipanteVO objExistente = (GrupoColaboradorParticipanteVO) i.next();
            if (objExistente.getColaboradorParticipante().getCodigo().equals(colaborador)) {
                return objExistente;
            }
        }
        return null;
    }

    public void colocarTipoColaboradorIgualTipoGrupo() {
    	setTipoGrupoAnterior(getTipoGrupo());
        Iterator i = getGrupoColaboradorParticipanteVOs().iterator();
        while (i.hasNext()) {
            GrupoColaboradorParticipanteVO participante = (GrupoColaboradorParticipanteVO) i.next();
            if (participante.getTipoVisao().equals("VI")) {
                Iterator j = participante.getColaboradorParticipante().getListaTipoColaboradorVOs().iterator();
                while (j.hasNext()) {
                    TipoColaboradorVO tipo = (TipoColaboradorVO) j.next();
                    if (tipo.getDescricao().equals(getTipoGrupo())) {
                        participante.setNaoPermitirAlterarTipoVisao(false);
                        return;
                    }
                }
                participante.setNaoPermitirAlterarTipoVisao(true);
            } else {
                participante.setNaoPermitirAlterarTipoVisao(false);
                participante.setTipoGrupo(getTipoGrupo());
            }
        }
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe
     * <code>GrupoColaboradorParticipante</code>.
     */
    public List<GrupoColaboradorParticipanteVO> getGrupoColaboradorParticipanteVOs() {
        if (grupoColaboradorParticipanteVOs == null) {
            grupoColaboradorParticipanteVOs = new ArrayList();
        }
        return (grupoColaboradorParticipanteVOs);
    }

    /**
     * Define Atributo responsável por manter os objetos da classe
     * <code>GrupoColaboradorParticipante</code>.
     */
    public void setGrupoColaboradorParticipanteVOs(List grupoColaboradorParticipanteVOs) {
        this.grupoColaboradorParticipanteVOs = grupoColaboradorParticipanteVOs;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public String getDescricaoComQuantidade() {
        if (descricao == null) {
            descricao = "";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(getDescricao());
        sb.append(" (");
        sb.append(grupoColaboradorParticipanteVOs == null ? 0 : grupoColaboradorParticipanteVOs.size());
        sb.append(")");
        return sb.toString();
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the gerente
     */
    public UsuarioVO getGerente() {
        return gerente;
    }

    /**
     * @param gerente
     *            the gerente to set
     */
    public void setGerente(UsuarioVO gerente) {
        this.gerente = gerente;
    }

    public String getTipoGrupo_Apresentar() {
        if (tipoGrupo.equals(null)) {
            return "";
        }
        return Dominios.getTipoGrupo().get(tipoGrupo);
    }

    /**
     * @return the tipoGrupo
     */
    public String getTipoGrupo() {
        if (tipoGrupo == null) {
            tipoGrupo = "";
        }
        return tipoGrupo;
    }

    /**
     * @param tipoGrupo
     *            the tipoGrupo to set
     */
    public void setTipoGrupo(String tipoGrupo) {
        this.tipoGrupo = tipoGrupo;
    }

    public Long getTotalParticipantes() {
        Long x;
        x = new Long(getGrupoColaboradorParticipanteVOs().size());
        return x;
    }

    /**
     * @return the totalCliente
     */
    public Long getTotalCliente() {
        if (totalCliente == null) {
            setTotalCliente(new Long(0));
        }

        if (getGrupoColaboradorParticipanteVOs() != null && !getGrupoColaboradorParticipanteVOs().isEmpty()) {
            Long x = 0L;
            Iterator i = getGrupoColaboradorParticipanteVOs().iterator();
            while (i.hasNext()) {
                GrupoColaboradorParticipanteVO part = (GrupoColaboradorParticipanteVO) i.next();
                x = x + part.getTotalCliente();
            }
            return x;
        }

        return totalCliente;
    }

    /**
     * @param totalCliente
     *            the totalCliente to set
     */
    public void setTotalCliente(Long totalCliente) {
        this.totalCliente = totalCliente;
    }
    
    

    /**
	 * @return the tipoGrupoAnterior
	 */
	public String getTipoGrupoAnterior() {
		 if (tipoGrupoAnterior == null) {
			 tipoGrupoAnterior = "";
	        }
		return tipoGrupoAnterior;
	}

	/**
	 * @param tipoGrupoAnterior the tipoGrupoAnterior to set
	 */
	public void setTipoGrupoAnterior(String tipoGrupoAnterior) {
		this.tipoGrupoAnterior = tipoGrupoAnterior;
	}

	

    /**
     * @return the abrirSimpleTooglePanel
     */
    public Boolean getAbrirSimpleTooglePanelPassivo() {
        return abrirSimpleTooglePanelPassivo;
    }

    /**
     * @param abrirSimpleTooglePanelPassivo
     *            the abrirSimpleTooglePanel to set
     */
    public void setAbrirSimpleTooglePanelPassivo(Boolean abrirSimpleTooglePanelPassivo) {
        this.abrirSimpleTooglePanelPassivo = abrirSimpleTooglePanelPassivo;
    }

    /**
     * @return the abrirSimpleTooglePanelIndicacao
     */
    public Boolean getAbrirSimpleTooglePanelIndicacao() {
        return abrirSimpleTooglePanelIndicacao;
    }

    /**
     * @param abrirSimpleTooglePanelIndicacao
     *            the abrirSimpleTooglePanelIndicacao to set
     */
    public void setAbrirSimpleTooglePanelIndicacao(Boolean abrirSimpleTooglePanelIndicacao) {
        this.abrirSimpleTooglePanelIndicacao = abrirSimpleTooglePanelIndicacao;
    }

    /**
     * @return the nomeListaClienteOrganizador
     */
    public String getNomeListaClienteOrganizador() {
        if (nomeListaClienteOrganizador == null) {
            nomeListaClienteOrganizador = "";
        }
        return nomeListaClienteOrganizador;
    }

    /**
     * @param nomeListaClienteOrganizador
     *            the nomeListaClienteOrganizador to set
     */
    public void setNomeListaClienteOrganizador(String nomeListaClienteOrganizador) {
        this.nomeListaClienteOrganizador = nomeListaClienteOrganizador;
    }

    public GrupoColaboradorVO clone() throws CloneNotSupportedException {
        GrupoColaboradorVO obj = (GrupoColaboradorVO) super.clone();
        for (GrupoColaboradorParticipanteVO parti : obj.getGrupoColaboradorParticipanteVOs()) {
            GrupoColaboradorParticipanteVO partipante = parti.clone();
            obj.getGrupoColaboradorParticipanteVOs().add(partipante);
        }
        return obj;
    }

    public boolean equals(Object obj) {
        if(obj instanceof GrupoColaboradorVO && obj != null) {
            GrupoColaboradorVO aux = (GrupoColaboradorVO)obj;
            return this.getCodigo().intValue() == aux.getCodigo().intValue();
        }
        return false;
    }

	/**
	 * @param empresa the empresa to set
	 */
	public void setEmpresa(EmpresaVO empresa) {
		this.empresa = empresa;
	}

	/**
	 * @return the empresa
	 */
	public EmpresaVO getEmpresa() {
		if(empresa == null){
			empresa = new EmpresaVO();
		}
		return empresa;
	}

    public Boolean getTodosParticipantesSelecionados() {
        if (todosParticipantesSelecionados == null) {
            todosParticipantesSelecionados = false;
        }
        return todosParticipantesSelecionados;
    }

    public void setTodosParticipantesSelecionados(Boolean todosParticipantesSelecionados) {
        this.todosParticipantesSelecionados = todosParticipantesSelecionados;
    }

    public String getDescricaoOrdenacao() {
	    if (descricaoOrdenacao == null) {
	        descricaoOrdenacao = descricao;
        }
        return descricaoOrdenacao;
    }

    public void setDescricaoOrdenacao(String descricaoOrdenacao) {
        this.descricaoOrdenacao = descricaoOrdenacao;
    }

    public boolean isSemGrupo() {
        return semGrupo;
    }

    public void setSemGrupo(boolean semGrupo) {
        this.semGrupo = semGrupo;
    }
}

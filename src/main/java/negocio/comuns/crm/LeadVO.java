/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.crm;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.facade.jdbc.arquitetura.Log;
import servicos.integracao.enumerador.TipoLeadEnum;

/**
 *
 * <AUTHOR>
 */
public class LeadVO extends SuperVO {

    private Integer codigo = 0;
    private PassivoVO passivo = new PassivoVO();
    private ClienteVO cliente = new ClienteVO();
    private String uuid = "";
    private String urlRD = "";
    private String email = "";
    private EmpresaVO empresa = new EmpresaVO();
    private Long idLead =0L;
    private IndicadoVO indicado = new IndicadoVO();
    private TipoLeadEnum tipo = null;
    private String dados;

    public TipoLeadEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoLeadEnum tipo) {
        this.tipo = tipo;
    }
    
    public PassivoVO getPassivo() {
        return passivo;
    }

    public void setPassivo(PassivoVO passivo) {
        this.passivo = passivo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUrlRD() {
        return urlRD;
    }

    public void setUrlRD(String urlRD) {
        this.urlRD = urlRD;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public long getIdLead() {
        return idLead;
    }

    public void setIdLead(long idLead) {
        this.idLead = idLead;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public IndicadoVO getIndicado() {
        return indicado;
    }

    public void setIndicado(IndicadoVO indicado) {
        this.indicado = indicado;
    }

    public String getDados() {
        if (dados == null) {
            dados = "";
        }
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.crm;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class OAuth2RDStationVO extends SuperVO {

    private Integer codigo = 0;
    private Integer codigoEmpresa = 0;
    private String codigoAuth;
    private String acessToken;
    private String refreshToken;
    private Date dataPersistir;


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getCodigoAuth() {
        return codigoAuth;
    }

    public void setCodigoAuth(String codigoAuth) {
        this.codigoAuth = codigoAuth;
    }

    public String getAcessToken() {
        return acessToken;
    }

    public void setAcessToken(String acessToken) {
        this.acessToken = acessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Date getDataPersistir() {
        return dataPersistir;
    }

    public void setDataPersistir(Date dataPersistir) {
        this.dataPersistir = dataPersistir;
    }
}

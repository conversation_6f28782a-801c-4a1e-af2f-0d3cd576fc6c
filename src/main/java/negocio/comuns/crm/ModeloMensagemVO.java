package negocio.comuns.crm;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import org.jfree.data.DomainInfo;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Reponsável por manter os dados da entidade ModeloMensagem. Classe do tipo VO
 * - Value Object composta pelos atributos da entidade com visibilidade
 * protegida e os métodos de acesso a estes atributos. Classe utilizada para
 * apresentar e manter em memória os dados desta entidade.
 * 
 * @see SuperVO
 */
public class ModeloMensagemVO extends SuperVO {

    protected Integer codigo;
    protected String titulo;
    protected String mensagem;
    protected String tipoMensagem;
    protected String tipoModeloMensagem;
    protected transient byte[] imagemModelo;
    protected String mensagemImagemModelo;
    protected String nomeImagem;
    protected MeioEnvio meioDeEnvio;
    @NaoControlarLogAlteracao
    private boolean feitoUploadAlgumaFoto = false;
    private String fotoKey;
    private boolean temImagem = false;
    private boolean ativo = true;
    private String configs;
    private String urlRedirecionamento;

    private Boolean builder = false;


    /**
     * Construtor padrão da classe <code>ModeloMensagem</code>. Cria uma nova
     * instância desta entidade, inicializando automaticamente seus atributos
     * (Classe VO).
     *
     * @throws Exception
     */
    public ModeloMensagemVO(Boolean carregado) throws Exception {
        super();
        inicializarDadosCarregando();

    }

    /**
     * Construtor padrão da classe <code>ModeloMensagem</code>. Cria uma nova
     * instância desta entidade, inicializando automaticamente seus atributos
     * (Classe VO).
     *
     * @throws Exception
     */
    public ModeloMensagemVO() {
        super();
        inicializarDados();

    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ModeloMensagemVO</code>. Todos os tipos de consistência de dados
     * são e devem ser implementadas neste método. São validações típicas:
     * verificação de campos obrigatórios, verificação de valores válidos para
     * os atributos.
     *
     * @exception ConsistirException
     *                Se uma inconsistência for encontrada aumaticamente é
     *                gerada uma exceção descrevendo o atributo e o erro
     *                ocorrido.
     */
    public static void validarDados(ModeloMensagemVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getTitulo().equals("")) {
            throw new ConsistirException("Digite um título.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
        setTipoMensagem(getTipoMensagem().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     *
     * @throws Exception
     * @throws Exception
     */
    public void inicializarDados() {
        setCodigo(null);
        setTitulo("");
        setMensagem("");
        setTipoMensagem("");
        setUrlRedirecionamento("");
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     *
     * @throws Exception
     * @throws Exception
     */
    public void inicializarDadosCarregando() throws Exception {
        setCodigo(null);
        setTitulo("");
//		setMensagem(getModeloMensagem());
        setMensagem("");
        setTipoMensagem("");
        initImagemModelo();

    }

    public void initImagemModelo() {
        setMensagemImagemModelo("");
        setImagemModelo(null);
        setFotoKey("");
    }

    /**
     * Metodo responsavel por atribuir o modelo correspondente
     *
     * Autor: Pedro Y. Saito
     * Criado em 04/01/2011
     */
    public void setModeloMensagem(String tipoModeloMsg, EmpresaVO empresaVO, MeioEnvio meioEnvio, String chave) throws Exception {
        if (meioEnvio == MeioEnvio.EMAIL) {
            String dominio = Uteis.obterCaminhoWeb();

            initImagemModelo();

            InputStream in = null;
            String msg = null;

            //Verificacao de qual modelo foi selecionado
            if ("selecione".equals(tipoModeloMsg)) {
                setMensagem("");
            } else if ("PA".equals(tipoModeloMsg)) {
                setMensagem(getModeloMensagem());
            } else {
                if (empresaVO != null) {
                    if ("A1".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Aniversario_01/html_limpo.htm"));
                    } else if ("A2".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Aniversario_02/html_limpo.htm"));
                    } else if ("A3".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Aniversario_03/html_limpo.htm"));
                    } else if ("A4".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Aniversario_04/html_limpo.htm"));
                    } else if ("A5".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Aniversario_05/html_limpo.htm"));
                    } else if ("A6".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Aniversario_06/html_limpo.htm"));
                    } else if ("A7".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Aniversario_07/html_limpo.htm"));
                    } else if ("A8".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Aniversario_08/html_limpo.htm"));
                    } else if ("A9".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Aniversario_09/html_limpo.htm"));
                    } else if ("FA".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Falta/html_limpo.htm"));
                    } else if ("G1".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Generalizado_Light/html_limpo.htm"));
                    } else if ("G2".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Generalizado_Light_Blue/html_limpo.htm"));
                    } else if ("G3".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Generalizado_Light_Green/html_limpo.htm"));
                    } else if ("I1".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Informativo_01/html_limpo.htm"));
                    } else if ("I2".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Informativo_02/html_limpo.htm"));
                    } else if ("I3".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Informativo_03/html_limpo.htm"));
                    } else if ("I4".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Informativo_04/html_limpo.htm"));
                    } else if ("R1".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Revisao_Avaliacao_Fisica_01/html_limpo.htm"));
                    } else if ("R2".equals(tipoModeloMsg)) {
                        in = new FileInputStream(new File(dominio + "/imagensCRM/email/Revisao_Avaliacao_Fisica_02/html_limpo.htm"));
                    }

                    msg = Uteis.convertStreamToString(in);

                    if (msg != null && !"".equals(msg)) {
                        setMensagem(msg);
                        //Chamada para o metodo que ira substituir as tags pelos dados
                        setMensagem(personalizarModeloMsg(this.getMensagem(), empresaVO, chave, true));
                    }
                }
            }
        } else if (meioEnvio == MeioEnvio.SMS) {
            if ("selecione".equals(tipoModeloMsg)) {
                setMensagem("");
            } else if ("A1".equals(tipoModeloMsg)) {
                setMensagem("Nao poderiamos deixar de lembrar de uma data tao especial, seu aniversario! Parabens e felicidades! Academia " + empresaVO.getNome());
            } else if ("A2".equals(tipoModeloMsg)) {
                setMensagem("A gente nunca esquece de voce! Parabens e feliz aniversario! Academia " + empresaVO.getNome());
            } else if ("A3".equals(tipoModeloMsg)) {
                setMensagem("O aniversario eh seu, mas o presente de ter você como aluno eh nosso! FELIZ ANIVERSARIO! Academia " + empresaVO.getNome());
            } else if ("A4".equals(tipoModeloMsg)) {
                setMensagem("Que seu aniversario seja repleto de saude, paz e muito amor! PARABENS! Academia " + empresaVO.getNome());
            } else if ("A5".equals(tipoModeloMsg)) {
                setMensagem("Comemore muito e se comer muito bolo lembre-se: a gente esta aqui! PARABENS! Academia " + empresaVO.getNome());
            } else if ("F1".equals(tipoModeloMsg)) {
                setMensagem("Nao acredito que voce esta desistindo da sua vida mais saudavel!!! Sentimos sua falta ein? Academia " + empresaVO.getNome());
            } else if ("F2".equals(tipoModeloMsg)) {
                setMensagem("Acredita que menos de 5% da população pratica atividade fisica? Nao desista de entrar para esse seleto grupo! Academia " + empresaVO.getNome());
            } else if ("F3".equals(tipoModeloMsg)) {
                setMensagem("Agora voce ja sabe: estamos no seu pe! Esperamos voce amanha para retornar as atividades normais! Academia " + empresaVO.getNome());
            } else if ("F4".equals(tipoModeloMsg)) {
                setMensagem("Voce perdeu a aula de NOME DA AULA! Foi divertidissima, esperamos voce amanha ein? Academia " + empresaVO.getNome());
            } else if ("V1".equals(tipoModeloMsg)) {
                setMensagem("Voce esta esperando mais o que para se decidir e ter uma vida mais saudavel? Te espero hoje na Academia " + empresaVO.getNome());
            } else if ("V2".equals(tipoModeloMsg)) {
                setMensagem("A Nome da Academia agradece a visita! Esperamos voce para auxilia-lo a ter uma vida saudavel! Academia " + empresaVO.getNome());
            } else if ("V3".equals(tipoModeloMsg)) {
                setMensagem("Faca parte do seleto grupo de 5% da populacao que pratica atividade fisica! Academia " + empresaVO.getNome());
            } else if ("V4".equals(tipoModeloMsg)) {
                setMensagem("Parabens! Voce acaba de ganhar uma semana para conhecer a Academia " + empresaVO.getNome() + " melhor! Academia " + empresaVO.getNome());
            } else if ("PV1".equals(tipoModeloMsg)) {
                setMensagem("Seja bem-vindo a Academia " + empresaVO.getNome() + " ! " + empresaVO.getNome());
            } else if ("PV2".equals(tipoModeloMsg)) {
                setMensagem("Agora voce vai ter mais saude e qualidade de vida! Parabens pela escolha! Academia " + empresaVO.getNome());
            } else if ("PV3".equals(tipoModeloMsg)) {
                setMensagem("O primeiro dia voce nunca esquece, nem a gente! Seja bem-vindo! " + empresaVO.getNome());
            } else if ("GR1".equals(tipoModeloMsg)) {
                setMensagem("A Nome da Academia esta preocupada com voce! Ligue-nos e marque uma visita! " + empresaVO.getNome());
            } else if ("GR2".equals(tipoModeloMsg)) {
                setMensagem("Voce estava indo tao bem, nao vai desistir ein? Estamos esperando voce para um papo! Academia " + empresaVO.getNome());
            }
        }
    }

    /**
     * Metodo responsavel por alterar o html com as seguintes tags abaixo, para os seus
     * respectivos dados:
     *
     * 	DOMINIO_APP_TAG, ENDERECO_TAG, CIDADE_ESTADO_TAG, TELEFONE_TAG, WEB_SITE_TAG, LOGO_EMPRESA_TAG
     *
     * Autor: Pedro Y. Saito
     * Criado em 04/01/2011
     */
    public static String personalizarModeloMsg(String msg, EmpresaVO empresaVO, String chave, boolean addHtml) throws Exception {
        String msgPerso = null;
        String msgOriginal = msg;
        String key = null;
        int parametro = 0;

        String path = PropsService.getPropertyValue(PropsService.urlAplicacaoCentral) + "/imagensCRM/email/img_modelo";
        //Tag para imagens
        parametro = msg.lastIndexOf("DOMINIO_APP_TAG");
        if (parametro > 0) {
            path = path.replaceAll("\\\\", "/");
            msgPerso = msg.replaceAll("DOMINIO_APP_TAG", path);
            msg = msgPerso;
        }

        //Tag para endereco
        parametro = msg.lastIndexOf("ENDERECO_TAG");
        if (parametro > 0 && empresaVO.getEndereco() != null) {
            if (!"".equals(empresaVO.getEndereco())) {
                msgPerso = msg.replaceAll("ENDERECO_TAG", addHtml ? "&nbsp;" + empresaVO.getEndereco() + "<br>" : empresaVO.getEndereco());
            } else {
                msgPerso = msg.replaceAll("ENDERECO_TAG", "");
            }
            msg = msgPerso;
        }

        //Tag para cidade/estado
        parametro = msg.lastIndexOf("CIDADE_ESTADO_TAG");
        if (parametro > 0) {
            String aux = "";
            if (empresaVO.getCidade() != null && empresaVO.getCidade().getNome() != null
                    && !"".equals(empresaVO.getCidade().getNome())) {
                aux = empresaVO.getCidade().getNome();
            }
            if (empresaVO.getEstado() != null && empresaVO.getEstado().getSigla() != null
                    && !"".equals(empresaVO.getEstado().getSigla())) {
                aux += " - " + empresaVO.getEstado().getSigla();
            }
            if (aux != null && !"".equals(aux)) {
                msgPerso = msg.replaceAll("CIDADE_ESTADO_TAG", addHtml ? "&nbsp;" + aux + "<br>" : aux);
            } else {
                msgPerso = msg.replaceAll("CIDADE_ESTADO_TAG", "");
            }

            msg = msgPerso;
        }

        //Tag para telefone
        parametro = msg.lastIndexOf("TELEFONE_TAG");
        if (parametro > 0 && empresaVO.getTelComercial1() != null) {
            if (!"".equals(empresaVO.getTelComercial1())) {
                msgPerso = msg.replaceAll("TELEFONE_TAG", addHtml ? "&nbsp;" + empresaVO.getTelComercial1() + "<br>" : empresaVO.getTelComercial1() );
            } else {
                msgPerso = msg.replaceAll("TELEFONE_TAG", "");
            }

            msg = msgPerso;
        }

        //Tag para web site
        parametro = msg.lastIndexOf("WEB_SITE_TAG");
        if (parametro > 0 && empresaVO.getSite() != null) {
            if (!"".equals(empresaVO.getSite())) {
                msgPerso = msg.replaceAll("WEB_SITE_TAG", addHtml ?"<blink>&nbsp;<a href=\"http://" + empresaVO.getSite() + "\" target=_blank>" + empresaVO.getSite() + "</a></blink>" : empresaVO.getSite());
            } else {
                msgPerso = msg.replaceAll("WEB_SITE_TAG", "");
            }

            msg = msgPerso;
        }

        //Tag para nome da empresa
        parametro = msg.lastIndexOf("NOME_EMPRESA");
        if (parametro > 0) {
            if (!UteisValidacao.emptyString(empresaVO.getNome())) {
                msgPerso = msg.replaceAll("NOME_EMPRESA", addHtml ? "&nbsp;" + empresaVO.getNome() + "<br>" : empresaVO.getNome());
            } else {
                msgPerso = msg.replaceAll("NOME_EMPRESA", "");
            }
            msg = msgPerso;
        }

        //Tag para logo da empresa
        parametro = msg.lastIndexOf("LOGO_EMPRESA_TAG");
        if (parametro > 0) {
            String logo = "";
            String caminho = "";
            if (empresaVO.getExisteFotoEmail()) {
                if (UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
                    caminho = "imagensCRM/_Logomarca_CRM_WEB.png";
                } else {
                    key =  MidiaService.getInstance().genKey(chave,
                            MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, empresaVO.getCodigo().toString(),".jpg");
                    caminho = PropsService.getPropertyValue(PropsService.urlFotosNuvem) + "/" + key;
                }
                logo = "<img border=\"0\" src=\"" + caminho + "\" width=\"190\" height=\"78\" align=\"left\" style=\"valign:middle\">";

            }

            if ("".equals(logo)) {
                caminho = Uteis.getURLAplicacao("/imagensCRM/") + "_Logomarca_CRM_WEB.png";
                logo = "<img border=\"0\" src=\"" + caminho + "\" width=\"190\" height=\"78\" align=\"left\" style=\"valign:middle\">";
            }

            msgPerso = msg.replaceAll("LOGO_EMPRESA_TAG", logo);

            msg = msgPerso;
        }

        if ((msgPerso == null) || ("".equals(msgPerso))) {
            msgPerso = msgOriginal;
        }

        return msgPerso;
    }

    public static String personalizarTagNomePessoa(String msg, String nome) {
        String mensagem = msg.replace("TAG_NOME", nome);
        return mensagem.replace("TAG_PNOME", Uteis.getPrimeiroNome(nome));
    }

    /**
     * <AUTHOR>
     * 07/10/2011
     */
    public void setarLogoEmpresa(EmpresaVO empresa) throws Exception {
        String caminho = "imagensCRM/email/tmp/logo_empresa_temp_" + Uteis.retirarAcentuacaoRegex(empresa.getNome().replaceAll(" ", "")) + ".png";
        if (mensagem.contains("imagensCRM/email/logo.png")) {
            if (UteisValidacao.emptyNumber(empresa.getCodigo())) {
                mensagem = mensagem.replaceAll("imagensCRM/email/logo.png", "imagensCRM/_Logomarca_CRM_WEB.png");
            } else {
                criarImagemLogoTemporaria(empresa);
                mensagem = mensagem.replaceAll("imagensCRM/email/logo.png", caminho);
                return;
            }
        }
        Pattern verificaMensagem = Pattern.compile("imagensCRM/email/tmp/logo_empresa_temp_.*?.png");
        Matcher m = verificaMensagem.matcher(mensagem);
        // enquanto o Matcher encontrar o pattern na String fornecida:
        while (m.find()) {
            if (UteisValidacao.emptyNumber(empresa.getCodigo())) {
                mensagem = mensagem.replaceAll(m.group(), "imagensCRM/_Logomarca_CRM_WEB.png");
            } else {
                criarImagemLogoTemporaria(empresa);
            }
        }
    }

    /**
     * <AUTHOR>
     * 07/10/2011
     */
    private static String criarImagemLogoTemporaria(EmpresaVO empresaVO) throws Exception {
        String caminho;
        String nomeImagem = Uteis.retirarAcentuacaoRegex(empresaVO.getNome().replaceAll(" ", ""));
        caminho = Uteis.obterCaminhoWeb() + "/imagensCRM/email/tmp/";
        caminho = caminho.replaceAll("\\\\", "/");

        empresaVO.setFotoEmail(UteisEmail.obterFotoEmpresa(empresaVO.getCodigo()));

        UteisEmail.criarImagem(caminho, empresaVO.getFotoEmail(), "logo_empresa_temp_" + nomeImagem + ".jpg");

        caminho = Uteis.getURLAplicacao("/imagensCRM/email/tmp/") + "logo_empresa_temp_" + nomeImagem + ".jpg";
        return caminho;
    }

    public boolean isMensagemUploadImagem() throws Exception {
        return this.getMensagem().contains("malaDiretaImagemCRM");
    }

    /**
     * Metodo que gera o HTML para a imagem que foi feito upload
     *
     * Autor: Pedro Y. Saito
     * Criado em 11/04/2011
     */
    public void criarMensagemUploadImagem(String caminho, String nomeArquivo) throws Exception {
//		String path = Uteis.getURLAplicacao("/imagensCRM/email/") + "uploadImagens/" + arq;
        StringBuilder email = new StringBuilder();

        email.append("<html>");
        email.append("<head>");
        email.append("<meta http-equiv=\"Content-Language\" content=\"pt-br>");
        email.append("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=windows-1252\">");
        email.append("</head>");
        email.append("<body>");
        email.append("<table border=\"0\" width=\"652\" style=\"border-collapse: collapse\">");
        email.append("<tr>");
        email.append("<td align=\"center\">");
        email.append("<img alt=\""+nomeArquivo+"\" src=\"" + caminho + "\" width=\"650\" id=\"malaDiretaImagemCRM\" border=\"0\"></td>");
        email.append("</tr>");
        email.append("</table>");
        email.append("</body>");
        email.append("</html>");

        setMensagem(email.toString());
    }

    public static String alterarCaminhoImagemParaNomeArquivo(String html, String arquivo, boolean envio) {

        String source = html;
        String sourceTmp = "" + html;

        //String regexTag = "<?\\s*\\w*\\s*src='?\\s*([\\w\\s%#\\/\\.;:_-])'?.*?>";
        String regexTag = "<?\\s*\\w*\\s*src\\s*=\\s*'?\\s*([\\w\\s%#\\/\\.;:_-]*)\\s*'?.*?>";

        Pattern pattern = Pattern.compile(regexTag);

        Matcher matcher = pattern.matcher(sourceTmp);

        // Mostra as similaridades
        while (matcher.find()) {

            String temp = sourceTmp.substring(matcher.start(), matcher.end());
            int ini = temp.indexOf("src=");
            int fim = temp.indexOf("\"", ini);
            fim = temp.indexOf("\"", fim + 1);
            String caminhoArquivo = temp.substring(ini + 5, fim);

            try {
                //
            	String path = envio ? UteisEmail.getCaminhoDasImagens() : Uteis.retornarPathAplicacao();
                String nomeArquivo = ( path + File.separator
                        + caminhoArquivo);
                nomeArquivo = nomeArquivo.replaceAll("////", File.separator);
                if (nomeArquivo != null) {
                    arquivo = arquivo.replaceAll("///", File.separator);

                    source = source.replace("src=\"" + caminhoArquivo,
                            "src=\"" + arquivo);
                }

            } catch (Exception ex) {
                Logger.getLogger(UteisEmail.class.getName()).log(Level.SEVERE, null, ex);
            }

        }

        return source;

    }

    public String getModeloMensagem() throws Exception {

        // obeter o ip ou dns;
        String dominio = Uteis.getURLAplicacao("/imagensCRM/email/");
        String img_cima = dominio + "cima_default1.png";
        String img_meio_cima = dominio + "meio_cima_default1.png";
        String img_meio_baixo = dominio + "meio_baixo_default1.png";
        String img_baixo = dominio + "baixo_default1.png";

        StringBuilder sb = new StringBuilder();
        sb.append("<html><body>");
        sb.append("<div class=\"Section1\">");
        sb.append("<div>");
        sb.append("<table class=\"MsoNormalTable\" style=\"width: 275px; mso-cellspacing: 0cm; mso-yfti-tbllook: 1184; mso-padding-alt: 0cm 0cm 0cm 0cm;\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" width=\"300\">");
        sb.append("<tbody>");
        sb.append("<tr style=\"height: 59.25pt; mso-yfti-irow: 0; mso-yfti-firstrow: yes;\">");
        sb.append("<td style=\"height: 59.25pt; padding: 0cm;\" colspan=\"3\">");
        sb.append("<p class=\"MsoNormal\" style=\"margin:0cm;margin-bottom:.0001pt\"><span style=\"font-family: Arial; color: black; font-size: 10pt; mso-no-proof: yes;\"><img id=\"_x0000_i1025\" src=\"" + img_cima + "\" alt=\"" + img_cima + "\" width=\"626\" height=\"77\" border=\"0\" /></span></p>");
        sb.append("</td>");
        sb.append("</tr>");
        sb.append("<tr style=\"mso-yfti-irow:1\">");
        sb.append("<td style=\"padding:0cm 0cm 0cm 0cm\" colspan=\"3\">");
        sb.append("<p class=\"MsoNormal\" style=\"margin:0cm;margin-bottom:.0001pt\"><span style=\"font-family: Arial; color: black; font-size: 10pt; mso-no-proof: yes;\"><img id=\"_x0000_i1027\" src=\"" + img_meio_cima + "\" border=\"0\" alt=\"" + img_meio_cima + "\" width=\"626\" height=\"20\" /></span></p>");
        sb.append("</td>");
        sb.append("</tr>");
        sb.append("<tr style=\"mso-yfti-irow:2\">");
        sb.append("<td style=\"width: 9pt; background: #FFD7B7; padding: 0cm;\" width=\"12\">");
        sb.append("<p><span style=\"font-size:10.0pt;font-family:Arial;color:black\">&nbsp;</span></p>");
        sb.append("</td>");
        sb.append("<td style=\"width: 469.5pt; background: #FFD7B7; padding: 0cm;\" width=\"626\">");
        sb.append("<p class=\"MsoNormal\" style=\"margin:0cm;margin-bottom:.0001pt\"><span style=\"font-family: Arial; color: black; font-size: 10pt;\">&nbsp;</span></p>");
        sb.append("<p class=\"MsoNormal\" style=\"margin:0cm;margin-bottom:.0001pt\"><span style=\"font-family: Arial; color: black; font-size: 10pt;\"><texto></span></p>");
        sb.append("<p class=\"MsoNormal\" style=\"margin:0cm;margin-bottom:.0001pt\"><span style=\"font-family: Arial; color: black; font-size: 10pt;\">&nbsp;</span></p>");
        sb.append("<p class=\"MsoNormal\" style=\"margin:0cm;margin-bottom:.0001pt\"><span style=\"font-family: Arial; color: black; font-size: 10pt;\">&nbsp;</span></p>");
        sb.append("</span></td>");
        sb.append("<td style=\"width: 9pt; background: #FFD7B7; padding: 0cm;\" width=\"12\">");
        sb.append("<p class=\"MsoNormal\" style=\"margin:0cm;margin-bottom:.0001pt\"><span style=\"font-family: Arial; color: black; font-size: 10pt;\">&nbsp;</span></p>");
        sb.append("</td>");
        sb.append("</tr>");
        sb.append("<tr style=\"mso-yfti-irow:3\">");
        sb.append("<td style=\"padding:0cm 0cm 0cm 0cm\" colspan=\"3\">");
        sb.append("<p class=\"MsoNormal\" style=\"margin:0cm;margin-bottom:.0001pt\"><span style=\"font-family: Arial; color: black; font-size: 10pt; mso-no-proof: yes;\"><img id=\"_x0000_i1027\" src=\"" + img_meio_baixo + "\" border=\"0\" alt=\"" + img_meio_baixo + "\" width=\"626\" height=\"20\" /></span></p>");
        sb.append("</td>");
        sb.append("</tr>");
        sb.append("<tr style=\"height: 43.5pt; mso-yfti-irow: 4; mso-yfti-lastrow: yes;\">");
        sb.append("<td style=\"height: 43.5pt; padding: 0cm;\" colspan=\"3\">");
        sb.append("<p class=\"MsoNormal\" style=\"margin:0cm;margin-bottom:.0001pt\"><span style=\"font-family: Arial; color: black; font-size: 10pt; mso-no-proof: yes;\"><img id=\"_x0000_i1028\" src=\"" + img_baixo + "\" border=\"0\" alt=\"" + img_baixo + "\" width=\"626\" height=\"59\" /></span></p>");
        sb.append("</td>");
        sb.append("</tr>");
        sb.append("</tbody>");
        sb.append("</table>");
        sb.append("</div>");
        sb.append("<p class=\"MsoNormal\" style=\"margin:0cm;margin-bottom:.0001pt\"><span style=\"color: black;\">&nbsp;</span></p>");
        sb.append("</div>");
        sb.append("</body></html>");
        return sb.toString();
    }

    public String getTipoMensagem_Apresentar() {
        if (tipoMensagem == null) {
            tipoMensagem = "";
        }
        return Dominios.getTipoMensagem().get(tipoMensagem);
    }

    public String getTipoMensagem() {
        if (tipoMensagem == null) {
            tipoMensagem = "";
        }
        return (tipoMensagem);
    }

    public void setTipoMensagem(String tipoMensagem) {
        this.tipoMensagem = tipoMensagem;
    }

    public String getMensagem() {
        if (mensagem == null) {
            mensagem = "";
        }
        return (mensagem);
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getTitulo() {
        if (titulo == null) {
            titulo = "";
        }
        return (titulo);
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return O campo tipoModeloMensagem.
     */
    public String getTipoModeloMensagem() {
        return this.tipoModeloMensagem;
    }

    /**
     * @param tipoModeloMensagem O novo valor de tipoModeloMensagem.
     */
    public void setTipoModeloMensagem(String tipoModeloMensagem) {
        this.tipoModeloMensagem = tipoModeloMensagem;
    }

    /**
     * @return O campo imagemModelo.
     */
    public byte[] getImagemModelo() {
        return this.imagemModelo;
    }

    /**
     * @param imagemModelo O novo valor de imagemModelo.
     */
    public void setImagemModelo(byte[] imagemModelo) {
        this.imagemModelo = imagemModelo;
    }

    /**
     * Metodo que verifica se o modelo de mensagem e um modelo com imagem de
     * upload, caso seja a imagem e criada em um diretorio temporario.
     *
     * Devido a problemas de exibicao da imagem no editor de texto do richfaces, foi
     * criado uma variavel que ira armazenar a mensagem que será envidada por e-mail
     * e uma que armazena a imagem de exibicao
     *
     * Autor: Pedro Y. Saito
     * Criado em 11/04/2011
     */
    public void verificarSeExisteImagemModelo(boolean envio, String chave) throws Exception {
        String path = envio ? UteisEmail.getCaminhoDasImagens()+"/" :Uteis.obterCaminhoWeb() + "/imagensCRM/email/tmp/";
        UteisEmail.criarImagem(path, this, chave);
    }

    /**
     * @return O campo mensagemImagemModelo.
     */
    public String getMensagemImagemModelo() {
        return this.mensagemImagemModelo;
    }

    /**
     * @param mensagemImagemModelo O novo valor de mensagemImagemModelo.
     */
    public void setMensagemImagemModelo(String mensagemImagemModelo) {
        this.mensagemImagemModelo = mensagemImagemModelo;
    }

    /**
     * @return O campo nomeImagem.
     */
    public String getNomeImagem() {
        return this.nomeImagem;
    }

    /**
     * @param nomeImagem O novo valor de nomeImagem.
     */
    public void setNomeImagem(String nomeImagem) {
        this.nomeImagem = nomeImagem;
    }

    /**
     * @return the meioDeEnvio
     */
    public MeioEnvio getMeioDeEnvioEnum() {
        if (meioDeEnvio == null) {
            meioDeEnvio = MeioEnvio.EMAIL;
        }
        return meioDeEnvio;
    }

    /**
     * @param meioDeEnvioEnum the meioDeEnvio to set
     */
    public void setMeioDeEnvioEnum(MeioEnvio meioDeEnvioEnum) {
        this.meioDeEnvio = meioDeEnvioEnum;
    }

    public void setMeioDeEnvio(Integer meioDeEnvio) throws Exception {
        this.meioDeEnvio = MeioEnvio.getMeioEnvioPorCodigo(meioDeEnvio);
    }

    public Integer getMeioDeEnvio() {
        return meioDeEnvio == null ? null : getMeioDeEnvioEnum().getCodigo();
    }

    public boolean isFeitoUploadAlgumaFoto() {
        return feitoUploadAlgumaFoto;
    }

    public void setFeitoUploadAlgumaFoto(boolean feitoUploadAlgumaFoto) {
        this.feitoUploadAlgumaFoto = feitoUploadAlgumaFoto;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getCaminhoImagemTemporaria() {
        if (getNomeImagem() == null || getNomeImagem().isEmpty()){
            return "";
        }
        return "imagensCRM/email/tmp/" + getNomeImagem();
    }

    public boolean isTemImagem() {
        return temImagem;
    }

    public void setTemImagem(boolean temImagem) {
        this.temImagem = temImagem;
    }
    
    public boolean getApp(){
        return meioDeEnvio != null && getMeioDeEnvioEnum().equals(MeioEnvio.APP);
    }

    public boolean getEmail(){
        return meioDeEnvio != null && getMeioDeEnvioEnum().equals(MeioEnvio.EMAIL);
    }

    public boolean isFtp(){
        return meioDeEnvio != null && getMeioDeEnvioEnum().equals(MeioEnvio.FTP);
    }

    public boolean getSms(){
        return meioDeEnvio != null && getMeioDeEnvioEnum().equals(MeioEnvio.SMS);
    }

    public boolean isApresentarTags(ConfiguracaoSistemaCRMVO configCRM) {
        return getSms() || getApp() || (getEmail() && configCRM.isEnviarEmailIndividualmente()) || isFtp();
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public String getUrlRedirecionamento() {
        if(urlRedirecionamento ==  null){
            urlRedirecionamento = "";
        }
        return urlRedirecionamento;
    }

    public void setUrlRedirecionamento(String urlRedirecionamento) {
        this.urlRedirecionamento = urlRedirecionamento;
    }

    public Boolean getBuilder() {
        if(builder == null){
            builder = false;
        }
        return builder;
    }

    public void setBuilder(Boolean builder) {
        this.builder = builder;
    }

    public String getConfigs() {
        return configs;
    }

    public void setConfigs(String configs) {
        this.configs = configs;
    }
}

package negocio.comuns.crm;


import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

public class QuarentenaVO extends SuperVO {

    private Integer codigo;
    private Date inicioQuarentena;
    private Date fimQuarentena;
    private Boolean ativa;
    private EmpresaVO empresa;
    private UsuarioVO usuarioIniciou;
    private UsuarioVO usuarioEncerrou;
    private String motivo = "";

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getInicioQuarentena() {
        return inicioQuarentena;
    }

    public String getInicioQuarentena_Apresentar() {
        if(inicioQuarentena != null){
            return Calendario.getData(this.inicioQuarentena, "dd/MM/yyyy HH:mm:ss");
        }
        return "";
    }

    public void setInicioQuarentena(Date inicioQuarentena) {
        this.inicioQuarentena = inicioQuarentena;
    }

    public Date getFimQuarentena() {
        return fimQuarentena;
    }

    public void setFimQuarentena(Date fimQuarentena) {
        this.fimQuarentena = fimQuarentena;
    }

    public boolean isAtiva() {
        return ativa;
    }

    public void setAtiva(boolean ativa) {
        this.ativa = ativa;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public UsuarioVO getUsuarioIniciou() {
        return usuarioIniciou;
    }

    public void setUsuarioIniciou(UsuarioVO usuarioIniciou) {
        this.usuarioIniciou = usuarioIniciou;
    }

    public UsuarioVO getUsuarioEncerrou() {
        return usuarioEncerrou;
    }

    public void setUsuarioEncerrou(UsuarioVO usuarioEncerrou) {
        this.usuarioEncerrou = usuarioEncerrou;
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }
}

package negocio.comuns.crm;

import negocio.comuns.utilitarias.Uteis;

import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Date;

public class FasesCRMSQL {

    public static String sqlSessoesFinais(int colaborador, int nrSessoesFaltantes, int empresa, boolean count, String codigoClientesObjecoes) {
        //selecionar todos os clientes do colaborador x que tenha y sessões a fazer ainda
        //e que compraram no mais sessões do y

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        if (count) {
            sql.append(" count (distinct cli.codigo) as meta FROM vendaavulsa  va \n");
        } else {
            sql.append(" cli.codigo as cliente, max(va.codigo) as vendaavulsa FROM vendaavulsa  va \n");
        }
        sql.append(" INNER JOIN itemvendaavulsa iva ON va.codigo = iva.vendaavulsa \n");
        sql.append(" INNER JOIN produto p ON p.codigo = iva.produto AND p.tipoproduto = 'SS' AND iva.quantidade >= ").append(nrSessoesFaltantes).append(" \n");
        sql.append(" INNER JOIN cliente cli ON cli.codigo = va.cliente \n");
        sql.append(" INNER JOIN vinculo v ON v.cliente = cli.codigo AND v.colaborador = ").append(colaborador).append(" \n");
        sql.append(" WHERE (SELECT COUNT(ag.id_agenda) FROM sch_estudio.agenda ag  \n");
        sql.append(" INNER JOIN sch_estudio.agenda_venda av ON av.id_agenda = ag.id_agenda \n");
        sql.append(" WHERE av.id_vendaavulsa = va.codigo AND ag.status IN ('B','C') AND ag.id_produto = p.codigo) = ").append(nrSessoesFaltantes).append(" \n");
        sql.append(" AND NOT EXISTS(SELECT * FROM fecharmetadetalhado WHERE cliente = cli.codigo AND sessoesfinais = ").append(nrSessoesFaltantes).append(" AND vendaavulsa = va.codigo) \n");
        sql.append(" AND cli.empresa = ").append(empresa);
        sql.append(" AND va.cliente not in (").append(codigoClientesObjecoes).append(")");
        
        if (!count) {
            sql.append(" GROUP BY cli.codigo \n");
        }
        return sql.toString();
    }

    public static String sqlSemAgendamento(Date data, int colaborador, ConfiguracaoDiasMetasTO nrSemAgendamento, int empresa, boolean count, String codigoClientesObjecoes) throws Exception {
        //selecionar todos os clientes do colaborador x em que o último agendamento tenha sido a y dias
        // e que não tenham algum agendamento em aberto
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        if (count) {
            sql.append(" count (distinct cli.codigo) as meta FROM cliente cli \n");
        } else {
            sql.append(" cli.codigo as cliente FROM cliente cli \n");
        }
        sql.append(" INNER JOIN vinculo vi ON vi.cliente = cli.codigo AND vi.colaborador = ").append(colaborador).append(" \n");
        sql.append(" WHERE 1 = 1\n");
        sql.append(" AND (SELECT\n");
        sql.append("             extract(DAY FROM (CAST('").append(Uteis.getDataJDBC(data)).append("' AS TIMESTAMP) - max(dataregistro))) AS nrdias\n");
        sql.append("           FROM sch_estudio.agenda_agendar aa\n");
        sql.append("             INNER JOIN vendaavulsa va\n");
        sql.append("               ON aa.id_vendaavulsa = va.codigo\n");
        sql.append("             INNER JOIN itemvendaavulsa iva");
        sql.append("               ON va.codigo = iva.vendaavulsa");
        sql.append("             LEFT JOIN sch_estudio.agenda_venda av\n");
        sql.append("               ON av.id_vendaavulsa = va.codigo\n");
        sql.append("             LEFT JOIN sch_estudio.agenda a\n");
        sql.append("               ON a.id_agenda = av.id_agenda\n");
        sql.append("           WHERE 1 = 1 and iva.produto = ").append(nrSemAgendamento.getProduto().getCodigo());
        sql.append("               AND aa.id_cliente = cli.codigo) = ").append(nrSemAgendamento.getNrDia()).append("\n");
        sql.append(" AND cli.empresa = ").append(empresa).append("\n");
        sql.append(" AND cli.codigo not in (").append(codigoClientesObjecoes).append(")");

        return sql.toString();
    }

    public static String sqlExAlunos(Date dia, int colaborador, ConfiguracaoDiasMetasTO tempoExAluno, int empresa, boolean count, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        if (count) {
            sql.append("  count(DISTINCT (scsdw.codigocontrato))\n");
        } else {
            sql.append(" scsdw.codigocliente as cliente \n");
        }
        sql.append("FROM situacaoclientesinteticodw scsdw\n");
        sql.append("  INNER JOIN vinculo ON vinculo.cliente = scsdw.codigocliente AND vinculo.colaborador = '").append(colaborador).append("'\n");
        if (!tipoVinculos.equals("TODOS")){
            sql.append(" and vinculo.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        sql.append("  INNER JOIN cliente ON scsdw.codigocliente = cliente.codigo\n");
        sql.append("WHERE 1 = 1\n");
        sql.append("      AND scsdw.codigocliente not in (").append(codigoClientesObjecoes).append(")");
        sql.append("      AND (scsdw.situacaocontrato LIKE 'CA' OR scsdw.situacaocontrato LIKE 'DE')\n");
        sql.append("      AND cast(scsdw.datavigenciaateajustada AS DATE) = (SELECT cast('").append(Uteis.getDataJDBC(dia)).append("' AS DATE) - INTERVAL '").append(tempoExAluno.getNrDia()).append(" days' AS date)\n");
        sql.append("      AND cliente.empresa = ").append(empresa).append(";");
        return sql.toString();
    }

    public static String sqlUltimoAcessoGympass(Date dia, int colaborador, ConfiguracaoDiasMetasTO tempoGympass, int empresa, boolean count, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        StringBuilder sql = new StringBuilder();
            sql.append("SELECT\n");
            if (count) {
                sql.append(" count(DISTINCT (sdw.codigocliente))\n");
            } else {
                sql.append(" DISTINCT (sdw.codigocliente) AS cliente, \n");
                sql.append("'CLIENTE'           AS origem, \n");
                sql.append("sdw.codigocliente   AS codigoOrigem \n");
            }
            sql.append("FROM situacaoclientesinteticodw sdw \n");
            sql.append("INNER JOIN pessoa pes ON pes.codigo = sdw.codigopessoa AND sdw.empresacliente = ").append(empresa).append("\n");
            sql.append("INNER JOIN periodoacessocliente pr ON pr.pessoa = sdw.codigopessoa \n");
            sql.append("INNER JOIN vinculo v ON v.cliente = sdw.codigocliente ").append(" AND v.colaborador = ").append(colaborador).append("\n");
            if (!tipoVinculos.equals("TODOS")) {
                sql.append(" AND v.tipovinculo in (").append(tipoVinculos).append(") \n");
            }
            sql.append("WHERE 1 = 1 \n");
            sql.append("AND coalesce(pr.tokengympass, '') <> ''");
            sql.append("AND pr.tipoacesso = 'PL' \n");
            sql.append("AND pr.datafinalacesso::date = ").append("(SELECT cast('").append(Uteis.getDataJDBC(dia)).append("' AS DATE) - INTERVAL '").append(tempoGympass.getNrDia()).append(" days' AS date)");
            sql.append("AND datafinalacesso::date = (select max(datafinalacesso) from periodoacessocliente p  where  p.pessoa  = sdw.codigopessoa   AND tipoacesso = 'PL' ) ");
            sql.append("AND sdw.codigocliente not in (").append(codigoClientesObjecoes).append(")");


        return sql.toString();
    }

    public static String sqlFilaEsperaTurmaCRM(Date dia, int colaborador, int empresa, boolean count, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        if (count) {
            sql.append(" count(DISTINCT (coalesce(f.cliente, f.passivo)))\n");
        } else {
            sql.append(" DISTINCT (coalesce(f.cliente, f.passivo)) AS clientePassivo, \n");
            sql.append("sdw.codigocliente   AS codigoClienteOrigem, \n");
            sql.append("ps.codigo   AS codigoPassivoOrigem, \n");
            sql.append("f.codigo as fila, \n");
            sql.append("ht.codigo as codigoTurma, \n");
            sql.append("ht.identificadorturma, \n");
            sql.append("ht.horainicial, \n");
            sql.append("ht.horafinal, \n");
            sql.append("ht.nrmaximoaluno, \n");
            sql.append("(select count(codigo) from matriculaalunohorarioturma where datafim >= '").append(Uteis.getDataJDBC(dia)).append("' and horarioturma = ht.codigo) as ocupacao, \n");
            sql.append("f.ordem \n");
        }
        sql.append("from horarioturma ht \n");
        sql.append("inner join turma t on t.codigo = ht.turma").append("\n");
        sql.append("inner join filaesperaturmacrm f on f.horarioturma = ht.codigo").append("\n");
        sql.append("left join passivo ps ON ps.codigo = f.passivo").append("\n");
        sql.append("left join cliente c ON c.codigo = f.cliente").append("\n");
        sql.append("left join pessoa p on p.codigo = c.pessoa").append("\n");
        sql.append("left join situacaoclientesinteticodw sdw on sdw.codigopessoa = p.codigo and sdw.empresacliente = ").append(empresa).append("\n");
        sql.append("left join vinculo v ON v.cliente = sdw.codigocliente").append("\n");
        if (!tipoVinculos.equals("TODOS")) {
            sql.append(" AND (v.tipovinculo is null or (v.tipovinculo in (").append(tipoVinculos).append("))) \n");
        }
        sql.append("WHERE 1 = 1 \n");
        sql.append("and t.datainicialvigencia <= '").append(Uteis.getDataJDBC(dia)).append("' and t.datafinalvigencia >= '").append(Uteis.getDataJDBC(dia)).append("'\n");
        sql.append("and ht.situacao = 'AT' and ht.ativo = true").append("\n");
        sql.append("and ((select count(codigo) from matriculaalunohorarioturma where datafim >= '").append(Uteis.getDataJDBC(dia)).append("' and horarioturma = ht.codigo) < ht.nrmaximoaluno)").append("\n");
        sql.append("and (sdw.codigocliente is null or (sdw.codigocliente not in (").append(codigoClientesObjecoes).append(")))").append("\n");
        if (!count) {
            sql.append("order by f.ordem");
        }


        return sql.toString();
    }

    public static String sqlGrupoRisco(Date dia, Integer nrRisco, int colaborador, int empresa, boolean count,String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        if (count) {
            sql.append("  count(DISTINCT(x1.cliente))\n");
        } else {
            sql.append("  DISTINCT(x1.cliente) AS cliente, 'CLIENTE' AS origem, x1.cliente AS codigoOrigem, x1.peso\n");
        }
        sql.append("FROM (SELECT rs.cliente,rs.peso FROM risco rs \n");
        sql.append("         INNER JOIN cliente ON cliente.codigo = rs.cliente AND cliente.situacao = 'AT' AND cliente.empresa = ").append(empresa).append(" \n");
        sql.append("         INNER JOIN vinculo ON vinculo.cliente = cliente.codigo AND vinculo.colaborador = ").append(colaborador).append(" \n");
        if (!tipoVinculos.equals("TODOS")){
            sql.append(" and vinculo.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        sql.append("       WHERE rs.peso > ").append(nrRisco).append("      AND cliente.codigo not in (").append(codigoClientesObjecoes).append(")").append(") x1  \n");
        sql.append("WHERE NOT EXISTS(\n");
        sql.append("    SELECT fmd.cliente, fmd.pesorisco from fecharmetadetalhado fmd\n");
        sql.append("      INNER JOIN fecharmeta fm ON fmd.fecharmeta = fm.codigo AND fm.identificadormeta = 'RI'\n");
        sql.append("                                  AND fm.dataregistro BETWEEN '").append(Uteis.getDataJDBC(Uteis.obterDataAnterior(dia, 7))).append("' \n");
        sql.append("                                  AND '").append(Uteis.getDataJDBC(dia)).append("'\n");
        sql.append("    WHERE cliente = x1.cliente AND pesorisco >= x1.peso) \n");
        sql.append("      AND (SELECT COUNT(*) FROM contratooperacao co \n");
        sql.append("  INNER JOIN contrato c ON c.codigo = co.contrato \n");
        sql.append("  INNER JOIN cliente cli ON cli.pessoa = c.pessoa \n");
        sql.append("WHERE co.tipooperacao IN ('TR', 'CR', 'AT') \n");
        sql.append("      AND cli.codigo = x1.cliente \n");
        sql.append("      AND '").append(Uteis.getDataJDBC(dia)).append("' BETWEEN datainicioefetivacaooperacao AND datafimefetivacaooperacao  ) <= 0");
        return sql.toString();
    }

    public static String sqlPosVenda(Integer nrDia, Integer codColaborador, Date dia, Integer empresa, boolean incluirContratosRenovados, boolean count, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT\n");
        if (count) {
            sql.append("  count(DISTINCT (situacaoclientesinteticodw.codigocontrato))\n");
        } else {
            sql.append("DISTINCT\n");
            sql.append("  (situacaoclientesinteticodw.codigocontrato) AS codigoOrigem,\n");
            sql.append("  (vinculo.cliente)                           AS cliente,\n");
            sql.append("  'CONTRATO'                                  AS origem\n");
        }
        sql.append("FROM situacaoclientesinteticodw\n");
        sql.append("  INNER JOIN vinculo ON vinculo.cliente = situacaoclientesinteticodw.codigocliente AND vinculo.colaborador = '").append(codColaborador).append("'\n");
        if (!tipoVinculos.equals("TODOS")){
            sql.append(" and vinculo.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        sql.append("  INNER JOIN contrato ON contrato.codigo = situacaoclientesinteticodw.codigocontrato ");
        if (!incluirContratosRenovados) {
            sql.append("  AND contrato.situacaocontrato NOT LIKE 'RN'\n");
        }
        sql.append("  INNER JOIN cliente ON situacaoclientesinteticodw.codigocliente = cliente.codigo\n");
        sql.append("WHERE ( situacaoclientesinteticodw.situacao = 'AT'\n");
//        sql.append("      AND situacaoclientesinteticodw.situacaoContrato = 'NO'\n");
        sql.append("AND ((contrato.situacaocontrato NOT LIKE 'RN' AND\n");
        sql.append("Cast(situacaoclientesinteticodw.datavigenciade AS DATE) = (SELECT\n");
        sql.append("cast('").append(Uteis.getDataJDBC(dia)).append("' AS DATE) - INTERVAL '").append(nrDia).append(" days' AS date))\n");
        if (incluirContratosRenovados) {
            sql.append("OR (contrato.situacaocontrato LIKE 'RN' AND\n");
            sql.append("Cast(situacaoclientesinteticodw.datavigenciade AS DATE) = (SELECT\n");
            sql.append("cast('").append(Uteis.getDataJDBC(dia)).append("' AS DATE) - INTERVAL '").append(nrDia).append(" days' AS date))\n");
        }
        sql.append(")      AND cliente.empresa = ").append(empresa).append(") AND cliente.codigo not in (").append(codigoClientesObjecoes).append(")").append("\n");

        return sql.toString();
    }

    public static String sqlVisitantesAntigos(Date dia, int colaborador, ConfiguracaoDiasMetasTO tempoConfigurado, int empresa, boolean count,String tipoVinculos, String codigoPessoasObjecoes) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT\n");
        if (count) {
            sql.append("  count(DISTINCT (sdw.codigocliente))\n");
        } else {
            sql.append("DISTINCT\n");
            sql.append("  (sdw.codigocliente)       AS cliente,\n");
            sql.append("  (sdw.codigocliente)       AS codigoOrigem,\n");
            sql.append("  'CLIENTE'                 AS origem\n");
        }
        sql.append("FROM situacaoclientesinteticodw sdw\n");
        sql.append("  INNER JOIN pessoa pes\n");
        sql.append("    ON pes.codigo = sdw.codigopessoa AND sdw.empresacliente = ").append(empresa).append(" \n");
        sql.append("  INNER JOIN vinculo v\n");
        sql.append("    ON v.cliente = sdw.codigocliente \n");
        if (!tipoVinculos.equals("TODOS")){
            sql.append(" AND v.tipovinculo in (").append(tipoVinculos).append(") \n");
        }
        sql.append("       AND v.colaborador = ").append(colaborador).append("\n");
        sql.append("       AND CAST(pes.datacadastro AS DATE) = \n");
        sql.append("    CAST((SELECT CAST('").append(Uteis.getDataJDBC(dia)).append("' AS DATE) - INTERVAL '").append(tempoConfigurado.getNrDia()).append(" DAY' AS DATA) AS DATE)\n");
        sql.append("       AND (SELECT count(codigo) FROM agenda ag WHERE ag.cliente = v.cliente AND dataagendamento > now()) = 0");
        sql.append("       AND (SELECT count(codigo) FROM contrato con WHERE con.pessoa = pes.codigo AND vigenciade > now()) = 0 ");
        sql.append("WHERE (sdw.situacao = 'VI')");
        sql.append(" AND pes.codigo not in (").append(codigoPessoasObjecoes).append(")");
        return sql.toString();
    }
}

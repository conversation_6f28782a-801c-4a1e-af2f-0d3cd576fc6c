package negocio.comuns.crm;

import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;

public class ClientePotencialVO extends SuperVO {

    private PassivoVO passivoVO;
    private IndicadoVO indicadoVO;
    private ClienteVO clienteVO;
    private String telefone;
    private String tipoPessoa;
    private String dataNasc;

    /**
     * @return the tipoPessoa
     */
    public String getTipoPessoa() {
        if (tipoPessoa == null) {
            tipoPessoa = "";
        }
        if (tipoPessoa.equals("PA")) {
            return "Passivo";
        }
        if (tipoPessoa.equals("IN")) {
            return "Indicado";
        }
        if (tipoPessoa.equals("CL")) {
            return "Cliente";
        }
        return tipoPessoa;
    }

    /**
     * @param tipoPessoa the tipoPessoa to set
     */
    public void setTipoPessoa(String tipoPessoa) {
        this.tipoPessoa = tipoPessoa;
    }

    /**
     * @return the telefone
     */
    public String getTelefone() {
        if (telefone == null) {
            telefone = "";
        }
        return telefone;
    }

    /**
     * @param telefone the telefone to set
     */
    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    /**
     * @return the clienteVO
     */
    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    /**
     * @param clienteVO the clienteVO to set
     */
    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    /**
     * @return the passivoVO
     */
    public PassivoVO getPassivoVO() {
        if (passivoVO == null) {
            passivoVO = new PassivoVO();
        }
        return passivoVO;
    }

    /**
     * @param passivoVO the passivoVO to set
     */
    public void setPassivoVO(PassivoVO passivoVO) {
        this.passivoVO = passivoVO;
    }

    /**
     * @return the indicadoVO
     */
    public IndicadoVO getIndicadoVO() {
        if (indicadoVO == null) {
            indicadoVO = new IndicadoVO();
        }
        return indicadoVO;
    }

    /**
     * @param indicadoVO the indicadoVO to set
     */
    public void setIndicadoVO(IndicadoVO indicadoVO) {
        this.indicadoVO = indicadoVO;
    }

    public Boolean getIsApresentarPassivo() {
        if (getPassivoVO().getCodigo().intValue() != 0) {
            return true;
        }

        return false;
    }

    public Boolean getIsApresentarIndicado() {
        if (getIndicadoVO().getCodigo().intValue() != 0) {
            return true;
        }
        return false;
    }

    public Boolean getIsApresentarCliente() {
        if (getClienteVO().getCodigo().intValue() != 0) {
            return true;
        }
        return false;
    }

    public String getDataNasc() {
        return dataNasc;
    }

    public void setDataNasc(String dataNasc) {
        this.dataNasc = dataNasc;
    }
}

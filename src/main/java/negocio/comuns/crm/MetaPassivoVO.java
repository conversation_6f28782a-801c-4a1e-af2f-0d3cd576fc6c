
package negocio.comuns.crm;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class MetaPassivoVO {
    private List<GrupoColaboradorVO> grupoColaboradorVos;
    private Integer totalizadorPassivo;
    


    public MetaPassivoVO(){
        inicializarDados();
    }

    public void inicializarDados(){
        setGrupoColaboradorVos(new ArrayList());
        setTotalizadorPassivo(new Integer(0));
        
    }
    
  
    /**
     * @return the totalizadorPassivo
     */
    public Integer getTotalizadorPassivo() {
        return totalizadorPassivo;
    }

    /**
     * @param totalizadorPassivo the totalizadorPassivo to set
     */
    public void setTotalizadorPassivo(Integer totalizadorPassivo) {
        this.totalizadorPassivo = totalizadorPassivo;
    }

    /**
     * @return the grupoColaboradorVos
     */
    public List<GrupoColaboradorVO> getGrupoColaboradorVos() {
        return grupoColaboradorVos;
    }

    /**
     * @param grupoColaboradorVos the grupoColaboradorVos to set
     */
    public void setGrupoColaboradorVos(List<GrupoColaboradorVO> grupoColaboradorVos) {
        this.grupoColaboradorVos = grupoColaboradorVos;
    }

  

}

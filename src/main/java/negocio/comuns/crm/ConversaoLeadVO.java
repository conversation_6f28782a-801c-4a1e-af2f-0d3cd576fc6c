/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.crm;

import java.util.Date;

import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;


/**
 *
 * <AUTHOR>
 */
public class ConversaoLeadVO extends SuperVO {
    
    private Integer codigo = 0;
    private LeadVO lead = new LeadVO();
    private ContratoVO contrato = new ContratoVO();
    private ObjecaoVO objecao = new ObjecaoVO();
    private AgendaVO agendamento = new AgendaVO();
    private String identificador = "";
    private Date dataCriacao;
    private Date dataLancamento;
    private boolean tratada = false;
    private String props = "";
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    private UsuarioVO responsavel = new UsuarioVO();

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public LeadVO getLead() {
        return lead;
    }

    public void setLead(LeadVO lead) {
        this.lead = lead;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public ObjecaoVO getObjecao() {
        return objecao;
    }

    public void setObjecao(ObjecaoVO objecao) {
        this.objecao = objecao;
    }

    public AgendaVO getAgendamento() {
        return agendamento;
    }

    public void setAgendamento(AgendaVO agendamento) {
        this.agendamento = agendamento;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public boolean isTratada() {
        return tratada;
    }

    public void setTratada(boolean tratada) {
        this.tratada = tratada;
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }

    public String getProps() {
        return props;
    }

    public void setProps(String props) {
        this.props = props;
    }

    public String getMensagem() {
        try {
            JSONObject objProps = new JSONObject(getProps());
            return objProps.getString("mensagem");
        } catch (Exception ignored) {
        }
        return "";
    }
}

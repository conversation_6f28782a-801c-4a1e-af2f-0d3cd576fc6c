/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class AgendaTotalTO {
    
    private Integer empresa;
    private Integer ocupacao;
    private String inicio;
    private String fim;
    private String titulo;
    private String id;
    private String responsavel;
    private Integer codigoResponsavel;
    private Date inicioVigencia;
    private Date fimVigencia;
    private String diaSemana;
    
    private String tipo;
    private Integer codigoTipo;
    
    private String local;
    private Integer codigoLocal;
    
    private Integer nrVagas;
    private Integer nrVagasPreenchidas;
    private String nivel;
    private Integer codigoNivel;
    
    private String cor;
    private Boolean aulaCheia;
    private Boolean permitirAulaExperimental;

    private Integer tolerancia;
    private String mensagem;
    private Double bonificacao;
    private Integer pontosBonus;
    private Double meta;
    
    //aqui é só pros casos do retorno do aula cheia ao adicionar aluno
    private Integer aulasExperimentais;
    private Integer matricula;
    private String nomeAluno;
    private boolean jaMarcouEuQuero;
    private String fotoProfessor;
    private Integer codigoMatriculaAlunoHorarioTurma;
    private Integer turma;

    public AgendaTotalTO() {
    }
    
    public AgendaTotalTO(Date data, AgendaTotalTO agenda) {
        this.empresa = agenda.empresa;
        this.inicio = Uteis.getData(data) + " "+agenda.inicio;
        this.fim = Uteis.getData(data) + " "+agenda.fim;
        this.titulo = agenda.titulo;
        this.responsavel = agenda.responsavel;
        this.codigoResponsavel = agenda.codigoResponsavel;
        this.tipo = agenda.tipo;
        this.codigoTipo = agenda.codigoTipo;
        this.local = agenda.local;
        this.codigoLocal = agenda.codigoLocal;
        this.nivel = agenda.nivel;
        this.codigoNivel = agenda.codigoNivel;
        this.nrVagas = agenda.nrVagas;
        this.inicioVigencia= agenda.inicioVigencia;
        this.fimVigencia= agenda.fimVigencia;
        this.nrVagasPreenchidas = agenda.nrVagasPreenchidas;
        this.cor = agenda.cor;
        this.id = agenda.id;
        this.aulaCheia = agenda.aulaCheia;
        this.permitirAulaExperimental = agenda.permitirAulaExperimental;
        this.tolerancia = agenda.tolerancia;
        this.mensagem = agenda.mensagem;
        this.bonificacao = agenda.bonificacao;
        this.pontosBonus = agenda.pontosBonus;
        this.meta = agenda.meta;
        this.jaMarcouEuQuero = agenda.jaMarcouEuQuero;
        this.diaSemana = agenda.diaSemana;
        this.ocupacao = agenda.ocupacao;
        this.fotoProfessor = agenda.fotoProfessor;
        this.codigoMatriculaAlunoHorarioTurma = agenda.codigoMatriculaAlunoHorarioTurma;
        this.turma = agenda.turma;
    }
    

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public Integer getCodigoResponsavel() {
        return codigoResponsavel;
    }

    public void setCodigoResponsavel(Integer codigoResponsavel) {
        this.codigoResponsavel = codigoResponsavel;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public Integer getCodigoLocal() {
        return codigoLocal;
    }

    public void setCodigoLocal(Integer codigoLocal) {
        this.codigoLocal = codigoLocal;
    }

    public Integer getNrVagas() { 
        return nrVagas;
    }

    public void setNrVagas(Integer nrVagas) {
        this.nrVagas = nrVagas;
    }

    public Integer getNrVagasPreenchidas() {
        return nrVagasPreenchidas;
    }

    public void setNrVagasPreenchidas(Integer nrVagasPreenchidas) {
        this.nrVagasPreenchidas = nrVagasPreenchidas;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getCodigoTipo() {
        return codigoTipo;
    }

    public void setCodigoTipo(Integer codigoTipo) {
        this.codigoTipo = codigoTipo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public Integer getCodigoNivel() {
        return codigoNivel;
    }

    public void setCodigoNivel(Integer codigoNivel) {
        this.codigoNivel = codigoNivel;
    }

    public Date getInicioVigencia() {
        return inicioVigencia;
    }

    public void setInicioVigencia(Date inicioVigencia) {
        this.inicioVigencia = inicioVigencia;
    }

    public Date getFimVigencia() {
        return fimVigencia;
    }

    public void setFimVigencia(Date fimVigencia) {
        this.fimVigencia = fimVigencia;
    }

    public Boolean getAulaCheia() {
        return aulaCheia;
    }

    public void setAulaCheia(Boolean aulaCheia) {
        this.aulaCheia = aulaCheia;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public Integer getTolerancia() {
        return tolerancia;
    }

    public void setTolerancia(Integer tolerancia) {
        this.tolerancia = tolerancia;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Double getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(Double bonificacao) {
        this.bonificacao = bonificacao;
    }

    public Integer getPontosBonus() {
        return pontosBonus;
    }

    public void setPontosBonus(Integer pontosBonus) {
        this.pontosBonus = pontosBonus;
    }

    public Double getMeta() {
        return meta;
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }

    public Integer getAulasExperimentais() {
        return aulasExperimentais;
    }

    public void setAulasExperimentais(Integer aulasExperimentais) {
        this.aulasExperimentais = aulasExperimentais;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public boolean isJaMarcouEuQuero() {
        return jaMarcouEuQuero;
    }

    public void setJaMarcouEuQuero(boolean jaMarcouEuQuero) {
        this.jaMarcouEuQuero = jaMarcouEuQuero;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Boolean getPermitirAulaExperimental() {
        return permitirAulaExperimental;
    }

    public void setPermitirAulaExperimental(Boolean permitirAulaExperimental) {
        this.permitirAulaExperimental = permitirAulaExperimental;
    }

    public String getFotoProfessor() {
        return fotoProfessor;
    }

    public void setFotoProfessor(String fotoProfessor) {
        this.fotoProfessor = fotoProfessor;
    }

    public Integer getCodigoMatriculaAlunoHorarioTurma() {
        return codigoMatriculaAlunoHorarioTurma;
    }

    public void setCodigoMatriculaAlunoHorarioTurma(Integer codigoMatriculaAlunoHorarioTurma) {
        this.codigoMatriculaAlunoHorarioTurma = codigoMatriculaAlunoHorarioTurma;
    }

    public Integer getTurma() {
        return turma;
    }

    public void setTurma(Integer turma) {
        this.turma = turma;
    }
    
    public String getHorario(){
        try {
            return inicio.substring(11) + " - " + fim.substring(11);
        }catch (Exception e){
            return "";
        }
    }

    public String getDataAulaApresentar(){
        try {
            return inicio.substring(0,10);
        }catch (Exception e){
            return "";
        }
    }

    public String getDescricaoAulaApresentar(){
        StringBuilder dados = new StringBuilder("");
        String data = inicio.substring(0,10);
        String horaInicio = inicio.substring(11);
        String horaFim = fim.substring(11);
        dados.append("Faltou a aula do dia ");
        dados.append(data);
        dados.append(" no horario ");
        dados.append(horaInicio.trim());
        dados.append(" ate as ");
        dados.append(horaFim.trim());
        return dados.toString();
    }
    
    public Date getDataAula(){
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        String dataString = inicio.substring(0, 10);
        Date data = Calendario.hoje();
        try {
            data = sdf.parse(dataString);
        } catch (ParseException ex) {
        }
        
        return data;
    }
}

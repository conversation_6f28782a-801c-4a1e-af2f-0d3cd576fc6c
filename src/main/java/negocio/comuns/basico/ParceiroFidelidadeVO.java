package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoParceiroEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * <AUTHOR>
 */
public class ParceiroFidelidadeVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    @ChaveEstrangeira
    private EmpresaVO empresa;
    private String clientID;
    private String clientSecret;
    private String clientIDRedemption;
    private String clientSecretRedemption;
    private String codigoLoja;
    private String codigoMaquina;
    private String codigoOferta;
    private String codigoResgate;
    private String tags;
    private String token;
    private Date dataExpiracaoToken;
    private boolean validarCliente = false;
    private String cpf;
    private boolean ambienteProducao = false;
    private boolean parcelaVencidaGeraPonto = false;

    private TipoParceiroEnum tipoParceiro = TipoParceiroEnum.DOTZ;
    @NaoControlarLogAlteracao
    private List<TabelaParceiroFidelidadeVO> itens;
    @NaoControlarLogAlteracao
    private List<ProdutoParceiroFidelidadeVO> produtos;
    @NaoControlarLogAlteracao
    private ProdutoParceiroFidelidadeVO produtoAdicionar;


    public ParceiroFidelidadeVO() {
        super();
    }

    public ParceiroFidelidadeVO(EmpresaVO empresa, TipoParceiroEnum tipoParceiro) {
        this.empresa = empresa;
        this.tipoParceiro = tipoParceiro;
    }

    public static void validarDados(ParceiroFidelidadeVO obj) throws ConsistirException {
        if (obj.getValidarDados()) {
            if (UteisValidacao.emptyString(obj.getClientID())) {
                throw new ConsistirException("O campo Client ID (Rewards) (ParceiroFidelidade) deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getClientSecret())) {
                throw new ConsistirException("O campo Client Secret (Rewards) (ParceiroFidelidade) deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getClientIDRedemption())) {
                throw new ConsistirException("O campo Client ID (Redemption) (ParceiroFidelidade) deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getClientSecretRedemption())) {
                throw new ConsistirException("O campo Client Secret (Redemption) (ParceiroFidelidade) deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getCodigoLoja())) {
                throw new ConsistirException("O campo Código Loja (StoreCode) (ParceiroFidelidade) deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getCodigoMaquina())) {
                throw new ConsistirException("O campo Código Máquina (DeviceCode) (ParceiroFidelidade) deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getCodigoOferta())) {
                throw new ConsistirException("O campo Código Oferta (OfferCode) (ParceiroFidelidade) deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getCodigoResgate())) {
                throw new ConsistirException("O campo Código Resgate (locationId) (ParceiroFidelidade) deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getTags())) {
                throw new ConsistirException("O campo Tags (ParceiroFidelidade) deve ser informado.");
            }
            if (UteisValidacao.valorNulo(obj, "empresa") || UteisValidacao.valorNulo(obj.getEmpresa(), "codigo")) {
                throw new ConsistirException("O campo EMPRESA (ParceiroFidelidade) deve ser informado.");
            }
            if (!UteisValidacao.emptyString(obj.getCpf()) && !SuperVO.verificaCPF(obj.getCpf())) {
                throw new ConsistirException("O CPF (ParceiroFidelidade) informado não é válido.");
            }
            if (UteisValidacao.emptyList(obj.getItens())) {
                throw new ConsistirException("Adicione uma tabela para acumular (ParceiroFidelidade).");
            }
        }
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public TipoParceiroEnum getTipoParceiro() {
        return tipoParceiro;
    }

    public void setTipoParceiro(TipoParceiroEnum tipoParceiro) {
        this.tipoParceiro = tipoParceiro;
    }

    @Override
    public Integer getCodigo() {
        return (codigo);
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List<TabelaParceiroFidelidadeVO> getItens() {
        if (itens == null) {
            itens = new ArrayList();
        }
        return itens;
    }

    public void setItens(List<TabelaParceiroFidelidadeVO> itens) {
        this.itens = itens;
    }

    public void addItem(TabelaParceiroFidelidadeVO item) {
        getItens().add(item);
    }

    public void removeItem(TabelaParceiroFidelidadeVO item) {
        getItens().remove(item);
    }

    public String getClientID() {
        if (clientID == null) {
            clientID = "";
        }
        return clientID;
    }

    public void setClientID(String clientID) {
        this.clientID = clientID;
    }

    public String getClientSecret() {
        if (clientSecret == null) {
            clientSecret = "";
        }
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getCodigoLoja() {
        if (codigoLoja == null) {
            codigoLoja = "";
        }
        return codigoLoja;
    }

    public void setCodigoLoja(String codigoLoja) {
        this.codigoLoja = codigoLoja;
    }

    public String getCodigoMaquina() {
        if (codigoMaquina == null) {
            codigoMaquina = "";
        }
        return codigoMaquina;
    }

    public void setCodigoMaquina(String codigoMaquina) {
        this.codigoMaquina = codigoMaquina;
    }

    public String getCodigoOferta() {
        if (codigoOferta == null) {
            codigoOferta = "";
        }
        return codigoOferta;
    }

    public void setCodigoOferta(String codigoOferta) {
        this.codigoOferta = codigoOferta;
    }

    public List<ProdutoParceiroFidelidadeVO> getProdutos() {
        if (produtos == null) {
            produtos = new ArrayList<ProdutoParceiroFidelidadeVO>();
        }
        return produtos;
    }

    public void setProdutos(List<ProdutoParceiroFidelidadeVO> produtos) {
        this.produtos = produtos;
    }

    public void addProduto(ProdutoParceiroFidelidadeVO produto) {
        getProdutos().add(produto);
    }

    public void removeProduto(ProdutoParceiroFidelidadeVO produto) {
        produto.setDescricao(produto.getDescricao().toUpperCase());
        getProdutos().remove(produto);
    }

    public ProdutoParceiroFidelidadeVO getProdutoAdicionar() {
        if (produtoAdicionar == null) {
            produtoAdicionar = new ProdutoParceiroFidelidadeVO();
        }
        return produtoAdicionar;
    }

    public void setProdutoAdicionar(ProdutoParceiroFidelidadeVO produtoAdicionar) {
        this.produtoAdicionar = produtoAdicionar;
    }

    public String getTags() {
        if (tags == null) {
            tags = "";
        }
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getCodigoResgate() {
        if (codigoResgate == null) {
            codigoResgate = "";
        }
        return codigoResgate;
    }

    public void setCodigoResgate(String codigoResgate) {
        this.codigoResgate = codigoResgate;
    }

    public String getToken() {
        if (token == null) {
            token = "";
        }
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Date getDataExpiracaoToken() {
        return dataExpiracaoToken;
    }

    public void setDataExpiracaoToken(Date dataExpiracaoToken) {
        this.dataExpiracaoToken = dataExpiracaoToken;
    }

    public String getClientIDRedemption() {
        if (clientIDRedemption == null) {
            clientIDRedemption = "";
        }
        return clientIDRedemption;
    }

    public void setClientIDRedemption(String clientIDRedemption) {
        this.clientIDRedemption = clientIDRedemption;
    }

    public String getClientSecretRedemption() {
        if (clientSecretRedemption == null) {
            clientSecretRedemption = "";
        }
        return clientSecretRedemption;
    }

    public void setClientSecretRedemption(String clientSecretRedemption) {
        this.clientSecretRedemption = clientSecretRedemption;
    }

    public boolean isValidarCliente() {
        return validarCliente;
    }

    public void setValidarCliente(boolean validarCliente) {
        this.validarCliente = validarCliente;
    }

    public String getCpf() {
        if (cpf == null) {
            cpf = "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public boolean isAmbienteProducao() {
        return ambienteProducao;
    }

    public void setAmbienteProducao(boolean ambienteProducao) {
        this.ambienteProducao = ambienteProducao;
    }

    public boolean isParcelaVencidaGeraPonto() {
        return parcelaVencidaGeraPonto;
    }

    public void setParcelaVencidaGeraPonto(boolean parcelaVencidaGeraPonto) {
        this.parcelaVencidaGeraPonto = parcelaVencidaGeraPonto;
    }
}

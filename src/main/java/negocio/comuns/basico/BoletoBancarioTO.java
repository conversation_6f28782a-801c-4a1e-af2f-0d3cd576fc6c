package negocio.comuns.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by GlaucoT on 30/04/2015
 */
public class BoletoBancarioTO extends SuperTO {

    private EmpresaVO empresa;
    private PessoaVO sacado;

    private Date dataVencimento = Calendario.hoje();
    private List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();
    private Double valorMultaJuros;

    private Double valorBoleto;

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public String getDataVencimento_Apresentar() {
        return Uteis.getDataAplicandoFormatacao(getDataVencimento(), "dd/MM/yyyy");
    }

    public List<MovParcelaVO> getParcelas() {
        return parcelas;
    }

    public void setParcelas(List<MovParcelaVO> parcelas) {
        this.parcelas = parcelas;
    }

    public PessoaVO getSacado() {
        if (sacado == null) {
            sacado = new PessoaVO();
        }
        return sacado;
    }

    public void setSacado(PessoaVO sacado) {
        this.sacado = sacado;
    }

    public Double getValorParcelas() {
        Double somatorioParcelas = 0.0;
        for (MovParcelaVO parcela : getParcelas()) {
            somatorioParcelas += parcela.getValorParcela();
        }
        return somatorioParcelas;
    }

    public String getValorParcelas_Apresentar() {
        return Formatador.formatarValorMonetario(getValorParcelas());
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }


    public Double getValorMultaJuros() {
        if (valorMultaJuros == null) {
            valorMultaJuros = 0.0;
    }
        return valorMultaJuros;
    }

    public void setValorMultaJuros(Double valorMultaJuros) {
        this.valorMultaJuros = valorMultaJuros;
    }

    public Double getValorBoleto() {
        if (valorBoleto == null) {
            valorBoleto = 0.0;
    }
        return valorBoleto;
}

    public void setValorBoleto(Double valorBoleto) {
        this.valorBoleto = valorBoleto;
    }

    public void calcularValorBoleto() {
        setValorBoleto(getValorParcelas() + getValorMultaJuros());
    }
}

package negocio.comuns.basico;

/**
 * Created by <PERSON> on 29/03/2016.
 */

import br.com.pactosolucoes.comuns.json.SuperJSON;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.basico.enumerador.TagsEnum;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;


public class ItemCampanhaJSON extends SuperJSON {

    private Integer codigo;
    private TagsEnum tag;
    private String texto;
    private String nomeImagem;
    private String urlImagem;
    private Boolean temLink = false;
    private String link;
    private String url;
    private String titulo;
    private Integer idPost;
    private String tagOrdem;
    private String nomeFundo;
    private String urlFundo;
    private String nomeFrente;
    private String urlFrente;
    private Integer ordem;
    private Date atualizadoEm;

    public JSONObject getJSONObject() {
        try {
            JSONObject json = new JSONObject();
            json.put("codigo", codigo);
            json.put("tag", tag.name());
            json.put("texto", texto);
            json.put("nomeImagem", nomeImagem);
            json.put("urlImagem", urlImagem);
            json.put("temLink", temLink);
            json.put("url", url);
            json.put("titulo", titulo);
            json.put("idPost", idPost);
            json.put("tagOrdem", tagOrdem);
            json.put("nomeFundo", nomeFundo);
            json.put("urlFundo", urlFundo);
            json.put("nomeFrente", nomeFrente);
            json.put("urlFrente", urlFrente);
            json.put("ordem", ordem);
            json.put("atualizadoEm", Uteis.getDataAplicandoFormatacao(atualizadoEm, "yyyy-MM-dd'T'HH:mm:ss.SSSZ"));
            return json;
        } catch (JSONException ex) {
            Logger.getLogger(ItemCampanhaJSON.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Date getAtualizadoEm() {
        return atualizadoEm;
    }

    public String getLink() {
        if(link == null){
            link = "";
        }
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }


    public void setAtualizadoEm(Date atualizadoEm) {
        this.atualizadoEm = atualizadoEm;
    }


    public boolean getSlider(){
        return tag != null && tag.equals(TagsEnum.SLIDER);
    }

    public boolean getCaixaPequena(){
        return tag != null && tag.equals(TagsEnum.CAIXAS_PEQUENAS);
    }

    public String getNomeFundo() {
        if(nomeFundo == null){
            nomeFundo = "";
        }
        return nomeFundo;
    }

    public void setNomeFundo(String nomeFundo) {
        this.nomeFundo = nomeFundo;
    }

    public String getUrlFundo() {
        if(urlFundo == null){
            urlFundo = "";
        }
        return urlFundo;
    }

    public void setUrlFundo(String urlFundo) {
        this.urlFundo = urlFundo;
    }

    public String getNomeFrente() {
        if(nomeFrente == null){
            nomeFrente = "";
        }
        return nomeFrente;
    }

    public void setNomeFrente(String nomeFrente) {
        this.nomeFrente = nomeFrente;
    }

    public String getUrlFrente() {
        if(urlFrente == null){
            urlFrente = "";
        }
        return urlFrente;
    }

    public void setUrlFrente(String urlFrente) {
        this.urlFrente = urlFrente;
    }

    public String getTitulo() {
        if(titulo == null){
            titulo = "";
        }
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getTagOrdem() {
        if(tagOrdem == null){
            tagOrdem = "";
        }
        return tagOrdem;
    }

    public void setTagOrdem(String tagOrdem) {
        this.tagOrdem = tagOrdem;
    }

    public ItemCampanhaJSON() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TagsEnum getTag() {
        return tag;
    }

    public void setTag(TagsEnum tag) {
        this.tag = tag;
    }

    public String getTexto() {
        if(texto == null){
            texto = "";
        }
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public String getNomeImagem() {
        if(nomeImagem == null){
            nomeImagem = "";
        }
        return nomeImagem;
    }

    public void setNomeImagem(String nomeImagem) {
        this.nomeImagem = nomeImagem;
    }

    public Boolean getTemLink() {
        if(temLink == null){
            temLink = false;
        }
        return temLink;
    }

    public void setTemLink(Boolean temLink) {
        this.temLink = temLink;
    }

    public String getUrl() {
        if(url == null){
            url = "";
        }
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getIdPost() {
        if(idPost == null){
            idPost = 0;
        }
        return idPost;
    }

    public void setIdPost(Integer idPost) {
        this.idPost = idPost;
    }

    public String getUrlImagem() {
        if(urlImagem == null){
            urlImagem = "";
        }
        return urlImagem;
    }

    public void setUrlImagem(String urlImagem) {
        this.urlImagem = urlImagem;
    }

    @Override
    public String toString() {
        return toJSON();
    }

    public Integer getOrdem() {
        if(ordem == null){
            ordem = 0;
        }
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }


}
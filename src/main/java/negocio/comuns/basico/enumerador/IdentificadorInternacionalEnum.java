/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.enumerador;


public enum IdentificadorInternacionalEnum {
    DEFAULT("","CPF","RG", "CNPJ"),
    BRASIL("BRASIL","CPF","RG", "CNPJ"),
    MOCAMBIQUE("MOÇAMBIQUE","NUIT", "BI", "CNPJ"),
    BOLIVIA("Bolívia","CÉDULA DE IDENTIDAD", "NIT", "CNPJ"),
    GUIANA("Guiana","", "", ""),
    URUGUAY("URUGUAY","NUMERO DE IDENTIFICACION", "IDENTIDAD", "RUT"),
    EUA("ESTADOS UNIDOS","SOCIAL SECURITY", "", "");


    private String nomePais;
    private String identificadorPrimario;
    private String identificadorSecundadrio;
    private String identificadorTerciario;

    IdentificadorInternacionalEnum(String nomePais, String identificadorPrimario, String identificadorSecundadrio, String identificadorTerciario) {
        this.nomePais = nomePais;
        this.identificadorPrimario = identificadorPrimario;
        this.identificadorSecundadrio = identificadorSecundadrio;
        this.identificadorTerciario = identificadorTerciario;
    }

    public String getNomePais() { return nomePais; }

    public String getIdentificadorPrimario() {
        return identificadorPrimario;
    }

    public String getIdentificadorSecundadrio() {
        return identificadorSecundadrio;
    }
    public String getIdentificadorTerciario() {
        return identificadorTerciario;
    }
}

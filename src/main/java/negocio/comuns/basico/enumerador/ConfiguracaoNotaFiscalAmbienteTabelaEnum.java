package negocio.comuns.basico.enumerador;

import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.notaFiscal.AmbienteEmissaoNotaFiscalEnum;

import java.util.ArrayList;
import java.util.List;

public enum ConfiguracaoNotaFiscalAmbienteTabelaEnum {

    COLUNA_CODIGO(0, Integer.class, "codigo", true),
    COLUNA_CONFIGURACAONOTAFISCAL(2, ConfiguracaoNotaFiscalVO.class, "configuracaoNotaFiscal", false),
    COLUNA_AMBIENTEEMISSAO(3, AmbienteEmissaoNotaFiscalEnum.class, "ambienteEmissao", false),
    COLUNA_SERIENFE(4, String.class, "serieNFe", false),
    COLUNA_SEQUENCIALNFE(5, Integer.class, "sequencialNFe", false),
    COLUNA_SEQUENCIALLOTENFE(6, Integer.class, "sequencialLoteNFe", false),
    COLUNA_USUARIOACESSOPROVEDOR(7, String.class, "usuarioAcessoProvedor", false),
    COLUNA_SENHAACESSOPROVEDOR(8, String.class, "senhaAcessoProvedor", false),
    COLUNA_TOKENACESSOPROVEDOR(9, String.class, "tokenAcessoProvedor", false),
    COLUNA_IDCSC(10, String.class, "idCSC", false),
    COLUNA_CSC(11, String.class, "csc", false),
    ;

    public static String NOME_TABELA = "ConfiguracaoNotaFiscalAmbiente";

    private Integer codigo;
    private Object tipo;
    private String descricao;
    private boolean chavePrimaria;

    private ConfiguracaoNotaFiscalAmbienteTabelaEnum(Integer codigo, Object tipo,
                                                     String descricao, boolean chavePrimaria) {
        this.codigo = codigo;
        this.tipo = tipo;
        this.descricao = descricao;
        this.chavePrimaria = chavePrimaria;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Object getTipo() {
        return tipo;
    }

    public void setTipo(Object tipo) {
        this.tipo = tipo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(boolean chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public static String obterChavePrimaria() {
        for (ConfiguracaoNotaFiscalAmbienteTabelaEnum col : ConfiguracaoNotaFiscalAmbienteTabelaEnum.values()) {
            if (col.isChavePrimaria()) {
                return col.getDescricao();
            }
        }
        return null;
    }

    public static List<String> obterListaColunas() {
        List<String> listaColunas = new ArrayList<String>();
        for (ConfiguracaoNotaFiscalAmbienteTabelaEnum col : ConfiguracaoNotaFiscalAmbienteTabelaEnum.values()) {
            if (!col.isChavePrimaria()) {
                listaColunas.add(col.getDescricao());
            }
        }
        return listaColunas;
    }

}

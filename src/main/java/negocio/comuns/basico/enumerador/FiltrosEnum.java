package negocio.comuns.basico.enumerador;

/**
 * Created by alcides on 09/11/2017.
 */
public enum FiltrosEnum {
    FLUXO_CAIXA_CONTAS_REALIZADO(1),
    FLUXO_CAIXA_CONTAS_PREVISTO(2),
    DRE_CONTAS(3),
    DF_CONTAS(4),
    FLUXO_CAIXA_CONTAS_REALIZADO_SALDO_INICIAL(5),
    FLUXO_CAIXA_CONTAS_PREVISTO_SALDO_INICIAL(6)
    ;

    private Integer codigo;

    FiltrosEnum(Integer codigo) {
        this.codigo = codigo;
    }

    public static FiltrosEnum obterPorCodigo(int codigo){
        for(FiltrosEnum f : FiltrosEnum.values()){
            if(f.codigo == codigo){
                return f;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }
}

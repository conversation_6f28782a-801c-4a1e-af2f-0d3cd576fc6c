package negocio.comuns.basico.enumerador;

public enum CausaSugestaoEnum {

    PAGAMENTO_CONJUNTO(0),
    MESMO_CARTAO(1);

    private Integer id;

    CausaSugestaoEnum(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public static CausaSugestaoEnum getFromId(Integer id){
        for(CausaSugestaoEnum c : values()){
            if(c.id.equals(id)){
                return c;
            }
        }
        return null;
    }
}

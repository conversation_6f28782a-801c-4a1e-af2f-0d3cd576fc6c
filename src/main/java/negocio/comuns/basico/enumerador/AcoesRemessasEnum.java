/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum AcoesRemessasEnum {
//    Contato com o Cliente;
    CONTATO(0),
//->Trocar Cartão Cliente;
    TROCAR_CARTAO(1),
//->Realizar contato Cielo;
    REALIZAR_CONTATO_CIELO(2),
//-><PERSON><PERSON><PERSON> reenvio;
    REALIZAR_REENVIO(3),
//->Outros
    OUTROS(4);

    private AcoesRemessasEnum(Integer codigo){
        this.codigo = codigo;
    }
    private Integer codigo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public static AcoesRemessasEnum getFromId(Integer codigo){
        if(codigo == null){
            return null;
        }
        for(AcoesRemessasEnum acao : AcoesRemessasEnum.values()){
            if(acao.getCodigo() == codigo){
                return acao;
            }
        }
        return null;
    }

    public boolean getContatoCliente(){
        return this.equals(CONTATO);
    }
    public boolean getContatoCielo(){
        return this.equals(REALIZAR_CONTATO_CIELO);
    }
    public boolean getBloqueio(){
        return this.equals(TROCAR_CARTAO);
    }
    public boolean getReenvio(){
        return this.equals(REALIZAR_REENVIO);
    }
}

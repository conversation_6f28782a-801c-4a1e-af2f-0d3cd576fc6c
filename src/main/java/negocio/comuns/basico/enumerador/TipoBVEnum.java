/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico.enumerador;

/**
 * <AUTHOR>
 */
public enum TipoBVEnum {

    MA(1, "Matr<PERSON>cula", "MA"),
    RT(2, "Retor<PERSON>", "RT"),
    RE(3, "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RE"),
    SS_PRIMEIRA_COMPRA(4, "Sessão - Primeira Compra", "SS-P"),
    SS_RETORNO_COMPRA(5, "Sessão - Retorno", "SS-R");
    private Integer codigo;
    private String descricao;
    private String sigla;

    private TipoBVEnum(Integer codigo, String descricao, String sigla) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.sigla = sigla;
    }

    public static TipoBVEnum getTipo(int consulta) {
        for (TipoBVEnum origem : TipoBVEnum.values()) {
            if (origem.getCodigo() == consulta) {
                return origem;
            }
        }
        return null;
    }


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }
}

package negocio.comuns.basico.enumerador;

public enum ClassificacaoNegocioEnum {

    LOWCOST(1,"Low Cost/Baixo Custo"),
    FULLSERVICE(2,"Full Service/Tradicional"),
    BOUTIQUE(3,"Boutique"),
    PREMIUM(4,"Premium/Select/Exclusive"),
    STUDIOPILATES(5,"Studio Pilates"),
    BOXCROSSFIT(6,"Box CrossFit®/Cross Training"),
    DOJO(7,"Dojô/Escola de Lutas"),
    NATACAO(8, "Natação"),
    ACADEMIAS_EMPRESARIAIS(9, "Academia empresariais/Governamentais")
    ;

    private Integer codigo;
    private String descricao;

     ClassificacaoNegocioEnum(Integer codigo, String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static ClassificacaoNegocioEnum obterPorCodigo(int codigo){
        for(ClassificacaoNegocioEnum item : ClassificacaoNegocioEnum.values()){
            if(item.getCodigo() == codigo)
                return item;
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

}

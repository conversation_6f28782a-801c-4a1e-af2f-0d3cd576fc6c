package negocio.comuns.basico.enumerador;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public enum MascaraDataEnum {

    DATA_DDMMYYYY_1(1, "dd/MM/yyyy", "08/07/1989"),
    DATA_DDMMYYYY_2(2, "ddMMyyyy", "08071989"),
    DATA_DDMMYYYY_3(3, "dd-MM-yyyy", "08-07-1989"),
    DATA_DDMMYYYY_4(4, "dd.MM.yyyy", "08.07.1989"),
    DATA_YYYYMMDD_1(5, "yyyy-MM-dd", "1989-07-08"),
    DATA_YYYYMMDD_2(6, "yyyyMMdd", "19890708"),
    ;

    private Integer codigo;
    private String mascara;
    private String exemplo;

    MascaraDataEnum(Integer codigo, String mascara, String exemplo) {
        this.codigo = codigo;
        this.mascara = mascara;
        this.exemplo = exemplo;
    }

    public static MascaraDataEnum obterPorCodigo(Integer codigo) {
        for (MascaraDataEnum f : MascaraDataEnum.values()) {
            if (f.getCodigo().equals(codigo)) {
                return f;
            }
        }
        return null;
    }

    public static List<SelectItem> obterListaSelectItem(boolean adicionarVazio) {
        List<SelectItem> lista = new ArrayList<>();
        if (adicionarVazio) {
            lista.add(new SelectItem(0, ""));
        }
        for (MascaraDataEnum masc : MascaraDataEnum.values()) {
            lista.add(new SelectItem(masc.getCodigo(), masc.getMascara() + " - Exemplo (" + masc.getExemplo() + ")"));
        }
        return lista;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getMascara() {
        return mascara;
    }

    public String getExemplo() {
        return exemplo;
    }
}

package negocio.comuns.basico.enumerador;

import br.com.pactosolucoes.ce.comuns.to.TipoAmbienteTO;
import br.com.pactosolucoes.ce.negocio.interessado.FormaContatoVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClassificacaoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaCadastroClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.DepartamentoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.GrauInstrucaoVO;
import negocio.comuns.basico.GrupoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.basico.ParentescoVO;
import negocio.comuns.basico.PerguntaClienteVO;
import negocio.comuns.basico.PerguntaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ProfissaoVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.contrato.ConvenioDescontoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.FeriadoVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.crm.ObjecaoVO;
import negocio.comuns.crm.TextoPadraoVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.ContaCorrenteVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.TipoContaVO;
import negocio.comuns.financeiro.TipoDocumentoVO;
import negocio.comuns.financeiro.TipoRemessaVO;
import negocio.comuns.financeiro.TipoRetornoVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ComposicaoVO;
import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.comuns.plano.DescontoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeEmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.NivelTurmaVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;

/**
 * Created by ulisses on 04/11/2016.
 */
public enum TabelaZWEnum {
    PLANOTEXTOPADRAO(1, "PLANOTEXTOPADRAO", "getPlanoTextoPadrao", "incluirSemCommit", "", "Descricao", PlanoTextoPadraoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    CATEGORIAPRODUTO(2, "CATEGORIAPRODUTO", "getCategoriaProduto", "incluir", "", "Descricao", CategoriaProdutoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    PRODUTO(3, "PRODUTO", "getProduto", "incluir", "", "Descricao", ProdutoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    CATEGORIA(4, "CATEGORIA", "getCategoria", "incluir", "", "Nome", CategoriaVO.class, "consultarPorNome", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    CENTROCUSTO(5, "CENTROCUSTO", "getCentroCusto", "incluir", "", "Descricao", CentroCustoTO.class, "consultarTodos", null, null),
    PAIS(6, "PAIS", "getPais", "incluirSemCommit", "", "Nome", PaisVO.class, "consultarPorNome", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    CIDADE(7, "CIDADE", "getCidade", "incluir", "", "nome", CidadeVO.class, "consultarPorNome", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    CLASSIFICACAO(8, "CLASSIFICACAO", "getClassificacao", "incluir", "", "nome", ClassificacaoVO.class, "consultarPorNome", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    CONDICAOPAGAMENTO(9, "CONDICAOPAGAMENTO", "getCondicaoPagamento", "incluirSemCommit", "", "descricao", CondicaoPagamentoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    PERGUNTA(10, "PERGUNTA", "getPergunta", "incluirSemCommit", "", "descricao", PerguntaVO.class, "consultarPorDescricao", new Class[] {String.class,Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    QUESTIONARIO(11, "QUESTIONARIO", "getQuestionario", "incluirSemCommit", "alterarSemCommit", "descricao", QuestionarioVO.class, "consultarPorDescricao", new Class[] {String.class,String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",null,false, Uteis.NIVELMONTARDADOS_TODOS}),
    BANCO(12, "BANCO", "getBanco", "incluir", "", "nome", BancoVO.class, "consultarPorNome", new Class[] {String.class,Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    CONTACORRENTE(13, "CONTACORRENTE", "getContaCorrente", "incluirSemCommit", "", "contaCorrente", ContaCorrenteVO.class, "consultarTodos", new Class[] {Integer.TYPE}, new Object[]{Uteis.NIVELMONTARDADOS_TODOS}),
    EMPRESA(14, "EMPRESA", "getEmpresa", "incluir", "alterarSemCommit", "nome", EmpresaVO.class, "consultarPorNome", new Class[] {String.class, Boolean.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",null,false, Uteis.NIVELMONTARDADOS_TODOS}),
    TIPORETORNO(15, "TIPORETORNO", "getTipoRetorno", "incluir", "", "descricao", TipoRetornoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    TIPOREMESSA(16, "TIPOREMESSA", "getTipoRemessa", "incluir", "", "descricao", TipoRemessaVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    PROFISSAO(17, "PROFISSAO", "getProfissao", "incluir", "", "descricao", ProfissaoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    GRAUINSTRUCAO(18, "GRAUINSTRUCAO", "getGrauInstrucao", "incluir", "", "descricao", GrauInstrucaoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    PESSOA(19, "PESSOA", "getPessoa", "incluirSemComit", "", "nome", PessoaVO.class, "consultarPorNome", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    PERFILACESSO(20, "PERFILACESSO", "getPerfilAcesso", "incluirSemCommit", "", "nome", PerfilAcessoVO.class, "consultarPorNome", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    DEPARTAMENTO(21, "DEPARTAMENTO", "getDepartamento", "incluir", "", "nome", DepartamentoVO.class, "consultarTodos", new Class[] {Integer.TYPE}, new Object[]{Uteis.NIVELMONTARDADOS_TODOS}),
    COLABORADOR(22, "COLABORADOR", "getColaborador", "incluirSemPessoa", "", "", ColaboradorVO.class, "consultarPorNomePessoa", new Class[] {String.class, Integer.class, Integer.TYPE}, new Object[]{"",0, Uteis.NIVELMONTARDADOS_TODOS}),
    USUARIO(23, "USUARIO", "getUsuario", "incluirSemCommit", "", "nome", UsuarioVO.class, "consultarPorNome", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    CONVENIOCOBRANCA(24, "CONVENIOCOBRANCA", "getConvenioCobranca", "incluir", "", "descricao", ConvenioCobrancaVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    CONVENIODESCONTO(25, "CONVENIODESCONTO", "getConvenioDesconto", "incluirSemCommit", "", "descricao", ConvenioDescontoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    DESCONTO(26, "DESCONTO", "getDesconto", "incluir", "", "descricao", DescontoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"", false,Uteis.NIVELMONTARDADOS_TODOS}),
    LOCALACESSO(27, "LOCALACESSO", "getLocalAcesso", "incluirSemCommit", "", "descricao", LocalAcessoVO.class, "consultarPorNome", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"", false,Uteis.NIVELMONTARDADOS_TODOS}),
    MODALIDADE(28, "MODALIDADE", "getModalidade", "incluirSemCommit", "", "nome", ModalidadeVO.class, "consultarPorNome", new Class[] {String.class, Integer.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"", 0, false,Uteis.NIVELMONTARDADOS_TODOS}),
    COMPOSICAO(29, "COMPOSICAO", "getComposicao", "incluirSemCommit", "", "", ComposicaoVO.class, "consultarPorDescricao", new Class[] {String.class,Integer.class,Boolean.TYPE, Integer.TYPE}, new Object[]{"",0, false, Uteis.NIVELMONTARDADOS_TODOS}),
    FORMAPAGAMENTO(30, "FORMAPAGAMENTO", "getFormaPagamento", "incluirSemCommit", "", "descricao", FormaPagamentoVO.class, "consultarPorDescricao", new Class[] {String.class,Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    JUSTIFICATIVAOPERACAO(31, "JUSTIFICATIVAOPERACAO", "getJustificativaOperacao", "incluir", "", "descricao", JustificativaOperacaoVO.class, "consultarPorDescricao", new Class[] {String.class, Integer.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",0,false, Uteis.NIVELMONTARDADOS_TODOS}),
    GRUPO(32, "GRUPO", "getGrupo", "incluir", "", "descricao", GrupoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    PARENTESCO(33, "PARENTESCO", "getParentesco", "incluir", "", "descricao", ParentescoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    HORARIO(34, "HORARIO", "getHorario", "incluirSemCommit", "", "descricao", HorarioVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    NIVELTURMA(35, "NIVELTURMA", "getNivelTurma", "incluir", "", "descricao", NivelTurmaVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    TURMA(36, "TURMA", "getTurma", "incluir", "", "descricao", TurmaVO.class, "consultarPorDescricaoTurma", new Class[] {String.class, Integer.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",0,false, Uteis.NIVELMONTARDADOS_TODOS}),
    AMBIENTE(37, "AMBIENTE", "getAmbiente", "incluir", "", "descricao", AmbienteVO.class, "consultarTodosPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"", false,Uteis.NIVELMONTARDADOS_TODOS}),
    HORARIOTURMA(38, "HORARIOTURMA", "getHorarioTurma", "incluir", "", "", HorarioTurmaVO.class, "consultarTodos", new Class[] {Integer.TYPE}, new Object[]{Uteis.NIVELMONTARDADOS_TODOS}),
    PLANO(39, "PLANO", "getPlano", "incluirSemCommit", "", "descricao", PlanoVO.class, "consultarPorDescricao", new Class[] {String.class, Integer.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",0, false, Uteis.NIVELMONTARDADOS_TODOS}),
    OPERADORACARTAO(40, "OPERADORACARTAO", "getOperadoraCartao", "incluir", "", "descricao", OperadoraCartaoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    PERGUNTACLIENTE(41, "PERGUNTACLIENTE", "getPerguntaCliente", "incluirSemCommit", "", "descricao", PerguntaClienteVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    CLIENTE(42, "CLIENTE", "getCliente", "incluirSemPessoaSemCommit", "", "", ClienteVO.class, "consultarTodos", new Class[] {Integer.TYPE}, new Object[]{Uteis.NIVELMONTARDADOS_TODOS}),

    QUESTIONARIOCLIENTE(43, "QUESTIONARIOCLIENTE", "getQuestionarioCliente", "incluirSemComit", "", "", QuestionarioClienteVO.class, "consultarPorDescricaoQuestionario", new Class[] {String.class, Integer.TYPE}, new Object[]{"",Uteis.NIVELMONTARDADOS_TODOS}),
    PLANOCONTA(44, "PLANOCONTA", "getPlanoConta", "incluirSemCommit", "", "Descricao", PlanoContaTO.class, "consultarTodos", new Class[] {}, new Object[]{}),
    RATEIOINTEGRACAO(45, "RATEIOINTEGRACAO", "getRateioIntegracao", "incluir", "", "", RateioIntegracaoTO.class, "consultarTodos", new Class[] {Integer.TYPE}, new Object[]{Uteis.NIVELMONTARDADOS_TODOS}),

    CONFIGURACAOSISTEMACRM(46, "CONFIGURACAOSISTEMACRM", "getConfiguracaoSistemaCRM", "incluirSemCommit", "alterarSemCommit", "", ConfiguracaoSistemaCRMVO.class, "consultarTodas", new Class[] {Integer.TYPE}, new Object[]{Uteis.NIVELMONTARDADOS_TODOS}),
    GRUPOCOLABORADOR(47, "GRUPOCOLABORADOR", "getGrupoColaborador", "incluirSemCommit", "", "descricao", GrupoColaboradorVO.class, "consultarPorDescricaoGrupo", new Class[] {String.class, Boolean.TYPE, Integer.TYPE, Integer.class}, new Object[]{"",false,Uteis.NIVELMONTARDADOS_TODOS,0}),
    TIPOAMBIENTE(48, "TIPOAMBIENTE", "getTipoAmbiente", "incluir", "", "descricao", TipoAmbienteTO.class, "consultarPorDescricao", new Class[] {String.class}, new Object[]{""}),
    FORMACONTATO(49, "FORMACONTATO", "getFormaContato", "incluir", "", "descricao", FormaContatoVO.class, "consultarTodos", new Class[] {}, new Object[]{}),
    TIPOCONTA(50, "TIPOCONTA", "getTipoConta", "incluir", "", "descricao", TipoContaVO.class, "consultarTodas", new Class[] {Integer.TYPE}, new Object[]{Uteis.NIVELMONTARDADOS_TODOS}),
    CONTA(51, "CONTA", "getConta", "incluir", "", "descricao", ContaVO.class, "consultarTiposContas", new Class[] {String.class, Integer.TYPE}, new Object[]{"",Uteis.NIVELMONTARDADOS_TODOS}),
    OBJECAO(52, "OBJECAO", "getObjecao", "incluir", "", "descricao", ObjecaoVO.class, "consultarTodas", new Class[] {Integer.TYPE}, new Object[]{Uteis.NIVELMONTARDADOS_TODOS}),
    TEXTOPADRAO(53, "TEXTOPADRAO", "getTextoPadrao", "incluir", "", "descricao", TextoPadraoVO.class, "consultarTodos", new Class[] {Integer.TYPE}, new Object[]{Uteis.NIVELMONTARDADOS_TODOS}),
    TIPODOCUMENTO(54, "TIPODOCUMENTO", "getTipoDocumento", "incluir", "", "descricao", TipoDocumentoVO.class, "consultarTodas", new Class[] {Integer.TYPE}, new Object[]{Uteis.NIVELMONTARDADOS_TODOS}),
    CONFIGURACAOSISTEMACADASTROVISITANTE(55, "CONFIGURACAOSISTEMACADASTROCLIENTE", "getConfiguracaoSistemaCadastroCliente", "", "alterar", "nome", ConfiguracaoSistemaCadastroClienteVO.class, "consultarTodos", new Class[] {Integer.TYPE}, new Object[]{Uteis.NIVELMONTARDADOS_TODOS}),
    CONFIGURACAOSISTEMA(56, "CONFIGURACAOSISTEMA", "getConfiguracaoSistema", "", "alterarSemCommit", "", ConfiguracaoSistemaVO.class, "consultarPorCodigo", new Class[] {Integer.class, Boolean.TYPE, Integer.TYPE}, new Object[]{1,false,Uteis.NIVELMONTARDADOS_TODOS}),
    USUARIOMOVEL(57, "USUARIOMOVEL", "getUsuarioMovel", "incluir", "", "nome", UsuarioMovelVO.class, "consultarPorNome", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    FERIADO(58, "FERIADO", "getFeriado", "incluir", "", "descricao", FeriadoVO.class, "consultarPorDescricao", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS}),
    MODELOMENSAGEM(59, "MODELOMENSAGEM", "getModeloMensagem", "incluir", "", "titulo", ModeloMensagemVO.class, "consultarPorTitulo", new Class[] {String.class, Boolean.TYPE, Integer.TYPE}, new Object[]{"",false, Uteis.NIVELMONTARDADOS_TODOS});


    TabelaZWEnum(int ordem, String nomeTabela, String nomeMetodoDaoFacadeFactory,
                 String nomeMetodoIncluir,
                 String nomeMetodoAlterar,
                 String nomeCampoDescricaoVO, Class clazzVO,
                 String nomeMetodoPesquisarTodos,
                 Class[] parameterTypesConsultarTodos,
                 Object[] parameterValuesConsultarTodos){
        this.ordem = ordem;
        this.nomeTabela = nomeTabela;
        this.nomeMetodoDaoFacadeFactory = nomeMetodoDaoFacadeFactory;
        this.nomeMetodoIncluir = nomeMetodoIncluir;
        this.nomeMetodoAlterar = nomeMetodoAlterar;
        this.nomeCampoDescricaoVO = nomeCampoDescricaoVO;
        this.clazzVO = clazzVO;
        this.nomeMetodoPesquisarTodos = nomeMetodoPesquisarTodos;
        this.parameterTypesConsultarTodos = parameterTypesConsultarTodos;
        this.parameterValuesConsultarTodos = parameterValuesConsultarTodos;
    }

    private int ordem;
    private String nomeTabela;
    private String nomeMetodoDaoFacadeFactory;
    private String nomeMetodoIncluir;
    private String nomeMetodoAlterar;
    private String nomeCampoDescricaoVO;
    private Class clazzVO;
    private String nomeMetodoPesquisarTodos;
    private Class[] parameterTypesConsultarTodos;
    private Object[] parameterValuesConsultarTodos;

    public static TabelaZWEnum getPorNomeTabela(String nomeTabela){
        for(TabelaZWEnum tabela : TabelaZWEnum.values()){
            if(tabela.getNomeTabela().toUpperCase().equals(nomeTabela.toUpperCase())){
                return tabela;
            }
        }
        return null;
    }

    public int getOrdem() {
        return ordem;
    }

    public void setOrdem(int ordem) {
        this.ordem = ordem;
    }

    public String getNomeTabela() {
        return nomeTabela;
    }

    public void setNomeTabela(String nomeTabela) {
        this.nomeTabela = nomeTabela;
    }

    public String getNomeMetodoDaoFacadeFactory() {
        return nomeMetodoDaoFacadeFactory;
    }

    public void setNomeMetodoDaoFacadeFactory(String nomeMetodoDaoFacadeFactory) {
        this.nomeMetodoDaoFacadeFactory = nomeMetodoDaoFacadeFactory;
    }

    public String getNomeMetodoIncluir() {
        return nomeMetodoIncluir;
    }

    public void setNomeMetodoIncluir(String nomeMetodoIncluir) {
        this.nomeMetodoIncluir = nomeMetodoIncluir;
    }

    public Class getClazzVO() {
        return clazzVO;
    }

    public void setClazzVO(Class clazzVO) {
        this.clazzVO = clazzVO;
    }


    public String getNomeCampoDescricaoVO() {
        return nomeCampoDescricaoVO;
    }

    public void setNomeCampoDescricaoVO(String nomeCampoDescricaoVO) {
        this.nomeCampoDescricaoVO = nomeCampoDescricaoVO;
    }

    public Class[] getParameterTypesConsultarTodos() {
        return parameterTypesConsultarTodos;
    }

    public void setParameterTypesConsultarTodos(Class[] parameterTypesConsultarTodos) {
        this.parameterTypesConsultarTodos = parameterTypesConsultarTodos;
    }

    public Object[] getParameterValuesConsultarTodos() {
        return parameterValuesConsultarTodos;
    }

    public void setParameterValuesConsultarTodos(Object[] parameterValuesConsultarTodos) {
        this.parameterValuesConsultarTodos = parameterValuesConsultarTodos;
    }

    public String getNomeMetodoPesquisarTodos() {
        return nomeMetodoPesquisarTodos;
    }

    public void setNomeMetodoPesquisarTodos(String nomeMetodoPesquisarTodos) {
        this.nomeMetodoPesquisarTodos = nomeMetodoPesquisarTodos;
    }

    public String getNomeMetodoAlterar() {
        return nomeMetodoAlterar;
    }

    public void setNomeMetodoAlterar(String nomeMetodoAlterar) {
        this.nomeMetodoAlterar = nomeMetodoAlterar;
    }


}

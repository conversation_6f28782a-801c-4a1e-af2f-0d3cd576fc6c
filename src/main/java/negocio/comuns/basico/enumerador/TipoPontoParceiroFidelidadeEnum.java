/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum TipoPontoParceiroFidelidadeEnum {

    NENHUM(0, "NENHUM", TipoParceiroEnum.NENHUM),
    RESGATAR(1, "RESGATAR", TipoParceiroEnum.DOTZ),
    ESTORNADO(2, "ESTORNADO", TipoParceiroEnum.DOTZ),
    ACUMULAR(3, "ACUMULAR", TipoParceiroEnum.DOTZ),
    ACUMULAR_RECORRENCIA(4, "ACUMULAR RECORRENCIA", TipoParceiroEnum.DOTZ),
    FALHA_ESTORNAR(5, "FALHA AO ESTORNAR DOTZ", TipoParceiroEnum.DOTZ),
    NAO_CLIENTE_DOTZ(6, "NÃO É CLIENTE DOTZ", TipoParceiroEnum.DOTZ)
    ;

    private Integer codigo;
    private String descricao;
    private TipoParceiroEnum tipoParceiroEnum;

    private TipoPontoParceiroFidelidadeEnum(final Integer codigo, final String descricao, final TipoParceiroEnum tipoParceiroEnum) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.tipoParceiroEnum = tipoParceiroEnum;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TipoParceiroEnum getTipoParceiroEnum() {
        return tipoParceiroEnum;
    }

    public void setTipoParceiroEnum(TipoParceiroEnum tipoParceiroEnum) {
        this.tipoParceiroEnum = tipoParceiroEnum;
    }

    public static TipoPontoParceiroFidelidadeEnum valueOf(Integer codigo) {
        for (TipoPontoParceiroFidelidadeEnum tipo  : TipoPontoParceiroFidelidadeEnum.values()) {
            if (tipo.getCodigo().equals(codigo)) {
                return tipo;
            }
        }
        return NENHUM;
    }
}

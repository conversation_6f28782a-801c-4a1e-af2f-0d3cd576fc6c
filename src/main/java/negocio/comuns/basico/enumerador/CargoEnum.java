package negocio.comuns.basico.enumerador;

public enum CargoEnum {

    DIRECAO(1, "Direção"),
    ADMINISTRATIVO(2, "Administrativo"),
    FINANCEIRO(3, "Financeiro"),
    PESSOALRH(4, "Pessoal / RH"),
    GERENCIA(5, "Gerência"),
    MEDICO(6, "Médico(a)"),
    NUTRICAO(7, "Nutrição"),
    FISIOTERAPIA(8, "Fisioterapia"),
    TECNICO(9, "Técnico"),
    MARKETING(10, "Marketing"),
    VENDAS(11, "Vendas"),
    RECEPCAO(12, "<PERSON>cepção"),
    TERCERIZADO(13, "Tercerizado"),
    OUTRO(99, "Outro"),
    ;

    private Integer codigo;
    private String descricao;

    CargoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static CargoEnum obterPorCodigo(int codigo) {
        for (CargoEnum item : CargoEnum.values()) {
            if (item.getCodigo().equals(codigo)) {
                return item;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

}

package negocio.comuns.basico.enumerador;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public enum TipoImportacaoEnum {

    CLIENTE(1, "CLIENTE"),
    CONTRATO(2, "CONTRATO"),
    ACESSOS(3, "ACESSOS"),
    COLABORADOR(4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"),
    USUARIO(5, "US<PERSON><PERSON><PERSON>"),
    PR<PERSON><PERSON><PERSON>(6, "PRODUT<PERSON>"),
    CONTA_FINANCEIRO(7, "CONTA FINANCEIRO"),
    FORNECEDOR(8, "FORNECEDOR"),
    MEMBERS_EVO(9, "MEMBERS_EVO"),
    MEMBERS_EVO_RECEBIMENTOS_PARCELAS(10, "MEMBERS_EVO_RECEBIMENTOS_PARCELAS"),
    ALUNOS_TURMAS(11, "ALUNOS_TURMAS"),
    MEMBERS_EVO_CONSULTA_API(12, "MEMBERS_EVO (Consulta API)"),
    PROSPECTS_EVO(13, "PROSPECTS_EVO"),
    TURMAS(14, "TURMAS"),
    PARCELAS_PAGAMENTOS(15, "PARCELAS_PAGAMENTOS"),
    TREINO_ATIVIDADES(16, "TREINO_ATIVIDADES"),
    TREINO_PROGRAMAS(17, "TREINO_PROGRAMAS"),
    TREINO_ATIVIDADE_FICHA(18, "TREINO_ATIVIDADE_FICHA"),
    ;

    private Integer codigo;
    private String descricao;

    TipoImportacaoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoImportacaoEnum obterPorCodigo(Integer codigo) {
        for (TipoImportacaoEnum f : TipoImportacaoEnum.values()) {
            if (f.getCodigo().equals(codigo)) {
                return f;
            }
        }
        return null;
    }
}

package negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico;

public enum LocaleEnum {

    PORTUGUES("Português (Brasil)", "pt_BR", "pt", "pt"),
    PORTUGUES_MZ("Português (Moçambique)", "pt_MZ", "pt-MZ", "pt"),
    INGLES("Inglês (EUA)", "en_US", "en", "en"),
    ESPANHOL_LA("Espanhol (América latina)", "es_ES", "es-LA", "es");

    LocaleEnum(String descricao, String locale, String localize, String siglaNovaPlataforma) {
        this.locale = locale;
        this.descricao = descricao;
        this.localize = localize;
        this.siglaNovaPlataforma = siglaNovaPlataforma;
    }

    private String descricao;
    private String locale;
    private String localize;
    private String siglaNovaPlataforma;

    public String getDescricao() {
        return descricao;
    }

    public String getLocale() {
        return locale;
    }

    public String getLocalize() {
        return localize;
    }

    public String getSiglaNovaPlataforma() {
        return siglaNovaPlataforma;
    }

    public static LocaleEnum obterLocale(String locale){
        for(LocaleEnum l : LocaleEnum.values()){
            if(l.getLocale().equals(locale) || l.getLocalize().equals(locale)){
                return l;
            }
        }
        return PORTUGUES;
    }
}

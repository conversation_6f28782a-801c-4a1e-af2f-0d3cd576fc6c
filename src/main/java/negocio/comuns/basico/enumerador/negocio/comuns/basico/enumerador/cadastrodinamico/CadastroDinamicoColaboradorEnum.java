package negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 17/08/2015.
 */
public enum CadastroDinamicoColaboradorEnum implements CadastroDinamicoEnumInterface  {

    // dados pessoais
    PROFISSAO("Profissão"),
    DATACADASTRO("Data Cadastro"),
    NOMEPAI("Nome do Pai ou Responsável"),
    NOMEMAE("Nome da Mãe ou Responsável"),
    CPFMAE("Cpf da Mãe ou Responsável"),
    CPF("CPF"),
    RG("RG"),
    ORGAOEMISSOR("Órgão Emissor"),
    ESTADOEMISSAO("Estado de Emissão"),
    PAIS("País"),
    ESTADO("Estado"),
    CIDADE("Cidade"),
    ESTADOCIVIL("Estado Civil"),
    NACIONALIDADE("Nacionalidade"),
    NATURALIDADE("Naturalidade"),
    SEXO("Sexo"),
    WEBPAGE("Web Page"),

    // Dados do colaborador
    CREF("CREF"),
    PRODUTODEFAULTPERSONAL("Produto Default do Personal trainer"),
    PORCENTAGEMCOMISSAO("Porcentagem Comissão"),
    AUTENTICARGOOGLE("Autenticar Google"),

    // Endereço
    CEP("CEP"),
    ENDERECO("Endereço"),
    COMPLEMENTO("Complemento"),
    NUMERO("Número"),
    BAIRRO("Bairro"),
    ENDERECOCORRESPONDENCIA("Endereço para Correspondência"),
    TIPOENDERECO("Tipo Endereço"),

    // EMAIL
    EMAIL("Email"),
    CONTATOEMERGENCIA("CONTATOEMERGENCIA"),
    TELEFONEEMERGENCIA("TELEFONEEMERGENCIA"),

    // Telefone
    TELEFONE("Telefone"),

    GENERO("Gênero"),

    NOMEREGISTRO("Nome Registro"),
    VALORFIXOCOMISSAO("Valor fixo Comissão");



    CadastroDinamicoColaboradorEnum(String label){
       this.label = label;
    }

    private String label;

    public static List<String> getListaCampos(){
        List<String> lista = new ArrayList<String>();
        for (CadastroDinamicoColaboradorEnum cadastroDinamicoColaboradorEnum: CadastroDinamicoColaboradorEnum.values()){
            lista.add(cadastroDinamicoColaboradorEnum.toString());
        }
        return lista;

    }


    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}

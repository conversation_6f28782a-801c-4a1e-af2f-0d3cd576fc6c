package negocio.comuns.basico.enumerador;

/**
 * Created by ulisses on 05/12/2022.
 */
public enum FormaPagamentoVendaRapidaEnum {

    CARTAO_CREDITO(1,"Cartão de crédito"),
    DEBITO_CONTA_CORRENTE(2,"Débito automático em conta corrente"),
    OUTRA_FORMA_PAGTO(3,"Outra forma de pagamento"),
    BOLETO_BANCARIO_ONLINE(4,"Boleto bancário");

    FormaPagamentoVendaRapidaEnum(int codigo, String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }

    private int codigo;
    private String descricao;

    public static FormaPagamentoVendaRapidaEnum get(int codigo){
        for (FormaPagamentoVendaRapidaEnum obj: FormaPagamentoVendaRapidaEnum.values()){
            if (obj.getCodigo() == codigo){
                return obj;
            }
        }
        return null;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

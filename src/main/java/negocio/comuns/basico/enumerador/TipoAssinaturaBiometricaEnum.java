/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum TipoAssinaturaBiometricaEnum {

    DIGITAL(0, "DIGITAL"),
    FACIAL(1, "FACIAL");

    private Integer codigo;
    private String descricao;

    private TipoAssinaturaBiometricaEnum(Integer codigo, String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoAssinaturaBiometricaEnum obterPorCodigo(Integer codigo){
        for(TipoAssinaturaBiometricaEnum tipo : TipoAssinaturaBiometricaEnum.values()){
            if(tipo.getCodigo().equals(codigo)){
                return tipo;
            }
        }
        return null;
    }
}

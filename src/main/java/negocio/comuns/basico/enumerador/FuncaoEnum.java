package negocio.comuns.basico.enumerador;

public enum FuncaoEnum {

    SOCIOPROPRIETARIO(1, "Sócio-proprietário"),
    GESTOR(2, "Gestor"),
    RECEPCIONISTA(3, "Recepcionista"),
    CONSULTOR(4, " Consultor(a) de Vendas"),
    PROFESSORAULASCOLETIVAS(5, "<PERSON>"),
    PROFESSORMUSCULACAO(6, " Professor de Mu<PERSON>ção"),
    ESTAGIARIO(7, "Estagiário"),
    GERENTEGERAL(8, "Gerente Geral"),
    GERENTEVENDAS(9, "Gerente Vendas"),
    COORDENADORTECNICO(10, "Coordenador Técnico"),
    CONSULTORMARKETING(11, "Consultor de Marketing"),
    CONSULTORNEGOCIO(12, "Consultor de Academias/Negócios"),
    PERSONALTRAINER(13, "Personal Trainer"),
    AUXILIARADMINISTRATIVO(14, "Auxiliar Administrativo"),
    MANUTENCAO(15, "Manutenção"),
    LIMPEZA(16, "Limpeza"),
    MEDICO(17, "Médico(a)"),
    NUTROLOGO(18, "Nutrólogo"),
    NUTRICIONISTA(19, "Nutricionista"),
    FISIOTERAPEUTA(20, "Fisioterapeuta"),
    QUIROPRATA(21, "Quiroprata"),
    AVALIADORFISICO(22, "Avaliador Físico"),
    DESIGNER(23, "Designer"),
    ANALISTAMARKETING(24, "Analista de Marketing"),
    TI(25, "TI - Tecnologia da Informação"),
    OUTRO(99, "Outro"),
    ;

    private Integer codigo;
    private String descricao;

    FuncaoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static FuncaoEnum obterPorCodigo(int codigo) {
        for (FuncaoEnum item : FuncaoEnum.values()) {
            if (item.getCodigo().equals(codigo)) {
                return item;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

}

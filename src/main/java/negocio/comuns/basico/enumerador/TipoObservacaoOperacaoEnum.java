package negocio.comuns.basico.enumerador;

/**
 * Created by glauco on 10/09/2014.
 */
public enum TipoObservacaoOperacaoEnum {

    NENHUM("", ""),
    PARCELA_CANCELADA("PC", "Pa<PERSON>ela <PERSON>");

    private String tipo;
    private String descricao;

    private TipoObservacaoOperacaoEnum(String tipo, String descricao) {
        this.tipo = tipo;
        this.descricao = descricao;
    }

    public static TipoObservacaoOperacaoEnum get(String tipo) {
        TipoObservacaoOperacaoEnum[] values = TipoObservacaoOperacaoEnum.values();
        for (TipoObservacaoOperacaoEnum tipoEnum : values) {
            if (tipoEnum.getTipo().equals(tipo)) {
                return tipoEnum;
            }
        }
        return TipoObservacaoOperacaoEnum.NENHUM;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}

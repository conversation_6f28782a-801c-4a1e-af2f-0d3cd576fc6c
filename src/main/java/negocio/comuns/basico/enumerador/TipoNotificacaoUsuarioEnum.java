package negocio.comuns.basico.enumerador;

import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created by <PERSON><PERSON> on 24/01/2017.
 */

public enum TipoNotificacaoUsuarioEnum {

    NENHUM              (0, 0, "Nenhum", "", true, "", ""),
    ENCONTROS_UCP       (1, 1, "Encontros e Webinars", "fa-icon-calendar", true, "24", "novos encontros"),
    UNIVERSIDADE_UCP    (2, 2, "Universidade Corporativa Pacto", "imagens_flat/icon_UCP.svg", true, "12", "novos conhecimentos compartilhados"),
    RESPOSTA_UCP        (3, 3, "Notificações de Resposta", "fa-icon-bell-o", true, "28", "respostas não visualizadas"),
    REMESSA_SEM_RETORNO (4, 4, "Notificações de Remessa", "", true, "", "remessas sem retorno dentro de 3 dias"),
    VENCIMENTO_CERTIFICADO(5,5,"Notificações de Vencimento de Certificado", "", true, "", "Certificado com vencimento menor que 30 dias"),
    VALOR_EMITIDO_NOTA_FISCAL(6,6,"Notificações de valor de emissão de nota fiscal", "", true, "", "Notificações de valor de emissão de nota fiscal");

    private int codigo;
    private Integer ordem;
    private String descricao;
    private String icone;
    private boolean permiteVarias;
    private String linkAgrupado;
    private String descricaoAgrupada;

    private TipoNotificacaoUsuarioEnum(int codigo, Integer ordem, String descricao, String icone, boolean permiteVarias, String linkAgrupado, String descricaoAgrupada) {
        this.codigo = codigo;
        this.ordem = ordem;
        this.descricao = descricao;
        this.icone = icone;
        this.permiteVarias = permiteVarias;
        this.linkAgrupado = linkAgrupado;
        this.descricaoAgrupada = descricaoAgrupada;
    }

    public static TipoNotificacaoUsuarioEnum obterPorCodigo(int codigo) {
        for (TipoNotificacaoUsuarioEnum item : TipoNotificacaoUsuarioEnum.values()) {
            if (item.getCodigo() == codigo)
                return item;
        }
        return null;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public boolean getSVG() {
        return !UteisValidacao.emptyString(icone) && icone.toLowerCase().endsWith("svg");
    }

    public boolean isPermiteVarias() {
        return permiteVarias;
    }

    public void setPermiteVarias(boolean permiteVarias) {
        this.permiteVarias = permiteVarias;
    }

    public String getLinkAgrupado() {
        return linkAgrupado;
    }

    public void setLinkAgrupado(String linkAgrupado) {
        this.linkAgrupado = linkAgrupado;
    }

    public String getDescricaoAgrupada() {
        return descricaoAgrupada;
    }

    public void setDescricaoAgrupada(String descricaoAgrupada) {
        this.descricaoAgrupada = descricaoAgrupada;
    }
}

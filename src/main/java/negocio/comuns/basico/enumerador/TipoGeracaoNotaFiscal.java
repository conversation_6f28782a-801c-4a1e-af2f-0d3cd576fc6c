package negocio.comuns.basico.enumerador;

/*
 * Created by <PERSON><PERSON> on 15/03/2017.
 */
public enum TipoGeracaoNotaFiscal {

    ENVIAR_NOTA (0,"Enviar Nota Módulo NFSe"),
    GERAR_XML   (1,"Gerar Arquivo XML"),
    NOTA_MANUAL (2,"Processar como Nota Manual"),
    GERAR_XML_MARABA    (3, "Gerar Arquivo XML Marabá");

    private int codigo;
    private String descricao;

    private TipoGeracaoNotaFiscal(int codigo, String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TipoGeracaoNotaFiscal obterPorCodigo(int codigo){
        for(TipoGeracaoNotaFiscal item : TipoGeracaoNotaFiscal.values()){
            if(item.getCodigo() == codigo)
                return item;
        }
        return null;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

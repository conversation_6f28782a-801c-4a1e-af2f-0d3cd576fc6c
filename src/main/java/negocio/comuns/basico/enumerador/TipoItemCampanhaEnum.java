/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.enumerador;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum TipoItemCampanhaEnum {
    AULA(1,"Aula Coletiva",true),
    RESGATE_BRINDE(2,"Resgate Brinde",false),
    AJUSTE_PONTO(3,"Ajuste Pontuação",false),
    PRODUTO(4,"Produtos",true),
    PLANO(5,"Planos",true),
    ESTORNO_PRODUTO(6,"Estorno Produto",false),
    ESTORNO_PLANO(7,"Estorno Plano",false),
    ACESSO(8,"<PERSON>sso",true),
    INDICACAO(9, "Indicações",true),
    PLANODURACAO(10, "Duração Plano",false),
    ACESSOCHUVA(11, "Acesso na Chuva",true),
    ACESSOFRIO(12, "Acesso no Frio",true),
    ACESSOCALOR(13, "Acesso no Calor",true),
    INDICACAO_CONVERTIDA(14,"Indicação Convertida",true);

    private Integer codigo;
    private String descricao;
    private Boolean imprimirBi;

    private TipoItemCampanhaEnum(Integer codigo, String descricao,Boolean imprimirBi) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.imprimirBi= imprimirBi;
    }
    
    public static TipoItemCampanhaEnum getTipo(Integer codigo){
        for (TipoItemCampanhaEnum tipoItemEnum : TipoItemCampanhaEnum.values()) {
            if (tipoItemEnum.codigo == codigo) {
                return tipoItemEnum;
            }
        }
        return null;
    }
    
    public Integer getCodigo() {
        return codigo;
    }


    public String getDescricao() {
        return descricao;
    }

    /**
     * Metodo que retorna apenas os items que estão marcados para serem visualizados dentro do BI ou telas oustros
     * itens são para uso interno de relatorios
     *
     * @return List<TipoItemCampanhaEnum>
     */
    public static List<TipoItemCampanhaEnum> getValuesEnun(){
        return Arrays.stream(TipoItemCampanhaEnum.values()).filter(item -> item.imprimirBi).collect(Collectors.toList());
    }

}

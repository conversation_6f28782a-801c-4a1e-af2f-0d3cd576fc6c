package negocio.comuns.basico.enumerador;


import controle.arquitetura.FuncionalidadeClienteEnumAux;
import controle.arquitetura.FuncionalidadeSistemaEnumAux;
import controle.arquitetura.ModuloAberto;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;
import servicos.notificacao.IdLocalicazaoMenuEnum;

/**
 * Created by Rafael on 02/09/2015.
 */
public class ClienteFuncionalidadeTO extends SuperTO {

    ClienteVO clienteVO;
    FuncionalidadeSistemaEnumAux funcionalidadeSistemaEnumAux = new FuncionalidadeSistemaEnumAux();
    FuncionalidadeClienteEnumAux funcionalidadeClienteEnumAux = new FuncionalidadeClienteEnumAux();
    boolean cliente;
    String ordenacao;
    String rotulo;
    String itemDescricao;
    Boolean descricao = false;
    int equivalencia = 0;
    Boolean subTitulo = false;
    private boolean apresentarMatCpf = false;
    private boolean autorizacaoCobranca;
    private IdLocalicazaoMenuEnum idLocalicazaoMenuEnum;

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public FuncionalidadeSistemaEnumAux getFuncionalidadeSistemaEnumAux() {
        return funcionalidadeSistemaEnumAux;
    }

    public void setFuncionalidadeSistemaEnumAux(FuncionalidadeSistemaEnumAux funcionalidadeSistemaEnumAux) {
        this.funcionalidadeSistemaEnumAux = funcionalidadeSistemaEnumAux;
    }

    public boolean isCliente() {
        return cliente;
    }

    public String getRotulo() {

        if(cliente) {
            return getClienteVO().getPessoa().getNome();
        } else if(getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum() != null)
            return  getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum().getDescricao();
        else
            return  getFuncionalidadeClienteEnumAux().getFuncionalidadeClienteEnum().getDescricao();

    }

    public void setRotulo(String rotulo) {
        this.rotulo = rotulo;
    }

    public void setCliente(Boolean cliente) {
        this.cliente = cliente;
    }

    public String getOrdenacao() {
        if(cliente){
            return "1"+clienteVO.getPessoa().getNome();
        }else{
            if(funcionalidadeSistemaEnumAux.getFuncionalidadeSistemaEnum() != null)
                return "2"+getEquivalencia()+ funcionalidadeSistemaEnumAux.getFuncionalidadeSistemaEnum().getDescricao();
            else
                return "2"+getEquivalencia()+funcionalidadeClienteEnumAux.getFuncionalidadeClienteEnum().getDescricao();
        }
    }

    public ModuloAberto getFuncionalidade(){

           if(getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum()!=null)
               return getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum().getModulo();
            else if(isCliente()){
               return ModuloAberto.ZILLYONWEB;
           }else if(getFuncionalidadeClienteEnumAux().getFuncionalidadeClienteEnum() !=null){
               return ModuloAberto.ZILLYONWEB;
           }
               return null;
    }

    public String getUrl(){
        if(funcionalidadeClienteEnumAux.getFuncionalidadeClienteEnum() != null)
            return funcionalidadeClienteEnumAux.getUrl();
        else if(funcionalidadeSistemaEnumAux.getFuncionalidadeSistemaEnum() != null)
            return funcionalidadeSistemaEnumAux.getUrl();
        else
            return "";
    }

    public FuncionalidadeClienteEnumAux getFuncionalidadeClienteEnumAux() {
        return funcionalidadeClienteEnumAux;
    }

    public void setFuncionalidadeClienteEnumAux(FuncionalidadeClienteEnumAux funcionalidadeClienteEnumAux) {
        this.funcionalidadeClienteEnumAux = funcionalidadeClienteEnumAux;
    }

    public int getEquivalencia() {
        return equivalencia;
    }

    public void setEquivalencia(int equivalencia) {
        this.equivalencia = equivalencia;
    }

    public String getItemDescricao() {
        return itemDescricao;
    }

    public void setItemDescricao(String itemDescricao) {
        this.itemDescricao = itemDescricao;
    }

    public Boolean getDescricao() {
        return descricao;
    }

    public void setDescricao(Boolean descricao) {
        this.descricao = descricao;
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    public Boolean getSubTitulo() {
        return subTitulo;
    }

    public void setSubTitulo(Boolean subTitulo) {
        this.subTitulo = subTitulo;
    }

    public boolean isAutorizacaoCobranca() {
        return autorizacaoCobranca;
    }

    public void setAutorizacaoCobranca(boolean autorizacaoCobranca) {
        this.autorizacaoCobranca = autorizacaoCobranca;
    }

    public boolean isApresentarMatCpf() {
        return apresentarMatCpf;
    }

    public void setApresentarMatCpf(boolean apresentarMatCpf) {
        this.apresentarMatCpf = apresentarMatCpf;
    }

    public IdLocalicazaoMenuEnum getIdLocalicazaoMenuEnum() {
        return idLocalicazaoMenuEnum;
    }

    public void setIdLocalicazaoMenuEnum(IdLocalicazaoMenuEnum idLocalicazaoMenuEnum) {
        this.idLocalicazaoMenuEnum = idLocalicazaoMenuEnum;
    }
}

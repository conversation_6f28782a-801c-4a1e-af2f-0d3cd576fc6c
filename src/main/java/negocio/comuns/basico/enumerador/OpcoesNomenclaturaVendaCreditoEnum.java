package negocio.comuns.basico.enumerador;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public enum OpcoesNomenclaturaVendaCreditoEnum {
    CREDITO_TREINO("CT", "Crédito Treino"),//padrão
    CREDITO_HORA_AULA("CH", "Crédito Hora/Aula");
    private String id;
    private String descricao;

    OpcoesNomenclaturaVendaCreditoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static List<SelectItem> getSelectList() {
        java.util.List<javax.faces.model.SelectItem> temp = new ArrayList<javax.faces.model.SelectItem>();
        for (int i = 0; i < OpcoesNomenclaturaVendaCreditoEnum.values().length; i++) {
            OpcoesNomenclaturaVendaCreditoEnum obj = OpcoesNomenclaturaVendaCreditoEnum.values()[i];
            temp.add(new javax.faces.model.SelectItem(obj.getId(), obj.getDescricao()));
        }
        return temp;
    }

    public static String getDescricao(String id) {
        for (OpcoesNomenclaturaVendaCreditoEnum value: OpcoesNomenclaturaVendaCreditoEnum.values())
            if (value.getId().equals(id)) return value.getDescricao();

        return "";
    }
}

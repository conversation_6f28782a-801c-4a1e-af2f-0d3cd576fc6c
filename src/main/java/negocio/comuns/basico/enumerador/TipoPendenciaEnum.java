/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.enumerador;

import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;

/**
 *
 * <AUTHOR>
 */
public enum TipoPendenciaEnum {

    NENHUM(0, "", "", "", null),
    B<PERSON><PERSON><PERSON>(1, "BV Pendente", "Boletins de Visita Não Preenchidos",  "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_BV_PENDENTE),
    CadastroIncompletoCliente(2, "Cadastro Incompleto (Cliente)", "Clientes com Cadastro Incompleto", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_CADASTROS_INCOMPLETOS_CLIENTE),
    CadastroIncompletoVisitante(3, "Cadastro Incompleto (Visitante)", "Visitantes com Cadastro Incompleto", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_CADASTROS_INCOMPLETOS_VISITANTE),
    ParcelaPendAPagar(4, "Parcelas a Pagar", "Parcelas a Pagar", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_PARCELAS_A_PAGAR),
    ParcelaPendAtraso(5, "Parcelas em Atraso", "Parcelas em Atraso", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_PARCELAS_ATRASO),
    DebitoCCorrente(6, "Débito em Conta Corrente", "Débito em Conta Corrente", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_CLIENTE_DEBITO),
    CreditoCCorrente(7, "Crédito em Conta Corrente", "Crédito em Conta Corrente", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_CREDITO_CC),

    Aniversariantes(8, "Aniversariantes do dia", "Aniversariantes do dia", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_ANIVERSARIANTES),
    AniversariantesColaborador(13, "Aniversariantes Colaboradores", "Aniversariantes Colaboradores", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_ANIVERSARIANTES_COL),
    ParcelaEmAbertoColaborador(9, "Colab. c/ Parcelas a Pagar", "Colaboradores c/ Parcelas a Pagar", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_PARCELA_ABERTO_COLABORADOR),
    ClientesProdutosVencidos(10, "Produtos Vencidos", "São considerados produtos que possuem vigência configurada, tais como: Avaliação Física, Atestado e outros", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_PRODUTO_VENCIDO),
    ClientesSemProdutos(11, "Sem Produtos", "Clientes sem Produtos", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_SEM_PRODUTOS),
    CartoesVencidos(12, "Cartões de Crédito Vencidos", "Cartões de Crédito Vencidos", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_CARTOES_VENCIDOS),
    CartoesAVencer(13, "Cartões de Crédito A Vencer Próximo Mês", "Cartões de Crédito A Vencer Próximo Mês", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_CARTOES_AVENCER),
    ClientesMesmoCartao(14, "Clientes com mesmo Cartão de Crédito", "Clientes com mesmo Cartão de Crédito", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_MESMO_CARTOES),
    ClientesSemFoto(15,"Sem Foto", "Mostra apenas alunos que acessaram a acadêmia nos últimos 30 dias", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_SEM_FOTO),
    ClientesTrancamentoVencidos(16,"Trancamento Vencido", "Clientes com Trancamento Vencido", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_TRANCADOS_VENCIDOS),
    ClientesTransferidosContratoCancelado(17, "Transferidos com Contrato Cancelado", "Transferidos com Contrato Cancelado", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_TRANSFERIDOS_CANCELADO),
    ClientesSemAssinaturaDigital(18,"Sem assinatura de contrato", "Clientes sem Assinatura Digital Cadastrado", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_SEM_ASSINATURA),
    CartoesComProblema(19, "Cartões de Credito Incompletos", "Cartões de Credito Incompletos", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA__CARTOES_PROBLEMA),
    ClientesSemGeoLocalizacao(20, "Sem Geolocalização", "Clientes sem Geolocalização", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_SEM_GEOLOCALIZACAO),
    ClientesSemBiometriaFacial(21,"Sem Reconhecimento Facial", "Clientes sem Reconhecimento Facial Cadastrado", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_SEM_BIOMETRIA_FACIAl),
    ClientesSemAssinaturaDigitalCancelamento(22,"Sem assinatura de cancelamento de contrato", "Clientes sem Assinatura Digital Cadastrado", "bi-pendencias-de-clientes-adm/", ItemExportacaoEnum.PENDENCIA_SEM_ASSINATURA_CANCELAMENTO);

    private TipoPendenciaEnum(int id, String descricao, String hint, String urlLinkWiki, ItemExportacaoEnum item) {
        this.id = id;
        this.descricao = descricao;
        this.hint = hint;
        this.urlLinkWiki = urlLinkWiki;
        this.itemExportacao = item;

    }
    public static TipoPendenciaEnum tipoPendenciaEnumFromDesc(String dx){
        for(TipoPendenciaEnum tpe : values()){
            if(tpe.getDescricao().equals(dx)){
                return tpe;
            }
        }
        return null;
    }
    private int id;
    private String descricao;
    private String hint;

    private String urlLinkWiki;

    private ItemExportacaoEnum itemExportacao;

    public String getUrlLinkWiki() {
        return urlLinkWiki;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }

    public ItemExportacaoEnum getItemExportacao() {
        return itemExportacao;
    }
}

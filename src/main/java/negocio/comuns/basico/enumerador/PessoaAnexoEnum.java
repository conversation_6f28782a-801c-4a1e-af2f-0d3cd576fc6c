package negocio.comuns.basico.enumerador;

import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;

public enum PessoaAnexoEnum {
    CARTAO_VACINACAO_PRIMEIRA_DOSE(1,"primeira dose"),
    CARTAO_VACINACAO_SEGUNDA_UNICA(2, "segunda dose ou dose única");

    private Integer codigo;
    private String descricao;

    private PessoaAnexoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static PessoaAnexoEnum consultarPorCodigo(Integer codigo){
        for (PessoaAnexoEnum item : PessoaAnexoEnum.values()) {
            if (item.getCodigo() != null && item.getCodigo().equals(codigo)) {
                return item;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}

package negocio.comuns.basico.enumerador;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public enum TipoLogEnum {

    ERRO(1, "Erro", "fa-icon-ban-circle", "color: #db394f;"),
    SUCESSO(2, "Sucesso", "fa-icon-ok-sign", "color: #3cdb5c;"),
    INFORMACAO(3, "Informação", "fa-icon-circle", "color: #fdb813"),
    ;

    private Integer codigo;
    private String descricao;
    private String styleClass;
    private String style;

    TipoLogEnum(Integer codigo, String descricao, String styleClass, String style) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.styleClass = styleClass;
        this.style = style;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoLogEnum obterPorCodigo(Integer codigo) {
        for (TipoLogEnum f : TipoLogEnum.values()) {
            if (f.getCodigo().equals(codigo)) {
                return f;
            }
        }
        return INFORMACAO;
    }

    public String getStyleClass() {
        return styleClass;
    }

    public String getStyle() {
        return style;
    }
}

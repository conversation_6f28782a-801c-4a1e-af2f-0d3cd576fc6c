package negocio.comuns.basico.enumerador;

/**
 * Created by glauco on 19/12/13.
 */
public enum TipoServicoEnum {

    PLANO("PL", "Plano"),
    SERVICO("SE", "Serviço"),
    PESQUISA("PS", "Pesquisa");

    private String tipo;
    private String descricao;

    private TipoServicoEnum(String tipo, String descricao) {
        this.tipo = tipo;
        this.descricao = descricao;
    }

    public static TipoServicoEnum getTipo(String tipoP) {
        for (TipoServicoEnum tipo : TipoServicoEnum.values()) {
            if (tipo.getTipo().equals(tipoP)) {
                return tipo;
            }
        }
        return null;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum AcaoObjcaoLeadEnum {
    NENHUMA(0,"Não atualizar"),
    ATUALIZAR_QUALQUER(1,"Atualizar com qualquer objeção"),
   APENAS_DEFINTIVA(2,"Atualizar apenas com objeção definitiva");
   
   AcaoObjcaoLeadEnum(int codigo, String descricao) {
       this.codigo = codigo;
       this.descricao = descricao;
   }
   private Integer codigo;
   private String descricao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
   
   public static AcaoObjcaoLeadEnum obterPorCodigo(int codigo){
        for(AcaoObjcaoLeadEnum a : AcaoObjcaoLeadEnum.values()){
            if(a.getCodigo() == codigo){
                return a;
            }
        }
        return null;
    }
    
}

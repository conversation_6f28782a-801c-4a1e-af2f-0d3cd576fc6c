package negocio.comuns.basico.enumerador;

/**
 * date : 28/04/2015 10:29:30 autor: <PERSON><PERSON><PERSON>
 */
public enum TipoBloqueioInadimplenciaEnum {

	PADRAO_SISTEMA(1, "PADRÃO DO SISTEMA"), MES_SUBSEQUENTE_AO_VENCIMENTO(2,
			"MÊS SUBSEQUENTE AO VENCIMENTO");

	TipoBloqueioInadimplenciaEnum(Integer codigo, String descricao) {
		this.codigo = codigo;
		this.descricao = descricao;
	}

	private Integer codigo;
	private String descricao;

	public Integer getCodigo() {
		return codigo;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	public String getDescricao() {
		return descricao;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public static TipoBloqueioInadimplenciaEnum getTipoBloqueioInadimplenciaEnum(
			Integer codigo) {
		if (codigo == null)
			return null;
		for (TipoBloqueioInadimplenciaEnum tipo : TipoBloqueioInadimplenciaEnum.values()) {
			if (tipo.getCodigo() == codigo) {
				return tipo;
			}
		}
		return null;
	}

}

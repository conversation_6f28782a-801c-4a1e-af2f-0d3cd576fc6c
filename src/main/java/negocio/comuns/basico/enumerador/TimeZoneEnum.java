/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.enumerador;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Adicionar TimeZones por demanda de acordo com o Padrão de Nomenclaturas da API
 *
 * <AUTHOR>
 */
public enum TimeZoneEnum {
    Brazil_East("Brazil/East", "BRT", "Brasilia Time"),//padrão
    Brazil_Acre("Brazil/Acre", "AMT", "Amazon Time"),
    Brazil_DeNoronha("Brazil/DeNoronha", "FNT", "Fernando <PERSON> Nor<PERSON>ha Time"),
    Brazil_West("Brazil/West", "AMT", "Amazon Time"),
    Brazil_GTM_3("GMT-3", "GMT-3", ""),
    GMT_MAIS_0("GMT+0", "GMT+0", ""),
    GMT_MAIS_1("GMT+1", "GMT+1", ""),
    GMT_MAIS_130("GMT+1:30", "GMT+1:30", ""),
    GMT_MAIS_145("GMT+1:45", "GMT+1:45", ""),
    GMT_MAIS_2("GMT+2", "GMT+2", ""),
    GMT_MAIS_230("GMT+2:30", "GMT+2:30", ""),
    GMT_MAIS_245("GMT+2:45", "GMT+2:45", ""),
    GMT_MAIS_3("GMT+3", "GMT+3", ""),
    GMT_MAIS_330("GMT+3:30", "GMT+3:30", ""),
    GMT_MAIS_345("GMT+3:45", "GMT+3:45", ""),
    GMT_MAIS_4("GMT+4", "GMT+4", ""),
    GMT_MAIS_430("GMT+4:30", "GMT+4:30", ""),
    GMT_MAIS_445("GMT+4:45", "GMT+4:45", ""),
    GMT_MAIS_5("GMT+5", "GMT+5", ""),
    GMT_MAIS_530("GMT+5:30", "GMT+5:30", ""),
    GMT_MAIS_545("GMT+5:45", "GMT+5:45", ""),
    GMT_MAIS_6("GMT+6", "GMT+6", ""),
    GMT_MAIS_630("GMT+6:30", "GMT+6:30", ""),
    GMT_MAIS_645("GMT+6:45", "GMT+6:45", ""),
    GMT_MAIS_7("GMT+7", "GMT+7", ""),
    GMT_MAIS_730("GMT+7:30", "GMT+7:30", ""),
    GMT_MAIS_745("GMT+7:45", "GMT+7:45", ""),
    GMT_MAIS_8("GMT+8", "GMT+8", ""),
    GMT_MAIS_830("GMT+8:30", "GMT+8:30", ""),
    GMT_MAIS_845("GMT+8:45", "GMT+8:45", ""),
    GMT_MAIS_9("GMT+9", "GMT+9", ""),
    GMT_MAIS_930("GMT+9:30", "GMT+9:30", ""),
    GMT_MAIS_945("GMT+9:45", "GMT+9:45", ""),
    GMT_MAIS_10("GMT+10", "GMT+10", ""),
    GMT_MAIS_1030("GMT+10:30", "GMT+10:30", ""),
    GMT_MAIS_1045("GMT+10:45", "GMT+10:45", ""),
    GMT_MAIS_11("GMT+11", "GMT+11", ""),
    GMT_MAIS_1130("GMT+11:30", "GMT+11:30", ""),
    GMT_MAIS_1145("GMT+11:45", "GMT+11:45", ""),
    GMT_MAIS_12("GMT+12", "GMT+12", ""),
    GMT_MENOS_1("GMT-1", "GMT-1", ""),
    GMT_MENOS_130("GMT-1:30", "GMT-1:30", ""),
    GMT_MENOS_145("GMT-1:45", "GMT-1:45", ""),
    GMT_MENOS_2("GMT-2", "GMT-2", ""),
    GMT_MENOS_230("GMT-2:30", "GMT-2:30", ""),
    GMT_MENOS_245("GMT-2:45", "GMT-2:45", ""),
    GMT_MENOS_3("GMT-3", "GMT-3", ""),
    GMT_MENOS_330("GMT-3:30", "GMT-3:30", ""),
    GMT_MENOS_345("GMT-3:45", "GMT-3:45", ""),
    GMT_MENOS_4("GMT-4", "GMT-4", ""),
    GMT_MENOS_430("GMT-4:30", "GMT-4:30", ""),
    GMT_MENOS_445("GMT-4:45", "GMT-4:45", ""),
    GMT_MENOS_5("GMT-5", "GMT-5", ""),
    GMT_MENOS_530("GMT-5:30", "GMT-5:30", ""),
    GMT_MENOS_545("GMT-5:45", "GMT-5:45", ""),
    GMT_MENOS_6("GMT-6", "GMT-6", ""),
    GMT_MENOS_630("GMT-6:30", "GMT-6:30", ""),
    GMT_MENOS_645("GMT-6:45", "GMT-6:45", ""),
    GMT_MENOS_7("GMT-7", "GMT-7", ""),
    GMT_MENOS_730("GMT-7:30", "GMT-7:30", ""),
    GMT_MENOS_745("GMT-7:45", "GMT-7:45", ""),
    GMT_MENOS_8("GMT-8", "GMT-8", ""),
    GMT_MENOS_830("GMT-8:30", "GMT-8:30", ""),
    GMT_MENOS_845("GMT-8:45", "GMT-8:45", ""),
    GMT_MENOS_9("GMT-9", "GMT-9", ""),
    GMT_MENOS_930("GMT-9:30", "GMT-9:30", ""),
    GMT_MENOS_945("GMT-9:45", "GMT-9:45", ""),
    GMT_MENOS_10("GMT-10", "GMT-10", ""),
    GMT_MENOS_1030("GMT-10:30", "GMT-10:30", ""),
    GMT_MENOS_1045("GMT-10:45", "GMT-10:45", ""),
    GMT_MENOS_11("GMT-11", "GMT-11", ""),
    GMT_MENOS_1130("GMT-11:30", "GMT-11:30", ""),
    GMT_MENOS_1145("GMT-11:45", "GMT-11:45", ""),
    GMT_MENOS_12("GMT-12", "GMT-12", "");

    private String id;
    private String sigla;
    private String descricao;

    private TimeZoneEnum(String id, String sigla, String descricao) {
        this.id = id;
        this.sigla = sigla;
        this.descricao = descricao;

    }

    public static List<SelectItem> getSelectListTimeZone() {
        List<SelectItem> temp = new ArrayList<SelectItem>();
        for (int i = 0; i < TimeZoneEnum.values().length; i++) {
            TimeZoneEnum obj = TimeZoneEnum.values()[i];
            temp.add(new SelectItem(obj.getId(), obj.getId() +  (obj.getDescricao().isEmpty() ? "" : " - " + obj.getDescricao())));
        }
        return temp;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.enumerador;

/**
 * Created by <PERSON> on 25/01/2024.
 */
public enum TipoTokenEnum {

    NENHUM(0, "Nenhum"),
    PIX_INTER(1, "Pix Banco Inter"),
    PIX_ITAU(2, "Pix Banco Itau"),
    ;

    private int codigo;
    private String descricao;
    TipoTokenEnum(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}



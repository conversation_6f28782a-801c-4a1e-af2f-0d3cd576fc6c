package negocio.comuns.basico.enumerador;

/**
 * Created by ulisses on 19/11/2016.
 */
public enum TipoIndiceFinanceiroEnum {

    IGPM(1, "IGPM","INDICE GERAL DE PREÇOS AO CONSUMIDOR"),
    INPC(2, "INPC", "INDICE NACIONAL DE PREÇOS AO CONSUMIDOR"),
    IPC_BR(3, "IPC_BR", "INDICE DE PREÇOS AO CONSUMIDOR - BRASIL"),
    IPCA(4, "IPCA", "INDICE NACIONAL DE PREÇOS AO CONSUMIDOR - AMPLO"),
    OUTROS(5, "OUTROS", "INDICE OUTROS");

    TipoIndiceFinanceiroEnum(Integer codigo, String sigla, String descricao){
        this.codigo = codigo;
        this.sigla = sigla;
        this.descricao = descricao;
    }

    private Integer codigo;
    private String sigla;
    private String descricao;

    public static TipoIndiceFinanceiroEnum getIndice(Integer codigo)throws Exception{
        for (TipoIndiceFinanceiroEnum obj: TipoIndiceFinanceiroEnum.values()){
            if (obj.getCodigo().equals(codigo))
                    return obj;
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }
}

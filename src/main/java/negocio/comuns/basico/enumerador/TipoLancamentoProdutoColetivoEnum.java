/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum TipoLancamentoProdutoColetivoEnum {
    DATA_ESPECIFICA("Data específica"),
    MES("Mês"),
    PARCELA("Por parcela"),
    CONTRATO_SEM_PRODUTO("Contrato sem produto");

    private String descricao;
    
    private TipoLancamentoProdutoColetivoEnum(String d){
        descricao = d;
    }

    public static TipoLancamentoProdutoColetivoEnum getFromId(Integer id){
       for(TipoLancamentoProdutoColetivoEnum tla : TipoLancamentoProdutoColetivoEnum.values()){
           if(tla.ordinal() == id.intValue()){
               return tla;
           }
       }
        return null;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    
}

package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * Reponsável por manter os dados da entidade Familiar. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class FamiliarVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer cliente;
    protected Integer familiar;
    protected String codAcesso;
    protected String nome;
    protected String identificador;
    @NaoControlarLogAlteracao
    private Integer contratoCompartilhado;
    @NaoControlarLogAlteracao
    private ContratoDependenteVO contratoDependente;

    private boolean compartilharPlano = false;
    private Integer dependenteInicial;

    @NaoControlarLogAlteracao
    protected ParentescoVO parentesco;
    @NaoControlarLogAlteracao
    private String situacao;
    @NaoControlarLogAlteracao
    private String matricula;

    /**
     * Construtor padrão da classe <code>Familiar</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public FamiliarVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>FamiliarVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(FamiliarVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getFamiliar() == 0) {
            throw new ConsistirException("O campo FAMILIAR (Aba - Dependentes) deve ser informado.");
        }
        if ((obj.getParentesco() == null) || (obj.getParentesco().getCodigo() == 0)) {
            throw new ConsistirException("O campo PARENTESCO (Aba - Dependentes) deve ser informado.");
        }
        if (obj.getCodAcesso().equals("")) {
            throw new ConsistirException("O campo CÓDIGO DE ACESSO (Aba - Dependentes) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setNome(getNome().toUpperCase());
        setCodAcesso(getCodAcesso().toUpperCase());
        setIdentificador(getIdentificador().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setFamiliar(0);
        setNome("");
        setCodAcesso("");
        setIdentificador("");
        setParentesco(new ParentescoVO());
    }

    /**
     * Retorna o objeto da classe <code>Parentesco</code> relacionado com (<code>Familiar</code>).
     */
    public ParentescoVO getParentesco() {
        if (parentesco == null) {
            parentesco = new ParentescoVO();
        }
        return (parentesco);
    }

    /**
     * Define o objeto da classe <code>Parentesco</code> relacionado com (<code>Familiar</code>).
     */
    public void setParentesco(ParentescoVO obj) {
        this.parentesco = obj;
    }

    public String getIdentificador() {
        if (identificador == null) {
            identificador = "";
        }
        return (identificador);
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getCodAcesso() {
        if (codAcesso == null) {
            codAcesso = "";
        }
        return (codAcesso);
    }

    public void setCodAcesso(String codAcesso) {
        this.codAcesso = codAcesso;
    }

    public Integer getFamiliar() {
        return (familiar);
    }

    public void setFamiliar(Integer familiar) {
        this.familiar = familiar;
    }

    public Integer getCliente() {
        return (cliente);
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    @Override
    public Integer getCodigo() {
        return (codigo);
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoApresentar() {
        if (situacao == null) {
            return "";
        }
        if (situacao.equals("AT")) {
            return "ATIVO";
        }
        if (situacao.equals("IN")) {
            return "INATIVO";
        }
        if (situacao.equals("VI")) {
            return "VISITANTE";
        }
        if (situacao.equals("TR")) {
            return "TRANCADO";
        }
        return (situacao);

    }

    public boolean isCompartilharPlano() {
        return compartilharPlano;
    }

    public void setCompartilharPlano(boolean compartilharPlano) {
        this.compartilharPlano = compartilharPlano;
    }

    public Integer getDependenteInicial() {
        return dependenteInicial;
    }

    public void setDependenteInicial(Integer dependenteInicial) {
        this.dependenteInicial = dependenteInicial;
    }

    public Integer getContratoCompartilhado() {
        return contratoCompartilhado;
    }

    public void setContratoCompartilhado(Integer contratoCompartilhado) {
        this.contratoCompartilhado = contratoCompartilhado;
    }

    public String getMatricula() {
        if(matricula == null){
            matricula = "";
        }
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public ContratoDependenteVO getContratoDependente() {
        return contratoDependente;
    }

    public void setContratoDependente(ContratoDependenteVO contratoDependente) {
        this.contratoDependente = contratoDependente;
    }
}

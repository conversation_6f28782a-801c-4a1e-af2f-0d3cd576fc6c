package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProdutoParceiroFidelidadeVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private String descricao;
    private Integer pontos;
    private Double valor;
    @ChaveEstrangeira
    private ParceiroFidelidadeVO parceiroFidelidade;
    private String codigoExterno;
    private boolean selecionado = false;

    public ProdutoParceiroFidelidadeVO() {
    }

    public ProdutoParceiroFidelidadeVO(ParceiroFidelidadeVO parceiroFidelidade) {
        this.parceiroFidelidade = parceiroFidelidade;
    }

    public static void validarDados(ProdutoParceiroFidelidadeVO obj) throws ConsistirException {
        if (obj.getValidarDados()) {
//            if (UteisValidacao.emptyString(obj.getNomeTabela())) {
//                throw new ConsistirException("O campo NOME (TabelaParceiroFidelidade) deve ser informado.");
//            }
//            if (UteisValidacao.emptyList(obj.getItens())) {
//                throw new ConsistirException("O campo ITENS (TabelaParceiroFidelidade) deve ser informado.");
//            }
        }
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getPontos() {
        if (pontos == null) {
            pontos = 0;
        }
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public ParceiroFidelidadeVO getParceiroFidelidade() {
        if (parceiroFidelidade == null) {
            parceiroFidelidade = new ParceiroFidelidadeVO();
        }
        return parceiroFidelidade;
    }

    public void setParceiroFidelidade(ParceiroFidelidadeVO parceiroFidelidade) {
        this.parceiroFidelidade = parceiroFidelidade;
    }

    public String getValor_Apresentar() {
        return Formatador.formatarValorMonetario(getValor());
    }

    public String getCodigoExterno() {
        if (codigoExterno == null) {
            codigoExterno = "";
        }
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }
}

package negocio.comuns.basico;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.facade.jdbc.basico.PerguntaCliente;

/**
 * Reponsável por manter os dados da entidade RespostaPergCliente. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see PerguntaCliente
 */
public class RespostaPergClienteVO extends SuperVO {

    protected Integer codigo;
    protected Integer perguntaCliente;
    protected Integer marcado;
    protected String descricaoRespota;
    protected Boolean respostaOpcao;
    //protected String respostaTextual;
    /**
     * Construtor padrão da classe <code>RespostaPergCliente</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public RespostaPergClienteVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>RespostaPergClienteVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(RespostaPergClienteVO obj) throws ConsistirException, Exception {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDescricaoRespota().equals("")) {
            PerguntaClienteVO pergunta = getFacade().getPerguntaCliente().consultarPorChavePrimaria(obj.getPerguntaCliente().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
            throw new ConsistirException("A RESPOSTA  da Pergunta " + pergunta.getDescricao().toString() + ": deve ser informado.");
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>RespostaPergClienteVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public void validarDadosRepostasSimples(Integer codigo, List objs) throws ConsistirException, Exception {
        int cont = 0;
        Iterator i = objs.iterator();
        while (i.hasNext()) {
            RespostaPergClienteVO respostaPergCliente = (RespostaPergClienteVO) i.next();
            if (respostaPergCliente.getRespostaOpcao()) {
                cont = 1;
            } else {
                setPerguntaCliente(codigo.intValue());
            }
        }
        if (cont == 0) {
            PerguntaClienteVO pergunta = getFacade().getPerguntaCliente().consultarPorChavePrimaria(getPerguntaCliente().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
            throw new ConsistirException("Marque uma das ALTERNATIVAS  Para Pergunta " + pergunta.getDescricao().toString() + " !.");
        } else {
                
        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricaoRespota(getDescricaoRespota().toUpperCase());
//        setRespostaTextual( respostaTextual.toUpperCase() );
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setPerguntaCliente(new Integer(0));
        setMarcado(new Integer(0));
        setDescricaoRespota("");
        setRespostaOpcao(new Boolean(false));
    //   setRespostaTextual( "" );

    }


//    public String getRespostaTextual() {
//        return (respostaTextual);
//    }
//     
//    public void setRespostaTextual( String respostaTextual ) {
//        this.respostaTextual = respostaTextual;
//    }
    public Boolean getRespostaOpcao() {
        return (respostaOpcao);
    }

    public Boolean isRespostaOpcao() {
        return (respostaOpcao);
    }

    public void setRespostaOpcao(Boolean respostaOpcao) {
        this.respostaOpcao = respostaOpcao;
    }

    public String getDescricaoRespota() {
        if (descricaoRespota== null) {
            descricaoRespota = "";
        }
        return (descricaoRespota);
    }

    public void setDescricaoRespota(String descricaoRespota) {
        this.descricaoRespota = descricaoRespota;
    }

    public Integer getPerguntaCliente() {
        return (perguntaCliente);
    }

    public void setPerguntaCliente(Integer perguntaCliente) {
        this.perguntaCliente = perguntaCliente;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMarcado() {
        return marcado;
    }

    public void setMarcado(Integer marcado) {
        this.marcado = marcado;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Classe VO responsável pelo relacionamento entre modalidade e colaborador em comissão
 * <AUTHOR>
 */
public class ModalidadeComissaoColaboradorVO extends SuperVO {

    private Integer codigo;
    private ModalidadeVO modalidade = new ModalidadeVO();
    private Double porcComissao;
    private Double valorComissao;
    private ColaboradorVO colaboradorVO = new ColaboradorVO();

    public static void validarDados(ModalidadeComissaoColaboradorVO modalidadeComissaoColaboradorVO) throws Exception{
        if(modalidadeComissaoColaboradorVO.getModalidade().getCodigo()==0){
            throw new Exception("Informe a MODALIDADE");
        }
        if(UteisValidacao.emptyNumber(modalidadeComissaoColaboradorVO.getPorcComissao()) &&
                UteisValidacao.emptyNumber(modalidadeComissaoColaboradorVO.getValorComissao())){
            throw new Exception("Informe a PORCENTAGEM ou VALOR FIXO");
        }
        if (modalidadeComissaoColaboradorVO.getPorcComissao() != null &&
                modalidadeComissaoColaboradorVO.getPorcComissao() > 100) {
            throw new Exception("A PORCENTAGEM deve ser menor que 100%");
        }
        if (modalidadeComissaoColaboradorVO.getValorComissao() != null &&
                modalidadeComissaoColaboradorVO.getValorComissao() < 0) {
            throw new Exception("O VALOR FIXO deve ser superior ou igual a 0");
        }
    }
    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        if(codigo==null){
            codigo=0;
        }
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the modalidade
     */
    public ModalidadeVO getModalidade() {
        return modalidade;
    }

    /**
     * @param modalidade the modalidade to set
     */
    public void setModalidade(ModalidadeVO modalidade) {
        this.modalidade = modalidade;
    }

    /**
     * @return the porcComissao
     */
    public Double getPorcComissao() {
        if(porcComissao==null){
            porcComissao = 0.0;
        }
        return porcComissao;
    }

    /**
     * @param porcComissao the porcComissao to set
     */
    public void setPorcComissao(Double porcComissao) {
        this.porcComissao = porcComissao;
    }

    /**
     * @return the colaboradorVO
     */
    public ColaboradorVO getColaboradorVO() {
        if(colaboradorVO==null){
            colaboradorVO = new ColaboradorVO();
        }
        return colaboradorVO;
    }

    /**
     * @param colaboradorVO the colaboradorVO to set
     */
    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public Double getValorComissao() {
        if (valorComissao == null) {
            valorComissao = 0.0;
        }
        return valorComissao;
    }

    public void setValorComissao(Double valorComissao) {
        this.valorComissao = valorComissao;
    }
}

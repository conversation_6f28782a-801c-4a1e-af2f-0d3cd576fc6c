/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.AcaoObjcaoLeadEnum;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ConfiguracaoEmpresaRDStationVO extends SuperVO{
    
    private int empresa=0;
    private boolean empresaUsaRD = false;
    private String chavePublica ="";
    private String chavePrivada="";
    private String horaLimite="23:59";
    private UsuarioVO responsavelPadrao;
    @NaoControlarLogAlteracao
    private boolean usuarioInativo = false;
    @NaoControlarLogAlteracao
    private String msgValidacao = "";
    @NaoControlarLogAlteracao
    private String urlWebhook = "";
    private String clientIdOauth = "6d8f483f-882b-4087-9753-411acd143125";  // https://appstore.rdstation.com/pt-BR/publisher/13242251495369/apps/2629
    private String clientSecretOauth = "0b0ae2af3a98412687e990f4247754bc"; // https://appstore.rdstation.com/pt-BR/publisher/13242251495369/apps/2629
    private int acaoObjecao = AcaoObjcaoLeadEnum.NENHUMA.getCodigo();
    private String eventWeebHook = "";
    private List<SelectItem> weebHookEvents;

    private boolean configAtualizarAlunoRdStationMarketing = false;
    private String accessTokenRdStationMarketing = "";
    private String refreshTokenRdStationMarketing = "";

    public ConfiguracaoEmpresaRDStationVO(){
        init();
    }

    public void init(){
        montarWeebHookEventos();
    }

    private void montarWeebHookEventos() {
        setWeebHookEvents(new ArrayList<>());
        getWeebHookEvents().add(new SelectItem("", ""));
        getWeebHookEvents().add(new SelectItem("WEBHOOK.CONVERTED", "Conversão"));
        getWeebHookEvents().add(new SelectItem("WEBHOOK.MARKED_OPPORTUNITY", "Oportunidade"));
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public boolean isEmpresaUsaRD() {
        return empresaUsaRD;
    }

    public void setEmpresaUsaRD(boolean empresaUsaRD) {
        this.empresaUsaRD = empresaUsaRD;
    }

    public String getChavePublica() {
        return chavePublica;
    }

    public void setChavePublica(String chavePublica) {
        this.chavePublica = chavePublica;
    }

    public String getChavePrivada() {
        return chavePrivada;
    }

    public void setChavePrivada(String chavePrivada) {
        this.chavePrivada = chavePrivada;
    }

    public String getHoraLimite() {
        return horaLimite;
    }

    public void setHoraLimite(String horaLimite) {
        this.horaLimite = horaLimite;
    }

    public UsuarioVO getResponsavelPadrao() {
        if (responsavelPadrao == null) {
            responsavelPadrao = new UsuarioVO();
        }
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(UsuarioVO responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }

    public String getMsgValidacao() {
        return msgValidacao;
    }

    public void setMsgValidacao(String msgValidacao) {
        this.msgValidacao = msgValidacao;
    }

    public boolean isUsuarioInativo() {
        return usuarioInativo;
    }

    public void setUsuarioInativo(boolean usuarioInativo) {
        this.usuarioInativo = usuarioInativo;
    }

    public String getUrlWebhook() {
        return urlWebhook;
    }

    public void setUrlWebhook(String urlWebhook) {
        this.urlWebhook = urlWebhook;
    }

    public void limparConfiguracao() {
        this.chavePrivada = "";
        this.chavePublica = "";
    }

    public int getAcaoObjecao() {
        return acaoObjecao;
    }

    public void setAcaoObjecao(int acaoObjecao) {
        this.acaoObjecao = acaoObjecao;
    }

    public String getClientIdOauth() {
        return clientIdOauth;
    }

    public void setClientIdOauth(String clientIdOauth) {
        this.clientIdOauth = clientIdOauth;
    }

    public String getClientSecretOauth() {
        return clientSecretOauth;
    }

    public void setClientSecretOauth(String clientSecretOauth) {
        this.clientSecretOauth = clientSecretOauth;
    }

    public List<SelectItem> getWeebHookEvents() {
        return weebHookEvents;
    }

    public void setWeebHookEvents(List<SelectItem> weebHookEvents) {
        this.weebHookEvents = weebHookEvents;
    }

    public String getEventWeebHook() {
        return eventWeebHook;
    }

    public void setEventWeebHook(String eventWeebHook) {
        this.eventWeebHook = eventWeebHook;
    }

    public boolean isAcaoObjecaoLeadAtaulizarQualquer() {
        return getAcaoObjecao() == AcaoObjcaoLeadEnum.ATUALIZAR_QUALQUER.getCodigo();
    }

    public boolean isAcaoObjecaoLeadApenasDefinitiva() {
        return getAcaoObjecao() == AcaoObjcaoLeadEnum.APENAS_DEFINTIVA.getCodigo();
    }

    public boolean isConfigAtualizarAlunoRdStationMarketing() {
        return configAtualizarAlunoRdStationMarketing;
    }

    public void setConfigAtualizarAlunoRdStationMarketing(boolean configAtualizarAlunoRdStationMarketing) {
        this.configAtualizarAlunoRdStationMarketing = configAtualizarAlunoRdStationMarketing;
    }

    public String getAccessTokenRdStationMarketing() {
        return accessTokenRdStationMarketing;
    }

    public void setAccessTokenRdStationMarketing(String accessTokenRdStationMarketing) {
        this.accessTokenRdStationMarketing = accessTokenRdStationMarketing;
    }

    public String getRefreshTokenRdStationMarketing() {
        return refreshTokenRdStationMarketing;
    }

    public void setRefreshTokenRdStationMarketing(String refreshTokenRdStationMarketing) {
        this.refreshTokenRdStationMarketing = refreshTokenRdStationMarketing;
    }
}

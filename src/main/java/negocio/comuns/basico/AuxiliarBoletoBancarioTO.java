package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.jboleto.JBoleto;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by GlaucoT on 14/05/2015
 */
public class AuxiliarBoletoBancarioTO extends SuperTO {

    private JBoleto boleto;

    public JRDataSource getBoletoDatasource() {
        List<JBoleto> boletos = new ArrayList<JBoleto>();
        boletos.add(boleto);
        return new JRBeanCollectionDataSource(boletos);
    }

    public JBoleto getBoleto() {
        return boleto;
    }

    public void setBoleto(JBoleto boleto) {
        this.boleto = boleto;
    }

}

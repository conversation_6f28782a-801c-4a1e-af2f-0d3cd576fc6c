package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

/*
 * Created by <PERSON><PERSON>
 */
public class EnderecoWS extends SuperTO {

    private Integer codigo;
    private String endereco;
    private String complemento;
    private String numero;
    private String bairro;
    private String cep;
    private String tipoEndereco;
    private Boolean enderecoCorrespondencia;
    private String tipoEnderecoDescricao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getTipoEndereco() {
        return tipoEndereco;
    }

    public void setTipoEndereco(String tipoEndereco) {
        this.tipoEndereco = tipoEndereco;
    }

    public Boolean getEnderecoCorrespondencia() {
        return enderecoCorrespondencia;
    }

    public void setEnderecoCorrespondencia(Boolean enderecoCorrespondencia) {
        this.enderecoCorrespondencia = enderecoCorrespondencia;
    }

    public String getTipoEnderecoDescricao() {
        return tipoEnderecoDescricao;
    }

    public void setTipoEnderecoDescricao(String tipoEnderecoDescricao) {
        this.tipoEnderecoDescricao = tipoEnderecoDescricao;
    }
}
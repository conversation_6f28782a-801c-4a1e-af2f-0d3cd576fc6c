/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import br.com.pactosolucoes.enumeradores.CampoBIEnum;
import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class CampoBIVO extends SuperVO{
    
    private CampoBIEnum campoBiEnum;
    private Double valor;
    private Date data;
    private Integer mes;
    private Integer empresa;
    private Integer ano;

    public CampoBIVO(CampoBIEnum campoBiEnum, Double valor, Date data, Integer mes, Integer ano, Integer empresa) {
        this.campoBiEnum = campoBiEnum;
        this.valor = valor;
        this.data = data;
        this.mes = mes;
        this.ano = ano;
        this.empresa = empresa;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
    
    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }
    
    public CampoBIVO(){
        
    }
    
    public CampoBIEnum getCampoBiEnum() {
        return campoBiEnum;
    }

    public void setCampoBiEnum(CampoBIEnum campoBiEnum) {
        this.campoBiEnum = campoBiEnum;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }
    
    
    
}

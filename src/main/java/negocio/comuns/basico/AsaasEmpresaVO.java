package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.CompanyTypeAsaasEnum;
import negocio.comuns.financeiro.enumerador.StatusAsaasEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 25/05/2023
 */

public class AsaasEmpresaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    private int empresa;
    private String id;
    private String apiKey;
    private String walletId;
    private Date dataCriacao;
    private String paramsEnvio;
    private String paramsResposta;
    private CompanyTypeAsaasEnum companyType;

    //Atributos Transientes
    protected String name;
    private String email;
    private String cpfCnpj;
    private String phone;
    private String mobilePhone;
    private String address;
    private String addressNumber;
    private String complement;
    private String province; //Bairro
    private String postalCode; //CEP
    private AccountNumberAsaasDTO accountNumberAsaasDTO;
    private TipoPessoa personType;
    private StatusAsaasEnum status;
    private AmbienteEnum ambienteEnum;


    public AsaasEmpresaVO() {
    }

    public AsaasEmpresaVO(JSONObject json) {
        this.id = json.optString("id");
        this.apiKey = json.optString("apiKey");
        this.walletId = json.optString("walletId");
        this.name = json.optString("name");
        this.email = json.optString("email");
        this.cpfCnpj = json.optString("cpfCnpj");
        this.companyType = CompanyTypeAsaasEnum.obterPorDescricao(json.optString("companyType"));
        this.phone = json.optString("phone");
        this.mobilePhone = json.optString("mobilePhone");
        this.address = json.optString("address");
        this.addressNumber = json.optString("addressNumber");
        this.complement = json.optString("complement");
        this.province = json.optString("province");
        this.postalCode = json.optString("postalCode");
        this.personType = TipoPessoa.obterPorLabel(json.optString("personType"));
        if (json.optJSONObject("accountNumber") != null) {
            this.accountNumberAsaasDTO = new AccountNumberAsaasDTO(json.optJSONObject("accountNumber"));
        }
    }


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getEmpresa() {
        return empresa;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getWalletId() {
        return walletId;
    }

    public void setWalletId(String walletId) {
        this.walletId = walletId;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public String getParamsEnvio() {
        return paramsEnvio;
    }

    public void setParamsEnvio(String paramsEnvio) {
        this.paramsEnvio = paramsEnvio;
    }

    public String getParamsResposta() {
        return paramsResposta;
    }

    public void setParamsResposta(String paramsResposta) {
        this.paramsResposta = paramsResposta;
    }


    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public CompanyTypeAsaasEnum getCompanyType() {
        return companyType;
    }

    public void setCompanyType(CompanyTypeAsaasEnum companyType) {
        this.companyType = companyType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddressNumber() {
        return addressNumber;
    }

    public void setAddressNumber(String addressNumber) {
        this.addressNumber = addressNumber;
    }

    public String getComplement() {
        return complement;
    }

    public void setComplement(String complement) {
        this.complement = complement;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public TipoPessoa getPersonType() {
        return personType;
    }

    public void setPersonType(TipoPessoa personType) {
        this.personType = personType;
    }

    public AccountNumberAsaasDTO getAccountNumberAsaasDTO() {
        return accountNumberAsaasDTO;
    }

    public void setAccountNumberAsaasDTO(AccountNumberAsaasDTO accountNumberAsaasDTO) {
        this.accountNumberAsaasDTO = accountNumberAsaasDTO;
    }

    public StatusAsaasEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAsaasEnum status) {
        this.status = status;
    }

    public String getCompanyTypeApresentar() {
        if (getCompanyType() != null) {
            return getCompanyType().getDescricao();
        }
        return "";
    }

    public String getPhoneApresentar() {
        if (!UteisValidacao.emptyString(getPhone())) {
            return Formatador.formataTelefoneZW(getPhone());
        }
        return "";
    }

    public String getMobilePhoneApresentar() {
        if (!UteisValidacao.emptyString(getMobilePhone())) {
            return Formatador.formataTelefoneZW(getMobilePhone());
        }
        return "";
    }

    public String getCnpjApresentar() {
        if (!UteisValidacao.emptyString(getCpfCnpj())) {
            return Uteis.formatarCpfCnpj(getCpfCnpj(), false);
        }
        return "";
    }

    public String getCepApresentar() {
        if (!UteisValidacao.emptyString(getPostalCode())) {
            return Uteis.formatarCEP(getPostalCode(), false);
        }
        return "";
    }

    public String getApiKeyApresentarResumida() {
        if (!UteisValidacao.emptyString(getApiKey())) {
            return getApiKey().substring(0, 32) + "...";
        }
        return "";
    }

    public AmbienteEnum getAmbienteEnum() {
        if (ambienteEnum == null) {
            return AmbienteEnum.PRODUCAO;
        }
        return ambienteEnum;
    }

    public boolean isAmbienteSandbox() {
        if (getAmbienteEnum() != null && getAmbienteEnum().equals(AmbienteEnum.HOMOLOGACAO)) {
            return true;
        }
        return false;
    }

    public void setAmbienteEnum(AmbienteEnum ambienteEnum) {
        this.ambienteEnum = ambienteEnum;
    }
}

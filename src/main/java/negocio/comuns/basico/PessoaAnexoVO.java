package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.PessoaAnexoEnum;
import negocio.comuns.contrato.ContratoTextoPadraoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.integracao.pjbank.enums.TipoAnexo;

import java.util.Date;

public class PessoaAnexoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private PessoaVO pessoa;
    private UsuarioVO usuarioResponsavel;
    private PessoaAnexoEnum tipoAnexo;
    private String anexo1;
    private String anexo2;
    private Date lancamento;
    @NaoControlarLogAlteracao
    private String anexo1_Apresentar;
    @NaoControlarLogAlteracao
    private String anexo2_Apresentar;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public PessoaAnexoEnum getTipoAnexo() {
        return tipoAnexo;
    }

    public void setTipoAnexo(PessoaAnexoEnum tipoAnexo) {
        this.tipoAnexo = tipoAnexo;
    }

    public void setAnexo1(String anexo1) {
        this.anexo1 = anexo1;
        if (anexo1 != null && !UteisValidacao.emptyString(anexo1) && !anexo1.contains("image_icon.jpg")) {
            setAnexo1_Apresentar(Uteis.getPaintFotoDaNuvem(anexo1));
        }
    }

    public String getAnexo1() {
        return anexo1;
    }

    public void setAnexo2(String anexo2) {
        this.anexo2 = anexo2;
        if (anexo2 != null && !UteisValidacao.emptyString(anexo2) && !anexo2.contains("image_icon.jpg")) {
            setAnexo2_Apresentar(Uteis.getPaintFotoDaNuvem(anexo2));
        }
    }

    public String getAnexo2() {
        return anexo2;
    }

    public Date getLancamento() {
        return lancamento;
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }

    public String getAnexo1_Apresentar() {
        return anexo1_Apresentar;
    }

    public void setAnexo1_Apresentar(String anexo1_Apresentar) {
        this.anexo1_Apresentar = anexo1_Apresentar;
    }

    public String getAnexo2_Apresentar() {
        return anexo2_Apresentar;
    }

    public void setAnexo2_Apresentar(String anexo2_Apresentar) {
        this.anexo2_Apresentar = anexo2_Apresentar;
    }

    public void initImagens() {
        String imagemPadrao = Uteis.getPaintImagemDaNuvem("", "../");
        if (getAnexo1() == null || getAnexo1().isEmpty())
            setAnexo1_Apresentar(imagemPadrao);
        if (getAnexo2() == null || getAnexo2().isEmpty())
            setAnexo2_Apresentar(imagemPadrao);
    }
}

package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.Uteis;

import java.sql.ResultSet;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * Created by <PERSON><PERSON> on 24/05/2018.
 */
public class AlunosAdimplentes extends BiDCCRelAbstractFactory {
    protected AlunosAdimplentes() {
    }

    @Override
    public Double getSum() throws Exception{
        return getFacade().getZWFacade().getContratoRecorrencia().somarAdimplentesRecorrentes(codigoEmpresa, dataBase, colaboradores,convenios);
    }

    @Override
    public ResultSet getResult() throws Exception {
        return null;
    }

    @Override
    public List getList() throws Exception {
        return getFacade().getZWFacade().getContratoRecorrencia().consultarAdimplentesRecorrencia(codigoEmpresa, dataBase, colaboradores,convenios, Uteis.NIVELMONTARDADOS_ROBO);
    }

    @Override
    public Integer getCount() throws Exception {
        return getFacade().getZWFacade().getContratoRecorrencia().contarAdimplentesRecorrentes(codigoEmpresa, dataBase, colaboradores,convenios);
    }
}
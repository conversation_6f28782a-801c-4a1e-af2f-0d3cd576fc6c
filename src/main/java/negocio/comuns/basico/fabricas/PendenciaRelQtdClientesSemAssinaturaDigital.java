package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdClientesSemAssinaturaDigital extends PendenciaRelAbstractFactory {

    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getCliente().consultarClienteSemAssinaturaDigital(codigoEmpresaFiltro, colaboradores,confPaginacao, getDataBaseInicialFiltroBI(), assinaturaCancelamento);
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getCliente().contarClienteSemAssinaturaDigital(codigoEmpresaFiltro,
                ( UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores ), getDataBaseInicialFiltroBI(), assinaturaCancelamento);
    }

}

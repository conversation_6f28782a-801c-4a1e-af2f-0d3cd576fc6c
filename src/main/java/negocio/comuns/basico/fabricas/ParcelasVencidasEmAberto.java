package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.Calendario;

import java.sql.ResultSet;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * Created by <PERSON> on 22/06/2016.
 */
public class ParcelasVencidasEmAberto extends BiDCCRelAbstractFactory {

    protected ParcelasVencidasEmAberto() {
    }

    @Override
    public Double getSum() throws Exception{
        return getFacade().getMovParcela().consultarParcelasDCCValor(codigoEmpresa, this.getDataBaseFiltroBIDCC(), getConvenios(), colaboradores);
    }

    @Override
    public Integer getCount() throws Exception {
        return getFacade().getMovParcela().consultarParcelasDCCCount(codigoEmpresa, this.getDataBaseFiltroBIDCC(), getConvenios(), colaboradores);
    }

    @Override
    public List getList() throws Exception {
        return null;
    }

    @Override
    public ResultSet getResult() throws Exception {
        return  null;
    }
}
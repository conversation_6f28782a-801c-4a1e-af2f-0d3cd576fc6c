package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdParcelaEmAbertoColaborador extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getMovParcela().consultarPendenciaParcelaEmAbertoAPagarColaborador(
                codigoEmpresaFiltro, colaboradores, confPaginacao, getDataBaseInicialFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getMovParcela().contarPendenciaParcelaEmAbertoAPagarColaborador(codigoEmpresaFiltro,
                (UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores ),getDataBaseInicialFiltroBI());
    }
}
package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdVisitanteCadastroIncompleto extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getClienteMensagem().consultarVisitanteMensagemCadastroIncompleto(
                codigoEmpresaFiltro, colaboradores, false,confPaginacao, getDataBaseInicialFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getClienteMensagem()
                .contarPendenciaClienteMensagemPorEmpresaColaboradorCadastroIncompleto(
                    codigoEmpresaFiltro,(UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores), "VI", false, getDataBaseInicialFiltroBI());
    }
}
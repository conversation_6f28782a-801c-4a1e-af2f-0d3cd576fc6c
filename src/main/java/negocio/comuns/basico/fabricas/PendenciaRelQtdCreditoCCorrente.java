package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdCreditoCCorrente extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getMovimentoContaCorrenteCliente().consultarPendenciasClienteCreditoContaCorrente(
                codigoEmpresaFiltro, colaboradores,confPaginacao, getDataBaseInicialFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getMovimentoContaCorrenteCliente().contarPendenciasClienteCreditoContaCorrente(codigoEmpresaFiltro,
                (UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores ), getDataBaseInicialFiltroBI());
    }
}
package negocio.comuns.basico.fabricas;

import java.sql.ResultSet;

public class PendenciaRelQtdCartoesAVencer extends PendenciaRelAbstractFactory  {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getContratoRecorrencia()
                .consultarPendenciaClienteCartaoAVencer(codigoEmpresaFiltro,getDataBaseInicialFiltroBI(), getDataBaseFiltroBIDCC(),
                        getIdsColaboradores(),confPaginacao);
    }

    @Override
    public ResultSet getCount() throws Exception {
        return null;
    }
}
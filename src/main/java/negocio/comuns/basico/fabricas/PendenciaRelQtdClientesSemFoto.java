package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdClientesSemFoto extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getCliente().consultarPessoaSemFoto(false, codigoEmpresaFiltro, colaboradores,confPaginacao, getDataBaseInicialFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getCliente().consultarPessoaSemFoto(true, codigoEmpresaFiltro, colaboradores, confPaginacao, getDataBaseInicialFiltroBI());
    }
}
package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdClientesSemProdutos extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getProdutoRel().contarClientesSemProdutosSintetico(codigoEmpresaFiltro, colaboradores,  getDataBaseInicialFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getProdutoRel().contarClientesSemProdutosSintetico(codigoEmpresaFiltro,
                ( UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores ), getDataBaseInicialFiltroBI());
    }
}
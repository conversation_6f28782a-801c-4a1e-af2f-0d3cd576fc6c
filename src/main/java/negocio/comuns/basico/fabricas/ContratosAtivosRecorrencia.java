package negocio.comuns.basico.fabricas;

/**
 * Created by <PERSON> on 22/06/2016.
 */

import negocio.comuns.utilitarias.Uteis;

import java.sql.ResultSet;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

public class ContratosAtivosRecorrencia extends BiDCCRelAbstractFactory {
    protected ContratosAtivosRecorrencia() {
    }

    @Override
    public Double getSum() throws Exception{
        return 0.0;
    }

    @Override
    public ResultSet getResult() throws Exception {
        return null;
    }

    @Override
    public List getList() throws Exception {
        return getFacade().getZWFacade().getContratoRecorrencia().consultarContratosRecorrencia(codigoEmpresa, this.getDataBaseFiltroBI(), null, this.getConvenios(), Uteis.NIVELMONTARDADOS_ROBO);
    }

    @Override
    public Integer getCount() throws Exception {
        return getFacade().getContratoRecorrencia().contarAtivosRecorrentes(codigoEmpresa, this.getDataBaseFiltroBI(), null, this.getConvenios());
    }
}
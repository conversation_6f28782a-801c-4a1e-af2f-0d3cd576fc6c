package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.Uteis;

import java.sql.ResultSet;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * Created by <PERSON> on 22/06/2016.
 */
public class ParcelasCanceladas extends BiDCCRelAbstractFactory {
    protected ParcelasCanceladas() {
    }

    @Override
    public Double getSum() throws Exception{
        return 0.0;
    }

    @Override
    public ResultSet getResult() throws Exception {
        return null;
    }

    @Override
    public List getList() throws Exception {
        return getFacade().getZWFacade().getContratoRecorrencia().consultarParcelasRecorrencia(
                this.codigoEmpresa, "DISTINCT mpa.codigo", false, null, null, "= 'CA'", Uteis.obterPrimeiroDiaMes(getDataBaseFiltroBI()), this.getDataBaseFiltroBI(),
                getConvenios(), null, null, this.isSomenteParcelasMes(), this.isSomenteParcelasForaMes());
    }

    @Override
    public Integer getCount() throws Exception {
        return  getFacade().getContratoRecorrencia().contarParcelasCanceladas(codigoEmpresa, "DISTINCT mpa.codigo, mpa.valorparcela", true, new String[]{"codigo", "valorparcela"}, null, "= 'CA'",
                Uteis.obterPrimeiroDiaMes(getDataBaseFiltroBI()), this.getDataBaseFiltroBI(), getConvenios(), "", null, isSomenteParcelasMes(), isSomenteParcelasForaMes());
    }
}
package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdClienteSemGeoLocalizacao extends PendenciaRelAbstractFactory {
    protected PendenciaRelQtdClienteSemGeoLocalizacao() {}


    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getCliente().consultarClienteSemGeoLocalizacao(
                codigoEmpresaFiltro, colaboradores, false,confPaginacao, getDataBaseInicialFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getCliente().contarPendenciaClientePorEmpresaColaboradorSemGeoLocalizacao(
                codigoEmpresaFiltro, ( UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores ), "AT", false, getDataBaseInicialFiltroBI());
    }
}

package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdClientesComTrancamentoVencidos extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getCliente().consultarTrancamentosVencidos(codigoEmpresaFiltro, getDataBaseInicialFiltroBI(),
                Uteis.obterPrimeiroDiaMes(getDataBaseFiltroBI()), getDataBaseFiltroBI(), colaboradores,confPaginacao);
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getCliente().contarTrancamentosVencidosContratos(codigoEmpresaFiltro,
                getDataBaseInicialFiltroBI(), Uteis.obterPrimeiroDiaMes(getDataBaseFiltroBI()),getDataBaseFiltroBI(),
                ( UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores ));
    }
}
package negocio.comuns.basico.fabricas;

import java.sql.ResultSet;

public class PendenciaRelQtdClientesTransferidosContratoCancelado extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getJustificativaOperacao()
                .consultarJustificativaContratoCanceladoDeOutraUnidade(codigoEmpresaFiltro);
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getJustificativaOperacao()
                .consultarTotalJustificativaContratoCanceladoDeOutraUnidade(codigoEmpresaFiltro);
    }

    @Override
    public String trataMensagem(String mensagem) {
        return "O Contrato " + mensagem.split("\\.")[1] + " foi Cancelado na Unidade " + mensagem.split("\\.")[3];
    }

    @Override
    public boolean ignorarSeForZero() {
        return true;
    }
}
package negocio.comuns.basico.fabricas;

import java.sql.ResultSet;
import java.util.Collection;
import java.util.Collections;

public class PendenciaRelQtdClientesMesmoCartao extends PendenciaRelAbstractFactory {

    public static final String NOME_CLIENTE = "NC";

    @Override
    public ResultSet getList() throws Exception {
        return  facadeFactory.getContratoRecorrencia()
                .consultarPendenciaClientesMesmoCartao(codigoEmpresaFiltro, false, getDataBaseInicialFiltroBI(),
                        getDataBaseFiltroBIDCC(),(String) parametros.get(NOME_CLIENTE), Collections.EMPTY_LIST, somenteClientesAtivos, confPaginacao);
    }

    @Override
    public ResultSet getCount() throws Exception {
        return null;
    }
}

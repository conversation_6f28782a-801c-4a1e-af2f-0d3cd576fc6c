package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdCadastroIncompleto extends PendenciaRelAbstractFactory {
    protected PendenciaRelQtdCadastroIncompleto() {}

    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getClienteMensagem().consultarClienteMensagemCadastroIncompleto(
                codigoEmpresaFiltro, colaboradores, false,confPaginacao, getDataBaseInicialFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getClienteMensagem().contarPendenciaClienteMensagemPorEmpresaColaboradorCadastroIncompleto(
                codigoEmpresaFiltro, ( UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores ), "AT", false, getDataBaseInicialFiltroBI());
    }
}
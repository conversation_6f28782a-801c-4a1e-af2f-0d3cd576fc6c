package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.Uteis;

import java.sql.ResultSet;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * Created by <PERSON> on 22/06/2016.
 */
public class ContratosCanceladosRecorrencia extends BiDCCRelAbstractFactory {

    protected ContratosCanceladosRecorrencia() {
    }

    @Override
    public Double getSum() throws Exception{
        return 0.0;
    }

    @Override
    public ResultSet getResult() throws Exception {
        return null;
    }

    @Override
    public List getList() throws Exception {
        return getFacade().getZWFacade().getContratoRecorrencia().consultarCanceladosRecorrencia(
                codigoEmpresa, this.getDataBaseFiltroBI(), null, getConvenios(), Uteis.NIVELMONTARDADOS_ROBO);
    }

    @Override
    public Integer getCount() throws Exception {
        return getFacade().getContratoRecorrencia().contarCanceladosAutomaticamente(codigoEmpresa, this.getDataBaseFiltroBI(), null, getConvenios());
    }
}

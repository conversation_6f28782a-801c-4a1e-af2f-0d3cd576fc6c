package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.Uteis;

import java.sql.ResultSet;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * Created by Rafael on 22/06/2016.
 */
public class CartoesCreditoVencidos extends BiDCCRelAbstractFactory {
    protected CartoesCreditoVencidos() {
    }

    @Override
    public Double getSum() throws Exception{
        return 0.0;
    }

    @Override
    public ResultSet getResult() throws Exception {
        return null;
    }

    @Override
    public List getList() throws Exception {
      return null;
    }

    @Override
    public Integer getCount() throws Exception {
        return getFacade().getContratoRecorrencia().contarPendenciaClienteMensagemCartaoVencido(codigoEmpresa, this.getDataBaseFiltroBI(), colaboradores);
    }
}
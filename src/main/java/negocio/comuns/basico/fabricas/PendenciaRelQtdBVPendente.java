package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdBVPendente extends PendenciaRelAbstractFactory {
    protected PendenciaRelQtdBVPendente(){}

    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getClienteMensagem().consultarClienteMensagemPorEmpresaQuestionarioCliente(false,
                codigoEmpresaFiltro, ( UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores ), false,confPaginacao, getDataBaseInicialFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return  facadeFactory.getClienteMensagem().
                consultarClienteMensagemPorEmpresaQuestionarioCliente(true,
                        codigoEmpresaFiltro, ( UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores ), false,confPaginacao, getDataBaseInicialFiltroBI());
    }
}
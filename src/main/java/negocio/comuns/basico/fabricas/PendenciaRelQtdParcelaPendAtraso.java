package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdParcelaPendAtraso extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getMovParcela().consultarPendenciaParcelaEmAbertoAtraso(codigoEmpresaFiltro,
                Calendario.hoje(), colaboradores, confPaginacao,getDataBaseInicialFiltroBI(), getDataBaseFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getMovParcela().contarPendenciaParcelaEmAbertoAtraso(codigoEmpresaFiltro,
                getDataBaseInicialFiltroBI(), getDataBaseFiltroBI(), colaboradores);
    }
}
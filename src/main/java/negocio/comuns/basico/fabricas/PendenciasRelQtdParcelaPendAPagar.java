package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciasRelQtdParcelaPendAPagar extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getMovParcela().consultarPendenciaParcelaEmAbertoAPagar(
                codigoEmpresaFiltro, colaboradores, confPaginacao, getDataBaseInicialFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getMovParcela().contarPendenciaParcelaEmAbertoAPagar(codigoEmpresaFiltro, getDataBaseInicialFiltroBI(), getDataBaseFiltroBI(),
                (UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores), false, false, true, null);
    }
}
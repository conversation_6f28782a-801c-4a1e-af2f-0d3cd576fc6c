package negocio.comuns.basico.fabricas;

import java.sql.ResultSet;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * Created by <PERSON><PERSON> on 24/05/2018.
 */
public class CartoesComProblema extends BiDCCRelAbstractFactory {
    protected CartoesComProblema() {
    }

    @Override
    public Double getSum() throws Exception{
        return 0.0;
    }

    @Override
    public ResultSet getResult() throws Exception {
        return null;
    }

    @Override
    public List getList() throws Exception {
        return null;
    }

    @Override
    public Integer getCount() throws Exception {
        return getFacade().getAutorizacaoCobrancaCliente().contarPendenciaCartaoProblema(codigoEmpresa, colaboradores);
    }
}
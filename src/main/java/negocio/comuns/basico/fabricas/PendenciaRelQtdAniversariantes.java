package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdAniversariantes extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getCliente().consultarAniversarioClientes(
                codigoEmpresaFiltro, colaboradores,confPaginacao,getDataBaseInicialFiltroBI(), getDataBaseFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getCliente().contarAniversarioClientes(codigoEmpresaFiltro,
                (UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores ),getDataBaseFiltroBI(), getDataBaseInicialFiltroBI());
    }
}
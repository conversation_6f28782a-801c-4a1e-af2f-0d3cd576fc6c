package negocio.comuns.basico.fabricas;

import java.sql.ResultSet;

public class PendenciaRelQtdCartoesComProblema extends PendenciaRelAbstractFactory  {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getAutorizacaoCobrancaCliente().consultarPendenciaClienteCartaoComProblema(codigoEmpresaFiltro, getIdsColaboradores(), confPaginacao, false);
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getAutorizacaoCobrancaCliente().contarPendenciaCartaoProblemaRS(codigoEmpresaFiltro, getIdsColaboradores());
    }
}
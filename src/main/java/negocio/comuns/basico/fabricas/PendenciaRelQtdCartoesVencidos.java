package negocio.comuns.basico.fabricas;

import java.sql.ResultSet;

public class PendenciaRelQtdCartoesVencidos extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return  facadeFactory.getContratoRecorrencia()
                .consultarPendenciaClienteMensagemCartaoVencido(codigoEmpresaFiltro,
                        getDataBaseInicialFiltroBI(), getDataBaseFiltroBIDCC(), getIdsColaboradores(), confPaginacao);
    }

    @Override
    public ResultSet getCount() throws Exception {
        return null;
    }
}
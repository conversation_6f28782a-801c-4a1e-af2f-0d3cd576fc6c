package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import annotations.arquitetura.PassWord;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import negocio.armario.AluguelArmarioVO;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.crm.IndicacaoVO;
import negocio.comuns.crm.ObjecaoVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.OrcamentoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.Calendar;



/**
 * Reponsável por manter os dados da entidade Cliente. Classe do tipo VO - Value
 * Object composta pelos atributos da entidade com visibilidade protegida e os
 * métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class ClienteVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String situacao;
    protected String matricula;
    protected Integer codigoMatricula;
    protected String codAcesso;
    protected String codAcessoAlternativo;
    protected String banco;
    protected String agencia;
    protected String agenciaDigito;
    protected String conta;
    protected String contaDigito;
    protected String identificadorParaCobranca;
    @NaoControlarLogAlteracao
    protected Boolean abilitarEmpresa;
    @NaoControlarLogAlteracao
    protected Boolean abilitarUsuario;
    @NaoControlarLogAlteracao
    protected Boolean apresentarRichModalErro;
    @NaoControlarLogAlteracao
    protected Boolean apresentarRichModalCadastroRapido;
    @NaoControlarLogAlteracao
    protected Boolean apresentarBotaoTransferirClienteEmpresa;
    private ClienteSituacaoVO clienteSituacaoVO_Apresentar;
    private Long matriculaExterna = 0L;
    private Long idExterno;
    private UsuarioMovelVO usuarioMovelVO;
    private Date dataValidadeCarteirinha;
    @NaoControlarLogAlteracao
    private Integer codigoReposicaoExperimental = null;
    private String anexo;
    private Date dataCadastroAnexo;
    private String nomeAnexo;
    @NaoControlarLogAlteracao
    private String urlCompletaAnexo;
    private String convenioClienteCobranca;
    private String saldoParceiroFidelidade;

    //Usada pelo autoatendimento
    private Integer pontuacao = 0;
    private Integer codigoMarcadoFavorito;

    private String tokenGoGood;

    /**
     * Atributo responsável por manter os objetos da classe
     * <code>ClienteSituacao</code>.
     */
    @NaoControlarLogAlteracao
    private List<ClienteSituacaoVO> clienteSituacaoVOs;
    /**
     * Atributo responsável por manter os objetos da classe
     * <code>Familiar</code>.
     */
    @Lista
    private List<FamiliarVO> familiarVOs;
    @NaoControlarLogAlteracao
    private List<FamiliarVO> familiaresExcluidosVOs;
    /**
     * Atributo responsável por manter os objetos da classe
     * <code>ClienteGrupo</code>.
     */
    @Lista
    private List<ClienteGrupoVO> clienteGrupoVOs;
    /**
     * Atributo responsável por manter os objetos da classe
     * <code>ClienteClassificacao</code>.
     */
    @Lista
    private List<ClienteClassificacaoVO> clienteClassificacaoVOs;
    /**
     * Atributo responsável por manter os objetos da classe
     * <code>Vinculo</code>.
     */
    @Lista
    @ListJson(clazz = VinculoVO.class)
    private List<VinculoVO> vinculoVOs;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Pessoa </code>.
     */
    @NaoControlarLogAlteracao
    @FKJson
    protected PessoaVO pessoa;

    private ProdutoVO freePass;
    @NaoControlarLogAlteracao
    private Integer responsavelFreePass;
    @NaoControlarLogAlteracao
    private String nomeResponsavelFreePass;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Categoria </code>.
     */
    @ChaveEstrangeira
    @FKJson
    protected CategoriaVO categoria;
    @NaoControlarLogAlteracao
    protected String msgErroExisteCliente;
    @ChaveEstrangeira
    @FKJson
    protected EmpresaVO empresa;
    @NaoControlarLogAlteracao
    protected String abaSelecionada;
    @NaoControlarLogAlteracao
    protected Boolean dadosClienteJaVisualizados;
    @NaoControlarLogAlteracao
    protected Boolean apresentarAbaContrato;
    @NaoControlarLogAlteracao
    protected List<ClienteMensagemVO> listaClienteMensagem;
    @NaoControlarLogAlteracao
    protected Boolean abaMedico;
    @NaoControlarLogAlteracao
    protected Boolean abaConsultor;
    @NaoControlarLogAlteracao
    protected Boolean abaObjetivo;
    @NaoControlarLogAlteracao
    protected Boolean abaObservacao;
    @NaoControlarLogAlteracao
    protected Boolean abaCatraca;
    @NaoControlarLogAlteracao
    protected Boolean abaAvisoCliente;
    @NaoControlarLogAlteracao
    protected List<ClienteMensagemVO> listaMensagemAvisoCliente;
    @NaoControlarLogAlteracao
    protected Boolean apresentarBotoes;
    @NaoControlarLogAlteracao
    protected String dataInicioContrato;
    @NaoControlarLogAlteracao
    protected String dataTerminoContrato;
    protected Double saldoContaCorrente;
    @NaoControlarLogAlteracao
    protected List<MovProdutoVO> listaHistoricoProduto;
    @NaoControlarLogAlteracao
    protected List<MovPagamentoVO> listaHistoricoPagamento;
    @NaoControlarLogAlteracao
    protected List<MovParcelaVO> listaParcelas;
    @NaoControlarLogAlteracao
    protected List<MovProdutoVO> listaProdutosComValidade;
    @NaoControlarLogAlteracao
    protected SituacaoClienteSinteticoDWVO situacaoClienteSinteticoVO;
    @NaoControlarLogAlteracao
    protected Integer codigoPassivo;
    @NaoControlarLogAlteracao
    protected Integer codigoIndicado;
    @NaoControlarLogAlteracao
    protected Boolean selecionado;
    @NaoControlarLogAlteracao
    protected AcessoClienteVO uaCliente;
    @NaoControlarLogAlteracao
    private List<AutorizacaoCobrancaClienteVO> autorizacoes;
    @NaoControlarLogAlteracao
    private AutorizacaoCobrancaClienteVO autorizacao;
    @NaoControlarLogAlteracao
    private boolean dadosSinteticoPreparados = false;
    private String situacaoClienteApresentar = "";
    @NaoControlarLogAlteracao
    private String situacaoContrato;
    @NaoControlarLogAlteracao
    private UsuarioVO usuarioResponsavelVinculo = new UsuarioVO();
    @NaoControlarLogAlteracao
    private boolean atualizarTreino = true;
    private boolean parqPositivo = false;
    private PessoaVO pessoaResponsavel = new PessoaVO();
    private boolean utilizarResponsavelPagamento = false;
    @NaoControlarLogAlteracao
    private List<ClienteVO> clienteDenpentedes;
    @NaoControlarLogAlteracao
    protected Boolean apresentarBotaoUsarResponsavel;
    private List<AluguelArmarioVO> armariosAlugados;
    private List<OrcamentoVO> orcamentos;
    private String origem;
    private Integer novaMatricula;

    @NaoControlarLogAlteracao
    private UsuarioVO usuarioAux; // objecto transient.

    @NaoControlarLogAlteracao
    private String observacao;
    private boolean verificarCliente;
    private Date verificadoEm;
    private UsuarioVO usuarioVerificacao;
    public static String FLAG_PERMITE_GRAVAR_CLIENTE = "FLAG_PERMITE_GRAVAR_CLIENTE";
    public static String ULTIMO_CLIENTE_EDITADO = "ULTIMO_CLIENTE_EDITADO"; //usado para permitir fazer mais de uma edição no cadastro do aluno.
    private Double porcentagemDescontoBoletoPagAntecipado = 0.0;

    private Boolean sesc = Boolean.FALSE;
    private Double renda;
    private String matriculaSesc;
    private String nomeSocial;
    private Date validadeCartaoSesc;

    private String professor;

    private ObjecaoVO objecao;

    /**
     * Informa quais serão os consultores responsaveis para quais os agendamentos serão alterados.
     * A chave do mapa contém o codigo do consultor responsavel atualmente pelos agendamentos. O valor para chave contem o codigo no novo consultor responsavel.
     */
    private Map<Integer, Integer> agendamentosAlterar; // objeto transient.

    /**
     * Construtor padrão da classe
     * <code>Cliente</code>. Cria uma nova instância desta entidade,
     * inicializando automaticamente seus atributos (Classe VO).
     */
    private boolean temMaisDeUmaDigital = false;

    /**
     * Unique Token para validar acesso do aluno com Gympass
     */
    private String gympasUniqueToken = "";
    private String gympassTypeNumber = "";

    private String empresaNome;
    private String mensagemLogVendasOnline = "";
    private Integer codAmigoFit;
    private String usernameAmigoFit;
    @PassWord
    private String senhaUsuarioAmigoFit;
    private BancoVO bancoAmigoFit;
    private int agenciaAmigoFit;
    private int digitoAgenciaAmigoFit;
    private int contaAmigoFit;
    private int digitoContaAmigoFit;
    private Boolean contaCorrenteAmigoFit;
    private Boolean contaPoupancaAmigoFit;
    private Date sincronizadoRedeEmpresa;
    @NaoControlarLogAlteracao
    private Integer titularPlanoCompartilhado;
    @NaoControlarLogAlteracao
    private boolean deveAtualizarDependentesSintetico = false;
    private String situacaoSPC;
    private Date dataInclusaoSPC;
    private boolean bloquearInclusaoSPC = false;
    private boolean possuiGymPass = false;
    private boolean possuiFreePass = false;
    private boolean possuiTotalPass = false;

    @NaoControlarLogAlteracao
    private IndicacaoVO indicacao;

    @NaoControlarLogAlteracao
    private FornecedorVO empresaFornecedor;

    /* ------ CAMPOS DE INTEGRAÇÃO SESI-CE ------- */
    /*    *  Esses campos armazenam as informações necessárias no perfil do aluno para integração com as academias conveniadas com o Sesi-Ceará
    * */

    private String necessidadesEspeciaisSesiCe;

    private Date dataValidadeCadastroSesiCe;

    private String razaoSocialEmpresaSesiCe;

    private String statusMatriculaSesiCe;

    private Date dataInclusaoClienteRestricaoRedeEmpresa;

    private Date dataSincronizacaoFranqueadora;
    private Long idManyChat;
    private Date dataSincronizacaoManyChat;

    public ClienteVO() {
        super();
        inicializarDados();
    }

    public ClienteVO(String nome){
        super();
        pessoa = new PessoaVO();
        pessoa.setNome(nome);
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ClienteVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @exception ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDados(ClienteVO obj, ConfiguracaoSistemaVO configuracaoSistema) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (configuracaoSistema.getMatriculaOb() && obj.getMatricula().trim().isEmpty()) {
            throw new Exception("O campo MATRÍCULA (Cliente) deve ser informado.");
        }
        if (obj.pessoa.getNome().trim().isEmpty()) {
            throw new Exception("O campo NOME (Aba - Dados Pessoais) deve ser informado.");
        }
        if(Pattern.compile("\\d*[!@#$%&\\*\\{\\}\\[\\]\\?:><,|\\\\/\"\\+]\\d*").matcher(obj.pessoa.getNome()).find()){
             throw new ConsistirException("O campo NOME não pode conter caracteres especiais (!@#$%&*}{][?:><,\\|/\"+).");
        }
        if (configuracaoSistema.getDataNascOb() && obj.pessoa.getCategoriaPessoa().equals(TipoPessoa.FISICA) && obj.pessoa.getDataNasc() == null) {
            throw new Exception("O campo DATA NASCIMENTO (Aba - Dados Pessoais) deve ser informado.");
        }
        if (configuracaoSistema.getDataNascOb() && obj.pessoa.getCategoriaPessoa().equals(TipoPessoa.FISICA) && !UteisValidacao.dataMenorDataAtual(obj.pessoa.getDataNasc())) {
            throw new Exception("O campo DATA NASCIMENTO (Aba - Dados Pessoais) deve possuir valor menor do que a data atual");
        }

        if (configuracaoSistema.getNomeMaeOb() && (obj.getPessoa().getNomeMae() == null || obj.getPessoa().getNomeMae().trim().isEmpty())) {
            throw new Exception("O campo NOME DA MÃE (Cliente) deve ser informado.");
        }

        if (configuracaoSistema.isCpfMaeOb() && UteisValidacao.emptyString(obj.getPessoa().getCpfMae())) {
            throw new Exception("O campo " + obj.identificadoresPessoais[0] + " DA MÃE (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.isRgMaeOb() && UteisValidacao.emptyString(obj.getPessoa().getRgMae())) {
            throw new Exception("O campo RG DA MÃE (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.isRgPaiOb() && UteisValidacao.emptyString(obj.getPessoa().getRgPai())) {
            throw new Exception("O campo RG DO PAI (Cliente) deve ser informado.");
        }

        if (configuracaoSistema.isUtilizarServicoSesiSC() && obj.getCategoria() != null && !UteisValidacao.emptyNumber(obj.getCategoria().getCodigo())) {
            CategoriaVO categoriaAtual = getFacade().getCategoria().consultarPorCodigo(obj.getCategoria().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (categoriaAtual.isObrigatorioCnpjClienteSesi() && UteisValidacao.emptyString(obj.getPessoa().getCnpjSesi())) {
                throw new Exception("O campo CNPJ Sesi Indústria é obrigatório para a categoria selecionada.");
            }
        }

        if (configuracaoSistema.isUtilizarServicoSesiSC() &&
                configuracaoSistema.getObjCnpjSesi() && UteisValidacao.emptyString(obj.getPessoa().getCnpjSesi())) {
            throw new Exception("O campo CNPJ Sesi Indústria é obrigatório.");
        }

        if(!configuracaoSistema.isUsarSistemaInternacional()) {
            if (configuracaoSistema.isCpfValidar() &&
                    !UteisValidacao.emptyString(obj.getPessoa().getCpfMae()) &&
                    !SuperVO.verificaCPF(obj.getPessoa().getCpfMae())) {
                throw new Exception("O campo CPF DA MÃE (Cliente) não é válido.");
            }
        }

        if (configuracaoSistema.getNomePaiOb() && (obj.getPessoa().getNomePai() == null || obj.getPessoa().getNomePai().trim().isEmpty())) {
            throw new Exception("O campo NOME DO PAI (Cliente) deve ser informado.");
        }

        if (configuracaoSistema.isCpfPaiOb() && UteisValidacao.emptyString(obj.getPessoa().getCpfPai())) {
            throw new Exception("O campo " + obj.identificadoresPessoais[0] + " DO PAI (Cliente) deve ser informado.");
        }

        if(!configuracaoSistema.isUsarSistemaInternacional()) {
            if (configuracaoSistema.isCpfValidar() &&
                    !UteisValidacao.emptyString(obj.getPessoa().getCpfPai()) &&
                    !SuperVO.verificaCPF(obj.getPessoa().getCpfPai())) {
                throw new Exception("O campo CPF DO PAI (Cliente) não é válido.");
            }
        }

        if (!UteisValidacao.emptyString(obj.getPessoa().getNomeMae())) {
            obj.getPessoa().setEmitirNotaNomeMae(true);
        } else if (!UteisValidacao.emptyString(obj.getPessoa().getNomePai())) {
            obj.getPessoa().setEmitirNotaNomeMae(false);
        }

        if (configuracaoSistema.isUsarNomeResponsavelNota()  && (Integer.parseInt(obj.getPessoa().getIdadePessoa()) < 18)
                && (obj.getPessoaResponsavel() != null && UteisValidacao.emptyNumber(obj.getPessoaResponsavel().getCodigo()))) {
            if (cpfResponsaveisValido(configuracaoSistema, obj.getPessoa())) {
                configuracaoSistema.setCfpOb(false);
            }
        }

        if(configuracaoSistema.isValidarCPFResponsaveis() && (Integer.parseInt(obj.getPessoa().getIdadePessoa()) < 18)){
            if (cpfResponsaveisValido(configuracaoSistema, obj.getPessoa())) {
                configuracaoSistema.setCfpOb(false);
            }
        }

        if (TipoPessoa.FISICA.equals(obj.getPessoa().getCategoriaPessoa())) {
            if (configuracaoSistema.getSexoOb() && (obj.getPessoa().getSexo() == null || obj.getPessoa().getSexo().trim().isEmpty())) {
                throw new Exception("O campo SEXO (Cliente) deve ser informado.");
            }
            if (configuracaoSistema.isGeneroOb() && (obj.getPessoa().getGenero() == null || obj.getPessoa().getGenero().trim().isEmpty())) {
                throw new Exception("O campo GÊNERO (Cliente) deve ser informado.");
            }
            if (configuracaoSistema.isNomeRegistroOb() && (obj.getPessoa().getNomeRegistro() == null || obj.getPessoa().getNomeRegistro().trim().isEmpty())) {
                throw new Exception("O campo NOME REGISTRO (Cliente) deve ser informado.");
            }
            if (configuracaoSistema.getProfissaoOb() && (obj.getPessoa().getProfissao() == null || obj.getPessoa().getProfissao().getCodigo() == 0)) {
                throw new Exception("O campo PROFISSÃO (Cliente) deve ser informado.");
            }

            //----------CPF
            if  (configuracaoSistema.getCfpOb()) {
                if (obj.getPessoa().getCfp().trim().isEmpty()
                        && obj.getPessoa().getRne().trim().isEmpty()
                        && obj.getPessoa().getPassaporte().trim().isEmpty()
                        && UteisValidacao.emptyNumber(obj.getPessoaResponsavel().getCodigo())) {
                    throw new Exception("O campo " + obj.identificadoresPessoais[0] + ", RNE ou Passaporte (Cliente) deve ser informado");
                }
            }

            if (configuracaoSistema.isValidarCpfDuplicado() && obj.getPessoa().getCodigo() == 0) {
                PessoaVO.validarCPF(obj.getPessoa(), obj.getEmpresa().getNome());
            }
            if(!configuracaoSistema.isUsarSistemaInternacional()) {
                if (configuracaoSistema.isCpfValidar()
                        && !obj.getPessoa().getCfp().trim().isEmpty()
                        && !SuperVO.verificaCPF(obj.getPessoa().getCfp())) {
                    throw new ConsistirException("O CPF não é VÁLIDO.");
                }
            }
            //----------CPF
        }
        if (configuracaoSistema.getCategoriaOb() && (obj.getCategoria() == null || obj.getCategoria().getCodigo() == 0)) {
            throw new Exception("O campo CATEGORIA (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getGrauInstrucaoOb() && (obj.getPessoa().getGrauInstrucao() == null || obj.getPessoa().getGrauInstrucao().getCodigo() == 0)) {
            throw new Exception("O campo GRAU DE INSTRUÇÃO (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getEstadoCivilOb() && (obj.getPessoa().getEstadoCivil() == null || obj.getPessoa().getEstadoCivil().trim().isEmpty())) {
            throw new Exception("O campo ESTADO CIVIL (Cliente) deve ser informado.");
        }

        if(!configuracaoSistema.isUsarSistemaInternacional()) {
            if (configuracaoSistema.getRgOb() && (obj.getPessoa().getRg() == null || obj.getPessoa().getRg().trim().isEmpty())) {
                throw new Exception("O campo RG (Cliente) deve ser informado.");
            }
            if (configuracaoSistema.getRgOb() && (obj.getPessoa().getRgOrgao() == null || obj.getPessoa().getRgOrgao().trim().isEmpty())) {
                throw new Exception("O campo Orgao do RG (Cliente) deve ser informado.");
            }
            if (configuracaoSistema.getRgOb() && (obj.getPessoa().getRgUf() == null || obj.getPessoa().getRgUf().trim().isEmpty())) {
                throw new Exception("O campo UF do RG (Cliente) deve ser informado.");
            }
        }
        if (configuracaoSistema.getTelefoneOb() && (obj.getPessoa().getTelefoneVOs() == null || obj.getPessoa().getTelefoneVOs().isEmpty())) {
            throw new Exception("Telefone deve ser informado.");
        }
        if (configuracaoSistema.getEmailOb() && obj.pessoa.getEmailVOs().isEmpty()) {
            throw new Exception("O campo EMAIL (Cliente) deve ser informado.");
        }
        obj.validarEnderecos(configuracaoSistema);
        if (configuracaoSistema.getPaisOb() && (obj.getPessoa().getPais() == null || obj.getPessoa().getPais().getCodigo().intValue() == 0)) {
            throw new Exception("O campo PAÍS (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getEstadoOb() && (obj.getPessoa().getEstadoVO() == null || obj.getPessoa().getEstadoVO().getCodigo().intValue() == 0)) {
            throw new Exception("O campo ESTADO  Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getCidadeOb() && (obj.getPessoa().getCidade() == null || obj.getPessoa().getCidade().getCodigo().intValue() == 0)) {
            throw new Exception("O campo CIDADE (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.isContatoEmergenciaOb() && (UteisValidacao.emptyString(obj.getPessoa().getContatoEmergencia()))) {
            throw new Exception("O campo CONTATO EMERGÊNCIA (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.isTelefoneEmergenciaOb() && (UteisValidacao.emptyString(obj.getPessoa().getTelefoneEmergencia()))) {
            throw new Exception("O campo TELEFONE DE EMERGÊNCIA (Cliente) deve ser informado.");
        }

        validarVinculos(obj);
        getFacade().getCliente().validarCodAcessoAlternativo(obj);
    }

    public static void validarDadosResponsavelFinanceiro(ClienteVO obj, ConfiguracaoSistemaVO configuracaoSistema) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if(configuracaoSistema.getNomeRespFinanceiroOb() && UteisValidacao.emptyString(obj.getPessoa().getNomeRespFinanceiro())) {
            throw new Exception("Informe o Nome do Responsável Financeiro.");
        }

        if(configuracaoSistema.getRgRespFinanceiroOb() && UteisValidacao.emptyString(obj.getPessoa().getRgRespFinanceiro())) {
            throw new Exception("Informe o RG do Responsável Financeiro.");
        }

        if(configuracaoSistema.getCpfRespFinanceiroOb() && UteisValidacao.emptyString(obj.getPessoa().getCpfRespFinanceiro())) {
            throw new Exception("Informe o CPF do Responsável Financeiro.");
        }

        if(configuracaoSistema.getEmailRespFinanceiroOb() && UteisValidacao.emptyString(obj.getPessoa().getEmailRespFinanceiro())) {
            throw new Exception("Informe o E-mail do Responsável Financeiro.");
        }
        if (configuracaoSistema.getEmailRespFinanceiroApresentar()
                && !UteisValidacao.emptyString(obj.getPessoa().getEmailRespFinanceiro())
                && !obj.getPessoa().getEmailRespFinanceiro().matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
            throw new Exception("Insira um e-mail válido para o Responsável Financeiro.");
        }

    }

    public static void validarNotaPaiMae(ClienteVO obj, ConfiguracaoSistemaVO configuracaoSistema) throws Exception {
        if (!obj.getDataNasc().isEmpty()){
            int idadeCliente = obj.getDataNasc().isEmpty() ? 0 : Uteis.calcularIdadePessoa(Calendario.hoje(),Uteis.getDate(obj.getDataNasc()));
            if (!(idadeCliente >= 18) ) {
                if (configuracaoSistema.isUsarNomeResponsavelNota() && UteisValidacao.emptyString(obj.getPessoa().getNomeMae()) && UteisValidacao.emptyString(obj.getPessoa().getNomePai())){
                    throw new Exception("Cliente é menor de 18 anos, é obrigatório informar os dados do responsável para a emissão de nota fiscal");
                }
            }
        }
    }

    public static void validarDadosSemCodigoAcesso(ClienteVO obj, ConfiguracaoSistemaVO configuracaoSistema) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (configuracaoSistema.getMatriculaOb() && obj.getMatricula().trim().isEmpty()) {
            throw new Exception("O campo MATRÍCULA (Cliente) deve ser informado.");
        }
        if (!UtilReflection.objetoMaiorQueZero(obj, "getPessoa().getCodigo()")){
            throw new Exception("Cliente sem relacionamento com pessoa. Consulte o cliente novamente.");
        }
        if (obj.pessoa.getNome().trim().isEmpty()) {
            throw new Exception("O campo NOME (Aba - Dados Pessoais) deve ser informado.");
        }
        if(Pattern.compile("\\d*[!@#$%&\\*\\{\\}\\[\\]\\?:><,|\\\\/\"\\+]\\d*").matcher(obj.pessoa.getNome()).find()){
            throw new ConsistirException("O campo NOME não pode conter caracteres especiais (!@#$%&*}{][?:><,\\|/\"+).");
        }
        if (configuracaoSistema.getDataNascOb() && obj.pessoa.getDataNasc() == null) {
            throw new Exception("O campo DATA NASCIMENTO (Aba - Dados Pessoais) deve ser informado.");
        }
        if (configuracaoSistema.getDataNascOb() && !UteisValidacao.dataMenorDataAtual(obj.pessoa.getDataNasc())) {
            throw new Exception("O campo DATA NASCIMENTO (Aba - Dados Pessoais) deve possuir valor menor do que a data atual");
        }
        if ((configuracaoSistema.getNomeMaeOb() || (configuracaoSistema.getNomeMaeApresentar() && (obj.getPessoa().getDataNasc() != null && Uteis.nrDiasEntreDatas(obj.getPessoa().getDataNasc(), Calendario.hoje()) < 18 * 365 + 4)))
                && (obj.getPessoa().getNomeMae().trim().isEmpty())) {
            throw new Exception("O campo NOME DA MÃE (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getNomePaiOb() && (obj.getPessoa().getNomePai() == null || obj.getPessoa().getNomePai().trim().isEmpty())) {
            throw new Exception("O campo NOME DO PAI (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getSexoOb() && (obj.getPessoa().getSexo() == null || obj.getPessoa().getSexo().trim().isEmpty())) {
            throw new Exception("O campo SEXO (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.isGeneroOb() && (obj.getPessoa().getGenero() == null || obj.getPessoa().getGenero().trim().isEmpty())) {
            throw new Exception("O campo GÊNERO (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.isNomeRegistroOb() && (obj.getPessoa().getNomeRegistro() == null || obj.getPessoa().getNomeRegistro().trim().isEmpty())) {
            throw new Exception("O campo NOME REGISTRO (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getProfissaoOb() && (obj.getPessoa().getProfissao() == null || obj.getPessoa().getProfissao().getCodigo() == 0)) {
            throw new Exception("O campo PROFISSÃO (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getGrauInstrucaoOb() && (obj.getPessoa().getGrauInstrucao() == null || obj.getPessoa().getGrauInstrucao().getCodigo() == 0)) {
            throw new Exception("O campo GRAU DE INSTRUÇÃO (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getEstadoCivilOb() && (obj.getPessoa().getEstadoCivil() == null || obj.getPessoa().getEstadoCivil().trim().isEmpty())) {
            throw new Exception("O campo ESTADO CIVIL (Cliente) deve ser informado.");
        }

        //----------CPF
        if (configuracaoSistema.getCfpOb() && obj.getPessoa().getCfp().trim().isEmpty() && UteisValidacao.emptyNumber(obj.getPessoaResponsavel().getCodigo())) {
            throw new Exception("O campo " + obj.identificadoresPessoais[0] + " (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.isValidarCpfDuplicado() && obj.getPessoa().getCodigo() == 0) {
            PessoaVO.validarCPF(obj.getPessoa(), obj.getEmpresa().getNome());
        }
        if (configuracaoSistema.isCpfValidar()
                && !obj.getPessoa().getCfp().trim().isEmpty()
                && !SuperVO.verificaCPF(obj.getPessoa().getCfp())) {
            throw new ConsistirException("O CPF não é VÁLIDO.");
        }
        //----------CPF

        if (configuracaoSistema.getRgOb() && (obj.getPessoa().getRg() == null || obj.getPessoa().getRg().trim().isEmpty())) {
            throw new Exception("O campo " + obj.identificadoresPessoais[1] + " (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getRgOb() && (obj.getPessoa().getRgOrgao() == null || obj.getPessoa().getRgOrgao().trim().isEmpty())) {
            throw new Exception("O campo Orgao do RG (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getRgOb() && (obj.getPessoa().getRgUf() == null || obj.getPessoa().getRgUf().trim().isEmpty())) {
            throw new Exception("O campo UF do RG (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getTelefoneOb() && (obj.getPessoa().getTelefoneVOs() == null || obj.getPessoa().getTelefoneVOs().isEmpty())) {
            throw new Exception("Telefone deve ser informado.");
        }
        if (configuracaoSistema.getEmailOb() && obj.pessoa.getEmailVOs().isEmpty()) {
            throw new Exception("O campo EMAIL (Cliente) deve ser informado.");
        }
        obj.validarEnderecos(configuracaoSistema);
        if (configuracaoSistema.getPaisOb() && (obj.getPessoa().getPais() == null || obj.getPessoa().getPais().getCodigo().intValue() == 0)) {
            throw new Exception("O campo PAÍS (Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getEstadoOb() && (obj.getPessoa().getEstadoVO() == null || obj.getPessoa().getEstadoVO().getCodigo().intValue() == 0)) {
            throw new Exception("O campo ESTADO  Cliente) deve ser informado.");
        }
        if (configuracaoSistema.getCidadeOb() && (obj.getPessoa().getCidade() == null || obj.getPessoa().getCidade().getCodigo().intValue() == 0)) {
            throw new Exception("O campo CIDADE (Cliente) deve ser informado.");
        }
        validarVinculos(obj);
    }

    public String getNome_Apresentar() {
        return getPessoa().getNome();
    }

    public String getCategoria_Apresentar() {
        return getCategoria().getNome();
    }

    public Date getDataDe_Apresentar() {
        return getSituacaoClienteSinteticoVO().getDataVigenciaDe();
    }

    public Date getDataAte_Apresentar() {
        return getSituacaoClienteSinteticoVO().getDataVigenciaAteAjustada();
    }

    public String getNascimento_Apresentar() {
        return getPessoa().getDataNasc_Apresentar();
    }

    public String getSituacaoApresentar() {

        if (situacao == null) {
            return "";
        }
        if (situacao.equals("VI")) {
            return "Visitantes";
        }
        if (situacao.equals("PL")) {
            return "Visitantes - Free Pass";
        }
        if (situacao.equals("AA")) {
            return "Visitantes - Aula Avulsa";
        }
        if (situacao.equals("DI")) {
            return "Visitantes - Diária";
        }
        if (situacao.equals("IN")) {
            return "Inativos";
        }
        if (situacao.equals("CA")) {
            return "Inativos - Cancelados";
        }
        if (situacao.equals("DE")) {
            return "Inativos - Desistentes";
        }
        if (situacao.equals("AT")) {
            return "Ativos";
        }
        if (situacao.equals("NO")) {
            return "Ativos - Normais";
        }
        if (situacao.equals("TR")) {
            return "Trancados";
        }
        if (situacao.equals("TV")) {
            return "Trancados Vencidos";
        }
        if (situacao.equals("AV")) {
            return "Ativos - A Vencer";
        }
        if (situacao.equals("VE")) {
            return "Inativos - Vencidos";
        }
        if (situacao.equals("CR")) {
            return "Ativos - Férias";
        }
        if (situacao.equals("AE")) {
            return "Ativos - Atestado";
        }
        return situacao;

    }

    /**
     * Método de validação simplificada usada no FINANCEIRO para cadastro de
     * pessoa simplificado pela tela de lançamentos
     *
     * @param obj
     * @throws ConsistirException
     */
    public static void validarDadosSimplificado(ClienteVO obj) throws ConsistirException {
        if (obj.getEmpresa().getCodigo() == null || obj.getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("O campo EMPRESA deve ser informado.");
        }
        if (obj.getVinculoVOs().isEmpty()) {
            throw new ConsistirException("O cliente deve ter pelo menos um vínculo.");
        }
    }

    public static void validarVinculos(ClienteVO obj) throws Exception {
        boolean consultor = false;
        if (!obj.getNovoObj() && obj.getVinculoVOs().isEmpty()) {
            throw new Exception("O cliente deve ter pelo menos um vínculo de consultor.");
        }
        // verifica todos os vinculos
        for (VinculoVO vinculo : obj.getVinculoVOs()) {
            // se tipo do vinculo é consultor
            if (vinculo.getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())) {
                // se ja existe outro vinculo de consultor para este Cliente
                if (consultor) {
                    throw new Exception("Não é possível incluir dois consultores para o cliente " + obj.getPessoa().getNome());
                } else {
                    consultor = true;
                }
            }
        }
        //deve ter pelo menos um consultor para gravar o cliente.
        if (!obj.getNovoObj() && !consultor) {
            throw new ConsistirException("O cliente deve ter pelo menos um vínculo de consultor.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        setSituacao(getSituacao().toUpperCase());
        setMatricula(getMatricula().toUpperCase());
        setCodAcesso(getCodAcesso().toUpperCase());
        setCodAcessoAlternativo(getCodAcessoAlternativo().toUpperCase());
        setBanco(getBanco().toUpperCase());
        setAgencia(getAgencia().toUpperCase());
        setAgenciaDigito(getAgenciaDigito().toUpperCase());
        setConta(getConta().toUpperCase());
        setContaDigito(getContaDigito().toUpperCase());
        setIdentificadorParaCobranca(getIdentificadorParaCobranca().toUpperCase());
        pessoa.realizarUpperCaseDados();
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setFreePass(new ProdutoVO());
        setListaClienteMensagem(new ArrayList<>());
        setSituacaoClienteSinteticoVO(new SituacaoClienteSinteticoDWVO());
    }

    /**
     *
     * @param existeSubordinada Variavel que informa se a situação do cliente
     * tem subordinda ou nao
     * @param situacao sera gravada para o cliente
     * @param subordinada sera gravada para o cliente
     * @param dataInicio sera gravada para o cliente
     * @param dataFim sera gravada para o cliente
     * @param numeroDias se da data final da situação sera gerada por numero de
     * dias
     * @throws java.lang.Exception
     */
    public void gerarClienteSituacao(Boolean existeSubordinada, String situacao, String subordinada, Date dataInicio, Date dataFim, Integer numeroDias) throws Exception {
        try {
            ClienteSituacaoVO obj = new ClienteSituacaoVO();
            if (existeSubordinada) {
                obj.setClienteVO(this);
                obj.setDataInicio(dataInicio);
                if (numeroDias > 0) {
                    obj.setDataFim(Uteis.obterDataFutura2(dataInicio, (numeroDias - 1)));
                } else {
                    obj.setDataFim(dataFim);
                }
                obj.setSituacao(situacao);
                obj.setSubordinadaSituacao(subordinada);
            } else {
                obj.setClienteVO(this);
                obj.setDataInicio(dataInicio);
                obj.setSituacao(situacao);
            }
            adicionarObjClienteSituacaoVOs(obj);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ClienteClassificacaoVO</code> ao List
     * <code>clienteClassificacaoVOs</code>. Utiliza o atributo padrão de
     * consulta da classe
     * <code>ClienteClassificacao</code> - getClassificacao().getCodigo() - como
     * identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ClienteClassificacaoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjClienteSituacaoVOs(ClienteSituacaoVO obj) throws Exception {
        //ClienteSituacaoVO.validarDados(obj);
        int index = 0;
        Iterator i = getClienteSituacaoVOs().iterator();
        while (i.hasNext()) {
            ClienteSituacaoVO objExistente = (ClienteSituacaoVO) i.next();
            if (objExistente.getSituacao().equals(obj.getSituacao()) || objExistente.getSubordinadaSituacao().equals(obj.getSubordinadaSituacao())) {
                getClienteSituacaoVOs().set(index, obj);
                return;
            }

            index++;
        }
        getClienteSituacaoVOs().add(obj);
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ClienteClassificacaoVO</code> ao List
     * <code>clienteClassificacaoVOs</code>. Utiliza o atributo padrão de
     * consulta da classe
     * <code>ClienteClassificacao</code> - getClassificacao().getCodigo() - como
     * identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ClienteClassificacaoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjClienteClassificacaoVOs(ClienteClassificacaoVO obj) throws Exception {
        ClienteClassificacaoVO.validarDados(obj);
        int index = 0;
        Iterator i = getClienteClassificacaoVOs().iterator();
        while (i.hasNext()) {
            ClienteClassificacaoVO objExistente = (ClienteClassificacaoVO) i.next();
            if (objExistente.getClassificacao().getCodigo().equals(obj.getClassificacao().getCodigo())) {
                getClienteClassificacaoVOs().set(index, obj);
                return;
            }

            index++;
        }
        getClienteClassificacaoVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>ClienteClassificacaoVO</code> no List
     * <code>clienteClassificacaoVOs</code>. Utiliza o atributo padrão de
     * consulta da classe
     * <code>ClienteClassificacao</code> - getClassificacao().getCodigo() - como
     * identificador (key) do objeto no List.
     *
     * @param classificacao Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjClienteClassificacaoVOs(Integer classificacao) throws Exception {
        int index = 0;
        Iterator i = getClienteClassificacaoVOs().iterator();
        while (i.hasNext()) {
            ClienteClassificacaoVO objExistente = (ClienteClassificacaoVO) i.next();
            if (objExistente.getClassificacao().getCodigo().equals(classificacao)) {
                getClienteClassificacaoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>ClienteClassificacaoVO</code> no List
     * <code>clienteClassificacaoVOs</code>. Utiliza o atributo padrão de
     * consulta da classe
     * <code>ClienteClassificacao</code> - getClassificacao().getCodigo() - como
     * identificador (key) do objeto no List.
     *
     * @param classificacao Parâmetro para localizar o objeto do List.
     */
    public ClienteClassificacaoVO consultarObjClienteClassificacaoVO(Integer classificacao) throws Exception {
        Iterator i = getClienteClassificacaoVOs().iterator();
        while (i.hasNext()) {
            ClienteClassificacaoVO objExistente = (ClienteClassificacaoVO) i.next();
            if (objExistente.getClassificacao().getCodigo().equals(classificacao)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ClienteGrupoVO</code> ao List
     * <code>clienteGrupoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>ClienteGrupo</code> - getGrupo().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ClienteGrupoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjClienteGrupoVOs(ClienteGrupoVO obj) throws Exception {
        ClienteGrupoVO.validarDados(obj);
        int index = 0;
        Iterator i = getClienteGrupoVOs().iterator();
        while (i.hasNext()) {
            ClienteGrupoVO objExistente = (ClienteGrupoVO) i.next();
            if (objExistente.getGrupo().getCodigo().equals(obj.getGrupo().getCodigo())) {
                getClienteGrupoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getClienteGrupoVOs().add(obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>ClienteGrupoVO</code> no List
     * <code>clienteGrupoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>ClienteGrupo</code> - getGrupo().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param grupo Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjClienteGrupoVOs(Integer grupo) throws Exception {
        int index = 0;
        Iterator i = getClienteGrupoVOs().iterator();
        while (i.hasNext()) {
            ClienteGrupoVO objExistente = (ClienteGrupoVO) i.next();
            if (objExistente.getGrupo().getCodigo().equals(grupo)) {
                getClienteGrupoVOs().remove(index);
                return;
            }
            index++;
        }
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>ClienteGrupoVO</code> no List
     * <code>clienteGrupoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>ClienteGrupo</code> - getGrupo().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param grupo Parâmetro para localizar o objeto do List.
     */
    public ClienteGrupoVO consultarObjClienteGrupoVO(Integer grupo) throws Exception {
        Iterator i = getClienteGrupoVOs().iterator();
        while (i.hasNext()) {
            ClienteGrupoVO objExistente = (ClienteGrupoVO) i.next();
            if (objExistente.getGrupo().getCodigo().equals(grupo)) {
                return objExistente;
            }
        }
        return null;
        //consultarObjSubordinadoOC
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ClienteGrupoVO</code> ao List
     * <code>clienteGrupoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>ClienteGrupo</code> - getGrupo().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ClienteGrupoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjVinculoVOs(VinculoVO obj) throws Exception {
        VinculoVO.validarDados(obj);
        int index = 0;
        Iterator i = getVinculoVOs().iterator();
        while (i.hasNext()) {
            VinculoVO objExistente = (VinculoVO) i.next();
            if (objExistente.getTipoVinculo().equals(obj.getTipoVinculo()) && objExistente.getColaborador().getCodigo().equals(obj.getColaborador().getCodigo().intValue())) {
                getVinculoVOs().set(index, obj);
                return;
            }
            if ((objExistente.getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla()) && obj.getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla()))) {
                throw new Exception("Já Existe um consultor responsável por este cliente. Exclua o anterior para incluir um novo.");
            }

            if ((objExistente.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla()) && obj.getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla()))) {
                throw new Exception("Já Existe um Professor (TreinoWeb) para este cliente. Exclua o anterior para incluir um novo.");
            }

            index++;
        }
        getVinculoVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>VinculoVO</code> no List
     * <code>VinculoVOs</code>. Utiliza o atributo padrão de consulta da classe
     * <code>ClienteGrupo</code> - getGrupo().getCodigo() - como identificador
     * (key) do objeto no List.
     */
    public void excluirObjVinculoVOs(String tipoVinculo) throws Exception {
        int index = 0;
        Iterator i = getVinculoVOs().iterator();
        while (i.hasNext()) {
            VinculoVO objExistente = (VinculoVO) i.next();
            if (objExistente.getTipoVinculo().equals(tipoVinculo)) {
                getVinculoVOs().remove(index);
                return;
            }
            index++;
        }
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>VinculoVO</code> no List
     * <code>VinculoVOs</code>. Utiliza o atributo padrão de consulta da classe
     * <code>ClienteGrupo</code> - getGrupo().getCodigo() - como identificador
     * (key) do objeto no List.
     */
    public void excluirObjVinculoVOs(Integer codigo) throws Exception {
        int index = 0;
        Iterator i = getVinculoVOs().iterator();
        while (i.hasNext()) {
            VinculoVO objExistente = (VinculoVO) i.next();
            if (objExistente.getCodigo().equals(codigo)) {
                getVinculoVOs().remove(index);
                return;
            }
            index++;
        }
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>VinculoVO</code> no List
     * <code>VinculoVOs</code>. Utiliza o atributo padrão de consulta da classe
     * <code>ClienteGrupo</code> - getGrupo().getCodigo() - como identificador
     * (key) do objeto no List.
     */
    public VinculoVO consultarObjVinculoVO(String tipoVinculo) throws Exception {
        Iterator i = getVinculoVOs().iterator();
        while (i.hasNext()) {
            VinculoVO objExistente = (VinculoVO) i.next();
            if (objExistente.getTipoVinculo().equals(tipoVinculo)) {
                return objExistente;
            }
        }
        return null;
        //consultarObjSubordinadoOC
    }

    /**
     * Operação responsável por encontrar o vinculo consultor
     */
    public VinculoVO obterVinculoConsultor() throws Exception {
        return consultarObjVinculoVO(TipoColaboradorEnum.CONSULTOR.getSigla());
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>FamiliarVO</code> ao List
     * <code>familiarVOs</code>. Utiliza o atributo padrão de consulta da classe
     * <code>Familiar</code> - getFamiliar() - como identificador (key) do
     * objeto no List.
     *
     * @param obj Objeto da classe <code>FamiliarVO</code> que será adiocionado
     * ao Hashtable correspondente.
     */
    public void adicionarObjFamiliarVOs(FamiliarVO obj) throws Exception {
        FamiliarVO.validarDados(obj);
        int index = 0;
        Iterator i = getFamiliarVOs().iterator();
        while (i.hasNext()) {
            FamiliarVO objExistente = (FamiliarVO) i.next();
            if (objExistente.getFamiliar().equals(obj.getFamiliar())) {
                getFamiliarVOs().set(index, obj);
                return;
            }
            index++;
        }
        getFamiliarVOs().add(obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>FamiliarVO</code> no List
     * <code>familiarVOs</code>. Utiliza o atributo padrão de consulta da classe
     * <code>Familiar</code> - getFamiliar() - como identificador (key) do
     * objeto no List.
     *
     * @param familiar Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjFamiliarVOs(Integer familiar) {
        int index = 0;
        Iterator i = getFamiliarVOs().iterator();
        while (i.hasNext()) {
            FamiliarVO objExistente = (FamiliarVO) i.next();
            if (objExistente.getFamiliar().equals(familiar)) {
                getFamiliarVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>FamiliarVO</code> no List
     * <code>familiarVOs</code>. Utiliza o atributo padrão de consulta da classe
     * <code>Familiar</code> - getFamiliar() - como identificador (key) do
     * objeto no List.
     *
     * @param familiar Parâmetro para localizar o objeto do List.
     */
    public FamiliarVO consultarObjFamiliarVO(Integer familiar) throws Exception {
        Iterator i = getFamiliarVOs().iterator();
        while (i.hasNext()) {
            FamiliarVO objExistente = (FamiliarVO) i.next();
            if (objExistente.getFamiliar().equals(familiar)) {
                return objExistente;
            }
        }
        return null;
        //consultarObjSubordinadoOC
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>ClienteGrupoVO</code> ao List
     * <code>clienteGrupoVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>ClienteGrupo</code> - getGrupo().getCodigo() - como identificador
     * (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>ClienteGrupoVO</code> que será
     * adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjClienteMensagemVOs(ClienteMensagemVO obj) throws Exception {
        int index = 0;
        Iterator i = getListaClienteMensagem().iterator();
        while (i.hasNext()) {
            ClienteMensagemVO objExistente = (ClienteMensagemVO) i.next();
            if (objExistente.getCodigo().equals(obj.getCodigo())) {
                getListaClienteMensagem().set(index, obj);
                verificarAbas(obj);
                return;
            }
            index++;
        }
        getListaClienteMensagem().add(obj);
        verificarAbas(obj);
    }

    public void adicionarObjAvisoCliente(ClienteMensagemVO obj) throws Exception {
        int index = 0;
        Iterator i = getListaMensagemAvisoCliente().iterator();
        while (i.hasNext()) {
            ClienteMensagemVO objExistente = (ClienteMensagemVO) i.next();
            if (objExistente.getCodigo().equals(obj.getCodigo())) {
                getListaMensagemAvisoCliente().set(index, obj);
                return;
            }
            index++;
        }
        getListaMensagemAvisoCliente().add(obj);
        verificarAbas(obj);
    }

    public void verificarAbas(ClienteMensagemVO obj) {
        if (obj.getTipomensagem().equals(TiposMensagensEnum.CATRACA)) {
            setAbaCatraca(true);
        }
        if (obj.getTipomensagem().equals(TiposMensagensEnum.AVISO_CONSULTOR)) {
            setAbaConsultor(true);
        }
        if (obj.getTipomensagem().equals(TiposMensagensEnum.OBJETIVO_CURTO)) {
            setAbaObjetivo(true);
        }
        if (obj.getTipomensagem().equals(TiposMensagensEnum.AVISO_MEDICO)) {
            setAbaMedico(true);
        }
    }

    public PeriodoAcessoClienteVO gerarPeriodoAcessoBaseadoCliente() {
        /*
         * WM - 22/12/2010 - Período acesso para Free Pass será iniciado na data de hoje
         * incondicionalmente, para já entrar em vigência e não considerar o final do contrato.
         */
        //PeriodoAcessoClienteVO periodoAcesso = (getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoPessoa(getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        PeriodoAcessoClienteVO obj = new PeriodoAcessoClienteVO();
        /*if (periodoAcesso != null) {
         obj.setDataInicioAcesso(Uteis.obterDataFutura2(periodoAcesso.getDataFinalAcesso(), 1));
         obj.setDataFinalAcesso(Uteis.obterDataFutura2(obj.getDataInicioAcesso(), (getFreePass().getNrDiasVigencia() - 1)));
         } else {*/
        obj.setDataInicioAcesso(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setDataFinalAcesso(Uteis.obterDataFutura2(obj.getDataInicioAcesso(), (getFreePass().getNrDiasVigencia() - 1)));

        //}
        obj.setPessoa(getPessoa().getCodigo());
        obj.setContrato(0);
        obj.setTipoAcesso("PL");
        obj.setResponsavel(getResponsavelFreePass());
        return obj;
    }

    public PeriodoAcessoClienteVO gerarPeriodoAcessoTotalPassBaseadoCliente(Date dataInicio, Boolean tipoTotalPass) throws Exception {
        PeriodoAcessoClienteVO obj = new PeriodoAcessoClienteVO();
        obj.setDataInicioAcesso(dataInicio);
        obj.setDataFinalAcesso(obj.getDataInicioAcesso());
        obj.setPessoa(getPessoa().getCodigo());
        obj.setContrato(0);
        obj.setReposicao(this.codigoReposicaoExperimental);
        obj.setTipoAcesso("PL");
        obj.setResponsavel(getResponsavelFreePass());
        obj.setTipototalpass(tipoTotalPass);
        obj.setCodigoEmpresa(this.getEmpresa().getCodigo());

        return obj;
    }

    public PeriodoAcessoClienteVO gerarPeriodoAcessoBaseadoCliente(Date dataInicio, String tokenGymPass, String tipoGymPass, String tokenGoGood) throws Exception {
        PeriodoAcessoClienteVO obj = new PeriodoAcessoClienteVO();
        obj.setDataInicioAcesso(dataInicio);
        int nrDias = this.getFreePass().getNrDiasVigencia();
        int diasParaAdicionar;
        if (nrDias == 0) {
            diasParaAdicionar = nrDias + 1;
        } else {
            diasParaAdicionar = nrDias - 1;
        }
        obj.setDataFinalAcesso(
                Uteis.obterDataFutura2(obj.getDataInicioAcesso(), diasParaAdicionar)
        );
        obj.setPessoa(getPessoa().getCodigo());
        obj.setContrato(0);
        obj.setReposicao(this.codigoReposicaoExperimental);
        obj.setTipoAcesso("PL");
        obj.setResponsavel(getResponsavelFreePass());
        obj.setProduto(this.getFreePass().getCodigo());
        if (!UteisValidacao.emptyString(tokenGymPass)) {
            obj.setTokenGymPass(tokenGymPass);
            obj.setTipoGymPass(tipoGymPass);
            obj.setCodigoEmpresa(this.getEmpresa().getCodigo());
        }
        if (!UteisValidacao.emptyString(tokenGoGood)) {
            obj.setTokenGoGood(tokenGoGood);
            obj.setCodigoEmpresa(this.getEmpresa().getCodigo());
        }
        return obj;
    }
    public String obterSituacaoClienteVisitante(Connection con) throws Exception {
        try {
            if (getSituacao().equals("VI")) {
                if (consultarSituacaoVisitanteFreePass(con)) {
                    return "PL";
                }
                if (consultarSituacaoVisitanteAulaAvulsa(con)) {
                    return "AA";
                }
                if (consultarSituacaoVisitanteDiaria(con)) {
                    return "DI";
                }
                return "VI";
            }
            return "VI";
        } catch (Exception e) {
            throw e;
        }

    }

    public Boolean consultarSituacaoVisitanteFreePass(Connection con) throws Exception {
        try {
            Boolean estaSituacao = (new PeriodoAcessoCliente(con).consultarPorDataEspecificaECodigoPessoaETipoAcesso(
                    negocio.comuns.utilitarias.Calendario.hoje(), getPessoa().getCodigo().intValue(), "PL",
                    false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, false));
            if (estaSituacao) {
                return true;
            }
            return false;
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarSituacaoFreePass(ClienteVO obj, Connection con) {
        try {
            obj.setPossuiFreePass(consultarSituacaoVisitanteFreePass(con));
        } catch (Exception e) {
            obj.setPossuiFreePass(false);
        }
    }

    public void validarSituacaoGymPass(ClienteVO obj, Connection con) {
        try {
            obj.setPossuiGymPass(false);

            if (!UteisValidacao.emptyString(obj.getGympasUniqueToken())) {
                obj.setPossuiGymPass(new PeriodoAcessoCliente(con).consultarPorDataEspecificaECodigoPessoaETipoAcesso(
                        negocio.comuns.utilitarias.Calendario.hoje(), getPessoa().getCodigo().intValue(), "PL",
                        false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, true));
            }
        } catch (Exception e) {
        }
    }

    public void validarSituacaoTotalPass(ClienteVO obj, Connection con) {
        try {
            obj.setPossuiTotalPass(new PeriodoAcessoCliente(con).existiuPeriodoAcessoTotalpass(
                    getPessoa().getCodigo().intValue()));
        } catch (Exception e) {
            obj.setPossuiTotalPass(false);
        }
    }

    public Boolean consultarSituacaoVisitanteDiaria(Connection con) throws Exception {
        try {
            Boolean estaSituacao = (new PeriodoAcessoCliente(con).consultarPorDataEspecificaECodigoPessoaETipoAcesso(negocio.comuns.utilitarias.Calendario.hoje(), getPessoa().getCodigo().intValue(), "DI", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, false));
            if (estaSituacao) {
                return true;
            }
            return false;
        } catch (Exception e) {
            throw e;
        }
    }

    public Boolean consultarSituacaoVisitanteAulaAvulsa(Connection con) throws Exception {
        try {
            Boolean estaSituacao = (new PeriodoAcessoCliente(con).consultarPorDataEspecificaECodigoPessoaETipoAcesso(negocio.comuns.utilitarias.Calendario.hoje(), getPessoa().getCodigo().intValue(), "AA", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, false));
            if (estaSituacao) {
                return true;
            }
            return false;
        } catch (Exception e) {
            throw e;
        }
    }

    public static String obterTelefoneCliente(ClienteVO cliente) {
        try {
            String telefone = "";
            String telefone1 = "";
            cliente.getPessoa().setTelefoneVOs(getFacade().getTelefone().consultarTelefones(cliente.getPessoa().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (cliente.getPessoa().getTelefoneVOs().size() != 0) {
                if (cliente.getPessoa().getTelefoneVOs().size() <= 1) {
                    Iterator i = cliente.getPessoa().getTelefoneVOs().iterator();
                    while (i.hasNext()) {
                        TelefoneVO tel = (TelefoneVO) i.next();
                        telefone = tel.getTipoTelefone_Apresentar() + ": " + tel.getNumero();
                    }
                } else {
                    Iterator i = cliente.getPessoa().getTelefoneVOs().iterator();
                    while (i.hasNext()) {
                        TelefoneVO tel = (TelefoneVO) i.next();
                        if (tel.getTipoTelefone().equals("RE")) {
                            telefone = tel.getTipoTelefone_Apresentar() + ": " + tel.getNumero();
                        } else {
                            telefone1 = tel.getTipoTelefone_Apresentar() + ": " + tel.getNumero();
                        }
                    }
                    if (telefone.trim().isEmpty()) {
                        telefone = telefone1;
                    }
                }
            }
            return telefone;
        } catch (Exception e) {
            return "";
        }
    }

    public String obterEmailCliente(ClienteVO cliente) throws Exception {
        try {
            String email = "";
            cliente.getPessoa().setEmailVOs(getFacade().getEmail().consultarEmails(cliente.getPessoa().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (cliente.getPessoa().getEmailVOs().size() != 0) {
                if (cliente.getPessoa().getEmailVOs().size() <= 1) {
                    Iterator i = cliente.getPessoa().getEmailVOs().iterator();
                    while (i.hasNext()) {
                        EmailVO end = (EmailVO) i.next();
                        return end.getEmail();
                    }
                } else {
                    Iterator i = cliente.getPessoa().getEmailVOs().iterator();
                    while (i.hasNext()) {
                        EmailVO end = (EmailVO) i.next();
                        if (end.getEmailCorrespondencia()) {
                            return end.getEmail();
                        } else {
                            email = end.getEmail();
                        }
                    }
                }
            }
            return email;
        } catch (Exception e) {
            throw e;
        }
    }

    public String obterEnderecoCliente(ClienteVO cliente) throws Exception {
        try {
            String endereco = "";
            cliente.getPessoa().setEnderecoVOs(getFacade().getEndereco().consultarEnderecos(cliente.getPessoa().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (cliente.getPessoa().getEnderecoVOs().size() != 0) {
                if (cliente.getPessoa().getEnderecoVOs().size() <= 1) {
                    Iterator i = cliente.getPessoa().getEnderecoVOs().iterator();
                    while (i.hasNext()) {
                        EnderecoVO end = (EnderecoVO) i.next();
                        return end.getEndereco() + " " + end.getBairro();
                    }
                } else {
                    Iterator i = cliente.getPessoa().getEnderecoVOs().iterator();
                    while (i.hasNext()) {
                        EnderecoVO end = (EnderecoVO) i.next();
                        if (end.getEnderecoCorrespondencia()) {
                            return end.getEndereco() + " " + end.getBairro();
                        } else {
                            endereco = end.getEndereco() + " " + end.getBairro();
                        }
                    }
                }
            }
            return endereco;
        } catch (Exception e) {
            throw e;
        }
    }

    public String obterVinculosExistentes() {
        StringBuilder sbVinculo = new StringBuilder();
        for (VinculoVO vi : getVinculoVOs()) {
            if (!vi.isNovoObj()) {
                sbVinculo.append(vi.getColaborador().getCodigo()).append(":");
            }
        }
        int tamanho = sbVinculo.length();
        String vinculo = "";
        if (tamanho > 0) {
            vinculo = (sbVinculo.substring(0, (tamanho - 1)));
        }
        return vinculo;
    }

    public String obterNovosVinculos() {
        StringBuilder sbVinculo = new StringBuilder();
        for (VinculoVO vi : getVinculoVOs()) {
            if (vi.isNovoObj()) {
                sbVinculo.append(vi.getColaborador().getCodigo() + ":");
            }
        }
        int tamanho = sbVinculo.length();
        String vinculo = "";
        if (tamanho > 0) {
            vinculo = (sbVinculo.substring(0, (tamanho - 1)));
        }
        return vinculo;
    }

    public Boolean getValidarDadosPessoais(ConfiguracaoSistemaVO configuracaoSistema) {
        return (((configuracaoSistema.getNomePendente() || configuracaoSistema.getNomeOb()) && (getPessoa().getNome() == null || getPessoa().getNome().trim().isEmpty()))
                || ((configuracaoSistema.getDataNascPendente() || configuracaoSistema.getDataNascOb()) && getPessoa().getDataNasc() == null)
                || ((configuracaoSistema.getNomeMaePendente() || configuracaoSistema.getNomeMaeOb()) && (getPessoa().getNomeMae() == null || getPessoa().getNomeMae().trim().isEmpty()))
                || ((configuracaoSistema.getNomePaiPendente() || configuracaoSistema.getNomePaiOb()) && (getPessoa().getNomePai() == null || getPessoa().getNomePai().trim().isEmpty()))
                || ((configuracaoSistema.getSexoPendente() || configuracaoSistema.getSexoOb()) && (getPessoa().getSexo() == null || getPessoa().getSexo().trim().isEmpty()))
                || ((configuracaoSistema.isGeneroPendente() || configuracaoSistema.isGeneroOb()) && (getPessoa().getGenero() == null || getPessoa().getGenero().trim().isEmpty()))
                || ((configuracaoSistema.isNomeRegistroPendente() || configuracaoSistema.isNomeRegistroOb()) && (getPessoa().getNomeRegistro() == null || getPessoa().getNomeRegistro().trim().isEmpty()))
                || ((configuracaoSistema.getProfissaoPendente() || configuracaoSistema.getProfissaoOb()) && (getPessoa().getProfissao() == null || getPessoa().getProfissao().getCodigo().intValue() == 0))
                || ((configuracaoSistema.getGrauInstrucaoPendente() || configuracaoSistema.getGrauInstrucaoOb()) && (getPessoa().getGrauInstrucao() == null || getPessoa().getGrauInstrucao().getCodigo().intValue() == 0))
                || ((configuracaoSistema.getEstadoCivilPendente() || configuracaoSistema.getEstadoCivilOb()) && (getPessoa().getEstadoCivil() == null || getPessoa().getEstadoCivil().trim().isEmpty()))
                || ((configuracaoSistema.getCpfPendente() || configuracaoSistema.getCfpOb()) && ((getPessoa().getCfp() == null || getPessoa().getCfp().trim().isEmpty()) && UteisValidacao.emptyNumber(getPessoaResponsavel().getCodigo())))
                || ((configuracaoSistema.getRgPendente() || configuracaoSistema.getRgOb()) && (getPessoa().getRg() == null || getPessoa().getRg().trim().isEmpty()))
                || ((configuracaoSistema.getEmailPendente() || configuracaoSistema.getEmailOb()) && (getPessoa().getEmailVOs() == null || getPessoa().getEmailVOs().size() == 0))
                || ((configuracaoSistema.getEnderecoPendente() || configuracaoSistema.getEnderecoOb()) && (getPessoa().getEnderecoVOs() == null || getPessoa().getEnderecoVOs().size() == 0))
                || ((configuracaoSistema.getCidadePendente() || configuracaoSistema.getCidadeOb()) && (getPessoa().getCidade() == null || getPessoa().getCidade().getCodigo().intValue() == 0))
                || ((configuracaoSistema.getEstadoPendente() || configuracaoSistema.getEstadoOb()) && (getPessoa().getEstadoVO() == null || getPessoa().getEstadoVO().getCodigo().intValue() == 0))
                || ((configuracaoSistema.getPaisPendente() || configuracaoSistema.getPaisOb()) && (getPessoa().getPais() == null || getPessoa().getPais().getCodigo().intValue() == 0))
                || ((configuracaoSistema.getCategoriaPendente() || configuracaoSistema.getCategoriaOb()) && (getCategoria() == null || getCategoria().getCodigo().intValue() == 0))
                || ((configuracaoSistema.getMatriculaPendente() || configuracaoSistema.getMatriculaOb()) && (getMatricula() == null || getMatricula().trim().isEmpty()))
                || ((configuracaoSistema.getWebPagePendente() || configuracaoSistema.getWebPageOb()) && (getPessoa().getWebPage() == null || getPessoa().getWebPage().trim().isEmpty()))
                || ((configuracaoSistema.getTelefonePendente() || configuracaoSistema.getTelefoneOb()) && (getPessoa().getTelefoneVOs() == null || getPessoa().getTelefoneVOs().size() == 0)))
                || ((configuracaoSistema.getNomeRespFinanceiroPendente() || configuracaoSistema.getNomeRespFinanceiroOb()) && (UteisValidacao.emptyString(getPessoa().getNomeRespFinanceiro())))
                || ((configuracaoSistema.getCpfRespFinanceiroPendente() || configuracaoSistema.getCpfRespFinanceiroOb()) && UteisValidacao.emptyString(getPessoa().getCpfRespFinanceiro()))
                || ((configuracaoSistema.getEmailRespFinanceiroPendente() || configuracaoSistema.getEmailRespFinanceiroOb()) && UteisValidacao.emptyString(getPessoa().getEmailRespFinanceiro()))
                || ((configuracaoSistema.getRgRespFinanceiroPendente() || configuracaoSistema.getRgRespFinanceiroOb()) && UteisValidacao.emptyString(getPessoa().getRgRespFinanceiro()));
    }

    public String getValidarDadosPessoaisString(ConfiguracaoSistemaVO configuracaoSistema) {
        String camposFalta = new String();
        String camposFaltaFormatado = new String();
        if ((configuracaoSistema.getNomePendente() || configuracaoSistema.getNomeOb()) && (getPessoa().getNome() == null || getPessoa().getNome().trim().isEmpty())) {
            camposFalta = "Nome, ";
        }
        if ((configuracaoSistema.getDataNascPendente() || configuracaoSistema.getDataNascOb()) && getPessoa().getDataNasc() == null) {
            camposFalta += "Dt Nasc., ";
        }
        if ((configuracaoSistema.getNomePaiPendente() || configuracaoSistema.getNomePaiOb()) && (getPessoa().getNomePai() == null || getPessoa().getNomePai().trim().isEmpty())) {
            camposFalta += "Nome Pai, ";
        }
        if ((configuracaoSistema.getNomeMaePendente() || configuracaoSistema.getNomeMaeOb()) && (getPessoa().getNomeMae() == null || getPessoa().getNomeMae().trim().isEmpty())) {
            camposFalta += "Nome Mãe, ";
        }
        if ((configuracaoSistema.getCpfPendente() || configuracaoSistema.getCfpOb()) && ((getPessoa().getCfp() == null || getPessoa().getCfp().trim().isEmpty())&& UteisValidacao.emptyNumber(getPessoaResponsavel().getCodigo()))) {
            camposFalta += "CPF, ";
        }
        if ((configuracaoSistema.getRgPendente() || configuracaoSistema.getRgOb()) && (getPessoa().getRg() == null || getPessoa().getRg().trim().isEmpty())) {
            camposFalta += "RG, ";
        }
        if ((configuracaoSistema.getPaisPendente() || configuracaoSistema.getPaisOb()) && (getPessoa().getPais() == null || getPessoa().getPais().getCodigo().intValue() == 0)) {
            camposFalta += "País, ";
        }
        if ((configuracaoSistema.getEstadoPendente() || configuracaoSistema.getEstadoOb()) && (getPessoa().getEstadoVO() == null || getPessoa().getEstadoVO().getCodigo().intValue() == 0)) {
            camposFalta += "Estado, ";
        }
        if ((configuracaoSistema.getCidadePendente() || configuracaoSistema.getCidadeOb()) && (getPessoa().getCidade() == null || getPessoa().getCidade().getCodigo().intValue() == 0)) {
            camposFalta += "Cidade, ";
        }
        if ((configuracaoSistema.getProfissaoPendente() || configuracaoSistema.getProfissaoOb()) && (getPessoa().getProfissao() == null || getPessoa().getProfissao().getCodigo().intValue() == 0)) {
            camposFalta += "Profissão, ";
        }
        if ((configuracaoSistema.getSexoPendente() || configuracaoSistema.getSexoOb()) && (getPessoa().getSexo() == null || getPessoa().getSexo().trim().isEmpty())) {
            camposFalta += "Sexo biológico, ";
        }
        if ((configuracaoSistema.isGeneroPendente() || configuracaoSistema.isGeneroOb()) && (getPessoa().getGenero() == null || getPessoa().getGenero().trim().isEmpty())) {
            camposFalta += "Gênero, ";
        }
        if ((configuracaoSistema.isNomeRegistroPendente() || configuracaoSistema.isNomeRegistroOb()) && (getPessoa().getNomeRegistro() == null || getPessoa().getNomeRegistro().trim().isEmpty())) {
            camposFalta += "Nome Registro, ";
        }
        if ((configuracaoSistema.getEstadoCivilPendente() || configuracaoSistema.getEstadoCivilOb()) && (getPessoa().getEstadoCivil() == null || getPessoa().getEstadoCivil().trim().isEmpty())) {
            camposFalta += "Estado Civil, ";
        }
        if ((configuracaoSistema.getGrauInstrucaoPendente() || configuracaoSistema.getGrauInstrucaoOb()) && (getPessoa().getGrauInstrucao() == null || getPessoa().getGrauInstrucao().getCodigo().intValue() == 0)) {
            camposFalta += "Grau Inst., ";
        }
        if ((configuracaoSistema.getWebPagePendente() || configuracaoSistema.getWebPageOb()) && (getPessoa().getWebPage() == null || getPessoa().getWebPage().trim().isEmpty())) {
            camposFalta += "Web Page, ";
        }
        if ((configuracaoSistema.getMatriculaPendente() || configuracaoSistema.getMatriculaOb()) && (getMatricula() == null || getMatricula().trim().isEmpty())) {
            camposFalta += "Matrícula, ";
        }
        if ((configuracaoSistema.getCategoriaPendente() || configuracaoSistema.getCategoriaOb()) && (getCategoria() == null || getCategoria().getCodigo().intValue() == 0)) {
            camposFalta += "Categoria, ";
        }
        if ((configuracaoSistema.getEnderecoPendente() || configuracaoSistema.getEnderecoOb()) && (getPessoa().getEnderecoVOs() == null || getPessoa().getEnderecoVOs().size() == 0)) {
            camposFalta += "Endereço, ";
        }
        if (((configuracaoSistema.getNomeRespFinanceiroPendente() || configuracaoSistema.getNomeRespFinanceiroOb()) && (UteisValidacao.emptyString(getPessoa().getNomeRespFinanceiro())))) {
            camposFalta += "Nome Resp. Financeiro, ";
        }
        if ((configuracaoSistema.getCpfRespFinanceiroPendente() || configuracaoSistema.getCpfRespFinanceiroOb()) && UteisValidacao.emptyString(getPessoa().getCpfRespFinanceiro())) {
            camposFalta += "CPF Resp. Financeiro, ";
        }
        if ((configuracaoSistema.getEmailRespFinanceiroPendente() || configuracaoSistema.getEmailRespFinanceiroOb()) && UteisValidacao.emptyString(getPessoa().getEmailRespFinanceiro())) {
            camposFalta += "E-mail Resp. Financeiro, ";
        }
        if ((configuracaoSistema.getRgRespFinanceiroPendente() || configuracaoSistema.getRgRespFinanceiroOb()) && UteisValidacao.emptyString(getPessoa().getRgRespFinanceiro())) {
            camposFalta += "RG Resp. Financeiro, ";
        }

        if ((getPessoa().getEnderecoVOs() == null || getPessoa().getEnderecoVOs().size() == 0)) {
            if (configuracaoSistema.getBairroPendente() ||configuracaoSistema.getBairroOb()) {
                camposFalta += "Bairro, ";
            }

            if (configuracaoSistema.getCepPendente() || configuracaoSistema.getCepOb()) {
                camposFalta += "CEP, ";
            }

            if (configuracaoSistema.getEnderecoComplementoPendente() || configuracaoSistema.getEnderecoComplementoOb()) {
                camposFalta += "Complemento, ";
            }

            if (configuracaoSistema.getNumeroPendente() || configuracaoSistema.getNumeroOb()) {
                camposFalta += "Número, ";
            }
        } else {
            boolean bairro = false;
            boolean CEP = false;
            boolean complemento = false;
            boolean numero = false;

            if (getPessoa().getEnderecoVOs() != null) {
                for (Object obj : getPessoa().getEnderecoVOs()) {
                    EnderecoVO endereco = (EnderecoVO) obj;
                    if ((configuracaoSistema.getBairroPendente() || configuracaoSistema.getBairroOb())
                            && UteisValidacao.emptyString(endereco.getBairro())
                            && !bairro) {
                        bairro = true;
                        camposFalta += "Bairro, ";
                    }

                    if ((configuracaoSistema.getCepPendente() || configuracaoSistema.getCepOb())
                            && UteisValidacao.emptyString(endereco.getBairro())
                            && !CEP) {
                        CEP = true;
                        camposFalta += "CEP, ";
                    }

                    if ((configuracaoSistema.getEnderecoComplementoPendente() || configuracaoSistema.getEnderecoComplementoOb())
                            && UteisValidacao.emptyString(endereco.getComplemento())
                            && !complemento) {
                        complemento = true;
                        camposFalta += "Complemento, ";
                    }

                    if ((configuracaoSistema.getNumeroPendente() || configuracaoSistema.getNumeroOb())
                            && UteisValidacao.emptyString(endereco.getNumero())
                            && !numero) {
                        numero = true;
                        camposFalta += "Número, ";
                    }
                }
            }
        }

        if ((configuracaoSistema.getTelefonePendente() || configuracaoSistema.getTelefoneOb()) && (getPessoa().getTelefoneVOs() == null || getPessoa().getTelefoneVOs().size() == 0)) {
            camposFalta += "Telefone, ";
        }
        if ((configuracaoSistema.getEmailPendente() || configuracaoSistema.getEmailOb()) && (getPessoa().getEmailVOs() == null || getPessoa().getEmailVOs().size() == 0)) {
            camposFalta += "Email, ";
        }
        if (camposFalta.charAt(camposFalta.length() - 2) == ',') { // verifica se o penúltimo caractere é uma vírgula
            camposFaltaFormatado = camposFalta.substring(0, camposFalta.length() - 2); //apaga a vírgula
            camposFaltaFormatado += "."; // acrescenta uma vírgula no final
        }
        return camposFaltaFormatado;
    }

    /**
     * Método usado para obter os campos que faltam no cadastro de uma pessoa
     * passada como parâmetro.
     *
     * @param configuracaoSistema
     * @return String com os campos que faltam no cadastro do cliente
     */
    public String getValidarDadosPessoaisStringCliente(ConfiguracaoSistemaVO configuracaoSistema, ClienteVO cliente) {
        String camposFalta = new String();
        String camposFaltaFormatado = new String();
        if (configuracaoSistema.getNomePendente() && (cliente.getPessoa().getNome() == null || cliente.getPessoa().getNome().trim().isEmpty())) {
            camposFalta = "Nome, ";
        }
        if (configuracaoSistema.getDataNascPendente() && cliente.getPessoa().getDataNasc() == null) {
            camposFalta += "Dt Nasc., ";
        }
        if (configuracaoSistema.getNomePaiPendente() && (cliente.getPessoa().getNomePai() == null || cliente.getPessoa().getNomePai().trim().isEmpty())) {
            camposFalta += "Nome Pai, ";
        }
        if (configuracaoSistema.getNomeMaePendente() && (cliente.getPessoa().getNomeMae() == null || cliente.getPessoa().getNomeMae().trim().isEmpty())) {
            camposFalta += "Nome Mãe, ";
        }
        if (configuracaoSistema.getCpfPendente() && ((cliente.getPessoa().getCfp() == null || cliente.getPessoa().getCfp().trim().isEmpty()) && UteisValidacao.emptyNumber(cliente.getPessoaResponsavel().getCodigo()))) {
            camposFalta += "CPF, ";
        }
        if (configuracaoSistema.getRgPendente() && (cliente.getPessoa().getRg() == null || cliente.getPessoa().getRg().trim().isEmpty())) {
            camposFalta += "RG, ";
        }
        if (configuracaoSistema.getPaisPendente() && (cliente.getPessoa().getPais() == null || cliente.getPessoa().getPais().getCodigo().intValue() == 0)) {
            camposFalta += "País, ";
        }
        if (configuracaoSistema.getEstadoPendente() && (cliente.getPessoa().getEstadoVO() == null || cliente.getPessoa().getEstadoVO().getCodigo().intValue() == 0)) {
            camposFalta += "Estado, ";
        }
        if (configuracaoSistema.getCidadePendente() && (cliente.getPessoa().getCidade() == null || cliente.getPessoa().getCidade().getCodigo().intValue() == 0)) {
            camposFalta += "Cidade, ";
        }
        if (configuracaoSistema.getProfissaoPendente() && (cliente.getPessoa().getProfissao() == null || cliente.getPessoa().getProfissao().getCodigo().intValue() == 0)) {
            camposFalta += "Profissão, ";
        }
        if (configuracaoSistema.getSexoPendente() && (cliente.getPessoa().getSexo() == null || cliente.getPessoa().getSexo().trim().isEmpty())) {
            camposFalta += "Sexo, ";
        }
        if (configuracaoSistema.getEstadoCivilPendente() && (cliente.getPessoa().getEstadoCivil() == null || cliente.getPessoa().getEstadoCivil().trim().isEmpty())) {
            camposFalta += "Estado Civil, ";
        }
        if (configuracaoSistema.getGrauInstrucaoPendente() && (cliente.getPessoa().getGrauInstrucao() == null || cliente.getPessoa().getGrauInstrucao().getCodigo().intValue() == 0)) {
            camposFalta += "Grau Inst., ";
        }
        if (configuracaoSistema.getWebPagePendente() && (cliente.getPessoa().getWebPage() == null || cliente.getPessoa().getWebPage().trim().isEmpty())) {
            camposFalta += "Web Page, ";
        }
        if (configuracaoSistema.getMatriculaPendente() && (cliente.getMatricula() == null || cliente.getMatricula().trim().isEmpty())) {
            camposFalta += "Matrícula, ";
        }
        if (configuracaoSistema.getCategoriaPendente() && (cliente.getCategoria() == null || cliente.getCategoria().getCodigo().intValue() == 0)) {
            camposFalta += "Categoria, ";
        }
        if (configuracaoSistema.getEnderecoPendente() && (cliente.getPessoa().getEnderecoVOs() == null || cliente.getPessoa().getEnderecoVOs().size() == 0)) {
            camposFalta += "Endereço, ";
        }
        if (configuracaoSistema.getTelefonePendente() && (cliente.getPessoa().getTelefoneVOs() == null || cliente.getPessoa().getTelefoneVOs().size() == 0)) {
            camposFalta += "Telefone, ";
        }
        if (configuracaoSistema.getEmailPendente() && (cliente.getPessoa().getEmailVOs() == null || cliente.getPessoa().getEmailVOs().size() == 0)) {
            camposFalta += "Email, ";
        }
        if (camposFalta.charAt(camposFalta.length() - 2) == ',') { // verifica se o penúltimo caractere é uma vírgula
            camposFaltaFormatado = camposFalta.substring(0, camposFalta.length() - 2); //apaga a vírgula
            camposFaltaFormatado += "."; // acrescenta uma vírgula no final
        }
        return camposFaltaFormatado;
    }

    /**
     * retorna true se algum endereco nao esta completo, ou lança exceção se
     * algum campo obrigatorio nao esta preenchido
     *
     * @param configuracaoSistema
     * @return
     * @throws Exception
     */
    public boolean validarEnderecos(ConfiguracaoSistemaVO configuracaoSistema) throws Exception {
        boolean ret = true;
        // Se algum campo de endereco for obrigatorio e nenhum endereco informado
        if (getPessoa().getEnderecoVOs().size() == 0) {

            if(configuracaoSistema.getEnderecoOb() ){
                throw new Exception("O Endereço do Cliente deve ser informado.");
            }
            String itensObrigatorios = "";
            if( configuracaoSistema.getCepOb()){
                itensObrigatorios += (itensObrigatorios.equals("") ? "":", ") +"CEP";
            }
            if(configuracaoSistema.getEnderecoComplementoOb()){
                itensObrigatorios += (itensObrigatorios.equals("") ? "":", ") +"Complemento";
            }
            if(configuracaoSistema.getBairroOb()){
                itensObrigatorios += (itensObrigatorios.equals("") ? "":", ") +"Bairro";
            }
            if(configuracaoSistema.getNumeroOb()) {
                itensObrigatorios += (itensObrigatorios.equals("") ? "" : ", ") + "número";
            }
            if(!itensObrigatorios.equals("")) {

                throw new Exception("O(s) campo(s) "+itensObrigatorios+" do Endereço do Cliente deve ser informado.");
            }
        }
        // percorre a lista de enderecos
        Iterator i = getPessoa().getEnderecoVOs().iterator();
        while (i.hasNext()) {
            EnderecoVO aux = (EnderecoVO) i.next();
            // verifica se o endereco está completo de acordo com a configuracao
            if (configuracaoSistema.getCepOb() && (aux.getCep() == null || aux.getCep().trim().isEmpty())) {
                throw new Exception("O campo CEP (Endereço) deve ser informado.");
            }
            if (configuracaoSistema.getEnderecoOb() && (aux.getEndereco() == null || aux.getEndereco().trim().isEmpty())) {
                throw new Exception("O campo Endereço (Endereço) deve ser informado.");
            }
            if (configuracaoSistema.getEnderecoComplementoOb() && (aux.getComplemento() == null || aux.getComplemento().trim().isEmpty())) {
                throw new Exception("O campo Complemento (Endereço) deve ser informado.");
            }
            if (configuracaoSistema.getBairroOb() && (aux.getBairro() == null || aux.getBairro().trim().isEmpty())) {
                throw new Exception("O campo Bairro (Endereço) deve ser informado.");
            }
            if (configuracaoSistema.getNumeroOb() && (aux.getNumero() == null || aux.getNumero().trim().isEmpty())) {
                throw new Exception("O campo Número (Endereço) deve ser informado.");
            }
        }
        return ret;
    }

    /**
     * Retorna o objeto da classe
     * <code>Categoria</code> relacionado com (
     * <code>Cliente</code>).
     */
    public CategoriaVO getCategoria() {
        if (categoria == null) {
            categoria = new CategoriaVO();
        }
        return (categoria);
    }

    /**
     * Define o objeto da classe
     * <code>Categoria</code> relacionado com (
     * <code>Cliente</code>).
     */
    public void setCategoria(CategoriaVO obj) {
        this.categoria = obj;
    }

    /**
     * Retorna o objeto da classe
     * <code>Pessoa</code> relacionado com (
     * <code>Cliente</code>).
     */
    public PessoaVO getPessoa() {
        if (pessoa == null) {
            pessoa = new PessoaVO();
        }
        return (pessoa);
    }

    /**
     * Define o objeto da classe
     * <code>Pessoa</code> relacionado com (
     * <code>Cliente</code>).
     */
    public void setPessoa(PessoaVO obj) {
        this.pessoa = obj;
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe
     * <code>ClienteClassificacao</code>.
     */
    public List getClienteClassificacaoVOs() {
        if (clienteClassificacaoVOs == null) {
            clienteClassificacaoVOs = new ArrayList<ClienteClassificacaoVO>();
        }
        return (clienteClassificacaoVOs);
    }

    /**
     * Define Atributo responsável por manter os objetos da classe
     * <code>ClienteClassificacao</code>.
     */
    public void setClienteClassificacaoVOs(List clienteClassificacaoVOs) {
        this.clienteClassificacaoVOs = clienteClassificacaoVOs;
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe
     * <code>ClienteGrupo</code>.
     */
    public List getClienteGrupoVOs() {
        if (clienteGrupoVOs == null) {
            clienteGrupoVOs = new ArrayList<ClienteGrupoVO>();
        }
        return (clienteGrupoVOs);
    }

    /**
     * Define Atributo responsável por manter os objetos da classe
     * <code>ClienteGrupo</code>.
     */
    public void setClienteGrupoVOs(List clienteGrupoVOs) {
        this.clienteGrupoVOs = clienteGrupoVOs;
    }

    public List<FamiliarVO> getFamiliarVOs() {
        if (familiarVOs == null) {
            familiarVOs = new ArrayList<>();
        }
        return familiarVOs;
    }

    public void setFamiliarVOs(List<FamiliarVO> familiarVOs) {
        this.familiarVOs = familiarVOs;
    }

    public List<FamiliarVO> getFamiliaresExcluidosVOs() {
        if (familiaresExcluidosVOs == null) {
            familiaresExcluidosVOs = new ArrayList<>();
        }
        return familiaresExcluidosVOs;
    }

    public void setFamiliaresExcluidosVOs(List<FamiliarVO> familiaresExcluidosVOs) {
        this.familiaresExcluidosVOs = familiaresExcluidosVOs;
    }

    public String getIdentificadorParaCobranca() {
        if (identificadorParaCobranca == null) {
            identificadorParaCobranca = "";
        }
        return (identificadorParaCobranca);
    }

    public void setIdentificadorParaCobranca(String identificadorParaCobranca) {
        this.identificadorParaCobranca = identificadorParaCobranca;
    }

    public String getContaDigito() {
        if (contaDigito == null) {
            contaDigito = "";
        }
        return (contaDigito);
    }

    public void setContaDigito(String contaDigito) {
        this.contaDigito = contaDigito;
    }

    public String getConta() {
        if (conta == null) {
            conta = "";
        }
        return (conta);
    }

    public void setConta(String conta) {
        this.conta = conta;
    }

    public String getAgenciaDigito() {
        if (agenciaDigito == null) {
            agenciaDigito = "";
        }
        return (agenciaDigito);
    }

    public void setAgenciaDigito(String agenciaDigito) {
        this.agenciaDigito = agenciaDigito;
    }

    public String getAgencia() {
        if (agencia == null) {
            agencia = "";
        }
        return (agencia);
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getBanco() {
        if (banco == null) {
            banco = "";
        }
        return (banco);
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getCodAcesso() {
        if (codAcesso == null) {
            codAcesso = "";
        }
        return (codAcesso);
    }

    public void setCodAcesso(String codAcesso) {
        this.codAcesso = codAcesso;
    }

    public String getMatricula() {
        if (matricula == null) {
            matricula = "";
        }
        return (matricula);
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "VI";
        }
        return (situacao);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo
     * com um domínio específico. Com base no valor de armazenamento do atributo
     * esta função é capaz de retornar o de apresentação correspondente. Útil
     * para campos como sexo, escolaridade, etc.
     */
    public String getSituacao_Apresentar() {
        if (situacao == null) {
            return "";
        }
        if (situacao.equals("AT")) {
            return "Ativo";
        }
        if (situacao.equals("IN")) {
            return "Inativo";
        }
        if (situacao.equals("VI")) {
            return "Visitante";
        }
        if (situacao.equals("TR")) {
            return "Trancado";
        }
        return (situacao);
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCodAcessoAlternativo() {
        if (codAcessoAlternativo == null) {
            codAcessoAlternativo = "";
        }
        return codAcessoAlternativo;
    }

    public void setCodAcessoAlternativo(String codAcessoAlternativo) {
        this.codAcessoAlternativo = codAcessoAlternativo;
    }

    public Boolean getApresentarRichModalErro() {
        if (apresentarRichModalErro == null) {
            apresentarRichModalErro = new Boolean(false);
        }
        return apresentarRichModalErro;
    }

    public void setApresentarRichModalErro(Boolean apresentarRichModalErro) {
        this.apresentarRichModalErro = apresentarRichModalErro;
    }

    public String getMsgErroExisteCliente() {
        if (msgErroExisteCliente == null) {
            msgErroExisteCliente = "";
        }
        return msgErroExisteCliente;
    }

    public void setMsgErroExisteCliente(String msgErroExisteCliente) {
        this.msgErroExisteCliente = msgErroExisteCliente;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public List<VinculoVO> getVinculoVOs() {
        if (vinculoVOs == null) {
            vinculoVOs = new ArrayList<VinculoVO>();
        }
        return vinculoVOs;
    }
    public void setVinculoVOs(List<VinculoVO> vinculoVOs) {
        this.vinculoVOs = vinculoVOs;
    }

    public Integer getCodigoMatricula() {
        if (codigoMatricula == null) {
            codigoMatricula = new Integer(0);
        }
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public Boolean getApresentarRichModalCadastroRapido() {
        if (apresentarRichModalCadastroRapido == null) {
            apresentarRichModalCadastroRapido = new Boolean(true);
        }
        return apresentarRichModalCadastroRapido;
    }

    public void setApresentarRichModalCadastroRapido(Boolean apresentarRichModalCadastroRapido) {
        this.apresentarRichModalCadastroRapido = apresentarRichModalCadastroRapido;
    }

    public Boolean getApresentarBotaoTransferirClienteEmpresa() {
        if (apresentarBotaoTransferirClienteEmpresa == null) {
            apresentarBotaoTransferirClienteEmpresa = new Boolean(true);
        }
        return apresentarBotaoTransferirClienteEmpresa;
    }

    public void setApresentarBotaoTransferirClienteEmpresa(Boolean apresentarBotaoTransferirClienteEmpresa) {
        this.apresentarBotaoTransferirClienteEmpresa = apresentarBotaoTransferirClienteEmpresa;
    }

    public String getNecessidadesEspeciaisSesiCe() {
        return necessidadesEspeciaisSesiCe;
    }

    public void setNecessidadesEspeciaisSesiCe(String necessidadesEspeciaisSesiCe) {
        this.necessidadesEspeciaisSesiCe = necessidadesEspeciaisSesiCe;
    }

    public Date getDataValidadeCadastroSesiCe() {
        return dataValidadeCadastroSesiCe;
    }

    public void setDataValidadeCadastroSesiCe(Date dataValidadeCadastroSesiCe) {
        this.dataValidadeCadastroSesiCe = dataValidadeCadastroSesiCe;
    }

    public String getRazaoSocialEmpresaSesiCe() {
        return razaoSocialEmpresaSesiCe;
    }

    public void setRazaoSocialEmpresaSesiCe(String razaoSocialEmpresaSesiCe) {
        this.razaoSocialEmpresaSesiCe = razaoSocialEmpresaSesiCe;
    }

    public String getStatusMatriculaSesiCe() {
        return statusMatriculaSesiCe;
    }

    public void setStatusMatriculaSesiCe(String statusMatriculaSesiCe) {
        this.statusMatriculaSesiCe = statusMatriculaSesiCe;
    }

    /**
     * @return the abaSelecionada
     */
    public String getAbaSelecionada() {
        if (abaSelecionada == null) {
            abaSelecionada = "abaBasico";
        }
        return abaSelecionada;
    }

    /**
     * @param abaSelecionada the abaSelecionada to set
     */
    public void setAbaSelecionada(String abaSelecionada) {
        this.abaSelecionada = abaSelecionada;
    }

    public Boolean getApresentarAbaContrato() {
        if (apresentarAbaContrato == null) {
            apresentarAbaContrato = new Boolean(false);
        }
        return apresentarAbaContrato;
    }

    public void setApresentarAbaContrato(Boolean apresentarAbaContrato) {
        this.apresentarAbaContrato = apresentarAbaContrato;
    }

    /**
     * @return the dadosClienteJaVisualizados
     */
    public Boolean getDadosClienteJaVisualizados() {
        if (dadosClienteJaVisualizados == null) {
            dadosClienteJaVisualizados = false;
        }
        return dadosClienteJaVisualizados;
    }

    /**
     * @param dadosClienteJaVisualizados the dadosClienteJaVisualizados to set
     */
    public void setDadosClienteJaVisualizados(Boolean dadosClienteJaVisualizados) {
        this.dadosClienteJaVisualizados = dadosClienteJaVisualizados;
    }

    public ProdutoVO getFreePass() {
        if (freePass == null) {
            freePass = new ProdutoVO();
        }
        return freePass;
    }

    public void setFreePass(ProdutoVO freePass) {
        this.freePass = freePass;
    }

    public String getNomeResponsavelFreePass() {
        if (nomeResponsavelFreePass == null) {
            nomeResponsavelFreePass = "";
        }
        return nomeResponsavelFreePass;
    }

    public void setNomeResponsavelFreePass(String nomeResponsavelFreePass) {
        this.nomeResponsavelFreePass = nomeResponsavelFreePass;
    }

    public Integer getResponsavelFreePass() {
        if (responsavelFreePass == null) {
            responsavelFreePass = new Integer(0);
        }
        return responsavelFreePass;
    }

    public void setResponsavelFreePass(Integer responsavelFreePass) {
        this.responsavelFreePass = responsavelFreePass;
    }

    public List<ClienteSituacaoVO> getClienteSituacaoVOs() {
        if (clienteSituacaoVOs == null) {
            clienteSituacaoVOs = new ArrayList<ClienteSituacaoVO>();
        }
        return clienteSituacaoVOs;
    }

    public void setClienteSituacaoVOs(List<ClienteSituacaoVO> clienteSituacaoVOs) {
        this.clienteSituacaoVOs = clienteSituacaoVOs;
    }

    public Boolean getAbilitarEmpresa() {
        if (abilitarEmpresa == null) {
            abilitarEmpresa = false;
        }
        return abilitarEmpresa;
    }

    public void setAbilitarEmpresa(Boolean abilitarEmpresa) {
        this.abilitarEmpresa = abilitarEmpresa;
    }

    public Boolean getAbilitarUsuario() {
        if (abilitarUsuario == null) {
            abilitarUsuario = new Boolean(false);
        }
        return abilitarUsuario;
    }

    public void setAbilitarUsuario(Boolean abilitarUsuario) {
        this.abilitarUsuario = abilitarUsuario;
    }

    public List<ClienteMensagemVO> getListaClienteMensagem() {
        if (listaClienteMensagem == null) {
            listaClienteMensagem = new ArrayList<ClienteMensagemVO>();
        }
        return listaClienteMensagem;
    }

    public void setListaClienteMensagem(List<ClienteMensagemVO> listaClienteMensagem) {
        this.listaClienteMensagem = listaClienteMensagem;
    }

    public Boolean getAbaConsultor() {
        if (abaConsultor == null) {
            abaConsultor = new Boolean(false);
        }
        return abaConsultor;
    }

    public Boolean getAbaMedico() {
        if (abaMedico == null) {
            abaMedico = new Boolean(false);
        }
        return abaMedico;
    }

    public Boolean getAbaObjetivo() {
        if (abaObjetivo == null) {
            abaObjetivo = new Boolean(false);
        }
        return abaObjetivo;
    }

    public void setAbaConsultor(Boolean abaConsultor) {
        this.abaConsultor = abaConsultor;
    }

    public void setAbaMedico(Boolean abaMedico) {
        this.abaMedico = abaMedico;
    }

    public void setAbaObjetivo(Boolean abaObjetivo) {
        this.abaObjetivo = abaObjetivo;
    }

    public Boolean getAbaObservacao() {
        if (abaObservacao == null) {
            abaObservacao = new Boolean(false);
        }
        return abaObservacao;
    }

    public void setAbaObservacao(Boolean abaObservacao) {
        this.abaObservacao = abaObservacao;
    }

    public Boolean getAbaCatraca() {
        if (abaCatraca == null) {
            abaCatraca = new Boolean(false);
        }
        return abaCatraca;
    }

    public void setAbaCatraca(Boolean abaCatraca) {
        this.abaCatraca = abaCatraca;
    }

    public Boolean getAbaAvisoCliente() {
        if (abaAvisoCliente == null) {
            abaAvisoCliente = new Boolean(false);
        }
        return abaAvisoCliente;
    }

    public void setAbaAvisoCliente(Boolean abaAvisoCliente) {
        this.abaAvisoCliente = abaAvisoCliente;
    }

    public List<ClienteMensagemVO> getListaMensagemAvisoCliente() {
        if (listaMensagemAvisoCliente == null) {
            listaMensagemAvisoCliente = new ArrayList<ClienteMensagemVO>();
        }
        return listaMensagemAvisoCliente;
    }

    public List<ClienteMensagemVO> getListaMensagemAvisoClienteOrdenada() {
        List<ClienteMensagemVO> lista = Ordenacao.ordenarLista(getListaMensagemAvisoCliente(), "codigo");
        Collections.reverse(lista);
        return lista;
    }

    public void setListaMensagemAvisoCliente(List<ClienteMensagemVO> listaMensagemAvisoCliente) {
        this.listaMensagemAvisoCliente = listaMensagemAvisoCliente;
    }

    public Boolean getApresentarBotoes() {
        if (apresentarBotoes == null) {
            apresentarBotoes = new Boolean(false);
        }
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public String getDataInicioContrato() {
        if (dataInicioContrato == null) {
            dataInicioContrato = "";
        }
        return dataInicioContrato;
    }

    public void setDataInicioContrato(String dataInicioContrato) {
        this.dataInicioContrato = dataInicioContrato;
    }

    /**
     * @return the codigoPassivo
     */
    public Integer getCodigoPassivo() {
        if (codigoPassivo == null) {
            codigoPassivo = 0;
        }
        return codigoPassivo;
    }

    /**
     * @param codigoPassivo the codigoPassivo to set
     */
    public void setCodigoPassivo(Integer codigoPassivo) {
        this.codigoPassivo = codigoPassivo;
    }

    /**
     * @return the codigoIndicado
     */
    public Integer getCodigoIndicado() {
        if (codigoIndicado == null) {
            codigoIndicado = 0;
        }
        return codigoIndicado;
    }

    /**
     * @param codigoIndicado the codigoIndicado to set
     */
    public void setCodigoIndicado(Integer codigoIndicado) {
        this.codigoIndicado = codigoIndicado;
    }

    /**
     * @return O campo selecionado.
     */
    public Boolean getSelecionado() {
        if (selecionado == null) {
            selecionado = Boolean.FALSE;
        }
        return selecionado;
    }

    /**
     * @param selecionado O novo valor de selecionado.
     */
    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public String getDataTerminoContrato() {
        if (dataTerminoContrato == null) {
            dataTerminoContrato = "";
        }
        return dataTerminoContrato;
    }

    public void setDataTerminoContrato(String dataTerminoContrato) {
        this.dataTerminoContrato = dataTerminoContrato;
    }

    public Double getSaldoContaCorrente() {
        if (saldoContaCorrente == null) {
            saldoContaCorrente = 0.0;
        }
        return saldoContaCorrente;
    }

    public void setSaldoContaCorrente(Double saldoContaCorrente) {
        this.saldoContaCorrente = saldoContaCorrente;
    }

    public List<MovPagamentoVO> getListaHistoricoPagamento() {
        if (listaHistoricoPagamento == null) {
            listaHistoricoPagamento = new ArrayList<MovPagamentoVO>();
        }
        return listaHistoricoPagamento;
    }

    public void setListaHistoricoPagamento(List<MovPagamentoVO> listaHistoricoPagamento) {
        this.listaHistoricoPagamento = listaHistoricoPagamento;
    }

    public List<MovProdutoVO> getListaHistoricoProduto() {
        if (listaHistoricoProduto == null) {
            listaHistoricoProduto = new ArrayList<MovProdutoVO>();
        }
        return listaHistoricoProduto;
    }

    public void setListaHistoricoProduto(List<MovProdutoVO> listaHistoricoProduto) {
        this.listaHistoricoProduto = listaHistoricoProduto;
    }

    public List<MovParcelaVO> getListaParcelas() {
        if (listaParcelas == null) {
            listaParcelas = new ArrayList<MovParcelaVO>();
        }
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public List<MovProdutoVO> getListaProdutosComValidade() {
        if (listaProdutosComValidade == null) {
            listaProdutosComValidade = new ArrayList<MovProdutoVO>();
        }
        return listaProdutosComValidade;
    }

    public void setListaProdutosComValidade(List<MovProdutoVO> listaProdutosComValidade) {
        this.listaProdutosComValidade = listaProdutosComValidade;
    }

    public void setSituacaoClienteSinteticoVO(SituacaoClienteSinteticoDWVO situacaoClienteSinteticoVO) {
        this.situacaoClienteSinteticoVO = situacaoClienteSinteticoVO;
    }

    public SituacaoClienteSinteticoDWVO getSituacaoClienteSinteticoVO() {
        if (situacaoClienteSinteticoVO == null){
            situacaoClienteSinteticoVO = new SituacaoClienteSinteticoDWVO();
        }
        return situacaoClienteSinteticoVO;
    }

    public void obterSituacaoClientePorCLienteEspecifico(ClienteVO obj, Connection con) throws Exception {
        try {
            obj.setClienteSituacaoVOs(new ArrayList());
            obj.setPossuiGymPass(false);
            obj.setPossuiTotalPass(false);
            if (obj.getSituacaoClienteSinteticoVO() != null) {
                if (obj.getSituacaoClienteSinteticoVO().getSituacao().equals("VI")) {
                    validarSituacaoVisitanteEspecifico(obj, con);
                    validarSituacaoGymPass(obj, con);
                    validarSituacaoTotalPass(obj, con);
                    return;
                }
                if (obj.getSituacaoClienteSinteticoVO().getSituacao().equals("AT")) {
                    validarSituacaoAtivoEspecifico(obj);
                    return;
                }
                if (obj.getSituacaoClienteSinteticoVO().getSituacao().equals("TR")) {
                    validarSituacaoTrancadoEspecifico(obj);
                    return;
                }
                if (obj.getSituacaoClienteSinteticoVO().getSituacao().equals("IN")) {
                    validarSituacaoInativoEspecifico(obj, con);
                    validarSituacaoFreePass(obj, con);
                    validarSituacaoGymPass(obj, con);
                    validarSituacaoTotalPass(obj, con);

                    return;
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarSituacaoVisitanteEspecifico(ClienteVO obj, Connection con) throws Exception {
        try {
            String sit = obj.obterSituacaoClienteVisitante(con);
            if (sit.equals("VI")) {
                preencherSituacaoClienteEspecifico(obj, "VI", "");
            } else {
                preencherSituacaoClienteEspecifico(obj, "VI", sit);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarSituacaoAtivoEspecifico(ClienteVO obj) throws Exception {
        try {
            String sit = obj.getSituacaoClienteSinteticoVO().getSituacaoContrato();
            if (sit != null) {
                if (sit.equals("NO")) {
                    preencherSituacaoClienteEspecifico(obj, "AT", "NO");
                } else {
                    preencherSituacaoClienteEspecifico(obj, "AT", sit);
                }
            }

        } catch (Exception e) {
            throw e;
        }
    }

    public void validarSituacaoTrancadoEspecifico(ClienteVO obj) throws Exception {
        try {
            String sit = obj.getSituacaoClienteSinteticoVO().getSituacaoContrato();
            if (sit.equals("TR")) {
                preencherSituacaoClienteEspecifico(obj, "TR", "");
            } else {
                preencherSituacaoClienteEspecifico(obj, "TR", sit);
            }

        } catch (Exception e) {
            throw e;
        }
    }

    public void validarSituacaoInativoEspecifico(ClienteVO obj, Connection con) throws Exception {
        try {
            String sit = obj.getSituacaoClienteSinteticoVO().getSituacaoContrato();

            if (sit.trim().isEmpty()) {
                preencherSituacaoClienteEspecifico(obj, "IN", "");
            } else {
                preencherSituacaoClienteEspecifico(obj, "IN", sit);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void preencherSituacaoClienteEspecifico(ClienteVO obj, String situacao,
            String subordinadaSituacao) {
        ClienteSituacaoVO situacaoCliente = new ClienteSituacaoVO();
        situacaoCliente.setClienteVO(obj);
        situacaoCliente.setSituacao(situacao);
        situacaoCliente.setSubordinadaSituacao(subordinadaSituacao);
        obj.getClienteSituacaoVOs().add(situacaoCliente);
    }

    public ClienteSituacaoVO getClienteSituacaoVO_Apresentar() {
        if (!getClienteSituacaoVOs().isEmpty()) {
            return getClienteSituacaoVOs().get(0);
        }
        return new ClienteSituacaoVO();
    }

    public AcessoClienteVO getUaCliente() {
        return uaCliente;
    }

    public void setUaCliente(AcessoClienteVO uaCliente) {
        this.uaCliente = uaCliente;
    }

    public void setMatriculaExterna(Long matriculaExterna) {
        this.matriculaExterna = matriculaExterna;
    }

    public Long getMatriculaExterna() {
        return matriculaExterna;
    }

    public List<AutorizacaoCobrancaClienteVO> getAutorizacoes() {
        if (autorizacoes == null) {
            autorizacoes = new ArrayList<AutorizacaoCobrancaClienteVO>();
        }
        return autorizacoes;
    }

    public void setAutorizacoes(List<AutorizacaoCobrancaClienteVO> autorizacoes) {
        this.autorizacoes = autorizacoes;
    }

    public boolean isDadosSinteticoPreparados() {
        return dadosSinteticoPreparados;
    }

    public void setDadosSinteticoPreparados(boolean dadosSinteticoPreparados) {
        this.dadosSinteticoPreparados = dadosSinteticoPreparados;
    }

    public UsuarioMovelVO getUsuarioMovelVO() {
        if (usuarioMovelVO == null) {
            usuarioMovelVO = new UsuarioMovelVO();
        }
        return usuarioMovelVO;
    }

    public void setUsuarioMovelVO(UsuarioMovelVO usuarioMovelVO) {
        this.usuarioMovelVO = usuarioMovelVO;
    }

    public JSONObject toJSON() {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                o.put(field.getName(), field.get(this));
            }
            o.put("pessoaObj", this.getPessoa().toJSON());

        } catch (Exception e) {
        }
        return o;
    }

    public AmostraClienteTO toAmostraClienteTO(){

        AmostraClienteTO amostraCliente = new AmostraClienteTO();
        amostraCliente.setCodigoCliente(getCodigo());
        amostraCliente.setSituacao(getSituacao());
        amostraCliente.setNome(getPessoa().getNome());
        amostraCliente.setEmails(getPessoa().emailVOsToString());
        amostraCliente.setTelefones(getPessoa().telefoneVOsToString());
        amostraCliente.setCategoria(getCategoria().getNome());
        amostraCliente.setMatricula(getMatricula());
        amostraCliente.setIdade(getPessoa().getIdadePessoa());
        amostraCliente.setDataCadastro(getPessoa().getDataCadastro());
        amostraCliente.setCodigoPessoa(getPessoa().getCodigo());
        return amostraCliente;
    }

    public ClienteVO(JSONObject o) {
        super();
        inicializarDados();
        try {
            setCodigo(o.getInt("codigo"));
            setCodAcesso(o.getString("codAcesso"));
            setCodigoMatricula(o.getInt("codigoMatricula"));
            setCodAcessoAlternativo(o.optString("codAcessoAlternativo"));

            JSONObject pessoaJson = o.getJSONObject("pessoaObj");
            setPessoa(new PessoaVO(pessoaJson));
        } catch (Exception ignored) {
        }

    }

    public String getSituacaoClienteApresentar() {
        return situacaoClienteApresentar;
    }

    public void setSituacaoClienteApresentar(String situacaoClienteApresentar) {
        this.situacaoClienteApresentar = situacaoClienteApresentar;
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public String getDataNasc() {
        return getPessoa().getDataNasc_Apresentar();
    }

    public Date getDataAniversario() {
        Date dataNascimento = getPessoa().getDataNasc();
        if (dataNascimento == null) {
            return null;
        }

        Calendar nascimentoCal = Calendar.getInstance();
        nascimentoCal.setTime(dataNascimento);

        Calendar aniversarioCal = Calendar.getInstance();
        aniversarioCal.set(Calendar.MONTH, nascimentoCal.get(Calendar.MONTH));
        aniversarioCal.set(Calendar.DAY_OF_MONTH, nascimentoCal.get(Calendar.DAY_OF_MONTH));

        return aniversarioCal.getTime();
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getSituacaoContrato_Apresentar() {
        if (situacaoContrato == null) {
            return "";
        }
        if (situacaoContrato.equals("NO")) {
            return "Normal";
        }
        if (situacaoContrato.equals("AE")) {
            return "Atestado";
        }
        if (situacaoContrato.equals("AV")) {
            return "A Vencer";
        }
        if (situacaoContrato.equals("CA")) {
            return "Cancelado";
        }
        if (situacaoContrato.equals("CR")) {
            return "Férias";
        }
        if (situacaoContrato.equals("DE")) {
            return "Desistente";
        }
        if (situacaoContrato.equals("TR")) {
            return "Trancado";
        }
        if (situacaoContrato.equals("TV")) {
            return "Trancado Vencido";
        }
        if (situacaoContrato.equals("VE")) {
            return "Vencido";
        }

        return (situacaoContrato);
    }

    public UsuarioVO getUsuarioResponsavelVinculo() {
        return usuarioResponsavelVinculo;
    }

    public void setUsuarioResponsavelVinculo(UsuarioVO usuarioResponsavelVinculo) {
        this.usuarioResponsavelVinculo = usuarioResponsavelVinculo;
    }

    public boolean isAtualizarTreino() {
        return atualizarTreino;
    }

    public void setAtualizarTreino(boolean atualizarTreino) {
        this.atualizarTreino = atualizarTreino;
    }

    public boolean isParqPositivo() {
        return parqPositivo;
    }

    public void setParqPositivo(boolean parqPositivo) {
        this.parqPositivo = parqPositivo;
    }

    private static boolean cpfResponsaveisValido(ConfiguracaoSistemaVO configuracaoSistema, PessoaVO pessoaVO) {
        if (configuracaoSistema.isValidarCPFResponsaveis() &&
                Integer.parseInt(pessoaVO.getIdadePessoa()) < 18 &&
                (!UteisValidacao.emptyString(pessoaVO.getCpfMae()) || !UteisValidacao.emptyString(pessoaVO.getCpfPai()))) {
            return true;
        }
        return false;
    }

    public String getValidarDadosObrigatorios(ConfiguracaoSistemaVO configuracaoSistema, String[] idenficadorPessoal, boolean verificarNomePaiMae, ClienteVO cliente) {
        StringBuilder camposFalta = new StringBuilder();
        String camposFaltaFormatado = "";
        boolean mostraNomeMaePai = false;
        if (configuracaoSistema.getNomeOb() && (getPessoa().getNome() == null || getPessoa().getNome().trim().isEmpty())) {
            camposFalta = new StringBuilder("Nome, ");
        }
        if (configuracaoSistema.getDataNascOb() && getPessoa().getDataNasc() == null) {
            camposFalta.append("Dt Nasc., ");
        }
        if (verificarNomePaiMae && configuracaoSistema.isUsarNomeResponsavelNota() && (UteisValidacao.emptyString(getPessoa().getNomePai()) && UteisValidacao.emptyString(getPessoa().getNomeMae()))){
            mostraNomeMaePai = true;
            camposFalta.append("Cliente é menor de 18 anos, é obrigatório informar os dados do responsável para a emissão de nota fiscal, ");
        }
        if (configuracaoSistema.getNomePaiOb() && (UteisValidacao.emptyString(getPessoa().getNomePai())) && !mostraNomeMaePai) {
            camposFalta.append("Nome Pai, ");
        }
        if (configuracaoSistema.getNomeMaeOb() && (UteisValidacao.emptyString(getPessoa().getNomeMae())) && !mostraNomeMaePai) {
            camposFalta.append("Nome Mãe, ");
        }
        if ((configuracaoSistema.isCpfPaiOb() && (UteisValidacao.emptyString(getPessoa().getCpfPai()))) && verificarNomePaiMae  && UteisValidacao.emptyString(getPessoa().getCpfMae())){
            camposFalta.append("CPF Pai, ");
        }
        if ((configuracaoSistema.isCpfMaeOb() && (UteisValidacao.emptyString(getPessoa().getCpfMae()))) && verificarNomePaiMae && UteisValidacao.emptyString(getPessoa().getCpfPai())) {
            camposFalta.append("CPF Mãe, ");
        }
        if (configuracaoSistema.isRgMaeOb() && (UteisValidacao.emptyString(getPessoa().getRgMae())) && verificarNomePaiMae && (UteisValidacao.emptyString(getPessoa().getRgPai()))) {
            camposFalta.append("RG Mãe, ");
        }
        if (configuracaoSistema.isRgPaiOb() && (UteisValidacao.emptyString(getPessoa().getRgPai())) && verificarNomePaiMae && (UteisValidacao.emptyString(getPessoa().getRgMae())) ) {
            camposFalta.append("RG Pai, ");
        }
        if (configuracaoSistema.isContatoEmergenciaOb() && (UteisValidacao.emptyString(getPessoa().getContatoEmergencia()))) {
            camposFalta.append("Contato Emergência, ");
        }
        if (configuracaoSistema.isTelefoneEmergenciaOb() && (UteisValidacao.emptyString(getPessoa().getTelefoneEmergencia()))) {
            camposFalta.append("Telefone de Emergência, ");
        }

        if (cpfResponsaveisValido(configuracaoSistema, getPessoa())) {
            configuracaoSistema.setCfpOb(false);
        }

        if  (configuracaoSistema.getCfpOb()) {
            if (getPessoa().getCfp().trim().isEmpty()
                    && getPessoa().getRne().trim().isEmpty()
                    && getPessoa().getPassaporte().trim().isEmpty()
                    && UteisValidacao.emptyNumber(getPessoaResponsavel().getCodigo())) {
                camposFalta.append(configuracaoSistema.isUsarSistemaInternacional() ? idenficadorPessoal[0] + ", " : "CPF, ");
            }
        }

        if (configuracaoSistema.getRgOb() && (getPessoa().getRg() == null || getPessoa().getRg().trim().isEmpty())) {
            camposFalta.append(configuracaoSistema.isUsarSistemaInternacional() ? idenficadorPessoal[1] + ", " : "RG, ");
        }
        if (configuracaoSistema.getPaisOb() && (getPessoa().getPais() == null || getPessoa().getPais().getCodigo() == 0)) {
            camposFalta.append("País, ");
        }
        if (configuracaoSistema.getEstadoOb() && (getPessoa().getEstadoVO() == null || getPessoa().getEstadoVO().getCodigo() == 0)) {
            camposFalta.append("Estado, ");
        }
        if (configuracaoSistema.getCidadeOb() && (getPessoa().getCidade() == null || getPessoa().getCidade().getCodigo() == 0)) {
            camposFalta.append("Cidade, ");
        }
        if (configuracaoSistema.getProfissaoOb() && (getPessoa().getProfissao() == null || getPessoa().getProfissao().getCodigo() == 0)) {
            camposFalta.append("Profissão, ");
        }
        if (configuracaoSistema.getSexoOb() && (getPessoa().getSexo() == null || getPessoa().getSexo().trim().isEmpty())) {
            camposFalta.append("Sexo, ");
        }
        if (configuracaoSistema.isGeneroOb() && (getPessoa().getGenero() == null || getPessoa().getGenero().trim().isEmpty())) {
            camposFalta.append("Gênero, ");
        }
        if (configuracaoSistema.isNomeRegistroOb() && (getPessoa().getNomeRegistro() == null || getPessoa().getNomeRegistro().trim().isEmpty())) {
            camposFalta.append("Nome Registro, ");
        }
        if (configuracaoSistema.getEstadoCivilOb() && (getPessoa().getEstadoCivil() == null || getPessoa().getEstadoCivil().trim().isEmpty())) {
            camposFalta.append("Estado Civil, ");
        }
        if (configuracaoSistema.getGrauInstrucaoOb() && (getPessoa().getGrauInstrucao() == null || getPessoa().getGrauInstrucao().getCodigo() == 0)) {
            camposFalta.append("Grau Inst., ");
        }
        if (configuracaoSistema.getMatriculaOb() && (getMatricula() == null || getMatricula().trim().isEmpty())) {
            camposFalta.append("Matrícula, ");
        }
        if (configuracaoSistema.getEnderecoOb() && (getPessoa().getEnderecoVOs() == null || getPessoa().getEnderecoVOs().size() == 0)) {
            camposFalta.append("Endereço, ");
        }
        if (configuracaoSistema.getTelefoneOb() && (getPessoa().getTelefoneVOs() == null || getPessoa().getTelefoneVOs().size() == 0)) {
            camposFalta.append("Telefone, ");
        }
        if (configuracaoSistema.getEmailOb() && (getPessoa().getEmailVOs() == null || getPessoa().getEmailVOs().size() == 0)) {
            camposFalta.append("Email, ");
        }
        if (configuracaoSistema.isUtilizarServicoSesiSC()) {
            if ((configuracaoSistema.getObjCnpjSesi() && UteisValidacao.emptyString(getPessoa().getCnpjSesi())) ||
                    (cliente != null && cliente.getCategoria() != null && cliente.getCategoria().isObrigatorioCnpjClienteSesi() &&
                            UteisValidacao.emptyString(getPessoa().getCnpjSesi()))) {
                camposFalta.append("CNPJ Sesi Indústria, ");
            }
        }
        if (UteisValidacao.emptyList(getPessoa().getEnderecoVOs())){
            if ((configuracaoSistema.getCepOb()) && !camposFalta.toString().contains("CEP,")) {
                camposFalta.append("CEP, ");
            }
            if ((configuracaoSistema.getEnderecoOb() && !camposFalta.toString().contains("Endereço,"))) {
                camposFalta.append("Endereço, ");
            }
            if ((configuracaoSistema.getEnderecoComplementoOb() && !camposFalta.toString().contains("Complemento,"))) {
                camposFalta.append("Complemento, ");
            }
            if ((configuracaoSistema.getBairroOb() && !camposFalta.toString().contains("Bairro,"))) {
                camposFalta.append("Bairro, ");
            }
            if ((configuracaoSistema.getNumeroOb() && !camposFalta.toString().contains("Número,"))) {
                camposFalta.append("Número, ");

            }
        }else{
            for (EnderecoVO aux : getPessoa().getEnderecoVOs()) {
                // verifica se o endereco está completo de acordo com a configuracao
                if ((configuracaoSistema.getCepOb() && (aux.getCep() == null || aux.getCep().trim().isEmpty()))
                        && !camposFalta.toString().contains("CEP,")) {
                    camposFalta.append("CEP, ");
                }
                if ((configuracaoSistema.getEnderecoOb() && (aux.getEndereco() == null || aux.getEndereco().trim().isEmpty()))
                        && !camposFalta.toString().contains("Endereço,")) {
                    camposFalta.append("Endereço, ");
                }
                if ((configuracaoSistema.getEnderecoComplementoOb() && (aux.getComplemento() == null || aux.getComplemento().trim().isEmpty()))
                        && !camposFalta.toString().contains("Complemento,")) {
                    camposFalta.append("Complemento, ");
                }
                if ((configuracaoSistema.getBairroOb() && (aux.getBairro() == null || aux.getBairro().trim().isEmpty()))
                        && !camposFalta.toString().contains("Bairro,")) {
                    camposFalta.append("Bairro, ");
                }
                if ((configuracaoSistema.getNumeroOb() && (aux.getNumero() == null || aux.getNumero().trim().isEmpty()))
                        && !camposFalta.toString().contains("Número,")) {
                    camposFalta.append("Número, ");

                }
            }
        }

        if (camposFalta.toString().contains(",") && camposFalta.charAt(camposFalta.length() - 2) == ',') { // verifica se o penúltimo caractere é uma vírgula
            camposFaltaFormatado = camposFalta.substring(0, camposFalta.length() - 2); //apaga a vírgula
            camposFaltaFormatado += "."; // acrescenta uma vírgula no final
        }
        return camposFaltaFormatado;
    }

    public String getPlanoApresentar () {
        return getSituacaoClienteSinteticoVO().getNomePlano();
    }

    public ClienteWS toWS() {
        ClienteWS clienteWS = new ClienteWS();
        clienteWS.setCodigo(this.getCodigo());
        clienteWS.setNomeUsuarioMovel(getUsuarioMovelVO().getNome());
        clienteWS.setCodigoPessoa(this.getPessoa().getCodigo());
        clienteWS.setNome(this.getNome_Apresentar());
        clienteWS.setMatricula(this.getMatricula());
        clienteWS.setCPF(this.getPessoa().getCfp());
        if (this.getPessoa().getCidade() !=null)
          clienteWS.setCodigoCidade(this.getPessoa().getCidade().getCodigo());
        if (this.getPessoa().getEstadoVO() !=null)
          clienteWS.setCodigoEstado(this.getPessoa().getEstadoVO().getCodigo());
        for (EmailVO emailVO : this.getPessoa().getEmailVOs()) {
            clienteWS.setEmail(emailVO.getEmail());
            break;
        }
        clienteWS.setSexo(this.getPessoa().getSexo());
        for (TelefoneVO telefoneVO : this.getPessoa().getTelefoneVOs()) {
            if (telefoneVO.getTipoTelefone().equals(TipoTelefoneEnum.RESIDENCIAL.getCodigo())) {
                clienteWS.setTelResidencial(telefoneVO.getNumero());
                break;
            }
        }

        for (TelefoneVO telefoneVO : this.getPessoa().getTelefoneVOs()) {
            if (telefoneVO.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo())) {
                clienteWS.setTelCelular(telefoneVO.getNumero());
                break;
            }
        }

        clienteWS.setEmpresa(this.getEmpresa().getCodigo());
        clienteWS.setPermiteContratosConcomitante(this.getEmpresa().getPermiteContratosConcomintante());
        clienteWS.setDataNascimento(this.getDataNasc());
        for (EnderecoVO enderecoVO : this.getPessoa().getEnderecoVOs()) {
            if (enderecoVO.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                clienteWS.setEndereco(enderecoVO.getEndereco());
                clienteWS.setComplemento(enderecoVO.getComplemento());
                clienteWS.setNumero(enderecoVO.getNumero());
                clienteWS.setBairro(enderecoVO.getBairro());
                clienteWS.setCep(enderecoVO.getCep());
                break;
            }
        }

        clienteWS.setDataCadastro(this.getPessoa().getDataCadastro_Apresentar());
        clienteWS.setSituacao(this.getSituacao_Apresentar());

        for (VinculoVO vinculoVO : this.getVinculoVOs()) {
            if (vinculoVO.getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())) {
                clienteWS.setConsultor(vinculoVO.getColaborador().getPessoa_Apresentar());
                break;
            }
        }

        for (EnderecoVO enderecoVO : this.getPessoa().getEnderecoVOs()) {
            clienteWS.getListaEnderecos().add(enderecoVO.toWS());
        }
        for (TelefoneVO telefoneVO : this.getPessoa().getTelefoneVOs()) {
            clienteWS.getListaTelefones().add(telefoneVO.toWS());
        }
        for (EmailVO emailVO : this.getPessoa().getEmailVOs()) {
            clienteWS.getListaEmails().add(emailVO.toWS());
        }
        for (AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO : this.getAutorizacoes()) {
            clienteWS.getListaAutorizacaoCobranca().add(autorizacaoCobrancaClienteVO.toWS());
        }

        clienteWS.setNomeEmpresa(this.getEmpresa_Apresentar());
        clienteWS.setPontuacao(this.getPontuacao());

        return clienteWS;
    }

    public PessoaVO getPessoaResponsavel() {
        return pessoaResponsavel;
    }

    public void setPessoaResponsavel(PessoaVO pessoaResponsavel) {
        this.pessoaResponsavel = pessoaResponsavel;
    }

    public List<ClienteVO> getClienteDenpentedes() {
        return clienteDenpentedes;
    }

    public void setClienteDenpentedes(List<ClienteVO> clienteDenpentedes) {
        this.clienteDenpentedes = clienteDenpentedes;
    }
    public List<AluguelArmarioVO> getArmariosAlugados() {
        if(armariosAlugados == null)
        armariosAlugados = new ArrayList<AluguelArmarioVO>();
        return armariosAlugados;
    }

    public void setArmariosAlugados(List<AluguelArmarioVO> armariosAlugados) {
        this.armariosAlugados = armariosAlugados;
    }

    public Boolean getApresentarBotaoUsarResponsavel() {
        return apresentarBotaoUsarResponsavel;
    }

    public void setApresentarBotaoUsarResponsavel(Boolean apresentarBotaoUsarResponsavel) {
        this.apresentarBotaoUsarResponsavel = apresentarBotaoUsarResponsavel;
    }

    public String getOrigem() {
        if (origem == null) {
            origem = "";
        }
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public boolean getPassivoIndicado() {
        if (getCodigoPassivo() != 0 || getCodigoIndicado() != 0) {
            return true;
        } else {
            return false;
        }
    }

    public UsuarioVO getUsuarioAux() {
        return usuarioAux;
    }

    public void setUsuarioAux(UsuarioVO usuarioAux) {
        this.usuarioAux = usuarioAux;
    }

    public Integer getNovaMatricula() {
        if (novaMatricula == null) {
            novaMatricula = 0;
        }
        return novaMatricula;
    }

    public void setNovaMatricula(Integer novaMatricula) {
        this.novaMatricula = novaMatricula;
    }

    public String getObservacao() {
        if(observacao == null){
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getNomeLembrete(){
        return UteisValidacao.emptyString(getObservacao()) ?
                getPessoa().getNome() :
                (getPessoa().getNome()+"<br/><b><center>LEMBRETE</center>"+getObservacao()+"</b>");
    }

    public Map<Integer, Integer> getAgendamentosAlterar() {
        return agendamentosAlterar;
    }

    public void setAgendamentosAlterar(Map<Integer, Integer> agendamentosAlterar) {
        this.agendamentosAlterar = agendamentosAlterar;
    }

    public Date getDataValidadeCarteirinha() {
        return dataValidadeCarteirinha;
    }

    public void setDataValidadeCarteirinha(Date dataValidadeCarteirinha) {
        this.dataValidadeCarteirinha = dataValidadeCarteirinha;
    }
    public String getDataValidadeCarteirinha_Apresentar() {
        return Uteis.getDataAplicandoFormatacao(this.dataValidadeCarteirinha, "dd/MM/yyyy");
    }

    public boolean isVerificarCliente() {
        return verificarCliente;
    }

    public void setVerificarCliente(boolean verificarCliente) {
        this.verificarCliente = verificarCliente;
    }

    public Date getVerificadoEm() {
        return verificadoEm;
    }

    public void setVerificadoEm(Date verificadoEm) {
        this.verificadoEm = verificadoEm;
    }

    public boolean isApresentarVerificarCliente() {
        return isVerificarCliente() && getVerificadoEm() == null;
    }
    public boolean isApresentarDesverificarCliente() {
        return isVerificarCliente() && getVerificadoEm() != null;
    }

    public String getVerificadoEm_Apresentar() {
        if (getVerificadoEm() != null) {
            return Uteis.getData(getVerificadoEm());
        }
        return "";
    }

    public String getVerificadoEm_Hint() {
        if (getVerificadoEm() != null) {
            return Uteis.getDataComHora(getVerificadoEm());
        }
        return "";
    }

    public UsuarioVO getUsuarioVerificacao() {
        return usuarioVerificacao;
    }

    public void setUsuarioVerificacao(UsuarioVO usuarioVerificacao) {
        this.usuarioVerificacao = usuarioVerificacao;
    }

    public Double getPorcentagemDescontoBoletoPagAntecipado() {
        return porcentagemDescontoBoletoPagAntecipado;
    }

    public void setPorcentagemDescontoBoletoPagAntecipado(Double porcentagemDescontoBoletoPagAntecipado) {
        this.porcentagemDescontoBoletoPagAntecipado = porcentagemDescontoBoletoPagAntecipado;
    }

    public ObjecaoVO getObjecao() {
        if (objecao == null) {
            objecao = new ObjecaoVO();
        }
        return objecao;
    }

    public void setObjecao(ObjecaoVO objecao) {
        this.objecao = objecao;
    }

    public boolean isTemMaisDeUmaDigital() {
        return temMaisDeUmaDigital;
    }

    public ClienteVO setTemMaisDeUmaDigital(boolean temMaisDeUmaDigital) {
        this.temMaisDeUmaDigital = temMaisDeUmaDigital;
        return this;
    }

    public Integer getCodigoReposicaoExperimental() {
        return codigoReposicaoExperimental;
    }

    public void setCodigoReposicaoExperimental(Integer codigoReposicaoExperimental) {
        this.codigoReposicaoExperimental = codigoReposicaoExperimental;
    }

    public String getAnexo() {
        if (anexo == null) {
            anexo = "";
        }
        return anexo;
    }

    public void setAnexo(String anexo) {
        this.anexo = anexo;
    }

    public String getUrlCompletaAnexo() {
        if (urlCompletaAnexo == null) {
            urlCompletaAnexo = "";
        }
        return urlCompletaAnexo;
    }

    public void setUrlCompletaAnexo(String urlCompletaAnexo) {
        this.urlCompletaAnexo = urlCompletaAnexo;
    }

    public Date getDataCadastroAnexo() {
        return dataCadastroAnexo;
    }

    public void setDataCadastroAnexo(Date dataCadastroAnexo) {
        this.dataCadastroAnexo = dataCadastroAnexo;
    }

    public String getNomeAnexo() {
        if (nomeAnexo == null) {
            nomeAnexo = "";
        }
        return nomeAnexo;
    }

    public void setNomeAnexo(String nomeAnexo) {
        this.nomeAnexo = nomeAnexo;
    }
    
    public String getDataCadastroAnexo_Apresentar(){
        return Uteis.getData(dataCadastroAnexo);
    }


    public Boolean getSesc() {
        if (sesc == null) {
            sesc = false;
        }
        return sesc;
    }

    public void setSesc(Boolean sesc) {
        this.sesc = sesc;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public Double getRenda() {
        if (renda == null) {
            renda = 0.0;
        }
        return renda;
    }

    public String getRendaApresentar() {
        return Formatador.formatarValorMonetario(renda == null ? 0.0 : renda);
    }

    public void setRenda(Double renda) {
        this.renda = renda;
    }

    public String getMatriculaSesc() {
        if (matriculaSesc == null) {
            matriculaSesc = "";
        }
        return matriculaSesc;
    }

    public void setMatriculaSesc(String matriculaSesc) {
        this.matriculaSesc = matriculaSesc;
    }

    public String getNomeSocial() {
        if (nomeSocial == null) {
            nomeSocial = "";
        }
        return nomeSocial;
    }

    public void setNomeSocial(String nomeSocial) {
        this.nomeSocial = nomeSocial;
    }

    public Date getValidadeCartaoSesc() {
        return dataValidadeCarteirinha;
    }

    public String getGympasUniqueToken() {
        return gympasUniqueToken;
    }

    public void setGympasUniqueToken(String gympasUniqueToken) {
        this.gympasUniqueToken = gympasUniqueToken;
    }

    public String getGympassTypeNumber() {
        return gympassTypeNumber;
    }

    public void setGympassTypeNumber(String gympassTypeNumber) {
        this.gympassTypeNumber = gympassTypeNumber;
    }

    public String getEmpresaNome() {
        if (empresaNome == null){
            empresaNome = "";
        }
        return empresaNome;
    }

    public void setEmpresaNome(String empresaNome) {
        this.empresaNome = empresaNome;
    }

    public String getConvenioClienteCobranca() {
        if(convenioClienteCobranca == null){
            convenioClienteCobranca = "";
        }
        return convenioClienteCobranca;
    }

    public void setConvenioClienteCobranca(String convenioClienteCobranca) {
        this.convenioClienteCobranca = convenioClienteCobranca;
    }

    public AutorizacaoCobrancaClienteVO getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(AutorizacaoCobrancaClienteVO autorizacao) {
        this.autorizacao = autorizacao;
    }

    public String getSaldoParceiroFidelidade() {
        if (saldoParceiroFidelidade == null) {
            saldoParceiroFidelidade = "";
        }
        return saldoParceiroFidelidade;
    }

    public void setSaldoParceiroFidelidade(String saldoParceiroFidelidade) {
        this.saldoParceiroFidelidade = saldoParceiroFidelidade;
    }

    public Integer getPontuacao() {
        return pontuacao;
    }

    public void setPontuacao(Integer pontuacao) {
        this.pontuacao = pontuacao;
    }

    public List<OrcamentoVO> getOrcamentos() {
        if (orcamentos == null) {
            orcamentos = new ArrayList<>();
        }
        return orcamentos;
    }

    public void setOrcamentos(List<OrcamentoVO> orcamentos) {
        this.orcamentos = orcamentos;
    }

    public Long getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Long idExterno) {
        this.idExterno = idExterno;
    }

    public String getUsernameAmigoFit() {

        if (usernameAmigoFit == null && getPessoa().getEmailCorrespondencia() != null){
            usernameAmigoFit = empresa.getNomeUsuarioAmigoFit();
        }
        return usernameAmigoFit;
    }

    public void setUsernameAmigoFit(String usernameAmigoFit) {
        this.usernameAmigoFit = usernameAmigoFit;
    }

    public String getSenhaUsuarioAmigoFit() {
        if(senhaUsuarioAmigoFit == null)
            senhaUsuarioAmigoFit = empresa.getSenhaUsuarioAmigoFit();

        return senhaUsuarioAmigoFit;
    }

    public void setSenhaUsuarioAmigoFit(String senhaUsuarioAmigoFit) {
        this.senhaUsuarioAmigoFit = senhaUsuarioAmigoFit;
    }

    public Integer getCodAmigoFit() {
        return codAmigoFit;
    }

    public void setCodAmigoFit(Integer codAmigoFit) {
        this.codAmigoFit = codAmigoFit;
    }

    public BancoVO getBancoAmigoFit() {
        if (bancoAmigoFit ==null){
            bancoAmigoFit = new BancoVO();
        }
        return bancoAmigoFit;
    }

    public void setBancoAmigoFit(BancoVO bancoAmigoFit) {
        this.bancoAmigoFit = bancoAmigoFit;
    }

    public int getAgenciaAmigoFit() {
        return agenciaAmigoFit;
    }

    public void setAgenciaAmigoFit(int agenciaAmigoFit) {
        this.agenciaAmigoFit = agenciaAmigoFit;
    }

    public int getDigitoAgenciaAmigoFit() {
        return digitoAgenciaAmigoFit;
    }

    public void setDigitoAgenciaAmigoFit(int digitoAgenciaAmigoFit) {
        this.digitoAgenciaAmigoFit = digitoAgenciaAmigoFit;
    }

    public int getContaAmigoFit() {
        return contaAmigoFit;
    }

    public void setContaAmigoFit(int contaAmigoFit) {
        this.contaAmigoFit = contaAmigoFit;
    }

    public int getDigitoContaAmigoFit() {
        return digitoContaAmigoFit;
    }

    public void setDigitoContaAmigoFit(int digitoContaAmigoFit) {
        this.digitoContaAmigoFit = digitoContaAmigoFit;
    }

    public Boolean getContaCorrenteAmigoFit(){
        if (contaCorrenteAmigoFit == null) {
            contaCorrenteAmigoFit = false;
        }
        return contaCorrenteAmigoFit;
    }

    public void setContaCorrenteAmigoFit(Boolean contaCorrenteAmigoFit) {
        this.contaCorrenteAmigoFit = contaCorrenteAmigoFit;
    }

    public Boolean getContaPoupancaAmigoFit() {
        if (contaPoupancaAmigoFit == null) {
            contaPoupancaAmigoFit = false;
        }
        return contaPoupancaAmigoFit;
    }

    public void setContaPoupancaAmigoFit(Boolean contaPoupancaAmigoFit) {
        this.contaPoupancaAmigoFit = contaPoupancaAmigoFit;
    }

    public JSONObject toJSONAPI() {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                o.put(field.getName(), field.get(this));
            }
            o.put("pessoaObj", this.getPessoa().toJSONComCPF());

        } catch (Exception e) {
        }
        return o;
    }

    public boolean isUtilizarResponsavelPagamento() {
        return utilizarResponsavelPagamento;
    }

    public void setUtilizarResponsavelPagamento(boolean utilizarResponsavelPagamento) {
        this.utilizarResponsavelPagamento = utilizarResponsavelPagamento;
    }

    public Date getSincronizadoRedeEmpresa() {
        return sincronizadoRedeEmpresa;
    }

    public void setSincronizadoRedeEmpresa(Date sincronizadoRedeEmpresa) {
        this.sincronizadoRedeEmpresa = sincronizadoRedeEmpresa;
    }

    public Integer getTitularPlanoCompartilhado() {
        return titularPlanoCompartilhado;
    }

    public void setTitularPlanoCompartilhado(Integer titularPlanoCompartilhado) {
        this.titularPlanoCompartilhado = titularPlanoCompartilhado;
    }

    public boolean isDependentePlanoCompartilhado() {
        return !UteisValidacao.emptyNumber(getTitularPlanoCompartilhado());
    }

    public boolean isDeveAtualizarDependentesSintetico() {
        return deveAtualizarDependentesSintetico;
    }

    public void setDeveAtualizarDependentesSintetico(boolean deveAtualizarDependentesSintetico) {
        this.deveAtualizarDependentesSintetico = deveAtualizarDependentesSintetico;
    }

    public Integer getCodigoMarcadoFavorito() {
        return codigoMarcadoFavorito;
    }

    public void setCodigoMarcadoFavorito(Integer codigoMarcadoFavorito) {
        this.codigoMarcadoFavorito = codigoMarcadoFavorito;
    }

    public String getSituacaoSPC() {
        return situacaoSPC;
    }

    public void setSituacaoSPC(String situacaoSPC) {
        this.situacaoSPC = situacaoSPC;
    }

    public Date getDataInclusaoSPC() {
        return dataInclusaoSPC;
    }

    public void setDataInclusaoSPC(Date dataInclusaoSPC) {
        this.dataInclusaoSPC = dataInclusaoSPC;
    }

    public String getDataInclusaoSPCApresentar() {
        if (getDataInclusaoSPC() != null) {
            return Uteis.getData(getDataInclusaoSPC());
        }
        return "";
    }

    public boolean getBloquearInclusaoSPC() {
        return bloquearInclusaoSPC;
    }

    public void setBloquearInclusaoSPC(Boolean bloquearInclusaoSPC) {
        this.bloquearInclusaoSPC = bloquearInclusaoSPC;
    }

    public boolean isPossuiGymPass() {
        return possuiGymPass;
    }

    public void setPossuiGymPass(boolean possuiGymPass) {
        this.possuiGymPass = possuiGymPass;
    }

    public boolean isPossuiFreePass() {
        return possuiFreePass;
    }

    public void setPossuiFreePass(boolean possuiFreePass) {
        this.possuiFreePass = possuiFreePass;
    }

    public boolean isPossuiTotalPass() {
        return possuiTotalPass;
    }

    public void setPossuiTotalPass(boolean possuiTotalPass) {
        this.possuiTotalPass = possuiTotalPass;
    }

    public IndicacaoVO getIndicacao() {
        if (indicacao == null) {
            indicacao = new IndicacaoVO();
        }
        return indicacao;
    }

    public void setIndicacao(IndicacaoVO indicacao) {
        this.indicacao = indicacao;
    }

    public Date getDataInclusaoClienteRestricaoRedeEmpresa() {
        return dataInclusaoClienteRestricaoRedeEmpresa;
    }

    public void setDataInclusaoClienteRestricaoRedeEmpresa(Date dataInclusaoClienteRestricaoRedeEmpresa) {
        this.dataInclusaoClienteRestricaoRedeEmpresa = dataInclusaoClienteRestricaoRedeEmpresa;
    }

    public FornecedorVO getEmpresaFornecedor() {
        if (empresaFornecedor == null) {
            empresaFornecedor = new FornecedorVO();
        }
        return empresaFornecedor;
    }

    public void setEmpresaFornecedor(FornecedorVO empresaFornecedor) {
        this.empresaFornecedor = empresaFornecedor;
    }

    public String getTokenGoGood() {
        return tokenGoGood;
    }

    public void setTokenGoGood(String tokenGoGood) {
        this.tokenGoGood = tokenGoGood;
    }

    public Date getDataSincronizacaoFranqueadora() {
        return dataSincronizacaoFranqueadora;
    }

    public void setDataSincronizacaoFranqueadora(Date dataSincronizacaoFranqueadora) {
        this.dataSincronizacaoFranqueadora = dataSincronizacaoFranqueadora;
    }

    public Long getIdManyChat() {
        return idManyChat;
    }

    public void setIdManyChat(Long idManyChat) {
        this.idManyChat = idManyChat;
    }

    public Date getDataSincronizacaoManyChat() {
        return dataSincronizacaoManyChat;
    }

    public void setDataSincronizacaoManyChat(Date dataSincronizacaoManyChat) {
        this.dataSincronizacaoManyChat = dataSincronizacaoManyChat;
    }
}

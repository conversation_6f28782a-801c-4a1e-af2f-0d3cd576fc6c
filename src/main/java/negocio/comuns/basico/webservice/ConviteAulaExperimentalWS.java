package negocio.comuns.basico.webservice;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.conviteaulaexperimental.json.TipoConviteAulaExperimentalJSON;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoTurmaEnum;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import org.json.JSONArray;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.interfaces.ConviteAulaExperimentalServiceInterface;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 19/01/2016.
 */

@WebService(name = "ConviteAulaExperimentalWS", serviceName = "ConviteAulaExperimentalWS")
public class ConviteAulaExperimentalWS {

    private final String RETORNO_OK = "OK";

    /**
     * Operação de Web service
     */
    @WebMethod(operationName = "consultarTipoConvitesAlunoPodeEnviar")
    public String consultarTipoConvitesAlunoPodeEnviar(@WebParam(name = "key") String key,
                                                       @WebParam(name = "empresa") Integer codigoEmpresa,
                                                       @WebParam(name = "codigoCliente") Integer codigoCliente) {
        try {
            SituacaoClienteSinteticoDW situacaoClienteSinteticoDW = DaoAuxiliar.retornarAcessoControle(key).getSituacaoClienteSinteticoDWDao();
            List<TipoConviteAulaExperimentalJSON> lista = new ArrayList<TipoConviteAulaExperimentalJSON>();
            if (situacaoClienteSinteticoDW.clienteTemVinculoComConsultor(codigoCliente)){
                lista = DaoAuxiliar.retornarAcessoControle(key).getTipoConviteAulaExperimental().consultarTipoConvitesVigenteAlunoPodeEnviar(codigoCliente, codigoEmpresa);
            }
            return new JSONArray(lista).toString();
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " +e.getMessage();
        }
    }

    @WebMethod(operationName = "gerarConvite")
    public String gerarConvite(@WebParam(name = "key") String key,
                                @WebParam(name = "empresa") Integer codigoEmpresa,
                                @WebParam(name = "codigoCliente") Integer codigoCliente,
                                @WebParam(name = "codigoTipoConvite") Integer codigoTipoConvite,
                                @WebParam(name = "nomeIndicado") String nomeConvidado,
                                @WebParam(name = "telefoneConvidado") String telefoneConvidado,
                                @WebParam(name = "emailConvidado") String emailConvidado){
        try {
            DaoAuxiliar.retornarAcessoControle(key).getConviteAulaExperimentalService().gerarConviteViaWS(key, OrigemSistemaEnum.APP_TREINO, codigoEmpresa, codigoCliente, codigoTipoConvite, nomeConvidado, telefoneConvidado, emailConvidado);
            return RETORNO_OK;
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " +e.getMessage();
        }
    }

    @WebMethod(operationName = "gerarConviteViaWeb")
    public String gerarConviteViaWeb(@WebParam(name = "key") String key,
                               @WebParam(name = "codigoUsuarioConvidou") Integer codigoUsuarioConvidou,
                               @WebParam(name = "codigoTipoConvite") Integer codigoTipoConvite,
                               @WebParam(name = "codigoIndicadoConvidado") Integer codigoIndicadoConvidado,
                               @WebParam(name = "codigoPassivoConvidado") Integer codigoPassivoConvidado,
                               @WebParam(name = "codigoClienteConvidado") Integer codigoClienteConvidado,
                               @WebParam(name = "codigoColaboradorResponsavelConvite") Integer codigoColaboradorResponsavelConvite){
        try {
            DaoAuxiliar.retornarAcessoControle(key).getConviteAulaExperimentalService().gerarConviteViaWeb(OrigemSistemaEnum.APP_TREINO, codigoUsuarioConvidou, codigoTipoConvite, codigoIndicadoConvidado,codigoPassivoConvidado,codigoClienteConvidado, codigoColaboradorResponsavelConvite);
            return RETORNO_OK;
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " +e.getMessage();
        }
    }



    @WebMethod(operationName = "validarConvite")
    public String validarConvite(@WebParam(name = "key") String key,
                                 @WebParam(name = "empresa") Integer codigoEmpresa,
                                 @WebParam(name = "codigoConvite") Integer codigoConvite){
        try {
            DaoAuxiliar.retornarAcessoControle(key).getConviteAulaExperimentalService().validarConvite(codigoConvite);
            return RETORNO_OK;
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " +e.getMessage();
        }
    }

    @WebMethod(operationName = "excluirConvite")
    public String excluirConvite(@WebParam(name = "key") String key,
                                 @WebParam(name = "empresa") Integer codigoEmpresa,
                                 @WebParam(name = "codigoConvite") Integer codigoConvite,
                                 @WebParam(name = "codigoCliente") Integer codigoCliente){
        try {
            DaoAuxiliar.retornarAcessoControle(key).getConviteAulaExperimentalService().excluirConvite(codigoConvite, codigoCliente);
            return RETORNO_OK;
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " +e.getMessage();
        }
    }


    @WebMethod(operationName = "alterarDadosCadastraisConvidado")
    public String alterarDadosCadastraisConvidado(@WebParam(name = "key") String key,
                                                 @WebParam(name = "empresa") Integer codigoEmpresa,
                                                 @WebParam(name = "codigoConvite") Integer codigoConvite,
                                                 @WebParam(name = "nome") String nome,
                                                 @WebParam(name = "telefoneRes") String telefoneRes,
                                                 @WebParam(name = "telefoneCelular") String telefoneCelular,
                                                 @WebParam(name = "email") String email){
        try {
            DaoAuxiliar.retornarAcessoControle(key).getConviteAulaExperimentalService().alterarDadosCadastraisConvidado(codigoConvite, nome, telefoneRes, telefoneCelular, email);
            return RETORNO_OK;
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " +e.getMessage();
        }
    }

    /**
     * Operação de Web service
     */
    @WebMethod(operationName = "agendarAulaExperimental")
    public String agendarAulaExperimental(@WebParam(name = "key") String key,
                                          @WebParam(name = "codigoConvite") Integer codigoConvite,
                                          @WebParam(name = "dataAula") String dataAula,
                                          @WebParam(name = "codigoHorarioTurma") Integer codigoHorarioTurma) {
        try {
            ConviteAulaExperimentalServiceInterface conviteService = DaoAuxiliar.retornarAcessoControle(key).getConviteAulaExperimentalService();
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            conviteService.agendarAulaExperimental(codigoConvite, sdf.parse(dataAula),codigoHorarioTurma);

            return RETORNO_OK;
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operação de Web service
     */
    @WebMethod(operationName = "desmarcarAulaExperimental")
    public String desmarcarAulaExperimental(@WebParam(name = "key") String key,
                                            @WebParam(name = "codigoConvite") Integer codigoConvite,
                                            @WebParam(name = "dataAula") String dataAula,
                                            @WebParam(name = "codigoHorarioTurma") Integer codigoHorarioTurma) {
        try {
            ConviteAulaExperimentalServiceInterface conviteService = DaoAuxiliar.retornarAcessoControle(key).getConviteAulaExperimentalService();
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            conviteService.desmarcarAulaExperimental(codigoConvite, sdf.parse(dataAula),codigoHorarioTurma);

            return RETORNO_OK;
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }


    /**
     * Operação de Web service
     */
    @WebMethod(operationName = "consultarTurmasParaAgendarAulaExperimental")
    public String consultarTurmasParaAgendarAulaExperimental(@WebParam(name = "key") String key,
                                                             @WebParam(name = "inicio") String inicio,
                                                             @WebParam(name = "fim") String fim,
                                                             @WebParam(name = "codigoConvite") Integer codigoConvite,
                                                             @WebParam(name = "consultarAulaCheia") Integer consultarAulaCheia,
                                                             @WebParam(name = "consultarTurmaZW") Integer consultarTurmaZW) {

        try {
            if ((consultarAulaCheia == null) || (consultarTurmaZW == null)){
                throw new ConsistirException("Os parâmetros consultarAulaCheia e consultarTurmaZW devem ser informados.");
            }
            TipoTurmaEnum tipoTurmaEnum;
            if ((consultarAulaCheia == 0) && (consultarTurmaZW == 0)){
                tipoTurmaEnum = TipoTurmaEnum.TODOS;
            }else{
                tipoTurmaEnum = consultarAulaCheia == 0 ? TipoTurmaEnum.AULA_CHEIA : TipoTurmaEnum.TURMA_ZW;
            }
            TurmasServiceInterface turmasService = DaoAuxiliar.retornarAcessoControle(key).getTurmaService();
            List<AgendaTotalJSON> agendamentos = turmasService.consultarTurmasParaAgendarAulaExperimental(
                    tipoTurmaEnum,
                    codigoConvite,
                    Calendario.getDataComHoraZerada(Uteis.getDate(inicio)),
                    Calendario.getDataComHora(Uteis.getDate(fim), "23:59:59"));
            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operação de Web service
     */
    @WebMethod(operationName = "consultarAulaExperimentalAgendada")
    public String consultarAulaExperimentalAgendada(@WebParam(name = "key") String key,
                                                           @WebParam(name = "codigoConvite") Integer codigoConvite,
                                                           @WebParam(name = "consultarAulaCheia") Integer consultarAulaCheia,
                                                           @WebParam(name = "consultarTurmaZW") Integer consultarTurmaZW) {
        try {
            if ((consultarAulaCheia == null) || (consultarTurmaZW == null)){
                throw new ConsistirException("Os parâmetros consultarAulaCheia e consultarTurmaZW devem ser informados.");
            }
            TipoTurmaEnum tipoTurmaEnum;
            if ((consultarAulaCheia == 0) && (consultarTurmaZW == 0)){
                tipoTurmaEnum = TipoTurmaEnum.TODOS;
            }else{
                tipoTurmaEnum = consultarAulaCheia == 0 ? TipoTurmaEnum.AULA_CHEIA : TipoTurmaEnum.TURMA_ZW;
            }
            TurmasServiceInterface turmasService = DaoAuxiliar.retornarAcessoControle(key).getTurmaService();
            List<AgendaTotalJSON> agendamentos = turmasService.consultarAulaExperimentalAgendada(tipoTurmaEnum,codigoConvite);
            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }


}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;

import org.json.JSONObject;
import negocio.comuns.nfe.NotaFiscalDeServicoVO;
import negocio.comuns.nfe.DocumentoFiscalRelatorioPeriodoTO;
import negocio.comuns.nfe.enumerador.ModeloDocumentoFiscal;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.nfe.NotaFiscalDeServico;
import negocio.interfaces.nfe.NotaFiscalDeServicoInterfaceFacade;
import webservice.controle.WebserviceControle;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@WebService(name = "IntegracaoNFSeWS", serviceName = "IntegracaoNFSeWS")
public class IntegracaoNFSeWS {

    /**
     * Operação de Web service
     */
    @WebMethod(operationName = "consultarNotas")
    public String consultarNota(
            @WebParam(name = "chave") String chave,
            @WebParam(name = "rps") Integer rps) {
        try {
            NotaFiscalDeServicoVO notaFiscalDeServicoVO = getDAO(chave).consultarPorChavePrimaria(rps, Uteis.NIVELMONTARDADOS_TODOS);
            return notaFiscalDeServicoVO.toJson().toString();
        } catch (Exception e) {
            return loggerPadrao(e);
        }
    }

    /**
     * Os 3 primeiros parâmetros são exigidos para uma certeza exatidão na hora de diferenciar as notas de cada empresa.
     *
     * @param chaveNFSe       para localizar as notas na empresa no módulo de notas.
     * @param chaveZW         para localizar as notas na empresa no módulo de notas.
     * @param cpfCnpj         para localizar as notas na empresa no módulo de notas.
     * @param modelosPesquisa modelos que serão pesquisados.
     *
     * @return um JSON de {@link DocumentoFiscalRelatorioPeriodoTO} contendo o total de notas <b>CANCELADAS</b> ou <b>NÃO AUTORIZADAS</b>
     * do período de ontem até hoje.
     */
    @WebMethod(operationName = "consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem")
    public String consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(@WebParam(name = "chaveNFSe") String chaveNFSe,
                                                                   @WebParam(name = "chaveZW") String chaveZW,
                                                                   @WebParam(name = "cpfCnpj") String cpfCnpj,
                                                                   @WebParam(name = "modelosPesquisa") List<String> modelosPesquisa) {
        try {
            Set<ModeloDocumentoFiscal> modeloDocumentoFiscalSet = new HashSet<ModeloDocumentoFiscal>();
            for (String modelo : modelosPesquisa) {
                modeloDocumentoFiscalSet.add(ModeloDocumentoFiscal.getEnumByNameThrowsException(modelo));
            }

            Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapRelatorioPorModelo = getDAO(chaveZW)
                    .consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(chaveNFSe, chaveZW, Uteis.removerMascara(cpfCnpj), modeloDocumentoFiscalSet);

            return new JSONObject(mapRelatorioPorModelo).toString();
        } catch (Exception e) {
            return loggerPadrao(e);
        }
    }

    private NotaFiscalDeServicoInterfaceFacade getDAO(String chave) throws Exception {
        WebserviceControle controlws = new WebserviceControle(chave);
        NotaFiscalDeServicoInterfaceFacade dao = new NotaFiscalDeServico(controlws.getCon());
        dao.setCon(controlws.getCon());

        return dao;
    }

    private String loggerPadrao(Exception e) {
        Logger.getLogger(IntegracaoNFSeWS.class.getName()).log(Level.SEVERE, null, e);
        return e.getMessage();
    }

}

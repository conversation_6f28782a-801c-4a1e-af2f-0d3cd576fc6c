/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.webservice;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import br.com.pactosolucoes.agendatotal.json.AgendamentoConfirmadoJSON;
import br.com.pactosolucoes.agendatotal.json.AgendamentoDesmarcadoJSON;
import br.com.pactosolucoes.agendatotal.json.ClienteSintenticoJson;
import br.com.pactosolucoes.agendatotal.json.ParamTurmasJSON;
import br.com.pactosolucoes.agendatotal.json.ResultAlunoClienteSinteticoJSON;
import br.com.pactosolucoes.agendatotal.json.TurmaAulaCheiaJSON;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.controlecredito.json.ControleCreditoTreinoJSON;
import br.com.pactosolucoes.integracao.importacao.ResultEmpresasJSON;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.spivi.EventJSON;
import br.com.pactosolucoes.spivi.SpiviService;
import br.com.pactosolucoes.turmas.json.AlunoAulaAcessoJSON;
import br.com.pactosolucoes.turmas.json.TurmaJSON;
import br.com.pactosolucoes.turmas.servico.dto.HorarioTurmaDTO;
import br.com.pactosolucoes.turmas.servico.dto.ParamAlunoAulaCheiaTO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.plano.HorarioTurmaInterfaceFacade;
import negocio.interfaces.plano.TurmaInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@WebService(name = "IntegracaoTurmasWS", serviceName = "IntegracaoTurmasWS")
public class IntegracaoTurmasWS {

    /**
     * Operaï¿½ï¿½o de Web service
     */
    @WebMethod(operationName = "consultarTurmas")
    public String consultarTurmasVigentes(@WebParam(name = "key") String key,
                                          @WebParam(name = "empresa") Integer empresa,
                                          @WebParam(name = "modalidade") Integer modalidade) {

        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<TurmaVO> turmas = turmasService.consultarTurmas(true, false, modalidade, empresa);
            List<TurmaJSON> turmasJson = new ArrayList<TurmaJSON>();
            for (TurmaVO turma : turmas) {
                turmasJson.add(new TurmaJSON(turma));
            }
            return new JSONArray(turmasJson.isEmpty() ? new ArrayList<TurmaJSON>() : turmasJson).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operaï¿½ï¿½o de Web service
     */
    @WebMethod(operationName = "consultarAgendamentos")
    public String consultarAgendamentos(@WebParam(name = "key") String key,
                                        @WebParam(name = "inicio") String inicio,
                                        @WebParam(name = "fim") String fim,
                                        @WebParam(name = "empresa") Integer empresa,
                                        @WebParam(name = "modalidade") String modalidades) {

        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            final List<AgendaTotalJSON> agendamentos = turmasService.consultarParaAgenda(
                    Calendario.getDataComHoraZerada(Uteis.getDate(inicio)),
                    Calendario.getDataComHora(Uteis.getDate(fim), "23:59:59"),
                    null, montarListaModalidades(modalidades), empresa,
                    modalidades != null && modalidades.equals("COLETIVAS"));

            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            JSONArray arrayErro = new JSONArray();
            try {
                JSONObject objErro = new JSONObject();
                objErro.put("erro", e.getMessage());
                arrayErro.put(objErro);
            } catch (Exception ignored) {

            }
            return arrayErro.toString();
        }
    }

    private List<Integer> montarListaModalidades(@WebParam(name = "modalidade") String modalidades) {
        List<Integer> listaModalidades = new ArrayList<Integer>();
        if (modalidades != null && !modalidades.equals("COLETIVAS")) {
            String[] split = modalidades.split(";");
            for (String mod : split) {
                try {
                    listaModalidades.add(Integer.valueOf(mod));
                } catch (Exception e) {
                }
            }
        }
        return listaModalidades;
    }

    /**
     * Operaï¿½ï¿½o de Web service
     */
    @WebMethod(operationName = "consultarAgendados")
    public String consultarAgendados(@WebParam(name = "key") String key,
                                     @WebParam(name = "inicio") String inicio,
                                     @WebParam(name = "fim") String fim,
                                     @WebParam(name = "empresa") Integer empresa) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendadoJSON> agendados = turmasService.consultarAgendadosParaAgenda(
                    Calendario.getDataComHoraZerada(Uteis.getDate(inicio)),
                    Calendario.getDataComHora(Uteis.getDate(fim), "23:59:59"), empresa);
            return new JSONArray(agendados.isEmpty() ? new ArrayList<AgendadoJSON>() : agendados).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operaï¿½ï¿½o de Web service
     */
    @WebMethod(operationName = "consultarReposicoes")
    public String consultarReposicoes(@WebParam(name = "key") String key,
                                      @WebParam(name = "inicio") String inicio,
                                      @WebParam(name = "fim") String fim,
                                      @WebParam(name = "empresa") Integer empresa) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendadoJSON> agendados = turmasService.consultarReposicoesParaAgenda(
                    Calendario.getDataComHoraZerada(Uteis.getDate(inicio)),
                    Calendario.getDataComHora(Uteis.getDate(fim), "23:59:59"),
                    empresa);
            return new JSONArray(agendados.isEmpty() ? new ArrayList<AgendadoJSON>() : agendados).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operaï¿½ï¿½o de Web service
     */
    @WebMethod(operationName = "desmarcarAulas")
    public String desmarcarAulas(@WebParam(name = "key") String key,
                                 @WebParam(name = "params") String params) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            JSONObject obj = new JSONObject(params);
            ParamTurmasJSON param = JSONMapper.getObject(obj, ParamTurmasJSON.class);
            return turmasService.desmarcarAluno(key,
                    param.getIdHorarioTurmaOrigem(),
                    Uteis.getDate(param.getDataOrigem(), "dd/MM/yyyy"),
                    param.getCodigoCliente(),
                    param.getCodigoContrato(),
                    param.getUsuario(),
                    param.getOrigemSistema(),
                    param.getEmpresa(),
                    param.isExperimental(),
                    null,
                    param.getJustificativa());
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operaï¿½ï¿½o de Web service
     */
    @WebMethod(operationName = "reporAula")
    public String reporAula(@WebParam(name = "key") String key,
                            @WebParam(name = "params") String params) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {

            JSONObject obj = new JSONObject(params);
            ParamTurmasJSON param = JSONMapper.getObject(obj, ParamTurmasJSON.class);
            return turmasService.reporAula(key,
                    Uteis.getDate(param.getDataOrigem(), "dd/MM/yyyy"),
                    Uteis.getDate(param.getDataReposicao(), "dd/MM/yyyy"),
                    param.getIdHorarioTurmaOrigem(),
                    param.getIdHorarioTurmaReposicao(),
                    param.getCodigoCliente(),
                    param.getCodigoContrato(),
                    param.getUsuario(),
                    param.getOrigemSistema(),
                    param.getEmpresa(),
                    param.isForcarMarcar(),
                    param.getSpiviSeat(),
                    param.getSpiviEvenID(),
                    param.getSpiviClientID());
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operacaoo de Web service
     */
    @WebMethod(operationName = "inserirAlunoAulaCheia")
    public String inserirAlunoAulaCheia(@WebParam(name = "key") String key,
                                        @WebParam(name = "params") String params) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {

            Uteis.logarDebug("================ Chegou no inserirAlunoAulaCheia WS ================");
            Uteis.logarDebug(params);

            JSONObject obj = new JSONObject(params);
            ParamTurmasJSON param = JSONMapper.getObject(obj, ParamTurmasJSON.class);
            ParamAlunoAulaCheiaTO paramAlunoAulaCheia = new ParamAlunoAulaCheiaTO();
            paramAlunoAulaCheia.setKey(key);
            paramAlunoAulaCheia.setCodigoCliente(param.getCodigoCliente());
            paramAlunoAulaCheia.setCodigoHorarioTurma(param.getIdHorarioTurmaOrigem());
            paramAlunoAulaCheia.setMinutosAgendarAntecedencia(param.getMinutosAgendarAntecedencia());
            paramAlunoAulaCheia.setData( Uteis.getDate(param.getDataOrigem(), "dd/MM/yyyy"));
            paramAlunoAulaCheia.setEmpresa(param.getEmpresa());
            paramAlunoAulaCheia.setAulaExperimental(param.isExperimental());
            paramAlunoAulaCheia.setValidarModalidade(param.isValidarModalidade());
            paramAlunoAulaCheia.setNrAulasExperimentais(param.getNrAulasExperimentais());
            paramAlunoAulaCheia.setValidarHorario(param.isValidarHorario());
            paramAlunoAulaCheia.setProibirComParcelaVencida(param.isProibirComParcelaVencida());
            paramAlunoAulaCheia.setProibirMarcarAulaAntesPagamentoPrimeiraParcela(param.isProibirMarcarAulaAntesPagamentoPrimeiraParcela());
            paramAlunoAulaCheia.setPermAlunoMarcarAulaOutraEmpresa(param.isPermAlunoMarcarAulaOutraEmpresa());
            paramAlunoAulaCheia.setSomenteValidar(param.getSomenteValidar());
            paramAlunoAulaCheia.setOrigemSistema(param.getOrigemSistema());
            paramAlunoAulaCheia.setCodigoUsuario(param.getUsuario());
            paramAlunoAulaCheia.setSpiviClientID(param.getSpiviClientID());
            paramAlunoAulaCheia.setControlarFreepass(param.isControlarFreepass());
            paramAlunoAulaCheia.setIgnorarTolerancia(param.getIgnorarTolerancia());
            paramAlunoAulaCheia.setAutorizado(param.getAutorizado());
            paramAlunoAulaCheia.setNivel(param.getNivel());
            paramAlunoAulaCheia.setFilaEspera(param.getFilaEspera());
            paramAlunoAulaCheia.setNrValidarVezesModalidade(param.getNrValidarVezesModalidade());
            paramAlunoAulaCheia.setModalidade(param.getModalidade());
            paramAlunoAulaCheia.setQtdfaltasbloqueioaluno(param.getQtdfaltasbloqueioaluno());
            paramAlunoAulaCheia.setQtdtempobloqueioaluno(param.getQtdtempobloqueioaluno());
            paramAlunoAulaCheia.setDescontarCreditoContratoAulaMarcada(param.getDescontarCreditoContratoAulaMarcada());
            paramAlunoAulaCheia.setBloquearGerarReposicaoAulaJaReposta(param.getBloquearGerarReposicaoAulaJaReposta());
            // origem 21 = CONVERSAS_IA(21, "Conversas IA")
            if (!UteisValidacao.emptyNumber(param.getOrigemSistema()) && param.getOrigemSistema() == 21) {
                return turmasService.inserirAlunoAulaCheiaConversasIA(paramAlunoAulaCheia);
            }
            if(!UteisValidacao.emptyNumber(param.getAutorizado())){
                return turmasService.inserirAutorizadoAulaCheia(paramAlunoAulaCheia);
            }
            if(!UteisValidacao.emptyString(param.getBookingId())){
                return turmasService.inserirAlunoAulaCheiaBooking(paramAlunoAulaCheia, param.getBookingId());
            }

            return turmasService.inserirAlunoAulaCheia(paramAlunoAulaCheia);
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }


    /**
     * Operaï¿½ï¿½o de Web service
     */
    @WebMethod(operationName = "excluirAlunoAulaCheia")
    public String excluirAlunoAulaCheia(@WebParam(name = "key") String key,
                                        @WebParam(name = "params") String params) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            JSONObject obj = new JSONObject(params);
            ParamTurmasJSON param = JSONMapper.getObject(obj, ParamTurmasJSON.class);
            return turmasService.excluirAlunoAulaCheia(key, param.getCodigoCliente(), param.getCodigoPassivo(), param.getIdHorarioTurmaOrigem(),
                    Uteis.getDate(param.getDataOrigem(), "dd/MM/yyyy"), param.getEmpresa(),
                    param.getMinutosDesmarcarAntecedencia(),
                    param.getOrigemSistema(),
                    param.getUsuario(),
                    param.getLimiteReposicoes(),
                    param.getManterRenovacao(),
                    param.getBloquearGerarReposicaoAulaJaReposta());
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operaï¿½ï¿½o de Web service
     */
    @WebMethod(operationName = "excluirAulaCheia")
    public String excluirAulaCheia(@WebParam(name = "key") String key,
                                   @WebParam(name = "codigoAula") Integer codigoAula,
                                   @WebParam(name = "codigoUsuario") Integer codigoUsuario) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.desativarAulaColetiva(codigoAula, codigoUsuario);
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod
    public String consultarClientesModalidade(
            @WebParam(name = "key") String key,
            @WebParam(name = "empresa") Integer empresa,
            @WebParam(name = "matricula") Integer matricula,
            @WebParam(name = "nome") String nome,
            @WebParam(name = "modalidade") Integer modalidade,
            @WebParam(name = "somenteAtivos") String somenteAtivos) {

        try {
            if (UteisValidacao.emptyString(nome)
                    && UteisValidacao.emptyNumber(matricula)) {
                throw new Exception("Informe o nome ou a matrícula para a consulta");
            }
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }

        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {

            List<AgendadoJSON> consultarAlunos = turmasService.consultarAlunos(
                    empresa, matricula, nome, modalidade,
                    Boolean.valueOf(somenteAtivos), null, null);
            return new JSONArray(consultarAlunos.isEmpty() ? new ArrayList<AgendadoJSON>() : consultarAlunos).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }

    }

    @WebMethod
    public String consultarTemAulaExtra(
            @WebParam(name = "key") String key,
            @WebParam(name = "modalidade") Integer modalidade,
            @WebParam(name = "saldo") Integer saldo,
            @WebParam(name = "contrato") Integer contrato) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.temAulaExtra(contrato, modalidade, saldo).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }

    }

    @WebMethod(operationName = "consultarDesmarcados")
    public String consultarDesmarcados(@WebParam(name = "key") String key,
                                       @WebParam(name = "inicio") String inicio,
                                       @WebParam(name = "fim") String fim,
                                       @WebParam(name = "empresa") Integer empresa) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendamentoDesmarcadoJSON> desmarcados = turmasService.consultarDesmarcados(
                    Uteis.getDate(inicio, "dd/MM/yyyy"),
                    Uteis.getDate(fim, "dd/MM/yyyy"), empresa);
            return new JSONArray(desmarcados.isEmpty() ? new ArrayList<AgendadoJSON>() : desmarcados).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "aulaARepor")
    public String aulaARepor(@WebParam(name = "key") String key,
                             @WebParam(name = "cliente") Integer cliente,
                             @WebParam(name = "contrato") Integer contrato,
                             @WebParam(name = "modalidade") Integer modalidade) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.aulaARepor(cliente, contrato, modalidade);
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "gravarAula")
    public String gravarAula(@WebParam(name = "key") String key,
                             @WebParam(name = "jsonStr") String jsonStr) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            JSONObject obj = new JSONObject(jsonStr);
            TurmaAulaCheiaJSON json = JSONMapper.getObject(obj, TurmaAulaCheiaJSON.class);
            if (json.getCodigo() == null || json.getCodigo() == 0) {
                return turmasService.gravarTurmaAulaCheia(json, key);
            } else {
                return turmasService.alterarTurmaAulaCheia(json, key);
            }

        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "obterAulasColetivas")
    public String obterAulasColetivas(@WebParam(name = "key") String key,
                                      @WebParam(name = "empresa") Integer empresa) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<TurmaAulaCheiaJSON> aulasColetivas = turmasService.obterAulasColetivas(empresa);
            return new JSONArray(aulasColetivas.isEmpty() ? new ArrayList<TurmaAulaCheiaJSON>() : aulasColetivas).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "obterPresencas")
    public String obterPresencas(@WebParam(name = "key") String key,
                                 @WebParam(name = "empresa") Integer empresa) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendadoJSON> presencas = turmasService.obterPresencas(empresa);
            return new JSONArray(presencas.isEmpty() ? new ArrayList<AgendadoJSON>() : presencas).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }


    @WebMethod(operationName = "obterAlunosDeUmaAula")
    public String obterAlunosDeUmaAula(@WebParam(name = "key") String key,
                                       @WebParam(name = "codigoHorario") Integer codigoHorario,
                                       @WebParam(name = "dia") String dia) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendadoJSON> presencas = turmasService.consultarAlunosDeUmaAula(codigoHorario, Uteis.getDate(dia));
            List<ClienteSintenticoJson> clienteSintenticoJsonList = turmasService.findClienteSintetico(presencas);
            ResultAlunoClienteSinteticoJSON objResult = new ResultAlunoClienteSinteticoJSON( clienteSintenticoJsonList, presencas);

            return objResult.toJSON();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "obterAlunosAulaNaoColetiva")
    public String obterAlunosAulaNaoColetiva(@WebParam(name = "key") String key,
                                             @WebParam(name = "codigoHorario") Integer codigoHorario,
                                             @WebParam(name = "dia") String dia) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendadoJSON> presencas = turmasService.consultarAlunosAulaNaoColetiva(codigoHorario, Uteis.getDate(dia));
            List<ClienteSintenticoJson> clienteSintenticoJsonList = turmasService.findClienteSintetico(presencas);

            ResultAlunoClienteSinteticoJSON objResult = new ResultAlunoClienteSinteticoJSON( clienteSintenticoJsonList, presencas);
            return objResult.toJSON();
        } catch (Exception e) {
            e.printStackTrace();
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "obterAlunosHorarioTurma")
    public String obterAlunosHorarioTurma(@WebParam(name = "key") String key,
                                          @WebParam(name = "empresa") Integer empresa,
                                          @WebParam(name = "codigoHorario") Integer codigoHorario,
                                          @WebParam(name = "dia") String dia) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendadoJSON> presencas = turmasService.consultarAlunosPorHorarioTurma(empresa, null, null, false, codigoHorario, Uteis.getDate(dia));
            return new JSONArray(presencas.isEmpty() ? new ArrayList<AgendadoJSON>() : presencas).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "obterAcessos")
    public String obterAcessos(@WebParam(name = "key") String key,
                               @WebParam(name = "empresa") Integer empresa) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.obterUltimosAcessos(empresa).toString();

        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "consultarProximasAulas")
    public String consultarProximasAulas(@WebParam(name = "key") String key,
                                         @WebParam(name = "matricula") Integer matricula,
                                         @WebParam(name = "proximos30dias") String proximos30dias) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendaTotalJSON> agendamentos = turmasService.consultarProximasAulas(matricula, Boolean.valueOf(proximos30dias));
            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "consultarAulasAlunoPeriodo")
    public String consultarAulasAlunoPeriodo(@WebParam(name = "key") String key,
                                             @WebParam(name = "matricula") Integer matricula,
                                             @WebParam(name = "inicio") String inicio,
                                             @WebParam(name = "fim") String fim) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendaTotalJSON> agendamentos = turmasService.consultarAulasAluno(Calendario.getDataComHoraZerada(Uteis.getDate(inicio)),
                    Calendario.getDataComHora(Uteis.getDate(fim), "23:59:59"), matricula);
            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "marcarDesmarcarAulasApp")
    public String marcarDesmarcarAulasApp(@WebParam(name = "key") String key,
                                          @WebParam(name = "matricula") Integer matricula,
                                          @WebParam(name = "horarioturma") Integer horarioturma,
                                          @WebParam(name = "data") String data,
                                          @WebParam(name = "marcar") String marcar,
                                          @WebParam(name = "bloquearParcelaVencida") String bloquearParcelaVencida) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.marcarDesmarcarAlunoApp(key, horarioturma,
                    Uteis.getDate(data, "dd/MM/yyyy"), matricula,
                    Boolean.valueOf(marcar), Boolean.valueOf(bloquearParcelaVencida), false, null);
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @Deprecated
    @WebMethod(operationName = "obterAulasDesmarcadas")
    public String obterAulasDesmarcadas(@WebParam(name = "key") String key,
                                        @WebParam(name = "matricula") Integer matricula) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendaTotalJSON> agendamentos = turmasService.consultarAulasDesmarcadasSemReposicao(matricula, null, null ,null);
            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "obterExtratoCreditos")
    public String obterExtratoCreditos(@WebParam(name = "key") String key,
                                       @WebParam(name = "matricula") Integer matricula,
                                       @WebParam(name = "data") String data) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<ControleCreditoTreinoJSON> extrato = turmasService.consultarExtratoCreditosAluno(matricula, Uteis.getDate(data, "dd/MM/yyyy HH:mm:ss"));
            return new JSONArray(extrato.isEmpty() ? new ArrayList<ControleCreditoTreinoJSON>() : extrato).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operaï¿½ï¿½o de Web service
     */
    @WebMethod(operationName = "consultarAgendamentosModalidadeAluno")
    public String consultarAgendamentosModalidadeAluno(@WebParam(name = "key") String key,
                                                       @WebParam(name = "inicio") String inicio,
                                                       @WebParam(name = "fim") String fim,
                                                       @WebParam(name = "matricula") Integer matricula,
                                                       @WebParam(name = "app") boolean app) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendaTotalJSON> agendamentos = turmasService.consultarParaAgendaModalidadesAluno(
                    Calendario.getDataComHoraZerada(Uteis.getDate(inicio)),
                    Calendario.getDataComHora(Uteis.getDate(fim), "23:59:59"), matricula, app);
            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operaï¿½ï¿½o de Web service
     */
    @WebMethod(operationName = "consultarTurmasAluno")
    public String consultarTurmasAluno(@WebParam(name = "key") String key,
                                       @WebParam(name = "matricula") Integer matricula) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendaTotalJSON> agendamentos = turmasService.consultarTurmasAluno(matricula);
            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "obterSaldoAluno")
    public String obterSaldoAluno(@WebParam(name = "key") String key,
                                  @WebParam(name = "matricula") Integer matricula) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.consultarSaldoAluno(matricula, false, null);
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "consultarProximasAulaCheia")
    public String consultarProximasAulaCheia(@WebParam(name = "key") String key,
                                             @WebParam(name = "matricula") Integer matricula) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendaTotalJSON> agendamentos = turmasService.consultarProximasAulaCheia(matricula);
            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "obterAcessosDia")
    public String obterAcessosDia(@WebParam(name = "key") String key,
                                  @WebParam(name = "empresa") Integer empresa,
                                  @WebParam(name = "inicio") String dia) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.obterAcessosDia(empresa, Uteis.getDate(dia)).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "consultarUmaAula")
    public String consultarUmaAula(@WebParam(name = "key") String key,
                                   @WebParam(name = "dia") String dia,
                                   @WebParam(name = "empresa") Integer codigoHorarioTurma) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            AgendaTotalJSON aula = turmasService.consultarUmaTurma(codigoHorarioTurma, Uteis.getDate(dia));
            return aula != null ? aula.toJSON() : null;
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "consultarProfessores")
    public String consultarProfessores(@WebParam(name = "key") String key,
                                       @WebParam(name = "empresa") Integer empresa,
                                       @WebParam(name = "somenteAtivos") String somenteAtivos) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            JSONArray arr = turmasService.obterProfessores(empresa, Boolean.valueOf(somenteAtivos));
            return arr.toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @Deprecated
    @WebMethod(operationName = "obterAulasDiaAluno")
    public String obterAulasDiaAluno(@WebParam(name = "key") String key,
                                     @WebParam(name = "matricula") Integer matricula,
                                     @WebParam(name = "dia") String dia) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendaTotalJSON> agendamentos = turmasService.consultarAulasDiaAluno(matricula, Uteis.getDate(dia), null);
            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "marcarEuQueroHorario")
    public String marcarEuQueroHorario(@WebParam(name = "key") String key,
                                       @WebParam(name = "codigoAluno") Integer codigoAluno,
                                       @WebParam(name = "horarioturma") Integer horarioturma,
                                       @WebParam(name = "data") String data) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            turmasService.marcarEuQueroHorario(codigoAluno, horarioturma, Uteis.getDate(data, "dd/MM/yyyy"));
            return "OK";
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, e.getMessage(), e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "desmarcarEuQueroHorario")
    public String desmarcarEuQueroHorario(@WebParam(name = "key") String key,
                                          @WebParam(name = "codigoAluno") Integer codigoAluno,
                                          @WebParam(name = "horarioturma") Integer horarioturma,
                                          @WebParam(name = "data") String data) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            turmasService.desmarcarEuQueroHorario(codigoAluno, horarioturma, Uteis.getDate(data, "dd/MM/yyyy"));
            return "OK";
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "consultarDemandasSintetico")
    public String consultarDemandasSintetico(@WebParam(name = "key") String key,
                                             @WebParam(name = "filtroProfessores") String filtroProfessores,
                                             @WebParam(name = "filtroModalidades") String filtroModalidades,
                                             @WebParam(name = "filtroAmbientes") String filtroAmbientes,
                                             @WebParam(name = "filtroTurmas") String filtroTurmas,
                                             @WebParam(name = "filtroHorarios") String filtroHorarios,
                                             @WebParam(name = "filtroDiasDaSemana") String filtroDiasDaSemana,
                                             @WebParam(name = "dataInicio") String dataInicio,
                                             @WebParam(name = "dataFim") String dataFim) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.consultarDemandasSintetico(
                    new JSONArray(filtroProfessores),
                    new JSONArray(filtroModalidades),
                    new JSONArray(filtroAmbientes),
                    new JSONArray(filtroTurmas),
                    new JSONArray(filtroHorarios),
                    new JSONArray(filtroDiasDaSemana),
                    Uteis.getDate(dataInicio, "dd/MM/yyyy"),
                    Uteis.getDate(dataFim, "dd/MM/yyyy")).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }


    @WebMethod(operationName = "consultarDemandasAnalitico")
    public String consultarDemandasAnalitico(@WebParam(name = "key") String key,
                                             @WebParam(name = "filtroProfessores") String filtroProfessores,
                                             @WebParam(name = "filtroModalidades") String filtroModalidades,
                                             @WebParam(name = "filtroAmbientes") String filtroAmbientes,
                                             @WebParam(name = "filtroTurmas") String filtroTurmas,
                                             @WebParam(name = "filtroHorarios") String filtroHorarios,
                                             @WebParam(name = "filtroDiasDaSemana") String filtroDiasDaSemana,
                                             @WebParam(name = "dataInicio") String dataInicio,
                                             @WebParam(name = "dataFim") String dataFim) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.consultarDemandasAnalitico(
                    new JSONArray(filtroProfessores),
                    new JSONArray(filtroModalidades),
                    new JSONArray(filtroAmbientes),
                    new JSONArray(filtroTurmas),
                    new JSONArray(filtroHorarios),
                    new JSONArray(filtroDiasDaSemana),
                    Uteis.getDate(dataInicio, "dd/MM/yyyy"),
                    Uteis.getDate(dataFim, "dd/MM/yyyy")).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "consultarTodasTurmas")
    public String consultarTodasTurmas(@WebParam(name = "key") String key) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.consultarTodasTurmas().toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "consultarTodasHoraInicial")
    public String consultarTodasHoraInicio(@WebParam(name = "key") String key) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.consultarTodasHoraInicial().toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operação de Web service
     */
    @WebMethod(operationName = "consultarTurmasParaAgendarAulaExperimental")
    public String consultarTurmasParaAgendarAulaExperimental(@WebParam(name = "key") String key,
                                                             @WebParam(name = "inicio") String inicio,
                                                             @WebParam(name = "fim") String fim,
                                                             @WebParam(name = "codigoConvite") Integer codigoConvite,
                                                             @WebParam(name = "consultarAulaCheia") Integer consultarAulaCheia,
                                                             @WebParam(name = "consultarTurmaZW") Integer consultarTurmaZW) {

        try {
//            if ((consultarAulaCheia == null) || (consultarTurmaZW == null)){
//                throw new ConsistirException("Os parâmetros consultarAulaCheia e consultarTurmaZW devem ser informados.");
//            }
//            TipoTurmaEnum tipoTurmaEnum;
//            if ((consultarAulaCheia == 0) && (consultarTurmaZW == 0)){
//                tipoTurmaEnum = TipoTurmaEnum.TODOS;
//            }else{
//                tipoTurmaEnum = consultarAulaCheia == 0 ? TipoTurmaEnum.AULA_CHEIA : TipoTurmaEnum.TURMA_ZW;
//            }
//            TurmasServiceInterface turmasService = DaoAuxiliar.retornarAcessoControle(key).getTurmaService();
//            List<AgendaTotalJSON> agendamentos = turmasService.consultarTurmasParaAgendarAulaExperimental(
//                    tipoTurmaEnum,
//                    codigoConvite,
//                    Calendario.getDataComHoraZerada(Uteis.getDate(inicio)),
//                    Calendario.getDataComHora(Uteis.getDate(fim), "23:59:59"));
//            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
            return "";
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    /**
     * Operação de Web service
     */
    @WebMethod(operationName = "consultarAulaExperimentalAgendada")
    public String consultarAulaExperimentalAgendadaTurmaZW(@WebParam(name = "key") String key,
                                                           @WebParam(name = "codigoConvite") Integer codigoConvite,
                                                           @WebParam(name = "consultarAulaCheia") Integer consultarAulaCheia,
                                                           @WebParam(name = "consultarTurmaZW") Integer consultarTurmaZW) {
        try {
//            if ((consultarAulaCheia == null) || (consultarTurmaZW == null)){
//                throw new ConsistirException("Os parâmetros consultarAulaCheia e consultarTurmaZW devem ser informados.");
//            }
//            TipoTurmaEnum tipoTurmaEnum;
//            if ((consultarAulaCheia == 0) && (consultarTurmaZW == 0)){
//                tipoTurmaEnum = TipoTurmaEnum.TODOS;
//            }else{
//                tipoTurmaEnum = consultarAulaCheia == 0 ? TipoTurmaEnum.AULA_CHEIA : TipoTurmaEnum.TURMA_ZW;
//            }
//            TurmasServiceInterface turmasService = DaoAuxiliar.retornarAcessoControle(key).getTurmaService();
//            List<AgendaTotalJSON> agendamentos = turmasService.consultarAulaExperimentalAgendada(tipoTurmaEnum,codigoConvite);
//            return new JSONArray(agendamentos.isEmpty() ? new ArrayList<AgendaTotalJSON>() : agendamentos).toString();
            return "";
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "confirmarAulaAluno")
    public String confirmarAulaAluno(@WebParam(name = "key") String key,
                                     @WebParam(name = "horarioTurma") Integer horarioTurma,
                                     @WebParam(name = "cliente") Integer cliente,
                                     @WebParam(name = "diaAula") String diaAula,
                                     @WebParam(name = "usuario") Integer usuario) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            usuario = usuario == null ? 0 : usuario;
            turmasService.inserirConfirmacaoAulaCheiaAluno(key, cliente, horarioTurma, usuario, Uteis.getDate(diaAula), null);
            return "OK";
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "removerAulaConfirmadaAluno")
    public String removerAulaConfirmadaAluno(@WebParam(name = "key") String key,
                                             @WebParam(name = "horarioTurma") Integer horarioTurma,
                                             @WebParam(name = "cliente") Integer cliente,
                                             @WebParam(name = "diaAula") String diaAula) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            turmasService.removerAulaCheiaConfirmada(key, cliente, 0, horarioTurma, Uteis.getDate(diaAula), null, null);
            return "OK";
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "consultarConfirmados")
    public String consultarConfirmados(@WebParam(name = "key") String key,
                                       @WebParam(name = "inicio") String inicio,
                                       @WebParam(name = "fim") String fim) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<AgendamentoConfirmadoJSON> confirmados = turmasService.consultarConfirmados(
                    Uteis.getDate(inicio, "dd/MM/yyyy"),
                    Uteis.getDate(fim, "dd/MM/yyyy"));
            return new JSONArray(confirmados.isEmpty() ? new ArrayList<AgendadoJSON>() : confirmados).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }


    @WebMethod(operationName = "inserirAlunoExperimental")
    public String inserirAlunoExperimental(@WebParam(name = "key") String key,
                                           @WebParam(name = "matricula") Integer matricula,
                                           @WebParam(name = "horarioturma") Integer horarioturma,
                                           @WebParam(name = "data") String data,
                                           @WebParam(name = "usuario") Integer usuario,
                                           @WebParam(name = "produtoFreePass") Integer produtoFreePass) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.inserirAlunoExperimental(key, horarioturma, Uteis.getDate(data, "dd/MM/yyyy"), matricula, usuario, produtoFreePass);
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }


    @WebMethod(operationName = "consultarProdutosFreePass")
    public String consultarProdutosFreePass(@WebParam(name = "key") String key,
                                            @WebParam(name = "empresa") Integer empresa) {
        try {
            List<ProdutoVO> fr = DaoAuxiliar.retornarAcessoControle(key).getProdutoDao().consultarPorDescricaoTipoProdutoAtivo("", "FR", false, Uteis.NIVELMONTARDADOS_MINIMOS);
            JSONArray array = new JSONArray();
            for(ProdutoVO f : fr){
                JSONObject json = new JSONObject();
                json.put("codigo", f.getCodigo());
                json.put("descricao", f.getDescricao());
                array.put(json);
            }
            return array.toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }


    @WebMethod(operationName = "consultarProdutosAulaCheia")
    public String consultarProdutosAulaCheia(@WebParam(name = "key") String key,
                                             @WebParam(name = "empresa") Integer empresa,
                                             @WebParam(name = "somenteAtivos") Boolean somenteAtivos) {
        try {
            List<ProdutoVO> fr = DaoAuxiliar.retornarAcessoControle(key).getProdutoDao().
                    consultarParaAulaCheia(empresa, false, Uteis.NIVELMONTARDADOS_MINIMOS, somenteAtivos);
            JSONArray array = new JSONArray();
            for(ProdutoVO f : fr){
                JSONObject json = new JSONObject();
                json.put("codigo", f.getCodigo());
                json.put("descricao", f.getDescricao());
                json.put("tipoProduto", f.getTipoProduto());
                array.put(json);
            }
            return array.toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "marcarPresenca")
    public String marcarPresenca(@WebParam(name = "key") String key,
                                 @WebParam(name = "cliente") Integer cliente,
                                 @WebParam(name = "horarioturma") Integer horarioturma,
                                 @WebParam(name = "data") String data,
                                 @WebParam(name = "reposicao") String reposicao,
                                 @WebParam(name = "desmarcar") String desmarcar){
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            turmasService.marcarPresenca(key, Uteis.getDate(data, "dd/MM/yyyy"), horarioturma,
                    cliente, Boolean.valueOf(reposicao), Boolean.valueOf(desmarcar), null, null);
            return "sucesso";
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "alunosPresentes")
    public String alunosPresentes(@WebParam(name = "key") String key,
                                  @WebParam(name = "horarioturma") Integer horarioturma,
                                  @WebParam(name = "data") String data) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            JSONArray array = turmasService.alunosPresentesTurma(horarioturma, Uteis.getDate(data, "dd/MM/yyyy"));
            return array.toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "pesquisarHorarioTurmaPorTurma")
    public String pesquisarHorarioTurmaPorTurma (@WebParam(name = "key") String key,
                                                 @WebParam(name = "turmaId") Integer turmaId) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            HorarioTurmaInterfaceFacade horarioTurmaService = acessoControle.getHorarioTurmaDao();
            TurmaVO turmaVO = new TurmaVO();
            turmaVO.setCodigo(turmaId);
            List<HorarioTurmaVO> horariosTurmaVO = horarioTurmaService.consultarPorTurma(turmaVO, Uteis.NIVELMONTARDADOS_TODOS);
            JSONArray retorno = new JSONArray();
            for (HorarioTurmaVO horarioTurmaVO : horariosTurmaVO) {
                JSONObject object = new JSONObject();
                object.put("id", horarioTurmaVO.getCodigo());
                object.put("nrVagas", horarioTurmaVO.getNrMaximoAluno());
                object.put("diaSemana", horarioTurmaVO.getDiaSemana());
                object.put("inicio", horarioTurmaVO.getHoraInicial());
                object.put("fim", horarioTurmaVO.getHoraFinal());

                TurmaInterfaceFacade turmaService = acessoControle.getTurmaDao();
                TurmaVO turma = turmaService.consultarPorChavePrimaria(horarioTurmaVO.getTurma(), Uteis.NIVELMONTARDADOS_TODOS);
                object.put("titulo", turma.getDescricao());
                object.put("codigoResponsavel", horarioTurmaVO.getProfessor().getCodigo());

                object.put("responsavel", horarioTurmaVO.getProfessor().getPessoa().getNome());
                object.put("fotoProfessor", horarioTurmaVO.getProfessor().getPessoa().getFotoKey());

                retorno.put(object);
            }

            return retorno.toString();
        } catch (Exception ex) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }


    @WebMethod(operationName = "pesquisarHorarioTurma")
    public String pesquisarHorarioTurma(@WebParam(name = "key") String key,
                                        @WebParam(name = "horarioturma") Integer horarioturma){
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            HorarioTurmaInterfaceFacade horarioTurmaService = acessoControle.getHorarioTurmaDao();
            JSONObject object = horarioTurmaService.consultarParaWS(horarioturma);
            return object.toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "consultarAulaPeloClienteHorario")
    public String consultarAulaPeloClienteHorario(@WebParam(name = "key") String key,
                                                  @WebParam(name = "data") String data,
                                                  @WebParam(name = "matricula") String matricula,
                                                  @WebParam(name = "horaInicial") String horaInicial,
                                                  @WebParam(name = "horaFinal") String horaFinal){
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            HorarioTurmaInterfaceFacade horarioTurma = acessoControle.getHorarioTurmaDao();
            JSONArray retorno = horarioTurma.consultarAulaPeloClienteHorario(matricula,  Uteis.getDate(data, "dd/MM/yyyy"), horaInicial, horaFinal);
            return retorno.toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }

    }

    @WebMethod(operationName = "consultarAlunoTemDiaria")
    public String consultarAlunoTemDiaria(@WebParam(name = "key") String key,
                                          @WebParam(name = "codigoCliente") Integer codigoCliente,
                                          @WebParam(name = "codigoModalidade") Integer codigoModalidade,
                                          @WebParam(name = "data") String data){
        try {
            HorarioTurmaInterfaceFacade horarioTurmaService = DaoAuxiliar.retornarAcessoControle(key).getHorarioTurmaDao();
            Boolean retorno = horarioTurmaService.temDiaria(codigoCliente, codigoModalidade, Uteis.getDate(data, "dd/MM/yyyy"));

            return retorno.toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "atualizarConfiguracaoNrAulasExperimentais")
    public String atualizarConfiguracaoNrAulasExperimentais(@WebParam(name = "key") String key,
                                                            @WebParam(name = "nrAulasExperimentaisAntesAlteracao") Integer nrAulasExperimentaisAntesAlteracao,
                                                            @WebParam(name = "valorNovo") Integer valorNovo) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            if(nrAulasExperimentaisAntesAlteracao < 0) {
                return turmasService.atualizarConfiguaracaoNrAulasExperimentaisSinteticoTodos((valorNovo));
            }
            return turmasService.atualizarConfiguaracaoNrAulasExperimentaisSintetico(nrAulasExperimentaisAntesAlteracao, valorNovo);
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "obterAulasColetivasPorDiaSemana")
    public String obterAulasColetivasPorDiaSemana(@WebParam(name = "key") String key,
                                                  @WebParam(name = "empresa") Integer empresa,
                                                  @WebParam(name = "dia") Integer dia) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            List<TurmaAulaCheiaJSON> aulasColetivas = turmasService.obterAulasColetivasPorDiaSemana(empresa, dia);
            return new JSONArray(aulasColetivas.isEmpty() ? new ArrayList<TurmaAulaCheiaJSON>() : aulasColetivas).toString();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "obterAlunosDaAulaComAcesso")
    public String obterAlunosDaAulaComAcesso(@WebParam(name = "key") String key,
                                             @WebParam(name = "horarioTurma") Integer horarioTurma,
                                             @WebParam(name = "dataDaAula") String dataDaAula) {

        List<AlunoAulaAcessoJSON> alunos;
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            HorarioTurma htS = new HorarioTurma(c);
            HorarioTurmaVO ht = htS.consultarPorChavePrimaria(horarioTurma, Uteis.NIVELMONTARDADOS_TODOS);

            String dia = dataDaAula.isEmpty() ? new SimpleDateFormat("yyyy-MM-dd").format(new Date()) : dataDaAula;
            String dataHorarioInicial = dia.concat(" ").concat(ht.getHoraInicialComTolerancia());

            String dataHorarioFinal = dia.concat(" ").concat(ht.getHoraFinal());

            alunos = turmasService.obterAlunosDaAulaComAcesso(horarioTurma, dataDaAula);
            for (AlunoAulaAcessoJSON aluno : alunos) {
                turmasService.obterAlunosAcesso(aluno,dataHorarioInicial,dataHorarioFinal,aluno.getCodigo());
            }

            //SE LISTA DE ALUNOS ESTIVER FAZIA VALIDAR DE O AMBIENTE DA SALA TEM UM COLETO
            if (alunos.isEmpty() && !turmasService.validarColetorPreenchido(horarioTurma)) {
                throw new Exception("O ambiente não tem nenhum coletor registrado, configure um coletor para o ambiente!");
            }


        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return e.getMessage();
        }

        return new JSONArray(alunos).toString();
    }

    @WebMethod(operationName = "existeReposicao")
    public String existeReposicao(@WebParam(name = "key") String key,
                                  @WebParam(name = "horarioTurma") Integer horarioTurma,
                                  @WebParam(name = "cliente") Integer cliente,
                                  @WebParam(name = "dataDaAula") String dataDaAula) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return String.valueOf(turmasService.existeReposicao(horarioTurma, cliente, dataDaAula));
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "buscarUnidades")
    public String buscarUnidades(@WebParam(name = "key") String key) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            ResultEmpresasJSON result = new ResultEmpresasJSON(turmasService.consultarUnidadesEmpresa());

            return result.toJSON();
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "obterAulaSpivi")
    public EventJSON obterAulaSpivi(@WebParam(name = "key") String key,
                                    @WebParam(name = "horarioTurmaCodigo") Integer horarioTurmaCodigo,
                                    @WebParam(name = "data") String data ) {
        try{
            SpiviService spiviService = DaoAuxiliar.retornarAcessoControle(key).getSpiviService();
            HorarioTurmaVO horarioTurma = DaoAuxiliar.retornarAcessoControle(key)
                    .getHorarioTurmaDao()
                    .consultarPorChavePrimaria(horarioTurmaCodigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            return spiviService.obterAula(horarioTurma, data);
        }catch (Exception e){
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return null;
        }
    }

    @WebMethod(operationName = "horariosTurma")
    public List<HorarioTurmaDTO> horariosTurma(@WebParam(name = "key") String key,
                                               @WebParam(name = "turma") Integer turma) {
        try (Connection c = new DAO().obterConexaoEspecifica(key);
             TurmasServiceInterface turmasService = new TurmasServiceImpl(c)) {
            return turmasService.horariosTurma(turma);
        }catch (Exception e){
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            return null;
        }
    }
}

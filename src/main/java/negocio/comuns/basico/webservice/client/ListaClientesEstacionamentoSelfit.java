
package negocio.comuns.basico.webservice.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for listaClientesEstacionamentoSelfit complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="listaClientesEstacionamentoSelfit">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="listaPessoas" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "listaClientesEstacionamentoSelfit", propOrder = {
    "key",
    "listaPessoas"
})
public class ListaClientesEstacionamentoSelfit {

    protected String key;
    protected String listaPessoas;

    /**
     * Gets the value of the key property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Sets the value of the key property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Gets the value of the listaPessoas property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getListaPessoas() {
        return listaPessoas;
    }

    /**
     * Sets the value of the listaPessoas property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setListaPessoas(String value) {
        this.listaPessoas = value;
    }

}

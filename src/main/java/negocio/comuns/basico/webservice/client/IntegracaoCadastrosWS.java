
package negocio.comuns.basico.webservice.client;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.1.6 in JDK 6
 * Generated source version: 2.1
 * 
 */
@WebService(name = "IntegracaoCadastrosWS", targetNamespace = "http://webservice.basico.comuns.negocio/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface IntegracaoCadastrosWS {


    /**
     * 
     * @param idEmpresa
     * @param nomePesquisar
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesPeloNome", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClientesPeloNome")
    @ResponseWrapper(localName = "consultarClientesPeloNomeResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClientesPeloNomeResponse")
    public String consultarClientesPeloNome(
        @WebParam(name = "idEmpresa", targetNamespace = "")
        int idEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "nomePesquisar", targetNamespace = "")
        String nomePesquisar)
        throws Exception
    ;

    /**
     * 
     * @param idEmpresa
     * @param matriculaPesquisar
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesPelaMatricula", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClientesPelaMatricula")
    @ResponseWrapper(localName = "consultarClientesPelaMatriculaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClientesPelaMatriculaResponse")
    public String consultarClientesPelaMatricula(
        @WebParam(name = "idEmpresa", targetNamespace = "")
        int idEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matriculaPesquisar", targetNamespace = "")
        String matriculaPesquisar)
        throws Exception
    ;

    /**
     * 
     * @param idEmpresa
     * @param nomePesquisar
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarProfessoresPeloNome", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarProfessoresPeloNome")
    @ResponseWrapper(localName = "consultarProfessoresPeloNomeResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarProfessoresPeloNomeResponse")
    public String consultarProfessoresPeloNome(
        @WebParam(name = "idEmpresa", targetNamespace = "")
        int idEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "nomePesquisar", targetNamespace = "")
        String nomePesquisar)
        throws Exception
    ;

    /**
     * 
     * @param matriculaPesquisar
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientePorMatriculaExternaImportacao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClientePorMatriculaExternaImportacao")
    @ResponseWrapper(localName = "consultarClientePorMatriculaExternaImportacaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClientePorMatriculaExternaImportacaoResponse")
    public String consultarClientePorMatriculaExternaImportacao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matriculaPesquisar", targetNamespace = "")
        String matriculaPesquisar)
        throws Exception
    ;

    /**
     * 
     * @param idEmpresa
     * @param matriculaPesquisar
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarGrupoDeRiscoPelaMatricula", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarGrupoDeRiscoPelaMatricula")
    @ResponseWrapper(localName = "consultarGrupoDeRiscoPelaMatriculaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarGrupoDeRiscoPelaMatriculaResponse")
    public String consultarGrupoDeRiscoPelaMatricula(
        @WebParam(name = "idEmpresa", targetNamespace = "")
        int idEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matriculaPesquisar", targetNamespace = "")
        String matriculaPesquisar)
        throws Exception
    ;

    /**
     * 
     * @param email
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClienteSinteticoPorEmailPessoa", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClienteSinteticoPorEmailPessoa")
    @ResponseWrapper(localName = "consultarClienteSinteticoPorEmailPessoaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClienteSinteticoPorEmailPessoaResponse")
    public String consultarClienteSinteticoPorEmailPessoa(
        @WebParam(name = "email", targetNamespace = "")
        String email,
        @WebParam(name = "key", targetNamespace = "")
        String key)
        throws Exception
    ;

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarEmpresas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarEmpresas")
    @ResponseWrapper(localName = "consultarEmpresasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarEmpresasResponse")
    public String consultarEmpresas(
        @WebParam(name = "key", targetNamespace = "")
        String key)
        throws Exception
    ;

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarLocaisAcesso", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarLocaisAcesso")
    @ResponseWrapper(localName = "consultarLocaisAcessoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarLocaisAcessoResponse")
    public String consultarLocaisAcesso(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key)
        throws Exception
    ;

    /**
     * 
     * @param localacesso
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarColetores", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarColetores")
    @ResponseWrapper(localName = "consultarColetoresResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarColetoresResponse")
    public String consultarColetores(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localacesso", targetNamespace = "")
        Integer localacesso)
        throws Exception
    ;

    /**
     * 
     * @param parametro
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientes", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClientes")
    @ResponseWrapper(localName = "consultarClientesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClientesResponse")
    public String consultarClientes(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "parametro", targetNamespace = "")
        String parametro)
        throws Exception
    ;

    /**
     * 
     * @param parametro
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarColaboradores", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarColaboradores")
    @ResponseWrapper(localName = "consultarColaboradoresResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarColaboradoresResponse")
    public String consultarColaboradores(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "parametro", targetNamespace = "")
        String parametro)
        throws Exception
    ;

    /**
     * 
     * @param nome
     * @param cpf
     * @param key
     * @param matricula
     * @return
     *     returns java.lang.String
     * @throws Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesTreino", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClientesTreino")
    @ResponseWrapper(localName = "consultarClientesTreinoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClientesTreinoResponse")
    public String consultarClientesTreino(
        @WebParam(name = "cpf", targetNamespace = "")
        String cpf,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula,
        @WebParam(name = "nome", targetNamespace = "")
        String nome)
        throws Exception
    ;

    /**
     * 
     * @param codigoCliente
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClienteSintetico", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClienteSintetico")
    @ResponseWrapper(localName = "consultarClienteSinteticoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ConsultarClienteSinteticoResponse")
    public String consultarClienteSintetico(
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param username
     * @param codigoCliente
     * @param senha
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gerarUsuarioMovelAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.GerarUsuarioMovelAluno")
    @ResponseWrapper(localName = "gerarUsuarioMovelAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.GerarUsuarioMovelAlunoResponse")
    public String gerarUsuarioMovelAluno(
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "username", targetNamespace = "")
        String username);

    /**
     * 
     * @param username
     * @param codigoCliente
     * @param senha
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarUsuarioMovelAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.AlterarUsuarioMovelAluno")
    @ResponseWrapper(localName = "alterarUsuarioMovelAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.AlterarUsuarioMovelAlunoResponse")
    public String alterarUsuarioMovelAluno(
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "username", targetNamespace = "")
        String username);

    /**
     * 
     * @param codigoCliente
     * @param codigoProfessor
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gerarVinculoProfessorAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.GerarVinculoProfessorAluno")
    @ResponseWrapper(localName = "gerarVinculoProfessorAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.GerarVinculoProfessorAlunoResponse")
    public String gerarVinculoProfessorAluno(
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "codigoProfessor", targetNamespace = "")
        Integer codigoProfessor,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     *
     * @param listaPessoas
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "listaClientesEstacionamentoSelfit", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ListaClientesEstacionamentoSelfit")
    @ResponseWrapper(localName = "listaClientesEstacionamentoSelfitResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "negocio.comuns.basico.webservice.client.ListaClientesEstacionamentoSelfitResponse")
    public String listaClientesEstacionamentoSelfit(
            @WebParam(name = "key", targetNamespace = "")
                    String key,
            @WebParam(name = "listaPessoas", targetNamespace = "")
                    String listaPessoas);
}

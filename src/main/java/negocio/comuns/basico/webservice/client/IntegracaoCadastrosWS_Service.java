
package negocio.comuns.basico.webservice.client;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.logging.Logger;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.1.6 in JDK 6
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "IntegracaoCadastrosWS", targetNamespace = "http://webservice.basico.comuns.negocio/", wsdlLocation = "http://localhost:8080/ZillyonWeb/IntegracaoCadastrosWS?wsdl")
public class IntegracaoCadastrosWS_Service
    extends Service
{

    private final static URL INTEGRACAOCADASTROSWS_WSDL_LOCATION;
    private final static Logger logger = Logger.getLogger(negocio.comuns.basico.webservice.client.IntegracaoCadastrosWS_Service.class.getName());

    static {
        URL url = null;
        try {
            URL baseUrl;
            baseUrl = negocio.comuns.basico.webservice.client.IntegracaoCadastrosWS_Service.class.getResource(".");
            url = new URL(baseUrl, "http://localhost:8080/ZillyonWeb/IntegracaoCadastrosWS?wsdl");
        } catch (MalformedURLException e) {
            logger.warning("Failed to create URL for the wsdl Location: 'http://localhost:8080/ZillyonWeb/IntegracaoCadastrosWS?wsdl', retrying as a local file");
            logger.warning(e.getMessage());
        }
        INTEGRACAOCADASTROSWS_WSDL_LOCATION = url;
    }

    public IntegracaoCadastrosWS_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public IntegracaoCadastrosWS_Service() {
        super(INTEGRACAOCADASTROSWS_WSDL_LOCATION, new QName("http://webservice.basico.comuns.negocio/", "IntegracaoCadastrosWS"));
    }

    /**
     * 
     * @return
     *     returns IntegracaoCadastrosWS
     */
    @WebEndpoint(name = "IntegracaoCadastrosWSPort")
    public IntegracaoCadastrosWS getIntegracaoCadastrosWSPort() {
        return super.getPort(new QName("http://webservice.basico.comuns.negocio/", "IntegracaoCadastrosWSPort"), IntegracaoCadastrosWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns IntegracaoCadastrosWS
     */
    @WebEndpoint(name = "IntegracaoCadastrosWSPort")
    public IntegracaoCadastrosWS getIntegracaoCadastrosWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://webservice.basico.comuns.negocio/", "IntegracaoCadastrosWSPort"), IntegracaoCadastrosWS.class, features);
    }

}

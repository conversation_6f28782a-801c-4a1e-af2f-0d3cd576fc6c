/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico;

import java.io.Serializable;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class DicasTO implements Serializable{

    private String titulo;
    private String subTitulo;
    private String texto;
    private String imagem;
    private String video;

    public DicasTO(String titulo, String subTtitulo, String texto, String imagem, String video) {
        this.titulo = titulo;
        this.subTitulo = subTtitulo;
        this.texto = texto;
        this.imagem = imagem;
        this.video = video;
    }

    public boolean getExibirImagem(){
        return !UteisValidacao.emptyString(imagem);
    }
    public boolean getExibirVideo(){
        return !UteisValidacao.emptyString(video);
    }

    public String getVideo() {
        return video;
    }

    public void setVideo(String video) {
        this.video = video;
    }
    
    public String getImagem() {
        return imagem;
    }

    public void setImagem(String imagem) {
        this.imagem = imagem;
    }

    public String getSubTitulo() {
        return subTitulo;
    }

    public void setSubTitulo(String subTtitulo) {
        this.subTitulo = subTtitulo;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    

}

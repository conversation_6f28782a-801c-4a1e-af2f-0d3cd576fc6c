/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.facade.jdbc.basico.TipoColaborador;

/**
 * <AUTHOR>
 */
public class TipoColaboradorVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer colaborador;
    protected String descricao;

    public TipoColaboradorVO() {
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ColaboradorVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(TipoColaboradorVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getDescricao() == null || obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo Tipo Colaborador (Colaborador) deve ser informado.");
        }
    }

    public static String listaTipo_Apresentar(String lista) {
        String aux[] = lista.split(",");
        StringBuilder listaDescricao = new StringBuilder("");
        if (aux == null) {
            return listaDescricao.toString();
        }
        for (String descricao : aux) {
            descricao = descricao.replaceAll("^\"+|\"+$", "");
            TipoColaboradorEnum tipoColaborador = TipoColaboradorEnum.getTipo(descricao);
            if (tipoColaborador != null) {
                listaDescricao.append(tipoColaborador.getDescricao()).append(", ");
            }
            listaDescricao.append(descricao).append(", ");
        }

        listaDescricao.delete(listaDescricao.length() - 2, listaDescricao.length() - 1);
        return listaDescricao.toString();
    }

    public void inicializarDados() {
        setCodigo(new Integer(0));
        setColaborador(0);
        setDescricao("");
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getColaborador() {
        if (colaborador == null) {
            colaborador = 0;
        }
        return colaborador;
    }

    public void setColaborador(Integer colaborador) {
        this.colaborador = colaborador;
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico.
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc.
     */
    public String getDescricao_Apresentar() {
        if (descricao == null) {
            descricao = "";
        }

        TipoColaboradorEnum tipoColaboradorEnum = TipoColaboradorEnum.getTipo(descricao);
        if (tipoColaboradorEnum != null) {
            return tipoColaboradorEnum.getDescricao();
        }

        return descricao;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

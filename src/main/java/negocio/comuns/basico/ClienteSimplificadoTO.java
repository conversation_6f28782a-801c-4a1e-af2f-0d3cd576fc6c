/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import annotations.arquitetura.ExportFormatter;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;
import servicos.bi.exportador.formatadores.FormatadorEnum;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class ClienteSimplificadoTO extends SuperTO {

    private int codigo = 0;
    private int codigoPessoa = 0;
    private String matricula = "";
    private String nome = "";
    private String situacao = "";
    private String cpf = "";
    private String email = "";
    private String endereco = "";
    private String cidade = "";
    private String estado = "";
    private String telefone = "";

    @ExportFormatter(formato = FormatadorEnum.SIM_NAO)
    private boolean existeContratoRenovado = false;
    private Date verificadoEm;
    private String usuarioVerificacao;

    public ClienteSimplificadoTO() {
    }

    public ClienteSimplificadoTO(int codigo, String nome, String matricula, String situacao) {
        this.codigo = codigo;
        this.nome = nome;
        this.matricula = matricula;
        this.situacao = situacao;
    }

    public ClienteSimplificadoTO(int codigo) {
        this.codigo = codigo;
    }

    public ClienteSimplificadoTO(ClienteVO clienteVO, PessoaVO pessoaVO) {
        this.codigo = clienteVO.getCodigo();
        this.codigoPessoa = pessoaVO.getCodigo();
        this.nome = pessoaVO.getNome();
        this.matricula = clienteVO.getMatricula();
        this.situacao = clienteVO.getSituacao();
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo
     * com um domínio específico. Com base no valor de armazenamento do atributo
     * esta função é capaz de retornar o de apresentação correspondente. Útil
     * para campos como sexo, escolaridade, etc.
     */
    public String getSituacao_Apresentar() {
        if (situacao == null) {
            return "";
        }
        if (situacao.equals("AT")) {
            return "Ativo";
        }
        if (situacao.equals("IN")) {
            return "Inativo";
        }
        if (situacao.equals("VI")) {
            return "Visitante";
        }
        if (situacao.equals("TR")) {
            return "Trancado";
        }
        return (situacao);
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(int codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public boolean isExisteContratoRenovado() {
        return existeContratoRenovado;
    }

    public void setExisteContratoRenovado(boolean existeContratoRenovado) {
        this.existeContratoRenovado = existeContratoRenovado;
    }

    public Date getVerificadoEm() {
        return verificadoEm;
    }

    public void setVerificadoEm(Date verificadoEm) {
        this.verificadoEm = verificadoEm;
    }

    public long getVerificadoEm_Ordenacao() {
        if (getVerificadoEm() == null) {
            return 0;
        }
        return getVerificadoEm().getTime();
    }

    public String getVerificadoEm_Hint() {
        if (getVerificadoEm() != null) {
            return Uteis.getDataComHora(getVerificadoEm());
        }
        return "";
    }

    public String getUsuarioVerificacao() {
        if (usuarioVerificacao == null) {
            return "";
        }
        return usuarioVerificacao;
    }

    public void setUsuarioVerificacao(String usuarioVerificacao) {
        this.usuarioVerificacao = usuarioVerificacao;
    }
}

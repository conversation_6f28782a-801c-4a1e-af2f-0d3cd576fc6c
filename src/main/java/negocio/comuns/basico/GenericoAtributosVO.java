/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import java.util.HashMap;
import java.util.Map;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class GenericoAtributosVO extends SuperVO{
    
    private Map<String, Object> mapaAtributos = new HashMap<String, Object>();

    public Map<String, Object> getMapaAtributos() {
        return mapaAtributos;
    }

    public void setMapaAtributos(Map<String, Object> mapaAtributos) {
        this.mapaAtributos = mapaAtributos;
    }
    
    public boolean existeAtributo(String atributo){
        return mapaAtributos != null && mapaAtributos.containsKey(atributo);
    }
    
    public void adicionarAtributo(String atributo, Object valor){
        if(mapaAtributos == null){
            mapaAtributos = new HashMap<String, Object>();
        }
        mapaAtributos.put(atributo,valor);
    }
    
    public Object obterAtributo(String atributo){
        return mapaAtributos != null ? mapaAtributos.get(atributo) : null;
    }
}

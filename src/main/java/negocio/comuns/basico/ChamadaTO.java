
package negocio.comuns.basico;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class ChamadaTO extends SuperTO {
    private static final long serialVersionUID = 5409722782419981655L;
    private PessoaVO aluno = new PessoaVO();
    private List<PresencaVO> listaPresencas = new ArrayList<PresencaVO>();

    public PessoaVO getAluno() {
        return aluno;
    }

    public void setAluno(PessoaVO aluno) {
        this.aluno = aluno;
    }

    public List<PresencaVO> getListaPresencas() {
        return listaPresencas;
    }

    public void setListaPresencas(List<PresencaVO> listaPresencas) {
        this.listaPresencas = listaPresencas;
    }
}

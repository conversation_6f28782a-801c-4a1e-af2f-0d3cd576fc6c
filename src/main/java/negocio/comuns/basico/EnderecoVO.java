package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * Reponsável por manter os dados da entidade Endereco. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see Pessoa
 */
public class EnderecoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;    
    protected String endereco;   
    protected String complemento;  
    protected String numero;
    protected String bairro;  
    protected String cep;  
    protected String tipoEndereco;
    @NaoControlarLogAlteracao
    protected Integer pessoa;    
    protected Boolean enderecoCorrespondencia;
    private String ltdlng;
    private Boolean adicionarAlterarEndereco=false;


    /**
     * Construtor padrão da classe <code>Endereco</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public EnderecoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>EnderecoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(EnderecoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
//        //validação será conforme os campos exigidos na configuração do sistema
//        if (obj.getEndereco().trim().isEmpty()) {
//            throw new ConsistirException("O campo ENDEREÇO (Aba - Endereço) deve ser informado.");
//        }
// Validação removida por causar inconsistência no cadastro detalhado de clientes, uma vez que a informação do número não é obrigatória na tela.
//        if (obj.getNumero().trim().isEmpty()) {
//            throw new ConsistirException("O campo NÚMERO (Aba - Endereço) deve ser informado.");
//        }
//        if (obj.getBairro().trim().isEmpty()) {
//            throw new ConsistirException("O campo BAIRRO (Aba - Endereço) deve ser informado.");
//        }
//        if (obj.getCep().trim().isEmpty()) {
//            throw new ConsistirException("O campo CEP (Aba - Endereço) deve ser informado.");
//        }
//        if (obj.getTipoEndereco() == null) {
//            throw new ConsistirException("O campo TIPO DE ENDEREÇO (Aba - Endereço) deve ser informado.");
//        }
    }

    public boolean isEmpty() throws ConsistirException {
        return (getEndereco().trim().isEmpty() && getNumero().trim().isEmpty() && getComplemento().trim().isEmpty() &&
                getBairro().trim().isEmpty() && getCep().trim().isEmpty());
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setEndereco(getEndereco().toUpperCase());
        setComplemento(getComplemento().toUpperCase());
        setNumero(getNumero().toUpperCase());
        setBairro(getBairro().toUpperCase());
        setCep(getCep().toUpperCase());
        setTipoEndereco(getTipoEndereco().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setEndereco("");
        setComplemento("");
        setNumero("");
        setBairro("");
        setCep("");
        setTipoEndereco("");
        setEnderecoCorrespondencia(new Boolean(false));
    }

    public Integer getPessoa() {
        return (pessoa);
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getTipoEndereco() {
        if (tipoEndereco == null) {
            tipoEndereco = "";
        }
        return (tipoEndereco);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico. 
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o 
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc. 
     */
    public String getTipoEndereco_Apresentar() {
        if (tipoEndereco == null) {
            tipoEndereco = "";
        }
        if (tipoEndereco.equals("RE")) {
            return "Residencial";
        }
        if (tipoEndereco.equals("CO")) {
            return "Comercial";
        }
        return (tipoEndereco);
    }

    public String getEnderecoCorrespondencia_Apresentar() {
        if (enderecoCorrespondencia) {
            return "Sim";
        }
        if (!enderecoCorrespondencia) {
            return "Não";
        }
        return ("");
    }

    public void setTipoEndereco(String tipoEndereco) {
        this.tipoEndereco = tipoEndereco;
    }

    public String getCep() {
        if (cep == null) {
            cep = "";
        }
        return (cep);
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getBairro() {
        if (bairro == null) {
            bairro = "";
        }
        return (bairro);
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getNumero() {
        if (numero == null) {
            numero = "";
        }
        return (numero);
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getComplemento() {
        if (complemento == null) {
            complemento = "";
        }
        return (complemento);
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getEndereco() {
        if (endereco == null) {
            endereco = "";
        }
        return (endereco);
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getEnderecoCorrespondencia() {
        return enderecoCorrespondencia;
    }

    public void setEnderecoCorrespondencia(Boolean enderecoCorrespondencia) {
        this.enderecoCorrespondencia = enderecoCorrespondencia;
    }

    public EnderecoWS toWS() {
        EnderecoWS enderecoWS = new EnderecoWS();
        enderecoWS.setCodigo(this.codigo);
        enderecoWS.setEndereco(this.endereco);
        enderecoWS.setComplemento(this.complemento);
        enderecoWS.setNumero(this.numero);
        enderecoWS.setBairro(this.bairro);
        enderecoWS.setCep(this.cep);
        enderecoWS.setTipoEndereco(this.tipoEndereco);
        enderecoWS.setTipoEnderecoDescricao(this.getTipoEndereco_Apresentar());
        enderecoWS.setEnderecoCorrespondencia(this.enderecoCorrespondencia);
        return enderecoWS;
    }

    public String getLtdlng() {
        return ltdlng;
    }

    public void setLtdlng(String ltdlng) {
        this.ltdlng = ltdlng;
    }

    public Boolean getAdicionarAlterarEndereco() {
        return adicionarAlterarEndereco;
    }

    public void setAdicionarAlterarEndereco(Boolean adicionarAlterarEndereco) {
        this.adicionarAlterarEndereco = adicionarAlterarEndereco;
    }
}

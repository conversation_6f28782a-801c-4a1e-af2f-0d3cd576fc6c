package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: GlaucoT
 * Date: 03/12/13
 * Time: 16:07
 * To change this template use File | Settings | File Templates.
 */
public class ClienteObservacaoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private String observacao = "";
    private Date dataCadastro = new Date();
    @ChaveEstrangeira
    private ClienteVO clienteVO = new ClienteVO();
    @ChaveEstrangeira
    private UsuarioVO usuarioVO = new UsuarioVO();
    @NaoControlarLogAlteracao
    private boolean alterarObservacao = false;
    private boolean importante = false;
    private Date dataAlteracao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getObservacao() {
        return observacao;
    }

    public String getObservacaoTratada() {
        try {
            String obs = "";
            String[] split = observacao.split(" ");
            for(String p : split){
                if(p.length() > 50){
                    obs += p.substring(0, 45) + "... ";
                    break;
                }else{
                    obs += p + " ";
                }
            }
            return obs.trim();
        } catch (Exception e) {
            return observacao;
        }
        
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public UsuarioVO getUsuarioVO() {
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }
    
    public void setAlterarObservacao(boolean alterarObservacao) {
        this.alterarObservacao = alterarObservacao;
    }

    public boolean isAlterarObservacao() {
        return alterarObservacao;
    }
    public String getObservacaoComLimite(){
        if (getObservacao().isEmpty()) {
            return "";
        }else{
            Integer tamanhoString = getObservacao().length();
            String observacaoNovo = "";
            if (tamanhoString > 60) {
               observacaoNovo += getObservacao().substring(0,60) + "... ";
            }else{
                observacaoNovo = getObservacao();
            }
            return observacaoNovo;
        }
    }

    public boolean isImportante() {
        return importante;
    }

    public void setImportante(boolean importante) {
        this.importante = importante;
    }

    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }
}

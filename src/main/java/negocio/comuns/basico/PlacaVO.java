/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;

public class PlacaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @NaoControlarLogAlteracao
    private PessoaVO pessoaVO;
    private String placa;
    private String descricao;


    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public String getPlaca() {
        if (placa == null) {
            placa = "";
        }
        return placa.toUpperCase();
    }

    public void setPlaca(String placa) {
        this.placa = placa;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao.toUpperCase();
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
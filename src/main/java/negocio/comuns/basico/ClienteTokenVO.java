package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class ClienteTokenVO extends SuperVO {

    private Integer codigo;
    private ClienteVO clienteVO;
    private Date validoAte;
    private Date utilizadoEm;
    private String codigoValidacao;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Date getValidoAte() {
        return validoAte;
    }

    public void setValidoAte(Date validoAte) {
        this.validoAte = validoAte;
    }

    public Date getUtilizadoEm() {
        return utilizadoEm;
    }

    public void setUtilizadoEm(Date utilizadoEm) {
        this.utilizadoEm = utilizadoEm;
    }

    public String getCodigoValidacao() {
        return codigoValidacao;
    }

    public void setCodigoValidacao(String codigoValidacao) {
        this.codigoValidacao = codigoValidacao;
    }
}

package negocio.comuns.basico;


import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.arquitetura.SuperTO;

import java.util.ArrayList;
import java.util.List;

/*
 * Created by <PERSON> on 11/11/2016.
 */
public class PerguntaUcpTO extends SuperTO {

    private Integer codigo;
    private String titulo;
    private String descricao;
    private List<TagUcpTO> tags;
    private List<ArtefatoUcpTO> artefatos;
    private Integer posicao;
    private Integer qtdVisualizada;
    private boolean apresentarZWConhecimentoFundamental = false;
    private boolean apresentarTRConhecimentoFundamental = false;
    private Integer ordemApresentar;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getLinkPergunta() {
        try {
            String request = (String) JSFUtilities.getManagedBean("SuperControle.contextPath");
            return request + "/redir?up&codPerguntaUCP=" + getCodigo();
        } catch (Exception ex) {
            return "";
        }
    }

    public List<TagUcpTO> getTags() {
        if (tags == null) {
            tags = new ArrayList<TagUcpTO>();
        }
        return tags;
    }

    public void setTags(List<TagUcpTO> tags) {
        this.tags = tags;
    }

    public List<ArtefatoUcpTO> getArtefatos() {
        if (artefatos == null) {
            artefatos = new ArrayList<ArtefatoUcpTO>();
        }
        return artefatos;
    }

    public void setArtefatos(List<ArtefatoUcpTO> artefatos) {
        this.artefatos = artefatos;
    }

    public Integer getPosicao() {
        if (posicao == null) {
            posicao = 0;
        }
        return posicao;
    }

    public void setPosicao(Integer posicao) {
        this.posicao = posicao;
    }

    public Integer getQtdVisualizada() {
        if (qtdVisualizada == null) {
            qtdVisualizada = 0;
        }
        return qtdVisualizada;
    }

    public void setQtdVisualizada(Integer qtdVisualizada) {
        this.qtdVisualizada = qtdVisualizada;
    }

    public String getLinkPerguntaSolicitacao() {
        try {
            String request = (String) JSFUtilities.getManagedBean("SuperControle.contextPath");
            return request + "/redir?up&solicitacao=true&codPerguntaUCP=" + getCodigo();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getDescricaoApresentar() {
        if (getDescricao().length() <= 190) {
            return getDescricao();
        } else {
            String retorno = getDescricao().substring(0, 190);
            return retorno + " ...";
        }
    }

    public Integer getOrdemApresentar() {
        if (ordemApresentar == null) {
            ordemApresentar = 0;
        }
        return ordemApresentar;
    }

    public void setOrdemApresentar(Integer ordemApresentar) {
        this.ordemApresentar = ordemApresentar;
    }

    public boolean isApresentarZWConhecimentoFundamental() {
        return apresentarZWConhecimentoFundamental;
    }

    public void setApresentarZWConhecimentoFundamental(boolean apresentarZWConhecimentoFundamental) {
        this.apresentarZWConhecimentoFundamental = apresentarZWConhecimentoFundamental;
    }

    public boolean isApresentarTRConhecimentoFundamental() {
        return apresentarTRConhecimentoFundamental;
    }

    public void setApresentarTRConhecimentoFundamental(boolean apresentarTRConhecimentoFundamental) {
        this.apresentarTRConhecimentoFundamental = apresentarTRConhecimentoFundamental;
    }
}

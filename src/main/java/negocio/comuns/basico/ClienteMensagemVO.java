package negocio.comuns.basico;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import java.util.Map;
import negocio.comuns.arquitetura.RiscoVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

/**
 * Reponsável por manter os dados da entidade ClienteMensagem. Classe do tipo VO
 * - Value Object composta pelos atributos da entidade com visibilidade
 * protegida e os métodos de acesso a estes atributos. Classe utilizada para
 * apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class ClienteMensagemVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String mensagem;
    @NaoControlarLogAlteracao
    protected ClienteVO cliente;
    protected TiposMensagensEnum tipomensagem;
    protected UsuarioVO usuario;
    @NaoControlarLogAlteracao
    protected QuestionarioClienteVO questionarioCliente;
    @NaoControlarLogAlteracao
    protected MovParcelaVO movParcela;
    @NaoControlarLogAlteracao
    protected ProdutoVO produto;
    @NaoControlarLogAlteracao
    protected Date dataDesbloqueio;
    @NaoControlarLogAlteracao
    protected Boolean apresentarEdicaoMensagem;
    protected Boolean bloqueio;
    protected Date dataBloqueio;
    @NaoControlarLogAlteracao
    private RiscoVO risco;
    @NaoControlarLogAlteracao
    private String mensagemTratada;
    private boolean desabilitado = false;
    private boolean bloqueioCheque = false;

    private Date dataRegistro;
    private Date dataAtualizacao;
    private Integer codigoOperacaoColetiva;

    /**
     * Construtor padrão da classe
     * <code>ClienteMensagem</code>. Cria uma nova instância desta entidade,
     * inicializando automaticamente seus atributos (Classe VO).
     */
    public ClienteMensagemVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ClienteMensagemVO</code>. Todos os tipos de consistência de dados
     * são e devem ser implementadas neste método. São validações típicas:
     * verificação de campos obrigatórios, verificação de valores válidos para
     * os atributos.
     *
     * @exception ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDados(ClienteMensagemVO obj) throws ConsistirException, ParseException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getCliente() == null
                || obj.getCliente().getCodigo().equals(0)) {
            throw new ConsistirException("O CLIENTE (ClienteMensagem) deve ser informado.");
        }
        if (obj.getMensagem().trim().isEmpty()) {
            throw new ConsistirException("O Campo MENSAGEM (ClienteMensagem) deve ser preenchido.");
        }
        if (obj.getTipomensagem() == null || obj.getTipomensagem().equals(TiposMensagensEnum.NENHUM)) {
            throw new ConsistirException("O TIPO DE MENSAGEM (ClienteMensagem) deve ser informado.");
        }
        if (obj.getDataBloqueio() != null) {
            if (obj.getTipomensagem().equals(TiposMensagensEnum.CATRACA) && Uteis.getCompareData(obj.getDataBloqueio(), Calendario.hoje()) < 0) {
                throw new ConsistirException("O Campo DATA (ClienteMensagem) não pode ser retroativo a data atual.");
            }
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
        setMensagem(getMensagem().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setMensagem("");
        setCliente(new ClienteVO());
        setTipomensagem(TiposMensagensEnum.NENHUM);
        setApresentarEdicaoMensagem(true);
        setUsuario(new UsuarioVO());
        setBloqueio(false);
        setQuestionarioCliente(new QuestionarioClienteVO());
        setMovParcela(new MovParcelaVO());
        setRisco(new RiscoVO());
        setProduto(new ProdutoVO());
        setCodigoOperacaoColetiva(0);
    }

    public TiposMensagensEnum getTipomensagem() {
        if (tipomensagem == null) {
            setTipomensagem(TiposMensagensEnum.NENHUM);
        }
        return tipomensagem;
    }

    public void setTipomensagem(TiposMensagensEnum tipomensagem) {
        this.tipomensagem = tipomensagem;
    }

    public String getMensagemTratadaMin() {
        return getTipomensagem().equals(TiposMensagensEnum.AVISO_CONSULTOR) ? 
                TiposMensagensEnum.AVISO_CONSULTOR.getMensagem() :
                getMensagemTratada().length() <= 150 ?
                getMensagemTratada() :  (getMensagemTratada().toLowerCase().contains("<html>") || getMensagemTratada().toLowerCase().contains("</html>") || getMensagemTratada().toLowerCase().contains("<body>") || getMensagemTratada().toLowerCase().contains("<strong>")) ?  getTipomensagem().getMensagem() :  getMensagemTratada().substring(0, 150)+"...";
    }
    public String getMensagemTratada() {
        if (mensagemTratada == null) {
            mensagemTratada = getMensagem().replaceAll("\\<br\\>", "").replaceAll("\\<br\\/\\>", "");
            Map<TiposMensagensEnum, Map<String, String>> mapaPalavrasChave = TiposMensagensEnum.getMapaPalavrasChave();
            Map<String, String> mapaPalavras = mapaPalavrasChave.get(tipomensagem);
            if (mapaPalavras == null || mapaPalavras.isEmpty()) {
                return mensagemTratada;
            }
            for (String k : mapaPalavras.keySet()) {
                mensagemTratada = mensagemTratada.replace(k, mapaPalavras.get(k));
            }
        }
        return mensagemTratada;
    }

    public String getMensagem() {
        if (mensagem == null) {
            mensagem = "";
        }
        return (mensagem);
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getApresentarEdicaoMensagem() {
        return apresentarEdicaoMensagem;
    }

    public void setApresentarEdicaoMensagem(Boolean apresentarEdicaoMensagem) {
        this.apresentarEdicaoMensagem = apresentarEdicaoMensagem;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public UsuarioVO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public Boolean getBloqueio() {
        return bloqueio;
    }

    public void setBloqueio(Boolean bloqueio) {
        this.bloqueio = bloqueio;
    }

    public Date getDataBloqueio() {
        return dataBloqueio;
    }

    public void setDataBloqueio(Date dataBloqueio) {
        this.dataBloqueio = dataBloqueio;
    }

    public MovParcelaVO getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(MovParcelaVO movParcela) {
        this.movParcela = movParcela;
    }

    public QuestionarioClienteVO getQuestionarioCliente() {
        return questionarioCliente;
    }

    public void setQuestionarioCliente(QuestionarioClienteVO questionarioCliente) {
        this.questionarioCliente = questionarioCliente;
    }

    public RiscoVO getRisco() {
        return risco;
    }

    public void setRisco(RiscoVO risco) {
        this.risco = risco;
    }

    public ProdutoVO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public Date getDataDesbloqueio() {
        return dataDesbloqueio;
    }

    public void setDataDesbloqueio(Date dataDesbloqueio) {
        this.dataDesbloqueio = dataDesbloqueio;
    }

    public boolean isNavegacaoPopUp() {
        return tipomensagem.equals(TiposMensagensEnum.BOLETIM)
                || tipomensagem.equals(TiposMensagensEnum.DADOS_INCOMPLETOS) || tipomensagem.equals(TiposMensagensEnum.RISCO)
                || tipomensagem.equals(TiposMensagensEnum.PRODUTO_VENCIDO) || tipomensagem.equals(TiposMensagensEnum.CARTAO_VENCIDO);
    }

    public boolean isNavegacaoFrame() {
        return tipomensagem.equals(TiposMensagensEnum.PARCELA_ATRASO);
    }

    public ClienteMensagemWS toWS() {
        ClienteMensagemWS clienteMensagemWS = new ClienteMensagemWS();
        clienteMensagemWS.setCodigo(this.getCodigo());
        clienteMensagemWS.setMensagem(this.getMensagem());
        clienteMensagemWS.setTipoMensagem(this.getTipomensagem());
        return clienteMensagemWS;
    }

    public void setMensagemTratada(String mensagemTratada) {
        this.mensagemTratada = mensagemTratada;
    }

    public boolean isDesabilitado() {
        return desabilitado;
    }

    public void setDesabilitado(boolean desabilitado) {
        this.desabilitado = desabilitado;
    }

    public boolean isBloqueioCheque() {
        return bloqueioCheque;
    }

    public void setBloqueioCheque(boolean bloqueioCheque) {
        this.bloqueioCheque = bloqueioCheque;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }

    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }

    public Integer getCodigoOperacaoColetiva() {
        if (this.codigoOperacaoColetiva == null) {
            this.codigoOperacaoColetiva = 0;
        }
        return codigoOperacaoColetiva;
    }

    public void setCodigoOperacaoColetiva(Integer codigoOperacaoColetiva) {
        this.codigoOperacaoColetiva = codigoOperacaoColetiva;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico;

import org.json.JSONObject;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import controle.arquitetura.servlet.ImpressaoServlet;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.MovParcelaTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.cielo.DCCCieloStatusEnum;
import servicos.propriedades.PropsService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@XStreamAlias("InfoCliente")
public class InfoClienteTO extends SuperTO {

    private String cpf;
    private String sexo;
    private String email;
    private String telCelular;
    private String telResidencial;
    private String endereco;
    private String endComplemento;
    private String endBairro;
    private String endCep;
    private String cidade;
    private String estado;
    private String estadoUF;
    private Date dataMatricula;
    private Double valorMensalidade;
    private Double valorMatricula;
    private String nomePlano;
    private String modalidades;
    private Integer diasAcessoUltimoMes;
    private Integer diasAcessoSemanaPassada;
    private Integer diasAcessoSemana2;
    private Integer diasAcessoSemana3;
    private Integer diasAcessoSemana4;
    private Integer diasDoUltimoAcesso;
    private String modeloContrato;
    private Date inicioContrato;
    private Date finalContrato;
    private Date dataUltimoAcesso;
    private String convenio;
    private Integer contrato;
    private String nomeEmpresa;
    private Double valorParcelaVencida;
    private String descricaoParcelaVencida;
    private Date dataParcelaVencida;
    private Double valorProximaParcela;
    private String descricaoProximaParcela;
    private Date dataProximaParcela;
    private List<GerenteTO> gerentes;

    public InfoClienteTO() {
    }

    public String getModeloContrato() {
        if (modeloContrato == null) {
            modeloContrato = "";
        }
        return modeloContrato;
    }

    public void setModeloContrato(String modeloContrato) {
        this.modeloContrato = modeloContrato;
    }

    public String getNomePlano() {
        if (nomePlano == null) {
            nomePlano = "";
        }
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public Integer getDiasAcessoUltimoMes() {
        if (diasAcessoUltimoMes == null) {
            diasAcessoUltimoMes = 0;
        }
        return diasAcessoUltimoMes;
    }

    public void setDiasAcessoUltimoMes(Integer diasAcessoUltimoMes) {
        this.diasAcessoUltimoMes = diasAcessoUltimoMes;
    }

    public String getModalidades() {
        if (modalidades == null) {
            modalidades = "";
        }
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public Integer getDiasAcessoSemanaPassada() {
        if (diasAcessoSemanaPassada == null) {
            diasAcessoSemanaPassada = 0;
        }
        return diasAcessoSemanaPassada;
    }

    public void setDiasAcessoSemanaPassada(Integer diasAcessoSemanaPassada) {
        this.diasAcessoSemanaPassada = diasAcessoSemanaPassada;
    }

    public Date getInicioContrato() {
        return inicioContrato;
    }

    public void setInicioContrato(Date inicioContrato) {
        this.inicioContrato = inicioContrato;
    }

    public Date getFinalContrato() {
        return finalContrato;
    }

    public void setFinalContrato(Date finalContrato) {
        this.finalContrato = finalContrato;
    }

    public String getConvenio() {
        if (convenio == null) {
            convenio = "";
        }
        return convenio;
    }

    public void setConvenio(String convenio) {
        this.convenio = convenio;
    }

    public Integer getContrato() {
        if (contrato == null) {
            contrato = 0;
        }
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Date getDataUltimoAcesso() {
        return dataUltimoAcesso;
    }

    public void setDataUltimoAcesso(Date dataUltimoAcesso) {
        this.dataUltimoAcesso = dataUltimoAcesso;
    }

    public Integer getDiasAcessoSemana2() {
        if (diasAcessoSemana2 == null) {
            diasAcessoSemana2 = 0;
        }
        return diasAcessoSemana2;
    }

    public void setDiasAcessoSemana2(Integer diasAcessoSemana2) {
        this.diasAcessoSemana2 = diasAcessoSemana2;
    }

    public Integer getDiasAcessoSemana3() {
        if (diasAcessoSemana3 == null) {
            diasAcessoSemana3 = 0;
        }
        return diasAcessoSemana3;
    }

    public void setDiasAcessoSemana3(Integer diasAcessoSemana3) {
        this.diasAcessoSemana3 = diasAcessoSemana3;
    }

    public Integer getDiasAcessoSemana4() {
        if (diasAcessoSemana4 == null) {
            diasAcessoSemana4 = 0;
        }
        return diasAcessoSemana4;
    }

    public void setDiasAcessoSemana4(Integer diasAcessoSemana4) {
        this.diasAcessoSemana4 = diasAcessoSemana4;
    }

    public Integer getDiasDoUltimoAcesso() {
        if (diasDoUltimoAcesso == null) {
            diasDoUltimoAcesso = 0;
        }
        return diasDoUltimoAcesso;
    }

    public void setDiasDoUltimoAcesso(Integer diasDoUltimoAcesso) {
        this.diasDoUltimoAcesso = diasDoUltimoAcesso;
    }

    public String obterURLModeloContrato(String chave) {
        try {
            if (UteisValidacao.emptyNumber(getContrato())) {
                return "";
            }
            JSONObject json = new JSONObject();
            json.put("chave", chave);
            json.put("contrato", getContrato());
            return (Uteis.getUrlAPI() + "/prest/util/" + chave+ "/impressao/" + Uteis.encriptar(json.toString(), Uteis.getChaveCriptoImpressaoServlet()));
        } catch (Exception ex) {
            return "";
        }
    }

    public Double getValorParcelaVencida() {
        if (valorParcelaVencida == null) {
            valorParcelaVencida = 0.0;
        }
        return valorParcelaVencida;
    }

    public void setValorParcelaVencida(Double valorParcelaVencida) {
        this.valorParcelaVencida = valorParcelaVencida;
    }

    public String getDescricaoParcelaVencida() {
        if (descricaoParcelaVencida == null) {
            descricaoParcelaVencida = "";
        }
        return descricaoParcelaVencida;
    }

    public void setDescricaoParcelaVencida(String descricaoParcelaVencida) {
        this.descricaoParcelaVencida = descricaoParcelaVencida;
    }

    public Date getDataParcelaVencida() {
        return dataParcelaVencida;
    }

    public void setDataParcelaVencida(Date dataParcelaVencida) {
        this.dataParcelaVencida = dataParcelaVencida;
    }

    public Double getValorProximaParcela() {
        if (valorProximaParcela == null) {
            valorProximaParcela = 0.0;
        }
        return valorProximaParcela;
    }

    public void setValorProximaParcela(Double valorProximaParcela) {
        this.valorProximaParcela = valorProximaParcela;
    }

    public String getDescricaoProximaParcela() {
        if (descricaoProximaParcela == null) {
            descricaoProximaParcela = "";
        }
        return descricaoProximaParcela;
    }

    public void setDescricaoProximaParcela(String descricaoProximaParcela) {
        this.descricaoProximaParcela = descricaoProximaParcela;
    }

    public Date getDataProximaParcela() {
        return dataProximaParcela;
    }

    public void setDataProximaParcela(Date dataProximaParcela) {
        this.dataProximaParcela = dataProximaParcela;
    }

    public String getNomeEmpresa() {
        if (nomeEmpresa == null) {
            nomeEmpresa = "";
        }
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getCpf() {
        if (cpf == null) {
            cpf = "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getSexo() {
        if (sexo == null) {
            sexo = "";
        }
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelCelular() {
        if (telCelular == null) {
            telCelular = "";
        }
        return telCelular;
    }

    public void setTelCelular(String telCelular) {
        this.telCelular = telCelular;
    }

    public String getTelResidencial() {
        if (telResidencial == null) {
            telResidencial = "";
        }
        return telResidencial;
    }

    public void setTelResidencial(String telResidencial) {
        this.telResidencial = telResidencial;
    }

    public String getEndereco() {
        if (endereco == null) {
            endereco = "";
        }
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getEndComplemento() {
        if (endComplemento == null) {
            endComplemento = "";
        }
        return endComplemento;
    }

    public void setEndComplemento(String endComplemento) {
        this.endComplemento = endComplemento;
    }

    public String getEndBairro() {
        if (endBairro == null) {
            endBairro = "";
        }
        return endBairro;
    }

    public void setEndBairro(String endBairro) {
        this.endBairro = endBairro;
    }

    public String getEndCep() {
        if (endCep == null) {
            endCep = "";
        }
        return endCep;
    }

    public void setEndCep(String endCep) {
        this.endCep = endCep;
    }

    public String getCidade() {
        if (cidade == null) {
            cidade = "";
        }
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getEstado() {
        if (estado == null) {
            estado = "";
        }
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getEstadoUF() {
        if (estadoUF == null) {
            estadoUF = "";
        }
        return estadoUF;
    }

    public void setEstadoUF(String estadoUF) {
        this.estadoUF = estadoUF;
    }

    public Date getDataMatricula() {
        return dataMatricula;
    }

    public void setDataMatricula(Date dataMatricula) {
        this.dataMatricula = dataMatricula;
    }

    public Double getValorMensalidade() {
        if (valorMensalidade == null) {
            valorMensalidade = 0.0;
        }
        return valorMensalidade;
    }

    public void setValorMensalidade(Double valorMensalidade) {
        this.valorMensalidade = valorMensalidade;
    }

    public Double getValorMatricula() {
        if (valorMatricula == null) {
            valorMatricula = 0.0;
        }
        return valorMatricula;
    }

    public void setValorMatricula(Double valorMatricula) {
        this.valorMatricula = valorMatricula;
    }

    public List<GerenteTO> getGerentes() {
        if (gerentes == null) {
            gerentes = new ArrayList<GerenteTO>();
        }
        return gerentes;
    }

    public void setGerentes(List<GerenteTO> gerentes) {
        this.gerentes = gerentes;
    }
}

package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class CreditoPactoHistoricoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Date dataRegistro;
    private Date dataAtualizacao;
    private Date dataReferencia;
    private EmpresaVO empresaVO;
    private TipoCobrancaPactoEnum tipoCobrancaPacto;
    private Integer qtdTotal;
    private Double valorTotal;
    private Double valorMedioUnitario;

    public CreditoPactoHistoricoVO() {
        this.dataRegistro = Calendario.hoje();
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }

    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }

    public Date getDataReferencia() {
        return dataReferencia;
    }

    public void setDataReferencia(Date dataReferencia) {
        this.dataReferencia = dataReferencia;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public TipoCobrancaPactoEnum getTipoCobrancaPacto() {
        return tipoCobrancaPacto;
    }

    public void setTipoCobrancaPacto(TipoCobrancaPactoEnum tipoCobrancaPacto) {
        this.tipoCobrancaPacto = tipoCobrancaPacto;
    }

    public Integer getQtdTotal() {
        if (qtdTotal == null) {
            qtdTotal = 0;
        }
        return qtdTotal;
    }

    public void setQtdTotal(Integer qtdTotal) {
        this.qtdTotal = qtdTotal;
    }

    public Double getValorTotal() {
        if (valorTotal == null) {
            valorTotal = 0.0;
        }
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Double getValorMedioUnitario() {
        if (valorMedioUnitario == null) {
            valorMedioUnitario = 0.0;
        }
        return valorMedioUnitario;
    }

    public void setValorMedioUnitario(Double valorMedioUnitario) {
        this.valorMedioUnitario = valorMedioUnitario;
    }
}

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;


public class ConfirmacaoEmailCompraVO extends SuperVO{

    private ClienteVO cliente;
    private Date dataRegistro;
    private String urlRtorno;
    private String token;
    private String email;
    private Integer plano;
    private Integer nrparcelasadesao;
    private Integer nrparcelasproduto;
    private String numeroCupomDesconto;
    private Integer nrParcelasPagamento;
    private Integer diaVencimento;

    public ClienteVO getCliente() {
        if(cliente == null ){
            cliente = new ClienteVO();
        }
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getUrlRtorno() {
        return urlRtorno;
    }

    public void setUrlRtorno(String urlRtorno) {
        this.urlRtorno = urlRtorno;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getNrparcelasadesao() {
        return nrparcelasadesao;
    }

    public void setNrparcelasadesao(Integer nrparcelasadesao) {
        this.nrparcelasadesao = nrparcelasadesao;
    }

    public Integer getNrParcelasPagamento() {
        return nrParcelasPagamento;
    }

    public void setNrParcelasPagamento(Integer nrParcelasPagamento) {
        this.nrParcelasPagamento = nrParcelasPagamento;
    }

    public Integer getDiaVencimento() {
        return diaVencimento;
    }

    public void setDiaVencimento(Integer diaVencimento) {
        this.diaVencimento = diaVencimento;
    }

    public String getNumeroCupomDesconto() {
        return numeroCupomDesconto;
    }

    public void setNumeroCupomDesconto(String numeroCupomDesconto) {
        this.numeroCupomDesconto = numeroCupomDesconto;
    }

    public Integer getNrparcelasproduto() {
        return nrparcelasproduto;
    }

    public void setNrparcelasproduto(Integer nrparcelasproduto) {
        this.nrparcelasproduto = nrparcelasproduto;
    }
}

package negocio.comuns.basico;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;

/**
 * Reponsável por manter os dados da entidade ConfiguracaoSistemaCadastroCliente. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class ConfiguracaoSistemaCadastroClienteVO extends SuperVO {

    protected String nome;
    protected boolean obrigatorio = false;
    protected boolean mostrar = false;
    protected boolean pendente = false;
    private boolean visitante = true;
//    private ConfiguracaoSistemaVO configuracaoSistema;

    /**
     * Construtor padrão da classe <code>ConfiguracaoSistemaCadastroCliente</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ConfiguracaoSistemaCadastroClienteVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setNome("");
//        setConfiguracaoSistema(new ConfiguracaoSistemaVO());
    }



    
    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome.trim();
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean getMostrar() {
        return mostrar;
    }

    public void setMostrar(boolean mostrar) {
        this.mostrar = mostrar;
    }

    public boolean getObrigatorio() {
        return obrigatorio;
    }

    public void setObrigatorio(boolean obrigatorio) {
        this.obrigatorio = obrigatorio;
    }

    public boolean getPendente() {
        return pendente;
    }

    public void setPendente(boolean pendente) {
        this.pendente = pendente;
    }

    public boolean isVisitante() {
        return visitante;
    }

    public void setVisitante(boolean visitante) {
        this.visitante = visitante;
    }
    
}

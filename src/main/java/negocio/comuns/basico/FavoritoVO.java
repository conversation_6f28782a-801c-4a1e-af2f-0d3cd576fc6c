package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

public class FavoritoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;

    protected String nome;
    protected Integer usuario;
    protected Integer tipo;
    protected String identificador;

    public FavoritoVO() {
    }

    public FavoritoVO(Integer usuario, Integer tipo, String identificador) {
        this.usuario = usuario;
        this.tipo = tipo;
        this.identificador = identificador;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }
}

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

public class GympassDiaVO extends SuperVO {
    private int codigoGympass;
    private int sequencia;
    private Double valorDia;

    public GympassDiaVO() {
        super();
        inicializarDados();
    }

    @Override
    public boolean equals(Object obj) {
        return (obj instanceof GympassDiaVO) && ((GympassDiaVO) obj).getSequencia() == this.sequencia;
    }

    public static void validarDados(GympassDiaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getValorDia() < 0.0) {
            throw new ConsistirException("O Valor do "+(obj.getSequencia()+1)+"º dia não pode ser negativo.");
        }
    }

    public void inicializarDados() {
        setCodigoGympass(0);
        setSequencia(0);
        setValorDia(0.0);
    }

    public int getCodigoGympass() {
        return codigoGympass;
    }

    public void setCodigoGympass(int codigoGympass) {
        this.codigoGympass = codigoGympass;
    }

    public int getSequencia() {
        return sequencia;
    }

    public void setSequencia(int sequencia) {
        this.sequencia = sequencia;
    }

    public Double getValorDia() {
        return valorDia;
    }

    public void setValorDia(Double valorDia) {
        this.valorDia = valorDia;
    }
}

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GympassVO extends SuperVO {
    private Double valorMaximo;
    private Double valorMinimo;
    private Date dataInicial;
    private Date dataFinal;
    private List<GympassDiaVO> dias;
    public static final String GYMPASS_CONTATO = " Por favor entre em contato com a GYMPASS";
    public static final String GYMPASS_OPERACOES_VALIDACAO = "VALIDACÃO";
    public static final String GYMPASS_OPERACOES_SALVAR = "SALVAR";
    public static final String GYMPASS_OPERACOES_EXCLUIR = "EXCLUSÃO";
    public static final String AUTORIZAR_TOKEN = "AUTORIZAR_TOKEN";


    public GympassVO() {
        super();
        setValorMaximo(0.0);
        setValorMinimo(0.0);
        setDataInicial(new Date());
        setDias(new ArrayList<GympassDiaVO>());
    }

    public static void validarDados(GympassVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getValorMaximo().compareTo(0.0) <= 0) {
            throw new ConsistirException("O valor máximo deve ser informado.");
        }
        validarPeriodo(obj);
        for (GympassDiaVO dia : obj.getDias()) {
            GympassDiaVO.validarDados(dia);
        }
    }

    public static void validarPeriodo(GympassVO obj) throws ConsistirException {
        if (obj.getDataInicial() == null) {
            throw new ConsistirException("A data inicial deve ser informada.");
        }
        if (obj.getDataFinal() == null) {
            throw new ConsistirException("A data final deve ser informada.");
        }
    }

    public Double getValorMaximo() {
        return valorMaximo;
    }

    public void setValorMaximo(Double valorMaximo) {
        this.valorMaximo = valorMaximo;
    }

    public Double getValorMinimo() {
        return valorMinimo;
    }

    public void setValorMinimo(Double valorMinimo) {
        this.valorMinimo = valorMinimo;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public List<GympassDiaVO> getDias() {
        return dias;
    }

    public void setDias(List<GympassDiaVO> dias) {
        this.dias = dias;
    }

    public static GympassVO buscaGympassAtivo(List<GympassVO> listaGympass, Date dataReferencia) {
        for (GympassVO gympassVO : listaGympass) {
            if (dataReferencia.compareTo(gympassVO.getDataInicial()) >= 0 &&
                dataReferencia.compareTo(gympassVO.getDataFinal()) <= 0) {
                return gympassVO;
            }
        } return null;
    }
}

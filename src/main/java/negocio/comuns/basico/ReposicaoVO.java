/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoAntecedenciaMarcarAulaEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.TimeZone;

/**
 *
 * <AUTHOR>
 */
public class ReposicaoVO extends SuperVO {

    private Date dataOrigem = Calendario.hoje();
    private Date dataReposicao = null;
    private Date dataPresenca;
    @ChaveEstrangeira
    private HorarioTurmaVO horarioTurmaOrigem;
    @ChaveEstrangeira
    private HorarioTurmaVO horarioTurma;
    @ChaveEstrangeira
    private TurmaVO turmaOrigem;
    @ChaveEstrangeira
    private TurmaVO turmaDestino;
    @ChaveEstrangeira
    private ClienteVO cliente;
    @ChaveEstrangeira
    private ContratoVO contrato;
    private Date dataLancamento = Calendario.hoje();
    @ChaveEstrangeira
    private UsuarioVO usuario;
    @NaoControlarLogAlteracao
    private int tipo = -1;//0 -> desmarcou aula 1 -> marcou reposição
    private OrigemSistemaEnum origemSistemaEnum = OrigemSistemaEnum.ZW;

    @NaoControlarLogAlteracao
    private boolean marcacaoAula = false;

    @NaoControlarLogAlteracao
    private boolean aulaExperimental = false;

    @NaoControlarLogAlteracao
    private Integer produtoFreePass;

    @NaoControlarLogAlteracao
    private ConviteAulaExperimentalVO conviteAulaExperimentalVO;

    @NaoControlarLogAlteracao
    private Date dataHoraAula; // atributo transient
    
    @NaoControlarLogAlteracao
    private boolean permitirLancarAulaRetroativa = false;

    private boolean horarioAtivo = false;

    private Integer spiviSeatID;
    private Integer spiviEventID;
    private Integer spiviClientID;

    public Integer getSpiviEventID() {
        return spiviEventID;
    }

    public void setSpiviEventID(final Integer spiviEventID) {
        this.spiviEventID = spiviEventID;
    }

    public Integer getSpiviClientID() {
        return spiviClientID;
    }

    public void setSpiviClientID(final Integer spiviClientID) {
        this.spiviClientID = spiviClientID;
    }

    public Integer getSpiviSeatID() {
        return spiviSeatID;
    }

    public void setSpiviSeatID(Integer spiviSeatID) {
        this.spiviSeatID = spiviSeatID;
    }

    public Date getDataPresenca() {
        return dataPresenca;
    }

    public void setDataPresenca(Date dataPresenca) {
        this.dataPresenca = dataPresenca;
    }

    public String getDataPresenca_Apresentar() {
        return Uteis.getData(this.dataReposicao, "dd/MM/yyyy");
    }

    public Date getDataReposicao() {
        return dataReposicao;
    }

    public void setDataReposicao(Date dataReposicao) {
        this.dataReposicao = dataReposicao;
    }

    public String getDataReposicao_Apresentar() {
        return Uteis.getData(this.dataReposicao, "dd/MM/yyyy");
    }

    public HorarioTurmaVO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaVO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public TurmaVO getTurmaDestino() {
        return turmaDestino;
    }

    public void setTurmaDestino(TurmaVO turmaDestino) {
        this.turmaDestino = turmaDestino;
    }

    public TurmaVO getTurmaOrigem() {
        return turmaOrigem;
    }

    public void setTurmaOrigem(TurmaVO turmaOrigem) {
        this.turmaOrigem = turmaOrigem;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public void validarDados() throws ConsistirException {
        validarDados(false, false);
    }

    public void validarDados(boolean diaria, boolean teveAlteracaoContrato) throws ConsistirException {
        if (dataReposicao == null) {
            throw new ConsistirException("Data de Reposição deve ser informada.");
        }
        if (horarioTurma == null || horarioTurma.getCodigo() == 0) {
            throw new ConsistirException("Horário Turma para a Reposição deve ser informado.");
        }
        if (turmaOrigem == null || turmaOrigem.getCodigo() == 0) {
            throw new ConsistirException("Turma de Origem deve ser informada.");
        }
        if (turmaDestino == null || turmaDestino.getCodigo() == 0) {
            throw new ConsistirException("Turma de Destino deve ser informada.");
        }
        if (cliente == null || cliente.getCodigo() == 0) {
            throw new ConsistirException("Cliente deve ser informado.");
        }
        if (!diaria && (contrato == null || contrato.getCodigo() == 0)) {
            throw new ConsistirException("Contrato deve ser informado.");
        }
        if (horarioTurmaOrigem == null || horarioTurmaOrigem.getCodigo() == 0) {
            throw new ConsistirException("Horário Turma de origem da Reposição deve ser informado.");
        }

        if (!diaria) {
            if (!(Calendario.maiorOuIgual(dataReposicao, contrato.getVigenciaDe()) &&
                    Calendario.menorOuIgual(dataReposicao, contrato.getVigenciaAteAjustada()))) {
                throw new ConsistirException(String.format("A Data de Reposição deve "
                                + "estar dentro da Vigência do Contrato: de %s à %s.",
                        contrato.getVigenciaDe_Apresentar(),
                        contrato.getVigenciaAteAjustada_Apresentar()));
            }


            if (!getCliente().getEmpresa().isAdicionarAulasDesmarcadasContratoAnterior() && horarioTurmaOrigem.getAulaDesmarcadaVO().getContratoAnterior().getCodigo() == 0 && !teveAlteracaoContrato) {
                if (!(Calendario.maiorOuIgual(dataOrigem, contrato.getVigenciaDe()) &&
                        Calendario.menorOuIgual(dataOrigem, contrato.getVigenciaAteAjustada()))
                        && !isMarcacaoAula()) {
                    throw new ConsistirException(String.format("A Data da aula Desmarcada deve "
                                    + "estar dentro da Vigência do Contrato: de %s à %s.",
                            contrato.getVigenciaDe_Apresentar(),
                            contrato.getVigenciaAteAjustada_Apresentar()));
                }
            }
        }

        if ((this.getOrigemSistemaEnum().equals(OrigemSistemaEnum.APP_TREINO) || this.getTurmaDestino().isBloquearReposicaoAcimaLimite()) &&  horarioTurma.getNrAlunoMatriculado() >= horarioTurma.getNrMaximoAluno()) {
            throw new ConsistirException("Número de vagas excedido! Consulte as turmas novamente e verifique as vagas disponíveis.");
        }
        
        if (!isPermitirLancarAulaRetroativa()) {
            if (Calendario.menor(dataReposicao, Calendario.hoje())) {
                throw new ConsistirException("A reposição não pode ser retroativa.");
            }
        }
    }

    /**
     * @return the dataLancamento
     */
    public Date getDataLancamento() {
        return dataLancamento;
    }

    /**
     * @param dataLancamento the dataLancamento to set
     */
    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getDataLancamento_Apresentar() {
        return Uteis.getDataComHHMM(this.dataLancamento);
    }

    public HorarioTurmaVO getHorarioTurmaOrigem() {
        return horarioTurmaOrigem;
    }

    public void setHorarioTurmaOrigem(HorarioTurmaVO horarioTurmaOrigem) {
        this.horarioTurmaOrigem = horarioTurmaOrigem;
    }

    public Date getDataOrigem() {
        return dataOrigem;
    }

    public String getDataOrigem_Apresentar() {
        return Uteis.getData(this.dataOrigem, "dd/MM/yyyy");
    }

    public void setDataOrigem(Date dataOrigem) {
        this.dataOrigem = dataOrigem;
    }

    public UsuarioVO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public int getTipo() {
        return tipo;
    }

    public void setTipo(int tipo) {
        this.tipo = tipo;
    }

    public String getDescricaoOrigem() {
        if(this.isMarcacaoAula()){
            return "Marcação de Aula Extra";
        }
        return String.format("%s - %s - %s às %s - %s - com: %s", getDataOrigem_Apresentar(),
                horarioTurmaOrigem.getDiaSemana_Apresentar().toUpperCase(),
                horarioTurmaOrigem.getHoraInicial(),
                horarioTurmaOrigem.getHoraFinal(),
                horarioTurmaOrigem.getIdentificadorTurma(),
                horarioTurmaOrigem.getProfessor().getPessoa().getNome());
    }
    public String getDescricaoOrigemCurta() {
        if(this.isMarcacaoAula()){
            return "Marcação de Aula Extra ";
        }
        return String.format("%s - %s - %s - %s às %s ",
                this.turmaOrigem.getModalidade().getNome(),
                getDataOrigem_Apresentar(),
                horarioTurmaOrigem.getDiaSemana_Apresentar().toUpperCase(),
                horarioTurmaOrigem.getHoraInicial(),
                horarioTurmaOrigem.getHoraFinal());
    }

    public String getDescricaoDestino() {
        if (horarioTurma == null) {
            return "";
        }
        return String.format("%s - %s - %s às %s - %s - com: %s",
                getDataReposicao_Apresentar(),
                horarioTurma.getDiaSemana_Apresentar().toUpperCase(),
                horarioTurma.getHoraInicial(),
                horarioTurma.getHoraFinal(),
                horarioTurma.getIdentificadorTurma(),
                horarioTurma.getProfessor().getPessoa().getNome());
    }

    public String getDescricaoDestinoCurta() {
        return String.format("%s  - %s - %s - %s às %s",
                turmaDestino.getModalidade().getNome(),
                getDataReposicao_Apresentar(),
                horarioTurma.getDiaSemana_Apresentar().toUpperCase(),
                horarioTurma.getHoraInicial(),
                horarioTurma.getHoraFinal());
    }


    public StringBuilder getResultadoReposicaoHTML(boolean exclusao) {
        StringBuilder texto = new StringBuilder();
        texto.append("<html><body style=\"font-size:11px;font-family:Arial,Verdana,sans-serif;color:#000;\">");
        //
        texto.append("Olá, ").append(this.getCliente().getPessoa().getNome()).append("!<br/>");
        texto.append("      Você está recebendo este e-mail de aviso automático, como comprovante de ");
        texto.append(exclusao ? "<b>CANCELAMENTO</b> de " : "");
        texto.append("sua Aula de Reposição.");
        texto.append("<br/><br/>");
        texto.append("Seguem os dados da sua Reposição:<br/><br/>");
        texto.append("<b>Lançamento: </b>").append(this.getDataLancamento_Apresentar()).append("<br/>");
        texto.append("<b>Aula Desmarcada: </b>").append(this.getDescricaoOrigem()).append("<br/>");
        texto.append("<b>Aula Reposição: </b>").append(this.getDescricaoDestino()).append("<br/>");
        texto.append("<b>Atendente: </b>").append(this.getUsuario().getUsername().toUpperCase()).append("<br/>");
        //
        texto.append("</body>");
        texto.append("</html>");

        return texto;
    }

    public StringBuilder getResultadoReposicaoTEXTO(boolean exclusao) {
        StringBuilder texto = new StringBuilder();
        texto.append("Olá! Seguem os dados de ");
        texto.append(exclusao ? " CANCELAMENTO de " : "");
        texto.append("sua Aula de Reposição:\n");
        texto.append("Lançamento: ").append(this.getDataLancamento_Apresentar()).append("\n");
        texto.append("Aula Desmarcada: ").append(this.getDescricaoOrigem()).append("\n");
        texto.append("Aula Reposição: ").append(this.getDescricaoDestino()).append("\n");
        texto.append("Atendente: ").append(this.usuario.getUsername().toUpperCase());
        return texto;
    }

    public void definirTipo(final HorarioTurmaVO horario) {
        if (horario.getCodigo().intValue() == this.getHorarioTurma().getCodigo().intValue()) {
            tipo = 1;
        } else if (horario.getCodigo().intValue() == this.getHorarioTurmaOrigem().getCodigo().intValue()) {
            tipo = 0;
        }
    }

    public String getTipoComoString() {
        if (tipo == 1) {
            return "Reposição (+1)";
        } else if (tipo == 0) {
            return "Desmarcou (-1)";
        }
        return "";
    }

    public String getData_Apresentar() {
        if (tipo == 1) {
            return getDataReposicao_Apresentar();
        } else if (tipo == 0) {
            return getDataOrigem_Apresentar();
        }
        return "";
    }

    public OrigemSistemaEnum getOrigemSistemaEnum() {
        return origemSistemaEnum;
    }

    public void setOrigemSistemaEnum(OrigemSistemaEnum origemSistemaEnum) {
        this.origemSistemaEnum = origemSistemaEnum;
    }

    public boolean isMarcacaoAula() {
        return marcacaoAula;
    }

    public void setMarcacaoAula(boolean marcacaoAula) {
        this.marcacaoAula = marcacaoAula;
    }

    public ConviteAulaExperimentalVO getConviteAulaExperimentalVO() {
        return conviteAulaExperimentalVO;
    }

    public void setConviteAulaExperimentalVO(ConviteAulaExperimentalVO conviteAulaExperimentalVO) {
        this.conviteAulaExperimentalVO = conviteAulaExperimentalVO;
    }

    private static Date retornarDataHoraAula(boolean horaInicio, HorarioTurmaVO horarioTurmaVO, Date dataAula){
        Calendar dataHoraAula = Calendario.getInstance();
        dataHoraAula.setTime(Calendario.getDataComHoraZerada(dataAula));
        String[] horaMinuto = null;
        if (horaInicio){
            horaMinuto = horarioTurmaVO.getHoraInicial().split(":");
        }else{
            horaMinuto = horarioTurmaVO.getHoraFinal().split(":");
        }
        dataHoraAula.set(Calendar.HOUR, Integer.parseInt(horaMinuto[0]));
        dataHoraAula.set(Calendar.MINUTE, Integer.parseInt(horaMinuto[1]));
        return dataHoraAula.getTime();
    }

    public static  void validarMinutosAntecedenciaMarcarAula(TurmaVO turmaVO, HorarioTurmaVO horarioTurmaVO, Date dataAula, Date agora)throws Exception{
        Calendar dataComparar = Calendario.getInstance();
        Date dataHoraAulaInicio = retornarDataHoraAula(true,horarioTurmaVO, dataAula);
        Date dataHoraAulaFim = retornarDataHoraAula(false,horarioTurmaVO, dataAula);
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        dataComparar.setTime(dataHoraAulaInicio);

        if (!UteisValidacao.emptyNumber(turmaVO.getMinutosAposInicioApp()) && Calendario.maiorComHora(agora, dataHoraAulaInicio)) {

            dataComparar.add(Calendar.MINUTE, turmaVO.getMinutosAposInicioApp());
            if (Calendario.maiorComHora(agora, dataComparar.getTime())){
                throw new ConsistirException("Operação não permitida. Não é permitido marcar aula com mais de " + turmaVO.getMinutosAposInicioApp() + " minutos após o início.");
            }

        } else {
            //retirar os minutos de antecedencia para desmarcar aula
            dataComparar.add(Calendar.MINUTE, (turmaVO.getMinutosAntecedenciaMarcarAula() * -1));
            if ((turmaVO.getTipoAntecedenciaMarcarAulaEnum() == TipoAntecedenciaMarcarAulaEnum.TEMPO_MINIMO_ANTECEDENCIA) && (turmaVO.getMinutosAntecedenciaMarcarAula() > 0)) {
                if (!(Calendario.menorOuIgualComHora(agora, dataComparar.getTime()))) {
                    //throw new ConsistirException("Operação não permitida. Para marcar a aula na turma " +turmaVO.getDescricao() + " é necessário uma antecedência mínima de (" + turmaVO.getMinutosAntecedenciaDesmarcarAula() + ") minutos antes do ínicio da aula.");
                    throw new ConsistirException("Operação não permitida. Para marcar a aula na turma " + turmaVO.getDescricao() + " a data da solicitação deve ser menor ou igual a  " + sdf.format(dataComparar.getTime()) + "");
                }
            }
        }

        if(turmaVO.getTipoAntecedenciaMarcarAulaEnum() == TipoAntecedenciaMarcarAulaEnum.TEMPO_MAXIMO_ANTECEDENCIA && !Uteis.datasMesmoDiaMesAno(dataAula, Calendario.hoje())) {
            sdf = new SimpleDateFormat("dd/MM/yyyy");
            throw new ConsistirException("Operação não permitida. Período permitido para marcar aula na turma " + turmaVO.getDescricao() + " é somente no dia " + sdf.format(dataAula));
        }

        if (!(Calendario.menorOuIgualComHora(agora,dataHoraAulaFim))){
            throw new ConsistirException("Operação não permitida. A aula da turma " +turmaVO.getDescricao() + " terminou no horário " + sdf.format(dataHoraAulaFim));
        }
    }



    public Date getDataHoraAula() {
        return dataHoraAula;
    }

    public void setDataHoraAula(Date dataHoraAula) {
        this.dataHoraAula = dataHoraAula;
    }

    public static Comparator COMPARATOR_DATA_HORA_AULA= new Comparator() {
        public int compare(Object o1, Object o2) {
            ReposicaoVO p1 = (ReposicaoVO) o1;
            ReposicaoVO p2 = (ReposicaoVO) o2;
            return p1.getDataHoraAula().compareTo(p2.getDataHoraAula());
        }
    };

    public boolean isAulaExperimental() {
        return aulaExperimental;
    }

    public void setAulaExperimental(boolean aulaExperimental) {
        this.aulaExperimental = aulaExperimental;
    }

    public Integer getProdutoFreePass() {
        return produtoFreePass;
    }

    public void setProdutoFreePass(Integer produtoFreePass) {
        this.produtoFreePass = produtoFreePass;
    }

    public boolean isPermitirLancarAulaRetroativa() {
        return permitirLancarAulaRetroativa;
    }

    public void setPermitirLancarAulaRetroativa(boolean permitirLancarAulaRetroativa) {
        this.permitirLancarAulaRetroativa = permitirLancarAulaRetroativa;
    }

    public boolean isHorarioAtivo() {
        return horarioAtivo;
    }

    public void setHorarioAtivo(boolean horarioAtivo) {
        this.horarioAtivo = horarioAtivo;
    }
}

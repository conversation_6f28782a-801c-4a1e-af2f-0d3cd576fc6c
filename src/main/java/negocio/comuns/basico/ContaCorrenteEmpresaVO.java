package negocio.comuns.basico;

import annotations.arquitetura.FKJson;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.ContaCorrenteVO;
import negocio.comuns.utilitarias.ConsistirException;

public class ContaCorrenteEmpresaVO extends SuperVO {

    protected Integer codigo;
    protected Integer empresa;
    @FKJson
    protected ContaCorrenteVO contaCorrente;


    public ContaCorrenteEmpresaVO() {
        super();
        inicializarDados();
    }

    public static void validarDados(ContaCorrenteEmpresaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getContaCorrente() == null) || (obj.getContaCorrente().getCodigo() == 0)) {
            throw new ConsistirException("O campo CONTA CORRENTE (Configurações da empresa) deve ser informado.");
        }
    }

    public void realizarUpperCaseDados() {
    }

    public void inicializarDados() {
        setCodigo(0);
        setContaCorrente(new ContaCorrenteVO());
    }

    public ContaCorrenteVO getContaCorrente() {
        if (contaCorrente == null) {
            contaCorrente = new ContaCorrenteVO();
        }
        return contaCorrente;
    }

    public void setContaCorrente(ContaCorrenteVO obj) {
        this.contaCorrente = obj;
    }

    public Integer getEmpresa() {
        return (empresa);
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricaoContaCorrente() {
        return getContaCorrente().getDescricaoApresentar();
    }

    public Integer getCodigoBanco() {
        return getContaCorrente().getBanco().getCodigo();
    }
}
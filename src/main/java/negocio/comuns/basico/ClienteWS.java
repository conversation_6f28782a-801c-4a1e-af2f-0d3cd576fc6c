
package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by glauco on 02/12/2014
 */
public class ClienteWS extends SuperTO {

    private int codigo;
    private int codigoPessoa;
    private String nome;
    private String matricula;
    private String CPF;
    private String email;
    private String sexo;
    private String telResidencial;
    private String telCelular;
    private Integer empresa;
    private String dataNascimento;
    private String endereco;
    private String dataCadastro;
    private String situacao;
    private String consultor;
    private String nomeEmpresa;
    private String complemento;
    private String numero;
    private String bairro;
    private String cep;
    private Boolean permiteContratosConcomitante;
    private String nomeUsuarioMovel;
    private Integer codigoCidade;
    private Integer codigoEstado;
    private Integer idEmpresaFinanceiroRede;
    private String chaveZW;
    private List<EmailWS> listaEmails;
    private List<TelefoneWS> listaTelefones;
    private List<EnderecoWS> listaEnderecos;
    private List<AutorizacaoCobrancaClienteWS> listaAutorizacaoCobranca;
    private Integer pontuacao;


    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(int codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getCPF() {
        return CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getTelResidencial() {
        return telResidencial;
    }

    public void setTelResidencial(String telResidencial) {
        this.telResidencial = telResidencial;
    }

    public String getTelCelular() {
        return telCelular;
    }

    public void setTelCelular(String telCelular) {
        this.telCelular = telCelular;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(String dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getConsultor() {
        return consultor;
    }

    public void setConsultor(String consultor) {
        this.consultor = consultor;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public Boolean getPermiteContratosConcomitante() {
        return permiteContratosConcomitante;
    }

    public void setPermiteContratosConcomitante(Boolean permiteContratosConcomitante) {
        this.permiteContratosConcomitante = permiteContratosConcomitante;
    }

    public String getNomeUsuarioMovel() {
        return nomeUsuarioMovel;
    }

    public void setNomeUsuarioMovel(String nomeUsuarioMovel) {
        this.nomeUsuarioMovel = nomeUsuarioMovel;
    }

    public Integer getCodigoCidade() {
        return codigoCidade;
    }

    public void setCodigoCidade(Integer codigoCidade) {
        this.codigoCidade = codigoCidade;
    }

    public Integer getCodigoEstado() {
        return codigoEstado;
    }

    public void setCodigoEstado(Integer codigoEstado) {
        this.codigoEstado = codigoEstado;
    }

    public Integer getIdEmpresaFinanceiroRede() {
        return idEmpresaFinanceiroRede;
    }

    public void setIdEmpresaFinanceiroRede(Integer idEmpresaFinanceiroRede) {
        this.idEmpresaFinanceiroRede = idEmpresaFinanceiroRede;
    }

    public String getChaveZW() {
        return chaveZW;
    }

    public void setChaveZW(String chaveZW) {
        this.chaveZW = chaveZW;
    }

    public List<EmailWS> getListaEmails() {
        if (listaEmails == null) {
            listaEmails = new ArrayList<EmailWS>();
        }
        return listaEmails;
    }

    public void setListaEmails(List<EmailWS> listaEmails) {
        this.listaEmails = listaEmails;
    }

    public List<TelefoneWS> getListaTelefones() {
        if (listaTelefones == null) {
            listaTelefones = new ArrayList<TelefoneWS>();
        }
        return listaTelefones;
    }

    public void setListaTelefones(List<TelefoneWS> listaTelefones) {
        this.listaTelefones = listaTelefones;
    }

    public List<EnderecoWS> getListaEnderecos() {
        if (listaEnderecos == null) {
            listaEnderecos = new ArrayList<EnderecoWS>();
        }
        return listaEnderecos;
    }

    public void setListaEnderecos(List<EnderecoWS> listaEnderecos) {
        this.listaEnderecos = listaEnderecos;
    }

    public List<AutorizacaoCobrancaClienteWS> getListaAutorizacaoCobranca() {
        if (listaAutorizacaoCobranca == null) {
            listaAutorizacaoCobranca = new ArrayList<AutorizacaoCobrancaClienteWS>();
        }
        return listaAutorizacaoCobranca;
    }

    public void setListaAutorizacaoCobranca(List<AutorizacaoCobrancaClienteWS> listaAutorizacaoCobranca) {
        this.listaAutorizacaoCobranca = listaAutorizacaoCobranca;
    }

    public Integer getPontuacao() {
        return pontuacao;
    }

    public void setPontuacao(Integer pontuacao) {
        this.pontuacao = pontuacao;
    }
}

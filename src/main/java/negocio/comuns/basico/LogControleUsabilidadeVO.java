package negocio.comuns.basico;

import java.util.Date;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade LogControleUsabilidade. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class LogControleUsabilidadeVO extends SuperVO {

    protected Integer codigo;
    protected Integer chavePrimaria;
    protected String entidade;
    protected String maquina;
    protected String acao;
    protected Date dataRegistro;
    protected UsuarioVO usuario;
    protected EmpresaVO empresa;
    protected String userOamd;

    /**
     * Construtor padrão da classe <code>LogControleUsabilidade</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public LogControleUsabilidadeVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>LogControleUsabilidadeVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(LogControleUsabilidadeVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }

        if (obj.getEntidade().equals("")) {
            throw new ConsistirException("O campo ENTIDADE (Log Controle Usabilidade) deve ser informado.");
        }
//        if (obj.getMaquina().equals("")) {
//            throw new ConsistirException("O campo MÁQUINA (Log Controle Usabilidade) deve ser informado.");
//        }
        if (obj.getUsuario() == null) {
            throw new ConsistirException("O campo USUARIO (Log Controle Usabilidade) deve ser informado.");
        }
        if (obj.getEmpresa() == null) {
            throw new ConsistirException("O campo EMPRESA (Log Controle Usabilidade) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
        setEntidade(getEntidade().toUpperCase());
        setMaquina(getMaquina().toUpperCase());
        setAcao(getAcao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setEntidade("");
        setMaquina("");
        setAcao("");
        setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        setUsuario(new UsuarioVO());
        setChavePrimaria(new Integer(0));
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public UsuarioVO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public Date getDataRegistro() {
        if (dataRegistro == null) {
            dataRegistro = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return (dataRegistro);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
     */
    public String getDataRegistro_Apresentar() {
        return (Uteis.getDataComHHMM(dataRegistro));
    }


    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getAcao() {
        if (acao == null) {
            acao = "";
        }
        return (acao);
    }

    public void setAcao(String acao) {
        this.acao = acao;
    }

    public String getMaquina() {
        if (maquina == null) {
            maquina = "";
        }
        return (maquina);
    }

    public void setMaquina(String maquina) {
        this.maquina = maquina;
    }

    public String getEntidade() {
        if (entidade == null) {
            entidade = "";
        }
        return (entidade);
    }

    public void setEntidade(String entidade) {
        this.entidade = entidade;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(Integer chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public String getUserOamd() {
        if (userOamd == null) {
            userOamd = "";
        }
        return userOamd;
    }

    public void setUserOamd(String useroamd) {
        this.userOamd = useroamd;
    }
    
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoTipoEnum;
import java.util.ArrayList;
import java.util.List;
import javax.faces.model.SelectItem;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class ConfiguracaoBIVO extends SuperVO {

    private BIEnum bi;
    private ConfiguracaoBIEnum configuracao;
    private Integer empresa;
    private Integer usuarioResponsavel;
    private String valor;

    public String getNome() {
        return configuracao == null ? "" : configuracao.getNome();
    }

    public BIEnum getBi() {
        return bi;
    }

    public void setBi(BIEnum bi) {
        this.bi = bi;
    }

    public ConfiguracaoBIEnum getConfiguracao() {
        return configuracao;
    }

    public void setConfiguracao(ConfiguracaoBIEnum configuracao) {
        this.configuracao = configuracao;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(Integer usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public List<SelectItem> getListaItens(){
        return (configuracao != null 
                && configuracao.getItens() != null
                && !configuracao.getItens().isEmpty()) ? configuracao.getItens() : new ArrayList<SelectItem>();
    }
    
    public Boolean getValorAsBoolean(){
        try {
            return Boolean.valueOf(valor);
        } catch (Exception e) {
            return null;
        }
    }
    public Double getValorAsDouble(){
        try {
            return Double.valueOf(valor);
        } catch (Exception e) {
            return 0.0;
        }
    }
    public Integer getValorAsInteger(){
        try {
            return Integer.valueOf(valor);
        } catch (Exception e) {
            return 0;
        }
    }
    
    public void setValorAsBoolean(Boolean valor){
        setValor(valor.toString());
    }
    public void setValorAsDouble(Double valor){
        setValor(valor.toString());
    }
    public void setValorAsInteger(Integer valor){
        setValor(valor.toString());
    }

    
    public boolean getTpString() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.STRING);
        } catch (Exception e) {
            return false;
        }

    }

    public boolean getTpInteger() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.INTEGER);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean getTpDouble() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.DOUBLE);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean getTpBoolean() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.BOOLEAN);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean getTpSenha() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.SENHA);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean getTpCombo() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.COMBO);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean getTpMinutos() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.MINUTOS);
        } catch (Exception e) {
            return false;
        }
    }
}

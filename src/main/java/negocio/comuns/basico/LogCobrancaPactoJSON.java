package negocio.comuns.basico;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.comuns.util.Formatador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 19/11/2021
 */
public class LogCobrancaPactoJSON extends SuperJSON {

    private Integer codigo;
    private String dataCobranca;
    private Integer quantidade;
    private Double valorTotal;
    private String usuario;
    private String justificativa;
    private String observacao;
    private boolean existeItens = false;

    public LogCobrancaPactoJSON() {
    }

    public LogCobrancaPactoJSON(LogCobrancaPactoVO obj) {
        this.codigo = obj.getCodigo();
        this.dataCobranca = obj.getDataCobranca_Apresentar();
        this.quantidade = obj.getQuantidade();
        this.valorTotal = obj.getValorTotal();
        this.usuario = obj.getNomeUsuarioOAMD();
        this.justificativa = obj.getJustificativa();
        this.observacao = obj.getObservacao();
        this.existeItens = obj.getListaItens().size() > 0;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDataCobranca() {
        return dataCobranca;
    }

    public void setDataCobranca(String dataCobranca) {
        this.dataCobranca = dataCobranca;
    }

    public Integer getQuantidade() {
        if (quantidade == null) {
            quantidade = 0;
        }
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Double getValorTotal() {
        if (valorTotal == null) {
            valorTotal = 0.0;
        }
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getJustificativa() {
        if (justificativa == null) {
            justificativa = "";
        }
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public String getObservacao_Apresentar() {
        return getObservacao().replace(" \n", "<br/>");
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getValorTotal_Apresentar() {
        return Formatador.formatarValorMonetario(getValorTotal());
    }

    public String getUsuario() {
        if (usuario == null) {
            usuario = "";
        }
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public boolean isExisteItens() {
        return existeItens;
    }

    public void setExisteItens(boolean existeItens) {
        this.existeItens = existeItens;
    }
}

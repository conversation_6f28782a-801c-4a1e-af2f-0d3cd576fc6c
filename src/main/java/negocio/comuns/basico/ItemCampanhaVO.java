package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.agendatotal.json.TurmaAulaCheiaJSON;
import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;

import java.util.*;

public class ItemCampanhaVO extends SuperVO implements Cloneable{

    @ChavePrimaria
    private Integer codigo;
    @NaoControlarLogAlteracao
    @ChaveEstrangeira
    private CampanhaDuracaoVO campanha;
    private Integer chaveestrangeira;
    private TipoItemCampanhaEnum tipoItemCampanha;
    private Integer duracao;
    private Integer pontos;
    @NaoControlarLogAlteracao
    private ProdutoVO produtoReferencia;
    @NaoControlarLogAlteracao
    private PlanoVO planoReferencia;
    @NaoControlarLogAlteracao
    private PlanoDuracaoVO planoDuracaoReferencia;
    @NaoControlarLogAlteracao
    private TurmaAulaCheiaJSON aulaReferencia;
    @NaoControlarLogAlteracao
    private EmpresaVO acessoReferencia;
    @NaoControlarLogAlteracao
    private boolean selecionarItem;
    private EmpresaVO empresa;
    @NaoControlarLogAlteracao
    private Boolean itemEstrangeiroExcluido = false;
    @NaoControlarLogAlteracao
    private Boolean habilitarSelecaoDiasAtivos;
    private List<String> diasDaSemanaAtivos = new ArrayList<>();

    public ItemCampanhaVO(Integer chaveestrangeira, TipoItemCampanhaEnum tipoItemCampanha, Integer pontos, Integer empresa) {
        this.chaveestrangeira = chaveestrangeira;
        this.tipoItemCampanha = tipoItemCampanha;
        this.pontos = pontos;
        this.empresa = new EmpresaVO();
        this.empresa.setCodigo(empresa);
    }

    public ItemCampanhaVO() {
    }

    public CampanhaDuracaoVO getCampanha() {
        return campanha;
    }

    public void setCampanha(CampanhaDuracaoVO campanha) {
        this.campanha = campanha;
    }

    public Integer getChaveestrangeira() {
        if (chaveestrangeira == null) {
            chaveestrangeira = 0;
        }
        return chaveestrangeira;
    }

    public void setChaveestrangeira(Integer chaveestrangeira) {
        this.chaveestrangeira = chaveestrangeira;
    }

    public TipoItemCampanhaEnum getTipoItemCampanha() {
        return tipoItemCampanha;
    }

    public void setTipoItemCampanha(TipoItemCampanhaEnum tipoItem) {
        this.tipoItemCampanha = tipoItem;
    }

    public Integer getDuracao() {
        if (duracao == null) {
            duracao = 0;
        }
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getPontos() {
        if (pontos == null) {
            pontos = 0;
        }
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public ProdutoVO getProdutoReferencia() {
        return produtoReferencia;
    }

    public void setProdutoReferencia(ProdutoVO produtoReferencia) {
        this.produtoReferencia = produtoReferencia;
    }

    public PlanoVO getPlanoReferencia() {
        return planoReferencia;
    }

    public void setPlanoReferencia(PlanoVO planoReferencia) {
        this.planoReferencia = planoReferencia;
    }

    public boolean isSelecionarItem() {
        return selecionarItem;
    }

    public void setSelecionarItem(boolean selecionarItem) {
        this.selecionarItem = selecionarItem;
    }
    
    public String getNomeItem(){
        if (tipoItemCampanha == TipoItemCampanhaEnum.PRODUTO) {
            return produtoReferencia.getDescricao();
        }else if(tipoItemCampanha == TipoItemCampanhaEnum.PLANO){
            return planoReferencia.getDescricao();
        }else if(tipoItemCampanha == TipoItemCampanhaEnum.AULA){
            return aulaReferencia.getNome();
        }else if(tipoItemCampanha == TipoItemCampanhaEnum.ACESSO){
            return acessoReferencia.getNome();
        }else if(tipoItemCampanha == TipoItemCampanhaEnum.ACESSOCHUVA){
            return acessoReferencia.getNome();
        }else if(tipoItemCampanha == TipoItemCampanhaEnum.ACESSOFRIO){
            return acessoReferencia.getNome();
        }else if(tipoItemCampanha == TipoItemCampanhaEnum.ACESSOCALOR){
            return acessoReferencia.getNome();
        }else if(tipoItemCampanha == TipoItemCampanhaEnum.PLANODURACAO){
            return planoDuracaoReferencia.getDescricaoDuracao();
        }else{
            return "";
        }
    }
    
    public String getTipoItemNome(){
        return tipoItemCampanha.getDescricao();
    }

    public TurmaAulaCheiaJSON getAulaReferencia() {
        return aulaReferencia;
    }

    public void setAulaReferencia(TurmaAulaCheiaJSON aulaReferencia) {
        this.aulaReferencia = aulaReferencia;
    }

    public PlanoDuracaoVO getPlanoDuracaoReferencia() {
        return planoDuracaoReferencia;
    }

    public void setPlanoDuracaoReferencia(PlanoDuracaoVO planoDuracaoReferencia) {
        this.planoDuracaoReferencia = planoDuracaoReferencia;
    }

    public EmpresaVO getAcessoReferencia() {
        return acessoReferencia;
    }

    public void setAcessoReferencia(EmpresaVO acessoReferencia) {
        this.acessoReferencia = acessoReferencia;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Boolean getItemEstrangeiroExcluido() {
        if (itemEstrangeiroExcluido == null) {
            itemEstrangeiroExcluido = true;
        }
        return itemEstrangeiroExcluido;
    }

    public void setItemEstrangeiroExcluido(Boolean itemEstrangeiroExcluido) {
        this.itemEstrangeiroExcluido = itemEstrangeiroExcluido;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ItemCampanhaVO that = (ItemCampanhaVO) o;
        return Objects.equals(getChaveestrangeira(), that.getChaveestrangeira()) &&
                getTipoItemCampanha() == that.getTipoItemCampanha() &&
                getEmpresa().getCodigo()==that.getEmpresa().getCodigo();
    }

    @Override
    public int hashCode() {
        return Objects.hash(getChaveestrangeira(), getTipoItemCampanha(), getEmpresa());
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List<String> getDiasDaSemanaAtivos() {
        return diasDaSemanaAtivos;
    }

    public void setDiasDaSemanaAtivos(List<String> diasDaSemanaAtivos) {
        this.diasDaSemanaAtivos = diasDaSemanaAtivos;
    }

    public Boolean getHabilitarSelecaoDiasAtivos() {
        return habilitarSelecaoDiasAtivos;
    }

    public void setHabilitarSelecaoDiasAtivos(Boolean habilitarSelecaoDiasAtivos) {
        this.habilitarSelecaoDiasAtivos = habilitarSelecaoDiasAtivos;
    }

    @Override
    public String toString() {
        return "ItemCampanhaVO{" +
                (chaveestrangeira!=null || chaveestrangeira >0 ? "chaveestrangeira=" + chaveestrangeira +",":"" )+
                " tipoItemCampanha=" + tipoItemCampanha.getDescricao() +
                ", pontos=" + pontos +
                '}';
    }
}

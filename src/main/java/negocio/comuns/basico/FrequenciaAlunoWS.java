package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

/**
 * Created by ulisses on 17/06/2016.
 */
public class FrequenciaAlunoWS extends SuperTO {

    private int quantidadeAcessosMesAtual;
    private int quantidadeAcessosMes2;
    private int quantidadeAcessosMes3;
    private int quantidadeAcessosMes4;

    public int getQuantidadeAcessosMesAtual() {
        return quantidadeAcessosMesAtual;
    }

    public void setQuantidadeAcessosMesAtual(int quantidadeAcessosMesAtual) {
        this.quantidadeAcessosMesAtual = quantidadeAcessosMesAtual;
    }

    public int getQuantidadeAcessosMes2() {
        return quantidadeAcessosMes2;
    }

    public void setQuantidadeAcessosMes2(int quantidadeAcessosMes2) {
        this.quantidadeAcessosMes2 = quantidadeAcessosMes2;
    }

    public int getQuantidadeAcessosMes3() {
        return quantidadeAcessosMes3;
    }

    public void setQuantidadeAcessosMes3(int quantidadeAcessosMes3) {
        this.quantidadeAcessosMes3 = quantidadeAcessosMes3;
    }

    public int getQuantidadeAcessosMes4() {
        return quantidadeAcessosMes4;
    }

    public void setQuantidadeAcessosMes4(int quantidadeAcessosMes4) {
        this.quantidadeAcessosMes4 = quantidadeAcessosMes4;
    }
}

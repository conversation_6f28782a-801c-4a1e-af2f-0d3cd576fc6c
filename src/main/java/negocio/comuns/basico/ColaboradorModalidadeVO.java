package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.ConsistirException;

public class ColaboradorModalidadeVO extends SuperVO {

    private Integer codigo;
    private ColaboradorVO colaboradorVO;
    private ModalidadeVO modalidadeVO;

    public ColaboradorModalidadeVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setCodigo(0);
        setColaboradorVO(new ColaboradorVO());
        setModalidadeVO(new ModalidadeVO());
    }

    public static void validarDados(ColaboradorModalidadeVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getModalidadeVO() == null) || (obj.getModalidadeVO().getCodigo().equals(0))) {
            throw new ConsistirException("O campo MODALIDADADE DO COLABORADOR deve ser informado.");
        }
    }

    public Integer getCodigo() {
        if (codigo == null){
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ColaboradorVO getColaboradorVO() {
        if (colaboradorVO == null) {
            colaboradorVO = new ColaboradorVO();
        }
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public ModalidadeVO getModalidadeVO() {
        if (modalidadeVO == null){
            modalidadeVO = new ModalidadeVO();
        }
        return modalidadeVO;
    }

    public void setModalidadeVO(ModalidadeVO modalidadeVO) {
        this.modalidadeVO = modalidadeVO;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico;

import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.MovParcelaVO;

/**
 *
 * <AUTHOR>
 */
public class AgrupamentoParcelasTO extends SuperTO{

    private List<MovParcelaVO> parcelasContato = new ArrayList<MovParcelaVO>();
    private List<MovParcelaVO> parcelasTrocarCartao = new ArrayList<MovParcelaVO>();
    private List<MovParcelaVO> parcelasContatoCielo = new ArrayList<MovParcelaVO>();
    private List<MovParcelaVO> parcelasReenvio = new ArrayList<MovParcelaVO>();
    private List<MovParcelaVO> parcelasOutros = new ArrayList<MovParcelaVO>();
    private List<MovParcelaVO> parcelasSemAcao = new ArrayList<MovParcelaVO>();

    private Double valorContato;
    private Double valorTrocarCartao;
    private Double valorContatoCielo;
    private Double valorReenvio;
    private Double valorOutros;
    private Double valorSemAcao;

    private Double valorContatoCieloSelecionadas;
    private Double valorReenvioSelecionadas;

    private Integer qtdContatoCieloSelecionadas;
    private Integer qtdReenvioSelecionadas;

    private Integer cont;
    private Double valor;

    public void totalizar(){
        valorContato = totalizar(parcelasContato);
        valorTrocarCartao = totalizar(parcelasTrocarCartao);
        valorContatoCielo = totalizar(parcelasContatoCielo);
        valorReenvio = totalizar(parcelasReenvio);
        valorOutros = totalizar(parcelasOutros);
        valorSemAcao = totalizar(parcelasSemAcao);
    }
    public Double totalizar(List<MovParcelaVO> lista){
        Double total = 0.0;
        for(MovParcelaVO parcela : lista){
            total = total + parcela.getValorParcela();
        }
        return total;
    }
    public void totalizarSelecionadas(List<MovParcelaVO> lista){
        valor = 0.0;
        cont = 0;
        for(MovParcelaVO parcela : lista){
            if(parcela.getParcelaEscolhida()){
                valor = valor + parcela.getValorParcela();
                cont++;
            }
        }
    }

    public void totalizarSelecionadas(){
        totalizarSelecionadas(parcelasContatoCielo);
        valorContatoCieloSelecionadas = valor.doubleValue();
        qtdContatoCieloSelecionadas = cont.intValue();
        totalizarSelecionadas(parcelasReenvio);
        valorReenvioSelecionadas = valor.doubleValue();
        qtdReenvioSelecionadas = cont.intValue();
        
        
    }

    public String getValorReenvioSelecionadasFormatado() {
        return Formatador.formatarValorMonetario(getValorReenvioSelecionadas());
    }

    public String getValorContatoCieloSelecionadasFormatado() {
        return Formatador.formatarValorMonetario(getValorContatoCieloSelecionadas());
    }

    public List<MovParcelaVO> getParcelasContato() {
        return parcelasContato;
    }

    public void setParcelasContato(List<MovParcelaVO> parcelasContato) {
        this.parcelasContato = parcelasContato;
    }

    public List<MovParcelaVO> getParcelasContatoCielo() {
        return parcelasContatoCielo;
    }

    public void setParcelasContatoCielo(List<MovParcelaVO> parcelasContatoCielo) {
        this.parcelasContatoCielo = parcelasContatoCielo;
    }

    public List<MovParcelaVO> getParcelasOutros() {
        return parcelasOutros;
    }

    public void setParcelasOutros(List<MovParcelaVO> parcelasOutros) {
        this.parcelasOutros = parcelasOutros;
    }

    public List<MovParcelaVO> getParcelasReenvio() {
        return parcelasReenvio;
    }

    public void setParcelasReenvio(List<MovParcelaVO> parcelasReenvio) {
        this.parcelasReenvio = parcelasReenvio;
    }

    public List<MovParcelaVO> getParcelasSemAcao() {
        return parcelasSemAcao;
    }

    public void setParcelasSemAcao(List<MovParcelaVO> parcelasSemAcao) {
        this.parcelasSemAcao = parcelasSemAcao;
    }

    public List<MovParcelaVO> getParcelasTrocarCartao() {
        return parcelasTrocarCartao;
    }

    public void setParcelasTrocarCartao(List<MovParcelaVO> parcelasTrocarCartao) {
        this.parcelasTrocarCartao = parcelasTrocarCartao;
    }

    public Integer getTotalContato() {
        return parcelasContato.size();
    }

    public Integer getTotalContatoCielo() {
        return parcelasContatoCielo.size();
    }

    public Integer getTotalOutros() {
        return parcelasOutros.size();
    }

    public Integer getTotalReenvio() {
        return parcelasReenvio.size();
    }

    public Integer getTotalSemAcao() {
        return parcelasSemAcao.size();
    }


    public Integer getTotalTrocarCartao() {
        return parcelasTrocarCartao.size();
    }


    public Double getValorContato() {
        return valorContato;
    }

    public void setValorContato(Double valorContato) {
        this.valorContato = valorContato;
    }

    public Double getValorContatoCielo() {
        return valorContatoCielo;
    }

    public void setValorContatoCielo(Double valorContatoCielo) {
        this.valorContatoCielo = valorContatoCielo;
    }

    public Double getValorOutros() {
        return valorOutros;
    }

    public void setValorOutros(Double valorOutros) {
        this.valorOutros = valorOutros;
    }

    public Double getValorReenvio() {
        return valorReenvio;
    }

    public void setValorReenvio(Double valorReenvio) {
        this.valorReenvio = valorReenvio;
    }

    public Double getValorSemAcao() {
        return valorSemAcao;
    }

    public void setValorSemAcao(Double valorSemAcao) {
        this.valorSemAcao = valorSemAcao;
    }

    public Double getValorTrocarCartao() {
        return valorTrocarCartao;
    }

    public void setValorTrocarCartao(Double valorTrocarCartao) {
        this.valorTrocarCartao = valorTrocarCartao;
    }

    public Integer getQtdContatoCieloSelecionadas() {
        return qtdContatoCieloSelecionadas;
    }

    public void setQtdContatoCieloSelecionadas(Integer qtdContatoCieloSelecionadas) {
        this.qtdContatoCieloSelecionadas = qtdContatoCieloSelecionadas;
    }

    public Integer getQtdReenvioSelecionadas() {
        return qtdReenvioSelecionadas;
    }

    public void setQtdReenvioSelecionadas(Integer qtdReenvioSelecionadas) {
        this.qtdReenvioSelecionadas = qtdReenvioSelecionadas;
    }

    public Double getValorContatoCieloSelecionadas() {
        return valorContatoCieloSelecionadas;
    }

    public void setValorContatoCieloSelecionadas(Double valorContatoCieloSelecionadas) {
        this.valorContatoCieloSelecionadas = valorContatoCieloSelecionadas;
    }

    public Double getValorReenvioSelecionadas() {
        return valorReenvioSelecionadas;
    }

    public void setValorReenvioSelecionadas(Double valorReenvioSelecionadas) {
        this.valorReenvioSelecionadas = valorReenvioSelecionadas;
    }



}

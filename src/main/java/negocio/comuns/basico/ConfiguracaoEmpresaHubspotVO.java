package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.AcaoObjcaoLeadEnum;
import org.json.JSONObject;

public class ConfiguracaoEmpresaHubspotVO extends SuperVO {

    private String clientsecret;
    private boolean empresausahub;
    private Integer empresa;
    private String  horaexpiracao;
    private String url_instalacao;
    private  String url_redirect;
    private String clientId;
    private String appId;
    private String token;
    private String horaLimite;
    private UsuarioVO responsavelPadrao;
    private int acaoObjecao;


    public boolean isEmpresausahub() {
        return empresausahub;
    }

    public void setEmpresausahub(boolean empresausahub) {
        this.empresausahub = empresausahub;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getHoraexpiracao() {
        return horaexpiracao;
    }

    public void setHoraexpiracao(String horaexpiracao) {
        this.horaexpiracao = horaexpiracao;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getUrl_instalacao() {
        return url_instalacao;
    }

    public void setUrl_instalacao(String url_instalacao) {
        this.url_instalacao = url_instalacao;
    }

    public String getUrl_redirect() {
        return url_redirect;
    }

    public void setUrl_redirect(String url_redirect) {
        this.url_redirect = url_redirect;
    }

    public String getClientsecret() {
        return clientsecret;
    }

    public void setClientsecret(String clientsecret) {
        this.clientsecret = clientsecret;
    }

    public String getHoraLimite() {
        return horaLimite;
    }

    public void setHoraLimite(String horaLimite) {
        this.horaLimite = horaLimite;
    }

    public UsuarioVO getResponsavelPadrao() {
        if (responsavelPadrao == null) {
            responsavelPadrao = new UsuarioVO();
        }
        return responsavelPadrao;
    }

    public void setResponsavelPadrao(UsuarioVO responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }

    public int getAcaoObjecao() {
        return acaoObjecao;
    }

    public void setAcaoObjecao(int acaoObjecao) {
        this.acaoObjecao = acaoObjecao;
    }

    public boolean isAcaoObjecaoLeadAtaulizarQualquer() {
        return getAcaoObjecao() == AcaoObjcaoLeadEnum.ATUALIZAR_QUALQUER.getCodigo();
    }

    public boolean isAcaoObjecaoLeadApenasDefinitiva() {
        return getAcaoObjecao() == AcaoObjcaoLeadEnum.APENAS_DEFINTIVA.getCodigo();
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String toString() {
        JSONObject json = new JSONObject();
        json.put("clientsecret", this.clientsecret);
        json.put("empresausahub", this.empresausahub);
        json.put("empresa", this.empresa);
        json.put("horaexpiracao", this. horaexpiracao);
        json.put("url_instalacao", this.url_instalacao);
        json.put("url_redirect", this.url_redirect);
        json.put("clientId", this.clientId);
        json.put("appId", this.appId);
        json.put("token", this.token);
        json.put("horaLimite", this.horaLimite);
        json.put("responsavelPadrao", this.responsavelPadrao);
        json.put("acaoObjecao", this.acaoObjecao);
        return json.toString();
    }

    public String getTokenPreparado() {
        return (this.getToken().startsWith("Bearer") ? this.getToken() : ("Bearer " + this.getToken()));
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}

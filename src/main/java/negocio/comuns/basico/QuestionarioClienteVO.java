package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade QuestionarioCliente. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class QuestionarioClienteVO extends SuperVO {
    
    @ChavePrimaria
    protected Integer codigo;
    protected Date data;
    protected String observacao;
    /** Atributo responsável por manter os objetos da classe <code>QuestionarioPerguntaCliente</code>. */
    @NaoControlarLogAlteracao
    @ListJson(clazz = QuestionarioPerguntaClienteVO.class)
    private List questionarioPerguntaClienteVOs;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Questionario </code>.*/
    @ChaveEstrangeira
    @FKJson
    protected QuestionarioVO questionario;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Cliente </code>.*/
   @ChaveEstrangeira
   @FKJson
    protected ClienteVO cliente;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
   @ChaveEstrangeira 
   @FKJson
   protected ColaboradorVO consultor;
    //Atributo criado para uso de regra de negocio.
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
   @ChaveEstrangeira 
   @FKJson
   protected ColaboradorVO consultorAnterior;
   @ChaveEstrangeira
   @FKJson
   private EventoVO eventoVO;
    private TipoBVEnum tipoBV;
    @NaoControlarLogAlteracao
    private UsuarioVO responsavel;
    private Date ultimaAtualizacao;

    /**
     * Atributo criado para controlar a alteração do <code>AgendaVO.consultorReponsavel</code> caso seja necessário na alteração do
     * {@link QuestionarioClienteVO}
     */
    @NaoControlarLogAlteracao
    private Integer codigoColaboradorAntesAlteracao;// transient

    private OrigemSistemaEnum origemSistemaEnum = OrigemSistemaEnum.ZW;

    /**
     * Construtor padrão da classe <code>QuestionarioCliente</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public QuestionarioClienteVO() {
        super();
        inicializarDados();
    }

    public String getMatriculaApresentar() {
        return getCliente().getMatricula();
    }

    public String getNomeApresentar() {
        return getCliente().getPessoa().getNome();
    }

    public String getSituacaoApresentar() {
        return getCliente().getSituacao_Apresentar();
    }

    public String getBvApresentar() {
        return getQuestionario().getTituloPesquisa();
    }

    public String getTipoBvApresentar() {
        return getTipoBV().getDescricao();
    }

    public String getConsultorApresentar() {
        return getConsultor().getPessoa().getNome();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>QuestionarioClienteVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(QuestionarioClienteVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getQuestionario() == null)
                || (obj.getQuestionario().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo QUESTIONÁRIO (Questionário Cliente) deve ser informado.");
        }
        if ((obj.getCliente() == null)
                || (obj.getCliente().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo CLIENTE (Questionário Cliente) deve ser informado.");
        }
        if ((obj.getConsultor() == null)
                || (obj.getConsultor().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo CONSULTOR (Questionário Cliente) deve ser informado.");
        }
        if (obj.getData() == null) {
            throw new ConsistirException("O campo DATA (Questionário Cliente) deve ser informado.");
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>QuestionarioClienteVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDadosConsultor(QuestionarioClienteVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }

        if ((obj.getConsultor() == null)
                || (obj.getConsultor().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo CONSULTOR (Questionário Cliente) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setData(negocio.comuns.utilitarias.Calendario.hoje());
        setQuestionarioPerguntaClienteVOs(new ArrayList());
        setQuestionario(new QuestionarioVO());
        setCliente(new ClienteVO());
        setConsultor(new ColaboradorVO());
        setObservacao("");
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>QuestionarioPerguntaClienteVO</code>
     * ao List <code>questionarioPerguntaClienteVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>QuestionarioPerguntaCliente</code> - getPerguntaCliente().getCodigo() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>QuestionarioPerguntaClienteVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjQuestionarioPerguntaClienteVOs(QuestionarioPerguntaClienteVO obj) throws Exception {
        QuestionarioPerguntaClienteVO.validarDados(obj);
        int index = 0;
        Iterator i = getQuestionarioPerguntaClienteVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaClienteVO objExistente = (QuestionarioPerguntaClienteVO) i.next();
            if (objExistente.getPerguntaCliente().getCodigo().equals(obj.getPerguntaCliente().getCodigo())) {
                getQuestionarioPerguntaClienteVOs().set(index, obj);
                return;
            }
            index++;
        }
        getQuestionarioPerguntaClienteVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>QuestionarioPerguntaClienteVO</code>
     * no List <code>questionarioPerguntaClienteVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>QuestionarioPerguntaCliente</code> - getPerguntaCliente().getCodigo() - como identificador (key) do objeto no List.
     * @param perguntaCliente  Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjQuestionarioPerguntaClienteVOs(Integer perguntaCliente) throws Exception {
        int index = 0;
        Iterator i = getQuestionarioPerguntaClienteVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaClienteVO objExistente = (QuestionarioPerguntaClienteVO) i.next();
            if (objExistente.getPerguntaCliente().getCodigo().equals(perguntaCliente)) {
                getQuestionarioPerguntaClienteVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>QuestionarioPerguntaClienteVO</code>
     * no List <code>questionarioPerguntaClienteVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>QuestionarioPerguntaCliente</code> - getPerguntaCliente().getCodigo() - como identificador (key) do objeto no List.
     * @param perguntaCliente  Parâmetro para localizar o objeto do List.
     */
    public QuestionarioPerguntaClienteVO consultarObjQuestionarioPerguntaClienteVO(Integer perguntaCliente) throws Exception {
        Iterator i = getQuestionarioPerguntaClienteVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaClienteVO objExistente = (QuestionarioPerguntaClienteVO) i.next();
            if (objExistente.getPerguntaCliente().getCodigo().equals(perguntaCliente)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (<code>QuestionarioCliente</code>).
     */
    public ColaboradorVO getConsultor() {
        if (consultor == null) {
            consultor = new ColaboradorVO();
        }
        return (consultor);
    }

    /**
     * Define o objeto da classe <code>Colaborador</code> relacionado com (<code>QuestionarioCliente</code>).
     */
    public void setConsultor(ColaboradorVO obj) {
        this.consultor = obj;
    }

    /**
     * Retorna o objeto da classe <code>Cliente</code> relacionado com (<code>QuestionarioCliente</code>).
     */
    public ClienteVO getCliente() {
        if (cliente == null) {
            cliente = new ClienteVO();
        }
        return (cliente);
    }

    /**
     * Define o objeto da classe <code>Cliente</code> relacionado com (<code>QuestionarioCliente</code>).
     */
    public void setCliente(ClienteVO obj) {
        this.cliente = obj;
    }

    /**
     * Retorna o objeto da classe <code>Questionario</code> relacionado com (<code>QuestionarioCliente</code>).
     */
    public QuestionarioVO getQuestionario() {
        if (questionario == null) {
            questionario = new QuestionarioVO();
        }
        return (questionario);
    }

    /**
     * Define o objeto da classe <code>Questionario</code> relacionado com (<code>QuestionarioCliente</code>).
     */
    public void setQuestionario(QuestionarioVO obj) {
        this.questionario = obj;
    }

    /** Retorna Atributo responsável por manter os objetos da classe <code>QuestionarioPerguntaCliente</code>. */
    public List getQuestionarioPerguntaClienteVOs() {
        return (questionarioPerguntaClienteVOs);
    }

    /** Define Atributo responsável por manter os objetos da classe <code>QuestionarioPerguntaCliente</code>. */
    public void setQuestionarioPerguntaClienteVOs(List questionarioPerguntaClienteVOs) {
        this.questionarioPerguntaClienteVOs = questionarioPerguntaClienteVOs;
    }

    public Date getData() {
        return (data);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
     */
    public String getData_Apresentar() {
        return (Uteis.getData(data));
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public ColaboradorVO getConsultorAnterior() {
        if (consultorAnterior == null) {
            consultorAnterior = new ColaboradorVO();
        }
        return consultorAnterior;
    }

    public void setConsultorAnterior(ColaboradorVO consultorAnterior) {
        this.consultorAnterior = consultorAnterior;
    }

    /**
     * @return the eventoVO
     */
    public EventoVO getEventoVO() {
        if (eventoVO == null) {
            eventoVO = new EventoVO();
        }
        return eventoVO;
    }

    /**
     * @param eventoVO the eventoVO to set
     */
    public void setEventoVO(EventoVO eventoVO) {
        this.eventoVO = eventoVO;
    }

    /**
     * @return the tipoBV
     */
    public TipoBVEnum getTipoBV() {
        return tipoBV;
    }

    /**
     * @param tipoBV the tipoBV to set
     */
    public void setTipoBV(TipoBVEnum tipoBV) {
        this.tipoBV = tipoBV;
    }

    public Integer getCodigoColaboradorAntesAlteracao() {
        return codigoColaboradorAntesAlteracao;
    }

    public void setCodigoColaboradorAntesAlteracao(Integer codigoColaboradorAntesAlteracao) {
        this.codigoColaboradorAntesAlteracao = codigoColaboradorAntesAlteracao;
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }
    
    public Date getUltimaAtualizacao() {
        return ultimaAtualizacao;
    }
    
    public void setUltimaAtualizacao(Date ultimaAtualizacao) {
        this.ultimaAtualizacao = ultimaAtualizacao;
}


    public OrigemSistemaEnum getOrigemSistemaEnum() {
        return origemSistemaEnum;
    }

    public void setOrigemSistemaEnum(OrigemSistemaEnum origemSistemaEnum) {
        this.origemSistemaEnum = origemSistemaEnum;
    }
}

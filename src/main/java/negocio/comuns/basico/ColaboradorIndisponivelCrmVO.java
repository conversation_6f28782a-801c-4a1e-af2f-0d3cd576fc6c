package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

public class ColaboradorIndisponivelCrmVO extends SuperVO {

    private Integer codigo;
    private EmpresaVO empresaVO;
    private ColaboradorVO colaboradorIndisponivelVO;
    private ColaboradorVO colaboradorSuplenteVO;
    private Date dtInicio;
    private Date dtFim;
    private String motivo;


    public ColaboradorIndisponivelCrmVO() {
        super();
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null){
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getMotivo() {
        if (motivo == null){
            motivo = "";
        }
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public Date getDtFim() {
        return dtFim;
    }

    public void setDtFim(Date dtFim) {
        this.dtFim = dtFim;
    }

    public ColaboradorVO getColaboradorIndisponivelVO() {
        if (colaboradorIndisponivelVO == null){
            colaboradorIndisponivelVO = new ColaboradorVO();
        }
        return colaboradorIndisponivelVO;
    }

    public void setColaboradorIndisponivelVO(ColaboradorVO colaboradorIndisponivelVO) {
        this.colaboradorIndisponivelVO = colaboradorIndisponivelVO;
    }

    public ColaboradorVO getColaboradorSuplenteVO() {
        if (colaboradorSuplenteVO == null){
            colaboradorSuplenteVO = new ColaboradorVO();
        }
        return colaboradorSuplenteVO;
    }

    public void setColaboradorSuplenteVO(ColaboradorVO colaboradorSuplenteVO) {
        this.colaboradorSuplenteVO = colaboradorSuplenteVO;
    }

    public Date getDtInicio() {
        return dtInicio;
    }

    public void setDtInicio(Date dtInicio) {
        this.dtInicio = dtInicio;
    }

    public Boolean indisponibilidadeColaboradorPorData(Date dia){

            if((getDtInicio().before(dia) || getDtInicio().equals(dia))
                    && getDtFim().after(dia) || getDtFim().equals(dia))
                return true;
        return false;
    }
    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null){
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }



}

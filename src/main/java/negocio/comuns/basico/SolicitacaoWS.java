package negocio.comuns.basico;

import negocio.intranet.SolicitacaoAndamentoJSON;
import negocio.intranet.SolicitacaoJSON;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class SolicitacaoWS implements Serializable {

    public String codigo;
    public Integer idTarefa;
    public String empresa;
    public String titulo;
    private String solicitante;
    private String descricao;
    private String statusOrdem;
    private boolean atividadeAbertaPeloCliente = false;
    private String dataConclusaoComHora_Apresentar;
    private String dataCadastroComHora_Apresentar;
    private List<SolicitacaoEmAdamentoWS> andamentos;
    private String atendente;
    private String categoria;
    private String ultimaDataConclusaoSolicitacao_apresentar;
    private String tempoUtilAtendimento;

    public SolicitacaoWS(SolicitacaoJSON solicitacaoJSON) {
        codigo = (solicitacaoJSON.getCodigo());
        atividadeAbertaPeloCliente = (solicitacaoJSON.isAtividadeAbertaPeloCliente());
        dataCadastroComHora_Apresentar = (solicitacaoJSON.getDataCadastroComHora_Apresentar());
        dataConclusaoComHora_Apresentar = (solicitacaoJSON.getDataConclusaoComHora_Apresentar());
        descricao = (solicitacaoJSON.getDescricao());
        empresa = (solicitacaoJSON.getEmpresa());
        idTarefa = (solicitacaoJSON.getIdTarefa());
        solicitante = (solicitacaoJSON.getSolicitante());
        statusOrdem = (solicitacaoJSON.getStatusOrdem());
        titulo = (solicitacaoJSON.getTitulo());
        atendente = solicitacaoJSON.getAtendente();
        categoria = solicitacaoJSON.getCategoria();
        ultimaDataConclusaoSolicitacao_apresentar = solicitacaoJSON.getUltimaDataConclusaoSolicitacao_apresentar();
        tempoUtilAtendimento = solicitacaoJSON.getTempoUtilAtendimento();
        andamentos = new ArrayList<SolicitacaoEmAdamentoWS>();
        if (solicitacaoJSON.getAndamentos() != null) {
            for (SolicitacaoAndamentoJSON solicitacaoAndamentoJSON : solicitacaoJSON.getAndamentos()) {
                andamentos.add(new SolicitacaoEmAdamentoWS(solicitacaoAndamentoJSON));
            }
        }
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public Integer getIdTarefa() {
        return idTarefa;
    }

    public void setIdTarefa(Integer idTarefa) {
        this.idTarefa = idTarefa;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getSolicitante() {
        return solicitante;
    }

    public void setSolicitante(String solicitante) {
        this.solicitante = solicitante;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getStatusOrdem() {
        return statusOrdem;
    }

    public void setStatusOrdem(String statusOrdem) {
        this.statusOrdem = statusOrdem;
    }

    public boolean isAtividadeAbertaPeloCliente() {
        return atividadeAbertaPeloCliente;
    }

    public void setAtividadeAbertaPeloCliente(boolean atividadeAbertaPeloCliente) {
        this.atividadeAbertaPeloCliente = atividadeAbertaPeloCliente;
    }

    public String getDataConclusaoComHora_Apresentar() {
        return dataConclusaoComHora_Apresentar;
    }

    public void setDataConclusaoComHora_Apresentar(String dataConclusaoComHora_Apresentar) {
        this.dataConclusaoComHora_Apresentar = dataConclusaoComHora_Apresentar;
    }

    public String getDataCadastroComHora_Apresentar() {
        return dataCadastroComHora_Apresentar;
    }

    public void setDataCadastroComHora_Apresentar(String dataCadastroComHora_Apresentar) {
        this.dataCadastroComHora_Apresentar = dataCadastroComHora_Apresentar;
    }

    public List<SolicitacaoEmAdamentoWS> getAndamentos() {
        return andamentos;
    }

    public void setAndamentos(List<SolicitacaoEmAdamentoWS> andamentos) {
        this.andamentos = andamentos;
    }

    public String getAtendente() {
        return atendente;
    }

    public void setAtendente(String atendente) {
        this.atendente = atendente;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public String getUltimaDataConclusaoSolicitacao_apresentar() {
        return ultimaDataConclusaoSolicitacao_apresentar;
    }

    public void setUltimaDataConclusaoSolicitacao_apresentar(String ultimaDataConclusaoSolicitacao_apresentar) {
        this.ultimaDataConclusaoSolicitacao_apresentar = ultimaDataConclusaoSolicitacao_apresentar;
    }

    public String getTempoUtilAtendimento() {
        return tempoUtilAtendimento;
    }

    public void setTempoUtilAtendimento(String tempoUtilAtendimento) {
        this.tempoUtilAtendimento = tempoUtilAtendimento;
    }
}

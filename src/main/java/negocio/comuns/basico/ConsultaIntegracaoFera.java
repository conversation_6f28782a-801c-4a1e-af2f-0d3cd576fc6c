package negocio.comuns.basico;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.controle.json.cliente.FrequenciaAlunoFeraJSON;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperTO;

public class ConsultaIntegracaoFera extends SuperJSON {

    private int codigo = 0;
    private String nome;
    private Integer empresa;
    private String dataVencimentoPlano;
    private String status;
    private String email;
    private FrequenciaAlunoFeraJSON frequencia;
    private String ultimaVisita;
    private String dataNascimento;
    private Boolean empresaUsaFreePass = false;
    private String dataCadastro;
    private String sexo;
    private String DataVencimentoTreino;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(String dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUltimaVisita() {
        return ultimaVisita;
    }

    public void setUltimaVisita(String ultimaVisita) {
        this.ultimaVisita = ultimaVisita;
    }

    public Boolean getEmpresaUsaFreePass() {
        return empresaUsaFreePass;
    }

    public void setEmpresaUsaFreePass(Boolean empresaUsaFreePass) {
        this.empresaUsaFreePass = empresaUsaFreePass;
    }

    public String getDataVencimentoPlano() {
        return dataVencimentoPlano;
    }

    public void setDataVencimentoPlano(String dataVencimentoPlano) {
        this.dataVencimentoPlano = dataVencimentoPlano;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public FrequenciaAlunoFeraJSON getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(FrequenciaAlunoFeraJSON frequencia) {
        this.frequencia = frequencia;
    }

    public String getDataVencimentoTreino() {
        return DataVencimentoTreino;
    }

    public void setDataVencimentoTreino(String dataVencimentoTreino) {
        DataVencimentoTreino = dataVencimentoTreino;
    }

    public String toJSON() {
        try {
            JSONObject object = new JSONObject();
            object.put("id",getCodigo());
            object.put("nome",getNome());
            object.put("empresa",getEmpresa());
            object.put("dataVencimentoPlano",getDataVencimentoPlano());
            object.put("dataVencimentoTreino",getDataVencimentoTreino());
            object.put("status",getStatus());
            object.put("email",getEmail());
            object.put("frequencia",new JSONObject(getFrequencia().toJSON()));
            object.put("ultimaVisita",getUltimaVisita());
            object.put("dataNascimento",getDataNascimento());
            object.put("empresaUsaFreePass",getEmpresaUsaFreePass());
            object.put("dataCadastro",getDataCadastro());
            object.put("sexo",getSexo());

            return object.toString();
        } catch (Exception ignored) {
            return "{}";
        }
    }
}

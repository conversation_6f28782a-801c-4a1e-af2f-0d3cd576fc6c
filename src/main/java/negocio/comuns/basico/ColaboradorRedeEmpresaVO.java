package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import servicos.discovery.RedeDTO;

import java.util.Date;

public class ColaboradorRedeEmpresaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer colaborador;
    private String chaveOrigem;
    private String chaveDestino;
    private Integer empresaDestino;
    private Date datacadastro;
    private Date dataatualizacao;
    private String nomeUnidade;
    private String mensagemSituacao;
    private RedeDTO redeDTO;
    private Integer colaboradorReplicado;
    private boolean selecionado;

    public ColaboradorRedeEmpresaVO(Integer colaborador, String chaveOrigem, String chaveDestino, Integer empresaDestino) {
        this.colaborador = colaborador;
        this.chaveOrigem = chaveOrigem;
        this.chaveDestino = chaveDestino;
        this.empresaDestino = empresaDestino;
    }

    public ColaboradorRedeEmpresaVO(String nomeUnidade, Integer colaborador, String chaveOrigem, String chaveDestino, Integer empresaDestino) {
        this.nomeUnidade = nomeUnidade;
        this.colaborador = colaborador;
        this.chaveOrigem = chaveOrigem;
        this.chaveDestino = chaveDestino;
        this.empresaDestino = empresaDestino;
    }

    public ColaboradorRedeEmpresaVO() {
        
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getColaborador() {
        return colaborador;
    }

    public void setColaborador(Integer colaborador) {
        this.colaborador = colaborador;
    }

    public String getChaveOrigem() {
        return chaveOrigem;
    }

    public void setChaveOrigem(String chaveOrigem) {
        this.chaveOrigem = chaveOrigem;
    }

    public String getChaveDestino() {
        return chaveDestino;
    }

    public void setChaveDestino(String chaveDestino) {
        this.chaveDestino = chaveDestino;
    }

    public Date getDatacadastro() {
        return datacadastro;
    }

    public void setDatacadastro(Date datacadastro) {
        this.datacadastro = datacadastro;
    }

    public Boolean getDataAtualizacaoInformada() {
        return getDataatualizacao() != null;
    }
    public Date getDataatualizacao() {
        return dataatualizacao;
    }

    public void setDataatualizacao(Date dataatualizacao) {
        this.dataatualizacao = dataatualizacao;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getMensagemSituacao() {
        return mensagemSituacao;
    }

    public void setMensagemSituacao(String mensagemSituacao) {
        this.mensagemSituacao = mensagemSituacao;
    }

    public RedeDTO getRedeDTO() {
        return redeDTO;
    }

    public void setRedeDTO(RedeDTO redeDTO) {
        this.redeDTO = redeDTO;
    }

    public Integer getColaboradorReplicado() {
        return colaboradorReplicado;
    }

    public void setColaboradorReplicado(Integer colaboradorReplicado) {
        this.colaboradorReplicado = colaboradorReplicado;
    }

    public Integer getEmpresaDestino() {
        return empresaDestino;
    }

    public void setEmpresaDestino(Integer empresaDestino) {
        this.empresaDestino = empresaDestino;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }
}

package negocio.comuns.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 19/03/2020
 */
public class BoletoEmailTO extends SuperTO {

    private EmpresaVO empresaVO;
    private PessoaVO pessoaVO;
    private String linhaDigitavel;
    private Double valor;
    private Date dataVencimento;
    private String urlFileArquivoBoleto;
    private String nomeArquivoBoleto;
    private String htmlEmail;
    private String linkBoleto;

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public String getLinhaDigitavel() {
        return linhaDigitavel;
    }

    public void setLinhaDigitavel(String linhaDigitavel) {
        this.linhaDigitavel = linhaDigitavel;
    }

    public String getHtmlEmail() {
        return htmlEmail;
    }

    public void setHtmlEmail(String htmlEmail) {
        this.htmlEmail = htmlEmail;
    }

    public String getMesReferencia() {
        if (getDataVencimento() != null) {
            return Uteis.getMesNomeReferencia(getDataVencimento()) + "/" + Uteis.getAnoData(getDataVencimento());
        } else {
            return "";
        }
    }

    public String getDataVencimentoApresentar() {
        if (getDataVencimento() != null) {
            return Calendario.getDataAplicandoFormatacao(getDataVencimento(), "dd/MM/yyyy");
        } else {
            return "";
        }
    }

    public String getValorApresentar() {
        if (getValor() != null) {
            return Formatador.formatarValorMonetarioSemMoeda(getValor());
        } else {
            return "";
        }
    }

    public String getUrlLogoEmpresa(String chave) {
        try {
            String genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, getEmpresaVO().getCodigo().toString());
            if (UteisValidacao.emptyString(genKey)) {
                genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, getEmpresaVO().getCodigo().toString());
            }
            if (UteisValidacao.emptyString(genKey)) {
                genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA, getEmpresaVO().getCodigo().toString());
            }
            return Uteis.getPaintFotoDaNuvem(genKey);
        } catch (Exception ignored) {
            return "";
        }
    }

    public String getNomeArquivoBoleto() {
        return nomeArquivoBoleto;
    }

    public void setNomeArquivoBoleto(String nomeArquivoBoleto) {
        this.nomeArquivoBoleto = nomeArquivoBoleto;
    }

    public String getUrlFileArquivoBoleto() {
        return urlFileArquivoBoleto;
    }

    public void setUrlFileArquivoBoleto(String urlFileArquivoBoleto) {
        this.urlFileArquivoBoleto = urlFileArquivoBoleto;
    }

    public String getLinkBoleto() {
        if (linkBoleto == null) {
            linkBoleto = "";
        }
        return linkBoleto;
    }

    public void setLinkBoleto(String linkBoleto) {
        this.linkBoleto = linkBoleto;
    }
}

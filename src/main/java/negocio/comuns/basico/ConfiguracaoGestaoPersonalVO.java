/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class ConfiguracaoGestaoPersonalVO extends SuperVO{
    
    private Integer empresa;
    private boolean obrigatorioAssociarAlunoAoCheckIn = false;
    private boolean bloquearAcessoPersonalSemCredito = false;
    private boolean mostrarFotosAlunosMonitor = false;
    private Integer duracaoCredito = 0;
    private Integer tempoCheckOutAutomatico = 0;
    private Integer diasBloqueioParcelaEmAberto = 0;
    private boolean gerarCreditoSomenteAoPagar = false;
    private boolean consumirCreditoPorAlunoVinculado = false;
    private boolean usarFotoPersonal = false;
    private List<ConfiguracaoGestaoPersonalEmailVO> emails = new ArrayList<ConfiguracaoGestaoPersonalEmailVO>();

    public boolean isObrigatorioAssociarAlunoAoCheckIn() {
        return obrigatorioAssociarAlunoAoCheckIn;
    }

    public void setObrigatorioAssociarAlunoAoCheckIn(boolean obrigatorioAssociarAlunoAoCheckIn) {
        this.obrigatorioAssociarAlunoAoCheckIn = obrigatorioAssociarAlunoAoCheckIn;
    }

    public boolean isBloquearAcessoPersonalSemCredito() {
        return bloquearAcessoPersonalSemCredito;
    }

    public void setBloquearAcessoPersonalSemCredito(boolean bloquearAcessoPersonalSemCredito) {
        this.bloquearAcessoPersonalSemCredito = bloquearAcessoPersonalSemCredito;
    }

    public Integer getDuracaoCredito() {
        return duracaoCredito;
    }

    public void setDuracaoCredito(Integer duracaoCredito) {
        this.duracaoCredito = duracaoCredito;
    }

    public Integer getDiasBloqueioParcelaEmAberto() {
        return diasBloqueioParcelaEmAberto;
    }

    public void setDiasBloqueioParcelaEmAberto(Integer diasBloqueioParcelaEmAberto) {
        this.diasBloqueioParcelaEmAberto = diasBloqueioParcelaEmAberto;
    }

    public boolean isGerarCreditoSomenteAoPagar() {
        return gerarCreditoSomenteAoPagar;
    }

    public void setGerarCreditoSomenteAoPagar(boolean gerarCreditoSomenteAoPagar) {
        this.gerarCreditoSomenteAoPagar = gerarCreditoSomenteAoPagar;
    }

    public boolean isConsumirCreditoPorAlunoVinculado() {
        return consumirCreditoPorAlunoVinculado;
    }

    public void setConsumirCreditoPorAlunoVinculado(boolean consumirCreditoPorAlunoVinculado) {
        this.consumirCreditoPorAlunoVinculado = consumirCreditoPorAlunoVinculado;
    }

    public List<ConfiguracaoGestaoPersonalEmailVO> getEmails() {
        return emails;
    }

    public void setEmails(List<ConfiguracaoGestaoPersonalEmailVO> emails) {
        this.emails = emails;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public boolean isMostrarFotosAlunosMonitor() {
        return mostrarFotosAlunosMonitor;
    }

    public void setMostrarFotosAlunosMonitor(boolean mostrarFotosAlunosMonitor) {
        this.mostrarFotosAlunosMonitor = mostrarFotosAlunosMonitor;
    }

    public Integer getTempoCheckOutAutomatico() {
        if(tempoCheckOutAutomatico == null){
            tempoCheckOutAutomatico = 0;
        }
        return tempoCheckOutAutomatico;
    }

    public void setTempoCheckOutAutomatico(Integer tempoCheckOutAutomatico) {
        this.tempoCheckOutAutomatico = tempoCheckOutAutomatico;
    }

    public boolean isUsarFotoPersonal() {
        return usarFotoPersonal;
    }

    public void setUsarFotoPersonal(boolean usarFotoPersonal) {
        this.usarFotoPersonal = usarFotoPersonal;
    }
    
}

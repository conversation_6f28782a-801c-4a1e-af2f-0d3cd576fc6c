/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import negocio.comuns.arquitetura.SuperVO;

/**
 * <AUTHOR>
 */
public class DepartamentoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo = 0;
    private String nome = "";
    @FKJson
    private EmpresaVO empresaVO;
    private boolean concessionario = false;

    public DepartamentoVO() {
        super();
    }

    public static void validarDados(DepartamentoVO obj) throws Exception {
        if (obj.getNome().trim().isEmpty()) {
            throw new Exception("O campo Nome(Departamento), deve ser preenchido.");
        }

        if (obj.getEmpresaVO() == null || obj.getEmpresaVO().getCodigo() == 0) {
            throw new Exception("A empresa(Departamento), deve ser preenchida.");
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        if (nome == null) {
            return "";
        } else {
            return nome;
        }
    }

    public void setNome(String nome) {
        this.nome = nome.toUpperCase();
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public boolean isConcessionario() {
        return concessionario;
    }

    public void setConcessionario(boolean concessionario) {
        this.concessionario = concessionario;
    }
}

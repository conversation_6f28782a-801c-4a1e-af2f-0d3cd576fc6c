package negocio.comuns.basico;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade Grupo. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
*/
public class GrupoVO extends SuperVO {
	
    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected Double percentualDescontoGrupo;
    protected Double valorDescontoGrupo;
    protected String tipoDesconto;
    protected Integer quantidadeMinimaAluno;
    protected String situacaoAluno;
    private String tipo;
    protected Boolean grupoInativo;

    /**
     * Construtor padrão da classe <code>Grupo</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public GrupoVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>GrupoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(GrupoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
        if (obj.getDescricao().equals("")) { 
            throw new ConsistirException("O campo DESCRIÇÃO (Grupo) deve ser informado.");
        }

        if (obj.getTipo().equals("")) {
            throw new ConsistirException("O campo Tipo (Grupo) deve ser informado.");
        }

        if ((obj.getTipoDesconto() == null) ||
                (obj.getTipoDesconto().equals(""))) { 
            throw new ConsistirException("O campo TIPO DE DESCONTO (Grupo) deve ser informado.");
        }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
        setDescricao( getDescricao().toUpperCase() );
        setTipoDesconto( getTipoDesconto().toUpperCase() );
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setDescricao( "" );
        setPercentualDescontoGrupo( new Double(0) );
        setValorDescontoGrupo( new Double(0) );
        setTipoDesconto( "" );
        setTipo("");
        setGrupoInativo(null);
    }
	

    public String getTipoDesconto() {
        if (tipoDesconto== null) {
            tipoDesconto = "";
        }
        return (tipoDesconto);
    }
     
    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico. 
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o 
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc. 
    */
    public String getTipoDesconto_Apresentar() {
        if (tipoDesconto == null) {
            tipoDesconto = "";
        }
        if (tipoDesconto.equals("PE")) {
            return "Percentual ";
        }
        if (tipoDesconto.equals("VA")) {
            return "Valor";
        }
        if (tipoDesconto.equals("BO")) {
            return "Bônus";
        }
        return (tipoDesconto);
    }
     
    public void setTipoDesconto( String tipoDesconto ) {
        this.tipoDesconto = tipoDesconto;
    }

    public Double getValorDescontoGrupo() {
        return (valorDescontoGrupo);
    }
     
    public void setValorDescontoGrupo( Double valorDescontoGrupo ) {
        this.valorDescontoGrupo = valorDescontoGrupo;
    }

    public Double getPercentualDescontoGrupo() {
        return (percentualDescontoGrupo);
    }
     
    public void setPercentualDescontoGrupo( Double percentualDescontoGrupo ) {
        this.percentualDescontoGrupo = percentualDescontoGrupo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }
     
    public void setDescricao( String descricao ) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }

    public Integer getQuantidadeMinimaAluno() {
        return quantidadeMinimaAluno;
    }

    public void setQuantidadeMinimaAluno(Integer quantidadeMinimaAluno) {
        this.quantidadeMinimaAluno = quantidadeMinimaAluno;
    }

    public String getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(String situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Boolean getGrupoInativo() {
        return grupoInativo;
    }

    public void setGrupoInativo(Boolean grupoInativo) {
        this.grupoInativo = grupoInativo;
    }
}

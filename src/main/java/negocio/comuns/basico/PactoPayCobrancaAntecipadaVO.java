package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayCobrancaAntecipadaDTO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.utilitarias.Calendario;
import org.json.JSONObject;

import java.util.Date;

public class PactoPayCobrancaAntecipadaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private PessoaVO pessoaVO;
    private Date dataRegistro;
    private String dados;
    private MeioEnvio meioEnvio;
    private PactoPayComunicacaoVO pactoPayComunicacao;

    public PactoPayCobrancaAntecipadaVO() {

    }

    public PactoPayCobrancaAntecipadaVO(PessoaVO pessoaVO, PactoPayCobrancaAntecipadaDTO dto, PactoPayComunicacaoVO pactoPayComunicacaoVO) {
        this.dataRegistro = Calendario.hoje();
        this.pessoaVO = pessoaVO;
        this.dados = dto.toString();
        this.meioEnvio = pactoPayComunicacaoVO.getMeioEnvio();
        this.pactoPayComunicacao = pactoPayComunicacaoVO;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDados() {
        if (dados == null) {
            dados = "";
        }
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }

    public PactoPayCobrancaAntecipadaDTO getDTO() {
        try {
            return new PactoPayCobrancaAntecipadaDTO(new JSONObject(this.getDados()));
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public MeioEnvio getMeioEnvio() {
        return meioEnvio;
    }

    public void setMeioEnvio(MeioEnvio meioEnvio) {
        this.meioEnvio = meioEnvio;
    }

    public PactoPayComunicacaoVO getPactoPayComunicacao() {
        if (pactoPayComunicacao == null) {
            pactoPayComunicacao = new PactoPayComunicacaoVO();
        }
        return pactoPayComunicacao;
    }

    public void setPactoPayComunicacao(PactoPayComunicacaoVO pactoPayComunicacao) {
        this.pactoPayComunicacao = pactoPayComunicacao;
    }
}

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

/*
 * Created by <PERSON><PERSON>
 */
public class TiposProduto extends SuperTO {

    private String codigo;
    private String descricao;

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
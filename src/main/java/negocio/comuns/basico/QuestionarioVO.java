package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoServicoEnum;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * Reponsável por manter os dados da entidade Questionario. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class QuestionarioVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo = 0;
    protected String tituloPesquisa = "";
    private String nomeInterno = "";
    private String tipoQuestionario = "";
    @NaoControlarLogAlteracao
    @ListJson(clazz = QuestionarioPerguntaVO.class)
    private List<QuestionarioPerguntaVO> questionarioPerguntaVOs = new ArrayList<QuestionarioPerguntaVO>();
    @NaoControlarLogAlteracao
    private List<QuestionarioPerguntaVO> questionarioPerguntaVOsAntesAlteracao = new ArrayList<QuestionarioPerguntaVO>();
    private String fundoCor;
    private String fundoImagem;
    private String textoInicio;
    private String textoFim;
    private boolean somenteUmaResposta = false;
    private boolean ativo = true;

    /**
     * Construtor padrão da classe <code>Questionario</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public QuestionarioVO() {
        super();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>QuestionarioVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(QuestionarioVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.isPesquisa() && obj.getTituloPesquisa().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Questionário) deve ser informado.");
        }
        if (obj.getTipoQuestionario().equals("")) {
            throw new ConsistirException("O campo TIPO (Questionário) deve ser informado.");
        }
        if(obj.getQuestionarioPerguntaVOs().isEmpty()){
            throw new ConsistirException("Cadastre pelo menos uma pergunta para esse questionário");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setTituloPesquisa(getTituloPesquisa().toUpperCase());
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>QuestionarioPerguntaVO</code>
     * ao List <code>questionarioPerguntaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>QuestionarioPergunta</code> - getPergunta().getCodigo() - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>QuestionarioPerguntaVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjQuestionarioPerguntaVOs(QuestionarioPerguntaVO obj) throws Exception {
        QuestionarioPerguntaVO.validarDados(obj);
        int index = 0;
        Iterator i = getQuestionarioPerguntaVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaVO objExistente = (QuestionarioPerguntaVO) i.next();
            if (objExistente.getPergunta().getCodigo().equals(obj.getPergunta().getCodigo())) {
                getQuestionarioPerguntaVOs().set(index, obj);

                return;
            }
            index++;
        }
        getQuestionarioPerguntaVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>QuestionarioPerguntaVO</code>
     * no List <code>questionarioPerguntaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>QuestionarioPergunta</code> - getPergunta().getCodigo() - como identificador (key) do objeto no List.
     *
     * @param pergunta Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjQuestionarioPerguntaVOs(Integer pergunta) throws Exception {
        int index = 0;
        Iterator i = getQuestionarioPerguntaVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaVO objExistente = (QuestionarioPerguntaVO) i.next();
            if (objExistente.getPergunta().getCodigo().equals(pergunta)) {
                getQuestionarioPerguntaVOs().remove(index);
                return;
            }
            index++;
        }
        return;
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>QuestionarioPerguntaVO</code>
     * no List <code>questionarioPerguntaVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>QuestionarioPergunta</code> - getPergunta().getCodigo() - como identificador (key) do objeto no List.
     *
     * @param pergunta Parâmetro para localizar o objeto do List.
     */
    public QuestionarioPerguntaVO consultarObjQuestionarioPerguntaVO(Integer pergunta) throws Exception {
        Iterator i = getQuestionarioPerguntaVOs().iterator();
        while (i.hasNext()) {
            QuestionarioPerguntaVO objExistente = (QuestionarioPerguntaVO) i.next();
            if (objExistente.getPergunta().getCodigo().equals(pergunta)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe <code>QuestionarioPergunta</code>.
     */
    public List<QuestionarioPerguntaVO> getQuestionarioPerguntaVOs() {
        if (questionarioPerguntaVOs == null) {
            questionarioPerguntaVOs = new ArrayList<QuestionarioPerguntaVO>();
        }
        return (questionarioPerguntaVOs);
    }

    /**
     * Define Atributo responsável por manter os objetos da classe <code>QuestionarioPergunta</code>.
     */
    public void setQuestionarioPerguntaVOs(List<QuestionarioPerguntaVO> questionarioPerguntaVOs) {
        this.questionarioPerguntaVOs = questionarioPerguntaVOs;
    }

    public String getTituloPesquisa() {
        if (tituloPesquisa == null) {
            tituloPesquisa = "";
        }
        return (tituloPesquisa);
    }

    public void setTituloPesquisa(String descricao) {
        this.tituloPesquisa = descricao;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoQuestionario() {
        if (tipoQuestionario == null){
            tipoQuestionario = "";
        }
        return tipoQuestionario;
    }

    public void setTipoQuestionario(String tipoQuestionario) {
        this.tipoQuestionario = tipoQuestionario;
    }

    public List<QuestionarioPerguntaVO> getQuestionarioPerguntaVOsAntesAlteracao() {
        return questionarioPerguntaVOsAntesAlteracao;
    }

    public void setQuestionarioPerguntaVOsAntesAlteracao(List<QuestionarioPerguntaVO> questionarioPerguntaVOsAntesAlteracao) {
        this.questionarioPerguntaVOsAntesAlteracao = questionarioPerguntaVOsAntesAlteracao;
    }
    
    public void registrarPerguntasAntesAlteracao() throws Exception{
        questionarioPerguntaVOsAntesAlteracao = new ArrayList<QuestionarioPerguntaVO>();
        for (QuestionarioPerguntaVO pergunta : questionarioPerguntaVOs){
            questionarioPerguntaVOsAntesAlteracao.add((QuestionarioPerguntaVO) pergunta.getClone(true));
        }
    }

    public String getFundoCor() {
        if (fundoCor == null) {
            fundoCor = "";
        }
        return fundoCor;
    }

    public void setFundoCor(String fundoCor) {
        this.fundoCor = fundoCor;
    }

    public String getFundoImagem() {
        if (fundoImagem == null) {
            fundoImagem = "";
        }
        return fundoImagem;
    }

    public void setFundoImagem(String fundoImagem) {
        this.fundoImagem = fundoImagem;
    }

    public String getTextoInicio() {
        if (textoInicio == null) {
            textoInicio = "";
        }
        return textoInicio;
    }

    public void setTextoInicio(String textoInicio) {
        this.textoInicio = textoInicio;
    }

    public String getTextoFim() {
        if (textoFim == null) {
            textoFim = "";
        }
        return textoFim;
    }

    public void setTextoFim(String textoFim) {
        this.textoFim = textoFim;
    }

    public String getTextoInicio_Apresentar() {
        return getTextoInicio().replaceAll("\n", "<br/>");
    }

    public String getTextoFim_Apresentar() {
        return getTextoFim().replaceAll("\n", "<br/>");
    }

    public boolean isPesquisa() {
        return getTipoQuestionario() != null && getTipoQuestionario().equals(TipoServicoEnum.PESQUISA.getTipo());
    }

    public boolean isSomenteUmaResposta() {
        return somenteUmaResposta;
    }

    public void setSomenteUmaResposta(boolean somenteUmaResposta) {
        this.somenteUmaResposta = somenteUmaResposta;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public String getNomeInterno() {
        return nomeInterno;
    }

    public void setNomeInterno(String nomeInterno) {
        this.nomeInterno = nomeInterno;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        QuestionarioVO that = (QuestionarioVO) o;
        return somenteUmaResposta == that.somenteUmaResposta &&
                ativo == that.ativo &&
                Objects.equals(codigo, that.codigo) &&
                Objects.equals(tituloPesquisa, that.tituloPesquisa) &&
                Objects.equals(tipoQuestionario, that.tipoQuestionario) &&
                Objects.equals(questionarioPerguntaVOs, that.questionarioPerguntaVOs) &&
                Objects.equals(questionarioPerguntaVOsAntesAlteracao, that.questionarioPerguntaVOsAntesAlteracao) &&
                Objects.equals(fundoCor, that.fundoCor) &&
                Objects.equals(fundoImagem, that.fundoImagem) &&
                Objects.equals(textoInicio, that.textoInicio) &&
                Objects.equals(textoFim, that.textoFim);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo, tituloPesquisa, tipoQuestionario, questionarioPerguntaVOs, questionarioPerguntaVOsAntesAlteracao, fundoCor, fundoImagem, textoInicio, textoFim, somenteUmaResposta, ativo);
    }
}

package negocio.comuns.basico;

import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import negocio.comuns.arquitetura.SuperTO;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 10/10/13
 * Time: 09:57
 * To change this template use File | Settings | File Templates.
 */
public class SituacaoClienteTO extends SuperTO {

    private boolean selecionado = false;
    private SituacaoClienteEnum situacaoClienteEnum = null;

    public boolean getSelecionado() {
        return selecionado;
    }

    public String getDescricao(){
        return getSituacaoClienteEnum().getDescricao();
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public SituacaoClienteEnum getSituacaoClienteEnum() {
        return situacaoClienteEnum;
    }

    public void setSituacaoClienteEnum(SituacaoClienteEnum situacaoClienteEnum) {
        this.situacaoClienteEnum = situacaoClienteEnum;
    }

    public String getCodigo(){
        return getSituacaoClienteEnum().getCodigo();
    }
}

package negocio.comuns.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoTelefone;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ClienteSescTO {
    private String CategoriaCliente;
    private String Habilitacao;
    private Date DataPrimeiraHabilitacao;
    private String SituacaoHabilitacao;
    private String HabilitacaoTitular;
    private String Parentesco;
    private String Nome;
    private String NomeSocial;
    private Date DataNascimento;
    private String Sexo;
    private String EstadoCivil;
    private String GrauEscolaridade;
    private String NomePai;
    private String NomeMae;
    private String NomeResponsavel; //API SESC-GO
    private String CpfResponsavel; //API SESC-GO
    private String Nacionalidade;
    private String Naturalidade;
    private Double ValorRendaMensal;
    private Double ValorRendaFamiliar;
    private Date DataVencimentoHabilitacaoSesc;
    private Integer NumeroViaHabilitacaoAtiva;
    private String Observacoes;
    private String Foto;
    private IdentificacaoSescTO Identificacao;
    private EnderecoSescTO Endereco;
    private ContatoSescTO Contato;

    public ClienteSescTO() {
    }

    public ClienteSescTO(JSONObject cliente) throws Exception {
        this.setCategoriaCliente(cliente.getString("dscategoria"));
        this.setHabilitacao(formatarCampoHabilitacaoApiSesc(cliente.getInt("cduop"), cliente.getInt("sqmatric"), cliente.getInt("nudv")));
        this.setDataPrimeiraHabilitacao(Uteis.getDate(cliente.optString("datacadastro"), "yyyy-MM-dd"));
        //this.setSituacaoHabilitacao(null);
        //this.setHabilitacaoTitular(null);
        //this.setParentesco(null);
        this.setNome(cliente.getString("nome"));
        this.setNomeSocial(cliente.getString("nomesocial"));
        this.setDataNascimento(Uteis.getDate(cliente.optString("datanascimento"), "yyyy-MM-dd"));
        this.setSexo(cliente.getInt("cdsexo") == 1 ? "M" : "F");
        this.setEstadoCivil(formatarCampoEstadoCivilApiSescGo(cliente.getInt("estadocivil"))); // Nao temos certeza da informação/parse
        //this.setGrauEscolaridade(null);
        this.setNomePai(cliente.getString("pai"));
        this.setNomeMae(cliente.getString("mae"));
        this.setNacionalidade(cliente.getString("nacionalidade"));
        this.setNaturalidade(cliente.getString("naturalidade"));
        //this.setValorRendaMensal(null);
        this.setDataVencimentoHabilitacaoSesc(Uteis.getDate(cliente.getString("datarenovacao"), "yyyy-MM-dd"));
        this.setNumeroViaHabilitacaoAtiva(1);

        if (!cliente.getString("categ").equals("T")) {
            if (!UteisValidacao.emptyString(this.getNomeMae())) {
                this.setNomeResponsavel(this.getNomeMae());
            }

            if (!UteisValidacao.emptyString(this.getNomePai())) {
                this.setNomeResponsavel(this.getNomePai());
            }
        }

        this.setIdentificacao(montarObjIdentificacaoApiSescDf(cliente));
        this.setContato(montarObjContatoApiSescDf(cliente));
        this.setEndereco(montarObjEnderecoApiSescDf(cliente));
        //this.setFoto(null);
    }

    public ClienteSescTO(JSONObject cliente, boolean usarJsonApiSescGo) throws Exception {
        if (usarJsonApiSescGo) { // GC-897: Nova API de Integração do SCA com o SESC/GO
            this.CategoriaCliente = formatarCampoCategoriaApiSescGo(cliente.optInt("cdcategori"));
            this.Habilitacao = formatarCampoHabilitacaoApiSesc(cliente.getInt("cduop"), cliente.getInt("sqmatric"), cliente.getInt("nudv"));
            this.DataPrimeiraHabilitacao = Uteis.getDate(cliente.optString("dtinscri"), "yyyy-MM-dd'T'HH:mm:ss");
            this.SituacaoHabilitacao = formatarCampoSituacaoHabilitacaoApiSescGo(cliente.optInt("stmatric"));
            this.HabilitacaoTitular = formatarCampoHabilitacaoTitularApiSescGo(cliente);
            this.Parentesco = formatarCampoParentescoApiSescGo(cliente.optInt("dsparentsc"));
            this.Nome = cliente.optString("nmcliente");
            this.NomeSocial = cliente.optString("nmsocial");
            this.DataNascimento = Uteis.getDate(cliente.optString("dtnascimen"), "yyyy-MM-dd'T'HH:mm:ss");
            this.Sexo = cliente.optInt("cdsexo") == 0 ? "M" : "F";
            this.EstadoCivil = formatarCampoEstadoCivilApiSescGo(cliente.optInt("cdestcivil"));
            this.GrauEscolaridade = formatarCampoGrauEscolaridadeApiSescGo(cliente.optInt("cdnivel"));
            this.NomePai = cliente.optString("nmpai");
            this.NomeMae = cliente.optString("nmmae");
            this.NomeResponsavel = cliente.optString("nomeresponsavel");
            this.CpfResponsavel = cliente.optString("cpfresponsavel");
            this.Nacionalidade = cliente.optString("dsnacional");
            this.Naturalidade = cliente.optString("dsnatural");
            this.ValorRendaMensal = cliente.optDouble("vlrenda", 0.0);
            this.DataVencimentoHabilitacaoSesc = Uteis.getDate(cliente.optString("dtvencto"), "yyyy-MM-dd'T'HH:mm:ss");
            this.NumeroViaHabilitacaoAtiva = 1;
            this.Identificacao = montarObjIdentificacaoApiSescGo(cliente);
            this.Contato = montarObjContatoApiSescGo(cliente);
            this.Foto = cliente.optString("foto");
        } else {
            this.CategoriaCliente = cliente.optString("CategoriaCliente");
            this.Habilitacao = cliente.optString("Habilitacao");
            this.DataPrimeiraHabilitacao = Uteis.getDate(cliente.optString("DataPrimeiraHabilitacao"), "dd/MM/yyyy");
            this.SituacaoHabilitacao = cliente.optString("SituacaoHabilitacao");
            this.HabilitacaoTitular = cliente.optString("HabilitacaoTitular");
            this.Parentesco = cliente.optString("Parentesco");
            this.Nome = cliente.optString("Nome");
            this.NomeSocial = cliente.optString("NomeSocial");
            this.DataNascimento = Uteis.getDate(cliente.optString("DataNascimento"), "dd/MM/yyyy");
            this.Sexo = cliente.optString("Sexo");
            this.EstadoCivil = cliente.optString("EstadoCivil");
            this.GrauEscolaridade = cliente.optString("GrauEscolaridade");
            this.NomePai = cliente.optString("NomePai");
            this.NomeMae = cliente.optString("NomeMae");
            this.Nacionalidade = cliente.optString("Nacionalidade");
            this.Naturalidade = cliente.optString("Naturalidade");
            this.ValorRendaMensal = cliente.optDouble("ValorRendaMensal", 0.0);
            this.ValorRendaFamiliar = cliente.optDouble("ValorRendaFamiliar", 0.0);
            this.DataVencimentoHabilitacaoSesc = Uteis.getDate(cliente.optString("DataVencimentoHabilitacaoSesc"), "dd/MM/yyyy");
            this.NumeroViaHabilitacaoAtiva = cliente.optInt("NumeroViaHabilitacaoAtiva");
            this.Observacoes = cliente.optString("Observacoes");
            this.Foto = cliente.optString("Foto");
            this.Identificacao = new IdentificacaoSescTO(cliente.optJSONObject("Identificacao"));
            this.Endereco = new EnderecoSescTO(cliente.optJSONObject("Endereco"), false);
            this.Contato = new ContatoSescTO(cliente.optJSONObject("Contato"));
        }
    }

    ContatoSescTO montarObjContatoApiSescDf(JSONObject cliente) {
        final ContatoSescTO contato = new ContatoSescTO();
        contato.setDDDTelefoneCelular(cliente.getString("ddd"));
        contato.setNumeroTelefoneCelular(cliente.getString("fonecelular"));
        contato.setDDDTelefoneFixo(cliente.getString("ddd"));
        contato.setNumeroTelefoneFixo(cliente.getString("telefone"));
        contato.setEmail(cliente.getString("email"));

        return contato;
    }

    EnderecoSescTO montarObjEnderecoApiSescDf(JSONObject cliente) {
        final EnderecoSescTO enderecoSescTO = new EnderecoSescTO();
        enderecoSescTO.setLogradouro(cliente.getString("endereco"));
        enderecoSescTO.setNumeroImovel(cliente.has("unidadeimp") ? String.valueOf(cliente.getInt("unidadeimp")) : "");
        enderecoSescTO.setComplementoNumeroImovel(cliente.getString("complemento"));
        enderecoSescTO.setBairro(cliente.getString("bairro"));
        enderecoSescTO.setMunicipio(cliente.getString("cidade"));
        enderecoSescTO.setUf(cliente.getString("uf"));
        enderecoSescTO.setCep(cliente.getString("cep"));

        return enderecoSescTO;
    }

    IdentificacaoSescTO montarObjIdentificacaoApiSescDf(JSONObject cliente) {
        final IdentificacaoSescTO identificacao = new IdentificacaoSescTO();
        identificacao.setCpf(cliente.getString("cpf"));

        if (!UteisValidacao.emptyString(cliente.getString("rg"))) {
            identificacao.setTipoDocumentoIdentidade("RG");
            identificacao.setNumeroDocumentoIdentidade(cliente.getString("rg"));
            identificacao.setOrgaoEmissor(cliente.getString("orgaoemissor"));
            identificacao.setDataEmissao(cliente.getString("dataemissao"));
        }

        return identificacao;
    }

    public String formatarCampoCategoriaApiSescGo(int cdcategori) {
        // Mapeamento entre o 'cdcategori' da api sesc-go e o nome da categoria correspondente no sistema pacto
        Map<Integer, String> mapCodigoParaNome = new HashMap<>();
        mapCodigoParaNome.put(10, "DEPENDENTE DE TRAB. FUNCIONARIO");
        mapCodigoParaNome.put(4, "TRAB. DO COMERCIO FALECIDO");
        mapCodigoParaNome.put(3, "TRAB. DO COMERCIO DESEMPREGADO");
        mapCodigoParaNome.put(5, "TRAB. DO COMERCIO APOSENTADO");
        mapCodigoParaNome.put(7, "PUBLICO EM GERAL CONVENIADO");
        mapCodigoParaNome.put(8, "PUBLICO EM GERAL DEP.CONVENIO");
        mapCodigoParaNome.put(9, "TRAB. DO COMERCIO FUNCIONARIO");
        mapCodigoParaNome.put(11, "TRAB. DO COMERCIO DE OUTRO ESTADO");
        mapCodigoParaNome.put(14, "DEPENDENTE DE TRAB. DO COM OUTRO ESTADO");
        mapCodigoParaNome.put(16, "DEPENDENTE DE TRAB. DO COMERCIO DESEMPREGADO");
        mapCodigoParaNome.put(23, "DEPENDENTE DE TRAB. DO COMERCIO PCG");
        mapCodigoParaNome.put(24, "PUBLICO EM GERAL PCG");
        mapCodigoParaNome.put(26, "DEPENDENTE DE TRAB. FUNCIONARIO PCG");
        mapCodigoParaNome.put(1, "TRAB. DO COMERCIO DE BENS, SERVIÇOS E TURISMO");
        mapCodigoParaNome.put(2, "DEPENDENTE DE TRAB. DO COMERCIO");
        mapCodigoParaNome.put(6, "PUBLICO EM GERAL");

        return mapCodigoParaNome.get(cdcategori);
    }

    private String formatarCampoHabilitacaoApiSesc(int cduop, int sqmatric, int nudv) {
    /* ---------------------------------------------------------------
     'HABILITAÇÃO' é formado pelos seguintes campos da API: "cduop" + "sqmatric" + "nudv"
     **cduop - preencher com zeros a esquerda até o tamanho 4
     **sqmatric - preencher com zeros à esquerda até o tamanho 6
     **nudv - usar sem formatacao especial
     O campo é composto pela concatenacao destes 3 campos.
     -----------------------------------------------------------------
     */

        final String cduopFormatado = String.format("%04d", cduop);
        final String sqmatricFormatado = String.format("%06d", sqmatric);
        final String nudvFormatado = Integer.toString(nudv);

        return cduopFormatado + sqmatricFormatado + nudvFormatado;
    }

    String formatarCampoSituacaoHabilitacaoApiSescGo(Integer stmatric) {
        // Nome do campo "stmatric", 0 = Normal, (1,2,3,4,5,6,7, outros) = Não Habilitado
        if (stmatric == null) {
            return null;
        }
        if (stmatric == 0) {
            return "Normal";
        } else {
            return "Não Habilitado";
        }
    }

    String formatarCampoHabilitacaoTitularApiSescGo(JSONObject json) {
        // campos "cduotitul" + "sqtitulmat" **Os campos são concataneados o UO + Matricula
        String cduotitul = json.optString("cduotitul", null);
        String sqtitulmat = json.optString("sqtitulmat", null);

        if (cduotitul == null || sqtitulmat == null) {
            return null;
        }

        return cduotitul + sqtitulmat;
    }

    String formatarCampoEstadoCivilApiSescGo(Integer dsestcivil) {
        if (dsestcivil == null) {
            return null;
        }
        switch (dsestcivil) {
            case 0:
                return "Solteiro";
            case 1:
                return "Casado";
            case 2:
                return "Viuvo";
            case 3:
                return "Divorciado";
            case 4:
                return "Separado";
            case 5:
                return "UniaoEstavel";
            default:
                return null;
        }
    }

    String formatarCampoParentescoApiSescGo(Integer dsparentsc) {
        if (dsparentsc == null) {
            return null;
        }
        switch (dsparentsc) {
            case 1:
                return "conjuge";
            case 2:
                return "viuvo";
            case 3:
                return "filho";
            case 4:
                return "neto";
            case 5:
                return "Enteado";
            case 6:
                return "pessoaSobGuarda";
            case 7:
                return "pai";
            case 8:
                return "mae";
            case 9:
                return "padrasto";
            case 10:
                return "madrasta";
            case 11:
            case 12:
                return "avos";
            case 13:
                return "tutor";
            case 15:
                return "irmao";
            default:
                return null;
        }
    }

    String formatarCampoGrauEscolaridadeApiSescGo(Integer cdnivel) {
        if (cdnivel == null) {
            return null;
        }

        switch (cdnivel) {
            case 0:
                return "EnsinoFundamentalIncompleto";
            case 1:
                return "EnsinoFundamentalCompleto";
            case 2:
                return "EnsinoMedioIncompleto";
            case 3:
                return "EnsinoMedioCompleto";
            case 4:
                return "EnsinoSuperiorIncompleto";
            case 5:
                return "EnsinoSuperiorCompleto";
            case 6:
                return "PosGraduacao";
            case 7:
                return "Mestrado";
            case 8:
                return "Doutorado";
            default:
                return null;
        }
    }

    IdentificacaoSescTO montarObjIdentificacaoApiSescGo(JSONObject json) {
        IdentificacaoSescTO identificacao = new IdentificacaoSescTO();
        identificacao.setCpf(json.optString("nucpf"));
        if (!UteisValidacao.emptyString(json.optString("nureggeral"))) {
            identificacao.setTipoDocumentoIdentidade("RG");
            identificacao.setNumeroDocumentoIdentidade(json.optString("nureggeral"));
            identificacao.setOrgaoEmissor(json.optString("idorgemirg"));
            identificacao.setDataEmissao(json.optString("dtemirg"));
        }
        return identificacao;
    }

    ContatoSescTO montarObjContatoApiSescGo(JSONObject json) {
        ContatoSescTO contato = new ContatoSescTO();
        contato.setDDDTelefoneCelular(json.optString("dddCelular"));
        contato.setNumeroTelefoneCelular(json.optString("celular"));
        contato.setDDDTelefoneFixo(json.optString("dddTelefone"));
        contato.setNumeroTelefoneFixo(json.optString("telefone"));
        contato.setEmail(json.optString("email"));
        return contato;
    }

    public String getCategoriaCliente() {
        return CategoriaCliente;
    }

    public void setCategoriaCliente(String categoriaCliente) {
        CategoriaCliente = categoriaCliente;
    }

    public String getHabilitacao() {
        return Habilitacao;
    }

    public void setHabilitacao(String habilitacao) {
        Habilitacao = habilitacao;
    }

    public Date getDataPrimeiraHabilitacao() {
        return DataPrimeiraHabilitacao;
    }

    public void setDataPrimeiraHabilitacao(Date dataPrimeiraHabilitacao) {
        DataPrimeiraHabilitacao = dataPrimeiraHabilitacao;
    }

    public String getSituacaoHabilitacao() {
        return SituacaoHabilitacao;
    }

    public void setSituacaoHabilitacao(String situacaoHabilitacao) {
        SituacaoHabilitacao = situacaoHabilitacao;
    }

    public String getHabilitacaoTitular() {
        return HabilitacaoTitular;
    }

    public void setHabilitacaoTitular(String habilitacaoTitular) {
        HabilitacaoTitular = habilitacaoTitular;
    }

    public String getParentesco() {
        return Parentesco;
    }

    public void setParentesco(String parentesco) {
        Parentesco = parentesco;
    }

    public String getNome() {
        return Nome;
    }

    public void setNome(String nome) {
        Nome = nome;
    }

    public String getNomeSocial() {
        return NomeSocial;
    }

    public void setNomeSocial(String nomeSocial) {
        NomeSocial = nomeSocial;
    }

    public Date getDataNascimento() {
        return DataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        DataNascimento = dataNascimento;
    }

    public String getSexo() {
        return Sexo;
    }

    public void setSexo(String sexo) {
        Sexo = sexo;
    }

    public String getEstadoCivil() {
        return EstadoCivil;
    }

    public void setEstadoCivil(String estadoCivil) {
        EstadoCivil = estadoCivil;
    }

    public String getGrauEscolaridade() {
        return GrauEscolaridade;
    }

    public void setGrauEscolaridade(String grauEscolaridade) {
        GrauEscolaridade = grauEscolaridade;
    }

    public String getNomePai() {
        return NomePai;
    }

    public void setNomePai(String nomePai) {
        NomePai = nomePai;
    }

    public String getNomeMae() {
        return NomeMae;
    }

    public void setNomeMae(String nomeMae) {
        NomeMae = nomeMae;
    }

    public String getNacionalidade() {
        return Nacionalidade;
    }

    public void setNacionalidade(String nacionalidade) {
        Nacionalidade = nacionalidade;
    }

    public String getNaturalidade() {
        return Naturalidade;
    }

    public void setNaturalidade(String naturalidade) {
        Naturalidade = naturalidade;
    }

    public Double getValorRendaMensal() {
        return ValorRendaMensal;
    }

    public void setValorRendaMensal(Double valorRendaMensal) {
        ValorRendaMensal = valorRendaMensal;
    }

    public Double getValorRendaFamiliar() {
        return ValorRendaFamiliar;
    }

    public void setValorRendaFamiliar(Double valorRendaFamiliar) {
        ValorRendaFamiliar = valorRendaFamiliar;
    }

    public Date getDataVencimentoHabilitacaoSesc() {
        return DataVencimentoHabilitacaoSesc;
    }

    public void setDataVencimentoHabilitacaoSesc(Date dataVencimentoHabilitacaoSesc) {
        DataVencimentoHabilitacaoSesc = dataVencimentoHabilitacaoSesc;
    }

    public Integer getNumeroViaHabilitacaoAtiva() {
        return NumeroViaHabilitacaoAtiva;
    }

    public void setNumeroViaHabilitacaoAtiva(Integer numeroViaHabilitacaoAtiva) {
        NumeroViaHabilitacaoAtiva = numeroViaHabilitacaoAtiva;
    }

    public String getObservacoes() {
        return Observacoes;
    }

    public void setObservacoes(String observacoes) {
        Observacoes = observacoes;
    }

    public String getFoto() {
        return Foto;
    }

    public void setFoto(String foto) {
        Foto = foto;
    }

    public String getNomeResponsavel() {
        return NomeResponsavel;
    }

    public void setNomeResponsavel(String nomeResponsavel) {
        NomeResponsavel = nomeResponsavel;
    }

    public String getCpfResponsavel() {
        return CpfResponsavel;
    }

    public void setCpfResponsavel(String cpfResponsavel) {
        CpfResponsavel = cpfResponsavel;
    }

    public IdentificacaoSescTO getIdentificacao() {
        return Identificacao;
    }

    public void setIdentificacao(IdentificacaoSescTO identificacao) {
        Identificacao = identificacao;
    }

    public EnderecoSescTO getEndereco() {
        return Endereco;
    }

    public void setEndereco(EnderecoSescTO endereco) {
        Endereco = endereco;
    }

    public ContatoSescTO getContato() {
        return Contato;
    }

    public void setContato(ContatoSescTO contato) {
        Contato = contato;
    }

    public ClienteVO toClienteVO(PaisVO paisVO, EstadoVO estadoVO, CidadeVO cidadeVO,
                                 GrauInstrucaoVO grauInstrucaoVO, CategoriaVO categoriaVO) {
        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setDataCadastro(this.DataPrimeiraHabilitacao);
        pessoaVO.setNome(this.Nome);
        pessoaVO.setDataNasc(this.DataNascimento);
        pessoaVO.setNomePai(this.NomePai);
        pessoaVO.setNomeMae(this.NomeMae);
        pessoaVO.setCfp(this.getIdentificacao().getCpf());
        pessoaVO.setRg(this.getIdentificacao().getNumeroDocumentoIdentidade());
        pessoaVO.setRgOrgao(this.getIdentificacao().getOrgaoEmissor());
        pessoaVO.setEstadoCivil(this.EstadoCivil);
        pessoaVO.setNacionalidade(this.Nacionalidade);
        pessoaVO.setNaturalidade(this.Naturalidade);
        pessoaVO.setSexo(this.Sexo.substring(0, 1));
        pessoaVO.setCategoriaPessoa(TipoPessoa.FISICA);

        pessoaVO.setAdicionar(true);

        pessoaVO.setEstadoVO(estadoVO);
        pessoaVO.setCidade(cidadeVO);
        pessoaVO.setPais(paisVO);
        pessoaVO.setGrauInstrucao(grauInstrucaoVO);


        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setPessoa(pessoaVO);
        clienteVO.setCategoria(categoriaVO);

        clienteVO.setSituacao("VI");

        clienteVO.setOrigem("IMPORTACAO-SESC");
        clienteVO.setSesc(Boolean.TRUE);
        clienteVO.setRenda(getValorRendaMensal());
        clienteVO.setMatriculaSesc(getHabilitacao());
        clienteVO.setNomeSocial(getNomeSocial());
        clienteVO.setDataValidadeCarteirinha(getDataVencimentoHabilitacaoSesc());

        return clienteVO;
    }

    public EnderecoVO toEnderecoVO() {
        EnderecoVO enderecoVO = new EnderecoVO();
        enderecoVO.setCep(this.getEndereco().getCep());
        enderecoVO.setEndereco(this.getEndereco().getLogradouro());
        enderecoVO.setNumero(this.getEndereco().getNumeroImovel());
        enderecoVO.setComplemento(this.getEndereco().getComplementoNumeroImovel().trim());
        enderecoVO.setBairro(this.getEndereco().getBairro().trim());
        enderecoVO.setTipoEndereco("RE");
        return enderecoVO;
    }

    public TelefoneVO toTelefoneFixoVO() {
        TelefoneVO telefoneVO = new TelefoneVO();
        if (!UteisValidacao.emptyString(this.getContato().getDDDTelefoneFixo())) {

            String sb = "(" + this.getContato().getDDDTelefoneFixo().trim() + ")" +
                    Uteis.tirarCaracteres(this.getContato().getNumeroTelefoneFixo(), true);
            telefoneVO.setNumero(sb);
        }
        telefoneVO.setTipoTelefone(TipoTelefone.RESIDENCIAL.getCodigo());
        return telefoneVO;
    }

    public TelefoneVO toTelefoneCelularVO() {
        TelefoneVO telefoneVO = new TelefoneVO();
        if (!UteisValidacao.emptyString(this.getContato().getDDDTelefoneCelular())) {

            String telefoneCelular = Uteis.tirarCaracteres(this.getContato().getNumeroTelefoneCelular(), true);
            if (telefoneCelular.length() <= 8) {
                telefoneCelular = "9" + telefoneCelular;
            }

            String sb = "(" + this.getContato().getDDDTelefoneCelular().trim() + ")" + telefoneCelular;
            telefoneVO.setNumero(sb);
        }
        telefoneVO.setTipoTelefone(TipoTelefone.CELULAR.getCodigo());
        return telefoneVO;
    }

    public EmailVO toEmailVO() {
        EmailVO emailVO = new EmailVO();
        emailVO.setEmail(this.getContato().getEmail());
        return emailVO;
    }
}

class IdentificacaoSescTO {
    private String Cpf;
    private String TipoDocumentoIdentidade;
    private String NumeroDocumentoIdentidade;
    private String ComplementoDocumentoIdentidade;
    private String OrgaoEmissor;
    private String DataEmissao;

    IdentificacaoSescTO() {

    }

    IdentificacaoSescTO(JSONObject object) {
        this.Cpf = object.optString("Cpf");
        this.TipoDocumentoIdentidade = object.optString("TipoDocumentoIdentidade");
        this.NumeroDocumentoIdentidade = object.optString("NumeroDocumentoIdentidade");
        this.ComplementoDocumentoIdentidade = object.optString("ComplementoDocumentoIdentidade");
        this.OrgaoEmissor = object.optString("OrgaoEmissor");
        this.DataEmissao = object.optString("DataEmissao");
    }

    public String getCpf() {
        return Cpf;
    }

    public void setCpf(String cpf) {
        Cpf = cpf;
    }

    public String getTipoDocumentoIdentidade() {
        return TipoDocumentoIdentidade;
    }

    public void setTipoDocumentoIdentidade(String tipoDocumentoIdentidade) {
        TipoDocumentoIdentidade = tipoDocumentoIdentidade;
    }

    public String getNumeroDocumentoIdentidade() {
        return NumeroDocumentoIdentidade;
    }

    public void setNumeroDocumentoIdentidade(String numeroDocumentoIdentidade) {
        NumeroDocumentoIdentidade = numeroDocumentoIdentidade;
    }

    public String getComplementoDocumentoIdentidade() {
        return ComplementoDocumentoIdentidade;
    }

    public void setComplementoDocumentoIdentidade(String complementoDocumentoIdentidade) {
        ComplementoDocumentoIdentidade = complementoDocumentoIdentidade;
    }

    public String getOrgaoEmissor() {
        return OrgaoEmissor;
    }

    public void setOrgaoEmissor(String orgaoEmissor) {
        OrgaoEmissor = orgaoEmissor;
    }

    public String getDataEmissao() {
        return DataEmissao;
    }

    public void setDataEmissao(String dataEmissao) {
        DataEmissao = dataEmissao;
    }
}

class ContatoSescTO {
    private String DDDTelefoneFixo;
    private String NumeroTelefoneFixo;
    private String DDDTelefoneCelular;
    private String NumeroTelefoneCelular;
    private String Email;
    private Boolean AceitaReceberInformacoesDoSesc;

    public ContatoSescTO() {

    }

    public ContatoSescTO(JSONObject object) {
        this.DDDTelefoneFixo = object.optString("DDDTelefoneFixo");
        this.NumeroTelefoneFixo = object.optString("NumeroTelefoneFixo");
        this.DDDTelefoneCelular = object.optString("DDDTelefoneCelular").trim();
        this.NumeroTelefoneCelular = object.optString("NumeroTelefoneCelular").trim();
        this.Email = object.optString("Email").trim();
        this.AceitaReceberInformacoesDoSesc = object.optBoolean("AceitaReceberInformacoesDoSesc");
    }

    public String getDDDTelefoneFixo() {
        return DDDTelefoneFixo;
    }

    public void setDDDTelefoneFixo(String DDDTelefoneFixo) {
        this.DDDTelefoneFixo = DDDTelefoneFixo;
    }

    public String getNumeroTelefoneFixo() {
        return NumeroTelefoneFixo;
    }

    public void setNumeroTelefoneFixo(String numeroTelefoneFixo) {
        NumeroTelefoneFixo = numeroTelefoneFixo;
    }

    public String getDDDTelefoneCelular() {
        return DDDTelefoneCelular;
    }

    public void setDDDTelefoneCelular(String DDDTelefoneCelular) {
        this.DDDTelefoneCelular = DDDTelefoneCelular;
    }

    public String getNumeroTelefoneCelular() {
        return NumeroTelefoneCelular;
    }

    public void setNumeroTelefoneCelular(String numeroTelefoneCelular) {
        NumeroTelefoneCelular = numeroTelefoneCelular;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String email) {
        Email = email;
    }

    public Boolean getAceitaReceberInformacoesDoSesc() {
        return AceitaReceberInformacoesDoSesc;
    }

    public void setAceitaReceberInformacoesDoSesc(Boolean aceitaReceberInformacoesDoSesc) {
        AceitaReceberInformacoesDoSesc = aceitaReceberInformacoesDoSesc;
    }
}

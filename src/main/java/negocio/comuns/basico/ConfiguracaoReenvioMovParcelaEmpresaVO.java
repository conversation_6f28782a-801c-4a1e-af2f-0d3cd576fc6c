package negocio.comuns.basico;

import annotations.arquitetura.FKJson;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.ContaCorrenteVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.utilitarias.ConsistirException;

public class ConfiguracaoReenvioMovParcelaEmpresaVO extends SuperVO {

    private Integer codigo;
    private EmpresaVO empresaVO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private Integer tentativasRealizar;
    private Integer posicao;


    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public Integer getTentativasRealizar() {
        if (tentativasRealizar == null) {
            tentativasRealizar = 0;
        }
        return tentativasRealizar;
    }

    public void setTentativasRealizar(Integer tentativasRealizar) {
        this.tentativasRealizar = tentativasRealizar;
    }

    public Integer getPosicao() {
        if (posicao == null) {
            posicao = 1;
        }
        return posicao;
    }

    public void setPosicao(Integer posicao) {
        this.posicao = posicao;
    }

    public String getTipoConvenio() {
        return getConvenioCobrancaVO().getTipo().getTipoCobranca().getDescricao();
    }
}

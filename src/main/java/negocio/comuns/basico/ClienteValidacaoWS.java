package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

/**
 * Created by ulisses on 12/06/2016.
 */
public class ClienteValidacaoWS extends SuperTO {

    private int codigo;
    private String nome = "";
    private String cpf = "";
    private String vencimentoMaisAntigo ="";
    private String situacaoCadastralCliente= "";
    private String nomeUnidade ="";
    private String situacaoDW="";

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getVencimentoMaisAntigo() {
        return vencimentoMaisAntigo;
    }

    public void setVencimentoMaisAntigo(String vencimentoMaisAntigo) {
        this.vencimentoMaisAntigo = vencimentoMaisAntigo;
    }

    public String getSituacaoCadastralCliente() {
        return situacaoCadastralCliente;
    }

    public void setSituacaoCadastralCliente(String situacaoCadastralCliente) {
        this.situacaoCadastralCliente = situacaoCadastralCliente;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getSituacaoDW() {
        return situacaoDW;
    }

    public void setSituacaoDW(String situacaoDW) {
        this.situacaoDW = situacaoDW;
    }
}

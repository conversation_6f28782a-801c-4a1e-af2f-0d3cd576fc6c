package negocio.comuns.basico;

import java.util.List;

public class FamiliaTO {

    private Integer codigoSugestao;
    private String nome;
    private String causa;

    private List<FamiliarTO> integrantes;

    public String toString() {
        try {
            String s = "";
            for (FamiliarTO f : integrantes) {
                s += " - " + f.getNome();
            }
            return s.replaceFirst(" - ", "");
        } catch (Exception e) {
            return " - ";
        }
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<FamiliarTO> getIntegrantes() {
        return integrantes;
    }

    public void setIntegrantes(List<FamiliarTO> integrantes) {
        this.integrantes = integrantes;
    }

    public String getCausa() {
        return causa;
    }

    public void setCausa(String causa) {
        this.causa = causa;
    }

    public Integer getCodigoSugestao() {
        return codigoSugestao;
    }

    public void setCodigoSugestao(Integer codigoSugestao) {
        this.codigoSugestao = codigoSugestao;
    }
}

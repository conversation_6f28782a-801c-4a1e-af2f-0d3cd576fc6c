/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico.xmlgen;

import java.util.List;

import negocio.comuns.xmlbase.ArquivoXML;
import negocio.comuns.xmlbase.BaseXML;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.ecf.cupomfiscal.comuns.to.CupomFiscalFormasPagamentoTO;

/**
 * Representação XML da classe de Formas de pagamento
 * 
 * <AUTHOR>
 * 
 */
public class CupomFiscalFormaPagamentoXML extends BaseXML {

	@SuppressWarnings("unchecked")
	@Override
	public ArquivoXML adicionarDadosNoArquivo(List listaCuponsFormasPagamento, boolean comAutorizacaoCobranca) {
		for (int i = 0; i < listaCuponsFormasPagamento.size(); i++) {
			CupomFiscalFormasPagamentoTO fpTO = (CupomFiscalFormasPagamentoTO) listaCuponsFormasPagamento
					.get(i);
			addArquivoXML("Codigo", "7", String.class, String.valueOf(fpTO
					.getCodigo()), i, arquivoXML);
			addArquivoXML("CupomFiscal", "7", String.class, String.valueOf(fpTO
					.getCupomfiscal()), i, arquivoXML);
			addArquivoXML("FormaPagamento", "5", String.class, fpTO
					.getFormaPagamento(), i, arquivoXML);
			addArquivoXML("Valor", "8", String.class, Formatador
					.formatarValorMonetarioSemMoeda(fpTO.getValor()), i,
					arquivoXML);

		}
		return arquivoXML;
	}

}

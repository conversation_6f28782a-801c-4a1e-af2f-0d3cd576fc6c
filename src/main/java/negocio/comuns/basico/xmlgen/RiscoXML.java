/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico.xmlgen;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.RiscoVO;
import negocio.comuns.xmlbase.ArquivoXML;
import negocio.comuns.xmlbase.BaseXML;

/**
 *
 * <AUTHOR>
 * Data: 30/11/10
 * Objetivo da classe: Gerar um arquivo XML através de uma lista de grupo de risco.
 */
public class RiscoXML extends BaseXML {

    @Override
    public  ArquivoXML adicionarDadosNoArquivo(List listaRisco, boolean comAutorizacaoCobranca) {
       SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
       String dia;
        for (int i = 0; i < listaRisco.size(); i++) {
            RiscoVO riscoVo = (RiscoVO) listaRisco.get(i);
            dia = "null";
            if (riscoVo.getDia() != null)
              dia = sf.format(riscoVo.getDia());
            addArquivoXML("nomeCliente", "50", String.class, riscoVo.getNomeCliente(), i, arquivoXML);
            addArquivoXML("matriculaCliente", "100", String.class, riscoVo.getMatriculaCliente(), i, arquivoXML);
            addArquivoXML("dia", "", Date.class, dia, i, arquivoXML);
            addArquivoXML("peso", "", Integer.class, riscoVo.getPeso().toString(), i, arquivoXML);
            String faixaDeRisco;
            String obsRisco;
            if (riscoVo.getPeso() == 3){
              faixaDeRisco = "ÓTIMO";
              obsRisco = "Totalmente fora do grupo de Risco";
            }else if ((riscoVo.getPeso() == 4) || (riscoVo.getPeso() == 5)){
              faixaDeRisco = "NORMAL";
              obsRisco = "Cliente normal não esta em Grupo de risco.";
            } else if (riscoVo.getPeso() == 6) {
              faixaDeRisco = "RISCO BAIXO";
              obsRisco = "Necessita trabalho pro-ativo.";
            } else if (riscoVo.getPeso() == 7) {
              faixaDeRisco = "RISCO ALTO";
              obsRisco = "Necessita trabalho de motivação para o cliente.";
            } else if (riscoVo.getPeso() == 8) {
              faixaDeRisco = "RISCO MUITO ALTO";
              obsRisco = "Cliente praticamente perdido, trabalho de recuperação.";
            }else{
              faixaDeRisco = "NÃO DEFINIDA";
              obsRisco = "Risco não tratado.";
            }
            addArquivoXML("faixaDeRisco", "30", String.class, faixaDeRisco, i, arquivoXML);
            addArquivoXML("obsRisco", "100", String.class, obsRisco, i, arquivoXML);
        }
        return arquivoXML;
    }
}

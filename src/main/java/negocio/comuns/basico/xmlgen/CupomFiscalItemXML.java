/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico.xmlgen;

import java.util.List;

import negocio.comuns.xmlbase.ArquivoXML;
import negocio.comuns.xmlbase.BaseXML;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.ecf.cupomfiscal.comuns.to.CupomFiscalItensTO;

/**
 * Representação XML da classe de Ítem do Cupom Fiscal
 * 
 * <AUTHOR>
 * 
 */
public class CupomFiscalItemXML extends BaseXML {

	@SuppressWarnings("unchecked")
	@Override
	public ArquivoXML adicionarDadosNoArquivo(List listaCuponsItens, boolean comAutorizacaoCobranca) {
		for (int i = 0; i < listaCuponsItens.size(); i++) {
			CupomFiscalItensTO itemTO = (CupomFiscalItensTO) listaCuponsItens.get(i);
			addArquivoXML("Codigo", "7", String.class, String.valueOf(itemTO
					.getCodigo()), i, arquivoXML);
			addArquivoXML("Produto", "5", String.class, String.valueOf(itemTO
					.getProduto()), i, arquivoXML);
			addArquivoXML("CupomFiscal", "7", String.class, String.valueOf(itemTO
					.getCupomfiscal()), i, arquivoXML);
			addArquivoXML("Quantidade", "3", String.class, String.valueOf(itemTO
					.getQuantidade()), i, arquivoXML);
			addArquivoXML("Descricao", "150", String.class, String
					.valueOf(itemTO.getDescricao()), i, arquivoXML);
			addArquivoXML("Valorunitario", "14", String.class, Formatador
					.formatarValorMonetarioSemMoeda(itemTO
					.getValorunitario()), i, arquivoXML);
			addArquivoXML("Valortotal", "14", String.class, Formatador
					.formatarValorMonetarioSemMoeda(itemTO.getValortotal()), i,
					arquivoXML);
			addArquivoXML("Tributacao", "5", String.class, itemTO
					.getAliquotaDesc(), i, arquivoXML);
                        addArquivoXML("ValorDescontoOuAcrescimo", "14", String.class, Formatador
					.formatarValorMonetarioSemMoeda(itemTO.getValorDescontoOuAcrescimo()), i,
					arquivoXML);
            addArquivoXML("ncm", "10", String.class, String
            				.valueOf(itemTO.getNcm()), i, arquivoXML);
//			addArquivoXML("Tributacao", "5", String.class, String.valueOf(itemTO
//			.getTributacao()), i, arquivoXML);

		}
		return arquivoXML;
	}

}

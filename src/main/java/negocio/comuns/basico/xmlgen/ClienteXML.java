/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico.xmlgen;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.xmlbase.ArquivoXML;
import negocio.comuns.xmlbase.BaseXML;
/**
 *
 * <AUTHOR>
 * Data: 30/11/10
 * Objetivo da classe: Gerar um arquivo XML através de uma lista de clientes.
 */
public class ClienteXML extends BaseXML {


    @Override
    public ArquivoXML adicionarDadosNoArquivo(List listaClientes, boolean comAutorizacaoCobranca) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        String dataNasc;
        for (int i = 0; i < listaClientes.size(); i++) {
            ClienteVO clienteVo = (ClienteVO) listaClientes.get(i);
            dataNasc = "null";
            if (clienteVo.getPessoa().getDataNasc() != null)
                dataNasc = sf.format(clienteVo.getPessoa().getDataNasc());
            addArquivoXML("CodigoMatricula", "", Integer.class, clienteVo.getCodigoMatricula().toString(), i, arquivoXML);
            addArquivoXML("Nome", "50", String.class, clienteVo.getPessoa().getNome(), i, arquivoXML);
            addArquivoXML("TelefoneRes", "15", String.class, retornarTelefoneCliente(clienteVo, "RE"), i, arquivoXML);
            addArquivoXML("TelefoneCom", "15", String.class, retornarTelefoneCliente(clienteVo, "CO"), i, arquivoXML);
            addArquivoXML("TelefoneCel", "15", String.class, retornarTelefoneCliente(clienteVo, "CE"), i, arquivoXML);
            addArquivoXML("Email", "50", String.class, retornarEmailCliente(clienteVo), i, arquivoXML);
            addArquivoXML("Sexo", "2", String.class, clienteVo.getPessoa().getSexo(), i, arquivoXML);
            addArquivoXML("EstadoCivil", "10", String.class, clienteVo.getPessoa().getEstadoCivil(), i, arquivoXML);
            addArquivoXML("DataNasc", "10", Date.class, dataNasc, i, arquivoXML);
            addArquivoXML("CodAcesso", "12", String.class, clienteVo.getCodAcesso(), i, arquivoXML);
            addArquivoXML("MatriculaExterna", "", Integer.class, clienteVo.getMatriculaExterna().toString(), i, arquivoXML);
            addArquivoXML("UrlFotoCliente", "255", String.class, clienteVo.getPessoa().getUrlFoto(), i, arquivoXML);

            if (comAutorizacaoCobranca) {
                for (AutorizacaoCobrancaClienteVO auto : clienteVo.getAutorizacoes()) {
                    String infoAuto = "";
                    if (auto.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                        infoAuto = auto.getCartaoMascarado_Apresentar();
                    } else if (auto.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
                        infoAuto = auto.getContaCorrenteApresentar();
                    } else if (auto.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)) {
                        continue;
                    }
                    addArquivoXML("Autorizacao" + auto.getCodigo(), "50", String.class, infoAuto, i, arquivoXML);
                }
            }
        }
        return arquivoXML;
    }

/*
     * Objetivo do Método: Retornar somente um telefone do cliente.
     */
    private String retornarTelefoneCliente(ClienteVO clienteVO, String tipoTelefone){
        String telefone= "";
        if (clienteVO.getPessoa().getTelefoneVOs() == null)
           return telefone;
        for (TelefoneVO  telefoneVO :clienteVO.getPessoa().getTelefoneVOs()){
          if (telefoneVO.getTipoTelefone().equals(tipoTelefone)){
             telefone = telefoneVO.getNumero();
             break;
          }
        }
        return telefone;
    }

    /*
     * Objetivo do Método: Retornar somente um email do cliente.
     */
    private String retornarEmailCliente(ClienteVO clienteVO){
        String email = "";
        if (clienteVO.getPessoa().getEmailVOs() == null)
           return email;
        if (clienteVO.getPessoa().getEmailVOs().size() > 0){
          EmailVO emailVO = (EmailVO)clienteVO.getPessoa().getEmailVOs().get(0);
          email = emailVO.getEmail();
        }
        return email;
    }


}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 */
public class HistoricoPontosVO extends SuperVO{
    
    @ChavePrimaria
    private Integer codigo;
    private String descricao;
    private Integer pontos;
    private Integer pontostotal;
    private ClienteVO cliente;
    private Date dataaula;
    private Date dataConfirmacao;
    private Boolean entrada;
    private TipoItemCampanhaEnum tipoPonto;
    private BrindeVO brinde;
    private Integer codigoCampanha;
    @NaoControlarLogAlteracao
    private Integer codigoUltimoRegistro;
    private Integer contrato;
    private Integer produto;
    private Integer codigoVenda;
    private String nomeEmpresa;
    private String observacao;

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getPontos() {
        if (pontos == null) {
            pontos = 0;
        }
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Integer getPontostotal() {
        if (pontostotal == null) {
            pontostotal = 0;
        }
        return pontostotal;
    }

    public void setPontostotal(Integer pontostotal) {
        this.pontostotal = pontostotal;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public Date getDataaula() {
        return dataaula;
    }

    public void setDataaula(Date dataaula) {
        this.dataaula = dataaula;
    }

    public Date getDataConfirmacao() {
        return dataConfirmacao;
    }

    public void setDataConfirmacao(Date dataConfirmacao) {
        this.dataConfirmacao = dataConfirmacao;
    }

    public Boolean getEntrada() {
        if (entrada == null) {
            entrada = true;
        }
        return entrada;
    }

    public void setEntrada(Boolean entrada) {
        this.entrada = entrada;
    }

    public TipoItemCampanhaEnum getTipoPonto() {
        return tipoPonto;
    }

    public void setTipoPonto(TipoItemCampanhaEnum tipoPonto) {
        this.tipoPonto = tipoPonto;
    }

    public BrindeVO getBrinde() {
        if (brinde == null) {
            brinde = new BrindeVO();
        }
        return brinde;
    }

    public void setBrinde(BrindeVO brinde) {
        this.brinde = brinde;
    }
    
    public String getMatricula_Apresentar(){
        if (cliente != null) {
            return cliente.getMatricula();
        }
        return "";
    }
    
    public String getNome_Apresentar(){
        if (cliente != null) {
            if (cliente.getPessoa() != null) {
                return cliente.getPessoa().getNome();
            }
        }
        return "";
    }
    
    public String getEntrada_Apresentar(){
        if (entrada) {
            return "Entrada";
        }else{
            return "Saida";
        }
    }
    
    public String getDataConfirmacao_Apresentar(){
        SimpleDateFormat smf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        return smf.format(dataConfirmacao);
    }
    
    public String getDataAula_Apresentar(){
       SimpleDateFormat smf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
       return smf.format(dataaula);
    }
    
    public String getBrinde_Apresentar(){
        if (brinde != null) {
            return brinde.getNome();
        }
        return "";
    }
    
    public String getTelefone_Apresentar(){
        return ClienteVO.obterTelefoneCliente(getCliente());
    }

    public Integer getCodigoUltimoRegistro() {
        if (codigoUltimoRegistro == null) {
            codigoUltimoRegistro = 0;
        }
        return codigoUltimoRegistro;
    }

    public void setCodigoUltimoRegistro(Integer codigoUltimoRegistro) {
        this.codigoUltimoRegistro = codigoUltimoRegistro;
    }
    
    public String getDescricao_Apresentar(){
        if (!getBrinde_Apresentar().isEmpty()) {
            return getBrinde_Apresentar();
        }else{
            return descricao;
        }
    }

    public Integer getContrato() {
        if (contrato == null) {
            contrato = 0;
        }
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getProduto() {
        if (produto == null) {
            produto = 0;
        }
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public Integer getCodigoVenda() {
        if (codigoVenda == null) {
            codigoVenda = 0;
        }
        return codigoVenda;
    }

    public void setCodigoVenda(Integer codigoVenda) {
        this.codigoVenda = codigoVenda;
    }

    public String getNomeEmpresa() {
        if (nomeEmpresa == null) {
            nomeEmpresa = "";
        }
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getCodigoCampanha() {
        return codigoCampanha;
    }

    public void setCodigoCampanha(Integer codigoCampanha) {
        this.codigoCampanha = codigoCampanha;
    }

    public JRDataSource getHistoricoPontosDatasource() {
        List<HistoricoPontosVO> historicoPontosVOS = new ArrayList<>();
        historicoPontosVOS.add(this);
        return new JRBeanCollectionDataSource(historicoPontosVOS);
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}

package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.Date;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class ProcessoImportacaoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer sequencialImportacao;
    private String descricao;
    private TipoImportacaoEnum tipoImportacaoEnum;
    private EmpresaVO empresaVO;
    private UsuarioVO usuarioVO;
    private String configuracao;
    private String status;
    private Date dataInicio;
    private Date dataFim;

    public ProcessoImportacaoVO() {
    }

    public static void validarDados(ProcessoImportacaoVO obj) throws ConsistirException {
        if (obj.getValidarDados()) {
//            if (UteisValidacao.emptyString(obj.getNomeTabela())) {
//                throw new ConsistirException("O campo NOME (TabelaParceiroFidelidade) deve ser informado.");
//            }
//            if (UteisValidacao.emptyList(obj.getItens())) {
//                throw new ConsistirException("O campo ITENS (TabelaParceiroFidelidade) deve ser informado.");
//            }
        }
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getSequencialImportacao() {
        if (sequencialImportacao == null) {
            sequencialImportacao = 0;
        }
        return sequencialImportacao;
    }

    public void setSequencialImportacao(Integer sequencialImportacao) {
        this.sequencialImportacao = sequencialImportacao;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public String getConfiguracao() {
        if (configuracao == null) {
            configuracao = "";
        }
        return configuracao;
    }

    public void setConfiguracao(String configuracao) {
        this.configuracao = configuracao;
    }

    public String getDataInicioApresentar() {
        if (getDataInicio() == null) {
            return "";
        } else {
            return Uteis.getDataComHora(getDataInicio());
        }
    }

    public String getDataFimApresentar() {
        if (getDataFim() == null) {
            return "";
        } else {
            return Uteis.getDataComHora(getDataFim());
        }
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getStatus() {
        if (status == null) {
            status = "";
        }
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTotal() {
        try {
            JSONObject json = new JSONObject(getStatus());
            return json.optInt("total");
        } catch (Exception ex) {
            return 0;
        }
    }

    public Integer getSucesso() {
        try {
            JSONObject json = new JSONObject(getStatus());
            return json.optInt("sucesso");
        } catch (Exception ex) {
            return 0;
        }
    }

    public Integer getFalha() {
        try {
            JSONObject json = new JSONObject(getStatus());
            return json.optInt("falha");
        } catch (Exception ex) {
            return 0;
        }
    }

    public Integer getAtual() {
        try {
            JSONObject json = new JSONObject(getStatus());
            return json.optInt("atual");
        } catch (Exception ex) {
            return 0;
        }
    }

    public boolean isParado() {
        try {
            JSONObject json = new JSONObject(getStatus());
            return json.optBoolean("parado");
        } catch (Exception ex) {
            return false;
        }
    }

    public Double getPercentual() {
        try {
            return Uteis.arredondarForcando2CasasDecimais((100 * getAtual().doubleValue()) / getTotal().doubleValue());
        } catch (Exception ex) {
            return 0.0;
        }
    }

    public TipoImportacaoEnum getTipoImportacaoEnum() {
        return tipoImportacaoEnum;
    }

    public void setTipoImportacaoEnum(TipoImportacaoEnum tipoImportacaoEnum) {
        this.tipoImportacaoEnum = tipoImportacaoEnum;
    }

    public void atualizarJSONStatus(Integer atual, Integer sucesso, Integer falha, Integer total) {
        if (UteisValidacao.emptyString(getStatus())) {
            setStatus(new JSONObject().toString());
        }

        JSONObject status = new JSONObject(getStatus());
        if (!UteisValidacao.emptyNumber(atual)) {
            status.put("atual", atual);
        }
        if (!UteisValidacao.emptyNumber(sucesso)) {
            status.put("sucesso", sucesso);
        }
        if (!UteisValidacao.emptyNumber(falha)) {
            status.put("falha", falha);
        }
        if (!UteisValidacao.emptyNumber(total)) {
            status.put("total", total);
        }
        setStatus(status.toString());
    }
}

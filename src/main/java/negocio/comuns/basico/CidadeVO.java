package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade Cidade. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class CidadeVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo = new Integer(0);
    protected String nome = "";
    @NaoControlarLogAlteracao
    protected String nomeSemAcento = "";
    @ChaveEstrangeira
    @FKJson
    protected EstadoVO estado = new EstadoVO();
    /** Atributo responsável por manter o objeto relacionado da classe <code>Pais </code>.*/
    @ChaveEstrangeira
    @FKJson
    protected PaisVO pais = new PaisVO();
    private String codigoMunicipio = ""; //codigoIbgeCidade
    private boolean homologada = false; //homologada eNOTAS

    /**
     * Construtor padrão da classe <code>Cidade</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public CidadeVO() {
        super();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>CidadeVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(CidadeVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getNome().equals("")) {
            throw new ConsistirException("O campo NOME (Cidade) deve ser informado.");
        }
        if ((obj.getPais() == null)
                || (obj.getPais().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo PAIS (Cidade) deve ser informado.");
        }
        if (obj.getEstado() == null || obj.getEstado().getCodigo().intValue() == 0) {
            throw new ConsistirException("O campo ESTADO (Cidade) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setNome(getNome().toUpperCase());
    }

    /**
     * Retorna o objeto da classe <code>Pais</code> relacionado com (<code>Cidade</code>).
     */
    public PaisVO getPais() {
        if (pais == null) {
            pais = new PaisVO();
        }
        return (pais);
    }

    public void setEstado(EstadoVO obj) {
        this.estado = obj;
    }

    public EstadoVO getEstado() {
        if (estado == null) {
            estado = new EstadoVO();
        }
        return estado;
    }

    public String getEstado_Apresentar(){
        return getEstado().getDescricao();
    }
    /**
     * Define o objeto da classe <code>Pais</code> relacionado com (<code>Cidade</code>).
     */
    public void setPais(PaisVO obj) {
        this.pais = obj;
    }

    public String getPais_Apresentar(){
        return getPais().getNome();
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico. 
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o 
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc. 
     */
    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return (nome);
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeSemAcento() {
        if (nomeSemAcento == null) {
            nomeSemAcento = "";
        }
        return nomeSemAcento;
    }

    public void setNomeSemAcento(String nomeSemAcento) {
        this.nomeSemAcento = nomeSemAcento;
    }

    public CidadeWS toWS() {
        CidadeWS cidadeWS = new CidadeWS();
        cidadeWS.setCodigo(this.codigo);
        cidadeWS.setNome(this.nome);
        if (this.estado.getCodigo() != null)
          cidadeWS.setCodigoEstado(this.estado.getCodigo());
        return  cidadeWS;
    }

    public String getCodigoMunicipio() {
        if (codigoMunicipio == null) {
            codigoMunicipio = "";
        }
        return codigoMunicipio;
    }

    public void setCodigoMunicipio(String codigoMunicipio) {
        this.codigoMunicipio = codigoMunicipio;
    }

    public String getCodigoIBGE() {
        return getCodigoMunicipio();
    }

    public boolean isHomologada() {
        return homologada;
    }

    public void setHomologada(boolean homologada) {
        this.homologada = homologada;
    }
}

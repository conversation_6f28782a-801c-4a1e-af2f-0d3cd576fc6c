package negocio.comuns.basico;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * Reponsável por manter os dados da entidade HistoricoVinculo. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */

public class HistoricoVinculoVO extends SuperVO {

    protected Integer codigo;
    protected Date dataRegistro;
    protected String tipoHistoricoVinculo;
    protected String tipoColaborador;
    protected ClienteVO cliente;
    protected ColaboradorVO colaborador;
    private String origem;

    /**
     * Construtor padrão da classe <code>HistoricoVinculo</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public HistoricoVinculoVO() {
        super();
        inicializarDados();
    }

    /**
     * Construtor padrão da classe <code>HistoricoVinculo</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public HistoricoVinculoVO(Integer cliente, Integer colaborador, String tipoHistoricoVinculo, String tipoColaborador, String origem, UsuarioVO usuarioVO) {
        setDataRegistro(Calendario.hoje());
        setTipoHistoricoVinculo(tipoHistoricoVinculo);
        setTipoColaborador(tipoColaborador);
        getCliente().setCodigo(cliente);
        getColaborador().setCodigo(colaborador);
        setOrigem(origem);
        setUsuarioVO(usuarioVO);
    }

    public HistoricoVinculoVO(Integer cliente, Integer colaborador, String tipoHistoricoVinculo, String tipoColaborador, Date dataRegistro, String origem, UsuarioVO usuarioVO) {
        setTipoHistoricoVinculo(tipoHistoricoVinculo);
        setTipoColaborador(tipoColaborador);
        setDataRegistro(dataRegistro);
        getCliente().setCodigo(cliente);
        getColaborador().setCodigo(colaborador);
        setOrigem(origem);
        setUsuarioVO(usuarioVO);
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>HistoricoVinculoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(HistoricoVinculoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if ((obj.getCliente() == null) ||
                (obj.getCliente().getCodigo() == 0)) {
            throw new ConsistirException("O cliente deve ser informado.");
        }
        if (obj.getDataRegistro() == null) {
            throw new ConsistirException("A data de registro deve ser informado");
        }
        if (obj.getTipoColaborador() == null || obj.getTipoColaborador().trim().equals("")) {
            throw new ConsistirException("O tipo colaborador deve ser informado.");
        }
        if ((obj.getColaborador() == null) ||
                (obj.getColaborador().getCodigo() == 0)) {
            throw new ConsistirException("O colaborador deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
        setTipoHistoricoVinculo(getTipoHistoricoVinculo().toUpperCase());
        setTipoColaborador(getTipoColaborador().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(null);
        setDataRegistro(Calendario.hoje());
        setTipoHistoricoVinculo("");
        setTipoColaborador("");
        setCliente(new ClienteVO());
        setColaborador(new ColaboradorVO());
    }

    /**
     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (<code>HistoricoVinculo</code>).
     */
    public ColaboradorVO getColaborador() {
        if (colaborador == null) {
            colaborador = new ColaboradorVO();
        }
        return (colaborador);
    }

    /**
     * Define o objeto da classe <code>Colaborador</code> relacionado com (<code>HistoricoVinculo</code>).
     */
    public void setColaborador(ColaboradorVO obj) {
        this.colaborador = obj;
    }

    /**
     * Retorna o objeto da classe <code>Cliente</code> relacionado com (<code>HistoricoVinculo</code>).
     */
    public ClienteVO getCliente() {
        if (cliente == null) {
            cliente = new ClienteVO();
        }
        return (cliente);
    }

    /**
     * Define o objeto da classe <code>Cliente</code> relacionado com (<code>HistoricoVinculo</code>).
     */
    public void setCliente(ClienteVO obj) {
        this.cliente = obj;
    }

    public String getTipoColaborador() {
        if (tipoColaborador == null) {
            tipoColaborador = "";
        }
        return (tipoColaborador);
    }

    public void setTipoColaborador(String tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }

    public String getTipoColaborador_Apresentar() {
        if (tipoColaborador == null) {
            tipoColaborador = "";
        }

        TipoColaboradorEnum tipoColaboradorEnum = TipoColaboradorEnum.getTipo(tipoColaborador);
        if (tipoColaboradorEnum != null) {
            return tipoColaboradorEnum.getDescricao();
        }

        return  tipoColaborador;
    }

    public String getTipoHistoricoVinculo() {
        if (tipoHistoricoVinculo == null) {
            tipoHistoricoVinculo = "";
        }
        return (tipoHistoricoVinculo);
    }

    public void setTipoHistoricoVinculo(String tipoHistoricoVinculo) {
        this.tipoHistoricoVinculo = tipoHistoricoVinculo;
    }

    public String getTipoHistoricoVinculo_Apresentar() {
        if (tipoHistoricoVinculo == null) {
            tipoHistoricoVinculo = "";
        }
        if (tipoHistoricoVinculo.equals("EN")) {
            return "Entrada";
        }
        if (tipoHistoricoVinculo.equals("SD")) {
            return "Saída";
        }
        return (tipoHistoricoVinculo);
    }

    public Date getDataRegistro() {
        if (dataRegistro == null) {
            dataRegistro = Calendario.hoje();
        }
        return (dataRegistro);
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDataRegistro_Apresentar() {
        return (Uteis.getDataComHora(dataRegistro));
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getOrigem() {
        if (origem == null) {
            origem = "";
        }
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public String getNomeUsuario() {
        if (getUsuarioVO() != null && getUsuarioVO().getCodigo() != 0) {
            return getUsuarioVO().getNomeAbreviado();
        }
        return "";
    }
}
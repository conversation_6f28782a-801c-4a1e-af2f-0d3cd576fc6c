package negocio.comuns.basico;
import annotations.arquitetura.FKJson;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * Reponsável por manter os dados da entidade QuestionarioPerguntaCliente. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see negocio.facade.jdbc.basico.QuestionarioCliente
*/
public class QuestionarioPerguntaClienteVO extends SuperVO {
	
    protected Integer codigo;
    protected Integer questionarioCliente;
    /** Atributo responsável por manter o objeto relacionado da classe <code>PerguntaCliente </code>.*/
    @FKJson
    protected PerguntaClienteVO perguntaCliente;
	
    /**
     * Construtor padrão da classe <code>QuestionarioPerguntaCliente</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public QuestionarioPerguntaClienteVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>QuestionarioPerguntaClienteVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(QuestionarioPerguntaClienteVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setPerguntaCliente( new PerguntaClienteVO() );
    }
	

    /**
     * Retorna o objeto da classe <code>PerguntaCliente</code> relacionado com (<code>QuestionarioPerguntaCliente</code>).
    */
    public PerguntaClienteVO getPerguntaCliente() {
        if (perguntaCliente == null) {
            perguntaCliente = new PerguntaClienteVO();
        }
        return (perguntaCliente);
    }
     
    /**
     * Define o objeto da classe <code>PerguntaCliente</code> relacionado com (<code>QuestionarioPerguntaCliente</code>).
    */
    public void setPerguntaCliente( PerguntaClienteVO obj) {
        this.perguntaCliente = obj;
    }

    public Integer getQuestionarioCliente() {
        return (questionarioCliente);
    }
     
    public void setQuestionarioCliente( Integer questionarioCliente ) {
        this.questionarioCliente = questionarioCliente;
    }

    public Integer getCodigo() {
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }
}
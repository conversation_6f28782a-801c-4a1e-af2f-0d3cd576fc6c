package negocio.comuns.basico;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;

public class MovPagamentoIntegracaoTO {

    private Integer codigo;
    private String nomePagador;
    private String produtosPagos;
    private String responsavelPagamento;
    private Double valor;
    private Date dataLancamento;
    private Integer formaPagamento;
    private String tipoFormaPagamento;
    private String descricaoFormaPagamento;
    private String autorizacaoCartao;

    private Integer codigoPessoa;
    private String nomePessoa;
    private String cpfPessoa;

    private Integer codigoCartao;
    private Date dataCompensacaoCartao;
    private String composicaoCartao;

    private Integer codigoCheque;
    private Date dataCompensacaoCheque;
    private String composicaoCheque;

    private Integer codigoEmpresa;
    private String nomeEmpresa;
    private String cnpjEmpresa;

    public MovPagamentoIntegracaoTO(ResultSet dadosSql) throws Exception {
        this.setCodigo(dadosSql.getInt("codigo"));
        this.setNomePagador(dadosSql.getString("nomePagador"));
        this.setProdutosPagos(dadosSql.getString("produtosPagos"));
        this.setResponsavelPagamento(dadosSql.getString("responsavelpagamento"));
        this.setValor(dadosSql.getDouble("valor"));
        this.setDataLancamento(dadosSql.getTimestamp("dataLancamento"));
        this.setFormaPagamento(dadosSql.getInt("formapagamento"));
        this.setTipoFormaPagamento(dadosSql.getString("tipoformapagamento"));
        this.setDescricaoFormaPagamento(dadosSql.getString("descricaoformapagamento"));
        this.setAutorizacaoCartao(dadosSql.getString("autorizacaocartao"));
        this.setCodigoPessoa(dadosSql.getInt("codpessoa"));
        this.setNomePessoa(dadosSql.getString("nomepessoa"));
        this.setCpfPessoa(dadosSql.getString("cpfpessoa"));
        this.setCodigoCartao(dadosSql.getInt("codcartao"));
        this.setDataCompensacaoCartao(dadosSql.getTimestamp("dataCompensacaoCartao"));
        this.setComposicaoCartao(dadosSql.getString("composicaocartao"));
        this.setCodigoCheque(dadosSql.getInt("codcheque"));
        this.setDataCompensacaoCheque(dadosSql.getTimestamp("dataCompensacaoCheque"));
        this.setComposicaoCheque(dadosSql.getString("composicaoCheque"));
        this.setCodigoEmpresa(dadosSql.getInt("codEmpresa"));
        this.setNomeEmpresa(dadosSql.getString("nomeempresa"));
        this.setCnpjEmpresa(dadosSql.getString("cnpjempresa"));
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomePagador() {
        return nomePagador;
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public String getProdutosPagos() {
        return produtosPagos;
    }

    public void setProdutosPagos(String produtosPagos) {
        this.produtosPagos = produtosPagos;
    }

    public String getResponsavelPagamento() {
        return responsavelPagamento;
    }

    public void setResponsavelPagamento(String responsavelPagamento) {
        this.responsavelPagamento = responsavelPagamento;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(Integer formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public String getTipoFormaPagamento() {
        return tipoFormaPagamento;
    }

    public void setTipoFormaPagamento(String tipoFormaPagamento) {
        this.tipoFormaPagamento = tipoFormaPagamento;
    }

    public String getDescricaoFormaPagamento() {
        return descricaoFormaPagamento;
    }

    public void setDescricaoFormaPagamento(String descricaoFormaPagamento) {
        this.descricaoFormaPagamento = descricaoFormaPagamento;
    }

    public String getAutorizacaoCartao() {
        return autorizacaoCartao;
    }

    public void setAutorizacaoCartao(String autorizacaoCartao) {
        this.autorizacaoCartao = autorizacaoCartao;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public String getCpfPessoa() {
        return cpfPessoa;
    }

    public void setCpfPessoa(String cpfPessoa) {
        this.cpfPessoa = cpfPessoa;
    }

    public Integer getCodigoCartao() {
        return codigoCartao;
    }

    public void setCodigoCartao(Integer codigoCartao) {
        this.codigoCartao = codigoCartao;
    }

    public Date getDataCompensacaoCartao() {
        return dataCompensacaoCartao;
    }

    public void setDataCompensacaoCartao(Date dataCompensacaoCartao) {
        this.dataCompensacaoCartao = dataCompensacaoCartao;
    }

    public String getComposicaoCartao() {
        return composicaoCartao;
    }

    public void setComposicaoCartao(String composicaoCartao) {
        this.composicaoCartao = composicaoCartao;
    }

    public Integer getCodigoCheque() {
        return codigoCheque;
    }

    public void setCodigoCheque(Integer codigoCheque) {
        this.codigoCheque = codigoCheque;
    }

    public Date getDataCompensacaoCheque() {
        return dataCompensacaoCheque;
    }

    public void setDataCompensacaoCheque(Date dataCompensacaoCheque) {
        this.dataCompensacaoCheque = dataCompensacaoCheque;
    }

    public String getComposicaoCheque() {
        return composicaoCheque;
    }

    public void setComposicaoCheque(String composicaoCheque) {
        this.composicaoCheque = composicaoCheque;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getCnpjEmpresa() {
        return cnpjEmpresa;
    }

    public void setCnpjEmpresa(String cnpjEmpresa) {
        this.cnpjEmpresa = cnpjEmpresa;
    }

    public Date getDataCompensacao() {
        if (dataCompensacaoCartao != null) {
            return dataCompensacaoCartao;
        } else if (dataCompensacaoCheque != null) {
            return dataCompensacaoCheque;
        } else {
            return dataLancamento;
        }
    }

    public String getDocumento() {
        if (!UteisValidacao.emptyNumber(codigoCartao)) {
            return "CC"+codigoCartao;
        } else if (!UteisValidacao.emptyNumber(codigoCheque)) {
            return "CH"+codigoCheque;
        } else {
            return "MP"+codigo;
        }
    }
}

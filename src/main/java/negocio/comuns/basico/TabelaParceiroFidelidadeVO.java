package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class TabelaParceiroFidelidadeVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private String nomeTabela;
    @ChaveEstrangeira
    private ParceiroFidelidadeVO parceiro;
    private boolean defaultRecorrencia = false;
    @NaoControlarLogAlteracao
    private List<TabelaParceiroFidelidadeItemVO> itens;
    @NaoControlarLogAlteracao
    private TabelaParceiroFidelidadeItemVO itemAdicionar;

    /**
     * Construtor padrão da classe <code>TabelaParceiroFidelidade</code>. Cria
     * uma nova instância desta entidade, inicializando automaticamente seus
     * atributos (Classe VO).
     */
    public TabelaParceiroFidelidadeVO() {

    }

    public TabelaParceiroFidelidadeVO(String nomeTabela, ParceiroFidelidadeVO parceiro) {
        this.nomeTabela = nomeTabela;
        this.parceiro = parceiro;
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>TabelaParceiroFidelidadeVO</code>. Todos os tipos de consistência
     * de dados são e devem ser implementadas neste método. São validações
     * típicas: verificação de campos obrigatórios, verificação de valores
     * válidos para os atributos.
     *
     * @param obj
     * @exception ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDados(TabelaParceiroFidelidadeVO obj) throws ConsistirException {
        if (obj.getValidarDados()) {
            if (UteisValidacao.emptyString(obj.getNomeTabela())) {
                throw new ConsistirException("O campo NOME (TabelaParceiroFidelidade) deve ser informado.");
            }
            if (UteisValidacao.emptyList(obj.getItens())) {
                throw new ConsistirException("O campo ITENS (TabelaParceiroFidelidade) da tabela \""+ obj.getNomeTabela() +"\" de acúmulo deve ser informado.");
            }
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        setNomeTabela(getNomeTabela().toUpperCase());
    }

    public String getNomeTabela() {
        return nomeTabela;
    }

    public void setNomeTabela(String nomeTabela) {
        this.nomeTabela = nomeTabela;
    }
    
    public String getNomeTabelaComDefault() {
        return String.format(defaultRecorrencia ? "%s (Recorrência)" : "%s", getNomeTabela());
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ParceiroFidelidadeVO getParceiro() {
        return parceiro;
    }

    public void setParceiro(ParceiroFidelidadeVO parceiro) {
        this.parceiro = parceiro;
    }

    public List<TabelaParceiroFidelidadeItemVO> getItens() {
        if (itens == null) {
            itens = new ArrayList();
        }
        return itens;
    }

    public void setItens(List<TabelaParceiroFidelidadeItemVO> itens) {
        this.itens = itens;
    }

    public boolean isDefaultRecorrencia() {
        return defaultRecorrencia;
    }

    public void setDefaultRecorrencia(boolean defaultRecorrencia) {
        this.defaultRecorrencia = defaultRecorrencia;
    }

    public TabelaParceiroFidelidadeItemVO getItemAdicionar() {
        if (itemAdicionar == null)
            itemAdicionar = new TabelaParceiroFidelidadeItemVO();
        return itemAdicionar;
    }
    
    public void novoItemAdicionar(final Double valorInicio, final Double valorFim, final Double multiplicador) {
        itemAdicionar = new TabelaParceiroFidelidadeItemVO(this, valorInicio, 
                valorFim, multiplicador);        
    }

    public void setItemAdicionar(TabelaParceiroFidelidadeItemVO itemAdicionar) {
        this.itemAdicionar = itemAdicionar;
    }

    public void addItem(TabelaParceiroFidelidadeItemVO item) throws ConsistirException {
        getItens().remove(item);
        validarIntervalo(item);
        getItens().add(item);
    }

    public void removeItem(TabelaParceiroFidelidadeItemVO item) {
        getItens().remove(item);
    }

    public void validarIntervalo(TabelaParceiroFidelidadeItemVO item) throws ConsistirException {
        for (TabelaParceiroFidelidadeItemVO i : getItens()) {
            if ((item.getValorFim() - item.getValorInicio()) <= 0) {
                throw new ConsistirException(String.format("Na tabela %s, a diferença do intervalo inicial e final deve se maior que zero.", 
                        item.getTabelaParceiro().getNomeTabela()));
            }
            if (item.hashCode() != i.hashCode()) {
                if (item.getValorInicio() < 0 || item.getValorFim() < 0) {
                    throw new ConsistirException(String.format("Na tabela %s o Intervalo inicial e final não podem ser negativos.",
                            item.getTabelaParceiro().getNomeTabela()));
                }
                if (item.getValorInicio() >= i.getValorInicio() && item.getValorInicio() <= i.getValorFim()) {
                    throw new ConsistirException(String.format("O valor inicial %s já está na tabela \"%s\" entre o intervalo %s até %s. Tente um intervalo diferente",
                            Formatador.formatarValorMonetario(item.getValorInicio()), i.getTabelaParceiro().getNomeTabela(), Formatador.formatarValorMonetario(i.getValorInicio()),
                            Formatador.formatarValorMonetario(i.getValorFim())));
                }
                if (item.getValorFim() >= i.getValorInicio() && item.getValorFim() <= i.getValorFim()) {
                    throw new ConsistirException(String.format("Na tabela \"%s\", o valor %s já está entre o intervalo %s até %s. Tente um intervalo diferente",
                            item.getTabelaParceiro().getNomeTabela(), Formatador.formatarValorMonetario(item.getValorFim()), Formatador.formatarValorMonetario(i.getValorInicio()), Formatador.formatarValorMonetario(i.getValorFim())));
                }
            }
        }
    }
}

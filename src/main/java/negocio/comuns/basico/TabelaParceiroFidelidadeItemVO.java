package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class TabelaParceiroFidelidadeItemVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    @ChaveEstrangeira
    private TabelaParceiroFidelidadeVO tabelaParceiro;
    private Double valorInicio;
    private Double valorFim;
    private Double multiplicador;
    @NaoControlarLogAlteracao
    private Integer indice = -1;

    /**
     * Construtor padrão da classe <code>ParceiroFidelidadeItem</code>. Cria uma
     * nova instância desta entidade, inicializando automaticamente seus
     * atributos (Classe VO).
     */
    public TabelaParceiroFidelidadeItemVO() {

    }

    public TabelaParceiroFidelidadeItemVO(TabelaParceiroFidelidadeVO tabelaParceiro, Double valorInicio, Double valorFim, Double multiplicador) {
        this.tabelaParceiro = tabelaParceiro;
        this.valorInicio = valorInicio;
        this.valorFim = valorFim;
        this.multiplicador = multiplicador;
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ParceiroFidelidadeItemVO</code>. Todos os tipos de consistência de
     * dados são e devem ser implementadas neste método. São validações típicas:
     * verificação de campos obrigatórios, verificação de valores válidos para
     * os atributos.
     *
     * @param obj
     * @throws negocio.comuns.utilitarias.ConsistirException
     */
    public static void validarDados(TabelaParceiroFidelidadeItemVO obj) throws ConsistirException {
        if (obj.getValidarDados()) {
            if (UteisValidacao.valorNulo(obj, "tabelaParceiro") || UteisValidacao.valorNulo(obj.getTabelaParceiro(), "codigo")) {
                throw new ConsistirException("O campo TABELA PARCEIRO FIDELIDADE (TabelaParceiroFidelidadeItem) deve ser informado.");
            }
            if (obj.getValorInicio() == null) {
                throw new ConsistirException("O campo VALOR INICIAL (TabelaParceiroFidelidadeItem) deve ser informado.");
            }
            if (UteisValidacao.emptyNumber(obj.getValorFim())) {
                throw new ConsistirException("O campo VALOR FINAL (TabelaParceiroFidelidadeItem) deve ser informado.");
            }
            if (UteisValidacao.emptyNumber(obj.getMultiplicador())) {
                throw new ConsistirException("O campo MULTIPLICADOR (TabelaParceiroFidelidadeItem) deve ser informado.");
            }
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
    }

    @Override
    public Integer getCodigo() {
        return (codigo);
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TabelaParceiroFidelidadeVO getTabelaParceiro() {
        if (tabelaParceiro == null) {
            tabelaParceiro = new TabelaParceiroFidelidadeVO();
        }
        return tabelaParceiro;
    }

    public void setTabelaParceiro(TabelaParceiroFidelidadeVO tabelaParceiro) {
        this.tabelaParceiro = tabelaParceiro;
    }

    public Double getValorInicio() {
        return valorInicio;
    }

    public void setValorInicio(Double valorInicio) {
        this.valorInicio = valorInicio;
    }

    public Double getValorFim() {
        return valorFim;
    }

    public void setValorFim(Double valorFim) {
        this.valorFim = valorFim;
    }

    public Double getMultiplicador() {
        return multiplicador;
    }

    public void setMultiplicador(Double multiplicador) {
        this.multiplicador = multiplicador;
    }

    public Integer getIndice() {
        return indice;
    }

    public void setIndice(Integer indice) {
        this.indice = indice;
    }

    public Integer calcularTotalPontos(Double valorBase) {
        return new Double(Math.abs(valorBase * getMultiplicador())).intValue();
    }
}

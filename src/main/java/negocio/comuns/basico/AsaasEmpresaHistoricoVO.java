package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 25/05/2023
 */

public class AsaasEmpresaHistoricoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    private int empresa;
    private Date dataCriacao;
    private String paramsEnvio;
    private String paramsResposta;


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public String getParamsEnvio() {
        return paramsEnvio;
    }

    public void setParamsEnvio(String paramsEnvio) {
        this.paramsEnvio = paramsEnvio;
    }

    public String getParamsResposta() {
        return paramsResposta;
    }

    public void setParamsResposta(String paramsResposta) {
        this.paramsResposta = paramsResposta;
    }
}

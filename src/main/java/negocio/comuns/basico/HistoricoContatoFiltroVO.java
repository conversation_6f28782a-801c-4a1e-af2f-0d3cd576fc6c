/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico;

import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class HistoricoContatoFiltroVO {

    private Date dataInicio = null;
    private Date dataTermino = null;
    private Date dataInicioAgendamento = null;
    private Date dataTerminoAgendameto = null;
    private int codigoColaborador = 0;
    private int nivelMontarDados;
    private List<String> listaFases = new ArrayList<String>();
    private List<String> listaOperacoes = new ArrayList<String>();
    private EmpresaVO empresaVO = new EmpresaVO();
    private String ordenacao = "";
    private boolean gerouAgendamento = false;


    private List<Integer> listaObjecoes = new ArrayList<Integer>();
    private boolean resultadoSimplesRegistro;
    private boolean resultadoObjecao;
    private boolean resultadoAgLigacao;
    private boolean resultadoAgVisita;
    private boolean resultadoAgAula;
    private boolean resultadoOutros;

    /**
     * @return the nivelMontarDados
     */
    public int getNivelMontarDados() {
        return nivelMontarDados;
    }

    /**
     * @param nivelMontarDados the nivelMontarDados to set
     */
    public void setNivelMontarDados(int nivelMontarDados) {
        this.nivelMontarDados = nivelMontarDados;
    }

    /**
     * @return the codigoColaborador
     */
    public int getCodigoColaborador() {
        return codigoColaborador;
    }

    /**
     * @param codigoColaborador the codigoColaborador to set
     */
    public void setCodigoColaborador(int codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    /**
     * @return the dataInicio
     */
    public Date getDataInicio() {
        return dataInicio;
    }

    /**
     * @param dataInicio the dataInicio to set
     */
    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    /**
     * @return the dataTermino
     */
    public Date getDataTermino() {
        return dataTermino;
    }

    /**
     * @param dataTermino the dataTermino to set
     */
    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    /**
     * @return the listaFases
     */
    public List<String> getListaFases() {
        return listaFases;
    }

    /**
     * @param listaFases the listaFases to set
     */
    public void setListaFases(List<String> listaFases) {
        this.listaFases = listaFases;
    }

    /**
     * @return the listaOperacoes
     */
    public List<String> getListaOperacoes() {
        return listaOperacoes;
    }

    /**
     * @param listaOperacoes the listaOperacoes to set
     */
    public void setListaOperacoes(List<String> listaOperacoes) {
        this.listaOperacoes = listaOperacoes;
    }

    /**
     * @return the empresaVO
     */
    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public String getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    /**
     * @param empresaVO the empresaVO to set
     */
    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public Date getDataInicioAgendamento() {
        return dataInicioAgendamento;
    }

    public void setDataInicioAgendamento(Date dataInicioAgendamento) {
        this.dataInicioAgendamento = dataInicioAgendamento;
    }

    public Date getDataTerminoAgendameto() {
        return dataTerminoAgendameto;
    }

    public void setDataTerminoAgendameto(Date dataTerminoAgendameto) {
        this.dataTerminoAgendameto = dataTerminoAgendameto;
    }

    public boolean isGerouAgendamento() {
        return gerouAgendamento;
    }

    public void setGerouAgendamento(boolean gerouAgendamento) {
        this.gerouAgendamento = gerouAgendamento;
    }


    public List<Integer> getListaObjecoes() {
        return listaObjecoes;
    }

    public void setListaObjecoes(List<Integer> listaObjecoes) {
        this.listaObjecoes = listaObjecoes;
    }

    public boolean isResultadoSimplesRegistro() {
        return resultadoSimplesRegistro;
    }

    public void setResultadoSimplesRegistro(boolean resultadoSimplesRegistro) {
        this.resultadoSimplesRegistro = resultadoSimplesRegistro;
    }

    public boolean isResultadoObjecao() {
        return resultadoObjecao;
    }

    public void setResultadoObjecao(boolean resultadoObjecao) {
        this.resultadoObjecao = resultadoObjecao;
    }

    public boolean isResultadoAgLigacao() {
        return resultadoAgLigacao;
    }

    public void setResultadoAgLigacao(boolean resultadoAgLigacao) {
        this.resultadoAgLigacao = resultadoAgLigacao;
    }

    public boolean isResultadoAgVisita() {
        return resultadoAgVisita;
    }

    public void setResultadoAgVisita(boolean resultadoAgVisita) {
        this.resultadoAgVisita = resultadoAgVisita;
    }

    public boolean isResultadoAgAula() {
        return resultadoAgAula;
    }

    public void setResultadoAgAula(boolean resultadoAgAula) {
        this.resultadoAgAula = resultadoAgAula;
    }

    public boolean isResultadoOutros() {
        return resultadoOutros;
    }

    public void setResultadoOutros(boolean resultadoOutros) {
        this.resultadoOutros = resultadoOutros;
    }
}

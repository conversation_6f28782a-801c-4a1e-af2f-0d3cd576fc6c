package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

/*
 * Created by <PERSON><PERSON>
 */
public class EmailWS extends SuperTO {

    private Integer codigo;
    private String email;
    private Boolean emailCorrespondencia;
    private Boolean bloqueadoBounce;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getEmailCorrespondencia() {
        return emailCorrespondencia;
    }

    public void setEmailCorrespondencia(Boolean emailCorrespondencia) {
        this.emailCorrespondencia = emailCorrespondencia;
    }

    public Boolean getBloqueadoBounce() {
        return bloqueadoBounce;
    }

    public void setBloqueadoBounce(Boolean bloqueadoBounce) {
        this.bloqueadoBounce = bloqueadoBounce;
    }
}
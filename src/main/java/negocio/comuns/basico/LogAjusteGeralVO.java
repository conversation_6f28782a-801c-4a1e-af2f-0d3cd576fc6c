/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class LogAjusteGeralVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Date datalancamento;
    private String usuario;
    private String usuarioOAMD;
    private ProcessoAjusteGeralEnum processoAjusteGeralEnum;
    private String parametros;

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDatalancamento() {
        return datalancamento;
    }

    public void setDatalancamento(Date datalancamento) {
        this.datalancamento = datalancamento;
    }

    public String getUsuario() {
        if (usuario == null) {
            usuario = "";
        }
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public ProcessoAjusteGeralEnum getProcessoAjusteGeralEnum() {
        return processoAjusteGeralEnum;
    }

    public void setProcessoAjusteGeralEnum(ProcessoAjusteGeralEnum processoAjusteGeralEnum) {
        this.processoAjusteGeralEnum = processoAjusteGeralEnum;
    }

    public String getParametros() {
        if (parametros == null) {
            parametros = "";
        }
        return parametros;
    }

    public void setParametros(String parametros) {
        this.parametros = parametros;
    }

    public String getUsuarioOAMD() {
        if (usuarioOAMD == null) {
            usuarioOAMD = "";
        }
        return usuarioOAMD;
    }

    public void setUsuarioOAMD(String usuarioOAMD) {
        this.usuarioOAMD = usuarioOAMD;
    }

    public String getDataLancamento_Apresentar() {
        return Uteis.getDataComHora(getDatalancamento());
    }
}
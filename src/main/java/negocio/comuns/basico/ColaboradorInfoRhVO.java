package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;

/**
 * Created by ulisses on 12/11/2022.
 */
public class ColaboradorInfoRhVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo = 0;

    @NaoControlarLogAlteracao
    private ColaboradorVO colaboradorVO;

    private String tamanhoUniformeCamisa;
    private int tamanhoUniformeCalca;
    private double valorSalario;
    private String observacao;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ColaboradorVO getColaboradorVO() {
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public String getTamanhoUniformeCamisa() {
        return tamanhoUniformeCamisa;
    }

    public void setTamanhoUniformeCamisa(String tamanhoUniformeCamisa) {
        this.tamanhoUniformeCamisa = tamanhoUniformeCamisa;
    }

    public int getTamanhoUniformeCalca() {
        return tamanhoUniformeCalca;
    }

    public void setTamanhoUniformeCalca(int tamanhoUniformeCalca) {
        this.tamanhoUniformeCalca = tamanhoUniformeCalca;
    }

    public double getValorSalario() {
        return valorSalario;
    }

    public void setValorSalario(double valorSalario) {
        this.valorSalario = valorSalario;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public boolean informouAlgumCampo(){
        if ((tamanhoUniformeCamisa != null) && (!tamanhoUniformeCamisa.trim().equals(""))){
            return true;
        }
        if (tamanhoUniformeCalca > 0){
            return true;
        }
        if (valorSalario > 0){
            return true;
        }
        if ((observacao != null) && (!observacao.trim().equals(""))){
            return true;
        }

        return false;
    }

}

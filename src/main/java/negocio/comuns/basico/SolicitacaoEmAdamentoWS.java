package negocio.comuns.basico;

import negocio.comuns.utilitarias.Uteis;
import negocio.intranet.SolicitacaoAndamentoJSON;

import java.io.Serializable;

public class SolicitacaoEmAdamentoWS implements Serializable {

    private Integer id;
    private String usuario;
    private String descricao;
    private String atividade;
    private String dataCadastro;
    private String grupoAtividade;
    private String urlFotoUsuario;
    private String dataCadastroOriginal;
    private Integer idSolicitacao;
    private String ticket;

    public SolicitacaoEmAdamentoWS(SolicitacaoAndamentoJSON obj ) {
        this.id = obj.getId();
        this.usuario = obj.getUsuario();
        this.descricao = obj.getDescricao();
        this.atividade = obj.getAtividade();
        this.dataCadastro = obj.getDataCadastro();
        this.grupoAtividade = obj.getGrupoAtividade();
        this.urlFotoUsuario = obj.getUrlFotoUsuario();
        this.dataCadastroOriginal = Uteis.getData(obj.getDataCadastroOriginal());
        this.idSolicitacao = obj.getIdSolicitacao();
        this.ticket = obj.getTicket();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getAtividade() {
        return atividade;
    }

    public void setAtividade(String atividade) {
        this.atividade = atividade;
    }

    public String getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(String dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getGrupoAtividade() {
        return grupoAtividade;
    }

    public void setGrupoAtividade(String grupoAtividade) {
        this.grupoAtividade = grupoAtividade;
    }

    public String getUrlFotoUsuario() {
        return urlFotoUsuario;
    }

    public void setUrlFotoUsuario(String urlFotoUsuario) {
        this.urlFotoUsuario = urlFotoUsuario;
    }

    public String getDataCadastroOriginal() {
        return dataCadastroOriginal;
    }

    public void setDataCadastroOriginal(String dataCadastroOriginal) {
        this.dataCadastroOriginal = dataCadastroOriginal;
    }

    public Integer getIdSolicitacao() {
        return idSolicitacao;
    }

    public void setIdSolicitacao(Integer idSolicitacao) {
        this.idSolicitacao = idSolicitacao;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }
}

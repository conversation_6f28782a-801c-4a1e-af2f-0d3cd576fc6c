package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.pactopay.PactoPayComunicacaoDTO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.StatusPactoPayComunicacaoEnum;
import negocio.comuns.financeiro.enumerador.TagReguaCobrancaPactoPayEnum;
import negocio.comuns.financeiro.enumerador.TipoEnvioPactoPayEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class PactoPayComunicacaoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Date dataRegistro;
    @ChaveEstrangeira
    private EmpresaVO empresaVO;
    @ChaveEstrangeira
    private PessoaVO pessoaVO;
    @ChaveEstrangeira
    private HistoricoContatoVO historicoContatoVO;
    @ChaveEstrangeira
    private PactoPayConfigVO pactoPayConfigVO;
    private MeioEnvio meioEnvio;
    private TipoEnvioPactoPayEnum tipoEnvioPactoPay;
    private String comunicacao;
    private boolean sucesso = false;
    private Date dataExecucao;
    private String resultado;
    //dataLido - usadao mais para e caso de email onde conseguimos identificar se o cara abriu o email, ele pode abrir o email e não clicar no link
    private Date dataLido;
    //dataClicou - quando o cara clicou no link - ele marca o clicou e o lido
    private Date dataClicou;
    private StatusPactoPayComunicacaoEnum status;
    private TransacaoVO transacaoVO;
    private PixVO pixVO;
    private BoletoVO boletoVO;
    private AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO;
    private List<MovParcelaVO> listaMovParcela;
    private Double desconto;
    private String tags;
    @NaoControlarLogAlteracao
    private String arquivoModelo;

    public PactoPayComunicacaoVO() {

    }

    public PactoPayComunicacaoVO(PessoaVO pessoaVO, EmpresaVO empresaVO,
                                 MeioEnvio meioEnvio) {
        this.setDataRegistro(Calendario.hoje());
        this.setStatus(StatusPactoPayComunicacaoEnum.NENHUM);
        this.setPessoaVO(pessoaVO);
        this.setEmpresaVO(empresaVO);
        this.setMeioEnvio(meioEnvio);
    }

    public void processarComunicacao(PactoPayComunicacaoDTO pactoPayComunicacaoDTO, PactoPayConfigVO pactoPayConfigVO) {
        this.getEmpresaVO().setCodigo(pactoPayComunicacaoDTO.getEmpresa());
        this.getPessoaVO().setCodigo(pactoPayComunicacaoDTO.getPessoa());
        this.setTipoEnvioPactoPay(pactoPayComunicacaoDTO.getTipoEnvioPactoPayEnum());
        this.setComunicacao(new JSONObject(pactoPayComunicacaoDTO).toString());
        this.setPactoPayConfigVO(pactoPayConfigVO);
        this.setStatus(StatusPactoPayComunicacaoEnum.GERADO);
    }

    public void erro(Exception ex) {
        this.setStatus(StatusPactoPayComunicacaoEnum.ERRO);
        if (UteisValidacao.emptyString(this.getResultado())) {
            this.setResultado(ex.getMessage());
        }
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataRegistroApresentar() {
        return Calendario.getDataAplicandoFormatacao(this.getDataRegistro(), "dd/MM/yyyy HH:mm:ss");
    }

    public MeioEnvio getMeioEnvio() {
        return meioEnvio;
    }

    public void setMeioEnvio(MeioEnvio meioEnvio) {
        this.meioEnvio = meioEnvio;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getResultado() {
        if (resultado == null) {
            resultado = "";
        }
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

    public TipoEnvioPactoPayEnum getTipoEnvioPactoPay() {
        if (tipoEnvioPactoPay == null) {
            tipoEnvioPactoPay = TipoEnvioPactoPayEnum.NENHUM;
        }
        return tipoEnvioPactoPay;
    }

    public void setTipoEnvioPactoPay(TipoEnvioPactoPayEnum tipoEnvioPactoPay) {
        this.tipoEnvioPactoPay = tipoEnvioPactoPay;
    }

    public Date getDataExecucao() {
        return dataExecucao;
    }

    public void setDataExecucao(Date dataExecucao) {
        this.dataExecucao = dataExecucao;
    }

    public String getDataExecucaoApresentar() {
        return Calendario.getDataAplicandoFormatacao(this.getDataExecucao(), "dd/MM/yyyy HH:mm:ss");
    }

    public HistoricoContatoVO getHistoricoContatoVO() {
        if (historicoContatoVO == null) {
            historicoContatoVO = new HistoricoContatoVO();
        }
        return historicoContatoVO;
    }

    public void setHistoricoContatoVO(HistoricoContatoVO historicoContatoVO) {
        this.historicoContatoVO = historicoContatoVO;
    }

    public PactoPayComunicacaoDTO getPactoPayComunicacaoDTO() {
        return new PactoPayComunicacaoDTO(new JSONObject(this.getComunicacao()));
    }

    public String getComunicacao() {
        if (comunicacao == null) {
            comunicacao = "";
        }
        return comunicacao;
    }

    public void setComunicacao(String comunicacao) {
        this.comunicacao = comunicacao;
    }

    public Date getDataLido() {
        return dataLido;
    }

    public void setDataLido(Date dataLido) {
        this.dataLido = dataLido;
    }

    public String getDataLidoApresentar() {
        return Calendario.getDataAplicandoFormatacao(this.getDataLido(), "dd/MM/yyyy HH:mm:ss");
    }

    public Date getDataClicou() {
        return dataClicou;
    }

    public void setDataClicou(Date dataClicou) {
        this.dataClicou = dataClicou;
    }

    public String getDataClicouApresentar() {
        return Calendario.getDataAplicandoFormatacao(this.getDataClicou(), "dd/MM/yyyy HH:mm:ss");
    }

    public StatusPactoPayComunicacaoEnum getStatus() {
        if (status == null) {
            status = StatusPactoPayComunicacaoEnum.NENHUM;
        }
        return status;
    }

    public void setStatus(StatusPactoPayComunicacaoEnum status) {
        this.status = status;
    }

    public TransacaoVO getTransacaoVO() {
        if (transacaoVO == null) {
            transacaoVO = new TransacaoVO();
        }
        return transacaoVO;
    }

    public void setTransacaoVO(TransacaoVO transacaoVO) {
        this.transacaoVO = transacaoVO;
    }

    public PixVO getPixVO() {
        if (pixVO == null) {
            pixVO = new PixVO();
        }
        return pixVO;
    }

    public void setPixVO(PixVO pixVO) {
        this.pixVO = pixVO;
    }

    public BoletoVO getBoletoVO() {
        if (boletoVO == null) {
            boletoVO = new BoletoVO();
        }
        return boletoVO;
    }

    public void setBoletoVO(BoletoVO boletoVO) {
        this.boletoVO = boletoVO;
    }

    public AutorizacaoCobrancaClienteVO getAutorizacaoCobrancaClienteVO() {
        if (autorizacaoCobrancaClienteVO == null) {
            autorizacaoCobrancaClienteVO = new AutorizacaoCobrancaClienteVO();
        }
        return autorizacaoCobrancaClienteVO;
    }

    public void setAutorizacaoCobrancaClienteVO(AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO) {
        this.autorizacaoCobrancaClienteVO = autorizacaoCobrancaClienteVO;
    }

    public String getTitleResultadoTela() {
        StringBuilder title = new StringBuilder();
        if (!UteisValidacao.emptyNumber(this.getTransacaoVO().getCodigo())) {
            title.append("Gerado Transação: ").append(this.getTransacaoVO().getCodigo()).append(" <br/>");
        }
        if (!UteisValidacao.emptyNumber(this.getPixVO().getCodigo())) {
            title.append("Gerado Pix: ").append(this.getPixVO().getCodigo()).append(" <br/>");
        }
        if (!UteisValidacao.emptyNumber(this.getBoletoVO().getCodigo())) {
            title.append("Gerado Boleto: ").append(this.getBoletoVO().getCodigo()).append(" <br/>");
        }
        if (!UteisValidacao.emptyNumber(this.getAutorizacaoCobrancaClienteVO().getCodigo())) {
            title.append("Autorização de Cobrança: ").append(this.getAutorizacaoCobrancaClienteVO().getCodigo()).append(" <br/>");
        }
        return title.toString();
    }

    public List<MovParcelaVO> getListaMovParcela() {
        if (listaMovParcela == null) {
            listaMovParcela = new ArrayList<>();
        }
        return listaMovParcela;
    }

    public void setListaMovParcela(List<MovParcelaVO> listaMovParcela) {
        this.listaMovParcela = listaMovParcela;
    }

    public Double getDesconto() {
        if (desconto == null) {
            desconto = 0.0;
        }
        return desconto;
    }

    public void setDesconto(Double desconto) {
        this.desconto = desconto;
    }

    public String getTags() {
        if (tags == null) {
            tags = new JSONObject().toString();
        }
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public JSONObject getTagsJSON() {
        return new JSONObject(this.getTags());
    }

    public Map<TagReguaCobrancaPactoPayEnum, Object> getTagsMap() {
        Map<TagReguaCobrancaPactoPayEnum, Object> mapa = new HashMap<>();
        try {
            JSONObject json = this.getTagsJSON();
            Iterator<String> keys = json.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                Object obj = json.get(key);
                TagReguaCobrancaPactoPayEnum tagReguaCobrancaPactoPayEnum = TagReguaCobrancaPactoPayEnum.obterPorTag(key);
                if (tagReguaCobrancaPactoPayEnum != null) {
                    mapa.put(tagReguaCobrancaPactoPayEnum, obj);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return mapa;
    }

    public String getCustom_fields() {
        String separador = "%s%";

        Map<TagReguaCobrancaPactoPayEnum, Object> map = this.getTagsMap();
        StringBuilder customFields = new StringBuilder();

        for (TagReguaCobrancaPactoPayEnum tag : TagReguaCobrancaPactoPayEnum.values()) {
            if (customFields.length() > 0) {
                customFields.append(separador);
            }
            Object valorTag = map.get(tag);
            customFields.append(valorTag == null ? "" : valorTag.toString());
        }
        return customFields.toString();
    }

    public PactoPayConfigVO getPactoPayConfigVO() {
        if (pactoPayConfigVO == null) {
            pactoPayConfigVO = new PactoPayConfigVO();
        }
        return pactoPayConfigVO;
    }

    public void setPactoPayConfigVO(PactoPayConfigVO pactoPayConfigVO) {
        this.pactoPayConfigVO = pactoPayConfigVO;
    }

    public String getArquivoModelo() {
        if (arquivoModelo == null) {
            arquivoModelo = "";
        }
        return arquivoModelo;
    }

    public void setArquivoModelo(String arquivoModelo) {
        this.arquivoModelo = arquivoModelo;
    }
}

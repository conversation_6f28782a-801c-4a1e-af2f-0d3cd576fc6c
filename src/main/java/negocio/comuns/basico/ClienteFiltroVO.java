/**
 *
 */
package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.facade.jdbc.basico.Pessoa;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class ClienteFiltroVO extends SuperVO {

    private ClienteVO clienteVO;
    private PessoaVO pessoaVO;
    private EmpresaVO empresaVO;
    private ContratoVO contratoVO;
    private Date inicio;
    private Date fim;
    private boolean controlarAcesso;
    private SituacaoClienteSinteticoDW situacaoClienteSinteticoDW;
    private ProfissaoVO profissaoVO;
    private GrauInstrucaoVO grauInstrucaoVO;
    private GrupoVO grupoVO;
    private ClassificacaoVO classificacaoVO;
    private int nivelMontarDados;
    private String paramTipoConsulta = "";
    private EmailVO emailVO;
    private TelefoneVO telefoneVO;
    private PlacaVO placaVO;
    private String responsavel;
    
    private String orderBy;
    private String orderByAD;

    private boolean consultarDeTodasEmpresas = false;

    public ClienteVO getClienteVO() {
        return this.clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public PessoaVO getPessoaVO() {
        return this.pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public void setPessoaVOSetSemValorNomeRgCpfRnePassaporte(PessoaVO pessoaVO){
        pessoaVO.setNome("sem valor");
        pessoaVO.setRg("sem valor");
        pessoaVO.setCfp("sem valor");
        pessoaVO.setRne("sem valor");
        pessoaVO.setPassaporte("sem valor");
        this.pessoaVO = pessoaVO;
    }

    public EmpresaVO getEmpresaVO() {
        return this.empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public int getNivelMontarDados() {
        return this.nivelMontarDados;
    }

    public void setNivelMontarDados(int nivelMontarDados) {
        this.nivelMontarDados = nivelMontarDados;
    }

    public Date getInicio() {
        return this.inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return this.fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public boolean isControlarAcesso() {
        return this.controlarAcesso;
    }

    public void setControlarAcesso(boolean controlarAcesso) {
        this.controlarAcesso = controlarAcesso;
    }

    public ContratoVO getContratoVO() {
        return this.contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public SituacaoClienteSinteticoDW getSituacaoClienteSinteticoDW() {
        return this.situacaoClienteSinteticoDW;
    }

    public void setSituacaoClienteSinteticoDW(SituacaoClienteSinteticoDW situacaoClienteSinteticoDW) {
        this.situacaoClienteSinteticoDW = situacaoClienteSinteticoDW;
    }

    public ProfissaoVO getProfissaoVO() {
        return this.profissaoVO;
    }

    public void setProfissaoVO(ProfissaoVO profissaoVO) {
        this.profissaoVO = profissaoVO;
    }

    public GrauInstrucaoVO getGrauInstrucaoVO() {
        return this.grauInstrucaoVO;
    }

    public void setGrauInstrucaoVO(GrauInstrucaoVO grauInstrucaoVO) {
        this.grauInstrucaoVO = grauInstrucaoVO;
    }

    public GrupoVO getGrupoVO() {
        return grupoVO;
    }

    public void setGrupoVO(GrupoVO grupoVO) {
        this.grupoVO = grupoVO;
    }

    public ClassificacaoVO getClassificacaoVO() {
        return classificacaoVO;
    }

    public void setClassificacaoVO(ClassificacaoVO classVO) {
        this.classificacaoVO = classVO;
    }

    public String getParamTipoConsulta() {
        return paramTipoConsulta;
    }

    public void setParamTipoConsulta(String paramTipoConsulta) {
        this.paramTipoConsulta = paramTipoConsulta;
    }

    public EmailVO getEmailVO() {
        return emailVO;
    }

    public void setEmailVO(EmailVO emailVO) {
        this.emailVO = emailVO;
    }

    public TelefoneVO getTelefoneVO() {
        return telefoneVO;
    }

    public void setTelefoneVO(TelefoneVO telefoneVO) {
        this.telefoneVO = telefoneVO;
    }

    public PlacaVO getPlacaVO() {
        return placaVO;
    }

    public void setPlacaVO(PlacaVO placaVO) {
        this.placaVO = placaVO;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByAD() {
        return orderByAD;
    }

    public void setOrderByAD(String orderByAD) {
        this.orderByAD = orderByAD;
    }


    public String getResponsavel() {
        if (responsavel == null) {
            responsavel = "";
        }
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public boolean isConsultarDeTodasEmpresas() {
        return consultarDeTodasEmpresas;
    }

    public void setConsultarDeTodasEmpresas(boolean consultarDeTodasEmpresas) {
        this.consultarDeTodasEmpresas = consultarDeTodasEmpresas;
    }
}

package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoLogEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class ProcessoImportacaoLogVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private ProcessoImportacaoVO processoImportacaoVO;
    private Date dataRegistro;
    private TipoLogEnum tipoLog;
    private String mensagem;

    public ProcessoImportacaoLogVO(){
    }

    public ProcessoImportacaoLogVO(ProcessoImportacaoVO processoImportacaoVO, TipoLogEnum tipoLog, String mensagem){
        this.processoImportacaoVO = processoImportacaoVO;
        this.dataRegistro = Calendario.hoje();
        this.tipoLog = tipoLog;
        this.mensagem = mensagem;
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProcessoImportacaoVO getProcessoImportacaoVO() {
        if (processoImportacaoVO == null) {
            processoImportacaoVO = new ProcessoImportacaoVO();
        }
        return processoImportacaoVO;
    }

    public void setProcessoImportacaoVO(ProcessoImportacaoVO processoImportacaoVO) {
        this.processoImportacaoVO = processoImportacaoVO;
    }

    public TipoLogEnum getTipoLog() {
        if (tipoLog == null) {
            tipoLog = TipoLogEnum.INFORMACAO;
        }
        return tipoLog;
    }

    public void setTipoLog(TipoLogEnum tipoLog) {
        this.tipoLog = tipoLog;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataRegistroApresentar() {
        if (getDataRegistro() == null) {
            return "";
        } else {
            return Uteis.getDataComHora(getDataRegistro());
        }
    }
}

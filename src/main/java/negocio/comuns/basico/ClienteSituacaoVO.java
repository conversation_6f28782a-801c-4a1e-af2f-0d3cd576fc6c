/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class ClienteSituacaoVO extends SuperVO {

    protected Integer codigo;
    protected ClienteVO clienteVO;
    protected String situacao;
    protected String subordinadaSituacao;
    protected Date dataInicio;
    protected Date dataFim;

    /**
     * Construtor padrão da classe <code>Cidade</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ClienteSituacaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>CidadeVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ClienteSituacaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getSituacao().equals("")) {
            throw new ConsistirException("O campo SITUAÇÃO (ClienteSituacao) deve ser informado.");
        }
        if ((obj.getClienteVO() == null) ||
                (obj.getClienteVO().getCodigo() == 0)) {
            throw new ConsistirException("O campo Cliente (ClienteSituacao) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setSituacao(getSituacao().toUpperCase());
        setSubordinadaSituacao(getSubordinadaSituacao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setSituacao("");
        setSubordinadaSituacao("");
        setClienteVO(new ClienteVO());

    }

    public String situacaoAtualCliente() {
        return getSituacaoAtualCliente_Apresentar();
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSubordinadaSituacao() {
        if (subordinadaSituacao == null) {
            subordinadaSituacao = "";
        }
        return subordinadaSituacao;
    }

    public void setSubordinadaSituacao(String subordinadaSituacao) {
        this.subordinadaSituacao = subordinadaSituacao;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Boolean getAtivo() {
        return getSituacao().equals("AT");
    }

    public Boolean getAtivoNormal() {
        return getSituacao().equals("AT") && getSubordinadaSituacao().equals("NO");
    }

    public Boolean getAtivoAvencer() {
        return getSituacao().equals("AT") && getSubordinadaSituacao().equals("AV");
    }

    public Boolean getAtivoAtestado() {
        return getSituacao().equals("AT") && getSubordinadaSituacao().equals("AE");
    }

    public Boolean getAtivoCarencia() {
        return getSituacao().equals("AT") && getSubordinadaSituacao().equals("CR");
    }

    public Boolean getTrancado() {
        return getSituacao().equals("TR");
    }

    public Boolean getTrancadoVencido() {
        return getSituacao().equals("TR") && getSubordinadaSituacao().equals("TV");
    }

    public Boolean getInativo() {
        return getSituacao().equals("IN");
    }

    public Boolean getInativoCancelamento() {
        return getSituacao().equals("IN") && getSubordinadaSituacao().equals("CA");
    }

    public Boolean getInativoDesistente() {
        return getSituacao().equals("IN") && getSubordinadaSituacao().equals("DE");
    }

    public Boolean getInativoVencido() {
        return getSituacao().equals("IN") && getSubordinadaSituacao().equals("VE");
    }

    public Boolean getVisitante() {
        return getSituacao().equals("VI");
    }

    public Boolean getVisitanteFreePass() {
        return (getSituacao().equals("VI") && getSubordinadaSituacao().equals("PL")) || (getSituacao().equals("IN") && getClienteVO().isPossuiFreePass());
    }

    public Boolean getGymPass() {
        return getClienteVO().isPossuiGymPass();
    }

    public Boolean getTotalPass() {
        return getClienteVO().isPossuiTotalPass();
    }

    public Boolean getVisitanteDiaria() {
        return getSituacao().equals("VI") && getSubordinadaSituacao().equals("DI");
    }

    public Boolean getVisitanteAulaAvulsa() {
        return getSituacao().equals("VI") && getSubordinadaSituacao().equals("AA");
    }

    public boolean getDependentePlanoCompartilhado() {
        return !UteisValidacao.emptyNumber(getClienteVO().getTitularPlanoCompartilhado());
    }

    public String getSituacaoAtualCliente_Apresentar() {
        if (getAtivoAtestado()) {
            return "AE";
        }
        if (getAtivoAvencer()) {
            return "AV";
        }
        if (getAtivoCarencia()) {
            return "CR";
        }
        if (getAtivoNormal()) {
            return "NO";
        }
        if (getAtivo()) {
            return "AT";
        }
        if (getInativoCancelamento()) {
            return "CA";
        }
        if (getInativoDesistente()) {
            return "DE";
        }
        if (getInativoVencido()) {
            return "VE";
        }
        if (getInativo()) {
            return "IN";
        }
        if (getTrancadoVencido()) {
            return "TV";
        }
        if (getTrancado()) {
            return "TR";
        }
        if (getVisitante()) {
            return "VI";
        }
        if (getVisitanteAulaAvulsa()) {
            return "AA";
        }
        if (getVisitanteDiaria()) {
            return "DI";
        }
        if (getVisitanteFreePass()) {
            return "PL";
        }
        if (getDependentePlanoCompartilhado()) {
            return "DP";
        }
        return "";
    }
}

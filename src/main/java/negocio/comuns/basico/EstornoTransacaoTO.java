package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.CupomFiscalVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UtilReflection;

import java.util.Date;

/**
 *  Created with IntelliJ IDEA.
 *  User: <PERSON><PERSON>
 *  Date: 21/04/2020
 */
public class EstornoTransacaoTO extends SuperTO {

    private TransacaoVO transacaoVO;
    private boolean sucesso = false;
    private String msgResultado;

    public TransacaoVO getTransacaoVO() {
        return transacaoVO;
    }

    public void setTransacaoVO(TransacaoVO transacaoVO) {
        this.transacaoVO = transacaoVO;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMsgResultado() {
        if (msgResultado == null) {
            msgResultado = "";
        }
        return msgResultado;
    }

    public void setMsgResultado(String msgResultado) {
        this.msgResultado = msgResultado;
    }
}

    package negocio.comuns.basico;

import com.sun.istack.NotNull;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoBloqueioInadimplenciaEnum;
import negocio.comuns.basico.enumerador.TipoCategoriaClubeEnum;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;

/**
 * Reponsável por manter os dados da entidade Categoria. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class CategoriaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;    
    protected String nome;    
    protected String tipoCategoria;
    @NaoControlarLogAlteracao
    protected Integer nrConvitePermitido;
    @NaoControlarLogAlteracao
    private boolean selecionado = false;
    private Integer tipoCategoriaClube;
    private Integer tipoBloqueioInadimplencia;
    private ProdutoVO produtoPadrao;
    private String nomeExterno;
    private boolean validarSituacaoEmpresaSesi = false;
    private boolean obrigatorioCnpjClienteSesi;

    /**
     * Construtor padrão da classe <code>Categoria</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public CategoriaVO() {
        super();
        inicializarDados();
    }

    public CategoriaVO(String nome) {
        super();
        inicializarDados();
        this.nome = nome;
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>CategoriaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(CategoriaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getNome().equals("")) {
            throw new ConsistirException("O campo NOME (Categoria) deve ser informado.");
        }
        if (obj.getTipoCategoria().equals("")) {
            throw new ConsistirException("O campo TIPO CATEGORIA (Categoria) deve ser informado.");
        }
//        if (obj.getNrConvitePermitido() == null) {
//            throw new ConsistirException("O campo NUMERO DE CONVITES (Categoria) deve ser informado.");
//        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setNome(getNome().toUpperCase());
        setTipoCategoria(getTipoCategoria().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        //setCodigo(new Integer(0));
        //setNome("");
        //setTipoCategoria("");
        //setNrConvitePermitido(new Integer(0));
    }

    public Integer getNrConvitePermitido() {
    	if(nrConvitePermitido == null){
    		nrConvitePermitido = 0;
    	}
        return (nrConvitePermitido);
    }

    public void setNrConvitePermitido(Integer nrConvitePermitido) {
        this.nrConvitePermitido = nrConvitePermitido;
    }

    public String getTipoCategoria() {
        if (tipoCategoria == null) {
            tipoCategoria = "";
        }
        return (tipoCategoria);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico. 
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o 
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc. 
     */
    public String getTipoCategoria_Apresentar() {
        if (tipoCategoria == null) {
            tipoCategoria = "";
        }
        if (tipoCategoria.equals("NS")) {
            return "Não Sócio";
        }
        if (tipoCategoria.equals("AL")) {
            return "Aluno";
        }
        if (tipoCategoria.equals("SO")) {
            return "Sócio";
        }
        if (tipoCategoria.equals("CO")) {
            return "Comerciário";
        }
        if (tipoCategoria.equals("DE")) {
            return "Dependente";
        }
        if (tipoCategoria.equals("US")) {
            return "Usuário";
        }
        return (tipoCategoria);
    }

    public String getTipoCategoriaClube_Apresentar(){
    	if (this.tipoCategoriaClube != null){
    		TipoCategoriaClubeEnum tipoEnum = TipoCategoriaClubeEnum.getTipoCategoriaClube(this.tipoCategoriaClube);
    		return (tipoEnum != null) ? tipoEnum.getDescricao() : "";
    	}
    	return "";
    }

    public TipoCategoriaClubeEnum getTipoCategoriaClubeEnum(){
    	if (this.tipoCategoriaClube != null){
    		return  TipoCategoriaClubeEnum.getTipoCategoriaClube(this.tipoCategoriaClube);
    	}
    	return null;
    }

    public void setTipoCategoria(String tipoCategoria) {
        this.tipoCategoria = tipoCategoria;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return (nome);
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
    	if(codigo == null){
    		codigo = 0;
    	}
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Integer getTipoCategoriaClube() {
		return tipoCategoriaClube;
	}

	public void setTipoCategoriaClube(Integer tipoCategoriaClube) {
		this.tipoCategoriaClube = tipoCategoriaClube;
	}

	public Integer getTipoBloqueioInadimplencia() {
		return tipoBloqueioInadimplencia;
	}

	public void setTipoBloqueioInadimplencia(Integer tipoBloqueioInadimplencia) {
		this.tipoBloqueioInadimplencia = tipoBloqueioInadimplencia;
	}

	public TipoBloqueioInadimplenciaEnum getTipoBloqueioInadimplenciaEnum() {
		return TipoBloqueioInadimplenciaEnum.getTipoBloqueioInadimplenciaEnum(this.tipoBloqueioInadimplencia);
	}

	public Boolean getDeSocio() {
		return (this.getTipoCategoria() != null) && (this.getTipoCategoria().equals("SO"));
	}

	public ProdutoVO getProdutoPadrao() {
		return produtoPadrao;
	}

	public void setProdutoPadrao(ProdutoVO produtoPadrao) {
		this.produtoPadrao = produtoPadrao;
	}

    @Override
    public boolean equals(Object obj) {
        return obj instanceof CategoriaVO &&
                ((CategoriaVO)obj).getCodigo() == this.codigo;
    }

    public int compareTo(@NotNull Object obj) {
        if (obj instanceof CategoriaVO) {
            return this.nome.compareTo(((CategoriaVO) obj).getNome());
        }
        return -1;
    }

    public String getNomeExterno() {
        if (nomeExterno == null) {
            nomeExterno = "";
        }
        return nomeExterno;
    }

    public void setNomeExterno(String nomeExterno) {
        this.nomeExterno = nomeExterno;
    }

    public boolean isValidarSituacaoEmpresaSesi() {
        return validarSituacaoEmpresaSesi;
    }

    public void setValidarSituacaoEmpresaSesi(boolean validarSituacaoEmpresaSesi) {
        this.validarSituacaoEmpresaSesi = validarSituacaoEmpresaSesi;
    }

    public boolean isObrigatorioCnpjClienteSesi() {
        return obrigatorioCnpjClienteSesi;
    }

    public void setObrigatorioCnpjClienteSesi(boolean obrigatorioCnpjClienteSesi) {
        this.obrigatorioCnpjClienteSesi = obrigatorioCnpjClienteSesi;
    }
}

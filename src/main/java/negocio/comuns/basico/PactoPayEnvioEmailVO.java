package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.pactopay.PactoPayEnvioEmailDadosDTO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.PactoPayEnvioEmailStatusEnum;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PactoPayEnvioEmailVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Date dataRegistro;
    private Date dataExecucao;
    @ChaveEstrangeira
    private PactoPayConfigVO pactoPayConfigVO;
    private EmpresaVO empresaVO;
    private PactoPayEnvioEmailStatusEnum status;
    private String mensagem;
    private String assunto;
    private String dados;
    private String envio;
    private String resposta;
    private List<PactoPayComunicacaoVO> listaPactoPayComunicacao;

    public PactoPayEnvioEmailVO() {

    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public PactoPayConfigVO getPactoPayConfigVO() {
        if (pactoPayConfigVO == null) {
            pactoPayConfigVO = new PactoPayConfigVO();
        }
        return pactoPayConfigVO;
    }

    public void setPactoPayConfigVO(PactoPayConfigVO pactoPayConfigVO) {
        this.pactoPayConfigVO = pactoPayConfigVO;
    }

    public PactoPayEnvioEmailStatusEnum getStatus() {
        return status;
    }

    public void setStatus(PactoPayEnvioEmailStatusEnum status) {
        this.status = status;
    }

    public PactoPayEnvioEmailDadosDTO getPactoPayEnvioEmailDadosDTO() {
        return new PactoPayEnvioEmailDadosDTO(new JSONObject(this.getDados()));
    }

    public String getDados() {
        if (dados == null) {
            dados = new JSONObject().toString();
        }
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }

    public String getEnvio() {
        return envio;
    }

    public void setEnvio(String envio) {
        this.envio = envio;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public List<PactoPayComunicacaoVO> getListaPactoPayComunicacao() {
        if (listaPactoPayComunicacao == null) {
            listaPactoPayComunicacao = new ArrayList<>();
        }
        return listaPactoPayComunicacao;
    }

    public void setListaPactoPayComunicacao(List<PactoPayComunicacaoVO> listaPactoPayComunicacao) {
        this.listaPactoPayComunicacao = listaPactoPayComunicacao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public Date getDataExecucao() {
        return dataExecucao;
    }

    public void setDataExecucao(Date dataExecucao) {
        this.dataExecucao = dataExecucao;
    }
}

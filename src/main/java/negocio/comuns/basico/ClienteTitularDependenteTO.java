package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;
import negocio.facade.jdbc.basico.Cliente;

import java.util.Date;

/**
 * Created by <PERSON> on 26/11/2015.
 */
public class ClienteTitularDependenteTO extends SuperTO {
    private int codigo;
    private ClienteTitularDependenteTO clienteTitular;
    private ClienteVO clienteDependente;
    private ParentescoVO parentesco;
    private Date dataLancamento;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public ClienteTitularDependenteTO getClienteTitular() {
        if(clienteTitular == null)
            clienteTitular = new ClienteTitularDependenteTO();
        return clienteTitular;
    }

    public void setClienteTitular(ClienteTitularDependenteTO clienteTitular) {
        this.clienteTitular = clienteTitular;
    }

    public ClienteVO getClienteDependente() {
        if(clienteDependente == null)
            clienteDependente = new ClienteVO();
        return clienteDependente;
    }

    public void setClienteDependente(ClienteVO clienteDependente) {
        this.clienteDependente = clienteDependente;
    }

    public ParentescoVO getParentesco() {
        if(parentesco == null)
            parentesco = new ParentescoVO();
        return parentesco;
    }

    public void setParentesco(ParentescoVO parentesco) {
        this.parentesco = parentesco;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public ClienteTitularDependenteTO getClone(){
        ClienteTitularDependenteTO clone = new ClienteTitularDependenteTO();
        clone.setCodigo(this.codigo);
        clone.setClienteDependente(this.getClienteDependente());
        clone.setDataLancamento(this.dataLancamento);
        clone.setParentesco(this.getParentesco());
        clone.setClienteTitular(this.getClienteTitular());
        return clone;
    }
}

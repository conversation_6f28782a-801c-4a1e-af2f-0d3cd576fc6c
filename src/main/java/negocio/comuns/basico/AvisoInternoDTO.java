package negocio.comuns.basico;

import servicos.impl.login.UsuarioGeralDTO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AvisoInternoDTO {
    private Integer codigo;
    private Integer codigoAutor;
    private String autor;
    private String dataPublicacao;
    private String aviso;
    private Date dataAte;
    private Boolean visivelParaTodos;
    private List<Integer> usuarios = new ArrayList<>();
    private List<Integer> perfis = new ArrayList<>();

    public String getAutor() {
        return autor;
    }

    public void setAutor(String autor) {
        this.autor = autor;
    }

    public Integer getCodigoAutor() {
        return codigoAutor;
    }

    public void setCodigoAutor(Integer codigoAutor) {
        this.codigoAutor = codigoAutor;
    }

    public String getDataPublicacao() {
        return dataPublicacao;
    }

    public void setDataPublicacao(String dataPublicacao) {
        this.dataPublicacao = dataPublicacao;
    }

    public String getAviso() {
        return aviso;
    }

    public void setAviso(String aviso) {
        this.aviso = aviso;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataAte() {
        return dataAte;
    }

    public void setDataAte(Date dataAte) {
        this.dataAte = dataAte;
    }

    public Boolean getVisivelParaTodos() {
        return visivelParaTodos;
    }

    public void setVisivelParaTodos(Boolean visivelParaTodos) {
        this.visivelParaTodos = visivelParaTodos;
    }

    public List<Integer> getUsuarios() {
        return usuarios;
    }

    public void setUsuarios(List<Integer> usuarios) {
        this.usuarios = usuarios;
    }

    public List<Integer> getPerfis() {
        return perfis;
    }

    public void setPerfis(List<Integer> perfis) {
        this.perfis = perfis;
    }
}

package negocio.comuns.basico;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.taglibs.standard.tag.common.core.SetSupport;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

public class MovimentoContaCorrenteClienteComposicaoVO extends SuperVO {

    private Integer codigo;
    public Integer getCodigo() {
		return codigo;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	public Integer getMovimento() {
		return movimento;
	}

	public void setMovimento(Integer movimento) {
		this.movimento = movimento;
	}

	public Integer getMovpagamento() {
		return movpagamento;
	}

	public void setMovpagamento(Integer movpagamento) {
		this.movpagamento = movpagamento;
	}

	public List<MovPagamentoVO> getMovPagamentosVOs() {
		return movPagamentosVOs;
	}

	public void setMovPagamentosVOs(List<MovPagamentoVO> movPagamentosVOs) {
		this.movPagamentosVOs = movPagamentosVOs;
	}

	private Integer movimento;
    private Integer movpagamento;
  
    private List <MovPagamentoVO> movPagamentosVOs;

    /**
     * Construtor padrão da classe <code>MovimentoContaCorrenteCliente</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public MovimentoContaCorrenteClienteComposicaoVO() {
        super();
        inicializarDados();
    }



    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setMovimento(new Integer(0));
        setMovpagamento(new Integer(0));
        setMovPagamentosVOs(new ArrayList());
    }

   
}

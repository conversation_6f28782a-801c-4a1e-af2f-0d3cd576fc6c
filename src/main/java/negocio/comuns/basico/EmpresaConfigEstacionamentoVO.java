package negocio.comuns.basico;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.List;

public class EmpresaConfigEstacionamentoVO extends SuperVO {

    private String host;
    private String user;
    private String pass;
    private Integer port;
    private boolean enviaValor = true;
    private boolean enviaHorario = true;
    private boolean enviaTelefoneEmail = false;
    private String nomeArquivo = "alunoEstacionamento.txt";
    private String produtosAdicionar;
    @NaoControlarLogAlteracao
    private List<ProdutoVO> produtosVOAdicionar;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPass() {
        return pass;
    }

    public void setPass(String pass) {
        this.pass = pass;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public boolean isEnviaValor() {
        return enviaValor;
    }

    public void setEnviaValor(boolean enviaValor) {
        this.enviaValor = enviaValor;
    }

    public boolean isEnviaHorario() {
        return enviaHorario;
    }

    public void setEnviaHorario(boolean enviaHorario) {
        this.enviaHorario = enviaHorario;
    }

    public boolean isEnviaTelefoneEmail() {
        return enviaTelefoneEmail;
    }

    public void setEnviaTelefoneEmail(boolean enviaTelefoneEmail) {
        this.enviaTelefoneEmail = enviaTelefoneEmail;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getProdutosAdicionar() {
        if (produtosAdicionar == null) {
            produtosAdicionar = "";
        }
        return produtosAdicionar;
    }

    public void setProdutosAdicionar(String produtosAdicionar) {
        this.produtosAdicionar = produtosAdicionar;
    }

    public List<ProdutoVO> getProdutosVOAdicionar() {
        if (produtosVOAdicionar == null) {
            produtosVOAdicionar = new ArrayList<>();
        }
        return produtosVOAdicionar;
    }

    public void setProdutosVOAdicionar(List<ProdutoVO> produtosVOAdicionar) {
        this.produtosVOAdicionar = produtosVOAdicionar;
    }

    public String obterProdutosAdicionar() {
        StringBuilder sb = new StringBuilder("");
        if (!getProdutosVOAdicionar().isEmpty()) {
            for (ProdutoVO produtoVO : getProdutosVOAdicionar()) {
                sb.append(",").append(produtoVO.getCodigo());
            }
            sb.deleteCharAt(0);
        }
        return sb.toString();
    }

    public static void validarDados(EmpresaConfigEstacionamentoVO obj) throws ConsistirException {
        if(UteisValidacao.emptyString(obj.getHost())) {
            throw new ConsistirException("Utilizar o sistema de Estacionamento está habilitada na aba Integrar estacionamento. O campo \"FTP Server\" deve ser informado.");
        }

        if(UteisValidacao.emptyNumber(obj.getPort())) {
            throw new ConsistirException("Utilizar o sistema de Estacionamento está habilitada na aba Integrar estacionamento. O campo \"FTP porta\" deve ser informado.");
        }

        if(UteisValidacao.emptyString(obj.getNomeArquivo())) {
            throw new ConsistirException("Utilizar o sistema de Estacionamento está habilitada na aba Integrar estacionamento. O campo \"Endereço e nome do aquivo no servidor\" deve ser informado.");
        }
    }
}

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 25/11/13
 * Time: 13:35
 * To change this template use File | Settings | File Templates.
 */
public class ConsultaClienteTO extends SuperTO {

    private Integer codCliente = 0;
    private String matricula = "";
    private String nome = "";
    private String categoria = "";
    private Date inicioContrato = null;
    private Date terminoContrato = null;
    private String rg;
    private String situacaoCliente = "";
    private String situacaoContrato = "";
    private Boolean possuiFreePass = false;
    private String profissao="";
    private String fotoKey = "";
    private String empresaNome;
    private Integer titularPlanoCompartilhado = 0;

    public ClienteVO obterClienteVO() {
        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setCodigo(getCodCliente());
        return clienteVO;
    }

    public String getSituacao_Apresentar() {
        return getSituacaoCliente() + " " + getSituacaoContrato();
    }

    public Boolean getAtivo() {
        return situacaoCliente.equals("AT");
    }

    public Boolean getAtivoNormal() {
        return situacaoCliente.equals("AT") && situacaoContrato.equals("NO");
    }

    public Boolean getAtivoAvencer() {
        return situacaoCliente.equals("AT") && situacaoContrato.equals("AV");
    }

    public Boolean getAtivoAtestado() {
        return situacaoCliente.equals("AT") && situacaoContrato.equals("AE");
    }

    public Boolean getAtivoCarencia() {
        return situacaoCliente.equals("AT") && situacaoContrato.equals("CR");
    }

    public Boolean getTrancado() {
        return situacaoCliente.equals("TR");
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }
    
    public Boolean getTrancadoVencido() {
        return situacaoCliente.equals("TR") && situacaoContrato.equals("TV");
    }

    public Boolean getInativo() {
        return situacaoCliente.equals("IN");
    }

    public Boolean getInativoCancelamento() {
        return situacaoCliente.equals("IN") && situacaoContrato.equals("CA");
    }

    public Boolean getInativoDesistente() {
        return situacaoCliente.equals("IN") && situacaoContrato.equals("DE");
    }

    public Boolean getInativoVencido() {
        return situacaoCliente.equals("IN") && situacaoContrato.equals("VE");
    }

    public Boolean getVisitante() {
        return situacaoCliente.equals("VI");
    }

    public Boolean getVisitanteFreePass() {
        return (situacaoCliente.equals("VI") && situacaoContrato.equals("PL")) || (situacaoCliente.equals("IN") && isPossuiFreePass());
    }

    public Boolean getVisitanteDiaria() {
        return situacaoCliente.equals("VI") && situacaoContrato.equals("DI");
    }

    public Boolean getVisitanteAulaAvulsa() {
        return situacaoCliente.equals("VI") && situacaoContrato.equals("AA");
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public String getNomeMin() {
        return nome == null ? "" : nome.toLowerCase();
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public Date getInicioContrato() {
        return inicioContrato;
    }

    public void setInicioContrato(Date inicioContrato) {
        this.inicioContrato = inicioContrato;
    }

    public Date getTerminoContrato() {
        return terminoContrato;
    }

    public void setTerminoContrato(Date terminoContrato) {
        this.terminoContrato = terminoContrato;
    }

    public String getSituacaoCliente() {
        return situacaoCliente;
    }

    public void setSituacaoCliente(String situacaoCliente) {
        this.situacaoCliente = situacaoCliente;
    }

    public String getProfissao() {
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public boolean isPossuiFreePass() {
        return possuiFreePass;
    }

    public void setPossuiFreePass(boolean possuiFreePass) {
        this.possuiFreePass = possuiFreePass;
    }

    public Integer getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(Integer codCliente) {
        this.codCliente = codCliente;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }
    
    
    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }

    public String getEmpresaNome() {
        return empresaNome;
    }

    public void setEmpresaNome(String empresaNome) {
        this.empresaNome = empresaNome;
    }

    public Integer getTitularPlanoCompartilhado() {
        if (titularPlanoCompartilhado == null) {
            titularPlanoCompartilhado = 0;
        }
        return titularPlanoCompartilhado;
    }

    public void setTitularPlanoCompartilhado(Integer titularPlanoCompartilhado) {
        this.titularPlanoCompartilhado = titularPlanoCompartilhado;
    }

    public boolean isDependentePlanoCompartilhado() {
        return !UteisValidacao.emptyNumber(getTitularPlanoCompartilhado());
    }
}

package negocio.comuns.basico;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LexicalAnalyzer {

    private Map<String, List<String>> mapClassesMetodos = new HashMap<>();

    public static void main(String[] args) throws IOException {
        File directory = new File("src/main/java");

        LexicalAnalyzer lexicalAnalyzer = new LexicalAnalyzer();
        lexicalAnalyzer.scanDirectory(directory);
        lexicalAnalyzer.relatorio();
    }

    private void relatorio() {
        for (Map.Entry<String, List<String>> entry : mapClassesMetodos.entrySet()) {
            System.out.println(entry.getKey());
            for (String method : entry.getValue()) {
                System.out.println(method);
            }

            System.out.println("");
            System.out.println("");
        }

        System.out.println("Total de classes: " + mapClassesMetodos.keySet().size());
    }

    public void scanDirectory(File directory) throws IOException {
        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                scanDirectory(file); // chamada recursiva para percorrer subdiretórios
            } else {
                analyzeFile(file);
            }
        }
    }


    public void analyzeFile(File file) throws IOException {
        if (!file.getAbsolutePath().contains("controle") &&
                !file.getAbsolutePath().contains("comuns")) {
            return;
        }
        boolean isValueObjectFile = file.getName().contains("VO");
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            boolean isInsideMethod = false;
            boolean isFacadeMethod = false;
            String currentMethod = "";

            while ((line = reader.readLine()) != null) {
                // Dividir a linha em tokens usando um analisador léxico
                String[] tokens = line.split("\\s+");

                // Verificar se a linha contém a palavra-chave "public", "private" ou "protected"
                for (int i = 0; i < tokens.length - 2; i++) {
                    if (tokens[i].equals("public") || tokens[i].equals("private") || tokens[i].equals("protected")) {
                        // Verificar se a linha contém o nome de um método
                        int methodNameToken = 2;
                        if (tokens[i + 1].equals("static")) {
                            methodNameToken = 3;
                        }
                        String methodName = tokens[i + methodNameToken];
                        isInsideMethod = true;
                        currentMethod = methodName;

                        // Encontrar a linha de início do método
                        for (int j = i + 2; j < tokens.length; j++) {
                            if (tokens[j].equals("{")) {
                                break;
                            }
                        }
                    }
                }

                // Verificar se a linha contém uma chamada ao método "getFacade"
                if (isInsideMethod && line.contains("getFacade")) {
                    isFacadeMethod = true;
                }

                // Se a linha contiver uma chave de fechamento de método, verificar se houve chamada ao método "getFacade" no método atual
                if (line.contains("}")) {
                    isInsideMethod = false;
                    if (isFacadeMethod && (isValueObjectFile || currentMethod.startsWith("get") || currentMethod.startsWith("is"))) {
                        List<String> listMetodosClasse = mapClassesMetodos.computeIfAbsent(file.getAbsolutePath(), k -> new ArrayList<>());
                        listMetodosClasse.add(currentMethod);
                    }
                    isFacadeMethod = false;
                }
            }
        }
    }
}

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaVO;

public class NFCeEmitirTO extends SuperTO {

    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO;
    private NotaFiscalConsumidorEletronicaVO notaVO;
    private PessoaVO pessoaVO;
    private String idReferencia;
    private String nomePagador;
    private boolean fornecedor = false;

    public String getIdReferencia() {
        if (idReferencia == null) {
            idReferencia = "";
        }
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public NotaFiscalConsumidorEletronicaVO getNotaVO() {
        if (notaVO == null) {
            notaVO = new NotaFiscalConsumidorEletronicaVO();
        }
        return notaVO;
    }

    public void setNotaVO(NotaFiscalConsumidorEletronicaVO notaVO) {
        this.notaVO = notaVO;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public String getNomePagador() {
        if (nomePagador == null) {
            nomePagador = "";
        }
        return nomePagador;
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public boolean isFornecedor() {
        return fornecedor;
    }

    public void setFornecedor(boolean fornecedor) {
        this.fornecedor = fornecedor;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalVO() {
        if (configuracaoNotaFiscalVO == null) {
            configuracaoNotaFiscalVO = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalVO;
    }

    public void setConfiguracaoNotaFiscalVO(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) {
        this.configuracaoNotaFiscalVO = configuracaoNotaFiscalVO;
    }
}

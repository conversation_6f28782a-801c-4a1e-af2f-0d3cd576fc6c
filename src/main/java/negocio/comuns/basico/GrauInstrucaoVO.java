package negocio.comuns.basico;

import java.util.Date;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.*;
import negocio.comuns.utilitarias.ConsistirException;

public class GrauInstrucaoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;

    public GrauInstrucaoVO() {
        super();
        inicializarDados();
    }

    public static void validarDados(GrauInstrucaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Grau de Instrução) deve ser informado.");
        }
    }

    public void inicializarDados() {
        //setCodigo(new Integer(0));
        //setDescricao("");
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    @Override
    public Integer getCodigo() {
    	if (codigo == null) {
            codigo = new Integer(0);
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.IdentificadorMensagemSistema;

/**
 * Created by johny<PERSON> on 13/01/2017.
 */
public class ModeloMensagemSistemaVO extends SuperVO{

    private IdentificadorMensagemSistema identificador;

    private String titulo;

    private String mensagem;

    public IdentificadorMensagemSistema getIdentificador() {
        return identificador;
    }

    public void setIdentificador(IdentificadorMensagemSistema identificador) {
        this.identificador = identificador;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }
}

package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.io.File;
import java.util.Date;

/**
 * Created by ulisses on 14/11/2022.
 */
public class ColaboradorDocumentoRhVO  extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;

    @NaoControlarLogAlteracao
    private ColaboradorVO colaboradorVO;

    @NaoControlarLogAlteracao
    private UsuarioVO usuarioVO;

    private String descricaoDocumento;
    @NaoControlarLogAlteracao
    private String anexo;
    private String nomeAnexo;
    private Date dataLancamento;

    // Atributos transients
    private String extensaoAnexo;
    @NaoControlarLogAlteracao
    private File arquivoDocumentoRh;
    @NaoControlarLogAlteracao
    private String onCompleteAnexoDocumentoRh ="";
    @NaoControlarLogAlteracao
    private String urlCompletaAnexo;
    @NaoControlarLogAlteracao
    private Integer identificadorAnexo;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ColaboradorVO getColaboradorVO() {
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public String getAnexo() {
        return anexo;
    }

    public void setAnexo(String anexo) {
        this.anexo = anexo;
    }

    public String getNomeAnexo() {
        return nomeAnexo;
    }

    public void setNomeAnexo(String nomeAnexo) {
        this.nomeAnexo = nomeAnexo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getDescricaoDocumento() {
        return descricaoDocumento;
    }

    public void setDescricaoDocumento(String descricaoDocumento) {
        this.descricaoDocumento = descricaoDocumento;
    }

    public String getExtensaoAnexo() {
        return extensaoAnexo;
    }

    public void setExtensaoAnexo(String extensaoAnexo) {
        this.extensaoAnexo = extensaoAnexo;
    }

    public File getArquivoDocumentoRh() {
        return arquivoDocumentoRh;
    }

    public void setArquivoDocumentoRh(File arquivoDocumentoRh) {
        this.arquivoDocumentoRh = arquivoDocumentoRh;
    }

    public String getOnCompleteAnexoDocumentoRh() {
        return onCompleteAnexoDocumentoRh;
    }

    public void setOnCompleteAnexoDocumentoRh(String onCompleteAnexoDocumentoRh) {
        this.onCompleteAnexoDocumentoRh = onCompleteAnexoDocumentoRh;
    }

    public String getUrlCompletaAnexo() {
        return Uteis.getPaintFotoDaNuvem(anexo);
    }



    public String getDataLancamento_apresentar(){
        if (this.dataLancamento == null){
            return "";
        }
        return Calendario.getData(this.dataLancamento,"dd/MM/yyyy HH:mm:ss");
    }

    public Integer getIdentificadorAnexo() {
        return identificadorAnexo;
    }

    public void setIdentificadorAnexo(Integer identificadorAnexo) {
        this.identificadorAnexo = identificadorAnexo;
    }
}

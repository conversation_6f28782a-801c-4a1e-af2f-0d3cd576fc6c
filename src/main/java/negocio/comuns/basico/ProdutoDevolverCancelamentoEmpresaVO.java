package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoVO;

public class ProdutoDevolverCancelamentoEmpresaVO extends SuperVO {

    private Integer codigo;
    private Integer empresa;
    private Integer produto;
    private ProdutoVO produtoVO;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public ProdutoVO getProdutoVO() {
        return produtoVO;
    }

    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }
}

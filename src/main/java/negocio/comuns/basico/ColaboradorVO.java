package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.UsoCreditoPersonalEnum;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.plano.PlanoCategoriaVO;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;
import negocio.comuns.acesso.AcessoColaboradorVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.crm.ClienteOrganizadorCarteiraVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.lang.StringUtils;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.treino.ProfessorSintetico;
import servicos.propriedades.PropsService;

import javax.faces.model.SelectItem;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Reponsável por manter os dados da entidade Colaborador. Classe do tipo VO -
 * Value Object composta pelos atributos da entidade com visibilidade protegida
 * e os métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */

public class ColaboradorVO extends SuperVO {

    private static final String SEM_TELEFONE = "SEM TELEFONE";

    @ChavePrimaria
    protected Integer codigo = 0;
    protected String situacao = "";
    protected String codAcesso = "";
    protected String codAcessoAlternativo = "";

    protected Boolean bloquearAcessoSemCheckin = false;
    @ChaveEstrangeira
    @FKJson
    protected PessoaVO pessoa = new PessoaVO();
    // FOI RETIRADO E ADICIONADO O TIPO COLABORADOR FUNCIONARIO - PROJETO 182232
    protected Boolean funcionario = false;
    @NaoControlarLogAlteracao
    protected String visaoColaborador;
    @NaoControlarLogAlteracao
    protected Boolean apresentarRichModalErro;
    @NaoControlarLogAlteracao
    protected Boolean apresentarBotaoTransferirColaboradorEmpresa;
    @NaoControlarLogAlteracao
    protected String msgErroExisteColaborador;
    @ChaveEstrangeira
    @FKJson
    protected EmpresaVO empresa;
    @Lista
    @ListJson(clazz = TipoColaboradorVO.class)
    protected List<TipoColaboradorVO> listaTipoColaboradorVOs = new ArrayList<TipoColaboradorVO>();
    @NaoControlarLogAlteracao
    //atributo usado para validar qual colaborador foi marcado para lista o relatorio de indice de conversao.
    protected Boolean colaboradorEscolhidoIndiceConversao = false;
    @NaoControlarLogAlteracao
    protected Boolean colaboradorEscolhidoIndiceConversaoSessao = false;
    //atributo usado para validar qual colaborador foi marcado para lista o relatorio Pendencia.
    @NaoControlarLogAlteracao
    protected Boolean colaboradorEscolhidoPendencia = false;
    @NaoControlarLogAlteracao
    //atributo usado somente na organização de carteira
    protected List<ClienteOrganizadorCarteiraVO> listaVinculos;
    //atributo usado para validar qual colaborador foi marcado para listar o panel de Renovacao.
    @NaoControlarLogAlteracao
    protected Boolean colaboradorEscolhidoRenovacao = false;
    //atributo criado somente para informar qual tipo colaborador
    protected String tipoColaborador;
    @NaoControlarLogAlteracao
    protected AcessoColaboradorVO uaColaborador = new AcessoColaboradorVO();
    @NaoControlarLogAlteracao
    protected Boolean selecionado = false;
    private int diaVencimento = 0;
    @NaoControlarLogAlteracao
    private ProdutoVO produtoDefault;
    @NaoControlarLogAlteracao
    private Boolean colaboradorEscolhidoOperacoes = false;
    //atributo usado para validar qual colaborador foi marcado para listar a consulta de meta.
    @NaoControlarLogAlteracao
    private Boolean colaboradorEscolhido = false;
    @NaoControlarLogAlteracao
    private boolean possuiMetaFinanceira = false;
    private Double porcComissao = 0.0;
    private Double valorComissao;
    private Double porcComissaoIndicacaoEstudio = 0.0;
    private List<ModalidadeComissaoColaboradorVO> listaModalidadesComissaoColaboradorVO = new ArrayList<ModalidadeComissaoColaboradorVO>();
    private List<TurmaComissaoColaboradorVO> listaTurmaComissaoColaboradorVO = new ArrayList<TurmaComissaoColaboradorVO>();
    private List<AlunoComissaoColaboradorVO> listaAlunoComissaoColaboradorVOs = new ArrayList<AlunoComissaoColaboradorVO>();
    private String codigoAfiliadoVitio;

    @NaoControlarLogAlteracao
    private boolean configurarTempoEntreAcessos = false;
    private Integer tempoEntreAcessos = -1;

    private String corAgendaProfissional = "#000000";
    private String tokenGoogle = "";
    
    private Integer usoCreditosPersonal;
    private Integer saldoCreditoPersonal;
    private boolean emAtendimentoPersonal;

    private String cref = "";
    private Date validadeCref;
    
    @NaoControlarLogAlteracao
    private transient byte fotoPersonal[];
    @NaoControlarLogAlteracao
    private List<ColaboradorIndisponivelCrmVO> listaColaboradorIndisponivelCrmVOS;

    @NaoControlarLogAlteracao
    private List<ColaboradorIndisponivelCrmVO> listaOriginalColaboradorIndisponivelCrmVOS;

    @NaoControlarLogAlteracao
    private boolean validarCamposDinamico = false; // atributo transiente

    @NaoControlarLogAlteracao
    private Integer codigoUsuarioColaboradorTransfAgendaAntigo; // atributo transiente

    @NaoControlarLogAlteracao
    private Integer codigoUsuarioColaboradorTransfAgendaNovo; // atributo transiente

    @ChaveEstrangeira
    @FKJson
    private DepartamentoVO departamentoVO;

    private String emailMovidesk;
    private boolean sincronizadoMovidesk;
    private GrupoColaboradorVO grupoEmQueFoiSelecionado;
    private Boolean situacaoCheckBox;
    private String situacaoUser = "";
    private String departamentoApresentar;

    @NaoControlarLogAlteracao
    private List<AutorizacaoCobrancaColaboradorVO> autorizacoes;

    private List<ColaboradorModalidadeVO> colaboradorModalidadeVOS;
    private Integer cargaHoraria;

    @NaoControlarLogAlteracao
    private ColaboradorInfoRhVO colaboradorInfoRhVO;

    @NaoControlarLogAlteracao
    private List<ColaboradorDocumentoRhVO> listaDocumentoRh;

    private boolean permitirAcessoRedeEmpresa = false;
    private Date sincronizadoRedeEmpresa;
    private String publicIdProfessorMgb;

    /**
     * Construtor padrão da classe
     * <code>Colaborador</code>. Cria uma nova instância desta entidade,
     * inicializando automaticamente seus atributos (Classe VO).
     */
    public ColaboradorVO() {
        super();
        inicializarDados();
    }

    public ColaboradorVO(JSONObject o) {
        super();
        inicializarDados();
        try {
            setCodigo(o.getInt("codigo"));
            setCodAcesso(o.getString("codAcesso"));
            setCodAcessoAlternativo(o.getString("codAcessoAlternativo"));
            setSituacao(o.getString("situacao"));
            JSONObject pessoaJson = o.getJSONObject("pessoaObj");
            setPessoa(new PessoaVO(pessoaJson));
        } catch (Exception ignored) {
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>ColaboradorVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores vál   idos para os atributos.
     *
     * @throws Exception Se uma inconsistência for encontrada
     *                            aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     *                            ocorrido.
     */
    public static void validarDados(ColaboradorVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getEmpresa() == null || obj.getEmpresa().getCodigo().equals(0)) {
            throw new ConsistirException("O campo EMPRESA (Colaborador) deve ser informado.");
        }
        if (obj.pessoa.getNome().equals("")) {
            throw new ConsistirException("O campo NOME (Aba - Dados Pessoais) deve ser informado.");
        }
        if(Pattern.compile("\\d*[!@#$%&\\*\\{\\}\\[\\]\\?:><,|\\\\/\"\\+]\\d*").matcher(obj.pessoa.getNome()).find()){
             throw new ConsistirException("O campo NOME não pode conter caracteres especiais (!@#$%&*}{][?:><,\\|/\"+).");
        }
        if (obj.pessoa.getDataNasc() == null) {
            throw new ConsistirException("O campo DATA NASCIMENTO (Dados Pessoais) deve ser informado.");
        }
        if (!UteisValidacao.dataMenorDataAtual(obj.pessoa.getDataNasc())) {
            throw new ConsistirException("O campo DATA NASCIMENTO (Dados Pessoais) deve ser menor do que a data atual.");
        }
        if (obj.getSituacao().equals("")) {
            throw new ConsistirException("O campo SITUAÇÃO (Aba - Colaborador) deve ser informado.");
        }
        if (obj.getDiaVencimento() < 0) {
            throw new ConsistirException("O Dia do Vencimento não pode ser Menor que Zero.");
        }
        if (obj.getDiaVencimento() > 30) {
            throw new ConsistirException("Ultimo Dia valido do Mês é o dia 30.");
        }

        if (obj.getPorcComissao() < 0 || obj.getPorcComissao() > 100.0) {
            throw new ConsistirException("O porcentual informado deve estar entre 0 e 100.");
        }
    }

    public String getPessoa_Apresentar() {
        return getPessoa().getNome();
    }

    public String getPessoa_DataNascimento() {
        return getPessoa().getDataNasc_Apresentar();
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        setTipoColaborador(getTipoColaborador().toUpperCase());
        setSituacao(getSituacao().toUpperCase());
        setCodAcesso(getCodAcesso().toUpperCase());
        setCodAcessoAlternativo(getCodAcessoAlternativo().toUpperCase());
        pessoa.realizarUpperCaseDados();
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public final void inicializarDados() {
//        setEstado(new EstadoVO());
//        setPessoa(new PessoaVO());
//        setEmpresa(new EmpresaVO());
//        setCodigo(new Integer(0));
//        setSituacao("AT");
//        setTipoColaborador("");
//        setVisaoColaborador("");
//        setCodAcesso("");
//        setFuncionario(new Boolean(false));
//        setCodAcessoAlternativo("");
//        setBanco("");
//        setAgencia("");
//        setAgenciaDigito("");
//        setConta("");
//        setMsgErroExisteColaborador("");
//        setContaDigito("");
//        setIdentificadorParaCobranca("");
//        setEmpresa(new EmpresaVO());
//        setApresentarRichModalErro(new Boolean(false));
//        setPessoa(new PessoaVO());
//        setApresentarBotaoTransferirColaboradorEmpresa(new Boolean(true));
//        setListaTipoColaboradorVOs(new ArrayList());
//        setColaboradorEscolhidoPendencia(new Boolean(false));
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>TipoColaboradorVO</code> ao List
     * <code>TipoColaboradorVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>TipoColaborador</code> - getCodigo() - como identificador (key) do
     * objeto no List.
     *
     * @param obj Objeto da classe
     *            <code>TipoColaboradorVO</code> que será adiocionado ao Hashtable
     *            correspondente.
     */
    public void adicionarObjTipoColaboradorVOs(TipoColaboradorVO obj) throws Exception {
        TipoColaboradorVO.validarDados(obj);
        int index = 0;
        Iterator i = getListaTipoColaboradorVOs().iterator();
        while (i.hasNext()) {
            TipoColaboradorVO objExistente = (TipoColaboradorVO) i.next();
            if (objExistente.getDescricao().equals(obj.getDescricao())) {
                getListaTipoColaboradorVOs().set(index, obj);
                return;
            }
            index++;
        }
        getListaTipoColaboradorVOs().add(obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>TipoColaboradorVO</code> no List
     * <code>TipoColaboradorVOs</code>. Utiliza o atributo padrão de consulta da
     * classe
     * <code>TipoColaborador</code> - getCodigo() - como identificador (key) do
     * objeto no List.
     *
     * @param descricao Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjTipoColaboradorVOs(String descricao) throws Exception {
        int index = 0;
        Iterator i = getListaTipoColaboradorVOs().iterator();
        while (i.hasNext()) {
            TipoColaboradorVO objExistente = (TipoColaboradorVO) i.next();
            if (objExistente.getDescricao().equals(descricao)) {
                getListaTipoColaboradorVOs().remove(index);
                return;
            }
            index++;
        }
        //excluirObjSubordinadoOC
    }

    /**
     * Retorna o objeto da classe
     * <code>Pessoa</code> relacionado com (
     * <code>Colaborador</code>).
     */
    public PessoaVO getPessoa() {
        if (pessoa == null) {
            pessoa = new PessoaVO();
        }
        return (pessoa);
    }

    /**
     * Define o objeto da classe
     * <code>Pessoa</code> relacionado com (
     * <code>Colaborador</code>).
     */
    public void setPessoa(PessoaVO obj) {
        this.pessoa = obj;
    }


    // FOI RETIRADO E ADICIONADO O TIPO COLABORADOR FUNCIONARIO - PROJETO 182232
    public Boolean getFuncionario() {
        if (funcionario == null) {
            funcionario = false;
        }
        return (funcionario);
    }

    public void setFuncionario(Boolean funcionario) {
        this.funcionario = funcionario;
    }

    public Boolean isFuncionario() {
        if (funcionario == null) {
            funcionario = false;
        }
        return (funcionario);
    }

    public String getCodAcesso() {
        if (codAcesso == null) {
            codAcesso = "";
        }
        return (codAcesso);
    }

    public void setCodAcesso(String codAcesso) {
        this.codAcesso = codAcesso;
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return (situacao);
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo
     * com um domínio específico. Com base no valor de armazenamento do atributo
     * esta função é capaz de retornar o de apresentação correspondente. Útil
     * para campos como sexo, escolaridade, etc.
     */
    public String getSituacao_Apresentar() {
        if (situacao == null) {
            situacao = "";
        }
        if (situacao.equals("AT")) {
            return "Ativo";
        }
        if (situacao.equals("NA")) {
            return "Inativo";
        }
        if (situacao.equals("IN")) {
            return "Inativo";
        }
        return (situacao);
    }

    public Boolean getSituacaoCheckBox() {
        if (situacaoCheckBox == null) {
            situacaoCheckBox = false;
        }
        return situacaoCheckBox;
    }

    public void setSituacaoCheckBox(Boolean situacaoCheckBox) {
        this.situacaoCheckBox = situacaoCheckBox;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public boolean isInativo() {
        if (situacao != null && situacao.equals("NA")) {
            return true;
        }
        return false;
    }
    public void setInativo(boolean situacaoBoolean){
        situacao = situacaoBoolean ? "AT" : "NA";
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCodAcessoAlternativo() {
        if (codAcessoAlternativo == null) {
            codAcessoAlternativo = "";
        }
        return codAcessoAlternativo;
    }

    public void setCodAcessoAlternativo(String codAcessoAlternativo) {
        this.codAcessoAlternativo = codAcessoAlternativo;
    }

    public String getTipoColaborador() {
        if (tipoColaborador == null) {
            tipoColaborador = "";
        }
        return (tipoColaborador);
    }

    public void setTipoColaborador(String tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo
     * com um domínio específico. Com base no valor de armazenamento do atributo
     * esta função é capaz de retornar o de apresentação correspondente. Útil
     * para campos como sexo, escolaridade, etc.
     */
    public String getTipoColaborador_Apresentar() {
        String tipoColab = "";

        for (TipoColaboradorVO tipo : getListaTipoColaboradorVOs()) {
            if (!tipoColab.equals("")) {
                tipoColab += ", ";
            }
            tipoColab += tipo.getDescricao_Apresentar();
        }

        return tipoColab;
    }

    public Boolean getApresentarRichModalErro() {
        if (apresentarRichModalErro == null) {
            apresentarRichModalErro = false;
        }
        return apresentarRichModalErro;
    }

    public void setApresentarRichModalErro(Boolean apresentarRichModalErro) {
        this.apresentarRichModalErro = apresentarRichModalErro;
    }

    public String getMsgErroExisteColaborador() {
        if (msgErroExisteColaborador == null) {
            msgErroExisteColaborador = "";
        }
        return msgErroExisteColaborador;
    }

    public void setMsgErroExisteColaborador(String msgErroExisteColaborador) {
        this.msgErroExisteColaborador = msgErroExisteColaborador;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Boolean getApresentarBotaoTransferirColaboradorEmpresa() {
        if (apresentarBotaoTransferirColaboradorEmpresa == null) {
            apresentarBotaoTransferirColaboradorEmpresa = false;
        }
        return apresentarBotaoTransferirColaboradorEmpresa;
    }

    public void setApresentarBotaoTransferirColaboradorEmpresa(Boolean apresentarBotaoTransferirColaboradorEmpresa) {
        this.apresentarBotaoTransferirColaboradorEmpresa = apresentarBotaoTransferirColaboradorEmpresa;
    }

    public List<TipoColaboradorVO> getListaTipoColaboradorVOs() {
        if (listaTipoColaboradorVOs == null) {
            listaTipoColaboradorVOs = new ArrayList<TipoColaboradorVO>();
        }
        return listaTipoColaboradorVOs;
    }

    public void setListaTipoColaboradorVOs(List<TipoColaboradorVO> listaTipoColaboradorVOs) {
        this.listaTipoColaboradorVOs = listaTipoColaboradorVOs;
    }

    public String getVisaoColaborador() {
        if (visaoColaborador == null) {
            visaoColaborador = "";
        }
        return visaoColaborador;
    }

    public void setVisaoColaborador(String visaoColaborador) {
        this.visaoColaborador = visaoColaborador;
    }

    public Boolean getColaboradorEscolhidoIndiceConversao() {
        if (colaboradorEscolhidoIndiceConversao == null) {
            colaboradorEscolhidoIndiceConversao = false;
        }
        return colaboradorEscolhidoIndiceConversao;
    }

    public void setColaboradorEscolhidoIndiceConversao(Boolean colaboradorEscolhidoIndiceConversao) {
        this.colaboradorEscolhidoIndiceConversao = colaboradorEscolhidoIndiceConversao;
    }

    public Boolean getColaboradorEscolhidoIndiceConversaoSessao() {
        if (colaboradorEscolhidoIndiceConversaoSessao == null) {
            colaboradorEscolhidoIndiceConversaoSessao = false;
        }
        return colaboradorEscolhidoIndiceConversaoSessao;
    }

    public void setColaboradorEscolhidoIndiceConversaoSessao(Boolean colaboradorEscolhidoIndiceConversaoSessao) {
        this.colaboradorEscolhidoIndiceConversaoSessao = colaboradorEscolhidoIndiceConversaoSessao;
    }

    public Boolean getMostrarVisaoColaborador() {
        return !getVisaoColaborador().equals("") && getVisaoColaborador().equals(TipoColaboradorEnum.CONSULTOR.getSigla());
    }

    public Boolean getMostrarVisaoProfessor() {
        return !getVisaoColaborador().equals("") && getVisaoColaborador().equals(TipoColaboradorEnum.PROFESSOR.getSigla());
    }

    public List<ClienteOrganizadorCarteiraVO> getListaVinculos() {
        if (listaVinculos == null) {
            setListaVinculos(new ArrayList<ClienteOrganizadorCarteiraVO>());
        }
        return listaVinculos;
    }

    public void setListaVinculos(List<ClienteOrganizadorCarteiraVO> listaVinculos) {
        this.listaVinculos = listaVinculos;
    }

    public Boolean getColaboradorEscolhidoPendencia() {
        if (colaboradorEscolhidoPendencia == null) {
            colaboradorEscolhidoPendencia = false;
        }
        return colaboradorEscolhidoPendencia;
    }

    public void setColaboradorEscolhidoPendencia(Boolean colaboradorEscolhidoPendencia) {
        this.colaboradorEscolhidoPendencia = colaboradorEscolhidoPendencia;
    }

    public Boolean getColaboradorEscolhidoRenovacao() {
        if (colaboradorEscolhidoRenovacao == null) {
            colaboradorEscolhidoRenovacao = false;
        }
        return colaboradorEscolhidoRenovacao;
    }

    public void setColaboradorEscolhidoRenovacao(Boolean colaboradorEscolhidoRenovacao) {
        this.colaboradorEscolhidoRenovacao = colaboradorEscolhidoRenovacao;
    }

    public List<SelectItem> getListaSelectItemTipoColaborador() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        Iterator i = getListaTipoColaboradorVOs().iterator();
        while (i.hasNext()) {
            TipoColaboradorVO obj = (TipoColaboradorVO) i.next();
            itens.add(new SelectItem(obj.getDescricao(), obj.getDescricao_Apresentar()));
        }
        return itens;
    }

    public Boolean getColaboradorEscolhido() {
        if (colaboradorEscolhido == null) {
            colaboradorEscolhido = false;
        }
        return colaboradorEscolhido;
    }

    public void setColaboradorEscolhido(Boolean colaboradorEscolhido) {
        this.colaboradorEscolhido = colaboradorEscolhido;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof ColaboradorVO) {
            ColaboradorVO aux = (ColaboradorVO) obj;
            return aux.getCodigo().intValue() == this.getCodigo().intValue();
        }
        return false;
    }

    public AcessoColaboradorVO getUaColaborador() {
        return uaColaborador;
    }

    public void setUaColaborador(AcessoColaboradorVO uaColaborador) {
        this.uaColaborador = uaColaborador;
    }

    public int getDiaVencimento() {
        return diaVencimento;
    }

    public void setDiaVencimento(int diaVencimento) {
        this.diaVencimento = diaVencimento;
    }

    public ProdutoVO getProdutoDefault() {
        if (produtoDefault == null) {
            produtoDefault = new ProdutoVO();
        }
        return produtoDefault;
    }
    public void removerProdutoDefault() {
        setProdutoDefault(new ProdutoVO());
    }

    public void setProdutoDefault(ProdutoVO produtoDefault) {
        this.produtoDefault = produtoDefault;
    }

    public Boolean getColaboradorEscolhidoOperacoes() {
        return colaboradorEscolhidoOperacoes;
    }

    public void setColaboradorEscolhidoOperacoes(Boolean colaboradorEscolhidoOperacoes) {
        this.colaboradorEscolhidoOperacoes = colaboradorEscolhidoOperacoes;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public boolean getPossuiMetaFinanceira() {
        return possuiMetaFinanceira;
    }

    public void setPossuiMetaFinanceira(boolean possuiMetaFinanceira) {
        this.possuiMetaFinanceira = possuiMetaFinanceira;
    }

    public Double getPorcComissao() {
        return porcComissao;
    }

    public void setPorcComissao(Double porcComissao) {
        this.porcComissao = porcComissao;
    }

    public Double getPorcComissaoIndicacaoEstudio() {
        return porcComissaoIndicacaoEstudio;
    }

    public void setPorcComissaoIndicacaoEstudio(Double porcComissaoIndicacaoEstudio) {
        this.porcComissaoIndicacaoEstudio = porcComissaoIndicacaoEstudio;
    }

    public String getPorcComissao_Apresentar() {
        return Formatador.formatarValorNumerico(porcComissao, "#0.00") + "%";
    }

    public String getValorComissao_Apresentar() {
        return Formatador.formatarValorMonetario(this.getValorComissao());
    }

    public List<ModalidadeComissaoColaboradorVO> getListaModalidadesComissaoColaboradorVO() {
        return listaModalidadesComissaoColaboradorVO;
    }

    public void setListaModalidadesComissaoColaboradorVO(List<ModalidadeComissaoColaboradorVO> listaModalidadesComissaoColaboradorVO) {
        this.listaModalidadesComissaoColaboradorVO = listaModalidadesComissaoColaboradorVO;
    }

    public List<TurmaComissaoColaboradorVO> getListaTurmaComissaoColaboradorVO() {
        return listaTurmaComissaoColaboradorVO;
    }

    public void setListaTurmaComissaoColaboradorVO(List<TurmaComissaoColaboradorVO> listaTurmaComissaoColaboradorVO) {
        this.listaTurmaComissaoColaboradorVO = listaTurmaComissaoColaboradorVO;
    }

    public List<AlunoComissaoColaboradorVO> getListaAlunoComissaoColaboradorVOs() {
        return listaAlunoComissaoColaboradorVOs;
    }

    public void setListaAlunoComissaoColaboradorVOs(List<AlunoComissaoColaboradorVO> listaAlunoComissaoColaboradorVOs) {
        this.listaAlunoComissaoColaboradorVOs = listaAlunoComissaoColaboradorVOs;
    }

    public String getCorAgendaProfissional() {
        return corAgendaProfissional;
    }

    public void setCorAgendaProfissional(String corAgendaProfissional) {
        this.corAgendaProfissional = corAgendaProfissional;
    }

    public boolean temTipoColaborador(final String descricao) {
        List<TipoColaboradorVO> l = getListaTipoColaboradorVOs();
        for (TipoColaboradorVO tipo : l) {
            if (tipo.getDescricao().equalsIgnoreCase(descricao)) {
                return true;
            }
        }
        return false;
    }

    public ProfessorSintetico toProfessorSintetico() {
        ProfessorSintetico p = new ProfessorSintetico();
        p.setAtivo(this.situacao != null && this.situacao.equals("AT"));
        p.setCodigoPessoa(this.pessoa.getCodigo());
        p.setNome(this.pessoa.getNome());
        p.setCodigoColaborador(this.getCodigo());
        p.setPersonal(this.temTipoColaborador(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla()) || this.temTipoColaborador(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla()));
        p.setProfessorTW(this.temTipoColaborador(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla()));
        p.setPosPago(this.usoCreditosPersonal != null && this.usoCreditosPersonal.equals(UsoCreditoPersonalEnum.PERMITIR_POS_PAGO.getCodigo()));
        p.setPersonaInterno(this.temTipoColaborador(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla()));
        p.setCref(this.getCref());
        StringBuilder emails = new StringBuilder();
        for(EmailVO email : getPessoa().getEmailVOs()){
            if(emails.indexOf(email.getEmail()) < 0){
                emails.append(email.getEmail()).append(";");
            }
        }
        if(pessoa.getFotoKey()!=null){
            p.setUriImagem(PropsService.getPropertyValue(PropsService.urlFotosNuvem)+"/"+pessoa.getFotoKey());
            p.setFotoKey(pessoa.getFotoKey());
        }else{
            p.setUriImagem(String.format("%s/%s",
                    PropsService.getPropertyValue(PropsService.urlFotosNuvem),
                    "fotoPadrao.jpg"));
            p.setFotoKey("fotoPadrao.jpg");
        }

        p.setEmail(emails.toString());
        return p;
    }

    public JSONObject toJSON() {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                o.put(field.getName(), field.get(this));
            }
            o.put("pessoaObj", getPessoa().toJSON());
        } catch (Exception ignored) {
        }
        return o;
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public boolean igual(ColaboradorVO colaboradorVO) {
        return this.getCodigo().equals(colaboradorVO.getCodigo());
    }

    public String getTokenGoogle() {
        return tokenGoogle;
    }

    public void setTokenGoogle(String tokenGoogle) {
        this.tokenGoogle = tokenGoogle;
    }

    public Integer getUsoCreditosPersonal() {
        return usoCreditosPersonal;
    }

    public void setUsoCreditosPersonal(Integer usoCreditosPersonal) {
        this.usoCreditosPersonal = usoCreditosPersonal;
    }

    
    public static void main(String ... args){
        try {
            System.out.println(TreinoWSConsumer.inserirCreditos("pulse", 1, 5, 336, null));
        } catch (Exception e) {
            e.printStackTrace();
        }
        
    }

    public Integer getSaldoCreditoPersonal() {
        if(saldoCreditoPersonal == null){
            saldoCreditoPersonal = 0;
        }
        return saldoCreditoPersonal;
    }

    public void setSaldoCreditoPersonal(Integer saldoCreditoPersonal) {
        this.saldoCreditoPersonal = saldoCreditoPersonal;
    }

    public boolean isEmAtendimentoPersonal() {
        return emAtendimentoPersonal;
    }

    public void setEmAtendimentoPersonal(boolean emAtendimentoPersonal) {
        this.emAtendimentoPersonal = emAtendimentoPersonal;
    }


    public Integer getTempoEntreAcessos() {
        return tempoEntreAcessos;
    }

    public void setTempoEntreAcessos(Integer tempoEntreAcessos) {
        this.tempoEntreAcessos = tempoEntreAcessos;
    }

    public boolean isConfigurarTempoEntreAcessos() {
        return configurarTempoEntreAcessos;
    }

    public void setConfigurarTempoEntreAcessos(boolean configurarTempoEntreAcessos) {
        this.configurarTempoEntreAcessos = configurarTempoEntreAcessos;
    }

    public byte[] getFotoPersonal() {
        return fotoPersonal;
    }

    public void setFotoPersonal(byte[] fotoPersonal) {
        this.fotoPersonal = fotoPersonal;
    }

    public String getCodigoAfiliadoVitio() {
        return codigoAfiliadoVitio;
    }

    public void setCodigoAfiliadoVitio(String codigoAfiliadoVitio) {
        this.codigoAfiliadoVitio = codigoAfiliadoVitio;
    }

    public String getCref() {
        if (cref == null) {
            cref = "";
        }
        return (cref);
    }

    public void setCref(String cref) {
        this.cref = cref;
    }


    public List<ColaboradorIndisponivelCrmVO> getListaColaboradorIndisponivelCrmVOS() {
        if (listaColaboradorIndisponivelCrmVOS == null){
            listaColaboradorIndisponivelCrmVOS = new ArrayList<ColaboradorIndisponivelCrmVO>(0);
        }
        return listaColaboradorIndisponivelCrmVOS;
    }

    public void setListaColaboradorIndisponivelCrmVOS(List<ColaboradorIndisponivelCrmVO> listaColaboradorIndisponivelCrmVOS) {
        this.listaColaboradorIndisponivelCrmVOS = listaColaboradorIndisponivelCrmVOS;
    }



    public List<ColaboradorIndisponivelCrmVO> getListaOriginalColaboradorIndisponivelCrmVOS() {
        return listaOriginalColaboradorIndisponivelCrmVOS;
    }

    public void setListaOriginalColaboradorIndisponivelCrmVOS(List<ColaboradorIndisponivelCrmVO> listaOriginalColaboradorIndisponivelCrmVOS) {
        this.listaOriginalColaboradorIndisponivelCrmVOS = listaOriginalColaboradorIndisponivelCrmVOS;
    }

    public boolean isValidarCamposDinamico() {
        return validarCamposDinamico;
    }

    public void setValidarCamposDinamico(boolean validarCamposDinamico) {
        this.validarCamposDinamico = validarCamposDinamico;
    }

    public DepartamentoVO getDepartamentoVO() {
        if (departamentoVO == null) {
            departamentoVO = new DepartamentoVO();
        }
        return departamentoVO;
    }

    public String getDepartamentoApresentar() {
        if(UteisValidacao.emptyString(departamentoApresentar)){
            departamentoApresentar = "";
        }
        return departamentoApresentar;
    }

    public void setDepartamentoApresentar(String departamentoApresentar) {
        this.departamentoApresentar = departamentoApresentar;
    }

    public void setDepartamentoVO(DepartamentoVO departamentoVO) {
        this.departamentoVO = departamentoVO;
    }

    public Integer getCodigoUsuarioColaboradorTransfAgendaAntigo() {
        return codigoUsuarioColaboradorTransfAgendaAntigo;
    }

    public void setCodigoUsuarioColaboradorTransfAgendaAntigo(Integer codigoUsuarioColaboradorTransfAgendaAntigo) {
        this.codigoUsuarioColaboradorTransfAgendaAntigo = codigoUsuarioColaboradorTransfAgendaAntigo;
    }

    public Integer getCodigoUsuarioColaboradorTransfAgendaNovo() {
        return codigoUsuarioColaboradorTransfAgendaNovo;
    }

    public void setCodigoUsuarioColaboradorTransfAgendaNovo(Integer codigoUsuarioColaboradorTransfAgendaNovo) {
        this.codigoUsuarioColaboradorTransfAgendaNovo = codigoUsuarioColaboradorTransfAgendaNovo;
    }

    public ColaboradorWS toWS() {
        ColaboradorWS colaboradorWS = new ColaboradorWS();
        colaboradorWS.setCodigo(this.codigo);
        colaboradorWS.setNome(this.getPessoa_Apresentar());
        return colaboradorWS;
    }

    public String getEmailMovidesk() {
        return emailMovidesk;
    }

    public void setEmailMovidesk(String emailMovidesk) {
        this.emailMovidesk = emailMovidesk;
    }

    public String getPrimeiroTelefoneNaoNulo() {
        for (final TelefoneVO telefoneVO : getPessoa().getTelefoneVOs()) {
            if (StringUtils.isNotBlank(telefoneVO.getNumero())) {
                return telefoneVO.getNumero();
            }
        }

        return SEM_TELEFONE;
    }

    public String getPrimeiroTelefoneNaoNuloWeHelp() {
        for (final TelefoneVO telefoneVO : getPessoa().getTelefoneVOs()) {
            if (StringUtils.isNotBlank(telefoneVO.getNumero())) {
                return "55"+ Uteis.removerMascara(telefoneVO.getNumero().replaceAll("\\(", "").replaceAll("\\)", ""));
            }
        }

        return "";
    }

    public void setSincronizadoMovidesk(boolean sincronizadoMovidesk) {
        this.sincronizadoMovidesk = sincronizadoMovidesk;
    }

    public boolean getSincronizadoMovidesk() {
        return sincronizadoMovidesk;
    }

    public GrupoColaboradorVO getGrupoEmQueFoiSelecionado() {
        if(grupoEmQueFoiSelecionado == null){
            grupoEmQueFoiSelecionado = new GrupoColaboradorVO();
        }
        return grupoEmQueFoiSelecionado;
    }

    public void setGrupoEmQueFoiSelecionado(GrupoColaboradorVO grupoEmQueFoiSelecionado) {
        this.grupoEmQueFoiSelecionado = grupoEmQueFoiSelecionado;
    }

    public List<AutorizacaoCobrancaColaboradorVO> getAutorizacoes() {
        if (autorizacoes == null){
            autorizacoes = new ArrayList<>();
        }
        return autorizacoes;
    }

    public void setAutorizacoes(List<AutorizacaoCobrancaColaboradorVO> autorizacoes) {
        this.autorizacoes = autorizacoes;
    }

    public Boolean getBloquearAcessoSemCheckin() {
        if(bloquearAcessoSemCheckin == null){
            bloquearAcessoSemCheckin = Boolean.FALSE;
        }
        return bloquearAcessoSemCheckin;
    }

    public void setBloquearAcessoSemCheckin(Boolean bloquearAcessoSemCheckin) {
        this.bloquearAcessoSemCheckin = bloquearAcessoSemCheckin;
    }

    public List<ColaboradorModalidadeVO> getColaboradorModalidadeVOS() {
        if(colaboradorModalidadeVOS == null){
            colaboradorModalidadeVOS = new ArrayList<>();
        }
        return colaboradorModalidadeVOS;
    }

    public void setColaboradorModalidadeVOS(List<ColaboradorModalidadeVO> colaboradorModalidadeVOS) {
        this.colaboradorModalidadeVOS = colaboradorModalidadeVOS;
    }

    public void adicionarObjColaboradorModalidadesVOs(ColaboradorModalidadeVO obj) throws Exception {
        ColaboradorModalidadeVO.validarDados(obj);
        int index = 0;
        Iterator i = getColaboradorModalidadeVOS().iterator();
        while (i.hasNext()) {
            ColaboradorModalidadeVO objExistente = (ColaboradorModalidadeVO) i.next();
            if (objExistente.getModalidadeVO().getCodigo().equals(obj.getModalidadeVO().getCodigo())) {
                getColaboradorModalidadeVOS().set(index, obj);
                return;
            }
            index++;
        }
        getColaboradorModalidadeVOS().add(obj);
    }


    public void excluirObjColaboradorModalidadesVOs(Integer modalidade) throws Exception {
        int index = 0;
        Iterator i = getColaboradorModalidadeVOS().iterator();
        while (i.hasNext()) {
            ColaboradorModalidadeVO objExistente = (ColaboradorModalidadeVO) i.next();
            if (objExistente.getModalidadeVO().getCodigo().equals(modalidade)) {
                getColaboradorModalidadeVOS().remove(index);
                return;
            }
            index++;
        }
    }

    public Integer getCargaHoraria() {
        if (cargaHoraria == null) {
            cargaHoraria = 0;
        }
        return cargaHoraria;
    }

    public void setCargaHoraria(Integer cargaHoraria) {
        this.cargaHoraria = cargaHoraria;
    }

    public ColaboradorInfoRhVO getColaboradorInfoRhVO() {
        if (this.colaboradorInfoRhVO == null){
            this.colaboradorInfoRhVO = new ColaboradorInfoRhVO();
            this.colaboradorInfoRhVO.setColaboradorVO(this);
            this.colaboradorInfoRhVO.setObjetoVOAntesAlteracao(new ColaboradorInfoRhVO());
        }
        return colaboradorInfoRhVO;
    }

    public void setColaboradorInfoRhVO(ColaboradorInfoRhVO colaboradorInfoRhVO) {
        this.colaboradorInfoRhVO = colaboradorInfoRhVO;
    }

    public List<ColaboradorDocumentoRhVO> getListaDocumentoRh() {
        return listaDocumentoRh;
    }

    public void setListaDocumentoRh(List<ColaboradorDocumentoRhVO> listaDocumentoRh) {
        this.listaDocumentoRh = listaDocumentoRh;
    }

    public Date getValidadeCref() {
        return validadeCref;
    }

    public void setValidadeCref(Date validadeCref) {
        this.validadeCref = validadeCref;
    }

    public boolean isPermitirAcessoRedeEmpresa() {
        return permitirAcessoRedeEmpresa;
    }

    public void setPermitirAcessoRedeEmpresa(boolean permitirAcessoRedeEmpresa) {
        this.permitirAcessoRedeEmpresa = permitirAcessoRedeEmpresa;
    }

    public Date getSincronizadoRedeEmpresa() {
        return sincronizadoRedeEmpresa;
    }

    public void setSincronizadoRedeEmpresa(Date sincronizadoRedeEmpresa) {
        this.sincronizadoRedeEmpresa = sincronizadoRedeEmpresa;
    }

    public String getPublicIdProfessorMgb() {
        return publicIdProfessorMgb;
    }

    public void setPublicIdProfessorMgb(String publicIdProfessorMgb) {
        this.publicIdProfessorMgb = publicIdProfessorMgb;
    }

    public Double getValorComissao() {
        if (valorComissao == null) {
            valorComissao = 0.0;
        }
        return valorComissao;
    }

    public void setValorComissao(Double valorComissao) {
        this.valorComissao = valorComissao;
    }
}

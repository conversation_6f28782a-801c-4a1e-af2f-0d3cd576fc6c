/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.MovParcelaTO;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.propriedades.PropsService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@XStreamAlias("Gerente")
public class GerenteTO extends SuperTO {

    private String nome;
    private String urlFoto;
    private Integer pessoa;

    public GerenteTO() {
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUrlFoto() {
        if (urlFoto == null) {
            urlFoto = "";
        }
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public Integer getPessoa() {
        if (pessoa == null) {
            pessoa = 0;
        }
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String obterUrlFoto(String chave) {
        if (!UteisValidacao.emptyNumber(getPessoa())) {
            final String url = "%s/av?key=%s&av_pes=%s";
            return String.format(url, PropsService.getPropertyValue(PropsService.urlAplicacao), chave, getPessoa());
        }
        return "";
    }
}

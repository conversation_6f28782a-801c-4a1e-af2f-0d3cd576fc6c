/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Classe VO responsável pelo relacionamento entre turma e colaborador em comissão
 * <AUTHOR>
 */
public class AlunoComissaoColaboradorVO extends SuperVO {

    private Integer codigo;
    private PessoaVO pessoaVO = new PessoaVO();
    private Double porcComissao = 0.0;
    private Double valorComissao;
    private ColaboradorVO colaboradorVO=new ColaboradorVO();

    public static void validarDados(AlunoComissaoColaboradorVO alunoComissaoColaboradorVO) throws Exception {
        if (alunoComissaoColaboradorVO.getPessoaVO().getCodigo() == 0) {
            throw new Exception("Informe a TURMA");
        }
        if(UteisValidacao.emptyNumber(alunoComissaoColaboradorVO.getPorcComissao()) &&
                UteisValidacao.emptyNumber(alunoComissaoColaboradorVO.getValorComissao())){
            throw new Exception("Informe a PORCENTAGEM ou VALOR FIXO");
        }
        if (alunoComissaoColaboradorVO.getPorcComissao() != null &&
                alunoComissaoColaboradorVO.getPorcComissao() > 100) {
            throw new Exception("A PORCENTAGEM deve ser menor que 100%");
        }
        if (alunoComissaoColaboradorVO.getValorComissao() != null &&
                alunoComissaoColaboradorVO.getValorComissao() < 0) {
            throw new Exception("O VALOR FIXO deve ser superior ou igual a 0");
        }
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the porcComissao
     */
    public Double getPorcComissao() {
        if (porcComissao == null) {
            porcComissao = 0.0;
        }
        return porcComissao;
    }

    /**
     * @param porcComissao the porcComissao to set
     */
    public void setPorcComissao(Double porcComissao) {
        this.porcComissao = porcComissao;
    }

    /**
     * @return the pessoaVO
     */
    public PessoaVO getPessoaVO() {
        if(pessoaVO==null){
            new PessoaVO();
        }
        return pessoaVO;
    }

    /**
     * @param pessoaVO the pessoaVO to set
     */
    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    /**
     * @return the colaboradorVO
     */
    public ColaboradorVO getColaboradorVO() {
        if(colaboradorVO==null){
            colaboradorVO = new ColaboradorVO();
        }
        return colaboradorVO;
    }

    /**
     * @param colaboradorVO the colaboradorVO to set
     */
    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public Double getValorComissao() {
        if (valorComissao == null) {
            valorComissao = 0.0;
        }
        return valorComissao;
    }

    public void setValorComissao(Double valorComissao) {
        this.valorComissao = valorComissao;
    }
}

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.CausaSugestaoEnum;

public class SugestaoFamiliaVO extends SuperVO {

    private Integer codigo;
    private CausaSugestaoEnum causa;
    private String codigos;
    private String situacoes;
    private Boolean aceito = Boolean.FALSE;
    private Boolean negado = Boolean.FALSE;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public CausaSugestaoEnum getCausa() {
        return causa;
    }

    public void setCausa(CausaSugestaoEnum causa) {
        this.causa = causa;
    }

    public String getCodigos() {
        return codigos;
    }

    public void setCodigos(String codigos) {
        this.codigos = codigos;
    }

    public Boolean getNegado() {
        return negado;
    }

    public void setNegado(Boolean negado) {
        this.negado = negado;
    }

    public String getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(String situacoes) {
        this.situacoes = situacoes;
    }

    public Boolean getAceito() {
        return aceito;
    }

    public void setAceito(Boolean aceito) {
        this.aceito = aceito;
    }
}

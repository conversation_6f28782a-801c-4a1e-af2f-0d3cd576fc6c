package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.Date;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class ProcessoImportacaoItemVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private ProcessoImportacaoVO processoImportacaoVO;
    private String dados;

    public ProcessoImportacaoItemVO(){
    }

    public ProcessoImportacaoItemVO(ProcessoImportacaoVO processoImportacaoVO, String dados) {
        this.processoImportacaoVO = processoImportacaoVO;
        this.dados = dados;
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProcessoImportacaoVO getProcessoImportacaoVO() {
        if (processoImportacaoVO == null) {
            processoImportacaoVO = new ProcessoImportacaoVO();
        }
        return processoImportacaoVO;
    }

    public void setProcessoImportacaoVO(ProcessoImportacaoVO processoImportacaoVO) {
        this.processoImportacaoVO = processoImportacaoVO;
    }

    public String getDados() {
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }

    public String getNome(){
        try {
            JSONObject json = new JSONObject(getDados());
            return json.optString("nome");
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer getCodigoGerado(){
        try {
            JSONObject json = new JSONObject(getDados());
            return json.optInt("codigo");
        } catch (Exception ex) {
            return null;
        }
    }

    public String getImportado(){
        try {
            JSONObject json = new JSONObject(getDados());
            return json.optBoolean("sucesso") ? "SIM" : "NÃO";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getMsgRetorno(){
        try {
            JSONObject json = new JSONObject(getDados());
            return json.optString("msgRetorno");
        } catch (Exception ex) {
            return "";
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.PlanoVO;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class BrindeVO extends SuperVO{
    
    @ChavePrimaria
    protected Integer codigo;
    private Boolean ativo;
    private Integer pontos;
    private String nome;
    private String descricao;
    private EmpresaVO empresa;
    private boolean aplicarPontuacaoParaTodosOsPlanos;
    private List<PlanoVO> planos;
    private List<Integer> codigoPlanos;

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAtivo() {
        if (ativo == null) {
            ativo = true;
        }
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getPontos() {
        if (pontos == null) {
            pontos = 0;
        }
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }
    
    public static void validarDados(BrindeVO obj) throws Exception{
        if (!obj.getValidarDados()) {
            return;
        }
        
        if (obj.getNome().equals("")) {
            throw new Exception("O campo Nome (Brinde) deve ser informado.");
        }

        if (obj.getEmpresa() == null) {
            throw new Exception("O campo Empresa (Brinde) deve ser informado.");
        }
    }
    
    public String getAtivo_Apresentar(){
        if (getAtivo()) {
            return "SIM";
        }else{
            return "NÃO";
        }
    }
    
    public String getPontos_Apresentar(){
        return getPontos().toString();
    }
    
    public Integer getPontosNegativo(){
        return (pontos * -1);
    }

     public boolean isAplicarPontuacaoParaTodosOsPlanos() {
        return aplicarPontuacaoParaTodosOsPlanos;
    }

    public void setAplicarPontuacaoParaTodosOsPlanos(boolean aplicarPontuacaoParaTodosOsPlanos) {
        this.aplicarPontuacaoParaTodosOsPlanos = aplicarPontuacaoParaTodosOsPlanos;
    }

    public List<PlanoVO> getPlanos() {
        return planos;
    }

    public void setPlanos(List<PlanoVO> planos) {
        this.planos = planos;
    }

    public List<Integer> getCodigoPlanos() {
        return codigoPlanos;
    }

    public void setCodigoPlanos(List<Integer> codigoPlanos) {
        this.codigoPlanos = codigoPlanos;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BrindeVO brindeVO = (BrindeVO) o;
        return aplicarPontuacaoParaTodosOsPlanos == brindeVO.aplicarPontuacaoParaTodosOsPlanos &&
                codigo.equals(brindeVO.codigo) &&
                Objects.equals(ativo, brindeVO.ativo) &&
                Objects.equals(pontos, brindeVO.pontos) &&
                Objects.equals(nome, brindeVO.nome) &&
                Objects.equals(descricao, brindeVO.descricao) &&
                Objects.equals(empresa, brindeVO.empresa);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo, ativo, pontos, nome, descricao, empresa, aplicarPontuacaoParaTodosOsPlanos);
    }
}

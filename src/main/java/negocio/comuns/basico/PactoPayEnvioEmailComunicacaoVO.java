package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

public class PactoPayEnvioEmailComunicacaoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private PactoPayEnvioEmailVO pactoPayEnvioEmailVO;
    @ChaveEstrangeira
    private PactoPayComunicacaoVO pactoPayComunicacaoVO;

    public PactoPayEnvioEmailComunicacaoVO() {

    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PactoPayEnvioEmailVO getPactoPayEnvioEmailVO() {
        if (pactoPayEnvioEmailVO == null) {
            pactoPayEnvioEmailVO = new PactoPayEnvioEmailVO();
        }
        return pactoPayEnvioEmailVO;
    }

    public void setPactoPayEnvioEmailVO(PactoPayEnvioEmailVO pactoPayEnvioEmailVO) {
        this.pactoPayEnvioEmailVO = pactoPayEnvioEmailVO;
    }

    public PactoPayComunicacaoVO getPactoPayComunicacaoVO() {
        if (pactoPayComunicacaoVO == null) {
            pactoPayComunicacaoVO = new PactoPayComunicacaoVO();
        }
        return pactoPayComunicacaoVO;
    }

    public void setPactoPayComunicacaoVO(PactoPayComunicacaoVO pactoPayComunicacaoVO) {
        this.pactoPayComunicacaoVO = pactoPayComunicacaoVO;
    }
}

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.plano.PlanoVO;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by GlaucoT on 16/02/2016
 */
public class ConfiguracaoSorteioVO extends SuperVO {

    private EmpresaVO empresaVO;
    private List<PlanoVO> planoVOs;
    private List<GenericoTO> situacoesCliente;

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public List<PlanoVO> getPlanoVOs() {
        if (planoVOs == null) {
            planoVOs = new ArrayList<PlanoVO>();
        }
        return planoVOs;
    }

    public void setPlanoVOs(List<PlanoVO> planoVOs) {
        this.planoVOs = planoVOs;
    }

    public String getPlanos() {
        StringBuilder sbPlanos = new StringBuilder();
        for (PlanoVO plano : getPlanoVOs()) {
            if (plano.getSelecionado()) {
                sbPlanos.append("|:").append(plano.getCodigo()).append(",").append(plano.getDescricao());
            }
        }
        return sbPlanos.toString();
    }

    public String getPlanosSQL() {
        StringBuilder sbPlanos = new StringBuilder();
        if (getPlanoVOs().size() > 0) {
            sbPlanos.append("AND con.plano IN (");
            for (PlanoVO plano : getPlanoVOs()) {
                if (plano.getSelecionado()) {
                    sbPlanos.append(plano.getCodigo()).append(",");
                }
            }

            sbPlanos.deleteCharAt(sbPlanos.length() - 1);
            sbPlanos.append(")\n");
        }
        return sbPlanos.toString();
    }

    public List<GenericoTO> getSituacoesCliente() {
        if (situacoesCliente == null) {
            situacoesCliente = new ArrayList<GenericoTO>();
        }
        return situacoesCliente;
    }

    public void setSituacoesCliente(List<GenericoTO> situacoesCliente) {
        this.situacoesCliente = situacoesCliente;
    }

    public String getSituacoes() {
        StringBuilder sbSituacoes = new StringBuilder();
        for (GenericoTO situacao : getSituacoesCliente()) {
            if (situacao.getSelecionado()) {
                sbSituacoes.append("|:").append(situacao.getCodigoString()).append(",").append(situacao.getLabel());
            }
        }
        return sbSituacoes.toString();
    }

    public String getSituacoesSQL() {
        StringBuilder sbSituacoes = new StringBuilder();
        if (getSituacoesCliente().size() > 0) {
            sbSituacoes.append("AND cli.situacao IN (");
            for (GenericoTO situacao : getSituacoesCliente()) {
                if (situacao.getSelecionado()) {
                    sbSituacoes.append("'").append(situacao.getCodigoString()).append("',");
                }
            }
            sbSituacoes.deleteCharAt(sbSituacoes.length() - 1);
            sbSituacoes.append(")\n");
        }
        return sbSituacoes.toString();
    }

    public String getRegras() {
        StringBuilder sbSituacoes = new StringBuilder();
        if (getSituacoesCliente().size() > 0) {
            sbSituacoes.append("Clientes nas situações: ");
            for (GenericoTO situacao : getSituacoesCliente()) {
                if (situacao.getSelecionado()) {
                    sbSituacoes.append(situacao.getLabel()).append(",");
                }
            }
            sbSituacoes.deleteCharAt(sbSituacoes.length() - 1);
            sbSituacoes.append("\n");
        }

        StringBuilder sbPlanos = new StringBuilder();
        if (getPlanoVOs().size() > 0) {
            sbPlanos.append("Clientes com os planos: ");
            for (PlanoVO plano : getPlanoVOs()) {
                if (plano.getSelecionado()) {
                    sbPlanos.append(plano.getDescricao()).append(",");
                }
            }

            sbPlanos.deleteCharAt(sbPlanos.length() - 1);
            sbPlanos.append("\n");
        }

        if (sbSituacoes.toString().isEmpty() && sbPlanos.toString().isEmpty()) {
            return "Todos clientes cadastrados";
        }

        return sbSituacoes.toString() + sbPlanos.toString();
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class ConfiguracaoGestaoPersonalEmailVO extends SuperVO{
    private String email;
    private Integer empresa;
    
    public ConfiguracaoGestaoPersonalEmailVO(String e){
        this.email = e;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
    
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    
    
    
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.lang.reflect.Field;

/**
 *
 * <AUTHOR>
 */
public class EmailVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer pessoa;
    protected PessoaVO pessoaVO;
    protected String email;
    protected Boolean emailCorrespondencia;
    private Boolean escolhido;
    @NaoControlarLogAlteracao
    private Integer cliente = 0;
    private Boolean bloqueadoBounce;
    private boolean receberEmailNovidades = false;
    private Integer bounced;

    private boolean permiteRemover = false;

    /**
     * Construtor padr<PERSON> da classe
     * <code>Endereco</code>. Cria uma nova instância desta entidade,
     * inicializando automaticamente seus atributos (Classe VO).
     */
    public EmailVO() {
        super();
        inicializarDados();
    }

    public EmailVO(Integer codigo, String email, Boolean emailCorrespondencia) {
        super();
        this.codigo = codigo;
        this.email = email;
        this.emailCorrespondencia = emailCorrespondencia;
    }

    public EmailVO(JSONObject o) {
        super();
        inicializarDados();
        try {
            setCodigo(o.getInt("codigo"));
            setEmail(o.getString("email"));
        } catch (Exception e) {
        }

    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>EnderecoVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @exception ConsistirException Se uma inconsistência for encontrada
     * aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     * ocorrido.
     */
    public static void validarDados(EmailVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getEmail().trim().equals("")) {
            throw new ConsistirException("O campo EMAIL (Aba - Email) deve ser informado.");
        }
        if (obj.getEmail().matches("^((.)*(\\s)+(.)*)$")) {
            throw new ConsistirException("O email não pode conter espaços em branco");
        }
        if (!obj.getEmail().matches("^(([a-zA-Z0-9]|\\.|\\_|\\-)*@(.)*)$")) {
            throw new ConsistirException("O endereço de email não pode conter caracteres especiais além de ponto (.), hífen (-) e underline (_)");
        }
        if (!obj.getEmail().matches("^((.)*@([a-zA-Z0-9]|\\.|\\-)*)$")) {
            throw new ConsistirException("O domínio do email não pode conter caracteres especiais além de ponto (.) e hífen (-)");
        }
        if (!obj.getEmail().matches("^((.){2,}@(.){2,})$")) {
            throw new ConsistirException("O endereço e o domínio do email devem possuir ao menos dois caracteres cada e estarem separados por arroba (@)");
        }
        if (!obj.getEmail().matches("^([a-zA-Z0-9](.)*@(.)*)$")) {
            throw new ConsistirException("O endereço de email deve começar com uma letra");
        }
        if (!obj.getEmail().matches("^((.)*@[a-zA-Z0-9](.)*)$")) {
            throw new ConsistirException("O domínio do email deve começar com uma letra ou número");
        }
        if (!obj.getEmail().matches("^((.)*@(.)*[a-zA-Z0-9])$")) {
            throw new ConsistirException("O domínio do email deve terminar com uma letra ou número");
        }
        if (!obj.getEmail().matches("^((.)*@[^\\.\\-]*([\\.|\\-][^\\.\\-]+)*)$")) {
            throw new ConsistirException("O domínio do email não pode conter dois caracteres especiais seguidos");
        }
        if (!obj.getEmail().matches("^((.)*@[^\\.]{1,26}(\\.[^\\.]{2,26})+)$")) {
            throw new ConsistirException("Cada domínio do email deve possuir no máximo vinte e seis caracteres e estarem separados por ponto \'.\'");
        }
        if (!obj.getEmail().matches("^((.)*@[^\\.]*[a-zA-Z][^\\.]*(\\.[^\\.]*[a-zA-Z][^\\.]*)*)$")) {
            throw new ConsistirException("Cada domínio do email deve possuir ao menos uma letra");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        setEmail(getEmail().toLowerCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setEmail("");
        setPessoa(0);
        setEmailCorrespondencia(true);
    }

    public boolean isEmpty() {
        return getEmail().trim().isEmpty();
    }

    public Integer getPessoa() {
        return (pessoa);
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return Uteis.removerCaracteresDeControle(email);
    }

    public void setEmail(String email) {
        this.email = Uteis.removerCaracteresDeControle(email);
    }

    public Boolean getEmailCorrespondencia() {
        return emailCorrespondencia;
    }

    public void setEmailCorrespondencia(Boolean emailCorrespondencia) {
        this.emailCorrespondencia = emailCorrespondencia;
    }

    public String getEmailCorrespondencia_Apresentar() {
        if (emailCorrespondencia) {
            return "Sim";
        }
        if (!emailCorrespondencia) {
            return "Não";
        }
        return ("");
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * Retorna o objeto da classe
     * <code>Pessoa</code> relacionado com (
     * <code>Email</code>).
     */
    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return (pessoaVO);
    }

    /**
     * Define o objeto da classe
     * <code>Pessoa</code> relacionado com (
     * <code>Email</code>).
     */
    public void setPessoaVO(PessoaVO obj) {
        this.pessoaVO = obj;
    }

    /**
     * @param escolhido the escolhido to set
     */
    public void setEscolhido(Boolean escolhido) {
        this.escolhido = escolhido;
    }

    /**
     * @return the escolhido
     */
    public Boolean getEscolhido() {
        if (escolhido == null) {
            escolhido = Boolean.TRUE;
        }
        return escolhido;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getCliente() {
        return cliente;
    }

    public JSONObject toJSON() {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                o.put(field.getName(), field.get(this));
            }
        } catch (Exception e) {
        }
        return o;
    }

    @Override
    public String toString() {
        final int n = 3;
        String tmp = email;
        try {
            if (tmp != null && tmp.contains("@")) {
                String prefix = tmp.substring(0, tmp.indexOf("@"));
                if (prefix.length() > n) {
                    prefix = prefix.substring(0, n);
                    final String suffix = tmp.substring(tmp.indexOf("@"));
                    return String.format("%s...%s", prefix, suffix);
                }
            }
        } catch (Exception e) {
        }
        return email;
    }

    @Override
    public int hashCode(){
        return this.pessoa.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return (obj != null) && (obj instanceof EmailVO) && obj.equals(this.codigo);
    }

    public Boolean getBloqueadoBounce() {
        if (bloqueadoBounce == null) {
            bloqueadoBounce = false;
        }
        return bloqueadoBounce;
    }

    public void setBloqueadoBounce(Boolean bloqueadoBounce) {
        this.bloqueadoBounce = bloqueadoBounce;
    }

    public String getBloqueadoBounce_Apresentar() {
        if (getBloqueadoBounce()) {
            return "Sim";
        }
        return "Não";
    }

    public EmailWS toWS() {
        EmailWS emailWS = new EmailWS();
        emailWS.setCodigo(this.codigo);
        emailWS.setEmail(this.email);
        emailWS.setEmailCorrespondencia(this.emailCorrespondencia);
        emailWS.setBloqueadoBounce(this.bloqueadoBounce);
        return emailWS;
    }

    public boolean isReceberEmailNovidades() {
        return receberEmailNovidades;
    }

    public void setReceberEmailNovidades(boolean receberEmailNovidades) {
        this.receberEmailNovidades = receberEmailNovidades;
    }

    public Integer getBounced() {
        return bounced;
    }

    public void setBounced(Integer bounced) {
        this.bounced = bounced;
    }

    public boolean isPermiteRemover() {
        return permiteRemover;
    }

    public void setPermiteRemover(boolean permiteRemover) {
        this.permiteRemover = permiteRemover;
    }
}

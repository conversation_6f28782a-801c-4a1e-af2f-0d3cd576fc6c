package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import servicos.propriedades.PropsService;

import java.io.File;
import java.util.Date;

/**
 * Created by glauco on 05/06/2014.
 */
public class ArquivoVO extends SuperVO {

    private Integer codigo = 0;
    private PessoaVO pessoa = new PessoaVO();
    private String tipo = "";
    private String extensao;
    private Date dataRegistro;

    public String getNomeArquivoGerado() {
        if (getExtensao().contains(".")) {
            return getCodigo() + "-" + getPessoa().getCodigo() + getExtensao();
        }
        return getCodigo() + "-" + getPessoa().getCodigo() + "." + getExtensao();
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getExtensao() {
        if (extensao == null) {
            extensao = "";
        }
        return extensao;
    }

    public void setExtensao(String extensao) {
        this.extensao = extensao;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }
}

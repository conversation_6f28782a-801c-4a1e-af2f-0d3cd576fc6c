package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: GlaucoT
 * Date: 03/12/13
 * Time: 16:07
 * To change this template use File | Settings | File Templates.
 */
public class EstornoObservacaoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private String justificativa = "";
    private Date dataEstorno = new Date();
    @ChaveEstrangeira
    private PessoaVO pessoaVO = new PessoaVO();
    private Integer codContrato = 0;
    @ChaveEstrangeira
    private String usuarioResponsavel = "";
    private Integer codEmpresa = 0;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public String getNome() {
        return getPessoaVO().getNome();
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Date getDataEstorno() {
        return dataEstorno;
    }

    public void setDataEstorno(Date dataEstorno) {
        this.dataEstorno = dataEstorno;
    }

    public String getDataEstorno_Apresentar() {
        if (dataEstorno != null) {
            return Uteis.getDataAplicandoFormatacao(dataEstorno, "dd/MM/yyyy HH:mm:ss");
        } else {
            return "";
        }
    }

    public Integer getCodContrato() {
        return codContrato;
    }

    public void setCodContrato(Integer codContrato) {
        this.codContrato = codContrato;
    }

    public String getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(String usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public Integer getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }
}

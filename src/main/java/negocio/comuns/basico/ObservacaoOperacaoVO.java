package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoObservacaoOperacaoEnum;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: GlaucoT
 * Date: 03/12/13
 * Time: 16:07
 * To change this template use File | Settings | File Templates.
 */
public class ObservacaoOperacaoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private String justificativa = "";
    private Date dataOperacao = Calendario.hoje();
    private TipoObservacaoOperacaoEnum tipoObservacao = TipoObservacaoOperacaoEnum.NENHUM;
    @ChaveEstrangeira
    private MovParcelaVO movParcela = new MovParcelaVO();
    private Double valorOperacao = 0.0;
    @ChaveEstrangeira
    private String usuarioResponsavel = "";
    private String tipoCancelamento;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getDataOperacao_Apresentar() {
        if (dataOperacao != null) {
            return Uteis.getDataAplicandoFormatacao(dataOperacao, "dd/MM/yyyy HH:mm:ss");
        } else {
            return "";
        }
    }

    public String getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(String usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public TipoObservacaoOperacaoEnum getTipoObservacao() {
        return tipoObservacao;
    }

    public void setTipoObservacao(TipoObservacaoOperacaoEnum tipoObservacao) {
        this.tipoObservacao = tipoObservacao;
    }

    public MovParcelaVO getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(MovParcelaVO movParcela) {
        this.movParcela = movParcela;
    }

    public Double getValorOperacao() {
        return valorOperacao;
    }

    public void setValorOperacao(Double valorOperacao) {
        this.valorOperacao = valorOperacao;
    }

    public boolean isOperacaoCancelamentoParcela() {
        return tipoObservacao.equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA);
    }

    public String getValorOperacao_Apresentar() {
        return Formatador.formatarValorMonetario(getValorOperacao());
    }

    public String getTipoCancelamento() {
        return tipoCancelamento;
    }

    public void setTipoCancelamento(String tipoCancelamento) {
        this.tipoCancelamento = tipoCancelamento;
    }
}

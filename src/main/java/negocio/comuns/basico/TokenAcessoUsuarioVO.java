/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoTokenAcessoEnum;

/**
 *
 * <AUTHOR>
 */
public class TokenAcessoUsuarioVO extends SuperVO{
    @ChavePrimaria
    private Integer codigo;
    private String descricao;
    private String token;
    private TipoTokenAcessoEnum tipo;

    public Integer getCodigo(){
        return codigo;
    }
    
    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public TipoTokenAcessoEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoTokenAcessoEnum tipo) {
        this.tipo = tipo;
    }
    
    
    
}

package negocio.comuns.basico;

import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class PresencaVO extends SuperVO {

    private Integer codigo = 0;
    private int dadosTurma = 0;       // corresponde a matricula do aluno em um horario turma
    private String diaSemana = "";
    private Date dataChamada = Calendario.hoje();
    private Date dataPresenca = Calendario.hoje();
    private boolean presente = false; // corresponde à presenca no dia
    private boolean aula = false;     // se o aluno tem aula ou nao nesse dia
    private boolean hoje = false;     // variavel usada na tela para mostrar a data de hoje
    private ReposicaoVO repo = null;
    private boolean carencia = false;

    private Date dataCadastro;

    public void validarDados() throws Exception {
        if (!getValidarDados().booleanValue()) {
            return;
        }
        //if(dadosTurma == 0)
        //    throw new Exception("Dados da Turma não foram encontratos.");
        if (dataPresenca == null) {
            throw new Exception("A Data da Presença deve ser informada.");
        }
    }

    public boolean isCarencia() {
        return carencia;
    }

    public void setCarencia(boolean carencia) {
        this.carencia = carencia;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getDadosTurma() {
        return dadosTurma;
    }

    public void setDadosTurma(int dadosTurma) {
        this.dadosTurma = dadosTurma;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getDiaChamada() {
        return String.valueOf(Uteis.obterDiaData(dataPresenca)) + "/" + Uteis.getMesData(dataPresenca);
    }

    public Date getDataChamada() {
        return dataChamada;
    }

    public void setDataChamada(Date dataChamada) {
        this.dataChamada = dataChamada;
    }

    public Date getDataPresenca() {
        return dataPresenca;
    }

    public void setDataPresenca(Date dataPresenca) {
        this.dataPresenca = dataPresenca;
    }

    public boolean isPresente() {
        return presente;
    }

    public void setPresente(boolean presente) {
        this.presente = presente;
    }

    public boolean isAula() {
        return aula;
    }

    public void setAula(boolean aula) {
        this.aula = aula;
    }

    public boolean isHoje() {
        return hoje;
    }

    public void setHoje(boolean hoje) {
        this.hoje = hoje;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public ReposicaoVO getRepo() {
        return repo;
    }

    public String getDiaSemana_Apresentar() {
        if (diaSemana == null) {
            diaSemana = "";
        }
        if (diaSemana.equals("DM")) {
            return "Domingo";
        }
        if (diaSemana.equals("SG")) {
            return "Segunda";
        }
        if (diaSemana.equals("TR")) {
            return "Terça";
        }
        if (diaSemana.equals("QA")) {
            return "Quarta";
        }
        if (diaSemana.equals("QI")) {
            return "Quinta";
        }
        if (diaSemana.equals("SX")) {
            return "Sexta";
        }
        if (diaSemana.equals("SB")) {
            return "Sábado";
        }
        return (diaSemana);
    }
    public void setRepo(ReposicaoVO repo) {
        this.repo = repo;
    }
}

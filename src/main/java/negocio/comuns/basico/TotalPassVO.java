package negocio.comuns.basico;

public class TotalPassVO {
    private String codigoTotalpass;
    private String apiKey;
    private Integer limiteDeAcessosPorDia;
    private Integer limiteDeAulasPorDia;
    private Integer empresa;
    private Boolean inativo;
    private Boolean permitirWod;
    public static final String TOTALPASS_OPERACOES_VALIDACAO = "VALIDACÃO";
    public static final String TOTALPASS_OPERACOES_DESVINCULACAO = "DESVINCULAÇÃO";

    public String getCodigoTotalpass() {
        return codigoTotalpass;
    }

    public void setCodigoTotalpass(String codigoTotalpass) {
        this.codigoTotalpass = codigoTotalpass;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public Integer getLimiteDeAcessosPorDia() {
        return limiteDeAcessosPorDia;
    }

    public void setLimiteDeAcessosPorDia(Integer limiteDeAcessosPorDia) {
        this.limiteDeAcessosPorDia = limiteDeAcessosPorDia;
    }

    public Integer getLimiteDeAulasPorDia() {
        return limiteDeAulasPorDia;
    }

    public void setLimiteDeAulasPorDia(Integer limiteDeAulasPorDia) {
        this.limiteDeAulasPorDia = limiteDeAulasPorDia;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Boolean getInativo() {
        return inativo;
    }

    public void setInativo(Boolean inativo) {
        this.inativo = inativo;
    }

    public Boolean getPermitirWod() {
        return permitirWod;
    }

    public void setPermitirWod(Boolean permitirWod) {
        this.permitirWod = permitirWod;
    }
}

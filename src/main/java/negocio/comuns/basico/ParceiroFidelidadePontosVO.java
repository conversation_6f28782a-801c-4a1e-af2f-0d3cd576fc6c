package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import java.util.Date;

import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.TipoPontoParceiroFidelidadeEnum;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class ParceiroFidelidadePontosVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private String historico;
    @ChaveEstrangeira
    private TabelaParceiroFidelidadeVO tabelaParceiroFidelidade;
    private Double multiplicador;
    private Integer pontos;
    private Date dataLancamento;
    private Integer pessoa;
    private String cpf;
    private MovPagamentoVO movPagamento;
    private ReciboPagamentoVO reciboPagamento;
    private TipoPontoParceiroFidelidadeEnum tipoPontoParceiroFidelidade;
    private String retornoParceiroFidelidade;
    private ClienteVO clienteVO;
    private UsuarioVO usuario;

    public ParceiroFidelidadePontosVO() {
        super();
    }

    public ParceiroFidelidadePontosVO(MovPagamentoVO movPagamentoVO, Date dataLancamento) {
        this.tabelaParceiroFidelidade = movPagamentoVO.getTabelaParceiroFidelidadeVO();
        this.multiplicador = movPagamentoVO.getMultiplicadorParceiroFidelidade();
        this.pontos = movPagamentoVO.getPontosParceiroFidelidade();
        this.dataLancamento = dataLancamento;
        this.pessoa = movPagamentoVO.getPessoa().getCodigo();
        this.cpf = movPagamentoVO.getCpfParceiroFidelidade();
        this.movPagamento = movPagamentoVO;
        this.reciboPagamento = movPagamentoVO.getReciboPagamento();
        this.tipoPontoParceiroFidelidade = movPagamentoVO.getTipoPontoParceiroFidelidade();
        this.usuario = movPagamentoVO.getResponsavelPagamento();
    }

    public static void validarDados(ParceiroFidelidadePontosVO obj) throws ConsistirException {
        if (obj.getValidarDados()) {
//            if (UteisValidacao.emptyString(obj.getHistorico())) {
//                throw new ConsistirException("O campo HISTÓRICO (ParceiroFidelidadePontos) deve ser informado.");
//            }
//            if (UteisValidacao.emptyNumber(obj.getPontos())) {
//                throw new ConsistirException("O campo PONTOS (ParceiroFidelidadePontos) deve ser informado.");
//            }
            if (UteisValidacao.valorNulo(obj, "dataLancamento")) {
                throw new ConsistirException("O campo DATA DE LANÇAMENTO (ParceiroFidelidadePontos) deve ser informado.");
            }
//            if (UteisValidacao.valorNulo(obj, "saldo")) {
//                throw new ConsistirException("O campo SALDO (ParceiroFidelidadePontos) deve ser informado.");
//            }
            if (UteisValidacao.emptyNumber(obj.getPessoa())) {
                throw new ConsistirException("O campo PESSOA (ParceiroFidelidadePontos) deve ser informado.");
            }
//            if (UteisValidacao.emptyNumber(obj.getTabelaParceiroFidelidade().getCodigo())) {
//                throw new ConsistirException("O campo TABELA PARCEIRO (ParceiroFidelidadePontos) deve ser informado.");
//            }
        }
    }

    public void realizarUpperCaseDados() {
        setHistorico(getHistorico().toUpperCase());
    }

    public String getHistorico() {
        if (historico == null) {
            historico = "";
        }
        return historico;
    }

    public void setHistorico(String historico) {
        this.historico = historico;
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPontos() {
        if (pontos == null) {
            pontos = 0;
        }
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getPessoa() {
        if (pessoa == null) {
            pessoa = 0;
        }
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getCpf() {
        if (cpf == null) {
            cpf = "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public MovPagamentoVO getMovPagamento() {
        if (movPagamento == null) {
            movPagamento = new MovPagamentoVO();
        }
        return movPagamento;
    }

    public void setMovPagamento(MovPagamentoVO movPagamento) {
        this.movPagamento = movPagamento;
    }

    public ReciboPagamentoVO getReciboPagamento() {
        if (reciboPagamento == null) {
            reciboPagamento = new ReciboPagamentoVO();
        }
        return reciboPagamento;
    }

    public void setReciboPagamento(ReciboPagamentoVO reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public TipoPontoParceiroFidelidadeEnum getTipoPontoParceiroFidelidade() {
        if (tipoPontoParceiroFidelidade == null) {
            tipoPontoParceiroFidelidade = TipoPontoParceiroFidelidadeEnum.NENHUM;
        }
        return tipoPontoParceiroFidelidade;
    }

    public void setTipoPontoParceiroFidelidade(TipoPontoParceiroFidelidadeEnum tipoPontoParceiroFidelidade) {
        this.tipoPontoParceiroFidelidade = tipoPontoParceiroFidelidade;
    }

    public String getRetornoParceiroFidelidade() {
        if (retornoParceiroFidelidade == null) {
            retornoParceiroFidelidade = "";
        }
        return retornoParceiroFidelidade;
    }

    public void setRetornoParceiroFidelidade(String retornoParceiroFidelidade) {
        this.retornoParceiroFidelidade = retornoParceiroFidelidade;
    }

    public Double getMultiplicador() {
        if (multiplicador == null) {
            multiplicador = 0.0;
        }
        return multiplicador;
    }

    public String getMultiplicador_String(){
        return getMultiplicador().toString();
    }


    public void setMultiplicador(Double multiplicador) {
        this.multiplicador = multiplicador;
    }

    public TabelaParceiroFidelidadeVO getTabelaParceiroFidelidade() {
        if (tabelaParceiroFidelidade == null) {
            tabelaParceiroFidelidade = new TabelaParceiroFidelidadeVO();
        }
        return tabelaParceiroFidelidade;
    }

    public void setTabelaParceiroFidelidade(TabelaParceiroFidelidadeVO tabelaParceiroFidelidade) {
        this.tabelaParceiroFidelidade = tabelaParceiroFidelidade;
    }

    public String getMsgGravarHistorico() {
        try {
            return getTipoPontoParceiroFidelidade().getDescricao() + " - RECIBO N." + getReciboPagamento().getCodigo();
        } catch (Exception ex) {
            return "";
        }
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public String getDataLancamento_Apresentar() {
        return Uteis.getDataComHHMM(getDataLancamento());
    }

    public String getTipoPonto_Apresentar() {
        return getTipoPontoParceiroFidelidade().getDescricao();
    }

    public String getNomePessoa() {
        return getClienteVO().getPessoa().getNome();
    }

    public Double getValor() {
        return getMovPagamento().getValorTotal();
    }

    public String getValor_Apresentar() {
        return getMovPagamento().getValorTotal_Apresentar();
    }

    public String getNomeTabela() {
        return getTabelaParceiroFidelidade().getNomeTabela();
    }

    public String getMatricula() {
        return getClienteVO().getMatricula();
    }

    public String getUsuarioNome() {
        return getUsuario().getNomeAbreviado();
    }

    public String getCodigoIdentificador() throws Exception {
        try {
            JSONObject jsonObject = new JSONObject(getRetornoParceiroFidelidade());
            return jsonObject.getString("identificador");
        } catch (Exception ex) {
         throw new Exception("Erro ao obter identificador");
        }
    }

    public String getOrderId() throws Exception {
        try {
            JSONObject jsonObject = new JSONObject(getRetornoParceiroFidelidade());
            return jsonObject.getString("identificadorExterno");
        } catch (Exception ex) {
            throw new Exception("Erro ao obter identificador");
        }
    }

    public UsuarioVO getUsuario() {
        if (usuario == null) {
            usuario = new UsuarioVO();
        }
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }
}

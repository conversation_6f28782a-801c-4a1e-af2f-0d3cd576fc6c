/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.AcoesRemessasEnum;

/**
 * <AUTHOR>
 */
public class AcoesStatusRemessaVO extends SuperVO {
    private String codigoStatus = "";
    private AcoesRemessasEnum acao;

    private Boolean reagendarAutomaticamente = false;
    private Integer qtdTentativasParaReagendamentoAutomatico = 0;
    private Integer qtdDiasParaReagendamentoAutomatico = 0;

    public AcoesStatusRemessaVO(AcoesRemessasEnum acao, String codigoStatus) {
        this.acao = acao;
        this.codigoStatus = codigoStatus;
    }

    public AcoesStatusRemessaVO() {
    }

    public String getAcaoName() {
        return acao == null ? "" : acao.name();
    }

    public Integer getCodigoAcao() {
        return acao == null ? null : acao.getCodigo();
    }

    public void setCodigoAcao(Integer codigo) {
        acao = AcoesRemessasEnum.getFromId(codigo);
    }

    public AcoesRemessasEnum getAcao() {
        return acao;
    }

    public void setAcao(AcoesRemessasEnum acao) {
        this.acao = acao;
    }

    public String getCodigoStatus() {
        return codigoStatus;
    }

    public void setCodigoStatus(String codigoStatus) {
        this.codigoStatus = codigoStatus;
    }

    public Boolean getReagendarAutomaticamente() {
        if (reagendarAutomaticamente == null) {
            reagendarAutomaticamente = false;
        }
        return reagendarAutomaticamente;
    }

    public void setReagendarAutomaticamente(Boolean reagendarAutomaticamente) {
        this.reagendarAutomaticamente = reagendarAutomaticamente;
    }

    public Integer getQtdTentativasParaReagendamentoAutomatico() {
        if (qtdTentativasParaReagendamentoAutomatico == null) {
            qtdTentativasParaReagendamentoAutomatico = 0;
        }
        return qtdTentativasParaReagendamentoAutomatico;
    }

    public void setQtdTentativasParaReagendamentoAutomatico(Integer qtdTentativasParaReagendamentoAutomatico) {
        this.qtdTentativasParaReagendamentoAutomatico = qtdTentativasParaReagendamentoAutomatico;
    }

    public Integer getQtdDiasParaReagendamentoAutomatico() {
        if (qtdDiasParaReagendamentoAutomatico == null) {
            qtdDiasParaReagendamentoAutomatico = 0;
        }
        return qtdDiasParaReagendamentoAutomatico;
    }

    public void setQtdDiasParaReagendamentoAutomatico(Integer qtdDiasParaReagendamentoAutomatico) {
        this.qtdDiasParaReagendamentoAutomatico = qtdDiasParaReagendamentoAutomatico;
    }

    public String getCodigoStatus_Ordenacao() {
        String codStatus = getCodigoStatus();
        if (codStatus.length() == 1) {
            codStatus = "0" + codStatus;
        }

        return codStatus;
    }

    public String getAcaoAssociada_Ordenacao() {
        String codStatus = getCodigoStatus();
        if (codStatus.length() == 1) {
            codStatus = "0" + codStatus;
        }

        return getAcaoName() + codStatus;
    }
}

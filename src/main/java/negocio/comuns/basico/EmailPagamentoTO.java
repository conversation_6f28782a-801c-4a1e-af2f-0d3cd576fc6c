package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.util.Date;

/**
 * Created by <PERSON><PERSON> on 17/09/2021
 */
public class EmailPagamentoTO extends SuperTO {

    private Integer empresa;
    private String empresaNomeFantasia;
    private String empresaCNPJ;
    private String empresaTelefone;
    private String empresaEmail;
    private String nomePessoa;
    private String cartao;
    private String email;
    private Double valor;
    private Date dataPagamento;
    private String urlDownload;
    private String codigoAutorizacao;
    private String produto;
    private TipoCobrancaEnum tipoCobrancaEnum;

    public EmailPagamentoTO() {
    }

    public EmailPagamentoTO(TipoCobrancaEnum tipoCobrancaEnum) {
        this.tipoCobrancaEnum = tipoCobrancaEnum;
    }

    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getUrlDownload() {
        if (urlDownload == null) {
            urlDownload = "";
        }
        return urlDownload;
    }

    public void setUrlDownload(String urlDownload) {
        this.urlDownload = urlDownload;
    }

    public String getCodigoAutorizacao() {
        if (codigoAutorizacao == null) {
            codigoAutorizacao = "";
        }
        return codigoAutorizacao;
    }

    public void setCodigoAutorizacao(String codigoAutorizacao) {
        this.codigoAutorizacao = codigoAutorizacao;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public String getUrlLogoEmpresa(String chave) {
        try {
            String genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, getEmpresa().toString());
            if (UteisValidacao.emptyString(genKey)) {
                genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, getEmpresa().toString());
            }
            if (UteisValidacao.emptyString(genKey)) {
                genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA, getEmpresa().toString());
            }
            return Uteis.getPaintFotoDaNuvem(genKey);
        } catch (Exception ignored) {
            return "";
        }
    }

    public String getNomePessoa() {
        if (nomePessoa == null) {
            nomePessoa = "";
        }
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmpresaNomeFantasia() {
        if (empresaNomeFantasia == null) {
            empresaNomeFantasia = "";
        }
        return empresaNomeFantasia;
    }

    public void setEmpresaNomeFantasia(String empresaNomeFantasia) {
        this.empresaNomeFantasia = empresaNomeFantasia;
    }

    public String getEmpresaCNPJ() {
        if (empresaCNPJ == null) {
            empresaCNPJ = "";
        }
        return empresaCNPJ;
    }

    public void setEmpresaCNPJ(String empresaCNPJ) {
        this.empresaCNPJ = empresaCNPJ;
    }

    public TipoCobrancaEnum getTipoCobrancaEnum() {
        return tipoCobrancaEnum;
    }

    public void setTipoCobrancaEnum(TipoCobrancaEnum tipoCobrancaEnum) {
        this.tipoCobrancaEnum = tipoCobrancaEnum;
    }

    public String getEmpresaTelefone() {
        if (empresaTelefone == null) {
            empresaTelefone = "";
        }
        return empresaTelefone;
    }

    public void setEmpresaTelefone(String empresaTelefone) {
        this.empresaTelefone = empresaTelefone;
    }

    public String getEmpresaEmail() {
        if (empresaEmail == null) {
            empresaEmail = "";
        }
        return empresaEmail;
    }

    public void setEmpresaEmail(String empresaEmail) {
        this.empresaEmail = empresaEmail;
    }

    public String getProduto() {
        if (produto == null) {
            produto = "";
        }
        return produto;
    }

    public void setProduto(String produto) {
        this.produto = produto;
    }

    public String getCartao() {
        if (cartao == null) {
            cartao = "";
        }
        return cartao;
    }

    public void setCartao(String cartao) {
        this.cartao = cartao;
    }
}

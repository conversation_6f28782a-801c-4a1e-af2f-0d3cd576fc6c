package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.facade.jdbc.basico.Cliente;

/**
 * Reponsável por manter os dados da entidade ClienteClassificacao. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see Cliente
 */
public class ClienteClassificacaoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer cliente;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Classificacao </code>.*/
    @ChaveEstrangeira
    protected ClassificacaoVO classificacao;

    /**
     * Construtor padrão da classe <code>ClienteClassificacao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ClienteClassificacaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ClienteClassificacaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ClienteClassificacaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getClassificacao() == null) ||
                (obj.getClassificacao().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo CLASSIFICAÇÃO (Aba - Grupo/Classificação) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setClassificacao(new ClassificacaoVO());
    }

    /**
     * Retorna o objeto da classe <code>Classificacao</code> relacionado com (<code>ClienteClassificacao</code>).
     */
    public ClassificacaoVO getClassificacao() {
        if (classificacao == null) {
            classificacao = new ClassificacaoVO();
        }
        return (classificacao);
    }

    /**
     * Define o objeto da classe <code>Classificacao</code> relacionado com (<code>ClienteClassificacao</code>).
     */
    public void setClassificacao(ClassificacaoVO obj) {
        this.classificacao = obj;
    }

    public Integer getCliente() {
        return (cliente);
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}
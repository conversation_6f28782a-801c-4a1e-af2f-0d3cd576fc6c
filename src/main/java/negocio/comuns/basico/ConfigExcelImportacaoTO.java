package negocio.comuns.basico;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import importador.enumerador.ImportacaoParcelasSituacaoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class ConfigExcelImportacaoTO {

    //configuracoes gerais
    private String chave;
    private UsuarioVO usuarioResponsavelImportacao;
    private EmpresaVO empresaVO;
    private Integer mascaraDataEnum;

    //importacao de contrato
    private Integer plano;
    private Integer horario;
    private Integer diasCarencia;
    private Integer modalidade;
    private Integer nrDiasAvencer;
    private Integer carenciaRenovacao;
    private boolean importarContratos = false;
    private boolean gerarParcelasDeAcordoDuracao = true;
    private ImportacaoParcelasSituacaoEnum importacaoParcelasSituacaoEnum;

    //importacao de cliente
    private Integer consultor;
    private Integer professorTreinoWeb;
    private String padraoDDD;
    private boolean validarCpfCnpjJaCadastrado = false;

    //importacao de produto
    private String tipoProduto;
    private Integer categoria;
    private boolean validarProdutoMesmoNome = true;

    //importacao de conta
    private Integer planoConta;
    private Integer centroCusto;

    //importacao de colaborador
    private boolean criarUsuario = true;


    public ConfigExcelImportacaoTO() {
        this.setImportacaoParcelasSituacaoEnum(ImportacaoParcelasSituacaoEnum.TODAS_PAGAS);
        this.setDiasCarencia(60);
    }

    public void validarDados(TipoImportacaoEnum tipoImportacaoEnum) throws ValidacaoException {
        if (UteisValidacao.emptyString(getChave())) {
            throw new ValidacaoException("Necessário a chave.");
        }
        if (UteisValidacao.emptyNumber(getUsuarioResponsavelImportacao().getCodigo())) {
            throw new ValidacaoException("Necessário o usuário responsável pela importação.");
        }
        if (UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())) {
            throw new ValidacaoException("Selecione a empresa onde os dados serão importados.");
        }


        if (UteisValidacao.emptyNumber(getMascaraDataEnum()) && !tipoImportacaoEnum.equals(TipoImportacaoEnum.PARCELAS_PAGAMENTOS) &&
                !tipoImportacaoEnum.equals(TipoImportacaoEnum.TREINO_ATIVIDADES) &&
                !tipoImportacaoEnum.equals(TipoImportacaoEnum.TREINO_ATIVIDADE_FICHA) &&
                !tipoImportacaoEnum.equals(TipoImportacaoEnum.TREINO_PROGRAMAS)) {
            throw new ValidacaoException("Selecione a máscara para os campos de data.");
        }


        if (!tipoImportacaoEnum.equals(TipoImportacaoEnum.TREINO_ATIVIDADES) &&
                !tipoImportacaoEnum.equals(TipoImportacaoEnum.TREINO_PROGRAMAS) &&
                !tipoImportacaoEnum.equals(TipoImportacaoEnum.TREINO_ATIVIDADE_FICHA) &&
                !tipoImportacaoEnum.equals(TipoImportacaoEnum.PARCELAS_PAGAMENTOS) &&
                !tipoImportacaoEnum.equals(TipoImportacaoEnum.PRODUTO) &&
                !tipoImportacaoEnum.equals(TipoImportacaoEnum.CONTA_FINANCEIRO) &&
                !tipoImportacaoEnum.equals(TipoImportacaoEnum.TURMAS) &&
                !tipoImportacaoEnum.equals(TipoImportacaoEnum.CONTRATO)) {
            if (UteisValidacao.emptyString(getPadraoDDD())) {
                throw new ValidacaoException("Informe o DDD padrão.");
            }
        }

        //validacoes de importacao de cliente
        if (tipoImportacaoEnum.equals(TipoImportacaoEnum.CLIENTE)) {
            if (UteisValidacao.emptyNumber(getConsultor())) {
                throw new ValidacaoException("Selecione o consultor.");
            }
        }

        //validacoes de importacao de contrato
        if (tipoImportacaoEnum.equals(TipoImportacaoEnum.CONTRATO)) {
            if (isImportarContratos()) {
                if (UteisValidacao.emptyNumber(getPlano())) {
                    throw new ValidacaoException("Selecione o plano.");
                }
                if (UteisValidacao.emptyNumber(getModalidade())) {
                    throw new ValidacaoException("Selecione a modalidade.");
                }
                if (UteisValidacao.emptyNumber(getHorario())) {
                    throw new ValidacaoException("Selecione o horário.");
                }
            }
        }
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getConsultor() {
        return consultor;
    }

    public void setConsultor(Integer consultor) {
        this.consultor = consultor;
    }

    public Integer getMascaraDataEnum() {
        if (mascaraDataEnum == null) {
            mascaraDataEnum = 0;
        }
        return mascaraDataEnum;
    }

    public void setMascaraDataEnum(Integer mascaraDataEnum) {
        this.mascaraDataEnum = mascaraDataEnum;
    }

    public String getPadraoDDD() {
        if (padraoDDD == null) {
            padraoDDD = "";
        }
        return padraoDDD;
    }

    public void setPadraoDDD(String padraoDDD) {
        this.padraoDDD = padraoDDD;
    }

    public Integer getDiasCarencia() {
        return diasCarencia;
    }

    public void setDiasCarencia(Integer diasCarencia) {
        this.diasCarencia = diasCarencia;
    }

    public Integer getProfessorTreinoWeb() {
        return professorTreinoWeb;
    }

    public void setProfessorTreinoWeb(Integer professorTreinoWeb) {
        this.professorTreinoWeb = professorTreinoWeb;
    }

    public Integer getHorario() {
        return horario;
    }

    public void setHorario(Integer horario) {
        this.horario = horario;
    }

    public Integer getCarenciaRenovacao() {
        if (carenciaRenovacao == null) {
            carenciaRenovacao = 0;
        }
        return carenciaRenovacao;
    }

    public void setCarenciaRenovacao(Integer carenciaRenovacao) {
        this.carenciaRenovacao = carenciaRenovacao;
    }

    public Integer getNrDiasAvencer() {
        if (nrDiasAvencer == null) {
            nrDiasAvencer = 0;
        }
        return nrDiasAvencer;
    }

    public void setNrDiasAvencer(Integer nrDiasAvencer) {
        this.nrDiasAvencer = nrDiasAvencer;
    }

    public boolean isGerarParcelasDeAcordoDuracao() {
        return gerarParcelasDeAcordoDuracao;
    }

    public void setGerarParcelasDeAcordoDuracao(boolean gerarParcelasDeAcordoDuracao) {
        this.gerarParcelasDeAcordoDuracao = gerarParcelasDeAcordoDuracao;
    }

    public boolean isImportarContratos() {
        return importarContratos;
    }

    public void setImportarContratos(boolean importarContratos) {
        this.importarContratos = importarContratos;
    }

    public boolean isValidarCpfCnpjJaCadastrado() {
        return validarCpfCnpjJaCadastrado;
    }

    public void setValidarCpfCnpjJaCadastrado(boolean validarCpfCnpjJaCadastrado) {
        this.validarCpfCnpjJaCadastrado = validarCpfCnpjJaCadastrado;
    }

    public UsuarioVO getUsuarioResponsavelImportacao() {
        if (usuarioResponsavelImportacao == null) {
            usuarioResponsavelImportacao = new UsuarioVO();
        }
        return usuarioResponsavelImportacao;
    }

    public void setUsuarioResponsavelImportacao(UsuarioVO usuarioResponsavelImportacao) {
        this.usuarioResponsavelImportacao = usuarioResponsavelImportacao;
    }

    public String getChave() {
        if (chave == null) {
            chave = "";
        }
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getTipoProduto() {
        if (tipoProduto == null) {
            tipoProduto = "";
        }
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public Integer getCategoria() {
        if (categoria == null) {
            categoria = 0;
        }
        return categoria;
    }

    public void setCategoria(Integer categoria) {
        this.categoria = categoria;
    }

    public boolean isValidarProdutoMesmoNome() {
        return validarProdutoMesmoNome;
    }

    public void setValidarProdutoMesmoNome(boolean validarProdutoMesmoNome) {
        this.validarProdutoMesmoNome = validarProdutoMesmoNome;
    }

    public boolean isCriarUsuario() {
        return criarUsuario;
    }

    public void setCriarUsuario(boolean criarUsuario) {
        this.criarUsuario = criarUsuario;
    }

    public Integer getPlanoConta() {
        return planoConta;
    }

    public void setPlanoConta(Integer planoConta) {
        this.planoConta = planoConta;
    }

    public Integer getCentroCusto() {
        return centroCusto;
    }

    public void setCentroCusto(Integer centroCusto) {
        this.centroCusto = centroCusto;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public ImportacaoParcelasSituacaoEnum getImportacaoParcelasSituacaoEnum() {
        return importacaoParcelasSituacaoEnum;
    }

    public void setImportacaoParcelasSituacaoEnum(ImportacaoParcelasSituacaoEnum importacaoParcelasSituacaoEnum) {
        this.importacaoParcelasSituacaoEnum = importacaoParcelasSituacaoEnum;
    }
}

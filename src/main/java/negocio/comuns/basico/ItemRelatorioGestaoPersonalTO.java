package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.ItemTaxaPersonalVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.ProdutoVO;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by glauco on 09/01/2015
 */
public class ItemRelatorioGestaoPersonalTO extends SuperTO {

    private ColaboradorVO personal = new ColaboradorVO();
    private double valorTotal = 0.0;
    private List<ItemTaxaPersonalVO> listaAlunos = new ArrayList<ItemTaxaPersonalVO>();
    private List<MovParcelaVO> listaParcelas = new ArrayList<MovParcelaVO>();
    private double valorPago = 0.0;

//    setAluno(new ItemTaxaPersonalVO());
//    setParcela(new MovParcelaVO());

    public ItemTaxaPersonalVO adicionarAluno(ClienteVO aluno, ProdutoVO produto) throws Exception {
        return adicionarAluno(aluno, produto, true);
    }

    public ItemTaxaPersonalVO adicionarAluno(ClienteVO aluno, ProdutoVO produto, Boolean mesmaEmpresa) throws Exception {
        // prepara os dados para incluir na lista
        ItemTaxaPersonalVO aux = new ItemTaxaPersonalVO();
        aux.setAluno(aluno);
        aux.setSituacao(ItemTaxaPersonalVO.LIVRE);
        aux.setMesmaEmpresa(mesmaEmpresa);
        if (!mesmaEmpresa) {
            aux.setNomeEmpresa(" (" + aluno.getEmpresa().getNome() + ")");
        }
        // adiciona o produto default se necessario
        if (produto == null) {
            if (getPersonal().getProdutoDefault() != null && getPersonal().getProdutoDefault().getCodigo() > 0) {
                aux.setProduto((ProdutoVO) getPersonal().getProdutoDefault().getClone(false));
            }
        } else {
            aux.setProduto(produto);
        }
        // coloca na lista somente se o aluno nao foi incluido anteriormente
        if (!listaAlunos.contains(aux)) {
            listaAlunos.add(aux);
        }
        return aux;
    }

    public double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public List<ItemTaxaPersonalVO> getListaAlunos() {
        return listaAlunos;
    }

    public void setListaAlunos(List<ItemTaxaPersonalVO> listaAlunos) {
        this.listaAlunos = listaAlunos;
    }

    public List<MovParcelaVO> getListaParcelas() {
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public ColaboradorVO getPersonal() {
        return personal;
    }

    public void setPersonal(ColaboradorVO personal) {
        this.personal = personal;
    }

    public double getValorPago() {
        return valorPago;
    }

    public void setValorPago(double valorPago) {
        this.valorPago = valorPago;
    }
}

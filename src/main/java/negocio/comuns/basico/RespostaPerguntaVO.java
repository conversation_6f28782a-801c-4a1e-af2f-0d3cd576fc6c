package negocio.comuns.basico;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

import java.util.Objects;

/**
 * Reponsável por manter os dados da entidade RespostaPergunta. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see Pergunta
*/
public class RespostaPerguntaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer pergunta;
    protected String descricaoRespota;
    protected Integer nrQuestao;
    
	
    /**
     * Construtor padr<PERSON> da classe <code>RespostaPergunta</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public RespostaPerguntaVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>RespostaPerguntaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(RespostaPerguntaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
        if (obj.getDescricaoRespota().equals("")) { 
            throw new ConsistirException("O campo DESCRIÇÃO DA RESPOSTA (Resposta da  Pergunta) deve ser informado.");
        }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
        setDescricaoRespota( getDescricaoRespota().toUpperCase() );
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setDescricaoRespota( "" );
    }
	

    public String getDescricaoRespota() {
        if (descricaoRespota== null) {
            descricaoRespota = "";
        }
        return (descricaoRespota);
    }
     
    public void setDescricaoRespota( String descricaoRespota ) {
        this.descricaoRespota = descricaoRespota;
    }

    public Integer getPergunta() {
        return (pergunta);
    }
     
    public void setPergunta( Integer pergunta ) {
        this.pergunta = pergunta;
    }

    public Integer getCodigo() {
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }

    public Integer getNrQuestao() {
        return nrQuestao;
    }

    public void setNrQuestao(Integer nrQuestao) {
        this.nrQuestao = nrQuestao;
    }

    public String getDescricaoRespostaResumo() {
        if (getDescricaoRespota().length() > 35) {
            return getDescricaoRespota().substring(0,35) + "...";
        }

        return getDescricaoRespota();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RespostaPerguntaVO that = (RespostaPerguntaVO) o;
        return Objects.equals(codigo, that.codigo) &&
                Objects.equals(pergunta, that.pergunta) &&
                Objects.equals(descricaoRespota, that.descricaoRespota);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo, pergunta, descricaoRespota);
    }
}

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

/**
 * Created by <PERSON><PERSON> on 05/09/2018.
 */
public class ItemGenericoTO extends SuperTO {

    private String codigo;
    private String descricao;
    private String hint;
    private boolean selecionado = false;

    public String getCodigo() {
        if (codigo == null) {
            codigo = "";
        }
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public String getHint() {
        if (hint == null) {
            hint = "";
        }
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }
}

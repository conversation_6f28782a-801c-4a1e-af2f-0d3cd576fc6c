package negocio.comuns.basico;

import negocio.comuns.CadastroDinamicoItemVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.List;

/**
 * Created by ulisses on 14/08/2015.
 */
public class CadastroDinamicoVO extends SuperVO {

    private Integer codigo;
    private String nomeTabela;
    private List<CadastroDinamicoItemVO> listaItem;


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeTabela() {
        return nomeTabela;
    }

    public void setNomeTabela(String nomeTabela) {
        this.nomeTabela = nomeTabela;
    }

    public List<CadastroDinamicoItemVO> getListaItem() {
        return listaItem;
    }

    public void setListaItem(List<CadastroDinamicoItemVO> listaItem) {
        this.listaItem = listaItem;
    }

    public void validarDados() throws ConsistirException, Exception {
        if (!getValidarDados().booleanValue()) {
            return;
        }
        if ((getNomeTabela() == null) || (getNomeTabela().trim().equals(""))) {
            throw new ConsistirException("O campo nome tabela (Cadastro Dinamico) deve ser informado.");
        }
    }

}

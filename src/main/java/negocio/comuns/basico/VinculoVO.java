package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

import java.util.Date;

/**
 * Reponsável por manter os dados da entidade Vinculo. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class VinculoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String tipoVinculo;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Cliente </code>.*/
    @NaoControlarLogAlteracao
    @FKJson
    protected ClienteVO cliente;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    @NaoControlarLogAlteracao
    @FKJson
    protected ColaboradorVO colaborador;
    @NaoControlarLogAlteracao
    //atributo usado na tela de organizadorCarteira
    protected Boolean vinculoEscolhido;

    /**
     * Construtor padrão da classe <code>Vinculo</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public VinculoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>VinculoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(VinculoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
//        if ((obj.getCliente() == null) ||
//                (obj.getCliente().getCodigo().intValue() == 0)) {
//            throw new ConsistirException("O campo CLIENTE (Vinculo) deve ser informado.");
//        }
        if ((obj.getTipoVinculo().equals(""))) {
            throw new ConsistirException("O campo TIPO COLABORADOR (Vínculo) deve ser informado.");
        }
        if ((obj.getColaborador() == null)
                || (obj.getColaborador().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo CONSULTOR(Vínculo) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setTipoVinculo("");
        setCliente(new ClienteVO());
        setColaborador(new ColaboradorVO());
        setVinculoEscolhido(false);
    }

    public String getTipoVinculo() {
        if (tipoVinculo == null) {
            tipoVinculo = "";
        }
        return (tipoVinculo);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico. 
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o 
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc. 
     */
    public String getTipoVinculo_Apresentar() {
        if (tipoVinculo == null) {
            tipoVinculo = "";
        }

        TipoColaboradorEnum tipoColaboradorEnum = TipoColaboradorEnum.getTipo(tipoVinculo);
        if (tipoColaboradorEnum != null) {
            return tipoColaboradorEnum.getDescricao();
        }

        return tipoVinculo;
    }

    public void setTipoVinculo(String tipoVinculo) {
        this.tipoVinculo = tipoVinculo;
    }

    /**
     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (<code>Vinculo</code>).
     */
    public ColaboradorVO getColaborador() {
        if (colaborador == null) {
            colaborador = new ColaboradorVO();
        }
        return (colaborador);
    }

    /**
     * Define o objeto da classe <code>Colaborador</code> relacionado com (<code>Vinculo</code>).
     */
    public void setColaborador(ColaboradorVO obj) {
        this.colaborador = obj;
    }

    /**
     * Retorna o objeto da classe <code>Cliente</code> relacionado com (<code>Vinculo</code>).
     */
    public ClienteVO getCliente() {
        if (cliente == null) {
            cliente = new ClienteVO();
        }
        return (cliente);
    }

    /**
     * Define o objeto da classe <code>Cliente</code> relacionado com (<code>Vinculo</code>).
     */
    public void setCliente(ClienteVO obj) {
        this.cliente = obj;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the vinculoEscolhido
     */
    public Boolean getVinculoEscolhido() {
        return vinculoEscolhido;
    }

    /**
     * @param vinculoEscolhido the vinculoEscolhido to set
     */
    public void setVinculoEscolhido(Boolean vinculoEscolhido) {
        this.vinculoEscolhido = vinculoEscolhido;
    }

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof VinculoVO) {
            VinculoVO vinculo = (VinculoVO)obj;
            return this.getColaborador().getCodigo().intValue() == vinculo.getColaborador().getCodigo() &&
                   this.getCliente().getCodigo().intValue() == vinculo.getCliente().getCodigo() &&
                   this.getTipoVinculo().equals(vinculo.getTipoVinculo());
        }
        return false;
    }

    public TipoColaboradorEnum getTipoColaboradorVinculo(){
        TipoColaboradorEnum tipoColaborador = null;
        if(!UteisValidacao.emptyString(this.tipoVinculo)){
            tipoColaborador = TipoColaboradorEnum.getTipo(this.tipoVinculo);
        }
        return tipoColaborador;
    }
}

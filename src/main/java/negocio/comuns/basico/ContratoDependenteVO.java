package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class ContratoDependenteVO extends SuperVO {

    private int posicaoDependente;

    private ClienteVO cliente;

    private ContratoVO contrato;
    private Date dataInicio;
    private Date dataFinal;
    private Date dataFinalAjustada;

    public int getPosicaoDependente() {
        return posicaoDependente;
    }

    public void setPosicaoDependente(int posicaoDependente) {
        this.posicaoDependente = posicaoDependente;
    }

    public ClienteVO getCliente() {
        if (cliente == null) {
            cliente = new ClienteVO();
        }
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Date getDataFinalAjustada() {
        return dataFinalAjustada;
    }

    public void setDataFinalAjustada(Date dataFinalAjustada) {
        this.dataFinalAjustada = dataFinalAjustada;
    }

    public ContratoVO getContrato() {
        if (contrato == null) {
            contrato = new ContratoVO();
        }
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public Double getPercentualAndamento() {
        return Uteis.obterPosicaoData(Calendario.hoje(), dataInicio, dataFinalAjustada);
    }
}

package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * Reponsável por manter os dados da entidade Estado. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see EstadoVO
 */
public class EstadoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo = new Integer(0);
    protected String descricao = "";
    protected String sigla = "";
    @NaoControlarLogAlteracao
    protected Integer pais = new Integer(0);
    private Integer codigoAux; // atributo transient
    private String codigoIBGE;

    /**
     * Construtor padrão da classe <code>Estado</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public EstadoVO() {
        super();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>EstadoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(EstadoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getSigla().equals("")) {
            throw new ConsistirException("O campo SIGLA (Estado) deve ser informado.");
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Estado) deve ser informado.");
        }

    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setSigla(getSigla().toUpperCase());
    }

    public Integer getPais() {
    	if(pais == null){
    		pais = new Integer(0);
    	}
        return (pais);
    }

    public void setPais(Integer pais) {
        this.pais = pais;
    }

    public String getSigla() {
        if (sigla == null) {
            sigla = "";
        }
        return (sigla);
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EstadoWS toWS() {
        EstadoWS estadoWS = new EstadoWS();
        estadoWS.setCodigo(this.codigo);
        estadoWS.setCodigoPais(this.pais);
        estadoWS.setNome(this.descricao);
        estadoWS.setSigla(this.sigla);
        return  estadoWS;
    }

    public Integer getCodigoAux() {
        return codigoAux;
    }

    public void setCodigoAux(Integer codigoAux) {
        this.codigoAux = codigoAux;
    }

    public String getCodigoIBGE() {
        return codigoIBGE;
    }

    public void setCodigoIBGE(String codigoIBGE) {
        this.codigoIBGE = codigoIBGE;
    }
}
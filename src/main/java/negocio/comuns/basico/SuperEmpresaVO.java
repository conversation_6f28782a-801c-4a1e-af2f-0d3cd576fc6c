package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.util.HashMap;
import java.util.Map;

public class SuperEmpresaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String nome;
    protected String nomeCurto;

    @NaoControlarLogAlteracao
    protected transient byte foto[];
    protected String razaoSocial;
    protected String endereco;
    protected String numero;
    protected String complemento;
    protected String CEP;
    protected String CNPJ;
    protected String inscEstadual;
    protected String email;
    @NaoControlarLogAlteracao
    protected String site;
    @NaoControlarLogAlteracao
    protected String fax;
    @NaoControlarLogAlteracao
    protected CidadeVO cidade = new CidadeVO();
    @NaoControlarLogAlteracao
    @FKJson
    protected PaisVO pais;
    @NaoControlarLogAlteracao
    @FKJson
    protected EstadoVO estado;
    @NaoControlarLogAlteracao
    protected Integer carencia;
    @NaoControlarLogAlteracao
    protected String nomeFoto;
    @NaoControlarLogAlteracao
    protected String alturaFotoEmpresa;
    @NaoControlarLogAlteracao
    protected String larguraFotoEmpresa;
    @NaoControlarLogAlteracao
    protected transient byte fotoRelatorio[];
    @NaoControlarLogAlteracao
    protected String nomeFotoRelatorio;
    @NaoControlarLogAlteracao
    protected String alturaFotoRelatorio;
    @NaoControlarLogAlteracao
    protected String larguraFotoRelatorio;
    @NaoControlarLogAlteracao
    protected transient byte fotoEmail[];
    @NaoControlarLogAlteracao
    protected String alturaFotoEmail;
    @NaoControlarLogAlteracao
    protected String larguraFotoEmail;
    @NaoControlarLogAlteracao
    protected transient byte fotoRedeSocial[];
    @NaoControlarLogAlteracao
    protected String alturaFotoRedeSocial;
    @NaoControlarLogAlteracao
    protected String larguraFotoRedeSocial;
    @NaoControlarLogAlteracao
    protected transient byte homeBackground640x551[];
    @NaoControlarLogAlteracao
    protected String alturahomeBackground640x551;
    @NaoControlarLogAlteracao
    protected String largurahomeBackground640x551;
    @NaoControlarLogAlteracao
    protected transient byte homeBackground320x276[];
    @NaoControlarLogAlteracao
    protected String alturahomeBackground320x276;
    @NaoControlarLogAlteracao
    protected String largurahomeBackground320x276;
    @NaoControlarLogAlteracao
    protected transient byte propagandaBoleto[];
    @NaoControlarLogAlteracao
    protected String alturaPropagandaBoleto;
    @NaoControlarLogAlteracao
    protected String larguraPropagandaBoleto;
    protected String setor;
    protected String telComercial1;
    @NaoControlarLogAlteracao
    protected String telComercial2;
    @NaoControlarLogAlteracao
    protected String telComercial3;
    protected boolean ativa = true;
    @NaoControlarLogAlteracao
    protected Map<MidiaEntidadeEnum, byte[]> fotos = new HashMap<MidiaEntidadeEnum, byte[]>();
    @NaoControlarLogAlteracao
    protected Integer codigoChaveIntegracaoDigitais;
    public SuperEmpresaVO() {
        super();
        inicializarDados();
    }

    public static void validarDados(SuperEmpresaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getNome().equals("")) {
            throw new ConsistirException("O campo NOME (Super Empresa) deve ser informado.");
        }
        if (obj.getRazaoSocial().equals("")) {
            throw new ConsistirException("O campo RAZÃO SOCIAL (Super Empresa) deve ser informado.");
        }
        if (obj.getEndereco().equals("")) {
            throw new ConsistirException("O campo ENDEREÇO (Super Empresa) deve ser informado.");
        }
        if (obj.getCNPJ().equals("")) {
            throw new ConsistirException("O campo CNPJ (Super Empresa) deve ser informado.");
        }
        if (obj.getInscEstadual().equals("")) {
            throw new ConsistirException("O campo INSCRIÇÃO ESTADUAL (Super Empresa) deve ser informado.");
        }
    }

    public void realizarUpperCaseDados() {
        setNome(getNome().toUpperCase());
        setRazaoSocial(getRazaoSocial().toUpperCase());
        setEndereco(getEndereco().toUpperCase());
        setSetor(getSetor().toUpperCase());
        setNumero(getNumero().toUpperCase());
        setComplemento(getComplemento().toUpperCase());
        setCEP(getCEP().toUpperCase());
        setCNPJ(getCNPJ().toUpperCase());
        setInscEstadual(getInscEstadual().toUpperCase());
        setTelComercial1(getTelComercial1().toUpperCase());
        setTelComercial2(getTelComercial2().toUpperCase());
        setTelComercial3(getTelComercial3().toUpperCase());
        setEmail(getEmail().toUpperCase());
        setSite(getSite().toUpperCase());
        setFax(getFax().toUpperCase());
    }

    public void inicializarDados() {

    }

    public long getTimeStamp() {
        return System.currentTimeMillis();
    }

    public String getFax() {
        if (fax == null) {
            fax = "";
        }
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getSite() {
        if (site == null) {
            site = "";
        }
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelComercial3() {
        if (telComercial3 == null) {
            telComercial3 = "";
        }
        return telComercial3;
    }

    public void setTelComercial3(String telComercial3) {
        this.telComercial3 = telComercial3;
    }

    public String getTelComercial2() {
        if (telComercial2 == null) {
            telComercial2 = "";
        }
        return telComercial2;
    }

    public void setTelComercial2(String telComercial2) {
        this.telComercial2 = telComercial2;
    }

    public String getTelComercial1() {
        if (telComercial1 == null) {
            telComercial1 = "";
        }
        return telComercial1;
    }

    public void setTelComercial1(String telComercial1) {
        this.telComercial1 = telComercial1;
    }

    public String getInscEstadual() {
        if (inscEstadual == null) {
            inscEstadual = "";
        }
        return inscEstadual;
    }

    public void setInscEstadual(String inscEstadual) {
        this.inscEstadual = inscEstadual;
    }

    public String getCNPJ() {
        if (CNPJ == null) {
            CNPJ = "";
        }
        return CNPJ;
    }

    public void setCNPJ(String CNPJ) {
        this.CNPJ = CNPJ;
    }

    public String getCEP() {
        if (CEP == null) {
            CEP = "";
        }
        return (CEP);
    }

    public void setCEP(String CEP) {
        this.CEP = CEP;
    }

    public String getComplemento() {
        if (complemento == null) {
            complemento = "";
        }
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getNumero() {
        if (numero == null) {
            numero = "";
        }
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getSetor() {
        if (setor == null) {
            setor = "";
        }
        return setor;
    }

    public void setSetor(String setor) {
        this.setor = setor;
    }

    public String getEndereco() {
        if (endereco == null) {
            endereco = "";
        }
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getRazaoSocialParaSoftDescriptor(boolean verificar) {
        if (UteisValidacao.emptyString(getRazaoSocial())) {
            return "";
        } else {
            if (verificar) {
                return "PAC " + "VERIF*" + Uteis.retirarAcentuacaoRegex(Uteis.removerMascara(getRazaoSocial())).replace("&", "");
            } else {
                return "PAC " + Uteis.retirarAcentuacaoRegex(Uteis.removerMascara(getRazaoSocial())).replace("&", "");
            }
        }
    }

    public String getRazaoSocial() {
        if (razaoSocial == null) {
            razaoSocial = "";
        }
        return (razaoSocial);
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PaisVO getPais() {
        if (pais == null) {
            pais = new PaisVO();
        }
        return pais;
    }

    public void setPais(PaisVO obj) {
        this.pais = obj;
    }

    public CidadeVO getCidade() {
        if (cidade == null) {
            cidade = new CidadeVO();
        }
        return (cidade);
    }

    public void setCidade(CidadeVO obj) {
        this.cidade = obj;
    }

    public EstadoVO getEstado() {
        if (estado == null) {
            estado = new EstadoVO();
        }
        return estado;
    }

    public void setEstado(EstadoVO estado) {
        this.estado = estado;
    }

    public byte[] getFoto() {
        if (foto == null) {
            foto = new byte[0];
        }
        return foto;
    }

    public void setFoto(byte[] foto) {
        this.foto = foto;
    }

    public Boolean getExisteFoto() {
        return getFoto() != null;
    }

    public Boolean getExisteFotoRelatorio() {
        return getFotoRelatorio() != null;
    }

    public String getNomeFoto() {
        if (nomeFoto == null) {
            nomeFoto = "";
        }
        return nomeFoto;
    }

    public void setNomeFoto(String nomeFoto) {
        this.nomeFoto = nomeFoto;
    }

    public byte[] getFotoRelatorio() {
        if (fotoRelatorio == null) {
            fotoRelatorio = new byte[0];
        }
        return fotoRelatorio;
    }

    public void setFotoRelatorio(byte[] fotoRelatorio) {
        this.fotoRelatorio = fotoRelatorio;
    }

    public String getNomeFotoRelatorio() {
        if (nomeFotoRelatorio == null) {
            nomeFotoRelatorio = "";
        }
        return nomeFotoRelatorio;
    }

    public void setNomeFotoRelatorio(String nomeFotoRelatorio) {
        this.nomeFotoRelatorio = nomeFotoRelatorio;
    }

    public String getAlturaFotoEmpresa() {
        if (alturaFotoEmpresa == null) {
            alturaFotoEmpresa = "";
        }
        return alturaFotoEmpresa;
    }

    public void setAlturaFotoEmpresa(String alturaFotoEmpresa) {
        this.alturaFotoEmpresa = alturaFotoEmpresa;
    }

    public String getAlturaFotoRelatorio() {
        if (alturaFotoRelatorio == null) {
            alturaFotoRelatorio = "";
        }
        return alturaFotoRelatorio;
    }

    public void setAlturaFotoRelatorio(String alturaFotoRelatorio) {
        this.alturaFotoRelatorio = alturaFotoRelatorio;
    }

    public String getLarguraFotoEmpresa() {
        if (larguraFotoEmpresa == null) {
            larguraFotoEmpresa = "";
        }
        return larguraFotoEmpresa;
    }

    public void setLarguraFotoEmpresa(String larguraFotoEmpresa) {
        this.larguraFotoEmpresa = larguraFotoEmpresa;
    }

    public String getLarguraFotoRelatorio() {
        if (larguraFotoRelatorio == null) {
            larguraFotoRelatorio = "";
        }
        return larguraFotoRelatorio;
    }

    public void setLarguraFotoRelatorio(String larguraFotoRelatorio) {
        this.larguraFotoRelatorio = larguraFotoRelatorio;
    }

    public Integer getCarencia() {
        if (carencia == null) {
            carencia = 0;
        }
        return carencia;
    }

    public void setCarencia(Integer carencia) {
        this.carencia = carencia;
    }

    public byte[] getFotoEmail() {
        if (this.fotoEmail == null) {
            this.fotoEmail = new byte[0];
        }
        return this.fotoEmail;
    }

    public void setFotoEmail(byte[] fotoEmail) {
        this.fotoEmail = fotoEmail;
    }

    public String getAlturaFotoEmail() {
        return this.alturaFotoEmail;
    }

    public void setAlturaFotoEmail(String alturaFotoEmail) {
        this.alturaFotoEmail = alturaFotoEmail;
    }

    public String getLarguraFotoEmail() {
        return this.larguraFotoEmail;
    }

    public void setLarguraFotoEmail(String larguraFotoEmail) {
        this.larguraFotoEmail = larguraFotoEmail;
    }

    public Boolean getExisteFotoEmail() {
        return getFotoEmail() != null;
    }

    public Boolean getExisteFotoRedeSocial() {
        return getFotoRedeSocial() != null;
    }

    public byte[] getFotoRedeSocial() {
        return fotoRedeSocial;
    }

    public void setFotoRedeSocial(byte[] fotoRedeSocial) {
        this.fotoRedeSocial = fotoRedeSocial;
    }

    public String getAlturaFotoRedeSocial() {
        if (alturaFotoRedeSocial == null) {
            alturaFotoRedeSocial = "";
        }
        return alturaFotoRedeSocial;
    }

    public void setAlturaFotoRedeSocial(String alturaFotoRedeSocial) {
        this.alturaFotoRedeSocial = alturaFotoRedeSocial;
    }

    public String getLarguraFotoRedeSocial() {
        if (larguraFotoRedeSocial == null) {
            larguraFotoRedeSocial = "";
        }
        return larguraFotoRedeSocial;
    }

    public void setLarguraFotoRedeSocial(String larguraFotoRedeSocial) {
        this.larguraFotoRedeSocial = larguraFotoRedeSocial;
    }

    public byte[] getHomeBackground640x551() {
        return homeBackground640x551;
    }

    public void setHomeBackground640x551(byte[] homeBackground640x551) {
        this.homeBackground640x551 = homeBackground640x551;
    }

    public byte[] getHomeBackground320x276() {
        return homeBackground320x276;
    }

    public void setHomeBackground320x276(byte[] homeBackground320x276) {
        this.homeBackground320x276 = homeBackground320x276;
    }

    public String getAlturahomeBackground640x551() {
        return alturahomeBackground640x551;
    }

    public void setAlturahomeBackground640x551(String alturahomeBackground640x551) {
        this.alturahomeBackground640x551 = alturahomeBackground640x551;
    }

    public String getLargurahomeBackground640x551() {
        return largurahomeBackground640x551;
    }

    public void setLargurahomeBackground640x551(String largurahomeBackground640x551) {
        this.largurahomeBackground640x551 = largurahomeBackground640x551;
    }

    public String getAlturahomeBackground320x276() {
        return alturahomeBackground320x276;
    }

    public void setAlturahomeBackground320x276(String alturahomeBackground320x276) {
        this.alturahomeBackground320x276 = alturahomeBackground320x276;
    }

    public String getLargurahomeBackground320x276() {
        return largurahomeBackground320x276;
    }

    public void setLargurahomeBackground320x276(String largurahomeBackground320x276) {
        this.largurahomeBackground320x276 = largurahomeBackground320x276;
    }

    public Map<MidiaEntidadeEnum, byte[]> getFotos() {
        return fotos;
    }

    public boolean isAtiva() {
        return ativa;
    }

    public void setAtiva(boolean ativa) {
        this.ativa = ativa;
    }

    public byte[] getPropagandaBoleto() {
        if (this.propagandaBoleto == null) {
            this.propagandaBoleto = new byte[0];
        }
        return propagandaBoleto;
    }

    public void setPropagandaBoleto(byte[] propagandaBoleto) {
        this.propagandaBoleto = propagandaBoleto;
    }

    public String getAlturaPropagandaBoleto() {
        if (alturaPropagandaBoleto == null) {
            alturaPropagandaBoleto = "";
        }
        return alturaPropagandaBoleto;
    }

    public void setAlturaPropagandaBoleto(String alturaPropagandaBoleto) {
        this.alturaPropagandaBoleto = alturaPropagandaBoleto;
    }

    public String getLarguraPropagandaBoleto() {
        if (larguraPropagandaBoleto == null) {
            larguraPropagandaBoleto = "";
        }
        return larguraPropagandaBoleto;
    }

    public void setLarguraPropagandaBoleto(String larguraPropagandaBoleto) {
        this.larguraPropagandaBoleto = larguraPropagandaBoleto;
    }

    public Integer getCodigoChaveIntegracaoDigitais() {
        if (codigoChaveIntegracaoDigitais == null) {
            codigoChaveIntegracaoDigitais = 0;
        }
        return codigoChaveIntegracaoDigitais;
    }

    public void setCodigoChaveIntegracaoDigitais(Integer codigoChaveIntegracaoDigitais) {
        this.codigoChaveIntegracaoDigitais = codigoChaveIntegracaoDigitais;
    }

    public String getNomeCurto() {
        return nomeCurto;
    }

    public void setNomeCurto(String nomeCurto) {
        this.nomeCurto = nomeCurto;
    }
}

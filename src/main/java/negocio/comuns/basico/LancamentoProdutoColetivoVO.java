/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.TipoLancamentoProdutoColetivoEnum;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class LancamentoProdutoColetivoVO extends SuperVO{

    
    @ChaveEstrangeira
    private ProdutoVO produtoVO = new ProdutoVO();
    @ChaveEstrangeira
    private PlanoVO planoVO = new PlanoVO();
    private TipoLancamentoProdutoColetivoEnum tipoLancamento = TipoLancamentoProdutoColetivoEnum.DATA_ESPECIFICA;
    private Date dataEspecifica;
    private Date dataLancamento = Calendario.hoje();
    private Date dataFim;
    private Mes mes = Mes.VAZIO;
    private Integer parcela;
    @ChaveEstrangeira
    private ModalidadeVO modalidadeVO = new ModalidadeVO();
    private Integer nrVezesParcelar;
    private String descricao;
    private String matriculas;
    @ChaveEstrangeira
    private UsuarioVO usuario = new UsuarioVO();
    @ChaveEstrangeira
    private EmpresaVO empresa = new EmpresaVO();
    private Boolean jaFoiLancado = Boolean.FALSE;
    private Double valor;
    //Periodo tem como finalidade abranger somente os clientes com plano com dataInicio no período e que esteja ativo.
    private Date vigenciaContratoInicio = null;
    private Date vigenciaContratoFim = null;

    public static void validarDados(LancamentoProdutoColetivoVO obj) throws Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getEmpresa().getCodigo() == 0 || obj.getEmpresa() == null) {
            throw new ConsistirException("O campo EMPRESA deve ser informado.");
        }
        if (obj.getProdutoVO().getCodigo() == 0 || obj.getProdutoVO() == null) {
            throw new ConsistirException("O campo PRODUTO deve ser informado.");
        }
        if (obj.getDescricao().trim().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO deve ser informado.");
        }
        if (obj.getTipoData() && obj.getDataEspecifica() == null) {
            throw new ConsistirException("O campo DATA ESPECÍFICA deve ser informado.");
        }
    
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public UsuarioVO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public Date getDataEspecifica() {
        return dataEspecifica;
    }

    public void setDataEspecifica(Date dataEspecifica) {
        this.dataEspecifica = dataEspecifica;
    }

    public Mes getMes() {
        return mes;
    }

    public void setMes(Mes mes) {
        this.mes = mes;
    }

    public ModalidadeVO getModalidadeVO() {
        return modalidadeVO;
    }

    public void setModalidadeVO(ModalidadeVO modalidadeVO) {
        this.modalidadeVO = modalidadeVO;
    }

    public Integer getNrVezesParcelar() {
        return nrVezesParcelar;
    }

    public void setNrVezesParcelar(Integer nrVezesParcelar) {
        this.nrVezesParcelar = nrVezesParcelar;
    }

    public Integer getParcela() {
        return parcela;
    }

    public void setParcela(Integer parcela) {
        this.parcela = parcela;
    }

    public PlanoVO getPlanoVO() {
        return planoVO;
    }

    public void setPlanoVO(PlanoVO planoVO) {
        this.planoVO = planoVO;
    }

    public ProdutoVO getProdutoVO() {
        return produtoVO;
    }

    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    public TipoLancamentoProdutoColetivoEnum getTipoLancamento() {
        return tipoLancamento;
    }

    public void setTipoLancamento(TipoLancamentoProdutoColetivoEnum tipoLancamento) {
        this.tipoLancamento = tipoLancamento;
    }

    public String getUsuarioNome() {
        return getUsuario().getNome();
    }

    public String getProdutoNome() {
        return getProdutoVO().getDescricao();
    }

    public boolean getTipoMes() {
        return tipoLancamento != null && tipoLancamento.equals(TipoLancamentoProdutoColetivoEnum.MES);
    }

    public boolean getTipoData() {
        return tipoLancamento != null && tipoLancamento.equals(TipoLancamentoProdutoColetivoEnum.DATA_ESPECIFICA);
    }

    public boolean getTipoParcela() {
        return tipoLancamento != null && tipoLancamento.equals(TipoLancamentoProdutoColetivoEnum.PARCELA);
    }

    public boolean getTipoContratoSemProduto() {
        return tipoLancamento != null && tipoLancamento.equals(TipoLancamentoProdutoColetivoEnum.CONTRATO_SEM_PRODUTO);
    }

    public Date getVigenciaContratoInicio() {
        return vigenciaContratoInicio;
    }

    public void setVigenciaContratoInicio(Date vigenciaContratoInicio) {
        this.vigenciaContratoInicio = vigenciaContratoInicio;
    }

    public Date getVigenciaContratoFim() {
        return vigenciaContratoFim;
    }

    public void setVigenciaContratoFim(Date vigenciaContratoFim) {
        this.vigenciaContratoFim = vigenciaContratoFim;
    }

    public Boolean getJaFoiLancado() {
        return jaFoiLancado;
    }

    public void setJaFoiLancado(Boolean jaFoiLancado) {
        this.jaFoiLancado = jaFoiLancado;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getMatriculas() {
        return matriculas;
    }

    public void setMatriculas(String matriculas) {
        this.matriculas = matriculas;
    }
}

package negocio.comuns.basico;

import org.json.JSONObject;

public class EnderecoSescTO {
    private String Logradouro;
    private String NumeroImovel;
    private String ComplementoNumeroImovel;
    private String Bairro;
    private String Municipio;
    private String Uf;
    private String Cep;

    public EnderecoSescTO() {
    }

    public EnderecoSescTO(JSONObject json, boolean usarApiSescGo) {
        if (usarApiSescGo) {
            this.Logradouro = json.optString("dslogradou");
            this.NumeroImovel = String.valueOf(json.optInt("nuimovel"));
            this.ComplementoNumeroImovel = json.optString("dscomplem");
            this.Bairro = json.optString("dsbairro");
            this.Municipio = json.optString("dsmunicip");
            this.Uf = json.optString("siestado");
            this.Cep = json.optString("nucep");

        } else {
            this.Logradouro = json.optString("Logradouro");
            this.NumeroImovel = json.optString("NumeroImovel");
            this.ComplementoNumeroImovel = json.optString("ComplementoNumeroImovel");
            this.Bairro = json.optString("Bairro").trim();
            this.Municipio = json.optString("Municipio").trim();
            this.Uf = json.optString("Uf");
            this.Cep = json.optString("Cep");
        }
    }

    public String getLogradouro() {
        return Logradouro;
    }

    public void setLogradouro(String logradouro) {
        Logradouro = logradouro;
    }

    public String getNumeroImovel() {
        return NumeroImovel;
    }

    public void setNumeroImovel(String numeroImovel) {
        NumeroImovel = numeroImovel;
    }

    public String getComplementoNumeroImovel() {
        return ComplementoNumeroImovel;
    }

    public void setComplementoNumeroImovel(String complementoNumeroImovel) {
        ComplementoNumeroImovel = complementoNumeroImovel;
    }

    public String getBairro() {
        return Bairro;
    }

    public void setBairro(String bairro) {
        Bairro = bairro;
    }

    public String getMunicipio() {
        return Municipio;
    }

    public void setMunicipio(String municipio) {
        Municipio = municipio;
    }

    public String getUf() {
        return Uf;
    }

    public void setUf(String uf) {
        Uf = uf;
    }

    public String getCep() {
        return Cep;
    }

    public void setCep(String cep) {
        Cep = cep;
    }
}

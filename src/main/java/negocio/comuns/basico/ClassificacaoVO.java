package negocio.comuns.basico;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade Classificacao. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
*/
public class ClassificacaoVO extends SuperVO {
	
    @ChavePrimaria
    protected Integer codigo;
    protected String nome;
    private Boolean enviarSMSAutomatico = false;
    /**
     * Construtor padrão da classe <code>Classificacao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public ClassificacaoVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ClassificacaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(ClassificacaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
        if (obj.getNome().equals("")) { 
            throw new ConsistirException("O campo NOME (Classificação) deve ser informado.");
        }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
        setNome( getNome().toUpperCase() );
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setNome( "" );
    }
	

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return (nome);
    }
     
    public void setNome( String nome ) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }

    /**
     * @return the enviarSMSAutomatico
     */
    public Boolean getEnviarSMSAutomatico() {
        if(enviarSMSAutomatico==null){
            enviarSMSAutomatico = false;
        }
        return enviarSMSAutomatico;
    }

    /**
     * @param enviarSMSAutomatico the enviarSMSAutomatico to set
     */
    public void setEnviarSMSAutomatico(Boolean enviarSMSAutomatico) {
        this.enviarSMSAutomatico = enviarSMSAutomatico;
    }

    public String getEnviarSMSAutomatico_Apresentar() {
        return getEnviarSMSAutomatico() ? "Sim" : "Não";
    }
}
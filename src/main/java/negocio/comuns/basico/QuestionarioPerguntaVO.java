package negocio.comuns.basico;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

import java.util.Objects;

/**
 * Reponsável por manter os dados da entidade QuestionarioPergunta. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see Questionario
*/
public class QuestionarioPerguntaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer questionario;
    protected Integer nrQuestao;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Pergunta </code>.*/
    @ChaveEstrangeira
    @FKJson()
    protected PerguntaVO pergunta;
    private Boolean obrigatoria = Boolean.FALSE;
	
    /**
     * Construtor padrão da classe <code>QuestionarioPergunta</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public QuestionarioPerguntaVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>QuestionarioPerguntaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(QuestionarioPerguntaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
        if ((obj.getPergunta() == null) ||
            (obj.getPergunta().getCodigo().intValue() == 0)) { 
            throw new ConsistirException("O campo PERGUNTA (Questionário Pergunta) deve ser informado.");
        }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setNrQuestao( new Integer(0) );
        setPergunta( new PerguntaVO() );
    }
	

    /**
     * Retorna o objeto da classe <code>Pergunta</code> relacionado com (<code>QuestionarioPergunta</code>).
    */
    public PerguntaVO getPergunta() {
        if (pergunta == null) {
            pergunta = new PerguntaVO();
        }
        return (pergunta);
    }
     
    /**
     * Define o objeto da classe <code>Pergunta</code> relacionado com (<code>QuestionarioPergunta</code>).
    */
    public void setPergunta( PerguntaVO obj) {
        this.pergunta = obj;
    }

    public Integer getQuestionario() {
        return (questionario);
    }
     
    public void setQuestionario( Integer questionario ) {
        this.questionario = questionario;
    }

    public Integer getCodigo() {
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }

    public Integer getNrQuestao() {
        return nrQuestao;
    }

    public void setNrQuestao(Integer nrQuestao) {
        this.nrQuestao = nrQuestao;
    }

    public Boolean getObrigatoria() {
        return obrigatoria;
    }

    public void setObrigatoria(Boolean obrigatoria) {
        this.obrigatoria = obrigatoria;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        QuestionarioPerguntaVO that = (QuestionarioPerguntaVO) o;
        return  Objects.equals(questionario, that.questionario) &&
                Objects.equals(nrQuestao, that.nrQuestao) &&
                Objects.equals(pergunta, that.pergunta) &&
                Objects.equals(obrigatoria, that.obrigatoria);
    }

    @Override
    public int hashCode() {
        return Objects.hash(questionario, nrQuestao, pergunta, obrigatoria);
    }
}
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
/**
 *
 * <AUTHOR>
 */
public class MovimentoContaCorrenteClienteFiltroVO extends SuperVO  {
    //atributo usado quando o usuario acessar o sistema e tiver uma empresa logada
    private EmpresaVO empresaVO;
    //atributo usado quando o usuario digitar um codigo de movimento, nome de cliente, descricao
    private MovimentoContaCorrenteClienteVO movContaCorrenteClienteVO;
    private boolean controlarAcesso;
    private int nivelMontarDados;
    private int codigoPessoa;

    /**
     * @return O campo empresaVO.
     */
    public EmpresaVO getEmpresaVO() {
        return this.empresaVO;
    }

    /**
     * @param empresaVO O novo valor de empresaVO.
     */
    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    /**
     * @return O campo nivelMontarDados.
     */
    public int getNivelMontarDados() {
        return this.nivelMontarDados;
    }

    /**
     * @param nivelMontarDados O novo valor de nivelMontarDados.
     */
    public void setNivelMontarDados(int nivelMontarDados) {
        this.nivelMontarDados = nivelMontarDados;
    }

    /**
     * @return O campo controlarAcesso.
     */
    public boolean isControlarAcesso() {
        return this.controlarAcesso;
    }

    /**
     * @param controlarAcesso O novo valor de controlarAcesso.
     */
    public void setControlarAcesso(boolean controlarAcesso) {
        this.controlarAcesso = controlarAcesso;
    }

    /**
     * @return the movContaCorrenteClienteVO
     */
    public MovimentoContaCorrenteClienteVO getMovContaCorrenteClienteVO() {
        return movContaCorrenteClienteVO;
    }

    /**
     * @param movContaCorrenteClienteVO the movContaCorrenteClienteVO to set
     */
    public void setMovContaCorrenteClienteVO(MovimentoContaCorrenteClienteVO movContaCorrenteClienteVO) {
        this.movContaCorrenteClienteVO = movContaCorrenteClienteVO;
    }

	public void setCodigoPessoa(int codigoPessoa) {
		this.codigoPessoa = codigoPessoa;
	}

	public int getCodigoPessoa() {
		return codigoPessoa;
	}


}

package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

/*
 * Created by <PERSON><PERSON>
 */
public class TelefoneWS extends SuperTO {

    private Integer codigo;
    private String numero;
    private String tipoTelefone;
    private String descricao;
    private String tipoTelefoneDescricao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getTipoTelefone() {
        return tipoTelefone;
    }

    public void setTipoTelefone(String tipoTelefone) {
        this.tipoTelefone = tipoTelefone;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipoTelefoneDescricao() {
        return tipoTelefoneDescricao;
    }

    public void setTipoTelefoneDescricao(String tipoTelefoneDescricao) {
        this.tipoTelefoneDescricao = tipoTelefoneDescricao;
    }
}
package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.to.PessoaTO;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.integracao.pactopay.TipoDocumentoEnum;
import br.com.pactosolucoes.integracao.pactopay.dto.ClienteDTO;
import br.com.pactosolucoes.integracao.pactopay.dto.EnderecoDTO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.CargoEnum;
import negocio.comuns.basico.enumerador.FuncaoEnum;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.enumerador.TipoBloqueioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.lang.ArrayUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade Pessoa. Classe do tipo VO - Value
 * Object composta pelos atributos da entidade com visibilidade protegida e os
 * métodos de acesso a estes atributos. Classe utilizada para apresentar e
 * manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class PessoaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    protected Date dataCadastro = Calendario.hoje();
    protected String nome = "";
    protected Date dataNasc;
    protected String nomePai = "";
    protected String nomeMae = "";
    private Date dataNascimentoResponsavel;
    protected String telefones = "";
    protected List nomePerfilEvento = new ArrayList();
    //protected String grauInstrucao;
    protected String cfp = "";
    protected String rne = "";
    protected String passaporte = "";
    protected String rg = "";
    protected String rgOrgao = "";
    protected String rgUf = "";
    protected String estadoCivil = "";
    protected String nacionalidade = "";
    protected String naturalidade = "";
    protected String sexo = "";
    protected String email = "";
    protected String webPage = "";
    protected String senhaAcesso = "";
    protected String rgMae = "";
    protected String rgPai = "";

    @NaoControlarLogAlteracao
    protected String confirmarSenhaAcesso = "";
    @NaoControlarLogAlteracao
    protected Boolean apresentarRichModalErro;
    @ChaveEstrangeira
    @FKJson
    protected EstadoVO estadoVO = new EstadoVO();
    @NaoControlarLogAlteracao
    protected Boolean adicionar;
    @NaoControlarLogAlteracao
    protected String nomeFoto = "";
    @NaoControlarLogAlteracao
    protected transient byte foto[];
    @NaoControlarLogAlteracao
    protected Boolean novaFoto;
    @NaoControlarLogAlteracao
    protected Boolean liberaSenhaAcesso = false;
    private boolean emitirNotaNomeMae = true;
    private Integer idVindi;
    private Date dataAlteracaoVindi;
    private String idMaxiPago;
    private Date dataAlteracaoMaxiPago;
    private int qtdeTelefones = 0;
    private Long idExterno;
    private String idExternoIntegracao;
    private String idGetNet;
    private Date dataAlteracaoGetNet;
    private String contatoEmergencia;
    private String telefoneEmergencia;
    @NaoControlarLogAlteracao
    private  Boolean formularioCliente = false;
    private Integer spiviClientID = 0;
    private String idMundiPagg;
    private Date dataAlteracaoMundiPagg;
    private String idPagarMe;
    private Date dataAlteracaoPagarMe;
    private String idStone;
    private Date dataAlteracaoStone;
    private String customerIdPagoLivre;
    private String idAsaas;
    private Date dataAlteracaoIdAsaas;
    private Date dataAlteracaoPagoLivre;
    @NaoControlarLogAlteracao
    protected String identificadorEntidade;
    @NaoControlarLogAlteracao
    protected Date lancamentoCartaoVacina;
    private String nomeRespFinanceiro;
    private String emailRespFinanceiro;
    private String cpfRespFinanceiro;
    private String rgRespFinanceiro;


    /**
     * Atributo responsável por manter os objetos da classe
     * <code>Endereco</code>.
     */
    @Lista
    @ListJson(clazz = EnderecoVO.class)
    private List<EnderecoVO> enderecoVOs = new ArrayList<EnderecoVO>();
    /**
     * Atributo responsável por manter os objetos da classe
     * <code>Telefone</code>.
     */
    @Lista
    @ListJson(clazz = TelefoneVO.class)
    private List telefoneVOs = new ArrayList();
    /**
     * Atributo responsável por manter os objetos da classe
     * <code>Telefone</code>.
     */
    @Lista
    @ListJson(clazz = EmailVO.class)
    private List<EmailVO> emailVOs = new ArrayList<EmailVO>();

    @Lista
    private List<PlacaVO> placaVOs = new ArrayList<PlacaVO>();
    /**
     * Atributo responsável por manter o objeto relacionado da classe <code>Profissao </code>.
     */
    @ChaveEstrangeira
    @FKJson
    protected ProfissaoVO profissao = new ProfissaoVO();
    /**
     * Atributo responsável por manter o objeto relacionado da classe <code>Cidade </code>.
     */
    @ChaveEstrangeira
    @FKJson
    protected CidadeVO cidade = new CidadeVO();
    /**
     * Atributo responsável por manter o objeto relacionado da classe <code>Pais </code>.
     */
    @ChaveEstrangeira
    @FKJson
    protected PaisVO pais = new PaisVO();
    @ChaveEstrangeira
    @FKJson
    protected GrauInstrucaoVO grauInstrucao = new GrauInstrucaoVO();
    private List eventos = new ArrayList();
    private String tipoPessoa = "";
    protected Boolean selecionado = false;
    private String emails;
    private String fotoKey;
    private TipoPessoa categoriaPessoa = TipoPessoa.FISICA;
    private String cnpj;
    private String cnpjSesi;
    private String inscEstadual;
    private String cfdf;
    @NaoControlarLogAlteracao
    private String numeroTelefonesApresentar;
    private String inscMunicipal;
    private String inscEstadualTerceiro;
    private String cfdfTerceiro;

    @NaoControlarLogAlteracao
    private String nomeComCodigo;

    private boolean emitirNotaNomeAluno = false;

    private boolean emitirNomeTerceiro = false;
    private String nomeTerceiro;
    private String cpfCNPJTerceiro;
    private String observacaoNota;

    @NaoControlarLogAlteracao
    private String assinaturaBiometriaDigital = ""; //atributo deve ser preenchido onde for usado devido o tamanho by Luiz Felipe
    @NaoControlarLogAlteracao
    private boolean assinaturaBiometriaDigitalB = false;
    @NaoControlarLogAlteracao
    private String assinaturaBiometriaFacial = ""; //atributo deve ser preenchido onde for usado devido o tamanho by Luiz Felipe
    @NaoControlarLogAlteracao
    private boolean assinaturaBiometriaFacialB = false;

    private boolean utilizaDotz = false;

    private String cpfPai;
    private String cpfMae;
    @NaoControlarLogAlteracao
    private String sobreNome;
    private CargoEnum cargoEnum;
    private FuncaoEnum funcaoEnum;
    private boolean atualizarDados = true;
    private boolean aceiteTermosPacto = false;
    private String ipAceiteTermosPacto;
    private String infoBrowserTermosPacto;
    private Date dataAceiteTermosPacto;
    private boolean receberSMSPacto = false;
    private boolean receberEmailNovidadesPacto = false;
    private Date dataBloqueioCobrancaAutomatica;
    private TipoBloqueioCobrancaEnum tipoBloqueioCobrancaAutomatica;
    private Double valorLimiteCaixaAbertoVendaAvulsa;
    private String genero = "";

    private String nomeRegistro = "";
    @NaoControlarLogAlteracao
    private boolean constaRestricaoSPC = false;
    private String emailPai;
    private String emailMae;
    private String nomeResponsavelEmpresa;
    private String cpfResponsavelEmpresa;
    private String nomeEmpresa;
    private static final String COL_CODIGO = "codigo";
    private static final String COL_NOME = "nome";
    private static final String COL_SENHAACESSO = "senhaacesso";
    private static final String COL_TELEFONES = "telefones";
    private static final String COL_EMAILS = "emails";
    private static final String COL_DATANASCIMENTO = "datanascimento";
    /**
     * Construtor padrão da classe <code>Pessoa</code>. Cria uma nova instância
     * desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public PessoaVO() {
        super();
    }

    public PessoaVO(Integer codigo) {
        this.codigo = codigo;
    }

    public PessoaVO(JSONObject o) {
        super();
        try {
            setCodigo(o.getInt(COL_CODIGO));
            setNome(o.getString(COL_NOME));
            try {
                setSenhaAcesso(o.getString(COL_SENHAACESSO));
            } catch (Exception e) {
                setSenhaAcesso("");
            }

            JSONArray mJsonTel = new JSONArray(o.getString(COL_TELEFONES));
            for (int i = 0; i < mJsonTel.length(); i++) {
                getTelefoneVOs().add(new TelefoneVO(mJsonTel.getJSONObject(i)));
            }

            JSONArray mJsonEm = new JSONArray(o.getString(COL_EMAILS));
            for (int i = 0; i < mJsonEm.length(); i++) {
                getEmailVOs().add(new EmailVO(mJsonEm.getJSONObject(i)));
            }
            String dataNascimento = o.optString(COL_DATANASCIMENTO);
            if (!UteisValidacao.emptyString(dataNascimento)) {
                setDataNasc(Uteis.getDate(dataNascimento));
            }
            setFotoKey(o.optString("fotoKey"));

            if (o.has("cpf")) {
                setCfp(o.optString("cpf"));
            }

            if (o.has("assinaturaBiometriaDigital")) {
                setAssinaturaBiometriaDigital(o.optString("assinaturaBiometriaDigital"));
            }

            if (o.has("assinaturaBiometriaFacial")) {
                setAssinaturaBiometriaFacial(o.optString("assinaturaBiometriaFacial"));
            }

        } catch (Exception ignored) {
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>PessoaVO</code>. Todos os tipos de consistência de dados são e
     * devem ser implementadas neste método. São validações típicas: verificação
     * de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada
     *                            aumaticamente é gerada uma exceção descrevendo o atributo e o erro
     *                            ocorrido.
     */
    public static void validarDados(PessoaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
    }

    /**
     * Método usado no cadastro de cliente simplificado do FINANCEIRO
     *
     * @param obj
     */
    public static void validarDadosSimplificado(PessoaVO obj) throws Exception {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getNome() == null || obj.getNome().isEmpty()) {
            throw new ConsistirException("O campo NOME deve ser informado.");
        }
        if (obj.getTipoPessoa() == null || obj.getTipoPessoa().isEmpty()) {
            throw new ConsistirException("O campo TIPO de Pessoa deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        setNome(getNome().toUpperCase());
        setSobreNome(getSobreNome().toUpperCase());
        setNomePai(getNomePai().toUpperCase());
        setNomeMae(getNomeMae().toUpperCase());
        setCfp(getCfp().toUpperCase());
        setRne(getRne().toUpperCase());
        setPassaporte(getPassaporte().toUpperCase());
        setRg(getRg().toUpperCase());
        setRgOrgao(getRgOrgao().toUpperCase());
        setRgUf(getRgUf().toUpperCase());
        setEstadoCivil(getEstadoCivil().toUpperCase());
        setNacionalidade(getNacionalidade().toUpperCase());
        setNaturalidade(getNaturalidade().toUpperCase());
        setSexo(getSexo().toUpperCase());
        setWebPage(getWebPage().toUpperCase());
        setGenero(getGenero().toUpperCase());
        if (getNomeResponsavelEmpresa() != null) {
            setNomeResponsavelEmpresa(getNomeResponsavelEmpresa().toUpperCase());
        }
        if (getCpfResponsavelEmpresa() != null) {
            setCpfResponsavelEmpresa(getCpfResponsavelEmpresa().toUpperCase());
        }
        setNomeRegistro(getNomeRegistro().toUpperCase());
    }

    public static void validarCPF(PessoaVO obj, String empresa) throws ConsistirException, Exception {
        PessoaVO validaCPF = new PessoaVO();
        try {
            validaCPF = getFacade().getPessoa().consultarPorCPF(obj.getCfp(), obj.getCodigo().intValue(), false);
            if ((validaCPF != null) && (!obj.getCfp().trim().equals(""))) {
                throw new ConsistirException("O CPF digitado " + validaCPF.getCfp() + ".  Já está cadastrado no sistema com o nome: " + validaCPF.getNome());
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>TelefoneVO</code> ao List <code>telefoneVOs</code>. Utiliza o
     * atributo padrão de consulta da classe <code>Telefone</code> - getNumero()
     * - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>TelefoneVO</code> que será adiocionado
     *            ao Hashtable correspondente.
     */
    public void adicionarObjTelefoneVOsSimples(TelefoneVO obj) throws Exception {
        obj.validarDados(obj.isUsarSistemaInternacional());
        int index = 0;
        Iterator i = getTelefoneVOs().iterator();
        while (i.hasNext()) {
            TelefoneVO objExistente = (TelefoneVO) i.next();
            if (objExistente.getTipoTelefone().equals(obj.getTipoTelefone()) && objExistente.getCodigo().equals(obj.getCodigo())) {
                getTelefoneVOs().set(index, obj);
                return;
            }
            index++;
        }
        getTelefoneVOs().add(obj);
    }

    public void adicionarObjTelefoneVOs(TelefoneVO obj) throws Exception {
        obj.validarDados(obj.isUsarSistemaInternacional());
        int index = 0;
        Iterator i = getTelefoneVOs().iterator();
        while (i.hasNext()) {
            TelefoneVO objExistente = (TelefoneVO) i.next();
            if (objExistente.getNumero().equals(obj.getNumero())) {
                getTelefoneVOs().set(index, obj);
                return;
            }
            index++;
        }
        obj.setCodigo(0);
        getTelefoneVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>TelefoneVO</code> no List <code>telefoneVOs</code>. Utiliza o
     * atributo padrão de consulta da classe <code>Telefone</code> - getNumero()
     * - como identificador (key) do objeto no List.
     *
     * @param numero Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjTelefoneVOs(String numero) throws Exception {
        int index = 0;
        Iterator i = getTelefoneVOs().iterator();
        while (i.hasNext()) {
            TelefoneVO objExistente = (TelefoneVO) i.next();
            if (objExistente.getNumero().equals(numero)) {
                getTelefoneVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>TelefoneVO</code> no List <code>telefoneVOs</code>. Utiliza o
     * atributo padrão de consulta da classe <code>Telefone</code> - getNumero()
     * - como identificador (key) do objeto no List.
     *
     * @param numero Parâmetro para localizar o objeto do List.
     */
    public TelefoneVO consultarObjTelefoneVO(String numero) throws Exception {
        Iterator i = getTelefoneVOs().iterator();
        while (i.hasNext()) {
            TelefoneVO objExistente = (TelefoneVO) i.next();
            if (objExistente.getNumero().equals(numero)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>EnderecoVO</code> ao List <code>enderecoVOs</code>. Utiliza o
     * atributo padrão de consulta da classe <code>Endereco</code> - getCodigo()
     * - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>EnderecoVO</code> que será adiocionado
     *            ao Hashtable correspondente.
     */
    public void adicionarObjEnderecoVOsSimples(EnderecoVO obj) throws Exception {
        EnderecoVO.validarDados(obj);
        int index = 0;
        Iterator i = getEnderecoVOs().iterator();
        while (i.hasNext()) {
            EnderecoVO objExistente = (EnderecoVO) i.next();
            if (objExistente.getTipoEndereco().equals(obj.getTipoEndereco())) {
                getEnderecoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getEnderecoVOs().add(obj);
        //adicionarObjSubordinadoOC
    }

    public void adicionarObjEnderecoVOs(EnderecoVO obj) throws Exception {
        EnderecoVO.validarDados(obj);
        int index = 0;
        Iterator i = getEnderecoVOs().iterator();
        while (i.hasNext()) {
            EnderecoVO objExistente = (EnderecoVO) i.next();
            if (objExistente.getEndereco().equals(obj.getEndereco())
                    && (objExistente.getNumero().equals(obj.getNumero()))
                    && objExistente.getBairro().equals(obj.getBairro())
                    && objExistente.getComplemento().equals(obj.getComplemento())) {
                getEnderecoVOs().set(index, obj);
                return;
            }
            index++;
        }
        obj.setCodigo(0);
        getEnderecoVOs().add(obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>EnderecoVO</code> no List <code>enderecoVOs</code>. Utiliza o
     * atributo padrão de consulta da classe <code>Endereco</code> - getCodigo()
     * - como identificador (key) do objeto no List.
     *
     * @param obj Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjEnderecoVOs(EnderecoVO obj) throws Exception {
        int index = 0;
        Iterator i = getEnderecoVOs().iterator();
        while (i.hasNext()) {
            EnderecoVO objExistente = (EnderecoVO) i.next();
            if (objExistente.getEndereco().equals(obj.getEndereco())
                    && (objExistente.getNumero().equals(obj.getNumero()))
                    && objExistente.getBairro().equals(obj.getBairro())
                    && objExistente.getComplemento().equals(obj.getComplemento())) {
                getEnderecoVOs().remove(index);
                return;
            }
            index++;
        }
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por consultar um objeto da classe
     * <code>EnderecoVO</code> no List <code>enderecoVOs</code>. Utiliza o
     * atributo padrão de consulta da classe <code>Endereco</code> - getCodigo()
     * - como identificador (key) do objeto no List.
     *
     * @param obj Parâmetro para localizar o objeto do List.
     */
    public EnderecoVO consultarObjEnderecoVO(EnderecoVO obj) throws Exception {
        Iterator i = getEnderecoVOs().iterator();
        while (i.hasNext()) {
            EnderecoVO objExistente = (EnderecoVO) i.next();
            if (objExistente.getEndereco().equals(obj.getEndereco())
                    || (objExistente.getNumero().equals(obj.getNumero()))
                    || objExistente.getBairro().equals(obj.getBairro())
                    || objExistente.getComplemento().equals(obj.getComplemento())) {
                return objExistente;
            }
        }
        return null;
        //consultarObjSubordinadoOC
    }

    public void adicionarObjEmailVOs(EmailVO obj) throws Exception {
        EmailVO.validarDados(obj);
        int index = 0;
        Iterator i = getEmailVOs().iterator();
        while (i.hasNext()) {
            EmailVO objExistente = (EmailVO) i.next();
            if (objExistente.getEmail().equals(obj.getEmail())) {
                getEmailVOs().set(index, obj);
                return;
            }
            index++;
        }
        obj.setCodigo(0);
        getEmailVOs().add(obj);
        //adicionarObjSubordinadoOC
    }

    public void adicionarObjPlacaVO(PlacaVO obj) throws Exception {
        int index = 0;
        Iterator i = getPlacaVOs().iterator();
        while (i.hasNext()) {
            PlacaVO objExistente = (PlacaVO) i.next();
            if (objExistente.getPlaca().equals(obj.getPlaca())) {
                getPlacaVOs().set(index, obj);
                return;
            }
            index++;
        }
        obj.setCodigo(0);
        getPlacaVOs().add(obj);
    }

    public void excluirObjPlacaVOs(PlacaVO obj) throws Exception {
        int index = 0;
        Iterator i = getPlacaVOs().iterator();
        while (i.hasNext()) {
            PlacaVO objExistente = (PlacaVO) i.next();
            if (objExistente.getPlaca().equals(obj.getPlaca())) {
                getPlacaVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por excluir um objeto da classe
     * <code>EnderecoVO</code> no List <code>enderecoVOs</code>. Utiliza o
     * atributo padrão de consulta da classe <code>Endereco</code> - getCodigo()
     * - como identificador (key) do objeto no List.
     *
     * @param obj Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjEmailOs(EmailVO obj) throws Exception {
        int index = 0;
        Iterator i = getEmailVOs().iterator();
        while (i.hasNext()) {
            EmailVO objExistente = (EmailVO) i.next();
            if (objExistente.getEmail().equals(obj.getEmail())) {
                getEmailVOs().remove(index);
                return;
            }
            index++;
        }
        //excluirObjSubordinadoOC
    }

    /**
     * Retorna o objeto da classe <code>Pais</code> relacionado com
     * (<code>Pessoa</code>).
     */
    public PaisVO getPais() {
        if (pais == null) {
            pais = new PaisVO();
        }
        return (pais);
    }

    /**
     * Define o objeto da classe <code>Pais</code> relacionado com
     * (<code>Pessoa</code>).
     */
    public void setPais(PaisVO obj) {
        this.pais = obj;
    }

    /**
     * Retorna o objeto da classe <code>Cidade</code> relacionado com
     * (<code>Pessoa</code>).
     */
    public CidadeVO getCidade() {
        if (cidade == null) {
            cidade = new CidadeVO();
        }
        return (cidade);
    }

    /**
     * Define o objeto da classe <code>Cidade</code> relacionado com
     * (<code>Pessoa</code>).
     */
    public void setCidade(CidadeVO obj) {
        this.cidade = obj;
    }

    public String getTelefones() {
        return getTelefones(false);
    }

    public String getTelefonesString() {
        if (telefones == null) {
            telefones = "";
        }
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public String getTodosTelefones() {
        return getTelefones(true);
    }

    public String getTelefonesCelular() {
        return getTelefonesPorTipo(TipoTelefoneEnum.CELULAR.getCodigo());
    }

    public String getTelefonesPorTipo(String tipoTelefone) {
        StringBuilder tels = new StringBuilder();
        if (getTelefoneVOs().size() > 0) {
            int qtdTelefones = getTelefoneVOs().size();
            for (int i = 0; i < qtdTelefones; i++) {
                Object obj = getTelefoneVOs().get(i);
                if (obj instanceof TelefoneVO) {
                    TelefoneVO telefone = (TelefoneVO) obj;
                    if (Uteis.validarTelefoneCelular(telefone.getNumero()) && telefone.getTipoTelefone().equals(tipoTelefone)) {
                        if (tels.toString().isEmpty()) {
                            tels.append(telefone.getNumero());
                        } else {
                            tels.append(";").append(telefone.getNumero());
                        }
                    }
                }
            }
        }
        return tels.toString();
    }

    public String getTelefones(boolean todos) {
        StringBuilder tels = new StringBuilder("");
        if (getTelefoneVOs().size() > 0) {
            int qtdTelefones = getTelefoneVOs().size();
            for (int i = 0; i < qtdTelefones; i++) {
                Object obj = getTelefoneVOs().get(i);
                if (obj instanceof TelefoneVO) {
                    TelefoneVO telefone = (TelefoneVO) obj;
                    if (Uteis.validarTelefoneCelular(telefone.getNumero()) || todos) {
                        if (tels.toString().isEmpty()) {
                            tels.append(telefone.getNumero());
                        } else {
                            tels.append(";").append(telefone.getNumero());
                        }
                    }
                }
            }
        }
        return tels.toString();
    }

    public String getTodosEmails() {
        StringBuilder tels = new StringBuilder();
        if (getEmailVOs().size() > 0) {
            int qtdEmails = getEmailVOs().size();
            for (int i = 0; i < qtdEmails; i++) {
                Object obj = getEmailVOs().get(i);
                if (obj instanceof EmailVO) {
                    EmailVO emailV = (EmailVO) obj;
                    if (tels.toString().isEmpty()) {
                        tels.append(emailV.getEmail());
                    } else {
                        tels.append(";").append(emailV.getEmail());
                    }
                }
            }
        }
        return tels.toString();
    }

    public String getTelefonesConsulta(boolean todos) {
        StringBuilder tels = new StringBuilder("");
        if (getTelefoneVOs().size() > 0) {
            int qtdTelefones = getTelefoneVOs().size();
            for (int i = 0; i < qtdTelefones; i++) {
                Object obj = getTelefoneVOs().get(i);
                if (obj instanceof TelefoneVO) {
                    TelefoneVO telefone = (TelefoneVO) obj;
                    if (Uteis.validarTelefoneCelular(telefone.getNumero()) || todos) {
                        if (tels.toString().isEmpty()) {
                            tels.append(Uteis.tirarCaracteres(telefone.getNumero(), true));
                        } else {
                            tels.append(";").append(Uteis.tirarCaracteres(telefone.getNumero(), true));
                        }
                    }
                }
            }
        }
        return tels.toString();
    }

    /**
     * Retorna o objeto da classe <code>Profissao</code> relacionado com
     * (<code>Pessoa</code>).
     */
    public ProfissaoVO getProfissao() {
        if (profissao == null) {
            profissao = new ProfissaoVO();
        }
        return (profissao);
    }

    /**
     * Define o objeto da classe <code>Profissao</code> relacionado com
     * (<code>Pessoa</code>).
     */
    public void setProfissao(ProfissaoVO obj) {
        this.profissao = obj;
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe
     * <code>Telefone</code>.
     */
    public List<TelefoneVO> getTelefoneVOs() {
        if (telefoneVOs == null) {
            telefoneVOs = new ArrayList();
        }
        return (telefoneVOs);
    }

    /**
     * Define Atributo responsável por manter os objetos da classe
     * <code>Telefone</code>.
     */
    public void setTelefoneVOs(List telefoneVOs) {
        this.telefoneVOs = telefoneVOs;
        this.qtdeTelefones = telefoneVOs.size();
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe
     * <code>Endereco</code>.
     */
    public List<EnderecoVO> getEnderecoVOs() {
        if (enderecoVOs == null) {
            enderecoVOs = new ArrayList<>();
        }
        return (enderecoVOs);
    }

    public int getQtdeTelefones() {
        return qtdeTelefones;
    }

    public void setQtdeTelefones(int qtdeTelefones) {
        this.qtdeTelefones = qtdeTelefones;
    }

    /**
     * Define Atributo responsável por manter os objetos da classe
     * <code>Endereco</code>.
     */
    public void setEnderecoVOs(List enderecoVOs) {
        this.enderecoVOs = enderecoVOs;
    }



    /**
     * Operação responsável por retornar o valor de apresentação de um atributo
     * com um domínio específico. Com base no valor de armazenamento do atributo
     * esta função é capaz de retornar o de apresentação correspondente. Útil
     * para campos como sexo, escolaridade, etc.
     */

    public String getSexo() {
        if (sexo == null) {
            sexo = "";
        }
        return (sexo);
    }

    public String getSexo_Apresentar() {
        if (sexo == null) {
            return "";
        }
        if (sexo.equals("F")) {
            return "Feminino";
        }
        if (sexo.equals("M")) {
            return "Masculino";
        }
        return (sexo);
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getNaturalidade() {
        if (naturalidade == null) {
            naturalidade = "";
        }
        return (naturalidade);
    }

    public void setNaturalidade(String naturalidade) {
        this.naturalidade = naturalidade;
    }

    public String getNacionalidade() {
        if (nacionalidade == null) {
            nacionalidade = "";
        }
        return (nacionalidade);
    }

    public void setNacionalidade(String nacionalidade) {
        this.nacionalidade = nacionalidade;
    }

    public String getEstadoCivil() {
        if (estadoCivil == null) {
            estadoCivil = "";
        }
        return (estadoCivil);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo
     * com um domínio específico. Com base no valor de armazenamento do atributo
     * esta função é capaz de retornar o de apresentação correspondente. Útil
     * para campos como sexo, escolaridade, etc.
     */
    public String getEstadoCivil_Apresentar() {
        if (estadoCivil == null) {
            estadoCivil = "";
        } else if (estadoCivil.equals("S")) {
            return "Solteiro(a)";
        } else if (estadoCivil.equals("C")) {
            return "Casado(a)";
        } else if (estadoCivil.equals("A")) {
            return "Amasiado(a)";
        } else if (estadoCivil.equals("V")) {
            return "Viúvo(a)";
        } else if (estadoCivil.equals("D")) {
            return "Divorciado(a)";
        } else if (estadoCivil.equals("P")) {
            return "Separado(a)";
        } else if (estadoCivil.equals("U")) {
            return "União estável";
        }
        return (estadoCivil);
    }

    public void setEstadoCivil(String estadoCivil) {
        this.estadoCivil = estadoCivil;
    }

    public String getRgUf() {
        if (rgUf == null) {
            rgUf = "";
        }
        return (rgUf);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo
     * com um domínio específico. Com base no valor de armazenamento do atributo
     * esta função é capaz de retornar o de apresentação correspondente. Útil
     * para campos como sexo, escolaridade, etc.
     */
    public String getRgUf_Apresentar() {
        if (rgUf == null) {
            return "";
        }
        return rgUf;
    }

    public void setRgUf(String rgUf) {
        this.rgUf = rgUf;
    }

    public String getRgOrgao() {
        if (rgOrgao == null) {
            rgOrgao = "";
        }
        return (rgOrgao);
    }

    public void setRgOrgao(String rgOrgao) {
        this.rgOrgao = rgOrgao;
    }

    public String getRg() {
        if (rg == null) {
            rg = "";
        }
        return (rg);
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getCfp() {
        if (cfp == null) {
            cfp = "";
        }
        return (cfp);
    }

    public String getNomeResponsaveis() {
        if (UteisValidacao.emptyString(getNomeMae()) && UteisValidacao.emptyString(getNomePai())) {
            return "";
        } else {
            String retorno = "Resp.: ";
            if (!UteisValidacao.emptyString(getNomeMae())) {
                retorno += getNomeMae().toUpperCase();
            }
            if (!UteisValidacao.emptyString(getNomePai())) {
                if (!UteisValidacao.emptyString(getNomeMae())) {
                    retorno += ", " + getNomePai().toUpperCase();
                } else {
                    retorno += getNomePai().toUpperCase();
                }
            }
            return retorno;
        }
    }


    public void setCfp(String cfp) {
        this.cfp = cfp;
    }

    public String getNomeMae() {
        if (nomeMae == null) {
            nomeMae = "";
        }
        return (nomeMae);
    }

    public String getRne() {
        if (rne == null) {
            rne = "";
        }
        return rne;
    }

    public void setRne(String rne) {
        this.rne = rne;
    }

    public String getPassaporte() {
        if (passaporte == null) {
            passaporte = "";
        }
        return passaporte;
    }

    public void setPassaporte(String passaporte) {
        this.passaporte = passaporte;
    }

    public void setNomeMae(String nomeMae) {
        this.nomeMae = nomeMae;
    }

    public String getNomePai() {
        if (nomePai == null) {
            nomePai = "";
        }
        return (nomePai);
    }

    public void setNomePai(String nomePai) {
        this.nomePai = nomePai;
    }

    public Date getDataNasc() {
        return (dataNasc);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato
     * padrão dd/mm/aaaa.
     */
    public String getDataNasc_Apresentar() {
        return (Uteis.getData(dataNasc));
    }

    /**
     * retorna a data de nascimento parcial, somente o dia e o mês
     *
     * @return
     */
    public String getDataNasc_Parcial() {
        if (getDataNasc() != null) {
            return (Uteis.getData(getDataNasc()).substring(0, 5));
        } else {
            return "";
        }
    }

    public void setDataNasc(Date dataNasc) {
        this.dataNasc = dataNasc;
    }

    public Date getDataNascimentoResponsavel() {
        return dataNascimentoResponsavel;
    }

    public void setDataNascimentoResponsavel(Date dataNascimentoResponsavel) {
        this.dataNascimentoResponsavel = dataNascimentoResponsavel;
    }

    public String getPrimeiroNomeConcatenado() {
        if (nome == null) {
            nome = "";
        }
        return (Uteis.obterPrimeiroNomeConcatenadoSobreNome(nome));
    }
    
    public String getPrimeiroNomeConcatenadoMin() {
        return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(getNome()).toLowerCase();
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return (nome);
    }

    public String getNomeApresentarExtrato() {
        if (UteisValidacao.emptyString(getNome())) {
            return "";
        }
        return "<b>Aluno:</b> " + getNome() + "</br>";
    }

    public String getNomeComCodigo(){
        if ((this.codigo != null) && (this.codigo > 0)){
            return this.nome + " ("+ this.codigo + ")";
        }
        return this.nome;
    }

    public void setNomeComCodigo(String nomeComCodigo) {
        this.nomeComCodigo = nomeComCodigo;
    }

    public String getNomeMinusculo() {
        return getNome().toLowerCase();
    }
    public String getNomeAbreviadoMinusculo() {
        return Uteis.getNomeAbreviado(getNome().toLowerCase());
    }
    public String getNomeAbreviado() {
        return Uteis.getNomeAbreviado(getNome());
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDataCadastro() {
        if (dataCadastro == null) {
            dataCadastro = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return (dataCadastro);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato
     * padrão dd/mm/aaaa.
     */
    public String getDataCadastro_Apresentar() {
        return (Uteis.getData(dataCadastro));
    }

    public String getDataCadastroComHoraApresentar() {
        return Uteis.getDataComHHMMSS(dataCadastro);
    }

    public String getDataNasc_PadraoAmericano() {
        return (Uteis.getData(getDataNasc(),"yyyy-MM-dd"));
    }

    public String getDataCadastro_PadraoAmericano() {
        return (Uteis.getData(getDataCadastro(),"yyyy-MM-dd"));
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public GrauInstrucaoVO getGrauInstrucao() {
        if (grauInstrucao == null) {
            grauInstrucao = new GrauInstrucaoVO();
        }
        return grauInstrucao;
    }

    public void setGrauInstrucao(GrauInstrucaoVO grauInstrucao) {
        this.grauInstrucao = grauInstrucao;
    }

    public String getWebPage() {
        if (webPage == null) {
            webPage = "";
        }
        return webPage;
    }

    public void setWebPage(String webPage) {
        this.webPage = webPage;
    }

    public List<EmailVO> getEmailVOs() {
        if (emailVOs == null) {
            emailVOs = new ArrayList<>();
        }
        return emailVOs;
    }

    public List<String> getEmailsString() {
        List<String> emails = new ArrayList<String>();
        for (EmailVO emailvo : getEmailVOs()) {
            emails.add(emailvo.getEmail());
        }
        return emails;
    }

    public void setEmailVOs(List emailVOs) {
        this.emailVOs = emailVOs;
    }

    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getApresentarRichModalErro() {
        if (apresentarRichModalErro == null) {
            apresentarRichModalErro = new Boolean(false);
        }
        return apresentarRichModalErro;
    }

    public void setApresentarRichModalErro(Boolean apresentarRichModalErro) {
        this.apresentarRichModalErro = apresentarRichModalErro;
    }

    public EstadoVO getEstadoVO() {
        if (estadoVO == null) {
            estadoVO = new EstadoVO();
        }
        return estadoVO;
    }

    public void setEstadoVO(EstadoVO estadoVO) {
        this.estadoVO = estadoVO;
    }

    public Boolean getAdicionar() {
        return adicionar;
    }

    public void setAdicionar(Boolean adicionar) {
        this.adicionar = adicionar;
    }

    public String getNomeFoto() {
        if (nomeFoto == null) {
            nomeFoto = "";
        }
        return nomeFoto;
    }

    public void setNomeFoto(String nomeFoto) {
        this.nomeFoto = nomeFoto;
    }

    public Boolean getNovaFoto() {
        if (novaFoto == null) {
            novaFoto = new Boolean(false);
        }
        return novaFoto;
    }

    public void setNovaFoto(Boolean novaFoto) {
        this.novaFoto = novaFoto;
    }

    public byte[] getFoto() {
        return foto;
    }

    public void setFoto(byte[] foto) {
        this.foto = foto;
    }

    public Boolean getExisteFoto() {
        if (getFoto() != null) {
            return true;
        } else {
            return false;
        }
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * Método que recebe um objeto pessoaVO e preenche os dados de pessoaTO
     *
     * @param pessoaTO
     * <AUTHOR>
     */
    public void preencheComPessoaTO(PessoaTO pessoaTO) {
        this.nome = pessoaTO.getNomeCompleto();
        this.codigo = pessoaTO.getCodigo();
        //setar emails
        EmailVO emailVO = new EmailVO();
        emailVO.setEmail(pessoaTO.getEmail());
        emailVO.setPessoa(pessoaTO.getCodigo());
        this.email = emailVO.getEmail();
        this.getEmailVOs().add(emailVO);
        //emails
        //setar telefones
        List<TelefoneVO> tels = new ArrayList<TelefoneVO>();
        TelefoneVO residencial = new TelefoneVO();
        TelefoneVO celular = new TelefoneVO();
        TelefoneVO comercial = new TelefoneVO();
        residencial.setTipoTelefone("RE");
        residencial.setNumero(pessoaTO.getTelefoneFixo());
        tels.add(residencial);
        comercial.setTipoTelefone("CO");
        comercial.setNumero(pessoaTO.getTelefoneComercial());
        tels.add(comercial);
        celular.setTipoTelefone("CE");
        celular.setNumero(pessoaTO.getTelefoneCelular());
        tels.add(celular);
        //telefones
        telefoneVOs = tels;

    }

    /**
     * @return the eventos
     */
    public List getEventos() {
        return eventos;
    }

    /**
     * @param eventos the eventos to set
     */
    public void setEventos(List eventos) {
        this.eventos = eventos;
    }

    /**
     * @return the nomePerfilEvento
     */
    public List getNomePerfilEvento() {
        return nomePerfilEvento;
    }

    /**
     * @param nomePerfilEvento the nomePerfilEvento to set
     */
    public void setNomePerfilEvento(List nomePerfilEvento) {
        this.nomePerfilEvento = nomePerfilEvento;
    }

    @Override
    public String toString() {
        return this.nome;
    }

    public String getSenhaAcesso() {
        return senhaAcesso;
    }

    public void setSenhaAcesso(String senhaAcesso) {
        this.senhaAcesso = senhaAcesso;
    }

    public String getConfirmarSenhaAcesso() {
        return confirmarSenhaAcesso;
    }

    public void setConfirmarSenhaAcesso(String confirmarSenhaAcesso) {
        this.confirmarSenhaAcesso = confirmarSenhaAcesso;
    }

    public void validarSenhaAcesso(boolean permiteOnzeDigitos) throws Exception {
        ClienteVO clienteVo = getFacade().getCliente().consultarPorCodigoPessoa(getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        validarSenhaAcesso(permiteOnzeDigitos, clienteVo);
    }

    public void validarSenhaAcesso(boolean permiteOnzeDigitos, ClienteVO clienteVO) throws Exception {
        // Verifica se as duas senhas são iguais.
        if (!getConfirmarSenhaAcesso().equals(getSenhaAcesso())) {
            throw new Exception("As senhas não conferem. Digite a senha novamente.");
        }
        //Verifica se a senha tem exatamente 6 dígitos
        if ((permiteOnzeDigitos ? getSenhaAcesso().length() != 11 : getSenhaAcesso().length() != 5)) {
            throw new Exception("A senha deve ter exatamente " + (permiteOnzeDigitos ? 11 : 5) + " dígitos. Digite a senha novamente.");
        }
        // A senha deverá ser composta de somente números
        if (!UteisValidacao.somenteNumeros(getSenhaAcesso())) {
            throw new Exception("A senha deve ter somente números. Digite a senha novamente.");
        }
        if (getSenhaAcesso().charAt(0) == getSenhaAcesso().charAt(1)
                && getSenhaAcesso().charAt(1) == getSenhaAcesso().charAt(2)
                && getSenhaAcesso().charAt(2) == getSenhaAcesso().charAt(3)
                && getSenhaAcesso().charAt(3) == getSenhaAcesso().charAt(4)) {
            throw new Exception("A senha não pode ter todos os digitos iguais! Digite a senha novamente.");
        }
        //Se a alteração da senha for para um cliente, a senha não pode ser o mesmo número de matrícula.
        if ((clienteVO != null) && (clienteVO.getCodigoMatricula().toString().equals(getSenhaAcesso()))) {
            throw new Exception("A senha não pode ser igual ao número de matrícula. Digite a senha novamente.");
        }
    }

    /**
     * @return the tipoPessoa
     */
    public String getTipoPessoa() {
        if (tipoPessoa == null) {
            tipoPessoa = "";
        }
        return tipoPessoa;
    }

    /**
     * @param tipoPessoa the tipoPessoa to set
     */
    public void setTipoPessoa(String tipoPessoa) {
        this.tipoPessoa = tipoPessoa;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public boolean getPossuiFoto() {
        return getFoto() != null && getFoto().length > 0;
    }

    public boolean getPossuiFotoNuvem() {
        return getFotoKey() != null && !getFotoKey().isEmpty();
    }

    public JSONObject toJSON() {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        try {
            o.put(COL_CODIGO, getCodigo());
            o.put(COL_SENHAACESSO, getSenhaAcesso());
            o.put(COL_NOME, getNome());

            JSONArray jsonTel = new JSONArray();
            getTelefoneVOs().forEach(it -> jsonTel.put(it.toJSON()));
            o.put(COL_TELEFONES, jsonTel.toString());

            JSONArray jsonEm = new JSONArray();
            getEmailVOs().forEach(it -> jsonEm.put(it.toJSON()));
            o.put(COL_EMAILS, jsonEm.toString());

            o.put(COL_DATANASCIMENTO, getDataNasc_Apresentar());
            o.put("fotoKey", getFotoKey());
            o.put("cpf", getCfp());
            o.put("assinaturaBiometriaDigital", getAssinaturaBiometriaDigital());
            o.put("assinaturaBiometriaFacial", getAssinaturaBiometriaFacial());
        } catch (Exception e) {
        }
        return o;
    }

    public Boolean getLiberaSenhaAcesso() {
        return liberaSenhaAcesso;
    }

    public void setLiberaSenhaAcesso(Boolean liberaSenhaAcesso) {
        this.liberaSenhaAcesso = liberaSenhaAcesso;
    }

    public String getCidade_Apresentar() {
        return getCidade().getNome();
    }

    public String getEmails() {
        return emails;
    }

    public void setEmails(String emails) {
        this.emails = emails;
    }

    public void setNrTelefones(String tel) {
        telefones = tel;
    }

    public String getNrTelefones() {
        return telefones;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getNrTelefonesApresentar() {
        return telefones == null || telefones.isEmpty()
                ? "" : telefones.replaceAll("CE", "")
                .replaceAll("RE", "")
                .replaceAll("CO", "")
                .replaceAll("-", "")
                .replaceAll("\\{", "")
                .replaceAll("\\}", "")
                .replaceAll(",", ";");
    }

    public String getUrlFoto() {
        try {
            String uri = Uteis.getPaintFotoDaNuvem(getFotoKey());
            return uri;
        } catch (Exception e) {
            return "";
        }
    }

    public String getUrlFotoNoCache() {
        try {
            String uri = Uteis.getPaintFotoDaNuvem(getFotoKey());
            if (!UteisValidacao.emptyString(uri) && !uri.contains("fotoPadrao"))
                return uri + (uri.contains("?") ? "&time=" : "?time=") + new Date().getTime();
            else return uri;
        } catch (Exception e) {
            return "";
        }
    }

    public String getUrlFotoContexto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }
    public String getListaTelefones_Apresentar() {
        String listaTelefones = "";
        Iterator i = getTelefoneVOs().iterator();
        while (i.hasNext()) {
            TelefoneVO telefone = (TelefoneVO) i.next();
            listaTelefones += telefone.getTipoTelefone_Apresentar() + ": " + telefone.getNumero() + " ";
        }
        return listaTelefones;
    }

    public List<PlacaVO> getPlacaVOs() {
        return placaVOs;
    }

    public void setPlacaVOs(List<PlacaVO> placaVOs) {
        this.placaVOs = placaVOs;
    }

    public String getIdadePessoa() {
        Integer idade = Uteis.calcularIdadePessoa(Calendario.hoje(), this.getDataNasc());
        if (idade.toString().length() == 1) {
            return "0" + idade;
        } else {
            return idade.toString();
        }
    }

    public Integer getIdade() {
        return Uteis.calcularIdadePessoa(Calendario.hoje(), this.getDataNasc());
    }

    public TipoPessoa getCategoriaPessoa() {
        if (categoriaPessoa == null) {
            categoriaPessoa = TipoPessoa.FISICA;
        }
        return categoriaPessoa;
    }

    public String getEmailCorrespondencia(){
        String emailRetornar= null;
        for (EmailVO emailVO: emailVOs){
            if (emailVO.emailCorrespondencia)
                emailRetornar = emailVO.getEmail();
        }
        if ((emailRetornar == null) && (emailVOs.size() > 0))
            emailRetornar = emailVOs.get(0).getEmail();
        return emailRetornar;
    }

    public String getNumeroTelefonesApresentar() {
        return numeroTelefonesApresentar;
    }

    public void setNumeroTelefonesApresentar(String numeroTelefonesApresentar) {
        this.numeroTelefonesApresentar = numeroTelefonesApresentar;
    }

    public void setCategoriaPessoa(TipoPessoa categoriaPessoa) {
        this.categoriaPessoa = categoriaPessoa;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getInscEstadual() {
        return inscEstadual;
    }

    public void setInscEstadual(String inscEstadual) {
        this.inscEstadual = inscEstadual;
    }
    public Boolean getPessoaJuridica(){
        return TipoPessoa.JURIDICA.equals(getCategoriaPessoa());
    }
    public Boolean getPessoaFisica(){
        return TipoPessoa.FISICA.equals(getCategoriaPessoa());
    }

    public String getCfdf() {
        return cfdf;
    }

    public void setCfdf(String cfdf) {
        this.cfdf = cfdf;
    }

    public String getInscMunicipal() {
        if (inscMunicipal == null) {
            inscMunicipal = "";
        }
        return inscMunicipal;
    }

    public void setInscMunicipal(String inscMunicipal) {
        this.inscMunicipal = inscMunicipal;
    }

    public boolean isEmitirNotaNomeMae() {
        return emitirNotaNomeMae;
    }

    public void setEmitirNotaNomeMae(boolean emitirNotaNomeMae) {
        this.emitirNotaNomeMae = emitirNotaNomeMae;
    }

    public Integer getIdVindi() {
        return idVindi;
    }

    public void setIdVindi(Integer idVindi) {
        this.idVindi = idVindi;
    }

    public Date getDataAlteracaoVindi() {
        return dataAlteracaoVindi;
    }

    public void setDataAlteracaoVindi(Date dataAlteracaoVindi) {
        this.dataAlteracaoVindi = dataAlteracaoVindi;
    }

    public Date getDataAlteracaoMaxiPago() {
        return dataAlteracaoMaxiPago;
    }

    public void setDataAlteracaoMaxiPago(Date dataAlteracaoMaxiPago) {
        this.dataAlteracaoMaxiPago = dataAlteracaoMaxiPago;
    }

    public String getIdMaxiPago() {
        return idMaxiPago;
    }

    public void setIdMaxiPago(String idMaxiPago) {
        this.idMaxiPago = idMaxiPago;
    }

    public boolean isEmitirNomeTerceiro() {
        return emitirNomeTerceiro;
    }

    public void setEmitirNomeTerceiro(boolean emitirNomeTerceiro) {
        this.emitirNomeTerceiro = emitirNomeTerceiro;
    }

    public boolean isEmitirNotaNomeAluno() {
        return emitirNotaNomeAluno;
    }

    public void setEmitirNotaNomeAluno(boolean emitirNotaNomeAluno) {
        this.emitirNotaNomeAluno = emitirNotaNomeAluno;
    }

    public String getNomeTerceiro() {
        if (nomeTerceiro == null) {
            nomeTerceiro = "";
        }
        return nomeTerceiro;
    }

    public void setNomeTerceiro(String nomeTerceiro) {
        this.nomeTerceiro = nomeTerceiro;
    }

    public String getCpfCNPJTerceiro() {
        if (cpfCNPJTerceiro == null) {
            cpfCNPJTerceiro = "";
        }
        return cpfCNPJTerceiro;
    }

    public void setCpfCNPJTerceiro(String cpfCNPJTerceiro) {
        this.cpfCNPJTerceiro = cpfCNPJTerceiro;
    }

    public String getObservacaoNota() {
        if (observacaoNota == null) {
            observacaoNota = "";
        }
        return observacaoNota;
    }

    public void setObservacaoNota(String observacaoNota) {
        this.observacaoNota = observacaoNota;
    }

    public Long getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Long idExterno) {
        this.idExterno = idExterno;
    }

    public String getIdGetNet() {
        if (idGetNet == null) {
            idGetNet = "";
        }
        return idGetNet;
    }

    public void setIdGetNet(String idGetNet) {
        this.idGetNet = idGetNet;
    }

    public Date getDataAlteracaoGetNet() {
        return dataAlteracaoGetNet;
    }

    public void setDataAlteracaoGetNet(Date dataAlteracaoGetNet) {
        this.dataAlteracaoGetNet = dataAlteracaoGetNet;
    }

    public String getAssinaturaBiometriaDigital() {
        //atributo deve ser preenchido onde for usado devido o tamanho
        // by Luiz Felipe
        if(assinaturaBiometriaDigital == null){
            assinaturaBiometriaDigital = "";
        }
        return assinaturaBiometriaDigital;
    }

    public void setAssinaturaBiometriaDigital(String assinaturaBiometriaDigital) {
        this.assinaturaBiometriaDigital = assinaturaBiometriaDigital;
    }

    public String getAssinaturaBiometriaFacial() {
        //atributo deve ser preenchido onde for usado devido o tamanho
        // by Luiz Felipe
        if (assinaturaBiometriaFacial == null) {
            assinaturaBiometriaFacial = "";
        }
        return assinaturaBiometriaFacial;
    }

    public void setAssinaturaBiometriaFacial(String assinaturaBiometriaFacial) {
        this.assinaturaBiometriaFacial = assinaturaBiometriaFacial;
    }

    public String emailVOsToString(){
        String emails = "";
        for (EmailVO email: getEmailVOs()) {
            emails += email.getEmail()+",";
        }
        return Uteis.removerUltimoCaractere(emails);
    }

    public String[] emailVOsToArrayString(){
        String[] emails = {};
        for (EmailVO email: getEmailVOs()) {
            ArrayUtils.add(emails, email.getEmail());
        }

        return emails;
    }

    public String telefoneVOsToString() {
        String telefones = "";
        for (TelefoneVO telefone: getTelefoneVOs()) {
            telefones += telefone.getNumero()+",";
        }
        return Uteis.removerUltimoCaractere(telefones);
    }

    public boolean isUtilizaDotz() {
        return utilizaDotz;
    }

    public void setUtilizaDotz(boolean utilizaDotz) {
        this.utilizaDotz = utilizaDotz;
    }

    public String getContatoEmergencia() {
        return contatoEmergencia;
    }

    public void setContatoEmergencia(String contatoEmergencia) {
        this.contatoEmergencia = contatoEmergencia;
    }

    public String getTelefoneEmergencia() {
        return telefoneEmergencia;
    }

    public void setTelefoneEmergencia(String telefoneEmergencia) {
        this.telefoneEmergencia = telefoneEmergencia;
    }
    public String getCpfPai() {
        if (cpfPai == null) {
            cpfPai = "";
        }
        return cpfPai;
    }

    public void setCpfPai(String cpfPai) {
        this.cpfPai = cpfPai;
    }

    public String getCpfMae() {
        if (cpfMae == null) {
            cpfMae = "";
        }
        return cpfMae;
    }

    public void setCpfMae(String cpfMae) {
        this.cpfMae = cpfMae;
    }

    public String getInscEstadualTerceiro() {
        if (inscEstadualTerceiro == null) {
            inscEstadualTerceiro = "";
        }
        return inscEstadualTerceiro;
    }

    public void setInscEstadualTerceiro(String inscEstadualTerceiro) {
        this.inscEstadualTerceiro = inscEstadualTerceiro;
    }

    public String getCfdfTerceiro() {
        if (cfdfTerceiro == null) {
            cfdfTerceiro = "";
        }
        return cfdfTerceiro;
    }

    public void setCfdfTerceiro(String cfdfTerceiro) {
        this.cfdfTerceiro = cfdfTerceiro;
    }

    public String getSobreNome() {
        if (sobreNome == null) {
            sobreNome = "";
        }
        return sobreNome;
    }

    public void setSobreNome(String sobreNome) {
        this.sobreNome = sobreNome;
    }

    public boolean isAtualizarDados() {
        return atualizarDados;
    }

    public void setAtualizarDados(boolean atualizarDados) {
        this.atualizarDados = atualizarDados;
    }

    public CargoEnum getCargoEnum() {
        return cargoEnum;
    }

    public void setCargoEnum(CargoEnum cargoEnum) {
        this.cargoEnum = cargoEnum;
    }

    public FuncaoEnum getFuncaoEnum() {
        return funcaoEnum;
    }

    public void setFuncaoEnum(FuncaoEnum funcaoEnum) {
        this.funcaoEnum = funcaoEnum;
    }

    public boolean isAceiteTermosPacto() {
        return aceiteTermosPacto;
    }

    public void setAceiteTermosPacto(boolean aceiteTermosPacto) {
        this.aceiteTermosPacto = aceiteTermosPacto;
    }

    public String getIpAceiteTermosPacto() {
        return ipAceiteTermosPacto;
    }

    public void setIpAceiteTermosPacto(String ipAceiteTermosPacto) {
        this.ipAceiteTermosPacto = ipAceiteTermosPacto;
    }

    public String getInfoBrowserTermosPacto() {
        return infoBrowserTermosPacto;
    }

    public void setInfoBrowserTermosPacto(String infoBrowserTermosPacto) {
        this.infoBrowserTermosPacto = infoBrowserTermosPacto;
    }

    public Date getDataAceiteTermosPacto() {
        return dataAceiteTermosPacto;
    }

    public void setDataAceiteTermosPacto(Date dataAceiteTermosPacto) {
        this.dataAceiteTermosPacto = dataAceiteTermosPacto;
    }

    public boolean isReceberSMSPacto() {
        return receberSMSPacto;
    }

    public void setReceberSMSPacto(boolean receberSMSPacto) {
        this.receberSMSPacto = receberSMSPacto;
    }

    public boolean isReceberEmailNovidadesPacto() {
        return receberEmailNovidadesPacto;
    }

    public void setReceberEmailNovidadesPacto(boolean receberEmailNovidadesPacto) {
        this.receberEmailNovidadesPacto = receberEmailNovidadesPacto;
    }

    public boolean isAssinaturaBiometriaDigitalB() {
        return assinaturaBiometriaDigitalB;
    }

    public void setAssinaturaBiometriaDigitalB(boolean assinaturaBiometriaDigitalB) {
        this.assinaturaBiometriaDigitalB = assinaturaBiometriaDigitalB;
    }

    public boolean isAssinaturaBiometriaFacialB() {
        return assinaturaBiometriaFacialB;
    }

    public void setAssinaturaBiometriaFacialB(boolean assinaturaBiometriaFacialB) {
        this.assinaturaBiometriaFacialB = assinaturaBiometriaFacialB;
    }

    public Boolean getFormularioCliente() {
        return formularioCliente;
    }

    public void setFormularioCliente(Boolean formularioCliente) {
        this.formularioCliente = formularioCliente;
    }

    public Integer getSpiviClientID() {
        return spiviClientID;
    }

    public void setSpiviClientID(Integer spiviClientID) {
        this.spiviClientID = spiviClientID;
    }

    public String getRgMae() {
        if (rgMae == null) {
            rgMae = "";
        }
        return rgMae;
    }

    public void setRgMae(String rgMae) {
        this.rgMae = rgMae;
    }

    public String getRgPai() {
        if (rgPai == null) {
            rgPai = "";
        }
        return rgPai;
    }

    public void setRgPai(String rgPai) {
        this.rgPai = rgPai;
    }

    public String getIdMundiPagg() {
        if (idMundiPagg == null) {
            idMundiPagg = "";
        }
        return idMundiPagg;
    }

    public void setIdMundiPagg(String idMundiPagg) {
        this.idMundiPagg = idMundiPagg;
    }

    public Date getDataAlteracaoMundiPagg() {
        return dataAlteracaoMundiPagg;
    }

    public void setDataAlteracaoMundiPagg(Date dataAlteracaoMundiPagg) {
        this.dataAlteracaoMundiPagg = dataAlteracaoMundiPagg;
    }

    public String getIdPagarMe() {
        if (idPagarMe == null) {
            idPagarMe = "";
        }
        return idPagarMe;
    }

    public void setIdPagarMe(String idPagarMe) {
        this.idPagarMe = idPagarMe;
    }

    public Date getDataAlteracaoPagarMe() {
        return dataAlteracaoPagarMe;
    }

    public void setDataAlteracaoPagarMe(Date dataAlteracaoPagarMe) {
        this.dataAlteracaoPagarMe = dataAlteracaoPagarMe;
    }

    //INICIO BLOQUEIO DE PARCELAS
    public TipoBloqueioCobrancaEnum getTipoBloqueioCobrancaAutomatica() {
        return tipoBloqueioCobrancaAutomatica;
    }

    public void setTipoBloqueioCobrancaAutomatica(TipoBloqueioCobrancaEnum tipoBloqueioCobrancaAutomatica) {
        this.tipoBloqueioCobrancaAutomatica = tipoBloqueioCobrancaAutomatica;
    }

    public Date getDataBloqueioCobrancaAutomatica() {
        return dataBloqueioCobrancaAutomatica;
    }

    public void setDataBloqueioCobrancaAutomatica(Date dataBloqueioCobrancaAutomatica) {
        this.dataBloqueioCobrancaAutomatica = dataBloqueioCobrancaAutomatica;
    }

    public String getDataBloqueioCobrancaAutomaticaApresentar() {
        if (getDataBloqueioCobrancaAutomatica() != null) {
            return Calendario.getDataAplicandoFormatacao(getDataBloqueioCobrancaAutomatica(), "dd/MM/yyyy");
        } else {
            return "";
        }
    }
    //FIM BLOQUEIO DE PARCELAS

    public JSONObject toJSONComCPF() {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        try {
            o.put(COL_CODIGO, getCodigo());
            o.put(COL_SENHAACESSO, getSenhaAcesso());
            o.put(COL_NOME, getNome());
            JSONArray jsonTel = new JSONArray();
            for (TelefoneVO tel : getTelefoneVOs()) {
                jsonTel.put(tel.toJSON());
            }
            o.put(COL_TELEFONES, jsonTel.toString());
            JSONArray jsonEm = new JSONArray();
            for (EmailVO obj : getEmailVOs()) {
                jsonEm.put(obj.toJSON());
            }
            o.put(COL_EMAILS, jsonEm.toString());
            o.put("email",getEmail());
            o.put(COL_DATANASCIMENTO, getDataNasc_Apresentar());
            o.put("cpf",getCfp());
            o.put("datacadastro", getDataCadastro_Apresentar());
        } catch (Exception e) {
        }
        return o;
    }

    public Double getValorLimiteCaixaAbertoVendaAvulsa() {
        if (valorLimiteCaixaAbertoVendaAvulsa == null) {
            valorLimiteCaixaAbertoVendaAvulsa = 0.0;
        }
        return valorLimiteCaixaAbertoVendaAvulsa;
    }

    public void setValorLimiteCaixaAbertoVendaAvulsa(Double valorLimiteCaixaAbertoVendaAvulsa) {
        this.valorLimiteCaixaAbertoVendaAvulsa = valorLimiteCaixaAbertoVendaAvulsa;
    }

    public ClienteDTO toClienteDTO(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) {
        ClienteDTO clienteDTO = new ClienteDTO();
        clienteDTO.setIdReferencia(this.getCodigo());
        clienteDTO.setNome(this.getNome());
        clienteDTO.setDataNascimento(this.getDataNasc());

        if (!UteisValidacao.emptyString(this.getCfp())) {
            clienteDTO.setDocumento(this.getCfp());
            clienteDTO.setTipoDocumento(TipoDocumentoEnum.CPF.name());
        } else if (!UteisValidacao.emptyString(this.getCnpj())) {
            clienteDTO.setDocumento(this.getCnpj());
            clienteDTO.setTipoDocumento(TipoDocumentoEnum.CNPJ.name());
        }

        if (tipoConvenioCobrancaEnum != null &&
                !tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.NENHUM)) {

           if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
               clienteDTO.setIdAdquirente(this.getIdVindi().toString());
           } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
               clienteDTO.setIdAdquirente(this.getIdGetNet());
           } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
               clienteDTO.setIdAdquirente(this.getIdPagarMe());
           } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
               clienteDTO.setIdAdquirente(this.getIdStone());
           } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG)) {
               clienteDTO.setIdAdquirente(this.getIdMundiPagg());
           } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO)) {
               clienteDTO.setIdAdquirente(this.getIdMaxiPago());
           }

           if (UteisValidacao.emptyString(clienteDTO.getIdAdquirente())) {
               clienteDTO.setIdAdquirente(this.getCodigo().toString());
           }
        }

        //email
        if (!UteisValidacao.emptyList(this.getEmailVOs())) {
            EmailVO emailVO = this.getEmailVOs().get(0);
            clienteDTO.setEmail(emailVO.getEmail());
        }
        if (UteisValidacao.emptyString(clienteDTO.getEmail()) &&
                !UteisValidacao.emptyString(this.getEmail())) {
            clienteDTO.setEmail(this.getEmail());
        }

        //telefone
        for (TelefoneVO telefoneVO : this.getTelefoneVOs()) {
            if (telefoneVO.getTipoTelefone().equalsIgnoreCase(TipoTelefoneEnum.CELULAR.getCodigo())) {
                clienteDTO.setCelular(telefoneVO.getNumero());
            }
            if (!telefoneVO.getTipoTelefone().equalsIgnoreCase(TipoTelefoneEnum.CELULAR.getCodigo())) {
                clienteDTO.setTelefone(telefoneVO.getNumero());
            }
        }

        //endereço
        EnderecoDTO enderecoDTO = new EnderecoDTO();
        enderecoDTO.setUf(this.getEstadoVO().getSigla());
        enderecoDTO.setCidade(this.getCidade().getNome());
        enderecoDTO.setPais(this.getPais().getNome());
        if (!UteisValidacao.emptyList(this.getEnderecoVOs())) {
            EnderecoVO enderecoVO  = this.getEnderecoVOs().get(0);
            enderecoDTO.setBairro(enderecoVO.getBairro());
            enderecoDTO.setNumero(enderecoVO.getNumero());
            enderecoDTO.setComplemento(enderecoVO.getComplemento());
            enderecoDTO.setEndereco(enderecoVO.getEndereco());
            enderecoDTO.setCep(enderecoVO.getCep());
        }
        clienteDTO.setEndereco(enderecoDTO);

        return clienteDTO;
    }

    public String getIdExternoIntegracao() {
        return idExternoIntegracao;
    }

    public void setIdExternoIntegracao(String idExternoIntegracao) {
        this.idExternoIntegracao = idExternoIntegracao;
    }

    public String getIdentificadorEntidade() {
        return identificadorEntidade;
    }

    public void setIdentificadorEntidade(String identificadorEntidade) {
        this.identificadorEntidade = identificadorEntidade;
    }

    public Date getLancamentoCartaoVacina() {
        return lancamentoCartaoVacina;
    }

    public void setLancamentoCartaoVacina(Date lancamentoCartaoVacina) {
        this.lancamentoCartaoVacina = lancamentoCartaoVacina;
    }

    public String getCustomerIdPagoLivre() {
        return customerIdPagoLivre;
    }

    public void setCustomerIdPagoLivre(String customerIdPagoLivre) {
        this.customerIdPagoLivre = customerIdPagoLivre;
    }

    public Date getDataAlteracaoPagoLivre() {
        return dataAlteracaoPagoLivre;
    }

    public void setDataAlteracaoPagoLivre(Date dataAlteracaoPagoLivre) {
        this.dataAlteracaoPagoLivre = dataAlteracaoPagoLivre;
    }

    public String getGenero() {
        if (genero == null) {
            genero = "";
        }
        return genero;
    }

    public void setGenero(String genero) {
        this.genero = genero;
    }

    public String getNomeRegistro() {
        if (nomeRegistro == null){
            nomeRegistro = "";
        }
        return nomeRegistro;
    }

    public void setNomeRegistro(String nomeRegistro) {
        this.nomeRegistro = nomeRegistro;
    }

    public String getGenero_Apresentar() {
        if (genero == null) {
            return "";
        }
        if (genero.equals("FC")) {
            return "Feminino cisgênero";
        }
        if (genero.equals("MC")) {
            return "Masculino cisgênero";
        }
        if (genero.equals("FT")) {
            return "Feminino transgênero";
        }
        if (genero.equals("MT")) {
            return "Masculino transgênero";
        }
        if (genero.equals("AG")) {
            return "Agênero";
        }
        if (genero.equals("NB")) {
            return "Não-binário";
        }
        return (genero);
    }

    public String getIdAsaas() {
        if (UteisValidacao.emptyString(idAsaas)) {
            return "";
        }
        return idAsaas;
    }

    public void setIdAsaas(String idAsaas) {
        this.idAsaas = idAsaas;
    }

    public boolean isConstaRestricaoSPC() {
        return constaRestricaoSPC;
    }

    public void setConstaRestricaoSPC(boolean constaRestricaoSPC) {
        this.constaRestricaoSPC = constaRestricaoSPC;
    }

    public String getEmailPai() {
        if (emailPai == null) {
            emailPai = "";
        }
        return emailPai;
    }

    public void setEmailPai(String emailPai) {
        this.emailPai = emailPai;
    }

    public String getEmailMae() {
        if (emailMae == null) {
            emailMae = "";
        }
        return emailMae;
    }

    public void setEmailMae(String emailMae) {
        this.emailMae = emailMae;
    }

    public boolean isMenorIdade(){
        return this.getDataNasc() != null && this.getIdade() < 18;
    }

    public Date getDataAlteracaoIdAsaas() {
        return dataAlteracaoIdAsaas;
    }

    public void setDataAlteracaoIdAsaas(Date dataAlteracaoIdAsaas) {
        this.dataAlteracaoIdAsaas = dataAlteracaoIdAsaas;
    }

    public String getDataAlteracaoIdAsaasApresentar() {
        if (getDataAlteracaoIdAsaas() == null) {
            return "";
        }
        return Uteis.getDataAplicandoFormatacao(getDataAlteracaoIdAsaas(), "dd/MM/yyyy");
    }

    public String getCnpjSesi() {
        return cnpjSesi;
    }

    public void setCnpjSesi(String cnpjSesi) {
        this.cnpjSesi = cnpjSesi;
    }

    public String getNomeResponsavelEmpresa() {
        return nomeResponsavelEmpresa;
    }

    public void setNomeResponsavelEmpresa(String nomeResponsavelEmpresa) {
        this.nomeResponsavelEmpresa = nomeResponsavelEmpresa;
    }

    public String getCpfResponsavelEmpresa() {
        return cpfResponsavelEmpresa;
    }

    public void setCpfResponsavelEmpresa(String cpfResponsavelEmpresa) {
        this.cpfResponsavelEmpresa = cpfResponsavelEmpresa;
    }

    public String getNomeEmpresa() {
        if (UteisValidacao.emptyString(nomeEmpresa)) {
            return "";
        }
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getNomeRespFinanceiro() {
        return nomeRespFinanceiro;
    }

    public void setNomeRespFinanceiro(String nomeRespFinanceiro) {
        this.nomeRespFinanceiro = nomeRespFinanceiro;
    }

    public String getEmailRespFinanceiro() {
        return emailRespFinanceiro;
    }

    public void setEmailRespFinanceiro(String emailRespFinanceiro) {
        this.emailRespFinanceiro = emailRespFinanceiro;
    }

    public String getCpfRespFinanceiro() {
        return cpfRespFinanceiro;
    }

    public void setCpfRespFinanceiro(String cpfRespFinanceiro) {
        this.cpfRespFinanceiro = cpfRespFinanceiro;
    }

    public String getRgRespFinanceiro() {
        return rgRespFinanceiro;
    }

    public void setRgRespFinanceiro(String rgRespFinanceiro) {
        this.rgRespFinanceiro = rgRespFinanceiro;
    }

    public String getIdStone() {
        if (UteisValidacao.emptyString(idStone)) {
            return "";
        }
        return idStone;
    }

    public void setIdStone(String idStone) {
        this.idStone = idStone;
    }

    public Date getDataAlteracaoStone() {
        return dataAlteracaoStone;
    }

    public void setDataAlteracaoStone(Date dataAlteracaoStone) {
        this.dataAlteracaoStone = dataAlteracaoStone;
    }
}


package negocio.comuns.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;

/**
 * Reponsável por manter os dados da entidade MovimentoContaCorrenteCliente. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class MovimentoContaCorrenteClienteVO extends SuperVO {

    public static final String DESCRICAO_PAGAMENTO_SALDO_DEVEDOR = "Pagto. saldo devedor C/C";
    public static final String DESCRICAO_SAQUE_SALDO_CREDOR = "Saque de C/C credora";
    public static final String DESCRICAO_MANUTENCAO_CONTA_CORRENTE = "Manut. C/C";
    public static final String DESCRICAO_DEPOSITO_CONTA_ALUNO = "DEPÓSITO CONTA CORRENTE ALUNO ";
    public static final String DESCRICAO_DEPOSITO_CONTA_ALUNO_BOLETO_EXCEDENTE = "DEPÓSITO REFERENTE A PAGAMENTO EXCEDENTE DE BOLETO BANCÁRIO";
    public static final String DESCRICAO_DEPOSITO_CONTA_ALUNO_BOLETO = "DEPÓSITO REFERENTE A PAGAMENTO DE BOLETO BANCÁRIO";
    public static final String DESCRICAO_DEPOSITO_CONTA_ALUNO_BOLETO_PARCELA_NAO_ABERTO = "DEPÓSITO REFERENTE A PAGAMENTO DE BOLETO BANCÁRIO - PARCELA NÃO ESTÁ EM ABERTO";
    public static final String DESCRICAO_DEPOSITO_CONTA_ALUNO_PARCELA_NAO_ABERTO = "DEPÓSITO REFERENTE A PAGAMENTO DE PARCELA QUE NÃO ESTÁ MAIS EM ABERTO";
    public static final String DESCRICAO_DEPOSITO_CONTA_ALUNO_PIX = "DEPÓSITO REF. A PAGAMENTO DE PIX DE PARCELA QUE NÃO ESTÁ MAIS EM ABERTO";
    public static final String DESCRICAO_DEPOSITO_CONTA_ALUNO_PIX_EXCEDENTE = "DEPÓSITO REFERENTE A PAGAMENTO EXCEDENTE DE PIX";
    public static final String DESCRICAO_ACERTO_CONTA_ALUNO = "ACERTO CONTA CORRENTE ALUNO ";
    protected Integer codigo;
    protected Double saldoAtual;
    private String saldoAtualFormatado;
    protected Double valor;
    private String valorFormatado;
    protected Double saldoAnterior;
    protected String descricao;
    protected String tipoMovimentacao;
    protected Date dataRegistro;
    private List<MovPagamentoVO> movPagamentosVOs;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Cliente </code>.*/
    protected PessoaVO Pessoa;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    // protected ColaboradorVO responsavelRegistro;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    protected UsuarioVO responsavelAutorizacao;
    protected ReciboPagamentoVO reciboPagamentoVO;
    protected ContratoOperacaoVO contratoOperacaoVO;
    private boolean apresentarPanelSaldoContaCorrente = false;
    private String pagamentosDebito = "";
    private ClienteVO clienteTransferencia;
    private Double valorTransferencia;
    private Double valorParcelaDebito;
    private String dataHoraRegistro;
    private List<MovPagamentoVO> movPagamentosVOsTemporario;

    /**
     * Construtor padrão da classe <code>MovimentoContaCorrenteCliente</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public MovimentoContaCorrenteClienteVO() {
        super();
        inicializarDados();
    }

    public String getPessoa_Apresentar() {
        return getPessoa().getNome();
    }

    public String getResponsavel_Apresentar() {
        return getUsuarioVO().getUsername();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>MovimentoContaCorrenteClienteVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(MovimentoContaCorrenteClienteVO obj, String operacao) throws ConsistirException {
        if (operacao.equals("transferencia")) {
            if (obj.getClienteTransferencia() == null || obj.getClienteTransferencia().getPessoa().getCodigo() == 0) {
                throw new ConsistirException("O campo Cliente deve ser informado.");
            }
            if (obj.valorTransferencia <= 0) {
                throw new ConsistirException("O campo Valor da Transferência  deve ser maior que zero.");
            }
            if (obj.valorTransferencia > Uteis.arredondarForcando2CasasDecimais(obj.getSaldoAtual())) {
                throw new ConsistirException("O campo Valor da Transferência deve ser menor ou igual ao saldo atual do aluno.");
            }
        } else if (operacao.equals("gerarParcela")) {
            if (obj.valorParcelaDebito <= 0) {
                throw new ConsistirException("O campo Valor a Pagar deve ser maior que zero.");
            }
            if (Uteis.arredondarForcando2CasasDecimais(obj.valorParcelaDebito) > Uteis.arredondarForcando2CasasDecimais((obj.getSaldoAtual() * (-1)))) {
                throw new ConsistirException("O campo Valor a Pagar deve ser menor ou igual ao valor absoluto do débito atual do aluno.");
            }
        } else {
            if (!obj.getValidarDados().booleanValue()) {
                return;
            }
            if ((obj.getPessoa() == null) || (obj.getPessoa().getCodigo().intValue() == 0)) {
                throw new ConsistirException("O campo NOME (Movimento de Conta Corrente do Cliente) deve ser informado.");
            }
            if (obj.getDescricao().equals("")) {
                throw new ConsistirException("O campo DESCRIÇÃO (Movimento de Conta Corrente do Cliente) deve ser informado.");
            }

            if ((obj.getResponsavelAutorizacao() == null) || (obj.getResponsavelAutorizacao().getCodigo().intValue() == 0)) {
                throw new ConsistirException("O campo RESPONSÁVEL PELA AUTORIZAÇÃO (Movimento de Conta Corrente do Cliente) deve ser informado.");
            }
            if (obj.getTipoMovimentacao().trim().isEmpty()) {
                throw new ConsistirException("O campo TIPO DE MOVIMENTAÇÃO (Movimento de Conta Corrente do Cliente)deve ser informado.");
            }
            if (obj.getValor().doubleValue() <= 0) {
                throw new ConsistirException("O campo VALOR deve ser maior do que zero.");
            }
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setSaldoAtual(new Double(0));
        setSaldoAnterior(new Double(0));
        setValor(new Double(0));
        setDescricao("");
        setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        setPessoa(new PessoaVO());
        //    setResponsavelRegistro( new ColaboradorVO() );
        setResponsavelAutorizacao(new UsuarioVO());
        setReciboPagamentoVO(new ReciboPagamentoVO());
        setContratoOperacaoVO(new ContratoOperacaoVO());
        setMovPagamentosVOs(new ArrayList());
        setClienteTransferencia(new ClienteVO());
    }

    public UsuarioVO getResponsavelAutorizacao() {
        return responsavelAutorizacao;
    }

    public void setResponsavelAutorizacao(UsuarioVO responsavelAutorizacao) {
        this.responsavelAutorizacao = responsavelAutorizacao;
    }

    public PessoaVO getPessoa() {
        if (this.Pessoa == null) {
            this.Pessoa = new PessoaVO();
        }
        return Pessoa;
    }

    public void setPessoa(PessoaVO Pessoa) {
        this.Pessoa = Pessoa;
    }

//    /**
//     * Retorna o objeto da classe <code>Colaborador</code> relacionado com (<code>MovimentoContaCorrenteCliente</code>).
//    */
//    public ColaboradorVO getResponsavelRegistro() {
//        if (responsavelRegistro == null) {
//            responsavelRegistro = new ColaboradorVO();
//        }
//        return (responsavelRegistro);
//    }
//     
//    /**
//     * Define o objeto da classe <code>Colaborador</code> relacionado com (<code>MovimentoContaCorrenteCliente</code>).
//    */
//    public void setResponsavelRegistro( ColaboradorVO obj) {
//        this.responsavelRegistro = obj;
//    }
    /**
     * Retorna o objeto da classe <code>Cliente</code> relacionado com (<code>MovimentoContaCorrenteCliente</code>).
     */
    public Date getDataRegistro() {
        return (dataRegistro);
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
     */
    public String getDataRegistro_Apresentar() {
        return (Uteis.getDataComHora(dataRegistro));
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public List<MovPagamentoVO> getMovPagamentosVOs() {
        return movPagamentosVOs;
    }

    public void setMovPagamentosVOs(List<MovPagamentoVO> movPagamentosVOs) {
        this.movPagamentosVOs = movPagamentosVOs;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getSaldoAtual() {
        return (saldoAtual);
    }

    public String getSaldoAtual_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(saldoAtual);
    }

    public void setSaldoAtual(Double saldoAtual) {
        this.saldoAtual = saldoAtual;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValor() {
        //VERIFICAÇÃO
        //IF this.valor != (duas casas decimais)
        //this.valor = (duas casas decimais)
        return this.valor;
        //return Formatador.obterValorNumerico(Valor.toString());
    }

    public String getValor_Apresentar() {
        return Formatador.formatarValorMonetario(valor);
    }

    public void setValor(Double Valor) {
        this.valor = Valor;
    }

    public String getTipoMovimentacao() {
        if (tipoMovimentacao == null) {
            tipoMovimentacao = "";
        }
        return tipoMovimentacao;
    }

    public String getTipoMovimentacao_Apresentar() {
        if (tipoMovimentacao != null) {
            if (tipoMovimentacao.equals("DE")) {
                return "Débito";
            } else if (tipoMovimentacao.equals("CR")) {
                return "Crédito";
            }
        }
        return "";
    }

    public void setTipoMovimentacao(String tipoMovimentacao) {
        this.tipoMovimentacao = tipoMovimentacao;
    }

    public Double getSaldoAnterior() {
        return saldoAnterior;
    }

    public void setSaldoAnterior(Double saldoAnterior) {
        this.saldoAnterior = saldoAnterior;
    }

    /**
     * @return the reciboPagamentoVO
     */
    public ReciboPagamentoVO getReciboPagamentoVO() {
        return reciboPagamentoVO;
    }

    /**
     * @param reciboPagamentoVO the reciboPagamentoVO to set
     */
    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamentoVO) {
        this.reciboPagamentoVO = reciboPagamentoVO;
    }

    /**
     * @return the contratoOperacaoVO
     */
    public ContratoOperacaoVO getContratoOperacaoVO() {
        return contratoOperacaoVO;
    }

    /**
     * @param contratoOperacaoVO the contratoOperacaoVO to set
     */
    public void setContratoOperacaoVO(ContratoOperacaoVO contratoOperacaoVO) {
        this.contratoOperacaoVO = contratoOperacaoVO;
    }

    public void setApresentarPanelSaldoContaCorrente(boolean apresentarPanelSaldoContaCorrente) {
        this.apresentarPanelSaldoContaCorrente = apresentarPanelSaldoContaCorrente;
    }

    public boolean isApresentarPanelSaldoContaCorrente() {
        return apresentarPanelSaldoContaCorrente;
    }

    /**
     * @param pagamentosDebito the pagamentosDebito to set
     */
    public void setPagamentosDebito(String pagamentosDebito) {
        this.pagamentosDebito = pagamentosDebito;
    }

    /**
     * @return the pagamentosDebito
     */
    public String getPagamentosDebito() {
        return pagamentosDebito;
    }

    public void setClienteTransferencia(ClienteVO clienteTransferencia) {
        this.clienteTransferencia = clienteTransferencia;
    }

    public ClienteVO getClienteTransferencia() {
        return clienteTransferencia;
    }

    public void setValorTransferencia(Double valorTransferencia) {
        this.valorTransferencia = valorTransferencia;
    }

    public Double getValorTransferencia() {
        return valorTransferencia;
    }

    public void setValorParcelaDebito(Double valorParcelaDebito) {
        this.valorParcelaDebito = valorParcelaDebito;
    }

    public Double getValorParcelaDebito() {
        return valorParcelaDebito;
    }

    public String getDataHoraRegistro() {
        return dataHoraRegistro;
    }

    public void setDataHoraRegistro(String dataHoraRegistro) {
        this.dataHoraRegistro = dataHoraRegistro;
    }

    public List<MovPagamentoVO> getMovPagamentosVOsTemporario() {
        return movPagamentosVOsTemporario;
    }

    public void setMovPagamentosVOsTemporario(List<MovPagamentoVO> movPagamentosVOsTemporario) {
        this.movPagamentosVOsTemporario = movPagamentosVOsTemporario;
    }

    public String getValorFormatado() {
        valorFormatado = Formatador.formatarMoeda(valor);
        return valorFormatado;
    }

    public String getSaldoAtualFormatado() {
        saldoAtualFormatado = Formatador.formatarMoeda(saldoAtual);
        return saldoAtualFormatado;
    }
}

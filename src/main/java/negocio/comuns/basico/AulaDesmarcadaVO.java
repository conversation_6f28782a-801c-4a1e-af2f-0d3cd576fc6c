
package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.OperacaoColetivaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.OperacaoColetiva;

import java.util.Date;

public class AulaDesmarcadaVO extends SuperVO {

    private Date dataOrigem;
    private Date dataReposicao;
    private Date dataLancamento;
    @ChaveEstrangeira
    private EmpresaVO empresaVO;
    @ChaveEstrangeira
    private ClienteVO clienteVO;
    @ChaveEstrangeira
    private ContratoVO contratoVO;
    @ChaveEstrangeira
    private TurmaVO turmaVO;
    @ChaveEstrangeira
    private HorarioTurmaVO horarioTurmaVO;
    @ChaveEstrangeira
    private UsuarioVO usuarioVO;
    private OrigemSistemaEnum origemSistemaEnum = OrigemSistemaEnum.ZW;
    @ChaveEstrangeira
    private ReposicaoVO reposicaoVO;
    private boolean desmarcadaPorAfastamento;
    @NaoControlarLogAlteracao
    private boolean permiteReporAulaDesmarcada = true;
    private ContratoVO contratoAnterior;
    private Integer turmaDestino;
    private OperacaoColetivaVO operacaoColetiva;
    private String justificativa;

    public Date getDataOrigem() {
        return dataOrigem;
    }

    public void setDataOrigem(Date dataOrigem) {
        this.dataOrigem = dataOrigem;
    }

    public Date getDataReposicao() {
        return dataReposicao;
    }

    public void setDataReposicao(Date dataReposicao) {
        this.dataReposicao = dataReposicao;
    }

    public Date getDataLancamento() {
        if (dataLancamento == null){
            dataLancamento = Calendario.hoje();
        }
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public HorarioTurmaVO getHorarioTurmaVO() {
        if (horarioTurmaVO == null){
            horarioTurmaVO = new HorarioTurmaVO();
        }
        return horarioTurmaVO;
    }

    public void setHorarioTurmaVO(HorarioTurmaVO horarioTurmaVO) {
        this.horarioTurmaVO = horarioTurmaVO;
    }

    public TurmaVO getTurmaVO() {
        if (turmaVO == null){
            turmaVO = new TurmaVO();
        }
        return turmaVO;
    }

    public void setTurmaVO(TurmaVO turmaVO) {
        this.turmaVO = turmaVO;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null){
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public ContratoVO getContratoVO() {
        if (contratoVO == null){
            contratoVO = new ContratoVO();
        }
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null){
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null){
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }


    public String getDataOrigem_Apresentar() {
        return Uteis.getData(this.dataOrigem, "dd/MM/yyyy");
    }

    public String getDescricaoOrigem() {
        return String.format("%s - %s - %s às %s - %s - com: %s", getDataOrigem_Apresentar(),
                getHorarioTurmaVO().getDiaSemana_Apresentar().toUpperCase(),
                getHorarioTurmaVO().getHoraInicial(),
                getHorarioTurmaVO().getHoraFinal(),
                getHorarioTurmaVO().getIdentificadorTurma(),
                getHorarioTurmaVO().getProfessor().getPessoa().getNome());
    }

    public String getDescricaoOrigemCurta() {
        return String.format("%s - %s - %s - %s às %s",
                this.turmaVO.getModalidade().getNome(),
                getDataOrigem_Apresentar(),
                getHorarioTurmaVO().getDiaSemana_Apresentar().toUpperCase(),
                getHorarioTurmaVO().getHoraInicial(),
                getHorarioTurmaVO().getHoraFinal());
    }

    public OrigemSistemaEnum getOrigemSistemaEnum() {
        return origemSistemaEnum;
    }

    public void setOrigemSistemaEnum(OrigemSistemaEnum origemSistemaEnum) {
        this.origemSistemaEnum = origemSistemaEnum;
    }

    public ReposicaoVO getReposicaoVO() {
        if (reposicaoVO == null) {
            reposicaoVO = new ReposicaoVO();
        }
        return reposicaoVO;
    }

    public void setReposicaoVO(ReposicaoVO reposicaoVO) {
        this.reposicaoVO = reposicaoVO;
    }

    public boolean isDesmarcadaPorAfastamento() {
        return desmarcadaPorAfastamento;
    }

    public void setDesmarcadaPorAfastamento(boolean desmarcadaPorAfastamento) {
        this.desmarcadaPorAfastamento = desmarcadaPorAfastamento;
    }

    public boolean isPermiteReporAulaDesmarcada() {
        return permiteReporAulaDesmarcada;
    }

    public void setPermiteReporAulaDesmarcada(boolean permiteReporAulaDesmarcada) {
        this.permiteReporAulaDesmarcada = permiteReporAulaDesmarcada;
    }

    public ContratoVO getContratoAnterior() {
        if (contratoAnterior == null) {
            contratoAnterior = new ContratoVO();
        }
        return contratoAnterior;
    }

    public void setContratoAnterior(ContratoVO contratoAnterior) {
        this.contratoAnterior = contratoAnterior;
    }

    public Integer getTurmaDestino() {
        return turmaDestino;
    }

    public void setTurmaDestino(Integer turmaDestino) {
        this.turmaDestino = turmaDestino;
    }

    public OperacaoColetivaVO getOperacaoColetiva() {
        if (operacaoColetiva == null){
            operacaoColetiva = new OperacaoColetivaVO();
        }
        return operacaoColetiva;
    }

    public void setOperacaoColetiva(OperacaoColetivaVO operacaoColetiva) {
        this.operacaoColetiva = operacaoColetiva;
    }

    public String getJustificativa() { return justificativa; }

    public void setJustificativa(String justificativa) { this.justificativa = justificativa; }
}

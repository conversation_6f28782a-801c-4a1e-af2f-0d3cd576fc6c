package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import org.json.JSONObject;
import controle.basico.ClienteControle;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;

import javax.swing.text.MaskFormatter;
import java.lang.reflect.Field;
import java.text.ParseException;

/**
 * Reponsável por manter os dados da entidade Telefone. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class TelefoneVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    private String ddi;
    protected String numero;
    protected String numeroEmergencia;
    protected String tipoTelefone;
    protected String descricao;
    @NaoControlarLogAlteracao
    protected Integer pessoa;
    protected String re;//residencial
    protected String ce;//celular
    protected String co;//comercial
    @NaoControlarLogAlteracao
    private Boolean selecionado;
    @NaoControlarLogAlteracao
    private boolean usarSistemaInternacional = false;
    private boolean receberSMS = false;
    private boolean usarNonoDigitoWApp = false;

    public TelefoneVO(JSONObject o) {
        super();
        try {
            inicializarDados();
            setDdi(o.optString("ddi"));
            setCodigo(o.getInt("codigo"));
            setNumero(o.getString("numero"));
            setTipoTelefone(o.getString("tipoTelefone"));
            setDescricao(o.getString("descricao"));
        } catch (Exception e) {
        }
    }
    public TelefoneVO(String ddi, Integer codigo, String numero, String tipoTelefone, String descricao) {
        super();
        this.ddi = ddi;
        this.codigo = codigo;
        this.numero = numero;
        this.tipoTelefone = tipoTelefone;
        this.descricao = descricao;
    }


    /**
     * @return the re
     */
    public String getRe() {
        return re;
    }

    /**
     * @param re the re to set
     */
    public void setRe(String re) {
        this.re = re;
    }

    /**
     * @return the ce
     */
    public String getCe() {
        return ce;
    }

    /**
     * @param ce the ce to set
     */
    public void setCe(String ce) {
        this.ce = ce;
    }

    /**
     * @return the co
     */
    public String getCo() {
        return co;
    }

    /**
     * @param co the co to set
     */
    public void setCo(String co) {
        this.co = co;
    }

    public boolean isUsarNonoDigitoWApp() {
        return usarNonoDigitoWApp;
    }

    public void setUsarNonoDigitoWApp(boolean usarNonoDigitoWApp) {
        this.usarNonoDigitoWApp = usarNonoDigitoWApp;
    }

    /**
     * Construtor padrão da classe <code>Telefone</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public TelefoneVO() {
        super();
        try {
            inicializarDados();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>TelefoneVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public void validarDados(boolean isInternacional) throws ConsistirException {
        if (!getValidarDados()) {
            return;
        }
        if (getNumero().equals("")) {
            throw new ConsistirException("O campo NÚMERO (Aba - Telefone) deve ser informado.");
        }
        if (getTipoTelefone().equals("")) {
            throw new ConsistirException("O campo TIPO DO TELEFONE (Aba - Telefone) deve ser informado.");
        }
        if (getTipoTelefone().equals("CE") || getTipoTelefone().equals("RE") || getTipoTelefone().equals("RC") || getTipoTelefone().equals("CO") || getTipoTelefone().equals("EM")) {
            validarTelefone(getNumero(), getTipoTelefone(), isInternacional);
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setNumero(getNumero().toUpperCase());
        setTipoTelefone(getTipoTelefone().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() throws Exception {
        setCodigo(0);
        setNumero("");
        setTipoTelefone("");
        setPessoa(0);
        setDescricao("");
    }

    public Integer getPessoa() {
        return (pessoa);
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getTipoTelefone() {
        if (tipoTelefone == null) {
            tipoTelefone = "";
        }
        return (tipoTelefone);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico. 
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o 
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc. 
     */
    public String getTipoTelefone_Apresentar() {
        if (tipoTelefone == null) {
            tipoTelefone = "";
        }
        if (tipoTelefone.equals("RE")) {
            return "Residencial";
        }
        if (tipoTelefone.equals("RC")) {
            return "Recado";
        }
        if (tipoTelefone.equals("CE")) {
            return "Celular";
        }
        if (tipoTelefone.equals("CO")) {
            return "Comercial";
        }
        if (tipoTelefone.equals("EM")) {
            return "Emergência";
        }
        return (tipoTelefone);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico.
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc.
     */
    public String getTipoTelefone_ApresentarEspecial() {
        if (tipoTelefone == null) {
            tipoTelefone = "";
        }
        if (descricao == null) {
            descricao = "";
        }
        if (tipoTelefone.equals("RE")) {
            if (!descricao.trim().isEmpty()) {
                return "";
            }
            return "Residencial";
        }
        if (tipoTelefone.equals("RC")) {
            if (!descricao.trim().isEmpty()) {
                return "";
            }
            return "Recado";
        }
        if (tipoTelefone.equals("CE")) {
            if (!descricao.trim().isEmpty()) {
                return "";
            }
            return "Celular";
        }
        if (tipoTelefone.equals("CO")) {
            if (!descricao.trim().isEmpty()) {
                return "";
            }
            return "Comercial";
        }
        if (tipoTelefone.equals("EM")) {
            if (!descricao.trim().isEmpty()) {
                return "";
            }
            return "Emergência";
        }
        return (tipoTelefone);
    }

    public boolean isEmpty() throws ConsistirException {
        return (getNumero().trim().isEmpty() && getTipoTelefone().trim().isEmpty());
    }

    public void setTipoTelefone(String tipoTelefone) {
        this.tipoTelefone = tipoTelefone;
    }

    public String getNumero() {
        if (numero == null) {
            numero = "";
        }
        return (numero);
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getNumeroApresentar() {
        return getDdi() + " " + getNumero();
    }

    public String getNumeroEmergencia(){
        if (numeroEmergencia == null){
            numeroEmergencia = "";
        }
        return (numeroEmergencia);
    }
    public  void setNumeroEmergencia(String numeroEmergencia){
        this.numeroEmergencia = numeroEmergencia;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        if(descricao ==null){
            descricao = "";
        }
        return descricao;
    }
    /**
     * Método usado para mostrar a descrição do
     * telefone com no máximo 10 caracteres
     */
    public String getDescricaoEspecial() {
        if (!descricao.trim().isEmpty()) {
            if (descricao.length() > 10) {
                return descricao.substring(0, 10);
            }
            return descricao;
        }
        return descricao;
    }

	/**
	 * @param selecionado the selecionado to set
	 */
	public void setSelecionado(Boolean selecionado) {
		this.selecionado = selecionado;
	}

	/**
	 * @return the selecionado
	 */
	public Boolean getSelecionado() {
		if(selecionado == null){
			selecionado = Boolean.FALSE;
		}
		return selecionado;
	}

    public JSONObject toJSON() {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                o.put(field.getName(), field.get(this));
            }


        } catch (Exception e) {
        }
        return o;
    }

    @Override
    public int hashCode(){
        return this.pessoa.hashCode();
    }

    @Override
    public boolean equals(Object obj){
        if ((obj == null) || (!(obj instanceof TelefoneVO)))
            return false;
        return ((TelefoneVO)obj).equals(this.codigo);
    }

    public static void validarTelefone(String numero, String nomeTelefoneMensagem, boolean isInternacional) throws ConsistirException {

        boolean contemParentesesAbrindo = false;
        boolean contemParentesesFechando = false;
        int qtdDigitos = 0;

        if (!isInternacional) {
            for (char cTelefone : numero.toCharArray()) {
                if (Character.isDigit(cTelefone)) {
                    qtdDigitos++;
                } else {
                    if (cTelefone == '(') {
                        contemParentesesAbrindo = true;
                    } else if (cTelefone == ')') {
                        contemParentesesFechando = true;
                    } else if (cTelefone != '-') {
                        throw new ConsistirException("O telefone do tipo " + TipoTelefoneEnum.obterPorSigla(nomeTelefoneMensagem) + " (" + numero + ") é inválido");
                    }
                }
            }

            if (!contemParentesesAbrindo || !contemParentesesFechando) {
                throw new ConsistirException("O telefone do tipo " + TipoTelefoneEnum.obterPorSigla(nomeTelefoneMensagem) + " (" + numero + ") está com a máscara errada");
            }

            if (qtdDigitos < 10) {
                throw new ConsistirException("O telefone do tipo " + TipoTelefoneEnum.obterPorSigla(nomeTelefoneMensagem) + " (" + numero + ") está incompleto");
            }
        }
    }

    public String getNumeroSemMascara() {
        return Uteis.removerMascara(this.getNumero().replaceAll("\\(", "").replaceAll("\\)", ""));
    }

    public boolean getNumeroCelular() {
        return Uteis.validarTelefoneCelular(this.numero);
    }
    public TelefoneWS toWS() {
        TelefoneWS telefoneWS = new TelefoneWS();
        telefoneWS.setCodigo(this.codigo);
        telefoneWS.setNumero(this.numero);
        telefoneWS.setTipoTelefone(this.tipoTelefone);
        telefoneWS.setDescricao(this.descricao);
        telefoneWS.setTipoTelefoneDescricao(TipoTelefoneEnum.obterPorSigla(this.tipoTelefone).getDescricao());
        return telefoneWS;
    }

    public String getDdi() {
	    if (ddi == null) {
	        ddi = "";
        }
        return ddi;
    }

    public void setDdi(String ddi) {
        this.ddi = ddi;
    }

    public boolean isUsarSistemaInternacional() {
        return usarSistemaInternacional;
    }

    public void setUsarSistemaInternacional(boolean usarSistemaInternacional) {
        this.usarSistemaInternacional = usarSistemaInternacional;
    }

    public boolean isReceberSMS() {
        return receberSMS;
    }

    public void setReceberSMS(boolean receberSMS) {
        this.receberSMS = receberSMS;
    }

    public static String formatTelefone(String value, String pattern) {
        MaskFormatter mf;
        try {
            value = value.replace("-","");
            mf = new MaskFormatter(pattern);
            mf.setValueContainsLiteralCharacters(false);
            return mf.valueToString(value);
        } catch (ParseException ex) {
            return value;
        }
    }

    public static String aplicarMascara(String telefone, String mascara) {
        try {
            if (telefone == null || telefone.isEmpty()) {
                return "";
            }
            if (mascara == null || mascara.isEmpty()) {
                return telefone;
            }

            MaskFormatter mf;

            mf = new MaskFormatter(mascara);
            mf.setValueContainsLiteralCharacters(false);
            return mf.valueToString(telefone);
        } catch (Exception e) {
            return telefone;
        }
    }
}

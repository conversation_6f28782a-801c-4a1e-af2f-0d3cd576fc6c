package negocio.comuns.basico;


import negocio.comuns.arquitetura.SuperTO;

/**
 * Created by <PERSON><PERSON> on 18/11/2016.
 */
public class ArtefatoUcpTO extends SuperTO {

    private Integer codigo;
    private String titulo;
    private String descricao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

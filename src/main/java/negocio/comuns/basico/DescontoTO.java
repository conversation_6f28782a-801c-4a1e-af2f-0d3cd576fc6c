package negocio.comuns.basico;

import br.com.pactosolucoes.comuns.util.Formatador;

import java.util.Date;

public class DescontoTO {

    public String descricao;
    public String usuario;
    public String matricula;
    public String cliente;
    public Integer contrato;
    public Date lancamento;
    public Double valor;
    public String convenio;
    public String moeda;



    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getCliente() {
        return cliente;
    }

    public void setCliente(String cliente) {
        this.cliente = cliente;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Date getLancamento() {
        return lancamento;
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }

    public Double getValor() {
        return valor == null ? 0.0 : valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getValorApresentar(){
        return moeda + " " + Formatador.formatarValorMonetarioSemMoeda(this.getValor());
    }

    public String getConvenio() {
        return convenio;
    }

    public void setConvenio(String convenio) {
        this.convenio = convenio;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }
}

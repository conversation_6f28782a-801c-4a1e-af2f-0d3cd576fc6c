package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.notaFiscal.AmbienteEmissaoNotaFiscalEnum;

public class ConfiguracaoNotaFiscalAmbienteVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO;
    private AmbienteEmissaoNotaFiscalEnum ambienteEmissao;
    private String serieNFe;
    private Integer sequencialNFe;
    private Integer sequencialLoteNFe;
    private String usuarioAcessoProvedor;
    private String senhaAcessoProvedor;
    private String tokenAcessoProvedor;
    private String idCSC;
    private String csc;


    public ConfiguracaoNotaFiscalAmbienteVO() {
    }

    public ConfiguracaoNotaFiscalAmbienteVO(AmbienteEmissaoNotaFiscalEnum ambienteEmissao) {
        this.ambienteEmissao = ambienteEmissao;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public AmbienteEmissaoNotaFiscalEnum getAmbienteEmissao() {
        if (ambienteEmissao == null) {
            ambienteEmissao = AmbienteEmissaoNotaFiscalEnum.HOMOLOGACAO;
        }
        return ambienteEmissao;
    }

    public void setAmbienteEmissao(AmbienteEmissaoNotaFiscalEnum ambienteEmissao) {
        this.ambienteEmissao = ambienteEmissao;
    }

    public Integer getSequencialNFe() {
        if (sequencialNFe == null) {
            sequencialNFe = 0;
        }
        return sequencialNFe;
    }

    public void setSequencialNFe(Integer sequencialNFe) {
        this.sequencialNFe = sequencialNFe;
    }

    public String getSerieNFe() {
        if (serieNFe == null) {
            serieNFe = "";
        }
        return serieNFe;
    }

    public void setSerieNFe(String serieNFe) {
        this.serieNFe = serieNFe;
    }

    public Integer getSequencialLoteNFe() {
        if (sequencialLoteNFe == null) {
            sequencialLoteNFe = 0;
        }
        return sequencialLoteNFe;
    }

    public void setSequencialLoteNFe(Integer sequencialLoteNFe) {
        this.sequencialLoteNFe = sequencialLoteNFe;
    }

    public String getUsuarioAcessoProvedor() {
        if (usuarioAcessoProvedor == null) {
            usuarioAcessoProvedor = "";
        }
        return usuarioAcessoProvedor;
    }

    public void setUsuarioAcessoProvedor(String usuarioAcessoProvedor) {
        this.usuarioAcessoProvedor = usuarioAcessoProvedor;
    }

    public String getSenhaAcessoProvedor() {
        if (senhaAcessoProvedor == null) {
            senhaAcessoProvedor = "";
        }
        return senhaAcessoProvedor;
    }

    public void setSenhaAcessoProvedor(String senhaAcessoProvedor) {
        this.senhaAcessoProvedor = senhaAcessoProvedor;
    }

    public String getTokenAcessoProvedor() {
        if (tokenAcessoProvedor == null) {
            tokenAcessoProvedor = "";
        }
        return tokenAcessoProvedor;
    }

    public void setTokenAcessoProvedor(String tokenAcessoProvedor) {
        this.tokenAcessoProvedor = tokenAcessoProvedor;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalVO() {
        if (configuracaoNotaFiscalVO == null) {
            configuracaoNotaFiscalVO = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalVO;
    }

    public void setConfiguracaoNotaFiscalVO(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) {
        this.configuracaoNotaFiscalVO = configuracaoNotaFiscalVO;
    }

    public String getIdCSC() {
        if (idCSC == null) {
            idCSC = "";
        }
        return idCSC;
    }

    public void setIdCSC(String idCSC) {
        this.idCSC = idCSC;
    }

    public String getCsc() {
        if (csc == null) {
            csc = "";
        }
        return csc;
    }

    public void setCsc(String csc) {
        this.csc = csc;
    }
}
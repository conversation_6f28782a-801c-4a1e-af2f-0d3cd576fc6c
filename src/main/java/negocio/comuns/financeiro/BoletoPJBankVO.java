/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import relatorio.negocio.comuns.financeiro.DescontoBoletoTO;
import servicos.impl.boleto.BancoEnum;
import servicos.integracao.pjbank.beanRecebimento.BoletoRecebimento;

import java.text.ParseException;
import java.util.Date;

public class BoletoPJBankVO extends SuperVO {

    @ChaveEstrangeira
    private MovParcelaVO movParcelaVO;
    @ChaveEstrangeira
    private ConvenioCobrancaVO convenioCobrancaVO;

    private Date dataRegistro;
    private Date dataVencimento;
    private Date dataCredito;
    private Date datapagamento;
    private Float valor;
    private Float juros;
    private Float multa;
    private Float desconto;
    private Integer pedidoNumero;
    private String webhook;
    private String banco;
    private String tokenFacilitador;
    private String linkGrupo;
    private String idUnico;
    private String idUnicoOriginal;
    private String linkBoleto;
    private String linhaDigitavel;
    private Float valorPago;
    private Float valorLiquido;
    private Float valorTarifa;
    private boolean pagamentoDuplicado;
    private String nossoNumero;
    private String nossoNumeroOriginal;
    private String registroSistemaBancario;
    private String registroRegeicaoMotivo;
    @ChaveEstrangeira
    private ReciboPagamentoVO reciboPagamentoVO;
    private String pedidoNumeroPJBank;
    private PessoaVO pessoaVO;
    private String chavePJBank;
    private String credencialPJBank;
    private String jsonEstorno;
    @ChaveEstrangeira
    private MovPagamentoVO movPagamentoVO;
    private boolean gerouCreditoContaCorrente = false;
    private Date dataAtualizacao;
    private Integer diaDoMesDescontoBoletoPagAntecipado;
    private Double porcentagemDescontoBoletoPagAntecipado;
    private Double multaValorFixo;
    private Double jurosValorFixo;

    public BoletoPJBankVO() {
    }

    public BoletoPJBankVO(BoletoRecebimento boletoRecebimento, ConvenioCobrancaVO convenio, Integer pessoa,
                          DescontoBoletoTO descontoBoletoTO, Double multaValorFixo, Double jurosValorFixo) {
        this.getMovParcelaVO().setCodigo(boletoRecebimento.getNumParcela());
        this.getConvenioCobrancaVO().setCodigo(convenio.getCodigo());
        this.dataRegistro = Calendario.hoje();
        this.dataVencimento = boletoRecebimento.getVencimento();
        this.dataCredito = null;
        this.datapagamento = null;
        this.valor = (float) boletoRecebimento.getValor();
        this.juros = (float) boletoRecebimento.getJuros();
        this.multa = (float) boletoRecebimento.getMulta();
        this.desconto = 0.0f;
        this.pedidoNumero = boletoRecebimento.getPedidoNumero();
        this.webhook = boletoRecebimento.getWebhook();
        this.banco = boletoRecebimento.getBanco();
        this.tokenFacilitador = boletoRecebimento.getTokenFacilitador();
        this.linkGrupo = "";
        this.idUnico = boletoRecebimento.getIdUnico();
        this.idUnicoOriginal = "";
        this.linkBoleto = boletoRecebimento.getLinkBoleto();
        this.linhaDigitavel = boletoRecebimento.getLinhaDigitavel();
        this.valorPago = 0.0f;
        this.valorLiquido = 0.0f;
        this.valorTarifa = 0.0f;
        this.pagamentoDuplicado = false;
        this.nossoNumero = boletoRecebimento.getNossoNumero();
        this.nossoNumeroOriginal = "0";
        this.registroSistemaBancario = "Aguardando Registro";
        this.registroRegeicaoMotivo = "";
        this.pedidoNumeroPJBank = boletoRecebimento.getPedidoNumeroPJBank();
        this.getPessoaVO().setCodigo(pessoa);
        this.chavePJBank = boletoRecebimento.getChavePJBank();
        this.credencialPJBank = boletoRecebimento.getCredencialPJBank();
        if (descontoBoletoTO != null) {
            this.diaDoMesDescontoBoletoPagAntecipado = descontoBoletoTO.getDiaMaximoPagamentoDesconto();
            this.porcentagemDescontoBoletoPagAntecipado = descontoBoletoTO.getPorcentagemDesconto();
        }
        this.multaValorFixo = multaValorFixo;
        this.jurosValorFixo = jurosValorFixo;
    }

    public MovParcelaVO getMovParcelaVO() {
        if (movParcelaVO == null) {
            movParcelaVO = new MovParcelaVO();
        }
        return movParcelaVO;
    }

    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public Date getDataCredito() {
        return dataCredito;
    }

    public void setDataCredito(Date dataCredito) {
        this.dataCredito = dataCredito;
    }

    public String getValorApresentar() {
        if (getValor() == null) {
            return "";
        } else {
            return Formatador.formatarValorMonetario(Double.valueOf(getValor()));
        }
    }

    public Float getValor() {
        return valor;
    }

    public void setValor(Float valor) {
        this.valor = valor;
    }

    public Float getJuros() {
        return juros;
    }

    public void setJuros(Float juros) {
        this.juros = juros;
    }

    public Float getMulta() {
        return multa;
    }

    public void setMulta(Float multa) {
        this.multa = multa;
    }

    public Float getDesconto() {
        return desconto;
    }

    public void setDesconto(Float desconto) {
        this.desconto = desconto;
    }

    public Integer getPedidoNumero() {
        return pedidoNumero;
    }

    public void setPedidoNumero(Integer pedidoNumero) {
        this.pedidoNumero = pedidoNumero;
    }

    public String getWebhook() {
        return webhook;
    }

    public void setWebhook(String webhook) {
        this.webhook = webhook;
    }

    public String getBanco() {
        if (banco == null) {
            banco = "";
        }
        return banco;
    }
    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getTokenFacilitador() {
        return tokenFacilitador;
    }

    public void setTokenFacilitador(String tokenFacilitador) {
        this.tokenFacilitador = tokenFacilitador;
    }

    public String getLinkGrupo() {
        return linkGrupo;
    }

    public void setLinkGrupo(String linkGrupo) {
        this.linkGrupo = linkGrupo;
    }

    public String getIdUnico() {
        return idUnico;
    }

    public void setIdUnico(String idUnico) {
        this.idUnico = idUnico;
    }

    public String getIdUnicoOriginal() {
        return idUnicoOriginal;
    }

    public void setIdUnicoOriginal(String idUnicoOriginal) {
        this.idUnicoOriginal = idUnicoOriginal;
    }

    public String getLinkBoleto() {
        if (linkBoleto == null) {
            linkBoleto = "";
        }
        return linkBoleto;
    }

    public void setLinkBoleto(String linkBoleto) {
        this.linkBoleto = linkBoleto;
    }

    public String getLinhaDigitavel() {
        return linhaDigitavel;
    }

    public void setLinhaDigitavel(String linhaDigitavel) {
        this.linhaDigitavel = linhaDigitavel;
    }

    public String getValorPagoApresentar() {
        if (getValorPago() == null) {
            return "";
        } else {
            return Formatador.formatarValorMonetario(Double.valueOf(getValorPago()));
        }
    }

    public Float getValorPago() {
        return valorPago;
    }

    public void setValorPago(Float valorPago) {
        this.valorPago = valorPago;
    }

    public String getValorLiquidoApresentar() {
        if (getValorLiquido() == null) {
            return "";
        } else {
            return Formatador.formatarValorMonetario(Double.valueOf(getValorLiquido()));
        }
    }

    public Float getValorLiquido() {
        return valorLiquido;
    }

    public void setValorLiquido(Float valorLiquido) {
        this.valorLiquido = valorLiquido;
    }

    public String getValorTarifaApresentar() {
        if (getValorTarifa() == null) {
            return "";
        } else {
            return Formatador.formatarValorMonetario(Double.valueOf(getValorTarifa()));
        }
    }

    public Float getValorTarifa() {
        return valorTarifa;
    }

    public void setValorTarifa(Float valorTarifa) {
        this.valorTarifa = valorTarifa;
    }

    public boolean isPagamentoDuplicado() {
        return pagamentoDuplicado;
    }

    public void setPagamentoDuplicado(boolean pagamentoDuplicado) {
        this.pagamentoDuplicado = pagamentoDuplicado;
    }

    public String getNossoNumero() {
        if (nossoNumero == null) {
            nossoNumero = "";
        }
        return nossoNumero;
    }

    public void setNossoNumero(String nossoNumero) {
        this.nossoNumero = nossoNumero;
    }

    public String getNossoNumeroOriginal() {
        return nossoNumeroOriginal;
    }

    public void setNossoNumeroOriginal(String nossoNumeroOriginal) {
        this.nossoNumeroOriginal = nossoNumeroOriginal;
    }

    public String getRegistroSistemaBancario() {
        if (registroSistemaBancario == null) {
            registroSistemaBancario = "";
        }
        return registroSistemaBancario;
    }

    public void setRegistroSistemaBancario(String registroSistemaBancario) {
        this.registroSistemaBancario = registroSistemaBancario;
    }

    public String getRegistroRegeicaoMotivo() {
        return registroRegeicaoMotivo;
    }

    public void setRegistroRegeicaoMotivo(String registroRegeicaoMotivo) {
        this.registroRegeicaoMotivo = registroRegeicaoMotivo;
    }

    public Date getDatapagamento() {
        return datapagamento;
    }

    public void setDatapagamento(Date datapagamento) {
        this.datapagamento = datapagamento;
    }

    public String getNomeBanco() {
        try {
            return BancoEnum.consultarNomePorCodigo(Integer.parseInt(getBanco()));
        } catch (Exception ex) {
            return "";
        }
    }

    public ReciboPagamentoVO getReciboPagamentoVO() {
        if (reciboPagamentoVO == null) {
            reciboPagamentoVO = new ReciboPagamentoVO();
        }
        return reciboPagamentoVO;
    }

    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamentoVO) {
        this.reciboPagamentoVO = reciboPagamentoVO;
    }

    public String getStatus() {
        String status = "";
        if (isPagamentoDuplicado()) {
            status = "Pago 2 vezes ou mais!";
        } else if (getRegistroSistemaBancario().equalsIgnoreCase("confirmado") && getValorPago() <= 0) {
            status = "Aguardando Pagamento";
        } else if ((getRegistroSistemaBancario().equalsIgnoreCase("confirmado") && getValorPago() > 0) || getReciboPagamentoVO().getCodigo() > 0) {
            status = "Pago";
        } else if (getRegistroSistemaBancario().equalsIgnoreCase("rejeitado")) {
            status = "Rejeitado: " + getRegistroRegeicaoMotivo();
        } else if (getRegistroSistemaBancario().equalsIgnoreCase("enviado")) {
            status = "Aguardando Registro";
        } else if (getRegistroSistemaBancario().equalsIgnoreCase("baixado")) {
            status = "Cancelado";
        }
        if (UteisValidacao.emptyString(status)) {
            status = Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(getRegistroSistemaBancario());
        }
        return status;
    }

    public String getStatusTitle() {
        if (getRegistroSistemaBancario().equalsIgnoreCase("pendente")) {
            return "Aguardando o PJBank enviar para o banco registrar o boleto.";
        } else if (getRegistroSistemaBancario().equalsIgnoreCase("enviado")) {
            return "Pedido de registro enviado para o banco, esperando a confirmação do registro.";
        } else if (getRegistroSistemaBancario().equalsIgnoreCase("confirmado")) {
            return "Confirmação do registro do boleto pelo banco.";
        } else if (getRegistroSistemaBancario().equalsIgnoreCase("rejeitado")) {
            String title = "Rejeição do registro do boleto pelo banco.";
            if (UteisValidacao.emptyString(getRegistroRegeicaoMotivo())) {
                title += ("<br/><br/>Motivo: " + getRegistroRegeicaoMotivo());
            }
            return title;
        } else if (getRegistroSistemaBancario().equalsIgnoreCase("baixado")) {
            return "Pedido de baixa realizado e enviado ao banco.";
        }
        return "";
    }

    public String getPedidoNumeroPJBank() {
        if (pedidoNumeroPJBank == null) {
            pedidoNumeroPJBank = "";
        }
        return pedidoNumeroPJBank;
    }

    public void setPedidoNumeroPJBank(String pedidoNumeroPJBank) {
        this.pedidoNumeroPJBank = pedidoNumeroPJBank;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public String getChavePJBank() {
        if (chavePJBank == null) {
            chavePJBank = "";
        }
        return chavePJBank;
    }

    public void setChavePJBank(String chavePJBank) {
        this.chavePJBank = chavePJBank;
    }

    public String getCredencialPJBank() {
        if (credencialPJBank == null) {
            credencialPJBank = "";
        }
        return credencialPJBank;
    }

    public void setCredencialPJBank(String credencialPJBank) {
        this.credencialPJBank = credencialPJBank;
    }

    public boolean isPodeCancelar() {
        return !getStatus().equalsIgnoreCase("Pago") && !getStatus().equalsIgnoreCase("Cancelado") && UteisValidacao.emptyNumber(getReciboPagamentoVO().getCodigo());
    }

    public boolean isPodeEnviarEmail() {
        return getStatus().equalsIgnoreCase("Aguardando Pagamento") ||
                getStatus().equalsIgnoreCase("Aguardando Registro");
    }

    public boolean isPodeSincronizar() {
        return !getStatus().equalsIgnoreCase("Cancelado") && UteisValidacao.emptyNumber(getReciboPagamentoVO().getCodigo());
    }

    public boolean isCancelado() {
        return getStatus().equalsIgnoreCase("Cancelado");
    }

    public boolean isEstornado() {
        return getStatus().equalsIgnoreCase("Estornado");
    }

    public String getEstornoTitle() {
        try {
            if (UteisValidacao.emptyString(getJsonEstorno())) {
                return "";
            }
            JSONObject json = new JSONObject(getJsonEstorno());
            StringBuilder title = new StringBuilder();
            if (isEstornado()) {
                title.append("<b>A parcela que estava vinculada ao boleto foi estornada no ZillyonWeb, porem não foi possivel cancelar o boleto na PJBANK.</b><br/>");
            } else {
                title.append("<b>Boleto foi cancelado na PJBANK.</b><br/>Isso pode acontecer automaticamente quando se estorna o contrato do aluno<br/>");
                title.append("ou então pelo cancelamento manual através do botão \"Cancelar Boleto\" disponível aqui nesta tela.<br/>");
            }
            title.append("<br/><b>Operação:</b> ").append(json.optString("operacao"));
            title.append("<br/><b>Data:</b> ").append(json.optString("data"));
            String usuario = json.optString("usuario");
            if (!UteisValidacao.emptyString(usuario)) {
                title.append("<br/><b>Usuário:</b> ").append(usuario);
            }
            String msgCancelarBoleto = json.optString("msgCancelarBoleto");
            if (!UteisValidacao.emptyString(msgCancelarBoleto)) {
                title.append("<br/><b>Retorno cancelar boleto:</b> ").append(msgCancelarBoleto);
            }
            return title.toString();
        } catch (Exception ex) {
            return getJsonEstorno() + " - " + ex.getMessage();
        }
    }

    public String getJsonEstorno() {
        if (jsonEstorno == null) {
            jsonEstorno = "";
        }
        return jsonEstorno;
    }

    public void setJsonEstorno(String jsonEstorno) {
        this.jsonEstorno = jsonEstorno;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        if (movPagamentoVO == null) {
            movPagamentoVO = new MovPagamentoVO();
        }
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public boolean isGerouCreditoContaCorrente() {
        return gerouCreditoContaCorrente;
    }

    public void setGerouCreditoContaCorrente(boolean gerouCreditoContaCorrente) {
        this.gerouCreditoContaCorrente = gerouCreditoContaCorrente;
    }

    public String getDataAtualizacaoApresentar() {
        if (getDataAtualizacao() == null) {
            return "";
        } else {
            return Formatador.formatarData(getDataAtualizacao(), "dd/MM/yyyy HH:mm:ss");
        }
    }

    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }

    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }

    public Boolean possuiDesconto() {
        return !UteisValidacao.emptyNumber(getDiaDoMesDescontoBoletoPagAntecipado()) && !UteisValidacao.emptyNumber(getPorcentagemDescontoBoletoPagAntecipado());
    }

    public Integer getDiaDoMesDescontoBoletoPagAntecipado() {
        if (diaDoMesDescontoBoletoPagAntecipado == null) {
            diaDoMesDescontoBoletoPagAntecipado = 0;
        }
        return diaDoMesDescontoBoletoPagAntecipado;
    }

    public void setDiaDoMesDescontoBoletoPagAntecipado(Integer diaDoMesDescontoBoletoPagAntecipado) {
        this.diaDoMesDescontoBoletoPagAntecipado = diaDoMesDescontoBoletoPagAntecipado;
    }

    public Double getPorcentagemDescontoBoletoPagAntecipado() {
        if (porcentagemDescontoBoletoPagAntecipado == null) {
            porcentagemDescontoBoletoPagAntecipado = 0.0;
        }
        return porcentagemDescontoBoletoPagAntecipado;
    }

    public void setPorcentagemDescontoBoletoPagAntecipado(Double porcentagemDescontoBoletoPagAntecipado) {
        this.porcentagemDescontoBoletoPagAntecipado = porcentagemDescontoBoletoPagAntecipado;
    }

    public Double getMultaValorFixo() {
        return multaValorFixo;
    }

    public void setMultaValorFixo(Double multaValorFixo) {
        this.multaValorFixo = multaValorFixo;
    }

    public Double getJurosValorFixo() {
        return jurosValorFixo;
    }

    public void setJurosValorFixo(Double jurosValorFixo) {
        this.jurosValorFixo = jurosValorFixo;
    }
}

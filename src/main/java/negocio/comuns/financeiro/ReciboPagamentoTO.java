package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.contrato.ContratoTO;
import negocio.comuns.utilitarias.Calendario;

/**
 *
 * <AUTHOR>
 */
public class ReciboPagamentoTO extends SuperTO {

    private static final long serialVersionUID = -8132084988342837624L;
    private int codigo = 0;
    private String nomePagador = "";
    private Date dataRecebimento = Calendario.hoje();
    private double valor = 0.0;
    private List<ContratoTO> contratos = new ArrayList<ContratoTO>();

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getNomePagador() {
        return nomePagador;
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public Date getDataRecebimento() {
        return dataRecebimento;
    }

    public void setDataRecebimento(Date dataRecebimento) {
        this.dataRecebimento = dataRecebimento;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public List<ContratoTO> getContratos() {
        return contratos;
    }

    public void setContratos(List<ContratoTO> contratos) {
        this.contratos = contratos;
    }
}

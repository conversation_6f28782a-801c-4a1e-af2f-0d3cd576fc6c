package negocio.comuns.financeiro.openbanking.stone;

import annotations.arquitetura.ChavePrimaria;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.arquitetura.SuperVO;


@JsonIgnoreProperties(ignoreUnknown = true)
public class ContaStoneVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer empresa;
    private String account_code;
    private String branch_code;
    private String id;
    private String owner_document;
    private String owner_id;
    private String owner_name;
    private Boolean restricted_features;
    private String institution_code;


    //Conta financeiro
    private String descricaoConta;
    private String descricaoCurtaConta;
    private Integer codigoBanco;
    private String agenciaConta;
    private String digitoAgenciaConta;
    private Boolean contaAtiva;
    private Boolean contaTotalizadaBI;
    private String contaObservacao;
    private String descricaoTipoConta;
    private String ComportamentoTipoConta;
    private String descricaoBancoOpenBankZW;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getAccount_code() {
        return account_code;
    }

    public void setAccount_code(String account_code) {
        this.account_code = account_code;
    }

    public String getBranch_code() {
        return branch_code;
    }

    public void setBranch_code(String branch_code) {
        this.branch_code = branch_code;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOwner_document() {
        return owner_document;
    }

    public void setOwner_document(String owner_document) {
        this.owner_document = owner_document;
    }

    public String getOwner_id() {
        return owner_id;
    }

    public void setOwner_id(String owner_id) {
        this.owner_id = owner_id;
    }

    public String getOwner_name() {
        return owner_name;
    }

    public void setOwner_name(String owner_name) {
        this.owner_name = owner_name;
    }

    public Boolean getRestricted_features() {
        if(this.restricted_features == null){
            this.restricted_features = false;
        }
        return restricted_features;
    }

    public void setRestricted_features(Boolean restricted_features) {
        this.restricted_features = restricted_features;
    }

    public String getInstitution_code() {
        return institution_code;
    }

    public void setInstitution_code(String institution_code) {
        this.institution_code = institution_code;
    }

    public String getDescricaoConta() {
        return descricaoConta;
    }

    public void setDescricaoConta(String descricaoConta) {
        this.descricaoConta = descricaoConta;
    }

    public String getDescricaoCurtaConta() {
        return descricaoCurtaConta;
    }

    public void setDescricaoCurtaConta(String descricaoCurtaConta) {
        this.descricaoCurtaConta = descricaoCurtaConta;
    }

    public Integer getCodigoBanco() {
        return codigoBanco;
    }

    public void setCodigoBanco(Integer codigoBanco) {
        this.codigoBanco = codigoBanco;
    }

    public String getAgenciaConta() {
        return agenciaConta;
    }

    public void setAgenciaConta(String agenciaConta) {
        this.agenciaConta = agenciaConta;
    }

    public String getDigitoAgenciaConta() {
        return digitoAgenciaConta;
    }

    public void setDigitoAgenciaConta(String digitoAgenciaConta) {
        this.digitoAgenciaConta = digitoAgenciaConta;
    }

    public Boolean getContaAtiva() {
        return contaAtiva;
    }

    public void setContaAtiva(Boolean contaAtiva) {
        this.contaAtiva = contaAtiva;
    }

    public Boolean getContaTotalizadaBI() {
        return contaTotalizadaBI;
    }

    public void setContaTotalizadaBI(Boolean contaTotalizadaBI) {
        this.contaTotalizadaBI = contaTotalizadaBI;
    }

    public String getContaObservacao() {
        return contaObservacao;
    }

    public void setContaObservacao(String contaObservacao) {
        this.contaObservacao = contaObservacao;
    }

    public String getDescricaoTipoConta() {
        return descricaoTipoConta;
    }

    public void setDescricaoTipoConta(String descricaoTipoConta) {
        this.descricaoTipoConta = descricaoTipoConta;
    }

    public String getComportamentoTipoConta() {
        return ComportamentoTipoConta;
    }

    public void setComportamentoTipoConta(String comportamentoTipoConta) {
        ComportamentoTipoConta = comportamentoTipoConta;
    }

    public String getDescricaoBancoOpenBankZW() {
        return descricaoBancoOpenBankZW;
    }

    public void setDescricaoBancoOpenBankZW(String descricaoBancoOpenBankZW) {
        this.descricaoBancoOpenBankZW = descricaoBancoOpenBankZW;
    }
}

package negocio.comuns.financeiro.openbanking.stone;

import annotations.arquitetura.ChavePrimaria;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.arquitetura.SuperVO;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TransferenciaStoneVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private String id;
    private Integer empresa;
    private String idempotency_key;
    private String account_id;
    private String created_at;
    private String created_by;
    private Integer amount;
    private String scheduled_to;
    private Integer movConta;
    private Integer movContaDesc;
    private String eventIdWebhook;
    private String operation_type;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getMovConta() {
        return movConta;
    }

    public void setMovConta(Integer movConta) {
        this.movConta = movConta;
    }

    public String getIdempotency_key() {
        return idempotency_key;
    }

    public void setIdempotency_key(String idempotency_key) {
        this.idempotency_key = idempotency_key;
    }

    public String getAccount_id() {
        return account_id;
    }

    public void setAccount_id(String account_id) {
        this.account_id = account_id;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getScheduled_to() {
        return scheduled_to;
    }

    public void setScheduled_to(String scheduled_to) {
        this.scheduled_to = scheduled_to;
    }

    public String getEventIdWebhook() {
        return eventIdWebhook;
    }

    public void setEventIdWebhook(String eventIdWebhook) {
        this.eventIdWebhook = eventIdWebhook;
    }

    public String getOperation_type() {
        return operation_type;
    }

    public void setOperation_type(String operation_type) {
        this.operation_type = operation_type;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getCreated_by() {
        return created_by;
    }

    public void setCreated_by(String created_by) {
        this.created_by = created_by;
    }

    public Integer getMovContaDesc() {
        return movContaDesc;
    }

    public void setMovContaDesc(Integer movContaDesc) {
        this.movContaDesc = movContaDesc;
    }
}

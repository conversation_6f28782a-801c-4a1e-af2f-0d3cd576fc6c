package negocio.comuns.financeiro.openbanking.stone;

import annotations.arquitetura.ChavePrimaria;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.arquitetura.SuperVO;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RetornoStoneVO extends SuperVO {

    private String eventId;
    private String transacaoId;
    private Integer empresa;
    private String status;
    private String retorno;
    private String eventType;

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getTransacaoId() {
        return transacaoId;
    }

    public void setTransacaoId(String transacaoId) {
        this.transacaoId = transacaoId;
    }

    public String getRetorno() {
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }
}

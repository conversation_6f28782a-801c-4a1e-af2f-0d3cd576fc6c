package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.TipoBloqueioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 28/04/2020
 */
public class PessoaBloqueioCobrancaTO extends SuperVO {

    private String matricula;
    private Integer cliente;
    private Integer pessoa;
    private String nome;
    private Integer empresa;
    private String empresaNome;
    private Date dataBloqueio;
    private String telefones;
    private String emails;
    private TipoBloqueioCobrancaEnum tipoBloqueioCobrancaEnum;
    private boolean sucesso = false;
    private String msgResultado;

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmpresaNome() {
        return empresaNome;
    }

    public void setEmpresaNome(String empresaNome) {
        this.empresaNome = empresaNome;
    }

    public Date getDataBloqueio() {
        return dataBloqueio;
    }

    public void setDataBloqueio(Date dataBloqueio) {
        this.dataBloqueio = dataBloqueio;
    }

    public String getDataBloqueioApresentar() {
        if (getDataBloqueio() != null) {
            return Calendario.getDataAplicandoFormatacao(getDataBloqueio(), "dd/MM/yyyy");
        } else {
            return "";
        }
    }

    public String getTelefones() {
        if (telefones == null) {
            telefones = "";
        }
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public String getEmails() {
        if (emails == null) {
            emails = "";
        }
        return emails;
    }

    public void setEmails(String emails) {
        this.emails = emails;
    }

    public String getTipoBloqueioCobrancaApresentar() {
        if (getTipoBloqueioCobrancaEnum() == null) {
            return "";
        } else {
            return getTipoBloqueioCobrancaEnum().getDescricao().toUpperCase();
        }
    }

    public TipoBloqueioCobrancaEnum getTipoBloqueioCobrancaEnum() {
        return tipoBloqueioCobrancaEnum;
    }

    public void setTipoBloqueioCobrancaEnum(TipoBloqueioCobrancaEnum tipoBloqueioCobrancaEnum) {
        this.tipoBloqueioCobrancaEnum = tipoBloqueioCobrancaEnum;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMsgResultado() {
        if (msgResultado == null) {
            msgResultado = "";
        }
        return msgResultado;
    }

    public void setMsgResultado(String msgResultado) {
        this.msgResultado = msgResultado;
    }
}

package negocio.comuns.financeiro;

import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.Date;

/**
 * Created by <PERSON> on 17/07/2023.
 */

public class PluggyConnectorDTO {

    protected String id;
    protected String idItem;
    protected PluggyItemVO pluggyItemVO;
    protected String name;
    protected String primaryColor;
    protected String institutionUrl;
    protected String country;
    protected String type;
    protected String imageUrl;
    protected PluggyConnectorHealthDTO health;
    protected Date createdAt;
    protected Date updatedAt;
    protected String statusApresentar = "";
    protected String createdAtApresentar = "";
    protected String updatedAtApresentar = "";
    protected String deviceNickname = ""; //quando pede autorização do dispositivo
    protected PluggyConnectorErrorDTO error;

    public PluggyConnectorDTO() {
        super();
        inicializarDados();
    }

    public PluggyConnectorDTO(JSONObject json) throws Exception {
        JSONObject connector = json.getJSONObject("connector");
        //receber objeto connector do json
        this.id = connector.optString("id", "");
        this.name = connector.optString("name", "");
        this.primaryColor = connector.optString("primaryColor", "");
        this.institutionUrl = connector.optString("institutionUrl", "");
        this.country = connector.optString("country", "");
        this.type = connector.optString("type", "");
        this.imageUrl = connector.optString("imageUrl", "");
        this.health = new PluggyConnectorHealthDTO(connector.getJSONObject("health"));
        this.statusApresentar = health.getStatus();

        if (json.optJSONObject("error") != null &&
                json.optJSONObject("error").optJSONObject("attributes") != null &&
                !UteisValidacao.emptyString(json.optJSONObject("error").optJSONObject("attributes").optString("deviceNicknamed"))) {
            this.deviceNickname = json.optJSONObject("error").optJSONObject("attributes").optString("deviceNickname");
        }
        if (
                json.optJSONObject("error") != null
                && !UteisValidacao.emptyString(json.optJSONObject("error").optString("code"))
                && !UteisValidacao.emptyString(json.optJSONObject("error").optString("message"))
        ) {
            this.error = new PluggyConnectorErrorDTO(json.getJSONObject("error"));
        }
    }

    public void inicializarDados() {
        this.id = "";
        this.name = "";
        this.primaryColor = "";
        this.institutionUrl = "";
        this.country = "";
        this.type = "";
        this.imageUrl = "";
        this.health = new PluggyConnectorHealthDTO();
        this.error = new PluggyConnectorErrorDTO();
        this.statusApresentar = "";
        this.createdAt = null;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrimaryColor() {
        return primaryColor;
    }

    public void setPrimaryColor(String primaryColor) {
        this.primaryColor = primaryColor;
    }

    public String getInstitutionUrl() {
        return institutionUrl;
    }

    public void setInstitutionUrl(String institutionUrl) {
        this.institutionUrl = institutionUrl;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public PluggyConnectorHealthDTO getHealth() {
        return health;
    }

    public void setHealth(PluggyConnectorHealthDTO health) {
        this.health = health;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedAtApresentar() {
        return createdAtApresentar;
    }

    public void setCreatedAtApresentar(String createdAtApresentar) {
        this.createdAtApresentar = createdAtApresentar;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedAtApresentar() {
        return updatedAtApresentar;
    }

    public void setUpdatedAtApresentar(String updatedAtApresentar) {
        this.updatedAtApresentar = updatedAtApresentar;
    }

    public String getStatusApresentar() {
        return statusApresentar;
    }

    public void setStatusApresentar(String statusApresentar) {
        this.statusApresentar = statusApresentar;
    }

    public String getIdItem() {
        return idItem;
    }

    public void setIdItem(String idItem) {
        this.idItem = idItem;
    }

    public String getDeviceNickname() {
        if (UteisValidacao.emptyString(deviceNickname)) {
            return "";
        }
        return deviceNickname;
    }

    public void setDeviceNickname(String deviceNickname) {
        this.deviceNickname = deviceNickname;
    }

    public PluggyConnectorErrorDTO getError() {
        return error;
    }

    public void setError(PluggyConnectorErrorDTO error) {
        this.error = error;
    }

    public PluggyItemVO getPluggyItemVO() {
        return pluggyItemVO;
    }

    public void setPluggyItemVO(PluggyItemVO pluggyItemVO) {
        this.pluggyItemVO = pluggyItemVO;
    }
}

package negocio.comuns.financeiro;

public class BIPlanoContaDTO {
    private PlanoContaTO planoContaTO;
    boolean selecionado = false;
    double valorTotal;

    public BIPlanoContaDTO() {
    }


    public BIPlanoContaDTO(PlanoContaTO planoContaTO) {
        this.planoContaTO = planoContaTO;
        this.selecionado = planoContaTO.isInsidirLTV();
    }

    public PlanoContaTO getPlanoContaTO() {
        if (planoContaTO == null)
            planoContaTO = new PlanoContaTO();
        return planoContaTO;
    }

    public void setPlanoContaTO(PlanoContaTO planoContaTO) {
        this.planoContaTO = planoContaTO;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(double valorTotal) {
        this.valorTotal = valorTotal;
    }
}

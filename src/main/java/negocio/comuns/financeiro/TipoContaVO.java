package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import java.util.ArrayList;
import java.util.List;

import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.crm.GenericoTO;

/**
 * Reponsável por manter os dados da entidade de tipos de contas
 */
public class TipoContaVO extends SuperVO {

	protected Integer codigo;
	protected String descricao;
	private ComportamentoConta comportamento;
	private Integer codigoComportamento;
	private List<ContaVO> contas = new ArrayList<ContaVO>();
        private List<GenericoTO> genericos = new ArrayList<GenericoTO>();
	/**
	 * Construtor padrão da classe <code>TipoContaVO</code>. Cria uma nova
	 * instância desta entidade, inicializando automaticamente seus atributos
	 * (Classe VO).
	 */
	public TipoContaVO() {
		super();
		inicializarDados();
	}

	/**
	 * Operação responsável por validar os dados de um objeto da classe
	 * <code>BancoVO</code>. Todos os tipos de consistência de dados são e devem
	 * ser implementadas neste método. São validações típicas: verificação de
	 * campos obrigatórios, verificação de valores válidos para os atributos.
	 * 
	 * @exception ConsistirExecption
	 *                Se uma inconsistência for encontrada aumaticamente é
	 *                gerada uma exceção descrevendo o atributo e o erro
	 *                ocorrido.
	 */
	public static void validarDados(TipoContaVO obj) throws ConsistirException {
		if (!obj.getValidarDados().booleanValue()) {
			return;
		}
                if(UteisValidacao.emptyString(obj.getDescricao())){
                        throw new ConsistirException("A descrição do tipo de conta deve ser informada");
                }
	}
        private Double valor;

        public Double getValor() {
            return valor;
        }

        public void setValor(Double valor) {
            this.valor = valor;
        }

        public String getValor_Apresentar() {
            return Formatador.formatarValorMonetarioSemMoeda(getValor());
        }
        public String getColor() {
            return valor < 0.0 ? "red" : "green";
        }

	/**
	 * Operação reponsável por inicializar os atributos da classe.
	 */
	public void inicializarDados() {
		setCodigo(new Integer(0));
		setDescricao("");
	}

	public Integer getCodigo() {
		return codigo;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	public String getDescricao() {
		return descricao;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public void setComportamento(ComportamentoConta comportamento) {
		this.comportamento = comportamento;
	}

	public ComportamentoConta getComportamento() {
		return comportamento;
	}

	public void setCodigoComportamento(Integer codigoComportamento) {
		this.codigoComportamento = codigoComportamento;
		comportamento = ComportamentoConta.getComportamentoConta(codigoComportamento);
	}

	public Integer getCodigoComportamento() {
		if(codigoComportamento == null && comportamento != null){
			codigoComportamento = comportamento.getCodigo();
		}
		return codigoComportamento;
	}
	public String getComportamentoApresentar(){
		return comportamento == null ? "" : comportamento.getDescricao();
	}

	public void setContas(List<ContaVO> contas) {
		this.contas = contas;
	}

	public List<ContaVO> getContas() {
		return contas;
	}

    public List<GenericoTO> getGenericos() {
        return genericos;
    }

    public void setGenericos(List<GenericoTO> genericos) {
        this.genericos = genericos;
    }

        

}
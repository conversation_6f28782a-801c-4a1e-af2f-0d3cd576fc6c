package negocio.comuns.financeiro;

/**
 *
 * Created by Paulo Junior on 13/02/2020.
 *
 */
public enum StatusAntifraudeGetNetEnum {

    NENHUM("NENHUM", ""),
    COD_FRAUD("FRAUD", "Transação negada por regra de segurança do sistema de Antifraude"),
    COD_200("200", "Transação negada por regra de segurança do sistema de Antifraude"),
    COD_250("250", "Transação negada por regra de segurança do sistema de Antifraude"),
    COD_0700("0700", "Transação negada por regra de segurança do sistema de Antifraude"),
    COD_2000("2000", "Transação negada por regra de segurança do sistema de Antifraude"),
    COD_480("480", "Transação negada por regra de segurança do sistema de Antifraude"),
    COD_481("481", "Transação negada por regra de segurança do sistema de Antifraude"),
    COD_0150("0150", "Transação enviada com o campo \"amount\" preenchido com o valor nulo ou inválido"),
    COD_0902("0902", "Transação enviada com o campo \"required\" preenchido com o valor nulo ou inválido"),
    COD_101("101", "Transação enviada com o campo \"required\" preenchido com o valor nulo ou inválido"),
    COD_102("102", "Transação enviada com o campo \"required\" preenchido com o valor nulo ou inválido"),
    COD_231("231", "Dados do cartão de crédito preenchidos incorretamente"),
    COD_0400("0400", "Dados do cartão de crédito preenchidos incorretamente"),
    COD_202("202", "Dados do cartão de crédito preenchidos incorretamente"),
    COD_400("400", "Emissor do cartão de crédito não aprovou a transação"),
    COD_0330("0330", "Emissor do cartão de crédito não aprovou a transação"),
    COD_0500("0500", "Emissor do cartão de crédito não aprovou a transação"),
    COD_0600("0600", "Emissor do cartão de crédito não aprovou a transação"),
    COD_1300("1300", "Emissor do cartão de crédito não aprovou a transação"),
    COD_0800("0800", "Emissor do cartão de crédito não aprovou a transação"),
    COD_234("234", "Indisponibilidade no sistema Antifraude . Contate a GetNet."),
    COD_0901("0901", "Indisponibilidade no sistema Antifraude . Contate a GetNet."),
    COD_0000("0000", "Indisponibilidade no sistema Antifraude . Contate a GetNet."),
    COD_0903("0903", "Indisponibilidade no sistema Antifraude . Contate a GetNet."),
    COD_150("150", "Indisponibilidade no sistema Antifraude . Contate a GetNet."),
    COD_151("151", "Indisponibilidade no sistema Antifraude . Contate a GetNet."),
    COD_121("121", "Indisponibilidade no sistema Antifraude . Contate a GetNet."),
    COD_500("500", "Indisponibilidade no sistema Antifraude . Contate a GetNet."),

    ;

    private String identificador;
    private String descricao;

    StatusAntifraudeGetNetEnum(String identificador, String descricao) {
        this.identificador = identificador;
        this.descricao = descricao;
    }

    public static StatusAntifraudeGetNetEnum valueOff(String identificador) {
        for (StatusAntifraudeGetNetEnum status : StatusAntifraudeGetNetEnum.values()) {
            if (status.getIdentificador().equals(identificador)) {
                return status;
            }
        }
        return StatusAntifraudeGetNetEnum.NENHUM;
    }

    public String getIdentificador() {
        return identificador;
    }

    public String getDescricao() {
        return descricao;
    }
}

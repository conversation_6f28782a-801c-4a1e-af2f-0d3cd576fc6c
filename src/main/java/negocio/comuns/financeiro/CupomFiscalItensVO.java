package negocio.comuns.financeiro;


import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;

/**
 * Classe de modelo para a tabela cupomfiscalitens
 * 
 * <AUTHOR>
 */
public class CupomFiscalItensVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected Double valorUnitario;
    protected Double valorTotal;
    protected Double valorDescontoOuAcrescimo = 0.0;
    protected Integer quantidade;

    @ChaveEstrangeira
    protected ProdutoVO produto;

    /**
     * Construtor padrão da classe <code>CupomFiscalItensVO</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente
     * seus atributos (Classe VO).
     */
    public CupomFiscalItensVO() {
        super();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>CupomFiscalItensVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas
     * neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação
     * de valores válidos para os atributos.
     *
     * @exception ConsistirExecption
     *                Se uma inconsistência for encontrada aumaticamente é
     *                gerada uma exceção descrevendo
     *                o atributo e o erro ocorrido.
     */
    public static void validarDados(CupomFiscalItensVO obj) throws ConsistirException {
        if (obj.getValorTotal().doubleValue() < 0) {
            throw new ConsistirException("O campo VALOR TOTAL (Cupom Fiscal) deve ser informado.");
        }

    }

    //////////
    //Getters and Setters
    //////////
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        //TODO codigo temporário para testes
        this.valorUnitario = valorUnitario;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        //codigo temporário para testes
        this.valorTotal = valorTotal;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public ProdutoVO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public Double getValorDescontoOuAcrescimo() {
        return valorDescontoOuAcrescimo;
    }

    public void setValorDescontoOuAcrescimo(Double valorDescontoOuAcrescimo) {
        this.valorDescontoOuAcrescimo = valorDescontoOuAcrescimo;
    }
}

package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 19/10/2021
 */
public class PessoaCPFTO extends SuperVO {

    private Integer pessoa;
    private String nome;
    private String cpf;
    private String nomeResponsavel;
    private String cpfResponsavel;
    private Integer pessoaResponsavel;

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        if (cpf == null) {
            cpf = "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getNomeResponsavel() {
        if (nomeResponsavel == null) {
            nomeResponsavel = "";
        }
        return nomeResponsavel;
    }

    public void setNomeResponsavel(String nomeResponsavel) {
        this.nomeResponsavel = nomeResponsavel;
    }

    public String getCpfResponsavel() {
        if (cpfResponsavel == null) {
            cpfResponsavel = "";
        }
        return cpfResponsavel;
    }

    public void setCpfResponsavel(String cpfResponsavel) {
        this.cpfResponsavel = cpfResponsavel;
    }

    public Integer getPessoaResponsavel() {
        if (pessoaResponsavel == null) {
            pessoaResponsavel = 0;
        }
        return pessoaResponsavel;
    }

    public void setPessoaResponsavel(Integer pessoaResponsavel) {
        this.pessoaResponsavel = pessoaResponsavel;
    }
}

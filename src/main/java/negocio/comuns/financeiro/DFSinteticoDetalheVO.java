/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class DFSinteticoDetalheVO extends SuperVO {
    
    private Integer empresa;
    private Date dataMes;
    private String dados;

    public DFSinteticoDetalheVO(Integer empresa, Date dataMes, String dados) {
        this.empresa = empresa;
        this.dataMes = dataMes;
        this.dados = dados;
    }

    public DFSinteticoDetalheVO() {
    }

    
    
    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getDataMes() {
        return dataMes;
    }

    public void setDataMes(Date dataMes) {
        this.dataMes = dataMes;
    }

    public String getDados() {
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }
    
    
}

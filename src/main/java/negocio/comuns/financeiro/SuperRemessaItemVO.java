package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.jboleto.JBoleto;
import servicos.impl.boleto.sicredi.SicrediCNAB400StatusEnum;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.bb.BBCNAB240StatusEnum;
import servicos.impl.dcc.bb.DCOBBStatusEnum;
import servicos.impl.dcc.bin.DCCBinStatusEnum;
import servicos.impl.dcc.bnb.Cnab400BNBStatusEnum;
import servicos.impl.dcc.bnb.ErrosBNBEnum;
import servicos.impl.dcc.bradesco.DCOBradescoOcorRejeitadoEnum;
import servicos.impl.dcc.bradesco.DCOBradescoOcorrenciaEnum;
import servicos.impl.dcc.caixa.CaixaCNAB240StatusEnum;
import servicos.impl.dcc.caixa.DCOCaixaStatusEnum;
import servicos.impl.dcc.caixa.DCOCaixaStatusSIACC150Enum;
import servicos.impl.dcc.cielo.DCCCieloStatusEnum;
import servicos.impl.dcc.getnet.DCOGetNetStatusEnum;
import servicos.impl.dcc.hsbc.DCOHSBCStatusEnum;
import servicos.impl.dcc.itau.BBCnab400ItauStatusEnum;
import servicos.impl.dcc.itau.DCOItauStatusEnum;
import servicos.impl.dcc.santander.Cnab400SantanderStatusEnum;
import servicos.impl.dcc.sicoob.Cnab400SicoobStatusEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by GlaucoT on 18/04/2016
 */
public abstract class SuperRemessaItemVO extends SuperVO {

    @ChaveEstrangeira
    protected RemessaVO remessa;
    protected Map<String, String> props = new HashMap<String, String>();

    public abstract TipoRemessaEnum getTipo();

    public abstract void setTipo(TipoRemessaEnum tipo);

    public abstract boolean isLayoutFebrabanDCO();

    public RemessaVO getRemessa() {
        if (remessa == null) {
            remessa = new RemessaVO();
        }
        return remessa;
    }

    public void setRemessa(RemessaVO remessa) {
        this.remessa = remessa;
    }

    public String get(String key) {
        return props.get(key);
    }

    public String remove(String key) {
        return props.remove(key);
    }

    public Map<String, String> getProps() {
        if (props == null) {
            props = new HashMap<String, String>();
        }
        return props;
    }

    public void setProps(Map<String, String> props) {
        this.props = props;
    }

    public String getCodigoStatus() {
        if (remessa == null || getTipo() == null) {
            return "";
        }
        String retorno;
        if (getTipo().equals(TipoRemessaEnum.GET_NET)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                retorno = DCOGetNetStatusEnum.valueOff(codStatus).getId();
                return (!codStatus.equals("NENHUM") && retorno.equals("NENHUM")) ? codStatus : retorno;
            } else {
                return DCOGetNetStatusEnum.StatusNENHUM.getId();
            }
        } else if (getTipo().equals(TipoRemessaEnum.EDI_CIELO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                retorno = DCCCieloStatusEnum.valueOff(codStatus).getId();
                return (!codStatus.equals("NENHUM") && retorno.equals("NENHUM")) ? codStatus : retorno;
            } else {
                return DCCCieloStatusEnum.StatusNENHUM.getId();
            }
        } else if (isLayoutFebrabanDCO()) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                retorno = DCOBBStatusEnum.valueOff(codStatus).getId();
                return (!codStatus.equals("NENHUM") && retorno.equals("NENHUM")) ? codStatus : retorno;
            } else {
                return DCOBBStatusEnum.StatusNENHUM.getId();
            }
        } else if (getTipo().equals(TipoRemessaEnum.BRADESCO_DCO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                retorno = DCOBradescoOcorrenciaEnum.valueOff(codStatus).getId();
                return (!codStatus.equals("NENHUM") && retorno.equals("NENHUM")) ? codStatus : retorno;
            } else {
                return DCOBradescoOcorrenciaEnum.OcorNENHUM.getId();
            }
        } else if (getTipo().equals(TipoRemessaEnum.HSBC_DCO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                retorno = DCOHSBCStatusEnum.valueOff(codStatus).getId();
                return (!codStatus.equals("NENHUM") && retorno.equals("NENHUM")) ? codStatus : retorno;
            } else {
                return DCOHSBCStatusEnum.StatusNENHUM.getId();
            }
        } else if (getTipo().equals(TipoRemessaEnum.ITAU_DCO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                retorno = DCOItauStatusEnum.valueOff(codStatus).getId();
                return (!codStatus.equals("NENHUM") && retorno.equals("NENHUM")) ? codStatus : retorno;
            } else {
                return DCOCaixaStatusEnum.StatusNENHUM.getId();
            }
        } else if (getTipo().equals(TipoRemessaEnum.ITAU_BOLETO) || getTipo().equals(TipoRemessaEnum.DAYCOVAL_BOLETO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                retorno = BBCnab400ItauStatusEnum.valueOff(codStatus).getId();
                return (!codStatus.equals("NENHUM") && retorno.equals("NENHUM")) ? codStatus : retorno;
            } else {
                return BBCnab400ItauStatusEnum.StatusNENHUM.getId();
            }
        } else if (getTipo().equals(TipoRemessaEnum.CAIXA_DCO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                retorno = DCOCaixaStatusSIACC150Enum.valueOff(codStatus).getId();
                return (!codStatus.equals("NENHUM") && retorno.equals("NENHUM")) ? codStatus : retorno;
            } else {
                return DCOCaixaStatusSIACC150Enum.StatusNENHUM.getId();
            }
        } else if (getTipo().equals(TipoRemessaEnum.CAIXA_SICOV_DCO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                retorno = DCOCaixaStatusEnum.valueOff(codStatus).getId();
                return (!codStatus.equals("NENHUM") && retorno.equals("NENHUM")) ? codStatus : retorno;
            } else {
                return DCOCaixaStatusEnum.StatusNENHUM.getId();
            }
        } else if (getTipo().equals(TipoRemessaEnum.DCC_BIN)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                retorno = DCCBinStatusEnum.valueOff(codStatus).getId();
                return (!codStatus.equals("NENHUM") && retorno.equals("NENHUM")) ? codStatus : retorno;
            } else {
                return DCCCieloStatusEnum.StatusNENHUM.getId();
            }
        } else if (getTipo().equals(TipoRemessaEnum.BOLETO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (UteisValidacao.emptyString(codStatus)) {
                return "";
            }

            switch (remessa.getConvenioCobranca().getBanco().getCodigoBanco()) {
                case JBoleto.BRADESCO:
                    return DCOBradescoOcorrenciaEnum.valueOff(codStatus).getId();
                case JBoleto.ITAU:
                    return BBCnab400ItauStatusEnum.valueOff(codStatus).getId();
                case JBoleto.SANTANDER:
                    return Cnab400SantanderStatusEnum.valueOff(codStatus).getId();
                case JBoleto.BANCOOB:
                    return Cnab400SicoobStatusEnum.valueOff(codStatus).getId();
                case JBoleto.BNB:
                    return Cnab400BNBStatusEnum.valueOff(codStatus).getId();
                case JBoleto.BANCO_DO_BRASIL:
                    return BBCNAB240StatusEnum.valueOff(codStatus).getId();
                case JBoleto.CAIXA_ECONOMICA:
                    return CaixaCNAB240StatusEnum.valueOff(codStatus).getId();
                case JBoleto.SICREDI:
                    return SicrediCNAB400StatusEnum.valueOff(codStatus).getId();
                default:
                    return "";
            }
        } else {
            return "";
        }
    }

    public String getDescricaoStatus() {
        if (getTipo().equals(TipoRemessaEnum.GET_NET)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                try {
                    return DCOGetNetStatusEnum.valueOff(codStatus).getDescricao();
                } catch (Exception e) {
                    return codStatus;
                }
            } else {
                return DCOGetNetStatusEnum.StatusNENHUM.getDescricao();
            }
        } else if (getTipo().equals(TipoRemessaEnum.EDI_CIELO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                try {
                    DCCCieloStatusEnum enumCielo = DCCCieloStatusEnum.valueOff(codStatus);
                    if (enumCielo != null && enumCielo.equals(DCCCieloStatusEnum.Status99)) {
                        return "<div style=\"color:red;font-weight: bold\">Atenção:</div><br/>Outros Motivos - Verificar junto a Cielo se foi cobrado e dar baixa manualmente";
                    } else {
                        return enumCielo.getDescricao();
                    }
                } catch (Exception e) {
                    return codStatus;
                }
            } else {
                return DCCCieloStatusEnum.StatusNENHUM.getDescricao();
            }
        } else if (getTipo().equals(TipoRemessaEnum.BRADESCO_DCO)) {
            StringBuilder retorno = new StringBuilder();
            String codigoErro = "";
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                try {
                    retorno.append(DCOBradescoOcorrenciaEnum.valueOf("Ocor" + codStatus).getDescricao()).append("\n");

                    if (!codStatus.equals("06")) {
                         codigoErro = get(DCCAttEnum.Motivo.name());
                        if (!UteisValidacao.emptyString(codigoErro)) {
                            for (int i = 0; i < codigoErro.length() - 1; i = i + 2) {
                                String codErroParcial = codigoErro.substring(i, i + 2);
                                if (!codErroParcial.equals("00")) {
                                    retorno.append(codErroParcial).append(" (").append(DCOBradescoOcorRejeitadoEnum.valueOff(codErroParcial).getDescricao()).append(") |\n");
                                }
                            }
                        }
                    }

                } catch (Exception e) {
                    return codStatus;
                }
                return retorno.toString();
            }
            return DCOBradescoOcorrenciaEnum.OcorNENHUM.getDescricao();
        } else if (isLayoutFebrabanDCO()) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                try {
                    return DCOBBStatusEnum.valueOf("Status" + codStatus).getDescricao();
                } catch (Exception e) {
                    return codStatus;
                }
            } else {
                return DCOBBStatusEnum.StatusNENHUM.getDescricao();
            }
        } else if (getTipo().equals(TipoRemessaEnum.HSBC_DCO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                try {
                    return DCOHSBCStatusEnum.valueOf("Status" + codStatus).getDescricao();
                } catch (Exception e) {
                    return codStatus;
                }
            } else {
                return DCOHSBCStatusEnum.StatusNENHUM.getDescricao();
            }
        } else if (getTipo().equals(TipoRemessaEnum.ITAU_DCO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                try {
                    return DCOItauStatusEnum.valueOf("Status" + codStatus).getDescricao();
                } catch (Exception e) {
                    return codStatus;
                }
            } else {
                return DCOItauStatusEnum.StatusNENHUM.getDescricao();
            }
        } else if (getTipo().equals(TipoRemessaEnum.ITAU_BOLETO) || getTipo().equals(TipoRemessaEnum.DAYCOVAL_BOLETO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                try {
                    return BBCnab400ItauStatusEnum.valueOf("Status" + codStatus).getDescricao();
                } catch (Exception e) {
                    return codStatus;
                }
            } else {
                return BBCnab400ItauStatusEnum.StatusNENHUM.getDescricao();
            }
        } else if (getTipo().equals(TipoRemessaEnum.CAIXA_DCO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                try {
                    return DCOCaixaStatusSIACC150Enum.valueOff(codStatus).getDescricao();
                } catch (Exception e) {
                    return codStatus;
                }
            } else {
                return DCOCaixaStatusSIACC150Enum.StatusNENHUM.getDescricao();
            }
        } else if (getTipo().equals(TipoRemessaEnum.CAIXA_SICOV_DCO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                try {
                    return DCOCaixaStatusEnum.valueOf("Status" + codStatus).getDescricao();
                } catch (Exception e) {
                    return codStatus;
                }
            } else {
                return DCOCaixaStatusEnum.StatusNENHUM.getDescricao();
            }
        } else if (getTipo().equals(TipoRemessaEnum.BOLETO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (UteisValidacao.emptyString(codStatus)) {
                return "Nenhum";
            }

            switch (remessa.getConvenioCobranca().getBanco().getCodigoBanco()) {
                case JBoleto.BRADESCO:
                    return DCOBradescoOcorrenciaEnum.valueOff(codStatus).getDescricao();
                case JBoleto.ITAU:
                    return BBCnab400ItauStatusEnum.valueOff(codStatus).getDescricao();
                case JBoleto.SANTANDER:
                    return Cnab400SantanderStatusEnum.valueOff(codStatus).getDescricao();
                case JBoleto.BANCOOB:
                    return Cnab400SicoobStatusEnum.valueOff(codStatus).getDescricao();
                case JBoleto.BNB:
                    String motivo = "";
                    if (!codStatus.equals("06")) {
                        String codigoErro = get(DCCAttEnum.CodigoErro.name());
                        if (!UteisValidacao.emptyString(codigoErro)) {
                            int posicaoErro = codigoErro.indexOf("1");
                            if (posicaoErro >= 0) {
                                posicaoErro = posicaoErro + 1;
                                motivo = ErrosBNBEnum.valueOff(posicaoErro).getDescricao();
                            }
                        }
                    }

                    String retorno = Cnab400BNBStatusEnum.valueOff(codStatus).getDescricao();
                    if (!UteisValidacao.emptyString(motivo)) {
                        retorno = retorno + " (" + motivo + ")";
                    }

                    return retorno;
                case JBoleto.BANCO_DO_BRASIL:
                    return BBCNAB240StatusEnum.valueOff(codStatus).getDescricao();
                case JBoleto.CAIXA_ECONOMICA:
                    return CaixaCNAB240StatusEnum.valueOff(codStatus).getDescricao();
                case JBoleto.SICREDI:
                    return SicrediCNAB400StatusEnum.valueOff(codStatus).getDescricao();
                default:
                    return "";
            }
        } else if (getTipo().equals(TipoRemessaEnum.DCC_BIN)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                if (codStatus.equalsIgnoreCase("FZ")) {
                    return "Transação Não Autorizada";
                }
                try {
                    return DCCBinStatusEnum.valueOff(codStatus).getDescricao();
                } catch (Exception e) {
                    return codStatus;
                }
            } else {
                return DCOGetNetStatusEnum.StatusNENHUM.getDescricao();
            }
        }  else {
            return "";
        }
    }

    public String getAcaoRealizar() {
        if (getTipo().equals(TipoRemessaEnum.EDI_CIELO)) {
            String codStatus = get(DCCAttEnum.StatusVenda.name());
            if (codStatus != null && !codStatus.trim().isEmpty()) {
                try {
                    return DCCCieloStatusEnum.valueOff(codStatus).getAcaoRealizar();
                } catch (Exception e) {
                    return codStatus;
                }
            } else {
                return DCCCieloStatusEnum.StatusNENHUM.getAcaoRealizar();
            }
        } else {
            return "";
        }
    }
}

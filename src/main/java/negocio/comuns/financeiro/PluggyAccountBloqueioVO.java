package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

/**
 * Created by <PERSON> on 10/04/2024.
 */

public class PluggyAccountBloqueioVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String id;
    protected String pluggyItem;


    public PluggyAccountBloqueioVO() {
        super();
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPluggyItem() {
        return pluggyItem;
    }

    public void setPluggyItem(String pluggyItem) {
        this.pluggyItem = pluggyItem;
    }
}

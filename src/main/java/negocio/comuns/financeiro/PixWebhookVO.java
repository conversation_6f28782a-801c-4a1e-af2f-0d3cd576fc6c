package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 * Created by <PERSON> on 21/11/2023.
 */

public class PixWebhookVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Date dataRegistro;
    protected int tipoConveio;
    protected String dados;
    protected int codigoPixWebhookOamd;


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public int getTipoConveio() {
        return tipoConveio;
    }

    public void setTipoConveio(int tipoConveio) {
        this.tipoConveio = tipoConveio;
    }

    public String getDados() {
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }

    public int getCodigoPixWebhookOamd() {
        if (UteisValidacao.emptyNumber(codigoPixWebhookOamd)) {
            return 0;
        }
        return codigoPixWebhookOamd;
    }

    public void setCodigoPixWebhookOamd(int codigoPixWebhookOamd) {
        this.codigoPixWebhookOamd = codigoPixWebhookOamd;
    }

    @Override
    public String toString() {
        return "PixWebhookVO{" +
                "codigo=" + codigo +
                ", dataRegistro=" + dataRegistro +
                ", tipoConveio=" + tipoConveio +
                ", dados='" + dados + '\'' +
                ", codigoPixWebhookOamd=" + codigoPixWebhookOamd +
                '}';
    }
}

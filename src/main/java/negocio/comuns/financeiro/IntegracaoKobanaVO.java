package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import org.json.JSONObject;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 21/06/2024.
 */

public class IntegracaoKobanaVO extends SuperVO {

    @ChavePrimaria
    protected Integer ambiente;
    protected Integer codigo;
    protected Integer empresa;
    protected String email;
    protected String business_cnpj;
    protected String nickname;
    protected String business_legal_name;
    protected boolean ativo;
    protected String api_access_token;
    protected Integer id;
    protected Date created_At;

    private List<FinancialAccountKobanaVO> financialAccountsKobanaVO;


    public IntegracaoKobanaVO() {
        super();
    }

    public IntegracaoKobanaVO(int empresa, JSONObject dados) {
        this.empresa = empresa;
        this.email = dados.getString("email");
        this.business_cnpj = dados.getString("business_cnpj");
        this.nickname = dados.getString("nickname");
        this.business_legal_name = dados.getString("business_legal_name");
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getBusiness_cnpj() {
        return business_cnpj;
    }

    public void setBusiness_cnpj(String business_cnpj) {
        this.business_cnpj = business_cnpj;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getBusiness_legal_name() {
        return business_legal_name;
    }

    public void setBusiness_legal_name(String business_legal_name) {
        this.business_legal_name = business_legal_name;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public String getApi_access_token() {
        return api_access_token;
    }

    public void setApi_access_token(String api_access_token) {
        this.api_access_token = api_access_token;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getCreated_At() {
        return created_At;
    }

    public void setCreated_At(Date created_At) {
        this.created_At = created_At;
    }

    public List<FinancialAccountKobanaVO> getFinancialAccountsKobanaVO() {
        return financialAccountsKobanaVO;
    }

    public void setFinancialAccountsKobanaVO(List<FinancialAccountKobanaVO> financialAccountsKobanaVO) {
        this.financialAccountsKobanaVO = financialAccountsKobanaVO;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public String getAmbiente_Apresentar() {
        if (getAmbiente() == null) {
            return "NENHUM";
        }
        return ambiente == 1 ? "PRODUÇÃO" : "HOMOLOGAÇÃO / SANDBOX";
    }
}

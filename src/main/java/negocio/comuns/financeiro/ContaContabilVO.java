package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * Created by ulisses on 08/02/2017.
 */
public class ContaContabilVO extends SuperVO {

    private Integer codigo;
    private String descricao;
    private Integer codigoIntegracao;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigoIntegracao() {
        return codigoIntegracao;
    }

    public void setCodigoIntegracao(Integer codigoIntegracao) {
        this.codigoIntegracao = codigoIntegracao;
    }


    public static void validarDados(ContaContabilVO obj)throws Exception{
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo Descrição (contaContábil) deve ser informado.");
        }
    }
}

package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.util.Formatador;

import java.util.ArrayList;
import java.util.List;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 *
 * <AUTHOR>
 */
public class MetaFinanceiraEmpresaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    @ChaveEstrangeira
    private EmpresaVO empresa = new EmpresaVO();
    private int ano = 0;
    private Mes mes = Mes.VAZIO;
    private String descricao = "";
    //aqui não vou gerar o log pelo sistema, pois será feito de uma forma própria para essas listas no método gerarLogAlteracoes desta classe
    @NaoControlarLogAlteracao
    private List<MetaFinanceiraEmpresaValoresVO> valores = new ArrayList<MetaFinanceiraEmpresaValoresVO>();
    @NaoControlarLogAlteracao
    private List<MetaFinanceiraConsultorVO> consultores = new ArrayList<MetaFinanceiraConsultorVO>();
    @NaoControlarLogAlteracao
    private String cor = "";
    @NaoControlarLogAlteracao
    private String corTexto = "";
    @NaoControlarLogAlteracao
    private double metaAtingida = 0.0;
    private String observacao;
    private Integer codigoMes;
    @NaoControlarLogAlteracao
    private List<MetaFinanceiraEmpresaValoresVO> valoresBackUp = new ArrayList<MetaFinanceiraEmpresaValoresVO>();

    private Double receitaVeloc = null;
    private Double faturamentoVeloc = null;
    private Double despesaVeloc = null;

    @NaoControlarLogAlteracao
    private boolean bateuTodosMetas;
    @NaoControlarLogAlteracao
    private String corMetaAtingida;

    public Double getDespesaVeloc() {
        return despesaVeloc;
    }

    public void setDespesaVeloc(Double despesaVeloc) {
        this.despesaVeloc = despesaVeloc;
    }

    public Double getFaturamentoVeloc() {
        return faturamentoVeloc;
    }

    public void setFaturamentoVeloc(Double faturamentoVeloc) {
        this.faturamentoVeloc = faturamentoVeloc;
    }

    public Double getReceitaVeloc() {
        return receitaVeloc;
    }

    public void setReceitaVeloc(Double receitaVeloc) {
        this.receitaVeloc = receitaVeloc;
    }

    public String getMetaAtingida_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(this.metaAtingida);
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public String getMes_Apresentar() {
        return getMes().getDescricao();
    }
    public String getAnoMes(){
        return getAno() + "/" + getCodigoMes();
    }
    public String getValor0() {
        try {
            return empresa.getMoeda() + " " + Formatador.formatarValorMonetarioSemMoeda(getValores().get(0).getValor());
        } catch (Exception e) {
            return "";
        }

    }

    public String getValor1() {
        try {
            return empresa.getMoeda() + " " + Formatador.formatarValorMonetarioSemMoeda(getValores().get(1).getValor());
        } catch (Exception e) {
            return "";
        }

    }

    public String getValor2() {
        try {
            return empresa.getMoeda() + " " + Formatador.formatarValorMonetarioSemMoeda(getValores().get(2).getValor());
        } catch (Exception e) {
            return "";
        }

    }

    public String getValor3() {
        try {
            return empresa.getMoeda() + " " + Formatador.formatarValorMonetarioSemMoeda(getValores().get(3).getValor());
        } catch (Exception e) {
            return "";
        }

    }

    public String getValor4() {
        try {
            return empresa.getMoeda() + " " + Formatador.formatarValorMonetarioSemMoeda(getValores().get(4).getValor());
        } catch (Exception e) {
            return "";
        }

    }

    public void validarDados() throws Exception {
        if (!validarDados) {
            return;
        }
        if (empresa == null || empresa.getCodigo().intValue() == 0) {
            throw new Exception("Informe a empresa.");
        }
        if (ano < (Uteis.getAnoData(Calendario.hoje())) - 10){
            throw new Exception("Ano informado inválido ou é 10 anos anterior ao ano atual.");
        }
        if (mes.equals(Mes.VAZIO)) {
            throw new Exception("Selecione um mês válido.");
        }
        if (descricao.trim().isEmpty()) {
            throw new Exception("Informe uma descrição.");
        }
        if (metasNaoValidas()) {
            throw new Exception("Informe pelo menos uma meta.");
        }
    }

    private boolean metasNaoValidas() {
        int metasValidas = 0;
        for (MetaFinanceiraEmpresaValoresVO mfv : valores) {
            metasValidas += (mfv.isEmpty() ? 0 : 1);
        }
        return metasValidas == 0;
    }

    public void realizarUpperCaseDados() {
        descricao = descricao.toUpperCase();
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public int getAno() {
        return ano;
    }

    public void setAno(int ano) {
        this.ano = ano;
    }

    public Mes getMes() {
        return mes;
    }

    public String getMesAno() {
        String mesAno = "";
        if (mes != null) {
            mesAno += this.mes.getCodigo();
            mesAno = mesAno.length() == 1 ? "0" + mesAno : mesAno;
            mesAno += "/" + this.getAno();
        }
        return mesAno;

    }

    public void setMes(Mes mes) {
        this.mes = mes;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<MetaFinanceiraEmpresaValoresVO> getValores() {
        return valores;
    }

    public MetaFinanceiraEmpresaValoresVO getListaValor0() {
        if (getValores().size() <= 0) {
            getValores().add(new MetaFinanceiraEmpresaValoresVO("#BF2A12"));
        }
        return getValores().get(0);
    }
    public MetaFinanceiraEmpresaValoresVO getListaValor1() {
        if (getValores().size() <= 1) {
            getValores().add(new MetaFinanceiraEmpresaValoresVO("#FF6E00"));
        }
        return getValores().get(1);
    }

    public MetaFinanceiraEmpresaValoresVO getListaValor2() {
        if (getValores().size() <= 2) {
            getValores().add(new MetaFinanceiraEmpresaValoresVO("#65B02A"));
        }
        return getValores().get(2);
    }

    public MetaFinanceiraEmpresaValoresVO getListaValor3() {
        if (getValores().size() <= 3) {
            getValores().add(new MetaFinanceiraEmpresaValoresVO("#25A24F"));
        }
        return getValores().get(3);
    }

    public MetaFinanceiraEmpresaValoresVO getListaValor4() {
        if (getValores().size() <= 4) {
            getValores().add(new MetaFinanceiraEmpresaValoresVO("#002C74"));
        }
        return getValores().get(4);
    }

    public void setValores(List<MetaFinanceiraEmpresaValoresVO> valores) {
        this.valores = valores;
    }

    public List<MetaFinanceiraConsultorVO> getConsultores() {
        return consultores;
    }

    public void setConsultores(List<MetaFinanceiraConsultorVO> consultores) {
        this.consultores = consultores;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public double getMetaAtingida() {
        return metaAtingida;
    }

    public void setMetaAtingida(double metaAtingida) {
        this.metaAtingida = metaAtingida;
        preparaCorPelaMetaAtingida();
    }

    public void preparaCorPelaMetaAtingida() {
        for (MetaFinanceiraEmpresaValoresVO mf : valores) {
            if (!mf.isEmpty() && metaAtingida >= mf.getValor()) {
                cor = mf.getCor();
            }
        }
    }

    public JRDataSource getListaValoresMeta() {
        JRDataSource jr = new JRBeanCollectionDataSource(getValores());
        return jr;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setCorTexto(String corTexto) {
        this.corTexto = corTexto;
    }

    public String getCorTexto() {
        return corTexto;
    }

    // João Alcides: o modelo de geração de log do sistema não gera de forma simples o log das entidades
    // que possuem uma complexidade maior, como o MetaFinanceiroVO e suas dependências.
    // criei método próprio de gerar o log de alterações dessas entidades.
    public List<LogVO> gerarLogAlteracoes(UsuarioVO responsavel, String operacao) throws Exception {
        List<LogVO> logs = new ArrayList<LogVO>();
        for (MetaFinanceiraEmpresaValoresVO valor : getValores()) {
            logs.addAll(valor.montarLogAlteracao(this.getCodigo(), responsavel, operacao));
        }

        for (MetaFinanceiraConsultorVO consultor : getConsultores()) {
            logs.addAll(consultor.montarLogAlteracao(this.getCodigo(), responsavel, operacao));
        }
        return logs;
    }

    public void registrarObjetoVOAntesDaAlteracao() {
        try {
            objetoVOAntesAlteracao = getClone(true);
            for (MetaFinanceiraEmpresaValoresVO valor : getValores()) {
                valor.setObjetoVOAntesAlteracao(valor.getClone(false));
            }
            for (MetaFinanceiraConsultorVO consultor : getConsultores()) {
                consultor.setObjetoVOAntesAlteracao(consultor.getClone(false));
            }
        } catch (Exception e) {
            objetoVOAntesAlteracao = null;
        }
    }

    public void inicializarObjetosAntesDaAlteracao() {
        objetoVOAntesAlteracao = new MetaFinanceiraEmpresaVO();
        for (MetaFinanceiraEmpresaValoresVO valor : getValores()) {
            valor.setObjetoVOAntesAlteracao(new MetaFinanceiraEmpresaValoresVO());
        }
        for (MetaFinanceiraConsultorVO consultor : getConsultores()) {
            consultor.setObjetoVOAntesAlteracao(new MetaFinanceiraConsultorVO());
        }
    }

    public void setValoresBackUp(List<MetaFinanceiraEmpresaValoresVO> valoresBackUp) {
        this.valoresBackUp = valoresBackUp;
    }

    public List<MetaFinanceiraEmpresaValoresVO> getValoresBackUp() {
        return valoresBackUp;
    }

    public void restaurarValores() throws Exception {
        valores = new ArrayList<MetaFinanceiraEmpresaValoresVO>();
        for (MetaFinanceiraEmpresaValoresVO valor : valoresBackUp) {
            valores.add((MetaFinanceiraEmpresaValoresVO) valor.getClone(true));
        }
    }
    
    public void backUpValores() throws Exception {
        valoresBackUp = new ArrayList<MetaFinanceiraEmpresaValoresVO>();
        for (MetaFinanceiraEmpresaValoresVO valor : valores) {
            valoresBackUp.add((MetaFinanceiraEmpresaValoresVO) valor.getClone(true));
        }
    }

    public boolean isBateuTodosMetas() {
        return bateuTodosMetas;
    }

    public void setBateuTodosMetas(boolean bateuTodosMetas) {
        this.bateuTodosMetas = bateuTodosMetas;
    }

    public String getCorMetaAtingida() {
        return corMetaAtingida;
    }

    public void setCorMetaAtingida(String corMetaAtingida) {
        this.corMetaAtingida = corMetaAtingida;
    }

    public Integer getCodigoMes() {
        return codigoMes;
    }

    public void setCodigoMes(Integer codigoMes) {
        this.codigoMes = codigoMes;
    }

}

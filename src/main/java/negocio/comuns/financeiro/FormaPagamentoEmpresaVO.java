package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;

import java.util.ArrayList;
import java.util.List;

public class FormaPagamentoEmpresaVO  extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    @ChaveEstrangeira
    private FormaPagamentoVO formaPagamento = new FormaPagamentoVO();
    @ChaveEstrangeira
    private EmpresaVO empresa = new EmpresaVO();
    private List<TaxaCartaoVO> taxas = new ArrayList<TaxaCartaoVO>();
    @ChaveEstrangeira
    private ContaCorrenteVO contaDestino;

    public List<TaxaCartaoVO> getTaxas() {
        return taxas;
    }

    public void setTaxas(List<TaxaCartaoVO> taxas) {
        this.taxas = taxas;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public FormaPagamentoVO getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamentoVO formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public ContaCorrenteVO getContaDestino() {
        if (contaDestino == null) {
            contaDestino = new ContaCorrenteVO();
        }
        return contaDestino;
    }

    public void setContaDestino(ContaCorrenteVO contaDestino) {
        this.contaDestino = contaDestino;
    }
}

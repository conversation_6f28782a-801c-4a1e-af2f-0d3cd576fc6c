package negocio.comuns.financeiro;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade TipoRetorno. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
*/
public class TipoRetornoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected String arquivoLayoutRetorno;
	
    /**
     * Construtor padrão da classe <code>TipoRetorno</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public TipoRetornoVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>TipoRetornoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(TipoRetornoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
        if (obj.getDescricao().equals("")) { 
            throw new ConsistirException("O campo DESCRIÇÃO (Tipo de Retorno) deve ser informado.");
        }
        if (obj.getArquivoLayoutRetorno().equals("")) { 
            throw new ConsistirException("O campo ARQUIVO DE LAYOUT DO RETORNO (Tipo de Retorno) deve ser informado.");
        }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
        setDescricao( getDescricao().toUpperCase() );
        setArquivoLayoutRetorno( getArquivoLayoutRetorno().toUpperCase() );
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setDescricao( "" );
        setArquivoLayoutRetorno( "" );
    }
	

    public String getArquivoLayoutRetorno() {
          if (arquivoLayoutRetorno== null) {
            arquivoLayoutRetorno = "";
        }
        return (arquivoLayoutRetorno);
    }
     
    public void setArquivoLayoutRetorno( String arquivoLayoutRetorno ) {
        this.arquivoLayoutRetorno = arquivoLayoutRetorno;
    }

    public String getDescricao() {
          if (descricao== null) {
            descricao = "";
        }
        return (descricao);
    }
     
    public void setDescricao( String descricao ) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }
}
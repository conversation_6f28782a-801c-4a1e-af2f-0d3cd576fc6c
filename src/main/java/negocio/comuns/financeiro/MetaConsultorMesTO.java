package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;

/**
 * Created by ulisses on 30/09/2015.
 */
public class MetaConsultorMesTO extends SuperTO {

    private Date mes;
    private Integer codigoColaborador;
    private Double totalMetaAtingida =0.0;
    private MetaFinanceiraEmpresaValoresVO metaFinanceiraEmpresaValoresVO;


    public MetaConsultorMesTO(){}

    public MetaConsultorMesTO(Date mes, Integer codigoColaborador){
        this.mes = mes;
        this.codigoColaborador = codigoColaborador;
    }
    public Date getMes() {
        return mes;
    }

    public void setMes(Date mes) {
        this.mes = mes;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public Double getTotalMetaAtingida() {
        return totalMetaAtingida;
    }

    public void setTotalMetaAtingida(Double totalMetaAtingida) {
        this.totalMetaAtingida = totalMetaAtingida;
    }

    public MetaFinanceiraEmpresaValoresVO getMetaFinanceiraEmpresaValoresVO() {
        return metaFinanceiraEmpresaValoresVO;
    }

    public void setMetaFinanceiraEmpresaValoresVO(MetaFinanceiraEmpresaValoresVO metaFinanceiraEmpresaValoresVO) {
        this.metaFinanceiraEmpresaValoresVO = metaFinanceiraEmpresaValoresVO;
    }

    @Override
    public int hashCode(){
        return this.mes.hashCode();
    }

    @Override
    public boolean equals(Object obj){
        if ((obj == null) || (!(obj instanceof MetaConsultorMesTO)))
            return false;
        return ((MetaConsultorMesTO) obj).getMes().equals(this.mes) &&
                ((MetaConsultorMesTO) obj).getCodigoColaborador().equals(this.codigoColaborador);

    }
}

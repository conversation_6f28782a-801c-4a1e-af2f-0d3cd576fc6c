package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.io.Serializable;
import java.math.BigDecimal;

/*
 * Created by <PERSON><PERSON> on 24/05/2018.
 */
public class InadimplenciaGraficoTO implements Serializable{

    private String mes;
    private BigDecimal valorPago;
    private BigDecimal valorEmAberto;
    private Double inadimplencia;
    private BigDecimal valorTotal;
    private BigDecimal quantidadeAlunosPrevisao;
    private BigDecimal quantidadeAlunosPagas;
    private BigDecimal quantidadeAlunosInadimplencia;
    private BigDecimal quantidadeAlunosCanceladas;

    public InadimplenciaGraficoTO(String mes, BigDecimal valorEmAberto, BigDecimal valorPago, BigDecimal valorTotal, BigDecimal quantidadeAlunosPrevisao, BigDecimal quantidadeAlunosPagas, BigDecimal quantidadeAlunosInadimplencia, BigDecimal quantidadeAlunosCanceladas){
        setMes(mes);
        setValorEmAberto(valorEmAberto);
        setValorPago(valorPago);
        setValorTotal(valorTotal);
        setQuantidadeAlunosPrevisao(quantidadeAlunosPrevisao);
        setQuantidadeAlunosPagas(quantidadeAlunosPagas);
        setQuantidadeAlunosInadimplencia(quantidadeAlunosInadimplencia);
        setQuantidadeAlunosCanceladas(quantidadeAlunosCanceladas);
        calcularInadimplencia();
    }

    public InadimplenciaGraficoTO(JSONObject json) {
        this.mes = json.getString("mes");
        this.valorPago = BigDecimal.valueOf(json.getDouble("valorPago"));
        this.valorEmAberto = BigDecimal.valueOf(json.getDouble("valorEmAberto"));
        this.inadimplencia = json.getDouble("inadimplencia");
        this.valorTotal = BigDecimal.valueOf(json.getDouble("valorTotal"));
        this.quantidadeAlunosPrevisao = BigDecimal.valueOf(json.getDouble("quantidadeAlunosPrevisao"));
        this.quantidadeAlunosPagas = BigDecimal.valueOf(json.getDouble("quantidadeAlunosPagas"));
        this.quantidadeAlunosInadimplencia = BigDecimal.valueOf(json.getDouble("quantidadeAlunosInadimplencia"));
        this.quantidadeAlunosCanceladas = BigDecimal.valueOf(json.getDouble("quantidadeAlunosCanceladas"));
    }

    private void calcularInadimplencia() {
        if(valorEmAberto != null && valorPago != null && valorTotal != null){
            Double soma = valorTotal.doubleValue();
            Double valor = 0.0;
            if(!UteisValidacao.emptyNumber(this.getValorTotal().doubleValue())){
                valor = valorEmAberto.doubleValue() / soma * 100;
            }
            this.inadimplencia = Uteis.arredondarForcando2CasasDecimais(valor);
        }
    }

    public String getEficiencia() {
        return Formatador.formatarValorMonetarioSemMoeda(100 - inadimplencia) + "% de eficiência";
    }

    public Double getEficienciaDouble() {
        return 100 - inadimplencia;
    }

    public String getMes() {
        return mes;
    }

    public void setMes(String mes) {
        this.mes = mes;
    }

    public BigDecimal getValorPago() {
        return valorPago;
    }

    public void setValorPago(BigDecimal valorPago) {
        this.valorPago = valorPago;
    }

    public BigDecimal getValorEmAberto() {
        return valorEmAberto;
    }

    public void setValorEmAberto(BigDecimal valorEmAberto) {
        this.valorEmAberto = valorEmAberto;
    }

    public Double getInadimplencia() {
        return inadimplencia;
    }

    public void setInadimplencia(Double inadimplencia) {
        this.inadimplencia = inadimplencia;
    }

    public BigDecimal getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(BigDecimal valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getInadimplencia_Apresentar() {
        if (getInadimplencia() == null) {
            return "";
        }
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(getInadimplencia());
    }

    public String getValorTotal_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorTotal().doubleValue());
    }

    public String getValorEmAberto_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorEmAberto().doubleValue());
    }

    public String getValorPago_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorPago().doubleValue());
    }

    public BigDecimal getQuantidadeAlunosPrevisao() {
        return quantidadeAlunosPrevisao;
    }

    public void setQuantidadeAlunosPrevisao(BigDecimal quantidadeAlunosPrevisao) {
        this.quantidadeAlunosPrevisao = quantidadeAlunosPrevisao;
    }

    public BigDecimal getQuantidadeAlunosPagas() {
        return quantidadeAlunosPagas;
    }

    public void setQuantidadeAlunosPagas(BigDecimal quantidadeAlunosPagas) {
        this.quantidadeAlunosPagas = quantidadeAlunosPagas;
    }

    public BigDecimal getQuantidadeAlunosInadimplencia() {
        return quantidadeAlunosInadimplencia;
    }

    public void setQuantidadeAlunosInadimplencia(BigDecimal quantidadeAlunosInadimplencia) {
        this.quantidadeAlunosInadimplencia = quantidadeAlunosInadimplencia;
    }

    public BigDecimal getQuantidadeAlunosCanceladas() {
        return quantidadeAlunosCanceladas;
    }

    public void setQuantidadeAlunosCanceladas(BigDecimal quantidadeAlunosCanceladas) {
        this.quantidadeAlunosCanceladas = quantidadeAlunosCanceladas;
    }
}

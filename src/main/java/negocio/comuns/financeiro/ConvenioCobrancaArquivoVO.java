package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.io.FileUtils;
import servicos.propriedades.PropsService;

import java.io.File;
import java.io.FileInputStream;
import java.security.KeyStore;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 14/07/2020
 */

public class ConvenioCobrancaArquivoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo = 0;
    @ChaveEstrangeira
    private int convenioCobranca;
    private String nomeArquivoOriginal;
    private transient byte arquivo[];
    private Date dataUpload;
    private String senha;
    private int Usuario;
    private String dataUploadString;
    @NaoControlarLogAlteracao
    private ConvenioCobrancaVO convenioCobrancaVO;
    @NaoControlarLogAlteracao
    private String arquivoPlenoString;
    private String dataExpiracaoCertificado;

    public static void validarDados(ConvenioCobrancaArquivoVO obj) throws ConsistirException {
        if (UteisValidacao.emptyNumber(obj.getConvenioCobranca())) {
            throw new ConsistirException("Não é possível gravar um ConvenioCobrancaArquivo sem convênio.");
        }
        if (UteisValidacao.emptyString(obj.getNomeArquivoOriginal())) {
            throw new ConsistirException("Não é possível gravar um ConvenioCobrancaArquivo sem o nome original do arquivo.");
        }
        if (obj.getArquivo() == null) {
            throw new ConsistirException("Não é possível gravar um ConvenioCobrancaArquivo sem o byte original do arquivo.");
        }
        if (obj.getDataUpload() == null) {
            throw new ConsistirException("Não é possível gravar um ConvenioCobrancaArquivo sem a data de upload.");
        }
        if (UteisValidacao.emptyString(obj.getSenha()) && !obj.getConvenioCobrancaVO().isPixInter() && !obj.getConvenioCobrancaVO().isDccCaixaOnline()) {
            throw new ConsistirException("Você incluiu um certificado, porém não informou a senha dele no campo \"Senha do Certificado\"");
        }
        if (UteisValidacao.emptyNumber(obj.getUsuario())) {
            throw new ConsistirException("Não é possível gravar um ConvenioCobrancaArquivo sem o usuário.");
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(int convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public String getNomeArquivoOriginal() {
        return nomeArquivoOriginal;
    }

    public void setNomeArquivoOriginal(String nomeArquivoOriginal) {
        this.nomeArquivoOriginal = nomeArquivoOriginal;
    }

    public byte[] getArquivo() {
        return arquivo;
    }

    public void setArquivo(byte[] arquivo) {
        this.arquivo = arquivo;
    }

    public Date getDataUpload() {
        return dataUpload;
    }

    public void setDataUpload(Date dataUpload) {
        this.dataUpload = dataUpload;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public int getUsuario() {
        return Usuario;
    }

    public void setUsuario(int usuario) {
        Usuario = usuario;
    }

    public String getDataUploadString() {
        return Uteis.getDataComHora(getDataUpload());
    }

    public void setDataUploadString(String dataUploadString) {
        this.dataUploadString = dataUploadString;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public String getArquivoPlenoString() {
        if (UteisValidacao.emptyString(arquivoPlenoString)) {
            return "";
        }
        return arquivoPlenoString;
    }

    public void setArquivoPlenoString(String arquivoPlenoString) {
        this.arquivoPlenoString = arquivoPlenoString;
    }

    public String getDataExpiracaoCertificado() {
        try {
            if (getArquivo() != null && (convenioCobrancaVO.isPixInter() || convenioCobrancaVO.isPixBradesco())) {
                return obterDataValidadeCertificado();
            }
        } catch (Exception ex) {
            return "";
        }
        return dataExpiracaoCertificado;
    }

    public void setDataExpiracaoCertificado(String dataExpiracaoCertificado) {
        this.dataExpiracaoCertificado = dataExpiracaoCertificado;
    }

    private String obterDataValidadeCertificado() {
        File arquivoTemp = new File("");
        try {
            if (!UteisValidacao.emptyString(getNomeArquivoOriginal()) && getNomeArquivoOriginal().contains(".pfx")) {

                String pass = "";
                if (convenioCobrancaVO.isPixInter()) {
                    pass = Uteis.desencriptar(PropsService.getPropertyValue(PropsService.senhaDecriptPCertPrivadoPfx), PropsService.getPropertyValue(PropsService.chaveCriptCertPrivado));
                } else if (convenioCobrancaVO.isPixBradesco()) {
                    pass = Uteis.desencriptar(getSenha(), PropsService.getPropertyValue(PropsService.chaveCriptCertPrivado));
                }

                arquivoTemp = File.createTempFile(String.format("cert-%s-%s", Calendario.hoje().getTime(), getNomeArquivoOriginal()), (".pfx"));
                FileUtils.writeByteArrayToFile(arquivoTemp, getArquivo());
                try (FileInputStream fis = new FileInputStream(arquivoTemp)) {
                    // Carrega o KeyStore com o arquivo PFX e senha
                    KeyStore keyStore = KeyStore.getInstance("PKCS12");
                    keyStore.load(fis, pass.toCharArray());

                    // Obtém o alias do certificado no KeyStore
                    String alias = keyStore.aliases().nextElement();

                    // Obtém o certificado associado ao alias
                    X509Certificate certificado = (X509Certificate) keyStore.getCertificate(alias);

                    // Obtém a data de validade do certificado
                    SimpleDateFormat formatoData = new SimpleDateFormat("dd/MM/yyyy");
                    return formatoData.format(certificado.getNotAfter());
                }
            }
        } catch (Exception e) {
            return "Não foi possível obter o vencimento do certificado";
        } finally {
            if (arquivoTemp.exists()) {
                boolean excluiuArqTemp = arquivoTemp.delete(); //excluir imediato
                if (!excluiuArqTemp) {
                    arquivoTemp.deleteOnExit();  // Garante a excluso ao sair se a excluso imediata falhar
                }
            }
        }
        return "Não foi possível obter o vencimento do certificado";
    }
}

package negocio.comuns.financeiro;

import java.util.Date;

import negocio.comuns.arquitetura.SuperVO;

public class DetalhamentoDiarioMetaFinanceiroVO extends SuperVO {
	
	private Date dia;
	private String cliente;
	private Integer contrato;
	private String responsavel;
	private String responsavelParcela;
	private Double valor;
    private String formaPagamento;
    private String duracaoContrato;

	public Date getDia() {
		return dia;
	}
	public void setDia(Date dia) {
		this.dia = dia;
	}
	public String getCliente() {
		return cliente;
	}
	public void setCliente(String cliente) {
		this.cliente = cliente;
	}
	public Integer getContrato() {
		return contrato;
	}
	public void setContrato(Integer contrato) {
		this.contrato = contrato;
	}
	public String getResponsavel() {
		return responsavel;
	}
	public void setResponsavel(String responsavel) {
		this.responsavel = responsavel;
	}
	public Double getValor() {
		return valor;
	}
	public void setValor(Double valor) {
		this.valor = valor;
	}

    public String getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public String getDuracaoContrato() {
        return duracaoContrato;
    }

	public String getResponsavelParcela() {
		return responsavelParcela;
	}

	public void setResponsavelParcela(String responsavelParcela) {
		this.responsavelParcela = responsavelParcela;
	}

	public void setDuracaoContrato(String duracaoContrato) {
        this.duracaoContrato = duracaoContrato;
    }
}

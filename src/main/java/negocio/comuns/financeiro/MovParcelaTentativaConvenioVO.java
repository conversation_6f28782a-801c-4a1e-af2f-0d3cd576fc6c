/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;

/*
 * <AUTHOR> 17/10/2017
 */
public class MovParcelaTentativaConvenioVO extends SuperVO {

    private Integer codigo;
    private MovParcelaVO movParcelaVO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private Integer nrTentativaParcela;

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public MovParcelaVO getMovParcelaVO() {
        if (movParcelaVO == null) {
            movParcelaVO = new MovParcelaVO();
        }
        return movParcelaVO;
    }

    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public Integer getNrTentativaParcela() {
        if (nrTentativaParcela == null) {
            nrTentativaParcela = 0;
        }
        return nrTentativaParcela;
    }

    public void setNrTentativaParcela(Integer nrTentativaParcela) {
        this.nrTentativaParcela = nrTentativaParcela;
    }
}

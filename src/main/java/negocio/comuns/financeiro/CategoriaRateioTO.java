package negocio.comuns.financeiro;

import java.io.Serializable;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade de rateio
 */
public class CategoriaRateioTO implements Serializable{
    private static final long serialVersionUID = -4624795098389390286L;
	private int codigo;
	private String nome;
	private List<RateioIntegracaoTO> rateios;
	private List<ProdutoRateioTO> produtosRateios;
	private List<RateioIntegracaoTO> produtos;
	
	public String getMensagem(){
		String msg = "";
		Integer rateiosSize = rateios == null ? 0 : rateios.size();
		msg += rateiosSize > 0 ? "Existem rateios definidos para esta categoria. " : "<font color=\"red\">Não existem rateios definidos para esta categoria.</font> ";
		msg += (produtosRateios == null ? 0 : produtosRateios.size()) + " produtos tem rateio específico.";
		return msg;
	}
	public int getCodigo() {
		return codigo;
	}
	public void setCodigo(int codigo) {
		this.codigo = codigo;
	}
	public String getNome() {
		return nome;
	}
	public void setNome(String nome) {
		this.nome = nome;
	}
	public List<RateioIntegracaoTO> getRateios() {
		return rateios;
	}
	public void setRateios(List<RateioIntegracaoTO> rateios) {
		this.rateios = rateios;
	}
	public List<ProdutoRateioTO> getProdutosRateios() {
		return produtosRateios;
	}
	public void setProdutosRateios(List<ProdutoRateioTO> produtosRateios) {
		this.produtosRateios = produtosRateios;
	}
	public List<RateioIntegracaoTO> getProdutos() {
		return produtos;
	}
	public void setProdutos(List<RateioIntegracaoTO> produtos) {
		this.produtos = produtos;
	}
	
	public int getQtdProdutos(){
		return this.getQtdProdutosRateios() + this.getQtdProdutosSemRateios();
	}
	
	public int getQtdProdutosRateios(){
		if (this.getProdutosRateios() == null){
			return 0;
		}	
		return this.getProdutosRateios().size();
	}
	
	public int getQtdProdutosSemRateios(){
		if (this.getProdutos() == null){
			return 0;
		}
		return this.getProdutos().size();
	}
}
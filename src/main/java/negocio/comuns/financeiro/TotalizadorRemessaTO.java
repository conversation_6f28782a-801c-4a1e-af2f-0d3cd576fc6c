package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TotalizadorRemessaTO extends SuperTO {

    private String label = "";
    private Double valor = 0.0;
    private Integer quantidade = 0;
    private Double porcentagem = 0.0;
    private String css = "";
    private Date mesReferencia;
    private String title;
    private List<TotalizadorRemessaTO> listaFilhoTotalizadorRemessaTO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private List<MovParcelaVO> listaParcelas;

    public TotalizadorRemessaTO() {

    }

    public TotalizadorRemessaTO(JSONObject json) {
        this.label = json.getString("label");
        this.valor = json.getDouble("valor");
        this.quantidade = json.getInt("quantidade");
        this.porcentagem = json.getDouble("porcentagem");
        this.mesReferencia = new Date(json.getLong("mesReferencia"));
        this.title = json.optString("title");

        try {
            listaParcelas = new ArrayList<>();
            JSONArray totalizador = json.getJSONArray("listaParcelas");
            for (int e = 0; e < totalizador.length(); e++) {
                JSONObject obj = totalizador.getJSONObject(e);
                MovParcelaVO movParcelaVO = new MovParcelaVO();
                movParcelaVO.setCodigo(obj.getInt("codigo"));
                movParcelaVO.setValorParcela(obj.getDouble("valorParcela"));
                if (obj.has("dataVencimento")) {
                    movParcelaVO.setDataVencimento(new Date(obj.getLong("dataVencimento")));
                }
                listaParcelas.add(movParcelaVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private Integer codConvenio = 0;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getAbrirPopUp(){
        return "abrirPopup('recorrenciaClienteForm.jsp', 'RecorrenciaCliente', 850, 650);";
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valor);
    }

    public String getCss() {
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }

    public Double getPorcentagem() {
        return porcentagem;
    }

    public void setPorcentagem(Double porcentagem) {
        this.porcentagem = porcentagem;
    }

    public String getPorcentagem_Apresentar() {
        if (getPorcentagem() == null) {
            return "";
        }
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(getPorcentagem());
    }

    public void calcularPorcentagem(Double valorParaComparar) {
        if (valorParaComparar > 0.0) {
            setPorcentagem((this.getValor() / valorParaComparar) * 100);
        } else {
            setPorcentagem(0.0);
        }
    }

    public Integer getCodConvenio() {
        return codConvenio;
    }

    public void setCodConvenio(Integer codConvenio) {
        this.codConvenio = codConvenio;
    }

    public Date getMesReferencia() {
        return mesReferencia;
    }

    public void setMesReferencia(Date mesReferencia) {
        this.mesReferencia = mesReferencia;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<TotalizadorRemessaTO> getListaFilhoTotalizadorRemessaTO() {
        if (listaFilhoTotalizadorRemessaTO == null) {
            listaFilhoTotalizadorRemessaTO = new ArrayList<TotalizadorRemessaTO>();
        }
        return listaFilhoTotalizadorRemessaTO;
    }

    public void setListaFilhoTotalizadorRemessaTO(List<TotalizadorRemessaTO> listaFilhoTotalizadorRemessaTO) {
        this.listaFilhoTotalizadorRemessaTO = listaFilhoTotalizadorRemessaTO;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public List<MovParcelaVO> getListaParcelas() {
        if (listaParcelas == null) {
            listaParcelas = new ArrayList<MovParcelaVO>();
        }
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public void processarQtdValor() {
        Double valor = 0.0;
        for (MovParcelaVO movParcelaVO : getListaParcelas()) {
            valor += movParcelaVO.getValorParcela();
        }
        setQuantidade(getListaParcelas().size());
        setValor(Uteis.arredondarForcando2CasasDecimais(valor));
    }

    public boolean isApresentarFormaPagamento() {
        return this.getLabel() != null && (this.getLabel().toUpperCase().contains("PAGAS PELO") || this.getLabel().toUpperCase().contains("PAGAS FORA"));
    }
}

package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.nfe.NotaFiscalConsumidorNFCeVO;
import negocio.comuns.nfe.NotaFiscalDeServicoVO;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.comuns.nfe.enumerador.TipoNotaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.lang.reflect.Field;
import java.util.*;

public class NotasFiscaisTO extends SuperTO {

    private String idRPS;
    private String idNFCe;
    private String serie;
    private String nrRPS;
    private String nrNota;
    private String status;
    private String valor;
    private String dataEmissao;
    private String dataProcessamento;
    private String razaoSocial;
    private Integer tipoNota;
    @JsonIgnore
    private Date dataEmissaoOrdenar;
    private String email;
    private Integer codNotaFiscal;
    private boolean enotas = false;
    private String linkPDF;

    public NotasFiscaisTO(){
    }

    public NotasFiscaisTO(NotaFiscalDeServicoVO notaVO) {
        this.idRPS = notaVO.getIdRPS().toString();
        this.idNFCe = "";
        this.serie = notaVO.getSerieRPS();
        this.nrRPS = notaVO.getNumeroRPS().toString();
        this.nrNota = notaVO.getNumeroNota();
        this.status = notaVO.getStatus();
        this.valor = Formatador.formatarValorMonetario(notaVO.getValorServicos());
        this.dataEmissaoOrdenar = notaVO.getDataEmissao();
        this.dataEmissao = Uteis.getDataAplicandoFormatacao(notaVO.getDataEmissao(), "dd/MM/yyyy HH:mm:ss");
        this.dataProcessamento = Uteis.getDataAplicandoFormatacao(notaVO.getDataProcessamento(), "dd/MM/yyyy HH:mm:ss");
        this.razaoSocial = notaVO.getRazaoSocialCons();
        this.tipoNota = TipoNotaEnum.NFSE.getCodigo();
        this.linkPDF = notaVO.getLinkDownloadPDF();
    }

    public NotasFiscaisTO(NotaFiscalConsumidorNFCeVO nfceVO) {
        this.idRPS = "";
        this.idNFCe = nfceVO.getId_NFCe().toString();
        this.serie = "";
        this.nrNota = nfceVO.getNumeroEnvio();
        this.status = nfceVO.getStatus();
        this.valor = Formatador.formatarValorMonetario(nfceVO.getValorTotal());
        this.dataEmissao = Uteis.getDataAplicandoFormatacao(nfceVO.getDataHoraEmissao(), "dd/MM/yyyy HH:mm:ss");
        this.dataEmissaoOrdenar = nfceVO.getDataHoraEmissao();
        this.dataProcessamento = Uteis.getDataAplicandoFormatacao(nfceVO.getDataHoraEmissao(), "dd/MM/yyyy HH:mm:ss");
        this.razaoSocial = nfceVO.getDestNome();
        this.tipoNota = TipoNotaEnum.NFCE.getCodigo();
    }

    public String getNrNota() {
        if (nrNota == null) {
            nrNota = "";
        }
        return nrNota;
    }

    public void setNrNota(String nrNota) {
        this.nrNota = nrNota;
    }

    public String getStatus() {
        if (status == null) {
            status = "";
        }
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRazaoSocial() {
        if (razaoSocial == null) {
            razaoSocial = "";
        }
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getSerie() {
        if (serie == null) {
            serie = "";
        }
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public String getIdRPS() {
        if (idRPS == null) {
            idRPS = "";
        }
        return idRPS;
    }

    public void setIdRPS(String idRPS) {
        this.idRPS = idRPS;
    }

    public String getNrRPS() {
        if (nrRPS == null) {
            nrRPS = "";
        }
        return nrRPS;
    }

    public void setNrRPS(String nrRPS) {
        this.nrRPS = nrRPS;
    }

    public String getIdNFCe() {
        if (idNFCe == null) {
            idNFCe = "";
        }
        return idNFCe;
    }

    public void setIdNFCe(String idNFCe) {
        this.idNFCe = idNFCe;
    }

    public Integer getTipoNota() {
        if (isEnotas()) {
            if (tipoNota == null) {
                tipoNota = TipoNotaFiscalEnum.NFSE.getCodigo();
            }
        } else {
            if (tipoNota == null) {
                tipoNota = TipoNotaEnum.NFSE.getCodigo();
            }
        }
        return tipoNota;
    }

    public void setTipoNota(Integer tipoNota) {
        this.tipoNota = tipoNota;
    }

    public JSONObject toJSON() {
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                o.put(field.getName(), field.get(this));
            }
        } catch (Exception e) {
        }
        return o;
    }

    public String getValor() {
        if (valor == null) {
            valor = "";
        }
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public String getDataEmissao() {
        if (dataEmissao == null) {
            dataEmissao = "";
        }
        return dataEmissao;
    }

    public void setDataEmissao(String dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public String getDataProcessamento() {
        if (dataProcessamento == null) {
            dataProcessamento = "";
        }
        return dataProcessamento;
    }

    public void setDataProcessamento(String dataProcessamento) {
        this.dataProcessamento = dataProcessamento;
    }

    public Date getDataEmissaoOrdenar() {
        return dataEmissaoOrdenar;
    }

    public void setDataEmissaoOrdenar(Date dataEmissaoOrdenar) {
        this.dataEmissaoOrdenar = dataEmissaoOrdenar;
    }

    public String getIdReferencia() {
        if (!UteisValidacao.emptyString(getIdRPS())) {
            return getIdRPS();
        } else if (!UteisValidacao.emptyString(getIdNFCe())) {
            return getIdNFCe();
        } else {
            return "";
        }
    }

    public String getTipoNotaApresentar() {
        if (isEnotas()) {
            TipoNotaFiscalEnum tipoNotaFiscalEnum = TipoNotaFiscalEnum.obterPorCodigo(getTipoNota());
            if (tipoNotaFiscalEnum != null) {
                return tipoNotaFiscalEnum.getDescricao();
            }
            return "Tipo desconhecido";
        }
        return TipoNotaEnum.getTipo(getTipoNota()).getDescricao();
    }

    public boolean getTipoNFSe() {
        if (isEnotas()) {
            return getTipoNota().equals(TipoNotaFiscalEnum.NFSE.getCodigo());
        }
        return getTipoNota().equals(TipoNotaEnum.NFSE.getCodigo());
    }

    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isMostrarAcoesNotaFiscalTelaCliente() {
        return getStatus().toUpperCase().equals("AUTORIZADO");
}

    public Integer getCodNotaFiscal() {
        if (codNotaFiscal == null) {
            codNotaFiscal = 0;
        }
        return codNotaFiscal;
    }

    public void setCodNotaFiscal(Integer codNotaFiscal) {
        this.codNotaFiscal = codNotaFiscal;
    }

    public boolean isEnotas() {
        return enotas;
    }

    public void setEnotas(boolean enotas) {
        this.enotas = enotas;
    }

    public String getLinkPDF() {
        if(linkPDF == null) {
            linkPDF = "";
        }
        return linkPDF;
    }

    public void setLinkPDF(String linkPDF) {
        this.linkPDF = linkPDF;
    }
}

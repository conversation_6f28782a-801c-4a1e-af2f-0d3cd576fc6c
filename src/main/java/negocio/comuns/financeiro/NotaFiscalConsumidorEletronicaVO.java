package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/*
 * Created by <PERSON><PERSON> on 27/03/2017.
 */
public class NotaFiscalConsumidorEletronicaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer id_NFCe;
    private Date dataRegistro;
    @ChaveEstrangeira
    private ReciboPagamentoVO reciboPagamento;
    @ChaveEstrangeira
    private UsuarioVO usuario;
    private Double valorTotal;
    @ChaveEstrangeira
    private MovContaVO movConta;
    @ChaveEstrangeira
    private ChequeVO cheque;
    @ChaveEstrangeira
    private CartaoCreditoVO cartaoCredito;
    @ChaveEstrangeira
    private MovPagamentoVO movPagamento;
    @ChaveEstrangeira
    private MovProdutoVO movProduto;
    private String jsonEnviar;
    @ChaveEstrangeira
    private EmpresaVO empresa;
    private String idReferencia;
    @ChaveEstrangeira
    private PessoaVO pessoaVO;
    private NotaFiscalVO notaFiscalVO;
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO;
    private boolean enotas = false;
    private SituacaoNotaFiscalEnum situacaoNotaFiscal;
    private Date dataEnvio;
    private String resultadoEnvio;

    private List<NotaFiscalConsumidorEletronicaItensVO> itensNota;
    private List<NotaFiscalConsumidorEletronicaFormasPagamentoVO> formasPagamentoNota;

    public NotaFiscalConsumidorEletronicaVO(){

    }

    public NotaFiscalConsumidorEletronicaVO(Integer codigo){
        this.codigo = codigo;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getId_NFCe() {
        if (id_NFCe == null) {
            id_NFCe = 0;
        }
        return id_NFCe;
    }

    public void setId_NFCe(Integer id_NFCe) {
        this.id_NFCe = id_NFCe;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public ReciboPagamentoVO getReciboPagamento() {
        if (reciboPagamento == null) {
            reciboPagamento = new ReciboPagamentoVO();
        }
        return reciboPagamento;
    }

    public void setReciboPagamento(ReciboPagamentoVO reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public UsuarioVO getUsuario() {
        if (usuario == null) {
            usuario = new UsuarioVO();
        }
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public Double getValorTotal() {
        if (valorTotal == null) {
            valorTotal = 0.0;
        }
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public List<NotaFiscalConsumidorEletronicaItensVO> getItensNota() {
        if (itensNota == null) {
            itensNota = new ArrayList<NotaFiscalConsumidorEletronicaItensVO>();
        }
        return itensNota;
    }

    public void setItensNota(List<NotaFiscalConsumidorEletronicaItensVO> itensNota) {
        this.itensNota = itensNota;
    }

    public List<NotaFiscalConsumidorEletronicaFormasPagamentoVO> getFormasPagamentoNota() {
        if (formasPagamentoNota == null) {
            formasPagamentoNota = new ArrayList<NotaFiscalConsumidorEletronicaFormasPagamentoVO>();
        }
        return formasPagamentoNota;
    }

    public void setFormasPagamentoNota(List<NotaFiscalConsumidorEletronicaFormasPagamentoVO> formasPagamentoNota) {
        this.formasPagamentoNota = formasPagamentoNota;
    }

    public MovContaVO getMovConta() {
        if (movConta == null) {
            movConta = new MovContaVO();
        }
        return movConta;
    }

    public void setMovConta(MovContaVO movConta) {
        this.movConta = movConta;
    }

    public ChequeVO getCheque() {
        if (cheque == null) {
            cheque = new ChequeVO();
        }
        return cheque;
    }

    public void setCheque(ChequeVO cheque) {
        this.cheque = cheque;
    }

    public CartaoCreditoVO getCartaoCredito() {
        if (cartaoCredito == null) {
            cartaoCredito = new CartaoCreditoVO();
        }
        return cartaoCredito;
    }

    public void setCartaoCredito(CartaoCreditoVO cartaoCredito) {
        this.cartaoCredito = cartaoCredito;
    }

    public MovPagamentoVO getMovPagamento() {
        if (movPagamento == null) {
            movPagamento = new MovPagamentoVO();
        }
        return movPagamento;
    }

    public void setMovPagamento(MovPagamentoVO movPagamento) {
        this.movPagamento = movPagamento;
    }

    public MovProdutoVO getMovProduto() {
        if (movProduto == null) {
            movProduto = new MovProdutoVO();
        }
        return movProduto;
    }

    public void setMovProduto(MovProdutoVO movProduto) {
        this.movProduto = movProduto;
    }

    public String getJsonEnviar() {
        if (jsonEnviar == null) {
            jsonEnviar = "";
        }
        return jsonEnviar;
    }

    public void setJsonEnviar(String jsonEnviar) {
        this.jsonEnviar = jsonEnviar;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getIdReferencia() {
        if (idReferencia == null) {
            idReferencia = "";
        }
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public NotaFiscalVO getNotaFiscalVO() {
        if (notaFiscalVO == null) {
            notaFiscalVO = new NotaFiscalVO();
        }
        return notaFiscalVO;
    }

    public void setNotaFiscalVO(NotaFiscalVO notaFiscalVO) {
        this.notaFiscalVO = notaFiscalVO;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalVO() {
        if (configuracaoNotaFiscalVO == null) {
            configuracaoNotaFiscalVO = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalVO;
    }

    public void setConfiguracaoNotaFiscalVO(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) {
        this.configuracaoNotaFiscalVO = configuracaoNotaFiscalVO;
    }

    public boolean isEnotas() {
        return enotas;
    }

    public void setEnotas(boolean enotas) {
        this.enotas = enotas;
    }

    public Date getDataEnvio() {
        return dataEnvio;
    }

    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;
    }

    public String getResultadoEnvio() {
        if (resultadoEnvio == null) {
            resultadoEnvio = "";
        }
        return resultadoEnvio;
    }

    public void setResultadoEnvio(String resultadoEnvio) {
        this.resultadoEnvio = resultadoEnvio;
    }

    public SituacaoNotaFiscalEnum getSituacaoNotaFiscal() {
        return situacaoNotaFiscal;
    }

    public void setSituacaoNotaFiscal(SituacaoNotaFiscalEnum situacaoNotaFiscal) {
        this.situacaoNotaFiscal = situacaoNotaFiscal;
    }
}

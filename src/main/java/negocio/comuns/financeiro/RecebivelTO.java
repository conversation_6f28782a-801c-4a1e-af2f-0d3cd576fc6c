package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;
import java.util.List;

public class RecebivelTO extends SuperTO {

    private String tipoRecebivel;
    private String matricula;
    private String nomePagador;
    private String nomeAlunosDaParcela;
    private String cpfPagador;
    private String conta;
    private Double valor;
    private Date dataCompensacao;
    private Date dataLancamento;

    //CartaoCredito
    private Integer codigo;
    private String codigoUnico;
    private Integer recibo;
    private Integer numeroLote;
    private String operadora;
    private String codigosComposicao;
    private Date dataOriginal;
    private Boolean cartaoEscolhido;
    private String autorizacao;
    private String nsu;
    private String nomePessoa;
    private Integer codigoContaContido;
    private String contaContido;
    private boolean removido;
    private Integer codigoPessoa;
    private Integer movConta;
    private String documentoIntegracaoSesi;

    //ChequeTO
    private String nomeNoCheque;
    private String numero;
    private String agencia;
    private String numeroBanco;
    private Date dataFim;
    private Boolean chequeEscolhido;
    private Integer pagaMovConta;
    private Integer loteAvulso;

    private Date dataQuitacao;
    private Date dataPagamento;
    private Double valorTotal;
    private Boolean credito;
    private String creditoApresentar;
    private Integer nrParcelaCartaoCredito;
    private String saldoContaCorrenteCliente;
    private String tipoPagador;
    private ConvenioCobrancaVO convenio;
    private OperadoraCartaoVO operadoraCartaoVO;
    private List pagamentoMovParcelaVOs;
    private List<ChequeVO> chequeVOs;
    private List<CartaoCreditoVO> cartaoCreditoVOs;
    private PessoaVO pessoa;
    private UsuarioVO responsavelPagamento;
    private ReciboPagamentoVO reciboPagamento;
    private Boolean pagamentoAvulso;
    private FormaPagamentoVO formaPagamento = new FormaPagamentoVO();
    private Boolean opcaoPagamentoCheque;
    private Boolean opcaoPagamentoCartaoCredito;
    private Boolean opcaoPagamentoCartaoDebito;
    private Boolean opcaoPagamentoDinheiro;
    private Boolean opcaoPagamentoTransferencia;
    private Boolean opcaoPagamentoBoleto;
    private Boolean opcaoPagamentoContaCorrenteCliente;
    private Boolean mostraContaCorrenteAcademia;
    private Boolean movPagamentoEscolhida;
    private Boolean apresentarCampoCPF;
    private Boolean apresentarCampoCNPJ;
    private ChequeVO chequeTransiente;//existe apenas enquanto emitindo recibo
    private Double valorReceberOuDevolverContaCorrente;
    private Boolean usarPagamentoDigital;
    private Boolean usarPagamentoAprovaFacil;
    private Date dataAlteracaoManual;
    private EmpresaVO empresa = new EmpresaVO();
    private String observacao;
    private Boolean movPagamentoEscolhidaFinan;
    private String autorizacaoCartao;
    private Integer movPagamentoOrigemCredito;
    private Date dataPrevistaDeposito;
    private String produtosPagos;
    private Integer nrCheques;
    private Boolean cupomEmitido;
    private String contaFinanceiro;
    private Date dataMovimento;
    private String produtos;
    private String planoContrato;
    private String modalidade;
    private String numerosParcelas;
    private String vencimentosParcelas;
    private String codigosParcelas;
    private String cnpjEmpresa;
    private String nomeEmpresa;
    private Integer parcelasMescladas;
    private UsuarioVO usuarioVO;
    @NaoControlarLogAlteracao
    private ContratoVO contratoVO;
    @NaoControlarLogAlteracao
    private String nomeResponsavelRecibo;

    private boolean antecipacao;
    private String antecipacaoApresentar;
    private String empresaApresentar;

    public String getTipoRecebivel() {
        return tipoRecebivel;
    }

    public void setTipoRecebivel(String tipoRecebivel) {
        this.tipoRecebivel = tipoRecebivel;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNomePagador() {
        return nomePagador;
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public String getConta() {
        return conta;
    }

    public void setConta(String conta) {
        this.conta = conta;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCodigoUnico() {
        return codigoUnico;
    }

    public void setCodigoUnico(String codigoUnico) {
        this.codigoUnico = codigoUnico;
    }

    public Integer getRecibo() {
        return recibo;
    }

    public void setRecibo(Integer recibo) {
        this.recibo = recibo;
    }

    public Integer getNumeroLote() {
        return numeroLote;
    }

    public void setNumeroLote(Integer numeroLote) {
        this.numeroLote = numeroLote;
    }

    public String getOperadora() {
        return operadora;
    }

    public void setOperadora(String operadora) {
        this.operadora = operadora;
    }

    public String getCodigosComposicao() {
        return codigosComposicao;
    }

    public void setCodigosComposicao(String codigosComposicao) {
        this.codigosComposicao = codigosComposicao;
    }

    public Date getDataOriginal() {
        return dataOriginal;
    }

    public void setDataOriginal(Date dataOriginal) {
        this.dataOriginal = dataOriginal;
    }

    public Boolean getCartaoEscolhido() {
        return cartaoEscolhido;
    }

    public void setCartaoEscolhido(Boolean cartaoEscolhido) {
        this.cartaoEscolhido = cartaoEscolhido;
    }

    public String getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(String autorizacao) {
        this.autorizacao = autorizacao;
    }

    public String getNsu() {
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public Integer getCodigoContaContido() {
        return codigoContaContido;
    }

    public void setCodigoContaContido(Integer codigoContaContido) {
        this.codigoContaContido = codigoContaContido;
    }

    public String getContaContido() {
        return contaContido;
    }

    public void setContaContido(String contaContido) {
        this.contaContido = contaContido;
    }

    public boolean isRemovido() {
        return removido;
    }

    public void setRemovido(boolean removido) {
        this.removido = removido;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getMovConta() {
        return movConta;
    }

    public void setMovConta(Integer movConta) {
        this.movConta = movConta;
    }

    public String getNomeNoCheque() {
        return nomeNoCheque;
    }

    public void setNomeNoCheque(String nomeNoCheque) {
        this.nomeNoCheque = nomeNoCheque;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getNumeroBanco() {
        return numeroBanco;
    }

    public void setNumeroBanco(String numeroBanco) {
        this.numeroBanco = numeroBanco;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Boolean getChequeEscolhido() {
        return chequeEscolhido;
    }

    public void setChequeEscolhido(Boolean chequeEscolhido) {
        this.chequeEscolhido = chequeEscolhido;
    }

    public Integer getPagaMovConta() {
        return pagaMovConta;
    }

    public void setPagaMovConta(Integer pagaMovConta) {
        this.pagaMovConta = pagaMovConta;
    }

    public Integer getLoteAvulso() {
        return loteAvulso;
    }

    public void setLoteAvulso(Integer loteAvulso) {
        this.loteAvulso = loteAvulso;
    }

    public Date getDataQuitacao() {
        return dataQuitacao;
    }

    public void setDataQuitacao(Date dataQuitacao) {
        this.dataQuitacao = dataQuitacao;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Boolean getCredito() {
        return credito;
    }

    public void setCredito(Boolean credito) {
        this.credito = credito;
    }

    public String getCreditoApresentar() {
        return creditoApresentar;
    }

    public void setCreditoApresentar(String creditoApresentar) {
        this.creditoApresentar = creditoApresentar;
    }

    public Integer getNrParcelaCartaoCredito() {
        return nrParcelaCartaoCredito;
    }

    public void setNrParcelaCartaoCredito(Integer nrParcelaCartaoCredito) {
        this.nrParcelaCartaoCredito = nrParcelaCartaoCredito;
    }

    public String getSaldoContaCorrenteCliente() {
        return saldoContaCorrenteCliente;
    }

    public void setSaldoContaCorrenteCliente(String saldoContaCorrenteCliente) {
        this.saldoContaCorrenteCliente = saldoContaCorrenteCliente;
    }

    public String getTipoPagador() {
        return tipoPagador;
    }

    public void setTipoPagador(String tipoPagador) {
        this.tipoPagador = tipoPagador;
    }

    public ConvenioCobrancaVO getConvenio() {
        return convenio;
    }

    public void setConvenio(ConvenioCobrancaVO convenio) {
        this.convenio = convenio;
    }

    public OperadoraCartaoVO getOperadoraCartaoVO() {
        return operadoraCartaoVO;
    }

    public void setOperadoraCartaoVO(OperadoraCartaoVO operadoraCartaoVO) {
        this.operadoraCartaoVO = operadoraCartaoVO;
    }

    public List getPagamentoMovParcelaVOs() {
        return pagamentoMovParcelaVOs;
    }

    public void setPagamentoMovParcelaVOs(List pagamentoMovParcelaVOs) {
        this.pagamentoMovParcelaVOs = pagamentoMovParcelaVOs;
    }

    public List<ChequeVO> getChequeVOs() {
        return chequeVOs;
    }

    public void setChequeVOs(List<ChequeVO> chequeVOs) {
        this.chequeVOs = chequeVOs;
    }

    public List<CartaoCreditoVO> getCartaoCreditoVOs() {
        return cartaoCreditoVOs;
    }

    public void setCartaoCreditoVOs(List<CartaoCreditoVO> cartaoCreditoVOs) {
        this.cartaoCreditoVOs = cartaoCreditoVOs;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public UsuarioVO getResponsavelPagamento() {
        return responsavelPagamento;
    }

    public void setResponsavelPagamento(UsuarioVO responsavelPagamento) {
        this.responsavelPagamento = responsavelPagamento;
    }

    public ReciboPagamentoVO getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(ReciboPagamentoVO reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public Boolean isPagamentoAvulso() {
        return pagamentoAvulso;
    }

    public void setPagamentoAvulso(Boolean pagamentoAvulso) {
        this.pagamentoAvulso = pagamentoAvulso;
    }

    public FormaPagamentoVO getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamentoVO formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public Boolean getOpcaoPagamentoCheque() {
        return opcaoPagamentoCheque;
    }

    public void setOpcaoPagamentoCheque(Boolean opcaoPagamentoCheque) {
        this.opcaoPagamentoCheque = opcaoPagamentoCheque;
    }

    public Boolean getOpcaoPagamentoCartaoCredito() {
        return opcaoPagamentoCartaoCredito;
    }

    public void setOpcaoPagamentoCartaoCredito(Boolean opcaoPagamentoCartaoCredito) {
        this.opcaoPagamentoCartaoCredito = opcaoPagamentoCartaoCredito;
    }

    public Boolean getOpcaoPagamentoCartaoDebito() {
        return opcaoPagamentoCartaoDebito;
    }

    public void setOpcaoPagamentoCartaoDebito(Boolean opcaoPagamentoCartaoDebito) {
        this.opcaoPagamentoCartaoDebito = opcaoPagamentoCartaoDebito;
    }

    public Boolean getOpcaoPagamentoDinheiro() {
        return opcaoPagamentoDinheiro;
    }

    public void setOpcaoPagamentoDinheiro(Boolean opcaoPagamentoDinheiro) {
        this.opcaoPagamentoDinheiro = opcaoPagamentoDinheiro;
    }

    public Boolean getOpcaoPagamentoTransferencia() {
        return opcaoPagamentoTransferencia;
    }

    public void setOpcaoPagamentoTransferencia(Boolean opcaoPagamentoTransferencia) {
        this.opcaoPagamentoTransferencia = opcaoPagamentoTransferencia;
    }

    public Boolean getOpcaoPagamentoBoleto() {
        return opcaoPagamentoBoleto;
    }

    public void setOpcaoPagamentoBoleto(Boolean opcaoPagamentoBoleto) {
        this.opcaoPagamentoBoleto = opcaoPagamentoBoleto;
    }

    public Boolean getOpcaoPagamentoContaCorrenteCliente() {
        return opcaoPagamentoContaCorrenteCliente;
    }

    public void setOpcaoPagamentoContaCorrenteCliente(Boolean opcaoPagamentoContaCorrenteCliente) {
        this.opcaoPagamentoContaCorrenteCliente = opcaoPagamentoContaCorrenteCliente;
    }

    public Boolean getMostraContaCorrenteAcademia() {
        return mostraContaCorrenteAcademia;
    }

    public void setMostraContaCorrenteAcademia(Boolean mostraContaCorrenteAcademia) {
        this.mostraContaCorrenteAcademia = mostraContaCorrenteAcademia;
    }

    public Boolean getMovPagamentoEscolhida() {
        return movPagamentoEscolhida;
    }

    public void setMovPagamentoEscolhida(Boolean movPagamentoEscolhida) {
        this.movPagamentoEscolhida = movPagamentoEscolhida;
    }

    public Boolean getApresentarCampoCPF() {
        return apresentarCampoCPF;
    }

    public void setApresentarCampoCPF(Boolean apresentarCampoCPF) {
        this.apresentarCampoCPF = apresentarCampoCPF;
    }

    public Boolean getApresentarCampoCNPJ() {
        return apresentarCampoCNPJ;
    }

    public void setApresentarCampoCNPJ(Boolean apresentarCampoCNPJ) {
        this.apresentarCampoCNPJ = apresentarCampoCNPJ;
    }

    public ChequeVO getChequeTransiente() {
        return chequeTransiente;
    }

    public void setChequeTransiente(ChequeVO chequeTransiente) {
        this.chequeTransiente = chequeTransiente;
    }

    public Double getValorReceberOuDevolverContaCorrente() {
        return valorReceberOuDevolverContaCorrente;
    }

    public void setValorReceberOuDevolverContaCorrente(Double valorReceberOuDevolverContaCorrente) {
        this.valorReceberOuDevolverContaCorrente = valorReceberOuDevolverContaCorrente;
    }

    public Boolean getUsarPagamentoDigital() {
        return usarPagamentoDigital;
    }

    public void setUsarPagamentoDigital(Boolean usarPagamentoDigital) {
        this.usarPagamentoDigital = usarPagamentoDigital;
    }

    public Boolean getUsarPagamentoAprovaFacil() {
        return usarPagamentoAprovaFacil;
    }

    public void setUsarPagamentoAprovaFacil(Boolean usarPagamentoAprovaFacil) {
        this.usarPagamentoAprovaFacil = usarPagamentoAprovaFacil;
    }

    public Date getDataAlteracaoManual() {
        return dataAlteracaoManual;
    }

    public void setDataAlteracaoManual(Date dataAlteracaoManual) {
        this.dataAlteracaoManual = dataAlteracaoManual;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Boolean getMovPagamentoEscolhidaFinan() {
        return movPagamentoEscolhidaFinan;
    }

    public void setMovPagamentoEscolhidaFinan(Boolean movPagamentoEscolhidaFinan) {
        this.movPagamentoEscolhidaFinan = movPagamentoEscolhidaFinan;
    }

    public String getAutorizacaoCartao() {
        return autorizacaoCartao;
    }

    public void setAutorizacaoCartao(String autorizacaoCartao) {
        this.autorizacaoCartao = autorizacaoCartao;
    }

    public Integer getMovPagamentoOrigemCredito() {
        return movPagamentoOrigemCredito;
    }

    public void setMovPagamentoOrigemCredito(Integer movPagamentoOrigemCredito) {
        this.movPagamentoOrigemCredito = movPagamentoOrigemCredito;
    }

    public Date getDataPrevistaDeposito() {
        return dataPrevistaDeposito;
    }

    public void setDataPrevistaDeposito(Date dataPrevistaDeposito) {
        this.dataPrevistaDeposito = dataPrevistaDeposito;
    }

    public String getProdutosPagos() {
        return produtosPagos;
    }

    public void setProdutosPagos(String produtosPagos) {
        this.produtosPagos = produtosPagos;
    }

    public Integer getNrCheques() {
        return nrCheques;
    }

    public void setNrCheques(Integer nrCheques) {
        this.nrCheques = nrCheques;
    }

    public Boolean getCupomEmitido() {
        return cupomEmitido;
    }

    public void setCupomEmitido(Boolean cupomEmitido) {
        this.cupomEmitido = cupomEmitido;
    }

    public String getContaFinanceiro() {
        return contaFinanceiro;
    }

    public void setContaFinanceiro(String contaFinanceiro) {
        this.contaFinanceiro = contaFinanceiro;
    }

    public Date getDataMovimento() {
        return dataMovimento;
    }

    public void setDataMovimento(Date dataMovimento) {
        this.dataMovimento = dataMovimento;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public String getProdutos() {
        return produtos;
    }

    public void setProdutos(String produtos) {
        this.produtos = produtos;
    }

    public String getPlanoContrato() {
        return planoContrato;
    }

    public void setPlanoContrato(String planoContrato) {
        this.planoContrato = planoContrato;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getCpf_Apresentar(){
        if( getPessoa() != null) {
            getPessoa().getCfp();
        } else {
            return "";
        }
        return "";
    }

    public String getCpfPagador() {
        return cpfPagador;
    }

    public void setCpfPagador(String cpfPagador) {
        this.cpfPagador = cpfPagador;
    }

    public String getNumerosParcelas() {
        return numerosParcelas;
    }

    public void setNumerosParcelas(String numerosParcelas) {
        this.numerosParcelas = numerosParcelas;
    }

    public String getVencimentosParcelas() {
        return vencimentosParcelas;
    }

    public void setVencimentosParcelas(String vencimentosParcelas) {
        this.vencimentosParcelas = vencimentosParcelas;
    }

    public String getCodigosParcelas() {
        return codigosParcelas;
    }

    public void setCodigosParcelas(String codigosParcelas) {
        this.codigosParcelas = codigosParcelas;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public String getCnpjEmpresa() {
        return cnpjEmpresa;
    }

    public void setCnpjEmpresa(String cnpjEmpresa) {
        this.cnpjEmpresa = cnpjEmpresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getParcelasMescladas() {
        return parcelasMescladas;
    }

    public void setParcelasMescladas(Integer parcelasMescladas) {
        this.parcelasMescladas = parcelasMescladas;
    }

    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public String getUsuarioResponsavelApresentar() {
        return (!UteisValidacao.emptyString(this.getNomeResponsavelRecibo())) ? this.getNomeResponsavelRecibo() : "";
    }

    public Integer getReciboPagamentoApresentar() {
        return (!UteisValidacao.emptyNumber(this.getRecibo())) ? this.getRecibo() : null;
    }

    public Integer getContratoReciboApresentar() {
        return (this.getContratoVO() != null) ? this.getContratoVO().getCodigo() : null;
    }

    public ContratoVO getContratoVO() {
        if (contratoVO == null) {
            return new ContratoVO();
        }
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public String getNomeResponsavelRecibo() {
        if (UteisValidacao.emptyString(nomeResponsavelRecibo)) {
            return "";
        }
        return nomeResponsavelRecibo;
    }

    public void setNomeResponsavelRecibo(String nomeResponsavelRecibo) {
        this.nomeResponsavelRecibo = nomeResponsavelRecibo;
    }

    public boolean isAntecipacao() {
        return antecipacao;
    }

    public void setAntecipacao(boolean antecipacao) {
        this.antecipacao = antecipacao;
    }

    public String getAntecipacaoApresentar() {
        if (antecipacaoApresentar == null) {
            return "";
        }
        return antecipacaoApresentar;
    }

    public void setAntecipacaoApresentar(String antecipacaoApresentar) {
        this.antecipacaoApresentar = antecipacaoApresentar;
    }

    public String getEmpresaApresentar() {
        return empresaApresentar;
    }

    public void setEmpresaApresentar(String empresaApresentar) {
        this.empresaApresentar = empresaApresentar;
    }

    public String getNomeAlunosDaParcela() {
        return nomeAlunosDaParcela;
    }

    public void setNomeAlunosDaParcela(String nomeAlunosDaParcela) {
        this.nomeAlunosDaParcela = nomeAlunosDaParcela;
    }

    public String getDocumentoIntegracaoSesi() {
        return documentoIntegracaoSesi;
    }

    public void setDocumentoIntegracaoSesi(String documentoIntegracaoSesi) {
        this.documentoIntegracaoSesi = documentoIntegracaoSesi;
    }

}

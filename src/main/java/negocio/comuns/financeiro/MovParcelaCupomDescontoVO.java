package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

/**
 * Created by ulisses on 05/06/2016.
 */
public class MovParcelaCupomDescontoVO extends SuperVO {

    private Integer codigo;
    private MovParcelaVO movParcelaVO;
    private String cupomDesconto;
    private String responsavelDesconto;
    private Double valorDesconto;
    private Date dataLancamento;
    private boolean descontoContratoNovo;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public MovParcelaVO getMovParcelaVO() {
        return movParcelaVO;
    }

    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }

    public String getCupomDesconto() {
        return cupomDesconto;
    }

    public void setCupomDesconto(String cupomDesconto) {
        this.cupomDesconto = cupomDesconto;
    }

    public String getResponsavelDesconto() {
        return responsavelDesconto;
    }

    public void setResponsavelDesconto(String responsavelDesconto) {
        this.responsavelDesconto = responsavelDesconto;
    }

    public Double getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public boolean isDescontoContratoNovo() {
        return descontoContratoNovo;
}

    public void setDescontoContratoNovo(boolean descontoContratoNovo) {
        this.descontoContratoNovo = descontoContratoNovo;
    }
}

package negocio.comuns.financeiro;

/**
 * Classe responsável pelos status do cupom
 * 
 * IMPRESSO(1), NAO_IMPRESSO(2), NAO_DEFINIDO(0);
 * 
 * <AUTHOR>
 * 
 */
public enum StatusImpressaoEnum {

    SUCESSO(1, "Impresso com Sucesso"),
    INSUCESSO(2, "Erro na Impressão"),
    CANCELADO(3, "Cupom Cancelado"),
    NAO_DEFINIDO(0, "Impressão Ainda não Realizada");
    private Integer codigo;
    private String descricao;

    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return descricao;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    StatusImpressaoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static StatusImpressaoEnum getStatusImpressao(final Integer codigo) {
        StatusImpressaoEnum obj = null;
        for (StatusImpressaoEnum status : StatusImpressaoEnum.values()) {
            if (status.getCodigo().equals(codigo)) {
                obj = status;
            }
        }
        return obj;
    }
}

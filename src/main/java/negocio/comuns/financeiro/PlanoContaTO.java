package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade de contas
 */
public class PlanoContaTO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private String codigoPlano;
    private String codigoTrv;
    private String descricao;
    private TipoES tipoPadrao;
    private Double meta;
    private String metaDesc = "";
    private Double percGastoPretendido;
    private String percGastoPretendidoDesc = "";
    private TipoEquivalenciaDRE equivalenciaDRE;
    @Lista
    private List<RateioIntegracaoTO> listaRateiosCentroCustos;
    @NaoControlarLogAlteracao
    private Boolean rateio;
    @NaoControlarLogAlteracao
    private PlanoContaTO planoPai;
    @NaoControlarLogAlteracao
    private List<Integer> codigosFilhos;
    private boolean ativo = true;
    private boolean defaultDevolucaoCheque = true;
    private  boolean insidirLTV = false;
    private Integer codigoLumi;
    @NaoControlarLogAlteracao
    private Boolean investimento;

    public String getMetaDescricaoDRE() {
        try {
            return Uteis.arredondarForcando2CasasDecimais(meta) + "%";
        } catch (Exception e) {
            return "";
        }
    }

    public String getDescricaoEquivalencia() {
        if (equivalenciaDRE == null) {
            return "";
        } else {
            return equivalenciaDRE.getDescricao();
        }
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public boolean getExisteRateio() {
        return !getListaRateiosCentroCustos().isEmpty();
    }

    public static void validarDados(PlanoContaTO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getCodigoPlano().isEmpty()) {
            throw new ConsistirException("O campo CÓDIGO CONTÁBIL deve ser informado");
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO deve ser informado.");
        }
        if (obj.getTipoPadrao() == null || obj.getTipoPadrao().getCodigo() == 0) {
            throw new ConsistirException("O campo TIPO PADRÃO deve ser informado.");
        }
        if (!UteisValidacao.emptyNumber(obj.getCodigo()) && obj.getPlanoPai() != null && !UteisValidacao.emptyNumber(obj.getPlanoPai().getCodigo())) {
            if (obj.getCodigo() != 0 && obj.getPlanoPai().getCodigo() != 0 && obj.getCodigo() == obj.getPlanoPai().getCodigo()) {
                throw new ConsistirException("O campo PLANO PAI e esse plano não podem ser iguais");
            }
        }

    }
    private boolean leaf;

    public BigDecimal getCodigoNode() {
        String codigo = this.getCodigoPlano();
        codigo = codigo.replace(".", "");
        return new BigDecimal(codigo.trim());
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCodigoPlano() {
        if (codigoPlano == null) {
            codigoPlano = "";
        }
        return codigoPlano;
    }

    public void setCodigoPlano(String codigoPlano) {
        this.codigoPlano = codigoPlano;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isLeaf() {
        return leaf;
    }

    public void setLeaf(boolean leaf) {
        this.leaf = leaf;
    }

    public TipoES getTipoPadrao() {
        return tipoPadrao;
    }

    public void setTipoPadrao(TipoES tipoPadrao) {
        this.tipoPadrao = tipoPadrao;
    }

    public String getCodigoTrv() {
        return codigoTrv;
    }

    public void setCodigoTrv(String codigoTrv) {
        this.codigoTrv = codigoTrv;
    }

    public String getDescricaoDetalhada() {
        return getCodigoPlano() + " - " + getDescricao()
                + (this.tipoPadrao.equals(TipoES.ENTRADA) ? " (+)" : " (-)") + (this.getListaRateiosCentroCustos().isEmpty() ? "" : " - RCC");
    }

    public String getDescricaoCurta() {
        return getCodigoPlano() + " - " + getDescricao();
    }

    /**
     * @return the planoPai
     */
    public PlanoContaTO getPlanoPai() {
        return planoPai;
    }

    /**
     * @param planoPai the planoPai to set
     */
    public void setPlanoPai(PlanoContaTO planoPai) {
        this.planoPai = planoPai;
    }

    /**
     * @return the equivalenciaDRE
     */
    public TipoEquivalenciaDRE getEquivalenciaDRE() {
        return equivalenciaDRE;
    }

    /**
     * @param equivalenciaDRE the equivalenciaDRE to set
     */
    public void setEquivalenciaDRE(TipoEquivalenciaDRE equivalenciaDRE) {
        this.equivalenciaDRE = equivalenciaDRE;
    }

    /**
     * @return the listaRateiosCentroCustos
     */
    public List<RateioIntegracaoTO> getListaRateiosCentroCustos() {
        if (listaRateiosCentroCustos == null) {
            listaRateiosCentroCustos = new ArrayList<RateioIntegracaoTO>();
        }
        return listaRateiosCentroCustos;
    }

    /**
     * @param listaRateiosCentroCustos the listaRateiosCentroCustos to set
     */
    public void setListaRateiosCentroCustos(List<RateioIntegracaoTO> listaRateiosCentroCustos) {
        this.listaRateiosCentroCustos = listaRateiosCentroCustos;
    }

    /**
     * @param rateio the rateio to set
     */
    public void setRateio(Boolean rateio) {
        this.rateio = rateio;
    }

    /**
     * @return the rateio
     */
    public Boolean getRateio() {
        if (rateio == null) {
            rateio = Boolean.FALSE;
        }
        return rateio;
    }

    /**
     * @param codigosFilhos the codigosFilhos to set
     */
    public void setCodigosFilhos(List<Integer> codigosFilhos) {
        this.codigosFilhos = codigosFilhos;
    }

    /**
     * @return the codigosFilhos
     */
    public List<Integer> getCodigosFilhos() {
        if (codigosFilhos == null) {
            codigosFilhos = new ArrayList<Integer>();
        }
        return codigosFilhos;
    }

    public boolean filhoDe(String codigoPai) {
        return (this.getCodigoPlano().length() > codigoPai.length() && this.getCodigoPlano().substring(0, codigoPai.length()).equals(codigoPai));
    }

    public boolean equals(Object obj) {
        if ((obj == null) || (!(obj instanceof PlanoContaTO))) {
            return false;
        }
        return ((PlanoContaTO) obj).getCodigoPlano().equals(this.getCodigoPlano());
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }

    public Double getMeta() {
        return meta;
    }

    public void setMetaDesc(String metaDesc) {
        this.metaDesc = metaDesc;
    }

    public String getMetaDesc() {
        return metaDesc;
    }

    public Double getPercGastoPretendido() {
        return percGastoPretendido;
    }

    public void setPercGastoPretendido(Double percGastoPretendido) {
        this.percGastoPretendido = percGastoPretendido;
    }

    public String getPercGastoPretendidoDesc() {
        return percGastoPretendidoDesc;
    }

    public void setPercGastoPretendidoDesc(String percGastoPretendidoDesc) {
        this.percGastoPretendidoDesc = percGastoPretendidoDesc;
    }

    public String getDescricaoCurtaComCodigo(){
        if ((this.codigo != null) && (this.codigo > 0)){
            return this.getDescricaoCurta() + " (" + this.codigo + ")";
        }
        return getDescricaoCurta();
    }

    public class ComparatorPorCodigoPlanoConta implements Comparator<PlanoContaTO> {

        /**
         *
         * <AUTHOR>
         * Objetivo: Ordenar uma lista de plano de contas pelo código do plano de contas.
         */
        public int compare(PlanoContaTO obj1, PlanoContaTO obj2) {
            return (obj1.getCodigoPlano().compareTo(obj2.getCodigoPlano()));
        }
    }

    public boolean isDefaultDevolucaoCheque() {
        return defaultDevolucaoCheque;
    }

    public void setDefaultDevolucaoCheque(boolean defaultDevolucaoCheque) {
        this.defaultDevolucaoCheque = defaultDevolucaoCheque;
    }

    public boolean isInsidirLTV() {
        return insidirLTV;
    }

    public void setInsidirLTV(boolean insidirLTV) {
        this.insidirLTV = insidirLTV;
    }

    public Integer getCodigoLumi() {
        if (codigoLumi == null) {
            codigoLumi = 0;
        }
        return codigoLumi;
    }

    public void setCodigoLumi(Integer codigoLumi) {
        this.codigoLumi = codigoLumi;
    }

    public Boolean getInvestimento() {
        return investimento;
    }

    public void setInvestimento(Boolean investimento) {
        this.investimento = investimento;
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import java.util.Date;



/**
 *
 * <AUTHOR>
 */
public class DadosGerencialPmgVO {

    private Integer codigo;
    private Integer empresa;
    private String nomeEmpresa;
    private String chave;
    private String identificador;
    private String indicador;
    private String periodicidade;
    private Date datageracao;
    private Date datapesquisainicio;
    private Date datapesquisafim;
    private Double valor;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }
    
    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getIndicador() {
        return indicador;
    }

    public void setIndicador(String indicador) {
        this.indicador = indicador;
    }

    public String getPeriodicidade() {
        return periodicidade;
    }

    public void setPeriodicidade(String periodicidade) {
        this.periodicidade = periodicidade;
    }

    public Date getDatageracao() {
        return datageracao;
    }

    public void setDatageracao(Date datageracao) {
        this.datageracao = datageracao;
    }

    public Date getDatapesquisainicio() {
        return datapesquisainicio;
    }

    public void setDatapesquisainicio(Date datapesquisainicio) {
        this.datapesquisainicio = datapesquisainicio;
    }

    public Date getDatapesquisafim() {
        return datapesquisafim;
    }

    public void setDatapesquisafim(Date datapesquisafim) {
        this.datapesquisafim = datapesquisafim;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }
    
    
    
}

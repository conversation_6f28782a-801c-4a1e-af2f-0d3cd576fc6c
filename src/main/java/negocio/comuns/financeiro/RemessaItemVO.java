/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import br.com.pactosolucoes.integracao.pactopay.StatusPactoPayEnum;
import br.com.pactosolucoes.integracao.pactopay.dto.TransacaoDTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoBaixaEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.lang.StringUtils;
import org.jboleto.JBoleto;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.boleto.sicredi.SicrediCNAB400StatusEnum;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.bb.BBCNAB240StatusEnum;
import servicos.impl.dcc.bnb.Cnab400BNBStatusEnum;
import servicos.impl.dcc.bradesco.DCOBradescoOcorrenciaEnum;
import servicos.impl.dcc.caixa.CaixaCNAB240StatusEnum;
import servicos.impl.dcc.cielo.DCCCieloStatusEnum;
import servicos.impl.dcc.itau.BBCnab400ItauStatusEnum;
import servicos.impl.dcc.safra.Cnab400SafraStatusEnum;
import servicos.impl.dcc.santander.Cnab400SantanderStatusEnum;
import servicos.impl.dcc.sicoob.Cnab240SicoobStatusEnum;
import servicos.impl.dcc.sicoob.Cnab400SicoobStatusEnum;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RemessaItemVO extends SuperRemessaItemVO {

    @ChaveEstrangeira
    private MovPagamentoVO movPagamento = new MovPagamentoVO();
    @NaoControlarLogAlteracao
    private Date dataPrevistaDeposito = null;
    @ChaveEstrangeira
    private PessoaVO pessoa = new PessoaVO();
    private Integer nrTentativaParcela = 0;
    private Integer identificador;

    @ChaveEstrangeira
    private MovParcelaVO movParcela = new MovParcelaVO();
    @NaoControlarLogAlteracao
    private List<RemessaItemMovParcelaVO> movParcelas = new ArrayList<RemessaItemMovParcelaVO>();
    private Date dataVencimentoBoleto = null;

    @NaoControlarLogAlteracao
    private ClienteVO clienteVO = new ClienteVO();

    private TipoRemessaEnum tipo = TipoRemessaEnum.DESCONHECIDO;
    private String codigosRetorno = "";
    private TipoBaixaEnum tipoBaixa = TipoBaixaEnum.NAO_BAIXADO;
    private Double valorCredito = 0.0;
    @NaoControlarLogAlteracao
    private String mesesAbertos = "";
    @NaoControlarLogAlteracao
    private AutorizacaoCobrancaVO autorizacaoCobrancaVO;
    private String identificadorEmpresaFinanceiro = "";
    @NaoControlarLogAlteracao
    private transient Double valorRPS;
    @NaoControlarLogAlteracao
    private transient String descricaoRPS;
    @NaoControlarLogAlteracao
    private transient Date dataEmissaoRPS;
    @NaoControlarLogAlteracao
    private transient String cpfCnpjRPS;
    private Integer diaDoMesDescontoBoletoPagAntecipado = 0;
    private Double porcentagemDescontoBoletoPagAntecipado = 0.0;
    private Double valorDescontoBoletoPagAntecipado;
    private Double porcentagemDescontoBoleto = 0.0;
    private Double valorItemRemessa;
    private Double valorParcela;
    private Double valorMulta;
    private Double valorJuros;
    private boolean contabilizadaPacto = false;
    private boolean autorizarDebito = false;
    @NaoControlarLogAlteracao
    private NazgDTO nazgDTO;
    private String jsonEstorno;
    @NaoControlarLogAlteracao
    private ReciboPagamentoVO reciboPagamentoVO;

    public MovParcelaVO getMovParcela() {
        if(movParcela == null){
            movParcela = new MovParcelaVO();
        }
        return movParcela;
    }

    public void setMovParcela(MovParcelaVO movParcela) {
        this.movParcela = movParcela;
    }

    public void put(String key, String value) {
        props.put(key, value);
    }

    public MovPagamentoVO getMovPagamento() {
        return movPagamento;
    }

    public void setMovPagamento(MovPagamentoVO movPagamento) {
        this.movPagamento = movPagamento;
    }

    public Date getDataPrevistaDeposito() {
        return dataPrevistaDeposito;
    }

    public Date getDataPrevistaPagamento(){
        String data = this.props.get(DCCAttEnum.DataVencimento.name());
        Date dataConvertida = null;
        if(!UteisValidacao.emptyString(data))
            try {
                dataConvertida = Calendario.getDate("ddMMyyyy", data);
            } catch (ParseException e) {}
        return dataConvertida;
    }

    public void setDataPrevistaDeposito(Date dataPrevistaDeposito) {
        this.dataPrevistaDeposito = dataPrevistaDeposito;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }
    public String getNomePessoaApresentar(){
        return pessoa.getNome();
    }
    public String getCodigoRemessa(){
        return getRemessa().getCodigo().toString();
    }
    public String getCodigoMovParcela(){
        return getMovParcela().getCodigo().toString();
    }
    public String getDescricao(){
        if(!movParcela.getDescricao().isEmpty()){
            return !movParcela.descricao.isEmpty() ? movParcela.descricao : "***Estornada***";
        }else{
            return "";
        }
    }

    public boolean isItemCancelado() {
        if (getTipo().equals(TipoRemessaEnum.EDI_CIELO) || getTipo().equals(TipoRemessaEnum.GET_NET) || getTipo().equals(TipoRemessaEnum.DCC_BIN) ) {
            String statusVenda = get(DCCAttEnum.StatusVenda.name());
            if (statusVenda != null && statusVenda.equals(DCCCieloStatusEnum.Status00.getId())) {
                if (movPagamento.getCodigo() == 0 && !movParcela.getSituacao().equals("PG") &&
                        (getRemessa() != null && getRemessa().getSituacaoRemessa() != null && !getRemessa().getSituacaoRemessa().equals(SituacaoRemessaEnum.ERRO_RETORNO))) {
                    return true;
                }
            }
        }
        return false;
    }

    public String getStyleMovPagamento() {
        if (isItemCancelado()) {
            return "color: orange;";
        } else if (movPagamento.getCodigo() == 0 && movParcela.getSituacao().equals("PG")) {
            return "color: red;";
        } else if (movPagamento.getCodigo() != 0) {
            return "color: blue;";
        }
        return "";
    }

    public String getTitleMovPagamento() {
        if (isItemCancelado()) {
            return "Item cancelado";
        } else if (movPagamento.getCodigo() == 0 && movParcela.getSituacao().equals("PG")) {
            return "Outra Remessa ou Outra Forma de Pagamento pagou a Parcela";
        } else if (movPagamento.getCodigo() != 0) {
            return "Código do Movimento de Pagamento";
        }
        return "";
    }

    public String getMovPagamento_Apresentar() {
        if (isItemCancelado()) {
            return "Pgt. Cancelado";
        } else if (movPagamento.getCodigo() == 0 && movParcela.getSituacao().equals("PG")) {
            return "Pgt. OUTRO";
        } else if (movPagamento.getCodigo() != 0) {
            return "Pgt. " + movPagamento.getCodigo();
        }
        return "";
    }

    public String getMovPagamentoBoleto() {
        if (movPagamento.getCodigo() == 0 && isTodasParcelasPagas()) {
            return "Pgt. OUTRO";
        } else if (movPagamento.getCodigo() != 0) {
            return "Pgt. " + movPagamento.getCodigo();
        }
        if (movPagamento.getCodigo() == 0 && (!isTodasParcelasPagas() && isTemParcelasPagas())) {
            return "Pgt. PARCIAL";
        }

        if (getMovParcelas().size() == 0) {
            return "***Estornado***";
        }
        return "";
    }
    public String getValorParcelaNumerico(){
        return getMovParcela().getValorParcelaNumerico();
    }
    public String getSituacaoParcela(){
        return isDuplicidade() ? "***Duplicidade***" : movParcela.getSituacao_Apresentar();
    }
    public String getVencimento(){
        return movParcela.getDataVencimento_Apresentar();
    }
    public String getCodigoContrato(){
        return getMovParcela().getContrato().getCodigo().toString();
    }
    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    @Override
    public Integer getCodigo() {
        return super.getCodigo();
    }

    @Override
    public void setCodigo(Integer codigo) {
        super.setCodigo(codigo);
    }

    public String getAutorizacao() {
        return get(DCCAttEnum.CodigoAutorizacao.name()) != null ? get(DCCAttEnum.CodigoAutorizacao.name()) : "";
    }

    public boolean isDuplicidade() {
        return this.movParcela.situacao.equals("PG") && (this.movPagamento.codigo == 0)
                && (!this.getAutorizacao().isEmpty()) && (!this.getAutorizacao().startsWith("00"));
    }

    public String getNomePessoa() {
        return get("nomePessoa") != null ? get("nomePessoa") : "";
    }

    public String getLogEstorno() {
        try {
            if (!UteisValidacao.emptyString(getJsonEstorno().trim())) {

                JSONObject json = new JSONObject(getJsonEstorno());

                StringBuilder log = new StringBuilder();
                if (!UteisValidacao.emptyNumber(json.getInt("Recibo"))) {
                    log.append("Recibo: ").append(json.getInt("Recibo")).append("<br/>");
                }

                if (!UteisValidacao.emptyString(json.getString("UsuarioEstornoNome"))) {
                    log.append("Usuário Estorno: ").append(json.getString("UsuarioEstornoNome")).append("<br/>");
                }

                if (!UteisValidacao.emptyString(json.getString("DataEstorno"))) {
                    log.append("Data Estorno: ").append(json.getString("DataEstorno")).append("<br/>");
                }

                if (log.length() > 0) {
                    return log.toString();
                }
            }
        } catch (Exception ignored) {
        }
        return get("logEstorno") != null ? get("logEstorno") : "";
    }
    
    public String getNome(){
        return getMovParcela().getPessoa_Apresentar();
    }

    public String getValorCartaoMascaradoOuAgenciaConta() {
        if (getTipo().equals(TipoRemessaEnum.EDI_CIELO) || getTipo().equals(TipoRemessaEnum.GET_NET) || getTipo().equals(TipoRemessaEnum.DCC_BIN)  ) {
            return get(APF.CartaoMascarado) != null ? get(APF.CartaoMascarado) : "";
        } else if (getTipo().equals(TipoRemessaEnum.BB_DCO)) {
            return get(DCCAttEnum.AgenciaDebito.name()) != null && get(DCCAttEnum.ContaCorrente.name()) != null
                    ? get(DCCAttEnum.AgenciaDebito.name()) + " " + get(DCCAttEnum.ContaCorrente.name()) : "";
        } else if (getTipo().equals(TipoRemessaEnum.BRADESCO_DCO)
                || getTipo().equals(TipoRemessaEnum.ITAU_DCO)
                || getTipo().equals(TipoRemessaEnum.CAIXA_DCO)
                || getTipo().equals(TipoRemessaEnum.HSBC_DCO)) {

            String agencia = get(DCCAttEnum.AgenciaDebito.name());
            if (UteisValidacao.emptyString(agencia)) {
                agencia = get(DCCAttEnum.Agencia.name());
            }


            String agenciaDV = get(DCCAttEnum.AgenciaDebitoDigito.name());
            //adicionar dígito
            if (!UteisValidacao.emptyString(agencia) &&
                    !UteisValidacao.emptyString(agenciaDV)) {
                agencia += "-" + agenciaDV;
            }

            String conta = get(DCCAttEnum.ContaCorrenteDebito.name());
            if (UteisValidacao.emptyString(conta)) {
                conta = get(DCCAttEnum.ContaCorrente.name());
            }

            String contaDV = get(DCCAttEnum.ContaCorrenteDebitoDigito.name());
            if (UteisValidacao.emptyString(contaDV)) {
                contaDV = get(DCCAttEnum.ContaCorrenteDigito.name());
            }

            //adicionar dígito
            if (!UteisValidacao.emptyString(conta) &&
                    !UteisValidacao.emptyString(contaDV)) {
                conta += "-" + contaDV;
            }

            if (UteisValidacao.emptyString(agencia) || UteisValidacao.emptyString(conta)) {
                return "";
            }
            return agencia + " / " + conta;
        } else {
            return "";
        }
    }

    public String getValorTokenAragorn() {
        if (getTipo().equals(TipoRemessaEnum.EDI_CIELO) ||
                getTipo().equals(TipoRemessaEnum.GET_NET) ||
                getTipo().equals(TipoRemessaEnum.DCC_BIN)) {
            return get(APF.TokenAragorn);
        } else {
            return "";
        }
    }

    public String getIdentificadorRemessa(){
        return remessa.getIdentificador();
    }

    public String getIdentificadorClienteEmpresa() {
        return get(DCCAttEnum.IdentificadorClienteEmpresa.name()) != null ? get(DCCAttEnum.IdentificadorClienteEmpresa.name()) : "";
    }

    public Integer getNrTentativaParcela() {
        return nrTentativaParcela;
    }

    public void setNrTentativaParcela(Integer nrTentativaParcela) {
        this.nrTentativaParcela = nrTentativaParcela;
    }

    public boolean isApresentarOperacaoRetornoManual() {
        return (getTipo().equals(TipoRemessaEnum.ITAU_BOLETO) || getTipo().equals(TipoRemessaEnum.BOLETO))
                && (get(DCCAttEnum.StatusVenda.name()) == null ||
                    get(DCCAttEnum.StatusVenda.name()).equals("") ||
                    get(DCCAttEnum.StatusVenda.name()).equals(BBCnab400ItauStatusEnum.Status02.getId()) ||
                    get(DCCAttEnum.StatusVenda.name()).equals(BBCnab400ItauStatusEnum.Status03.getId()) ||
                    get(DCCAttEnum.StatusVenda.name()).equals(BBCnab400ItauStatusEnum.Status21.getId()));
    }

    public List<RemessaItemMovParcelaVO> getMovParcelas() {
        if (movParcelas == null) {
            return new ArrayList<RemessaItemMovParcelaVO>();
        }
        return movParcelas;
    }

    public void setMovParcelas(List<RemessaItemMovParcelaVO> movParcelas) {
        this.movParcelas = movParcelas;
    }

    public Date getDataVencimentoBoleto() {
        return dataVencimentoBoleto;
    }

    public void setDataVencimentoBoleto(Date dataVencimentoBoleto) {
        this.dataVencimentoBoleto = dataVencimentoBoleto;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Double getValorBoleto() {
        double valorBoleto = 0.0;
        for (RemessaItemMovParcelaVO parcela : movParcelas) {
            valorBoleto += parcela.getValorOriginal();
        }
        if (valorCredito > 0.0) {
            valorBoleto -= valorCredito;
        }
        return valorBoleto;
    }

    public String getValorBoletoApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorBoleto());
    }

    public TipoRemessaEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoRemessaEnum tipo) {
        this.tipo = tipo;
    }

    public boolean isMostrarBotaoImprimir() {
        boolean existeParcelaPaga = false;
        for (RemessaItemMovParcelaVO itemMovParcelaVO : getMovParcelas()) {
            if (itemMovParcelaVO.getMovParcelaVO().getSituacao().equals("PG")) {
                existeParcelaPaga = true;
            }
        }

        return getMovPagamento().getCodigo() == 0 && !existeParcelaPaga && getMovParcelas().size() > 0;
    }

    public boolean isTodasParcelasPagas() {
        if (getMovParcelas().size() == 0) {
            return false;
        } else {
            for (RemessaItemMovParcelaVO itemMovParcelaVO : getMovParcelas()) {
                if (StringUtils.isBlank(itemMovParcelaVO.getMovParcelaVO().getSituacao())) {
                    return false;
                }

                if (itemMovParcelaVO.getMovParcelaVO().getSituacao().equals("EA")) {
                    return false;
                }
            }
            return true;
        }
    }

    public boolean isTemParcelasPagas() {
        if (getMovParcelas().size() == 0) {
            return false;
        } else {
            for (RemessaItemMovParcelaVO itemMovParcelaVO : getMovParcelas()) {
                if (itemMovParcelaVO.getMovParcelaVO().getSituacao().equals("PG")) {
                    return true;
                }
            }
            return false;
        }
    }

    public String getCodigosRetorno() {
        if (codigosRetorno == null) {
            codigosRetorno = "";
        }
        return codigosRetorno;
    }

    public void setCodigosRetorno(String codigosRetorno) {
        this.codigosRetorno = codigosRetorno;
    }

    public boolean processouRetorno(String codigoRetorno) {
        return getCodigosRetorno().contains(codigoRetorno + "|");
    }

    public String getMesesAbertos() {
        return mesesAbertos;
    }

    public void setMesesAbertos(String mesesAbertos) {
        this.mesesAbertos = mesesAbertos;
    }

    public String getCepSacado() {
        if (getPessoa() == null) {
            return "";
        }
        if (getPessoa().getEnderecoVOs().size() > 0) {
            return getPessoa().getEnderecoVOs().get(0).getCep();
        }
        return "";
    }

    public TipoBaixaEnum getTipoBaixa() {
        return tipoBaixa;
    }

    public void setTipoBaixa(TipoBaixaEnum tipoBaixa) {
        this.tipoBaixa = tipoBaixa;
    }

    public Double getValorCredito() {
        if (valorCredito == null) {
            valorCredito = 0.0;
        }
        return valorCredito;
    }

    public void setValorCredito(Double valorCredito) {
        this.valorCredito = valorCredito;
    }

    public boolean isLayoutFebrabanDCO() {
        return (getTipo() != null) &&
                (getTipo().equals(TipoRemessaEnum.SANTANDER_DCO) ||
                        getTipo().equals(TipoRemessaEnum.BRADESCO_DCO) ||
                        getTipo().equals(TipoRemessaEnum.BB_DCO) ||
                        getTipo().equals(TipoRemessaEnum.CAIXA_SICOV_DCO));
    }

    public boolean isLiquidado(String statusVenda) {
        if (getTipo().equals(TipoRemessaEnum.BOLETO) || getTipo().equals(TipoRemessaEnum.ITAU_BOLETO)) {
            switch (getRemessa().getConvenioCobranca().getBanco().getCodigoBanco()) {
                case JBoleto.BRADESCO:
                    return statusVenda.equals(DCOBradescoOcorrenciaEnum.Ocor06.getId())
                            || statusVenda.equals(DCOBradescoOcorrenciaEnum.Ocor17.getId());
                case JBoleto.ITAU:
                    return statusVenda.equals(BBCnab400ItauStatusEnum.Status06.getId());
                case JBoleto.SANTANDER:
                    return statusVenda.equals(Cnab400SantanderStatusEnum.Status06.getId());
                case JBoleto.BANCOOB:
                    return statusVenda.equals(Cnab400SicoobStatusEnum.Status05.getId())
                            || statusVenda.equals(Cnab400SicoobStatusEnum.Status06.getId())
                            || statusVenda.equals(Cnab400SicoobStatusEnum.Status15.getId())
                            || statusVenda.equals(Cnab240SicoobStatusEnum.Status05.getId())
                            || statusVenda.equals(Cnab240SicoobStatusEnum.Status06.getId())
                            || statusVenda.equals(Cnab240SicoobStatusEnum.Status15.getId());
                case JBoleto.BNB:
                    return statusVenda.equalsIgnoreCase(Cnab400BNBStatusEnum.Status06.getId())
                            || statusVenda.equalsIgnoreCase(Cnab400BNBStatusEnum.Status07.getId())
                            || statusVenda.equalsIgnoreCase(Cnab400BNBStatusEnum.Status08.getId());
                case JBoleto.BANCO_DO_BRASIL:
                    return statusVenda.equalsIgnoreCase(BBCNAB240StatusEnum.Status06.getId())
                            || statusVenda.equalsIgnoreCase(BBCNAB240StatusEnum.Status17.getId());
                case JBoleto.CAIXA_ECONOMICA:
                    return statusVenda.equalsIgnoreCase(CaixaCNAB240StatusEnum.Status06.getId())
                            || statusVenda.equalsIgnoreCase(CaixaCNAB240StatusEnum.Status17.getId());
                case JBoleto.SAFRA:
                    return statusVenda.equalsIgnoreCase(Cnab400SafraStatusEnum.Status06.getId());
                case JBoleto.SICREDI:
                    return statusVenda.equalsIgnoreCase(SicrediCNAB400StatusEnum.Status06.getId());
                default:
                    return false;
            }
        } else if (getTipo().equals(TipoRemessaEnum.DAYCOVAL_BOLETO)) {
            return statusVenda.equals(BBCnab400ItauStatusEnum.Status06.getId());
        }
        return false;
    }

    public boolean isRegistroAceito(String statusVenda) {
        if (getTipo().equals(TipoRemessaEnum.BOLETO) || getTipo().equals(TipoRemessaEnum.ITAU_BOLETO)) {
            switch (getRemessa().getConvenioCobranca().getBanco().getCodigoBanco()) {
                case JBoleto.BRADESCO:
                    return statusVenda.equals(DCOBradescoOcorrenciaEnum.Ocor02.getId());
                case JBoleto.ITAU:
                    return statusVenda.equals(BBCnab400ItauStatusEnum.Status02.getId());
                case JBoleto.SANTANDER:
                    return statusVenda.equals(Cnab400SantanderStatusEnum.Status02.getId());
                case JBoleto.BANCOOB:
                    return statusVenda.equals(Cnab400SicoobStatusEnum.Status02.getId()) || statusVenda.equals(Cnab240SicoobStatusEnum.Status02.getId());
                case JBoleto.BNB:
                    return statusVenda.equals(Cnab400BNBStatusEnum.Status02.getId());
                case JBoleto.BANCO_DO_BRASIL:
                    return statusVenda.equalsIgnoreCase(BBCNAB240StatusEnum.Status02.getId());
                case JBoleto.CAIXA_ECONOMICA:
                    return statusVenda.equalsIgnoreCase(CaixaCNAB240StatusEnum.Status02.getId());
                case JBoleto.SAFRA:
                    return statusVenda.equalsIgnoreCase(Cnab400SafraStatusEnum.Status02.getId());
                case JBoleto.SICREDI:
                    return statusVenda.equalsIgnoreCase(SicrediCNAB400StatusEnum.Status02.getId());
                default:
                    return false;
            }
        } else if (getTipo().equals(TipoRemessaEnum.DAYCOVAL_BOLETO)) {
            return statusVenda.equals(BBCnab400ItauStatusEnum.Status02.getId());
        }
        return false;
    }

    public boolean isStatusPrevisto(String statusVenda) {
        if (getTipo().equals(TipoRemessaEnum.BOLETO) || getTipo().equals(TipoRemessaEnum.ITAU_BOLETO)) {
            switch (getRemessa().getConvenioCobranca().getBanco().getCodigoBanco()) {
                case JBoleto.BRADESCO:
                    for (DCOBradescoOcorrenciaEnum e : DCOBradescoOcorrenciaEnum.values()) {
                        if (statusVenda.equals(e.getId())) {
                            return true;
                        }
                    }
                    break;
                case JBoleto.ITAU:
                    for (BBCnab400ItauStatusEnum e : BBCnab400ItauStatusEnum.values()) {
                        if (statusVenda.equals(e.getId())) {
                            return true;
                        }
                    }
                    break;
                case JBoleto.SANTANDER:
                    for (Cnab400SantanderStatusEnum e : Cnab400SantanderStatusEnum.values()) {
                        if (statusVenda.equals(e.getId())) {
                            return true;
                        }
                    }
                    break;
                case JBoleto.BANCOOB:
                    for (Cnab400SicoobStatusEnum e : Cnab400SicoobStatusEnum.values()) {
                        if (statusVenda.equals(e.getId())) {
                            return true;
                        }
                    }
                    break;
                case JBoleto.BNB:
                    for (Cnab400BNBStatusEnum e : Cnab400BNBStatusEnum.values()) {
                        if (statusVenda.equals(e.getId())) {
                            return true;
                        }
                    }
                    break;
                case JBoleto.BANCO_DO_BRASIL:
                    for (BBCNAB240StatusEnum e : BBCNAB240StatusEnum.values()) {
                        if (statusVenda.equals(e.getId())) {
                            return true;
                        }
                    }
                    break;
                case JBoleto.CAIXA_ECONOMICA:
                    for (CaixaCNAB240StatusEnum e : CaixaCNAB240StatusEnum.values()) {
                        if (statusVenda.equals(e.getId())) {
                            return true;
                        }
                    }
                    break;
                case JBoleto.SAFRA:
                    for (Cnab400SafraStatusEnum e : Cnab400SafraStatusEnum.values()) {
                        if (statusVenda.equals(e.getId())) {
                            return true;
                        }
                    }
                    break;
                case JBoleto.SICREDI:
                    for (SicrediCNAB400StatusEnum e : SicrediCNAB400StatusEnum.values()) {
                        if (statusVenda.equals(e.getId())) {
                            return true;
                        }
                    }
                    break;
                default:
                    return false;
            }
        } else if (getTipo().equals(TipoRemessaEnum.DAYCOVAL_BOLETO)) {
            for (BBCnab400ItauStatusEnum e : BBCnab400ItauStatusEnum.values()) {
                if (statusVenda.equals(e.getId())) {
                    return true;
                }
            }
        }
        return false;
    }

    public AutorizacaoCobrancaVO getAutorizacaoCobrancaVO() {
        return autorizacaoCobrancaVO;
    }

    public void setAutorizacaoCobrancaVO(AutorizacaoCobrancaVO autorizacaoCobrancaVO) {
        this.autorizacaoCobrancaVO = autorizacaoCobrancaVO;
    }
    public String obterDVNossoNumeroCNB400(String codigo){
        String nossoNumero =  StringUtilities.formatarCampo(new BigDecimal(codigo), 7);
        int total = 0 ;
        for(int e = 0 ; e < 7 ; e++){
            int num = new Integer(nossoNumero.substring(6 - e,7 - e));
            int multiplicador = (e + 2);
            total += (num * multiplicador);
        }
        int resto = (total % 11);
        int digito = resto == 10 ?  1 : (resto == 1 || resto == 0 ? 0 :  11 - resto );
        return digito+"";
    }
    public String obterNossoNumeroCNB400_Apresentar(){
        String nossoNumero =  StringUtilities.formatarCampo(new BigDecimal(getCodigo()), 7);
        return nossoNumero+"-"+obterDVNossoNumeroCNB400(nossoNumero);
    }
    public String getIdentificadorEmpresaFinanceiro() {
        return identificadorEmpresaFinanceiro;
    }

    public void setIdentificadorEmpresaFinanceiro(String identificadorEmpresaFinanceiro) {
        this.identificadorEmpresaFinanceiro = identificadorEmpresaFinanceiro;
    }

    public double getValorRPS() {
        if (valorRPS == null) {
            valorRPS = 0.0;
        }
        return valorRPS;
    }

    public void setValorRPS(double valorRPS) {
        this.valorRPS = valorRPS;
    }

    public String getDescricaoRPS() {
        if (descricaoRPS == null) {
            descricaoRPS = "";
        }
        return descricaoRPS;
    }

    public void setDescricaoRPS(String descricaoRPS) {
        this.descricaoRPS = descricaoRPS;
    }

    public Date getDataEmissaoRPS() {
        if(dataEmissaoRPS == null) {
            dataEmissaoRPS = Calendario.hoje();
        }
        return dataEmissaoRPS;
    }

    public void setDataEmissaoRPS(Date dataEmissaoRPS) {
        this.dataEmissaoRPS = dataEmissaoRPS;
    }

    public String getCpfCnpjRPS() {
        if (cpfCnpjRPS == null) {
            cpfCnpjRPS = "";
        }
        return cpfCnpjRPS;
    }

    public void setCpfCnpjRPS(String cpfCnpjRPS) {
        this.cpfCnpjRPS = cpfCnpjRPS;
    }

    public void setIdentificador(Integer identificador) {
        this.identificador = identificador;
    }

    public Integer getIdentificador() {
        return identificador;
    }

    public Integer getDiaDoMesDescontoBoletoPagAntecipado() {
        return diaDoMesDescontoBoletoPagAntecipado;
    }

    public void setDiaDoMesDescontoBoletoPagAntecipado(Integer diaDoMesDescontoBoletoPagAntecipado) {
        this.diaDoMesDescontoBoletoPagAntecipado = diaDoMesDescontoBoletoPagAntecipado;
    }

    public Double getPorcentagemDescontoBoletoPagAntecipado() {
        return porcentagemDescontoBoletoPagAntecipado;
    }

    public void setPorcentagemDescontoBoletoPagAntecipado(Double porcentagemDescontoBoletoPagAntecipado) {
        this.porcentagemDescontoBoletoPagAntecipado = porcentagemDescontoBoletoPagAntecipado;
    }

    public Double getValorDescontoBoletoPagAntecipado() {
        if (valorDescontoBoletoPagAntecipado == null) {
            valorDescontoBoletoPagAntecipado = 0.0;
        }
        return valorDescontoBoletoPagAntecipado;
    }

    public void setValorDescontoBoletoPagAntecipado(Double valorDescontoBoletoPagAntecipado) {
        this.valorDescontoBoletoPagAntecipado = valorDescontoBoletoPagAntecipado;
    }

    public Boolean possuiDesconto(){
        return !UteisValidacao.emptyNumber(getDiaDoMesDescontoBoletoPagAntecipado()) && !UteisValidacao.emptyNumber(getPorcentagemDescontoBoletoPagAntecipado());
    }

    public Boolean possuiDescontoConvenio() {
        if (!possuiDesconto() && getDataPgtoDesconto())
            return !UteisValidacao.emptyNumber(getRemessa().getConvenioCobranca().getDescontoBoleto());
        return false;
    }

    public Boolean getDataPgtoDesconto(){
        return Calendario.hoje().before(Calendario.subtrairDias(getDataVencimentoBoleto(), 0));
    }

    public Date getDataPagamentoAntecipado() {
        Date dataPagAntecipado = null;
        try {
            String data = (getDiaDoMesDescontoBoletoPagAntecipado() < 10 ? ("0" + getDiaDoMesDescontoBoletoPagAntecipado()) : getDiaDoMesDescontoBoletoPagAntecipado()) + "/" + Uteis.getDataAplicandoFormatacao(getDataVencimentoBoleto(), "MM/yyyy");
            dataPagAntecipado = Uteis.getDate(data, "dd/MM/yyyy");
            if (Calendario.maior(dataPagAntecipado, getDataVencimentoBoleto())) {
                dataPagAntecipado = Uteis.somarMeses(dataPagAntecipado, -1);
            }
        }catch (Exception e){
            Uteis.logar(e, this.getClass());
        }
        return  Calendario.menorOuIgual(Calendario.hoje(), dataPagAntecipado) ? dataPagAntecipado : null;
    }


    public Double getValorItemRemessa() {
        if (valorItemRemessa == null) {
            valorItemRemessa =  0.0;
        }
        return valorItemRemessa;
    }

    public void setValorItemRemessa(Double valorItemRemessa) {
        this.valorItemRemessa = valorItemRemessa;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        RemessaItemVO that = (RemessaItemVO) o;

        return getCodigo().equals(that.getCodigo());
    }

    @Override
    public int hashCode() {
        return getCodigo();
    }

    public Double getValorParcela() {
        if (valorParcela == null) {
            valorParcela =  0.0;
        }
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public Double getValorMulta() {
        if (valorMulta == null) {
            valorMulta =  0.0;
        }
        return valorMulta;
    }

    public void setValorMulta(Double valorMulta) {
        this.valorMulta = valorMulta;
    }

    public Double getValorJuros() {
        if (valorJuros == null) {
            valorJuros =  0.0;
        }
        return valorJuros;
    }

    public void setValorJuros(Double valorJuros) {
        this.valorJuros = valorJuros;
    }

    public String getValorJurosNumerico() {
        return Formatador.formatarValorNumerico(this.getValorJuros(), Formatador.FRMT_NUM_PADRAO);
    }

    public String getValorMultaNumerico() {
        return Formatador.formatarValorNumerico(this.getValorMulta(), Formatador.FRMT_NUM_PADRAO);
    }

    public String getValorItemRemessaNumerico() {
        return Formatador.formatarValorNumerico(this.getValorItemRemessa(), Formatador.FRMT_NUM_PADRAO);
    }

    public boolean isContabilizadaPacto() {
        return contabilizadaPacto;
    }

    public void setContabilizadaPacto(boolean contabilizadaPacto) {
        this.contabilizadaPacto = contabilizadaPacto;
    }

    public Date getDataCobrancaDCO() {
        try {
            return LayoutRemessaBase.getDataVencimento(this.getMovParcela().getDataVencimento(), remessa.getDataRegistro(), remessa);
        } catch (Exception ex) {
            return null;
        }
    }

    public boolean isAutorizarDebito() {
        return autorizarDebito;
    }

    public void setAutorizarDebito(boolean autorizarDebito) {
        this.autorizarDebito = autorizarDebito;
    }

    public NazgDTO getNazgDTO() {
        if (nazgDTO == null) {
            nazgDTO = new NazgDTO();
        }
        return nazgDTO;
    }

    public void setNazgDTO(NazgDTO nazgDTO) {
        this.nazgDTO = nazgDTO;
    }

    public Double getPorcentagemDescontoBoleto() {
        return porcentagemDescontoBoleto == null ? 0.0 : porcentagemDescontoBoleto;
    }

    public void setPorcentagemDescontoBoleto(Double porcentagemDescontoBoleto) {
        this.porcentagemDescontoBoleto = porcentagemDescontoBoleto;
    }

    public boolean isPermiteRemoverParcelaBoleto() {
        if (UteisValidacao.emptyList(getMovParcelas())) {
            return false;
        }

        for (RemessaItemMovParcelaVO itemMovParcelaVO : getMovParcelas()) {
            if (itemMovParcelaVO.getMovParcelaVO().getSituacao().equals("PG")) {
                return false;
            }
        }
        return true;
    }

    public List<MovParcelaVO> getListaParcelasApresentar() {
        List<MovParcelaVO> lista = new ArrayList<>();
        if (getRemessa().isNovoFormato() && !UteisValidacao.emptyList(getMovParcelas())) {
            for (RemessaItemMovParcelaVO item : getMovParcelas()) {
                lista.add(item.getMovParcelaVO());
            }
        } else if (getMovParcela() != null) {
            lista.add(getMovParcela());
        }
        return lista;
    }

    public String getJsonEstorno() {
        if (jsonEstorno == null) {
            jsonEstorno = "";
        }
        return jsonEstorno;
    }

    public void setJsonEstorno(String jsonEstorno) {
        this.jsonEstorno = jsonEstorno;
    }

    public ReciboPagamentoVO getReciboPagamentoVO() {
        if (reciboPagamentoVO == null) {
            reciboPagamentoVO = new ReciboPagamentoVO();
        }
        return reciboPagamentoVO;
    }

    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamentoVO) {
        this.reciboPagamentoVO = reciboPagamentoVO;
    }

    public TransacaoDTO toTransacaoDTO(ConvenioCobrancaVO convenioCobrancaVO) {
        TransacaoDTO transacaoDTO = new TransacaoDTO();
        transacaoDTO.setIdReferencia(this.getCodigo());

        if (convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC)) {
            String tokenAragorn = this.getProps().get(APF.TokenAragorn);
            if (Uteis.isAmbienteDesenvolvimentoTeste()) {
                tokenAragorn = "cde730a96771663c4484f2ed7e9198cb";
            }
            transacaoDTO.setTokenAragorn(tokenAragorn);
        }

        transacaoDTO.setCliente(this.getPessoa().toClienteDTO(null));
        transacaoDTO.setValor(this.getValorItemRemessa());

        boolean parceladoLojista;
        Integer numeroParcelasOperadora;
        if (this.getRemessa().isNovoFormato()) {
            parceladoLojista = this.getMovParcelas().get(0).getMovParcelaVO().isParceladoLojista();
            numeroParcelasOperadora = this.getMovParcelas().get(0).getMovParcelaVO().getNumeroParcelasOperadora();
        } else {
            parceladoLojista = this.getMovParcela().isParceladoLojista();
            numeroParcelasOperadora = this.getMovParcela().getNumeroParcelasOperadora();
        }

        transacaoDTO.setParceladoLojista(parceladoLojista);
        transacaoDTO.setNrParcelas(numeroParcelasOperadora);
        transacaoDTO.setDescricao("");
        transacaoDTO.setTexto("");
        transacaoDTO.setInstrucoes(convenioCobrancaVO.getInstrucoesBoleto());
        transacaoDTO.setInstrucoesAdicional("");
        transacaoDTO.setDataRegistro(Calendario.getDataAplicandoFormatacao(this.getRemessa().getDataRegistro(), "yyyyMMddHHmmss"));
        transacaoDTO.setVencimento(Calendario.getDataAplicandoFormatacao(this.getDataVencimentoBoleto(), "yyyyMMdd"));
        return transacaoDTO;
    }

    public String getBandeira() {
        return get(DCCAttEnum.Bandeira.name()) != null ? get(DCCAttEnum.Bandeira.name()) : "";
    }

    public Integer getCodigoStatusPactoPay() {
        return getRemessa().getSituacaoRemessa() != null ? getRemessa().getSituacaoRemessa().getStatusPactoPayEnum().getCodigo() : StatusPactoPayEnum.NENHUM.getCodigo();
    }

    public boolean isItemRemessaTemParcelaEmAberto() {
        for (RemessaItemMovParcelaVO itemVO : this.getMovParcelas())
            if (itemVO.getMovParcelaVO().getSituacao().equals("EA")) {
                return true;
            }
        return false;
    }
}

package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.estudio.modelo.CancelamentoSessaoVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.CancelamentoContratoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.NumeroPorExtenso;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.basico.PessoaVO;

public class ReciboDevolucaoVO extends SuperVO {

	
	@ChavePrimaria
	private Integer codigo;
	@ChaveEstrangeira
	private ContratoVO contrato;
	@ChaveEstrangeira
	private MovProdutoVO produtoVO;
	private Date dataDevolucao;
	private Double valorDevolucao;
	@ChaveEstrangeira
	private UsuarioVO responsavelDevolucao;

    private Double valorOriginal;

	private Double valorTotalPagoPeloCliente;
	private Double valorUtilizadoPeloCliente;
	private Double valorTotalSomaProdutoContratos;
	private Double valorTaxaCancelamento;
	private Double valorMultaCancelamento;
	private Double valorDevolvidoEmDinheiro;
	private Double valorContrato;
	private Double valorEmContaCorrente;
	private Double valorRealDevolucao;
    private Integer pessoa;
	
	private List<ChequeVO> chequesDevolvidos;
	private List<CartaoCreditoVO> cartoesDevolvidos;
        private List<MovParcelaVO> parcelasEdicaoPagamento;
	
	private Boolean quitacao = Boolean.FALSE;
	private Boolean quitacaoManual = Boolean.FALSE;
	private Boolean liberacao = Boolean.FALSE;
	private Boolean liberacaoDevolucao = Boolean.FALSE;
	private Boolean devolucaoManual = Boolean.FALSE;
	private Integer prodDevolucao = 0;
	private Integer prodRecebiveis = 0;
        private Integer reciboEditado = 0;
	private List<Integer> listaPagamentos; //pagamentos que foram devolvidos, usado no caso de estorno
	private Double valorBaseContrato;
	private double valorDevolverSaldoTransferenciaCredito = 0; // atributo transient
	
	public String getValorSaldoCCApresentar(){
		return Formatador.formatarValorMonetario(this.getValorEmContaCorrente());
	}
	
	public String getValorContratoApresentar(){
		return Formatador.formatarValorMonetario(this.getValorContrato());
	}

	public String getDescricao(String moeda) {
		          StringBuilder descricao = new StringBuilder();

            if (getContrato().getCodigo() > 0) {
                descricao.append("\nValor dos Custos Administrativos:  -" + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(this.getValorTaxaCancelamento()));
                descricao.append("\nValor da Multa de Cancelamento:  -"  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(this.getValorMultaCancelamento()));
                descricao.append("\n");
                this.getParcelasEdicaoPagamento();
                if (this.getValorDevolvidoEmDinheiro() > 0.00) {
                    if (!getLiberacaoDevolucao()) {
                        Double valor = this.getValorDevolvidoEmDinheiro();
                        descricao.append("\nValor devolvido em dinheiro:  " + moeda + " ").append(Formatador.formatarValorMonetarioSemMoeda(valor));
                    }
                } else  if (this.getValorDevolvidoEmDinheiro() < 0.00){
                    descricao.append("\nResíduo a ser quitado pelo cliente:  "  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(this.getValorDevolvidoEmDinheiro() * -1) + (getLiberacao() ? " (ISENTADO)" : "")) ;
                }
                Double valorRecebiveis = getValorRecebiveis();
                if (valorRecebiveis > 0) {
                    descricao.append("\nValor devolvido em recebíveis:  "  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(valorRecebiveis));
                }
                if ((this.getValorDevolvidoEmDinheiro() > 0 && !getLiberacaoDevolucao())&& getValorRecebiveis() > 0.0) {
                    Double valor = this.getValorDevolvidoEmDinheiro() + getValorRecebiveis();
                    descricao.append("\nValor final devolvido:  "  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(valor));
                }
            } else if (produtoVO != null && !UteisValidacao.emptyNumber(produtoVO.getCodigo())) {
                if (!getQuitacao()) {
                    descricao.append("\nValor devolvido em dinheiro:  "  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(this.getValorDevolvidoEmDinheiro()));
                } else {
                    descricao.append("\nResíduo a ser quitado pelo cliente:  "  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(this.getValorDevolvidoEmDinheiro()));
                }
                Double valorRecebiveis = getValorRecebiveis();
                if (valorRecebiveis > 0) {
                    descricao.append("\n\nValor devolvido em recebíveis:  "  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(valorRecebiveis));
                }
                descricao.append("\nValor final devolvido:  "  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(this.getValorDevolucao()));
                
            } else if (!UteisValidacao.emptyNumber(reciboEditado)) {
                descricao.append("\nRecibo Editado:  " + reciboEditado);
                Double valorRecebiveis = getValorRecebiveis();
                descricao.append("\n\nValor devolvido em recebíveis:  "  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(valorRecebiveis));
                if(!UteisValidacao.emptyList(parcelasEdicaoPagamento)){
                    descricao.append("\nParcela(s) gerada(s): "+getCodigosParcelasEdicao());
                }
            } else {
                descricao.append("\nValor devolvido em dinheiro:  "  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(this.getValorDevolvidoEmDinheiro()));
                Double valorRecebiveis = getValorRecebiveis();
                descricao.append("\n\nValor devolvido em recebíveis:  "  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(valorRecebiveis));
                descricao.append("\nValor final devolvido:  "  + moeda + " " + Formatador.formatarValorMonetarioSemMoeda(this.getValorDevolucao()));
            }
            return descricao.toString();
	}

	public String getDescricao(){
		return getDescricao(produtoVO.getEmpresa().getMoeda().isEmpty() ? "R$" : produtoVO.getEmpresa().getMoeda());
	}
	
	public String getInformacoes(){
		StringBuilder descricao = new StringBuilder();
		if (getContrato().getCodigo() > 0){
			descricao.append("Valor do Contrato:  "+Formatador.formatarValorMonetarioSemMoeda(this.getValorContrato()));
		} else if(produtoVO != null && !UteisValidacao.emptyNumber(produtoVO.getCodigo())){
			descricao.append("Produto:  "+this.getProdutoVO().getDescricao());
			descricao.append("\nValor:  "+Formatador.formatarValorMonetarioSemMoeda(this.getValorContrato()));
		}else{
                    descricao.append("Cancelamento conta-corrente do cliente");
                }
		return descricao.toString();
	}
	
	public Integer getCodigo() {
		return codigo;
	}
	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}
	public ContratoVO getContrato() {
		return contrato;
	}
	public void setContrato(ContratoVO contrato) {
		this.contrato = contrato;
	}
	public Date getDataDevolucao() {
		return dataDevolucao;
	}
	public void setDataDevolucao(Date dataDevolucao) {
		this.dataDevolucao = dataDevolucao;
	}
	public Double getValorDevolucao() {
		return valorDevolucao;
	}
	public void setValorDevolucao(Double valorDevolucao) {
		this.valorDevolucao = valorDevolucao;
	}
	public UsuarioVO getResponsavelDevolucao() {
		return responsavelDevolucao;
	}
	public void setResponsavelDevolucao(UsuarioVO usuario) {
		this.responsavelDevolucao = usuario;
	}	
	public Double getValorTotalPagoPeloCliente() {
		return valorTotalPagoPeloCliente;
	}
	public void setValorTotalPagoPeloCliente(Double valorTotalPagoPeloCliente) {   
		this.valorTotalPagoPeloCliente = valorTotalPagoPeloCliente;
	}
	public Double getValorUtilizadoPeloCliente() {
		return valorUtilizadoPeloCliente;
	}
	public void setValorUtilizadoPeloCliente(Double valorUtilizadoPeloCliente) {
		this.valorUtilizadoPeloCliente = valorUtilizadoPeloCliente;
	}
	public Double getValorTotalSomaProdutoContratos() {
		return valorTotalSomaProdutoContratos;
	}
	public void setValorTotalSomaProdutoContratos(Double valorTotalSomaProdutoContratos) {
		this.valorTotalSomaProdutoContratos = valorTotalSomaProdutoContratos;
	}
	public Double getValorTaxaCancelamento() {
		return valorTaxaCancelamento;
	}
	public void setValorTaxaCancelamento(Double valorTaxaCancelamento) {
		this.valorTaxaCancelamento = valorTaxaCancelamento;
	}
	public Double getValorMultaCancelamento() {
		return valorMultaCancelamento;
	}
	public void setValorMultaCancelamento(Double valorMultaCancelamento) {
		this.valorMultaCancelamento = valorMultaCancelamento;
	}	
	
	public void montarReciboDevolucao(CancelamentoContratoVO cancelamentoContratoVO, ContratoVO contrato){
		this.setValorContrato(contrato.getValorFinal());
		this.setValorBaseContrato(contrato.getValorBaseCalculo());
    	this.setResponsavelDevolucao(cancelamentoContratoVO.getResponsavelCancelamento());
    	this.setContrato(contrato);
    	this.setProdutoVO(new MovProdutoVO());
    	this.setValorMultaCancelamento(cancelamentoContratoVO.getValorMensalComBaseNoPercentual());
    	this.setValorTaxaCancelamento(cancelamentoContratoVO.getValorTaxaCancelamento());
    	this.setValorTotalPagoPeloCliente(cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque());
    	this.setValorTotalSomaProdutoContratos(cancelamentoContratoVO.getValorTotalSomaProdutoContratos());
    	this.setValorUtilizadoPeloCliente(cancelamentoContratoVO.getValorUtilizadoPeloClienteMensal());
    	this.setValorEmContaCorrente(cancelamentoContratoVO.getSaldoContaCorrenteCliente());
    	this.setDataDevolucao(Calendario.hoje());
    	this.setQuitacao(cancelamentoContratoVO.getQuitacaoCancelamento());
    	this.setQuitacaoManual(cancelamentoContratoVO.isQuitacaoManualCancelamento());
    	this.setLiberacao(cancelamentoContratoVO.getLiberacaoCancelamento());
    	this.setLiberacaoDevolucao(cancelamentoContratoVO.getLiberacaoDevolucao());
    	this.setDevolucaoManual(cancelamentoContratoVO.getDevolucaoManualCancelamento());
        if (cancelamentoContratoVO.isAlterarTipoCancelamento()) {
            this.setValorOriginal(cancelamentoContratoVO.getValorASerDevolvido());
        }
    	Double valorRecebiveis = getValorRecebiveis();
    	//valores
    	if(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo() >= 0 && !getQuitacaoManual()){
    		if(getLiberacaoDevolucao()){
    			this.setValorDevolucao(valorRecebiveis);
        		this.setValorDevolvidoEmDinheiro(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo());
    		}else if (getDevolucaoManual()){
    			this.setValorDevolucao(cancelamentoContratoVO.getValorDevolucaoCancelamento() + valorRecebiveis);
    			this.setValorRealDevolucao(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo());
        		this.setValorDevolvidoEmDinheiro(cancelamentoContratoVO.getValorDevolucaoCancelamento());
    		}else{
    			this.setValorDevolucao(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo() + valorRecebiveis);
        		this.setValorDevolvidoEmDinheiro(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo());
        		
    		}
    		
    	}else{
    		if(getQuitacao() || getLiberacao()){
    			this.setValorDevolucao(valorRecebiveis);
    			this.setValorDevolvidoEmDinheiro(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo());
    		}
    		if(getQuitacaoManual()){
    			this.setValorDevolucao(valorRecebiveis);
    			this.setValorDevolvidoEmDinheiro(cancelamentoContratoVO.getValorQuitacaoCancelamento()*-1);
    		}
    	}
    }
	
	public void montarReciboDevolucaoProduto(CancelamentoSessaoVO cancelamentoSessaoVO){
		this.setValorContrato(cancelamentoSessaoVO.getValorPagoPeloCliente());
		this.setValorBaseContrato(cancelamentoSessaoVO.getMovProdutoVO().getTotalFinal());
    	this.setResponsavelDevolucao(cancelamentoSessaoVO.getResponsavelCancelamento());
    	ContratoVO contrato = new ContratoVO();
    	contrato.setCodigo(0);
    	this.setContrato(contrato);
    	this.setProdutoVO(cancelamentoSessaoVO.getMovProdutoVOCancelado());
    	this.setValorMultaCancelamento(0.0);
    	this.setValorTaxaCancelamento(0.0);
    	this.setValorTotalPagoPeloCliente(cancelamentoSessaoVO.getSomaValorPagoPeloClienteComCheque());
    	this.setValorTotalSomaProdutoContratos(0.0);
    	this.setValorUtilizadoPeloCliente(cancelamentoSessaoVO.getValorUtilizadoCliente());
    	this.setValorEmContaCorrente(0.0);
    	this.setDataDevolucao(Calendario.hoje());
    	Double valorRecebiveis = getValorRecebiveis();
    	//valores
    	if(cancelamentoSessaoVO.getValorASerDevolvidoBaseCalculo() >= 0){
    		this.setQuitacao(false);
    		this.setValorDevolucao(cancelamentoSessaoVO.getValorASerDevolvidoBaseCalculo() + valorRecebiveis);
        	this.setValorDevolvidoEmDinheiro(cancelamentoSessaoVO.getValorASerDevolvidoBaseCalculo());
    	}else{
    		this.setQuitacao(true);
   			this.setValorDevolucao(valorRecebiveis);
   			this.setValorDevolvidoEmDinheiro(cancelamentoSessaoVO.getValorASerDevolvidoBaseCalculo()*-1);
    	}
    }
	
    public Double getValorRecebiveis(){
            Double valor = 0.0;
            for(ChequeVO cheque : getChequesDevolvidos()){
                    valor += cheque.getValorTotal();
            }
            for(CartaoCreditoVO cartao : getCartoesDevolvidos()){
                    valor += cartao.getValorTotal();
            }
            return valor;
    }

	public String getValorPorExtenso(){
		return getValorPorExtenso(produtoVO.getEmpresa().getMoeda().isEmpty() ? "R$" : produtoVO.getEmpresa().getDescMoeda());
	}
        
    public String getValorPorExtenso(String descMoedaEmpresa){
        NumeroPorExtenso npe = new NumeroPorExtenso(getValorDevolucao() < 0.0 ? getValorDevolucao()*-1 : getValorDevolucao(), descMoedaEmpresa);
        return npe.toString().toUpperCase();
    }
    
	public String getValorMonetario() {
		Double valor = this.getValorDevolucao() >= 0 ? this.getValorDevolucao() : this.getValorDevolucao()*-1;
		return Formatador.formatarValorMonetarioSemMoeda(valor);
	}
	
	public String getDataFormatada(){
		return Uteis.getDataComHora(getDataDevolucao());
	}

	public void setChequesDevolvidos(List<ChequeVO> chequesDevolvidos) {
		this.chequesDevolvidos = chequesDevolvidos;
	}

	public List<ChequeVO> getChequesDevolvidos() {
		if(chequesDevolvidos == null)
			chequesDevolvidos = new ArrayList<ChequeVO>();
		return chequesDevolvidos;
	}

	public void setCartoesDevolvidos(List<CartaoCreditoVO> cartoesDevolvido) {
		this.cartoesDevolvidos = cartoesDevolvido;
	}

	public List<CartaoCreditoVO> getCartoesDevolvidos() {
		if(cartoesDevolvidos == null)
			cartoesDevolvidos = new ArrayList<CartaoCreditoVO>();
		return cartoesDevolvidos;
	}
	
	public JRDataSource getListaCheque() {
        JRDataSource jr1 = new JRBeanArrayDataSource(getChequesDevolvidos().toArray());
        return jr1;
    }
	public JRDataSource getListaCheque2() {
        JRDataSource jr1 = new JRBeanArrayDataSource(getChequesDevolvidos().toArray());
        return jr1;
    }

	public void setValorDevolvidoEmDinheiro(Double valorDevolvidoEmDinheiro) {
		this.valorDevolvidoEmDinheiro = valorDevolvidoEmDinheiro;
	}

	public Double getValorDevolvidoEmDinheiro() {
		return valorDevolvidoEmDinheiro;
	}
	
	public JRDataSource getListaCartoes() {
        JRDataSource jr1 = new JRBeanArrayDataSource(getCartoesDevolvidos().toArray());
        return jr1;
    }
	
	public JRDataSource getListaCartoes2() {
        JRDataSource jr1 = new JRBeanArrayDataSource(getCartoesDevolvidos().toArray());
        return jr1;
    }
	
	public boolean getApresentarCheques(){
		return !getChequesDevolvidos().isEmpty();
	}
	
	public boolean getApresentarCartoes(){
		return !getCartoesDevolvidos().isEmpty();
	}

	public void setQuitacao(Boolean quitacao) {
		this.quitacao = quitacao;
	}

	public Boolean getQuitacao() {
		return quitacao;
	}

	public void setQuitacaoManual(Boolean quitacaoManual) {
		this.quitacaoManual = quitacaoManual;
	}

	public Boolean getQuitacaoManual() {
		return quitacaoManual;
	}

	public void setLiberacao(Boolean liberacao) {
		this.liberacao = liberacao;
	}

	public Boolean getLiberacao() {
		return liberacao;
	}

	public void setValorContrato(Double valorContrato) {
		this.valorContrato = valorContrato;
	}

	public Double getValorContrato() {
		return valorContrato;
	}

	public void setValorEmContaCorrente(Double valorEmContaCorrente) {
		this.valorEmContaCorrente = valorEmContaCorrente;
	}

	public Double getValorEmContaCorrente() {
		return valorEmContaCorrente;
	}

	public void setLiberacaoDevolucao(Boolean liberacaoDevolucao) {
		this.liberacaoDevolucao = liberacaoDevolucao;
	}

	public Boolean getLiberacaoDevolucao() {
		return liberacaoDevolucao;
	}

	public void setDevolucaoManual(Boolean devolucaoManual) {
		this.devolucaoManual = devolucaoManual;
	}

	public Boolean getDevolucaoManual() {
		if(devolucaoManual == null){
			devolucaoManual = false;
		}
		return devolucaoManual;
	}

	public void setValorRealDevolucao(Double valorManualDevolucao) {
		this.valorRealDevolucao = valorManualDevolucao;
	}

	public Double getValorRealDevolucao() {
		return valorRealDevolucao;
	}

	public void setProdutoVO(MovProdutoVO produtoVO) {
		this.produtoVO = produtoVO;
	}

	public MovProdutoVO getProdutoVO() {
		return produtoVO;
	}

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }


    public Double getValorOriginal() {
        return valorOriginal;
    }

    public void setValorOriginal(Double valorOriginal) {
        this.valorOriginal = valorOriginal;
    }

    public void montarReciboDevolucaoCC(List<ChequeVO> cheques, List<CartaoCreditoVO> cartoes, Double valorDevolvidoDinheiro,
            ClienteVO cliente, UsuarioVO usuario, Double saldoAtual, List<Integer> movPagamentos){
        contrato = new ContratoVO();
    	contrato.setCodigo(0);
		this.setValorContrato(0.0);
		this.setValorBaseContrato(0.0);
    	this.setResponsavelDevolucao(usuario);
    	this.setProdutoVO(new MovProdutoVO());
    	this.setValorMultaCancelamento(0.0);
    	this.setValorTaxaCancelamento(0.0);
    	this.setValorTotalPagoPeloCliente(0.0);
    	this.setValorTotalSomaProdutoContratos(0.0);
    	this.setValorUtilizadoPeloCliente(0.0);
    	this.setValorEmContaCorrente(saldoAtual);
    	this.setDataDevolucao(Calendario.hoje());
    	this.setQuitacao(false);
    	this.setQuitacaoManual(false);
    	this.setLiberacao(false);
    	this.setLiberacaoDevolucao(false);
    	this.setDevolucaoManual(false);
        setCartoesDevolvidos(cartoes);
        setChequesDevolvidos(cheques);
    	Double valorRecebiveis = getValorRecebiveis();
    	//valores
        this.setValorDevolucao(valorRecebiveis + valorDevolvidoDinheiro);
        this.setValorDevolvidoEmDinheiro(valorDevolvidoDinheiro);
        this.setValorRealDevolucao(valorRecebiveis + valorDevolvidoDinheiro);
        this.setPessoa(cliente.getPessoa().getCodigo());
        this.setListaPagamentos(movPagamentos);
    }

    public Integer getProdDevolucao() {
        return prodDevolucao;
    }

    public void setProdDevolucao(Integer prodDevolucao) {
        this.prodDevolucao = prodDevolucao;
    }

    public Integer getProdRecebiveis() {
        return prodRecebiveis;
    }

    public void setProdRecebiveis(Integer prodRecebiveis) {
        this.prodRecebiveis = prodRecebiveis;
    }

    public List<Integer> getListaPagamentos() {
        if(listaPagamentos == null){
            listaPagamentos = new ArrayList<Integer>();
        }
        return listaPagamentos;
    }

    public void setListaPagamentos(List<Integer> listaPagamentos) {
        this.listaPagamentos = listaPagamentos;
    }

	public Double getValorBaseContrato() {
		return valorBaseContrato;
	}

	public void setValorBaseContrato(Double valorBaseContrato) {
		this.valorBaseContrato = valorBaseContrato;
	}

	public double getValorDevolverSaldoTransferenciaCredito() {
		return valorDevolverSaldoTransferenciaCredito;
	}

	public void setValorDevolverSaldoTransferenciaCredito(double valorDevolverSaldoTransferenciaCredito) {
		this.valorDevolverSaldoTransferenciaCredito = valorDevolverSaldoTransferenciaCredito;
	}

    public List<MovParcelaVO> getParcelasEdicaoPagamento() {
        if(parcelasEdicaoPagamento == null){
            return new ArrayList<MovParcelaVO>();
        }
        return parcelasEdicaoPagamento;
    }

    public void setParcelasEdicaoPagamento(List<MovParcelaVO> parcelasEdicaoPagamento) {
        this.parcelasEdicaoPagamento = parcelasEdicaoPagamento;
    }

    public Integer getReciboEditado() {
        return reciboEditado;
    }

    public void setReciboEditado(Integer reciboEditado) {
        this.reciboEditado = reciboEditado;
    }

    public void montarReciboDevolucaoEdicaoPagamento(List<ChequeVO> cheques, List<MovParcelaVO> parcelas,
           Integer pessoa, UsuarioVO usuario , Integer recibo){
        contrato = new ContratoVO();
    	contrato.setCodigo(0);
		this.setValorContrato(0.0);
		this.setValorBaseContrato(0.0);
    	this.setResponsavelDevolucao(usuario);
    	this.setProdutoVO(new MovProdutoVO());
    	this.setValorMultaCancelamento(0.0);
    	this.setValorTaxaCancelamento(0.0);
    	this.setValorTotalPagoPeloCliente(0.0);
    	this.setValorTotalSomaProdutoContratos(0.0);
    	this.setValorUtilizadoPeloCliente(0.0);
    	this.setValorEmContaCorrente(0.0);
    	this.setDataDevolucao(Calendario.hoje());
    	this.setQuitacao(false);
    	this.setQuitacaoManual(false);
    	this.setLiberacao(false);
    	this.setLiberacaoDevolucao(false);
    	this.setDevolucaoManual(false);
        setChequesDevolvidos(cheques);
        setParcelasEdicaoPagamento(parcelas);
    	Double valorRecebiveis = getValorRecebiveis();
    	//valores
        this.setValorDevolucao(valorRecebiveis);
        this.setValorDevolvidoEmDinheiro(0.0);
        this.setValorRealDevolucao(valorRecebiveis);
        this.setPessoa(pessoa);
        this.setReciboEditado(recibo);
    }

    private String getCodigosParcelasEdicao() {
        String codigos = "";
        for (MovParcelaVO parcela : parcelasEdicaoPagamento){
            codigos += ", "+parcela.getCodigo();
        }
        return codigos.replaceFirst(", ", "");
    }
    
}

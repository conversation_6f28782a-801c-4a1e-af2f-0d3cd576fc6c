package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

/**
 * Created by GlaucoT on 17/04/2015
 */
public class RemessaItemMovParcelaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    @ChaveEstrangeira
    private RemessaItemVO remessaItemVO = new RemessaItemVO();
    @ChaveEstrangeira
    private MovParcelaVO movParcelaVO = new MovParcelaVO();
    @NaoControlarLogAlteracao
    private ClienteVO clienteVO = new ClienteVO();
    private Double valorOriginal = 0.0;
    private Double valorMulta;
    private Double valorJuros;
    private Integer nrTentativaParcela;
    private String jsonEstorno;

    public Double getValorParcela() {
        return movParcelaVO.getValorParcela();
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public RemessaItemVO getRemessaItemVO() {
        return remessaItemVO;
    }

    public void setRemessaItemVO(RemessaItemVO remessaItemVO) {
        this.remessaItemVO = remessaItemVO;
    }

    public MovParcelaVO getMovParcelaVO() {
        return movParcelaVO;
    }

    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Double getValorOriginal() {
        return valorOriginal;
    }

    public void setValorOriginal(Double valorOriginal) {
        this.valorOriginal = valorOriginal;
    }

    public String getValorOriginal_Apresentar() {
        return Formatador.formatarValorMonetario(getValorOriginal());
    }

    public Boolean getValorAlterado() {
        return Uteis.arredondarForcando2CasasDecimais(getValorOriginal()) != (getMovParcelaVO().getValorParcela());
    }

    public String getInfoAlteracaoValor() {
        return "A parcela teve seu valor alterado";
    }

    public String getBackground() {
        if (!getValorAlterado()) {
            return "";
        } else  {
            return "background-color: #F4C9D1";
        }
    }

    public Double getValorMulta() {
        if (valorMulta == null) {
            valorMulta = 0.0;
        }
        return valorMulta;
    }

    public void setValorMulta(Double valorMulta) {
        this.valorMulta = valorMulta;
    }

    public String getValorMulta_Apresentar() {
        return Formatador.formatarValorMonetario(getValorMulta());
    }

    public Double getValorJuros() {
        if (valorJuros == null) {
            valorJuros = 0.0;
        }
        return valorJuros;
    }

    public void setValorJuros(Double valorJuros) {
        this.valorJuros = valorJuros;
    }

    public String getValorJuros_Apresentar() {
        return Formatador.formatarValorMonetario(getValorJuros());
    }

    public Integer getNrTentativaParcela() {
        if (nrTentativaParcela == null) {
            nrTentativaParcela = 0;
        }
        return nrTentativaParcela;
    }

    public void setNrTentativaParcela(Integer nrTentativaParcela) {
        this.nrTentativaParcela = nrTentativaParcela;
    }

    public String getLogEstornoApresentar() {
        try {
            if (!UteisValidacao.emptyString(getJsonEstorno().trim())) {

                JSONObject json = new JSONObject(getJsonEstorno());

                StringBuilder log = new StringBuilder();

                if (!UteisValidacao.emptyNumber(json.getInt("Codigo"))) {
                    log.append("<b>Parcela:</b> ").append(json.getInt("Codigo"));
                    if (!UteisValidacao.emptyString(json.getString("Descricao"))) {
                        log.append(" - ").append(json.getString("Descricao"));
                    }
                    log.append("<br/>");
                }

                if (!UteisValidacao.emptyString(json.getString("PessoaNome"))) {
                    log.append("<b>Pessoa:</b> ").append(json.getString("PessoaNome"));
                }

                if (log.length() > 0) {
                    return log.toString();
                }
            }
        } catch (Exception ignored) {
        }
        return "";
    }

    public String getJsonEstorno() {
        if (jsonEstorno == null) {
            jsonEstorno = "";
        }
        return jsonEstorno;
    }

    public void setJsonEstorno(String jsonEstorno) {
        this.jsonEstorno = jsonEstorno;
    }
}

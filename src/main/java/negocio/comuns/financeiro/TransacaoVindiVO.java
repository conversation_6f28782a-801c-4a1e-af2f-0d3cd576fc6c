package negocio.comuns.financeiro;

import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;

import java.util.Date;

/**
 * Created by johny<PERSON> on 23/02/2017.
 */
public class TransacaoVindiVO extends TransacaoVO{

    public String getValorCodigoExterno() throws Exception{
        return getCodigoExterno();
    }

    public String getValorCartaoMascarado() {
        String cartao = "";
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            JSONObject profilePayment = obj.getJSONObject("bill").getJSONArray("charges").getJSONObject(0).getJSONObject("last_transaction").getJSONObject("payment_profile");
            cartao = profilePayment.getString("card_number_first_six") + "*******" + profilePayment.getString("card_number_last_four");
        } catch (Exception ignored) {
        }

        if (UteisValidacao.emptyString(cartao)) {
            try {
                String card = this.obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoMascarado);
                cartao = APF.getCartaoMascarado(card);
            } catch (Exception ignored) {
            }
        }
        return cartao;
    }

    public String getValorUltimaTransacaoAprovada() throws Exception{
        return getValorAtributoResposta(APF.Transacao);
    }

    public String getResultadoRequisicao() throws Exception{
        JSONObject ultimaTransacao = new JSONObject();
        for(int i = 0; i < 5; i++){
            try{
                getUltimaTransacaoAte5Charges(i);
                ultimaTransacao = getUltimaTransacaoAte5Charges(i);
            }catch (Exception ignored){
            }
        }
        return ultimaTransacao.has("errors") ? ultimaTransacao.get("errors").toString() : ultimaTransacao.getString("gateway_message");
    }

    public String getCodErroExterno() {
        String codigoRetorno = "";
        try {
            try {
                JSONObject ultimaTransacao = new JSONObject();
                for (int i = 0; i < 5; i++) {
                    try {
                        ultimaTransacao = getUltimaTransacaoAte5Charges(i);
                        codigoRetorno = ultimaTransacao.optString("gateway_response_code");
                        if (!UteisValidacao.emptyString(codigoRetorno)) {
                            return codigoRetorno;
                        }
                    } catch (Exception ignored) {
                    }
                }
            } catch (Exception ignored) {
            }
        } catch (Exception ex) {
            codigoRetorno = "";
        }

        if (UteisValidacao.emptyString(codigoRetorno) &&
                getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
            return "0";
        }

        if (UteisValidacao.emptyString(codigoRetorno)) {
            return "?";
        } else {
            return codigoRetorno;
        }
    }

    private JSONObject getUltimaTransacaoAte5Charges(int tentativa){
        JSONObject obj = new JSONObject(getParamsResposta());
        if(obj.has("errors")){
            return obj;
        }
        return  obj.getJSONObject("bill").getJSONArray("charges").getJSONObject(tentativa).getJSONObject("last_transaction");
    }

    public String getAutorizacao() {
        String autorizacao = "";
        for(int i = 0; i < 5 ; i++){
            try{
                autorizacao = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("processor_approval_code");
                break;
            }catch(Exception ignore){
                autorizacao = "";
            }
        }
        if(UteisValidacao.emptyString(autorizacao)){
            for(int i = 0; i < 5 ; i++){
                try{
                    autorizacao = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("authorization");
                    break;
                }catch(Exception ignore){
                    autorizacao = "";
                }
            }
        }
        if(UteisValidacao.emptyString(autorizacao)){
            for(int i = 0; i < 5 ; i++){
                try{
                    autorizacao = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("authorization_code");
                    break;
                }catch(Exception ignore){
                        autorizacao = "";
                }
            }
        }
        if(UteisValidacao.emptyString(autorizacao)){
            for(int i = 0; i < 5 ; i++){
                try{
                    autorizacao = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("NumAutor");
                    break;
                }catch(Exception ignore){
                    autorizacao = "";
                }
            }
        }

        if(UteisValidacao.emptyString(autorizacao)){
            for(int i = 0; i < 5 ; i++){
                try{
                    autorizacao = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("auth");
                    break;
                }catch(Exception ignore){
                    autorizacao = "";
                }
            }
        }

        //stone
        if(UteisValidacao.emptyString(autorizacao)){
            for(int i = 0; i < 5 ; i++){
                try{
                    autorizacao = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("stone_id_rcpt_tx_id");
                    break;
                }catch(Exception ignore){
                    autorizacao = "";
                }
            }
        }

        //Cielo
        if(UteisValidacao.emptyString(autorizacao)){
            for(int i = 0; i < 5 ; i++){
                try{
                    autorizacao = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").optString("arp");
                    break;
                }catch(Exception ignore){
                    autorizacao = "";
                }
            }
        }
        return autorizacao;
    }

    public String getNSU() {
        String nsu = "";
        for(int i = 0; i < 5 ; i++){
            try{
                // CIELO
                nsu = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("nsu");
                break;
            }catch(Exception ignore){
                nsu = "";
            }
        }
        if(UteisValidacao.emptyString(nsu)){
            for(int i = 0; i < 5 ; i++){
                try{
                    // CIELO V3
                    nsu = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("proof_of_sale");
                    break;
                }catch(Exception ignore){
                    nsu = "";
                }
            }
        }
        if(UteisValidacao.emptyString(nsu)){
            for(int i = 0; i < 5 ; i++){
                try{
                    // REDE
                    nsu = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("NumSqn");
                    break;
                }catch(Exception ignore){
                    nsu = "";
                }
            }
        }
        if(UteisValidacao.emptyString(nsu)){
            for(int i = 0; i < 5 ; i++){
                try{
                    //GLOBAL
                    nsu = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("ds_nsu");
                    break;
                }catch(Exception ignore){
                    nsu = "";
                }
            }
        }
        if(UteisValidacao.emptyString(nsu)){
            for(int i = 0; i < 5 ; i++){
                try{
                    //STONE
                    nsu = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("stone_id_rcpt_tx_id");
                    break;
                }catch(Exception ignore){
                    nsu = "";
                }
            }
        }
        if(UteisValidacao.emptyString(nsu)){
            for(int i = 0; i < 5 ; i++){
                try{
                    //GETNET
                    nsu = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway_response_fields").getString("Transaction_id");
                    break;
                }catch(Exception ignore){
                    nsu = "";
                }
            }
        }
        return nsu;
    }

    public String getAdquirente(){
        String adquirente = "";
        if(UteisValidacao.emptyString(adquirente)){
            for(int i = 0; i < 5 ; i++){
                try{
                    adquirente = getUltimaTransacaoAte5Charges(i).getJSONObject("gateway").getString("connector");
                    break;
                }catch(Exception ignore){
                    adquirente = "";
                }
            }
        }
        return adquirente;
    }

    public String getBandeira() {
        String bandeira = "";
        try {
            for (int i = 0; i < 5; i++) {
                try {
                    bandeira = new JSONObject(getParamsResposta()).getJSONObject("bill").getJSONArray("charges").getJSONObject(i).getJSONObject("last_transaction").getJSONObject("payment_profile").getJSONObject("payment_company").getString("name");
                    break;
                } catch (Exception ignored) {
                }
            }
        } catch (Exception ignored) {
        }

        if (UteisValidacao.emptyString(bandeira)) {
            try {
                bandeira = this.obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira);
            } catch (Exception ignored) {
            }
        }
        return bandeira;
    }

    public String getCartaoMascarado() {
        String result = "";
        try{
            result = getValorCartaoMascarado();
        }catch (Exception ignored){}
        return result;
    }

    public String getValorAtributoResposta(String nomeAtributo) throws Exception {
        String valor = "";
        if(nomeAtributo.equalsIgnoreCase(APF.Transacao)){
            try{
                JSONObject json = new JSONObject(this.getParamsResposta());
                valor = Integer.valueOf(json.getJSONObject("bill").getInt("id")).toString();
            }catch (Exception e){}
        }
        return valor;
    }

    public String getValorAtributoCancelamento(String nomeAtributo) {
        if (UteisValidacao.emptyString(getResultadoCancelamento()) ||
                getResultadoCancelamento().startsWith("{\"errors")) {
            try {
                for (int i = 0; i < 5; i++) {
                    try {
                        JSONObject last_transaction = new JSONObject(getParamsResposta()).getJSONObject("bill").getJSONArray("charges").getJSONObject(i).getJSONObject("last_transaction");
                        if (last_transaction.getString("transaction_type").equalsIgnoreCase("refund")) {
                            String result = last_transaction.getString("gateway_message");
                            if (!UteisValidacao.emptyString(result)) {
                                return result;
                            }
                        }
                    } catch (Exception ex) {
                        return "";
                    }
                }
            } catch (Exception ignore) {
            }
        }

        String valor = "";
        if (!UteisValidacao.emptyString(getResultadoCancelamento())) {
            try {
                if (APF.ResultSolicCancel.equalsIgnoreCase(nomeAtributo)) {
                    valor = new JSONObject(getResultadoCancelamento()).getJSONObject("last_transaction").getString("gateway_message");
                }
            } catch (Exception e) {
            }
        }
        return valor;
    }

    public String dataProximaTentativa() {
        try {
            JSONObject retornoJSON = new JSONObject(getParamsResposta());
            JSONObject bill = retornoJSON.getJSONObject("bill");
            JSONArray charges = bill.getJSONArray("charges");
            JSONObject charge = charges.getJSONObject(0);
            String next_attempt = charge.optString("next_attempt");
            Date data = null;
            if (!UteisValidacao.emptyString(next_attempt)) {
                data = Uteis.getDate(next_attempt, "yyyy-MM-dd'T'HH:mm:ss");
            }

            if (data != null) {
                return Calendario.getDataAplicandoFormatacao(data, "dd/MM/yyyy");
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String nrTentativas() {
        try {
            JSONObject retornoJSON = new JSONObject(getParamsResposta());
            JSONObject bill = retornoJSON.getJSONObject("bill");
            JSONArray charges = bill.getJSONArray("charges");
            JSONObject charge = charges.getJSONObject(0);
            Integer nrTentativasAutomaticas = charge.getInt("attempt_count");
            if (!UteisValidacao.emptyNumber(nrTentativasAutomaticas)) {
                return nrTentativasAutomaticas.toString();
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getTID() {
        String tid = "";
        for (int i = 0; i < 5; i++) {
            try {
                tid = getUltimaTransacaoAte5Charges(i).optString("gateway_transaction_id");
                if (!UteisValidacao.emptyString(tid)) {
                    return tid;
                }
            } catch (Exception ignore) {
                tid = "";
            }
        }
        return "";
    }

    public Integer getNrVezes() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            return obj.getInt("installments");
        } catch (Exception ex) {
            return 0;
        }
    }
}

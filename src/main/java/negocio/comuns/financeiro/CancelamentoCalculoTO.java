package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.contrato.ContratoVO;

import java.util.Date;

public abstract class CancelamentoCalculoTO extends SuperTO {

    public abstract Double getValorAnuidade();

    public abstract Date getDataVencimentoAnuidade();

    public abstract void setDataVencimentoAnuidade(Date dataVencimentoAnuidade);

    public abstract ContratoVO getContratoVO();

    public abstract Double getValorMulta();

    public abstract Date getDataVencimentoMulta();

    public abstract Date getDataCancelamento();

    public abstract void setCobrarAnuidade(String s);

    public abstract void setValorAnuidade(Double valorAnuidade);

    public abstract void setDiasProRataAnuidade(Integer diasProRata);
}

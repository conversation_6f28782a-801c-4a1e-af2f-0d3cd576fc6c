/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class DRESinteticoDWVO extends SuperVO {

    private int mes;
    private int ano;
    private Double faturamento;
    private Double receita;
    private Double despesa;
    private Date dataExecucao;
    private int empresa;
    private Double competencia;

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }
    
    public Date getDataExecucao() {
        return dataExecucao;
    }

    public void setDataExecucao(Date dataExecucao) {
        this.dataExecucao = dataExecucao;
    }

    public int getAno() {
        return ano;
    }

    public void setAno(int ano) {
        this.ano = ano;
    }

    public Double getDespesa() {
        if(despesa == null){
            despesa = 0.0;
        }
        return despesa;
    }

    public void setDespesa(Double despesa) {
        this.despesa = despesa;
    }

    public Double getFaturamento() {
        if(faturamento == null){
            faturamento = 0.0;
        }
        return faturamento;
    }

    public void setFaturamento(Double faturamento) {
        this.faturamento = faturamento;
    }

    public int getMes() {
        return mes;
    }

    public void setMes(int mes) {
        this.mes = mes;
    }

    public Double getReceita() {
        if(receita == null){
            receita = 0.0;
        }
        return receita;
    }

    public void setReceita(Double receita) {
        this.receita = receita;
    }


    public Double getCompetencia() {
        if (competencia == null){
            competencia = 0.0;
        }
        return competencia;
    }

    public void setCompetencia(Double competencia) {
        this.competencia = competencia;
    }
}

package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.io.Serializable;
import java.math.BigDecimal;

/*
 * Created by johnys on 04/04/2017.
 */
public class BIInadimplenciaTO implements Serializable{

    private String mes;
    private BigDecimal valorPago;
    private BigDecimal valorEmAberto;
    private Double inadimplencia;
    private BigDecimal valorTotal;

    public BIInadimplenciaTO(String mes, BigDecimal valorEmAberto, BigDecimal valorPago, BigDecimal valorTotal){
        setMes(mes);
        setValorEmAberto(valorEmAberto);
        setValorPago(valorPago);
        setValorTotal(valorTotal);
        calcularInadimplencia();
    }

    public BIInadimplenciaTO(JSONObject json) {
        this.mes = json.getString("mes");
        this.valorPago = BigDecimal.valueOf(json.getDouble("valorPago"));
        this.valorEmAberto = BigDecimal.valueOf(json.getDouble("valorEmAberto"));
        this.inadimplencia = json.getDouble("inadimplencia");
        this.valorTotal = BigDecimal.valueOf(json.getDouble("valorTotal"));
    }

    private void calcularInadimplencia() {
        if(valorEmAberto != null && valorPago != null && valorTotal != null){
            Double soma = valorTotal.doubleValue();
            Double valor = 0.0;
            if(!UteisValidacao.emptyNumber(this.getValorTotal().doubleValue())){
                valor = valorEmAberto.doubleValue() / soma * 100;
            }
            this.inadimplencia = Uteis.arredondarForcando2CasasDecimais(valor);
        }
    }

    public String getEficiencia() {
        return Formatador.formatarValorMonetarioSemMoeda(100 - inadimplencia) + "% de eficiência";
    }

    public Double getEficienciaDouble() {
        return 100 - inadimplencia;
    }

    public String getMes() {
        return mes;
    }

    public void setMes(String mes) {
        this.mes = mes;
    }

    public BigDecimal getValorPago() {
        return valorPago;
    }

    public void setValorPago(BigDecimal valorPago) {
        this.valorPago = valorPago;
    }

    public BigDecimal getValorEmAberto() {
        return valorEmAberto;
    }

    public void setValorEmAberto(BigDecimal valorEmAberto) {
        this.valorEmAberto = valorEmAberto;
    }

    public Double getInadimplencia() {
        return inadimplencia;
    }

    public void setInadimplencia(Double inadimplencia) {
        this.inadimplencia = inadimplencia;
    }

    public BigDecimal getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(BigDecimal valorTotal) {
        this.valorTotal = valorTotal;
    }
}

package negocio.comuns.financeiro;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Date;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.MovPagamento;
import org.json.JSONObject;

/**
 * Reponsável por manter os dados da entidade PagamentoMovParcela. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see MovPagamento
 */
public class PagamentoMovParcelaVO extends SuperVO {

    protected Integer codigo;
    protected Integer movPagamento;
    protected Double valorPago;
    /** Atributo responsável por manter o objeto relacionado da classe <code>MovParcela </code>.*/
    protected MovParcelaVO movParcela;
    protected ReciboPagamentoVO reciboPagamento;

    /**
     * Construtor padrão da classe <code>PagamentoMovParcela</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public PagamentoMovParcelaVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PagamentoMovParcelaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDadosComConexao(PagamentoMovParcelaVO obj, Connection connection) throws ConsistirException, Exception {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getMovParcela() == null)
                || (obj.getMovParcela().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo MOVPARCELA (Pagamento Movimento Parcela) deve ser informado.");
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(
                String.format("select movpagamento from pagamentomovparcela where movparcela = %s and recibopagamento <> %s",
                new Object[]{obj.getMovParcela().getCodigo(), obj.getReciboPagamento().getCodigo()}),connection);
        if (rs.next()) {
            int movpagamento = rs.getInt("movpagamento");
            throw new ConsistirException(String.format("Não é possível efetuar o pagamento pois a Parcela \"%s\" já está paga pelo Pagamento: \"%s\"",
                    new Object[]{obj.getMovParcela().getCodigo(), movpagamento}));
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setMovParcela(new MovParcelaVO());
        setValorPago(new Double(0));
        setReciboPagamento(new ReciboPagamentoVO());
    }

    /**
     * Retorna o objeto da classe <code>MovParcela</code> relacionado com (<code>PagamentoMovParcela</code>).
     */
    public MovParcelaVO getMovParcela() {
        if (movParcela == null) {
            movParcela = new MovParcelaVO();
        }
        return (movParcela);
    }

    /**
     * Define o objeto da classe <code>MovParcela</code> relacionado com (<code>PagamentoMovParcela</code>).
     */
    public void setMovParcela(MovParcelaVO obj) {
        this.movParcela = obj;
    }

    public Integer getMovPagamento() {
        return (movPagamento);
    }

    public void setMovPagamento(Integer movPagamento) {
        this.movPagamento = movPagamento;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorPago() {
        return valorPago;
    }

    public void setValorPago(Double valorPago) {
        this.valorPago = valorPago;
    }

    public ReciboPagamentoVO getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(ReciboPagamentoVO reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }
    
    public Date getDataVencimentoParcela() {
        if (movParcela == null) {
            return null;
        }
        return movParcela.getDataVencimento();
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("valorPago", valorPago);
        json.put("parcela", movParcela == null || movParcela.getCodigo().equals(0) ? null : movParcela.toJSON());

        return json;
    }
}

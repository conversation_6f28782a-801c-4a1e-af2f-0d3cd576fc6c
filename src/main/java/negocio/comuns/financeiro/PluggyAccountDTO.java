package negocio.comuns.financeiro;

import negocio.comuns.financeiro.enumerador.PluggyAccountTypeEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

/**
 * Created by <PERSON> on 18/07/2023.
 */

public class PluggyAccountDTO {

    protected String id;
    protected String type;
    protected String subtype;
    protected String name;
    protected double balance;
    protected String currencyCode;
    protected String itemId;
    protected String number;
    protected String marketingName;
    protected String taxNumber;
    protected String owner;
    protected PluggyAccountBankDataDTO bankData;
    protected PluggyAccountTypeEnum pluggyAccountTypeEnum;
    protected boolean bloqueadaParaExibicao;


    public PluggyAccountDTO() {
    }

    public PluggyAccountDTO(JSONObject json) throws Exception {
        this.id = json.optString("id", "");
        this.type = json.optString("type", "");
        this.subtype = json.optString("subtype", "");
        this.name = json.optString("name", "");
        this.balance = json.optDouble("balance", 0.0);
        this.currencyCode = json.optString("currencyCode", "");
        this.itemId = json.optString("itemId", "");
        this.number = json.optString("number");
        this.marketingName = json.optString("marketingName");
        this.taxNumber = json.optString("taxNumber");
        this.owner = json.optString("owner");
        if (json.optJSONObject("bankData") != null) {
            this.bankData = new PluggyAccountBankDataDTO(json.getJSONObject("bankData"));
        }
    }

    public String getTextoComplementarApresentar() {
        StringBuilder sb = new StringBuilder();
        if (getType().equals(PluggyAccountTypeEnum.BANK.name()) && !UteisValidacao.emptyString(getNumber())) {
            sb.append(getName());
            if (getBankData() != null) {
            sb.append(" | ").append(getBankData().getTransferNumber());
            }
        } else if (getType().equals(PluggyAccountTypeEnum.CREDIT.name()) && !UteisValidacao.emptyString(getNumber())) {
            sb.append(getName());
            if (!UteisValidacao.emptyString(getNumber())) {
                sb.append(" | ").append("Nº Cartão: ************").append(getNumber());
            }
        }
        return sb.toString();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSubtype() {
        return subtype;
    }

    public void setSubtype(String subtype) {
        this.subtype = subtype;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public double getBalance() {
        return balance;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getMarketingName() {
        return marketingName;
    }

    public void setMarketingName(String marketingName) {
        this.marketingName = marketingName;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public PluggyAccountBankDataDTO getBankData() {
        return bankData;
    }

    public void setBankData(PluggyAccountBankDataDTO bankData) {
        this.bankData = bankData;
    }

    public PluggyAccountTypeEnum getPluggyAccountTypeEnum() {
        return pluggyAccountTypeEnum;
    }

    public void setPluggyAccountTypeEnum(PluggyAccountTypeEnum pluggyAccountTypeEnum) {
        this.pluggyAccountTypeEnum = pluggyAccountTypeEnum;
    }

    public boolean isBloqueadaParaExibicao() {
        return bloqueadaParaExibicao;
    }

    public void setBloqueadaParaExibicao(boolean bloqueadaParaExibicao) {
        this.bloqueadaParaExibicao = bloqueadaParaExibicao;
    }
}

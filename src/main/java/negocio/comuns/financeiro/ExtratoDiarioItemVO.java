/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoItemExtratoEnum;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoCredencialStoneEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.bin.ExtratoBinTipoRegistroEnum;
import servicos.impl.dcc.cielo.OrigemAjusteCieloEnum;
import servicos.impl.dcc.getnet.CodigosProdutosExtratoGetNetEnum;
import servicos.impl.dcc.getnet.TipoRegistroExtratoGetNetEnum;
import servicos.impl.dcc.rede.ExtratoRedeTipoRegistroEnum;

import java.util.*;

/**
 * <AUTHOR>
 */
public class ExtratoDiarioItemVO extends SuperVO {
    /*
    *  Na documentacao da Cielo de Extrato https://developercielo.github.io/tutorial/edi-extrato-eletronico#tabela-iv-c%C3%B3digo-do-produto, no Registro 1, na posição 233 - 235 informa desse valor Código do Produto
    *  Que são as bandeiras para pagamentos Débito
    *  Nesse atríbuto tem apenas os principais, não tendo a lista completa
    *  Se algum dia não Conciliar algum valor em débito, pode ser a falta do Código do Produto do item nessa lista
    *  Ai basta ir na documentação confirmar se realmente é de pagamento Débito e adicionar ao atributo
    */
    public static final List<String> codigosOperadorasDebito = Arrays.asList("011", "014", "017", "018", "025", "031", "036", "041", "042", "044", "045", "046", "058", "065", "066", "069", "071", "094", "097", "111", "342");
    private Map<String, String> props = new HashMap<String, String>();
    private String propsString;
    private Date dataLancamento;
    private Date dataPrevistaPagamento;
    private String autorizacao;
    private String estabelecimento;
    private String nrCartao;
    private String arquivo;
    private String ro;
    private Double valorBruto = 0.0;
    private Double taxa = 0.0;
    private Double valorLiquido;
    private Double valorComissao;
    private Integer codigoMovPagamento = 0;
    private Integer codigoMovPagamentoExtratoDiario = 0;
    private Integer codigoMovConta = 0;
    private Integer tipoConciliacao;
    private Integer nrParcela = 0;
    private Integer codigoCartaoCredito = 0;
    private SituacaoItemExtratoEnum situacao;
    @NaoControlarLogAlteracao
    private String contaMovimento;
    @NaoControlarLogAlteracao
    private Date dataAproximada;
    @NaoControlarLogAlteracao
    private String nomePagador;
    @NaoControlarLogAlteracao
    private Boolean somenteDatas;
    @NaoControlarLogAlteracao
    private CartaoCreditoTO cartao;
    @NaoControlarLogAlteracao
    private MovPagamentoVO movPagamento;
    private Integer nrTotalParcelas = 0;
    private String observacao;

    private boolean cartaoEscolhido = false;
    private boolean itemEscolhido = false;
    private String tipoRegistro;
    private String tipoArquivo;
    @NaoControlarLogAlteracao
    private FormaPagamentoVO formaPagamentoVO;
    @NaoControlarLogAlteracao
    private String informacoes;
    private Date dataProcessamentoExtrato;
    private Date dataReprocessamento;
    private Boolean credito = false;
    private String identificadorProduto = "";
    @NaoControlarLogAlteracao
    private ConvenioCobrancaVO convenio;
    @NaoControlarLogAlteracao
    private EmpresaVO empresa;
    private boolean apresentarExtrato = false;
    private String nsu;
    private String tipoFormaPagamento;
    private boolean estorno = false;
    private String origemAjuste;
    @NaoControlarLogAlteracao
    private List<MovParcelaVO> listaParcelas;
    @NaoControlarLogAlteracao
    private boolean parcelaDCC = false;
    @NaoControlarLogAlteracao
    private ReciboPagamentoVO reciboPagamentoVO;
    private String numeroUnicoTransacao;
    @NaoControlarLogAlteracao
    private List<CartaoCreditoTO> cartoes;
    private int composicao = 0;
    private double valorComposicao = 0.0;
    private String nomeAluno;
    private String matricula;
    private TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum;
    private boolean antecipacao;
    private Date dataPgtoOriginalAntesDaAntecipacao;
    private PessoaVO pessoa;
    private Double valorDescontadoAntecipacao;
    private Double taxaCalculadaAntecipacao;
    private Date dataCancelamento;
    private Date dataArquivo;
    private int codConvenio;
    private String idExterno;
    private String idExterno2;
    @NaoControlarLogAlteracao
    private double valorLiquidoParcelamentoVendaAgrupado;
    @NaoControlarLogAlteracao
    private double valorDescontadoTaxasParcelamentoVendaAgrupado;
    @NaoControlarLogAlteracao
    private TipoCredencialStoneEnum tipoCredencialStoneEnum;
    private boolean alterouDataRecebimentoZWAutomaticamente;
    private Date dataPgtoOriginalZWAntesDaAlteracaoAutomatica;
    /*
    * Tipos de Parcelamento - InstallmentType
    * 1 - A Vista lojista
    * 2 - Parcelado lojista
    * 3 - Parcelado emissor
    * */
    private int tipoParcelamento;

    public ExtratoDiarioItemVO() {
    }

    public String getInformacoes() {
        if (informacoes == null) {
            informacoes = "<b>" + getNomePagador() + "</b>" + "</br>";
            informacoes += formaPagamentoVO == null ? "" :
                    "Forma de pagamento: " + formaPagamentoVO.getDescricao();
        }
        return informacoes;
    }

    public ExtratoDiarioItemVO(RegistroRemessa detail, String arquivo, TipoConciliacaoEnum tipoConciliacaoEnum, ConvenioCobrancaVO convenio) throws Exception {
        this.arquivo = arquivo;
        this.convenio = convenio;
        this.empresa = convenio.getEmpresa();
        this.tipoConciliacao = tipoConciliacaoEnum.getCodigo();
        this.numeroUnicoTransacao = detail.getValue(DCCAttEnum.NumeroUnicoTransacao.name()).trim();
        tipoRegistro = detail.getValue(DCCAttEnum.TipoRegistro.name());
        estabelecimento = detail.getValue(DCCAttEnum.NumeroEstabelecimento.name()).trim();

        Map<String, String> propsGravar = new HashMap<String, String>();
        for (ObjetoGenerico obj : detail.getAtributos()) {
            propsGravar.put(obj.getAtributo(), obj.getValor());
        }
        props = propsGravar;
        ro = detail.getValue(DCCAttEnum.NumeroResumoOperacoes.name());
        try {
            nrParcela = new Integer(detail.getValue(DCCAttEnum.NumeroParcela.name()));
            if (nrParcela == 0) {
                nrParcela = 1;
            }
        } catch (NumberFormatException nf) {
            nrParcela = 1;
        }
        try {
            nrTotalParcelas = new Integer(detail.getValue(DCCAttEnum.QuantidadeParcelas.name()));
            if (nrTotalParcelas == 0) {
                nrTotalParcelas = 1;
            }
        } catch (NumberFormatException nf) {
            nrTotalParcelas = 1;
        }

        try {
            if (convenio != null && !convenio.getTipo().equals(TipoConvenioCobrancaEnum.NENHUM)) {
                this.tipoConvenioCobrancaEnum = convenio.getTipo();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET) || convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {

            if (tipoRegistro.equals(TipoRegistroExtratoGetNetEnum.TipoRegistro1.getId())) {

                apresentarExtrato = true;
                tipoConciliacao = TipoConciliacaoEnum.VENDAS.getCodigo();

                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataResumoVenda.name()), "ddMMyyyy");
                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataCredito.name()), "ddMMyyyy");

                ro = detail.getValue(DCCAttEnum.NumeroResumoVenda.name());

                String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
                valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

                String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
                valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));

                String valorDescontoRetorno = detail.getValue(DCCAttEnum.ValorDesconto.name());
                valorComissao = new Double(valorDescontoRetorno.substring(0, valorDescontoRetorno.length() - 2) + "." + valorDescontoRetorno.substring(valorDescontoRetorno.length() - 2));

                if (valorComissao > 0.0) {
                    taxa = (valorComissao * 100) / valorBruto;
                }

                nrParcela = new Integer(detail.getValue(DCCAttEnum.ParcelaResumoVenda.name()));
                nrTotalParcelas = new Integer(detail.getValue(DCCAttEnum.QuantidadeParcelas.name()));

                CodigosProdutosExtratoGetNetEnum codEnum = CodigosProdutosExtratoGetNetEnum.valueOff(detail.getValue(DCCAttEnum.IdentificadorProduto.name()).trim());
                credito = codEnum == null || codEnum.isCredito();

            } else if (tipoRegistro.equals(TipoRegistroExtratoGetNetEnum.TipoRegistro2.getId())) {

                apresentarExtrato = true;
                tipoConciliacao = TipoConciliacaoEnum.PAGAMENTOS.getCodigo();

                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataTransacao.name()), "ddMMyyyy");
                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataPagamento.name()), "ddMMyyyy");

                ro = detail.getValue(DCCAttEnum.NumeroResumoVenda.name());
                nrCartao = detail.getValue(DCCAttEnum.NumeroCartao.name());

                try {
                    autorizacao = detail.getValue(DCCAttEnum.CodigoAutorizacao.name());
                    if (!UteisValidacao.emptyString(autorizacao) && !Uteis.getValidarStringSePossuiLetra(autorizacao)) {
                        autorizacao  = Integer.valueOf(autorizacao).toString();
                    }
                } catch (Exception ignored) {
                }

                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataPagamento.name()), "ddMMyyyy");

                String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorParcela.name());
                valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

                nrParcela = new Integer(detail.getValue(DCCAttEnum.NumeroParcela.name()));
                nrTotalParcelas = new Integer(detail.getValue(DCCAttEnum.QuantidadeParcelas.name()));
            }

        } else if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {

            ro = detail.getValue(DCCAttEnum.NumeroResumoVenda.name());
            tipoArquivo = detail.getValue(DCCAttEnum.TipoArquivo.name());

            if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getId())
                    && tipoArquivo.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getTipoArquivoRedeEnum().getId())) {

                apresentarExtrato = true;
                tipoConciliacao = TipoConciliacaoEnum.VENDAS.getCodigo();

                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "ddMMyyyy");

                //PEGA DO ARQUIVO 006
//                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataPrevistaCredito.name()), "ddMMyyyy");

                autorizacao = detail.getValue(DCCAttEnum.CodigoAutorizacao.name());
                nrCartao = detail.getValue(DCCAttEnum.NumeroCartao.name());

                String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
                valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

                String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
                valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));

                String valorDescontoRetorno = detail.getValue(DCCAttEnum.ValorDesconto.name());
                valorComissao = new Double(valorDescontoRetorno.substring(0, valorDescontoRetorno.length() - 2) + "." + valorDescontoRetorno.substring(valorDescontoRetorno.length() - 2));

                if (valorComissao > 0.0) {
                    taxa = (valorComissao * 100) / valorBruto;
                }

                nsu = detail.getValue(DCCAttEnum.NumeroComprovanteVenda.name());

                credito = true;

            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro010.getId())
                    && tipoArquivo.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro010.getTipoArquivoRedeEnum().getId())) {

                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "ddMMyyyy");

                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataCreditoPrimeiraParcela.name()), "ddMMyyyy");

                String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
                valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

                String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
                valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));

                String valorDescontoRetorno = detail.getValue(DCCAttEnum.ValorDesconto.name());
                valorComissao = new Double(valorDescontoRetorno.substring(0, valorDescontoRetorno.length() - 2) + "." + valorDescontoRetorno.substring(valorDescontoRetorno.length() - 2));

                if (valorComissao > 0.0) {
                    taxa = (valorComissao * 100) / valorBruto;
                }

                credito = true;

            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro012.getId())
                    && tipoArquivo.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro012.getTipoArquivoRedeEnum().getId())) {

                apresentarExtrato = true;
                tipoConciliacao = TipoConciliacaoEnum.VENDAS.getCodigo();

                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "ddMMyyyy");

                autorizacao = detail.getValue(DCCAttEnum.CodigoAutorizacao.name());
                nsu = detail.getValue(DCCAttEnum.NumeroComprovanteVenda.name());
                nrCartao = detail.getValue(DCCAttEnum.NumeroCartao.name());

                String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
                valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

                String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
                valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));

                String valorDescontoRetorno = detail.getValue(DCCAttEnum.ValorDesconto.name());
                valorComissao = new Double(valorDescontoRetorno.substring(0, valorDescontoRetorno.length() - 2) + "." + valorDescontoRetorno.substring(valorDescontoRetorno.length() - 2));

                if (valorComissao > 0.0) {
                    taxa = (valorComissao * 100) / valorBruto;
                }

                credito = true;

            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro014.getId())
                    && tipoArquivo.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro014.getTipoArquivoRedeEnum().getId())) {

                apresentarExtrato = false;
                tipoConciliacao = TipoConciliacaoEnum.PAGAMENTOS.getCodigo();

                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "ddMMyyyy");

                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataCredito.name()), "ddMMyyyy");

                autorizacao = detail.getValue(DCCAttEnum.CodigoAutorizacao.name());
                nrCartao = detail.getValue(DCCAttEnum.NumeroCartao.name());

                String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
                valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

                String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
                valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));

                String valorDescontoRetorno = detail.getValue(DCCAttEnum.ValorDesconto.name());
                valorComissao = new Double(valorDescontoRetorno.substring(0, valorDescontoRetorno.length() - 2) + "." + valorDescontoRetorno.substring(valorDescontoRetorno.length() - 2));

                if (valorComissao > 0.0) {
                    taxa = (valorComissao * 100) / valorBruto;
                }

                credito = true;


            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro034.getId())
                    && tipoArquivo.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro034.getTipoArquivoRedeEnum().getId())) {

                apresentarExtrato = false;
                tipoConciliacao = TipoConciliacaoEnum.VENDAS.getCodigo();

                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "ddMMyyyy");

                autorizacao = detail.getValue(DCCAttEnum.CodigoAutorizacao.name());
                nrCartao = detail.getValue(DCCAttEnum.NumeroCartao.name());

                String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
                valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

                credito = true;

            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro006.getId())
                    && tipoArquivo.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro006.getTipoArquivoRedeEnum().getId())) {

                apresentarExtrato = false;
                tipoConciliacao = TipoConciliacaoEnum.VENDAS.getCodigo();
                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataPrevistaCredito.name()), "ddMMyyyy");

                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "ddMMyyyy");

                String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
                valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

                String valorDescontoRetorno = detail.getValue(DCCAttEnum.ValorDesconto.name());
                valorComissao = new Double(valorDescontoRetorno.substring(0, valorDescontoRetorno.length() - 2) + "." + valorDescontoRetorno.substring(valorDescontoRetorno.length() - 2));

                String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
                valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));

                if (valorComissao > 0.0) {
                    taxa = (valorComissao * 100) / valorBruto;
                }

                credito = true;


            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro034.getId())
                    && tipoArquivo.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro034.getTipoArquivoRedeEnum().getId())) {

                apresentarExtrato = false;
                tipoConciliacao = TipoConciliacaoEnum.PAGAMENTOS.getCodigo();

                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataCredito.name()), "ddMMyyyy");

                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "ddMMyyyy");

                String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
                valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

                String valorDescontoRetorno = detail.getValue(DCCAttEnum.ValorDesconto.name());
                valorComissao = new Double(valorDescontoRetorno.substring(0, valorDescontoRetorno.length() - 2) + "." + valorDescontoRetorno.substring(valorDescontoRetorno.length() - 2));

                String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
                valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));

                if (valorComissao > 0.0) {
                    taxa = (valorComissao * 100) / valorBruto;
                }

                credito = true;

            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro05.getId())
                    && tipoArquivo.equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro05.getTipoArquivoRedeEnum().getId())) {

                apresentarExtrato = true;
                tipoConciliacao = TipoConciliacaoEnum.VENDAS.getCodigo();

                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataCredito.name()), "ddMMyyyy");

                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "ddMMyyyy");

                String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
                valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

                String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
                valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));

                String valorDescontoRetorno = detail.getValue(DCCAttEnum.ValorDesconto.name());
                valorComissao = new Double(valorDescontoRetorno.substring(0, valorDescontoRetorno.length() - 2) + "." + valorDescontoRetorno.substring(valorDescontoRetorno.length() - 2));

                nrCartao = detail.getValue(DCCAttEnum.NumeroCartao.name());

                nsu = detail.getValue(DCCAttEnum.NumeroComprovanteVenda.name());

                if (valorComissao > 0.0) {
                    taxa = (valorComissao * 100) / valorBruto;
                }

                credito = false;

            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro01.getId())
                    && tipoArquivo.equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro01.getTipoArquivoRedeEnum().getId())) {

                apresentarExtrato = false;
                tipoConciliacao = TipoConciliacaoEnum.VENDAS.getCodigo();

                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataCredito.name()), "ddMMyyyy");

                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "ddMMyyyy");

                String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
                valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

                String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
                valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));

                String valorDescontoRetorno = detail.getValue(DCCAttEnum.ValorDesconto.name());
                valorComissao = new Double(valorDescontoRetorno.substring(0, valorDescontoRetorno.length() - 2) + "." + valorDescontoRetorno.substring(valorDescontoRetorno.length() - 2));

                nrCartao = detail.getValue(DCCAttEnum.NumeroCartao.name());

                if (valorComissao > 0.0) {
                    taxa = (valorComissao * 100) / valorBruto;
                }

                credito = false;

            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro035.getId())
                    && tipoArquivo.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro035.getTipoArquivoRedeEnum().getId())) {

                apresentarExtrato = true;
                //Feito assim porquê ainda não encontrei um caso de chargeback ou outros tipos de cancelamento para adicionar ao if.
                //Motivos de Cancelamento, vidi Tabela3 do documento abaixo.
                //https://www.itau.com.br/media/dam/m/2ef89011df234207/original/rede-nl-extrato-eletronico-especificacao-tecnica-financeiro-portugues.pdf
                if(detail.getValue(DCCAttEnum.CodigoMotivo.name()).equals("18")){
                    tipoConciliacao = TipoConciliacaoEnum.CANCELAMENTO.getCodigo();
                } else {
                    tipoConciliacao = TipoConciliacaoEnum.CANCELAMENTO.getCodigo();
                }
                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataCredito.name()), "ddMMyyyy");
                dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataResumoVendaOriginal.name()), "ddMMyyyy");

                String valorAjusteLiquidoRetorno = detail.getValue(DCCAttEnum.ValorAjuste.name());
                valorBruto = new Double(valorAjusteLiquidoRetorno.substring(0, valorAjusteLiquidoRetorno.length() - 2) + "." + valorAjusteLiquidoRetorno.substring(valorAjusteLiquidoRetorno.length() - 2));
                valorBruto = valorBruto * -1;

                nrCartao = detail.getValue(DCCAttEnum.NumeroCartao.name());
                situacao = SituacaoItemExtratoEnum.PENDENCIAS;
                nsu = detail.getValue(DCCAttEnum.NSU.name());
                autorizacao = detail.getValue(DCCAttEnum.CodigoAutorizacao.name());
                nrParcela = Integer.valueOf(detail.getValue(DCCAttEnum.NumeroParcela.name()));
                //Adicionei esse como true, pois só peguei casos de cartão de crédito e 035 é cancelamento. Se surgir novo 035, precisa criar if aqui
                credito = true;
                estorno = true;
            }

        } else if (detail.getValue(DCCAttEnum.TipoRegistro.name()).equals("1")) {
            dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataGeracao.name()), "yyMMdd");
            dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataPrevistaCredito.name()), "yyMMdd");

            String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
            valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

            String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
            valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));

            String sinalValorBruto = detail.getValue(DCCAttEnum.SinalValorBruto.name());
            if (sinalValorBruto != null && sinalValorBruto.equalsIgnoreCase("-")) {
                valorBruto = valorBruto * -1;
            }
            String sinalValorLiquido = detail.getValue(DCCAttEnum.SinalValorLiquido.name());
            if (sinalValorLiquido != null && sinalValorLiquido.equalsIgnoreCase("-")) {
                valorLiquido = valorLiquido * -1;
            }

            valorComissao = valorBruto - valorLiquido;

            String taxaStr = detail.getValue(DCCAttEnum.TaxaComissao.name());
            if (taxaStr != null && taxaStr.length() > 2) {
                taxa = new Double(taxaStr.substring(0, taxaStr.length() - 2) + "." + taxaStr.substring(taxaStr.length() - 2));
            }
            identificadorProduto = detail.getValue(DCCAttEnum.IdentificadorProduto.name());
            avaliarCreditoOuDebito();

            if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.PAGAMENTOS)) {
                try {
                    String origem = detail.getValue(DCCAttEnum.OrigemAjuste.name());
                    if (!UteisValidacao.emptyString(origem.trim())) {
                        OrigemAjusteCieloEnum origemAjusteCieloEnum = OrigemAjusteCieloEnum.valueOff(origem.trim());
                        origemAjuste = (origemAjusteCieloEnum.getId());
                        estorno = origemAjusteCieloEnum.isChargeback();
                    }
                } catch (Exception ignored) {
                }
            }

        } else if (detail.getValue(DCCAttEnum.TipoRegistro.name()).equals("2")) {
            apresentarExtrato = true;
            autorizacao = detail.getValue(DCCAttEnum.CodigoAutorizacao.name());
            nrCartao = detail.getValue(DCCAttEnum.NumeroCartao.name());

            dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "yyyyMMdd");
//            dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "yyyyMMdd");

            String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorParcela.name());
            if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.VENDAS) && nrTotalParcelas > 1) {
                valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorVenda.name());
            }

            valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

            String sinalValor = detail.getValue(DCCAttEnum.SinalValor.name());
            if (sinalValor != null && sinalValor.equalsIgnoreCase("-")) {
                valorBruto = valorBruto * -1;
            }

            if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.PAGAMENTOS)) {
                try {
                    String origem = detail.getValue(DCCAttEnum.OrigemAjuste.name());
                    if (!UteisValidacao.emptyString(origem.trim())) {
                        OrigemAjusteCieloEnum origemAjusteCieloEnum = OrigemAjusteCieloEnum.valueOff(origem.trim());
                        origemAjuste = (origemAjusteCieloEnum.getId());
                        estorno = origemAjusteCieloEnum.isChargeback();
                    }
                } catch (Exception ignored) {
                }
            }

        } else if (detail.getValue(DCCAttEnum.TipoRegistro.name()).equals("E")) {
            //Cielo - layout 15

            identificadorProduto = detail.getValue(DCCAttEnum.IdentificadorProduto.name());
            avaliarCreditoOuDebito();

            apresentarExtrato = true;
            dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataGeracao.name()), "ddMMyyyy");
            dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataPrevistaCredito.name()), "ddMMyyyy");
            autorizacao = detail.getValue(DCCAttEnum.CodigoAutorizacao.name());
            nrCartao = detail.getValue(DCCAttEnum.NumeroCartao.name());

            String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
            valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));

            String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
            valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));
            if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.VENDAS)) {
                valorLiquido = valorLiquido * nrTotalParcelas;
            }

            String sinalValorBruto = detail.getValue(DCCAttEnum.SinalValorBruto.name());
            if (sinalValorBruto != null && sinalValorBruto.equalsIgnoreCase("-")) {
                valorBruto = valorBruto * -1;
            }
            String sinalValorLiquido = detail.getValue(DCCAttEnum.SinalValorLiquido.name());
            if (sinalValorLiquido != null && sinalValorLiquido.equalsIgnoreCase("-")) {
                valorLiquido = valorLiquido * -1;
            }

            valorComissao = valorBruto - valorLiquido;

            String taxaStr = detail.getValue(DCCAttEnum.TaxaComissao.name());
            if (taxaStr != null && taxaStr.length() > 2) {
                taxa = new Double(taxaStr.substring(0, taxaStr.length() - 2) + "." + taxaStr.substring(taxaStr.length() - 2));
            }

        } else if (detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro011.getId()) ||
                detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro013.getId()) ||
                detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro016.getId()) ||
                detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro021.getId()) ||
                detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro023.getId()) ||
                detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro026.getId())) {

            apresentarExtrato = true;

            dataLancamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataVenda.name()), "ddMMyyyy");

            if (detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro013.getId()) ||
                    detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro011.getId())) {
                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataPrevistaCredito.name()), "ddMMyyyy");
            } else if (detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro016.getId())) {
                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataCreditoPrimeiraParcela.name()), "ddMMyyyy");
            } else {
                dataPrevistaPagamento = Uteis.getDate(detail.getValue(DCCAttEnum.DataPagamento.name()), "ddMMyyyy");
            }

            if (detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro011.getId()) || detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro021.getId())) {
                credito = false;
            } else {
                credito = true;
            }

            String valorLiquidoRetorno = detail.getValue(DCCAttEnum.ValorLiquido.name());
            valorLiquido = new Double(valorLiquidoRetorno.substring(0, valorLiquidoRetorno.length() - 2) + "." + valorLiquidoRetorno.substring(valorLiquidoRetorno.length() - 2));
            String valorBrutoRetorno = detail.getValue(DCCAttEnum.ValorBruto.name());
            valorBruto = new Double(valorBrutoRetorno.substring(0, valorBrutoRetorno.length() - 2) + "." + valorBrutoRetorno.substring(valorBrutoRetorno.length() - 2));
            if (detail.getValue(DCCAttEnum.TipoRegistro.name()).equals(ExtratoBinTipoRegistroEnum.TipoRegistro026.getId())) {
                Double valorBrutoParcela = Uteis.arredondarForcando2CasasDecimais(valorBruto / nrTotalParcelas);
                if (nrParcela.equals(1)) {
                    valorBrutoParcela = Uteis.arredondarForcando2CasasDecimais(valorBrutoParcela + (valorBruto - (valorBrutoParcela * nrTotalParcelas)));
                }
                valorBruto = valorBrutoParcela;
                valorComissao = Uteis.arredondarForcando2CasasDecimais(valorBruto - valorLiquido);
            } else {
                String valorDescontoRetorno = detail.getValue(DCCAttEnum.ValorDesconto.name());
                valorComissao =  new Double(valorDescontoRetorno.substring(0, valorDescontoRetorno.length() - 2) + "." + valorDescontoRetorno.substring(valorDescontoRetorno.length() - 2));;
            }

            autorizacao = detail.getValue(DCCAttEnum.CodigoAutorizacao.name());
            nrCartao = detail.getValue(DCCAttEnum.NumeroCartao.name());
            if (valorComissao > 0.0) {
                taxa = (valorComissao * 100) / valorBruto;
            }
        }
    }

    public ExtratoDiarioItemVO(SituacaoItemExtratoEnum sit) {
        situacao = sit;
    }

    public ExtratoDiarioItemVO(MovPagamentoVO mp) {
        situacao = SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE;
        movPagamento = mp;
        nomePagador = mp.getNomePagador();
    }

    public ExtratoDiarioItemVO(CartaoCreditoTO cc) {
        situacao = SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE;
        cartao = cc;
        nomePagador = cc.getNomePagador();
    }

    public String getDataLancamentoApresentar() {
        return (Uteis.getData(dataLancamento));
    }

    public String getAntecipadoApresentar() {
        if (isAntecipacao()) {
            return "Sim";
        }
        return "Não";
    }

    public String getDataPrevistaPagamentoApresentar() {
        return (Uteis.getData(dataPrevistaPagamento));
    }

    public boolean getDataDiferente() {
        if(dataPrevistaPagamento != null){
            if(cartao != null && cartao.getDataCompensacao() != null){
                return !Calendario.igual(dataPrevistaPagamento, cartao.getDataCompensacao());
            }else if(movPagamento != null && !UteisValidacao.emptyNumber(movPagamento.getCodigo()) && movPagamento.getDataPagamento() != null){
                return !Calendario.igual(dataPrevistaPagamento, movPagamento.getDataPagamento());
            }else{
                return false;
            }
        }
        return false;
    }

    public boolean getValorDiferente() {
        return (cartao != null && !Uteis.valoresIguaisComTolerancia(getValorBruto(), cartao.getValor(), 0.9, this.composicao))
                || (movPagamento != null && !UteisValidacao.emptyNumber(movPagamento.getCodigo())
                    && (!credito || TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao))
                    && !Uteis.valoresIguaisComTolerancia(getValorBruto(), movPagamento.getValor(), 0.9, this.composicao));
    }

    public String getDataCC() {
        return cartao == null ? "" : (Uteis.getData(cartao.getDataCompensacao()));
    }

    public String getDataMP() {
        return movPagamento == null || movPagamento.getCodigo() == 0 ? "" : (Uteis.getData(movPagamento.getDataPagamento()));
    }

    public String getDataLancamentoMP() {
        return movPagamento == null || movPagamento.getCodigo() == 0 ? "" : (Uteis.getData(movPagamento.getDataLancamento()));
    }

    public String getValorMP() {
        return movPagamento == null || movPagamento.getCodigo() == 0 ? "" : Formatador.formatarValorMonetarioSemMoeda(movPagamento.getValor());
    }

    public String getValorCC() {
        return cartao == null ? "" : Formatador.formatarValorMonetarioSemMoedaMantendoSinal((cartao.getValor()));
    }

    public Double getValorCCNumber() {
        return cartao == null ? 0.0 : cartao.getValor();
    }

    public String getValorBrutoApresentar() {
        //cancelamentos e chargeback
        if (valorBruto < 0) {
            return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valorBruto);
        }
        /*
         * Tipos de Parcelamento - InstallmentType
         * 1 - A Vista lojista
         * 2 - Parcelado lojista
         * 3 - Parcelado emissor
         * */
        //por vendas e tipo stone
        if ((TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.equals(tipoConvenioCobrancaEnum) ||
                TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.equals(tipoConvenioCobrancaEnum) ||
                TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT.equals(tipoConvenioCobrancaEnum))
                && TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao)
                && tipoParcelamento != 3) {
            return Formatador.formatarValorMonetarioSemMoedaMantendoSinal((valorLiquido + valorComissao) * nrTotalParcelas);
        }
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valorLiquido + valorComissao);
    }

    public String getValorBrutoApresentarInfoItem() {
        //cancelamentos e chargeback
        if (valorBruto < 0) {
            return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valorBruto);
        }
        //por vendas e tipo stone
        if ((TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.equals(tipoConvenioCobrancaEnum) ||
                TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.equals(tipoConvenioCobrancaEnum) ||
                TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT.equals(tipoConvenioCobrancaEnum))
                && TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao)
                && tipoParcelamento != 3) {
            return Formatador.formatarValorMonetarioSemMoedaMantendoSinal((valorLiquido + valorComissao) * nrTotalParcelas);
        }
        return "R$ " + Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valorLiquido + valorComissao);
    }

    public String getValorLiquidoApresentar() {
        if ((TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.equals(tipoConvenioCobrancaEnum) ||
                TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.equals(tipoConvenioCobrancaEnum))
                && !UteisValidacao.emptyNumber(this.valorLiquidoParcelamentoVendaAgrupado)) {
            return "R$ " + Formatador.formatarValorMonetarioSemMoedaMantendoSinal(this.valorLiquidoParcelamentoVendaAgrupado);
        }
        return "R$ " + Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valorLiquido);
    }

    public String getTaxaTotalApresentar() {
        if ((TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.equals(tipoConvenioCobrancaEnum) ||
                TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.equals(tipoConvenioCobrancaEnum))
                && !UteisValidacao.emptyNumber(this.valorDescontadoTaxasParcelamentoVendaAgrupado)) {
            return "R$ " + Formatador.formatarValorMonetarioSemMoedaMantendoSinal(this.valorDescontadoTaxasParcelamentoVendaAgrupado);
        }
        return "R$ " + (Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valorComissao));
    }

    public String getTaxaTotalApresentarPercentual() {
        return (Formatador.formatarValorMonetarioSemMoedaMantendoSinal(taxa+taxaCalculadaAntecipacao)).replace(",",".") + "%" ;
    }

    public String getTaxaCartaoApresentar() {
        return (Formatador.formatarValorMonetarioSemMoedaMantendoSinal(taxa) + "%").replace(",",".");
    }

    public String getTaxaAntecipacaoApresentar() {
        return (Formatador.formatarValorMonetarioSemMoedaMantendoSinal(taxaCalculadaAntecipacao) + "%").replace(",",".");
    }

    public String getValorDescontadoTaxaAntecipacaoApresentar() {
        return "R$ " + Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valorDescontadoAntecipacao);
    }

    public String getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(String estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public String getNrCartao() {
        return nrCartao;
    }

    public void setNrCartao(String nrCartao) {
        this.nrCartao = nrCartao;
    }

    public Map<String, String> getProps() {
        return props;
    }

    public void setProps(Map<String, String> props) {
        this.props = props;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataPrevistaPagamento() {
        return dataPrevistaPagamento;
    }

    public void setDataPrevistaPagamento(Date dataPrevistaPagamento) {
        this.dataPrevistaPagamento = dataPrevistaPagamento;
    }

    public String getAutorizacao() {
        if (autorizacao == null) {
            autorizacao = "";
        }
        return autorizacao;
    }

    public String getAutorizacaoInt() {
        try {
            return autorizacao == null || Uteis.getValidarStringSePossuiLetra(autorizacao) ?
                    autorizacao : UteisValidacao.emptyNumber(Integer.valueOf(autorizacao)) ? null : autorizacao;
        } catch (Exception e) {
            return autorizacao;
        }

    }

    public void setAutorizacao(String autorizacao) {
        this.autorizacao = autorizacao;
    }

    public Double getValorBruto() {
        return valorBruto;
    }

    public void setValorBruto(Double valorBruto) {
        this.valorBruto = valorBruto;
    }

    public Double getValorLiquido() {
        return valorLiquido;
    }

    public void setValorLiquido(Double valorLiquido) {
        this.valorLiquido = valorLiquido;
    }

    public Double getValorComissao() {
        return valorComissao;
    }

    public void setValorComissao(Double valorComissao) {
        this.valorComissao = valorComissao;
    }

    public Integer getCodigoMovPagamento() {
        return codigoMovPagamento;
    }

    public void setCodigoMovPagamento(Integer codigoMovPagamento) {
        this.codigoMovPagamento = codigoMovPagamento;
    }

    public Integer getCodigoCartaoCredito() {
        return codigoCartaoCredito;
    }

    public void setCodigoCartaoCredito(Integer codigoCartaoCredito) {
        this.codigoCartaoCredito = codigoCartaoCredito;
    }

    public SituacaoItemExtratoEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoItemExtratoEnum situacao) {
        this.situacao = situacao;
    }

    public Integer getNrParcela() {
        return nrParcela;
    }

    public void setNrParcela(Integer nrParcela) {
        this.nrParcela = nrParcela;
    }

    public Date getDataAproximada() {
        return dataAproximada;
    }

    public void setDataAproximada(Date dataAproximada) {
        this.dataAproximada = dataAproximada;
    }

    public String getNomePagador() {
        if (nomePagador == null) {
            return nomePagador = "";
        }
        return Uteis.toCamelCase(nomePagador, true);
    }

    public String getNomePagadorCortado() {
        if (nomePagador == null) {
            return nomePagador = "";
        }
        if (nomePagador.trim().length() > 28) {
            try {
                return getNomePagador().substring(0, 28).trim().concat("...");
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return Uteis.toCamelCase(getNomePagador(), true);
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public Integer getCodigoMovConta() {
        return codigoMovConta;
    }

    public void setCodigoMovConta(Integer codigoMovConta) {
        this.codigoMovConta = codigoMovConta;
    }

    public Boolean getSomenteDatas() {
        return somenteDatas;
    }

    public void setSomenteDatas(Boolean somenteDatas) {
        this.somenteDatas = somenteDatas;
    }

    public CartaoCreditoTO getCartao() {
        return cartao;
    }

    public void setCartao(CartaoCreditoTO cartao) {
        this.cartao = cartao;
    }

    public MovPagamentoVO getMovPagamento() {
        return movPagamento;
    }

    public void setMovPagamento(MovPagamentoVO movPagamento) {
        this.movPagamento = movPagamento;
    }

    public boolean isCartaoEscolhido() {
        return cartaoEscolhido;
    }

    public void setCartaoEscolhido(boolean cartaoEscolhido) {
        this.cartaoEscolhido = cartaoEscolhido;
    }

    public boolean isItemEscolhido() {
        return itemEscolhido;
    }

    public void setItemEscolhido(boolean itemEscolhido) {
        this.itemEscolhido = itemEscolhido;
    }


    public String getTipoRegistro() {
        if (tipoRegistro == null) {
            tipoRegistro = "";
        }
        return tipoRegistro;
    }

    public void setTipoRegistro(String tipoRegistro) {
        this.tipoRegistro = tipoRegistro;
    }

    public String getRo() {
        return ro;
    }

    public void setRo(String ro) {
        this.ro = ro;
    }

    public Double getTaxa() {
        return taxa;
    }

    public void setTaxa(Double taxa) {
        this.taxa = taxa;
    }

    public void setInformacoes(String informacoes) {
        this.informacoes = informacoes;
    }

    public FormaPagamentoVO getFormaPagamentoVO() {
        return formaPagamentoVO;
    }

    public void setFormaPagamentoVO(FormaPagamentoVO formaPagamentoVO) {
        this.formaPagamentoVO = formaPagamentoVO;
    }

    public String getArquivo() {
        return arquivo;
    }

    public void setArquivo(String arquivo) {
        this.arquivo = arquivo;
    }

    public TipoConciliacaoEnum getTipoConciliacaoEnum() {
        if (UteisValidacao.emptyNumber(this.tipoConciliacao)) {
            return null;
        }
        TipoConciliacaoEnum tipoConciliacaoEnum = TipoConciliacaoEnum.getTipo(this.tipoConciliacao);
        if (tipoConciliacaoEnum != null) {
            return tipoConciliacaoEnum;
        }
        return null;
    }

    public Integer getTipoConciliacao() {
        return tipoConciliacao;
    }

    public void setTipoConciliacao(Integer tipoConciliacao) {
        this.tipoConciliacao = tipoConciliacao;
    }

    public String getContaMovimento() {
        return contaMovimento;
    }

    public void setContaMovimento(String contaMovimento) {
        this.contaMovimento = contaMovimento;
    }

    public ConvenioCobrancaVO getConvenio() {
        if (convenio == null) {
            convenio = new ConvenioCobrancaVO();
        }
        return convenio;
    }

    public void setConvenio(ConvenioCobrancaVO convenio) {
        this.convenio = convenio;
    }

    public Integer getCodigoMovPagamentoExtratoDiario() {
        return codigoMovPagamentoExtratoDiario;
    }

    public void setCodigoMovPagamentoExtratoDiario(Integer codigoMovPagamentoExtratoDiario) {
        this.codigoMovPagamentoExtratoDiario = codigoMovPagamentoExtratoDiario;
    }


    public Date getDataProcessamentoExtrato() {
        return dataProcessamentoExtrato;
    }

    public void setDataProcessamentoExtrato(Date dataProcessamentoExtrato) {
        this.dataProcessamentoExtrato = dataProcessamentoExtrato;
    }

    public Date getDataReprocessamento() {
        return dataReprocessamento;
    }

    public void setDataReprocessamento(Date dataReprocessamento) {
        this.dataReprocessamento = dataReprocessamento;
    }

    public Integer getNrTotalParcelas() {
        return nrTotalParcelas;
    }

    public void setNrTotalParcelas(Integer nrTotalParcelas) {
        this.nrTotalParcelas = nrTotalParcelas;
    }


    public String getNrParcelas_apresentar() {
        if (this.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo())) {
            if (UteisValidacao.emptyNumber(this.nrTotalParcelas)) {
                return "";
            }
            return nrTotalParcelas.toString();
        }
        if (UteisValidacao.emptyNumber(this.nrTotalParcelas) || this.nrTotalParcelas == 1) {
            return "";
        } else {
            return nrParcela + "/" + nrTotalParcelas;
        }
    }

    public Boolean getCredito() {
        return credito;
    }

    public void setCredito(Boolean credito) {
        this.credito = credito;
    }

    public String getIdentificadorProduto() {
        return identificadorProduto;
    }

    public void setIdentificadorProduto(String identificadorProduto) {
        this.identificadorProduto = identificadorProduto;
    }

    public void avaliarCreditoOuDebito() {
        if (codigosOperadorasDebito.contains(this.identificadorProduto)) {
            credito = false;
        } else {
            credito = true;
        }
    }

    public boolean isFormaPagamentoDiferente() {
        if (!UteisValidacao.emptyNumber(codigo) && (formaPagamentoVO != null && !UteisValidacao.emptyString(formaPagamentoVO.getTipoFormaPagamento()))) {
            if ((credito && !formaPagamentoVO.getTipoFormaPagamento().equals("CA")) || (!credito && formaPagamentoVO.getTipoFormaPagamento().equals("CA"))) {
                return true;
            }
        }
        return false;
    }

    public String getTipoArquivo() {
        if (tipoArquivo == null) {
            tipoArquivo = "";
        }
        return tipoArquivo;
    }

    public void setTipoArquivo(String tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }

    public boolean isApresentarExtrato() {
        return apresentarExtrato;
    }

    public void setApresentarExtrato(boolean apresentarExtrato) {
        this.apresentarExtrato = apresentarExtrato;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getNsu() {
        if (nsu == null) {
            nsu = "";
        }
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public String getTipoFormaPagamento() {
        if (tipoFormaPagamento == null) {
            tipoFormaPagamento = "";
        }
        return tipoFormaPagamento;
    }

    public void setTipoFormaPagamento(String tipoFormaPagamento) {
        this.tipoFormaPagamento = tipoFormaPagamento;
    }

    public boolean isEstorno() {
        return estorno;
    }

    public void setEstorno(boolean estorno) {
        this.estorno = estorno;
    }

    public String getOrigemAjuste() {
        if (origemAjuste == null) {
            origemAjuste = "";
        }
        return origemAjuste;
    }

    public void setOrigemAjuste(String origemAjuste) {
        this.origemAjuste = origemAjuste;
    }

    public List<MovParcelaVO> getListaParcelas() {
        if (listaParcelas == null) {
            listaParcelas = new ArrayList<MovParcelaVO>();
        }
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public boolean isParcelaDCC() {
        return parcelaDCC;
    }

    public void setParcelaDCC(boolean parcelaDCC) {
        this.parcelaDCC = parcelaDCC;
    }

    public String getParcelasApresentar() {
        StringBuilder retorno = new StringBuilder();
        for (MovParcelaVO movParcelaVO : getListaParcelas()) {
            if (!UteisValidacao.emptyNumber(movParcelaVO.getContrato().getCodigo())) {
                retorno.append("Contrato ").append(movParcelaVO.getContrato().getCodigo()).append(" - ");
            }
            retorno.append(movParcelaVO.getDescricao()).append("<br/>");
        }
        return retorno.toString();
    }

    public ReciboPagamentoVO getReciboPagamentoVO() {
        if (reciboPagamentoVO == null) {
            reciboPagamentoVO = new ReciboPagamentoVO();
        }
        return reciboPagamentoVO;
    }

    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamentoVO) {
        this.reciboPagamentoVO = reciboPagamentoVO;
    }

    public String getNumeroUnicoTransacao() {
        return numeroUnicoTransacao;
    }

    public void setNumeroUnicoTransacao(String numeroUnicoTransacao) {
        this.numeroUnicoTransacao = numeroUnicoTransacao;
    }

    public boolean isItemGetNet() {
        return getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE) || getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET);
    }

    public boolean isValorPositivo() {
        return getValorBruto() > 0;
    }

    public List<CartaoCreditoTO> getCartoes() {
        if(cartoes == null){
            cartoes = new ArrayList<>();
        }
        return cartoes;
    }

    public void setCartoes(List<CartaoCreditoTO> cartoes) {
        this.cartoes = cartoes;
    }

    public int getComposicao() {
        return composicao;
    }

    public void setComposicao(int composicao) {
        this.composicao = composicao;
    }

    public double getValorComposicao() {
        return valorComposicao;
    }

    public void setValorComposicao(double valorComposicao) {
        this.valorComposicao = valorComposicao;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public String getNomeAlunoApresentar() {
        if (nomeAluno == null) {
            nomeAluno = getPessoa().getNome();
        }
        return nomeAluno;
    }

    public String getTipoConciliacaoApresentar() {
        if (!UteisValidacao.emptyNumber(tipoConciliacao)) {
            return TipoConciliacaoEnum.getTipo(tipoConciliacao).getDescricao();
        }
        return "Nenhum";
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public TipoConvenioCobrancaEnum getTipoConvenioCobrancaEnum() {
        if (tipoConvenioCobrancaEnum == null) {
            tipoConvenioCobrancaEnum = TipoConvenioCobrancaEnum.NENHUM;
        }
        return tipoConvenioCobrancaEnum;
    }

    public boolean getStoneModeloPSP() {
        return getTipoCredencialStoneEnum() != null && getTipoCredencialStoneEnum().equals(TipoCredencialStoneEnum.PSP);
    }

    public boolean isStone() {
        return getTipoConvenioCobrancaEnum() != null &&
                (getTipoConvenioCobrancaEnum().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) ||
                        getTipoConvenioCobrancaEnum().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5) ||
                        getTipoConvenioCobrancaEnum().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT));
    }

    public void setTipoConvenioCobrancaEnum(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) {
        this.tipoConvenioCobrancaEnum = tipoConvenioCobrancaEnum;
    }

    public boolean isAntecipacao() {
        return antecipacao;
    }

    public void setAntecipacao(boolean antecipacao) {
        this.antecipacao = antecipacao;
    }

    public Date getDataPgtoOriginalAntesDaAntecipacao() {
        return dataPgtoOriginalAntesDaAntecipacao;
    }

    public String getDataPgtoOriginalAntesDaAntecipacaoApresentar() {
        if (dataPgtoOriginalAntesDaAntecipacao == null) {
            return "";
        }
        return Uteis.getDataAplicandoFormatacao(dataPgtoOriginalAntesDaAntecipacao, "dd/MM/yyyy");
    }

    public void setDataPgtoOriginalAntesDaAntecipacao(Date dataPgtoOriginalAntesDaAntecipacao) {
        this.dataPgtoOriginalAntesDaAntecipacao = dataPgtoOriginalAntesDaAntecipacao;
    }

    public PessoaVO getPessoa() {
        if (pessoa == null) {
            pessoa = new PessoaVO();
        }
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public Double getValorDescontadoAntecipacao() {
        return valorDescontadoAntecipacao;
    }

    public void setValorDescontadoAntecipacao(Double valorDescontadoAntecipacao) {
        this.valorDescontadoAntecipacao = valorDescontadoAntecipacao;
    }

    public Double getTaxaCalculadaAntecipacao() {
        return taxaCalculadaAntecipacao;
    }

    public void setTaxaCalculadaAntecipacao(Double taxaCalculadaAntecipacao) {
        this.taxaCalculadaAntecipacao = taxaCalculadaAntecipacao;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public Date getDataArquivo() {
        return dataArquivo;
    }

    public void setDataArquivo(Date dataArquivo) {
        this.dataArquivo = dataArquivo;
    }

    public int getCodConvenio() {
        return codConvenio;
    }

    public void setCodConvenio(int codConvenio) {
        this.codConvenio = codConvenio;
    }

    public String getIdExterno() {
        if (idExterno == null) {
            idExterno = "";
        }
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }

    public String getIdExterno2() {
        if (idExterno2 == null) {
            idExterno2 = "";
        }
        return idExterno2;
    }

    public void setIdExterno2(String idExterno2) {
        this.idExterno2 = idExterno2;
    }

    public boolean isPagoLivre() {
        return this.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                this.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY);
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getPropsString() {
        if (UteisValidacao.emptyString(propsString)) {
            return "";
        }
        return propsString;
    }

    public void setPropsString(String propsString) {
        this.propsString = propsString;
    }

    public double getValorLiquidoParcelamentoVendaAgrupado() {
        return valorLiquidoParcelamentoVendaAgrupado;
    }

    public void setValorLiquidoParcelamentoVendaAgrupado(double valorLiquidoParcelamentoVendaAgrupado) {
        this.valorLiquidoParcelamentoVendaAgrupado = valorLiquidoParcelamentoVendaAgrupado;
    }

    public double getValorDescontadoTaxasParcelamentoVendaAgrupado() {
        return valorDescontadoTaxasParcelamentoVendaAgrupado;
    }

    public void setValorDescontadoTaxasParcelamentoVendaAgrupado(double valorDescontadoTaxasParcelamentoVendaAgrupado) {
        this.valorDescontadoTaxasParcelamentoVendaAgrupado = valorDescontadoTaxasParcelamentoVendaAgrupado;
    }

    public TipoCredencialStoneEnum getTipoCredencialStoneEnum() {
        return tipoCredencialStoneEnum;
    }

    public void setTipoCredencialStoneEnum(TipoCredencialStoneEnum tipoCredencialStoneEnum) {
        this.tipoCredencialStoneEnum = tipoCredencialStoneEnum;
    }

    public boolean isAlterouDataRecebimentoZWAutomaticamente() {
        return alterouDataRecebimentoZWAutomaticamente;
    }

    public void setAlterouDataRecebimentoZWAutomaticamente(boolean alterouDataRecebimentoZWAutomaticamente) {
        this.alterouDataRecebimentoZWAutomaticamente = alterouDataRecebimentoZWAutomaticamente;
    }

    public Date getDataPgtoOriginalZWAntesDaAlteracaoAutomatica() {
        return dataPgtoOriginalZWAntesDaAlteracaoAutomatica;
    }

    public void setDataPgtoOriginalZWAntesDaAlteracaoAutomatica(Date dataPgtoOriginalZWAntesDaAlteracaoAutomatica) {
        this.dataPgtoOriginalZWAntesDaAlteracaoAutomatica = dataPgtoOriginalZWAntesDaAlteracaoAutomatica;
    }

    public String getDataPgtoOriginalZWAntesDaAlteracaoAutomaticaApresentar() {
        if (dataPgtoOriginalZWAntesDaAlteracaoAutomatica == null) {
            return "";
        }
        return Uteis.getDataAplicandoFormatacao(dataPgtoOriginalZWAntesDaAlteracaoAutomatica, "dd/MM/yyyy");
    }

    public int getTipoParcelamento() {
        return tipoParcelamento;
    }

    public void setTipoParcelamento(int tipoParcelamento) {
        this.tipoParcelamento = tipoParcelamento;
    }
}


package negocio.comuns.financeiro;

import org.json.JSONObject;

/**
 * Created by <PERSON> on 29/09/2023.
 */

public class PluggyTransactionPaymentDataDTO {

    protected String name;
    protected PluggyTransactionPaymentDataPayerDTO payer;
    protected String paymentMethod;
    protected PluggyTransactionPaymentDataReceiverDTO receiver;

    public PluggyTransactionPaymentDataDTO() {
    }

    public PluggyTransactionPaymentDataDTO(JSONObject json) throws Exception {
        this.name = json.optString("name", "");
        if (json.optJSONObject("payer") != null) {
            this.payer = new PluggyTransactionPaymentDataPayerDTO(json.optJSONObject("payer"));
        }
        if (json.optJSONObject("receiver") != null) {
            this.receiver = new PluggyTransactionPaymentDataReceiverDTO(json.optJSONObject("receiver"));
        }
        this.paymentMethod = json.optString("paymentMethod", "");
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public PluggyTransactionPaymentDataPayerDTO getPayer() {
        return payer;
    }

    public void setPayer(PluggyTransactionPaymentDataPayerDTO payer) {
        this.payer = payer;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public PluggyTransactionPaymentDataReceiverDTO getReceiver() {
        return receiver;
    }

    public void setReceiver(PluggyTransactionPaymentDataReceiverDTO receiver) {
        this.receiver = receiver;
    }
}

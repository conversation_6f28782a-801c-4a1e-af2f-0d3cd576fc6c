/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class RemessaTransacaoVO extends SuperVO {

    private RemessaVO remessa;
    private TransacaoVO transacao;

    public RemessaVO getRemessa() {
        return remessa;
    }

    public void setRemessa(RemessaVO remessa) {
        this.remessa = remessa;
    }

    public TransacaoVO getTransacao() {
        return transacao;
    }

    public void setTransacao(TransacaoVO transacao) {
        this.transacao = transacao;
    }
}

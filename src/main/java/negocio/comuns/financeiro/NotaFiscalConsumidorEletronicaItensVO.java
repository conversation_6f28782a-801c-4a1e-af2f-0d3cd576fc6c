package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoVO;

/*
 * Created by <PERSON><PERSON> on 27/03/2017.
 */
public class NotaFiscalConsumidorEletronicaItensVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronica;
    @ChaveEstrangeira
    private ProdutoVO produto;
    private String descricao;
    private Double valorUnitario;
    private Integer quantidade;
    private Double valorDescontoOuAcrescimo;
    private Double valorTotal;


    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public NotaFiscalConsumidorEletronicaVO getNotaFiscalConsumidorEletronica() {
        if (notaFiscalConsumidorEletronica == null) {
            notaFiscalConsumidorEletronica = new NotaFiscalConsumidorEletronicaVO();
        }
        return notaFiscalConsumidorEletronica;
    }

    public void setNotaFiscalConsumidorEletronica(NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronica) {
        this.notaFiscalConsumidorEletronica = notaFiscalConsumidorEletronica;
    }

    public ProdutoVO getProduto() {
        if (produto == null) {
            produto = new ProdutoVO();
        }
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValorUnitario() {
        if (valorUnitario == null) {
            valorUnitario = 0.0;
        }
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public Integer getQuantidade() {
        if (quantidade == null) {
            quantidade = 0;
        }
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Double getValorDescontoOuAcrescimo() {
        if (valorDescontoOuAcrescimo == null) {
            valorDescontoOuAcrescimo = 0.0;
        }
        return valorDescontoOuAcrescimo;
    }

    public void setValorDescontoOuAcrescimo(Double valorDescontoOuAcrescimo) {
        this.valorDescontoOuAcrescimo = valorDescontoOuAcrescimo;
    }

    public Double getValorTotal() {
        if (valorTotal == null) {
            valorTotal = 0.0;
        }
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

}

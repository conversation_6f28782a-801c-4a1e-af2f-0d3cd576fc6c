package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

/*
 * Created by <PERSON><PERSON> on 27/03/2017.
 */
public class NotaFiscalConsumidorEletronicaFormasPagamentoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronica;
    @ChaveEstrangeira
    private FormaPagamentoVO formaPagamento;
    private Double valor;


    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public NotaFiscalConsumidorEletronicaVO getNotaFiscalConsumidorEletronica() {
        if (notaFiscalConsumidorEletronica == null) {
            notaFiscalConsumidorEletronica = new NotaFiscalConsumidorEletronicaVO();
        }
        return notaFiscalConsumidorEletronica;
    }

    public void setNotaFiscalConsumidorEletronica(NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronica) {
        this.notaFiscalConsumidorEletronica = notaFiscalConsumidorEletronica;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public FormaPagamentoVO getFormaPagamento() {
        if (formaPagamento == null) {
            formaPagamento = new FormaPagamentoVO();
        }
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamentoVO formaPagamento) {
        this.formaPagamento = formaPagamento;
    }
}

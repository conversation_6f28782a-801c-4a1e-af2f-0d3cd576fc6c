/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class CaixaVO extends SuperTO {

    private Integer codigo = 0;
    private UsuarioVO usuarioVo = new UsuarioVO();
    private EmpresaVO empresaVo;
    private Date dataAbertura;
    private Date dataReabertura;
    private Date dataFechamento;
    private List<CaixaContaVO> listaCaixaConta = new ArrayList<CaixaContaVO>();
    private List<CaixaMovContaVO> listaCaixaMovConta = new ArrayList<CaixaMovContaVO>();
    private double totalEntrada = 0;
    private double totalSaida = 0;
    private double totalTransferencia = 0;
    private UsuarioVO responsavelFechamento = new UsuarioVO();
    private Date dataTrabalho;

    public CaixaVO(){
    	
    }
    
    public CaixaVO(Integer codigo){
    	this.codigo = codigo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataAbertura() {
        return dataAbertura;
    }

    public void setDataAbertura(Date dataAbertura) {
        this.dataAbertura = dataAbertura;
    }

    public Date getDataFechamento() {
        return dataFechamento;
    }

    public void setDataFechamento(Date dataFechamento) {
        this.dataFechamento = dataFechamento;
    }

    public EmpresaVO getEmpresaVo() {
        if (empresaVo == null) {
            empresaVo = new EmpresaVO();
        }
        return empresaVo;
    }

    public void setEmpresaVo(EmpresaVO empresaVo) {
        this.empresaVo = empresaVo;
    }

    public UsuarioVO getUsuarioVo() {
        return usuarioVo;
    }

    public void setUsuarioVo(UsuarioVO usuarioVo) {
        this.usuarioVo = usuarioVo;
    }

    public List<CaixaContaVO> getListaCaixaConta() {
        return listaCaixaConta;
    }

    public void setListaCaixaConta(List<CaixaContaVO> listaCaixaConta) {
        this.listaCaixaConta = listaCaixaConta;
    }

    public double getTotalEntrada() {
        return totalEntrada;
    }

    public void setTotalEntrada(double totalEntrada) {
        this.totalEntrada = totalEntrada;
    }

    public double getTotalSaida() {
        return totalSaida;
    }
    
    public String getTotalSaidaMonetario(){
    	return Formatador.formatarValorMonetarioSemMoeda(totalSaida);
    }
    
    public String getTotalEntradaMonetario(){
    	return Formatador.formatarValorMonetarioSemMoeda(totalEntrada);
    }
    public String getTotalTransferenciaMonetario(){
    	return Formatador.formatarValorMonetarioSemMoeda(totalTransferencia);
    }
    public void setTotalSaida(double totalSaida) {
        this.totalSaida = totalSaida;
    }

    public List<CaixaMovContaVO> getListaCaixaMovConta() {
        return listaCaixaMovConta;
    }

    public void setListaCaixaMovConta(List<CaixaMovContaVO> listaCaixaMovConta) {
        this.listaCaixaMovConta = listaCaixaMovConta;
    }

	public void setResponsavelFechamento(UsuarioVO responsavelFechamento) {
		this.responsavelFechamento = responsavelFechamento;
	}

	public UsuarioVO getResponsavelFechamento() {
		return responsavelFechamento;
	}

	public void setTotalTransferencia(double totalTransferencia) {
		this.totalTransferencia = totalTransferencia;
	}

	public double getTotalTransferencia() {
		return totalTransferencia;
	}

	public void setDataReabertura(Date dataReabertura) {
		this.dataReabertura = dataReabertura;
	}

	public Date getDataReabertura() {
		return dataReabertura;
	}

	public void setDataTrabalho(Date dataTrabalho) {
		this.dataTrabalho = dataTrabalho;
	}

    public String getDataTrabalho_Apresentar(){
        return Uteis.getData(getDataTrabalho());
    }
	public Date getDataTrabalho() {
		return dataTrabalho;
	}


    
}

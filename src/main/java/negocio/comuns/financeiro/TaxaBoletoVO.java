package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.enumerador.FormaCalculo;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;

import java.util.Date;

public class TaxaBoletoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private Double taxa = 0.0;
    @NaoControlarLogAlteracao
    @FKJson
    private FormaPagamentoVO formaPagamentoVO = new FormaPagamentoVO();
    private Date vigenciaInicial;
    private Date vigenciaFinal;
    private Integer tipo = FormaCalculo.PERCENTUAL.getCodigo();
    @ChaveEstrangeira
    private EmpresaVO empresa = new EmpresaVO();

    public TaxaBoletoVO() {
        super();
    }

    public Double getTaxa() {
        return taxa;
    }

    public void setTaxa(Double taxa) {
        this.taxa = taxa;
    }

    public Date getVigenciaInicial() {
        return vigenciaInicial;
    }

    public void setVigenciaInicial(Date vigenciaInicial) {
        this.vigenciaInicial = vigenciaInicial;
    }

    public Date getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(Date vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public FormaPagamentoVO getFormaPagamentoVO() {
        return formaPagamentoVO;
    }

    public void setFormaPagamentoVO(FormaPagamentoVO formaPagamentoVO) {
        this.formaPagamentoVO = formaPagamentoVO;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }
}

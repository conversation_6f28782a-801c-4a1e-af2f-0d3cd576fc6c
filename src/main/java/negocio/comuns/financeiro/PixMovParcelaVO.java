package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;

public class PixMovParcelaVO extends SuperVO {

    private Integer codigo;
    private Integer movparcela;
    private MovParcelaVO movParcelaVO;
    private Integer pix;
    @NaoControlarLogAlteracao
    private ClienteVO clienteVO;
    private Double valorMulta;
    private Double valorJuros;


    public PixMovParcelaVO() {
    }

    public PixMovParcelaVO(Integer movparcela) {
        this.movparcela = movparcela;
    }

    public PixMovParcelaVO(Integer movparcela, Integer pix) {
        this.movparcela = movparcela;
        this.pix = pix;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMovparcela() {
        return movparcela;
    }

    public void setMovparcela(Integer movparcela) {
        this.movparcela = movparcela;
    }

    public MovParcelaVO getMovParcelaVO() {
        return movParcelaVO;
    }

    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }

    public Integer getPix() {
        return pix;
    }

    public void setPix(Integer pix) {
        this.pix = pix;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Double getValorMulta() {
        if (valorMulta == null) {
            return 0.0;
        }
        return valorMulta;
    }

    public void setValorMulta(Double valorMulta) {
        this.valorMulta = valorMulta;
    }

    public Double getValorJuros() {
        if (valorJuros == null) {
            return 0.0;
        }
        return valorJuros;
    }

    public void setValorJuros(Double valorJuros) {
        this.valorJuros = valorJuros;
    }
}

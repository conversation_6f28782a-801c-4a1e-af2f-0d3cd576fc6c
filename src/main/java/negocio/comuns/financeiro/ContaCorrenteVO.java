package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade ContaCorrente. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class ContaCorrenteVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String agencia;
    protected String agenciaDV;
    protected String contaCorrente;
    protected String contaCorrenteDV;
    protected String codigoOperacao;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Banco </code>.*/
    @ChaveEstrangeira
    @FKJson
    protected BancoVO banco;

    /**
     * Construtor padrão da classe <code>ContaCorrente</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ContaCorrenteVO() {
        super();
        inicializarDados();
    }

    public String getBanco_Apresentar() {
        return getBanco().getNome();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ContaCorrenteVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ContaCorrenteVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getAgencia().equals("")) {
            throw new ConsistirException("O campo AGÊNCIA (Conta Corrente) deve ser informado.");
        }
        if (obj.getContaCorrente().equals("")) {
            throw new ConsistirException("O campo CONTA CORRENTE (Conta Corrente) deve ser informado.");
        }
        if (obj.getContaCorrenteDV().equals("")) {
            throw new ConsistirException("O campo DÍGITO VERIFICADOR DA CONTA CORRENTE (Conta Corrente) deve ser informado.");
        }
        if ((obj.getBanco() == null)
                || (obj.getBanco().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo BANCO (Conta Corrente) deve ser informado.");
        }
    }

    public static void removerEspacosEmBranco(ContaCorrenteVO obj) throws ConsistirException {
        obj.setAgencia(Uteis.removerEspacosInicioFimString(obj.getAgencia()));
        obj.setAgenciaDV(Uteis.removerEspacosInicioFimString(obj.getAgenciaDV()));
        obj.setContaCorrente(Uteis.removerEspacosInicioFimString(obj.getContaCorrente()));
        obj.setContaCorrenteDV(Uteis.removerEspacosInicioFimString(obj.getContaCorrenteDV()));
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setAgencia(getAgencia().toUpperCase());
        setAgenciaDV(getAgenciaDV().toUpperCase());
        setContaCorrente(getContaCorrente().toUpperCase());
        setContaCorrenteDV(getContaCorrenteDV().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setAgencia("");
        setAgenciaDV("");
        setContaCorrente("");
        setContaCorrenteDV("");
        setBanco(new BancoVO());
    }

    /**
     * Retorna o objeto da classe <code>Banco</code> relacionado com (<code>ContaCorrente</code>).
     */
    public BancoVO getBanco() {
        if (banco == null) {
            banco = new BancoVO();
        }
        return (banco);
    }

    /**
     * Define o objeto da classe <code>Banco</code> relacionado com (<code>ContaCorrente</code>).
     */
    public void setBanco(BancoVO obj) {
        this.banco = obj;
    }

    public String getContaCorrenteDV() {
        if (contaCorrenteDV == null) {
            contaCorrenteDV = "";
        }
        return (contaCorrenteDV);
    }

    public void setContaCorrenteDV(String contaCorrenteDV) {
        this.contaCorrenteDV = contaCorrenteDV;
    }

    public String getContaCorrente() {
        if (contaCorrente == null) {
            contaCorrente = "";
        }
        return (contaCorrente);
    }

    public void setContaCorrente(String contaCorrente) {
        this.contaCorrente = contaCorrente;
    }

    public String getAgenciaDV() {
        if (agenciaDV == null) {
            agenciaDV = "";
        }
        return (agenciaDV);
    }

    public void setAgenciaDV(String agenciaDV) {
        this.agenciaDV = agenciaDV;
    }

    public String getAgencia() {
        if (agencia == null) {
            agencia = "";
        }
        return (agencia);
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCodigoOperacao() {
        if(codigoOperacao == null){
            codigoOperacao = "";
        }
        return codigoOperacao;
    }

    public void setCodigoOperacao(String codigoOperacao) {
        this.codigoOperacao = codigoOperacao;
    }

    public String getDescricaoApresentar() {
        return "AG:" + getAgencia() + "-" + getAgenciaDV() + "/ CC:" + getContaCorrente() + "-" + getContaCorrenteDV();
    }

    public String getDescricaoBBApresentar() {
        if (UteisValidacao.emptyNumber(getBanco().getCodigoBanco()) &&
                UteisValidacao.emptyString(getAgencia()) &&
                UteisValidacao.emptyString(getAgenciaDV()) &&
                UteisValidacao.emptyString(getContaCorrente()) &&
                UteisValidacao.emptyString(getContaCorrenteDV())) {
            return "";
        }
        return "B:" + getBanco().getCodigoBanco() + " / "
                + " AG:" + getAgencia() + "-" + getAgenciaDV() + "/ CC:" + getContaCorrente() + "-" + getContaCorrenteDV();
    }

    public String getApresentarDadosBancarios(){
        return "B:" + getBanco().getCodigoBanco() + " / "
                + " AG:" + getAgencia() + (UteisValidacao.emptyString(getAgenciaDV()) ? "" : ("-" + getAgenciaDV())) + " / CC:" + getContaCorrente() + "-" + getContaCorrenteDV();
    }
}

package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.UteisValidacao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
public class CaixaAbertoTO implements Serializable{

	private Integer contrato;
	private Integer diaria;
	private Integer vendaAvulsa;
	private Integer personal;
	private Integer pessoa;
	private Integer colaborador;
	private Integer cliente;
	private String nomeCliente;
	private String descricao;
	private Double valorTotal;
	private Date lancamento;
	private Boolean exibeParcelas = Boolean.TRUE;
	private Boolean marcarTodas;
	private Integer codigoEvento;
	private List<MovParcelaVO> parcelas = new ArrayList<>();
	private List<MovParcelaVO> parcelasPagasCE = new ArrayList<>();
	private List<MovPagamentoVO> pagamentosCE = new ArrayList<>();
	private Map<Integer, Double> parcelasSelecionadas;
	private String empresaNome;
	private Integer empresaCodigo;
	private EmpresaVO empresaVO;
	private String descricaoProduto;
	private boolean apresentarNomeProduto = false;


	public boolean getExibirBotoesContrato(){
		return !UteisValidacao.emptyNumber(contrato);
	}
	
	public boolean getEmptyParcelas(){
		return parcelas.isEmpty();
	}
	
	public boolean getEmptyParcelasPagasCE(){
		return parcelasPagasCE.isEmpty();
	}
	
	public boolean getCentralEventos(){
		return !UteisValidacao.emptyNumber(getCodigoEvento());
	}

	public Integer getContrato() {
		if (contrato == null) {
			contrato = 0;
		}
		return contrato;
	}

	public void setContrato(Integer contrato) {
		this.contrato = contrato;
	}

	public Integer getDiaria() {
		if (diaria == null) {
			diaria = 0;
		}
		return diaria;
	}

	public void setDiaria(Integer diaria) {
		this.diaria = diaria;
	}

	public Integer getVendaAvulsa() {
		if (vendaAvulsa == null) {
			vendaAvulsa = 0;
		}
		return vendaAvulsa;
	}

	public void setVendaAvulsa(Integer vendaAvulsa) {
		this.vendaAvulsa = vendaAvulsa;
	}

	public Integer getPersonal() {
		if (personal == null) {
			personal = 0;
		}
		return personal;
	}
	public void setPersonal(Integer personal) {
		this.personal = personal;
	}

	
	public String getNomeCliente() {
		return nomeCliente;
	}
	public void setNomeCliente(String nomeCliente) {
		this.nomeCliente = nomeCliente;
	}
	public Double getValorTotal() {
		return valorTotal;
	}
	public void setValorTotal(Double valorTotal) {
		this.valorTotal = valorTotal;
	}
	public Date getLancamento() {
		return lancamento;
	}
	public void setLancamento(Date lancamento) {
		this.lancamento = lancamento;
	}
	public List<MovParcelaVO> getParcelas() {
		return parcelas;
	}
	public void setParcelas(List<MovParcelaVO> parcelas) {
		this.parcelas = parcelas;
	}
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}
	public String getDescricao() {
		return descricao;
	}
	public void setExibeParcelas(Boolean exibeParcelas) {
		this.exibeParcelas = exibeParcelas;
	}
	public Boolean getExibeParcelas() {
		return exibeParcelas;
	}
	public String getLancamentoFormatada() {
		return Formatador.formatarDataPadrao(this.getLancamento());
	}
	
	public String getValorTotalMonetario() {
		return Formatador.formatarValorMonetarioSemMoeda(this.getValorTotal());
	}

	public void setMarcarTodas(Boolean marcarTodas) {
		this.marcarTodas = marcarTodas;
	}

	public Boolean getMarcarTodas() {
		return marcarTodas;
	}

	public void setPessoa(Integer pessoa) {
		this.pessoa = pessoa;
	}

	public Integer getPessoa() {
		return pessoa;
	}

	public void setParcelasPagasCE(List<MovParcelaVO> parcelasPagasCE) {
		this.parcelasPagasCE = parcelasPagasCE;
	}

	public List<MovParcelaVO> getParcelasPagasCE() {
		return parcelasPagasCE;
	}

	public void setCodigoEvento(Integer codigoEvento) {
		this.codigoEvento = codigoEvento;
	}

	public Integer getCodigoEvento() {
		return codigoEvento;
	}

	public Integer getColaborador() {
		return colaborador;
	}

	public void setColaborador(Integer colaborador) {
		this.colaborador = colaborador;
	}

	public Integer getCliente() {
		return cliente;
	}

	public void setCliente(Integer cliente) {
		this.cliente = cliente;
	}

	public boolean equals(Object o) {
		boolean equal = false;
		if (o instanceof CaixaAbertoTO) {
			CaixaAbertoTO item = (CaixaAbertoTO) o;
			 if(this.getPessoa().equals(item.getPessoa())
					 && this.getContrato().equals(item.getContrato())
					 && this.getDiaria().equals(item.getDiaria())
					 && this.getVendaAvulsa().equals(item.getVendaAvulsa())
					 && this.getPersonal().equals(item.getPersonal())){
				 equal = true;
			 }
				 
				 
		}
		return equal;

	}
	
	public CaixaAbertoTO cloneReduzido(){
		CaixaAbertoTO clone = new CaixaAbertoTO();
		clone.setPessoa(this.getPessoa());
		clone.setContrato(this.getContrato());
		clone.setNomeCliente(this.getNomeCliente());
		clone.setCliente(this.getCliente());
		clone.setColaborador(this.getColaborador());
		clone.setDiaria(getDiaria());
		clone.setPersonal(getPersonal());
		clone.setVendaAvulsa(this.getVendaAvulsa());
		clone.setEmpresaCodigo(this.getEmpresaCodigo());
		clone.setEmpresaNome(this.getEmpresaNome());
		clone.setEmpresaVO(this.getEmpresaVO());
		return clone;
	}
	
	public int hashCode(){  
		return this.getPessoa()
				+this.getContrato()
				+this.getPersonal()
				+this.getVendaAvulsa()
				+this.getDiaria();  
	}

	public void setParcelasSelecionadas(Map<Integer, Double> parcelasSelecionadas) {
		this.parcelasSelecionadas = parcelasSelecionadas;
	}

	public Map<Integer, Double> getParcelasSelecionadas() {
		if(parcelasSelecionadas == null){
			parcelasSelecionadas = new HashMap<Integer, Double>();
		}
		return parcelasSelecionadas;
	}
	public Integer getNrParcelasSelecionadas(){
		  return getParcelasSelecionadas().size();
	}
	
	public Double getValorParcelasSelecionadas(){
		Set<Integer> keySet = getParcelasSelecionadas().keySet();
		Double valor = 0.0;
		for(Integer key : keySet){
			valor += getParcelasSelecionadas().get(key);
		}
		return valor;
	}

    
    public String getNomeAbreviado(){
    	String nome = getNomeCliente();  
		String nomes[] = nome.split(" ");
		if(nomes.length <= 1){
			return nome;
		}
		int tamanho = nomes.length;  
		String abv = nomes[0];  
		 
		for (int i = 1; i < tamanho - 1; i++) {  
			if(nomes[i].length()> 0)
				abv += " " + nomes[i].charAt(0) + ".";  
		}  
		  
		abv += " " + nomes[tamanho - 1]; 
		
		return abv;
    }

	public void setPagamentosCE(List<MovPagamentoVO> pagamentosCE) {
		this.pagamentosCE = pagamentosCE;
	}

	public List<MovPagamentoVO> getPagamentosCE() {
		return pagamentosCE;
	}

    public boolean isExibirBotoesCliente() {
        return !UteisValidacao.emptyNumber(pessoa) && !UteisValidacao.emptyNumber(cliente);
    }

	public boolean isExibirBotoesColaborador() {
		return !UteisValidacao.emptyNumber(pessoa) && !UteisValidacao.emptyNumber(colaborador);
	}

	public String getEmpresaNome() {
		return empresaNome;
	}

	public void setEmpresaNome(String empresaNome) {
		this.empresaNome = empresaNome;
	}

	public Integer getEmpresaCodigo() {
		return empresaCodigo;
	}

	public void setEmpresaCodigo(Integer empresaCodigo) {
		this.empresaCodigo = empresaCodigo;
	}

	public EmpresaVO getEmpresaVO() {
		if (empresaVO == null) {
			empresaVO = new EmpresaVO();
		}
		return empresaVO;
	}

	public void setEmpresaVO(EmpresaVO empresaVO) {
		this.empresaVO = empresaVO;
	}

	public String getDescricaoProduto() {
		return descricaoProduto;
	}

	public void setDescricaoProduto(String descricaoProduto) {
		this.descricaoProduto = descricaoProduto;
	}

	public boolean isApresentarNomeProduto() {
		return apresentarNomeProduto;
	}

	public void setApresentarNomeProduto(boolean apresentarNomeProduto) {
		this.apresentarNomeProduto = apresentarNomeProduto;
	}

	public boolean isExibirBotoesVendaAvulsa(){
		return !UteisValidacao.emptyNumber(vendaAvulsa);
	}
}

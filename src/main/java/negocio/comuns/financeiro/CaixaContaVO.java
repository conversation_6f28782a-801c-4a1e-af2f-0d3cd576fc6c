/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.SuperVO;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import br.com.pactosolucoes.comuns.util.Formatador;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperTO;

/**
 * <AUTHOR>
 */
public class CaixaContaVO extends SuperVO {

    private CaixaVO caixaVo = new CaixaVO();
    private ContaVO contaVo = new ContaVO();
    private double saldoInicial = 0;
    private double saldoFinal = 0;
    @NaoControlarLogAlteracao
    private Double entrada = 0.0;
    @NaoControlarLogAlteracao
    private Double saida = 0.0;

    @NaoControlarLogAlteracao
    private List<ItemRelatorioFechamentoCaixaTO> listaMovimentacoes = new ArrayList<ItemRelatorioFechamentoCaixaTO>();

    public String getDescricao() {
        return contaVo.getDescricao();
    }

    public CaixaVO getCaixaVo() {
        return caixaVo;
    }

    public void setCaixaVo(CaixaVO caixaVo) {
        this.caixaVo = caixaVo;
    }

    public ContaVO getContaVo() {
        return contaVo;
    }

    public void setContaVo(ContaVO contaVo) {
        this.contaVo = contaVo;
    }

    public double getSaldoFinal() {
        return saldoFinal;
    }

    public void setSaldoFinal(double saldoFinal) {
        this.saldoFinal = saldoFinal;
    }

    public double getSaldoInicial() {
        return saldoInicial;
    }

    public void setSaldoInicial(double saldoInicial) {
        this.saldoInicial = saldoInicial;
    }

    public void setEntrada(Double entrada) {
        this.entrada = entrada;
    }

    public Double getEntrada() {
        return entrada;
    }

    public void setSaida(Double saida) {
        this.saida = saida;
    }

    public Double getSaida() {
        return saida;
    }

    public String getInicialApresentar() {
        return (Formatador.formatarValorMonetarioSemMoeda(this.saldoInicial));
    }

    public String getSaldoAtualApresentar() {
        return (Formatador.formatarValorMonetarioSemMoeda(this.saldoFinal));
    }

    public String getSaidaApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(this.saida);
    }

    public String getEntradaApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(this.entrada);
    }

    public void setListaMovimentacoes(List<ItemRelatorioFechamentoCaixaTO> listaMovimentacoes) {
        this.listaMovimentacoes = listaMovimentacoes;
    }

    public List<ItemRelatorioFechamentoCaixaTO> getListaMovimentacoes() {
        return listaMovimentacoes;
    }

    public JRDataSource getListaMovimentacoesJR() {
        JRDataSource jr1 = new JRBeanCollectionDataSource(getListaMovimentacoes());
        return jr1;
    }

    public boolean getTemMovimentacoes() {
        return !getListaMovimentacoes().isEmpty();
    }

}

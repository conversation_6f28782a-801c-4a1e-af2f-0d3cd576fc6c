/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
public class MonitoramentoVO extends SuperVO {

    private Integer codigo;
    private Long alunosAtivosNaRede;
    private Long alunosAtivosPagantes;
    private Long aulasExperimentaisMarcadas;
    private Long aulasMarcadas;
    private BigDecimal aulasConvertidas;
    private String chaveECodigo;
    private Long checkins;
    private BigDecimal churn;
    private Long desafiadosDoMes;
    private Date dia;
    private BigDecimal faturamentoMensalidade;
    private BigDecimal faturamentoMerchan;
    private BigDecimal faturamentoNutricao;
    private BigDecimal faturamentoTotal;
    private BigDecimal ltvAluno;
    private String nomeUnidade;
    private Long planosAnuais;
    private Long planosMensais;
    private Long planosSemestrais;
    private Long planosTrimestrais;
    private BigDecimal ticketMedio;
    private List<MonitoramentoHistoricoMesVO> monitoramentoHistoricoMesProduto;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Long getAlunosAtivosNaRede() {
        return alunosAtivosNaRede;
    }

    public Long getAlunosAtivosPagantes() {return alunosAtivosPagantes;}

    public void setAlunosAtivosPagantes(Long alunosAtivosPagantes) {this.alunosAtivosPagantes = alunosAtivosPagantes;}

    public void setAlunosAtivosNaRede(Long alunosAtivosNaRede) {
        this.alunosAtivosNaRede = alunosAtivosNaRede;
    }

    public Long getAulasExperimentaisMarcadas() {
        return aulasExperimentaisMarcadas;
    }

    public void setAulasExperimentaisMarcadas(Long aulasExperimentaisMarcadas) {
        this.aulasExperimentaisMarcadas = aulasExperimentaisMarcadas;
    }

    public Long getAulasMarcadas() {
        return aulasMarcadas;
    }

    public void setAulasMarcadas(Long aulasMarcadas) {
        this.aulasMarcadas = aulasMarcadas;
    }

    public BigDecimal getAulasConvertidas() {
        return aulasConvertidas;
    }

    public void setAulasConvertidas(BigDecimal aulasConvertidas) {
        this.aulasConvertidas = aulasConvertidas;
    }

    public String getChaveECodigo() {
        return chaveECodigo;
    }

    public void setChaveECodigo(String chaveECodigo) {
        this.chaveECodigo = chaveECodigo;
    }

    public Long getCheckins() {
        return checkins;
    }

    public void setCheckins(Long checkins) {
        this.checkins = checkins;
    }

    public BigDecimal getChurn() {
        return churn;
    }

    public void setChurn(BigDecimal churn) {
        this.churn = churn;
    }

    public Long getDesafiadosDoMes() {
        return desafiadosDoMes;
    }

    public void setDesafiadosDoMes(Long desafiadosDoMes) {
        this.desafiadosDoMes = desafiadosDoMes;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public BigDecimal getFaturamentoMensalidade() {
        return faturamentoMensalidade;
    }

    public void setFaturamentoMensalidade(BigDecimal faturamentoMensalidade) {
        this.faturamentoMensalidade = faturamentoMensalidade;
    }

    public BigDecimal getFaturamentoMerchan() {
        return faturamentoMerchan;
    }

    public void setFaturamentoMerchan(BigDecimal faturamentoMerchan) {
        this.faturamentoMerchan = faturamentoMerchan;
    }

    public BigDecimal getFaturamentoNutricao() {
        return faturamentoNutricao;
    }

    public void setFaturamentoNutricao(BigDecimal faturamentoNutricao) {
        this.faturamentoNutricao = faturamentoNutricao;
    }

    public BigDecimal getFaturamentoTotal() {
        return faturamentoTotal;
    }

    public void setFaturamentoTotal(BigDecimal faturamentoTotal) {
        this.faturamentoTotal = faturamentoTotal;
    }

    public BigDecimal getLtvAluno() {
        return ltvAluno;
    }

    public void setLtvAluno(BigDecimal ltvAluno) {
        this.ltvAluno = ltvAluno;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public Long getPlanosAnuais() {
        return planosAnuais;
    }

    public void setPlanosAnuais(Long planosAnuais) {
        this.planosAnuais = planosAnuais;
    }

    public Long getPlanosMensais() {
        return planosMensais;
    }

    public void setPlanosMensais(Long planosMensais) {
        this.planosMensais = planosMensais;
    }

    public Long getPlanosSemestrais() {
        return planosSemestrais;
    }

    public void setPlanosSemestrais(Long planosSemestrais) {
        this.planosSemestrais = planosSemestrais;
    }

    public Long getPlanosTrimestrais() {
        return planosTrimestrais;
    }

    public void setPlanosTrimestrais(Long planosTrimestrais) {
        this.planosTrimestrais = planosTrimestrais;
    }

    public BigDecimal getTicketMedio() {
        return ticketMedio;
    }

    public void setTicketMedio(BigDecimal ticketMedio) {
        this.ticketMedio = ticketMedio;
    }

    public List<MonitoramentoHistoricoMesVO> getMonitoramentoHistoricoMesProduto() {
        return monitoramentoHistoricoMesProduto;
    }

    public void setMonitoramentoHistoricoMesProduto(List<MonitoramentoHistoricoMesVO> monitoramentoHistoricoMesProduto) {
        this.monitoramentoHistoricoMesProduto = monitoramentoHistoricoMesProduto;
    }
}

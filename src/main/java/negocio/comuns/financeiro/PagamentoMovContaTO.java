package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;

import negocio.comuns.financeiro.enumerador.TipoFormaPagto;

public class PagamentoMovContaTO extends SuperTO {

    private static final long serialVersionUID = -2043980389313751689L;
    private FormaPagamentoVO formaPagamento = new FormaPagamentoVO();
    private Double valor = 0.0;
    private LoteVO lote = new LoteVO();
    private List<ChequeVO> cheques = new ArrayList<ChequeVO>();
    private List<MovContaRateioVO> rateios = new ArrayList<MovContaRateioVO>();

    public Double valorDosRateios() {
        Double soma = 0.0;
        for (MovContaRateioVO rateio : rateios) {
            soma += rateio.getValor();
        }
        return soma;
    }

    public FormaPagamentoVO formaPagtoRateios() {
        return rateios.isEmpty() ? new FormaPagamentoVO() : rateios.get(0).getFormaPagamentoVO();
    }

    public void setFormaPagamento(FormaPagamentoVO formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public FormaPagamentoVO getFormaPagamento() {
        if (formaPagamento == null) {
            formaPagamento = new FormaPagamentoVO();
        }
        return formaPagamento;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getValor() {
        return valor;
    }

    public void setLote(LoteVO lote) {
        this.lote = lote;
    }

    public LoteVO getLote() {
        return lote;
    }

    public void setRateios(List<MovContaRateioVO> rateios) {
        this.rateios = rateios;
    }

    public List<MovContaRateioVO> getRateios() {
        return rateios;
    }

    public void setCheques(List<ChequeVO> cheques) {
        this.cheques = cheques;
    }

    public List<ChequeVO> getCheques() {
        return cheques;
    }

    public boolean getPagarComLote() {
        return formaPagamento != null
                && formaPagamento.getTipoFormaPagamento() != null
                && formaPagamento.getTipoFormaPagamento().equals(TipoFormaPagto.LOTE.getSigla());
    }

    public boolean getPagarComCheque() {
        return formaPagamento != null
                && formaPagamento.getTipoFormaPagamento() != null
                && formaPagamento.getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla());
    }

    public boolean getEditarValor() {
        return !getPagarComCheque() && !getPagarComLote();
    }

    public String getValorFormatado() {
        return Formatador.formatarValorNumerico(this.getValor(), Formatador.FRMT_NUM_PADRAO);
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * <AUTHOR>
 */
public class TransacaoImpressaoTO extends SuperTO {

    private String codigoExterno;
    private String tid;
    private String numeroCartao;
    private String dataVenda;
    private String dataCancelamento;
    private String valorVenda;
    private String valorCancelado;
    private String codigoAutorizacao;
    private String nsu;
    private EmpresaVO empresa;
    private TipoTransacaoEnum tipoTransacaoEnum;
    private String tipoGestaoTransacao;

    public TransacaoImpressaoTO(TransacaoVO transacaoVO) {
        this.codigoExterno = transacaoVO.getCodigoExterno();
        this.tid = transacaoVO.getTID();
        this.numeroCartao = transacaoVO.getCartaoMascarado();
        this.dataVenda = transacaoVO.getDataProcessamento_Apresentar();
        this.dataCancelamento = transacaoVO.getDataHoraCancelamento_Apresentar();
        this.valorVenda = transacaoVO.getValor_Apresentar();
        this.valorCancelado = transacaoVO.getValor_Apresentar();
        this.empresa = transacaoVO.getEmpresaVO();
        this.tipoTransacaoEnum = transacaoVO.getTipo();
        this.tipoGestaoTransacao = transacaoVO.getTipoGestaoTransacao();

        String autorizacao = transacaoVO.getCodigoAutorizacao();
        if (UteisValidacao.emptyString(autorizacao)) {
            autorizacao = transacaoVO.getAutorizacao();
        }
        this.codigoAutorizacao = autorizacao;
        this.nsu = transacaoVO.getNSU();
    }

    public String getCodigoExterno() {
        if (codigoExterno == null) {
            codigoExterno = "";
        }
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public String getNumeroCartao() {
        if (numeroCartao == null) {
            numeroCartao = "";
        }
        return numeroCartao;
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public String getDataVenda() {
        if (dataVenda == null) {
            dataVenda = "";
        }
        return dataVenda;
    }

    public void setDataVenda(String dataVenda) {
        this.dataVenda = dataVenda;
    }

    public String getDataCancelamento() {
        if (dataCancelamento == null) {
            dataCancelamento = "";
        }
        return dataCancelamento;
    }

    public void setDataCancelamento(String dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public String getValorVenda() {
        if (valorVenda == null) {
            valorVenda = "";
        }
        return valorVenda;
    }

    public void setValorVenda(String valorVenda) {
        this.valorVenda = valorVenda;
    }

    public String getValorCancelado() {
        if (valorCancelado == null) {
            valorCancelado = "";
        }
        return valorCancelado;
    }

    public void setValorCancelado(String valorCancelado) {
        this.valorCancelado = valorCancelado;
    }

    public String getCodigoAutorizacao() {
        if (codigoAutorizacao == null) {
            codigoAutorizacao = "";
        }
        return codigoAutorizacao;
    }

    public void setCodigoAutorizacao(String codigoAutorizacao) {
        this.codigoAutorizacao = codigoAutorizacao;
    }

    public String getNsu() {
        if (nsu == null) {
            nsu = "";
        }
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getEmpresaNome() {
        return getEmpresa().getRazaoSocial();
    }

    public String getEmpresaEndereco1() {
        StringBuilder end = new StringBuilder();

        if (!UteisValidacao.emptyString(getEmpresa().getEndereco())) {
            end.append(getEmpresa().getEndereco());
        }

        if (!UteisValidacao.emptyString(getEmpresa().getNumero())) {
            if (end.length() > 0) {
                end.append(", ");
            }
            end.append(getEmpresa().getNumero());
        }


        if (!UteisValidacao.emptyString(getEmpresa().getSetor())) {
            if (end.length() > 0) {
                end.append(", ");
            }
            end.append(getEmpresa().getSetor());
        }


        if (!UteisValidacao.emptyString(getEmpresa().getComplemento())) {
            if (end.length() > 0) {
                end.append(", ");
            }
            end.append(getEmpresa().getComplemento());
        }
        return end.toString();
    }

    public String getEmpresaEndereco2() {
        StringBuilder end = new StringBuilder();

        //cidade
        end.append(getEmpresa().getCidade_Apresentar());
        if (end.length() > 0) {
            end.append(" - ");
        }

        //estado
        end.append(getEmpresa().getEstado().getSigla());

        if (end.length() > 0) {
            end.append(" - ");
        }

        //cep
        end.append(getEmpresa().getCEP());
        return end.toString();
    }

    public String getEmpresaEndereco3() {
        return "";
    }

    public String getTid() {
        if (tid == null) {
            tid = "";
        }
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getTipoTransacao() {
        return this.getTipoTransacaoEnum().name();
    }

    public TipoTransacaoEnum getTipoTransacaoEnum() {
        if (tipoTransacaoEnum == null) {
            tipoTransacaoEnum = TipoTransacaoEnum.NENHUMA;
        }
        return tipoTransacaoEnum;
    }

    public void setTipoTransacaoEnum(TipoTransacaoEnum tipoTransacaoEnum) {
        this.tipoTransacaoEnum = tipoTransacaoEnum;
    }

    public String getTipoGestaoTransacao() {
        if (tipoGestaoTransacao == null) {
            tipoGestaoTransacao = "";
        }
        return tipoGestaoTransacao;
    }

    public void setTipoGestaoTransacao(String tipoGestaoTransacao) {
        this.tipoGestaoTransacao = tipoGestaoTransacao;
    }

    private String getAdquirente() {
        if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.CIELO_ONLINE) || this.getTipoGestaoTransacao().toUpperCase().contains("CIELO")) {
            return "CIELO";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.E_REDE) || this.getTipoGestaoTransacao().toUpperCase().contains("REDE")) {
            return "REDE";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.STONE_ONLINE) || this.getTipoGestaoTransacao().toUpperCase().contains("STONE")) {
            return "STONE";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.GETNET_ONLINE) || this.getTipoGestaoTransacao().toUpperCase().contains("GETNET") || this.getTipoGestaoTransacao().toUpperCase().contains("GET NET")) {
            return "GETNET";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.VINDI) || this.getTipoGestaoTransacao().toUpperCase().contains("VINDI")) {
            return "VINDI";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.MUNDIPAGG) || this.getTipoGestaoTransacao().toUpperCase().contains("MUNDIPAGG")) {
            return "MUNDIPAGG";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.PAGAR_ME) || this.getTipoGestaoTransacao().toUpperCase().contains("PAGARME")) {
            return "PAGARME";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.PAGBANK) || this.getTipoGestaoTransacao().toUpperCase().contains("PAGBANK")) {
            return "PAGBANK";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.STRIPE) || this.getTipoGestaoTransacao().toUpperCase().contains("STRIPE")) {
            return "STRIPE";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.PAGOLIVRE) || this.getTipoGestaoTransacao().toUpperCase().contains("PAGOLIVRE")) {
            return "PAGOLIVRE";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.FACILITEPAY) ||
                this.getTipoGestaoTransacao().toUpperCase().contains("FACILITEPAY")
                || this.getTipoGestaoTransacao().toUpperCase().contains("FYPAY")) {
            return "FYPAY";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.CEOPAG) || this.getTipoGestaoTransacao().toUpperCase().contains("CEOPAG")) {
            return "CEOPAG";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.PINBANK) || this.getTipoGestaoTransacao().toUpperCase().contains("PINBANK")) {
            return "PINBANK";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.ONE_PAYMENT) || this.getTipoGestaoTransacao().toUpperCase().contains("ONEPAYMENT")) {
            return "ONEPAYMENT";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.MAXIPAGO) || this.getTipoGestaoTransacao().toUpperCase().contains("MAXIPAGO")) {
            return "MAXIPAGO";
        } else if (this.getTipoTransacaoEnum().equals(TipoTransacaoEnum.DCC_CAIXA_ONLINE) || this.getTipoGestaoTransacao().toUpperCase().contains("CAIXA")) {
            return "CAIXA";
        } else {
            return "";
        }
    }

    public String getLogomarca() {
        if (this.getAdquirente().equalsIgnoreCase("CIELO")) {
            return "logo_cielo.jpg";
        } else if (this.getAdquirente().equalsIgnoreCase("REDE")) {
            return "logo_rede.png";
        } else if (this.getAdquirente().equalsIgnoreCase("STONE")) {
            return "logo_stone.png";
        } else if (this.getAdquirente().equalsIgnoreCase("GETNET")) {
            return "logo_getnet.png";
        } else if (this.getAdquirente().equalsIgnoreCase("GLOBALPAYMENTS")) {
            return "logo_globalpayments.png";
        } else if (this.getAdquirente().equalsIgnoreCase("VINDI")) {
            return "logo_vindi.png";
        } else if (this.getAdquirente().equalsIgnoreCase("MUNDIPAGG")) {
            return "logo_mundipagg.png";
        } else if (this.getAdquirente().equalsIgnoreCase("PAGARME")) {
            return "logo_pagarme.png";
        } else if (this.getAdquirente().equalsIgnoreCase("STRIPE")) {
            return "logo_stripe.png";
        } else if (this.getAdquirente().equalsIgnoreCase("PAGOLIVRE")) {
            return "logo_pagolivre.png";
        } else if (this.getAdquirente().equalsIgnoreCase("PINBANK")) {
            return "logo_pinBank.png";
        } else if (this.getAdquirente().equalsIgnoreCase("ONEPAYMENT")) {
            return "logo_one_payment.png";
        } else if (this.getAdquirente().equalsIgnoreCase("MAXIPAGO")) {
            return "logo_maxipago.png";
        } else if (this.getAdquirente().equalsIgnoreCase("CEOPAG")) {
            return "logo_ceopag.png";
        } else if (this.getAdquirente().equalsIgnoreCase("CAIXA")) {
            return "logo_caixa.png";
        } else {
            return "";
        }
    }

    //Utilizado no Ireport
    public String getMensagemInformacao() {
        if (this.getAdquirente().equalsIgnoreCase("CIELO")) {
            return "Para quaisquer outros esclarecimentos, entre em contato com a Central de Relacionamento Cielo. \n" +
                    "Atendimento de segunda a sábado, das 8h às 22h. \n" +
                    "Ligue 4002 5472 (capitais e regiões metropolitanas) ou 0800 570 8472(demais localidades) ou acesse o site www.cielo.com.br e clique no botão \"Fale Conosco\".";

        } else if (this.getAdquirente().equalsIgnoreCase("REDE")) {
            return "Para quaisquer outros esclarecimentos, entre em contato com a Central de Relacionamento Rede. \n" +
                    "Atendimento de segunda à sábado, das 8h às 22h. \n" +
                    "Ligue 4001 4433 (capitais e regiões metropolitanas) ou 0800 728 4433(demais localidades) ou acesse o site www.userede.com.br e clique no botão \"Fale Conosco\".";

        } else if (this.getAdquirente().equalsIgnoreCase("STONE")) {
            return "Para quaisquer outros esclarecimentos, entre em contato com a Central de Relacionamento Stone. \n" +
                    "Atendimento de segunda à sexta, das 8h às 18h. \n" +
                    "Ligue 3004 9680 (capitais e regiões metropolitanas) ou 0800 326 0506(demais localidades) ou acesse o site www.stone.com.br e clique em \"Atendimento\".";

        } else if (this.getAdquirente().equalsIgnoreCase("GETNET")) {
            return "Para quaisquer outros esclarecimentos, entre em contato com a Central de Relacionamento Getnet. \n" +
                    "Atendimento de segunda à sexta, das 8h30 às 17h30 - EXCETO FERIADOS. \n" +
                    "Ligue 0800 646 3404 ou acesse o site www.getnet.com.br e clique em \"Atendimento\".";

        } else if (this.getAdquirente().equalsIgnoreCase("VINDI")) {
            return "Para quaisquer outros esclarecimentos, entre em contato com a Central de Relacionamento Vindi. \n" +
                    "Atendimento de segunda à sexta, de 8h30 às 19h. \n" +
                    "Acesse o site www.vindi.com.br e clique em \"Atendimento\".";

        } else if (this.getAdquirente().equalsIgnoreCase("MUNDIPAGG")) {
            return "Para quaisquer outros esclarecimentos, entre em contato com a Central de Relacionamento Mundipagg. \n" +
                    "Atendimento de segunda à sexta, de 9h às 18h. \n" +
                    "Acesse o site www.mundipagg.com e clique em \"Atendimento\".";

        } else if (this.getAdquirente().equalsIgnoreCase("PAGARME")) {
            return "Para quaisquer outros esclarecimentos, entre em contato com a Central de Relacionamento Pagar.me. \n" +
                    "Atendimento de segunda à sexta, de 9h às 18h. \n" +
                    "Acesse o site www.pagar.me e clique em \"Atendimento\".";
        } else if (this.getAdquirente().equalsIgnoreCase("GLOBALPAYMENTS")) {
            return "Para quaisquer outros esclarecimentos, entre em contato com a Central de Relacionamento Global Payments. \n" +
                    "Atendimento de segunda à domingo, de 8h às 22h. \n" +
                    "Ligue 0800 772 5213 ou acesse o site www.globalpaymentsinc.com e clique em \"Atendimento\".";
        } else if (this.getAdquirente().equalsIgnoreCase("PAGOLIVRE")) {
            return "Para quaisquer outros esclarecimentos, entre em contato com a Central de Relacionamento PagoLivre. \n" +
                    "Telefone/WhatsApp (11) 4380-7056 e/ou acesse o site www.pagolivre.com.br  e clique na opção  \"Suporte\". \n" +
                    "Atendimento de segunda à sábado, das 9h às 19h (exceto feriados nacionais).";
        } else if (this.getAdquirente().equalsIgnoreCase("PINBANK")) {
            return "Para quaisquer outros esclarecimentos, entre em contato com a Central de Relacionamento PinBank. \n" +
                    "Atendimento via whatsapp que pode ser encontrado através do site https://www.pinbank.com.br/ \n";
        }
        else {
            return "";
        }
    }

    //Utilizado no Ireport
    public String getMensagemInfoEstorno() {
        if (this.getAdquirente().equalsIgnoreCase("PAGOLIVRE")) {
            return "Informamos que o reembolso ao portador do cartão dependerá do processamento pelo banco emissor.";
        } else {
            return "Informamos que o crédito ao portador do cartão dependerá do processamento deste valor pelo banco emissor do mesmo \n" +
                    "e da data de fechamento da fatura do(a) cliente.";
        }
    }
}

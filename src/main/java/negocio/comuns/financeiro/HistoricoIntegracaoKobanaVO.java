package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 * Created by <PERSON> on 25/06/2024.
 */

public class HistoricoIntegracaoKobanaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer empresa;
    protected String metodo;
    protected Integer lote;
    protected String paramsEnvio;
    protected String paramsRetorno;
    protected Date dataRegistro;
    protected boolean sucesso;


    public HistoricoIntegracaoKobanaVO() {
        super();
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getMetodo() {
        return metodo;
    }

    public void setMetodo(String metodo) {
        this.metodo = metodo;
    }

    public String getParamsEnvio() {
        return paramsEnvio;
    }

    public void setParamsEnvio(String paramsEnvio) {
        this.paramsEnvio = paramsEnvio;
    }

    public String getParamsRetorno() {
        return paramsRetorno;
    }

    public void setParamsRetorno(String paramsRetorno) {
        this.paramsRetorno = paramsRetorno;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public Integer getLote() {
        return lote;
    }

    public void setLote(Integer lote) {
        this.lote = lote;
    }
}

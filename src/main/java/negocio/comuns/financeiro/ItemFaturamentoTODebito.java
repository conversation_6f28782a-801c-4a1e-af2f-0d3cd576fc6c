package negocio.comuns.financeiro;

import java.math.BigDecimal;

class ItemFaturamentoTODebito extends ItemFaturamentoTO {
    private final BigDecimal taxa = new BigDecimal("1.25");

    @Override
    public BigDecimal getTaxaSTONE() {
        return taxa;
    }

    @Override
    public String getFormaPagamento() {
        return "DÉBITO";
    }

    @Override
    public int getOrdem() {
        return 0;
    }
}

package negocio.comuns.financeiro;

import java.util.Date;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;

import negocio.comuns.utilitarias.Uteis;

public class ComprovanteOperacaoTO extends SuperTO {

    private static final long serialVersionUID = -3281829567739905813L;
    private String codigo = "";
    private String tipoOperacao = "";
    private String contaDestino = "";
    private String descricao = "";
    private String lote = "";
    private String formaPagamento = "";
    private Double valor = 0.0;
    private Date dataQuitacao;
    private Date dataLancamento;
    private String fornecedor;
    private String observacoes;

    public String getValor_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valor);
    }

    public String getData_Apresentar() {
        return Uteis.getData(getDataLancamento());
    }

    public String getDataQuitacao_Apresentar() {
        return Uteis.getData(getDataQuitacao());
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setContaDestino(String contaDestino) {
        this.contaDestino = contaDestino;
    }

    public String getContaDestino() {
        return contaDestino;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setLote(String lote) {
        this.lote = lote;
    }

    public String getLote() {
        return lote;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getValor() {
        return valor;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public String getFormaPagamento() {
        return formaPagamento;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setDataQuitacao(Date dataQuitacao) {
        this.dataQuitacao = dataQuitacao;
    }

    public Date getDataQuitacao() {
        return dataQuitacao;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public String getFornecedor() {
        return fornecedor;
    }

    public void setFornecedor(String fornecedor) {
        this.fornecedor = fornecedor;
    }

    public String getObservacoes() {
        return observacoes;
    }

    public void setObservacoes(String observacoes) {
        this.observacoes = observacoes;
    }
}

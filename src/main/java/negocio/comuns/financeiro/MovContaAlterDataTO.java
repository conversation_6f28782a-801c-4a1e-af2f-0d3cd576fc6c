package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;

/**
 * Created by ulisses on 23/02/2017.
 */
public class MovContaAlterDataTO extends SuperTO {
    private String lancamentoAutomatico;
    private Integer contaDevedora;
    private Integer contaCredora;
    private String dataCompetencia;
    private Double valor;
    private Integer codigoHistorico;
    private String complementoHistorico;
    private Integer centroCustoDebito;
    private Integer centroCustoCredito;
    private Integer numeroDocumento;
    private String dataQuitacao;
    private String descricaoContaCredora;
    private String descricaoContaDevedora;
    private String descricaoHistoricoContabil;

    public MovContaAlterDataTO clone(){
        MovContaAlterDataTO obj = new MovContaAlterDataTO();
        obj.setLancamentoAutomatico(this.lancamentoAutomatico);
        obj.setContaDevedora(this.contaDevedora);
        obj.setContaCredora(this.contaCredora);
        obj.setDataCompetencia(this.dataCompetencia);
        obj.setValor(this.valor);
        obj.setCodigoHistorico(this.codigoHistorico);
        obj.setComplementoHistorico(this.complementoHistorico);
        obj.setCentroCustoCredito(this.centroCustoCredito);
        obj.setCentroCustoDebito(this.centroCustoDebito);
        obj.setNumeroDocumento(this.numeroDocumento);
        obj.setDataQuitacao(this.dataQuitacao);
        obj.setDescricaoContaCredora(this.descricaoContaCredora);
        obj.setDescricaoContaDevedora(this.descricaoContaDevedora);
        obj.setDescricaoHistoricoContabil(this.descricaoHistoricoContabil);
        return obj;
    }

    public String getLancamentoAutomatico() {
        return lancamentoAutomatico;
    }

    public void setLancamentoAutomatico(String lancamentoAutomatico) {
        this.lancamentoAutomatico = lancamentoAutomatico;
    }

    public Integer getContaDevedora() {
        return contaDevedora;
    }

    public void setContaDevedora(Integer contaDevedora) {
        this.contaDevedora = contaDevedora;
    }

    public Integer getContaCredora() {
        return contaCredora;
    }

    public void setContaCredora(Integer contaCredora) {
        this.contaCredora = contaCredora;
    }

    public String getDataCompetencia() {
        return dataCompetencia;
    }

    public void setDataCompetencia(String dataCompetencia) {
        this.dataCompetencia = dataCompetencia;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getCodigoHistorico() {
        return codigoHistorico;
    }

    public void setCodigoHistorico(Integer codigoHistorico) {
        this.codigoHistorico = codigoHistorico;
    }

    public String getComplementoHistorico() {
        return complementoHistorico;
    }

    public void setComplementoHistorico(String complementoHistorico) {
        this.complementoHistorico = complementoHistorico;
    }

    public Integer getCentroCustoDebito() {
        return centroCustoDebito;
    }

    public void setCentroCustoDebito(Integer centroCustoDebito) {
        this.centroCustoDebito = centroCustoDebito;
    }

    public Integer getCentroCustoCredito() {
        return centroCustoCredito;
    }

    public void setCentroCustoCredito(Integer centroCustoCredito) {
        this.centroCustoCredito = centroCustoCredito;
    }

    public Integer getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(Integer numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public String getDataQuitacao() {
        return dataQuitacao;
    }

    public void setDataQuitacao(String dataQuitacao) {
        this.dataQuitacao = dataQuitacao;
    }

    public String getDescricaoContaCredora() {
        if (descricaoContaCredora == null){
            return "";
        }
        return descricaoContaCredora;
    }

    public void setDescricaoContaCredora(String descricaoContaCredora) {
        this.descricaoContaCredora = descricaoContaCredora;
    }

    public String getDescricaoContaDevedora() {
        if (descricaoContaDevedora == null){
            return "";
        }
        return descricaoContaDevedora;
    }

    public void setDescricaoContaDevedora(String descricaoContaDevedora) {
        this.descricaoContaDevedora = descricaoContaDevedora;
    }

    public String getDescricaoHistoricoContabil() {
        if (descricaoHistoricoContabil == null){
            return "";
        }
        return descricaoHistoricoContabil;
    }

    public void setDescricaoHistoricoContabil(String descricaoHistoricoContabil) {
        this.descricaoHistoricoContabil = descricaoHistoricoContabil;
    }

}

package negocio.comuns.financeiro;

import org.json.JSONObject;

/**
 * Created by <PERSON> on 18/07/2023.
 */

public class PluggyAccountBankDataDTO {

    protected String transferNumber;
    protected double closingBalance;

    public PluggyAccountBankDataDTO() throws Exception {
    }

    public PluggyAccountBankDataDTO(JSONObject json) throws Exception {
        //receber objeto connector do json
        this.transferNumber = json.optString("transferNumber", "");
        this.closingBalance = json.optDouble("name", 0.0);
    }

    public String getTransferNumber() {
        return transferNumber;
    }

    public void setTransferNumber(String transferNumber) {
        this.transferNumber = transferNumber;
    }

    public double getClosingBalance() {
        return closingBalance;
    }

    public void setClosingBalance(double closingBalance) {
        this.closingBalance = closingBalance;
    }
}

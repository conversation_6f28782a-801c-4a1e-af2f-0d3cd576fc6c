package negocio.comuns.financeiro;


import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.pagolivre.PagoLivreRetornoEnum;

/**
 * Created by Estulano on 23/09/2021.
 */

public class TransacaoPagoLivreVO extends TransacaoVO {

    public String getValorCartaoMascarado() throws Exception {
        JSONObject obj = new JSONObject(getParamsEnvio());
        return APF.getCartaoMascarado(obj.getJSONObject("card").getString("cardMask"));
    }

    public String getValorUltimaTransacaoAprovada() throws Exception {
        return getValorAtributoResposta(APF.Transacao);
    }

    public String getResultadoRequisicao() {
        String msg = "";
        if (!UteisValidacao.emptyString(getReturnCode())) {
            msg = PagoLivreRetornoEnum.valueOff(getReturnCode()).getDescricao();
        } else if (!UteisValidacao.emptyString(getParamsResposta())){
            try {
                JSONObject obj = new JSONObject(getParamsResposta());
                msg = obj.optString("errors");
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        if ((msg == null || UteisValidacao.emptyString(msg.replaceAll(" ", "").trim())) && !UteisValidacao.emptyString(getParamsResposta())) {
            try {
                JSONObject obj = new JSONObject(getParamsResposta());
                msg = obj.optString("reason");
                if (UteisValidacao.emptyString(msg)) {
                    msg = obj.optString("message");
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return msg;
    }

    public String getAutorizacao() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            return obj.getString("acquirerAuthorization");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getPaymentId() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            return obj.getString("paymentId");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getBandeira() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            String band = obj.getJSONObject("card").optString("cardBrand");

            //obter descricão da bandeira
            OperadorasExternasAprovaFacilEnum operadora = OperadorasExternasAprovaFacilEnum.valueOf(Integer.parseInt(band));
            if (!UteisValidacao.emptyString(operadora.getDescricao())) {
                for (OperadorasExternasAprovaFacilEnum ope : OperadorasExternasAprovaFacilEnum.values()) {
                    String operadoraEnum = ope.getDescricao().toUpperCase().replaceAll(" ", "");
                    String operadoraPagamento = operadora.getDescricao().toUpperCase().replaceAll(" ", "");
                    if (operadoraEnum.equalsIgnoreCase(operadoraPagamento)) {
                        if (operadoraPagamento.equals("MASTERCARD")){
                            return "MASTER";
                        } else {
                            return operadoraPagamento;
                        }
                    }
                }
            }
        } catch (Exception ex) {
            return "";
        }
        return "";
    }

    public String getCartaoMascarado() {
        String result = "";
        try {
            result = getValorCartaoMascarado();
        } catch (Exception e) {
        }
        return result;
    }

    public String getValorAtributoResposta(String nomeAtributo) throws Exception {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao)) {
            return getPaymentId();
        }
        return valor;
    }

    public String getValorAtributoCancelamento(String nomeAtributo) throws Exception {
        String valor = "";
        try {
            JSONObject json = new JSONObject(getResultadoCancelamento());
            String returnCode = json.getString("returnCode");
            if (!UteisValidacao.emptyString(returnCode)) {
                return PagoLivreRetornoEnum.valueOff(returnCode).getDescricao();
            }
        } catch (Exception ex) {
            return "";
        }
        return valor;
    }

    public String getPayment() throws Exception {
        try {
            return this.getParamsResposta();
        } catch (Exception e) {
            return "";
        }
    }

    public String getCodErroExterno() {
        return getReturnCode();
    }

    public String getReturnCode() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            return json.optString("code");
            //retorna código somente se não estiver aprovada
        } catch (Exception ex) {
            return "";
        }
    }

    public String getReason() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            return json.optString("reason");
            //retorna código somente se não estiver aprovada
        } catch (Exception ex) {
            return "";
        }
    }
    public String getNSU() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            return json.getString("paymentId");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getTID() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            return json.getString("paymentId");
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer getNrVezes() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            return obj.getInt("installments");
        } catch (Exception ex) {
            return 0;
        }
    }
}

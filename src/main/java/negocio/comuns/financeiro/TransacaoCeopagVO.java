package negocio.comuns.financeiro;


import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.pagolivre.PagoLivreRetornoEnum;

public class TransacaoCeopagVO extends TransacaoVO {

    public String getValorCartaoMascarado() throws Exception {
        try {
            JSONObject transaction = firstTransaction();
            JSONObject card = transaction.getJSONObject("card");
            String cardNumber = card.optString("cardNumber");
            if (!UteisValidacao.emptyString(cardNumber)) {
                return cardNumber;
            }
        } catch (Exception ignored) {
        }
        try {
            String card = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoMascarado);
            return APF.getCartaoMascarado(card);
        } catch (Exception ignored) {
        }
        return "";
    }

    public String getValorUltimaTransacaoAprovada() throws Exception {
        return getValorAtributoResposta(APF.Transacao);
    }

    public String getResultadoRequisicao() {
        StringBuilder ret = new StringBuilder();
        try {
            if (UteisValidacao.emptyString(getParamsResposta())) {
                return "";
            }
            JSONObject json = new JSONObject(getParamsResposta());
            JSONArray erros = json.optJSONArray("errors");
            if (erros != null && erros.length() > 0) {
                for (int i = 0; i < erros.length(); i++) {
                    JSONObject jsonEr = erros.getJSONObject(i);
                    String message = jsonEr.optString("message");
                    if (!UteisValidacao.emptyString(message)) {
                        ret.append(", ").append(message);
                    }
                }
            }

            JSONObject charge = json.optJSONObject("charge");
            String chargeStatus = charge.optString("chargeStatus");
            JSONArray transactions = charge != null ? charge.optJSONArray("transactions") : null;
            if (transactions != null && transactions.length() > 0) {
                for (int i = 0; i < transactions.length(); i++) {
                    JSONObject jsonEr = transactions.getJSONObject(i);
                    String acquirerErrorMessage = jsonEr.optString("acquirerErrorMessage");
                    if (chargeStatus.equalsIgnoreCase("NotAuthorized") &&
                            acquirerErrorMessage.toLowerCase().contains("estabelecer comunica") &&
                            acquirerErrorMessage.toLowerCase().contains("com a adquirente globalpayments")) {
                        acquirerErrorMessage = "Transação não autorizada. Entre em contato com o banco emissor do cartão.";
                    }
                    if (!UteisValidacao.emptyString(acquirerErrorMessage)) {
                        ret.append(", ").append(acquirerErrorMessage);
                    }
                }
            }
        } catch (Exception ignored) {
        }
        return ret.toString().replaceFirst(",", "");
    }

    public String getAdquirente() {
        try {
            JSONObject transaction = firstTransaction();
            return transaction != null ? transaction.optString("acquirer") : "";
        } catch (Exception ex) {
            return "";
        }
    }

    private JSONObject charge() {
        try {
            if (UteisValidacao.emptyString(getParamsResposta())) {
                return null;
            }
            JSONObject json = new JSONObject(getParamsResposta());
            return json.getJSONObject("charge");
        } catch (Exception ex) {
            return null;
        }
    }

    private JSONObject firstTransaction() {
        try {
            JSONObject charge = charge();
            JSONArray transactions = charge != null ? charge.optJSONArray("transactions") : null;
            return transactions != null ? transactions.optJSONObject(0) : null;
        } catch (Exception ex) {
            return null;
        }
    }

    public String getAutorizacao() {
        try {
            JSONObject transaction = firstTransaction();
            return transaction != null ? transaction.optString("authorizationCode") : "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getPaymentId() {
        try {
            JSONObject charge = charge();
            return charge != null ? charge.optString("id") : "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getBandeira() {
        try {
            JSONObject transaction = firstTransaction();
            JSONObject card = transaction.getJSONObject("card");
            String brand = card.optString("brand");
            if (!UteisValidacao.emptyString(brand)) {
                return brand;
            }
        } catch (Exception ignored) {
        }
        try {
            return obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira);
        } catch (Exception ignored) {
        }
        return "";
    }

    public String getCartaoMascarado() {
        try {
            return getValorCartaoMascarado();
        } catch (Exception e) {
            return "";
        }
    }

    public String getValorAtributoResposta(String nomeAtributo) throws Exception {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao)) {
            return getPaymentId();
        }
        return valor;
    }

    public String getValorAtributoCancelamento(String nomeAtributo) throws Exception {
        String valor = "";
        try {
            JSONObject json = new JSONObject(getResultadoCancelamento());
            String returnCode = json.getString("returnCode");
            if (!UteisValidacao.emptyString(returnCode)) {
                return PagoLivreRetornoEnum.valueOff(returnCode).getDescricao();
            }
        } catch (Exception ex) {
            return "";
        }
        return valor;
    }

    public String getPayment() throws Exception {
        try {
            return this.getParamsResposta();
        } catch (Exception e) {
            return "";
        }
    }

    public String getCodErroExterno() {
        return getReturnCode();
    }

    public String getReturnCode() {
        try {
            JSONObject transation = firstTransaction();
            return transation != null ? transation.optString("acquirerErrorCode") : "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getReason() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            return json.optString("reason");
            //retorna código somente se não estiver aprovada
        } catch (Exception ex) {
            return "";
        }
    }

    public String getNSU() {
        try {
            JSONObject transaction = firstTransaction();
            return transaction != null ? transaction.optString("merchantTransactionId") : "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getTID() {
        try {
            JSONObject transaction = firstTransaction();
            return transaction != null ? transaction.optString("transactionId") : "";
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer getNrVezes() {
        try {
            JSONObject transaction = firstTransaction();
            return transaction != null ? transaction.optInt("installmentNumber") : 0;
        } catch (Exception ex) {
            return 0;
        }
    }
}

package negocio.comuns.financeiro;

import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.redepay.ERedeRetornoEnum;



/*
 * Created by <PERSON><PERSON> on 18/07/2017.
 */
public class TransacaoERedeVO extends TransacaoVO {

    public String getValorCodigoExterno() throws Exception {
        return getCodigoExterno();
    }

    public String getValorCartaoMascarado() throws Exception {
        JSONObject obj = new JSONObject(getParamsEnvio());
        return APF.getCartaoMascarado(obj.getString("cardNumber"));
    }

    public String getValorUltimaTransacaoAprovada() throws Exception {
        return getValorAtributoResposta(APF.Transacao);
    }

    public String getResultadoRequisicao() throws Exception {
        return ERedeRetornoEnum.valueOff(getReturnCode()).getDescricao();
    }

    public String getAutorizacao() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            return obj.getString("authorizationCode");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getPaymentId() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            return obj.getString("tid");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getBandeira() {
        try {
            JSONObject obj = new JSONObject(getOutrasInformacoes());
            String band = obj.optString("cartaoBandeira");
            if (UteisValidacao.emptyString(band)) {
                band = obj.optString("band");
            }
            return band.toUpperCase();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getCartaoMascarado() {
        String result = "";
        try {
            result = getValorCartaoMascarado();
        } catch (Exception e) {
        }
        return result;
    }

    public String getValorAtributoResposta(String nomeAtributo) throws Exception {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao)) {
            return getPaymentId();
        }
        return valor;
    }

    public String getValorAtributoCancelamento(String nomeAtributo) throws Exception {
        String valor = "";
        try {
            JSONObject json = new JSONObject(getResultadoCancelamento());
            String returnCode = json.getString("returnCode");
            if (!UteisValidacao.emptyString(returnCode)) {
                return ERedeRetornoEnum.valueOff(returnCode).getDescricao();
            }
        } catch (Exception ex) {
            return "";
        }
        return valor;
    }

    public String getPayment() throws Exception {
        try {
            return this.getParamsResposta();
        } catch (Exception e) {
            return "";
        }
    }

    public String getCodErroExterno() {
        try {
            String codigoRetorno = getReturnCode();
            if (!ERedeRetornoEnum.Retorno00.getCodigo().equals(codigoRetorno)) { //somente se não for autorizado retorna o erro.
                return codigoRetorno;
            }
        } catch (Exception ex) {
        }
        return "0";
    }

    public String getReturnCode() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            return json.getString("returnCode");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getNSU() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            return json.getString("nsu");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getTID() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            return json.getString("tid");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getNumeroFiliacao() {
        try {
            JSONObject json = new JSONObject(getParamsEnvio());
            return json.getString("distributorAffiliation");
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer getNrVezes() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            return obj.getInt("installments");
        } catch (Exception ex) {
            return 0;
        }
    }
}

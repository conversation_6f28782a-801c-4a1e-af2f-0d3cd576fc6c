package negocio.comuns.financeiro;

/**
 * Classe responsável pelas alíquotas fiscais
 *
 * <AUTHOR>
 */
public enum AliquotasFiscaisEnum {
    _001(24, "0,01", "Alíquota de 0,01%"),
    _01(1, "01", "Alíquota de 01%"),
    _02(2, "02", "Alíquota de 02%"),
    _03(3, "03", "Alíquota de 03%"),
    _04(4, "04", "Alíquota de 04%"),
    _05(5, "05", "Alíquota de 05%"),
    _06(6, "06", "Alíquota de 06%"),
    _07(7, "07", "Alíquota de 07%"),
    _08(8, "08", "Alíquota de 08%"),
    _0836(26, "8,36", "Alíquota de 08,36%"),
    _09(9, "09", "Alíquota de 09%"),
    _10(10, "10", "Alíquota de 10%"),
    _11(11, "11", "Alíquota de 11%"),
    _12(12, "12", "Alíquota de 12%"),
    _13(13, "13", "Alíquo<PERSON> de 13%"),
    _14(14, "14", "<PERSON><PERSON>quota de 14%"),
    _1483(27, "14,83", "<PERSON><PERSON>quota de 14,83%"),
    _15(15, "15", "<PERSON><PERSON>quota de 15%"),
    _16(16, "16", "<PERSON><PERSON>quota de 16%"),
    _1694(25, "16,94", "<PERSON><PERSON>quota de 16,94%"),
    _17(23, "17", "<PERSON><PERSON>quota de 17%"),
    _II(17, "II", "Alíquota de Isenção de ICMS"),
    _FF(18, "FF", "Alíquota de Substuição Tributária"),
    _NN(19, "NN", "Alíquota de Nao incidencia de ICMS"),
    _IS(20, "IS", "Alíquota de Isenção de Serviço"),
    _SF(21, "SF", "Alíquota de Substituição de ISSQN"),
    _SN(22, "SN", "Alíquota de optantes Simples Nacional");

    private Integer codigo;
    private String descricao;
    private String identificador;

    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return descricao;
    }

    /**
     * @return the identificador
     */
    public String getIdentificador() {
        return identificador;
    }

    AliquotasFiscaisEnum(Integer codigo, String identificador, String descricao) {
        this.codigo = codigo;
        this.identificador = identificador;
        this.descricao = descricao;
    }

    public static AliquotasFiscaisEnum getAliquotaFiscal(final Integer codigo) {
        AliquotasFiscaisEnum obj = null;
        for (AliquotasFiscaisEnum status : AliquotasFiscaisEnum.values()) {
            if (status.getCodigo().equals(codigo)) {
                obj = status;
            }
        }
        return obj;
    }
}

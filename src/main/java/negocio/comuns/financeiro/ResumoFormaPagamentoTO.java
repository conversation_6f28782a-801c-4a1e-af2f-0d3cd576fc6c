package negocio.comuns.financeiro;

import java.util.List;
import negocio.comuns.arquitetura.SuperTO;

import negocio.comuns.financeiro.enumerador.TipoFormaPagto;

/**
 *
 * <AUTHOR>
 */
public class ResumoFormaPagamentoTO extends SuperTO {
    private static final long serialVersionUID = -8366750270847966376L;
    /*
     * sao 4 de cada atributo para formar uma linha na tela
     */

    private String descricao1 = "";
    private double total1 = 0.0;
    private String tipo1 = "";
    private boolean mostrar1 = false;
    private List lista1 = null;
    private String tipoLista1 = "closed";
    private FormaPagamentoVO formaPagamento1 = new FormaPagamentoVO();
    private String descricao2 = "";
    private double total2 = 0.0;
    private String tipo2 = "";
    private boolean mostrar2 = false;
    private List lista2 = null;
    private String tipoLista2 = "closed";
    private FormaPagamentoVO formaPagamento2 = new FormaPagamentoVO();
    private String descricao3 = "";
    private double total3 = 0.0;
    private String tipo3 = "";
    private boolean mostrar3 = false;
    private List lista3 = null;
    private String tipoLista3 = "closed";
    private FormaPagamentoVO formaPagamento3 = new FormaPagamentoVO();
    private String descricao4 = "";
    private double total4 = 0.0;
    private String tipo4 = "";
    private boolean mostrar4 = false;
    private List lista4 = null;
    private String tipoLista4 = "closed";
    private FormaPagamentoVO formaPagamento4 = new FormaPagamentoVO();

    public String getCor1() {
        return obterCor(tipo1);
    }

    public String getCor2() {
        return obterCor(tipo2);
    }

    public String getCor3() {
        return obterCor(tipo3);
    }

    public String getCor4() {
        return obterCor(tipo4);
    }

    public String getTitle1() {
        return obterTitle(tipo1);
    }

    public String getTitle2() {
        return obterTitle(tipo2);
    }

    public String getTitle3() {
        return obterTitle(tipo3);
    }

    public String getTitle4() {
        return obterTitle(tipo4);
    }

    private String obterTitle(String tipo) {
        return (TipoFormaPagto.BOLETOBANCARIO.getSigla().equals(tipo) || TipoFormaPagto.CREDITOCONTACORRENTE.getSigla().equals(tipo))
                ? "Não entra no caixa fisicamente" : "Pagamento em espécie";
    }

    /**
     * Responsável por
     * <AUTHOR> Alcides
     * 21/02/2013
     */
    private String obterCor(String tipo) {
        return (TipoFormaPagto.BOLETOBANCARIO.getSigla().equals(tipo) || TipoFormaPagto.CREDITOCONTACORRENTE.getSigla().equals(tipo))
                ? "#62FF62" : "#eee";
    }

    public String getDescricao1() {
        return descricao1;
    }

    public void setDescricao1(String descricao1) {
        this.descricao1 = descricao1;
    }

    public double getTotal1() {
        return total1;
    }

    public void setTotal1(double total1) {
        this.total1 = total1;
    }

    public String getTipo1() {
        return tipo1;
    }

    public void setTipo1(String tipo1) {
        this.tipo1 = tipo1;
    }

    public String getDescricao2() {
        return descricao2;
    }

    public void setDescricao2(String descricao2) {
        this.descricao2 = descricao2;
    }

    public double getTotal2() {
        return total2;
    }

    public void setTotal2(double total2) {
        this.total2 = total2;
    }

    public String getTipo2() {
        return tipo2;
    }

    public void setTipo2(String tipo2) {
        this.tipo2 = tipo2;
    }

    public String getDescricao3() {
        return descricao3;
    }

    public void setDescricao3(String descricao3) {
        this.descricao3 = descricao3;
    }

    public double getTotal3() {
        return total3;
    }

    public void setTotal3(double total3) {
        this.total3 = total3;
    }

    public String getTipo3() {
        return tipo3;
    }

    public void setTipo3(String tipo3) {
        this.tipo3 = tipo3;
    }

    public String getDescricao4() {
        return descricao4;
    }

    public void setDescricao4(String descricao4) {
        this.descricao4 = descricao4;
    }

    public double getTotal4() {
        return total4;
    }

    public void setTotal4(double total4) {
        this.total4 = total4;
    }

    public String getTipo4() {
        return tipo4;
    }

    public void setTipo4(String tipo4) {
        this.tipo4 = tipo4;
    }

    public boolean isMostrar1() {
        return mostrar1;
    }

    public void setMostrar1(boolean mostrar1) {
        this.mostrar1 = mostrar1;
    }

    public boolean isMostrar2() {
        return mostrar2;
    }

    public void setMostrar2(boolean mostrar2) {
        this.mostrar2 = mostrar2;
    }

    public boolean isMostrar3() {
        return mostrar3;
    }

    public void setMostrar3(boolean mostrar3) {
        this.mostrar3 = mostrar3;
    }

    public boolean isMostrar4() {
        return mostrar4;
    }

    public void setMostrar4(boolean mostrar4) {
        this.mostrar4 = mostrar4;
    }

    public List getLista1() {
        return lista1;
    }

    public void setLista1(List lista1) {
        this.lista1 = lista1;
    }

    public List getLista2() {
        return lista2;
    }

    public void setLista2(List lista2) {
        this.lista2 = lista2;
    }

    public List getLista3() {
        return lista3;
    }

    public void setLista3(List lista3) {
        this.lista3 = lista3;
    }

    public List getLista4() {
        return lista4;
    }

    public void setLista4(List lista4) {
        this.lista4 = lista4;
    }

    public String getTipoLista1() {
        return tipoLista1;
    }

    public void setTipoLista1(String tipoLista1) {
        this.tipoLista1 = tipoLista1;
    }

    public String getTipoLista2() {
        return tipoLista2;
    }

    public void setTipoLista2(String tipoLista2) {
        this.tipoLista2 = tipoLista2;
    }

    public String getTipoLista3() {
        return tipoLista3;
    }

    public void setTipoLista3(String tipoLista3) {
        this.tipoLista3 = tipoLista3;
    }

    public String getTipoLista4() {
        return tipoLista4;
    }

    public void setTipoLista4(String tipoLista4) {
        this.tipoLista4 = tipoLista4;
    }

    public int getQtde1() {
        return lista1.size();
    }

    public int getQtde2() {
        return lista2.size();
    }

    public int getQtde3() {
        return lista3.size();
    }

    public int getQtde4() {
        return lista4.size();
    }

    public FormaPagamentoVO getFormaPagamento1() {
        return formaPagamento1;
    }

    public void setFormaPagamento1(FormaPagamentoVO formaPagamento1) {
        this.formaPagamento1 = formaPagamento1;
    }

    public FormaPagamentoVO getFormaPagamento2() {
        return formaPagamento2;
    }

    public void setFormaPagamento2(FormaPagamentoVO formaPagamento2) {
        this.formaPagamento2 = formaPagamento2;
    }

    public FormaPagamentoVO getFormaPagamento3() {
        return formaPagamento3;
    }

    public void setFormaPagamento3(FormaPagamentoVO formaPagamento3) {
        this.formaPagamento3 = formaPagamento3;
    }

    public FormaPagamentoVO getFormaPagamento4() {
        return formaPagamento4;
    }

    public void setFormaPagamento4(FormaPagamentoVO formaPagamento4) {
        this.formaPagamento4 = formaPagamento4;
    }
}

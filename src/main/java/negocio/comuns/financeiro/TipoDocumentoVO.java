package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade de tipos de documento
 */
public class TipoDocumentoVO extends SuperVO {

	protected Integer codigo;
	protected String descricao;
	@NaoControlarLogAlteracao
	private Boolean tipoDocumentoEscolhido;

	/**
	 * Construtor padrão da classe <code>TipoDocumentoVO</code>. Cria uma nova
	 * instância desta entidade, inicializando automaticamente seus atributos
	 * (Classe VO).
	 */
	public TipoDocumentoVO() {
		super();
		inicializarDados();
	}

	/**
	 * Operação responsável por validar os dados de um objeto da classe
	 * <code>BancoVO</code>. Todos os tipos de consistência de dados são e devem
	 * ser implementadas neste método. São validações típicas: verificação de
	 * campos obrigatórios, verificação de valores válidos para os atributos.
	 * 
	 * @exception ConsistirExecption
	 *                Se uma inconsistência for encontrada aumaticamente é
	 *                gerada uma exceção descrevendo o atributo e o erro
	 *                ocorrido.
	 */
	public static void validarDados(TipoDocumentoVO obj)
			throws ConsistirException {
		if (!obj.getValidarDados().booleanValue()) {
			return;
		}
                if(UteisValidacao.emptyString(obj.getDescricao())){
                       throw new ConsistirException("Informe a descrição do tipo de documento");
                }
	}

	/**
	 * Operação reponsável por inicializar os atributos da classe.
	 */
	public void inicializarDados() {
		setCodigo(new Integer(0));
		setDescricao("");
	}

	public Integer getCodigo() {
		return codigo;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	public String getDescricao() {
		return descricao;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	/**
	 * @param tipoDocumentoEscolhido the tipoDocumentoEscolhido to set
	 */
	public void setTipoDocumentoEscolhido(Boolean tipoDocumentoEscolhido) {
		this.tipoDocumentoEscolhido = tipoDocumentoEscolhido;
	}

	/**
	 * @return the tipoDocumentoEscolhido
	 */
	public Boolean getTipoDocumentoEscolhido() {
		if(tipoDocumentoEscolhido == null){
			tipoDocumentoEscolhido = Boolean.FALSE;
		}
		return tipoDocumentoEscolhido;
	}
}
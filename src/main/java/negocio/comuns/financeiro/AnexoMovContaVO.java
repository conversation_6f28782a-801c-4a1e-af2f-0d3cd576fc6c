package negocio.comuns.financeiro;

import negocio.comuns.utilitarias.Uteis;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON> on 07/03/2017.
 */
public class AnexoMovContaVO implements Serializable{
    private Integer codigo;
    private String dataSource;
    private String fotoKey;
    private Date dia;
    private Integer movConta;
    private byte[] foto;
    private Boolean fotoAdicionada;

    public void init(){
        setFotoAdicionada(false);
        setCodigo(0);
        setDataSource("");
        setFotoKey("");
        setDia(new Date());
        setMovConta(0);
        setFoto(new byte[0]);
    }

    public Boolean getFotoAdicionada() {
        return fotoAdicionada;
    }

    public void setFotoAdicionada(Boolean fotoAdicionada) {
        this.fotoAdicionada = fotoAdicionada;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Integer getMovConta() {
        return movConta;
    }

    public void setMovConta(Integer movConta) {
        this.movConta = movConta;
    }

    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public byte[] getFoto() {
        if (foto == null) {
            foto = new byte[0];
        }
        return foto;
    }

    public void setFoto(byte[] foto) {
        this.foto = foto;
    }
}

package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.contrato.ComissaoGeralConfiguracaoVO;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 28/09/2015.
 */
public class ComissaoMetaFinananceiraVO extends SuperVO {


    private Integer codigo = 0;
    private Integer codigoMeta = 0;
    private Double valorEspontaneo = 0.0;
    private Double porcentagemEspontaneo = 0.0;
    private Double valorAgendado = 0.0;
    private Double porcentagemAgendado = 0.0;
    private ComissaoGeralConfiguracaoVO comissaoGeralConfiguracaoVO;

    public ComissaoMetaFinananceiraVO(){}

    public ComissaoMetaFinananceiraVO(Integer codigoMeta){
        this.codigoMeta = codigoMeta;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    public String getNomeMeta(){
        return this.codigoMeta + "ª Meta";
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoMeta() {
        return codigoMeta;
    }

    public void setCodigoMeta(Integer codigoMeta) {
        this.codigoMeta = codigoMeta;
    }

    public Double getValorEspontaneo() {
        return valorEspontaneo;
    }

    public void setValorEspontaneo(Double valorEspontaneo) {
        this.valorEspontaneo = valorEspontaneo;
    }

    public Double getPorcentagemEspontaneo() {
        return porcentagemEspontaneo;
    }

    public void setPorcentagemEspontaneo(Double porcentagemEspontaneo) {
        this.porcentagemEspontaneo = porcentagemEspontaneo;
    }

    public String getValorEspontaneo_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorEspontaneo);
    }

    public String getPorcentagemAgendado_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(porcentagemAgendado) + "%";
    }

    public String getPorcentagemEspontaneo_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(porcentagemEspontaneo) + "%";
    }


    public String getValorAgendado_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorAgendado);
    }


    public Double getValorAgendado() {
        return valorAgendado;
    }

    public void setValorAgendado(Double valorAgendado) {
        this.valorAgendado = valorAgendado;
    }

    public Double getPorcentagemAgendado() {
        return porcentagemAgendado;
    }

    public void setPorcentagemAgendado(Double porcentagemAgendado) {
        this.porcentagemAgendado = porcentagemAgendado;
    }

    public ComissaoGeralConfiguracaoVO getComissaoGeralConfiguracaoVO() {
        return comissaoGeralConfiguracaoVO;
    }

    public void setComissaoGeralConfiguracaoVO(ComissaoGeralConfiguracaoVO comissaoGeralConfiguracaoVO) {
        this.comissaoGeralConfiguracaoVO = comissaoGeralConfiguracaoVO;
    }

    public static List<ComissaoMetaFinananceiraVO> criarListaComissaoMeta(){
        List<ComissaoMetaFinananceiraVO> lista = new ArrayList<ComissaoMetaFinananceiraVO>();
        lista.add(new ComissaoMetaFinananceiraVO(1));
        lista.add(new ComissaoMetaFinananceiraVO(2));
        lista.add(new ComissaoMetaFinananceiraVO(3));
        lista.add(new ComissaoMetaFinananceiraVO(4));
        lista.add(new ComissaoMetaFinananceiraVO(5));
        return lista;
    }

}

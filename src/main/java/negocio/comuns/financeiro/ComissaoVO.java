/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.Lista;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import relatorio.negocio.comuns.financeiro.DetalhamentoLancamentoDF_VO;

/**
 *
 * <AUTHOR>
 */
public class ComissaoVO extends SuperVO {

    @ChaveEstrangeira
    private ClienteVO cliente;    
    @Lista
    List<DetalhamentoLancamentoDF_VO> listaDetalheLancamento;
    private Double receitaAluno = 0.0;

    public String getNome() {
        return cliente.getPessoa().getNome();
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }
    
    public Double getReceitaAluno() {
        return receitaAluno;
    }

    public void setReceitaAluno(Double receitaAluno) {
        this.receitaAluno = receitaAluno;
    }

    public List<DetalhamentoLancamentoDF_VO> getListaDetalheLancamento() {
        if(listaDetalheLancamento == null){
            listaDetalheLancamento = new ArrayList<DetalhamentoLancamentoDF_VO>();
        }
        return listaDetalheLancamento;
    }

    public void setListaDetalheLancamento(List<DetalhamentoLancamentoDF_VO> listaDetalheLancamento) {
        this.listaDetalheLancamento = listaDetalheLancamento;
    }
    
	public JRDataSource getDsPagamentos() {
        JRDataSource jr1 = new JRBeanArrayDataSource(getListaDetalheLancamento().toArray());
        return jr1;
    }
}

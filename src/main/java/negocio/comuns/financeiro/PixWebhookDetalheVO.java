package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

/**
 * Created by <PERSON> on 21/11/2023.
 */

public class PixWebhookDetalheVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer pixWebhook;
    protected String txid;
    protected Integer pix;
    protected Date dataRegistro;
    protected boolean processado;
    protected Date dataProcessamento;


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Integer getPixWebhook() {
        return pixWebhook;
    }

    public void setPixWebhook(Integer pixWebhook) {
        this.pixWebhook = pixWebhook;
    }

    public String getTxid() {
        return txid;
    }

    public void setTxid(String txid) {
        this.txid = txid;
    }

    public Integer getPix() {
        return pix;
    }

    public void setPix(Integer pix) {
        this.pix = pix;
    }

    public boolean isProcessado() {
        return processado;
    }

    public void setProcessado(boolean processado) {
        this.processado = processado;
    }

    public Date getDataProcessamento() {
        return dataProcessamento;
    }

    public void setDataProcessamento(Date dataProcessamento) {
        this.dataProcessamento = dataProcessamento;
    }

    @Override
    public String toString() {
        return "PixWebhookDetalheVO{" +
                "codigo=" + codigo +
                ", pixWebhook=" + pixWebhook +
                ", txid='" + txid + '\'' +
                ", pix=" + pix +
                ", dataRegistro=" + dataRegistro +
                ", processado=" + processado +
                ", dataProcessamento=" + dataProcessamento +
                '}';
    }
}

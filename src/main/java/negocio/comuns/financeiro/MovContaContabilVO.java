package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UtilReflection;

/**
 * Created by ulisses on 09/02/2017.
 */
public class MovContaContabilVO extends SuperVO {

    private Integer codigo;
    private MovContaVO movContaVO;
    private Integer historicoContabil;
    private String complementoHistoricoContabil;
    private ContaContabilVO contaContabilCreditoValor = new ContaContabilVO();
    private ContaContabilVO contaContabilDebitoValor= new ContaContabilVO();
    private Double valorMulta = 0.0;
    private ContaContabilVO contaContabilCreditoMulta = new ContaContabilVO();
    private ContaContabilVO contaContabilDebitoMulta = new ContaContabilVO();
    private Double valorJuro = 0.0;
    private ContaContabilVO contaContabilCreditoJuro = new ContaContabilVO();
    private ContaContabilVO contaContabilDebitoJuro = new ContaContabilVO();

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public MovContaVO getMovContaVO() {
        return movContaVO;
    }

    public void setMovContaVO(MovContaVO movContaVO) {
        this.movContaVO = movContaVO;
    }

    public Integer getHistoricoContabil() {
        if (historicoContabil == null){
            return 0;
        }
        return historicoContabil;
    }

    public void setHistoricoContabil(Integer historicoContabil) {
        this.historicoContabil = historicoContabil;
    }

    public String getComplementoHistoricoContabil() {
        if (complementoHistoricoContabil == null){
            return "";
        }
        return complementoHistoricoContabil;
    }

    public void setComplementoHistoricoContabil(String complementoHistoricoContabil) {
        this.complementoHistoricoContabil = complementoHistoricoContabil;
    }

    public ContaContabilVO getContaContabilCreditoValor() {
        return contaContabilCreditoValor;
    }

    public void setContaContabilCreditoValor(ContaContabilVO contaContabilCreditoValor) {
        this.contaContabilCreditoValor = contaContabilCreditoValor;
    }

    public ContaContabilVO getContaContabilDebitoValor() {
        return contaContabilDebitoValor;
    }

    public void setContaContabilDebitoValor(ContaContabilVO contaContabilDebitoValor) {
        this.contaContabilDebitoValor = contaContabilDebitoValor;
    }

    public Double getValorMulta() {
        return valorMulta;
    }

    public void setValorMulta(Double valorMulta) {
        this.valorMulta = valorMulta;
    }

    public ContaContabilVO getContaContabilCreditoMulta() {
        return contaContabilCreditoMulta;
    }

    public void setContaContabilCreditoMulta(ContaContabilVO contaContabilCreditoMulta) {
        this.contaContabilCreditoMulta = contaContabilCreditoMulta;
    }

    public ContaContabilVO getContaContabilDebitoMulta() {
        return contaContabilDebitoMulta;
    }

    public void setContaContabilDebitoMulta(ContaContabilVO contaContabilDebitoMulta) {
        this.contaContabilDebitoMulta = contaContabilDebitoMulta;
    }


    public ContaContabilVO getContaContabilCreditoJuro() {
        return contaContabilCreditoJuro;
    }

    public void setContaContabilCreditoJuro(ContaContabilVO contaContabilCreditoJuro) {
        this.contaContabilCreditoJuro = contaContabilCreditoJuro;
    }

    public ContaContabilVO getContaContabilDebitoJuro() {
        return contaContabilDebitoJuro;
    }

    public void setContaContabilDebitoJuro(ContaContabilVO contaContabilDebitoJuro) {
        this.contaContabilDebitoJuro = contaContabilDebitoJuro;
    }

    public Double getValorJuro() {
        return valorJuro;
    }

    public void setValorJuro(Double valorJuro) {
        this.valorJuro = valorJuro;
    }

    public static void validarDados(MovContaContabilVO obj)throws Exception{
        if (!obj.validarDados){
            return;
        }
        if ((UtilReflection.objetoMaiorQueZero(obj, "getContaContabilCreditoValor().getCodigo()")) &&
           (UtilReflection.objetoMaiorQueZero(obj, "getContaContabilDebitoValor().getCodigo()"))){
            if (obj.getContaContabilCreditoValor().getCodigo().equals(obj.getContaContabilDebitoValor().getCodigo())){
                throw new ConsistirException("A Conta Devedora e Credora definida para o valor do lançamento não pode ser a mesma.");
            }
        }
    }

}

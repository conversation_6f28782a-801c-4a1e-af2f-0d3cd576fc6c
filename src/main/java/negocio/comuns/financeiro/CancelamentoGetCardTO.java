package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import org.json.JSONObject;

public class CancelamentoGetCardTO extends SuperVO {

    private String dataRegistro;
    private Double valor;
    private String parcelas;
    private Integer pessoa;
    private Integer idTransacao;
    private String convenio;
    private String usuario;
    private String dadosPedido;
    private String paramsrespcancel;
    private String paramsenvio;
    private String dadosCancelLog;


    public CancelamentoGetCardTO() {
    }

    public CancelamentoGetCardTO(String dataRegistro, String convenio, Double valor, String dadosPedido, Integer pessoa, String paramsrespcancel, String dadosCancelLog, String paramsEnvio) {
        this.dataRegistro = dataRegistro;
        this.convenio = convenio;
        this.valor = valor;
        this.pessoa = pessoa;
        JSONObject jsonDadosPedido = new JSONObject(dadosPedido);
        JSONObject jsonDadosCancelLog = new JSONObject(dadosCancelLog);
        try {
            String descricoesConcatenadas = "";
            for (int i = 0; i < jsonDadosPedido.getJSONArray("parcelas").length(); i++) {
                descricoesConcatenadas += jsonDadosPedido.getJSONArray("parcelas").getInt(i) + ", ";
            }
            if (!descricoesConcatenadas.isEmpty()) {
                descricoesConcatenadas = descricoesConcatenadas.substring(0, descricoesConcatenadas.length() - 2);
            }
            this.parcelas = descricoesConcatenadas;
        }catch (Exception ex){
            this.parcelas = "";
        }
        try {
            this.idTransacao = new JSONObject(jsonDadosCancelLog.getString("retorno")).getInt("idTransacao");
        }catch (Exception ignore){
            this.idTransacao = 0;
        }
        try{
            this.usuario = jsonDadosCancelLog.getString("usuario");
        }catch (Exception ignore){
            this.usuario = "";
        }
        this.paramsenvio = paramsEnvio;
        this.paramsrespcancel = paramsrespcancel;
        this.dadosPedido = dadosPedido;
        this.dadosCancelLog = dadosCancelLog;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getParcelas() {
        return parcelas;
    }

    public void setParcelas(String parcelas) {
        this.parcelas = parcelas;
    }

    public Integer getIdTransacao() {
        return idTransacao;
    }

    public void setIdTransacao(Integer idTransacao) {
        this.idTransacao = idTransacao;
    }

    public String getConvenio() {
        return convenio;
    }

    public void setConvenio(String convenio) {
        this.convenio = convenio;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getParamsrespcancel() {
        return paramsrespcancel;
    }

    public void setParamsrespcancel(String paramsrespcancel) {
        this.paramsrespcancel = paramsrespcancel;
    }

    public String getDadosCancelLog() {
        return dadosCancelLog;
    }

    public void setDadosCancelLog(String dadosCancelLog) {
        this.dadosCancelLog = dadosCancelLog;
    }

    public String getDadosPedido() {
        return dadosPedido;
    }

    public void setDadosPedido(String dadosPedido) {
        this.dadosPedido = dadosPedido;
    }

    public String getParamsenvio() {
        return paramsenvio;
    }

    public void setParamsenvio(String paramsenvio) {
        this.paramsenvio = paramsenvio;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }
}

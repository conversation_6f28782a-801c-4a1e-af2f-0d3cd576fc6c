package negocio.comuns.financeiro;

import org.json.JSONObject;

/**
 * Created by <PERSON> on 29/09/2023.
 */

public class PluggyTransactionPaymentDataPayerDTO {

    protected String accountNumber;
    protected String branchNumber;
    protected PluggyTransactionPaymentDataDocumentNumberDTO documentNumber;
    protected String name;

    public PluggyTransactionPaymentDataPayerDTO() {
    }

    public PluggyTransactionPaymentDataPayerDTO(JSONObject json) throws Exception {
        this.accountNumber = json.optString("accountNumber", "");
        this.branchNumber = json.optString("branchNumber", "");
        if (json.optJSONObject("documentNumber") != null) {
            this.documentNumber = new PluggyTransactionPaymentDataDocumentNumberDTO(json.optJSONObject("documentNumber"));
        }
        this.name = json.optString("name", "");
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getBranchNumber() {
        return branchNumber;
    }

    public void setBranchNumber(String branchNumber) {
        this.branchNumber = branchNumber;
    }

    public PluggyTransactionPaymentDataDocumentNumberDTO getDocumentNumber() {
        return documentNumber;
    }

    public void setDocumentNumber(PluggyTransactionPaymentDataDocumentNumberDTO documentNumber) {
        this.documentNumber = documentNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

package negocio.comuns.financeiro;

import java.math.BigDecimal;

class ItemFaturamentoTOCreditoAVista extends ItemFaturamentoTO {
    private final BigDecimal taxa = new BigDecimal("1.6");

    @Override
    public BigDecimal getTaxaSTONE() {
        return taxa;
    }

    @Override
    public String getFormaPagamento() {
        return "CRÉDITO À VISTA";
    }

    @Override
    public int getOrdem() {
        return 1;
    }
}

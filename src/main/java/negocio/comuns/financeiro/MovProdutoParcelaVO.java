package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * Reponsável por manter os dados da entidade MovProdutoParcela. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class MovProdutoParcelaVO extends SuperVO {

    protected Integer codigo;
    protected Integer movProduto;
    @NaoControlarLogAlteracao
    private MovProdutoVO movProdutoVO;
    private MovParcelaVO movParcelaVO;
    protected Double valorPago;
    /** Atributo responsável por manter o objeto relacionado da classe <code>MovParcela </code>.*/
    protected Integer movParcela;
    protected ReciboPagamentoVO reciboPagamento;

    private MovParcelaVO movParcelaOriginalMultaJuros;

    /**
     * Construtor padrão da classe <code>MovProdutoParcela</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public MovProdutoParcelaVO() {
        super();
        inicializarDados();
    }

    public MovProdutoParcelaVO(MovProdutoVO movProduto, MovParcelaVO movParcela) {
        this.movProdutoVO = movProduto;
        this.movParcelaVO = movParcela;
    }

    public MovProdutoParcelaVO(Integer codigoMovProduto, Integer codigoMovParcela) {
        this.movProduto = codigoMovProduto;
        this.movProdutoVO = new MovProdutoVO(codigoMovProduto);

        this.movParcela = codigoMovParcela;
        this.movParcelaVO = new MovParcelaVO(codigoMovParcela);
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>MovProdutoParcelaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(MovProdutoParcelaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setValorPago(0.0);
        setMovParcela(0);
        setReciboPagamento(new ReciboPagamentoVO());
    }

    public Integer getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(Integer movParcela) {
        this.movParcela = movParcela;
    }

    public Double getValorPago() {
        return (valorPago);
    }

    public void setValorPago(Double valorPago) {
        this.valorPago = valorPago;
    }

    public Integer getMovProduto() {
        return (movProduto);
    }

    public void setMovProduto(Integer movProduto) {
        this.movProduto = movProduto;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ReciboPagamentoVO getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(ReciboPagamentoVO reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public MovProdutoVO getMovProdutoVO() {
        return movProdutoVO;
    }

    public void setMovProdutoVO(MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    public MovParcelaVO getMovParcelaOriginalMultaJuros() {
        if (movParcelaOriginalMultaJuros == null) {
            movParcelaOriginalMultaJuros = new MovParcelaVO();
        }
        return movParcelaOriginalMultaJuros;
    }

    public void setMovParcelaOriginalMultaJuros(MovParcelaVO movParcelaOriginalMultaJuros) {
        this.movParcelaOriginalMultaJuros = movParcelaOriginalMultaJuros;
    }

    public MovParcelaVO getMovParcelaVO() {
        return movParcelaVO;
    }

    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }
}
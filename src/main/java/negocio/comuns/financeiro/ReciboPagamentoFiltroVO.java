/**
 *
 */
package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;

/**
 * <AUTHOR>
 *
 */
public class ReciboPagamentoFiltroVO extends SuperVO {
    //atributo usado quando o usuario digitar nome de pessoa
    private PessoaVO pessoaVO;
    //atributo usado quando o usuario acessar o sistema e tiver uma empresa logada
    private EmpresaVO empresaVO;
    //atributo usado quando o usuario digitar um codigo de recibo
    private ReciboPagamentoVO reciboPagamentoVO;
    private boolean controlarAcesso;
    private int nivelMontarDados;
    

    /**
     * @return O campo pessoaVO.
     */
    public PessoaVO getPessoaVO() {
        return this.pessoaVO;
    }

    /**
     * @param pessoaVO O novo valor de pessoaVO.
     */
    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    /**
     * @return O campo empresaVO.
     */
    public EmpresaVO getEmpresaVO() {
        return this.empresaVO;
    }

    /**
     * @param empresaVO O novo valor de empresaVO.
     */
    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    /**
     * @return O campo nivelMontarDados.
     */
    public int getNivelMontarDados() {
        return this.nivelMontarDados;
    }

    /**
     * @param nivelMontarDados O novo valor de nivelMontarDados.
     */
    public void setNivelMontarDados(int nivelMontarDados) {
        this.nivelMontarDados = nivelMontarDados;
    }

    /**
     * @return O campo controlarAcesso.
     */
    public boolean isControlarAcesso() {
        return this.controlarAcesso;
    }

    /**
     * @param controlarAcesso O novo valor de controlarAcesso.
     */
    public void setControlarAcesso(boolean controlarAcesso) {
        this.controlarAcesso = controlarAcesso;
    }

    public ReciboPagamentoVO getReciboPagamentoVO() {
        return reciboPagamentoVO;
    }

    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamento) {
        this.reciboPagamentoVO = reciboPagamento;
    }
}

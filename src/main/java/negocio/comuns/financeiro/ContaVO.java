package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.BancoOpenBankEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import java.util.ArrayList;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade de contas
 */
public class ContaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @ChaveEstrangeira
    @FKJson
    protected EmpresaVO empresa;
    @ChavePrimaria
    @FKJson
    protected TipoContaVO tipoConta;
    protected String descricao;
    protected String descricaoCurta;
    @ChaveEstrangeira
    @FKJson
    protected BancoVO banco;
    protected String agencia;
    protected String bancoNome;
    protected String agenciaDV;
    protected String numero;
    protected String numeroDV;
    protected boolean ativa = true;
    protected boolean mostrarBi = false;
    protected String observacao;
    @NaoControlarLogAlteracao
    private Boolean contaEscolhida = Boolean.FALSE;
    private String TipoDeConta;
    private Double saldoAtual = 0.0;
    private double saldoInicial =0;
    @NaoControlarLogAlteracao
    private Double entrada = 0.0;
    @NaoControlarLogAlteracao
    private Double saida = 0.0;
    @NaoControlarLogAlteracao
    private List<ItemRelatorioFechamentoCaixaTO> listaMovimentacoes = new ArrayList<ItemRelatorioFechamentoCaixaTO>();
    @NaoControlarLogAlteracao
    private Double valorRetiradaAutomatica = 0.0;
    @NaoControlarLogAlteracao
    private List<ChequeVO> chequesRetirar;
    @NaoControlarLogAlteracao
    private List<CartaoCreditoVO> cartaoRetirar;
    @NaoControlarLogAlteracao
    public boolean entraSaldoInicial = false;
    public boolean mostrarDREDemonstrativo = true;
    private BancoOpenBankEnum bancoOpenBankEnum;

    public String getSaldoAtualFormatado() {
        return Formatador.formatarValorNumerico(this.saldoAtual, Formatador.FRMT_NUM_PADRAO);
    }

    public String getValorRetirarFormatado() {
        return Formatador.formatarValorNumerico(this.valorRetiradaAutomatica, Formatador.FRMT_NUM_PADRAO);
    }
    public String getSaldoAtualApresentar() {
        return (br.com.pacto.priv.utils.Uteis.arredondarForcando2CasasDecimaisMantendoSinal(saldoAtual) < 0.0 ? " - " : "") + (Formatador.formatarValorMonetarioSemMoeda(this.saldoAtual));
    }

    public String getInicialApresentar() {
        return (br.com.pacto.priv.utils.Uteis.arredondarForcando2CasasDecimaisMantendoSinal(saldoInicial) < 0.0 ? " - " : "") + (Formatador.formatarValorMonetarioSemMoeda(this.saldoInicial));
    }

    public String getSaidaApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(this.saida);
    }

    public String getEntradaApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(this.entrada);
    }

    /**
     * Construtor padrão da classe <code>TipoContaVO</code>. Cria uma nova
     * instância desta entidade, inicializando automaticamente seus atributos
     * (Classe VO).
     */
    public ContaVO() {
        super();
        inicializarDados();
    }

    public ContaVO(Integer codigo, String descricao){
        this.setCodigo(codigo);
        this.setDescricao(descricao);
    }


    public ContaVO(Integer codigo) {
        super();
        inicializarDados();
        this.setCodigo(codigo);
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>BancoVO</code>. Todos os tipos de consistência de dados são e devem
     * ser implementadas neste método. São validações típicas: verificação de
     * campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @exception ConsistirExecption
     *                Se uma inconsistência for encontrada aumaticamente é
     *                gerada uma exceção descrevendo o atributo e o erro
     *                ocorrido.
     */
    public static void validarDados(ContaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getEmpresa().getCodigo().intValue()==0) {
            throw new ConsistirException("Informe a empresa");
        }
        if (obj.getTipoConta() == null || UteisValidacao.emptyNumber(obj.getTipoConta().getCodigo())) {
            throw new ConsistirException("Informe o tipo de conta");
        }
        if (UteisValidacao.emptyString(obj.getDescricao())) {
            throw new ConsistirException("Informe a descrição da conta");
        }

    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setTipoConta(new TipoContaVO());
        setBanco(new BancoVO());
    }

    //GETTERS AND SETTERS
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public TipoContaVO getTipoConta() {
        return tipoConta;
    }
    public String getTipoConta_Apresenta(){
        return tipoConta.getDescricao();
    }
    public String getNomeBanco_Apresentar(){
        return banco.getNome();
    }
    public void setTipoConta(TipoContaVO tipoConta) {
        this.tipoConta = tipoConta;
    }

    public String getDescricao() {
        if(descricao == null)
            descricao = "";
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public BancoVO getBanco() {
        return banco;
    }

    public void setBanco(BancoVO banco) {
        this.banco = banco;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getAgenciaDV() {
        return agenciaDV;
    }

    public void setAgenciaDV(String agenciaDV) {
        this.agenciaDV = agenciaDV;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getNumeroDV() {
        return numeroDV;
    }

    public void setNumeroDV(String numeroDV) {
        this.numeroDV = numeroDV;
    }

    public boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(boolean desativada) {
        this.ativa = desativada;
    }

    public boolean getMostrarBi() {
        return mostrarBi;
    }

    public void setMostrarBi(boolean mostrarBi) {
        this.mostrarBi = mostrarBi;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    /**
     * @param contaEscolhida the contaEscolhida to set
     */
    public void setContaEscolhida(Boolean contaEscolhida) {
        this.contaEscolhida = contaEscolhida;
    }

    /**
     * @return the contaEscolhida
     */
    public Boolean getContaEscolhida() {
        return contaEscolhida;
    }

    public void setSaldoAtual(Double saldoAtual) {
        this.saldoAtual = saldoAtual;
    }

    public Double getSaldoAtual() {
        return saldoAtual;
    }

    public String getColorSaldo(){
        if(br.com.pacto.priv.utils.Uteis.arredondarForcando2CasasDecimaisMantendoSinal(saldoAtual) < 0.0){
            return "#da2128"; //vermelho
        }else{
            return "#008000"; //verde
        }
    }
    public void setSaldoInicial(double saldoInicial) {
        this.saldoInicial = saldoInicial;
    }
    public double getSaldoInicial() {
        return saldoInicial;
    }
    public void setEntrada(Double entrada) {
        this.entrada = entrada;
    }
    public Double getEntrada() {
        return entrada;
    }
    public void setSaida(Double saida) {
        this.saida = saida;
    }
    public Double getSaida() {
        return saida;
    }
    public void setListaMovimentacoes(List<ItemRelatorioFechamentoCaixaTO> listaMovimentacoes) {
        this.listaMovimentacoes = listaMovimentacoes;
    }
    public List<ItemRelatorioFechamentoCaixaTO> getListaMovimentacoes() {
        return listaMovimentacoes;
    }

    public JRDataSource getListaMovimentacoesJR(){
        JRDataSource jr1 = new JRBeanCollectionDataSource(getListaMovimentacoes());
        return jr1;
    }

    public boolean getTemMovimentacoes(){
        return !getListaMovimentacoes().isEmpty();
    }

    public Double getValorRetiradaAutomatica() {
        return valorRetiradaAutomatica;
    }

    public void setValorRetiradaAutomatica(Double valorRetiradaAutomatica) {
        this.valorRetiradaAutomatica = valorRetiradaAutomatica;
    }

    public List<CartaoCreditoVO> getCartaoRetirar() {
        return cartaoRetirar;
    }

    public void setCartaoRetirar(List<CartaoCreditoVO> cartaoRetirar) {
        this.cartaoRetirar = cartaoRetirar;
    }

    public List<ChequeVO> getChequesRetirar() {
        return chequesRetirar;
    }

    public void setChequesRetirar(List<ChequeVO> chequesRetirar) {
        this.chequesRetirar = chequesRetirar;
    }

    public boolean getMostrarCheques(){
        return chequesRetirar != null && !chequesRetirar.isEmpty();
    }

    public boolean getMostrarCartoes(){
        return cartaoRetirar != null && !cartaoRetirar.isEmpty();
    }

    public String getDescricaoCurta() {
        return descricaoCurta;
    }

    public void setDescricaoCurta(String descricaoCurta) {
        this.descricaoCurta = descricaoCurta;
    }

    public boolean isAtiva() {
        return ativa;
    }

    public boolean isMostrarBi() {
        return mostrarBi;
    }

    public boolean isEntraSaldoInicial() {
        return entraSaldoInicial;
    }

    public void setEntraSaldoInicial(boolean entraSaldoInicial) {
        this.entraSaldoInicial = entraSaldoInicial;
    }

    public void formatarObservacao(){
        observacao = observacao.replace("</p>", "");
        observacao = observacao.replace("<p>", "");
    }

    public String getTipoDeConta() {
        return TipoDeConta;
    }

    public void setTipoDeConta(String tipoDeConta) {
        TipoDeConta = tipoDeConta;
    }

    public boolean isMostrarDREDemonstrativo() {
        return mostrarDREDemonstrativo;
    }

    public void setMostrarDREDemonstrativo(boolean mostrarDREDemonstrativo) {
        this.mostrarDREDemonstrativo = mostrarDREDemonstrativo;
    }

    public BancoOpenBankEnum getBancoOpenBankEnum() {
        return bancoOpenBankEnum;
    }

    public void setBancoOpenBankEnum(BancoOpenBankEnum bancoOpenBankEnum) {
        this.bancoOpenBankEnum = bancoOpenBankEnum;
    }

    public boolean isContaIntegracaoOpenBank(){
        if(this.getBancoOpenBankEnum() != null && this.getBancoOpenBankEnum().equals(BancoOpenBankEnum.STONE_OPENBANK)){
            return true;
        }
        return false;
    }
}

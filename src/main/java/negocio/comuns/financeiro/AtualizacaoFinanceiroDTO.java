package negocio.comuns.financeiro;

import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

import java.util.Date;

public class AtualizacaoFinanceiroDTO {
    private String chaveZw;
    private Integer codigoZw;
    private String nomeDono;
    private String responsavelGeral;
    private String responsavelTelefone;
    private String responsavelEmail;
    private Integer metragem;
    private String nicho;
    private Date dataAtualizacaoFinanceiro;

    public AtualizacaoFinanceiroDTO() {
    }

    public AtualizacaoFinanceiroDTO(String dados) {
        JSONObject json = new JSONObject(dados);
        this.chaveZw = json.optString("chaveZw");
        this.codigoZw = json.optInt("codigoZw");
        this.nomeDono = json.optString("nomeDono");
        this.responsavelGeral = json.optString("responsavelGeral");
        this.responsavelTelefone = json.optString("responsavelTelefone");
        this.responsavelEmail = json.optString("responsavelEmail");
        this.metragem = json.optInt("metragem");
        this.nicho = json.optString("nicho");
        try {
            this.dataAtualizacaoFinanceiro = Uteis.getDate(json.optString("dataAtualizacaoFinanceiro"), "yyyy-MM-dd");
        } catch (Exception e) {
        }
    }

    public String getChaveZw() {
        return chaveZw;
    }

    public void setChaveZw(String chaveZw) {
        this.chaveZw = chaveZw;
    }

    public Integer getCodigoZw() {
        return codigoZw;
    }

    public void setCodigoZw(Integer codigoZw) {
        this.codigoZw = codigoZw;
    }

    public String getNomeDono() {
        if (nomeDono == null) {
            return "";
        }
        return nomeDono;
    }

    public void setNomeDono(String nomeDono) {
        this.nomeDono = nomeDono;
    }

    public String getResponsavelGeral() {
        if (responsavelGeral == null) {
            return "";
        }
        return responsavelGeral;
    }

    public void setResponsavelGeral(String responsavelGeral) {
        this.responsavelGeral = responsavelGeral;
    }

    public String getResponsavelTelefone() {
        if (responsavelTelefone == null) {
            return "";
        }
        return responsavelTelefone;
    }

    public void setResponsavelTelefone(String responsavelTelefone) {
        this.responsavelTelefone = responsavelTelefone;
    }

    public String getResponsavelEmail() {
        if (responsavelEmail == null) {
            return "";
        }
        return responsavelEmail;
    }

    public void setResponsavelEmail(String responsavelEmail) {
        this.responsavelEmail = responsavelEmail;
    }

    public Integer getMetragem() {
        return metragem;
    }

    public void setMetragem(Integer metragem) {
        this.metragem = metragem;
    }

    public boolean isMetragemValid() {
        return metragem >= 30 && metragem <= 6000;
    }

    public String getNicho() {
        return nicho;
    }

    public void setNicho(String nicho) {
        this.nicho = nicho;
    }

    public Date getDataAtualizacaoFinanceiro() {
        return dataAtualizacaoFinanceiro;
    }

    public void setDataAtualizacaoFinanceiro(Date dataAtualizacaoFinanceiro) {
        this.dataAtualizacaoFinanceiro = dataAtualizacaoFinanceiro;
    }

    public JSONObject toJson() {
        JSONObject dadosJSON = new JSONObject();
        dadosJSON.put("chaveZw", chaveZw);
        dadosJSON.put("codigoZw", codigoZw);
        dadosJSON.put("nomeDono", nomeDono);
        dadosJSON.put("responsavelGeral", responsavelGeral);
        dadosJSON.put("responsavelTelefone", responsavelTelefone);
        dadosJSON.put("responsavelEmail", responsavelEmail);
        dadosJSON.put("metragem", metragem);
        dadosJSON.put("nicho", nicho);

        return dadosJSON;
    }
}

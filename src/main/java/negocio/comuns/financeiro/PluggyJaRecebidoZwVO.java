package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 * Created by <PERSON> on 15/01/2024.
 */

public class PluggyJaRecebidoZwVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected MovContaTransactionPluggyVO movContaTransactionPluggyVO;
    protected double valor;
    protected Date dataVencimento;
    protected Date dataOperacao;


    public PluggyJaRecebidoZwVO() {
        super();
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public MovContaTransactionPluggyVO getMovContaTransactionPluggyVO() {
        return movContaTransactionPluggyVO;
    }

    public void setMovContaTransactionPluggyVO(MovContaTransactionPluggyVO movContaTransactionPluggyVO) {
        this.movContaTransactionPluggyVO = movContaTransactionPluggyVO;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getDataVencimento_Apresentar() {
        if (dataVencimento == null) {
            return "";
        }
        return Uteis.getData(getDataVencimento());
    }

    public String getDataOperacao_Apresentar() {
        if (dataOperacao == null) {
            return "";
        }
        return Uteis.getData(getDataOperacao());
    }

    public String getValorApresentar() {
        if (UteisValidacao.emptyNumber(getValor())) {
            return "";
        }
        return Uteis.formataDuasCasasDecimaisEPontoCasasMilenio(getValor());
    }
}

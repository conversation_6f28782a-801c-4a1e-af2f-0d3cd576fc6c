package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by GlaucoT on 19/11/2015
 */
public class MovParcelaDetalhamentoVO extends SuperVO {

    private MovParcelaVO movParcelaVO;
    private JSONArray chequesDevolvidos;
    private JSONArray notasEmitidasAnteriormente;

    public MovParcelaVO getMovParcelaVO() {
        if (movParcelaVO == null) {
            movParcelaVO = new MovParcelaVO();
        }
        return movParcelaVO;
    }

    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }

    public JSONArray getChequesDevolvidos() {
        return chequesDevolvidos;
    }

    public void setChequesDevolvidos(JSONArray chequesDevolvidos) {
        this.chequesDevolvidos = chequesDevolvidos;
    }

    public List<ChequeTO> getChequesTO() {
        List<ChequeTO> cheques = null;
        try {
            cheques = JSONMapper.getList(getChequesDevolvidos(), ChequeTO.class);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
        }
        return cheques;
    }

    public JRDataSource getCheques() {
        return new JRBeanCollectionDataSource(getChequesTO());
    }

    public JSONArray getNotasEmitidasAnteriormente() {
        return notasEmitidasAnteriormente;
    }

    public void setNotasEmitidasAnteriormente(JSONArray notasEmitidasAnteriormente) {
        this.notasEmitidasAnteriormente = notasEmitidasAnteriormente;
    }

    public List<NFSeEmitidaVO> getNotasEmitidas() {
        List<NFSeEmitidaVO> notas = new ArrayList<NFSeEmitidaVO>();
        try {
            if (getNotasEmitidasAnteriormente() == null) {
                return notas;
            }
            for (int i = 0; i < getNotasEmitidasAnteriormente().length(); i++) {
                JSONObject object = getNotasEmitidasAnteriormente().getJSONObject(i);
                NFSeEmitidaVO nfSeEmitidaVO = new NFSeEmitidaVO(object);
                notas.add(nfSeEmitidaVO);
            }
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
        }
        return notas;
    }
}

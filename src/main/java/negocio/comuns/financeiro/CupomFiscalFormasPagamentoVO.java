package negocio.comuns.financeiro;


import negocio.comuns.arquitetura.SuperVO;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;

/**
 * Classe de modelo para a tabela cupomfiscalitens
 * 
 * <AUTHOR>
 */
public class CupomFiscalFormasPagamentoVO extends SuperVO {

	@ChavePrimaria
    protected Integer codigo;
	protected Double valor;
	
	@ChaveEstrangeira
    protected CupomFiscalVO cupom;
    @ChaveEstrangeira
    protected FormaPagamentoVO forma;
    
    /**
     * Construtor padrão da classe <code>CupomFiscalItensVO</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente
     * seus atributos (Classe VO).
     */
    public CupomFiscalFormasPagamentoVO() {
        super();
    }
    
    //////////
    //Getters and Setters
    //////////
	public Integer getCodigo() {
		return codigo;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	public Double getValor() {
		return valor;
	}

	public void setValor(Double valor) {
		//TODO codigo para ambiente de desv
		this.valor = valor;
	}

	public CupomFiscalVO getCupom() {
		return cupom;
	}

	public void setCupom(CupomFiscalVO cupom) {
		this.cupom = cupom;
	}

	public FormaPagamentoVO getForma() {
		return forma;
	}

	public void setForma(FormaPagamentoVO forma) {
		this.forma = forma;
	}

}

package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

public class ContaBancariaFornecedorVO extends SuperVO {

    private Integer codigo;
    private String agency_number;
    private String agency_digit;
    private String account_number;
    private String account_digit;
    private PessoaVO pessoaVO;
    private BancoVO bancoVO;
    private String cpfOuCnpj;

    public ContaBancariaFornecedorVO(JSONObject json) {
        this.agency_number = json.optString("agency_number");
        this.agency_digit = json.optString("agency_digit");
        this.account_number = json.optString("account_number");
        this.account_digit = json.optString("account_digit");
        this.cpfOuCnpj = json.optString("document_number");
    }

    public ContaBancariaFornecedorVO() {
    }

    public String getAgency_number() {
        return agency_number;
    }

    public void setAgency_number(String agency_number) {
        this.agency_number = agency_number;
    }

    public String getAgency_digit() {
        return agency_digit;
    }

    public void setAgency_digit(String agency_digit) {
        this.agency_digit = agency_digit;
    }

    public String getAccount_number() {
        return account_number;
    }

    public void setAccount_number(String account_number) {
        this.account_number = account_number;
    }

    public String getAccount_digit() {
        return account_digit;
    }

    public void setAccount_digit(String account_digit) {
        this.account_digit = account_digit;
    }

    public BancoVO getBancoVO() {
        if (bancoVO == null) {
            bancoVO = new BancoVO();
        }
        return bancoVO;
    }

    public void setBancoVO(BancoVO bancoVO) {
        this.bancoVO = bancoVO;
    }

    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public static void validarDados(ContaBancariaFornecedorVO obj) throws ConsistirException {
        if (UteisValidacao.emptyString(obj.getCpfOuCnpj())) {
            throw new ConsistirException("O CPF ou CNPJ da conta bancária do fornecedor deve ser informado");
        } else {
            if (obj.getCpfOuCnpj().length() != 11 && obj.getCpfOuCnpj().length() != 14) {
                throw new ConsistirException("O CPF ou CNPJ informado é inválido");
            }
            if (obj.getCpfOuCnpj().length() == 11) {
                if (!UteisValidacao.isValidCPF(obj.getCpfOuCnpj().trim())) {
                    throw new ConsistirException("O CPF informado é inválido");
                }
            }
            if (obj.getCpfOuCnpj().length() == 14) {
                if (!UteisValidacao.validaCNPJ(obj.getCpfOuCnpj().trim())) {
                    throw new ConsistirException("O CNPJ informado é inválido");
                }
            }
        }
        if (obj.getBancoVO() == null || UteisValidacao.emptyNumber(obj.getBancoVO().getCodigo())) {
            throw new ConsistirException("O banco do fornecedor deve ser informado");
        }
        if (UteisValidacao.emptyString(obj.getAgency_number())) {
            throw new ConsistirException("O Número da Agência deve ser informado");
        }
        if (UteisValidacao.emptyString(obj.getAgency_digit())) {
            throw new ConsistirException("O Dígito da Agência deve ser informado");
        }
        if (UteisValidacao.emptyString(obj.getAccount_number())) {
            throw new ConsistirException("O Número da Conta deve ser informado");
        }
        if (UteisValidacao.emptyString(obj.getAccount_digit())) {
            throw new ConsistirException("O Dígito da Conta deve ser informado");
        }
        if (obj.getPessoaVO() == null || UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
            throw new ConsistirException("A pessoa fornecedor da Conta Bancária deve ser informado");
        }
    }

    public String getCpfOuCnpj() {
        return cpfOuCnpj;
    }

    public void setCpfOuCnpj(String cpfOuCnpj) {
        this.cpfOuCnpj = cpfOuCnpj;
    }

    public String getDadosBancariosApresentar() {
        return "Banco: " + getBancoVO().getNome() + " | Agência: " + getAgency_number() + "-" + getAgency_digit() +
                " | Conta: " + getAccount_number() + "-" + getAgency_digit() + " | CPF/CNPJ: " + getCpfOuCnpj();
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}

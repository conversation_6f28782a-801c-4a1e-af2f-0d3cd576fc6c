/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import java.util.Date;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class HistoricoCartaoTO extends SuperTO{
    
    private Date dataInicio;
    private Date dataFim;
    private String descricao;
    private String conta;
    private Integer movConta;
    private Integer lote;
    private boolean credito = true;
    
    private Date dataCompensacao;
    private Date dataCompensacaoOriginal;

    public Integer getLote() {
        return lote;
    }

    public void setLote(Integer lote) {
        this.lote = lote;
    }
    
    public String getDataInicioApresentar() {
        return Uteis.getDataAplicandoFormatacao(dataInicio, "dd/MM/yy");
    }
    
    public String getDataFimApresentar() {
        return Uteis.getDataAplicandoFormatacao(dataFim, "dd/MM/yy");
    }
    
    public String getDataCompensacaoApresentar() {
        return Uteis.getDataAplicandoFormatacao(dataCompensacao, "dd/MM/yy");
    }
    
    public String getDataOriginalApresentar() {
        return Uteis.getDataAplicandoFormatacao(dataCompensacaoOriginal, "dd/MM/yy HH:mm");
    }
    public HistoricoCartaoTO(){
        
    }
    public HistoricoCartaoTO(Date dataInicio,Date dataFim, String descricao, String conta, Integer movconta, Integer lote) {
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.descricao = descricao;
        this.conta = conta;
        this.movConta = movconta;
        this.lote = lote;
    }
    
    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getConta() {
        return conta;
    }

    public void setConta(String conta) {
        this.conta = conta;
    }

    public Integer getMovConta() {
        return movConta;
    }

    public void setMovConta(Integer movConta) {
        this.movConta = movConta;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public Date getDataCompensacaoOriginal() {
        return dataCompensacaoOriginal;
    }

    public void setDataCompensacaoOriginal(Date dataCompensacaoOriginal) {
        this.dataCompensacaoOriginal = dataCompensacaoOriginal;
    }

    public boolean isCredito() {
        return credito;
    }

    public void setCredito(boolean credito) {
        this.credito = credito;
    }
    
}

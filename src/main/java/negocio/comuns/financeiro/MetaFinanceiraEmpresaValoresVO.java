
package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class MetaFinanceiraEmpresaValoresVO extends SuperVO {
    @ChavePrimaria
    private Integer codigo = 0;
    private int metaFinanceiraEmpresa = 0;
    private double valor = 0.0;
    private String cor = "";
    private String observacao = "";

    private int numeroMeta;

    public MetaFinanceiraEmpresaValoresVO(String cor){
        this.cor = cor;
    }
    public MetaFinanceiraEmpresaValoresVO(){
        this.cor = cor;
    }
    public String getValor_Apresentar(){
    	return Formatador.formatarValorMonetarioSemMoeda(this.valor);
	}
    
    public void validarDados() throws Exception {
        if(!validarDados)
            return;
        if(valor < 0.0)
            throw new Exception("Informe um valor válido para esta meta.");
        if((cor == null || cor.trim().isEmpty()) && (!observacao.trim().isEmpty() || valor > 0.0))
            throw new Exception("Escolha uma cor para identificar esta meta.");
    }

    public void realizarUpperCaseDados() {
        observacao = observacao.toUpperCase();
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getMetaFinanceiraEmpresa() {
        return metaFinanceiraEmpresa;
    }

    public void setMetaFinanceiraEmpresa(int metaFinanceiraEmpresa) {
        this.metaFinanceiraEmpresa = metaFinanceiraEmpresa;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public String getCor() {
        if(cor == null){
        	cor = "";
        }
    	return cor;
    }

    public static MetaFinanceiraEmpresaValoresVO verificarValorQualMetaFoiAtingida(Double metaAtingida, List<MetaFinanceiraEmpresaValoresVO> listaMetaOrdenadaPeloValorAsc) {
        MetaFinanceiraEmpresaValoresVO metaAnteriorValores = new MetaFinanceiraEmpresaValoresVO();
        metaAnteriorValores.setCor("#FF0000");
        metaAnteriorValores.setObservacao("Abaixo da meta mínima");
        //for (MetaFinanceiraEmpresaValoresVO obj : metas) {
        for (int i=0; i<listaMetaOrdenadaPeloValorAsc.size(); i++) {
            MetaFinanceiraEmpresaValoresVO obj = listaMetaOrdenadaPeloValorAsc.get(i);
            obj.setNumeroMeta(1 + i);
            //verificar faixa de valor
            if (metaAtingida >= metaAnteriorValores.getValor()
                    && metaAtingida < obj.getValor()) {
                break;
            }
            metaAnteriorValores = obj;
        }
        return metaAnteriorValores;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public boolean isEmpty() {
        return valor == 0.0 &&
               observacao.trim().isEmpty() &&
               (cor == null || cor.trim().isEmpty());
    }
    /**
     * Joao Alcides
     * 17/02/2012
     */
    public String getCorTexto(){
    	//de inicio, a cor do texto será preta
    	String corTexto = "#000000";
    	String cor = this.getCor().replaceAll("#", "");
    	//obter cores separadamente
    	if(cor.length()> 0){
    		int r = Integer.parseInt(cor.substring(0, 2),16);
        	int g = Integer.parseInt(cor.substring(2, 4),16);
        	int b = Integer.parseInt(cor.substring(4, 6),16);
        	//aqui verifica a necessidade de mudar a cor do texto 
        	//se numa escala de 0 a 255 o valor de duas das cores forem menores do que 100 
        	//e o verde não for a cor dominante, mudar para branco a cor do texto
        	if( ((r < 100 && g < 100) || (r < 100 && b < 100) || (g < 100 && b < 100)) && (g < 220)){
        		corTexto = "#FFFFFF";
        	}	
    	}
    	return corTexto;
    }

    // João Alcides: o modelo de geração de log do sistema não gera de forma simples o log das entidades 
    // que possuem uma complexidade maior, como o MetaFinanceiroVO e suas dependências.
    // criei método próprio de gerar o log de alterações dessas entidades.
    public List<LogVO> montarLogAlteracao(Integer codigoMeta, UsuarioVO responsavel,String operacao) throws IllegalArgumentException, ClassNotFoundException, IllegalAccessException, Exception{
    	List<LogVO> log = gerarLogAlteracaoObjetoVO();
    	for(LogVO obj : log){
    		obj.setChavePrimaria(codigoMeta.toString());
    		obj.setNomeEntidade("METAFINANCEIRAEMPRESA");
    		obj.setChavePrimariaEntidadeSubordinada(getCodigo().toString());
    		obj.setNomeEntidadeDescricao("MetaFinanceiraEmpresaVO - MetaFinanceiraEmpresaValoresVO");
    		obj.setOperacao(operacao);
    		obj.setResponsavelAlteracao(responsavel.getNome());
                obj.setUserOAMD(responsavel.getUserOamd());
    		obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
    		obj.setNomeCampo("Meta-"+(getObservacao().isEmpty() ? 
    				((MetaFinanceiraEmpresaValoresVO)getObjetoVOAntesAlteracao()).getObservacao() : getObservacao())+": "+obj.getNomeCampo());
    	}
		return log;
    }

    public int getNumeroMeta() {
        return numeroMeta;
    }

    public void setNumeroMeta(int numeroMeta) {
        this.numeroMeta = numeroMeta;
    }
}

package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;
import org.json.JSONObject;

import java.util.Date;
import java.util.List;

public class AcessoGymPassTO extends SuperTO {

    private String label = "";
    private Integer quantidadeAcesso = 0;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getQuantidadeAcesso() {
        return quantidadeAcesso;
    }

    public void setQuantidadeAcesso(Integer quantidadeAcesso) {
        this.quantidadeAcesso = quantidadeAcesso;
    }

    private List<AcessoGymPassTO> acessosGymPassEmIntervalo;

    public AcessoGymPassTO(String label, Integer quantidadeAcesso) {
        this.label = label;
        this.quantidadeAcesso = quantidadeAcesso;
    }

    public AcessoGymPassTO() {
    }
}

package negocio.comuns.financeiro;

import java.util.Date;

import negocio.comuns.utilitarias.Uteis;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.UteisValidacao;

public class RepasseTO extends SuperTO {

    private static final long serialVersionUID = 7587880410051107229L;
    private String matricula = "";
    private String nomeAluno = "";
    private String plano = "";
    private Double valor = 0.0;
    private Date dataCompensacao;
    private Double totalDescontar = 0.0;
    private Double valorCompensado = 0.0;
    private Double valorRepasse = 0.0;
    private Integer movpagamento = 0;
    private Integer nrParcelas = 0;
    private String operadoraCartao = "";
    private Double taxa = 0.0;
    
    private Integer movproduto = null;
    private String mesReferencia = "";

    public String getValor_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valor);
    }

    public String getValorCompensado_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorCompensado);
    }

    public String getValorRepasse_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorRepasse);
    }

    public String getTotalDescontar_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(totalDescontar);
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getPlano() {
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public String getDataCompensacao_Apresentar() {
        if(UteisValidacao.emptyString(mesReferencia)){
            return Uteis.getData(dataCompensacao);
        }
        return mesReferencia;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public Double getTotalDescontar() {
        return totalDescontar;
    }

    public void setTotalDescontar(Double totalDescontar) {
        this.totalDescontar = totalDescontar;
    }

    public Double getValorCompensado() {
        return valorCompensado;
    }

    public void setValorCompensado(Double valorCompensado) {
        this.valorCompensado = valorCompensado;
    }

    public Double getValorRepasse() {
        return valorRepasse;
    }

    public void setValorRepasse(Double valorRepasse) {
        this.valorRepasse = valorRepasse;
    }

    public void setMovpagamento(Integer movpagamento) {
        this.movpagamento = movpagamento;
    }

    public Integer getMovpagamento() {
        return movpagamento;
    }

    public Integer getNrParcelas() {
        return nrParcelas;
    }

    public void setNrParcelas(Integer nrParcelas) {
        this.nrParcelas = nrParcelas;
    }

    public String getOperadoraCartao() {
        return operadoraCartao;
    }

    public void setOperadoraCartao(String operadoraCartao) {
        this.operadoraCartao = operadoraCartao;
    }

    public Double getTaxa() {
        return taxa;
    }

    public void setTaxa(Double taxa) {
        this.taxa = taxa;
    }

    public Integer getMovproduto() {
        return movproduto;
    }

    public void setMovproduto(Integer movproduto) {
        this.movproduto = movproduto;
    }

    public String getMesReferencia() {
        return mesReferencia;
    }

    public void setMesReferencia(String mesReferencia) {
        this.mesReferencia = mesReferencia;
    }
    
    
}

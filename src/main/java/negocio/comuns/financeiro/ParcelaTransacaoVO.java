package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;

public class ParcelaTransacaoVO extends SuperVO {
    private String nomeCliente;
    private Integer codigoCliente;
    private String matricula;
    private Integer codigoParcela;
    private String descricaoParcela;
    private String situacaoParcela;
    private Double valorParcela;
    private ClienteVO cliente;

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getCodigoParcela() {
        return codigoParcela;
    }

    public void setCodigoParcela(Integer codigoParcela) {
        this.codigoParcela = codigoParcela;
    }

    public String getDescricaoParcela() {
        return descricaoParcela;
    }

    public void setDescricaoParcela(String descricaoParcela) {
        this.descricaoParcela = descricaoParcela;
    }

    public String getSituacaoParcela() {
        return situacaoParcela;
    }

    public void setSituacaoParcela(String situacaoParcela) {
        this.situacaoParcela = situacaoParcela;
    }

    public String getSituacaoParcela_Apresentar() {
        if (getSituacaoParcela() == null) {
            return "";
        }
        if (getSituacaoParcela().equals("EA")) {
            return "Em Aberto";
        }
        if (getSituacaoParcela().equals("PG")) {
            return "Pago";
        }
        if (getSituacaoParcela().equals("CA")) {
            return "Cancelado";
        }
        return getSituacaoParcela();
    }

    public Double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public String getValorParcela_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorParcela());
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }
}
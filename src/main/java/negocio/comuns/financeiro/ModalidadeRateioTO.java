package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;

/**
 * Reponsável por manter os dados da entidade de rateio
 */
public class ModalidadeRateioTO extends SuperTO {

    private static final long serialVersionUID = 646868035091197960L;
    private int codigo;
    private String nome;
    private List<RateioIntegracaoTO> rateios;

    public String getMensagem() {
        String msg = "";
        Integer rateiosSize = rateios == null ? 0 : rateios.size();
        msg += rateiosSize > 0 ? "Existem rateios definidos para esta modalidade. " : "<font color=\"red\">Não existem rateios definidos para esta modalidade.</font> ";
        return msg;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<RateioIntegracaoTO> getRateios() {
        if (rateios == null) {
            rateios = new ArrayList<RateioIntegracaoTO>();
        }
        return rateios;
    }

    public void setRateios(List<RateioIntegracaoTO> rateios) {
        this.rateios = rateios;
    }
}

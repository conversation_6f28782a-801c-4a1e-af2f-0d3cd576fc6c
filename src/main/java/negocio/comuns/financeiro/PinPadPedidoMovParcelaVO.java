package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

public class PinPadPedidoMovParcelaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private int pinpadpedido;
    private int movparcela;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getPinpadpedido() {
        return pinpadpedido;
    }

    public void setPinpadpedido(int pinpadpedido) {
        this.pinpadpedido = pinpadpedido;
    }

    public int getMovparcela() {
        return movparcela;
    }

    public void setMovparcela(int movparcela) {
        this.movparcela = movparcela;
    }
}

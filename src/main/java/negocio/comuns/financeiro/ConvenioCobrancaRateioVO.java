package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 15/05/2020
 */
public class ConvenioCobrancaRateioVO extends SuperVO {

    private Integer codigo;
    private Date dataRegistro;
    private Date dataAlteracao;
    private String descricao;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private EmpresaVO empresaVO;
    private ProdutoVO produtoVO;
    private PlanoVO planoVO;
    private String tipoProduto;
    private boolean padrao = false;
    @NaoControlarLogAlteracao
    private List<ConvenioCobrancaRateioItemVO> itens;

    public ConvenioCobrancaRateioVO() {
        super();
    }

    public ConvenioCobrancaRateioVO(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO) {
        super();
        this.convenioCobrancaVO = convenioCobrancaVO;
        this.empresaVO = empresaVO;
    }

    public static void validarDados(ConvenioCobrancaRateioVO rateioValidar, List<PlanoVO> listaPlanos, List<ProdutoVO> listaProdutos) throws ConsistirException {
        if (!rateioValidar.getValidarDados()) {
            return;
        }
        if (UteisValidacao.emptyString(rateioValidar.getDescricao().trim())) {
            throw new ConsistirException("O campo DESCRIÇÃO (Convênio Cobrança Rateio) deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(rateioValidar.getEmpresaVO().getCodigo())) {
            throw new ConsistirException("Selecione a empresa.");
        }
        if (UteisValidacao.emptyList(rateioValidar.getItens())) {
            throw new ConsistirException("Adicione pelo menos um item no rateio.");
        }
        BigDecimal porcentagem = BigDecimal.ZERO; // Usando BigDecimal para maior precisão

        // Iterando sobre os itens e somando as porcentagens com BigDecimal
        for (ConvenioCobrancaRateioItemVO itemVO : rateioValidar.getItens()) {
            porcentagem = porcentagem.add(new BigDecimal(itemVO.getPorcentagem().toString()));
        }

        // Comparando com 100% (em BigDecimal) sem arredondar a soma
        BigDecimal cemPorcento = new BigDecimal("100.00");

        // Usando compareTo para comparar a precisão total
        if (porcentagem.compareTo(cemPorcento) != 0) {
            throw new ConsistirException("A soma da porcentagem do rateio deve ser 100%.");
        }

        List <ConvenioCobrancaRateioVO> listaTodosRateiosExistentesConvenio = new ArrayList<>();
        listaTodosRateiosExistentesConvenio = rateioValidar.getConvenioCobrancaVO().getListaConvenioCobrancaRateioVO();
        listaTodosRateiosExistentesConvenio.removeIf(r -> r.getCodigo() == rateioValidar.getCodigo()); //remmove o que está editando para que ele não passe na lsita 2 vezxes nas validações abaixo

        //VALIDAÇÕES BÁSICAS DE DUPLICAÇÃO
        for (ConvenioCobrancaRateioVO rateioExistente : listaTodosRateiosExistentesConvenio) {
            if (!UteisValidacao.emptyNumber(rateioExistente.getProdutoVO().getCodigo()) && !UteisValidacao.emptyNumber(rateioValidar.getProdutoVO().getCodigo())
                    && rateioExistente.getProdutoVO().getCodigo().equals(rateioValidar.getProdutoVO().getCodigo())) {
                ProdutoVO produtoVO = obterProdutoVO(listaProdutos, rateioExistente.getProdutoVO().getCodigo());
                throw new ConsistirException("Já existe um rateio na lista principal cadastrado para o Produto: '" + produtoVO.getDescricao() + "' informado!");
            }
            if (!UteisValidacao.emptyNumber(rateioExistente.getPlanoVO().getCodigo()) && !UteisValidacao.emptyNumber(rateioValidar.getPlanoVO().getCodigo())
                    && rateioExistente.getPlanoVO().getCodigo().equals(rateioValidar.getPlanoVO().getCodigo())) {
                PlanoVO planoVO = obterPlanoVO(listaPlanos, rateioExistente.getPlanoVO().getCodigo());
                throw new ConsistirException("Já existe um rateio na lista principal cadastrado para o Plano: '" + planoVO.getDescricao() + "' informado!");
            }
            if (!UteisValidacao.emptyString(rateioExistente.getTipoProduto()) && !UteisValidacao.emptyString(rateioValidar.getTipoProduto())
                    && rateioExistente.getTipoProduto().equals(rateioValidar.getTipoProduto())) {
                TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(rateioExistente.getTipoProduto());
                throw new ConsistirException("Já existe um rateio na lista principal cadastrado para o Tipo Produto: '" + tipoProduto.getDescricao() + "' informado!");
            }
        }

        //VALIDAÇÃO ESPECÍFICA DE DUPLICAÇÃO
        //Se definiu plano específico, precisa validar se o produto padrão do gerar parcelas do plano já não está presente na lista de rateios existentes
        boolean informouPlanoEspecifico = !UteisValidacao.emptyNumber(rateioValidar.getPlanoVO().getCodigo());
        if (informouPlanoEspecifico) {
            PlanoVO planoVO = obterPlanoVO(listaPlanos, rateioValidar.getPlanoVO().getCodigo());
            int codProdutoGerarParcelasPlanoDoPlanoInformado = planoVO.getProdutoPadraoGerarParcelasContrato().getCodigo();
            for (ConvenioCobrancaRateioVO rateioExistente : listaTodosRateiosExistentesConvenio) {
                if (rateioExistente.getProdutoVO().getCodigo().equals(codProdutoGerarParcelasPlanoDoPlanoInformado)) {
                    ProdutoVO produtoVO = obterProdutoVO(listaProdutos, codProdutoGerarParcelasPlanoDoPlanoInformado);
                    throw new ConsistirException("O plano: " + planoVO.getDescricao() + " tem o produto padrão: '" + produtoVO.getDescricao()
                            + "' que já está cadastrado na lista de rateios existentes. Você precisa editar o existente ou alterar o 'produto padrão gerar parcelas contrato' lá no cadastro de plano");
                }
            }
        }

        //VALIDAÇÃO ESPECÍFICA DE DUPLICAÇÃO
        //Se definiu produto específico, precisa validar se o produto padrão do gerar parcelas do plano já não está presente na lista de rateios existentes
        boolean informouProdutoEspecifico = !UteisValidacao.emptyNumber(rateioValidar.getProdutoVO().getCodigo());
        if (informouProdutoEspecifico) {
            for (ConvenioCobrancaRateioVO rateioExistente : listaTodosRateiosExistentesConvenio) {
                PlanoVO planoVO = new PlanoVO();
                planoVO = obterPlanoVO(listaPlanos, rateioExistente.getPlanoVO().getCodigo());
                if (planoVO == null || UteisValidacao.emptyNumber(planoVO.getCodigo())) {
                    continue;
                }
                int codProdutoGerarParcelasPlanoDoPlanoExistente = planoVO.getProdutoPadraoGerarParcelasContrato().getCodigo();
                if (rateioValidar.getProdutoVO().getCodigo().equals(codProdutoGerarParcelasPlanoDoPlanoExistente)) {
                    ProdutoVO produtoVO = obterProdutoVO(listaProdutos, rateioValidar.getProdutoVO().getCodigo());
                    throw new ConsistirException("O produto: " + produtoVO.getDescricao() + " já está definido como o produto padrão do plano: '" + planoVO.getDescricao()
                            + "' que já está cadastrado na lista de rateios existentes. Você precisa editar o existente ou alterar o 'produto padrão gerar parcelas contrato' lá no cadastro de plano");
                }
            }
        }


        if (rateioValidar.getConvenioCobrancaVO() != null && rateioValidar.getConvenioCobrancaVO().isStoneV5()) {
            if (rateioValidar.getItens().size() == 1) {
                throw new ConsistirException("Você precisa informar pelo menos 2 items para o rateio.");
            }

            boolean possuiRecebedorPrincipal = false;
            for (ConvenioCobrancaRateioItemVO itemVO : rateioValidar.getItens()) {
                if (itemVO.isRecebedorPrincipal()) {
                    possuiRecebedorPrincipal = true;
                }
            }
            if (!possuiRecebedorPrincipal) {
                throw new ConsistirException("Você precisa informar pelo menos um recebedor principal. Edite um dos recebedores da lista e marque a opção \"Recebedor Principal\".");
            }
        }

        if (UteisValidacao.emptyNumber(rateioValidar.getPlanoVO().getCodigo()) && UteisValidacao.emptyNumber(rateioValidar.getProdutoVO().getCodigo())
                && UteisValidacao.emptyString(rateioValidar.getTipoProduto()) && !rateioValidar.isPadrao()) {
            throw new ConsistirException("Você não selecionou nenhum PLANO ou PRODUTO ou TIPO PRODUTO. Neste caso para que o Split funcione," +
                    " marque a opção \"Padrão para todos os recebimentos\" depois tente novamente.");
        }
    }

    public static PlanoVO obterPlanoVO(List<PlanoVO> listaPlanos, int codPlano) {
        if (UteisValidacao.emptyList(listaPlanos)) {
            return null;
        }
        for (PlanoVO plano : listaPlanos) {
            if (plano.getCodigo().equals(codPlano)) {
                return plano;
            }
        }
        return null;
    }

    public static ProdutoVO obterProdutoVO(List<ProdutoVO> listaProdutos, int codProduto) {
        if (UteisValidacao.emptyList(listaProdutos)) {
            return null;
        }
        for (ProdutoVO produto : listaProdutos) {
            if (produto.getCodigo().equals(codProduto)) {
                return produto;
            }
        }
        return null;
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public ProdutoVO getProdutoVO() {
        if (produtoVO == null) {
            produtoVO = new ProdutoVO();
        }
        return produtoVO;
    }

    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    public PlanoVO getPlanoVO() {
        if (planoVO == null) {
            planoVO = new PlanoVO();
        }
        return planoVO;
    }

    public void setPlanoVO(PlanoVO planoVO) {
        this.planoVO = planoVO;
    }

    public List<ConvenioCobrancaRateioItemVO> getItens() {
        if (itens == null) {
            itens = new ArrayList<>();
        }
        return itens;
    }

    public void setItens(List<ConvenioCobrancaRateioItemVO> itens) {
        this.itens = itens;
    }

    public String getTipoProduto() {
        if (tipoProduto == null) {
            tipoProduto = "";
        }
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public String getTipoProduto_Apresentar() {
        if (!UteisValidacao.emptyString(getTipoProduto())) {
            TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(getTipoProduto());
            if (tipoProduto != null) {
                return tipoProduto.getDescricao();
            }
        }
        return "";
    }

    public boolean isPadrao() {
        return padrao;
    }

    public void setPadrao(boolean padrao) {
        this.padrao = padrao;
    }
}

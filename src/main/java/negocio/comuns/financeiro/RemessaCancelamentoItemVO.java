package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by GlaucoT on 07/04/2016
 */
public class RemessaCancelamentoItemVO extends SuperRemessaItemVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private RemessaItemVO itemRemessaCancelar;
    @NaoControlarLogAlteracao
    private Boolean selecionado = false;
    @NaoControlarLogAlteracao
    private List<MovParcelaVO> listaMovParcela;
    private MovParcelaVO movParcela = new MovParcelaVO();

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public RemessaItemVO getItemRemessaCancelar() {
        if (itemRemessaCancelar == null) {
            itemRemessaCancelar = new RemessaItemVO();
        }
        return itemRemessaCancelar;
    }

    public void setItemRemessaCancelar(RemessaItemVO itemRemessaCancelar) {
        this.itemRemessaCancelar = itemRemessaCancelar;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Integer getMovPagamento() {
        if (getItemRemessaCancelar().getMovPagamento() != null) {
            return getItemRemessaCancelar().getMovPagamento().getCodigo();
        }
        return null;
    }

    public Integer getReciboPagamento() {
        if (getMovPagamento() != null) {
            return getItemRemessaCancelar().getMovPagamento().getReciboPagamento().getCodigo();
        }
        return null;
    }

    public List<MovParcelaVO> getListaMovParcela() {
        if (listaMovParcela == null) {
            listaMovParcela = new ArrayList<MovParcelaVO>();
            listaMovParcela.add(getItemRemessaCancelar().getMovParcela());
        }
        return listaMovParcela;
    }

    public boolean isLayoutFebrabanDCO() {
        return false;
    }

    public TipoRemessaEnum getTipo() {
        return remessa.getTipo();
    }

    public void setTipo(TipoRemessaEnum tipo) {
        //Não alterar tipo, por ser uma remessa de cancelamento.
    }

    public String getPessoa_Apresentar() {
        return getItemRemessaCancelar().getMovParcela().getPessoa_Apresentar();
    }

    public String getDataRemessa() {
        return Uteis.getData(getRemessa().getDataRegistro());
    }

    public String getCodRemessa() {
        return getItemRemessaCancelar().getRemessa().getCodigo().toString();
    }

    public String getDescricaoParcela() {
        return getItemRemessaCancelar().getMovParcela().getDescricao();
    }

    public String getValorParcela() {
        return Formatador.formatarValorMonetarioSemMoeda(getItemRemessaCancelar().getMovParcela().getValorParcela());
    }

    public MovParcelaVO getMovParcela() {
        if(movParcela == null){
            movParcela = new MovParcelaVO();
        }
        return movParcela;
    }

    public void setMovParcela(MovParcelaVO movParcela) {
        this.movParcela = movParcela;
    }
}

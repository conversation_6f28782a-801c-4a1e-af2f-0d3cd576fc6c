package negocio.comuns.financeiro;

import annotations.arquitetura.ExportFunctionExecute;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import org.json.JSONArray;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.enumerador.SituacaoNFSeEnum;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.*;

public class ItemGestaoNotasTO extends SuperTO {

    private Integer codigo;
    private String nome = "";
    private String matricula = "";
    private String cpf = "";
    private Double valor = 0.0;
    private boolean selecionado = false;
    private boolean selecionadoDesvincular = false;
    private Boolean nfseemitida = false;
    private Integer codNFSeEmitida;
    private Integer codNFCeEmitida;
    private Boolean parcialmenteemitida = false;
    private String produtosPagos = "";
    private List<Integer> formasPagamento;
    private Map<Integer, Double> valoresEFormas;
    private Integer codCliente = 0;
    private Integer codColaborador = 0;

    private ChequeVO chequeVO = null;
    private MovProdutoVO movProdutoVO = null;
    private MovPagamentoVO movPagamentoVO = null;
    private CartaoCreditoVO cartaoCreditoVO = null;
    private ReciboPagamentoVO reciboPagamentoVO = null;
    @ExportFunctionExecute(function = "replaceAll",arq = {"<br>","\\n"})
    private String descricaoProdutosPagos;
    private Integer clienteTitular;
    private String retorno = "";
    private Integer rps;
    private String nrNotaManual;
    private Integer quantidade;
    private String endereco;
    private Integer codUsuarioResponsavelPagamento;
    private Integer id_NFCe;
    private MovContaVO movContaVO;
    private Date dataEmissao;
    private Date dataReferenciaItem;
    private SituacaoNotaFiscalEnum situacaoNotaFiscal;

    private Double valorEmitido;
    private boolean click = false;
    private boolean emitidoDeOutraForma = false;
    private Double valorNaoEmitido; //utilizado para totalizador
    private JSONArray arrayJSONEnviar;
    private List<NFSeEmitidaVO> listaNFSEEmitida;

    private Integer codNotaFiscal;
    private NotaFiscalVO notaFiscalVO;
    private Integer sequencialFamilia;
    private boolean selecionadoExcluir = false;
    private String nomeResponsavel;
    private String nomeResponsavelEmissaoNota;

    private PessoaVO pessoaVO;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getValor_apresentar() {
        return Formatador.formatarValorMonetario(getValor());
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }



    public String getProdutosPagos() {
        return produtosPagos;
    }

    public void setProdutosPagos(String produtosPagos) {
        this.produtosPagos = produtosPagos;
    }

    public Boolean processarProdutosPagos(String tipoProdutoEmissaoNFSe, boolean processarContaCorrente, EmpresaVO empresaVO) {
        String[] produtos = new String[0];
        if (getProdutosPagos() != null) {
            produtos = getProdutosPagos().split("\\|");
        }
        Boolean produtoEmitir = false;
        //|prodPa.produto,prodPa.tipo,prodPa.contrato,prodPa.valorPago;
        Double valorPagoProduto = 0.0;
        if (!UteisValidacao.emptyString(getProdutosPagos()) && !processarContaCorrente) {
            for (String infoProdutoTemp : produtos) {
                if (!infoProdutoTemp.isEmpty()) {
                    String[] infoProduto = infoProdutoTemp.split(",");
                    if (tipoProdutoEmissaoNFSe.contains(infoProduto[1])) {
                        valorPagoProduto += Double.parseDouble(infoProduto[3]);
                        produtoEmitir = true;
                    }
                }
            }
        } else if (processarContaCorrente) {
            valorPagoProduto = getValor();
            produtoEmitir = true;
        }
        if(empresaVO != null && (TipoRelatorioDF
                .getTipoRelatorioDF(empresaVO.getTipoGestaoNFSe())
                .equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA) &&
                !empresaVO.isEmiteValorTotalFaturamento())){
            setValor(valorPagoProduto);
        }
        return produtoEmitir;
    }

    public MovProdutoVO getMovProdutoVO() {
        if(movProdutoVO == null)
            movProdutoVO = new MovProdutoVO();
        return movProdutoVO;
    }

    public void setMovProdutoVO(MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    public String getRetorno() {
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public CartaoCreditoVO getCartaoCreditoVO() {
        return cartaoCreditoVO;
    }

    public void setCartaoCreditoVO(CartaoCreditoVO cartaoCreditoVO) {
        this.cartaoCreditoVO = cartaoCreditoVO;
    }

    public ChequeVO getChequeVO() {
        return chequeVO;
    }

    public void setChequeVO(ChequeVO chequeVO) {
        this.chequeVO = chequeVO;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public ReciboPagamentoVO getReciboPagamentoVO() {
        return reciboPagamentoVO;
    }

    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamentoVO) {
        this.reciboPagamentoVO = reciboPagamentoVO;
    }

    public Integer getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(Integer codCliente) {
        this.codCliente = codCliente;
    }

    public Boolean getParcialmenteemitida() {
        return parcialmenteemitida;
    }

    public void setParcialmenteemitida(Boolean parcialmenteemitida) {
        this.parcialmenteemitida = parcialmenteemitida;
    }

    public String getStyle() {
        if (getParcialmenteemitida()) {
            return "background-color: rgba(71, 203, 127, 0.4);";
        } else {
            return "";
        }
    }

    public String getTextStyle() {
        if (getParcialmenteemitida()) {
            return "color: #000 !important;";
        } else {
            return "";
        }
    }

    public List<Integer> getFormasPagamento() {
        if (formasPagamento == null) {
            formasPagamento = new ArrayList<>();
        }
        return formasPagamento;
    }

    public void setFormasPagamento(List<Integer> formasPagamento) {
        this.formasPagamento = formasPagamento;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Boolean getNfseemitida() {
        return nfseemitida;
    }

    public String getNotaNFSEEmitida(){
        return ((this.nfseemitida != null) && (this.nfseemitida)) ? "SIM": "NÃO";
    }

    public void setNfseemitida(Boolean nfseemitida) {
        this.nfseemitida = nfseemitida;
    }

    public Integer getCodColaborador() {
        return codColaborador;
    }

    public void setCodColaborador(Integer codColaborador) {
        this.codColaborador = codColaborador;
    }

    public Map<Integer, Double> getValoresEFormas() {
        if (valoresEFormas == null) {
            valoresEFormas = new HashMap<>();
        }
        return valoresEFormas;
    }

    public void setValoresEFormas(Map<Integer, Double> valoresEFormas) {
        this.valoresEFormas = valoresEFormas;
    }

    public String getDescricaoProdutosPagos() {
        if (descricaoProdutosPagos == null) {
            descricaoProdutosPagos = "";
        }
        return descricaoProdutosPagos;
    }

    public void setDescricaoProdutosPagos(String descricaoProdutosPagos) {
        this.descricaoProdutosPagos = descricaoProdutosPagos;
    }

    public String getDescricaoProdutosPagos_apresentar() {
        return String.join("<br>",new HashSet<String>(Arrays.asList(descricaoProdutosPagos.split("<br>"))).toArray(new String[0]) );
    }

    public Integer getRps() {
        return rps;
    }

    public void setRps(Integer rps) {
        this.rps = rps;
    }

    public Integer getClienteTitular() {
        return clienteTitular;
    }

    public void setClienteTitular(Integer clienteTitular) {
        this.clienteTitular = clienteTitular;
    }

    public String getNrNotaManual() {
        if (nrNotaManual == null) {
            nrNotaManual = "";
        }
        return nrNotaManual;
    }

    public void setNrNotaManual(String nrNotaManual) {
        this.nrNotaManual = nrNotaManual;
    }

    public Integer getQuantidade() {
        if (quantidade == null) {
            quantidade = 0;
        }
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }
    
    public Integer getDiaCompetencia(){
        
        String dataFormatada = movProdutoVO.getDataLancamento_Apresentar();
        
        String dia = dataFormatada.substring(0,2);
        
        return Integer.parseInt(dia);
    }

    public String getCor() {
        if (this.getNfseemitida()) {
            return SituacaoNFSeEnum.ENVIADA.getCor();
        } else {
            return SituacaoNFSeEnum.NAO_ENVIADA.getCor();
        }
    }

    public boolean getApresentarDesvincular() {
        return !UteisValidacao.emptyNumber(getRps());
    }

    public boolean isApresentarExcluirNotaFiscalNFSe() {
        return !UteisValidacao.emptyNumber(getCodNotaFiscal()) &&
                !UteisValidacao.emptyNumber(getCodNFSeEmitida()) &&
                !UteisValidacao.emptyNumber(getNotaFiscalVO().getCodigo()) &&
                getNotaFiscalVO().getStatusNotaEnum().isPodeEstornar();
    }

    public boolean isApresentarExcluirNotaFiscalNFCe() {
        return !UteisValidacao.emptyNumber(getCodNotaFiscal()) &&
                !UteisValidacao.emptyNumber(getCodNFCeEmitida()) &&
                !UteisValidacao.emptyNumber(getNotaFiscalVO().getCodigo()) &&
                getNotaFiscalVO().getStatusNotaEnum().isPodeEstornar();
    }

    public boolean getApresentarDesvincularNFCe() {
        return !UteisValidacao.emptyNumber(getId_NFCe()) || ((notaFiscalVO != null && notaFiscalVO.getTipo().equals(TipoNotaFiscalEnum.NFE) && !UteisValidacao.emptyNumber(getCodNFSeEmitida())) &&
                getNotaFiscalVO().getStatusNotaEnum().isPodeEstornar());
    }

    public String getEndereco() {
        if (endereco == null) {
            endereco = "";
        }
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getDescricaoProduto() {
        return getMovProdutoVO().getDescricao();
    }

    public Integer getCodUsuarioResponsavelPagamento() {
        if (codUsuarioResponsavelPagamento == null) {
            codUsuarioResponsavelPagamento = 0;
        }
        return codUsuarioResponsavelPagamento;
    }

    public void setCodUsuarioResponsavelPagamento(Integer codUsuarioResponsavelPagamento) {
        this.codUsuarioResponsavelPagamento = codUsuarioResponsavelPagamento;
    }

    public Integer getId_NFCe() {
        if (id_NFCe == null) {
            id_NFCe = 0;
}
        return id_NFCe;
    }

    public void setId_NFCe(Integer id_NFCe) {
        this.id_NFCe = id_NFCe;
    }

    public MovContaVO getMovContaVO() {
        return movContaVO;
    }

    public void setMovContaVO(MovContaVO movContaVO) {
        this.movContaVO = movContaVO;
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public Date getDataReferenciaItem() {
        return dataReferenciaItem;
    }

    public void setDataReferenciaItem(Date dataReferenciaItem) {
        this.dataReferenciaItem = dataReferenciaItem;
    }

    public String getDataReferenciaItem_Apresentar() {
        return Uteis.getData(getDataReferenciaItem());
    }

    public String getDataMesReferenciaItem_Apresentar() {
        return Uteis.getDataAplicandoFormatacao(getDataReferenciaItem(), "MM/YYYY");
    }

    public String getDataEmissao_Apresentar() {
        return Uteis.getData(getDataEmissao());
    }

    public Double getValorEmitido() {
        if (valorEmitido == null) {
            valorEmitido = 0.0;
        }
        return valorEmitido;
    }

    public void setValorEmitido(Double valorEmitido) {
        this.valorEmitido = valorEmitido;
    }

    public boolean isClick() {
        return click;
    }

    public void setClick(boolean click) {
        this.click = click;
    }

    public Double getValorNaoEmitido() {
        if (valorNaoEmitido == null) {
            valorNaoEmitido = 0.0;
        }
        return valorNaoEmitido;
    }

    public void setValorNaoEmitido(Double valorNaoEmitido) {
        this.valorNaoEmitido = valorNaoEmitido;
    }

    public String getValorNaoEmitido_apresentar() {
        return Formatador.formatarValorMonetario(getValorNaoEmitido());
    }

    public String getValorTotal_apresentar() {
        return Formatador.formatarValorMonetario(getValor() + getValorNaoEmitido());
    }

    public boolean isEmitidoDeOutraForma() {
        return emitidoDeOutraForma;
    }

    public void setEmitidoDeOutraForma(boolean emitidoDeOutraForma) {
        this.emitidoDeOutraForma = emitidoDeOutraForma;
    }

    public JSONArray getArrayJSONEnviar() {
        if (arrayJSONEnviar == null) {
            arrayJSONEnviar = new JSONArray();
        }
        return arrayJSONEnviar;
    }

    public void setArrayJSONEnviar(JSONArray arrayJSONEnviar) {
        this.arrayJSONEnviar = arrayJSONEnviar;
    }

    public List<NFSeEmitidaVO> getListaNFSEEmitida() {
        if (listaNFSEEmitida == null) {
            listaNFSEEmitida = new ArrayList<NFSeEmitidaVO>();
        }
        return listaNFSEEmitida;
    }

    public void setListaNFSEEmitida(List<NFSeEmitidaVO> listaNFSEEmitida) {
        this.listaNFSEEmitida = listaNFSEEmitida;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public SituacaoNotaFiscalEnum getSituacaoNotaFiscal() {
        return situacaoNotaFiscal;
    }

    public void setSituacaoNotaFiscal(SituacaoNotaFiscalEnum situacaoNotaFiscal) {
        this.situacaoNotaFiscal = situacaoNotaFiscal;
    }

    public Integer getCodNFSeEmitida() {
        if (codNFSeEmitida == null) {
            codNFSeEmitida = 0;
        }
        return codNFSeEmitida;
    }

    public void setCodNFSeEmitida(Integer codNFSeEmitida) {
        this.codNFSeEmitida = codNFSeEmitida;
    }

    public Integer getCodNotaFiscal() {
        if (codNotaFiscal == null) {
            codNotaFiscal = 0;
        }
        return codNotaFiscal;
    }

    public void setCodNotaFiscal(Integer codNotaFiscal) {
        this.codNotaFiscal = codNotaFiscal;
    }

    public NotaFiscalVO getNotaFiscalVO() {
        if (notaFiscalVO == null) {
            notaFiscalVO = new NotaFiscalVO();
        }
        return notaFiscalVO;
    }

    public void setNotaFiscalVO(NotaFiscalVO notaFiscalVO) {
        this.notaFiscalVO = notaFiscalVO;
    }

    public Integer getCodNFCeEmitida() {
        if (codNFCeEmitida == null) {
            codNFCeEmitida = 0;
        }
        return codNFCeEmitida;
    }

    public void setCodNFCeEmitida(Integer codNFCeEmitida) {
        this.codNFCeEmitida = codNFCeEmitida;
    }

    public Integer getSequencialFamilia() {
        return sequencialFamilia;
    }

    public void setSequencialFamilia(Integer sequencialFamilia) {
        this.sequencialFamilia = sequencialFamilia;
    }

    public boolean isSelecionadoExcluir() {
        return selecionadoExcluir;
    }

    public void setSelecionadoExcluir(boolean selecionadoExcluir) {
        this.selecionadoExcluir = selecionadoExcluir;
    }

    public boolean isSelecionadoDesvincular() {
        return selecionadoDesvincular;
    }

    public void setSelecionadoDesvincular(boolean selecionadoDesvincular) {
        this.selecionadoDesvincular = selecionadoDesvincular;
    }

    public String getNomeResponsavel() {
        if (nomeResponsavel == null) {
            nomeResponsavel = "";
        }
        return nomeResponsavel;
    }

    public void setNomeResponsavel(String nomeResponsavel) {
        this.nomeResponsavel = nomeResponsavel;
    }

    public String getNomeResponsavelEmissaoNota() {
        if(nomeResponsavelEmissaoNota == null){
            nomeResponsavelEmissaoNota = "";
        }
        return nomeResponsavelEmissaoNota;
    }

    public void setNomeResponsavelEmissaoNota(String nomeResponsavelEmissaoNota) {
        this.nomeResponsavelEmissaoNota = nomeResponsavelEmissaoNota;
    }

    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public Boolean getResponsavelReciboDiferenteDoTitularContrato (){
        if(!UteisValidacao.emptyString(nome) && pessoaVO != null){
            return !nome.equals(pessoaVO.getNome());
        }
        return false;
    }

    public String getMensagemResponsavelReciboDiferenteDoTitularContrato (){
        return "Nota referente ao plano/produto do(a) cliente: \n" +
                pessoaVO.getNome();
    }

    public String getTipoNota(){
        if (UteisValidacao.emptyNumber(id_NFCe) && !nfseemitida){
            return "";
        }
        return notaFiscalVO.getTipo().toString();
    }
}

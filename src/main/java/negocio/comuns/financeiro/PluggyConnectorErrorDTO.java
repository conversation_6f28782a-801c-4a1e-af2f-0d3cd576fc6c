package negocio.comuns.financeiro;

import org.json.JSONObject;

/**
 * Created by <PERSON> on 17/07/2023.
 */

public class PluggyConnectorErrorDTO {

    protected String code;
    protected String message;
    protected String providerMessage;
    protected String[] qrCodeCaixa;

    public PluggyConnectorErrorDTO(JSONObject json) {
        this.code = json.optString("code", "");
        this.message = json.optString("message", "");
        this.providerMessage = json.optString("providerMessage", "");
    }

    public PluggyConnectorErrorDTO() {
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getProviderMessage() {
        return providerMessage;
    }

    public void setProviderMessage(String providerMessage) {
        this.providerMessage = providerMessage;
    }

    public String[] getQrCodeCaixa() {
        return qrCodeCaixa;
    }

    public void setQrCodeCaixa(String[] qrCodeCaixa) {
        this.qrCodeCaixa = qrCodeCaixa;
    }

}

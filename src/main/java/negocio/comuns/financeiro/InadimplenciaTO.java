package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/*
 * Created by <PERSON><PERSON> on 24/05/2018.
 */
public class InadimplenciaTO extends SuperTO {

    private String label = "";
    private Double valor = 0.0;
    private Integer quantidade = 0;
    private Double porcentagem = 0.0;
    private String css = "";
    private Date mesReferencia;
    private String title;
    private List<MovParcelaVO> listaParcelas;
    private Integer quantidadeAlunos = 0;
    private Integer codConvenio = 0;

    public InadimplenciaTO() {
    }

    public InadimplenciaTO(JSONObject json) {
        this.setLabel(json.getString("label"));
        this.setValor(json.getDouble("valor"));
        this.setQuantidade(json.getInt("quantidade"));
        this.setPorcentagem(json.getDouble("porcentagem"));
        if (json.has("mesReferencia")) {
            this.setMesReferencia(new Date(json.getLong("mesReferencia")));
        }
        this.setQuantidadeAlunos(json.getInt("quantidadeAlunos"));
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valor);
    }

    public String getCss() {
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }

    public Double getPorcentagem() {
        return porcentagem;
    }

    public void setPorcentagem(Double porcentagem) {
        this.porcentagem = porcentagem;
    }

    public String getPorcentagem_Apresentar() {
        if (getPorcentagem() == null) {
            return "";
        }
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(getPorcentagem());
    }

    public void calcularPorcentagem(Double valorParaComparar) {
        if (valorParaComparar > 0.0) {
            setPorcentagem((this.getValor() / valorParaComparar) * 100);
        } else {
            setPorcentagem(0.0);
        }
    }

    public Integer getCodConvenio() {
        return codConvenio;
    }

    public void setCodConvenio(Integer codConvenio) {
        this.codConvenio = codConvenio;
    }

    public Date getMesReferencia() {
        return mesReferencia;
    }

    public String getMesReferencia_Apresentar() {
        if (getLabel().equals("Média")) {
            return "Média";
        }
        return Calendario.getData(mesReferencia, "MMMM/yyyy");
    }

    public void setMesReferencia(Date mesReferencia) {
        this.mesReferencia = mesReferencia;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<MovParcelaVO> getListaParcelas() {
        if (listaParcelas == null) {
            listaParcelas = new ArrayList<MovParcelaVO>();
        }
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public Integer getQuantidadeAlunos() {
        return quantidadeAlunos;
    }

    public void setQuantidadeAlunos(Integer quantidadeAlunos) {
        this.quantidadeAlunos = quantidadeAlunos;
    }

    public void processarQtdValor() {
        Double valor = 0.0;
        for (MovParcelaVO movParcelaVO : getListaParcelas()) {
            valor += movParcelaVO.getValorParcela();
        }
        setQuantidade(getListaParcelas().size());
        setValor(Uteis.arredondarForcando2CasasDecimais(valor));
    }
}

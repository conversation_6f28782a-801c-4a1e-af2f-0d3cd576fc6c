package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.SuperVO;
import org.json.JSONObject;

public class FormaPagamentoPerfilAcessoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    @ChaveEstrangeira
    private FormaPagamentoVO formaPagamento = new FormaPagamentoVO();
    @ChaveEstrangeira
    private PerfilAcessoVO perfilAcessoVO;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public FormaPagamentoVO getFormaPagamento() {
        if (formaPagamento == null) {
            formaPagamento = new FormaPagamentoVO();
        }
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamentoVO formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public PerfilAcessoVO getPerfilAcessoVO() {
        if (perfilAcessoVO == null) {
            perfilAcessoVO = new PerfilAcessoVO();
        }
        return perfilAcessoVO;
    }

    public void setPerfilAcessoVO(PerfilAcessoVO perfilAcessoVO) {
        this.perfilAcessoVO = perfilAcessoVO;
    }

    public String toString() {
        return perfilAcessoVO != null ? perfilAcessoVO.getNome() : super.toString();
    }
}

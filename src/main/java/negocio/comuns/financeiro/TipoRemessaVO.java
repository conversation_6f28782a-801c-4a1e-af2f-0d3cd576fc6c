package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.ArquivoLayoutRemessaEnum;
import negocio.comuns.utilitarias.ConsistirException;


public class TipoRemessaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private String descricao;
    private ArquivoLayoutRemessaEnum arquivoLayoutRemessa;
    private String tipoRemessa;

    @ChaveEstrangeira
    @FKJson
    private TipoRetornoVO tipoRetorno;

    public TipoRemessaVO() {
        super();
        inicializarDados();
    }

    public String getTipoRetorno_Apresentar(){
        return getTipoRetorno().getDescricao();
    }

    public static void validarDados(TipoRemessaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Tipo de Remessa) deve ser informado.");
        }
        if ((obj.getTipoRetorno() == null) ||
                (obj.getTipoRetorno().getCodigo() == 0)) {
            throw new ConsistirException("O campo TIPO DE RETORNO (Tipo de Remessa) deve ser informado.");
        }
        if (obj.getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.NENHUM)) {
            throw new ConsistirException("O campo ARQUIVO DE LAYOUT DA REMESSA (Tipo de Remessa) deve ser informado.");
        }
        if (obj.getTipoRemessa().equals("")) {
            throw new ConsistirException("O campo TIPO DE REMESSA (Tipo de Remessa) deve ser informado.");
        }
    }

    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
        setTipoRemessa(getTipoRemessa().toUpperCase());
    }

    public void inicializarDados() {
        setCodigo(0);
        setDescricao("");
        setArquivoLayoutRemessa(ArquivoLayoutRemessaEnum.NENHUM);
        setTipoRemessa("");
        setTipoRetorno(new TipoRetornoVO());
    }

    public TipoRetornoVO getTipoRetorno() {
        if (tipoRetorno == null) {
            tipoRetorno = new TipoRetornoVO();
        }
        return tipoRetorno;
    }


    public void setTipoRetorno(TipoRetornoVO obj) {
        this.tipoRetorno = obj;
    }

    public String getTipoRemessa() {
        if (tipoRemessa == null) {
            tipoRemessa = "";
        }
        return tipoRemessa;
    }

    public void setTipoRemessa(String tipoRemessa) {
        this.tipoRemessa = tipoRemessa;
    }

    public ArquivoLayoutRemessaEnum getArquivoLayoutRemessa() {
        return arquivoLayoutRemessa;
    }

    public void setArquivoLayoutRemessa(ArquivoLayoutRemessaEnum arquivoLayoutRemessa) {
        this.arquivoLayoutRemessa = arquivoLayoutRemessa;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}

package negocio.comuns.financeiro;

import java.util.Date;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoItemExtratoEnum;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.financeiro.CartaoCredito;
import negocio.facade.jdbc.financeiro.MovPagamento;

/**
 * <AUTHOR>
 */
public class CartaoCreditoTO extends SuperTO {
    private Integer codigo = 0;
    private int recibo = 0;
    private int numeroLote = 0;
    private int operadoraCodigo = 0;
    private String operadora = "";
    private String nomePagador = "";
    private String nomeAlunosDaParcela = "";
    private Date dataCompensacao = null;
    private Date dataLancamento = null;
    private String codigosComposicao= "";
    private Date dataOriginal = null;
    private double valor = 0.0;
    private boolean cartaoEscolhido = false;
    private String autorizacao = "";
    private String nsu = "";
    private int codigoContaContido = 0;
    private String contaContido = "";
    private boolean removido = false;
    private String matricula = "";
    private int codigoPessoa = 0;
    private int movConta = 0;
    private boolean ativo = true;
    private int nrVezes = 0;
    private ExtratoDiarioItemVO itemExtrato;
    private String cpfPagador = "";
    private MovPagamentoVO movPagamentoVO;
    private int nrParcela = 0;
    private String adquirente = "";
    private int adquirenteCod = 0;
    private String title = "";
    private EmpresaVO empresa;
    private String codigosParcelas = "";
    private String vencimentosParcelas = "";
    private String numerosParcelas = "";
    private String nomePessoa = "";
    private FormaPagamentoVO formaPagamentoVO;
    private UsuarioVO usuarioVO;
    @NaoControlarLogAlteracao
    private ContratoVO contratoVO;
    @NaoControlarLogAlteracao
    private String nomeResponsavelRecibo;
    @NaoControlarLogAlteracao
    private boolean antecipacao;
    @NaoControlarLogAlteracao
    private Date dataPgtoOriginalAntesDaAntecipacao;
    @NaoControlarLogAlteracao
    private Double valorDescontadoAntecipacao;
    @NaoControlarLogAlteracao
    private Double taxaCalculadaAntecipacao;
    @NaoControlarLogAlteracao
    private String documentoIntegracaoSesi = "";
    private boolean alterouDataRecebimentoZWAutomaticamente;
    private Date dataPgtoOriginalZWAntesDaAlteracaoAutomatica;


    public boolean getAvisoData(){
        return itemExtrato != null && itemExtrato.getDataPrevistaPagamento() != null 
                && itemExtrato.getSituacao() != null
                && !itemExtrato.getSituacao().equals(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE)
                && !Calendario.getDataComHoraZerada(dataCompensacao).equals(Calendario.getDataComHoraZerada(itemExtrato.getDataPrevistaPagamento()));
    }

    public String getCorLinha(){
        if(itemExtrato != null && itemExtrato.getSituacao() != null){
            return itemExtrato.getSituacao().getCorLinha();
        }
        return "#A90102";
    }
    
    public boolean getAvisoValor(){
        return itemExtrato != null && itemExtrato.getSituacao() != null
                && !itemExtrato.getSituacao().equals(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE)
                && itemExtrato.getValorBruto() != null && !itemExtrato.getValorBruto().equals(getValor());
    }
    
    public ExtratoDiarioItemVO getItemExtrato() {
        return itemExtrato;
    }

    public void setItemExtrato(ExtratoDiarioItemVO itemExtrato) {
        this.itemExtrato = itemExtrato;
    }
    
    public CartaoCreditoTO(){

    }

    public CartaoCreditoTO(int codigo, String codigosComposicao, Date dataCompensacao, Date dataLancamento, Double valor,
                            String nomePagador, String autorizacao, String operadora){
        this.codigo = codigo;
        this.codigosComposicao = codigosComposicao;
        this.dataCompensacao = dataCompensacao;
        this.dataLancamento = dataLancamento;
        this.valor = valor;
        this.nomePagador = nomePagador;
        this.autorizacao = autorizacao;
        this.operadora = operadora;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getOperadora() {
        return operadora;
    }

    public String getOperadoraMin() {
        return operadora == null ? "" : operadora.toLowerCase();
    }

    public void setOperadora(String operadora) {
        this.operadora = operadora;
    }

    public String getNomePagador() {
        return nomePagador;
    }

    public String getNomePagadorMin() {
        return nomePagador == null ? "" : Uteis.getNomeAbreviado(nomePagador.toLowerCase());
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public double getValor() {
        return valor;
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valor);
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public boolean isCartaoEscolhido() {
        return cartaoEscolhido;
    }

    public void setCartaoEscolhido(boolean cartaoEscolhido) {
        this.cartaoEscolhido = cartaoEscolhido;
    }

    public int getNumeroLote() {
        return numeroLote;
    }

    public void setNumeroLote(int numeroLote) {
        this.numeroLote = numeroLote;
    }

    public boolean getApresentarNumeroLote() {
        return numeroLote > 0;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public int getRecibo() {
        return recibo;
    }

    public void setRecibo(int recibo) {
        this.recibo = recibo;
    }

    public Date getDataOriginal() {
        return dataOriginal;
    }

    public void setDataOriginal(Date dataOriginal) {
        this.dataOriginal = dataOriginal;
    }

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof CartaoCreditoTO) {
            CartaoCreditoTO aux = (CartaoCreditoTO) obj;
            return this.codigo == aux.getCodigo();
        }
        return false;
    }

	public void setAutorizacao(String autorizacao) {
		this.autorizacao = autorizacao;
	}

	public String getAutorizacao() {
		return autorizacao;
	}

    public String getNsu() {
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public String getDataCompensacaoApresentar() {
        return Uteis.getData(dataCompensacao);
    }

    public String getDataCompensacaoYYYYMMDD() {
        return Uteis.getDataAplicandoFormatacao(dataCompensacao, "yyyyMMdd");
    }

    public String getDataLancamentoApresentar() {
        return Uteis.getData(dataLancamento);
    }

    public String getDataLancamentoYYYYMMDD() {
        return Uteis.getDataAplicandoFormatacao(dataLancamento, "yyyyMMdd");
    }

    public String getNumeroLoteApresentar(){
    	return numeroLote == 0 ? "" : String.valueOf(numeroLote);
    }

	public void setCodigosComposicao(String codigosComposicao) {
		this.codigosComposicao = codigosComposicao;
	}

	public String getCodigosComposicao() {
		return codigosComposicao;
	}

	public void setCodigoContaContido(int codigoContaContido) {
		this.codigoContaContido = codigoContaContido;
	}

	public int getCodigoContaContido() {
		return codigoContaContido;
	}

	public void setContaContido(String contaContido) {
		this.contaContido = contaContido;
	}

	public String getContaContido() {
		return contaContido;
	}

	public String getContaContidoMin() {
		return contaContido == null ? "" : contaContido.toLowerCase();
	}

	public void setRemovido(boolean removido) {
		this.removido = removido;
	}

	public boolean isRemovido() {
		return removido;
	}

	public String getColor(){
		return removido ? "gray" : "blue";
	}

    public String getPrimeiroNomePagador() {
        return Uteis.getPrimeiroNome(nomePagador);
    }

	public void setMatricula(String matricula) {
		this.matricula = matricula;
	}

	public String getMatricula() {
		return matricula;
	}

    public Long getMatriculaRelatorio() {
        return UteisValidacao.emptyString(matricula) ? 0L : Long.valueOf(matricula);
    }

	public void setCodigoPessoa(int codigoPessoa) {
		this.codigoPessoa = codigoPessoa;
	}

	public int getCodigoPessoa() {
		return codigoPessoa;
	}

	public void setMovConta(int movConta) {
		this.movConta = movConta;
	}

	public int getMovConta() {
		return movConta;
	}

	public boolean getFornecedor(){
		return UteisValidacao.emptyString(matricula);
	}

    public RecebivelTO getRecebivelTO() throws Exception {
        RecebivelTO recebivelTO = new RecebivelTO();
        recebivelTO.setCodigo(this.getCodigo());
        recebivelTO.setCodigoUnico("CC-"+this.getCodigo());
        recebivelTO.setRecibo(this.getRecibo());
        recebivelTO.setNumeroLote(this.getNumeroLote() == 0 ? null : this.getNumeroLote());
        recebivelTO.setOperadora(this.getOperadora());
        recebivelTO.setNomePagador(this.getNomePagador());
        recebivelTO.setNomePessoa(this.getNomePessoa());
        recebivelTO.setCpfPagador(this.getCpfPagador());
        recebivelTO.setDataCompensacao(this.getDataCompensacao());
        recebivelTO.setDataLancamento(this.getDataLancamento());
        recebivelTO.setCodigosComposicao(this.getCodigosComposicao());
        recebivelTO.setDataOriginal(this.getDataOriginal());
        recebivelTO.setValor(Uteis.arredondarForcando2CasasDecimais(this.getValor()));
        recebivelTO.setEmpresa(this.getEmpresa());
        if (this.getMovPagamentoVO() != null && this.getMovPagamentoVO().getPagamentoMovParcelaVOs() != null && this.getMovPagamentoVO().getPagamentoMovParcelaVOs().size() > 0) {
            recebivelTO.setPagamentoMovParcelaVOs(this.getMovPagamentoVO().getPagamentoMovParcelaVOs());
        }
        //cartaoEscolhido?
        recebivelTO.setAutorizacao(this.getAutorizacao());
        recebivelTO.setCodigoContaContido(this.getCodigoContaContido() == 0 ? null : this.getCodigoContaContido());
        recebivelTO.setContaContido(this.getContaContido());
        recebivelTO.setNsu(this.getNsu());
        //removido?
        recebivelTO.setMatricula(this.getMatricula());
        recebivelTO.setCodigoPessoa(this.getCodigoPessoa());
        recebivelTO.setMovConta(this.getMovConta() == 0 ? null : this.getMovConta());
        recebivelTO.setAntecipacao(this.isAntecipacao());
        if (recebivelTO.isAntecipacao()) {
            recebivelTO.setAntecipacaoApresentar("Sim");
        } else {
            recebivelTO.setAntecipacaoApresentar("Não");
        }
        if (this.getMovPagamentoVO() != null && !UteisValidacao.emptyString(this.getMovPagamentoVO().getObservacao())) {
            recebivelTO.setObservacao(this.getMovPagamentoVO().getObservacao());
        }

        CartaoCredito cartaoCredito = new CartaoCredito();
        CartaoCreditoVO cartaoCreditoVO = cartaoCredito.consultarPorChavePrimaria(getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
         boolean emContaCorrenteAluno = false;
        if(!UteisValidacao.emptyString(cartaoCreditoVO.getComposicao())){
            String produtosPagosComposicao = cartaoCreditoVO.getProdutosPagos();
            String[] cartoesComposicao = cartaoCreditoVO.getComposicao().split(",");
            for(String cart : cartoesComposicao){
                cart = cartaoCredito.obterProdutosPagos(Integer.parseInt(cart));
                if(!UteisValidacao.emptyString(cart)){
                    produtosPagosComposicao += cart; 
                } else {
                    emContaCorrenteAluno = true;
                }
                
            }
            cartaoCreditoVO.setProdutosPagos(produtosPagosComposicao);
        }
        if(UteisValidacao.emptyString(cartaoCreditoVO .getProdutosPagos())){
            recebivelTO.setProdutos("CONTA CORRENTE");
            recebivelTO.setPlanoContrato("");
            recebivelTO.setModalidade("");
        } else {
            MovPagamento movPagamento = new MovPagamento();
            String produtosPlanos = movPagamento.consultarDescricaoProdutosPagosPlano(cartaoCreditoVO.getProdutosPagos());
            String[] valores = produtosPlanos.split("\\?");
            recebivelTO.setProdutos(valores.length > 0 ? valores[0]+(emContaCorrenteAluno ? ",CONTA CORRENTE":"") : "");
            recebivelTO.setPlanoContrato(valores.length > 1 ? valores[1] : "");
            recebivelTO.setModalidade(valores.length > 2 ? valores[2] : "");
        }

        recebivelTO.setEmpresa(this.getEmpresa());
        recebivelTO.setVencimentosParcelas(this.getVencimentosParcelas());
        recebivelTO.setNumerosParcelas(this.getNumerosParcelas());
        recebivelTO.setCodigosParcelas(this.getCodigosParcelas());
        recebivelTO.setUsuarioVO(this.getUsuarioVO());
        if (this.getMovPagamentoVO() != null && this.getMovPagamentoVO().getFormaPagamento() != null) {
            recebivelTO.setFormaPagamento(this.getMovPagamentoVO().getFormaPagamento());
        }
        return recebivelTO;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }


    public int getNrVezes() {
        return nrVezes;
    }

    public void setNrVezes(int nrVezes) {
        this.nrVezes = nrVezes;
    }
    
    public String getDataOriginalApresentar() {
        if (dataOriginal == null) {
            return Uteis.getData(dataCompensacao);
        } else {
            return Uteis.getData(dataOriginal);
        }

    }

    public String getDataOriginalYYYYMMDD() {
        if (dataOriginal == null) {
            return Uteis.getDataAplicandoFormatacao(dataCompensacao, "yyyyMMdd");
        } else {
            return Uteis.getDataAplicandoFormatacao(dataOriginal, "yyyyMMdd");
        }

    }

    public String getTitle(){
        if(UteisValidacao.emptyString(title)){
            StringBuilder tit = new StringBuilder();
            tit.append(UteisValidacao.emptyString(adquirente) ? "" : ("<b>Adquirente:</b> "+adquirente+"<br/>"));
            tit.append(UteisValidacao.emptyString(operadora) ? "" : ("<b>Operadora:</b> "+operadora+"<br/>"));
            tit.append(UteisValidacao.emptyNumber(nrVezes) ? "" : ("<b>Número de vezes:</b> "+nrVezes));
            title = tit.toString();
        }
        return title;

    }

    public boolean isExibirInfoPessoaContratoDiferenteDoPagador() {
        try {
            if (getMovPagamentoVO() != null &&
                    getMovPagamentoVO().getPessoaVODoContratoDoMovPagamento() != null &&
                    !UteisValidacao.emptyString(getMovPagamentoVO().getPessoaVODoContratoDoMovPagamento().getNome())) {
                return true;
            }
        } catch (Exception ex) {
            return false;
        }
        return false;
    }

    public String getTitleInfoPessoaContratoDiferenteDoPagador() {
        try {
            StringBuilder sb = new StringBuilder();

            // preencher nome do aluno
            sb.append("Pagamento pertence a: ").append(getMovPagamentoVO().getPessoaVODoContratoDoMovPagamento().getNome());

            // tentar apresentar também o cód. de matrícula
            if (getMovPagamentoVO() != null &&
                    getMovPagamentoVO().getReciboPagamento() != null &&
                    getMovPagamentoVO().getReciboPagamento().getContrato() != null &&
                    getMovPagamentoVO().getReciboPagamento().getContrato().getCliente() != null &&
                    !UteisValidacao.emptyNumber(getMovPagamentoVO().getReciboPagamento().getContrato().getCliente().getCodigo())) {
                sb.append( " | MAT. " + getMovPagamentoVO().getCodMatriculaDoContratoDoMovPagamento());
            }
            return sb.toString();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getCpfPagador() {
        return cpfPagador;
    }

    public void setCpfPagador(String cpfPagador) {
        this.cpfPagador = cpfPagador;
    }
    
    public String getObterTodosCartoesComposicao() {
        String codigos = "" + this.codigo;
        if (this.codigosComposicao != null && !this.codigosComposicao.equals("")) {
            codigos += "," +  this.codigosComposicao;
        }
        return codigos;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getAdquirente() {
        return adquirente;
    }

    public void setAdquirente(String adquirente) {
        this.adquirente = adquirente;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public int getNrParcela() {
        return nrParcela;
    }

    public void setNrParcela(int nrParcela) {
        this.nrParcela = nrParcela;
    }
    
    public String getNrParcelas_apresentar(){
        String parcelaApresentar = this.nrParcela + "/" + nrVezes;
        if(parcelaApresentar.equals("1/1")){
            return "1";
        }
        return parcelaApresentar;
    }
    
    public int getAdquirenteCod() {
        return adquirenteCod;
    }

    public void setAdquirenteCod(int adquirenteCod) {
        this.adquirenteCod = adquirenteCod;
    }

    public int getOperadoraCodigo() {
        return operadoraCodigo;
    }

    public void setOperadoraCodigo(int operadoraCodigo) {
        this.operadoraCodigo = operadoraCodigo;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public EmpresaVO getEmpresa() {
        if(empresa == null){
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setCodigosParcelas(String codigosParcelas) {
        this.codigosParcelas = codigosParcelas;
    }

    public String getCodigosParcelas() {
        return codigosParcelas;
    }

    public void setVencimentosParcelas(String vencimentosParcelas) {
        this.vencimentosParcelas = vencimentosParcelas;
    }

    public String getVencimentosParcelas() {
        return vencimentosParcelas;
    }

    public void setNumerosParcelas(String numerosParcelas) {
        this.numerosParcelas = numerosParcelas;
    }

    public String getNumerosParcelas() {
        return numerosParcelas;
    }

    public FormaPagamentoVO getFormaPagamentoVO() {
        return formaPagamentoVO;
    }

    public void setFormaPagamentoVO(FormaPagamentoVO formaPagamentoVO) {
        this.formaPagamentoVO = formaPagamentoVO;
    }

    public String getOperadora_Ordenar() {
        if(this.operadora == null){
            return "";
        }
        return this.operadora;
    }

    public boolean getConsumidor(){
        return UteisValidacao.emptyString(matricula);
    }

    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public String getUsuarioResponsavelApresentar() {
        return (!UteisValidacao.emptyString(this.getNomeResponsavelRecibo())) ? this.getNomeResponsavelRecibo() : "";
    }
    public Integer getReciboPagamentoApresentar() {
        return (!UteisValidacao.emptyNumber(this.getRecibo())) ? this.getRecibo() : null;
    }

    public Integer getContratoReciboApresentar() {
        return (this.getContratoVO() != null) ? this.getContratoVO().getCodigo() : null;
    }

    public ContratoVO getContratoVO() {
        if (contratoVO == null) {
            return new ContratoVO();
        }
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public String getNomeResponsavelRecibo() {
        if (UteisValidacao.emptyString(nomeResponsavelRecibo)) {
            return "";
        }
        return nomeResponsavelRecibo;
    }

    public void setNomeResponsavelRecibo(String nomeResponsavelRecibo) {
        this.nomeResponsavelRecibo = nomeResponsavelRecibo;
    }

    public boolean isAntecipacao() {
        return antecipacao;
    }

    public void setAntecipacao(boolean antecipacao) {
        this.antecipacao = antecipacao;
    }

    public void setDataPgtoOriginalAntesDaAntecipacao(Date dataPgtoOriginalAntesDaAntecipacao) {
        this.dataPgtoOriginalAntesDaAntecipacao = dataPgtoOriginalAntesDaAntecipacao;
    }

    public String getDataPgtoOriginalAntesDaAntecipacaoApresentar() {
        if (dataPgtoOriginalAntesDaAntecipacao == null) {
            return "";
        }
        return Uteis.getDataAplicandoFormatacao(dataPgtoOriginalAntesDaAntecipacao, "dd/MM/yyyy");
    }

    public Date getDataPgtoOriginalAntesDaAntecipacao() {
        return dataPgtoOriginalAntesDaAntecipacao;
    }

    public Double getValorDescontadoAntecipacao() {
        return valorDescontadoAntecipacao;
    }

    public void setValorDescontadoAntecipacao(Double valorDescontadoAntecipacao) {
        this.valorDescontadoAntecipacao = valorDescontadoAntecipacao;
    }

    public Double getTaxaCalculadaAntecipacao() {
        return taxaCalculadaAntecipacao;
    }

    public void setTaxaCalculadaAntecipacao(Double taxaCalculadaAntecipacao) {
        this.taxaCalculadaAntecipacao = taxaCalculadaAntecipacao;
    }

    public String getNomeAlunosDaParcela() {
        return nomeAlunosDaParcela;
    }

    public void setNomeAlunosDaParcela(String nomeAlunosDaParcela) {
        this.nomeAlunosDaParcela = nomeAlunosDaParcela;
    }

    public String getDocumentoIntegracaoSesi() {
        return documentoIntegracaoSesi;
    }

    public void setDocumentoIntegracaoSesi(String documentoIntegracaoSesi) {
        this.documentoIntegracaoSesi = documentoIntegracaoSesi;
    }

    public boolean isAlterouDataRecebimentoZWAutomaticamente() {
        return alterouDataRecebimentoZWAutomaticamente;
    }

    public void setAlterouDataRecebimentoZWAutomaticamente(boolean alterouDataRecebimentoZWAutomaticamente) {
        this.alterouDataRecebimentoZWAutomaticamente = alterouDataRecebimentoZWAutomaticamente;
    }

    public Date getDataPgtoOriginalZWAntesDaAlteracaoAutomatica() {
        return dataPgtoOriginalZWAntesDaAlteracaoAutomatica;
    }

    public void setDataPgtoOriginalZWAntesDaAlteracaoAutomatica(Date dataPgtoOriginalZWAntesDaAlteracaoAutomatica) {
        this.dataPgtoOriginalZWAntesDaAlteracaoAutomatica = dataPgtoOriginalZWAntesDaAlteracaoAutomatica;
    }

    public String getDataPgtoOriginalZWAntesDaAlteracaoAutomaticaApresentar() {
        if (dataPgtoOriginalZWAntesDaAlteracaoAutomatica == null) {
            return "";
        }
        return Uteis.getDataAplicandoFormatacao(dataPgtoOriginalZWAntesDaAlteracaoAutomatica, "dd/MM/yyyy");
    }
}

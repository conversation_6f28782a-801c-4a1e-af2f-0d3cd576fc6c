package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.ConsistirException;
import org.json.JSONObject;
import servicos.impl.geoitd.enums.BandeirasGeoitdEnum;

import java.util.List;

/**
 * Reponsável por manter os dados da entidade OperadoraCartao. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class OperadoraCartaoVO extends SuperVO {

    private Integer codigo;
    private Integer codigoOperadora;
    private String descricao;
    private boolean credito;
    private int qtdeMaxParcelas;
    private Double taxa;
    private OperadorasExternasPagamentoDigitalEnum codigoIntegracao = OperadorasExternasPagamentoDigitalEnum.OPERADORA_Nenhum;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoAPF;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoVindi;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoCielo;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoDebito;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoERede;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoGetNet;
    private TipoDebitoOnlineEnum tipoDebitoOnline;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoMaxiPago;
    private  boolean ativo = false;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoFitnessCard;
    private BandeirasCapptaEnum bandeiraCappta;
    private String codBandeiraGeoitd;
    private BandeirasGeoitdEnum bandeirasGeoitd;
    private Boolean isGeoitd;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoStoneOnline;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoMundiPagg;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoPagarMe;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoPagBank;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoStripe;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoPagoLivre;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoFacilitePay;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoPinBank;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoOnePayment;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoCeopag;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoDCCCaixaOnline;
    private OperadorasExternasAprovaFacilEnum codigoIntegracaoStoneOnlineV5;
    private boolean padraoRecebimento = false;



    /**
     * Construtor padrão da classe <code>OperadoraCartao</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public OperadoraCartaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da classe <code>OperadoraCartaoVO</code>.
     */
    public static void validarUnicidade(List<OperadoraCartaoVO> lista, OperadoraCartaoVO obj) throws ConsistirException {
        for (OperadoraCartaoVO repetido : lista) {
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>OperadoraCartaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(OperadoraCartaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo Descricao (Operadora Cartão)  deve ser informado");
        }
        if (!obj.isCredito())
            obj.setQtdeMaxParcelas(1); // qdo é debito é a vista
        if (obj.getQtdeMaxParcelas() <= 0) {
            throw new ConsistirException("Quantidade máxima de parcelas não pode ser menor ou igual a zero.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setDescricao(getDescricao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(null);
        setDescricao("");
        setCodigoOperadora(0);
        setQtdeMaxParcelas(0);
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }

    public String getDescricaoMin() {
        return getDescricao().toLowerCase();
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigoOperadora() {
        if (codigoOperadora == null) {
            codigoOperadora = 0;
        }
        return (codigoOperadora);
    }

    public void setCodigoOperadora(Integer codigoOperadora) {
        this.codigoOperadora = codigoOperadora;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public OperadorasExternasPagamentoDigitalEnum getCodigoIntegracao() {
        return codigoIntegracao;
    }

    public void setCodigoIntegracao(OperadorasExternasPagamentoDigitalEnum codigoIntegracao) {
        this.codigoIntegracao = codigoIntegracao;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoAPF() {
        return codigoIntegracaoAPF;
    }

    public void setCodigoIntegracaoAPF(OperadorasExternasAprovaFacilEnum codigoIntegracaoAPF) {
        this.codigoIntegracaoAPF = codigoIntegracaoAPF;
    }

    public boolean isCredito() {
        return credito;
    }

    public void setCredito(boolean credito) {
        this.credito = credito;
    }

    public int getQtdeMaxParcelas() {
        return qtdeMaxParcelas;
    }

    public void setQtdeMaxParcelas(int qtdeMaxParcelas) {
        this.qtdeMaxParcelas = qtdeMaxParcelas;
    }

    public Double getTaxa() {
        if (taxa == null) {
            taxa = 0d;
        }
        return taxa;
    }

    public void setTaxa(Double taxa) {
        this.taxa = taxa;
    }

    public OperadoraCartaoWS toWS() {
        OperadoraCartaoWS operadoraCartaoWS = new OperadoraCartaoWS();
        operadoraCartaoWS.setCodigo(this.getCodigo());
        operadoraCartaoWS.setCodigoOperadora(this.getCodigoOperadora());
        operadoraCartaoWS.setDescricao(this.getDescricao());
        return operadoraCartaoWS;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoVindi() {
        return codigoIntegracaoVindi;
    }

    public void setCodigoIntegracaoVindi(OperadorasExternasAprovaFacilEnum codigoIntegracaoVindi) {
        this.codigoIntegracaoVindi = codigoIntegracaoVindi;
    }

    public OperadorasExternasAprovaFacilEnum getOperadoraIntegracao(){
        if (getCodigoIntegracaoAPF() != null) {
            return getCodigoIntegracaoAPF();
        } else if (getCodigoIntegracaoStoneOnlineV5() != null) {
            return getCodigoIntegracaoStoneOnlineV5();
        } else if (getCodigoIntegracaoVindi() != null) {
            return getCodigoIntegracaoVindi();
        } else if (getCodigoIntegracaoCielo() != null) {
            return getCodigoIntegracaoCielo();
        } else if (getCodigoIntegracaoDebito() != null) {
            return getCodigoIntegracaoDebito();
        } else if (getCodigoIntegracaoMaxiPago() != null) {
            return getCodigoIntegracaoMaxiPago();
        } else if (getCodigoIntegracaoERede() != null) {
            return getCodigoIntegracaoERede();
        } else if (getCodigoIntegracaoFitnessCard() != null) {
            return getCodigoIntegracaoFitnessCard();
        } else if (getCodigoIntegracaoGetNet() != null) {
            return getCodigoIntegracaoGetNet();
        } else if (getCodigoIntegracaoStoneOnline() != null) {
            return getCodigoIntegracaoStoneOnline();
        } else if (getCodigoIntegracaoMundiPagg() != null) {
            return getCodigoIntegracaoMundiPagg();
        } else if (getCodigoIntegracaoPagarMe() != null) {
            return getCodigoIntegracaoPagarMe();
        } else if (getCodigoIntegracaoStripe() != null) {
            return getCodigoIntegracaoStripe();
        } else if (getCodigoIntegracaoPagoLivre() != null) {
            return getCodigoIntegracaoPagoLivre();
        } else if (getCodigoIntegracaoFacilitePay() != null) {
            return getCodigoIntegracaoFacilitePay();
        } else if (getCodigoIntegracaoPinBank() != null) {
            return getCodigoIntegracaoPinBank();
        } else if (getCodigoIntegracaoOnePayment() != null) {
            return getCodigoIntegracaoOnePayment();
        } else if (getCodigoIntegracaoCeopag() != null) {
            return getCodigoIntegracaoCeopag();
        } else if (getCodigoIntegracaoPagBank() != null) {
            return getCodigoIntegracaoPagBank();
        } else if (getCodigoIntegracaoDCCCaixaOnline() != null) {
            return getCodigoIntegracaoDCCCaixaOnline();
        } else {
            return null;
        }
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoCielo() {
        return codigoIntegracaoCielo;
}

    public void setCodigoIntegracaoCielo(OperadorasExternasAprovaFacilEnum codigoIntegracaoCielo) {
        this.codigoIntegracaoCielo = codigoIntegracaoCielo;
    }

    public TipoDebitoOnlineEnum getTipoDebitoOnline() {
        return tipoDebitoOnline;
    }

    public void setTipoDebitoOnline(TipoDebitoOnlineEnum tipoDebitoOnline) {
        this.tipoDebitoOnline = tipoDebitoOnline;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoERede() {
        return codigoIntegracaoERede;
    }

    public void setCodigoIntegracaoERede(OperadorasExternasAprovaFacilEnum codigoIntegracaoERede) {
        this.codigoIntegracaoERede = codigoIntegracaoERede;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoDebito() {
        return codigoIntegracaoDebito;
    }

    public void setCodigoIntegracaoDebito(OperadorasExternasAprovaFacilEnum codigoIntegracaoDebito) {
        this.codigoIntegracaoDebito = codigoIntegracaoDebito;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoMaxiPago() {
        return codigoIntegracaoMaxiPago;
    }

    public void setCodigoIntegracaoMaxiPago(OperadorasExternasAprovaFacilEnum codigoIntegracaoMaxiPago) {
        this.codigoIntegracaoMaxiPago = codigoIntegracaoMaxiPago;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoStripe() {
        return codigoIntegracaoStripe;
    }

    public void setCodigoIntegracaoStripe(OperadorasExternasAprovaFacilEnum codigoIntegracaoStripe) {
        this.codigoIntegracaoStripe = codigoIntegracaoStripe;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoPagoLivre() {
        return codigoIntegracaoPagoLivre;
    }

    public void setCodigoIntegracaoPagoLivre(OperadorasExternasAprovaFacilEnum codigoIntegracaoPagoLivre) {
        this.codigoIntegracaoPagoLivre = codigoIntegracaoPagoLivre;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoPinBank() {
        return codigoIntegracaoPinBank;
    }

    public void setCodigoIntegracaoPinBank(OperadorasExternasAprovaFacilEnum codigoIntegracaoPinBank) {
        this.codigoIntegracaoPinBank = codigoIntegracaoPinBank;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoOnePayment() {
        return codigoIntegracaoOnePayment;
    }

    public void setCodigoIntegracaoOnePayment(OperadorasExternasAprovaFacilEnum codigoIntegracaoOnePayment) {
        this.codigoIntegracaoOnePayment = codigoIntegracaoOnePayment;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public String getSituacao_Apresentar() {
        return isAtivo() ? "ATIVO" : "INATIVO";
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoFitnessCard() {
        return codigoIntegracaoFitnessCard;
    }

    public void setCodigoIntegracaoFitnessCard(OperadorasExternasAprovaFacilEnum codigoIntegracaoFitnessCard) {
        this.codigoIntegracaoFitnessCard = codigoIntegracaoFitnessCard;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoGetNet() {
        return codigoIntegracaoGetNet;
    }

    public void setCodigoIntegracaoGetNet(OperadorasExternasAprovaFacilEnum codigoIntegracaoGetNet) {
        this.codigoIntegracaoGetNet = codigoIntegracaoGetNet;
    }

    public BandeirasCapptaEnum getBandeiraCappta() {
        return bandeiraCappta;
    }

    public void setBandeiraCappta(BandeirasCapptaEnum bandeiraCappta) {
        this.bandeiraCappta = bandeiraCappta;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoStoneOnline() {
        return codigoIntegracaoStoneOnline;
    }

    public void setCodigoIntegracaoStoneOnline(OperadorasExternasAprovaFacilEnum codigoIntegracaoStoneOnline) {
        this.codigoIntegracaoStoneOnline = codigoIntegracaoStoneOnline;
    }

    public BandeirasGeoitdEnum getBandeirasGeoitd() {
        return bandeirasGeoitd;
    }

    public void setBandeirasGeoitd(BandeirasGeoitdEnum bandeirasGeoitd) {
        this.bandeirasGeoitd = bandeirasGeoitd;
    }

    public Boolean getGeoitd() {
        return (isGeoitd == null ? false : this.isGeoitd);
    }

    public void setGeoitd(Boolean geoitd) {
        isGeoitd = geoitd;
    }

    public String getCodBandeiraGeoitd() {
        return (codBandeiraGeoitd == null ? "0" : this.codBandeiraGeoitd);
    }

    public void setCodBandeiraGeoitd(String codBandeiraGeoitd) {
        this.codBandeiraGeoitd = codBandeiraGeoitd;
    }

    public String getNomeOperadoraResponsavel() {
        if (getCodigoIntegracaoAPF() != null) {
            return getCodigoIntegracaoAPF().getDescricao();
        } else if (getCodigoIntegracao() != null && !getCodigoIntegracao().equals(OperadorasExternasPagamentoDigitalEnum.OPERADORA_Nenhum)) {
            return getCodigoIntegracao().getDescricao();
        } else if (getCodigoIntegracaoVindi() != null) {
            return getCodigoIntegracaoVindi().getDescricao();
        } else if (getCodigoIntegracaoCielo() != null) {
            return getCodigoIntegracaoCielo().getDescricao();
        } else if (getCodigoIntegracaoDebito() != null) {
            return getCodigoIntegracaoDebito().getDescricao();
        } else if (getCodigoIntegracaoERede() != null) {
            return getCodigoIntegracaoERede().getDescricao();
        } else if (getCodigoIntegracaoMaxiPago() != null) {
            return getCodigoIntegracaoMaxiPago().getDescricao();
        } else if (getCodigoIntegracaoFitnessCard() != null) {
            return getCodigoIntegracaoFitnessCard().getDescricao();
        } else if (getCodigoIntegracaoStoneOnline() != null) {
            return getCodigoIntegracaoStoneOnline().getDescricao();
        } else if (getCodigoIntegracaoMundiPagg() != null) {
            return getCodigoIntegracaoMundiPagg().getDescricao();
        } else if (getCodigoIntegracaoPagarMe() != null) {
            return getCodigoIntegracaoPagarMe().getDescricao();
        } else if (getCodigoIntegracaoStripe() != null) {
            return getCodigoIntegracaoStripe().getDescricao();
        } else if (getCodigoIntegracaoPagoLivre() != null) {
            return getCodigoIntegracaoPagoLivre().getDescricao();
        } else if (getCodigoIntegracaoFacilitePay() != null) {
            return getCodigoIntegracaoFacilitePay().getDescricao();
        } else if (getCodigoIntegracaoPinBank() != null) {
            return getCodigoIntegracaoPinBank().getDescricao();
        } else if (getCodigoIntegracaoOnePayment() != null) {
            return getCodigoIntegracaoOnePayment().getDescricao();
        } else if (getCodigoIntegracaoCeopag() != null) {
            return getCodigoIntegracaoCeopag().getDescricao();
        } else if (getBandeiraCappta() != null) {
            return getBandeiraCappta().getDescricao();
        } else {
            return getDescricao();
        }
    }

    public TipoTransacaoEnum getTipoTransacaoEnum(boolean usaAprovaFacil) {
        if (usaAprovaFacil && this.getCodigoIntegracaoAPF() != null) {
            return TipoTransacaoEnum.AprovaFacilCB;
        } else if (this.getCodigoIntegracaoStoneOnlineV5() != null) {
            return TipoTransacaoEnum.DCC_STONE_ONLINE_V5;
        } else if (this.getCodigoIntegracaoVindi() != null) {
            return TipoTransacaoEnum.VINDI;
        } else if (this.getCodigoIntegracaoCielo() != null) {
            return TipoTransacaoEnum.CIELO_ONLINE;
        } else if (this.getCodigoIntegracaoMaxiPago() != null) {
            return TipoTransacaoEnum.MAXIPAGO;
        } else if (this.getCodigoIntegracaoERede() != null) {
            return TipoTransacaoEnum.E_REDE;
        } else if (this.getCodigoIntegracaoFitnessCard() != null) {
            return TipoTransacaoEnum.FITNESS_CARD;
        } else if (this.getCodigoIntegracaoGetNet() != null) {
            return TipoTransacaoEnum.GETNET_ONLINE;
        } else if (this.getCodigoIntegracaoStoneOnline() != null) {
            return TipoTransacaoEnum.STONE_ONLINE;
        } else if (this.getCodigoIntegracaoMundiPagg() != null) {
            return TipoTransacaoEnum.MUNDIPAGG;
        } else if (this.getCodigoIntegracaoPagarMe() != null) {
            return TipoTransacaoEnum.PAGAR_ME;
        } else if (this.getCodigoIntegracaoPagBank() != null) {
            return TipoTransacaoEnum.PAGBANK;
        } else if (this.getCodigoIntegracaoStripe() != null) {
            return TipoTransacaoEnum.STRIPE;
        } else if (this.getCodigoIntegracaoPagoLivre() != null) {
            return TipoTransacaoEnum.PAGOLIVRE;
        } else if (this.getCodigoIntegracaoFacilitePay() != null) {
            return TipoTransacaoEnum.FACILITEPAY;
        } else if (this.getCodigoIntegracaoPinBank() != null) {
            return TipoTransacaoEnum.PINBANK;
        } else if (this.getCodigoIntegracaoOnePayment() != null) {
            return TipoTransacaoEnum.ONE_PAYMENT;
        } else if (this.getCodigoIntegracaoCeopag() != null) {
            return TipoTransacaoEnum.CEOPAG;
        } else if (this.getCodigoIntegracaoDCCCaixaOnline() != null) {
            return TipoTransacaoEnum.DCC_CAIXA_ONLINE;
        } else {
            return null;
        }
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoMundiPagg() {
        return codigoIntegracaoMundiPagg;
    }

    public void setCodigoIntegracaoMundiPagg(OperadorasExternasAprovaFacilEnum codigoIntegracaoMundiPagg) {
        this.codigoIntegracaoMundiPagg = codigoIntegracaoMundiPagg;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoPagarMe() {
        return codigoIntegracaoPagarMe;
    }

    public void setCodigoIntegracaoPagarMe(OperadorasExternasAprovaFacilEnum codigoIntegracaoPagarMe) {
        this.codigoIntegracaoPagarMe = codigoIntegracaoPagarMe;
    }

    public boolean isPadraoRecebimento() {
        return padraoRecebimento;
    }

    public void setPadraoRecebimento(boolean padraoRecebimento) {
        this.padraoRecebimento = padraoRecebimento;
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("codigo", codigo);
        json.put("codigoOperadora", codigoOperadora);
        json.put("descricao", descricao);
        json.put("credito", credito);
        json.put("qtdeMaxParcelas", qtdeMaxParcelas);

        return json;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoFacilitePay() {
        return codigoIntegracaoFacilitePay;
    }

    public void setCodigoIntegracaoFacilitePay(OperadorasExternasAprovaFacilEnum codigoIntegracaoFacilitePay) {
        this.codigoIntegracaoFacilitePay = codigoIntegracaoFacilitePay;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoCeopag() {
        return codigoIntegracaoCeopag;
    }

    public void setCodigoIntegracaoCeopag(OperadorasExternasAprovaFacilEnum codigoIntegracaoCeopag) {
        this.codigoIntegracaoCeopag = codigoIntegracaoCeopag;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoDCCCaixaOnline() {
        return codigoIntegracaoDCCCaixaOnline;
    }

    public void setCodigoIntegracaoDCCCaixaOnline(OperadorasExternasAprovaFacilEnum codigoIntegracaoDCCCaixaOnline) {
        this.codigoIntegracaoDCCCaixaOnline = codigoIntegracaoDCCCaixaOnline;
    }


    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoStoneOnlineV5() {
        return codigoIntegracaoStoneOnlineV5;
    }

    public void setCodigoIntegracaoStoneOnlineV5(OperadorasExternasAprovaFacilEnum codigoIntegracaoStoneOnlineV5) {
        this.codigoIntegracaoStoneOnlineV5 = codigoIntegracaoStoneOnlineV5;
    }

    public OperadorasExternasAprovaFacilEnum getCodigoIntegracaoPagBank() {
        return codigoIntegracaoPagBank;
    }

    public void setCodigoIntegracaoPagBank(OperadorasExternasAprovaFacilEnum codigoIntegracaoPagBank) {
        this.codigoIntegracaoPagBank = codigoIntegracaoPagBank;
    }
}

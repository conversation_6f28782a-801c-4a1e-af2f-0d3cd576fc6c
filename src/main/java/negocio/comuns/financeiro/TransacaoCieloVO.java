package negocio.comuns.financeiro;

import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.cieloecommerce.CieloECommerceRetornoEnum;

/*
 * Created by <PERSON><PERSON> on 31/08/2017.
 */
public class TransacaoCieloVO extends TransacaoVO {

    public String getValorCodigoExterno() throws Exception {
        return getCodigoExterno();
    }

    public String getValorCartaoMascarado() throws Exception {
        if (getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE)) {
            JSONObject obj = new JSONObject(getParamsEnvio());
            JSONObject payment = obj.getJSONObject("Payment");
            JSONObject creditCard = payment.getJSONObject("CreditCard");
            return APF.getCartaoMascarado(creditCard.getString("CardNumber"));
        } else {
            JSONObject obj = new JSONObject(getParamsResposta());
            JSONObject payment = obj.getJSONObject("Payment");
            JSONObject creditCard = new JSONObject();
            if (getTipo().equals(TipoTransacaoEnum.CIELO_DEBITO_ONLINE)) {
                creditCard = payment.getJSONObject("DebitCard");
            }
            return APF.getCartaoMascarado(creditCard.getString("CardNumber"));
        }
    }

    public String getValorUltimaTransacaoAprovada() throws Exception {
        return getValorAtributoResposta(APF.Transacao);
    }

    public String getResultadoRequisicao() throws Exception {
        try {
            JSONObject json = new JSONObject(getPayment());
            String returnMessage = json.optString("ReturnMessage");
            if (returnMessage.contains("AnonType_nome-portadorDadosCartao")) {
                return "Nome do titular não informado.";
            }
            if (returnMessage.contains("XML informado não é valido")) {
                return "Caractere inválido na transação. Verifique os campos texto, como nome do cliente e nome da empresa.";
            }
        } catch (Exception ignored) {
        }

        if (getCodErroExterno().equalsIgnoreCase("002")) {
            try {
                JSONObject json = new JSONObject(getPayment());
                String returnMessage = json.optString("ReturnMessage");
                if (!UteisValidacao.emptyString(returnMessage)) {
                    if (returnMessage.contains("Credenciais Invalidas")) {
                        return "Credenciais Inválidas ou bloqueadas. Necessário entrar em contato com a Cielo (401 SC_UNAUTHORIZED)";
                    }
                    return returnMessage;
                }
            } catch (Exception ignored) {
            }
        }

        CieloECommerceRetornoEnum cieloEnum = CieloECommerceRetornoEnum.valueOff(getCodErroExterno());
        if (cieloEnum.equals(CieloECommerceRetornoEnum.StatusNENHUM)) {
            try {
                JSONArray array = new JSONArray(getParamsResposta());
                if (array.length() > 0) {
                    JSONObject json = array.getJSONObject(0);
                    String message =  json.optString("Message");
                    if (!UteisValidacao.emptyString(message)) {
                        if (message.equalsIgnoreCase("MERCHANTKEY IS INVALID")) {
                            return "MerchantKey Inválido";
                        }
                        if (message.equalsIgnoreCase("MERCHANTID IS INVALID")) {
                            return "MerchantId Inválido";
                        }
                        if (message.equalsIgnoreCase("MERCHANT IS BLOCKED")) {
                            return "Entre em contato com a Cielo e informe que não está conseguindo realizar a captura das transações via Ecommerce por esse motivo: <b>Merchant está bloqueado.</b>";
                        }
                        return message;
                    }
                }
            } catch (Exception ignored) {
            }

            try {
                JSONObject json = new JSONObject(getPayment());
                String returnMessage = json.optString("ReturnMessage");
                if (!UteisValidacao.emptyString(returnMessage)) {
                    return returnMessage;
                } else if (json.optString("Status").equals("0")){
                    return "NotFinished - (Aguardando atualização de status na Cielo)";
                }
            } catch (Exception ignored) {
            }
        }
        return cieloEnum.getDescricao();
    }

    public String getAutorizacao() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            JSONObject payment = obj.getJSONObject("Payment");
            return payment.getString("AuthorizationCode");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getPaymentId() {
        try {
            JSONObject obj = new JSONObject(getPayment());
            return obj.getString("PaymentId");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getBandeira() {
        String result = "";
        try {
            if (getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE)) {
                JSONObject obj = new JSONObject(getParamsEnvio());
                JSONObject payment = obj.getJSONObject("Payment");
                JSONObject creditCard = payment.getJSONObject("CreditCard");
                result = creditCard.getString("Brand");
            } else {
                JSONObject obj = new JSONObject(getParamsResposta());
                JSONObject payment = obj.getJSONObject("Payment");
                JSONObject creditCard = new JSONObject();
                if (getTipo().equals(TipoTransacaoEnum.CIELO_DEBITO_ONLINE)) {
                    creditCard = payment.getJSONObject("DebitCard");
                }
                result = creditCard.getString("Brand");
            }
        } catch (Exception ex) {
            return "";
        }
        return result.toUpperCase();
    }

    public String getCartaoMascarado() {
        try {
            return getValorCartaoMascarado();
        } catch (Exception e) {
            return "";
        }
    }

    public String getValorAtributoResposta(String nomeAtributo) throws Exception {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao)) {
            return getPaymentId();
        }
        return valor;
    }

    public String getValorAtributoCancelamento(String nomeAtributo) throws Exception {
        String valor = "";
        if (!UteisValidacao.emptyString(getResultadoCancelamento())) {
            try {
                if (APF.ResultSolicCancel.equalsIgnoreCase(nomeAtributo)) {
                    valor = new JSONObject(getResultadoCancelamento()).getJSONObject("last_transaction").getString("gateway_message");
                }
            } catch (Exception e) {
            }
        }
        return valor;
    }

    public String getPayment() throws Exception {
        try {
            JSONObject json = new JSONObject(this.getParamsResposta());
            return json.getJSONObject("Payment").toString();
        } catch (Exception e) {
            return new JSONObject().toString();
        }
    }

    public String getCodErroExterno() {
        try {
            String codigoRetorno = "";
            try {
                JSONObject json = new JSONObject(getPayment());
                codigoRetorno = json.optString("ReturnCode");
            } catch (Exception ex) {
                codigoRetorno = "";
            }

            if (UteisValidacao.emptyString(codigoRetorno)) {
                try {
                    JSONArray array = new JSONArray(getParamsResposta());
                    if (array.length() > 0) {
                        JSONObject json = array.getJSONObject(0);
                        String code =  json.optString("Code");
                        if (!UteisValidacao.emptyString(code)) {
                            codigoRetorno = code;
                        }
                    }
                } catch (Exception ignored) {
                }
            }

            return codigoRetorno;
        } catch (Exception ex) {
            return "";
        }
    }

    public String getNSU() {
        try {
            if (getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE)) {
                JSONObject obj = new JSONObject(getParamsResposta());
                JSONObject payment = obj.getJSONObject("Payment");
                return payment.getString("ProofOfSale");
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getTID() {
        try {
            if (getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE)) {
                JSONObject obj = new JSONObject(getParamsResposta());
                JSONObject payment = obj.getJSONObject("Payment");
                return payment.getString("Tid");
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer getNrVezes() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            JSONObject payment = obj.getJSONObject("Payment");
            return payment.getInt("Installments");
        } catch (Exception ex) {
            return 0;
        }
    }
}

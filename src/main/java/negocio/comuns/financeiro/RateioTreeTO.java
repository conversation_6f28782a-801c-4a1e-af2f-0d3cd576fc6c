package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.enumeradores.TipoRateioEnum;
import java.io.Serializable;



/**
 * <AUTHOR>
 *
 */
public class RateioTreeTO implements Serializable{
    private static final long serialVersionUID = 3065392205438828465L;
	
	private String codigoAgrupador;
	private String nome;
	private String tipoProduto;
	private String empresas;
	private int codigoEntidade = 0;
	private int entidadeRateio;
	private boolean detalhamento = false;
	private List<RateioIntegracaoTO> rateios;
	private int previa;
	private boolean exibirPrevia = true;
	private int tipoRateio = 0;
	private boolean possuiRateio = false;
	
	
	/**
	 * <AUTHOR>
	 * 23/08/2011
	 */
	public List<RateioIntegracaoTO> getRateiosPlano(){
		List<RateioIntegracaoTO> lista = new ArrayList<RateioIntegracaoTO>();
		for(RateioIntegracaoTO rateio : this.getRateios()){
			if(rateio.getTipoRateio().equals(TipoRateioEnum.PLANO_CONTAS.getCodigo()))
				lista.add(rateio);
		}
		return lista;
	}
	/**
	 * <AUTHOR>
	 * 23/08/2011
	 */
	public List<RateioIntegracaoTO> getRateiosCentro(){
		List<RateioIntegracaoTO> lista = new ArrayList<RateioIntegracaoTO>();
		for(RateioIntegracaoTO rateio : this.getRateios()){
			if(rateio.getTipoRateio().equals(TipoRateioEnum.CENTRO_CUSTOS.getCodigo()))
				lista.add(rateio);
		}
		return lista;
	}
	
	public boolean equals(Object obj){
        if (obj == null)
            return false;
        if (!(obj instanceof RateioTreeTO))
            return false;
        return  (((RateioTreeTO)obj).getCodigoAgrupador().equals(this.getCodigoAgrupador()));
    }

    public Long getCodigoNode() {
        String codigo = this.getCodigoAgrupador();
        codigo =  codigo.replace(".", "");
        return Long.parseLong(codigo.trim());
    }
	
	
	/**
	 * @return the nome
	 */
	public String getNome() {
		return nome;
	}
	/**
	 * @param nome the nome to set
	 */
	public void setNome(String nome) {
		this.nome = nome;
	}

	/**
	 * @param codigoAgrupador the codigoAgrupador to set
	 */
	public void setCodigoAgrupador(String codigoAgrupador) {
		this.codigoAgrupador = codigoAgrupador;
	}

	/**
	 * @return the codigoAgrupador
	 */
	public String getCodigoAgrupador() {
		return codigoAgrupador;
	}

	/**
	 * @param codigoEntidade the codigoEntidade to set
	 */
	public void setCodigoEntidade(int codigoEntidade) {
		this.codigoEntidade = codigoEntidade;
	}

	/**
	 * @return the codigoEntidade
	 */
	public int getCodigoEntidade() {
		return codigoEntidade;
	}

	/**
	 * @param entidadeRateio the tipoRateio to set
	 */
	public void setEntidadeRateio(int entidadeRateio) {
		this.entidadeRateio = entidadeRateio;
	}

	/**
	 * @return the tipoRateio
	 */
	public int getEntidadeRateio() {
		return entidadeRateio;
	}

	/**
	 * @param rateis the rateis to set
	 */
	public void setRateios(List<RateioIntegracaoTO> rateios) {
		this.rateios = rateios;
	}

	/**
	 * @return the rateis
	 */
	public List<RateioIntegracaoTO> getRateios() {
		if(rateios == null){
			rateios = new ArrayList<RateioIntegracaoTO>();
		}
		return rateios;
	}

	/**
	 * @param detalhamento the detalhamento to set
	 */
	public void setDetalhamento(boolean detalhamento) {
		this.detalhamento = detalhamento;
	}

	/**
	 * @return the detalhamento
	 */
	public boolean getDetalhamento() {
		return detalhamento;
	}

	/**
	 * @param previa the previa to set
	 */
	public void setPrevia(int previa) {
		this.previa = previa;
	}

	/**
	 * @return the previa
	 */
	public int getPrevia() {
		return previa;
	}

	/**
	 * @param exibirPrevia the exibirPrevia to set
	 */
	public void setExibirPrevia(boolean exibirPrevia) {
		this.exibirPrevia = exibirPrevia;
	}

	/**
	 * @return the exibirPrevia
	 */
	public boolean getExibirPrevia() {
		return exibirPrevia;
	}

	/**
	 * @param tipoRateio the tipoRateio to set
	 */
	public void setTipoRateio(int tipoRateio) {
		this.tipoRateio = tipoRateio;
	}

	/**
	 * @return the tipoRateio
	 */
	public int getTipoRateio() {
		return tipoRateio;
	}
	/**
	 * @param possuiRateio the possuiRateio to set
	 */
	public void setPossuiRateio(boolean possuiRateio) {
		this.possuiRateio = possuiRateio;
	}
	/**
	 * @return the possuiRateio
	 */
	public boolean isPossuiRateio() {
		return possuiRateio;
	}
	/**
	 * @param tipoProduto the tipoProduto to set
	 */
	public void setTipoProduto(String tipoProduto) {
		this.tipoProduto = tipoProduto;
	}
	/**
	 * @return the tipoProduto
	 */
	public String getTipoProduto() {
		return tipoProduto;
	}

    public String getEmpresas() {
        return empresas;
    }

    public void setEmpresas(String empresas) {
        this.empresas = empresas;
    }
	
        
}

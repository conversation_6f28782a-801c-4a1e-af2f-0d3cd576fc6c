package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CancelamentoAvaliandoParcelaTO extends CancelamentoCalculoTO {

    private ContratoVO contratoVO;
    private Integer diasUtilizados;

    private Double valorParcelaValoresEmAtraso;
    private boolean gerarParcelaValoresEmAtraso = false;

    private Double valorAnuidade;
    private Date dataCancelamento;
    private Integer diasProRataAnuidade;

    private List<MovParcelaVO> listaParcelasRestanteEmAberto;
    private List<MovParcelaVO> listaParcelasPagar;
    private List<MovParcelaVO> listaParcelasVencidas;
    private MovParcelaVO parcelaQuitacaoCancelamento;
    private String cobrarAnuidade;
    private boolean cobrarAnuidadeTotal = false;
    private boolean naoCobrarMulta = true;
    private Double porcentagemMulta;
    private Double valorMulta;
    private Double valorMultaOriginal;
    private boolean alterarValorMulta = false;
    private boolean usuarioAlterouValorMulta = false;
    private boolean cancelamentoAutomatico = false;
    private String informacoesDesfazer;
    private List<TransacaoVO> transacoes;

//    DEFINIÇÃO COBRAR ANUIDADE!!!!!
//    ("S"); //NAO
//    ("N"); //SIM

    public Integer getDiasUtilizados() {
        if (diasUtilizados == null) {
            diasUtilizados = 0;
        }
        return diasUtilizados;
    }

    public void setDiasUtilizados(Integer diasUtilizados) {
        this.diasUtilizados = diasUtilizados;
    }

    public String getValorAnuidade_Apresentar() {
        return Formatador.formatarValorMonetario(getValorAnuidade());
    }

    public Double getValorAnuidade() {
        if (valorAnuidade == null) {
            valorAnuidade = 0.0;
        }
        return valorAnuidade;
    }

    public void setValorAnuidade(Double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public Integer getDiasProRataAnuidade() {
        if (diasProRataAnuidade == null) {
            diasProRataAnuidade = 0;
        }
        return diasProRataAnuidade;
    }

    public void setDiasProRataAnuidade(Integer diasProRataAnuidade) {
        this.diasProRataAnuidade = diasProRataAnuidade;
    }

    public List<MovParcelaVO> getListaParcelasPagar() {
        if (listaParcelasPagar == null) {
            listaParcelasPagar = new ArrayList<>();
        }
        return listaParcelasPagar;
    }

    public void setListaParcelasPagar(List<MovParcelaVO> listaParcelasPagar) {
        this.listaParcelasPagar = listaParcelasPagar;
    }

    public String getQuantidadeParcelasPagar() {
        if (getListaParcelasPagar().size() == 1) {
            return "1 parcela";
        }
        return getListaParcelasPagar().size() + " parcelas";
    }

    public List<MovParcelaVO> getListaParcelasRestanteEmAberto() {
        if (listaParcelasRestanteEmAberto == null) {
            listaParcelasRestanteEmAberto = new ArrayList<>();
        }
        return listaParcelasRestanteEmAberto;
    }

    public void setListaParcelasRestanteEmAberto(List<MovParcelaVO> listaParcelasRestanteEmAberto) {
        this.listaParcelasRestanteEmAberto = listaParcelasRestanteEmAberto;
    }

    public String getQuantidadeParcelasRestanteEmAberto() {
        if (getListaParcelasRestanteEmAberto().size() == 1) {
            return "1 parcela";
        }
        return getListaParcelasRestanteEmAberto().size() + " parcelas";
    }

    public String getCobrarAnuidade() {
        if (cobrarAnuidade == null) {
            cobrarAnuidade = "";
        }
        return cobrarAnuidade;
    }

    public void setCobrarAnuidade(String cobrarAnuidade) {
        this.cobrarAnuidade = cobrarAnuidade;
    }

    public boolean isGerarParcelaValoresEmAtraso() {
        return gerarParcelaValoresEmAtraso;
    }

    public void setGerarParcelaValoresEmAtraso(boolean gerarParcelaValoresEmAtraso) {
        this.gerarParcelaValoresEmAtraso = gerarParcelaValoresEmAtraso;
    }

    public Double getValorParcelaValoresEmAtraso() {
        if (valorParcelaValoresEmAtraso == null) {
            valorParcelaValoresEmAtraso = 0.0;
        }
        return valorParcelaValoresEmAtraso;
    }

    public void setValorParcelaValoresEmAtraso(Double valorParcelaValoresEmAtraso) {
        this.valorParcelaValoresEmAtraso = valorParcelaValoresEmAtraso;
    }

    public String getValorParcelaValoresEmAtraso_Apresentar() {
        return Formatador.formatarValorMonetario(getValorParcelaValoresEmAtraso());
    }

    public Date getDataVencimentoParcelaValoresEmAtraso() {
        return getDataCancelamento();
    }

    public ContratoVO getContratoVO() {
        if (contratoVO == null) {
            contratoVO = new ContratoVO();
        }
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public Date getDataVencimentoAnuidade() {
        return getDataCancelamento();
    }

    public void setDataVencimentoAnuidade(Date dataVencimentoAnuidade) {

    }


    public String getDataVencimentoAnuidade_Apresentar() {
        return Uteis.getData(getDataVencimentoAnuidade());
    }

    public String getInformacoes() {
        return "";
    }

    public String getDescricaoCalculo() {
        StringBuilder msg = new StringBuilder();
        msg.append("SOLICITAÇÃO DE CANCELAMENTO:\n");
        msg.append("- Data da solicitação de cancelamento: ").append(Uteis.getData(getDataCancelamento())).append(".");
        msg.append("- Acesso até o dia de hoje, continue desfrutando de nossos serviços.\n\n");

        msg.append("PRÓXIMAS COBRANÇAS:\n" +
                "Serão efetuadas as cobranças futuras do pró-rata da taxa de manutenção anual, " +
                "última mensalidade e eventuais débitos pendentes até a data do efetivo cancelamento, " +
                "conforme previsto no termo de matrícula.\n\n");

        if (!UteisValidacao.emptyList(getListaParcelasVencidas())) {
            msg.append("PARCELAS VENCIDAS:\n");
            for (MovParcelaVO movParcelaVO : getListaParcelasVencidas()) {
                msg.append("Parcela: ").append(movParcelaVO.getDescricao()).append(" - Dt. Vencimento: ").append(Uteis.getData(movParcelaVO.getDataVencimento())).append(" - Valor ").append(Formatador.formatarValorMonetario(movParcelaVO.getValorParcela())).append(".\n");
            }
            msg.append("\n");
        }

        if (isGerarParcelaValoresEmAtraso()) {
            msg.append("PARCELA REFERENTE AOS VALORES EM ATRASO:\n");
            msg.append("Valor: ").append(Formatador.formatarValorMonetario(getValorParcelaValoresEmAtraso())).append(".\n");
            msg.append("Data Vencimento: ").append(Uteis.getData(getDataVencimentoParcelaValoresEmAtraso())).append(".\n\n");
        }
        if (!isGerarParcelaValoresEmAtraso() && !UteisValidacao.emptyList(getListaParcelasPagar())) {
            msg.append("MENSALIDADE:\n");
            msg.append("Cobrar próxima parcela: ").append(getListaParcelasPagar().get(0).getDescricao()).append(".\n");
            msg.append("Valor: ").append(getListaParcelasPagar().get(0).getValorParcela_Apresentar()).append(".\n");
            msg.append("Data Vencimento: ").append(Uteis.getData(getListaParcelasPagar().get(0).getDataVencimento())).append(".\n\n");
        }

        if (getCobrarAnuidade().equals("S")) {
            if (isCobrarAnuidadeTotal()) {
                msg.append("ANUIDADE:\n");
                msg.append(getValorAnuidade_Apresentar()).append(" - Referente a anuidade, a ser cobrada no dia ").append(Uteis.getData(getDataVencimentoAnuidade())).append(".\n\n");
            } else {
                msg.append("PRÓ-RATA DE ANUIDADE:\n");
                msg.append(getValorAnuidade_Apresentar()).append(" - Referente à ").append(getDiasProRataAnuidade()).append(" dias de pró-rata referente a anuidade, a ser cobrada no dia ").append(Uteis.getData(getDataVencimentoAnuidade())).append(".\n\n");
            }
        }
        if (!isNaoCobrarMulta() && getValorMulta() > 0.0) {
            msg.append("MULTA:\n");
            msg.append("Multa de ").append(getValorMulta_Apresentar()).append(", referente à ").append(getPorcentagemMulta()).append("% sobre o valor restante do contrato, a ser cobrada no dia seguinte ao cancelamento.\n\n");
        }
        if (isNaoCobrarMulta() && getValorMulta() > 0.0) {
            msg.append("MULTA:\n");
            msg.append("Multa de ").append(getValorMulta_Apresentar()).append(", referente à ").append(getPorcentagemMulta()).append("% sobre o valor restante do contrato, NÃO SERÁ COBRADA.\n\n");
        }
        return msg.toString();
    }

    public boolean isCobrarAnuidadeTotal() {
        return cobrarAnuidadeTotal;
    }

    public void setCobrarAnuidadeTotal(boolean cobrarAnuidadeTotal) {
        this.cobrarAnuidadeTotal = cobrarAnuidadeTotal;
    }

    public Double getValorMulta() {
        if (valorMulta == null) {
            valorMulta = 0.0;
        }
        return valorMulta;
    }

    public void setValorMulta(Double valorMulta) {
        this.valorMulta = valorMulta;
    }

    public Double getPorcentagemMulta() {
        if (porcentagemMulta == null) {
            porcentagemMulta = 0.0;
        }
        return porcentagemMulta;
    }

    public void setPorcentagemMulta(Double porcentagemMulta) {
        this.porcentagemMulta = porcentagemMulta;
    }

    @Override
    public Date getDataVencimentoMulta() {
        return getDataCancelamento();
    }

    public String getValorMulta_Apresentar() {
        return Formatador.formatarValorMonetario(getValorMulta());
    }

    public String getPorcentagemMulta_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getPorcentagemMulta());
    }

    public boolean isAlterarValorMulta() {
        return alterarValorMulta;
    }

    public void setAlterarValorMulta(boolean alterarValorMulta) {
        this.alterarValorMulta = alterarValorMulta;
    }

    public boolean isNaoCobrarMulta() {
        return naoCobrarMulta;
    }

    public void setNaoCobrarMulta(boolean naoCobrarMulta) {
        this.naoCobrarMulta = naoCobrarMulta;
    }

    public boolean isUsuarioAlterouValorMulta() {
        return usuarioAlterouValorMulta;
    }

    public void setUsuarioAlterouValorMulta(boolean usuarioAlterouValorMulta) {
        this.usuarioAlterouValorMulta = usuarioAlterouValorMulta;
    }

    public Double getValorMultaOriginal() {
        if (valorMultaOriginal == null) {
            valorMultaOriginal = 0.0;
        }
        return valorMultaOriginal;
    }

    public void setValorMultaOriginal(Double valorMultaOriginal) {
        this.valorMultaOriginal = valorMultaOriginal;
    }

    public List<MovParcelaVO> getListaParcelasVencidas() {
        if (listaParcelasVencidas == null) {
            listaParcelasVencidas = new ArrayList<MovParcelaVO>();
        }
        return listaParcelasVencidas;
    }

    public void setListaParcelasVencidas(List<MovParcelaVO> listaParcelasVencidas) {
        this.listaParcelasVencidas = listaParcelasVencidas;
    }

    public boolean isCancelamentoAutomatico() {
        return cancelamentoAutomatico;
    }

    public void setCancelamentoAutomatico(boolean cancelamentoAutomatico) {
        this.cancelamentoAutomatico = cancelamentoAutomatico;
    }

    public String getInformacoesDesfazer() {
        if (informacoesDesfazer == null) {
            informacoesDesfazer = "";
        }
        return informacoesDesfazer;
    }

    public void setInformacoesDesfazer(String informacoesDesfazer) {
        this.informacoesDesfazer = informacoesDesfazer;
    }

    public MovParcelaVO getParcelaQuitacaoCancelamento() {
        if (parcelaQuitacaoCancelamento == null) {
            parcelaQuitacaoCancelamento = new MovParcelaVO();
        }
        return parcelaQuitacaoCancelamento;
    }

    public void setParcelaQuitacaoCancelamento(MovParcelaVO parcelaQuitacaoCancelamento) {
        this.parcelaQuitacaoCancelamento = parcelaQuitacaoCancelamento;
    }

    public List<TransacaoVO> getTransacoes() {
        if (transacoes == null) {
            transacoes = new ArrayList<>();
        }
        return transacoes;
    }

    public void setTransacoes(List<TransacaoVO> transacoes) {
        this.transacoes = transacoes;
    }
}

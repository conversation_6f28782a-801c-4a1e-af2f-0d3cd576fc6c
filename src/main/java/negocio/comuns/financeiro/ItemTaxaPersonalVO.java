
package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.plano.DescontoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class ItemTaxaPersonalVO extends SuperVO {
    // constantes
    public static final int LIVRE = 0;
    public static final int GERADO = 1;
    public static final int PAGO = 2;
    public static final int VENCIDO = 3;

    // atributos da tabela
    @ChavePrimaria
    private Integer codigo = 0;
    @ChaveEstrangeira
    private ClienteVO aluno = new ClienteVO();
    @ChaveEstrangeira
    private ProdutoVO produto = new ProdutoVO();
    @ChaveEstrangeira
    private DescontoVO desconto = new DescontoVO();
    private Double descontoEspecifico = new Double(0.0);
    private boolean mostrarDesconto = false;

    // dados de controle
    private boolean marcado = false;
    private int situacao = LIVRE;
    // necessario colocar aqui para evitar o uso de mais uma lista
    private MovProdutoVO movProduto = new MovProdutoVO();
    
    private String mesReferencia = "";
    private String personal = "";
    private Boolean mesmaEmpresa = true;
    private String nomeEmpresa = "";

    public void validarDados() throws Exception {
        if(aluno == null || aluno.getCodigo().intValue() == 0)
            throw new Exception("Aluno Não Informado.");
        if(produto == null || produto.getCodigo().intValue() == 0)
            throw new Exception("Produto Não Informado.");
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteVO getAluno() {
        return aluno;
    }

    public void setAluno(ClienteVO aluno) {
        this.aluno = aluno;
    }

    public ProdutoVO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public DescontoVO getDesconto() {
        return desconto;
    }

    public void setDesconto(DescontoVO desconto) {
        this.desconto = desconto;
    }

    public boolean isMarcado() {
        return marcado;
    }

    public void setMarcado(boolean marcado) {
        this.marcado = marcado;
    }

    public int getSituacao() {
        return situacao;
    }

    public void setSituacao(int situacao) {
        this.situacao = situacao;
    }

    public boolean isLivre() {
        return situacao == LIVRE;
    }

    public boolean isGerado() {
        return situacao == GERADO;
    }

    public boolean isPago() {
        return situacao == PAGO;
    }

    public boolean isVencido() {
        return situacao == VENCIDO;
    }

    public MovProdutoVO getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProdutoVO movProduto) {
        this.movProduto = movProduto;
    }

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof ItemTaxaPersonalVO) {
            ItemTaxaPersonalVO aux = (ItemTaxaPersonalVO)obj;
            return this.getAluno().getCodigo().intValue() == aux.getAluno().getCodigo().intValue();
        }
        return false;
    }

    public double getValorFinal() {
        double valor = getProduto().getValorFinal();
        if(getProduto().getDesconto().getTipoDesconto().equals(TipoDesconto.PE)) {
            valor = valor * (1 - getProduto().getDesconto().getValor() / 100);
        } else if(getProduto().getDesconto().getTipoDesconto().equals(TipoDesconto.VA)) {
            valor = valor - getProduto().getDesconto().getValor();
        }
        return valor;
    }

    public double getValorDesconto(){
        double valor = 0.00 ;
        if(getProduto().getDesconto().getTipoDesconto().equals(TipoDesconto.PE)) {
            valor = 100 - (getProduto().getValorFinal()/getProduto().getValorFinal() * (1 - getProduto().getDesconto().getValor() / 100)*100);
        } else if(getProduto().getDesconto().getTipoDesconto().equals(TipoDesconto.VA)) {
            valor = getProduto().getDesconto().getValor();
        }
        return valor;
    }
    public boolean isMostrarDesconto() {
        return mostrarDesconto;
    }

    public void setMostrarDesconto(boolean mostrarDesconto) {
        this.mostrarDesconto = mostrarDesconto;
    }

    public String getNomeAluno_Apresentar () {
        if (getAluno() == null || getAluno().getPessoa() == null || UteisValidacao.emptyString(getAluno().getPessoa().getNome())) {
            return "";
        }
        return getAluno().getPessoa().getNome();
    }
    
    public String getNomeAluno(){
        return getAluno().getPessoa().getNome();
    }
    
    public String getDescricaoProduto(){
        return getProduto().getDescricao();
    }

    public String getMesReferencia() {
        return mesReferencia;
    }

    public void setMesReferencia(String mesReferencia) {
        this.mesReferencia = mesReferencia;
    }

    public String getPersonal() {
        return personal;
    }

    public void setPersonal(String personal) {
        this.personal = personal;
    }
    
    public Double getValorProduto(){
        try {
            return getProduto().getValorFinal();
        } catch (Exception e) {
            return 0.0;
        }
    }

    public Double getDescontoEspecifico() {
        return descontoEspecifico;
    }

    public void setDescontoEspecifico(Double descontoEspecifico) {
        this.descontoEspecifico = descontoEspecifico;
    }

    public Boolean getMesmaEmpresa() {
        if (mesmaEmpresa == null) {
            mesmaEmpresa = true;
        }
        return mesmaEmpresa;
    }

    public void setMesmaEmpresa(Boolean mesmaEmpresa) {
        this.mesmaEmpresa = mesmaEmpresa;
    }

    public String getNomeEmpresa() {
        if (nomeEmpresa == null) {
            nomeEmpresa = "";
        }
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }
}

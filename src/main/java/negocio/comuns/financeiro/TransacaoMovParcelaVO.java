/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class TransacaoMovParcelaVO extends SuperVO{

    private MovParcelaVO movParcela;
    private TransacaoVO transacao;
    private Integer nrTentativaParcela;
    private Double valorParcela;
    private Double valorMulta;
    private Double valorJuros;

    public MovParcelaVO getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(MovParcelaVO movParcela) {
        this.movParcela = movParcela;
    }

    public TransacaoVO getTransacao() {
        return transacao;
    }

    public void setTransacao(TransacaoVO transacao) {
        this.transacao = transacao;
    }

    public Integer getNrTentativaParcela() {
        if (nrTentativaParcela == null) {
            nrTentativaParcela = 0;
}
        return nrTentativaParcela;
    }

    public void setNrTentativaParcela(Integer nrTentativaParcela) {
        this.nrTentativaParcela = nrTentativaParcela;
    }

    public Double getValorParcela() {
        if (valorParcela == null) {
            valorParcela =  0.0;
        }
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public Double getValorMulta() {
        if (valorMulta == null) {
            valorMulta =  0.0;
        }
        return valorMulta;
    }

    public void setValorMulta(Double valorMulta) {
        this.valorMulta = valorMulta;
    }

    public Double getValorJuros() {
        if (valorJuros == null) {
            valorJuros =  0.0;
        }
        return valorJuros;
    }

    public void setValorJuros(Double valorJuros) {
        this.valorJuros = valorJuros;
    }
}

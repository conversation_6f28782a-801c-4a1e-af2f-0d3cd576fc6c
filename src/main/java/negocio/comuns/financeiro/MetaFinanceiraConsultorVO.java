package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.facade.jdbc.financeiro.MetaFinanceiraEmpresa;

/**
 *
 * <AUTHOR>
 */
public class MetaFinanceiraConsultorVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    @NaoControlarLogAlteracao
    private int metaFinanceiraEmpresa = 0;
    @ChaveEstrangeira
    private ColaboradorVO colaborador = new ColaboradorVO();
    private double percentagem = 0.0;
    @NaoControlarLogAlteracao
    private double meta1 = 0.0;
    @NaoControlarLogAlteracao
    private double meta2 = 0.0;
    @NaoControlarLogAlteracao
    private double meta3 = 0.0;
    @NaoControlarLogAlteracao
    private double meta4 = 0.0;
    @NaoControlarLogAlteracao
    private double meta5 = 0.0;
    @NaoControlarLogAlteracao
    private List<MetaFinanceiraEmpresaValoresVO> valores = new ArrayList<MetaFinanceiraEmpresaValoresVO>();

    public MetaFinanceiraConsultorVO() {
    }

    public MetaFinanceiraConsultorVO(int codigo) {
        this.codigo = codigo;
    }

    public void validarDados() throws Exception {
        if (!validarDados) {
            return;
        }
        if (colaborador == null || colaborador.getCodigo().intValue() == 0) {
            throw new Exception("Informe um Colaborador para continuar.");
        }
        if (percentagem <= 0.0) {
            throw new Exception("Informe uma Percentagem para continuar.");
        }
        if (percentagem > 100){
            throw new Exception("Informe uma Percentagem até 100%.");
        }
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getMetaFinanceiraEmpresa() {
        return metaFinanceiraEmpresa;
    }

    public void setMetaFinanceiraEmpresa(int metaFinanceiraEmpresa) {
        this.metaFinanceiraEmpresa = metaFinanceiraEmpresa;
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    public double getPercentagem() {
        return percentagem;
    }

    public void setPercentagem(double percentagem) {
        this.percentagem = percentagem;
    }

    public double getMeta1() {
        return meta1;
    }

    public void setMeta1(double meta1) {
        this.meta1 = meta1;
    }

    public double getMeta2() {
        return meta2;
    }

    public void setMeta2(double meta2) {
        this.meta2 = meta2;
    }

    public double getMeta3() {
        return meta3;
    }

    public void setMeta3(double meta3) {
        this.meta3 = meta3;
    }

    public double getMeta4() {
        return meta4;
    }

    public void setMeta4(double meta4) {
        this.meta4 = meta4;
    }

    public double getMeta5() {
        return meta5;
    }

    public void setMeta5(double meta5) {
        this.meta5 = meta5;
    }

    // João Alcides: o modelo de geração de log do sistema não gera de forma simples o log das entidades 
    // que possuem uma complexidade maior, como o MetaFinanceiroVO e suas dependências.
    // criei método próprio de gerar o log de alterações dessas entidades.
    public List<LogVO> montarLogAlteracao(Integer codigoMeta, UsuarioVO responsavel, String operacao) throws IllegalArgumentException, ClassNotFoundException, IllegalAccessException, Exception {
        if (getObjetoVOAntesAlteracao() == null) {
            setObjetoVOAntesAlteracao(new MetaFinanceiraConsultorVO());
        }
        List<LogVO> log = gerarLogAlteracaoObjetoVO();

        for (LogVO obj : log) {
            obj.setChavePrimaria(codigoMeta.toString());
            obj.setNomeEntidade("METAFINANCEIRAEMPRESA");
            obj.setChavePrimariaEntidadeSubordinada(getCodigo().toString());
            obj.setNomeEntidadeDescricao("MetaFinanceiraEmpresaVO - MetaFinanceiraConsultorVO");
            obj.setOperacao(operacao);
            obj.setResponsavelAlteracao(responsavel.getNome());
            obj.setUserOAMD(responsavel.getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("Consultor-" + getColaborador().getPessoa().getNome() + ": " + obj.getNomeCampo());
        }
        return log;
    }

    public void setValores(List<MetaFinanceiraEmpresaValoresVO> valores) {
        this.valores = valores;
    }

    public List<MetaFinanceiraEmpresaValoresVO> getValores() {
        return valores;
    }

    public void montarValores(MetaFinanceiraEmpresaVO meta) {
        valores = new ArrayList<MetaFinanceiraEmpresaValoresVO>();
        for (MetaFinanceiraEmpresaValoresVO valor : meta.getValoresBackUp()) {
            MetaFinanceiraEmpresaValoresVO valorMeta = new MetaFinanceiraEmpresaValoresVO();
            valorMeta.setCodigo(valor.getCodigo());
            valorMeta.setValor(valor.getValor() * (percentagem / 100));
            valores.add(valorMeta);
        }
    }
}

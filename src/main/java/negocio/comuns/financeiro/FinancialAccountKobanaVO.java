package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import com.sun.xml.ws.api.tx.at.Transactional;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

import java.util.Date;

/**
 * Created by <PERSON> on 25/06/2024.
 */

public class FinancialAccountKobanaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer empresa;
    protected String uid;
    protected Date created_at;
    protected boolean ativo;
    protected String financial_provider_slug;
    protected int codIntegracaoKobana;

    @Transactional
    protected String account_number;
    @Transactional
    protected String account_digit;
    @Transactional
    protected String agency_number;
    @Transactional
    protected String agency_digit;
    @Transactional
    protected String created_at_Apresentar;


    public FinancialAccountKobanaVO() {
        super();
    }

    public FinancialAccountKobanaVO(int empresa, JSONObject dados) throws Exception {
        this.empresa = empresa;
        this.uid = dados.getString("uid");
        this.created_at = Uteis.getDate(dados.getString("created_at"), "yyyy-MM-dd'T'HH:mm:ss");
        this.financial_provider_slug = dados.getString("financial_provider_slug");
        this.account_number = dados.getString("account_number");
        this.account_digit = dados.getString("account_digit");
        this.agency_number = dados.getString("agency_number");
        this.agency_digit = dados.getString("agency_digit");
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Date getCreated_at() {
        return created_at;
    }

    public void setCreated_at(Date created_at) {
        this.created_at = created_at;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public String getFinancial_provider_slug() {
        return financial_provider_slug;
    }

    public void setFinancial_provider_slug(String financial_provider_slug) {
        this.financial_provider_slug = financial_provider_slug;
    }

    public int getCodIntegracaoKobana() {
        return codIntegracaoKobana;
    }

    public void setCodIntegracaoKobana(int codIntegracaoKobana) {
        this.codIntegracaoKobana = codIntegracaoKobana;
    }

    public String getCreated_at_Apresentar() {
        if (getCreated_at() == null) {
            return "";
        }
        return Uteis.getDataComHora(getCreated_at());
    }

    public void setCreated_at_Apresentar(String created_at_Apresentar) {
        this.created_at_Apresentar = created_at_Apresentar;
    }
}

package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by GlaucoT on 03/05/2016
 */
public class AgrupadorRemessaTO extends SuperTO {

    private Date diaAgrupado;
    private List<RemessaVO> remessas;

    public Date getDiaAgrupado() {
        return diaAgrupado;
    }

    public void setDiaAgrupado(Date diaAgrupado) {
        this.diaAgrupado = diaAgrupado;
    }

    public Integer getQtdItens() {
        int qtdItens = 0;
        for (RemessaVO remessa : getRemessas()) {
            qtdItens += remessa.getQtdRegistros();
        }
        return qtdItens;
    }

    public Double getValorBruto() {
        double valorLiquido = 0;
        for (RemessaVO remessa : getRemessas()) {
            valorLiquido += remessa.getValorBruto();
        }
        return valorLiquido;
    }

    public Double getValorAceito() {
        double valorAceito = 0;
        for (RemessaVO remessa : getRemessas()) {
            valorAceito += remessa.getValorAceito();
        }
        return valorAceito;
    }

    public Double getValorLiquido() {
        double valorLiquido = 0;
        for (RemessaVO remessa : getRemessas()) {
            valorLiquido += remessa.getValorLiquido();
        }
        return valorLiquido;
    }

    public List<RemessaVO> getRemessas() {
        if (remessas == null) {
            remessas = new ArrayList<RemessaVO>();
        }
        return remessas;
    }

    public void setRemessas(List<RemessaVO> remessas) {
        this.remessas = remessas;
    }

    public void adicionarItem(RemessaVO remessaVO) {
        getRemessas().add(remessaVO);
    }
}

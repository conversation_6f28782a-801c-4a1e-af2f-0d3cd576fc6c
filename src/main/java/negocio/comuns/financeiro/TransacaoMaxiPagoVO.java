package negocio.comuns.financeiro;

import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.maxiPago.MaxiPagoRespostaTransacaoEnum;

import java.util.logging.Level;
import java.util.logging.Logger;

/*
 * Created by <PERSON><PERSON> on 17/08/2017.
 */
public class TransacaoMaxiPagoVO extends TransacaoVO {

    public String getValorCodigoExterno() throws Exception {
        return getCodigoExterno();
    }

    public String getValorCartaoMascarado() throws Exception {
        JSONObject obj = new JSONObject(getParamsEnvio());
        return APF.getCartaoMascarado(obj.getString("creditCardNumber"));
    }

    public String getValorUltimaTransacaoAprovada() throws Exception {
        return getValorAtributoResposta(APF.Transacao);
    }

    public String getResultadoRequisicao() throws Exception {

        try {
            //Em caso de cancelamento
            if (isCancelada()) {
                return "Transação Cancelada";
            }
        } catch (Exception ex) {
        }

        JSONObject json = new JSONObject(getParamsResposta());
        String retorno = json.getString("responseCode");
        String msgDetalhadaAdquirenteMaxipago = json.optString("processorMessage");
        return MaxiPagoRespostaTransacaoEnum.valueOff(retorno).getDescricao() + (UteisValidacao.emptyString(msgDetalhadaAdquirenteMaxipago) ? "" : ": " + msgDetalhadaAdquirenteMaxipago);
    }

    private boolean isCancelada() {
        try {
            String cancelamento = getResultadoCancelamento();
            JSONObject cancelamentoJson = new JSONObject(cancelamento);
            if (cancelamentoJson.optString("processorMessage").equalsIgnoreCase("Refund successful.")) {
                //já cancelada
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            return false;
        }
    }

    public String getAutorizacao() {
        try {
            return getCodigoAutorizacao();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getPaymentId() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            return obj.getString("transactionID");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getTID() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            return obj.getString("transactionID");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getBandeira() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            String bandeira = obj.getString("band").toUpperCase();
            if (bandeira.equals("MASTERCARD")){
                return "MASTER";
            } else {
                return bandeira;
            }
        } catch (Exception ex) {
            return "";
        }
    }

    public String getCartaoMascarado() {
        try {
            return getValorCartaoMascarado();
        } catch (Exception ignored) {
            return "";
        }
    }

    public String getValorAtributoResposta(String nomeAtributo) throws Exception {
        if (nomeAtributo.equals(APF.Transacao)) {
            return getPaymentId();
        }
        return "";
    }

    public String getValorAtributoCancelamento(String nomeAtributo) throws Exception {
        String valor = "";
        if (!UteisValidacao.emptyString(getResultadoCancelamento())) {
            try {
                if (APF.ResultSolicCancel.equalsIgnoreCase(nomeAtributo)) {
                    valor = new JSONObject(getResultadoCancelamento()).getJSONObject("last_transaction").getString("gateway_message");
                }
            } catch (Exception ignored) {
            }
        }
        return valor;
    }

    private String getPayment() throws Exception {
        try {
            JSONObject json = new JSONObject(this.getParamsResposta());
            return json.getJSONObject("Payment").toString();
        } catch (Exception e) {
            return "";
        }
    }

    public String getCodErroExterno() {
        try {
            String codigoRetorno = getResponseCode();
            if (!UteisValidacao.emptyString(codigoRetorno) && !MaxiPagoRespostaTransacaoEnum.APROVADA.getId().equals(codigoRetorno)) { //somente se não for autorizado retorna o erro.
                return codigoRetorno;
            }
        } catch (Exception ex) {
        }
        return "0";
    }

    private String getResponseCode() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            return json.getString("responseCode");
        } catch (Exception ex) {
            return "";
        }
    }
}

package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import servicos.pix.PixOperacaoLogEnum;

import java.util.Date;

public class PixLogVO extends SuperVO {

    private Integer codigo;
    private Date dataRegistro;
    private PixVO pixVO;
    private UsuarioVO usuarioVO;
    private PixOperacaoLogEnum operacao;
    private String log;

    public PixLogVO() {

    }

    public PixLogVO(PixVO pixVO, UsuarioVO usuarioVO, PixOperacaoLogEnum operacao) {
        this.pixVO = pixVO;
        this.usuarioVO = usuarioVO;
        this.operacao = operacao;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getLog() {
        if (log == null) {
            log = "";
        }
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public PixVO getPixVO() {
        if (pixVO == null) {
            pixVO = new PixVO();
        }
        return pixVO;
    }

    public void setPixVO(PixVO pixVO) {
        this.pixVO = pixVO;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public PixOperacaoLogEnum getOperacao() {
        if (operacao == null) {
            operacao = PixOperacaoLogEnum.NENHUM;
        }
        return operacao;
    }

    public void setOperacao(PixOperacaoLogEnum operacao) {
        this.operacao = operacao;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class ObjetoGenerico extends SuperTO {
    private static final long serialVersionUID = 7178296834953470795L;

    private String atributo = "";
    private String valor = "";

    public ObjetoGenerico(String atributo, String valor) {
        this.atributo = atributo;
        this.valor = valor;
    }

    public ObjetoGenerico(String nomeAtributoEValor) {
        String[] a = nomeAtributoEValor.split("=");
        if (a != null && a.length > 1) {
            this.atributo = a[0].trim();
            this.valor = a[1];
        }
    }

    public ObjetoGenerico(String atributo, Object valor) {
        this.atributo = atributo.trim();
        this.valor = valor.toString();
    }

    public String getAtributo() {
        return atributo;
    }

    public void setAtributo(String atributo) {
        this.atributo = atributo;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    @Override
    public String toString() {
        return String.format("%s=%s", this.atributo, this.valor);
    }
}

package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.beans.Transient;

public class AdquirenteVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private TipoConvenioCobrancaEnum tipoConvenio;
    private String nome;
    private boolean situacao = true;
    private boolean isGeoitd = false;

    private String cnpj = "";

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public AdquirenteVO(TipoConvenioCobrancaEnum tipoConvenio, String nome) {
        this.tipoConvenio = tipoConvenio;
        this.nome = nome;
    }

    public AdquirenteVO() {

    }

    public static void validarDados(AdquirenteVO obj) throws Exception {
        if (UteisValidacao.emptyString(obj.getNome())) {
            throw new ConsistirException("O campo NOME (Adquirente) deve ser informado.");
        }
    }

    @Override
    public Integer getCodigo() {
        if(codigo == null){
            codigo = 0;
        }
        return codigo;
    }

    public String getConvenioApresentar(){
        return  tipoConvenio == null ? "" : tipoConvenio.getDescricao();
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoConvenioCobrancaEnum getTipoConvenio() {
        return tipoConvenio;
    }

    public void setTipoConvenio(TipoConvenioCobrancaEnum tipoConvenio) {
        this.tipoConvenio = tipoConvenio;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isSituacao() {
        return situacao;
    }

    public void setSituacao(boolean situacao) {
        this.situacao = situacao;
    }

    public String getSituacao_Apresentar() {
        return isSituacao() ? "ATIVO" : "INATIVO";
    }

    public boolean isGeoitd() {
        return isGeoitd;
    }

    public void setGeoitd(boolean geoitd) {
        isGeoitd = geoitd;
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("codigo", codigo);
        json.put("nome", nome);
        json.put("tioConvenio", tipoConvenio.toJSON());

        return json;
    }
}

package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.comuns.util.Formatador;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.crm.AberturaMetaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

public class DetalhamentoMetaFinanceiroVO extends SuperVO {
	
	private Date dia;
	private Integer diaSimples;
	private Double faturamentoRealizado;
	private Double faturamentoAcumulado;
	private List<MetaFinanceiraEmpresaValoresVO> metas = new ArrayList<MetaFinanceiraEmpresaValoresVO>();
	private String cor = "";
	private String corTexto = "";
	private String observacao = "";
	private Boolean diaCalculado;
	
	public String getDia_Apresentar(){
		return Formatador.formatarData( dia, "dd/MM");
	}
	
	public String getDia_Semana_Apresentar(){
		Calendar calendar = Calendario.getInstance();
        calendar.setTime(dia);
        return Formatador.formatarData( dia, "dd/MM") +" ("+
        	   DiaSemana.getDiaSemanaNumeral(calendar.get(Calendar.DAY_OF_WEEK)).getDescricaoSimples()+")";
	}
	
	public String getDiaComAno(){
		return Formatador.formatarData( dia, "dd/MM/yyyy");
	}
	
	public String getFaturamentoRealizado_Apresentar(){
		return Formatador.formatarValorMonetarioSemMoeda(this.faturamentoRealizado);
	}
	
	public String getFaturamentoAcumulado_Apresentar(){
		return Formatador.formatarValorMonetarioSemMoeda(this.faturamentoAcumulado);
	}
	
	public Date getDia() {
		return dia;
	}
	public void setDia(Date dia) {
		this.dia = dia;
	}
	public Double getFaturamentoRealizado() {
		return faturamentoRealizado;
	}
	public void setFaturamentoRealizado(Double faturamentoRealizado) {
		this.faturamentoRealizado = faturamentoRealizado;
	}
	public Double getFaturamentoAcumulado() {
		return faturamentoAcumulado;
	}
	public void setFaturamentoAcumulado(Double faturamentoAcumulado) {
		this.faturamentoAcumulado = faturamentoAcumulado;
	}
	public List<MetaFinanceiraEmpresaValoresVO> getMetas() {
		return metas;
	}
	public void setMetas(List<MetaFinanceiraEmpresaValoresVO> metas) {
		this.metas = metas;
	}
	
	@Override
	public int hashCode() {
		return dia.hashCode();
	}
	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DetalhamentoMetaFinanceiroVO other = (DetalhamentoMetaFinanceiroVO) obj;
		//comparar data
		if (!Calendario.getDataComHoraZerada(this.getDia()).equals(Calendario.getDataComHoraZerada(other.getDia())))
			return false;
		return true;
	}

	public void setCorTexto(String corTexto) {
		this.corTexto = corTexto;
	}

	public String getCorTexto() {
		return corTexto;
	}

	public void setCor(String cor) {
		this.cor = cor;
	}

	public String getCor() {
		return cor;
	}

	public void setObservacao(String observacao) {
		this.observacao = observacao;
	}

	public String getObservacao() {
		return observacao;
	}
	public void setDiaSimples(Integer diaSimples) {
		this.diaSimples = diaSimples;
	}
	public Integer getDiaSimples() {
		return diaSimples;
	}
	public void setDiaCalculado(Boolean diaCalculado) {
		this.diaCalculado = diaCalculado;
	}
	public Boolean getDiaCalculado() {
		return diaCalculado;
	}

}

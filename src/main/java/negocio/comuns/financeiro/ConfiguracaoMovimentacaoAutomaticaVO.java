package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;

public class ConfiguracaoMovimentacaoAutomaticaVO extends SuperVO {

    private Integer codigo;
    private EmpresaVO empresa = new EmpresaVO();
    private FormaPagamentoVO formaPagamento = new FormaPagamentoVO();
    private ContaVO conta = new ContaVO();
    private UsuarioVO usuario = new UsuarioVO();
    private AdquirenteVO adquirente = new AdquirenteVO();
    private ConvenioCobrancaVO convenio = new ConvenioCobrancaVO();
    private TipoFormaPagto tipoFormaPagto;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public FormaPagamentoVO getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamentoVO formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public ContaVO getConta() {
        return conta;
    }

    public void setConta(ContaVO conta) {
        this.conta = conta;
    }

    public UsuarioVO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public AdquirenteVO getAdquirente() {
        return adquirente;
    }

    public void setAdquirente(AdquirenteVO adquirente) {
        this.adquirente = adquirente;
    }

    public TipoFormaPagto getTipoFormaPagto() {
        return tipoFormaPagto;
    }

    public void setTipoFormaPagto(TipoFormaPagto tipoFormaPagto) {
        this.tipoFormaPagto = tipoFormaPagto;
    }

    public ConvenioCobrancaVO getConvenio() {
        return convenio;
    }

    public void setConvenio(ConvenioCobrancaVO convenio) {
        this.convenio = convenio;
    }
}



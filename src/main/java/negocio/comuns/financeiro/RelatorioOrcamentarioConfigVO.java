package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.List;

public class RelatorioOrcamentarioConfigVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    @ChaveEstrangeira
    private EmpresaVO empresa = new EmpresaVO();
    private int ano = 0;
    private Mes mes = Mes.VAZIO;
    private String descricao = "";
    @NaoControlarLogAlteracao
    private List<RelatorioOrcamentarioValoresPrevisaoVO> valores = new ArrayList<RelatorioOrcamentarioValoresPrevisaoVO>();



    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public void validarDados() throws Exception {
        if (!validarDados) {
            return;
        }
        if (empresa == null || empresa.getCodigo().intValue() == 0) {
            throw new Exception("Informe a empresa.");
        }
        if (ano < (Uteis.getAnoData(Calendario.hoje())) - 10){
            throw new Exception("Ano informado inválido ou é 10 anos anterior ao ano atual.");
        }
        if (mes.equals(Mes.VAZIO)) {
            throw new Exception("Selecione um mês válido.");
        }
        if (descricao.trim().isEmpty()) {
            throw new Exception("Informe uma descrição.");
        }
    }

    public void realizarUpperCaseDados() {
        descricao = descricao.toUpperCase();
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public int getAno() {
        return ano;
    }

    public void setAno(int ano) {
        this.ano = ano;
    }

    public Mes getMes() {
        return mes;
    }

    public String getMesAno() {
        String mesAno = "";
        if (mes != null) {
            mesAno += this.mes.getCodigo();
            mesAno = mesAno.length() == 1 ? "0" + mesAno : mesAno;
            mesAno += "/" + this.getAno();
        }
        return mesAno;
    }

    public void setMes(Mes mes) {
        this.mes = mes;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<RelatorioOrcamentarioValoresPrevisaoVO> getValores() {
        return valores;
    }

    public void setValores(List<RelatorioOrcamentarioValoresPrevisaoVO> valores) {
        this.valores = valores;
    }

}

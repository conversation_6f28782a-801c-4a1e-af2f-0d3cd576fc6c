package negocio.comuns.financeiro;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ComissaoTree implements Serializable {

    public static int COLABORADOR = 1;
    public static int MODALIDADE = 2;
    public static int TURMA = 3;
    public static int HORARIO = 4;
    public static int PESSOA = 5;
    private int chavePrimaria = 0;
    private int cliente = 0;
    private int chavePrimariaColaborador = 0;
    private String codigoAgrupador = "";
    private String nome = "";
    private String tipoComissao;
    private double comissao = 0.0;
    private double valorComissao = 0.0;
    private double valorPago = 0.0;
    private int tipoNo = 0;
    private List<DiaSemana> diasSemana = new ArrayList<DiaSemana>();
    private int qtdAluno = 0;
    private List<ComissaoTree> modalidades = new ArrayList();
    private List<ComissaoTree> turmas = new ArrayList();
    private List<ComissaoTree> horarios = new ArrayList();
    private List<ComissaoTree> alunos = new ArrayList();
    private Integer movPagamento;
    private ClienteVO clienteVO;
    private TurmaVO turmaVO;
    private MatriculaAlunoHorarioTurmaVO matriculaAluno;

    public ComissaoTree() {
    }

    public static List<ComissaoTree> getFilhos(List<ComissaoTree> cts, String codPai) {
        List<ComissaoTree> filhos = new ArrayList<ComissaoTree>();
        for (ComissaoTree ct : cts) {
            if (ct.getCodigoPai().equals(codPai))
                filhos.add(ct);
        }
        return filhos;
    }

    public MatriculaAlunoHorarioTurmaVO getMatriculaAluno() {
        return matriculaAluno;
    }

    public void setMatriculaAluno(MatriculaAlunoHorarioTurmaVO matriculaAluno) {
        this.matriculaAluno = matriculaAluno;
    }

    public TurmaVO getTurmaVO() {
        return turmaVO;
    }

    public void setTurmaVO(TurmaVO turmaVO) {
        this.turmaVO = turmaVO;
    }

    public ClienteVO getClienteVO() {
        if(clienteVO == null){
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Integer getMovPagamento() {
        return movPagamento;
    }

    public void setMovPagamento(Integer movPagamento) {
        this.movPagamento = movPagamento;
    }

    public List<ComissaoTree> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<ComissaoTree> alunos) {
        this.alunos = alunos;
    }

    public List<ComissaoTree> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<ComissaoTree> horarios) {
        this.horarios = horarios;
    }

    public List<ComissaoTree> getTurmas() {
        return turmas;
    }

    public void setTurmas(List<ComissaoTree> turmas) {
        this.turmas = turmas;
    }

    public boolean getApresentarDiasSemana() {
        return tipoNo == PESSOA || tipoNo == HORARIO;
    }

    public boolean getApresentarLink() {
        return tipoNo == PESSOA;
    }

    public List<ComissaoTree> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ComissaoTree> modalidades) {
        this.modalidades = modalidades;
    }

    public Long getCodigoNode() {
        String codigo = this.getCodigoAgrupador();
        codigo = codigo.replace(".", "");
        return Long.parseLong(codigo.trim());
    }

    /**
     * @return the codigoAgrupador
     */
    public String getCodigoAgrupador() {
        return codigoAgrupador;
    }

    /**
     * @param codigoAgrupador the codigoAgrupador to set
     */
    public void setCodigoAgrupador(String codigoAgrupador) {
        this.codigoAgrupador = codigoAgrupador;
    }

    public List<DiaSemana> getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(List<DiaSemana> diasSemana) {
        this.diasSemana = diasSemana;
    }

    public double getComissao() {
        return comissao;
    }

    public void setComissao(double comissao) {
        this.comissao = comissao;
    }

    public double getValorComissao() {
        return valorComissao;
    }

    public void setValorComissao(double somaValores) {
        this.valorComissao = somaValores;
    }

    public double getValorPago() {
        return valorPago;
    }

    public void setValorPago(double fracaoPg) {
        this.valorPago = fracaoPg;
    }

    public int getTipoNo() {
        return tipoNo;
    }

    public void setTipoNo(int tipoNo) {
        this.tipoNo = tipoNo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean equals(Object o) {
        if (o instanceof ComissaoTree) {
            ComissaoTree ct = (ComissaoTree) o;
            return ct.tipoNo == this.tipoNo && this.getChavePrimaria() == ct.getChavePrimaria() && this.getChavePrimariaColaborador() == ct.getChavePrimariaColaborador();
        } else {
            return false;
        }
    }

    public int getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(int chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public int getChavePrimariaColaborador() {
        return chavePrimariaColaborador;
    }

    public void setChavePrimariaColaborador(int chavePrimariaColaborador) {
        this.chavePrimariaColaborador = chavePrimariaColaborador;
    }

    public String getStyle() {
        if (tipoNo == MODALIDADE || tipoNo == HORARIO) {
            return "tituloDemonstrativo";
        }
        if (tipoNo == COLABORADOR || tipoNo == TURMA) {
            return "tituloBold";
        }
        if (tipoNo == PESSOA) {
            return "titulo";
        }
        return "";
    }

    public String getValorPago_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorPago);
    }

    public String getValorComissao_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorComissao);
    }

    public String getPercComissao_Apresentar() {
        if (!UteisValidacao.emptyString(this.getTipoComissao()) &&
                this.getTipoComissao().equalsIgnoreCase("R$")) {
            return Formatador.formatarValorMonetario(comissao);
        } else {
            double porc = Uteis.arredondarForcando2CasasDecimais(comissao);
            Double d = new Double(porc);
            return d.toString() + "%";
        }
    }

    public int getCliente() {
        return cliente;
    }

    public void setCliente(int cliente) {
        this.cliente = cliente;
    }

    public String getDias() {
        Ordenacao.ordenarLista(diasSemana, "numeral");
        String dia = "";
        for (DiaSemana d : diasSemana) {
            dia += "," + d.getCodigo();
        }
        return dia.replaceFirst(",", "");
    }

    public int getQtdAluno() {
        return qtdAluno;
    }

    public void setQtdAluno(int qtdAluno) {
        this.qtdAluno = qtdAluno;
    }

    public String getCodigoPai() {
        String[] codigoSeparado = codigoAgrupador.split("\\.");
        if (codigoSeparado.length == 1) {
            return "";
        } else {
            int i = 0;
            StringBuilder codigoPai = new StringBuilder();
            while (i < (codigoSeparado.length - 1)) {
                codigoPai.append(codigoSeparado[i]);
                if (i != (codigoSeparado.length - 2)) {
                    codigoPai.append(".");
                }
                i++;
            }
            return codigoPai.toString();
        }
    }

    public String getTipoComissao() {
        return tipoComissao;
    }

    public void setTipoComissao(String tipoComissao) {
        this.tipoComissao = tipoComissao;
    }
}

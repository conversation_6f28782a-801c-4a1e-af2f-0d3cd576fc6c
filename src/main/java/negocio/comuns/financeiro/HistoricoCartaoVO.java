package negocio.comuns.financeiro;

/**
 * Created by <PERSON> on 25/05/2015.
 */

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;


public class HistoricoCartaoVO extends SuperVO {

    private Integer codigo;
    private CartaoCreditoVO cartao = new CartaoCreditoVO();
    private MovContaVO movConta = new MovContaVO();
    private Date dataInicio;
    private Date dataFim;
    private LoteVO lote = new LoteVO();
    private boolean credito = true;

    public boolean isCredito() {
        return credito;
    }

    public void setCredito(boolean credito) {
        this.credito = credito;
    }

    public String getCustodiaDe(){
        if(getMovConta() != null && getMovConta().getTipoOperacaoLancamento() != null){
            switch (getMovConta().getTipoOperacaoLancamento()) {
                case PAGAMENTO:
                    return getMovConta().getPessoaVO().getNome();
                default:
                    return getMovConta().getContaVO().getDescricao();
            }
        }
        return "";
    }


    public Integer getCodigo() {
        return codigo;
    }
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
    public CartaoCreditoVO getCartao() {
        return cartao;
    }
    public void setCartao(CartaoCreditoVO cartao) {
        this.cartao = cartao;
    }
    public MovContaVO getMovConta() {
        return movConta;
    }
    public void setMovConta(MovContaVO movConta) {
        this.movConta = movConta;
    }
    public Date getDataInicio() {
        return dataInicio;
    }

    public String getDataInicioApresentar() {
        return Uteis.getDataAplicandoFormatacao(dataInicio, "dd/MM/yyyy HH:mm:ss");
    }
    public String getDataFimApresentar() {
        return Uteis.getDataAplicandoFormatacao(dataFim, "dd/MM/yyyy HH:mm:ss");
    }
    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }
    public Date getDataFim() {
        return dataFim;
    }
    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }
     public void setLote(LoteVO lote) {
        this.lote = lote;
    }
    public LoteVO getLote() {
        return lote;
    }
}

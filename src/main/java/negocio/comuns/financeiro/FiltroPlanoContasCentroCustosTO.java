package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;

import negocio.comuns.utilitarias.UteisValidacao;

public class FiltroPlanoContasCentroCustosTO extends SuperTO {

    private static final long serialVersionUID = -1338302354322299448L;
    private List<ObjetoTreeTO> listaTree;
    private List<Integer> codigosPlanosSelecionados;
    private int contAjusteTree = 0;
    private String codigosConcatenados;

    public List<Integer> codigoSelecionados() {
        List<Integer> codigos = new ArrayList<Integer>();
        if (this.getCodigosConcatenados().indexOf(";") > -1) {
            String[] split = this.getCodigosConcatenados().split(";");
            for (String cod : split) {
                codigos.add(Integer.parseInt(cod));
            }
        }
        return codigos;
    }

    public String nomesSelecionados(String tipoFiltro) {
        List<Integer> codigoSelecionados = codigoSelecionados();
        String selecionados = "";
        if (listaTree != null){
            for (ObjetoTreeTO obj : listaTree) {
                if (codigoSelecionados.contains(obj.getCodigoEntidade())) {
                    selecionados += "," + obj.getNome();
                }
            }
        }
        if (!UteisValidacao.emptyString(selecionados)) {
            selecionados = " | "+tipoFiltro+": " + selecionados.replaceFirst(",", "");
        }
        return selecionados;
    }

    /**
     * Responsável por ajustar o código agrupador para evitar que mesmo em arvores diferentes
     * haja dois códigos iguais. Isso porque a função jquery que monta a tree view expande/retrai
     * a linha, faz isso pelo id do tr. Portanto se tiver mais de uma arvore na tela e dois ou mais
     * ids iguais, vão ocorrer incosistências nas arvores.
     * <AUTHOR> 19/08/2011
     */
    public void ajustarCodigoAgrupador(ObjetoTreeTO obj) {
        String codigoAgrupador = obj.getCodigoAgrupador();
        //obter codigo pai
        Integer codigoPai = Integer.valueOf(codigoAgrupador.substring(0, 3));
        //verificar  se o código indica objeto filhos
        String filhos = codigoAgrupador.substring(3);
        //se o objeto indicar pai, atualizar o contador do filtro
        if (filhos.isEmpty()) {
            contAjusteTree++;
        }
        //a regra para ajuste do código é o tamanho do demonstrativo + o contador dos filtros + o código pai
//        codigoPai = getListaDFBrowser().size() + contAjusteTree;
        codigoPai = contAjusteTree;
        codigoAgrupador = codigoPai.toString();
        //adicionar zeros ao código do pai
        while (codigoAgrupador.length() < 3) {
            codigoAgrupador = "0" + codigoAgrupador;
        }
        //adicionar os filhos
        codigoAgrupador += filhos;
        //atualizar o código agrupador
        obj.setCodigoAgrupador(codigoAgrupador);
    }

    /**
     * Monta a arvore dos planos de contas
     * <AUTHOR> 18/08/2011
     * @throws Exception
     */
    public void listarPlanoContas(List<PlanoContaTO> planos) throws Exception {
        this.setListaTree(new ArrayList<ObjetoTreeTO>());
        //consultar os planos de contas
        for (PlanoContaTO plano : planos) {
            ObjetoTreeTO obj = new ObjetoTreeTO();
            obj.setCodigoAgrupador(plano.getCodigoPlano());
            ajustarCodigoAgrupador(obj);
            //marcar como selecionado se o código estiver contido na lista de codigos selecionados
            //isto pq a caixa de filtros é renderizada sempre que o botão 'visualizar' é clicado
            if (this.getCodigosPlanosSelecionados().contains(obj.getCodigoEntidade())) {
                obj.setSelecionado(true);
            }
            obj.setCodigoEntidade(plano.getCodigo());
            obj.setNome(plano.getDescricao());
            this.getListaTree().add(obj);
        }
    }

    public void listarCentroCustos(List<CentroCustoTO> centroCustos) throws Exception {
        this.setListaTree(new ArrayList<ObjetoTreeTO>());
        //consultar os planos de contas
        for (CentroCustoTO plano : centroCustos) {
            ObjetoTreeTO obj = new ObjetoTreeTO();
            obj.setCodigoAgrupador(plano.getCodigoCentro());
            ajustarCodigoAgrupador(obj);
            //marcar como selecionado se o código estiver contido na lista de codigos selecionados
            //isto pq a caixa de filtros é renderizada sempre que o botão 'visualizar' é clicado
            if (this.getCodigosPlanosSelecionados().contains(obj.getCodigoEntidade())) {
                obj.setSelecionado(true);
            }
            obj.setCodigoEntidade(plano.getCodigo());
            obj.setNome(plano.getDescricao());
            this.getListaTree().add(obj);
        }
    }

    /**
     * @return the codigosPlanosSelecionados
     */
    public List<Integer> getCodigosPlanosSelecionados() {
        if (codigosPlanosSelecionados == null) {
            codigosPlanosSelecionados = new ArrayList<Integer>();
        }
        return codigosPlanosSelecionados;
    }

    /**
     * @param codigosPlanosSelecionados the codigosPlanosSelecionados to set
     */
    public void setCodigosPlanosSelecionados(List<Integer> codigosPlanosSelecionados) {
        this.codigosPlanosSelecionados = codigosPlanosSelecionados;
    }

    /**
     * @return the listaTree
     */
    public List<ObjetoTreeTO> getListaTree() {
        if (listaTree == null) {
            listaTree = new ArrayList<ObjetoTreeTO>();
        }
        return listaTree;
    }

    public void setListaTree(List<ObjetoTreeTO> lista) {
        listaTree = lista;

    }

    /**
     * @param codigosConcatenados the codigosConcatenados to set
     */
    public void setCodigosConcatenados(String codigosConcatenados) {
        this.codigosConcatenados = codigosConcatenados;
    }

    /**
     * @return the codigosConcatenados
     */
    public String getCodigosConcatenados() {
        if (codigosConcatenados == null) {
            codigosConcatenados = "";
        }
        return codigosConcatenados;
    }
}

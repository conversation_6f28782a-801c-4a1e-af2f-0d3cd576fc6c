package negocio.comuns.financeiro.webservice;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import controle.financeiro.CentroCustosControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.AnexoMovContaVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.ResumoFormaPagamentoTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.CentroCusto;
import negocio.facade.jdbc.financeiro.PlanoConta;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.annotation.Resource;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.xml.ws.WebServiceContext;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Rafael on 20/02/2017.
 */
@WebService(name = "FinanceiroWS", serviceName = "FinanceiroWS")
public class FinanceiroWS {

    @Resource
    private WebServiceContext context;

    @WebMethod(operationName = "inserirLancamento")
    public String inserirLancamento(@WebParam(name = "key") String key,
                                    @WebParam(name = "empresa") int empresa,
                                    @WebParam(name = "descricao") String descricao,
                                    @WebParam(name = "tipo") Integer tipo,
                                    @WebParam(name = "favorecido") Integer favorecido,
                                    @WebParam(name = "usuario") String usuario,
                                    @WebParam(name = "vencimento") String vencimento,
                                    @WebParam(name = "valor") Double valor,
                                    @WebParam(name = "anexos") String anexosJSON) {
        try {
            MovContaVO movContaVO = new MovContaVO();
            movContaVO.setEmpresaVO(new EmpresaVO());
            movContaVO.getEmpresaVO().setCodigo(empresa);
            movContaVO.setDataLancamento(Uteis.getDate(vencimento));
            movContaVO.setDataCompetencia(Uteis.getDate(vencimento));
            movContaVO.setDataVencimento(Uteis.getDate(vencimento));
            movContaVO.setValor(valor);
            movContaVO.setApp(Boolean.TRUE);
            movContaVO.setDescricao(descricao);
            movContaVO.setTipoOperacaoLancamento(TipoES.getTipoPadrao(tipo).equals(TipoES.ENTRADA) ? TipoOperacaoLancamento.RECEBIMENTO : TipoOperacaoLancamento.PAGAMENTO);
            movContaVO.setPessoaVO(new PessoaVO());
            movContaVO.setValidarFavorecido(false);
            movContaVO.getPessoaVO().setCodigo(favorecido);
            movContaVO.setUsuarioVO(DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarPorUsername(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            MovContaRateioVO rateioVO = new MovContaRateioVO();
            movContaVO.setAnexos(new ArrayList<AnexoMovContaVO>());
            if (!UteisValidacao.emptyString(anexosJSON)) {
                JSONObject jsonObject = new JSONObject(anexosJSON);
                Integer codFormaPagamento = jsonObject.optInt("formaPagamento");
                Integer codPlanoConta = jsonObject.optInt("planoConta");
                Integer codCentroCusto = jsonObject.optInt("centroCusto");
                String observacoes = jsonObject.optString("observacoes");

                if (codFormaPagamento != null) {
                    FormaPagamentoVO formaPagamentoVO = DaoAuxiliar.retornarAcessoControle(key).getFormaPagamentoDao().
                            consultarPorCodigo(codFormaPagamento, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS).get(0);
                    rateioVO.setFormaPagamentoVO(formaPagamentoVO);
                }
                if (observacoes != null) {
                    movContaVO.setObservacoes(observacoes);
                }
                if (codCentroCusto != null) {
                    CentroCusto centroCustos = new CentroCusto(DaoAuxiliar.retornarAcessoControle(key).getCon());
                    CentroCustoTO centroCustoTO = centroCustos.consultarPorChavePrimaria(codCentroCusto);
                    rateioVO.setCentroCustoVO(centroCustoTO);
                }
                if (codPlanoConta != null) {
                    PlanoConta planoConta = new PlanoConta(DaoAuxiliar.retornarAcessoControle(key).getCon());
                    PlanoContaTO planoContaTO = planoConta.consultarPorChavePrimaria(codPlanoConta);
                    rateioVO.setPlanoContaVO(planoContaTO);
                }

                JSONArray anexos = new JSONArray(UteisValidacao.emptyString(jsonObject.optString("anexos")) ? "[]" : jsonObject.optString("anexos"));
                for (int e = 0; e < anexos.length(); e++) {
                    AnexoMovContaVO obj = new AnexoMovContaVO();
                    obj.setDataSource(anexos.getString(e));
                    obj.setDia(Calendario.hoje());
                    movContaVO.getAnexos().add(obj);
                }
            }
            rateioVO.setDescricao(descricao);
            rateioVO.setTipoES(TipoES.getTipoPadrao(tipo));
            rateioVO.setValor(valor);
            movContaVO.setMovContaRateios(new ArrayList<MovContaRateioVO>());
            movContaVO.getMovContaRateios().add(rateioVO);
            DaoAuxiliar.retornarAcessoControle(key).getMovContaDao().incluir(movContaVO, 0, false, null);
            return "OK";
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @WebMethod(operationName = "consultarFavorecidos")
    public String consultarFavorecidos(@WebParam(name = "key") String key,
                                       @WebParam(name = "empresa") int empresa,
                                       @WebParam(name = "valor") String valor) {
        try {
            List<PessoaVO> pessoaVOS = DaoAuxiliar.retornarAcessoControle(key).getPessoaDao().
                    consultarPorNomePessoaComLimiteFinanceiro(empresa, valor, false, 10, false, Uteis.NIVELMONTARDADOS_TIPOPESSOA);
            JSONArray objs = new JSONArray();
            for (PessoaVO pessoaVO : pessoaVOS) {
                JSONObject obj = new JSONObject();
                obj.put("codigo", pessoaVO.getCodigo());
                obj.put("cnpj", pessoaVO.getCnpj());
                obj.put("cpf", pessoaVO.getCfp());
                obj.put("nome", pessoaVO.getNome());
                obj.put("tipo", pessoaVO.getTipoPessoa());
                objs.put(obj);
            }
            return objs.toString();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }
}

package negocio.comuns.financeiro;

import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.apf.APF;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 05/11/2020
 */
public class TransacaoPactoPayVO extends TransacaoVO {

    public String getValorCodigoExterno() {
        return getCodigoExterno();
    }

    public String getCartaoMascarado() {
        try {
            return getValorCartaoMascarado();
        } catch (Exception e) {
            return "";
        }
    }

    public String getValorCartaoMascarado() {
        try {
            JSONObject obj = new JSONObject(getOutrasInformacoes());
            return obj.optString("cartaoMascarado");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getValorUltimaTransacaoAprovada() {
        return getValorAtributoResposta(APF.Transacao);
    }

    public String getResultadoRequisicao() {
        try {
            JSONObject responstaJSON = new JSONObject(getParamsResposta());
            return responstaJSON.getJSONObject("transacaoResultado").getString("motivoRetorno");
        } catch (Exception ex) {
            ex.printStackTrace();
            return "Desconhecido";
        }
    }

    public String getAutorizacao() {
        try {
            JSONObject responstaJSON = new JSONObject(getParamsResposta());
            return responstaJSON.getJSONObject("transacaoResultado").getString("autorizacao");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getPaymentId() {
        try {
            JSONObject responstaJSON = new JSONObject(getParamsResposta());
            return responstaJSON.getJSONObject("transacaoResultado").getString("idTransacaoAdquirente");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getBandeira() {
        try {
            JSONObject obj = new JSONObject(getOutrasInformacoes());
            String band = obj.optString("cartaoBandeira");
            if (UteisValidacao.emptyString(band)) {
                band = obj.optString("band");
            }
            return band.toUpperCase();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getValorAtributoResposta(String nomeAtributo) {
        JSONObject responstaJSON = new JSONObject(getParamsResposta());
        return responstaJSON.getJSONObject("transacaoResultado").getString("id");
    }

    public String getValorAtributoCancelamento(String nomeAtributo) {
        if (!UteisValidacao.emptyString(getResultadoCancelamento())) {
            try {
                JSONObject json = new JSONObject(getResultadoCancelamento());
                if (json.has("message")) {
                    return json.getString("message");
                }
            } catch (Exception e) {
            }
        }
        return "";
    }

    public String getCodErroExterno() {
        try {
            JSONObject responstaJSON = new JSONObject(getParamsResposta());
            return responstaJSON.getJSONObject("transacaoResultado").getString("codigoRetorno");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getNSU() {
        try {
            JSONObject responstaJSON = new JSONObject(getParamsResposta());
            return responstaJSON.getJSONObject("transacaoResultado").getString("nsu");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getTID() {
        try {
            JSONObject responstaJSON = new JSONObject(getParamsResposta());
            return responstaJSON.getJSONObject("transacaoResultado").getString("tid");
        } catch (Exception ex) {
            return "";
        }
    }
}

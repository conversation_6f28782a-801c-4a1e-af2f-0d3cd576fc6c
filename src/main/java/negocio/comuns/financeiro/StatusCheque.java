package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;

/**
 * <AUTHOR>
 *
 */
public enum StatusCheque {
	CUSTODIA(1,"Custódia"), 
	COMPENSADO(2,"Compensado"), 
	DEVOLVIDO(3,"Devolvido"), 
	TRANSFERENCIA(4,"Transferência"),
	DEPOSITADO(5, "Depositado"), 
	RETIRAR_LOTES(6, "Retirado dos lotes"),
	CHEQUE_DEVOLVIDO_PAGO(7, "Pagamento de cheque devolvido");
	
	private int codigo;
	private String descricao;
	
	StatusCheque(int codigo, String descricao){
		this.setCodigo(codigo);
		this.setDescricao(descricao);
	}
	
	public static StatusCheque getStatusCheque(final int codigo) {
		StatusCheque statusCheque = null;
		for (StatusCheque status : StatusCheque.values()) {
			if (status.getCodigo() == codigo){
				statusCheque = status;
			}
		}
		return statusCheque;
	}
	
	public static List<SelectItem> getListaSelectItem(){
		List<SelectItem> lista = new ArrayList<SelectItem>();
		for(StatusCheque status : StatusCheque.values()){
			lista.add(new SelectItem(status.codigo, status.descricao));
		}
		return lista; 
	}

	public void setCodigo(int codigo) {
		this.codigo = codigo;
	}

	public int getCodigo() {
		return codigo;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public String getDescricao() {
		return descricao;
	}
}

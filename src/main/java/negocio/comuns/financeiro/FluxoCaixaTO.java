package negocio.comuns.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.*;

/*
 * <AUTHOR>
 */
public class FluxoCaixaTO extends SuperTO {

    private Integer codigo;
    private Integer codigoMovConta;
    private Integer codigoPessoa;
    private String agrupador;
    private String descricao;
    private Date dia;
    private Double entradas;
    private Double saidas;
    private Double saldo;
    private Double saldoRealizado = null;
    private PlanoContaTO planoConta;
    private TipoES tipoES;
    private Integer codtipoFormaPagto;
    private Integer conta;
    private String tipoFormaPagto;
    private String nivelPai = null;
    private List<FluxoCaixaTO> listaFluxoCaixa;
    private boolean planoAgrupador = false;
    private boolean entradaDiferente = false;
    private Integer index;
    private boolean saidaDiferente = false;
    private Map<String, Double> formasEntrada = new HashMap<String, Double>();
    private Map<String, Double> formasSaida = new HashMap<String, Double>();
    private Map<Integer, Double> contas = new HashMap<Integer, Double>();
    private String tableFormasEntrada = null;
    private String tableFormasSaida = null;
    private TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobranca;

    private Double entradasTipoAutorizacaoNenhum = 0.0;
    private Double entradasTipoAutorizacaoCartaoCredito = 0.0;
    private Double entradasTipoAutorizacaoDebitoConta = 0.0;
    private Double entradasTipoAutorizacaoBoletoBancario = 0.0;

    public FluxoCaixaTO() {
    }

    public String getTableFormasEntrada(){
        if(tableFormasEntrada == null){
            if(formasEntrada == null){
                tableFormasEntrada = "";
            }
            tableFormasEntrada = "<table>";
            for(String k : formasEntrada.keySet()){
                tableFormasEntrada += "<tr><td><b>"+k+":</b></td>";
                tableFormasEntrada += "<td>"+Formatador.formatarValorMonetarioSemMoedaMantendoSinal(formasEntrada.get(k))+"</td></tr>";
            }
            tableFormasEntrada +="</table>";
        }
        return tableFormasEntrada;
    }

    public String getTableFormasSaida(){
        if(tableFormasSaida == null){
            if(formasSaida == null){
                tableFormasSaida = "";
            }
            tableFormasSaida = "<table>";
            for(String k : formasSaida.keySet()){
                tableFormasSaida += "<tr><td><b>"+k+":</b></td>";
                tableFormasSaida += "<td>"+Formatador.formatarValorMonetarioSemMoedaMantendoSinal(formasSaida.get(k))+"</td></tr>";
            }
            tableFormasSaida +="</table>";
        }
        return tableFormasSaida;
    }

    public FluxoCaixaTO(String agrupador, String planoContas, Date dia) {
        this.agrupador = agrupador;
        this.dia = dia;
        this.planoConta = new PlanoContaTO();
        this.planoConta.setDescricao(planoContas);
        planoAgrupador = true;
    }

    public String getNomePlano(){
        return planoConta == null ? "" : planoConta.getDescricao();
    }

    public String getId(){
        return agrupador == null ? dia == null ?  "" : String.valueOf(dia.getTime()) : agrupador.replaceAll("\\.", "\\_");
    }

    public String getNivelPai(){
        if(nivelPai == null){
            nivelPai = agrupador == null || agrupador.length() < 4 || agrupador.equals("NAOINFORMADO") ? "" :
                    agrupador.substring(0,3).replaceAll("\\.", "\\_");
        }
        return nivelPai;
    }

    public Integer getIndex(){
        if(index == null){
            index = UteisValidacao.emptyString(getAgrupador()) || getAgrupador().equals("NAOINFORMADO")  ?
                    0 : getAgrupador().length() - getAgrupador().replaceAll("\\.", "").length();
        }
        return index;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }


    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Double getEntradas() {
        if (entradas == null) {
            entradas = 0.0;
        }
        return entradas;
    }

    public void setEntradas(Double entradas) {
        this.entradas = entradas;
    }

    public Double getSaidas() {
        if (saidas == null) {
            saidas = 0.0;
        }
        return saidas;
    }

    public void setSaidas(Double saidas) {
        this.saidas = saidas;
    }

    public Double getSaldo() {
        if (saldo == null) {
            saldo = 0.0;
        }
        return saldo;
    }

    public void setSaldo(Double saldo) {
        this.saldo = saldo;
    }

    public PlanoContaTO getPlanoConta() {
        if (planoConta == null) {
            planoConta = new PlanoContaTO();
        }
        return planoConta;
    }

    public void setPlanoConta(PlanoContaTO planoConta) {
        this.planoConta = planoConta;
    }

    public TipoES getTipoES() {
        return tipoES;
    }

    public void setTipoES(TipoES tipoES) {
        this.tipoES = tipoES;
    }

    public String getTotal_Apresentar() {

        return UteisValidacao.emptyNumber(getTotal()) ? "" : Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotal());
    }

    public String getEntrada_Apresentar() {
        return UteisValidacao.emptyNumber(getEntradas()) ? "" : Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getEntradas());
    }


    public String getSaida_Apresentar() {
        return UteisValidacao.emptyNumber(getSaidas()) ? "" : Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getSaidas());
    }

    public String getSaldo_Apresentar() {

        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getSaldo());
    }

    public String getSaldoRealizado_Apresentar() {

        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getSaldoRealizado());
    }

    public List<FluxoCaixaTO> getListaFluxoCaixa() {
        if (listaFluxoCaixa == null) {
            listaFluxoCaixa = new ArrayList<FluxoCaixaTO>();
        }
        return listaFluxoCaixa;
    }

    public void setListaFluxoCaixa(List<FluxoCaixaTO> listaFluxoCaixa) {
        this.listaFluxoCaixa = listaFluxoCaixa;
    }

    public boolean isApresentarNivel() {
        return getListaFluxoCaixa().size() > 0;
    }

    public Double getTotal() {
        return getEntradas() + getSaidas();
    }

    public String getDiaApresentar(){
        return dia == null ? "" : Uteis.getDataAplicandoFormatacao(dia, "dd/MM");
    }

    public String getDiaAnoApresentar(){
        return dia == null ? "" : Uteis.getDataAplicandoFormatacao(dia, "dd/MM/yyyy");
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getCodigoMovConta() {
        return codigoMovConta;
    }

    public void setCodigoMovConta(Integer codigoMovConta) {
        this.codigoMovConta = codigoMovConta;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public boolean isEntradaDiferente() {
        return entradaDiferente;
    }

    public void setEntradaDiferente(boolean entradaDiferente) {
        this.entradaDiferente = entradaDiferente;
    }

    public boolean isSaidaDiferente() {
        return saidaDiferente;
    }

    public void setSaidaDiferente(boolean saidaDiferente) {
        this.saidaDiferente = saidaDiferente;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if(obj instanceof FluxoCaixaTO){
            FluxoCaixaTO aux = (FluxoCaixaTO) obj;
            return this.hashCode() == aux.hashCode();
        }
        return false;
    }

    @Override
    public int hashCode() {
        int hash =
                descricao != null && codigoMovConta != null ? (descricao.hashCode() + codigoMovConta.hashCode()
                        + (agrupador == null ? 0 : agrupador.hashCode()) + (dia == null ? 0 : dia.hashCode()) + (entradas == null ? 0 : entradas.hashCode()) + (saidas == null ? 0 : saidas.hashCode()) ) :

                descricao != null && codigoPessoa != null ? (descricao.hashCode() + codigoPessoa.hashCode()
                        + (agrupador == null ? 0 : agrupador.hashCode()) + (dia == null ? 0 : dia.hashCode()) + (entradas == null ? 0 : entradas.hashCode()) + (saidas == null ? 0 : saidas.hashCode()) ) :

                dia != null && agrupador != null ? (agrupador.hashCode() + dia.hashCode()) :
                        dia == null ? 0 : dia.hashCode();
        return hash;
    }

    public Double getSaldoRealizado() {
        return saldoRealizado;
    }

    public void setSaldoRealizado(Double saldoRealizado) {
        this.saldoRealizado = saldoRealizado;
    }

    public String getAgrupador() {
        return agrupador;
    }

    public void setAgrupador(String agrupador) {
        this.agrupador = agrupador;
    }

    public boolean isPlanoAgrupador() {
        return planoAgrupador;
    }

    public void setPlanoAgrupador(boolean planoAgrupador) {
        this.planoAgrupador = planoAgrupador;
    }

    public void setNivelPai(String nivelPai) {
        this.nivelPai = nivelPai;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Integer getCodtipoFormaPagto() {
        return codtipoFormaPagto;
    }

    public void setCodtipoFormaPagto(Integer codtipoFormaPagto) {
        this.codtipoFormaPagto = codtipoFormaPagto;
    }

    public String getTipoFormaPagto() {
        return tipoFormaPagto;
    }

    public void setTipoFormaPagto(String tipoFormaPagto) {
        this.tipoFormaPagto = tipoFormaPagto;
    }

    public Map<String, Double> getFormasEntrada() {
        return formasEntrada;
    }

    public void setFormasEntrada(Map<String, Double> formasEntrada) {
        this.formasEntrada = formasEntrada;
    }

    public Map<String, Double> getFormasSaida() {
        return formasSaida;
    }

    public void setFormasSaida(Map<String, Double> formasSaida) {
        this.formasSaida = formasSaida;
    }

    public Map<Integer, Double> getContas() {
        return contas;
    }

    public void setContas(Map<Integer, Double> contas) {
        this.contas = contas;
    }

    public Integer getConta() {
        return conta;
    }

    public void setConta(Integer conta) {
        this.conta = conta;
    }

    public TipoAutorizacaoCobrancaEnum getTipoAutorizacaoCobranca() {
        if(tipoAutorizacaoCobranca == null){
            tipoAutorizacaoCobranca = TipoAutorizacaoCobrancaEnum.NENHUM;
        }
        return tipoAutorizacaoCobranca;
    }

    public void setTipoAutorizacaoCobranca(TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobranca) {
        this.tipoAutorizacaoCobranca = tipoAutorizacaoCobranca;
    }

    public Double getEntradasTipoAutorizacaoNenhum() {
        return entradasTipoAutorizacaoNenhum;
    }

    public void setEntradasTipoAutorizacaoNenhum(Double entradasTipoAutorizacaoNenhum) {
        this.entradasTipoAutorizacaoNenhum = entradasTipoAutorizacaoNenhum;
    }

    public Double getEntradasTipoAutorizacaoCartaoCredito() {
        return entradasTipoAutorizacaoCartaoCredito;
    }

    public void setEntradasTipoAutorizacaoCartaoCredito(Double entradasTipoAutorizacaoCartaoCredito) {
        this.entradasTipoAutorizacaoCartaoCredito = entradasTipoAutorizacaoCartaoCredito;
    }

    public Double getEntradasTipoAutorizacaoDebitoConta() {
        return entradasTipoAutorizacaoDebitoConta;
    }

    public void setEntradasTipoAutorizacaoDebitoConta(Double entradasTipoAutorizacaoDebitoConta) {
        this.entradasTipoAutorizacaoDebitoConta = entradasTipoAutorizacaoDebitoConta;
    }

    public Double getEntradasTipoAutorizacaoBoletoBancario() {
        return entradasTipoAutorizacaoBoletoBancario;
    }

    public void setEntradasTipoAutorizacaoBoletoBancario(Double entradasTipoAutorizacaoBoletoBancario) {
        this.entradasTipoAutorizacaoBoletoBancario = entradasTipoAutorizacaoBoletoBancario;
    }
}

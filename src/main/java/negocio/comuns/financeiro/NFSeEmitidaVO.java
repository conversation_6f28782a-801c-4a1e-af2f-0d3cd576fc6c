package negocio.comuns.financeiro;

import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;

import java.util.Date;

/**
 * Created by glauco on 19/02/14
 */
public class NFSeEmitidaVO extends SuperVO {

    private Integer codigo = 0;
    private Integer idRps = null;
    private ReciboPagamentoVO recibo;
    private ChequeVO cheque;
    private CartaoCreditoVO cartaoCredito;
    private MovPagamentoVO movPagamento;
    private MovProdutoVO movProduto;
    private MovContaVO movConta;
    private ContratoVO contrato;
    private String nrNotaManual;
    private Date dataLancamento = null;
    private String idReferencia;
    private Double valor;
    private Integer pessoa;
    private String jsonEnviar;
    private Date dataEnvio; //data que foi enviada para o módulo NFSe
    private Date dataRegistro; //data que o registro foi gerado
    private Date dataEmissao; //data de emissao do RPS
    private Date dataReferencia; //data que a nota deveria ser emitida
    private Integer empresa;
    private boolean notaFamilia = false;
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO;
    private NotaFiscalVO notaFiscalVO;
    private boolean enotas = false;
    private String resultadoEnvio;
    private SituacaoNotaFiscalEnum situacaoNotaFiscal;
    private Integer sequencialFamilia;

    public NFSeEmitidaVO() {

    }

    public NFSeEmitidaVO(Integer codigo) {
        this.codigo = codigo;
    }

    public NFSeEmitidaVO(JSONObject json) throws JSONException {
        this.codigo = json.getInt("codigo");
        this.idRps = json.getInt("idRps");
        if (!json.isNull("recibo")) {
            getRecibo().setCodigo(json.getInt("recibo"));
        }
        if (!json.isNull("cheque")) {
            getCheque().setCodigo(json.getInt("cheque"));
        }
        if (!json.isNull("cartaoCredito")) {
            getCartaoCredito().setCodigo(json.getInt("cartaoCredito"));
        }
        if (!json.isNull("movPagamento")) {
            getMovPagamento().setCodigo(json.getInt("movPagamento"));
        }
        if (!json.isNull("movProduto")) {
            getMovProduto().setCodigo(json.getInt("movProduto"));
        }
        if (!json.isNull("movConta")) {
            getMovConta().setCodigo(json.getInt("movConta"));
        }
        if (!json.isNull("contrato")) {
            getContrato().setCodigo(json.getInt("contrato"));
        }
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ReciboPagamentoVO getRecibo() {
        if (recibo == null) {
            recibo = new ReciboPagamentoVO();
        }
        return recibo;
    }

    public void setRecibo(ReciboPagamentoVO recibo) {
        this.recibo = recibo;
    }

    public ChequeVO getCheque() {
        if (cheque == null) {
            cheque  = new ChequeVO();
        }
        return cheque;
    }

    public void setCheque(ChequeVO cheque) {
        this.cheque = cheque;
    }

    public CartaoCreditoVO getCartaoCredito() {
        if (cartaoCredito == null) {
            cartaoCredito = new CartaoCreditoVO();
        }
        return cartaoCredito;
    }

    public void setCartaoCredito(CartaoCreditoVO cartaoCredito) {
        this.cartaoCredito = cartaoCredito;
    }

    public MovPagamentoVO getMovPagamento() {
        if (movPagamento == null) {
            movPagamento = new MovPagamentoVO();
        }
        return movPagamento;
    }

    public void setMovPagamento(MovPagamentoVO movPagamento) {
        this.movPagamento = movPagamento;
    }

    public MovProdutoVO getMovProduto() {
        if (movProduto == null) {
            movProduto = new MovProdutoVO();
        }
        return movProduto;
    }

    public void setMovProdutoVO(MovProdutoVO movProduto) {
        this.movProduto = movProduto;
    }

    public Integer getIdRps() {
        return idRps;
    }

    public void setIdRps(Integer idRps) {
        this.idRps = idRps;
    }

    public ContratoVO getContrato() {
        if (contrato == null) {
            contrato = new ContratoVO();
        }
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public MovContaVO getMovConta() {
        if (movConta == null) {
            movConta = new MovContaVO();
        }
        return movConta;
    }

    public void setMovConta(MovContaVO movConta) {
        this.movConta = movConta;
    }

    public JSONObject toJSON() {
        JSONObject o = new JSONObject();
        try {
            o.put("codigo", this.getCodigo());
            o.put("idRps", this.getIdRps());
            if (this.getRecibo().getCodigo() != 0) {
                o.put("recibo", this.getRecibo().getCodigo());
            }
            if (this.getCheque().getCodigo() != 0) {
                o.put("cheque", this.getCheque().getCodigo());
            }
            if (this.getCartaoCredito().getCodigo() != 0) {
                o.put("cartaoCredito", this.getCartaoCredito().getCodigo());
            }
            if (this.getMovPagamento().getCodigo() != 0) {
                o.put("movPagamento", this.getMovPagamento().getCodigo());
            }
            if (this.getMovProduto().getCodigo() != 0) {
                o.put("movProduto", this.getMovProduto().getCodigo());
            }
            if (this.getMovConta().getCodigo() != 0) {
                o.put("movConta", this.getMovConta().getCodigo());
            }
            if (this.getContrato().getCodigo() != 0) {
                o.put("contrato", this.getContrato().getCodigo());
            }
        } catch (Exception e) {
        }
        return o;
    }

    @Override
    public int hashCode(){
        return 1;
    }

    @Override
    public boolean equals(Object obj){
        if ((obj == null) || (!(obj instanceof NFSeEmitidaVO))){
            return false;
        }
        return ((NFSeEmitidaVO) obj).getCodigo().equals(this.codigo);
    }

    public String getNrNotaManual() {
        if (nrNotaManual == null) {
            nrNotaManual = "";
        }
        return nrNotaManual;
    }

    public void setNrNotaManual(String nrNotaManual) {
        this.nrNotaManual = nrNotaManual;
    }

    public void setMovProduto(MovProdutoVO movProduto) {
        this.movProduto = movProduto;
    }


    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getIdReferencia() {
        if (idReferencia == null) {
            idReferencia = "";
        }
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Date getDataEnvio() {
        return dataEnvio;
    }

    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;
    }

    public Integer getPessoa() {
        if (pessoa == null) {
            pessoa = 0;
        }
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getJsonEnviar() {
        if (jsonEnviar == null) {
            jsonEnviar = "";
        }
        return jsonEnviar;
    }

    public void setJsonEnviar(String jsonEnviar) {
        this.jsonEnviar = jsonEnviar;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public Date getDataReferencia() {
        return dataReferencia;
    }

    public void setDataReferencia(Date dataReferencia) {
        this.dataReferencia = dataReferencia;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public boolean isNotaFamilia() {
        return notaFamilia;
    }

    public void setNotaFamilia(boolean notaFamilia) {
        this.notaFamilia = notaFamilia;
    }

    public NotaFiscalVO getNotaFiscalVO() {
        if (notaFiscalVO == null) {
            notaFiscalVO = new NotaFiscalVO();
        }
        return notaFiscalVO;
    }

    public void setNotaFiscalVO(NotaFiscalVO notaFiscalVO) {
        this.notaFiscalVO = notaFiscalVO;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalVO() {
        if (configuracaoNotaFiscalVO == null) {
            configuracaoNotaFiscalVO = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalVO;
    }

    public void setConfiguracaoNotaFiscalVO(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) {
        this.configuracaoNotaFiscalVO = configuracaoNotaFiscalVO;
    }

    public boolean isEnotas() {
        return enotas;
    }

    public void setEnotas(boolean enotas) {
        this.enotas = enotas;
    }

    public String getResultadoEnvio() {
        if (resultadoEnvio == null) {
            resultadoEnvio = "";
        }
        return resultadoEnvio;
    }

    public void setResultadoEnvio(String resultadoEnvio) {
        this.resultadoEnvio = resultadoEnvio;
    }

    public SituacaoNotaFiscalEnum getSituacaoNotaFiscal() {
        return situacaoNotaFiscal;
    }

    public void setSituacaoNotaFiscal(SituacaoNotaFiscalEnum situacaoNotaFiscal) {
        this.situacaoNotaFiscal = situacaoNotaFiscal;
    }

    public Integer getSequencialFamilia() {
        return sequencialFamilia;
    }

    public void setSequencialFamilia(Integer sequencialFamilia) {
        this.sequencialFamilia = sequencialFamilia;
    }
}

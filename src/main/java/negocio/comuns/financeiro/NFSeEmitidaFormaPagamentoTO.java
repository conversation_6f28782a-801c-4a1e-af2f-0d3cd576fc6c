package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;

/**
 * Created by <PERSON><PERSON> on 09/07/2018
 */
public class NFSeEmitidaFormaPagamentoTO extends SuperTO {

    private String siglaTipoFormaPagamento;
    private Double valor;

    public String getSiglaTipoFormaPagamento() {
        if (siglaTipoFormaPagamento == null) {
            siglaTipoFormaPagamento = "";
        }
        return siglaTipoFormaPagamento;
    }

    public void setSiglaTipoFormaPagamento(String siglaTipoFormaPagamento) {
        this.siglaTipoFormaPagamento = siglaTipoFormaPagamento;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }


}

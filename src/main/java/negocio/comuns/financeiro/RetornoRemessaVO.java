/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import java.math.RoundingMode;
import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import negocio.comuns.utilitarias.Uteis;

/**
 * <AUTHOR>
 */
public class RetornoRemessaVO extends SuperVO {

    private Integer codigo;
    private String nomeArquivo;
    private Date dataPrevistaCredito;
    private Integer quantidadeDeItens;
    private Double valorTotalDoArquivo;
    private Integer quantidadeSucesso;
    private Double valorSucesso;
    private Integer quantidadeErro;
    private Double valorErro;
    private Date dataProcessamento;
    private Date dataUltimoProcessamento;
    private String arquivoRetorno;
    private Map<Integer,Double> valorTarifasEmpresa;
    private String empresaSolicitante = "";
    private String userSolicitante = "";

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public Date getDataPrevistaCredito() {
        return dataPrevistaCredito;
    }

    public void setDataPrevistaCredito(Date dataPrevistaCredito) {
        this.dataPrevistaCredito = dataPrevistaCredito;
    }

    public Integer getQuantidadeDeItens() {
        if (quantidadeDeItens == null) {
            quantidadeDeItens = 0;
        }
        return quantidadeDeItens;
    }

    public void setQuantidadeDeItens(Integer quantidadeDeItens) {
        this.quantidadeDeItens = quantidadeDeItens;
    }

    public Double getValorTotalDoArquivo() {
        if (valorTotalDoArquivo == null) {
            valorTotalDoArquivo = 0.0;
        }
        return valorTotalDoArquivo;
    }

    public void setValorTotalDoArquivo(Double valorTotalDoArquivo) {
        this.valorTotalDoArquivo = valorTotalDoArquivo;
    }

    public Integer getQuantidadeSucesso() {
        if (quantidadeSucesso == null) {
            quantidadeSucesso = 0;
        }
        return quantidadeSucesso;
    }

    public void setQuantidadeSucesso(Integer quantidadeSucesso) {
        this.quantidadeSucesso = quantidadeSucesso;
    }

    public Double getValorSucesso() {
        if (valorSucesso == null) {
            valorSucesso = 0.0;
        }
        return valorSucesso;
    }

    public void setValorSucesso(Double valorSucesso) {
        this.valorSucesso = valorSucesso;
    }

    public Integer getQuantidadeErro() {
        if (quantidadeErro == null) {
            quantidadeErro = 0;
        }
        return quantidadeErro;
    }

    public void setQuantidadeErro(Integer quantidadeErro) {
        this.quantidadeErro = quantidadeErro;
    }

    public Double getValorErro() {
        if (valorErro == null) {
            valorErro = 0.0;
        }
        return valorErro;
    }

    public void setValorErro(Double valorErro) {
        this.valorErro = valorErro;
    }

    public Date getDataProcessamento() {
        return dataProcessamento;
    }

    public void setDataProcessamento(Date dataProcessamento) {
        this.dataProcessamento = dataProcessamento;
    }

    public Date getDataUltimoProcessamento() {
        return dataUltimoProcessamento;
    }

    public void setDataUltimoProcessamento(Date dataUltimoProcessamento) {
        this.dataUltimoProcessamento = dataUltimoProcessamento;
    }

    public String getArquivoRetorno() {
        return arquivoRetorno;
    }

    public void setArquivoRetorno(String arquivoRetorno) {
        this.arquivoRetorno = arquivoRetorno;
    }

    public void incrementarErro() {
        if (quantidadeErro == null) {
            quantidadeErro = 0;
        }
        quantidadeErro++;
    }

    public void incrementarValorErro(double valorBoletoNaoProcessado) {
        if (valorErro == null) {
            valorErro = 0.0;
        }
        valorErro += valorBoletoNaoProcessado;
    }

    public void incrementarSucesso() {
        if (quantidadeSucesso == null) {
            quantidadeSucesso = 0;
        }
        quantidadeSucesso++;
    }

    public void incrementarValorSucesso(double valorBoleto) {
        if (valorSucesso == null) {
            valorSucesso = 0.0;
        }
        valorSucesso += valorBoleto;
    }

    public void incrementarValorTarifas(final Integer empresa, double valorDaTarifa) {
        if(valorTarifasEmpresa.containsKey(empresa)){
            valorTarifasEmpresa.put(empresa, Uteis.arredondarForcando2CasasDecimais(valorTarifasEmpresa.get(empresa)+ valorDaTarifa));
        } else {
            valorTarifasEmpresa.put(empresa, valorDaTarifa);
        }
    }

    public void limpar() {
        setQuantidadeSucesso(0);
        setQuantidadeErro(0);
        setValorSucesso(0.0);
        setValorErro(0.0);
        setValorTarifasEmpresa(new HashMap<Integer, Double>());
    }

    public Map<Integer, Double> getValorTarifasEmpresa() {
        if(valorTarifasEmpresa == null){
            return new HashMap<Integer, Double>();
        }
        return valorTarifasEmpresa;
    }

    public void setValorTarifasEmpresa(Map<Integer, Double> valorTarifasEmpresa) {
        this.valorTarifasEmpresa = valorTarifasEmpresa;
    }

    public String getEmpresaSolicitante() {
        return empresaSolicitante;
    }

    public void setEmpresaSolicitante(String empresaSolicitante) {
        this.empresaSolicitante = empresaSolicitante;
    }

    public String getUserSolicitante() {
        return userSolicitante;
    }

    public void setUserSolicitante(String userSolicitante) {
        this.userSolicitante = userSolicitante;
    }
}

package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

public class TaxaCartaoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private int nrmeses = 0;
    private Double taxa = 0.0;
    @NaoControlarLogAlteracao
    @FKJson
    private FormaPagamentoVO formaPagamentoVO = new FormaPagamentoVO();
    @ChaveEstrangeira
    private FormaPagamentoEmpresaVO formaPagamentoEmpresaVO = new FormaPagamentoEmpresaVO();
    @ChaveEstrangeira
    private AdquirenteVO adquirenteVO = new AdquirenteVO();
    @ChaveEstrangeira
    private OperadoraCartaoVO bandeira;
    private Date vigenciaInicial;
    private Date vigenciaFinal;
    private boolean compensacaoPorTaxa = false;
    private int nrDiasCompensacaoPorTaxa = 0;

    private String produtoVendaSesi = "";

    private String tipodocumentoSesi = "";

    @Override
    public Integer getCodigo() {
        if(codigo == null){
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getNrmeses() {
        return nrmeses;
    }

    public void setNrmeses(int nrmeses) {
        this.nrmeses = nrmeses;
    }

    public Double getTaxa() {
        return taxa;
    }

    public void setTaxa(Double taxa) {
        this.taxa = taxa;
    }

    public FormaPagamentoVO getFormaPagamentoVO() {
        return formaPagamentoVO;
    }

    public void setFormaPagamentoVO(FormaPagamentoVO formaPagamentoVO) {
        this.formaPagamentoVO = formaPagamentoVO;
    }

    public Date getVigenciaInicial() {
        return vigenciaInicial;
    }

    public void setVigenciaInicial(Date vigenciaInicial) {
        this.vigenciaInicial = vigenciaInicial;
    }

    public Date getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(Date vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }

    public String getVigenciaInicial_Apresentar() {
        return Uteis.getData(getVigenciaInicial());
    }

    public String getVigenciaFinal_Apresentar() {
        if (getVigenciaFinal() == null) {
            return "Sem vigência final";
        } else {
            return Uteis.getData(getVigenciaFinal());
        }

    }

    public String getCompensacaoPorTaxa_Apresentar() {
        if(isCompensacaoPorTaxa()){
            return "SIM";
        }else{
            return "NÃO";
        }
    }

    public OperadoraCartaoVO getBandeira() {
        if(bandeira == null){
            bandeira = new OperadoraCartaoVO();
        }
        return bandeira;
    }

    public void setBandeira(OperadoraCartaoVO bandeira) {
        this.bandeira = bandeira;
    }

    public FormaPagamentoEmpresaVO getFormaPagamentoEmpresaVO() {
        return formaPagamentoEmpresaVO;
    }

    public void setFormaPagamentoEmpresaVO(FormaPagamentoEmpresaVO formaPagamentoEmpresaVO) {
        this.formaPagamentoEmpresaVO = formaPagamentoEmpresaVO;
    }

    public AdquirenteVO getAdquirenteVO() {
        if(adquirenteVO == null){
            adquirenteVO = new AdquirenteVO();
        }
        return adquirenteVO;
    }

    public void setAdquirenteVO(AdquirenteVO adquirenteVO) {
        this.adquirenteVO = adquirenteVO;
    }

    public boolean isCompensacaoPorTaxa() {
        return compensacaoPorTaxa;
    }

    public void setCompensacaoPorTaxa(boolean compensacaoPorTaxa) {
        this.compensacaoPorTaxa = compensacaoPorTaxa;
    }

    public int getNrDiasCompensacaoPorTaxa() {
        return nrDiasCompensacaoPorTaxa;
    }

    public void setNrDiasCompensacaoPorTaxa(int nrDiasCompensacaoPorTaxa) {
        this.nrDiasCompensacaoPorTaxa = nrDiasCompensacaoPorTaxa;
    }

    public String getDescricaoParaLog() {
        try {
            return ("Nr Meses=" + this.getNrmeses() + "\n  " +
                    "Taxa= " + this.getTaxa() + "\n  " +
                    (!UteisValidacao.emptyString(this.getAdquirenteVO().getNome()) ? ("Adquirente= " + this.getAdquirenteVO().getNome()) : "" + "\n  " )+
                    "CompensacaoPorTaxa= " + (this.isCompensacaoPorTaxa() ? "SIM" : "NÃO") + "\n  " +
                    "NrDiasCompensacaoPorTaxa= " + this.getNrDiasCompensacaoPorTaxa() + "\n  " +
                    (!UteisValidacao.emptyString(this.getBandeira().getDescricao()) ? ("Bandeira= " + this.getBandeira().getDescricao()) : "" + "\n  " )+
                    (this.getVigenciaInicial() != null ? "Vigencia Inicial= " + Uteis.getData(this.getVigenciaInicial()) : "") + "\n  " +
                    (this.getVigenciaFinal() != null ? "Vigencia Final= " + Uteis.getData(this.getVigenciaFinal()) : "")) + "\n  ";
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    public String getProdutoVendaSesi() {
        return produtoVendaSesi;
    }

    public void setProdutoVendaSesi(String produtoVendaSesi) {
        this.produtoVendaSesi = produtoVendaSesi;
    }

    public String getTipodocumentoSesi() {
        return tipodocumentoSesi;
    }

    public void setTipodocumentoSesi(String tipodocumentoSesi) {
        this.tipodocumentoSesi = tipodocumentoSesi;
    }
}

package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.StatusPinpadEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.stone.connect.DadosPedidoDTO;

import java.util.Date;

public class PinPadPedidoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Date dataRegistro;
    private OpcoesPinpadEnum pinpad;
    private PessoaVO pessoaVO;
    private EmpresaVO empresaVO;
    private ConvenioCobrancaVO convenioCobrancaVO;

    private Double valor;
    private String idExterno;
    private String idExternoCancel;
    private String pdvPinPad;
    private StatusPinpadEnum status;
    private String paramsEnvio;
    private String paramsResp;
    private String paramsRespCancel;
    private Integer pinPadPedidoOrigem;
    private OrigemCobrancaEnum origem;
    private FormaPagamentoVO formaPagamentoVO;

    private MovPagamentoVO movPagamentoVO;
    private ReciboPagamentoVO reciboPagamentoVO;
    private DadosPedidoDTO dadosPedidoDTO;
    private UsuarioVO usuarioVO;
    @NaoControlarLogAlteracao
    private String msg;
    @NaoControlarLogAlteracao
    private String retornoPinpad;
    @NaoControlarLogAlteracao
    private String retornoPinpadOperacao;

    public PinPadPedidoVO() {

    }

    public PinPadPedidoVO(Integer codigo) {
        this.codigo = codigo;
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public String getDataRegistroApresentar() {
        return Uteis.getDataComHHMM(this.getDataRegistro());
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public OpcoesPinpadEnum getPinpad() {
        return pinpad;
    }

    public void setPinpad(OpcoesPinpadEnum pinpad) {
        this.pinpad = pinpad;
    }

    public String getIdExterno() {
        if (idExterno == null) {
            idExterno = "";
        }
        return idExterno;
    }

    public void setIdExterno(String idExterno) {
        this.idExterno = idExterno;
    }

    public StatusPinpadEnum getStatus() {
        return status;
    }

    public void setStatus(StatusPinpadEnum status) {
        this.status = status;
    }

    public String getParamsEnvio() {
        if (paramsEnvio == null) {
            paramsEnvio = "";
        }
        return paramsEnvio;
    }

    public void setParamsEnvio(String paramsEnvio) {
        this.paramsEnvio = paramsEnvio;
    }

    public String getParamsResp() {
        if (paramsResp == null) {
            paramsResp = "";
        }
        return paramsResp;
    }

    public void setParamsResp(String paramsResp) {
        this.paramsResp = paramsResp;
    }

    public String getMsg() {
        if (msg == null) {
            msg = "";
        }
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getPdvPinPad() {
        if (pdvPinPad == null) {
            pdvPinPad = "";
        }
        return pdvPinPad;
    }

    public void setPdvPinPad(String pdvPinPad) {
        this.pdvPinPad = pdvPinPad;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getValorApresentar() {
        if (getValor() == null) {
            return "";
        } else {
            return Formatador.formatarValorMonetario(this.getValor());
        }
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public boolean isPermiteAlterar() {
        return this.getPinpad().equals(OpcoesPinpadEnum.STONE_CONNECT) &&
                this.getStatus().equals(StatusPinpadEnum.AGUARDANDO);
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public String getRetornoPinpad() {
        if (retornoPinpad == null) {
            retornoPinpad = "";
        }
        return retornoPinpad;
    }

    public void setRetornoPinpad(String retornoPinpad) {
        this.retornoPinpad = retornoPinpad;
    }

    public String getRetornoPinpadOperacao() {
        if (retornoPinpadOperacao == null) {
            retornoPinpadOperacao = "";
        }
        return retornoPinpadOperacao;
    }

    public void setRetornoPinpadOperacao(String retornoPinpadOperacao) {
        this.retornoPinpadOperacao = retornoPinpadOperacao;
    }

    public OrigemCobrancaEnum getOrigem() {
        if (origem == null) {
            origem = OrigemCobrancaEnum.NENHUM;
        }
        return origem;
    }

    public void setOrigem(OrigemCobrancaEnum origem) {
        this.origem = origem;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        if (movPagamentoVO == null) {
            movPagamentoVO = new MovPagamentoVO();
        }
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public ReciboPagamentoVO getReciboPagamentoVO() {
        if (reciboPagamentoVO == null) {
            reciboPagamentoVO = new ReciboPagamentoVO();
        }
        return reciboPagamentoVO;
    }

    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamentoVO) {
        this.reciboPagamentoVO = reciboPagamentoVO;
    }

    public DadosPedidoDTO getDadosPedidoDTO() {
        if (dadosPedidoDTO == null) {
            dadosPedidoDTO = new DadosPedidoDTO();
        }
        return dadosPedidoDTO;
    }

    public void setDadosPedidoDTO(DadosPedidoDTO dadosPedidoDTO) {
        this.dadosPedidoDTO = dadosPedidoDTO;
    }

    public FormaPagamentoVO getFormaPagamentoVO() {
        if (formaPagamentoVO == null) {
            formaPagamentoVO = new FormaPagamentoVO();
        }
        return formaPagamentoVO;
    }

    public void setFormaPagamentoVO(FormaPagamentoVO formaPagamentoVO) {
        this.formaPagamentoVO = formaPagamentoVO;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public String getNrControle() {
        String controle = "";
        try {
            if (!this.getPinpad().equals(OpcoesPinpadEnum.GETCARD)) {
                return "";
            }
            JSONObject json = new JSONObject(this.getParamsResp()).getJSONObject("response");
            String cupomReduzido = json.optString("CupomReduzido");
            if (!UteisValidacao.emptyString(cupomReduzido)) {
                try {
                    String[] lista = cupomReduzido.split("CTR:");
                    controle = lista[1];
                } catch (Exception ignored) {
                }
            }

            String cupomCliente = json.optString("CupomCliente");
            if (UteisValidacao.emptyString(controle) &&
                    !UteisValidacao.emptyString(cupomCliente)) {
                try {
                    String[] lista = cupomCliente.split("CONTROLE ");
                    controle = lista[1].split(" ")[0];
                } catch (Exception ignored) {
                }
            }

            String cupomLoja = json.optString("CupomLoja");
            if (UteisValidacao.emptyString(controle) &&
                    !UteisValidacao.emptyString(cupomLoja)) {
                try {
                    String[] lista = cupomLoja.split("CONTROLE ");
                    controle = lista[1].split(" ")[0];
                } catch (Exception ignored) {
                }
            }
            return controle;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return controle;
    }

    public String getParamsRespCancel() {
        if (paramsRespCancel == null) {
            paramsRespCancel = "";
        }
        return paramsRespCancel;
    }

    public void setParamsRespCancel(String paramsRespCancel) {
        this.paramsRespCancel = paramsRespCancel;
    }

    public String getIdExternoCancel() {
        if (idExternoCancel == null) {
            idExternoCancel = "";
        }
        return idExternoCancel;
    }

    public void setIdExternoCancel(String idExternoCancel) {
        this.idExternoCancel = idExternoCancel;
    }

    public Integer getPinPadPedidoOrigem() {
        if (pinPadPedidoOrigem == null) {
            pinPadPedidoOrigem = 0;
        }
        return pinPadPedidoOrigem;
    }

    public void setPinPadPedidoOrigem(Integer pinPadPedidoOrigem) {
        this.pinPadPedidoOrigem = pinPadPedidoOrigem;
    }
}

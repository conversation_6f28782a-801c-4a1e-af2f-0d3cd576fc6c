package negocio.comuns.financeiro;

import java.math.BigDecimal;

class ItemFaturamentoTOCreditoAte12X extends ItemFaturamentoTO {
    private final BigDecimal taxa = new BigDecimal("1.9");

    @Override
    public BigDecimal getTaxaSTONE() {
        return taxa;
    }

    @Override
    public String getFormaPagamento() {
        return "CRÉDITO ATÉ 12X";
    }

    @Override
    public int getOrdem() {
        return 3;
    }
}

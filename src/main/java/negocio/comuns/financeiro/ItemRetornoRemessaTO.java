package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.arquitetura.SuperTO;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;

/**
 * Created by GlaucoT on 26/08/2015
 */
public class ItemRetornoRemessaTO extends SuperTO {

    private RegistroRemessa registroRemessa;

    public ItemRetornoRemessaTO(RegistroRemessa registroRemessa) {
        this.registroRemessa = registroRemessa;
    }

    public RegistroRemessa getRegistroRemessa() {
        return registroRemessa;
    }

    public void setRegistroRemessa(RegistroRemessa registroRemessa) {
        this.registroRemessa = registroRemessa;
    }

    public String getStatusVenda() {
        return registroRemessa.getValue(DCCAttEnum.StatusVenda.name());
    }

    public String getNossoNumero() {
        return registroRemessa.getValue(DCCAttEnum.NossoNumero.name());
    }

    public String getNumDocumento() {
        return registroRemessa.getValue(DCCAttEnum.NumDocumento.name());
    }

    public String getNomeCliente() { return registroRemessa.getValue(DCCAttEnum.NomePessoa.name());}

    public String getValorRetorno() {
        String valorParcelaRetorno = registroRemessa.getValue(DCCAttEnum.ValorVenda.name());
        valorParcelaRetorno = valorParcelaRetorno.substring(0, valorParcelaRetorno.length() - 2) + "." + valorParcelaRetorno.substring(valorParcelaRetorno.length() - 2);
        Double valorParcela = Double.parseDouble(valorParcelaRetorno);
        return Formatador.formatarValorMonetario(valorParcela);
    }
}

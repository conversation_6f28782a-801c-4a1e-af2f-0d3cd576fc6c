package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Date;

/**
 * Created by <PERSON><PERSON>
 */
public class NFSeEmitidaHistoricoVO extends SuperVO {

    private Integer codigo;
    private Integer nfseEmitida;
    private Integer rps;
    private Integer reciboPagamento;
    private Integer cheque;
    private Integer cartaoCredito;
    private Integer movPagamento;
    private Integer movProduto;
    private Integer movConta;
    private Integer contrato;
    private String nrNotaManual;
    private String idReferencia;
    private Double valor;
    private Date dataEnvio;
    private Integer pessoa;
    private String jsonEnviar;
    private SituacaoNotaFiscalEnum situacaoNotaFiscal = SituacaoNotaFiscalEnum.GERADA;
    private Date dataRegistro;
    private Date dataEmissao;
    private Date dataReferencia;
    private Integer empresa;
    private boolean notaFamilia = false;
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO;
    private Integer sequencialFamilia;

    public NFSeEmitidaHistoricoVO() {

    }

    public NFSeEmitidaHistoricoVO(NFSeEmitidaVO nfSeEmitidaVO) {
        this.nfseEmitida = nfSeEmitidaVO.getCodigo();
        this.rps = nfSeEmitidaVO.getIdRps();

        if (nfSeEmitidaVO.getRecibo()!= null ) {
            this.reciboPagamento = nfSeEmitidaVO.getRecibo().getCodigo();
        }
        if (nfSeEmitidaVO.getCheque()!= null ) {
            this.cheque = nfSeEmitidaVO.getCheque().getCodigo();
        }
        if (nfSeEmitidaVO.getCartaoCredito()!= null ) {
            this.cartaoCredito = nfSeEmitidaVO.getCartaoCredito().getCodigo();
        }
        if (nfSeEmitidaVO.getMovPagamento()!= null ) {
            this.movPagamento = nfSeEmitidaVO.getMovPagamento().getCodigo();
        }
        if (nfSeEmitidaVO.getMovProduto()!= null ) {
            this.movProduto = nfSeEmitidaVO.getMovProduto().getCodigo();
        }
        if (nfSeEmitidaVO.getMovConta()!= null ) {
            this.movConta = nfSeEmitidaVO.getMovConta().getCodigo();
        }
        if (nfSeEmitidaVO.getContrato()!= null ) {
            this.contrato = nfSeEmitidaVO.getContrato().getCodigo();
        }
        this.nrNotaManual = nfSeEmitidaVO.getNrNotaManual();
        this.idReferencia = nfSeEmitidaVO.getIdReferencia();
        this.valor = nfSeEmitidaVO.getValor();
        this.dataEnvio = nfSeEmitidaVO.getDataEnvio();
        this.pessoa = nfSeEmitidaVO.getPessoa();
        this.jsonEnviar = nfSeEmitidaVO.getJsonEnviar();
        this.dataRegistro = nfSeEmitidaVO.getDataRegistro();
        this.dataEmissao = nfSeEmitidaVO.getDataEmissao();
        this.dataReferencia = nfSeEmitidaVO.getDataReferencia();
        this.empresa = nfSeEmitidaVO.getEmpresa();
        this.notaFamilia = nfSeEmitidaVO.isNotaFamilia();
        this.configuracaoNotaFiscalVO = nfSeEmitidaVO.getConfiguracaoNotaFiscalVO();
        this.situacaoNotaFiscal = nfSeEmitidaVO.getSituacaoNotaFiscal();
        this.sequencialFamilia = nfSeEmitidaVO.getSequencialFamilia();
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getNfseEmitida() {
        return nfseEmitida;
    }

    public void setNfseEmitida(Integer nfseEmitida) {
        this.nfseEmitida = nfseEmitida;
    }

    public Integer getRps() {
        return rps;
    }

    public void setRps(Integer rps) {
        this.rps = rps;
    }

    public Integer getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(Integer reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public Integer getCheque() {
        return cheque;
    }

    public void setCheque(Integer cheque) {
        this.cheque = cheque;
    }

    public Integer getCartaoCredito() {
        return cartaoCredito;
    }

    public void setCartaoCredito(Integer cartaoCredito) {
        this.cartaoCredito = cartaoCredito;
    }

    public Integer getMovPagamento() {
        return movPagamento;
    }

    public void setMovPagamento(Integer movPagamento) {
        this.movPagamento = movPagamento;
    }

    public Integer getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(Integer movProduto) {
        this.movProduto = movProduto;
    }

    public Integer getMovConta() {
        return movConta;
    }

    public void setMovConta(Integer movConta) {
        this.movConta = movConta;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public String getNrNotaManual() {
        return nrNotaManual;
    }

    public void setNrNotaManual(String nrNotaManual) {
        this.nrNotaManual = nrNotaManual;
    }

    public String getIdReferencia() {
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Date getDataEnvio() {
        return dataEnvio;
    }

    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getJsonEnviar() {
        return jsonEnviar;
    }

    public void setJsonEnviar(String jsonEnviar) {
        this.jsonEnviar = jsonEnviar;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public Date getDataReferencia() {
        return dataReferencia;
    }

    public void setDataReferencia(Date dataReferencia) {
        this.dataReferencia = dataReferencia;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getDataEmissaoJSON() {
        try {
            JSONObject jsonObject = new JSONObject(getJsonEnviar());
            JSONArray lista = jsonObject.getJSONArray("RPS");
            JSONObject obj = lista.getJSONObject(0);
            return Uteis.getDate(obj.getString("DataEmissao"), "yyyy-MM-dd HH:mm:ss");
        } catch (Exception e){
            return getDataEmissao();
        }
    }

    public String getNomeAlunoJSON() {
        try {
            JSONObject jsonObject = new JSONObject(getJsonEnviar());
            JSONArray lista = jsonObject.getJSONArray("RPS");
            JSONObject obj = lista.getJSONObject(0);
            return obj.getString("NomeAluno");
        } catch (Exception e){
            return "";
        }
    }

    public boolean isNotaFamilia() {
        return notaFamilia;
    }

    public void setNotaFamilia(boolean notaFamilia) {
        this.notaFamilia = notaFamilia;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalVO() {
        if (configuracaoNotaFiscalVO == null) {
            configuracaoNotaFiscalVO = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalVO;
    }

    public void setConfiguracaoNotaFiscalVO(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) {
        this.configuracaoNotaFiscalVO = configuracaoNotaFiscalVO;
    }

    public SituacaoNotaFiscalEnum getSituacaoNotaFiscal() {
        return situacaoNotaFiscal;
    }

    public void setSituacaoNotaFiscal(SituacaoNotaFiscalEnum situacaoNotaFiscal) {
        this.situacaoNotaFiscal = situacaoNotaFiscal;
    }

    public Integer getSequencialFamilia() {
        return sequencialFamilia;
    }

    public void setSequencialFamilia(Integer sequencialFamilia) {
        this.sequencialFamilia = sequencialFamilia;
    }
}

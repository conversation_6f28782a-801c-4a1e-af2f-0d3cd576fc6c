package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class CancelamentoAntecipadoTO extends SuperTO {

    private ContratoVO contratoVO;
    private Double valorParcela;
    private Date dataBaseCancelamento;
    private List<MovParcelaVO> parcelas;


    public ContratoVO getContratoVO() {
        if (contratoVO == null) {
            contratoVO = new ContratoVO();
        }
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public Double getValorParcela() {
        if (valorParcela == null) {
            valorParcela = 0.0;
        }
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public List<MovParcelaVO> getParcelas() {
        if (parcelas == null) {
            parcelas = new ArrayList<MovParcelaVO>();
        }
        return parcelas;
    }

    public void setParcelas(List<MovParcelaVO> parcelas) {
        this.parcelas = parcelas;
    }

    public Date getDataBaseCancelamento() {
        if (dataBaseCancelamento == null) {
            dataBaseCancelamento = Calendario.hoje();
        }
        return dataBaseCancelamento;
    }

    public void setDataBaseCancelamento(Date dataBaseCancelamento) {
        this.dataBaseCancelamento = dataBaseCancelamento;
    }
}

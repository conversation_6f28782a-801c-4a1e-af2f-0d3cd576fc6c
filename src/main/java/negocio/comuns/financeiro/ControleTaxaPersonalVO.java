
package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.PlanoPersonalTextoPadraoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class ControleTaxaPersonalVO extends SuperVO {
    @ChavePrimaria
    private Integer codigo = 0;
    private EmpresaVO empresa;
    private Date dataRegistro = Calendario.hoje();
    @ChaveEstrangeira
    private ColaboradorVO personal = new ColaboradorVO();
    @ChaveEstrangeira
    private UsuarioVO responsavel = new UsuarioVO();
    @Lista
    private List<ItemTaxaPersonalVO> alunos = new ArrayList<ItemTaxaPersonalVO>();
    private PlanoVO plano = new PlanoVO();
    private Date dataInicioVigenciaPlano;
    private Date dataFimVigenciaPlano;
    @NaoControlarLogAlteracao
    private int qtdeParcelasAtrasadas;
    @NaoControlarLogAlteracao
    private int qtdeParcelasEmAberto;
    @NaoControlarLogAlteracao
    private boolean vigente;
    @NaoControlarLogAlteracao
    private int qtdeParcelasPagas;
    private Integer responsavelCancelamento;
    private boolean cancelado;
    private Date dataCancelamento;
    @NaoControlarLogAlteracao
    private String situacao = "";
    private PlanoPersonalTextoPadraoVO planoPersonalTextoPadrao;
    private Date assinadoEm;

    public ControleTaxaPersonalVO() {
    }

    public ControleTaxaPersonalVO(int codigo) {
        this.codigo = codigo;
    }

    public void validarDados() throws Exception {
        if(empresa == null)
            throw new Exception("Empresa Não Informada.");
        if(personal == null || personal.getCodigo().intValue() == 0)
            throw new Exception("Personal Não Informado.");
        if(responsavel == null || responsavel.getCodigo().intValue() == 0)
            throw new Exception("Responsavel Não Informado.");
        if(alunos == null || alunos.isEmpty())
            throw new Exception("Nenhum Aluno Informado.");
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ColaboradorVO getPersonal() {
        return personal;
    }

    public void setPersonal(ColaboradorVO personal) {
        this.personal = personal;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public String getDataRegistro_apresentar() {
        return Uteis.getData(dataRegistro);
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public EmpresaVO getEmpresa(){
        if(empresa == null){
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }

    public List<ItemTaxaPersonalVO> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<ItemTaxaPersonalVO> alunos) {
        this.alunos = alunos;
    }

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof ControleTaxaPersonalVO) {
            ControleTaxaPersonalVO aux = (ControleTaxaPersonalVO)obj;
            return this.getCodigo().intValue() == aux.getCodigo().intValue();
        }
        return false;
    }

    public PlanoVO getPlano() {
        if(plano == null){
            plano = new PlanoVO();
        }
        return plano;
    }

    public void setPlano(PlanoVO plano) {
        this.plano = plano;
    }

    public Date getDataInicioVigenciaPlano() {
        return dataInicioVigenciaPlano;
    }

    public String getDataInicioVigencia_apresentar() {
        return Uteis.getData(dataInicioVigenciaPlano);
    }

    public void setDataInicioVigenciaPlano(Date dataInicioVigenciaPlano) {
        this.dataInicioVigenciaPlano = dataInicioVigenciaPlano;
    }

    public Date getDataFimVigenciaPlano() {
        return dataFimVigenciaPlano;
    }

    public String getDataFimVigencia_apresentar() {
        return Uteis.getData(dataFimVigenciaPlano);
    }

    public void setDataFimVigenciaPlano(Date dataFimVigenciaPlano) {
        this.dataFimVigenciaPlano = dataFimVigenciaPlano;
    }

    public void setQtdeParcelasAtrasadas(int qtdeParcelasAtrasadas) {
        this.qtdeParcelasAtrasadas = qtdeParcelasAtrasadas;
    }

    public int getQtdeParcelasAtrasadas() {
        return qtdeParcelasAtrasadas;
    }

    public int getQtdeParcelasEmAberto() {
        return qtdeParcelasEmAberto;
    }

    public void setQtdeParcelasEmAberto(int qtdeParcelasEmAberto) {
        this.qtdeParcelasEmAberto = qtdeParcelasEmAberto;
    }

    public boolean isVigente() {
        return vigente;
    }

    public void setVigente(boolean vigente) {
        this.vigente = vigente;
    }

    public int getQtdeParcelasPagas() {
        return qtdeParcelasPagas;
    }

    public void setQtdeParcelasPagas(int qtdeParcelasPagas) {
        this.qtdeParcelasPagas = qtdeParcelasPagas;
    }

    public void setResponsavelCancelamento(Integer responsavelCancelamento) {
        this.responsavelCancelamento = responsavelCancelamento;
    }

    public Integer getResponsavelCancelamento() {
        return responsavelCancelamento;
    }

    public void setCancelado(boolean cancelado) {
        this.cancelado = cancelado;
    }

    public boolean getCancelado() {
        return cancelado;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public PlanoPersonalTextoPadraoVO getPlanoPersonalTextoPadrao() {
        if (planoPersonalTextoPadrao == null){
            planoPersonalTextoPadrao = new PlanoPersonalTextoPadraoVO();
        }
        return planoPersonalTextoPadrao;
    }

    public void setPlanoPersonalTextoPadrao(PlanoPersonalTextoPadraoVO planoPersonalTextoPadrao) {
        this.planoPersonalTextoPadrao = planoPersonalTextoPadrao;
    }

    public Date getAssinadoEm() {
        return assinadoEm;
    }

    public void setAssinadoEm(Date assinadoEm) {
        this.assinadoEm = assinadoEm;
    }

}

package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import br.com.pactosolucoes.enumeradores.TipoPeriodoConsultaLancamentos;
import br.com.pactosolucoes.enumeradores.TipoPesquisaConsultaLancamentos;
import controle.financeiro.GestaoRecebiveisControle;
import controle.financeiro.MovContaControle;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FiltroLancamentosTO extends SuperTO {

    private static final long serialVersionUID = 7831467910976366385L;
    private Integer codigo;
    private Integer tipoLancamento;
    private String descricao;
    private Double valorMinimo;
    private Double valorMaximo;
    private PessoaVO favorecido;
    private List<PessoaVO> favorecidos;
    private List<Integer> favorecidosCodigos;
    private EmpresaVO empresaRelatorio;
    private TipoPesquisaConsultaLancamentos tipoPesquisa;
    private TipoPeriodoConsultaLancamentos tipoPeriodo;
    //listas pra tela
    private List<EmpresaVO> listaEmpresa;
    private List<ContaVO> listaConta;
    private List<FormaPagamentoVO> listaFormaPgto;
    private List<TipoDocumentoVO> listaTipoDocumento;
    private List<Integer> codigosPlanoContas;
    private List<Integer> codigosCentroCustos;
    //filtros de datas
    private Date inicioLancamento;
    private Date finalLancamento;
    private Date inicioQuitacao;
    private Date finalQuitacao;
    private Date inicioVencimento;
    private Date finalVencimento;
    private Date inicioCompetencia;
    private Date finalCompetencia;
    private Boolean apenasNaoQuitados;
    private Boolean apenasQuitados;
    private Boolean pagamentosConjunto;
    //valores booleanos para controle de painéis de filtros em tela
    private boolean parametro1 = false;
    private boolean parametro2 = false;
    private boolean parametro3 = false;
    private boolean parametro4 = false;
    private boolean parametro5 = false;
    private ChequeVO chequeVO = new ChequeVO();
    private boolean agruparPorPlanoDeContasCentroCusto = false;
    private Boolean somenteContasMobile = Boolean.FALSE;
    private Boolean agendamentosAndamento = Boolean.FALSE;
    private Date dataInicioUltimaAlteracao;
    private Date dataFimUltimaAlteracao;
    private boolean movimentacoesExcluidas = false;
    private Boolean somenteContasHoje = Boolean.FALSE;

    private String numeroDocumento;

    public String detalharFiltros(String nomesPlanoContas, String nomesCentroCustos) {
        String filtros = "";
        if (agendamentosAndamento) {
            filtros += " | Último lançamento de agendamento recorrente em andamento ";
        }
        if (somenteContasMobile) {
            filtros += " | Somente contas lançadas pelo App do gestor";
        }
        if (apenasNaoQuitados && !apenasQuitados) {
            filtros += " | Somente Não Quitados";
        }

        if (!apenasNaoQuitados && apenasQuitados) {
            filtros += " | Somente Quitados";
        }
        if (pagamentosConjunto) {
            filtros += " | Pagos em conjunto ";
        }
        //descricao
        if (!UteisValidacao.emptyString(this.getDescricao())) {
            filtros += " | Descrição: " + this.getDescricao();
        }
        //tipolancamento
        if (!UteisValidacao.emptyNumber(this.getTipoLancamento())) {
            filtros += " | Tipo de Lançamento: " + TipoOperacaoLancamento.getTipoOperacaoLancamento(this.getTipoLancamento()).getDescricao();
        }
        //empresas selecionadas
        String empresas = "";
        if (getListaEmpresa() != null){
            for (EmpresaVO empresa : getListaEmpresa()) {
                if (empresa.getEmpresaEscolhida()) {
                    empresas += ", " + empresa.getNome();
                }
            }
        }
        if (!empresas.isEmpty()) {
            filtros += " | Empresa: " + empresas.replaceFirst(",", "");
        }
        if (!UteisValidacao.emptyNumber(this.getCodigo())) {
            filtros += " | Código: " + getCodigo();
        }

        //tipos documentos selecionados
        String tipoDocumentos = "";
        if (getListaTipoDocumento() != null){
            for (TipoDocumentoVO doc : getListaTipoDocumento()) {
                if (doc.getTipoDocumentoEscolhido()) {
                    tipoDocumentos += ", " + doc.getDescricao();
                }
            }
        }
        if (!tipoDocumentos.isEmpty()) {
            filtros += " | Tipo de Documento: " + tipoDocumentos.replaceFirst(",", "");
        }
        //contas selecionadas
        String contas = "";
        if (getListaConta() != null){
            for (ContaVO conta : getListaConta()) {
                if (conta.getContaEscolhida()) {
                    contas += ", " + conta.getDescricao();
                }
            }
        }
        if (!contas.isEmpty()) {
            filtros += " | Conta: " + contas.replaceFirst(",", "");
        }
        //formas de pagamento selecionadas
        String formasPagamento = "";
        if (getListaFormaPgto() != null){
            for (FormaPagamentoVO pgto : getListaFormaPgto()) {
                if (pgto.getFormaPagamentoEscolhida()) {
                    formasPagamento += ", " + pgto.getDescricao();
                }
            }
        }
        if (!formasPagamento.isEmpty()) {
            filtros += " | Forma Pagamento: " + formasPagamento.replaceFirst(",", "");
        }
        //planos de contas selecionados

        //favorecido
        if (CollectionUtils.isNotEmpty(this.getFavorecidos())) {
            final StringBuilder favorecidos = new StringBuilder();
            String separador = "";
            for (PessoaVO favorecido : this.getFavorecidos()) {
                favorecidos.append(separador).append(favorecido.getNome());
                separador = ", ";
            }
            filtros += " | Favorecidos: " + favorecidos;
        }

        //valores máximo e minimo
        if (!UteisValidacao.emptyNumber(this.getValorMaximo())) {
            filtros += " | Valor de: " + Formatador.formatarValorMonetario(this.getValorMinimo()) + " a " + Formatador.formatarValorMonetario(this.getValorMaximo());
        }


        //data lancamento
        if (this.getInicioLancamento() != null && this.getFinalLancamento() != null) {
            filtros += " | Data de Lançamento de: " + Uteis.getData(this.getInicioLancamento()) + " até " + Uteis.getData(this.getFinalLancamento());
        }
        //data quitacao
        if (this.getInicioQuitacao() != null && this.getFinalQuitacao() != null) {
            filtros += " | Data de Quitação de: " + Uteis.getData(this.getInicioQuitacao()) + " até " + Uteis.getData(this.getFinalQuitacao());
        }
        //data competencia
        if (this.getInicioCompetencia() != null && this.getFinalCompetencia() != null) {
            filtros += " | Data de Competência de: " + Uteis.getData(this.getInicioCompetencia()) + " até " + Uteis.getData(this.getFinalCompetencia());
        }
        //data vencimento
        if (this.getInicioVencimento() != null && this.getFinalVencimento() != null) {
            filtros += " | Data de Vencimento de: " + Uteis.getData(this.getInicioVencimento()) + " até " + Uteis.getData(this.getFinalVencimento());
        }

        //cheque: banco
        if(!UteisValidacao.emptyNumber(this.getChequeVO().getBanco().getCodigo())){
            filtros += " | Banco: " + this.getChequeVO().getBanco().getCodigo();
        }
        //cheque: agencia
        if(!UteisValidacao.emptyString(this.getChequeVO().getAgencia())){
            filtros += " | Agencia: " + this.getChequeVO().getAgencia();
        }
        //cheque: conta
        if(!UteisValidacao.emptyString(this.getChequeVO().getNumero())){
            filtros += " | Conta: " + this.getChequeVO().getNumero();
        }
        //cheque: numero cheque
        if(!UteisValidacao.emptyString(this.getChequeVO().getNumero())){
            filtros += " | Numero Cheque: " + this.getChequeVO().getNumero();
        }
        filtros += nomesPlanoContas;
        filtros += nomesCentroCustos;

        //data ultima alteração
        if (this.getDataInicioUltimaAlteracao() != null && this.getDataFimUltimaAlteracao() != null) {
            filtros += " | Dt. Interna(IAE): " + Uteis.getData(this.getDataInicioUltimaAlteracao()) + " até " + Uteis.getData(this.getDataFimUltimaAlteracao()) +
                        " Ordenação por: Dt. Interna(IAE)";
        }

        if (this.isMovimentacoesExcluidas()) {
            filtros += " | Somente movimentações excluidas";
        }

        return filtros.replaceFirst("[|]", "");
    }

    /**
     * Responsável por setar as datas da consulta padrão
     * author: alcides
     * 01/12/2011
     * @throws Exception 
     */
    public void setarDatasPadrao() throws Exception {
        limparDatas();
        switch (getTipoPeriodoEnum()) {
            case LANCAMENTOS_DIA:
//                tipoLancamento = null;
                inicioLancamento = Calendario.hoje();
                finalLancamento = Calendario.hoje();
                break;
            case LANCAMENTOS_SEMANA:
//                tipoLancamento = null;
                inicioLancamento = Uteis.obterDataAnterior(Calendario.hoje(), 7);
                finalLancamento = Calendario.hoje();
                break;
            case LANCAMENTOS_MES:
//                tipoLancamento = null;
                inicioLancamento = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
                finalLancamento = Uteis.obterUltimoDiaMes(Calendario.hoje());
                break;
            case CONTAS_RECEBER_SEMANA:
                tipoLancamento = TipoOperacaoLancamento.RECEBIMENTO.getCodigo();
                inicioVencimento = Uteis.obterDataAnterior(Calendario.hoje(), 7);
                finalVencimento = Calendario.hoje();
                break;
            case CONTAS_RECEBER_MES:
                tipoLancamento = TipoOperacaoLancamento.RECEBIMENTO.getCodigo();
                inicioVencimento = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
                finalVencimento = Uteis.obterUltimoDiaMes(Calendario.hoje());
                break;
            case CONTAS_PAGAR_SEMANA:
                tipoLancamento = TipoOperacaoLancamento.PAGAMENTO.getCodigo();
                inicioVencimento = Uteis.obterDataAnterior(Calendario.hoje(), 7);
                finalVencimento = Calendario.hoje();
                break;
            case CONTAS_PAGAR_MES:
                tipoLancamento = TipoOperacaoLancamento.PAGAMENTO.getCodigo();
                inicioVencimento = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
                finalVencimento = Uteis.obterUltimoDiaMes(Calendario.hoje());
                break;
        }

    }

    public void limparFiltros() throws Exception {
        descricao = "";
        valorMinimo = 0.0;
        valorMaximo = 0.0;
        favorecido = new PessoaVO();
        tipoPesquisa = null;
        tipoPeriodo = null;
        this.codigo = null;
        this.codigosPlanoContas = new ArrayList<>();
        this.codigosCentroCustos = new ArrayList<>();
        this.numeroDocumento = "";
        this.apenasQuitados = false;
        this.apenasNaoQuitados = true;
        this.pagamentosConjunto = false;
        this.agruparPorPlanoDeContasCentroCusto = false;
        this.somenteContasMobile = false;
        this.agendamentosAndamento = false;
        this.movimentacoesExcluidas = false;
        for (ContaVO conta : listaConta) {
            conta.setContaEscolhida(false);
        }
        for (FormaPagamentoVO fp : listaFormaPgto) {
            fp.setFormaPagamentoEscolhida(false);
        }
        for (TipoDocumentoVO tipoDoc : listaTipoDocumento) {
            tipoDoc.setTipoDocumentoEscolhido(false);
        }
        GestaoRecebiveisControle gestaoRecebiveisControle = (GestaoRecebiveisControle) JSFUtilities.getFromSession(GestaoRecebiveisControle.class.getSimpleName());
        if (gestaoRecebiveisControle != null) {
            gestaoRecebiveisControle.setPesquisarRecebiveisComLote(false);
            gestaoRecebiveisControle.setPesquisarRecebiveisSemLote(false);
        }
        if (getTipoLancamento().equals(TipoOperacaoLancamento.PAGAMENTO.getCodigo())) {
            setTipoPesquisaEnum(TipoPesquisaConsultaLancamentos.PESQUISAPADRAO);
            setTipoPeriodoEnum(TipoPeriodoConsultaLancamentos.CONTAS_PAGAR_MES);
        } else if (getTipoLancamento().equals(TipoOperacaoLancamento.RECEBIMENTO.getCodigo())) {
            setTipoPesquisaEnum(TipoPesquisaConsultaLancamentos.PESQUISAPADRAO); //pesquisa padrão
            setTipoPeriodoEnum(TipoPeriodoConsultaLancamentos.CONTAS_RECEBER_MES);
        }

        //Filtro plano de contas
        MovContaControle movContaControle = (MovContaControle) JSFUtilities.getFromSession(MovContaControle.class.getSimpleName());
        movContaControle.getFiltroPlanoContas().setCodigosPlanosSelecionados(new ArrayList<>());
        movContaControle.getFiltroPlanoContas().setCodigosConcatenados("");
        if (movContaControle.getFiltroPlanoContas().getListaTree() != null) {
            for (ObjetoTreeTO plano : movContaControle.getFiltroPlanoContas().getListaTree()) {
                plano.setSelecionado(false);
            }
        }
        //Filtro centro de custos
        movContaControle.getFiltroCentroCustos().setCodigosPlanosSelecionados(new ArrayList<>());
        movContaControle.getFiltroCentroCustos().setCodigosConcatenados("");
        if (movContaControle.getFiltroCentroCustos().getListaTree() != null) {
            for (ObjetoTreeTO centro : movContaControle.getFiltroCentroCustos().getListaTree()) {
                centro.setSelecionado(false);
            }
        }

        limparDatas();
        getFavorecidos().clear();
    }

    /**
     * author: alcides
     * 01/12/2011
     */
    public void limparDatas() {
        inicioLancamento = null;
        finalLancamento = null;

        inicioQuitacao = null;
        finalQuitacao = null;

        inicioVencimento = null;
        finalVencimento = null;

        inicioCompetencia = null;
        finalCompetencia = null;
    }

    /**
     * author: alcides
     * 30/11/2011
     */
    public void inicializarListas(List<EmpresaVO> listaEmpresa, List<ContaVO> listaConta, List<FormaPagamentoVO> listaFormaPgto,
            List<TipoDocumentoVO> listaTipoDocumento) {
        this.listaEmpresa = listaEmpresa;
        this.listaConta = listaConta;
        this.listaFormaPgto = new ArrayList<FormaPagamentoVO>();
        for (FormaPagamentoVO obj : listaFormaPgto) {
            if (!obj.getTipoFormaPagamento().equals("CC")) {
                this.listaFormaPgto.add(obj);
            }
        }

        this.listaTipoDocumento = listaTipoDocumento;
    }

    //-------------------------- GETTERS AND SETTERS --------------------------//
    /**
     * @return the parametro1
     */
    public boolean isParametro1() {
        return parametro1;
    }

    /**
     * @param parametro1 the parametro1 to set
     */
    public void setParametro1(boolean parametro1) {
        this.parametro1 = parametro1;
    }

    /**
     * @return the parametro2
     */
    public boolean isParametro2() {
        return parametro2;
    }

    /**
     * @param parametro2 the parametro2 to set
     */
    public void setParametro2(boolean parametro2) {
        this.parametro2 = parametro2;
    }

    /**
     * @return the parametro3
     */
    public boolean isParametro3() {
        return parametro3;
    }

    /**
     * @param parametro3 the parametro3 to set
     */
    public void setParametro3(boolean parametro3) {
        this.parametro3 = parametro3;
    }

    /**
     * @return the parametro4
     */
    public boolean isParametro4() {
        return parametro4;
    }

    /**
     * @param parametro4 the parametro4 to set
     */
    public void setParametro4(boolean parametro4) {
        this.parametro4 = parametro4;
    }

    /**
     * @return the parametro5
     */
    public boolean isParametro5() {
        return parametro5;
    }

    /**
     * @param parametro5 the parametro5 to set
     */
    public void setParametro5(boolean parametro5) {
        this.parametro5 = parametro5;
    }

    /**
     * @return the listaEmpresa
     */
    public List<EmpresaVO> getListaEmpresa() {
        return listaEmpresa;
    }

    /**
     * @return the listaConta
     */
    public List<ContaVO> getListaConta() {
        return listaConta;
    }

    /**
     * @return the listaFormaPgto
     */
    public List<FormaPagamentoVO> getListaFormaPgto() {
        return listaFormaPgto;
    }

    /**
     * @return the listaTipoDocumento
     */
    public List<TipoDocumentoVO> getListaTipoDocumento() {
        return listaTipoDocumento;
    }

    /**
     * @return the tipoLancamento
     */
    public Integer getTipoLancamento() {
        if (tipoLancamento == null){
            tipoLancamento = 0;
        }
        return tipoLancamento;
    }

    /**
     * @param tipoLancamento the tipoLancamento to set
     */
    public void setTipoLancamento(Integer tipoLancamento) {
        this.tipoLancamento = tipoLancamento;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    /**
     * @param descricao the descricao to set
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    /**
     * @return the valorMinimo
     */
    public Double getValorMinimo() {
        if (valorMinimo == null) {
            valorMinimo = 0.0;
        }
        return valorMinimo;
    }

    /**
     * @param valorMinimo the valorMinimo to set
     */
    public void setValorMinimo(Double valorMinimo) {
        this.valorMinimo = valorMinimo;
    }

    /**
     * @return the valorMaximo
     */
    public Double getValorMaximo() {
        if (valorMaximo == null) {
            valorMaximo = 0.0;
        }
        return valorMaximo;
    }

    /**
     * @param valorMaximo the valorMaximo to set
     */
    public void setValorMaximo(Double valorMaximo) {
        this.valorMaximo = valorMaximo;
    }

    /**
     * @return the favorecido
     */
    public PessoaVO getFavorecido() {
        if (favorecido == null) {
            favorecido = new PessoaVO();
        }
        return favorecido;
    }

    /**
     * @param favorecido the favorecido to set
     */
    public void setFavorecido(PessoaVO favorecido) {
        this.favorecido = favorecido;
    }

    /**
     * @return the tipoPesquisa
     */
    public TipoPesquisaConsultaLancamentos getTipoPesquisaEnum() {
        if (tipoPesquisa == null) {
            tipoPesquisa = TipoPesquisaConsultaLancamentos.PESQUISAPADRAO;
        }
        return tipoPesquisa;
    }

    /**
     * @param tipoPesquisa the tipoPesquisa to set
     * @throws Exception
     */
    public void setTipoPesquisaEnum(TipoPesquisaConsultaLancamentos tipoPesquisa) throws Exception {
        this.tipoPesquisa = tipoPesquisa;
    }

    /**
     * @param tipoPesquisa the tipoPesquisa to set
     * @throws Exception
     */
    public void setTipoPesquisa(Integer tipoPesquisa) throws Exception {
        this.tipoPesquisa = TipoPesquisaConsultaLancamentos.getTipoPesquisaConsultaLancamentos(tipoPesquisa);
    }

    public Integer getTipoPesquisa() {
        return tipoPesquisa == null ? null : getTipoPesquisaEnum().getCodigo();
    }

    /**
     * @return the inicioLancamento
     */
    public Date getInicioLancamento() {
        return inicioLancamento;
    }

    /**
     * @param inicioLancamento the inicioLancamento to set
     */
    public void setInicioLancamento(Date inicioLancamento) {
        this.inicioLancamento = inicioLancamento;
    }

    /**
     * @return the finalLancamento
     */
    public Date getFinalLancamento() {
        return finalLancamento;
    }

    /**
     * @param finalLancamento the finalLancamento to set
     */
    public void setFinalLancamento(Date finalLancamento) {
        this.finalLancamento = finalLancamento;
    }

    /**
     * @return the inicioQuitacao
     */
    public Date getInicioQuitacao() {
        return inicioQuitacao;
    }

    /**
     * @param inicioQuitacao the inicioQuitacao to set
     */
    public void setInicioQuitacao(Date inicioQuitacao) {
        this.inicioQuitacao = inicioQuitacao;
    }

    /**
     * @return the finalQuitacao
     */
    public Date getFinalQuitacao() {
        return finalQuitacao;
    }

    /**
     * @param finalQuitacao the finalQuitacao to set
     */
    public void setFinalQuitacao(Date finalQuitacao) {
        this.finalQuitacao = finalQuitacao;
    }

    /**
     * @return the inicioVencimento
     */
    public Date getInicioVencimento() {
        return inicioVencimento;
    }

    /**
     * @param inicioVencimento the inicioVencimento to set
     */
    public void setInicioVencimento(Date inicioVencimento) {
        this.inicioVencimento = inicioVencimento;
    }

    /**
     * @return the finalVencimento
     */
    public Date getFinalVencimento() {
        return finalVencimento;
    }

    /**
     * @param finalVencimento the finalVencimento to set
     */
    public void setFinalVencimento(Date finalVencimento) {
        this.finalVencimento = finalVencimento;
    }

    /**
     * @return the inicioCompetencia
     */
    public Date getInicioCompetencia() {
        return inicioCompetencia;
    }

    /**
     * @param inicioCompetencia the inicioCompetencia to set
     */
    public void setInicioCompetencia(Date inicioCompetencia) {
        this.inicioCompetencia = inicioCompetencia;
    }

    /**
     * @return the finalCompetencia
     */
    public Date getFinalCompetencia() {
        return finalCompetencia;
    }

    /**
     * @param finalCompetencia the finalCompetencia to set
     */
    public void setFinalCompetencia(Date finalCompetencia) {
        this.finalCompetencia = finalCompetencia;
    }

    /**
     * @param listaEmpresa the listaEmpresa to set
     */
    public void setListaEmpresa(List<EmpresaVO> listaEmpresa) {
        this.listaEmpresa = listaEmpresa;
    }

    public int getQtdEmpresasSelecionadas() {
        if (listaEmpresa == null || listaEmpresa.isEmpty()) {
            return 0;
        }
        int qtdEmpresasSelecionadas = 0;
        for (EmpresaVO empresa : listaEmpresa) {
            if (empresa.getEmpresaEscolhida()) {
                qtdEmpresasSelecionadas++;
            }
        }
        return qtdEmpresasSelecionadas;
    }

    /**
     * @param listaConta the listaConta to set
     */
    public void setListaConta(List<ContaVO> listaConta) {
        this.listaConta = listaConta;
    }

    /**
     * @param listaFormaPgto the listaFormaPgto to set
     */
    public void setListaFormaPgto(List<FormaPagamentoVO> listaFormaPgto) {
        this.listaFormaPgto = listaFormaPgto;
    }

    /**
     * @param listaTipoDocumento the listaTipoDocumento to set
     */
    public void setListaTipoDocumento(List<TipoDocumentoVO> listaTipoDocumento) {
        this.listaTipoDocumento = listaTipoDocumento;
    }

    /**
     * @param codigoPeriodo the codigoPeriodo to set
     * @throws Exception
     */
    public void setTipoPeriodo(Integer codigoPeriodo) throws Exception {
        this.tipoPeriodo = TipoPeriodoConsultaLancamentos.getTipoPeriodoConsultaLancamentos(codigoPeriodo);
        if (this.tipoPeriodo != null && this.getTipoPesquisaEnum().equals(TipoPesquisaConsultaLancamentos.PESQUISAPADRAO)) {
            setarDatasPadrao();
        }

    }

    public void setTipoPeriodoEnum(TipoPeriodoConsultaLancamentos codigoPeriodo) throws Exception {
        this.tipoPeriodo = codigoPeriodo;
        if (this.tipoPeriodo != null && this.getTipoPesquisaEnum().equals(TipoPesquisaConsultaLancamentos.PESQUISAPADRAO)) {
            setarDatasPadrao();
        }
    }

    /**
     * @return the codigoPeriodo
     */
    public Integer getTipoPeriodo() {
        return tipoPeriodo == null ? null : tipoPeriodo.getCodigo();
    }

    /**
     * @return the codigoPeriodo
     * @throws Exception
     */
    public TipoPeriodoConsultaLancamentos getTipoPeriodoEnum() throws Exception {
        return tipoPeriodo;
    }

    public String getCodigosEmpresa() {
        //empresas selecionadas
        String empresas = "";
        if (getListaEmpresa() != null){
            for (EmpresaVO empresa : getListaEmpresa()) {
                if (empresa.getEmpresaEscolhida()) {
                    empresas += ", " + empresa.getCodigo();
                }
            }
        }
        return empresas.replaceFirst(",", "");
    }

    public String getCodigosTipoDocumento() {
        String tipoDocumentos = "";
        if (getListaTipoDocumento() != null){
            for (TipoDocumentoVO doc : getListaTipoDocumento()) {
                if (doc.getTipoDocumentoEscolhido()) {
                    tipoDocumentos += ", " + doc.getCodigo();
                }
            }
        }
        return tipoDocumentos.replaceFirst(",", "");
    }

    public String getCodigosConta() {
        //contas selecionadas
        String contas = "";
        if (getListaConta() != null){
            for (ContaVO conta : getListaConta()) {
                if (conta.getContaEscolhida()) {
                    contas += ", " + conta.getCodigo();
                }
            }
        }
        return contas.replaceFirst(",", "");
    }

    public String getCodigosFormaPgto() {
        //formas de pagamento selecionadas
        String formasPagamento = "";
        if (getListaFormaPgto() != null){
            for (FormaPagamentoVO pgto : getListaFormaPgto()) {
                if (pgto.getFormaPagamentoEscolhida()) {
                    formasPagamento += ", " + pgto.getCodigo();
                }
            }
        }
        return formasPagamento.replaceFirst(",", "");
    }

    public void adicionarFavorecido(PessoaVO favorecido) {
        boolean novoFavorecido = true;
        for (final PessoaVO fav : getFavorecidos()) {
            if (fav.getCodigo().equals(favorecido.getCodigo())) {
                novoFavorecido = false;
                break;
            }
        }

        if (novoFavorecido) {
            getFavorecidos().add(favorecido);
        }
    }

    /**
     * @param codigosPlanoContas the codigosPlanoContas to set
     */
    public void setCodigosPlanoContas(List<Integer> codigosPlanoContas) {
        this.codigosPlanoContas = codigosPlanoContas;
    }

    public String getCodigosPlanoContas() {
        if (codigosPlanoContas == null) {
            codigosPlanoContas = new ArrayList<Integer>();
        }
        //formas de pagamento selecionadas
        String planoContas = "";
        for (Integer codigo : codigosPlanoContas) {
            planoContas += ", " + codigo;
        }
        return planoContas.replaceFirst(",", "");
    }

    /**
     * @param empresaRelatorio the empresaRelatorio to set
     */
    public void setEmpresaRelatorio(EmpresaVO empresaRelatorio) {
        this.empresaRelatorio = empresaRelatorio;
    }

    /**
     * @return the empresaRelatorio
     */
    public EmpresaVO getEmpresaRelatorio() {
        if (empresaRelatorio == null) {
            empresaRelatorio = new EmpresaVO();
        }
        return empresaRelatorio;
    }

    /**
     * @param apenasNaoPagos the apenasNaoPagos to set
     */
    public void setApenasNaoQuitados(Boolean apenasNaoPagos) {
        this.apenasNaoQuitados = apenasNaoPagos;
    }

    /**
     * @return the apenasNaoPagos
     */
    public Boolean getApenasNaoQuitados() {
        if (apenasNaoQuitados == null) {
            apenasNaoQuitados = Boolean.FALSE;
        }
        return apenasNaoQuitados;
    }

    /**
     * @param apenasQuitados the apenasQuitados to set
     */
    public void setApenasQuitados(Boolean apenasQuitados) {
        this.apenasQuitados = apenasQuitados;
    }

    /**
     * @return the apenasQuitados
     */
    public Boolean getApenasQuitados() {
        if (apenasQuitados == null) {
            apenasQuitados = Boolean.FALSE;
        }
        return apenasQuitados;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setPagamentosConjunto(Boolean pagamentosConjunto) {
        this.pagamentosConjunto = pagamentosConjunto;
    }

    public Boolean getPagamentosConjunto() {
        if (pagamentosConjunto == null) {
            pagamentosConjunto = Boolean.FALSE;
        }
        return pagamentosConjunto;
    }

    public String getCodigosCentroCustos() {
        if (codigosCentroCustos == null) {
            codigosCentroCustos = new ArrayList<Integer>();
        }
        //formas de pagamento selecionadas
        String centroCustos = "";
        for (Integer codigo : codigosCentroCustos) {
            centroCustos += ", " + codigo;
        }
        return centroCustos.replaceFirst(",", "");
    }

    public void setCodigosCentroCustos(List<Integer> codigosCentroCustos) {
        this.codigosCentroCustos = codigosCentroCustos;
    }

    public ChequeVO getChequeVO() {
        return chequeVO;
    }

    public void setChequeVO(ChequeVO chequeVO) {
        this.chequeVO = chequeVO;
    }

    public boolean isAgruparPorPlanoDeContasCentroCusto() {
        return agruparPorPlanoDeContasCentroCusto;
    }

    public void setAgruparPorPlanoDeContasCentroCusto(boolean agruparPorPlanoDeContasCentroCusto) {
        this.agruparPorPlanoDeContasCentroCusto = agruparPorPlanoDeContasCentroCusto;
    }

    public Boolean getAgendamentosAndamento() {
        return agendamentosAndamento;
    }

    public void setAgendamentosAndamento(Boolean agendamentosAndamento) {
        this.agendamentosAndamento = agendamentosAndamento;
    }

    public Boolean getSomenteContasMobile() {
        return somenteContasMobile;
    }

    public void setSomenteContasMobile(Boolean somenteContasMobile) {
        this.somenteContasMobile = somenteContasMobile;
    }

    public Date getDataInicioUltimaAlteracao() {
        return dataInicioUltimaAlteracao;
    }

    public void setDataInicioUltimaAlteracao(Date dataInicioUltimaAlteracao) {
        this.dataInicioUltimaAlteracao = dataInicioUltimaAlteracao;
    }

    public Date getDataFimUltimaAlteracao() {
        return dataFimUltimaAlteracao;
    }

    public void setDataFimUltimaAlteracao(Date dataFimUltimaAlteracao) {
        this.dataFimUltimaAlteracao = dataFimUltimaAlteracao;
    }

    public List<PessoaVO> getFavorecidos() {
        if (favorecidos == null) {
            favorecidos = new ArrayList<PessoaVO>();
        }

        return favorecidos;
    }

    public void setFavorecidos(List<PessoaVO> favorecidos) {
        this.favorecidos = favorecidos;
    }

    public List<Integer> getFavorecidosCodigos() {
        return favorecidosCodigos;
    }

    public void setFavorecidosCodigos(List<Integer> favorecidosCodigos) {
        this.favorecidosCodigos = favorecidosCodigos;
    }

    public boolean isMovimentacoesExcluidas() {
        return movimentacoesExcluidas;
    }

    public void setMovimentacoesExcluidas(boolean movimentacoesExcluidas) {
        this.movimentacoesExcluidas = movimentacoesExcluidas;
    }

    public Boolean getSomenteContasHoje() {
        return somenteContasHoje;
    }

    public void setSomenteContasHoje(Boolean somenteContasHoje) {
        this.somenteContasHoje = somenteContasHoje;
    }

    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public void limparDatasTipoVazio(){
        if (tipoPesquisa == null || tipoPesquisa.equals("")){
            limparDatas();
        } else if (tipoPesquisa != null && tipoPesquisa.equals(TipoPesquisaConsultaLancamentos.PERIODO)){
            tipoPeriodo = null;
        }
    }
}

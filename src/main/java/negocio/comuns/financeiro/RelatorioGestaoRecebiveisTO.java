package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;

import negocio.comuns.utilitarias.Uteis;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

public class RelatorioGestaoRecebiveisTO extends SuperTO {

    private static final long serialVersionUID = 5805703777319576297L;
    private List<ResumoFormaPagamentoRelatorio> formasPagamento = new ArrayList<ResumoFormaPagamentoRelatorio>();
    private boolean detalhar = true;
    private Double valor = 0.0;
    private Double valorEspecie = 0.0;
    private Double valorBoleto = 0.0;
    private Double valorContaCorrente = 0.0;
    private Double valorDevolucoes = 0.0;
    private Date dataInicioFaturamento;
    private Date dataFimFaturamento;
    private Date dataInicioCompensacao;
    private Date dataFimCompensacao;

    public List<ResumoFormaPagamentoRelatorio> clonarFormasSemDados() {
        List<ResumoFormaPagamentoRelatorio> formas = new ArrayList<ResumoFormaPagamentoRelatorio>();
        for (ResumoFormaPagamentoRelatorio rfp : formasPagamento) {
            formas.add(rfp.getCopiaLimpa());
        }
        return formas;
    }

    public boolean getFaturamento() {
        return dataInicioFaturamento != null && dataFimFaturamento != null;
    }

    public boolean getCompensacao() {
        return dataInicioCompensacao != null && dataFimCompensacao != null;
    }

    public void setFormasPagamento(List<ResumoFormaPagamentoRelatorio> formasPagamento) {
        this.formasPagamento = formasPagamento;
    }

    public List<ResumoFormaPagamentoRelatorio> getFormasPagamento() {
        return formasPagamento;
    }

    public JRDataSource getFormas() {
        List<ResumoFormaPagamentoRelatorio> lista = new ArrayList<ResumoFormaPagamentoRelatorio>();
        for (ResumoFormaPagamentoRelatorio rfpr : formasPagamento) {
            if (!rfpr.getDevolucao()) {
                lista.add(rfpr);
            }
        }
        JRDataSource jr1 = new JRBeanCollectionDataSource(lista);
        return jr1;

    }

    public JRDataSource getFormas2() {
        JRDataSource jr1 = new JRBeanCollectionDataSource(formasPagamento);
        return jr1;

    }

    public JRDataSource getFormasOutros() {
        List<ResumoFormaPagamentoRelatorio> lista = new ArrayList<ResumoFormaPagamentoRelatorio>();
        for (ResumoFormaPagamentoRelatorio rfpr : formasPagamento) {
            if (rfpr.getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla())
                    || rfpr.getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())
                    || rfpr.getDevolucao()) {
                lista.add(rfpr);
            }
        }
        JRDataSource jr1 = new JRBeanCollectionDataSource(lista);
        return jr1;

    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valor);
    }

    public void setDataInicioFaturamento(Date dataInicioFaturamento) {
        this.dataInicioFaturamento = dataInicioFaturamento;
    }

    public Date getDataInicioFaturamento() {
        return dataInicioFaturamento;
    }

    public String getDataInicioFaturamentoApresentar() {
        return Uteis.getData(dataInicioFaturamento);
    }

    public void setDataFimFaturamento(Date dataFimFaturamento) {
        this.dataFimFaturamento = dataFimFaturamento;
    }

    public Date getDataFimFaturamento() {
        return dataFimFaturamento;
    }

    public String getDataFimFaturamentoApresentar() {
        return Uteis.getData(dataFimFaturamento);
    }

    public void setDataInicioCompensacao(Date dataInicioCompensacao) {
        this.dataInicioCompensacao = dataInicioCompensacao;
    }

    public Date getDataInicioCompensacao() {
        return dataInicioCompensacao;
    }

    public String getDataInicioCompensacaoApresentar() {
        return Uteis.getData(dataInicioCompensacao);
    }

    public void setDataFimCompensacao(Date dataFimCompensacao) {
        this.dataFimCompensacao = dataFimCompensacao;
    }

    public Date getDataFimCompensacao() {
        return dataFimCompensacao;
    }

    public String getDataFimCompensacaoApresentar() {
        return Uteis.getData(dataFimCompensacao);
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getValor() {
        return valor;
    }

    public void setDetalhar(boolean detalhar) {
        this.detalhar = detalhar;
    }

    public boolean getDetalhar() {
        return detalhar;
    }

    public Double getValorEspecie() {
        return valorEspecie;
    }

    public void setValorEspecie(Double valorEspecie) {
        this.valorEspecie = valorEspecie;
    }
    
    public String getValorEspecieApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorEspecie);
    }

    public Double getValorBoleto() {
        return valorBoleto;
    }

    public void setValorBoleto(Double valorBoleto) {
        this.valorBoleto = valorBoleto;
    }
    
    public String getValorBoletoApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorBoleto);
    }
    
    public Double getValorContaCorrente() {
        return valorContaCorrente;
    }

    public void setValorContaCorrente(Double valorContaCorrente) {
        this.valorContaCorrente = valorContaCorrente;
    }
    
    public String getValorContaCorrenteApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorContaCorrente);
    }

    public Double getValorDevolucoes() {
        return valorDevolucoes;
    }

    public void setValorDevolucoes(Double valorDevolucoes) {
        this.valorDevolucoes = valorDevolucoes;
    }
    
    public String getValorDevolucoesApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorDevolucoes);
    }
}

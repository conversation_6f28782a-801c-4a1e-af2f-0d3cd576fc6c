package negocio.comuns.financeiro;

import java.util.Date;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;

public class HistoricoChequeVO extends SuperVO {
	
	private Integer codigo;
	private ChequeVO cheque = new ChequeVO();
	private MovContaVO movConta = new MovContaVO();
	private Date dataInicio;
	private Date dataFim;
	private StatusCheque status;
	private LoteVO lote = new LoteVO();
	
	public String getCustodiaDe(){
		if(getMovConta() != null && getMovConta().getTipoOperacaoLancamento() != null){
			switch (getMovConta().getTipoOperacaoLancamento()) {
			case PAGAMENTO:
				return getMovConta().getPessoaVO().getNome();
			default:
				return getMovConta().getContaVO().getDescricao();
			}	
		}
		return "";
	}
	
	public Integer getCodigo() {
		return codigo;
	}
	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}
	public ChequeVO getCheque() {
		return cheque;
	}
	public void setCheque(ChequeVO cheque) {
		this.cheque = cheque;
	}
	public MovContaVO getMovConta() {
		return movConta;
	}
	public void setMovConta(MovContaVO movConta) {
		this.movConta = movConta;
	}
	public Date getDataInicio() {
		return dataInicio;
	}
	
	public String getDataInicioApresentar() {
        return Uteis.getDataAplicandoFormatacao(dataInicio, "dd/MM/yy HH:mm");
    }
	public String getDataFimApresentar() {
        return Uteis.getDataAplicandoFormatacao(dataFim, "dd/MM/yy HH:mm");
    }
	public void setDataInicio(Date dataInicio) {
		this.dataInicio = dataInicio;
	}
	public Date getDataFim() {
		return dataFim;
	}
	public void setDataFim(Date dataFim) {
		this.dataFim = dataFim;
	}
	public StatusCheque getStatus() {
		return status;
	}
	public void setStatus(StatusCheque status) {
		this.status = status;
	}
	public void setLote(LoteVO lote) {
		this.lote = lote;
	}
	public LoteVO getLote() {
		return lote;
	}

}

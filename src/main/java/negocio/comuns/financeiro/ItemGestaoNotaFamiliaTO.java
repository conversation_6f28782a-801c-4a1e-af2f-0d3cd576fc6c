package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.StatusEnotasEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;


import java.util.*;

/**
 * Created by <PERSON> on 01/12/2015.
 */
public class ItemGestaoNotaFamiliaTO extends SuperTO{

    private Integer codigo;
    private String nome = "";
    private String cnpj = "";
    private Double valor = 0.0;
    private boolean selecionado = false;
    private Boolean nfseemitida = false;
    private Boolean parcialmenteemitida = false;
    private String produtosPagos = "";
    private List<Integer> formasPagamento = new ArrayList<Integer>();
    private Map<Integer, Double> valoresEFormas = new HashMap<Integer,  Double>();
    private Integer codCliente = 0;
    private ClienteVO clientePagador;
    private Integer codColaborador = 0;
    private List<ItemGestaoNotasTO> reciboPagamentoVOs = null;
    private String descricaoProdutosPagos;
    private String retorno = "";
    private JSONObject jsonNotaEnviar;
    private NFSeEmitidaVO nfSeEmitidaVO;
    private Date dataEmissao;
    private Date dataReferenciaItem;
    private SituacaoNotaFiscalEnum situacaoNotaFiscal;
    private Double valorEmitido;
    private List<NFSeEmitidaVO> nfSeEmitidaLista;
    private Integer sequencialFamilia;
    private Integer codNotaFiscal;
    private NotaFiscalVO notaFiscalVO;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Boolean getNfseemitida() {
        return nfseemitida;
    }

    public String getNotaNFSEEmitida(){
        return ((this.nfseemitida != null) && (this.nfseemitida)) ? "SIM": "NÃO";
    }


    public void setNfseemitida(Boolean nfseemitida) {
        this.nfseemitida = nfseemitida;
    }

    public Boolean getParcialmenteemitida() {
        return parcialmenteemitida;
    }

    public void setParcialmenteemitida(Boolean parcialmenteemitida) {
        this.parcialmenteemitida = parcialmenteemitida;
    }

    public String getProdutosPagos() {
        return produtosPagos;
    }

    public void setProdutosPagos(String produtosPagos) {
        this.produtosPagos = produtosPagos;
    }

    public List<Integer> getFormasPagamento() {
        return formasPagamento;
    }

    public void setFormasPagamento(List<Integer> formasPagamento) {
        this.formasPagamento = formasPagamento;
    }

    public Map<Integer, Double> getValoresEFormas() {
        return valoresEFormas;
    }

    public void setValoresEFormas(Map<Integer, Double> valoresEFormas) {
        this.valoresEFormas = valoresEFormas;
    }

    public Integer getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(Integer codCliente) {
        this.codCliente = codCliente;
    }

    public Integer getCodColaborador() {
        return codColaborador;
    }

    public void setCodColaborador(Integer codColaborador) {
        this.codColaborador = codColaborador;
    }

    public List<ItemGestaoNotasTO> getReciboPagamentoVOs() {
        if(reciboPagamentoVOs == null)
            reciboPagamentoVOs = new ArrayList<ItemGestaoNotasTO>();
        return reciboPagamentoVOs;
    }

    public String getValor_apresentar() {
        return Formatador.formatarValorMonetario(getValor());
    }
    public void setReciboPagamentoVOs(List<ItemGestaoNotasTO> reciboPagamentoVOs) {
        this.reciboPagamentoVOs = reciboPagamentoVOs;
    }

    public ClienteVO getClientePagador() {
        if(clientePagador == null)
            clientePagador = new ClienteVO();
        return clientePagador;
    }

    public void setClientePagador(ClienteVO clientePagador) {
        this.clientePagador = clientePagador;
    }

    public String getTextStyle() {
        if (getParcialmenteemitida()) {
            return "color: #000 !important;";
        } else {
            return "";
        }
    }

    public String getStyle() {
        if (getParcialmenteemitida()) {
            return "background-color: rgba(48, 123, 82, 0.24) !important;";
        } else {
            return "background-color: #fff;";
        }
    }

    public String getRetorno() {
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public String getDescricaoProdutosPagos() {
        return descricaoProdutosPagos;
    }

    public void setDescricaoProdutosPagos(String descricaoProdutosPagos) {
        this.descricaoProdutosPagos = descricaoProdutosPagos;
    }

    public JSONObject getJsonNotaEnviar() {
        return jsonNotaEnviar;
    }

    public void setJsonNotaEnviar(JSONObject jsonNotaEnviar) {
        this.jsonNotaEnviar = jsonNotaEnviar;
    }

    public NFSeEmitidaVO getNfSeEmitidaVO() {
        return nfSeEmitidaVO;
    }

    public void setNfSeEmitidaVO(NFSeEmitidaVO nfSeEmitidaVO) {
        this.nfSeEmitidaVO = nfSeEmitidaVO;
    }

    public Date getDataReferenciaItem() {
        return dataReferenciaItem;
    }

    public void setDataReferenciaItem(Date dataReferenciaItem) {
        this.dataReferenciaItem = dataReferenciaItem;
    }

    public Date getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public Double getValorEmitido() {
        if (valorEmitido == null) {
            valorEmitido = 0.0;
        }
        return valorEmitido;
    }

    public void setValorEmitido(Double valorEmitido) {
        this.valorEmitido = valorEmitido;
    }

    public List<NFSeEmitidaVO> getNfSeEmitidaLista() {
        if (nfSeEmitidaLista == null) {
            nfSeEmitidaLista = new ArrayList<NFSeEmitidaVO>();
        }
        return nfSeEmitidaLista;
    }

    public void setNfSeEmitidaLista(List<NFSeEmitidaVO> nfSeEmitidaLista) {
        this.nfSeEmitidaLista = nfSeEmitidaLista;
    }

    public SituacaoNotaFiscalEnum getSituacaoNotaFiscal() {
        return situacaoNotaFiscal;
    }

    public void setSituacaoNotaFiscal(SituacaoNotaFiscalEnum situacaoNotaFiscal) {
        this.situacaoNotaFiscal = situacaoNotaFiscal;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getSequencialFamilia() {
        if (sequencialFamilia == null) {
            sequencialFamilia = 0;
        }
        return sequencialFamilia;
    }

    public void setSequencialFamilia(Integer sequencialFamilia) {
        this.sequencialFamilia = sequencialFamilia;
    }

    public Integer getCodNotaFiscal() {
        if (codNotaFiscal == null) {
            codNotaFiscal = 0;
        }
        return codNotaFiscal;
    }

    public void setCodNotaFiscal(Integer codNotaFiscal) {
        this.codNotaFiscal = codNotaFiscal;
    }

    public NotaFiscalVO getNotaFiscalVO() {
        if (notaFiscalVO == null) {
            notaFiscalVO = new NotaFiscalVO();
        }
        return notaFiscalVO;
    }

    public void setNotaFiscalVO(NotaFiscalVO notaFiscalVO) {
        this.notaFiscalVO = notaFiscalVO;
    }

    public boolean isApresentarExcluirNotaFiscalNFSeFamilia() {
        return !UteisValidacao.emptyNumber(getCodNotaFiscal()) &&
                !UteisValidacao.emptyNumber(getSequencialFamilia()) &&
                !UteisValidacao.emptyNumber(getNotaFiscalVO().getCodigo()) &&
                (getNotaFiscalVO().getStatusNotaEnum().equals(StatusEnotasEnum.ERRO) || getNotaFiscalVO().getStatusNotaEnum().equals(StatusEnotasEnum.NEGADA));
    }
}

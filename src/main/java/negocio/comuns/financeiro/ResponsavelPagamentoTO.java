package negocio.comuns.financeiro;

import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 05/04/2024
 */
public class ResponsavelPagamentoTO extends SuperVO {

    private Integer pessoa;
    private String nome;
    private String cpf;
    private boolean usandoDoResponsavel = false;
    private List<EmailVO> emails;
    private List<TelefoneVO> telefones;

    public ResponsavelPagamentoTO() {

    }

    public ResponsavelPagamentoTO(PessoaVO pessoaVO) {
        this.pessoa = pessoaVO.getCodigo();
        this.nome = pessoaVO.getNome();
        this.cpf = pessoaVO.getCfp();
        this.emails = pessoaVO.getEmailVOs();
        this.telefones = pessoaVO.getTelefoneVOs();
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        if (cpf == null) {
            cpf = "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public List<EmailVO> getEmails() {
        if (emails == null) {
            emails = new ArrayList<>();
        }
        return emails;
    }

    public void setEmails(List<EmailVO> emails) {
        this.emails = emails;
    }

    public List<TelefoneVO> getTelefones() {
        if (telefones == null) {
            telefones = new ArrayList<>();
        }
        return telefones;
    }

    public void setTelefones(List<TelefoneVO> telefones) {
        this.telefones = telefones;
    }

    public boolean isUsandoDoResponsavel() {
        return usandoDoResponsavel;
    }

    public void setUsandoDoResponsavel(boolean usandoDoResponsavel) {
        this.usandoDoResponsavel = usandoDoResponsavel;
    }

    public String getTelefoneValido() {
        for (TelefoneVO telefoneVO : this.getTelefones()) {
            //priorizar telefone celular
            if (telefoneVO.getTipoTelefone().equalsIgnoreCase(TipoTelefoneEnum.CELULAR.getCodigo()) &&
                    !UteisValidacao.emptyString(telefoneVO.getNumero()) &&
                    telefoneVO.getNumero().replaceAll("[^0-9]", "").length() == 11) {
                return telefoneVO.getNumero();
            }
        }
        for (TelefoneVO telefoneVO : this.getTelefones()) {
            if (!UteisValidacao.emptyString(telefoneVO.getNumero()) &&
                    (telefoneVO.getNumero().replaceAll("[^0-9]", "").length() == 10 ||
                    telefoneVO.getNumero().replaceAll("[^0-9]", "").length() == 11)) {
                return telefoneVO.getNumero();
            }
        }
        return null;
    }

    public String getEmailValido() {
        //priorizar o que email correspondencia
        for (EmailVO emailVO : this.getEmails()) {
            if (emailVO.getEmailCorrespondencia() != null && emailVO.getEmailCorrespondencia() &&
                    UteisValidacao.validaEmail(emailVO.getEmail())) {
                return emailVO.getEmail();
            }
        }
        for (EmailVO emailVO : this.getEmails()) {
            if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                return emailVO.getEmail();
            }
        }
        return null;
    }
}

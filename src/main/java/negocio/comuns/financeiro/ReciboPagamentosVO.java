/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import org.json.JSONObject;

/**
 * <AUTHOR>
 */
public class ReciboPagamentosVO extends SuperVO {

   ReciboPagamentoVO reciboPagamentoVO;
   String erro;
   String tipoPagamento;

    public String getTipoPagamento() {
        return tipoPagamento;
    }

    public void setTipoPagamento(String tipoPagamento) {
        this.tipoPagamento = tipoPagamento;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }

    public ReciboPagamentoVO getReciboPagamentoVO() {
        return reciboPagamentoVO;
    }

    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamentoVO) {
        this.reciboPagamentoVO = reciboPagamentoVO;
    }

    public JSONObject toJson(){
        JSONObject json =  new JSONObject();
        if(erro != null) {
            json.put("erro", erro);
        }
        if(reciboPagamentoVO != null) {
            json.put("reciboPagamentoVO", reciboPagamentoVO);
        }

        return json;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class ReceitaSinteticoDWVO extends SuperVO{

    private Integer mes;
    private Integer ano;
    private Double cartaoCredito;
    private Double cartaoDebito;
    private Double chequeVista;
    private Double chequePrazo;
    private Double dinheiro;
    private Double pix;
    private Double transferenciaBancaria;
    private Double boleto;
    private Double outros;

    private Integer empresa;
    private Date dataExecucao;

    public String getMesDescricao(){
        Mes mesEnum = Mes.getMesPeloCodigo(mes);
        return mesEnum == null ? "": mesEnum.getDescricao();
    }

    public String getDataExecucaoApresentar(){
        return dataExecucao == null ? "" :  Uteis.getDataComHHMM(dataExecucao);
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Double getCartaoCredito() {
        return cartaoCredito;
    }

    public void setCartaoCredito(Double cartaoCredito) {
        this.cartaoCredito = cartaoCredito;
    }

    public Double getCartaoDebito() {
        return cartaoDebito;
    }

    public void setCartaoDebito(Double cartaoDebito) {
        this.cartaoDebito = cartaoDebito;
    }

    public Double getChequePrazo() {
        return chequePrazo;
    }

    public void setChequePrazo(Double chequePrazo) {
        this.chequePrazo = chequePrazo;
    }

    public Double getChequeVista() {
        return chequeVista;
    }

    public void setChequeVista(Double chequeVista) {
        this.chequeVista = chequeVista;
    }

    public Date getDataExecucao() {
        return dataExecucao;
    }

    public void setDataExecucao(Date dataExecucao) {
        this.dataExecucao = dataExecucao;
    }

    public Double getDinheiro() {
        return dinheiro;
    }

    public void setDinheiro(Double dinheiro) {
        this.dinheiro = dinheiro;
    }

    public Double getBoleto() {
        return boleto;
    }

    public void setBoleto(Double boleto) {
        this.boleto = boleto;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Double getOutros() {
        return outros;
    }

    public void setOutros(Double outros) {
        this.outros = outros;
    }

    public Double getPix() {
        return pix;
    }

    public void setPix(Double pix) {
        this.pix = pix;
    }

    public Double getTransferenciaBancaria() {
        return transferenciaBancaria;
    }

    public void setTransferenciaBancaria(Double transferenciaBancaria) {
        this.transferenciaBancaria = transferenciaBancaria;
    }
}

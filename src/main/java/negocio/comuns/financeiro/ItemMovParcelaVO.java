/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.contrato.ContratoVO;

/**
 *
 * <AUTHOR>
 */
public class ItemMovParcelaVO extends SuperVO {

    protected ContratoVO contratoVO;
    protected MovParcelaVO movParcelaVO;
    protected String tipoMovParcela;

    public ItemMovParcelaVO() {
        inicializarDados();
    }

    public void inicializarDados() {
        setContratoVO(new ContratoVO());
        setMovParcelaVO(new MovParcelaVO());
        setTipoMovParcela("");
    }

    public ContratoVO getContratoVO() {
        if (contratoVO == null) {
            contratoVO = new ContratoVO();
        }
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public String getTipoMovParcela() {
        if (tipoMovParcela == null) {
            tipoMovParcela = "";
        }
        return tipoMovParcela;
    }

    public void setTipoMovParcela(String tipoMovParcela) {
        this.tipoMovParcela = tipoMovParcela;
    }

    public MovParcelaVO getMovParcelaVO() {
        if (movParcelaVO == null) {
            movParcelaVO = new MovParcelaVO();
        }
        return movParcelaVO;
    }

    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }
}

package negocio.comuns.financeiro;

import java.math.BigDecimal;

class ItemFaturamentoTOCreditoAcima12X extends ItemFaturamentoTO {
    private final BigDecimal taxa = new BigDecimal("1.9");

    @Override
    public BigDecimal getTaxaSTONE() {
        return taxa;
    }

    @Override
    public String getFormaPagamento() {
        return "CRÉDITO ACIMA DE 12X (Somente recorrência)";
    }

    @Override
    public int getOrdem() {
        return 4;
    }
}

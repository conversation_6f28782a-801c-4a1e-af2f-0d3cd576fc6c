package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 15/05/2020
 */
public class ConvenioCobrancaRateioItemVO extends SuperVO {

    private Integer codigo;
    private Date dataRegistro;
    private Date dataAlteracao;
    private ConvenioCobrancaRateioVO convenioCobrancaRateioVO;
    private String nomeRecebedor;
    private String idRecebedor;
    private Double porcentagem;
    private boolean recebedorPrincipal;

    public ConvenioCobrancaRateioItemVO() {
        super();
    }

    public ConvenioCobrancaRateioItemVO(ConvenioCobrancaRateioVO convenioCobrancaRateioVO) {
        super();
        this.convenioCobrancaRateioVO = convenioCobrancaRateioVO;
    }

    public static void validarDados(ConvenioCobrancaRateioItemVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (UteisValidacao.emptyString(obj.getIdRecebedor().trim())) {
            throw new ConsistirException("Selecione um recebedor.");
        }

        if (UteisValidacao.emptyNumber(obj.getPorcentagem())) {
            throw new ConsistirException("Informe o percentual.");
        }
        if (obj.getPorcentagem() > 100) {
            throw new ConsistirException("A porcentagem não pode ser superior à 100.");
        }
    }

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }

    public ConvenioCobrancaRateioVO getConvenioCobrancaRateioVO() {
        if (convenioCobrancaRateioVO == null) {
            convenioCobrancaRateioVO = new ConvenioCobrancaRateioVO();
        }
        return convenioCobrancaRateioVO;
    }

    public void setConvenioCobrancaRateioVO(ConvenioCobrancaRateioVO convenioCobrancaRateioVO) {
        this.convenioCobrancaRateioVO = convenioCobrancaRateioVO;
    }

    public String getNomeRecebedor() {
        if (nomeRecebedor == null) {
            nomeRecebedor = "";
        }
        return nomeRecebedor;
    }

    public void setNomeRecebedor(String nomeRecebedor) {
        this.nomeRecebedor = nomeRecebedor;
    }

    public String getIdRecebedor() {
        if (idRecebedor == null) {
            idRecebedor = "";
        }
        return idRecebedor;
    }

    public void setIdRecebedor(String idRecebedor) {
        this.idRecebedor = idRecebedor;
    }

    public Double getPorcentagem() {
        if (porcentagem == null) {
            porcentagem = 0.0;
        }
        return porcentagem;
    }

    public void setPorcentagem(Double porcentagem) {
        this.porcentagem = porcentagem;
    }

    public String getPorcentagem_Apresentar() {
        return getPorcentagem().toString().replace(".",",") + "%";
    }

    public boolean isRecebedorPrincipal() {
        return recebedorPrincipal;
    }

    public void setRecebedorPrincipal(boolean recebedorPrincipal) {
        this.recebedorPrincipal = recebedorPrincipal;
    }
}

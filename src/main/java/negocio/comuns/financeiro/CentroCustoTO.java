package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * <PERSON>onsável por manter os dados da entidade de custos
 */
public class CentroCustoTO extends SuperVO {

    
    private Integer codigo;
    private String codigoCentro;
    private String codigoTrv;
    private String descricao;
    private boolean leaf;
    private CentroCustoTO centroPai;
    private boolean ativo = true;

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public static void validarDados(CentroCustoTO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getCodigoCentro().isEmpty()) {
            throw new ConsistirException("O campo CÓDIGO CONTÁBIL deve ser informado");
        }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO deve ser informado.");
        }
        if (obj.getCodigo() != 0 && obj.getCentroPai().getCodigo() != 0 && obj.getCodigo() == obj.getCentroPai().getCodigo()) {
            throw new ConsistirException("O campo CENTRO PAI e esse centro não podem ser iguais");
        }
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCodigoCentro() {
        if (codigoCentro == null) {
            codigoCentro = "";
        }
        return codigoCentro;
    }

    public String getDescricaoCurtaComCodigo(){
        if ((this.codigo != null) && (this.codigo > 0)){
            return this.getDescricaoCurta() + " (" + this.codigo + ")";
        }
        return getDescricaoCurta();
    }


    public void setCodigoCentro(String codigoCentro) {
        this.codigoCentro = codigoCentro;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public Long getCodigoNode() {
        String codigo = this.getCodigoCentro();
        codigo = codigo.replace(".", "");
        return Long.parseLong(codigo.trim());
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isLeaf() {
        return leaf;
    }

    public void setLeaf(boolean leaf) {
        this.leaf = leaf;
    }

    public String getCodigoTrv() {
        return codigoTrv;
    }

    public void setCodigoTrv(String codigoTrv) {
        this.codigoTrv = codigoTrv;
    }

    public String getDescricaoCurta() {
        return getCodigoCentro() + " - " + getDescricao();
    }

    /**
     * @return the planoPai
     */
    public CentroCustoTO getCentroPai() {
        return centroPai;
    }

    /**
     * @param planoPai the planoPai to set
     */
    public void setCentroPai(CentroCustoTO centroPai) {
        this.centroPai = centroPai;
    }

    public boolean filhoDe(String codigoPai) {
        return (this.getCodigoCentro().length() > codigoPai.length() && this.getCodigoCentro().substring(0, codigoPai.length()).equals(codigoPai));
    }
}

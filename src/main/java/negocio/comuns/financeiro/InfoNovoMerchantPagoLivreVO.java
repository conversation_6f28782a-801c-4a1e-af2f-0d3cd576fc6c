package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class InfoNovoMerchantPagoLivreVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo = 0;
    protected Date dataRegistro;
    protected String paramsEnvio;
    protected String paramsResposta;
    protected int tipoConvenioCobranca;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getParamsEnvio() {
        return paramsEnvio;
    }

    public void setParamsEnvio(String paramsEnvio) {
        this.paramsEnvio = paramsEnvio;
    }

    public String getParamsResposta() {
        return paramsResposta;
    }

    public void setParamsResposta(String paramsResposta) {
        this.paramsResposta = paramsResposta;
    }

    public int getTipoConvenioCobranca() {
        return tipoConvenioCobranca;
    }

    public void setTipoConvenioCobranca(int tipoConvenioCobranca) {
        this.tipoConvenioCobranca = tipoConvenioCobranca;
    }
}

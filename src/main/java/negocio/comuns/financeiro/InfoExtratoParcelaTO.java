package negocio.comuns.financeiro;

import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class InfoExtratoParcelaTO {

    private Date pagamento;
    private String meio;
    private Integer codigoMovproduto;
    private String nota;
    private String fp;
    private String statusProtheus;

    public Integer getCodigoMovproduto() {
        return codigoMovproduto;
    }

    public void setCodigoMovproduto(Integer codigoMovproduto) {
        this.codigoMovproduto = codigoMovproduto;
    }

    public Date getPagamento() {
        return pagamento;
    }

    public void setPagamento(Date pagamento) {
        this.pagamento = pagamento;
    }

    public String getMeio() {
        return meio;
    }

    public void setMeio(String meio) {
        this.meio = meio;
    }

    public String getNota() {
        return nota;
    }

    public void setNota(String nota) {
        this.nota = nota;
    }

    public String getStatusProtheus() {
        return statusProtheus;
    }

    public void setStatusProtheus(String statusProtheus) {
        this.statusProtheus = statusProtheus;
    }

    public String getPagamentoApresentar() {
        return (Uteis.getData(pagamento));
    }

    public String getFp() {
        return fp;
    }

    public void setFp(String fp) {
        this.fp = fp;
    }
}

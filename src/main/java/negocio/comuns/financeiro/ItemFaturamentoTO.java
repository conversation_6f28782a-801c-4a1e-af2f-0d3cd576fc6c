package negocio.comuns.financeiro;

import java.math.BigDecimal;

public abstract class ItemFaturamentoTO {
    private BigDecimal valor;
    private BigDecimal taxaReais;
    private BigDecimal taxaReaisSTONE;
    private BigDecimal economia;
    private BigDecimal taxaPorcentagem;

    public abstract BigDecimal getTaxaSTONE();
    public abstract String getFormaPagamento();

    ItemFaturamentoTO() {
        taxaPorcentagem = new BigDecimal(0);
        valor = new BigDecimal(0);
    }

    public void setTaxaPorcentagem(BigDecimal taxaPorcentagem) {
        this.taxaPorcentagem = taxaPorcentagem;
        calcular();
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
        calcular();
    }

    private void calcular() {
        if (taxaPorcentagem != null && valor != null) {
            this.taxaReais = valor.multiply(taxaPorcentagem).divide(new BigDecimal(100), 2, BigDecimal.ROUND_CEILING);
            this.taxaReaisSTONE = valor.multiply(getTaxaSTONE()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_CEILING);
            this.economia = taxaReais.subtract(taxaReaisSTONE);
        }
    }

    public BigDecimal getValor() {
        return valor;
    }

    public BigDecimal getTaxaReais() {
        return taxaReais;
    }

    public BigDecimal getEconomia() {
        return economia;
    }

    public BigDecimal getTaxaPorcentagem() {
        return taxaPorcentagem;
    }

    public boolean isPossuiTaxa() {
        return taxaPorcentagem != null && taxaPorcentagem.doubleValue() != 0;
    }

    public BigDecimal getTaxaReaisSTONE() {
        return taxaReaisSTONE;
    }

    public boolean isValorInformado() {
        return valor != null && valor.doubleValue() != 0;
    }

    public abstract int getOrdem();

    public boolean atualizar(ItemFaturamentoTO item) {
        if (getFormaPagamento().equals(item.getFormaPagamento())) {
            setValor(getValor().add(item.getValor()));
            if (item.getTaxaPorcentagem().doubleValue() > getTaxaPorcentagem().doubleValue())
                setTaxaPorcentagem(item.getTaxaPorcentagem());

            return true;
        }

        return false;
    }
}

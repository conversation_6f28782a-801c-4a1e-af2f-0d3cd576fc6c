package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade Banco. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class BancoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer codigoBanco;
    protected String nome;
    protected String ispb;

    /**
     * Construtor padrão da classe <code>Banco</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public BancoVO() {
        super();
        inicializarDados();
    }

    public BancoVO(Integer codigo, Integer codigoBanco, String nome) {
        this.codigo = codigo;
        this.codigoBanco = codigoBanco;
        this.nome = nome;
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>BancoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(BancoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getNome().equals("")) {
            throw new ConsistirException("O campo NOME (Banco) deve ser informado.");
        }
        if (obj.getCodigoBanco() == 0) {
            throw new ConsistirException("O campo CÓDIGO DO BANCO (Banco) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setNome(getNome().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setCodigoBanco(new Integer(0));
        setNome("");
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return (nome);
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoBanco() {
        if (codigoBanco == null) {
            codigoBanco = 0;
        }
        return codigoBanco;
    }

    public void setCodigoBanco(Integer codigoBanco) {
        this.codigoBanco = codigoBanco;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final BancoVO other = (BancoVO) obj;
        if (this.codigo != other.codigo && (this.codigo == null || !this.codigo.equals(other.codigo))) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 89 * hash + (this.codigo != null ? this.codigo.hashCode() : 0);
        return hash;
    }

    public String getNomeByCodigoBanco(){
        try {
            return getFacade().getBanco().consultarCodigoBanco(this.codigoBanco, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS).getNome();
        }catch (Exception ignore){
            return "";
        }
    }

    public String getIspb() {
        if (UteisValidacao.emptyString(ispb)) {
            return "";
        }
        return ispb;
    }

    public void setIspb(String ispb) {
        this.ispb = ispb;
    }
}

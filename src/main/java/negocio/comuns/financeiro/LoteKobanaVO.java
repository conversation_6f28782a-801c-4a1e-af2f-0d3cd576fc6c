package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoContaPagarLoteEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.RegistrationStatusKobanaEnum;
import negocio.comuns.financeiro.enumerador.StatusLoteKobanaEnum;
import negocio.comuns.financeiro.enumerador.StatusPagamentoKobanaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 09/07/2024.
 */

public class LoteKobanaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer empresa;
    protected double valor;
    protected String uid;
    protected StatusLoteKobanaEnum status;
    protected RegistrationStatusKobanaEnum registration_status;
    protected String financial_account_uid;
    protected Date created_at;
    protected Date updated_at;
    protected String paramsEnvio;
    protected String paramsRetorno;
    protected TipoContaPagarLoteEnum tipoContaPagarLoteEnum;
    protected List<LoteKobanaItemVO> listaLoteKobanaItemVO = new ArrayList<>();

    private List<FinancialAccountKobanaVO> financialAccountsKobanaVO;

    public LoteKobanaVO() {
        super();
    }

    public LoteKobanaVO(int empresa, JSONObject dados) throws Exception {
        this.empresa = empresa;
        this.uid = dados.getString("uid");
        this.registration_status = RegistrationStatusKobanaEnum.obterPorValue(dados.getString("registration_status"));
        this.financial_account_uid = dados.getString("financial_account_uid");
        this.created_at = Uteis.getDate(dados.getString("created_at"), "yyyy-MM-dd'T'HH:mm:ss");
        this.updated_at = Uteis.getDate(dados.getString("updated_at"), "yyyy-MM-dd'T'HH:mm:ss");
        this.status = StatusLoteKobanaEnum.obterPorValue(dados.getString("status"));
        //boleto, boleto de consumo e pix
        if (dados.has("payments")) {
            JSONArray payments = dados.getJSONArray("payments");
            for (int i = 0; i < payments.length(); i++) {
                JSONObject payment = payments.getJSONObject(i);
                LoteKobanaItemVO item = new LoteKobanaItemVO(payment);
                listaLoteKobanaItemVO.add(item);
                this.valor += payment.getDouble("amount");
            }
        }
        //transferência
        else if (dados.has("transfers")) {
            JSONArray transfers = dados.getJSONArray("transfers");
            for (int i = 0; i < transfers.length(); i++) {
                JSONObject payment = transfers.getJSONObject(i);
                LoteKobanaItemVO item = new LoteKobanaItemVO(payment);
                listaLoteKobanaItemVO.add(item);
                this.valor += payment.getDouble("amount");
            }
        }
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUid() {
        if (UteisValidacao.emptyString(uid)) {
            return "";
        }
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public StatusLoteKobanaEnum getStatus() {
        return status;
    }

    public void setStatus(StatusLoteKobanaEnum status) {
        this.status = status;
    }

    public void setCreated_at(Date created_at) {
        this.created_at = created_at;
    }

    public Date getUpdated_at() {
        return updated_at;
    }

    public void setUpdated_at(Date updated_at) {
        this.updated_at = updated_at;
    }

    public RegistrationStatusKobanaEnum getRegistration_status() {
        return registration_status;
    }

    public void setRegistration_status(RegistrationStatusKobanaEnum registration_status) {
        this.registration_status = registration_status;
    }

    public Date getCreated_at() {
        return created_at;
    }

    public String getFinancial_account_uid() {
        if (UteisValidacao.emptyString(financial_account_uid)) {
            return "";
        }
        return financial_account_uid;
    }

    public void setFinancial_account_uid(String financial_account_uid) {
        this.financial_account_uid = financial_account_uid;
    }

    public List<FinancialAccountKobanaVO> getFinancialAccountsKobanaVO() {
        return financialAccountsKobanaVO;
    }

    public void setFinancialAccountsKobanaVO(List<FinancialAccountKobanaVO> financialAccountsKobanaVO) {
        this.financialAccountsKobanaVO = financialAccountsKobanaVO;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getParamsEnvio() {
        if (UteisValidacao.emptyString(paramsEnvio)) {
            return "";
        }
        return paramsEnvio;
    }

    public void setParamsEnvio(String paramsEnvio) {
        this.paramsEnvio = paramsEnvio;
    }

    public String getParamsRetorno() {
        if (UteisValidacao.emptyString(paramsRetorno)) {
            return "";
        }
        return paramsRetorno;
    }

    public void setParamsRetorno(String paramsRetorno) {
        this.paramsRetorno = paramsRetorno;
    }

    public List<LoteKobanaItemVO> getListaLoteKobanaItemVO() {
        return listaLoteKobanaItemVO;
    }

    public void setListaLoteKobanaItemVO(List<LoteKobanaItemVO> listaLoteKobanaItemVO) {
        this.listaLoteKobanaItemVO = listaLoteKobanaItemVO;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public TipoContaPagarLoteEnum getTipoContaPagarLoteEnum() {
        return tipoContaPagarLoteEnum;
    }

    public void setTipoContaPagarLoteEnum(TipoContaPagarLoteEnum tipoContaPagarLoteEnum) {
        this.tipoContaPagarLoteEnum = tipoContaPagarLoteEnum;
    }

    public String getCreatedAt_Apresentar() {
        if (created_at == null) {
            return "";
        }
        return Uteis.getDataComHora(created_at);
    }

    public String getUpdatedAt_Apresentar() {
        if (updated_at == null) {
            return "";
        }
        return Uteis.getDataComHora(updated_at);
    }

    public String getValor_Apresentar() {
        return "R$ " + Formatador.formatarValorMonetarioSemMoeda(valor);
    }

    public String getTipoContaPagarLote_Apresentar() {
        if (tipoContaPagarLoteEnum == null) {
            return "";
        }
        return tipoContaPagarLoteEnum.getDescricaoExibir();
    }

    public String getStatus_Apresentar() {
        if (status == null) {
            return "";
        }
        return status.getNomeApresentar();
    }

    public String getRegistration_Status_Apresentar() {
        if (registration_status == null) {
            return "";
        }
        return registration_status.getDescricao();
    }

    public String getDescricaoUID_Apresentar() {
        return "Lote - " + uid;
    }

    public String getImagemTipo_Apresentar() {
        if (tipoContaPagarLoteEnum == null) {
            return "";
        } else if (tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.PAYLOAD_PIX)) {
            return "/imagens/pix-text-logo.png";
        } else if (tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.TRANSFERENCIA)) {
            return "/imagens/transfer-text-logo.png";
        } else if (tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.BOLETO)) {
            return "/imagens/boleto-text-logo.png";
        } else if (tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.BOLETO_CONTA_CONSUMO)) {
            return "/imagens/consumo-text-logo.png";
        }
        return "";
    }

    public String getQtd_Apresentar() {
        if (UteisValidacao.emptyList(listaLoteKobanaItemVO)) {
            return "0";
        }
        return String.valueOf(listaLoteKobanaItemVO.size());
    }

    public String getStyleCssImagemTipo() {
        if (tipoContaPagarLoteEnum == null) {
            return "";
        } else if (tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.PAYLOAD_PIX)) {
            return "width: 48px;";
        } else if (tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.TRANSFERENCIA)) {
            return "width: 107px;";
        } else if (tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.BOLETO)) {
            return "width: 70px;";
        } else if (tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.BOLETO_CONTA_CONSUMO)) {
            return "width: 89px;";
        }
        return "";
    }

    public String getStyleCssStatus() {
        if (status == null) {
            return "";
        } else {
            return "background-color:" + status.getColor() + "!important;";
        }
    }

    public String getTitleExplicacaoStatusLote() {
        if (UteisValidacao.emptyString(getParamsRetorno())) {
            return "";
        }
        if (getStatus() != null && (getStatus().equals(StatusLoteKobanaEnum.GERADO) ||
                getStatus().equals(StatusLoteKobanaEnum.REPROVED) ||
                getStatus().equals(StatusLoteKobanaEnum.APPROVED))) {
            return getStatus().getDescricao();
        }
        try {
            String msg = "";
            JSONArray erros = new JSONObject(getParamsRetorno()).getJSONArray("errors");

            // Percorrendo o JSONArray de erros do retorno
            for (int i = 0; i < erros.length(); i++) {
                // Obtendo cada objeto de erro
                JSONObject erro = erros.getJSONObject(i);
                String detail = erro.getString("detail");
                msg += detail + " | ";
            }
            if (msg.contains("Payments payable code precisa conter 44, 47 ou 48 caracteres numéricos")) {
                return "Existem códigos de barras incorretos para um ou mais boletos. Verifique e tente novamente.";
            }
            if (msg.contains("CNPJ da Empresa não é válido")) {
                msg = msg.replace("CNPJ da Empresa não é válido", "CNPJ da Empresa não é válido ou já existe um cadastro com este CNPJ");
                return msg;
            }
            if (UteisValidacao.emptyString(msg)) {
                return getParamsRetorno();
            }
            return Uteis.removerUltimosCaracteres(msg, 3);
        } catch (Exception e) {
            return "";
        }
    }

    public String getStyleOpacityPermiteAprovarReprovarLote() {
        if (!isPermiteAprovarReprovarLote()) {
            return "opacity: 0.2; text-decoration: none; cursor: no-drop;";
        }
        return "";
    }

    public String getStyleOpacityPermiteSincronizarLote() {
        if (!isPermiteSincronizarLote()) {
            return "opacity: 0.2; text-decoration: none; cursor: no-drop;";
        }
        return "";
    }


    public String getTitleIconAprovarLote() {
        if (!isPermiteAprovarReprovarLote()) {
            return "Este Lote não está apto para aprovação";
        } else {
            return "Aprovar Lote para pagamento";
        }
    }

    public String getTitleIconReprovarLote() {
        if (!isPermiteAprovarReprovarLote()) {
            return "Este Lote não está apto para reprovação";
        } else {
            return "Reprovar Lote para pagamento";
        }
    }

    public String getTitleIconSincronizarLote() {
        if (!isPermiteSincronizarLote()) {
            return "Este Lote não está apto para sincronização";
        } else {
            return "Sincronizar Lote de pagamento";
        }
    }

    public boolean isPermiteSincronizarLote() {
        return getStatus() != null && (getStatus().equals(StatusLoteKobanaEnum.NENHUM)
                || getStatus().equals(StatusLoteKobanaEnum.GERADO)
                || getStatus().equals(StatusLoteKobanaEnum.AWAITING_APPROVAL)
                || getStatus().equals(StatusLoteKobanaEnum.APPROVED)
                || getStatus().equals(StatusLoteKobanaEnum.REPROVED));
    }

    public boolean isPermiteAprovarReprovarLote() {
        return getStatus() != null && getStatus().equals(StatusLoteKobanaEnum.AWAITING_APPROVAL);
    }

    public String getTitleUltAtualizacaoLoteExplicacao() {
        return "Exibe a data da última atualização do status ou do status do registro do <b>lote</b> em sí." +
                "</br> Caso queira ver os status dos <b>pagamentos</b> dentro deste lote, clique em 'Ver pagamentos do lote' na coluna de ações." +
                "</br> Qualquer atualização é enviada automaticamente para a Pacto pelo próprio Banco.";
    }

    public String getUidLoteCopiar() {
        return "<b>Uid do lote:</b> " + getUid() + " <br><br> <b>Clique para Copiar</b>";
    }
}



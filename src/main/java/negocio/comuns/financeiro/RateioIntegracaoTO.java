package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.enumeradores.TipoRateioEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Reponsável por manter os dados da entidade de rateio
 */
public class RateioIntegracaoTO extends SuperVO {

    private static final long serialVersionUID = -1700729928511510401L;
    @ChavePrimaria
    private Integer codigo;
    private Integer codigoProduto = 0;
    private Integer codigoModalidade = 0;
    private Integer codigoCategoria = 0;
    private Integer codigoCentroCustos = 0;
    private Integer codigoPlanoContas = 0;
    private Integer tipoRateio = 0;
    private Double percentagem;
    private String percentagemDesc;
    private String descricao = "";
    private String nomePlano = "";
    private String codigoPlano = "";
    private String nomeCentro = "";
    private String codigoCentro = "";
    private String nomeProduto = "";
    private TipoES tipoES;
    private Integer planoContasRateio = 0;
    // flag para indicar se o rateio foi alterado ou não.
    private boolean update;
    private double totalPlanoConta; // Atributo utilizado na visualização dos detalhes do lançamento, no demonstrativo financeiro
    private double totalCentroCusto; // Atributo utilizado na visualização dos detalhes do lançamento, no demonstrativo financeiro
    private double percentagemCentroCusto; // Atributo utilizado na visualização dos detalhes do lançamento, no demonstrativo financeiro
    private String stylePlanoConta;
    private String styleCentroCusto;
    private boolean rateioAmbientes = false;
    private boolean rateioServicos = false;
    private Integer codigoProdutoCE = 0;
    private Integer codigoTipoProdutoCE = 0;
    private Integer codigoServico = 0;
    private Integer codigoAmbiente = 0;

    private boolean credito = false;
    private boolean geralModalidade = false;
    private boolean devolucaoCredito = false;
    
    private Integer empresa = null; 
    private String nomeEmpresa = "";
    protected Boolean validarDados = true;
    private Integer planoConta;
    private Integer centroCusto;

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }
    
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public Integer getCodigoModalidade() {
        return codigoModalidade;
    }

    public void setCodigoModalidade(Integer codigoModalidade) {
        this.codigoModalidade = codigoModalidade;
    }

    public Integer getCodigoCategoria() {
        return codigoCategoria;
    }

    public void setCodigoCategoria(Integer codigoCategoria) {
        this.codigoCategoria = codigoCategoria;
    }

    public Integer getCodigoCentroCustos() {
        return codigoCentroCustos;
    }

    public void setCodigoCentroCustos(Integer codigoCentroCustos) {
        this.codigoCentroCustos = codigoCentroCustos;
    }

    public Integer getCodigoPlanoContas() {
        return codigoPlanoContas;
    }

    public void setCodigoPlanoContas(Integer codigoPlanoContas) {
        this.codigoPlanoContas = codigoPlanoContas;
    }

    public Double getPercentagem() {
        return percentagem;
    }

    public void setPercentagem(Double percentagem) {
        this.percentagem = percentagem;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isUpdate() {
        return update;
    }

    public void setUpdate(boolean update) {
        this.update = update;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public String getCodigoPlano() {
        return codigoPlano;
    }

    public void setCodigoPlano(String codigoPlano) {
        this.codigoPlano = codigoPlano;
    }

    public String getNomeCentro() {
        return nomeCentro;
    }

    public void setNomeCentro(String nomeCentro) {
        this.nomeCentro = nomeCentro;
    }

    public String getCodigoCentro() {
        return codigoCentro;
    }

    public void setCodigoCentro(String codigoCentro) {
        this.codigoCentro = codigoCentro;
    }

    public String getDescPlano() {
        return this.getCodigoPlano() + " " + this.getNomePlano();
    }

    public String getDescCentro() {
        return this.getCodigoCentro() + " " + this.getNomeCentro();
    }

    public String toString() {
        return "Rateio:" + this.getCodigoCentroCustos() + ","
                + this.getCodigoPlanoContas() + "," + this.getPercentagem();
    }

    public String getPercentagemDesc() {
        return percentagemDesc;
    }

    public void setPercentagemDesc(String percentagemDesc) {
        this.percentagemDesc = percentagemDesc;
    }

    public boolean equals(Object obj) {
        if (obj instanceof RateioIntegracaoTO) {
            RateioIntegracaoTO objRateio = (RateioIntegracaoTO) obj;
            if (objRateio.getCodigoPlanoContas().equals(
                    this.getCodigoPlanoContas())
                    && objRateio.getCodigoCentroCustos().equals(
                    this.getCodigoCentroCustos()) && 
                    ((this.getEmpresa() == null && objRateio.getEmpresa() == null)
                        || (this.getEmpresa() != null && objRateio.getEmpresa() != null && this.getEmpresa().equals(objRateio.getEmpresa())))
                    ) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public String getNomeProduto() {
        return nomeProduto;
    }

    public void setNomeProduto(String nomeProduto) {
        this.nomeProduto = nomeProduto;
    }

    /**
     * @param tipoRateio the tipoRateio to set
     */
    public void setTipoRateio(Integer tipoRateio) {
        this.tipoRateio = tipoRateio;
    }

    /**
     * @return the tipoRateio
     */
    public Integer getTipoRateio() {
        if (!UteisValidacao.emptyNumber(this.getCodigoCentroCustos())) {
            tipoRateio = TipoRateioEnum.CENTRO_CUSTOS.getCodigo();
        }
        if (!UteisValidacao.emptyNumber(this.getCodigoPlanoContas())) {
            tipoRateio = TipoRateioEnum.PLANO_CONTAS.getCodigo();
        }
        if (tipoRateio == null) {
            tipoRateio = TipoRateioEnum.PLANO_CONTAS.getCodigo();
        }
        return tipoRateio;
    }

    public double getPercent() {
        return this.getPercentagem() / 100;
    }

    public TipoES getTipoES() {
        return tipoES;
    }

    public void setTipoES(TipoES tipoES) {
        this.tipoES = tipoES;
    }

    /**
     * @return the planoContasRateio
     */
    public Integer getPlanoContasRateio() {
        return planoContasRateio;
    }

    /**
     * @param planoContasRateio the planoContasRateio to set
     */
    public void setPlanoContasRateio(Integer planoContasRateio) {
        this.planoContasRateio = planoContasRateio;
    }

    public double getPercentagemCentroCusto() {
        return percentagemCentroCusto;
    }

    public void setPercentagemCentroCusto(double percentagemCentroCusto) {
        this.percentagemCentroCusto = percentagemCentroCusto;
    }

    public double getTotalCentroCusto() {
        return totalCentroCusto;
    }

    public void setTotalCentroCusto(double totalCentroCusto) {
        this.totalCentroCusto = totalCentroCusto;
    }

    public double getTotalPlanoConta() {
        return totalPlanoConta;
    }

    public String getTotalPlanoContaSemArredondamento() {
        return Uteis.naoArredondarRetString(totalPlanoConta);
    }

    public void setTotalPlanoConta(double totalPlanoConta) {
        this.totalPlanoConta = totalPlanoConta;
    }

    public String getStyleCentroCusto() {
        return styleCentroCusto;
    }

    public void setStyleCentroCusto(String styleCentroCusto) {
        this.styleCentroCusto = styleCentroCusto;
    }

    public String getStylePlanoConta() {
        return stylePlanoConta;
    }

    public void setStylePlanoConta(String stylePlanoConta) {
        this.stylePlanoConta = stylePlanoConta;
    }

    public void setRateioAmbientes(boolean rateioAmbiente) {
        this.rateioAmbientes = rateioAmbiente;
    }

    public boolean getRateioAmbientes() {
        return rateioAmbientes;
    }

    public void setCodigoProdutoCE(Integer codigoProdutoCE) {
        this.codigoProdutoCE = codigoProdutoCE;
    }

    public Integer getCodigoProdutoCE() {
        return codigoProdutoCE;
    }

    public void setCodigoTipoProdutoCE(Integer codigoTipoProducao) {
        this.codigoTipoProdutoCE = codigoTipoProducao;
    }

    public Integer getCodigoTipoProdutoCE() {
        return codigoTipoProdutoCE;
    }

    public void setCodigoServico(Integer codigoServico) {
        this.codigoServico = codigoServico;
    }

    public Integer getCodigoServico() {
        return codigoServico;
    }

    public void setCodigoAmbiente(Integer codigoAmbiente) {
        this.codigoAmbiente = codigoAmbiente;
    }

    public Integer getCodigoAmbiente() {
        return codigoAmbiente;
    }

    public void setRateioServicos(boolean rateioServicos) {
        this.rateioServicos = rateioServicos;
    }

    public boolean getRateioServicos() {
        return rateioServicos;
    }

    public boolean isCredito() {
        return credito;
    }

    public void setCredito(boolean credito) {
        this.credito = credito;
    }

    public boolean isDevolucaoCredito() {
        return devolucaoCredito;
    }

    public void setDevolucaoCredito(boolean devolucaoCredito) {
        this.devolucaoCredito = devolucaoCredito;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public boolean isGeralModalidade() {
        return geralModalidade;
    }

    public void setGeralModalidade(boolean geralModalidade) {
        this.geralModalidade = geralModalidade;
    }
    
    public Boolean getValidarDados() {
        return validarDados;
    }
    
    public void setValidarDados(Boolean validarDados) {
        this.validarDados = validarDados;
}

    public Integer getPlanoConta() {
        return planoConta;
    }

    public void setPlanoConta(Integer planoConta) {
        this.planoConta = planoConta;
    }

    public Integer getCentroCusto() {
        return centroCusto;
    }

    public void setCentroCusto(Integer centroCusto) {
        this.centroCusto = centroCusto;
    }
}

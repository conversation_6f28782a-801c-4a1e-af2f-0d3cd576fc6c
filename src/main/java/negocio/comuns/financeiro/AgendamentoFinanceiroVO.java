
package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.financeiro.enumerador.FrequenciaAgendamento;
import negocio.comuns.financeiro.enumerador.LayoutDescricao;
import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class AgendamentoFinanceiroVO extends SuperVO {
    @ChavePrimaria
    private Integer codigo = 0;
    private String descricao = "";
    private FrequenciaAgendamento frequencia = FrequenciaAgendamento.MENSAL;
    private LayoutDescricao layoutDescricao = LayoutDescricao.MES_REFERENCIA;
    private Date proximoVencimento = Calendario.hoje();
    private Date vencimentoUltimaParcela = Calendario.hoje();
    private boolean usaParcelasFixas = false;
    private int parcelaInicial = 0;
    private int parcelaFinal = 0;
    private int qtdeParcelasGerar = FrequenciaAgendamento.MENSAL.getQtdeParcelasGerar();
    private int qtdeDiasNovaGeracao = FrequenciaAgendamento.MENSAL.getQtdeDiasNovaGeracao();
    private int diaVencimento = 0;
    private Date dataLancamento = Calendario.hoje();
    private Boolean dataCalendarioPreenchida = false;
    private Boolean gerarApenasDiasUteis = false;

    public void validarDados() throws Exception {
        if(descricao.trim().isEmpty())
            throw new Exception("Informe a descrição.");
        if(proximoVencimento == null)
            throw new Exception("Informe a data do proximo vencimento.");
        if(usaParcelasFixas) {
            if(parcelaInicial == 0 || parcelaFinal == 0)
                throw new Exception("Informe a Parcela Inicial e a Parcela Final.");
            if(parcelaInicial <= 0)
                throw new Exception("A Parcela Inicial dever ser maior que zero.");
            if(parcelaInicial > parcelaFinal)
                throw new Exception("A Parcela Inicial não pode ser maior que a Parcela Final.");
        } else
            validarQtdes();
    }

    private void validarQtdes() throws Exception {
        int maxDiasParcelas = frequencia.getQtdeDias()*qtdeParcelasGerar;
        if(qtdeDiasNovaGeracao > maxDiasParcelas)
            throw new Exception("Quantidade de dias faltando para vencer a última parcela deve ser menor que "+maxDiasParcelas);
//        if(vencimentoUltimaParcela == null)
//            throw new Exception("Informe a data de vencimento da ultima parcela.");
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer Codigo) {
        this.codigo = Codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public FrequenciaAgendamento getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(FrequenciaAgendamento frequencia) {
        this.frequencia = frequencia;
    }

    public LayoutDescricao getLayoutDescricao() {
        return layoutDescricao;
    }

    public void setLayoutDescricao(LayoutDescricao layoutDescricao) {
        this.layoutDescricao = layoutDescricao;
    }

    public Date getProximoVencimento() {
        return proximoVencimento;
    }

    public void setProximoVencimento(Date proximoVencimento) {
        this.proximoVencimento = proximoVencimento;
    }

    public Date getVencimentoUltimaParcela() {
        return vencimentoUltimaParcela;
    }

    public void setVencimentoUltimaParcela(Date vencimentoUltimaParcela) {
        this.vencimentoUltimaParcela = vencimentoUltimaParcela;
    }

    public boolean isUsaParcelasFixas() {
        return usaParcelasFixas;
    }

    public void setUsaParcelasFixas(boolean usaParcelasFixas) {
        this.usaParcelasFixas = usaParcelasFixas;
    }

    public int getParcelaInicial() {
        return parcelaInicial;
    }

    public void setParcelaInicial(int parcelaInicial) {
        this.parcelaInicial = parcelaInicial;
    }

    public int getParcelaFinal() {
        return parcelaFinal;
    }

    public void setParcelaFinal(int parcelaFinal) {
        this.parcelaFinal = parcelaFinal;
    }

    public int getQtdeDiasNovaGeracao() {
        return qtdeDiasNovaGeracao;
    }

    public void setQtdeDiasNovaGeracao(int qtdeDiasNovaGeracao) {
        this.qtdeDiasNovaGeracao = qtdeDiasNovaGeracao;
    }

    public int getQtdeParcelasGerar() {
        return qtdeParcelasGerar;
    }

    public void setQtdeParcelasGerar(int qtdeParcelasGerar) {
        this.qtdeParcelasGerar = qtdeParcelasGerar;
    }

    public int getDiaVencimento() {
        return diaVencimento;
    }

    public void setDiaVencimento(int diaVencimento) {
        this.diaVencimento = diaVencimento;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }
    
    public String getDataLancamento_Apresentar() {
    	if(dataLancamento == null){
    		return "";
    	}
        return Uteis.getData(getDataLancamento());
    }

    public Boolean getDataCalendarioPreenchida() {
        return dataCalendarioPreenchida;
    }

    public void setDataCalendarioPreenchida(Boolean dataCalendarioPreenchida) {
        this.dataCalendarioPreenchida = dataCalendarioPreenchida;
    }
    
    public void validandoDataCalendario(){
        setDataCalendarioPreenchida(vencimentoUltimaParcela != null);
    }

    public Boolean getGerarApenasDiasUteis() {
        return gerarApenasDiasUteis;
    }

    public void setGerarApenasDiasUteis(Boolean gerarApenasDiasUteis) {
        this.gerarApenasDiasUteis = gerarApenasDiasUteis;
    }
}

package negocio.comuns.financeiro;

import annotations.arquitetura.Lista;
import org.json.JSONObject;

import java.util.List;

public class BILtvGraficoChurnRateDTO {

    int anoEvasao;
    int competenciaEvasao;
    double churnRate = 0.00D;

    public BILtvGraficoChurnRateDTO() {
    }

    public BILtvGraficoChurnRateDTO(JSONObject jsonObject) {
        this.anoEvasao = jsonObject.getInt("anoEvasao");
        this.competenciaEvasao = jsonObject.getInt("competenciaEvasao");
        this.churnRate = jsonObject.getDouble("churnRate");
    }

    public int getAnoEvasao() {
        return anoEvasao;
    }

    public void setAnoEvasao(int anoEvasao) {
        this.anoEvasao = anoEvasao;
    }

    public int getCompetenciaEvasao() {
        return competenciaEvasao;
    }

    public void setCompetenciaEvasao(int competenciaEvasao) {
        this.competenciaEvasao = competenciaEvasao;
    }


    public double getChurnRate() {
        return churnRate;
    }

    public void setChurnRate(double churnRate) {
        this.churnRate = churnRate;
    }
}

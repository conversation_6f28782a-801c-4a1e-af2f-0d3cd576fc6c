package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoItemExtratoEnum;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.UteisValidacao;

public class TotalizadorConciliacaoDTO {

    private Double creditoBruto = 0.0;
    private Double creditoLiquido = 0.0;
    private Double debitoBruto = 0.0;
    private Double debitoLiquido = 0.0;
    private Double cancelamentos = 0.0;
    private Double cancelamentosLiquido = 0.0;
    private Double totalBruto = 0.0;
    private Double totalLiquido = 0.0;
    private Double totalZW = 0.0;
    private Double totalDebitoZW = 0.0;
    private Double totalCreditoZW = 0.0;

    private Integer qtdCredito = 0;
    private Integer qtdDebito = 0;
    private Integer qtdCancelamentos = 0;
    private Integer qtdTotal = 0;

    public void totalizar(ExtratoDiarioItemVO extratoItem){
        if(!UteisValidacao.emptyNumber(extratoItem.getCodigo())){
            if(extratoItem.getValorBruto() < 0.0){
                qtdCancelamentos += 1;
                cancelamentos += extratoItem.getValorBruto() == null ? 0.0 : extratoItem.getValorBruto();
                cancelamentosLiquido += extratoItem.getValorLiquido() == null ? 0.0 : extratoItem.getValorLiquido();
            } else if (extratoItem.getCredito()) {
                creditoBruto += extratoItem.getValorBruto() == null ? 0.0 : extratoItem.getValorBruto();
                creditoLiquido += extratoItem.getValorLiquido() == null ? 0.0 : extratoItem.getValorLiquido();
                qtdCredito += 1;
                qtdTotal += 1;
            } else {
                qtdDebito += 1;
                qtdTotal += 1;
                debitoBruto += extratoItem.getValorBruto() == null ? 0.0 : extratoItem.getValorBruto();
                debitoLiquido += extratoItem.getValorLiquido() == null ? 0.0 : extratoItem.getValorLiquido();
            }
        }

        if(extratoItem.getValorBruto() < 0.0){
            return;
        }

        totalBruto += extratoItem.getValorBruto() == null ? 0.0 : extratoItem.getValorBruto();
        totalLiquido += extratoItem.getValorLiquido() == null ? 0.0 : extratoItem.getValorLiquido();

        if (extratoItem.getMovPagamento() != null && extratoItem.getMovPagamento().getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla()) &&
                extratoItem.getCartao() != null && !UteisValidacao.emptyNumber(extratoItem.getCartao().getCodigo())) {
            totalCreditoZW += extratoItem.getCartao().getValor();
            totalZW += extratoItem.getCartao().getValor();
        } else if (extratoItem.getCartao() == null &&
                extratoItem.getTipoConciliacao() != null && extratoItem.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo()) &&
                extratoItem.getMovPagamento() != null && !UteisValidacao.emptyNumber(extratoItem.getMovPagamento().getCodigo()) &&
                extratoItem.getMovPagamento().getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
            totalCreditoZW += extratoItem.getMovPagamento().getValor();
            totalZW += extratoItem.getMovPagamento().getValor();
        } else if (extratoItem.getMovPagamento() != null && extratoItem.getMovPagamento().getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())) {
            totalDebitoZW += extratoItem.getMovPagamento().getValor();
            totalZW += extratoItem.getMovPagamento().getValor();
        }
    }

    public Double getTotalBruto() {
        return totalBruto;
    }

    public void setTotalBruto(Double totalBruto) {
        this.totalBruto = totalBruto;
    }

    public Double getTotalLiquido() {
        return totalLiquido;
    }

    public void setTotalLiquido(Double totalLiquido) {
        this.totalLiquido = totalLiquido;
    }

    public Double getTotalZW() {
        return totalZW;
    }

    public void setTotalZW(Double totalZW) {
        this.totalZW = totalZW;
    }

    public Double getTotalDebitoZW() {
        return totalDebitoZW;
    }

    public void setTotalDebitoZW(Double totalDebitoZW) {
        this.totalDebitoZW = totalDebitoZW;
    }

    public Double getTotalCreditoZW() {
        return totalCreditoZW;
    }

    public void setTotalCreditoZW(Double totalCreditoZW) {
        this.totalCreditoZW = totalCreditoZW;
    }

    public Double getCreditoBruto() {
        return creditoBruto;
    }

    public void setCreditoBruto(Double creditoBruto) {
        this.creditoBruto = creditoBruto;
    }

    public Double getCreditoLiquido() {
        return creditoLiquido;
    }

    public void setCreditoLiquido(Double creditoLiquido) {
        this.creditoLiquido = creditoLiquido;
    }

    public Double getDebitoBruto() {
        return debitoBruto;
    }

    public void setDebitoBruto(Double debitoBruto) {
        this.debitoBruto = debitoBruto;
    }

    public Double getDebitoLiquido() {
        return debitoLiquido;
    }

    public void setDebitoLiquido(Double debitoLiquido) {
        this.debitoLiquido = debitoLiquido;
    }

    public Double getCancelamentos() {
        return cancelamentos;
    }

    public void setCancelamentos(Double cancelamentos) {
        this.cancelamentos = cancelamentos;
    }

    public String getCreditoLiquidoApresentar() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(creditoLiquido);
    }

    public String getDebitoLiquidoApresentar() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(debitoLiquido);
    }

    public String getTotalLiquidoApresentar() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(totalLiquido);
    }

    public String getCancelamentoLiquidoApresentar() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(cancelamentosLiquido);
    }

    public Double getCancelamentosLiquido() {
        return cancelamentosLiquido;
    }

    public void setCancelamentosLiquido(Double cancelamentosLiquido) {
        this.cancelamentosLiquido = cancelamentosLiquido;
    }

    public Integer getQtdCredito() {
        return qtdCredito;
    }

    public void setQtdCredito(Integer qtdCredito) {
        this.qtdCredito = qtdCredito;
    }

    public Integer getQtdDebito() {
        return qtdDebito;
    }

    public void setQtdDebito(Integer qtdDebito) {
        this.qtdDebito = qtdDebito;
    }

    public Integer getQtdTotal() {
        return qtdTotal;
    }

    public void setQtdTotal(Integer qtdTotal) {
        this.qtdTotal = qtdTotal;
    }

    public Integer getQtdCancelamentos() {
        return qtdCancelamentos;
    }

    public void setQtdCancelamentos(Integer qtdCancelamentos) {
        this.qtdCancelamentos = qtdCancelamentos;
    }
}

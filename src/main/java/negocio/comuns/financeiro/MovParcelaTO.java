package negocio.comuns.financeiro;

import java.util.Date;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Calendario;

/**
 *
 * <AUTHOR>
 */
public class MovParcelaTO extends SuperTO {

    private static final long serialVersionUID = 5413770320400314635L;
    private int codigo = 0;
    private String descricao = "";
    private double valor = 0.0;
    private Date dataVencimento = Calendario.hoje();

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }
}

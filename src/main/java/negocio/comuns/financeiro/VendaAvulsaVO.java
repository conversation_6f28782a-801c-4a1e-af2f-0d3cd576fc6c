package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ClienteVO;
import java.util.Iterator;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;

import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.plano.PlanoTextoPadraoVO ;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.plano.enumerador.TipoDesconto;

/**
 * Reponsável por manter os dados da entidade VendaAvulsa. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class VendaAvulsaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String tipoComprador;
    protected String nomeComprador;
    private Double valorTotal;
    protected Date dataRegistro;
    protected MovParcelaVO parcela;
    protected PlanoTextoPadraoVO textoPadrao;
    @Lista
    /** Atributo responsável por manter os objetos da classe <code>ItemVendaAvulsa</code>. */
    private List<ItemVendaAvulsaVO> itemVendaAvulsaVOs;
    @NaoControlarLogAlteracao
    /** Atributo responsável por manter o objeto relacionado da classe <code>Cliente </code>.*/
    protected ClienteVO cliente;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Colaborador </code>.*/
    protected ColaboradorVO colaborador;
    protected UsuarioVO responsavel = new UsuarioVO();
    @ChaveEstrangeira
    protected EmpresaVO empresa = new EmpresaVO();
    @NaoControlarLogAlteracao
    protected Boolean apresentarCliente;
    @NaoControlarLogAlteracao
    protected Boolean apresentarColaborador;
    @NaoControlarLogAlteracao
    protected Boolean apresentarConsumidor;
    @NaoControlarLogAlteracao
    protected Boolean apresentarEmpresa;
    private String descricaoAdicional = "";
    private List<MovProdutoVO> movProdutoVOs;
    //flag usado no processo de recebimento de débito de conta corrente do cliente
    //evite usá-lo para outros processos pois interfere na tela de pagamento
    private Boolean produtoDebitoContaCorrente = false;

    @NaoControlarLogAlteracao
    private Integer nrVezesParcelamento = 1;
    @NaoControlarLogAlteracao
    private Date vencimentoPrimeiraParcela = Calendario.hoje();
    private List<MovParcelaVO> movParcelaVOs;
    private boolean apresentarParcelamento;
    private OrigemSistemaEnum origemSistema;
    @NaoControlarLogAlteracao
    private AulaAvulsaDiariaVO diariaVendaOnline = null;
    private PessoaVO pessoaVO;
    private EventoVO eventoVO;

    /**
     * Construtor padrão da classe <code>VendaAvulsa</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public VendaAvulsaVO() {
        super();
        inicializarDados();
    }

    public String getResponsavel_Apresentar() {
        return getUsuarioVO().getNome();
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>VendaAvulsaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(VendaAvulsaVO obj) throws ConsistirException,Exception {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getEmpresa() == null || obj.getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("O campo Empresa (Venda Avulsa) deve ser informado.");
        }
        if (obj.getTipoComprador().isEmpty()) {
            throw new ConsistirException("O campo TIPO DE COMPRADOR deve ser informado.");
        }
        if (obj.getTipoComprador().equals("CI") && (obj.getCliente().getCodigo() == 0 || obj.getNomeComprador().isEmpty())) {
            throw new ConsistirException("O campo NOME COMPRADOR (Venda Avulsa) deve ser informado. Selecione novamente um dos nomes da lista apresentada");
        }
        if (obj.getTipoComprador().equals("CO") && (obj.getColaborador().getCodigo() == 0 || obj.getNomeComprador().isEmpty())) {
            throw new ConsistirException("O campo NOME COMPRADOR (Venda Avulsa) deve ser informado. Selecione novamente um dos nomes da lista apresentada");
        }
        if (obj.getTipoComprador().equals("CN") && obj.getNomeComprador().equals("")) {
            throw new ConsistirException("O campo NOME COMPRADOR (Venda Avulsa) deve ser informado.");
        }
        if (obj.getItemVendaAvulsaVOs().isEmpty()) {
            throw new ConsistirException("Para realizar a venda é necessário consumir pelo menos um produto.");
        }

        if (UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
            if (!UteisValidacao.emptyNumber(obj.getCliente().getPessoa().getCodigo())){
                obj.setPessoaVO(obj.getCliente().getPessoa());
            } else if (!UteisValidacao.emptyNumber(obj.getColaborador().getPessoa().getCodigo())){
                obj.setPessoaVO(obj.getColaborador().getPessoa());
            }
        }

        Iterator i = obj.getItemVendaAvulsaVOs().iterator();
        while (i.hasNext()){
            ItemVendaAvulsaVO itemVendaAvulsaVO = (ItemVendaAvulsaVO) i.next();
            if (itemVendaAvulsaVO.getProduto().getTipoVigencia().equals("ID") && itemVendaAvulsaVO.getProduto().getNrDiasVigencia() == 0){

                throw new Exception("Produto "+ itemVendaAvulsaVO.getProduto().getDescricao() + " por ser um produto de Intervalo de dias, precisa ter o número de dias maior que 0.");
            }
        }
//        if (obj.getValorTotal().doubleValue() == 0) {
//            throw new ConsistirException("Venda Realizada com SUCESSO!");
//        }
    }
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
//        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
//            return;
//        }
        setTipoComprador(getTipoComprador().toUpperCase());
        setNomeComprador(getNomeComprador().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setTipoComprador("");
        setNomeComprador("");
        setValorTotal(0.0);
        setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        setItemVendaAvulsaVOs(new ArrayList<ItemVendaAvulsaVO>());
        setCliente(new ClienteVO());
        setColaborador(new ColaboradorVO());
        setResponsavel(new UsuarioVO());
        setEmpresa(new EmpresaVO());
        setApresentarColaborador(false);
        setApresentarCliente(false);
        setApresentarConsumidor(false);
        setApresentarEmpresa(false);
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>ItemVendaAvulsaVO</code>
     * ao List <code>itemVendaAvulsaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>ItemVendaAvulsa</code> - getProduto().getCodigo() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>ItemVendaAvulsaVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjItemVendaAvulsaVOs(ItemVendaAvulsaVO obj) throws Exception {
        ItemVendaAvulsaVO.validarDados(obj);
        int index = 0;
        Iterator i = getItemVendaAvulsaVOs().iterator();
        while (i.hasNext()) {
            ItemVendaAvulsaVO objExistente = (ItemVendaAvulsaVO) i.next();
            if (objExistente.getProduto().getCodigo().equals(obj.getProduto().getCodigo())) {
                if (obj.isSomarItemAdicionar() && !obj.isEdicaoVendaAvulsa()) {
                    objExistente.setQuantidade(objExistente.getQuantidade() + obj.getQuantidade());
                } else {
                    getItemVendaAvulsaVOs().set(index, obj);
                }
                return;
            }
            index++;
        }
        getItemVendaAvulsaVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>ItemVendaAvulsaVO</code>
     * no List <code>itemVendaAvulsaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>ItemVendaAvulsa</code> - getProduto().getCodigo() - como identificador (key) do objeto no List.
     * @param produto  Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjItemVendaAvulsaVOs(Integer produto) throws Exception {
        int index = 0;
        Iterator i = getItemVendaAvulsaVOs().iterator();
        while (i.hasNext()) {
            ItemVendaAvulsaVO objExistente = (ItemVendaAvulsaVO) i.next();
            if (objExistente.getProduto().getCodigo().equals(produto)) {
                getItemVendaAvulsaVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public void parcelaPaga(VendaAvulsaVO obj, MovParcelaVO movParcela) throws Exception {
        Iterator i = obj.getItemVendaAvulsaVOs().iterator();
        while (i.hasNext()) {
            ItemVendaAvulsaVO itemVenda = (ItemVendaAvulsaVO) i.next();
            inicializarMovProdutoPago(itemVenda, obj.getResponsavel(), obj, movParcela);
        }
    }

    public void parcela(VendaAvulsaVO obj, MovParcelaVO movParcela) throws Exception {
        Iterator i = obj.getItemVendaAvulsaVOs().iterator();
        while (i.hasNext()) {
            ItemVendaAvulsaVO itemVenda = (ItemVendaAvulsaVO) i.next();
            inicializarMovProduto(itemVenda, obj.getResponsavel(), obj, movParcela);
        }
    }

    public void inicializarMovProduto(ItemVendaAvulsaVO itemVenda, UsuarioVO usuario, VendaAvulsaVO obj, MovParcelaVO movParcela) throws Exception {
        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setProduto(itemVenda.getProduto());
        movProdutoVO.setRenovavelAutomaticamente(itemVenda.getProduto().getRenovavelAutomaticamente());
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao(itemVenda.getProduto().getDescricao());
        movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(negocio.comuns.utilitarias.Calendario.hoje()));
        movProdutoVO.setQuantidade(itemVenda.getQuantidade());
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(negocio.comuns.utilitarias.Calendario.hoje()));
        movProdutoVO.setDataInicioVigencia(itemVenda.getDataVenda());
        movProdutoVO.setDataFinalVigencia(itemVenda.getDataValidade());
        movProdutoVO.setDataLancamento(obj.getDataRegistro());
        movProdutoVO.setResponsavelLancamento(usuario);
        movProdutoVO.setMovpagamentocc(itemVenda.getMovpagamentos());
        if (itemVenda.getTabelaDesconto().getValor() != 0.0 && itemVenda.getTabelaDesconto().getTipoDesconto().equals(TipoDesconto.PE)) {
            movProdutoVO.setValorDesconto(Uteis.arredondarForcando2CasasDecimais((itemVenda.getProduto().getValorFinal()* movProdutoVO.getQuantidade())  - itemVenda.getValorParcial()));
        } else if (itemVenda.getTabelaDesconto().getValor() != 0.0 && itemVenda.getTabelaDesconto().getTipoDesconto().equals(TipoDesconto.VA)) {
            movProdutoVO.setValorDesconto(itemVenda.getTabelaDesconto().getValor());
        } else if (itemVenda.getDescontoManual()) {
            movProdutoVO.setValorDesconto(itemVenda.getValorDescontoManual());
        } else {
            movProdutoVO.setValorDesconto(0.0);
        }
        movProdutoVO.setPrecoUnitario(itemVenda.getProduto().getValorFinal());
        movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais((movProdutoVO.getPrecoUnitario() * movProdutoVO.getQuantidade()) - (movProdutoVO.getValorDesconto())));
        movProdutoVO.setEmpresa(obj.getEmpresa());
        if (obj.getTipoComprador().equals("CI")) {
            movProdutoVO.setPessoa(obj.getCliente().getPessoa());
        }
        if (obj.getTipoComprador().equals("CO")) {
            movProdutoVO.setPessoa(obj.getColaborador().getPessoa());
        }
        movProdutoVO.setQuitado(false);
        movProdutoVO.setSituacao("EA");
        getFacade().getMovProduto().incluirSemCommit(movProdutoVO);
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProduto(movProdutoVO.getCodigo());
        movProdutoParcela.setMovProdutoVO(movProdutoVO);
        movProdutoParcela.setValorPago(movProdutoVO.getTotalFinal());
        movParcela.getMovProdutoParcelaVOs().add(movProdutoParcela);
    }

    public void inicializarMovProdutoPago(ItemVendaAvulsaVO itemVenda, UsuarioVO usuario, VendaAvulsaVO obj, MovParcelaVO movParcela) throws Exception {
        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setProduto(itemVenda.getProduto());
        movProdutoVO.setRenovavelAutomaticamente(itemVenda.getProduto().getRenovavelAutomaticamente());
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao(!obj.getDescricaoAdicional().isEmpty()
                ? obj.getDescricaoAdicional() : itemVenda.getProduto().getDescricao());
        movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(obj.getDataRegistro()));
        movProdutoVO.setQuantidade(itemVenda.getQuantidade());
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(obj.getDataRegistro()));
        movProdutoVO.setDataInicioVigencia(itemVenda.getDataVenda());
        movProdutoVO.setDataFinalVigencia(itemVenda.getDataValidade());
        movProdutoVO.setDataLancamento(obj.getDataRegistro());
        movProdutoVO.setResponsavelLancamento(usuario);
        if (itemVenda.getTabelaDesconto().getValor() != 0.0 && itemVenda.getTabelaDesconto().getTipoDesconto().equals(TipoDesconto.PE)) {
            movProdutoVO.setValorDesconto(Uteis.arredondarForcando2CasasDecimais(itemVenda.getProduto().getValorFinal() * itemVenda.getQuantidade() * (itemVenda.getTabelaDesconto().getValor() / 100)));
        } else if (itemVenda.getTabelaDesconto().getValor() != 0.0 && itemVenda.getTabelaDesconto().getTipoDesconto().equals(TipoDesconto.VA)) {
            movProdutoVO.setValorDesconto(itemVenda.getTabelaDesconto().getValor());
        } else if (itemVenda.getDescontoManual()) {
            movProdutoVO.setValorDesconto(itemVenda.getValorDescontoManual());
        } else {
            movProdutoVO.setValorDesconto(0.0);
        }
        movProdutoVO.setPrecoUnitario(itemVenda.getProduto().getValorFinal());
        movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais((movProdutoVO.getPrecoUnitario() * movProdutoVO.getQuantidade())
                - movProdutoVO.getValorDesconto()));
        movProdutoVO.setEmpresa(obj.getEmpresa());
        if (obj.getTipoComprador().equals("CI")) {
            movProdutoVO.setPessoa(obj.getCliente().getPessoa());
        }
        if (obj.getTipoComprador().equals("CO")) {
            movProdutoVO.setPessoa(obj.getColaborador().getPessoa());
        }
        movProdutoVO.setQuitado(true);
        movProdutoVO.setSituacao("PG");
        getFacade().getMovProduto().incluirSemCommit(movProdutoVO);
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProduto(movProdutoVO.getCodigo());
        movProdutoParcela.setMovProdutoVO(movProdutoVO);
        movProdutoParcela.setValorPago(movProdutoVO.getTotalFinal());
        movParcela.getMovProdutoParcelaVOs().add(movProdutoParcela);
    }

    public Double getValorTotal() {
        if (valorTotal == null) {
            valorTotal = 0.0;
        }
        valorTotal = 0.0;
        if (!getItemVendaAvulsaVOs().isEmpty()) {

            Iterator i = getItemVendaAvulsaVOs().iterator();
            while (i.hasNext()) {
                ItemVendaAvulsaVO obj = (ItemVendaAvulsaVO) i.next();
                valorTotal = valorTotal + obj.getValorParcial();
            }
        }
        return (valorTotal);
    }

    public String getValor_Apresentar() {
        return Formatador.formatarValorMonetario(valorTotal);
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getNomeComprador() {
        if (nomeComprador == null) {
            nomeComprador = "";
        }
        return (nomeComprador);
    }

    public void setNomeComprador(String nomeComprador) {
        this.nomeComprador = nomeComprador;
    }

    public String getTipoComprador() {
        if (tipoComprador == null) {
            tipoComprador = "";
        }
        return (tipoComprador);
    }

    public String getTipoComprador_Apresentar() {
        if (tipoComprador == null) {
            return tipoComprador = "";
        }
        if (tipoComprador.equals("CN")) {
            return "Consumidor";
        }
        if (tipoComprador.equals("CO")) {
            return "Colaborador";
        }
        if (tipoComprador.equals("CI")) {
            return "Cliente";
        }
        return "";
    }

    public void apresentarComprador() {
        if (tipoComprador == null) {
            tipoComprador = "";
        }
        if (tipoComprador.equals("CN")) {
            setApresentarConsumidor(true);
            setApresentarCliente(false);
            setApresentarColaborador(false);
            setCliente(new ClienteVO());
            setColaborador(new ColaboradorVO());
            setNomeComprador("");
            setApresentarParcelamento(false);
            setNrVezesParcelamento(1);
            setVencimentoPrimeiraParcela(Calendario.hoje());
        }
        if (tipoComprador.equals("CO")) {
            setApresentarColaborador(true);
            setApresentarConsumidor(false);
            setApresentarCliente(false);
            setCliente(new ClienteVO());
            setNomeComprador("");
            setApresentarParcelamento(true);
            setNrVezesParcelamento(1);
            setVencimentoPrimeiraParcela(Calendario.hoje());
        }
        if (tipoComprador.equals("CI")) {
            setApresentarCliente(true);
            setApresentarColaborador(false);
            setApresentarConsumidor(false);
            setNomeComprador("");
            setColaborador(new ColaboradorVO());
            setApresentarParcelamento(true);
            setNrVezesParcelamento(1);
            setVencimentoPrimeiraParcela(Calendario.hoje());
        }
    }

    public Double obterValorParcelas(boolean primeira) {
        Double valorparcela = Uteis.arredondarForcando2CasasDecimais((getValorTotal()) / this.getNrVezesParcelamento());
        if(primeira && (!UteisValidacao.emptyNumber(this.getNrVezesParcelamento()) && this.getNrVezesParcelamento() > 1)){
            valorparcela  =Uteis.arredondarForcando2CasasDecimais(valorparcela + (valorTotal - (valorparcela *this.getNrVezesParcelamento())));
        }
        return  valorparcela;
    }

    public ColaboradorVO getColaborador() {
        if (colaborador == null) {
            colaborador = new ColaboradorVO();
        }
        return (colaborador);
    }

    public void setColaborador(ColaboradorVO obj) {
        this.colaborador = obj;
    }

    public ClienteVO getCliente() {
        if (cliente == null) {
            cliente = new ClienteVO();
        }
        return (cliente);
    }

    public void setCliente(ClienteVO obj) {
        this.cliente = obj;
    }

    public Integer getItemVendaAvulsaVOsSize() {
        return getItemVendaAvulsaVOs().size();
    }

    /** Retorna Atributo responsável por manter os objetos da classe <code>ItemVendaAvulsa</code>. */
    public List<ItemVendaAvulsaVO> getItemVendaAvulsaVOs() {
        if (itemVendaAvulsaVOs == null) {
            itemVendaAvulsaVOs = new ArrayList<ItemVendaAvulsaVO>();
        }
        return itemVendaAvulsaVOs;
    }

    /** Define Atributo responsável por manter os objetos da classe <code>ItemVendaAvulsa</code>. */
    public void setItemVendaAvulsaVOs(List<ItemVendaAvulsaVO> itemVendaAvulsaVOs) {
        this.itemVendaAvulsaVOs = itemVendaAvulsaVOs;
    }

    public void setTipoComprador(String tipoComprador) {
        this.tipoComprador = tipoComprador;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getApresentarCliente() {
        return apresentarCliente;
    }

    public void setApresentarCliente(Boolean apresentarCliente) {
        this.apresentarCliente = apresentarCliente;
    }

    public Boolean getApresentarColaborador() {
        return apresentarColaborador;
    }

    public void setApresentarColaborador(Boolean apresentarColaborador) {
        this.apresentarColaborador = apresentarColaborador;
    }

    public Boolean getApresentarConsumidor() {
        return apresentarConsumidor;
    }

    public void setApresentarConsumidor(Boolean apresentarConsumidor) {
        this.apresentarConsumidor = apresentarConsumidor;
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Boolean getApresentarEmpresa() {
        return apresentarEmpresa;
    }

    public void setApresentarEmpresa(Boolean apresentarEmpresa) {
        this.apresentarEmpresa = apresentarEmpresa;
    }

    public String getDataRegistro_Apresentar() {
        return Uteis.getData(dataRegistro);
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public void setDescricaoAdicional(String descricaoAdicional) {
        this.descricaoAdicional = descricaoAdicional;
    }

    public String getDescricaoAdicional() {
        return descricaoAdicional;
    }

    public Boolean getProdutoDebitoContaCorrente() {
        return produtoDebitoContaCorrente;
    }

    public void setProdutoDebitoContaCorrente(Boolean produtoDebitoContaCorrente) {
        this.produtoDebitoContaCorrente = produtoDebitoContaCorrente;
    }

    public MovParcelaVO getParcela() {
        return parcela;
    }

    public void setParcela(MovParcelaVO parcela) {
        this.parcela = parcela;
    }

    public PlanoTextoPadraoVO getTextoPadrao() {
        return textoPadrao;
    }

    public void setTextoPadrao(PlanoTextoPadraoVO textoPadrao) {
        this.textoPadrao = textoPadrao;
    }

    public List<MovProdutoVO> getMovProdutoVOs() {
        if (movProdutoVOs == null) {
            movProdutoVOs = new ArrayList<MovProdutoVO>();
        }
        return movProdutoVOs;
    }

    public void setMovProdutoVOs(List<MovProdutoVO> movProdutoVOs) {
        this.movProdutoVOs = movProdutoVOs;
    }

    public Integer getNrVezesParcelamento() {
        return nrVezesParcelamento;
    }

    public void setNrVezesParcelamento(Integer nrVezesParcelamento) {
        this.nrVezesParcelamento = nrVezesParcelamento;
    }

    public Date getVencimentoPrimeiraParcela() {
        return vencimentoPrimeiraParcela;
    }

    public void setVencimentoPrimeiraParcela(Date vencimentoPrimeiraParcela) {
        this.vencimentoPrimeiraParcela = vencimentoPrimeiraParcela;
    }

    public List<MovParcelaVO> getMovParcelaVOs() {
        if (movParcelaVOs == null) {
            movParcelaVOs = new ArrayList<MovParcelaVO>();
        }
        return movParcelaVOs;
    }

    public void setMovParcelaVOs(List<MovParcelaVO> movParcelaVOs) {
        this.movParcelaVOs = movParcelaVOs;
    }

    public void setApresentarParcelamento(boolean apresentarParcelamento) {
        this.apresentarParcelamento = apresentarParcelamento;
    }

    public boolean isApresentarParcelamento() {
        return apresentarParcelamento;
    }

    public OrigemSistemaEnum getOrigemSistema() {
        if (origemSistema == null) {
            origemSistema = OrigemSistemaEnum.ZW;
        }
        return origemSistema;
    }

    public void setOrigemSistema(OrigemSistemaEnum origemSistema) {
        this.origemSistema = origemSistema;
    }

    public AulaAvulsaDiariaVO getDiariaVendaOnline() {
        return diariaVendaOnline;
    }

    public void setDiariaVendaOnline(AulaAvulsaDiariaVO diariaVendaOnline) {
        this.diariaVendaOnline = diariaVendaOnline;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public EventoVO getEventoVO() {
        if (eventoVO == null){
            eventoVO = new EventoVO();
        }
        return eventoVO;
    }

    public void setEventoVO(EventoVO eventoVO) {
        this.eventoVO = eventoVO;
    }
}

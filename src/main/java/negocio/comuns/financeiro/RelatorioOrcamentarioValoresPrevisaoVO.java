
package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;

public class RelatorioOrcamentarioValoresPrevisaoVO extends SuperVO {
    @ChavePrimaria
    private Integer codigo = 0;
    private double valor = 0.0;
    private PlanoContaTO planoContaTO;
    private int relatorioOrcamentarioConfigVO = 0;

    public RelatorioOrcamentarioValoresPrevisaoVO(){
    }

    public boolean isEmpty() {
        return valor == 0.0;
    }

    public String getValor_Apresentar(){
    	return Formatador.formatarValorMonetarioSemMoeda(this.valor);
	}
    
    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public PlanoContaTO getPlanoContaTO() {
        return planoContaTO;
    }

    public void setPlanoContaTO(PlanoContaTO planoContaTO) {
        this.planoContaTO = planoContaTO;
    }

    public int getRelatorioOrcamentarioConfigVO() {
        return relatorioOrcamentarioConfigVO;
    }

    public void setRelatorioOrcamentarioConfigVO(int relatorioOrcamentarioConfigVO) {
        this.relatorioOrcamentarioConfigVO = relatorioOrcamentarioConfigVO;
    }
}

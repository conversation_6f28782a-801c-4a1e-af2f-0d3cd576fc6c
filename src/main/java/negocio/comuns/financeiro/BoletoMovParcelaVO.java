/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 07/12/2021
 */
public class BoletoMovParcelaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private BoletoVO boletoVO;
    @ChaveEstrangeira
    private MovParcelaVO movParcelaVO;
    private Integer nrTentativaParcela;
    private Double valorParcela;
    private Double valorMulta;
    private Double valorJuros;
    private String jsonEstorno;

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public BoletoVO getBoletoVO() {
        if (boletoVO == null) {
            boletoVO = new BoletoVO();
        }
        return boletoVO;
    }

    public void setBoletoVO(BoletoVO boletoVO) {
        this.boletoVO = boletoVO;
    }

    public MovParcelaVO getMovParcelaVO() {
        if (movParcelaVO == null) {
            movParcelaVO = new MovParcelaVO();
        }
        return movParcelaVO;
    }

    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }

    public Integer getNrTentativaParcela() {
        if (nrTentativaParcela == null) {
            nrTentativaParcela = 0;
        }
        return nrTentativaParcela;
    }

    public void setNrTentativaParcela(Integer nrTentativaParcela) {
        this.nrTentativaParcela = nrTentativaParcela;
    }

    public String getValorTotalApresentar() {
        return Formatador.formatarValorMonetario(getValorTotal());
    }

    public Double getValorTotal() {
        return getValorParcela() + getValorMulta() + getValorJuros();
    }

    public String getValorParcelaApresentar() {
        return Formatador.formatarValorMonetario(getValorParcela());
    }

    public Double getValorParcela() {
        if (valorParcela == null) {
            valorParcela = 0.0;
        }
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public String getValorMultaApresentar() {
        return Formatador.formatarValorMonetario(getValorMulta());
    }

    public Double getValorMulta() {
        if (valorMulta == null) {
            valorMulta = 0.0;
        }
        return valorMulta;
    }

    public void setValorMulta(Double valorMulta) {
        this.valorMulta = valorMulta;
    }

    public String getValorJurosApresentar() {
        return Formatador.formatarValorMonetario(getValorJuros());
    }

    public Double getValorJuros() {
        if (valorJuros == null) {
            valorJuros = 0.0;
        }
        return valorJuros;
    }

    public void setValorJuros(Double valorJuros) {
        this.valorJuros = valorJuros;
    }

    public String getJsonEstorno() {
        if (jsonEstorno == null) {
            jsonEstorno = "";
        }
        return jsonEstorno;
    }

    public void setJsonEstorno(String jsonEstorno) {
        this.jsonEstorno = jsonEstorno;
    }
}

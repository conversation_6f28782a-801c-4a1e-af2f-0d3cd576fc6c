package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade CartaoCredito. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class CartaoCreditoVO extends SuperVO {

    private Integer codigo;
    private MovPagamentoVO movpagamento;
    private Date dataCompensacao;
    private Double valor;
    private Double valorTotal;
    private String valorFormatado;
    private String valorTotalFormatado;
    private Date dataOriginal;
    private LoteVO loteVO;
    private String situacao;
    private String Composicao;
    private String nrDocumento;
    private String composicaoNova;
    private OperadoraCartaoVO operadora;
    @NaoControlarLogAlteracao
    private boolean cartaoEscolhido;
    @NaoControlarLogAlteracao
    private boolean cartaoCompensar;
    @NaoControlarLogAlteracao
    private boolean pagaOutroContrato; 
    @NaoControlarLogAlteracao
    private Boolean apresentarCartao;
    private MovContaVO movConta = new MovContaVO();
    private String nomeNoCartao = "";
    private String obterTodosCartoesComposicao;
    private Boolean cupomEmitido = false;
    private Boolean nfseEmitido = false;
    private boolean nfceEmitido = false;
    private String produtosPagos;
    private String avisoVinculos;
    private NFSeEmitidaVO nfSeEmitidaCartaoExcluido;
    private Integer nrParcela = 0;
    @NaoControlarLogAlteracao
    private boolean devolverParcial = false;
    @NaoControlarLogAlteracao
    private Double valorParcialDevolver = 0.0;
    @NaoControlarLogAlteracao
    private Double valorParcialDevolverCalculo = 0.0;
    private boolean alterouDataRecebimentoZWAutomaticamente;
    private Date dataPgtoOriginalZWAntesDaAlteracaoAutomatica;


    /**
     * Construtor padrão da classe <code>CartaoCredito</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public CartaoCreditoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da classe <code>CartaoCreditoVO</code>.
     */
    public static void validarUnicidade(List<CartaoCreditoVO> lista, CartaoCreditoVO obj) throws ConsistirException {
        for (CartaoCreditoVO repetido : lista) {
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>CartaoCreditoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(CartaoCreditoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(null);
        setDataCompensacao(negocio.comuns.utilitarias.Calendario.hoje());
        setValor(0.0);
        setDataOriginal(null);
        setOperadora(new OperadoraCartaoVO());
        setCartaoCompensar(false);
        setCartaoEscolhido(false);
        setApresentarCartao(false);
        setPagaOutroContrato(false);
        setSituacao("EA");
        setValorTotal(0.0);
        setComposicao("");
        setComposicaoNova("");
        setProdutosPagos("");
        setNrParcela(nrParcela);
    }

    public Double getValor() {
        if (valor == null) {
            valor = new Double(0);
        }
        return (valor);
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public void setValorTotal(Double valorTotal) {
		this.valorTotal = valorTotal;
	}

	public Double getValorTotal() {
		return valorTotal;
	}

	public String getDataCompensacao_Apresentar() {
        if (dataCompensacao == null) {
            return "";
        }
        return (Uteis.getDataComHora(dataCompensacao));
    }

    public Date getDataCompensacao() {
        if (dataCompensacao == null) {
            dataCompensacao = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return (dataCompensacao);
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public MovPagamentoVO getMovpagamento() {
        if (movpagamento == null) {
            movpagamento = new MovPagamentoVO();
        }
        return (movpagamento);
    }

    public void setMovpagamento(MovPagamentoVO movpagamento) {
        this.movpagamento = movpagamento;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataOriginal() {
        return dataOriginal;
    }

    public void setDataOriginal(Date dataOriginal) {
        this.dataOriginal = dataOriginal;
    }

    public LoteVO getLote() {
        return loteVO;
    }

    public void setLote(LoteVO lote) {
        this.loteVO = lote;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof CartaoCreditoVO) {
            CartaoCreditoVO aux = (CartaoCreditoVO) obj;
            return this.codigo.intValue() == aux.getCodigo().intValue();
        }
        return false;
    }

    public OperadoraCartaoVO getOperadora() {
        return operadora;
    }

    public void setOperadora(OperadoraCartaoVO operadora) {
        this.operadora = operadora;
    }

    public boolean isCartaoEscolhido() {
        return cartaoEscolhido;
    }

    public void setCartaoEscolhido(boolean cartaoEscolhido) {
        this.cartaoEscolhido = cartaoEscolhido;
    }

    public boolean isCartaoCompensar() {
        return cartaoCompensar;
    }

    public void setCartaoCompensar(boolean cartaoCompensar) {
        this.cartaoCompensar = cartaoCompensar;
    }

    public Boolean getApresentarCartao() {
        return apresentarCartao;
    }

    public void setApresentarCartao(Boolean apresentarCartao) {
        this.apresentarCartao = apresentarCartao;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

	public void setMovConta(MovContaVO movConta) {
		this.movConta = movConta;
	}

	public MovContaVO getMovConta() {
		return movConta;
	}

	public void setComposicao(String composicao) {
		Composicao = composicao;
	}

	public String getComposicao() {
		return Composicao;
	}

	public void setNrDocumento(String nrDocumento) {
		this.nrDocumento = nrDocumento;
	}

	public String getNrDocumento() {
		if(nrDocumento == null){
			nrDocumento = "";
		}
		return nrDocumento;
	}

	public void setNomeNoCartao(String nomeNoCartao) {
		this.nomeNoCartao = nomeNoCartao;
	}

	public String getNomeNoCartao() {
		return nomeNoCartao;
	}
	
	/**
	 * @param obterTodosChequesComposicao the obterTodosChequesComposicao to set
	 */
	public void setObterTodosCartoesComposicao(
			String obterTodosChequesComposicao) {
		this.obterTodosCartoesComposicao = obterTodosChequesComposicao;
	}

	/**
	 * @return the obterTodosChequesComposicao
	 */
	public String getObterTodosCartoesComposicao() {
		this.obterTodosCartoesComposicao = codigo.toString();
		if(getComposicao() != null && !getComposicao().equals("") ){
			obterTodosCartoesComposicao += ","+Composicao;
		}
		return obterTodosCartoesComposicao;
	}
	
    public boolean getTemLote() {
        return getLote().getCodigo() > 0;
    }

	public void setCupomEmitido(Boolean cupomEmitido) {
		this.cupomEmitido = cupomEmitido;
	}

	public Boolean getCupomEmitido() {
		return cupomEmitido;
	}

	public void setPagaOutroContrato(boolean pagaOutroContrato) {
		this.pagaOutroContrato = pagaOutroContrato;
	}

	public boolean isPagaOutroContrato() {
		return pagaOutroContrato;
	}

        public CartaoCreditoTO toTO(){
            return new CartaoCreditoTO(this.getCodigo(), this.getComposicao(),
                                                    this.getDataCompensacao(), this.getMovpagamento().getDataLancamento(),
                                                    this.getValorTotal(),this.getMovpagamento().getNomePagador(),
                                                    this.getMovpagamento().getAutorizacaoCartao(),
                                                    this.getOperadora().getDescricao());
        }

        public Boolean getTemComposicao(){
		return !UteisValidacao.emptyString(Composicao);
	}

     public String getProdutosPagos() {
        return produtosPagos;
    }

    public void setProdutosPagos(String produtosPagos) {
        this.produtosPagos = produtosPagos;
    }

    public Boolean getNfseEmitido() {
        return nfseEmitido;
    }

    public void setNfseEmitido(Boolean nfseEmitido) {
        this.nfseEmitido = nfseEmitido;
    }

    public String getComposicaoNova() {
        return composicaoNova;
    }

    public void setComposicaoNova(String composicaoNova) {
        this.composicaoNova = composicaoNova;
    }

    public String getAvisoVinculos() {
        if(avisoVinculos == null){
            return "";
        }
        return avisoVinculos;
    }

    public void setAvisoVinculos(String avisoVinculos) {
        this.avisoVinculos = avisoVinculos;
    }

    public NFSeEmitidaVO getNfSeEmitidaCartaoExcluido() {
        return nfSeEmitidaCartaoExcluido;
    }

    public void setNfSeEmitidaCartaoExcluido(NFSeEmitidaVO nfSeEmitidaCartaoExcluido) {
        this.nfSeEmitidaCartaoExcluido = nfSeEmitidaCartaoExcluido;
    }
    
    public boolean codigoPertenceAComposicao(Integer codigoCartao) {
        if(this.codigo !=  null && codigo.equals(codigoCartao)){
            return true;
        }
        if(this.Composicao == null || this.Composicao == ""){
             return false;
        }
        if((this.Composicao.contains(","+codigoCartao.toString()+",") || this.Composicao.startsWith(codigoCartao.toString()+",") || this.Composicao.endsWith(","+codigoCartao.toString()) || this.Composicao.equals(codigoCartao.toString()))){
            return true;
        }
        return false;
    }

    public Integer getNrParcela() {
        return nrParcela;
    }

    public void setNrParcela(Integer nrParcela) {
        this.nrParcela = nrParcela;
    }

    public boolean isDevolverParcial() {
        return devolverParcial;
    }

    public void setDevolverParcial(boolean devolverParcial) {
        this.devolverParcial = devolverParcial;
    }

    public Double getValorParcialDevolver() {
        return valorParcialDevolver;
    }

    public void setValorParcialDevolver(Double valorParcialDevolver) {
        this.valorParcialDevolver = valorParcialDevolver;
    }

    public Double getValorParcialDevolverCalculo() {
        return valorParcialDevolverCalculo;
    }

    public void setValorParcialDevolverCalculo(Double valorParcialDevolverCalculo) {
        this.valorParcialDevolverCalculo = valorParcialDevolverCalculo;
    }
    
    public Double getValorFinalParcialCalculo() {
        return this.valor - this.valorParcialDevolverCalculo;
    }
    
    public Double getValorFinalParcial() {
        return this.valor - this.valorParcialDevolver;
    }

    public boolean isNfceEmitido() {
        return nfceEmitido;
    }

    public void setNfceEmitido(boolean nfceEmitido) {
        this.nfceEmitido = nfceEmitido;
    }

    public String getValorFormatado() {
	   valorFormatado = Formatador.formatarMoeda(valor);
        return valorFormatado;
    }

    public String getValorTotalFormatado() {
	    valorTotalFormatado = Formatador.formatarMoeda(valorTotal);
        return valorTotalFormatado;
    }

    public List<ProdutoPago> getProdutoPagos(){
        List<ProdutoPago> pagos = new ArrayList<ProdutoPago>();
        if(null != getProdutosPagos()){
            String[] pps = getProdutosPagos().split("\\|");
            for (String pp : pps) {
                if(!pp.isEmpty()) {
                    String[] it = pp.split("\\,");

                    ProdutoPago p = new ProdutoPago();
                    p.setProduto(Integer.parseInt(it[0]));
                    p.setTipoProduto(TipoProduto.getTipoProdutoCodigo(it[1]));
                    p.setContrato(Integer.parseInt(it[2]));
                    p.setValor(Double.parseDouble(it[3]));

                    pagos.add(p);
                }
            }
        }

        return pagos;
    }

    public boolean isAlterouDataRecebimentoZWAutomaticamente() {
        return alterouDataRecebimentoZWAutomaticamente;
    }

    public void setAlterouDataRecebimentoZWAutomaticamente(boolean alterouDataRecebimentoZWAutomaticamente) {
        this.alterouDataRecebimentoZWAutomaticamente = alterouDataRecebimentoZWAutomaticamente;
    }

    public Date getDataPgtoOriginalZWAntesDaAlteracaoAutomatica() {
        return dataPgtoOriginalZWAntesDaAlteracaoAutomatica;
    }

    public void setDataPgtoOriginalZWAntesDaAlteracaoAutomatica(Date dataPgtoOriginalZWAntesDaAlteracaoAutomatica) {
        this.dataPgtoOriginalZWAntesDaAlteracaoAutomatica = dataPgtoOriginalZWAntesDaAlteracaoAutomatica;
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;


/**
 *
 * <AUTHOR>
 */
public class FreePassVO extends SuperVO{

    protected ClienteVO clienteVO;
    protected ProdutoVO produtoFreePass;
    protected EmpresaVO empresaVO;
    protected UsuarioVO usuarioResponsavel;
    protected String clienteFreePass;
    protected Boolean apresentarEmpresa;
    protected Boolean apresentarCliente;
   

    public FreePassVO() {
        super();
        inicialiarDados();
    }

    public void inicialiarDados(){
        setClienteVO(new ClienteVO());
        setProdutoFreePass(new ProdutoVO());
        setEmpresaVO(new EmpresaVO());
        setUsuarioResponsavel(new UsuarioVO());
        setApresentarEmpresa(false);
        setApresentarCliente(true);
    }

    public static void validarDados(FreePassVO obj)throws ConsistirException{
         if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if(obj.getClienteVO() == null || obj.getClienteVO().getCodigo().intValue()==0){
            throw new ConsistirException("O CLIENTE deve ser informado.");
        }
        if(obj.getProdutoFreePass()==null || obj.getProdutoFreePass().getCodigo().intValue()==0){
            throw new ConsistirException("O PRODUTO FREEPASS deve ser informado.");
        }
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public ProdutoVO getProdutoFreePass() {
        return produtoFreePass;
    }

    public void setProdutoFreePass(ProdutoVO produtoFreePass) {
        this.produtoFreePass = produtoFreePass;
    }

    public String getClienteFreePass() {
        return clienteFreePass;
    }

    public void setClienteFreePass(String clienteFreePass) {
        this.clienteFreePass = clienteFreePass;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public Boolean getApresentarEmpresa() {
        return apresentarEmpresa;
    }

    public void setApresentarEmpresa(Boolean apresentarEmpresa) {
        this.apresentarEmpresa = apresentarEmpresa;
    }

    public Boolean getApresentarCliente() {
        return apresentarCliente;
    }

    public void setApresentarCliente(Boolean apresentarCliente) {
        this.apresentarCliente = apresentarCliente;
    }

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

}

package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.time.format.DateTimeFormatter;
import java.util.Date;

public class HistoricoImportacaoContaVO {

	private Integer codigo;

	private Date dataImportacao;
	private String usuario;
	private String origemArquivo;
	private Integer totalImportados;
	private Integer totalErros;
	private String mensagemResultado="";
	private boolean error;
	private String mensagemError;


	public HistoricoImportacaoContaVO() {
		this.mensagemResultado="";
		this.dataImportacao = new Date();
	}



	public Integer getCodigo() {
		return codigo;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}


	public Date getDataImportacao() {
		return dataImportacao;
	}

	public void setDataImportacao(Date dataImportacao) {
		this.dataImportacao = dataImportacao;
	}

	public String getUsuario() {
		return usuario;
	}

	public void setUsuario(String usuario) {
		this.usuario = usuario;
	}

	public String getOrigemArquivo() {
		return origemArquivo;
	}

	public void setOrigemArquivo(String origemArquivo) {
		this.origemArquivo = origemArquivo;
	}

	public Integer getTotalImportados() {
		return totalImportados;
	}

	public void setTotalImportados(Integer totalImportados) {
		this.totalImportados = totalImportados;
	}

	public Integer getTotalErros() {
		return totalErros;
	}

	public void setTotalErros(Integer totalErros) {
		this.totalErros = totalErros;
	}

	public String getMensagemResultado() {
		return mensagemResultado;
	}

	public void setMensagemResultado(String mensagemResultado) {
		this.mensagemResultado = mensagemResultado;
	}

	public boolean isError() {
		return error;
	}

	public void setError(boolean error) {
		this.error = error;
	}

	public String getMensagemError() {
		return mensagemError;
	}

	public void setMensagemError(String mensagemError) {
		this.mensagemError = mensagemError;
	}

	public void registrarException(Exception e) {
		setError(true);
		setMensagemError(e.getMessage());
		if(UteisValidacao.emptyString(getMensagemResultado())){
			setMensagemResultado( "Error, "+e.getMessage());
		}else{
			setMensagemResultado( getMensagemResultado() +" Error : "+e.getMessage());
		}
	}

	public void registrarSucesso() {
		setMensagemResultado("Importação concluida com sucesso!");
	}

	public void registrarMensagem(String msg) {
		if(UteisValidacao.emptyString(getMensagemResultado())){
			setMensagemResultado( msg);
		}else{
			setMensagemResultado( getMensagemResultado() +" "+msg );
		}
	}
}
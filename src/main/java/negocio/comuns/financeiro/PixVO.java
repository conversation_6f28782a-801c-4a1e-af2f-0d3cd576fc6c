package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.pix.PixDto;
import servicos.pix.PixStatusEnum;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PixVO extends SuperVO {

    private Integer codigo;
    private String txid;
    private ConvenioCobrancaVO conveniocobranca;
    private Date data;
    private Integer expiracao; // Em segundos
    private Double valor;
    private Integer empresa;
    private String status;
    private String status_anterior;
    private String textoImagemQrcode;
    private String devedorCpf;
    private String devedorCnpj;
    private String devedorNome;
    private Integer pessoa;
    private String recebedorChave;
    private List<PixMovParcelaVO> pixMovParcelas;
    private Integer reciboPagamento;
    private String clientId;
    private String clientSecret;
    private String appKey;
    private String basicAuth;
    private String ambiente;
    private Integer formapagamento;
    private String paramsEnvio;
    private String paramsResposta;
    private UsuarioVO usuarioResponsavel;
    private Date dataPagamento;
    @NaoControlarLogAlteracao
    private String chaveBanco; // atributo transient
    @NaoControlarLogAlteracao
    private boolean reciboEstornado = false; // atributo transient
    private MovPagamentoVO movPagamentoVO;
    private OrigemCobrancaEnum origem;
    @NaoControlarLogAlteracao
    private Integer qtdMovParcelas;
    @NaoControlarLogAlteracao
    private PessoaVO pessoaVO;
    @NaoControlarLogAlteracao
    private ClienteVO clienteVO;
    private Double desconto;

    private String e2eId;

    private String pedidoNumero;

    private Date dataCredito;
    private String transactionReceiptUrl;

    @NaoControlarLogAlteracao
    private boolean pagoOrigemWebhook;

    public PixVO() {
    }

    public PixVO(PixDto pixDto, ConvenioCobrancaVO convenioCobranca, Integer empresa, Integer pessoa) {
        setTxid(pixDto.getTxid());
        setConveniocobranca(convenioCobranca);
        setData(pixDto.getCalendario().getCriacao());
        setValor(Double.parseDouble(pixDto.getValor().getOriginal()));
        setEmpresa(empresa);
        setStatus(pixDto.getStatus());
        setTextoImagemQrcode(pixDto.getTextoImagemQRcode());
        setDevedorCpf(pixDto.getDevedor().getCpf());
        setDevedorCnpj(pixDto.getDevedor().getCnpj());
        setPessoa(pessoa);
        setDevedorNome(pixDto.getDevedor().getNome());
        setRecebedorChave(pixDto.getChave());
        setExpiracao(pixDto.getCalendario().getExpiracao());
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTxid() {
        return txid;
    }

    public void setTxid(String txid) {
        this.txid = txid;
    }

    public ConvenioCobrancaVO getConveniocobranca() {
        if (conveniocobranca == null){
            conveniocobranca = new ConvenioCobrancaVO();
        }
        return conveniocobranca;
    }

    public void setConveniocobranca(ConvenioCobrancaVO conveniocobranca) {
        this.conveniocobranca = conveniocobranca;
    }

    public Date getData() {
        return data;
    }

    public String getDataFormatada(){
        return Uteis.getDataComHHMMSS(data);
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getStatus() {
        if (status == null) {
            status = "";
        }
        return status;
    }

    public boolean isStatusAtivo() {
        return getStatusEnum() != null && getStatusEnum().equals(PixStatusEnum.ATIVA);
    }

    public boolean isStatusExpirado() {
        return getStatusEnum() != null && getStatusEnum().equals(PixStatusEnum.EXPIRADA);
    }

    public boolean isStatusConcluido() {
        return getStatusEnum() != null && getStatusEnum().equals(PixStatusEnum.CONCLUIDA);
    }

    public PixStatusEnum getStatusEnum(){
        return PixStatusEnum.valueOf(status);
    }

    public String getStatusDescricao(){
        try {
            if (this.getStatus().equals(PixStatusEnum.ATIVA.toString())) {
                return "AGUARDANDO PAGAMENTO";
            }

            if (this.getStatus().equals(PixStatusEnum.CONCLUIDA.toString())) {
                return "PAGO";
            }

            if (this.getStatus().equals(PixStatusEnum.REMOVIDA_PELO_USUARIO_RECEBEDOR.toString())) {
                return "CANCELADA";
            }

            return this.getStatus();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public String getStatusExplicacao() {
        try {
            if (this.getStatus().equals(PixStatusEnum.ATIVA.toString())) {
                return "Aguardando Pagamento";
            }

            if (status.equals(PixStatusEnum.CONCLUIDA.toString())) {
                if (dataPagamento != null) {
                    return "Pago no dia " + Uteis.getDataComHHMM(dataPagamento);
                } else {
                    return "Pago";
                }
            }

            if (status.equals(PixStatusEnum.EXPIRADA.toString())) {
                return "Expirou o período de pagamento, necessário gerar outro Pix ou pagar por outra forma de pagamento";
            }

            if (status.equals(PixStatusEnum.REMOVIDA_PELO_USUARIO_RECEBEDOR.toString()) ||
                    status.equals(PixStatusEnum.REMOVIDA_PELO_PSP.toString())) {
                return "Não está mais disponível para pagamento, necessário gerar outro Pix ou pagar por outra forma de pagamento";
            }

            return this.getStatus();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusAnterior() {
        return status_anterior;
    }

    public void setStatusAnterior(String status_anterior) {
        this.status_anterior = status_anterior;
    }

    public String getTextoImagemQrcode() {
        return textoImagemQrcode;
    }

    public void setTextoImagemQrcode(String texto_imagem_qrcode) {
        this.textoImagemQrcode = texto_imagem_qrcode;
    }

    public String getDevedorCpf() {
        return devedorCpf;
    }

    public void setDevedorCpf(String devedor_cpf) {
        this.devedorCpf = devedor_cpf;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getDevedorNome() {
        return devedorNome;
    }

    public String getDevedorCpfOuCnpjFormatado(){
        if (!UteisValidacao.emptyString(this.getDevedorCpf())) {
            return Uteis.formatarCpfCnpj(this.getDevedorCpf(), false);
        } else if (!UteisValidacao.emptyString(this.getDevedorCnpj())) {
            return Uteis.formatarCpfCnpj(this.getDevedorCnpj(), false);
        } else {
            return "";
        }
    }

    public void setDevedorNome(String devedor_nome) {
        this.devedorNome = devedor_nome;
    }

    public String getRecebedorChave() {
        if (UteisValidacao.emptyString(recebedorChave)) {
            return "";
        }
        return recebedorChave;
    }

    public void setRecebedorChave(String recebedor_chave) {
        this.recebedorChave = recebedor_chave;
    }

    public Integer getExpiracao() {
        return expiracao;
    }

    public void setExpiracao(Integer expiracao) {
        this.expiracao = expiracao;
    }

    public Double getValor() {
        return valor;
    }

    public String getValorFormatado(){
        return Uteis.formatarValorEmReal(valor);
    }

    public String getValorApresentar(){
        return Formatador.formatarValorMonetario(valor);
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public List<PixMovParcelaVO> getPixMovParcelas() {
        if (pixMovParcelas == null) {
            pixMovParcelas = new ArrayList<>();
        }
        return pixMovParcelas;
    }

    public void setPixMovParcelas(List<PixMovParcelaVO> pixMovParcelas) {
        this.pixMovParcelas = pixMovParcelas;
    }

    public boolean expirado() throws ParseException {
        return Calendario.menorOuIgualHHMM(dataExpiracao(), Calendario.hoje());
    }

    public Date dataExpiracao(){
        return Calendario.somarSegundos(data, expiracao.intValue());
    }

    public boolean expiradoPJBank() throws Exception {
        //usado no pjbank
        return  Calendario.menorOuIgualComHora(calcularExpiracaoPJBank(), Calendario.hoje());
    }

    public Date calcularExpiracaoPJBank() throws Exception {
        //usado no pjbank
        //fórmula: (expiração configurada no convênio (em dias) + segundos até o próximo processo remessaService rodar), onde o remessa service pode rodar às 12:30 e as 22:00

        //primeiro obter a data e horário exato que o pix vai expirar
        Date expiraEm = Calendario.somarDias(data, expiracao.intValue());

        //agora obter o próximo processamento do remessa service lá no dia em que o pix vai expirar
        //obs: pode ser que seja 12:30 ou as 22:00
        Date proximoProcessamento = Uteis.obterProximoProcessamentoRemessaServiceAPartirDeUmaData(expiraEm);

        return proximoProcessamento;
    }

    public String getReciboPagamentoApresentarTela() {
        if (UteisValidacao.emptyNumber(getReciboPagamento()) && this.isReciboEstornado()) {
            return "ESTORNADO";
        } else if (!UteisValidacao.emptyNumber(getReciboPagamento())) {
            return getReciboPagamento().toString();
        }
        return "";
    }

    public boolean isApresentarDetalhesPix() {
        if (this.getConveniocobranca() != null) {
            return (this.getConveniocobranca().isPixPjBank() &&
                    !this.getStatus().equals(PixStatusEnum.CANCELADA.getDescricao()) &&
                    !this.getStatus().equals(PixStatusEnum.EXPIRADA.getDescricao()))
                    || this.getConveniocobranca().isPixAsaas();
        }
        return false;
    }

    public Integer getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(Integer reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String pppKey) {
        this.appKey = pppKey;
    }

    public String getBasicAuth() {
        return basicAuth;
    }

    public void setBasicAuth(String basicAuth) {
        this.basicAuth = basicAuth;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public AmbienteEnum getAmbienteEnum(){
        return AmbienteEnum.valueOf(ambiente);
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public Integer getFormapagamento() {
        return formapagamento;
    }

    public void setFormapagamento(Integer formapagamento) {
        this.formapagamento = formapagamento;
    }

    public String getParcelasTelaCliente() {
        StringBuilder parce = new StringBuilder();
        for (PixMovParcelaVO pixMovParcelaVO : this.getPixMovParcelas()) {
            if (pixMovParcelaVO.getMovParcelaVO() != null &&
                    !UteisValidacao.emptyNumber(pixMovParcelaVO.getMovParcelaVO().getCodigo())) {
                parce.append(pixMovParcelaVO.getMovParcelaVO().getDescricao());
                parce.append("</br>");
            }
        }
        return parce.toString();
    }

    public String getParcelasRelPix() {
        StringBuilder parce = new StringBuilder();
        for (PixMovParcelaVO pixMovParcelaVO : this.getPixMovParcelas()) {
            if (pixMovParcelaVO.getMovParcelaVO() != null &&
                    !UteisValidacao.emptyNumber(pixMovParcelaVO.getMovParcelaVO().getCodigo())) {
                parce.append(pixMovParcelaVO.getMovParcelaVO().getDescricao());
                parce.append(" | ");
            }
        }
        //remove o caractere especial "|" do final da string
        if (parce.length() > 0) {
            return parce.substring(0, parce.length() - 2);
        }
        return parce.toString();
    }

    public String getParcelasRelPixTitle() {
        StringBuilder title = new StringBuilder();
        for (PixMovParcelaVO pixMovParcelaVO : this.getPixMovParcelas()) {
            if (pixMovParcelaVO.getMovParcelaVO() != null &&
                    !UteisValidacao.emptyNumber(pixMovParcelaVO.getMovParcelaVO().getCodigo())) {
                title.append("Cód. ").append(pixMovParcelaVO.getMovParcelaVO().getCodigo()).append(" - ").append(pixMovParcelaVO.getMovParcelaVO().getDescricao()).append(" | ");
                if (!UteisValidacao.emptyString(pixMovParcelaVO.getMovParcelaVO().getPessoa().getNome())) {
                    title.append("Pessoa: ").append(pixMovParcelaVO.getMovParcelaVO().getPessoa().getNome());
                    title.append("</br>");
                }
            }
        }
        return title.toString();
}

    public String getParcelasTelaClienteTitle() {
        StringBuilder title = new StringBuilder();
        for (PixMovParcelaVO pixMovParcelaVO : this.getPixMovParcelas()) {
            if (pixMovParcelaVO.getMovParcelaVO() != null &&
                    !UteisValidacao.emptyNumber(pixMovParcelaVO.getMovParcelaVO().getCodigo())) {
                title.append("Cód. ").append(pixMovParcelaVO.getMovParcelaVO().getCodigo()).append(" - ").append(pixMovParcelaVO.getMovParcelaVO().getDescricao());
                if (!UteisValidacao.emptyString(pixMovParcelaVO.getClienteVO().getPessoa().getNome())) {
                    title.append(" | Pessoa: ").append(pixMovParcelaVO.getClienteVO().getPessoa().getNome());
                    title.append("</br>");
                }
            }
        }
        return title.toString();
    }

    public String getParamsEnvio() {
        if (paramsEnvio == null) {
            paramsEnvio = "";
        }
        return paramsEnvio;
    }

    public void setParamsEnvio(String paramsEnvio) {
        this.paramsEnvio = paramsEnvio;
    }

    public String getParamsResposta() {
        if (paramsResposta == null) {
            paramsResposta = "";
        }
        return paramsResposta;
    }

    public void setParamsResposta(String paramsResposta) {
        this.paramsResposta = paramsResposta;
    }

    public UsuarioVO getUsuarioResponsavel() {
        if (usuarioResponsavel == null) {
            usuarioResponsavel = new UsuarioVO();
        }
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public String getConvenioCobranca_Title() {
        if (UteisValidacao.emptyNumber(this.getConveniocobranca().getCodigo())){
            return "Não foi possível obter o convênio";
        } else {
            return this.getConveniocobranca().getDescricao();
        }
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public String getTextoQRCodeSemEncodeSomenteJSP() {
        try {
            if (!UteisValidacao.emptyString((String) JSFUtilities.getFromSession("urlBrowser")) && !UteisValidacao.emptyString(Uteis.getUrl())) {
                //monta a url com o endereço local obtido pelo browser
                //criptografia do servlet QRCodeServlet
                return Uteis.getUrl() + "/QRCode?qrtext=" + Uteis.encriptar(this.getTextoImagemQrcode(), "TxTQrcOdE") + "&encr=s";
            } else {
                return obterUrlSomenteImagemQRcode(getChaveBanco());
            }
        } catch (Exception ex) {
            return "";
        }
    }

    public String obterUrlCompartilharQRcode(String chave) {
        JSONObject json = new JSONObject();
        json.put("chave", chave);
        json.put("pix", this.getCodigo());
        //criptografia compartilhada com a API
        return (Uteis.getUrlAPI() + "/prest/pix/visualizar/" + Uteis.encriptar(json.toString(), "TKP4cT0PiX"));
    }

    public String obterUrlSomenteImagemQRcode(String chave) {
        //criptografia do servlet QRCodeServlet
        String textoQrCode = Uteis.encriptar(this.getTextoImagemQrcode(), "TxTQrcOdE");
        return (Uteis.getUrlAPI() + "/prest/pix/qrcode/" + chave+ "/" + textoQrCode);
    }

    public String getChaveBanco() {
        if (chaveBanco == null) {
            chaveBanco = "";
        }
        return chaveBanco;
    }

    public void setChaveBanco(String chaveBanco) {
        this.chaveBanco = chaveBanco;
    }

    public boolean isReciboEstornado() {
        return reciboEstornado;
    }

    public void setReciboEstornado(boolean reciboEstornado) {
        this.reciboEstornado = reciboEstornado;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        if (movPagamentoVO == null) {
            movPagamentoVO = new MovPagamentoVO();
        }
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public OrigemCobrancaEnum getOrigem() {
        if (origem == null) {
            origem = OrigemCobrancaEnum.NENHUM;
        }
        return origem;
    }

    public void setOrigem(OrigemCobrancaEnum origem) {
        this.origem = origem;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Integer getQtdMovParcelas() {
        if (qtdMovParcelas == null) {
            qtdMovParcelas = 0;
        }
        return qtdMovParcelas;
    }

    public void setQtdMovParcelas(Integer qtdMovParcelas) {
        this.qtdMovParcelas = qtdMovParcelas;
    }

    public Double getDesconto() {
        if (desconto == null) {
            desconto = 0.0;
        }
        return desconto;
    }

    public void setDesconto(Double desconto) {
        this.desconto = desconto;
    }

    public String getE2eId() {
        if (e2eId == null) {
            return "";
        }
        return e2eId;
    }

    public void setE2eId(String e2eId) {
        this.e2eId = e2eId;
    }

    public String getPedidoNumero() {
        if (UteisValidacao.emptyString(pedidoNumero)) {
            return "";
        }
        return pedidoNumero;
    }

    public void setPedidoNumero(String pedidoNumero) {
        this.pedidoNumero = pedidoNumero;
    }

    public Date getDataCredito() {
        return dataCredito;
    }

    public void setDataCredito(Date dataCredito) {
        this.dataCredito = dataCredito;
    }

    public String getTransactionReceiptUrl() {
        if (UteisValidacao.emptyString(transactionReceiptUrl)) {
            return "";
        }
        return transactionReceiptUrl;
    }

    public void setTransactionReceiptUrl(String transactionReceiptUrl) {
        this.transactionReceiptUrl = transactionReceiptUrl;
    }

    public boolean isPagoOrigemWebhook() {
        return pagoOrigemWebhook;
    }

    public void setPagoOrigemWebhook(boolean pagoOrigemWebhook) {
        this.pagoOrigemWebhook = pagoOrigemWebhook;
    }

    public boolean isPodeSincronizar() {
        return this.conveniocobranca != null && getStatusEnum() != null && getStatusEnum().equals(PixStatusEnum.ATIVA) &&
                (this.conveniocobranca.isPixBB() || this.conveniocobranca.isPixSantander() || this.conveniocobranca.isPixBradesco() ||
                        this.conveniocobranca.isPixPjBank() || this.conveniocobranca.isPixInter() || this.conveniocobranca.isPixItau());
    }

    public boolean isExibirDataExpiracaoCalculadaPix() {
        return getStatusEnum() != null && (getStatusEnum().equals(PixStatusEnum.ATIVA) || getStatusEnum().equals(PixStatusEnum.EXPIRADA));
    }

    public String getTitleExpiracaoCalculada() throws Exception {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("Pix foi gerado em: ").append(getDataFormatada()).append("</br>");
            if (getData() != null && getConveniocobranca() != null && getConveniocobranca().getTipo() != null) {
                Date expiraEm = null;
                if (getConveniocobranca().getTipo().equals(TipoConvenioCobrancaEnum.PIX_PJBANK)) { //PJBANK
                    expiraEm = calcularExpiracaoPJBank(); //pjbank é em dias, então rpecisa calcular
                } else { //Demais pix já é calculado no momento da geração do pix
                    expiraEm = Calendario.somarSegundos(getData(), getExpiracao().intValue());
                }

                String textoExpiracao = "";
                if (getStatus() != null && getStatus().equals(PixStatusEnum.EXPIRADA.getDescricao())) {
                    textoExpiracao = "Pix expirado em: ";
                } else if (getStatus() != null && getStatus().equals(PixStatusEnum.ATIVA.getDescricao())) {
                    textoExpiracao = "Pix expira em: ";
                }

                if (!UteisValidacao.emptyString(textoExpiracao)) {
                        sb.append(textoExpiracao).append(Uteis.getDataComHHMM(expiraEm));
                }
            }
            return sb.toString();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getDevedorCnpj() {
        return devedorCnpj;
    }

    public void setDevedorCnpj(String devedorCnpj) {
        this.devedorCnpj = devedorCnpj;
    }
}

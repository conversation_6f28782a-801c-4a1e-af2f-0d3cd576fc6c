package negocio.comuns.financeiro;

import java.util.Date;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import negocio.comuns.arquitetura.SuperTO;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class RecebivelAvulsoTO extends SuperTO {

    private static final long serialVersionUID = -4989754405531760406L;
    private EmpresaVO empresa;
    private Boolean tipoCheque = Boolean.TRUE;
    private ClienteVO cliente = new ClienteVO();
    private Date faturamento;
    private Date compensacao;
    private Date lancamento;
    private UsuarioVO responsavel = new UsuarioVO();
    private CentroCustoTO centroCustos = new CentroCustoTO();
    private PlanoContaTO planoContas = new PlanoContaTO();
    private ChequeVO cheque = new ChequeVO();
    private CartaoCreditoVO cartaoCredito = new CartaoCreditoVO();
    private Double valor = 0.0;
    private String codAutorizacao = "";
    private int movConta = 0;
    private String msgErro = "";

    public void validarDados() throws ValidacaoException {
        if (UteisValidacao.emptyNumber(getEmpresa().getCodigo())) {
            throw new ValidacaoException("msg_recAvulso_empresa");
        }
        if (UteisValidacao.emptyNumber(getResponsavel().getCodigo())) {
            throw new ValidacaoException("msg_recAvulso_responsavel");
        }
        if (UteisValidacao.emptyNumber(getCliente().getPessoa().getCodigo())) {
            throw new ValidacaoException("msg_recAvulso_cliente");
        }
        if (faturamento == null) {
            throw new ValidacaoException("msg_recAvulso_faturamento");
        }
        if (compensacao == null) {
            throw new ValidacaoException("msg_recAvulso_compensacao");
        }
        if (UteisValidacao.emptyNumber(valor)) {
            throw new ValidacaoException("msg_recAvulso_valor");
        }
        if (!getTipoCheque() && UteisValidacao.emptyNumber(getCartaoCredito().getOperadora().getCodigo())) {
            throw new ValidacaoException("msg_recAvulso_operadora");
        }
        if (!getTipoCheque() && UteisValidacao.emptyNumber(getCartaoCredito().getNrParcela())) {
            throw new ValidacaoException("msg_recAvulso_nrparcela");
        }
        if (getTipoCheque() && UteisValidacao.emptyNumber(getCheque().getBanco().getCodigo())) {
            throw new ValidacaoException("msg_recAvulso_banco");
        }
        if (getTipoCheque() && UteisValidacao.emptyString(getCheque().getAgencia())) {
            throw new ValidacaoException("msg_recAvulso_agencia");
        }
        if (getTipoCheque() && UteisValidacao.emptyString(getCheque().getConta())) {
            throw new ValidacaoException("msg_recAvulso_conta");
        }
        if (getTipoCheque() && UteisValidacao.emptyString(getCheque().getNumero())) {
            throw new ValidacaoException("msg_recAvulso_nrcheque");
        }

    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public Date getFaturamento() {
        return faturamento;
    }

    public void setFaturamento(Date faturamento) {
        this.faturamento = faturamento;
    }

    public Date getCompensacao() {
        return compensacao;
    }

    public void setCompensacao(Date compensacao) {
        this.compensacao = compensacao;
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }

    public CentroCustoTO getCentroCustos() {
        return centroCustos;
    }

    public void setCentroCustos(CentroCustoTO centroCustos) {
        this.centroCustos = centroCustos;
    }

    public PlanoContaTO getPlanoContas() {
        return planoContas;
    }

    public void setPlanoContas(PlanoContaTO planoContas) {
        this.planoContas = planoContas;
    }

    public ChequeVO getCheque() {
        return cheque;
    }

    public void setCheque(ChequeVO cheque) {
        this.cheque = cheque;
    }

    public CartaoCreditoVO getCartaoCredito() {
        return cartaoCredito;
    }

    public void setCartaoCredito(CartaoCreditoVO cartaoCredito) {
        this.cartaoCredito = cartaoCredito;
    }

    public void setTipoCheque(Boolean tipoCheque) {
        this.tipoCheque = tipoCheque;
    }

    public Boolean getTipoCheque() {
        return tipoCheque;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getValor() {
        return valor;
    }

    public void setCodAutorizacao(String codAutorizacao) {
        this.codAutorizacao = codAutorizacao;
    }

    public String getCodAutorizacao() {
        return codAutorizacao;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setMovConta(int movConta) {
        this.movConta = movConta;
    }

    public int getMovConta() {
        return movConta;
    }

    public String getCompensacaoApresentar() {
        return Uteis.getData(compensacao);
    }

    public String getFaturamentoApresentar() {
        return Uteis.getData(faturamento);
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }

    public Date getLancamento() {
        return lancamento;
    }

    public String getLancamentoApresentar() {
        return Uteis.getData(lancamento);
    }

    public String getMsgErro() {
        return msgErro;
    }

    public void setMsgErro(String msgErro) {
        this.msgErro = msgErro;
    }
    
    public Integer getBancoCodigo(){
        if(this.cheque.getBanco() != null){
            return cheque.getBanco().getCodigo();
        }
        return 0;
    }
    
    public String getAgenciaApresentar(){
        return this.cheque.getAgencia();
    }
    public String getContaApresentar(){
        return this.cheque.getConta();
    }
    public String getNumeroApresentar(){
        return this.cheque.getNumero();
    }
    public String getNomeNoChequeApresentar(){
        return this.cheque.getNomeNoCheque();
    }
    
    public String getCPFCNPJApresentar(){
        return this.cheque.getCnpj()+this.cheque.getCpf();
    }
    
    public String getMatriculaApresentar(){
        return this.cliente.getMatricula();
    }
}

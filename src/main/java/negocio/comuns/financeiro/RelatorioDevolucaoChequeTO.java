package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * Created by ulisses on 16/11/2017.
 */
public class RelatorioDevolucaoChequeTO extends SuperTO {

    private String nomePessoa = "";
    private String numeroCheque = "";
    private String agenciaCheque= "";
    private String contaCheque= "";
    private String nomeBancoCheque="";
    private double valorCheque;
    private Date dataCompensacao;
    private Date dataDevolucao;
    private boolean devolvido = false;
    private boolean registroTotalizador = false;
    private String nomeNoCheque = "";

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public String getNumeroCheque() {
        return numeroCheque;
    }

    public void setNumeroCheque(String numeroCheque) {
        this.numeroCheque = numeroCheque;
    }

    public String getAgenciaCheque() {
        return agenciaCheque;
    }

    public void setAgenciaCheque(String agenciaCheque) {
        this.agenciaCheque = agenciaCheque;
    }

    public String getContaCheque() {
        return contaCheque;
    }

    public void setContaCheque(String contaCheque) {
        this.contaCheque = contaCheque;
    }

    public String getNomeBancoCheque() {
        return nomeBancoCheque;
    }

    public void setNomeBancoCheque(String nomeBancoCheque) {
        this.nomeBancoCheque = nomeBancoCheque;
    }

    public double getValorCheque() {
        return valorCheque;
    }

    public void setValorCheque(double valorCheque) {
        this.valorCheque = valorCheque;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public String getDataCompensacao_apresentar() {
        return Uteis.getDataAplicandoFormatacao(getDataCompensacao(), "dd/MM/yyyy");
    }

    public String getDataDevolucao_apresentar() {
        return Uteis.getDataAplicandoFormatacao(getDataDevolucao(), "dd/MM/yyyy");
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public Date getDataDevolucao() {
        return dataDevolucao;
    }

    public void setDataDevolucao(Date dataDevolucao) {
        this.dataDevolucao = dataDevolucao;
    }

    public String getValor_apresentar(){
        if (registroTotalizador){
            return "Total geral: "  +Formatador.formatarValorMonetario(getValorCheque());
        }else{
            return Formatador.formatarValorMonetario(getValorCheque());
        }

    }

    public boolean isRegistroTotalizador() {
        return registroTotalizador;
    }

    public void setRegistroTotalizador(boolean registroTotalizador) {
        this.registroTotalizador = registroTotalizador;
    }

    public boolean isDevolvido() {
        return devolvido;
    }

    public void setDevolvido(boolean devolvido) {
        this.devolvido = devolvido;
    }

    public String getNomeNoCheque() {
        return nomeNoCheque;
    }

    public void setNomeNoCheque(String nomeNoCheque) {
        this.nomeNoCheque = nomeNoCheque;
    }
}

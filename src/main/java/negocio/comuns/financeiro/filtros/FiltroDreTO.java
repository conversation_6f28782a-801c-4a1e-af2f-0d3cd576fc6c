package negocio.comuns.financeiro.filtros;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;
import java.util.List;

public class FiltroDreTO extends SuperTO{
    private static final long serialVersionUID = -3761376574106079271L;

    private TipoRelatorioDF tipoRelatorioDF;
    private TipoVisualizacaoRelatorioDF tipoVisualizacao;
    private Date dataInicial;
    private Date dataInicioRelatorio = Calendario.hoje();
    private Date dataFimRelatorio = Calendario.hoje();
    private Integer codEmpresa;
    private TipoFonteDadosDF tipoFonteDadosDF = TipoFonteDadosDF.TODAS;
    private boolean agruparValorProdutoMMasModalidade ;
    private boolean centroCustos = false;
    private List<ContaVO> contasFiltro;

    public TipoRelatorioDF getTipoRelatorioDF() {
        return tipoRelatorioDF;
    }

    public void setTipoRelatorioDF(TipoRelatorioDF tipoRelatorioDF) {
        this.tipoRelatorioDF = tipoRelatorioDF;
    }

    public TipoVisualizacaoRelatorioDF getTipoVisualizacao() {
        return tipoVisualizacao;
    }

    public void setTipoVisualizacao(TipoVisualizacaoRelatorioDF tipoVisualizacao) {
        this.tipoVisualizacao = tipoVisualizacao;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataInicioRelatorio() {
        return dataInicioRelatorio;
    }

    public void setDataInicioRelatorio(Date dataInicioRelatorio) {
        this.dataInicioRelatorio = dataInicioRelatorio;
    }

    public Date getDataFimRelatorio() {
        return dataFimRelatorio;
    }

    public void setDataFimRelatorio(Date dataFimRelatorio) {
        this.dataFimRelatorio = dataFimRelatorio;
    }

    public void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }


    public TipoFonteDadosDF getTipoFonteDadosDF() {
        return tipoFonteDadosDF;
    }

    public void setTipoFonteDadosDF(TipoFonteDadosDF tipoFonteDadosDF) {
        this.tipoFonteDadosDF = tipoFonteDadosDF;
    }

    public boolean isAgruparValorProdutoMMasModalidade() {
        return agruparValorProdutoMMasModalidade;
    }

    public void setAgruparValorProdutoMMasModalidade(boolean agruparValorProdutoMMasModalidade) {
        this.agruparValorProdutoMMasModalidade = agruparValorProdutoMMasModalidade;
    }

    public Integer getCodEmpresa() {
        return codEmpresa;
    }

    public boolean isCentroCustos() {
        return centroCustos;
    }

    public void setCentroCustos(boolean centroCustos) {
        this.centroCustos = centroCustos;
    }
    public List<ContaVO> getContasFiltro() {
        return contasFiltro;
    }

    public void setContasFiltro(List<ContaVO> contasFiltro) {
        this.contasFiltro = contasFiltro;
    }
}

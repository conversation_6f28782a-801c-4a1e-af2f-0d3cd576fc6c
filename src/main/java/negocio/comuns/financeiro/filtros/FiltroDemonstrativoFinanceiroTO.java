package negocio.comuns.financeiro.filtros;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FiltroDemonstrativoFinanceiroTO extends SuperTO {
    private static final long serialVersionUID = -3761376574106079271L;


    private TipoRelatorioDF tipoRelatorioDF;
    private TipoVisualizacaoRelatorioDF tipoVisualizacao = TipoVisualizacaoRelatorioDF.PLANOCONTA;
    private Date dataInicial;
    private Date dataInicioRelatorio = Calendario.hoje();
    private Date dataFimRelatorio = Calendario.hoje();
    private String filtroContas;
    private List<ContaVO> contasFiltro;
    private Integer codEmpresa;
    private List<Integer> codigosCentrosSelecionados = new ArrayList<Integer>();
    private TipoFonteDadosDF tipoFonteDadosDF = TipoFonteDadosDF.TODAS;
    private boolean agruparValorProdutoMMasModalidade ;





    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataInicioRelatorio() {
        return dataInicioRelatorio;
    }

    public void setDataInicioRelatorio(Date dataInicioRelatorio) {
        this.dataInicioRelatorio = dataInicioRelatorio;
    }

    public Date getDataFimRelatorio() {
        return dataFimRelatorio;
    }

    public void setDataFimRelatorio(Date dataFimRelatorio) {
        this.dataFimRelatorio = dataFimRelatorio;
    }

    public String getFiltroContas() {
        return filtroContas;
    }

    public void setFiltroContas(String filtroContas) {
        this.filtroContas = filtroContas;
    }



    public TipoRelatorioDF getTipoRelatorioDF() {
        return tipoRelatorioDF;
    }

    public void setTipoRelatorioDF(TipoRelatorioDF tipoRelatorioDF) {
        this.tipoRelatorioDF = tipoRelatorioDF;
    }

    public TipoVisualizacaoRelatorioDF getTipoVisualizacao() {
        return tipoVisualizacao;
    }

    public void setTipoVisualizacao(TipoVisualizacaoRelatorioDF tipoVisualizacao) {
        this.tipoVisualizacao = tipoVisualizacao;
    }

    public List<Integer> getCodigosCentrosSelecionados() {
        return codigosCentrosSelecionados;
    }

    public void setCodigosCentrosSelecionados(List<Integer> codigosCentrosSelecionados) {
        this.codigosCentrosSelecionados = codigosCentrosSelecionados;
    }

    public TipoFonteDadosDF getTipoFonteDadosDF() {
        return tipoFonteDadosDF;
    }

    public void setTipoFonteDadosDF(TipoFonteDadosDF tipoFonteDadosDF) {
        this.tipoFonteDadosDF = tipoFonteDadosDF;
    }

    public boolean isAgruparValorProdutoMMasModalidade() {
        return agruparValorProdutoMMasModalidade;
    }

    public void setAgruparValorProdutoMMasModalidade(boolean agruparValorProdutoMMasModalidade) {
        this.agruparValorProdutoMMasModalidade = agruparValorProdutoMMasModalidade;
    }

    public Integer getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    public List<ContaVO> getContasFiltro() {
        if (contasFiltro == null) {
            contasFiltro = new ArrayList<ContaVO>();
        }
        return contasFiltro;
    }

    public void setContasFiltro(List<ContaVO> contasFiltro) {
        this.contasFiltro = contasFiltro;
    }
}


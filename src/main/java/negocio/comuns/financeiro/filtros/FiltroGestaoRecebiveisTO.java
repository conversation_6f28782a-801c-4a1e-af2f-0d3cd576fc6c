package negocio.comuns.financeiro.filtros;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.LoteVO;

import java.util.Date;
import java.util.List;

public class FiltroGestaoRecebiveisTO extends SuperTO {
    private static final long serialVersionUID = -3761376574106079271L;
    private int codigoEmpresa;
    private String nome;
    private String nomeTerceiro;
    private Date dataInicialCompensacao;
    private String horaInicialCompensacao;
    private Date dataFinalCompensacao;
    private String horaFinalCompensacao;
    private Date dataInicialLancamento;
    private String horaInicialLancamento;
    private Date dataFinalLancamento;
    private String horaFinalLancamento;
    private String cpf;
    private String nomeClienteContrato;
    private String matricula;
    private String nsu;
    private Integer operadoraCartao;
    private String codAutorizacao;
    private String codigoLoteFiltro = "";
    private List<String> empresasSelecionadas;


    public int getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(int codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomeTerceiro() {
        return nomeTerceiro;
    }

    public void setNomeTerceiro(String nomeTerceiro) {
        this.nomeTerceiro = nomeTerceiro;
    }

    public Date getDataInicialCompensacao() {
        return dataInicialCompensacao;
    }

    public void setDataInicialCompensacao(Date dataInicialCompensacao) {
        this.dataInicialCompensacao = dataInicialCompensacao;
    }

    public String getHoraInicialCompensacao() {
        return horaInicialCompensacao;
    }

    public void setHoraInicialCompensacao(String horaInicialCompensacao) {
        this.horaInicialCompensacao = horaInicialCompensacao;
    }

    public Date getDataFinalCompensacao() {
        return dataFinalCompensacao;
    }

    public void setDataFinalCompensacao(Date dataFinalCompensacao) {
        this.dataFinalCompensacao = dataFinalCompensacao;
    }

    public String getHoraFinalCompensacao() {
        return horaFinalCompensacao;
    }

    public void setHoraFinalCompensacao(String horaFinalCompensacao) {
        this.horaFinalCompensacao = horaFinalCompensacao;
    }

    public Date getDataInicialLancamento() {
        return dataInicialLancamento;
    }

    public void setDataInicialLancamento(Date dataInicialLancamento) {
        this.dataInicialLancamento = dataInicialLancamento;
    }

    public String getHoraInicialLancamento() {
        return horaInicialLancamento;
    }

    public void setHoraInicialLancamento(String horaInicialLancamento) {
        this.horaInicialLancamento = horaInicialLancamento;
    }

    public Date getDataFinalLancamento() {
        return dataFinalLancamento;
    }

    public void setDataFinalLancamento(Date dataFinalLancamento) {
        this.dataFinalLancamento = dataFinalLancamento;
    }

    public String getHoraFinalLancamento() {
        return horaFinalLancamento;
    }

    public void setHoraFinalLancamento(String horaFinalLancamento) {
        this.horaFinalLancamento = horaFinalLancamento;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getNomeClienteContrato() {
        return nomeClienteContrato;
    }

    public void setNomeClienteContrato(String nomeClienteContrato) {
        this.nomeClienteContrato = nomeClienteContrato;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNsu() {
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public Integer getOperadoraCartao() {
        return operadoraCartao;
    }

    public void setOperadoraCartao(Integer operadoraCartao) {
        this.operadoraCartao = operadoraCartao;
    }

    public String getCodAutorizacao() {
        return codAutorizacao;
    }

    public void setCodAutorizacao(String codAutorizacao) {
        this.codAutorizacao = codAutorizacao;
    }

    public String getCodigoLoteFiltro() {
        return codigoLoteFiltro;
    }

    public void setCodigoLoteFiltro(String codigoLoteFiltro) {
        this.codigoLoteFiltro = codigoLoteFiltro;
    }

    public List<String> getEmpresasSelecionadas() {
        return empresasSelecionadas;
    }

    public void setEmpresasSelecionadas(List<String> empresasSelecionadas) {
        this.empresasSelecionadas = empresasSelecionadas;
    }

}



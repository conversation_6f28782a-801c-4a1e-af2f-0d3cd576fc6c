package negocio.comuns.financeiro.filtros;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.ContaVO;

import java.util.ArrayList;
import java.util.List;

public class FiltroMovFinanceirasTO extends SuperTO {
    private static final long serialVersionUID = -3761376574106079271L;
    private List<ContaVO> contas = new ArrayList<ContaVO>();

    public List<ContaVO> getContas() {
        return contas;
    }

    public void setContas(List<ContaVO> contas) {
        this.contas = contas;
    }
}

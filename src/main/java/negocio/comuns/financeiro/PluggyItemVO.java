package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 17/07/2023.
 */

public class PluggyItemVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer empresa;

    protected String id;
    protected String dadosRetorno;
    protected boolean ativo;
    protected PluggyConnectorDTO connector;
    protected boolean selecionadoParaFiltrar;
    protected List<PluggyAccountDTO> pluggyAccountsDTO;


    public PluggyItemVO() {
        super();
        inicializarDados();
    }

    public PluggyItemVO(int codigo, int empresa, JSONObject dados) {
        this.codigo = codigo;
        this.id = dados.getString("id");
        this.empresa = empresa;
        this.dadosRetorno = dados.toString();
    }


    public void inicializarDados() {
        setCodigo(new Integer(0));
        setDadosRetorno("");
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getDadosRetorno() {
        return dadosRetorno;
    }

    public void setDadosRetorno(String dadosRetorno) {
        this.dadosRetorno = dadosRetorno;
    }

    public PluggyConnectorDTO getConnector() {
        return connector;
    }

    public void setConnector(PluggyConnectorDTO connector) {
        this.connector = connector;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public String getImageUrl() {
        try {
            if (dadosRetorno != null) {
                return new JSONObject(dadosRetorno).getJSONObject("connector").getString("imageUrl");
            }
            return "";
        } catch (Exception ignore) {
            return "";
        }
    }

    public String getImageUrlFacilitePay() {
        try {
            return "https://fypay.com.br/wp-content/uploads/2023/07/favicon_com_borda.png";
        } catch (Exception ignore) {
            return "";
        }
    }

    public boolean isPluggySandbox() {
        try {
            if (dadosRetorno != null) {
                return new JSONObject(dadosRetorno).getJSONObject("connector").getString("name").equals("Pluggy Bank");
            }
            return false;
        } catch (Exception ignore) {
            return false;
        }
    }

    public String getNameConnector() {
        try {
            if (dadosRetorno != null) {
                String name = new JSONObject(dadosRetorno).getJSONObject("connector").getString("name");
                if (name.contains("Pluggy")) {
                    return "Fypay Bank";
                }
                return name;
            }
            return "";
        } catch (Exception ignore) {
            return "";
        }
    }

    public String getTitleImagemConnector() {
        if (isPluggySandbox()) {
            return "Este é um banco de exemplo com lançamentos fictícios, usado apenas para exemplo.</br>" +
                    "Ao conectar suas contas bancárias reais, todas elas aparecerão lado a lado e ainda poderão ser filtradas.";
        } else {
            return "Exibir somente lançamentos " + getNameConnector() + "</br>" + obterTextoContasConectadas();
        }
    }

    public String obterTextoContasConectadas(){
        if (UteisValidacao.emptyList(pluggyAccountsDTO)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("</br>");
        String textoPluralOuSingular = pluggyAccountsDTO.size() > 1 ? "conexões ativas" : "conexão ativa";
        sb.append("<b>").append(pluggyAccountsDTO.size()).append(" ").append(textoPluralOuSingular).append("</b></br>");
        for (PluggyAccountDTO pluggyAccountDTO : pluggyAccountsDTO) {
            sb.append(pluggyAccountDTO.getTextoComplementarApresentar()).append("</br>");
        }
        return sb.toString();
    }

    public String getPrimaryColor() {
        try {
            if (dadosRetorno != null) {
                return "#" + new JSONObject(dadosRetorno).getJSONObject("connector").getString("primaryColor");
            }
            return "#000000";
        } catch (Exception ignore) {
            return "#000000";
        }
    }

    public boolean isSelecionadoParaFiltrar() {
        return selecionadoParaFiltrar;
    }

    public void setSelecionadoParaFiltrar(boolean selecionadoParaFiltrar) {
        this.selecionadoParaFiltrar = selecionadoParaFiltrar;
    }

    public List<PluggyAccountDTO> getPluggyAccountsDTO() {
        if (UteisValidacao.emptyList(pluggyAccountsDTO)) {
            return new ArrayList<>();
        }
        return pluggyAccountsDTO;
    }

    public void setPluggyAccountsDTO(List<PluggyAccountDTO> pluggyAccountsDTO) {
        this.pluggyAccountsDTO = pluggyAccountsDTO;
    }

    public boolean isItau() {
        try {
            if (!UteisValidacao.emptyString(getDadosRetorno())) {
                JSONObject obj = new JSONObject(getDadosRetorno());
                return obj.getJSONObject("connector").getString("name").toUpperCase().contains("ITAU") || obj.getJSONObject("connector").getString("name").toUpperCase().contains("ITAÚ");
            }
            return false;
        } catch (Exception ex) {
            return false;
        }
    }

    public boolean isCaixaEconomica() {
        try {
            if (!UteisValidacao.emptyString(getDadosRetorno())) {
                JSONObject obj = new JSONObject(getDadosRetorno());
                return obj.getJSONObject("connector").getString("name").toUpperCase().contains("CAIXA ECONOMICA FEDERAL");
            }
            return false;
        } catch (Exception ex) {
            return false;
        }
    }
}

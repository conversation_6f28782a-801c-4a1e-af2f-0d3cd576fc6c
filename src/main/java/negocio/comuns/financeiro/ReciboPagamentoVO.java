/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.NumeroPorExtenso;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.LogVO;
import org.json.JSONObject;

/**
 * <AUTHOR>
 */
public class ReciboPagamentoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Date data;
    protected Double valorTotal;
    protected String nomePessoaPagador;
    protected String nomeResponsaveis;
    @ChaveEstrangeira
    protected PessoaVO pessoaPagador;
    protected UsuarioVO responsavelLancamento;
    @NaoControlarLogAlteracao
    protected ContratoVO contrato;
    private EmpresaVO empresa;
    @NaoControlarLogAlteracao
    private List<MovPagamentoVO> pagamentosDesteRecibo = new ArrayList<>();
    @NaoControlarLogAlteracao
    private Boolean emitirNFSeAutomatico = false;
    private Boolean nfseEmitida = false;
    @NaoControlarLogAlteracao
    private String nomeResponsavelLegal;
    @NaoControlarLogAlteracao
    private String cpfResponsavelLegal;

    /**
     * Construtor padrão da classe <code>ReciboPagamentoVO</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente
     * seus atributos (Classe VO).
     */
    public ReciboPagamentoVO() {
        super();
        inicializarDados();

    }

    public JSONObject toJson(){
        JSONObject json =  new JSONObject();
        json.put("codigo", codigo);
        json.put("data", data);
        json.put("valorTotal", valorTotal);
        json.put("nomePessoaPagador", nomePessoaPagador);
        json.put("nomeResponsaveis", nomeResponsaveis);
        json.put("cpfPagador", pessoaPagador.getCfp());
        json.put("responsavelLancamento", responsavelLancamento);
        json.put("pagamentosDesteRecibo", MovPagamentoVO.toJSON(pagamentosDesteRecibo));

        return json;
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>BancoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas
     * neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação
     * de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é
     *                            gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(ReciboPagamentoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getEmpresa() == null || obj.getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("Empresa deve ser informada."); // campo novo
        }
        if (obj.getData() == null) {
            throw new ConsistirException("O campo DATA (Recibo do Pagamento) deve ser informado.");
        }
        if (obj.getNomePessoaPagador() == null || obj.getNomePessoaPagador().equals("")) {
            throw new ConsistirException("O campo NOME PESSOA PAGADORA (Recibo do Pagamento) deve ser informado.");
        }
        if (obj.getResponsavelLancamento() == null || obj.getResponsavelLancamento().getCodigo() == 0) {
            throw new ConsistirException("O campo RESPONSÁVEL LANÇAMENTO (Recibo do Pagamento) deve ser informado.");
        }
        if (obj.getValorTotal() < 0) {
            throw new ConsistirException("O campo VALOR TOTAL (Recibo do Pamento) deve ser informado.");
        }

    }

    public String getResponsavel_Apresentar() {
        return getUsuarioVO().getUsername();
    }

    public String getEmpresa_Apresentar() {
        return getEmpresa().getNome();
    }

    public String getValor_Apresentar() {
        if (valorTotal == null) {
            valorTotal = 0.0;
        }
        return Formatador.formatarValorMonetario(valorTotal);
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        //        setCodigo(new Integer(0));
        //        setData(negocio.comuns.utilitarias.Calendario.hoje());
        //        setValorTotal(new Double(0));
        //        setPessoaPagador(new PessoaVO());
        //        setResponsavelLancamento(new UsuarioVO());
        //        setContrato(new ContratoVO());
        //        setNomePessoaPagador("");
    }

    public PessoaVO getPessoaPagador() {
        if (pessoaPagador == null) {
            pessoaPagador = new PessoaVO();

        }
        return pessoaPagador;
    }

    public void setPessoaPagador(PessoaVO pessoaPagador) {
        this.pessoaPagador = pessoaPagador;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ContratoVO getContrato() {
        if (contrato == null) {
            contrato = new ContratoVO();
        }
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public Date getData() {
        if (data == null) {
            data = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public String getData_Apresentar() {
        return Uteis.getData(data);
    }

    public UsuarioVO getResponsavelLancamento() {
        if (responsavelLancamento == null) {
            responsavelLancamento = new UsuarioVO();
        }
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(UsuarioVO responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Double getValorTotal() {
        if (valorTotal == null) {
            valorTotal = 0.0;
        }
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getNomePessoaPagador() {
        if (nomePessoaPagador == null) {
            nomePessoaPagador = "";
        }
        return nomePessoaPagador;
    }

    public void setNomePessoaPagador(String nomePessoaPagador) {
        this.nomePessoaPagador = nomePessoaPagador;
    }

    public String getNomeResponsaveis() {
        if (nomeResponsaveis == null) {
            nomeResponsaveis = "";
        }
        return nomeResponsaveis;
    }

    public void setNomeResponsaveis(String nomeResponsaveis) {
        this.nomeResponsaveis = nomeResponsaveis;
    }

    public String getValorTotalPorExtenso() {
        NumeroPorExtenso npe = new NumeroPorExtenso(getValorTotal(), empresa.getDescMoeda());
        return npe.toString().toUpperCase();
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public List<MovPagamentoVO> getPagamentosDesteRecibo() {
        return pagamentosDesteRecibo;
    }

    public void setPagamentosDesteRecibo(List<MovPagamentoVO> pagamentosDesteRecibo) {
        this.pagamentosDesteRecibo = pagamentosDesteRecibo;
    }

    public Boolean getEmitirNFSeAutomatico() {
        return emitirNFSeAutomatico;
    }

    public void setEmitirNFSeAutomatico(Boolean emitirNFSeAutomatico) {
        this.emitirNFSeAutomatico = emitirNFSeAutomatico;
    }

    public Boolean getNfseEmitida() {
        return nfseEmitida;
    }

    public void setNfseEmitida(Boolean nfseEmitida) {
        this.nfseEmitida = nfseEmitida;
    }

    public String getOnclickNFSe() throws Exception {
        if (getFacade().getNFSeEmitida().consultarPorRecibo(getCodigo()) != null) {
            setNfseEmitida(true);
        }
        return getNfseEmitida() ? "alert('A NFSe deste recibo já foi enviada. Caso deseje enviar novamente, é necessário desvincular o recibo da NFSE que já foi enviada.');return false;"
                : "if(!confirm('Deseja enviar a nota fiscal eletrônica de serviço?')) {return false;}";
    }
    
    public void montarLogReciboPagamento(LogVO obj) throws Exception {
        String pessoaPagador = "NULL";
        String contrato = "NULL";
        obj.setChavePrimaria(getCodigo().toString());
        obj.setNomeEntidade("RECIBOPAGAMENTO");
        obj.setNomeEntidadeDescricao("Recibo Pagamento");
        obj.setOperacao("INCLUSAO - RECIBO PAGAMENTO");
        obj.setResponsavelAlteracao(getResponsavelLancamento().getNome());
        obj.setUserOAMD(getResponsavelLancamento().getUserOamd());
        obj.setNomeCampo("TODOS");
        if (getPessoaPagador() != null && getPessoaPagador().getCodigo() != 0) {
            pessoaPagador = getPessoaPagador().getCodigo().toString();
        }
        if (getContrato() != null && getContrato().getCodigo() != 0) {
            contrato = getContrato().getCodigo().toString();
        }
        obj.setValorCampoAlterado("--------------------------------------\n\r");
        obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "Empresa = " + getEmpresa_Apresentar() + "\n\rcódigo do recibo = " + getCodigo() + "\n\r" + "data entrada no caixa = " + getData_Apresentar() + "\n\r" + "valor Total =  " + empresa.getMoeda() + " " + Uteis.getDoubleFormatado(getValorTotal()) + "\n\r" + "nome do Cliente = " + getNomePessoaPagador() + "\n\r" + "codigo do cliente = " + pessoaPagador + "\n\r" + "responsável lançamento = " + getResponsavelLancamento().getNome() + "\n\r" + "contrato= " + contrato + "\n\r");
        obj.setValorCampoAnterior("");
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
    }

    public String getNomeResponsavelLegal() {
        if (nomeResponsavelLegal == null) {
            nomeResponsavelLegal = "";
        }
        return nomeResponsavelLegal;
    }

    public void setNomeResponsavelLegal(String nomeResponsavelLegal) {
        this.nomeResponsavelLegal = nomeResponsavelLegal;
    }

    public String getCpfResponsavelLegal() {
        if (cpfResponsavelLegal == null) {
            cpfResponsavelLegal = "";
        }
        return cpfResponsavelLegal;
    }

    public void setCpfResponsavelLegal(String cpfResponsavelLegal) {
        this.cpfResponsavelLegal = cpfResponsavelLegal;
    }
}

package negocio.comuns.financeiro;

import java.util.Date;
import java.util.List;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.ConsistirException;
import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.ecf.cupomfiscal.comuns.to.CupomFiscalTO;
import negocio.comuns.basico.ClienteVO;

/**
 * Classe de modelo para a tabela cupomfiscal
 * 
 * <AUTHOR>
 */
/**
 * <AUTHOR>
 *
 */
    public class CupomFiscalVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Date dataHoraVenda;
    protected Date dataHoraEmissao;
    protected Date dataPagamento;
    protected StatusImpressaoEnum statusImpressao;
    protected Integer co_cupom;
    protected Double valorTotal;
    protected int local;
    @ChaveEstrangeira
    protected ReciboPagamentoVO recibo = new ReciboPagamentoVO();
    @ChaveEstrangeira
    protected MovPagamentoVO pagamento = new MovPagamentoVO();
    @ChaveEstrangeira
    protected CartaoCreditoVO cartao;
    @ChaveEstrangeira
    protected ChequeVO cheque;
    @ChaveEstrangeira
    protected PessoaVO pessoaVO = new PessoaVO();
    @ChaveEstrangeira
    protected UsuarioVO responsavel;
    private ClienteVO clienteVO = new ClienteVO();
    private String matricula;
    private List<CupomFiscalFormasPagamentoVO> formaPagamentoVOs;
    private List<CupomFiscalItensVO> itensVOs;

    /**
     * Construtor padrão da classe <code>CupomFiscalVO</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente
     * seus atributos (Classe VO).
     */
    public CupomFiscalVO() {
        super();
    }

    public int getCupom_Apresentar() {
        return getRecibo().getCodigo();
    }

    public String getMatricula_Apresentar() {
        return getClienteVO().getMatricula();
    }

    public String getPessoa_Apresentar() {
        return getPessoaVO().getNome();
    }

    public String getSituacao_Apresentar() {
        return getStatusImpressao().getDescricao();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>CupomFiscalVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas
     * neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação
     * de valores válidos para os atributos.
     *
     * @exception ConsistirExecption
     *                Se uma inconsistência for encontrada aumaticamente é
     *                gerada uma exceção descrevendo
     *                o atributo e o erro ocorrido.
     */
    public static void validarDados(CupomFiscalVO obj) throws ConsistirException {
        if (obj.getValorTotal().doubleValue() < 0) {
            throw new ConsistirException("O campo VALOR TOTAL (Cupom Fiscal) deve ser informado.");
        }

    }

    //////////
    //Getters and Setters
    //////////
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataHoraVenda() {
        return dataHoraVenda;
    }

    public String getDataHoraVendaFormatada() {
        return Formatador.formatarData(dataHoraVenda, "dd/MM/yyyy HH:mm:ss");
    }

    public void setDataHoraVenda(Date dataHoraVenda) {
        this.dataHoraVenda = dataHoraVenda;
    }

    public Date getDataHoraEmissao() {
        return dataHoraEmissao;
    }

    public String getDataHoraEmissaoFormatada() {
        return Formatador.formatarData(dataHoraEmissao, "dd/MM/yyyy HH:mm:ss");
    }

    public void setDataHoraEmissao(Date dataHoraEmissao) {
        this.dataHoraEmissao = dataHoraEmissao;
    }

    public StatusImpressaoEnum getStatusImpressao() {
        return statusImpressao;
    }

    public void setStatusImpressao(StatusImpressaoEnum statusImpressao) {
        this.statusImpressao = statusImpressao;
    }

    public Integer getCo_cupom() {
        return co_cupom;
    }

    public void setCo_cupom(Integer coCupom) {
        co_cupom = coCupom;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        //TODO codigo para testes do ambiente de desv
        this.valorTotal = valorTotal;
    }

    public ReciboPagamentoVO getRecibo() {
        return recibo;
    }

    public void setRecibo(ReciboPagamentoVO recibo) {
        this.recibo = recibo;
    }

    public ChequeVO getCheque() {
        return cheque;
    }

    public void setCheque(ChequeVO cheque) {
        this.cheque = cheque;
    }

    public List<CupomFiscalFormasPagamentoVO> getFormaPagamentoVOs() {
        return formaPagamentoVOs;
    }

    public void setFormaPagamentoVOs(List<CupomFiscalFormasPagamentoVO> formaPagamentoVOs) {
        this.formaPagamentoVOs = formaPagamentoVOs;
    }

    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public int getLocal() {
        return local;
    }

    public void setLocal(int local) {
        this.local = local;
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }

    public List<CupomFiscalItensVO> getItensVOs() {
        return itensVOs;
    }

    public void setItensVOs(List<CupomFiscalItensVO> itensVOs) {
        this.itensVOs = itensVOs;
    }

    public CupomFiscalTO toTO() {
        CupomFiscalTO obj = new CupomFiscalTO();
        obj.setCheque(this.getCheque() == null ? 0 : this.getCheque().getCodigo());
        obj.setCo_cupom(this.getCo_cupom());
        obj.setCodigo(this.getCodigo());
//		obj.setFormasPagamento(this.getFormaPagamentoVOs())
        obj.setHoraEmissao(this.getDataHoraEmissao());
        obj.setHoraVenda(this.getDataHoraVenda());
//		obj.setItens(null);
        obj.setRecibo(this.getRecibo() == null ? 0 : this.getRecibo().getCodigo());
        obj.setPagamento(this.getPagamento() == null ? 0 : this.getPagamento().getCodigo());
        obj.setCartao(this.getCartao() == null ? 0 : this.getCartao().getCodigo());
        obj.setStatusImpressao(this.getStatusImpressao().getCodigo());
        obj.setValor(this.getValorTotal());
        obj.setCodCliente(this.getPessoaVO().getCodigo());
        obj.setNomeCliente(this.getPessoaVO().getNome());
        obj.setCpfCliente(this.getPessoaVO().getCfp());
        obj.setResponsavel(this.getResponsavel().getCodigo());
        obj.setDataPagamento(this.getDataPagamento());
        obj.setMatricula(this.getMatricula());
        return obj;
    }

    public String getValorTotalFormatado() {
        return Formatador.formatarValorMonetario(this.getValorTotal());
    }

    /**
     * @return the clienteVO
     */
    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    /**
     * @param clienteVO the clienteVO to set
     */
    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public MovPagamentoVO getPagamento() {
        return pagamento;
    }

    public void setPagamento(MovPagamentoVO pagamento) {
        this.pagamento = pagamento;
    }

    public CartaoCreditoVO getCartao() {
        return cartao;
    }

    public void setCartao(CartaoCreditoVO cartao) {
        this.cartao = cartao;
    }

    public String getMatricula() {
        if(matricula == null){
            return "";
        }
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }
}

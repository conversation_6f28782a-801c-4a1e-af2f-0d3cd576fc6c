package negocio.comuns.financeiro;

import org.json.JSONObject;

/**
 * Created by <PERSON> on 29/09/2023.
 */

public class PluggyTransactionPaymentDataDocumentNumberDTO {

    protected String type; //cpf ou cnpj
    protected String value;

    public PluggyTransactionPaymentDataDocumentNumberDTO() {
    }

    public PluggyTransactionPaymentDataDocumentNumberDTO(JSONObject json) throws Exception {
        this.type = json.optString("type", "");
        this.value = json.optString("value", "");
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}

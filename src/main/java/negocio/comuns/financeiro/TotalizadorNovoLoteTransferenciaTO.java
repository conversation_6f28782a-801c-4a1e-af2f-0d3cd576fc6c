/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;

/**
 * <AUTHOR> 24/07/2024
 */
public class TotalizadorNovoLoteTransferenciaTO extends SuperTO {

    private static final long serialVersionUID = 60394873821639775L;
    private Integer quantidade = 0;
    private Double valor = 0.0;

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getValor_Apresentar() {
        return Formatador.formatarValorMonetario(valor);
    }

}

package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import br.com.pactosolucoes.comuns.util.Formatador;

public class ResumoFormaPagamentoRelatorio extends SuperTO {

    private boolean cheque = false;
    private boolean cartao = false;
    private boolean devolucao = false;
    private String formaPagamento = "";
    private double valor = 0.0;
    private boolean imprimir = true;
    private boolean exibirAutorizacao = false;
    private Date dia;
    private String tipoFormaPagamento = "";
    private boolean exibirNSU;

    public String getValorExcel() {
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.valor);
        if (this.valor < 0) {
            valor = "-" + valor;
        }
        return valor;
    }

    public String getAno() {
        return Integer.valueOf(Uteis.getAnoData(dia)).toString();
    }

    public String getDiaMes() {
        return Integer.valueOf(Uteis.getDiaMesData(dia)).toString();
    }

    public String getMes() {
        return Integer.valueOf(Uteis.getMesData(dia)).toString();
    }

    public ResumoFormaPagamentoRelatorio getCopiaLimpa() {
        ResumoFormaPagamentoRelatorio rfpf = new ResumoFormaPagamentoRelatorio();
        rfpf.setFormaPagamento(this.formaPagamento);
        rfpf.setTipoFormaPagamento(this.tipoFormaPagamento);
        rfpf.setDevolucao(devolucao);
        return rfpf;
    }

    public boolean getOutro() {
        return !cheque && !cartao && !devolucao;
    }

    private List<ChequeTO> cheques = new ArrayList<ChequeTO>();
    private List<CartaoCreditoTO> cartoes = new ArrayList<CartaoCreditoTO>();
    private List<MovPagamentoVO> pagamentos = new ArrayList<MovPagamentoVO>();
    private List<MovContaVO> listaDevolucoes = new ArrayList<MovContaVO>();

    public List<MovContaVO> getListaDevolucoes() {
        return listaDevolucoes;
    }

    public void setListaDevolucoes(List<MovContaVO> listaDevolucoes) {
        this.listaDevolucoes = listaDevolucoes;
    }


    public JRDataSource getChequesJR() {
        return new JRBeanCollectionDataSource(cheques);

    }

    public JRDataSource getDevolucoesJR() {
        return new JRBeanCollectionDataSource(listaDevolucoes);

    }

    public JRDataSource getCartoesJR() {
        return new JRBeanCollectionDataSource(getCartoes());

    }

    public JRDataSource getPagamentosJR() {
        return new JRBeanCollectionDataSource(getPagamentos());
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public double getValor() {
        return valor;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public String getFormaPagamento() {
        return formaPagamento;
    }

    public void setCheque(boolean cheque) {
        this.cheque = cheque;
    }

    public boolean getCheque() {
        return cheque;
    }

    public void setCartao(boolean cartao) {
        this.cartao = cartao;
    }

    public boolean getCartao() {
        return cartao;
    }

    public void setCheques(List<ChequeTO> cheques) {
        this.cheques = cheques;
    }

    public List<ChequeTO> getCheques() {
        return cheques;
    }

    public void setCartoes(List<CartaoCreditoTO> cartoes) {
        this.cartoes = cartoes;
    }

    public List<CartaoCreditoTO> getCartoes() {
        return cartoes;
    }

    public void setPagamentos(List<MovPagamentoVO> pagamentos) {
        this.pagamentos = pagamentos;
    }

    public List<MovPagamentoVO> getPagamentos() {
        return pagamentos;
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valor);
    }

    public void setImprimir(boolean imprimir) {
        this.imprimir = imprimir;
    }

    public boolean getImprimir() {
        return imprimir && !(getCheques().isEmpty() && getCartoes().isEmpty() && getPagamentos().isEmpty() && getListaDevolucoes().isEmpty());
    }

    public boolean getDisabled() {
        return getCheques().isEmpty() && getCartoes().isEmpty() && getPagamentos().isEmpty()&& getListaDevolucoes().isEmpty();
    }

    public void setExibirAutorizacao(boolean exibirAutorizacao) {
        this.exibirAutorizacao = exibirAutorizacao;
    }

    public boolean isExibirAutorizacao() {
        return exibirAutorizacao;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Date getDia() {
        return dia;
    }

    public void setTipoFormaPagamento(String tipoFormaPagamento) {
        this.tipoFormaPagamento = tipoFormaPagamento;
    }

    public String getTipoFormaPagamento() {
        return tipoFormaPagamento;
    }

    public boolean getDevolucao() {
        return devolucao;
    }

    public void setDevolucao(boolean devolucoes) {
        this.devolucao = devolucoes;
    }

    public void setExibirNSU(boolean exibirNSU) {
        this.exibirNSU = exibirNSU;
    }

    public boolean isExibirNSU() {
        return exibirNSU;
    }
}


package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class LoteVO extends SuperVO {
    @ChavePrimaria
    private Integer codigo = 0;
    private EmpresaVO empresa;
    private String descricao = "";
    private UsuarioVO usuarioResponsavel = null;
    private Date dataLancamento = null;
    private Date dataDeposito = Calendario.hoje();
    private List<ChequeVO> cheques = null;
    private List<CartaoCreditoVO> cartoes = null;
    private double valor = 0.0;
    private boolean depositado = false;
    @NaoControlarLogAlteracao
    private TipoOperacaoLancamento tipoOperacao = null;
    
    private List<CartaoCreditoTO> cartoesTO = new ArrayList<CartaoCreditoTO>();
    private List<ChequeTO> chequesTO = new ArrayList<ChequeTO>();
    
    private int pagaMovConta = 0; // Atributo foi substituido pelo campo "lotePagouConta" da tabela movConta. Motivo: Um lote pode ser usado para pagar mais de uma conta.
    @NaoControlarLogAlteracao
    private int codigoContaContido = 0; 
    
    @NaoControlarLogAlteracao
    private String conta = "";

    @NaoControlarLogAlteracao
    private String contaLote;
    
    private boolean avulso = false;

    public void validarDados() throws Exception {
        validarDadosSimples();
        if((getCheques() == null || getCheques().isEmpty()) &&
           (getCartoes() == null || getCartoes().isEmpty())) {
            boolean alteracao = false;
            LoteVO objAntes = (LoteVO) getObjetoVOAntesAlteracao();
            if(!Calendario.igual(objAntes.getDataLancamento(), this.getDataLancamento())){
                alteracao = true;
            }
            if(!Calendario.igual(objAntes.getDataDeposito(), this.getDataDeposito())){
                alteracao = true;
            }

            if(!alteracao)
            throw new Exception("Nenhum item foi selecionado.");
        }
    }
    public LoteVO(){
    }
    public LoteVO(Integer codigo){
    	setCodigo(codigo);
    }

    public void validarDadosSimples() throws Exception {
        if(getEmpresa() == null || getEmpresa().getCodigo() == 0)
            throw new Exception("Informe uma empresa para o Lote.");
        if(descricao.trim().isEmpty())
            throw new Exception("Informe uma descrição para o lote.");
        if(usuarioResponsavel == null || usuarioResponsavel.getCodigo().intValue() == 0)
            throw new Exception("Responsável pelo Lote não encontrado.");
        if(dataLancamento == null)
            throw new Exception("Data de Lançamento do Lote não foi encontrada.");
        if(dataDeposito == null)
            throw new Exception("Data de Depósito do Lote não foi encontrada.");
    }

    public void realizarUpperCaseDados() {
        setDescricao(descricao.toUpperCase());
    }

    public void adicionarCheque(ChequeVO ch) {
        cheques.add(ch);
        valor = Uteis.arredondarForcando2CasasDecimais(valor + ch.getValor());
    }

    public void adicionarCartao(CartaoCreditoVO cc) {
        cartoes.add(cc);
        valor = Uteis.arredondarForcando2CasasDecimais(valor + cc.getValor());
    }

    public void removeCheque(ChequeVO ch) {
        cheques.remove(ch);
        valor = Uteis.arredondarForcando2CasasDecimais(valor - ch.getValor());
    }

    public void removeCartao(CartaoCreditoVO cc) {
        cartoes.remove(cc);
        valor = Uteis.arredondarForcando2CasasDecimais(valor - cc.getValor());
    }

    @Override
    public Integer getCodigo() {
        return this.codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getDescLoteUsadoParaPagar_apresentar(){
        return this.codigo + "(" + this.descricao + ")";
    }

    public String getDescricaoLower() {
        return descricao == null ? "" : descricao.toLowerCase();
    }

    public String getUsuarioLower() {
        return getUsuarioResponsavel() == null ? "" :
                getUsuarioResponsavel().getNome() == null ? "" :
                        getUsuarioResponsavel().getNomeAbreviado().toLowerCase();
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataDeposito() {
        return dataDeposito;
    }

    public void setDataDeposito(Date dataDeposito) {
        this.dataDeposito = dataDeposito;
    }

    public List<ChequeVO> getCheques() {
        return cheques;
    }

    public void setCheques(List<ChequeVO> cheques) {
        this.cheques = cheques;
    }

    public List<CartaoCreditoVO> getCartoes() {
        return cartoes;
    }

    public void setCartoes(List<CartaoCreditoVO> cartoes) {
        this.cartoes = cartoes;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof LoteVO) {
            LoteVO aux = (LoteVO) obj;
            return this.codigo.intValue() == aux.getCodigo().intValue();
        }
        return false;
    }

	public void setCartoesTO(List<CartaoCreditoTO> cartoesTO) {
		this.cartoesTO = cartoesTO;
	}

	public List<CartaoCreditoTO> getCartoesTO() {
		if(cartoesTO == null){
			cartoesTO = new ArrayList<CartaoCreditoTO>();
		}
		return cartoesTO;
	}

	public void setChequesTO(List<ChequeTO> chequesTO) {
		this.chequesTO = chequesTO;
	}

	public List<ChequeTO> getChequesTO() {
		if(chequesTO == null){
			chequesTO = new ArrayList<ChequeTO>();
		}
		return chequesTO;
	}

	public void setDepositado(boolean depositado) {
		this.depositado = depositado;
	}

	public boolean getDepositado() {
		return depositado;
	}

	public void setConta(String conta) {
		this.conta = conta;
	}

	public String getConta() {
		return conta;
	}

	public String getContaLower() {
		return conta == null ? "" : conta.toLowerCase();
	}
	
	public String getDataLancamento_Apresentar() {
		if(dataLancamento == null){
    		return "";
    	}
		return Uteis.getData(dataLancamento);
	}
	
    public String getValor_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValor());
    }

	
	public TipoFormaPagto getTipoFormaPagto(){
		return (cartoes != null && !cartoes.isEmpty()) ? TipoFormaPagto.CARTAOCREDITO : TipoFormaPagto.CHEQUE;
			
	}

	public void setPagaMovConta(int pagaMovConta) {
		this.pagaMovConta = pagaMovConta;
	}

	public int getPagaMovConta() {
		return pagaMovConta;
	}

	public void setTipoOperacao(TipoOperacaoLancamento tipoOperacao) {
		this.tipoOperacao = tipoOperacao;
	}

	public TipoOperacaoLancamento getTipoOperacao() {
		return tipoOperacao;
	}

	public void setAvulso(boolean avulso) {
		this.avulso = avulso;
	}

	public boolean getAvulso() {
		return avulso;
	}
	public void setCodigoContaContido(int codigoContaContido) {
		this.codigoContaContido = codigoContaContido;
	}
	public int getCodigoContaContido() {
		return codigoContaContido;
	}

    public String getContaLote() {
        return contaLote;
    }

    public void setContaLote(String contaLote) {
        this.contaLote = contaLote;
    }
}

package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;

public class RecCliConsultorMovProdTO extends SuperTO {

    private Integer codCliente;
    private String nomeCliente;
    private Integer codConsultorEpoca;
    private Double valor = 0.0;

    public Integer getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(Integer codCliente) {
        this.codCliente = codCliente;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public Integer getCodConsultorEpoca() {
        return codConsultorEpoca;
    }

    public void setCodConsultorEpoca(Integer codConsultorEpoca) {
        this.codConsultorEpoca = codConsultorEpoca;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

}

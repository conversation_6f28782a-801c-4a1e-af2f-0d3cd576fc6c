package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 20/07/2021
 */
public enum SituacaoTransactionPluggyEnum {

    NENHUMA(0, ""),
    NAO_CONCILIADO(1, "Não Conciliado"),
    CONCILIADO(2, "Conciliado"),
    GRAVAR_PENDENTE(3, "Pendente");
    private Integer codigo;
    private String descricao;

    SituacaoTransactionPluggyEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static SituacaoTransactionPluggyEnum obterPorCodigo(Integer codigo) {
        for (SituacaoTransactionPluggyEnum origem : SituacaoTransactionPluggyEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import negocio.comuns.utilitarias.Ordenacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 27/04/2020
 */
public enum TipoBloqueioCobrancaEnum {

    TODAS_PARCELAS(0, "Todas as parcelas","Bloqueio de todas cobranças automáticas"),
    PARCELAS_FUTURAS(1, "Vencimento maior que", "Bloqueio das parcelas com vencimento superior a data selecionada."),
    ;

    private Integer codigo;
    private String descricao;
    private String title;

    TipoBloqueioCobrancaEnum(Integer codigo, String descricao, String title) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.title = title;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getTitle() {
        return title;
    }

    public static TipoBloqueioCobrancaEnum obterTipoBloqueioCobrancaEnum(Integer codigo) {
        for (TipoBloqueioCobrancaEnum situacao : TipoBloqueioCobrancaEnum.values()) {
            if (situacao.getCodigo().equals(codigo)) {
                return situacao;
            }
        }
        return TODAS_PARCELAS;
    }

    public static List getSelectListTipoBloqueioCobrancaEnum() {
        List<SelectItem> temp = new ArrayList<SelectItem>();
        for (TipoBloqueioCobrancaEnum tipo : TipoBloqueioCobrancaEnum.values()) {
            temp.add(new SelectItem(tipo, tipo.getDescricao().toUpperCase()));
        }
        Ordenacao.ordenarLista(temp, "label");
        temp.add(0, new SelectItem(null, ""));
        return temp;
    }
}

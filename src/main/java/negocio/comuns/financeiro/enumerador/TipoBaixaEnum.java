/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

public enum TipoBaixaEnum {

    NAO_BAIXADO(0, "Não Baixado"),
    DUPLICIDADE(1, "Baixa em Duplicidade"),
    MENOR_VALOR(2, "Baixa com Valor Menor"),
    MAIOR_VALOR(3, "Baixa com Valor Maior"),
    NORMAL(4, "Baixa Normal"),
    MANUAL(5, "Baixa Manual");
    private int id;
    private String descricao;

    TipoBaixaEnum(int id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public static TipoBaixaEnum getTipoRemessaEnum(final int codigo) {
        for (TipoBaixaEnum tipo : TipoBaixaEnum.values()) {
            if (tipo.getId() == codigo) {
                return tipo;
            }
        }
        return TipoBaixaEnum.NAO_BAIXADO;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

}

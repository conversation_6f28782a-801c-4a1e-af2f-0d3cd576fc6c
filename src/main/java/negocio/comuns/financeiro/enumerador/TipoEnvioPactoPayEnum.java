package negocio.comuns.financeiro.enumerador;

public enum TipoEnvioPactoPayEnum {

    NENHUM(0, "NENHUM"),
    CARTAO_A_VENCER(1, "CARTAO_A_VENCER"),
    PARCELA_PENDENTE(2, "PARCELA_PENDENTE"),
    COBRANCA_ANTECIPADA(3, "COBRANCA_ANTECIPADA"),
    RESULTADO_COBRANCA(4, "RESULTADO_COBRANCA"),
    CARTAO_VENCIDO(5, "CARTAO_VENCIDO"),
    ;

    private int id;
    private String identificador;

    TipoEnvioPactoPayEnum(int id, String identificador) {
        this.id = id;
        this.identificador = identificador;
    }

    public int getId() {
        return id;
    }

    public String getIdentificador() {
        return identificador;
    }

    public static TipoEnvioPactoPayEnum obterPorId(int id) {
        for (TipoEnvioPactoPayEnum tipo : values()) {
            if (tipo.getId() == id) {
                return tipo;
            }
        }
        return null;
    }
}

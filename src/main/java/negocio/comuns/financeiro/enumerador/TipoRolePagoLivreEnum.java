package negocio.comuns.financeiro.enumerador;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;

/**
 * <AUTHOR> 14/09/2021;
 */
public enum TipoRolePagoLivreEnum {

    NENHUM(0, "nenhum"),
    MERCHANT_OPERATOR(1, "merchantOperator"),
    MERCHANT_FINANCIAL(2, "merchantFinancial");

    private Integer codigo;
    private String descricao;

    private TipoRolePagoLivreEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public static TipoRolePagoLivreEnum obterPorDescricao(String descricao) {
        for (TipoRolePagoLivreEnum tipo : TipoRolePagoLivreEnum.values()) {
            if (tipo.getDescricao().equalsIgnoreCase(descricao)) {
                return tipo;
            }
        }
        return NENHUM;
    }

}

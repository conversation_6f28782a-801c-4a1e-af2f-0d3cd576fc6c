/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro.enumerador;

/**
 * <AUTHOR> 14/03/2025
 */

public enum TipoVisualizacaoAgendamentoFinanceiro {
    INTERVALO_PARCELAS(1, "Intervalo de parcelas"),
    QUANTIDADE_PARCELAS_FIXAS(2, "Parcelas fixas"),
    ATE_DETERMINADO_VENCIMENTO(3, "Até determinado vencimento");

    TipoVisualizacaoAgendamentoFinanceiro(int codigo, String descricao) {
        setCodigo(codigo);
        setDescricao(descricao);
    }

    int codigo;
    String descricao;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoVisualizacaoAgendamentoFinanceiro getTipoVisualizacao(final int codigo) {
        TipoVisualizacaoAgendamentoFinanceiro tipoVisualizacao = null;
        for (TipoVisualizacaoAgendamentoFinanceiro tipo : TipoVisualizacaoAgendamentoFinanceiro.values()) {
            if (tipo.getCodigo() == codigo) {
                tipoVisualizacao = tipo;
                break;
            }
        }
        return tipoVisualizacao;
    }

}

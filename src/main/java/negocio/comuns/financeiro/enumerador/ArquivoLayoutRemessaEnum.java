package negocio.comuns.financeiro.enumerador;

public enum ArquivoLayoutRemessaEnum {
    NENHUM("", "NENHUM", "", ""),
    DCC("LAYOUT-REMESSA-DCC", "DCC/DCO", "", ""),
    CARNE("CARNE", "CARNÊ (3 POR PÁGINA) SANTANDER", "carneNovo", "boleto.jasper"),
    BOLETO_BB("BOLETO-BANCO-DO-BRASIL", "BOLETO MODELO BANCO DO BRASIL", "boletoNovo", "boletoBB.jasper"),
    BOLETO_ITAU("ITAU", "BOLETO MODELO ITAÚ", "boletoNovo", "boletoItau.jasper"),
    BOLETO_BRB("BRB", "BOLETO MODELO BRB", "boletoNovo", "boletoBRB.jasper"),
    BOLETO_CLUBE("CLUBE", "BOLETO MODELO CLUBE", "boletoNovoClube", "boleto.jasper"),
    BOLETO_PADRAO("BOLETO", "B<PERSON>ET<PERSON> PADRÃO", "boletoNovo", "boleto.jasper"),
    CARNE_BRADESCO("CARNE-BRADESCO", "CARNÊ (3 POR PÁGINA) BRADESCO", "carneNovoBradesco", "boleto.jasper"),
    CARNE_BANCO_DO_BRASIL("CARNE-BANCO-DO-BRASIL", "CARNÊ (3 POR PÁGINA) BANCO DO BRASIL", "carneNovoBB", "boleto.jasper"),
    CARNE_BANCO_DO_NORDESTE("CARNE-BANCO-DO-NORDESTE", "CARNÊ (3 POR PÁGINA) BANCO DO NORDESTE", "carneNovoBNB", "carneBNB.jasper"),
    BOLETO_CAIXA("BOLETO-CAIXA", "BOLETO MODELO CAIXA", "boletoNovo", "boletoCaixa.jasper"),
    BOLETO_SICOOB_240("BOLETO-SICOOB-240", "BOLETO MODELO SICOOB 240", "boletoNovo", "boleto.jasper"),
    BOLETO_SICOOB_240_CARNE("BOLETO-CARNE-SICOOB-240", "CARNÊ (3 POR PÁGINA) SICOOB 240", "carneNovoBradesco", "boleto.jasper"),
    BOLETO_DAYCOVAL("BOLETO_DAYCOVAL", "BOLETO_DAYCOVAL", "boletoNovo", "boletoDaycoval.jasper"),
    BOLETO_SICREDI("BOLETO-SICREDI", "BOLETO SICREDI", "carneSicredi", "boletoSicredi.jasper"),
    CARNE_BANCO_SICREDI("CARNE-BANCO-SICREDI", "CARNE BANCO SICREDI", "carneNovoBaseSicredi", "carneSicredi3unid.jasper"),
    BOLETO_SANTANDER("BOLETO_SANTANDER", "BOLETO SANTANDER", "boletoNovo", "boletoSantander.jasper"),
    BOLETO_SAFRA("BOLETO-SAFRA", "BOLETO SAFRA", "boletoNovo", "boletoSafra.jasper"),
    ;


    private String id;
    private String descricao;
    private String report;
    private String subReport;

    ArquivoLayoutRemessaEnum(String id, String descricao, String report, String subReport) {
        this.id = id;
        this.descricao = descricao;
        this.report = report;
        this.subReport = subReport;
    }

    public static ArquivoLayoutRemessaEnum getFromId(String id) {
        for (ArquivoLayoutRemessaEnum arquivoLayoutEnum : ArquivoLayoutRemessaEnum.values()) {
            if (arquivoLayoutEnum.getId().equals(id)) {
                return arquivoLayoutEnum;
            }
        }
        return ArquivoLayoutRemessaEnum.NENHUM;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getReport() {
        return report;
    }

    public void setReport(String report) {
        this.report = report;
    }

    public String getSubReport() {
        return subReport;
    }

    public void setSubReport(String subReport) {
        this.subReport = subReport;
    }
}

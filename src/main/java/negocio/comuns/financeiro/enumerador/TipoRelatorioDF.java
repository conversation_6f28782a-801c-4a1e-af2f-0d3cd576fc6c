/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro.enumerador;

/**
 * <AUTHOR>
 *         Define o tipo de Relatório que será gerado no Demonstrativo Financeiro
 */
public enum TipoRelatorioDF {
    RECEITA(1, "Por Receita/Despesa", "Avalia os pagamentos do ADM por data de compensação. No FI, as contas são avaliadas por data de quitação."),  // relatório por receita visa os pagamentos realizados
    COMPETENCIA(2, "Por Competência", "Avalia os produtos e contas por data de competência (mês/ano), independentemente se foram pagos/quitadas ou não."), // relatório por competência visa as vendas realizadas. Não necessariamente precisam estarem pagas.
    FATURAMENTO_DE_CAIXA(3, "Por Faturamento Recebido", "Avalia os pagamentos do ADM por data de lançamento. No FI, as contas são avaliadas por data de quitação e data de lançamento no período informado."),
    FATURAMENTO(4, "Por Faturamento", "Avalia os produtos e contas por data de lançamento, independentemente se foram pagos/quitadas ou não"),
    COMPETENCIA_QUITADA(5, "Por Competência Quitada", "Avalia apenas os produtos pagos e contas quitadas por data de competência (mês/ano)."),
    COMPETENCIA_NAO_QUITADA(6, "Por Competência Não Quitada", "Avalia apenas os produtos não pagos e contas não quitadas por data de competência (mês/ano)."),
    RECEITAPROVISAO(7,"Por Receita/Despesa Provisão","Avalia os pagamentos do ADM por data de compensação. No FI, as contas são avaliadas por data de vencimento,independentemente se foram quitadas ou não."),
    COMPETENCIA_INDEPENDENTE_QUITACAO(8, "Por Competência Independente da Quitação", "Avalia os produtos e contas por data de competência (mês/ano), independentemente se foram pagos/quitadas ou não.");

    int codigo;
    String descricao;
    private String descricaoAuxiliar;

    TipoRelatorioDF(int codigo, String descricao, String descricaoAuxiliar) {
        setCodigo(codigo);
        setDescricao(descricao);
        setDescricaoAuxiliar(descricaoAuxiliar);
    }

    public static TipoRelatorioDF getTipoRelatorioDF(final int codigo) {
        TipoRelatorioDF tipoRel = null;
        for (TipoRelatorioDF tipo : TipoRelatorioDF.values()) {
            if (tipo.getCodigo() == codigo) {
                tipoRel = tipo;
                break;
            }
        }
        return tipoRel;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }


    public String getDescricaoAuxiliar() {
        return descricaoAuxiliar;
    }

    public void setDescricaoAuxiliar(String descricaoAuxiliar) {
        this.descricaoAuxiliar = descricaoAuxiliar;
    }
    
    public String getDescricaoCompleta() {
        return descricao + ": " + descricaoAuxiliar;
    }
}


/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

/*
 * <AUTHOR>
 */
public enum SituacaoNFCeEnum {

    ENVIADA(1, "Enviada", "#00008B", "Nota enviada, entre no módulo NFC-e para verificar a situação da nota.", ""),
    NAO_ENVIADA(2, "Não enviada", "#fff212", "Nota não enviada", "");

    private int id;
    private String descricao;
    private String cor;
    private String hint;
    private String classe;

    private SituacaoNFCeEnum(int id, String descricao, String cor, String hint, String classe) {
        this.id = id;
        this.descricao = descricao;
        this.cor = cor;
        this.hint = hint;
        this.classe = classe;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getClasse() {
        return classe;
    }
}

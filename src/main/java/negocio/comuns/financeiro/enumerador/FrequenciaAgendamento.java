
package negocio.comuns.financeiro.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum FrequenciaAgendamento {
    SEMANAL(1, "Semanal", 7, 12, 63),
    MENSAL(2, "Mensal", 30, 12, 90),
    SEMESTRAL(3, "Semestral", 180, 2, 90),
    ANUAL(4, "Anual", 360, 2, 270);

    private int codigo; // Codigo que será gravado no banco de dados.
    private String descricao; // Descrição da frequencia que será apresentada na tela.
    private int qtdeDias; // Quantidade de dias que representa a frequencia.
    private int qtdeParcelasGerar; // Quantidade de parcelas que o sistema irá gerar a cada nova geração.
    private int qtdeDiasNovaGeracao; // Quantidade de dias que faltam em relação ao ultimo vencimento, para gerar as novas parcelas.

    FrequenciaAgendamento(int codigo, String descricao, int qtdeDias, int qtdeParcelasGerar, int qtdeDiasNovaGeracao) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.qtdeDias = qtdeDias;
        this.qtdeParcelasGerar = qtdeParcelasGerar;
        this.qtdeDiasNovaGeracao = qtdeDiasNovaGeracao;
    }

    public static FrequenciaAgendamento getFrequencia(int codigo) {
        for(FrequenciaAgendamento fa : values()) {
            if(fa.getCodigo() == codigo)
                return fa;
        }
        return FrequenciaAgendamento.MENSAL;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getQtdeDias() {
        return qtdeDias;
    }

    public void setQtdeDias(int qtdeDias) {
        this.qtdeDias = qtdeDias;
    }

    public int getQtdeParcelasGerar() {
        return qtdeParcelasGerar;
    }

    public void setQtdeParcelasGerar(int qtdeParcelasGerar) {
        this.qtdeParcelasGerar = qtdeParcelasGerar;
    }

    public int getQtdeDiasNovaGeracao() {
        return qtdeDiasNovaGeracao;
    }

    public void setQtdeDiasNovaGeracao(int qtdeDiasNovaGeracao) {
        this.qtdeDiasNovaGeracao = qtdeDiasNovaGeracao;
    }
}

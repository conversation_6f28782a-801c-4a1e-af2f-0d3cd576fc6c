/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import negocio.comuns.utilitarias.Ordenacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum VanExtratoEnum {

    OUTRO(0, "OUTRO"),
    NEXXERA(1, "NEXXERA"),
    TIVIT(2, "TIVIT"),
    CIELO(3, "CIELO");

    private Integer codigo;
    private String descricao;

    private VanExtratoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static List<SelectItem> getListaSelectItem(){
        List<SelectItem> lista = new ArrayList<SelectItem>();
        for(VanExtratoEnum tipo : values()){
            lista.add(new SelectItem(tipo, tipo.getDescricao()));
        }
        Ordenacao.ordenarLista(lista, "label");
        return lista;
    }

    public static VanExtratoEnum obterPorCodigo(final Integer codigo) {
        if (codigo == null) {
            return VanExtratoEnum.OUTRO;
        }

        for (VanExtratoEnum sit : VanExtratoEnum.values()) {
            if (sit.getCodigo().equals(codigo)) {
                return sit;
            }
        }
        return VanExtratoEnum.OUTRO;
    }
}

package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 09/07/2024
 * <p>
 */
public enum RegistrationStatusKobanaEnum {
    NENHUM(0, "Nenhum"),
    PENDING(1, "Pendente"),
    REQUESTED(2, "Requisitado"),
    CONFIRMED(3, "Confirmado"),
    REJECTED(4, "Rejeitado"),
    FAILED(5, "Falha"),
    PAC_ERROR(6, "PAC - Erro"),
    RECUSADO(7, "Recusado"),
    ;

    private final Integer codigo;
    private final String descricao;

    RegistrationStatusKobanaEnum(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static RegistrationStatusKobanaEnum obterPorCodigo(Integer codigo) {
        for (RegistrationStatusKobanaEnum status : values()) {
            if (status.getCodigo().equals(codigo)) {
                return status;
            }
        }
        return RegistrationStatusKobanaEnum.NENHUM;
    }

    public static RegistrationStatusKobanaEnum obterPorValue(String value) {
        for (RegistrationStatusKobanaEnum obj : RegistrationStatusKobanaEnum.values()) {
            if (obj.name().equals(value.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public static RegistrationStatusKobanaEnum obterPorDescricao(String descricao) {
        for (RegistrationStatusKobanaEnum obj : RegistrationStatusKobanaEnum.values()) {
            if (obj.getDescricao().toUpperCase().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import br.com.pactosolucoes.integracao.pactopay.StatusPactoPayEnum;

/**
 *
 * <AUTHOR>
 */
public enum SituacaoRemessaEnum {

    NENHUMA(0, "Nenhuma", "", "", "", StatusPactoPayEnum.NENHUM),//vazio
    GERADA(1, "Gerada", "#00008B"/*azul escuro*/, "Remessa Gerada, precisa ser enviada.", "", StatusPactoPayEnum.GERADA),
    RETORNO_PROCESSADO(2, "Retorno Processado", "#008800"/*verde*/, "Retorno processado. Veja Resultado", "", StatusPactoPayEnum.PROCESSADO),
    ERRO_RETORNO(3, "Erro Retorno", "#FFD42E", "Houve algum erro de validação do arquivo de Retorno.", "", StatusPactoPayEnum.ERRO_RETORNO),
    REMESSA_ENVIADA(4, "Remessa Enviada", "#777777", "Remessa Enviada pelo Processo Automático. Aguarde Retorno Automático.", "", StatusPactoPayEnum.ENVIADA),
    REMESSA_PADRAO(0, "Remessa Padrão", "", "Remessa padrão para cobrança.", "remessa-padrao", StatusPactoPayEnum.NENHUM),
    REMESSA_CANCELAMENTO(0, "Remessa Cancelamento", "", "Remessa de cancelamento de cobrança.", "remessa-cancelamento", StatusPactoPayEnum.CANCELADA);
    private int id;
    private String descricao;
    private String cor;
    private String hint;
    private String classe;
    private StatusPactoPayEnum statusPactoPayEnum;

    private SituacaoRemessaEnum(int id, String descricao, String cor, String hint, String classe, StatusPactoPayEnum statusPactoPayEnum) {
        this.id = id;
        this.descricao = descricao;
        this.cor = cor;
        this.hint = hint;
        this.classe = classe;
        this.statusPactoPayEnum = statusPactoPayEnum;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public static SituacaoRemessaEnum valueOf(final int id) {
        SituacaoRemessaEnum[] values = SituacaoRemessaEnum.values();
        for (SituacaoRemessaEnum sit : values) {
            if (sit.id == id) {
                if (sit.equals(SituacaoRemessaEnum.REMESSA_PADRAO) || sit.equals(SituacaoRemessaEnum.REMESSA_CANCELAMENTO)) {
                    return SituacaoRemessaEnum.NENHUMA;
                }
                return sit;
            }
        }
        return SituacaoRemessaEnum.NENHUMA;
    }

    public String getClasse() {
        return classe;
    }

    public StatusPactoPayEnum getStatusPactoPayEnum() {
        return statusPactoPayEnum;
    }
}

package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 09/02/2022
 */
public enum AtributoTransacaoEnum {

    outras_informacoes_anterior,
    convenio_nome,
    codigoAutenticacao01,
    codigoAutenticacao02,
    codigoAutenticacao03,
    codigoAutenticacao04,
    codigoAutenticacao05,
    codigoAutenticacao06,
    codigoAutenticacao07,
    msgErro,
    mensagemErro,
    codigoRetornoPacto,
    erroGenericoTransacao,
    cartaoMascarado,
    cartaoTitular,
    cartaoBandeira,
    numeroParcelas,
    cartaoCvv,
    adquirente,
    identificadorPacto,
    ipCliente,
    presencial,
    ;
}

package negocio.comuns.financeiro.enumerador;

/**
 * <AUTHOR>
 */
public enum RegrasRetentativaPorBandeiraEnum {

    NENHUMA(0, 0, 0, 0, 0),
    ELO(1, 16, 0, 0, 15),
    HIPERCARD(2, 30, 0, 0, 0),
    MASTERCARD(3, 30, 35, 7, 0),
    VISA(4, 30, 15, 0, 0),
    ;

    private int id;
    private int qtdDiasPeriodoValidar;
    private int qtdTentativaLimite30Dias;
    private int qtdTentativaLimite24Horas;
    private int qtdTentativaLimiteMesVigente;

    RegrasRetentativaPorBandeiraEnum(int id, int qtdDiasPeriodoValidar, int qtdTentativaLimite30Dias, int qtdTentativaLimite24Horas, int qtdTentativaLimiteMesVigente) {
        this.id = id;
        this.qtdDiasPeriodoValidar = qtdDiasPeriodoValidar;
        this.qtdTentativaLimite24Horas = qtdTentativaLimite24Horas;
        this.qtdTentativaLimite30Dias = qtdTentativaLimite30Dias;
        this.qtdTentativaLimiteMesVigente = qtdTentativaLimiteMesVigente;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getQtdDiasPeriodoValidar() {
        return qtdDiasPeriodoValidar;
    }

    public void setQtdDiasPeriodoValidar(int qtdDiasPeriodoValidar) {
        this.qtdDiasPeriodoValidar = qtdDiasPeriodoValidar;
    }

    public static RegrasRetentativaPorBandeiraEnum obterPorCodigo(final int codigo) {
        for (RegrasRetentativaPorBandeiraEnum regra : RegrasRetentativaPorBandeiraEnum.values()) {
            if (regra.getId() == codigo) {
                return regra;
            }
        }
        return RegrasRetentativaPorBandeiraEnum.NENHUMA;
    }

    public static RegrasRetentativaPorBandeiraEnum obterPorDescricao(String descricao) {
        for (RegrasRetentativaPorBandeiraEnum obj : RegrasRetentativaPorBandeiraEnum.values()) {
            if (obj.name().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUMA;
    }

    public int getQtdTentativaLimite24Horas() {
        return qtdTentativaLimite24Horas;
    }

    public void setQtdTentativaLimite24Horas(int qtdTentativaLimite24Horas) {
        this.qtdTentativaLimite24Horas = qtdTentativaLimite24Horas;
    }

    public int getQtdTentativaLimite30Dias() {
        return qtdTentativaLimite30Dias;
    }

    public void setQtdTentativaLimite30Dias(int qtdTentativaLimite30Dias) {
        this.qtdTentativaLimite30Dias = qtdTentativaLimite30Dias;
    }

    public int getQtdTentativaLimiteMesVigente() {
        return qtdTentativaLimiteMesVigente;
    }

    public void setQtdTentativaLimiteMesVigente(int qtdTentativaLimiteMesVigente) {
        this.qtdTentativaLimiteMesVigente = qtdTentativaLimiteMesVigente;
    }
}

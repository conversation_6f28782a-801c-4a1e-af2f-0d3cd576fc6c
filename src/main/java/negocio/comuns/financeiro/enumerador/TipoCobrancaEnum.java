package negocio.comuns.financeiro.enumerador;

public enum TipoCobrancaEnum {

    NENHUM(0, "NENHUM"),
    BOLETO(1, "BOLETO"),
    ONLINE(2, "ONLINE"),
    EDI_DCC(3, "EDI DCC"),
    EDI_DCO(4, "EDI DCO"),
    PIX(5, "PIX"),
    BOLETO_ONLINE(6, "BOLETO ONLINE"),
    PINPAD(7, "PINPAD");

    private int id;
    private String descricao;

    private TipoCobrancaEnum(int id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoCobrancaEnum obterPorId(int id) {
        for (TipoCobrancaEnum tipo : values()) {
            if (tipo.getId() == id) {
                return tipo;
            }
        }
        return null;
    }
}

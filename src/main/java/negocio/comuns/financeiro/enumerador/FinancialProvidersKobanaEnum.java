package negocio.comuns.financeiro.enumerador;

import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 24/06/2024
 */
public enum FinancialProvidersKobanaEnum {
    ABC_BRASIL("bank", "ABC Brasil", "BANCO ABC BRASIL S.A.", "246", "246-1", "abc"),
    AILOS("bank", "Ailos", "Cooperativa Central de Crédito Ailos", "085", "085-0", "ailos"),
    ARBI("bank", "ARBI", "BANCO ARBI S.A.", "213", "213-5", "arbi"),
    BANESE("bank", "BANESE", "BANESE", "047", "047-7", "banese"),
    BANESTES("bank", "Banestes", "BANESTES S.A. BANCO DO ESTADO DO ESPIRITO SANTO", "021", "021-3", "banestes"),
    BANRISUL("bank", "Banrisul", "BANCO DO ESTADO DO RIO GRANDE DO SUL S.A.", "041", "041-8", "banrisul"),
    BANCO_DO_BRASIL("bank", "Banco do Brasil", "BANCO DO BRASIL S.A.", "001", "001-9", "bb"),
    BOCOM_BBM("bank", "BOCOM BBM", "O Banco BOCOM BBM S.A.", "107", "107-4", "bbm"),
    BIB("bank", "BIB", "Banco Industrial do Brasil S.A.", "604", "604-1", "bib"),
    BLUPAY("ip", "BLUPAY", "BLUPAY", "001", "001-9", "blupay"),
    BANCO_DO_NORDESTE("bank", "Banco do Nordeste", "BANCO DO NORDESTE DO BRASIL S.A.", "004", "004-3", "bnb"),
    BNP_PARIBAS("bank", "BNP PARIBAS", "BANCO BNP PARIBAS BRASIL S.A.", "752", "752-8", "bnpparibas"),
    BRADESCO("bank", "Bradesco", "BANCO BRADESCO S.A.", "237", "237-2", "bradesco"),
    BRB("bank", "BRB", "BRB - Banco de Brasília S.A.", "070", "070-1", "brb"),
    BANCO_BS2("bank", "Banco BS2", "BANCO BS2 S.A.", "218", "218-6", "bs2"),
    BANCO_BTG("bank", "Banco BTG", "BANCO BTG PACTUAL S.A.", "208", "208-9", "btg"),
    BV("bank", "BV", "BCO VOTORANTIM S.A.", "655", "655-6", "bv"),
    CAIXA("bank", "Caixa Econômica Federal", "CAIXA ECONOMICA FEDERAL", "104", "104-0", "caixa"),
    CARUANA("bank", "Caruana", "CARUANA S.A. - SOCIEDADE DE CRÉDITO, FINANCIAMENTO E INVESTIMENTO", "130", "130-9", "caruana"),
    CITIBANK("bank", "Citibank", "BANCO CITIBANK S.A.", "745", "745-5", "citibank"),
    CORA("bank", "Cora", "CORA SOCIEDADE DE CRÉDITO DIRETO S.A.", "403", "403-0", "cora"),
    CREDISIS("bank", "CrediSIS", "Cooperativa Central de Crédito Noroeste Brasileiro Ltda.", "097", "097-3", "credisis"),
    CRESOL("bank", "CRESOL", "CRESOL", "133", "133-3", "cresol"),
    CRESOL_BRADESCO("bank", "CRESOL", "CRESOL CONFEDERAÇÃO", "237", "237-2", "cresol-bradesco"),
    DAYCOVAL("bank", "Daycoval", "BANCO DAYCOVAL S.A.", "707", "707-2", "daycoval"),
    INTER("bank", "Banco Inter", "BANCO INTER S.A.", "077", "077-9", "inter"),
    BACKOFFICE("bank", "BackOffice", "back_office"),
    ITAU("bank", "Itaú", "ITAÚ UNIBANCO S.A.", "341", "341-7", "itau"),
    MERCANTIL("bank", "BANCO MERCANTIL", "BANCO MERCANTIL DO BRASIL S.A.", "389", "389-1", "mercantil"),
    MONEYPLUS("bank", "MONEY PLUS SCMEPP LTDA", "MONEY PLUS SOCIEDADE DE CRÉDITO AO MICROEMPREENDEDOR E A EMPRESA DE PEQUENO PORT", "274", "274-7", "moneyplus"),
    PJBANK("ip", "PJBank", "PJBank", "481", "481-2", "pjbank"),
    QITECH("bank", "QI Tech", "QI Tech", "329", "329-8", "qitech"),
    SAFRA("bank", "Safra", "BANCO SAFRA S.A.", "422", "422-7", "safra"),
    SANTANDER("bank", "Santander", "BANCO SANTANDER (BRASIL) S.A.", "033", "033-7", "santander"),
    SEMEAR("bank", "Semear", "BANCO SEMEAR S.A.", "743", "743-9", "semear"),
    RENDIMENTO("bank", "Rendimento", "BCO RENDIMENTO S.A.", "633", "633-5", "rendimento"),
    SICOOB("bank", "Bancoob/Sicoob", "BANCO COOPERATIVO DO BRASIL S.A. - BANCOOB", "756", "756-0", "sicoob"),
    SICREDI("bank", "Sicredi", "BANCO COOPERATIVO SICREDI S.A.", "748", "748-X", "sicredi"),
    SOFISA("bank", "Sofisa", "Banco Sofisa S. A.", "637", "637-8", "sofisa"),
    UNICRED("bank", "UNICRED DO BRASIL", "UNICRED DO BRASIL", "136", "136-8", "unicred"),
    UNIPRIME("bank", "UNIPRIME PARANÁ", "UNIPRIME NORTE DO PARANÁ", "084", "084-1", "uniprime"),
    UNIPRIME_CENTRAL("bank", "UNIPRIME Central", "Uniprime Central - Central Interestadual De Cooperativas De Credito Ltda.", "099", "099-X", "uniprime99"),
    ZEMO("ip", "ZemoBank", "ZemoBank", "655", "655-6", "zemo"),
    EXAMPLE_BANK("bank", "Banco de exemplo", "Banco de exemplo", "000", "000-0", "example_bank"),
    SPC("credit-bureau", "SPC", "spc"),
    PLUGGY("partner", "Pluggy", "pluggy");

    private final String kind;
    private final String name;
    private final String bcbName;
    private final String number;
    private final String numberWithDigit;
    private final String slug;

    FinancialProvidersKobanaEnum(String kind, String name, String bcbName, String number, String numberWithDigit, String slug) {
        this.kind = kind;
        this.name = name;
        this.bcbName = bcbName;
        this.number = number;
        this.numberWithDigit = numberWithDigit;
        this.slug = slug;
    }

    public static FinancialProvidersKobanaEnum obterPorCodigoBancario(int codBanco) {
        for (FinancialProvidersKobanaEnum provider : FinancialProvidersKobanaEnum.values()) {
            if (!UteisValidacao.emptyString(provider.getNumber()) && Integer.valueOf(provider.getNumber()).equals(codBanco)) {
                return provider;
            }
        }
        return null;
    }

    FinancialProvidersKobanaEnum(String kind, String name, String slug) {
        this(kind, name, null, null, null, slug);
    }

    public String getKind() {
        return kind;
    }

    public String getName() {
        return name;
    }

    public String getBcbName() {
        return bcbName;
    }

    public String getNumber() {
        return number;
    }

    public String getNumberWithDigit() {
        return numberWithDigit;
    }

    public String getSlug() {
        return slug;
    }
}

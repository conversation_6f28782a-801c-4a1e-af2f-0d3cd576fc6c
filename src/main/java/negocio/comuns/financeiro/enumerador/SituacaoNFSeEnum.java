/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

/*
 * <AUTHOR>
 */
public enum SituacaoNFSeEnum {

    ENVIADA(1, "Enviada", "#00008B", "Nota enviada, entre no módulo NFSe para verificar a situação da nota.", ""),
    NAO_ENVIADA(2, "Não enviada", "#fff212", "Nota não enviada", ""),
    ENVIADO_OUTROMES(3, "Parcialmente emitida", "", "Parte desse pagamento já foi emitido em meses anteriores.", "rgba(71, 203, 127, 0.4)"),
    EMITIDA_OUTRA_FORMA(4, "Emitida com outra forma de emissão", "orange", "Emitida com outra forma de emissão, verifique no módulo NFSe.", "");

    private int id;
    private String descricao;
    private String cor;
    private String hint;
    private String classe;

    private SituacaoNFSeEnum(int id, String descricao, String cor, String hint, String classe) {
        this.id = id;
        this.descricao = descricao;
        this.cor = cor;
        this.hint = hint;
        this.classe = classe;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getClasse() {
        return classe;
    }
}

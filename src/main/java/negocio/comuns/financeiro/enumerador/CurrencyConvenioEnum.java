package negocio.comuns.financeiro.enumerador;

public enum CurrencyConvenioEnum {

    BRL("BRL"),
    USD("USD");

    private String descricao;

    CurrencyConvenioEnum(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public static CurrencyConvenioEnum valueOff(String descricao) {
        CurrencyConvenioEnum[] values = CurrencyConvenioEnum.values();
        for (CurrencyConvenioEnum currencyConvenioEnum : values) {
            if (currencyConvenioEnum.getDescricao().equals(descricao)) {
                return currencyConvenioEnum;
            }
        }
        return null;
    }

}

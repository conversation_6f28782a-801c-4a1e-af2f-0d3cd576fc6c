package negocio.comuns.financeiro.enumerador;

/**
 * Enum utilizado na atualização cadastral do financeiro
 */
public enum NichoEnum {

    ACADEMIA_FULL_SERVICE_HIGH_COST("AFSH", "Academia Full Service High Cost"),
    ACADEMIA_FULL_SERVICE_LOW_COST("AFSLC", "Academia Full Service Low Cost"),
    ACADEMIA_NATACAO("AN", "Academia de Natação"),
    ACADEMIA_LUTAS("AL", "Academia de Lutas"),
    BOX_CROSS_FUNCIONAL("BCF", "Box Cross / Funcional"),
    STUDIOS("ST", "Studios"),
    ESPORTES_QUADRA_AREIA("EQA", "Esportes de Quadra e Areia"),
    AUTARQUIA("AU", "Autarquia"),
    ASSOCIACAO_CLUBE_PARTICULAR("ACP", "Associação / Clube Particular"),
    ;

    private String sigla;
    private String descricao;

    private NichoEnum(String sigla, String descricao) {
        this.sigla = sigla;
        this.descricao = descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public String getDescricao() {
        return descricao;
    }

    public static NichoEnum consultarPorSigla(String sigla) {
        for (NichoEnum item : values()) {
            if (item.getSigla().equals(sigla)) {
                return item;
            }
        }
        return null;
    }
}

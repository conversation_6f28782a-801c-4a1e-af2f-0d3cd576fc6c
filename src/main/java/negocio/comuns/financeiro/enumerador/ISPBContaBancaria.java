package negocio.comuns.financeiro.enumerador;

import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 29/07/2024
 * <p>
 */
public enum ISPBContaBancaria {
    NENHUM(0, ""),
    BCO_DO_BRASIL_SA(1, "00000000"),
    BRB_BCO_DE_BRASILIA_SA(70, "00000208"),
    SANTINVEST_SA_CFI(539, "00122327"),
    CCR_SEARA(430, "00204963"),
    AGK_CC_SA(272, "********"),
    CONF_NAC_COOP_CENTRAIS_UNICRED(136, "********"),
    SEFER_INVESTIMENTOS_DTVM_LTDA(407, "********"),
    CAIXA_ECONOMICA_FEDERAL(104, "********"),
    BANCO_INTER(77, "********"),
    COLUNA_SA_DTVM(423, "********"),
    BCO_RIBEIRAO_PRETO_SA(741, "********"),
    BANCO_BARI_SA(330, "********"),
    EWALLY_IP_SA(534, "********"),
    BANCO_SEMEAR(743, "********"),
    PLANNER_CV_SA(100, "********"),
    FDO_GARANTIDOR_CRÉDITOS(541, "********"),
    BCO_B3_SA(96, "********"),
    BCO_RABOBANK_INTL_BRASIL_SA(747, "********"),
    CIELO_IP_SA(362, "********"),
    CCR_DE_ABELARDO_LUZ(322, "********"),
    BCO_COOPERATIVO_SICREDI_SA(748, "********"),
    SICRES(578, "********"),
    COOP_DE_AGRICULTORES_E_AEROPORTUÁRIOS_DO_BRASIL(350, "********"),
    BCO_BNP_PARIBAS_BRASIL_S_A(752, "********"),
    CECM_COOPERFORTE(379, "********"),
    Kirton_Bank(399, "********"),
    BCO_BRASILEIRO_DE_CRÉDITO_SA(378, "********"),
    BCO_BV_SA(413, "********"),
    BANCO_SICOOB_SA(756, "********"),
    TRINUS_CAPITAL_DTVM(360, "********"),
    BCO_KEB_HANA_DO_BRASIL_SA(757, "********"),
    XP_INVESTIMENTOS_CCTVM_SA(102, "********"),
    SISPRIME_DO_BRASIL_COOP(84, "********"),
    BANVOX_DTVM(591, "********"),
    PAN_CFI(555, "********"),
    CM_CAPITAL_MARKETS_CCTVM_LTDA(180, "********"),
    BCO_MORGAN_STANLEY_SA(66, "********"),
    UBS_BRASIL_CCTVM_SA(15, "********"),
    TREVISO_CC_SA(143, "********"),
    HIPERCARD_BM_SA(62, "********"),
    BCO_JSAFRA_SA(74, "********"),
    UNIPRIME_COOPCENTRAL_LTDA(99, "********"),
    BCO_TOYOTA_DO_BRASIL_SA(387, "********"),
    PARATI_CFI_SA(326, "********"),
    BCO_ALFA_SA(25, "********"),
    BANCO_ABN_AMRO_CLEARING_SA(75, "********"),
    BCO_CARGILL_SA(40, "********"),
    TERRA_INVESTIMENTOS_DTVM(307, "********"),
    CECM_DOS_TRABPORT_DA_GVITOR(385, "********"),
    SOCINAL_SA_CFI(425, "03881423"),
    SERVICOOP(190, "03973814"),
    OZ_CORRETORA_DE_CÂMBIO_SA(296, "04062902"),
    BANCO_BRADESCARD(63, "04184779"),
    NOVA_FUTURA_CTVM_LTDA(191, "04257795"),
    FIDUCIA_SCMEPP_LTDA(382, "04307598"),
    GOLDMAN_SACHS_DO_BRASIL_BM_SA(64, "04332281"),
    CREDISIS_CENTRAL_DE_COOPERATIVAS_DE_CRÉDITO_LTDA(97, "04632856"),
    CCM_DESP_TRÂNS_SC_E_RS(16, "04715685"),
    BCO_AFINZ_SA_BM(299, "04814563"),
    CECM_SERV_PUBL_PINHÃO(471, "04831810"),
    HBI_SCD(540, "04849745"),
    BANCO_INBURSA(12, "04866275"),
    BCO_DA_AMAZONIA_SA(3, "04902979"),
    CONFIDENCE_CC_SA(60, "04913129"),
    BCO_DO_EST_DO_PA_SA(37, "04913711"),
    ZEMA_CFI_SA(359, "05351887"),
    CASA_CREDITO_SA_SCM(159, "05442029"),
    COOPCENTRAL_AILOS(85, "05463212"),
    COOP_CREDITAG(400, "05491616"),
    CREDIARE_CFI_SA(429, "05676026"),
    PLANNER_SOCIEDADE_DE_CRÉDITO_DIRETO(410, "05684234"),
    CECM_FABRIC_CALÇADOS_SAPIRANGA(328, "05841967"),
    RPW_SA_SCFI(548, "06249129"),
    BCO_BBI_SA(36, "06271464"),
    PICPAY_INVEST(469, "07138049"),
    BCO_BRADESCO_FINANC_SA(394, "07207996"),
    BCO_DO_NORDESTE_DO_BRASIL_SA(4, "07237373"),
    HEDGE_INVESTMENTS_DTVM_LTDA(458, "07253654"),
    BCO_CCB_BRASIL_SA(320, "07450604"),
    HS_FINANCEIRA(189, "07512441"),
    LECCA_CFI_SA(105, "07652226"),
    BCO_KDB_BRASIL_SA(76, "07656500"),
    BANCO_TOPÁZIO_SA(82, "07679404"),
    HSCM_SCMEPP_LTDA(312, "07693858"),
    VALOR_SCD_SA(195, "07799277"),
    POLOCRED_SCMEPP_LTDA(93, "07945233"),
    CCR_DE_IBIAM(391, "08240446"),
    CCR_DE_SÃO_MIGUEL_DO_OESTE(273, "********"),
    BCO_CSF_SA(368, "********"),
    PAGSEGURO_INTERNET_IP_SA(290, "********"),
    MONEYCORP_BCO_DE_CÂMBIO_SA(259, "********"),
    F_D_GOLD_DTVM_LTDA(395, "********"),
    EFÍ_SA_IP(364, "********"),
    ICAP_DO_BRASIL_CTVM_LTDA(157, "********"),
    SOCRED_SA_SCMEPP(183, "********"),
    STATE_STREET_BR_SA_BCO_COMERCIAL(14, "********"),
    CARUANA_SCFI(130, "********"),
    MIDWAY_SA_SCFI(358, "********"),
    CODEPE_CVC_SA(127, "********"),
    PICPAY_BANK_BANCO_MÚLTIPLO_SA(79, "********"),
    MASTER_BI_SA(141, "********"),
    SUPERDIGITAL_IP_SA(340, "********"),
    BANCOSEGURO_SA(81, "********"),
    BCO_YAMAHA_MOTOR_SA(475, "********"),
    CRESOL_CONFEDERAÇÃO(133, "********"),
    MERCADO_PAGO_IP_LTDA(323, "********"),
    AF_DESENVOLVE_SP_SA(577, "********"),
    BCO_AGIBANK_SA(121, "********"),
    BCO_DA_CHINA_BRASIL_SA(83, "********"),
    GET_MONEY_CC_LTDA(138, "********"),
    BCO_BANDEPE_SA(24, "********"),
    GLOBAL_SCM_LTDA(384, "********"),
    NEON_FINANCEIRA_CFI_SA(426, "********"),
    MERCADO_BITCOIN_IP_LTDA(576, "********"),
    BANCO_RANDON_SA(88, "********"),
    OM_DTVM_LTDA(319, "********"),
    BMP_SCMEPP_LTDA(274, "********"),
    TRAVELEX_BANCO_DE_CÂMBIO_SA(95, "********"),
    BANCO_FINAXIS(94, "********"),
    GAZINCRED_SA_SCFI(478, "********"),
    BCO_SENFF_SA(276, "********"),
    MIRAE_ASSET_BRASIL_CCTVM_LTDA(447, "********"),
    CONTA_PRONTA_IP(569, "********"),
    BCO_DO_EST_DE_SE_SA(47, "********"),
    EBURY_BCO_DE_CÂMBIO_SA(144, "********"),
    ACESSO_SOLUÇÕES_DE_PAGAMENTO_SA_INSTITUIÇÃO_DE_PAGAMENTO(332, "********"),
    FITBANK_IP(450, "********"),
    BR_PARTNERS_BI(126, "********"),
    ÓRAMA_DTVM_SA(325, "********"),
    DOCK_IP_SA(301, "********"),
    BRL_TRUST_DTVM_SA(173, "********"),
    OSLO_CAPITAL_DTVM_SA(331, "********"),
    BCO_WESTERN_UNION(119, "********"),
    HUB_IP_SA(396, "********"),
    CELCOIN_IP_SA(509, "********"),
    PARANA_BCO_SA(254, "********"),
    BARI_CIA_HIPOTECÁRIA(268, "********"),
    IUGU_IP_SA(401, "********"),
    BCO_BOCOM_BBM_SA(107, "********"),
    BANCO_BESA_SA(334, "********"),
    SOCIAL_BANK_SA(412, "********"),
    BCO_WOORI_BANK_DO_BRASIL_SA(124, "********"),
    INTRA_DTVM(549, "********"),
    FACTA_SA_CFI(149, "********"),
    STONE_IP_SA(197, "********"),
    ID_CTVM(439, "********"),
    BROKER_BRASIL_CC_LTDA(142, "********"),
    PINBANK_IP(529, "********"),
    BCO_MERCANTIL_DO_BRASIL_SA(389, "********"),
    BCO_TRIANGULO_SA(634, "********"),
    SENSO_CCVM_SA(545, "********"),
    ICBC_DO_BRASIL_BM_SA(132, "********"),
    VIPS_CC_LTDA(298, "********"),
    BMS_SCD_SA(377, "********"),
    CREFAZ_SCMEPP_SA(321, "********"),
    CLOUDWALK_IP_LTDA(542, "********"),
    NU_PAGAMENTOS_IP(260, "********"),
    CDC_SCD_SA(470, "********"),
    UBS_BRASIL_BI_SA(129, "********"),
    AZIMUT_BRASIL_DTVM_LTDA(562, "********"),
    BRAZA_BANK_SA_BCO_DE_CÂMBIO(128, "********"),
    LAMARA_SCD_SA(416, "********"),
    ZOOP_MEIOS_DE_PAGAMENTO(595, "********"),
    ASAAS_IP_SA(461, "********"),
    UNIDA_DTVM_LTDA(194, "********"),
    SUDACRED_SCD_SA(538, "********"),
    PROVER_PROMOCAO_DE_VENDAS_IP_LTDA(588, "********"),
    PAY4FUN_IP_SA(561, "********"),
    NEON_PAGAMENTOS_SA_IP(536, "********"),
    EBANX_IP_LTDA(383, "********"),
    CARTOS_SCD_SA(324, "********"),
    MAG_IP_LTDA(560, "********"),
    SRM_BANK(533, "********"),
    VORTX_DTVM_LTDA(310, "********"),
    PICPAY(380, "********"),
    FLAGSHIP_IP_LTDA(566, "********"),
    WILL_FINANCEIRA_SACFI(280, "********"),
    GUITTA_CC_LTDA(146, "********"),
    FFA_SCMEPP_LTDA(343, "********"),
    TRANSFEERA_IP_SA(593, "********"),
    BANCO_DIGIO(335, "********"),
    AL5_SA_CFI(349, "********"),
    CREDUFES(427, "********"),
    REALIZE_CFI_SA(374, "********"),
    GENIAL_INVESTIMENTOS_CVM_SA(278, "********"),
    IB_CCTVM_SA(271, "********"),
    BCO_BANESTES_SA(21, "********"),
    BCO_ABC_BRASIL_SA(246, "28195667"),
    GALAPAGOS_DTVM_SA(292, "28650236"),
    STONEX_BANCO_DE_CÂMBIO_SA(554, "28811341"),
    Scotiabank_Brasil(751, "29030467"),
    TORO_CTVM_SA(352, "29162769"),
    BANCO_BTG_PACTUAL_SA(208, "30306294"),
    NU_FINANCEIRA_SA_CFI(386, "30680829"),
    BCO_MODAL_SA(746, "30723886"),
    PAGPRIME_IP(557, "30944783"),
    U4C_INSTITUIÇÃO_DE_PAGAMENTO_SA(546, "30980539"),
    BCO_CLASSICO_SA(241, "31597552"),
    IDEAL_CTVM_SA(398, "31749596"),
    BCO_C6_SA(336, "31872495"),
    BCO_GUANABARA_SA(612, "31880826"),
    BCO_INDUSTRIAL_DO_BRASIL_SA(604, "31895683"),
    BCO_CREDIT_SUISSE_SA(505, "32062580"),
    BEETELLER(550, "32074986"),
    UZZIPAY_IP_SA(552, "32192325"),
    QI_SCD_SA(329, "32402502"),
    FAIR_CC_SA(196, "32648370"),
    CREDITAS_SCD(342, "32997490"),
    MERCANTIL_FINANCEIRA(567, "33040601"),
    BCO_LA_NACION_ARGENTINA(300, "33042151"),
    CITIBANK_NA(477, "33042953"),
    BCO_CEDULA_SA(266, "33132044"),
    BCO_BRADESCO_BERJ_SA(122, "33147315"),
    BCO_JP_MORGAN_SA(376, "33172537"),
    BCO_XP_SA(348, "33264668"),
    BCO_CAIXA_GERAL_BRASIL_SA(473, "33466988"),
    BCO_CITIBANK_SA(745, "33479023"),
    BCO_RODOBENS_SA(120, "33603457"),
    BCO_FATOR_SA(265, "33644196"),
    BNDES(7, "33657248"),
    CCC_POUP_INV_DO_CENTRO_NORTE_DO_BRASIL(583, "33667205"),
    CCC_POUP_INV_DE_MS_GO_DF_E_TO(582, "33737818"),
    ATIVA_SA_INVESTIMENTOS_CCTVM(188, "33775974"),
    BGC_LIQUIDEZ_DTVM_LTDA(134, "33862244"),
    BANCO_ITAÚ_CONSIGNADO_SA(29, "33885724"),
    MASTER_SA_CCTVM(467, "33886862"),
    BANCO_MASTER(243, "33923798"),
    LISTO_SCD_SA(397, "34088029"),
    HAITONG_BI_DO_BRASIL_SA(78, "34111187"),
    INTERCAM_CC_LTDA(525, "34265629"),
    ÓTIMO_SCD_SA(355, "34335592"),
    BMP_SCD_SA(531, "34337707"),
    ISSUER_IP_LTDA(597, "34747388"),
    REAG_DTVM_SA(528, "34829992"),
    PLANTAE_CFI(445, "35551187"),
    Z1_IP_LTDA(586, "35810871"),
    UPP_SEP_SA(373, "35977097"),
    OLIVEIRA_TRUST_DTVM_SA(111, "36113876"),
    FINVEST_DTVM(512, "36266751"),
    QISTA_SA_CFI(516, "36583700"),
    BONUSPAGO_SCD_SA(408, "36586946"),
    MAF_DTVM_SA(484, "36864992"),
    COBUCCIO_SA_SCFI(402, "36947229"),
    SCFI_EFÍ_SA(507, "37229413"),
    SUMUP_SCD_SA(404, "37241230"),
    ZIPDIN_SCD_SA(418, "37414009"),
    LEND_SCD_SA(414, "37526080"),
    DM(449, "37555231"),
    FIDD_DTVM_LTDA(587, "37678915"),
    MERCADO_CRÉDITO_SCFI_SA(518, "37679449"),
    ACCREDITO_SCD_SA(406, "37715993"),
    CORA_SCFI(403, "37880206"),
    NUMBRS_SCD_SA(419, "38129006"),
    DELCRED_SCD_SA(435, "38224857"),
    FÊNIX_DTVM_LTDA(455, "38429045"),
    MULTICRED_SCD_SA(544, "38593706"),
    CC_LAR_CREDI(421, "39343350"),
    CREDIHOME_SCD(443, "39416705"),
    OPEA_SCD(535, "39519944"),
    UY3_SCD_SA(457, "39587424"),
    CREDSYSTEM_SCD_SA(428, "39664698"),
    HEMERA_DTVM_LTDA(448, "39669186"),
    CREDIFIT_SCD_SA(452, "39676772"),
    FFCRED_SCD_SA(510, "39738065"),
    STARK_SCD_SA(462, "39908427"),
    CAPITAL_CONSIG_SCD_SA(465, "40083667"),
    PROTEGE_PAY_CASH_IP_SA(563, "40276692"),
    PORTOPAR_DTVM_LTDA(306, "40303299"),
    PROSEFTUR(556, "40333582"),
    FOURTRADE_COR_DE_CAMBIO_LTDA(305, "40353377"),
    AZUMI_DTVM(463, "40434681"),
    REPASSES_FINANCEIROS_E_SOLUCOES_TECNOLOGICAS_IP_SA(590, "40473435"),
    J17_SCD_SA(451, "40475846"),
    TRINUS_SCD_SA(444, "40654622"),
    LIONS_TRUST_DTVM(519, "40768766"),
    MÉRITO_DTVM_LTDA(454, "41592532"),
    UNAVANTI_SCD_SA(460, "42047025"),
    RJI(506, "42066258"),
    SBCASH_SCD(482, "42259084"),
    BNY_MELLON_BCO_SA(17, "42272526"),
    PEFISA_SA_CFI(174, "43180355"),
    SUPERLÓGICA_SCD_SA(481, "43599047"),
    PEAK_SEP_SA(521, "44019481"),
    BR_CAPITAL_DTVM_SA(433, "44077014"),
    BCO_LA_PROVINCIA_B_AIRES_BCE(495, "********"),
    HR_DIGITAL_SCD(523, "********"),
    ATICCA_SCD_SA(527, "********"),
    MAGNUM_SCD(511, "********"),
    SOMAPAY_SCD_SA(520, "********"),
    ATF_SCD_SA(513, "********"),
    BANCO_GENIAL(125, "********"),
    BNK_DIGITAL_SCD_SA(547, "********"),
    MAPS_IP_LTDA(592, "********"),
    EAGLE_SCD_SA(532, "********"),
    MICROCASH_SCMEPP_LTDA(537, "********"),
    WNT_CAPITAL_DTVM(524, "********"),
    MONETARIE_SCD(526, "********"),
    JPMORGAN_CHASE_BANK(488, "********"),
    QI_DTVM_LTDA(558, "********"),
    RED_SCD_SA(522, "********"),
    SER_FINANCE_SCD_SA(530, "********"),
    DGBK_CREDIT_SA_SOCIEDADE_DE_CRÉDITO_DIRETO(575, "********"),
    EMBRACRED_SA_SCD(594, "********"),
    PERCAPITAL_SCD_SA(553, "********"),
    BCO_ANDBANK_SA(65, "********"),
    VERT_DTVM_LTDA(551, "********"),
    KANASTRA_SCD(559, "********"),
    QUADRA_SCD(579, "********"),
    TRIO_IP_LTDA(619, "********"),
    BRCONDOS_SCD_SA(568, "********"),
    LEVYCAM_CCV_LTDA(145, "********"),
    BCV_BCO_CRÉDITO_E_VAREJO_SA(250, "********"),
    SETHI_SCD_SA(585, "********"),
    G5_SCD_SA(589, "********"),
    REVOLUT_SCD_SA(620, "********"),
    ALL_IN_CRED_SCD_SA(572, "********"),
    NITRO_SCD_SA(614, "********"),
    BCO_HSBC_SA(269, "********"),
    BCO_ARBI_SA(213, "********"),
    _321_SCD_SA(644, "********"),
    INTESA_SANPAOLO_BRASIL_SA_BM(139, "********"),
    BCO_TRICURY_SA(18, "********"),
    BCO_SAFRA_SA(422, "********"),
    BCO_LETSBANK_SA(630, "********"),
    BCO_FIBRA_SA(224, "********"),
    BCO_VOLKSWAGEN_SA(393, "********"),
    BCO_LUSO_BRASILEIRO_SA(600, "********"),
    BCO_GM_SA(390, "********"),
    BANCO_PAN(623, "********"),
    BCO_VOTORANTIM_SA(655, "********"),
    BCO_ITAUBANK_SA(479, "********"),
    BCO_MUFG_BRASIL_SA(456, "********"),
    BCO_SUMITOMO_MITSUI_BRASIL_SA(464, "********"),
    ITAÚ_UNIBANCO_SA(341, "********"),
    BCO_BRADESCO_SA(237, "********"),
    BCO_MERCEDES_BENZ_SA(381, "********"),
    OMNI_BANCO_SA(613, "********"),
    BCO_SOFISA_SA(637, "********"),
    BANCO_VOITER(653, "********"),
    BCO_CREFISA_SA(69, "********"),
    BCO_MIZUHO_SA(370, "********"),
    BANCO_INVESTCRED_UNIBANCO_SA(249, "********"),
    BCO_BMG_SA(318, "********"),
    BCO_C6_CONSIG(626, "********"),
    AVENUE_SECURITIES_DTVM_LTDA(508, "********"),
    BCO_SOCIETE_GENERALE_BRASIL(366, "********"),
    NEON_CTVM_SA(113, "********"),
    TULLETT_PREBON_BRASIL_CVC_LTDA(131, "********"),
    CSUISSE_HEDGING_GRIFFO_CV_SA(11, "********"),
    BCO_PAULISTA_SA(611, "********"),
    BOFA_MERRILL_LYNCH_BM_SA(755, "********"),
    CREDISAN_CC(89, "********"),
    BCO_PINE_SA(643, "********"),
    NU_INVEST_CORRETORA_DE_VALORES_SA(140, "********"),
    BCO_DAYCOVAL_SA(707, "********"),
    CAROL_DTVM_LTDA(288, "********"),
    SINGULARE_CTVM_SA(363, "********"),
    RENASCENCA_DTVM_LTDA(101, "********"),
    DEUTSCHE_BANK_SABCO_ALEMAO(487, "********"),
    BANCO_CIFRA(233, "********"),
    GUIDE(177, "********"),
    TRUSTEE_DTVM_LTDA(438, "********"),
    SIMPAUL(365, "********"),
    BCO_RENDIMENTO_SA(633, "********"),
    CENTRAL_NORDESTE(581, "********"),
    BCO_BS2_SA(218, "********"),
    LASTRO_RDV_DTVM_LTDA(293, "********"),
    FRENTE_CC_SA(285, "********"),
    EXIM_CC_LTDA(514, "********"),
    BT_CC_LTDA(80, "********"),
    ÁGORA_CTVM_SA(565, "********"),
    NOVO_BCO_CONTINENTAL_SA_BM(753, "********"),
    BCO_CRÉDIT_AGRICOLE_BR_SA(222, "********"),
    CCR_COOPAVEL(281, "********"),
    BANCO_SISTEMA(754, "********"),
    CREDIALIANÇA_CCR(98, "********"),
    BCO_VR_SA(610, "********"),
    OURIBANK_SA(712, "********"),
    CCC_POUP_E_INV_DOS_ESTADOS_DO_PR_SP_E_RJ(584, "********"),
    BCO_RNX_SA(720, "********"),
    CREDICOAMO(10, "********"),
    CREDIBRF_COOP(440, "********"),
    CCCPOUPINV_SUL_E_SUDESTE_CENTRAL_SULSUDESTE(580, "********"),
    RB_INVESTIMENTOS_DTVM_LTDA(283, "********"),
    BCO_SANTANDER_BRASIL_SA(33, "90400888"),
    BANCO_JOHN_DEERE_SA(217, "91884981"),
    BCO_DO_ESTADO_DO_RS_SA(41, "92702067"),
    COOPCRECE(543, "92825397"),
    ADVANCED_CC_LTDA(117, "92856905"),
    BCO_DIGIMAIS_SA(654, "92874270"),
    WARREN_CVMC_LTDA(371, "92875780"),
    BANCO_ORIGINAL(212, "92894922"),
    EFX_CC_LTDA(289, "94968518"),
    ;

    private final Integer codigo_banco;
    private final String ispb;

    ISPBContaBancaria(int codigo_banco, String ispb) {
        this.codigo_banco = codigo_banco;
        this.ispb = ispb;
    }

    public static ISPBContaBancaria obterPorCodigoBanco(Integer codigo_banco) {
        for (ISPBContaBancaria value : values()) {
            if (value.getCodigo_banco().equals(codigo_banco)) {
                return value;
            }
        }
        return ISPBContaBancaria.NENHUM;
    }

    public static ISPBContaBancaria obterPorValue(String value) {
        for (ISPBContaBancaria obj : ISPBContaBancaria.values()) {
            if (obj.name().equals(value.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public static ISPBContaBancaria obterPorDescricao(String descricao) {
        for (ISPBContaBancaria obj : ISPBContaBancaria.values()) {
            if (obj.getIspb().toUpperCase().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public Integer getCodigo_banco() {
        return codigo_banco;
    }

    public String getIspb() {
        if (UteisValidacao.emptyString(ispb)) {
            return "";
        }
        return ispb;
    }
}

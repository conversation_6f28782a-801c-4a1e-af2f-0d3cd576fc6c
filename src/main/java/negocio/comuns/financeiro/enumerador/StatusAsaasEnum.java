package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 05/05/2023
 */
public enum StatusAsaasEnum {

    PENDING(0, "Pendente"),
    RECEIVED(1, "<PERSON><PERSON>bid<PERSON>"), //<PERSON><PERSON><PERSON><PERSON> (saldo já creditado na conta)
    CONFIRMED(2, "Confirmada"), //Pagamento confirmado (saldo ainda não creditado)
    OVERDUE(3, "Vencida"),
    REFUNDED(4, "Estornada"),
    RECEIVED_IN_CASH(5, "Recebida em dinheiro"), //Recebida em dinheiro (não gera saldo na conta)
    REFUND_IN_PROGRESS(6, "Estorno em progresso"), //Estorno em processamento (liquidação já está agendada, cobrança será estornada após executar a liquidação)
    CHARGEBACK_REQUESTED(7, "Recebido chargeback"),
    CHARGEBACK_DISPUTE(8, "Chargeback em disputa"), //Em disputa de chargeback (caso sejam apresentados documentos para contestação)
    AWAITING_CHARGEBACK_REVERSAL(9, "Chargeback disputa vencida"), //Disputa vencida, aguardando repasse da adquirente
    DUNNING_REQUESTED(10, "Processo de negativação"), //Em processo de negativação
    AWAITING_RISK_ANALYSIS(11, "Pagamento em análise"),
    ;

    private Integer codigo;
    private String descricao;

    StatusAsaasEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static StatusAsaasEnum obterPorCodigo(Integer codigo) {
        for (StatusAsaasEnum origem : StatusAsaasEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return PENDING;
    }

    public static StatusAsaasEnum obterPorLabel(String descricao) {
        for (StatusAsaasEnum obj : StatusAsaasEnum.values()) {
            if (obj.name().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return null;
    }
}

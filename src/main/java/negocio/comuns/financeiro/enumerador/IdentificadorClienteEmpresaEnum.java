package negocio.comuns.financeiro.enumerador;

/**
 * Created by GlaucoT on 06/01/2016
 */
public enum IdentificadorClienteEmpresaEnum {

    COD_PESSOA(0, "COD. PESSOA"),
    CPF(1, "CPF"),
    MATRICULA(2, "MATRICULA");

    private int codigo;
    private String descricao;

    IdentificadorClienteEmpresaEnum(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static IdentificadorClienteEmpresaEnum valueOf(final int id) {
        IdentificadorClienteEmpresaEnum[] lista = IdentificadorClienteEmpresaEnum.values();
        IdentificadorClienteEmpresaEnum identificador = null;
        for (IdentificadorClienteEmpresaEnum tiposIdentificadores : lista) {
            if (tiposIdentificadores.getCodigo() == id) {
                identificador = tiposIdentificadores;
            }
        }
        return identificador;
    }

    public final int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 14/12/2024
 * <p>
 */
public enum BancoPermiteAPITransferenciaKobana {
    //por enquanto só tem nesse enum os bancos que disponibilizam API de pagamento na Koabana
    NENHUM("", "Nenhum"),
    INTER("inter", "Banco Inter"),
    ITAU("itau", "Itaú"),
    SICOOB("sicoob", "Sicoob"),
    QI_TECH("qitech", "Qi Tech"),
    ;

    private final String slug;
    private final String nome;

    BancoPermiteAPITransferenciaKobana(String slug, String nome) {
        this.slug = slug;
        this.nome = nome;
    }

    public static BancoPermiteAPITransferenciaKobana obterPorValue(String value) {
        for (BancoPermiteAPITransferenciaKobana obj : BancoPermiteAPITransferenciaKobana.values()) {
            if (obj.name().equals(value.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public static BancoPermiteAPITransferenciaKobana obterPorNome(String nome) {
        for (BancoPermiteAPITransferenciaKobana obj : BancoPermiteAPITransferenciaKobana.values()) {
            if (obj.getNome().toUpperCase().equals(nome.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public static BancoPermiteAPITransferenciaKobana obterPorSlug(String slug) {
        for (BancoPermiteAPITransferenciaKobana obj : BancoPermiteAPITransferenciaKobana.values()) {
            if (obj.getSlug().toUpperCase().equals(slug.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public String getSlug() {
        return slug;
    }

    public String getNome() {
        return nome;
    }
}

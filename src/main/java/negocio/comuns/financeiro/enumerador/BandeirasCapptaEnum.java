package negocio.comuns.financeiro.enumerador;

import negocio.comuns.utilitarias.Uteis;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public enum BandeirasCapptaEnum {

    CODIGO_1(1, "ABN"),
    CODIGO_2(2, "ABRAPETITE"),
    CODIGO_3(3, "ACC-CARD"),
    CODIGO_4(4, "ACEITO"),
    CODIGO_5(5, "ACSP"),
    CODIGO_6(6, "AGIPLAN"),
    CODIGO_7(7, "ALELO"),
    CODIGO_8(8, "AMAZONIA CELULAR"),
    CODIGO_9(9, "AMEX"),
    CODIGO_10(10, "ASCARD"),
    CODIGO_11(11, "AURA"),
    CODIGO_12(12, "AURA BERGAMAIS"),
    CODIGO_13(13, "AURA FNAC"),
    CODIGO_14(14, "AVISTA"),
    CODIGO_15(15, "BANCRED"),
    CODIGO_16(16, "BANESCARD"),
    CODIGO_17(17, "BANESE CARD"),
    CODIGO_18(18, "BANESTIK"),
    CODIGO_19(19, "BANKCARD"),
    CODIGO_20(20, "BANKTEC"),
    CODIGO_21(21, "BANPARA"),
    CODIGO_22(22, "BANQUET"),
    CODIGO_23(23, "BANQUET SMART"),
    CODIGO_24(24, "BANRICOMPRAS"),
    CODIGO_25(25, "BANRISUL"),
    CODIGO_26(26, "BANRISUL ALIMENTACAO"),
    CODIGO_27(27, "BANRISUL COMBUSTIVEL"),
    CODIGO_28(28, "BASECARD"),
    CODIGO_29(29, "BEM"),
    CODIGO_30(30, "BIGCARD"),
    CODIGO_31(31, "BLACKHAWK"),
    CODIGO_32(32, "BNB CLUBE"),
    CODIGO_33(33, "BNDES"),
    CODIGO_34(34, "BONUS"),
    CODIGO_35(35, "BRADESCO"),
    CODIGO_36(36, "BRASILCARD"),
    CODIGO_37(37, "BRT FIXA"),
    CODIGO_38(38, "BRTELECOM"),
    CODIGO_39(39, "CABAL CRED"),
    CODIGO_40(40, "CABAL DEB"),
    CODIGO_41(41, "CABAL VALE"),
    CODIGO_42(42, "CALCARD"),
    CODIGO_43(43, "CARDCO"),
    CODIGO_44(44, "CARTAO BRINQUEDO"),
    CODIGO_45(45, "CARTAO MAMAE"),
    CODIGO_46(46, "CARTAO NATAL"),
    CODIGO_47(47, "CB-BANCO DO BRASIL"),
    CODIGO_48(48, "CB-BRADESCO"),
    CODIGO_49(49, "CB-CITIBANK"),
    CODIGO_50(50, "CB-HSBC"),
    CODIGO_51(51, "CB-UNIBANCO"),
    CODIGO_52(52, "CELULAR"),
    CODIGO_53(53, "CHECKCHECK"),
    CODIGO_54(54, "CHEQUE ELETRONICO"),
    CODIGO_55(55, "CHEQUEPRE"),
    CODIGO_56(56, "CIELO"),
    CODIGO_57(57, "CLARO"),
    CODIGO_58(58, "CLARO OFF-LINE"),
    CODIGO_59(59, "COLABORADOR"),
    CODIGO_60(60, "COM VOCE"),
    CODIGO_61(61, "COMPROCARD"),
    CODIGO_62(62, "CONSTRUCARD"),
    CODIGO_63(63, "CONVCARD"),
    CODIGO_64(64, "COOPERCRED VALE"),
    CODIGO_65(65, "CPF"),
    CODIGO_66(66, "CREDI-SHOP"),
    CODIGO_67(67, "CREDITEM"),
    CODIGO_68(68, "CREDPAR"),
    CODIGO_69(69, "CREDSYSTEM"),
    CODIGO_70(70, "CTBC-CELULAR"),
    CODIGO_71(71, "CTBC-FIXO"),
    CODIGO_72(72, "DACASA"),
    CODIGO_73(73, "DIAMANTE"),
    CODIGO_74(74, "DINERS"),
    CODIGO_75(75, "DMCARD"),
    CODIGO_76(76, "ECXCARD"),
    CODIGO_77(77, "ELAVON"),
    CODIGO_78(78, "ELECTRON"),
    CODIGO_79(79, "ELO CREDITO"),
    CODIGO_80(80, "ELO DEBITO"),
    CODIGO_81(81, "ELO VOUCHER"),
    CODIGO_82(82, "EMBRATEL"),
    CODIGO_83(83, "EMBRATEL LIVRE ONLINE"),
    CODIGO_84(84, "EPAY"),
    CODIGO_85(85, "EPAY SEGUROS"),
    CODIGO_86(86, "EPHARMA"),
    CODIGO_87(87, "EPHARMA BALCAO"),
    CODIGO_88(88, "EVANGELICO"),
    CODIGO_89(89, "FACIL"),
    CODIGO_90(90, "FACIL_LOSANGO"),
    CODIGO_91(91, "FAI"),
    CODIGO_92(92, "FALA FACIL"),
    CODIGO_93(93, "FANCARD"),
    CODIGO_94(94, "FARMASEG"),
    CODIGO_95(95, "FIC"),
    CODIGO_96(96, "FININVEST"),
    CODIGO_97(97, "FLEXMED"),
    CODIGO_98(98, "FORTBRASIL"),
    CODIGO_99(99, "FUNCIONAL CARD"),
    CODIGO_100(100, "GETNET"),
    CODIGO_101(101, "GIVEX"),
    CODIGO_102(102, "GOIASCARD"),
    CODIGO_103(103, "GOODCARD"),
    CODIGO_104(104, "GOODMED"),
    CODIGO_105(105, "GOODVALE"),
    CODIGO_106(106, "GREENCARD"),
    CODIGO_107(107, "GYNCARD"),
    CODIGO_108(108, "HAPPY CARD"),
    CODIGO_109(109, "HIPERCARD"),
    CODIGO_110(110, "IBI"),
    CODIGO_111(111, "IBICARD"),
    CODIGO_112(112, "IBIPL"),
    CODIGO_113(113, "INCOMM"),
    CODIGO_114(114, "INFOCARDS"),
    CODIGO_115(115, "ITAU"),
    CODIGO_116(116, "ITAU-MOBILE"),
    CODIGO_117(117, "J.C.B."),
    CODIGO_118(118, "JETPARCARD"),
    CODIGO_119(119, "LEADER CARD"),
    CODIGO_120(120, "LOJISTA"),
    CODIGO_121(121, "LOSANGO"),
    CODIGO_122(122, "MAESTRO"),
    CODIGO_123(123, "MAIS!"),
    CODIGO_124(124, "MARISA"),
    CODIGO_125(125, "MASTERCARD"),
    CODIGO_126(126, "MAXICRED"),
    CODIGO_127(127, "MEDCHEQUE"),
    CODIGO_128(128, "MINASCRED"),
    CODIGO_129(129, "MULTIALIMENTACAO"),
    CODIGO_130(130, "MULTIALIMENTACAO BEN"),
    CODIGO_131(131, "MULTIBENEFICIO"),
    CODIGO_132(132, "MULTIBENEFICIOS"),
    CODIGO_133(133, "MULTICASH"),
    CODIGO_134(134, "MULTICESTABASICA"),
    CODIGO_135(135, "MULTICHEQUE"),
    CODIGO_136(136, "MULTICHEQUE ANTIGO"),
    CODIGO_137(137, "MULTICHEQUE BEN"),
    CODIGO_138(138, "MULTICOMBUSTIVEL"),
    CODIGO_139(139, "MULTICULTURA"),
    CODIGO_140(140, "MULTIEMPRESARIAL"),
    CODIGO_141(141, "MULTIFARMA"),
    CODIGO_142(142, "MULTIREFEICAO"),
    CODIGO_143(143, "MURY"),
    CODIGO_144(144, "NBC"),
    CODIGO_145(145, "NEUS"),
    CODIGO_146(146, "NEXTEL"),
    CODIGO_147(147, "NOKIA"),
    CODIGO_148(148, "NOVARTIS"),
    CODIGO_149(149, "NUTRICARD/BONUSCRED"),
    CODIGO_150(150, "NUTRICASH"),
    CODIGO_151(151, "OBOÉCARD"),
    CODIGO_152(152, "OI"),
    CODIGO_153(153, "OI FIXA"),
    CODIGO_154(154, "OI OFF"),
    CODIGO_155(155, "ORGCARD CRED"),
    CODIGO_156(156, "ORGCARD DEB"),
    CODIGO_157(157, "ORGCARD DVCS"),
    CODIGO_158(158, "PAGUE CONTAS VISANET 4.1"),
    CODIGO_159(159, "PBMGOV"),
    CODIGO_160(160, "PERSONAL CARD"),
    CODIGO_161(161, "PHARMASYSTEM"),
    CODIGO_162(162, "PL FREECENTER"),
    CODIGO_163(163, "PL GETNET MASTER"),
    CODIGO_164(164, "PL GETNET VISA"),
    CODIGO_165(165, "PL MAGAZINE"),
    CODIGO_166(166, "PL TESOURA"),
    CODIGO_167(167, "PLANVALE"),
    CODIGO_168(168, "PLANVALE R"),
    CODIGO_169(169, "POLICARD"),
    CODIGO_170(170, "PORTALCARD"),
    CODIGO_171(171, "POUPCARD"),
    CODIGO_172(172, "PRATICARD"),
    CODIGO_173(173, "PREMIACAO"),
    CODIGO_174(174, "PREMIUM"),
    CODIGO_175(175, "PRESENTE"),
    CODIGO_176(176, "PRESTASERV"),
    CODIGO_177(177, "PREVSAUDE"),
    CODIGO_178(178, "PREZUNIC"),
    CODIGO_179(179, "PRIVATE LABEL REDECARD"),
    CODIGO_180(180, "QUALYCARD"),
    CODIGO_181(181, "RAINBOW"),
    CODIGO_182(182, "RANCHO CARD ALIMENTACAO"),
    CODIGO_183(183, "RANCHO CARD CONVENIO"),
    CODIGO_184(184, "REC-FININVEST"),
    CODIGO_185(185, "REDECARD"),
    CODIGO_186(186, "REDESOFTNEX"),
    CODIGO_187(187, "REDESOFTNEX CONV"),
    CODIGO_188(188, "REFEISUL"),
    CODIGO_189(189, "ROSSI"),
    CODIGO_190(190, "SAFRA AMANCO"),
    CODIGO_191(191, "SAPORE"),
    CODIGO_192(192, "SENFF"),
    CODIGO_193(193, "SERASA"),
    CODIGO_194(194, "SERCOMTEL-CELULAR"),
    CODIGO_195(195, "SERCOMTEL-FIXO"),
    CODIGO_196(196, "SICREDI CREDITO"),
    CODIGO_197(197, "SICREDI DEBITO"),
    CODIGO_198(198, "SIFRAGO"),
    CODIGO_199(199, "SIMCRED"),
    CODIGO_200(200, "SISCRED"),
    CODIGO_201(201, "SISTEMA"),
    CODIGO_202(202, "SODEXO ALIMENTACAO"),
    CODIGO_203(203, "SODEXO REFEICAO"),
    CODIGO_204(204, "SOLLO"),
    CODIGO_205(205, "SOLUCARD"),
    CODIGO_206(206, "SOMAR"),
    CODIGO_207(207, "SOROCRED"),
    CODIGO_208(208, "SOROCRED CREDITO"),
    CODIGO_209(209, "STAFFCARD"),
    CODIGO_210(210, "SUPER BONUS"),
    CODIGO_211(211, "SUPERCARD"),
    CODIGO_212(212, "SYSDATA"),
    CODIGO_213(213, "TECBAN"),
    CODIGO_214(214, "TELECHEQUE"),
    CODIGO_215(215, "TELEFONICA"),
    CODIGO_216(216, "TELEFONIC-FAMILIA"),
    CODIGO_217(217, "TELEMAR"),
    CODIGO_218(218, "TELEMIG"),
    CODIGO_219(219, "TELENET"),
    CODIGO_220(220, "TELESP-SUPER 15"),
    CODIGO_221(221, "TICKET ALIMENTAÇÃO"),
    CODIGO_222(222, "TICKET CAR"),
    CODIGO_223(223, "TICKET RESTAURANTE"),
    CODIGO_224(224, "TIM"),
    CODIGO_225(225, "TIM ON-LINE"),
    CODIGO_226(226, "TIPCARD"),
    CODIGO_227(227, "TMS"),
    CODIGO_228(228, "TOKORO"),
    CODIGO_229(229, "TOPCARD"),
    CODIGO_230(230, "TOPPREMIUM"),
    CODIGO_231(231, "TRANSCHECK"),
    CODIGO_232(232, "TRICARD"),
    CODIGO_233(233, "TRNCENTRE"),
    CODIGO_234(234, "UNIK"),
    CODIGO_235(235, "UNNISA"),
    CODIGO_236(236, "USECRED"),
    CODIGO_237(237, "VAELETRONICO"),
    CODIGO_238(238, "VALE MAIS"),
    CODIGO_239(239, "VALECARD"),
    CODIGO_240(240, "VALECASH"),
    CODIGO_241(241, "VALEFROTA"),
    CODIGO_242(242, "VALEGAS"),
    CODIGO_243(243, "VALESHOP"),
    CODIGO_244(244, "VALETIK"),
    CODIGO_245(245, "VEGAS CARD"),
    CODIGO_246(246, "VERDECARD"),
    CODIGO_247(247, "VEROCHEQUE"),
    CODIGO_248(248, "VIA FINANCEIRA"),
    CODIGO_249(249, "VIDALINK"),
    CODIGO_250(250, "VISA"),
    CODIGO_251(251, "VIVO"),
    CODIGO_252(252, "VIVO OFF-LINE"),
    CODIGO_253(253, "VR ALIMENTACAO"),
    CODIGO_254(254, "VR AUTO"),
    CODIGO_255(255, "VR CULTURA"),
    CODIGO_256(256, "VR REFEICAO"),
    CODIGO_257(257, "ZOGBI"),
    CODIGO_259(259, "SODEXO COMBUSTÍVEL"),
    CODIGO_260(260, "ALGORIX"),
    CODIGO_261(261, "ALGORIX_S"),
    CODIGO_262(262, "SAVS"),
    CODIGO_263(263, "VEROCHEQUE CREDITO"),
    CODIGO_265(265, "CREDZ"),
    CODIGO_266(266, "ESPLANADA"),
    CODIGO_267(267, "SODEXO"),
    CODIGO_268(268, "ELO REFEICAO"),
    CODIGO_269(269, "ELO ALIMENTACAO"),
    CODIGO_270(270, "PBM PADRAO"),
    CODIGO_271(271, "CIELO AUTO"),
    CODIGO_273(273, "BIGCARD DEB"),
    CODIGO_276(276, "TICKET ALIMENTACAO"),
    CODIGO_277(277, "ALELO REFEICAO"),
    CODIGO_278(278, "ALELO ALIMENTACAO"),
    CODIGO_279(279, "BAHAMAS CRED"),
    CODIGO_280(280, "BAHAMAS ALIM"),
    CODIGO_281(281, "BANESCARD DEBITO"),
    CODIGO_283(283, "FLEETCOR"),
    CODIGO_284(284, "MAXXCARD"),
    CODIGO_285(285, "METTACARD"),
    CODIGO_286(286, "REDE CRED"),
    CODIGO_287(287, "REDE DEB"),
    CODIGO_288(288, "REDE VOUCHER"),
    CODIGO_289(289, "HIPER DEB"),
    CODIGO_290(290, "ALGORIX-VOUCHER"),
    CODIGO_291(291, "CREDIALIMENTACAO"),
    CODIGO_292(292, "VISA VALE"),
    CODIGO_293(293, "UAI CARD"),
    CODIGO_294(294, "REDEMED"),
    CODIGO_295(295, "PARACATUCARD"),
    CODIGO_296(296, "VALECARD DEB"),
    CODIGO_297(297, "MULTIPLUS"),
    CODIGO_298(298, "POLICARD CREDITO"),
    CODIGO_299(299, "POLICARD DEBITO"),
    CODIGO_300(300, "POLICARD ALIMENTACAO"),
    CODIGO_301(301, "POLICARD REFEICAO"),
    CODIGO_302(302, "GOODCARD ALIMENTACAO"),
    CODIGO_303(303, "GOODCARD REFEICAO"),
    CODIGO_304(304, "ALELO REFEIÇÃO"),
    CODIGO_305(305, "ALELO ALIMENTAÇÃO"),
    CODIGO_306(306, "VEGAS CARD"),
    CODIGO_307(307, "VEGAS CARD"),
    CODIGO_308(308, "VALLE EXPRESS");

    private Integer codigo;
    private String descricao;

    private BandeirasCapptaEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static List getSelectListTipo() {
        List temp = new ArrayList<BandeirasCapptaEnum>();
        temp.add(new SelectItem(null, "(Nenhum)"));
        for (int i = 0; i < BandeirasCapptaEnum.values().length; i++) {
            BandeirasCapptaEnum obj = BandeirasCapptaEnum.values()[i];
            temp.add(new SelectItem(obj, obj.getDescricao()));
        }
        return temp;
    }

    public static BandeirasCapptaEnum obterPorCodigo(Integer codigo) {
        for (BandeirasCapptaEnum op : BandeirasCapptaEnum.values()) {
            if (op.getCodigo().equals(codigo)) {
                return op;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

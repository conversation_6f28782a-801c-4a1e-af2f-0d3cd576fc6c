package negocio.comuns.financeiro.enumerador;

public enum PactoPayEnvioEmailStatusEnum {

    NENHUM(0, "NENHUM"),
    CRIADO(1, "CRIADO"),
    ENVIADO_JENKINS(2, "ENVIADO_JENKINS"),
    ENVIADO_SENDY(3, "ENVIADO_SENDY"),
    ERRO(4, "ERRO"),
    ;

    private int id;
    private String identificador;

    PactoPayEnvioEmailStatusEnum(int id, String identificador) {
        this.id = id;
        this.identificador = identificador;
    }

    public int getId() {
        return id;
    }

    public String getIdentificador() {
        return identificador;
    }

    public static PactoPayEnvioEmailStatusEnum obterPorId(int id) {
        for (PactoPayEnvioEmailStatusEnum tipo : values()) {
            if (tipo.getId() == id) {
                return tipo;
            }
        }
        return null;
    }
}

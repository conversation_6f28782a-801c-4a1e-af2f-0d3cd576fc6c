package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 07/12/2021
 */
public enum SituacaoBoletoEnum {

    NENHUMA(0, "", ""),
    GERADO(1, "Gerado", ""),
    ERRO(2, "<PERSON>rro", "#FFD42E"),
    AGUARDANDO_REGISTRO(3, "Aguardando registro", "#00008B"),
    AGUARDANDO_PAGAMENTO(4, "Aguardando Pagamento", "#00008B"),
    PAGO(5, "Pago", "#008800"),
    CANCELADO(6, "Cancelado", "#F3CEDB"),
    ESTORNADO(7, "E<PERSON>rna<PERSON>", "#961EE6"),
    REJEITADO(8, "Rejeitado", "#DF0000"),
    CANCELAMENTO_PENDENTE(9, "Cancelamento Pendente", "");

    private Integer codigo;
    private String descricao;
    private String styleCor;

    SituacaoBoletoEnum(Integer codigo, String descricao, String styleCor) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.styleCor = styleCor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getStyleCor() {
        return styleCor;
    }

    public static SituacaoBoletoEnum obterPorCodigo(Integer codigo) {
        for (SituacaoBoletoEnum origem : SituacaoBoletoEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }
}

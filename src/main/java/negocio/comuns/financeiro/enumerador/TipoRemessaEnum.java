/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

/**
 * <AUTHOR>
 */
public enum TipoRemessaEnum {

    DESCONHECIDO(0, "Desconhecido", "", TipoTransacaoEnum.NENHUMA),
    APROVA_FACIL(1, "Aprova Fácil", "", TipoTransacaoEnum.NENHUMA),
    EDI_CIELO(2, "Cielo DCC", "CIELOEDIDCC", TipoTransacaoEnum.NENHUMA),
    BB_DCO(3, "BB DCO", "BBDCO", TipoTransacaoEnum.NENHUMA),
    BRADESCO_DCO(4, "BRADESCO DCO", "BRADESCODCO", TipoTransacaoEnum.NENHUMA),
    ITAU_DCO(5, "ITAU DCO", "ITAUDCO", TipoTransacaoEnum.NENHUMA),
    CAIXA_DCO(6, "CAIXA DCO", "CAIXADCO", TipoTransacaoEnum.NENHUMA),
    HSBC_DCO(7, "HSBC DCO", "HSBCDCO", TipoTransacaoEnum.NENHUMA),
    GET_NET(8, "GET NET DCO", "GETNETDCC", TipoTransacaoEnum.NENHUMA),
    ITAU_BOLETO(9, "ITAU BOLETO", "ITAUBOLETO", TipoTransacaoEnum.NENHUMA),
    ITAU_CNAB400(25, "ITAU BOLETO CNAB400", "ITAUBOLETOCNAB400", TipoTransacaoEnum.NENHUMA),
    BOLETO(10, "Boleto Bancário", "BOLETOBANCARIO", TipoTransacaoEnum.NENHUMA),
    SANTANDER_DCO(11, "SANTANDER DCO", "SANTANDERDCO", TipoTransacaoEnum.NENHUMA),
    DCC_BIN(12, "BIN DCC", "BINDCC", TipoTransacaoEnum.NENHUMA),
    DCC_VINDI(13, "VINDI DCC","VINDIDCC", TipoTransacaoEnum.VINDI),
    DCC_CIELO_ONLINE(14, "DCC CIELO ONLINE","DCCCIELOONLINE", TipoTransacaoEnum.CIELO_ONLINE),
    DCC_MAXIPAGO(16, "MAXIPAGO DCC","MAXIPAGODCC", TipoTransacaoEnum.MAXIPAGO),
    DCC_E_REDE(17, "e-Rede DCC","EREDEDCC", TipoTransacaoEnum.E_REDE),
    DAYCOVAL_BOLETO(18, "DAYCOVAL BOLETO", "DAYCOVALBOLETO", TipoTransacaoEnum.NENHUMA),
    DCC_FITNESS_CARD(19, "DCC FITNESS CARD","DCCFITNESSCARD", TipoTransacaoEnum.FITNESS_CARD),
    DCC_GETNET_ONLINE(20, "DCC GETNET ONLINE","DCCGETNETONLINE", TipoTransacaoEnum.GETNET_ONLINE),
    DCC_STONE(21, "STONE DCC", "STONEDCC", TipoTransacaoEnum.STONE_ONLINE),
    CAIXA_SICOV_DCO(22, "CAIXA SICOV DCO", "CAIXASICOVDCO", TipoTransacaoEnum.NENHUMA),
    DCC_MUNDIPAGG(23, "DCC Mundipagg", "DCCMUNDIPAGG", TipoTransacaoEnum.MUNDIPAGG),
    DCC_PAGAR_ME(24, "DCC Pagar.me", "DCCPAGARME", TipoTransacaoEnum.PAGAR_ME),
    PIX(25, "Pix" ,"PIX", TipoTransacaoEnum.PACTO_PAY),
    DCC_STRIPE(26, "DCC Stripe", "DCCSTRIPE", TipoTransacaoEnum.STRIPE),
    DCC_PAGOLIVRE(27, "DCC PagoLivre","DCCPAGOLIVRE", TipoTransacaoEnum.PAGOLIVRE),
    DCC_PINBANK(28, "DCC PinBank","DCCPINBANK", TipoTransacaoEnum.PINBANK),
    DCC_ONE_PAYMENT(29, "DCC One Payment","DCCONEPAYMENT", TipoTransacaoEnum.ONE_PAYMENT),
    DCC_FACILITEPAY(30, "DCC Fypay","DCCFACILITEPAY", TipoTransacaoEnum.FACILITEPAY),
    DCC_FACILITEPAY_MS(31, "DCC FacilitePay Ms","DCCFACILITEPAYMS", TipoTransacaoEnum.FACILITEPAY_MS),
    DCC_CEOPAG(32, "DCC Ceopag","DCCCEOPAG", TipoTransacaoEnum.CEOPAG),
    DCC_CAIXA_ONLINE(33, "DCC Caixa Online","DCCCAIXAONLINE", TipoTransacaoEnum.DCC_CAIXA_ONLINE),
    DCC_PAGBANK(34, "DCC PagBank", "DCCPAGBANK", TipoTransacaoEnum.PAGBANK),
    DCC_STONE_ONLINE_V5(35, "DCC Stone Online v5","DCCSTONEONLINEV5", TipoTransacaoEnum.DCC_STONE_ONLINE_V5),
    ;


    private int id;
    private String descricao;
    private String identificador;
    private TipoTransacaoEnum tipoTransacao;

    private TipoRemessaEnum(int id, String descricao, String identificador, TipoTransacaoEnum tipoTransacao) {
        this.id = id;
        this.descricao = descricao;
        this.identificador = identificador;
        this.tipoTransacao = tipoTransacao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public static TipoRemessaEnum getTipoRemessaEnum(final int codigo) {
        for (TipoRemessaEnum tipo : TipoRemessaEnum.values()) {
            if (tipo.getId() == codigo) {
                return tipo;
            }
        }
        return TipoRemessaEnum.DESCONHECIDO;
    }

    public String getIdent(final int id, final String codigoEstabelecimento, Date dataRemessa) {
        if (this == BRADESCO_DCO) {
            return String.format("CB%s%s", Calendario.getData("ddMM"), StringUtilities.formatarCampoForcandoZerosAEsquerda(id, 2));
        } else if (this == ITAU_DCO) {
            return String.format("I%s", StringUtilities.formatarCampoForcandoZerosAEsquerda(id, 2));
        } else if (this == ITAU_BOLETO) {
            return String.format("IB%s%s", Calendario.getData("ddMM"), StringUtilities.formatarCampoForcandoZerosAEsquerda(id, 2));
        } else if (this == DAYCOVAL_BOLETO) {
            return String.format("ZEG%s%s", Calendario.getData("ddMM"), StringUtilities.formatarCampoForcandoZerosAEsquerda(id, 1));
        } else if (this == GET_NET) {
            if (dataRemessa != null) {
                return String.format("PGRCR_%s_%s_%s", codigoEstabelecimento, Calendario.getDataAplicandoFormatacao(dataRemessa,"yyyyMMdd"), "XXXX");
            } else {
                return String.format("PGRCR_%s_%s_%s", codigoEstabelecimento, Calendario.getData("yyyyMMdd"), "XXXX");
            }
        } else if (this == BOLETO) {
            return String.format("CB%s%s", Calendario.getData("ddMM"), StringUtilities.formatarCampoForcandoZerosAEsquerda(id, 2));
        } else if(this == DCC_BIN){
            return String.format("BRPACT01.REM-%s-%s-%s", identificador, Calendario.getData("yyyyMMddHHmmss"), id);
        }else{
            return String.format("REM-%s-%s-%s", identificador, Calendario.getData("yyyyMMddHHmmssSSS"), id);
        }
    }

    public String getIdentCancelamento(final int id, final String codigoEstabelecimento) {
        if (this == GET_NET) {
            return String.format("CANEL_%s_%s_%s", codigoEstabelecimento, Calendario.getData("yyyyMMdd"), StringUtilities.formatarCampoForcandoZerosAEsquerda(id, 9));
        } else {
            return String.format("REM-%s-%s-%s-CA", identificador, Calendario.getData("yyyyMMddHHmmss"), id);
        }
    }

    public String getNomeArquivo(final int id, final String codigoEstabelecimento, Date dataRemessa) {
        if (this == BRADESCO_DCO) {
            return String.format("CB%s%s", Calendario.getData("ddMM"), StringUtilities.formatarCampoForcandoZerosAEsquerda(id, 2));
        } else if (this == ITAU_DCO) {
            return String.format("I%s", StringUtilities.formatarCampoForcandoZerosAEsquerda(id, 2));
        } else if (this == DAYCOVAL_BOLETO) {
            return String.format("ZEG%s%s", Calendario.getData("ddMM"), StringUtilities.formatarCampoForcandoZerosAEsquerda(id, 1));
        } else if (this == ITAU_BOLETO) {
            return String.format("IB%s%s", Calendario.getData("ddMM"), StringUtilities.formatarCampoForcandoZerosAEsquerda(id, 2));
        } else if (this == GET_NET || this == DCC_BIN) {
            return getIdent(id, codigoEstabelecimento, dataRemessa);
        } else if (this == BOLETO) {
            return String.format("CB%s%s", Calendario.getData("ddMM"), StringUtilities.formatarCampoForcandoZerosAEsquerda(id, 2));
        } else {
            return String.format("REM%s-%s", Calendario.getData("yyMMddHHmmssSSS"), id);
        }
    }

    public String getNomeArquivoCancelamento(final int id, final String codigoEstabelecimento) {
        if (this == GET_NET) {
            return getIdentCancelamento(id, codigoEstabelecimento);
        } else {
            return String.format("REM%s_CA", Calendario.getData("yyMMddHHmmss"));
        }
    }

    public TipoTransacaoEnum getTipoTransacao() {
        return tipoTransacao;
    }

    public void setTipoTransacao(TipoTransacaoEnum tipoTransacao) {
        this.tipoTransacao = tipoTransacao;
    }

    public boolean isDCO() {
        return this == BB_DCO ||
                this == BRADESCO_DCO ||
                this == ITAU_DCO ||
                this == CAIXA_DCO ||
                this == HSBC_DCO ||
                this == SANTANDER_DCO ||
                this == CAIXA_SICOV_DCO;
    }

    public boolean isPix(){
        return this == PIX;
    }
}

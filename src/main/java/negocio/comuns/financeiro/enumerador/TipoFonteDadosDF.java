/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum TipoFonteDadosDF {

   ZILLYON_WEB(1,"ADM"),
   FINANCEIRO(2,"Financeiro"),
   CENTRAL_EVENTOS(4, "Central de Eventos"),
   TODAS_MENOS_FINANCEIRO(5, "Todas, exceto Financeiro"),
   TODAS(3,"Todas")
   ;

   private int codigo;
   private String descricao;

   TipoFonteDadosDF(int codigo, String descricao){
     setCodigo(codigo);
     setDescricao(descricao);
   }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TipoFonteDadosDF getTipoFonteDadosDF(int codigo){
        TipoFonteDadosDF tipo = null;
        for (TipoFonteDadosDF obj: TipoFonteDadosDF.values()){
            if (obj.getCodigo() == codigo){
                tipo = obj;
                break;
            }
        }

        return tipo;

    }
    
}

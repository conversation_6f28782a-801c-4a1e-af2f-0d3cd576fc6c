package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 07/12/2021
 */
public enum TipoBoletoEnum {

    NENHUMA(0, "Nenhuma"),
    PJ_BANK(1, "PJBank"),
    ITAU(2, "Itau"),
    ASAAS(3, "Asaa<PERSON>"),
    CAIXA(4, "Caixa"),
    BANCO_BRASIL(5, "Banco Brasil"),
    ;

    private Integer codigo;
    private String descricao;

    TipoBoletoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoBoletoEnum obterPorCodigo(Integer codigo) {
        for (TipoBoletoEnum origem : TipoBoletoEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }
}

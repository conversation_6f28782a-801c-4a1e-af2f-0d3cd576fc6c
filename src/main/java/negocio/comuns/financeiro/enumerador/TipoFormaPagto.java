/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro.enumerador;

import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;

/**
 * <AUTHOR>
 */
public enum TipoFormaPagto {

    AVISTA(1, "AV", "Á Vista", "Pagamentos diferente de Cheque e Cartão de Crédito"),
    CHEQUE(2, "CH", "Cheque", "Pagamentos em Cheque"),
    CARTAOCREDITO(3, "CA", "Cartão de Crédito", "Pagamentos em Cartão de Crédito"),
    CARTAODEBITO(4, "CD", "Cartão de Débito", ""),
    CONVENIO(5, "CO", "Convênio", ""),
    CREDITOCONTACORRENTE(6, "CC", "Crédito em Conta Corrente", ""),
    BOLETOBANCARIO(7, "BB", "Boleto Bancário", ""),
    PAGAMENTODIGITAL(8, "PD", "Pagamento Digital", ""),
    LOTE(9, "LO", "Lote", "Pagamento com lote"),
    PARCEIRO_FIDELIDADE(10, "PF", "Parceiro Fidelidade", "Pagamento utilizando pontos de fidelidade"),
    TRANSFERENCIA_BANCARIA(11, "TB", "Transferência Bancária", "Pagamentos realizados por transferência bancária"),
    PIX(12, "PX", "Pix", "Pix é um meio de pagamento eletrônico do Brasil lançado oficialmente no dia 5 de outubro de 2020. Suas chaves de transação (conhecidas como chaves Pix) podem ser cadastradas utilizando os números do telefone, CPF ou endereço de e-mail do usuário, ou mesmo através de uma chave aleatória que possibilita o acesso aos dados bancários do usuário da conta e realizar a transação imediatamente.");

    private int codigo;
    private String descricao;
    private String sigla;
    private String hintExplicativo;

    TipoFormaPagto(int codigo, String sigla, String descricao, String hintExplicativo) {
        setSigla(sigla);
        setCodigo(codigo);
        setDescricao(descricao);
        setHintExplicativo(hintExplicativo);
    }

    public static TipoFormaPagto getTipoFormaPagto(int codigo) {
        TipoFormaPagto tipoRetornar = null;
        for (TipoFormaPagto tipo : TipoFormaPagto.values()) {
            if (tipo.getCodigo() == codigo) {
                tipoRetornar = tipo;
                break;
            }
        }
        return tipoRetornar;
    }


    public static TipoFormaPagto getTipoFormaPagtoSigla(final String sigla) {
        if (StringUtils.isBlank(sigla)) {
            return null;
        }

        for (TipoFormaPagto tipo : TipoFormaPagto.values()) {
            if (tipo.getSigla().equalsIgnoreCase(sigla)) {
                return tipo;
            }
        }

        return null;
    }

    public boolean isCartao() {
        switch (this) {
            case CARTAOCREDITO:
            case CARTAODEBITO:
                return true;
            default:
                return false;
        }
    }

    public boolean isNotCartao() {
        return !isCartao();
    }

    public boolean isCreditoContaCorrente() {
        switch (this) {
            case CREDITOCONTACORRENTE:
                return true;
            default:
                return false;
        }
    }

    public boolean isNotCreditoContaCorrente() {
        return !isCreditoContaCorrente();
    }


    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getHintExplicativo() {
        return hintExplicativo;
    }

    public void setHintExplicativo(String hintExplicativo) {
        this.hintExplicativo = hintExplicativo;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }


    public static TipoFormaPagto getTipoFormaProtheus(String sigla) {
        if(sigla.equals("CC")){
            return TipoFormaPagto.CARTAOCREDITO;
        }else if(sigla.equals("CD")){
            return TipoFormaPagto.CARTAODEBITO;
        }else if(sigla.equals("BB")){
            return TipoFormaPagto.BOLETOBANCARIO;
        }else{
            return TipoFormaPagto.AVISTA;
        }

    }

    public JSONObject toJSON() {
        return new JSONObject(this);
    }
}

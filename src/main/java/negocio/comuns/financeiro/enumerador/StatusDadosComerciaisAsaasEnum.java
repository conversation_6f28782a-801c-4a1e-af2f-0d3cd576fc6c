package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 11/05/2023
 */
public enum StatusDadosComerciaisAsaasEnum {

    NENHUMA(0, "Nenhuma"),
    APPROVED(1, "APROVADA"),
    AWAITING_ACTION_AUTHORIZATION(2, "AGUARDANDO AUTORIZAÇÃO"),
    DENIED(3, "NEGADA"),
    PENDING(4, "PENDENTE"),
    ;

    private Integer codigo;
    private String descricao;

    StatusDadosComerciaisAsaasEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static StatusDadosComerciaisAsaasEnum obterPorCodigo(Integer codigo) {
        for (StatusDadosComerciaisAsaasEnum origem : StatusDadosComerciaisAsaasEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }

    public static StatusDadosComerciaisAsaasEnum obterPorDescricao(String descricao) {
        for (StatusDadosComerciaisAsaasEnum obj : StatusDadosComerciaisAsaasEnum.values()) {
            if (obj.name().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUMA;
    }
}

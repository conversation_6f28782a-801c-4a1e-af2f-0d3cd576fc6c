package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 05/05/2023
 */
public enum TipoDescontoAsaasEnum {

    NENHUMA(0, "Nenhuma"),
    FIXED(1, "Fixo"),
    PERCENTAGE(2, "Percentagem"),
    ;

    private Integer codigo;
    private String descricao;

    TipoDescontoAsaasEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoDescontoAsaasEnum obterPorCodigo(Integer codigo) {
        for (TipoDescontoAsaasEnum origem : TipoDescontoAsaasEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }
}

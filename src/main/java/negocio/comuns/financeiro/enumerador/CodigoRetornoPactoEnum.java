/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 09/05/2018
 *
 * Padronizar os códigos de retorno das adquirentes, criando assim um código Pacto
 *
 */
public enum CodigoRetornoPactoEnum {

    SUCESSO                 ("PAC000", "Cobrança realizada com sucesso.", OperacaoRetornoCobrancaEnum.NENHUM, false),
    OUTRO                   ("PAC001", "Verifique o retorno da adquirente.", OperacaoRetornoCobrancaEnum.OUTRO, false),

    //cartão sem saldo pode realizar tentativa de cobrança outro dia..
    SALDO_INSUFICIENTE      ("PAC002", "Saldo/limite no cartão insuficiente para a cobrança.", OperacaoRetornoCobrancaEnum.REENVIAR, false),

    //cartão vencido.. não permite retentativa (cuidado ao validar pois cliente pode ter informado o vencimento errado)
    CARTAO_VENCIDO          ("PAC003", "Cartão está vencido.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, true),

    //cartão que pode ser desbloqueado e pode voltar a ter tentativa de cobrança
    CARTAO_BLOQUEADO_TEMPORARIO        ("PAC004", "Cartão está bloqueado temporariamente.", OperacaoRetornoCobrancaEnum.REENVIAR, false),

    //cartão que pode não pode ser desbloqueado... não permite retentativa
    CARTAO_BLOQUEADO_PERMANENTE        ("PAC005", "Cartão está bloqueado permanentemente.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, true),

    //cartão inválido ou inexistente
    CARTAO_INVALIDO         ("PAC006", "Cartão inválido.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, true),

    //cartão que pode foi cancelado não permite retentativa
    CARTAO_CANCELADO        ("PAC007", "Cartão está cancelado.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, true),

    //conta ou cartão foi encerrada.. não permite retentativa
    CARTAO_CONTA_ENCERRADA  ("PAC008", "Conta ou cartão encerrado ou bloqueado.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, true),

    //bloqueio por limite de transação com o mesmo número de cartão
    BLOQUEIO_AGUARDAR  ("PAC009", "Bloqueio devido configuração de somente uma tentativa por cartão em cada processo automático.", OperacaoRetornoCobrancaEnum.REENVIAR, false),
    //bloqueio por limite de tentativa com o mesmo número de cartão/bandeira na adquirente
    BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA  ("PAC010", "Bloqueio devido retentativas excedidas do mesmo cartão para a bandeira.", OperacaoRetornoCobrancaEnum.REENVIAR, false),
    BLOQUEIO_OPERADORA_NAO_PERMITE_RETENTATIVA_NO_CARTAO  ("PAC011", "Bloqueio devido bandeira não permitir mais retentativas desse cartão.", OperacaoRetornoCobrancaEnum.REENVIAR, false),
    BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_24_HORAS("PAC012", "Bloqueio devido retentativas excedidas do mesmo cartão para a bandeira nas últimas 24 horas.", OperacaoRetornoCobrancaEnum.REENVIAR, false),
    BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_30_DIAS("PAC013", "Bloqueio devido retentativas excedidas do mesmo cartão para a bandeira nos últimos 30 dias.", OperacaoRetornoCobrancaEnum.REENVIAR, false),
    BLOQUEIO_LIMITE_TENTATIVA_CARTAO_ULT_TENTATIVA_E_IRREVERSIVEL("PAC014", "A adquirente/bandeira nos devolveu a última tentativa de cobrança deste cartão como IRREVERSÍVEL. Não será possível tentar novamente, necessário a troca do cartão.", OperacaoRetornoCobrancaEnum.REENVIAR, true),
    BLOQUEIO_LIMITE_TENTATIVA_CARTAO_ULT_TENTATIVA_E_IRREVERSIVEL_ESTABELECIMENTO("PAC015", "A última tentativa de cobrança nesse estabelecimento foi devolvida como irreversível. Necessário realizar a verificação das credenciais do convênio.", OperacaoRetornoCobrancaEnum.REENVIAR, true),
    BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_MES_VIGENTE("PAC016", "Bloqueio devido retentativas excedidas do mesmo cartão para a bandeira no mês vigente.", OperacaoRetornoCobrancaEnum.REENVIAR, false),
    ;

    private String codigo;
    private String descricao;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobrancaEnum;
    private boolean irreversivel;

    private CodigoRetornoPactoEnum(String codigo, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobrancaEnum, boolean irreversivel) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.operacaoRetornoCobrancaEnum = operacaoRetornoCobrancaEnum;
        this.irreversivel = irreversivel;
    }

    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static List getSelectListRetornoPactoPagamento() {
        List<SelectItem> temp = new ArrayList<SelectItem>();
        temp.add(new SelectItem(null, "(Todas)"));
        for (CodigoRetornoPactoEnum tipo : CodigoRetornoPactoEnum.values()) {
            temp.add(new SelectItem(tipo, tipo.getDescricao()));
        }
        return temp;
    }

    public static CodigoRetornoPactoEnum consultarPorCodigo(String codigo) {
        for (CodigoRetornoPactoEnum amb : CodigoRetornoPactoEnum.values()) {
            if (amb.getCodigo().equals(codigo)) {
                return amb;
            }
        }
        return null;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobrancaEnum() {
        return operacaoRetornoCobrancaEnum;
    }

    public boolean isIrreversivel() {
        return irreversivel;
    }
}

package negocio.comuns.financeiro.enumerador;

public enum StatusPactoPayComunicacaoEnum {

    NENHUM(0, "NENHUM"),
    GERADO(1, "GERADO"),
    PROCESSADO(2, "PROCESSADO"),
    ERRO(3, "ERRO"),
    FILA_ENVIO_MASSA(4, "FILA_ENVIO_MASSA"),
    ;

    private int id;
    private String identificador;

    StatusPactoPayComunicacaoEnum(int id, String identificador) {
        this.id = id;
        this.identificador = identificador;
    }

    public int getId() {
        return id;
    }

    public String getIdentificador() {
        return identificador;
    }

    public static StatusPactoPayComunicacaoEnum obterPorId(int id) {
        for (StatusPactoPayComunicacaoEnum tipo : values()) {
            if (tipo.getId() == id) {
                return tipo;
            }
        }
        return null;
    }
}

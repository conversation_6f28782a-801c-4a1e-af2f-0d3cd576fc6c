/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import java.util.ArrayList;

/**
 *
 * <AUTHOR>
 */
public enum IndicadoresDadosGerencialEnum {
    
    MOVIMENTACAO_CONTRATO(1, "MC","Movimentaçao de Contratos"),
    INDICE_CONVERSAO(2, "IC","Indice Conversão de Vendas"),
    INDICE_RENOVACAO(3, "IR","Indice de Renovação"),
    QUANTIDADE_PLANOS(4,"QP", "Contratos Faturados por Duração"),
    PENDENCIAS(5,"PE", "Pendências"),
    FINANCEIROS(6,"FI", "Indicadores Financeiros"),
    CRM(6,"CR", "CRM"),
    GOR(7,"GR", "Índice criado para ser usado apenas no Game OF Results"),
    LTV(8,"LT", "LTV - Ciclo de vida do seu cliente"),
    ADM(9,"AD", "Adm"),
    TICKET_MEDIO(10,"TM", "Ticket Médio"),

    ;

    private Integer id;
    private String sigla;
    private String descricao;
    
     IndicadoresDadosGerencialEnum(final Integer id, final String sigla, final String descricao) {
        this.id = id;
        this.sigla= sigla;
        this.descricao = descricao;
    }


    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }
    
    public static IndicadoresDadosGerencialEnum getIndicador(Integer codigo) {
        IndicadoresDadosGerencialEnum  indicador = null;
        for (IndicadoresDadosGerencialEnum  indicadorPMG : IndicadoresDadosGerencialEnum .values()) {
            if (indicadorPMG.getId().equals(codigo)) {
                indicador = indicadorPMG;
            }
        }
        return indicador;
    }

    public static IndicadoresDadosGerencialEnum getIndicadorPorSigla(String sigla) {
        IndicadoresDadosGerencialEnum indicador = null;
        for (IndicadoresDadosGerencialEnum indicadorPMG : IndicadoresDadosGerencialEnum.values()) {
            if (indicadorPMG.getSigla().equals(sigla)) {
                indicador = indicadorPMG;
            }
        }
        return indicador;
    }

    public static String getIdentificador(String sigla) {
        IndicadoresDadosGerencialEnum ident = null;
        for (IndicadoresDadosGerencialEnum identEnum : IndicadoresDadosGerencialEnum.values()) {
            if (identEnum.getSigla().equals(sigla)) {
                ident = identEnum;
            }
        }
        if (ident == null) {
            return "";
        }
        return ident.getDescricao();
    }
    
}

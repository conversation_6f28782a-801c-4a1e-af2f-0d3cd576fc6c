package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 09/07/2024
 * <p>
 */
public enum StatusLoteKobanaEnum {
    //De início foi implementado os status de lote de acordo com a documentação da API da Kobana,
    // porém depois a Kobana disse que eles não são importantes e que não se atualizam corretamente, por isso serão criados novos genéricos...
    NENHUM(0, "Nenhum", "#777777", "Não foi possível obter o status do lote"),
    GERADO(1, "Gerado", "#00008B", "Lote gerado com sucesso! Agora você precisa acompanhar os status dos pagamentos dentro do lote."),
    PAC_ERROR(2, "PAC - Erro", "#FFD42E", ""),
    RECUSADO(3, "Recusado", "#DF0000", "Lote recusado"),
    APPROVED(4, "Aprovado", "#378ea3", "Este lote foi aprovado por um usuário"),
    REPROVED(5, "Reprovado", "#DF0000", "Este lote foi reprovado por um usuário"),
    AWAITING_APPROVAL(6, "Aguardando Aprovação", "#3c3c82", "Lote aguardando aprovação do usuário"),
    PENDING(7, "Pendente", "#00008B", "Lote pendente"),
    CONFIRMED(8, "Confirmado", "#4ab550", "Lote confirmado"),
    REJECTED(9, "Rejeitado", "#DF0000", "Lote rejeitado"),
    SCHEDULED(10, "Agendado", "#777777", "Lote Agendado"),
    ;

    private final Integer codigo;
    private final String nomeApresentar;
    private final String color;
    private final String descricao;
    ;

    StatusLoteKobanaEnum(int codigo, String nomeApresentar, String color, String descricao) {
        this.codigo = codigo;
        this.nomeApresentar = nomeApresentar;
        this.color = color;
        this.descricao = descricao;
    }

    public static StatusLoteKobanaEnum obterPorCodigo(Integer codigo) {
        for (StatusLoteKobanaEnum status : values()) {
            if (status.getCodigo().equals(codigo)) {
                return status;
            }
        }
        return StatusLoteKobanaEnum.NENHUM;
    }

    public static StatusLoteKobanaEnum obterPorValue(String value) {
        for (StatusLoteKobanaEnum obj : StatusLoteKobanaEnum.values()) {
            if (obj.name().equals(value.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public static StatusLoteKobanaEnum obterPorDescricao(String descricao) {
        for (StatusLoteKobanaEnum obj : StatusLoteKobanaEnum.values()) {
            if (obj.getNomeApresentar().toUpperCase().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getNomeApresentar() {
        return nomeApresentar;
    }

    public String getColor() {
        return color;
    }

    public String getDescricao() {
        return descricao;
    }
}

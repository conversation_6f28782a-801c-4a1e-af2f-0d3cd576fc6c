package negocio.comuns.financeiro.enumerador;

/**
 * <AUTHOR> 10/09/2021;
 */
public enum TipoContaMerchantPagoLivre {

    CONTA_CORRENTE(0, "Conta Corrente"),
    CONTA_POUPANCA(1, "Conta Poupanca");

    private Integer codigo;

    private String descricao;

    private TipoContaMerchantPagoLivre(Integer codigo, String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    }

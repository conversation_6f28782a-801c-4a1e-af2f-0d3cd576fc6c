package negocio.comuns.financeiro.enumerador;


/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 08/01/2020
 */
public enum TipoBoletoPJBankEnum {

    BOLETO(0, "Somente Boleto", ""),
    BOLETO_PIX(1, "Boleto e QR Code Pix (Híbrido)", "pix-e-boleto"),
    PIX(2, "Somente QR Code Pix", "pix");

    private int id;
    private String descricao;
    private String parametro;

    private TipoBoletoPJBankEnum(int id, String descricao, String parametro) {
        this.id = id;
        this.descricao = descricao;
        this.parametro = parametro;
    }

    public int getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getParametro() {
        return parametro;
    }

    public static TipoBoletoPJBankEnum obterPorId(int id) {
        for (TipoBoletoPJBankEnum tipo : TipoBoletoPJBankEnum.values()) {
            if (tipo.getId() == id) {
                return tipo;
            }
        }
        return null;
    }
}

package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 11/05/2023
 */
public enum StatusSituacaoCadastralAsaasEnum {

    NENHUMA(0, "Nenhuma"),
    NOT_SENT(1, "NÃO ENVIADO - Documentos ainda não enviados"),
    PENDING(2, "PENDENTE - Documentos em análise"),
    APPROVED(3, "APROVADO"),
    REJECTED(4, "REJECTED - Necessário reenvio de algum documento"),
    ;

    private Integer codigo;
    private String descricao;

    StatusSituacaoCadastralAsaasEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static StatusSituacaoCadastralAsaasEnum obterPorCodigo(Integer codigo) {
        for (StatusSituacaoCadastralAsaasEnum origem : StatusSituacaoCadastralAsaasEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }

    public static StatusSituacaoCadastralAsaasEnum obterPorDescricao(String descricao) {
        for (StatusSituacaoCadastralAsaasEnum obj : StatusSituacaoCadastralAsaasEnum.values()) {
            if (obj.name().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUMA;
    }
}

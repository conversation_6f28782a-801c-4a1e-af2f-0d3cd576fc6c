package negocio.comuns.financeiro.enumerador;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

public enum TipoConsultaMetaEnum {

	FATURAMENTO_RECEBIDO("Faturamento Recebido", 1),
	FATURAMENTO("Faturamento", 2), 
	RECEITA ("Receita", 3),
	COMPETENCIA("Competência", 4);
	
	private String descricao;
	private Integer codigo;
	
	private TipoConsultaMetaEnum(String descricao, Integer codigo){
		this.descricao = descricao;
		this.codigo = codigo;
	}
	
	public static List<SelectItem> getSelectItens(){
		List<SelectItem> itens = new ArrayList<SelectItem>();
		for(TipoConsultaMetaEnum consultarPor : TipoConsultaMetaEnum.values()){
			itens.add(new SelectItem(consultarPor.getCodigo(), consultarPor.getDescricao()));
		}
		return itens;
	}
	
	public static TipoConsultaMetaEnum getTipoPorCodigo(Integer codigo){
		TipoConsultaMetaEnum tipo = null;
		for(TipoConsultaMetaEnum consultarPor : TipoConsultaMetaEnum.values()){
			if(consultarPor.getCodigo().equals(codigo)){
				tipo = consultarPor;
			}
		}
		return tipo;
	}
	
	public String getDescricao() {
		return descricao;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public Integer getCodigo() {
		return codigo;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}
	
	

}

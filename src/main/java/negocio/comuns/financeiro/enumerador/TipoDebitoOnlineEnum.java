/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/*
 * Created by <PERSON><PERSON> on 31/08/2017.
 */
public enum TipoDebitoOnlineEnum {

    NENHUM(0, "(Nenhum)"),
    CIELO(1, "Cielo"),
    E_REDE(2, "e-Rede"),
    GEOCOM(3, "Geocom");

    private int id;
    private String descricao;

    private TipoDebitoOnlineEnum(int id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public static TipoDebitoOnlineEnum getTipoDebitoOnlineEnum(final int codigo) {
        for (TipoDebitoOnlineEnum tipo : TipoDebitoOnlineEnum.values()) {
            if (tipo.getId() == codigo) {
                return tipo;
            }
        }
        return TipoDebitoOnlineEnum.NENHUM;
    }

    public static List getSelectListTipo() {
        List temp = new ArrayList<TipoDebitoOnlineEnum>();
        for (TipoDebitoOnlineEnum tipo : TipoDebitoOnlineEnum.values()) {
            temp.add(new SelectItem(tipo, tipo.getDescricao()));
        }
        return temp;
    }
}

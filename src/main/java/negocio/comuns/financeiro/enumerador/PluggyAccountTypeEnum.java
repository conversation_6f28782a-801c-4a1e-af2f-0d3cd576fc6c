package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 10/04/2024
 */
public enum PluggyAccountTypeEnum {

    NENHUMA(0, ""),
    BANK(1, "<PERSON>tas"),
    CREDIT(2, "Cartões de Crédito");
    private Integer codigo;
    private String descricao;

    PluggyAccountTypeEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static PluggyAccountTypeEnum obterPorCodigo(Integer codigo) {
        for (PluggyAccountTypeEnum origem : PluggyAccountTypeEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }

    public static PluggyAccountTypeEnum obterPorDescricao(String descricao) {
        for (PluggyAccountTypeEnum obj : PluggyAccountTypeEnum.values()) {
            if (obj.name().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUMA;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}

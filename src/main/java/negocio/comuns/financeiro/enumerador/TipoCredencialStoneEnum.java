package negocio.comuns.financeiro.enumerador;

import negocio.comuns.utilitarias.Ordenacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 01/05/2020
 *
 * Enum utilizado no convenio de cobranca e na transação
 *
 */
public enum TipoCredencialStoneEnum {

    NENHUM(0, "NENHUM"),
    GATEWAY(1, "GATEWAY"),
    PSP(2, "PSP"),
    ;

    private Integer codigo;
    private String descricao;

    private TipoCredencialStoneEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoCredencialStoneEnum consultarPorCodigo(Integer codigo) {
        for (TipoCredencialStoneEnum amb : values()) {
            if (amb.getCodigo().equals(codigo)) {
                return amb;
            }
        }
        return NENHUM;
    }

    public static List<SelectItem> obterListSelectItem() {
        List<SelectItem> lista = new ArrayList<>();
        for (TipoCredencialStoneEnum amb : TipoCredencialStoneEnum.values()) {
            if (!amb.equals(TipoCredencialStoneEnum.NENHUM)) {
                lista.add(new SelectItem(amb, amb.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(TipoCredencialStoneEnum.NENHUM, ""));
        return lista;
    }

    public static List<SelectItem> obterListSelectItemParaUsoSplit() {
        List<SelectItem> lista = new ArrayList<>();
        for (TipoCredencialStoneEnum amb : TipoCredencialStoneEnum.values()) {
            if (!amb.equals(TipoCredencialStoneEnum.NENHUM) && !amb.equals(TipoCredencialStoneEnum.GATEWAY)) {
                lista.add(new SelectItem(amb, amb.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(TipoCredencialStoneEnum.NENHUM, ""));
        return lista;
    }
}

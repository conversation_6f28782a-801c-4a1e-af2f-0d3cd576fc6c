package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 21/07/2021
 */
public enum TipoTransactionPluggyEnum {

    NENHUMA(0, ""),
    DEBIT(1, "Pagamento"),
    CREDIT(2, "Recebimento");
    private Integer codigo;
    private String descricao;

    TipoTransactionPluggyEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoTransactionPluggyEnum obterPorCodigo(Integer codigo) {
        for (TipoTransactionPluggyEnum origem : TipoTransactionPluggyEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }

    public static TipoTransactionPluggyEnum obterPorDescricao(String descricao) {
        for (TipoTransactionPluggyEnum obj : TipoTransactionPluggyEnum.values()) {
            if (obj.name().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUMA;
    }
}

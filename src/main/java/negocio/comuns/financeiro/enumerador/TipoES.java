/**
 * 
 */
package negocio.comuns.financeiro.enumerador;

/**
 * <AUTHOR> felipe
 * 
 */
public enum TipoES {

    ENTRADA(1, "Entrada", "E", "E", "#008000"),
    SAIDA(2, "<PERSON><PERSON><PERSON>", "S", "S", "#da2128"),
    INVESTIMENTO(3, "Investimento", "I", "I", "green");

    private int codigo;
    private String descricao;
    private String descricaoCurta;
    private String sigla;
    private String cor;

    private TipoES(int codigo, String descricao,String descricaoCurta, String sigla, String cor) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.descricaoCurta = descricaoCurta;
        this.sigla = sigla;
        this.cor = cor;
    }

    public int getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public static TipoES getTipoPadrao(final int codigo) {
        TipoES obj = null;
        for (TipoES status : TipoES.values()) {
            if (status.getCodigo() == codigo) {
                obj = status;
            }
        }
        return obj;
    }

	public void setDescricaoCurta(String descricaoCurta) {
		this.descricaoCurta = descricaoCurta;
	}

	public String getDescricaoCurta() {
		return descricaoCurta;
	}

    public String getSigla() {
        return sigla;
    }

    public static TipoES getPorSigla(final String sigla) {
        for (TipoES status : TipoES.values()) {
            if (status.getSigla().equalsIgnoreCase(sigla)) {
                return status;
            }
        }
        return null;
    }

    public static TipoES getByName(final String name) {
        for (TipoES tipo : TipoES.values()) {
            if (tipo.name().equalsIgnoreCase(name)) {
                return tipo;
            }
        }
        return null;
    }

    public String getCor() {
        if (cor == null) {
            cor = "";
        }
        return cor;
    }
}

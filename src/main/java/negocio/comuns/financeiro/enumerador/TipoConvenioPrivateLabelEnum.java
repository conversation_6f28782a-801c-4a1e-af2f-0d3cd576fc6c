/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/*
 * Created by <PERSON><PERSON>
 */
public enum TipoConvenioPrivateLabelEnum {

    NENHUM(0, "Nenhum", ""),
    FITNESS_CARD(1, "Fitness Card", "http://*************/fitnesscard");

    private int id;
    private String descricao;
    private String urlAPI;

    TipoConvenioPrivateLabelEnum(int id, String descricao, String urlAPI) {
        this.id = id;
        this.descricao = descricao;
        this.urlAPI = urlAPI;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUrlAPI() {
        return urlAPI;
    }

    public void setUrlAPI(String urlAPI) {
        this.urlAPI = urlAPI;
    }

    public static TipoConvenioPrivateLabelEnum getTipoTransacaoEnum(final int codigo) {
        for (TipoConvenioPrivateLabelEnum tipo : TipoConvenioPrivateLabelEnum.values()) {
            if (tipo.getId() == codigo) {
                return tipo;
            }
        }
        return TipoConvenioPrivateLabelEnum.NENHUM;
    }

    public static List getSelectListTipoConvenioPrivateLabel() {
        List<SelectItem> temp = new ArrayList<SelectItem>();
        for (TipoConvenioPrivateLabelEnum tipo : TipoConvenioPrivateLabelEnum.values()) {
            temp.add(new SelectItem(tipo.getId(), tipo.getDescricao()));
        }
        return temp;
    }
}

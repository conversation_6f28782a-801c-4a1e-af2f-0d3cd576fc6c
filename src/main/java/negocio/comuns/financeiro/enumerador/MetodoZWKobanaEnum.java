package negocio.comuns.financeiro.enumerador;

/**
 * Created with <PERSON>li<PERSON> IDEA.
 * User: <PERSON>
 * Date: 11/07/2024
 * <p>
 */
public enum MetodoZWKobanaEnum {
    NENHUM(0, "nenhum"),
    CRIAR_NOVO_LOTE_BOLETO_ERRO_REQUISICAO(1, "CRIAR_NOVO_LOTE_BOLETO_ERRO_REQUISICAO"),
    CRIAR_NOVO_LOTE_BOLETO_ERRO_RECUSADO(2, "CRIAR_NOVO_LOTE_BOLETO_ERRO_RECUSADO"),
    CRIAR_NOVO_LOTE_BOLETO_ERRO_CRIAR_LOTE(3, "CRIAR_NOVO_LOTE_BOLETO_ERRO_CRIAR_LOTE"),
    CRIAR_NOVO_LOTE_BOLETO_ERRO_CRIAR_JSON(4, "CRIAR_NOVO_LOTE_BOLETO_ERRO_JSON"),
    CRIAR_NOVO_LOTE_BOLETO_SUCESSO(5, "CRIAR_NOVO_LOTE_BOLETO_SUCESSO"),
    CRIAR_NOVO_LOTE_PIX_ERRO_REQUISICAO(6, "CRIAR_NOVO_LOTE_PIX_ERRO_REQUISICAO"),
    CRIAR_NOVO_LOTE_PIX_ERRO_RECUSADO(7, "CRIAR_NOVO_LOTE_PIX_ERRO_RECUSADO"),
    CRIAR_NOVO_LOTE_PIX_ERRO_CRIAR_LOTE(8, "CRIAR_NOVO_LOTE_PIX_ERRO_CRIAR_LOTE"),
    CRIAR_NOVO_LOTE_PIX_ERRO_CRIAR_JSON(9, "CRIAR_NOVO_LOTE_PIX_ERRO_CRIAR_JSON"),
    CRIAR_NOVO_LOTE_PIX_SUCESSO(10, "CRIAR_NOVO_LOTE_PIX_SUCESSO"),
    CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_REQUISICAO(11, "CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_REQUISICAO"),
    CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_RECUSADO(12, "CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_RECUSADO"),
    CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_CRIAR_LOTE(13, "CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_CRIAR_LOTE"),
    CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_CRIAR_JSON(14, "CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_CRIAR_JSON"),
    CRIAR_NOVO_LOTE_BOLETO_CONSUMO_SUCESSO(15, "CRIAR_NOVO_LOTE_BOLETO_CONSUMO_SUCESSO"),
    CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_REQUISICAO(16, "CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_REQUISICAO"),
    CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_RECUSADO(17, "CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_RECUSADO"),
    CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_CRIAR_LOTE(18, "CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_CRIAR_LOTE"),
    CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_CRIAR_JSON(19, "CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_CRIAR_JSON"),
    CRIAR_NOVO_LOTE_TRANSFERENCIA_SUCESSO(20, "CRIAR_NOVO_LOTE_TRANSFERENCIA_SUCESSO"),
    CRIAR_SUBCONTA(21, "CRIAR_SUBCONTA"),
    INATIVAR_SUBCONTA(22, "INATIVAR_SUBCONTA"),
    REATIVAR_SUBCONTA(23, "REATIVAR_SUBCONTA"),
    CRIAR_FINANCIAL_ACCOUNT(24, "CRIAR_FINANCIAL_ACCOUNT"),
    APROVAR_LOTE_ERRO_REQUISICAO(25, "APROVAR_LOTE_ERRO_REQUISICAO"),
    APROVAR_LOTE_RECUSADO(26, "APROVAR_LOTE_RECUSADO"),
    APROVAR_LOTE_SUCESSO(27, "APROVAR_LOTE_SUCESSO"),
    ;

    private final Integer codigo;
    private final String descricao;

    MetodoZWKobanaEnum(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static MetodoZWKobanaEnum obterPorCodigo(Integer codigo) {
        for (MetodoZWKobanaEnum status : values()) {
            if (status.getCodigo().equals(codigo)) {
                return status;
            }
        }
        return MetodoZWKobanaEnum.NENHUM;
    }

    public static MetodoZWKobanaEnum obterPorValue(String value) {
        for (MetodoZWKobanaEnum obj : MetodoZWKobanaEnum.values()) {
            if (obj.name().equals(value.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public static MetodoZWKobanaEnum obterPorDescricao(String descricao) {
        for (MetodoZWKobanaEnum obj : MetodoZWKobanaEnum.values()) {
            if (obj.getDescricao().toUpperCase().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}

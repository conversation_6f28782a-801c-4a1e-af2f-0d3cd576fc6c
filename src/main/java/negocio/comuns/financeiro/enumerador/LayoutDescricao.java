

package negocio.comuns.financeiro.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum LayoutDescricao {
    NUMERO_PARCELAS(1, "Número de Parcelas"),
    MES_REFERENCIA(2, "Mês/Ano Referência");

    private int codigo;
    private String descricao;

    LayoutDescricao(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static LayoutDescricao getLayoutDescricao(int codigo) {
        for(LayoutDescricao fa : values()) {
            if(fa.getCodigo() == codigo)
                return fa;
        }
        return LayoutDescricao.MES_REFERENCIA;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

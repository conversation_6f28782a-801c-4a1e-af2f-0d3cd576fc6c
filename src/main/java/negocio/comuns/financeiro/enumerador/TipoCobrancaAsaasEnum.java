package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 05/05/2023
 */
public enum TipoCobrancaAsaasEnum {

    NENHUMA(0, "NENHUMA"),
    BOLETO(1, "B<PERSON>ET<PERSON>"),
    CREDIT_CARD(2, "CARTAOCREDITO"),
    PIX(3, "PIX"),
    ;

    private Integer codigo;
    private String descricao;

    TipoCobrancaAsaasEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoCobrancaAsaasEnum obterPorCodigo(Integer codigo) {
        for (TipoCobrancaAsaasEnum origem : TipoCobrancaAsaasEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }
}

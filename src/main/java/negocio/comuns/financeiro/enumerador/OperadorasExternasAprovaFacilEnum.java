package negocio.comuns.financeiro.enumerador;

import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public enum OperadorasExternasAprovaFacilEnum {

    VISA(1, "Visa", "visa", "Visa", new Integer[]{4}, 902, "VISA (CRÉDITO)"),
    MASTERCARD(2, "Mastercard", "mastercard", "Master", new Integer[]{5,2}, 602, "MASTERCARD (CRÉDITO)"),
    DINERS(3, "Diners", "diners", "Diners", new Integer[]{30, 36}, 603, "DINNERS CLUB (CREDITO)"),
    AMEX(4, "Amex", "amex", "Amex", new Integer[]{34, 37}, 905, "AMERICAN EXPRESS (CREDITO)"),
    HIPERCARD(5, "Hipercard", "hipercard", "Hipercard", new Integer[]{38,60}, 0, "HIPERCARD (CREDITO)"),
    JC<PERSON>(6, "JCB", "jcb", "JCB", new Integer[]{35}, 0, "JCB (CREDITO)"),
    SOROCRED(7, "Sorocred", "sorocred", "", new Integer[]{0}, 0, "SOROCRED (CREDITO)"),
    AURA(8, "Aura", "aura", "Aura", new Integer[]{50},0, "AURA (CREDITO)"),
    ELO(9, "Elo", "elo", "Elo", new Integer[]{0},604, "ELO (CREDITO)"),
    CABAL(10, "Cabal", "cabal", "cabal", new Integer[]{60}, 0, "CABAL (CREDITO)"),
    COM_VC(11, "Com.vc", "comvc", "com.vc", new Integer[]{0}, 0, "COM.CV (CREDITO)"),
    BANESCARD(12, "Banescard", "banescard", "banescard", new Integer[]{0}, 0, ""),
    DISCOVER(13, "Discover", "discover", "discover", new Integer[]{0}, 0, "DISCOVER (CREDITO)"),
    ;

    private Integer id;
    private String descricao;
    private String imagem;
    private String descricaoCielo;
    private Integer[] binStart;
    private Integer codProtheus;
    private String descricaoOperadoraCartaoPadrao;

    private OperadorasExternasAprovaFacilEnum(Integer codigo, String descricao, String imagem, String descricaoCielo, Integer[] binStart, Integer codProtheus,
                                              String descricaoOperadoraCartaoPadrao) {
        this.imagem = imagem;
        this.id = codigo;
        this.descricao = descricao;
        this.descricaoCielo = descricaoCielo;
        this.binStart = binStart;
        this.codProtheus = codProtheus;
        this.descricaoOperadoraCartaoPadrao = descricaoOperadoraCartaoPadrao;
    }

    public static List getSelectListTipo() {
        List temp = new ArrayList<OperadorasExternasAprovaFacilEnum>();
        for (int i = 0; i < OperadorasExternasAprovaFacilEnum.values().length; i++) {
            OperadorasExternasAprovaFacilEnum obj = OperadorasExternasAprovaFacilEnum.values()[i];
            temp.add(new SelectItem(obj, obj.getDescricao()));
        }
        Ordenacao.ordenarLista(temp, "label");
        temp.add(0, new SelectItem(null, "(Nenhum)"));
        return temp;
    }

    public static List getListOperadoras() {
        List temp = new ArrayList<OperadorasExternasAprovaFacilEnum>();
        for (int i = 0; i < OperadorasExternasAprovaFacilEnum.values().length; i++) {
            OperadorasExternasAprovaFacilEnum obj = OperadorasExternasAprovaFacilEnum.values()[i];
            temp.add(obj);
        }
        return temp;
    }

    public static OperadorasExternasAprovaFacilEnum valueOf(final int id) {
        OperadorasExternasAprovaFacilEnum[] lista = OperadorasExternasAprovaFacilEnum.values();
        for (OperadorasExternasAprovaFacilEnum op : lista) {
            if (op.getId() == id) {
                return op;
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return descricao;
    }
    public String getDescricaoMinusculo(){
        return descricao.toLowerCase();
    }

    /**
     * @param descricao the descricao to set
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    /**
     * @return the imagem
     */
    public String getImagem() {
        return imagem;
    }

    /**
     * @param imagem the imagem to set
     */
    public void setImagem(String imagem) {
        this.imagem = imagem;
    }

    public Integer[] getBinStart() {
        return binStart;
    }

    public void setBinStart(Integer[] binStart) {
        this.binStart = binStart;
    }

    public static List<OperadorasExternasAprovaFacilEnum> operadorasConvenio(TipoConvenioCobrancaEnum tipo) {

        //Mundipagg
        //https://docs.mundipagg.com/reference#criar-cartao
        if(tipo.equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(ELO);
            lista.add(MASTERCARD);
            lista.add(VISA);
            lista.add(AMEX);
            lista.add(JCB);
            lista.add(AURA);
            lista.add(HIPERCARD);
            lista.add(DINERS);
            lista.add(DISCOVER);
            return lista;
        }

        //Pagarme
        //https://docs.mundipagg.com/reference#criar-cartao
        if(tipo.equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(ELO);
            lista.add(MASTERCARD);
            lista.add(VISA);
            lista.add(AMEX);
            lista.add(JCB);
            lista.add(AURA);
            lista.add(HIPERCARD);
            lista.add(DINERS);
            lista.add(DISCOVER);
            return lista;
        }

        //Pagbank
        if(tipo.equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(ELO);
            lista.add(MASTERCARD);
            lista.add(VISA);
            lista.add(AMEX);
            lista.add(JCB);
            lista.add(AURA);
            lista.add(HIPERCARD);
            lista.add(DINERS);
            lista.add(DISCOVER);
            return lista;
        }


        //Pagarme v5
        if(tipo.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(ELO);
            lista.add(MASTERCARD);
            lista.add(VISA);
            lista.add(AMEX);
            lista.add(JCB);
            lista.add(AURA);
            lista.add(HIPERCARD);
            lista.add(DINERS);
            lista.add(DISCOVER);
            return lista;
        }


        //Getnet Online
        //"Mastercard""Visa""Amex""Elo""Hipercard"
        //https://developers.getnet.com.br/api#tag/Pagamento%2Fpaths%2F~1v1~1cards~1verification%2Fpost
        if(tipo.equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)){
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(MASTERCARD);
            lista.add(VISA);
            lista.add(AMEX);
            lista.add(ELO);
            lista.add(HIPERCARD);
            lista.add(DISCOVER);
            return lista;
        }


        //Cielo Online
        //https://developercielo.github.io/manual/cielo-ecommerce#cart%C3%A3o-de-cr%C3%A9dito
        //Bandeira do cartão (Visa / Master / Amex / Elo / Aura / JCB / Diners / Discover / Hipercard / Hiper).
        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(VISA);
            lista.add(MASTERCARD);
            lista.add(AMEX);
            lista.add(ELO);
            lista.add(AURA);
            lista.add(JCB);
            lista.add(DINERS);
            lista.add(DISCOVER);
            lista.add(HIPERCARD);
            lista.add(CABAL);
            return lista;
        }

        //Stripe
        //https://stripe.com/docs/api/cards/object?lang=java#card_object-brand
        //Card brand. Can be American Express, Diners Club, Discover, JCB, MasterCard, UnionPay, Visa, or Unknown.
        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(AMEX);
            lista.add(DINERS);
            lista.add(DISCOVER);
            lista.add(JCB);
            lista.add(MASTERCARD);
            lista.add(VISA);
            return lista;
        }

        //PagoLivre Online
        //Bandeira do cartão (Visa / Master / Amex / Elo / Aura / JCB / Diners / Discover / Hipercard / Hiper).
        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                tipo.equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(VISA);
            lista.add(MASTERCARD);
            lista.add(AMEX);
            lista.add(ELO);
            lista.add(AURA);
            lista.add(JCB);
            lista.add(DINERS);
            lista.add(DISCOVER);
            lista.add(HIPERCARD);
            lista.add(CABAL);
            return lista;
        }

        //Ceopag
        //Bandeira do cartão (VISA / MASTERCARD / AMEX / ELO / HIPERCARD / HIPER / DINERS).
        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_CEOPAG)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(VISA);
            lista.add(MASTERCARD);
            lista.add(AMEX);
            lista.add(ELO);
            lista.add(HIPERCARD);
            lista.add(DINERS);
            return lista;
        }

        //PinBank Online
        //Bandeira do cartão (Visa / Master / Amex / Elo / Hipercard).
        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(VISA);
            lista.add(MASTERCARD);
            lista.add(AMEX);
            lista.add(ELO);
            lista.add(HIPERCARD);
            return lista;
        }

        //One Payment Online
        //Não está especificado as bandeiras na doc de API, carregar todas...
        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT)) {
            List<OperadorasExternasAprovaFacilEnum> lista = getListOperadoras();
            return lista;
        }

        //DCC Caixa Online
        //A Caixa trabalha com a Fiserv, que pela documentção é uma empresa genérica que atuam com várias adquirente (Cielo, Rede, Getnet, etc).
        //Configurei, pelos dados que tinha no portal eSiTef do cliente.
        //Se algum outro cliente, tiver uma configuração diferente, precisa adicionar a nova bandeira nesse local.
        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(VISA);
            lista.add(MASTERCARD);
            lista.add(AMEX);
            lista.add(HIPERCARD);
            lista.add(DINERS);
            lista.add(ELO);
            return lista;
        }

        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_FITNESS_CARD)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(COM_VC);
            return lista;
        }

        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();
            lista.add(MASTERCARD);
            lista.add(VISA);
            lista.add(AMEX);
            lista.add(ELO);
            lista.add(HIPERCARD);
            lista.add(DISCOVER);
            return lista;
        }

        List<OperadorasExternasAprovaFacilEnum> lista = new ArrayList<>();

        lista.add(VISA);
        lista.add(MASTERCARD);

        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
            lista.add(CABAL);
            lista.add(ELO);
            lista.add(HIPERCARD);
            return lista;
        }

        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_VINDI) || tipo.equals(TipoConvenioCobrancaEnum.DCC_MAXIPAGO)) {
            lista.add(HIPERCARD);
            lista.add(AMEX);
            lista.add(AURA);
            lista.add(ELO);
        }

        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
            lista.add(SOROCRED);
        }

        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
            lista.add(HIPERCARD);
            lista.add(AMEX);
        }

        if (tipo.equals(TipoConvenioCobrancaEnum.DCC)) {
            lista.add(CABAL);
            lista.add(BANESCARD);
        }

        if (tipo.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
            lista.add(ELO);
            lista.add(AMEX);
            lista.add(HIPERCARD);
            lista.add(DISCOVER);
            return lista;
        }

        lista.add(ELO);
        lista.add(DINERS);
        return lista;
    }

    public Integer getCodProtheus() {
        return codProtheus;
    }

    public String getDescricaoCielo() {
        return descricaoCielo;
    }

    public static List getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum tipo) {
        List temp = new ArrayList<OperadorasExternasAprovaFacilEnum>();
        for (OperadorasExternasAprovaFacilEnum obj : operadorasConvenio(tipo)) {
            temp.add(new SelectItem(obj, obj.getDescricao().toUpperCase()));
        }
        Ordenacao.ordenarLista(temp, "label");
        temp.add(0, new SelectItem(null, "(Nenhum)"));
        return temp;
    }

    public String getDescricaoParaVindi() {
        if (this.equals(OperadorasExternasAprovaFacilEnum.AMEX)) {
            return "american_express";
        } else if (this.equals(OperadorasExternasAprovaFacilEnum.DISCOVER)) {
            return OperadorasExternasAprovaFacilEnum.ELO.name().toLowerCase();
        } else {
            return this.name().toLowerCase();
        }
    }

    public static OperadorasExternasAprovaFacilEnum obterPorDescricao(String descricao) {
        if (UteisValidacao.emptyString(descricao)) {
            return null;
        }
        for (OperadorasExternasAprovaFacilEnum op : OperadorasExternasAprovaFacilEnum.values()) {
            if (op.getDescricao().equalsIgnoreCase(descricao)) {
                return op;
            }
        }
        for (OperadorasExternasAprovaFacilEnum op : OperadorasExternasAprovaFacilEnum.values()) {
            if (op.getDescricao().toUpperCase().contains(descricao.toUpperCase()) ||
                    op.getDescricaoCielo().toUpperCase().contains(descricao.toUpperCase())) {
                return op;
            }
        }
        return null;
    }

    public String getDescricaoOperadoraCartaoPadrao() {
        return descricaoOperadoraCartaoPadrao;
    }

    public void setDescricaoOperadoraCartaoPadrao(String descricaoOperadoraCartaoPadrao) {
        this.descricaoOperadoraCartaoPadrao = descricaoOperadoraCartaoPadrao;
    }

}

package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 09/07/2024
 * <p>
 */
public enum StatusPagamentoKobanaEnum {
    NENHUM(0, "Nenhum", "#777777", "Não foi possível obter o status do lote"),
    PENDING(1, "Pendente", "#00008B", "Pagamento pendente"),
    SCHEDULED(2, "Agendado", "#777777", "Pagamento Agendado"),
    AWAITING_APPROVAL(3, "Aguardando Aprovação", "#3c3c82", "Pagamento pendente de aprovação"),
    REPROVED(4, "Reprovado", "#DF0000", "Este Pagamento foi reprovado por um usuário"),
    APPROVED(5, "Aprovado", "#378ea3", "Este Pagamento foi aprovado por um usuário"),
    CONFIRMED(6, "Confirma<PERSON>", "#4ab550", "Pagamento confirmado"),
    FAILED(7, "<PERSON>alha", "#DF0000", "Falha no Pagamento"),
    REJECTED(8, "Rejeitado", "#DF0000", "Pagamento rejeitado"),
    CANCELED(9, "Cancelado", "#6C757D", "Pagamento cancelado"),
    ;

    private final Integer codigo;
    private final String nomeApresentar;
    private final String color;
    private final String descricao;
    ;

    StatusPagamentoKobanaEnum(int codigo, String nomeApresentar, String color, String descricao) {
        this.codigo = codigo;
        this.nomeApresentar = nomeApresentar;
        this.color = color;
        this.descricao = descricao;
    }

    public static StatusPagamentoKobanaEnum obterPorCodigo(Integer codigo) {
        for (StatusPagamentoKobanaEnum status : values()) {
            if (status.getCodigo().equals(codigo)) {
                return status;
            }
        }
        return StatusPagamentoKobanaEnum.NENHUM;
    }

    public static StatusPagamentoKobanaEnum obterPorValue(String value) {
        for (StatusPagamentoKobanaEnum obj : StatusPagamentoKobanaEnum.values()) {
            if (obj.name().equals(value.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public static StatusPagamentoKobanaEnum obterPorDescricao(String descricao) {
        for (StatusPagamentoKobanaEnum obj : StatusPagamentoKobanaEnum.values()) {
            if (obj.getNomeApresentar().toUpperCase().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getNomeApresentar() {
        return nomeApresentar;
    }

    public String getColor() {
        return color;
    }

    public String getDescricao() {
        return descricao;
    }
}

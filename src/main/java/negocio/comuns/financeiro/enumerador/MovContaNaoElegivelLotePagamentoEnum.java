package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 04/07/2024
 */

public enum MovContaNaoElegivelLotePagamentoEnum {

    NENHUMA(0, "Não foi possível obter o motivo de não elegibilidade."),
    FORMA_PGTO_NAO_INFORMADA(1, "Forma de pagamento não informada."),
    FORMA_PGTO_PIX_SEM_PAYLOAD(2, "Payload não informado para a forma de pagamento PIX."),
    FORMA_PGTO_BOLETO_SEM_CODIGO_BARRAS(3, "Código de barras não informado para a forma de pagamento Boleto."),
    FORMA_PGTO_BOLETO_SEM_CPF_OU_CNPJ_BENEFICIARIO(4, "CPF/CNPJ do beneficiário do boleto não foi informado."),
    FORMA_PGTO_BOLETO_COD_BARRAS_INVALIDO(5, "Código de barras informado está incorreto para esse Boleto. Deve conter 47 números."),
    FORMA_PGTO_BOLETO_CONSUMO_COD_BARRAS_INVALIDO(6, "Código de barras informado está incorreto para esse Boleto de consumo. Deve conter 48 números."),
    FORMA_PGTO_TRANSFERENCIA_SEM_CONTA_BANCARIA(7, "Dados da conta bancária do fornecedor não foi informado."),
    SEM_RATEIO(8, "Nenhum rateio encontrado para esta conta."),
    MAIS_DE_UM_RATEIO(9, "Não é permitido adicionar lançamentos com mais de um rateio."),
    BANCO_NAO_PERMITE_API_PAGAMENTO(10, "O seu banco não permite a realização de LOTE DE PAGAMENTOS via API."),
    BANCO_NAO_PERMITE_API_TRANSFERENCIA(11, "O seu banco não permite a realização de LOTE DE TRANSFERÊNCIAS via API."),
    BANCO_NAO_PERMITE_API_PAGAMENTO_FORMA_PGTO_PIX(12, "O seu banco NÃO permite a realização de LOTE DE PAGAMENTOS de Pix."),
    FORMA_PGTO_ATUAL_NAO_ACEITA_LOTE(13, "A Forma de pagamento Atual desse lançamento não está disponível para criação de lotes. Informe uma das formas de pagamento aceitas."),
    ;

    private Integer codigo;
    private String descricao;

    MovContaNaoElegivelLotePagamentoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static MovContaNaoElegivelLotePagamentoEnum obterPorCodigo(Integer codigo) {
        for (MovContaNaoElegivelLotePagamentoEnum origem : MovContaNaoElegivelLotePagamentoEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }

    public static MovContaNaoElegivelLotePagamentoEnum obterPorDescricao(String descricao) {
        for (MovContaNaoElegivelLotePagamentoEnum obj : MovContaNaoElegivelLotePagamentoEnum.values()) {
            if (obj.getDescricao().toUpperCase().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUMA;
    }
}

package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 14/12/2024
 * <p>
 */
public enum BancoPermiteAPIPagamentoKobana {
    //por enquanto só tem nesse enum os bancos que disponibilizam API de pagamento na Koabana
    NENHUM("", "Nenhum"),
    BANCO_DO_BRASIL("bb", "Banco Do Brasil"),
    SANTANDER("santander", "Santander"),
    SICOOB("sicoob", "Sicoob"),
    BTG_PACTUAL("btg", "BTG Pactual"),
    BANCO_EXEMPLO("example_bank", "Banco Exemplo"),
    QI_TECH("qitech", "Qi Tech"),
    BACK_OFFICE("back_office", "Back Office"),
    ;

    private final String slug;
    private final String nome;

    BancoPermiteAPIPagamentoKobana(String slug, String nome) {
        this.slug = slug;
        this.nome = nome;
    }

    public static BancoPermiteAPIPagamentoKobana obterPorValue(String value) {
        for (BancoPermiteAPIPagamentoKobana obj : BancoPermiteAPIPagamentoKobana.values()) {
            if (obj.name().equals(value.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public static BancoPermiteAPIPagamentoKobana obterPorNome(String nome) {
        for (BancoPermiteAPIPagamentoKobana obj : BancoPermiteAPIPagamentoKobana.values()) {
            if (obj.getNome().toUpperCase().equals(nome.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public static BancoPermiteAPIPagamentoKobana obterPorSlug(String slug) {
        for (BancoPermiteAPIPagamentoKobana obj : BancoPermiteAPIPagamentoKobana.values()) {
            if (obj.getSlug().toUpperCase().equals(slug.toUpperCase())) {
                return obj;
            }
        }
        return NENHUM;
    }

    public String getSlug() {
        return slug;
    }

    public String getNome() {
        return nome;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Calendario;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum NomenclaturaArquivoEnum {

    NENHUM(0, "NENHUM", ""),
    NEXXERA(1, "NEXXERA", "REM + CODIGO_BANCO + DT_GERACAO_REMESSA + XXXX (Sequencial) + \"-\" + NUMEROESTABELECIMENTO(NumeroContrato) + \"-\" + CHAVE(key)"),
    TIVIT(2, "TIVIT", "");


    private Integer codigo;
    private String descricao;
    private String exemplo;

    private NomenclaturaArquivoEnum(Integer codigo, String descricao, String exemplo) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.exemplo = exemplo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getExemplo() {
        return exemplo;
    }

    public void setExemplo(String exemplo) {
        this.exemplo = exemplo;
    }

    public static List<SelectItem> getListaSelectItem(){
        List<SelectItem> lista = new ArrayList<SelectItem>();
        for(NomenclaturaArquivoEnum tipo : values()){
            lista.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
        }
        return lista;
    }

    public static NomenclaturaArquivoEnum obterPorCodigo(final Integer codigo) {
        if (codigo == null) {
            return NomenclaturaArquivoEnum.NENHUM;
        }

        for (NomenclaturaArquivoEnum sit : NomenclaturaArquivoEnum.values()) {
            if (sit.getCodigo().equals(codigo)) {
                return sit;
            }
        }
        return NomenclaturaArquivoEnum.NENHUM;
    }

    public static String obterNomeArquivo(RemessaVO remessaVO, String chave, NomenclaturaArquivoEnum nomenclaturaArquivoEnum) {
        if (nomenclaturaArquivoEnum == null || nomenclaturaArquivoEnum.equals(NomenclaturaArquivoEnum.NENHUM)) {
            return "";
        } else if (nomenclaturaArquivoEnum.equals(NomenclaturaArquivoEnum.NEXXERA)) {
            return obterNomeArquivoNexxera(remessaVO, chave);
        } else {
            return "";
        }
    }

    private static String obterNomeArquivoNexxera(RemessaVO remessaVO, String chave) {

            /* by Luiz Felipe 04/09/2019
            Modelo no nome do arquivo definido junto com a nexxera.

            REM104DDMMYYYYXXXX-NUMEROESTABELECIMENTO-CODIGOPACTO.TXT

            Sendo:

            REM - Fixo
            104 - Código do banco
            DDMMYYYY - Data Arquivo
            XXXX - Sequencial Remessa - Gerado pela Pacto
            NUMEROESTABELECIMENTO  - (Num. Contrato/Num. Estabelecimento)  no caso 328847
            CODIGOPACTO  -

            Exemplo Pratique:
            REM104190820190001-328847-b9055aace082097a99ba272a5613ab5f.TXT

            */

            StringBuilder nomeArquivo = new StringBuilder();
            nomeArquivo.append("REM");
            nomeArquivo.append(remessaVO.getConvenioCobranca().getBanco().getCodigoBanco());
            nomeArquivo.append(Calendario.getData(remessaVO.getDataRegistro(), "ddMMyyyy"));
            nomeArquivo.append(StringUtilities.formatarCampoForcandoZerosAEsquerda(remessaVO.getSequencialArquivo(), 4));
            nomeArquivo.append("-");
            nomeArquivo.append(remessaVO.getConvenioCobranca().getNumeroContrato());
            nomeArquivo.append("-");
            nomeArquivo.append(chave);
            return nomeArquivo.toString();
    }
}

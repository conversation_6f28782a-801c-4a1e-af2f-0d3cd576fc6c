package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 11/10/2023
 */
public enum TransactionPluggyCategoryEnum {
    NENHUMA(""),
    _01000000("Renda"),
    _01010000("Sal<PERSON><PERSON>"),
    _01020000("Aposentadoria"),
    _01030000("Atividades de empreendedorismo"),
    _01040000("Auxílio do governo"),
    _01050000("Renda não-recorrente"),
    _02000000("Empréstimos e financiamento"),
    _02010000("Atraso no pagamento e custos de cheque especial"),
    _02020000("Juros cobrados"),
    _02030000("Financiamento"),
    _02030001("Financiamento imobiliário"),
    _02030002("Financiamento de veículos"),
    _02030003("Empréstimo estudantil"),
    _02040000("Empréstimos"),
    _03000000("Investimentos"),
    _03010000("Investimento automático"),
    _03020000("Renda fixa"),
    _03030000("Fundos multimercado"),
    _03040000("Renda variável"),
    _03050000("Ajuste de margem"),
    _03060000("Juros de rendimentos de dividendos"),
    _03070000("Pensão"),
    _04000000("Transferência mesma titularidade"),
    _04010000("Transferência mesma titularidade - Dinheiro"),
    _04020000("Transferência mesma titularidade - PIX"),
    _04030000("Transferência mesma titularidade - TED"),
    _05000000("Transferências"),
    _05010000("Transferência - Boleto bancário"),
    _05020000("Transferência - Dinheiro"),
    _05030000("Transferência - Cheque"),
    _05040000("Transferência - DOC"),
    _05050000("Transferência - Câmbio"),
    _05060000("Transferência - Mesma instituição"),
    _05070000("Transferência - PIX"),
    _05080000("Transferência - TED"),
    _05090000("Transferências para terceiros"),
    _05090001("Transferência para terceiros - Boleto bancário"),
    _05090002("Transferência para terceiros - Débito"),
    _05090003("Transferência para terceiros - DOC"),
    _05090004("Transferência para terceiros - PIX"),
    _05090005("Transferência para terceiros - TED"),
    _05100000("Pagamento de cartão de crédito"),
    _06000000("Obrigações legais"),
    _06010000("Saldo bloqueado"),
    _06020000("Pensão alimentícia"),
    _07000000("Serviços"),
    _07010000("Telecomunicação"),
    _07010001("Internet"),
    _07010002("Celular"),
    _07010003("TV"),
    _07020000("Educação"),
    _07020001("Cursos online"),
    _07020002("Universidade"),
    _07020003("Escola"),
    _07020004("Creche"),
    _07030000("Saúde e bem-estar"),
    _07030001("Academia e centros de lazer"),
    _07030002("Prática de esportes"),
    _07030003("Bem-estar"),
    _07040000("Bilhetes"),
    _07040001("Estádios e arenas"),
    _07040002("Museus e pontos turísticos"),
    _07040003("Cinema, Teatro e Concertos"),
    _08000000("Compras"),
    _08010000("Compras online"),
    _08020000("Eletrônicos"),
    _08030000("Pet Shops e veterinários"),
    _08040000("Vestiário"),
    _08050000("Artigos infantis"),
    _08060000("Livraria"),
    _08070000("Artigos esportivos"),
    _08080000("Papelaria"),
    _08090000("Cashback"),
    _09000000("Serviços digitais"),
    _09010000("Jogos e videogames"),
    _09020000("Streaming de vídeo"),
    _09030000("Streaming de música"),
    _10000000("Supermercado"),
    _11000000("Alimentos e bebidas"),
    _11010000("Restaurantes, bares e lanchonetes"),
    _11020000("Delivery de alimentos"),
    _12000000("Viagens"),
    _12010000("Aeroportos e cias. aéreas"),
    _12020000("Hospedagem"),
    _12030000("Programas de milhagem"),
    _12040000("Passagem de ônibus"),
    _13000000("Doações"),
    _14000000("Apostas"),
    _14010000("Loteria"),
    _14020000("Apostas online"),
    _15000000("Impostos"),
    _15010000("Imposto de renda"),
    _15020000("Imposto sobre investimentos"),
    _15030000("Impostos sobre operações financeiras"),
    _16000000("Taxas bancárias"),
    _16010000("Taxas de conta corrente"),
    _16020000("Taxas sobre transferências e caixa eletrônico"),
    _16030000("Taxas de cartão de crédito"),
    _17000000("Moradia"),
    _17010000("Aluguel"),
    _17020000("Serviços de utilidade pública"),
    _17020001("Água"),
    _17020002("Eletricidade"),
    _17020003("Gás"),
    _17030000("Utensílios para casa"),
    _17040000("Impostos sobre moradia"),
    _18000000("Saúde"),
    _18010000("Dentista"),
    _18020000("Farmácia"),
    _18030000("Ótica"),
    _18040000("Hospitais, clínicas e laboratórios"),
    _19000000("Transporte"),
    _19010000("Táxi e transporte privado urbano"),
    _19020000("Transporte público"),
    _19030000("Aluguel de veículos"),
    _19040000("Aluguel de bicicletas"),
    _19050000("Serviços automotivos"),
    _19050001("Postos de gasolina"),
    _19050002("Estacionamentos"),
    _19050003("Pedágios e pagamentos no veículo"),
    _19050004("Taxas e impostos sobre veículos"),
    _19050005("Manutenção de veículos"),
    _19050006("Multas de trânsito"),
    _20000000("Seguros"),
    _200100000("Seguro de vida"),
    _200200000("Seguro residencial"),
    _200300000("Seguro saúde"),
    _200400000("Seguro de veículos"),
    _21000000("Lazer");

    private final String descriptionTranslated;

    TransactionPluggyCategoryEnum(String descriptionTranslated) {
        this.descriptionTranslated = descriptionTranslated;
    }

    public String getDescriptionTranslated() {
        return descriptionTranslated;
    }

    public static TransactionPluggyCategoryEnum getTransactionPluggyCategoryEnum(String codigo) {
        for (TransactionPluggyCategoryEnum categoria : TransactionPluggyCategoryEnum.values()) {
            if (categoria.name().replace("_", "").equals(codigo)) {
                return categoria;
            }
        }
        return TransactionPluggyCategoryEnum.NENHUMA;
    }
}


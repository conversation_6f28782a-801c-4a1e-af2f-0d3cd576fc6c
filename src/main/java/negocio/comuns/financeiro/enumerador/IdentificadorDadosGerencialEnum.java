    /*
     * To change this template, choose Tools | Templates
     * and open the template in the editor.
     */
    package negocio.comuns.financeiro.enumerador;

    /**
     *
     * <AUTHOR>
     */
    public enum IdentificadorDadosGerencialEnum {
        TOTAL_INICIO(1,"TI", "Ativos + vencidos no Início do Mês",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        SALDO_MES(2,"SM", "Saldo do Mes",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        VENCIDOS_FINAL(3,"VF", "Total de Alunos Vencidos no final do mês",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        DESISTENTES(4,"DE", "Total de Alunos Desistentes",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        CANCELADOS(5,"CA", "Total de Cancelados",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        TOTAL_FINAL(6,"TF", "Ativos + vencidos final do Mês",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        MATRICULADOS(7,"MCM", "Total de Alunos Matriculados",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        REMATRICULADOS(8,"MCR", "Total de Alunos Rematriculados",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        TRANCADOS(9,"MCT", "Total de Alunos Trancados",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        RE_TRANCADOS(10,"RTR", "Total de Alunos Matriculados",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        PERC_MATRICULAS(11,"PMA", "% Mat X Ativ + Venc Inicio do Mes",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        PERC_REMATRICULAS(12,"PRE", "% Rem X Ativ + Venc Inicio do Mes",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        PERC_MATRI_REM(13,"PMR", "% Mat + Rem X Ativ + Venc Inicio do Mes",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),

        RENOVACAO_PREVISAO(20,"RP", "Total de Alunos a Renovar no mês",IndicadoresDadosGerencialEnum.INDICE_RENOVACAO.getSigla()),
        RENOVACAO_CORRESP(21,"RC", "% A Renovar corresp. Ao total de ativos + vencidos",IndicadoresDadosGerencialEnum.INDICE_RENOVACAO.getSigla()),
        RENOVACAO_REALIZADA(22,"RR", "Total de Renovações",IndicadoresDadosGerencialEnum.INDICE_RENOVACAO.getSigla()),
        RENOVACAO_NAOREALIZADA(23,"RN", "Não renovados",IndicadoresDadosGerencialEnum.INDICE_RENOVACAO.getSigla()),
        RENOVACAO_INDICE(24,"RI", "Índice de Renovação do mês",IndicadoresDadosGerencialEnum.INDICE_RENOVACAO.getSigla()),

        
        PLANOS_VENDIDOS(30,"QPV", "Total de Planos Vendidos",IndicadoresDadosGerencialEnum.QUANTIDADE_PLANOS.getSigla()),
        PLANOS_MENSAIS(31, "QPM", "% Venda de Planos Mensais",IndicadoresDadosGerencialEnum.QUANTIDADE_PLANOS.getSigla()),
        PLANOS_LONGADURACAO(32, "QPL", "% Venda de Planos de Longa Duração (8, 9, 12, 15,18 ou 24)",IndicadoresDadosGerencialEnum.QUANTIDADE_PLANOS.getSigla()),

        CLIENTES_PARCEMABERTO(40, "CPE", "Clientes com Parcelas a pagar", IndicadoresDadosGerencialEnum.PENDENCIAS.getSigla()),
        CLIENTES_PARCATRASO(41, "CPA", "Clientes com Parcelas em atraso", IndicadoresDadosGerencialEnum.PENDENCIAS.getSigla()),
        CLIENTES_DEBITOCC(42, "CDC", "Clientes com Débito conta corrente", IndicadoresDadosGerencialEnum.PENDENCIAS.getSigla()),
        VALOR_PARCEMABERTO(43, "VPE", "Valor de Parcelas a pagar", IndicadoresDadosGerencialEnum.PENDENCIAS.getSigla()),
        VALOR_PARCATRASO(44, "VPA", "Valor de Parcelas em atraso", IndicadoresDadosGerencialEnum.PENDENCIAS.getSigla()),

        FATURAMENTO_META(50,"FAM","META MINIMA DE FATURAMETO DO MÊS",IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        FATURAMENTO_ATINGIDO(51,"FAA","META DE FATUR. ALCANÇADO NO PERIODO",IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        FATURAMENTO_RECEBIDO(52,"FAR","FECHAMENTO DE CAIXA POR OPERADOR",IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        RECEITA_PERIODO(53,"REP","Receita por Periodo",IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        RECEITA_ALUNO(54,"REA","Receita por Aluno - (Tiket Médio)",IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        DESPESAS_TOTAIS(55,"DET","Despesas Totais",IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        DESPESAS_ALUNO(56,"DEA","Despesas por Aluno",IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        
        BV_MATRICULAS(60,"BM", "Total de BV´s para Matrículas",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        MATRICULAS(61,"MA", "Matriculados",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        ICV_MATRICULAS(62,"IM", "%ICV - Matrículas",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        BV_REMATRICULAS(63,"BR", "Total de BV´s para Rematrículas",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        REMATRICULAS(64,"RE", "Rematriculados",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        ICV_REMATRICULAS(65,IndicadoresDadosGerencialEnum.INDICE_RENOVACAO.getSigla(), "%ICV - Rematrículas",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        BV_TOTAL(66,"BT", "Total de BV´s",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        ICV_TOTAL(67,"IT", "%ICV - TOTAL",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        BV_IDEAL(68,"BI", "Quantidade Ideal de BV´s",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        BV_RETORNOS(69,"BRT", "Total de BV´s de Retorno",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        RETORNO_MATRICULAS(70,"MRT", "Matriculados Retorno",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        RETORNO_REMATRICULAS(71,"RRT", "Rematriculados Retorno",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        ICV_RETORNOS(72,"IRT", "%ICV - Retornos",IndicadoresDadosGerencialEnum.INDICE_CONVERSAO.getSigla()),
        RISCO_ALTO(73,"RIS", "Risco alto",IndicadoresDadosGerencialEnum.INDICE_RENOVACAO.getSigla()),
        
        DRE_RECEITA(74,"DRR","DRE por receita","DRE"),
        DRE_COMPETENCIA(75,"DRC","DRE por competência","DRE"),
        DRE_FATURAMENTO_DE_CAIXA(76,"DFC","DRE por faturamento de caixa", "DRE"),
        DRE_FATURAMENTO(77,"DRF","DRE por faturamento","DRE"),
        DRE_COMPETENCIA_QUITADA(78,"DCQ","DRE por competencia quitada","DRE"),
        DRE_COMPETENCIA_NAO_QUITADA(79,"DNQ","DRE por competencia não quitada","DRE"),
        
        META_CRM_CONVERSAO_INDICADOS(80,"CVI","Meta - Conversão de indicados",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        META_CRM_CONVERSAO_AGENDADOS(81,"CVA","Meta - Conversão de agendados",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        META_CRM_CONVERSAO_EX_ALUNOS(82,"CVE","Meta - Conversão de ex-alunos",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        META_CRM_CONVERSAO_VISITANTES_ANTIGOS(83,"CVV","Meta - Conversão de visitantes antigos",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        META_CRM_CONVERSAO_DESISTENTES(84,"CVD","Meta - Conversão de desistentes",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        META_CRM_CONVERSAO_RECEPTIVO(85,"CVR","Meta - Conversão de receptivo",IndicadoresDadosGerencialEnum.CRM.getSigla()),

        RESULTADO_CRM_CONVERSAO_INDICADOS(86,"CRI","Resultados - Conversão de indicados",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        RESULTADO_CRM_CONVERSAO_AGENDADOS(87,"CRA","Resultados - Conversão de agendados",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        RESULTADO_CRM_CONVERSAO_EX_ALUNOS(88,"CRE","Resultados - Conversão de ex-alunos",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        RESULTADO_CRM_CONVERSAO_VISITANTES_ANTIGOS(89,"CRV","Resultados - Conversão de visitantes antigos",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        RESULTADO_CRM_CONVERSAO_DESISTENTES(90,"CRD","Resultados - Conversão de desistentes",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        RESULTADO_CRM_CONVERSAO_RECEPTIVO(91,"CRR","Resultados - Conversão de receptivo",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        CONTATOS_CRM(92,"CCR","Quantidade - Contatos com cliente",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        NOVOS_CONTRATOS(93,"NCR","Novos Contratos",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        ATIVOS_FIM_MES(94,"AFM","Rotividade - Ativos fim do mês",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        META_CONTATOS_CRM(95,"MCC","Quantidade - Meta Contatos com cliente",IndicadoresDadosGerencialEnum.CRM.getSigla()),
        ATIVOS_INICIO_MES(96,"ATM","Rotividade - Ativos início do mês",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        CONTRATO_TRANSFERIDO(97,"CT", "Total de Alunos Que Receberem um Contrato Transferido","ACT"),

        NUMERO_CHECKINS(98, "NCK", "Número de check-ins", IndicadoresDadosGerencialEnum.GOR.getSigla()),
        PLANOS_MENSALIDADES(99, "PLM", "Planos e mensalidades existentes das unidades", IndicadoresDadosGerencialEnum.GOR.getSigla()),
        AGENDAMENTO_AULAS_EXPERIMENTAIS(100, "AAE", "Agendamento das Aulas Experimentais Diárias", IndicadoresDadosGerencialEnum.GOR.getSigla()),
        RECEITA_ACUMULADA(101, "RAC", "Receita Acumulada do Dia", IndicadoresDadosGerencialEnum.GOR.getSigla()),
        VENDA_DE_PRODUTOS(102, "VDP", "Venda de Produtos Cadastrados", IndicadoresDadosGerencialEnum.GOR.getSigla()),
        CONVERSAO_AULAS_EXPERIMENTAIS(103, "CAE", "Conversão das Aulas Experimentais em Planos", IndicadoresDadosGerencialEnum.GOR.getSigla()),
        PLANOS_A_VENCER(104, "PAV", "Planos a vencer em 15 e 30 dias", IndicadoresDadosGerencialEnum.GOR.getSigla()),
        FATURAMENTO_MES_SELECIONADO(105, "FMS", "Faturamento do Mês", IndicadoresDadosGerencialEnum.GOR.getSigla()),
        LINHA_PERFORMANCE(106, "LPE", "Linha Mínima de Performance Aceitável", IndicadoresDadosGerencialEnum.GOR.getSigla()),
        ESTOQUE_DE_PRODUTOS(107, "EDP", "Estoque de Produtos em Pontos e Valores Real", IndicadoresDadosGerencialEnum.GOR.getSigla()),
        NUMERO_CANCELAMENTO(108, "NCA", "Número de Cancelamento de Planos Mensais", IndicadoresDadosGerencialEnum.GOR.getSigla()),
        NUMERO_DESAFIADOS(109, "NDE", "Número de Desafiados do Mês", IndicadoresDadosGerencialEnum.GOR.getSigla()),


        LTV_CAC(110, "CAC", "CAC - Custo de Aquisição do Cliente", IndicadoresDadosGerencialEnum.LTV.getSigla()),
        LTV_LTV(111, "LTV", "LTV - Lifetime value", IndicadoresDadosGerencialEnum.LTV.getSigla()),
        LTV_CHURN(112, "CHU", "Churn rate", IndicadoresDadosGerencialEnum.LTV.getSigla()),
        LTV_TEMPO_MEDIO(113, "TMV", "Tempo médio de vida dos clientes", IndicadoresDadosGerencialEnum.LTV.getSigla()),

        DEPENDENTE_INICIO_MES(114,"DIM","Rotividade - Dependente início do mês",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        DEPENDENTE_FIM_MES(115,"DFM","Rotividade - Dependente fim do mês",IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),

        CLIENTES_ATIVOS(116, "CLA", "Clientes ativos", IndicadoresDadosGerencialEnum.ADM.getSigla()),

        RECEBIVEIS_FATURAMENTO_ESPECIE(117, "RFE", "Gestão Recebíveis - Faturamento Espécie", IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        RECEBIVEIS_FATURAMENTO_BOLETO(118, "RFB", "Gestão Recebíveis - Faturamento Boleto", IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        RECEBIVEIS_FATURAMENTO_DEVOLUCAO(119, "RFD", "Gestão Recebíveis - Faturamento Devolução", IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        RECEBIVEIS_FATURAMENTO_CONTA_CORRENTE(120, "RFC", "Gestão Recebíveis - Faturamento Conta Corrente", IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        RECEBIVEIS_FATURAMENTO_TOTAL(121, "FTO", "Gestão Recebíveis - Faturamento Total", IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),

        RECEBIVEIS_RECEITA_ESPECIE(122, "RRE", "Gestão Recebíveis - Receita Espécie", IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        RECEBIVEIS_RECEITA_BOLETO(123, "RRB", "Gestão Recebíveis - Receita Boleto", IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        RECEBIVEIS_RECEITA_DEVOLUCAO(124, "RRD", "Gestão Recebíveis - Receita Devolução", IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        RECEBIVEIS_RECEITA_CONTA_CORRENTE(125, "RRC", "Gestão Recebíveis - Receita Conta Corrente", IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),
        RECEBIVEIS_RECEITA_TOTAL(126, "RTO", "Gestão Recebíveis - Receita Total", IndicadoresDadosGerencialEnum.FINANCEIROS.getSigla()),

        ATIVO_DEPENDENTE_INICIO_MES(127, "ADI", "Movimentação de Contratos - Ativo + Dependente início do mês", IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        ATIVO_DEPENDENTE_FIM_MES(128, "ADF", "Movimentação de Contratos - Ativo + Dependente fim do mês", IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),

        CLIENTES_DEPENDENTE_ATIVO_INICIO_MES(129, "CDI", "Clientes - Ativo + Dependente início do mês", IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        CLIENTES_DEPENDENTE_ATIVO_FIM_MES(130, "CDF", "Clientes - Ativo + Dependente fim do mês", IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),

        BOLSISTAS(131, "BOL", "Bolsistas", IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        CANCELADOS_HOJE(132, "CAH", "Cancelados Hoje", IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        DESISTENTES_HOJE(133, "DEH", "Desistentes Hoje", IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO.getSigla()),
        TICKET_MEDIO_RECEITA(134, "TMR", "Receita Ticket Médio", IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla()),
        TICKET_MEDIO_COMPETENCIA(135, "TMC", "Competência Ticket Médio", IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla()),
        TICKET_MEDIO_ATIVOS(136, "TMA", "Ativos Ticket Médio", IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla()),
        TICKET_MEDIO_BOLTAS(137, "TMB", "Bolsas Ticket Médio", IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla()),
        TICKET_MEDIO_PAGANTES(138, "TMP", "Pagantes Ticket Médio", IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla()),
        TICKET_MEDIO_DESPESAS(139, "TMD", "Despesas Ticket Médio", IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla()),
        TICKET_MEDIO_POR_RECEITA(140, "TPR", "Ticket Médio por Receita", IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla()),
        TICKET_MEDIO_POR_COMPETENCIA(141, "TPC", "Ticket Médio por Competencia", IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla()),
        TICKET_MEDIO_DESPESAS_CONTRATO(142, "TDC", "Ticket Médio de despesas por contrato", IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla()),
        TICKET_MEDIO_MEDIA_ATIVOS(143, "TMM", "Média de Ativos Ticket Médio", IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla()),
        TICKET_MEDIO_DEPENDENTES(144, "TDP", "Dependentes Ticket Médio", IndicadoresDadosGerencialEnum.TICKET_MEDIO.getSigla()),
        ;



        private Integer id;
        private String sigla;
        private String descricao;
        private IndicadoresDadosGerencialEnum indicador;

         IdentificadorDadosGerencialEnum(final Integer id, final String sigla, final String descricao, final String siglaIndicador) {
            this.id = id;
            this.sigla= sigla;
            this.descricao = descricao;
            this.indicador = IndicadoresDadosGerencialEnum.getIndicadorPorSigla(siglaIndicador);
        }


        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }

        public String getDescricao() {
            return descricao;
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getSigla() {
            return sigla;
        }

        public void setSigla(String sigla) {
            this.sigla = sigla;
        }

        public static IdentificadorDadosGerencialEnum getFase(Integer codigo) {
            IdentificadorDadosGerencialEnum  indicador = null;
            for (IdentificadorDadosGerencialEnum  indicadorPMG : IdentificadorDadosGerencialEnum .values()) {
                if (indicadorPMG.getId().equals(codigo)) {
                    indicador = indicadorPMG;
                }
            }
            return indicador;
        }

        public static IdentificadorDadosGerencialEnum getFasePorSigla(String sigla) {
            IdentificadorDadosGerencialEnum indicador = null;
            for (IdentificadorDadosGerencialEnum indicadorPMG : IdentificadorDadosGerencialEnum.values()) {
                if (indicadorPMG.getSigla().equals(sigla)) {
                    indicador = indicadorPMG;
                }
            }
            return indicador;
        }

        public static String getIdentificador(String sigla) {
            IdentificadorDadosGerencialEnum ident = null;
            for (IdentificadorDadosGerencialEnum identEnum : IdentificadorDadosGerencialEnum.values()) {
                if (identEnum.getSigla().equals(sigla)) {
                    ident = identEnum;
                }
            }
            if (ident == null) {
                return "";
            }
            return ident.getDescricao();
        }

        public IndicadoresDadosGerencialEnum getIndicador() {
            return indicador;
        }

        public void setIndicador(IndicadoresDadosGerencialEnum indicador) {
            this.indicador = indicador;
        }

    }

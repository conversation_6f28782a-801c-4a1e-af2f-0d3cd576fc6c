package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 11/05/2023
 */
public enum CompanyTypeAsaasEnum {

    NENHUMA(0, "Nenhuma"),
    MEI(1, "Micro Empreendedor Individual"),
    LIMITED(2, "Empresa Limitada"),
    INDIVIDUAL(3, "Empresa Individual"),
    ASSOCIATION(4, "Associação"),
    ;

    private Integer codigo;
    private String descricao;

    CompanyTypeAsaasEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static CompanyTypeAsaasEnum obterPorCodigo(Integer codigo) {
        for (CompanyTypeAsaasEnum origem : CompanyTypeAsaasEnum.values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return NENHUMA;
    }

    public static CompanyTypeAsaasEnum obterPorDescricao(String descricao) {
        for (CompanyTypeAsaasEnum obj : CompanyTypeAsaasEnum.values()) {
            if (obj.name().equals(descricao.toUpperCase())) {
                return obj;
            }
        }
        return NENHUMA;
    }
}
